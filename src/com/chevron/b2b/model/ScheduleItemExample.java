package com.chevron.b2b.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 计划事项单表查询条件
 * <AUTHOR>
 * @version 1.0 2018-12-18 16:56
 */
public class ScheduleItemExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ScheduleItemExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdIsNull() {
            addCriterion("input_ctrl_id is null");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdIsNotNull() {
            addCriterion("input_ctrl_id is not null");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdEqualTo(Long value) {
            addCriterion("input_ctrl_id =", value, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdNotEqualTo(Long value) {
            addCriterion("input_ctrl_id <>", value, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdGreaterThan(Long value) {
            addCriterion("input_ctrl_id >", value, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdGreaterThanOrEqualTo(Long value) {
            addCriterion("input_ctrl_id >=", value, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdLessThan(Long value) {
            addCriterion("input_ctrl_id <", value, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdLessThanOrEqualTo(Long value) {
            addCriterion("input_ctrl_id <=", value, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdIn(List<Long> values) {
            addCriterion("input_ctrl_id in", values, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdNotIn(List<Long> values) {
            addCriterion("input_ctrl_id not in", values, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdBetween(Long value1, Long value2) {
            addCriterion("input_ctrl_id between", value1, value2, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andInputCtrlIdNotBetween(Long value1, Long value2) {
            addCriterion("input_ctrl_id not between", value1, value2, "inputCtrlId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNull() {
            addCriterion("schedule_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNotNull() {
            addCriterion("schedule_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdEqualTo(Long value) {
            addCriterion("schedule_id =", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotEqualTo(Long value) {
            addCriterion("schedule_id <>", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThan(Long value) {
            addCriterion("schedule_id >", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_id >=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThan(Long value) {
            addCriterion("schedule_id <", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_id <=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIn(List<Long> values) {
            addCriterion("schedule_id in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotIn(List<Long> values) {
            addCriterion("schedule_id not in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdBetween(Long value1, Long value2) {
            addCriterion("schedule_id between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_id not between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNull() {
            addCriterion("caption is null");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNotNull() {
            addCriterion("caption is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionEqualTo(String value) {
            addCriterion("caption =", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotEqualTo(String value) {
            addCriterion("caption <>", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThan(String value) {
            addCriterion("caption >", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThanOrEqualTo(String value) {
            addCriterion("caption >=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThan(String value) {
            addCriterion("caption <", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThanOrEqualTo(String value) {
            addCriterion("caption <=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionIn(List<String> values) {
            addCriterion("caption in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotIn(List<String> values) {
            addCriterion("caption not in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionBetween(String value1, String value2) {
            addCriterion("caption between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotBetween(String value1, String value2) {
            addCriterion("caption not between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigIsNull() {
            addCriterion("input_ctrl_config is null");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigIsNotNull() {
            addCriterion("input_ctrl_config is not null");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigEqualTo(String value) {
            addCriterion("input_ctrl_config =", value, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigNotEqualTo(String value) {
            addCriterion("input_ctrl_config <>", value, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigGreaterThan(String value) {
            addCriterion("input_ctrl_config >", value, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigGreaterThanOrEqualTo(String value) {
            addCriterion("input_ctrl_config >=", value, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigLessThan(String value) {
            addCriterion("input_ctrl_config <", value, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigLessThanOrEqualTo(String value) {
            addCriterion("input_ctrl_config <=", value, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigIn(List<String> values) {
            addCriterion("input_ctrl_config in", values, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigNotIn(List<String> values) {
            addCriterion("input_ctrl_config not in", values, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigBetween(String value1, String value2) {
            addCriterion("input_ctrl_config between", value1, value2, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andInputCtrlConfigNotBetween(String value1, String value2) {
            addCriterion("input_ctrl_config not between", value1, value2, "inputCtrlConfig");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagIsNull() {
            addCriterion("required_flag is null");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagIsNotNull() {
            addCriterion("required_flag is not null");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagEqualTo(Integer value) {
            addCriterion("required_flag =", value, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagNotEqualTo(Integer value) {
            addCriterion("required_flag <>", value, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagGreaterThan(Integer value) {
            addCriterion("required_flag >", value, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("required_flag >=", value, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagLessThan(Integer value) {
            addCriterion("required_flag <", value, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagLessThanOrEqualTo(Integer value) {
            addCriterion("required_flag <=", value, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagIn(List<Integer> values) {
            addCriterion("required_flag in", values, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagNotIn(List<Integer> values) {
            addCriterion("required_flag not in", values, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagBetween(Integer value1, Integer value2) {
            addCriterion("required_flag between", value1, value2, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andRequiredFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("required_flag not between", value1, value2, "requiredFlag");
            return (Criteria) this;
        }

        public Criteria andOrderNumbIsNull() {
            addCriterion("order_numb is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumbIsNotNull() {
            addCriterion("order_numb is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumbEqualTo(Double value) {
            addCriterion("order_numb =", value, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbNotEqualTo(Double value) {
            addCriterion("order_numb <>", value, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbGreaterThan(Double value) {
            addCriterion("order_numb >", value, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbGreaterThanOrEqualTo(Double value) {
            addCriterion("order_numb >=", value, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbLessThan(Double value) {
            addCriterion("order_numb <", value, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbLessThanOrEqualTo(Double value) {
            addCriterion("order_numb <=", value, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbIn(List<Double> values) {
            addCriterion("order_numb in", values, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbNotIn(List<Double> values) {
            addCriterion("order_numb not in", values, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbBetween(Double value1, Double value2) {
            addCriterion("order_numb between", value1, value2, "orderNumb");
            return (Criteria) this;
        }

        public Criteria andOrderNumbNotBetween(Double value1, Double value2) {
            addCriterion("order_numb not between", value1, value2, "orderNumb");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
