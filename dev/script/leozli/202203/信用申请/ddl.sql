ALTER TABLE [wx_t_credit_customer_info_final] add bu                               nvarchar(30)                                   null

CREATE INDEX ix_cre_cus_info_final_sold_to ON wx_t_credit_customer_info_final (sold_to);
CREATE INDEX ix_cre_cus_inf_fin_sal_cai ON wx_t_credit_customer_info_final (sales_cai);

ALTER TABLE wx_t_workflow_pre_exe_group ADD CONSTRAINT uk_workflow_pre_exe_group UNIQUE (group_name);

ALTER TABLE [wx_t_credit_app_request_form] add bu                               nvarchar(30)                                   null
ALTER TABLE [wx_t_credit_app_request_form] add workflow_status                  int                                        not null default(0)
ALTER TABLE [wx_t_credit_app_request_form] add customer_type                    nvarchar(30)                                   null
ALTER TABLE [wx_t_credit_app_request_form] add sold_to_code                     nvarchar(30)                                   null
ALTER TABLE [wx_t_credit_app_request_form] add payer_code                       nvarchar(30)                                   null
ALTER TABLE [wx_t_credit_app_request_form] add customer_name                    nvarchar(100)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add direct_annual_sales_plan         decimal(20,6)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add indirect_annual_sales_plan       decimal(20,6)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add ext_flag                         int                                        not null default(0)
ALTER TABLE [wx_t_credit_app_request_form] add ext_property1                    nvarchar(256)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add ext_property2                    nvarchar(256)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add ext_property3                    nvarchar(256)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add ext_property4                    nvarchar(256)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add ext_property5                    nvarchar(512)                                  null
ALTER TABLE [wx_t_credit_app_request_form] add flsr_id                          bigint                                         null
ALTER TABLE [wx_t_credit_app_request_form] add form_status                      int                                        not null default(0)
ALTER TABLE [wx_t_credit_app_request_form] add version_no                       bigint                                     not null
ALTER TABLE [wx_t_credit_app_request_form] add sales_channel                    nvarchar(50)                                   null
ALTER TABLE [wx_t_credit_app_request_form] add locker_id                        nvarchar(64)                                   null
ALTER TABLE [wx_t_credit_app_request_form] add apply_amount_usd                 decimal(20,6)                                  null

ALTER TABLE [wx_t_credit_app_customer_finance_info] add form_id                          bigint                                     not null
ALTER TABLE wx_t_credit_app_customer_finance_info ADD CONSTRAINT uk_cre_app_cus_fin_info UNIQUE (form_id);

ALTER TABLE [wx_t_credit_app_request_from_att] add request_no                       nvarchar(20)                               not null
CREATE INDEX ix_cre_app_req_fro_att_req_no ON wx_t_credit_app_request_from_att (request_no);

