insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id)
select distinct m.menu_id, r.role_id, '2', 1, 1,1,1--r.ch_role_name, m.menu_name, r.role_descript 
from wx_t_menu m left join wx_t_role r on 1=1
where r.status=1 and m.menu_id in (50344,
50349,
41,
50324,
50223,
50341,
33,
40052,
10035,
5,
50273,
50056,
50128)
and exists (select 1 from wx_t_rolesource rs1 left join [wx_t_menu20200810] m1 on m1.menu_pid=rs1.source_id 
left join [wx_t_menu] m2 on m2.menu_id=m1.menu_id
where m1.status=1 and m2.menu_pid=m.menu_id and m2.status=1)
and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)