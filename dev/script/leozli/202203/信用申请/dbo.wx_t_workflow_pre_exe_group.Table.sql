/*    ==Scripting Parameters==

    Source Server Version : SQL Server 2016 (13.0.4435)
    Source Database Engine Edition : Microsoft SQL Server Standard Edition
    Source Database Engine Type : Standalone SQL Server

    Target Server Version : SQL Server 2017
    Target Database Engine Edition : Microsoft SQL Server Standard Edition
    Target Database Engine Type : Standalone SQL Server
*/
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Credit BU Sales Manager', N'$table.user$.cai=(case when exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 where credit_bc1.[customer_code]=''$soldTo$'')
 then (select top 1 credit_rs1.channel_manager_cai from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_bc1.distributor_id=credit_bcs1.distributor_id
 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region where credit_bc1.[customer_code]=''$soldTo$'' and credit_rs1.bu=''$bu$'')
 else (select top 1 credit_rs1.channel_manager_cai from wx_t_credit_customer_info_final credit_c1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_c1.sales_cai=credit_bcs1.sales_cai
 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region where credit_c1.sold_to=''$soldTo$'' and credit_c1.bu=''$bu$'' and credit_c1.status=1) end) 
', CAST(100.0000000000 AS Decimal(20, 10)), N'信用申请Channel Manager', N'需soldTo和bu参数', 1, 0, 1, CAST(N'2022-07-21T15:17:32.987' AS DateTime), 1, CAST(N'2022-08-10T14:44:06.607' AS DateTime))
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Credit BU Area Sales Manager', N'$table.user$.cai=(case when exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 where credit_bc1.[customer_code]=''$soldTo$'')
 then (select top 1 credit_bcs1.supervisor_cai from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_bc1.distributor_id=credit_bcs1.distributor_id
 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region where credit_bc1.[customer_code]=''$soldTo$'' and credit_rs1.bu=''$bu$'')
 else (select top 1 credit_bcs1.supervisor_cai from wx_t_credit_customer_info_final credit_c1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_c1.sales_cai=credit_bcs1.sales_cai where credit_c1.sold_to=''$soldTo$'' and credit_c1.bu=''$bu$'' and credit_c1.status=1) end) ', CAST(110.0000000000 AS Decimal(20, 10)), N'信用申请ASM', N'需soldTo和bu参数', 1, 0, 1, CAST(N'2022-07-24T22:47:06.857' AS DateTime), 1, CAST(N'2022-08-10T14:44:37.207' AS DateTime))
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Credit ABM Sales Manager', N' $table.user$.cai=(case when exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 where credit_bc1.[customer_code]=''$soldTo$'')
 then (select top 1 credit_rs1.bu_manager_cai from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_bc1.distributor_id=credit_bcs1.distributor_id
 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region where credit_bc1.[customer_code]=''$soldTo$'' and credit_rs1.bu=''$bu$'')
 else (select top 1 credit_rs1.bu_manager_cai from wx_t_credit_customer_info_final credit_c1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_c1.sales_cai=credit_bcs1.sales_cai
 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region where credit_c1.sold_to=''$soldTo$'' and credit_c1.bu=''$bu$'' and credit_c1.status=1) end) ', CAST(120.0000000000 AS Decimal(20, 10)), N'信用申请ABM', N'需soldTo和bu参数', 1, 0, 1, CAST(N'2022-07-24T23:05:49.747' AS DateTime), 1, CAST(N'2022-08-02T09:28:15.753' AS DateTime))
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Local Credit Analyst', N'$table.user$.user_id!=1 and exists (select 1 from wx_t_userrole credit_ur
left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name=''Local_Credit_Analyst'' and credit_r.status=1)', CAST(130.0000000000 AS Decimal(20, 10)), N'Local Credit Analyst', N'', 1, 0, 1, CAST(N'2022-07-24T23:49:40.610' AS DateTime), 1, CAST(N'2022-08-10T00:32:09.893' AS DateTime))
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Local Credit Team Lead', N'$table.user$.user_id!=1 and (exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name=''Local_Credit_Team_Lead'' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 )
	or (exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name=''Local_Credit_Team_Lead'' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	and exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name=''Local_Credit_Analyst'' and credit_r.status=1)))	
', CAST(140.0000000000 AS Decimal(20, 10)), N'Local Credit Team Lead', N'', 1, 0, 1, CAST(N'2022-07-24T23:50:32.990' AS DateTime), 1, CAST(N'2022-08-22T10:28:10.683' AS DateTime))
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'China Finance Manager', N'$table.user$.user_id!=1 and (exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name=''China_Finance_Manager'' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 )
	or (exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name=''China_Finance_Manager'' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	and exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name=''Local_Credit_Team_Lead'' and credit_r.status=1)))	
', CAST(150.0000000000 AS Decimal(20, 10)), N'China Finance Manager', N'', 1, 0, 1, CAST(N'2022-07-24T23:51:41.787' AS DateTime), 1, CAST(N'2022-08-22T10:28:22.123' AS DateTime))
