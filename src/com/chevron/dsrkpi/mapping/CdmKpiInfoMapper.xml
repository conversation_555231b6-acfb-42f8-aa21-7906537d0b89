<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi.dao.CdmKpiInfoMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi.model.CdmKpiInfo">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="kpi_type" property="kpiType" jdbcType="VARCHAR"/>
		<result column="org_id" property="orgId" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="kpi_year" property="kpiYear" jdbcType="BIGINT"/>
		<result column="year_goals" property="yearGoals" jdbcType="NUMERIC"/>
		<result column="mon_goals" property="monGoals" jdbcType="NUMERIC"/>
		<result column="feb_goals" property="febGoals" jdbcType="NUMERIC"/>
		<result column="mar_goals" property="marGoals" jdbcType="NUMERIC"/>
		<result column="apr_goals" property="aprGoals" jdbcType="NUMERIC"/>
		<result column="may_goals" property="mayGoals" jdbcType="NUMERIC"/>
		<result column="june_goals" property="juneGoals" jdbcType="NUMERIC"/>
		<result column="july_goals" property="julyGoals" jdbcType="NUMERIC"/>
		<result column="aug_goals" property="augGoals" jdbcType="NUMERIC"/>
		<result column="sept_goals" property="septGoals" jdbcType="NUMERIC"/>
		<result column="oct_goals" property="octGoals" jdbcType="NUMERIC"/>
		<result column="nov_goals" property="novGoals" jdbcType="NUMERIC"/>
		<result column="dec_goals" property="decGoals" jdbcType="NUMERIC"/>
		<result column="year_awards" property="yearAwards" jdbcType="NUMERIC"/>
		<result column="total_points" property="totalPoints" jdbcType="NUMERIC"/>
		<result column="remaining_points" property="remainingPoints" jdbcType="NUMERIC"/>
		<result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
		<result column="reserved_field1" property="reservedField1" jdbcType="VARCHAR"/>
		<result column="reserved_field2" property="reservedField2" jdbcType="VARCHAR"/>
		<result column="reserved_field3" property="reservedField3" jdbcType="VARCHAR"/>
		<result column="reserved_field4" property="reservedField4" jdbcType="VARCHAR"/>
		<result column="reserved_field5" property="reservedField5" jdbcType="VARCHAR"/>
		<result column="reserved_field6" property="reservedField6" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,create_user_id,create_time,update_user_id,update_time,kpi_type,org_id,dsr_id,kpi_year,year_goals,mon_goals,
		feb_goals,mar_goals,apr_goals,may_goals,june_goals,july_goals,aug_goals,sept_goals,oct_goals,nov_goals,dec_goals,
		year_awards,total_points,remaining_points,del_flag,reserved_field1,reserved_field2,reserved_field3,reserved_field4,
		reserved_field5,reserved_field6
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi.model.CdmKpiInfo">
		update wx_t_cdm_kpi_info set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi.model.CdmKpiInfo">
		update wx_t_cdm_kpi_info
		<set>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="kpiType != null" >
				kpi_type = #{kpiType,jdbcType=VARCHAR},
			</if>
			<if test="orgId != null" >
				org_id = #{orgId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null" >
				dsr_id = #{dsrId,jdbcType=BIGINT},
			</if>
			<if test="kpiYear != null" >
				kpi_year = #{kpiYear,jdbcType=BIGINT},
			</if>
			<if test="yearGoals != null" >
				year_goals = #{yearGoals,jdbcType=NUMERIC},
			</if>
			<if test="monGoals != null" >
				mon_goals = #{monGoals,jdbcType=NUMERIC},
			</if>
			<if test="febGoals != null" >
				feb_goals = #{febGoals,jdbcType=NUMERIC},
			</if>
			<if test="marGoals != null" >
				mar_goals = #{marGoals,jdbcType=NUMERIC},
			</if>
			<if test="aprGoals != null" >
				apr_goals = #{aprGoals,jdbcType=NUMERIC},
			</if>
			<if test="mayGoals != null" >
				may_goals = #{mayGoals,jdbcType=NUMERIC},
			</if>
			<if test="juneGoals != null" >
				june_goals = #{juneGoals,jdbcType=NUMERIC},
			</if>
			<if test="julyGoals != null" >
				july_goals = #{julyGoals,jdbcType=NUMERIC},
			</if>
			<if test="augGoals != null" >
				aug_goals = #{augGoals,jdbcType=NUMERIC},
			</if>
			<if test="septGoals != null" >
				sept_goals = #{septGoals,jdbcType=NUMERIC},
			</if>
			<if test="octGoals != null" >
				oct_goals = #{octGoals,jdbcType=NUMERIC},
			</if>
			<if test="novGoals != null" >
				nov_goals = #{novGoals,jdbcType=NUMERIC},
			</if>
			<if test="decGoals != null" >
				dec_goals = #{decGoals,jdbcType=NUMERIC},
			</if>
			<if test="yearAwards != null" >
				year_awards = #{yearAwards,jdbcType=NUMERIC},
			</if>
			<if test="totalPoints != null" >
				total_points = #{totalPoints,jdbcType=NUMERIC},
			</if>
			<if test="remainingPoints != null" >
				remaining_points = #{remainingPoints,jdbcType=NUMERIC},
			</if>
			<if test="delFlag != null" >
				del_flag = #{delFlag,jdbcType=INTEGER},
			</if>
			<if test="reservedField1 != null" >
				reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
			</if>
			<if test="reservedField2 != null" >
				reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
			</if>
			<if test="reservedField3 != null" >
				reserved_field3 = #{reservedField3,jdbcType=VARCHAR},
			</if>
			<if test="reservedField4 != null" >
				reserved_field4 = #{reservedField4,jdbcType=VARCHAR},
			</if>
			<if test="reservedField5 != null" >
				reserved_field5 = #{reservedField5,jdbcType=VARCHAR},
			</if>
			<if test="reservedField6 != null" >
				reserved_field6 = #{reservedField6,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoExample">
    	delete from wx_t_cdm_kpi_info
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi.model.CdmKpiInfo" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_cdm_kpi_info
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="kpiType != null">
				kpi_type,
			</if>
			<if test="orgId != null">
				org_id,
			</if>
			<if test="dsrId != null">
				dsr_id,
			</if>
			<if test="kpiYear != null">
				kpi_year,
			</if>
			<if test="yearGoals != null">
				year_goals,
			</if>
			<if test="monGoals != null">
				mon_goals,
			</if>
			<if test="febGoals != null">
				feb_goals,
			</if>
			<if test="marGoals != null">
				mar_goals,
			</if>
			<if test="aprGoals != null">
				apr_goals,
			</if>
			<if test="mayGoals != null">
				may_goals,
			</if>
			<if test="juneGoals != null">
				june_goals,
			</if>
			<if test="julyGoals != null">
				july_goals,
			</if>
			<if test="augGoals != null">
				aug_goals,
			</if>
			<if test="septGoals != null">
				sept_goals,
			</if>
			<if test="octGoals != null">
				oct_goals,
			</if>
			<if test="novGoals != null">
				nov_goals,
			</if>
			<if test="decGoals != null">
				dec_goals,
			</if>
			<if test="yearAwards != null">
				year_awards,
			</if>
			<if test="totalPoints != null">
				total_points,
			</if>
			<if test="remainingPoints != null">
				remaining_points,
			</if>
			<if test="delFlag != null">
				del_flag,
			</if>
			<if test="reservedField1 != null">
				reserved_field1,
			</if>
			<if test="reservedField2 != null">
				reserved_field2,
			</if>
			<if test="reservedField3 != null">
				reserved_field3,
			</if>
			<if test="reservedField4 != null">
				reserved_field4,
			</if>
			<if test="reservedField5 != null">
				reserved_field5,
			</if>
			<if test="reservedField6 != null">
				reserved_field6,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="kpiType != null">
				#{kpiType,jdbcType=VARCHAR},
			</if>
			<if test="orgId != null">
				#{orgId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null">
				#{dsrId,jdbcType=BIGINT},
			</if>
			<if test="kpiYear != null">
				#{kpiYear,jdbcType=BIGINT},
			</if>
			<if test="yearGoals != null">
				#{yearGoals,jdbcType=NUMERIC},
			</if>
			<if test="monGoals != null">
				#{monGoals,jdbcType=NUMERIC},
			</if>
			<if test="febGoals != null">
				#{febGoals,jdbcType=NUMERIC},
			</if>
			<if test="marGoals != null">
				#{marGoals,jdbcType=NUMERIC},
			</if>
			<if test="aprGoals != null">
				#{aprGoals,jdbcType=NUMERIC},
			</if>
			<if test="mayGoals != null">
				#{mayGoals,jdbcType=NUMERIC},
			</if>
			<if test="juneGoals != null">
				#{juneGoals,jdbcType=NUMERIC},
			</if>
			<if test="julyGoals != null">
				#{julyGoals,jdbcType=NUMERIC},
			</if>
			<if test="augGoals != null">
				#{augGoals,jdbcType=NUMERIC},
			</if>
			<if test="septGoals != null">
				#{septGoals,jdbcType=NUMERIC},
			</if>
			<if test="octGoals != null">
				#{octGoals,jdbcType=NUMERIC},
			</if>
			<if test="novGoals != null">
				#{novGoals,jdbcType=NUMERIC},
			</if>
			<if test="decGoals != null">
				#{decGoals,jdbcType=NUMERIC},
			</if>
			<if test="yearAwards != null">
				#{yearAwards,jdbcType=NUMERIC},
			</if>
			<if test="totalPoints != null">
				#{totalPoints,jdbcType=NUMERIC},
			</if>
			<if test="remainingPoints != null">
				#{remainingPoints,jdbcType=NUMERIC},
			</if>
			<if test="delFlag != null">
				#{delFlag,jdbcType=INTEGER},
			</if>
			<if test="reservedField1 != null">
				#{reservedField1,jdbcType=VARCHAR},
			</if>
			<if test="reservedField2 != null">
				#{reservedField2,jdbcType=VARCHAR},
			</if>
			<if test="reservedField3 != null">
				#{reservedField3,jdbcType=VARCHAR},
			</if>
			<if test="reservedField4 != null">
				#{reservedField4,jdbcType=VARCHAR},
			</if>
			<if test="reservedField5 != null">
				#{reservedField5,jdbcType=VARCHAR},
			</if>
			<if test="reservedField6 != null">
				#{reservedField6,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_cdm_kpi_info
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.kpiType != null">
				kpi_type = #{record.kpiType,jdbcType=VARCHAR},
			</if>
			<if test="record.orgId != null">
				org_id = #{record.orgId,jdbcType=BIGINT},
			</if>
			<if test="record.dsrId != null">
				dsr_id = #{record.dsrId,jdbcType=BIGINT},
			</if>
			<if test="record.kpiYear != null">
				kpi_year = #{record.kpiYear,jdbcType=BIGINT},
			</if>
			<if test="record.yearGoals != null">
				year_goals = #{record.yearGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.monGoals != null">
				mon_goals = #{record.monGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.febGoals != null">
				feb_goals = #{record.febGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.marGoals != null">
				mar_goals = #{record.marGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.aprGoals != null">
				apr_goals = #{record.aprGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.mayGoals != null">
				may_goals = #{record.mayGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.juneGoals != null">
				june_goals = #{record.juneGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.julyGoals != null">
				july_goals = #{record.julyGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.augGoals != null">
				aug_goals = #{record.augGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.septGoals != null">
				sept_goals = #{record.septGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.octGoals != null">
				oct_goals = #{record.octGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.novGoals != null">
				nov_goals = #{record.novGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.decGoals != null">
				dec_goals = #{record.decGoals,jdbcType=NUMERIC},
			</if>
			<if test="record.yearAwards != null">
				year_awards = #{record.yearAwards,jdbcType=NUMERIC},
			</if>
			<if test="record.totalPoints != null">
				total_points = #{record.totalPoints,jdbcType=NUMERIC},
			</if>
			<if test="record.remainingPoints != null">
				remaining_points = #{record.remainingPoints,jdbcType=NUMERIC},
			</if>
			<if test="record.delFlag != null">
				del_flag = #{record.delFlag,jdbcType=INTEGER},
			</if>
			<if test="record.reservedField1 != null">
				reserved_field1 = #{record.reservedField1,jdbcType=VARCHAR},
			</if>
			<if test="record.reservedField2 != null">
				reserved_field2 = #{record.reservedField2,jdbcType=VARCHAR},
			</if>
			<if test="record.reservedField3 != null">
				reserved_field3 = #{record.reservedField3,jdbcType=VARCHAR},
			</if>
			<if test="record.reservedField4 != null">
				reserved_field4 = #{record.reservedField4,jdbcType=VARCHAR},
			</if>
			<if test="record.reservedField5 != null">
				reserved_field5 = #{record.reservedField5,jdbcType=VARCHAR},
			</if>
			<if test="record.reservedField6 != null">
				reserved_field6 = #{record.reservedField6,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoExample">
		delete from wx_t_cdm_kpi_info
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoExample" resultType="int">
		select count(1) from wx_t_cdm_kpi_info
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_cdm_kpi_info
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_cdm_kpi_info
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.kpi_type, t1.org_id,
			 t1.dsr_id, t1.kpi_year, t1.year_goals, t1.mon_goals, t1.feb_goals, t1.mar_goals, t1.apr_goals, t1.may_goals,
			 t1.june_goals, t1.july_goals, t1.aug_goals, t1.sept_goals, t1.oct_goals, t1.nov_goals, t1.dec_goals,
			 t1.year_awards, t1.total_points, t1.remaining_points, t1.del_flag, t1.reserved_field1, t1.reserved_field2,
			 t1.reserved_field3, t1.reserved_field4, t1.reserved_field5, t1.reserved_field6
		  from wx_t_cdm_kpi_info t1
		 where 1=1
		 <choose>
			 <when test="tmm">
			 	AND t1.reserved_field6 = '1'
			 </when>
			 <otherwise>
			 	AND t1.reserved_field2 = '1'
			 </otherwise>
		 </choose>
		 <if test="dsrId != null and dsrId != ''">
		 	and t1.dsr_id = #{dsrId, jdbcType=BIGINT }
		 </if>
		 <if test="types != null and types != ''">
		 	and t1.kpi_type in
		 	<foreach collection="types" item="type" open="(" close=")" separator="," >
				#{type ,jdbcType=VARCHAR}
			</foreach>
		 </if>
		 <if test="kpiYear != null and kpiYear != ''">
		 	and t1.kpi_year = #{kpiYear,jdbcType=BIGINT}
		 </if>
		 <if test="orgId != null and orgId != ''">
		 	and t1.org_id = #{orgId,jdbcType=BIGINT}
		 </if>
		 ORDER BY dsr_id
	</select>
	
	
	<resultMap id="ResultByCondtionMap" type="com.chevron.dsrkpi.model.CdmKpiInfoVo">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="kpi_type" property="kpiType" jdbcType="VARCHAR"/>
		<result column="org_id" property="orgId" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="kpi_year" property="kpiYear" jdbcType="BIGINT"/>
		<result column="year_goals" property="yearGoals" jdbcType="NUMERIC"/>
		<result column="mon_goals" property="monGoals" jdbcType="NUMERIC"/>
		<result column="feb_goals" property="febGoals" jdbcType="NUMERIC"/>
		<result column="mar_goals" property="marGoals" jdbcType="NUMERIC"/>
		<result column="apr_goals" property="aprGoals" jdbcType="NUMERIC"/>
		<result column="may_goals" property="mayGoals" jdbcType="NUMERIC"/>
		<result column="june_goals" property="juneGoals" jdbcType="NUMERIC"/>
		<result column="july_goals" property="julyGoals" jdbcType="NUMERIC"/>
		<result column="aug_goals" property="augGoals" jdbcType="NUMERIC"/>
		<result column="sept_goals" property="septGoals" jdbcType="NUMERIC"/>
		<result column="oct_goals" property="octGoals" jdbcType="NUMERIC"/>
		<result column="nov_goals" property="novGoals" jdbcType="NUMERIC"/>
		<result column="dec_goals" property="decGoals" jdbcType="NUMERIC"/>
		<result column="year_awards" property="yearAwards" jdbcType="NUMERIC"/>
		<result column="total_points" property="totalPoints" jdbcType="NUMERIC"/>
		<result column="remaining_points" property="remainingPoints" jdbcType="NUMERIC"/>
		<result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
		<result column="reserved_field1" property="reservedField1" jdbcType="VARCHAR"/>
		<result column="reserved_field2" property="reservedField2" jdbcType="VARCHAR"/>
		<result column="reserved_field3" property="reservedField3" jdbcType="VARCHAR"/>
		<result column="reserved_field4" property="reservedField4" jdbcType="VARCHAR"/>
		<result column="reserved_field5" property="reservedField5" jdbcType="VARCHAR"/>
		<result column="reserved_field6" property="reservedField6" jdbcType="VARCHAR"/>
		<result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
		<result column="ch_name" property="dsrName" jdbcType="VARCHAR"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="suppervisor_name" property="suppervisorName" jdbcType="VARCHAR"/>	
		<result column="distributor_id" property="distributorId" jdbcType="INTEGER"/>
		<result column="faceVisitNum" property="faceVisitNum" jdbcType="INTEGER"/>
		<result column="factEntryNum" property="factEntryNum" jdbcType="INTEGER"/>
		<result column="factSalesNum" property="factSalesNum" jdbcType="INTEGER"/>
		<result column="factSnSalesNum" property="factSnSalesNum" jdbcType="INTEGER"/>
		<result column="factTmmNum" property="factTmmNum" jdbcType="INTEGER"/>
	</resultMap>
	
	<sql id="cdm_kpi_info_sql">	
		SELECT
		DISTINCT crss.customer_name_cn,u.ch_name,po.distributor_id,wk.*
		FROM
			wx_t_cdm_kpi_info wk
		LEFT JOIN wx_t_partner_o2o_enterprise po ON wk.org_id = po.partner_id
		LEFT JOIN wx_t_user u ON wk.dsr_id = u.user_id
		LEFT JOIN view_customer_region_sales_channel crss ON po.distributor_id = crss.distributor_id AND crss.channel_weight &amp; 1 &gt; 0
		LEFT JOIN wx_t_value_transform_map tr ON crss.region_name = tr.value_before_transform
		AND tr.transform_type = 'ChRegionNameMapping'
		LEFT JOIN wx_t_userrole ur ON u.user_id = ur.user_id
		LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crss.sales_cai
						AND dp_001_sr1.del_flag = 0
		WHERE 1 = 1 
	</sql>
	<sql id="cdm_kpi_info_condition_sql">
		<if test="customerNameCn != null and customerNameCn != ''">
			and crss.customer_name_cn like '%'+ #{customerNameCn,jdbcType=VARCHAR}+'%'
		</if>
		<if test="regionName != null and regionName != '' ">
			and tr.value_after_transform = #{regionName,jdbcType=VARCHAR}
		</if>
		<if test="dsrName != null and dsrName != ''">
			and u.ch_name like '%'+ #{dsrName,jdbcType=VARCHAR}+'%'
		</if>
		<if test="salesName != null and salesName != ''">
			and crss.sales_name like '%'+ #{salesName,jdbcType=VARCHAR}+'%'
		</if>
		<if test="suppervisorName != null and suppervisorName != ''">
			and crss.suppervisor_name like '%'+ #{suppervisorName,jdbcType=VARCHAR}+'%'
		</if>

        <if test=" salesCai != null and  salesCai != ''">
            AND crss.sales_cai = #{salesCai}
        </if>
        <if test="suppervisorCai != null and suppervisorCai != ''">
            AND crss.suppervisor_cai = #{suppervisorCai}
        </if>
        <if test="teamLeaderCai != null and teamLeaderCai != ''">
            AND dp_001_sr1.sales_cai_level LIKE '%_'+  #{teamLeaderCai}+'%'
        </if>
        <if test="channelManagerCai != null and channelManagerCai != ''">
            AND crss.channel_manager_cai = #{channelManagerCai}
        </if>
        <if test="buManagerCai != null and buManagerCai != ''">
            AND crss.bu_manager_cai = #{buManagerCai}
        </if>

	</sql>
	<select id="getCdmKpiInfoListByCondition" resultMap="ResultByCondtionMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo">
		<include refid="cdm_kpi_info_sql"></include>
		<include refid="cdm_kpi_info_condition_sql"></include>
        <if test="tmmUser == null or !tmmUser">
            <!--DSR绩效使用reserved_field2来判断是否生效-->
            and wk.reserved_field2 = '1'
        </if>
        <if test="tmmUser != null and tmmUser">
        <!--tmm绩效计算用reserved_field6来区分是否生效-->
            <if test="allFlag == null or allFlag == false">
                and wk.reserved_field6 = '1'
            </if>
        </if>
<if test="kpiYear != null and kpiYear != ''">
    and wk.kpi_year = #{kpiYear,jdbcType=BIGINT}
</if>
<if test="orgId != null and orgId != ''">
    and wk.org_id = #{orgId,jdbcType=BIGINT}
</if>
<if test="dsrId != null and dsrId != ''">
    and wk.dsr_id = #{dsrId,jdbcType=BIGINT}
</if>
<if test="dsrIds != null and dsrIds.size > 0 ">
    and wk.dsr_id in
    <foreach collection="dsrIds" item="item" open="(" close=")" separator="," >
        #{item}
    </foreach>
</if>
<if test="orgIds != null and orgIds.size>0">
    and wk.org_id in
    <foreach collection="orgIds" item="orgId" open="(" close=")" separator="," >
        #{orgId ,jdbcType=BIGINT}
    </foreach>
    and wk.dsr_id is null
</if>
<if test="kpiType != null and kpiType != ''">
    and wk.kpi_type = #{kpiType,jdbcType=VARCHAR}
</if>
<if test="tmmUser != null">
    <if test="tmmUser and (orgIds == null or orgIds == '')">
    AND exists (
        SELECT
            1
        FROM
            wx_t_tmm_info tmm
        WHERE
            tmm.from_source = 1 and wk.dsr_id = tmm.dsr_id
        )
    </if>
    <if test="!tmmUser and (orgIds == null or orgIds == '')">
        AND NOT EXISTS (
            SELECT
            1
        FROM
            wx_t_tmm_info tmm
        WHERE
            tmm.from_source = 1 and wk.dsr_id = tmm.dsr_id
        )
    </if>
</if>
</select>

<resultMap type="com.chevron.dsrkpi.model.app.AppCdmKpiDisInfoVO" id="AppResultMap">
<result column="partnerConfirmNum" property="partnerConfirmNum" jdbcType="BIGINT"/>
<result column="mktConfirmNum" property="mktConfirmNum" jdbcType="BIGINT"/>
<result column="commitNum" property="commitNum" jdbcType="BIGINT"/>
<result column="factNum" property="factNum" jdbcType="BIGINT"/>
<result column="dayTime" property="dayTime" jdbcType="VARCHAR"/>
<result column="otherNum" property="otherNum" jdbcType="BIGINT"/>
<result column="outSalesNum" property="outSalesNum" jdbcType="BIGINT"/>
<result column="product_property" property="productProperty" jdbcType="BIGINT"/>
</resultMap>

<select id="appGetCDMVisitInfo" resultMap="AppResultMap" parameterType="map">
SELECT COUNT (t2.org_id) factNum,t2.xg_sj dayTime
    FROM
        (
            SELECT
                t1.*, ROW_NUMBER () OVER (partition BY t1.org_id ORDER BY t1.xg_sj ) rowNum
            FROM
                (
                    SELECT DISTINCT s.org_id, CONVERT (VARCHAR(10), s.xg_sj, 23) xg_sj
                    FROM
                        wx_task_main w
                    LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
                    LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
                    WHERE
                    s.exec_user = #{dsrId,jdbcType=BIGINT}
                    AND w.tmb_type_code = 'TT_2_XD'
                    AND s.xg_sj &gt;= #{startDay,jdbcType=TIMESTAMP}
                    AND s.xg_sj &lt; #{endDay,jdbcType=TIMESTAMP}
                    AND ws.status != -3
                    AND s.task_status = 4
                ) t1
        ) t2
    WHERE
        t2.rowNum &lt;= (
            SELECT DISTINCT
                code
            FROM
                wx_t_properties
            WHERE
                codetype = 'cdm_mon_air_days'
        )
    GROUP BY
        t2.xg_sj
</select>

<select id="appGetCDMEntryInfo"  resultMap="AppResultMap" parameterType="map">
SELECT COUNT (tt.status) commitNum, tt.status mktConfirmNum , tt.ext_property19 partnerConfirmNum
    FROM
        (
            SELECT DISTINCT s.org_id, CONVERT (VARCHAR(100), ws.ext_property22, 23) xg_sj, s.exec_user, ws.status, ws.ext_property19
            FROM
                wx_task_main w
            LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
            LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
            WHERE
                s.exec_user =  #{dsrId,jdbcType=BIGINT}
            AND w.tmb_type_code = 'TT_2_SD'
            AND ws.ext_property22 &gt;= #{startDay,jdbcType=TIMESTAMP}
            AND ws.ext_property22 &lt; #{endDay,jdbcType=TIMESTAMP}
            AND (s.task_status = 4 OR s.task_status = 3)
        ) tt
    GROUP BY
        tt.ext_property19,
        tt.status
</select>

<select id="appGetCDMSalesInfo" resultMap="AppResultMap" parameterType="map">
SELECT tt.work_shop_id otherNum, tt.status outSalesNum, tt.partner_confirm_type partnerConfirmNum, SUM (tt.saleNum) factNum
FROM
    (
        SELECT DISTINCT wto.order_no, wto.work_shop_id, wto.status, wto.partner_confirm_type, wol.sku,
        <!-- wol.amount * convert(float, tp.capacity) -->
				(case when t.org_id is not null and t1.org_id is not null then wol.amount * convert(float, tp.capacity)
				when t.org_id is null then wol.amount * convert(float, tp.capacity)
				else 0 end) saleNum
				FROM
					wx_t_order wto
				LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
				LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
				LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
				LEFT JOIN wx_t_user u ON u.user_id = wto.creator
				left join (select DISTINCT org_id from wx_t_cdm_kpi_sellIn_product_code) t on u.org_id  = t.org_id
				left join wx_t_cdm_kpi_sellIn_product_code t1 on u.org_id  = t1.org_id and t1.product_code = tp.sku
				WHERE
					wto.creator = #{dsrId,jdbcType=BIGINT}
				AND wto.create_time &gt;= #{startDay,jdbcType=TIMESTAMP}
				AND wto.create_time &lt; #{endDay,jdbcType=TIMESTAMP}
				AND wto.status IN (10, 11)
			) tt
		GROUP BY
			tt.status,
			tt.partner_confirm_type,tt.work_shop_id
	</select>
	
	<select id="appGetCDMTMMFinishInfo" resultMap="AppResultMap" parameterType="map">
		SELECT tt.work_shop_id otherNum, tt.status outSalesNum, tt.partner_confirm_type partnerConfirmNum,
			SUM (tt.saleNum) factNum, tt.product_property
		FROM
			(
				SELECT DISTINCT wto.order_no, 	wto.work_shop_id, wto.status, wto.partner_confirm_type,
					wol.sku, wol.amount * CONVERT (FLOAT, tp.capacity) saleNum, tp.product_property
				FROM
					wx_t_order wto
				LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
				LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
				LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
				WHERE
					wto.creator = #{dsrId,jdbcType=BIGINT}
				AND wto.create_time &gt;= #{startDay,jdbcType=TIMESTAMP}
				AND wto.create_time &lt; #{endDay,jdbcType=TIMESTAMP}
				AND wto.status IN (10, 11)
			) tt
		GROUP BY tt.status, tt.partner_confirm_type, tt.work_shop_id, tt.product_property
	</select>
	
	<select id="countPartnerConfirmCDMEntry" resultType="Long" parameterType="map"> 
		SELECT
			COUNT (tt.org_id) commitNum
		FROM
			(
				SELECT DISTINCT
					s.org_id,
					CONVERT (VARCHAR(7), ws.ext_property22, 23) xg_sj,
					s.exec_user,
					ws.status,
					ws.ext_flag
				FROM
					wx_task_main w
				LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
				LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
				WHERE
					s.exec_user = #{dsrId,jdbcType=BIGINT}
				AND w.tmb_type_code = 'TT_2_SD'
				AND ws.ext_property22 &gt;= #{startDay,jdbcType=TIMESTAMP}
				AND ws.ext_property22 &lt; #{endDay,jdbcType=TIMESTAMP}
				AND ws.status != -3
				AND s.task_status = 4
				AND ws.ext_flag <![CDATA[&]]> 256 = 256  
			) tt
	</select>
	
	<select id="getCdmKpiFinishListByCondition" resultMap="ResultByCondtionMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo"> 
		SELECT 
			crsc.customer_name_cn,
			crsc.region_name,
			crsc.suppervisor_name,
			crsc.sales_name,
			crsc.distributor_id,
			u.ch_name,
			<if test="tmmUser">
				tt_tmm.factTmmNum,
			</if>
			cd.*
		<!-- 
			tt_visit.faceVisitNum,
			tt_entry.factEntryNum,
			tt_sales.saleNum factSalesNum -->
			<!-- tt_sn_sales.saleNum factSnSalesNum, -->
		FROM
			wx_t_cdm_kpi_info cd
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = cd.org_id
		LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 1 &gt; 0
		LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform
		AND tr.transform_type = 'ChRegionNameMapping'
		LEFT JOIN wx_t_user u ON cd.dsr_id = u.user_id
		LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
	<!-- 	LEFT JOIN (
			SELECT
				SUM (t.faceVisitNum) faceVisitNum,
				t.exec_user
			FROM
				(	
				SELECT
					COUNT (t2.org_id) faceVisitNum,
					t2.exec_user
				FROM
					(
						SELECT
							tt.*, ROW_NUMBER () OVER (
								partition BY tt.org_id,tt.exec_user
								ORDER BY
									tt.xg_sj
							) rowNum
						FROM
							(
								SELECT DISTINCT
									s.exec_user,
									s.org_id,
									CONVERT (VARCHAR(10), s.xg_sj, 23) xg_sj
								FROM
									wx_task_main w
								LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
								LEFT JOIN wx_t_cdm_kpi_info ck ON s.exec_user = ck.dsr_id
								LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
								WHERE
									w.tmb_type_code = 'TT_2_XD'
								AND ck.reserved_field2 = '1'
								AND s.xg_sj &gt;= '${startDate}'
								AND s.xg_sj &lt; '${endDate}'
								AND s.xg_sj &gt;= '2020-04-01 00:00:00.000'
								AND ws.status != - 3
								AND s.task_status = 4
							) tt
					) t2
				WHERE
					t2.rowNum &lt;= (
						SELECT DISTINCT
							code
						FROM
							wx_t_properties
						WHERE
							codetype = 'cdm_mon_air_days'
					)
				GROUP BY
					t2.exec_user
				UNION ALL
				SELECT
					SUM (kpi_value) faceVisitNum,
					dsr_id exec_user
				FROM
					wx_t_cdm_kpi_integral_his_info cd
				WHERE
					cd.kpi_type = 'settingVisit'
				AND cd.effective_time &lt; '${startDate}'
				AND cd.kpi_year = #{kpiYear}
				AND cd.effective_time &gt;= '2020-04-01 00:00:00.000'
				GROUP BY cd.dsr_id
			) t GROUP BY t.exec_user
		) tt_visit ON cd.dsr_id = tt_visit.exec_user
		LEFT JOIN (
		SELECT
			SUM(t.factEntryNum) factEntryNum, t.creator
		FROM
			(
			SELECT
				COUNT (DISTINCT ws.id) factEntryNum,
				ws.creator
			FROM
				wx_t_work_shop ws
			LEFT JOIN wx_task_sub s ON s.org_id = ws.id
			LEFT JOIN wx_t_cdm_kpi_info ck ON s.exec_user = ck.dsr_id
			LEFT JOIN wx_task_main w ON s.task_main_id = w.task_main_id
			WHERE
				1 = 1
			AND w.tmb_type_code = 'TT_2_SD'
			AND ck.reserved_field2 = '1'
			AND ws.partner_confirm_time &gt;= '${startDate}'
			AND ws.partner_confirm_time &lt; '${endDate}'
			AND ws.partner_confirm_time &gt;= '2020-04-01 00:00:00.000'
			AND ws.ext_flag &amp; 256 &gt; 0
			GROUP BY
				ws.creator
			UNION ALL
			SELECT
				SUM (kpi_value) factEntryNum,
				dsr_id creator
			FROM
				wx_t_cdm_kpi_integral_his_info cd
			WHERE
				cd.kpi_type = 'settingEntry'
			AND cd.effective_time &lt; '${startDate}'
			AND cd.kpi_year = #{kpiYear}
			AND cd.effective_time  &gt;= '2020-04-01 00:00:00.000'
			GROUP BY
				cd.dsr_id
			) t GROUP BY t.creator
		) tt_entry ON cd.dsr_id = tt_entry.creator
		LEFT JOIN (  
			SELECT t.creator,
				SUM (t.saleNum) saleNum
			FROM
				(
				SELECT
					wto.creator,
					SUM (wol.amount * convert(float, tp.capacity)) saleNum
				FROM
					wx_t_order wto
				LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
				LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = wto.creator AND cd.kpi_type = 'settingSales'
				LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
				LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
				WHERE
					wto.create_time &gt;= '${startDate}'
				AND cd.reserved_field2 = '1'
				AND wto.create_time &lt; '${endDate}'
				AND wto.create_time &gt;= '2020-04-01 00:00:00.000'
				AND (wto.status = 11 OR  wto.partner_confirm_type = 1)
				GROUP BY
					wto.creator
				UNION ALL
				SELECT
					dsr_id creator,
					SUM (kpi_value) saleNum
				FROM
					wx_t_cdm_kpi_integral_his_info cd
				WHERE
					cd.kpi_type = 'settingSales'
				AND cd.effective_time &lt; '${startDate}'
				AND cd.kpi_year = #{kpiYear}
				AND cd.effective_time  &gt;= '2020-04-01 00:00:00.000'
				GROUP BY cd.dsr_id
				) t
			GROUP BY
				t.creator
		) tt_sales ON tt_sales.creator = cd.dsr_id -->
		<!-- LEFT JOIN (
			SELECT
				wto.creator,
				SUM (wol.amount * tp.pack) saleNum
			FROM
				wx_t_order wto
			LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
			LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
			LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			WHERE
				wto.partner_confirm_time &gt;= #{startDate}
			AND wto.partner_confirm_time &lt; #{endDate}
			AND (wto.status = 11 OR  wto.partner_confirm_type = 1)
			AND tp.oil_grade = 'SN'
			GROUP BY
				wto.creator
		) tt_sn_sales ON tt_sn_sales.creator = cd.dsr_id -->
		<if test="tmmUser">
			LEFT JOIN (
				SELECT
					wto.creator,
					COUNT (DISTINCT wto.work_shop_id) factTmmNum
				FROM
					wx_t_order wto
				LEFT JOIN wx_t_userrole ur ON wto.creator = ur.user_id
				WHERE
					wto.update_time &gt;= '${startDate}'
				AND wto.update_time &lt;  '${endDate}'
				AND wto.update_time  &gt;= '2020-04-01 00:00:00.000'
				AND wto.status IN (10, 11)
				AND ur.role_id = (
					SELECT DISTINCT
						role_id
					FROM
						wx_t_role
					WHERE
						ch_role_name = 'tmm'
				)
				GROUP BY
					wto.creator
			) tt_tmm ON tt_tmm.creator = cd.dsr_id 
		
		</if>
		WHERE
			cd.dsr_id IS NOT NULL
		AND cd.kpi_year = #{kpiYear}
		AND cd.kpi_type != 'settingReward'
		AND cd.reserved_field2 = '1'
		<if test="tmmUser != null">
			<if test="tmmUser">
				AND	ur.role_id = (
					SELECT DISTINCT
						role_id
					FROM
						wx_t_role
					WHERE
						ch_role_name = 'tmm'
					)
			</if>
			<if test="!tmmUser">
				AND NOT EXISTS (
					SELECT
						1
					FROM
						wx_t_userrole ur
					LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
					WHERE
						ur.user_id = cd.dsr_id
					AND r.ch_role_name = 'tmm'
				)
			</if>
		</if>
		<if test="dsrName != null and dsrName != ''">
			AND u.ch_name like '%'+ #{dsrName} +'%'
		</if>
		<if test="customerNameCn != null and customerNameCn != ''">
			AND crsc.customer_name_cn like '%'+ #{customerNameCn} +'%'
		</if>
		<if test="regionName != null and regionName != ''">
			AND tr.value_after_transform = #{regionName} 
		</if>
		<if test="salesName != null and salesName != ''">
			AND crsc.sales_name like '%'+ #{salesName} +'%'
		</if>
		<if test="suppervisorName != null and suppervisorName != ''">
			AND crsc.suppervisor_name like '%'+ #{suppervisorName} +'%' 
		</if>
		<if test="partnerId != null and partnerId != ''">
			AND cd.org_id = #{partnerId}
		</if>
		<if test=" salesCai != null and  salesCai != ''">
			AND crsc.sales_cai = #{salesCai}
		</if>
		<if test="suppervisorCai != null and suppervisorCai != ''">
			AND crsc.suppervisor_cai = #{suppervisorCai}
		</if>
		<if test="teamLeaderCai != null and teamLeaderCai != ''">
			and dp_001_sr1.sales_cai_level LIKE '%_'+ #{teamLeaderCai} +'%' 
		</if>
		<if test="channelManagerCai != null and channelManagerCai != ''">
			AND crsc.channel_manager_cai = #{channelManagerCai}
		</if>
		<if test="buManagerCai != null and buManagerCai != ''">
			AND crsc.bu_manager_cai = #{buManagerCai}
		</if>
	</select>


    <select id="getCdmKpiListByMonth" resultMap="ResultByCondtionMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo">
        SELECT
        crsc.customer_name_cn,
        crsc.region_name,
        crsc.suppervisor_name,
        crsc.sales_name,
        crsc.distributor_id,
        u.ch_name,
        cd.*,
        <if test="tmmUser">
            tt_tmm.factTmmNum,
        </if>
        tt_visit.faceVisitNum,
        tt_entry.factEntryNum,
        tt_sales.saleNum factSalesNum
        <!-- tt_sn_sales.saleNum factSnSalesNum, -->
        FROM
        wx_t_cdm_kpi_info cd
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = cd.org_id
        LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 1 &gt; 0
        LEFT JOIN wx_t_user u ON cd.dsr_id = u.user_id
        LEFT JOIN (
        SELECT
        COUNT (t2.org_id) faceVisitNum,
        t2.exec_user
        FROM
        (
        SELECT
        tt.*, ROW_NUMBER () OVER (
        partition BY tt.org_id,tt.exec_user
        ORDER BY
        tt.xg_sj
        ) rowNum
        FROM
        (
        SELECT DISTINCT
        s.exec_user,
        s.org_id,
        CONVERT (VARCHAR(10), s.xg_sj, 23) xg_sj
        FROM
        wx_task_main w
        LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
        LEFT JOIN wx_t_cdm_kpi_info ck ON s.exec_user = ck.dsr_id
        LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
        WHERE
        w.tmb_type_code = 'TT_2_XD'
        AND s.xg_sj &gt;= '${startDate}'
        AND s.xg_sj &lt; '${endDate}'
        AND s.xg_sj &gt;= '2020-04-01 00:00:00.000'
        AND ws.status != - 3
        AND s.task_status = 4
        ) tt
        ) t2
        WHERE
        t2.rowNum &lt;= (
        SELECT DISTINCT
        code
        FROM
        wx_t_properties
        WHERE
        codetype = 'cdm_mon_air_days'
        )
        GROUP BY
        t2.exec_user
        ) tt_visit ON cd.dsr_id = tt_visit.exec_user
        LEFT JOIN (
        SELECT
        COUNT (DISTINCT ws.id) factEntryNum,
        ws.creator
        FROM
        wx_t_work_shop ws
        LEFT JOIN wx_task_sub s ON s.org_id = ws.id
        LEFT JOIN wx_task_main w ON s.task_main_id = w.task_main_id
        WHERE
        1 = 1
        AND w.tmb_type_code = 'TT_2_SD'
        AND ws.ext_property22 &gt;= '${startDate}'
        AND ws.ext_property22 &lt; '${endDate}'
        AND ws.ext_property22 &gt;= '2020-04-01 00:00:00.000'
        AND ws.ext_flag &amp; 256 &gt; 0
        GROUP BY
        ws.creator
        ) tt_entry ON cd.dsr_id = tt_entry.creator
        LEFT JOIN (
        SELECT
        wto.creator,
        SUM (case when t.org_id is not null and t1.org_id is not null then wol.amount * convert(float, tp.capacity)
        when t.org_id is null then wol.amount * convert(float, tp.capacity)
        else 0 end) as saleNum FROM wx_t_order wto
        LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
        LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
        LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
        LEFT JOIN wx_t_user u ON u.user_id = wto.creator
        left join (select DISTINCT org_id from wx_t_cdm_kpi_sellIn_product_code) t on u.org_id  = t.org_id
        left join wx_t_cdm_kpi_sellIn_product_code t1 on u.org_id  = t1.org_id and t1.product_code = tp.sku
        WHERE
        wto.create_time &gt;= '${startDate}'
        AND wto.create_time &lt; '${endDate}'
        AND wto.create_time &gt;= '2020-04-01 00:00:00.000'
        AND (wto.status = 11 OR  wto.partner_confirm_type = 1)
        GROUP BY
        wto.creator
        ) tt_sales ON tt_sales.creator = cd.dsr_id
        <if test="tmmUser">
            LEFT JOIN (
            SELECT
            wto.creator,
            COUNT (DISTINCT wto.work_shop_id) factTmmNum
            FROM
            wx_t_order wto
            LEFT JOIN wx_t_userrole ur ON wto.creator = ur.user_id
            WHERE
            wto.update_time &gt;= '${startDate}'
            AND wto.update_time &lt;  '${endDate}'
            AND wto.update_time  &gt;= '2020-04-01 00:00:00.000'
            AND wto.status IN (10, 11)
            AND ur.role_id = (
            SELECT DISTINCT
            role_id
            FROM
            wx_t_role
            WHERE
            ch_role_name = 'tmm'
            )
            GROUP BY
            wto.creator
            ) tt_tmm ON tt_tmm.creator = cd.dsr_id

        </if>
        WHERE
        cd.dsr_id IS NOT NULL
        AND cd.kpi_year = #{kpiYear}
        AND cd.kpi_type != 'settingReward'
        AND cd.reserved_field2 = '1'
        <if test="tmmUser != null">
            <if test="tmmUser">
                AND	ur.role_id = (
                SELECT DISTINCT
                role_id
                FROM
                wx_t_role
                WHERE
                ch_role_name = 'tmm'
                )
            </if>
            <if test="!tmmUser">
                AND NOT EXISTS (
                SELECT
                1
                FROM
                wx_t_userrole ur
                LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
                WHERE
                ur.user_id = cd.dsr_id
                AND r.ch_role_name = 'tmm'
                )
            </if>
        </if>
        <if test="dsrName != null and dsrName != ''">
            AND u.ch_name like '%'+ #{dsrName} +'%'
        </if>
        <if test="customerNameCn != null and customerNameCn != ''">
            AND crsc.customer_name_cn like '%'+ #{customerNameCn} +'%'
        </if>
        <if test="regionName != null and regionName != ''">
            AND crsc.region_name like '%'+ #{regionName} +'%'
        </if>
        <if test="salesName != null and salesName != ''">
            AND crsc.sales_name like '%'+ #{salesName} +'%'
        </if>
        <if test="suppervisorName != null and suppervisorName != ''">
            AND crsc.suppervisor_name like '%'+ #{suppervisorName} +'%'
        </if>
        <if test="partnerId != null and partnerId != ''">
            AND cd.org_id = #{partnerId}
        </if>
        <if test=" salesCai != null and  salesCai != ''">
            AND crsc.sales_cai = #{salesCai}
        </if>
        <if test="suppervisorCai != null and suppervisorCai != ''">
            AND crsc.suppervisor_cai = #{suppervisorCai}
        </if>
        <if test="channelManagerCai != null and channelManagerCai != ''">
            AND crsc.channel_manager_cai = #{channelManagerCai}
        </if>
        <if test="buManagerCai != null and buManagerCai != ''">
            AND crsc.bu_manager_cai = #{buManagerCai}
        </if>
    </select>


	<select id="getPartnerNamList" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo" resultType="java.lang.String">
		SELECT DISTINCT
			crsc.customer_name_cn
		FROM
			wx_t_cdm_kpi_info cd
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = cd.org_id
		LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 1 > 0
		LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
		WHERE 1 = 1
		AND cd.reserved_field2 = '1'
		<if test="customerNameCn != null and customerNameCn != ''">
			AND crsc.customer_name_cn like '%'+ #{customerNameCn} +'%'
		</if>
		<if test="partnerId != null and partnerId != ''">
			AND cd.org_id = #{partnerId}
		</if>
		<if test="teamLeaderCai != null and teamLeaderCai != ''">
			and dp_001_sr1.sales_cai_level LIKE '%_' + #{teamLeaderCai} + '%'
		</if>
		<if test="suppervisorCai != null and suppervisorCai != ''">
			AND crsc.suppervisor_cai = #{suppervisorCai}
		</if>
		<if test=" salesCai != null and  salesCai != ''">
			AND crsc.sales_cai = #{salesCai}
		</if>
		<if test="channelManagerCai != null and channelManagerCai != ''">
			AND crsc.channel_manager_cai = #{channelManagerCai}
		</if>
		<if test="buManagerCai != null and buManagerCai != ''">
			AND crsc.bu_manager_cai = #{buManagerCai}
		</if>
	</select>
	
	
	<resultMap id="BaseIntegralByKpiTypeMap" type="com.chevron.dsrkpi.model.CdmKpiIntegralHisInfoVo">
		<result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
		<result column="ch_name" property="dsrName" jdbcType="VARCHAR"/>
		<result column="org_id" property="orgId" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="kpi_year" property="kpiYear" jdbcType="BIGINT"/>
		<result column="kpi_value" property="kpiValue" jdbcType="BIGINT"/>
		<result column="kpi_type" property="kpiType" jdbcType="VARCHAR"/>
		<result column="total_points" property="totalPoints" jdbcType="NUMERIC"/>
		<result column="remaining_points" property="remainingPoints" jdbcType="NUMERIC"/>
		<result column="year_awards" property="yearAwards" jdbcType="NUMERIC"/>
		<result column="integrals" property="integrals" jdbcType="NUMERIC"/>
		<result column="mon" property="mon" jdbcType="BIGINT"/>
		<result column="finish_date" property="finishDate" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	
	<select id="getCdmKpiVisitFinishList" resultMap="BaseIntegralByKpiTypeMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo">
		SELECT
			COUNT (t2.org_id) kpi_value,
			t2.exec_user dsr_id,CONVERT (VARCHAR(10), t2.xg_sj, 120) finish_date
		FROM
			(
				SELECT
					tt.*, ROW_NUMBER () OVER (
						partition BY tt.org_id,tt.exec_user,CONVERT (VARCHAR(7), tt.xg_sj, 23)
						ORDER BY
							tt.xg_sj
					) rowNum
				FROM
					(
						SELECT DISTINCT
							s.exec_user,
							s.org_id,
							CONVERT (VARCHAR(10), s.xg_sj, 23) xg_sj
						FROM
							wx_task_main w
						LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
						LEFT JOIN wx_t_cdm_kpi_info ck ON s.exec_user = ck.dsr_id
						LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
						WHERE
							w.tmb_type_code = 'TT_2_XD'
						AND ck.reserved_field2 = '1'
						AND s.xg_sj &gt;= '2020-04-01 00:00:00.000'
						<if test="startDate != null and endDate != ''">
							AND s.xg_sj &gt;= '${startDate}'
						</if>
						<if test="endDate != null and endDate != ''">
							AND s.xg_sj &lt;= '${endDate}'
						</if>
						AND ws.status != - 3
						AND s.task_status = 4
						<if test="orgId != null and orgId != ''">
							AND ck.org_id = #{orgId}
						</if>
						<if test="dsrId != null and dsrId != ''">
							AND ck.dsr_id = #{dsrId}
						</if>
						<if test="!tmmUser">
							AND NOT EXISTS (
							SELECT
								1
							FROM
								wx_t_userrole ur
							LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
							WHERE
								ur.user_id = ck.dsr_id
							AND r.ch_role_name = 'tmm')
						</if>
						<if test="tmmUser">
							AND  EXISTS (
							SELECT
								1
							FROM
								wx_t_userrole ur
							LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
							WHERE
								ur.user_id = ck.dsr_id
							AND r.ch_role_name = 'tmm')
						</if>
					) tt
			) t2
		WHERE
			t2.rowNum &lt;= (
				SELECT DISTINCT
					code
				FROM
					wx_t_properties
				WHERE
					codetype = 'cdm_mon_air_days'
			)
		GROUP BY
			t2.xg_sj,
			t2.exec_user
	</select>
	
	<select id="getCdmKpiEntryFinishList" resultMap="BaseIntegralByKpiTypeMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo">
		SELECT
			SUM (factEntryNum) kpi_value,
			creator dsr_id,
			CONVERT (
						VARCHAR (10),
						ext_property22,
						23
					) finish_date
		FROM
			(
				SELECT
					COUNT (DISTINCT ws.id) factEntryNum,
					ws.creator,
					CONVERT (
						VARCHAR (10),
						ws.ext_property22,
						23
					) ext_property22
				FROM
					wx_t_work_shop ws
				LEFT JOIN wx_task_sub s ON s.org_id = ws.id
				LEFT JOIN wx_task_main w ON s.task_main_id = w.task_main_id
				LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = ws.creator
				WHERE
					1 = 1
				AND w.tmb_type_code = 'TT_2_SD'
				AND cd.reserved_field2 = '1'
				AND ws.ext_property22 &gt;= '2020-04-01 00:00:00.000'
				<if test="startDate != null and startDate != ''">
					AND ws.ext_property22 &gt;= '${startDate}'
				</if>
				<if test="endDate != null and endDate != ''">
					AND ws.ext_property22 &lt; '${endDate}'
				</if>
				
				AND ws.ext_flag &amp; 256 &gt; 0
				<if test="orgId != null and orgId != ''">
					AND cd.org_id = #{orgId}
				</if>
				<if test="dsrId != null and dsrId != ''">
					AND cd.dsr_id = #{dsrId}
				</if>
				<if test="!tmmUser">
					AND NOT EXISTS (
					SELECT
						1
					FROM
						wx_t_userrole ur
					LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
					WHERE
						ur.user_id = cd.dsr_id
					AND r.ch_role_name = 'tmm')
				</if>
				<if test="tmmUser">
						AND EXISTS (
					SELECT
						1
					FROM
						wx_t_userrole ur
					LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
					WHERE
						ur.user_id = cd.dsr_id
					AND r.ch_role_name = 'tmm')
				</if>
				GROUP BY
					ws.creator,
					ws.ext_property22
			) tt
		GROUP BY
			tt.creator,
			tt.ext_property22
	</select>
	
	<select id="getCdmKpiSalesFinishList" resultMap="BaseIntegralByKpiTypeMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo">
		SELECT
			tt.creator dsr_id,
			SUM (tt.saleNum) kpi_value,
			CONVERT (
						VARCHAR (10),
						tt.update_time,
						23
					)
			  finish_date
		FROM
			(
				SELECT
					wto.creator,
					<!-- SUM (wol.amount * convert(float, tp.capacity)) -->
					SUM (case when t.org_id is not null and t1.org_id is not null then wol.amount * convert(float, tp.capacity)
					when t.org_id is null then wol.amount * convert(float, tp.capacity)
					else 0 end) saleNum,
					CONVERT (
						VARCHAR (10),
						wto.update_time,
						23
					) update_time
				FROM
					wx_t_order wto
				LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
				LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
				LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
				LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = wto.creator AND cd.kpi_type = 'settingSales'
				LEFT JOIN wx_t_user u ON u.user_id = wto.creator
				left join (select DISTINCT org_id from wx_t_cdm_kpi_sellIn_product_code) t on u.org_id  = t.org_id
				left join wx_t_cdm_kpi_sellIn_product_code t1 on u.org_id  = t1.org_id and t1.product_code = tp.sku
				WHERE 1 = 1 
				AND cd.reserved_field2 = '1'
				AND wto.create_time &gt;= '2020-04-01 00:00:00.000'
				<if test="startDate != null and startDate != ''">
					AND wto.create_time &gt;= '${startDate}'
				</if>
				<if test="endDate != null and endDate != ''">
					AND wto.create_time &lt; '${endDate}'
				</if>
				AND (
					wto.status = 11
					OR wto.partner_confirm_type = 1
				)
				<if test="dsrId != null and dsrId != ''">
					AND cd.dsr_id = #{dsrId}
				</if>
				<if test="orgId != null and orgId != ''">
					AND cd.org_id = #{orgId}
				</if>
				<if test="!tmmUser">
					AND NOT EXISTS (
						SELECT
							1
						FROM
							wx_t_userrole ur
						LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
						WHERE
							ur.user_id = cd.dsr_id
						AND r.ch_role_name = 'tmm')
				</if>
				<if test="tmmUser">
					AND  EXISTS (
						SELECT
							1
						FROM
							wx_t_userrole ur
						LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
						WHERE
							ur.user_id = cd.dsr_id
						AND r.ch_role_name = 'tmm')
				</if>
				GROUP BY
					wto.creator,
					wto.update_time
			) tt
		GROUP BY
			tt.creator,
			tt.update_time
	</select>

	
	
	<resultMap type="com.chevron.dsrkpi.model.BaselineTargetAct" id="BaselineTargetActResultMap">
		<result column="customer_name_cn" property="customerNameCn" jdbcType="BIGINT"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="year" property="year" jdbcType="BIGINT"/>
		<result column="quarter" property="quarter" jdbcType="BIGINT"/>
		<result column="month" property="month" jdbcType="VARCHAR"/>
		<result column="product_channel" property="productChannel" jdbcType="BIGINT"/>
		<result column="type" property="type" jdbcType="BIGINT"/>
		<result column="act_liters" property="actLiters" jdbcType="BIGINT"/>
		<result column="target_liters" property="targetLiters" jdbcType="BIGINT"/>
	</resultMap>
	
	<select id="getBaselineTargetActList" resultMap="BaselineTargetActResultMap" parameterType="map">
		SELECT
			distributor_id,
			type,
			SUM (act_liters) act_liters,
			SUM (target_liters) target_liters
		FROM
			dw_view_baseline_target_act
		where 1 =1 
		<if test="distributorId != null and distributorId != ''">
			AND distributor_id = #{distributorId}
		</if>
		<if test="year != null and year != ''">
			AND year = #{year}
		</if>
		<if test="quarter != null and quarter != ''">
			AND quarter = #{quarter}
		</if>
		<if test="month != null and month != ''">
			AND month &lt;= #{month}
		</if>
		<if test="productChannel != null and productChannel != ''">
			AND product_channel = #{productChannel}
		</if>
		<if test="type != null and type != ''">
			AND type = #{type}
		</if>
        <if test="null != partners and partners.size > 0">
            AND distributor_id in
            <foreach collection="partners" index="index" item="item" open="(" separator="," close=")">
                 #{item}
            </foreach>
        </if>
		GROUP BY distributor_id,type
	</select>

	<select id="selecExcelExportToEmailsByYearMonth"  resultType="com.chevron.dsrkpi.model.ExcelExportToEmail4Cdm" parameterType="map">
	SELECT
		case when a.kpi_type = 'settingEntry' then 'DSR录入新客户数(家)'
		when a.kpi_type = 'settingSales' then '已确认销量(KL)'
		when a.kpi_type = 'settingVisit' then 'DSR拜访客户数(家)' end "kpiName",
		a.customer_name_cn "organizationName",
		a.ch_name "dsrName",
		case when a.user_type = 1 then 'DSR' 
		 when a.user_type = 0 then 'TMM'  end "kpiCode",
		a.total_points "allPoint",
		a.remaining_points "leftPoint",
		a.kpi_value "kpiValue",
		a.integral "awardPoint"
	FROM
		(
	SELECT DISTINCT
		its.kpi_type,
		crsc.customer_name_cn,
		u.ch_name,
	CASE

		WHEN EXISTS (
	SELECT
	1
	FROM
	wx_t_userrole ur
	LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
	WHERE
	ur.user_id = its.dsr_id
	AND r.ch_role_name = 'tmm'
	) THEN
			0 ELSE 1 
			END user_type,
		cd.total_points,
		cd.remaining_points,
		its.kpi_value,
		its.integral 
	FROM
		wx_t_cdm_kpi_integral_his_info its
		LEFT JOIN wx_t_cdm_kpi_info cd ON its.dsr_id = cd.dsr_id 
		AND cd.org_id = its.partner_id
		LEFT JOIN view_customer_region_sales_channel crsc ON its.distributor_id = crsc.distributor_id
		LEFT JOIN wx_t_user u ON u.user_id = cd.dsr_id 
	WHERE
		1 = 1 
		AND effective_time = #{yearMonth,jdbcType=TIMESTAMP}
		AND cd.org_id = #{orgId,jdbcType=BIGINT}
		AND its.kpi_type not in ('SNAbove','SellIn')
		) a 
	WHERE
		a.user_type = 1 
	ORDER BY
	a.ch_name
	</select>
		<select id="selecSellinByYearMonth"  resultType="com.chevron.dsrkpi.model.ExcelExportToEmail4Cdm" parameterType="map">
	SELECT
		case WHEN a.kpi_type = 'SNAbove' THEN 'SN+销量(L)' 
			WHEN a.kpi_type = 'SellIn' THEN 'Sell-In销量(L)' end "kpiName",
		a.customer_name_cn "organizationName",
		'' "dsrName",
		'' "kpiCode",
		0 "allPoint",
		0 "leftPoint",
		a.defaults_value1 "kpiValue",
		0 "awardPoint"
	FROM
		(
	SELECT DISTINCT
		its.kpi_type,
		crsc.customer_name_cn,
		u.ch_name,
	CASE
		
		WHEN EXISTS (
	SELECT
	1
	FROM
	wx_t_userrole ur
	LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
	WHERE
	ur.user_id = its.dsr_id
	AND r.ch_role_name = 'tmm'
	) THEN
			0 ELSE 1 
			END user_type,
		cd.total_points,
		cd.remaining_points,
		its.defaults_value1,
		its.integral 
	FROM
		wx_t_cdm_kpi_integral_his_info its
		LEFT JOIN wx_t_cdm_kpi_info cd ON its.dsr_id = cd.dsr_id 
		AND cd.org_id = its.partner_id
		LEFT JOIN view_customer_region_sales_channel crsc ON its.distributor_id = crsc.distributor_id
		LEFT JOIN wx_t_user u ON u.user_id = cd.dsr_id 
	WHERE
		1 = 1 
		AND effective_time = #{yearMonth,jdbcType=TIMESTAMP}
		AND cd.org_id = #{orgId,jdbcType=BIGINT}
		AND its.kpi_type in ('SNAbove','SellIn')
		) a 
	WHERE
		a.user_type = 1 
	</select>
	<select id="getToList" resultType="com.sys.auth.model.WxTUser" parameterType="map">
		SELECT
			u.*
		FROM
			view_customer_region_sales_channel v
			LEFT JOIN wx_t_partner_o2o_enterprise poe ON v.distributor_id = poe.distributor_id 
			left join wx_t_user u on sales_cai = u.cai
		WHERE
			poe.partner_id = #{orgId,jdbcType=BIGINT}
			AND channel_weight &amp; 1 &gt; 0
		union 
		SELECT
			u.*
		FROM
			view_customer_region_sales_channel v
			LEFT JOIN wx_t_partner_o2o_enterprise poe ON v.distributor_id = poe.distributor_id 
			left join wx_t_user u on suppervisor_cai = u.cai
		WHERE
			poe.partner_id = #{orgId,jdbcType=BIGINT}
			AND channel_weight &amp; 1 &gt; 0
		union
		select xx_u001.* from wx_t_role xx_r001
		   left join wx_t_userrole xx_ur001 on xx_ur001.role_id=xx_r001.role_id
		   left join wx_t_user xx_u001 on xx_u001.user_id=xx_ur001.user_id
		  where xx_r001.ch_role_name='Service_Partner_Manager' and xx_u001.org_id=#{orgId,jdbcType=BIGINT}
		  and (xx_u001.type is null or xx_u001.type != '1') and xx_u001.status != 0
	</select>
	
	<resultMap id="DicBaseResultMap" type="com.chevron.dsrkpi.model.DicItemVo" >
	    <id column="id" property="id" jdbcType="BIGINT" />
	    <result column="dic_type_code" property="dicTypeCode" jdbcType="NVARCHAR" />
	    <result column="dic_item_code" property="dicItemCode" jdbcType="NVARCHAR" />
	    <result column="dic_item_name" property="dicItemName" jdbcType="NVARCHAR" />
	    <result column="dic_item_desc" property="dicItemDesc" jdbcType="NVARCHAR" />
	    <result column="status" property="status" jdbcType="NVARCHAR" />
	    <result column="creator" property="creator" jdbcType="BIGINT" />
	    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
	    <result column="updator" property="updator" jdbcType="BIGINT" />
	    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	    <result column="sort_numb" property="sortNumb" jdbcType="NUMERIC" />
	</resultMap>
  
  <sql id="Dit_Base_Column_List" >
    id, dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, creator, 
    create_time, updator, update_time, sort_numb
  </sql>
	<select id="queryDicItemByParams" resultMap="DicBaseResultMap" parameterType="map">
		     select 
	    <include refid="Dit_Base_Column_List" />
	    from wx_t_dic_item
	    where dic_type_code = #{dicTypeCode,jdbcType=NVARCHAR} and status='1'
	    <if test="includeChannels !=null ">
	            and dic_item_desc&amp;#{includeChannels, jdbcType=INTEGER}>0
	    </if>
	    <if test="dicItemCode !=null and dicItemCode != ''">
	           and dic_item_code = #{dicItemCode,jdbcType=NVARCHAR}
	    </if>
	    order by sort_numb asc
	</select>

    <select id="getPartnerTargetActList" resultMap="BaselineTargetActResultMap" parameterType="map">
        SELECT
        distributor_id,
        type,
        SUM (act_liters) act_liters,
        SUM (target_liters) target_liters
        FROM
        dw_view_baseline_target_act
        where product_channel = 'Consumer' and type = 'Total' and quarter = #{quarter} and year = #{year}
        GROUP BY distributor_id,type
    </select>
	
	
	<resultMap id="pointsInfoMap" type="com.chevron.dsrkpi.model.app.AppPointsInfo" >
	    <result column="kpi_type" property="kpiType" jdbcType="NVARCHAR" />
	    <result column="points" property="points" jdbcType="NUMERIC" />
	</resultMap>
	
	<select id="getAppPoints" resultMap="pointsInfoMap">
		SELECT
			SUM (integral) points,
			kpi_type
		FROM
			wx_t_cdm_kpi_integral_his_info
		WHERE
			dsr_id = #{dsrId}
		GROUP BY
			kpi_type
	</select>
	
	<resultMap type="com.chevron.dsrkpi.model.CdmKpiFinishInfoVo" id="CdmKpiFactMap">
		<result column="type" property="kpiType" jdbcType="NVARCHAR" />
	    <result column="factNum" property="factNum" jdbcType="NUMERIC" />
	    <result column="dsr_id" property="dsrId" jdbcType="BIGINT" />
	</resultMap>
	<select id="getCdmKpiFactFinishList" resultMap="CdmKpiFactMap" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo">
			SELECT
				'settingVisit' type,
				SUM (t.faceVisitNum) factNum,
				t.exec_user dsr_id
			FROM
				(	
				SELECT
					COUNT (t2.org_id) faceVisitNum,
					t2.exec_user
				FROM
					(
						SELECT
							tt.*, ROW_NUMBER () OVER (
								partition BY tt.org_id,tt.exec_user,CONVERT (VARCHAR(7), tt.xg_sj, 23)
								ORDER BY
									tt.xg_sj
							) rowNum
						FROM
							(
								SELECT DISTINCT
									s.exec_user,
									s.org_id,
									CONVERT (VARCHAR(10), s.xg_sj, 23) xg_sj
								FROM
									wx_task_main w
								LEFT JOIN wx_task_sub s ON s.task_main_id = w.task_main_id
								LEFT JOIN wx_t_cdm_kpi_info ck ON s.exec_user = ck.dsr_id
								LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
								WHERE
									w.tmb_type_code = 'TT_2_XD'
								AND ck.reserved_field2 = '1'
								AND s.xg_sj &gt;= '${cdmKpiInfoVo.startDate}'
								AND s.xg_sj &lt; '${cdmKpiInfoVo.endDate}'
								AND s.xg_sj &gt;= '2020-04-01 00:00:00.000'
								AND ws.status != - 3
								AND s.task_status = 4
							) tt
					) t2
				WHERE
					t2.rowNum &lt;= (
						SELECT DISTINCT
							code
						FROM
							wx_t_properties
						WHERE
							codetype = 'cdm_mon_air_days'
					)
				GROUP BY
					t2.exec_user
				UNION ALL
				SELECT
					SUM (kpi_value) faceVisitNum,
					dsr_id exec_user
				FROM
					wx_t_cdm_kpi_integral_his_info cd
				WHERE
					cd.kpi_type = 'settingVisit'
				AND cd.effective_time &lt; '${cdmKpiInfoVo.startDate}'
				AND cd.kpi_year = #{cdmKpiInfoVo.kpiYear}
				AND cd.effective_time &gt;= '2020-04-01 00:00:00.000'
				GROUP BY cd.dsr_id
			) t GROUP BY t.exec_user
			UNION  ALL
			SELECT
				'settingEntry' type,
				SUM(t.factEntryNum) factNum, t.creator  dsr_id
			FROM
			(
			SELECT
				COUNT (DISTINCT ws.id) factEntryNum,
				ws.creator
			FROM
				wx_t_work_shop ws
			LEFT JOIN wx_task_sub s ON s.org_id = ws.id
			LEFT JOIN wx_t_cdm_kpi_info ck ON s.exec_user = ck.dsr_id
			LEFT JOIN wx_task_main w ON s.task_main_id = w.task_main_id
			WHERE
				1 = 1
			AND w.tmb_type_code = 'TT_2_SD'
			AND ck.reserved_field2 = '1'
			AND ws.ext_property22 &gt;= '${cdmKpiInfoVo.startDate}'
			AND ws.ext_property22 &lt; '${cdmKpiInfoVo.endDate}'
			AND ws.ext_property22 &gt;= '2020-04-01 00:00:00.000'
			AND ws.ext_flag &amp; 256 &gt; 0
			GROUP BY
				ws.creator
			UNION ALL
			SELECT
				SUM (kpi_value) factEntryNum,
				dsr_id creator
			FROM
				wx_t_cdm_kpi_integral_his_info cd
			WHERE
				cd.kpi_type = 'settingEntry'
			AND cd.effective_time &lt; '${cdmKpiInfoVo.startDate}'
			AND cd.kpi_year = #{cdmKpiInfoVo.kpiYear}
			AND cd.effective_time  &gt;= '2020-04-01 00:00:00.000'
			GROUP BY
				cd.dsr_id
			) t GROUP BY t.creator
			UNION ALL
			SELECT  'settingSales' type, SUM (t.saleNum) factNum, t.creator dsr_id	
			FROM
				(
				SELECT
					wto.creator,
					<!-- SUM (wol.amount * convert(float, tp.capacity)) -->
					SUM (case when t.org_id is not null and t1.org_id is not null then wol.amount * convert(float, tp.capacity)
					when t.org_id is null then wol.amount * convert(float, tp.capacity)
					else 0 end) saleNum
				FROM
					wx_t_order wto
				LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
				LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = wto.creator AND cd.kpi_type = 'settingSales'
				LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
				LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
				LEFT JOIN wx_t_user u ON u.user_id = wto.creator
				left join (select DISTINCT org_id from wx_t_cdm_kpi_sellIn_product_code) t on u.org_id  = t.org_id
				left join wx_t_cdm_kpi_sellIn_product_code t1 on u.org_id  = t1.org_id and t1.product_code = tp.sku
				WHERE
					wto.create_time &gt;= '${cdmKpiInfoVo.startDate}'
				AND cd.reserved_field2 = '1'
				AND wto.create_time &lt; '${cdmKpiInfoVo.endDate}'
				AND wto.create_time &gt;= '2020-04-01 00:00:00.000'
				AND (wto.status = 11 OR  wto.partner_confirm_type = 1)
				GROUP BY
					wto.creator
				UNION ALL
				SELECT
					dsr_id creator,
					SUM (kpi_value) saleNum
				FROM
					wx_t_cdm_kpi_integral_his_info cd
				WHERE
					cd.kpi_type = 'settingSales'
				AND cd.effective_time &lt; '${cdmKpiInfoVo.startDate}'
				AND cd.kpi_year = #{cdmKpiInfoVo.kpiYear}
				AND cd.effective_time  &gt;= '2020-04-01 00:00:00.000'
				GROUP BY cd.dsr_id
				) t
			GROUP BY
				t.creator
	</select>

	<select id="selectTmmByUserId" resultType="Long">
		SELECT top 1 id FROM wx_t_tmm_info where dsr_id = #{dsrId, jdbcType=BIGINT }
	</select>
	

    <!-- 结果集映射 -->
    <resultMap id="wxTTmmInfoBaseResultMap" type="com.chevron.dsrkpi.model.TMMKpiInfo">
        <result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
        <result column="from_source" property="fromSource" jdbcType="INTEGER"/>
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="performance_pay" property="performancePay" jdbcType="NUMERIC"/>
        <result column="entry_date" property="entryDate" jdbcType="TIMESTAMP"/>
        <result column="kpi_start_date" property="kpiStartDate" jdbcType="TIMESTAMP"/>
        <result column="year" property="year" jdbcType="VARCHAR"/>
        <result column="month" property="month" jdbcType="VARCHAR"/>
        <result column="kpi_value" property="kpiValue" jdbcType="BIGINT"/>
        <result column="kpi_type" property="kpiType" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getTMMEntryKpi" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo" resultMap="wxTTmmInfoBaseResultMap">
        SELECT 'settingTMMEntry'          kpi_type,
               COUNT(DISTINCT t_entry.id) kpi_value,
               creator                    dsr_id,
               year,
               month
        FROM (
                 SELECT ws.id,
                        ws.creator,
                        year(ws.ext_property22)  as year,
                        month(ws.ext_property22) as month
                 FROM wx_t_work_shop ws
                          LEFT JOIN wx_task_sub s ON s.org_id = ws.id
                          LEFT JOIN wx_t_cdm_kpi_info cd
                                    ON s.exec_user = cd.dsr_id AND cd.kpi_type = 'settingTMMEntry'
                          LEFT JOIN wx_task_main w ON s.task_main_id = w.task_main_id
                 WHERE 1 = 1
                   AND cd.del_flag = 0
                   AND cd.reserved_field6 = 1
                   AND w.tmb_type_code = 'TT_2_SD'
                   AND ws.ext_property22 >= #{startDate}
                   AND ws.ext_property22 <![CDATA[ < ]]> #{endDate}
                   AND ws.ext_flag <![CDATA[ & ]]> 256 > 0
                   AND exists (
                         SELECT
                             1
                         FROM
                             wx_t_tmm_info tmm
                         WHERE
                             ws.creator = tmm.dsr_id and tmm.from_source = 1
                        <if test="dsrIds != null and dsrIds.size > 0 ">
                            and tmm.dsr_id in
                            <foreach collection="dsrIds" item="item" open="(" close=")" separator="," >
                                #{item}
                            </foreach>
                        </if>
                     )
             ) t_entry
        GROUP BY t_entry.creator,
                 year,
                 month
    </select>

    <select id="getTMMOrderKpi" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo" resultMap="wxTTmmInfoBaseResultMap">
        SELECT 'settingTMMOrder'           kpi_type,
               COUNT(t_order.work_shop_id) kpi_value,
               t_order.creator             dsr_id,
               t_order.year,
               t_order.month
        FROM (
                 SELECT DISTINCT wto.work_shop_id,
                                 wto.creator,
                                 year(wto.partner_confirm_time)  as year,
                                 month(wto.partner_confirm_time) as month
                 FROM wx_t_order wto
                          LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = wto.creator
                     AND cd.kpi_type = 'settingTMMOrder'
                 WHERE 1 = 1
                   AND cd.del_flag = 0
                   AND cd.reserved_field6 = 1
                   AND wto.partner_confirm_time >= #{startDate}
                   AND wto.partner_confirm_time <![CDATA[ < ]]> #{endDate}
                   AND exists (
                         SELECT
                             1
                         FROM
                             wx_t_tmm_info tmm
                         WHERE
                             wto.creator = tmm.dsr_id and tmm.from_source = 1
                    <if test="dsrIds != null and dsrIds.size > 0 ">
                        and tmm.dsr_id in
                        <foreach collection="dsrIds" item="item" open="(" close=")" separator="," >
                            #{item}
                        </foreach>
                    </if>
                     )
             ) t_order
        GROUP BY t_order.creator,
                 t_order.year,
                 t_order.month
    </select>

    <select id="getTMMSalesKpi" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo" resultMap="wxTTmmInfoBaseResultMap">
        SELECT 'settingTMMSales' kpi_type,
               SUM(
                       t_sales.amount * CONVERT(FLOAT, t_sales.capacity)
                   )             kpi_value,
               t_sales.creator   dsr_id,
               t_sales.year,
               t_sales.month
        FROM (
                 SELECT DISTINCT  wto.id, wto.creator,
                        wol.amount,
                        tp.capacity,
                        year(wto.partner_confirm_time)  as year,
                        month(wto.partner_confirm_time) as month
                 FROM wx_t_order wto
                          LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
                          LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = wto.creator
                     AND cd.kpi_type = 'settingTMMSales'
                          LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
                          LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
                 WHERE 1 = 1
                   AND cd.del_flag = 0
                   AND cd.reserved_field6 = 1
                   AND wto.partner_confirm_time >= #{startDate}
                   AND wto.partner_confirm_time <![CDATA[ < ]]> #{endDate}
                   AND (
                     wto.status = 11 OR wto.partner_confirm_type = 1
                     )
                   AND exists (
                         SELECT
                             1
                         FROM
                             wx_t_tmm_info tmm
                         WHERE
                             wto.creator = tmm.dsr_id and tmm.from_source = 1
                        <if test="dsrIds != null and dsrIds.size > 0 ">
                            and tmm.dsr_id in
                            <foreach collection="dsrIds" item="item" open="(" close=")" separator="," >
                                #{item}
                            </foreach>
                        </if>
                     )
             ) t_sales
        GROUP BY creator,
                 t_sales.year,
                 t_sales.month
    </select>

    <select id="getTMMSNAboveKpi" parameterType="com.chevron.dsrkpi.model.CdmKpiInfoVo" resultMap="wxTTmmInfoBaseResultMap">
        SELECT 'settingTMMSNAbove' kpi_type,
               SUM(
                       t_sales.amount * CONVERT(FLOAT, t_sales.capacity)
                   )             kpi_value,
               t_sales.creator   dsr_id,
               t_sales.year,
               t_sales.month
        FROM (
                 SELECT DISTINCT wto.id ,wto.creator,
                        wol.amount,
                        tp.capacity,
                        year(wto.partner_confirm_time)  as year,
                        month(wto.partner_confirm_time) as month
                 FROM wx_t_order wto
                          LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
                          LEFT JOIN wx_t_cdm_kpi_info cd ON cd.dsr_id = wto.creator
                     AND cd.kpi_type = 'settingTMMSNAbove'
                          LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
                          LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
                 WHERE 1 = 1
                   AND cd.del_flag = 0
                   AND tp.product_property = 'SN Above'
                   AND cd.reserved_field6 = 1
                   AND wto.partner_confirm_time >= #{startDate}
                   AND wto.partner_confirm_time <![CDATA[ < ]]> #{endDate}
                   AND (
                     wto.status = 11 OR wto.partner_confirm_type = 1
                     )
                   AND exists (
                         SELECT
                             1
                         FROM
                             wx_t_tmm_info tmm
                         WHERE
                             wto.creator = tmm.dsr_id and tmm.from_source = 1
                    <if test="dsrIds != null and dsrIds.size > 0 ">
                        and tmm.dsr_id in
                        <foreach collection="dsrIds" item="item" open="(" close=")" separator="," >
                            #{item}
                        </foreach>
                    </if>
                     )
             ) t_sales
        GROUP BY creator,
                 t_sales.year,
                 t_sales.month
    </select>

    <select id="selectYearReward" resultMap="BaseResultMap">
        select dsr_id,max(year_awards) year_awards from wx_t_cdm_kpi_info
        where dsr_id is not null and reserved_field2 = 1 and kpi_year = #{year} group by dsr_id
    </select>
</mapper>
