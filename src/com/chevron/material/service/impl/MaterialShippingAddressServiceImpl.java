package com.chevron.material.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chevron.material.business.MaterialShippingAddressBizService;
import com.chevron.material.model.MaterialShippingAddress;
import com.chevron.material.model.MaterialShippingAddressExample;
import com.chevron.material.model.enums.AddressType;
import com.chevron.material.service.MaterialShippingAddressService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;

/**
 * 积分兑换个人地址信息表操作RPC接口实现
 *
 * <AUTHOR>
 * @version 1.0 2020-07-10 13:56
 */
@Service
public class MaterialShippingAddressServiceImpl implements MaterialShippingAddressService {

    @Autowired
    private MaterialShippingAddressBizService materialShippingAddressBizService;

    private final static Logger log = Logger.getLogger(MaterialShippingAddressServiceImpl.class);

    @Override
    public JsonResponse save(MaterialShippingAddress record) {
        JsonResponse map = new JsonResponse();
        log.info("save: " + JsonUtil.writeValue(record));
        Long userId = ContextUtil.getCurUserId();
        try {
            if (record.getId() == null) {
                if (record.getSourceType() == null) {
                    record.setUserId(userId);
                    record.setSourceType(AddressType.POINT_STORE.getType());
                    record.setSourceId(ContextUtil.getCurUserId());
                } else {
                    if (AddressType.getByType(record.getSourceType()) == null) {
                        map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                        map.setErrorMsg("地址类型错误~");
                        return map;
                    }
                }
                record.setCreateTime(new Date());
                record.setCreateUserId(userId);
                materialShippingAddressBizService.insert(record);
            } else {
            	record.setUpdateTime(new Date());
                record.setUpdateUserId(userId);
                materialShippingAddressBizService.update(record);
            }
            log.info("save success.");
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.material.service.impl.MaterialShippingAddressServiceImpl.save", JsonUtil.writeValue(record));
        }
        return map;
    }

    @Override
    public JsonResponse saveForEditPage(MaterialShippingAddress record) {
        JsonResponse map = new JsonResponse();
        log.info("saveForEditPage: " + JsonUtil.writeValue(record));
        try {
            if (record.getId() == null) {
                materialShippingAddressBizService.insert(record);
            } else {
                materialShippingAddressBizService.updateForEditPage(record);
            }
            log.info("saveForEditPage success.");
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.material.service.impl.MaterialShippingAddressServiceImpl.saveForEditPage", JsonUtil.writeValue(record));
        }
        return map;
    }

    @Override
    public JsonResponse delete(List<Long> ids) {
        JsonResponse map = new JsonResponse();
        log.info("delete: " + JsonUtil.writeValue(ids));
        try {
            materialShippingAddressBizService.delete(ids);
            log.info("delete success.");
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.material.service.impl.MaterialShippingAddressServiceImpl.delete", JsonUtil.writeValue(ids));
        }
        return map;
    }

    @Override
    public JsonResponse query(Long userId) {
        JsonResponse map = new JsonResponse();
        try {
            MaterialShippingAddressExample example = new MaterialShippingAddressExample();
            userId = userId == null ? ContextUtil.getCurUserId() : userId;
            example.createCriteria().andSourceIdEqualTo(userId).andSourceTypeEqualTo(AddressType.POINT_STORE.getType());
            List<MaterialShippingAddress> materialShippingAddresses = materialShippingAddressBizService.queryByExample(example);
            map.setListResult(materialShippingAddresses);
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public JsonResponse query(Long sourceId, String sourceType) {
        JsonResponse map = new JsonResponse();
        try {
            if (sourceId == null || StringUtils.isBlank(sourceType)) {
                map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                return map;
            }
            MaterialShippingAddressExample example = new MaterialShippingAddressExample();
            example.createCriteria().andSourceTypeEqualTo(sourceType).andSourceIdEqualTo(sourceId);
            List<MaterialShippingAddress> materialShippingAddresses = materialShippingAddressBizService.queryByExample(example);
            map.setListResult(materialShippingAddresses);
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            e.printStackTrace();
        }
        return map;
    }
}
