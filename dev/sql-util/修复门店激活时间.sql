insert into wx_log (log_type, operator, ext_property1, ext_property2, ext_property3, create_time)
select 'repair_workshop_active_time', 1,
datediff(second, '1970-01-01 08:00', ws.active_time), ws.workshop_id, 
datediff(second, '1970-01-01 08:00', w.activation_time), getdate() 

from wx_t_work_shop w 
left join 
(select min(ws1.create_time) active_time, ws1.workshop_id from wx_t_workshop_status ws1 where ws1.workshop_with_status=3 group by ws1.workshop_id) ws 
on ws.workshop_id=w.id 
--left join wx_t_workshop_status ws2 on ws2.workshop_with_status=3
where partner_id=56352 and w.activation_time!=ws.active_time
and w.activation_time>='2021-03-01'





--insert into wx_log (log_type, operator, ext_property1, ext_property2, ext_property3, create_time)
select 'repair_workshop_active_time', 1,
datediff(second, '1970-01-01 08:00', ws.active_time), ws.workshop_id, 
datediff(second, '1970-01-01 08:00', w.activation_time), getdate() 
--update ws2 set ws2.create_time=w.activation_time
from wx_t_work_shop w 
left join 
(select min(ws1.create_time) active_time, ws1.workshop_id from wx_t_workshop_status ws1 where ws1.workshop_with_status=3 group by ws1.workshop_id) ws 
on ws.workshop_id=w.id 
left join wx_t_workshop_status ws2 on ws2.workshop_with_status=3 and ws2.workshop_id=ws.workshop_id
where partner_id=56352 and w.activation_time!=ws.active_time
and w.activation_time>='2021-03-01'


select o.organization_name "经销商名称", w.work_shop_name "门店名称",ws.workshop_id,
ws.active_time "原激活时间", w.activation_time "新激活时间"
from wx_t_work_shop w 
left join 
(select min(ws1.create_time) active_time, ws1.workshop_id from wx_t_workshop_status ws1 where ws1.workshop_with_status=3 group by ws1.workshop_id) ws 
on ws.workshop_id=w.id 
--left join wx_t_workshop_status ws2 on ws2.workshop_with_status=3
left join wx_t_organization o on o.id=w.partner_id
where w.activation_time!=ws.active_time
and w.activation_time>='2021-01-01'
order by o.id

--insert into wx_t_workshop_status (create_time, workshop_id, workshop_with_status)
select ws.active_time, w.id, 2 workshop_with_status --into wx_t_workshop_status20210426
from wx_t_work_shop w 
left join 
(select min(ws1.create_time) active_time, ws1.workshop_id from wx_t_workshop_status ws1 where ws1.workshop_with_status=3 group by ws1.workshop_id) ws 
on ws.workshop_id=w.id 
left join wx_t_workshop_status ws2 on ws2.workshop_with_status=3 and ws2.workshop_id=ws.workshop_id
where w.ext_flag&256>0 and w.create_time>='2021-04-01'
and not exists (select 1 from wx_t_workshop_status ws1 where ws1.workshop_id=w.id and ws1.workshop_with_status=2)
