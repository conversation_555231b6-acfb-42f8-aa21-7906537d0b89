select dateadd(second, 1607246795, '1970-01-01 08:00')
select datediff(second, '1970-01-01 08:00', getdate())
CHARINDEX(',' + cast(t1.workflow_status as varchar) + ',', ',' + REPLACE(#{workflowStatus}, '|', ',') + ',')>0
select * from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
where module_code='EliteFund.award'

select l.* from wx_t_log l left join wx_t_user u on u.user_id=l.user_id where u.login_name='AWSZ' 
order by l.log_id desc;

select m.module_name, r.resource_name,r.resource_type_code from wx_t_resource r left join wx_t_resource_sql rs on r.resource_id=rs.resource_id 
left join wx_t_module m on m.module_id=r.module_id
where rs.sqlid like 'com.chevron.partnerorder.dao.PartnerOrderVoMapper.getPartnerOrdersByCondition%'


select * from wx_t_workshop_business_line
ALTER TABLE [wx_t_in_stock] ALTER COLUMN [stock_from_address] [nvarchar](500) NULL
--product.nosync 产品-不需要同步数据
select * from dbo.task_schedule_job

DBCC CHECKIDENT ('wx_t_qr_code_detail',reseed,100017120139)
--清除3个月前发红包日志
delete from wx_log where log_type='RedPacketsJobLog' and DATEDIFF(month, create_time, getdate())>3

'10000'+SUBSTRING('"&A1018&"',6, 7) 
code_id=(*********** + convert(int,SUBSTRING('"&A1&"',5,8)))
--产品qr标记设置	
select * from wx_t_product;
update wx_t_product set is_qrcode=1 where id=29;
--修改技师门店
--update wx_t_workshop_employee set name='富驰', workshop_id=73567, workshop_name='富驰汽车维修' where mobile='***********'
select * from wx_t_mechanic_qrcode where mechanic_code='M201701161616027597';
--update wx_t_mechanic_qrcode set workshop_id=73567 where mechanic_code='M201701161616027597';

  	 SELECT we.name "员工姓名", we.workshop_name "所属门店", case when we.employee_type='Owner' then '店主' else '技师' end "员工类型", 
	 we.mobile "手机", isnull((select sum(v.capacity) from wx_v_oil_verification v where v.mechanic_code = we.code and v.workshop_id=we.workshop_id), 0) "扫码升数(L)", 
	 we.creation_time "录入时间", u.ch_name as "录入的销售人员"
      FROM wx_t_workshop_employee we
      LEFT JOIN wx_t_user u on u.user_id = we.creator
      LEFT JOIN wx_t_workshop_partner  t_w_p ON we.workshop_id = t_w_p.workshop_id
	  where t_w_p.partner_id=7
	  order by we.workshop_id, we.id desc
--技师店长
SELECT distinct o.organization_name "合伙人名称", o.id "SP ID", ws.work_shop_name "门店名称", we.workshop_id "门店ID", 
we.name "员工姓名", we.code "员工ID",
 case when we.employee_type='Owner' then '店主' else '技师' end "员工类型", 
	 we.mobile "手机"--,we.creation_time "录入时间", u.ch_name as "录入的销售人员"
      FROM wx_t_workshop_employee we
      LEFT JOIN wx_t_user u on u.user_id = we.creator
      LEFT JOIN wx_t_workshop_partner  t_w_p ON we.workshop_id = t_w_p.workshop_id
	  left join wx_t_organization o on o.id=t_w_p.partner_id
	  left join wx_t_work_shop ws on ws.id=we.workshop_id
	  where t_w_p.partner_id=7
	  order by we.workshop_id, we.id desc
	  
--巡店任务照片导出
	select a.organization_name "合伙人名称", a.work_shop_name "门店名称", isnull(a.hjps, '') "环境拍摄照片", isnull(a.cxpjc, '') "促销品检查照片" from (
select o1.id id1, t.id, o1.organization_name, t.work_shop_name, (select substring((
 SELECT ','+cast(af1.storage_name as varchar(90)) FROM wx_att_file af1 
join wx_task_sub ts1 on ts1.org_id=t.id
 join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id and tic1.check_id=23041850 
 where exists (select 1 from wx_task_main tm1 where tm1.create_time >= '2017-02-01 00:00:00.000' and tm1.task_main_id=ts1.task_main_id and tm1.tmb_type_code='TT_2_XD')
and af1.source_type=3 and af1.source_id=tic1.id  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) hjps, 
 (select substring((
 SELECT ','+cast(af2.storage_name as varchar(90)) FROM wx_att_file af2
join wx_task_sub ts1 on ts1.org_id=t.id
 join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id and tic2.check_id=23061847 
 where exists (select 1 from wx_task_main tm1 where tm1.create_time >= '2017-02-01 00:00:00.000' and tm1.task_main_id=ts1.task_main_id and tm1.tmb_type_code='TT_2_XD')
and af2.source_type=3 and af2.source_id=tic2.id  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) cxpjc
from wx_t_work_shop t
join wx_t_workshop_partner wp1 on wp1.workshop_id=t.id
join wx_t_organization o1 on wp1.partner_id=o1.id
--where t.status='3'
) a where a.id1 in (22956) and 
(a.hjps is not null or a.cxpjc is not null)
order by a.id1, a.id


	select a.organization_name "合伙人名称", a.work_shop_name "门店名称", isnull(a.hjps, '') "环境拍摄照片", isnull(a.cxpjc, '') "促销品检查照片" from (
select o1.id id1, t.id, o1.organization_name, t.work_shop_name, (select substring((
 SELECT ','+cast(af1.storage_name as varchar(90)) FROM wx_att_file af1 
join wx_task_sub ts1 on ts1.org_id=t.id
 join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id and tic1.check_id=23041850 
 where exists (select 1 from wx_task_main tm1 where tm1.create_time >= '2017-02-01 00:00:00.000' and tm1.task_main_id=ts1.task_main_id and tm1.tmb_type_code='TT_2_XD')
and af1.source_type=3 and af1.source_id=tic1.id  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) hjps, 
 (select substring((
 SELECT ','+cast(af2.storage_name as varchar(90)) FROM wx_att_file af2
join wx_task_sub ts1 on ts1.org_id=t.id
 join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id and tic2.check_id=23061847 
 where exists (select 1 from wx_task_main tm1 where tm1.create_time >= '2017-02-01 00:00:00.000' and tm1.task_main_id=ts1.task_main_id and tm1.tmb_type_code='TT_2_XD')
and af2.source_type=3 and af2.source_id=tic2.id  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) cxpjc
from wx_t_work_shop t
join wx_t_workshop_partner wp1 on wp1.workshop_id=t.id
join wx_t_organization o1 on wp1.partner_id=o1.id
where 
 t.status='3'
) a where a.id1 in (33455,44946) and 
(a.hjps is not null or a.cxpjc is not null)
order by a.id1, a.id

--门头照导出
select o1.organization_name, t.work_shop_name, t.id, af1.storage_name,(select substring((
 SELECT ','+cast(af2.storage_name as varchar(90)) FROM wx_att_file af2
join wx_task_sub ts1 on ts1.org_id=t.id
 join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id and tic2.check_id=23061844 
 where exists (select 1 from wx_task_main tm1 where tm1.task_main_id=ts1.task_main_id and tm1.tmb_type_code='TT_2_SD')
and af2.source_type=3 and af2.source_id=tic2.id  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) mpz
from wx_t_work_shop t
join wx_t_workshop_partner wp1 on wp1.workshop_id=t.id
join wx_t_organization o1 on wp1.partner_id=o1.id
join wx_att_file af1 on t.photo_id=af1.uuid
where t.status='3'

--扫码升数不小于40的激活门店
 select l2.organization_name "合伙人名称", t.work_shop_name "门店名称"--, t.id--, count(1) 
, t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型"--,t.longitude, t.latitude
,(select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-02-01 00:00:00.000' and a3.creation_time<'2017-03-01 00:00:00.000') "扫码升数(L)"--,t.remark--, we.name,
--case when we.employee_type='Owner' then '店长' else '技师' end empt, we.mobile,
from wx_t_work_shop t --left join wx_t_workshop_employee we on t.id=we.workshop_id 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=t.id
	join wx_t_organization l2 on l1.partner_id=l2.id
	where --t.status='3' and t.active_fee_flag=1 
	--and l2.id=7--and we.id is null 
	--and exists (select 1 from wx_t_workshop_status ws1 where t.id=ws1.workshop_id and ws1.workshop_with_status='3' and ws1.create_time>='2017-01-01 00:00:00.000' and ws1.create_time<'2017-02-01 00:00:00.000')
	--and t.update_time>='2017-01-01 00:00:00.000' and t.update_time<'2017-02-01 00:00:00.000'
--and 
 (select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-02-01 00:00:00.000' and a3.creation_time<'2017-03-01 00:00:00.000')>=40
	--and exists (select 1 from wx_t_workshop_partner l1 where l1.partner_id=22955 and l1.workshop_id=t.id)
	--group by t.id
	order by l2.id,t.id
--门店激活导出
select distinct t3.id, t2.id, t1.id, l2.id "合伙人ID", l2.organization_name "合伙人名称", t.id "门店ID", t.work_shop_name "门店名称", 
t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型", 
(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t.id order by wss.create_time desc) "激活时间"
from wx_t_work_shop t 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
join wx_t_workshop_partner l1 on l1.workshop_id=t.id and l1.relation_type='trade'
join wx_t_organization l2 on l1.partner_id=l2.id
where 
--l2.id in (33436)
t.status='3'
order by l2.id, t3.id, t2.id, t1.id, t.id	

select distinct t3.id, t2.id, t1.id, l2.organization_name "合伙人名称", l2.id "SP ID", t.work_shop_name "门店名称", t.id "门店ID", 
t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型", 
(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t.id order by wss.create_time desc) "激活时间"
from wx_t_work_shop t 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
join wx_t_workshop_partner l1 on l1.workshop_id=t.id
join wx_t_organization l2 on l1.partner_id=l2.id
where --ws.create_time>='2017-01-01' and 
t.status='3'
--l2.id in (33436)
order by l2.id, t3.id, t2.id, t1.id, t.id	

select distinct l2.id, t3.id, t2.id, t1.id, t.id, l2.organization_name "合伙人名称", t.work_shop_name "门店名称", 
t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型", ws.create_time "激活时间"
from wx_t_work_shop t 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
join wx_t_workshop_partner l1 on l1.workshop_id=t.id
join wx_t_organization l2 on l1.partner_id=l2.id
join wx_t_workshop_status ws on t.id=ws.workshop_id and ws.workshop_with_status='3'
where ws.create_time>='2017-01-01' and 
l2.id in (33436)
order by l2.id, t3.id, t2.id, t1.id, t.id	
--合伙人导出
select o.id "SP ID", o.organization_name "合伙人名称" from wx_t_organization o where o.type=1 order by o.id
--1月前激活门店扫码	
 select distinct l2.id,t.id,t3.id, t2.id, t1.id, l2.organization_name "合伙人名称", t.work_shop_name "门店名称", 
 t3.region_name "所属省份", t2.region_name "所属城市"--, t.id--, count(1) 
, t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型"--,t.longitude, t.latitude
,(select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-02-01 00:00:00.000' and a3.creation_time<'2017-03-01 00:00:00.000') "扫码升数(L)"--,t.remark--, we.name,
--case when we.employee_type='Owner' then '店长' else '技师' end empt, we.mobile,
from wx_t_work_shop t --left join wx_t_workshop_employee we on t.id=we.workshop_id 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=t.id
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_workshop_status ws on t.id=ws.workshop_id and ws.workshop_with_status='3'
	where --t.status='3' and t.active_fee_flag=1 
	--and l2.id=7--and we.id is null 
	--and exists (select 1 from wx_t_workshop_status ws1 where t.id=ws1.workshop_id and ws1.workshop_with_status='3' and ws1.create_time>='2017-01-01 00:00:00.000' and ws1.create_time<'2017-02-01 00:00:00.000')
	--and t.update_time>='2017-01-01 00:00:00.000' and t.update_time<'2017-02-01 00:00:00.000'
	ws.create_time<'2017-01-01'
	and l2.id in(33529, 33436)
and 
 (select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-02-01 00:00:00.000' and a3.creation_time<'2017-03-01 00:00:00.000')>=40
	--and exists (select 1 from wx_t_workshop_partner l1 where l1.partner_id=22955 and l1.workshop_id=t.id)
	--group by t.id
	order by l2.id,t.id, t3.id, t2.id, t1.id

 select distinct l2.id,t.id,t3.id, t2.id, t1.id, l2.organization_name "合伙人名称", t.work_shop_name "门店名称", 
 t3.region_name "所属省份", t2.region_name "所属城市"--, t.id--, count(1) 
, t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型"--,t.longitude, t.latitude
,(select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-03-01 00:00:00.000' and a3.creation_time<'2017-04-01 00:00:00.000') "扫码升数(L)"--,t.remark--, we.name,
--case when we.employee_type='Owner' then '店长' else '技师' end empt, we.mobile,
 ,isnull(t.management_fee_flag, 0) "激活费发放次数"
from wx_t_work_shop t --left join wx_t_workshop_employee we on t.id=we.workshop_id 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=t.id
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_workshop_status ws on t.id=ws.workshop_id and ws.workshop_with_status='3'
	where --t.status='3' and 
	--t.active_fee_flag=1 and
	--and l2.id=7--and we.id is null 
	--and exists (select 1 from wx_t_workshop_status ws1 where t.id=ws1.workshop_id and ws1.workshop_with_status='3' and ws1.create_time>='2017-01-01 00:00:00.000' and ws1.create_time<'2017-02-01 00:00:00.000')
	--and t.update_time>='2017-01-01 00:00:00.000' and t.update_time<'2017-02-01 00:00:00.000'
	ws.create_time<'2017-01-01' and 
	(t.management_fee_flag is null or t.management_fee_flag < 3)
	--and l2.id in(44696)
and 
 (select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-03-01 00:00:00.000' and a3.creation_time<'2017-04-01 00:00:00.000')>=40
	--and exists (select 1 from wx_t_workshop_partner l1 where l1.partner_id=22955 and l1.workshop_id=t.id)
	--group by t.id
	order by l2.id,t.id, t3.id, t2.id, t1.id	
	
--导出比较数据
select max(o.organization_name) "合伙人名称", t.work_shop_name "门店名称", max(t.work_shop_address) "门店地址", count(1) "数量"
from wx_t_work_shop t 
left join wx_t_workshop_partner tp on tp.workshop_id=t.id
join wx_t_organization o on tp.partner_id=o.id
where t.active_fee_flag=1 
--and tp.partner_id=44946
group by o.id, t.work_shop_name
order by o.id

select "合伙人名称", t.work_shop_name "门店名称", max(t.work_shop_address) "门店地址", count(1) "数量"
from wx_t_work_shop t 
left join wx_t_workshop_partner tp 
join wx_t_
where tp.workshop_id=t.id and tp.partner_id=44946)
where t.active_fee_flag=1 and 
group by t.work_shop_name;
--order by t.id
select t.work_shop_name, t.id id, t.work_shop_address
from wx_t_work_shop t where t.active_fee_flag=1 and 
exists (select 1 from wx_t_workshop_partner tp where tp.workshop_id=t.id and tp.partner_id=7)
--group by t.work_shop_name
order by t.id

--1月前激活门店
select distinct l2.id,t.id, t.region_id, l2.organization_name "合伙人名称", t.work_shop_name "门店名称", t.work_shop_address "门店地址"
from wx_t_work_shop t 
join wx_t_workshop_partner l1 on l1.workshop_id=t.id
join wx_t_organization l2 on l1.partner_id=l2.id
join wx_t_workshop_status ws on t.id=ws.workshop_id and ws.workshop_with_status='3'
where ws.create_time<'2017-01-01' and l2.id in (22956,33455)
order by l2.id, t.region_id, t.id

--扫码导出明细	
select distinct l2.id, t3.id, t2.id, t1.id, ws.id, a3.id, l2.organization_name "合伙人名称", ws.work_shop_name "门店名称", 
t1.region_name "所属区域", ws.work_shop_address "门店地址", --a3.qr_code QR, 
a3.sku, 
p.name "产品名称", 
a3.capacity "扫码升数(L)", a3.creation_time "扫码时间"
from wx_v_oil_verification a3
	join wx_t_work_shop ws on a3.workshop_id=ws.id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=a3.workshop_id and l1.relation_type='trade'
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_product p on a3.sku=p.sku
	where  a3.creation_time>='2017-09-01 00:00:00.000' and a3.creation_time<'2017-10-01 00:00:00.000'
	--and l1.partner_id != 8
	--group by a3.sku, l2.id
	order by l2.id, t3.id, t2.id, t1.id, ws.id, a3.sku

select l2.organization_name "合伙人名称", --ws.work_shop_name "门店名称", a3.qr_code QR, 
a3.sku, 
p.name "产品名称", 
a3.capacity "扫码升数(L)", a3.creation_time "扫码时间"
from wx_v_oil_verification a3
	join wx_t_work_shop ws on a3.workshop_id=ws.id
	join wx_t_workshop_partner l1 on l1.workshop_id=a3.workshop_id and l1.relation_type='trade'
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_product p on a3.sku=p.sku
	--where  a3.creation_time>='2017-01-01 00:00:00.000' and a3.creation_time<'2017-02-01 00:00:00.000'
	--group by a3.sku, l2.id
	order by l2.id, ws.id, a3.sku

	
--合伙人扫码导出 
select max(l2.organization_name) "合伙人名称", a3.sku, max(p.name) "产品名称", sum(a3.capacity) "扫码升数(L)"
from wx_v_oil_verification a3
	join wx_t_workshop_partner l1 on l1.workshop_id=a3.workshop_id and l1.relation_type='trade'
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_product p on a3.sku=p.sku
	where  a3.creation_time>='2017-01-01 00:00:00.000' and a3.creation_time<'2017-02-01 00:00:00.000'
	group by a3.sku, l2.id
	order by l2.id, a3.sku
--插入qr管理权限
 insert into wx_t_rolesource (role_id, rs_type, source_id,xg_user,status,tenant_id) values (11189,2,10035,1,1,1);
 insert into wx_t_rolesource (role_id, rs_type, source_id,xg_user,status,tenant_id) values (11189,2,50075,1,1,1);
 insert into wx_t_rolesource (role_id, rs_type, source_id,xg_user,status,tenant_id) values (11189,2,50076,1,1,1);
--插入用户qr管理权限
 insert into wx_t_userbase (user_id, rs_type, source_id,xg_user,status,tenant_id) values (76313,2,10035,1,1,1);
 insert into wx_t_userbase (user_id, rs_type, source_id,xg_user,status,tenant_id) values (76313,2,50075,1,1,1);
 insert into wx_t_userbase (user_id, rs_type, source_id,xg_user,status,tenant_id) values (76313,2,50076,1,1,1);
 
 --	insert into wx_t_mechanic_qrcode (created_by,creation_time,mechanic_code,qr_code, sku, workshop_id) values 
--	(1,'2017-01-08 00:00','M201612222113321328','n9pedttt2st9b84fxgex0gbg0xo0','500247LPK',84911);
--插入子模块
insert into wx_t_module (module_name, parent_module_id, enalbe_flag, created_by) values ('库存管理', (select module_id from wx_t_module where module_name='产品管理'), 'Y', 1);
--插入资源
insert into wx_t_resource (resource_name,module_id,resource_type_code,created_by) values ('合伙人库存',(select module_id from wx_t_module where module_name='库存管理'),'SP',1);
--插入SQL
insert into wx_t_resource_sql (sqlid,resource_id,created_by) values ('com.chevron.partnerorder.dao.PartnerInventoryVoMapper.selectInventorySum',(select resource_id from wx_t_resource where resource_name='合伙人库存'), 1);
--插入chevronadmin所有数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) values ('USER', 1, (select resource_id from wx_t_resource where resource_name='合伙人库存'), 1,'partner_id!=9',1);
--插入指定角色所有数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) 
select 'ROLE', role_id, (select resource_id from wx_t_resource where resource_name='合伙人库存'), 1,'partner_id!=9',1
from wx_t_role
where ch_role_name in ('Chevron_Manager');
--插入指定角色负责合伙人数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) 
select 'ROLE', role_id, (select resource_id from wx_t_resource where resource_name='合伙人库存'), 7,'exists (select 1 from wx_t_partner_responsible_main xx_001_prm join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id where xx_001_pr.partner_id=${resultsetAlias}.partner_id and xx_001_prm.user_id=#{currentUserId})',1
from wx_t_role
where ch_role_name in ('chevron_Marketing');
--插入指定角色所属合伙人数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) 
select 'ROLE', role_id, (select resource_id from wx_t_resource where resource_name='合伙人库存'), 3,'partner_id=#{currentPartnerId}',1
from wx_t_role
where ch_role_name in ('Service_Partner_Admin','Service_Partner_Manager');

insert into wx_t_resource (resource_name,module_id,resource_type_code,created_by) values ('门店库存',(select module_id from wx_t_module where module_name='库存管理'),'WS',1);
insert into wx_t_resource_sql (sqlid,resource_id,created_by) values ('com.chevron.pms.dao.WorkShopVoMapper.selWorkshopWithInventory',(select resource_id from wx_t_resource where resource_name='门店库存'), 1);
--插入chevronadmin所有数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) values ('USER', 1, (select resource_id from wx_t_resource where resource_name='门店库存'), 4,'not exists (select 1 from wx_t_workshop_partner xx_001_wp where xx_001_wp.partner_id=9 and xx_001_wp.workshop_id=${resultsetAlias}.workshop_id)',1);
--插入指定角色所有门店数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) 
select 'ROLE', role_id, (select resource_id from wx_t_resource where resource_name='门店库存'), 4,'not exists (select 1 from wx_t_workshop_partner xx_001_wp where xx_001_wp.partner_id=9 and xx_001_wp.workshop_id=${resultsetAlias}.workshop_id)',1
from wx_t_role
where ch_role_name in ('Chevron_Manager');
--插入指定角色负责合伙人下门店数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) 
select 'ROLE', role_id, (select resource_id from wx_t_resource where resource_name='门店库存'), 9,'exists (select 1 from wx_t_partner_responsible_main xx_001_prm join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id join wx_t_workshop_partner xx_001_wp on xx_001_wp.partner_id=xx_001_pr.partner_id where xx_001_wp.workshop_id=${resultsetAlias}.workshop_id and xx_001_prm.user_id=#{currentUserId})',1
from wx_t_role
where ch_role_name in ('chevron_Marketing');
--插入指定角色所属合伙人下门店数据权限
insert into wx_t_subject_res_permission (subject_type,subject_id,resource_id,permission_type_id,permission_expression,created_by) 
select 'ROLE', role_id, (select resource_id from wx_t_resource where resource_name='门店库存'), 8,'exists (select 1 from wx_t_workshop_partner xx_001_wp where xx_001_wp.partner_id=#{currentPartnerId} and xx_001_wp.workshop_id=${resultsetAlias}.workshop_id)',1
from wx_t_role
where ch_role_name in ('Service_Partner_Admin','Service_Partner_Manager');
--恢复数据
	SET   IDENTITY_INSERT wx_t_work_shop on;
	INSERT INTO wx_t_work_shop (id,work_shop_code, work_shop_name, work_shop_address, longitude, latitude, area, seats_num, employees_num, service_scope, business_license_code, reserve_service_tel, contact_person, contact_person_tel, business_detail, harvest_person, harvest_person_tel, butt_in_charge_person, butt_in_charge_person_tel, bank_acount_name, bank_acount, bank, credit_rating, create_time, update_time, creator, remark, status, business_license_expiry_date, type, mechanic_count, excute_user_id, photo_id, source, signed_type, scale, region_id, region_name, work_shop_name_py, active_fee_flag, management_fee_flag)
VALUES (85845,'201612271649404455', '宏星汽修', '丰美路', 114.213673, 30.620531, '', NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '', '', '', '', '', '2016-12-28 16:25:42', '2016-12-28 16:25:42', '76037', '', '3', NULL, '快修保养店', NULL, 76037, 'z250so27wri6fpztut5wozdq', '扫店录入', '', '1-3', 420104, '硚口区', 'HXQX ', 1, NULL)
	SET   IDENTITY_INSERT wx_t_work_shop off; 
	
	SELECT        mq.id, CAST(LEFT(p.capacity, 1) AS INT) AS capacity, mq.mechanic_code, mq.workshop_id, ws.work_shop_name AS workshop_name, mq.creation_time, mq.sku, p.name AS product_name, 0 AS status, 
                         CASE uq.is_effective WHEN 'Y' THEN 1 ELSE NULL END AS order_flag, uq.user_wechat_openid, mq.qr_code
FROM            dbo.wx_t_mechanic_qrcode AS mq LEFT OUTER JOIN
                         dbo.wx_v_user_qrcode_valid AS uq ON mq.qr_code = uq.qr_code INNER JOIN
                         dbo.wx_t_product AS p ON mq.sku = p.sku LEFT OUTER JOIN
                         dbo.wx_t_work_shop AS ws ON mq.workshop_id = ws.id INNER JOIN
                         dbo.wx_t_workshop_employee AS we ON mq.mechanic_code = we.code

--激活管理费更新
 update c set c.management_fee_flag=isnull(c.management_fee_flag,0)+1
 from wx_t_work_shop c where exists (
 select distinct l2.id,t.id,t3.id, t2.id, t1.id, l2.organization_name "合伙人名称", t.work_shop_name "门店名称"
 --, t3.region_name "所属省份", t2.region_name "所属城市"--, t.id--, count(1) 
, t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型"--,t.longitude, t.latitude
,(select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-02-01 00:00:00.000' and a3.creation_time<'2017-03-01 00:00:00.000') "扫码升数(L)"--,t.remark--, we.name,
--case when we.employee_type='Owner' then '店长' else '技师' end empt, we.mobile,
from wx_t_work_shop t --left join wx_t_workshop_employee we on t.id=we.workshop_id 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=t.id
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_workshop_status ws on t.id=ws.workshop_id and ws.workshop_with_status='3'
	where --t.status='3' and t.active_fee_flag=1 
	--and l2.id=7--and we.id is null 
	--and exists (select 1 from wx_t_workshop_status ws1 where t.id=ws1.workshop_id and ws1.workshop_with_status='3' and ws1.create_time>='2017-01-01 00:00:00.000' and ws1.create_time<'2017-02-01 00:00:00.000')
	--and t.update_time>='2017-01-01 00:00:00.000' and t.update_time<'2017-02-01 00:00:00.000'
	ws.create_time<'2017-01-01' and 
	--l2.id in(33529, 33436) and 

 (select sum(a3.capacity) from wx_v_oil_verification a3 where a3.workshop_id=t.id and a3.creation_time>='2017-02-01 00:00:00.000' and a3.creation_time<'2017-03-01 00:00:00.000')>=40
	--and exists (select 1 from wx_t_workshop_partner l1 where l1.partner_id=22955 and l1.workshop_id=t.id)
	--group by t.id
	and c.id=t.id
)  
--技师扫码查询
select o.organization_name, wp.workshop_name, we.name, mq.creation_time from wx_t_mechanic_qrcode mq 
left join wx_t_workshop_employee we on we.code=mq.mechanic_code
left join wx_t_work_shop w on w.id=mq.workshop_id
left join wx_t_workshop_partner wp on wp.workshop_id=mq.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
 where mq.qr_code='onm45eekpu51cmr7vyqvlf91z6rd'
 
 --删除滴滴养车的qrcode
 select o.organization_name, wp.workshop_name, we.name, mq.creation_time, mq.qr_code from wx_t_mechanic_qrcode mq join wx_t_workshop_employee we on we.code=mq.mechanic_code
left join wx_t_work_shop w on w.id=we.workshop_id
left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
 where wp.partner_id=9

delete mq from wx_t_mechanic_qrcode mq join wx_t_workshop_employee we on we.code=mq.mechanic_code
left join wx_t_work_shop w on w.id=we.workshop_id
left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
 where wp.partner_id=9
--激活门店达标导出
select o.organization_name "合伙人名称", 
t.work_shop_name "门店名称", t3.region_name "省", t2.region_name "市", t1.region_name "区", t.work_shop_address "门店地址", 
w1.award_fee "金额(元)",
w1.active_time "录入时间"
from wx_t_workshop_award_detail w1 
 join wx_t_organization o on o.id=w1.partner_id 
 join wx_t_work_shop t on t.id=w1.workshop_id
 left join wx_t_region t1 on t.region_id=t1.id
		left join wx_t_region t2 on t1.parent_id=t2.id
		left join wx_t_region t3 on t2.parent_id=t3.id
		where w1.close_date>='2017-01-01' and w1.close_date<'2017-02-01'
order by w1.partner_id, t3.id, t2.id, t1.id, w1.active_time 
--核销达标导出
select o.organization_name "合伙人名称",
t.work_shop_name "门店名称", t3.region_name "省", t2.region_name "市", t1.region_name "区", t.work_shop_address "门店地址", 
ad1.scan_capacity "扫码升数(L)" ,
ad1.award_fee "金额(元)"
from wx_t_oil_ver_award_det ad1 
 join wx_t_work_shop t on t.id=ad1.workshop_id
 left join wx_t_region t1 on t.region_id=t1.id
		left join wx_t_region t2 on t1.parent_id=t2.id
		left join wx_t_region t3 on t2.parent_id=t3.id
join wx_t_organization o on o.id=ad1.partner_id
where ad1.close_date >= '2017-01-01' and ad1.close_date < '2017-02-01'
order by ad1.partner_id, t3.id, t2.id, t1.id, ad1.scan_capacity
--创建qr数据库索引 
create index ix_qr_code_detail_batch_id on db_owner.wx_t_qr_code_detail(batch_id);
create index ix_qr_code_detail_qrcode on db_owner.wx_t_qr_code_detail(qr_code);
--创建唯一索引
alter table wx_t_mechanic_qrcode add constraint uk_mechanic_qrcode_qrcode unique (qr_code)

--门店任务导出
select o.id, t.id, o.organization_name "合伙人名称", t.work_shop_name "门店名称", t3.region_name "省", t2.region_name "市", t1.region_name "区", t.work_shop_address "门店地址",
isnull(t.sd_time, t.create_time) "扫店时间",
(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t.id and not exists (select 1 from wx_t_workshop_status wss1 where wss1.workshop_id=wss.workshop_id and wss.create_time>wss.create_time)) "录店时间",
(select substring((
 select '	' + CONVERT(varchar(100), s.xg_sj, 20) from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id where s.org_id=t.id and m.task_status=4 and m.tmb_type_code='TT_2_XD' and s.tenant_id=t.partner_id  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) "巡店时间1"

from (
select ws.work_shop_name, ws.create_time, ws.id, ws.region_id, ws.work_shop_address, ws.status,
(select top 1 wp.partner_id from wx_t_workshop_partner wp where wp.workshop_id=ws.id order by (case when wp.relation_type='trade' then 0 else 1 end), wp.create_time) partner_id,
(select top 1 ts1.xg_sj FROM wx_task_main task_main join wx_task_sub ts1 on ts1.task_main_id=task_main.task_main_id where ts1.org_id=ws.id and task_main.tmb_type_code='TT_2_SD' and task_main.task_status=4) sd_time
  from wx_t_work_shop ws 
  where ws.status='3'
) t
 left join wx_t_region t1 on t.region_id=t1.id
 left join wx_t_region t2 on t1.parent_id=t2.id
 left join wx_t_region t3 on t2.parent_id=t3.id
 join wx_t_organization o on o.id=t.partner_id	
 where t.status='3'
 order by o.id, t.id, t3.id, t2.id, t1.id
 
select distinct t3.id, t2.id, t1.id, l2.id "SP ID", t.id "门店ID", l2.organization_name "合伙人名称", t.work_shop_name "门店名称", 
t3.region_name "所属省份", t2.region_name "所属城市",
t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型", 
(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t.id order by wss.create_time desc) "激活时间",
s.xg_sj "巡店完成时间", s.task_id, s.task_main_id
from wx_t_work_shop t 
	left join wx_t_region t1 on t.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
join wx_t_workshop_partner l1 on l1.workshop_id=t.id
join wx_t_organization l2 on l1.partner_id=l2.id
join wx_task_sub s on  s.org_id=t.id
join wx_task_main m on m.task_main_id = s.task_main_id
where --ws.create_time>='2017-01-01' and 
convert(int, t.status)>-1 and 
s.task_status=4 and m.tmb_type_code='TT_2_XD'
--l2.id in (33436)
order by l2.id, t3.id, t2.id, t1.id, t.id, s.xg_sj desc	
 --线上产品物流码
      select code
    from wx_t_partner_inventory_out_record pior1
    where  exists (select 1 from wx_t_partner_order_address_product ap1 join wx_t_partner_order po1 on po1.id=ap1.partner_order_id join wx_t_organization o on po1.partner_id=o.id where ap1.out_stock_no=pior1.stock_out_no
       and o.organization_name in ('e养车'))
       and exists (select 1 from qrcodedb.dbo.wx_t_qr_code_detail cd where cd.code_id='10000'+SUBSTRING(pior1.code,6, 7))
       
--用户分配超级管理员权限
INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
 VALUES ((select user_id from wx_t_user where login_name='supadmin')
           ,(select user_id from wx_t_user where login_name='chevronadmin')
           ,(select role_id from wx_t_role where ch_role_name='admin'),1);
select * from wx_t_partner_o2o_enterprise e where exists (select 1 from wx_t_partner_o2o_enterprise e1 where e1.partner_id=e.partner_id and e1.id>e.id)

delete e from wx_t_partner_o2o_enterprise e where exists (select 1 from wx_t_partner_o2o_enterprise e1 where e1.partner_id=e.partner_id and e1.id>e.id)

	
select  l2.organization_name "合伙人名称", 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partner.partnerProperty' and di1.dic_item_code=poe.partner_property) partner_property_text,
t.work_shop_name "门店名称", t3.region_name "所属省份", t2.region_name "所属城市", t1.region_name "所属区域", 
t.work_shop_address "门店地址",t.type "门店类型", 
(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t.id order by wss.create_time desc) "激活时间",
(select u1.ch_name from wx_t_user u1 where u1.user_id=t.excute_user_id) "执行人", t.remark "备注" from wx_t_work_shop t left join wx_t_region t1 
on t.region_id=t1.id left join wx_t_region t2 on t1.parent_id=t2.id left join wx_t_region t3 on t2.parent_id=t3.id join wx_t_workshop_partner l1 
on l1.workshop_id=t.id and l1.relation_type='trade' join wx_t_organization l2 on l1.partner_id=l2.id 
left join wx_t_partner_o2o_enterprise poe on l2.id=poe.partner_id
where t.status='3' and l2.id!=9
group by l2.organization_name, 
t.work_shop_name, t1.region_name, t.work_shop_address,t.type,t.id,t.create_time,t.remark,t.excute_user_id,t3.region_name, t2.region_name,poe.partner_property
order by l2.organization_name, t.create_time desc, t.id desc
	
select a.organization_name, 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partner.partnerProperty' and di1.dic_item_code=a.partner_property) partner_property_text,
a.tm, count(1) mc
from (
select distinct l2.organization_name, l2.id, poe.partner_property, mq.mechanic_code, 
convert(varchar(20), year(isnull(mq.update_time, mq.creation_time))) + '-' + 
right('0' + convert(varchar(20), month(isnull(mq.update_time, mq.creation_time))), 2) tm
from wx_t_organization l2
left join wx_t_partner_o2o_enterprise poe on l2.id=poe.partner_id
join wx_t_workshop_partner wp on wp.partner_id=l2.id and wp.relation_type='trade'
join wx_t_mechanic_qrcode mq on mq.workshop_id=wp.workshop_id
where mq.mechanic_code != '-1' and l2.id!=9
) a
group by a.organization_name, a.partner_property, a.tm,a.id
order by a.id, a.tm desc

--取消qrcode历史显示
update wx_t_qr_code_batch set creation_time=null where creation_time>'2018-03-16' 

--出库单
select osl.code, os.stock_to, osl.scan_time, os.stock_out_no from wx_t_out_stock_line osl join wx_t_out_stock os on os.stock_out_no=osl.stock_out_no
where os.stock_from='12964' and
 osl.scan_time>='2018-04-21' and osl.code in ('102952010703188900362559') order by osl.id
 
 --箱码
 select * from wx_t_oem_product_packaging_code t where t.code1='102917901010'
 --recv_id 20205 高桥 
select * from wx_t_oem_delivery_product dp left join wx_t_oem_delivery d on d.id=dp.delivery_id where code='102931247442'
 
 select * from wx_t_organization o join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id where pe.partner_property='NORMAL'
and not exists (select 1 from wx_t_partner_responsible r where r.partner_id=o.id and r.responsible_main_id=104)
(select prm.id from wx_t_partner_responsible_main prm join wx_t_user u on u.user_id=prm.user_id where u.login_name='vicky' and prm.fun_flag='order_confirm'))


select distinct o.id, o.organization_name, 
case when pe.partner_property='NORMAL' AND o.create_time>='2018-01-01' then '普通合伙人&经销商' 
when pe.partner_property='NORMAL' then '普通合伙人' else (select di.dic_item_name from wx_t_dic_item di 
where di.dic_type_code='partner.partnerProperty' and di.dic_item_code=pe.partner_property) end "合伙人属性" ,
u.ch_name "负责销售",
u.email
from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join wx_t_partner_responsible pr on pr.partner_id=o.id
left join wx_t_partner_responsible_main prm on prm.id=pr.responsible_main_id and prm.fun_flag='order_confirm'
left join wx_t_user u on u.user_id=prm.user_id
where o.type=1  and prm.id is not null
order by o.id

update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u join wx_t_organization o on o.id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where o.type=1 and (u.type is null or u.type != '1')
and r.ch_role_name in ('Service_Partner_Admin', 'Service_Partner_Manager')
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=3212
 
 delete ur from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id
left join wx_t_user u on u.user_id=ur.user_id
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where r.ch_role_name='Caltex_Dealer'
and (pe.partner_property is null or pe.partner_property != 'Delo Distributor')
and (pe.partner_property is null or (pe.partner_property != 'NORMAL' or o.create_time < '2018-01-01'))

select * from wx_t_qr_code_batch qb where qb.batch_id in (
select qd.batch_id from temp_qr_map1 tm 
left join wx_t_qr_code_detail qd on qd.code_id=(*********** + convert(int,SUBSTRING(tm.code1,5,8)))
group by qd.batch_id)

INSERT INTO [dbo].[wx_t_qr_code_batch]
           ([batch_no],[sku],[product_name],[batch_count])
select 'TEMP20180615' + tm.product_id+convert(nvarchar(30),qd.batch_id), tm.product_id, '', count(1)  from temp_qr_map1 tm
left join wx_t_qr_code_detail qd on qd.code_id=(*********** + convert(int,SUBSTRING(tm.code1,5,8)))
group by tm.product_id, qd.batch_id

update qd set qd.batch_id=qb.batch_id 
--select * 
from temp_qr_map1 tm
join wx_t_qr_code_detail qd on qd.code_id=(*********** + convert(int,SUBSTRING(tm.code1,5,8)))
join wx_t_qr_code_batch qb on qb.batch_no like 'TEMP20180615' + tm.product_id+convert(nvarchar(30),qd.batch_id)

--POINT_TYPE  "CALTEX_POINT","加德士积分"/"PROMOTION_POINT","促销积分"/"CDM_STOCK_POINT","CDM进货积分"/"CDM_MATERIAL_POINT","CDM物料积分"
SELECT o.id,o.organization_name,sum(pvd.POINT_VALUE-pvd.POINT_PAYED) AS LEFT_point,pvd.POINT_TYPE
FROM wx_t_organization o
LEFT JOIN wx_t_partner_o2o_enterprise en ON en.partner_id=o.id
LEFT JOIN wx_t_point_account pac ON pac.POINT_ACCOUNT_OWNER_ID=o.id
LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID=pac.ID 

WHERE 1=1
AND ((en.partner_property='NORMAL' AND o.create_time>'2018-01-01') OR en.partner_property='Delo Distributor')
AND pvd.POINT_TYPE='CALTEX_POINT'
GROUP BY o.id,o.organization_name,pvd.POINT_TYPE
ORDER BY o.organization_name

update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u join wx_t_organization o on o.id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where o.type=1 and (u.type is null or u.type != '1')
and r.ch_role_name in ('Caltex_Dealer')
and ((SELECT sum(pvd.POINT_VALUE-pvd.POINT_PAYED)
FROM wx_t_point_account pac 
LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID=pac.ID 

WHERE pvd.POINT_TYPE='CALTEX_POINT' and pac.POINT_ACCOUNT_OWNER_ID=o.id) > 0
or (SELECT sum(pvd.POINT_VALUE-pvd.POINT_PAYED)
FROM wx_t_point_account pac 
LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID=pac.ID 

WHERE pvd.POINT_TYPE='PROMOTION_POINT' and pac.POINT_ACCOUNT_OWNER_ID=o.id) > 0)
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=3975
 
 INSERT INTO [dbo].[wx_t_userrole]  ([user_id]         ,[grant_userid]         ,[role_id]         ,[xg_sj]         ,[xg_user]         ,[status]          ,[tenant_id])    
select u.user_id, 1, (select role_id from wx_t_role where ch_role_name='Service_Partner_CLERK') ,getdate()           ,1           ,1           ,1 
from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id
left join wx_t_user u on u.user_id=ur.user_id
where u.status=1 and r.ch_role_name='Service_Partner_BD'
and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=u.user_id and r1.ch_role_name='Service_Partner_CLERK')


--合伙人删除回滚
select * from wx_t_workshop_partner wp join wx_log l on l.log_type='WorkshopDelete' and l.ext_property1=wp.workshop_id
where wp.partner_id=56508
select *
--update wp set wp.status=1
 from wx_t_user wp join wx_log l on l.log_type='UserDelete' and l.ext_property1=wp.user_id
where wp.org_id=56508 and wp.type is not null and wp.type != '1'

--新建账号
select * from wx_t_user u where u.login_name in ('***********', '***********')
--update u set u.reset_flag=1, email=null from wx_t_user u where u.login_name in ('***********', '***********')

--update wx_t_organization set status=1 where id=56508

select * from wx_t_business_fun_resource bfr1 join wx_t_business_fun bf1 on bfr1.business_fun_code=bf1.business_fun_code and bf1.enable_flag=1 where bfr1.resource_type=2 and bfr1.resource_id=50180
--同步合伙人
--同步合伙人销售关系
INSERT INTO [dbo].[dw_customer_region_sales_supervisor_rel]
           ([customer_name_cn]
      ,[region_name]
      ,[sales_cai]
      ,[sales_name]
      ,[suppervisor_cai]
      ,[suppervisor_name]
      ,[distributor_id]
      ,[customer_name_en])
  select distinct bi.[customer_name_cn]
      ,bi.[region_name]
      ,bi.[sales_cai]
      ,bi.[sales_name]
      ,bi.[suppervisor_cai]
      ,bi.[suppervisor_name]
      ,bi.[distributor_id]
      ,bi.[customer_name_en]
  FROM [chemid].[chevronDW_Base].dbo.view_customer_region_sales_supervisor_rel_pp where customer_name_cn='乌海市丰琪商贸有限责任公司'
--新建合伙人管理用户
--重置密码设置
update u set u.reset_flag=1, email=null from wx_t_user u where u.login_name in ('***********', '***********')  

--产品权限
insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from wx_t_product p, wx_t_organization o
where p.sku in('500175LPK','503033DNK','503033DNK') and 
o.type=1 and o.status=1 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)

and exists (select 1 from wx_t_partner_o2o_enterprise pe 
left join PP_MID.dbo.syn_dw_to_pp_customer c on pe.sap_code=c.customer_code
left join view_customer_region_sales_channel v on c.distributor_id=v.distributor_id
where v.region_name in ('NC','East & CC','NE') and pe.partner_id=o.id)


insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from 
(select * from wx_t_product ip1 where exists (select 1 from wx_t_dealer_product_permission dpp 
left join wx_t_partner_o2o_enterprise pe on dpp.dealer_id=pe.partner_id
left join PP_MID.dbo.syn_dw_to_pp_customer c on pe.sap_code=c.customer_code
left join view_customer_region_sales_channel v on c.distributor_id=v.distributor_id
where v.region_name in ('NE') and ip1.support_order=1 and dpp.product_sku=ip1.sku)) p, wx_t_organization o
where --p.sku in('500175LPK','503033DNK','503033DNK') and 
--p.sku not in('500268NJK') and
o.type=1 and o.status=1 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)
and o.id=56664


select * 
--DELETE p
from wx_t_dealer_product_permission p left join wx_t_organization o on p.dealer_id=o.id
where o.type=1 and o.status=1 and p.product_sku in ('500268LPK','500268NJK')
and exists (select 1 from wx_t_partner_o2o_enterprise pe 
left join PP_MID.dbo.syn_dw_to_pp_customer c on pe.sap_code=c.customer_code
left join view_customer_region_sales_channel v on c.distributor_id=v.distributor_id
where v.region_name in ('NC','East & CC','NE') and pe.partner_id=o.id)


insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from wx_t_product p, wx_t_organization o
where p.support_order=1 and o.id=56651
and o.type=1 and o.status=1 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)

--查询数据权限模块
select r.resource_name from wx_t_resource r left join wx_t_resource_sql rs on r.resource_id=rs.resource_id where rs.sqlid like 'com.chevron.material.dao.WXTMaterialDealerVoMapper.queryDealerByPromotion%'

--DMS管理员数据权限
INSERT INTO [dbo].[wx_t_partner_responsible]
           ([partner_id]
           ,[create_time]
           ,[creator]
           ,[remark]
           ,[responsible_main_id])
select [partner_id]
           ,getdate()
           ,[creator]
           ,[remark]
           ,(select prm.id from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='HOYD' and u.status=1)
		   from [wx_t_partner_responsible] pr
where exists (select 1 from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='ZLAA' and u.status=1 
and prm.id=pr.responsible_main_id)
and not exists (select 1 from wx_t_partner_responsible pr1 where pr1.partner_id=pr.partner_id and pr1.responsible_main_id=(select prm.id from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='HOYD')) 

--技师导出
select o.organization_name "经销商名称", t.work_shop_name "门店名称",
we.name "技师姓名", case when we.id is null then null when we.employee_type='Owner' then '店长' else '技师' end "店长/技师",
case when we.id is null then null when we.version_no=1 then '是' else '否' end "B2B技师",
isnull(we.update_time, we.creation_time) "注册时间" from wx_t_work_shop t
left join wx_t_workshop_employee we  on we.workshop_id=t.id
left join wx_t_workshop_partner wp on wp.workshop_id=t.id
left join wx_t_organization o on o.id=wp.partner_id
where o.id!=9 and convert(int, t.status) > 0 and
o.status=1 and t.status != '-101'
order by o.id, t.id

--经销商负责区域
select o.organization_name "经销商名称",
pe.sap_code "SAP Code", 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partner.partnerProperty' and di1.dic_item_code=pe.partner_property) "经销商属性",
(case when o.status=0 then '是' else '否' end) "取消合作",
(select substring((
 SELECT ','+cast(xx0.region_name as varchar(290)) FROM (select distinct r3.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配省份",
(select substring((
 SELECT ','+cast(xx0.region_name as varchar(290)) FROM (select distinct r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配城市",
(select substring((
 SELECT ','+cast(xx0.region_name as varchar(290)) FROM (select distinct r1.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配区县" from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
where o.type=1
order by o.id

--同步销售关系
insert into [dw_customer_region_sales_supervisor_rel] ([customer_name_cn]
      ,[region_name]
      ,[sales_cai]
      ,[sales_name]
      ,[suppervisor_cai]
      ,[suppervisor_name]
      ,[distributor_id]
      ,[customer_name_en])
  select distinct bi.[customer_name_cn]
      ,bi.[region_name]
      ,bi.[sales_cai]
      ,bi.[sales_name]
      ,bi.[suppervisor_cai]
      ,bi.[suppervisor_name]
      ,bi.[distributor_id]
      ,bi.[customer_name_en] FROM [chemid].[chevronDW_Base].dbo.view_customer_region_sales_supervisor_rel_pp bi
  left join [pmpdb01].[dbo].[dw_customer_region_sales_supervisor_rel] crss on bi.distributor_id=crss.distributor_id
	   and bi.customer_name_cn collate Chinese_PRC_CI_AS=crss.customer_name_cn 
	   AND bi.[sales_cai] collate Chinese_PRC_CI_AS = crss.[sales_cai]
	   AND bi.[suppervisor_cai] collate Chinese_PRC_CI_AS=crss.[suppervisor_cai] 
	   and bi.[region_name] collate Chinese_PRC_CI_AS=crss.[region_name]
  where bi.region_name like 'CDM%' and
	crss.region_name is null and
	not exists (select 1 from wx_t_partner_o2o_enterprise pe left join wx_t_organization o on o.id=pe.partner_id where o.status=0 and pe.distributor_id=bi.distributor_id) and 
   bi.[customer_name_cn] not in ('New business') and
   bi.[suppervisor_cai] not in ('ZHYM', 'YILE', 'SKCL', 'JNDG')
   
select ocrss.* 
--delete ocrss
from [dw_customer_region_sales_supervisor_rel] ocrss
left join dw_region_sales_channel_rel orsc on ocrss.region_name=orsc.region_name
where exists (
  select 1 FROM [chemid].[chevronDW_Base].dbo.view_customer_region_sales_supervisor_rel_pp bi
  left join [pmpdb01].[dbo].[dw_customer_region_sales_supervisor_rel] crss on bi.distributor_id=crss.distributor_id
	   and bi.customer_name_cn collate Chinese_PRC_CI_AS=crss.customer_name_cn 
	   AND bi.[sales_cai] collate Chinese_PRC_CI_AS = crss.[sales_cai]
	   AND bi.[suppervisor_cai] collate Chinese_PRC_CI_AS=crss.[suppervisor_cai] 
	   and bi.[region_name] collate Chinese_PRC_CI_AS=crss.[region_name]
	left join [chemid].[chevronDW_Base].dbo.view_region_sales_channel_rel_pp brsc on bi.region_name=brsc.region_name
  where bi.region_name like 'CDM%' and
	crss.region_name is null and
	not exists (select 1 from wx_t_partner_o2o_enterprise pe left join wx_t_organization o on o.id=pe.partner_id where o.status=0 and pe.distributor_id=bi.distributor_id) and 
   bi.[customer_name_cn] not in ('New business') and
   bi.[suppervisor_cai] not in ('ZHYM', 'YILE', 'SKCL', 'JNDG')
   and bi.distributor_id=ocrss.distributor_id and brsc.sales_channel_name collate Chinese_PRC_CI_AS=orsc.sales_channel_name)
   
--角色菜单权限
 select * from wx_t_rolesource rs left join wx_t_role r on r.role_id=rs.role_id where rs.source_id=50123
 
--积分订单缺货处理
 select * from dbo.wx_t_material_application_detail where APPLICATION_ID in (select id from dbo.wx_t_material_application where APPLICATION_CODE = 'MA003146') 
--and MATERIAL_ID = 313

select * 
--delete d 
from  dbo.wx_t_material_application_detail d  where APPLICATION_ID = 3179 and MATERIAL_ID = 316  and MATERIAL_SKU_CODE = 'M0000424-蓝色-XXL'  and APPLICATION_QTY = 4


select * from dbo.wx_t_point_account pac where pac.POINT_ACCOUNT_OWNER_ID 
  in (select o.id from dbo.wx_t_organization o left join wx_t_partner_o2o_enterprise enp on enp.partner_id = o.id where enp.sap_code = '8309348'
)


select * from dbo.wx_t_point_value_detail where POINT_ACCOUNT_ID = 3 and POINT_TYPE = 'CALTEX_POINT' order by TRANS_TIME desc

SELECT * 
--update d set POINT_PAYED = (POINT_PAYED - 2100)
FROM dbo.wx_t_point_value_detail d WHERE id = 4493

--update dbo.wx_t_point_value_detail set POINT_PAYED = (POINT_PAYED - 2100) where id = 4493

--新增区县
	SET   IDENTITY_INSERT wx_t_region on;
	INSERT INTO wx_t_region (id,region_code, region_name, parent_id, region_type, sort, status, is_leaf)
VALUES (440310,'440310', '坪山区', 440300, 'D', 0, 1, 1)
	INSERT INTO wx_t_region (id,region_code, region_name, parent_id, region_type, sort, status, is_leaf)
VALUES (440311,'440311', '光明区', 440300, 'D', 0, 1, 1)
	SET   IDENTITY_INSERT wx_t_region off; 
--QBR流程处理
select o.organization_name, qi.* from wx_t_qbr_access_info qi left join wx_t_organization o on o.id=qi.partner_id where partner_id in (56372,56505)
--update qi set qi.pass_type=0 from wx_t_qbr_access_info qi where qi.id=591
--update qi set qi.delete_flag=1 from wx_t_qbr_access_info qi where qi.id in (593,603)
--经销商特殊关系维护
INSERT INTO [dbo].[wx_customer_region_sales_supervisor_rel]
           ([customer_name_cn]
           ,[region_name]
           ,[sales_cai]
           ,[sales_name]
           ,[suppervisor_cai]
           ,[suppervisor_name]
           ,[distributor_id]
           ,[customer_name_en])
select crss.[customer_name_cn]
           ,crss.[region_name]
           ,'FITX'
           ,'王飞贺'
           ,[suppervisor_cai]
           ,[suppervisor_name]
           ,crss.[distributor_id]
           ,[customer_name_en] from dw_customer_region_sales_supervisor_rel crss 
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=crss.distributor_id
where pe.partner_id in (56395, 56383, 56393, 56397) and crss.region_name like 'CDM%'

--门店导出
		select o.organization_name "经销商名称", t1.work_shop_name "门店名称", 
		r3.region_name "省", r2.region_name "市", r1.region_name "区", 
		CASE WHEN t1.from_source=1 then '金富力业务' when t1.from_source=2 then '德乐业务' end "门店来源",
		t1.work_shop_address "地址", case when t1.status ='0' then '潜在门店' when t1.status='1' then '已功店'
		when t1.status='3' then '合作门店' end "门店状态",
		(select max(ts1.xg_sj) FROM wx_task_main task_main join wx_task_sub ts1 on ts1.task_main_id=task_main.task_main_id where ts1.org_id=t1.id and task_main.tmb_type_code='TT_2_SD' and ts1.task_status='4') "扫店时间",
		case when t1.status='3' then (select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t1.id order by wss.create_time desc) else null end "录店时间"
		  --,t1.id
		  from wx_t_work_shop t1
		  join wx_t_workshop_partner wp1 on wp1.workshop_id=t1.id
		  join wx_t_organization o on wp1.partner_id=o.id 
		    left join wx_t_region r1 on t1.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
		LEFT JOIN wx_att_file tt_file ON tt_file.uuid=t1.photo_id
		 where o.status='1' and convert(int, t1.status)>=0 
		 --and o.organization_name='福州金富达贸易有限责任公司'
		 --and t1.from_source=1
		 and t1.delete_flag=0
		 order by t1.id
--撤回CDM陈列之星
select s.task_id, o.organization_name, t1.id, t1.work_shop_name,
s.task_status, s.check_evaluation,
s.xg_sj
FROM wx_task_main m
	LEFT JOIN wx_task_sub s ON m.task_main_id = s.task_main_id
	LEFT JOIN wx_t_work_shop t1 ON s.org_id = t1.id
	LEFT JOIN wx_t_organization o ON o.id = m.tenant_id
	LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
WHERE m.tmb_type_code = 'TT_2_CDMCLZX'
	AND s.task_status IN ('3', '4')
	and s.check_evaluation>=0
	and m.tenant_id !=9
	and t1.work_shop_name  in ('三宏快修  (出租  公司)')

--CDM店招
select ma.id, mf.field_value workshop_id, w.work_shop_name store_name, isnull(ma.price,0) price, crss.region_name,
ma.organization_name, ma.apply_person_name
from view_mkt_apply ma
left join wx_t_mkt_field mf on ma.id=mf.mkt_apply_id and mf.field_name='workShopId'
left join wx_t_work_shop w on w.id=mf.field_value
left join dw_customer_region_sales_supervisor_rel crss on ma.partner_id=crss.distributor_id and crss.region_name like 'CDM%'
 INNER JOIN wx_t_partner_o2o_enterprise eop ON eop.distributor_id = ma.partner_id
	 where ma.mkt_type in ('STORE_FRONT', 'STORE_IN_STORE')
	 and ma.price > 0
	 --and ma.asm_audit_status2=1
union all SELECT null, w.id, w.work_shop_name,recruitment_amount, crss.region_name,
crss.customer_name_cn, ''
FROM wx_t_work_shop w
 INNER JOIN wx_t_workshop_partner wp ON w.id = wp.workshop_id
 left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join dw_customer_region_sales_supervisor_rel crss on pe.distributor_id=crss.distributor_id and crss.region_name like 'CDM%'
WHERE recruitment_amount IS NOT NULL AND recruitment_amount>0  	  	

--高桥新产品修复
select dp.* from wx_t_oem_delivery_product dp left join wx_t_oem_delivery d on d.id=dp.delivery_id where dp.codelevel='1' and recv_id='20205'
and product_id in ('572151HRD')
union all 
select dp.id, dp.delivery_id, dp.product_id, t.code1, dp.codelevel, dp.scantime, dp.creation_time, dp.created_by, dp.update_time
 from wx_t_oem_delivery_product dp left join wx_t_oem_delivery d on d.id=dp.delivery_id
left join wx_t_oem_product_packaging_code t on t.code2=dp.code where dp.codelevel='2' and recv_id='20205'
and product_id in ('572151HRD')

--CDM MKT加积分导入 （数据导入权限）
insert into wx_t_import_data_sets (partner_id, import_option, import_option_value) values (56566, 'CDM红包', 'cdm_red_bag_point_import');
--德乐门店DSR.
select o.organization_name "经销商名称",w.id "门店ID", w.work_shop_name "门店名称", u.ch_name "DSR.", u.mobile_tel "手机号", u.login_name "登录名"
from wx_t_work_shop w 
left join wx_task_sub ts on ts.org_id=w.id
left join wx_task_main tm on tm.task_main_id = ts.task_main_id
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on ts.exec_user=u.user_id
where w.from_source=2 and w.delete_flag=0
and o.id!=9 and tm.tmb_type_code = 'TT_2_CLZX'
order by o.id, w.id
--
select distinct crss.sales_cai, crss.sales_name, crss.suppervisor_cai, crss.suppervisor_name, 
crss.region_name, rsc.channel_manager_cai, rsc.channel_manager_name, rsc.sales_channel_name,
 rsc.bu_manager_cai, rsc.bu_manager_name, rsc.bu
from dw_customer_region_sales_supervisor_rel crss
left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
where rsc.bu in ('CDM', 'CIO')
order by rsc.bu, rsc.sales_channel_name,crss.suppervisor_cai, crss.sales_cai

insert into wx_t_region_partner (partner_id, region_id, create_time, update_time)
select 56609, r.id, getdate(), getdate() from wx_t_region r where region_type='D' and (convert(nvarchar(20), id) like '32%' or convert(nvarchar(20), id) like '51%')
and not exists (select 1 from wx_t_region_partner rp where rp.region_id=r.id and rp.partner_id=56609)

  select * 
  --delete r
  from dw_customer_region_sales_supervisor_rel r where r.distributor_id in (
  select distinct bi.[distributor_id] FROM [chemid].chevronDW_Base_202002.dbo.view_customer_region_sales_supervisor_rel_pp bi
  left join [pmpdb01].[dbo].[dw_customer_region_sales_supervisor_rel] crss on bi.distributor_id=crss.distributor_id
	   and bi.customer_name_cn collate Chinese_PRC_CI_AS=crss.customer_name_cn 
	   AND bi.[sales_cai] collate Chinese_PRC_CI_AS = crss.[sales_cai]
	   AND bi.[suppervisor_cai] collate Chinese_PRC_CI_AS=crss.[suppervisor_cai] 
	   and bi.[region_name] collate Chinese_PRC_CI_AS=crss.[region_name]
  where bi.region_name collate Chinese_PRC_CI_AS in (select region_name from dw_region_sales_channel_rel where bu='Indirect') and bi.sales_cai is not null and
	crss.region_name is null and
	not exists (select 1 from wx_t_partner_o2o_enterprise pe left join wx_t_organization o on o.id=pe.partner_id where o.status=0 and pe.distributor_id=bi.distributor_id) and 
   bi.[customer_name_cn] not in ('New business') ) and r.region_name collate Chinese_PRC_CI_AS in (select region_name from dw_region_sales_channel_rel where bu='Indirect')
   
   
  INSERT INTO [dbo].[dw_customer_region_sales_supervisor_rel]
           ([customer_name_cn]
           ,[region_name]
           ,[sales_cai]
           ,[sales_name]
           ,[suppervisor_cai]
           ,[suppervisor_name]
           ,[distributor_id]
           ,[customer_name_en])
  select distinct bi.[customer_name_cn]
      ,bi.[region_name]
      ,bi.[sales_cai]
      ,bi.[sales_name]
      ,bi.[suppervisor_cai]
      ,bi.[suppervisor_name]
      ,bi.[distributor_id]
      ,bi.[customer_name_en] FROM [chemid].chevronDW_Base_202002.dbo.view_customer_region_sales_supervisor_rel_pp bi
  left join [pmpdb01].[dbo].[dw_customer_region_sales_supervisor_rel] crss on bi.distributor_id=crss.distributor_id
	   and bi.customer_name_cn collate Chinese_PRC_CI_AS=crss.customer_name_cn 
	   AND bi.[sales_cai] collate Chinese_PRC_CI_AS = crss.[sales_cai]
	   AND bi.[suppervisor_cai] collate Chinese_PRC_CI_AS=crss.[suppervisor_cai] 
	   and bi.[region_name] collate Chinese_PRC_CI_AS=crss.[region_name]
  where bi.region_name collate Chinese_PRC_CI_AS in (select region_name from dw_region_sales_channel_rel where bu='Indirect') and bi.sales_cai is not null and
	crss.region_name is null and
	not exists (select 1 from wx_t_partner_o2o_enterprise pe left join wx_t_organization o on o.id=pe.partner_id where o.status=0 and pe.distributor_id=bi.distributor_id) and 
   bi.[customer_name_cn] not in ('New business')  and bi.region_name not in ('EC')
   --and bi.[suppervisor_cai] not in ('ZHYM', 'YILE', 'SKCL', 'JNDG')
   
truncate table [customer_sales_region_rel];
insert into [customer_sales_region_rel] ([customer_name_cn]
      ,[customer_name_en]
      ,[region_name]
      ,[sales_cai]
      ,[sales_name]
      ,[suppervisor_cai]
      ,[suppervisor_name]
      ,[distributor_id]
      ,[create_time]
      ,[del_flag])
SELECT [customer_name_cn]
      ,[customer_name_en]
      ,[region_name]
      ,[sales_cai]
      ,[sales_name]
      ,[suppervisor_cai]
      ,[suppervisor_name]
      ,[distributor_id]
      ,getdate() [create_time]
      ,0 [del_flag]
  FROM [chemid].chevronDW_Base_202002.dbo.view_customer_region_sales_supervisor_rel_pp   
  
  
  SELECT job_id, create_time, update_time, job_name, job_group
	, job_status, cron_expression, description, bean_class, is_concurrent
	, spring_id, method_name, task_id, start_time, end_time
	, repetition_type, repetition_frequency, create_user_id, create_user_name, source_task_name
FROM task_schedule_job where job_name in ('caculateCaltexPointFromBiTask', 'calculateCDMPointFromBiTask')

--消息推送撤回
drop table wx_push_message20200310
select * into wx_push_message20200311
--delete 
from wx_push_message where message_main_id=21840
update m set m.status=1 from wx_message m where id=21840

--初始化采购订单
insert into wx_t_partner_bill ([partner_id]
      ,[partner_name]
      ,[init_amount]
      ,[total_amount]
      ,[update_time]
      ,[remark]
      ,[status])
select o.id, o.organization_name, 0, 0, getdate(), null, 1 from wx_t_organization o left join 
wx_t_partner_bill pb on o.id=pb.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where exists (select 1 from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id
left join wx_t_role r on r.role_id=ur.role_id where u.user_id=ur.user_id
and r.ch_role_name in ('Service_Partner_Manager') and (u.type is null or u.type!='1') and u.org_id=o.id) and o.status=1 and o.type=1
and pb.id is null

insert into wx_t_partner_manage_fee ([partner_id]
      ,[partner_name]
      ,[init_amount]
      ,[total_amount]
      ,[update_time]
      ,[remark]
      ,[status])
select o.id, o.organization_name, 0, 0, getdate(), null, 1 from wx_t_organization o left join 
wx_t_partner_manage_fee pb on o.id=pb.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where exists (select 1 from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id
left join wx_t_role r on r.role_id=ur.role_id where u.user_id=ur.user_id
and r.ch_role_name in ('Service_Partner_Manager') and (u.type is null or u.type!='1') and u.org_id=o.id) and o.status=1 and o.type=1
and pb.id is null

select *
--delete p 
from wx_t_properties p where codetype='ReportCenter.lastNotifyDate'

delete rs from wx_t_rolesource rs left join wx_t_role r on r.role_id=rs.role_id 
where rs.source_id in (50284, 50285, 50286, 50287) and r.ch_role_name not in('admin', 'C&I_Signage_Intern')
--菜单业务权限
select * from wx_t_business_fun bf left join wx_t_business_fun_resource bfs on bf.business_fun_code=bfs.business_fun_code where bfs.resource_id='50316'
--删除报表提醒日期
delete p from wx_t_properties p where codetype='ReportCenter.lastNotifyDate'
--定时任务
SELECT * FROM task_schedule_job where spring_id in ('caculateCaltexPointFromBiTask', 'calculateCDMPointFromBiTask')
select * from qrtz_triggers where TRIGGER_NAME='caculateCaltexPointFromBiTask'; 

--放开B2B权限
select * 
--update pe set pe.ext_flag = pe.ext_flag | 2
from wx_t_partner_o2o_enterprise pe where pe.partner_id=56312

[prdsvruser@PP-App01 apache-tomcat-7.0.92]$ wget --header "Cookie:_ga=GA1.2.2137677067.1526377556; _vwo_uuid=D99C614F6C0D3D9FF48CC4A6E1FE20CF0; UM_distinctid=171526c09bf1e8-0ffd315fe58e9d-3f6b490f-144000-171526c09c29; _vwo_uuid_v2=D231AFB8C7817A51DEC855A8BD51679A2|6a98277f4372437b7b804b55a0e53807; ApplicationGatewayAffinity=c57e03b416c6848b33e6cf474556bdd92abb3220c909c419898abcae4f6b913f; ApplicationGatewayAffinityCORS=c57e03b416c6848b33e6cf474556bdd92abb3220c909c419898abcae4f6b913f; JSESSIONID=69FCE0D07984258BF579321B4C75CA6C; CNZZDATA1273411267=1660481951-1523561030-%7C1594177429" http://192.168.0.5/material/dealer/exportPointSummary.do?salesChannel=Consumer

--drop table wx_t_oem_delivery_product
select p.product_id, p.code into wx_t_oem_delivery_product from pmpdb01.dbo.wx_t_oem_delivery_product p left join pmpdb01.dbo.wx_t_oem_delivery d on d.id=p.delivery_id 
where d.creation_time>'2020-07-01' and p.codelevel=1

--菜单权限
select rs.source_id, r.* from wx_t_rolesource rs left join wx_t_role r on r.role_id=rs.role_id where rs.source_id=50076
select * from wx_t_userbase um 
left join wx_t_user u on um.user_id=u.user_id 
where um.rs_type='2' and um.source_id in (50075, 50076) and u.status=1
order by um.source_id

INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES (getdate(), getdate(), N'fixGqQrcode', N'WorkGroup', N'1', N'0 0 6 * * ?', N'每天6点执行', NULL, N'1', N'fixQrcodeSku', N'execute', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '高桥QR绑定')

select top 100 * --into wx_push_message 
--update m set m.[status]=1
from pmpdb01.dbo.wx_push_message m
where --message_id in (87851,86878) and message_main_id in (24735,24728)--
user_id in (132397, 132399)

select * 
--update p set p.code='www.cvx-sh.com'--www1.cvx-sh.com
from wx_t_properties p where --code like '%www.cvx-sh.com%' and 
id=10127

--合并经销商
SELECT * FROM chemid.chevronDW_Base_202002.chv_ods.master_customer_merge C WHERE C.[old amended customer name(chinese)]='辽宁九孚工程有限公司'

select f1.distributor_id, 
f1.apply_amount,isnull(use_fund_amount, 0)+isnull(use_ivi_amount,0)+isnull(use_year_amount,0),
f1.*,o.organization_name
--update f1 set f1.apply_amount=isnull(use_fund_amount, 0)+isnull(use_ivi_amount,0)+isnull(use_year_amount,0)	
from wx_t_v2_elite_fund_form f1
	left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=f1.distributor_id
	left join wx_t_organization o on o.id=pe.partner_id
	where f1.form_status >= 10 and f1.delete_flag=0 
	and (f1.apply_amount!=isnull(use_fund_amount, 0)+isnull(use_ivi_amount,0)+isnull(use_year_amount,0))
	--group by f1.distributor_id
	
select f1.distributor_id, 
f1.final_amount,isnull(use_fund_amount, 0)+isnull(use_ivi_amount,0)+isnull(use_year_amount,0),
f1.*,o.organization_name
--update f1 set f1.final_amount=isnull(use_fund_amount, 0)+isnull(use_ivi_amount,0)+isnull(use_year_amount,0)	
from wx_t_v2_elite_fund_form f1
	left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=f1.distributor_id
	left join wx_t_organization o on o.id=pe.partner_id
	where f1.form_status >= 10 and f1.delete_flag=0 
	and (f1.final_amount!=isnull(use_fund_amount, 0)+isnull(use_ivi_amount,0)+isnull(use_year_amount,0))	