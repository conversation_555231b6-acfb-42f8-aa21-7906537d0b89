package com.sys.quartz.business;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;

import com.chevron.exportdata.Export;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.pms.model.ExportErrorLogForFWOrder;
import com.common.config.MyPropertyConfigurer;
import com.common.util.CommonUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.email.service.EmailSenderService;
import com.sys.log.dao.LogMapper;
import com.sys.log.model.Log;

@Service
public class ErrorLogsService {
	public final Logger logerr = Logger.getLogger(this.getClass());
	@Resource
	WxTPropertiesMapper propertiesMapper;
	@Resource
	LogMapper logMapper;
	@Resource 
	Export exprotService;
	@Resource 
	EmailSenderService emailSenderService;
	public static final String IS_NOT_EXPORT_STATUS = "0";
	public static final String IS_EXPORT_STATUS = "1";
	
	/**
	 * 邮件接收者类型
	 */
	public static final String acceptor_type = "fw.order.error.log.email.accepter";
	/**
	 * 邮件抄送者类型
	 */
	public static final String cc_type = "fw.order.error.log.email.cc";

	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void doScanErrorLogsForFWOrder()
	{
		Date currentDate = new Date();
		String beginDate = "";
		String endDate = "";
		try
		{
			
			/*String timesuffix = "17:00:00";
	    	HashMap<String, Object> reqmap = new HashMap<String, Object>();
			reqmap.put("codetype","scan.log.fw.order.time");
			WxTProperties properties  = propertiesMapper.selectByMap(reqmap);
			if(null!=properties)
			{
				timesuffix = properties.getCode();
				
			}
			Date beginTime = DateUtil.addDays(currentDate, -1);
			beginDate = DateUtil.toDateStr(beginTime)+" "+timesuffix;
			endDate = DateUtil.getCurrentDate(DateUtil.DEFAULT_DATE_PATTERN)+" "+timesuffix;*/
			
			//1.根据类型，时间段获取日志信息
			//获取未有被导出的订单
			List<Log> lstLogs = getLogsForFWOrder(IS_NOT_EXPORT_STATUS);
			if(null==lstLogs || lstLogs.isEmpty())
			{
				logerr.error("=========doScanErrorLogsForFWOrder----lstLogs is null 1030");
				return;
			}
			//2.组装数据，并发送邮件
			boolean isSuccess = sendEmailForErrorLogs(lstLogs,beginDate,endDate);
			if(!isSuccess)
			{
				logerr.error("=========doScanErrorLogsForFWOrder----false");
				throw new Exception("doScanErrorLogsForFWOrder----false");
			}
			//3.更新已经导出之后的订单状态为已导出
			List<Long> exportOrderIds = new ArrayList<Long>();
			for(Log lg:lstLogs)
			{
				exportOrderIds.add(lg.getId());
			}
			
			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
					exportOrderIds, 2000);
			int order_page_account = order_all_page.getTotalPages();
			Map<String,Object> reqMap = new HashMap<String,Object>();
			for (int i = 1; i <= order_page_account; i++) 
			{
				List<Long> tmpOrderIds = order_all_page.getObjects(i);
				reqMap.put("needUpdateLogIds", tmpOrderIds);
				reqMap.put("isExportStatus", IS_EXPORT_STATUS);
				logMapper.updateLogsByMap(reqMap);
			}
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			ex.printStackTrace();
			logerr.error(ex.getMessage());
			
		}
	}

	private List<Log> getLogsForFWOrder(String exportStatus) {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<Log> lstLogs = new ArrayList<Log>();
		reqMap.put("exportStatus", exportStatus);
		reqMap.put("logType", Log.FWORDER_API_TYPE);
		lstLogs = logMapper.getLogsByMap(reqMap);
		return lstLogs;
	}
	
	private List<Log> getLogsForFWOrder(String beginDate, String endDate) {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<Log> lstLogs = new ArrayList<Log>();
		reqMap.put("beginDate", beginDate);
		reqMap.put("endDate", endDate);
		reqMap.put("logType", Log.FWORDER_API_TYPE);
		lstLogs = logMapper.getLogsByMap(reqMap);
		return lstLogs;
	}

	private boolean sendEmailForErrorLogs(List<Log> lstLogs, String beginDate,
			String endDate) {
		boolean isSendResult = true;
		Map<String,Object> dataMap = new HashMap<String,Object>();
		try
		{
			//获取收件人，抄送者信息
			//接受者
			String[] accepters = null;
			//实时从数据库中查询
			String ddaccepters = "";
			Map<String,Object> reqMap1 = new HashMap<String,Object>();
			reqMap1.put("codetype",acceptor_type);
			WxTProperties properties  = propertiesMapper.selectByMap(reqMap1);
			if(null!=properties)
			{
				ddaccepters = properties.getCode();
			}
			if(null==ddaccepters || ddaccepters.isEmpty())
			{
				return false;
			}
			accepters = ddaccepters.split(";");
			
			//邮件抄送者
			String[] ccaccepters = null;
			//实时从数据库中查询
			String ccaccepter = "";
			reqMap1.put("codetype",cc_type);
			WxTProperties properties2  = propertiesMapper.selectByMap(reqMap1);
			if(null!=properties2)
			{
				ccaccepter = properties2.getCode();
			}
			if(null!=ccaccepter)
			{
				ccaccepters = ccaccepter.split(";");
			}
			//组装数据,发送邮件
			List<ExportErrorLogForFWOrder> lstExportData = new ArrayList<ExportErrorLogForFWOrder>();
			int i = 1;
			for(Log lg:lstLogs)
			{
				ExportErrorLogForFWOrder errorLogOrderInfo = new ExportErrorLogForFWOrder();
				errorLogOrderInfo.setIndexNo(""+i);
				errorLogOrderInfo.setUid(lg.getExtProperty4());
				errorLogOrderInfo.setPlanteNo(lg.getExtProperty5());
				errorLogOrderInfo.setServiceTime(lg.getExtProperty6());
				errorLogOrderInfo.setWorkshopName(lg.getExtProperty7());
				errorLogOrderInfo.setReceiveName(lg.getExtProperty8());
				errorLogOrderInfo.setReceiveTel(lg.getExtProperty9());
				errorLogOrderInfo.setReginCode(lg.getExtProperty10());
				errorLogOrderInfo.setWorkshopAdd(lg.getExtProperty11());
				errorLogOrderInfo.setRemark(lg.getExtProperty12());
				errorLogOrderInfo.setErrorInfo(lg.getExtProperty2());
				errorLogOrderInfo.setCreateTime(DateUtils.toDateStrNew(lg.getCreateTime(), 0));
				i++;
				lstExportData.add(errorLogOrderInfo);
			}
			
			logerr.info("sendEmailForErrorLogs acceptname0-----:=========:");
			String acceptname = accepters[0].substring(0, accepters[0].lastIndexOf("@"));
			logerr.info("sendEmailForErrorLogs acceptname1-----:"+acceptname+"=========:");
			if(accepters.length>1)
			{
				acceptname = "ALL";
			}
			
			dataMap.put("acceptUserName", acceptname);
			dataMap.put("msgcontent", "附件是大地或合众服务订单从"+beginDate+"到"+endDate+"调用接口创建失败的订单信息，请处理！");
			dataMap.put("lstOrders", lstExportData);
			//quartz中使用此获取上下文
			ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
			Map<String,Object> returnMap = exprotService.exportDataForCommonAndCreateFile(lstExportData, "com.chevron.pms.model.ExportErrorLogForFWOrder", "大地或合众创建服务订单失败信息", MyPropertyConfigurer
					.getVal("EXPORT_EMAIL_ERROR_FW_ORDER_DATA"), "大地或合众服务订单调用接口失败导出_"+CommonUtil.generateCode("EXPORT"), context);
			File[] attfiles = (File[]) returnMap.get("attfiles");
			isSendResult = emailSenderService.sendEmailForCommon(context,
					accepters, ccaccepters, MyPropertyConfigurer.getVal("mail.fw.order.error.log.subject"),dataMap, attfiles, MyPropertyConfigurer.getVal("mail.fw.order.error.log.ftl"));
			
		}catch (Exception e) {
			e.printStackTrace();
			logerr.error(e.getMessage());
			isSendResult = false;
		}
		return isSendResult;
	}
	
	
	
}
