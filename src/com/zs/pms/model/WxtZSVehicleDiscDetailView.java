package com.zs.pms.model;

public class WxtZSVehicleDiscDetailView {

	private String zstModelId;

    private String zstId;

    private String factoryName;

    private String brandName;

    private String dmpimp;

    private String vehicleType;

    private String vehicleSeries;

    private String vehicleSeriesSop;

    private String vehicleSeriesEop;

    private String zstTypeId;

    private String zstType;

    private String zstModelName;

    private String modelYear;

    private String zstModelMsrp;

    private String zstModelSop;

    private String zstModelEop;

    private String engine;

    private String engineDisplacementCc;

    private String engineDisplacementL;

    private String power;

    private String turboBoost;

    private String gearboxDesc;

    private String drivingModel;
    
    private String discName;

    private String discPartsNo;

    private String discPurchasePrice;

    private String discSalePrice;
    
    private String suggest1;
    
    private String suggest2;
    
    private String suggest3;
    
    private String suggest4;
    
    private String suggest5;
    
    private String suggest6;
    
    private String suggest7;
    
    private String suggest8;
    
    private String suggest9;
    
    private String suggest10;
    
    private String suggest11;
    
    private String suggest12;
    
    private String suggest13;
    
    private String suggest14;
    
    private String suggest15;
    
    private String suggest16;
    
    private String suggest17;
    
    private String suggest18;
    
    private String suggest19;
    
    private String suggest20;
    
    private String suggest21;
    
    private String suggest22;
    
    private String suggest23;
    
    private String suggest24;
    
    private String suggest25;
    
    private String suggest26;
    
    private String suggest27;
    
    private String suggest28;

	public String getZstModelId() {
		return zstModelId;
	}

	public void setZstModelId(String zstModelId) {
		this.zstModelId = zstModelId;
	}

	public String getZstId() {
		return zstId;
	}

	public void setZstId(String zstId) {
		this.zstId = zstId;
	}

	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getDmpimp() {
		return dmpimp;
	}

	public void setDmpimp(String dmpimp) {
		this.dmpimp = dmpimp;
	}

	public String getVehicleType() {
		return vehicleType;
	}

	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType;
	}

	public String getVehicleSeries() {
		return vehicleSeries;
	}

	public void setVehicleSeries(String vehicleSeries) {
		this.vehicleSeries = vehicleSeries;
	}

	public String getVehicleSeriesSop() {
		return vehicleSeriesSop;
	}

	public void setVehicleSeriesSop(String vehicleSeriesSop) {
		this.vehicleSeriesSop = vehicleSeriesSop;
	}

	public String getVehicleSeriesEop() {
		return vehicleSeriesEop;
	}

	public void setVehicleSeriesEop(String vehicleSeriesEop) {
		this.vehicleSeriesEop = vehicleSeriesEop;
	}

	public String getZstTypeId() {
		return zstTypeId;
	}

	public void setZstTypeId(String zstTypeId) {
		this.zstTypeId = zstTypeId;
	}

	public String getZstType() {
		return zstType;
	}

	public void setZstType(String zstType) {
		this.zstType = zstType;
	}

	public String getZstModelName() {
		return zstModelName;
	}

	public void setZstModelName(String zstModelName) {
		this.zstModelName = zstModelName;
	}

	public String getModelYear() {
		return modelYear;
	}

	public void setModelYear(String modelYear) {
		this.modelYear = modelYear;
	}

	public String getZstModelMsrp() {
		return zstModelMsrp;
	}

	public void setZstModelMsrp(String zstModelMsrp) {
		this.zstModelMsrp = zstModelMsrp;
	}

	public String getZstModelSop() {
		return zstModelSop;
	}

	public void setZstModelSop(String zstModelSop) {
		this.zstModelSop = zstModelSop;
	}

	public String getZstModelEop() {
		return zstModelEop;
	}

	public void setZstModelEop(String zstModelEop) {
		this.zstModelEop = zstModelEop;
	}

	public String getEngine() {
		return engine;
	}

	public void setEngine(String engine) {
		this.engine = engine;
	}

	public String getEngineDisplacementCc() {
		return engineDisplacementCc;
	}

	public void setEngineDisplacementCc(String engineDisplacementCc) {
		this.engineDisplacementCc = engineDisplacementCc;
	}

	public String getEngineDisplacementL() {
		return engineDisplacementL;
	}

	public void setEngineDisplacementL(String engineDisplacementL) {
		this.engineDisplacementL = engineDisplacementL;
	}

	public String getPower() {
		return power;
	}

	public void setPower(String power) {
		this.power = power;
	}

	public String getTurboBoost() {
		return turboBoost;
	}

	public void setTurboBoost(String turboBoost) {
		this.turboBoost = turboBoost;
	}

	public String getGearboxDesc() {
		return gearboxDesc;
	}

	public void setGearboxDesc(String gearboxDesc) {
		this.gearboxDesc = gearboxDesc;
	}

	public String getDrivingModel() {
		return drivingModel;
	}

	public void setDrivingModel(String drivingModel) {
		this.drivingModel = drivingModel;
	}

	public String getDiscName() {
		return discName;
	}

	public void setDiscName(String discName) {
		this.discName = discName;
	}

	public String getDiscPartsNo() {
		return discPartsNo;
	}

	public void setDiscPartsNo(String discPartsNo) {
		this.discPartsNo = discPartsNo;
	}

	public String getDiscPurchasePrice() {
		return discPurchasePrice;
	}

	public void setDiscPurchasePrice(String discPurchasePrice) {
		this.discPurchasePrice = discPurchasePrice;
	}

	public String getDiscSalePrice() {
		return discSalePrice;
	}

	public void setDiscSalePrice(String discSalePrice) {
		this.discSalePrice = discSalePrice;
	}

	public String getSuggest1() {
		return suggest1;
	}

	public void setSuggest1(String suggest1) {
		this.suggest1 = suggest1;
	}

	public String getSuggest2() {
		return suggest2;
	}

	public void setSuggest2(String suggest2) {
		this.suggest2 = suggest2;
	}

	public String getSuggest3() {
		return suggest3;
	}

	public void setSuggest3(String suggest3) {
		this.suggest3 = suggest3;
	}

	public String getSuggest4() {
		return suggest4;
	}

	public void setSuggest4(String suggest4) {
		this.suggest4 = suggest4;
	}

	public String getSuggest5() {
		return suggest5;
	}

	public void setSuggest5(String suggest5) {
		this.suggest5 = suggest5;
	}

	public String getSuggest6() {
		return suggest6;
	}

	public void setSuggest6(String suggest6) {
		this.suggest6 = suggest6;
	}

	public String getSuggest7() {
		return suggest7;
	}

	public void setSuggest7(String suggest7) {
		this.suggest7 = suggest7;
	}

	public String getSuggest8() {
		return suggest8;
	}

	public void setSuggest8(String suggest8) {
		this.suggest8 = suggest8;
	}

	public String getSuggest9() {
		return suggest9;
	}

	public void setSuggest9(String suggest9) {
		this.suggest9 = suggest9;
	}

	public String getSuggest10() {
		return suggest10;
	}

	public void setSuggest10(String suggest10) {
		this.suggest10 = suggest10;
	}

	public String getSuggest11() {
		return suggest11;
	}

	public void setSuggest11(String suggest11) {
		this.suggest11 = suggest11;
	}

	public String getSuggest12() {
		return suggest12;
	}

	public void setSuggest12(String suggest12) {
		this.suggest12 = suggest12;
	}

	public String getSuggest13() {
		return suggest13;
	}

	public void setSuggest13(String suggest13) {
		this.suggest13 = suggest13;
	}

	public String getSuggest14() {
		return suggest14;
	}

	public void setSuggest14(String suggest14) {
		this.suggest14 = suggest14;
	}

	public String getSuggest15() {
		return suggest15;
	}

	public void setSuggest15(String suggest15) {
		this.suggest15 = suggest15;
	}

	public String getSuggest16() {
		return suggest16;
	}

	public void setSuggest16(String suggest16) {
		this.suggest16 = suggest16;
	}

	public String getSuggest17() {
		return suggest17;
	}

	public void setSuggest17(String suggest17) {
		this.suggest17 = suggest17;
	}

	public String getSuggest18() {
		return suggest18;
	}

	public void setSuggest18(String suggest18) {
		this.suggest18 = suggest18;
	}

	public String getSuggest19() {
		return suggest19;
	}

	public void setSuggest19(String suggest19) {
		this.suggest19 = suggest19;
	}

	public String getSuggest20() {
		return suggest20;
	}

	public void setSuggest20(String suggest20) {
		this.suggest20 = suggest20;
	}

	public String getSuggest21() {
		return suggest21;
	}

	public void setSuggest21(String suggest21) {
		this.suggest21 = suggest21;
	}

	public String getSuggest22() {
		return suggest22;
	}

	public void setSuggest22(String suggest22) {
		this.suggest22 = suggest22;
	}

	public String getSuggest23() {
		return suggest23;
	}

	public void setSuggest23(String suggest23) {
		this.suggest23 = suggest23;
	}

	public String getSuggest24() {
		return suggest24;
	}

	public void setSuggest24(String suggest24) {
		this.suggest24 = suggest24;
	}

	public String getSuggest25() {
		return suggest25;
	}

	public void setSuggest25(String suggest25) {
		this.suggest25 = suggest25;
	}

	public String getSuggest26() {
		return suggest26;
	}

	public void setSuggest26(String suggest26) {
		this.suggest26 = suggest26;
	}

	public String getSuggest27() {
		return suggest27;
	}

	public void setSuggest27(String suggest27) {
		this.suggest27 = suggest27;
	}

	public String getSuggest28() {
		return suggest28;
	}

	public void setSuggest28(String suggest28) {
		this.suggest28 = suggest28;
	}
    
	
}
