select wp.partner_id, o.organization_name "经销商名称", u.ch_name "DSR", w.dms_key, dw.Distributor_Code, dw.Customer_Code, dw.Customer_Name,
w.id, w.work_shop_name "商用油门店", w.work_shop_address "门店地址",
case when w.from_source=1 then '乘用车业务' when w.from_source=2 then '商用油业务' else convert(nvarchar(10),w.from_source) end "门店来源",
(case when w.status='0' then '潜在' when w.status='3' then '合作'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' else w.status end) "门店状态", w.create_time "录入时间" from wx_t_work_shop w
left join dw_dms_workshop dw on dw.[Distributor_Code] + '/' + dw.[Customer_Code]=w.dms_key
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=w.excute_user_id
where w.delete_flag=0 
and exists (select 1 from wx_t_work_shop w2 where w2.dms_key=w.dms_key having count(1)>1)
order by o.id, w.dms_key, w.from_source,w.id