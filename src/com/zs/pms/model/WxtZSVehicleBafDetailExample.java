package com.zs.pms.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxtZSVehicleBafDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WxtZSVehicleBafDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNull() {
            addCriterion("t_model_id is null");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNotNull() {
            addCriterion("t_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andTModelIdEqualTo(String value) {
            addCriterion("t_model_id =", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotEqualTo(String value) {
            addCriterion("t_model_id <>", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThan(String value) {
            addCriterion("t_model_id >", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_id >=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThan(String value) {
            addCriterion("t_model_id <", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThanOrEqualTo(String value) {
            addCriterion("t_model_id <=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLike(String value) {
            addCriterion("t_model_id like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotLike(String value) {
            addCriterion("t_model_id not like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdIn(List<String> values) {
            addCriterion("t_model_id in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotIn(List<String> values) {
            addCriterion("t_model_id not in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdBetween(String value1, String value2) {
            addCriterion("t_model_id between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotBetween(String value1, String value2) {
            addCriterion("t_model_id not between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andBafNameIsNull() {
            addCriterion("baf_name is null");
            return (Criteria) this;
        }

        public Criteria andBafNameIsNotNull() {
            addCriterion("baf_name is not null");
            return (Criteria) this;
        }

        public Criteria andBafNameEqualTo(String value) {
            addCriterion("baf_name =", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameNotEqualTo(String value) {
            addCriterion("baf_name <>", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameGreaterThan(String value) {
            addCriterion("baf_name >", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameGreaterThanOrEqualTo(String value) {
            addCriterion("baf_name >=", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameLessThan(String value) {
            addCriterion("baf_name <", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameLessThanOrEqualTo(String value) {
            addCriterion("baf_name <=", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameLike(String value) {
            addCriterion("baf_name like", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameNotLike(String value) {
            addCriterion("baf_name not like", value, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameIn(List<String> values) {
            addCriterion("baf_name in", values, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameNotIn(List<String> values) {
            addCriterion("baf_name not in", values, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameBetween(String value1, String value2) {
            addCriterion("baf_name between", value1, value2, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNameNotBetween(String value1, String value2) {
            addCriterion("baf_name not between", value1, value2, "bafName");
            return (Criteria) this;
        }

        public Criteria andBafNoIsNull() {
            addCriterion("baf_no is null");
            return (Criteria) this;
        }

        public Criteria andBafNoIsNotNull() {
            addCriterion("baf_no is not null");
            return (Criteria) this;
        }

        public Criteria andBafNoEqualTo(String value) {
            addCriterion("baf_no =", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoNotEqualTo(String value) {
            addCriterion("baf_no <>", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoGreaterThan(String value) {
            addCriterion("baf_no >", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoGreaterThanOrEqualTo(String value) {
            addCriterion("baf_no >=", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoLessThan(String value) {
            addCriterion("baf_no <", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoLessThanOrEqualTo(String value) {
            addCriterion("baf_no <=", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoLike(String value) {
            addCriterion("baf_no like", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoNotLike(String value) {
            addCriterion("baf_no not like", value, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoIn(List<String> values) {
            addCriterion("baf_no in", values, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoNotIn(List<String> values) {
            addCriterion("baf_no not in", values, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoBetween(String value1, String value2) {
            addCriterion("baf_no between", value1, value2, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafNoNotBetween(String value1, String value2) {
            addCriterion("baf_no not between", value1, value2, "bafNo");
            return (Criteria) this;
        }

        public Criteria andBafLevelIsNull() {
            addCriterion("baf_level is null");
            return (Criteria) this;
        }

        public Criteria andBafLevelIsNotNull() {
            addCriterion("baf_level is not null");
            return (Criteria) this;
        }

        public Criteria andBafLevelEqualTo(String value) {
            addCriterion("baf_level =", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelNotEqualTo(String value) {
            addCriterion("baf_level <>", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelGreaterThan(String value) {
            addCriterion("baf_level >", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelGreaterThanOrEqualTo(String value) {
            addCriterion("baf_level >=", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelLessThan(String value) {
            addCriterion("baf_level <", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelLessThanOrEqualTo(String value) {
            addCriterion("baf_level <=", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelLike(String value) {
            addCriterion("baf_level like", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelNotLike(String value) {
            addCriterion("baf_level not like", value, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelIn(List<String> values) {
            addCriterion("baf_level in", values, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelNotIn(List<String> values) {
            addCriterion("baf_level not in", values, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelBetween(String value1, String value2) {
            addCriterion("baf_level between", value1, value2, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafLevelNotBetween(String value1, String value2) {
            addCriterion("baf_level not between", value1, value2, "bafLevel");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionIsNull() {
            addCriterion("baf_consumption is null");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionIsNotNull() {
            addCriterion("baf_consumption is not null");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionEqualTo(String value) {
            addCriterion("baf_consumption =", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionNotEqualTo(String value) {
            addCriterion("baf_consumption <>", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionGreaterThan(String value) {
            addCriterion("baf_consumption >", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionGreaterThanOrEqualTo(String value) {
            addCriterion("baf_consumption >=", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionLessThan(String value) {
            addCriterion("baf_consumption <", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionLessThanOrEqualTo(String value) {
            addCriterion("baf_consumption <=", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionLike(String value) {
            addCriterion("baf_consumption like", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionNotLike(String value) {
            addCriterion("baf_consumption not like", value, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionIn(List<String> values) {
            addCriterion("baf_consumption in", values, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionNotIn(List<String> values) {
            addCriterion("baf_consumption not in", values, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionBetween(String value1, String value2) {
            addCriterion("baf_consumption between", value1, value2, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafConsumptionNotBetween(String value1, String value2) {
            addCriterion("baf_consumption not between", value1, value2, "bafConsumption");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointIsNull() {
            addCriterion("baf_freezepoint is null");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointIsNotNull() {
            addCriterion("baf_freezepoint is not null");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointEqualTo(String value) {
            addCriterion("baf_freezepoint =", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointNotEqualTo(String value) {
            addCriterion("baf_freezepoint <>", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointGreaterThan(String value) {
            addCriterion("baf_freezepoint >", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointGreaterThanOrEqualTo(String value) {
            addCriterion("baf_freezepoint >=", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointLessThan(String value) {
            addCriterion("baf_freezepoint <", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointLessThanOrEqualTo(String value) {
            addCriterion("baf_freezepoint <=", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointLike(String value) {
            addCriterion("baf_freezepoint like", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointNotLike(String value) {
            addCriterion("baf_freezepoint not like", value, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointIn(List<String> values) {
            addCriterion("baf_freezepoint in", values, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointNotIn(List<String> values) {
            addCriterion("baf_freezepoint not in", values, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointBetween(String value1, String value2) {
            addCriterion("baf_freezepoint between", value1, value2, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafFreezepointNotBetween(String value1, String value2) {
            addCriterion("baf_freezepoint not between", value1, value2, "bafFreezepoint");
            return (Criteria) this;
        }

        public Criteria andBafColorIsNull() {
            addCriterion("baf_color is null");
            return (Criteria) this;
        }

        public Criteria andBafColorIsNotNull() {
            addCriterion("baf_color is not null");
            return (Criteria) this;
        }

        public Criteria andBafColorEqualTo(String value) {
            addCriterion("baf_color =", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorNotEqualTo(String value) {
            addCriterion("baf_color <>", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorGreaterThan(String value) {
            addCriterion("baf_color >", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorGreaterThanOrEqualTo(String value) {
            addCriterion("baf_color >=", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorLessThan(String value) {
            addCriterion("baf_color <", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorLessThanOrEqualTo(String value) {
            addCriterion("baf_color <=", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorLike(String value) {
            addCriterion("baf_color like", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorNotLike(String value) {
            addCriterion("baf_color not like", value, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorIn(List<String> values) {
            addCriterion("baf_color in", values, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorNotIn(List<String> values) {
            addCriterion("baf_color not in", values, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorBetween(String value1, String value2) {
            addCriterion("baf_color between", value1, value2, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafColorNotBetween(String value1, String value2) {
            addCriterion("baf_color not between", value1, value2, "bafColor");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceIsNull() {
            addCriterion("baf_purchase_price is null");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceIsNotNull() {
            addCriterion("baf_purchase_price is not null");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceEqualTo(String value) {
            addCriterion("baf_purchase_price =", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceNotEqualTo(String value) {
            addCriterion("baf_purchase_price <>", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceGreaterThan(String value) {
            addCriterion("baf_purchase_price >", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceGreaterThanOrEqualTo(String value) {
            addCriterion("baf_purchase_price >=", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceLessThan(String value) {
            addCriterion("baf_purchase_price <", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceLessThanOrEqualTo(String value) {
            addCriterion("baf_purchase_price <=", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceLike(String value) {
            addCriterion("baf_purchase_price like", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceNotLike(String value) {
            addCriterion("baf_purchase_price not like", value, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceIn(List<String> values) {
            addCriterion("baf_purchase_price in", values, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceNotIn(List<String> values) {
            addCriterion("baf_purchase_price not in", values, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceBetween(String value1, String value2) {
            addCriterion("baf_purchase_price between", value1, value2, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafPurchasePriceNotBetween(String value1, String value2) {
            addCriterion("baf_purchase_price not between", value1, value2, "bafPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceIsNull() {
            addCriterion("baf_sale_price is null");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceIsNotNull() {
            addCriterion("baf_sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceEqualTo(String value) {
            addCriterion("baf_sale_price =", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceNotEqualTo(String value) {
            addCriterion("baf_sale_price <>", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceGreaterThan(String value) {
            addCriterion("baf_sale_price >", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceGreaterThanOrEqualTo(String value) {
            addCriterion("baf_sale_price >=", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceLessThan(String value) {
            addCriterion("baf_sale_price <", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceLessThanOrEqualTo(String value) {
            addCriterion("baf_sale_price <=", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceLike(String value) {
            addCriterion("baf_sale_price like", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceNotLike(String value) {
            addCriterion("baf_sale_price not like", value, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceIn(List<String> values) {
            addCriterion("baf_sale_price in", values, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceNotIn(List<String> values) {
            addCriterion("baf_sale_price not in", values, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceBetween(String value1, String value2) {
            addCriterion("baf_sale_price between", value1, value2, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andBafSalePriceNotBetween(String value1, String value2) {
            addCriterion("baf_sale_price not between", value1, value2, "bafSalePrice");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNull() {
            addCriterion("import_time is null");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNotNull() {
            addCriterion("import_time is not null");
            return (Criteria) this;
        }

        public Criteria andImportTimeEqualTo(Date value) {
            addCriterion("import_time =", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotEqualTo(Date value) {
            addCriterion("import_time <>", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThan(Date value) {
            addCriterion("import_time >", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("import_time >=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThan(Date value) {
            addCriterion("import_time <", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThanOrEqualTo(Date value) {
            addCriterion("import_time <=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeIn(List<Date> values) {
            addCriterion("import_time in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotIn(List<Date> values) {
            addCriterion("import_time not in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeBetween(Date value1, Date value2) {
            addCriterion("import_time between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotBetween(Date value1, Date value2) {
            addCriterion("import_time not between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}