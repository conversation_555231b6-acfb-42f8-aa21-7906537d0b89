insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'home_page_v2_todo', 1024, 1, '首页。1024-经销商管理员', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'home_page_v2_todo', 1024, 1, '首页。1024-经销商管理员', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'home_page_v2_todo', 1024, 1, '首页。1024-经销商管理员', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'home_page_v2_todo', 1024, 1, '首页。1024-经销商管理员', 1, getdate());

update h set h.channel_weight=3, permission_weight=1027
from wx_t_homepagev2_entry h where
    usageFlag is not null and id=42