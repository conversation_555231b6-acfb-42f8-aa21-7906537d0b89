--���ݻ������ֻ���
select x.* from (
select workshop.work_shop_name, ol.sku,product.name, ol.amount,
CONVERT(float,t_points.product_litre) * ol.amount as liter_count, t_order.create_time,
CONVERT(float,t_points.points) *  ol.amount as points,
(select top 1 we.code from wx_t_workshop_employee we where we.employee_type='Owner' and we.workshop_id=workshop.id  order by we.id desc) mechanic_code
from wx_t_order_line ol 
left join wx_t_product product on product.sku = ol.sku 
left join wx_product_points t_points on t_points.sku = ol.sku 
left join wx_t_order t_order on t_order.id = ol.order_id
left join wx_t_work_shop workshop on workshop.id = t_order.work_shop_id
where --product.product_channel = 'C&I' and 
t_order.order_type = 'PA' AND t_order.source = 'PAOD' 
and t_order.source_id = 'PAOD1'
--order by ol.create_time desc 
) x where x.points is not null
order by x.work_shop_name asc ,x.create_time asc 

/*
select workshop.work_shop_name, ol.sku,product.name, ol.amount,
CONVERT(float,product.capacity) * ol.amount as liter_count, t_order.create_time,CONVERT(float,t_points.points) *  ol.amount as points  
from wx_t_order_line ol 
left join wx_t_product product on product.sku = ol.sku 
left join wx_product_points t_points on t_points.sku = ol.sku 
left join wx_t_order t_order on t_order.id = ol.order_id
left join wx_t_work_shop workshop on workshop.id = t_order.work_shop_id
where product.product_channel = 'C&I'  and t_order.order_type = 'PA' AND t_order.source = 'PAOD' and t_order.source_id = 'PAOD1'
order by workshop.work_shop_name asc ,ol.create_time asc */