CREATE PROCEDURE [dbo].[pr_syn_retailer_region] 
( 
	@syn_type nvarchar(50) NULL,
    @partner_id bigint NULL
)
as
begin
	set xact_abort on
	SET NOCOUNT ON
	if @syn_type='ALL'
	begin
			delete rp1
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			join wx_t_region_partner rp1 on rp1.partner_id=o.id
			where o.type=3
			and not exists (select 1 from wx_t_region_partner irp1
				where irp1.partner_id=pe.ext_property1 and irp1.region_id=rp1.region_id and irp1.channel_weight=rp1.channel_weight)

	end 
	else if @syn_type='DISTRIBUTOR'
	begin
		if @partner_id is null
		begin
			PRINT 'partner id is null'
			RETURN
		end 
		else
		begin
			delete rp1
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			join wx_t_region_partner rp1 on rp1.partner_id=o.id
			where o.type=3 and pe.ext_property1=@partner_id
			and not exists (select 1 from wx_t_region_partner irp1
				where irp1.partner_id=pe.ext_property1 and irp1.region_id=rp1.region_id and irp1.channel_weight=rp1.channel_weight)
		end 
	end 
	else if @syn_type='RETAILER'
	begin
		if @partner_id is null
		begin
			PRINT 'retailer id is null'
			RETURN
		end 
		else
		begin
			PRINT 'undo RETAILER'
		end 
	end 
	else 
	begin
		PRINT 'unkown syn_type'
		RETURN
	end 
END