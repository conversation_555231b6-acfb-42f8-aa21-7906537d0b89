<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.RegionVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.RegionVo" >
    <id column="id" property="id" jdbcType="BIGINT" /> 
    <result column="region_code" property="regionCode" jdbcType="NVARCHAR" />
    <result column="region_name" property="regionName" jdbcType="NVARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="region_type" property="regionType" jdbcType="NVARCHAR" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="is_leaf" property="isLeaf" jdbcType="BIT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    
    <result column="province_id" property="provinceId" jdbcType="BIGINT" />
    <result column="city_id" property="cityId" jdbcType="BIGINT" />
    
    <result column="city_name" property="cityName" jdbcType="NVARCHAR" />
    <result column="province_name" property="provinceName" jdbcType="NVARCHAR" />
    <result column="city_code" property="cityCode" jdbcType="NVARCHAR" />
    <result column="province_code" property="provinceCode" jdbcType="NVARCHAR" />
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, region_code, region_name, parent_id, region_type, sort, status, is_leaf, create_time, 
    creator, update_time, updator, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.RegionVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_region
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_region
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selChildrenByParentId" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_t_region where parent_id=#{parentId, jdbcType=BIGINT} order by create_time
  </select>
  <select id="selRegionByParams" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_t_region r where 1=1
    <if test="parentId != null">
        and parent_id=#{parentId, jdbcType=BIGINT}
    </if>
    <if test="loginUserId != null">
     and (not exists (select 1 from wx_t_user_charge_region ucr where ucr.user_id=#{loginUserId}) or 
     ((r.region_type='P' and exists (select 1 from wx_t_user_charge_region ucr join wx_t_region t1 on t1.id=ucr.charge_region_id join wx_t_region t2 on t2.id=t1.parent_id where ucr.user_id=#{loginUserId} and t2.parent_id=r.id))
     or (r.region_type='C' and exists (select 1 from wx_t_user_charge_region ucr join wx_t_region t1 on t1.id=ucr.charge_region_id where ucr.user_id=#{loginUserId} and t1.parent_id=r.id))
     or (r.region_type='D' and exists (select 1 from wx_t_user_charge_region ucr where ucr.user_id=#{loginUserId} and ucr.charge_region_id=r.id))))
    </if>
    <if test="partnerId != null">
    	and ((r.region_type='P' and exists (select 1 from wx_t_region_partner rp join wx_t_region t1 on t1.id=rp.region_id join wx_t_region t2 on t2.id=t1.parent_id where rp.partner_id=#{partnerId, jdbcType=BIGINT} and t2.parent_id=r.id
    	<if test="channelWeight != null">
    	and rp.channel_weight&amp;${channelWeight}>0
    	</if>
    	))
or (r.region_type='C' and exists (select 1 from wx_t_region_partner rp join wx_t_region t1 on t1.id=rp.region_id where rp.partner_id=#{partnerId, jdbcType=BIGINT} and t1.parent_id=r.id
    	<if test="channelWeight != null">
    	and rp.channel_weight&amp;${channelWeight}>0
    	</if>
))
or (r.region_type='D' and exists (select 1 from wx_t_region_partner rp where rp.partner_id=#{partnerId, jdbcType=BIGINT} and rp.region_id=r.id
    	<if test="channelWeight != null">
    	and rp.channel_weight&amp;${channelWeight}>0
    	</if>
)))
    </if>
  </select>
  <select id="selDistByParams" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_t_region r where r.region_type='D'
    <if test="provinceId != null">
        and exists (select 1 from wx_t_region r1 where r1.id=r.parent_id and r1.parent_id=#{provinceId, jdbcType=BIGINT})
    </if>
    <if test="partnerId != null">
    	 and exists (select 1 from wx_t_region_partner rp where rp.partner_id=#{partnerId, jdbcType=BIGINT} and rp.region_id=r.id)
    </if>
  </select>
  
  <resultMap id="RegionNode" type="java.util.HashMap">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="region_name" jdbcType="VARCHAR" property="text" />
  </resultMap>
  <select id="selFullRegionByDist" parameterType="long" resultMap="RegionNode">
    select
    <include refid="Base_Column_List" />
    from wx_t_region where id=(select t1.parent_id from wx_t_region t1 left join wx_t_region t2 on t1.id=t2.parent_id where t2.id=#{distId, jdbcType=BIGINT})
    union all
    select
    <include refid="Base_Column_List" />
    from wx_t_region where id=(select parent_id from wx_t_region where id=#{distId, jdbcType=BIGINT})
    union all
    select
    <include refid="Base_Column_List" />
    from wx_t_region where id=#{distId, jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_region
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.RegionVo" >
    insert into wx_t_region (id, region_code, region_name, 
      parent_id, region_type, sort, 
      status, is_leaf, create_time, 
      creator, update_time, updator, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{regionCode,jdbcType=NVARCHAR}, #{regionName,jdbcType=NVARCHAR}, 
      #{parentId,jdbcType=BIGINT}, #{regionType,jdbcType=NVARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{isLeaf,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=NVARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=NVARCHAR}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.RegionVo" >
    insert into wx_t_region
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="regionCode != null" >
        region_code,
      </if>
      <if test="regionName != null" >
        region_name,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="regionType != null" >
        region_type,
      </if>
      <if test="sort != null" >
        sort,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="isLeaf != null" >
        is_leaf,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="regionCode != null" >
        #{regionCode,jdbcType=NVARCHAR},
      </if>
      <if test="regionName != null" >
        #{regionName,jdbcType=NVARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="regionType != null" >
        #{regionType,jdbcType=NVARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isLeaf != null" >
        #{isLeaf,jdbcType=BIT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_region
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.regionCode != null" >
        region_code = #{record.regionCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.regionName != null" >
        region_name = #{record.regionName,jdbcType=NVARCHAR},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.regionType != null" >
        region_type = #{record.regionType,jdbcType=NVARCHAR},
      </if>
      <if test="record.sort != null" >
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.isLeaf != null" >
        is_leaf = #{record.isLeaf,jdbcType=BIT},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=NVARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updator != null" >
        updator = #{record.updator,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_region
    set id = #{record.id,jdbcType=BIGINT},
      region_code = #{record.regionCode,jdbcType=NVARCHAR},
      region_name = #{record.regionName,jdbcType=NVARCHAR},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      region_type = #{record.regionType,jdbcType=NVARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      is_leaf = #{record.isLeaf,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=NVARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      updator = #{record.updator,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.RegionVo" >
    update wx_t_region
    <set >
      <if test="regionCode != null" >
        region_code = #{regionCode,jdbcType=NVARCHAR},
      </if>
      <if test="regionName != null" >
        region_name = #{regionName,jdbcType=NVARCHAR},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="regionType != null" >
        region_type = #{regionType,jdbcType=NVARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="isLeaf != null" >
        is_leaf = #{isLeaf,jdbcType=BIT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.RegionVo" >
    update wx_t_region
    set region_code = #{regionCode,jdbcType=NVARCHAR},
      region_name = #{regionName,jdbcType=NVARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      region_type = #{regionType,jdbcType=NVARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      is_leaf = #{isLeaf,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=NVARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- add by bo.liu 0818 -->
  <select id="getRegionParentIdByRegionId" parameterType="java.lang.Long" resultMap="BaseResultMap">
  	SELECT
		(SELECT tt_region1.parent_id FROM wx_t_region tt_region1 WHERE tt_region1.id=tt_region.parent_id) province_id,
		(SELECT tt_region.parent_id) city_id
	FROM
		wx_t_region tt_region
	WHERE
		tt_region.id = #{regionId,jdbcType=BIGINT}
  
  </select>
  
  <!-- end -->
  <!-- 定义一个 TreeNode-->
  <resultMap id="regionTreeNode" type="com.chevron.pms.model.RegionVoTree" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="region_code" property="regionCode" jdbcType="NVARCHAR" />
    <result column="region_name" property="regionName" jdbcType="NVARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="region_type" property="regionType" jdbcType="NVARCHAR" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="is_leaf" property="isLeaf" jdbcType="BIT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    
    <result column="province_id" property="provinceId" jdbcType="BIGINT" />
    <result column="city_id" property="cityId" jdbcType="BIGINT" />
  </resultMap>
  
  
  <!-- add by ken.zhang 31/8/2016 -->
  <select id="setAllRegion" resultMap="BaseResultMap">
  <!--  除去跟节点的所有数据      TODO: organization_code 可能需要变化-->   
    select * from wx_t_region  where region_code!='${regionCode}' AND status !=0
  </select>
  
  
  <select id="selectByRegionCode" parameterType="map"  resultMap="BaseResultMap" >
  <!--  读取根节点-->   
    select * from wx_t_region  where region_code= '6' AND status !=0
  </select>
  
    <select id="selectByForTreeNodes" parameterType="map"  resultMap="regionTreeNode" >
  <!--  读取所有数据除了根节点-->   
    select * from wx_t_region  where region_code!= 6 AND status !=0
  </select>
  
   <insert id="insertRegionSelective" parameterType="com.chevron.pms.model.RegionVoTree"  >
    insert into wx_t_region
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="regionCode != null" >
        region_code,
      </if>
      <if test="regionName != null" >
        region_name,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="regionType != null" >
        region_type,
      </if>
      <if test="sort != null" >
        sort,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="isLeaf != null" >
        is_leaf,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="regionCode != null" >
        #{regionCode,jdbcType=NVARCHAR},
      </if>
      <if test="regionName != null" >
        #{regionName,jdbcType=NVARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="regionType != null" >
        #{regionType,jdbcType=NVARCHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isLeaf != null" >
        #{isLeaf,jdbcType=BIT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectCountAllChildRegion" parameterType="java.lang.Long" resultType="int">
  	select count(*) from wx_t_region where parent_id=#{id};
   </select>
  
    <update id="updateRegion" parameterType="com.chevron.pms.model.RegionVoTree" >
    update wx_t_region
    <set >
      <if test="regionCode != null" >
        region_code = #{regionCode,jdbcType=NVARCHAR},
      </if>
      <if test="regionName != null" >
        region_name = #{regionName,jdbcType=NVARCHAR},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="regionType != null" >
        region_type = #{regionType,jdbcType=NVARCHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="isLeaf != null" >
        is_leaf = #{isLeaf,jdbcType=BIT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectByRegionByParentId"   resultMap="regionTreeNode" parameterType="map" >
      SELECT
          *
      From
          wx_t_region  tt_region
      WHERE
          tt_region.parent_id = #{parentId,jdbcType=BIGINT}
      and
          tt_region.status = 1
  </select>
  <select id="selectDistByProvinceId" parameterType="long" resultMap="BaseResultMap">
      select
          region1.*, region2.id  AS cityId, region2.region_name AS cityName
          from wx_t_region region1
          LEFT JOIN wx_t_region region2 ON region2.id = region1.parent_id
          where region1.parent_id IN (SELECT id FROM wx_t_region region2 WHERE parent_id = #{parentId, jdbcType=BIGINT})

  </select>
  <resultMap id="RegionVoMap" type="com.chevron.pms.model.RegionVo" >
      <id column="id" property="id" jdbcType="BIGINT" />
      <result column="region_code" property="regionCode" jdbcType="NVARCHAR" />
      <result column="region_name" property="regionName" jdbcType="NVARCHAR" />
      <result column="parent_id" property="parentId" jdbcType="BIGINT" />
      <result column="region_type" property="regionType" jdbcType="NVARCHAR" />
      <result column="sort" property="sort" jdbcType="INTEGER" />
      <result column="status" property="status" jdbcType="INTEGER" />
      <result column="is_leaf" property="isLeaf" jdbcType="BIT" />
      <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
      <result column="creator" property="creator" jdbcType="NVARCHAR" />
      <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
      <result column="updator" property="updator" jdbcType="NVARCHAR" />
      <result column="remark" property="remark" jdbcType="NVARCHAR" />
      <result column="channel_weight" property="channelWeight" jdbcType="INTEGER" />

      <result column="province_id" property="provinceId" jdbcType="BIGINT" />
      <result column="city_id" property="cityId" jdbcType="BIGINT" />
      <result column="province_name" property="provinceName" jdbcType="NVARCHAR" />
      <result column="city_name" property="cityName" jdbcType="NVARCHAR" />
    </resultMap>
  <select id="selectDistCityProvinceByParterId" parameterType="long" resultMap="RegionVoMap">
      SELECT t3.region_name AS province_name, province.* FROM (
          SELECT t2.region_code AS city_id,t2.region_name AS city_name ,t2.parent_id AS province_id,dist.*  FROM (
              SELECT  t1.*, regionpartner.channel_weight  FROM wx_t_region t1
                  LEFT JOIN wx_t_region_partner  regionpartner ON regionpartner.region_id = t1.id WHERE regionpartner.partner_id = #{partnerId, jdbcType=BIGINT}
              ) dist LEFT JOIN wx_t_region t2 ON t2.region_code = dist.parent_id
          ) province LEFT JOIN wx_t_region t3 ON t3.region_code = province.province_id

  </select>
  <select id="selectByRegionCodenew" resultMap="BaseResultMap" parameterType="java.lang.String" >
      select
      <include refid="Base_Column_List" />
      from wx_t_region
      where region_code = #{regionCode}
  </select>
  <select id="selectByRegionCodenew2" resultMap="BaseResultMap" parameterType="java.lang.String" >
      SELECT
      t1.region_code city_code,t1.region_name city_name,
      t2.region_code,t2.region_name,
      t3.region_code province_code,t3.region_name province_name
      FROM wx_t_region t1 LEFT JOIN wx_t_region t2
      ON t1.parent_id = t2.id
      LEFT JOIN wx_t_region t3
      ON t2.parent_id= t3.id
      WHERE t1.region_code = #{regionCode}
  </select>
  <select id="selectDistsDetailByDistIds" resultMap="RegionVoMap" parameterType="java.lang.String" >
      SELECT dist.*,
          city.region_code as city_id,
          city.region_name as city_name,
          province.region_code as province_id,
          province.region_name as province_name FROM wx_t_region dist
          LEFT JOIN wx_t_region city ON dist.parent_id = city.id
          LEFT JOIN wx_t_region province ON city.parent_id = province.id
          WHERE dist.id in
          <foreach collection="distIdList" item="listItem" open="(" close=")" separator="," >
            #{listItem}
          </foreach>
          order by dist.region_code
  </select>
  <select id="getProvCityDistByRegionCode" resultMap="BaseResultMap" parameterType="long" >
      select
      <include refid="Base_Column_List" />
      from wx_t_region region,(
          SELECT dist.region_code as distcode , city.region_code  AS citycode ,province.region_code AS provincecode FROM wx_t_region dist
          LEFT JOIN wx_t_region city ON dist.parent_id = city.id
          LEFT JOIN wx_t_region province ON city.parent_id = province.id
          WHERE dist.region_code = #{regionCode})
      codes WHERE region.region_code = codes.distcode OR region.region_code = codes.citycode OR region.region_code = codes.provincecode

  </select>

  <select id="getRegionIdsByRegionName" resultType="java.lang.Long">
     SELECT DISTINCT id FROM wx_t_region WHERE region_name  LIKE ''+#{regionName}+'%'
  </select>

  <select id="getRegionIdsByParentRegionId" resultType="java.lang.Long">
      SELECT  DISTINCT a.region_name  FROM  wx_t_region a,f_getRegionIds(${regionId}) b
      <if test="partnerId!=null">
      LEFT JOIN wx_t_region_partner t_region_partner
      ON t_region_partner.region_id = b.id
      </if>
      where a.id = b.id and a.status!=0
      <if test="partnerId!=null">
       AND  t_region_partner.partner_id=#{partnerId}
      </if>
      <if test="existsWorkshop">
      and exists (select 1 from wx_t_work_shop w where w.region_id=a.id)
      </if>
      order by a.id
  </select>

  <select id="getAllAreas" resultType="string">
      SELECT  DISTINCT r.remark  FROM  wx_t_region r
      where r.region_type='P'
  </select>

  <!-- 根据区Id查询省市区名字 -->
  <select id="getRegionNameByRegionId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
      SELECT
      t1.region_code city_code,t1.region_name city_name,
      t2.region_code,t2.region_name,
      t3.region_code province_code,t3.region_name province_name
      FROM wx_t_region t1 LEFT JOIN wx_t_region t2
      ON t1.parent_id = t2.id
      LEFT JOIN wx_t_region t3
      ON t2.parent_id= t3.id
      WHERE t1.id = #{regionId,jdbcType=BIGINT}
  </select>

  <select id="getRegionIdByCityName" resultType="long" parameterType="string">
      SELECT top 1 region_code FROM wx_t_region where region_type = 'C' and region_name = #{cityName} ORDER BY parent_id DESC
  </select>

  <select id="getRegionByDistributorId" resultType="map">
      select p.partner_id,r1.region_name as dist,r2.region_name as city,r3.region_name as province
      from wx_t_partner_o2o_enterprise p
      join wx_t_region_partner rp on p.partner_id = rp.partner_id
      join wx_t_region r1 on r1.id=rp.region_id
      join wx_t_region r2 on r2.id=r1.parent_id
      join wx_t_region r3 on r3.id=r2.parent_id
      where p.distributor_id = #{distributorId}
  </select>

  <select id="listRegionByIds" resultType="com.chevron.pms.model.TertiaryRegionVo">
    select
      t1.id regionId, t2.id cityId, t3.id provinceId,
      t1.region_name regionName, t2.region_name cityName, t3.region_name provinceName
    from wx_t_region t1
    left join wx_t_region t2 ON t1.parent_id = t2.id
    left join wx_t_region t3 ON t2.parent_id = t3.id
    where t1.id in (
      <foreach collection="ids" item="id" separator=",">
        #{id, jdbcType=BIGINT}
      </foreach>
    )
  </select>
</mapper>