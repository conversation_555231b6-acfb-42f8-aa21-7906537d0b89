select ws.workflow_code, ws.step_code, ws.step_name, el.source_type, isnull(eg.group_name, u.ch_name) from wx_t_workflow_exe_list el
left join wx_t_workflow_step ws on el.step_id=ws.step_id
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP'
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER'
--where ws.workflow_code='CDM_APPLY' --and eg.group_name='申请FLSR'
order by workflow_code