package com.chevron.b2b.model;

/**
 * 计划事项实例-wx_t_schedule_item_instance
 * <AUTHOR>
 * @version 1.0 2018-12-18 15:32
 */
public class ScheduleItemInstance {

	/**  ID  **/
    private Long id;

	/**  计划事项ID  **/
    private Long scheduleItemId;

	/**  计划实例ID  **/
    private Long scheduleInstanceId;

	/**  输入值  **/
    private String inputValue;
    
	/**  标题  **/
    private String caption;

	/**  输入控件配置  **/
    private String inputCtrlConfig;

	/**  是否必填(1-必填，0-非必填)  **/
    private Integer requiredFlag;

	/**  控件类型  **/
    private String ctrlType;
    
    private Long inputCtrlId;
    
    private Integer ctrlHeight;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getScheduleItemId() {
        return scheduleItemId;
    }

    public void setScheduleItemId(Long scheduleItemId) {
        this.scheduleItemId = scheduleItemId;
    }

    public Long getScheduleInstanceId() {
        return scheduleInstanceId;
    }

    public void setScheduleInstanceId(Long scheduleInstanceId) {
        this.scheduleInstanceId = scheduleInstanceId;
    }

    public String getInputValue() {
        return inputValue;
    }

    public void setInputValue(String inputValue) {
        this.inputValue = inputValue;
    }

	public String getCaption() {
		return caption;
	}

	public void setCaption(String caption) {
		this.caption = caption;
	}

	public String getInputCtrlConfig() {
		return inputCtrlConfig;
	}

	public void setInputCtrlConfig(String inputCtrlConfig) {
		this.inputCtrlConfig = inputCtrlConfig;
	}

	public Integer getRequiredFlag() {
		return requiredFlag;
	}

	public void setRequiredFlag(Integer requiredFlag) {
		this.requiredFlag = requiredFlag;
	}

	public String getCtrlType() {
		return ctrlType;
	}

	public void setCtrlType(String ctrlType) {
		this.ctrlType = ctrlType;
	}

	public Long getInputCtrlId() {
		return inputCtrlId;
	}

	public void setInputCtrlId(Long inputCtrlId) {
		this.inputCtrlId = inputCtrlId;
	}

	public Integer getCtrlHeight() {
		return ctrlHeight;
	}

	public void setCtrlHeight(Integer ctrlHeight) {
		this.ctrlHeight = ctrlHeight;
	}
}
