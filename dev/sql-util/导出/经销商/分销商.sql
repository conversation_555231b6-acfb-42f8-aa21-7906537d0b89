select o1.organization_name "经销商", o.organization_name "分销商",case when o.status=1 then 'Active' else 'Inactive' end "状态",
u.ch_name "创建人", co.organization_name "创建机构", o.create_time "创建时间"
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
left join wx_t_organization o1 on o1.id=pe.ext_property1
left join wx_t_user u on u.user_id=o.creator
left join wx_t_organization co on co.id=u.org_id
where o.type=3 --and o.status=1 
and o1.id!=9
order by o1.id