package com.sys.dealer.service.impl;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.dealer.business.DealerBusinessCustomBizService;
import com.sys.dealer.dao.DealerBusinessCustomMapper;
import com.sys.dealer.model.DealerBusinessCustom;
import com.sys.dealer.model.DealerBusinessCustomParams;
import com.sys.dealer.service.DealerBusinessCustomService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 经销商业务定制操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2018-03-07 15:14
 */
@Service
public class DealerBusinessCustomServiceImpl implements DealerBusinessCustomService {
	@Autowired
	private DealerBusinessCustomBizService dealerBusinessCustomBizService;
	
	@Autowired
	private DealerBusinessCustomMapper dealerBusinessCustomMapper;
	
	private final static Logger log = Logger.getLogger(DealerBusinessCustomServiceImpl.class);

	@Override
	public Map<String, Object> save(DealerBusinessCustom record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				dealerBusinessCustomBizService.insert(record);
			}else{
				dealerBusinessCustomBizService.update(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> delete(List<Long> pkValues) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			dealerBusinessCustomBizService.delete(pkValues);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getDealerBusinessByPartnerId(Long partnerId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			Map<String,Object> requestMap = new HashMap<String,Object>();
			requestMap.put("dealerId", partnerId);
			requestMap.put("enableFlag", 1);
			requestMap.put("businessFunCode", "PROMOTION_POINT");
			List<DealerBusinessCustom> dataList=dealerBusinessCustomMapper.queryByParams(requestMap);
			resultMap.put(Constants.RESULT_DATA_CODE, dataList);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		}catch(Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
}
