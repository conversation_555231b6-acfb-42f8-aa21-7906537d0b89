select  o.organization_name "经销商名称", u.ch_name "用户姓名",u.login_name "登录名",(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "经销商分配城市",
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_user_charge_region rp 
join wx_t_region r1 on r1.id=rp.charge_region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.user_id=u.user_id ) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "用户分配城市",
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.city_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name city_name, r1.region_name from wx_t_user_charge_region rp 
join wx_t_region r1 on r1.id=rp.charge_region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.user_id=u.user_id ) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "用户分配区县" from wx_t_user u 
	left join wx_t_organization o on o.id=u.org_id
	where exists (select 1 from wx_t_user_charge_region ur where ur.user_id=u.user_id )
	and o.id!=9 and o.status=1 and u.status=1
	order by o.id