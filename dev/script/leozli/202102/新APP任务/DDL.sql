ALTER TABLE [wx_task_mb] add brand                      int                                  null
ALTER TABLE [wx_task_sub] add brand                      int                                  null
ALTER TABLE [wx_task_sub] add submit_time                      datetime                                  null

/*==============================================================*/
/* Module: V2_DSR_KPI                                           */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_v2_dsr_kpi_target                                */
/*==============================================================*/
create table wx_t_v2_dsr_kpi_target (
	id                               bigint                            not null identity,
	effective_year                   int                                        not null,
	region                           nvarchar(64)                               not null,
	partner_id                       bigint                                     not null,
	dsr_id                           bigint                                     not null,
	kpi_code                         nvarchar(64)                               not null,
	effective_month                  int                                        not null,
	target_value                     decimal(20,6)                                  null,
	ext_property1                    nvarchar(64)                                   null,
	ext_property2                    nvarchar(64)                                   null,
	ext_property3                    nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_v2_dsr_kpi_target ADD CONSTRAINT uk_v2_dsr_kpi_target UNIQUE (effective_year,region,partner_id,dsr_id,kpi_code,effective_month);
