package com.chevron.material.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;

import com.chevron.inventory2021.business.Inventory2021BizService;
import com.chevron.inventory2021.model.Inventory2021;
import com.chevron.inventory2021.model.InventoryLog2021;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.material.business.ApplicationBizService;
import com.chevron.material.dao.CouponOptionDetailMapper;
import com.chevron.material.dao.WXTMaterialAddressConfigVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationDetailVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationLogisticsVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationlStatusHistoryVoMapper;
import com.chevron.material.dao.WXTMaterialInventoryLogVoMapper;
import com.chevron.material.dao.WXTMaterialSkuVoMapper;
import com.chevron.material.dao.WXTMaterialVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseOutDetailVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseOutVoMapper;
import com.chevron.material.dto.SmartCommOrder;
import com.chevron.material.dto.SmartCommOrderLine;
import com.chevron.material.model.AddressConfigDTO;
import com.chevron.material.model.ApplicationHead;
import com.chevron.material.model.ApplicationLine;
import com.chevron.material.model.ApplicationParams;
import com.chevron.material.model.ApplicationState;
import com.chevron.material.model.B2BAbnormalDetection;
import com.chevron.material.model.CouponApplyParams;
import com.chevron.material.model.CouponOptionDetail;
import com.chevron.material.model.MaterialSource;
import com.chevron.material.model.PromotionGiftApplyInfo;
import com.chevron.material.model.WXTMaterialAddressConfigVo;
import com.chevron.material.model.WXTMaterialAddressConfigVoExample;
import com.chevron.material.model.WXTMaterialApplicationDetailVo;
import com.chevron.material.model.WXTMaterialApplicationLogisticsVo;
import com.chevron.material.model.WXTMaterialApplicationLogisticsVoExample;
import com.chevron.material.model.WXTMaterialApplicationVo;
import com.chevron.material.model.WXTMaterialApplicationVoExample;
import com.chevron.material.model.WXTMaterialApplicationVoExample.Criteria;
import com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVo;
import com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVoExample;
import com.chevron.material.model.WXTMaterialSkuVo;
import com.chevron.material.model.WXTMaterialSkuVoExample;
import com.chevron.material.model.WXTMaterialVo;
import com.chevron.material.model.WXTMaterialVoExample;
import com.chevron.material.model.WXTMaterialWarehouseOutDetailVo;
import com.chevron.material.model.WXTMaterialWarehouseOutVo;
import com.chevron.material.model.WXTMaterialWarehouseOutVoExample;
import com.chevron.material.model.virtual.CardInfo;
import com.chevron.material.model.virtual.SubmitParam;
import com.chevron.material.model.virtual.VirtualGiftType;
import com.chevron.material.service.ApplicationService;
import com.chevron.material.service.SmartCommService;
import com.chevron.material.util.ApplicationUtil;
import com.chevron.material.util.DealerPermissUtil;
import com.chevron.material2021.business.PointMaterialBizService;
import com.chevron.material2021.model.PointMaterial;
import com.chevron.material2021.model.PointMaterialExample;
import com.chevron.pms.dao.PartnerO2OEnterpriseMapper;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.PartnerO2OEnterprise;
import com.chevron.pms.model.PartnerO2OEnterpriseExample;
import com.chevron.pms.model.PartnerResponsibleVo;
import com.chevron.pms.model.RegionVo;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.service.RegionService;
import com.chevron.point.business.PointAccountBizService;
import com.chevron.point.dao.WXTPointAccountVoMapper;
import com.chevron.point.dic.BusinessType;
import com.chevron.point.dic.PointAccountType;
import com.chevron.point.dto.PointType;
import com.chevron.point.model.B2BPointSummary;
import com.chevron.point.model.WXTPointAccountVo;
import com.chevron.point.model.WXTPointAccountVoExample;
import com.chevron.point.service.B2BPointService;
import com.chevron.point.service.PointAccountService;
import com.chevron.point.service.PointService;
import com.chevron.sellin.dao.PromotionGiftModifyLogMapper;
import com.chevron.sellin.dao.PromotionGiftPoolMapper;
import com.chevron.sellin.model.PromotionGiftModifyLog;
import com.chevron.sellin.model.PromotionGiftPool;
import com.chevron.sellin.model.enums.ModifyType;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.token.util.TokenUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.HttpSessionGets;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.Kuaidi100Util;
import com.common.util.MD5;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.SendMessageUtil;
import com.common.util.SpringUtils;
import com.common.util.WeixinMessageUtil;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.auth.model.WxTPropertiesExample;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserExample;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.email.service.EmailSenderService;
import com.sys.organization.model.OrganizationVo;
import com.sys.organization.service.OrganizationService;
import com.sys.properties.service.SequenceTypes;
import com.sys.properties.service.WxTPropertiesService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Service
public class ApplicationServiceImpl implements ApplicationService {

    public static final Logger log = LoggerFactory.getLogger(ApplicationServiceImpl.class);

    private static final String ALREADY_MAX_TIMES = "注意: 您本月提交的订单数量达到上限, 不能再次申请, 或者联系您的雪佛龙销售负责人。";

    private static final String POINT_IS_FROZEN = "注意: 您的%s正处于冻结状态, 或者联系您的雪佛龙销售负责人。";

    @Autowired
    private WXTMaterialApplicationVoMapper wxtMaterialApplicationVoMapper;
    @Autowired
    private WXTMaterialApplicationDetailVoMapper wxtMaterialApplicationDetailVoMapper;
    @Autowired
    private RegionService regionService;
    @Autowired
    private WorkshopMasterBizService workshopMasterBizService;
    @Resource
    private WxTPropertiesService wxTPropertiesServiceImpl;
    @Autowired
    private EmailSenderService emailSenderService;
    @Autowired
    private PartnerResponsibleVoMapper partnerResponsibleVoMapper;
    @Resource
    WxTUserMapper wxTUserMapper;
    /**
     * 增加数据字典的获取功能
     */
    @Resource
    private DicService dicServiceImpl;
    @Autowired
    SmartCommService smartCommService;
//    @Autowired
//    private WXTMaterialInventoryVoMapper inventoryVoMapper;
    @Autowired
    WXTMaterialInventoryLogVoMapper inventoryLogVoMapper;
    @Autowired
    WXTMaterialApplicationlStatusHistoryVoMapper applicationStatusHistoryVoMapper;
    @Autowired
    WXTMaterialApplicationLogisticsVoMapper wxtMaterialApplicationLogisticsVoMapper;
    @Autowired
    WXTMaterialVoMapper materialVoMapper;
    @Autowired
    WXTMaterialSkuVoMapper materialSkuVoMapper;
    @Autowired
    WXTMaterialWarehouseOutVoMapper materialWarehouseOutVoMapper;
    @Autowired
    WXTMaterialWarehouseOutDetailVoMapper materialWarehouseOutDetailVoMapper;
    @Autowired
    WXTMaterialAddressConfigVoMapper wxtMaterialAddressConfigVoMapper;
    @Autowired
    PointService pointService;
    @Autowired
    PointAccountService pointAccountService;
    @Autowired
    private OrganizationService organizationService;
    /**
     * 增加解冻、冻结功能
     */
    @Autowired
    private WXTPointAccountVoMapper wXTPointAccountVoMapper;

    @Autowired
    private PointAccountBizService pointAccountBizService;

    @Autowired
    private PartnerO2OEnterpriseMapper partnerO2OEnterpriseMapper;

    @Autowired
    private WorkshopEmployeeMapper workshopEmployeeMapper;

    @Autowired
    private WxTPropertiesMapper wxTPropertiesMapper;
    
    @Autowired
	private DicService dicService;

    @Autowired
    private DicItemVoMapper dicItemVoMapper;

    @Autowired
    private WXTMaterialVoMapper wxtMaterialVoMapper;

    @Autowired
    private PromotionGiftPoolMapper promotionGiftPoolMapper;

    @Autowired
    private PromotionGiftModifyLogMapper promotionGiftModifyLogMapper;

    @Autowired
    private B2BPointService b2BPointService;

    @Autowired
    private CouponOptionDetailMapper couponOptionDetailMapper;
    
    @Autowired
    private ApplicationBizService applicationBizService;
    
    @Autowired
    private Inventory2021BizService inventory2021BizService;
    
    @Autowired
    private PointMaterialBizService pointMaterialBizService;
    
    private String getSunrainRecipients() {
        return (String) Constants.getSystemPropertyByCodeType("sunrain.recipients");
    }

    /**
     * 这个函数是material/jsp/approvalDetail.jsp页面在pointMode != true的时候，
     * 更新这个application的application详细表——wx_t_material_application_detail的内容
     * @param applicationId
     * @param applicationCode
     * @param applicationLines
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> updateApplicationLines(Long applicationId, String applicationCode, ApplicationLine[] applicationLines) throws Exception {
    	throw new RuntimeException("暂不支持");
//        log.debug("Start ApplicationServiceImpl.updateApplicationLines()");
//        Map<String, Object> resultMap = new HashMap<String, Object>();
//        // 检查库存是否足够
//        String stockMsg = checkVirtualStock(applicationLines, true);
//
//        boolean isSufficient = StringUtils.isEmpty(stockMsg);
//        if (!isSufficient) {
//            log.warn("库存校验失败, 无法提交申请: {}", stockMsg);
//            resultMap.put(Constants.RESULT_SUCCESS, false);
//            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, stockMsg);
//            return resultMap;
//        }
//
//        // 回滚虚拟库存
//        applicationBizService.rollbackVirtualStock(applicationId);
//
//        // 删除旧的申请单行
//        WXTMaterialApplicationDetailVoExample example = new WXTMaterialApplicationDetailVoExample();
//        example.createCriteria().andApplicationIdEqualTo(applicationId);
//        wxtMaterialApplicationDetailVoMapper.deleteByExample(example);
//
//
//        // 插入新的的申请单行
//        List<WXTMaterialApplicationDetailVo> lines = prepareLineVo(applicationLines, applicationId);
//        wxtMaterialApplicationDetailVoMapper.insertBatch(lines);
//
//        // 扣除虚拟库存
//        updateVirtualStock(applicationCode, lines);
//        resultMap.put(Constants.RESULT_SUCCESS, true);
//        resultMap.put("msg", "物料申请详情更新成功");
//
//        return resultMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> isFirstApplicationMonthly(String type) throws Exception {
        log.debug("Start ApplicationServiceImpl.isFirstApplicationMonthly()");
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(Constants.RESULT_SUCCESS, true);
        
        PointType pointType = PointType.getPointTypeByApplicationType(type);

        WxTUser user = ContextUtil.getCurUser();
        boolean isRegionDevided = false;
        Long regionNumber = 0L;
        Long partnerId = user.getOrgId();
        regionNumber = wxtMaterialApplicationVoMapper.countSPRegionNumber(partnerId);
        if (null != regionNumber && regionNumber.intValue() > 0) {
            isRegionDevided = true;
        }

        WXTPointAccountVo pointAccountVo = null;
        if(pointType != null) {
        	pointAccountVo = pointType.getPointAccount(user);
        }
        if(pointAccountVo == null) {
            List<WXTPointAccountVo> pointAccountVoList = getWxtPointAccountVos(partnerId,null);
            if (pointAccountVoList != null && !pointAccountVoList.isEmpty()) {
                pointAccountVo = pointAccountVoList.get(0);
            }
        }
        resultMap.put("pointAccountInfo",pointAccountVo);
        resultMap.put("isFirstTime", checkSPApplicationHistory(type));
        resultMap.put("isRegionDevided", isRegionDevided);
        resultMap.put("regionNumber", regionNumber);
        resultMap.put("notAllowOrder", checkNotAllowOrderByAppType(type));
        return resultMap;
    }

    /**
     * 根据数据字典里面的point.notAllowOrder判断是否这类的appType是否不允许下单的状态
     * @param appType
     * @return
     */
    private boolean checkNotAllowOrderByAppType(String appType) {
        boolean isNotAllowed = false;
        Map<String, Object> dicMap = dicServiceImpl.getDicItemByDicTypeCode("point.notAllowOrder");
        List<DicItemVo> disallowAppTypeList = (ArrayList) dicMap.get("data");
        if(disallowAppTypeList!=null && disallowAppTypeList.size() >0){
            for (DicItemVo dicItemVo:disallowAppTypeList){
                 if(dicItemVo.getDicItemCode().equals(appType)){
                    isNotAllowed = true;
                    break;
                }
            }
        }
        return isNotAllowed;
    }

    /**
     * 检查该合伙人在该月是否重复申请,和对应的数据字典里面的MAX_APP_PER_MONTH比较
     *
     * @return
     */
    private boolean checkSPApplicationHistory(String type) {
        WxTUser user = ContextUtil.getCurUser();
        // getmUserTypes为1的在操作订单的时候不需要查询是否达到最大的订单数了
        Long getmUserTypes = user.getmUserTypes();
        if (1L == getmUserTypes) {
            return true;
        }

        // 如果这个user是partner或者是b2b的订单
        if (isPartnerUser(user) || ApplicationUtil.B2B_APPLICATION_TYPE.equals(type)) {
            boolean isLegal = true;
//			boolean isRegionDevided = false;
//			Long regionNumber = 0L;
            Long partnerId = user.getOrgId();

//			regionNumber = wxtMaterialApplicationVoMapper.countSPRegionNumber(partnerId);
//			if (null != regionNumber && regionNumber.intValue() > 0) {
//				isRegionDevided = true;
//			}

            Date startTimeOfMonth = DateUtil.getStartTimeOfMonth(null, null);
            WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
            Criteria createCriteria = example.createCriteria();
            // DRAFT/REJECTED/RETURNED都不算在这个月下的订单
            createCriteria.andApplicationOrgIdEqualTo(partnerId).
                    andApplicationTimeGreaterThanOrEqualTo(startTimeOfMonth).
                    andApplicationStatusNotIn(Arrays.asList(ApplicationState.DRAFT.getValue(), ApplicationState.REJECTED.getValue(), ApplicationState.RETURNED.getValue()));
            if (!StringUtils.isEmpty(type)) {
                createCriteria.andApplicationTypeEqualTo(type);
            } else {
                createCriteria.andApplicationTypeIsNull();
            }
            List<WXTMaterialApplicationVo> list = wxtMaterialApplicationVoMapper.selectByExample(example);

            // list.size()就是目前的下单的次数
            if (null != list && !list.isEmpty()) {
                Map<String, Object> dicMap = dicServiceImpl.getDicItemByDicTypeCode("MAX_APP_PER_MONTH");
                Map<String, Integer> maxAppCountMap = getMapAppCount(dicMap);
//				if("caltex".equals(type) || "promotion".equals(type)) {
                // 加德士积分和促销积分兑换的场合，每个经销商每个月只能申请两次(现在改成由数据字典配置了)
                Integer maxAppPerMonth = maxAppCountMap.get(type);
                if (maxAppPerMonth!=null && list.size() >= maxAppPerMonth) {
                    isLegal = false;
                }
//				} else {
//					// 雪佛龙合伙人需要分区域下单的场合， 每个区域每月只能下单一次
//					if (isRegionDevided && regionNumber.intValue() <= list.size()) {
//						isLegal = false;
//					}
//					// 雪佛龙合伙人不分区域下单的场合， 只能下单一次
//					if (!isRegionDevided) {
//						isLegal = false;
//					}
//				}
            }

            return isLegal;
        }else {
        	PointType pointType = PointType.getPointTypeByApplicationType(type);
        	if(pointType != null) {
        		return pointType.checkSPApplicationHistory(user);
        	}
        }

        return false;
    }

    /**
     * 从数据字典获取每月的最大订单数
     *
     * @param dicMap
     * @return
     */
    private Map<String, Integer> getMapAppCount(Map<String, Object> dicMap) {
        Map<String, Integer> maxAppCountMap = new HashMap<String, Integer>();
        //
        maxAppCountMap.put("caltex", 2);
        maxAppCountMap.put("promotion", 2);
        maxAppCountMap.put("cdm_stock", 1);
        maxAppCountMap.put("cdm_material", 1);
        if ("success".equals(dicMap.get("result"))) {
            List<DicItemVo> itemVoListist = (ArrayList) dicMap.get("data");
            for (DicItemVo dicItemVo : itemVoListist) {
                try {
                    maxAppCountMap.put(dicItemVo.getDicItemCode(), Integer.parseInt(dicItemVo.getDicItemDesc()));
                } catch (Exception e) {
                    log.error("ApplicationServiceImpl.getMapAppCount exception", e);
                }
            }
        }
        return maxAppCountMap;
    }

    private boolean isPointOrder(String applicationType) {
        return ApplicationUtil.isPointOrder(applicationType);
    }

    /**
     * 检查积分是否满足这个订单需要的积分
     * @param applicationHead
     * @param applicationLines
     * @return
     */
    private boolean checkPointsMeetTotalPrice(ApplicationHead applicationHead, ApplicationLine[] applicationLines){
        boolean result = false;
        String applicationType = applicationHead.getApplicationType();
        Long partnerId = applicationHead.getApplicationOrgId();
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationType);
        Double totalPoint = null;//getPointsByAppTypeBizType(partnerId, applicationHead.getUserId(), applicationType, applicationHead.getBizType()).doubleValue();
        WXTPointAccountVo accountVo = pointType == null ? null : pointType.getPointAccount(ContextUtil.getCurUser());
    	if(accountVo != null) {
    		if(accountVo.getId() != null) {
    			try {
					BigDecimal tp = pointAccountBizService.queryPointTotalByType(accountVo.getId(), new String[] {pointType.getValue()}, applicationHead.getBizType());
					totalPoint = tp == null ? 0.0 : tp.doubleValue();
				} catch (WxPltException e) {
					throw new RuntimeException(e);
				}
    		}
    	}else {
        	totalPoint = getPointsByAppTypeBizType(partnerId, applicationHead.getUserId(), applicationType, applicationHead.getBizType()).doubleValue();
        }

        // 根据ApplicationLine获取totalPrice
        Double totalPrice = 0.0D;
        for (ApplicationLine line : applicationLines) {
            if (line.getApplicationQty() > 0 && line.getMaterialPrice() > 0) {
                totalPrice += line.getApplicationQty() * line.getMaterialPrice();
            }
        }

        // 获取当前那些正在被审批没有实际扣除的订单的pendingPoint
        // b2b的积分(b2b这边对应salesChannel)，promotion的积分 也是需要传bizType
        BigDecimal pendingPointBigDecimal = wxtMaterialApplicationDetailVoMapper.queryPendingOrderPointTotal(partnerId, applicationType, applicationHead.getBizType());
        Double pendingPoint = 0D;
        if (pendingPointBigDecimal != null) {
            pendingPoint = pendingPointBigDecimal.doubleValue();
        }

        if (totalPoint - pendingPoint >= totalPrice) {
            result = true;
        } else {
            log.error("积分数不足, 无法提交申请:{} {} totalPoint={} pendingPoint={} totalPrice={} bizType={} salesChannel={}",
                    partnerId,applicationType,totalPoint,pendingPoint,totalPrice,applicationHead.getBizType(),applicationHead.getSalesChannel());
            result = false;
        }
        return result;
    }

    @Override
    public JsonResponse dsrCreate(ApplicationHead applicationHead, ApplicationLine[] applicationLines) {
        JsonResponse jsonResponse = new JsonResponse();
         
        // 1、先判断applicationId是否为空
        // 为空的话就是需要新建
        // 不为空的话那就是submit
        applicationHead.setSalesChannel("CDM");
        if(applicationHead == null || applicationLines == null || com.common.util.StringUtils.isEmpty(applicationHead.getSalesChannel())) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            return jsonResponse;
        }

//        // 其实这个biz在b2b的订单中就是salesChannel,这里适配一下
//        applicationHead.setBizType(applicationHead.getSalesChannel());
//        // b2b的订单是不可能选择Ship2Workshop
//        applicationHead.setShip2Workshop(false);
        //判断订单是否虚拟商品
        Map<String,Object> createResult = new HashMap<String, Object>();
        //如果是B2B的订单，判断是否是虚拟商品
        boolean virtualGift;
		try {
			virtualGift = applicationBizService.isVirtualGift(applicationLines);
		} catch (WxPltException e) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            jsonResponse.setErrorMsg(e.getMessage());
            return jsonResponse;
		}
        applicationHead.setUserId(ContextUtil.getCurUserId().toString());
        if(virtualGift){
            applicationHead.setPush2Approval(false);
            applicationHead.setVirtualGiftFlag(true);
            jsonResponse =  this.createVirtualGiftOrder(applicationHead, applicationLines);
            return jsonResponse;
        }else{
            applicationHead.setPush2Approval(true);
            createResult = this.create(applicationHead, applicationLines);
        }
        Boolean result = (Boolean)createResult.get(Constants.RESULT_SUCCESS);
        if(!result){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg((String) createResult.get(Constants.RESULT_ERROR_MSG_KEY));
        } else {
            Map<String,Object> applicationInfoMap = new HashMap<String, Object>();
            applicationInfoMap.put("applicationId",createResult.get("applicationId"));
            jsonResponse.setDataResult(applicationInfoMap);
        }
        return jsonResponse;
    }

    /**
     * 从数据库获取物料数据
     */
    private Boolean isVirtualGift(ApplicationLine[] applicationLines) throws WxPltException {
    	if(applicationLines.length != 1) {
    		//虚拟礼品只有一个礼品下单
    		return false;
    	}
    	ApplicationLine applicationLine = applicationLines[0];
    	Map<String, Object> params = new HashMap<String, Object>(1);
    	params.put("id", applicationLine.getMaterialId());
    	List<PointMaterial> list = pointMaterialBizService.queryByParams(params);
        if(list.isEmpty()) {
        	throw new WxPltException("物料" + applicationLine.getMaterialSkuCode() + "未上架");
        }
        PointMaterial material = list.get(0);
        if(!Constants.VIRTUAL_MATERIAL_TYPE.equals(material.getMaterialDictionary().getMaterialTag())){
            return false;
        }
        applicationLine.setMaterialPrice(material.getPointValue());
        applicationLine.setVirtualGiftCode(material.getMaterialDictionary().getAttribute2());
        applicationLine.setVirtualGiftBusiness(material.getMaterialDictionary().getAttribute1());
        applicationLine.setActualAmount(StringUtils.isBlank(material.getMaterialDictionary().getAttribute4()) ? null : Double.parseDouble(material.getMaterialDictionary().getAttribute4()));
        applicationLine.setMaterialName(material.getMaterialDictionary().getMaterialName());
        return true;
    }
    
    private Map<String, Inventory2021> getInventorySkuMap(ApplicationLine[] applicationLines) throws WxPltException {
    	List<String> materialCodes = new ArrayList<String>(applicationLines.length);
    	for(ApplicationLine line : applicationLines) {
    		materialCodes.add(line.getMaterialSkuCode());
    	}
    	Map<String, Object> params = new HashMap<String, Object>(3);
    	params.put("productType", Inventory2021.PRODUCT_TYPE_MATERIAL);
    	params.put("inventoryType", Inventory2021.INVENTORY_TYPE_MATERIAL);
    	//productKey包含供应商信息
    	params.put("productKeys", materialCodes);
    	List<Inventory2021> list = inventory2021BizService.queryByParams(params);
    	Map<String, Inventory2021> map = new HashMap<String, Inventory2021>(list.size());
    	for(Inventory2021 inventory : list) {
    		map.put(inventory.getProductKey(), inventory);
    	}
    	return map;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> create(ApplicationHead applicationHead, ApplicationLine[] applicationLines) {
        String applicationType = applicationHead.getApplicationType();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationType);
        if(pointType==null && StringUtils.isBlank(applicationHead.getFromSource())){
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY,"错误参数");
            return resultMap;
        }
        log.debug("Start ApplicationServiceImpl.create() applicationHead=" + applicationHead);
        // 如果applicationId已经存在id了的话，说明是draft状态或者PENDING_4_URGENT的状态，就进入update的按个分支
        if (null != applicationHead && null != applicationHead.getApplicationId()
                && applicationHead.getApplicationId() > 0) {
            // goto update
            return update(applicationHead, applicationLines);
        }
        log.debug("Start ApplicationServiceImpl.create() applicationHead.getApplicationOrgId,salesChannel ",applicationHead.getApplicationOrgId(),applicationHead.getSalesChannel());
        int res4Line = 0;
        int readyLines = 0;
        int res4Head = 0;
        WxTUser user = ContextUtil.getCurUser();
        try {
            // 如果订单的类型是pointMode，增加在进入create的时候就判断这个用户的这个applicationType的积分是否被冻结
            if (isPointOrder(applicationHead.getApplicationType())) {
                String errorStr = checkIsFreezeType(applicationHead.getApplicationOrgId(), applicationHead.getUserId(), applicationHead.getApplicationType(), applicationHead.getSalesChannel());
                if (errorStr != null) {
                    log.warn(errorStr);
                    resultMap.put(Constants.RESULT_SUCCESS, false);
                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, errorStr);
                    return resultMap;
                }

                if(checkNotAllowOrderByAppType(applicationHead.getApplicationType())){
                    log.warn(applicationHead.getApplicationType() + "积分目前不允许下单");
                    resultMap.put(Constants.RESULT_SUCCESS, false);
                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, pointType != null ? pointType.getText() : applicationType + "兑换目前不能下订单,或者联系您的雪佛龙销售负责人");
                    return resultMap;
                }
            }
            Map<String, Inventory2021> inventorySkuMap = getInventorySkuMap(applicationLines);
            // 提交审批的需要检查这个月的订单数量是不是达到最大
            if (applicationHead.getPush2Approval()) {
                // 检查该SP该月是否申请第二次
                boolean islegal = true;
                if (!isPointOrder(applicationHead.getApplicationType())) {
                    islegal = checkSPApplicationHistory(applicationHead.getApplicationType());
                }
                if (!islegal) {
                    log.warn(ALREADY_MAX_TIMES);
                    resultMap.put(Constants.RESULT_SUCCESS, false);
                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, ALREADY_MAX_TIMES);
                    return resultMap;
                }
                // 检查库存是否足够
                String stockMsg = checkInventory(applicationLines, inventorySkuMap);
                boolean isSufficient = StringUtils.isEmpty(stockMsg);
                if (!isSufficient) {
                    log.warn("库存校验失败, 无法提交申请: {}", stockMsg);
                    resultMap.put(Constants.RESULT_SUCCESS, false);
                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, stockMsg);
                    return resultMap;
                }

//                //检测是否超过上限
//                String _stockMsg = checkLimitQty(applicationLines, applicationHead.getApplicationType(), applicationHead.getApplicationOrgId(), applicationHead.getUserId(), false);
//                isSufficient = StringUtils.isEmpty(_stockMsg);
//                if (!isSufficient) {
//                    log.warn("申请上限校验失败, 无法提交申请: {}", _stockMsg);
//                    resultMap.put(Constants.RESULT_SUCCESS, false);
//                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, _stockMsg);
//                    return resultMap;
//                }

                // 检测积分是否足够，使用的也是最新的applicationLines
                boolean isEnoughPoint = checkPointsMeetTotalPrice(applicationHead,applicationLines);
                if (!isEnoughPoint) {
                    resultMap.put(Constants.RESULT_SUCCESS, false);
                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "您目前的积分余额不足，下单失败。");
                    return resultMap;
                }
            }
            // 进入这个分支的说明是新建的application, 新建的是需要refreshAppLinesPrice，刷新一下price
            refreshAppLinesPrice(applicationLines);
            
            WXTMaterialApplicationVo record = prepareHeadVo(applicationHead, false);
            if(pointType != null && pointType.isPersonalAccount()) {
            	record.setApplicationUserId(user.getUserId().toString());
            }
            if(pointType == null){
                record.setAttribute1(Constants.PROMOTION_POINT_EXCHANGE); //如果是促销积分兑换，写入标记
                record.setPromotionType(applicationHead.getFromSource()); //如果是促销积分兑换，写入归属的积分大类
            }
            record.setVersionNo(WXTMaterialApplicationVo.VERSION_NO_202109);
            res4Head = wxtMaterialApplicationVoMapper.insertSelective(record);
            Long applicationId = record.getId();
            List<WXTMaterialApplicationDetailVo> lines = prepareLineVo(applicationLines, applicationId);

            readyLines = lines == null ? 0 : lines.size();
            res4Line = wxtMaterialApplicationDetailVoMapper.insertBatch(lines);

            if (applicationHead.getPush2Approval()) {
                // 扣除库存
            	updateMaterialInventory(record, applicationLines, inventorySkuMap);
//                updateVirtualStock(record.getApplicationCode(), lines);
                // 普通非加急积分订单的场合，不需要审批，直接生成出库单
                if (isPointOrder(record.getApplicationType()) && !record.getIsUrgent()
                        && ApplicationState.PENDING_ON_OUTBOUND.getValue().equals(record.getApplicationStatus())) {
                    // 这里就是直接下单的,依然applicationLines已经插入，就是最新的，扣减积分也是基于这个来，那就是直接使用最新的价格来计算这个订单了
                    Double totalPrice = applicationBizService.calcTotalPriceByAppDetail(record.getId());
                    Map<String, Object> pointServiceRes = pointService.consumePoints(record.getApplicationOrgId(), applicationHead.getUserId(), totalPrice,
                            record.getApplicationCode(), record.getApplicationOrgName(),
                            pointType != null ? pointType.getValue() : applicationType, applicationHead.getBizType(), new HashMap<String, Object>());
                    if (!Constants.SUCCESS_CODE.equals(pointServiceRes.get(Constants.RESULT_CODE_KEY))) {
                        throw new Exception(pointType != null ? pointType.getText() : applicationType + "账户不足");
                    }
                    pushWarehouseOutInfo(applicationId);
                    // 扣除真实库存
//                    updateRealStock(record.getApplicationCode(), lines);
                } else {
                    // 发送邮件给审批者
                    sendReadyEmail(applicationId, record.getLastUpdateTime().getTime());
                }
            }

            // 申请单状态变化历史记录
            String status = "";
            String step = "";
            if (applicationHead.getPush2Approval()) {
                status = ApplicationState.READY.getValue();
                step = "新建并提交审批";
                if(applicationHead.getApplicationType().equals(ApplicationUtil.APPLICATION_TYPE_CDM_STORE_OPEN)) {
                	wxtMaterialAddressConfigVoMapper.insertWorkshopFlag(record.getId(),applicationHead.getWorkshopId(),2);
                }
            } else {
                status = ApplicationState.DRAFT.getValue();
                step = "新建";
                if(applicationHead.getApplicationType().equals(ApplicationUtil.APPLICATION_TYPE_CDM_STORE_OPEN)) {
                	wxtMaterialAddressConfigVoMapper.insertWorkshopFlag(record.getId(),applicationHead.getWorkshopId(),1);
                }
            }
            applicationBizService.pushApplicationHistory(applicationId, step, "", status, "");

            resultMap.put(Constants.RESULT_SUCCESS, true);
            resultMap.put("applicationId", applicationId);
            resultMap.put("applicationCode", record.getApplicationCode());
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("ApplicationServiceImpl.create() exception", e);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单创建失败," + e.getMessage());
            resultMap.put(Constants.RESULT_ERROR, "程序异常，回滚变更:" + e.getMessage());
            return resultMap;
        }

        log.debug("End ApplicationServiceImpl.create()");
        if (res4Head != 1) {
            resultMap.put(Constants.RESULT_SUCCESS, false);
            String error = "插入物料申请表" + res4Head + "条成功";
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单创建失败," + error);
            resultMap.put(Constants.RESULT_ERROR, error);
            return resultMap;
        }

        if (res4Line != readyLines) {
            resultMap.put(Constants.RESULT_SUCCESS, false);
            String error = "插入物料申请详情表" + res4Line + "条成功";
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单创建失败," + error);
            resultMap.put(Constants.RESULT_ERROR, error);
            return resultMap;
        }
        return resultMap;
    }

    @Override
    public JsonResponse createVirtualGiftOrder(ApplicationHead applicationHead, ApplicationLine[] applicationLines) {
        log.debug("Start createVirtualGiftOrder.create() applicationHead=" + applicationHead);
        JsonResponse jsonResponse = new JsonResponse();
        jsonResponse.setReponseStatus(ResponseStatus.WARNING);
        String abnormalDetection = this.abnormalDetection(applicationHead,applicationLines);
        if (abnormalDetection != null) {
            log.warn(abnormalDetection);
            jsonResponse.setErrorMsg(abnormalDetection);
            return jsonResponse;
        }
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationHead.getApplicationType());
        WxTUser user = ContextUtil.getCurUser();
        // 如果订单的类型是pointMode，增加在进入create的时候就判断这个用户的这个applicationType的积分是否被冻结
        if (isPointOrder(applicationHead.getApplicationType())) {
            String errorStr = checkIsFreezeType(applicationHead.getApplicationOrgId(), applicationHead.getUserId(), applicationHead.getApplicationType(), applicationHead.getSalesChannel());
            if (errorStr != null) {
                log.warn(errorStr);
                jsonResponse.setErrorMsg(errorStr);
                return jsonResponse;
            }
            if(checkNotAllowOrderByAppType(applicationHead.getApplicationType())){
                log.warn(applicationHead.getApplicationType() + "积分目前不允许下单");
                jsonResponse.setErrorMsg(applicationHead.getApplicationType() + "积分目前不允许下单");
                return jsonResponse;
            }
        }
        Map<String, Inventory2021> inventorySkuMap;
		try {
			inventorySkuMap = getInventorySkuMap(applicationLines);
		} catch (WxPltException e1) {
            log.error(e1.getMessage(), e1);
            jsonResponse.setErrorMsg(e1.getMessage());
            return jsonResponse;
		}
        // 检查库存是否足够
        String stockMsg = checkInventory(applicationLines, inventorySkuMap);
        boolean isSufficient = StringUtils.isEmpty(stockMsg);
        if (!isSufficient) {
            log.warn("库存校验失败, 无法提交申请: {}", stockMsg);
            jsonResponse.setErrorMsg(stockMsg);
            return jsonResponse;
        }
//        //检测是否超过上限
//        String _stockMsg = checkLimitQty(applicationLines, applicationHead.getApplicationType(), applicationHead.getApplicationOrgId(), applicationHead.getUserId(), false);
//        isSufficient = StringUtils.isEmpty(_stockMsg);
//        if (!isSufficient) {
//            log.warn("申请上限校验失败, 无法提交申请: {}", _stockMsg);
//            jsonResponse.setErrorMsg(_stockMsg);
//            return jsonResponse;
//        }
        // 检测积分是否足够，使用的也是最新的applicationLines
        boolean isEnoughPoint = checkPointsMeetTotalPrice(applicationHead,applicationLines);
        if (!isEnoughPoint) {
            jsonResponse.setErrorMsg("您目前的积分余额不足，下单失败。");
            return jsonResponse;
        }
        WXTMaterialApplicationVo record = prepareHeadVo(applicationHead, false);
        record.setVersionNo(WXTMaterialApplicationVo.VERSION_NO_202109);
        if(pointType.isPersonalAccount()) {
            record.setApplicationUserId(user.getUserId().toString());
        }
        record.setPostCompany(null);
        record.setAttribute1("virtual");
        record.setApplicationStatus(ApplicationState.VIRTUAL_SUBMIT.getValue());
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();//设置属性的默认属性
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);//设置事务的传播行为，此处是设置为开启一个新事物
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);//设置事务的隔离级别，此处是读已经提交

        //手动开启事务
        DataSourceTransactionManager transactionManager = SpringUtils.getBean(DataSourceTransactionManager.class);
        TransactionStatus transactionStatus = transactionManager.getTransaction(definition);
        List<WXTMaterialApplicationDetailVo> lines = new ArrayList<WXTMaterialApplicationDetailVo>();
        try {
            wxtMaterialApplicationVoMapper.insertSelective(record);//订单表
            lines = prepareLineVo(applicationLines, record.getId());//订单明细表
            wxtMaterialApplicationDetailVoMapper.insertBatch(lines);
            transactionManager.commit(transactionStatus);
        }catch (Exception e){
            transactionManager.rollback(transactionStatus);
            jsonResponse.setErrorMsg("兑换失败，请稍后再试。");
            return jsonResponse;
        }
        transactionStatus = transactionManager.getTransaction(definition); //开启新的事务
        try {
            //开始扣除积分
            Double totalPrice = applicationBizService.calcTotalPriceByAppDetail(record.getId());
            Map<String, Object> pointServiceRes = pointService.consumePoints(record.getApplicationOrgId(), applicationHead.getUserId(), totalPrice,
                    record.getApplicationCode(), record.getApplicationOrgName(),
                    pointType.getValue(), applicationHead.getBizType(), new HashMap<String, Object>());
            //扣除真实库存
//            updateRealStock(record.getApplicationCode(), lines);
            updateMaterialInventory(record, applicationLines, inventorySkuMap);
            if (!Constants.SUCCESS_CODE.equals(pointServiceRes.get(Constants.RESULT_CODE_KEY))) {
                jsonResponse.setErrorMsg(pointType.getText() + "账户积分不足");
                return jsonResponse;
            }
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            e.printStackTrace();
            jsonResponse.setErrorMsg("库存锁定失败，请稍后再试。");
            return jsonResponse;
        }
        //开始调用充值接口
        ApplicationLine applicationLine = applicationLines[0];
        WXTMaterialApplicationVo updateApplication = new WXTMaterialApplicationVo();
        updateApplication.setId(Long.parseLong(record.getId().toString()));
        SubmitParam submitParam =  null;
        try {
            submitParam = this.buildSubmitParam(record, applicationLine, applicationHead);
            this.chooseSubmitVirtualService(submitParam);
            updateApplication.setAttribute2(submitParam.getBody());
            updateApplication.setLastUpdateTime(new Date());
            wxtMaterialApplicationVoMapper.updateByPrimaryKeySelective(updateApplication);
            transactionManager.commit(transactionStatus);
        }catch (Exception e){
            transactionManager.rollback(transactionStatus);
            updateApplication.setAttribute2(submitParam == null ? null : submitParam.getBody());
            updateApplication.setApplicationStatus(ApplicationState.VIRTUAL_FAILED.getValue());
            updateApplication.setLastUpdateTime(new Date());
            wxtMaterialApplicationVoMapper.updateByPrimaryKeySelective(updateApplication);
            log.error("请求虚拟商品订单接口出错，错误信息：" + e.getMessage());
            if(e instanceof WxPltException){
                jsonResponse.setErrorMsg(((WxPltException) e).getExpMsg());
            }else{
                jsonResponse.setErrorMsg("兑换失败，请稍后再试。");
            }
            return jsonResponse;
        }
        //有卡密发送卡密，无卡密发送成功通知
        List<CardInfo> cardInfos = null;
        try {
            cardInfos = this.resolveResultData(submitParam.getBody());
            if(CollectionUtil.isNotEmpty(cardInfos)){
                StringBuffer sb = new StringBuffer();
                sb.append("您好，您兑换的商品: ").append(applicationLine.getMaterialName()).append("。卡密信息如下: ");
                for (CardInfo cardInfo : cardInfos) {
                    sb.append(" 卡密账号: ").append(cardInfo.getCardNo()).append(" 卡密密码：").append(cardInfo.getCardPwd());
                    if(StringUtils.isNotBlank(cardInfo.getInvalidTime())){
                        sb.append(" 有效期至：").append(cardInfo.getInvalidTime()).append("。 ");
                    }
                }
                try {
                    SendMessageUtil.sndMessageToPhone(applicationHead.getNumber(), sb.toString());
                } catch (Exception e) {
                    log.error("发送短信给 {} 失败，短信内容 {}", applicationHead.getNumber(), sb.toString());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            jsonResponse.setErrorMsg("卡密解析或发送失败,请联系管理员。");
            e.printStackTrace();
            return jsonResponse;
        }
        jsonResponse.setReponseStatus(ResponseStatus.SUCCESS);
        jsonResponse.put("applicationId", record.getId());
        jsonResponse.put("applicationCode", record.getApplicationCode());
        return jsonResponse;
    }

    private SubmitParam buildSubmitParam(WXTMaterialApplicationVo record,ApplicationLine applicationLine,ApplicationHead applicationHead)  throws Exception {
        if(applicationLine.getVirtualGiftBusiness() == null){
            throw new WxPltException("商品信息有误，请稍后再试");
        }
        SubmitParam submitParam = new SubmitParam();
        submitParam.setBusinessId(applicationLine.getVirtualGiftBusiness());
        submitParam.setProductId(applicationLine.getVirtualGiftCode());
        submitParam.setQuantity(applicationLine.getApplicationQty());
        submitParam.setRechargeNo(applicationHead.getNumber());
        submitParam.setGiftName(applicationLine.getMaterialName());
        if(applicationLine.getActualAmount()!=null){
            submitParam.setAmount(new Double(applicationLine.getActualAmount() * applicationLine.getApplicationQty()).intValue());
        }
        submitParam.setUserOrderNo(applicationBizService.getBcUserOrderNo(record));
        return submitParam;
    }

    private SubmitParam chooseSubmitVirtualService(SubmitParam submitParam) throws Exception {
        if(VirtualGiftType.HF.getType().equals(submitParam.getBusinessId()) || VirtualGiftType.LL.getType().equals(submitParam.getBusinessId())){
            return this.submitVirtualHFANDLLOrder(submitParam);
        }else if(VirtualGiftType.LPK.getType().equals(submitParam.getBusinessId())){
            return this.submitVirtualGiftOrder(submitParam);
        }
        throw new WxPltException("商品信息有误，请稍后再试");
    }

    private SubmitParam submitVirtualGiftOrder(SubmitParam submitParam) throws Exception{
        String url= MyPropertyConfigurer.getVal("virtual.gift.apply.lpk.url");
        String appKey = MyPropertyConfigurer.getVal("virtual.gift.apply.lpk.appKey");
        String uid = MyPropertyConfigurer.getVal("virtual.gift.apply.lpk.uid");
        String sign = MD5.MD5Encode(submitParam.getBusinessId() + submitParam.getProductId() + submitParam.getQuantity() + submitParam.getUserOrderNo() + appKey);
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("re_url", Constants.getSystemPropertyByCodeType("app.host") + "/material/application/bcvipcallback.do");
        params.put("business_id", submitParam.getBusinessId());
        params.put("user_order_no", submitParam.getUserOrderNo());
        params.put("product_id", submitParam.getProductId());
        params.put("quantity", submitParam.getQuantity());
        params.put("uid", uid);
        params.put("sign", sign);
        log.info("bc vip params: " + JsonUtil.writeValue(params));
        String body = HttpUtil.get(url, params);
        submitParam.setBody(body);
        log.info("虚拟商品兑换接口返回的报文： {} ",body);
        if(StringUtils.isBlank(body)){
            throw new WxPltException("充值失败，充值接口返回为空");
        }
        JSONObject jsonObject = new JSONObject(body);
        Integer code = jsonObject.getInt("code");
        String message = jsonObject.getStr("message");
        if(10000!=code){
            log.error("虚拟礼品兑换失败： " + message + "==返回的报文： ", body);
            WxPltException wxPltException = new WxPltException(message);
            wxPltException.setExpMsg(message);
            throw wxPltException;
        }
        return submitParam;
    }

    private SubmitParam submitVirtualHFANDLLOrder(SubmitParam submitParam) throws Exception{
        if(submitParam.getAmount() == null){
            log.error("虚拟礼品{}，没有设置金额",submitParam.getGiftName());
            throw new WxPltException(submitParam.getGiftName() + "没有设置金额，请联系管理员");
        }
        String url = null;
        if(submitParam.getBusinessId().equals(VirtualGiftType.HF.getType())){
            url = MyPropertyConfigurer.getVal("virtual.gift.apply.hf.url");
        }else if(submitParam.getBusinessId().equals(VirtualGiftType.LL.getType())){
            url = MyPropertyConfigurer.getVal("virtual.gift.apply.ll.url");
        }
        String appKey = MyPropertyConfigurer.getVal("virtual.gift.apply.hfll.appKey");
        String uid = MyPropertyConfigurer.getVal("virtual.gift.apply.hfll.uid");
        String sign = MD5.MD5Encode(submitParam.getRechargeNo() + submitParam.getAmount()  + submitParam.getUserOrderNo() + appKey);
        HashMap<String, Object> params = new HashMap<String, Object>();
        
        params.put("re_url", Constants.getSystemPropertyByCodeType("app.host") + "/material/application/bchfcallback.do");
        params.put("business_id", submitParam.getBusinessId());
        params.put("user_order_no", submitParam.getUserOrderNo());
        params.put("recharge_no", submitParam.getRechargeNo());
        params.put("amount", submitParam.getAmount());
        params.put("uid", uid);
        params.put("sign", sign);
        log.info("bc hf params: " + JsonUtil.writeValue(params));
        String body = HttpUtil.get(url, params);
        submitParam.setBody(body);
        log.info("businessId: {}, 返回的报文： {} ",submitParam.getBusinessId(),body);
        if(StringUtils.isBlank(body)){
            throw new WxPltException("充值失败，充值接口返回为空");
        }
        JSONObject jsonObject = new JSONObject(body);
        Integer code = jsonObject.getInt("code");
        String message = jsonObject.getStr("message");
        if(10000!=code){
            log.error("虚拟礼品兑换失败： " + message + "==返回的报文： ", body);
            WxPltException wxPltException = new WxPltException(message);
            wxPltException.setExpMsg(message);
            throw wxPltException;
        }
        return submitParam;
    }

    /**
     * 异常检测
     */
    private String abnormalDetection(ApplicationHead applicationHead,ApplicationLine[] applicationLines) {
        String applicationUserId = applicationHead.getUserId();
        if (StringUtils.isBlank(applicationUserId)) {
            return "兑换人ID为空，请重新提交。";
        }
        String materialCode = null;
        for (ApplicationLine applicationLine : applicationLines) {
            if(materialCode == null){
                materialCode = applicationLine.getMaterialCode();
            }else{
                if(materialCode.equals(applicationLine.getMaterialCode())){
                    return "一次只允许兑换一种虚拟商品";
                }
            }
        }
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("applicationUserId", applicationUserId);
        map.put("today", DateUtil.getCurrentDate("yyyy-MM-dd"));
        List<B2BAbnormalDetection> b2BAbnormalDetections = wxtMaterialApplicationVoMapper.abnormalDetection(map);
        WxTPropertiesExample example = new WxTPropertiesExample();
        example.createCriteria().andCodetypeIn(Arrays.asList(Constants.VIRTUAL_GIFT_EXCHANGE_TIME_UP, Constants.VIRTUAL_GIFT_EXCHANGE_POINT_UP));
        List<WxTProperties> wxTProperties = wxTPropertiesMapper.selectByExample(example);
        String pointUp = null;
        String timeUp = null;
        for (WxTProperties wxTProperty : wxTProperties) {
            if (wxTProperty.getCodetype().equals(Constants.VIRTUAL_GIFT_EXCHANGE_TIME_UP)) {
                timeUp = wxTProperty.getCode();
            } else if (wxTProperty.getCodetype().equals(Constants.VIRTUAL_GIFT_EXCHANGE_POINT_UP)) {
                pointUp = wxTProperty.getCode();
            }
        }
        String error = null;
        if (CollectionUtil.isNotEmpty(b2BAbnormalDetections)) {
            //判断是否超出了限制
            B2BAbnormalDetection b2BAbnormalDetection = b2BAbnormalDetections.get(0);
            if (StringUtils.isNotBlank(timeUp) && b2BAbnormalDetection.getTimes() >= Integer.parseInt(timeUp)) {
                error = StrUtil.concat(true, "申请人: ", b2BAbnormalDetection.getApplicationUserName(),
                        ",今日已兑换次数: ", String.valueOf(b2BAbnormalDetection.getTimes()),
                        ",已兑换积分: ", String.valueOf(b2BAbnormalDetection.getCountPoint()),
                        ",达到每日兑换次数限制: ", timeUp);
            }
            if (StringUtils.isNotBlank(pointUp) && b2BAbnormalDetection.getCountPoint() >= Double.parseDouble(pointUp)) {
                error = StrUtil.concat(true, "申请人: ", b2BAbnormalDetection.getApplicationUserName(),
                        ",今日已兑换次数: ", String.valueOf(b2BAbnormalDetection.getTimes()),
                        ",已兑换积分: ", String.valueOf(b2BAbnormalDetection.getCountPoint()),
                        ",达到每日兑换积分限制: ", pointUp);
            }
            if (StringUtils.isNotBlank(error)) {
                log.error(error);
                WeixinMessageUtil.weixinAlarmMessagePush("PP-B2B-虚拟商品兑换", "虚拟商品兑换预警",
                        "虚拟商品兑换预警", "达到兑换限制条件", error);
            }
        }
        return error;
    }

    private List<CardInfo> resolveResultData(String body) throws Exception{
        List<CardInfo> list = new ArrayList<CardInfo>();
        if(StringUtils.isBlank(body)){
            return list;
        }
        String key = MyPropertyConfigurer.getVal("virtual.gift.apply.lpk.appKey");
        JSONObject jsonObject = new JSONObject(body);
        AES aes = new AES(Mode.CBC,
                Padding.NoPadding,
                key.substring(0, 16).getBytes(Charset.forName("UTF-8")),
                MD5.MD5Encode(key).substring(8, 24).getBytes(Charset.forName("UTF-8")));
        try {
            JSONObject result = jsonObject.getJSONObject("result");
            //如果result中有数据则解密充值卡信息
            JSONArray datas = result.getJSONArray("data");
            if(datas!=null){
                for (int i = 0; i < datas.size(); i++) {
                    CardInfo cardInfo = new CardInfo();
                    JSONObject object = datas.getJSONObject(i);
                    cardInfo.setCardNo(aes.decryptStrFromBase64(object.getStr("card_no"),Charset.forName("UTF-8")).trim());
                    cardInfo.setCardPwd(aes.decryptStrFromBase64(object.getStr("card_pwd"),Charset.forName("UTF-8")).trim());
                    cardInfo.setInvalidTime(object.getStr("invalid_time"));
                    list.add(cardInfo);
                }
            }
        }catch (Exception e){
            log.error("解密卡密信息出错： " + e.getMessage() + "返回的报文： ", body);
            throw new WxPltException("卡密信息解析失败。");
        }
        return list;
    }
    
    

    /**
     * 检查申请单行的所有物料库存是否足够
     *
     * @param applicationLines
     * @return message, 若库存不足, 则返回消息, 反之则返回null;
     */
    private String checkInventory(final ApplicationLine[] applicationLines, Map<String, Inventory2021> inventorySkuMap) {
        // 检查库存是否足够
        String stockMsg = null;
        for (ApplicationLine line : applicationLines) {
            long appQty = line.getApplicationQty();
            if (appQty < 1) {
                continue;
            }
            Inventory2021 inventory = inventorySkuMap.get(line.getMaterialSkuCode());
            if(inventory == null) {
                stockMsg = "物料" + line.getMaterialSkuCode() + "库不存在, 请联系雪佛龙销售";
                break;
            	
            }
            int stockQty = inventory.getCurrentQuantity();
            if (stockQty == 0 || appQty > stockQty) {
                stockMsg = "物料" + line.getMaterialSkuCode() + "库存不足, 请重新申请";
                break;
            }
        }
        return stockMsg;
    }

    /**
     * 获取  这个partnerId 对应 地促积分（fromPromotePoint） 和 促销积分（promotionPoint） 的积分
     * 前端material/js/applicationNew.js的在弹出选择（地促积分和促销积分）界面的时候会显示不同积分的值
     * @param partnerId
     * @return
     */
    @Override
    public Map<String, Object> getPromotionPointDetail(Long partnerId) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(Constants.RESULT_SUCCESS, false);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "获取促销积分信息失败");
        Map<String, Object> pointDetail = new HashMap<String, Object>();
        try {
            BigDecimal promotionPoint = pointAccountService.selectPromotionTotalPoint(partnerId, null);
            BigDecimal fromPromotePoint = pointAccountService.selectPromotionTotalPoint(partnerId, BusinessType.CALTEX_POINT_FROM_PROMOTE.name());
            pointDetail.put("promotionPoint", promotionPoint == null ? 0.0d : promotionPoint.doubleValue());
            pointDetail.put("fromPromotePoint", fromPromotePoint == null ? 0.0d : fromPromotePoint.doubleValue());
            resultMap.put("pointDetail", pointDetail);
            resultMap.put("success", true);
        } catch (Exception e) {
            resultMap.put("errorMsg", "获取促销积分信息失败");
            log.error("获取促销积分信息失败" + e.getLocalizedMessage());
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, false);
        }
        return resultMap;
    }

//    /**
//     * 检查申请单行的所有物料申请数量是否超过申请限制
//     * @param applicationLines 商品购买的细节
//     * @param partnerId 机构的id  SP的积分都是需要这个partnerId
//     * @param userId 用户的userId TC的b2b积分就是需要这个userId
//     * @param containsLockedQty 是否包含lockedQty
//     * @return
//     */
//    private String checkLimitQty(final ApplicationLine[] applicationLines, String applicationType, Long partnerId,String userId, Boolean containsLockedQty) {
//        // 检查库存是否足够
//        String stockMsg = null;
//        if (null == applicationLines || applicationLines.length < 1) {
//            stockMsg = "物料清单为空, 无法提交申请";
//            return stockMsg;
//        }
//
//
//        boolean isTotalZeroQty = true;
//        for (ApplicationLine line : applicationLines) {
//
//            Long appQty = line.getApplicationQty();
//            if (appQty < 1) {
//                continue;
//            }
//            isTotalZeroQty = false;
//
//            WXTMaterialSkuVoKey key = new WXTMaterialSkuVoKey();
//            key.setMaterialId(line.getMaterialId());
//            key.setMaterialSkuCode(line.getMaterialSkuCode());
//            WXTMaterialSkuVo sku = materialSkuVoMapper.selectByPrimaryKey(key);
//            Long limitQtyPerOrder = sku.getLimitQty() == null ? 0 : sku.getLimitQty();
//            Long limitQtyPerYear = sku.getTradeQty() == null ? 0 : sku.getTradeQty();
//
//            if (limitQtyPerOrder != 0L && appQty > limitQtyPerOrder) {
//                stockMsg = "物料" + line.getMaterialSkuCode() + "数量超过单次申请上限" + limitQtyPerOrder + ", 请重新申请";
//                break;
//            }
//
//            if (limitQtyPerYear != 0) {
//                // selectHistoryTotal这个函数查询的时候，如果applicationType是b2b的话，则使用userId，
//                // 如果不是则使用使用partnerId来查询
//                BigDecimal historyTotalBigDecimal = wxtMaterialApplicationDetailVoMapper.selectHistoryTotal(line.getMaterialId(), applicationType, partnerId, userId);
//                Double historyTotal = 0D;
//                if (historyTotalBigDecimal != null) {
//                    historyTotal = historyTotalBigDecimal.doubleValue();
//                }
//
//                if (containsLockedQty && null != line.getLockedQty()) {
//                    historyTotal = historyTotal - line.getLockedQty() + appQty;
//                } else {
//                    historyTotal = historyTotal + appQty;
//                }
//                if (historyTotal > limitQtyPerYear) {
//                    stockMsg = "物料" + line.getMaterialSkuCode() + "数量超过本年度申请上限" + limitQtyPerYear + ", 请重新申请";
//                    break;
//                }
//            }
//        }
//
//        if (isTotalZeroQty) {
//            stockMsg = "物料清单为空, 无法提交申请";
//        }
//        return stockMsg;
//    }

    private void updateMaterialInventory(WXTMaterialApplicationVo applicationVo, ApplicationLine[] lines, 
    		Map<String, Inventory2021> inventorySkuMap) throws WxPltException {
    	InventoryLog2021 inventoryLog = null;
    	Inventory2021 inventory = null;
    	List<Inventory2021> records = new ArrayList<Inventory2021>(inventorySkuMap.size());
    	for(ApplicationLine line : lines) {
    		inventory = inventorySkuMap.get(line.getMaterialSkuCode());
    		inventoryLog = new InventoryLog2021();
    		inventoryLog.setBusinessKey(applicationVo.getId().toString());
    		inventoryLog.setBusinessType(ApplicationBizService.INVENTORY_BUSINESS_TYPE);
    		inventoryLog.setModifyQuantity(-line.getApplicationQty().intValue());
    		inventoryLog.setOriginalQuantity(inventory.getCurrentQuantity());
    		inventoryLog.setLogDesc("积分订单【" + applicationVo.getApplicationCode() + "】减少【" + line.getApplicationQty() + "】");
    		inventoryLog.setExtProperty1(applicationVo.getApplicationCode());
    		inventory.setInventoryLog(inventoryLog);
    		inventory.setCurrentQuantity(inventory.getCurrentQuantity() - line.getApplicationQty().intValue());
    		records.add(inventory);
    	}
    	inventory2021BizService.save(records);
    }
    
//    /**
//     * 扣除虚拟库存
//     *
//     * @param lines
//     * @throws Exception
//     */
//    private void updateVirtualStock(String applicationCode, List<WXTMaterialApplicationDetailVo> lines) throws Exception {
//        // 扣除虚拟库存
//        Date now = DateUtil.getCurrentDate();
//        if (null != lines && !lines.isEmpty()) {
//            for (WXTMaterialApplicationDetailVo line : lines) {
//                WXTMaterialInventoryVo inventory = new WXTMaterialInventoryVo();
//                inventory.setVirtualStockQty(-line.getApplicationQty());
//                inventory.setLastUpdatedBy(line.getLastUpdatedBy());
//                inventory.setLastUpdateTime(now);
//                inventory.setMaterialId(line.getMaterialId());
//                inventory.setMaterialSkuCode(line.getMaterialSkuCode());
//                inventory.setWarehouseId(line.getWarehouseId());
//                int updateVirtualInventory = inventoryVoMapper.updateVirtualInventory(inventory);
//
//                if (1 == updateVirtualInventory) {
//                	applicationBizService.pushInventoryLog(now, line.getMaterialId(), line.getMaterialSkuCode(), line.getWarehouseId(),
//                            StockType.VIRTUAL, -line.getApplicationQty(), "申请单提交扣除", null, applicationCode);
//                }
//            }
//        }
//    }

    /**
     * 扣除真实库存
     *
     * @param lines
     * @throws Exception
     */
    private void updateRealStock(String applicationCode, List<WXTMaterialApplicationDetailVo> lines) throws Exception {
        Date now = DateUtil.getCurrentDate();
        if (null != lines && !lines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : lines) {
                applicationBizService.updateRealStock(now, line.getMaterialId(), line.getMaterialSkuCode(),
                        line.getWarehouseId(), -line.getApplicationQty(), "积分订单结算完成扣除", null, applicationCode,line.getVirtualGift());
            }
        }
    }
//
//    /**
//     * 获取当前订单的totalPrice
//     * ****注意这个是按照 实时的价格来计算的*****
//     * @param applicationId
//     * @return
//     */
//    private Double calcTotalPrice(Long applicationId) {
//        Double totalPrice = wxtMaterialApplicationDetailVoMapper.calcAppTotalPrice(applicationId);
//        return totalPrice;
//    }

    @Override
    public Map<String, Object> getShipmentInfo(Long workshopId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WorkshopMaster workshopVo = workshopMasterBizService.getBean(workshopId);
        if (null != workshopVo && null != workshopVo.getRegionId()) {
            String regionName = getRegionName(workshopVo.getRegionId());
            resultMap.put("regionName", regionName);
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getAddressList(Long partnerId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialApplicationVo> voList = null;
        List<ApplicationHead> list = new ArrayList<ApplicationHead>();
        WXTMaterialAddressConfigVoExample example = new WXTMaterialAddressConfigVoExample();
        example.createCriteria().andPartnerIdEqualTo(partnerId).andDeleteFlagNotEqualTo(true);
        List<WXTMaterialAddressConfigVo> addressList = wxtMaterialAddressConfigVoMapper.selectByExample(example);
        if (addressList != null && !addressList.isEmpty()) {
            for (WXTMaterialAddressConfigVo v : addressList) {
                ApplicationHead addressVo = new ApplicationHead();
                addressVo.setAddress(v.getAddressDetail());
                addressVo.setContacts(v.getContacts());
                addressVo.setNumber(v.getContactNumber());
                if (null != v.getAddressRegion() && !v.getAddressRegion().isEmpty()) {
                    addressVo.setDistCode(v.getAddressRegion());
                    String distCode = v.getAddressRegion();
                    addressVo.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    addressVo.setCityCode(getCityCode(Long.valueOf(distCode)));
                    String addressRegionName = getRegionName(Long.valueOf(v.getAddressRegion()));
                    addressVo.setAddressRegionName(addressRegionName);
                }
                list.add(addressVo);
            }
        } else {
            try {
                voList = wxtMaterialApplicationVoMapper.selectAddressList(partnerId);
            } catch (Exception e) {
                resultMap.put("list", null);
                resultMap.put(Constants.RESULT_SUCCESS, false);
                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取失败");
                resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
                return resultMap;
            }

            if (null != voList && !voList.isEmpty()) {
                for (WXTMaterialApplicationVo v : voList) {
                    ApplicationHead addressVo = new ApplicationHead();
                    addressVo.setAddress(v.getAddressDetail());
                    addressVo.setContacts(v.getContacts());
                    addressVo.setNumber(v.getContactNumber());
                    addressVo.setDistCode(v.getAddressRegion());
                    String distCode = v.getAddressRegion();
                    addressVo.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    addressVo.setCityCode(getCityCode(Long.valueOf(distCode)));
                    String addressRegionName = getRegionName(Long.valueOf(v.getAddressRegion()));
                    addressVo.setAddressRegionName(addressRegionName);
                    list.add(addressVo);
                }
            }
        }

        resultMap.put("list", list);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> getPointTypeAddressList(Long partnerId, String pointType) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialApplicationVo> voList = null;
        List<AddressConfigDTO> list = new ArrayList<AddressConfigDTO>();
        List<WXTMaterialAddressConfigVo> addressList = new ArrayList<WXTMaterialAddressConfigVo>();
        PointType point = PointType.getByApplicationType(pointType);
        if(point != null && point.getEditAddress()){
            resultMap.put("editAddress", true);
        }
        addressList = wxtMaterialAddressConfigVoMapper.selectPointTypeAddress(pointType,partnerId);
        if (addressList != null && !addressList.isEmpty()) {
            for (WXTMaterialAddressConfigVo v : addressList) {
                AddressConfigDTO addressVo = new AddressConfigDTO();
                addressVo.setId(v.getId());
                if(StringUtils.isBlank(v.getAttribute1())){
                    addressVo.setDefaultFlag(true);//预设地址不能删除
                }
                addressVo.setAddress(v.getAddressDetail());
                addressVo.setContacts(v.getContacts());
                addressVo.setNumber(v.getContactNumber());
                if (null != v.getAddressRegion() && !v.getAddressRegion().isEmpty()) {
                    addressVo.setDistCode(v.getAddressRegion());
                    String distCode = v.getAddressRegion();
                    addressVo.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    addressVo.setCityCode(getCityCode(Long.valueOf(distCode)));
                    String addressRegionName = getRegionName(Long.valueOf(v.getAddressRegion()));
                    addressVo.setAddressRegionName(addressRegionName);
                }
                list.add(addressVo);
            }
        } else {
            try {
                voList = wxtMaterialApplicationVoMapper.selectAddressList(partnerId);
            } catch (Exception e) {
                resultMap.put("list", null);
                resultMap.put(Constants.RESULT_SUCCESS, false);
                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取失败");
                resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
                return resultMap;
            }

            if (null != voList && !voList.isEmpty()) {
                for (WXTMaterialApplicationVo v : voList) {
                    AddressConfigDTO addressVo = new AddressConfigDTO();
                    addressVo.setAddress(v.getAddressDetail());
                    addressVo.setContacts(v.getContacts());
                    addressVo.setNumber(v.getContactNumber());
                    addressVo.setDistCode(v.getAddressRegion());
                    String distCode = v.getAddressRegion();
                    addressVo.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    addressVo.setCityCode(getCityCode(Long.valueOf(distCode)));
                    String addressRegionName = getRegionName(Long.valueOf(v.getAddressRegion()));
                    addressVo.setAddressRegionName(addressRegionName);
                    list.add(addressVo);
                }
            }
        }
        resultMap.put("list", list);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> getWorkshop(Long applicationId,String workShopId) throws Exception {
    	Map<String, Object> resultMap = new HashMap<String, Object>();
    	Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applicationId", applicationId);
        paramsMap.put("workShopId", workShopId);
        List<WXTMaterialAddressConfigVo> addressList = wxtMaterialAddressConfigVoMapper.selectWorkShop(paramsMap);
        List<ApplicationHead> list = new ArrayList<ApplicationHead>();
        if (addressList != null && !addressList.isEmpty()) {
        	 for (WXTMaterialAddressConfigVo v : addressList) {
                 ApplicationHead addressVo = new ApplicationHead();
                 addressVo.setAddress(v.getAddressDetail());
                 addressVo.setContacts(v.getContacts());
                 addressVo.setNumber(v.getContactNumber());
                 addressVo.setAddressRegionName(v.getAddressRegion());
                 addressVo.setBizType(v.getStatus());
                 addressVo.setWorkshopComments(v.getType());
                 addressVo.setWorkshopId(v.getId());
                 list.add(addressVo);
             }
        }
        resultMap.put("list", list);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> selectWorkShopByShopId(Long workShopId) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialAddressConfigVo> addressList = wxtMaterialAddressConfigVoMapper.selectWorkShopByShopId(workShopId);
        List<ApplicationHead> list = new ArrayList<ApplicationHead>();
        if (addressList != null && !addressList.isEmpty()) {
            for (WXTMaterialAddressConfigVo v : addressList) {
                ApplicationHead addressVo = new ApplicationHead();
                addressVo.setAddress(v.getAddressDetail());
                addressVo.setContacts(v.getContacts());
                addressVo.setNumber(v.getContactNumber());
                addressVo.setAddressRegionName(v.getAddressRegion());
                addressVo.setBizType(v.getStatus());
                addressVo.setWorkshopComments(v.getType());
                addressVo.setWorkshopId(v.getId());
                list.add(addressVo);
            }
        }
        resultMap.put("list", list);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取成功");
        return resultMap;
    };

    @Override
    public Map<String, Object> getlist(ApplicationParams params) {
        ResponseMap resultMap = new ResponseMap();
        try {
            WxTUser curUser = ContextUtil.getCurUser();
            if (curUser!= null && WxTUser.USER_MODEL_SP.equals(curUser.getUserModel())) {
                params.setApplicationOrgId(curUser.getOrgId());
            } else {
                params.setApplicationOrgId(null);
            }
            if(params.getApplicationType() != null && params.getApplicationType().size() == 1) {
                PointType pointType = ApplicationUtil.getPointTypeByAppType(params.getApplicationType().get(0));
                if(pointType != null){
                    pointType.processOrderParams(params, curUser);
                }
            }
            //获取权限码
            int permiss=DealerPermissUtil.getCurUserDealerPermissCode(dicService);
            List<WXTMaterialApplicationVo> wxtMaterialApplicationVos = wxtMaterialApplicationVoMapper.selectByPagination(params);
            if(params.getFromApp() != null && params.getFromApp()) {
                for (WXTMaterialApplicationVo wxtMaterialApplicationVo : wxtMaterialApplicationVos) {
                    List<WXTMaterialApplicationDetailVo> goods = wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(wxtMaterialApplicationVo.getId());
                    wxtMaterialApplicationVo.setApplicationlines(goods);
                }
            }
            resultMap.put("rows", wxtMaterialApplicationVos);
            resultMap.put("total", params.getTotalCount());
            resultMap.put("code", "success");
            resultMap.put("pointPermission", permiss);
            resultMap.put("errorMsg", "成功");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
		}
        return resultMap;
    }

    @Override
    public Map<String, Object> getWorkshopList(Long partnerId,String workShopName) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialApplicationVo> voList = null;
        List<ApplicationHead> list = new ArrayList<ApplicationHead>();

        WXTMaterialAddressConfigVoExample example = new WXTMaterialAddressConfigVoExample();
        // 这里的查询改为非删除，之前的查询条件type = caltex_point，会让很多用户查询不出address
        example.createCriteria().andPartnerIdEqualTo(partnerId).andDeleteFlagNotEqualTo(true);
//		example.createCriteria().andPartnerIdEqualTo(partnerId).andTypeEqualTo("caltex_point");
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("partnerId", partnerId);
        paramsMap.put("workShopName", workShopName);
        List<WXTMaterialAddressConfigVo> addressList = wxtMaterialAddressConfigVoMapper.selectWorkShopList(paramsMap);
        if (addressList != null && !addressList.isEmpty()) {
            for (WXTMaterialAddressConfigVo v : addressList) {
                ApplicationHead addressVo = new ApplicationHead();
                addressVo.setAddress(v.getAddressDetail());
                addressVo.setContacts(v.getContacts());
                addressVo.setNumber(v.getContactNumber());
                addressVo.setAddressRegionName(v.getAddressRegion());
                addressVo.setBizType(v.getStatus());
                addressVo.setWorkshopComments(v.getType());
                addressVo.setWorkshopId(v.getId());
                list.add(addressVo);
            }
        } else {
            try {
                voList = wxtMaterialApplicationVoMapper.selectAddressList(partnerId);
            } catch (Exception e) {
                resultMap.put("list", null);
                resultMap.put(Constants.RESULT_SUCCESS, false);
                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取失败");
                resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
                return resultMap;
            }

            if (null != voList && !voList.isEmpty()) {
                for (WXTMaterialApplicationVo v : voList) {
                    ApplicationHead addressVo = new ApplicationHead();
                    addressVo.setAddress(v.getAddressDetail());
                    addressVo.setContacts(v.getContacts());
                    addressVo.setNumber(v.getContactNumber());
                    addressVo.setDistCode(v.getAddressRegion());
                    String distCode = v.getAddressRegion();
                    addressVo.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    addressVo.setCityCode(getCityCode(Long.valueOf(distCode)));
                    String addressRegionName = getRegionName(Long.valueOf(v.getAddressRegion()));
                    addressVo.setAddressRegionName(addressRegionName);
                    list.add(addressVo);
                }
            }
        }

        resultMap.put("list", list);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> findWorkshopList(Long partnerId,String workShopName,Boolean onceFlag,Boolean potential) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialApplicationVo> voList = null;
        List<ApplicationHead> list = new ArrayList<ApplicationHead>();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("partnerId", partnerId);
        paramsMap.put("workShopName", workShopName);
        paramsMap.put("onceFlag",onceFlag);
        paramsMap.put("potential",potential);
        List<WXTMaterialAddressConfigVo> addressList = wxtMaterialAddressConfigVoMapper.findWorkShopList(paramsMap);
        if (addressList != null && !addressList.isEmpty()) {
            for (WXTMaterialAddressConfigVo v : addressList) {
                ApplicationHead addressVo = new ApplicationHead();
                addressVo.setAddress(v.getAddressDetail());
                addressVo.setContacts(v.getContacts());
                addressVo.setNumber(v.getContactNumber());
                addressVo.setAddressRegionName(v.getAddressRegion());
                addressVo.setBizType(v.getStatus());
                addressVo.setWorkshopComments(v.getType());
                addressVo.setWorkshopId(v.getId());
                list.add(addressVo);
            }
        } else {
            try {
                voList = wxtMaterialApplicationVoMapper.selectAddressList(partnerId);
            } catch (Exception e) {
                resultMap.put("list", null);
                resultMap.put(Constants.RESULT_SUCCESS, false);
                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取失败");
                resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
                return resultMap;
            }

            if (null != voList && !voList.isEmpty()) {
                for (WXTMaterialApplicationVo v : voList) {
                    ApplicationHead addressVo = new ApplicationHead();
                    addressVo.setAddress(v.getAddressDetail());
                    addressVo.setContacts(v.getContacts());
                    addressVo.setNumber(v.getContactNumber());
                    addressVo.setDistCode(v.getAddressRegion());
                    String distCode = v.getAddressRegion();
                    addressVo.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    addressVo.setCityCode(getCityCode(Long.valueOf(distCode)));
                    String addressRegionName = getRegionName(Long.valueOf(v.getAddressRegion()));
                    addressVo.setAddressRegionName(addressRegionName);
                    list.add(addressVo);
                }
            }
        }

        resultMap.put("list", list);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人物料寄送地址信息获取成功");
        return resultMap;
    }
    
    @Override
    public Map<String, Object> getApplicationHeadInfo(Long applicationId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationVo application = null;
        try {
            application = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        } catch (Exception e) {
            resultMap.put("application", null);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单信息获取失败");
            resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
            return resultMap;
        }

        if (null != application) {
            String addressRegion = application.getAddressRegion();
            if (!StringUtils.isEmpty(addressRegion)) {
                String addressRegionName = getRegionName(Long.valueOf(addressRegion));
                application.setAddressRegionName(addressRegionName);
            }
        }
        resultMap.put("application", application);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单信息获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> getApplicationHeadInfo4Edit(Long applicationId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationVo application = null;
        ApplicationHead applicationHead = new ApplicationHead();
        try {
            application = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        } catch (Exception e) {
            resultMap.put("application", null);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单信息获取失败");
            resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
            return resultMap;
        }

        if (null != application) {
            String addressRegion = application.getAddressRegion();
            if (!StringUtils.isEmpty(addressRegion)) {
                String addressRegionName = getRegionName(Long.valueOf(addressRegion));
                application.setAddressRegionName(addressRegionName);
            }
            if (application.getShipToWorkshop()) {
                applicationHead.setApplicationId(applicationId);
                applicationHead.setShip2Workshop(true);
                applicationHead.setApplicationName4Workshop(application.getApplicationName());
                applicationHead.setApplicationOrgId4Workshop(application.getApplicationOrgId());
                applicationHead.setApplicationOrgName4Workshop(application.getApplicationOrgName());
                applicationHead.setWorkshopId(application.getWorkshopId());
                applicationHead.setWorkshopName(application.getWorkshopName());
                if (null != application.getAddressRegion()) {
                    applicationHead.setWorkshopRegionId(Long.valueOf(application.getAddressRegion()));
                }
                applicationHead.setWorkshopAddress(application.getAddressDetail());
                applicationHead.setWorkshopContacts(application.getContacts());
                applicationHead.setWorkshopContactNumber(application.getContactNumber());
                applicationHead.setWorkshopRecipientDate(application.getRecipientDate());
                applicationHead.setWorkshopComments(application.getComments());
            } else {
                applicationHead.setApplicationId(applicationId);
                applicationHead.setShip2Workshop(false);
                applicationHead.setApplicationName(application.getApplicationName());
                applicationHead.setApplicationOrgId(application.getApplicationOrgId());
                applicationHead.setApplicationOrgName(application.getApplicationOrgName());
                applicationHead.setAddress(application.getAddressDetail());
                applicationHead.setWorkshopId(application.getWorkshopId());
                applicationHead.setWorkshopName(application.getWorkshopName());
                applicationHead.setContacts(application.getContacts());
                applicationHead.setNumber(application.getContactNumber());
                applicationHead.setDistCode(application.getAddressRegion());
                String distCode = application.getAddressRegion();
                if (!StringUtils.isEmpty(distCode)) {
                    applicationHead.setProvinceCode(getProvinceCode(Long.valueOf(distCode)));
                    applicationHead.setCityCode(getCityCode(Long.valueOf(distCode)));
                }
                applicationHead.setRecipientDate(application.getRecipientDate());
                applicationHead.setComments(application.getComments());
            }
            applicationHead.setFromSource(application.getPromotionType());
        }

        resultMap.put("application", applicationHead);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单信息获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> getApplicationLineInfo(Long applicationId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialApplicationDetailVo> applicationlines = null;
        try {
            applicationlines = wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(applicationId);
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
            resultMap.put("applicationLines", null);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请行详情获取失败");
            resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
            return resultMap;
        }

        resultMap.put("applicationLines", applicationlines);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请行详情获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> getWarehouseOutLineInfo(Long warehouseOutId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<WXTMaterialWarehouseOutDetailVo> warehouseOutlines = null;
        try {
            warehouseOutlines = materialWarehouseOutDetailVoMapper.selectWithMaterialInfo(warehouseOutId);
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
            resultMap.put("warehouseOutLines", null);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "出库单行详情获取失败");
            resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
            return resultMap;
        }

        resultMap.put("warehouseOutLines", warehouseOutlines);
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "出库单行详情获取成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> markAsPending(Long applicationId, Date lastUpdateTime) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();

        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setApplicationStatus(ApplicationState.PENDING_ON_L1.getValue());
        record.setLastUpdatedBy(ContextUtil.getCurUserId());

        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        example.createCriteria().andDeleteFlagEqualTo(false).andIdEqualTo(applicationId)
                .andApplicationStatusEqualTo(ApplicationState.READY.getValue())
                .andLastUpdateTimeEqualTo(lastUpdateTime);

        int updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);

        log.debug("标记申请单为[一级审批中]: 更新结果:{}, 申请ID:{}", updateResult, applicationId);
        resultMap.put(Constants.RESULT_SUCCESS, updateResult > 0);
        if (updateResult != 1) {
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "更新为[一级审批中]失败,请刷新页面再试一次");
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> approve(Long approvalLevel, Long applicationId, Date lastUpdateTime, String comments) throws Exception {
        return approve(approvalLevel, applicationId, lastUpdateTime, comments, "4");
    }

    @Override
    public Map<String, Object> approve(Long approvalLevel, Long applicationId, Date lastUpdateTime, String comments, String expressCompany) throws Exception {

        if (1L == approvalLevel || -1L == approvalLevel) {
            return approveL1(approvalLevel, applicationId, lastUpdateTime, comments, expressCompany);
        } else if (2L == approvalLevel) {
            return approveL2(approvalLevel, applicationId, lastUpdateTime, comments, expressCompany);
        } else if (3L == approvalLevel) {
            return approveL3(approvalLevel, applicationId, lastUpdateTime, comments, expressCompany);
        } else {
            log.error("审批操作失败, 审批等级错误");
            return prepareResultMap(false, "审批操作失败, 审批等级错误", null);
        }
    }

    @Override
    public Map<String, Object> batchApprove(Long approvalLevel, List<WXTMaterialApplicationVo> applications) throws Exception {

        Map<String, Object> result = null;
        boolean hasFaild = false;
        if (1L == approvalLevel || -1L == approvalLevel) {
            for (WXTMaterialApplicationVo app : applications) {
                result = approveL1(approvalLevel, app.getId(), app.getLastUpdateTime(), "", app.getPostCompany());
                if (null == result || null == result.get(Constants.RESULT_SUCCESS) || !(Boolean) result.get(Constants.RESULT_SUCCESS)) {
                    hasFaild = true;
                    log.error("初步确认操作失败, 程序异常，物料申请ID:" + app.getId());
                }
            }
        } else if (2L == approvalLevel) {
            for (WXTMaterialApplicationVo app : applications) {
                result = approveL2(approvalLevel, app.getId(), app.getLastUpdateTime(), "", app.getPostCompany());
                if (null == result || null == result.get(Constants.RESULT_SUCCESS) || !(Boolean) result.get(Constants.RESULT_SUCCESS)) {
                    hasFaild = true;
                    log.error("最终审批操作失败, 程序异常，物料申请ID:" + app.getId());
                }
            }
        } else if (3L == approvalLevel) {
            for (WXTMaterialApplicationVo app : applications) {
                result = approveL3(approvalLevel, app.getId(), app.getLastUpdateTime(), "", app.getPostCompany());
                if (null == result || null == result.get(Constants.RESULT_SUCCESS) || !(Boolean) result.get(Constants.RESULT_SUCCESS)) {
                    hasFaild = true;
                    log.error("加急审批操作失败, 程序异常，物料申请ID:" + app.getId());
                }
            }
        } else {
            log.error("审批操作失败, 审批等级错误");
            return prepareResultMap(false, "审批操作失败, 审批等级错误", null);
        }
        String msg = "操作成功";
        if (hasFaild) {
            msg = "操作失败，请刷新重试或者请求IT支持";
        }
        return prepareResultMap(!hasFaild, msg, null);
    }

    /**
     * 一级审批，即物料申请数量确认审批， 通过后发给最终审批者审核
     *
     * @param approvalLevel
     * @param applicationId
     * @param lastUpdateTime
     * @param comments
     * @param expressCompany
     * @return resultMap
     * @throws Exception
     */
    private Map<String, Object> approveL1(Long approvalLevel, Long applicationId, Date lastUpdateTime, String comments, String expressCompany) throws Exception {
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setLastUpdateTime(DateUtil.getCurrentDate());
        record.setLastUpdatedBy(ContextUtil.getCurUserId());
        record.setPostCompany(expressCompany);
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false).andIdEqualTo(applicationId);
        criteria.andApplicationStatusIn(Arrays.asList(ApplicationState.READY.getValue(),
                ApplicationState.PENDING_ON_L1.getValue()));
        // Ervin 2017/4/20 客户要求(与万华讨论) 暂时不启用二级审批,只需要一级审批
        // Ervin 2017/8/24客户要求(与万华讨论) 需要两级审批制度, 一级审批为确认数量,二级审批为最终审批
        record.setApplicationStatus(ApplicationState.PENDING_ON_L2.getValue());

        int updateResult = -2;
        if (!hasApprovalPermission(ContextUtil.getCurUser(), approvalLevel)) {
            log.error("审批操作失败, 没有权限进行此操作");
            return prepareResultMap(false, "没有权限进行此操作", null);
        }

        updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);
        if (updateResult != 1) {
            log.error("审批操作失败, 更新申请单状态失败");
            return prepareResultMap(false, "审批操作失败, 请刷新再试", null);
        }

        // 发送邮件到最终审批者
        sendReadyEmail(applicationId, record.getLastUpdateTime().getTime());
        // 发送邮件到审批者：操作成功
        sendEmail4OperationSuccess(applicationId, true, true);
        // 申请单状态变化历史记录
        applicationBizService.pushApplicationHistory(applicationId, "确认通过", ApplicationState.PENDING_ON_L1.getValue(), ApplicationState.PENDING_ON_L2.getValue(), comments);

        log.debug("标记申请单为[审批通过]: 更新结果:{}, 申请ID:{}", updateResult, applicationId);
        return prepareResultMap(true, "审批操作成功", null);
    }

    /**
     * 最终审批， 通过后发往第三方供应商（礼享网，桑瑞光辉）下订单或者直接发货
     *
     * @param approvalLevel
     * @param applicationId
     * @param lastUpdateTime
     * @param comments
     * @param expressCompany
     * @return resultMap
     * @throws Exception
     */
    private Map<String, Object> approveL2(Long approvalLevel, Long applicationId, Date lastUpdateTime, String comments, String expressCompany) throws Exception {
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setLastUpdateTime(DateUtil.getCurrentDate());
        record.setLastUpdatedBy(ContextUtil.getCurUserId());
        record.setPostCompany(expressCompany);
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false).andIdEqualTo(applicationId);
        criteria.andApplicationStatusEqualTo(ApplicationState.PENDING_ON_L2.getValue());
        record.setApplicationStatus(ApplicationState.APPROVED.getValue());

        int updateResult = -2;
        if (!hasApprovalPermission(ContextUtil.getCurUser(), approvalLevel)) {
            log.error("审批操作失败, 没有权限进行此操作");
            return prepareResultMap(false, "没有权限进行此操作", null);
        }

        try {
            // 更新申请单主信息：申请单状态和快递公司设置
            updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);

            if (updateResult != 1) {
                log.error("审批失败, 更新申请单状态失败.");
                return prepareResultMap(false, "审批失败, 更新申请单状态失败.", "审批失败, 更新申请单状态失败.");
            }

            // 推送到第三方供应商下单或直接发货, 快递公司可以由审批者修改, 页面配置期望到达日期
            Map<String, Object> pushResult = push2ThirdPartyVendor(applicationId);
            if (null == pushResult) {
                log.error("审批操作成功, 但提交到第三方供应商失败");
                return prepareResultMap(false, "审批操作成功, 但提交到第三方供应商失败", "");
            }

            if (!(Boolean) pushResult.get(Constants.RESULT_SUCCESS)) {
                return pushResult;
            }

            // 发送已审批邮件到申请者
            sendApprovedEmail(applicationId);
            // 发邮件给审批者, 操作成功
            sendEmail4OperationSuccess(applicationId, true, false);
            // 申请单状态变化历史记录
            applicationBizService.pushApplicationHistory(applicationId, "审批通过", ApplicationState.PENDING_ON_L2.getValue(), ApplicationState.APPROVED.getValue(), comments);

        } catch (Exception e) {
            log.error("ApplicationServiceImpl.approve() exception", e);
            return prepareResultMap(false, "审批失败, 系统异常", "程序异常，回滚变更:" + e.getMessage());
        }

        log.debug("标记申请单为[审批通过]: 更新结果:{}, 申请ID:{}", updateResult, applicationId);
        return prepareResultMap(true, "审批操作成功", null);
    }

    /**
     * 加急审批积分订单， 通过后等待出库发货
     *
     * @param approvalLevel
     * @param applicationId
     * @param lastUpdateTime
     * @param comments
     * @param expressCompany
     * @return resultMap
     * @throws Exception
     */
    private Map<String, Object> approveL3(Long approvalLevel, Long applicationId, Date lastUpdateTime, String comments, String expressCompany) throws Exception {
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        Long userId = ContextUtil.getCurUserId();
        Date now = DateUtil.getCurrentDate();
        record.setLastUpdateTime(now);
        record.setLastUpdatedBy(userId);
        record.setPostCompany(expressCompany);
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false).andIdEqualTo(applicationId);
        criteria.andApplicationStatusEqualTo(ApplicationState.PENDING_4_URGENT.getValue());
        record.setApplicationStatus(ApplicationState.PENDING_ON_OUTBOUND.getValue());

        int updateResult = -2;
        if (!hasApprovalPermission(ContextUtil.getCurUser(), approvalLevel)) {
            log.error("审批操作失败, 没有权限进行此操作");
            return prepareResultMap(false, "没有权限进行此操作", null);
        }

        try {
            WXTMaterialApplicationVo application = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
            PointType pointType = ApplicationUtil.getPointTypeByAppType(application.getApplicationType());
            String pointCode = pointType == null ? application.getApplicationType() : pointType.getValue();
            // 应该根据订单的数据来计算totalPrice
            Double totalPrice = applicationBizService.calcTotalPriceByAppDetail(application.getId());
            Map<String, Object> pointServiceRes = pointService.consumePoints(application.getApplicationOrgId(), application.getApplicationUserId(),totalPrice, application.getApplicationCode(),
                    application.getApplicationOrgName(), pointCode, application.getBusinessTypeCode(), new HashMap<String, Object>());
            if (!Constants.SUCCESS_CODE.equals(pointServiceRes.get(Constants.RESULT_CODE_KEY))) {
                throw new Exception("账户积分不足");
            }

            // 更新申请单主信息：申请单状态
            updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);

            if (updateResult != 1) {
                log.error("审批失败, 更新积分订单状态失败.");
                return prepareResultMap(false, "审批失败, 更新积分订单状态失败.", "审批失败, 更新积分订单状态失败.");
            }

            // 是否直接推送仓库 TODO
            // 推送到第三方供应商下单或直接发货, 快递公司可以由审批者修改, 页面配置期望到达日期
//			Map<String, Object> pushResult = push2ThirdPartyVendor(applicationId);
//			if (null == pushResult) {
//				log.error("审批操作成功, 但提交到第三方供应商失败");
//				return prepareResultMap(false, "审批操作成功, 但提交到第三方供应商失败", "");
//			}
//
//			if(!(Boolean)pushResult.get(Constants.RESULT_SUCCESS)) {
//				return pushResult;
//			}

            // 发送已审批邮件到申请者
            // sendApprovedEmail(applicationId);
            // 发邮件给审批者, 操作成功
            // sendEmail4OperationSuccess(applicationId, true, false);

            // 申请单状态变化历史记录
            applicationBizService.pushApplicationHistory(applicationId, "审批通过, 待出库", ApplicationState.PENDING_4_URGENT.getValue(), ApplicationState.PENDING_ON_OUTBOUND.getValue(), comments);

            // 加德士积分礼品兑换的场合需要生成出库单
            pushWarehouseOutInfo(applicationId);

            // 扣除真实库存
            List<WXTMaterialApplicationDetailVo> applicationlines = wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(applicationId);
            updateRealStock(record.getApplicationCode(), applicationlines);

        } catch (Exception e) {
            log.error("ApplicationServiceImpl.approve() exception", e);
            return prepareResultMap(false, "审批失败, 系统异常", "程序异常，回滚变更:" + e.getMessage());
        }

        log.debug("标记申请单为[审批通过]: 更新结果:{}, 申请ID:{}", updateResult, applicationId);
        return prepareResultMap(true, "审批操作成功", null);
    }

    /**
     * 生成出库单
     * @param applicationId
     */
    private void pushWarehouseOutInfo(Long applicationId) {
        Long userId = ContextUtil.getCurUserId();
        Date now = DateUtil.getCurrentDate();
        // 生成出库单，这里并没有记录b2b积分需要的applicationUserId，
        // 查询的时候需要去关联application的表，不需要这么多的冗余数据
        WXTMaterialApplicationVo application = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        List<WXTMaterialApplicationDetailVo> lines = wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(applicationId);
        WXTMaterialWarehouseOutVo warehouseOut = new WXTMaterialWarehouseOutVo();
        warehouseOut.setAddressDetail(application.getAddressDetail());
        warehouseOut.setAddressRegion(application.getAddressRegion());
        warehouseOut.setAdminComments("");
        warehouseOut.setApplicationCode(application.getApplicationCode());
        warehouseOut.setApplicationId(application.getId());
        warehouseOut.setApplicationName(application.getApplicationName());
        warehouseOut.setApplicationOrgId(application.getApplicationOrgId());
        warehouseOut.setApplicationPersonId(application.getApplicationPersonId());
        warehouseOut.setApplicationTime(application.getApplicationTime());
        warehouseOut.setApplicationType(application.getPromotionType());
        warehouseOut.setAttribute1(application.getAttribute1());
        warehouseOut.setAttribute2(application.getAttribute2());
        warehouseOut.setComments(application.getComments());
        warehouseOut.setContactNumber(application.getContactNumber());
        warehouseOut.setContacts(application.getContacts());
        warehouseOut.setCreatedBy(userId);
        warehouseOut.setCreationTime(now);
        warehouseOut.setDeleteFlag(false);
        warehouseOut.setIsUrgent(application.getIsUrgent());
        warehouseOut.setLastUpdatedBy(userId);
        warehouseOut.setLastUpdateTime(now);
        warehouseOut.setPostCompany(application.getPostCompany());
        warehouseOut.setRecipientDate(application.getRecipientDate());
        warehouseOut.setStatus(ApplicationState.PENDING_ON_OUTBOUND.getValue());


        Map<Long, String> warehouses = new HashMap<Long, String>();
        if (lines != null && !lines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo v : lines) {
                if (!warehouses.containsKey(v.getWarehouseId())) {
                    warehouses.put(v.getWarehouseId(), v.getWarehouseName());
                }
            }

            for (Entry<Long, String> warehouse : warehouses.entrySet()) {
                warehouseOut.setWarehouseId(warehouse.getKey());
                warehouseOut.setWarehouseName(warehouse.getValue());
                materialWarehouseOutVoMapper.insertSelective(warehouseOut);
                long warehouseOutId = warehouseOut.getId();

                for (WXTMaterialApplicationDetailVo v : lines) {
                    if (null != v.getWarehouseId() && v.getWarehouseId().equals(warehouse.getKey())) {
                        WXTMaterialWarehouseOutDetailVo outDetail = new WXTMaterialWarehouseOutDetailVo();
                        outDetail.setApplicationQty(v.getApplicationQty());
                        outDetail.setAttribute1(v.getAttribute1());
                        outDetail.setAttribute2(v.getAttribute2());
                        outDetail.setComments(v.getComments());
                        outDetail.setCreatedBy(userId);
                        outDetail.setCreationTime(now);
                        outDetail.setDeleteFlag(false);
                        outDetail.setLastUpdatedBy(userId);
                        outDetail.setLastUpdateTime(now);
                        outDetail.setMaterialId(v.getMaterialId());
                        outDetail.setMaterialPrice(new BigDecimal(0));
                        outDetail.setMaterialSkuCode(v.getMaterialSkuCode());
                        outDetail.setWarehouseId(v.getWarehouseId());
                        outDetail.setWarehouseOutId(warehouseOutId);
                        materialWarehouseOutDetailVoMapper.insertSelective(outDetail);
                    }
                }
                warehouseOut.setId(null);
            }
        }
    }


    private Map<String, Object> prepareResultMap(Boolean isSuccess, String msg, String exceptionMsg) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(Constants.RESULT_SUCCESS, isSuccess);
        resultMap.put("msg", msg);
        resultMap.put(Constants.RESULT_ERROR, exceptionMsg);
        return resultMap;
    }

    private Map<String, Object> push2ThirdPartyVendor(Long applicationId) throws Exception {
        List<WXTMaterialApplicationDetailVo> lines = wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(applicationId);
        WXTMaterialApplicationVo head = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);

        Boolean hasSMCMaterial = false;
        Boolean hasSunrainMaterial = false;
        Boolean push2SMCSuccess = true;
        Boolean push2SunrainSucess = true;

        for (WXTMaterialApplicationDetailVo line : lines) {
            if (MaterialSource.isFromEnjoygifts(line.getMaterialSource())) {
                hasSMCMaterial = true;
            }
            if (MaterialSource.isFromSunrain(line.getMaterialSource())) {
                hasSunrainMaterial = true;
            }
        }

        String smcOrderCode = null;
        // 根据申请单包含物料来源的不同情况， 分别推送到第三方供应商
        if (hasSMCMaterial) {
            smcOrderCode = push2SMC(head, lines);
            if (StringUtils.isEmpty(smcOrderCode)) {
                push2SMCSuccess = false;
            }
        }

        if (hasSunrainMaterial) {
            push2SunrainSucess = push2Sunrain(head, lines);
        }

        // 根据申请单包含物料来源的不同情况，以及第三方处理的结果，做不同的处理
        if (hasSMCMaterial && hasSunrainMaterial) {
            if (push2SunrainSucess) {
                return push2SMCCallback(applicationId, smcOrderCode);
            }

            if (push2SMCSuccess && !push2SunrainSucess) {
                Map<String, Object> push2SMCCallbackResult = push2SMCCallback(applicationId, smcOrderCode);
                if (!(Boolean) push2SMCCallbackResult.get(Constants.RESULT_SUCCESS)) {
                    push2SMCCallbackResult.put("msg", push2SMCCallbackResult.get("msg") + ", 邮件到桑瑞光辉发送失败(桑瑞提供的部分物料)");
                    return push2SMCCallbackResult;
                } else {
                    log.error("审批异常， 请联系IT支持, 向礼享网下单成功, 但是邮件到桑瑞光辉发送失败(桑瑞提供的部分物料)");
                    return prepareResultMap(false, "审批异常， 请联系IT支持, 向礼享网下单成功, 但是邮件到桑瑞光辉发送失败(桑瑞提供的部分物料)", null);
                }
            }

            if (!push2SMCSuccess && !push2SunrainSucess) {
                log.error("审批异常， 请联系IT支持, 向礼享网下单失败(礼享网提供的物料部分), 同时邮件到桑瑞光辉发送失败(桑瑞提供的部分物料)");
                return prepareResultMap(false, "审批异常， 请联系IT支持, 向礼享网下单失败(礼享网提供的物料部分), 同时邮件到桑瑞光辉发送失败(桑瑞提供的部分物料)", null);
            }
        }

        if (hasSMCMaterial && !hasSunrainMaterial) {
            return push2SMCCallback(applicationId, smcOrderCode);
        }

        if (!hasSMCMaterial && hasSunrainMaterial) {
            if (push2SunrainSucess) {
                return prepareResultMap(true, "", "");
            } else {
                log.error("系统异常, 请联系IT支持人员, 发送邮件到桑瑞光辉失败");
                return prepareResultMap(false, "系统异常, 请联系IT支持人员, 发送邮件到桑瑞光辉失败", "系统异常, 请联系IT支持人员, 发送邮件到桑瑞光辉失败");
            }
        }

        if (!hasSMCMaterial && !hasSunrainMaterial) {
            log.error("系统异常, 该申请单没有需要发送到第三方的物料清单");
            return prepareResultMap(false, "系统异常, 该申请单没有需要发送到第三方的物料清单", "");
        }

        return prepareResultMap(true, "第三方推送成功", null);

    }

    private Map<String, Object> push2SMCCallback(Long applicationId, String smcOrderCode) {
        WXTMaterialApplicationVo record = null;
        WXTMaterialApplicationVoExample example = null;
        int updateResult = -2;
        if (StringUtils.isEmpty(smcOrderCode)) {
            record = new WXTMaterialApplicationVo();
            record.setApplicationStatus(ApplicationState.PENDING_ON_L2.getValue());
            example = new WXTMaterialApplicationVoExample();
            example.createCriteria().andDeleteFlagEqualTo(false).andIdEqualTo(applicationId);
            updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);

            log.error("审批操作成功, 但提交到SMC失败");
            return prepareResultMap(false, "审批失败, 无法提交订单到礼享网", "审批操作成功, 但提交到SMC失败, 请联系IT支持人员");
        } else {
            // 更新状态SMC订单号
            record = new WXTMaterialApplicationVo();
            record.setSmcOrderCode(smcOrderCode);
            record.setIsDone2Smc(true);
            example = new WXTMaterialApplicationVoExample();
            example.createCriteria().andDeleteFlagEqualTo(false).andIdEqualTo(applicationId);
            updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);

            if (updateResult != 1) {
                log.error("系统异常, 请联系IT支持人员, 回写SMC订单号失败");
                return prepareResultMap(false, "审批失败, 礼享网下单成功， 但是更新系统SMC订单号失败", "系统异常, 请联系IT支持人员, 回写SMC订单号失败");
            }
        }

        return prepareResultMap(true, "推送到礼享网下单成功", null);
    }

    private Boolean push2Sunrain(WXTMaterialApplicationVo head, List<WXTMaterialApplicationDetailVo> lines) throws Exception {
        List<WXTMaterialApplicationDetailVo> sunrainLines = new ArrayList<WXTMaterialApplicationDetailVo>();
        if (null != lines && !lines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : lines) {
                if (MaterialSource.isFromSunrain(line.getMaterialSource())) {
                    sunrainLines.add(line);
                }
            }
        }
        if (sunrainLines.isEmpty()) {
            log.warn("系统异常, 没有需要桑瑞光辉发货的物料清单");
            return true;
        }
        Boolean sendEmailResult = false;
        try {
            sendEmailResult = sendEmail2Sunrain(head, lines);
        } catch (Exception e) {
            sendEmailResult = false;
            log.error("发送邮件到桑瑞光辉失败", e);
        }

        return sendEmailResult;
    }

    private Boolean sendEmail2Sunrain(WXTMaterialApplicationVo head, List<WXTMaterialApplicationDetailVo> lines) throws Exception {

        if (null == head) {
            return false;
        }
        head.setAddressRegionName(getRegionName(Long.valueOf(head.getAddressRegion())));
        if (null != lines && !lines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : lines) {
                String[] split = line.getMaterialSkuCode().split("-");
                line.setMaterialSkuCode(split[1] + ',' + split[2]);
            }
        }

        String sunrainRecipients = getSunrainRecipients();
        String[] acceptEmail = {"<EMAIL>", "<EMAIL>"};
        if (StringUtils.isNotEmpty(sunrainRecipients)) {
            acceptEmail = sunrainRecipients.split(",");
        }
        String[] ccEmail = null;
        String acceptPersonName = "桑瑞光辉";
        String partnerName = head.getApplicationOrgName();
        String subjectName = "雪佛龙合伙人【" + partnerName + "】物料申请待发货";

        Map<String, Object> contentMap = new HashMap<String, Object>();
        contentMap.put("mailRecipient", acceptPersonName);
        contentMap.put("messageBody", "您好，雪佛龙合伙人【" + partnerName + "】有一个新的物料申请单需要发货， 详情如下：");
        contentMap.put("applicationHead", head);
        contentMap.put("applicationlines", lines);
        return emailSenderService.sendEmailForCommon(HttpSessionGets.getSession().getServletContext(), acceptEmail, ccEmail, subjectName, contentMap, null, "material_order_sunrain.ftl");

    }

    /**
     * 准备参数并提交订单请求到礼享网
     *
     * @param head
     * @param lines
     * @throws Exception
     */
    private String push2SMC(WXTMaterialApplicationVo head, List<WXTMaterialApplicationDetailVo> lines) throws Exception {
        if (null != head && null != lines && !lines.isEmpty()) {
            SmartCommOrder order = new SmartCommOrder();
            order.setChevronOrderCode(head.getApplicationCode());
            order.setReceiveName(head.getContacts());
            order.setReceiveMobile(head.getContactNumber());
            String addressRegion = head.getAddressRegion();
            String addressProvinceName = getProvinceName(Long.valueOf(addressRegion));
            order.setReceiveProvice(addressProvinceName);
            String receiveAddress = "";
            if (head.getShipToWorkshop()) {
                receiveAddress = head.getAddressDetail() + ", 门店: " + head.getWorkshopName();
            } else {
                receiveAddress = getRegionName(Long.valueOf(head.getAddressRegion())) + " " + head.getAddressDetail();
            }
            order.setReceiveAddress(receiveAddress);
            order.setPostCompany(head.getPostCompany());

            order.setRecipientDate(DateUtil.getDateStr(head.getRecipientDate(), "yyyy-MM-dd"));
            order.setOrderPurpose("雪佛龙物料");
            order.setCostCenter("DEFAULT"); // TODO 成本中心不设置
            order.setComment(head.getComments());
            List<SmartCommOrderLine> itemList = new ArrayList<SmartCommOrderLine>();
            for (WXTMaterialApplicationDetailVo line : lines) {
                if (MaterialSource.isFromEnjoygifts(line.getMaterialSource())) {
                    SmartCommOrderLine ol = new SmartCommOrderLine();
                    ol.setUnitItemCd(line.getSmcMaterialCode());
                    ol.setUnitSkuCd(line.getMaterialSkuCode());
                    ol.setQty(line.getApplicationQty());
                    ol.setComment(line.getComments());
                    itemList.add(ol);
                }
            }
            order.setItemList(itemList.toArray(new SmartCommOrderLine[itemList.size()]));

            String smcOrderCode = smartCommService.createOrder(order);
            return smcOrderCode;
        }

        return null;

    }

    @Override
    public Map<String, Object> disallow(Long approvalLevel, Long applicationId, Date lastUpdateTime, String comments) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setLastUpdateTime(DateUtil.getCurrentDate());
        record.setLastUpdatedBy(ContextUtil.getCurUserId());
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false).andIdEqualTo(applicationId);

        String oStatus = null;
        boolean isLevelOne = true;
        if (1L == approvalLevel) {
            oStatus = ApplicationState.PENDING_ON_L1.getValue();
        } else if (2L == approvalLevel) {
            isLevelOne = false;
            oStatus = ApplicationState.PENDING_ON_L2.getValue();
        } else if (3L == approvalLevel) {
            isLevelOne = false;
            oStatus = ApplicationState.PENDING_4_URGENT.getValue();
        } else {
            resultMap.put("success", false);
            resultMap.put("msg", "驳回操作失败, 审批等级错误");
            return resultMap;
        }

        criteria.andApplicationStatusIn(Arrays.asList(ApplicationState.READY.getValue(),
                ApplicationState.PENDING_ON_L1.getValue(),
                ApplicationState.PENDING_4_URGENT.getValue(),
                ApplicationState.PENDING_ON_L2.getValue()));

        record.setApplicationStatus(ApplicationState.REJECTED.getValue());
        int updateResult = -2;
        if (hasApprovalPermission(ContextUtil.getCurUser(), approvalLevel)) {
            updateResult = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);
            if (updateResult == 1) {
                // 申请单状态变化历史记录
            	applicationBizService.pushApplicationHistory(applicationId, "驳回", oStatus, ApplicationState.REJECTED.getValue(), comments);
                resultMap.put("msg", "驳回操作成功");
                resultMap.put("success", true);
                // 回滚虚拟库存
                applicationBizService.rollbackVirtualStock(applicationId);
                if (3L != approvalLevel) {
                    // 发邮件给审批者, 操作成功
                    sendEmail4OperationSuccess(applicationId, false, isLevelOne);
                    // 发送邮件到申请者
                    sendRejectedEmail(applicationId);
                }
            } else {
                resultMap.put("msg", "驳回操作失败, 请刷新再试");
                resultMap.put("success", false);
            }
        } else {
            resultMap.put("success", false);
            resultMap.put("msg", "没有权限进行此操作");
        }
        log.debug("标记申请单为[驳回]: 更新结果:{}, 申请ID:{}", updateResult, applicationId);
        return resultMap;
    }

    @Override
    public Map<String, Object> recall(Long applicationId, Date lastUpdateTime) throws Exception {
        //获取订单详情
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setLastUpdateTime(DateUtil.getCurrentDate());
        record.setLastUpdatedBy(ContextUtil.getCurUserId());
        record.setApplicationStatus(ApplicationState.DRAFT.getValue());
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        example.createCriteria().andIdEqualTo(applicationId).andLastUpdateTimeEqualTo(lastUpdateTime)
                .andApplicationStatusEqualTo(ApplicationState.READY.getValue());
        int updateByExample = -1;
        try {
            updateByExample = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);
        } catch (Exception e) {
            resultMap.put("errorMsg", "召回申请单失败, 后台错误");
            log.error("召回申请单失败,后台错误: 申请ID:{}", applicationId);
            resultMap.put("success", false);
            resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
            return resultMap;
        }

        if (updateByExample != 1) {
            resultMap.put("errorMsg", "召回申请单失败, 申请单状态已变更");
            log.error("召回申请单失败: 申请ID:{}", applicationId);
            resultMap.put("success", false);
            return resultMap;
        }
        // 回滚虚拟库存
        applicationBizService.rollbackVirtualStock(applicationId);

        // 申请单状态变化历史记录
        applicationBizService.pushApplicationHistory(applicationId, "召回", ApplicationState.READY.getValue(), ApplicationState.DRAFT.getValue(), "");
        resultMap.put("success", true);
        resultMap.put("successMsg", "召回申请成功, 您现在可以修改该申请");
        log.debug("召回申请单成功: 更新结果:{}, 申请ID:{}", updateByExample, applicationId);
        return resultMap;
    }

    /**
     * 更新申请单
     *
     * @param applicationHead
     * @param applicationLines
     * @return
     */

    private Map<String, Object> update(ApplicationHead applicationHead, ApplicationLine[] applicationLines) {
    	throw new RuntimeException("暂不支持");
//        Map<String, Object> resultMap = new HashMap<String, Object>();
//        int res4Line = 0;
//        int res4Head = 0;
//
//        try {
//            // 根据目前applicationStatus来判断是否需要更新price
//            WXTMaterialApplicationVo curApplicationInfo = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationHead.getApplicationId());
//            // 如果这个订单是不能再提交的状态，就返回失败
//            if((!ApplicationState.DRAFT.getValue().equals(curApplicationInfo.getApplicationStatus()))
//                    && (!ApplicationState.REJECTED.getValue().equals(curApplicationInfo.getApplicationStatus()))){
//                log.warn(applicationHead.getApplicationType() + "这个订单的状态不能再提交" + curApplicationInfo.getApplicationCode());
//                resultMap.put(Constants.RESULT_SUCCESS, false);
//                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "这个订单的状态不能再提交");
//                return resultMap;
//            }
//            // 如果是draft的，先检查目前这类积分是否可以使用
//            if(ApplicationState.DRAFT.getValue().equals(curApplicationInfo.getApplicationStatus())){
//                refreshAppLinesPrice(applicationLines);
//                if(checkNotAllowOrderByAppType(applicationHead.getApplicationType())){
//                    log.warn(applicationHead.getApplicationType() + "积分目前不允许下单");
//                    resultMap.put(Constants.RESULT_SUCCESS, false);
//                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, ApplicationUtil.getPointTypeByAppType(applicationHead.getApplicationType()).getText() + "兑换目前不能下订单,或者联系您的雪佛龙销售负责人");
//                    return resultMap;
//                }
//            }
//            // 增加在进入update的时候就判断这个用户的这个applicationType的积分是否被冻结
//            if (isPointOrder(applicationHead.getApplicationType())) {
//                String errorStr = checkIsFreezeType(applicationHead.getApplicationOrgId(), applicationHead.getUserId(), applicationHead.getApplicationType(), applicationHead.getSalesChannel());
//                if (errorStr != null) {
//                    log.warn(errorStr);
//                    resultMap.put(Constants.RESULT_SUCCESS, false);
//                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, errorStr);
//                    return resultMap;
//                }
//            }
//            // 检查该SP该月是否申请第二次
//            boolean islegal = true;
//            if (!isPointOrder(applicationHead.getApplicationType())) {
//                islegal = checkSPApplicationHistory(applicationHead.getApplicationType());
//            }
//            if (!islegal) {
//                log.warn(ALREADY_MAX_TIMES);
//                resultMap.put(Constants.RESULT_SUCCESS, false);
//                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, ALREADY_MAX_TIMES);
//                return resultMap;
//            }
//            // 检查库存是否足够
//            String stockMsg = checkVirtualStock(applicationLines, false);
//            boolean isSufficient = StringUtils.isEmpty(stockMsg);
//            if (!isSufficient) {
//                log.warn("库存校验失败, 无法提交申请: {}", stockMsg);
//                resultMap.put(Constants.RESULT_SUCCESS, false);
//                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, stockMsg);
//                return resultMap;
//            }
//
//            //检测是否超过上限
//            String _stockMsg = checkLimitQty(applicationLines, applicationHead.getApplicationType(), applicationHead.getApplicationOrgId(), applicationHead.getUserId(), false);
//            isSufficient = StringUtils.isEmpty(_stockMsg);
//            if (!isSufficient) {
//                log.warn("申请上限校验失败, 无法提交申请: {}", _stockMsg);
//                resultMap.put(Constants.RESULT_SUCCESS, false);
//                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, _stockMsg);
//                return resultMap;
//            }
//            // 检测积分是否足够，使用applicationLines（draft是最新的，PENDING_4_URGENT的是当时的）
//            boolean isEnoughPoint = checkPointsMeetTotalPrice(applicationHead,applicationLines);
//            if (!isEnoughPoint) {
//                resultMap.put(Constants.RESULT_SUCCESS, false);
//                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "您目前的积分余额不足，下单失败。");
//                return resultMap;
//            }
//
//            // 更新申请单头信息
//            WXTMaterialApplicationVo record = prepareHeadVo(applicationHead, true);
//            res4Head = wxtMaterialApplicationVoMapper.updateByPrimaryKeySelective(record);
//            Long applicationId = record.getId();
//
//            // 删除旧的申请单行
//            WXTMaterialApplicationDetailVoExample example = new WXTMaterialApplicationDetailVoExample();
//            example.createCriteria().andApplicationIdEqualTo(applicationId);
//            wxtMaterialApplicationDetailVoMapper.deleteByExample(example);
//
//            // 插入新的的申请单行，现在都能保证是最后需要扣减的价格了（draft是最新的，PENDING_4_URGENT的是当时的）
//            List<WXTMaterialApplicationDetailVo> lines = prepareLineVo(applicationLines, applicationId);
//            res4Line = wxtMaterialApplicationDetailVoMapper.insertBatch(lines);
//
//            resultMap.put(Constants.RESULT_SUCCESS, true);
//            resultMap.put("applicationId", applicationId);
//
//            if (applicationHead.getPush2Approval() && res4Line > 0) {
//                // 扣除虚拟库存
//                updateVirtualStock(record.getApplicationCode(), lines);
//
//                // 普通非加急积分订单的场合，不需要审批，直接生成出库单
//                if (isPointOrder(record.getApplicationType()) && !record.getIsUrgent()
//                        && ApplicationState.PENDING_ON_OUTBOUND.getValue().equals(record.getApplicationStatus())) {
//                    PointType pointType = ApplicationUtil.getPointTypeByAppType(record.getApplicationType());
//                    // 这里就是draft变成PENDING_ON_OUTBOUND扣除积分，这里也应该使用application detail的内容来计算totalPrice
//                    Double totalPrice = applicationBizService.calcTotalPriceByAppDetail(record.getId());
//                    Map<String, Object> pointServiceRes = pointService.consumePoints(record.getApplicationOrgId(),applicationHead.getUserId(), totalPrice,
//                            record.getApplicationCode(),record.getApplicationOrgName(), pointType.getValue(), applicationHead.getBizType(),new HashMap<String, Object>());
//                    if (!Constants.SUCCESS_CODE.equals(pointServiceRes.get(Constants.RESULT_CODE_KEY))) {
//                        throw new Exception("积分账户不足");
//                    }
//                    pushWarehouseOutInfo(applicationId);
//
//                    // 扣除真实库存
//                    updateRealStock(record.getApplicationCode(), lines);
//                } else {
//                    // 发送邮件到审批者
//                    sendReadyEmail(applicationId, record.getLastUpdateTime().getTime());
//                }
//            }
//
//            // 申请单状态变化历史记录
//            String status = "";
//            String step = "";
//            if (applicationHead.getPush2Approval()) {
//                status = ApplicationState.READY.getValue();
//                step = "提交审批";
//                if(applicationHead.getApplicationType().equals(ApplicationUtil.APPLICATION_TYPE_CDM_STORE_OPEN)) {
//                	wxtMaterialAddressConfigVoMapper.updateWorkshopFlag(record.getId(),applicationHead.getWorkshopId(),2);
//                }
//            } else {
//                status = ApplicationState.DRAFT.getValue();
//                step = "修改";
//                if(applicationHead.getApplicationType().equals(ApplicationUtil.APPLICATION_TYPE_CDM_STORE_OPEN)) {
//                	wxtMaterialAddressConfigVoMapper.updateWorkshopFlag(record.getId(),applicationHead.getWorkshopId(),1);
//                }
//            }
//            applicationBizService.pushApplicationHistory(applicationId, step, ApplicationState.DRAFT.getValue(), status, "");
//
//        } catch (Exception e) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            log.error("ApplicationServiceImpl.update() exception", e);
//            resultMap.put(Constants.RESULT_SUCCESS, false);
//            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单更新失败," + e.getMessage());
//            resultMap.put(Constants.RESULT_ERROR, "程序异常，回滚变更:" + e.getMessage());
//            return resultMap;
//        }
//
//        log.debug("End ApplicationServiceImpl.create()");
//        if (res4Head != 1) {
//            resultMap.put(Constants.RESULT_SUCCESS, false);
//            String error = "插入物料申请表" + res4Head + "条失败";
//            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单更新失败," + error);
//            resultMap.put(Constants.RESULT_ERROR, error);
//            return resultMap;
//        }
//
//        if (res4Line != applicationLines.length) {
//            resultMap.put(Constants.RESULT_SUCCESS, true);
//            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单更新成功");
//            resultMap.put(Constants.RESULT_ERROR, "插入物料申请详情表" + res4Line + "条成功, 总条数" + applicationLines.length);
//            log.error("插入物料申请详情表" + res4Line + "条成功, 总条数" + applicationLines.length);
//            return resultMap;
//        }
//        return resultMap;
    }

    /**
     * 组装订单行vo对象
     * 这些vo是插入的wx_t_material_application_detail表的
     *
     * @param applicationLines
     * @param applicationId
     * @return WXTMaterialApplicationDetailVo List
     */
    private List<WXTMaterialApplicationDetailVo> prepareLineVo(ApplicationLine[] applicationLines, Long applicationId) {
        List<WXTMaterialApplicationDetailVo> records = new ArrayList<WXTMaterialApplicationDetailVo>();
        WxTUser curUser = ContextUtil.getCurUser();
        Date currentDate = DateUtil.getCurrentDate();
        for (ApplicationLine line : applicationLines) {
            WXTMaterialApplicationDetailVo vo = new WXTMaterialApplicationDetailVo();
            vo.setApplicationId(applicationId);
            vo.setMaterialSkuCode(line.getMaterialSkuCode());
            vo.setWarehouseId(line.getWarehouseId());
            vo.setMaterialPrice(new BigDecimal(line.getMaterialPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
            if (null != line.getMaterialPrice()) {
                vo.setMaterialPrice(new BigDecimal(line.getMaterialPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            if (line.getApplicationQty() < 1) {
                continue;
            }
            if(StringUtils.isNotBlank(line.getVirtualGiftCode())){
                vo.setVirtualGift(true);
            }
            vo.setApplicationQty(line.getApplicationQty());
            vo.setMaterialId(line.getMaterialId());
            vo.setCreatedBy(curUser.getUserId());
            vo.setLastUpdatedBy(curUser.getUserId());
            vo.setCreationTime(currentDate);
            vo.setLastUpdateTime(currentDate);
            vo.setDeleteFlag(false);
            vo.setAttribute1(String.valueOf(line.getVirtualStockQty()));
            vo.setComments(line.getComments());
            records.add(vo);
        }

        return records;
    }

    /**
     * 组装订单头vo对象
     *
     * @param applicationHead
     * @param doUpdate
     * @return WXTMaterialApplicationVo
     */
    private WXTMaterialApplicationVo prepareHeadVo(ApplicationHead applicationHead, boolean doUpdate) {
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setShipToWorkshop(applicationHead.getShip2Workshop());
        // 设定默认物流为 航瑞汽运
        record.setPostCompany("4");

        if (applicationHead.getShip2Workshop()) {
            record.setApplicationName(applicationHead.getApplicationName4Workshop());
            record.setWorkshopId(applicationHead.getWorkshopId());
            record.setWorkshopName(applicationHead.getWorkshopName());
            record.setAddressRegion(String.valueOf(applicationHead.getWorkshopRegionId()));
            record.setAddressDetail(applicationHead.getWorkshopAddress());
            record.setContacts(applicationHead.getWorkshopContacts());
            record.setContactNumber(applicationHead.getWorkshopContactNumber());
            if (null == applicationHead.getWorkshopRecipientDate() || new Date().after(applicationHead.getWorkshopRecipientDate())) {
                applicationHead.setWorkshopRecipientDate(DateUtil.addDays(new Date(), 7));
            }
            record.setRecipientDate(applicationHead.getWorkshopRecipientDate());
            record.setComments(applicationHead.getWorkshopComments());
        } else if(applicationHead.getApplicationType().equals(ApplicationUtil.APPLICATION_TYPE_CDM_STORE_OPEN)){
        	record.setApplicationName(applicationHead.getApplicationName());
            record.setAddressRegion(applicationHead.getDistCode());
            record.setAddressDetail(applicationHead.getAddress());
            record.setWorkshopId(applicationHead.getWorkshopId());
            record.setWorkshopName(applicationHead.getWorkshopName());
            record.setContacts(applicationHead.getContacts());
            record.setContactNumber(applicationHead.getNumber());
            // recipientDate——期望到达日期，如果在今天之前或者为空，则设置为7天之后
            if (null == applicationHead.getRecipientDate() || new Date().after(applicationHead.getRecipientDate())) {
                applicationHead.setRecipientDate(DateUtil.addDays(new Date(), 7));
            }
            record.setRecipientDate(applicationHead.getRecipientDate());
            record.setComments(applicationHead.getComments());
            record.setIsUrgent(applicationHead.getIsUrgent());
            record.setApplicationType(applicationHead.getApplicationType());
        }else {
            record.setApplicationName(applicationHead.getApplicationName());
            record.setWorkshopId(applicationHead.getWorkshopId());
            record.setWorkshopName(applicationHead.getWorkshopName());
            record.setAddressRegion(applicationHead.getDistCode());
            record.setAddressDetail(applicationHead.getAddress());
            record.setContacts(applicationHead.getContacts());
            record.setContactNumber(applicationHead.getNumber());
            // recipientDate——期望到达日期，如果在今天之前或者为空，则设置为7天之后
            if (null == applicationHead.getRecipientDate() || new Date().after(applicationHead.getRecipientDate())) {
                applicationHead.setRecipientDate(DateUtil.addDays(new Date(), 7));
            }
            record.setRecipientDate(applicationHead.getRecipientDate());
            record.setComments(applicationHead.getComments());
            record.setIsUrgent(applicationHead.getIsUrgent());
            record.setApplicationType(applicationHead.getApplicationType());
        }
        // 如果不是更新的，是新增加的则是需要获取下一个applicationCode
        if (!doUpdate) {
            record.setApplicationCode(
                    "MA" + wxTPropertiesServiceImpl.getSequenceByType(SequenceTypes.MATERIAL_APPLICATION_CODE, 6, 1));
        }

        Date now = DateUtil.getCurrentDate();
        if (applicationHead.getPush2Approval()) {
            if (isPointOrder(applicationHead.getApplicationType())) {
                if (applicationHead.getIsUrgent()) {
                    record.setApplicationStatus(ApplicationState.PENDING_4_URGENT.getValue());
                } else {
                    record.setApplicationStatus(ApplicationState.PENDING_ON_OUTBOUND.getValue());
                }
            } else {
                record.setApplicationStatus(ApplicationState.READY.getValue());
            }
        } else {
            // 不是Push2Approval的都是draft状态
            record.setApplicationStatus(ApplicationState.DRAFT.getValue());
        }

        WxTUser curUser = ContextUtil.getCurUser();

        if(ApplicationUtil.isB2BApplicationType(applicationHead.getApplicationType())){
            record.setApplicationUserId(applicationHead.getUserId());
            //如果是b2b的虚拟订单，加入门店名称
            if(Boolean.TRUE.equals(applicationHead.getVirtualGiftFlag())){
                WorkshopEmployee workshopEmployee = workshopEmployeeMapper.selectByCode(applicationHead.getUserId());
                if(workshopEmployee != null){
                    record.setWorkshopId(workshopEmployee.getWorkshopId());
                    record.setWorkshopName(workshopEmployee.getWorkshopName());
                }
            }
        } else {
            if (isPartnerUser(curUser)) {
                // 若是合伙人用户，其对应的组织则指定为申请单所属组织
                record.setApplicationOrgId(curUser.getOrgId());
            } else {
                if (applicationHead.getShip2Workshop()) {
                    // 指定门店的场合, 获取门店对应的合伙人ID
                    record.setApplicationOrgId(applicationHead.getApplicationOrgId4Workshop());
                } else {
                    // 指定特定址的场合, 获取特定的合伙人ID
                    record.setApplicationOrgId(applicationHead.getApplicationOrgId());
                }
            }
        }

        if (!doUpdate) {
            record.setCreatedBy(curUser.getUserId());
            record.setCreationTime(now);
            record.setDeleteFlag(false);
        } else {
            record.setId(applicationHead.getApplicationId());
        }
        // 如果是promotion的，并且bizType还是CALTEX_POINT_FROM_PROMOTE，就记录到订单的BusinessTypeCode中
        if ("promotion".equals(applicationHead.getApplicationType())
                && BusinessType.CALTEX_POINT_FROM_PROMOTE.name().equals(applicationHead.getBizType())) {
            record.setBusinessTypeCode(applicationHead.getBizType());
        } else if(ApplicationUtil.isB2BApplicationType(applicationHead.getApplicationType())){
            // 如果是b2b的则，把BusinessTypeCode设置成salesChannel
            record.setBusinessTypeCode(applicationHead.getBizType());
        }
        record.setApplicationPersonId(curUser.getUserId());
        record.setLastUpdatedBy(curUser.getUserId());
        record.setApplicationTime(now);
        record.setLastUpdateTime(now);
        return record;
    }

    /**
     * 组装促销礼品兑换订单头vo对象
     */
    private WXTMaterialApplicationVo preparePromotionGiftVo(ApplicationHead applicationHead) {
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setShipToWorkshop(applicationHead.getShip2Workshop());
        // 设定默认物流为 航瑞汽运
        record.setPostCompany("4");
        record.setApplicationName(applicationHead.getApplicationName());
        record.setAddressRegion(applicationHead.getDistCode());
        record.setAddressDetail(applicationHead.getAddress());
        record.setContacts(applicationHead.getContacts());
        record.setContactNumber(applicationHead.getNumber());
        // recipientDate——期望到达日期，如果在今天之前或者为空，则设置为7天之后
        if (null == applicationHead.getRecipientDate() || new Date().after(applicationHead.getRecipientDate())) {
            applicationHead.setRecipientDate(DateUtil.addDays(new Date(), 7));
        }
        record.setRecipientDate(applicationHead.getRecipientDate());
        record.setComments(applicationHead.getComments());
        record.setApplicationType(applicationHead.getApplicationType());  //设置申请类型，这里是积分大类
        record.setApplicationCode("MA" + wxTPropertiesServiceImpl.getSequenceByType(SequenceTypes.MATERIAL_APPLICATION_CODE, 6, 1));
        record.setApplicationStatus(ApplicationState.PENDING_ON_OUTBOUND.getValue());
        Date now = DateUtil.getCurrentDate();
        WxTUser curUser = ContextUtil.getCurUser();
        record.setCreatedBy(curUser.getUserId());
        record.setCreationTime(now);
        record.setDeleteFlag(false);
        if (isPartnerUser(curUser)) {
            // 若是合伙人用户，其对应的组织则指定为申请单所属组织
            record.setApplicationOrgId(curUser.getOrgId());
        }
        record.setApplicationPersonId(curUser.getUserId());
        record.setLastUpdatedBy(curUser.getUserId());
        record.setApplicationTime(now);
        record.setLastUpdateTime(now);
        return record;
    }

    /**
     * 组装订单行vo对象
     * 这些vo是插入的wx_t_material_application_detail表的
     *
     * @param applicationLines
     * @param applicationId
     * @return WXTMaterialApplicationDetailVo List
     */
    private List<WXTMaterialApplicationDetailVo> preparePromotionGiftLineVo(ApplicationLine[] applicationLines, Long applicationId) {
        List<WXTMaterialApplicationDetailVo> records = new ArrayList<WXTMaterialApplicationDetailVo>();
        WxTUser curUser = ContextUtil.getCurUser();
        Date currentDate = DateUtil.getCurrentDate();
        for (ApplicationLine line : applicationLines) {
            WXTMaterialApplicationDetailVo vo = new WXTMaterialApplicationDetailVo();
            vo.setApplicationId(applicationId);
            vo.setMaterialSkuCode(line.getMaterialSkuCode());
            vo.setWarehouseId(line.getWarehouseId());
            vo.setMaterialPrice(BigDecimal.ZERO);
            if (line.getApplicationQty() < 1) {
                continue;
            }
            vo.setApplicationQty(line.getApplicationQty());
            vo.setMaterialId(line.getMaterialId());
            vo.setCreatedBy(curUser.getUserId());
            vo.setLastUpdatedBy(curUser.getUserId());
            vo.setCreationTime(currentDate);
            vo.setLastUpdateTime(currentDate);
            vo.setDeleteFlag(false);
            vo.setAttribute1(String.valueOf(line.getVirtualStockQty()));
            vo.setComments(line.getComments());
            records.add(vo);
        }
        return records;
    }

    /**
     * 获取地区名
     *
     * @param distCode
     * @return Region Name
     */
    private String getRegionName(Long distCode) {
        if (null == distCode) {
            return null;
        }

        List<RegionVo> provCityDist = regionService.getProvCityDistByRegionId(distCode);
        String regionName = "";
        String name = "";
        for (RegionVo region : provCityDist) {
            if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_PROVINCE)) {
                name = region.getRegionName();
                regionName = name;
                log.debug("getProvinceName: " + region.getRegionName());
                break;
            }
        }
        for (RegionVo region : provCityDist) {
            if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_CITY)) {
                name = region.getRegionName();
                regionName = regionName + "-" + name;
                log.debug("getCityName : " + region.getRegionName());
                break;
            }
        }
        for (RegionVo region : provCityDist) {
            if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_DISTRICT)) {
                name = region.getRegionName();
                regionName = regionName + "-" + name;
                log.debug("getRegionName : " + region.getRegionName());
                break;
            }
        }
        return regionName;
    }

    /**
     * 获取省码
     *
     * @param distCode
     * @return province Code
     */
    private String getProvinceCode(Long distCode) {
        List<RegionVo> provCityDist = regionService.getProvCityDistByRegionId(distCode);
        String name = null;
        for (RegionVo region : provCityDist) {
            if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_PROVINCE)) {
                name = region.getRegionCode();
                break;
            }
        }
        return name;
    }

    /**
     * 获取省份名称
     *
     * @param distCode
     * @return province Code
     */
    private String getProvinceName(Long distCode) {
        List<RegionVo> provCityDist = regionService.getProvCityDistByRegionId(distCode);
        String provinceCode = null;
        for (RegionVo region : provCityDist) {
            if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_PROVINCE)) {
                provinceCode = region.getRegionName();
                break;
            }
        }
        return provinceCode;
    }

    /**
     * 获取城市码
     *
     * @param distCode
     * @return city code
     */
    private String getCityCode(Long distCode) {
        List<RegionVo> provCityDist = regionService.getProvCityDistByRegionId(distCode);
        String cityCode = null;
        for (RegionVo region : provCityDist) {
            if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_CITY)) {
                cityCode = region.getRegionCode();
                break;
            }
        }
        return cityCode;
    }

    /**
     * 是否有审批权限
     *
     * @param curUser       系统用户对象
     * @param approvalLevel 审批级别, 1=一级
     * @return
     */
    private boolean hasApprovalPermission(WxTUser curUser, Long approvalLevel) {
        return true;
    }

    /**
     * 是否为合伙人用户
     *
     * @param user 系统用户对象
     * @return true/false
     */
    private boolean isPartnerUser(WxTUser user) {
        return user != null && WxTUser.USER_MODEL_SP.equals(user.getUserModel());
    }

    private void sendApprovedEmail(Long applicationId) {
        WXTMaterialApplicationVo applicationHead = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        String addressRegion = applicationHead.getAddressRegion();
        if (!StringUtils.isEmpty(addressRegion)) {
            String addressRegionName = getRegionName(Long.valueOf(addressRegion));
            applicationHead.setAddressRegionName(addressRegionName);
        }
        List<WXTMaterialApplicationDetailVo> applicationlines = wxtMaterialApplicationDetailVoMapper
                .selectWithMaterialInfo(applicationId);
        if (null != applicationlines && !applicationlines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : applicationlines) {
                String[] split = line.getMaterialSkuCode().split("-");
                line.setMaterialSkuCode(split[1] + ',' + split[2]);
            }
        }
        String[] acceptEmail = new String[1];
        String appPersonEmail = getEmailByUserId(applicationHead.getApplicationPersonId());
        acceptEmail[0] = appPersonEmail;
        String subjectName = "物料申请单[" + applicationHead.getApplicationCode() + "]审批通过";
        Map<String, Object> contentMap = new HashMap<String, Object>();
        String acceptPersonName = "雪佛龙物料申请人";
        String partnerName = "您的物料申请[" + applicationHead.getApplicationCode() + "]已审批成功, 请登录雪佛龙合伙人平台系统查看更多详情: ";
        contentMap.put("acceptPersonName", acceptPersonName);
        contentMap.put("partnerName", partnerName);
        contentMap.put("applicationHead", applicationHead);
        contentMap.put("applicationlines", applicationlines);
        emailSenderService.sendEmailForCommon(HttpSessionGets.getSession().getServletContext(), acceptEmail, null,
                subjectName, contentMap, null, "partner_material_application.ftl");

    }

    private void sendRejectedEmail(Long applicationId) {
        WXTMaterialApplicationVo applicationHead = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        String addressRegion = applicationHead.getAddressRegion();
        if (!StringUtils.isEmpty(addressRegion)) {
            String addressRegionName = getRegionName(Long.valueOf(addressRegion));
            applicationHead.setAddressRegionName(addressRegionName);
        }
        List<WXTMaterialApplicationDetailVo> applicationlines = wxtMaterialApplicationDetailVoMapper
                .selectWithMaterialInfo(applicationId);
        if (null != applicationlines && !applicationlines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : applicationlines) {
                String[] split = line.getMaterialSkuCode().split("-");
                line.setMaterialSkuCode(split[1] + ',' + split[2]);
            }
        }
        String[] acceptEmail = new String[1];
        String appPersonEmail = getEmailByUserId(applicationHead.getApplicationPersonId());
        acceptEmail[0] = appPersonEmail;
        String subjectName = "物料申请单[" + applicationHead.getApplicationCode() + "]被驳回";
        Map<String, Object> contentMap = new HashMap<String, Object>();
        String acceptPersonName = "雪佛龙物料申请人";
        String partnerName = "您的物料申请[" + applicationHead.getApplicationCode() + "]已被驳回, 请登录雪佛龙合伙人平台系统查看更多详情: ";
        contentMap.put("acceptPersonName", acceptPersonName);
        contentMap.put("partnerName", partnerName);
        contentMap.put("applicationHead", applicationHead);
        contentMap.put("applicationlines", applicationlines);
        emailSenderService.sendEmailForCommon(HttpSessionGets.getSession().getServletContext(), acceptEmail, null,
                subjectName, contentMap, null, "partner_material_application.ftl");
    }

    private void sendReadyEmail(Long applicationId, Long lastUpdateTime) {
        WXTMaterialApplicationVo applicationHead = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        String addressRegion = applicationHead.getAddressRegion();
        if (!StringUtils.isEmpty(addressRegion)) {
            String addressRegionName = getRegionName(Long.valueOf(addressRegion));
            applicationHead.setAddressRegionName(addressRegionName);
        }
        List<WXTMaterialApplicationDetailVo> applicationlines = wxtMaterialApplicationDetailVoMapper
                .selectWithMaterialInfo(applicationId);
        if (null != applicationlines && !applicationlines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : applicationlines) {
                String[] split = line.getMaterialSkuCode().split("-");
                line.setMaterialSkuCode(split[1] + ',' + split[2]);
            }
        }
        String[] acceptEmail = null, ccEmail = null, userIds = null;
        boolean is4levelOne = true;
        if (ApplicationState.READY.getValue().equals(applicationHead.getApplicationStatus())) {
            is4levelOne = true;
        } else if (ApplicationState.PENDING_ON_L2.getValue().equals(applicationHead.getApplicationStatus())) {
            is4levelOne = false;
        } else {
            return;
        }

        Map<String, String[]> emailList = getEmailList(applicationHead.getApplicationOrgId(), is4levelOne);
        acceptEmail = (String[]) emailList.get("to");
        ccEmail = (String[]) emailList.get("cc");
        userIds = (String[]) emailList.get("id");
        if (null == acceptEmail || acceptEmail.length < 1) {
            log.error("物料申请请求审批邮件发送失败， 系统未配置该审批业务的审批人或者审批者未配置收件邮箱，对应合伙人:" + applicationHead.getApplicationOrgName());
            return;
        }

        String subjectName = null;
        String acceptPersonName = null;
        String partnerName = null;
        if (is4levelOne) {
            subjectName = "合伙人[" + applicationHead.getApplicationOrgName() + "]物料申请单待审核";
            acceptPersonName = "物料申请确认者";
            partnerName = "合伙人" + applicationHead.getApplicationOrgName() + ", 有物料申请需要您审核确认, 通过后最终审核人方可审核, 详情如下：";
        } else {
            subjectName = "合伙人[" + applicationHead.getApplicationOrgName() + "]物料申请单待审核";
            acceptPersonName = "物料申请审批者";
            partnerName = "合伙人" + applicationHead.getApplicationOrgName() + ", 有物料申请需要您最终审核, 通过后会通过礼享网下单发货, 详情如下：";
        }

        Map<String, Object> contentMap = new HashMap<String, Object>();
        contentMap.put("acceptPersonName", acceptPersonName);
        contentMap.put("partnerName", partnerName);
        contentMap.put("applicationHead", applicationHead);
        contentMap.put("applicationlines", applicationlines);


        if (null != RequestContextHolder.getRequestAttributes()) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest();
            String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + "/";
            String baseLink = null;
            String templateLink = "/material/jsp/approval.jsp";
            String approvalLevel = null;
            if (is4levelOne) {
                approvalLevel = "1";
                baseLink = "/material/jsp/approvalDetail.jsp?approvalLevel=1&applicationId=";
            } else {
                approvalLevel = "2";
                templateLink = "/material/jsp/approval.jsp?approvalLevel=2";
                baseLink = "/material/jsp/approvalDetail.jsp?approvalLevel=2&applicationId=";
            }
            if (userIds != null && userIds.length > 0) {
                contentMap.put("approvalAction1Link",
                        basePath + TokenUtil.generateTokenLink(userIds[0], "materialApproveDirectly",
                                templateLink,
                                baseLink + applicationId, true,
                                String.valueOf(applicationId), String.valueOf(lastUpdateTime), String.valueOf(approvalLevel)));
                contentMap.put("approvalAction2Link",
                        basePath + TokenUtil.generateTokenLink(userIds[0], "materialRejectDirectly",
                                templateLink,
                                baseLink + applicationId, true,
                                String.valueOf(applicationId), String.valueOf(lastUpdateTime), String.valueOf(approvalLevel)));
//				contentMap
//				.put("approvalPageLink",
//						basePath + TokenUtil.generateTokenLink(userIds[0], "materialApproval",
//								templateLink,
//								baseLink + applicationId, false));
            }
        }

        emailSenderService.sendEmailForCommon(HttpSessionGets.getSession().getServletContext(), acceptEmail, ccEmail,
                subjectName, contentMap, null, "partner_material_application.ftl");
    }

    private void sendEmail4OperationSuccess(Long applicationId, boolean approveOrdisallow, boolean is4levelOne) {
        WXTMaterialApplicationVo applicationHead = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
        String addressRegion = applicationHead.getAddressRegion();
        if (!StringUtils.isEmpty(addressRegion)) {
            String addressRegionName = getRegionName(Long.valueOf(addressRegion));
            applicationHead.setAddressRegionName(addressRegionName);
        }
        List<WXTMaterialApplicationDetailVo> applicationlines = wxtMaterialApplicationDetailVoMapper
                .selectWithMaterialInfo(applicationId);
        if (null != applicationlines && !applicationlines.isEmpty()) {
            for (WXTMaterialApplicationDetailVo line : applicationlines) {
                String[] split = line.getMaterialSkuCode().split("-");
                line.setMaterialSkuCode(split[1] + ',' + split[2]);
            }
        }
        String[] acceptEmail = null, ccEmail = null;

        Map<String, String[]> emailList = getEmailList(applicationHead.getApplicationOrgId(), is4levelOne);
        acceptEmail = (String[]) emailList.get("to");
        ccEmail = (String[]) emailList.get("cc");

        String subjectName = null;
        String acceptPersonName = null;
        String partnerName = null;
        if (approveOrdisallow) {
            if (is4levelOne) {
                subjectName = "合伙人[" + applicationHead.getApplicationOrgName() + "]物料申请单[" + applicationHead.getApplicationCode() + "]已确认成功";
                acceptPersonName = "物料申请确认者";
                partnerName = "合伙人" + applicationHead.getApplicationOrgName() + ", 物料申请[" + applicationHead.getApplicationCode() + "]已确认, 已发往最终审核人等待审核, 详情如下：";
            } else {
                subjectName = "合伙人[" + applicationHead.getApplicationOrgName() + "]物料申请单[" + applicationHead.getApplicationCode() + "]已最终审批成功";
                acceptPersonName = "物料申请审批者";
                partnerName = "合伙人" + applicationHead.getApplicationOrgName() + ", 物料申请[" + applicationHead.getApplicationCode() + "]已最终审批成功, 已向礼享网下单, 等待发货, 详情如下：";
            }
        } else {
            if (is4levelOne) {
                subjectName = "合伙人[" + applicationHead.getApplicationOrgName() + "]物料申请单[" + applicationHead.getApplicationCode() + "]已驳回成功";
                acceptPersonName = "物料申请确认者";
                partnerName = "合伙人" + applicationHead.getApplicationOrgName() + ", 物料申请[" + applicationHead.getApplicationCode() + "]已驳回成功, 详情如下：";
            } else {
                subjectName = "合伙人[" + applicationHead.getApplicationOrgName() + "]物料申请单[" + applicationHead.getApplicationCode() + "]已驳回成功";
                acceptPersonName = "物料申请审批者";
                partnerName = "合伙人" + applicationHead.getApplicationOrgName() + ", 物料申请[" + applicationHead.getApplicationCode() + "]已驳回成功, 详情如下：";
            }
        }

        Map<String, Object> contentMap = new HashMap<String, Object>();
        contentMap.put("acceptPersonName", acceptPersonName);
        contentMap.put("partnerName", partnerName);
        contentMap.put("applicationHead", applicationHead);
        contentMap.put("applicationlines", applicationlines);

        emailSenderService.sendEmailForCommon(HttpSessionGets.getSession().getServletContext(), acceptEmail, ccEmail,
                subjectName, contentMap, null, "partner_material_application.ftl");
    }

    private Map<String, String[]> getEmailList(Long partnerId, boolean forLevelOne) {
        Map<String, String[]> res = new HashMap<String, String[]>();

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("funFlag", forLevelOne ? "material_approval" : "material_approval_confirm");
        paramMap.put("partnerId", partnerId);
        List<PartnerResponsibleVo> voList = partnerResponsibleVoMapper.queryPartnerResponsibleEmailInfo(paramMap);

        List<String> emailList = new ArrayList<String>();
        List<String> userIdList = new ArrayList<String>();
        List<String> ccList = new ArrayList<String>();
        if (null != voList && !voList.isEmpty()) {
            for (PartnerResponsibleVo vo : voList) {
                userIdList.add(String.valueOf(vo.getResponsiblePersonId()));
                emailList.add(vo.getResponsiblePersonEmail());
                ccList.add(vo.getDayReportCc());
            }
        }
        res.put("to", emailList.toArray(new String[emailList.size()]));
        res.put("cc", ccList.toArray(new String[ccList.size()]));
        res.put("id", userIdList.toArray(new String[userIdList.size()]));
        return res;
    }

    private String getEmailByUserId(Long userId) {
        if (null == userId) {
            return null;
        }

        WxTUserExample example = new WxTUserExample();
        example.createCriteria().andUserIdEqualTo(userId);
        List<WxTUser> wxtUser = wxTUserMapper.selectByExample(example);
        if (null == wxtUser || wxtUser.size() < 1) {
            return null;
        }
        return wxtUser.get(0).getEmail();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Boolean updateApplicationStatus(Long applicationId, String applicationCode, ApplicationState status, String materialCode, String materialSkuCode) throws WxPltException {
        if ((null == applicationId || 0 == applicationId) && StringUtils.isEmpty(applicationCode)) {
            log.error("updateApplicationStatus: 参数申请单ID和申请单编码不能同时为空");
            return false;
        }
        Date now = DateUtil.getCurrentDate();
        Long userId = ContextUtil.getCurUserId();
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setApplicationStatus(status.getValue());
        record.setLastUpdatedBy(userId);
        record.setLastUpdateTime(now);
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        if (null == applicationId) {
            example.createCriteria().andApplicationCodeEqualTo(applicationCode);
        } else {
            example.createCriteria().andIdEqualTo(applicationId);
        }
        int res = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);
        if (1 == res) {
            if (null == applicationId || 0 == applicationId) {
                WXTMaterialApplicationVoExample example2 = new WXTMaterialApplicationVoExample();
                example2.createCriteria().andApplicationCodeEqualTo(applicationCode);
                List<WXTMaterialApplicationVo> selectByExample = wxtMaterialApplicationVoMapper.selectByExample(example2);
                if (null != selectByExample && !selectByExample.isEmpty() && null != selectByExample.get(0)) {
                    applicationId = selectByExample.get(0).getId();
                }
            }

            if (null != applicationId && 0 != applicationId) {
                // 申请单状态变化历史记录
                if (status.equals(ApplicationState.OUTBOUND)) {
                    String materialName = "";
                    String colorNSize = "";
                    String comments = "出库";
                    if (StringUtils.isNotEmpty(materialCode) && StringUtils.isNotEmpty(materialSkuCode)) {
                        WXTMaterialVoExample materialExample = new WXTMaterialVoExample();
                        materialExample.createCriteria().andMaterialCodeEqualTo(materialCode);
                        List<WXTMaterialVo> materialList = materialVoMapper.selectByExample(materialExample);
                        if (null != materialList && materialList.size() == 1) {
                            materialName = materialList.get(0).getMaterialName();
                        }
                        WXTMaterialSkuVoExample materialSkuExample = new WXTMaterialSkuVoExample();
                        materialSkuExample.createCriteria().andMaterialSkuCodeEqualTo(materialSkuCode);
                        List<WXTMaterialSkuVo> materialSkuList = materialSkuVoMapper.selectByExample(materialSkuExample);
                        if (null != materialSkuList && materialSkuList.size() == 1) {
                            colorNSize = materialSkuList.get(0).getMaterialSkuPropColor() + "," + materialSkuList.get(0).getMaterialSkuPropSize();
                        }
                        comments = materialName + "(" + colorNSize + ")" + "出库";
                    }
                    applicationBizService.pushApplicationHistory(applicationId, comments, ApplicationState.APPROVED.getValue(), ApplicationState.OUTBOUND.getValue(), "");
                }
                if (status.equals(ApplicationState.SHIPPING)) {
                	applicationBizService.pushApplicationHistory(applicationId, "送货中", ApplicationState.OUTBOUND.getValue(), ApplicationState.SHIPPING.getValue(), "");
                }

                if (status.equals(ApplicationState.RECEIVED)) {
                	applicationBizService.pushApplicationHistory(applicationId, "已收货", ApplicationState.SHIPPING.getValue(), ApplicationState.RECEIVED.getValue(), "");
                }
            }
        }
        return res == 1;
    }

    @Override
    public Map<String, Object> getApplicationStatusHistory(Long applicationId) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationlStatusHistoryVoExample example = new WXTMaterialApplicationlStatusHistoryVoExample();
        example.createCriteria().andApplicationIdEqualTo(applicationId);
        example.setOrderByClause("CREATION_TIME DESC");

        List<WXTMaterialApplicationlStatusHistoryVo> historyList = applicationStatusHistoryVoMapper.selectByExample(example);

        if (null != historyList && !historyList.isEmpty()) {
            resultMap.put("historyList", historyList);
            resultMap.put(Constants.RESULT_SUCCESS, true);
            resultMap.put("msg", "获取申请单状态历史成功");
        } else {
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "获取申请单状态历史失败");
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> receive(Long applicationId, Date lastUpdateTime) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationVo record = new WXTMaterialApplicationVo();
        record.setLastUpdateTime(DateUtil.getCurrentDate());
        record.setLastUpdatedBy(ContextUtil.getCurUserId());
        record.setApplicationStatus(ApplicationState.RECEIVED.getValue());
        WXTMaterialApplicationVoExample example = new WXTMaterialApplicationVoExample();
        example.createCriteria().andIdEqualTo(applicationId).andLastUpdateTimeEqualTo(lastUpdateTime)
                .andApplicationStatusEqualTo(ApplicationState.SHIPPING.getValue());
        int updateByExample = -1;
        try {
            updateByExample = wxtMaterialApplicationVoMapper.updateByExampleSelective(record, example);
        } catch (Exception e) {
            resultMap.put("errorMsg", "申请单确认收货失败, 后台错误");
            log.error("申请单确认收货失败,后台错误: 申请ID:{}", applicationId);
            resultMap.put("success", false);
            resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
            return resultMap;
        }

        if (updateByExample != 1) {
            resultMap.put("errorMsg", "申请单确认收货失败, 申请单状态未变更");
            log.error("申请单确认收货失败: 申请ID:{}", applicationId);
            resultMap.put("success", false);
            return resultMap;
        }
        // 申请单状态变化历史记录
        applicationBizService.pushApplicationHistory(applicationId, "确认收货", ApplicationState.SHIPPING.getValue(), ApplicationState.RECEIVED.getValue(), "");
        resultMap.put("success", true);
        resultMap.put("successMsg", "确认收货成功");
        log.debug("申请单确认收货成功: 更新结果:{}, 申请ID:{}", updateByExample, applicationId);
        return resultMap;
    }

    @Override
    public Map<String, Object> getApplicationLogistics(String applicationCode) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        WXTMaterialApplicationLogisticsVoExample example = new WXTMaterialApplicationLogisticsVoExample();
        example.createCriteria().andShipmentCodeEqualTo(applicationCode);
        example.setOrderByClause("CREATION_TIME DESC");

        List<WXTMaterialApplicationLogisticsVo> logisticsList = wxtMaterialApplicationLogisticsVoMapper.selectByExample(example);

        if (null != logisticsList) {
            resultMap.put("logisticsList", logisticsList);
            resultMap.put(Constants.RESULT_SUCCESS, true);
            resultMap.put("msg", "获取申请单物流信息成功");
        } else {
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "获取申请单物流信息失败");
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> queryPendingGiftTotal(Long partnerOrgId, String pointType) {
        Map<String, Object> map = new HashMap<String, Object>(5);
        Long giftTotal = 0L;
        try {
            if (null != partnerOrgId) {
                giftTotal = wxtMaterialApplicationDetailVoMapper.countShoppingCartQty(partnerOrgId, pointType);
            }
            map.put("code", "success");
            map.put("msg", "暂存订单礼品数获取成功");
            map.put("giftTotal", giftTotal);
        } catch (Exception e) {
            map.put("code", "error");
            map.put("msg", "暂存订单礼品数获取失败" + MessageResourceUtil.getMessage("system.unexpected_exception"));
        }
        return map;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> addLogisticsInfo(Long warehouseOutId, WXTMaterialApplicationLogisticsVo vo) {
        try {
            Map<String, Object> resultMap = new HashMap<String, Object>();
            Date now = DateUtil.getCurrentDate();
            Long userId = ContextUtil.getCurUserId();
            int res = 0;
            WXTMaterialApplicationLogisticsVo record = new WXTMaterialApplicationLogisticsVo();
            record.setShipmentCode(vo.getShipmentCode());
            record.setSmcOrderCode(vo.getSmcOrderCode());
            record.setExpressCompany(vo.getExpressCompany());
            record.setExpressNumber(vo.getExpressNumber());
            record.setExpressContext(vo.getExpressContext());
            record.setTrackingTime(now);
            record.setDeleteFlag(false);
            record.setLastUpdatedBy(userId);
            record.setLastUpdateTime(now);
            record.setCreatedBy(userId);
            record.setCreationTime(now);
            res = wxtMaterialApplicationLogisticsVoMapper.insertSelective(record);
            WXTMaterialApplicationLogisticsVoExample _example = new WXTMaterialApplicationLogisticsVoExample();
            _example.createCriteria().andShipmentCodeEqualTo(vo.getShipmentCode()).andSmcOrderCodeEqualTo(vo.getSmcOrderCode());
            List<WXTMaterialApplicationLogisticsVo> existedLog = wxtMaterialApplicationLogisticsVoMapper.selectByExample(_example);
            if (res > 0 && existedLog.size() == 1) {
                updateApplicationStatus(null, vo.getShipmentCode(), ApplicationState.SHIPPING, null, null);
                WXTMaterialWarehouseOutVoExample example = new WXTMaterialWarehouseOutVoExample();
                example.createCriteria().andIdEqualTo(warehouseOutId);
                WXTMaterialWarehouseOutVo warehouseOut = new WXTMaterialWarehouseOutVo();
                warehouseOut.setStatus(ApplicationState.OUTBOUND.getValue());
                materialWarehouseOutVoMapper.updateByExampleSelective(warehouseOut, example);
            }
            resultMap.put("success", true);
            resultMap.put("successMsg", "物流信息添加成功");
            log.debug("物流信息添加成功, 申请单:{}", vo.getShipmentCode());
            return resultMap;
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            Map<String, Object> resultMap = new HashMap<String, Object>();
            resultMap.put("success", false);
            resultMap.put("errorMsg", "物流信息添加失败");
            log.error("物流信息添加失败, 申请单:{}", vo.getShipmentCode());
            e.printStackTrace();
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> getDeclaration() throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(Constants.RESULT_SUCCESS, true);
        resultMap.put("text", wxTPropertiesServiceImpl.getPropertiesCodeByCodeType("point_exchange_declaration"));
        return resultMap;
    }

    @Override
    public Map<String, Object> getShippingAppList(Long partnerId) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(Constants.RESULT_SUCCESS, true);
        List<WXTMaterialApplicationVo> list = wxtMaterialApplicationVoMapper.selectShippingAppList(partnerId);
        resultMap.put("list", list);
        return resultMap;
    }

    @Override
    public Map<String, Object> getShippingAppListByPartnerIdAndApplicationType(
            Long partnerId, String applicationType) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(Constants.RESULT_SUCCESS, true);

        WXTMaterialApplicationVoExample ae = new WXTMaterialApplicationVoExample();
        WXTMaterialApplicationVoExample.Criteria applicationCriteria = ae.createCriteria();
        applicationCriteria.andApplicationOrgIdEqualTo(partnerId);
        applicationCriteria.andApplicationTypeEqualTo(applicationType);
        applicationCriteria.andApplicationStatusNotEqualTo(ApplicationState.RETURNED.getValue());
        applicationCriteria.andDeleteFlagNotEqualTo(true);
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationType);
        if(pointType != null && pointType.isPersonalAccount()) {
        	applicationCriteria.andApplicationUserIdEqualTo(ContextUtil.getCurUserId().toString());
        }
        List<WXTMaterialApplicationVo> list = wxtMaterialApplicationVoMapper.selectByExample(ae);
        resultMap.put("list", list);
        return resultMap;
    }

    /**
     * 根据applicationType 和 bizType 获取 这个 partnerId对应的积分
     * 对于promotion的积分，会根据bizType来区分（地促积分或者促销积分）
     * material/js/applicationCreate.js   rpc call有调用的
     * @param partnerId
     * @param applicationType
     * @param bizType
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> getPointByByPartnerIdAndApplicationType(
            Long partnerId, String userId, String applicationType, String bizType) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationType);
        //如果是查询B2B积分那么单独处理
        if(pointType != null && pointType.equals(PointType.B2B_POINT)){
            JsonResponse pointSummary = b2BPointService.getPointSummary(userId, bizType);
            resultMap.put("success", true);
            Object object = pointSummary.get(JsonResponse.KEY_DATA);
            if(object != null){
                B2BPointSummary point = (B2BPointSummary) object;
                resultMap.put("totalPoint", point.getTotalPoint() == null ? 0 : point.getTotalPoint());
            }
            return resultMap;
        }
        WXTPointAccountVo accountVo = null;
        if(pointType != null){
            accountVo = pointType.getPointAccount(ContextUtil.getCurUser());
        }
        BigDecimal totalPoint = null;
        if(accountVo != null) {
            if(accountVo.getId() != null) {
                totalPoint = pointAccountBizService.queryPointTotalByType(accountVo.getId(), new String[] {pointType.getValue()}, null);
            }
        }else {
            totalPoint = getPointsByAppTypeBizType(partnerId, userId, applicationType, bizType);
        }
        resultMap.put("success", true);
        resultMap.put("totalPoint", totalPoint == null ? 0 : totalPoint);
        return resultMap;
    }

    /**
     * 获取总的剩余的积分，根据partnerId，applicationType，bizType，salesChannel
     * @param partnerId
     * @param applicationType
     * @param bizType
     * @return
     */
    private BigDecimal getPointsByAppTypeBizType(Long partnerId, String userId, String applicationType, String bizType) {
        BigDecimal totalPoint = BigDecimal.valueOf(0);
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationType);
        String type = applicationType;
        if(pointType != null){
            type = pointType.getValue();
        }
        if("null".equals(bizType)){
            bizType = null;
        }
        Map<String, Object> querySpecialPointTotal = pointAccountService.querySpecialPointTotal(partnerId, userId, new String[]{type}, bizType);
        if (null != querySpecialPointTotal && querySpecialPointTotal.containsKey("totalPoint")) {
            totalPoint = (BigDecimal) querySpecialPointTotal.get("totalPoint");
            if (totalPoint == null) {
                totalPoint = BigDecimal.valueOf(0);
            }
        }
        return totalPoint;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> sendBack(Long applicationId) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            // 查询这个applicationId目前对应application的数据
            WXTMaterialApplicationVo curAppRecord = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
			applicationBizService.sendBackOrder(curAppRecord);
            resultMap.put("success", true);
            resultMap.put("successMsg", "打回订单成功");
		} catch (Exception e) {
            resultMap.put("errorMsg", "打回订单失败, 申请单状态未变更");
            log.error("申请单确认收货失败: 申请ID:{}", applicationId, e);
            resultMap.put("success", false);
		}

        return resultMap;
    }

    /**
     * 更新applicationLines里面的price字段
     * 因为如果是 暂存的订单最后确认的时候——如果暂存的时候订单里面商品的价格已经变化了，那现在付钱应该使用最新的商品的价格
     *    需要审批（加急）的订单在最后被确认的时候——应该是当时的价格（）
     * @param applicationLines
     * @return
     */
    private void refreshAppLinesPrice(ApplicationLine[] applicationLines) throws WxPltException {
    	List<Long> materialIds = new ArrayList<Long>(applicationLines.length);
    	for(ApplicationLine line : applicationLines) {
    		materialIds.add(line.getMaterialId());
    	}
    	PointMaterialExample example = new PointMaterialExample();
    	example.createCriteria().andDeleteFlagEqualTo(0l).andIdIn(materialIds);
    	List<PointMaterial> list = pointMaterialBizService.queryByExample(example);
    	Map<Long, PointMaterial> giftMap = new HashMap<Long, PointMaterial>(list.size());
    	for(PointMaterial pointMaterial : list) {
    		giftMap.put(pointMaterial.getId(), pointMaterial);
    	}
    	for(ApplicationLine line : applicationLines) {
    		PointMaterial pointMaterial = giftMap.get(line.getMaterialId());
    		if(pointMaterial == null) {
    			throw new WxPltException("未查询到物料" + line.getMaterialSkuCode() + "价格");
    		}
    		line.setMaterialPrice(pointMaterial.getPointValue());
    	}
    }

    /**
     * 是否是一件被冷冻的状态
     *
     * @param dealerId
     * @param applicationType
     * @param salesChannel
     * @return
     */
    public String checkIsFreezeType(Long dealerId,String userId, String applicationType, String salesChannel) {
        boolean isFreezeType = false;
        String errorStr = null;
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationType);
        // 查询dealerId对应的account账户的FROZEN_TYPE字段情况
        // 对于把b2b的积分也是可以使用这个的
        List<WXTPointAccountVo> pointAccountVoList = getWxtPointAccountVos(dealerId,userId);
        if (pointAccountVoList != null && !pointAccountVoList.isEmpty()) {
            WXTPointAccountVo pointAccountVo = pointAccountVoList.get(0);
            String curFrozenTypeStr = pointAccountVo.getFrozenType();
            if(!StringUtils.isEmpty(curFrozenTypeStr)){
                // 如果是b2b的积分的订单，那就判断salesChannel是否在curFrozenTypeStr
                if(ApplicationUtil.isB2BApplicationType(applicationType) && curFrozenTypeStr.contains(salesChannel)) {
                    isFreezeType = true;
                } else if (curFrozenTypeStr.contains(applicationType)) {
                    isFreezeType = true;
                }
            }
        }
        if (isFreezeType) {
            errorStr = String.format(POINT_IS_FROZEN, pointType!=null?pointType.getText():applicationType);
            log.warn(errorStr, dealerId, applicationType);
        }
        return errorStr;
    }

    /**
     * @param dealerId        零售商的org_id
     * @param applicationType application的Type
     * @param freeze          true——冻结，false——解冻
     * @return 是否设置成功
     * @throws Exception 设置出现了异常
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> updatePointState(Long dealerId, String applicationType, boolean freeze) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        // 转换applicationType到pointType
        String curPointType = applicationBizService.getPointTypeName(applicationType);
        if (curPointType == null) {
            String error = "设置积分的状态失败，applicationType为空";
            resultMap.put("errorMsg", error);
            resultMap.put("success", false);
            log.error("error:{} {} {}", error, dealerId, applicationType, freeze);
            return resultMap;
        }

        // 查询dealerId对应的account账户的FROZEN_TYPE字段情况
        // b2b的积分应该也是需要这样来冻结的
        List<WXTPointAccountVo> pointAccountVoList = getWxtPointAccountVos(dealerId, null);
        if (pointAccountVoList != null && !pointAccountVoList.isEmpty()) {
            WXTPointAccountVo pointAccountVo = pointAccountVoList.get(0);
            processUpdatePointState(applicationType, freeze, pointAccountVo);

            resultMap.put("msg", "更新积分状态成功");
            resultMap.put("success", true);
        } else {
            PartnerO2OEnterprise partner = null;
            PartnerO2OEnterpriseExample partnerExample = new PartnerO2OEnterpriseExample();
            partnerExample.createCriteria().andPartnerIdEqualTo(dealerId);
            List<PartnerO2OEnterprise> partnerList = partnerO2OEnterpriseMapper.selectByExample(partnerExample);

            if (partnerList != null && !partnerList.isEmpty()) {
                partner = partnerList.get(0);
            }

            if (partner == null) {
                String error = "查询账户失败为空，更新积分状态失败";
                resultMap.put("errorMsg", error);
                resultMap.put("success", false);
                log.error("error:{} {}", error, dealerId, applicationType, freeze);
                return resultMap;
            } else if (partner.getSapCode() == null || "1".equals(partner.getSapCode())) {
                String error = "查询账户SAP_CODE失败为空，更新积分状态失败";
                resultMap.put("errorMsg", error);
                resultMap.put("success", false);
                log.error("error:{} {}", error, dealerId, applicationType, freeze);
                return resultMap;
            } else {
                OrganizationVo orgVo = organizationService.selectByPrimaryKey(dealerId);
                //插入账户
                WXTPointAccountVo pointAccountVo = new WXTPointAccountVo();
                pointAccountVo.setIsEnabled(true);
                pointAccountVo.setPointAccountOwnerId(partner.getPartnerId());
                pointAccountVo.setPointAccountOwnerCode(partner.getEnterpriseCode());
                pointAccountVo.setPointAccountOwnerName(orgVo.getOrganizationName());
                pointAccountVo.setPointAccountType(PointAccountType.SP.name());
                pointAccountVo.setCreatedBy(ContextUtil.getCurUserId());
                pointAccountBizService.insert(pointAccountVo);
                processUpdatePointState(applicationType, freeze, pointAccountVo);

                resultMap.put("msg", "更新积分状态成功");
                resultMap.put("success", true);
            }
        }

        return resultMap;
    }

    /**
     * 和后台数据库交互Update PointState
     *
     * @param applicationType
     * @param freeze
     * @param pointAccountVo
     * @return
     */
    private int processUpdatePointState(String applicationType, boolean freeze, WXTPointAccountVo pointAccountVo) {
        String curFrozenTypeStr = pointAccountVo.getFrozenType();
        String newFrozenTypeStr = null;
        int updateResult = 0;
        // 根据
        if (freeze) {
            if (curFrozenTypeStr == null) {
                newFrozenTypeStr = (applicationType + ",");
            } else {
                if (!curFrozenTypeStr.contains(applicationType)) {
                    newFrozenTypeStr = curFrozenTypeStr + (applicationType + ",");
                }
            }
        } else {
            if (curFrozenTypeStr != null) {
                if (curFrozenTypeStr.contains(applicationType)) {
                    newFrozenTypeStr = curFrozenTypeStr.replace((applicationType + ","), "");
                }
            }
        }
        log.debug("wXTPointAccountVoMapper.updateByPrimaryKeySelective newFrozenTypeStr {}", newFrozenTypeStr);
        if (newFrozenTypeStr != null) {
            WXTPointAccountVo record = new WXTPointAccountVo();
            record.setId(pointAccountVo.getId());
            record.setFrozenType(newFrozenTypeStr);
            record.setLastUpdatedBy(ContextUtil.getCurUserId());
            record.setLastUpdateTime(DateUtil.getCurrentDate());
            updateResult = wXTPointAccountVoMapper.updateByPrimaryKeySelective(record);
            log.debug("wXTPointAccountVoMapper.updateByPrimaryKeySelective result {}", updateResult);
        }
        return updateResult;
    }

    /**
     * 根据dealerId（PointAccountOwnerId）获取point account
     * @param dealerId
     * @return
     */
    private List<WXTPointAccountVo> getWxtPointAccountVos(Long dealerId,String userId) {
        WXTPointAccountVoExample example = new WXTPointAccountVoExample();
        WXTPointAccountVoExample.Criteria criteria = example.createCriteria();
        if(userId != null){
            criteria.andPointAccountOwnerCodeEqualTo(userId).andDeleteFlagNotEqualTo(true);
        } else {
            criteria.andPointAccountOwnerIdEqualTo(dealerId).andDeleteFlagNotEqualTo(true);
        }

        return wXTPointAccountVoMapper.selectByExample(example);
    }

    @Override
    public ResponseMap searchLogistics(String applicationCode) {
        ResponseMap resultMap = new ResponseMap();
        WXTMaterialApplicationLogisticsVoExample example = new WXTMaterialApplicationLogisticsVoExample();
        example.createCriteria().andShipmentCodeEqualTo(applicationCode);
        example.setOrderByClause("CREATION_TIME DESC");
        List<WXTMaterialApplicationLogisticsVo> logisticsList = wxtMaterialApplicationLogisticsVoMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(logisticsList)) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg("无结果。");
        }
        List<DicItemVo> expressCompanys = dicItemVoMapper.selectByCode("EXPRESS_COMPANY");
        WXTMaterialApplicationVoExample example1 = new WXTMaterialApplicationVoExample();
        example1.createCriteria().andApplicationCodeEqualTo(applicationCode);
        String phoneNum = null;
        List<WXTMaterialApplicationVo> wxtMaterialApplicationVos = wxtMaterialApplicationVoMapper.selectByExample(example1);
        if (CollectionUtil.isNotEmpty(wxtMaterialApplicationVos)) {
            phoneNum = wxtMaterialApplicationVos.get(0).getContactNumber();
        }
        for (WXTMaterialApplicationLogisticsVo vo : logisticsList) {
            for (DicItemVo expressCompany : expressCompanys) {
                if (StringUtils.equals(vo.getExpressCompany(), expressCompany.getDicItemName()) && StringUtils.isNotBlank(vo.getExpressNumber())) {
                    Kuaidi100Util.KuaidiQueryResult kuaidiQueryResult = Kuaidi100Util.kuaidiQuery(vo.getExpressNumber(), phoneNum);
                    if ("ok".equals(kuaidiQueryResult.getMessage())) {
                        vo.setKuaidiContexts(kuaidiQueryResult.getData());
                    }
                }
            }
        }
        resultMap.setDataResult(logisticsList);
        return resultMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ResponseMap createPromotionGiftApplication(ApplicationHead applicationHead, ApplicationLine[] applicationLines) {
        log.debug("Start ApplicationServiceImpl.create() applicationHead=" + applicationHead);
        ResponseMap resultMap = new ResponseMap();
        PointType pointType = ApplicationUtil.getPointTypeByAppType(applicationHead.getApplicationType());
        //物料表中的积分类型
        String pointCode = pointType != null ? pointType.getValue() : applicationHead.getApplicationType();
        WxTUser user = ContextUtil.getCurUser();
        WXTMaterialApplicationVo application = null;
        boolean couponGiftFlag = applicationHead.getCouponGiftFlag() != null && applicationHead.getCouponGiftFlag();
        try {
            if (couponGiftFlag) {
                application = this.createCouponGiftApplication(applicationHead, applicationLines, user, pointType, pointCode);
            } else {
                application = this.createCommonPromotionGiftApplication(applicationHead, applicationLines, user, pointType, pointCode);
            }
            resultMap.put(Constants.RESULT_SUCCESS, true);
            resultMap.put("applicationId", application.getId());
            resultMap.put("applicationCode", application.getApplicationCode());
        } catch (WxPltException e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("ApplicationServiceImpl.create() exception", e);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
            resultMap.put(Constants.ERROR_CODE, "申请单创建失败," + e.getMessage());
            return resultMap;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("ApplicationServiceImpl.createPromotionGiftApplication() exception", e);
            resultMap.put(Constants.RESULT_SUCCESS, false);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请单创建失败," + e.getMessage());
            resultMap.put(Constants.RESULT_ERROR, "程序异常，回滚变更:" + e.getMessage());
            return resultMap;
        }
        return resultMap;
    }

    /**
     * 普通礼品兑换
     */
    private WXTMaterialApplicationVo createCommonPromotionGiftApplication(ApplicationHead applicationHead, ApplicationLine[] applicationLines,
                                                                          WxTUser user,
                                                                          PointType pointType,
                                                                          String pointCode) throws WxPltException{
        List<PromotionGiftApplyInfo> promotionGiftApplyInfos = this.collectPromotionGiftApplyInfo(applicationLines);
        this.checkPromotionGiftApplyInfo(promotionGiftApplyInfos, user, pointCode);
        WXTMaterialApplicationVo application = this.createApplication(user, applicationHead, pointType, applicationLines,false);
        this.updatePromotionGiftPool(promotionGiftApplyInfos, user, application);
        return application;
    }


    /**
     * 优惠券兑换
     */
    public WXTMaterialApplicationVo createCouponGiftApplication(ApplicationHead applicationHead, ApplicationLine[] applicationLines,
                                                   WxTUser user,
                                                   PointType pointType,
                                                   String pointCode) throws WxPltException {
        log.debug("Start ApplicationServiceImpl.createCouponGiftApplication() applicationHead=" + applicationHead);
        List<CouponApplyParams> couponApplyParams = applicationHead.getCouponApplyParams();
        List<PromotionGiftApplyInfo> promotionGiftApplyInfos = this.couponCollectPromotionGiftApplyInfo(couponApplyParams);
        this.couponApplyCheck(couponApplyParams, applicationLines);
        this.checkPromotionGiftApplyInfo(promotionGiftApplyInfos, user, pointCode);
        WXTMaterialApplicationVo application = this.createApplication(user, applicationHead, pointType, applicationLines,true);
        this.updatePromotionGiftPool(promotionGiftApplyInfos, user, application);
        return application;
    }

    /**
     * 创建订单
     * @throws WxPltException 
     */
    private WXTMaterialApplicationVo createApplication(WxTUser user,
                                                       ApplicationHead applicationHead,
                                                       PointType pointType,
                                                       ApplicationLine[] applicationLines,
                                                       boolean couponFlag
    ) throws WxPltException{
        WXTMaterialApplicationVo record = this.preparePromotionGiftVo(applicationHead);
        if (pointType != null && pointType.isPersonalAccount()) {
            record.setApplicationUserId(user.getUserId().toString());
        }
        record.setAttribute1(Constants.PROMOTION_EXCHANGE);
        record.setAttribute2(couponFlag ? Constants.COUPON_EXCHANGE : null);
        record.setPromotionType(applicationHead.getFromSource());
        record.setVersionNo(WXTMaterialApplicationVo.VERSION_NO_202109);
        wxtMaterialApplicationVoMapper.insertSelective(record);
        Long applicationId = record.getId();
        List<WXTMaterialApplicationDetailVo> lines = this.preparePromotionGiftLineVo(applicationLines, applicationId);
        wxtMaterialApplicationDetailVoMapper.insertBatch(lines);
        //生成出库单
        this.pushWarehouseOutInfo(applicationId);
        // 申请单状态变化历史记录
        String status = ApplicationState.OUTBOUND.getValue();
        String step = "用户下单";
        applicationBizService.pushApplicationHistory(applicationId, step, "", status, applicationHead.getApplicationType() + (couponFlag ? "[优惠券兑换]" : "[礼品兑换]"));
        return record;
    }

    private void couponApplyCheck(List<CouponApplyParams> couponApplyParams, ApplicationLine[] applicationLines) throws WxPltException {
        //校验选择的商品是否是优惠券的
        if (CollectionUtil.isEmpty(couponApplyParams)) {
            throw new WxPltException("缺少优惠券参数");
        }
        HashSet<Long> couponIds = new HashSet<Long>();
        for (CouponApplyParams couponApplyParam : couponApplyParams) {
            couponIds.add(couponApplyParam.getCouponId());
        }
        List<CouponOptionDetail> list = couponOptionDetailMapper.couponDetailByCouponIds(couponIds);
        //校验优惠券选项是否满足要求
        for (CouponApplyParams couponApplyParam : couponApplyParams) {
            for (CouponOptionDetail couponOptionDetail : list) {
                if(couponApplyParam.getCouponId().equals(couponOptionDetail.getCouponId())){
                    if(couponApplyParam.getOptions().size() == couponOptionDetail.getOptionNum()){
                        break;
                    }else{
                        throw new WxPltException(couponOptionDetail.getMaterialName() + "，该优惠券必须选择 " + couponOptionDetail.getOptionNum() + " 个选项");
                    }
                }
            }
        }
        //校验优惠券物料选择的数量
        HashMap<Long, Long> giftAmountMap = new HashMap<Long, Long>();
        for (CouponOptionDetail couponOptionDetail : list) {
            for (CouponApplyParams couponApplyParam : couponApplyParams) {
                if (couponOptionDetail.getCouponId().equals(couponApplyParam.getCouponId()) && couponApplyParam.getOptions().contains(couponOptionDetail.getCouponOptionId())) {
                    Long count = giftAmountMap.get(couponOptionDetail.getMaterialId());
                    count = count == null ? couponOptionDetail.getQuantity() : count + couponOptionDetail.getQuantity();
                    giftAmountMap.put(couponOptionDetail.getMaterialId(), count);
                }
            }
        }
        HashMap<Long, Long> applyGiftCountMap = this.summaryApplyMaterials(applicationLines);
        for (Entry<Long, Long> entry : applyGiftCountMap.entrySet()) {
            Long materialId = entry.getKey();
            Long applyAmount = entry.getValue();
            Long needAmount = giftAmountMap.get(materialId);
            if (needAmount == null || !applyAmount.equals(needAmount)) {
                throw new WxPltException("当前选购数量不符合要求！物料数量须等于礼品券赠送数量才可提交下单。");
            }
        }
    }

    /**
     * 核销礼品池礼品并且记录日志
     */
    private void updatePromotionGiftPool(List<PromotionGiftApplyInfo> giftApplyInfos,
                                         WxTUser user,
                                         WXTMaterialApplicationVo application) throws WxPltException{
        Date now = new Date();
        Map<String, PromotionGiftPool> updatePoolSpuMap = new HashMap<String, PromotionGiftPool>(giftApplyInfos.size()); //根据SPU合并礼品池变更信息
        List<PromotionGiftModifyLog> modifyLogs = new ArrayList<PromotionGiftModifyLog>(giftApplyInfos.size()); //礼品池变更日志集合
        for (PromotionGiftApplyInfo giftApplyInfo : giftApplyInfos) {
            //根据SPU合并礼品池变更信息
            PromotionGiftPool update = updatePoolSpuMap.get(giftApplyInfo.getSpu());
            if(update == null) {
            	update = new PromotionGiftPool();
                update.setId(giftApplyInfo.getId());
                update.setAvailableQuantity(giftApplyInfo.getAvailableQuantity() - giftApplyInfo.getApplyAmount());
                update.setVersionNo(giftApplyInfo.getVersionNo());
                update.setUpdateUserId(user.getUserId());
                
                PromotionGiftModifyLog log = new PromotionGiftModifyLog();
                log.setPoolId(giftApplyInfo.getId());
                log.setModifyTime(now);
                log.setModifyQuantity(giftApplyInfo.getApplyAmount());
                log.setModifyType(ModifyType.MODIFY_TYPE_REDUCE.getCode());
                log.setBusinessKey(application.getId().toString());
                log.setExtProperty1(application.getApplicationCode());
                log.setBusinessType("exchange");
                log.setComments("用户下单兑换");
                log.setCreateTime(now);
                log.setCreateUserId(application.getCreatedBy());
                update.setModifyLog(log);
                
                modifyLogs.add(log);
                
                updatePoolSpuMap.put(giftApplyInfo.getSpu(), update);
            }else {
                update.setAvailableQuantity(update.getAvailableQuantity() - giftApplyInfo.getApplyAmount());
                update.getModifyLog().setModifyQuantity(update.getModifyLog().getModifyQuantity() + giftApplyInfo.getApplyAmount());
            }
        }
        
        for(PromotionGiftPool update : updatePoolSpuMap.values()) {
            int i = promotionGiftPoolMapper.updatePromotionGiftPool(update);
            if (i <= 0) {
                throw new WxPltException("数据过期超时，请重新提交。");
            }
        }
        
        promotionGiftModifyLogMapper.insertBatch(modifyLogs);
        
        //物料库存更新
        updateGiftInventory(giftApplyInfos, application);
    }
    
    private void updateGiftInventory(List<PromotionGiftApplyInfo> giftApplyInfos, WXTMaterialApplicationVo application) throws WxPltException {
    	List<String> materialCodes = new ArrayList<String>(giftApplyInfos.size());
    	for(PromotionGiftApplyInfo line : giftApplyInfos) {
    		materialCodes.add(line.getMaterialSkuCode());
    	}
    	Map<String, Object> params = new HashMap<String, Object>(3);
    	params.put("productType", Inventory2021.PRODUCT_TYPE_MATERIAL);
    	params.put("inventoryType", Inventory2021.INVENTORY_TYPE_MATERIAL);
    	//productKey包含供应商信息
    	params.put("productKeys", materialCodes);
    	List<Inventory2021> list = inventory2021BizService.queryByParams(params);
    	Map<String, Inventory2021> inventorySkuMap = new HashMap<String, Inventory2021>(list.size());
    	for(Inventory2021 inventory : list) {
    		inventorySkuMap.put(inventory.getProductKey(), inventory);
    	}
    	
        List<Inventory2021> inventory2021s = new ArrayList<Inventory2021>(giftApplyInfos.size()); //变更物料库存集合
    	Inventory2021 inventory = null;
    	InventoryLog2021 inventoryLog = null;
    	for(PromotionGiftApplyInfo line : giftApplyInfos) {
    		inventory = inventorySkuMap.get(line.getMaterialSkuCode());
    		inventoryLog = new InventoryLog2021();
    		inventoryLog.setBusinessKey(application.getId().toString());
    		inventoryLog.setBusinessType(ApplicationBizService.INVENTORY_BUSINESS_TYPE);
    		inventoryLog.setModifyQuantity(-line.getApplyAmount());
    		inventoryLog.setOriginalQuantity(inventory.getCurrentQuantity());
    		inventoryLog.setLogDesc("积分订单【" + application.getApplicationCode() + "】减少【" + line.getApplyAmount() + "】");
    		inventoryLog.setExtProperty1(application.getApplicationCode());
    		inventory.setInventoryLog(inventoryLog);
    		inventory.setCurrentQuantity(inventory.getCurrentQuantity() - line.getApplyAmount());
    		inventory2021s.add(inventory);
    	}
    	inventory2021BizService.save(inventory2021s);

    }

//    private void insertCommonGiftExchangeLog(PromotionGiftApplyInfo giftApplyInfo,Date date,WXTMaterialApplicationVo application){
//        //生成一条日志记录
//        PromotionGiftModifyLog log = new PromotionGiftModifyLog();
//        log.setPoolId(giftApplyInfo.getId());
//        log.setModifyTime(date);
//        log.setModifyQuantity(giftApplyInfo.getApplyAmount());
//        log.setModifyType(ModifyType.MODIFY_TYPE_REDUCE.getCode());
//        log.setBusinessKey(application.getId().toString());
//        log.setExtProperty1(application.getApplicationCode());
//        log.setBusinessType("exchange");
//        log.setComments("用户下单兑换");
//        log.setCreateTime(date);
//        log.setCreateUserId(application.getCreatedBy());
//        promotionGiftModifyLogMapper.insertSelective(log);
//    }
//
//    private void insertCouponGiftExchangeLog(PromotionGiftApplyInfo giftApplyInfo,Date date,WXTMaterialApplicationVo application){
//        Integer applyAmount = giftApplyInfo.getApplyAmount();
//        List<List<Long>> couponOptions = giftApplyInfo.getCouponOptions();
//        for (int i = 0; i < applyAmount; i++) {
//            //生成一条日志记录
//            PromotionGiftModifyLog log = new PromotionGiftModifyLog();
//            log.setPoolId(giftApplyInfo.getId());
//            log.setModifyTime(date);
//            log.setModifyQuantity(1);
//            log.setModifyType(ModifyType.MODIFY_TYPE_REDUCE.getCode());
//            log.setBusinessKey(application.getId().toString());
//            log.setExtProperty1(application.getApplicationCode());
//            log.setBusinessType("exchange");
//            log.setComments("优惠券兑换");
//            log.setCreateTime(date);
//            log.setCreateUserId(application.getCreatedBy());
//            log.setExtProperty2(CollectionUtil.join(couponOptions.get(i), ","));
//            promotionGiftModifyLogMapper.insertSelective(log);
//        }
//    }

    /**
     * 检查礼品池库存并组合申请信息
     */
    private List<PromotionGiftApplyInfo> checkPromotionGiftApplyInfo(List<PromotionGiftApplyInfo> promotionGiftApplyInfos, WxTUser user, String pointCode) throws WxPltException {
        HashSet<Long> materialIds = new HashSet<Long>();
        for (PromotionGiftApplyInfo promotionGiftApplyInfo : promotionGiftApplyInfos) {
            materialIds.add(promotionGiftApplyInfo.getMaterialId());
        }
        List<PromotionGiftPool> promotionGiftPools = promotionGiftPoolMapper.selectPromotionGiftByMaterialIds(user.getOrgId(), pointCode, materialIds);
        Map<String, Integer> availableQuantitySpuMap = new HashMap<String, Integer>(materialIds.size());
        Map<Long, PromotionGiftPool> poolMaterialIdMap = new HashMap<Long, PromotionGiftPool>(promotionGiftPools.size());
        for (PromotionGiftPool promotionGiftPool : promotionGiftPools) {
        	poolMaterialIdMap.put(promotionGiftPool.getMaterialId(), promotionGiftPool);
        }
        
        for (PromotionGiftApplyInfo promotionGiftApplyInfo : promotionGiftApplyInfos) {
        	PromotionGiftPool promotionGiftPool = poolMaterialIdMap.get(promotionGiftApplyInfo.getMaterialId());
        	if(promotionGiftPool == null) {
        		throw new WxPltException("物料" + promotionGiftApplyInfo.getMaterialId() + "异常");
        	}
        	Integer availableQuantity = availableQuantitySpuMap.get(promotionGiftPool.getMaterialCode());
        	if(availableQuantity == null) {
        		availableQuantity = promotionGiftPool.getAvailableQuantity();
        	}
        	if(promotionGiftApplyInfo.getApplyAmount() > availableQuantity) {
        		throw new WxPltException("物料" + promotionGiftPool.getMaterialName() + "下单的数量超过了可申请数量");
        	}
            promotionGiftApplyInfo.setId(Long.parseLong(promotionGiftPool.getExtProperty1())); //ID可能有重复，暂存到extProperty1
            promotionGiftApplyInfo.setVersionNo(promotionGiftPool.getVersionNo());
            promotionGiftApplyInfo.setAvailableQuantity(promotionGiftPool.getAvailableQuantity());
            promotionGiftApplyInfo.setSpu(promotionGiftPool.getMaterialCode());
            promotionGiftApplyInfo.setMaterialSkuCode(promotionGiftPool.getExtProperty2()); //实际物料编码
            
        	availableQuantitySpuMap.put(promotionGiftPool.getMaterialCode(), availableQuantity - promotionGiftApplyInfo.getApplyAmount());
        }
        return promotionGiftApplyInfos;
    }

    /**
     * 按照物料ID汇总申请的数量
     */
    private HashMap<Long, Long> summaryApplyMaterials(ApplicationLine[] applicationLines) {
        HashMap<Long, Long> tempMap = new HashMap<Long, Long>();
        for (ApplicationLine applicationLine : applicationLines) {
            Long count = tempMap.get(applicationLine.getMaterialId());
            if (count == null) {
                count = 0L;
            }
            count += applicationLine.getApplicationQty() == null ? 0L : applicationLine.getApplicationQty();
            tempMap.put(applicationLine.getMaterialId(), count);
        }
        return tempMap;
    }

    /**
     *  组合需要核销的礼品信息
     */
    private List<PromotionGiftApplyInfo> collectPromotionGiftApplyInfo(ApplicationLine[] applicationLines){
        List<PromotionGiftApplyInfo> list = new ArrayList<PromotionGiftApplyInfo>();
        HashMap<Long, Long> amountMap = this.summaryApplyMaterials(applicationLines);
        for (Long materialId : amountMap.keySet()) {
            PromotionGiftApplyInfo promotionGiftApplyInfo = new PromotionGiftApplyInfo();
            promotionGiftApplyInfo.setMaterialId(materialId);
            promotionGiftApplyInfo.setApplyAmount(amountMap.get(materialId).intValue());
            promotionGiftApplyInfo.setCouponFlag(false);
            list.add(promotionGiftApplyInfo);
        }
        return list;
    }

    /**
     *  组合需要核销的优惠券信息
     */
    private List<PromotionGiftApplyInfo> couponCollectPromotionGiftApplyInfo(List<CouponApplyParams> couponApplyParams){
        List<PromotionGiftApplyInfo> list = new ArrayList<PromotionGiftApplyInfo>();
        HashMap<Long, Integer> amountMap = new HashMap<Long, Integer>();
        for (CouponApplyParams couponApplyParam : couponApplyParams) {
            Integer amount = amountMap.get(couponApplyParam.getCouponId());
            amountMap.put(couponApplyParam.getCouponId(), amount == null ? 1 : amount + 1);
        }
        for (Long materialId : amountMap.keySet()) {
            PromotionGiftApplyInfo promotionGiftApplyInfo = new PromotionGiftApplyInfo();
            promotionGiftApplyInfo.setMaterialId(materialId);
            promotionGiftApplyInfo.setApplyAmount(amountMap.get(materialId));
            promotionGiftApplyInfo.setCouponFlag(true);
            list.add(promotionGiftApplyInfo);
        }
        for (PromotionGiftApplyInfo promotionGiftApplyInfo : list) {
            ArrayList<List<Long>> options = new ArrayList<List<Long>>();
            for (CouponApplyParams couponApplyParam : couponApplyParams) {
                if(promotionGiftApplyInfo.getMaterialId().equals(couponApplyParam.getCouponId())){
                    options.add(couponApplyParam.getOptions());
                }
            }
            promotionGiftApplyInfo.setCouponOptions(options);
        }
        return list;
    }
}
