select p.name "产品中文名", p.alias_name "产品别名",  (select substring((
 SELECT ','+x.product_name FROM 
 (select distinct dp.product_name from wx_t_db2b_product dp where dp.sku=p.sku) x FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) "商品名称" from wx_t_product p
 
 
 select sku, name, case when p.category='TCP' then '是' else '否' end "特劲产品(后续产品无物流信息)", 
(select count(1) from wx_t_oem_delivery_product dp where dp.product_id=p.sku) "中商物流条数",
(select count(1) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "中商激活条数(生产批次数)",
(case when exists (select 1 from temp_qr_sku qs where qs.sku=p.sku) then '是' else '否' end) "生成QR"
,(case when p.ext_flag&8>0 then '是' else '否' end) "是否需要扫码",
(case when p.support_order=1 then '是' else '否' end) "是否支持下单",
(case when p.status=1 then 'Active' else 'Inactive' end) "状态",
(case when convert(float,p.capacity)=0 then '' when p.pack/convert(float,p.capacity)>1 then '箱/瓶' else '桶' end) "单位",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.category' and di1.dic_item_code=p.category) "产品分类",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.oilType' and di1.dic_item_code=p.oil_type) "产品系列",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.capacity' and di1.dic_item_code=p.capacity) "产品容量",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.viscosity' and di1.dic_item_code=p.viscosity) "产品规格"
--distinct category, is_competing 
from wx_T_product p where (category is null or category!='QT') --and status=1 --and pack>0 
--and p.support_order=1 --and convert(float,p.capacity)>0
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)