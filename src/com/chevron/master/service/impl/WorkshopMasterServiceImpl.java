package com.chevron.master.service.impl;

import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.business.WorkshopRewardRecordBizService;
import com.chevron.master.controller.WorkshopMasterController;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterExample;
import com.chevron.master.model.WorkshopMasterParams;
import com.chevron.master.model.enums.CampaignEm;
import com.chevron.master.service.WorkshopMasterService;
import com.chevron.pms.business.WorkshopStatusBizService;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.log.util.LogUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 门店操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2019-07-05 13:52
 */
@Service
public class WorkshopMasterServiceImpl implements WorkshopMasterService {

	private final static Logger log = LoggerFactory.getLogger(WorkshopMasterServiceImpl.class);

	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;
	@Autowired
	private WorkshopMasterMapper workshopMasterMapper;
	@Autowired
	private DicItemVoMapper dicItemVoMapper;
	@Autowired
	private WorkshopRewardRecordBizService workshopRewardRecordBizService;

	@Resource
	WorkshopStatusBizService workshopStatusBizService;
	@Override
	public JsonResponse save(WorkshopMaster record) {
		JsonResponse map = new JsonResponse();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				workshopMasterBizService.insert(record);
			}else{
				workshopMasterBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse delete(List<Long> ids) {
		JsonResponse map = new JsonResponse();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			workshopMasterBizService.delete(ids);
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}

	@Override
	public JsonResponse getBean(Long id, String dmsKey){
		JsonResponse map = new JsonResponse();
		log.info("getBean: " + id + "," + dmsKey);
		try {
			if(id == null) {
				//DMS门店
				map.put("bean", workshopMasterMapper.selectDmsWorkshopByKey(dmsKey));
			}else {
				//PP门店
				map.put("bean", workshopMasterBizService.getBean(id));
			}
			map.put("workshopPropertySource", dicItemVoMapper.selectByCode(WorkshopMasterController.DIC_TYPE_CODE_WORKSHOP_PROPERTY));
//			map.put("workshopPropertySource", dicItemVoMapper.selectByCode(WorkshopMasterController.DIC_TYPE_CODE_FROM_SOURCE));
			log.info("getBean success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.getBean", id + "," + dmsKey);
		}
		return map;
	}

	@Override
	public JsonResponse unbindDms(Long workshopId) {
		JsonResponse map = new JsonResponse();
		log.info("unbindDms: " + workshopId);
		try {
			Date now = DateUtils.getCurrentDate();
			WorkshopMaster newRecord = new WorkshopMaster();
			newRecord.setId(workshopId);
			newRecord.setDmsKey("-1");
			newRecord.setExtProperty21(now);
			newRecord.setExtProperty13(ContextUtil.getCurUserId().toString());
			workshopMasterBizService.update(newRecord);
			log.info("unbindDms success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.unbindDms", workshopId.toString());
		}
		return map;
	}

	@Override
	public JsonResponse bindDms(Long workshopId, String dmsKey) {
		JsonResponse map = new JsonResponse();
		log.info("bindDms: " + workshopId + "," + dmsKey);
		try {
			//确保dms门店未匹配过
			WorkshopMasterExample example = new WorkshopMasterExample();
			example.createCriteria().andDmsKeyEqualTo(dmsKey).andDeleteFlagEqualTo(0);
			List<WorkshopMaster> list = workshopMasterBizService.queryByExample(example);
			if(!list.isEmpty()) {
				throw new WxPltException("当前DMS门店已经匹配了\"" + list.get(0).getWorkshopName() + "\"门店。门店不能重复匹配");
			}
			Date now = DateUtils.getCurrentDate();
			WorkshopMaster newRecord = new WorkshopMaster();
			newRecord.setId(workshopId);
			newRecord.setDmsKey(dmsKey);
			newRecord.setExtProperty21(now);
			newRecord.setExtProperty13(ContextUtil.getCurUserId().toString());
			workshopMasterBizService.update(newRecord);
			log.info("bindDms success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.bindDms", workshopId.toString() + "," + dmsKey);
		}
		return map;
	}

	@Override
	public JsonResponse updateWorkShopVilitional(Long workShopId) {
		JsonResponse resMap = new JsonResponse();
		try {
			WorkshopMaster workshopMaster = new WorkshopMaster();
			workshopMaster.setId(workShopId);
			workshopMaster.setExtProperty16("1");
			workshopMasterBizService.update(workshopMaster);
		} catch (WxPltException e) {
			resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.updateWorkShopVilitional", workShopId.toString());
		}
		return resMap;
	}

	@Override
	public JsonResponse deleteWorkShop(Long workshopId) {
		JsonResponse resMap = new JsonResponse();
		WorkshopMaster workshopMaster = new WorkshopMaster(); 
		workshopMaster.setId(workshopId);
		workshopMaster.setDeleteFlag(1);
		try {
			//更改门店状态
			workshopMasterBizService.update(workshopMaster);
			log.info(" 门店管理逻辑删除门店,门店id: "+workshopId+"==================");
			LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.chevron.master.service.impl.WorkshopMasterServiceImpl.deleteWorkShop" , "门店管理逻辑删除门店 ,门店id:" + workshopId);
		} catch (WxPltException e) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			e.printStackTrace();
		}
		return resMap;
	}

	@Override
	public JsonResponse updateWorkShopPartnerConfirm(String workShopIds,Integer extFlag) {
		JsonResponse resMap = new JsonResponse();
		if(StringUtils.isBlank(workShopIds)) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的门店id");
			return resMap;
		}
		try {
			workshopMasterBizService.partnerConfirm(workShopIds, extFlag);
		} catch (WxPltException e) {
			resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.updateWorkShopPartnerConfirm", workShopIds);
		}
		return resMap;
	}

	//手动调用接口-潜在客户转合作客户(确认合作)
	@Override
	public JsonResponse updateWorkShopPartnerConfirmV2(String workShopIds,Integer extFlag) {
		JsonResponse resMap = new JsonResponse();
		if(StringUtils.isBlank(workShopIds)) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的门店id");
			return resMap;
		}
		try {
			workshopMasterBizService.partnerConfirmV2(workShopIds, extFlag);
		} catch (WxPltException e) {
			resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resMap.handleException(e, this, ContextUtil.getCurUserId(),
					"com.chevron.master.service.impl.WorkshopMasterServiceImpl.updateWorkShopPartnerConfirmV2", workShopIds);
		}
		return resMap;
	}

	@Override
	public JsonResponse queryWorkShopInfoForPage(WorkshopMasterParams workshopMasterParams) {
		JsonResponse resMap = new JsonResponse();
		//权限控制 
		WxTUser curUser = ContextUtil.getCurUser();
		String userModel = curUser.getUserModel();
		if(WxTUser.USER_MODEL_SP.equals(userModel)) {						
			workshopMasterParams.setPartnerId(curUser.getOrgId());
		}
		
		//获取数据
		List<WorkshopMaster> WorkShopInfoList = workshopMasterMapper.queryWorkShopInfoForPage(workshopMasterParams);
		resMap.setTotalOfPaging(workshopMasterParams.getTotalCount());
		resMap.setListResult(WorkShopInfoList);
		return resMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateExtFlag(WorkshopMaster req) throws WxPltException {
		log.info("门店打标开始，workshopId={}，req={}", req.getId(), JsonUtil.toJSONString(req));

		Date currentTime = DateUtil.getCurrentDate();

		// 更新门店标签
		workshopMasterBizService.updateExtFlag(req, currentTime);
		log.info("门店标签已更新，workshopId={}，extFlag={}", req.getId(), req.getExtFlag());

		// 记录门店奖励（手工发放）
		if (req.getExtFlag() != null) {
			if (req.getExtFlag() == 20) { // 国六节油养护站（原CK合作门店）
				workshopRewardRecordBizService.createManualSendRecord(ContextUtil.getCurUserId(), req.getId()
						, CampaignEm.NATIONAL_VI_FUEL_CONSERVATION_STATION.getName(), currentTime);
				//潜在门店国六申请，自动转为合作门店，并同步至大咖汇ETO
				final WorkshopMaster workshopMaster = workshopMasterMapper.selectByPrimaryKey(req.getId());
				if(!(WorkshopMaster.WORKSHOP_STATUS3.equals(workshopMaster.getStatus()))){
					log.info("BPM潜在门店国六申请，潜在转合作！workshopid="+workshopMaster.getId());
					workshopMaster.setNewExtFlag(256);
					workshopMaster.setRemark("潜在门店BPM申请国六流程,潜在门店转为合作门店! extFlag+256");
					workshopMaster.setExtProperty22(DateUtils.getCurrentDate());
					workshopMaster.setActivationTime(workshopMaster.getExtProperty22());
					workshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS3);
					//激活门店
					workshopStatusBizService.insertWhenNotExists(workshopMaster.getId(), WorkshopMaster.WORKSHOP_STATUS3.toString(),
							null, workshopMaster.getActivationTime());
					//门店信息更新且同步到ETO大咖汇
					workshopMasterBizService.update(workshopMaster);
				}

			} else if (req.getExtFlag() == 21) { // 超低灰门店
				workshopRewardRecordBizService.createManualSendRecord(ContextUtil.getCurUserId(), req.getId()
						, CampaignEm.ULTRA_LOW_ASH_STORES.getName(), currentTime);
			}
		}

		log.info("门店打标完成，workshopId={}", req.getId());
	}

}
