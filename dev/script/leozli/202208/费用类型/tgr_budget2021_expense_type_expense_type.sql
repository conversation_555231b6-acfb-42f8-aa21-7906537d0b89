create trigger tgr_budget2021_expense_type_expense_type
--ALTER trigger [dbo].[tgr_budget2021_expense_type_expense_type] 
on wx_t_budget2021_expense_type
    for update
as
    declare @old_expense_type varchar(30), @new_expense_type varchar(30), @used_flag int;
    --更新前的数据
    select @old_expense_type = expense_type from deleted;
    --更新后的数据
    select @new_expense_type = expense_type from inserted;
    if (@old_expense_type!=@new_expense_type)
        begin
	        --更新费用procedure中的费用类型
	        update [PP_MID].[dbo].[mid_kn_expense_key] set expense_sub_type=@new_expense_type where expense_sub_type=@old_expense_type
        end
go