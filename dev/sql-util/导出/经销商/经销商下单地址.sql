SELECT o.organization_name "经销商", pro.region_name AS "省份", city.region_name AS "城市", dist.region_name AS "区域",
t1.address "地址"
FROM wx_t_partner_address t1
	LEFT JOIN wx_t_region pro ON pro.region_code = t1.province_code
	LEFT JOIN wx_t_region city ON city.region_code = t1.city_code
	LEFT JOIN wx_t_region dist ON dist.region_code = t1.dist_code
	left join wx_t_organization o on o.id=t1.partner_id
WHERE 1 = 1
	AND o.status=1
	order by o.id