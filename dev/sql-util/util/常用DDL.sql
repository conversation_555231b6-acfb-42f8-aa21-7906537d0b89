DROP INDEX ix_b2b_ver_point_pol_sku ON wx_t_b2b_verify_point_policy
ALTER TABLE [wx_t_b2b_verify_point_policy] ALTER COLUMN sku                              nvarchar(4000)                             not null
ALTER TABLE [wx_t_b2b_verify_point_policy] add policy_name                      nvarchar(256)                                  null
CREATE INDEX ix_oem_product_packaging_code_id ON wx_t_oem_product_packaging_code (id);
ALTER TABLE wx_t_qrcode_short_url_pool ADD CONSTRAINT uk_qrcode_short_url_pool UNIQUE (qrcode);
ALTER TABLE wx_t_oem_product_packaging ADD CONSTRAINT pk_oem_product_packaging PRIMARY KEY NONCLUSTERED (id);
alter table wx_t_promotion_gift_pool drop uk_promotion_gift_pool