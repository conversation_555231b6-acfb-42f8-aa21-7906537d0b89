package com.chevron.b2b.service;

import com.chevron.b2b.model.Schedule;

import java.util.List;
import java.util.Map;

/**
 * 计划操作RPC接口
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
public interface ScheduleService {

	/**
	 * 保存计划信息
	 * @param record 计划对象
	 * @return 处理结果
	 */
	public Map<String, Object> save(Schedule record);
	
	/**
	 * 删除计划对象
	 * @param ids 被删除计划对象ID集合
	 * @return 处理结果
	 */
	public Map<String, Object> delete(List<Long> ids);

	/**
	 * 根据主键获取对象
	 * @param id 主键值
	 * @return 主键对应对象
	 */
	public Map<String, Object> getBean(Long id);
}
