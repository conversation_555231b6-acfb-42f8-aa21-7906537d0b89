package com.sys.quartz.business;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.task.dao.WxTaskMbMapper;
import com.chevron.task.model.WxTaskMain;
import com.chevron.task.model.WxTaskMb;
import com.chevron.task.service.AppTaskService;
import com.chevron.task.service.TaskMainServiceI;
import com.chevron.task.service.impl.TaskMainService;
import com.common.util.DateUtil;
import com.sys.push.model.WxPushMessage;
import com.sys.push.service.MessagePushService;
import com.sys.quartz.model.WorkshopExcuteUserInfo;
/**
 * 
 * @Author: bo.liu  2017-3-28 下午4:27:29 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class CreateTaskQuartzService{
	public final Logger log = Logger.getLogger(this.getClass());
	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;
	@Resource
	private WxTaskMbMapper mbMapper;
	@Resource
	private AppTaskService appTaskService;
	@Resource
	private TaskMainServiceI taskMainService;
	@Resource
	MessagePushService msgPushService;
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public synchronized void doHandleCreateTask(Date date) {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<Long> userIds = new ArrayList<Long>();
		try
		{	
			reqMap.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
			reqMap.put("status", 3);
			
			//0.获取门店列表，条件：状态是3，且执行人不为null的门店，关联查询对应执行人的组织id
			List<WorkshopMaster> lstWorkshop = workshopMasterBizService.querySimpleByParams(reqMap);
			//1.组装门店，执行人数据，用于创建任务
			List<WorkshopExcuteUserInfo> lstWorkshopExcuteUserInfo = new ArrayList<WorkshopExcuteUserInfo>();
			WorkshopExcuteUserInfo workshopExcuteUserInfo = null;//新的WorkshopExcuteUserInfo对象
			List<String> currentUserWorkshops = null;//1个数对象对应N个实例对象（一个用户对应N个门店，，用于存放相同执行人的门店）
			Long lastExcuteUserId = 0L;//控制->原始的用户执行人id
			Long currentExuteUserId = 0L;
			Long lastNewExcuteUserId = 0L;//控制->新实例对象
			
			
			//分组归类{用户:ss;门店:[,,,]}
			for(WorkshopMaster workShopVo:lstWorkshop)
			{
				currentExuteUserId = workShopVo.getExcuteUserId();
				Long workshopIds = workShopVo.getId();
				if(!currentExuteUserId.equals(lastExcuteUserId))
				{
					workshopExcuteUserInfo = new WorkshopExcuteUserInfo();//不等的时候新的实例对象
					currentUserWorkshops = new ArrayList<String>();
					workshopExcuteUserInfo.setUserId(currentExuteUserId);
					workshopExcuteUserInfo.setPartnerId(workShopVo.getPartnerId());
					workshopExcuteUserInfo.setUserName(workShopVo.getExecuteUserName());
				}
				
				currentUserWorkshops.add(""+workshopIds);
				lastExcuteUserId = currentExuteUserId;
				workshopExcuteUserInfo.setWorkshopIds(currentUserWorkshops);
				
				if(!currentExuteUserId.equals(lastNewExcuteUserId))
				{
					lstWorkshopExcuteUserInfo.add(workshopExcuteUserInfo);
				}
				lastNewExcuteUserId = currentExuteUserId;
			}
			
			//2.根据typecode，获取模板id
			reqMap.put("mbType", WxTaskMain.TASK_TYPE_XD);
			List<WxTaskMb>  lstMbs = mbMapper.queryMbsByMbTypeAndPartnerId(reqMap);
			if(null==lstMbs || lstMbs.isEmpty())
			{
				log.debug("------------------------------by ljc doHandleCreateTask 创建任务失败，没有找到对应的模板信息---------------------------");
				return ;
			}
			Long taskTemplateId = lstMbs.get(0).getMbId();
			//3.获取指定的步骤,并构建任务
			String steps = appTaskService.getTaskSteps(taskTemplateId, WxTaskMain.TASK_TYPE_XD);
			List<WxTaskMain> lstTaskMain = new ArrayList<WxTaskMain>();
			for(WorkshopExcuteUserInfo workshopExcuteUserInfo2:  lstWorkshopExcuteUserInfo)
			{
				List<String> lstworkshopIDS = workshopExcuteUserInfo2.getWorkshopIds();
				ImportDataPageModelUtil workshoppage = new ImportDataPageModelUtil(
						lstworkshopIDS, TaskMainService.TOTAL_WORKSHOP);
				int workshop_account = workshoppage.getTotalPages();
				for (int i = 1; i <= workshop_account; i++) {
					WxTaskMain wxTask = new WxTaskMain();
					wxTask.setExcuteUserId(workshopExcuteUserInfo2.getUserId());
					wxTask.setExecuteUserName(workshopExcuteUserInfo2.getUserName());
					wxTask.setFrequencyType(1);
					wxTask.setPublishStatus(1);
					wxTask.setTaskDescription("定时创建巡店任务");
					wxTask.setTaskStartTime(date);
					wxTask.setTaskFinishTime(DateUtil.addDays(date, 10));
					wxTask.setTaskMbId(taskTemplateId);
					wxTask.setTaskPriority("1");
					wxTask.setTaskSteps(steps);
					wxTask.setTmbTypeCode(WxTaskMain.TASK_TYPE_XD);
					List<String> tempWorkshopIds = workshoppage.getObjects(i);
					String workshopIDS = "";
					for(String workshopID:tempWorkshopIds)
					{
						workshopIDS+=workshopID+",";
					}
					wxTask.setWorkshopIds(workshopIDS.substring(0, workshopIDS.length()-1));
					lstTaskMain.add(wxTask);
				}
			}
			
			//录入任务
			for(WxTaskMain wxTask:lstTaskMain)
			{
				Long excuteUserId = wxTask.getExcuteUserId();
				if(!userIds.contains(excuteUserId))
				{
					userIds.add(excuteUserId);
				}
				//这里不推送，后面对多个用户一起推送
				taskMainService.insertTask(wxTask, steps, "", wxTask.getTaskStartTime().getTime(), wxTask.getTaskFinishTime().getTime(),TaskMainService.CREATE_TASK_NOT_SND_MSG); 
			}
			//这里对多个userId推送消息，，需要去重......05.31
			log.info("---doHandleCreateTask pushMessage start--");
			msgPushService.pushMessage(WxPushMessage.MSG_TYPE_PERSONAL, TaskMainService.TASK_MSG_TITLE,
					TaskMainService.TASK_MSG_CONTENT_TIME_XD, null, userIds);
			log.info("---doHandleCreateTask pushMessage end--");
			
			
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			ex.printStackTrace();
		}
		log.debug("------------------------------by ljc doHandleCreateTask end---------------------------");
	}
}
