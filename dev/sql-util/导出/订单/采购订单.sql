select o.organization_name "经销商名称", sc.ship_to_code, sc.sap_code sold_to_code, po.order_no "订单编号",pl.sku sku, p.name "产品名称",--bp.[pack_type] sales_unit,
pl.actual_amount "下单数量", pl.free_amount "赠送数量", pl.total_value "总价",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=1) "提交时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=3) "确认时间",
(select p.dic_item_name from wx_t_dic_item p where p.dic_type_code = 'PARTNER_ORDER_STATUS' and p.dic_item_code = po.status) as "状态"
				from wx_t_partner_order po 
				left join wx_t_partner_order_line pl on pl.partner_order_id=po.id
                --left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=pl.sku
				--left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
				left join wx_t_product p on p.sku=pl.sku
				left join wx_t_organization o on o.id=po.partner_id
				left join [PP_MID].[dbo].[mid_partner_sale_config] sc on po.partner_sale_config_id=sc.id
		where po.status not in (0,4) --and po.create_time>'2021-01-01' --and po.order_no='P07211008005253'
		order by po.id desc

select o.organization_name "经销商名称", sc.ship_to_code, sc.sap_code sold_to_code, po.order_no "订单编号",pl.sku sku, p.name "产品名称",--bp.[pack_type] sales_unit,
pl.actual_amount "下单数量", pl.free_amount "赠送数量", pl.total_value "总价", (isnull(pl.actual_amount,0)+isnull(pl.free_amount,0))*p.pack "总升数",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=1) "提交时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=3) "确认时间",
(select p.dic_item_name from wx_t_dic_item p where p.dic_type_code = 'PARTNER_ORDER_STATUS' and p.dic_item_code = po.status) as "状态"
				from wx_t_partner_order po 
				left join wx_t_partner_order_line pl on pl.partner_order_id=po.id
                --left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=pl.sku
				--left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
				left join wx_t_product p on p.sku=pl.sku
				left join wx_t_organization o on o.id=po.partner_id
				left join [PP_MID].[dbo].[mid_partner_sale_config] sc on po.partner_sale_config_id=sc.id
		where po.status not in (0,4) and po.create_time>'2020-01-01' --and po.order_no='P07211008005253'
		order by po.id desc		

select a.order_no "订单编号", a.partner_name "合伙人", a.source_meaning "订单来源", a.status_meaning "订单状态", 
a.total_liter_count "合计升数", a.create_time "创建时间" from(
			SELECT tt_order.id, tt_order.order_no, tt_order.order_type, tt_order.buy_user_no, tt_order.buy_user_name
				, tt_order.buyer_card_id, tt_order.invoice_info, tt_order.source, tt_dicitem.dic_item_name AS source_meaning, tt_order.source_id
				, tt_order.pay_type, pay_state, tt_order.total_product_price, tt_order.total_delivery_price, tt_order.total_order_price
				, tt_order.partner_id, tt_order.status, tt_order.create_time, tt_order.update_time, tt_order.creator
				, tt_order.remark, tt_order.delivery_time, tt_order.discount_fee, tt_order.discounted_total_order_price, tt_order.total_liter_count
				, (
					SELECT u.CH_NAME
					FROM wx_t_user u
					WHERE u.USER_ID = tt_order.creator
				) AS creator_display_name
				, (
					SELECT o.organization_name
					FROM wx_t_organization o
					WHERE o.id = tt_order.partner_id
				) AS partner_name
				, (
					SELECT p.dic_item_name
					FROM wx_t_dic_item p
					WHERE p.dic_type_code = 'PARTNER_ORDER_STATUS'
						AND p.dic_item_code = tt_order.status
				) AS status_meaning, tt_poe.partner_property
				, (
					SELECT di1.dic_item_name
					FROM wx_t_dic_item di1
					WHERE di1.dic_type_code = 'partner.partnerProperty'
						AND di1.dic_item_code = tt_poe.partner_property
				) AS partner_property_txt
			FROM wx_t_partner_order tt_order
				LEFT JOIN wx_t_organization tt_org ON tt_order.partner_id = tt_org.id
				LEFT JOIN wx_t_partner_o2o_enterprise tt_poe ON tt_poe.partner_id = tt_order.partner_id
				LEFT JOIN wx_t_dic_item tt_dicitem ON tt_dicitem.dic_item_code = tt_order.source
				LEFT JOIN (
					SELECT distributor_id, region
					FROM dw_access_control_customer_org_sales
					GROUP BY distributor_id, region
				) r
				ON tt_poe.distributor_id = r.distributor_id
			WHERE 1 = 1
				AND tt_dicitem.dic_type_code = 'PARTNER_ORDER_SOURCE'
				AND (tt_order.status != '0'
					OR (tt_order.status = '0'
						AND tt_order.creator = ''))) a
						order by a.partner_id, a.create_time desc
						
						
						
select o.organization_name "经销商名称", sc.ship_to_code, sc.sap_code sold_to_code, po.order_no "订单编号",pl.sku sku, p.name "产品名称",--bp.[pack_type] sales_unit,
pl.actual_amount "下单数量", pl.free_amount "赠送数量", pl.total_value "总价", (isnull(pl.actual_amount,0)+isnull(pl.free_amount,0))*p.pack "总升数",
po.create_time "创建时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=1) "提交时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=3) "确认时间",
(select p.dic_item_name from wx_t_dic_item p where p.dic_type_code = 'PARTNER_ORDER_STATUS' and p.dic_item_code = po.status) as "状态"
				from wx_t_partner_order po 
				left join wx_t_partner_order_line pl on pl.partner_order_id=po.id
                --left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=pl.sku
				--left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
				left join wx_t_product p on p.sku=pl.sku
				left join wx_t_organization o on o.id=po.partner_id
				left join [PP_MID].[dbo].[mid_partner_sale_config] sc on po.partner_sale_config_id=sc.id
		where po.status not in (0,4) --and po.create_time>'2020-01-01' --and po.order_no='P07211008005253'
		order by po.id desc		
						