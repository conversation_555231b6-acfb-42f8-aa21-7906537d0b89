/**
 * 表格编辑器
 * @param {Object} config (必选)表格编辑器信息
 * @public
 * @class 表格编辑器
 * <AUTHOR> 2011/9/19
 */
TableEditor = function(config){
	this.setConfig(config);
	this._init();
}

/**
 * 表格编辑器类型原型
 */
TableEditor.prototype = {
		html : '', //{String}编辑的表格的html
		renderTo : null, //{String/jQuery}表格编辑器所在的容器
		width : 0, //{integer}表格的宽度
		height : 0, //{integer}表格的高度
		minColWidth : 100, //{integer}表格列的最小宽度
		basePath : '', //{String}基路径
		
		table : null, //{TableEditor.Table}表格编辑器的表格
		colWidths : null, //{Array}表格所有列的宽度集合
		selectedCells : null, //{TableEditor.SelectedCells}选中的表格单元格集合
		
		element : null, //{jQuery}表格编辑器jQuery对象
		tableRuler : null, //{TableEditor.TableRuler}表格的标尺的jQuery对象
		$tableContainer : null, //{jQuery}编辑的表格所在的容器
		$colGroup : null, //{jQuery}表格的列集合，用此设定表格的列宽度属性
		__counter : 0, //{integer}分配实例的计算器
		_cells : null, //{Map}表格单元格容器，存放所有的未被合并的单元格对象
		
		ELEMENT_CLASS : 'table-editor', //表格编辑器类名
		/**
		 * 添加表格的右键菜单
		 * @param {Object} options (必选)添加的菜单信息
		 * @return {Menu1.Group.MenuItem} 创建的右键菜单项对象
		 * @public
		 */
		addContextMenu : function(options){
			var _p = null; //父菜单对象
			if(options['parentMenu']){
				_p = this.contextMenu.findMenuItem(options['parentMenu']);
				if(_p){
					return _p.createMenuItem(options['title'], options, options['groupName'], options['groupIndex']);
				}
			}
			return this.contextMenu.createMenuItem(options['title'], options, options['groupName'], options['groupIndex']);
		},
		/**
		 * 获取表格的HTML代码
		 * @return {String} 表格的HTML代码
		 * @public
		 */
		getHTML : function(){
			var $ = jQuery, thiz = this, _html = '<table><colgroup>';
			//定义列宽
			for(var i = 0; i < this.colWidths.length; i++){
				_html += '<col style="width: ' + this._format(100 * this.colWidths[i] / this.width) + '%;" />';
			}
			_html += '</colgroup>';
			
			//解析单元格
			
			//解析头部
			$(' > thead', this.$table).each(function(){
				_html += ('<thead>' + thiz.getCellHTML(this) + '</thead>');
			});
			//解析body
			$(' > tbody', this.$table).each(function(){
				_html += ('<tbody>' + thiz.getCellHTML(this) + '</tbody>');
			});
			//解析tfoot
			$(' > tfoot', this.$table).each(function(){
				_html += ('<tfoot>' + thiz.getCellHTML(this) + '</tfoot>');
			});
			//没有thead,tbody等元素封装时
			return _html + (thiz.getCellHTML(this.$table) + '</table>');
		},
		/**
		 * 获取指定页面元素下的所有单元格的HTML代码
		 * @param {HTMLObject/jQuery} dom (必选)处理的页面元素对象。如table,tbody等
		 * @return {String} 返回指定元素内部的HTML代码
		 * @private
		 */
		getCellHTML : function(dom){
			var $ = jQuery, thiz = this, _html = '';
			$('> tr', dom).each(function(){
				_html += '<tr>';
				$('> td, > th', this).each(function(){
					var _instanceId = $(this).attr('instanceId'), 
					_cell = thiz.getCell(_instanceId),
					_innerHTML = _cell.$tdContent.html();
					_html += ('<' + this.tagName + ' valign="top" widthPer="' + thiz._format(_cell.getWidth() * 100 / thiz.width) + '"');
					if(_cell.colSpan > 1){
						_html += (' colSpan="' + _cell.colSpan + '"');
					}
					if(_cell.rowSpan > 1){
						_html += (' rowSpan="' + _cell.rowSpan + '"');
					}
					//单元格内容
					if(_innerHTML == ''){
						_html += ('>&nbsp;</' + this.tagName + '>');
					}else{
						_html += ('>' + _innerHTML + '</' + this.tagName + '>');
					}
				});
				_html += '</tr>';
			});
			return _html;
		},
		/**
		 * 改变指定表格列的宽度
		 * @param {integer} colNumb (必选)改变宽度的列号
		 * @param {integer} increase (必选)列新增的宽度
		 * @return {void}
		 * @public
		 */
		changeColWidth : function(colNumb, increase){
			var _cols = [];//变动的表格列集合
			//指定列的新宽度
			this.colWidths[colNumb] = this.colWidths[colNumb] + increase;
			_cols.push(colNumb);
			if(colNumb != this.colWidths.length - 1){//指定改变宽度列的下一列减少增加的宽度
				var _w = this.colWidths[colNumb + 1] - increase; //指定列的下一列的新宽度
				if(_w >= this.minColWidth){
					increase = 0; //指定列新增宽度处理完成
					this.colWidths[colNumb + 1] = _w;
				}else{
					increase = this.colWidths[colNumb + 1] - this.minColWidth - increase; //还剩下的没有处理的宽度
					this.colWidths[colNumb + 1] = this.minColWidth;
				}
				_cols.push(colNumb + 1);
			}else{
				increase = increase * (-1);
			}
			if(increase != 0){ //指定列新增的宽度没有处理完成
				this.fixTableWidth(increase, _cols);
			}
			this.colwidthchanged(_cols);
		},
		/**
		 * 设置表格编辑的宽度。包括表格列宽度
		 * @param {integer} newWidth (必选)表格新的宽度
		 * @return {boolean} 操作是否成功。表格新宽度如果小于其最小宽度，操作不成功
		 * @public
		 */
		setWidth : function(newWidth){
			if(newWidth < this.colWidths.length * this.minColWidth){
				return false;
			}
			this.setTableWidth(newWidth);
			
			//设置单元格宽度
			var _newTotalWidth = 0, //初始设置的列宽的总宽度
				_cols = []; //变动的列号集合
			//初始设置每列宽度
			for(var i = 0; i < this.colWidths.length; i++){
				var _width = parseInt((this.colWidths[i] / this.width) * newWidth, 10);
				_width = (_width > this.minColWidth ? _width : this.minColWidth);
				if(this.colWidths[i] != _width){ //列宽度变动
					this.colWidths[i] = _width
					_cols.push(i);
				}
				_newTotalWidth += _width;
			}
			//适当调整每列的宽度
			if(newWidth != _newTotalWidth){
				this.fixTableWidth(newWidth - _newTotalWidth, _cols);
			}
			this.colwidthchanged(_cols);
		},
		/**
		 * 设置表格的宽度。只设置表格宽度和编辑器的宽度，不处理表格列宽
		 * @param {integer} newWidth (必选)表格新的宽度
		 * @return {void}
		 * @public
		 */
		setTableWidth : function(newWidth){
			//设置表格宽度
			this.element.css('width', newWidth + 'px');
			this.$table.css('width', newWidth + 'px');
			this.width = newWidth;
			this.fireEvent('tablewidthchanged', newWidth);
		},
		/**
		 * 调整表格列宽度，将表格宽度的误差平均到每列中
		 * @param {integer} discrepancy (必选)表格的宽度与所有列的总宽度的差距
		 * @param {Array} changedCols (必选)调整列宽度过程中，列宽度发生变化的列存放地
		 * @return {void}
		 * @public
		 */
		fixTableWidth : function(discrepancy, changedCols){
			//var _step = parseInt(discrepancy / this.colWidths.length, 10); //调整列宽步长
			//_step = (_step == 0 ? (discrepancy > 0 ? 1 : -1) : _step);
			var _totalWidth = this.width - discrepancy, tempDiscrepancy = discrepancy;
			//每列宽度加上调整步长
			for(var i = 0; i < this.colWidths.length; i++){
				var _step = parseInt((this.colWidths[i] / _totalWidth) * tempDiscrepancy);
				_step = (_step == 0 ? (discrepancy > 0 ? 1 : -1) : _step);
				
				var _width = this.colWidths[i] + _step;
				_width = (_width > this.minColWidth ? _width : this.minColWidth);
				if(_width != this.colWidths[i]){//列宽有变动
					discrepancy -= (_width - this.colWidths[i]);
					this.colWidths[i] = _width;
					changedCols.push(i);
					if(discrepancy == 0){ //列总宽度与表格宽度相同
						return;
					}
				}
			}
			//还有差异，继续调整
			this.fixTableWidth(discrepancy, changedCols);
		},
		/**
		 * 以指定坐标区域，更新表格的选中区域
		 * @param {Array} points (必选)选中区域的坐标集合
		 * @return {void}
		 * @public
		 */
		updateSelectedCells : function(points){
			var _fc = this.table.cellAt(points[0].x, points[0].y), //第一个点所在的单元格
				_bc = _fc.getColNumb(), //选中区域的起始列号
				_ec = _bc + _fc.colSpan - 1, //选中区域的终止列号
				_br = _fc.row.getRowNumb(), //选中区域的起始行号
				_er = _br + _fc.rowSpan - 1; //选中区域的终止行号
			//确定选中区域的边界
			for(var i = 1; i < points.length; i++){
				var _c = this.table.cellAt(points[i].x, points[i].y), //当前点所在单元格
					_cn = _c.getColNumb(), //单元格的列号
					_rn = _c.row.getRowNumb(); //单元格的行号
				_bc = Math.min(_bc, _cn);
				_ec = Math.max(_ec, _cn + _c.colSpan - 1);
				_br = Math.min(_br, _rn);
				_er = Math.max(_er, _rn + _c.rowSpan - 1);
			}
			this.getSelectedCells().setSelectedCells(_bc, _ec, _br, _er);
		},
		/**
		 * 获取选中单元格区域对象
		 * @return {TableEditor.SelectedCells} 编辑器的选中单元格区域对象
		 * @private
		 */
		getSelectedCells : function(){
			if(this.selectedCells == null){
				//创建选中单元格区域
				this.selectedCells = new TableEditor.SelectedCells(this);
				//销毁选中单元格区域
				this.addListener('beforedestroy', function(){
					this.selectedCells.destroy();
					delete this.selectedCells;
				})
			}
			return this.selectedCells;
		},
		/**
		 * 将表格单元格交给编辑器统一管理
		 * @param {TableEditor.Table.Row.Cell} cell (必选)添加的表格单元格
		 * @return {void}
		 * @public
		 */
		addCell : function(cell){
			this._cells[cell.instanceId] = cell;
			cell.addListener('beforedestroy', function(){
				this.tableEditor.removeCell(this);
			});
		},
		/**
		 * 移除表格单元格对象
		 * @param {TableEditor.Table.Row.Cell} cell (必选)移除的表格对象
		 * @return {void}
		 * @public
		 */
		removeCell : function(cell){
			delete this._cells[cell.instanceId];
		},
		/**
		 * 获取指定实例ID的表格单元格对象
		 * @param {String} instanceId (必选)指定的单元格的实例ID
		 * @return {TableEditor.Table.Row.Cell} 返回指定实例ID的表格单元格对象
		 * @public
		 */
		getCell : function(instanceId){
			return this._cells[instanceId];
		},
		/**
		 * 向表格列宽集合中压入宽度
		 * @param {Object} options (必选)宽度属性
		 * @return {void}
		 * @private
		 */
		pushColWidth : function(options){
			if(options.isNumb){ //是具体宽度
				this.colWidths[options.colNumb] = options.width;
			}else if(!this.colWidths[options.colNumb]){
				//新建抽象列宽
				this.colWidths[options.colNumb] = new TableEditor.ColWidth(this, options.colNumb, options.widthGroup, options.totalWidth);
			}else if(this.colWidths[options.colNumb].clazz == 'TableEditor.ColWidth'){
				//在抽象列宽中添加组
				this.colWidths[options.colNumb].addGroup(options.widthGroup, options.totalWidth);
			}
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			
			this.contextMenu.destroy();
			delete this.contextMenu;
			
			jQuery(document).unbind('click', this._selectListener);
			delete this._selectListener;
			
			this.table.destroy();
			delete this.table;
			
			this.tableRuler.destroy();
			delete this.tableRuler;
			
			this.$tableContainer.remove();
			delete this.$tableContainer;
			
			this.element.remove();
			delete this.element;
			
			delete this.renderTo;
			delete this._cells;
			this.fireEvent('afterdestroy');
		},
		/**
		 * 初始化表格编辑器
		 * @return {void}
		 * @private
		 */
		_init : function(){
			var $ = jQuery;
			//处理渲染容器
			if(!this.renderTo || this.renderTo == ''){
				throw "TableEditor's renderTo property can't be null!";
			}else if(typeof this.renderTo == 'string'){
				this.renderTo = $('#' + this.renderTo);
			}
			//初始化表格单元格容器
			this._cells = {};
			this.colWidths = [];
		
			this.element = $('<div class="' + this.ELEMENT_CLASS + '" style="width: ' + this.width + 'px;"/>').appendTo(this.renderTo);
			//编辑的表格
			this.$table = $(this.html).css({
				'width': this.width + 'px',
				'height' : this.height + 'px',
				'border-collapse' : 'collapse'
			}).attr({
				'cellpadding' : 0,
				'cellspacing' : 0
			});
			this.$tableContainer = $('<div class="table-container"/>').append(this.$table).appendTo(this.element);
			
			var thiz = this, _hasColGroup = false; //表格是否有定义列宽的列集合对象
			this.$colGroup = this.$table.find('colgroup');
			if(this.$colGroup.length == 1){
				$('col', this.$colGroup).each(function(){
					var $this = $(this), 
					_width = parseInt(thiz.width * parseInt($this.css('width'), 10) / 100); //表格列宽
					thiz.colWidths.push(_width);
					$this.css('width', _width);
				}); 
				_hasColGroup = true;
			}
			//解析表格
			this.processTable(_hasColGroup);
			
			if(!_hasColGroup){//表格没有定义列宽的列集合对象，解析表格列宽度，并定义列宽的列集合对象
				this.processWidth();
				this.$colGroup = $('<colgroup/>').prependTo(this.$table);
				for(var i = 0; i < this.colWidths.length; i++){
					this.$colGroup.append('<col style="width: ' + this.colWidths[i] + 'px;" />');
				}
			}
			
			var _totalWidth = 0; //表格总宽度
			for(var i = 0; i < this.colWidths.length; i++){
				_totalWidth += this.colWidths[i];
			}
			if(_totalWidth != this.width){ //调整表格宽度
				var _cols = []; //变动的列宽度
				this.fixTableWidth(this.width - _totalWidth, _cols);
				this.colwidthchanged(_cols);
			}
			//表格标尺
			this.tableRuler = new TableEditor.TableRuler(this, this.width);
			this.tableRuler.element.prependTo(this.element);
			//创建右键菜单
			this.contextMenu = this.$table.contextMenu1({
				basePath: this.basePath,
				themePath : this.themePath,
				menuInfo : {
					groupInfos: [{
						menuItemInfos : [{
							name : 'insertcolleft',
							title: '左侧插入列',
							index: 1,
							onclick : function(){
								thiz.getSelectedCells().insertColLeft();
								thiz.__documentNoClick = true; //不监听页面的click事件
							}
						}, {
							name : 'insertcolright',
							title: '右侧插入列',
							index: 2,
							onclick : function(){
								thiz.getSelectedCells().insertColRight();
								thiz.__documentNoClick = true; //不监听页面的click事件
							}
						}, {
							name : 'insertrowlast',
							title: '上方插入行',
							index: 3,
							onclick : function(){
								thiz.getSelectedCells().insertRowLast();
								thiz.__documentNoClick = true; //不监听页面的click事件
							}
						}, {
							name : 'insertrownext',
							title: '下方插入行',
							index: 4,
							onclick : function(){
								thiz.getSelectedCells().insertRowNext();
								thiz.__documentNoClick = true; //不监听页面的click事件
							}
						}, {
							name : 'mergecells',
							title: '合并单元格',
							index: 5,
							onclick : function(){
								thiz.getSelectedCells().mergeCells();
								thiz.__documentNoClick = true; //不监听页面的click事件
							},
							listeners : [{
								beforeshow : function(){
		    						if(thiz.getSelectedCells().isMergable()){ //选中区域可合并的
		    							this.setDisable(false);
		    						}else{
		    							this.setDisable(true);
		    						}
		    					}
							}]
						}, {
							name : 'splitcol',
							title: '拆分成两列',
							index: 6,
							onclick : function(){
								thiz.getSelectedCells().splitCol();
								thiz.__documentNoClick = true; //不监听页面的click事件
							},
							listeners : [{
								beforeshow : function(){
		    						if(thiz.getSelectedCells().isSplitable()){ //选中区域可拆分的
		    							this.setDisable(false);
		    						}else{
		    							this.setDisable(true);
		    						}
		    					}
							}]
						}, {
							name : 'splitrow',
							title: '拆分成两行',
							index: 7,
							onclick : function(){
								thiz.getSelectedCells().splitRow();
								thiz.__documentNoClick = true; //不监听页面的click事件
							},
							listeners : [{
								beforeshow : function(){
		    						if(thiz.getSelectedCells().isSplitable()){ //选中区域可拆分的
		    							this.setDisable(false);
		    						}else{
		    							this.setDisable(true);
		    						}
		    					}
							}]
						}, {
							name : 'deletecol',
							title: '删除列',
							index: 8,
							onclick : function(){
						        if (confirm('删除单元格内的组件将会被移除！您确认要删除选中单元格所在列？')) {
									thiz.getSelectedCells().deleteCols();
						        }
								thiz.__documentNoClick = true; //不监听页面的click事件
							},
							listeners : [{
								beforeshow : function(){
		    						if(thiz.getSelectedCells().isColDeletable()){ //可删除选中列的
		    							this.setDisable(false);
		    						}else{
		    							this.setDisable(true);
		    						}
		    					}
							}]
						}, {
							name : 'deleterow',
							title: '删除行',
							index: 9,
							onclick : function(){
						        if (confirm('删除单元格内的组件将会被移除！您确认要删除选中单元格所在行？')) {
									thiz.getSelectedCells().deleteRows();
						        }
								thiz.__documentNoClick = true; //不监听页面的click事件
							},
							listeners : [{
								beforeshow : function(){
		    						if(thiz.getSelectedCells().isRowDeletable()){ //可删除选中列的
		    							this.setDisable(false);
		    						}else{
		    							this.setDisable(true);
		    						}
		    					}
							}]
						}]
					}]
				}
			}, function(e){ //弹出菜单前事件
				var _selectedCell = thiz.table.cellAt(e.pageX, e.pageY), //右键事件所在单元格
					_cn = _selectedCell.getColNumb(), //单元格所在列号
					_rn = _selectedCell.row.getRowNumb(); //单元格所在的行号
				if(!thiz.getSelectedCells().inSelectedCells(_cn, _rn)){
					thiz.getSelectedCells().setSelectedCells(_cn, _cn + _selectedCell.colSpan - 1, _rn, _rn + _selectedCell.rowSpan - 1);
				}
			});
			
			//表格绑定拖拽选中事件
			this.$table.bind('mousedown', function(e){
				if((jQuery.browser.msie && e.button == 1) ||
	            		(!jQuery.browser.msie && e.button == 0)){
					thiz.mouseStart(e);
					e.preventDefault();
				}
				return true;
			});
			
			//单击表格外面，取消表格单元格的选中
			this._selectListener = function(e){
				if(!thiz.__documentNoClick && (jQuery.browser.msie || e.button == 0)){
					var _cell = thiz.getCell($(e.target).parents('td[instanceId]:first').attr('instanceId')); //鼠标下的单元格
					if(_cell == null){
						thiz.getSelectedCells().cancelSelectCells();
					}else{
						var _cn = _cell.getColNumb(), //单元格的起始列号
						_rn = _cell.row.getRowNumb(); //单元格的起始行号
						thiz.getSelectedCells().setSelectedCells(_cn, _cn + _cell.colSpan - 1, _rn, _rn + _cell.rowSpan - 1);
					}
				}
				thiz.__documentNoClick = false; //监听页面的click事件
			};
			$(document).bind('click', this._selectListener);
			this.getSelectHelper().hide();
		},
		/**
		 * 表格列宽发生变化
		 * @param {Array} cols (必选)列宽发生变化的列号的集合
		 * @return {void}
		 * @public
		 */
		colwidthchanged : function(cols){
			var thiz = this;
			$('col', this.$colGroup).each(function(i){
				$(this).css('width', thiz.colWidths[i] + 'px');
			});
			this.fireEvent("colwidthchanged", cols);
		},
		/**
		 * 解析表格
		 * @param {boolean} hasColGroup (必选)是否有定义列宽对象，没有则解析表格时，需要解析表格的宽度
		 * @return {void} 
		 * @private
		 */
		processTable : function(hasColGroup){
			var thiz = this, $ = jQuery,
				_rowSize = 0, //表格的行数
				_colSize = 0, //表格的列数
				_nextColNumb = 0, //下一个解析的单元格的列号
				_cellInfosMap = {}, //表格单元格信息集合映射表
				_rowElementsMap = {}; //表格行元素集合映射表
			$('tr', thiz.$table).each(function(i){
				_rowElementsMap[i] = $(this);
				$('td, th', this).each(function(){
					var _colSpan = this.colSpan, //列跨度
						_rowSpan = this.rowSpan, //行跨度
						_widthPer = parseInt($(this).css('width'), 10), //单元格宽度百分比
						_instanceId = thiz.newInstanceId(); //页面分给单元格的实例ID
					
					if(!hasColGroup && !isNaN(_widthPer) && _widthPer > 0){
						var	_width = _widthPer * thiz.width / 100; //单元格宽度
						
						//处理单元格宽度
						if(_colSpan == 1){ //单列单元格，即单元格没有列的合并
							//压入具体宽度
							thiz.pushColWidth({
								width : _width,
								colNumb: _nextColNumb,
								isNumb : true
							});
						}else{
							for(var a = 0; a < _colSpan; a++){
								var _widthGroup = []; //每列抽象宽度的宽度组
								for(var b = 0; b < _colSpan; b++){
									if(a != b){
										_widthGroup.push(_nextColNumb + b);
									}
								}
								//压入抽象宽度
								thiz.pushColWidth({
									totalWidth : _width,
									colNumb: _nextColNumb + a,
									isNumb : false,
									widthGroup : _widthGroup
								});
							}
						}
					}
					
					//解析单元格信息
					for(var a = 0; a < _rowSpan; a++){
						for(var b = 0; b < _colSpan; b++){
							if(a == 0 && b == 0){ //合并后的单元格
								_cellInfosMap[i + '-' + _nextColNumb] = {
										instanceId : _instanceId,
										autoInstanceId : false,
										element: jQuery(this).css('width', '').attr('instanceId', _instanceId),
										colSpan: _colSpan,
										rowSpan : _rowSpan
								};
							}else{//被合并的单元格
								_cellInfosMap[(i + a) + '-' + (_nextColNumb + b)] = {
										mergedInstanceId : _instanceId,
										available: false,
										mergedCell : function(){
											return this.tableEditor.getCell(this.mergedInstanceId);
										}
								};
							}
						}
					}
					//更新表格行数和列数
					_rowSize = Math.max(_rowSize, i + _rowSpan);
					_colSize = Math.max(_colSize, _nextColNumb + _colSpan);
					//设置下一个单元格的列号
					_nextColNumb += _colSpan;
				});
				//重置下一个单元格列号
				_nextColNumb = 0;
			});
			//初始表格行信息
			var _rowInfos = []; //表格的行信息集合
			for(var i = 0; i < _rowSize; i++){
				var _cellInfos = []; //行内的单元格信息集合
				for(var j = 0; j < _colSize; j++){
					_cellInfos.push(_cellInfosMap[i + '-' + j]);
				}
				_rowInfos.push({
					cellInfos : _cellInfos,
					element : _rowElementsMap[i]
				});
			}
			//创建表格
			thiz.table = new TableEditor.Table({
				tableEditor : thiz,
				element : thiz.$table,
				rowInfos : _rowInfos
			});
		},
		/**
		 * 解析表格列宽度
		 * @return {void}
		 * @private
		 */
		processWidth : function(){
			var forceWidth = false, //强制解析出宽度。按抽象宽度中的列分组平分宽度
				finished = true; //解析宽度完成
			do{
				var updated = false; //解析过程中是否发生改变
				finished = true;
				for(var i = 0; i < this.colWidths.length; i++){
					if(this.colWidths[i].clazz == 'TableEditor.ColWidth'){
						finished = false;
						if(this.colWidths[i].countWidth(forceWidth)){
							updated = true;
						}
					}
				}
				if(!updated){ //强制平分
					forceWidth = true;
				}
			}while(!finished)
		},
		/**
		 * 分配实例ID
		 * @return {string} 生成的页面唯一ID
		 * @public
		 */
		newInstanceId : function(){
			return 'td' + new Date().getTime() + this.__counter++;
		},
		/**
		 * 表格拖拽选中单元格的起始事件处理
		 * @param {event} e (必选)触发的页面事件对象
		 * @return {void}
		 * @private
		 */
		mouseStart : function(e){
			this.mouseStop();
			if(!this.___noCellSelect){
				this._mouseStarted = true;
			}
		},
		/**
		 * 表格拖拽选中单元格的过程中事件处理
		 * @param {event} e (必选)触发的页面事件对象
		 * @return {void}
		 * @private
		 */
		mouseDrag : function(e){
			if(this._mouseStarted && !this._showHelper){
				this.getSelectHelper().show({x:e.pageX, y:e.pageY});
				this._showHelper = true;
				if(jQuery.browser.msie){ //IE在拖动过程中也会触发页面的click事件
					this.__documentNoClick = true;
				}
				return e.preventDefault();
			}else if(this._mouseStarted){
				this.getSelectHelper().updateEndPoint({x:e.pageX, y:e.pageY});
				this.updateSelectedCells(this.getSelectHelper().getAllPoints());
				return e.preventDefault();
			}
		},
		/**
		 * 表格拖拽选中单元格的完成事件处理
		 * @param {event} e (可选)触发的页面事件对象
		 * @return {void}
		 * @private
		 */
		mouseStop : function(e){
			if(this._mouseStarted){
				this.getSelectHelper().hide();
				this._mouseStarted = false;
				this._showHelper = false;
			}
		},
		/**
		 * 获取鼠标拖拽的帮助对象
		 * @return {TableEditor.SelectHelper} 鼠标拖拽的帮助对象
		 * @private
		 */
		getSelectHelper : function(){
			if(!this.selectHelper){
				this.selectHelper = new TableEditor.SelectHelper(this, this.$tableContainer);
				var thiz = this, $ = jQuery,
				_mouseMoveListener = function(e){ //页面mousemove事件监听
					return thiz.mouseDrag(e);
				},_mouseUpListener = function(e){//页面mouseup事件监听
					thiz.mouseStop(e);
				};
				$(document).bind('mousemove', _mouseMoveListener).bind('mouseup', _mouseUpListener);
				//销毁对象
				this.addListener('beforedestroy', function(){
					$(document).unbind('mousemove', _mouseMoveListener).unbind('mouseup', _mouseUpListener);
					this.mouseStop();
					this.selectHelper.destroy();
					delete this.selectHelper;
				});
			}
			return this.selectHelper;
		},
	    /**
	     * 注册事件
	     * @param {String} eventName 事件名称,大小写任意,将自动转化为小写存储
	     * @param {Function} callBack 事件触发函数
	     */
	    addListener: function(eventName, callBack){
	        if (eventName) {
	            if (!this._eventCalls) {
	                this._eventCalls = {};
	                this.addListener('afterdestroy', function(){
	                	delete this._eventCalls;
	                });
	            }
	            var eventName = eventName.toLowerCase();
	            if (this._eventCalls[eventName]) {
	                this._eventCalls[eventName].push(callBack);
	            }
	            else {
	                this._eventCalls[eventName] = [callBack];
	            }
	        }
	    },
	    /**
	     * 移除事件监听。
	     * @param {String} eventName 事件名称
	     * @param {Function} callBack 事件监听回调函数
	     * @return 移除的事件监听回调函数
	     */
	    removeListener: function(eventName, callBack){
	        if (this._eventCalls && eventName) {
	            var calls = this._eventCalls[eventName.toLowerCase()];
	            if (calls) {
	                for (var i = 0; i < calls.length; i++) {
	                    if (calls[i] == callBack) {
	                        return calls.splice(i, 1)[0];
	                    }
	                }
	            }
	        }
	        return null;
	    },
	    /** 
	     * 触发事件 先调用系统本身的事件,如 beforerender，然后调用，调用由addListener添加的事件，当遇到事件的返回值为false[Boolean]时，停止继续调用。
	     * @param {String} eventName 事件名称，大小写任意，将自动转化为小写调用
	     * @param {Object..} 需要传递到触发事件的参数
	     * @private
	     */
	    fireEvent: function(){
	        var _success = true;
	        if (arguments[0]) {
	        	var eventName = arguments[0].toLowerCase();
	            if (this._eventCalls) {
	                var calls = this._eventCalls[eventName];
	                if (calls) {
	                    for (var i = 0; i < calls.length; i++) {
	                        _success = calls[i].apply(this, Array.prototype.slice.call(arguments, 1));
	                        if (_success === false) {
	                            return false;
	                        }
	                    }
	                }
	            }
	        }
	        return _success != false;
	    },
		/**
		 * 设置当前对象的配置信息。将属性赋给当前对象，事件(listeners)赋给当前对象的事件中心
		 * @param {object} config (可选)传入的配置信息
		 */
		setConfig : function(config){
			if(config){
				for(var k in config){
					if(k == 'listeners'){//将配置中的事件监听添加到事件控制中心
						if(config['listeners']){
							for(var i = 0; i < config['listeners'].length; i++){
								for(var k1 in config['listeners'][i]){
									this.addListener(k1, config['listeners'][i][k1]);
								}
							}
						}
					}else{ //将配置中的非事件监听属性添加到当前对象中
						this[k] = config[k];
					}
				}
			}
		},
		/**
		 * 合并对象
		 * @param {object} a (必选)被合并对象
		 * @param {object} b (必选)合并的对象
		 * @return {object} 合并后的新的对象
		 * @public
		 */
		merge : function(a, b){
	    	if(b){
	    		a = a || {};
	    		for(var k in b){
	    			a[k] = b[k];
	    		}
	    	}
	    	return a;
		},
		/**
		 * 格式化数字，保留2位小数
		 * @param {float} numb (必选)需要格式化的数字
		 * @return {float} 四舍五入，保留2位小数点的数字
		 * @private
		 */
		_format : function(numb){
			return Math.round(numb * 100) / 100;
		}
}

/**
 * 选中表格单元格集合类型
 * @param {TableEditor} tableEditor (必选)表格编辑器
 * @class 选中表格单元格集合类型
 * @constructor
 * <AUTHOR> 2011/9/27
 */
TableEditor.SelectedCells = function(tableEditor){
	var thiz = this;
	this.tableEditor = tableEditor;
	//监听列宽改变
	tableEditor.addListener('colwidthchanged', function(cols){
		if(thiz._needChangeRuler(cols)){
			thiz.updateRuler();
		}
	});
}

/**
 * 选中表格单元格集合类型原型
 */
TableEditor.SelectedCells.prototype = {
		beginColNumb : -1, //{integer}起始列号
		endColNumb : -1, //{integer}终止列号
		beginRowNumb : -1, //{integer}起始行号
		endRowNumb : -1, //{integer}终止行号
		tableEditor : null, //{TableEditor}所属表格编辑器
		cellRuler : null, //{TableEditor.CellRuler}选中区域的单元格标尺对象
		/**
		 * 通过设置选中的区域，设置选中的单元格集合
		 * @param {integer} beginColNumb (必选)选中区域的起始列号
		 * @param {integer} endColNumb (必选)选中区域的终止列号
		 * @param {integer} beginRowNumb (必选)选中区域的起始行号
		 * @param {integer} endRowNumb (必选)选中区域的终止行号
		 * @return {void}
		 * @public
		 */
		setSelectedCells : function(beginColNumb, endColNumb, beginRowNumb, endRowNumb){
			//选中区域没有变化
			if(this.tableEditor.___noCellSelect || 
					(beginColNumb == this.beginColNumb && endColNumb == this.endColNumb && 
					beginRowNumb == this.beginRowNumb && endRowNumb == this.endRowNumb)){
				return;
			}
			
			var _oldBCN = this.beginColNumb, //旧起始列号
				_oldECN = this.endColNumb,  //旧终止列号
				_oldBRN = this.beginRowNumb, //旧起始行号
				_oldERN = this.endRowNumb; //旧终止行号
			//选中选中区域内的所有单元格
			this.updateSelectedCells(beginColNumb, endColNumb, beginRowNumb, endRowNumb);
			//取消选中非选中区域内的单元格
			if(_oldBCN != -1){
				for(var i = _oldBCN; i <= _oldECN; i++){
					for(j = _oldBRN; j <= _oldERN; j++){
						if(!this.inSelectedCells(i, j)){
							this.tableEditor.table.getCell(j, i).select(false);
						}
					}
				}
			}
			//更新选中单元格标尺
			this.updateRuler();
		},
		/**
		 * 更新并确认选中区域，即将选中区域的单元格
		 * @param {integer} beginColNumb (必选)选中区域的起始列号
		 * @param {integer} endColNumb (必选)选中区域的终止列号
		 * @param {integer} beginRowNumb (必选)起始行号
		 * @param {integer} endRowNumb (必选)终止行号
		 * @return {void}
		 * @private
		 */
		updateSelectedCells : function(beginColNumb, endColNumb, beginRowNumb, endRowNumb){
			if(beginColNumb != -1){
				for(var i = beginColNumb; i <= endColNumb; i++){
					for(var j = beginRowNumb; j <= endRowNumb; j++){
						var _cell = this.tableEditor.table.getCell(j, i);
						//选中区域最左侧的单元格
						if(i == beginColNumb && _cell.mergedCell && _cell.mergedCell.getColNumb() != _cell.getColNumb()){
							this.updateSelectedCells(_cell.mergedCell.getColNumb(), endColNumb, beginRowNumb, endRowNumb);
							return;
						}
						//选中区域最上方的单元格
						if(j == beginRowNumb && _cell.mergedCell && _cell.mergedCell.row != _cell.row){
							this.updateSelectedCells(beginColNumb, endColNumb, _cell.mergedCell.row.getRowNumb(), endRowNumb);
							return;
						}
						//选中区域最右侧的单元格
						if(i == endColNumb){
							if(_cell.mergedCell && (_cell.mergedCell.getColNumb() + _cell.mergedCell.colSpan - 1 > endColNumb)){
								this.updateSelectedCells(beginColNumb, _cell.mergedCell.getColNumb() + _cell.mergedCell.colSpan - 1, beginRowNumb, endRowNumb);
								return;
							}else if(!_cell.mergedCell && _cell.colSpan > 1){
								this.updateSelectedCells(beginColNumb, _cell.getColNumb() + _cell.colSpan - 1, beginRowNumb, endRowNumb);
								return;
							}
						}
						//选中区域最下方的单元格
						if(j == endRowNumb){
							if(_cell.mergedCell && (_cell.mergedCell.row.getRowNumb() + _cell.mergedCell.rowSpan - 1 > endRowNumb)){
								this.updateSelectedCells(beginColNumb, endColNumb, beginRowNumb, _cell.mergedCell.row.getRowNumb() + _cell.mergedCell.rowSpan - 1);
								return;
							}else if(!_cell.mergedCell && _cell.rowSpan > 1){
								this.updateSelectedCells(beginColNumb, endColNumb, beginRowNumb, _cell.row.getRowNumb() + _cell.rowSpan - 1);
								return;
							}
						}
						
						//设置单元格选中
						_cell.select(true);
					}
				}
			}
			//最终确定选中区域
			this.beginColNumb = beginColNumb;
			this.endColNumb = endColNumb;
			this.beginRowNumb = beginRowNumb;
			this.endRowNumb = endRowNumb;
		},
		/**
		 * 取消选中单元格
		 * @return {void}
		 * @public
		 */
		cancelSelectCells : function(){
			this.setSelectedCells(-1, -1, -1, -1);
		},
		/**
		 * 表格选中区域宽度
		 * @return {integer} 表格选中区域宽度
		 * @public
		 */
		getWidth : function(){
			var _width = 0;
			for(var i = this.beginColNumb; i <= this.endColNumb; i++){
				_width += this.tableEditor.colWidths[i];
			}
			return _width;
		},
		/**
		 * 获取选中区域的最小宽度，即选中区域中的每一列的最小宽度之和
		 * @return {integer} 选中区域的最小宽度
		 */
		getMinWidth : function(){
			var _minWidth = 0;
			for(var i = this.beginColNumb; i < this.endColNumb; i++){
				_minWidth += this.tableEditor.colWidths[i];
			}
			return _minWidth + this.tableEditor.minColWidth;
		},
		/**
		 * 获取选中区域的最大宽度。选中区域的宽度+其能从下一列中获取的宽度。注：选中区域不包含最后一列
		 * @return {integer} 选中区域的最大宽度
		 * @public
		 */
		getMaxWidth : function(){
			if(this.endColNumb + 1 == this.tableEditor.colWidths.length){ //选中区域包含最后1列
				return this.getWidth();
			}
			return this.getWidth() + this.tableEditor.colWidths[this.endColNumb + 1] - this.tableEditor.minColWidth;
		},
		/**
		 * 增加选择区域最后一列的宽度
		 * @param {integer} increase (必选)选中区域最后一列的新增宽度
		 * @return {void}
		 * @public
		 */
		changeColWidth : function(increase){
			this.tableEditor.changeColWidth(this.endColNumb, increase);
		},
		/**
		 * 获取选中区域是否可以设置宽度。选中区域的最后一列不是编辑的表格的最后一列。可设置宽度时，单元格标尺可以设置尺码
		 * @return {boolean} 选中区域是否可以设置宽度
		 * @public
		 */
		isResizable : function(){
			return this.endColNumb < this.tableEditor.colWidths.length - 1;
		},
		/**
		 * 获取选中区域是否可拆分的。选中区域为单个单元格时，是可拆分行或列的。
		 * @return {boolean} 选中区域是否可拆分的（拆分行或列）
		 * @public
		 */
		isSplitable : function(){
			var _selectCell = this.tableEditor.table.getCell(this.beginRowNumb, this.beginColNumb); //选中区域的左上角单元格
			return (_selectCell.colSpan == this.endColNumb - this.beginColNumb + 1) && 
				(_selectCell.rowSpan == this.endRowNumb - this.beginRowNumb + 1);
		},
		/**
		 * 获取选中区域是否可合并的。选中区域不为单个单元格时，是可合并行和列的。
		 * @return {boolean} 选中区域是否可合并的（合并行和列）
		 * @public
		 */
		isMergable : function(){
			return !this.isSplitable();
		},
		/**
		 * 获取选中区域是否可删除列的。选中区域没有包含所有的列。
		 * @return {boolean} 选中区域是否可删除列的
		 * @public
		 */
		isColDeletable : function(){
			return this.beginColNumb != 0 || (this.endColNumb != this.tableEditor.colWidths.length - 1);
		},
		/**
		 * 获取选中区域是否可删除行的。选中区域没有包含所有的哈根。
		 * @return {boolean} 选中区域是否可删除行的
		 * @public
		 */
		isRowDeletable : function(){
			return this.beginRowNumb != 0 || (this.endRowNumb != this.tableEditor.table.rows.length - 1);
		},
		/**
		 * 指定单元格是否在选中的单元格集合中
		 * @param {integer} colNumb (必选)指定判断单元格的列号
		 * @param {integer} rowNumb (必选)指定判断单元格的行号
		 * @return {boolean} 指定单元格是否在当前选中单元格中。true-指定单元格在选中单元格集合中
		 * @public
		 */
		inSelectedCells : function(colNumb, rowNumb){
			return colNumb <= this.endColNumb && colNumb >= this.beginColNumb && 
					rowNumb <= this.endRowNumb && rowNumb >= this.beginRowNumb;
		},
		/**
		 * 向选中区域的左边插入列
		 * @return {void}
		 * @public
		 */
		insertColLeft : function(){
			//插入列位置
			var _newColNumb = this.beginColNumb;
			//插入列
			this.tableEditor.table.insertCol(_newColNumb, function(rowNumb, colNumb){
				var $ = jQuery,
					_element = null, //单元格的jQuery对象
					_mergedCell = null, //合并后的单元格
					_row = this.rows[rowNumb], //插入元素所在行对象
					_nextCell = _row.cells[colNumb]; //插入元素的后一个元素
				
				if(_nextCell.available || (_nextCell.mergedCell.getColNumb() == colNumb + 1)){ //插入元素的同行后一个元素没有被合并
					var _nextElement = (_nextCell.available ? 
							_nextCell.element : _nextCell.mergedCell.element);//上一个元素
					_element = $('<' + _nextElement.attr('tagName') + ' />').html('').css('height', '20px');
				}else{//插入元素的后一个元素被合并，插入元素也会被合并
					_mergedCell = _nextCell.mergedCell;
					if(_mergedCell.row == _row){ //插入需合并元素与合并后元素同一行
						_mergedCell.setColSpan(_mergedCell.colSpan + 1);
					}
				}
				return {
					element : _element,
					mergedCell : _mergedCell,
					available : _mergedCell == null
				};
			});
			//调整宽度
			var _cols = [], //改变宽度的列集合
				_width = this.tableEditor.minColWidth, //新插入列的宽度
				_resizeTableWidth = true; //是否需要重新设置表格的宽度。表格所有列的最低宽度之和大于表格总宽度
			if((this.tableEditor.colWidths.length + 1) * this.tableEditor.minColWidth <= this.tableEditor.width){
				_width = this.getWidth();
				_resizeTableWidth = false;
			}
			//选中区域改变
			this.setSelectedCells(this.beginColNumb + 1, this.endColNumb + 1, this.beginRowNumb, this.endRowNumb);
			//插入一列宽度为列最小宽度来占位新插入列的宽度。在调整表格列宽度的时候因其已经最小，所有不会再减少宽度了
			this.tableEditor.colWidths.splice(_newColNumb, 0, _width);
			if(!_resizeTableWidth){ //能保证插入列后，每列的宽度都不小于最小宽度
				this.tableEditor.fixTableWidth(_width * (-1), _cols);
			}else{ //不能保证时，新插入的宽度为列最小宽度，并且表格的宽度新增最小列宽度。
				//改变表格总宽度
				this.tableEditor.setTableWidth(this.tableEditor.width + _width);
			}
			_cols.push(_newColNumb);
			//激活表格列宽度改变事件
			this.tableEditor.colwidthchanged(_cols);
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 向选中区域的右边插入列
		 * @return {void}
		 * @public
		 */
		insertColRight : function(){
			var _newColNumb = this.endColNumb + 1;
			//插入列
			this.tableEditor.table.insertCol(_newColNumb, function(rowNumb, colNumb){
				var $ = jQuery,
					_element = null, //单元格的jQuery对象
					_mergedCell = null, //合并后的单元格
					_row = this.rows[rowNumb], //插入元素所在行对象
					_nextCell = _row.cells[colNumb]; //插入元素的后一个元素
				
				if(!_nextCell || _nextCell.available || (_nextCell.mergedCell.getColNumb() == colNumb + 1)){ //插入元素的同行后一个元素没有被列合并或插入的是最后一列或与合并后的单元格在同一列
					var _lastElement = (_row.cells[colNumb - 1].available ? 
							_row.cells[colNumb - 1].element : _row.cells[colNumb - 1].mergedCell.element);//上一个元素
					_element = $('<' + _lastElement.attr('tagName') + ' />').html('').css('height', '20px');
				}else{//插入元素的后一个元素被合并，插入元素也会被合并
					_mergedCell = _nextCell.mergedCell;
					if(_mergedCell.row == _row){ //插入需合并元素与合并后元素同一行
						_mergedCell.setColSpan(_mergedCell.colSpan + 1);
					}
				}
				return {
					element : _element,
					mergedCell : _mergedCell,
					available : _mergedCell == null
				};
			});
			//调整宽度
			var _cols = [], //改变宽度的列集合
				_width = this.tableEditor.minColWidth, //新插入列的宽度
				_resizeTableWidth = true; //是否需要重新设置表格的宽度。表格所有列的最低宽度之和大于表格总宽度
			if((this.tableEditor.colWidths.length + 1) * this.tableEditor.minColWidth <= this.tableEditor.width){
				_width = this.getWidth();
				_resizeTableWidth = false;
			}
			//插入一列宽度为列最小宽度来占位新插入列的宽度。在调整表格列宽度的时候因其已经最小，所有不会再减少宽度了
			this.tableEditor.colWidths.splice(_newColNumb, 0, _width);
			if(!_resizeTableWidth){ //能保证插入列后，每列的宽度都不小于最小宽度
				this.tableEditor.fixTableWidth(_width * (-1), _cols);
			}else{ //不能保证时，新插入的宽度为列最小宽度，并且表格的宽度新增最小列宽度。
				//改变表格总宽度
				this.tableEditor.setTableWidth(this.tableEditor.width + _width);
			}
			_cols.push(_newColNumb);
			//激活表格列宽度改变事件
			this.tableEditor.colwidthchanged(_cols);
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 在上方插入行
		 * @return {void}
		 * @public
		 */
		insertRowLast : function(){
			//新插入的行的行号
			var _newRowNumb = this.beginRowNumb;
			//插入行
			this.tableEditor.table.insertRow(_newRowNumb, function(colNumb, rowNumb){
				var $ = jQuery,
					_element = null, //单元格的jQuery对象
					_mergedCell = null, //合并后的单元格
					_nextRow = this.rows[rowNumb]; //插入行的下一行对象
				
				if(_nextRow.cells[colNumb].available || 
						_nextRow.cells[colNumb].mergedCell.row == _nextRow){ //插入的当前单元格下一行对应单元格没有被上一行合并
					var _nextElement = (_nextRow.cells[colNumb].available ? 
							_nextRow.cells[colNumb].element : _nextRow.cells[colNumb].mergedCell.element);//下一行对应元素
					_element = $('<' + _nextElement.attr('tagName') + ' />').html('').css('height', '20px');
				}else{//下一行对应的元素被上一行合并
					_mergedCell = _nextRow.cells[colNumb].mergedCell;
					if(_mergedCell.getColNumb() == colNumb){ //插入需合并元素与合并后元素同一列
						_mergedCell.setRowSpan(_mergedCell.rowSpan + 1);
					}
				}
				return {
					element : _element,
					mergedCell : _mergedCell,
					available : _mergedCell == null
				};
			});
			//重置选中区域
			this.setSelectedCells(this.beginColNumb, this.endColNumb, this.beginRowNumb + 1, this.endRowNumb + 1);
			//设置插入行的每个单元格的宽度
			for(var i = 0; i < this.tableEditor.table.rows[_newRowNumb].cells.length; i++){
				if(this.tableEditor.table.rows[_newRowNumb].cells[i].element){
					this.tableEditor.table.rows[_newRowNumb].cells[i].fixWidth();
				}
			}
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 在下方插入行
		 * @return {void}
		 * @public
		 */
		insertRowNext : function(){
			//新插入的行的行号
			var _newRowNumb = this.endRowNumb + 1;
			//插入行
			this.tableEditor.table.insertRow(_newRowNumb, function(colNumb, rowNumb){
				var $ = jQuery,
					_element = null, //单元格的jQuery对象
					_mergedCell = null, //合并后的单元格
					_nextRow = this.rows[rowNumb]; //插入行的下一行对象
				
				if(!_nextRow || _nextRow.cells[colNumb].available || 
						_nextRow.cells[colNumb].mergedCell.row == _nextRow){ //插入的当前单元格是最后一行或下一行对应单元格没有被上一行合并
					var _lastElement = (this.rows[rowNumb - 1].cells[colNumb].available ? 
							this.rows[rowNumb - 1].cells[colNumb].element : this.rows[rowNumb - 1].cells[colNumb].mergedCell.element);//上一行对应元素
					_element = $('<' + _lastElement.attr('tagName') + ' />').html('').css('height', '20px');
				}else{//下一行对应的元素被上一行合并
					_mergedCell = _nextRow.cells[colNumb].mergedCell;
					if(_mergedCell.getColNumb() == colNumb){ //插入需合并元素与合并后元素同一列
						_mergedCell.setRowSpan(_mergedCell.rowSpan + 1);
					}
				}
				return {
					element : _element,
					mergedCell : _mergedCell,
					available : _mergedCell == null
				};
			});
			//设置插入行的每个单元格的宽度
			for(var i = 0; i < this.tableEditor.table.rows[_newRowNumb].length; i++){
				if(this.tableEditor.table.rows[_newRowNumb].cells[i].element){
					this.tableEditor.table.rows[_newRowNumb].cells[i].fixWidth();
				}
			}
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 合并选中的单元格
		 * @return {void}
		 * @public
		 */
		mergeCells : function(){
			//合并单元格
			for(var i = this.beginColNumb; i <= this.endColNumb; i++){
				for(var j = this.beginRowNumb; j <= this.endRowNumb; j++){
					if((i != this.beginColNumb || j != this.beginRowNumb) && this.tableEditor.table.getCell(j, i).available){
						this.tableEditor.table.getCell(j, i).changeToNone(this.tableEditor.table.getCell(this.beginRowNumb, this.beginColNumb));
					}
				}
			}
			//调整合并后单元格宽度
			this.tableEditor.table.getCell(this.beginRowNumb, this.beginColNumb).fixWidth();
			//清理行
			for(var i = this.endRowNumb; i >= this.beginRowNumb + 1; i--){
				if(this.tableEditor.table.clearRow(i)){
					this.endRowNumb--;
				}
			}
			//清理列
			for(var i = this.endColNumb; i >= this.beginColNumb + 1; i--){
				if(this.tableEditor.table.clearCol(i)){ //列被删掉
					this.tableEditor.colWidths[i - 1] += this.tableEditor.colWidths[i];
					this.tableEditor.colWidths.splice(i, 1);
					jQuery(this.tableEditor.$colGroup.find('col')[i - 1]).css('width', this.tableEditor.colWidths[i - 1] + 'px');
					this.endColNumb--;
				}
			}
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 将选中单元格拆分行
		 * @return {void}
		 * @public
		 */
		splitRow : function(){
			var $ = jQuery, thiz = this,
				_cell = this.tableEditor.table.getCell(this.beginRowNumb, this.beginColNumb), //被拆分的单元格
				_newCell = null; //拆分后的单元格
			if(_cell.rowSpan > 1){ //单元格合并过行
				var _element = $('<' + _cell.element.attr('tagName') + ' />').html('').css('height', '20px'), //新单元格元素
					_last = true; //新单元格元素是其所在行的最后一个
				for(var i = this.endColNumb + 1; i < this.tableEditor.colWidths.length; i++){
					if(this.tableEditor.table.getCell(this.endRowNumb, i).element){
						_element.insertBefore(this.tableEditor.table.getCell(this.endRowNumb, i).element);
						_last = false;
						break;
					}
				}
				if(_last){
					this.tableEditor.table.rows[this.endRowNumb].element.append(_element);
				}
				_cell.setRowSpan(_cell.rowSpan - 1);
				//新单元格替换旧的占位单元格
				this.tableEditor.table.getCell(this.endRowNumb, this.beginColNumb).destroy();
				_newCell = this.tableEditor.table.rows[this.endRowNumb].cells[this.beginColNumb] = 
					this.tableEditor.table.rows[this.endRowNumb].createCell({
					element : _element
				});
				_newCell.setColSpan(_cell.colSpan);//新单元格与原单元格有相同的列跨度
				//更新新单元格所在行后的被合并的占位单元格
				for(var i = this.beginColNumb + 1; i <= this.endColNumb; i ++){
					this.tableEditor.table.getCell(this.endRowNumb, i).mergedCell = _newCell;
				}
				//更新选中区
				this.setSelectedCells(this.beginColNumb, this.endColNumb, this.beginRowNumb, this.endRowNumb - 1);
			}else{ //单元格没有合并其他行，在下方新插入1行
				this.tableEditor.table.insertRow(this.endRowNumb + 1, function(colNumb, rowNumb){
					var _element = null, //单元格元素
						_colSpan = 1, //单元格的列跨度
						_mergedCell = null; //合并后的单元格
					if(colNumb == thiz.beginColNumb){
						var _curCell = this.getCell(thiz.beginRowNumb, colNumb); //拆分的单元格
						_colSpan = _curCell.colSpan;
						_element = $('<' + _curCell.element.attr('tagName') + '/>').html('').css('height', '20px').attr('colSpan', _colSpan);
					}else if(colNumb > thiz.beginColNumb && colNumb <= thiz.endColNumb){
						_mergedCell = function(){ //拆分后，新的单元格合并后的单元格
							return this.row.cells[thiz.beginColNumb];
						}
					}else{
						var _upCell = this.rows[rowNumb - 1].cells[colNumb]; //上一行对应的单元格
						_mergedCell = (_upCell.available ? _upCell : _upCell.mergedCell);
						if(colNumb == _mergedCell.getColNumb()){ //合并后的单元格与当前单元格在同一列
							_mergedCell.setRowSpan(_mergedCell.rowSpan + 1);
						}
					}
					return {
						element : _element,
						mergedCell : _mergedCell,
						colSpan: _colSpan,
						available : _mergedCell == null
					};
				});
				_newCell = this.tableEditor.table.getCell(this.endRowNumb + 1, this.beginColNumb);
			}
			_newCell.fixWidth();
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 将选中单元格拆分列
		 * @return {void}
		 * @public
		 */
		splitCol : function(){
			var $ = jQuery, thiz = this,
				_cell = this.tableEditor.table.getCell(this.beginRowNumb, this.beginColNumb), //被拆分的单元格
				_newCell = null; //拆分后的单元格
			if(_cell.colSpan > 1){ //单元格合并过列
				var _element = $('<' + _cell.element.attr('tagName') + ' />').html('').css('height', '20px'); //新单元格元素
				_element.insertAfter(_cell.element);
				_cell.setColSpan(_cell.colSpan - 1);
				//新单元格替换旧的占位单元格
				this.tableEditor.table.getCell(this.beginRowNumb, this.endColNumb).destroy();
				_newCell = this.tableEditor.table.rows[this.beginRowNumb].cells[this.endColNumb] = 
					this.tableEditor.table.rows[this.beginRowNumb].createCell({
					element : _element
				});
				_newCell.setRowSpan(_cell.rowSpan);//新单元格与原单元格有相同的行跨度
				//更新新单元格所在列后的被合并的占位单元格
				for(var i = this.beginRowNumb + 1; i <= this.endRowNumb; i++){
					this.tableEditor.table.getCell(i, this.endColNumb).mergedCell = _newCell;
				}
				//更新选中区
				this.setSelectedCells(this.beginColNumb, this.endColNumb - 1, this.beginRowNumb, this.endRowNumb);
			}else{ //单元格没有合并其他列，在右方插入新列
				this.tableEditor.table.insertCol(this.endColNumb + 1, function(rowNumb, colNumb){
					var _element = null, //单元格元素
						_rowSpan = 1, //单元格的行跨度
						_mergedCell = null; //合并后的单元格
					if(rowNumb == thiz.beginRowNumb){ //拆分出来新单元格所在位置
						var _curCell = this.getCell(rowNumb, thiz.beginColNumb); //拆分的单元格
						_rowSpan = _curCell.rowSpan;
						_element = $('<' + _curCell.element.attr('tagName') + '/>').html('')
							.attr('rowSpan', _rowSpan).css('height', (_rowSpan * 20) + 'px');
					}else if(rowNumb > thiz.beginRowNumb && rowNumb <= thiz.endRowNumb){
						_mergedCell = function(){ //拆分后，新的单元格合并后的单元格
							return this.row.table.getCell(thiz.beginRowNumb, colNumb);
						}
					}else{
						var _leftCell = this.getCell(rowNumb, colNumb - 1); //上一列对应的单元格
						_mergedCell = (_leftCell.available ? _leftCell : _leftCell.mergedCell);
						if(rowNumb == _mergedCell.row.getRowNumb()){ //合并后的单元格与当前单元格在同一行
							_mergedCell.setColSpan(_mergedCell.colSpan + 1);
						}
					}
					return {
						element : _element,
						mergedCell : _mergedCell,
						rowSpan: _rowSpan,
						available : _mergedCell == null
					};
				});
				//设置新插入列宽度
				var _w = parseInt(this.tableEditor.colWidths[this.beginColNumb] / 2); //新插入列宽度
				if(_w < this.tableEditor.minColWidth){
					this.tableEditor.setTableWidth(this.tableEditor.width + this.tableEditor.minColWidth);
					this.tableEditor.colWidths.splice(this.beginColNumb + 1, 0, this.tableEditor.minColWidth);
					this.tableEditor.colWidths[this.beginColNumb + 1] = this.tableEditor.minColWidth;
					this.tableEditor.colwidthchanged([this.beginColNumb + 1]);
				}else{
					var $ = jQuery;
					this.tableEditor.colWidths[this.beginColNumb] = this.tableEditor.colWidths[this.beginColNumb] - _w;
					this.tableEditor.colWidths.splice(this.beginColNumb + 1, 0, _w);
					this.tableEditor.colwidthchanged([this.beginColNumb, this.beginColNumb + 1]);
				}
				
				_newCell = this.tableEditor.table.getCell(this.beginRowNumb, this.beginColNumb + 1);
				//更新选区的标尺
				this.tableEditor.getSelectedCells().updateRuler();
			}
			_newCell.fixWidth();
			_cell.fixWidth();
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 删除选中的列
		 * @return {void}
		 * @public
		 */
		deleteCols : function(){
			var _width = 0, //选中区域的宽度
				_cols = [], //删除列操作改变宽度的列
				_endColNumb = this.endColNumb,
				_beginColNumb = this.beginColNumb;
			//取消选中区域
			this.cancelSelectCells();
			//遍历删除选中的每一列
			for(var i = _endColNumb; i >= _beginColNumb; i--){
				this.tableEditor.table.deleteCol(i);
				_width += this.tableEditor.colWidths[i];
				this.tableEditor.colWidths.splice(i, 1);
			}
			//调整表格宽度
			this.tableEditor.fixTableWidth(_width, _cols);
			this.tableEditor.colwidthchanged(_cols);
			//清理行
			for(var i = this.tableEditor.table.rows.length - 1; i > 0; i--){
				this.tableEditor.table.clearRow(i);
			}
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 删除选中的行
		 * @return {void}
		 * @public
		 */
		deleteRows : function(){
			var _endRowNumb = this.endRowNumb, _beginRowNumb = this.beginRowNumb;
			//取消选中区域
			this.cancelSelectCells();
			//遍历删除选中的每一行
			for(var i = _endRowNumb; i >= _beginRowNumb; i--){
				this.tableEditor.table.deleteRow(i);
			}
			//清理合并的列
			for(var i = this.tableEditor.colWidths.length - 1; i > 0; i--){
				if(this.tableEditor.table.clearCol(i)){ //列被删掉
					this.tableEditor.colWidths[i - 1] += this.tableEditor.colWidths[i];
					this.tableEditor.colWidths.splice(i, 1);
					jQuery(this.tableEditor.$colGroup.find('col')[i - 1]).css('width', this.tableEditor.colWidths[i - 1] + 'px');
				}
			}
			this.tableEditor.table.fireEvent('cellchanged');
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			if(this.cellRuler){
				this.cellRuler.destroy();
				delete this.cellRuler;
			}
			delete this.tableEditor;
		},
		/**
		 * 更新选中单元格的标尺
		 * @return {void}
		 * @public
		 */
		updateRuler : function(){
			if(this.beginColNumb == -1){ //取消选中区域
				this.cellRuler.hide();
				return;
			}else if(this.cellRuler == null){
				this.cellRuler = new TableEditor.CellRuler(this.tableEditor.element.find('.table-container'), this.tableEditor);
			}
			var _left = 0; //选中区域单元格标尺的left属性
			for(var i = 0; i < this.beginColNumb; i++){
				_left += this.tableEditor.colWidths[i];
			}
			this.cellRuler.show(_left, this.getWidth(), this.getMinWidth(), this.getMaxWidth());
		},
		/**
		 * 指定的列号集合的列宽度改变后，是否需要重新调整单元格标尺
		 * @param {Array} cols (必选)改变宽度的列集合
		 * @return {boolean} 返回指定的列号集合的列宽度改变后，是否需要重新调整单元格标尺
		 * @private
		 */
		_needChangeRuler : function(cols){
			for(var i = 0; i < cols.length; i++){
				if(this.endColNumb >= cols[i]){
					return true;
				}
			}
			return false;
		}
}

/**
 * 拖动选择表格单元格帮助类型
 * @param {TableEditor} tableEditor (必选)表格编辑器
 * @param {string/jQuery} renderTo (必选)渲染容器
 * @class 拖动选择表格单元格帮助类型
 * @constructor
 * <AUTHOR> 2011/9/28
 */
TableEditor.SelectHelper = function(tableEditor, renderTo){
	this.tableEditor = tableEditor;
	this.renderTo = renderTo;
	this._init();
}

/**
 * 拖动选择表格单元格帮助对象类型原型
 */
TableEditor.SelectHelper.prototype = {
		element : null, //{jQuery}拖动选择表格单元格帮助对象的页面元素对象
		tableEditor : null, //{TableEditor}表格编辑器对象
		renderTo : null, //{string/jQuery}渲染容器
		startPoint : null, //{Point}拖动起始点坐标
		endPoint : null, //{Point}拖动终点坐标
		/**
		 * 显示拖动选择表格单元格帮助对象
		 * @param {Point} startPoint (必选)拖动的鼠标起始点坐标
		 * @return {void}
		 * @public
		 */
		show : function(startPoint){
			var _off = this.renderTo.offset(); //容器所在位置偏移量
			this.startPoint = {
					x: startPoint.x - _off.left,
					y : startPoint.y - _off.top
			};
			this.element.css({
				left : this.startPoint.x + 'px',
				top : this.startPoint.y + 'px',
				width : '0',
				height : '0'
			}).show();
		},
		/**
		 * 隐藏拖动选择表格单元格帮助对象
		 * @return {void}
		 * @public
		 */
		hide : function(){
			this.element.hide();
		},
		/**
		 * 更新拖动的终点坐标
		 * @param {Point} endPoint (必选)拖动当前鼠标的坐标
		 * @return {void}
		 * @public
		 */
		updateEndPoint : function(endPoint){
			var _off = this.renderTo.offset(); //容器偏移量
			this.endPoint = {
					x: endPoint.x - _off.left,
					y: endPoint.y - _off.top
			};
			var	_trbp = {x: this.tableEditor.width, y: this.tableEditor.table.element.height()}, //表格的右下角坐标
			_p = {
					x: (this.endPoint.x < 1 ? 1 : (this.endPoint.x >= _trbp.x ? (_trbp.x - 1) : this.endPoint.x)),
					y: (this.endPoint.y < 1 ? 1 : (this.endPoint.y >= _trbp.y ? (_trbp.y - 1) : this.endPoint.y))
			};//有效终点坐标
			
			this.element.css({
				left : Math.min(this.startPoint.x, _p.x) + 'px',
				top : Math.min(this.startPoint.y, _p.y) + 'px',
				width : Math.abs(_p.x - this.startPoint.x) + 'px',
				height : Math.abs(_p.y - this.startPoint.y) + 'px'
			});
			this.endPoint = _p;
		},
		/**
		 * 获取选中区域4个折点坐标
		 * @return {Array} 选中区域4个折点坐标数组
		 * @public
		 */
		getAllPoints : function(){
			var _off = this.renderTo.offset(); //容器偏移量
			return [{x:this.startPoint.x + _off.left, y:this.startPoint.y + _off.top}, 
			        {x:this.endPoint.x + _off.left, y: this.endPoint.y + _off.top}, 
			        {x:this.startPoint.x + _off.left, y : this.endPoint.y + _off.top},
			        {x:this.endPoint.x + _off.left, y : this.startPoint.y + _off.top}];
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			this.element.remove();
			delete this.element;
			delete this.tableEditor;
			delete this.startPoint;
			delete this.endPoint;
			delete this.renderTo;
		},
		/**
		 * 初始化对象
		 * @return {void}
		 * @private
		 */
		_init : function(){
			var $ = jQuery;
			if(typeof this.renderTo == 'string'){
				this.renderTo = $('#' + this.renderTo);
			}
			this.element = $('<div class="select-helper"></div>').appendTo(this.renderTo);
		}
}

/**
 * 表格编辑器的表格宽度标尺
 * @param {TableEditor} tableEditor (必选)表格编辑器
 * @param {integer} width (必选)表格当前的宽度
 * @class 表格编辑器的表格宽度标尺
 * <AUTHOR> 2011/9/19 
 * @public
 */
TableEditor.TableRuler = function(tableEditor, width){
	this.tableEditor = tableEditor;
	this.width = width;
	var thiz = this;
	//添加表格宽度改变事件监听
	tableEditor.addListener('tablewidthchanged', function(newWidth){
		thiz.setWidth(newWidth);
	});
	this._init();
}

/**
 * 表格编辑器的表格宽度标尺类型原型
 */
TableEditor.TableRuler.prototype = {
		tableEditor : null, //{TableEditor}表格编辑器
		
		element : null, //{jQuery}表格标尺jQuery对象
		$size : null, //{jQuery}表格总宽度
		
		fireEvent : TableEditor.prototype.fireEvent, 
		addListener : TableEditor.prototype.addListener,
		removeListener : TableEditor.prototype.removeListener,
		/**
		 * 设置表格标尺宽度
		 * @param {integer} width (必选)表格的宽度
		 * @return {void}
		 * @public
		 */
		setWidth : function(width){
			this.$size.html(width);
			this.width = width;
		},
		/**
		 * 获取表格的宽度
		 * @return {integer} 表格标尺中表格的宽度
		 * @public
		 */
		getWidth : function(){
			return this.width;
		},
		/**
		 * 销毁表格标尺对象
		 * @return
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			this.$size.remove();
			delete this.$size;
			this.element.remove();
			delete this.element;
			delete this.tableEditor;
			this.fireEvent('afterdestroy');
		},
		/**
		 * 初始化表格标尺对象
		 * @return {void}
		 * @private
		 */
		_init : function(){
			var $ = jQuery, thiz = this;
			thiz.element = $('<div class="table-ruler"><div class="table-ruler-1"><div class="table-ruler-2"><div class="table-ruler-3"><div class="table-ruler-4" align="center"></div></div></div></div></div>')
			.click(function(){
				thiz.tableEditor.__documentNoClick = true;
			});
			//尺码容器
			var _$container = $('<span class="ruler-size-container" />').appendTo(thiz.element.find('.table-ruler-4'));
			//尺码对象
			thiz.$size = $('<span class="table-ruler-size-text"></span>').html(thiz.width).appendTo(_$container).bind('dblclick', function(){
				thiz._editSize();
			});
			_$container.append('<span class="ruler-unit">像素</span>');
		},
		/**
		 * 编辑表格标尺尺码
		 * @return {void}
		 * @private
		 */
		_editSize : function(){
			var $ = jQuery, thiz = this;
			if(!thiz.$sizeEditor){//尺码文本框未初始化
				//创建文本框
				thiz.$sizeEditor = $('<input type="text" value="' + thiz.width + '"/>')
				.css('width', thiz.$size.outerWidth(true) + 'px')
				.insertAfter(thiz.$size).bind('blur', function(){
					if(!isNaN(this.value) && this.value >= (thiz.tableEditor.minColWidth * thiz.tableEditor.colWidths.length)){
						//设置表格列宽度
						thiz.tableEditor.setWidth(this.value);
						//设置标尺宽度
						//thiz.setWidth(this.value);
					}
					$(this).hide();
					thiz.$size.show();
				}).click(function(){
					//单击时，取消事件冒泡
					return false;
				});
				//非文本宽单击时，文本框失去焦点
				var _listener = function(e){
					if(e.target != thiz.$sizeEditor[0]){
						thiz.$sizeEditor[0].blur();
					}
				}
				$(document).bind('mousedown', _listener);
				//销毁对象
				thiz.addListener('beforedestroy', function(){
					$(document).unbind('mousedown', _listener);
					this.$sizeEditor.remove();
					delete this.$sizeEditor;
				});
			}else{
				thiz.$sizeEditor.val(thiz.width).show();
			}
			thiz.$size.hide();
			thiz.$sizeEditor[0].select();
		}
}

/**
 * 选中单元格区域标尺类型
 * @param {string/jQuery} renderTo (必选)单元格标尺渲染的容器
 * @param {TableEditor} tableEditor (必选)单元格标尺所在的表格编辑器
 * @class 选中单元格区域标尺类型
 * @constructor
 * <AUTHOR> 2011/9/27
 */
TableEditor.CellRuler = function(renderTo, tableEditor){
	this.renderTo = renderTo;
	this.tableEditor = tableEditor;
	this._init();
}

/**
 * 选中单元格的标尺类型原型
 */
TableEditor.CellRuler.prototype = {
		left : 0, //{integer}单元格标尺左边的宽度
		width : 0, //{integer}单元格标尺的宽度
		minWidth : 10, //{integer}单元格标尺的最小宽度。标尺设置单元格宽度时使用
		maxWidth : 1000, //{integer}单元格标尺的最大宽度。标尺设置单元格宽度时使用
		renderTo : '', //{string/jQuery}单元格标尺的渲染目标
		
		tableEditor : null, //{TableEditor}单元格尺寸所属表格编辑器，用于在手动设置标尺尺码后，设置表格的列宽
		$ruler : null, //{jQuery}单元格标尺的jQuery对象
		$mark : null, //{jQuery}单元格标尺的游标jQuery对象
		$size : null, //{jQuery}单元格尺寸码值
		$sizeEditor : null, //{jQuery}单元格尺寸码值编辑器
		
		fireEvent : TableEditor.prototype.fireEvent, 
		addListener : TableEditor.prototype.addListener,
		removeListener : TableEditor.prototype.removeListener,
		_format : TableEditor.prototype._format,
		/**
		 * 显示单元格标尺
		 * @param {integer} left (必选)标尺的left坐标
		 * @param {integer} width (必选)标尺的宽度
		 * @param {integer} minWidth (必选)标尺显示的最小宽度
		 * @param {integer} maxWidth (必选)标尺显示的最大宽度
		 * @return {void}
		 * @public
		 */
		show : function(left, width, minWidth, maxWidth){
			//设置标尺宽度范围
			this.minWidth = minWidth;
			this.maxWidth = maxWidth;
			//调整标尺
			this.$ruler.show();
			this.$ruler.css('left', left + 'px');
			this.left = left;
			this.setWidth(width);
		},
		/**
		 * 隐藏单元格标尺
		 * @return {void}
		 * @public
		 */
		hide : function(){
			this.$ruler.hide();
		},
		/**
		 * 设置单元格标尺宽度
		 * @param {integer} newWidth (必选)单元格标尺新的宽度
		 * @return {integer} 返回设置后的标尺新尺寸
		 * @public
		 */
		setWidth : function(newWidth){
			if(newWidth <= this.maxWidth && newWidth >= this.minWidth){ //新宽度在最小宽度与最大宽度之间
				this.$ruler.css('width', newWidth + 'px');
				this.$size.html(newWidth + '(' + this._format((newWidth / this.tableEditor.width) * 100) + '%)');
				this.width = newWidth;
				this.fireEvent('widthchanged', newWidth);
				return newWidth;
			}
			return this.width;
		},
		/**
		 * 显示单元格标尺的游标
		 * @return {void}
		 * @public
		 */
		showMark : function(){
			if(!this._markVisiable){ //游标不可见
				if(!this.$mark){ //游标未创建
					this.$mark = $('<div class="cell-ruler-mark"></div>').prependTo(this.renderTo);
					//标尺宽度变化，游标位置随之变化
					this.addListener('widthchanged', function(newWidth){
						if(this._markVisiable){
							this.$mark.css('left', (this.left + newWidth - 1) + 'px');
						}
					});
					//销毁游标
					this.addListener('beforedestroy', function(){
						this.$mark.remove();
						delete this.$mark;
					});
				}else{
					this.$mark.show();
				}
				//设置游标位置
				this.$mark.css('left', (this.left + this.width - 1) + 'px');
				this._markVisiable = true;
			}
		},
		/**
		 * 隐藏单元格标尺的游标
		 * @return {void}
		 * @public
		 */
		hideMark : function(){
			if(this._markVisiable){
				this.$mark.hide();
				this._markVisiable = false;
			}
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			delete this.renderTo;
			this.$size.remove();
			delete this.$size;
			this.$ruler.remove();
			delete this.$ruler;
			this.fireEvent('afterdestroy');
		},
		/**
		 * 初始化单元格标尺对象
		 * @return {void}
		 * @private
		 */
		_init : function(){
			var $ = jQuery, thiz = this;
			if(typeof this.renderTo == 'string'){
				this.renderTo = $('#' + this.renderTo);
			}
			//标尺
			this.$ruler = $('<div class="cell-ruler"><div class="cell-ruler-1"><div class="cell-ruler-2"><div class="cell-ruler-3"><div class="cell-ruler-4" align="center"></div></div></div></div></div>')
			.prependTo(this.renderTo).click(function(){ 
				thiz.tableEditor.__documentNoClick = true;
			});
			//尺码容器
			var $sizeContainer = $('<span class="ruler-size-container"></span>').appendTo(this.$ruler.find('.cell-ruler-4'));
			//尺码
			this.$size = $('<span class="ruler-size-text"></span>')
				.appendTo($sizeContainer).bind('dblclick', function(){
					if(thiz.tableEditor.getSelectedCells().isResizable()){ //不包含最后一列的选择单元格集合
						thiz._editSize();
					}
				});
			//单位
			$sizeContainer.append('<span class="ruler-unit">像素</span>');
		},
		/**
		 * 编辑单元格尺寸尺码
		 * @return {void}
		 * @private
		 */
		_editSize : function(){
			var $ = jQuery, thiz = this;
			if(!thiz.$sizeEditor){//尺码文本框未初始化
				//创建文本框
				thiz.$sizeEditor = $('<input type="text" value="' + thiz.width + '"/>')
				.css('width', thiz.$size.outerWidth(true) + 'px')
				.insertAfter(thiz.$size).bind('blur', function(){
					var _increase = (thiz.width - thiz.setWidth(this.value)) * (-1); //新增宽度
					if(_increase != 0){
						thiz.tableEditor.getSelectedCells().changeColWidth(_increase);
					}
					$(this).hide();
					thiz.$size.show();
					thiz.tableEditor.___noCellSelect = false;
				}).click(function(){
					//单击时，取消事件冒泡
					return false;
				});
				//非文本宽单击时，文本框失去焦点
				var _listener = function(e){
					if(e.target != thiz.$sizeEditor[0]){
						thiz.$sizeEditor[0].blur();
					}
				}
				$(document).bind('mousedown', _listener);
				//销毁对象
				thiz.addListener('beforedestroy', function(){
					$(document).unbind('mousedown', _listener);
					this.$sizeEditor.remove();
					delete this.$sizeEditor;
				});
			}else{
				thiz.$sizeEditor.val(thiz.width).show();
			}
			thiz.$size.hide();
			thiz.$sizeEditor[0].select();
			thiz.tableEditor.___noCellSelect = true;
		}
}

/**
 * 抽象的表格列宽度对象。针对合并单元格的列
 * @param {TableEditor} tableEditor (必选)列宽度所属表格编辑器
 * @param {integer} colNumb (必选)列宽度的列号
 * @param {Array} widthGroup (必选)列宽度抽象对象的一个列宽度集合
 * @param {integer} totalWidth (必选)列宽度的一个列宽度集合的总宽度
 * @public
 * @class 抽象的表格列宽度对象。针对合并单元格的列
 * <AUTHOR> 2011/9/19
 */
TableEditor.ColWidth = function(tableEditor, colNumb, widthGroup, totalWidth){
	this.tableEditor = tableEditor;
	this.colNumb = colNumb;
	this.widthGroups = [{
		widthGroup : widthGroup,
		totalWidth : totalWidth
	}];
}

/**
 * 抽象的表格列宽度对象类型原型
 */
TableEditor.ColWidth.prototype = {
		clazz : 'TableEditor.ColWidth', //类名
		tableEditor : null, //{TableEditor}单元格宽度对象所属编辑器
		widthGroups : null, //{TableEditor.ColWidth}单元格宽度分组对象数组
		colNumb : 0, //{integer}单元格列号
		/**
		 * 添加单元格宽度分组
		 * @param {Array} widthGroup (必选)同组宽度的列号数组
		 * @param {integer} totalWidth (必选)同组宽度的总宽度
		 * @return {void}
		 * @public
		 */
		addGroup : function(widthGroup, totalWidth){
			this.widthGroups.push({
				widthGroup : widthGroup,
				totalWidth : totalWidth
			});
		},
		/**
		 * 计算单元格的宽度
		 * @param {boolean} forceWidth (可选)是否需要平分分组中的宽度
		 * @return {boolean} 计算过程中是否有变动。true-有变动
		 * @public
		 */
		countWidth : function(forceWidth){
			var _updated = false; //计算内容没有更新
			for(var i = 0; i < this.widthGroups.length; i++){
				for(var j = this.widthGroups[i].widthGroup.length - 1; j >= 0; j--){
					if(this.widthGroups[i].widthGroup.length == 0){ //得到当前列的宽度
						this.tableEditor.colWidths[this.colNumb] = this.widthGroups[i].totalWidth;
						this.destroy();
						return true;
					}else if(forceWidth){ //强制平分宽度
						var _width = parseInt(this.widthGroups[i].totalWidth / (this.widthGroups[i].widthGroup.length + 1)); //平分后的宽度
						for(var a = 0; a < this.widthGroups[i].widthGroup.length; a++){
							this.tableEditor.colWidths[this.widthGroups[i].widthGroup[a]].destroy();
							this.tableEditor.colWidths[this.widthGroups[i].widthGroup[a]] = _width;
						}
						this.tableEditor.colWidths[this.colNumb] = _width;
						this.destroy();
						return true;
					}else if(this.tableEditor.colWidths[this.widthGroups[i].widthGroup[j]].clazz != 'TableEditor.ColWidth'){ //减少分组元素
						this.widthGroups[i].totalWidth -= this.tableEditor.colWidths[this.widthGroups[i].widthGroup[j]];
						this.widthGroups[i].widthGroup.splice(j, 1);
						_updated = true;
					}
				}
			}
			return _updated;
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			delete this.tableEditor;
			delete this.widthGroups;
		}
}

/**
 * 编辑器的表格类型
 * @param {object} config (必选)构造参数
 * @class 编辑器的表格类型
 * @constructor
 * <AUTHOR> 2011/9/26
 */
TableEditor.Table = function(config){
	this.setConfig(config);
	this._init();
}

/**
 * 表格类型原型
 */
TableEditor.Table.prototype = {
		rows : null, //{Array}行行的集合
		tableEditor : null, //{TableEditor}表格所在的表格编辑器
		element : null, //{jQuery}表格的jQuery对象
		
		fireEvent : TableEditor.prototype.fireEvent, 
		addListener : TableEditor.prototype.addListener,
		removeListener : TableEditor.prototype.removeListener,
		setConfig : TableEditor.prototype.setConfig,
		/**
		 * 获取单元格对象
		 * @param {integer} rowNumb (必选)单元格的行号（从零开始）
		 * @param {integer} colNumb (必选)单元格的列号（从零开始）
		 * @return {TableEditor.Table.Row.Cell} 表格的单元格对象
		 * @public
		 */
		getCell : function(rowNumb, colNumb){
			return this.rows[rowNumb].cells[colNumb];
		},
		/**
		 * 指定坐标的单元格对象
		 * @param {integer} x (必选)横坐标
		 * @param {integer} y (必选)纵坐标
		 * @return {TableEditor.Table.Row.Cell} 指定坐标所在的单元格对象
		 * @public
		 */
		cellAt : function(x, y){
			var $ = jQuery, thiz = this, _cell = null;
			$('td, th', this.element).each(function(){
				var _$this = $(this),
					_offset = _$this.offset(), //单元格左上角偏移量
					_bl = parseInt(_$this.css('border-left-width')), //左边边框的宽度
					_br = parseInt(_$this.css('border-right-width')), //右边边框的宽度
					_bt = parseInt(_$this.css('border-top-width')), //上边边框的宽度
					_bb = parseInt(_$this.css('border-bottom-width')); //下边边框的宽度
				//指定坐标是否在单元格区域内
				if(x >= (_offset.left - _bl) && (x <= _offset.left + _$this.width() + _br) && 
						y >= (_offset.top - _bt) &&(y <= _offset.top + _$this.height() + _bb)){
					_cell = thiz.tableEditor.getCell(_$this.attr('instanceId'));
					return false;
				}	
			});
			return _cell;
		},
		/**
		 * 在表格的指定位置插入行
		 * @param {integer} rowNumb (必选)表格插入行的位置
		 * @param {function} getCellInfo (必选)插入的行在创建单元格时，获取在指定的位置处的单元格信息的回调
		 * @return {void}
		 * @public
		 */
		insertRow : function(rowNumb, getCellInfo){
			var $ = jQuery, 
				_element = $('<tr/>'), //行的jQuery对象
				_cellInfos = []; //行的单元格信息
			if(rowNumb == 0){ //插入到第一行
				_element.insertBefore(this.rows[0].element);
			}else{
				_element.insertAfter(this.rows[rowNumb - 1].element);
			}
			//获取行中的单元格信息
			for(var i = 0; i < this.rows[0].cells.length; i++){
				var _cellInfo = getCellInfo.call(this, i, rowNumb);
				_cellInfos.push(_cellInfo);
				if(_cellInfo.element){ //将单元格元素加入行元素中
					_element.append(_cellInfo.element);
				}
			}
			this.rows.splice(rowNumb, 0, this.createRow({
				element : _element,
				cellInfos : _cellInfos
			}));
		},
		/**
		 * 在指定位置插入1列
		 * @param {integer} colNumb (必选)列插入的位置
		 * @param {function} getCellInfo (必选)插入列时，在每一行插入的元素的信息
		 * @return {void}
		 * @public
		 */
		insertCol : function(colNumb, getCellInfo){
			for(var i = 0; i < this.rows.length; i++){ //每行插入1个元素
				var _cellInfo = getCellInfo.call(this, i, colNumb);
				if(_cellInfo.element){ //插入的单元格没有被合并
					var _last = true; //插入的单元格元素在当前行的最后1列
					for(var j = colNumb; j < this.rows[i].cells.length; j++){
						if(this.getCell(i, j).element){
							_cellInfo.element.insertBefore(this.getCell(i, j).element);
							_last = false;
							break;
						}
					}
					if(_last){
						this.rows[i].element.append(_cellInfo.element);
					}
				}
				this.rows[i].cells.splice(colNumb, 0, this.rows[i].createCell(_cellInfo));
			}
			this.tableEditor.$colGroup.append('<col/>');
		},
		/**
		 * 删除指定行
		 * @param {integer} rowNumb (必选)要删除的行的行号
		 * @return {void}
		 * @public
		 */
		deleteRow : function(rowNumb){
			var _row = this.rows[rowNumb]; //删除的行对象
			for(var i = _row.cells.length - 1; i >= 0; i--){
				var _cell = _row.cells[i];
				if(_cell.element){ //单元格没有被合并
					if(_cell.rowSpan > 1){ //合并了其他行的单元格
						_cell.setRowSpan(_cell.rowSpan - 1);
						var _last = true; //此单元格的元素，是下一行对应的最后一个元素
						//遍历下一行，将此单元格的元素插入下一行对应的位置
						for(var j = i + _cell.colSpan; j < _row.cells.length; j++){
							if(this.rows[rowNumb + 1].cells[j].element){
								_cell.element.insertBefore(this.rows[rowNumb + 1].cells[j].element);
								_last = false;
								break;
							}
						}
						if(_last){
							this.rows[rowNumb + 1].element.append(_cell.element);
						}
						//将当前单元格的信息转移给下一行对应的单元格
						this.rows[rowNumb + 1].cells[i].destroy();
						this.rows[rowNumb + 1].cells[i] = this.rows[rowNumb + 1].createCell({
							element : _cell.element,
							rowSpan : _cell.rowSpan,
							colSpan : _cell.colSpan
						});
						//转移原被合并单元格
						for(var a = i + _cell.colSpan - 1; a >= i; a--){
							for(var b = rowNumb + _cell.rowSpan; b > rowNumb; b--){
								if(a != i || (b != rowNumb + 1)){
									this.rows[b].cells[a].mergedCell = this.rows[rowNumb + 1].cells[i];
								}
							}
						}
						delete _cell.element;
					}
				}else if(_cell.mergedCell.getColNumb() == i){ //如果被删除的被合并单元格与合并后的单元格在同一列，更新合并后单元格的rowSpan属性
					_cell.mergedCell.setRowSpan(_cell.mergedCell.rowSpan - 1);
				}
			}
			_row.destroy();
			this.rows.splice(rowNumb, 1);
		},
		/**
		 * 删除表格的指定列
		 * @param {integer} colNumb (必选)要删除的列的列号
		 * @return {void}
		 * @public
		 */
		deleteCol : function(colNumb){
			//遍历每一行指定位置的单元格
			for(var i = 0; i < this.rows.length; i++){
				var _cell = this.rows[i].cells[colNumb];
				if(_cell.element){//单元格没有被合并
					if(_cell.colSpan > 1){//合并了其他列
						_cell.setColSpan(_cell.colSpan - 1);
						//将当前单元格信息转给下一个单元格
						this.rows[i].cells[colNumb + 1].destroy();
						this.rows[i].cells[colNumb + 1] = this.rows[i].createCell({
							element : _cell.element,
							rowSpan : _cell.rowSpan,
							colSpan : _cell.colSpan
						});
						//转移原被合并单元格
						for(var a = colNumb + _cell.colSpan; a > colNumb; a--){
							for(var b = i + _cell.rowSpan - 1; b >= i; b--){
								if((a != colNumb + 1) || (b != i)){
									this.rows[b].cells[a].mergedCell = this.rows[i].cells[colNumb + 1];
								}
							}
						}
						delete _cell.element;
					}
				}else if(_cell.mergedCell.element && i == _cell.mergedCell.row.getRowNumb()){//与合并后的单元格不是同一列（同一列的已经被销毁），但是同一行
					_cell.mergedCell.setColSpan(_cell.mergedCell.colSpan - 1);
				}
				_cell.destroy();
				this.rows[i].cells.splice(colNumb, 1);
			}
			//移除列宽度定义
			var $ = jQuery;
			$($('col', this.tableEditor.$colGroup)[colNumb]).remove();
		},
		/**
		 * 清理表格行。主要用于在单元格合并后，删除被合并单元格所在的所有单元格都被合并的行
		 * @param {integer} rowNumb (必选)需要清理的行号
		 * @return {boolean} 指定行号的行是否被删掉
		 * @public
		 */
		clearRow : function(rowNumb){
			for(var i = 0; i < this.rows[rowNumb].cells.length; i++){
				if(this.rows[rowNumb].cells[i].element){ //此单元格未被合并
					return false;
				}
			}
			this.deleteRow(rowNumb);
			return true;
		},
		/**
		 * 清理表格列。主要用于在单元格合并后，删除被合并单元格所在的所有单元格都被合并的列
		 * @param {integer} colNumb (必选)需要清理的列号
		 * @return {boolean} 被清理的列是否被删掉
		 * @public
		 */
		clearCol : function(colNumb){
			for(var i = 0; i < this.rows.length; i++){
				if(this.rows[i].cells[colNumb].element){ //此单元格未被合并
					return false;
				}
			}
			this.deleteCol(colNumb);
			return true;
		},
		/**
		 * 表格创建内部行对象
		 * @param {Object} rowInfo (必选)行对象信息
		 * @return {TableEditor.Table.Row} 返回创建好的内部行对象
		 * @public
		 */
		createRow : function(rowInfo){
			rowInfo.table = this;
			rowInfo.tableEditor = this.tableEditor;
			return new TableEditor.Table.Row(rowInfo);
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			//销毁内部行对象
			for(var i = 0; i < this.rows.length; i++){
				this.rows[i].destroy();
			}
			delete this.rows;
			delete this.tableEditor;
			this.element.remove();
			delete this.element;
			this.fireEvent('afterdestroy');
		},
		/**
		 * 初始化表格对象
		 * @return {void}
		 * @private
		 */
		_init : function(){
			//创建内部行对象
			this.rows = [];
			if(this.rowInfos){
				for(var i = 0; i < this.rowInfos.length; i++){
					this.rows.push(this.createRow(this.rowInfos[i]));
				}
				delete this.rowInfos;
			}
			this.fireEvent('cellchanged');
		}
}

/**
 * 表格行类型
 * @param {object} config (必选)构造参数
 * @class 表格行类型
 * @constructor
 * <AUTHOR> 2011/9/26
 */
TableEditor.Table.Row = function(config){
	this.setConfig(config);
	this._init();
}

/**
 * 表格行类型原型
 */
TableEditor.Table.Row.prototype = {
		cells : null, //{Array}列
		tableEditor : null, //{TableEditor}表格行所在的表格编辑器
		table : null, //{TableEditor.Table}行所在表格
		
		fireEvent : TableEditor.prototype.fireEvent, 
		addListener : TableEditor.prototype.addListener,
		removeListener : TableEditor.prototype.removeListener,
		setConfig : TableEditor.prototype.setConfig,
		/**
		 * 获取单元格的行号（从零开始计数）
		 * @return {integer} 单元格的行号
		 * @public
		 */
		getRowNumb : function(){
			for(var i = 0; i < this.table.rows.length; i++){
				if(this == this.table.rows[i]){
					return i;
				}
			}
			return -1;
		},
		/**
		 * 创建表格行对象的内部单元格对象
		 * @param {Object} cellInfo (必选)要创建的内部单元格对象信息
		 * @return {TableEditor.Table.Row.Cell} 返回创建的表格行的内部单元格对象
		 * @public
		 */
		createCell : function(cellInfo){
			cellInfo.row = this;
			cellInfo.tableEditor = this.tableEditor;
			return new TableEditor.Table.Row.Cell(cellInfo);
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			//销毁行的内部单元格对象
			for(var i = 0; i < this.cells.length; i++){
				this.cells[i].destroy();
			}
			delete this.cells;
			delete this.tableEditor;
			this.element.remove();
			delete this.element;
			this.fireEvent('afterdestroy');
		},
		/**
		 * 初始化行
		 * @return {void}
		 * @private
		 */
		_init : function(){
			this.cells = [];
			//创建内部单元格
			if(this.cellInfos){
				for(var i = 0; i < this.cellInfos.length; i++){
					this.cells.push(this.createCell(this.cellInfos[i]));
				}
				delete this.cellInfos;
			}
		}
}

/**
 * 表格单元格对象
 * @param {object} config (必选)构造参数
 * @class 表格单元格对象
 * @constructor
 * <AUTHOR> 2011/9/26
 */
TableEditor.Table.Row.Cell = function(config){
	this.setConfig(config);
	this._init();
}

/**
 * 表格单元格类型原型
 */
TableEditor.Table.Row.Cell.prototype = {
		rowSpan: 1, //{integer}跨行数
		colSpan: 1, //{integer}跨列数
		instanceId: '', //{integer}单元格的实例ID。简历单元格dom对象与js对象的关联关系
		available : true, //{boolean}有效的
		editable : true, //{boolean}单元格是可编辑
		hResizable : true, //{boolean}单元格水平可拖动宽度
		autoInstanceId : true, //{boolean}是否页面自动分配实例ID
		_selected : false, //{boolean}单元格已经选中
		
		element: null, //{jQuery}单元格jQuery对象
		row : null, //{TableEditor.Table.Row}单元格所在行对象
		tableEditor : null, //{TableEditor}表格编辑器对象
		mergedCell : null, //{TableEditor.Table.Row.Cell/function}合并后的单元格
		
		TD_SELECTED_CLASS : 'selected', //单元格选中类名
		
		fireEvent : TableEditor.prototype.fireEvent, 
		addListener : TableEditor.prototype.addListener,
		removeListener : TableEditor.prototype.removeListener,
		setConfig : TableEditor.prototype.setConfig,
		/**
		 * 获取单元格的列号（从零开始计数）
		 * @return {integer} 单元格的列号
		 * @public
		 */
		getColNumb : function(){
			for(var i = 0; i < this.row.cells.length; i++){
				if(this == this.row.cells[i]){
					return i;
				}
			}
			return -1;
		},
		/**
		 * 设置单元格是否可拖动宽度
		 * @param {boolean} dragWidth (必选)单元格是否可拖动宽度
		 * @return {void}
		 * @private
		 */
		setDragWidth : function(dragWidth){
			if(!this.hResizable){ //单元格不可水平拖动宽度
				return false;
			}
			if(!this._dragWidth && dragWidth){
				if(!this._$widthDragHelper){ //创建拖动宽度辅助对象
					var $ = jQuery, thiz = this; 
					this._$widthDragHelper = $('<div class="width-drag-helper"/>').draggable({
						cursor: 'col-resize',
						helper: 'clone',
						appendTo: $('div.table-container', this.tableEditor.element),
						containment: $('div.table-container', this.tableEditor.element),
						revertDuration : 10,
						cursorAt: {left: 11},
						revert: true,
						start : function(event, ui){//开始拖拽宽度
							//设置当前单元格位选中单元格
							var _cn = thiz.getColNumb(), //单元格的起始列号
							_rn = thiz.row.getRowNumb(); //单元格的起始行号
							thiz.tableEditor.getSelectedCells().setSelectedCells(_cn, _cn + thiz.colSpan - 1, _rn, _rn + thiz.rowSpan - 1);
							//显示单元格标尺的游标
							thiz.tableEditor.getSelectedCells().cellRuler.showMark();
							thiz.__startCellWidth = thiz.tableEditor.getSelectedCells().cellRuler.width;
							if(jQuery.browser.msie){
								thiz.tableEditor.__documentNoClick = true;
							}
						},
						drag : function(event, ui){ //拖动宽度过程中
							//更新单元格标尺宽度
							thiz.tableEditor.getSelectedCells().cellRuler.setWidth(parseInt(thiz.__startCellWidth + ui.position.left - ui.originalPosition.left, 10));
						},
						stop : function(event, ui){ //拖动宽度完成
							var _increase = thiz.tableEditor.getSelectedCells().cellRuler.width - thiz.__startCellWidth; //单元格所在列新增宽度
							if(_increase != 0){
								//设置单元格最终宽度
								thiz.tableEditor.getSelectedCells().changeColWidth(_increase);
							}
							//隐藏单元格标尺游标
							thiz.tableEditor.getSelectedCells().cellRuler.hideMark();
						}
					}).prependTo(this.$tdWrapper);
					// 销毁创建的页面对象
					this.addListener('beforeremoveelement', function(){
						this._$widthDragHelper.draggable('destroy');
						this._$widthDragHelper.remove();
						delete this._$widthDragHelper;
					});
				}else{//显示拖拽宽度辅助对象
					this._$widthDragHelper.show();
				}
				this._dragWidth = true;
			}else if(this._dragWidth && !dragWidth){ //隐藏拖拽宽度辅助对象
				this._$widthDragHelper.hide();
				this._dragWidth = false;
			}
		},
		/**
		 * 选中/取消选中单元格
		 * @param {boolean} select (必须)是否是选中单元格。true-选中单元格，false-取消选中单元格
		 * @return {boolean} 操作是否成功。false-单元格不可编辑，选中/取消选中操作不成功
		 * @public
		 */
		select : function(select){
			if(!this.editable || !this.available){ //单元格不可编辑或是占位单元格
				return false;
			}
			if(!this._selected && select){ //选中单元格
				this.element.addClass(this.TD_SELECTED_CLASS);
				this._selected = true;
			}else if(this._selected && !select){ //取消选中单元格
				this.element.removeClass(this.TD_SELECTED_CLASS);
				this._selected = false;
			}
			return true;
		},
		/**
		 * 获取单元格宽度
		 * @return {integer} 返回单元格的宽度，单位像素
		 * @public
		 */
		getWidth : function(){
			var _width = 0, _cn = this.getColNumb();
			for(var i = 0; i < this.colSpan; i++){
				_width += this.tableEditor.colWidths[_cn + i];
			}
			return _width;
		},
		/**
		 * 设置单元格的列跨度
		 * @param {integer} colSpan (必选)单元格新的列跨度
		 * @return {void}
		 * @public
		 */
		setColSpan : function(colSpan){
			this.element.attr('colSpan', colSpan);
			this.colSpan = colSpan;
		},
		/**
		 * 单元格被合并后，将当前单元格对象设置为占位单元格，即不可用单元格
		 * @param {TableEditor.Table.Row.Cell} mergedCell (必选)当前单元格合并后的单元格对象
		 * @return {void}
		 * @public
		 */
		changeToNone : function(mergedCell){
			this.available = false;
			this.mergedCell = mergedCell;
			//将被当前单元格合并的单元格转移到合并此单元格后的单元格
			for(var i = 0; i < this.rowSpan; i++){
				for(var j = 0; j < this.colSpan; j++){
					if(i != 0 || j != 0){
						this.row.table.getCell(this.row.getRowNumb() + i, this.getColNumb() + j).mergedCell = mergedCell;
					}
				}
			}
			//设置合并后单元格对象的列跨度或行跨度属性
			if(this.row == mergedCell.row){ //同一行单元格
				mergedCell.setColSpan(mergedCell.colSpan + this.colSpan);
			}else if(this.getColNumb() == mergedCell.getColNumb()){ //同一列单元格
				mergedCell.setRowSpan(mergedCell.rowSpan + this.rowSpan);
			}
			//处理将移除单元格页面元素内部的html串
			if(this.$tdContent.html() != ''){
				if(mergedCell.$tdContent.html() == ''){
					mergedCell.$tdContent.html(this.$tdContent.html());
				}else{
					mergedCell.$tdContent.html(mergedCell.$tdContent.html() + this.$tdContent.html());
				}
			}
			//移除页面元素
			this.removeElement();
		},
		/**
		 * 移除单元格页面元素
		 * @return {void}
		 * @private
		 */
		removeElement : function(){
			this.fireEvent('beforeremoveelement');
			this.$tdContent.remove();
			delete this.$tdContent;
			this.$tdWrapper.remove();
			delete this.$tdWrapper;
			this.element.remove();
			delete this.element;
			this.fireEvent('afterremoveelement');
		},
		/**
		 * 设置单元格的行跨度
		 * @param {integer} rowSpan (必选)单元格新的行跨度
		 * @return {void}
		 * @public
		 */
		setRowSpan : function(rowSpan){
			this.element.attr('rowSpan', rowSpan);
			this.rowSpan = rowSpan;
			this.element.css('height', (20 * rowSpan) + 'px'); //修改最小高度
		},
		/**
		 * 销毁对象
		 * @return {void}
		 * @public
		 */
		destroy : function(){
			this.fireEvent('beforedestroy');
			if(this.mergedCell){ //被合并的占位单元格
				delete this.mergedCell;
			}else if(this.element){ //需要销毁的页面元素。
				this.removeElement();
			}
			delete this.row;
			delete this.tableEditor;
			this.fireEvent('afterdestroy');
		},
		/**
		 * 初始化单元格对象
		 * @return {void}
		 * @public
		 */
		_init : function(){
			var thiz = this;
			if(this.autoInstanceId && this.available){ //页面分配实例ID
				this.instanceId = this.tableEditor.newInstanceId();
			}
			if(this.element){ //设置单元格页面元素的instanceId属性
				this.element.attr('instanceId', this.instanceId);
			}
			//处理合并后的单元格对象
			if(this.mergedCell && typeof this.mergedCell == 'function'){
				this.mergedCell = this.mergedCell.call(this);
			}
			
			if(this.available){
				//this.element.resizable({});
				//将单元格内容移入单元格容器中
				var _innerHTML = this.element.html();
				this.$tdWrapper = jQuery('<div class="td-wrapper" />').appendTo(this.element.empty());
				this.$tdContent = jQuery('<div class="td-content">' + (_innerHTML == '&nbsp;' ? '' : _innerHTML) + '</div>').appendTo(this.$tdWrapper);
				//页面单元格元素单击事件。如果当前单元格没有选中，选中当前单元格
				//this.element.click(function(){
				//	var _cn = thiz.getColNumb(), //单元格起始列号
				//		_rn = thiz.row.getRowNumb(); //单元格起始行号
					//if(!thiz.tableEditor.getSelectedCells().inSelectedCells(_cn, _rn)){
				//	thiz.tableEditor.getSelectedCells().setSelectedCells(_cn, _cn + thiz.colSpan - 1, _rn, _rn + thiz.rowSpan - 1);
					//}
				//});
				//监听表格列宽度发生改变，设置单元格宽度
				//var _listener = function(cols){
				//	if(thiz.available && thiz.inCols(cols)){
				//		thiz.fixWidth();
				//	}
				//};
				//this.tableEditor.addListener('colwidthchanged', _listener);
				//监听表格单元格数目发生变化
				var _cellChangeListener = function(){
					if(thiz.available){
						//thiz.$tdContent.html('(' + thiz.row.getRowNumb() + ',' + thiz.getColNumb() + ') ' + 'rowSpan: ' + thiz.rowSpan + '; colSpan: ' + thiz.colSpan);
						if(thiz.hResizable){ //列宽度可变的
							//如果单元格为非最后一列单元格，则单元格可拖动宽度
							if(thiz.getColNumb() + thiz.colSpan - 1 != thiz.row.cells.length - 1){
								thiz.setDragWidth(true);
							}else{
								thiz.setDragWidth(false);
							}
						}
						thiz.$tdWrapper.css('height', parseInt(thiz.tableEditor.height * thiz.rowSpan / thiz.row.table.rows.length, 10) + 'px');
					}
				};
				this.row.table.addListener('cellchanged', _cellChangeListener);
				//销毁单元格对象时，移除编辑器中的监听
				this.addListener('beforedestroy', function(){
					//this.tableEditor.removeListener('colwidthchanged', _listener);
					this.row.table.removeListener('cellchanged', _cellChangeListener);
				});
			}
			
			//表格编辑器统一管理单元格（表格编辑器不需要管理占位单元格）
			if(this.available){
				this.tableEditor.addCell(this);
			}
		},
		/**
		 * 调整单元格的宽度
		 * @param {void}
		 * @public
		 */
		fixWidth : function(){
			//var _width = 0, _colNumb = this.getColNumb(); //单元格的起始列号
			//for(var i = 0; i < this.colSpan; i++){
			//	_width += this.tableEditor.colWidths[i + _colNumb];
			//}
			//this.element.css('width', _width + 'px');
		},
		/**
		 * 单元格在指定列范围中
		 * @param {Array} cols (必选)列号集合，即列的范围。
		 * @return {boolean} 单元格是否在指定列范围中
		 * @public
		 */
		inCols : function(cols){
			var _colNumb = this.getColNumb(); //单元格的起始列号
			for(var i = 0; i < cols.length; i++){
				if(_colNumb <= cols[i] && (_colNumb + this.colSpan > cols[i])){
					return true;
				}
			}
			return false;
		}
}