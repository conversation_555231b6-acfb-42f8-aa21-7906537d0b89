package com.chevron.mktci.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.sys.workflow.model.WorkflowInstance;
import com.sys.workflow.model.WorkflowStep;
import com.sys.workflow.utils.Action;
import com.sys.workflow.utils.ActionTheme;
import com.sys.workflow.utils.ActionType;
import com.sys.workflow.utils.DownloadConfig;
import com.sys.workflow.utils.GetConfig;
import com.sys.workflow.utils.IWorkflowHandleBean;
import com.sys.workflow.utils.PostConfig;
import com.sys.workflow.utils.ResponseType;
import com.sys.workflow.utils.WorkflowType;

public class WorkflowHandler implements IWorkflowHandleBean {

	@Override
	public List<Action> getTodoListActions(WorkflowInstance workflowInstance, Long executor) throws WxPltException {
		List<Action> actions = new ArrayList<Action>(5);
		Action action = null;
		//审批按钮
		if(workflowInstance.getAcceptFlag() == 1) {
			action = new Action(ActionType.GET, workflowInstance.getListOperateName());
			action.setActionTheme(ActionTheme.ACCEPT);
			action.setConfig(new GetConfig(new StringBuilder("SPA/resource-application/index.jsp#/cio/apply/")
					.append(workflowInstance.getFormKey()).append("?stepcode=")
					.append(workflowInstance.getCurrentStep()).append("&submited=")
					.append(WorkflowStep.STEP_CODE_REQUEST.equals(workflowInstance.getCurrentStep()) ? "" : "1")
					.append("&executor=").append(executor == null ? "" : executor)
					.append("&v=").append(DateUtil.getCurrentDate("yyyyMMddHHmmss")).toString()));
			actions.add(action);
		}
		//查看按钮
		if("Y".equals(workflowInstance.getInstanceExtProperty5())){
			action = new Action(ActionType.DOWNLOAD, "导出");
			action.setActionTheme(ActionTheme.DOWNLOAD);
			DownloadConfig config = new DownloadConfig("mkt/finalPDF.do?id=" + workflowInstance.getFormKey() + "&v=" + System.currentTimeMillis());
//			Map<String, Object> params = new HashMap<String, Object>();
//			params.put("id", workflowInstance.getFormKey());
//			config.setParams(params);
			action.setConfig(config);
			actions.add(action);
		}
		actions.add(buildPcViewAction(workflowInstance, executor, true));
		//删除草稿按钮
		if(workflowInstance.getStatus() == WorkflowInstance.STATUS_DRAFT && workflowInstance.getCreateUserId().equals(ContextUtil.getCurUserId())) {
			actions.add(buildDelAction(workflowInstance));
		}
		return actions;
	}
	
	protected Action buildPcViewAction(WorkflowInstance workflowInstance, Long executor, boolean byDone) throws WxPltException {
		Action action = new Action(ActionType.GET, "查看");
		action.setActionTheme(ActionTheme.VIEW);
		action.setConfig(new GetConfig(new StringBuilder("SPA/resource-application/index.jsp#/cio/apply/")
				.append(workflowInstance.getFormKey()).append("?stepcode=")
				.append(workflowInstance.getCurrentStep()).append("&submited=")
				.append((!byDone && WorkflowStep.STEP_CODE_REQUEST.equals(workflowInstance.getCurrentStep())) ? "" : "1")
				.append("&executor=").append(executor == null ? "" : executor)
				.append("&view=1&v=").append(DateUtil.getCurrentDate("yyyyMMddHHmmss")).toString()));
		return action;
	}
	
	protected Action buildDelAction(WorkflowInstance workflowInstance) throws WxPltException {
		Action action = new Action(ActionType.POST, "删除草稿");
		action.setActionTheme(ActionTheme.DELETE);
		action.setAjax(true);
		action.setResponseType(ResponseType.RELOAD);
		PostConfig config = new PostConfig("mktciapply/delDraft.do");
		Map<String, Object> params = new HashMap<String, Object>(5);
		params.put("id", workflowInstance.getFormKey());
		config.setParams(params);
		action.setConfig(config);
		return action;
	}
	
	protected Action buildAppViewAction(WorkflowInstance workflowInstance, boolean byDone) throws WxPltException {
		Action action = new Action(ActionType.POST, "查看");
		action.setActionTheme(ActionTheme.VIEW);
		PostConfig config = new PostConfig("CIOResourceApplicationDetail");
		Map<String, Object> params = new HashMap<String, Object>(5);
		params.put("stepcode", workflowInstance.getCurrentStep());
		params.put("submited", byDone || !WorkflowStep.STEP_CODE_REQUEST.equals(workflowInstance.getCurrentStep()));
		params.put("editable", false);
		params.put("id", workflowInstance.getFormKey());
		params.put("title", WorkflowType.COMMERCIAL_SIGNAGE.getTitle());
		config.setParams(params);
		action.setConfig(config);
		return action;
	}

	@Override
	public List<Action> getAppTodoListActions(WorkflowInstance workflowInstance, Long executor) throws WxPltException {
		List<Action> actions = new ArrayList<Action>(5);
		Action action = null;
		if(workflowInstance.getAcceptFlag() == 1) {
			action = new Action(ActionType.POST, workflowInstance.getListOperateName());
			action.setActionTheme(ActionTheme.ACCEPT);
			PostConfig config = new PostConfig("CIOResourceApplicationDetail");
			Map<String, Object> params = new HashMap<String, Object>(5);
			params.put("stepcode", workflowInstance.getCurrentStep());
			params.put("submited", !WorkflowStep.STEP_CODE_REQUEST.equals(workflowInstance.getCurrentStep()));
			params.put("editable", true);
			params.put("id", workflowInstance.getFormKey());
			params.put("title", WorkflowType.COMMERCIAL_SIGNAGE.getTitle());
			config.setParams(params);
			action.setConfig(config);
			actions.add(action);
		}
		actions.add(buildAppViewAction(workflowInstance, false));
		if(workflowInstance.getStatus() == WorkflowInstance.STATUS_DRAFT && workflowInstance.getCreateUserId().equals(ContextUtil.getCurUserId())) {
			actions.add(buildDelAction(workflowInstance));
		}
		return actions;
	}

	@Override
	public List<Action> getDoneListActions(WorkflowInstance workflowInstance, Long executor) throws WxPltException {
		List<Action> actions = new ArrayList<Action>(5);
		Action action = null;
		if(workflowInstance.getRecallFlag() == 1) {
			action = buildPcViewAction(workflowInstance, executor, true);
			action.setActionName("撤回");
			action.setActionTheme(ActionTheme.RECALL);
			actions.add(action);
		}
		actions.add(buildPcViewAction(workflowInstance, executor, true));
		return actions;
	}

	@Override
	public List<Action> getAppDoneListActions(WorkflowInstance workflowInstance, Long executor) throws WxPltException {
		List<Action> actions = new ArrayList<Action>(5);
		Action action = null;
		if(workflowInstance.getRecallFlag() == 1) {
			action = buildAppViewAction(workflowInstance, true);
			action.setActionName("撤回");
			action.setActionTheme(ActionTheme.RECALL);
			actions.add(action);
		}
		actions.add(buildAppViewAction(workflowInstance, true));
		return actions;
	}

}
