package com.chevron.b2b.business;

import com.chevron.b2b.model.Schedule;
import com.chevron.b2b.model.ScheduleExample;
import com.chevron.b2b.model.ScheduleParams;
import com.common.exception.WxPltException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计划业务接口
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
public interface ScheduleBizService {
	
	/**
	 * 保存计划
	 * @param record 被插入计划
	 * @throws WxPltException
	 */
	public void insert(Schedule record) throws WxPltException;
	
	/**
	 * 修改计划
	 * @param record 被修改计划
	 * @throws WxPltException
	 */
	public void update(Schedule record) throws WxPltException;
	
	/**
	 * 删除计划
	 * @param ids 被删除计划id集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的计划
	 * @param example 被删除计划满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(ScheduleExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<Schedule> queryByExample(ScheduleExample example) throws WxPltException;
	
	/**
	 * 列表查询计划对象
	 * @param params 查询条件
	 * @return 满足条件计划对象集合
	 */
    public List<Schedule> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 获取指定主键的计划对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public Schedule getBean(Long id) throws WxPltException;	

	/**
	 * 分页查询计划
	 * @param params 查询参数
	 * @param resultMap 结果对象
	 * @throws WxPltException
	 */
	public void queryForPage(ScheduleParams params, Map<String, Object> resultMap) throws WxPltException;
	
	public void publishSchedule(Schedule record, List<Long> assignDistributors, List<Long> assignWorkshops) throws WxPltException;

	public void publishSchedule(Schedule record, Date publishTime, Long publishUser) throws WxPltException;
}
