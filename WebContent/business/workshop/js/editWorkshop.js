var foo = new $.JsonRpcClient({
	ajaxUrl : '/wxPublicRpc.do'
});
var selectStatus = {}, workshopName = '';
var attFileList = [], rewardCtracts = [];
var verificationRule = null, skuDistributionRules = null, locationConfirmedFlag = -1;
var validators = [], cdmValidators = [{field: 'joinShopGiftPlan', validator: 'notempty', errorMsg: '请选择是否参与雪佛龙养护大咖汇计划'},
	{field: 'joinLocationPlan', validator: 'notempty', errorMsg: '请选择是否参与门店定位计划'},
	{field: 'joinCkPlan', validator: 'notempty', errorMsg: '请选择是否加入CK合作门店计划'}];

var form ; //全局表单
$(document).ready(function(){
	bindBackspaceUrl('business/workshop/workshopPage.jsp?cacheParams=true&toItem=' + workshopId);
//	if(isEdit){
//		BUI.use('bui/form',function(Form){
//			//设置全局表单对象
//		     form =  new Form.Form({
//		        srcNode : '#editForm'
//		      }).render();
//		  });
//	}
	initContractUploader();
	initRewardContractUploader();
	if(workshopId){
		LoadMask.show();
		setTimeout(function(){
			foo.call('workShopServiceImpl.getWorkshopForUpdateByWorkshopId',[workshopId],
					function(result) {
						LoadMask.hide();
						if("success" == result.code){ 
							if(result.mWorkShopVo.status != '-10'){
								$('.field-wlabel1').removeClass('field-limit');
								for(var i = 0; i < cdmValidators.length; i++){
									validators.push(cdmValidators[i]);
								}
							}
							if(isEdit /*&& result.mWorkShopVo.status == '-10' && (permissionWeight == -1 || (permissionWeight & 1) > 0)*/){
								//定位确认
								$('#locationFun').removeClass('hide-important');
								if((result.mWorkShopVo.extFlag & 1) == 0){
									locationConfirmedFlag = 0;
								}
							}
							//录店设置默认执行人
							if(subTaskId && !result.mWorkShopVo.excuteUserId){
								result.mWorkShopVo.excuteUserId = excuteUserId;
							}
							if(result.mWorkShopVo.status!='-10'){
								$('.field-source1').removeClass('field-limit');
							}
							initUpdate(result.mWorkShopVo);
							initDistributionRule(result.distributionRule);
							selectStatus = {
								provId: result.mWorkShopVo.provinceId,
								cityId: result.mWorkShopVo.cityId,
								distId: result.mWorkShopVo.regionId,
								partnerIds: result.mWorkShopVo.partnerIds
							};
							skuDistributionRules = result.skuDistributionRules;
							initSource();
							initSignedType();
							initType();
							initScale();
							initCtrls();
							initProv(result.mWorkShopVo);
							initCity(result.mWorkShopVo);
//							alert($('#regionId').val());
							initDist(result.mWorkShopVo);
//							alert($('#regionId').val());
							if(userType == 1){
								initPartners(result.mWorkShopVo);
							}else{
								$('#partnersField').hide();
							}
							refreshBDCtrl();
							initUpload();
							workshopName = result.mWorkShopVo.workShopName;
							$('#headerTitle').text($('#headerTitle').text() + ' - ' + workshopName);
							if(!isEdit){
								initView();
								var mechanicList = [], ownerList = [];
								for(var i = 0; i < result.mechainVoLst.length; i++){
									var item = result.mechainVoLst[i];
									if(item.employeeType == 'Owner'){
										ownerList.push(item);
									}else{
										mechanicList.push(item);
									}
								}
								initMechanicGrid(mechanicList, '#mechanic_list');
								initMechanicGrid(ownerList, '#manager_list');
							}
							initExistsContract(result.mWorkShopVo.id);
							initExistsRewardContract(result.mWorkShopVo.id);
							refreshDistributionStatus(result.mWorkShopVo.partnerIds);
							$('#activeFeeFlag').val(result.mWorkShopVo.activeFeeFlag == 1 ? '已发放' : '未发放');
							$('#managementFeeFlag').val(result.mWorkShopVo.managementFeeFlag ? result.mWorkShopVo.managementFeeFlag : 0);
							$('#activeFeeFlag_div').show();
							$('#managementFeeFlag_div').show();
							
							$('#monthlyOilSalesVolume').val(result.mWorkShopVo.monthlyOilSalesVolume ? result.mWorkShopVo.monthlyOilSalesVolume : 0);
                            $('#monthlyOilSalesVolume_div').show();
							$('#monthlyChevronOilSalesVolume').val(result.mWorkShopVo.monthlyChevronOilSalesVolume ? result.mWorkShopVo.monthlyChevronOilSalesVolume : 0);
                            $('#monthlyChevronOilSalesVolume_div').show();
//							alert($('#regionId').val());
						}else{
							common.alertMes("加载修改的门店信息失败", 'error', function(){
								window.location = common.ctx + 'business/workshop/workshopPage.jsp';
							});
						}
				},  function(error) {
					common.ajaxTimeout(error);
					LoadMask.hide();
				});
		}, 0);
	}else{
		initSource();
		initSignedType();
		initType();
		initScale();
		initProv();
		initCity();
		initDist();
		initCtrls();
		if(userType == 1){
			//chevron新建
			initPartners();
		}else{
			//sp新建
			$('#partnersField').hide();
			$('#partnerIds').val(loginOrgId);
			refreshDistributionStatus(loginOrgId);
		}
		refreshBDCtrl();
		initUpload();
	}
});

function initCtrls(){
	//初始化其他设备
	Ctrls.DicItem.init('#otherEquipment', 'workshop.equipment', {
		canEmpty: false,
    	defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化其他设置
	Ctrls.DicItem.init('#otherCollocation', 'workshop.collocation', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化运营时间
	Ctrls.DicItem.init('#operationPeriods', 'workshop.operationPeriods', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:false
		}
	});
	//初始化节假日是否休息
	Ctrls.SimpleSelect.init('#isHolidayClose', [{"value": "1", "text": "是"}, {"value": "2", "text": "否"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化收款方式
	Ctrls.DicItem.init('#paymentType', 'workshop.paymentType', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:false
		}
	});
	//初始化有无兴趣提高数字化管理
	Ctrls.SimpleSelect.init('#digitalManagementIntention', [{"value": "1", "text": "有"}, {"value": "2", "text": "无"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化有无形象提升需求
	Ctrls.SimpleSelect.init('#levelPromotionIntention', [{"value": "1", "text": "有"}, {"value": "2", "text": "无"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化服务类型
	Ctrls.DicItem.init('#serviceScope', 'workshop.serviceScope', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化门店授权经销商
	Ctrls.DicItem.init('#authProductAgency', 'workshop.authProductAgency', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化销售哪种粘度的机油较多
	Ctrls.DicItem.init('#saleMajorOilViscosity', 'product.viscosity', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化销售哪种类型的油比较多
	Ctrls.DicItem.init('#saleMajorOilType', 'product.oilTypeText', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化销售的主要机油品牌
	Ctrls.DicItem.init('#saleMajorOilBrand', 'product.brand', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化是否连锁
	Ctrls.SimpleSelect.init('#isMultiple', [{"value": "1", "text": "是"}, {"value": "2", "text": "否"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化周边小区数量
	Ctrls.DicItem.init('#nearbyVillageNumb', 'workshop.nearbyVillageNumb', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:false
		}
	});
	//初始化引流方式
	Ctrls.DicItem.init('#spreadWay', 'workshop.marketingWay', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:true
		}
	});
	//初始化有无营销概念
	Ctrls.SimpleSelect.init('#hasMarketingConcept', [{"value": "1", "text": "有"}, {"value": "2", "text": "无"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化有无仓库
	Ctrls.SimpleSelect.init('#hasWarehouse', [{"value": "1", "text": "有"}, {"value": "2", "text": "无"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化管理成熟度
	Ctrls.DicItem.init('#managementMaturity', 'workshop.managementMaturity', {
		canEmpty: false,
		defaultItemIndex: -1,
		ctrlOpts: {
        	disabled: !isEdit,
	        multipleSelect:false
		}
	});
	//初始化是否店招
	Ctrls.SimpleSelect.init('#shopRecruitment', [{"value": "1", "text": "是"}, {"value": "0", "text": "否"}],{ctrlOpts: {disabled: !isEdit}, events: {
		'change': function(e){
			if(userId == 1 && e.value == '1'){
				$('.recruitment-property').removeClass('hide-important');
			}else if(userId == 1 && e.value == '0'){
				$('.recruitment-property').addClass('hide-important');
			}
			approvalCtrl.selectedValue = e.value;
			if(e.value == '1'){
				updateRegionPointCtrl(true);
			}else{
				updateRegionPointCtrl(false);
			}
	    }
	}});
	//初始化是否店招已确认
	Ctrls.SimpleSelect.init('#recruitmentConfirmed', [{"value": "1", "text": "是"}, {"value": "0", "text": "否"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化是否参与雪佛龙店长技师兑礼计划
	Ctrls.SimpleSelect.init('#joinShopGiftPlan', [{"value": "1", "text": "是"}, {"value": "0", "text": "否"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化是否参与门店定位计划
	Ctrls.SimpleSelect.init('#joinLocationPlan', [{"value": "1", "text": "是"}, {"value": "0", "text": "否"}],{ctrlOpts: {disabled: !isEdit}});
	//初始化是否是否加入CK合作门店计划
	Ctrls.SimpleSelect.init('#joinCkPlan', [{"value": "1", "text": "是"}, {"value": "0", "text": "否"}],{ctrlOpts: {disabled: !isEdit}});
}

function initView(){
	BUI.use(['bui/tab','bui/mask'],function(Tab){
        var tab = new Tab.TabPanel({
          render : '#tab',
          elCls : 'nav-tabs',
          panelContainer : '#tab_content',//如果内部有容器，那么会跟标签项一一对应，如果没有会自动生成
          //selectedEvent : 'mouseenter',//默认为click,可以更改事件
          autoRender: true,
          children:[
            {title:'门店信息',value:'1',selected : true},
            {title:'任务信息',value:'2'},
            {title:'技师',value:'5'},
            {title:'店长',value:'6'}/*,
            {title:'门店用户',value:'8'},
            {title:'门店核销',value:'7'},
            {title:'订单信息',value:'3'},
            {title:'库存信息',value:'4'}*/
          ]
        });
        tab.on('selectedchange', function(ev){
        	$(window).trigger('resize');
        });
	});
	initTaskList();
//	initManagerList();
//	initOrderList();
//	initInventoryList();
	$('#orgField').hide();
	loadUserList(null,null);
}
function initManagerList(){
	foo.call('wxUserService.findWorkshopManagers',[workshopId],
			function(result) {
				if(result.success){ 
					BUI.use(['bui/grid'],function(Grid){
						var columns = [
						           {title : '姓名',dataIndex :'chName', sortable: false, width:'30%'},
						           {title : '登录名',dataIndex :'loginName', sortable: false, width:'30%'},
						           {title : '门店',dataIndex :'', sortable: false, width:'40%', renderer: function(){return workshopName;}}/*,
						           {title : '性别',dataIndex :'sex', sortable: false, width:50, renderer: function(value){
						        	   if(value == 'M'){
						        		   return "男";
						        	   }else if(value == 'F'){
						        		   return "女";
						        	   }
						           }},
						           {title : '生日',dataIndex :'birthday', sortable: false, width:110,
										renderer : function (value,obj, index) {
							              return formatTimeMillis(value);
							        }},
						           {title : '手机号码',dataIndex :'mobile', sortable: false, width:100},
						           {title : '电子邮箱',dataIndex :'email', sortable: false, width:'20%'},
						           {title : '通讯地址',dataIndex :'address', sortable: false, width:'30%'}*/
						          ],
						    grid = new Grid.Grid({
								render:'#manager_list',
								columns : columns,
								items : result.data
							});
							grid.render();
							common.gridAutoResize(grid);
					});
				}else{
					common.alertMes("加载门店店长信息失败", 'error');
				}
		},  function(error) {
			common.ajaxTimeout(error);
		});
}
function initShopManager(userid){
	$('#shopManagerResult').hide();
	foo.call('wxUserService.getWxTUserInfoByUseridNew',[userid],function(result){
		if(null==result.resultData)
		{
			$('#shopManagerInfo').hide();
			$('#shopManagerResult').show().html('店长未录入');
		}else{
			$('#shopManagerInfo').show();
			$('#shopManagerResult').hide();
			
			$("#manager_info input[name='loginName']").val(result.resultData.loginName);
			$("#manager_info input[name='chName']").val(result.resultData.chName);
			$("#manager_info input[name='sex'][value='"+result.resultData.sex+"']").prop("checked",true);
			birthday = new Date();
			birthday.setTime(result.resultData.birthday);
			year = birthday.getFullYear();
			month = (parseInt(birthday.getMonth())+1);
			month = (month>=10)?month:"0"+month;
			date = birthday.getDate();
			date = (date>=10)?date:"0"+date;
			$("#manager_info input[name='birthday']").val(year + "-" + month + "-" + date);
			
			$("#manager_info input[name='address']").val(result.resultData.address);
			$("#manager_info input[name='email']").val(result.resultData.email);
			$("#manager_info input[name='mobile']").val(result.resultData.mobileTel);
		}
	});
}
var taskDataStore = null, 
	prioritis = {
		'1': '低',
		'2': '中',
		'3': '高',
		'4': '紧急'
};
function initTaskList(){
	
	BUI.use([ 'bui/grid', 'bui/data' ], function(_grid, _data) {
		var Grid = _grid;
		var Store = _data.Store;
		var columns = [ {
							title : '序号',
							dataIndex : '',
							width : 50,
							sortable: false,
							renderer : function (value,obj, index) {
								
					              return index + 1;
					        }
						},{
							title : '任务名称',
							dataIndex : 'taskName',
							width : '30%'
						},{
							title : '任务类型',
							dataIndex : 'tmbTypeName',
							width : '15%'
						}, {
							title : '开始日期',
							dataIndex : 'taskStartTime',
							width : '10%',
							renderer : function (value,obj, index) {
					              return formatTimeMillis(value);
					        }
						}, {
							title : '到期日期',
							dataIndex : 'taskFinishTime',
							width : '10%',
							renderer : function (value,obj, index) {
					              return formatTimeMillis(value);
					        }
						}, {
							title : '任务状态',
							dataIndex : 'taskStatus',
							elCls: 'text-center',
							width : '10%',
							renderer : function(value1,node){
								var value = common.taskMainStatusMap[value1];
								return value;
							}
						},{
							title : '优先级',
							dataIndex : 'taskPriority',
							width : '10%',
							renderer : function (value,obj, index) {
					              return prioritis[value];
					        }
						},{
							title : '创建人',
							dataIndex : 'createUserName',
							width : '15%'
						}, {
							title : '操作',
							dataIndex : 'taskMainId',
							width : 110,
							sortable: false,
							renderer : function (value,obj) {
								return '<a href=\"javascript:void(0);\" onclick="viewTask(\'' + value + '\', \'' + obj.tmbTypeCode + '\');">查看</a>'
					        }
						} ];

		taskDataStore = new Store({
			url: '/task/data.do',
			autoLoad:false,
			root: 'resultLst',
			totalProperty : 'total',
			remoteSort: true,
			sortField: 'taskStartTime',
			sortDirection: 'ASC',
			params: {workshopId: workshopId},
		    proxy : {
				method : 'post'
			},
			pageSize:10
		});
		var grid = new Grid.Grid({
				render : '#task_list',
				columns : columns,
				store : taskDataStore,
				bbar : {
					pagingBar : true
				}
			});
		grid.render();
		common.initGrid(grid, null, true);
	});
}

function viewTask(taskMainId, tmbTypeCode){
	var menuUrl;
	if(tmbTypeCode == 'TT_2_SD'){
		menuUrl = 'page/task/taskManage.jsp?tmbTypeCode=TT_2_SD';
	}else if(tmbTypeCode == 'TT_2_LD'){
		menuUrl = 'page/task/taskManage.jsp?tmbTypeCode=TT_2_LD';
	}else{
		menuUrl = 'page/task/taskManage.jsp?tmbTypeCode=TT_2_XD';
	}
	var url = common.ctx + 'page/task/subTaskManage.jsp?taskMainId=' + taskMainId + '&&taskTypeCodeTmp=' + tmbTypeCode;
	if(top.openMenuTab){
		top.openMenuTab(menuUrl, url);
	}else{
		window.location.href = url;
	}
}

var orderTableDataStore = null;
function initOrderList(){
	
	BUI.use([ 'bui/grid', 'bui/data' ], function(_grid, _data) {
		var Grid = _grid;
		var Store = _data.Store;
		var columns = [{
			title : '序号',
			dataIndex :'',
			sortable: false,
			width:80,
			renderer : function(value,obj,index){
				return index + 1;
        	}
		}, {
			title : '订单编号',
			dataIndex : 'orderNo',
			width : '25%'
		}, {
			title : '订单来源',
			dataIndex : 'source',
			width : '15%'
		}, {
			title : '订单总价',
			dataIndex : 'totalOrderPrice',
			elCls: 'text-right',
			width : '15%'
		}, {
			title : '订单状态',
			dataIndex : 'statusMeaning',
			width : '10%'
		}, {
			title : '创建时间',
			dataIndex : 'createTime',
			width : '15%',
			renderer : function (value,obj) {
				if(value){
					return formatTimeMillis(value);
				}
	            return "";
	        }
		}, {
			title : '创建人',
			dataIndex : 'creatorDisplayName',
			width : '20%'
		}/*, {
			title : '操作',
			dataIndex : 'id',
			sortable: false,
			width : '5%',
			renderer : function (value,obj) {
				var returnStr = '<a href="javascript:void(0);" onclick="showDetailDialog(false, \'' + obj.workshopName + '\', [' + obj.id + '])">查看</a>';
				if(obj.status == '0'){
					returnStr += ('&nbsp;<a href="javascript:void(0);" onclick="showDetailDialog(true, \'' + obj.workshopName + '\', [' + obj.id + '])">确认</a>');
				}
	            return returnStr;
	        }
		}*/ ];

		orderTableDataStore = new Store({
			url: '/order/queryOrders.do',
			autoLoad:true,
			totalProperty : 'total',
			remoteSort: true,
			sortField: 'orderNo',
			sortDirection: 'ASC',
			params: {workshopId: workshopId},
		    proxy : {
				method : 'post'
			},
			pageSize:10
		});
		var grid = new Grid.Grid({
				render : '#order_list',
				columns : columns,
				store : orderTableDataStore,
				bbar : {
					pagingBar : true
				}
			});
		grid.render();
	});
}
var inventoryStore = null;
function initInventoryList(){
		BUI.use(['bui/grid','bui/data'],function(Grid,Data){
		var Store = Data.Store,
		columns = [{
			title : '序号',
			dataIndex :'',
			sortable: false,
			width:80,
			renderer : function(value,obj,index){
				return index + 1;
        	}
		},
		{
			title : '产品名称',
			dataIndex :'productName',
			width:'50%'
		},
		{
			title : '产品类别',
			dataIndex :'productCategory',
			width:'30%'
		},
		{
			title : '库存数量',
			dataIndex :'quantity',
			width:'20%'
		}/*,
		{
			title:'操作',dataIndex:'id',width:'10%',sortable: false,renderer : function(value,obj){
			  return "<a onclick='showDetail(" + value + ")'>查看</a>";
          }}*/
		];
		
		inventoryStore = new Store({
			url: '/inventory/data.do',
			autoLoad:true,
			totalProperty : 'total',
			remoteSort: true,
			sortField: 'productName',
			sortDirection: 'ASC',
			params: {workshopId: workshopId},
		    proxy : {
				method : 'post'
			},
			pageSize:10
		});
		
		var grid = new Grid.Grid({
			render:'#inventory_list',
			columns : columns,
			loadMask: true, //加载数据时显示屏蔽层
			store : inventoryStore,
			bbar:{
				pagingBar:true,
				width:'100%'
			}
		});
		
		grid.render();
	});
}
function initSource(){
	  BUI.use('bui/select',function(Select){
		  var items = [];
		  if(sourceData){
			  for(var i = 0; i < sourceData.length; i++){
				  items.push({text: sourceData[i].dicItemName, value: sourceData[i].dicItemCode});
			  }
		  }
		  var select = new Select.Select({  
		          render:'#source_c',
		          valueField:'#source',
		          disabled: !isEdit,
		          width: '100%',
		          elStyle: {'white-space': 'nowrap'},
		          items:items
		        });
		    select.render();
		  });
}

function initSignedType(){
	  BUI.use('bui/select',function(Select){
		  var items = [];
		  if(sourceData){
			  for(var i = 0; i < signedTypeData.length; i++){
				  items.push({text: signedTypeData[i].dicItemName, value: signedTypeData[i].dicItemCode});
			  }
		  }
		        var select = new Select.Select({  
		          render:'#signedType_c',
		          valueField:'#signedType',
		          disabled: !isEdit,
		          width: '100%',
		          elStyle: {'white-space': 'nowrap'},
		          items:items
		        });
		    select.render();
		  });
}
function initType(){
	  BUI.use('bui/select',function(Select){
		  var items = [];
		  if(typeData){
			  for(var i = 0; i < typeData.length; i++){
				  items.push({text: typeData[i].dicItemName, value: typeData[i].dicItemCode});
			  }
		  }
		        var select = new Select.Select({  
		          render:'#type_c',
		          valueField:'#type',
		          disabled: !isEdit,
		          width: '100%',
		          elStyle: {'white-space': 'nowrap'},
		          items:items
		        });
		    select.render();
		  });
}
function initScale(){
	  BUI.use('bui/select',function(Select){
		  var items = [];
		  if(scaleData){
			  for(var i = 0; i < scaleData.length; i++){
				  items.push({text: scaleData[i].dicItemName, value: scaleData[i].dicItemCode});
			  }
		  }
		        var select = new Select.Select({  
		          render:'#scale_c',
		          valueField:'#scale',
		          disabled: !isEdit,
		          width: '100%',
		          elStyle: {'white-space': 'nowrap'},
		          items:items
		        });
		    select.render();
		  });
}

var partnersCtrl = null;
function initPartners(data){
	  BUI.use('bui/select',function(Select){
		  partnersCtrl = new Select.Select({  
		          render:'#partners_c',
		          valueField:'#partnerIds',
		          width: '100%',
		          disabled: !isEdit,
		          elStyle: {'white-space': 'nowrap'},
		          multipleSelect:true,
		          items:[]
		        });
		  partnersCtrl.render();
		  partnersCtrl.on('change', function(ev){
			  if(ev.value != selectStatus.partnerIds){
				  selectStatus.partnerIds = ev.value;
				  refreshBDCtrl();
				  refreshDistributionStatus(selectStatus.partnerIds);
			  }
		    });
		  if(data && data.regionId){
			  refreshPartners(data.regionId, true);
		  }
		  });
}
function refreshPartners(regionId, noReset){
	if(!partnersCtrl){
		//合伙人操作修改区县时 不处理
		return;
	}
	if(!regionId){
		partnersCtrl.set('items', []);
		return;
	}
	foo.call('workShopServiceImpl.getAllPartnerByRegionID',[regionId],
			function(result) {
				var items = [];
				if("success" == result.code){ 
					for(var i = 0; i < result.partnerLst.length; i++){
						items.push({text: result.partnerLst[i].organizationName, value: result.partnerLst[i].id});
					}
				}else{
					common.alertMes("加载" + $('#regionName') + "下合伙人失败", 'error');
				}
				partnersCtrl.set('items', items);
				if(!noReset){
					partnersCtrl.setSelectedValue('-1');
				}
		},  function(error) {
			common.ajaxTimeout(error);
		});
}

function refreshBDCtrl(){
	var partnerIds = $('#partnerIds').val();
	if(userType != 1 && partnerIds == ""){
		partnerIds = loginOrgId;
	}
	if(partnerIds == ''){
		if(bdCtrl == null){
			initBDCtrl([]);
		}
		return;
	}
	foo.call('workShopServiceImpl.getExcuteUsersByPartners',[partnerIds],
			function(result) {
				var items = [];
				if("success" == result.code){ 
					if(result.lstExcuteUsers){
						for(var i = 0; i < result.lstExcuteUsers.length; i++){
							items.push({text: result.lstExcuteUsers[i].chName, value: result.lstExcuteUsers[i].userId});
						}
					}
				}else{
					common.alertMes("加载可选执行人失败", 'error');
				}
				if(bdCtrl == null){
					initBDCtrl(items);
				}else{
					bdCtrl.set('items', items);
				}
		},  function(error) {
			common.ajaxTimeout(error);
		});
}
var bdCtrl = null;
function initBDCtrl(items){
	  BUI.use('bui/select',function(Select){
		    bdCtrl = new Select.Select({  
		          render:'#excuteUserId_c',
		          valueField:'#excuteUserId',
		          disabled: !isEdit,
		          width: '100%',
		          elStyle: {'white-space': 'nowrap'},
		          items:items
		        });
		    bdCtrl.render();
		  });
}

function initProv(){
	var provCtrl = Ctrls.Province.init('#prov', '#prov_c', {
		emptyValue: '-1',
		emptyText: '请选择省',
		autoLoad: true,
		ctrlOpts: {
			disabled: !isEdit
		},
		events: {
			'change': function(e){
		    	if(e.value != selectStatus.provId){
		    		selectStatus.provId = e.value;
		    		refreshCity(e.value);
		    	}
		    }
		}
	});
}

var cityCtrl = null;
function initCity(data){
	cityCtrl = Ctrls.City.init('#city', '#city_c', {
		emptyValue: '-1',
		emptyText: '请选择市',
		autoLoad: data && data.provinceId && data.provinceId != '-1' ? true : false,
		param: {provinceId: data ? data.provinceId : null},
		ctrlOpts: {
			disabled: !isEdit
		},
		events: {
			'change': function(ev){
		    	if(ev.value != selectStatus.cityId){
		    		selectStatus.cityId = ev.value;
		    		refreshDist(ev.value);
		    	}
		    }
		}
	});
	cityCtrl.ctrl.get('picker').on('show', function(){
		if($('#prov').val() == '-1' || $('#prov').val() == ''){
			common.alertMes('请先选择省', 'error');
		}
	});
}

var distCtrl = null;
function initDist(data){
	distCtrl = Ctrls.District.init('#regionId', '#region_c', {
		emptyValue: '-1',
		emptyText: '请选择区',
		autoLoad: data && data.cityId && data.cityId != '-1' ? true : false,
		param: {cityId: data ? data.cityId : null},
		ctrlOpts: {
			disabled: !isEdit
		},
		events: {
			'change': function(ev){
		    	if(ev.value != selectStatus.distId){
		    		selectStatus.distId = ev.value;
		    		refreshPartners(ev.value);
		    	}
		    }
		}
	});
	distCtrl.ctrl.get('picker').on('show', function(){
		if($('#city').val() == '-1' || $('#city').val() == ''){
			common.alertMes('请先选择市', 'error');
		}
	});
}

function refreshCity(provinceId, noReset){
	if(provinceId && provinceId != '-1'){
		cityCtrl.load({provinceId: provinceId});
	}else{
		cityCtrl.empty();
	}
}

function refreshDist(cityId, noReset){
	if(cityId && cityId != '-1'){
		distCtrl.load({cityId: cityId});
	}else{
		distCtrl.empty();
	}
}

function showPhoto(attId){
	var path = null;
	if(attId){
		path = common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' + attId;
	}else{
		path = common.ctx + 'images/wu.jpg';
	}
	$('#photoImg').attr('src', path);
	$('#photoImg').attr('onclick', 'showLargerImg(' + attId + ')');
	$('#photoImg').attr('onerror', 'imageError(this)');
}

function showLargerImg(attId){
	$("#file_img_cover").remove();
	var bodyCover = $('<div id="file_img_cover" ><a class="close" data-dismiss="modal" href="javascript: void(0);"></a></div>');
	$("body").append(bodyCover);
	bodyCover.show();
	$('.close', bodyCover).click(function(){
		bodyCover.remove();
	});
	var imgWrapper =  $('<div class="large-image-wrapper"></div>');
	var _left =  $('<div class="large-image-wrapper-left"></div>');
	var _right =  $('<div class="large-image-wrapper-right"></div>');

	var img = $('<img id="file_img_larger" title="点击关闭" alt="图片加载失败"'+
	' src="' + common.ctx + 'downloadAttachmentFile.do?attId=' + attId + '" onerror="imageError(this);"/>')
	.css({
		"max-width" :  $(window).width()*0.9-300 + 'px', 
		"max-height" : $(window).height()*0.9 + 'px',
	});
	
	img.load(function(){
		var rightOffset = 50;
		bodyAppend = imgWrapper.append(_left.append(img));
		
		bodyCover.append(bodyAppend)
		.click(function(e){
			if (img.is(e.target)) {
				bodyCover.remove();
			}
		});
		imgWrapper.css({
			"right": ($(window).width()-img.width()-rightOffset)/2,
			"top":  ($(window).height()-img.height()-20)/2,
		});
		event.stopPropagation();
		return false;
	});
}

function imageError(img) {
	var $img = $(img);
	if(!$img.attr("loaderror")){
		$img.attr('src', common.ctx + 'images/unsupport.jpg').attr('loaderror', true);
	}
}

function initUpload(){
	if(isEdit){
		$('#uploadPhoto_c').show();
	   BUI.use('bui/uploader',function (Uploader) {
			  var uploader = new Uploader.Uploader({
		      render: '#uploadPhoto_c',
		      url: common.ctx + 'uploadAttchmentFile.do',
		      name: 'myfiles',
		      button: {text: '上传图片'},
		      width: '100%',
		      multiple: false,
		      data: {sourceType:'8'},
		      // 可以直接在这里直接设置成功的回调
		      success: function(result){
		    	  LoadMask.hide();
		    	  var att = result.attachmentFileList[0];
		    	  $('#photoId').val(att.uuid);
		    	  showPhoto(att.attId);
		    	  return false;
		      },
		      change : function(e){
		    	  console.log(e);
		      },
		      isSuccess : function(result){
		    	  if(result.code == "success"){
		    	      return true;
		    	  }
		    	  return false;
		      },
		      // 失败的回调
		      error: function(result){
		    	  LoadMask.hide();
		    	  common.alertMes('门头照上传失败','error');
		      }
		    });
			  uploader.render();
			  uploader.on('start',function(){
		    	  LoadMask.show();
		      });
			  $('#uploadPhoto_c .bui-simple-list').hide();
			  $('#uploadPhoto_c .bui-uploader-button').append('<span class="field-comments" style="line-height: 28px; vertical-align: top;">上传图片将覆盖当前门头照</span>');
		  });
	}
}

function initContractUploader(){
	if(isEdit){
		   BUI.use('bui/uploader',function (Uploader) {
				  var uploader = new Uploader.Uploader({
			      render: '#uploadContract_c',
			      url: common.ctx + 'uploadAttchmentFile.do',
			      name: 'myfiles',
//			      button: {text: '上传图片'},
			      width: '100%',
//			      disabled: !isEdit,
			      multiple: false,
			      data: {sourceType:'9', sourceId: workshopId},
			      // 可以直接在这里直接设置成功的回调
			      success: function(result){
			    	  for(var i = 0; i < result.attachmentFileList.length; i++){
			    		  attFileList.push(result.attachmentFileList[i]);
			    	  }
			    	  refreshAttResult();
			    	  return false;
			      },
			      change : function(e){
//			    	  console.log(e);
			      },         
			      isSuccess : function(result){
			    	  if(result.code == "success"){
			    	      return true;
			    	  }
			    	  return false;
			      },
			      // 失败的回调
			      error: function(result){
			    	  common.alertMes('合同上传失败','error');
			    	  refreshAttResult();
			      }
			    });
				  uploader.render();
			  });
		}else{
			$('#uploadContract_c').append('<div class="bui-uploader defaultTheme" style="width: 100%;" aria-disabled="false" aria-pressed="false"><div class="bui-queue bui-simple-list"><ul></ul></div></div>');
		}
}

function initRewardContractUploader(){
	if(isEdit){
		   BUI.use('bui/uploader',function (Uploader) {
				  var uploader = new Uploader.Uploader({
			      render: '#rewardContract_c',
			      url: common.ctx + 'uploadAttchmentFile.do',
			      name: 'myfiles',
//			      button: {text: '上传图片'},
			      width: '100%',
			      multiple: false,
//			      disabled: !isEdit,
			      data: {sourceType:'11', sourceId: workshopId},
			      // 可以直接在这里直接设置成功的回调
			      success: function(result){
			    	  for(var i = 0; i < result.attachmentFileList.length; i++){
			    		  rewardCtracts.push(result.attachmentFileList[i]);
			    	  }
			    	  refreshRewardContractResult();
			    	  return false;
			      },
			      change : function(e){
//			    	  console.log(e);
			      },         
			      isSuccess : function(result){
			    	  if(result.code == "success"){
			    	      return true;
			    	  }
			    	  return false;
			      },
			      // 失败的回调
			      error: function(result){
			    	  common.alertMes('合同上传失败','error');
			    	  refreshRewardContractResult();
			      }
			    });
				  uploader.render();
			  });
		}else{
			$('#rewardContract_c').append('<div class="bui-uploader defaultTheme" style="width: 100%;" aria-disabled="false" aria-pressed="false"><div class="bui-queue bui-simple-list"><ul></ul></div></div>');
		}
}

function initExistsContract(workshopId){
	foo.call('fileManagerService.findAttsBySource', [workshopId, '9'], function (result) {
		if (result.success) {
			attFileList = result.data;
			refreshAttResult();
		} else {
			common.alertMes('加载门店合同失败', 'error');
		}
	}, function (error) {
		common.ajaxTimeout(error);
	});
}

function initExistsRewardContract(workshopId){
	foo.call('fileManagerService.findAttsBySource', [workshopId, '11'], function (result) {
		if (result.success) {
			rewardCtracts = result.data;
			refreshRewardContractResult();
		} else {
			common.alertMes('加载门店服务费合同失败', 'error');
		}
	}, function (error) {
		common.ajaxTimeout(error);
	});
}
function refreshAttResult(){
	var liContent = [];
	$.each(attFileList, function (i, value) {
		var fileA = createAttFilesLink(value);
		liContent.push('<li class="bui-queue-item bui-queue-item-success">', fileA);
		if(isEdit){
			liContent.push('<span class="action"><a href="javascript:void(0);" class="" onclick="removeAttId(', value.attId,', ', value.sourceType, ', this)">删除</a></span>');
		}
		liContent.push('</li>');
	});
	$("#uploadContract_c").find("ul").empty().append(liContent.join(''));
}
function refreshRewardContractResult(){
	var liContent = [];
	$.each(rewardCtracts, function (i, value) {
		var fileA = createAttFilesLink(value);
		liContent.push('<li class="bui-queue-item bui-queue-item-success">', fileA);
		if(isEdit){
			liContent.push('<span class="action"><a href="javascript:void(0);" class="" onclick="removeAttId(', value.attId,', ', value.sourceType, ', this)">删除</a></span>');
		}
		liContent.push('</li>');
	});
	$("#rewardContract_c").find("ul").empty().append(liContent.join(''));
}
function createAttFilesLink(attFileObj) {
	return '<a href=\"/downloadAttachmentFile.do?attId=' + attFileObj.attId + '&sourceType=' + attFileObj.sourceType + '\" style="margin-right:10px">' + attFileObj.fileName + '</a>';
}
function removeAttId(attId, sourceType, el) {
	foo.call('fileManagerService.deleteAttFile', [attId], function (result) {
		if (result.code != "success") {
			common.alertMes('删除附件失败', 'error');
		} else {
			var fileList = sourceType == 11 ? rewardCtracts : attFileList;
			$.each(fileList, function (i, value) {
				if (value != undefined && value.attId == attId) {
					fileList.splice(i, 1);
					return false;
				}
			});
			$(el).parents('li.bui-queue-item:first').remove();
		}
	}, function (error) {
		common.ajaxTimeout(error);
	});
}
function initMechanicGrid(items, render){
	BUI.use(['bui/grid'],function(Grid){
		var columns = [
		           {title : '姓名',dataIndex :'name', sortable: false, width:'30%'},
//		           {title : '微信号',dataIndex :'wechatAccount', sortable: false, width:'30%'},
		           {title : '电话',dataIndex :'mobile', sortable: false, width:'30%'},
		           {title : '门店',dataIndex :'', sortable: false, width:'40%', renderer: function(){return workshopName;}}
		          ],
		mechanicGrid = new Grid.Grid({
			render: render,
			columns : columns,
			items : items
		});
		mechanicGrid.render();
		common.gridAutoResize(mechanicGrid);
	});
}

function scrollInput(el){
	$('.show-block').removeClass('show-block');
	el.parents('.info-block:first').parent().addClass('show-block');
}

var props = ['workShopName', 'regionId', 'regionName', 'workShopAddress', 'longitude', 'latitude', 'area', 'seatsNum', 'employeesNum', 'serviceScope',
             'contactPerson', 'contactPersonTel', /*'businessLicenseCode', 'reserveServiceTel', */'source', 'signedType', /*'businessDetail', */
             /*'harvestPerson', 'harvestPersonTel',*/ 'buttInChargePerson', 'buttInChargePersonTel', 'bankAcountName', 'bankAcount', 'bank',
             'creditRating', 'remark', 'photoId', 'partnerIds', 'excuteUserId', 'type', 'provinceId', 'cityId', 'scale','trenchNumb', 
             'fourColumnElevatorNumb', 'twoColumnElevatorNumb', 'otherEquipment', 'otherCollocation', 'operationYear','operationPeriods', 
             'isHolidayClose', 'digitalManagementIntention', 'levelPromotionIntention', 'authProductAgency', 'saleMajorOilViscosity', 
             'saleMajorOilType', 'saleMajorOilBrand', 'carMaintainNumb', 'workshopOwner', 'workshopOwnerMobile', 'workshopTel', 'isMultiple', 
             'paymentType', 'nearbyVillageNumb', 'spreadWay', 'hasMarketingConcept', 'hasWarehouse', 'managementMaturity','monthlyOilSalesVolume','monthlyChevronOilSalesVolume',
             'shopRecruitment', 'joinShopGiftPlan', 'joinLocationPlan','joinCkPlan', 'recruitmentAmount', 'recruitmentConfirmed', 'extFlag'];//TODO
function wrapObj(){
	
	if($('#editForm [name=workShopName]').val() == ""){
		common.alertMes('请输入门店名称','error');
		scrollInput($('#editForm [name=workShopName]'));
		return false;
	}
//	if($('#editForm [name=photoId]').val() == ""){
//		common.alertMes('请上传门头照','error');
//		return false;
//	}
	if($('#editForm [name=partnerIds]').val() == ""){
		common.alertMes('请选择合作合伙人','error');
		scrollInput($('#editForm [name=partnerIds]'));
		return false;
	}
	if($('#editForm [name=provinceId]').val() == "" || $('#editForm [name=provinceId]').val() == "-1"){
		common.alertMes('请选择门店所在省','error');
		scrollInput($('#editForm [name=provinceId]'));
		return false;
	}
	if($('#editForm [name=cityId]').val() == "" || $('#editForm [name=cityId]').val() == "-1"){
		common.alertMes('请选择门店所在市','error');
		scrollInput($('#editForm [name=cityId]'));
		return false;
	}
	if($('#editForm [name=regionId]').val() == "" || $('#editForm [name=regionId]').val() == ""){
		common.alertMes('请选择门店所在区','error');
		scrollInput($('#editForm [name=regionId]'));
		return false;
	}
	if($('#editForm [name=workShopAddress]').val() == ""){
		common.alertMes('请输入门店地址','error');
		scrollInput($('#editForm [name=workShopAddress]'));
		return false;
	}
	if($('#editForm [name=contactPersonTel]').val() == ""){
		common.alertMes('请输入门店联系电话','error');
		scrollInput($('#editForm [name=contactPersonTel]'));
		return false;
	}
	if($('#editForm [name=longitude]').val() != "" && isNaN($('#editForm [name=longitude]').val())){
		common.alertMes('请在经度框内输入数字','error');
		scrollInput($('#editForm [name=longitude]'));
		return false;
	}
	if($('#editForm [name=latitude]').val() != "" && isNaN($('#editForm [name=latitude]').val())){
		common.alertMes('请在维度框内输入数字','error');
		scrollInput($('#editForm [name=latitude]'));
		return false;
	}
	if($('#editForm [name=area]').val() != "" && isNaN($('#editForm [name=area]').val())){
		common.alertMes('请在面积框内输入数字','error');
		scrollInput($('#editForm [name=area]'));
		return false;
	}
	if($('#editForm [name=seatsNum]').val() != "" && $('#editForm [name=seatsNum]').val() != "0" && !/^\+?[1-9][0-9]*$/.test($('#editForm [name=seatsNum]').val())){
		common.alertMes('请在工位数框内输入整数','error');
		scrollInput($('#editForm [name=seatsNum]'));
		return false;
	}
	if($('#editForm [name=employeesNum]').val() != "" && $('#editForm [name=employeesNum]').val() != "0" && !/^\+?[1-9][0-9]*$/.test($('#editForm [name=employeesNum]').val())){
		common.alertMes('请在员工数框内输入整数','error');
		scrollInput($('#editForm [name=employeesNum]'));
		return false;
	}
	for(var i = 0; i < validators.length; i++){
		var v = validators[i];
		if($('#editForm [name=' + v.field + ']').val() == ''){
			common.alertMes(v.errorMsg,'error');
			scrollInput($('#editForm [name=' + v.field + ']'));
			return false;
		}
	}
	
	var obj = {};
	for(var i = 0; i < props.length; i++){
		obj[props[i]] = $('#editForm [name=' + props[i] + ']').val();
	}
	if(workshopId){
		obj.id = workshopId;
		if(status){
			obj.status = status;
		}
	}else{
		obj.status = $("#editForm input[name='status']:checked").val();
	}
	return obj;
}

function initUpdate(data){
	for(var i = 0; i < props.length; i++){
		$('#editForm [name=' + props[i] + ']').val(data[props[i]]);
	}
	showPhoto(data.attId);
	if(data.activeTime){
		$('#editForm [name=activeTime]').val(common.formatDate(new Date(data.activeTime), 'yyyy-MM-dd hh:mm:ss')).parents('.field-group:first').css('display', 'inline-block');
	}
	//$("#editForm input[name='status'][value='" + data[props[i]] + "']").prop('checked', true);
	if(userId == 1 && data.shopRecruitment == 1){
		$('.recruitment-property').removeClass('hide-important');
	}
}

function initDistributionRule(rule){
	if(rule != null){
		$('#editForm [name=perLiterReward]').val(rule.perLiterReward);
		$('#editForm [name=ownerReward]').val(rule.ownerReward);
		$('#editForm [name=mechanicReward]').val(rule.mechanicReward);
		$('#editForm [name=awardRealtime]').prop('checked', rule.awardRealtime == 'Y');
	}
}

var privacyOk = false;
function save(noPrivacyTip){
	//保存前校验
//	var v  = form.isValid();
//	if(!v){
//		return false;
//	}
	if(!noPrivacyTip && !privacyOk && privacyStatus != 10){
		common.updatePrivacy(userId, 'Workshop.editPage', function(){
			save(true);
		});
		return;
	}
	privacyOk = true;
	var obj = wrapObj();
	if(!obj){
		return;
	}
	obj.extFlag -= 0;
	if(locationConfirmedFlag == 0){
		common.alertMes("请点击“门店定位确认”确认已录入系统的坐标和地址无误！", 'error');
		return false;
	}else if(locationConfirmedFlag == 1){
		obj.extFlag += 1;
	}
	if(obj.recruitmentAmount){
		if(userId == 1 && obj.shopRecruitment == '1' && isNaN(obj.recruitmentAmount)){
			common.alertMes("店招金额必须是数字！", 'error');
			return;
		}else if(isNaN(obj.recruitmentAmount)){
			delete obj.recruitmentAmount;
		}
	}
	var rule = null;
	if(verificationRule != null){
		var perLiterReward = $('#perLiterReward').val().trim() - 0;
		if(perLiterReward > 0 && perLiterReward <= verificationRule.perLiterReward){
			rule = {
					perLiterReward: perLiterReward,
					ownerReward: $('#ownerReward').val().trim(),
					mechanicReward: $('#mechanicReward').val().trim(),
					awardRealtime: $('#awardRealtime').prop('checked') ? 'Y' : 'N',
					partnerId: verificationRule.partnerId
			};
		}
	}
	var skuRules = [];
	for(var sku in updatedSkuDistributionRules){
		if(updatedSkuDistributionRules[sku]){
			skuRules.push(updatedSkuDistributionRules[sku]);
		}
	}
	LoadMask.show();
	setTimeout(function(){
		foo.call('workShopServiceImpl.insertOrUpdateWorkShop',[obj, attFileList, rewardCtracts, rule, subTaskId, 
		                                                       skuRules, deletedSkuDistributionRules],
				function(result) {
					LoadMask.hide();
					if("success" == result.code){ 
						common.alertMes("门店提交成功", 'success'/*, function(){
							window.location = common.ctx + 'business/workshop/editWorkshop.jsp?workshopId=' + result.workshopId;
							return this.close();
						}*/);
						if(workshopId){
							window.location = '/business/workshop/workshopPage.jsp?cacheParams=true&toItem=' + workshopId;
						}else{
							window.location.href ='/business/workshop/workshopPage.jsp';
						}
					}else{
						common.alertMes("门店提交失败", 'error');
					}
			},  function(error) {
				common.ajaxTimeout(error);
				LoadMask.hide();
			});
	}, 0);
}
function loadUserList(orgid,muserNmaeOrAccount){
	LoadMask.show();
	foo.call('orgVoService.getUserByOrgId',[workshopId,$('#mUserNameOraccount').val(), '1'],function(result){
		LoadMask.hide();
		$("#user_grid").html("");
		var data = result.userList;
        if(null!=data)
        {
        	$(data).each(function (j, obj) {
        		if(0==obj.status){
        			obj.status="停用";
        		}else if(1==obj.status){
        			obj.status="启用";
        		}
			});	
		}
        
		BUI.use(['bui/grid','bui/data'],function(Grid,Data){
			var Grid = Grid,
			Store = Data.Store,
			columns = [
					{title : '账号',dataIndex : 'loginName', width:'30%'},
					{title : '姓名',dataIndex : 'chName', width:'30%'},
					{title : '状态',dataIndex : 'status', width:'20%'},
					{title : '操作',dataIndex : 'userId', width: '20%', renderer:function(value,node){
						var mStatus = "";
						if(node.status=="停用")
						{
							mStatus = "1"; 
							return  "<a href=\"javascript:void(0);\" onclick='modifyUserStatus("+value+","+mStatus+")'>启用</a>&nbsp;<a href=\"javascript:void(0);\"  onclick='getUserInfo("+value+")'>修改</a>&nbsp;<a href=\"javascript:void(0);\"  onclick='removeUser("+value+")'>删除</a>";
						}else if(node.status=="启用")
						{
							mStatus = "0"; 
							return "<a href=\"javascript:void(0);\" onclick='modifyUserStatus("+value+","+mStatus+")'>停用</a>&nbsp;<a href=\"javascript:void(0);\" onclick='getUserInfo("+value+")'>修改</a>&nbsp;<a href=\"javascript:void(0);\" onclick='removeUser("+value+")'>删除</a>";
						}
						
						//return prefButton+otherButton;//"<button class='button button-default button-mini' onclick='getUserInfo("+value+")'>修改</button> <button class='button button-default button-mini' onclick='removeUser("+value+")'>删除</button>";
					}}
				],
			store = new Store({
				data : data,
				pageSize : 10
			});
			var grid = new Grid.Grid({
				render:'#user_grid',
				columns : columns,
				loadMask: true,
				store: store,
				bbar:{
					pagingBar:true
				}
			});
			grid.render();
			common.initGrid(grid, null, true);
		});
	},function(error){
		common.ajaxTimeout(error);
	});
}
function showBlock(el){
	var $group = $(el).parent();
	if($group.hasClass('show-block')){
		$group.removeClass('show-block');
	}else{
		$group.addClass('show-block');
	}
}

var spRuleBar = null, wsRuleBar = null;
function refreshDistributionStatus(partnerIds){
	foo.call('workShopServiceImpl.getVerificationRule',[partnerIds],
			function(result) {
				if(result.success){
					verificationRule = result.data;
					updateDistributionStatus(verificationRule);
					if(verificationRule){
						refreshSkuDistributionGrid(result.skuDistributionRules);
					}
				}else{
					common.alertMes("获取合伙人核销规则失败！", 'error');
				}
		},  function(error) {
			common.ajaxTimeout(error);
			LoadMask.hide();
		});
}
function refreshSkuDistributionGrid(spSkuDistributionRules){
	if(skuDistributionGrid == null){
		if(!skuDistributionRules || skuDistributionRules.length == 0){
			//使用默认sku分配配置
			skuDistributionRules = spSkuDistributionRules || [];
			initSkuDistributionGrid(skuDistributionRules);
		}else{
			//初始化门店SKU分配规则
			for(var i = 0; i < skuDistributionRules.length; i++){
				skuDistributionRules[i].isWs = true;
			}
			if(!spSkuDistributionRules || spSkuDistributionRules.length == 0){
				//合伙人没有SKU分配规则
				initSkuDistributionGrid(skuDistributionRules);
			}else{
				//使用合并后分配规则
				initSkuDistributionGrid(mergeSkuDistributionRules(spSkuDistributionRules, skuDistributionRules));
			}
		}
	}else{
		//使用合并后分配规则
		skuDistributionGrid.setItems(mergeSkuDistributionRules(spSkuDistributionRules, skuDistributionRules));
	}
}
function mergeSkuDistributionRules(spRules, wsRules){
	var spRuleMap = {};
	//重组合伙人SKU分配规则
	if(spRules){
		for(var i = 0; i < spRules.length; i++){
			spRuleMap[spRules[i].sku] = spRules[i];
		}
	}
	for(var i = wsRules.length - 1; i >= 0; i--){
		if(!wsRules[i].isWs && !spRuleMap[wsRules[i].sku]){
			//去除新SP没有的SKU分配规则
			wsRules.splice(i, 1);
		}else if(!wsRules[i].isWs){
			//覆盖原来的SKU分配规则 
			wsRules[i] = spRuleMap[wsRules[i].sku];
			spRuleMap[wsRules[i].sku].processed = true;
		}else if(wsRules[i].isWs && spRuleMap[wsRules[i].sku]){
			//覆盖SKU默认分配规则
			wsRules[i].defaultRule = spRuleMap[wsRules[i].sku];
			spRuleMap[wsRules[i].sku].processed = true;
		}
	}
	for(var sku in spRuleMap){
		if(!spRuleMap[sku].processed){
			//新增新的默认sku分配规则
			wsRules.push(spRuleMap[sku]);
		}
	}
	return wsRules;
}
function updateDistributionStatus(rule){
	if(rule == null){
		$('.has-sp-verification-rule').each(function(){$(this).addClass('hide');});
		$('.no-sp-verification-rule').each(function(){$(this).removeClass('hide');});
	}else{
		$('.no-sp-verification-rule').each(function(){$(this).addClass('hide');});
		var ws = $('#perLiterReward').val().trim();
		if(ws == "" || isNaN(ws)){
			ws = 0;
		}else{
			ws = ws - 0;
			if(ws > rule.perLiterReward){
				ws = rule.perLiterReward;
			}
		}
		var sp = rule.perLiterReward - ws;
		$('.has-sp-verification-rule').each(function(){$(this).removeClass('hide');});
		//SP分配游标
		if(loginOrgType == 1){
			$('#perLiterReward_c').hide();
		}else{
			$('#perLiterReward_c').show();
			updateSpDistritution(sp, ws);
		}
		//门店分配游标
		updateWsDistritutionByWs(ws);
	}
}
function updateSpDistritution(sp, ws, updateWsRule){
	sp = parseFloat(parseFloat(sp).toFixed(2));
	$('#perLiterReward').val(parseFloat(parseFloat(ws).toFixed(2)));
	$('#spReward').val(sp);
	$('#spAllReward').text(verificationRule.perLiterReward);
	var spp = parseFloat((sp / verificationRule.perLiterReward * 100).toFixed(2));
	$('#spRewardComment').text('(' + spp + '%)');
	$('#wsAllRewardComment').text('(' + parseFloat((100 - spp).toFixed(2)) + '%)');
	//初始化sp分配标尺
	var opts = {
            min: 0,
            max: verificationRule.perLiterReward,
            from: sp,
            type: "single",
            step: 0.1,
            hasGrid: false,
            disable: !isEdit,
            hideMinMax: false,
            hideFromTo: true,
            onChange: function(slider){
				$('#spReward').val(parseFloat(parseFloat(slider.from).toFixed(2)));
				$('#perLiterReward').val(parseFloat((verificationRule.perLiterReward - slider.from).toFixed(2)));
			},
            onFinish: function(slider){
				var spp = parseFloat((slider.from / verificationRule.perLiterReward * 100).toFixed(2));
				$('#spRewardComment').text('(' + spp + '%)');
				$('#wsAllRewardComment').text('(' + parseFloat((100 - spp).toFixed(2)) + '%)');
				//更新门店分配游标
				updateWsDistritutionByWs(verificationRule.perLiterReward - slider.from);
            }
	};
	if(spRuleBar){
		spRuleBar.update(opts);
	}else{
		$('#perLiterReward_bar').ionRangeSlider(opts);
		spRuleBar = $('#perLiterReward_bar').data('ionRangeSlider');
	}
	//更新门店分配标尺
	if(updateWsRule){
		updateWsDistritutionByWs(ws);
	}
}
function updateWsDistritutionByWs(ws){
	if(ws < 0.01){
		$('#wsAllocation_c').hide().parent().removeClass('show-wsallocation');
	}else{
		$('#wsAllocation_c').show().parent().addClass('show-wsallocation');
		var mech = $('#mechanicReward').val().trim();
		if(mech == "" || isNaN(mech)){
			mech = 0;
		}else{
			mech = mech - 0;
			if(mech > ws){
				mech = ws;
			}
		}
		var wso = ws - mech;
		updateWsDistritution(wso, mech, ws);
	}
}
var wsRuleBarInited = false;
function updateWsDistritution(wso, mech, ws){
	wso = parseFloat(parseFloat(wso).toFixed(2));
	ws = parseFloat(parseFloat(ws).toFixed(2));;
	$('#ownerReward').val(wso);
	$('#mechanicReward').val(parseFloat(parseFloat(mech).toFixed(2)));
	$('#wsAllReward').text(ws);
	var wsop = parseFloat((wso / ws * 100).toFixed(2));
	$('#wsRewardComment').text('(' + wsop + '%)');
	$('#mechRewardComment').text('(' + parseFloat((100 - wsop).toFixed(2)) + '%)');
	//初始化sp分配标尺
	var opts = {
            min: 0,
            max: ws,
            from: wso,
            type: "single",
            step: 0.1,
            hasGrid: false,
            disable: !isEdit,
            hideMinMax: false,
            hideFromTo: true,
            onChange: function(slider){
				$('#ownerReward').val(parseFloat(parseFloat(slider.from).toFixed(2)));
				$('#mechanicReward').val(parseFloat((ws - slider.from).toFixed(2)));
			},
            onFinish: function(slider){
				var wsop = parseFloat((slider.from / ws * 100).toFixed(2));
				$('#wsRewardComment').text('(' + wsop + '%)');
				$('#mechRewardComment').text('(' + parseFloat((100 - wsop).toFixed(2)) + '%)');
            }
	};
	if(wsRuleBar){
		wsRuleBar.update(opts);
	}else{
		$('#wsAllocation_bar').ionRangeSlider(opts);
		wsRuleBar = $('#wsAllocation_bar').data('ionRangeSlider');
	}
}

function formatTimeMillis(timeMillis){
	var date = new Date(timeMillis);
	var monthOfDay = formatCurDay(date.getMonth()+1);
	var dateOfDay = formatCurDay(date.getDate());
	var dateFormat = date.getFullYear()+"-"+monthOfDay+"-"+dateOfDay;
	return dateFormat;
}
function formatCurDay(formatPara){
	if(formatPara < 10){
		return "0"+formatPara;
	}else{
		return formatPara;
	}
}

var skuDistributionGrid = null, updatedSkuDistributionRules = {}, deletedSkuDistributionRules = [];
function initSkuDistributionGrid(items){
	BUI.use(['bui/grid'],function(Grid){
		var columns = [
			           {title : '序号',dataIndex :'order', sortable: false, width:50,renderer:function(value,obj,index){
			        	   return index + 1;
			           }},
		           {title : 'sku',dataIndex :'sku', sortable: false, width:'35%'},
		           {title : '产品名称',dataIndex :'productName', sortable: false, width:'65%'},
		           {title : '店老板金额(元)',dataIndex :'ownerReward', sortable: false, elCls: 'text-right', width:100,renderer: function(value){
		        	   if(value){
		        		   return common.formatMoney(value, 2);
		        	   }
		        	   return 0;
		           }},
		           {title : '技师金额(元)',dataIndex :'mechanicReward', sortable: false, elCls: 'text-right', width:90,renderer: function(value){
		        	   if(value){
		        		   return common.formatMoney(value, 2);
		        	   }
		        	   return 0;
		           }},
		           {title : '合伙人金额(元)',dataIndex :'', sortable: false, elCls: 'text-right', width:100,renderer: function(value, item){
			       		if(!verificationRule){
			    			return '-';
			    		}
		        	   var perLiterReward = verificationRule.perLiterReward;
		        	   if(perLiterReward == '' || isNaN(perLiterReward)){
		        		   return '-';
		        	   }
		        	   value = parseFloat(perLiterReward) - item.ownerReward - item.mechanicReward;
		        	   if(value >= 0){
		        		   return common.formatMoney(value, 2);
		        	   }
		        	   return '-';
		           }}
		          ];
		if(isEdit){
			columns.push({title : '操作',dataIndex : 'id', sortable: false,width:120,renderer:function(value,obj,index){
		        	   if(!value){
		        		   value = 'null';
		        	   }
		        	   var text = '<a href="javascript:void(0);" onclick="editSkuDistribution(' + index + ')">修改</a>';
		        	   if(obj.isWs && obj.defaultRule){
		        		   text += '&nbsp;<a onclick="removeDistribution(' + value + ', ' + index + ', \'' + obj.sku + '\')" href="javascript:void(0);">恢复默认</a>';
		        	   }else if(obj.isWs){
		        		   text += '&nbsp;<a onclick="removeDistribution(' + value + ', ' + index + ', \'' + obj.sku + '\')" href="javascript:void(0);">删除</a>';
		        	   }
		        	   return text;
		           }});
		}
		skuDistributionGrid = new Grid.Grid({
			render:'#skuDistributionGrid',
			columns : columns,
			width:'100%',
			items : items
		});
		skuDistributionGrid.render();
	});
}
var editDialog = null, productCtrl = null;
function initEditDialog(title,partnerDisabled){
	BUI.use('bui/overlay', function (Overlay) {
		editDialog = new Overlay.Dialog({
			title: title,
			width: 600,
//			height: 300,
			success: function () { },
			bodyContent: $("#editDialog"),
			buttons: [{
				text: '确定',
				elCls: 'button btn-create',
				handler: function () {
					saveSkuDistribution(this);
				}
			},
			{
				text: "取消",
				elCls: 'button btn-cancel',
				handler: function () {
					this.close();
				}
			}]
		});
		editDialog.render();
		editDialog.show();
		//渲染产品名称控件
		productCtrl = Ctrls.ProductAutoSelect.init('#skuDistributionForm input[name=sku]', {ctrlOpts: {
			disabled: partnerDisabled,
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
	            if (!json || !json.value) {
	                return false;
	            }
	
	            var newValue = [];
	            len = json.value.length;
	
	            var skuDistributions = skuDistributionGrid.getItems(), existsSkus = {};
	            for(var i = 0; i < skuDistributions.length; i++){
	            	existsSkus[skuDistributions[i].sku] = true;
	            }
	            for (var i = 0; i < len; i++) {
	            	if(!existsSkus[json.value[i].sku]){
		                newValue.push({
		                    sku: json.value[i].sku,
		                    text: json.value[i].text
		                });
	            	}
	            }
	            json.value = newValue;
	
	            //字符串转化为 js 对象
	            return json;
	        }
		}});
	});
}
var editingSkuDistribution = null;
function editSkuDistribution(index){
	if(!verificationRule){
		return;
	}
	var perLiterReward = verificationRule.perLiterReward;
	editingSkuDistribution = skuDistributionGrid.getItemAt(index);
	$('#skuDistributionForm input[name=spReward]').val(parseFloat(perLiterReward - editingSkuDistribution.ownerReward - editingSkuDistribution.mechanicReward));
	$('#skuDistributionForm input[name=ownerReward]').val(parseFloat(editingSkuDistribution.ownerReward));
	$('#skuDistributionForm input[name=mechanicReward]').val(parseFloat(editingSkuDistribution.mechanicReward));
	$('#skuDistributionForm input[name=ruleId]').val(editingSkuDistribution.id);
	if(editDialog == null){
		$('#skuDistributionForm input[name=sku]').val(editingSkuDistribution.sku).attr('text', editingSkuDistribution.productName);
		initEditDialog("修改优先服务费分配规则", true);
	}else{
		productCtrl.disable();
		productCtrl.setValueText(editingSkuDistribution.sku, editingSkuDistribution.productName);
		editDialog.set('titile', "修改优先服务费分配规则");
		editDialog.show();
	}
}
function addSkuDistribution(){
	if(!verificationRule){
		return;
	}
	var perLiterReward = verificationRule.perLiterReward;
	if(editDialog == null){
		$('#skuDistributionForm input[name=spReward]').val(perLiterReward);
		initEditDialog("添加优先服务费分配规则", false);
	}else{
		productCtrl.enable();
		productCtrl.empty();
		editDialog.set('titile', "添加优先服务费分配规则");
		editDialog.show();
	}
	editingSkuDistribution = null;
}
function removeDistribution(ruleId, index, sku){
	if(ruleId){
		deletedSkuDistributionRules.push(ruleId);
	}
	var rule = skuDistributionGrid.getItemAt(index);
	skuDistributionGrid.removeItemAt(index);
	if(rule.defaultRule){
		skuDistributionGrid.addItemAt(rule.defaultRule, index);
	}
//	var items = skuDistributionGrid.getItems();
//	items.splice(index, 1);
//	skuDistributionGrid.setItems(items);
}
function saveSkuDistribution(dialog){
	var ownerReward = $('#skuDistributionForm input[name=ownerReward]').val();
	if(ownerReward == '' || isNaN(ownerReward)){
		common.alertMes("核销每升服务费分配给店老板金额必须是数字", "error");
		return false;
	}
	ownerReward = parseFloat(ownerReward);
	if(ownerReward < 0){
		common.alertMes("核销每升服务费分配给店老板金额不能小于0", "error");
		return false;
	}
	var mechanicReward = $('#skuDistributionForm input[name=mechanicReward]').val();
	if(mechanicReward == '' || isNaN(mechanicReward)){
		common.alertMes("核销每升服务费分配给技师金额必须是数字", "error");
		return false;
	}
	mechanicReward = parseFloat(mechanicReward);
	if(mechanicReward < 0){
		common.alertMes("核销每升服务费分配给技师金额不能小于0", "error");
		return false;
	}
	if(ownerReward + mechanicReward > verificationRule.perLiterReward){
		common.alertMes("核销每升服务费分配给合伙人金额不能小于0", "error");
		return false;
	}
	var rule = {
			id: $('#skuDistributionForm input[name=ruleId]').val(),
			ownerReward: ownerReward,
			mechanicReward: mechanicReward,
			perLiterReward: ownerReward + mechanicReward,
			awardRealtime: 'N',
			sku: $('#skuDistributionForm input[name=sku]').val(),
			isWs: true,
			partnerId: verificationRule.partnerId,
			productName: productCtrl.getText()
	};
	if(rule.sku == ''){
		common.alertMes("SKU不能为空", "error");
		return false;
	}
	if(editingSkuDistribution){
		if(editingSkuDistribution.isWs){
			rule = $.extend(editingSkuDistribution, rule);
			skuDistributionGrid.updateItem(rule);
		}else{
			rule.defaultRule = editingSkuDistribution;
			rule.id = null;
			var index = skuDistributionGrid.indexOfItem(editingSkuDistribution);
			skuDistributionGrid.removeItemAt(index);
			skuDistributionGrid.addItemAt(rule, index);
		}
	}else{
		skuDistributionGrid.addItem(rule);
	}
	updatedSkuDistributionRules[rule.sku] = rule;
	dialog.close();
}
function validateAmount(el, label){
	if(el.value == ''){
		el.value = '0';
	}else if(isNaN(el.value)){
		common.alertMes(label + "格式不对", function(){
			el.focus();
			el.select();
		}, "error");
		return false;
	}else if(el.value - 0 < 0){
		el.value = '0';
	}
	return true;
}

function fixDistribution(args, perLiterReward){
	var vals = [];
	if(perLiterReward === undefined){
		//分配规则变动引起
		if(!verificationRule){
			return;
		}
		perLiterReward = verificationRule.perLiterReward;
		vals.push(args[0].val());
	}else{
		vals.push(args[0].val());
		if(isNaN(vals[0])){
			return;
		}
	}
	vals.push(args[1].val());
	vals.push(args[2].val());
	if(isNaN(vals[1]) || isNaN(vals[2])){
		return;
	}
	vals[0] = vals[0] - 0;
	vals[1] = vals[1] - 0;
	vals[2] = vals[2] - 0;
	var offset = perLiterReward - vals[0] - vals[1] - vals[2];
	if(offset != 0){
		vals[1] = vals[1] + offset;
		if(vals[1] < 0){
			offset = vals[1];
			args[1].val('0');
			vals[2] = vals[2] + offset;
			if(vals[2] < 0){
				args[0].val(parseFloat(vals[0] + vals[2]));
				args[2].val('0');
			}else{
				args[2].val(parseFloat(vals[2]));
			}
		}else{
			args[1].val(parseFloat(vals[1]));
		}
	}
}

var mapDialog = null;
function editLocation(){
	if(mapDialog == null){
		initMapDialog();
	}else{
		mapDialog.show();
	}
	initAMap({
		  position: [$('#editForm input[name=longitude]').val(),$('#editForm input[name=latitude]').val()],
		  name: $('#editForm input[name=workShopName]').val(),
		  address: $('#editForm input[name=workShopAddress]').val()
		});
}
function initMapDialog(){
	BUI.use('bui/overlay', function (Overlay) {
		mapDialog = new Overlay.Dialog({
			title: '门店定位确认',
			width: 900,
//			height: 300,
			success: function () { },
			bodyContent: $("#mapDialog"),
			buttons: [
			{
				text: "关闭",
				elCls: 'button btn-cancel',
				handler: function () {
					this.close();
				}
			}]
		});
		mapDialog.render();
		mapDialog.show();
	});
}
function setAddressValue (data) {
//	  console.log(data)
	if(data){
		for(var k in data){
			if(k == 'address'){
				$('#editForm input[name=workShopAddress]').val(data.address);
			}else if(k == 'position'){
				var p = data.position;
				if(p){
					$('#editForm input[name=longitude]').val(p[0]);
					$('#editForm input[name=latitude]').val(p[1]);
				}
			}
		}
	}
	if(locationConfirmedFlag == 0){
		locationConfirmedFlag = 1;
	}
	mapDialog.close();
}