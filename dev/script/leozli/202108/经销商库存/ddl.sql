ALTER TABLE PP_MID.dbo.[mid_partner_sale_config] add store_id                         bigint                                     null

ALTER TABLE wx_t_inventory_check2021 add business_type                    nvarchar(64)                               not null default('DISTRIBUTOR_CHECK')
ALTER TABLE wx_t_inventory_check2021 add status                           int                                        not null default(10)
ALTER TABLE wx_t_inventory_check2021 add details                          nvarchar(max)                                  null
ALTER TABLE wx_t_inventory_check2021 add check_category                   nvarchar(64)                               not null default('INVENTORY_CHECK')
ALTER TABLE wx_t_order add ext_property1                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property2                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property3                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property4                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property5                    nvarchar(512)                                  null
ALTER TABLE wx_t_order add ext_property6                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property7                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property8                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property9                    nvarchar(256)                                  null
ALTER TABLE wx_t_order add ext_property10                    nvarchar(256)                                  null

ALTER TABLE wx_t_inventory_log2021 add recall_original_quantity         int                                            null

ALTER TABLE [wx_t_resource_sql] ALTER COLUMN sqlid                              varchar(256)                             not null

update t set t.code=t.code+'/'+convert(varchar(20),t.id)
from wx_t_out_stock_line t where t.code in (
select t1.code from wx_t_out_stock_line t1 group by t1.code having count(1)>1
)

ALTER TABLE wx_t_out_stock_line ADD CONSTRAINT uk_out_stock_line UNIQUE (code);

ALTER TABLE wx_t_in_stock add ext_property1                    nvarchar(256)                                  null
ALTER TABLE wx_t_in_stock add ext_property2                    nvarchar(256)                                  null
ALTER TABLE wx_t_in_stock add ext_property3                    nvarchar(256)                                  null
ALTER TABLE wx_t_in_stock add ext_property4                    nvarchar(256)                                  null
ALTER TABLE wx_t_in_stock add ext_property5                    nvarchar(512)                                  null

ALTER TABLE wx_t_store2021 ADD CONSTRAINT uk_store2021 UNIQUE (org_type,org_id,name,store_type);

