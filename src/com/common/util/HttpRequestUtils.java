package com.common.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.chevron.autozi.model.ReplenishMentBodyBean;
import com.chevron.autozi.model.ReplenishMentHeaderBean;
import com.chevron.autozi.model.ReturnedPurchaseBodyBean;
import com.chevron.autozi.model.ReturnedPurchaseHeaderBean;
import com.chevron.autozi.model.ZCReplenishMentBean;
import com.chevron.autozi.model.ZCReturnedPurchaseBean;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
/**
 * 
 * @Author: bo.liu  2017-1-5 下午1:39:26 
 * @Version: $Id$
 * @Desc: <p></p>
 */
public class HttpRequestUtils {
	private static Logger logger = LoggerFactory.getLogger(HttpRequestUtils.class); // 日志记录
	//请求host
	public static String HOST_URL = "http://**************/chevron/autotziApi/productPurchaseManage";//"http://partnerstg.chinacloudapp.cn/chevron/autotziApi/productPurchaseManage";//
	//退货请求通知接口URL
	public static String RETURNEDPURCHASE_REQ_URL = HOST_URL + "/returnedPurchase.do";
	//退货请求通知接口URL
	public static String REPLENISHMENT_REQ_URL = HOST_URL + "/replenishment.do";
	private static final int REQUEST_TIMEOUT = 60*1000;  //设置请求超时10秒钟
	private static final int SO_TIMEOUT = 60*1000;       //设置等待数据超时时间10秒钟
	
	//补货反馈确认，调用中驰
	public static String REPLENISHMENT_CONFRIM_REQ_URL = "http://{domain}/openapi/xfl/puchaseorder/orderFeedBackInfor";
	public static String RETURNEDPURCHASE_CONFRIM_REQ_URL = "http://{domain}/openapi/xfl/puchaseorder/reOrderFeedBackInfor";
	public static String DIDI_SCREATE = "Dd#4r8coupon?_7rfG@";
	
	private static DefaultHttpClient httpClient;
	/**
	 * 
	 * <AUTHOR> 2017-1-5 下午1:17:35
	 * @return
	 */
	public static DefaultHttpClient getHttpClient()
	{
		if(null==httpClient)
		{
			httpClient = new DefaultHttpClient();
			//请求超时
			httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, REQUEST_TIMEOUT); 
			//读取超时
			httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, SO_TIMEOUT);
			
		}
		return httpClient;
	}
	
	
	/**
	 * httpPost
	 * 
	 * @param url
	 *            路径
	 * @param jsonParam
	 *            参数
	 * @return
	 */
	public static JSONObject httpPost(String url, JSONObject jsonParam) {
		return httpPost(url, jsonParam, false);
	}

	
	/**
	 * post请求
	 * 
	 * @param url
	 *            url地址
	 * @param jsonParam
	 *            参数
	 * @param noNeedResponse
	 *            不需要返回结果
	 * @return
	 */
	public static JSONObject httpPost(String url, JSONObject jsonParam,
			boolean noNeedResponse) {
		// post请求返回结果
		DefaultHttpClient httpClient = getHttpClient();
		JSONObject jsonResult = null;
		HttpPost method = new HttpPost(url);
		BufferedReader in = null;
		String responseContent = null;
		HttpResponse result = null;
		try {
			if (null != jsonParam) {
				// 解决中文乱码问题
				StringEntity entity = new StringEntity(jsonParam.toString(),
						"utf-8");
				entity.setContentEncoding("UTF-8");
				entity.setContentType("application/json");
				method.setEntity(entity);
			}
			result = httpClient.execute(method);
			url = URLDecoder.decode(url, "UTF-8");
			
			/** 请求发送成功，并得到响应 **/
			if (result.getStatusLine().getStatusCode() == 200) {
					HttpEntity reseposeentity = result.getEntity();
					/** 读取服务器返回过来的json字符串数据 **/
					//方式一 读取返回的json字符串：
					//responseContent = EntityUtils.toString(reseposeentity);
					
					//方式二 通过流获取
					in = new BufferedReader (new InputStreamReader (reseposeentity.getContent(), "UTF-8"));
					StringBuffer sb = new StringBuffer();
		            String line;
		            while ((line = in.readLine()) != null) {
		            	sb.append(line);
		            }
		            responseContent = sb.toString();
					/** 把json字符串转换成json对象 **/
					jsonResult = JSONObject.fromObject(responseContent);
					
			}else
			{
				method.abort();
				throw new WxPltException("HTTP ERROR Status: " + result.getStatusLine().getStatusCode() + ":" + result.getStatusLine().getReasonPhrase());
			}
			
		} catch (Exception e) {
			method.abort();
			logger.error("post请求提交失败:" + url, e);
			jsonResult = null;
		}finally{
			httpClient.clearResponseInterceptors();
		}
		return jsonResult;
	}

	/**
	 * 发送get请求
	 * 
	 * @param url
	 *            路径
	 * @return
	 */
	public static JSONObject httpGet(String url) {
		// get请求返回结果
		JSONObject jsonResult = null;
		try {
			DefaultHttpClient client = getHttpClient();
			// 发送get请求
			HttpGet request = new HttpGet(url);
			HttpResponse response = client.execute(request);

			/** 请求发送成功，并得到响应 **/
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				/** 读取服务器返回过来的json字符串数据 **/
				String strResult = EntityUtils.toString(response.getEntity());
				/** 把json字符串转换成json对象 **/
				jsonResult = JSONObject.fromObject(strResult);
				url = URLDecoder.decode(url, "UTF-8");
			} else {
				logger.error("get请求提交失败:" + url);
			}
		} catch (IOException e) {
			logger.error("get请求提交失败:" + url, e);
		}
		return jsonResult;
	}

	

	public static void testReturnedPurchase1() {
		String url = RETURNEDPURCHASE_REQ_URL;
		try
		{
			
		JSONObject params = new JSONObject();
		params.put("merId", "10002");
		params.put("validTime", "201611162344789");
		params.put("sign", "98a469d0949f820a1ffefa180df4a400");

		JSONObject header = new JSONObject();
		header.put("buyerName", "中驰商务电子信息有限公司");
		header.put("poReturnHeaderNo", "VB1611241046314043");
		header.put("orderingTime", "2016-11-17");
		header.put("rdcWareHouseCode", "2016-11-17");
		header.put("rdcWareHouseName", "2016-11-17");
		header.put("rdcAddress", "2016-11-17");
		header.put("contacter", "2016-11-17");
		header.put("contactPhone", "2016-11-17");
		header.put("status", "2016-11-17");
		params.put("header", header);

		JSONArray body = new JSONArray();
		JSONObject bodyp1 = new JSONObject();
		JSONObject bodyp2 = new JSONObject();
		bodyp1.put("poReturnNo", "1001");
		bodyp1.put("productCode", "XFLCODE");
		bodyp1.put("returnQuantity", "20001");

		bodyp2.put("poReturnNo", "1002");
		bodyp2.put("productCode", "XFLCODE2");
		bodyp2.put("returnQuantity", "20002");
		body.add(bodyp1);
		body.add(bodyp2);
		params.put("body", body);
			
		String ret = httpPost(url, params).toString();
		System.out.println("======ljc testReturnedPurchase1:"+ret);
		
		}catch(Exception ex)
		{
			ex.printStackTrace();
		}
	}
	
	public static void testReturnedPurchase2() {
		String url = RETURNEDPURCHASE_REQ_URL;
		try
		{
		
		ZCReturnedPurchaseBean zcReturnedPurchaseBean = new ZCReturnedPurchaseBean();
		zcReturnedPurchaseBean.setMerId("10002");
		Date dateTime = new Date();
		Calendar cal=Calendar.getInstance();
		cal.setTime(dateTime);
		zcReturnedPurchaseBean.setValidTime(DateUtils.toDateStr(cal, 16));
		
		//header
		ReturnedPurchaseHeaderBean returnedPurchaseHeaderBean = new ReturnedPurchaseHeaderBean();
		returnedPurchaseHeaderBean.setBuyerName("中驰商务电子信息有限公司");
		returnedPurchaseHeaderBean.setPoReturnHeaderNo("VTH1611211824120002");
		returnedPurchaseHeaderBean.setOrderingTime("2016-11-17 00:24");
		returnedPurchaseHeaderBean.setRdcWareHouseCode("a1025368");
		returnedPurchaseHeaderBean.setRdcWareHouseName("北京1号库");
		returnedPurchaseHeaderBean.setRdcAddress("北京市海淀区永丰路");
		returnedPurchaseHeaderBean.setContacter("张三");
		returnedPurchaseHeaderBean.setContactPhone("123123213");
		returnedPurchaseHeaderBean.setStatus("0");
		zcReturnedPurchaseBean.setHeader(returnedPurchaseHeaderBean);
		//需要签名字段排序
		HashMap<String, String> paramMap = new HashMap<String, String>();
		List<String> commonallParams = new ArrayList<String>();//客户端暂时没有用,,服务端必须要
		List<String> headerallParams = new ArrayList<String>();//客户端暂时没有用，，服务端必须要
		List<String> commonSignParams = new ArrayList<String>();
		List<String> headerSignParams = new ArrayList<String>();
		
		List<String> needParams = new ArrayList<String>();
		ReflectHelper.getParametersFromJavaBean(zcReturnedPurchaseBean, returnedPurchaseHeaderBean, commonallParams, commonSignParams,headerallParams,headerSignParams, paramMap,needParams);
		if(null==commonSignParams || commonSignParams.isEmpty())
		{
			System.out.println("参数不对");
			return;
		}
		
		if(null==headerSignParams || headerSignParams.isEmpty())
		{
			System.out.println("头部参数不对");
			return;
		}
		
		StringBuilder signedParamsStr = new StringBuilder();
		Collections.sort(headerSignParams);
		for(String headerNeedSign:headerSignParams)
		{
			System.out.println(headerNeedSign+"-------"+paramMap.get(headerNeedSign));
			String headerSignParamData = paramMap.get(headerNeedSign);
			signedParamsStr.append(headerSignParamData);
			signedParamsStr.append("|");
		}
		//公共字段不用排序
//		Collections.sort(commonSignParams);
//		for(String commonNeedSign:commonSignParams)
//		{
//			System.out.println(commonNeedSign+"-------"+paramMap.get(commonNeedSign));
//			String commonSignParamData = paramMap.get(commonNeedSign);
//			signedParamsStr.append(commonSignParamData);
//			signedParamsStr.append("|");
//		}
		signedParamsStr.append(paramMap.get(Constants.ZC_API_VALIDTIME)+"|");
		signedParamsStr.append(paramMap.get(Constants.ZC_API_MERID)+"|");
		signedParamsStr.append(Constants.ZC_API_KEY);
		String signData = EncryptionUtil.getMD5Result(signedParamsStr.toString());
		zcReturnedPurchaseBean.setSign(signData);
		
		
		//body
		ReturnedPurchaseBodyBean returnedPurchaseBodyBean = new ReturnedPurchaseBodyBean();
		ReturnedPurchaseBodyBean returnedPurchaseBodyBean2 = new ReturnedPurchaseBodyBean();
		
		List<ReturnedPurchaseBodyBean> bodylst = new ArrayList<ReturnedPurchaseBodyBean>();
		returnedPurchaseBodyBean.setPoReturnNo("1001");
		returnedPurchaseBodyBean.setProductCode("GWBT20001");
		returnedPurchaseBodyBean.setReturnQuantity("521");
		
		returnedPurchaseBodyBean2.setPoReturnNo("1002");
		returnedPurchaseBodyBean2.setProductCode("GWBT20002");
		returnedPurchaseBodyBean2.setReturnQuantity("522");
		bodylst.add(returnedPurchaseBodyBean);
		bodylst.add(returnedPurchaseBodyBean2);
		zcReturnedPurchaseBean.setBody(bodylst);
		
		String reqJSONStr = JsonUtil.writeValue(zcReturnedPurchaseBean);
		System.out.println(reqJSONStr);
		JSONObject zcReturnedPurchaseStr = JSONObject.fromObject(reqJSONStr);

		String ret = httpPost(url, zcReturnedPurchaseStr).toString();
		System.out.println("======ljc testReturnedPurchase2"+ret);
		
		}catch(Exception ex)
		{
			ex.printStackTrace();
		}
	}
	
	public static void testReplenishMentPurchase1() {
		String url = REPLENISHMENT_REQ_URL;
		try
		{
			
		JSONObject params = new JSONObject();
		params.put("merId", "10002");
		params.put("validTime", "201611162344789");
		params.put("sign", "98a469d0949f820a1ffefa180df4a400");

		JSONObject header = new JSONObject();
		header.put("buyerName", "中驰商务电子信息有限公司");
		header.put("poHeaderNo", "VB1611241046314043");
		header.put("orderingTime", "2016-11-17");
		header.put("expDeliveyDate", "2016-11-17");
		header.put("rdcWareHouseCode", "2016-11-17");
		header.put("rdcWareHouseName", "2016-11-17");
		header.put("rdcAddress", "2016-11-17");
		header.put("contacter", "2016-11-17");
		header.put("contactPhone", "2016-11-17");
		header.put("status","0");
		params.put("header", header);

		JSONArray body = new JSONArray();
		JSONObject bodyp1 = new JSONObject();
		JSONObject bodyp2 = new JSONObject();
		bodyp1.put("poNo", "1001");
		bodyp1.put("productCode", "XFLCODE");
		bodyp1.put("orderQuantity", "20001");

		bodyp2.put("poNo", "1002");
		bodyp2.put("productCode", "XFLCODE2");
		bodyp2.put("orderQuantity", "20002");
		body.add(bodyp1);
		body.add(bodyp2);
		params.put("body", body);
			
		String ret = httpPost(url, params).toString();
		System.out.println("======ljc testReplenishMentPurchase1:"+ret);
		
		}catch(Exception ex)
		{
			ex.printStackTrace();
		}
	}
	
	public static void testReplenishMentPurchase2() {
		String url = REPLENISHMENT_REQ_URL;
		try
		{
		
		ZCReplenishMentBean zcReplenishMentBean = new ZCReplenishMentBean();
		zcReplenishMentBean.setMerId("10002");
		Date dateTime = new Date();
		Calendar cal=Calendar.getInstance();
		cal.setTime(dateTime);
		zcReplenishMentBean.setValidTime(DateUtils.toDateStr(cal, 16));
		
		//header
		ReplenishMentHeaderBean replenishMentHeaderBean = new ReplenishMentHeaderBean();
		replenishMentHeaderBean.setBuyerName("中驰商务电子信息有限公司");
		replenishMentHeaderBean.setPoHeaderNo("VTH1611211824120002");
		replenishMentHeaderBean.setExpDeliveyDate("21323213123213");
		replenishMentHeaderBean.setOrderingTime("2016-11-17 00:24");
		replenishMentHeaderBean.setRdcWareHouseCode("a1025368");
		replenishMentHeaderBean.setRdcWareHouseName("北京1号库");
		replenishMentHeaderBean.setRdcAddress("北京市海淀区永丰路");
		replenishMentHeaderBean.setContacter("张三");
		replenishMentHeaderBean.setContactPhone("123123213");
		zcReplenishMentBean.setHeader(replenishMentHeaderBean);
		replenishMentHeaderBean.setStatus("0");
		//需要签名字段排序
		HashMap<String, String> paramMap = new HashMap<String, String>();
		List<String> commonallParams = new ArrayList<String>();//客户端暂时没有用,,服务端必须要
		List<String> headerallParams = new ArrayList<String>();//客户端暂时没有用，，服务端必须要
		List<String> commonSignParams = new ArrayList<String>();
		List<String> headerSignParams = new ArrayList<String>();
		
		List<String> needParams = new ArrayList<String>();
		ReflectHelper.getParametersFromJavaBean(zcReplenishMentBean, replenishMentHeaderBean, commonallParams, commonSignParams,headerallParams,headerSignParams, paramMap,needParams);
		if(null==commonSignParams || commonSignParams.isEmpty())
		{
			System.out.println("参数不对");
			return;
		}
		
		if(null==headerSignParams || headerSignParams.isEmpty())
		{
			System.out.println("头部参数不对");
			return;
		}
		
		StringBuilder signedParamsStr = new StringBuilder();
		Collections.sort(headerSignParams);
		for(String headerNeedSign:headerSignParams)
		{
			System.out.println(headerNeedSign+"-------"+paramMap.get(headerNeedSign));
			String headerSignParamData = paramMap.get(headerNeedSign);
			signedParamsStr.append(headerSignParamData);
			signedParamsStr.append("|");
		}
		//公共字段不用排序
//		Collections.sort(commonSignParams);
//		for(String commonNeedSign:commonSignParams)
//		{
//			System.out.println(commonNeedSign+"-------"+paramMap.get(commonNeedSign));
//			String commonSignParamData = paramMap.get(commonNeedSign);
//			signedParamsStr.append(commonSignParamData);
//			signedParamsStr.append("|");
//		}
		signedParamsStr.append(paramMap.get(Constants.ZC_API_VALIDTIME)+"|");
		signedParamsStr.append(paramMap.get(Constants.ZC_API_MERID)+"|");
		signedParamsStr.append(Constants.ZC_API_KEY);
		String signData = EncryptionUtil.getMD5Result(signedParamsStr.toString());
		zcReplenishMentBean.setSign(signData);
		
		
		//body
		ReplenishMentBodyBean replenishMentBodyBean = new ReplenishMentBodyBean();
		ReplenishMentBodyBean replenishMentBodyBean2 = new ReplenishMentBodyBean();
		List<ReplenishMentBodyBean> bodylst = new ArrayList<ReplenishMentBodyBean>();
		replenishMentBodyBean.setPoNo("1001");
		replenishMentBodyBean.setProductCode("GWBT20001");
		replenishMentBodyBean.setOrderQuantity("521");
		
		replenishMentBodyBean2.setPoNo("1002");
		replenishMentBodyBean2.setProductCode("GWBT20002");
		replenishMentBodyBean2.setOrderQuantity("522");
		bodylst.add(replenishMentBodyBean);
		bodylst.add(replenishMentBodyBean2);
		zcReplenishMentBean.setBody(bodylst);
		
		String reqJSONStr = JsonUtil.writeValue(zcReplenishMentBean);
		System.out.println(reqJSONStr);
		JSONObject zcReplenishMentBeanStr = JSONObject.fromObject(reqJSONStr);

		Map<String, String> httpHeader = new HashMap<String, String>();
		httpHeader.put("Content-Type", "application/json;charset=utf-8");
//		String ret = HttpSender.post(url, zcReplenishMentBeanStr, null, httpHeader);
		String ret = httpPost(url, zcReplenishMentBeanStr).toString();
		System.out.println("======ljc testReplenishMentPurchase2"+ret);
		
		}catch(Exception ex)
		{
			ex.printStackTrace();
		}
	}
	

	
	
	
	public static void main(String arg[]) throws Exception {
		//testReturnedPurchase1();
		testReturnedPurchase2();
		//testReplenishMentPurchase1();
		//testReplenishMentPurchase2();
	}
	
}
