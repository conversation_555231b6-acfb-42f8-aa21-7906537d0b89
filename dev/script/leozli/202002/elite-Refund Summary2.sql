select crss.customer_name_cn, crss.customer_name_en, 
case when crss.region_name='Industrial' then '' else crss.region_name end cdm_region_name, 
case when crss.region_name='Industrial' then '' else crss.sales_name end cdm_sales_name, 
crss.region_name cio_region_name, crss.sales_name cio_sales_name,
bd_fund_cdm_q1,mkt_fund_cdm_q1,ivi_fund_cdm_q1,bd_fund_cio_q1,mkt_fund_cio_q1,ivi_fund_cio_q1,
bd_fund_cdm_q234,mkt_fund_cdm_q234,ivi_fund_cdm_q234,bd_fund_cio_q234,mkt_fund_cio_q234,
ivi_fund_cio_q234, isnull(ciobdffq12.fund_value, 0) bd_ffund_cioq12,isnull(ciobdffq12.ivi_fund, 0) bd_fivi_cioq12,
isnull(ciomktffq12.fund_value, 0) mkt_ffund_cioq12,isnull(ciomktffq12.ivi_fund, 0) mkt_fivi_cioq12,
bd_fund_cio_q1+bd_fund_cio_q234-isnull(ciobdffq12.fund_value, 0) bd_ffund_cioq34,
0 bd_fivi_cioq34, mkt_fund_cio_q1+mkt_fund_cio_q234-isnull(ciomktffq12.fund_value, 0) mkt_ffund_cioq34,
ivi_fund_cio_q1+ivi_fund_cio_q234-isnull(ciobdffq12.ivi_fund, 0)-isnull(ciomktffq12.ivi_fund, 0) mkt_fivi_cioq34,

isnull(cdmbdffq12.fund_value, 0) bd_ffund_cdmq12,isnull(cdmbdffq12.ivi_fund, 0) bd_fivi_cdmq12,
isnull(cdmmktffq12.fund_value, 0) mkt_ffund_cdmq12,isnull(cdmmktffq12.ivi_fund, 0) mkt_fivi_cdmq12,
bd_fund_cdm_q1+bd_fund_cdm_q234-isnull(cdmbdffq12.fund_value, 0) bd_ffund_cdmq34,
0 bd_fivi_cdmq34,mkt_fund_cdm_q1+mkt_fund_cdm_q234-isnull(cdmmktffq12.fund_value, 0) mkt_ffund_cdmq34,
ivi_fund_cdm_q1+ivi_fund_cdm_q234-isnull(cdmbdffq12.ivi_fund, 0)-isnull(cdmmktffq12.ivi_fund, 0) mkt_fivi_cdmq34
 from (SELECT t1.Distributor_id AS org_id, 2019 year
		, SUM(case when Quarter='Q1' then ISNULL(actual_CDM_BD_Fund, 0) else 0 end) AS bd_fund_cdm_q1
		, SUM(case when Quarter='Q1' then ISNULL(actual_CDM_Marketing_Fund, 0) else 0 end) AS mkt_fund_cdm_q1
		, SUM(case when Quarter='Q1' then ISNULL(actual_CDM_IVI_Fund, 0) else 0 end) AS ivi_fund_cdm_q1
		, SUM(case when Quarter='Q1' then ISNULL(actual_CIO_BD_Fund, 0) else 0 end) AS bd_fund_cio_q1
		, SUM(case when Quarter='Q1' then ISNULL(actual_CIO_Marketing_Fund, 0) else 0 end) AS mkt_fund_cio_q1
		, SUM(case when Quarter='Q1' then ISNULL(actual_CIO_IVI_Fund, 0) else 0 end) AS ivi_fund_cio_q1
		, SUM(case when Quarter='Q1' then 0 else ISNULL(actual_CDM_BD_Fund, 0) end) AS bd_fund_cdm_q234
		, SUM(case when Quarter='Q1' then 0 else ISNULL(actual_CDM_Marketing_Fund, 0) end) AS mkt_fund_cdm_q234
		, SUM(case when Quarter='Q1' then 0 else ISNULL(actual_CDM_IVI_Fund, 0) end) AS ivi_fund_cdm_q234
		, SUM(case when Quarter='Q1' then 0 else ISNULL(actual_CIO_BD_Fund, 0) end) AS bd_fund_cio_q234
		, SUM(case when Quarter='Q1' then 0 else ISNULL(actual_CIO_Marketing_Fund, 0) end) AS mkt_fund_cio_q234
		, SUM(case when Quarter='Q1' then 0 else ISNULL(actual_CIO_IVI_Fund, 0) end) AS ivi_fund_cio_q234
	FROM dw_pp_customer_fund_quarter_new_view t1
	WHERE 1 = 1
		AND t1.year = '2019'
	GROUP BY t1.Distributor_id) cfq
left join dw_customer_region_sales_supervisor_rel crss on cfq.org_id=crss.distributor_id
left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name 
left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '1'
			AND sales_channel = 'C&I'
			AND form_status >= 30
			and ext_property1='q1q2'
		GROUP BY distributor_id, program_year) ciobdffq12 on ciobdffq12.org_id=cfq.org_id and ciobdffq12.year=cfq.year
left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '2'
			AND sales_channel = 'C&I'
			AND form_status >= 30
			and ext_property1='q1q2'
		GROUP BY distributor_id, program_year) ciomktffq12 on ciomktffq12.org_id=cfq.org_id and ciomktffq12.year=cfq.year

left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '1'
			AND sales_channel = 'C&I'
			AND form_status >= 30
			and ext_property1='q3q4'
		GROUP BY distributor_id, program_year) ciobdffq34 on ciobdffq34.org_id=cfq.org_id and ciobdffq34.year=cfq.year
left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '2'
			AND sales_channel = 'C&I'
			AND form_status >= 30
			and ext_property1='q3q4'
		GROUP BY distributor_id, program_year) ciomktffq34 on ciomktffq34.org_id=cfq.org_id and ciomktffq34.year=cfq.year


left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '1'
			AND sales_channel = 'CDM'
			AND form_status >= 30
			and ext_property1='q1q2'
		GROUP BY distributor_id, program_year) cdmbdffq12 on cdmbdffq12.org_id=cfq.org_id and cdmbdffq12.year=cfq.year
left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '2'
			AND sales_channel = 'CDM'
			AND form_status >= 30
			and ext_property1='q1q2'
		GROUP BY distributor_id, program_year) cdmmktffq12 on cdmmktffq12.org_id=cfq.org_id and cdmmktffq12.year=cfq.year

left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '1'
			AND sales_channel = 'CDM'
			AND form_status >= 30
			and ext_property1='q3q4'
		GROUP BY distributor_id, program_year) cdmbdffq34 on cdmbdffq34.org_id=cfq.org_id and cdmbdffq34.year=cfq.year
left join (SELECT distributor_id AS org_id, program_year AS year
			, SUM(isnull(final_amount, 0) - isnull(use_ivi_amount, 0)) AS fund_value
			, SUM(use_ivi_amount) AS ivi_fund
		FROM wx_t_elite_fund_form
		WHERE fund_type = '2'
			AND sales_channel = 'CDM'
			AND form_status >= 30
			and ext_property1='q3q4'
		GROUP BY distributor_id, program_year) cdmmktffq34 on cdmmktffq34.org_id=cfq.org_id and cdmmktffq34.year=cfq.year
where rsc.sales_channel_name in ('Indirect','Industrial')