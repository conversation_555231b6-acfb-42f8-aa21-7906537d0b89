(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0c61e046"],{"4e42":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("title-piece",{attrs:{title:"Temporary Credit Request Form 临时信用额度申请表"}},[i("buttons",{attrs:{id:e.id,"show-download-btn":""}})],1),i("basic"),i("finance"),e.showHistory?i("history"):e._e(),i("upload")],1)},a=[],l=i("768b"),n=i("f221"),c=i("1e29"),o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"tempFinance",staticClass:"form",attrs:{model:e.cfiInfo,rules:e.rules}},[i("first"),e.isCredit?i("div",{staticClass:"form-title"},[e._v("\n    Customer Finance Information 客户财务信息\n  ")]):e._e(),e.isCredit?i("basic"):e._e(),e.isCredit?i("short"):e._e(),e.isCredit?i("long"):e._e(),e.isCredit?i("assets"):e._e(),e.isCredit?i("profitability"):e._e(),i("last")],1)},s=[],d=i("cebc"),m=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-row",[i("el-col",{attrs:{span:11}},[i("confirmed-temp-credit-limit")],1),i("el-col",{attrs:{span:13}},[i("confirmed-temp-payment-term")],1),i("el-col",{attrs:{span:11}},[i("confirmed-expired-date")],1)],1)},f=[],u=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Confirmed Temp. Credit limit 最终批准的临时总额度 : ","label-width":"360px",prop:"cfiConfirmedTempCreditLimit"}},[i("el-input",{attrs:{disabled:e.disabled,size:"small",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},p=[],b=i("2f62"),C=i("65e9"),h={name:"credit-apply-cfiConfirmedTempCreditLimit",computed:Object(d["a"])({},Object(b["b"])(["cfiInfo","canEditComfirmedCredit"]),{disabled:function(){return!this.canEditComfirmedCredit},value:{get:function(){return Object(C["a"])(this.cfiInfo.cfiConfirmedTempCreditLimit)},set:function(e){this.$store.commit("UPDATE_APPLY_FORM",{cfiInfo:{cfiConfirmedTempCreditLimit:Object(C["b"])(e)}})}}})},v=h,y=i("2877"),T=Object(y["a"])(v,u,p,!1,null,null,null),O=T.exports,E=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Confirmed Temp. Payment Term 最终审批临时账期 : ","label-width":"310px",prop:"cfiConfirmedTempPaymentTerm"}},[i("el-select",{attrs:{placeholder:"select",disabled:e.disabled,size:"small"},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)},_=[],P={name:"credit-apply-cfiConfirmedTempPaymentTerm",computed:Object(d["a"])({},Object(b["b"])(["cfiInfo","canEditComfirmedCredit","paymentTermListOptions"]),{disabled:function(){return!this.canEditComfirmedCredit},value:{get:function(){return this.cfiInfo.cfiConfirmedTempPaymentTerm},set:function(e){this.$store.commit("UPDATE_APPLY_FORM",{cfiInfo:{cfiConfirmedTempPaymentTerm:e}})}},options:{get:function(){var e=this.paymentTermListOptions||[];return e.map(function(e){return{label:e,value:e}})}}})},g=P,j=Object(y["a"])(g,E,_,!1,null,null,null),x=j.exports,I=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Confirmed Expire Date 最终审批到期日 : ","label-width":"360px",prop:"cfiConfirmedExpiredDate"}},[i("el-date-picker",{attrs:{type:"date",placeholder:"select date",disabled:e.disabled,size:"small"},model:{value:e.cfiInfo.cfiConfirmedExpiredDate,callback:function(t){e.$set(e.cfiInfo,"cfiConfirmedExpiredDate",t)},expression:"cfiInfo.cfiConfirmedExpiredDate"}})],1)},L=[],w={name:"credit-apply-cbiConfirmedExpiredDate",computed:Object(d["a"])({},Object(b["b"])(["cfiInfo","canEditComfirmedCredit"]),{disabled:function(){return!this.canEditComfirmedCredit}})},$=w,R=Object(y["a"])($,I,L,!1,null,null,null),k=R.exports,F={name:"credit-apply-fianace-basic",components:{ConfirmedTempCreditLimit:O,ConfirmedTempPaymentTerm:x,ConfirmedExpiredDate:k}},A=F,q=Object(y["a"])(A,m,f,!1,null,null,null),D=q.exports,H=i("dcfc"),V=i("cf8d"),z=i("a3b4"),S=i("0ea0"),Y=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("h4",[e._v("PROFITABILITY  MEASURES ")]),i("el-row",[i("el-col",{attrs:{span:8}},[i("working-capital")],1),i("el-col",{attrs:{span:4}},[i("equity")],1),i("el-col",{attrs:{span:7}},[i("working-assets")],1),i("el-col",{attrs:{span:5}},[i("estimated-value")],1),i("el-col",{attrs:{span:12}},[i("credit-limit-estimated-value")],1),i("el-col",{attrs:{span:12}},[i("calculated-credit-limit-per-credit-policy")],1),i("el-col",{attrs:{span:12}},[i("current-requested-temp-credit-limit-of-the-calculated-credit-limit")],1),i("el-col",{attrs:{span:6}},[i("total-score")],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("rec-add-temp-credit-limit")],1),i("el-col",{attrs:{span:12}},[i("rec-temp-payment-term")],1)],1)],1)},N=[],U=i("80d9"),B=i("e5e5"),M=i("4140"),W=i("24ec"),J=i("ee75"),G=i("7fb4"),K=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Current+Requested Temp. Credit Limit of The Calculated Credit Limit % : ","label-width":"420px"}},[i("el-input",{attrs:{disabled:"",size:"small",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},Q=[],X=i("5fe9"),Z=i.n(X),ee={name:"credit-apply-cfiCurrentRequestedTempCreditLimitOfTheCalculatedCreditLimit",computed:Object(d["a"])({},Object(b["b"])(["applyForm","cfiInfo"]),{value:{get:function(){var e=this.applyForm.cbiCreditLimitOfYearN1,t=this.applyForm.cbiRequestedTempCreditLimit,i=this.cfiInfo.cfiCalculatedCreditLimitPerCreditPolicy;try{return(100*Z.a.divide(Z.a.plus(e,t),i)).toFixed(2)+"%"}catch(r){return""}}}})},te=ee,ie=Object(y["a"])(te,K,Q,!1,null,null,null),re=ie.exports,ae=i("fe51"),le=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Rec. Add Temp. Credit limit : ","label-width":"420px",prop:"cfiRecAddTempCreditLimit"}},[i("el-input",{attrs:{disabled:e.disabled,size:"small",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},ne=[],ce={name:"credit-apply-cfiRecAddTempCreditLimit",computed:Object(d["a"])({},Object(b["b"])(["cfiInfo","canEditComfirmedCredit"]),{disabled:function(){return!this.canEditComfirmedCredit},value:{get:function(){return Object(C["a"])(this.cfiInfo.cfiRecAddTempCreditLimit)},set:function(e){this.$store.commit("UPDATE_APPLY_FORM",{cfiInfo:{cfiRecAddTempCreditLimit:Object(C["b"])(e)}})}}})},oe=ce,se=Object(y["a"])(oe,le,ne,!1,null,null,null),de=se.exports,me=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Rec. Temp. Payment Term : ","label-width":"380px",prop:"cfiRecTempPaymentTerm"}},[i("el-select",{attrs:{placeholder:"select",disabled:e.disabled,size:"small"},model:{value:e.cfiInfo.cfiRecTempPaymentTerm,callback:function(t){e.$set(e.cfiInfo,"cfiRecTempPaymentTerm",t)},expression:"cfiInfo.cfiRecTempPaymentTerm"}},e._l(e.options,function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)},fe=[],ue={name:"credit-apply-cfiRecTempPaymentTerm",computed:Object(d["a"])({},Object(b["b"])(["cfiInfo","canEditComfirmedCredit","paymentTermListOptions"]),{disabled:function(){return!this.canEditComfirmedCredit},options:{get:function(){var e=this.paymentTermListOptions||[];return e.map(function(e){return{label:e,value:e}})}}})},pe=ue,be=Object(y["a"])(pe,me,fe,!1,null,null,null),Ce=be.exports,he={name:"credit-apply-fianace-profitability-temp",components:{WorkingCapital:U["a"],Equity:B["a"],WorkingAssets:M["a"],EstimatedValue:W["a"],CreditLimitEstimatedValue:J["a"],CalculatedCreditLimitPerCreditPolicy:G["a"],CurrentRequestedTempCreditLimitOfTheCalculatedCreditLimit:re,TotalScore:ae["a"],RecAddTempCreditLimit:de,RecTempPaymentTerm:Ce}},ve=he,ye=Object(y["a"])(ve,Y,N,!1,null,null,null),Te=ye.exports,Oe=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-row",[i("el-col",{attrs:{span:12}},[i("comments-from-credit")],1)],1)},Ee=[],_e=i("5a62"),Pe={name:"credit-apply-fianace-basic",components:{CommentsFromCredit:_e["a"]}},ge=Pe,je=Object(y["a"])(ge,Oe,Ee,!1,null,null,null),xe=je.exports,Ie=/((^[1-9]\d*)|^0)(\.\d{2}){0,1}$/,Le={cfiYearN1PaymentRecord:[{required:!0,message:"",trigger:"blur"}],cfiPayHistoryWithChevron:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,i){t?Ie.test(Object(C["b"])(t))?i():i(new Error("")):i()}}],cfiDsoInChevronChina:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,i){t?Ie.test(Object(C["b"])(t))?i():i(new Error("")):i()}}],cfiConfirmedTempCreditLimit:[{validator:function(e,t,i){t?Ie.test(Object(C["b"])(t))?i():i(new Error("")):i()}}],cfiRecAddTempCreditLimit:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,i){t?Ie.test(Object(C["b"])(t))?i():i(new Error("")):i()}}],cfiRecTempPaymentTerm:[{required:!0,message:"",trigger:"blur"}]},we=i("e681"),$e={name:"credit-apply-finance-cv",components:{First:D,Basic:H["a"],Short:V["a"],Long:z["a"],Assets:S["a"],Profitability:Te,Last:xe},data:function(){return{rules:Le}},computed:Object(d["a"])({},Object(b["b"])(["cfiInfo","isCredit"])),created:function(){var e=this;we["a"].$on("tempFinanceValidate",function(t){e.$refs.tempFinance.validate(t)}),we["a"].$on("tempPayHistoryValidate",function(t){console.log("tempPayHistoryValidate trigger"),e.$refs.tempFinance.validateField("cfiPayHistoryWithChevron",t)})},destroyed:function(){we["a"].$off("tempFinanceValidate"),we["a"].$off("tempPayHistoryValidate")}},Re=$e,ke=Object(y["a"])(Re,o,s,!1,null,null,null),Fe=ke.exports,Ae=i("900b"),qe=i("dd4a"),De=i("d4de"),He={name:"credit-apply-temp-review",components:{TitlePiece:n["a"],Basic:c["a"],Finance:Fe,Buttons:Ae["a"],History:qe["a"],Upload:De["a"]},data:function(){return{showHistory:!1,id:this.$route.query.id,fromPage:this.$route.query.fromPage,formVersionNo:this.$route.query.formVersionNo,lockerId:this.$route.query.lockerId}},created:function(){var e=this;this.$store.dispatch("getCreditApply",{id:this.id,fromPage:this.fromPage,lockerId:this.lockerId?this.lockerId:""}).then(function(t){var i=Object(l["a"])(t,1),r=i[0];r&&(e.showHistory=!0)})}},Ve=He,ze=Object(y["a"])(Ve,r,a,!1,null,null,null);t["default"]=ze.exports},fe51:function(e,t,i){"use strict";var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",{attrs:{label:"Chevron Scoring : ","label-width":"250px"}},[i("el-input",{attrs:{disabled:"",size:"small",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},a=[],l=(i("c5f6"),i("cebc")),n=i("2f62"),c=i("5fe9"),o=i.n(c),s={name:"credit-apply-cfiTotalScore",computed:Object(l["a"])({},Object(n["b"])(["cfiInfo"]),{value:{get:function(){var e=Number(this.cfiInfo.cfiTotalScore);return o.a.round(e,2)}}})},d=s,m=i("2877"),f=Object(m["a"])(d,r,a,!1,null,null,null);t["a"]=f.exports}}]);
//# sourceMappingURL=chunk-0c61e046.f6f4501e.js.map