---窜货：
delete from wx_t_non_local_mkt_flow_form;  --流程主表
delete from wx_t_loc_mkt_product_info; --上报的产品表
delete from wx_t_Main_Local_marketing ; --上报主记录表
delete from wx_t_non_local_mkt_flow_pro_res ; --处理结果表
delete from wx_t_non_local_times_record; --串货次数累计表（按经销商和渠道汇总的）
delete from wx_t_non_local_mkt_emaile_info ; ---清邮件发放
delete FROM  wx_att_file   where source_type in (55,54);--清除非本地营销 处理的pdf以及上传邮件盖章模板数据
delete FROM  wx_t_message_notify_schedule where source_type  ='NON_LOCAL_MKT_NOTICE';
delete from wx_t_workflow_instance where workflow_code  = 'NON_LOCK_MKT'

-----册除历史记录
--1先删子任务SELECT * FROM  
DELETE wx_task_sub where task_id in (
SELECT s.task_id FROM wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
where tmb_type_code ='TT_2_FEELING_GOODS');
--2再删主任务SELECT * FROM 
DELETE wx_task_main where tmb_type_code ='TT_2_FEELING_GOODS';
--首面的合格不合格都修改成进行中
update wx_t_local_marketing set result_code = 2 where YEAR(feed_back_date) = 2020 and feed_back_month  = '12'