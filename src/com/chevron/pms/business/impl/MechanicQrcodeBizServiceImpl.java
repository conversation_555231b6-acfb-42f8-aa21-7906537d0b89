package com.chevron.pms.business.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.chevron.pms.business.MechanicQrcodeBizService;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.dao.MechanicQrcodeMapper;
import com.chevron.pms.model.MechanicQrcode;
import com.chevron.pms.model.MechanicQrcodeExample;
import com.chevron.pms.model.OilVerificationImportVo;
import com.chevron.pms.service.MechanicQrcodeService;
import com.chevron.pms.service.QrCodeService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.StringUtils;

@Service
public class MechanicQrcodeBizServiceImpl implements MechanicQrcodeBizService {
	
	@Autowired
	private MechanicQrcodeMapper mechanicQrcodeMapper;
	
	@Resource
	private MechanicQrcodeService mechanicQrcodeServiceImpl;
	
	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	@Autowired
	private QrCodeService qrCodeServiceImpl;
	
	@Autowired
	private WorkshopEmployeeBizService workshopEmployeeBizService;
	
	@Override
	public void saveMechanicQrCode(String qrCode, String sku, String mechanicCode) {
		MechanicQrcode mechanicQrcode = new MechanicQrcode();
		mechanicQrcode.setMechanicCode(mechanicCode);
		mechanicQrcode.setCreatedBy(ContextUtil.getCurUserId());
		mechanicQrcode.setQrCode(qrCode);
		mechanicQrcode.setSku(sku);
		mechanicQrcode.setWorkshopId(workshopEmployeeBizService.queryWorkshopEmployeeByCode(mechanicCode).getWorkshopId());
		mechanicQrcodeMapper.insertSelective(mechanicQrcode);
	}

	@Override
	public String getQrCodeFirstMechanic(String qrCode) {
		MechanicQrcodeExample example = new MechanicQrcodeExample();
		example.createCriteria().andQrCodeEqualTo(qrCode);
		List<MechanicQrcode> resultList = mechanicQrcodeMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			return resultList.get(0).getMechanicCode();
		}
		return null;
	}

	@Override
	public long getMechanicQrcodeTotalCapacity(String mechanicCode, Date dateFrom, Date dateTo) {
		Long totalCapacity = mechanicQrcodeMapper.getQrcodeTotalCapacityByMechanicCode(mechanicCode, dateFrom, dateTo); 
		return totalCapacity == null ? 0 : totalCapacity.longValue();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void importDataByTraceCode(final List<OilVerificationImportVo> data, final Map<String, Object> progressStatus)
			throws WxPltException {
		try {
			//分批次查询QRCODE、技师编码以及是否已核销验证
			List<Long[]> qrcodeParams = new ArrayList<Long[]>();
			List<StringBuilder> qrcodeSqlList = new ArrayList<StringBuilder>();
			List<List<OilVerificationImportVo>> importParamList = new ArrayList<List<OilVerificationImportVo>>();
			Long[] qrcodeParam = null;
			StringBuilder qrcodeSql = null;
			List<OilVerificationImportVo> importParam = null;
			int index = 0;
			int QRCODE_PARAM_SIZE = 500;
			for(OilVerificationImportVo vo : data){
				if(qrcodeParam == null){
					if(data.size() <= QRCODE_PARAM_SIZE + index){
						qrcodeParam = new Long[data.size() - index];
					}else{
						qrcodeParam = new Long[QRCODE_PARAM_SIZE];
					}
					importParam = new ArrayList<OilVerificationImportVo>();
					importParamList.add(importParam);
					qrcodeParams.add(qrcodeParam);
					qrcodeSql = new StringBuilder();
					qrcodeSqlList.add(qrcodeSql);
				}else{
					qrcodeSql.append(" union all ");
				}
				qrcodeSql.append("select d.code_id, d.qr_code, ").append(index).append(" index0 from wx_t_qr_code_detail d where d.code_id=?");
				qrcodeParam[index % QRCODE_PARAM_SIZE] = qrCodeServiceImpl.getCodeId(vo.getLogisticsCode());
				importParam.add(vo);
				if(++index == QRCODE_PARAM_SIZE){
					qrcodeParam = null;
				}
			}
			//执行分批次查询及组装核销参数
			Map<String, StringBuilder> verifyParamMap = new HashMap<String, StringBuilder>();
			StringBuilder notExistsMsg = null;
			StringBuilder verifiedMsg = null;
			for(int i = 0; i < qrcodeParams.size(); i++){
				progressStatus.put("desc", "组装导入参数：第" + (i  + 1) + "批次" + qrcodeParams.get(i).length + "条/" + data.size() + "条...");
				jdbcTemplate.query(qrcodeSqlList.get(i).toString(), 
						qrcodeParams.get(i), new RowMapper<Object>() {

							@Override
							public Object mapRow(ResultSet arg0, int arg1)
									throws SQLException {
								data.get(arg0.getInt("index0")).setQrcode(arg0.getString("qr_code"));
								return null;
							}});
				for(MechanicQrcode mechanicQrcode : mechanicQrcodeMapper.getImportInfoByTraceCode(importParamList.get(i))){
					if("-1".equals(mechanicQrcode.getQrCode())){
						if(verifiedMsg == null){
							verifiedMsg = new StringBuilder("以下物流码已核销：");
						}else{
							verifiedMsg.append(",");
						}
						verifiedMsg.append(importParamList.get(i).get(((Long)mechanicQrcode.getId()).intValue()).getLogisticsCode());
					}else if(StringUtils.isBlank(mechanicQrcode.getQrCode())){
						if(notExistsMsg == null){
							notExistsMsg = new StringBuilder("以下物流码不存在：");
						}else{
							notExistsMsg.append(",");
						}
						notExistsMsg.append(importParamList.get(i).get(((Long)mechanicQrcode.getId()).intValue()).getLogisticsCode());
					}else{
						//组装核销参数
						StringBuilder verifyParam = verifyParamMap.get(mechanicQrcode.getMechanicCode());
						if(verifyParam == null){
							verifyParam = new StringBuilder();
							verifyParamMap.put(mechanicQrcode.getMechanicCode(), verifyParam);
						}else{
							verifyParam.append(",");
						}
						verifyParam.append(mechanicQrcode.getQrCode());
					}
				}
			}
			//验证判断
			if(notExistsMsg != null || verifiedMsg != null){
				StringBuilder errorMsg = notExistsMsg;
				if(errorMsg == null){
					errorMsg = verifiedMsg;
				}else if(verifiedMsg != null){
					errorMsg.append("; <br/>").append(verifiedMsg);
				}
				throw new WxPltException(errorMsg.toString());
			}
			int i = 1;
			//分技师调用核销接口
			for(String mechanicCode : verifyParamMap.keySet()){
				progressStatus.put("desc", "调用技师核销接口：" + i++ + "/" + verifyParamMap.size() + "技师...");
				Map<String, Object> resultMap = mechanicQrcodeServiceImpl.saveMechanicQrcode(mechanicCode, verifyParamMap.get(mechanicCode).toString());
				if(!"success".equals(resultMap.get("code"))){
					throw new WxPltException((String)resultMap.get("codeMsg"));
				}
			}
		}catch (WxPltException e) {
			throw e;
		} catch (Exception e) {
			e.printStackTrace();
			throw new WxPltException("\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u5931\u8d25: " + e.getMessage(), e);
		}
	}

	@Override
	public MechanicQrcode getMechanicQrcodeByQrcode(String qrcode) {
		MechanicQrcodeExample example = new MechanicQrcodeExample();
		example.createCriteria().andQrCodeEqualTo(qrcode);
		List<MechanicQrcode> resultList = mechanicQrcodeMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			return resultList.get(0);
		}
		return null;
	}

	@Override
	public int validateMechanicQrcode(Map<String, Object> paramMap) {
		return mechanicQrcodeMapper.validateMechanicQrcode(paramMap);
	}

}
