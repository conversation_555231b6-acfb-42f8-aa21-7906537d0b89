--insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Oil_Apply_Test_Track'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());
