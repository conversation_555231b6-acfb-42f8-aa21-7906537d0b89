--设置可代理菜单 金富力店招，德乐店招，精英计划，兑换汇总，精英计划汇总，商用油油样申请，资源包申请
update m set m.workflow_delegate_flag=1 from wx_t_menu m where menu_id in ();

insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Direct_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Channel_Manager'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Direct_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Supervisor'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Direct_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Sales'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Channel_Manager'), (select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Supervisor'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Channel_Manager'), (select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Sales'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Supervisor'), (select r.role_id from wx_t_role r where r.ch_role_name='Chevron_OEM_Sales'), 1, getdate());

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('WorkflowDelegate.parentMenuCode', '33', '流程代理父菜单编码');