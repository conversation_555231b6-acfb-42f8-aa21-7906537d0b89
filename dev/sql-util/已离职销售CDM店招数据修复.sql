select a.*, u1.user_id, u1.ch_name from wx_t_mkt_apply a
left join dw_customer_region_sales_supervisor_rel cv1 on a.partner_id=cv1.distributor_id
left join wx_t_user u1 on u1.cai=cv1.sales_cai and u1.status=1
where a.id in (
select id from view_mkt_apply v
 where (flow_finish3 is null or flow_finish3!=1) and mkt_type in ('STORE_FRONT', 'STORE_IN_STORE')
and not exists (
select 1
from view_customer_region_sales_channel cv
left join wx_t_user u  on cv.sales_cai=u.cai
where u.login_name=v.apply_user))
