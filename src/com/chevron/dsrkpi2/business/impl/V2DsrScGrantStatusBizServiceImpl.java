package com.chevron.dsrkpi2.business.impl;

import com.chevron.dsrkpi2.business.V2DsrScGrantStatusBizService;
import com.chevron.dsrkpi2.dao.V2DsrScAdjustHistoryMapper;
import com.chevron.dsrkpi2.dao.V2DsrScAdjustInfoMapper;
import com.chevron.dsrkpi2.dao.V2DsrScGrantHistoryMapper;
import com.chevron.dsrkpi2.dao.V2DsrScGrantStatusMapper;
import com.chevron.dsrkpi2.model.V2DsrScAdjustHistory;
import com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryParams;
import com.chevron.dsrkpi2.model.V2DsrScAdjustInfo;
import com.chevron.dsrkpi2.model.V2DsrScAdjustInfoExample;
import com.chevron.dsrkpi2.model.V2DsrScGrantHistory;
import com.chevron.dsrkpi2.model.V2DsrScGrantStatus;
import com.chevron.dsrkpi2.model.V2DsrScGrantStatusExample;
import com.chevron.dsrkpi2.model.V2DsrScGrantStatusParams;
import com.chevron.mktresource.Utils.SortUtils;
import com.chevron.pms.service.PartnerService;
import com.chevron.point.business.PointBizService;
import com.chevron.report.business.BiProcedureBizService;
import com.chevron.report.model.DsrKpiServiceFeePaymentField;
import com.chevron.report.model.DsrKpiServiceFeePaymentParam;
import com.chevron.report.model.DsrKpiServiceFeePaymentVo;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.BatchOperationUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MathUtil;
import com.common.util.ResponseMap;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DSR服务费发放状态业务接口实现类
 * <AUTHOR>
 * @version 1.0 2021-04-16 10:23
 */
@Service
public class V2DsrScGrantStatusBizServiceImpl implements V2DsrScGrantStatusBizService {
	@Autowired
	private V2DsrScGrantStatusMapper v2DsrScGrantStatusMapper;
	@Autowired
	private V2DsrScGrantHistoryMapper v2DsrScGrantHistoryMapper;
	@Autowired
	private V2DsrScAdjustInfoMapper v2DsrScAdjustInfoMapper;
	@Autowired
	private V2DsrScAdjustHistoryMapper v2DsrScAdjustHistoryMapper;
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	@Autowired
	private BiProcedureBizService biProcedureBizService;
	@Autowired
	private PartnerService partnerService;

	@Autowired
	private PointBizService pointBizService;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(V2DsrScGrantStatus record) throws WxPltException {
		v2DsrScGrantStatusMapper.insertSelective(record);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(V2DsrScGrantStatus record) throws WxPltException {
		v2DsrScGrantStatusMapper.updateByPrimaryKeySelective(record);
	}


	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		V2DsrScGrantStatusExample example = new V2DsrScGrantStatusExample();
		example.createCriteria().andIdIn(ids);
		v2DsrScGrantStatusMapper.deleteByExample(example);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(V2DsrScGrantStatusExample example) throws WxPltException {
		v2DsrScGrantStatusMapper.deleteByExample(example);
	}

	@Override
	public List<V2DsrScGrantStatus> queryByExample(V2DsrScGrantStatusExample example) throws WxPltException {
		return v2DsrScGrantStatusMapper.selectByExample(example);
	}

	@Override
	public List<V2DsrScGrantStatus> queryByParams(Map<String, Object> params) throws WxPltException {
		return v2DsrScGrantStatusMapper.queryByParams(params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateForEditPage(V2DsrScGrantStatus record) throws WxPltException {
		v2DsrScGrantStatusMapper.updateForEditPage(record);
	}

	@Override
	public V2DsrScGrantStatus getBean(Long id) throws WxPltException{
		return v2DsrScGrantStatusMapper.selectByPrimaryKey(id);
	}

	@Override
	public void queryForPage(V2DsrScGrantStatusParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public List<V2DsrScGrantStatus> queryForPage(V2DsrScGrantStatusParams params) throws WxPltException {
		return v2DsrScGrantStatusMapper.queryForPage(params);
	}

	@Override
	public ResponseMap queryForStatus(V2DsrScGrantStatusParams params) throws WxPltException {
		ResponseMap resultMap = new ResponseMap();
		Map<String, Object> data = new HashMap<String, Object>(2);
		if(params == null){
			throw new WxPltException("参数不能为空");
		}
		if(params.getGrantYear() == null){
			throw new WxPltException("服务费发放年份不能为空");
		}
		if(params.getGrantMonth() == null){
			throw new WxPltException("服务费发放月份不能为空");
		}
		//权限检查
		WxTUser curUser = ContextUtil.getCurUser();

		if(params.getGrantYear() == 2021){
			if(params.getGrantMonth() == 4){
				//如果是2021年4月，检查当月是否发放
				V2DsrScGrantStatusExample example = new V2DsrScGrantStatusExample();
				V2DsrScGrantStatusExample.Criteria criteria = example.createCriteria();
				criteria.andGrantYearEqualTo(params.getGrantYear());
				criteria.andGrantMonthEqualTo(params.getGrantMonth());
				List<Integer> statusList = new ArrayList<Integer>();
				statusList.add(0);
				statusList.add(1);
				criteria.andGrantStatusIn(statusList);
				criteria.andDeleteFlagEqualTo(0);
				List<V2DsrScGrantStatus> list = v2DsrScGrantStatusMapper.selectByExample(example);
				if(CollectionUtils.isEmpty(list)){
					data.put("isShow", true);
					data.put("isClick", false);
				}else {
					data.put("isShow", true);
					data.put("isClick", true);
				}
			}else if(params.getGrantMonth() > 4){
				//如果大于2021年4月，先检查上月月是否发放，在检查当月是否发放
				V2DsrScGrantStatusExample example = new V2DsrScGrantStatusExample();
				V2DsrScGrantStatusExample.Criteria criteria = example.createCriteria();
				criteria.andGrantYearEqualTo(params.getGrantYear());
				criteria.andGrantMonthEqualTo(params.getGrantMonth()-1);
				criteria.andGrantStatusEqualTo(1);
				criteria.andDeleteFlagEqualTo(0);
				List<V2DsrScGrantStatus> list = v2DsrScGrantStatusMapper.selectByExample(example);
				if(!CollectionUtils.isEmpty(list)){
					//检查当月是否发放
					V2DsrScGrantStatusExample example2 = new V2DsrScGrantStatusExample();
					V2DsrScGrantStatusExample.Criteria criteria2 = example2.createCriteria();
					criteria2.andGrantYearEqualTo(params.getGrantYear());
					criteria2.andGrantMonthEqualTo(params.getGrantMonth());
					List<Integer> statusList = new ArrayList<Integer>();
					statusList.add(0);
					statusList.add(1);
					criteria2.andGrantStatusIn(statusList);
					criteria2.andDeleteFlagEqualTo(0);
					List<V2DsrScGrantStatus> list2 = v2DsrScGrantStatusMapper.selectByExample(example2);
					if(CollectionUtils.isEmpty(list2)){
						data.put("isShow", true);
						data.put("isClick", false);
					}else {
						data.put("isShow", true);
						data.put("isClick", true);
					}
				}else {
					data.put("isShow", false);
					data.put("isClick", false);
				}
			}else{
				data.put("isShow", false);
				data.put("isClick", false);
			}
		}else if(params.getGrantYear() > 2021){
			//先检查上月月是否发放，在检查当月是否发放
			V2DsrScGrantStatusExample example = new V2DsrScGrantStatusExample();
			V2DsrScGrantStatusExample.Criteria criteria = example.createCriteria();
			if(params.getGrantMonth() == 1){
				criteria.andGrantYearEqualTo(params.getGrantYear()-1);
				criteria.andGrantMonthEqualTo(12);
			}else {
				criteria.andGrantYearEqualTo(params.getGrantYear());
				criteria.andGrantMonthEqualTo(params.getGrantMonth()-1);
			}
			criteria.andGrantStatusEqualTo(1);
			criteria.andDeleteFlagEqualTo(0);
			List<V2DsrScGrantStatus> list = v2DsrScGrantStatusMapper.selectByExample(example);
			if(!CollectionUtils.isEmpty(list)){
				//检查当月是否发放
				V2DsrScGrantStatusExample example2 = new V2DsrScGrantStatusExample();
				V2DsrScGrantStatusExample.Criteria criteria2 = example2.createCriteria();
				criteria2.andGrantYearEqualTo(params.getGrantYear());
				criteria2.andGrantMonthEqualTo(params.getGrantMonth());
				List<Integer> statusList = new ArrayList<Integer>();
				statusList.add(0);
				statusList.add(1);
				criteria2.andGrantStatusIn(statusList);
				criteria2.andDeleteFlagEqualTo(0);
				List<V2DsrScGrantStatus> list2 = v2DsrScGrantStatusMapper.selectByExample(example2);
				if(CollectionUtils.isEmpty(list2)){
					data.put("isShow", true);
					data.put("isClick", false);
				}else {
					data.put("isShow", true);
					data.put("isClick", true);
				}
			}else {
				data.put("isShow", false);
				data.put("isClick", false);
			}
		}else {
			data.put("isShow", false);
			data.put("isClick", false);
		}

		//获取当前用户的查看权限
		int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "Dsr.kpi");
		if(permissionWeight != -1 && (permissionWeight & 512) == 0) {
			data.remove("isShow");
			data.put("isShow", false);
		}

		resultMap.setDataResult(data);
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void grantDsrServiceFee(V2DsrScGrantStatusParams params) throws WxPltException {
		WxTUser curUser = ContextUtil.getCurUser();
		if(params == null){
			throw new WxPltException("参数不能为空");
		}
		if(params.getGrantYear() == null){
			throw new WxPltException("服务费发放年份不能为空");
		}
		if(params.getGrantMonth() == null){
			throw new WxPltException("服务费发放月份不能为空");
		}
		//判断是否可以发放
		ResponseMap responseMap = queryForStatus(params);
		Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
		Boolean ifGrantStatus = (Boolean) data.get("isShow");
		if(ifGrantStatus){
			//初始化发放状态
			V2DsrScGrantStatusExample example = new V2DsrScGrantStatusExample();
			V2DsrScGrantStatusExample.Criteria criteria = example.createCriteria();
			criteria.andGrantYearEqualTo(params.getGrantYear());
			criteria.andGrantMonthEqualTo(params.getGrantYear());
			criteria.andGrantStatusEqualTo(2);
			List<V2DsrScGrantStatus> grantStatuses = v2DsrScGrantStatusMapper.selectByExample(example);
			V2DsrScGrantStatus record = new V2DsrScGrantStatus();
			if(CollectionUtils.isEmpty(grantStatuses)){
				record.setGrantMonth(params.getGrantMonth());
				record.setGrantYear(params.getGrantYear());
				record.setCreateUserId(curUser.getUserId());
				record.setCreateTime(DateUtil.getCurrentDate());
				record.setUpdateUserId(curUser.getUserId());
				record.setUpdateTime(DateUtil.getCurrentDate());
				record.setGrantStatus(0);
				record.setDeleteFlag(0);
				v2DsrScGrantStatusMapper.insertSelective(record);
			}else {
				V2DsrScGrantStatus v2DsrScGrantStatus = grantStatuses.get(0);
				record.setId(v2DsrScGrantStatus.getId());
				record.setUpdateUserId(curUser.getUserId());
				record.setUpdateTime(DateUtil.getCurrentDate());
				record.setGrantStatus(0);
				v2DsrScGrantStatusMapper.updateByPrimaryKeySelective(record);
			}

			//查询发放服务费详细
			DsrKpiServiceFeePaymentParam dsrKpiServiceFeePaymentParam = new DsrKpiServiceFeePaymentParam();
			dsrKpiServiceFeePaymentParam.setYear(params.getGrantYear());
			dsrKpiServiceFeePaymentParam.setMonth(params.getGrantMonth());
			dsrKpiServiceFeePaymentParam.setPermissionWeight(1);
			List<DsrKpiServiceFeePaymentVo> feePaymentVos = biProcedureBizService.queryDsrKpiServiceFeePaymentShow(
					dsrKpiServiceFeePaymentParam,
					DsrKpiServiceFeePaymentField.REGION,
					DsrKpiServiceFeePaymentField.PARTNER_ID,
					DsrKpiServiceFeePaymentField.DSR_ID,
					DsrKpiServiceFeePaymentField.NEW_CUSTOMER_ACTUAL_YTD,
					DsrKpiServiceFeePaymentField.VISIT_DAYS_ACTUAL_YTD,
					DsrKpiServiceFeePaymentField.SALES_VOLUME_ACTUAL_YTD,
					DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_ACTUAL_YTD,
					DsrKpiServiceFeePaymentField.MULTIPLE_STANDARD_REACHING_RATE_YTD,
					DsrKpiServiceFeePaymentField.NEW_CUSTOMER_ACTUAL_MTD,
					DsrKpiServiceFeePaymentField.NEW_CUSTOMER_TARGET_MTD,
					DsrKpiServiceFeePaymentField.NEW_CUSTOMER_KPI_MTD,
					DsrKpiServiceFeePaymentField.VISIT_DAYS_ACTUAL_MTD,
					DsrKpiServiceFeePaymentField.VISIT_DAYS_TARGET_MTD,
					DsrKpiServiceFeePaymentField.VISIT_DAYS_KPI_MTD,
					DsrKpiServiceFeePaymentField.SALES_VOLUME_ACTUAL_MTD,
					DsrKpiServiceFeePaymentField.SALES_VOLUME_TARGET_MTD,
					DsrKpiServiceFeePaymentField.SALES_VOLUME_KPI_MTD,
					DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_ACTUAL_MTD,
					DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_TARGET_MTD,
					DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_KPI_MTD,
					DsrKpiServiceFeePaymentField.MULTIPLE_STANDARD_REACHING_RATE_MTD,
					DsrKpiServiceFeePaymentField.POINT_PAID_TARGET_MTD,
					DsrKpiServiceFeePaymentField.POINT_PAID_ACTUAL_MTD
			);
			//数据组装
			if(!CollectionUtils.isEmpty(feePaymentVos)){
				List<V2DsrScGrantHistory> insertList = new ArrayList<V2DsrScGrantHistory>();
				for (DsrKpiServiceFeePaymentVo feePaymentVo : feePaymentVos) {
					V2DsrScGrantHistory dsrScGrantHistory = new V2DsrScGrantHistory();
					BeanUtils.copyProperties(feePaymentVo,dsrScGrantHistory);
					dsrScGrantHistory.setGrantYear(params.getGrantYear());
					dsrScGrantHistory.setGrantMonth(params.getGrantMonth());
					dsrScGrantHistory.setCreateUserId(curUser.getUserId());
					dsrScGrantHistory.setCreateTime(DateUtil.getCurrentDate());
					dsrScGrantHistory.setUpdateUserId(curUser.getUserId());
					dsrScGrantHistory.setUpdateTime(DateUtil.getCurrentDate());
					dsrScGrantHistory.setDeleteFlag(0);
					dsrScGrantHistory.setPartnerName(feePaymentVo.getCustomerNameCn());
					insertList.add(dsrScGrantHistory);
				}
				//批量插入
				try {
					if(!CollectionUtils.isEmpty(insertList)){
						BatchOperationUtil<V2DsrScGrantHistory> insertListTemp = new BatchOperationUtil<V2DsrScGrantHistory>(insertList, 50);
						while(insertListTemp.hasMoreElements()) {
							v2DsrScGrantHistoryMapper.insertBatch(insertListTemp.nextElement());
						}
						//DSR积分分配
						pointBizService.sendDsrPointAsync(DateUtil.getStartTimeOfMonth(params.getGrantYear(),params.getGrantMonth()),insertList, curUser);
						//更新发放状态
						record.setUpdateUserId(curUser.getUserId());
						record.setUpdateTime(DateUtil.getCurrentDate());
						record.setGrantStatus(1);
						v2DsrScGrantStatusMapper.updateByPrimaryKeySelective(record);
					}
				}catch (Exception e){
					//更新发放状态
					record.setUpdateUserId(curUser.getUserId());
					record.setUpdateTime(DateUtil.getCurrentDate());
					record.setGrantStatus(2);
					v2DsrScGrantStatusMapper.updateByPrimaryKeySelective(record);
					throw new WxPltException("服务费发放异常，系统错误");
				}
			}
		}

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void adjustDsrServiceFee(V2DsrScAdjustInfo record) throws WxPltException {
		if(record == null){
			throw new WxPltException("参数不能为空");
		}
		if(record.getAdjustYear() == null){
			throw new WxPltException("服务费调整年份不能为空");
		}
		if(record.getAdjustMonth() == null){
			throw new WxPltException("服务费调整月份不能为空");
		}
		if(record.getPartnerId() == null){
			throw new WxPltException("PartnerId不能为空");
		}
		if(record.getDsrId() == null){
			throw new WxPltException("DSR不能为空");
		}
		if(record.getOldValue() == null){
			throw new WxPltException("服务费调整老目标值不能为空");
		}
		if(record.getTargetValue() == null){
			throw new WxPltException("服务费调整目标值不能为空");
		}
		WxTUser curUser = ContextUtil.getCurUser();
		//
		V2DsrScAdjustInfoExample example = new V2DsrScAdjustInfoExample();
		V2DsrScAdjustInfoExample.Criteria criteria = example.createCriteria();
		criteria.andPartnerIdEqualTo(record.getPartnerId());
		criteria.andDsrIdEqualTo(record.getDsrId());
		criteria.andAdjustMonthEqualTo(record.getAdjustMonth());
		criteria.andAdjustYearEqualTo(record.getAdjustYear());
		criteria.andDeleteFlagEqualTo(0);
		List<V2DsrScAdjustInfo> list = v2DsrScAdjustInfoMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(list)){
			record.setCreateUserId(curUser.getUserId());
			record.setCreateTime(DateUtil.getCurrentDate());
			record.setUpdateUserId(curUser.getUserId());
			record.setUpdateTime(DateUtil.getCurrentDate());
			record.setDeleteFlag(0);
			v2DsrScAdjustInfoMapper.insertSelective(record);
		}else {
			V2DsrScAdjustInfo v2DsrScAdjustInfo = list.get(0);
			v2DsrScAdjustInfo.setTargetValue(record.getTargetValue());
			v2DsrScAdjustInfo.setUpdateUserId(curUser.getUserId());
			v2DsrScAdjustInfo.setUpdateTime(DateUtil.getCurrentDate());
			v2DsrScAdjustInfoMapper.updateByPrimaryKeySelective(v2DsrScAdjustInfo);
		}
		//插入历史记录
		V2DsrScAdjustHistory history = new V2DsrScAdjustHistory();
		history.setRegion(record.getRegion());
		history.setAdjustYear(record.getAdjustYear());
		history.setAdjustMonth(record.getAdjustMonth());
		history.setDsrId(record.getDsrId());
		history.setPartnerId(record.getPartnerId());
		history.setOldValue(record.getOldValue());
		history.setNewValue(record.getTargetValue());
		history.setCreateUserId(curUser.getUserId());
		history.setCreateTime(DateUtil.getCurrentDate());
		history.setUpdateUserId(curUser.getUserId());
		history.setUpdateTime(DateUtil.getCurrentDate());
		history.setDeleteFlag(0);
		v2DsrScAdjustHistoryMapper.insertSelective(history);
	}

	@Override
	public void adjustHistoryList(V2DsrScAdjustHistoryParams params, ResponseMap resultMap) throws WxPltException {
		if(params == null){
			throw new WxPltException("参数不能为空");
		}
		if(params.getAdjustYear() == null){
			throw new WxPltException("服务费调整年份不能为空");
		}
		if(params.getAdjustMonth() == null){
			throw new WxPltException("服务费调整月份不能为空");
		}
		if(params.getPartnerId() == null){
			throw new WxPltException("PartnerId不能为空");
		}
		if(params.getDsrId() == null){
			throw new WxPltException("DSR不能为空");
		}
		resultMap.put(Constants.RESULT_LST_KEY, v2DsrScAdjustHistoryMapper.queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public void queryKpiList(DsrKpiServiceFeePaymentParam params, ResponseMap resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryKpiList(params));
	}

	@Override
	public List<DsrKpiServiceFeePaymentVo> queryKpiList(DsrKpiServiceFeePaymentParam params) throws WxPltException {
		if (params == null) {
			throw new WxPltException("参数不能为空");
		}
		if (params.getYear() == null) {
			throw new WxPltException("服务费调整年份不能为空");
		}
		if (params.getMonth() == null) {
			throw new WxPltException("服务费调整月份不能为空");
		}
		//权限检查
		WxTUser curUser = ContextUtil.getCurUser();
		//查询发放服务费详细
		DsrKpiServiceFeePaymentParam dsrKpiServiceFeePaymentParam = new DsrKpiServiceFeePaymentParam();
		//获取当前用户的查看权限
		int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "Dsr.kpi");
		if(permissionWeight == -1 || (permissionWeight & 1) > 0) {
			//chevron admin & mkt & ABM看全部
			dsrKpiServiceFeePaymentParam.setPermissionWeight(1);
		}else if((permissionWeight & 2) > 0) {
			//ASM
			dsrKpiServiceFeePaymentParam.setPermissionWeight(2);
			dsrKpiServiceFeePaymentParam.setSalesCai(curUser.getCai());
		}else if((permissionWeight & 4) > 0){
			//Team leader
			dsrKpiServiceFeePaymentParam.setPermissionWeight(4);
			dsrKpiServiceFeePaymentParam.setSalesCai(curUser.getCai());
		}else if((permissionWeight & 8) > 0){
			//FLSR
			dsrKpiServiceFeePaymentParam.setPermissionWeight(8);
			dsrKpiServiceFeePaymentParam.setSalesCai(curUser.getCai());
		}else if((permissionWeight & 16) > 0){
			//经销商
			dsrKpiServiceFeePaymentParam.setPermissionWeight(1);
			Long distributorId = partnerService.getDistributorIdByPartnerId(curUser.getOrgId());
			dsrKpiServiceFeePaymentParam.setDistributorId(distributorId);
		}
		dsrKpiServiceFeePaymentParam.setYear(params.getYear());
		dsrKpiServiceFeePaymentParam.setMonth(params.getMonth());
		if (StringUtils.isNotBlank(params.getRegion())) {
			dsrKpiServiceFeePaymentParam.setRegion(params.getRegion());
		}
		if (params.getDistributorId() != null) {
			dsrKpiServiceFeePaymentParam.setDistributorId(params.getDistributorId());
		}
		if (params.getDsrId() != null) {
			dsrKpiServiceFeePaymentParam.setDsrId(params.getDsrId());
		}
		List<DsrKpiServiceFeePaymentVo> list = biProcedureBizService.queryDsrKpiServiceFeePaymentShow(
				dsrKpiServiceFeePaymentParam,
				DsrKpiServiceFeePaymentField.REGION,
				DsrKpiServiceFeePaymentField.PARTNER_ID,
				DsrKpiServiceFeePaymentField.CUSTOMER_NAME_CN,
				DsrKpiServiceFeePaymentField.DSR_ID,
				DsrKpiServiceFeePaymentField.DSR_NAME,
				DsrKpiServiceFeePaymentField.NEW_CUSTOMER_ACTUAL_YTD,
				DsrKpiServiceFeePaymentField.VISIT_DAYS_ACTUAL_YTD,
				DsrKpiServiceFeePaymentField.SALES_VOLUME_ACTUAL_YTD,
				DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_ACTUAL_YTD,
				DsrKpiServiceFeePaymentField.MULTIPLE_STANDARD_REACHING_RATE_YTD,
				DsrKpiServiceFeePaymentField.NEW_CUSTOMER_ACTUAL_MTD,
				DsrKpiServiceFeePaymentField.NEW_CUSTOMER_TARGET_MTD,
				DsrKpiServiceFeePaymentField.NEW_CUSTOMER_KPI_MTD,
				DsrKpiServiceFeePaymentField.VISIT_DAYS_ACTUAL_MTD,
				DsrKpiServiceFeePaymentField.VISIT_DAYS_TARGET_MTD,
				DsrKpiServiceFeePaymentField.VISIT_DAYS_KPI_MTD,
				DsrKpiServiceFeePaymentField.SALES_VOLUME_ACTUAL_MTD,
				DsrKpiServiceFeePaymentField.SALES_VOLUME_TARGET_MTD,
				DsrKpiServiceFeePaymentField.SALES_VOLUME_KPI_MTD,
				DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_ACTUAL_MTD,
				DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_TARGET_MTD,
				DsrKpiServiceFeePaymentField.HERO_PRODUCT_SALES_VOLUME_KPI_MTD,
				DsrKpiServiceFeePaymentField.MULTIPLE_STANDARD_REACHING_RATE_MTD,
				DsrKpiServiceFeePaymentField.POINT_PAID_TARGET_MTD,
				DsrKpiServiceFeePaymentField.POINT_PAID_ACTUAL_MTD
		);

		//判断是否可以发放
		V2DsrScGrantStatusParams statusParams = new V2DsrScGrantStatusParams();
		statusParams.setGrantYear(params.getYear());
		statusParams.setGrantMonth(params.getMonth());
		ResponseMap responseMap = queryForStatus(statusParams);
		Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
		Boolean isGrant = (Boolean) data.get("isClick");
		Boolean isCanEdit = false;
		if(!isGrant){
			isCanEdit = true;
		}
		//计算合计
		Map<Long, DsrKpiServiceFeePaymentVo> map = new HashMap<Long, DsrKpiServiceFeePaymentVo>();
		if (!CollectionUtils.isEmpty(list)) {
			for (DsrKpiServiceFeePaymentVo feePaymentVo : list) {
				if(permissionWeight == -1 || (permissionWeight & 1536) > 0) {
					feePaymentVo.setIfCanEdit(isCanEdit);
					feePaymentVo.setIfShowHistory(true);
				}else {
					feePaymentVo.setIfCanEdit(false);
					feePaymentVo.setIfShowHistory(false);
					if(isGrant){
						feePaymentVo.setPointPaidActualMtd(feePaymentVo.getPointPaidActualMtd());
					}else {
						feePaymentVo.setPointPaidActualMtd(null);
					}
				}
				feePaymentVo.setIfTotal(false);
				feePaymentVo.setGrantStatus(isGrant);
				if (!map.containsKey(feePaymentVo.getPartnerId())) {
					DsrKpiServiceFeePaymentVo totalFeePaymentVo = new DsrKpiServiceFeePaymentVo();
					totalFeePaymentVo.setPartnerId(feePaymentVo.getPartnerId());
					totalFeePaymentVo.setRegion(feePaymentVo.getRegion());
					totalFeePaymentVo.setCustomerNameCn(feePaymentVo.getCustomerNameCn() + "(合计)");
					totalFeePaymentVo.setNewCustomerActualYtd(feePaymentVo.getNewCustomerActualYtd());
					totalFeePaymentVo.setVisitDaysActualYtd(feePaymentVo.getVisitDaysActualYtd());
					totalFeePaymentVo.setSalesVolumeActualYtd(feePaymentVo.getSalesVolumeActualYtd());
					totalFeePaymentVo.setHeroProductSalesVolumeActualYtd(feePaymentVo.getHeroProductSalesVolumeActualYtd());
					totalFeePaymentVo.setNewCustomerActualMtd(feePaymentVo.getNewCustomerActualMtd());
					totalFeePaymentVo.setNewCustomerTargetMtd(feePaymentVo.getNewCustomerTargetMtd());
					totalFeePaymentVo.setNewCustomerKpiMtd(feePaymentVo.getNewCustomerKpiMtd());
					totalFeePaymentVo.setVisitDaysActualMtd(feePaymentVo.getVisitDaysActualMtd());
					totalFeePaymentVo.setVisitDaysTargetMtd(feePaymentVo.getVisitDaysTargetMtd());
					totalFeePaymentVo.setVisitDaysKpiMtd(feePaymentVo.getVisitDaysKpiMtd());
					totalFeePaymentVo.setSalesVolumeActualMtd(feePaymentVo.getSalesVolumeActualMtd());
					totalFeePaymentVo.setSalesVolumeTargetMtd(feePaymentVo.getSalesVolumeTargetMtd());
					totalFeePaymentVo.setSalesVolumeKpiMtd(feePaymentVo.getSalesVolumeKpiMtd());
					totalFeePaymentVo.setHeroProductSalesVolumeActualMtd(feePaymentVo.getHeroProductSalesVolumeActualMtd());
					totalFeePaymentVo.setHeroProductSalesVolumeTargetMtd(feePaymentVo.getHeroProductSalesVolumeTargetMtd());
					totalFeePaymentVo.setHeroProductSalesVolumeKpiMtd(feePaymentVo.getHeroProductSalesVolumeKpiMtd());
					totalFeePaymentVo.setPointPaidTargetMtd(feePaymentVo.getPointPaidTargetMtd());
					if(permissionWeight == -1 || (permissionWeight & 1536) > 0) {
						totalFeePaymentVo.setPointPaidActualMtd(feePaymentVo.getPointPaidActualMtd());
					}else {
						if(isGrant){
							totalFeePaymentVo.setPointPaidActualMtd(feePaymentVo.getPointPaidActualMtd());
						}else {
							totalFeePaymentVo.setPointPaidActualMtd(null);
						}
					}
					totalFeePaymentVo.setMultipleStandardReachingRateMtd(feePaymentVo.getMultipleStandardReachingRateMtd());
					totalFeePaymentVo.setIfCanEdit(false);
					totalFeePaymentVo.setIfShowHistory(false);
					totalFeePaymentVo.setGrantStatus(isGrant);
					totalFeePaymentVo.setIfTotal(true);
					map.put(feePaymentVo.getPartnerId(), totalFeePaymentVo);
				} else {
					DsrKpiServiceFeePaymentVo totalFeePaymentVo = map.get(feePaymentVo.getPartnerId());
					totalFeePaymentVo.setNewCustomerActualYtd(MathUtil.add(totalFeePaymentVo.getNewCustomerActualYtd(), feePaymentVo.getNewCustomerActualYtd()));
					totalFeePaymentVo.setVisitDaysActualYtd(MathUtil.add(totalFeePaymentVo.getVisitDaysActualYtd(), feePaymentVo.getVisitDaysActualYtd()));
					totalFeePaymentVo.setSalesVolumeActualYtd(MathUtil.add(totalFeePaymentVo.getSalesVolumeActualYtd(), feePaymentVo.getSalesVolumeActualYtd()));
					totalFeePaymentVo.setHeroProductSalesVolumeActualYtd(MathUtil.add(totalFeePaymentVo.getHeroProductSalesVolumeActualYtd(), feePaymentVo.getHeroProductSalesVolumeActualYtd()));
					totalFeePaymentVo.setNewCustomerActualMtd(MathUtil.add(totalFeePaymentVo.getNewCustomerActualMtd(), feePaymentVo.getNewCustomerActualMtd()));
					totalFeePaymentVo.setNewCustomerTargetMtd(MathUtil.add(totalFeePaymentVo.getNewCustomerTargetMtd(), feePaymentVo.getNewCustomerTargetMtd()));
					if(totalFeePaymentVo.getNewCustomerTargetMtd() > 0){
						double newCustomerKpiMtd = MathUtil.division(totalFeePaymentVo.getNewCustomerActualMtd(), totalFeePaymentVo.getNewCustomerTargetMtd());
						if(newCustomerKpiMtd > 1){
							newCustomerKpiMtd = 1.0;
						}
						totalFeePaymentVo.setNewCustomerKpiMtd(newCustomerKpiMtd);
					}else {
						totalFeePaymentVo.setNewCustomerKpiMtd(0.0);
					}
					totalFeePaymentVo.setVisitDaysActualMtd(MathUtil.add(totalFeePaymentVo.getVisitDaysActualMtd(), feePaymentVo.getVisitDaysActualMtd()));
					totalFeePaymentVo.setVisitDaysTargetMtd(MathUtil.add(totalFeePaymentVo.getVisitDaysTargetMtd(), feePaymentVo.getVisitDaysTargetMtd()));
					if(totalFeePaymentVo.getVisitDaysTargetMtd() > 0){
						double visitDaysKpiMtd = MathUtil.division(totalFeePaymentVo.getVisitDaysActualMtd(), totalFeePaymentVo.getVisitDaysTargetMtd());
						if(visitDaysKpiMtd > 1){
							visitDaysKpiMtd = 1.0;
						}
						totalFeePaymentVo.setVisitDaysKpiMtd(visitDaysKpiMtd);
					}else {
						totalFeePaymentVo.setVisitDaysKpiMtd(0.0);
					}
					totalFeePaymentVo.setSalesVolumeActualMtd(MathUtil.add(totalFeePaymentVo.getSalesVolumeActualMtd(), feePaymentVo.getSalesVolumeActualMtd()));
					totalFeePaymentVo.setSalesVolumeTargetMtd(MathUtil.add(totalFeePaymentVo.getSalesVolumeTargetMtd(), feePaymentVo.getSalesVolumeTargetMtd()));
					if(totalFeePaymentVo.getSalesVolumeTargetMtd() > 0){
						double salesVolumeKpiMtd = MathUtil.division(totalFeePaymentVo.getSalesVolumeActualMtd(), totalFeePaymentVo.getSalesVolumeTargetMtd());
						if(salesVolumeKpiMtd > 1){
							salesVolumeKpiMtd = 1.0;
						}
						totalFeePaymentVo.setSalesVolumeKpiMtd(salesVolumeKpiMtd);
					}else {
						totalFeePaymentVo.setSalesVolumeKpiMtd(0.0);
					}
					totalFeePaymentVo.setHeroProductSalesVolumeActualMtd(MathUtil.add(totalFeePaymentVo.getHeroProductSalesVolumeActualMtd(), feePaymentVo.getHeroProductSalesVolumeActualMtd()));
					totalFeePaymentVo.setHeroProductSalesVolumeTargetMtd(MathUtil.add(totalFeePaymentVo.getHeroProductSalesVolumeTargetMtd(), feePaymentVo.getHeroProductSalesVolumeTargetMtd()));
					if(totalFeePaymentVo.getHeroProductSalesVolumeTargetMtd() > 0){
						double heroProductSalesVolumeKpiMtd = MathUtil.division(totalFeePaymentVo.getHeroProductSalesVolumeActualMtd(), totalFeePaymentVo.getHeroProductSalesVolumeTargetMtd());
						if(heroProductSalesVolumeKpiMtd > 1){
							heroProductSalesVolumeKpiMtd = 1.0;
						}
						totalFeePaymentVo.setHeroProductSalesVolumeKpiMtd(heroProductSalesVolumeKpiMtd);
					}else {
						totalFeePaymentVo.setHeroProductSalesVolumeKpiMtd(0.0);
					}
					totalFeePaymentVo.setPointPaidTargetMtd(MathUtil.add(totalFeePaymentVo.getPointPaidTargetMtd(),feePaymentVo.getPointPaidTargetMtd()));
					if(permissionWeight == -1 || (permissionWeight & 1536) > 0) {
						totalFeePaymentVo.setPointPaidActualMtd(MathUtil.add(totalFeePaymentVo.getPointPaidActualMtd(),feePaymentVo.getPointPaidActualMtd()));
					}else {
						if(isGrant){
							totalFeePaymentVo.setPointPaidActualMtd(MathUtil.add(totalFeePaymentVo.getPointPaidActualMtd(),feePaymentVo.getPointPaidActualMtd()));
						}else {
							totalFeePaymentVo.setPointPaidActualMtd(null);
						}
					}
					Double multipleStandardReachingRateMtd = 0.0;
					if("CC".equals(totalFeePaymentVo.getRegion())){
						multipleStandardReachingRateMtd = totalFeePaymentVo.getNewCustomerKpiMtd()*0.2
								+ totalFeePaymentVo.getVisitDaysKpiMtd()*0.2
								+ totalFeePaymentVo.getSalesVolumeKpiMtd()*0.3
								+ totalFeePaymentVo.getHeroProductSalesVolumeKpiMtd()*0.3;
					}else if("NC".equals(totalFeePaymentVo.getRegion())){
						multipleStandardReachingRateMtd = totalFeePaymentVo.getNewCustomerKpiMtd()*0.2
								+ totalFeePaymentVo.getVisitDaysKpiMtd()*0.2
								+ totalFeePaymentVo.getSalesVolumeKpiMtd()*0.3
								+ totalFeePaymentVo.getHeroProductSalesVolumeKpiMtd()*0.3;
					}else if("NE".equals(totalFeePaymentVo.getRegion())){
						multipleStandardReachingRateMtd = totalFeePaymentVo.getNewCustomerKpiMtd()*0.35
								+ totalFeePaymentVo.getVisitDaysKpiMtd()*0.35
								+ totalFeePaymentVo.getSalesVolumeKpiMtd()*0.2
								+ totalFeePaymentVo.getHeroProductSalesVolumeKpiMtd()*0.1;
					}else if("SC".equals(totalFeePaymentVo.getRegion())){
						multipleStandardReachingRateMtd = totalFeePaymentVo.getNewCustomerKpiMtd()*0.2
								+ totalFeePaymentVo.getVisitDaysKpiMtd()*0.2
								+ totalFeePaymentVo.getSalesVolumeKpiMtd()*0.3
								+ totalFeePaymentVo.getHeroProductSalesVolumeKpiMtd()*0.3;
//					}else if("NW & SW".equals(totalFeePaymentVo.getRegion())){
					}else if("SW".equals(totalFeePaymentVo.getRegion())){
						multipleStandardReachingRateMtd = totalFeePaymentVo.getNewCustomerKpiMtd()*0.3
								+ totalFeePaymentVo.getVisitDaysKpiMtd()*0.1
								+ totalFeePaymentVo.getSalesVolumeKpiMtd()*0.3
								+ totalFeePaymentVo.getHeroProductSalesVolumeKpiMtd()*0.3;
					}
					totalFeePaymentVo.setMultipleStandardReachingRateMtd(multipleStandardReachingRateMtd);
					totalFeePaymentVo.setIfCanEdit(false);
					totalFeePaymentVo.setIfShowHistory(false);
					totalFeePaymentVo.setGrantStatus(isGrant);
					totalFeePaymentVo.setIfTotal(true);
				}
			}
		}
		//将合计放到结果里
		if (!CollectionUtils.isEmpty(map)) {
			for (Long partnerId: map.keySet()) {
				list.add(map.get(partnerId));
			}
		}
		String [] sortNameArr = {"customerNameCn"};
		boolean [] isAscArr = {true};
		SortUtils.sort(list,sortNameArr,isAscArr);

		return list;
	}
}
