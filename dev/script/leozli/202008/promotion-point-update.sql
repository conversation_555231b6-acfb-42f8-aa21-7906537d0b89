insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('PromotionPoint.pointType', '促销积分积分类型配置', '促销积分积分类型配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'cdm_promotion', '乘用车促销积分', 'Consumer', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'cdm_red_bag', '乘用车红包', 'Consumer', '1', 20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'cdm_pit_pack', 'PitPack积分', 'Consumer', '1', 30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'promotion', '商用油促销积分', 'Commercial', '1', 40);

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'cdm_material', '乘用车物料积分', 'Consumer', '1', 50);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'cdm_stock', '乘用车进货积分', 'Consumer', '1', 60);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'cdm_store_open', '乘用车新店开业礼包', 'Consumer', '1', 70);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'caltex', '商用油进货积分', 'Commercial', '1', 80);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'dsr_cio', '德乐DSR积分', 'Commercial', '0', 40);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PromotionPoint.pointType', 'b2b', 'B2B', 'Consumer', '0', 40);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('BI.productChannel', 'BI产品渠道配置', 'BI产品渠道配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('BI.productChannel', 'Consumer', 'Consumer', '乘用车', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('BI.productChannel', 'Commercial', 'Commercial', '商用油', '1', 10);

INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('B2B'           ,'B2B_POINT'            ,'b2b'            ,'Commercial'           ,1            ,0            , 0           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('德乐DSR积分'           ,'DSR_CIO_POINT'            ,'dsr_cio'            ,'Commercial'           ,1            ,0            , 0           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('商用油进货积分'           ,'CALTEX_POINT'            ,'caltex'            ,'Commercial'           ,1            ,0            , 0           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('乘用车新店开业礼包'           ,'CDM_STORE_OPEN_POINT'            ,'cdm_store_open'            ,'Consumer'           ,1            ,0            , 0           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('乘用车进货积分'           ,'CDM_STOCK_POINT'            ,'cdm_stock'            ,'Consumer'           ,1            ,0            , 0           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('乘用车物料积分'           ,'CDM_MATERIAL_POINT'            ,'cdm_material'            ,'Consumer'           ,1            ,0            , 0           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('商用油促销积分'           ,'PROMOTION_POINT'            ,'promotion'            ,'Commercial'           ,1            ,0            , 1           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('PitPack积分'           ,'CDM_PIT_PACK'            ,'cdm_pit_pack'            ,'Consumer'           ,1            ,0            , 1           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('乘用车红包'           ,'CDM_RED_BAG_POINT'            ,'cdm_red_bag'            ,'Consumer'           ,1            ,0            , 1           ,-1            ,0            ,1           ,getdate())
INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('乘用车促销积分'           ,'CDM_PROMOTION_POINT'            ,'cdm_promotion'            ,'Consumer'           ,1            ,0            , 1           ,-1            ,0            ,1           ,getdate())

--select pp.point_code, gp.* 
update gp set gp.point_code=pp.point_code
from wx_t_promotion_gift_pool gp
left join wx_t_promotion_point pp on gp.application_type=pp.point_type and pp.predefine_flag=1
--order by gp.id

--select case when ext_property8='Consumer' then 'CDM_PROMOTION_POINT' else 'PROMOTION_POINT' end,* 
update pdd set pdd.ext_property3=(case when ext_property8='Consumer' then 'CDM_PROMOTION_POINT' else 'PROMOTION_POINT' end)
from wx_t_promotion_delivery_detail pdd where award_type=1

--select * 
update m set m.menu_pid=50341
from wx_t_menu m where m.menu_id=50364

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('SellinPromotion.lastSynDate', '2020-10-15', '上一次促销设置同步日期配置');

select * into wx_t_delivery_order_rel20201015 from wx_t_delivery_order_rel
truncate table wx_t_delivery_order_rel
insert into wx_t_delivery_order_rel (delivery_detail_id, delivery_id)
select * from (
			select distinct pdd.id, pd.delivery_id from [wx_t_promotion_delivery_detail] pdd
			left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
			left join [PP_MID].[cmt].[campaign_offer_detail] cod on ods.offer_id=cod.offer_id and ods.distributor_id=cod.distributor_id
				and CONVERT(varchar(100), ods.create_time, 112)=CONVERT(varchar(100), cod.create_time, 112)
			left join wx_t_sell_in_promotion_delivery pd on pd.base_trans_sell_in_id=cod.base_trans_sell_in_id) a where delivery_id is not null
			  --id in (617, 618)
			  
insert into wx_t_delivery_order_rel (delivery_detail_id, delivery_id)
select * from (
			select distinct pdd.id, pd.delivery_id from [wx_t_promotion_delivery_detail] pdd
			left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
			left join [PP_MID].[cmt].[campaign_offer_detail] cod on ods.offer_id=cod.offer_id and ods.distributor_id=cod.distributor_id
				--and CONVERT(varchar(100), ods.create_time, 112)=CONVERT(varchar(100), cod.create_time, 112)
			left join wx_t_sell_in_promotion_delivery pd on pd.base_trans_sell_in_id=cod.base_trans_sell_in_id) a where --delivery_id is not null
			  id in (617, 618)