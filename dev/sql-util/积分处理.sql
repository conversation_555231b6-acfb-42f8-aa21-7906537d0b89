    insert into dbo.wx_t_point_pending_record (distributor_id,partner_id,partner_name,bi_sellin_line_id,pending_score,status,sales_channel_name,
      creation_time,
      created_by,
      last_update_time,
      last_updated_by,
      liters,
      product_sku,
      trans_time)
    SELECT
        distributor_id,
		partner_id,
		partner_name,
		bi_sellin_line_id,
		pending_score,
		status,
		sales_channel_name,
		creation_time,
		created_by,
		last_update_time,
		last_updated_by,
		liters,
		product_sku,
		trans_time
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            0 AS bi_sellin_line_id,
            sum( round( dw.abp_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.product_channel sales_channel_name,
            getdate() AS creation_time,
            1AS created_by,
            getdate() AS last_update_time,
            1 AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            dw.product_code_sap product_sku,
            dw.trans_time
          from PP_MID.dbo.syn_dw_to_pp_abp dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = en.partner_id
          WHERE
            1 = 1
            AND (dw.trans_time >= '2020-05-01' and trans_time < '2020-06-01')
            --AND dw.product_channel = #{salesChannelName}
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = en.distributor_id
                  AND re.product_sku = dw.product_code_sap
                  AND re.sales_channel_name = dw.sales_channel_name
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
            dw.product_code_sap,
            dw.product_channel,
            dw.trans_time
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0
		  ORDER BY
			total_pending_info.partner_name,
			total_pending_info.product_sku