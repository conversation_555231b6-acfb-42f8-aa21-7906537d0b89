package com.chevron.b2b.model;

import java.util.Date;
import java.util.List;

/**
 * 计划实例-wx_t_schedule_instance
 * <AUTHOR>
 * @version 1.0 2018-12-18 15:31
 */
public class ScheduleInstance {

	/**  ID  **/
    private Long id;

	/**  计划ID  **/
    private Long scheduleId;

	/**  经销商ID  **/
    private Long distributorId;

	/**  门店ID  **/
    private Long workshopId;

	/**  执行人类型(mechanic-技师)  **/
    private String executorType;

	/**  执行人键值  **/
    private String executorKey;

	/**  执行人姓名  **/
    private String executorName;

	/**  状态(0-新建，5-已参加，10-暂存，15-已提交，100-已完成)  **/
    private Integer status;

	/**  提交时间  **/
    private Date submitTime;

	/**  删除标记(1-删除，0-未删除)  **/
    private Integer deleteFlag;

	/**  创建用户ID  **/
    private Long createUserId;

	/**  创建时间  **/
    private Date createTime;

	/**  最后修改用户ID  **/
    private Long updateUserId;

	/**  最后修改时间  **/
    private Date updateTime;
    
    private String scheduleName;

	/**  图标图片ID  **/
    private Long iconId;

	/**  横幅图片ID  **/
    private Long bannerId;

	/**  活动类型(activity-活动，task-任务)  **/
    private String scheduleType;

	/**  奖励积分  **/
    private Double awardPoint;

	/**  开始时间  **/
    private Date startTime;

	/**  截止时间  **/
    private Date endTime;

	/**  描述  **/
    private String scheduleDesc;

	/**  发布时间  **/
    private Date publishTime;

	/**  备注(记录审批意见)  **/
    private String remark;
    
    private String statusText;
    
    private Integer leftSteps;
    
    private String scheduleTypeText;
    
    private String salesChannel;
    
    private String distributorName;
    
    private String workshopName;
    
    private Long mechanicId;

	/**  分享标记(1-分享,0-不分享)  **/
    private Integer shareFlag;

	/**  可变积分  **/
    private Integer fixedPointFlag;

	/**  最大积分(可变积分时有效)  **/
    private Double maxPoint;
    
    private Long agreeCount;
    
    private Integer clientAgreeFlag;

	/**  表单背景(分享活动有)  **/
    private Long formBg;

	/**  操作背景  **/
    private Long actionBg;
    
    /** 点赞数量 */
    private int agreeNumb;
    
    private List<ScheduleItemInstance> itemList;
    
    private List<ActivityAgree> activityAgrees;
    
    /** 新建状态 */
    public final static Integer STATUS_NEW = 0;
    
    /** 已参加状态 */
    public final static Integer STATUS_JOINED = 5;
    
    /** 未通过状态 */
    public final static Integer STATUS_REJECT = 7;
    
    /** 暂存状态 */
    public final static Integer STATUS_TEMP = 10;
    
    /** 已提交状态 */
    public final static Integer STATUS_SUBMIT = 15;
    
    /** 已完成状态 */
    public final static Integer STATUS_COMPLETED = 100;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Long getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(Long distributorId) {
        this.distributorId = distributorId;
    }

    public Long getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(Long workshopId) {
        this.workshopId = workshopId;
    }

    public String getExecutorType() {
        return executorType;
    }

    public void setExecutorType(String executorType) {
        this.executorType = executorType;
    }

    public String getExecutorKey() {
        return executorKey;
    }

    public void setExecutorKey(String executorKey) {
        this.executorKey = executorKey;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public String getScheduleName() {
		return scheduleName;
	}

	public void setScheduleName(String scheduleName) {
		this.scheduleName = scheduleName;
	}

	public Long getIconId() {
		return iconId;
	}

	public void setIconId(Long iconId) {
		this.iconId = iconId;
	}

	public Long getBannerId() {
		return bannerId;
	}

	public void setBannerId(Long bannerId) {
		this.bannerId = bannerId;
	}

	public String getScheduleType() {
		return scheduleType;
	}

	public void setScheduleType(String scheduleType) {
		this.scheduleType = scheduleType;
	}

	public Double getAwardPoint() {
		return awardPoint;
	}

	public void setAwardPoint(Double awardPoint) {
		this.awardPoint = awardPoint;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getScheduleDesc() {
		return scheduleDesc;
	}

	public void setScheduleDesc(String scheduleDesc) {
		this.scheduleDesc = scheduleDesc;
	}

	public Date getPublishTime() {
		return publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	public List<ScheduleItemInstance> getItemList() {
		return itemList;
	}

	public void setItemList(List<ScheduleItemInstance> itemList) {
		this.itemList = itemList;
	}

	public String getStatusText() {
		return statusText;
	}

	public void setStatusText(String statusText) {
		this.statusText = statusText;
	}

	public Integer getLeftSteps() {
		return leftSteps;
	}

	public void setLeftSteps(Integer leftSteps) {
		this.leftSteps = leftSteps;
	}

	public String getScheduleTypeText() {
		return scheduleTypeText;
	}

	public void setScheduleTypeText(String scheduleTypeText) {
		this.scheduleTypeText = scheduleTypeText;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getDistributorName() {
		return distributorName;
	}

	public void setDistributorName(String distributorName) {
		this.distributorName = distributorName;
	}

	public String getWorkshopName() {
		return workshopName;
	}

	public void setWorkshopName(String workshopName) {
		this.workshopName = workshopName;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<ActivityAgree> getActivityAgrees() {
		return activityAgrees;
	}

	public void setActivityAgrees(List<ActivityAgree> activityAgrees) {
		this.activityAgrees = activityAgrees;
	}

	public Long getMechanicId() {
		return mechanicId;
	}

	public void setMechanicId(Long mechanicId) {
		this.mechanicId = mechanicId;
	}

	public Integer getShareFlag() {
		return shareFlag;
	}

	public void setShareFlag(Integer shareFlag) {
		this.shareFlag = shareFlag;
	}

	public Integer getFixedPointFlag() {
		return fixedPointFlag;
	}

	public void setFixedPointFlag(Integer fixedPointFlag) {
		this.fixedPointFlag = fixedPointFlag;
	}

	public Double getMaxPoint() {
		return maxPoint;
	}

	public void setMaxPoint(Double maxPoint) {
		this.maxPoint = maxPoint;
	}

	public Long getAgreeCount() {
		return agreeCount;
	}

	public void setAgreeCount(Long agreeCount) {
		this.agreeCount = agreeCount;
	}

	public Integer getClientAgreeFlag() {
		return clientAgreeFlag;
	}

	public void setClientAgreeFlag(Integer clientAgreeFlag) {
		this.clientAgreeFlag = clientAgreeFlag;
	}

	public Long getFormBg() {
		return formBg;
	}

	public void setFormBg(Long formBg) {
		this.formBg = formBg;
	}

	public Long getActionBg() {
		return actionBg;
	}

	public void setActionBg(Long actionBg) {
		this.actionBg = actionBg;
	}

	public int getAgreeNumb() {
		return agreeNumb;
	}

	public void setAgreeNumb(int agreeNumb) {
		this.agreeNumb = agreeNumb;
	}
}
