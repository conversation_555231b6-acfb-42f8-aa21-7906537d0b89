/**
 * Created by Lecter on 16/8/27.
 */
(function($){
    $.getUrlParam = function(name)
    {
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r!=null) return unescape(r[2]); return null;
    }
})(jQuery);
$(document).ready(function () {
	var productName = $.getUrlParam('productName');
	while(productName.indexOf("+") > 0){
		productName = productName.replace("+","&nbsp;") ;
	}
	var capacity = $.getUrlParam('capacity');
	var firstTime = $.getUrlParam('firstTime');
	var validateCount = $.getUrlParam('validateCount');
	
	productName = decodeURIComponent(productName); 
	
	
	productName = productName;
	$("#capacity").html(capacity);
	$("#productName").html(productName);
	$("#validateTime").html("首次查询时间：" + firstTime + " / 查询次数：" + validateCount + "次");
});
String.prototype.replaceAll  = function(s1,s2){     
    return this.replace(new RegExp(s1,"gm"),s2);     
}  