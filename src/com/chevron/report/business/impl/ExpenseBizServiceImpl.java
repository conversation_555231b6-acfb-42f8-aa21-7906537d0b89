package com.chevron.report.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chevron.budget2021.dao.Budget2021ExpenseImportMapper;
import com.chevron.budget2021.model.Budget2021ExpenseImport;
import com.chevron.budget2021.model.Budget2021ExpenseImportExample;
import com.chevron.report.business.BiProcedureBizService;
import com.chevron.report.business.ExpenseBizService;
import com.chevron.report.model.*;
import com.chevron.report.util.DistributorExpenseField;
import com.chevron.report.util.ExpenseDetail;
import com.chevron.report.util.ExpenseItem;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.StringUtils;
import com.google.common.collect.Lists;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.DicItemVoExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Service
public class ExpenseBizServiceImpl implements ExpenseBizService {

	private static final Logger log = LoggerFactory.getLogger(ExpenseBizServiceImpl.class);

	@Resource
	private JdbcTemplate wxJdbcTemplate;

	@Autowired
	private BiProcedureBizService biProcedureBizService;

	@Autowired
	private DicItemVoMapper dicItemVoMapper;

	@Autowired
	private Budget2021ExpenseImportMapper budget2021ExpenseImportMapper;

	@Override
	public ExpenseDetail allocateExpense(ExpenseItem expenseItem, int year, Long distributorId, double totalAmount,
										 ExpenseDetail oldExpenseDetail) throws WxPltException {
		return expenseItem.allocateExpense(year, distributorId, totalAmount, oldExpenseDetail, this, biProcedureBizService);
	}

	@Override
	public ExpenseDetail allocateExpenseAfter2021(int year, Long distributorId, int brand, double totalAmount,
												  ExpenseDetail oldExpenseDetail) throws WxPltException {

		if(oldExpenseDetail != null && oldExpenseDetail.getOnlineAmount() > totalAmount - 0.000001) {
			//如果新费用未超过老费用，无需重新分配
			oldExpenseDetail.setOnlineAmount(totalAmount);
			return oldExpenseDetail;
		}

		ExpenseDetail detail = new ExpenseDetail();
		if(totalAmount == 0){
			return detail;
		}
		DistributorExpenseVo budget = queryBudgetAndExpenseByDistributor(distributorId,
				null, year, false, brand);

		if(oldExpenseDetail != null) {
			//排除修改前预算
			if(oldExpenseDetail.getSparkAmount() != null) {
				budget.setSparkActual(budget.getSparkActual() - oldExpenseDetail.getSparkAmount());
			}
			if(oldExpenseDetail.getOnlineAmount() != null) {
				budget.setFlsrActual(budget.getFlsrActual() - oldExpenseDetail.getOnlineAmount());
			}
		}

		if(budget.getFlsrBudget() == null) {
			throw new WxPltException("预算未分配");
		}
		if(budget.getFlsrBudget() > budget.getFlsrActual() + totalAmount - 0.0000001) {
			detail.setSparkAmount(0.0);
			detail.setOnlineAmount(totalAmount);
			return detail;
		}
		throw new WxPltException("基金池基金不足");
	}

	@Override
	public List<DistributorExpenseVo> queryBudget2021(Integer year) throws WxPltException {
		final List<DistributorExpenseVo> expenseVos = new ArrayList<DistributorExpenseVo>(300);
		//大区费用按销售计算
		String sql = new StringBuilder(10000).append("select v.region,v.sales_cai, v.sales_name_cn, v.supervisor_cai, v.supervisor_name_cn, b.region_budget_value region_budget, ")
				.append(" b2.mkt_budget_value mkt_budget,b2.item_budget_value item_budget, b2.region_budget_value region_total_budget, b2.brand")
				.append(" from (select distinct v1.region,v1.sales_cai, v1.sales_name_cn, v1.supervisor_cai, v1.supervisor_name_cn, v1.channel_weight from dw_customer_org_sales v1")
				.append(" inner join PP_MID.dbo.syn_dw_to_pp_customer ppc on ppc.distributor_id = v1.distributor_id and ppc.customer_status ='Active' WHERE v1.del_flag =0) v ")
				.append(" left join wx_t_budget2021 b2 on b2.budget_key=v.region and b2.budget_type=2 and v.channel_weight&b2.channel_weight>0")
				.append(" left join wx_t_budget2021 b on b.budget_key=v.sales_cai and b.budget_year=b2.budget_year and b.budget_type=3 and b.brand=b2.brand and v.channel_weight&b.channel_weight>0")
				.append(" where b2.budget_year=? order by b2.brand,v.region").toString();
		Object[] params = new Object[] {year};
		log.debug(StringUtils.printSql(sql, params));
		wxJdbcTemplate.query(sql, params, new RowCallbackHandler() {
			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				DistributorExpenseVo expenseVo = new DistributorExpenseVo();
				expenseVo.setSalesCai(arg0.getString("sales_cai"));
				expenseVo.setRegionName(arg0.getString("region"));
				expenseVo.setSalesName(arg0.getString("sales_name_cn"));
				expenseVo.setSupervisorCai(arg0.getString("supervisor_cai"));
				expenseVo.setSupervisorName(arg0.getString("supervisor_name_cn"));
				expenseVo.setBrand(arg0.getInt("brand"));
				if(arg0.getObject("region_total_budget") != null){
					expenseVo.setRegionAsmBudget(arg0.getDouble("region_total_budget"));
				}
				if(arg0.getObject("region_budget") != null){
					expenseVo.setRegionBudget(arg0.getDouble("region_budget"));
				}
				if(arg0.getObject("mkt_budget") != null){
					expenseVo.setMktBudget(arg0.getDouble("mkt_budget"));
				}
				if(arg0.getObject("item_budget") != null){
					expenseVo.setProjectBudget(arg0.getDouble("item_budget"));
				}
				expenseVos.add(expenseVo);
			}
		});
		return expenseVos;
	}
	
	@Override
	public DistributorExpenseVo queryBudgetAndExpense2021(int year, Long distributorId, int brand) throws WxPltException {
		final DistributorExpenseVo expenseVo = new DistributorExpenseVo();
		//大区费用按销售计算
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select top 1 v.region,v.sales_cai, v.sales_name_cn, v.supervisor_cai, v.supervisor_name_cn, ")
				.append(" b.region_budget_value region_budget,b2.mkt_budget_value mkt_budget,b2.item_budget_value item_budget from dw_customer_org_sales v ")
				.append(" left join wx_t_budget2021 b on b.budget_key=v.sales_cai and b.budget_year=? and b.budget_type=3 and b.brand=? and v.channel_weight&b.channel_weight>0")
				.append(" left join wx_t_budget2021 b2 on b2.budget_key=v.region and b2.budget_year=? and b2.budget_type=2 and b2.brand=? and v.channel_weight&b2.channel_weight>0")
				.append(" where v.distributor_id=? order by ((case when b.budget_key is null then 0 else 1 end)+(case when b2.budget_key is null then 0 else 1 end)) desc");
		params.add(year);
		params.add(brand);
		params.add(year);
		params.add(brand);
		params.add(distributorId);
		log.debug(sql.toString());
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {
			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				expenseVo.setSalesCai(arg0.getString("sales_cai"));
				expenseVo.setRegionName(arg0.getString("region"));
				expenseVo.setSalesName(arg0.getString("sales_name_cn"));
				expenseVo.setSupervisorCai(arg0.getString("supervisor_cai"));
				expenseVo.setSupervisorName(arg0.getString("supervisor_name_cn"));
				if(arg0.getObject("region_budget") != null){
					expenseVo.setRegionBudget(arg0.getDouble("region_budget"));
				}
				if(arg0.getObject("mkt_budget") != null){
					expenseVo.setMktBudget(arg0.getDouble("mkt_budget"));
				}
				if(arg0.getObject("item_budget") != null){
					expenseVo.setProjectBudget(arg0.getDouble("item_budget"));
				}
			}
		});

//		//市场费用、项目费用按区域计算
//		List<Object> params2 = new ArrayList<Object>();
//		StringBuilder sql2 = new StringBuilder(10000).append("select b.budget_key region_name,b.mkt_budget_value mkt_budget,b.item_budget_value item_budget")
//				.append(" from wx_t_budget2021 b")
//				.append(" where exists(select 1 from dw_customer_org_sales v where v.distributor_id=? and v.region=b.budget_key) and b.budget_year=? and b.budget_type=2 and b.brand=?");
//		params2.add(distributorId);
//		params2.add(year);
//		params2.add(brand);
//		log.debug(sql2.toString());
//		wxJdbcTemplate.query(sql2.toString(), params2.toArray(new Object[params2.size()]), new RowCallbackHandler() {
//			@Override
//			public void processRow(ResultSet arg0) throws SQLException {
//				if(arg0.getObject("mkt_budget") != null){
//					expenseVo.setMktBudget(arg0.getDouble("mkt_budget"));
//				}
//				if(arg0.getObject("item_budget") != null){
//					expenseVo.setProjectBudget(arg0.getDouble("item_budget"));
//				}
//			}
//		});

		//是否分配预算校验
		if(expenseVo.getRegionBudget()==null){
			throw new WxPltException("暂未分配预算，请先分配预算!");
		}
		DistributorExpenseParam param = new DistributorExpenseParam();
		param.setBrand(brand);
		param.setYear(year);
//		param.setSalesCai(expenseVo.getSalesCai());
		param.setRegion(expenseVo.getRegionName());
		List<DistributorExpenseVo> dataList = biProcedureBizService.queryDistributorExpense(param,
				DistributorExpenseField.DISTRIBUTOR_ID, DistributorExpenseField.EXPENSE_CODE, DistributorExpenseField.SALES_CAI, DistributorExpenseField.REGION,
				DistributorExpenseField.SPARK_AMOUNT, DistributorExpenseField.ONLINE_AMOUNT, DistributorExpenseField.MKT_AMOUNT, DistributorExpenseField.PROJECT_AMOUNT,
				DistributorExpenseField.TOTAL_AMOUNT);
		expenseVo.setSparkActual(0.0);
		expenseVo.setFlsrActual(0.0);
		expenseVo.setRegionActual(0.0);
		expenseVo.setMktActual(0.0);
		expenseVo.setProjectActual(0.0);
		for(DistributorExpenseVo vo : dataList) {
			if(distributorId.equals(vo.getDistributorId()) && vo.getSparkAmount() != null) {
				expenseVo.setSparkActual(expenseVo.getSparkActual() + vo.getSparkAmount());
			}
			if(expenseVo.getSalesCai().equals(vo.getSalesCai()) && vo.getOnlineAmount() != null) {
				expenseVo.setFlsrActual(expenseVo.getFlsrActual() + vo.getOnlineAmount());
			}
			if(expenseVo.getSalesCai().equals(vo.getSalesCai()) && vo.getOnlineAmount() != null) {
				expenseVo.setRegionActual(expenseVo.getRegionActual() + vo.getOnlineAmount());
			}
			if(expenseVo.getRegionName().equals(vo.getRegionName()) && vo.getMktAmount() != null) {
				expenseVo.setMktActual(expenseVo.getMktActual() + vo.getMktAmount());
			}
			if(expenseVo.getRegionName().equals(vo.getRegionName()) && vo.getProjectAmount() != null) {
				expenseVo.setProjectActual(expenseVo.getProjectActual() + vo.getProjectAmount());
			}
		}

//		DistributorExpenseParam param2 = new DistributorExpenseParam();
//		param2.setBrand(brand);
//		param2.setYear(year);
//		param2.setRegion(expenseVo.getRegionName());
//		List<DistributorExpenseVo> dataList2 = biProcedureBizService.queryDistributorExpense(param2,
//				DistributorExpenseField.DISTRIBUTOR_ID, DistributorExpenseField.EXPENSE_CODE, DistributorExpenseField.SALES_CAI,DistributorExpenseField.REGION,
//				DistributorExpenseField.SPARK_AMOUNT, DistributorExpenseField.ONLINE_AMOUNT, DistributorExpenseField.MKT_AMOUNT, DistributorExpenseField.PROJECT_AMOUNT,
//				DistributorExpenseField.TOTAL_AMOUNT);
//		expenseVo.setMktActual(0.0);
//		expenseVo.setProjectActual(0.0);
//		for(DistributorExpenseVo vo : dataList2) {
//			if(expenseVo.getRegionName().equals(vo.getRegionName()) && vo.getMktAmount() != null) {
//				expenseVo.setMktActual(expenseVo.getMktActual() + vo.getMktAmount());
//			}
//			if(expenseVo.getRegionName().equals(vo.getRegionName()) && vo.getProjectAmount() != null) {
//				expenseVo.setProjectActual(expenseVo.getProjectActual() + vo.getProjectAmount());
//			}
//		}

		return expenseVo;
	}

	@Override
	public DistributorExpenseVo queryBudgetAndExpenseBySales(Long salesId, ExpenseItem expenseItem, int year) throws WxPltException {
		final DistributorExpenseVo expenseVo = new DistributorExpenseVo();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select v.region_name,v.sales_cai,v.sales_name,")
				.append(" (select b.budget_value from wx_t_mkt_resource_cost_budget b where b.flsr_cai=v.sales_cai and b.sales_channel=?) flsr_budget ")
				.append(" from  wx_t_user fu1 ")
				.append(" left join view_customer_region_sales_channel v on fu1.cai=v.sales_cai ")
				.append(" where fu1.user_id=? and v.channel_weight&?>0 ");
		if((expenseItem.getChannelWeight() & 1) > 0) {
			params.add("Consumer");
		}else {
			params.add("Commercial");
		}
		params.add(expenseItem.getExpenseCode());
		params.add(salesId);
		params.add(expenseItem.getChannelWeight());
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {

			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				expenseVo.setSalesCai(arg0.getString("sales_cai"));
				expenseVo.setRegionName(arg0.getString("region_name"));
				expenseVo.setSalesName(arg0.getString("sales_name"));
				expenseVo.setFlsrBudget(arg0.getDouble("flsr_budget"));
			}
		});

		DistributorExpenseParam param = new DistributorExpenseParam();
		param.setSalesCai(expenseVo.getSalesCai());
		param.setYear(year);
		List<DistributorExpenseVo> dataList = biProcedureBizService.queryDistributorExpense(param, DistributorExpenseField.ONLINE_AMOUNT);
		expenseVo.setFlsrActual(0.0);
		for(DistributorExpenseVo vo : dataList) {
			if(vo.getOnlineAmount() != null) {
				expenseVo.setFlsrActual(expenseVo.getFlsrActual() + vo.getOnlineAmount());
			}
		}
		return expenseVo;
	}

	@Override
	public DistributorExpenseVo queryBudgetAndExpenseByDistributor(Long distributorId, ExpenseItem expenseItem,
																   int year, final boolean includeAsm, int brand) throws WxPltException {
		final DistributorExpenseVo expenseVo = new DistributorExpenseVo();
		DistributorExpenseParam param = new DistributorExpenseParam();
		//1. 获取预算
		if(year < 2021) {
			buildBudgetBefore2021(expenseVo, param, distributorId, expenseItem, year, includeAsm);
			param.setBrand(expenseItem.getChannelWeight());
		}else {
			buildBudgetAfter2021(expenseVo, param, distributorId, year, brand);
			param.setBrand(brand);
		}
		//2. 获取实际花费
		if(includeAsm) {
			param.setRegion(expenseVo.getRegionName());
		}else {
			param.setSalesCai(expenseVo.getSalesCai());
		}
		param.setYear(year);
		List<DistributorExpenseVo> dataList = biProcedureBizService.queryDistributorExpense(param,
				DistributorExpenseField.DISTRIBUTOR_ID, DistributorExpenseField.EXPENSE_CODE, DistributorExpenseField.SALES_CAI,
				DistributorExpenseField.SPARK_AMOUNT, DistributorExpenseField.ONLINE_AMOUNT,
				DistributorExpenseField.TOTAL_AMOUNT);
		expenseVo.setSparkActual(0.0);
		expenseVo.setFlsrActual(0.0);
		expenseVo.setRegionActual(0.0);
		for(DistributorExpenseVo vo : dataList) {
			if(distributorId.equals(vo.getDistributorId()) && vo.getSparkAmount() != null) {
				expenseVo.setSparkActual(expenseVo.getSparkActual() + vo.getSparkAmount());
			}
			if(expenseVo.getSalesCai().equals(vo.getSalesCai()) && vo.getOnlineAmount() != null) {
				expenseVo.setFlsrActual(expenseVo.getFlsrActual() + vo.getOnlineAmount());
			}
//			if(expenseItem.getExpenseCode().equals(vo.getExpenseCode()) && vo.getTotalAmount() != null) {
//				//只处理当前费用类型
//				expenseVo.setRegionActual(expenseVo.getRegionActual() + vo.getTotalAmount());
//			}
		}

		return expenseVo;
	}

	@Override
	public DistributorExpenseVo queryProjectBudgetAndExpenseByDistributor(Long distributorId, int year, int brand) throws WxPltException {
		final DistributorExpenseVo expenseVo = new DistributorExpenseVo();
		DistributorExpenseParam param = new DistributorExpenseParam();
		//1. 获取预算
		buildItemBudget(expenseVo, distributorId, year, brand);
		//2. 获取实际花费
		param.setBrand(brand);
		param.setYear(year);
		Budget2021ExpenseImportExample example = new Budget2021ExpenseImportExample();
		example.createCriteria().andBrandEqualTo(param.getBrand())
				.andExpenseYearEqualTo(param.getYear())
				.andExpenseMainCodeEqualTo(4)
				.andRegionNameEqualTo(expenseVo.getRegionName())
				.andDeleteFlagEqualTo(0);
		List<Budget2021ExpenseImport> budget2021ExpenseImports = budget2021ExpenseImportMapper.selectByExample(example);
		expenseVo.setFlsrActual(0.0);
		if (!CollectionUtils.isEmpty(budget2021ExpenseImports)){
			for (Budget2021ExpenseImport expenseImport : budget2021ExpenseImports) {
				expenseVo.setFlsrActual(expenseVo.getFlsrActual() + expenseImport.getPayValue());
			}
		}
		return expenseVo;
	}

	protected void buildBudgetBefore2021(final DistributorExpenseVo expenseVo, DistributorExpenseParam param,
										 Long distributorId, ExpenseItem expenseItem, int year, final boolean includeAsm) throws WxPltException {
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select v.region_name,v.sales_cai,v.sales_name,")
				.append(" (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) ")
				.append(" from wx_t_mkt_dealer_quarter t where t.year = ? and t.distributor_id=v.distributor_id and t.sales_channel=?) spark_budget,")
				.append(" (select b.budget_value from wx_t_mkt_resource_cost_budget b where b.flsr_cai=v.sales_cai and b.sales_channel=? and b.year = ?) flsr_budget ")
				.append(", v.suppervisor_cai, v.suppervisor_name");
		params.add(year);
		if((expenseItem.getChannelWeight() & 1) > 0) {
			params.add("Consumer");
			params.add("Consumer");
		}else {
			params.add("Commercial");
			params.add("Commercial");
		}
		params.add(year);
		if(includeAsm) {
			sql.append(", (select b.budget_value from wx_t_mkt_resource_cost_budget b where b.expense_code=? and b.region_name=v.region_name) region_budget");
			params.add(expenseItem.getExpenseCode());
		}
		sql.append(" from view_customer_region_sales_channel v ")
				.append(" where v.distributor_id=? and v.channel_weight&?>0 ");
		params.add(distributorId);
		params.add(expenseItem.getChannelWeight());
		log.debug(sql.toString());
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {

			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				expenseVo.setSalesCai(arg0.getString("sales_cai"));
				expenseVo.setRegionName(arg0.getString("region_name"));
				expenseVo.setSalesName(arg0.getString("sales_name"));
				expenseVo.setSupervisorCai(arg0.getString("suppervisor_cai"));
				expenseVo.setSupervisorName(arg0.getString("suppervisor_name"));
				expenseVo.setSparkBudget(arg0.getDouble("spark_budget"));
				if(arg0.wasNull()) {
					expenseVo.setSparkBudget(null);
				}
				expenseVo.setFlsrBudget(arg0.getDouble("flsr_budget"));
				if(includeAsm) {
					expenseVo.setRegionBudget(arg0.getDouble("region_budget"));
				}
			}
		});

	}

	protected void buildBudgetAfter2021(final DistributorExpenseVo expenseVo, DistributorExpenseParam param,
										Long distributorId, int year, int brand) throws WxPltException {
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select v.region,v.sales_cai, v.sales_name_cn, v.supervisor_cai, v.supervisor_name_cn, ")
				.append(" b.region_budget_value region_budget,b.mkt_budget_value mkt_budget,b.item_budget_value item_budget from dw_customer_org_sales v ")
				.append(" left join wx_t_budget2021 b on b.budget_key=v.sales_cai and b.budget_year=? and b.budget_type=3 and b.brand=?")
				.append(" where v.distributor_id=? and v.channel_weight&b.channel_weight>0");
		params.add(year);
		params.add(brand);
		params.add(distributorId);
        log.debug(sql.toString());
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {

			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				expenseVo.setSalesCai(arg0.getString("sales_cai"));
				expenseVo.setRegionName(arg0.getString("region"));
				expenseVo.setSalesName(arg0.getString("sales_name_cn"));
				expenseVo.setSupervisorCai(arg0.getString("supervisor_cai"));
				expenseVo.setSupervisorName(arg0.getString("supervisor_name_cn"));
				if(arg0.wasNull()) {
					expenseVo.setSparkBudget(null);
				}
				expenseVo.setFlsrBudget(arg0.getDouble("region_budget"));
			}
		});
		if(expenseVo.getSalesCai() == null) {
			throw new WxPltException("FLSR预算未分配");
		}
	}
	protected void buildItemBudget(final DistributorExpenseVo expenseVo, Long distributorId, int year, int brand) throws WxPltException {
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select v.region,v.sales_cai, v.sales_name_cn, v.supervisor_cai, v.supervisor_name_cn, ")
				.append(" b.region_budget_value region_budget,b.mkt_budget_value mkt_budget,b.item_budget_value item_budget from dw_customer_org_sales v ")
				.append(" left join wx_t_budget2021 b on b.budget_key=v.region and b.budget_year=? and b.budget_type=2 and b.brand=?")
				.append(" where v.distributor_id=? and v.channel_weight&b.channel_weight>0");
		params.add(year);
		params.add(brand);
		params.add(distributorId);
		log.debug(sql.toString());
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {

			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				expenseVo.setSalesCai(arg0.getString("sales_cai"));
				expenseVo.setRegionName(arg0.getString("region"));
				expenseVo.setSalesName(arg0.getString("sales_name_cn"));
				expenseVo.setSupervisorCai(arg0.getString("supervisor_cai"));
				expenseVo.setSupervisorName(arg0.getString("supervisor_name_cn"));
				if(arg0.wasNull()) {
					expenseVo.setSparkBudget(null);
				}
				expenseVo.setFlsrBudget(arg0.getDouble("item_budget"));
			}
		});
		if(expenseVo.getSalesCai() == null) {
			throw new WxPltException("大区项目预算未分配");
		}
	}


	@Override
	public Integer checkExpenseSupport(ExpenseItem expenseItem, Long distributorId) throws WxPltException {
		DicItemVoExample example = new DicItemVoExample();
		example.createCriteria().andDicTypeCodeEqualTo(Constants.ORG_APPLY_EXPENSE_NOT_SUPPORT).andDicItemCodeEqualTo(distributorId.toString().concat("&").concat(expenseItem.getExpenseCode()));
		List<DicItemVo> dicItemVos = dicItemVoMapper.selectByExample(example);
		if(dicItemVos.size() > 1){
			throw new WxPltException(expenseItem.getExpenseCode().concat("存在两条【星火计划费用经销商支持】配置"));
		}
		if(CollectionUtil.isEmpty(dicItemVos)){
			return null;
		}
		return Integer.parseInt(dicItemVos.get(0).getDicItemName());
	}

	@Override
	public PartnerCostCenterVo queryPartnerCostCenter(Long partnerId, Integer brand) throws WxPltException {
		PartnerCostCenterVo partnerCostCenterVo = new PartnerCostCenterVo();
		partnerCostCenterVo.setBrandId(brand);
		partnerCostCenterVo.setPartnerId(partnerId);
		//param
		PartnerCostCenterParam param = new PartnerCostCenterParam();
		param.setPartnerId(partnerId);
		param.setBrandId(brand);
		List<PartnerCostCenterVo> dataList = biProcedureBizService.queryPartnerCostCenter(param
				, PartnerCostCenterField.PARTNER_ID
				, PartnerCostCenterField.PARTNER_NAME_CN
				, PartnerCostCenterField.BRAND_ID
				, PartnerCostCenterField.COST_CENTER
				, PartnerCostCenterField.COMPANY_CODE);

		if(!CollectionUtils.isEmpty(dataList)){
			partnerCostCenterVo.setCompanyCode(dataList.get(0).getCompanyCode());
			partnerCostCenterVo.setCostCenter(dataList.get(0).getCostCenter());
			partnerCostCenterVo.setPartnerNameCn(dataList.get(0).getPartnerNameCn());
		}
		return partnerCostCenterVo;
	}

	@Override
	public PartnerSeminarVo queryPartnerSeminar(Long partnerId, Integer brand) throws WxPltException {
		PartnerSeminarVo partnerSeminarVo = new PartnerSeminarVo();
		partnerSeminarVo.setPartnerId(partnerId);
		partnerSeminarVo.setBrandId(brand);
		partnerSeminarVo.setSeminarQty(0);
		//param
		PartnerSeminarParam param = new PartnerSeminarParam();
		param.setPartnerId(partnerId.toString());
		param.setBrandId(brand.toString());
		List<PartnerSeminarVo> dataList = biProcedureBizService.queryPartnerSeminar(param
				, PartnerSeminarField.PARTNER_ID
//				, PartnerSeminarField.PARTNER_NAME_CN
				, PartnerSeminarField.BRAND_ID
				, PartnerSeminarField.SEMINAR_QTY);

		if(!CollectionUtils.isEmpty(dataList)){
			partnerSeminarVo.setSeminarQty(dataList.get(0).getSeminarQty() == null ? 0 : dataList.get(0).getSeminarQty());
			partnerSeminarVo.setPartnerNameCn(dataList.get(0).getPartnerNameCn());
		}

		return partnerSeminarVo;
	}

	@Override
	public PartnerSignageVo queryPartnerSignage(Long partnerId, Integer brand) {
		PartnerSignageVo partnerSignageVo = new PartnerSignageVo();
		partnerSignageVo.setPartnerId(partnerId);
		partnerSignageVo.setBrandId(brand);
		partnerSignageVo.setShopForShopActiveEndMarketQty(0);
		partnerSignageVo.setShopForShopLossingEndMarketQty(0);
		partnerSignageVo.setShopForShopEndMarketAvgSellThrough(0d);
		partnerSignageVo.setSignageQty(0);

		PartnerEndMarketOverallPerformanceParam param = new PartnerEndMarketOverallPerformanceParam();
		param.setPartnerId(partnerId.toString());
		param.setBrandId(brand.toString());
		List<PartnerEndMarketOverallPerformanceVo> dataList = biProcedureBizService.queryPartnerEndMarketOverallPerformance(param
				, PartnerEndMarketOverallPerformanceField.PARTNER_ID
				, PartnerEndMarketOverallPerformanceField.PARTNER_NAME_CN
				, PartnerEndMarketOverallPerformanceField.BRAND_ID
				, PartnerEndMarketOverallPerformanceField.SHOP_FOR_SHOP_ACTIVE_END_MARKET_QTY
				, PartnerEndMarketOverallPerformanceField.SHOP_FOR_SHOP_LOSSING_END_MARKET_QTY
//				, PartnerEndMarketOverallPerformanceField.SHOP_FOR_SHOP_END_MARKET_AVG_SELL_THROUGH
				, PartnerEndMarketOverallPerformanceField.SIGNAGE_QTY);

		if(!CollectionUtils.isEmpty(dataList)){
			partnerSignageVo.setPartnerNameCn(dataList.get(0).getPartnerNameCn());
			partnerSignageVo.setShopForShopActiveEndMarketQty(dataList.get(0).getShopForShopActiveEndMarketQty() == null ? 0 : dataList.get(0).getShopForShopActiveEndMarketQty());
			partnerSignageVo.setShopForShopLossingEndMarketQty(dataList.get(0).getShopForShopLossingEndMarketQty() == null ? 0 : dataList.get(0).getShopForShopLossingEndMarketQty());
			partnerSignageVo.setShopForShopEndMarketAvgSellThrough(dataList.get(0).getShopForShopEndMarketAvgSellThrough() == null ? 0d : dataList.get(0).getShopForShopEndMarketAvgSellThrough());
			partnerSignageVo.setSignageQty(dataList.get(0).getSignageQty() == null ? 0 : dataList.get(0).getSignageQty());
		}

		return partnerSignageVo;
	}

	@Override
	public PartnerStoreSignageVo queryPartnerStoreSignage(Long partnerId, Integer brand, Long storeId) throws WxPltException {
		PartnerStoreSignageVo partnerSignageVo = new PartnerStoreSignageVo();
		partnerSignageVo.setPartnerId(partnerId);
		partnerSignageVo.setBrandId(brand);
		partnerSignageVo.setSignageQty(0);

		PartnerEndMarketOverallPerformanceParam param = new PartnerEndMarketOverallPerformanceParam();
		param.setPartnerId(partnerId.toString());
		param.setBrandId(brand.toString());
		param.setEndMarketCustomerId(storeId.toString());
		List<PartnerEndMarketOverallPerformanceVo> dataList = biProcedureBizService.queryPartnerEndMarketOverallPerformance(param
				, PartnerEndMarketOverallPerformanceField.PARTNER_ID
				, PartnerEndMarketOverallPerformanceField.PARTNER_NAME_CN
				, PartnerEndMarketOverallPerformanceField.BRAND_ID
//				, PartnerEndMarketOverallPerformanceField.SHOP_FOR_SHOP_ACTIVE_END_MARKET_QTY
//				, PartnerEndMarketOverallPerformanceField.SHOP_FOR_SHOP_LOSSING_END_MARKET_QTY
//				, PartnerEndMarketOverallPerformanceField.SHOP_FOR_SHOP_END_MARKET_AVG_SELL_THROUGH
				, PartnerEndMarketOverallPerformanceField.SIGNAGE_QTY);

		if(!CollectionUtils.isEmpty(dataList)){
			partnerSignageVo.setPartnerNameCn(dataList.get(0).getPartnerNameCn());
			partnerSignageVo.setSignageQty(dataList.get(0).getSignageQty() == null ? 0 : dataList.get(0).getSignageQty());
		}

		return partnerSignageVo;
	}

	@Override
	public PartnerOverallPerformanceVo queryPartnerOverallPerformance(Long partnerId, Integer brand) throws WxPltException {
		PartnerOverallPerformanceVo partnerOverallPerformanceVo = new PartnerOverallPerformanceVo();
		partnerOverallPerformanceVo.setPartnerId(partnerId);
		if((Constants.getChannelWeightByBrand(brand) & 1) > 0) {
			partnerOverallPerformanceVo.setProductChannel("Consumer");
		}else {
			partnerOverallPerformanceVo.setProductChannel("Commercial");
		}
		partnerOverallPerformanceVo.setYtdSellIn(0d);
		partnerOverallPerformanceVo.setYtdSellThrough(0d);
		partnerOverallPerformanceVo.setRolling12MonthSellIn(0d);
		partnerOverallPerformanceVo.setRolling12MonthSellThrough(0d);
		partnerOverallPerformanceVo.setRolling12MonthAvgSellInMarginRmb(0d);
		partnerOverallPerformanceVo.setRolling12MonthAvgSellThroughVol(0d);
		partnerOverallPerformanceVo.setShopForShopEndMarketAvgSellThrough(0d);

		PartnerOverallPerformanceParam param = new PartnerOverallPerformanceParam();
		param.setPartnerId(partnerId.toString());
		if((Constants.getChannelWeightByBrand(brand) & 1) > 0) {
			param.setProductChannel("Consumer");
		}else {
			param.setProductChannel("Commercial");
		}
		List<PartnerOverallPerformanceVo> dataList = biProcedureBizService.queryPartnerOverallPerformance(param, PartnerOverallPerformanceField.PARTNER_ID, PartnerOverallPerformanceField.PARTNER_NAME_CN
				, PartnerOverallPerformanceField.PRODUCT_CHANNEL, PartnerOverallPerformanceField.YTD_SELL_IN, PartnerOverallPerformanceField.YTD_SELL_THROUGH
				, PartnerOverallPerformanceField.ROLLING_12_MONTH_SELL_IN, PartnerOverallPerformanceField.ROLLING_12_MONTH_SELL_THROUGH
				, PartnerOverallPerformanceField.ROLLING_12_MONTH_AVG_SELL_IN_MARGIN_RMB, PartnerOverallPerformanceField.ROLLING_12_MONTH_AVG_SELL_THROUGH_VOL
				, PartnerOverallPerformanceField.SHOP_FOR_SHOP_END_MARKET_AVG_SELL_THROUGH);

		if(!CollectionUtils.isEmpty(dataList)){
			partnerOverallPerformanceVo.setPartnerNameCn(dataList.get(0).getPartnerNameCn());
			partnerOverallPerformanceVo.setProductChannel(dataList.get(0).getProductChannel());
			partnerOverallPerformanceVo.setYtdSellIn(dataList.get(0).getYtdSellIn() == null ? 0d : dataList.get(0).getYtdSellIn());
			partnerOverallPerformanceVo.setYtdSellThrough(dataList.get(0).getYtdSellThrough() == null ? 0d : dataList.get(0).getYtdSellThrough());
			partnerOverallPerformanceVo.setRolling12MonthSellIn(dataList.get(0).getRolling12MonthSellIn() == null ? 0d : dataList.get(0).getYtdSellThrough());
			partnerOverallPerformanceVo.setRolling12MonthSellThrough(dataList.get(0).getRolling12MonthSellThrough() == null ? 0d : dataList.get(0).getRolling12MonthSellThrough());
			partnerOverallPerformanceVo.setRolling12MonthAvgSellInMarginRmb(dataList.get(0).getRolling12MonthAvgSellInMarginRmb() == null ? 0d : dataList.get(0).getRolling12MonthAvgSellInMarginRmb());
			partnerOverallPerformanceVo.setRolling12MonthAvgSellThroughVol(dataList.get(0).getRolling12MonthAvgSellThroughVol() == null ? 0d : dataList.get(0).getRolling12MonthAvgSellThroughVol());
			partnerOverallPerformanceVo.setShopForShopEndMarketAvgSellThrough(dataList.get(0).getShopForShopEndMarketAvgSellThrough() == null ? 0d : dataList.get(0).getShopForShopEndMarketAvgSellThrough());
		}

		return partnerOverallPerformanceVo;
	}

	@Override
	public List<PartnerProductOverallPerformanceVo> queryPartnerProductOverallPerformance(Long partnerId, Integer brand) throws WxPltException {
		List<PartnerProductOverallPerformanceVo> list = Lists.newArrayList();

		PartnerProductOverallPerformanceParam param = new PartnerProductOverallPerformanceParam();
		param.setPartnerId(partnerId.toString());
		if((Constants.getChannelWeightByBrand(brand) & 1) > 0) {
			param.setProductChannel("Consumer");
		}else {
			param.setProductChannel("Commercial");
		}
		param.setIsByCategory(1);
		List<PartnerProductOverallPerformanceVo> dataList = biProcedureBizService.queryPartnerProductOverallPerformance(param
				, PartnerProductOverallPerformanceField.PARTNER_ID, PartnerProductOverallPerformanceField.PARTNER_NAME_CN
				, PartnerProductOverallPerformanceField.PRODUCT_CHANNEL, PartnerProductOverallPerformanceField.PRODUCT_CATEGORY
				, PartnerProductOverallPerformanceField.YTD_SELL_IN, PartnerProductOverallPerformanceField.YTD_SELL_THROUGH
				, PartnerProductOverallPerformanceField.ROLLING_12_MONTH_SELL_IN, PartnerProductOverallPerformanceField.ROLLING_12_MONTH_SELL_THROUGH
				, PartnerProductOverallPerformanceField.ROLLING_12_MONTH_AVG_SELL_IN_MARGIN_RMB, PartnerProductOverallPerformanceField.ROLLING_12_MONTH_AVG_SELL_THROUGH_VOL
				, PartnerProductOverallPerformanceField.SHOP_FOR_SHOP_END_MARKET_AVG_SELL_THROUGH);

		if(!CollectionUtils.isEmpty(dataList)){
			for (PartnerProductOverallPerformanceVo vo : dataList) {
				PartnerProductOverallPerformanceVo partnerOverallPerformanceVo = new PartnerProductOverallPerformanceVo();
				partnerOverallPerformanceVo.setPartnerId(vo.getPartnerId());
				partnerOverallPerformanceVo.setProductChannel(vo.getProductChannel());
				partnerOverallPerformanceVo.setProductCategory(vo.getProductCategory());
				partnerOverallPerformanceVo.setYtdSellIn(vo.getYtdSellIn());
				partnerOverallPerformanceVo.setYtdSellThrough(vo.getYtdSellThrough());
				partnerOverallPerformanceVo.setRolling12MonthSellIn(vo.getRolling12MonthSellIn());
				partnerOverallPerformanceVo.setRolling12MonthSellThrough(vo.getRolling12MonthSellThrough());
				partnerOverallPerformanceVo.setRolling12MonthAvgSellInMarginRmb(vo.getRolling12MonthAvgSellInMarginRmb());
				partnerOverallPerformanceVo.setRolling12MonthAvgSellThroughVol(vo.getRolling12MonthAvgSellThroughVol());
				partnerOverallPerformanceVo.setShopForShopEndMarketAvgSellThrough(vo.getShopForShopEndMarketAvgSellThrough());
				list.add(partnerOverallPerformanceVo);
			}
		}

		return list;
	}

	@Override
	public void execProcessPointsToExpense(String pointType, Long businessId) throws WxPltException {
		ProcessPointsToExpenseParam param = new ProcessPointsToExpenseParam();
		param.setPointType(pointType);
		param.setBusinessId(businessId);
		biProcedureBizService.execProcessPointsToExpense(param);
	}

}
