--1. 门店经销商关系
select * 
--update w set w.partner_id=9
from wx_t_work_shop w where w.id=186332
select * 
--update wp set wp.partner_id=9
from wx_t_workshop_partner wp where wp.workshop_id=186332

--2. 门店执行人
update w set w.excute_user_id=(select top 1 u.user_id from wx_t_user u
  left join wx_t_organization o on o.id=u.org_id
  where (u.type is null or u.type !='1') and o.type=1 and u.status='1' and u.org_id=w.partner_id
  and exists (select 1 from wx_t_userrole ur
left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id
and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')) order by u.user_id)
from wx_t_work_shop w where w.id=186332

--3. 更新门店技师经销商
select *
--update we set partner_id=9 
from wx_t_workshop_employee we where we.workshop_id=186332