package com.chevron.o2oorder.Constant;

import com.chevron.disb2b.model.Db2bOrder;
import com.chevron.disb2b.util.Db2bConstants;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.MD5;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONObject;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

public class HttpClientUtils {
    private static final String OPEN_ID = "openid";  //
    private static final Log logger = LogFactory.getLog(HttpClientUtils.class);
        public static String getToken(){
            String url = WexConstants.BASE_TOKEN_URL + "grant_type="+WexConstants.GRANT_TYPE+"&appid="+WexConstants.APP_ID+"&secret="+WexConstants.SECRET;
            JSONObject jsonObject=null;
            try{
                URL realUrl = new URL(url);
                // 打开和URL之间的连接
                HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
                connection.setRequestMethod("GET");
                connection.connect();
                // 获取所有响应头字段
                Map<String, List<String>> map = connection.getHeaderFields();
                // 遍历所有的响应头字段
                for (String key : map.keySet()) {
                    logger.warn(key + "--->" + map.get(key));
                }
                // 定义 BufferedReader输入流来读取URL的响应
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String result = "";
                String line;
                while ((line = in.readLine()) != null) {
                    result += line;
                }
                jsonObject = new JSONObject(result);
                if(jsonObject.has("access_token")){
                    return jsonObject.getString("access_token");
                }
                return "";
            } catch (Exception e) {
                logger.error("获取token失败,原因为:"+e.getMessage());
				logger.info("accesstoken:"+jsonObject);
                e.printStackTrace(System.err);
                return "";
            }
        }
        
        public static String getJsApiTicket(String accessToken) throws Exception {
            InputStream inputStream = null;
            HttpURLConnection connection = null;
            String message = "";
            try {
                String path = WexConstants.JSPA_TICKET_URL +"access_token="+accessToken + "&type=jsapi";
                URL url = new URL(path);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                //设置连接超时
                connection.setConnectTimeout(50000);
                //是否打开输入流 ， 此方法默认为true
                connection.setDoInput(true);
                //是否打开输出流， 此方法默认为false
                connection.setDoOutput(true);
                connection.setUseCaches(false);
                //表示连接
                connection.connect();
                inputStream = connection.getInputStream();
     
                int size = inputStream.available();
                byte[] bs = new byte[size];
                inputStream.read(bs);
                message = new String(bs, "UTF-8");
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("ticketToken:"+message);
            } finally {
                // 关闭资源
                if (inputStream != null) {
                    inputStream.close();
                }
                // 关闭远程链接
                if (connection != null) {
                    connection.disconnect();
                }
            }
            JSONObject jsonObject = new JSONObject(message);
            if(jsonObject.has("ticket")){
                return jsonObject.getString("ticket");
            }
            return "";
        }
        
//        public static File getWexinImages(String id,String savePath){
//        	String accessToken = ContextUtil.getO2OAccessToken(WexConstants.ACCESS_TOKEN);
//            String path = WexConstants.IMAGE_URL +"access_token="+accessToken+"&media_id="+id;
//            InputStream in = null;
//            FileOutputStream out = null;
//            try {
//            	 // 创建SSLContext对象，并使用我们指定的信任管理器初始化
//                TrustManager[] tm = { new MyX509TrustManager() };
//                SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
//                sslContext.init(null, tm, new java.security.SecureRandom());
//                // 从上述SSLContext对象中得到SSLSocketFactory对象
//                javax.net.ssl.SSLSocketFactory ssf = sslContext.getSocketFactory();
//
//                URL url = new URL(path);
//                HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
//                httpUrlConn.setSSLSocketFactory(ssf);
//
//                httpUrlConn.setDoOutput(true);
//                httpUrlConn.setDoInput(true);
//                httpUrlConn.setUseCaches(false);
//                // 设置请求方式（GET/POST）
//                httpUrlConn.setRequestMethod("GET");
//                httpUrlConn.connect();
//
//                //获取文件扩展名
//                String ext=getExt(httpUrlConn.getContentType());
//                Map<String, List<String>> map = httpUrlConn.getHeaderFields();
//                // 遍历所有的响应头字段
//                for (String key : map.keySet()) {
//                    System.err.println(key + "--->" + map.get(key));
//                }
//               // savePath=savePath;
//                System.out.println("savePath"+savePath);
//                File file = new File(savePath+"images"+ext);
//                in = httpUrlConn.getInputStream();
//                out = new FileOutputStream(file);
//                int length=100*1024;
//                byte[] byteBuffer = new byte[length]; //存储文件内容
//
//                int byteread =0;
//                int bytesum=0;
//                while (( byteread=in.read(byteBuffer)) != -1) {
//                    bytesum += byteread;
//                    out.write(byteBuffer,0,byteread);
//
//                }
//                httpUrlConn.disconnect();
//                return file;
//			} catch (Exception e) {
//			}finally {
//                if( in != null){
//                    try{
//                        in.close();
//                    }catch (Exception eIn){
//                        eIn.printStackTrace();
//                    }
//                }
//                if(out != null){
//                    try{
//                        out.close();
//                    }catch (Exception eOut){
//                        eOut.printStackTrace();
//                    }
//                }
//            }
//			return null;
//        }
        
        public static HttpURLConnection httpRequest(String id) {
        	String accessToken = ContextUtil.getO2OAccessToken(WexConstants.ACCESS_TOKEN);
            String path = WexConstants.IMAGE_URL +"access_token="+accessToken+"&media_id="+id;
            HttpURLConnection  httpUrlConn=null;
            try {  
                URL url = new URL(path);  
                httpUrlConn = (HttpURLConnection) url.openConnection();  

                httpUrlConn.setDoOutput(false);  
                httpUrlConn.setDoInput(true);  
                httpUrlConn.setUseCaches(false);  
                httpUrlConn.setRequestMethod("GET");  
                httpUrlConn.connect();  
            } catch (Exception e) {
                e.printStackTrace();
            }  
            return httpUrlConn ;
        } 
        
//        private static String getExt(String contentType){
//            if("image/jpeg".equals(contentType)){
//                return ".jpg";
//            }else if("image/png".equals(contentType)){
//                return ".png";
//            }else if("image/gif".equals(contentType)){
//                return ".gif";
//            }
//
//            return null;
//        }
        
        //获取微信公众号用户的openId
        public static String getOpenId(String Code) {
        	 String url = WexConstants.OPEN_ID_URL +"appid="+WexConstants.APP_ID+"&secret="+WexConstants.SECRET+"&code="+Code+"&grant_type="+WexConstants.OPENID_GRANT_TYPE;
             JSONObject jsonObject=null;
             try{
                 URL realUrl = new URL(url);
                 // 打开和URL之间的连接
                 HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
                 connection.setRequestMethod("GET");
                 connection.connect();
                 // 定义 BufferedReader输入流来读取URL的响应
                 BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                 String result = "";
                 String line;
                 while ((line = in.readLine()) != null) {
                     result += line;
                 }
                 jsonObject = new JSONObject(result);
                 return jsonObject.getString(OPEN_ID);//"openid"
             } catch (Exception e) {
                 e.printStackTrace();
                 logger.error("获取openId失败,原因为:"+e.getMessage());
 				logger.info("accesstoken:"+jsonObject);
                 return "";
             }
        }
        
        //B2B获取微信公众号用户的openId
        public static String getB2bOpenId(String code,String openIdUrl,String appId,String secret) {
        	String url = openIdUrl +"appid="+appId+"&secret="+secret+"&code="+code+"&grant_type="+WexConstants.OPENID_GRANT_TYPE;
             JSONObject jsonObject=null;
             try{
                 URL realUrl = new URL(url);
                 // 打开和URL之间的连接
                 HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
                 connection.setRequestMethod("GET");
                 connection.connect();
                 // 定义 BufferedReader输入流来读取URL的响应
                 BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                 String result = "";
                 String line;
                 while ((line = in.readLine()) != null) {
                     result += line;
                 }
                 jsonObject = new JSONObject(result);
                 return jsonObject.getString(OPEN_ID);
             } catch (Exception e) {
                 e.printStackTrace();
                 logger.error("获取openId失败！异常为:"+e.getMessage());
 				logger.info("accesstoken:"+jsonObject);
                 return "";
             }
        }
        
        //B2B根据订单信息，调用微信统一接口
        
        public static String httpXML(String url, String xml) {
    		String result = null;
    		try {
    			InputStream is = httpPostXML(url, xml);
    			BufferedReader in = new BufferedReader(new InputStreamReader(is,
    					"UTF-8"));
                StringBuilder buffer = new StringBuilder();
    			String line = "";
    			while ((line = in.readLine()) != null) {
    				buffer.append(line);
    			}
    			result = buffer.toString();
    		} catch (Exception e) {
    			e.printStackTrace();
    			return null;
    		}
    		return result;
    	}
     
        /*微信统一接口*/
    	private static InputStream httpPostXML(String url, String xml) {
    		InputStream is = null;
    		PrintWriter out = null;
    		try {
    			URL u;
    			u = new URL(url);
    			HttpURLConnection conn = (HttpURLConnection) u.openConnection();
    			conn.setRequestProperty("Content-Type",
    					"application/x-www-form-urlencoded");
    			conn.setConnectTimeout(20000);
    			conn.setReadTimeout(50000);
    			conn.setDoOutput(true);
    			conn.setDoInput(true);
    			conn.setUseCaches(false);
    			conn.setRequestMethod("POST");
    			OutputStreamWriter outWriter = new OutputStreamWriter(
    					conn.getOutputStream(), "UTF-8");
    			out = new PrintWriter(outWriter);
    			
    			out.print(xml);
    			out.flush();
    			out.close();
     
    			is = conn.getInputStream();
    		} catch (Exception e) {
     
    			e.printStackTrace();
    		}
     
    		return is;
    	}
    	public static String orderInfoXml(Map<String, Object> params) {
    		Db2bOrder order = (Db2bOrder) params.get("orderInfos");
    		String xml="";
    		Map <String,Object> map=new HashMap<String,Object>();
    		//换成配置
    		map.put("appid", params.get("appId"));
    		map.put("mch_id", params.get("mchId"));
    		String key = (String) params.get("apiKey");
    		map.put("nonce_str", UUID.randomUUID().toString().replace("-", "").substring(0, 16));
    		map.put("sign_type", "MD5");
    		map.put("detail", order.toString());
    		map.put("out_trade_no", order.getOrderNo()+CommonUtil.generateSequenceCode("CreateOrderNoByTime", 4));//保证每次生成的订单都不一样，这样可以重复提交订单，获取系统订单的话，截掉后面四位
    		map.put("total_fee",String.valueOf((Math.round(order.getTotalPayMoney()*100))));//把页面传过来的价格price放到订单map里，其他参数同理可传进来
    		map.put(OPEN_ID, order.getOpenId());
    		map.put("spbill_create_ip", "*************");
    		map.put("notify_url", "http://wwwstg.cvx-sh.com");//这里是微信支付结果通知的接口，用来接收订单支付情况
    		map.put("trade_type", "JSAPI");
		    map.put("body",Db2bConstants.WECHAT_PAY_PRODUCT_NAME);
    		String sign="";
			String signTemp = sort(map);
			signTemp = signTemp + "&key="+key;
			sign = MD5.MD5Encodes(signTemp);
    		map.put("sign", sign);
    		xml=XmlUtils.toXml(map);
    		return xml;
    	}
        public static Map<String, Object> wxPay(Map<String, Object> params) throws AesException{
        	Db2bOrder order = (Db2bOrder) params.get("orderInfos");
        	String xml=HttpClientUtils.orderInfoXml(params);
        	String wxResult = HttpClientUtils.httpXML(WexConstants.WX_PAY_URL, xml);
        	Map<String, Object> map=new HashMap<String,Object>();
    		map=XmlUtils.toMap(wxResult);
    		String timeStamp =String.valueOf(System.currentTimeMillis() / 1000);
    		Map<String ,Object > paramsMap=new HashMap<String ,Object>();
    		paramsMap.put("package","prepay_id="+ map.get("prepay_id"));
    		paramsMap.put("appId",map.get("appid"));
    		paramsMap.put("nonceStr",map.get("nonce_str"));
    		paramsMap.put("signType", "MD5");
    		paramsMap.put("timeStamp", timeStamp);
    		String signTemp = sort(paramsMap);
    		String key = (String) params.get("apiKey");
			signTemp = signTemp + "&key="+ key;
    		String sign=MD5.MD5Encodes(signTemp);
    		paramsMap.put("sign", sign);
    		paramsMap.put("out_trade_no", order.getOrderNo());
    		paramsMap.put("openId", order.getOpenId());
        	return paramsMap;
        }
        
        //排序
      public static String sort(Map<String, Object> paramsMap) {
        	paramsMap = sortMapByKey(paramsMap);
        String signTemp = "";
         for(Map.Entry<String, Object> entry : paramsMap.entrySet()) {
        	 String keyValues = entry.getKey()+"="+ entry.getValue() + "&";
        	 signTemp = signTemp + keyValues;
         }
         if(!"".equals(signTemp)) {
        	 signTemp = signTemp.substring(0, signTemp.length()-1);
         }
         return signTemp;
        }
       public static Map<String, Object> sortMapByKey(Map<String, Object> map) {
        	if (map == null || map.isEmpty()) {
        	   return new HashMap<String, Object>();
        	 }

           Map<String, Object> sortMap =new TreeMap<String, Object>(new MapKeyComparator());
           sortMap.putAll(map);
           return sortMap;
        }
}
