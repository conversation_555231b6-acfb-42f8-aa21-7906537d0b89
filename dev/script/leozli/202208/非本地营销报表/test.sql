select * from wx_t_non_local_user_report where from_distributor_id is null

delete wx_t_non_local_mkt_flow_pro_res where id in (355,944,1176)

select * from wx_t_loc_mkt_product_info where report_id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)
select r.* from wx_t_non_local_mkt_flow_form f left join wx_t_non_local_mkt_flow_pro_res r on r.flow_id=f.id  where report_id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)
select * from wx_t_non_local_mkt_flow_form  where report_id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)
select * from wx_t_Main_Local_marketing where id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)

delete from wx_t_loc_mkt_product_info where report_id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)
delete from wx_t_non_local_mkt_flow_form  where report_id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)
delete from wx_t_Main_Local_marketing where id in 
(13577,12237,1790,12959,14663,14359,12358,13785,14007,13334,13615,14006,13999)



select logistics_code, count(1) from wx_t_loc_mkt_product_info r group by logistics_code
having count(1)>1
--select * from wx_t_Main_Local_marketing where id in (13577,14144)

select from_distributor_id, [上报来源], (select distinct customer_name_cn from [PP_MID].[dbo].[syn_dw_to_pp_customer] where distributor_id=from_distributor_id), count(1) from ( 
select f.from_distributor_id,
('R'+case m.reprot_type when 1 then 'SRSM' when 2 then 'USAO' when 3 then 'ADLM' when 4 then 'MESA' end+convert(nvarchar(20),m.create_time,112)+convert(nvarchar(20),m.id%10000) ) as '上报ID',
d1.dic_item_name as '上报来源',m.report_name as '上报人',
convert(nvarchar(20),m.create_time,20) as '上报日期',
year(m.create_time) as '上报年',
month(m.create_time) as '上报月',
day(m.create_time) as '上报日',p.region_name as '上报省',
c.region_name as '上报市',
ISNULL(p1.name,b.product_name) as '产品名称',
case when not exists (select 1 from wx_t_region prov1 where prov1.region_type='P' and prov1.status=1 
	and exists (select 1 from wx_t_region city1 where city1.status=1 and city1.parent_id=prov1.id)
	and not exists (select 1 from wx_t_region r1 
		join wx_t_region r2 on r2.id=r1.parent_id
        where r2.parent_id=prov1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as "省",
case when not exists (select 1 from wx_t_region city1 where city1.region_type='C' and city1.status=1 
	and not exists (select 1 from wx_t_region r1 
        where r1.parent_id=city1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as "市",
b.sku SKU,
bp01.t3_category 'T3 category',
b.scan_time "出库日期",b.product_time "生产日期",b.product_batch "生产批号",
        b.product_out_no "出库单号",b.logistics_code "产品物流码",
d2.dic_item_name as '产品渠道',
ISNULL(dic1.dic_item_name,cs.customer_name_cn) as '被非本地营销影响经销商',
cs.region "被非本地营销影响大区",
diac1.dic_item_name "被非本地营销影响经销商授权渠道",
 (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.to_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '被非本地营销影响经销商授权省',
(select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.to_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '被非本地营销影响经销商授权市',
cs.sales_name_cn "被非本地营销影响FLSR",
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '非本地营销经销商',
cs1.region "非本地营销大区",
diac1.dic_item_name "非本地营销影响经销商授权渠道",
 case when not exists (select 1 from wx_t_region prov1 where prov1.region_type='P' and prov1.status=1 
	and exists (select 1 from wx_t_region city1 where city1.status=1 and city1.parent_id=prov1.id)
	and not exists (select 1 from wx_t_region r1 
		join wx_t_region r2 on r2.id=r1.parent_id
        where r2.parent_id=prov1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as '非本地营销影响经销商授权省',
case when not exists (select 1 from wx_t_region city1 where city1.region_type='C' and city1.status=1 
	and not exists (select 1 from wx_t_region r1 
        where r1.parent_id=city1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as '非本地营销影响经销商授权市',
cs1.sales_name_cn "非本地营销FLSR",

case when f.to_flsr_confirm is null then '' when f.to_flsr_confirm = 0 then '否' else '是' end as '被非本地营销影响FLSR是否同意上报',
case when f.form_status = 50 then 'ABM已确认' 
when f.form_status = 35 then '不处理'
when f.from_asm_response = -1 then '未确认' 
when f.from_asm_response = 1 then '已接受' 
when f.from_asm_response = 0 then '待仲裁' 
else '-' end as '判定状态',
case when r3.local_marketing_status = 1 then '是' when r3.local_marketing_status = 0 then '否' else '-' end as '非本地营销（ABM）',
IIF(r3.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r3.punish_flag & 2 > 0 and r3.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r3.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r3.punish_flag & 32 > 0 and len(r3.attribute9)>0, dbo.[fun_get_nonlocal_quarter_punish_desc](r3.attribute9, r3.attribute3),'')
+ IIF(r3.punish_flag & 16 > 0 and r3.attribute6 is not null, '扣除' + r3.attribute3 + '年的年终奖励的' + r3.attribute6 + '%;','')
+ IIF(r3.punish_flag & 64 > 0 and len(r3.attribute10)>0, dbo.[fun_get_nonlocal_sku_punish_desc](r3.attribute10, r3.attribute3),'')
+ IIF(r3.punish_flag & 4 > 0 and r3.other_reasons is not null,'其他（' + r3.other_reasons +' ）;','') as 'ABM处罚内容',
case when r3.kpi_result = 1 then '合格' when r3.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（ABM）',
r3.handle_info as 'ABM建议',
case when r3.attribute1='1' then '是' else '否' end "处罚文件已确认",
CONVERT(varchar(100), ei.deliver_time, 20) "处罚文件发放时间",
case when r2.local_marketing_status = 1 then '是' when r2.local_marketing_status = 0 then '否' else '-' end as '非本地营销（被非本地营销影响ASM）',
IIF(r2.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r2.punish_flag & 2 > 0 and r2.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r2.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r2.punish_flag & 32 > 0 and len(r2.attribute9)>0, dbo.[fun_get_nonlocal_quarter_punish_desc](r2.attribute9, r2.attribute3),'')
+ IIF(r2.punish_flag & 16 > 0 and r2.attribute6 is not null, '扣除' + r2.attribute3 + '年的年终奖励的' + r2.attribute6 + '%;','')
+ IIF(r2.punish_flag & 64 > 0 and len(r2.attribute10)>0, dbo.[fun_get_nonlocal_sku_punish_desc](r2.attribute10, r2.attribute3),'')
+ IIF(r2.punish_flag & 4 > 0 and r2.other_reasons is not null,'其他（' + r2.other_reasons +' ）','') as '被非本地营销影响ASM处罚内容',
case when r2.kpi_result = 1 then '合格' when r2.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（被非本地营销影响ASM）',
r2.handle_info as '被非本地营销影响ASM建议',
case when r1.local_marketing_status = 1 then '是' when r1.local_marketing_status = 0 then '否' else '-' end as '非本地营销（非本地营销ASM）',
IIF(r1.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r1.punish_flag & 2 > 0 and r1.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r1.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r1.punish_flag & 32 > 0 and len(r1.attribute9)>0, dbo.[fun_get_nonlocal_quarter_punish_desc](r1.attribute9, r1.attribute3),'')
+ IIF(r1.punish_flag & 16 > 0 and r1.attribute6 is not null, '扣除' + r1.attribute3 + '年的年终奖励的' + r1.attribute6 + '%;','')
+ IIF(r1.punish_flag & 64 > 0 and len(r1.attribute10)>0, dbo.[fun_get_nonlocal_sku_punish_desc](r1.attribute10, r1.attribute3),'')
+ IIF(r1.punish_flag & 4 > 0 and r1.other_reasons is not null,'其他（' + r1.other_reasons +' ）','') as '非本地营销ASM处罚内容',
case when r1.kpi_result = 1 then '合格' when r1.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（非本地营销ASM）',
r1.handle_info as '非本地营销ASM建议',
case when r0.local_marketing_status = 1 then '是' when r0.local_marketing_status = 0 then '否' else '-' end as '非本地营销（非本地营销FLSR）',
r0.handle_info as '非本地营销FLSR建议'
from wx_t_Main_Local_marketing m
left join wx_t_non_local_mkt_flow_form f on f.report_id  = m.id 
left join wx_t_loc_mkt_product_info b on f.id = b.flow_id
left join wx_t_non_local_mkt_flow_pro_res r0 on r0.flow_id = b.flow_id and r0.channel  = b.channel  and r0.setp_no  = 'FROM_FLSR_SUBMIT_SURVEY'
left join wx_t_non_local_mkt_flow_pro_res r1 on r1.flow_id = b.flow_id and r1.channel  = b.channel  and r1.setp_no in ('FROM_ASM_SUBMIT_SURVEY','GQ_OEM_HANDLER')
left join wx_t_non_local_mkt_flow_pro_res r2 on r2.flow_id = b.flow_id and r2.channel  = b.channel  and r2.setp_no  = 'TO_ASM_SUBMIT_FEEDBACK'
left join wx_t_non_local_mkt_flow_pro_res r3 on r3.flow_id = b.flow_id and r3.channel  = b.channel  and r3.setp_no  = 'ABM_CONFIRM'
left join dw_customer_org_sales cs on cs.distributor_id = f.to_distributor_id and cs.channel_weight&(case when b.auth_channel&3=2 then 2 else 1 end)>0
left join dw_customer_org_sales cs1 on cs1.distributor_id = f.from_distributor_id and cs1.channel_weight&(case when b.auth_channel&3=2 then 2 else 1 end)>0
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = f.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_dic_item dic1 on cast(dic1.dic_item_code as bigint) = f.to_distributor_id and dic1.dic_type_code = 'non_local_special_partner'
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id = f.from_distributor_id
left join wx_t_region c on c.id = m.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = m.reprot_type and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left JOIN PP_MID.dbo.bi_product bp01 ON bp01.product_code_SAP = b.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.display_channel and d2.dic_type_code  = 'NonLocalSales.displayChannel'
left join wx_t_non_local_mkt_emaile_info ei on ei.source_id=r3.id and ei.delete_flag=0
left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=b.auth_channel
where f.id is not null
and cs1.distributor_id is null and f.from_distributor_id not in (45, -10000)
union all
select b.from_distributor_id, ('RUSAN'+convert(nvarchar(20),b.report_time,112)+convert(nvarchar(20),b.id%10000) ) as '上报ID',
d1.dic_item_name as '上报来源','用户扫码验真' as '上报人',
convert(nvarchar(20),b.report_time,20) as '上报日期',
year(b.report_time) as '上报年',
month(b.report_time) as '上报月',
day(b.report_time) as '上报日',p.region_name as '上报省',
c.region_name as '上报市',
p1.name as '产品名称',
case when not exists (select 1 from wx_t_region prov1 where prov1.region_type='P' and prov1.status=1 
	and exists (select 1 from wx_t_region city1 where city1.status=1 and city1.parent_id=prov1.id)
	and not exists (select 1 from wx_t_region r1 
		join wx_t_region r2 on r2.id=r1.parent_id
        where r2.parent_id=prov1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as "省",
case when not exists (select 1 from wx_t_region city1 where city1.region_type='C' and city1.status=1 
	and not exists (select 1 from wx_t_region r1 
        where r1.parent_id=city1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as "市",
b.sku,
bp01.t3_category 'T3 category',
convert(nvarchar(20),b.out_date,23) "出库日期",
convert(nvarchar(20),b.product_date,23) "生产日期",b.product_batch "生产批号",
        b.out_order_no "出库单号",b.logistics_code "物流码",
d2.dic_item_name as '产品渠道',
'' as '被非本地营销影响经销商',
'' "被非本地营销影响大区",
'' "被非本地营销影响授权渠道",
'' "被非本地营销影响授权省",
'' "被非本地营销影响授权市",
'' "被非本地营销影响FLSR",
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '非本地营销经销商',
cs1.region "非本地营销大区",
diac1.dic_item_name "非本地营销影响授权渠道",
case when not exists (select 1 from wx_t_region prov1 where prov1.region_type='P' and prov1.status=1 
	and exists (select 1 from wx_t_region city1 where city1.status=1 and city1.parent_id=prov1.id)
	and not exists (select 1 from wx_t_region r1 
		join wx_t_region r2 on r2.id=r1.parent_id
        where r2.parent_id=prov1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as '非本地营销影响授权省',
case when not exists (select 1 from wx_t_region city1 where city1.region_type='C' and city1.status=1 
	and not exists (select 1 from wx_t_region r1 
        where r1.parent_id=city1.id and CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0)) then '全国' else (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) end as '非本地营销影响授权市',
cs1.sales_name_cn "非本地营销FLSR",

'' as '被非本地营销影响FLSR是否同意上报',
'不处理' as '判定状态',
'-' as '非本地营销（ABM）',
'' as 'ABM处罚内容',
'-' as 'KPI合格（ABM）',
'' as 'ABM建议',
'-' "处罚文件已确认",
'' "处罚文件发放时间",
'-' as '非本地营销（被非本地营销影响ASM）',
'' as '被非本地营销影响ASM处罚内容',
'-' as 'KPI合格（被非本地营销影响ASM）',
'' as '被非本地营销影响ASM建议',
'-' as '非本地营销（非本地营销ASM）',
'' as '非本地营销ASM处罚内容',
'-' as 'KPI合格（非本地营销ASM）',
'' as '非本地营销ASM建议',
'-' as '非本地营销（非本地营销FLSR）',
'' as '非本地营销FLSR建议'
from wx_t_non_local_user_report b
left join dw_customer_org_sales cs1 on cs1.distributor_id = b.from_distributor_id and cs1.channel_weight&b.auth_channel>0
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = b.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_region c on c.id = b.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = '2' and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left JOIN PP_MID.dbo.bi_product bp01 ON bp01.product_code_SAP = b.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.display_channel and d2.dic_type_code  = 'NonLocalSales.displayChannel'
left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=b.auth_channel
where b.ext_flag&2048>0
and not exists (select 1 from wx_t_loc_mkt_product_info mp1 where mp1.logistics_code=b.logistics_code)
and cs1.distributor_id is null and (b.from_distributor_id is null or b.from_distributor_id not in (45, -10000))
) a group by from_distributor_id,[上报来源]
