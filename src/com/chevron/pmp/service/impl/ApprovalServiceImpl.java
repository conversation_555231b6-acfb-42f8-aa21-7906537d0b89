package com.chevron.pmp.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sys.auth.model.WxTUser;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.pmp.dao.ApprovalInfoVoMapper;
import com.chevron.pmp.model.ApprovalInfoVo;
import com.chevron.pmp.service.ApprovalService;
import com.common.util.ContextUtil;

@Service
public class ApprovalServiceImpl implements ApprovalService {
	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;
	@Resource
	private ApprovalInfoVoMapper approvalInfoVoMapper;

	@Override
	public Map<String, Object> findWorkshopApprovalDetail(Long workshopId) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			map.put("workshop", workshopMasterBizService.getBean(workshopId));
			map.put("approvalLog", approvalInfoVoMapper.selApprovalLogs(1, workshopId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> approval(int sourceFlag, Long sourceId,
			boolean isApproved, String approvalComment, int oldStatus) {
		Map<String, Object> map = new HashMap<String, Object>(1);
		try {
			WxTUser user = ContextUtil.getCurUser();
			ApprovalInfoVo approvalInfoVo = new ApprovalInfoVo();
			approvalInfoVo.setApprovalComment(approvalComment);
			approvalInfoVo.setIsApproved(isApproved ? 1 : 0);
			approvalInfoVo.setOperateTime(new Date());
			approvalInfoVo.setOperator(user.getUserId());
			approvalInfoVo.setSourceFlag(sourceFlag);
			approvalInfoVo.setSourceId(sourceId);
			//门店审批
			if(sourceFlag == 1){
				WorkshopMaster workShopVo = new WorkshopMaster();
				workShopVo.setId(sourceId);
				if(oldStatus == 2){
					//雪佛龙审批
					approvalInfoVo.setFlowStep(String.valueOf(2));
					if(isApproved){
						//审核通过，激活门店
						workShopVo.setStatus(3);
						//更新发起任务状态
						approvalInfoVoMapper.updateTaskSubByApprove(sourceId);
						//设置门店门头照
						List<String> photoIds = approvalInfoVoMapper.selApprovedWorkshopPhotos(sourceId); 
						if(photoIds.size() > 0){
							workShopVo.setPhotoId(photoIds.get(0));
						}
					}else{
						//审核未通过，打回合伙人重新审批
						workShopVo.setStatus(1);
					}
				}else{
					//合伙人审批
					approvalInfoVo.setFlowStep(String.valueOf(1));
					if(isApproved){
						workShopVo.setStatus(2);
					}else{
						//合伙人拒绝
						workShopVo.setStatus(4);
						//更新任务
						approvalInfoVoMapper.updateTaskMainByReject(sourceId);
						approvalInfoVoMapper.updateTaskSubByReject(sourceId, approvalComment);
					}
				}
				//更新门店状态
				workshopMasterBizService.update(workShopVo);
			}
			//插入审批日志
			approvalInfoVoMapper.insertSelective(approvalInfoVo);
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

}
