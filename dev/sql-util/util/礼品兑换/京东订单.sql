="union all select (select distinct pac.POINT_ACCOUNT_OWNER_ID  "&
" from wx_t_point_account pac  "&
 "left join wx_t_point_value_detail pde ON pac.id = pde.POINT_ACCOUNT_ID "&
" LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID "&
" where CHARINDEX('"&F542&"',plog.COMMENTS)>0) pid"

="union all select (select organization_name from wx_t_organization o where o.id="&P542&") partner_name"

="union all select (select distinct pac.POINT_ACCOUNT_OWNER_ID  "&
" from wx_t_point_account pac  "&
 "left join wx_t_point_value_detail pde ON pac.id = pde.POINT_ACCOUNT_ID "&
" LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID "&
" where CHARINDEX('"&F542&"',plog.COMMENTS)>0) pid"

select o.organization_name "经销商名称", p.region_name as "省", c.region_name as '市', d.region_name as '区县',
t.ADDRESS_DETAIL '详细地址'
         from wx_t_material_address_config t
        left join wx_t_region d on d.id = t.ADDRESS_REGION
        left join wx_t_region c on c.id = d.parent_id
        left join wx_t_region p on p.id = c.parent_id
		left join wx_t_organization o on o.id=t.partner_id
		where t.DELETE_FLAG=0 and o.id in (44696)
		order by o.id
		
select * from wx_t_point_business where BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' and attribute1='202208228165243624207-1'		

SELECT o.id, o.organization_name
FROM wx_t_point_account pac
	LEFT JOIN wx_t_point_value_detail pde ON pac.id = pde.POINT_ACCOUNT_ID
	LEFT JOIN dbo.wx_t_point_business pb ON pde.BUSINESS_ID = pb.ID
	left join wx_t_organization o on o.id=pac.POINT_ACCOUNT_OWNER_ID
WHERE pb.business_type_code='PLATFORM_POINT_CONSUME' and pb.attribute1='202206175150134106832-1'