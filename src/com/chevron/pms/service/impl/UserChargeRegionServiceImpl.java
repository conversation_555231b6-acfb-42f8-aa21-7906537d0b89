package com.chevron.pms.service.impl;

import com.chevron.pms.business.UserChargeRegionBizService;
import com.chevron.pms.model.UserChargeRegion;
import com.chevron.pms.service.UserChargeRegionService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户负责区域操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2017-07-03 14:47
 */
@Service
public class UserChargeRegionServiceImpl implements UserChargeRegionService {
	@Autowired
	private UserChargeRegionBizService userChargeRegionBizService;
	
	private final static Logger log = Logger.getLogger(UserChargeRegionServiceImpl.class);
	
	@Override
	public Map<String, Object> save(List<UserChargeRegion> records, Long userId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			userChargeRegionBizService.update(records, userId);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
}
