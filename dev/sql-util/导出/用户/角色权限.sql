="union all select '"&A2&"' role, '"&B2&"' 'fun_list', '"&C2&"' dview"

select * into temp_userrole from () a

select r.role_descript "Role", pm.menu_name + '/' + m.menu_name "Function List", 
(select distinct a.dview from temp_userrole a where a.role=r.role_descript and a.fun_list=pm.menu_name + '/' + m.menu_name) 'Data View'
from wx_t_role r
left join wx_t_menu m on m.status=1 and  exists (select 1 from wx_t_userrole ur left join wx_t_rolesource rs on ur.role_id=rs.role_id 
where rs.role_id=r.role_id
		and rs.rs_type='2' and rs.source_id=m.menu_id)
left join wx_t_menu pm on m.menu_pid=pm.menu_id
where pm.menu_id is not null and pm.status=1 and r.ch_role_name not in ('admin') and r.status=1
order by r.role_id, pm.sort,pm.menu_id, m.sort, m.menu_id
