update wx_t_properties set code='N' where codetype = 'https.enabled';
update wx_t_user set password='f92274ff7c2f4914da79760b2e9ad872ceff0d20', salt='27B157064129F4E5'
update wx_t_user set password='3bb6db69559e8aa0647e9ca3aa9fc2719dd2f5b1' where user_id=1

update wx_t_properties set code = '123456' where codetype = 'o2o_service.enterprisePay.key';

update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8024/api/common/sendnormalredpack' where codetype = 'o2o_service.enterprisePay.url';

update wx_t_properties set code = 'chevron1020' where codetype = 'wechat.getAllEnterprise.key';

update wx_t_properties set code = 'http://**************:19090/enterpriseInfo/getEnterpriseInfo' where codetype = 'wechat.getAllEnterprise.url';

update wx_t_properties set code = 'http://localhost/vehicle/getVehicleInfoByLevelId.do' where codetype = 'vehicle.interface.getVehicleInfoByLevelId.url';

update wx_t_properties set code = 'http://localhost/vehicle/getLevelIdByVinCode.do' where codetype = 'vehicle.interface.getLevelIdByVinCode.url';

update wx_t_properties set code = '123456' where codetype = 'o2o.getWechatTempCode.key';

update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8017/api/QRCode/GetBatchQRCode' where codetype = 'o2o.getWechatTempCode.url';

update wx_t_properties set code = 'http://localhost/vehicle/getRecommendProductsByLevelId.do' where codetype = 'vehicle.interface.getRecommendProductsByLevelId.url';

update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8024/api/common/sendTextMsg' where codetype = 'o2o.service.sendMessage';
update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8024/api/common/departmentlist' where codetype = 'o2o_service.getDepartments.url';

update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8024/api/common/regupdateoffline' where codetype = 'o2o.service.updateRegisterMechanic';
update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8024/api/common/EditSPinfo' where codetype = 'o2o_service.updateSp';

update wx_t_properties set code = 'https://wwwstg.cvx-sh.com' where codetype = 'app.host';

update wx_t_properties set code = '123456' where codetype = 'o2o_service.registerMechanic.key';

update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8024/api/common/registeroffline' where codetype = 'o2o_service.registerMechanic';

update wx_t_properties set code = 'http://wxportalstg.techronworks.cn:8017/api/userinfo' where codetype = 'wx.userinfo.url';

--根据实际情况进行修改

update wx_t_properties set code = 'wwwstg.cvx-sh.com' where codetype = 'application.host';

--根据实际情况进行修改
update wx_t_properties set code = '8080' where codetype = 'application.port';

update wx_t_properties set code = '[dw_mtd_all_order_report]' where codetype = 'MTD.reportView';
update wx_t_properties set code = 'https://scrmbackend-stg.techronworks.cn/api/qrcode/create/chevron_shopping' where codetype = 'Qrcode.DeloTempQrcodeGenerator.url';
update wx_t_properties set code = 'datavaluable' where codetype = 'Qrcode.DeloTempQrcodeGenerator.appId';
update wx_t_properties set code = '[dw_MDB_ERROR_LOG_MTD]' where codetype = 'MTD.view.warning';
update wx_t_properties set code = 'http://zhsh.co/newpage/fac100320/p/query.aspx' where codetype = 'gq_verification_url';
update wx_t_properties set code = 'TKreEeuhzI1lCkDy1JrJex2EABY=' where codetype = 'b2b.srcm.access_secret';
update wx_t_properties set code = 'https://b2bstg.techronworks.cn:8898/mgt-api/gateway.do' where codetype = 'b2b.srcm.syn.mechanic.url';

--关闭设备推送消息
update wx_t_user set receive_msg=0 where device_id is not null and login_name not in ('suntanwei', 'lwh_moyan', 'suntanwei1');


UPDATE wx_t_partner_responsible_main SET day_report_cc = '<EMAIL>' WHERE day_report_cc LIKE '%chevron%';

UPDATE  wx_t_user SET email = '<EMAIL>' WHERE email LIKE '%chevron%'
update wx_t_user set mobile_tel='15683297280' where mobile_tel is not null;
update wx_t_dic_item set dic_item_name='<EMAIL>' where dic_type_code='csr_userid_email';
update wx_t_dic_item set dic_item_desc='<EMAIL>' where dic_type_code='ForecastExport.notifyEmail';
update wx_t_dic_item set dic_item_name='<EMAIL>' where dic_type_code='sprebate.finalemail';
update wx_t_dic_item set dic_item_name='<EMAIL>' where dic_type_code='deliveryCode_statistic_email';

delete from wx_t_dic_item where dic_type_code='pendingPointConfirm.email.CDM' and (dic_item_code like '%chevron%' or dic_item_name like '%chevron%' or dic_item_desc like '%chevron%');
delete from wx_t_dic_item where dic_type_code='pendingPointConfirm.email.C&I' and (dic_item_code like '%chevron%' or dic_item_name like '%chevron%' or dic_item_desc like '%chevron%');

--修改周报邮箱
update wx_t_dic_item set dic_item_desc='<EMAIL>' where dic_type_code in ('task.weekemail.scandetial','task.monthemail.awardclose','task.monthemail.oilverawardclose') and dic_item_code='to';
update wx_t_dic_item set dic_item_desc='<EMAIL>' where dic_type_code in ('task.weekemail.scandetial','task.monthemail.awardclose','task.monthemail.oilverawardclose') and dic_item_code='cc';


UPDATE wx_t_properties SET code = 'http://localhost/vehicle/getRecommendProductsByTModelId.do' WHERE codetype = 'vehicle.interface.getRecommendProductsByTModelId.url';

UPDATE wx_t_properties SET code = 'http://localhost/vehicle/getTModelIdByVinCode.do' WHERE codetype = 'vehicle.interface.getTModelIdByVinCode.url';

UPDATE wx_t_properties SET code = 'http://localhost/vehicle/getVehicleInfoByTModelId.do' WHERE codetype = 'vehicle.interface.getVehicleInfoByTModelId.url';
-- 物料下单之礼享网集成配置
UPDATE wx_t_properties SET code = 'http://enjoygifts.zhong-tech.com/commodity/' WHERE codetype = 'smartComm.service.host';
UPDATE wx_t_properties SET code = 'http://staticenjoygifts.zhong-tech.com' WHERE codetype = 'smartComm.image.host';
UPDATE wx_t_properties SET code = 'chevrontest' WHERE codetype = 'smartComm.service.username';
UPDATE wx_t_properties SET code = '654321' WHERE codetype = 'smartComm.service.password';

UPDATE wx_t_properties SET code = '1273266517' WHERE codetype = 'Umeng.id';
UPDATE wx_t_properties SET code = '**********' WHERE codetype = 'ReportCenter.sftp.host';
UPDATE wx_t_properties SET code = 'project' WHERE codetype = 'ReportCenter.sftp.userName';
UPDATE wx_t_properties SET code = '&zo0tcn8J%x9#$Mj' WHERE codetype = 'ReportCenter.sftp.password';
UPDATE wx_t_properties SET code = '/data/chevron/excelexport' WHERE codetype = 'ReportCenter.rootPath';

--修改定时任务状态

UPDATE task_schedule_job SET job_status = 0;

delete from qrtz_fired_triggers;  
delete from qrtz_simple_triggers;  
delete from qrtz_simprop_triggers;  
delete from qrtz_cron_triggers;  
delete from qrtz_blob_triggers;  
delete from qrtz_triggers;  
delete from qrtz_job_details;  
delete from qrtz_calendars;  
delete from qrtz_paused_trigger_grps;  
delete from qrtz_locks;  
delete from qrtz_scheduler_state;



--更新sftp服务器信息
UPDATE wx_t_properties SET code='svc-imois-mudan-test'
WHERE codetype='haorder.sftp.user'
UPDATE wx_t_properties SET code='ANjnerW5' WHERE codetype='haorder.sftp.pwd'
--更新属性表中的邮件配置信息
UPDATE wx_t_properties SET code='<EMAIL>' WHERE id IN(
SELECT id FROM wx_t_properties WHERE code LIKE '%@%'
) AND id !=10129
--更新用户表中的邮件信息
UPDATE  wx_t_user SET email='<EMAIL>' WHERE email IS NOT NULL AND email!=''
--更新预约服务订单的邮箱配置信息
update wx_t_order_yyfw_sets  SET email_fw_accepter='<EMAIL>' ,email_fw_cc='<EMAIL>'
update wx_t_order_yyfw_sets  SET fw_order_email_by_region ='浙江省:<EMAIL>,<EMAIL>;辽宁省:<EMAIL>;黑龙江省:<EMAIL>' WHERE source_name='大地保险'

update t set dic_item_name='<EMAIL>' from wx_t_dic_item t where dic_item_name like '%@chevron.com%'
update t set dic_item_desc='<EMAIL>' from wx_t_dic_item t where dic_item_desc like '%@chevron.com%'
update t set dic_item_code='<EMAIL>' from wx_t_dic_item t where dic_item_code like '%@chevron.com%'
update t set code='<EMAIL>' from wx_t_properties t where code like '%@chevron.com%'
update t set codename='<EMAIL>' from wx_t_properties t where codename like '%@chevron.com%'

update wx_t_workflow_step set todo_email_cc='<EMAIL>' where todo_email_cc is not null and todo_email_cc != ''
update wx_t_workflow_step set reject_email_cc='<EMAIL>' where reject_email_cc is not null and reject_email_cc != ''
update wx_t_workflow_step set pre_recall_email_cc='<EMAIL>' where pre_recall_email_cc is not null and pre_recall_email_cc != ''
update wx_t_workflow set end_email_cc='<EMAIL>' where end_email_cc is not null and end_email_cc != ''

update wx_t_properties set code = 'https://wwwstg1.cvx-sh.com' where codetype = 'app.host';
update wx_t_properties set code = 'wwwstg1.cvx-sh.com' where codetype = 'application.host';
update wx_t_properties set code = 'false' where codetype = 'is_product';

update w set w.is_has_product=0 from wx_t_workshop_employee w

update p set p.code='pjel6vonkfyao0l' from wx_t_properties p where codetype='LoginSign.PP'
update wx_t_properties set code='N' where codetype='Task.synB2bMechnicOn'

--修改供应商邮箱 卢文卿
update t set dic_item_name='<EMAIL>' from wx_t_dic_item t where dic_item_name like '%@umiceworld.com%'
update t set dic_item_desc='<EMAIL>' from wx_t_dic_item t where dic_item_desc like '%@umiceworld.com%'
update t set dic_item_code='<EMAIL>' from wx_t_dic_item t where dic_item_code like '%@umiceworld.com%'
update t set code='<EMAIL>' from wx_t_properties t where code like '%@umiceworld.com%'
update t set codename='<EMAIL>' from wx_t_properties t where codename like '%@umiceworld.com%'

--eto
update wx_t_properties set code='208' where codetype = 'Eto.organization_id';
update wx_t_properties set code='http://ecrm-test.woaap.com/oauth/token' where codetype = 'Eto.token.url';
update wx_t_properties set code='100001' where codetype = 'Eto.token.client_id';
update wx_t_properties set code='FprJqoOYHyynB4Q0fje6DrrCcKZWtvclAJDl7RXc' where codetype = 'Eto.token.client_secret';
update wx_t_properties set code='client_credentials' where codetype = 'Eto.token.grant_type';
update wx_t_properties set code='*' where codetype = 'Eto.token.scope';
update wx_t_properties set code='http://ecrm-test.woaap.com/public-api/company/save' where codetype = 'Eto.synCompany.url';
update wx_t_properties set code='http://ecrm-test.woaap.com/public-api/shop/sync' where codetype = 'Eto.synWorkshop.url';
update wx_t_properties set code='110000' where codetype = 'Eto.synWorkshop.province';
update wx_t_properties set code='111000' where codetype = 'Eto.synWorkshop.city';
update wx_t_properties set code='110101' where codetype = 'Eto.synWorkshop.district';
update wx_t_properties set code='127' where codetype = 'Eto.synWorkshop.brand_id';
update wx_t_properties set code='雪佛龙' where codetype = 'Eto.synWorkshop.brand_name';
update wx_t_properties set code='http://ecrm-test.woaap.com/public-api/shop/delete' where codetype = 'Eto.delWorkshop.url';
update wx_t_properties set code='http://ecrm-test.woaap.com/public-api/member/register' where codetype = 'Eto.registerWorkshopEmployee.url';
update wx_t_properties set code='http://ecrm-test.woaap.com/public-api/member/update' where codetype = 'Eto.updateWorkshopEmployee.url';
update wx_t_properties set code='http://eto-midtest-chevron.woaap.com/api/order/index?service_name=RefundClosed' where codetype = 'Eto.orderRefund.url';
update wx_t_properties set code='0B81C91A859ABF1D8D071FA45FE7D695' where codetype = 'Eto.appkey';
update wx_t_properties set code='32C86B1FCC1D0051CC67161F73E86D09' where codetype = 'Eto.appsecret';
update wx_t_properties set code='http://eto-midtest-chevron.woaap.com/api/product/index?service_name=Create' where codetype = 'Eto.synProduct.url';

update wx_t_properties set code='https://chevron-campagin-test.woaap.com/api/chevron/qrcode' where codetype = 'Qrcode.EtoTempQrcodeGenerator.url';
update wx_t_properties set code='LmJa?>D1A}{]m' where codetype = 'Qrcode.EtoTempQrcodeGenerator.key';
update wx_t_properties set code='bb7b938d901d3676e48b78c774d6dc5a' where codetype = 'Qrcode.EtoTempQrcodeGenerator.appid.version3';
update wx_t_properties set code='wx057aea5734018491' where codetype = 'Qrcode.Eto.wechatAppId';
update wx_t_properties set code='http://api-test.woaap.com/api/ackey' where codetype = 'Eto.ackey.url';
update wx_t_properties set code='8f3b889237e6ae4bf6d1bca0964eedb7' where codetype = 'Eto.ackey.appid';
update wx_t_properties set code='d9a6dd14fcc89922b216fde44e0f1247' where codetype = 'Eto.ackey.appkey';
update wx_t_properties set code='http://api-test.woaap.com/api/jsticket' where codetype = 'Eto.apiticket.url';
update wx_t_properties set code='http://api-test.woaap.com/api/message-template-send' where codetype = 'Eto.wechatMessage.url';
update wx_t_properties set code='XyPuGFyr65oXZ5nqOocLXTbaCy1crD3M7qt3-gS3VPs' where codetype = 'Eto.wechatMessage.templateId';
update wx_t_properties set code='http://api-test.woaap.com/api/userinfo?lang=zh_CN' where codetype = 'Eto.getUserInfoByOpenId.url';
delete from wx_t_dic_item where dic_type_code='Eto.wechat.alertUser'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Eto.wechat.alertUser', 'o-7tPt2n_Kw5muGzQsyqKnQEjc4M', '李镇涛', '', '1', 1);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Eto.wechat.alertUser', 'o-7tPtxu5beTyrPqG8b5-Wp3g_fQ', '刘万华', '', '1', 1);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Eto.wechat.alertUser', 'o-7tPt59AIWtDRWcCLeiUF01KhF0', '曾亮', '', '1', 1);

delete wx_t_properties where codetype='MaterialPlatform.JD.notifyUserUpdate.on';
delete wx_t_properties where codetype='MaterialPlatform.SmartComm.notifyUserUpdate.on';

update wx_t_properties set code='https://sit.bmsw.cywlfw.com:442/pp/login' where codetype = 'MaterialPlatform.JD.accessUrl';
update wx_t_properties set code='https://sit.bmsw.cywlfw.com:442/pp/user' where codetype = 'MaterialPlatform.JD.notifyUserUpdate.url';
update wx_t_properties set code='https://sit.bmsw.cywlfw.com:442/pp/distributor' where codetype = 'MaterialPlatform.JD.notifyDistributorUpdate.url';

update wx_t_properties set code='https://uat.enjoygifts.cn/we-chat-interface/credit!login.action' where codetype = 'MaterialPlatform.SmartComm.accessUrl';
update wx_t_properties set code='https://uat.enjoygifts.cn/we-chat-interface/credit!userStatus.action' where codetype = 'MaterialPlatform.SmartComm.notifyUserUpdate.url';

update p set p.code=REPLACE(p.code,':8089/',':8088/')
from wx_t_properties p where codetype like 'bpm.%'

update wx_t_user set ext_flag=ext_flag|1 where status=1