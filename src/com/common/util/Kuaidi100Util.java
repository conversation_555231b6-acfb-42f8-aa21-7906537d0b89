package com.common.util;

import com.alibaba.fastjson.JSON;
import com.common.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.*;

public class Kuaidi100Util {
	private static Logger log = LoggerFactory.getLogger(Kuaidi100Util.class);
//	private static final String CUSTOMER = "AC9DE3920CC6D6AFA293D0822408DA7C";
//	private static final String KEY = "zjBWJeWw3295";
	private static final char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
			'a', 'b', 'c', 'd', 'e', 'f' };

	private static final String AUTO_NUMBER_URL = "http://www.kuaidi100.com/autonumber/auto?num=%s&key=%s";
    private static final String QUERY_URL = "http://poll.kuaidi100.com/poll/query.do";
//    {
//        "message": "ok",
//            "nu": "75115713941923",
//            "ischeck": "1",
//            "condition": "F00",
//            "com": "zhongtong",
//            "status": "200",
//            "state": "3",
//            "data": [
//        {
//            "time": "2018-12-14 19:50:30",
//                "ftime": "2018-12-14 19:50:30",
//                "context": "【重庆市】 快件已在 【沙坪坝一部】 签收, 签收人: 菜鸟, 如有疑问请电联:13508319852 / 023-65388675, 您的快递已经妥投, 如果您对我们的服务感到满意, 请给个五星好评, 鼓励一下我们【请在评价快递员处帮忙点亮五颗星星哦~】"
//        }]

    public static class KuaidiQueryContext {
        private String time;
        private String ftime;
        private String context;

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getFtime() {
            return ftime;
        }

        public void setFtime(String ftime) {
            this.ftime = ftime;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }
    }
    public static class KuaidiQueryResult {
        private String message;
        private String nu;
        private String ischeck;
        private String condition;
        private String com;
        private String status;
        private String state;
        private List<KuaidiQueryContext> data = new ArrayList<KuaidiQueryContext>();

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getNu() {
            return nu;
        }

        public void setNu(String nu) {
            this.nu = nu;
        }

        public String getIscheck() {
            return ischeck;
        }

        public void setIscheck(String ischeck) {
            this.ischeck = ischeck;
        }

        public String getCondition() {
            return condition;
        }

        public void setCondition(String condition) {
            this.condition = condition;
        }

        public String getCom() {
            return com;
        }

        public void setCom(String com) {
            this.com = com;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public List<KuaidiQueryContext> getData() {
            return data;
        }

        public void setData(List<KuaidiQueryContext> data) {
            this.data = data;
        }

        public KuaidiQueryResult() {
        }

        public KuaidiQueryResult(String message, String nu, String ischeck, String condition, String com, String status, String state, List<KuaidiQueryContext> data) {
            this.message = message;
            this.nu = nu;
            this.ischeck = ischeck;
            this.condition = condition;
            this.com = com;
            this.status = status;
            this.state = state;
            this.data = data;
        }
    }
    /**
     * 类似下面的报文
     * [
     *   {
     *     "comCode": "baishiwuliu",
     *     "id": "",
     *     "noCount": 4,
     *     "noPre": "751157",
     *     "startTime": ""
     *   }
     * ]
     */
	public static class AutoResult {
	    private String comCode;
	    private String id;
	    private Long noCount;
	    private String noPre;
	    private String startTime;

        public String getComCode() {
            return comCode;
        }

        public void setComCode(String comCode) {
            this.comCode = comCode;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Long getNoCount() {
            return noCount;
        }

        public void setNoCount(Long noCount) {
            this.noCount = noCount;
        }

        public String getNoPre() {
            return noPre;
        }

        public void setNoPre(String noPre) {
            this.noPre = noPre;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }
    }

	public static KuaidiQueryResult kuaidiQuery(String num,String phone){
        String customer = (String)Constants.getSystemPropertyByCodeType(Constants.KUAIDI100_CUSTOMER);//这是申请下来的公钥
        String key = (String)Constants.getSystemPropertyByCodeType(Constants.KUAIDI100_KEY);//这是申请下来的私钥
		//微信异常报警服务路径
        KuaidiQueryResult kuaidiQueryResult = null;

        List<AutoResult> autoResultList = null;
        String autoNumberUrl = String.format(AUTO_NUMBER_URL,num,key);
        String autoNumResult = HttpSender.get(autoNumberUrl,null);
        if(!StringUtils.isEmpty(autoNumResult)){
            autoResultList = JSON.parseArray(autoNumResult,AutoResult.class);
        }
        if(autoResultList!=null && autoResultList.size() > 0){
            for (AutoResult autoResult:autoResultList){
                String com = autoResult.getComCode();
                String param ="{\"com\":\""+ com +"\",\"num\":\""+ num +"\"";//com 快递公司标识，num:快递单号
                // 如果是shunfeng的需要增加phone字段
                if("shunfeng".equals(com) && StringUtils.isNotBlank(phone)){
                    // 根据快递单号查询对应的联系电话
//                    String phone = "13823212342";
                    // 查找这个订单号对应的application
                    param += ",\"phone\":\""+ phone +"\"";
                }
                param += "}";
                String sign = MD5.MD5Encode(param + key + customer).toUpperCase();//md5加密
                HashMap<String, Object> params = new HashMap<String, Object>();
                params.put("param",param);
                params.put("sign",sign);
                params.put("customer",customer);

                try {
                    String serviceUrl = QUERY_URL + "?param=" + URLEncoder.encode(param, "utf-8") + "&sign="+sign + "&customer="+customer;
                    String respStr =  HttpSender.postJSON(serviceUrl,params,null);
                    kuaidiQueryResult = JSON.parseObject(respStr,KuaidiQueryResult.class);
                    if(kuaidiQueryResult!=null && "ok".equals(kuaidiQueryResult.getMessage())){
                            break;
                    } else {
                        continue;
                    }
                } catch (Exception e) {
                    kuaidiQueryResult = null;
                    log.error(e.getMessage(),e);
                }
            }
        }

        if(kuaidiQueryResult == null){
            kuaidiQueryResult = new KuaidiQueryResult();
            kuaidiQueryResult.setMessage("不支持此快递公司查询。");
        }

        return kuaidiQueryResult;
	}



	public final static String encode(String s) {
		try {
			MessageDigest mdInst = MessageDigest.getInstance("MD5");
			byte[] btInput = s.getBytes();
			// 使用指定的字节更新摘要
			mdInst.update(btInput);
			// 获得密文
			byte[] md = mdInst.digest();
			// 把密文转换成十六进制的字符串形式
			int j = md.length;
			char str[] = new char[j * 2];
			int k = 0;
			for (int i = 0; i < j; i++) {
				byte byte0 = md[i];
				str[k++] = hexDigits[byte0 >>> 4 & 0xf];
				str[k++] = hexDigits[byte0 & 0xf];
			}
			return new String(str);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

}
