select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --delete wiel1
			 --update wiel1 set wiel1.actual_executor=131012
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 left join wx_t_v2_elite_collect_form f on f.id=wi1.form_key
		 left join wx_t_workflow_step_history wsh on wsh.step_instance_id=wsi1.step_instance_id
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='ELITE_COLLECT_V2020' 
		 and f.apply_phase='q1q2'
		 and wiel1.actual_executor=131747

select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --delete wiel1
			 --update wsh set wsh.actual_executor=131747,executor=131747
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 left join wx_t_v2_elite_collect_form f on f.id=wi1.form_key
		 left join wx_t_workflow_step_history wsh on wsh.step_instance_id=wsi1.step_instance_id
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='ELITE_COLLECT_V2020' 
		 and f.apply_phase='q1q2'
		 and wsh.actual_executor=131012		 