;(function($){
	//计算记录汇总数据
	var calculateSummary = function(records, grid){
		var sumRecord = {};
		for(var i = 0; i < grid.columns.length; i++){
			var col = grid.columns[i];
			if(col.summary){
				calculateColSummary(sumRecord, col, records);
			}
		}
		return sumRecord;
	};
	//计算列合计
	var calculateColSummary = function(summaryRecord, col, records){
		summaryRecord[col.property] = null;
		for(var j = 0; j < records.length; j++){
			var v = col.propertyProcessor(records[j], col.property);
			if(v === 0 || v){
				summaryRecord[col.property] = (summaryRecord[col.property] || 0) + (v - 0);
			}
		}
	};
	//用排序二叉树构建数据数组
	var buildList = function(list, node){
		if(node._leftNode){
			buildList(list, node._leftNode);
		}
		list.push(node);
		if(node._rightNode){
			buildList(list, node._rightNode);
		}
	};

	//计算总页数
	var calculateTotalPage = function(totalSize, pageSize){
		if(totalSize == 0){
			return 1;
		}
		return totalSize % pageSize == 0 ? totalSize / pageSize : (Math.floor(totalSize / pageSize) + 1);
	};
	
	//分页处理
	var pagingProcess = function(records, store){
		var i = (store.params.pageNo - 1) * store.grid.paging.pageSize, 
			endIndex = Math.min(i + store.grid.paging.pageSize, records.length); r = [];
		for(; i < endIndex; i++){
			r.push(records[i]);
		}
		return r;
	};

	UI.Store = UI.extend(UI.Base, {
		SORT_DERICTIONS: [null, 'ASC', 'DESC'],
		configEvents: ['beforeload', 'afterload'],
		init: function(opts){
			var instance = this.newInstance(opts);
			//初始化排序
			if(instance.sortable){
				var ps = [], ds = [];
				if(instance.sortProperty){
					var _ps = instance.sortProperty.split(','), _ds = null;
					if(instance.sortDirection){
						_ds = instance.sortDirection.split(',');
					}
					for(var i = 0; i < _ps.length; i++){
						ps.push(_ps[i]);
						ds.push(_ds[i]);
					}
				}else if(instance.sortProperties){
					for(var i = 0; i < instance.sortProperties.length; i++){
						ps.push(instance.sortProperties[i]);
						ds.push((instance.sortDirections && instance.sortDirections[i] && instance.sortDirections[i].toUpperCase() == 'DESC') ? 'DESC' : 'ASC');
					}
				}
				instance.sortProperties = ps;
				instance.sortDirections = ds;
			}
			//初始化数据仓库的数据处理器
			if(instance.url){
				instance._dataHandler = this.dataHandlers.ajax;
			}else{
				instance._dataHandler = this.dataHandlers.local;
			}
			instance._dataHandler.init(instance);
			//扩展数据处理器默认属性
			if(instance._dataHandler.defaults){
				instance = $.extend({}, instance._dataHandler.defaults, instance);
			}
			//扩展数据处理器接口
			if(instance._dataHandler.base){
				instance = $.extend(instance, instance._dataHandler.base);
			}
			if(!instance.params){
				instance.params = {};
			}
			return instance;
		},
		dataHandlers: {
			ajax: {
				init: function(store){
					//初始化数据仓库分页处理器
//					if(instance.paging){
//						instance._pagingHandler = this.dataHandlers.paging;
//					}else{
//						instance._dataHandler = this.dataHandlers.unpaging;
//					}
				},
				base: {
					//表格结果集排序
					sort: function(grid, data, filter, searchText){
					},
					//刷新表格
					load: function(params){
						if(params){
							this.params = $.extend(this.params, params);
						}
						if(this.grid.paging){
							this.clazz.dataHandlers.ajax.pagingProcessBeforeRequest(this);
						}
						this.trigger('beforeload', params);
						this.clazz.dataHandlers.ajax.request(this);
					},
					//刷新列，重新计算列合计
					refreshCols: function(cols){
						throw '后端分页不支持刷新列';
					}
				},
				defaults: {
					url: null,
					method: 'post',
					totalProperty: 'total',
					recordsProperty: 'resultLst',
					errorMsgPrefix: '数据加载失败。',
					summaryProperty: 'summary'
				},
				pagingProcessBeforeRequest: function(instance){
					var pageNo = instance.params.pageNo || instance.grid.paging.pageNo;
					instance.params.limit = instance.grid.paging.pageSize;
					instance.params.start = instance.grid.paging.pageSize * (pageNo - 1);
				},
				pagingProcessAfterRequest: function(instance, result){
					var totalCount = result[instance.totalProperty], totalPage = calculateTotalPage(totalCount, instance.grid.paging.pageSize);
					instance.params.pageNo = Math.min(instance.params.pageNo, totalPage);
					instance.grid.paging.update(totalCount, instance.params.pageNo, totalPage, instance.grid);
				},
				request: function(instance){
					var self = this;
					if(instance.method == 'post'){
						$.post(instance.url, instance.params, function(result){
							self.afterRequestProcess(result, instance);
						}, 'json');
					}else{
						$.getJson(instance.url, instance.params, function(result){
							self.afterRequestProcess(result, instance);
						});
					}
				},
				afterRequestProcess: function(result, instance){
					if(this.validateResponse(result, instance) !== false){
						var data = {
								records: result[instance.recordsProperty]
						};
						if(instance.grid.hasSummary){
							data.summary = result[instance.summaryProperty];
						}
						if(instance.grid.paging){
							this.pagingProcessAfterRequest(instance, result);
						}
						instance.trigger('afterload', data);
						instance.grid.renderData(data);
						instance.trigger('aftershow');
					}
				},
				validateResponse: function(result, instance){
					if(result.code == 'success'){
						return true;
					}else{
						UI.alertError(instance.errorMsgPrefix + result.errorMsg)
						return false;
					}
				}
			},
			local: {
				init: function(store){
					//初始化数据仓库分页处理器
//					if(instance.paging){
//						instance._pagingHandler = this.dataHandlers.paging;
//					}else{
//						instance._dataHandler = this.dataHandlers.unpaging;
//					}
				},
				base: {
					//设置表格结果集
					setResult: function(data){
						this.data = data;
						this._summaryRecord = null;
						this.trigger('afterloaddata', this.data);
						this.load({pageNumb: 1});
					},
					//全局搜索
					search: function(searchText){
						this._searchText = searchText;
						this.load({pageNumb: 1});
					},
					//表格结果集排序
					sort: function(grid, data, filter, searchText){
//						if(data && data.length > 0){
//							var newData = [], len = data.length, root = null;
//							for(var i = 0; i < len; i++){
//								var item = data[i];
//								if(filter && filter.call(grid, item, searchText)){
//									//初始化二叉树链接
//									item._leftNode = item._rightNode = null;
//									if(root == null){
//										root = data[i];
//									}else{
//										root = buildBTree(root, data[i]);
//									}
//								}
//							}
//							buildList(newData, root);
//							return newData;
//						}
//						return data;
					},
					//刷新表格
					load: function(params){
						if(params){
							this.params = $.extend(this.params, params);
						}
						this.trigger('beforeload', params);
						var records = this.data;
						var data = {};
						if(records && records.length > 0){
							//TODO 分页排序处理
							//						grid._data = this.sort(grid, grid.data, grid._searchText ? grid.filter : null, grid._searchText);
							//TODO 排序处理
							//分页处理
							if(this.grid.paging){
								this.params.pageNo = this.params.pageNo || this.grid.paging.pageNo;
								var totalPage = calculateTotalPage(records.length, this.grid.paging.pageSize);
								this.params.pageNo = Math.min(this.params.pageNo, totalPage);
								records = pagingProcess(records, this);
								this.grid.paging.update(this.data.length, this.params.pageNo, totalPage, this.grid);
							}
							data.records = records;
							if(this.grid.hasSummary){
								if(this._summaryRecord){
									data.summary = this._summaryRecord;
								}else{
									this._summaryRecord = data.summary = calculateSummary(this.data, this.grid);
								}
							}
						}
//						grid.trigger('_datachange', grid._data);
						this.grid.renderData(data);
//						grid._dataHandler.renderData(grid, grid._data, params);
						this.trigger('afterload');
					},
					//刷新列，重新计算列合计
					refreshCols: function(cols){
						if(this._summaryRecord){
							for(var i = 0; i < cols.length; i++){
								var col = cols[i];
								if(col.summary){
									calculateColSummary(this._summaryRecord, col, this.data);
								}
							}
						}
					}
				}
			}
		},
		base: {
			bindGrid: function(grid){
				this.grid = grid;
			},
			refresh: function(){
				this.load();
			},
			load: function(params){
				throw '为实现load方法';
			}
		},
		defaults: {
			sortProperties: null, //排序属性集合
			sortDirections: null, //排序方向集合。ASC,DESC
			sortProperty: null, //排序属性集合，逗号分隔
			sortDirection: null, //排序方向集合，逗号分隔。ASC,DESC
			multipleSort: false, //是否多列排序
			sortable: false, //是否排序
			autoLoad: true //自动加载数据
		}
	});
})(jQuery);