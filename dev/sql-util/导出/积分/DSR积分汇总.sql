--select sum(point_paid_actual_mtd) from  dbo.wx_t_v2_dsr_sc_grant_history;
SELECT sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end) as "总发放", 
sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
sum(pde.POINT_VALUE-pde.POINT_PAYED) "总剩余"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on u.user_id = pde.CREATED_BY
        WHERE pde.point_type='DSR_CIO_POINT'
		having sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)>0		
		
SELECT o.organization_name "经销商名称", u.login_name "登录名", u.ch_name "姓名", 
sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end) as "总发放", 
sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
sum(pde.POINT_VALUE-pde.POINT_PAYED) "总剩余"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        --left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on u.user_id = pac.POINT_ACCOUNT_OWNER_ID
		left join wx_t_organization o on o.id=u.org_id
        WHERE pde.point_type='DSR_CIO_POINT'
		group by o.organization_name, u.login_name, u.ch_name
		having sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)>0	
		
SELECT o.organization_name "经销商名称", u.login_name "登录名", u.ch_name "姓名", 
pde.POINT_VALUE "发放", 
CONVERT(varchar(7), pde.TRANS_TIME, 23) "月份"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        --left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on u.user_id = pac.POINT_ACCOUNT_OWNER_ID
		left join wx_t_organization o on o.id=u.org_id
        WHERE pde.point_type='DSR_CIO_POINT' and bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' and plog.CREATION_TIME>'2021-05-01'
		order by o.id, u.user_id, pde.TRANS_TIME
		
SELECT o.organization_name "经销商名称", u.login_name "登录名", u.ch_name "姓名", 
sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end) as "总发放", 
sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
sum(pde.POINT_VALUE-pde.POINT_PAYED) "总剩余"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        --left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on u.user_id = pac.POINT_ACCOUNT_OWNER_ID
		left join wx_t_organization o on o.id=u.org_id
        WHERE pde.point_type='DSR_CIO_POINT' and u.status=1 and o.id!=9
		group by o.organization_name, u.login_name, u.ch_name, o.id
		having sum(pde.POINT_VALUE-pde.POINT_PAYED)>0
		order by o.id			
		