/****** <PERSON><PERSON><PERSON> for SelectTopNRows command from SSMS  ******/
SELECT C.[distributor_id]
	  , o.id
      ,max([customer_name_cn]) [customer_name_cn]
      ,[company_code]
      ,max([customer_type]) [customer_type] 
	  ,max(pe.partner_property) partner_property
  FROM [PP_MID].[dbo].[syn_dw_to_pp_customer] c
  left join wx_t_partner_o2o_enterprise pe on c.distributor_id=pe.distributor_id
  left join wx_t_organization o on o.id=pe.partner_id
  where pe.partner_property='NORMAL' and o.status=1
  group by C.[distributor_id],[company_code],  o.id
  order by o.id