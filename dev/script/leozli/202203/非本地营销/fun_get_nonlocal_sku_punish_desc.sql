create FUNCTION [dbo].[fun_get_nonlocal_sku_punish_desc] 
(   
@punish_value nvarchar(4000),
@punish_year int
)  
RETURNS nvarchar(4000)  
AS  
BEGIN  
	DECLARE @start_idx int
	DECLARE @idx int
	DECLARE @desc nvarchar(4000) 
	SET @start_idx=1 
	SET @desc=''
	SET @idx=CHARINDEX(',', @punish_value, @start_idx)
	WHILE @idx>@start_idx
	BEGIN
		SET @desc=@desc + '扣除'+convert(varchar(20), @punish_year) + '年' + SUBSTRING(@punish_value, @start_idx, @idx - @start_idx) + '全年进货量的'
		SET @start_idx = @idx + 1
		SET @idx=CHARINDEX(';', @punish_value, @start_idx)
		IF @idx < 1
			SET @idx = 4000
		SET @desc=@desc + SUBSTRING(@punish_value, @start_idx, @idx - @start_idx) + '%；'
		SET @start_idx = @idx + 1
		SET @idx=CHARINDEX(',', @punish_value, @start_idx)
	END 
   RETURN @desc  
END  

--select dbo.[fun_get_nonlocal_sku_punish_desc]('SKU1,5;SKU2,10;', 2022)