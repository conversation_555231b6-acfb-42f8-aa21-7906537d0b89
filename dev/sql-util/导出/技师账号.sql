SELECT v.region "Region", v.sales_name_cn "FLSR", o.organization_name AS "所属经销商",
isnull(u.ch_name,(select top 1 u1.ch_name from wx_task_sub s
LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
left join wx_t_user u1 on s.exec_user=u1.user_id
where m.tmb_type_code in ('TT_2_MR', 'TT_2_SD', 'TT_2_XD') and w.id=s.org_id and s.task_status='4' order by s.xg_sj desc)) "DSR"
	, CASE 
		WHEN we.employee_type = 'Owner' THEN '店长'
		ELSE '技师'
	END AS "会员属性（店长/技师）", we.name AS "姓名", we.mobile AS "手机号", w.work_shop_name AS "门店名称", w.id "门店ID"
	, isnull(we.update_time, we.creation_time) AS "注册绑定时间", we.creation_time "创建时间", p.qrcode_point AS "瓶盖积分"
	, p.activity_point AS "活动积分", p.task_point AS "任务积分", p.register_point AS "注册奖励积分", p.total_import_point AS "积分合计",
	p.remain_point "剩余积分"
FROM wx_t_workshop_employee we
	LEFT JOIN wx_t_work_shop w ON w.id = we.workshop_id
	left join wx_t_user u on w.excute_user_id=u.user_id
	LEFT JOIN wx_t_workshop_partner wp ON wp.workshop_id = we.workshop_id
	LEFT JOIN wx_t_organization o ON wp.partner_id = o.id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales v on v.distributor_id=pe.distributor_id and v.product_channel='Consumer'
	LEFT JOIN (
		SELECT SUM((case when pb.BUSINESS_TYPE_CODE = 'POINT_IMPORT' then pvd.POINT_VALUE else 0 end)) AS total_import_point,  SUM(pvd.POINT_VALUE-pvd.POINT_PAYED) AS remain_point, SUM(CASE 
				WHEN pb.EARN_TYPE = 'QR_CODE' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS qrcode_point
			, SUM(CASE 
				WHEN pb.EARN_TYPE = 'MISSION_REWARD' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS task_point, SUM(CASE 
				WHEN pb.EARN_TYPE = 'ACTIVITY_REWARD' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS activity_point
			, SUM(CASE 
				WHEN pb.EARN_TYPE = 'MECHANIC_REGISTER' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS register_point, em.code, em.name
			, em.workshop_id, em.workshop_name
			, pvd.SUB_TYPE
		FROM dbo.wx_t_point_value_detail pvd
			LEFT JOIN dbo.wx_t_point_business pb ON pb.id = pvd.BUSINESS_ID
			LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
			LEFT JOIN dbo.wx_t_workshop_employee em ON em.code = pa.POINT_ACCOUNT_OWNER_CODE
			LEFT JOIN dbo.wx_t_work_shop shop ON shop.id = em.workshop_id
		WHERE 1 = 1
			AND pvd.POINT_TYPE = 'B2B_POINT'
			AND em.code IS NOT NULL
		GROUP BY em.code, em.workshop_id, em.workshop_name, em.name, pvd.SUB_TYPE
	) p
	ON we.code = p.code
WHERE we.version_no = 1 and o.id!=9
ORDER BY v.region,v.sales_cai,o.id,u.user_id, w.id, we.employee_type DESC


SELECT v.region "Region", v.sales_name_cn "FLSR", o.organization_name AS "所属经销商",
isnull(u.ch_name,(select top 1 u1.ch_name from wx_task_sub s
LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
left join wx_t_user u1 on s.exec_user=u1.user_id
where m.tmb_type_code in ('TT_2_MR', 'TT_2_SD', 'TT_2_XD') and w.id=s.org_id and s.task_status='4' order by s.xg_sj desc)) "DSR"
	, (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='WorkshopEmployee.employeeType' and di1.dic_item_code=we.employee_type) AS "员工类型", we.name AS "姓名", we.mobile AS "手机号", w.work_shop_name AS "门店名称", w.id "门店ID"
	, isnull(we.update_time, we.creation_time) AS "最后更新时间", we.creation_time "创建时间"
FROM wx_t_workshop_employee we
	LEFT JOIN wx_t_work_shop w ON w.id = we.workshop_id
	left join wx_t_user u on w.excute_user_id=u.user_id
	LEFT JOIN wx_t_workshop_partner wp ON wp.workshop_id = we.workshop_id
	LEFT JOIN wx_t_organization o ON wp.partner_id = o.id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales v on v.distributor_id=pe.distributor_id and v.product_channel='Consumer'
WHERE w.delete_flag=0--we.version_no = 1 and o.id!=9
ORDER BY v.region,v.sales_cai,o.id,u.user_id, w.id, we.employee_type DESC