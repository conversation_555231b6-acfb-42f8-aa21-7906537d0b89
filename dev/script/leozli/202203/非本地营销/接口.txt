1. 串货查询(手动录入物流码查询接口；APP上报替换/FleeingGoods/checkCode.do?mCode=102926120146)
/mainlocalmarketing/qurynonlocalsale.do?originalCode=102926120146&reportType=1
参数：
originalCode：扫码code
reportType：上报类型 1-APP上报，3-手动录入
返回：
{
    "__version": "1.1",
    "code": "success",
    "data": {
        "id": null,
        "reportId": null,
        "flowId": null,
        "fromDistributorId": -10000, //窜货经销商
        "productName": "银德乐多级机油 15W40 6X4 L", //产品名称
        "sku": "500549LMB", 
        "reportingRegionId": null,
        "scanTime": 1528422001000, //出库时间
        "productTime": 1564934400000, //生产日期
        "productBatch": "CN9G301525", //生产批次
        "productOutNo": "220939883", //出库单号
        "logisticsCode": "102926120146", //物流码
        "channel": "2", //扣减渠道
        "photoAttId": null,
        "attribute1": null,
        "attribute2": null,
        "attribute3": null,
        "createUserId": null,
        "createTime": null,
        "updateUserId": null,
        "approveTime": null,
        "remark": null,
        "approveStatus": null,
        "updateTime": null,
        "approveUserId": null,
        "approveUserName": null,
        "city": null,
        "province": null,
        "fromDistributorName": "上海昂畅实业有限公司", //窜货经销商名称
        "displayChannel": "C&I", //显示产品渠道
        "authChannel": 2, //授权渠道
        "fromDistributorRegions": null,
        "toDistributorRegions": null,
        "channelText": "商用油", //扣减渠道标题（tab标题）
        "displayChannelText": "商用油" //显示渠道标题（产品列表渠道）
    }
}

2. 产品列表接口（手动录入产品搜索）
/mainlocalmarketing/quryproductinfo.do?keyword=5&limit=20
参数：
keyword：搜索关键字
limit：返回列表条数限制
返回：
{
    "__version": "1.1",
    "code": "success",
    "resultLst": [
        {
            "id": null,
            "reportId": null,
            "flowId": null,
            "fromDistributorId": null,
            "productName": "CH Hav Fully Syn MTF 75W90 (18LP ML2)", //产品名称
            "sku": "513006HRK", //SKU
            "reportingRegionId": null,
            "scanTime": null,
            "productTime": null,
            "productBatch": null,
            "productOutNo": null,
            "logisticsCode": null,
            "channel": "1", //扣减渠道
            "photoAttId": null,
            "attribute1": null,
            "attribute2": null,
            "attribute3": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "approveTime": null,
            "remark": null,
            "approveStatus": null,
            "updateTime": null,
            "approveUserId": null,
            "approveUserName": null,
            "city": null,
            "province": null,
            "fromDistributorName": null,
            "displayChannel": "CDM", //显示渠道
            "authChannel": 1, //授权渠道
            "fromDistributorRegions": null,
            "toDistributorRegions": null,
            "channelText": "乘用车", //扣减渠道标题
            "displayChannelText": "乘用车" //显示渠道标题
        }
    ]
}

3. 列表 POST JSON
/nonlocalmktflowform/cardList.do?appToken=16d90f9679065fecc323c062537d6eb003fd579e
参数(除了displayChannel，其他不变)：{"field":"update_time","direction":"DESC","limit":10,"start":0,"fromDistributorId":null,"toDistributorId":null,"displayChannel":null,"fromRegion":null,"toRegion":null,"reportDateStart":null,"reportDateEnd":null,"handleFlag":false}
返回：
{
    "__version": "1.1",
    "total": 15,
    "code": "success",
    "resultLst": [
        {
            "id": 13247, //流程ID(用于流程详情接口参数)
            "reportId": 13277, //上报ID
            "reqNo": "202207010033",
            "reqUserId": 1,
            "reqTime": 1656642592090, //上报时间
            "formStatus": 10,
            "toDistributorId": 157,
            "fromDistributorId": 208,
            "toFlsrConfirm": null,
            "toFlsrConfirmTime": null,
            "fromFlsrSurveySubmitTime": null,
            "fromAsmSurveySubmitTime": null,
            "toAsmFeedbackSubmitTime": null,
            "fromAsmResponse": null,
            "fromAsmResponseSubmitTime": null,
            "abmSubmitTime": null,
            "approvalCompletionTime": null,
            "localMarketingStatus": null,
            "extFlag": null,
            "deleteFlag": null,
            "attribute1": null,
            "attribute2": null,
            "attribute3": null,
            "attribute4": null,
            "attribute5": null,
            "updateTime": 1656642589043,
            "updateUserId": null,
            "createTime": null,
            "createUserId": null,
            "workflowInstance": {
                "flowInstanceId": 2782,
                "workflowCode": null,
                "formKey": null,
                "flowRequestNo": null,
                "currentStep": "TO_FLSR_CONFIRM", //当前步骤（(用于流程详情接口参数)）
                "nextStepNo": null,
                "formDesc": null,
                "formSummary": null,
                "requestUser": null,
                "stepVersionNo": null,
                "instanceExtProperty1": null,
                "instanceExtProperty2": null,
                "instanceExtProperty3": null,
                "instanceExtProperty4": null,
                "instanceExtProperty5": null,
                "instanceExtProperty6": null,
                "instanceExtProperty7": null,
                "instanceExtProperty8": null,
                "instanceExtProperty9": null,
                "applyTime": null,
                "status": null,
                "statusText": null,
                "closeTime": null,
                "workflowVersion": null,
                "versionNo": 1,
                "deleteFlag": null,
                "createUserId": null,
                "createTime": null,
                "updateUserId": null,
                "updateTime": 1656642589043,
                "instanceApplyType": null,
                "instanceApplyTypeText": null,
                "instanceApplyOwner": null,
                "instanceApplyOwnerType": null,
                "instanceApplyOwnerKey": null,
                "acceptFlag": 0,
                "rejectFlag": 0,
                "recallFlag": 0,
                "abortFlag": 0,
                "editFlag": 0,
                "requestUserName": null,
                "listOperateName": "审批",
                "listActions": null,
                "appListActions": null,
                "latestStepHistory": null,
                "stepInstances": null,
                "workflow": null,
                "applyTimeF": ""
            },
            "respons": [],
            "productInfos": [],
            "times": [],
            "fromDistributorIdName": "佛山市顺德区加孚商贸有限公司", //窜货经销商
            "toDistributorName": null,
            "executeFlag": null,
            "rejectFlag": null,
            "nextStep": null,
            "nextStepName": null,
            "nextStepExecuteUser": null,
            "versionNo": null,
            "nonLocalCount": 0,
            "executor": null,
            "channelWeight": 1,
            "specialFlag": false,
            "fromKaFlag": 0,
            "fromSpecialFlag": null,
            "submitConfirm": null,
            "remark": null,
            "workflowStep": null,
            "fromDistributorRegions": null,
            "mainLocalMarketing": {
                "id": 13277, 
                "createUserId": null,
                "createTime": null,
                "updateUserId": null,
                "updateTime": null,
                "reportId": null,
                "reportRegionId": 510100,
                "reportName": "沈逸", //上报人
                "reprotType": 4,
                "distributorId": 157,
                "customerNameCn": null,
                "reprotStatus": 0,
                "extFlag": null,
                "deleteFlag": null,
                "attribute1": null,
                "attribute2": null,
                "attribute3": null,
                "cityName": "成都市", //上报市
                "provinceName": "四川省", //上报省
                "distributorName": "成都乐途汽车配件有限公司", //被窜货经销商
                "remark": null,
                "reportLatitude": null,
                "reportLongitude": null,
                "photoLatitude": null,
                "photoLongitude": null,
                "productInfos": null,
                "respons": null,
                "files": null,
                "todoNum": 1, //待处理按钮的待办数量
                "specialFlag": false,
                "saveFlag": null,
                "relativeDistance": null
            },
            "displayChannelsText": "乘用车", //产品渠道
            "attFileSize": 0 //上传附件数量
        },...
    ]
}

4. 流程详情 POST
/nonlocalmktflowform/detail.do?id=13247&stepCode=TO_FLSR_CONFIRM&appToken=16d90f9679065fecc323c062537d6eb003fd579e
{
    "currentStep": {
        "stepId": 106,
        "workflowCode": "NON_LOCK_MKT",
        "stepCode": "TO_FLSR_CONFIRM",
        "stepName": "是否同意上报",
        "historyStepName": null,
        "stepIcon": null,
        "listOperateName": "",
        "acceptAlias": "同意",
        "operationPermissionWeight": 3,
        "bizPermissionWeight": 0,
        "rejectToStep": "",
        "rejectNotifyType": 0,
        "rejectEmailCc": "",
        "rejectNotifyTemplate": null,
        "todoNotifyType": 3,
        "todoEmailCc": "",
        "todoNotifyTemplate": null,
        "preRecallNotifyType": 0,
        "preRecallEmailCc": null,
        "preRecallEmailTemp": null,
        "finishRateType": 0,
        "finishRate": null,
        "extProperty1": null,
        "extProperty2": null,
        "extProperty3": null,
        "extProperty4": null,
        "extProperty5": null,
        "extProperty6": null,
        "extProperty7": null,
        "extProperty8": null,
        "extProperty9": null,
        "sortNumb": 2.2,
        "rejectSmsNotifyTemplate": null,
        "todoSmsNotifyTemplate": null,
        "preRecallSmsTemp": null,
        "propertyViewWeight1": 0,
        "propertyEditWeight1": 0,
        "propertyRequiredWeight1": 0,
        "propertyViewWeight2": 0,
        "propertyEditWeight2": 0,
        "propertyRequiredWeight2": 0,
        "propertyViewWeight3": 0,
        "propertyEditWeight3": 0,
        "propertyRequiredWeight3": 0,
        "remark": "",
        "predefinedFlag": 0,
        "deleteFlag": 0,
        "createUserId": 1,
        "createTime": 1603263594283,
        "workflowExeListList": null,
        "workflowBizPermissionList": null,
        "recallOperationName": "撤回",
        "acceptOperationName": "同意",
        "abortOperationName": "终止",
        "rejectOperationName": "驳回",
        "todoName": "待是否同意上报"
    },
    "code": "success",
    "form": {
        "id": 13247,
        "reportId": 13277,
        "reqNo": "202207010033",
        "reqUserId": 1,
        "reqTime": 1656642592090,
        "formStatus": 10,
        "toDistributorId": 157,
        "fromDistributorId": 208,
        "toFlsrConfirm": null,
        "toFlsrConfirmTime": null,
        "fromFlsrSurveySubmitTime": null,
        "fromAsmSurveySubmitTime": null,
        "toAsmFeedbackSubmitTime": null,
        "fromAsmResponse": null,
        "fromAsmResponseSubmitTime": null,
        "abmSubmitTime": null,
        "approvalCompletionTime": null,
        "localMarketingStatus": null,
        "extFlag": 0,
        "deleteFlag": 0,
        "attribute1": null,
        "attribute2": null,
        "attribute3": null,
        "attribute4": null,
        "attribute5": null,
        "updateTime": 1656642592090,
        "updateUserId": 1,
        "createTime": 1656642592090,
        "createUserId": 1,
        "workflowInstance": {
            "flowInstanceId": 2782,
            "workflowCode": null,
            "formKey": null,
            "flowRequestNo": null,
            "currentStep": null,
            "nextStepNo": null,
            "formDesc": null,
            "formSummary": null,
            "requestUser": null,
            "stepVersionNo": null,
            "instanceExtProperty1": null,
            "instanceExtProperty2": null,
            "instanceExtProperty3": null,
            "instanceExtProperty4": null,
            "instanceExtProperty5": null,
            "instanceExtProperty6": null,
            "instanceExtProperty7": null,
            "instanceExtProperty8": null,
            "instanceExtProperty9": null,
            "applyTime": 1656642589043,
            "status": null,
            "statusText": null,
            "closeTime": null,
            "workflowVersion": null,
            "versionNo": 1, //版本号（流程执行参数）
            "deleteFlag": 0,
            "createUserId": 1,
            "createTime": 1656642592090,
            "updateUserId": 1,
            "updateTime": 1656642592090,
            "instanceApplyType": null,
            "instanceApplyTypeText": null,
            "instanceApplyOwner": null,
            "instanceApplyOwnerType": null,
            "instanceApplyOwnerKey": null,
            "acceptFlag": 1, //是否可提交
            "rejectFlag": 1, //是否可驳回
            "recallFlag": 0,
            "abortFlag": 0,
            "editFlag": 0,
            "requestUserName": null,
            "listOperateName": "审批",
            "listActions": null,
            "appListActions": null,
            "latestStepHistory": null,
            "stepInstances": null,
            "workflow": null,
            "applyTimeF": "2022-07-01 10:29:49"
        },
        "respons": [],
        "productInfos": [
            {
                "id": 584,
                "reportId": null,
                "flowId": null,
                "fromDistributorId": null,
                "productName": "雪佛龙金富力ProDS全合成机油ECO 5 5W-30 4x4L", //产品名称
                "sku": "500264LPK", //SKU
                "reportingRegionId": null,
                "scanTime": 1528422001000,
                "productTime": 1564675200000,
                "productBatch": "CN9C070404",
                "productOutNo": "218754895",
                "logisticsCode": "102926120107",
                "channel": "1",
                "photoAttId": null,
                "attribute1": null,
                "attribute2": null,
                "attribute3": null,
                "createUserId": null,
                "createTime": null,
                "updateUserId": null,
                "approveTime": null,
                "remark": null,
                "approveStatus": null,
                "updateTime": null,
                "approveUserId": null,
                "approveUserName": null,
                "city": "佛山市,上海市", //窜货经销商授权市
                "province": "广东省,上海市", //窜货经销商授权省
                "fromDistributorName": null,
                "displayChannel": null,
                "authChannel": null,
                "fromDistributorRegions": null,
                "toDistributorRegions": null,
                "channelText": "乘用车", //扣减渠道标题
                "displayChannelText": "乘用车" //显示渠道标题
            }
        ],
        "times": [],
        "fromDistributorIdName": "佛山市顺德区加孚商贸有限公司", //窜货经销商
        "toDistributorName": "成都乐途汽车配件有限公司", //被窜货经销商
        "executeFlag": null,
        "rejectFlag": true,
        "nextStep": "TO_FLSR_CONFIRM", //下一步步骤编码
        "nextStepName": "是否同意上报", //下一步步骤名称
        "nextStepExecuteUser": "白英杰", //下一步步骤执行人
        "versionNo": 1,
        "nonLocalCount": 0,
        "executor": null,
        "channelWeight": 1,
        "specialFlag": false,
        "fromKaFlag": 0,
        "fromSpecialFlag": false,
        "submitConfirm": null,
        "remark": null,
        "workflowStep": {
            "stepId": null,
            "workflowCode": null,
            "stepCode": "END",
            "stepName": "结束",
            "historyStepName": null,
            "stepIcon": null,
            "listOperateName": null,
            "acceptAlias": null,
            "operationPermissionWeight": null,
            "bizPermissionWeight": null,
            "rejectToStep": null,
            "rejectNotifyType": null,
            "rejectEmailCc": null,
            "rejectNotifyTemplate": null,
            "todoNotifyType": null,
            "todoEmailCc": null,
            "todoNotifyTemplate": null,
            "preRecallNotifyType": null,
            "preRecallEmailCc": null,
            "preRecallEmailTemp": null,
            "finishRateType": null,
            "finishRate": null,
            "extProperty1": null,
            "extProperty2": null,
            "extProperty3": null,
            "extProperty4": null,
            "extProperty5": null,
            "extProperty6": null,
            "extProperty7": null,
            "extProperty8": null,
            "extProperty9": null,
            "sortNumb": null,
            "rejectSmsNotifyTemplate": null,
            "todoSmsNotifyTemplate": null,
            "preRecallSmsTemp": null,
            "propertyViewWeight1": null,
            "propertyEditWeight1": null,
            "propertyRequiredWeight1": null,
            "propertyViewWeight2": null,
            "propertyEditWeight2": null,
            "propertyRequiredWeight2": null,
            "propertyViewWeight3": null,
            "propertyEditWeight3": null,
            "propertyRequiredWeight3": null,
            "remark": null,
            "predefinedFlag": null,
            "deleteFlag": null,
            "createUserId": null,
            "createTime": null,
            "workflowExeListList": null,
            "workflowBizPermissionList": null,
            "recallOperationName": "撤回",
            "acceptOperationName": "通过",
            "abortOperationName": "终止",
            "rejectOperationName": "驳回",
            "todoName": "待结束"
        },
        "fromDistributorRegions": null,
        "mainLocalMarketing": {
            "id": 13277,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "reportId": null,
            "reportRegionId": null,
            "reportName": null,
            "reprotType": null,
            "distributorId": null,
            "customerNameCn": null,
            "reprotStatus": null,
            "extFlag": null,
            "deleteFlag": null,
            "attribute1": null,
            "attribute2": null,
            "attribute3": null,
            "cityName": null,
            "provinceName": null,
            "distributorName": null,
            "remark": null,
            "reportLatitude": null,
            "reportLongitude": null,
            "photoLatitude": null,
            "photoLongitude": null,
            "productInfos": null,
            "respons": null,
            "files": null,
            "todoNum": null,
            "specialFlag": false,
            "saveFlag": null,
            "relativeDistance": null
        },
        "displayChannelsText": null,
        "attFileSize": null
    }
}

5. 报表 POST
https://wwwstg.cvx-sh.com/reportview/report/pagedata.do?packageName=nonlocalsale&viewName=NonLocalSaleReport&limit=20&start=0&field=from_region,from_flsr&direction=ASC&permissionWeight=8&cai=OPCX&appToken=5892342fb969e2a0657b5fa73480ca47690a13ff
参数:
	packageName=nonlocalsale&viewName=NonLocalSaleReport
	limit=20&start=0&field=from_region,from_flsr&direction=ASC 分页
	fromRegion：非本地营销大区
	fromDistributorId：非本地营销经销商
	reportDateFrom：上报开始日期
	reportDateTo：上报截至日期
	channel:影响渠道
	permissionWeight：权限值 (FLEEING.GOODS)
	cai:登录用户CAI属性
返回：
	{
    "__version": "1.1",
    "total": 4,
    "code": "success",
    "resultLst": [
        {
            "from_flsr": "李强",
            "from_asm": "章园",
            "channel_text": "乘用车",
            "non_local_punish": null,
            "report_times": 2,
            "rownumber": 1,
            "from_distributor_name": "潍坊孚惠四方贸易有限公司",
            "from_region": "East & CC"
        },...
    ]
}

6. 报表导出 POST
/reportview/excel/exportbycols.do?packageName=nonlocalsale&viewName=NonLocalSaleReport&field=region,flsr&direction=ASC&permissionWeight=8&cai=OPCX&columnsDictKey=NonLocalSale.report.excelColumns&fileName=非本地营销报表&appToken=5892342fb969e2a0657b5fa73480ca47690a13ff
参数:
	packageName=nonlocalsale&viewName=NonLocalSaleReport
	field=region,flsr&direction=ASC 分页
	fromRegion：非本地营销大区
	fromDistributorId：非本地营销经销商
	reportDateFrom：上报开始日期
	reportDateTo：上报截至日期
	channel:影响渠道
	permissionWeight：权限值 (FLEEING.GOODS)
	cai:登录用户CAI属性
	columnsDictKey: Excel列配置
	fileName：Excel文件名
	

7. 用户扫码 POST
/reportview/report/pagedata.do?packageName=nonlocalsale&viewName=UserReport&limit=20&start=0&field=report_time&direction=DESC&permissionWeight=1&cai=&appToken=5892342fb969e2a0657b5fa73480ca47690a13ff
参数:
	packageName=nonlocalsale&viewName=UserReport
	limit=20&start=0&field=report_time&direction=DESC 分页
	fromRegion：产品所属大区
	fromDistributorId：产品所属经销商
	reportDateFrom：上报开始日期
	reportDateTo：上报截至日期
	displayChannel:产品渠道
	logisticsCode：物流码
	localSaleFlag：本地营销标记。1是，0-否，小于0-全部
	permissionWeight：权限值 (FLEEING.GOODS)
	cai:登录用户CAI属性
返回：
	{
    "__version": "1.1",
    "total": 1,
    "code": "success",
    "resultLst": [
        {
            "display_channel": "CDM",
            "from_distributor_id": 251,
            "out_date": "2018-06-08",  //出库日期
            "out_order_no": "218879707", //出库单号
            "product_city": "佛山市,广州市,清远市", //产品销售城市
            "logistics_code": "102926120138", //物流码
            "report_province": "重庆市", //扫码省份
            "product_name": "雪佛龙金富力ProDS全合成机油ECO 5 5W-30 4x4L", //产品名称
            "product_date": "2019-08-05", //生产日期
            "product_batch": "CN9H011538", //生产批次
            "report_city": "重庆市", //扫码城市
            "product_province": "广东省", //产品销售省份
            "report_time": "2022-06-30 18:19:41", //扫码时间
            "display_channel_text": "乘用车", //产品渠道标题
            "rownumber": 1,
            "from_distributor_name": "广州市华臣润滑油有限公司", //所属经销商
            "sku": "500264LPK", 
            "from_region": "SC", //所属大区
            "local_sale_text": "否" //本地营销标记标题
        }
    ]
}

8. 用户扫码导出 POST
/reportview/excel/exportbycols.do?packageName=nonlocalsale&viewName=UserReport&limit=20&start=0&field=report_time&direction=DESC&permissionWeight=1&cai=&columnsDictKey=NonLocalSale.UserReport.excelColumns&fileName=用户扫码验真&appToken=5892342fb969e2a0657b5fa73480ca47690a13ff
参数:
	packageName=nonlocalsale&viewName=UserReport
	field=report_time&direction=DESC 分页
	fromRegion：产品所属大区
	fromDistributorId：产品所属经销商
	reportDateFrom：上报开始日期
	reportDateTo：上报截至日期
	displayChannel:产品渠道
	logisticsCode：物流码
	localSaleFlag：本地营销标记。1是，0-否，小于0-全部
	permissionWeight：权限值 (FLEEING.GOODS)
	cai:登录用户CAI属性
	columnsDictKey: Excel列配置
	fileName：Excel文件名
