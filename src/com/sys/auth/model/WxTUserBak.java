package com.sys.auth.model;

import java.util.Date;

public class WxTUserBak {
	
    private Long userId;
    
    private Long post ;//岗位 标记是否分销商监理  1是监理
    public Long getPost() {
		return post;
	}
	public void setPost(Long post) {
		this.post = post;
	}
	
	private String postion; //职位代码  对应主数据代码
	private String salt;
	
    private String postionName; //职位名称
    
	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.login_name
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private String loginName;

    
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.user_no
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private String userNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.password
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private String password;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.ch_name
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private String chName;

   
    private String allowLogin;

    private String sex;

    private Date birthday;

    private String address;

    private String email;

    private String mobileTel;

    private Long orgId;
    
    private Long tempFlag;
    
    public Long getTempFlag() {
		return tempFlag;
	}
	public void setTempFlag(Long tempFlag) {
		this.tempFlag = tempFlag;
	}

	private String orgName;
    public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	/**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.user_intime
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private Date userIntime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.description
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
   
    private Date xzTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.xz_user
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private String xzUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.xg_time
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private Date xgTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.xg_user
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private String xgUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.status
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_user.tenant_id
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    private Long tenantId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.user_id
     *
     * @return the value of wx_t_user.user_id
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getChName() {
        return chName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.ch_name
     *
     * @param chName the value for wx_t_user.ch_name
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setChName(String chName) {
        this.chName = chName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.allow_login
     *
     * @return the value of wx_t_user.allow_login
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getAllowLogin() {
        return allowLogin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.allow_login
     *
     * @param allowLogin the value for wx_t_user.allow_login
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setAllowLogin(String allowLogin) {
        this.allowLogin = allowLogin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.sex
     *
     * @return the value of wx_t_user.sex
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getSex() {
        return sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.sex
     *
     * @param sex the value for wx_t_user.sex
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.birthday
     *
     * @return the value of wx_t_user.birthday
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public Date getBirthday() {
        return birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.birthday
     *
     * @param birthday the value for wx_t_user.birthday
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.address
     *
     * @return the value of wx_t_user.address
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.address
     *
     * @param address the value for wx_t_user.address
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.email
     *
     * @return the value of wx_t_user.email
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.email
     *
     * @param email the value for wx_t_user.email
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.mobile_tel
     *
     * @return the value of wx_t_user.mobile_tel
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getMobileTel() {
        return mobileTel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.mobile_tel
     *
     * @param mobileTel the value for wx_t_user.mobile_tel
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setMobileTel(String mobileTel) {
        this.mobileTel = mobileTel;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }
    
    public Date getUserIntime() {
        return userIntime;
    }

    public void setUserIntime(Date userIntime) {
        this.userIntime = userIntime;
    }
    
    public Date getXzTime() {
        return xzTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.xz_time
     *
     * @param xzTime the value for wx_t_user.xz_time
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setXzTime(Date xzTime) {
        this.xzTime = xzTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.xz_user
     *
     * @return the value of wx_t_user.xz_user
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getXzUser() {
        return xzUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.xz_user
     *
     * @param xzUser the value for wx_t_user.xz_user
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setXzUser(String xzUser) {
        this.xzUser = xzUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.xg_time
     *
     * @return the value of wx_t_user.xg_time
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public Date getXgTime() {
        return xgTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.xg_time
     *
     * @param xgTime the value for wx_t_user.xg_time
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setXgTime(Date xgTime) {
        this.xgTime = xgTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.xg_user
     *
     * @return the value of wx_t_user.xg_user
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public String getXgUser() {
        return xgUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.xg_user
     *
     * @param xgUser the value for wx_t_user.xg_user
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setXgUser(String xgUser) {
        this.xgUser = xgUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.status
     *
     * @return the value of wx_t_user.status
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.status
     *
     * @param status the value for wx_t_user.status
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_user.tenant_id
     *
     * @return the value of wx_t_user.tenant_id
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_user.tenant_id
     *
     * @param tenantId the value for wx_t_user.tenant_id
     *
     * @mbggenerated Wed Jun 03 10:58:07 CST 2015
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
    
	public String getPostion() {
		return postion;
	}

	public void setPostion(String postion) {
		this.postion = postion;
	}

	public String getPostionName() {
		return postionName;
	}

	public void setPostionName(String postionName) {
		this.postionName = postionName;
	}

	public String getSalt() {
		return salt;
	}
	public void setSalt(String salt) {
		this.salt = salt;
	}
	public Object clone() {  
        WxTUser o = null;  
        try {  
            o = (WxTUser) super.clone();  
        } catch (CloneNotSupportedException e) {  
            e.printStackTrace();  
        }  
        return o;  
    }  
}
