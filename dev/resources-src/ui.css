.ui-grid {
	border-left: 1px solid #dddddd;
	border-top: 1px solid #dddddd;
}
.ui-grid-y-scroll{
	position: absolute;
	right: 0;
	background-color: #fafafa;
}
.ui-grid-locked, .ui-grid-unlocked {
    float: left;
    overflow: hidden;
}
.ui-grid-table {
    border-collapse: collapse;
    width: 100%;
    position: relative;
}
.ui-grid-cell {
	padding: 2px 8px;
	min-width: 25px;
	min-height: 12px;
	line-height: 26px;
}
.ui-grid-td {
	border-right: 1px solid #dddddd;
	border-bottom: 1px solid #dddddd;
}
.ui-grid-header-row .ui-grid-td {
	text-align: center;
	font-weight: 700;
}
.ui-grid-header .ui-grid-td-colspan1 {
	text-align: left;
}
.ui-grid-header {
}
.ui-grid-body {
}
.ui-grid-x-scroll {
	overflow: hidden;
	background-color: #fafafa;
}
.ui-grid-x-scroll-empty, .ui-grid-x-scroll-bar {
	float: left;
}
.ui-x-scrollbar-auto {
	overflow-x: auto;
}
.ui-x-scrollbar .ui-scrollbar-holder {
	height: 1px;
}
.ui-x-scrollbar-scroll {
	overflow-y: scroll;
}
.ui-x-scrollbar-scroll .ui-scrollbar-holder {
	width: 1px;
}
.no-data-comment {
    text-align: center;
    font-weight: 700;
    color: #999;
    padding: 6px;
	border-bottom: 1px solid #dddddd;
}
.ui-grid-summary .ui-grid-locked, .ui-grid-summary .ui-grid-unlocked {
	border-top: 1px solid #dddddd;
}
.ui-tree-grid-node-icon {
	display: inline-block;
    vertical-align: top;
    height: 16px;
    width: 16px;
    background: url(image/tree_icon.gif) no-repeat -999px -999px transparent;
    position: relative;
    top: 5px;
}
.ui-tree-grid-node-leaf .ui-tree-grid-node-icon {
}
.ui-tree-grid-node-expand .ui-tree-grid-node-icon {
	background-position: -48px 0;
	cursor: pointer;
}
.ui-tree-grid-node-expand-hover .ui-tree-grid-node-icon {
}
.ui-tree-grid-node-collapse .ui-tree-grid-node-icon {
	background-position: -32px 0;
	cursor: pointer;
}
.ui-tree-grid-node-collapse-hover .ui-tree-grid-node-icon {
}
.ui-tree-grid-collapse-item {
	display: none;
}
.ui-tree-grid .ui-grid-summary-row .ui-grid-td, .ui-tree-grid .ui-tree-grid-record-level1 .ui-grid-td {
	border-right: none;
    background-color: #ddd;
    border-bottom: 8px solid #fff;
}
.ui-tree-grid .ui-grid-header-row .ui-grid-td {
	border-right: none;
    background-color: #ddd;
    border-bottom: 1px solid #fff;
}
.ui-tree-grid .ui-grid-header-row td.ui-grid-header-leaf {
	border-right: none;
    background-color: #ddd;
    border-bottom: 8px solid #fafafa;
}
.ui-tree-grid .ui-grid-td {
	border-right: none;
	border-bottom: 1px solid #dddddd;
}
.ui-tree-grid .ui-grid-locked .ui-grid-table tr td.ui-grid-part-last-td {
	border-right: 1px solid #fafafa;
}
.ui-paging-bar-default .ui-paging-btn button.ui-button-hover {
	border-color: rgb(146,186,223);
}
div.ui-paging-bar-default button.ui-button-disabled {
	opacity: 0.2;
}
.ui-paging-bar-default .ui-paging-btn button {
	background: url(image/table.gif) no-repeat -999px -999px;
	height: 16px;
    width: 16px;
    margin: 0;
    text-indent: -100px;
    border: 1px solid #fff;
    vertical-align: middle;
	opacity: 1;
}
.ui-paging-bar-default span.ui-paging-first-btn button {
	background-position: 3px 2px;
}
.ui-paging-bar-default span.ui-paging-pre-btn button {
	background-position: -57px 2px;
}
.ui-paging-bar-default span.ui-paging-next-btn button {
	background-position: -37px 2px;
}
.ui-paging-bar-default span.ui-paging-last-btn button {
	background-position: -17px 2px;
}
.ui-paging-bar .ui-page-no-int {
	padding: 3px 6px;
    width: 30px;
    vertical-align: baseline;
    height: 16px;
    color: #9d261d;
    margin: 0 3px;
}
.ui-paging-bar .ui-page-bar-separator {
	height: 14px;
    width: 2px;
	background: url(image/table.gif) no-repeat -80px 0;
	display: inline-block;
	vertical-align: middle;
    margin: 0 8px;
}
.ui-paging-bar {
	height: 30px;
	line-height: 30px;
	margin: 5px 0;
	float: right;
}
.ui-paging-bar .ui-total-page, .ui-paging-bar .ui-input-page-no, .ui-paging-bar .ui-total-record {
	padding: 0 12px;
}
.ui-paging-bar .ui-total-page-value, .ui-paging-bar .ui-total-record-value {
	color: #9d261d;
	padding: 0 5px;
}
.ui-page-no-go-btn button {
	font-size: 12px;
    margin: 2px 8px;
    line-height: 18px;
}
.ui-grid-paging {
	height: 40px;
}
.input-warning {
	background-color: #fcf8e3;
}
.ui-tree-grid-node img {
	display: none;
	width: 16px;
	height: 16px;
}
.ui-tree-grid-node-loading img {
	display: block;
}
.ui-tree-grid-node-loading .ui-tree-grid-node-icon {
	background-position: -999px -999px;
}