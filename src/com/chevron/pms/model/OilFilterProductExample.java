package com.chevron.pms.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OilFilterProductExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public OilFilterProductExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andBrandCodeIsNull() {
            addCriterion("brand_code is null");
            return (Criteria) this;
        }

        public Criteria andBrandCodeIsNotNull() {
            addCriterion("brand_code is not null");
            return (Criteria) this;
        }

        public Criteria andBrandCodeEqualTo(String value) {
            addCriterion("brand_code =", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeNotEqualTo(String value) {
            addCriterion("brand_code <>", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeGreaterThan(String value) {
            addCriterion("brand_code >", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeGreaterThanOrEqualTo(String value) {
            addCriterion("brand_code >=", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeLessThan(String value) {
            addCriterion("brand_code <", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeLessThanOrEqualTo(String value) {
            addCriterion("brand_code <=", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeLike(String value) {
            addCriterion("brand_code like", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeNotLike(String value) {
            addCriterion("brand_code not like", value, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeIn(List<String> values) {
            addCriterion("brand_code in", values, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeNotIn(List<String> values) {
            addCriterion("brand_code not in", values, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeBetween(String value1, String value2) {
            addCriterion("brand_code between", value1, value2, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandCodeNotBetween(String value1, String value2) {
            addCriterion("brand_code not between", value1, value2, "brandCode");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("brand_name is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("brand_name is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("brand_name =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("brand_name <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("brand_name >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("brand_name >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("brand_name <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("brand_name <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("brand_name like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("brand_name not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("brand_name in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("brand_name not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("brand_name between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("brand_name not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterIsNull() {
            addCriterion("excircle_diameter is null");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterIsNotNull() {
            addCriterion("excircle_diameter is not null");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterEqualTo(String value) {
            addCriterion("excircle_diameter =", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterNotEqualTo(String value) {
            addCriterion("excircle_diameter <>", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterGreaterThan(String value) {
            addCriterion("excircle_diameter >", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterGreaterThanOrEqualTo(String value) {
            addCriterion("excircle_diameter >=", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterLessThan(String value) {
            addCriterion("excircle_diameter <", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterLessThanOrEqualTo(String value) {
            addCriterion("excircle_diameter <=", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterLike(String value) {
            addCriterion("excircle_diameter like", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterNotLike(String value) {
            addCriterion("excircle_diameter not like", value, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterIn(List<String> values) {
            addCriterion("excircle_diameter in", values, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterNotIn(List<String> values) {
            addCriterion("excircle_diameter not in", values, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterBetween(String value1, String value2) {
            addCriterion("excircle_diameter between", value1, value2, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andExcircleDiameterNotBetween(String value1, String value2) {
            addCriterion("excircle_diameter not between", value1, value2, "excircleDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterIsNull() {
            addCriterion("seal_ring_outside_diameter is null");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterIsNotNull() {
            addCriterion("seal_ring_outside_diameter is not null");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterEqualTo(String value) {
            addCriterion("seal_ring_outside_diameter =", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterNotEqualTo(String value) {
            addCriterion("seal_ring_outside_diameter <>", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterGreaterThan(String value) {
            addCriterion("seal_ring_outside_diameter >", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterGreaterThanOrEqualTo(String value) {
            addCriterion("seal_ring_outside_diameter >=", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterLessThan(String value) {
            addCriterion("seal_ring_outside_diameter <", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterLessThanOrEqualTo(String value) {
            addCriterion("seal_ring_outside_diameter <=", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterLike(String value) {
            addCriterion("seal_ring_outside_diameter like", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterNotLike(String value) {
            addCriterion("seal_ring_outside_diameter not like", value, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterIn(List<String> values) {
            addCriterion("seal_ring_outside_diameter in", values, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterNotIn(List<String> values) {
            addCriterion("seal_ring_outside_diameter not in", values, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterBetween(String value1, String value2) {
            addCriterion("seal_ring_outside_diameter between", value1, value2, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingOutsideDiameterNotBetween(String value1, String value2) {
            addCriterion("seal_ring_outside_diameter not between", value1, value2, "sealRingOutsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterIsNull() {
            addCriterion("seal_ring_inside_diameter is null");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterIsNotNull() {
            addCriterion("seal_ring_inside_diameter is not null");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterEqualTo(String value) {
            addCriterion("seal_ring_inside_diameter =", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterNotEqualTo(String value) {
            addCriterion("seal_ring_inside_diameter <>", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterGreaterThan(String value) {
            addCriterion("seal_ring_inside_diameter >", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterGreaterThanOrEqualTo(String value) {
            addCriterion("seal_ring_inside_diameter >=", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterLessThan(String value) {
            addCriterion("seal_ring_inside_diameter <", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterLessThanOrEqualTo(String value) {
            addCriterion("seal_ring_inside_diameter <=", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterLike(String value) {
            addCriterion("seal_ring_inside_diameter like", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterNotLike(String value) {
            addCriterion("seal_ring_inside_diameter not like", value, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterIn(List<String> values) {
            addCriterion("seal_ring_inside_diameter in", values, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterNotIn(List<String> values) {
            addCriterion("seal_ring_inside_diameter not in", values, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterBetween(String value1, String value2) {
            addCriterion("seal_ring_inside_diameter between", value1, value2, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andSealRingInsideDiameterNotBetween(String value1, String value2) {
            addCriterion("seal_ring_inside_diameter not between", value1, value2, "sealRingInsideDiameter");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsIsNull() {
            addCriterion("installation_dimensions is null");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsIsNotNull() {
            addCriterion("installation_dimensions is not null");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsEqualTo(String value) {
            addCriterion("installation_dimensions =", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsNotEqualTo(String value) {
            addCriterion("installation_dimensions <>", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsGreaterThan(String value) {
            addCriterion("installation_dimensions >", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsGreaterThanOrEqualTo(String value) {
            addCriterion("installation_dimensions >=", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsLessThan(String value) {
            addCriterion("installation_dimensions <", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsLessThanOrEqualTo(String value) {
            addCriterion("installation_dimensions <=", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsLike(String value) {
            addCriterion("installation_dimensions like", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsNotLike(String value) {
            addCriterion("installation_dimensions not like", value, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsIn(List<String> values) {
            addCriterion("installation_dimensions in", values, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsNotIn(List<String> values) {
            addCriterion("installation_dimensions not in", values, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsBetween(String value1, String value2) {
            addCriterion("installation_dimensions between", value1, value2, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andInstallationDimensionsNotBetween(String value1, String value2) {
            addCriterion("installation_dimensions not between", value1, value2, "installationDimensions");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(String value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(String value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(String value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(String value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(String value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(String value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLike(String value) {
            addCriterion("height like", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotLike(String value) {
            addCriterion("height not like", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<String> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<String> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(String value1, String value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(String value1, String value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Long value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Long value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Long value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Long value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Long value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Long value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Long> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Long> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Long value1, Long value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Long value1, Long value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNull() {
            addCriterion("updator is null");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNotNull() {
            addCriterion("updator is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatorEqualTo(Long value) {
            addCriterion("updator =", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotEqualTo(Long value) {
            addCriterion("updator <>", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThan(Long value) {
            addCriterion("updator >", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThanOrEqualTo(Long value) {
            addCriterion("updator >=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThan(Long value) {
            addCriterion("updator <", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThanOrEqualTo(Long value) {
            addCriterion("updator <=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorIn(List<Long> values) {
            addCriterion("updator in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotIn(List<Long> values) {
            addCriterion("updator not in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorBetween(Long value1, Long value2) {
            addCriterion("updator between", value1, value2, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotBetween(Long value1, Long value2) {
            addCriterion("updator not between", value1, value2, "updator");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}