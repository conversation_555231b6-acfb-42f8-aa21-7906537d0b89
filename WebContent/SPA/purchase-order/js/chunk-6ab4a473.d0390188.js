(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6ab4a473"],{"071f":function(t,e,r){t.exports=r("fda1").f("toPrimitive")},"16a8":function(t,e,r){"use strict";r("a818")},"1fa8":function(t,e,r){var n=r("cb7c");t.exports=function(t,e,r,a){try{return a?e(n(r)[0],r[1]):e(r)}catch(o){var i=t["return"];throw void 0!==i&&n(i.call(t)),o}}},2621:function(t,e){e.f=Object.getOwnPropertySymbols},2633:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[t._e(),1===t.step?r("product",{attrs:{cart:t.cart,products:t.products,updateCart:t.updateCart,createPreOrder:t.createPreOrder,changeAccount:t.isAccountChangable?t.changeAccount:null},on:{search:t.getTableData}}):t._e(),2===t.step?r("cart",{attrs:{cart:t.cart,goBackStepOne:t.goBackStepOne,createOrder:t.createOrder}}):t._e(),r("el-dialog",{staticClass:"custom-dialog",attrs:{visible:t.dialog.visible},on:{"update:visible":function(e){return t.$set(t.dialog,"visible",e)}}},[t.cashList.length||t.creditList.length?r("el-radio-group",{staticStyle:{display:"block"},model:{value:t.cart.partnerSaleConfigId,callback:function(e){t.$set(t.cart,"partnerSaleConfigId",e)},expression:"cart.partnerSaleConfigId"}},[t.hasIndustrial?t._e():r("span",{staticClass:"dialog-inner-title"},[t._v("请选择下单类型")]),t.hasIndustrial?r("span",{staticClass:"dialog-inner-title"},[t._v("请选择收货地址")]):t._e(),t.cashList.length?r("div",[t.hasIndustrial?t._e():r("div",{staticClass:"dialog-inner-subtitle"},[t._v("现金账户(SP)")]),t.hasIndustrial?r("div",{staticStyle:{height:"20px"}}):t._e(),t._l(t.cashList,(function(e){return r("el-radio",{key:e.id,staticStyle:{padding:"5px 0",display:"block",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{label:e.id},on:{change:function(r){return t.changeCart(e)}}},[r("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.shipToCode))]),t._v(" "+t._s(e.address)+"\n        ")])}))],2):t._e(),t.creditList.length?r("div",[t.hasIndustrial?t._e():r("div",{staticClass:"dialog-inner-subtitle"},[t._v("信用账户(普通经销商)")]),t._l(t.creditList,(function(e){return r("el-radio",{key:e.id,staticStyle:{padding:"5px 0",display:"block",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{label:e.id},on:{change:function(r){return t.changeCart(e)}}},[r("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.shipToCode))]),t._v(" "+t._s(e.address)+"\n        ")])}))],2):t._e()]):t._e(),t.hasIndustrial?t._e():r("el-radio-group",{staticStyle:{"margin-top":"15px",display:"block"},model:{value:t.cart.receiveType,callback:function(e){t.$set(t.cart,"receiveType",e)},expression:"cart.receiveType"}},[r("div",{staticClass:"dialog-inner-title"},[t._v("请选择收货模式")]),t._l(t.cart.receiveTypeList,(function(e){return r("el-radio",{key:e.id,staticStyle:{padding:"3px 0",display:"block"},attrs:{label:e.dicItemCode}},[r("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.dicItemName))]),r("span",[t._v("\n          "+t._s(t.getLimitationDescription(e.dicItemCode))+"\n        ")])])}))],2),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(e){t.dialog.visible=!1}}},[t._v("取消")]),r("el-button",{attrs:{type:"primary",disabled:!t.cart.partnerSaleConfigId},on:{click:function(e){t.dialog.visible=!1}}},[t._v("确定\n      ")])],1)],1)],1)},a=[],i=(r("8e6e"),r("ac6a"),r("456d"),r("d604")),o=r.n(i),s=r("db2a");function c(t){if(o()(t))return Object(s["a"])(t)}var u=r("b258"),l=r.n(u),d=r("5927"),p=r.n(d),f=r("57f7"),h=r.n(f);function m(t){if("undefined"!=typeof l.a&&null!=t[p.a]||null!=t["@@iterator"])return h()(t)}var g=r("e630");function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t){return c(t)||m(t)||Object(g["a"])(t)||v()}var y=r("bd86"),w=(r("6762"),r("2fdb"),r("c5f6"),r("7514"),r("768b")),x=(r("96cf"),r("3b8d")),O=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-steps",{attrs:{active:t.step,"finish-status":"success",simple:""}},[r("el-step",{attrs:{title:"选择商品"}}),r("el-step",{attrs:{title:"确认价格"}}),r("el-step",{attrs:{title:"生成采购订单"}})],1)},C=[],_={props:["step"]},k=_,S=r("2877"),I=Object(S["a"])(k,O,C,!1,null,null,null),P=I.exports,j=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("operation",{attrs:{createPreOrder:t.createPreOrder,changeAccount:t.changeAccount,cart:t.cart}}),r("search",{attrs:{options:t.products.options,params:t.products.params,cart:t.cart,updateCart:t.updateCart,search:t.$emit("search")}}),r("products",{attrs:{table:t.products,updateCart:t.updateCart}})],1)},T=[],L=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-row",{staticStyle:{"margin-bottom":"18px",padding:"10px","border-bottom":"1px solid #ccc"}},[r("el-col",{attrs:{span:12}},[r("h2",{staticStyle:{margin:"8px 0 0"}},[t._v("选择商品")])]),r("el-col",{staticClass:"text-right",staticStyle:{margin:"8px 0 0"},attrs:{span:12}},[t.hasIndustrial?t._e():r("el-button",{attrs:{type:"primary"},on:{click:t.changeAccount}},[t._v("更改下单类型")]),t.hasIndustrial?r("el-button",{attrs:{type:"primary"},on:{click:t.changeAccount}},[t._v("更改收货地址")]):t._e(),t.cart.hasSaleConfig?r("el-button",{attrs:{type:"primary"},on:{click:t.createPreOrder}},[t._v("获取价格")]):t._e(),r("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)},N=[],D={props:["createPreOrder","changeAccount","cart"],created:function(){console.log("lai le hhh222",this.$route.query.hasIndustrial)},computed:{hasIndustrial:function(){return"true"===this.$route.query.hasIndustrial}}},$=D,B=Object(S["a"])($,L,N,!1,null,null,null),E=B.exports,A=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-form",{attrs:{"label-width":"150px"},nativeOn:{submit:function(e){return e.preventDefault(),t.checkEnter(e)}}},[r("el-form-item",{attrs:{label:"搜索 ："}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:"输入产品名称或产品编号"},model:{value:t.params.keyword,callback:function(e){t.$set(t.params,"keyword",e)},expression:"params.keyword"}}),r("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")]),r("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger"},on:{click:t.clear}},[t._v("清空筛选条件")])],1),r("el-form-item",{attrs:{label:"产品分类 ："}},[r("el-checkbox-group",{on:{change:t.search},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},t._l(t.options.category,(function(e){return r("el-checkbox",{key:e.dicItemCode,attrs:{label:e.dicItemCode}},[t._v("\n        "+t._s(e.dicItemName)+"\n      ")])})),1)],1),r("select-product-line",{ref:"productLine",attrs:{params:t.params,lines:t.options.oiltype},on:{change:t.search},model:{value:t.params.oiltype,callback:function(e){t.$set(t.params,"oiltype",e)},expression:"params.oiltype"}}),r("el-form-item",{attrs:{label:"产品规格 ："}},[r("el-checkbox-group",{on:{change:t.search},model:{value:t.params.viscosity,callback:function(e){t.$set(t.params,"viscosity",e)},expression:"params.viscosity"}},t._l(t.options.viscosity,(function(e){return r("el-checkbox",{key:e.dicItemCode,attrs:{label:e.dicItemCode}},[t._v("\n        "+t._s(e.dicItemName)+"\n      ")])})),1)],1),r("el-form-item",{attrs:{label:"产品容量 ："}},[r("el-checkbox-group",{on:{change:t.search},model:{value:t.params.capacity,callback:function(e){t.$set(t.params,"capacity",e)},expression:"params.capacity"}},t._l(t.options.capacity,(function(e){return r("el-checkbox",{key:e.dicItemCode,attrs:{label:e.dicItemCode}},[t._v("\n        "+t._s(e.dicItemName)+"\n      ")])})),1)],1),r("el-form-item",{staticClass:"color-danger text-right"},[r("i",{staticClass:"el-icon-shopping-cart-full"}),r("el-popover",{attrs:{placement:"bottom",width:"800",trigger:"click"}},[r("products",{attrs:{table:t.cart,updateCart:t.updateCart}}),t.literRatio?r("div",{staticStyle:{float:"left","margin-top":"10px","margin-right":"5px","font-weight":"normal",color:"red"}},[t._v("\n        "+t._s(t.literRatio)+"\n      ")]):t._e(),r("div",{staticStyle:{float:"right","margin-top":"10px","margin-right":"5px","font-weight":"bold"}},[t._v("\n        合计升数: "+t._s(t._f("toMoney")(t.totalLiterCount))+"\n      ")]),r("span",{staticStyle:{"margin-right":"10px"},attrs:{slot:"reference"},slot:"reference"},[t._v("\n        已选中 "+t._s(t.cart.data.length)+" 商品，点击查看\n      ")])],1),t.cart.hasSaleConfig?r("el-button",{attrs:{type:"success",loading:t.saving},on:{click:t.save}},[t._v("保存订单")]):t._e()],1)],1)},q=[],M=r("5698"),V=r.n(M);function F(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(e.includes(n))continue;r[n]=t[n]}return r}function z(t,e){if(null==t)return{};var r,n,a=F(t,e);if(V.a){var i=V()(t);for(n=0;n<i.length;n++)r=i[n],e.includes(r)||{}.propertyIsEnumerable.call(t,r)&&(a[r]=t[r])}return a}var U=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.table.loading,expression:"table.loading"}],staticStyle:{"margin-top":"15px"},attrs:{size:"small",data:t.table.data,"empty-text":"没有数据",border:""}},[r("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),r("el-table-column",{attrs:{label:"产品名称",prop:"sku",width:"120"}}),r("el-table-column",{attrs:{label:"产品信息",prop:"name"},scopedSlots:t._u([{key:"default",fn:function(e){return[!t.isComposite(e.row)&&e.row.iconId?r("el-image",{staticStyle:{width:"40px",height:"40px","margin-left":"5px","margin-right":"10px"},attrs:{src:t.photoUrl(e.row.iconId)}}):t._e(),t.isComposite(e.row)?r("span",{domProps:{innerHTML:t._s(e.row.remark)}}):r("span",{staticStyle:{position:"absolute",top:"50%",transform:"translate(0, -50%)"}},[t._v("\n        "+t._s(e.row.name||e.row.productName)+"\n      ")])]}}])}),r("el-table-column",{attrs:{label:"规格",prop:"viscosity",width:"100"}}),r("el-table-column",{attrs:{label:"升/箱",prop:"boxCapacity",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n      "+t._s(t._f("toMoney")(e.row.boxCapacity))+"\n    ")]}}])}),r("el-table-column",{attrs:{label:"订货数量(箱)",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("cartNumber",{attrs:{props:e},on:{change:t.updateCart}})]}}])})],1)},K=[],W=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-input-number",{staticStyle:{width:"120px"},attrs:{type:"number",min:0,"controls-position":"right"},on:{change:t.change},model:{value:t.props.row.amount,callback:function(e){t.$set(t.props.row,"amount",e)},expression:"props.row.amount"}})},H=[],Q={props:["props"],data:function(){return{num:""}},methods:{change:function(t){this.$emit("change",this.props.row)}}},J=Q,G=Object(S["a"])(J,W,H,!1,null,null,null),Y=G.exports,X=r("0b96"),Z={props:["table","updateCart"],components:{cartNumber:Y},data:function(){return{options:[]}},created:function(){this.getOptions()},methods:{photoUrl:function(t){return"/downloadAttachmentFile.do?attId=".concat(t)},isComposite:function(t){var e=this.options.find((function(e){return e.dicItemCode==t.sku}))||{};return!!e.dicItemCode},getOptions:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByRPC({method:"dicService.getDicItemByDicTypeCode",params:["sap.bundle.sku.config"]});case 2:e=t.sent,r=Object(w["a"])(e,2),n=r[0],a=r[1],n&&(this.options=a.result.data);case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()}},tt=Z,et=Object(S["a"])(tt,U,K,!1,null,null,null),rt=et.exports,nt=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("el-form-item",{attrs:{label:"产品系列 ："}},[r("el-checkbox-group",{on:{change:t.changeAdults},model:{value:t.value.adults,callback:function(e){t.$set(t.value,"adults",e)},expression:"value.adults"}},t._l(t.lines,(function(e){return r("el-checkbox",{key:e.dicItemCode,attrs:{label:e.dicItemCode}},[t._v("\n        "+t._s(e.dicItemName)+"\n      ")])})),1)],1),t.sublines.length?r("el-form-item",{attrs:{label:"产品子系列 ："}},[r("el-checkbox-group",{on:{change:t.changeChildren},model:{value:t.value.children,callback:function(e){t.$set(t.value,"children",e)},expression:"value.children"}},t._l(t.sublines,(function(e){return r("el-checkbox",{key:e.dicItemCode,attrs:{label:e.dicItemCode}},[t._v("\n        "+t._s(e.dicItemName)+"\n      ")])})),1)],1):t._e()],1)},at=[],it=(r("5df3"),r("4f7f"),{props:["params","lines"],data:function(){return{sublines:[],value:{adults:[],children:[]}}},mounted:function(){this.addSelectChildenAndAdult()},methods:{addSelectChildenAndAdult:function(){var t=this.params.oiltype||[];if(t.length){var e=this.lines.map((function(t){return t.dicItemCode})),r=this.lines.filter((function(t){return t.children})).map((function(t){return t.children})).reduce((function(t,e){return[].concat(b(t),b(e))}),[]).map((function(t){return t.dicItemCode})),n=t.filter((function(t){return e.indexOf(t)>-1})),a=t.filter((function(t){return r.indexOf(t)>-1})),i=this.lines.filter((function(t){return t.children})).map((function(t){var e=t.children.map((function(t){return t.dicItemCode})),r=e.find((function(t){return a.indexOf(t)>-1}));return r?t.children:[]})).reduce((function(t,e){return[].concat(b(t),b(e))}),[]),o=b(new Set(i.map((function(t){return t.dicItemDesc}))));this.sublines=i,this.value={adults:[].concat(b(n),b(o)),children:a}}},changeAdults:function(t){var e=this.lines.filter((function(e){return t.indexOf(e.dicItemCode)>-1}));this.removeSelectChildenWithoutAdult(),this.sublines=e.filter((function(t){return t.children})).map((function(t){return t.children})).reduce((function(t,e){return[].concat(b(t),b(e))}),[]),this.params.oiltype=[].concat(b(e.filter((function(t){return!t.children})).map((function(t){return t.dicItemCode}))),b(this.value.children)),this.update()},changeChildren:function(t){var e=this,r=this.lines.filter((function(t){return e.value.adults.indexOf(t.dicItemCode)>-1}));this.params.oiltype=[].concat(b(r.filter((function(t){return!t.children})).map((function(t){return t.dicItemCode}))),b(t)),this.update()},removeSelectChildenWithoutAdult:function(){var t=this,e=this.lines.filter((function(t){return t.children})).map((function(t){return t.children})).reduce((function(t,e){return[].concat(b(t),b(e))}),[]).filter((function(e){return t.value.children.indexOf(e.dicItemCode)>-1}));this.value.children=e.filter((function(e){return t.value.adults.indexOf(e.dicItemDesc)>-1})).map((function(t){return t.dicItemCode}))},update:function(){this.params.start=0,this.$emit("change")},reset:function(){this.sublines=[],this.value.adults=[],this.value.children=[]}}}),ot=it,st=Object(S["a"])(ot,nt,at,!1,null,null,null),ct=st.exports;r("7f7f");function ut(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function lt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ut(Object(r),!0).forEach((function(e){Object(y["a"])(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ut(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function dt(t){return{sku:t.sku,amount:t.amount}}function pt(t){var e=t.products,r=t.orgId;return e.map((function(t){return lt(lt({},t),{},{orgId:r})})).map(ft)}function ft(t,e){return{deliveryId:e,partnerId:t.orgId,quantity:t.amount,revenueRmb:t.totalValue,productSku:t.sku}}function ht(t){var e=t.orderLineDetailList,r=t.salesCode,n=t.salesOrg,a={products:e};return a.totalCapacity=R.pipe(R.map(R.prop("totalLiterCount")),R.sum)(e),a.countmoney=R.pipe(R.map(R.prop("totalValue")),R.sum)(e),a.salesCode=r,a.salesOrg=n,a}function mt(t){return{sku:t.sku,amount:t.amount,ladderPrice:t.ladderPrice,promotionTitle:t.promotionTitle,remark:t.remark,discountedTotalValue:t.totalValue,freeAmount:t.freeAmount,productName:t.productName||t.name,totalValue:t.totalValue,actualAmount:t.actualAmount,totalLiterCount:t.totalLiterCount,bottlesperbox:t.bottlesperbox,capacity:t.capacity,units:"桶"==t.unitsText?"barrel":"box",price:t.ladderPrice,type:t.category,inventoryCount:t.inventoryCount,promotionSku:t.promotionSku}}var gt=["products"],vt={props:["params","cart","updateCart","options"],components:{products:rt,"select-product-line":ct},data:function(){return{saving:!1}},computed:{totalLiterCount:function(){return this.cart.data.map((function(t){return Number(t.boxCapacity||0)*Number(t.amount)})).reduce((function(t,e){return t+e}),0)},literRatio:function(){var t=this.cart,e=t.partnerSaleConfigList,r=t.partnerSaleConfigId,n=t.channelRatioList,a=t.data,i={Havoline:{value:"CDM",label:"金富力"},Delo:{value:"C&I",label:"德乐"}},o=e.find((function(t){return t.id==r}))||{},s=n.find((function(t){return t.dicItemCode==o.shipToCode}));if(!s)return"";var c=Number(s.dicItemDesc),u=i[s.dicItemName].value,l=i[s.dicItemName].label,d=a.map((function(t){return Number(t.amount)*Number(t.boxCapacity)})).reduce((function(t,e){return t+e}),0),p=a.filter((function(t){return t.productChannel==u})).map((function(t){return Number(t.amount)*Number(t.boxCapacity)})).reduce((function(t,e){return t+e}),0);return console.log("".concat(u," ").concat(c," ").concat(p," ").concat(d," ").concat(p/d)),p/d<c?"所选的".concat(l,"产品升数高于总升数的").concat(100*c,"%才能下单"):""}},methods:{search:function(){this.params.start=0,this.$emit("search")},clear:function(){this.$refs.productLine.reset(),this.params.start=0,this.params.category=[],this.params.capacity=[],this.params.oiltype=[],this.params.viscosity=[],this.$emit("search")},save:function(){var t=this,e=function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e){var r,n,a,i,o,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=e.products,n=z(e,gt),t.next=3,X["a"].requestByRPC({method:"partnerOrderService.insertPartnerOrder",params:[n,r]});case 3:if(a=t.sent,i=Object(w["a"])(a,2),o=i[0],s=i[1],o){t.next=9;break}throw s.result;case 9:return t.abrupt("return",{id:s.result.id});case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),r=function(){var r=Object(x["a"])(regeneratorRuntime.mark((function r(n){var a,i,o,s,c,u,l,d,p;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=n.cart,i=n.cart.partnerSaleConfigList.find((function(e){return e.id===t.cart.partnerSaleConfigId}))||{},o=i.id,s=i.salesOrg,c=i.sapCode,u=n.$store.getters.currentUser.orgId,l={id:a.id,receiveType:a.receiveType,partnerId:u,salesOrg:s,salesCode:c,partnerSaleConfigId:o,products:a.data.map(mt),remark:"",distId:"",address:"",status:0,contactPerson:"",contactPersonTel:"",totalLiterCount:""},r.prev=4,n.saving=!0,r.next=8,e(l);case 8:d=r.sent,p=d.id,n.saving=!1,n.cart.id=p,r.next=17;break;case 14:r.prev=14,r.t0=r["catch"](4),n.$notify.error(r.t0.errorMsg);case 17:case"end":return r.stop()}}),r,null,[[4,14]])})));return function(t){return r.apply(this,arguments)}}();r(this)}}},bt=vt,yt=Object(S["a"])(bt,A,q,!1,null,null,null),wt=yt.exports,xt=function(){var t=this,e=t.$createElement,r=t._self._c||e;return t.products.total>0?r("div",{staticStyle:{"text-align":"center",margin:"20px 0 40px"}},[r("el-pagination",{attrs:{layout:"sizes, prev, pager, next",background:"","current-page":t.page,"page-sizes":[10,20,50,100],"page-size":t.params.limit,total:t.products.total},on:{"update:currentPage":function(e){t.page=e},"update:current-page":function(e){t.page=e},"update:pageSize":function(e){return t.$set(t.params,"limit",e)},"update:page-size":function(e){return t.$set(t.params,"limit",e)},"current-change":t.change,"size-change":t.change}})],1):t._e()},Ot=[],Ct={props:["params","products"],computed:{page:{get:function(){return this.params.start/this.params.limit+1},set:function(t){this.params.start=(t-1)*this.params.limit}}},methods:{change:function(){this.$emit("change")}}},_t=Ct,kt=Object(S["a"])(_t,xt,Ot,!1,null,null,null),St=kt.exports,It={props:["step","products","cart","updateCart","createPreOrder","changeAccount"],components:{operation:E,search:wt,products:rt,pagination:St},created:function(){console.log("lai le hhh",this.$route.query.hasIndustrial)},computed:{hasIndustrial:function(){console.log("lai le hhh"),"true"===this.$route.query.hasIndustrial&&console.log("lai le hhh")}}},Pt=It,jt=Object(S["a"])(Pt,j,T,!1,null,null,null),Tt=jt.exports,Rt=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:t.detail.loading,expression:"detail.loading"}]},[r("operation",{attrs:{detail:t.detail,cart:t.cart,goBackStepOne:t.goBackStepOne}}),r("products",{attrs:{detail:t.detail}}),r("div",{staticStyle:{color:"red"}},[t._v("* 因为小数点四舍五入处理，此试算金额可能和公司出具的正式发票金额有略微差异，最终价格以正式发票金额为准！")]),r("div",{staticStyle:{padding:"5px"}},[t._v(t._s(t.detail.error))]),r("formPiece",{attrs:{detail:t.detail,cart:t.cart},on:{update:t.getPrice}}),t.detail.approvalList.length?r("process",{attrs:{detail:t.detail}}):t._e()],1)},Lt=[],Nt=(r("ffc1"),function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-row",{staticStyle:{"margin-bottom":"18px",padding:"10px","border-bottom":"1px solid #ccc"}},[r("el-col",{attrs:{span:12}},[r("h2",{staticStyle:{margin:"8px 0 0"}},[t._v("确认价格")])]),r("el-col",{staticClass:"text-right",staticStyle:{margin:"8px 0 0"},attrs:{span:12}},[r("el-button",{attrs:{type:"success"},on:{click:t.goBackStepOne}},[t._v("修改订单")]),r("el-button",{attrs:{type:"success",loading:t.saving},on:{click:t.save}},[t._v("保存订单")]),r("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("提交订单")]),r("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1),r("promotion-dialog",{attrs:{dialog:t.dialog}})],1)}),Dt=[],$t=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{staticClass:"promotion-dialog",staticStyle:{"margin-top":"10%","margin-right":"15%"},attrs:{title:"提示",visible:t.dialog.visible},on:{"update:visible":function(e){return t.$set(t.dialog,"visible",e)}}},[r("div",{staticClass:"el-message-box__content"},[r("div",{staticClass:"el-message-box__container"},[r("div",{staticClass:"el-message-box__status el-icon-warning"}),r("div",{staticClass:"el-message-box__message"},[r("p",{staticStyle:{"min-height":"20px"},domProps:{innerHTML:t._s(t.dialog.message)}})])])]),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticClass:"promotion-dialog__button--underlined",attrs:{type:"text"},on:{click:function(e){t.fileDialog.visible=!0}}},[t._v("\n      查看当前促销活动\n    ")]),r("el-button",{attrs:{type:"success"},on:{click:t.dialog.cancel}},[t._v("修改订单")]),r("el-button",{attrs:{type:"primary"},on:{click:t.dialog.confirm}},[t._v("继续结算")])],1),r("el-dialog",{staticClass:"promotion-dialog__file-dialog",staticStyle:{"margin-top":"10%"},attrs:{title:"",visible:t.fileDialog.visible,"append-to-body":""},on:{"update:visible":function(e){return t.$set(t.fileDialog,"visible",e)}}},[r("div",t._l(t.fileDialog.promotions,(function(e){return r("el-button",{key:e.id,staticStyle:{display:"block","margin-left":"0"},attrs:{type:"text"},on:{click:function(r){return t.onDownload(e)}}},[t._v("\n        "+t._s(e.promotionName)+"\n      ")])})),1)])],1)},Bt=[];function Et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function At(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Et(Object(r),!0).forEach((function(e){Object(y["a"])(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Et(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var qt={props:["dialog"],data:function(){return{fileDialog:{visible:!1,promotions:[]}}},methods:{getPromotions:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i,o,s,c,u;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByDO({path:"sellinpromotion/data.do",params:At({paging:!1,field:"id",direction:"DESC",enableFlag:1,activeFlag:1},this.dialog.params)});case 2:if(e=t.sent,r=Object(w["a"])(e,2),n=r[0],a=r[1],n){t.next=8;break}return t.abrupt("return");case 8:i=a.resultLst||[],o={commercial:"商用油",consumer:"乘用车"},s=i.map((function(t){return o[t.productChannel]})),c="".concat(s.filter((function(t,e){return s.indexOf(t)===e})).join("和")),u="",t.t0=this.dialog.messageType,t.next=1===t.t0?16:2===t.t0?18:20;break;case 16:return u="本月有".concat(c,"促销活动, 满足促销条件有积分赠送。\n                您下单的产品没有包含促销产品。"),t.abrupt("break",20);case 18:return u="本月有".concat(c,"促销活动, 满足促销条件有积分赠送。\n                您下单的产品未达到促销门槛。"),t.abrupt("break",20);case 20:this.fileDialog.promotions=i,this.dialog.message=u;case 22:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),onDownload:function(t){var e=t.attId,r="/downloadAttachmentFile.do?attId=".concat(e,"&inline=true");window.open(r,"_blank")}},watch:{"dialog.visible":function(t){t&&(this.fileDialog.promotions=[],this.dialog.message="正在获取促销内容...",this.getPromotions())}}},Mt=qt,Vt=(r("d665"),Object(S["a"])(Mt,$t,Bt,!1,null,"bbbdca7e",null)),Ft=Vt.exports,zt={props:["detail","cart","goBackStepOne"],components:{"promotion-dialog":Ft},data:function(){return{saving:!1,dialog:{visible:!1,message:"",params:{},cancel:function(){},confirm:function(){}}}},methods:{submit:function(){var t=this,e=this.getPromotion();if(e){if(this.dialog.params={partnerId:this.detail.orgId},this.dialog.cancel=function(){if(t.dialog.visible=!1,t.goBackStepOne(),window._czc){var e=t.$store.getters.currentUser,r=e.orgName,n=e.chName,a=e.userId;window._czc.push(["_trackEvent","采购订单","修改订单","".concat(r,"+").concat(n),a,""])}},this.dialog.confirm=function(){t.dialog.visible=!1,t.createOrder()},this.dialog.visible=!0,window._czc){var r=this.$store.getters.currentUser,n=r.orgName,a=r.chName,i=r.userId;window._czc.push(["_trackEvent","采购订单","促销弹框","".concat(n,"+").concat(a),i,""])}}else this.createOrder()},createOrder:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i,o,s,c,u;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.detail.contactPerson&&this.detail.contactPersonTel){t.next=2;break}return t.abrupt("return",this.$notify.error("请填写联系方式"));case 2:if(e=this.cart.limitation.volume,!(this.detail.totalCapacity<e)){t.next=5;break}return t.abrupt("return",this.$notify.error("满".concat(e,"L才能下单")));case 5:t.prev=5,this.validateRatio(this.cart),t.next=14;break;case 9:return t.prev=9,t.t0=t["catch"](5),r=t.t0.message,this.$notify.error(r),t.abrupt("return");case 14:if(!((this.detail.mark||"").length>150)){t.next=16;break}return t.abrupt("return",this.$notify.error("备注最多不超过150个字符"));case 16:return n="partnerOrderService.insertPartnerOrder",a={partnerId:this.detail.orgId,salesOrg:this.detail.salesOrg,salesCode:this.detail.salesCode,remark:this.detail.remark,distId:this.detail.distId,address:this.detail.address,contactPerson:this.detail.contactPerson,contactPersonTel:this.detail.contactPersonTel,totalLiterCount:this.detail.totalCapacity},this.detail.id?a.id=this.detail.id:a.partnerId=this.detail.orgId,a.partnerSaleConfigId=this.cart.partnerSaleConfigId,a.receiveType=this.cart.receiveType,i=this.$loading({lock:!0,text:"正在生成采购订单",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),t.next=24,X["a"].requestByRPC({method:n,params:[a,this.detail.products.map(mt)]});case 24:o=t.sent,s=Object(w["a"])(o,2),c=s[0],u=s[1],i.close(),c?(this.$router.go(-1),this.$notify.success("采购订单提交成功")):u&&u.result.errorMsg&&this.$notify.error(u&&u.result.errorMsg?u.result.errorMsg:"采购订单提交失败，请稍后重试");case 30:case"end":return t.stop()}}),t,this,[[5,9]])})));function e(){return t.apply(this,arguments)}return e}(),save:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e="partnerOrderService.insertPartnerOrder",r={status:0,partnerId:this.detail.orgId,salesOrg:this.detail.salesOrg,salesCode:this.detail.salesCode,remark:this.detail.remark,distId:this.detail.distId,address:this.detail.address,contactPerson:this.detail.contactPerson,contactPersonTel:this.detail.contactPersonTel,totalLiterCount:this.detail.totalCapacity},this.detail.id?r.id=this.detail.id:r.partnerId=this.detail.orgId,r.partnerSaleConfigId=this.cart.partnerSaleConfigId,r.receiveType=this.cart.receiveType,this.saving=!0,t.next=8,X["a"].requestByRPC({method:e,params:[r,this.detail.products.map(mt)]});case 8:n=t.sent,a=Object(w["a"])(n,2),i=a[0],o=a[1],i?(this.detail.id=o.result.id,this.$notify.success("采购订单保存成功")):this.$notify.error(o&&o.result.errorMsg?o.result.errorMsg:"采购订单保存失败，请稍后重试"),this.saving=!1;case 14:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),validateRatio:function(t){var e=t.partnerSaleConfigList,r=t.partnerSaleConfigId,n=t.channelRatioList,a=t.data,i={Havoline:{value:"CDM",label:"金富力"},Delo:{value:"C&I",label:"德乐"}},o=e.find((function(t){return t.id==r}))||{},s=n.find((function(t){return t.dicItemCode==o.shipToCode}));if(s){var c=Number(s.dicItemDesc),u=i[s.dicItemName].value,l=i[s.dicItemName].label,d=a.map((function(t){return Number(t.amount)*Number(t.boxCapacity)})).reduce((function(t,e){return t+e}),0),p=a.filter((function(t){return t.productChannel==u})).map((function(t){return Number(t.amount)*Number(t.boxCapacity)})).reduce((function(t,e){return t+e}),0);if(console.log("".concat(u," ").concat(c," ").concat(p," ").concat(d," ").concat(p/d)),p/d<c)throw{message:"所选的".concat(l,"产品升数高于总升数的").concat(100*c,"%才能下单")}}},getPromotion:function(){var t=this;return this.detail.availablePromotionCount&&this.detail.hasPromotionRules1?(this.dialog.messageType=1,!0):(this.detail.resultLst||[]).find((function(e){return t.dialog.messageType=2,e.promotionRules.length>0&&!e.deliveryDetails}))}}},Ut=zt,Kt=Object(S["a"])(Ut,Nt,Dt,!1,null,null,null),Wt=Kt.exports,Ht=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-table",{staticStyle:{"margin-top":"15px"},attrs:{size:"small",data:t.detail.products,"empty-text":"没有数据",border:"","show-summary":"","summary-method":t.getSummaries}},[r("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),r("el-table-column",{attrs:{label:"产品编号",prop:"sku",width:"120"}}),r("el-table-column",{attrs:{label:"产品名称",prop:"productName"}}),r("el-table-column",{attrs:{label:"订货数量+赠送数量",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.freeAmount?[t._v("\n        "+t._s(t._f("toMoney")(e.row.amount))+"+"+t._s(t._f("toMoney")(e.row.freeAmount))+"\n      ")]:[t._v(t._s(t._f("toMoney")(e.row.amount)))]]}}])}),r("el-table-column",{attrs:{label:"合计升数",prop:"totalLiterCount",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("toMoney")(e.row.totalLiterCount)))]}}])}),r("el-table-column",{attrs:{label:"价格",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("toMoney")(e.row.ladderPrice)))]}}])}),r("el-table-column",{attrs:{label:"合计金额（元）",prop:"totalValue",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("to2Precision")(e.row.totalValue)))]}}])}),r("el-table-column",{attrs:{label:"备注",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.remark))]}}])})],1)},Qt=[],Jt={props:["detail"],methods:{getSummaries:function(t){var e=this,r=t.columns,n=t.data,a=[];return r.forEach((function(t,r){if(0!==r){var i=n.map((function(e){return Number(e[t.property])}));if(i.every((function(t){return isNaN(t)})))a[r]="";else{var o="totalValue"==t.property?e.$options.filters.to2Precision:e.$options.filters.toMoney;a[r]=o(i.reduce((function(t,e){var r=Number(e);return isNaN(r)?t:t+e}),0))}}else a[r]="合计"})),a}}},Gt=Jt,Yt=Object(S["a"])(Gt,Ht,Qt,!1,null,null,null),Xt=Yt.exports,Zt=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("el-col",{attrs:{span:12}},[r("h3",{staticStyle:{margin:"8px 0"}},[t._v("订单流程")])]),r("el-table",{staticStyle:{"margin-top":"15px"},attrs:{size:"small",data:t.detail.approvalList,"empty-text":"没有数据",border:""}},[r("el-table-column",{attrs:{label:"序号",type:"index",width:"60"}}),r("el-table-column",{attrs:{label:"步骤",prop:"approvalStep",width:"120"}}),r("el-table-column",{attrs:{label:"处理意见",prop:"approvalComment"}}),r("el-table-column",{attrs:{label:"处理人",prop:"approvalPersonName",width:"100"}}),r("el-table-column",{attrs:{label:"处理时间",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("dayjs")(e.row.approvalTime)))]}}])})],1)],1)},te=[],ee={props:["detail"]},re=ee,ne=Object(S["a"])(re,Zt,te,!1,null,null,null),ae=ne.exports,ie=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-form",{attrs:{"label-position":"left"}},[r("el-row",[r("el-col",{attrs:{span:11}},[r("el-form-item",{staticClass:"detail-mark-label",attrs:{label:"备注："}},[r("div",{attrs:{slot:"label"},slot:"label"},[r("div",{staticStyle:{"margin-top":"1.5em"}},[t._v("备注：")])]),r("el-input",{staticStyle:{"max-width":"480px"},attrs:{type:"textarea",rows:5},model:{value:t.detail.remark,callback:function(e){t.$set(t.detail,"remark",e)},expression:"detail.remark"}})],1)],1),t.hasIndustrial?t._e():r("el-col",{attrs:{span:12,offset:1}},[r("el-form-item",{staticClass:"promotion-text-label",attrs:{label:"预计获得促销奖励："}},[r("div",{attrs:{slot:"label"},slot:"label"},[r("div",{staticStyle:{display:"flex","flex-drection":"row"}},[r("div",{staticStyle:{"min-width":"110px","margin-top":"1.5em"}},[t._v("预计获得促销奖励：")]),r("div",{staticStyle:{color:"red","font-size":"12px"}},[t._v("\n              *以下仅为预计可获得积分数，可能与最终获得积分数不一致;\n              系统会依据实际成交产品，时间及价格等是否符合积分规则来计算最终可获得积分数\n            ")])])]),r("el-input",{staticClass:"promotion-text",attrs:{type:"textarea",rows:5,disabled:""},model:{value:t.detail.promotionInfo,callback:function(e){t.$set(t.detail,"promotionInfo",e)},expression:"detail.promotionInfo"}})],1)],1),r("el-col",[r("el-form-item",{attrs:{label:"运送到：","label-width":"75px"}},[r("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择派送地址",disabled:""},model:{value:t.valueInner,callback:function(e){t.valueInner=e},expression:"valueInner"}},t._l(t.options,(function(e){return r("el-option",{key:e.id,attrs:{value:e.id,label:e.shipToCode}},[r("span",{staticStyle:{float:"left","font-weight":"bold"}},[t._v(t._s(e.shipToCode))]),r("span",{staticStyle:{float:"right",color:"#8492a6","margin-left":"5px"}},[t._v(t._s(e.provinceName)+" "+t._s(e.cityName)+" "+t._s(e.distName)+"\n              "+t._s(e.address))])])})),1)],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"联系人：","label-width":"75px",prop:"contactPerson"}},[r("el-input",{staticStyle:{width:"200px"},model:{value:t.detail.contactPerson,callback:function(e){t.$set(t.detail,"contactPerson",e)},expression:"detail.contactPerson"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"联系电话：","label-width":"75px",prop:"contactPersonTel"}},[r("el-input",{staticStyle:{width:"200px"},model:{value:t.detail.contactPersonTel,callback:function(e){t.$set(t.detail,"contactPersonTel",e)},expression:"detail.contactPersonTel"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"联系地址：","label-width":"75px",prop:"address"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{disabled:""},model:{value:t.detail.address,callback:function(e){t.$set(t.detail,"address",e)},expression:"detail.address"}})],1)],1)],1)],1)},oe=[],se=(r("a481"),r("d225")),ce=r("b0b4"),ue=r("4d20"),le=function(){function t(){Object(se["a"])(this,t)}return Object(ce["a"])(t,[{key:"getDealers",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"get",path:"partnerController/queryPartnerForCtrlBySales.do",contentType:"form",params:{salesChannel:"cio"===t.salesChannel?"Commercial":"Consumer",salesId:t.executor,limit:30,includeDmsWorkshopField:"1"}})}},{key:"getSuppliers",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{id:2,jsonrpc:"2.0",method:"supplierService.getSuppliers",params:[t.partnerId,"16"]}})}},{key:"getSalesVolume",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"get",path:"mkt/get-avg-volume-list.do",contentType:"json",data:{workshopId:t.workshopId}})}},{key:"getDealersByKeyword",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return t.urlPath?t.urlPath=t.urlPath.replace(/\//g,"-"):t.urlPath="",Object(ue["a"])({method:"get",path:"partnerController/queryPartnerForCtrl.do",contentType:"form",params:{partnerName:t.partnerName,salesChannel:"cio"===t.salesChannel?"Commercial":"Consumer",resourceId:"resource-application".concat(t.urlPath)}})}},{key:"getStoreByDealerId",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"post",path:"workshopmaster/mkt/list.do",contentType:"form",data:{start:t.pageSize*(t.page-1),limit:t.pageSize,queryType:2,workshopName:t.keyword,partnerId:t.partnerId,status:3,funFlag:t.funFlag,resourceId:"cdmMktApplySave",fromSource:"cio"===t.salesChannel?"2":"1",mktKey:t.applyType,oldMktKey:t.applyType,pageIndex:0,field:"id",direction:"DESC"}})}},{key:"getStoreByApplyType",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"post",path:"mktcdmapply/querystores.do",contentType:"form",params:{start:t.pageSize*(t.page-1),limit:t.pageSize,partnerId:t.partnerId,field:"id",signType:t.applyType},data:{workshopName:t.keyword}})}},{key:"getStoreById",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{id:2,jsonrpc:"2.0",method:"workshopMasterService.getBean",params:[t.id,t.dmsKey]}})}},{key:"getProductsByName",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"post",path:"mkt/cio/getProductByName.do",contentType:"form",data:{name:t.keyword}})}},{key:"getSellThrough",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(ue["a"])({method:"post",path:"mkt/cio/actualSellThroughAverage.do",contentType:"json",data:{workshopId:t.workshopId,chevronProductCode:t.productCode}})}},{key:"getDicItemByCode",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"dicService.getDicItemByDicTypeCode",params:t,id:1}})}},{key:"getAmountFlowConfig",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"cdm.flow.config",params:t,id:1}})}},{key:"getSalesByManager",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"potentialCustomerService.getCustomerInfoList",params:t,id:1}})}},{key:"getVisitPlanList",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"visitPlanService.getVisitPlanListByCondition",params:t,id:1}})}},{key:"savePotentialCustomer",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"potentialCustomerService.saveOrUpdate",params:t,id:1}})}},{key:"getDealersNew",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"potentialCustomerService.getCustomerList",params:t,id:1}})}},{key:"addVisitPlan",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"visitPlanService.save",params:t,id:1}})}},{key:"getVisitDetailById",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"visitPlanService.getVisitPlanById",params:t,id:1}})}},{key:"saveVisitTarget",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"customerVisitTargetService.save",params:t,id:1}})}},{key:"deleteVisitTarget",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"customerVisitTargetService.deleteByLogic",params:t,id:1}})}},{key:"getPerformanceListByCondition",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"salesPerEvalFormService.getRegionSalesTrees",params:t,id:1}})}},{key:"bachSeachDealerIsCanCreatePlan",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"visitPlanService.checkPlanList",params:t,id:1}})}},{key:"bachSaveVisitPlan",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"visitPlanService.querySave",params:t,id:1}})}},{key:"getUserInfo",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"userService.getLoginUser",params:t,id:1}})}},{key:"saveScoreByPlan",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return Object(ue["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{jsonrpc:"2.0",method:"salesPerEvalFormService.save",params:t,id:1}})}}])}(),de=new le,pe={props:["detail","cart","update"],data:function(){return{partnerSaleConfigId:"",show:!0,regionName:"",orderminnotlimit:[],showPromotional:!1}},computed:{valueInner:{get:function(){return this.partnerSaleConfigId},set:function(t){var e=this.options.find((function(e){return e.id==t}));this.detail.contactPerson=e.contactPerson,this.detail.contactPersonTel=e.contactPersonTel,this.detail.address=e.address,this.detail.distId=e.distId,this.cart.partnerSaleConfigId=e.id,this.partnerSaleConfigId=e.id,this.$emit("update")}},hasIndustrial:function(){return"true"===this.$route.query.hasIndustrial},options:function(){return this.cart.saleConfigList}},mounted:function(){this.partnerSaleConfigId=this.cart.partnerSaleConfigId,this.fetchInit(),this.fetchShipTocode()},methods:{fetchInit:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,de.getDicItemByCode(["partnersaleconfig.region.orderminnotlimit"]);case 2:e=t.sent,r=Object(w["a"])(e,2),n=r[0],a=r[1],n&&(this.orderminnotlimit=a.result.data.map((function(t){return t.dicItemName})));case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),fetchShipTocode:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i,o,s=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByDO({path:"partnerSaleConfig/queryPartnerSaleConfig.do",contentType:"form",data:{start:0,limit:2,activeFlag:1,field:"creationTime",direction:"desc",queryField:"",queryType:2,pageIndex:0,partnerName:"",partnerId:this.detail.orgId,paging:!1}});case 2:e=t.sent,r=Object(w["a"])(e,2),n=r[0],a=r[1],n&&(i=a.resultLst||[],this.regionName=(i.find((function(t){return t.id==s.valueInner}))||{}).regionName,o=this.orderminnotlimit.indexOf(this.regionName),this.showPromotional=!(o>-1));case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()}},fe=pe,he=(r("16a8"),Object(S["a"])(fe,ie,oe,!1,null,"f40d03ae",null)),me=he.exports;function ge(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ve(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ge(Object(r),!0).forEach((function(e){Object(y["a"])(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ge(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var be={props:["cart","goBackStepOne"],components:{operation:Wt,products:Xt,formPiece:me,process:ae},data:function(){return{detail:{loading:!1,products:[],addressId:"",contactPerson:"",contactPersonTel:"",distId:"",address:"",remark:"",countmoney:0,totalCapacity:0,error:"",approvalList:[]}}},created:function(){var t=function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.getContact(),!e.cart.id){t.next=4;break}return t.next=4,e.getData();case 4:return t.next=6,e.getPrice();case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();t(this)},methods:{getContact:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=this.cart.saleConfigList.find((function(t){return t.id==r.cart.partnerSaleConfigId})),this.detail.contactPerson=e.contactPerson,this.detail.contactPersonTel=e.contactPersonTel,this.detail.address=e.address,this.detail.distId=e.distId;case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getData:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByRPC({method:"partnerOrderService.queryPartnerOrderDetailWithPower",params:[this.cart.id]});case 2:e=t.sent,r=Object(w["a"])(e,2),n=r[0],a=r[1],n&&this.$set(this,"detail",ve(ve({},this.detail),{},{approvalList:a.result.approvalList}));case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getPrice:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i,o,s,c;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.detail.loading=!0,e=this.cart.partnerSaleConfigId,r=this.cart.data.map(dt),t.next=5,X["a"].requestByRPC({method:"partnerOrderService.getSapOrderUtil",params:[this.$store.getters.currentUser.orgId,e,r]});case 5:n=t.sent,a=Object(w["a"])(n,2),i=a[0],o=a[1],this.detail.loading=!1,i?(s=ve(ve(ve({},this.detail),ht(o.result)),{},{addressId:this.detail.addressId,orgId:this.$store.getters.currentUser.orgId,id:this.cart.id,receiveType:this.cart.receiveType}),this.$set(this,"detail",s),c=pt(s),this.getPromotion(c)):(this.detail.error=o.result.errorMsg,this.$notify.error(o.result.errorMsg));case 11:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getPromotion:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e){var r,n,a,i,o,s,c,u,l,d;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByDO({method:"post",path:"sellinpromotiondelivery/calpromotion.do",data:e});case 2:r=t.sent,n=Object(w["a"])(r,2),a=n[0],i=n[1],a&&(o=ve({},this.detail),o.hasPromotion=!!i.resultLst.find((function(t){return t.deliveryDetails})),o.hasPromotionRules1=i.resultLst.every((function(t){return!t.deliveryDetails&&!t.promotionRules.length>0})),o.availablePromotionCount=i.availablePromotionCount,o.resultLst=i.resultLst,s={},i.resultLst.map((function(t){(t.deliveryDetails||[]).map((function(t){var e=t.awardType+"/"+t.extProperty2+"/"+t.extProperty3;s[e]?s[e].awardQuantity+=t.awardQuantity:s[e]=t}))})),o.promotionInfo=Object.entries(s).reduce((function(t,e){return[].concat(b(t),[e[1]])}),[]).map((function(t){return"赠"+t.extProperty1+" "+t.awardQuantity+t.extProperty4})).join(";"),this.$set(this,"detail",o),i.availablePromotionCount&&window._czc&&(c=this.$store.getters.currentUser,u=c.orgName,l=c.chName,d=c.userId,window._czc.push(["_trackEvent","采购订单","获取到促销信息","".concat(u,"+").concat(l),d,""])));case 7:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()}},ye=be,we=Object(S["a"])(ye,Rt,Lt,!1,null,null,null),xe=we.exports,Oe=r("5a0c"),Ce=r.n(Oe),_e=r("e418"),ke=r.n(_e);function Se(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ie(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Se(Object(r),!0).forEach((function(e){Object(y["a"])(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Se(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}Ce.a.extend(ke.a);var Pe={components:{step:P,product:Tt,cart:xe},data:function(){return{step:1,products:{loading:!1,params:{keyword:"",category:[],capacity:[],oiltype:[],viscosity:[],channel:[]},options:{loading:!1,category:[],capacity:[],oiltype:[],viscosity:[],channel:[],excludeCai:[]},data:[],total:0},cart:{hasSaleConfig:!1,loading:!1,id:"",data:[],partnerSaleConfigList:[],receiveType:"",saleConfigList:[],partnerSaleConfigId:"",receiveTypeList:[],channelRatioList:[],unlimitedDistributors:[],limitation:{volume:0,message:""}},dialog:{visible:!1},isAccountChangable:!0,orderminnotlimit:[],showSign:!0}},computed:{currentUser:function(){return this.$store.getters.currentUser},cashList:function(){return this.cart.partnerSaleConfigList.filter((function(t){return"cash"==t.paymentTerm}))},creditList:function(){return this.cart.partnerSaleConfigList.filter((function(t){return"credit"==t.paymentTerm}))},hasIndustrial:function(){return"true"===this.$route.query.hasIndustrial}},created:function(){this.getSearchOptions(),this.fetchInit()},mounted:function(){var t=function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getChannelRatios();case 2:return t.next=4,e.getAccounTypes();case 4:return t.next=6,e.getReceiveTypes();case 6:return t.next=8,e.getCart();case 8:return t.next=10,e.getTableData();case 10:return t.next=12,e.getUnlimitedDistributors();case 12:e.cart.partnerSaleConfigId||(e.dialog.visible=!0);case 13:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();t(this)},methods:{fetchInit:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,de.getDicItemByCode(["partnersaleconfig.region.orderminnotlimit"]);case 2:e=t.sent,r=Object(w["a"])(e,2),n=r[0],a=r[1],n&&(this.orderminnotlimit=a.result.data.map((function(t){return t.dicItemName})));case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),changeCart:function(t){console.log("changeCart",t),console.log("---\x3e",this.orderminnotlimit);var e=this.orderminnotlimit.indexOf(t.regionName);this.showSign=!(e>-1)},getTableData:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i,o,s=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=this.products.params,r=this.$store.getters.currentUser.orgId,this.products.loading=!0,t.next=5,X["a"].requestByRPC({method:"productServiceImpl.queryProductAndPartnerInventoryByMultiParam",params:[e.keyword,"","",{categoryList:e.category,viscosityList:e.viscosity,oilTypeList:e.oiltype,capacityList:e.capacity,productChannelList:e.channel},r]});case 5:n=t.sent,a=Object(w["a"])(n,2),i=a[0],o=a[1],this.products.loading=!1,i&&(this.products.total=o.result.lst.length,this.products.data=[],o.result.lst.map((function(t){t.orgId=r,s.products.data.push(s.cart.data.find((function(e){return e.sku===t.sku}))||t)})));case 11:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getLimitationDescription:function(t){var e=this.getLimitation(t),r=e.description;return r},getLimitation:function(t){var e={SELF:3e3,POST:5e3};if(e[t]){var r={volume:e[t],description:": 每单起订量大于等于".concat(e[t]/1e3,"KL")},n=this.cart,a=n.unlimitedDistributors,i=n.partnerSaleConfigList,o=n.partnerSaleConfigId,s=this.currentUser.orgId,c=a.find((function(t){return t.dicItemCode==s})),u=(c||{}).dicItemDesc||"{}",l=JSON.parse(u),d=l.start_date,p=l.end_date,f=Ce()().isBetween(d,p,"day","[]");if(!c||isNaN(Number(l[t]))||!f)return r;var h="".concat(t,"_Tips"),m=!l[h]&&0==l[t];if(m)return{volume:0,description:""};var g=(i.find((function(t){return t.id==o}))||{}).shipToCode;if(!l.shipTo||String(l.shipTo).includes(g)){var v=l[h];return{volume:Number(l[t]),description:v?": ".concat(v):""}}return r}this.$notify.error({message:"未配置收货模式，请联系雪佛龙销售",duration:7e3})},getUnlimitedDistributors:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getOptions("SellinOrder.PartnerQtyLimit");case 2:e=t.sent,this.cart.unlimitedDistributors=e;case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getChannelRatios:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getOptions("SellinOrder.productChannelRatio");case 2:e=t.sent,this.cart.channelRatioList=e;case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getReceiveTypes:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getOptions("SellinOrder.receiveType");case 2:e=t.sent,console.log("receiveTypeList---\x3e",e),this.cart.receiveTypeList=e,this.cart.receiveType=1==e.length?e[0].dicItemCode:"";case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getSearchOptions:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.products.options.loading=!0,t.next=3,this.getOptions("ProductChannel");case 3:return t.t0=t.sent,t.next=6,this.getOptions("product.category");case 6:return t.t1=t.sent,t.next=9,this.getOptions("product.capacity");case 9:return t.t2=t.sent,t.next=12,this.getProductLine();case 12:return t.t3=t.sent,t.next=15,this.getOptions("product.viscosity");case 15:return t.t4=t.sent,t.next=18,this.getOptions("partner_order_check_by_cai");case 18:t.t5=t.sent,e={channel:t.t0,category:t.t1,capacity:t.t2,oiltype:t.t3,viscosity:t.t4,excludeCai:t.t5},this.products.options.loading=!1,this.products.options=e;case 22:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getProductLine:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.getOptions("product.productLine");case 2:return e=t.sent,r=e.map((function(t){return t.dicItemCode})),t.next=6,this.getOptions("product.oilType");case 6:return n=t.sent,a=e.map((function(t){return Ie(Ie({},t),{},{children:n.filter((function(e){return e.dicItemDesc==t.dicItemCode}))})})),i=n.filter((function(t){return-1==r.indexOf(t.dicItemDesc)})),t.abrupt("return",[].concat(b(i),b(a)));case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getOptions:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e){var r,n,a,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByRPC({method:"dicService.getDicItemByDicTypeCode",params:[e]});case 2:if(r=t.sent,n=Object(w["a"])(r,2),a=n[0],i=n[1],!a){t.next=8;break}return t.abrupt("return",i.result.data);case 8:return t.abrupt("return",[]);case 9:case"end":return t.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}(),getCart:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e,r,n,a=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.$route.query.id,r=0==this.$route.query.status,n=function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e,r){var n,i,o,s,c,u;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByRPC({method:"partnerOrderService.queryPartnerOrderDetailWithPower",params:[r]});case 2:n=t.sent,i=Object(w["a"])(n,2),o=i[0],s=i[1],c={orderLineVoLst:[],partnerOrderVo:{id:r}},o&&(c=Ie(Ie({},c),s.result)),e.cart.data=c.orderLineVoLst,e.cart.id=c.partnerOrderVo.id,e.cart.partnerSaleConfigId=c.partnerOrderVo.partnerSaleConfigId,e.cart.receiveType=c.partnerOrderVo.receiveType,u=a.cart.partnerSaleConfigList.find((function(t){return t.id==c.partnerOrderVo.partnerSaleConfigId})),a.changeCart(u);case 14:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}(),e){t.next=5;break}return t.abrupt("return");case 5:return t.next=7,n(this,e);case 7:r?this.goBackStepOne():(this.cart.loading=!1,this.products.loading=!1,this.goNextStep());case 8:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),changeAccount:function(){console.log("first"),this.dialog.visible=!0},getAccounTypes:function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=function(){var t=Object(x["a"])(regeneratorRuntime.mark((function t(e){var r,n,a,i,o,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,X["a"].requestByDO({path:"partnerSaleConfig/queryPartnerSaleConfig.do",contentType:"form",data:{start:0,limit:2,activeFlag:1,field:"creationTime",direction:"desc",queryField:"",queryType:2,pageIndex:0,partnerName:"",partnerId:e.currentUser.orgId,paging:!1}});case 2:if(r=t.sent,n=Object(w["a"])(r,2),a=n[0],i=n[1],!a){t.next=17;break}if(o=i.resultLst&&i.resultLst.length,e.cart.hasSaleConfig=o,o){t.next=12;break}return e.$notify.error({message:"未配置下单信息，请联系雪佛龙销售",duration:7e3}),t.abrupt("return");case 12:s=i.resultLst||[],e.cart.partnerSaleConfigList=s,e.cart.partnerSaleConfigId==s.length==1&&s[0].id,t.next=18;break;case 17:i&&i.result&&e.$notify.error(i.result.errorMsg);case 18:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),t.next=3,e(this);case 3:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),updateCart:function(t){Number(t.amount)>0?this.cart.data.find((function(e){return e.id===t.id}))||this.cart.data.push(t):this.cart.data=R.filter((function(e){return e.id!==t.id}))(this.cart.data)},createPreOrder:function(){var t=this.cart.partnerSaleConfigId,e=this.cart.partnerSaleConfigList.find((function(e){return e.id==t}));console.log("🚀 ~ createPreOrder ~ partnerSaleConfig:",this.cart.partnerSaleConfigList);var r=this.products.options.excludeCai;if(r.some((function(t){return t.dicItemCode==e.salesCai}))){var n=["503005LPB","503007LPB"],a=this.cart.data.filter((function(t){return n.includes(t.sku)}));if(a.length>0){var i=a.reduce((function(t,e){return t+(e.amount||0)}),0);if(i<500)return void this.$notify.error("订单中含有503005LPB产品或503007LPB产品，它们总数量(单个或之和)需大于或等于500才可下单，请您重新修改后再试!")}}if(!this.hasIndustrial){this.cart.limitation=this.getLimitation(this.cart.receiveType);var o=this.cart.limitation.volume,s=this.cart.data.map((function(t){return(Number(t.boxCapacity)||0)*(Number(t.amount)||0)})).reduce((function(t,e){return t+e}),0);if(s<o)return void this.$notify.error("满".concat(o,"L才能下单"))}if(this.cart.partnerSaleConfigId)if(this.cart.data.length<=0)this.$notify.error("请选择要采购的商品");else{try{this.validateRatio(this.cart)}catch(u){var c=u.message;return void this.$notify.error(c)}this.goNextStep()}else this.dialog.visible=!0},validateRatio:function(t){var e=t.partnerSaleConfigList,r=t.partnerSaleConfigId,n=t.channelRatioList,a=t.data,i={Havoline:{value:"CDM",label:"金富力"},Delo:{value:"C&I",label:"德乐"}},o=e.find((function(t){return t.id==r}))||{},s=n.find((function(t){return t.dicItemCode==o.shipToCode}));if(s){var c=Number(s.dicItemDesc),u=i[s.dicItemName].value,l=i[s.dicItemName].label,d=a.map((function(t){return Number(t.amount)*Number(t.boxCapacity)})).reduce((function(t,e){return t+e}),0),p=a.filter((function(t){return t.productChannel==u})).map((function(t){return Number(t.amount)*Number(t.boxCapacity)})).reduce((function(t,e){return t+e}),0);if(console.log("".concat(u," ").concat(c," ").concat(p," ").concat(d," ").concat(p/d)),p/d<c)throw{message:"所选的".concat(l,"产品升数高于总升数的").concat(100*c,"%才能下单")}}},goNextStep:function(){var t=this;if(this.dialog.visible&&(this.dialog.visible=!1),this.cart.data&&this.cart.data.length){var e=this.cart.partnerSaleConfigList.find((function(e){return e.id==t.cart.partnerSaleConfigId})).paymentTerm;this.cart.saleConfigList=this.cart.partnerSaleConfigList.filter((function(t){return t.paymentTerm==e})),this.step=2}},goBackStepOne:function(){this.step=1},goBack:function(){2===this.step&&this.step}}},je=Pe,Te=(r("cca6"),Object(S["a"])(je,n,a,!1,null,null,null));e["default"]=Te.exports},"27ee":function(t,e,r){var n=r("23c6"),a=r("2b4c")("iterator"),i=r("84f2");t.exports=r("8378").getIteratorMethod=function(t){if(void 0!=t)return t[a]||t["@@iterator"]||i[n(t)]}},"2fdb":function(t,e,r){"use strict";var n=r("5ca1"),a=r("d2c8"),i="includes";n(n.P+n.F*r("5147")(i),"String",{includes:function(t){return!!~a(this,t,i).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"33a4":function(t,e,r){var n=r("84f2"),a=r("2b4c")("iterator"),i=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||i[a]===t)}},"3b17":function(t,e,r){},"4a59":function(t,e,r){var n=r("9b43"),a=r("1fa8"),i=r("33a4"),o=r("cb7c"),s=r("9def"),c=r("27ee"),u={},l={};e=t.exports=function(t,e,r,d,p){var f,h,m,g,v=p?function(){return t}:c(t),b=n(r,d,e?2:1),y=0;if("function"!=typeof v)throw TypeError(t+" is not iterable!");if(i(v)){for(f=s(t.length);f>y;y++)if(g=e?b(o(h=t[y])[0],h[1]):b(t[y]),g===u||g===l)return g}else for(m=v.call(t);!(h=m.next()).done;)if(g=a(m,b,h.value,e),g===u||g===l)return g};e.BREAK=u,e.RETURN=l},"4f7f":function(t,e,r){"use strict";var n=r("c26b"),a=r("b39a"),i="Set";t.exports=r("e0b8")(i,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return n.def(a(this,i),t=0===t?0:t,t)}},n)},"504c":function(t,e,r){var n=r("0d58"),a=r("6821"),i=r("52a7").f;t.exports=function(t){return function(e){var r,o=a(e),s=n(o),c=s.length,u=0,l=[];while(c>u)i.call(o,r=s[u++])&&l.push(t?[r,o[r]]:o[r]);return l}}},5147:function(t,e,r){var n=r("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[n]=!1,!"/./"[t](e)}catch(a){}}return!0}},5698:function(t,e,r){r("d256"),t.exports=r("a7d3").Object.getOwnPropertySymbols},"5cc5":function(t,e,r){var n=r("2b4c")("iterator"),a=!1;try{var i=[7][n]();i["return"]=function(){a=!0},Array.from(i,(function(){throw 2}))}catch(o){}t.exports=function(t,e){if(!e&&!a)return!1;var r=!1;try{var i=[7],s=i[n]();s.next=function(){return{done:r=!0}},i[n]=function(){return s},t(i)}catch(o){}return r}},"5df3":function(t,e,r){"use strict";var n=r("02f4")(!0);r("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,r=this._i;return r>=e.length?{value:void 0,done:!0}:(t=n(e,r),this._i+=t.length,{value:t,done:!1})}))},6762:function(t,e,r){"use strict";var n=r("5ca1"),a=r("c366")(!0);n(n.P,"Array",{includes:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),r("9c6c")("includes")},"67ab":function(t,e,r){var n=r("ca5a")("meta"),a=r("d3f4"),i=r("69a8"),o=r("86cc").f,s=0,c=Object.isExtensible||function(){return!0},u=!r("79e5")((function(){return c(Object.preventExtensions({}))})),l=function(t){o(t,n,{value:{i:"O"+ ++s,w:{}}})},d=function(t,e){if(!a(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!i(t,n)){if(!c(t))return"F";if(!e)return"E";l(t)}return t[n].i},p=function(t,e){if(!i(t,n)){if(!c(t))return!0;if(!e)return!1;l(t)}return t[n].w},f=function(t){return u&&h.NEED&&c(t)&&!i(t,n)&&l(t),t},h=t.exports={KEY:n,NEED:!1,fastKey:d,getWeak:p,onFreeze:f}},"8e6e":function(t,e,r){var n=r("5ca1"),a=r("990b"),i=r("6821"),o=r("11e9"),s=r("f1ae");n(n.S,"Object",{getOwnPropertyDescriptors:function(t){var e,r,n=i(t),c=o.f,u=a(n),l={},d=0;while(u.length>d)r=c(n,e=u[d++]),void 0!==r&&s(l,e,r);return l}})},"990b":function(t,e,r){var n=r("9093"),a=r("2621"),i=r("cb7c"),o=r("7726").Reflect;t.exports=o&&o.ownKeys||function(t){var e=n.f(i(t)),r=a.f;return r?e.concat(r(t)):e}},a6fa:function(t,e,r){"use strict";r.d(e,"a",(function(){return d}));var n=r("b258"),a=r.n(n),i=r("5927"),o=r.n(i);function s(t){return s="function"==typeof a.a&&"symbol"==typeof o.a?function(t){return typeof t}:function(t){return t&&"function"==typeof a.a&&t.constructor===a.a&&t!==a.a.prototype?"symbol":typeof t},s(t)}var c=r("071f"),u=r.n(c);function l(t,e){if("object"!=s(t)||!t)return t;var r=t[u.a];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(t){var e=l(t,"string");return"symbol"==s(e)?e:e+""}},a818:function(t,e,r){},adae:function(t,e,r){},b0b4:function(t,e,r){"use strict";r.d(e,"a",(function(){return s}));var n=r("ec5b"),a=r.n(n),i=r("a6fa");function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),a()(t,Object(i["a"])(n.key),n)}}function s(t,e,r){return e&&o(t.prototype,e),r&&o(t,r),a()(t,"prototype",{writable:!1}),t}},b39a:function(t,e,r){var n=r("d3f4");t.exports=function(t,e){if(!n(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},bd86:function(t,e,r){"use strict";r.d(e,"a",(function(){return o}));var n=r("ec5b"),a=r.n(n),i=r("a6fa");function o(t,e,r){return(e=Object(i["a"])(e))in t?a()(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}},c26b:function(t,e,r){"use strict";var n=r("86cc").f,a=r("2aeb"),i=r("dcbc"),o=r("9b43"),s=r("f605"),c=r("4a59"),u=r("01f9"),l=r("d53b"),d=r("7a56"),p=r("9e1e"),f=r("67ab").fastKey,h=r("b39a"),m=p?"_s":"size",g=function(t,e){var r,n=f(e);if("F"!==n)return t._i[n];for(r=t._f;r;r=r.n)if(r.k==e)return r};t.exports={getConstructor:function(t,e,r,u){var l=t((function(t,n){s(t,l,e,"_i"),t._t=e,t._i=a(null),t._f=void 0,t._l=void 0,t[m]=0,void 0!=n&&c(n,r,t[u],t)}));return i(l.prototype,{clear:function(){for(var t=h(this,e),r=t._i,n=t._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete r[n.i];t._f=t._l=void 0,t[m]=0},delete:function(t){var r=h(this,e),n=g(r,t);if(n){var a=n.n,i=n.p;delete r._i[n.i],n.r=!0,i&&(i.n=a),a&&(a.p=i),r._f==n&&(r._f=a),r._l==n&&(r._l=i),r[m]--}return!!n},forEach:function(t){h(this,e);var r,n=o(t,arguments.length>1?arguments[1]:void 0,3);while(r=r?r.n:this._f){n(r.v,r.k,this);while(r&&r.r)r=r.p}},has:function(t){return!!g(h(this,e),t)}}),p&&n(l.prototype,"size",{get:function(){return h(this,e)[m]}}),l},def:function(t,e,r){var n,a,i=g(t,e);return i?i.v=r:(t._l=i={i:a=f(e,!0),k:e,v:r,p:n=t._l,n:void 0,r:!1},t._f||(t._f=i),n&&(n.n=i),t[m]++,"F"!==a&&(t._i[a]=i)),t},getEntry:g,setStrong:function(t,e,r){u(t,e,(function(t,r){this._t=h(t,e),this._k=r,this._l=void 0}),(function(){var t=this,e=t._k,r=t._l;while(r&&r.r)r=r.p;return t._t&&(t._l=r=r?r.n:t._t._f)?l(0,"keys"==e?r.k:"values"==e?r.v:[r.k,r.v]):(t._t=void 0,l(1))}),r?"entries":"values",!r,!0),d(e)}}},cca6:function(t,e,r){"use strict";r("3b17")},d225:function(t,e,r){"use strict";function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}r.d(e,"a",(function(){return n}))},d2c8:function(t,e,r){var n=r("aae3"),a=r("be13");t.exports=function(t,e,r){if(n(e))throw TypeError("String#"+r+" doesn't accept regex!");return String(a(t))}},d665:function(t,e,r){"use strict";r("adae")},dcbc:function(t,e,r){var n=r("2aba");t.exports=function(t,e,r){for(var a in e)n(t,a,e[a],r);return t}},e0b8:function(t,e,r){"use strict";var n=r("7726"),a=r("5ca1"),i=r("2aba"),o=r("dcbc"),s=r("67ab"),c=r("4a59"),u=r("f605"),l=r("d3f4"),d=r("79e5"),p=r("5cc5"),f=r("7f20"),h=r("5dbc");t.exports=function(t,e,r,m,g,v){var b=n[t],y=b,w=g?"set":"add",x=y&&y.prototype,O={},C=function(t){var e=x[t];i(x,t,"delete"==t||"has"==t?function(t){return!(v&&!l(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return v&&!l(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,r){return e.call(this,0===t?0:t,r),this})};if("function"==typeof y&&(v||x.forEach&&!d((function(){(new y).entries().next()})))){var _=new y,k=_[w](v?{}:-0,1)!=_,S=d((function(){_.has(1)})),I=p((function(t){new y(t)})),P=!v&&d((function(){var t=new y,e=5;while(e--)t[w](e,e);return!t.has(-0)}));I||(y=e((function(e,r){u(e,y,t);var n=h(new b,e,y);return void 0!=r&&c(r,g,n[w],n),n})),y.prototype=x,x.constructor=y),(S||P)&&(C("delete"),C("has"),g&&C("get")),(P||k)&&C(w),v&&x.clear&&delete x.clear}else y=m.getConstructor(e,t,g,w),o(y.prototype,r),s.NEED=!0;return f(y,t),O[t]=y,a(a.G+a.W+a.F*(y!=b),O),v||m.setStrong(y,t,g),y}},e341:function(t,e,r){var n=r("d13f");n(n.S+n.F*!r("7d95"),"Object",{defineProperty:r("3adc").f})},e418:function(t,e,r){!function(e,r){t.exports=r()}(0,(function(){"use strict";return function(t,e,r){e.prototype.isBetween=function(t,e,n,a){var i=r(t),o=r(e),s="("===(a=a||"()")[0],c=")"===a[1];return(s?this.isAfter(i,n):!this.isBefore(i,n))&&(c?this.isBefore(o,n):!this.isAfter(o,n))||(s?this.isBefore(i,n):!this.isAfter(i,n))&&(c?this.isAfter(o,n):!this.isBefore(o,n))}}}))},ec5b:function(t,e,r){r("e341");var n=r("a7d3").Object;t.exports=function(t,e,r){return n.defineProperty(t,e,r)}},f1ae:function(t,e,r){"use strict";var n=r("86cc"),a=r("4630");t.exports=function(t,e,r){e in t?n.f(t,e,a(0,r)):t[e]=r}},f605:function(t,e){t.exports=function(t,e,r,n){if(!(t instanceof e)||void 0!==n&&n in t)throw TypeError(r+": incorrect invocation!");return t}},ffc1:function(t,e,r){var n=r("5ca1"),a=r("504c")(!0);n(n.S,"Object",{entries:function(t){return a(t)}})}}]);