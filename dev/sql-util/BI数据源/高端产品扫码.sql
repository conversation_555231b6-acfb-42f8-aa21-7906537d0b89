--新数据源
			SELECT osp.actual_out_count * CONVERT(FLOAT, tp.capacity) AS liters
				   , wto.work_shop_id
				   , ws.partner_id
				   , wto.create_time
				   , tp.sku
				   , bp.tier_category
				   , YEAR(wto.create_time) [year]
				   , MONTH(wto.create_time) [month]
			FROM wx_t_order wto
			LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
			LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
			LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
			JOIN PP_MID.dbo.bi_product bp ON bp.product_code_SAP = tp.sku
			WHERE wto.status IN (10, 11)
		    AND ws.delete_flag = 0w
			AND bp.tier_category = 'Premium'
			AND bp.product_channel = 'Consumer'
			AND YEAR(wto.create_time)>=2021
			
			

