CREATE PROCEDURE [dbo].[pr_syn_distributor_rel] 
as
begin
	SET NOCOUNT ON
	begin tran;
	begin try
		/*遍历变动关系*/
		declare cursor_diff cursor
    	for
		 select distinct a.distributor_id,a.customer_name_cn customer_name,a.sales_cai new_sales_cai,a.sales_name_cn new_sales_name, 
		 cos1.sales_cai old_sales_cai, cos1.sales_name_cn old_sales_name,a.region
			from PP_MID.dbo.syn_dw_to_pp_customer_org_sales a
			left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on a.org_hier_id=so.org_hier_id
			left join dw_region_sales_channel_rel rr1 on rr1.region_name=a.region
			left join dw_customer_org_sales cos1 on cos1.distributor_id=a.distributor_id and cos1.product_channel=a.product_channel
			left join dw_region_sales_channel_rel rr2 on rr2.region_name=cos1.region
		  where rr1.bu='Indirect' and rr2.bu='Indirect' and cos1.from_source='BI' and a.sales_cai!=cos1.sales_cai
		 
		declare @distributor_id bigint,
		@customer_name varchar(150),
        @new_sales_cai varchar(50),
        @new_sales_name varchar(150),
        @old_sales_cai varchar(50),
        @old_sales_name varchar(150),  
		@region varchar(150),
        @year int,
        @brand int,
        @online_amount decimal(20,6),
		@new_sales_budget_count int
        
        select @year=isnull((select dic_item_name from wx_t_dic_item where dic_type_code='Budget2021.overspendNotify' and dic_item_code='year'), year(getdate()))
        --创建临时表存经销商费用
		create table #temp_distributor_expense (brand int, online_amount decimal(20,6))

    	open cursor_diff
        fetch next from cursor_diff
        into @distributor_id,@customer_name,@new_sales_cai,@new_sales_name,@old_sales_cai,@old_sales_name,@region
        WHILE @@FETCH_STATUS = 0
        begin
			insert into #temp_distributor_expense EXEC pp_sp_distributor_expense @distributor_id,null,null,'commercial,consumer',null,@year,33792,null,7
	        /*转移费用*/
	        declare cursor_expense cursor
    		for
            select brand,online_amount from #temp_distributor_expense
            
	    	open cursor_expense
	        fetch next from cursor_expense
	        into @brand,@online_amount
	        
	        WHILE @@FETCH_STATUS = 0
	        begin
		        if @online_amount>0
		        begin
			        /*费用迁出*/
			        insert into wx_t_budget2021_log  ([log_type]
			           ,[budget_year]
			           ,[budget_type]
			           ,[brand]
			           ,[budget_key]
			           ,[offset]
			           ,[final_value]
			           ,[value_type]
			           ,[create_user_id]
			           ,[create_time]
			           ,[comments]) values (-1, @year,3,@brand,@old_sales_cai,@online_amount,(select b.region_budget_value-@online_amount 
			           		from wx_t_budget2021 b where b.budget_year=@year and b.brand=@brand and b.budget_type=3 and b.budget_key=@old_sales_cai),1,1,getdate(),
			           		'【' + @customer_name + '】关系转出到【' + @new_sales_name + '】')
			        update b set b.region_budget_value=b.region_budget_value-@online_amount from  wx_t_budget2021 b 
			        	where b.budget_year=@year and b.brand=@brand and b.budget_type=3 and b.budget_key=@old_sales_cai
			        /*费用迁入*/
			        insert into wx_t_budget2021_log  ([log_type]
			           ,[budget_year]
			           ,[budget_type]
			           ,[brand]
			           ,[budget_key]
			           ,[offset]
			           ,[final_value]
			           ,[value_type]
			           ,[create_user_id]
			           ,[create_time]
			           ,[comments]) values (1, @year,3,@brand,@new_sales_cai,@online_amount,isnull((select b.region_budget_value 
			           		from wx_t_budget2021 b where b.budget_year=@year and b.brand=@brand and b.budget_type=3 and b.budget_key=@new_sales_cai), 0) + @online_amount,1,1,getdate(),
			           		'【' + @customer_name + '】关系从【' + @old_sales_name+'】转入')
					--费用迁入新员工，新增预算记录
					set @new_sales_budget_count=0
					select @new_sales_budget_count=count(1) from wx_t_budget2021 b where b.budget_year=@year and b.brand=@brand and b.budget_type=3 and b.budget_key=@new_sales_cai
					if @new_sales_budget_count<1
						insert into wx_t_budget2021 ([budget_year],[brand],[budget_type],[budget_key],[budget_titile]
							,[channel_weight],[region_budget_value],[create_user_id],[create_time]
							,[update_user_id],[update_time],[budget_partner_key]) values (@year,@brand,3,@new_sales_cai,@new_sales_name,
							(case when @brand&6>0 then 2 else 1 end),@online_amount,1,getdate(),1,getdate(),@region)
					else
						update b set b.region_budget_value=b.region_budget_value+@online_amount from  wx_t_budget2021 b 
			        		where b.budget_year=@year and b.brand=@brand and b.budget_type=3 and b.budget_key=@new_sales_cai
				end
					
				fetch next from cursor_expense
				into @brand,@online_amount
		    end 
			--关闭游标
			close cursor_expense
			--删除游标
			deallocate cursor_expense
			truncate table #temp_distributor_expense
			
            fetch next from cursor_diff
        	into @distributor_id,@customer_name,@new_sales_cai,@new_sales_name,@old_sales_cai,@old_sales_name,@region
        end
	    --关闭游标
	    close cursor_diff
	    --删除游标
	    deallocate cursor_diff
		drop table #temp_distributor_expense
	    --转移关系
delete cso1 from dw_customer_org_sales cso1 where from_source='BI'
insert into dw_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[product_channel]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[supervisor_cai]
      ,[supervisor_name_cn]
      ,[effective_from_date]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[customer_type]
      ,[from_source]
	  ,channel_weight)
 select a.[distributor_id]
      ,a.[customer_name_cn]
      ,a.[org_hier_id]
      ,a.[region]
      ,a.[product_channel]
      ,a.[sales_cai]
      ,a.[sales_name_cn]
      ,a.[supervisor_cai]
      ,a.[supervisor_name_cn]
      ,a.[effective_from_date]
      ,a.[create_time]
      ,a.[update_time]
      ,a.[del_flag]
      ,a.[customer_type]
      ,'BI' [from_source],
	  case when so.bu='Indirect' then (case when a.product_channel='Consumer' 
			then 1 when a.product_channel='Commercial' then 2 else 0 end) 
			when so.sales_channel='OEM' then 4 
			when so.sales_channel='Industrial' then 8
			else null end channel_weight 
	from PP_MID.dbo.syn_dw_to_pp_customer_org_sales a
	left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on a.org_hier_id=so.org_hier_id
  where not exists (select 1 from dw_customer_org_sales dac1 where dac1.from_source='PP' and a.region=dac1.region 
	and a.distributor_id=dac1.distributor_id and a.product_channel=dac1.product_channel)
	    
		commit
	end try
	begin catch
		rollback tran;
		insert into wx_log (operator,log_type,ext_property1,ext_property2,create_time) values (1,'syn.distributor.rel.error','BI经销商关系同步',ERROR_MESSAGE(),getdate());
	    --关闭游标
	    close cursor_diff
	    --删除游标
	    deallocate cursor_diff
		drop table if exists #temp_distributor_expense
		--关闭游标
		close cursor_expense 
		--删除游标
		deallocate cursor_expense
	end catch
END