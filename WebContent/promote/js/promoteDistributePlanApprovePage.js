$(document).ready(function(){
    initGrid(); 
    initTab();
});
var foo = new $.JsonRpcClient({
	ajaxUrl: '/wxPublicRpc.do'
});

var daichuliGrid,yichuliGrid,yishenheGrid,daishenheGrid;
var daichuliStore,yichuliStore,yishenheStore,daishenheStore;
function initGrid(){
    BUI.use(["bui/grid",'bui/data'],function(Grid,Data){
        var columns1=[
            {
                title:'序号',
                width:'50px',
                renderer:function (v,o, index){
                    return index+1;
                },
                sortable:false
            },{
                title:'标题',
                width:'45%',
                dataIndex:'batchTitle',
                sortable:false
            },{
                title:'创建日期',
                width:'15%',
                dataIndex:'createTime',
                renderer:function(value,obj,index){
                    var d=new Date(value);
                    return d.getFullYear()+'-'+d.getMonth()+'-'+d.getDate()+" "+d.getHours()+':'+d.getMinutes()+':'+d.getSeconds();
                },
                sortable:false
            },{
                title:'状态',
                width:'10%',
                dataIndex:'approveStatus',
                renderer:function(value,obj,index){
                    if(value=='1'){
                        return '已提交';
                    }
                    if(value=='0'){
                        return '暂存';
                    }
                    if(value=='2'){
                        return "审核中";
                    }
                    if(value=='3'){
                        return '审核通过';
                    }
                    if(value=='4'){
                        return '已驳回';
                    }
                },
                sortable:false
            },{
                title:'操作',
                width:'20%',
                renderer:function(value,obj){
                    return '<a href="javascript:void(0);" onclick="modify('+obj.batchid+')">修改</a>&nbsp;'+
                    '<a href="javascript:void(0);" onclick="showApproveHistory('+obj.batchid+')">审核历史</a>';
                },
                sortable:false
            }
        ];
        var columns2=[
            {
                title:'序号',
                width:'50px',
                renderer:function (v,o, index){
                    return index+1;
                },
                sortable:false
            },{
                title:'标题',
                width:'45%',
                dataIndex:'batchTitle',
                sortable:false
            },{
                title:'创建日期',
                width:'15%',
                dataIndex:'createTime',
                renderer:function(value,obj,index){
                    var d=new Date(value);
                    return d.getFullYear()+'-'+d.getMonth()+'-'+d.getDate()+" "+d.getHours()+':'+d.getMinutes()+':'+d.getSeconds();
                },
                sortable:false
            },{
                title:'状态',
                width:'10%',
                dataIndex:'approveStatus',
                renderer:function(value,obj,index){
                    if(value=='1'){
                        return '已提交';
                    }
                    if(value=='0'){
                        return '暂存';
                    }
                    if(value=='2'){
                        return "审核中";
                    }
                    if(value=='3'){
                        return '审核通过';
                    }
                    if(value=='4'){
                        return '已驳回';
                    }
                },
                sortable:false
            },{
                title:'操作',
                width:'20%',
                renderer:function(value,obj){
                    var actionStr='<a href="javascript:void(0);" onclick="showDetail('+obj.batchid+','+obj.parentDistributionId+')">详情</a>';
                    if(promoteUserType!='channelManager'){
                        actionStr=actionStr+'&nbsp;<a href="javascript:void(0);" onclick="showApproveHistory('+obj.batchid+')">审核历史</a>';
                    }   
                    return actionStr;
                },
                sortable:false
            }
        ];
        var columns3=[
            {
                title:'序号',
                width:'50px',
                renderer:function (v,o, index){
                    return index+1;
                },
                sortable:false
            },{
                title:'标题',
                width:'45%',
                dataIndex:'batchTitle',
                sortable:false
            },{
                title:'创建日期',
                width:'15%',
                dataIndex:'createTime',
                renderer:function(value,obj,index){
                    var d=new Date(value);
                    return d.getFullYear()+'-'+d.getMonth()+'-'+d.getDate()+" "+d.getHours()+':'+d.getMinutes()+':'+d.getSeconds();
                },
                sortable:false
            },{
                title:'状态',
                width:'10%',
                dataIndex:'approveStatus',
                renderer:function(value,obj,index){
                    if(value=='1'){
                        return '已提交';
                    }
                    if(value=='0'){
                        return '暂存';
                    }
                    if(value=='2'){
                        return "审核中";
                    }
                    if(value=='3'){
                        return '审核通过';
                    }
                    if(value=='4'){
                        return '已驳回';
                    }
                },
                sortable:false
            },{
                title:'操作',
                width:'20%',
                renderer:function(value,obj){
                    return '<a href="javascript:void(0);" onclick="showDetail('+obj.batchid+','+obj.parentDistributionId+')">详情</a>&nbsp;'+
                    '<a href="javascript:void(0);" onclick="showApproveHistory('+obj.batchid+')">审核历史</a>';
                },
                sortable:false
            }
        ];
        var columns4=[
            {
                title:'序号',
                width:'50px',
                renderer:function (v,o, index){
                    return index+1;
                },
                sortable:false
            },{
                title:'标题',
                width:'45%',
                dataIndex:'batchTitle',
                sortable:false
            },{
                title:'创建日期',
                width:'15%',
                dataIndex:'createTime',
                renderer:function(value,obj,index){
                    var d=new Date(value);
                    return d.getFullYear()+'-'+d.getMonth()+'-'+d.getDate()+" "+d.getHours()+':'+d.getMinutes()+':'+d.getSeconds();
                },
                sortable:false
            },{
                title:'状态',
                width:'10%',
                dataIndex:'approveStatus',
                renderer:function(value,obj,index){
                    if(value=='1'){
                        return '已提交';
                    }
                    if(value=='0'){
                        return '暂存';
                    }
                    if(value=='2'){
                        return "审核中";
                    }
                    if(value=='3'){
                        return '审核通过';
                    }
                    if(value=='4'){
                        return '已驳回';
                    }
                },
                sortable:false
            },{
                title:'操作',
                width:'20%',
                renderer:function(value,obj){
                    return '<a href="javascript:void(0);" onclick="showDetail('+obj.batchid+','+obj.parentDistributionId+')">详情</a>&nbsp;'+
                    '<a href="javascript:void(0);" onclick="showApproveDialog('+obj.batchid+',3,'+obj.versionFlag+')">审批通过</a>&nbsp;'+
                    '<a href="javascript:void(0);" onclick="showApproveDialog('+obj.batchid+',4,'+obj.versionFlag+')">驳回</a>&nbsp;'+
                    '<a href="javascript:void(0);" onclick="showApproveHistory('+obj.batchid+')">审核历史</a>';
                },
                sortable:false
            }
        ];
        daichuliStore=new Data.Store({
            data:[]
        });
        yichuliStore=new Data.Store({
            data:[]
        });
        yishenheStore=new Data.Store({
            data:[]
        });
        daishenheStore=new Data.Store({
            data:[]
        });
        daichuliGrid=new Grid.Grid({
            render:"#tab-content-wrapper div[data-type=daichuli] .approveGrid",
            width:'100%',
            columns : columns1,
            store : daichuliStore
        }); 
        daichuliGrid.render();
        yichuliGrid=new Grid.Grid({
            render:"#tab-content-wrapper div[data-type=yichuli] .approveGrid",
            width:'100%',
            columns : columns2,
            store : yichuliStore
        }); 
        yichuliGrid.render();
        yishenheGrid=new Grid.Grid({
            render:"#tab-content-wrapper div[data-type=yishenhe] .approveGrid",
            width:'100%',
            columns : columns3,
            store : yishenheStore
        }); 
        yishenheGrid.render();
        daishenheGrid=new Grid.Grid({
            render:"#tab-content-wrapper div[data-type=daishenhe] .approveGrid",
            width:'100%',
            columns : columns4,
            store : daishenheStore
        }); 
        daishenheGrid.render();
    });
}

function query(type){
    var params=[];
    var key=$("#tab-content-wrapper>div[data-type="+type+"] input[data-name=keyword]").val();
    if(type=="daichuli"){
        params=['pending',key];
    }
    if(type=="yishenhe"){
        params=['approved',key];
    }
    if(type=="yichuli"){
        params=['processed',key];
    }
    if(type=="daishenhe"){
        params=['inapproval',key];
    }
    LoadMask.show();
    foo.call("promoteDistributionService.getPromoteDistributionApplyList",params,function(result){
        LoadMask.hide();
        if(result.code=='success'){
            switch(type){
                case "daichuli":daichuliStore.setResult(result.resultLst);break;
                case "yishenhe":yishenheStore.setResult(result.resultLst);break;
                case "yichuli":yichuliStore.setResult(result.resultLst);break;
                case "daishenhe":daishenheStore.setResult(result.resultLst);break;
            }
        }
    });
}

var tab=null;
function initTab(){
    BUI.use(['bui/tab','bui/mask'],function(Tab){
        tab = new Tab.TabPanel({
            render : '#tabs',
            elCls : 'nav-tabs',
            panelContainer : '#tab-content-wrapper',//如果内部有容器，那么会跟标签项一一对应，如果没有会自动生成
            autoRender: false,
            children: [
                { title: '待处理', value: '1', type: "daichuli" },
                { title: '已处理', value: '2', type: "yichuli" },
                { title: '已审核', value: '3', type: "yishenhe" },
                { title: '待审核', value: '4', type: "daishenhe" }
            ]
        }); 
        tab.on('selectedchange', function(ev){
            var item = ev.item,_value = item.get('value'),_text = item.get('text');
            var type=ev.item.get("type");
            $(window).trigger('resize');
            query(type);
        });
        tab.render();
        var items=tab.getItems();
        if(promoteUserType=='marketing'){
            items[0].hide();
            items[1].hide();
            $(".createPlan").hide();
            tab.setSelected(tab.getItemAt(3));
        }
        if(promoteUserType=='channelManager'){
            items[3].hide();
            items[2].hide();
            tab.setSelected(tab.getItemAt(0));
        }
        if(promoteUserType=='supervisor'){
            items[1].hide();
            tab.setSelected(tab.getItemAt(0));
        }
    });
}

function modify(planBatchId){
    window.location.href='/promote/jsp/createPromoteDistributePlanPage.jsp?type='+promoteUserType+'&planBatchId='+planBatchId;
}

function showDetail(planBatchId,parentDistributionId){
    window.location.href='/promote/jsp/promoteDistributePlanApproveDetailPage.jsp?type='+promoteUserType+'&applyBatchId='+planBatchId+'&parentDistributionId='+parentDistributionId;
}

var approveDialog;
function showApproveDialog(planBatchId,status,version){
    if(approveDialog==null){
        BUI.use("bui/overlay",function(Overlay){
            approveDialog=new Overlay.Dialog({
                title:'审批理由',
                width:500,
                height:300,
                mask:true,  //设置是否模态
                contentId:"approveDialog",
                success:function(){
                    var reason=$("#approveReason").val();
                    foo.call("promoteDistributionWorkFlowService.approvePromoteDistribution",[planBatchId,status,reason,version],function(result){
                        if(result.code=='success'){
                            if(status==3){
                                common.alertMes("审核成功！","success");
                                query("daishenhe");
                            } else if(status==4){
                                common.alertMes("驳回成功！","success");
                                query("daishenhe");
                            }
                        }
                    });
                }
            });
            approveDialog.show();
        });
    } else{
        approveDialog.show();
    }
}

var historyDialog;
var historyGrid;
var historyStore;
function showApproveHistory(applyBatchId) {
    if (historyDialog == null) {
        BUI.use(["bui/overlay", "bui/data", "bui/grid"],function(Overlay,Data,Grid){
            historyDialog = new Overlay.Dialog({
                title: "查看审批历史",
                width: 800, 
                height: 500,
                mask: true,  //设置是否模态
                buttons:[],
                contentId: "approveHistoryDialog",
            });
            foo.call("promoteDistributionWorkFlowService.queryPromoteDistributionApprovalHistory",[applyBatchId],function(result){
                if(result.code=='success'){
                    if (historyGrid == null) {
                        BUI.use(["bui/data", "bui/grid"],function(Data,Grid){
                            var columns = [
                                {
                                    title:'序号',
                                    width:'50px',
                                    renderer:function (v,o, index){
                                        return index+1;
                                    },
                                    sortable:false
                                },{
                                    title:'审批人',
                                    width:'45%',
                                    dataIndex:'approveName',
                                    sortable:false
                                },{
                                    title:"审批备注",
                                    width:"20%",
                                    dataIndex:'approveRemark',
                                    sortable:false
                                },{
                                    title:"审批时间",
                                    width:"20%",
                                    dataIndex:"approveTime",
                                    renderer:function(value,obj,index){
                                        return common.formatDate(new Date(value), 'yyyy-MM-dd hh:mm:ss');
                                    },
                                    sortable:false
                                },{
                                    title:"步骤",
                                    width:"15%",
                                    dataIndex:"stepSequence",
                                    sortable:false
                                }
                            ];
                            historyStore = new Data.Store({
                                data: []
                            });
                            historyGrid = new Grid.Grid({
                                render: "#historyGrid",
                                width: '100%',
                                columns: columns,
                                store: historyStore
                            });
                            historyGrid.render();
                        });
                    }
                    historyStore.setResult(result.resultLst);
                }
            });
        });
        historyDialog.show();
    } else{
        historyDialog.show();
    }
}

