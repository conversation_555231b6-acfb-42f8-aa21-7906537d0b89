package com.sys.auth.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxTRoleExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public WxTRoleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRoleIdIsNull() {
            addCriterion("role_id is null");
            return (Criteria) this;
        }

        public Criteria andRoleIdIsNotNull() {
            addCriterion("role_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoleIdEqualTo(Long value) {
            addCriterion("role_id =", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotEqualTo(Long value) {
            addCriterion("role_id <>", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdGreaterThan(Long value) {
            addCriterion("role_id >", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("role_id >=", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdLessThan(Long value) {
            addCriterion("role_id <", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdLessThanOrEqualTo(Long value) {
            addCriterion("role_id <=", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdIn(List<Long> values) {
            addCriterion("role_id in", values, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotIn(List<Long> values) {
            addCriterion("role_id not in", values, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdBetween(Long value1, Long value2) {
            addCriterion("role_id between", value1, value2, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotBetween(Long value1, Long value2) {
            addCriterion("role_id not between", value1, value2, "roleId");
            return (Criteria) this;
        }

        public Criteria andChRoleNameIsNull() {
            addCriterion("ch_role_name is null");
            return (Criteria) this;
        }

        public Criteria andChRoleNameIsNotNull() {
            addCriterion("ch_role_name is not null");
            return (Criteria) this;
        }

        public Criteria andChRoleNameEqualTo(String value) {
            addCriterion("ch_role_name =", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameNotEqualTo(String value) {
            addCriterion("ch_role_name <>", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameGreaterThan(String value) {
            addCriterion("ch_role_name >", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameGreaterThanOrEqualTo(String value) {
            addCriterion("ch_role_name >=", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameLessThan(String value) {
            addCriterion("ch_role_name <", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameLessThanOrEqualTo(String value) {
            addCriterion("ch_role_name <=", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameLike(String value) {
            addCriterion("ch_role_name like", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameNotLike(String value) {
            addCriterion("ch_role_name not like", value, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameIn(List<String> values) {
            addCriterion("ch_role_name in", values, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameNotIn(List<String> values) {
            addCriterion("ch_role_name not in", values, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameBetween(String value1, String value2) {
            addCriterion("ch_role_name between", value1, value2, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andChRoleNameNotBetween(String value1, String value2) {
            addCriterion("ch_role_name not between", value1, value2, "chRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameIsNull() {
            addCriterion("cn_role_name is null");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameIsNotNull() {
            addCriterion("cn_role_name is not null");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameEqualTo(String value) {
            addCriterion("cn_role_name =", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameNotEqualTo(String value) {
            addCriterion("cn_role_name <>", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameGreaterThan(String value) {
            addCriterion("cn_role_name >", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameGreaterThanOrEqualTo(String value) {
            addCriterion("cn_role_name >=", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameLessThan(String value) {
            addCriterion("cn_role_name <", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameLessThanOrEqualTo(String value) {
            addCriterion("cn_role_name <=", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameLike(String value) {
            addCriterion("cn_role_name like", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameNotLike(String value) {
            addCriterion("cn_role_name not like", value, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameIn(List<String> values) {
            addCriterion("cn_role_name in", values, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameNotIn(List<String> values) {
            addCriterion("cn_role_name not in", values, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameBetween(String value1, String value2) {
            addCriterion("cn_role_name between", value1, value2, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andCnRoleNameNotBetween(String value1, String value2) {
            addCriterion("cn_role_name not between", value1, value2, "cnRoleName");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptIsNull() {
            addCriterion("role_descript is null");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptIsNotNull() {
            addCriterion("role_descript is not null");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptEqualTo(String value) {
            addCriterion("role_descript =", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptNotEqualTo(String value) {
            addCriterion("role_descript <>", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptGreaterThan(String value) {
            addCriterion("role_descript >", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptGreaterThanOrEqualTo(String value) {
            addCriterion("role_descript >=", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptLessThan(String value) {
            addCriterion("role_descript <", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptLessThanOrEqualTo(String value) {
            addCriterion("role_descript <=", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptLike(String value) {
            addCriterion("role_descript like", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptNotLike(String value) {
            addCriterion("role_descript not like", value, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptIn(List<String> values) {
            addCriterion("role_descript in", values, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptNotIn(List<String> values) {
            addCriterion("role_descript not in", values, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptBetween(String value1, String value2) {
            addCriterion("role_descript between", value1, value2, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleDescriptNotBetween(String value1, String value2) {
            addCriterion("role_descript not between", value1, value2, "roleDescript");
            return (Criteria) this;
        }

        public Criteria andRoleOrderIsNull() {
            addCriterion("role_order is null");
            return (Criteria) this;
        }

        public Criteria andRoleOrderIsNotNull() {
            addCriterion("role_order is not null");
            return (Criteria) this;
        }

        public Criteria andRoleOrderEqualTo(String value) {
            addCriterion("role_order =", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderNotEqualTo(String value) {
            addCriterion("role_order <>", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderGreaterThan(String value) {
            addCriterion("role_order >", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderGreaterThanOrEqualTo(String value) {
            addCriterion("role_order >=", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderLessThan(String value) {
            addCriterion("role_order <", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderLessThanOrEqualTo(String value) {
            addCriterion("role_order <=", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderLike(String value) {
            addCriterion("role_order like", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderNotLike(String value) {
            addCriterion("role_order not like", value, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderIn(List<String> values) {
            addCriterion("role_order in", values, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderNotIn(List<String> values) {
            addCriterion("role_order not in", values, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderBetween(String value1, String value2) {
            addCriterion("role_order between", value1, value2, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleOrderNotBetween(String value1, String value2) {
            addCriterion("role_order not between", value1, value2, "roleOrder");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNull() {
            addCriterion("role_type is null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNotNull() {
            addCriterion("role_type is not null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualTo(Integer value) {
            addCriterion("role_type =", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualTo(Integer value) {
            addCriterion("role_type <>", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThan(Integer value) {
            addCriterion("role_type >", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("role_type >=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThan(Integer value) {
            addCriterion("role_type <", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("role_type <=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIn(List<Integer> values) {
            addCriterion("role_type in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotIn(List<Integer> values) {
            addCriterion("role_type not in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeBetween(Integer value1, Integer value2) {
            addCriterion("role_type between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("role_type not between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andPresetBjIsNull() {
            addCriterion("preset_bj is null");
            return (Criteria) this;
        }

        public Criteria andPresetBjIsNotNull() {
            addCriterion("preset_bj is not null");
            return (Criteria) this;
        }

        public Criteria andPresetBjEqualTo(Integer value) {
            addCriterion("preset_bj =", value, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjNotEqualTo(Integer value) {
            addCriterion("preset_bj <>", value, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjGreaterThan(Integer value) {
            addCriterion("preset_bj >", value, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjGreaterThanOrEqualTo(Integer value) {
            addCriterion("preset_bj >=", value, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjLessThan(Integer value) {
            addCriterion("preset_bj <", value, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjLessThanOrEqualTo(Integer value) {
            addCriterion("preset_bj <=", value, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjIn(List<Integer> values) {
            addCriterion("preset_bj in", values, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjNotIn(List<Integer> values) {
            addCriterion("preset_bj not in", values, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjBetween(Integer value1, Integer value2) {
            addCriterion("preset_bj between", value1, value2, "presetBj");
            return (Criteria) this;
        }

        public Criteria andPresetBjNotBetween(Integer value1, Integer value2) {
            addCriterion("preset_bj not between", value1, value2, "presetBj");
            return (Criteria) this;
        }

        public Criteria andXgSjIsNull() {
            addCriterion("xg_sj is null");
            return (Criteria) this;
        }

        public Criteria andXgSjIsNotNull() {
            addCriterion("xg_sj is not null");
            return (Criteria) this;
        }

        public Criteria andXgSjEqualTo(Date value) {
            addCriterion("xg_sj =", value, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjNotEqualTo(Date value) {
            addCriterion("xg_sj <>", value, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjGreaterThan(Date value) {
            addCriterion("xg_sj >", value, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjGreaterThanOrEqualTo(Date value) {
            addCriterion("xg_sj >=", value, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjLessThan(Date value) {
            addCriterion("xg_sj <", value, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjLessThanOrEqualTo(Date value) {
            addCriterion("xg_sj <=", value, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjIn(List<Date> values) {
            addCriterion("xg_sj in", values, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjNotIn(List<Date> values) {
            addCriterion("xg_sj not in", values, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjBetween(Date value1, Date value2) {
            addCriterion("xg_sj between", value1, value2, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgSjNotBetween(Date value1, Date value2) {
            addCriterion("xg_sj not between", value1, value2, "xgSj");
            return (Criteria) this;
        }

        public Criteria andXgUserIsNull() {
            addCriterion("xg_user is null");
            return (Criteria) this;
        }

        public Criteria andXgUserIsNotNull() {
            addCriterion("xg_user is not null");
            return (Criteria) this;
        }

        public Criteria andXgUserEqualTo(Long value) {
            addCriterion("xg_user =", value, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserNotEqualTo(Long value) {
            addCriterion("xg_user <>", value, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserGreaterThan(Long value) {
            addCriterion("xg_user >", value, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserGreaterThanOrEqualTo(Long value) {
            addCriterion("xg_user >=", value, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserLessThan(Long value) {
            addCriterion("xg_user <", value, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserLessThanOrEqualTo(Long value) {
            addCriterion("xg_user <=", value, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserIn(List<Long> values) {
            addCriterion("xg_user in", values, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserNotIn(List<Long> values) {
            addCriterion("xg_user not in", values, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserBetween(Long value1, Long value2) {
            addCriterion("xg_user between", value1, value2, "xgUser");
            return (Criteria) this;
        }

        public Criteria andXgUserNotBetween(Long value1, Long value2) {
            addCriterion("xg_user not between", value1, value2, "xgUser");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wx_t_role
     *
     * @mbggenerated do_not_delete_during_merge Wed Jun 10 15:24:25 CST 2015
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wx_t_role
     *
     * @mbggenerated Wed Jun 10 15:24:25 CST 2015
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}