--select distinct "经销商名称" from (
select org.organization_name "经销商名称",w.work_shop_name "门店名称", isnull(oppc.code1, osl.code) "物流码", 
--case when osl.code_type='box' then '箱码' else '瓶码' end "物流码类型", 
p1.sku, p1.name "产品名称",
osl.scan_time "扫码时间"
from wx_t_order o
left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
left join wx_t_organization org on t_workshop_partner.partner_id=org.id
left join wx_t_work_shop w on w.id=o.work_shop_id
left join wx_t_out_stock t_outstock on t_outstock.order_no = o.order_no
	      				LEFT join wx_t_out_stock_line osl ON t_outstock.stock_out_no = osl.stock_out_no
	     				left join wx_t_product p1 on p1.sku=osl.sku 
left join wx_t_oem_product_packaging_code oppc on oppc.code2=(case when osl.code_type='box' then osl.code else null end)
	     				WHERE t_outstock.order_no = o.order_no --and p1.product_property = 'SN Above'
						and t_outstock.out_time>='2018-04-01' and t_outstock.out_time<'2019-01-01'
						and osl.code is not null
						and t_workshop_partner.partner_id in (8,12964,33436,56306)
order by org.id, osl.scan_time desc
--) a