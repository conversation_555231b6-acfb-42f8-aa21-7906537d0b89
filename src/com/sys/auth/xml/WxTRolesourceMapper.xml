<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.auth.dao.WxTRolesourceMapper" >
  <resultMap id="BaseResultMap" type="com.sys.auth.model.WxTRolesource" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    <id column="rs_id" property="rsId" jdbcType="BIGINT" />
    <result column="role_id" property="roleId" jdbcType="BIGINT" />
    <result column="rs_type" property="rsType" jdbcType="INTEGER" />
    <result column="source_id" property="sourceId" jdbcType="BIGINT" />
    <result column="add_flag" property="addFlag" jdbcType="VARCHAR" />
    <result column="update_flag" property="updateFlag" jdbcType="VARCHAR" />
    <result column="del_flag" property="delFlag" jdbcType="VARCHAR" />
    <result column="view_flag" property="viewFlag" jdbcType="VARCHAR" />
    <result column="xg_user" property="xgUser" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    rs_id, role_id, rs_type, source_id, add_flag, update_flag, del_flag, view_flag, xg_user, 
    status, tenant_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTRolesourceExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_rolesource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from wx_t_rolesource
    where rs_id = #{rsId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    delete from wx_t_rolesource
    where rs_id = #{rsId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sys.auth.model.WxTRolesource" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    delete from wx_t_rolesource where 1=1
      <if test="rsId != null" >
        and rs_id=#{rsId,jdbcType=BIGINT}
      </if>
      <if test="roleId != null" >
        and role_id=#{roleId,jdbcType=BIGINT}
      </if>
      <if test="rsType != null" >
        and rs_type=#{rsType,jdbcType=INTEGER}
      </if>
      <if test="sourceId != null" >
        and source_id=#{sourceId,jdbcType=BIGINT}
      </if>
      <if test="status != null" >
        and status=#{status,jdbcType=INTEGER}
      </if>
      <if test="tenantId != null" >
        and tenant_id=#{tenantId,jdbcType=BIGINT}
      </if>
  </delete>
  <insert id="insert" parameterType="com.sys.auth.model.WxTRolesource" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    insert into wx_t_rolesource (rs_id, role_id, rs_type, 
      source_id, add_flag, update_flag, 
      del_flag, view_flag, xg_user, 
      status, tenant_id)
    values (#{rsId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}, #{rsType,jdbcType=INTEGER}, 
      #{sourceId,jdbcType=BIGINT}, #{addFlag,jdbcType=VARCHAR}, #{updateFlag,jdbcType=VARCHAR}, 
      #{delFlag,jdbcType=VARCHAR}, #{viewFlag,jdbcType=VARCHAR}, #{xgUser,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sys.auth.model.WxTRolesource" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    insert into wx_t_rolesource
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rsId != null" >
        rs_id,
      </if>
      <if test="roleId != null" >
        role_id,
      </if>
      <if test="rsType != null" >
        rs_type,
      </if>
      <if test="sourceId != null" >
        source_id,
      </if>
      <if test="addFlag != null" >
        add_flag,
      </if>
      <if test="updateFlag != null" >
        update_flag,
      </if>
      <if test="delFlag != null" >
        del_flag,
      </if>
      <if test="viewFlag != null" >
        view_flag,
      </if>
      <if test="xgUser != null" >
        xg_user,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rsId != null" >
        #{rsId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null" >
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="rsType != null" >
        #{rsType,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null" >
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="addFlag != null" >
        #{addFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateFlag != null" >
        #{updateFlag,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null" >
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="viewFlag != null" >
        #{viewFlag,jdbcType=VARCHAR},
      </if>
      <if test="xgUser != null" >
        #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sys.auth.model.WxTRolesourceExample" resultType="Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    select count(*) from wx_t_rolesource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    update wx_t_rolesource
    <set >
      <if test="record.rsId != null" >
        rs_id = #{record.rsId,jdbcType=BIGINT},
      </if>
      <if test="record.roleId != null" >
        role_id = #{record.roleId,jdbcType=BIGINT},
      </if>
      <if test="record.rsType != null" >
        rs_type = #{record.rsType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceId != null" >
        source_id = #{record.sourceId,jdbcType=BIGINT},
      </if>
      <if test="record.addFlag != null" >
        add_flag = #{record.addFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.updateFlag != null" >
        update_flag = #{record.updateFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.delFlag != null" >
        del_flag = #{record.delFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.viewFlag != null" >
        view_flag = #{record.viewFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.xgUser != null" >
        xg_user = #{record.xgUser,jdbcType=BIGINT},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    update wx_t_rolesource
    set rs_id = #{record.rsId,jdbcType=BIGINT},
      role_id = #{record.roleId,jdbcType=BIGINT},
      rs_type = #{record.rsType,jdbcType=INTEGER},
      source_id = #{record.sourceId,jdbcType=BIGINT},
      add_flag = #{record.addFlag,jdbcType=VARCHAR},
      update_flag = #{record.updateFlag,jdbcType=VARCHAR},
      del_flag = #{record.delFlag,jdbcType=VARCHAR},
      view_flag = #{record.viewFlag,jdbcType=VARCHAR},
      xg_user = #{record.xgUser,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sys.auth.model.WxTRolesource" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    update wx_t_rolesource
    <set >
      <if test="roleId != null" >
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
      <if test="rsType != null" >
        rs_type = #{rsType,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null" >
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="addFlag != null" >
        add_flag = #{addFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateFlag != null" >
        update_flag = #{updateFlag,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null" >
        del_flag = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="viewFlag != null" >
        view_flag = #{viewFlag,jdbcType=VARCHAR},
      </if>
      <if test="xgUser != null" >
        xg_user = #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where rs_id = #{rsId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sys.auth.model.WxTRolesource" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 16 09:42:04 CST 2015.
    -->
    update wx_t_rolesource
    set role_id = #{roleId,jdbcType=BIGINT},
      rs_type = #{rsType,jdbcType=INTEGER},
      source_id = #{sourceId,jdbcType=BIGINT},
      add_flag = #{addFlag,jdbcType=VARCHAR},
      update_flag = #{updateFlag,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=VARCHAR},
      view_flag = #{viewFlag,jdbcType=VARCHAR},
      xg_user = #{xgUser,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where rs_id = #{rsId,jdbcType=BIGINT}
  </update>
  
  <delete id="deleteOldPermissions" parameterType="com.sys.auth.model.WxTRolesource" >
    delete from wx_t_rolesource
    where role_id = #{roleId,jdbcType=BIGINT}
  </delete>
  
  <select id="selectByRoleId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select * from wx_t_rolesource
    where role_id = #{roleId,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteBySourceId" parameterType="java.lang.Long" >
    delete from wx_t_rolesource where source_id = #{sourceId,jdbcType=BIGINT}
  </delete>
  <insert id="insertByBatch" useGeneratedKeys="true" parameterType="java.util.List" >
    insert into wx_t_rolesource (role_id, rs_type, 
      source_id, xg_user, 
      status, tenant_id)
      values
    <foreach collection="list" index="index" item="item" separator=",">
	   	<trim prefix="(" suffix=")" suffixOverrides=",">
      #{item.roleId,jdbcType=BIGINT}, #{item.rsType,jdbcType=INTEGER}, 
      #{item.sourceId,jdbcType=BIGINT}, #{item.xgUser,jdbcType=BIGINT}, 
      #{item.status,jdbcType=INTEGER}, #{item.tenantId,jdbcType=BIGINT}
      </trim>
	</foreach>
  </insert>
  <!-- 根据比较结果 删除需要删除的权限数据 -->
  <delete id="deleteByCompareOrg"  >    
    delete from wx_t_rolesource 
	where wx_t_rolesource.role_id =#{roleId,jdbcType=BIGINT}
	 and wx_t_rolesource.source_id in (  
	  	select o2.org_id from (
	  	<!-- 已有的权限 -->
		  select o1.org_id from wx_t_org o1 where o1.org_id in
			<foreach item="item" index="index" collection="curSourceIdList" open="(" separator="," close=")">
		     #{item}
			</foreach>			      


		 ) o2
		 <if test="newSourceIdList != null" >
		    where o2.org_id not in <!-- 新的权限 --> 
		 	<foreach item="item" index="index" collection="newSourceIdList" open="(" separator="," close=")">
		     #{item}
			</foreach>
		  </if>
		)
  </delete>
   <!-- 根据比较结果 插入权限数据 -->
   <insert id="insertByCompareOrg">
	 insert  into  wx_t_rolesource (role_id,rs_type,source_id,xg_user,status,tenant_id) 
	 select  #{roleId,jdbcType=BIGINT} as role_id ,
	    #{rsType,jdbcType=INTEGER} as rs_type,o3.org_id as source_id, 
	     #{userId,jdbcType=BIGINT} as xg_user,
	 	1 as status,#{tenantId,jdbcType=BIGINT} as tenant_id 
	  from wx_t_org o INNER JOIN(
	
	  select o2.org_id from (
	 <!-- 新的权限 --> 
	  select o1.org_id from wx_t_org o1 where o1.org_id in
	   <foreach item="item" index="index" collection="newSourceIdList" open="(" separator="," close=")">
		     #{item}
		</foreach>
	 ) o2 
		 <if test="curSourceIdList != null" >
		 where o2.org_id not in
		 <!-- 已有的权限 -->
		 	<foreach item="item" index="index" collection="curSourceIdList" open="(" separator="," close=")">
			     #{item}
			</foreach>
		 </if>	 
	  ) o3 on o.org_id=o3.org_id    
   
   </insert>
</mapper>