truncate table wx_t_workshop_employee;
SET   IDENTITY_INSERT wx_t_workshop_employee on
insert into wx_t_workshop_employee ([id]
      ,[code]
      ,[name]
      ,[wechat_account]
      ,[mobile]
      ,[workshop_id]
      ,[workshop_name]
      ,[employee_type]
      ,[creator]
      ,[creation_time]
      ,[updator]
      ,[update_time]
      ,[partner_id]
      ,[version_no]
      ,[enable_flag]
      ,[wechat_open_id]
      ,[b2b_open_id]
      ,[is_has_product])
select [id]
      ,[code]
      ,[name]
      ,[wechat_account]
      ,[mobile]
      ,[workshop_id]
      ,[workshop_name]
      ,[employee_type]
      ,[creator]
      ,[creation_time]
      ,[updator]
      ,[update_time]
      ,[partner_id]
      ,[version_no]
      ,[enable_flag]
      ,[wechat_open_id]
      ,[b2b_open_id]
      ,[is_has_product] from pmpdb01.dbo.wx_t_workshop_employee--pmpdb01
SET   IDENTITY_INSERT wx_t_workshop_employee off
