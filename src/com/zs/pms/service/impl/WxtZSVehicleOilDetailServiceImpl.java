package com.zs.pms.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.common.util.ContextUtil;
import com.sys.log.util.LogUtils;
import com.zs.common.ChevronOil;
import com.zs.common.ZsOil;
import com.zs.excelutil.ExcelReaderUtil;
import com.zs.importdata.ImportZsDataUtil;
import com.zs.pms.dao.WxtZSVehicleGearoilDetailMapper;
import com.zs.pms.dao.WxtZSVehicleOilDetailMapper;
import com.zs.pms.dao.WxtZSVehicleTypeMapper;
import com.zs.pms.model.WxtZSVehicleGearoilDetail;
import com.zs.pms.model.WxtZSVehicleOilDetail;
import com.zs.pms.model.WxtZSVehicleOilDetailExample;
import com.zs.pms.model.WxtZSVehicleOilDetailView;
import com.zs.pms.model.WxtZSVehicleType;
import com.zs.pms.service.WxtZSVehicleOilDetailService;

@Service
public class WxtZSVehicleOilDetailServiceImpl implements WxtZSVehicleOilDetailService {

	private static Logger log = LoggerFactory.getLogger(WxtZSVehicleOilDetailServiceImpl.class);

	@Autowired
	private WxtZSVehicleTypeMapper vehicleTypeMapper;

	@Autowired
	private WxtZSVehicleOilDetailMapper vehicleOilDetailMapper;
	
	@Autowired
	private WxtZSVehicleGearoilDetailMapper vehiclegearOilDetailMapper;

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> importVehicleOilDetail(Workbook wb) {
		List<WxtZSVehicleOilDetailView> lsTypeOilDetailVo = new ArrayList<WxtZSVehicleOilDetailView>();
		WxtZSVehicleOilDetailView vehicleFilterDetailVo = new WxtZSVehicleOilDetailView();

		List<WxtZSVehicleType> typeList_new = new ArrayList<WxtZSVehicleType>();
		List<WxtZSVehicleType> typeList_update = new ArrayList<WxtZSVehicleType>();

		List<WxtZSVehicleOilDetail> oilDetailList_new = new ArrayList<WxtZSVehicleOilDetail>();
		List<WxtZSVehicleOilDetail> oilDetailList_update = new ArrayList<WxtZSVehicleOilDetail>();

		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// 0.解析数据.
			Map<String, Object> responseMap = ImportZsDataUtil.getImportDataByReflect(wb, vehicleFilterDetailVo,
					ImportZsDataUtil.VEHICLETYPEOIL);

			// 1.判断返回结果是否正确.
			String resultCode = (String) responseMap.get("result");
			if (resultCode.equals("dataInvalid")) {
				resultMap.put("code", "error");
				resultMap.put("codeMsg", "导入数据格式错误");
				return resultMap;
			}
			// 2.返回结果正确，继续执行
			lsTypeOilDetailVo = (List<WxtZSVehicleOilDetailView>) responseMap.get("datalst");
			// 3.循环结果，判断备注是新增还是修改
			Set<String> tmodelIdSet = new HashSet<String>();
			Set<String> tmodelOilSet = new HashSet<String>();
			for (WxtZSVehicleOilDetailView mPvehicleOilDetailVo : lsTypeOilDetailVo) {
				// 判断车型是否已有
				Map<String, Object> praMap = new HashMap<String, Object>();
				praMap.put("tModelId", mPvehicleOilDetailVo.getZstModelId());
				praMap.put("tId", mPvehicleOilDetailVo.getZstId());
				praMap.put("modelYear", mPvehicleOilDetailVo.getModelYear());
				List<WxtZSVehicleType> vehicleTypeVoList = vehicleTypeMapper.selectByPra(praMap);
				WxtZSVehicleType vehicleTypeVo = new WxtZSVehicleType();
				if (vehicleTypeVoList == null || vehicleTypeVoList.isEmpty()) {
					String primaryKey = mPvehicleOilDetailVo.getZstModelId().trim()
							+ mPvehicleOilDetailVo.getZstId().trim() + mPvehicleOilDetailVo.getModelYear().trim();
					if (!tmodelIdSet.contains(primaryKey)) {
						tmodelIdSet.add(primaryKey);
						vehicleTypeVo.settModelId(mPvehicleOilDetailVo.getZstModelId());
						vehicleTypeVo.settId(mPvehicleOilDetailVo.getZstId());
						vehicleTypeVo.setFactoryName(mPvehicleOilDetailVo.getFactoryName());
						vehicleTypeVo.setBrandName(mPvehicleOilDetailVo.getBrandName());
						vehicleTypeVo.setDmpimp(mPvehicleOilDetailVo.getDmpimp());
						vehicleTypeVo.setVehicleType(mPvehicleOilDetailVo.getVehicleType());
						vehicleTypeVo.setVehicleSeries(mPvehicleOilDetailVo.getVehicleSeries());
						vehicleTypeVo.setVehicleSeriesSop(mPvehicleOilDetailVo.getVehicleSeriesSop());
						vehicleTypeVo.setVehicleSeriesEop(mPvehicleOilDetailVo.getVehicleSeriesEop());
						vehicleTypeVo.settTypeId(mPvehicleOilDetailVo.getZstTypeId());
						vehicleTypeVo.settType(mPvehicleOilDetailVo.getZstType());

						String modelName = mPvehicleOilDetailVo.getZstModelName();
						modelName = modelName.replace(mPvehicleOilDetailVo.getVehicleType(), "");
						String modelYearStr = mPvehicleOilDetailVo.getModelYear() + "款";
						modelName = modelName.replace(modelYearStr, "");
						vehicleTypeVo.settModelName(modelName);

						vehicleTypeVo.setModelYear(mPvehicleOilDetailVo.getModelYear());
						vehicleTypeVo.settModelMsrp(mPvehicleOilDetailVo.getZstModelMsrp());
						vehicleTypeVo.settModelSop(mPvehicleOilDetailVo.getZstModelSop());
						vehicleTypeVo.settModelEop(mPvehicleOilDetailVo.getZstModelEop());
						vehicleTypeVo.setEngine(mPvehicleOilDetailVo.getEngine());
						vehicleTypeVo.setEngineDisplacementCc(mPvehicleOilDetailVo.getEngineDisplacementCc());
						vehicleTypeVo.setEngineDisplacementL(mPvehicleOilDetailVo.getEngineDisplacementL());
						vehicleTypeVo.setPower(mPvehicleOilDetailVo.getPower());
						vehicleTypeVo.setTurboBoost(mPvehicleOilDetailVo.getTurboBoost());
						vehicleTypeVo.setGearboxDesc(mPvehicleOilDetailVo.getGearboxDesc());
						vehicleTypeVo.setDrivingModel(mPvehicleOilDetailVo.getDrivingModel());

						vehicleTypeVo.setImportTime(new Date());
						typeList_new.add(vehicleTypeVo);
					}
				} else {
					vehicleTypeVo = vehicleTypeVoList.get(0);
					vehicleTypeVo.settModelId(mPvehicleOilDetailVo.getZstModelId());
					vehicleTypeVo.settId(mPvehicleOilDetailVo.getZstId());
					vehicleTypeVo.setFactoryName(mPvehicleOilDetailVo.getFactoryName());
					vehicleTypeVo.setBrandName(mPvehicleOilDetailVo.getBrandName());
					vehicleTypeVo.setDmpimp(mPvehicleOilDetailVo.getDmpimp());
					vehicleTypeVo.setVehicleType(mPvehicleOilDetailVo.getVehicleType());
					vehicleTypeVo.setVehicleSeries(mPvehicleOilDetailVo.getVehicleSeries());
					vehicleTypeVo.setVehicleSeriesSop(mPvehicleOilDetailVo.getVehicleSeriesSop());
					vehicleTypeVo.setVehicleSeriesEop(mPvehicleOilDetailVo.getVehicleSeriesEop());
					vehicleTypeVo.settTypeId(mPvehicleOilDetailVo.getZstTypeId());
					vehicleTypeVo.settType(mPvehicleOilDetailVo.getZstType());

					String modelName = mPvehicleOilDetailVo.getZstModelName();
					modelName = modelName.replace(mPvehicleOilDetailVo.getVehicleType(), "");
					String modelYearStr = mPvehicleOilDetailVo.getModelYear() + "款";
					modelName = modelName.replace(modelYearStr, "");
					vehicleTypeVo.settModelName(modelName);

					vehicleTypeVo.setModelYear(mPvehicleOilDetailVo.getModelYear());
					vehicleTypeVo.settModelMsrp(mPvehicleOilDetailVo.getZstModelMsrp());
					vehicleTypeVo.settModelSop(mPvehicleOilDetailVo.getZstModelSop());
					vehicleTypeVo.settModelEop(mPvehicleOilDetailVo.getZstModelEop());
					vehicleTypeVo.setEngine(mPvehicleOilDetailVo.getEngine());
					vehicleTypeVo.setEngineDisplacementCc(mPvehicleOilDetailVo.getEngineDisplacementCc());
					vehicleTypeVo.setEngineDisplacementL(mPvehicleOilDetailVo.getEngineDisplacementL());
					vehicleTypeVo.setPower(mPvehicleOilDetailVo.getPower());
					vehicleTypeVo.setTurboBoost(mPvehicleOilDetailVo.getTurboBoost());
					vehicleTypeVo.setGearboxDesc(mPvehicleOilDetailVo.getGearboxDesc());
					vehicleTypeVo.setDrivingModel(mPvehicleOilDetailVo.getDrivingModel());

					vehicleTypeVo.setUpdateTime(new Date());
					typeList_update.add(vehicleTypeVo);
				}
				// 判断对应的机油推荐是否已有
				if (!tmodelOilSet
						.contains(mPvehicleOilDetailVo.getZstModelId() + mPvehicleOilDetailVo.getOilPartsNo())) {
					tmodelOilSet.add(mPvehicleOilDetailVo.getZstModelId() + mPvehicleOilDetailVo.getOilPartsNo());
					Map<String, Object> oilPra = new HashMap<String, Object>();
					oilPra.put("tModelId", mPvehicleOilDetailVo.getZstModelId());
					oilPra.put("oilName", mPvehicleOilDetailVo.getOilName());
					oilPra.put("oilPartsNo", mPvehicleOilDetailVo.getOilPartsNo());
					oilPra.put("oilType", mPvehicleOilDetailVo.getOilType());
					oilPra.put("oilConsumption", mPvehicleOilDetailVo.getOilConsumption());
					oilPra.put("oilGrade", mPvehicleOilDetailVo.getOilGrade());
					List<WxtZSVehicleOilDetail> vehicleOilDetailList = vehicleOilDetailMapper.selectByPra(oilPra);
					WxtZSVehicleOilDetail vehicleOilDetailTemp = new WxtZSVehicleOilDetail();
					if (vehicleOilDetailList == null || vehicleOilDetailList.isEmpty()) {
						vehicleOilDetailTemp = new WxtZSVehicleOilDetail();
						vehicleOilDetailTemp.settModelId(mPvehicleOilDetailVo.getZstModelId());
						vehicleOilDetailTemp.setOilName(mPvehicleOilDetailVo.getOilName());
						vehicleOilDetailTemp.setOilPartsNo(mPvehicleOilDetailVo.getOilPartsNo());
						vehicleOilDetailTemp.setOilConsumption(mPvehicleOilDetailVo.getOilConsumption());
						vehicleOilDetailTemp.setOilTotalPrice(mPvehicleOilDetailVo.getOilTotalPrice());
						vehicleOilDetailTemp.setOilType(mPvehicleOilDetailVo.getOilType());
						vehicleOilDetailTemp.setOilViscosity(mPvehicleOilDetailVo.getOilViscosity());
						vehicleOilDetailTemp.setOilGrade(mPvehicleOilDetailVo.getOilGrade());
						vehicleOilDetailTemp.setImportTime(new Date());
						oilDetailList_new.add(vehicleOilDetailTemp);
					} else {
						vehicleOilDetailTemp = vehicleOilDetailList.get(0);
						vehicleOilDetailTemp.settModelId(mPvehicleOilDetailVo.getZstModelId());
						vehicleOilDetailTemp.setOilName(mPvehicleOilDetailVo.getOilName());
						vehicleOilDetailTemp.setOilPartsNo(mPvehicleOilDetailVo.getOilPartsNo());
						vehicleOilDetailTemp.setOilConsumption(mPvehicleOilDetailVo.getOilConsumption());
						vehicleOilDetailTemp.setOilTotalPrice(mPvehicleOilDetailVo.getOilTotalPrice());
						vehicleOilDetailTemp.setOilType(mPvehicleOilDetailVo.getOilType());
						vehicleOilDetailTemp.setOilViscosity(mPvehicleOilDetailVo.getOilViscosity());
						vehicleOilDetailTemp.setOilGrade(mPvehicleOilDetailVo.getOilGrade());
						vehicleOilDetailTemp.setUpdateTime(new Date());
						oilDetailList_update.add(vehicleOilDetailTemp);
					}
				}
			}
			// 4.对新增的配件品牌进行分页处理
			// 4.1 新增车型
			ImportDataPageModelUtil vehicleTypeNewPage = new ImportDataPageModelUtil(typeList_new, 30);
			int vehicleTypeNewTotalPage = vehicleTypeNewPage.getTotalPages();
			for (int i = 1; i <= vehicleTypeNewTotalPage; i++) {
				List<WxtZSVehicleType> volst = vehicleTypeNewPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleTypeMapper.insertVehicleTypeBatch(volst);
			}
			// 4.2 修改车型
			ImportDataPageModelUtil vehicleTypeUpdatePage = new ImportDataPageModelUtil(typeList_update, 30);
			int vehicleTypeUpdateTotalPage = vehicleTypeUpdatePage.getTotalPages();
			for (int i = 1; i <= vehicleTypeUpdateTotalPage; i++) {
				List<WxtZSVehicleType> volst = vehicleTypeUpdatePage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleTypeMapper.updateBatch(volst);
			}
			// 4.3 新增机油推荐详情
			ImportDataPageModelUtil oilDetailNewPage = new ImportDataPageModelUtil(oilDetailList_new, 40);
			int oilDetailNewTotalPage = oilDetailNewPage.getTotalPages();
			for (int i = 1; i <= oilDetailNewTotalPage; i++) {
				List<WxtZSVehicleOilDetail> volst = oilDetailNewPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleOilDetailMapper.insertOilDetailBatch(volst);
			}
			// 4.4 更新机滤详情
			ImportDataPageModelUtil oilDetailUpdatePage = new ImportDataPageModelUtil(oilDetailList_update, 40);
			int OilDetailUpdateTotalPage = oilDetailUpdatePage.getTotalPages();
			for (int i = 1; i <= OilDetailUpdateTotalPage; i++) {
				List<WxtZSVehicleOilDetail> volst = oilDetailUpdatePage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleOilDetailMapper.updateBatch(volst);
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入数据失败,异常信息：" + e.getMessage());
			return resultMap;
		}
		int uploadDataAccount = lsTypeOilDetailVo.size();
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
		return resultMap;
	}
	
	

	@Override
	public Map<String, Object> importVehicleOilDetailUpdate(File file) {
		List<WxtZSVehicleOilDetailView> vehicleTypeList = new ArrayList<WxtZSVehicleOilDetailView>();
		
		List<WxtZSVehicleType> typeList_new = new ArrayList<WxtZSVehicleType>();
		List<WxtZSVehicleType> typeList_update = new ArrayList<WxtZSVehicleType>();

		List<WxtZSVehicleOilDetail> oilDetailList_new = new ArrayList<WxtZSVehicleOilDetail>();
		List<WxtZSVehicleOilDetail> oilDetailList_update = new ArrayList<WxtZSVehicleOilDetail>();
		
		List<WxtZSVehicleGearoilDetail> gearoilDetailList_new = new ArrayList<WxtZSVehicleGearoilDetail>();
		List<WxtZSVehicleGearoilDetail> gearoilDetailList_update = new ArrayList<WxtZSVehicleGearoilDetail>();

		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.zs.pms.service.impl.WxtZSVehicleOilDetailServiceImpl.importVehicleOilDetailUpdate", "start...");
			// 0.解析数据.
			ExcelReaderUtil excel = new ExcelReaderUtil();
            excel.readFileOneSheet(file, 1);
            
			LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.zs.pms.service.impl.WxtZSVehicleOilDetailServiceImpl.importVehicleOilDetailUpdate", "read(" + excel.getDataList().size() + ")...");
            System.out.println(excel.getDataList().size());
            List<Map<String, String>> dataList= excel.getDataList();
           
            List<WxtZSVehicleOilDetail> oilDetailList = new ArrayList<WxtZSVehicleOilDetail>();
            List<WxtZSVehicleGearoilDetail> gearOilDetailList = new ArrayList<WxtZSVehicleGearoilDetail>();
            for(Map<String, String> dataMap : dataList){
            	WxtZSVehicleOilDetailView vehicleType = new WxtZSVehicleOilDetailView();
            	vehicleType.setZstModelId(dataMap.get("A"));
            	vehicleType.setZstId(dataMap.get("B"));
            	vehicleType.setFactoryName(dataMap.get("C"));
            	vehicleType.setBrandName(dataMap.get("D"));
            	vehicleType.setDmpimp(dataMap.get("E"));
            	vehicleType.setVehicleType(dataMap.get("F"));
            	vehicleType.setVehicleSeries(dataMap.get("G"));
            	vehicleType.setVehicleSeriesSop(dataMap.get("H"));
            	vehicleType.setVehicleSeriesEop(dataMap.get("I"));
            	vehicleType.setZstTypeId(dataMap.get("J"));
            	vehicleType.setZstType(dataMap.get("K"));
            	vehicleType.setZstModelName(dataMap.get("L"));
            	vehicleType.setModelYear(dataMap.get("M"));
            	vehicleType.setZstModelMsrp(dataMap.get("N"));
            	vehicleType.setZstModelSop(dataMap.get("O"));
            	vehicleType.setZstModelEop(dataMap.get("P"));
            	vehicleType.setEngine(dataMap.get("Q"));
            	vehicleType.setEngineDisplacementCc(dataMap.get("R"));
            	vehicleType.setEngineDisplacementL(dataMap.get("S"));
            	vehicleType.setPower(dataMap.get("T"));
            	vehicleType.setTurboBoost(dataMap.get("U"));
            	vehicleType.setGearboxDesc(dataMap.get("V"));
            	vehicleType.setDrivingModel(dataMap.get("W"));
            	vehicleTypeList.add(vehicleType);
            	
            	if(dataMap.get("X").equals("机油")){
            		WxtZSVehicleOilDetail oilDetail = new WxtZSVehicleOilDetail();
            		oilDetail.settModelId(dataMap.get("A"));
            		oilDetail.setOilName("机油");
            		oilDetail.setOilPartsNo(dataMap.get("Y"));
            		oilDetail.setOilConsumption(dataMap.get("AB"));
            		oilDetail.setOilTotalPrice(dataMap.get("AC"));
            		oilDetail.setOilType(dataMap.get("AD"));
            		oilDetail.setOilViscosity(dataMap.get("AE"));
            		oilDetail.setOilGrade(dataMap.get("AF"));
            		oilDetailList.add(oilDetail);
            		
            	}else if(dataMap.get("X").equals("变速箱油")){
            		WxtZSVehicleGearoilDetail oilDetail = new WxtZSVehicleGearoilDetail();
            		oilDetail.settModelId(dataMap.get("A"));
            		oilDetail.setOilName("变速箱油");
            		oilDetail.setOilPartsNo(dataMap.get("Y"));
            		oilDetail.setWaveBoxNo(dataMap.get("Z"));
            		oilDetail.setWaveBoxOilNo(dataMap.get("AA"));
            		oilDetail.setOilTotalPrice(dataMap.get("AC"));
            		oilDetail.setOilType(dataMap.get("AD"));
            		oilDetail.setOilViscosity(dataMap.get("AE"));
            		oilDetail.setOilGrade(dataMap.get("AF"));
            		gearOilDetailList.add(oilDetail);
            		
            	}
            }
            resultMap.put("vehicleTypeList",vehicleTypeList.size());
            resultMap.put("oilDetailList",oilDetailList.size());
            resultMap.put("gearOilDetailList",gearOilDetailList.size());

			// 3.循环结果，判断备注是新增还是修改
			Set<String> tmodelIdSet = new HashSet<String>();
			Set<String> tmodelOilSet = new HashSet<String>();
			for (WxtZSVehicleOilDetailView mPvehicleOilDetailVo : vehicleTypeList) {
				// 判断车型是否已有
				Map<String, Object> praMap = new HashMap<String, Object>();
				praMap.put("tModelId", mPvehicleOilDetailVo.getZstModelId());
				praMap.put("tId", mPvehicleOilDetailVo.getZstId());
				praMap.put("modelYear", mPvehicleOilDetailVo.getModelYear());
				List<WxtZSVehicleType> vehicleTypeVoList = vehicleTypeMapper.selectByPra(praMap);
				WxtZSVehicleType vehicleTypeVo = new WxtZSVehicleType();
				if (vehicleTypeVoList == null || vehicleTypeVoList.isEmpty()) {
					String primaryKey = mPvehicleOilDetailVo.getZstModelId().trim()
							+ mPvehicleOilDetailVo.getZstId().trim() + mPvehicleOilDetailVo.getModelYear().trim();
					if (!tmodelIdSet.contains(primaryKey)) {
						tmodelIdSet.add(primaryKey);
						vehicleTypeVo.settModelId(mPvehicleOilDetailVo.getZstModelId());
						vehicleTypeVo.settId(mPvehicleOilDetailVo.getZstId());
						vehicleTypeVo.setFactoryName(mPvehicleOilDetailVo.getFactoryName());
						vehicleTypeVo.setBrandName(mPvehicleOilDetailVo.getBrandName());
						vehicleTypeVo.setDmpimp(mPvehicleOilDetailVo.getDmpimp());
						vehicleTypeVo.setVehicleType(mPvehicleOilDetailVo.getVehicleType());
						vehicleTypeVo.setVehicleSeries(mPvehicleOilDetailVo.getVehicleSeries());
						vehicleTypeVo.setVehicleSeriesSop(mPvehicleOilDetailVo.getVehicleSeriesSop());
						vehicleTypeVo.setVehicleSeriesEop(mPvehicleOilDetailVo.getVehicleSeriesEop());
						vehicleTypeVo.settTypeId(mPvehicleOilDetailVo.getZstTypeId());
						vehicleTypeVo.settType(mPvehicleOilDetailVo.getZstType());

						String modelName = mPvehicleOilDetailVo.getZstModelName();
						modelName = modelName.replace(mPvehicleOilDetailVo.getVehicleType(), "");
						String modelYearStr = mPvehicleOilDetailVo.getModelYear() + "款";
						modelName = modelName.replace(modelYearStr, "");
						vehicleTypeVo.settModelName(modelName);

						vehicleTypeVo.setModelYear(mPvehicleOilDetailVo.getModelYear());
						vehicleTypeVo.settModelMsrp(mPvehicleOilDetailVo.getZstModelMsrp());
						vehicleTypeVo.settModelSop(mPvehicleOilDetailVo.getZstModelSop());
						vehicleTypeVo.settModelEop(mPvehicleOilDetailVo.getZstModelEop());
						vehicleTypeVo.setEngine(mPvehicleOilDetailVo.getEngine());
						vehicleTypeVo.setEngineDisplacementCc(mPvehicleOilDetailVo.getEngineDisplacementCc());
						vehicleTypeVo.setEngineDisplacementL(mPvehicleOilDetailVo.getEngineDisplacementL());
						vehicleTypeVo.setPower(mPvehicleOilDetailVo.getPower());
						vehicleTypeVo.setTurboBoost(mPvehicleOilDetailVo.getTurboBoost());
						vehicleTypeVo.setGearboxDesc(mPvehicleOilDetailVo.getGearboxDesc());
						vehicleTypeVo.setDrivingModel(mPvehicleOilDetailVo.getDrivingModel());

						vehicleTypeVo.setImportTime(new Date());
						typeList_new.add(vehicleTypeVo);
					}
				} else {
					vehicleTypeVo = vehicleTypeVoList.get(0);
					vehicleTypeVo.settModelId(mPvehicleOilDetailVo.getZstModelId());
					vehicleTypeVo.settId(mPvehicleOilDetailVo.getZstId());
					vehicleTypeVo.setFactoryName(mPvehicleOilDetailVo.getFactoryName());
					vehicleTypeVo.setBrandName(mPvehicleOilDetailVo.getBrandName());
					vehicleTypeVo.setDmpimp(mPvehicleOilDetailVo.getDmpimp());
					vehicleTypeVo.setVehicleType(mPvehicleOilDetailVo.getVehicleType());
					vehicleTypeVo.setVehicleSeries(mPvehicleOilDetailVo.getVehicleSeries());
					vehicleTypeVo.setVehicleSeriesSop(mPvehicleOilDetailVo.getVehicleSeriesSop());
					vehicleTypeVo.setVehicleSeriesEop(mPvehicleOilDetailVo.getVehicleSeriesEop());
					vehicleTypeVo.settTypeId(mPvehicleOilDetailVo.getZstTypeId());
					vehicleTypeVo.settType(mPvehicleOilDetailVo.getZstType());

					String modelName = mPvehicleOilDetailVo.getZstModelName();
					modelName = modelName.replace(mPvehicleOilDetailVo.getVehicleType(), "");
					String modelYearStr = mPvehicleOilDetailVo.getModelYear() + "款";
					modelName = modelName.replace(modelYearStr, "");
					vehicleTypeVo.settModelName(modelName);

					vehicleTypeVo.setModelYear(mPvehicleOilDetailVo.getModelYear());
					vehicleTypeVo.settModelMsrp(mPvehicleOilDetailVo.getZstModelMsrp());
					vehicleTypeVo.settModelSop(mPvehicleOilDetailVo.getZstModelSop());
					vehicleTypeVo.settModelEop(mPvehicleOilDetailVo.getZstModelEop());
					vehicleTypeVo.setEngine(mPvehicleOilDetailVo.getEngine());
					vehicleTypeVo.setEngineDisplacementCc(mPvehicleOilDetailVo.getEngineDisplacementCc());
					vehicleTypeVo.setEngineDisplacementL(mPvehicleOilDetailVo.getEngineDisplacementL());
					vehicleTypeVo.setPower(mPvehicleOilDetailVo.getPower());
					vehicleTypeVo.setTurboBoost(mPvehicleOilDetailVo.getTurboBoost());
					vehicleTypeVo.setGearboxDesc(mPvehicleOilDetailVo.getGearboxDesc());
					vehicleTypeVo.setDrivingModel(mPvehicleOilDetailVo.getDrivingModel());

					vehicleTypeVo.setUpdateTime(new Date());
					typeList_update.add(vehicleTypeVo);
				}
			}
			
			for(WxtZSVehicleOilDetail oilDetail: oilDetailList){
				// 判断对应的机油推荐是否已有
				Map<String, Object> oilPra = new HashMap<String, Object>();
				oilPra.put("tModelId", oilDetail.gettModelId());
				oilPra.put("oilName", oilDetail.getOilName());
				oilPra.put("oilPartsNo", oilDetail.getOilPartsNo());
				oilPra.put("oilType", oilDetail.getOilType());
				oilPra.put("oilConsumption", oilDetail.getOilConsumption());
				oilPra.put("oilGrade", oilDetail.getOilGrade());
				List<WxtZSVehicleOilDetail> vehicleOilDetailList = vehicleOilDetailMapper.selectByPra(oilPra);
				WxtZSVehicleOilDetail vehicleOilDetailTemp = new WxtZSVehicleOilDetail();
				if (vehicleOilDetailList == null || vehicleOilDetailList.isEmpty()) {
					vehicleOilDetailTemp.setImportTime(new Date());
					oilDetailList_new.add(oilDetail);
				} else {
					oilDetail.setUpdateTime(new Date());
					oilDetailList_update.add(oilDetail);
				}
			}
			
			for(WxtZSVehicleGearoilDetail oilDetail: gearOilDetailList){
				// 判断对应的机油推荐是否已有
				Map<String, Object> oilPra = new HashMap<String, Object>();
				oilPra.put("tModelId", oilDetail.gettModelId());
				oilPra.put("oilName", oilDetail.getOilName());
				oilPra.put("oilPartsNo", oilDetail.getOilPartsNo());
				oilPra.put("oilType", oilDetail.getOilType());
				oilPra.put("oilConsumption", oilDetail.getOilConsumption());
				oilPra.put("oilGrade", oilDetail.getOilGrade());
				List<WxtZSVehicleOilDetail> vehicleOilDetailList = vehicleOilDetailMapper.selectByPra(oilPra);
				WxtZSVehicleOilDetail vehicleOilDetailTemp = new WxtZSVehicleOilDetail();
				if (vehicleOilDetailList == null || vehicleOilDetailList.isEmpty()) {
					vehicleOilDetailTemp.setImportTime(new Date());
					gearoilDetailList_new.add(oilDetail);
				} else {
					oilDetail.setUpdateTime(new Date());
					gearoilDetailList_update.add(oilDetail);
				}
			}
			
			
			// 4.对新增的配件品牌进行分页处理
			// 4.1 新增车型
			ImportDataPageModelUtil vehicleTypeNewPage = new ImportDataPageModelUtil(typeList_new, 30);
			int vehicleTypeNewTotalPage = vehicleTypeNewPage.getTotalPages();
			for (int i = 1; i <= vehicleTypeNewTotalPage; i++) {
				List<WxtZSVehicleType> volst = vehicleTypeNewPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleTypeMapper.insertVehicleTypeBatch(volst);
			}
			// 4.2 修改车型
			ImportDataPageModelUtil vehicleTypeUpdatePage = new ImportDataPageModelUtil(typeList_update, 30);
			int vehicleTypeUpdateTotalPage = vehicleTypeUpdatePage.getTotalPages();
			for (int i = 1; i <= vehicleTypeUpdateTotalPage; i++) {
				List<WxtZSVehicleType> volst = vehicleTypeUpdatePage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleTypeMapper.updateBatch(volst);
			}
			// 4.3 新增机油推荐详情
			ImportDataPageModelUtil oilDetailNewPage = new ImportDataPageModelUtil(oilDetailList_new, 40);
			int oilDetailNewTotalPage = oilDetailNewPage.getTotalPages();
			for (int i = 1; i <= oilDetailNewTotalPage; i++) {
				List<WxtZSVehicleOilDetail> volst = oilDetailNewPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleOilDetailMapper.insertOilDetailBatch(volst);
			}
			// 4.4 更新机滤详情
			ImportDataPageModelUtil oilDetailUpdatePage = new ImportDataPageModelUtil(oilDetailList_update, 40);
			int OilDetailUpdateTotalPage = oilDetailUpdatePage.getTotalPages();
			for (int i = 1; i <= OilDetailUpdateTotalPage; i++) {
				List<WxtZSVehicleOilDetail> volst = oilDetailUpdatePage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleOilDetailMapper.updateBatch(volst);
			}
			
			// 4.3 新增变速箱推荐详情
			ImportDataPageModelUtil gearoilDetailNewPage = new ImportDataPageModelUtil(gearoilDetailList_new, 40);
			int gearoilDetailNewTotalPage = gearoilDetailNewPage.getTotalPages();
			for (int i = 1; i <= oilDetailNewTotalPage; i++) {
				List<WxtZSVehicleGearoilDetail> volst = gearoilDetailNewPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehiclegearOilDetailMapper.insertOilDetailBatch(volst);
			}
			// 4.4 更新变速箱油详情
			ImportDataPageModelUtil gearoilDetailUpdatePage = new ImportDataPageModelUtil(gearoilDetailList_update, 40);
			int gearOilDetailUpdateTotalPage = gearoilDetailUpdatePage.getTotalPages();
			for (int i = 1; i <= OilDetailUpdateTotalPage; i++) {
				List<WxtZSVehicleGearoilDetail> volst = gearoilDetailUpdatePage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehiclegearOilDetailMapper.updateBatch(volst);
			}
			LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.zs.pms.service.impl.WxtZSVehicleOilDetailServiceImpl.importVehicleOilDetailUpdate", "updated...");
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.zs.pms.service.impl.WxtZSVehicleOilDetailServiceImpl.importVehicleOilDetailUpdate", "error...");
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入数据失败,异常信息：" + e.getMessage());
			return resultMap;
		}
		int uploadDataAccount = vehicleTypeList.size();
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
		return resultMap;
	}



	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> optVehicleOilDetail() throws Exception {

		List<WxtZSVehicleOilDetail> oilDetailList_new = new ArrayList<WxtZSVehicleOilDetail>();

		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<WxtZSVehicleType> vehicleTypeList = vehicleTypeMapper.selectWithLYVhehicleByPra(null);

			for (WxtZSVehicleType tempVehicleType : vehicleTypeList) {
				Map<String, Object> oilPra = new HashMap<String, Object>();
				oilPra.put("tModelId", tempVehicleType.gettModelId());

				List<WxtZSVehicleOilDetail> vehicleOilDetailList = vehicleOilDetailMapper.selectByPra(oilPra);
				if (vehicleOilDetailList != null && !vehicleOilDetailList.isEmpty()) {
					String fullSynInit = "";
					String semiSynInit = "";
					String mirealInit = "";

					String oilGrade = "";
					for (WxtZSVehicleOilDetail oilDetail : vehicleOilDetailList) {
						oilGrade = oilGrade + oilDetail.getOilGrade();
						if (ChevronOil.OIL_TYPE_FULL_SYNTHETIC_CN.equals(oilDetail.getOilType())) {
							fullSynInit = fullSynInit + oilDetail.getOilViscosity() + ";";
						} else if (ChevronOil.OIL_TYPE_SEMI_SYNTHETIC_CN.equals(oilDetail.getOilType())) {
							semiSynInit = semiSynInit + oilDetail.getOilViscosity() + ";";
						} else if (ChevronOil.OIL_TYPE_MINERAL_CN.equals(oilDetail.getOilType())) {
							mirealInit = mirealInit + oilDetail.getOilViscosity() + ";";
						}
					}

					List<ZsOil> oilRecomendtList = new ArrayList<ZsOil>();

					List<ZsOil> fullSynOilRecomendtList = ChevronOil.getChevronMatchedOil(fullSynInit, null,
							ChevronOil.OIL_TYPE_FULL_SYNTHETIC);
					if (fullSynOilRecomendtList != null && !fullSynOilRecomendtList.isEmpty()) {
						String fuelType = tempVehicleType.getFuelType();
						if ((fuelType != null && fuelType.equals("柴油"))
								|| tempVehicleType.gettModelName().indexOf("柴油") > 0) {
							// 柴油机推荐
							ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_40, ChevronOil.OIL_TYPE_FULL_SYNTHETIC);
							fullSynOilRecomendtList.clear();
							fullSynOilRecomendtList.add(zsOil);
						} else {
							if (tempVehicleType.getTurboBoost().equals("Y")) {
								if (ChevronOil.getJpuseurCountrySet().contains(tempVehicleType.getCountry())) {
									ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_30,
											ChevronOil.OIL_TYPE_FULL_SYNTHETIC);
									fullSynOilRecomendtList.clear();
									fullSynOilRecomendtList.add(zsOil);
								} else {
									ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_40,
											ChevronOil.OIL_TYPE_FULL_SYNTHETIC);
									fullSynOilRecomendtList.clear();
									fullSynOilRecomendtList.add(zsOil);
								}
							}
						}
						oilRecomendtList.addAll(fullSynOilRecomendtList);
					}
					List<ZsOil> semiSynOilRecomendtList = ChevronOil.getChevronMatchedOil(semiSynInit, null,
							ChevronOil.OIL_TYPE_SEMI_SYNTHETIC);
					if (semiSynOilRecomendtList != null && !semiSynOilRecomendtList.isEmpty()) {
						String fuelType = tempVehicleType.getFuelType();
						if ((fuelType != null && fuelType.equals("柴油"))
								|| tempVehicleType.gettModelName().indexOf("柴油") > 0) {
							// 柴油机推荐
							if (tempVehicleType.getTurboBoost().equals("Y")) {
								ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_40, ChevronOil.OIL_TYPE_FULL_SYNTHETIC);
								semiSynOilRecomendtList.clear();
								semiSynOilRecomendtList.add(zsOil);
							} else {
								ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_10W_40, ChevronOil.OIL_TYPE_SEMI_SYNTHETIC);
								semiSynOilRecomendtList.clear();
								semiSynOilRecomendtList.add(zsOil);
							}
						} else {
							if (tempVehicleType.getTurboBoost().equals("Y")) {
								if (ChevronOil.getJpuseurCountrySet().contains(tempVehicleType.getCountry())) {
									ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_30,
											ChevronOil.OIL_TYPE_SEMI_SYNTHETIC);
									semiSynOilRecomendtList.clear();
									semiSynOilRecomendtList.add(zsOil);
								} else {
									ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_40,
											ChevronOil.OIL_TYPE_SEMI_SYNTHETIC);
									semiSynOilRecomendtList.clear();
									semiSynOilRecomendtList.add(zsOil);
								}
							}
						}
						oilRecomendtList.addAll(semiSynOilRecomendtList);
					}
					List<ZsOil> mirealOilRecomendtList = ChevronOil.getChevronMatchedOil(mirealInit, null,
							ChevronOil.OIL_TYPE_MINERAL);
					if (mirealOilRecomendtList != null && !mirealOilRecomendtList.isEmpty()) {
						String fuelType = tempVehicleType.getFuelType();
						if ((fuelType != null && fuelType.equals("柴油"))
								|| tempVehicleType.gettModelName().indexOf("柴油") > 0) {
							// 柴油机推荐
							ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_10W_40, ChevronOil.OIL_TYPE_MINERAL);
							mirealOilRecomendtList.clear();
							mirealOilRecomendtList.add(zsOil);
						} else {
							if (tempVehicleType.getTurboBoost().equals("Y")) {
								if (ChevronOil.getJpuseurCountrySet().contains(tempVehicleType.getCountry())) {
									ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_5W_30, ChevronOil.OIL_TYPE_MINERAL);
									mirealOilRecomendtList.clear();
									mirealOilRecomendtList.add(zsOil);
								} else {
									ZsOil zsOil = new ZsOil(ChevronOil.CHEVRON_10W_40, ChevronOil.OIL_TYPE_MINERAL);
									mirealOilRecomendtList.clear();
									mirealOilRecomendtList.add(zsOil);
								}
							}
						}
						oilRecomendtList.addAll(mirealOilRecomendtList);
					}
					for (ZsOil zsOil : oilRecomendtList) {
						WxtZSVehicleOilDetail oilDetail = new WxtZSVehicleOilDetail();
						oilDetail.settModelId(tempVehicleType.gettModelId());
						oilDetail.setOilName("优化机油");
						oilDetail.setOilConsumption(vehicleOilDetailList.get(0).getOilConsumption());
						oilDetail.setOilGrade("SN");
						oilDetail.setOilViscosity(zsOil.getViscosity());
						if (zsOil.getType() == ChevronOil.OIL_TYPE_FULL_SYNTHETIC) {
							oilDetail.setOilType(ChevronOil.OIL_TYPE_FULL_SYNTHETIC_CN);
						} else if (zsOil.getType() == ChevronOil.OIL_TYPE_SEMI_SYNTHETIC) {
							oilDetail.setOilType(ChevronOil.OIL_TYPE_SEMI_SYNTHETIC_CN);
						} else if (zsOil.getType() == ChevronOil.OIL_TYPE_MINERAL) {
							oilDetail.setOilType(ChevronOil.OIL_TYPE_MINERAL_CN);
						}
						/*oilDetailList_new.add(oilDetail);*/

						oilDetail.setImportTime(new Date());
						WxtZSVehicleOilDetailExample zsd = new WxtZSVehicleOilDetailExample();
						WxtZSVehicleOilDetailExample.Criteria detailCriteria = zsd.createCriteria();
						detailCriteria.andTModelIdEqualTo(tempVehicleType.gettModelId());
						detailCriteria.andOilNameEqualTo("优化机油");
						//2015年之前生产的德系车（宝马、奔驰、奥迪、大众），推荐5W-30的机油的，全部改成5W-40（包括半合成和全合成）
						if(tempVehicleType.getBrandName().endsWith("宝马")
							|| tempVehicleType.getBrandName().endsWith("奔驰")
							|| tempVehicleType.getBrandName().endsWith("奥迪")
							|| tempVehicleType.getBrandName().endsWith("大众")){
							if(!StringUtils.isEmpty(tempVehicleType.getModelYear())){
								Long yearLong = Long.valueOf(tempVehicleType.getModelYear());
								if(yearLong < 2015){
									if(zsOil.getViscosity().equals(ChevronOil.CHEVRON_5W_30) &&
											(oilDetail.getOilType().equals(ChevronOil.OIL_TYPE_FULL_SYNTHETIC) 
											|| oilDetail.getOilType().equals(ChevronOil.OIL_TYPE_SEMI_SYNTHETIC)))
									{
										detailCriteria.andOilViscosityEqualTo(ChevronOil.CHEVRON_5W_40);
									}
								}
							}
						}else{					
							detailCriteria.andOilViscosityEqualTo(zsOil.getViscosity());
						}					
						detailCriteria.andOilTypeEqualTo(oilDetail.getOilType());
						List<WxtZSVehicleOilDetail> detailList = vehicleOilDetailMapper.selectByExample(zsd);
						if (detailList == null || detailList.isEmpty()) {
							vehicleOilDetailMapper.insertSelective(oilDetail);
						}
					}
				}
			}

			// 4.3 新增机油推荐详情
			/*ImportDataPageModelUtil oilDetailNewPage = new ImportDataPageModelUtil(
					oilDetailList_new, 40);
			int oilDetailNewTotalPage = oilDetailNewPage.getTotalPages();
			for (int i = 1; i <= oilDetailNewTotalPage; i++) {
				List<WxtZSVehicleOilDetail> volst = oilDetailNewPage
						.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleOilDetailMapper.insertOilDetailBatch(volst);
			}*/
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "转换数据失败,异常信息：" + e.getMessage());
			log.error("", e);
			throw e;
		}
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "转换成功");
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> synLYOilToZsOilDetail() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<WxtZSVehicleOilDetail> oilDetailList = vehicleTypeMapper.selectVehicleTypeByLevelIdWithNorecommend();
		try {

			for (WxtZSVehicleOilDetail oilDetail : oilDetailList) {
				oilDetail.setOilName("机油(LY)");
				oilDetail.setImportTime(new Date());
			}
			ImportDataPageModelUtil oilDetailNewPage = new ImportDataPageModelUtil(oilDetailList, 40);
			int oilDetailNewTotalPage = oilDetailNewPage.getTotalPages();
			for (int i = 1; i <= oilDetailNewTotalPage; i++) {
				List<WxtZSVehicleOilDetail> volst = oilDetailNewPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				vehicleOilDetailMapper.insertOilDetailBatch(volst);
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "同步数据失败,异常信息：" + e.getMessage());
			return resultMap;
		}
		int uploadDataAccount = oilDetailList.size();
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "同步成功,总数量：" + uploadDataAccount);
		return resultMap;
	}

}
