INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.getAppKey.url', 'http://************:61616/api/DataTransfer/simpleDatapipe', 'QRCODE中商appKey获取接口URL');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.getAppKey.business_type', '3000', 'QRCODE中商appKey获取接口business_type');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.getAppKey.FACID', '100173', 'QRCODE中商appKey获取接口FACID');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.getAppKey.PlaceId', 'L0001', 'QRCODE中商appKey获取接口PlaceId');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.getAppKey.userCode', 'L0001', 'QRCODE中商appKey获取接口userCode');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.getAppKey.passWord', 'L0001@2017', 'QRCODE中商appKey获取接口passWord');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.queryTrackInfo.url', 'http://************:61616/api/DataTransfer/simpleDatapipe', 'QRCODE中商物流信息查询接口URL');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Qrcode.Zs.queryTrackInfo.business_type', 'SP_Query_Digit', 'QRCODE中商物流信息查询接口business_type');
