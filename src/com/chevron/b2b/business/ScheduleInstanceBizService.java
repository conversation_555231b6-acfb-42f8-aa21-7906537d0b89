package com.chevron.b2b.business;

import com.chevron.b2b.model.ScheduleInstance;
import com.chevron.b2b.model.ScheduleInstanceExample;
import com.chevron.b2b.model.ScheduleInstanceParams;
import com.common.exception.WxPltException;

import java.util.List;
import java.util.Map;

/**
 * 计划实例业务接口
 * <AUTHOR>
 * @version 1.0 2018-12-20 17:16
 */
public interface ScheduleInstanceBizService {
	
	/**
	 * 保存计划实例
	 * @param record 被插入计划实例
	 * @throws WxPltException
	 */
	public void insert(ScheduleInstance record) throws WxPltException;
	
	/**
	 * 修改计划实例
	 * @param record 被修改计划实例
	 * @throws WxPltException
	 */
	public void update(ScheduleInstance record) throws WxPltException;
	
	/**
	 * 删除计划实例
	 * @param ids 被删除计划实例id集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的计划实例
	 * @param example 被删除计划实例满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(ScheduleInstanceExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<ScheduleInstance> queryByExample(ScheduleInstanceExample example) throws WxPltException;
	
	/**
	 * 列表查询计划实例对象
	 * @param params 查询条件
	 * @return 满足条件计划实例对象集合
	 */
    public List<ScheduleInstance> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 获取指定主键的计划实例对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public ScheduleInstance getBean(Long id) throws WxPltException;	

	/**
	 * 分页查询计划实例
	 * @param params 查询参数
	 * @param resultMap 结果对象
	 * @throws WxPltException
	 */
	public void queryForPage(ScheduleInstanceParams params, Map<String, Object> resultMap) throws WxPltException;
	
	public void addAgree(Long activityId, String userId) throws WxPltException;
	
	public void cancelAgree(Long activityId, String userId) throws WxPltException;
}
