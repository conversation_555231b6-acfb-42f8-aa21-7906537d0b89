--select top 100 * from wx_t_userrole
--INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, (select role_id from wx_t_role where ch_role_name='Elite_Distributor'),1
from wx_t_user u 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where (u.type is null or u.type!='1') and u.status=1 and r.ch_role_name in ('CDM_DIST')
and not exists (select 1 from wx_t_user u1 left join wx_t_userrole ur1 on u1.user_id=ur1.user_id
	left join wx_t_role r1 on r1.role_id=ur1.role_id
	where (u1.type is null or u1.type != '1')
	and r1.ch_role_name in ('Service_Partner_Admin', 'Service_Partner_Manager')
	and u1.status=1 and u1.org_id=u.org_id)
and pe.distributor_id in (53)

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, r.role_id,1
from wx_t_user u 
left join wx_t_role r on 1=1
where (u.type is null or u.type!='1') and u.status=1 and r.ch_role_name in ('Chevron_Material_Admin','Chevron_Material_Supplier')
and u.login_name in ('13632429835',
'13662315485',
'13570965620',
'15922252344',
'13570236050',
'13648068453',
'13500032285')