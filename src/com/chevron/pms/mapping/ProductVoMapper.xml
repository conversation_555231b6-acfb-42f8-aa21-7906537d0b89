<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.pms.dao.ProductVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.ProductVo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="name" property="name" jdbcType="NVARCHAR" />
    <result column="category" property="category" jdbcType="NVARCHAR" />
    <result column="category_text" property="categoryText" jdbcType="NVARCHAR" />
    <result column="specification" property="specification" jdbcType="NVARCHAR" />
    <result column="specifications" property="specifications" jdbcType="NVARCHAR" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="brand" property="brand" jdbcType="NVARCHAR" />
    <result column="oil_type" property="oilType" jdbcType="NVARCHAR" />
    <result column="oil_type_text" property="oilTypeText" jdbcType="NVARCHAR" />
    <result column="price" property="price" jdbcType="NUMERIC" />
    <result column="sale_price" property="salePrice" jdbcType="NUMERIC" />
    <result column="box_price" property="boxPrice" jdbcType="NUMERIC" />
    <result column="box_capacity" property="boxCapacity" jdbcType="NUMERIC" />
    <result column="viscosity" property="viscosity" jdbcType="NVARCHAR" />
    <result column="viscosity_text" property="viscosityText" jdbcType="NVARCHAR" />
    <result column="capacity" property="capacity" jdbcType="NVARCHAR" />
    <result column="oil_grade" property="oilGrade" jdbcType="NVARCHAR" />
    <result column="package_type" property="packageType" jdbcType="NVARCHAR" />
    <result column="sales_channels" property="salesChannels" jdbcType="NVARCHAR" />
    <result column="bar_code" property="barCode" jdbcType="NVARCHAR" />
    <result column="extra_code" property="extraCode" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="detail_desc" property="detailDesc" jdbcType="NVARCHAR" />
    <result column="is_competing" property="isCompeting" jdbcType="INTEGER" />
    <result column="is_collect" property="isCollect" jdbcType="INTEGER" />
    <result column="icon_id" property="iconId" jdbcType="BIGINT"/>
    <result column="doc_id" property="docId" jdbcType="BIGINT"/>
    <result column="can_o2o_reserve" property="canO2oReserve" jdbcType="INTEGER" />
    <result column="bottlesperbox" property="bottlesperbox" jdbcType="NVARCHAR" />
    <result column="packageunit" property="packageunit" jdbcType="NVARCHAR" />
	<result column="can_verify" property="canVerify" jdbcType="INTEGER"/>
	<result column="product_property" property="productProperty" jdbcType="NVARCHAR"/>
	<result column="bottle_qty" property="bottleQty" jdbcType="INTEGER"/>
		<result column="product_channel" property="productChannel" jdbcType="VARCHAR"/>
    
  </resultMap>
  <sql id="Base_Column_List" >
    id, sku, name, category, specification, specifications, units, brand, oil_type, price, 
    sale_price, viscosity, capacity, oil_grade, package_type, sales_channels, bar_code, 
    extra_code, create_time, update_time, creator, remark, detail_desc,is_competing, 
    is_collect, doc_id,can_o2o_reserve,can_verify,box_price,box_capacity,product_property,product_channel 
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_product
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_product
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.ProductVo" >
    insert into wx_t_product ( sku, name, 
      category, specification, specifications, 
      units, brand, oil_type, 
      price, sale_price, box_price, box_capacity, viscosity, 
      capacity, oil_grade, package_type, 
      sales_channels, bar_code, extra_code, 
      create_time, update_time, creator, 
      remark, detail_desc, is_competing, 
      is_collect, doc_id, can_o2o_reserve)
    values ( #{sku,jdbcType=NVARCHAR}, #{name,jdbcType=NVARCHAR}, 
      #{category,jdbcType=NVARCHAR}, #{specification,jdbcType=NVARCHAR}, #{specifications,jdbcType=NVARCHAR}, 
      #{units,jdbcType=NVARCHAR}, #{brand,jdbcType=NVARCHAR}, #{oilType,jdbcType=NVARCHAR}, 
      #{price,jdbcType=NUMERIC}, #{salePrice,jdbcType=NUMERIC}, #{boxPrice,jdbcType=NUMERIC},#{boxCapacity,jdbcType=NUMERIC}, #{viscosity,jdbcType=NVARCHAR}, 
      #{capacity,jdbcType=NVARCHAR}, #{oilGrade,jdbcType=NVARCHAR}, #{packageType,jdbcType=NVARCHAR}, 
      #{salesChannels,jdbcType=NVARCHAR}, #{barCode,jdbcType=NVARCHAR}, #{extraCode,jdbcType=NVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=NVARCHAR}, 
      #{remark,jdbcType=NVARCHAR}, #{detailDesc,jdbcType=NVARCHAR}, #{isCompeting,jdbcType=INTEGER}, 
      #{isCollect,jdbcType=INTEGER}, #{docId,jdbcType=BIGINT}, #{canO2oReserve,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.ProductVo" >
    insert into wx_t_product
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="category != null" >
        category,
      </if>
      <if test="specification != null" >
        specification,
      </if>
      <if test="specifications != null" >
        specifications,
      </if>
      <if test="units != null" >
        units,
      </if>
      <if test="brand != null" >
        brand,
      </if>
      <if test="oilType != null" >
        oil_type,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="salePrice != null" >
        sale_price,
      </if>
      <if test="viscosity != null" >
        viscosity,
      </if>
      <if test="capacity != null" >
        capacity,
      </if>
      <if test="oilGrade != null" >
        oil_grade,
      </if>
      <if test="packageType != null" >
        package_type,
      </if>
      <if test="salesChannels != null" >
        sales_channels,
      </if>
      <if test="barCode != null" >
        bar_code,
      </if>
      <if test="extraCode != null" >
        extra_code,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="detailDesc != null" >
        detail_desc,
      </if> 
      <if test="isCompeting != null" >
        is_competing,
      </if>
      <if test="isCollect != null" >
        is_collect,
      </if>
      <if test="docId != null" >
        doc_id,
      </if>
      <if test="canO2oReserve != null" >
        can_o2o_reserve,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=NVARCHAR},
      </if>
      <if test="specification != null" >
        #{specification,jdbcType=NVARCHAR},
      </if>
      <if test="specifications != null" >
        #{specifications,jdbcType=NVARCHAR},
      </if>
      <if test="units != null" >
        #{units,jdbcType=NVARCHAR},
      </if>
      <if test="brand != null" >
        #{brand,jdbcType=NVARCHAR},
      </if>
      <if test="oilType != null" >
        #{oilType,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="salePrice != null" >
        #{salePrice,jdbcType=NUMERIC},
      </if>
      <if test="viscosity != null" >
        #{viscosity,jdbcType=NVARCHAR},
      </if>
      <if test="capacity != null" >
        #{capacity,jdbcType=NVARCHAR},
      </if>
      <if test="oilGrade != null" >
        #{oilGrade,jdbcType=NVARCHAR},
      </if>
      <if test="packageType != null" >
        #{packageType,jdbcType=NVARCHAR},
      </if>
      <if test="salesChannels != null" >
        #{salesChannels,jdbcType=NVARCHAR},
      </if>
      <if test="barCode != null" >
        #{barCode,jdbcType=NVARCHAR},
      </if>
      <if test="extraCode != null" >
        #{extraCode,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="detailDesc != null" >
        #{detailDesc,jdbcType=NVARCHAR},
      </if>
      <if test="isCompeting != null" >
        #{isCompeting,jdbcType=INTEGER},
      </if>
      <if test="isCollect != null" >
        #{isCollect,jdbcType=INTEGER},
      </if>
      <if test="docId != null" >
        #{docId,jdbcType=BIGINT},
      </if>
      <if test="canO2oReserve != null" >
        #{canO2oReserve,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.ProductVo" >
    update wx_t_product
    <set >
      <if test="sku != null" >
        sku = #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=NVARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=NVARCHAR},
      </if>
      <if test="specification != null" >
        specification = #{specification,jdbcType=NVARCHAR},
      </if>
      <if test="specifications != null" >
        specifications = #{specifications,jdbcType=NVARCHAR},
      </if>
      <if test="units != null" >
        units = #{units,jdbcType=NVARCHAR},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=NVARCHAR},
      </if>
      <if test="oilType != null" >
        oil_type = #{oilType,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="salePrice != null" >
        sale_price = #{salePrice,jdbcType=NUMERIC},
      </if>
      <if test="viscosity != null" >
        viscosity = #{viscosity,jdbcType=NVARCHAR},
      </if>
      <if test="capacity != null" >
        capacity = #{capacity,jdbcType=NVARCHAR},
      </if>
      <if test="oilGrade != null" >
        oil_grade = #{oilGrade,jdbcType=NVARCHAR},
      </if>
      <if test="packageType != null" >
        package_type = #{packageType,jdbcType=NVARCHAR},
      </if>
      <if test="salesChannels != null" >
        sales_channels = #{salesChannels,jdbcType=NVARCHAR},
      </if>
      <if test="barCode != null" >
        bar_code = #{barCode,jdbcType=NVARCHAR},
      </if>
      <if test="extraCode != null" >
        extra_code = #{extraCode,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="detailDesc != null" >
        detail_desc = #{detailDesc,jdbcType=NVARCHAR},
      </if>
      <if test="isCompeting != null" >
        is_competing = #{isCompeting,jdbcType=INTEGER},
      </if>
      <if test="isCollect != null" >
        is_collect = #{isCollect,jdbcType=INTEGER},
      </if>
      <if test="docId != null" >
        doc_id = #{docId,jdbcType=BIGINT},
      </if>
      <if test="canO2oReserve != null" >
        can_o2o_reserve = #{canO2oReserve,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.ProductVo" >
    update wx_t_product
    set sku = #{sku,jdbcType=NVARCHAR},
      name = #{name,jdbcType=NVARCHAR},
      category = #{category,jdbcType=NVARCHAR},
      specification = #{specification,jdbcType=NVARCHAR},
      specifications = #{specifications,jdbcType=NVARCHAR},
      units = #{units,jdbcType=NVARCHAR},
      brand = #{brand,jdbcType=NVARCHAR},
      oil_type = #{oilType,jdbcType=NVARCHAR},
      price = #{price,jdbcType=NUMERIC},
      sale_price = #{salePrice,jdbcType=NUMERIC},
      box_price = #{boxPrice,jdbcType=NUMERIC},
      box_capacity = #{boxCapacity,jdbcType=NUMERIC},
      viscosity = #{viscosity,jdbcType=NVARCHAR},
      capacity = #{capacity,jdbcType=NVARCHAR},
      oil_grade = #{oilGrade,jdbcType=NVARCHAR},
      package_type = #{packageType,jdbcType=NVARCHAR},
      sales_channels = #{salesChannels,jdbcType=NVARCHAR},
      bar_code = #{barCode,jdbcType=NVARCHAR},
      extra_code = #{extraCode,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR},
      detail_desc = #{detailDesc,jdbcType=NVARCHAR},
      is_competing = #{isCompeting,jdbcType=INTEGER},
      doc_id = #{docId,jdbcType=BIGINT},
      is_collect = #{isCollect,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="getAllProducts"  resultMap="BaseResultMap">
	    select 
	    <include refid="Base_Column_List" />
	    from wx_t_product
  </select>
  
   <select id="getProductsByParam" parameterType="java.util.Map" resultMap="BaseResultMap">
	    select 
	    <if test="limit != null">
	    	top ${limit}
	    </if>
	    <include refid="Base_Column_List" />
	    ,(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = ID and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id,
	(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.oilType' and di1.dic_item_code=p.oil_type) oil_type_text,
	(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.package.bottlesperbox' and di1.dic_item_code=p.sku ) bottlesperbox,
	(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='product.package.bottlesperbox' and di1.dic_item_code=p.sku ) packageunit
	    from wx_t_product p
	    where
	     <if test="queryField !=null">
	    	(sku  LIKE '%' + #{queryField} + '%' or name  LIKE '%' + #{queryField} + '%')
	    and
	    </if>
	    <if test="sku!=null">
	    	sku  LIKE '%' + #{sku} + '%'
	    and
	    </if>
	    <if test="name!=null and nameFpy == null">
	    	name  LIKE '%' + #{name} + '%'
	    and
	    </if>
	    <if test="name!=null and nameFpy!=null">
	    	(name  LIKE #{name} or left(dbo.f_GetPyToAboutHanyu(name),500) LIKE  #{nameFpy})
	    and
	    </if>
	    <if test="productChannelList != null">
	    exists (select 1 from wx_t_value_transform_map vtm where vtm.transform_type='ProductChannelMapping' and vtm.value_before_transform=p.product_channel and vtm.value_after_transform in
	    	<foreach item="item" index="index" collection="productChannelList" open="(" separator="," close=")">  
			 #{item}  
			</foreach> 
	    )
	    and
	    </if>
	    <if test="keyWord != null">
	    	(sku like #{keyWord} or name like #{keyWord} or left(dbo.f_GetPyToAboutHanyu(name),500) LIKE  #{keyWordPy})
	    	and
	    </if>
	    <if test="brandList!=null">
	    	brand in
	    	<foreach item="brand" index="index" collection="brandList" open="(" separator="," close=")">  
			 #{brand}  
			</foreach> 
			and
	    </if>
	    <if test="oilTypeList!=null">
	    	oil_type in
	    	<foreach item="oilType" index="index" collection="oilTypeList" open="(" separator="," close=")">  
			 #{oilType}  
			</foreach> 
			and
	    </if>
	    <if test="priceList!=null">
	    	price in
	    	<foreach item="price" index="index" collection="priceList" open="(" separator="," close=")">  
			 #{price}  
			</foreach> 
			and
	    </if>
	     <if test="startPrice!=null">
	    	price between #{startPrice,jdbcType=NUMERIC} AND #{endPrice,jdbcType=NUMERIC} and
	    </if>
	    <if test="viscosityList!=null">
	    	viscosity in
	    	<foreach item="viscosity" index="index" collection="viscosityList" open="(" separator="," close=")">  
			 #{viscosity}  
			</foreach> 
			and
	    </if>
	    <if test="capacityList!=null">
	    	capacity in
	    	<foreach item="capacity" index="index" collection="capacityList" open="(" separator="," close=")">  
			 #{capacity}  
			</foreach> 
			and
	    </if>
	    <if test="categoryList!=null">
	    	category in
	    	<foreach item="category" index="index" collection="categoryList" open="(" separator="," close=")">  
			 #{category}  
			</foreach> 
			and
	    </if>
	    <if test="skuList!=null">
	    	p.sku in
	    	<foreach item="item" index="index" collection="skuList" open="(" separator="," close=")">  
			 #{item}  
			</foreach> 
			and
	    </if>
	    <if test="isQrCode != null">
	    	is_qrcode = #{isQrCode}
	    	and
	    </if>
	    <if test="isCompeting != null">
	    	is_competing = #{isCompeting}
	    	and
	    </if>
	    <if test="isCollect != null">
	    	is_collect = #{isCollect}
	    	and
	    </if>
	    <if test="supportOrder != null">
	    	support_order = #{supportOrder}
	    	and
	    </if>
	    <if test="productProperty != null">
	    	product_property = #{productProperty}
	    	and
	    </if>
	    status = 1 
		 <if test="constraintSql != null">
		 	and ${constraintSql}
		 </if>
		 <if test="workshopId != null">
	and exists (select 1 from wx_t_dealer_product_permission dpp1 join wx_t_workshop_partner wp1 on wp1.partner_id=dpp1.dealer_id where wp1.workshop_id=#{workshopId} and dpp1.product_sku=p.sku)
		 </if>
		 <if test="partnerId != null">
	and exists (select 1 from wx_t_dealer_product_permission dpp1 where dpp1.dealer_id=#{partnerId} and dpp1.product_sku=p.sku)
		 </if>
		 <if test="workshopId != null">
	and exists (select 1 from wx_t_dealer_product_permission dpp1 join wx_t_workshop_partner wp1 on wp1.partner_id=dpp1.dealer_id where wp1.workshop_id=#{workshopId} and dpp1.product_sku=p.sku)
		 </if>
	    order by sku asc
  </select>
  
  <select id="getProductsByKeyword" parameterType="java.util.Map" resultMap="BaseResultMap">
	    select 
	    <include refid="Base_Column_List" />
	    ,(select af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = ID and af.SOURCE_TYPE = '5') as icon_id
	    from wx_t_product
	    where 1 = 1
	    <if test="keyword != null">
	    	and (sku like '%' + #{keyword} + '%' or name like '%' + #{keyword} + '%')
	    </if>
	    <if test="isQrCode != null">
	    	and is_qrcode = #{isQrCode}
	    </if>
	    <if test="isCompetitor != null">
	    	and is_competing = #{isCompetitor}
	    </if>
	    order by create_time desc
  </select>
  
  <delete id="deleteProductByCodes">
	    delete from wx_t_product
	    where sku in
		<foreach item="skus" index="index" collection="array" open="(" separator="," close=")">  
			 #{skus}  
		</foreach> 
  </delete>
  
  <delete id="deleteBySku" parameterType="java.lang.String" >
    delete from wx_t_product
    where sku = #{sku,jdbcType=NVARCHAR}
  </delete>
  <select id="selectBySku" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select (select top 1 af1.att_id from wx_att_file af1 where af1.source_id=t1.id and af1.source_type='5' order by af1.create_time asc) icon_id,
    <include refid="Base_Column_List" /> ,bottle_qty
    from wx_t_product t1
    where sku = #{sku,jdbcType=NVARCHAR}
  </select>
  <select id="selectQrCodeProduct" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_product
    where is_qrcode = 1
  </select>
  <select id="selectTcpProduct" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_product
    where category = 'TCP'
  </select>
  
   <select id="seletctByWorkshopId" resultMap="BaseResultMap" parameterType="map">
    SELECT 
       row_number() over(order by t.id asc) as rownumber,
       t.quantity, 
	   t.id,
	   t1.brand,
	   t1.oil_type,
       t1.name,
       t1.units,
       t1.category,
       t2.work_shop_name,
       t1.sku,
       t.workshop_id,
       t1.capacity,
       (select top 1 af1.att_id from wx_att_file af1 where af1.source_id=t1.id and af1.source_type='5' order by af1.create_time asc) icon_id
	FROM wx_t_inventory t
	JOIN wx_t_product t1 ON t.sku=t1.sku
	JOIN wx_t_work_shop t2 ON t.workshop_id = t2.id
	WHERE t.status=1
	AND t.workshop_id = #{workshopId}
  </select>
  
    <select id="queryProductForPage" parameterType="com.chevron.pms.model.ProductParams" resultMap="BaseResultMap">
	    select (select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = p1.ID and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id,
    p1.id, sku, p1.name, p1.category, p1.specification, p1.specifications, p1.units, p1.brand, p1.oil_type, p1.price, 
    p1.sale_price, p1.viscosity, p1.capacity, p1.oil_grade, p1.package_type, p1.sales_channels, p1.bar_code, 
    p1.extra_code, p1.create_time, p1.update_time, p1.creator, p1.remark, p1.detail_desc,is_competing, 
    p1.is_collect, di_ot.dic_item_name oil_type_text, di_viscosity.dic_item_name viscosity_text, di_category.dic_item_name category_text, p1.doc_id
	    from wx_t_product p1
	    left join wx_t_dic_item di_ot on di_ot.dic_type_code='product.oilType' and di_ot.dic_item_code=p1.oil_type
	    left join wx_t_dic_item di_viscosity on di_viscosity.dic_type_code='product.viscosity' and di_viscosity.dic_item_code=p1.viscosity
	    left join wx_t_dic_item di_category on di_category.dic_type_code='product.category' and di_viscosity.dic_item_code=p1.category
	    where
	     <if test="queryField !=null and queryField != ''">
	    	(sku  LIKE '%' + #{queryField} + '%' or name  LIKE '%' + #{queryField} + '%')
	    and
	    </if>
	    <if test="sku!=null and sku != ''">
	    	sku  LIKE '%' + #{sku} + '%'
	    and
	    </if>
	    <if test="name!=null and name != ''">
	    	name  LIKE '%' + #{name} + '%'
	    and
	    </if>
	    <if test="productMultiParamQueryVo != null and productMultiParamQueryVo.brandList!=null">
	    	brand in
	    	<foreach item="brand" index="index" collection="productMultiParamQueryVo.brandList" open="(" separator="," close=")">  
			 '${brand}'  
			</foreach> 
			and
	    </if>
	    <if test="productMultiParamQueryVo != null and productMultiParamQueryVo.oilTypeList!=null">
	    	oil_type in
	    	<foreach item="oilType" index="index" collection="productMultiParamQueryVo.oilTypeList" open="(" separator="," close=")">  
			 '${oilType}'  
			</foreach> 
			and
	    </if>
	    <if test="productMultiParamQueryVo != null and productMultiParamQueryVo.viscosityList!=null">
	    	viscosity in
	    	<foreach item="viscosity" index="index" collection="productMultiParamQueryVo.viscosityList" open="(" separator="," close=")">  
			 '${viscosity}'  
			</foreach> 
			and
	    </if>
	    <if test="productMultiParamQueryVo != null and productMultiParamQueryVo.capacityList!=null">
	    	capacity in
	    	<foreach item="capacity" index="index" collection="productMultiParamQueryVo.capacityList" open="(" separator="," close=")">  
			 '${capacity}'  
			</foreach> 
			and
	    </if>
	    <if test="productMultiParamQueryVo != null and productMultiParamQueryVo.categoryList!=null">
	    	category in
	    	<foreach item="category" index="index" collection="productMultiParamQueryVo.categoryList" open="(" separator="," close=")">  
			 '${category}'  
			</foreach> 
			and
	    </if>
	    <if test="isCompeting != null">
	    	is_competing = #{isCompeting}
	    	and
	    </if>
	    <if test="supportOrder != null">
	    	support_order = #{supportOrder}
	    	and
	    </if>
	    <if test="partnerId != null">
			exists (select 1 from wx_t_dealer_product_permission dpp1 where dpp1.dealer_id=#{partnerId} and dpp1.product_sku=p1.sku)
			and 
	    </if>
	    category!='QT'
  </select>
  <select id="getCompetingProducts" parameterType="java.util.Map" resultMap="BaseResultMap">
	    select (select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = p1.ID and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id,
    p1.id, sku, p1.name, p1.category, p1.specification, p1.specifications, p1.units, p1.brand, p1.oil_type, p1.price, 
    p1.sale_price, p1.viscosity, p1.capacity, p1.oil_grade, p1.package_type, p1.sales_channels, p1.bar_code, 
    p1.extra_code, p1.create_time, p1.update_time, p1.creator, p1.remark, p1.detail_desc,is_competing, 
    p1.is_collect, di_ot.dic_item_name oil_type_text, di_viscosity.dic_item_name viscosity_text, di_category.dic_item_name category_text, p1.doc_id
	    from wx_t_product p1
	    left join wx_t_dic_item di_ot on di_ot.dic_type_code='product.oilType' and di_ot.dic_item_code=p1.oil_type
	    left join wx_t_dic_item di_viscosity on di_viscosity.dic_type_code='product.viscosity' and di_viscosity.dic_item_code=p1.viscosity
	    left join wx_t_dic_item di_category on di_category.dic_type_code='product.category' and di_viscosity.dic_item_code=p1.category
	    where is_competing = 1
	    <if test="productId != null">
	    and exists (select 1 from wx_t_product p2 where p2.id=#{productId} and p2.oil_type=p1.oil_type and p2.viscosity=p1.viscosity and p2.category = p1.category)
	    </if>
	    order by create_time desc
  </select>
  
   <select id="selectProductByMap" parameterType="java.util.Map" resultMap="BaseResultMap">
	    select
	    	*,(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = ID and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id
	    from wx_t_product p
	    where  
	    <if test="skus!=null" >
	    	sku in
	    	<foreach item="item" index="index" collection="skus" open="(" separator="," close=")">
	    		 #{item,jdbcType=NVARCHAR}
	    	</foreach>
		 </if>
		 <if test="category!=null">
		 	category=#{category,jdbcType=NVARCHAR}
		 </if>
		 <if test="oilViscosity!=null">
		    viscosity = #{oilViscosity}
		    and
		    sku LIKE '%01A'
		 </if>
		  <if test="categorys!=null">
            category in
            <foreach item="item" index="index" collection="categorys" open="(" separator="," close=")">
                 #{item,jdbcType=NVARCHAR}
            </foreach>
         </if>
		 ORDER BY sku 
  </select>
  <select id="qrcodeWithNoTraceNo" resultType="int" parameterType="map" >
    select count(1) from wx_t_product p 
join wx_t_dic_item di on di.dic_type_code='product.category.qrcodeWithNoTraceNo' and di.dic_item_code=p.category and di.status=1
where p.sku=#{sku,jdbcType=NVARCHAR}
  </select>
  
  <select id="getProductPropertyBySku" resultType="java.lang.String" parameterType="java.lang.String" >
    select p.product_property from wx_t_product p 
		where p.sku=#{sku,jdbcType=NVARCHAR}
  </select>
  
  <select id="getProductInfosBySku" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select  
     <include refid="Base_Column_List" />
     from wx_t_product p 
		where p.sku=#{sku,jdbcType=NVARCHAR}
  </select>
  
</mapper>