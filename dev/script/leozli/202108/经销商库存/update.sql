--insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('InventoryCheck2021.checkCategory', '库存盘点分类配置', '库存盘点分类配置');
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('InventoryCheck2021.checkCategory', 'INVENTORY_CHECK', '库存盘点', '', '1', 10);
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('InventoryCheck2021.checkCategory', 'INVENTORY_MOVE', '库存转移', '', '1', 20);
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('InventoryCheck2021.checkCategory', 'INVENTORY_OTHERS', '其它', '', '1', 30);

update wx_t_role set user_flag=user_flag|(128+256) where ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')
update wx_t_role set user_flag=user_flag|256 where ch_role_name in ('Service_Partner_BD','Caltex_BD')

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('Workshop.customerType', '16', '个人', '3', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('Workshop.customerType', '32', '批发商', '3', '1');

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Workshop.editPage', 1024, 1, '新增个人/批发商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'Workshop.editPage', 1024, 1, '新增个人/批发商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Workshop.editPage', 1024, 1, '新增个人/批发商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Workshop.editPage', 1024, 1, '新增个人/批发商', 1, getdate());

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('PartnerOrder.inStockTime', '采购订单入库时间设置', '采购订单入库时间设置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('PartnerOrder.inStockTime', 'startDate', '2021-10-01', '开始日期', '1', 10);

insert into wx_t_resource_sql (sqlid, resource_id,creation_time,created_by) values ('com.sys.organization.dao.OrganizationVoMapper.queryPartnerForCtrl-distributor-inventory',(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='库存管理' and m.module_name='订单管理'),getdate(),1)
insert into wx_t_resource_sql (sqlid, resource_id,creation_time,created_by) values ('com.sys.organization.dao.OrganizationVoMapper.queryPartnerForCtrl-distributor-store',(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='仓库管理' and m.module_name='订单管理'),getdate(),1)

INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'synDistributorInStock', N'WorkGroup', N'1', N'0 0 12 * * ?', N'每天中午12点执行', NULL, N'1', N'partnerOrderQuartz', N'synInStockBySapOrder', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '同步经销商SAP发货单入库')
