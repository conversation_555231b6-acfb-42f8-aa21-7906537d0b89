package com.chevron.material.dto;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 物流信息数据对象详情
 * <AUTHOR>
 * @date 2017年8月9日
 */
public class SMCUpdateLogisticsDetailDto implements Serializable{

	private static final long serialVersionUID = 4827465515013522962L;

	/**
	 * 物流/快递公司名称
	 */
	private String com;
	/**
	 * 物流/快递单号
	 */
	private String nu;
	/**
	 * 每条跟踪信息的时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date time;
	/**
	 * 每条跟综信息的描述
	 */
	private String context;
	/**
	 * 快递单当前的状态
	 */
	private String state;
	/**
	 * 备用
	 */
	private String comments;
	/**
	 * 消息
	 */
	private String message;

	public String getCom() {
		return com;
	}
	public void setCom(String com) {
		this.com = com;
	}
	public String getNu() {
		return nu;
	}
	public void setNu(String nu) {
		this.nu = nu;
	}
	public Date getTime() {
		return time;
	}
	public void setTime(Date time) {
		this.time = time;
	}
	public String getContext() {
		return context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}

}
