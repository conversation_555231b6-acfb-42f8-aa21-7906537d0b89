1. 基金类型
{"id":2,"jsonrpc":"2.0","method":"v2EliteFundFormService.getFundTypes","params":[]}
{"jsonrpc":"2.0","id":2,"result":{"code":"success","fundTypes":[{"value":"consumer_mkt_fund","text":"乘用车市场营销基金"},{"value":"commerical_mkt_fund","text":"商用油市场营销基金"}]}}

2. 申报项目
{"id":2,"jsonrpc":"2.0","method":"v2EliteFundFormService.getApplyProjects","params":["consumer_mkt_fund"]}
{"jsonrpc":"2.0","id":2,"result":{"code":"success","applyProjects":[{"value":"buy_car","text":"购置车辆"},{"value":"office_equipment","text":"办公设备"},{"value":"stock_hire","text":"库存租赁"},{"value":"other_support","text":"其他雪佛龙支持的可持续的运营费用"},{"value":"training_meeting","text":"人员培训参加雪佛龙或者商会会议的差旅费"},{"value":"promotion","text":"当地促销"},{"value":"customer_interact","text":"客户互动"},{"value":"signature","text":"门头广告牌"},{"value":"facility","text":"小型设备"}]}}

3. 经销商费用
{"id":2,"jsonrpc":"2.0","method":"v2EliteFundFormService.getApplyDistributorInfo","params":[null, null]}
参数：
	distributorId: 默认当前登录用户，
	applyYear：默认当前申请年份
返回：	
{
	"jsonrpc": "2.0",
	"id": 2,
	"result": {
		"distributorName": "唐山海田润滑油销售有限公司",
		"code": "success",
		"distributorId": 582,
		"commerical_mkt_fund": {
			"earn_ivi_fund": 1E+3,
			"use_fund_amount": 5E+2,
			"fund_type": "commerical_mkt_fund",
			"use_year_amount": 0,
			"earn_annual_amount": 0,
			"invoice_amount": 1E+3,
			"use_ivi_amount": 5E+2,
			"earn_fund_amount": 5E+3
		},
		"commerical_spark": {
			"earn_ivi_fund": 0,
			"use_fund_amount": null,
			"fund_type": "commerical_spark",
			"use_year_amount": null,
			"earn_annual_amount": 0,
			"invoice_amount": null,
			"use_ivi_amount": null,
			"earn_fund_amount": 0
		},
		"consumer_mkt_fund": {
			"earn_ivi_fund": 1E+3,
			"use_fund_amount": 5E+2,
			"fund_type": "consumer_mkt_fund",
			"use_year_amount": 0,
			"earn_annual_amount": 0,
			"invoice_amount": 1E+3,
			"use_ivi_amount": 5E+2,
			"earn_fund_amount": 3E+3
		}
	}
}

4. 抽查列表 POST
/v2elitefundform/checkdata.do?limit=10&start=0&field=executeTime&direction=desc&executor=131746
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].ckeckRejectFlag：抽查驳回权限。1-有权限，0-无权限
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].workflowInstance.latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）
	
5. 抽查驳回接口 RPC
v2EliteFundFormService.rejectByCheck(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数
	form:表单对象。只需包含表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

	
6. BS leader 提交汇总审核 RPC
v2EliteCollectFormService.submitLevel1Collect(Long executor)
参数：executor：被代理人用户ID

7. 红十字发票经销商 POST
/v2elitefundform/currentapplieddistributor.do
参数： 
	executor:被代理人
返回:
	resultLst: V2EliteFundForm集合（distributorId：经销商ID；distributorName：经销商名称；channelWeight：位与运算。1-Consumer下载，2-Commercial下载）

8. 获取可用剩余发票信息 RPC
v2EliteFundFormService.queryUnusedInvoice(Long distributorId, String fundType, String applyProject)
返回：
	resultLst: V2EliteFundForm id,unusedInvoice,unusedInvoiceInfo,formVersion
	
9. 导出已办列表 POST
/v2elitefundform/exportdonedata.do
参数：已办列表参数

	
10. 导出全部列表 POST
/v2elitefundform/exportalldata.do
参数：全部列表参数

11. 获取可汇总数量接口 RPC
v2EliteFundFormService.getCollectableCount()
返回
	data: 可汇总数量
	
页面参数：
executor: 被代理人用户ID

1.获取列表页面权限 RPC
operationPermissionService.getOperationPermissionByUser(Long userId, String moduleCode)
参数：
	userId: 被代理人用户ID/null
	moduleCode：EliteFundV2.apply
返回：
	weight：1-提交申请权限。页面中包含草稿选项卡、创建申请按钮
			2-BS区域Excel汇总导出
			4-下载发票通知文件

2. 获取申请步骤信息（包含申请页面操作权限和字段权限）RPC
v2EliteFundFormService.getRequestStep()
返回：
	data：WorkflowStep

3. 临时保存表单 RPC
v2EliteFundFormService.save(V2EliteFundForm record, Long executor)

4. 提交/通过 RPC
v2EliteFundFormService.accept(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数：
	form:V2EliteFundForm。非可编辑页面传{id:xxx}只需传ID
	remark:通过备注。可编辑页面没有备注传null
	executor:被代理人
	versionNo：流程版本号。申请页面传null。其他form.workflowInstance.versionNo
	
5. 获取表单明细 RPC
v2EliteFundFormService.detail(Long id, String stepCode, Long executor)
参数：
	id: 表单ID
	stepCode: 流程当前步骤编码
	executor：被代理人用户ID
返回：
	currentStep：WorkflowStep
	form: V2EliteFundForm
	form.workflowInstance.versionNo：版本号
	form.workflowInstance.acceptFlag：通过按钮标记。1-有权限，0-无权限
	form.workflowInstance.rejectFlag：驳回按钮标记。1-有权限，0-无权限
	form.workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	form.workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）


6. 待办列表 POST
/v2elitefundform/tododata.do?limit=10&start=0&field=executeTime&direction=desc&executor=129915
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].workflowInstance.latestStepHistory.actionName 状态。resultLst[0].workflowInstance.latestStepHistory为null时显示草稿
	resultLst[0].workflowInstance.acceptFlag：通过按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.rejectFlag：驳回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）

7. 已办列表 POST
/v2elitefundform/donedata.do?limit=10&start=0&field=executeTime&direction=desc&executor=122995
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）
	
8. 全部列表 POST
/v2elitefundform/alldata.do?limit=10&start=0&field=executeTime&direction=desc
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）

9. 流程办理历史 RPC
v2EliteFundFormService.getWorkflowStepHistory(String id)
参数：
	id：表单ID
返回：
	data：List<WorkflowStepHistory>
	data[0].workflowStep.stepName：步骤名称
	data[0].executorName：执行人姓名
	data[0].executor：执行人ID
	data[0].actualExecutorName：代办人姓名（actualExecutor不为空且不等于executor时有效）
	data[0].actualExecutor：代办人ID（actualExecutor不为空且不等于executor时有效）
	data[0].approveStatusText：办理状态
	data[0].executeTime：执行时间
	data[0].approveRemark：审批备注

10. 流程图 RPC
v2EliteFundFormService.getWorkflowStepInstances(String id)
参数：
	id：表单ID
返回：
	data：List<WorkflowStepInstance>
	data[0].workflowStep.stepName：步骤名称
	data[0].worInsExeList：List<WorInsExeList> 办理人列表
	data[0].worInsExeList[0].executor：执行人ID
	data[0].worInsExeList[0].executorName：执行人姓名
	data[0].worInsExeList[0].actualExecutorName：代办人姓名（actualExecutor不为空且不等于executor时有效）
	data[0].worInsExeList[0].actualExecutor：代办人ID（actualExecutor不为空且不等于executor时有效）

11. 驳回 RPC
v2EliteFundFormService.reject(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数：
	form:表单对象。只需包含表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

12. 撤回 RPC
v2EliteFundFormService.recall(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数：
	id:表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

13. 删除草稿
v2EliteFundFormService.delete(List<Long> ids)
参数：
	ids:要删除表单ID集合
		
唐山海田润滑油销售有限公司: 13931559111 duhaitao 
广州诺一贸易有限公司: huxiaohui LKWX WJAS
临汾市尧都区东盛达商贸有限公司: 18235752666,  ZYON LIZN
QIOY YUGE AJAR