/*==============================================================*/
/* Module: OIL_SAMPLE_APPLY                                     */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_oil_sample_apply                                 */
/*==============================================================*/
create table wx_t_oil_sample_apply (
	id                               bigint                            not null identity,
	applicant                        nvarchar(64)                                   null,
	applicant_date                   datetime                                       null,
	department                       nvarchar(64)                                   null,
	warehouse                        nvarchar(64)                                   null,
	glNo                             nvarchar(64)                                   null,
	customerId                       bigint                                         null,
	type                             nvarchar(64)                                   null,
	responsible_csr                  nvarchar(64)                                   null,
	ship_to_code                     nvarchar(64)                                   null,
	delivery_dress                   nvarchar(64)                                   null,
	applay_oil_reason                nvarchar(64)                                   null,
	customer_sap_code                nvarchar(64)                                   null,
	cost_center                      nvarchar(64)                                   null,
	request_no                       nvarchar(64)                                   null,
	request_comment                  nvarchar(1024)                                 null,
	form_status                      int                                            null,
	del_flag                         nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	trial_status                     int                                            null,
	abm_submit_time                  datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_oil_sample_product                               */
/*==============================================================*/
create table wx_t_oil_sample_product (
	id                               bigint                            not null identity,
	apply_id                         bigint                                     not null,
	pack                             decimal(20,6)                                  null,
	pack_count                       nvarchar(64)                                   null,
	product_name                     nvarchar(64)                                   null,
	product_sku                      nvarchar(64)                                   null,
	product_price                    nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	del_flag                         nvarchar(64)                                   null,
	bottle_number                    int                                            null,
	remark                           nvarchar(1000)                                 null,
	primary key (id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_oil_sam_pro_oil_sam_apply        */
/*==============================================================*/
CREATE INDEX ix_oil_sam_pro_oil_sam_apply ON wx_t_oil_sample_product (apply_id);
