package com.chevron.task.model;

import java.util.Date;

public class WxTaskAttach {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.att_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long attId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.source_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long sourceId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.file_name
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private String fileName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.storage_name
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private String storageName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.file_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private String fileType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.source_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private String sourceType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.file_size
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long fileSize;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.upload_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long uploadUser;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.att_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private String attType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.zt
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private String zt;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.xz_sj
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Date xzSj;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.xz_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long xzUser;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.xg_sj
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Date xgSj;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.xg_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long xgUser;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column wx_task_attach.tenant_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	private Long tenantId;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.att_id
	 * @return  the value of wx_task_attach.att_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getAttId() {
		return attId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.att_id
	 * @param attId  the value for wx_task_attach.att_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setAttId(Long attId) {
		this.attId = attId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.source_id
	 * @return  the value of wx_task_attach.source_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getSourceId() {
		return sourceId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.source_id
	 * @param sourceId  the value for wx_task_attach.source_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.file_name
	 * @return  the value of wx_task_attach.file_name
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public String getFileName() {
		return fileName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.file_name
	 * @param fileName  the value for wx_task_attach.file_name
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.storage_name
	 * @return  the value of wx_task_attach.storage_name
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public String getStorageName() {
		return storageName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.storage_name
	 * @param storageName  the value for wx_task_attach.storage_name
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setStorageName(String storageName) {
		this.storageName = storageName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.file_type
	 * @return  the value of wx_task_attach.file_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public String getFileType() {
		return fileType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.file_type
	 * @param fileType  the value for wx_task_attach.file_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.source_type
	 * @return  the value of wx_task_attach.source_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public String getSourceType() {
		return sourceType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.source_type
	 * @param sourceType  the value for wx_task_attach.source_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.file_size
	 * @return  the value of wx_task_attach.file_size
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getFileSize() {
		return fileSize;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.file_size
	 * @param fileSize  the value for wx_task_attach.file_size
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setFileSize(Long fileSize) {
		this.fileSize = fileSize;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.upload_user
	 * @return  the value of wx_task_attach.upload_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getUploadUser() {
		return uploadUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.upload_user
	 * @param uploadUser  the value for wx_task_attach.upload_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setUploadUser(Long uploadUser) {
		this.uploadUser = uploadUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.att_type
	 * @return  the value of wx_task_attach.att_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public String getAttType() {
		return attType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.att_type
	 * @param attType  the value for wx_task_attach.att_type
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setAttType(String attType) {
		this.attType = attType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.zt
	 * @return  the value of wx_task_attach.zt
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public String getZt() {
		return zt;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.zt
	 * @param zt  the value for wx_task_attach.zt
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setZt(String zt) {
		this.zt = zt;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.xz_sj
	 * @return  the value of wx_task_attach.xz_sj
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Date getXzSj() {
		return xzSj;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.xz_sj
	 * @param xzSj  the value for wx_task_attach.xz_sj
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setXzSj(Date xzSj) {
		this.xzSj = xzSj;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.xz_user
	 * @return  the value of wx_task_attach.xz_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getXzUser() {
		return xzUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.xz_user
	 * @param xzUser  the value for wx_task_attach.xz_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setXzUser(Long xzUser) {
		this.xzUser = xzUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.xg_sj
	 * @return  the value of wx_task_attach.xg_sj
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Date getXgSj() {
		return xgSj;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.xg_sj
	 * @param xgSj  the value for wx_task_attach.xg_sj
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setXgSj(Date xgSj) {
		this.xgSj = xgSj;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.xg_user
	 * @return  the value of wx_task_attach.xg_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getXgUser() {
		return xgUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.xg_user
	 * @param xgUser  the value for wx_task_attach.xg_user
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setXgUser(Long xgUser) {
		this.xgUser = xgUser;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column wx_task_attach.tenant_id
	 * @return  the value of wx_task_attach.tenant_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public Long getTenantId() {
		return tenantId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column wx_task_attach.tenant_id
	 * @param tenantId  the value for wx_task_attach.tenant_id
	 * @mbggenerated  Sun May 03 00:40:46 CST 2015
	 */
	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}
}