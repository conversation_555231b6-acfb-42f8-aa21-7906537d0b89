--delete wx_t_properties where codetype='User.pwdPolicy.TurnOn'
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('User.pwdPolicy.TurnOn', 'Y', '用户密码策略开关开启');

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('User.pwdPolicy', '用户密码策略配置', '用户密码策略配置');
--delete d from wx_t_dic_item d where dic_type_code='User.pwdPolicy'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'effectiveDay', '有效天数', '365', '1',10);
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'minMatchs', '可选规则满足最小条数', '3', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'rules', '策略规则', '[{"type":"regular","config":".{8,}","msg":"密码长度至少为8个字符","funWeight":3},{"type":"optional","config":"3","msg":"密码组合至少需包含下列四种字符其中的三种<br/>（例如：Cvx123456 或 cvx123456%）","funWeight":3,"options":[{"type":"regular","config":"[0-9]+","msg":"阿拉伯数字（0~9）"},{"type":"regular","config":"[A-Z]+","msg":"大写英文字母（A~Z）"},{"type":"regular","config":"[a-z]+","msg":"小写英文字母（a~z）"},{"type":"regular","config":"[^0-9a-zA-Z\u4E00-\u9FA5]+","msg":"非字母和数字的特殊字符（!,@,#,$,%,^,&,等）"}]},{"type":"norepeat","config":12,"msg":"不可重复使用前12次密码","funWeight":2}]', '1',10);
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'minMatchMsg', '不满足最小条数错误提示', '密码必须满足包含大写、小写、数字或符号中的任意三种', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'deadDay', '执行最后期限', '2022-12-31', '1',10);
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'deadDay', '执行最后期限', '2022-12-31', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'expireNotifyDay', '逾期提醒天数', '10', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'expireEmailNotifyDays', '逾期邮件提醒天数', '10,3,1', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'effectiveDay.Chevron_Customer_Service', '客服密码有效天数', '90', '1',10);


insert into wx_t_pwd_change_log (create_user_id,create_time,pwd,user_id,ext_property1,ext_flag)
select 1,getdate(),isnull(u1.password,'-1'),u1.user_id,u1.salt,0 from wx_t_user u1 where u1.reset_flag=0 and u1.status=1

INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'userPwdPolicyTask.markPwdExpired', N'WorkGroup', N'1', N'0 0 0 * * ?', N'每天凌晨0点执行', NULL, N'1', N'userPwdPolicyTask', N'markPwdExpired', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '标记用户密码即将过期')
INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'userPwdPolicyTask.sendPwdTobeExpireEmail', N'WorkGroup', N'1', N'0 0 8 * * ?', N'每天早上8点执行', NULL, N'1', N'userPwdPolicyTask', N'sendPwdTobeExpireEmail', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '发送用户密码即将过期提醒邮件')


insert into wx_t_pwd_change_log (create_user_id,create_time,pwd,user_id,ext_property1,ext_flag)
select 1,dateadd(day, -1,getdate()),isnull(u1.password,'-1'),u1.user_id,u1.salt,0 from wx_t_user u1 where u1.reset_flag is null and u1.status=1
and not exists (select 1 from wx_t_pwd_change_log l where l.user_id=u1.user_id)