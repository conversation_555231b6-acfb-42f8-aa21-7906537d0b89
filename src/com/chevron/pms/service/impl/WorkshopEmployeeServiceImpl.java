package com.chevron.pms.service.impl;

import java.util.*;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sys.auth.model.WxTUser;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.EtoBizService;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.o2oorder.business.O2OBizService;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.WorkshopEmpSummary;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopEmployeeExample;
import com.chevron.pms.model.WorkshopEmployeeParams;
import com.chevron.pms.service.WorkshopEmployeeService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;

@Service
public class WorkshopEmployeeServiceImpl implements WorkshopEmployeeService {

	@Autowired
	private WorkshopEmployeeBizService workshopEmpBizService;
	
	@Autowired
	private WorkshopEmployeeMapper workshopEmployeeMapper;
	
	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;
	
	@Autowired
	private O2OBizService o2oBizService;
	
	@Autowired
	private EtoBizService etoBizService;
	
	private Logger log = Logger.getLogger(WorkshopEmployeeServiceImpl.class);
	
	@Override
	public Map<String, Object> queryWorkshopEmployeeSummaryList(Long partnerId, Long workshopId, Date dateFrom,
			Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			if (partnerId == null){
				partnerId = ContextUtil.getCurUser().getOrgId();
			}
			List<WorkshopEmpSummary> empSummaryList = workshopEmpBizService.queryWorkshopEmpSummaryList(partnerId, workshopId, newDateFrom, newDateTo);
			resultMap.put("code", "success");
			resultMap.put("data", empSummaryList);
		} catch (Exception e) {
			resultMap.put("code", "error");
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryWorkshopEmployeeList(Long workshopId, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			Long partnerId = null;
			if (workshopId == null){
				partnerId = ContextUtil.getCurUser().getOrgId();
			}
			List<WorkshopEmployee> empList = workshopEmpBizService.queryWorkshopEmpList(partnerId, workshopId, null, newDateFrom, newDateTo);
			resultMap.put("data", empList);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryWorkshopEmployeeByWorkshop(Long workshopId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<WorkshopEmployee> empList = workshopEmpBizService.queryWorkshopEmpList(null, workshopId, null, null, null);
			resultMap.put(Constants.RESULT_LST_KEY, empList);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryWorkshopEmployeeList1(Long partnerId,
			String workshopName, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			WxTUser user = ContextUtil.getCurUser();
			if(user.getmUserTypes() != 1){
				partnerId = user.getOrgId();
			}
			if(workshopName != null && !workshopName.trim().equals("")){
				workshopName = "%" + workshopName + "%";
			}else{
				workshopName = null;
			}
			List<WorkshopEmployee> empList = workshopEmpBizService.queryWorkshopEmpList(partnerId, null, workshopName, newDateFrom, newDateTo);
			resultMap.put("code", "success");
			resultMap.put("data", empList);
		} catch (Exception e) {
			resultMap.put("code", "error");
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> queryWorkshopEmployeeListNew(WorkshopEmployeeParams workshopEmployeeParams)throws Exception {
		 Map<String, Object> resultMap = new HashMap<String,Object>();
		 //0.处理前台数据
		 String pDateFrom = workshopEmployeeParams.getDateStart();
		 if (!StringUtils.isNull(pDateFrom)){
			Date dateFrom1 = DateUtils.stringToDate(pDateFrom, 3);
			workshopEmployeeParams.setDateFrom(dateFrom1);
		 }
			
		 String pDateTo = workshopEmployeeParams.getDateEnd();
		 if (!StringUtils.isNull(pDateTo)){
			Date dateTo1 = DateUtils.stringToDate(pDateTo, 3);
			workshopEmployeeParams.setDateTo(dateTo1);
		 }
		 
		 
		 Date dateFrom = workshopEmployeeParams.getDateFrom();
		 Date dateTo = workshopEmployeeParams.getDateTo();
		 if(null==dateFrom)
		 {
			 dateFrom = DateUtil.getMinimumDate();
			 workshopEmployeeParams.setDateFrom(dateFrom);
			
		 }
		 
		 if(null==dateTo)
		 {
			 dateTo = DateUtil.getMaximumDate();
			 workshopEmployeeParams.setDateTo(dateTo);
			 
		 }
		
		 WxTUser user = ContextUtil.getCurUser();
		 if(user.getmUserTypes() != 1){
			workshopEmployeeParams.setPartnerId(user.getOrgId());
			
		 }
		 String workshopName = workshopEmployeeParams.getWorkshopName();
		 if(workshopName != null && !workshopName.trim().equals("")){
			workshopName = "%" + workshopName + "%";
		 }else{
			workshopName = null;
		 }
		 String name = workshopEmployeeParams.getName();
		 if(name != null && !name.trim().equals("")){
			 name = "%" + name + "%";
		 }else{
			 name = null;
		 }
		 String mobile = workshopEmployeeParams.getMobile();
		 if(mobile != null && !mobile.trim().equals("")){
			 mobile = "%" + mobile + "%";
		 }else{
			 mobile = null;
		 }
		 String creatorName = workshopEmployeeParams.getCreatorName();
		 if(creatorName != null && !creatorName.trim().equals("")){
			 creatorName = "%" + creatorName + "%";
		 }else{
			 creatorName = null;
		 }
		 
		 String queryField = workshopEmployeeParams.getQueryField();
		 if(queryField != null && !queryField.trim().equals("")){
			 queryField = "%" + queryField + "%";
		  }else{
			 queryField = null;
		  }
		 workshopEmployeeParams.setQueryField(queryField);
		 workshopEmployeeParams.setName(name);
		 workshopEmployeeParams.setMobile(mobile);
		 workshopEmployeeParams.setCreatorName(creatorName);
		 workshopEmployeeParams.setWorkshopName(workshopName);
		 resultMap = workshopEmpBizService.queryWorkshopEmpListNew(workshopEmployeeParams);
		 return resultMap;
	}

	@Override
	public JsonResponse saveMechanic(WorkshopEmployee workshopEmployee) {
		JsonResponse resultMap = new JsonResponse();
		WxTUser user = ContextUtil.getCurUser();
		if(workshopEmployee == null){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("保存技师信息不能为空");
			LogUtils.addErrorLog(user.getUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.saveMechanic", 
					"保存技师信息不能为空", null);
			return resultMap;
		}
		log.info("saveMechanic: " + JsonUtil.writeValue(workshopEmployee));
		try {
			String code = workshopEmployee.getCode();
			if(workshopEmployee.getWorkshopId() != null){
				//带门店信息
				WorkshopMaster workShopVo = workshopMasterBizService.getBean(workshopEmployee.getWorkshopId());
				if(workShopVo == null){
					resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
					resultMap.setErrorMsg("技师所在门店不存在");
					LogUtils.addErrorLog(user.getUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.saveMechanic", 
							"技师所在门店不存在", JsonUtil.writeValue(workshopEmployee));
					return resultMap;
				}
				if(workShopVo.getStatus() < 1 || workShopVo.getDeleteFlag() == 1){
					resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
					resultMap.setErrorMsg("技师所在门店不可用");
					LogUtils.addErrorLog(user.getUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.saveMechanic", 
							"技师所在门店不可用", JsonUtil.writeValue(workshopEmployee));
					return resultMap;
				}
				workshopEmployee.setWorkshopName(workShopVo.getWorkShopName());
			}
			workshopEmployee.setVersionNo(1);
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("checkExists", true);
			if(StringUtils.isNotBlank(code)){
				paramMap.put("code", code);
			}else if(workshopEmployee.getEnableFlag() == null){
				//新增技师，设置默认状态
				workshopEmployee.setEnableFlag(1);
			}
			paramMap.put("mobile", workshopEmployee.getMobile());
			List<WorkshopEmployee> list = workshopEmployeeMapper.selectByParams(paramMap);
			boolean update = false;
			boolean mobileChanged = false;
			if(list.size() > 1){
				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resultMap.setErrorMsg("技师编码和手机号不匹配");
				LogUtils.addErrorLog(user.getUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.saveMechanic", 
						"技师编码和手机号不匹配", JsonUtil.writeValue(workshopEmployee));
				return resultMap;
			}else if(!list.isEmpty()){
				WorkshopEmployee existEmployee = list.get(0);
				if(StringUtils.isNotBlank(code) && !code.equals(existEmployee.getCode())){
					resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
					LogUtils.addErrorLog(user.getUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.saveMechanic", 
							"技师编码和手机号不匹配", JsonUtil.writeValue(workshopEmployee));
					return resultMap;
				}
				if(!existEmployee.getMobile().equals(workshopEmployee.getMobile())) {
					mobileChanged = true;
				}
				workshopEmployee.setId(existEmployee.getId());
				workshopEmployee.setUpdator(user.getUserId());
				workshopEmployee.setUpdateTime(DateUtil.getCurrentDate());
				workshopEmployeeMapper.updateByPrimaryKeySelective(workshopEmployee);
				workshopEmployee.setCode(existEmployee.getCode());
				if(existEmployee.getVersionNo() != 1) {
					workshopEmpBizService.awardB2bPointsByRegister(workshopEmployee);
				}
				update = true;
			} else {
				if(StringUtils.isEmpty(code)){
					//注册新技师
					workshopEmployee.setCode(CommonUtil.generateCode("M"));
				}
				if(workshopEmployee.getCreator() == null){
					workshopEmployee.setCreator(user.getUserId());
				}
				workshopEmployee.setCreationTime(DateUtil.getCurrentDate());
				workshopEmployeeMapper.insertSelective(workshopEmployee);
				workshopEmpBizService.awardB2bPointsByRegister(workshopEmployee);
			}
			//同步门店直播技师
			etoBizService.synWorkshopEmployee(workshopEmployee, update, mobileChanged);
			resultMap.setDataResult(workshopEmployee);
			log.info("saveMechanic success.");
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.saveMechanic", JsonUtil.writeValue(workshopEmployee));
		}
		return resultMap;
	}

	@Override
	public JsonResponse isInWorkshop(String mechanicCode, Double longitude,
			Double latitude) {
		JsonResponse resultMap = new JsonResponse();
		log.info("isInWorkshop: " + mechanicCode + "," + longitude + "," + latitude);
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("mechanicCode", mechanicCode);
			paramMap.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
			List<WorkshopMaster> list = workshopMasterBizService.querySimpleByParams(paramMap);
			if(list.isEmpty()){
				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resultMap.setErrorMsg("技师所在门店不存在");
				return resultMap;
			}
			WorkshopMaster workShopVo = list.get(0);
			if(workShopVo.getStatus() < 1){
				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resultMap.setErrorMsg("技师所在门店不可用");
				return resultMap;
			}
			double distance = o2oBizService.getDistanceBetweenLocationAndWorkshop(longitude, latitude, workShopVo);
			if(distance <= 1){
				resultMap.put("validFlag", 1);
			}else{
				resultMap.put("validFlag", 0);
			}
			log.info("isInWorkshop success: " + distance);
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.isInWorkshop", mechanicCode + "," + longitude + "," + latitude);
		}
		return resultMap;
	}

	//同步crm
	@Override
	public JsonResponse synMechanicToScrm(WorkshopEmployee employee) {
		JsonResponse resultMap = new JsonResponse();
		try {
			workshopEmpBizService.synMechanicInfoToScrm(employee, ContextUtil.getCurUser());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.pms.service.impl.WorkshopEmployeeServiceImpl.synMechanicToScrm", null);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getEnterpriseQrcodeUrl(String spId, String salesChannel) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(StringUtils.isBlank(spId)){
				throw new WxPltException("E001", "传入合伙人ID为空");
			}
			Long partnerId = null;
			try {
				partnerId = Long.parseLong(spId);
			} catch (Exception e) {
				throw new WxPltException("E002", "传入合伙人ID格式不对");
			}
			map.put("url", workshopEmployeeMapper.getEnterpriseQrcodeUrl(partnerId, salesChannel, "Chevron"));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, e.getExpCode());
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> saveUpdateWorkShopEmployee(Long id,String storeName,String employeeType,String mobile,Long workshopId){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WorkshopEmployee workshopEmployee = new WorkshopEmployee();
		WxTUser user = ContextUtil.getCurUser();
		workshopEmployee.setName(storeName);
		workshopEmployee.setEmployeeType(employeeType);
		workshopEmployee.setMobile(mobile);
		workshopEmployee.setWorkshopId(workshopId);
		workshopEmployee.setId(id);
		WorkshopEmployee existEmployee= checkEmployeeExist(workshopEmployee);
		boolean update = false;
		try {
			WorkshopMaster workShopVo = workshopMasterBizService.getBean(workshopEmployee.getWorkshopId());
			workshopEmployee.setWorkshopName(workShopVo.getWorkShopName());
			//手机号不存在，并且id为空则为新增
			if (existEmployee == null && workshopEmployee.getId()==null){
				workshopEmployee.setCreationTime(DateUtil.getCurrentDate());
				workshopEmployee.setCreator(ContextUtil.getCurUserId());
				workshopEmployee.setCode(CommonUtil.generateCode("M"));
				workshopEmployee.setEnableFlag(1);
				workshopEmployeeMapper.insertSelective(workshopEmployee);
			}else {
				update = true;
				if(existEmployee!=null && 0==existEmployee.getEnableFlag()) {
					workshopEmployee.setEnableFlag(1);
				}
				if(id==null) {
					workshopEmployee.setId(existEmployee.getId());	
				}
				workshopEmployee.setUpdator(user.getUserId());
				workshopEmployee.setUpdateTime(DateUtil.getCurrentDate());
				workshopEmployeeMapper.updateByPrimaryKeySelective(workshopEmployee);
			}
			try{
				//pp同步门店直播技师(方便运维测试用)
				etoBizService.synWorkshopEmployee(workshopEmployee, update, false);
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			}catch (WxPltException e) {
				if (e.getMessage().contains("尚未查询到用户信息")) {
					etoBizService.synWorkshopEmployee(workshopEmployee, false, false);
				}
			}
		} catch (Exception e) {
			resultMap.put(Constants.ERROR_CODE, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	public WorkshopEmployee checkEmployeeExist(WorkshopEmployee workshopEmployee) {
		WorkshopEmployeeExample example = new WorkshopEmployeeExample();
		example.createCriteria().andMobileEqualTo(workshopEmployee.getMobile());
		List<WorkshopEmployee> employeeList = workshopEmployeeMapper.selectByExample(example);
		if (employeeList != null && employeeList.size() > 0){
			return employeeList.get(0);
		}
		return null;
	}
	
	public Map<String, Object> deleteWorkShopEmployee(Long id){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WorkshopEmployee workshopEmployee = new WorkshopEmployee();
		WxTUser user = ContextUtil.getCurUser();
		workshopEmployee.setUpdator(user.getUserId());
		workshopEmployee.setUpdateTime(DateUtil.getCurrentDate());
		workshopEmployee.setEnableFlag(0);
		workshopEmployee.setId(id);
		try {
			int data = workshopEmployeeMapper.updateByPrimaryKeySelective(workshopEmployee);
			if(data>0) {
			   resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			}else {
				resultMap.put(Constants.ERROR_CODE, "删除失败");	
			}
		} catch (Exception e) {
			resultMap.put(Constants.ERROR_CODE, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	
	public Map<String,Object> updateWorkShopEmployeeById(List<WorkshopEmployee> employee) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if(!employee.isEmpty()) {
			  for(WorkshopEmployee employee2:employee) {
					workshopEmployeeMapper.updateByPrimaryKeySelective(employee2);
			  }
			}
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put(Constants.ERROR_CODE, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	/*
	*  批量同步 变更会员服务导购或门店
	* */
	public Map<String,Object> synWorkShopEmployees(){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		//门店有效的所有门店员工
		List<WorkshopEmployee> employeeList = workshopEmployeeMapper.selectAllEmployees();
		try {
			if(employeeList.size()>100){
				//子集合的长度
				int toIndex = 100;
				List<List<WorkshopEmployee>> lists = new ArrayList<List<WorkshopEmployee>>();
				for(int i=0;i<employeeList.size();i+=toIndex){
					if(i+100 > employeeList.size()){
						toIndex = employeeList.size()-i;
					}
					List<WorkshopEmployee> newList = employeeList.subList(i,i+toIndex);
					lists.add(newList);
				}
				for (List<WorkshopEmployee> list:lists) {
					etoBizService.synEtoWorkShopEmployees(list);
				}
			}else {
				etoBizService.synEtoWorkShopEmployees(employeeList);
			}
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

}
