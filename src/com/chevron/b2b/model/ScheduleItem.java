package com.chevron.b2b.model;

/**
 * 计划事项-wx_t_schedule_item
 * <AUTHOR>
 * @version 1.0 2018-12-18 16:56
 */
public class ScheduleItem {

	/**  ID  **/
    private Long id;

	/**  输入控件ID  **/
    private Long inputCtrlId;

	/**  计划ID  **/
    private Long scheduleId;

	/**  标题  **/
    private String caption;

	/**  输入控件配置  **/
    private String inputCtrlConfig;

	/**  是否必填(1-必填，0-非必填)  **/
    private Integer requiredFlag;

	/**  顺序号  **/
    private Double orderNumb;
    
    private String inputCtrlName;

	/**  控件高度  **/
    private Integer ctrlHeight;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInputCtrlId() {
        return inputCtrlId;
    }

    public void setInputCtrlId(Long inputCtrlId) {
        this.inputCtrlId = inputCtrlId;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public String getInputCtrlConfig() {
        return inputCtrlConfig;
    }

    public void setInputCtrlConfig(String inputCtrlConfig) {
        this.inputCtrlConfig = inputCtrlConfig;
    }

    public Integer getRequiredFlag() {
        return requiredFlag;
    }

    public void setRequiredFlag(Integer requiredFlag) {
        this.requiredFlag = requiredFlag;
    }

    public Double getOrderNumb() {
        return orderNumb;
    }

    public void setOrderNumb(Double orderNumb) {
        this.orderNumb = orderNumb;
    }

	public String getInputCtrlName() {
		return inputCtrlName;
	}

	public void setInputCtrlName(String inputCtrlName) {
		this.inputCtrlName = inputCtrlName;
	}

	public Integer getCtrlHeight() {
		return ctrlHeight;
	}

	public void setCtrlHeight(Integer ctrlHeight) {
		this.ctrlHeight = ctrlHeight;
	}
}
