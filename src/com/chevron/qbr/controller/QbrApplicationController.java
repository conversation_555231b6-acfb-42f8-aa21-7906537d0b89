package com.chevron.qbr.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.chevron.qbr.dao.QbrBasicMapper;
import com.chevron.qbr.dao.QbrCdmPerformanceAdjustMapper;
import com.chevron.qbr.dao.QbrPartnerRemarkRecordMapper;
import com.chevron.qbr.dao.WXTQbrAttListVoMapper;
import com.chevron.qbr.model.QbrCdmPerformanceAdjust;
import com.chevron.qbr.model.QbrPartnerRemarkRecord;
import com.chevron.qbr.model.QbrPartnerRemarkRecordExample;
import com.chevron.qbr.model.WXTQbrAttListVo;
import com.chevron.qbr.model.dms.QbrDmsPerformanceDTO;
import com.chevron.qbr.model.dms.QbrDmsPerformanceQueryParam;
import com.chevron.qbr.service.DmsSellInDataAnalysis;
import com.chevron.qbr.service.QbrDmsPerformanceService;
import com.chevron.report.model.DMSDistributorPerformanceParam;
import com.chevron.sop.model.SopOtherSupportDetail;
import com.chevron.sop.model.SopSupportSummary;
import com.chevron.sop.service.SopService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.file.model.WxAttFile;
import com.sys.file.service.FileManagerService;
import com.sys.file.web.FileManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/qbr")
public class QbrApplicationController {
    public static final Logger log = LoggerFactory.getLogger(QbrApplicationController.class);

    @Autowired
    FileManager fileManager;
	
	@Autowired
	DmsSellInDataAnalysis dmsSellInDataAnalysis;

    @Autowired
    WXTQbrAttListVoMapper wxtQbrAttListVoMapper;

    @Autowired
    private QbrDmsPerformanceService qbrDmsPerformanceService;

    @Autowired
	private OperationPermissionBizService operationPermissionBizService;
    
    @Autowired
    private QbrBasicMapper qbrBasicMapper;

    @Autowired
    private QbrPartnerRemarkRecordMapper qbrPartnerRemarkRecordMapper;

    @Autowired
    private FileManagerService fileManagerService;

    @Autowired
    private SopService sopService;

    @Autowired
    private QbrCdmPerformanceAdjustMapper qbrCdmPerformanceAdjustMapper;

    
    /**
     * 上传附件，app使用的，使用token
     *
     * @param myFile
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadForAppAttachmentFile.do", method = RequestMethod.POST)
    public Map<String, Object> uploadForAppAttachmentFile(@RequestParam(value = "myFile", required = false) MultipartFile myFile,
                                                          @RequestParam(value = "partnerId", required = false) Long partnerId,
                                                          @RequestParam(value = "sourceType", defaultValue = WxAttFile.SourceType_QBR, required = false) String sourceType,
                                                          HttpServletRequest request) {
        return uploadForAttachmentFile(myFile, partnerId, sourceType, request);
    }

    /**
     * @param myFile
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadAttachmentFile.do", method = RequestMethod.POST)
    @Transactional
    public Map<String, Object> uploadForAttachmentFile(@RequestParam(value = "myFile", required = false) MultipartFile myFile,
                                                       @RequestParam(value = "partnerId", required = false) Long partnerId,
                                                       @RequestParam(value = "sourceType", defaultValue = WxAttFile.SourceType_QBR, required = false) String sourceType,
                                                       HttpServletRequest request) {
        JsonResponse jsonResponse = new JsonResponse();
        jsonResponse.put("jsonrpc", "2.0");
        jsonResponse.put("id", 1);
        // 先校验token
        // 然后上传到SourceType_B2B的下面
        if (myFile == null) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
        } else {
            boolean needRecordPartner = WxAttFile.SourceType_QBR_OTHER.equals(sourceType);
            if (needRecordPartner && partnerId == null) {
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                return jsonResponse;
            }
            try {
                MultipartFile[] myFiles = new MultipartFile[]{
                        myFile
                };
                Map<String, Object> resMap = fileManager.handleUploadRequest(sourceType, myFiles, request);
                if (resMap != null && "success".equals(resMap.get("code"))) {
                    List<WxAttFile> attFileList = (List<WxAttFile>) resMap.get("attachmentFileList");
                    WxAttFile wxAttFile = attFileList.get(0);
                    if (needRecordPartner) {
                        WXTQbrAttListVo record = new WXTQbrAttListVo(wxAttFile, partnerId);
                        wxtQbrAttListVoMapper.insertSelective(record);
                    }
//					jsonResponse.setListResult(attFileList);
                    jsonResponse.setDataResult(wxAttFile);
                } else {
                    jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
                    log.error("QbrApplicationController uploadForAppAttachmentFile", resMap);
                }
            } catch (Exception e) {
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
                log.error("QbrApplicationController uploadForAppAttachmentFile", e);
            }
        }

        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/dms/qbrDmsPerformanceDatasByCategory.do")
    public JsonResponse getQbrDmsPerformanceDatasByCategory(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<QbrDmsPerformanceDTO> list = qbrDmsPerformanceService.getQbrDmsPerformanceDatasByCategory(param);
            List<QbrDmsPerformanceDTO> month = new ArrayList<QbrDmsPerformanceDTO>();
            List<QbrDmsPerformanceDTO> premonth = new ArrayList<QbrDmsPerformanceDTO>();
            for (QbrDmsPerformanceDTO item : list) {
                if (Boolean.TRUE.equals(item.isPreMonthCutOff())) {
                    premonth.add(item);
                } else {
                    month.add(item);
                }
            }
            jsonResponse.setListResult(month);
            jsonResponse.put("lastMonth", premonth.size()==0?month:premonth);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/dms/qbrDmsPerformanceDatasByWorkShopName.do")
    public JsonResponse getQbrDmsPerformanceDatasByWorkShopName(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<QbrDmsPerformanceDTO> list = qbrDmsPerformanceService.getQbrDmsPerformanceDatasByWorkShopName(param);
            List<QbrDmsPerformanceDTO> month = new ArrayList<QbrDmsPerformanceDTO>();
            List<QbrDmsPerformanceDTO> premonth = new ArrayList<QbrDmsPerformanceDTO>();
            for (QbrDmsPerformanceDTO item : list) {
                if (Boolean.TRUE.equals(item.isPreMonthCutOff())) {
                    premonth.add(item);
                } else {
                    month.add(item);
                }
            }
            jsonResponse.setListResult(month);
            jsonResponse.put("lastMonth", premonth.size()==0?month:premonth);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/dms/qbrDmsSelectDatas.do")
    public JsonResponse getQbrDmsSelectDatas(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            jsonResponse.setDataResult(qbrDmsPerformanceService.getQbrDmsSelectDatas(param));
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/dms/productsSaleInfo.do")
    public JsonResponse getProductsSaleInfo(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<QbrDmsPerformanceDTO> productsSaleInfo = qbrDmsPerformanceService.getProductsSaleInfo(param);
            List<QbrDmsPerformanceDTO> month = new ArrayList<QbrDmsPerformanceDTO>();
            List<QbrDmsPerformanceDTO> premonth = new ArrayList<QbrDmsPerformanceDTO>();
            for (QbrDmsPerformanceDTO item : productsSaleInfo) {
                if (Boolean.TRUE.equals(item.isPreMonthCutOff())) {
                    premonth.add(item);
                } else {
                    month.add(item);
                }
            }
            jsonResponse.setListResult(month);
            jsonResponse.put("lastMonth", premonth.size()==0?month:premonth);

        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/dms/listOfUnmarketableProducts.do")
    public JsonResponse listOfUnmarketableProducts(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            jsonResponse.setListResult(qbrDmsPerformanceService.listOfUnmarketableProducts(param));
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/dms/inventoryWarning.do")
    public JsonResponse inventoryWarning(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            jsonResponse.setListResult(qbrDmsPerformanceService.inventoryWarning(param));
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }
	
	@ResponseBody
	@RequestMapping(value = "/dms/getDateAnalyList.do", method = RequestMethod.POST)
	public JsonResponse getDateAnalyList(DMSDistributorPerformanceParam param) {
	/*	WxTUser curUser = ContextUtil.getCurUser();
		int includeChannels = curUser.getIncludeChannels();*/
		setCustomerCategory(param);
		return dmsSellInDataAnalysis.getDateAnalyList(param);
	}


	
	@ResponseBody
	@RequestMapping(value = "/dms/getDateAnalyListThroughDesc.do", method = RequestMethod.POST)
	public JsonResponse getDateAnalyListThroughDesc(DMSDistributorPerformanceParam param) {
		/*WxTUser curUser = ContextUtil.getCurUser();
		int includeChannels = curUser.getIncludeChannels();*/
		setCustomerCategory(param);
		return dmsSellInDataAnalysis.getDateAnalyListThroughDesc(param);
	}
	
	@ResponseBody
	@RequestMapping(value = "/dms/getDateAnalyListSellInDesc.do", method = RequestMethod.POST)
	public JsonResponse getDateAnalyListSellInDesc(DMSDistributorPerformanceParam param) {
	/*	WxTUser curUser = ContextUtil.getCurUser();
		int includeChannels = curUser.getIncludeChannels();*/
		setCustomerCategory(param);
		return dmsSellInDataAnalysis.getDateAnalyListSellInDesc(param);
	}
	
	@ResponseBody
	@RequestMapping(value = "/dms/getDateAnalyListSellAllDes.do", method = RequestMethod.POST)
	public JsonResponse getDateAnalyListSellAllDes(DMSDistributorPerformanceParam param) {
	/*	WxTUser curUser = ContextUtil.getCurUser();
		int includeChannels = curUser.getIncludeChannels();*/
		setCustomerCategory(param);
		return dmsSellInDataAnalysis.getDateAnalyListSellAllDes(param);
	}
	
	private void setCustomerCategory(DMSDistributorPerformanceParam param) {
		try {
		
			WxTUser curUser = ContextUtil.getCurUser();
			int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "QBR_DMS_INFO");
			if((permissionWeight & 2) >0) {
				//查询全量销售关系
				List<String> produceChannelList = qbrBasicMapper.getProduceChannelByFlsr(param.getDistributorId(), curUser.getCai());
				if(!CollectionUtil.isEmpty(produceChannelList) && produceChannelList.size() < 2 ) {
					//param.setProductChannel("Consumer");
					param.setProductChannel(produceChannelList.get(0));
				}
				
			}
		} catch (WxPltException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

    /**
     * 编辑qbr备注信息
     */
    @ResponseBody
    @RequestMapping(value = "/editQbrTextRemark.do")
    public JsonResponse editQbrTextRemark(@RequestBody QbrPartnerRemarkRecord qbrPartnerRemarkRecord) {
        JsonResponse jsonResponse = new JsonResponse();
        Long id = qbrPartnerRemarkRecord.getId();
        String year = qbrPartnerRemarkRecord.getYear();
        String quarter = qbrPartnerRemarkRecord.getQuarter();
        String partnerId = qbrPartnerRemarkRecord.getPartner();
        WxTUser curUser = ContextUtil.getCurUser();
        //如果是一个季度第一次记录需要检查
        if (id == null) {
            if (StringUtils.isBlank(year) || StringUtils.isBlank(quarter) || StringUtils.isBlank(partnerId)) {
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                jsonResponse.setErrorMsg("参数不全");
                return jsonResponse;
            }
            //检查是否已经存在这个季度的数据
            QbrPartnerRemarkRecordExample example = new QbrPartnerRemarkRecordExample();
            example.createCriteria().andPartnerEqualTo(partnerId).andYearEqualTo(year).andQuarterEqualTo(quarter);
            List<QbrPartnerRemarkRecord> records = qbrPartnerRemarkRecordMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(records)) {
                id = records.get(0).getId();
                QbrPartnerRemarkRecord record = new QbrPartnerRemarkRecord();
                record.setId(id);
                record.setRemark(qbrPartnerRemarkRecord.getRemark());
                record.setUpdateUserId(curUser.getUserId());
                record.setUpdateTime(new Date());
                qbrPartnerRemarkRecordMapper.updateByPrimaryKeySelective(record);
            } else {
                QbrPartnerRemarkRecord record = new QbrPartnerRemarkRecord();
                record.setYear(year);
                record.setQuarter(quarter);
                record.setPartner(partnerId);
                record.setRemark(qbrPartnerRemarkRecord.getRemark());
                record.setCreateTime(new Date());
                record.setUpdateTime(new Date());
                record.setCreateUserId(curUser.getUserId());
                record.setUpdateUserId(curUser.getUserId());
                qbrPartnerRemarkRecordMapper.insertSelective(record);
                id = record.getId();
            }
        } else if (qbrPartnerRemarkRecord.getRemark() != null) {
            QbrPartnerRemarkRecord record = new QbrPartnerRemarkRecord();
            record.setId(id);
            record.setRemark(qbrPartnerRemarkRecord.getRemark());
            record.setUpdateUserId(curUser.getUserId());
            record.setUpdateTime(new Date());
            qbrPartnerRemarkRecordMapper.updateByPrimaryKeySelective(record);
        }
        qbrPartnerRemarkRecord.setId(id);
        jsonResponse.setDataResult(qbrPartnerRemarkRecord);
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/queryQbrTextRemark.do")
    public JsonResponse queryQbrTextRemark(@RequestParam(value = "year", required = false) String year,
                                           @RequestParam(value = "quarter", required = false) String quarter,
                                           @RequestParam(value = "partnerId", required = false) String partnerId) {
        JsonResponse jsonResponse = new JsonResponse();
        QbrPartnerRemarkRecordExample example = new QbrPartnerRemarkRecordExample();
        QbrPartnerRemarkRecordExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(year)) {
            criteria.andYearEqualTo(year);
        }
        if (StringUtils.isNotBlank(quarter)) {
            criteria.andQuarterEqualTo(quarter);
        }
        if (StringUtils.isNotBlank(partnerId)) {
            criteria.andPartnerEqualTo(partnerId);
        }
        try {
            List<QbrPartnerRemarkRecord> records = qbrPartnerRemarkRecordMapper.selectByExample(example);
            if (CollectionUtil.isEmpty(records)) {
                return jsonResponse;
            }
            for (QbrPartnerRemarkRecord record : records) {
                Map<String, Object> attsBySource = fileManagerService.findAttsBySource(record.getId().toString(), WxAttFile.SOURCETYPE_QBR_REMARK);
                if (attsBySource != null && attsBySource.get("data") != null) {
                    record.setFiles((List<WxAttFile>) attsBySource.get("data"));
                }
            }
            jsonResponse.setListResult(records);
        } catch (Exception e) {
            jsonResponse.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            jsonResponse.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * qbr查询SOP实际支持（含税）
     */
    @ResponseBody
    @RequestMapping(value = "/qbrSopActualSupport.do")
    public JsonResponse qbrSopActualSupport(@RequestParam(value = "year") String year,
                                            @RequestParam(value = "channel", required = false, defaultValue = "Consumer") String channel,
    @RequestParam(value = "partnerId") String partnerId) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<SopSupportSummary> sopSupportSummaries = sopService.qbrSopActualSupport(year, channel, partnerId);
            jsonResponse.setListResult(sopSupportSummaries);
        } catch (Exception e) {
            jsonResponse.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            jsonResponse.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * qbr查询SOP实际支持（含税）
     */
    @ResponseBody
    @RequestMapping(value = "/qbrOtherSopSupport.do")
    public JsonResponse qbrOtherSopSupport(@RequestParam(value = "year") String year,
                                            @RequestParam(value = "channel") String channel,
                                           @RequestParam(value = "partnerId") String partnerId) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<SopOtherSupportDetail> list = sopService.qbrOtherSopSupport(year, channel, partnerId);
            jsonResponse.setListResult(list);
        } catch (Exception e) {
            jsonResponse.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            jsonResponse.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * qbr查询SOP实际支持（含税）
     */
    @ResponseBody
    @RequestMapping(value = "/qbrCmdPerformanceAdjust.do")
    public ResponseMap qbrCmdPerformanceAdjust(@RequestParam(value = "year") String year,
                                               @RequestParam(value = "partnerId") Long partnerId,
                                               @RequestParam(value = "productChannel", required=false) String productChannel) {
        ResponseMap jsonResponse = new ResponseMap();
        try {
        	if(StringUtils.isBlank(productChannel)) {
        		productChannel = "Consumer";
        	}
            List<QbrCdmPerformanceAdjust> qbrCdmPerformanceAdjusts = qbrCdmPerformanceAdjustMapper.qbrCmdPerformanceAdjust(year, partnerId, productChannel);
            jsonResponse.setListResult(qbrCdmPerformanceAdjusts);
        } catch (Exception e) {
            jsonResponse.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            jsonResponse.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * app查询销量排名
     */
    @ResponseBody
    @RequestMapping("/dms/getProductsSalesTop.do")
    public JsonResponse getProductsSalesTop(@RequestBody QbrDmsPerformanceQueryParam param) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<QbrDmsPerformanceDTO> productsSaleInfo = qbrDmsPerformanceService.getProductsSalesTop(param);
            jsonResponse.setListResult(productsSaleInfo);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

}
