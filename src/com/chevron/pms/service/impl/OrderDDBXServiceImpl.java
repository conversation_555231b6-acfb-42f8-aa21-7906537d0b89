package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.antlr.runtime.EarlyExitException;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;

import com.chevron.exportdata.Export;
import com.chevron.exportdata.ExportExcel;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.importdata.ImportDataUtil;
import com.chevron.partnerorder.model.SapSellin;
import com.chevron.pms.dao.OrderLineVoMapper;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.dao.PreferentialTypeVoMapper;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.model.BaseParams;
import com.chevron.pms.model.ManualXBOrder;
import com.chevron.pms.model.OilInjectionVo;
import com.chevron.pms.model.OrderDDBXCondition;
import com.chevron.pms.model.OrderForEmailExport;
import com.chevron.pms.model.OrderForEmailExportNew;
import com.chevron.pms.model.OrderLineVo;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.PreferentialTypeVo;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.service.OrderBizService;
import com.chevron.pms.service.OrderDDBXService;
import com.chevron.thirdorder.model.OrderCommonVo;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtils;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVo;
import com.sys.properties.service.SequenceTypes;
import com.sys.quartz.business.ScanOrderForConfirmService;
/**
 * 
 * @Author: bo.liu  2016-9-23 上午10:39:17 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class OrderDDBXServiceImpl implements OrderDDBXService{
	public final static Logger log = Logger.getLogger(OrderDDBXServiceImpl.class);
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_CURRENT_ORDER_KEY = "currentOrder";
	
	public final static String EXPORT_ALL = "EXPORT_ALL";
	public final static String EXPORT_BATCH = "EXPORT_BATCH";
	public final static String REPLACE_SKU_PART=  "01A";
	public static final String ORDER_LINE_OIL_TYPE = "JY";//机油
	public static final String OIL_COST_4L = ">4L";
	public static final String OIL_COST_1L = "<=4L";
	@Resource
	OrderVoMapper orderMapper;
	
	@Resource
	OrderLineVoMapper orderLineMapper;
	
	@Resource
	private Export exportExcel;
	
	@Resource
	private ScanOrderForConfirmService scanorderService;
	@Resource
	PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	@Resource
	OrganizationVoMapper organizationVoMapper;
	@Resource
	PreferentialTypeVoMapper preferentialTypeVoMapper;
	@Resource
	ProductVoMapper productMapper;
	@Resource
	private OrderBizService orderBizService;

	@Override
	public Map<String,Object> getDDXBOrdersByCondition(
			OrderDDBXCondition orderConditions) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			//0.解析条件
			if(null!=orderConditions)
			{
				if(null!=orderConditions.getOrderEffectiveTime())
				{
				   Date orderEffectiveTime =	DateUtils.stringToDate(orderConditions.getOrderEffectiveTime(), 3);
				   reqMap.put("orderEffectiveTime", orderEffectiveTime);
				}
				reqMap.put("orderConditions", orderConditions);
			}
			
			//1.组装条件
			reqMap.put("orderType", OrderVo.DDXB_ORDER_TYPE);
			reqMap.put("nowdate", new Date());
			reqMap.put("status","3");//已下单
			//2.根据条件查询，默认查询所有为null;
			lstOrders = orderMapper.getDDXBOrdersByCondition(reqMap);
			resultMap.put(RESULT_LST_KEY, lstOrders);
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getDDXBOrdersByConditionNew(
			OrderDDBXCondition orderConditions, int start, int pageSize) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			//0.解析条件
			if(null!=orderConditions)
			{
				if(null!=orderConditions.getOrderEffectiveTime())
				{
				   Date orderEffectiveTime =	DateUtils.stringToDate(orderConditions.getOrderEffectiveTime(), 3);
				   reqMap.put("orderEffectiveTime", orderEffectiveTime);
				}
				reqMap.put("orderConditions", orderConditions);
			}
			
			//1.组装条件
			reqMap.put("orderType", OrderVo.DDXB_ORDER_TYPE);
			reqMap.put("nowdate", new Date());
			reqMap.put("status","3");//已下单
			reqMap.put("start", start);
			reqMap.put("pageSize", pageSize);
			//2.根据条件查询，默认查询所有为null;
			lstOrders = orderMapper.getDDXBOrdersByConditionNew(reqMap);
			int totalRecord = orderMapper.countDDXBOrdersByConditionByPageNew(reqMap);
			resultMap.put(RESULT_LST_KEY, lstOrders);
			resultMap.put("totalRecord", totalRecord);
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	
	//此接口可以与上面的接口合并到一个中，，目前这样处理为了防止后续需求更新的情况（业务复杂的处理）
	@Override
	public Map<String,Object> getDDFWOrdersByCondition(
			OrderDDBXCondition orderConditions) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			//0.解析条件
			String orderSource = null;
			if(null!=orderConditions)
			{
				if(null!=orderConditions.getSendProductTime())
				{
				   Date deliveryTime =	DateUtils.stringToDate(orderConditions.getSendProductTime(), 3);
				   reqMap.put("deliveryTime", deliveryTime);
				}
				if(orderConditions.getSendStatus().equals(""))
				{
					orderConditions.setSendStatus(null);
				}
				reqMap.put("orderConditions", orderConditions);
				orderSource = orderConditions.getOrderSource();
				if(null!=orderSource)
				{
					if(orderSource.trim().equals(""))
					{
						orderSource = null;
					}
				}
				
			}
			
			//1.组装条件
			reqMap.put("orderType", OrderVo.DDFW_ORDER_TYPE);
			reqMap.put("nowdate", new Date());
			
			//add by bo.liu 1026
			reqMap.put("orderSource", orderSource);
			lstOrders = orderMapper.getFWOrdersByCondition(reqMap);
			resultMap.put(RESULT_LST_KEY, lstOrders);
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}

	@Override
	public Map<String,Object> getDDXBOrderDetail(String orderId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			//0.获取对应的订单信息
			if(null ==orderId || "".equals(orderId))
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息：订单id传入为null");
				return resultMap;
			}
			
			//1.获取对应订单的行信息(产品信息),把订单行信息指定到对应的订单信息上
			OrderVo orderVo = orderMapper.selectForDDXB(Long.parseLong(orderId));
			if(null==orderVo)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息：此订单存在");
				return resultMap;
			}
			reqMap.put("orderId", orderVo.getId());
			List<OrderLineVo> lstOrderLineVo = orderLineMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
			if(null!=lstOrderLineVo && !lstOrderLineVo.isEmpty())
			{
				orderVo.setOrderLines(lstOrderLineVo);
			}
			//2.返回当前订单的详情
			resultMap.put(RESULT_CURRENT_ORDER_KEY, orderVo);//前端UI上获取订单产品详情列表，需要用到的订单currentOrder.OrderLines即可
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}

	//暂时不用，用getDDFWOrderDetailByOrderId来获取服务订单的接口，后续复杂逻辑可能会用到此接口
	@Override
	public Map<String,Object> getDDFWOrderDetail(String plateNumber) {
		return null;
	}

	@Override
	public Map<String, Object> getDDFWOrderDetailByOrderId(String orderId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			//0.获取对应的订单信息
			if(null ==orderId || "".equals(orderId))
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息：订单id传入为null");
				return resultMap;
			}
			
			//1.获取对应订单的行信息(产品信息),把订单行信息指定到对应的订单信息上
			OrderVo orderVo = orderMapper.selectByPrimaryKey(Long.parseLong(orderId));
			if(null==orderVo)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息：此订单存在");
				return resultMap;
			}
			reqMap.put("orderId", orderVo.getId());
			List<OrderLineVo> lstOrderLineVo = orderLineMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
			if(null!=lstOrderLineVo && !lstOrderLineVo.isEmpty())
			{
				orderVo.setOrderLines(lstOrderLineVo);
			}
			//2.返回当前订单的详情
			resultMap.put(RESULT_CURRENT_ORDER_KEY, orderVo); //前端UI上获取订单产品详情列表，需要用到的订单currentOrder.OrderLines即可
			
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String,Object> exportDDXBOrder(String orderIds,HttpServletRequest request, HttpServletResponse response,String exportType,int type) {
		
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(exportType.equals(EXPORT_BATCH))
			{
				if(null==orderIds || orderIds.trim().equals(""))
				{
					resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
					resultMap.put(RESULT_ERROR_MSG_KEY, "请选择要导出的目标订单" );
					return resultMap;
				}
				String[] orderIdLst = orderIds.split(",");
				reqMap.put("orderIds", orderIdLst);
			}else if((exportType.equals(EXPORT_ALL)))
			{
				reqMap.put("orderIds", null);
			}
			reqMap.put("isExport", 0);//状态没有导出过的
			reqMap.put("nowdate", new Date());
			reqMap.put("orderType", OrderVo.DDXB_ORDER_TYPE);
			//0.查询传递过来的订单号，为没有被导出过的状态的订单
			List<Long> needExportOrderIds = orderMapper.getOrderIdsByCondition(reqMap);
			
			//1.获取未有导出订单的产品信息
			if(null==needExportOrderIds || needExportOrderIds.isEmpty())
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "所选订单已经导出过，不能重复导出" );
				return resultMap;
			}
			/* 原先的方式
			reqMap.put("needExportOrderIds", needExportOrderIds);
			List<OrderLineVo> lstOrderLineVo = orderLineMapper.getExportDataByOrderIds(reqMap);
			if(null==lstOrderLineVo || lstOrderLineVo.size()==0)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "没有找到对应产品可以导出,或者对应订单产品信息已经丢失" );
				return resultMap;
			}
			boolean isExport = exportExcel.exportData(lstOrderLineVo, "com.chevron.pms.model.OrderLineVo", response, "新保订单产品数据"+CommonUtil.generateCode("DD"), "新保订单数据", request);
			
			//2.重置新保订单状态 已经为导出
			if(isExport)
			{
				reqMap.put("needUpdateOrderIds", needExportOrderIds);
				reqMap.put("isExport", 1);
				orderMapper.updateBatchOrderExportStatus(reqMap);
			}*/
			
			
			//新方式、进行分组
			List<OilInjectionVo> lstOilInjections = getOrderLstGroupByOilInjection(needExportOrderIds);
			if(lstOilInjections.isEmpty())
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "所选订单在进行油量加注分组时发生错误");
				return resultMap;
			}
			resultMap =exportExcel.exportZipData(lstOilInjections, "com.chevron.pms.model.OrderLineVo", "新保订单产品数据包"+CommonUtil.generateCode("WS"), "XBOrderData",type, request, response);
			String resultCode = (String) resultMap.get(RESULT_CODE_KEY);
			if(resultCode.equals(SUCCESS_CODE))
			{
				reqMap.put("needUpdateOrderIds", needExportOrderIds);
				reqMap.put("isExport", 1);
				orderMapper.updateBatchOrderExportStatus(reqMap);
			}
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error(ex.getMessage(), ex);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+ex.getMessage() );
			return resultMap;
		}
		return resultMap;
	}

	
	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> exportAllDDXBOrder(HttpServletRequest request,
			HttpServletResponse response) {
		Map<String, Object> getDDXBOrderMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		getDDXBOrderMap = getDDXBOrdersByCondition(null);
		String resultCode = (String) getDDXBOrderMap.get(RESULT_CODE_KEY);
		if(resultCode.equals(ERROR_CODE))
		{
			return getDDXBOrderMap;
		}
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		lstOrders = (List<OrderVo>) getDDXBOrderMap.get(RESULT_LST_KEY);
		if(lstOrders.isEmpty())
		{
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息：没有可导出的数据");
			return resultMap;
		}
		String orderIdss = "";
		for(OrderVo orderVo:lstOrders)
		{
			orderIdss=orderIdss+orderVo.getId()+",";
		}
		String orderIDS = orderIdss.substring(0, orderIdss.length()-1);
		resultMap = exportDDXBOrder(orderIDS,request,response,EXPORT_ALL,ExportExcel.EXPORT_TYPE);
		return resultMap;
	}
	
	/**
	 * 根据订单机油注量进行分组
	 * <AUTHOR> 2016-10-21 上午11:21:48
	 * @param needExportOrderIds
	 * @return
	 */
	public List<OilInjectionVo> getOrderLstGroupByOilInjection(List<Long> needExportOrderIds)
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//查询所有符合条件的订单
		reqMap.put("orderLst", needExportOrderIds);
		List<OrderVo> lstOrders = orderMapper.getOrdersByOrderIds(reqMap);
		
		
		Collections.sort(lstOrders, new Comparator<OrderVo>(){  
			  
	        public int compare(OrderVo o1, OrderVo o2) {  
	          
	        	double d1 = Double.parseDouble(o1.getOilInjection());
	        	double d2 = Double.parseDouble(o2.getOilInjection());
	            if(d1 > d2){  
	                return 1;  
	            }  
	            if(d1 == d2){  
	                return 0;  
	            }  
	            return -1;  
	        }  
	    });   
		
		//根据订单再进行分组
		List<OilInjectionVo> lstOilInjection = new ArrayList<OilInjectionVo>();
		OilInjectionVo  oilInjectionVoNew = null;
		String lastOilInject = "";//控制->原始的
		String lastNewOilInject = "";//控制->新实例对象
		String currentOilInject = "";//控制->原始当前的
		List<Long> orderIds = null;
		for(OrderVo orderVo:lstOrders)
		{
			currentOilInject = orderVo.getOilInjection();
			if(!currentOilInject.equals(lastOilInject))
			{
				oilInjectionVoNew = new OilInjectionVo();
				orderIds = new ArrayList<Long>();
				oilInjectionVoNew.setOilInjection(currentOilInject);
			}
			orderIds.add(orderVo.getId());
			lastOilInject = currentOilInject;
			oilInjectionVoNew.setOrderIds(orderIds);
			
			if(!currentOilInject.equals(lastNewOilInject))
			{
				lstOilInjection.add(oilInjectionVoNew);
			}
			lastNewOilInject = currentOilInject;
		}
		
		return lstOilInjection;
	}
	
	
	
	@Override
	public Map<String, Object> queryFWBOrders(OrderDDBXCondition orderConditions) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(null==orderConditions)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			if("false".equals(is_custommybatisintercepor))
			{
				orderConditions.setIsOpernCustomMybatisInterceptor("2");//关闭的
			}
			
			if(null!=orderConditions.getOrderEffectiveTime())
			{
			   Date effectiveDate =	DateUtils.stringToDate(orderConditions.getOrderEffectiveTime(), 3);
			   orderConditions.setOrderEffectiveDate(effectiveDate);
			}
			//add by bo.liu 20170213 start
			if(null!=orderConditions.getOrderCreateTime())
			{
				 Date createDate =	DateUtils.stringToDate(orderConditions.getOrderCreateTime(), 3);
				 orderConditions.setOrderCreateDate(createDate);
			}
			
			if(null!=orderConditions.getOrderCreateEndTime() && !orderConditions.getOrderCreateEndTime().isEmpty())
			{
				 Date createEndDate =	DateUtils.addDays(DateUtils.stringToDate(orderConditions.getOrderCreateEndTime(), 3), 1) ;
				 orderConditions.setOrderCreateEndTime(DateUtils.toDateStr(createEndDate));
			}
			//end
			
			orderConditions.setOrderType(OrderVo.DDXB_ORDER_TYPE);
			orderConditions.setStatus("3");
			if(null==orderConditions.getSendStatus() || orderConditions.getSendStatus().isEmpty())
			{
				orderConditions.setSendStatus(null);
			}
			if(null==orderConditions.getOrderSource() || orderConditions.getOrderSource().isEmpty())
			{
				orderConditions.setOrderSource(null);
			}
			if(null==orderConditions.getOrderCode() || orderConditions.getOrderCode().isEmpty())
			{
				orderConditions.setOrderCode(null);
			}else
			{
				orderConditions.setOrderCode(orderConditions.getOrderCode().trim());
			}
			//add by bo.liu 12.19 start ADVANCED_SEARCH_TYPE
			int queryType = orderConditions.getQueryType();
			if(queryType == BaseParams.ADVANCED_SEARCH_TYPE)//高级搜索
			{
				orderConditions.setQueryField(null);
			}
			//end 
			
			Long totalRecord = 0L;
			//设置登录机构 特殊处理合纵数据 edit by leozli 20171016
			orderConditions.setLoginOrg(ContextUtil.getCurUser().getOrgId());
			
			lstOrders = orderMapper.getFWBOrdersByCondition(orderConditions);
			if("false".equals(is_custommybatisintercepor))//没有开数据权限
			{
				totalRecord = orderMapper.countGetFWBOrdersByCondition(orderConditions);
			}else
			{
				
				totalRecord = orderConditions.getTotalCount();
			}
			resultMap.put(RESULT_LST_KEY, lstOrders);
			resultMap.put("totalRecord", totalRecord);
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	public Map<String, Object> queryFWOrders(OrderDDBXCondition orderConditions)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(null==orderConditions)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			if("false".equals(is_custommybatisintercepor))
			{
				orderConditions.setIsOpernCustomMybatisInterceptor("2");//关闭的
			}
			
			//1.组装条件
			orderConditions.setOrderType(OrderVo.DDFW_ORDER_TYPE);
			orderConditions.setNowdate(new Date());
			//add by bo.liu 12.19 start ADVANCED_SEARCH_TYPE
			int queryType = orderConditions.getQueryType();
			if(queryType == BaseParams.ADVANCED_SEARCH_TYPE)//高级搜索
			{
				orderConditions.setQueryField(null);
			}
			//end 
			
			
			Long totalRecord = 0L;
			//设置登录机构 特殊处理合纵数据 edit by leozli 20171016
			if(ContextUtil.getCurUser()!=null) {
			orderConditions.setLoginOrg(ContextUtil.getCurUser().getOrgId());
			}
			
			lstOrders = orderMapper.getFWOrdersByConditionNew(orderConditions);
			if("false".equals(is_custommybatisintercepor))//没有开数据权限
			{
				totalRecord = orderMapper.countGetFWOrdersByCondition(orderConditions);
			}else
			{
				
				totalRecord = orderConditions.getTotalCount();
			}
			resultMap.put(RESULT_LST_KEY, lstOrders);
			resultMap.put("totalRecord", totalRecord);
			
		}catch (Exception e) {
			log.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	
	//add by bo.liu 1227
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> exportDDXBOrderNewForExcel(String orderIds,
			HttpServletRequest request, HttpServletResponse response,
			String exportType, int type,String orderSource,String settlementModelType) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(exportType.equals(EXPORT_BATCH))
			{
				if(null==orderIds || orderIds.trim().equals(""))
				{
					resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
					resultMap.put(RESULT_ERROR_MSG_KEY, "请选择要导出的目标订单" );
					return resultMap;
				}
				String[] orderIdLst = orderIds.split(",");
				reqMap.put("orderIds", orderIdLst);
			}else if((exportType.equals(EXPORT_ALL)))
			{
				reqMap.put("orderIds", null);
			}
			reqMap.put("isExport", 0);//状态没有导出过的
			reqMap.put("nowdate", new Date());
			reqMap.put("orderType", OrderVo.DDXB_ORDER_TYPE);
			//TODO 过滤订单来源，，，模式等
			reqMap.put("orderSource", orderSource);
			reqMap.put("settlementModelType", settlementModelType);
			//0.查询传递过来的订单号，为没有被导出过的状态的订单
			List<Long> needExportOrderIds = orderMapper.getOrderIdsByCondition(reqMap);
			
			//1.获取未有导出订单的产品信息
			if(null==needExportOrderIds || needExportOrderIds.isEmpty())
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "所选订单已经导出过，不能重复导出" );
				return resultMap;
			}
			
			//2.组织发送邮件的信息
			String fileName =  "大地新保导出到SAP订单_"+CommonUtil.generateCode("EXPORT");
			String clazzName = "com.chevron.pms.model.OrderForEmailExportNew";
			String exportEmailTmpFolder = "EXPORT_EMAIL_TMP_DATA";
			String acceptor = Constants.MAIL_DD_ACCEPTER;
			String ccAcceptor = Constants.MAIL_DD_CC;
			String emailSubject="mail.dd.xborder.subject";
			String ftl = MyPropertyConfigurer
					.getVal("mail.dd.xborder.ftl");
			Map<String,Object> dataMap = new HashMap<String,Object>();
			if(orderSource.equals(OrderCommonVo.ORDER_SOURCE_YXC_VALUE))
			{
				fileName = "易修车待确认新保订单_"+CommonUtil.generateCode("EXPORT");
				exportEmailTmpFolder = "EXPORT_EMAIL_YXC_ORDER_DATA";
				acceptor = Constants.MAIL_YXC_SAP_ACCEPTER;
			    ccAcceptor = Constants.MAIL_YXC_SAP_CC;
			    emailSubject="mail.yxc.xborder.sap.subject";
			    ftl = MyPropertyConfigurer
						.getVal("mail.yxc.order.confirm.ftl");
			}
			dataMap.put("fileName", fileName);
			dataMap.put("type", type);
			dataMap.put("className", clazzName);
			dataMap.put("exportEmailTmpFolder", exportEmailTmpFolder);
			dataMap.put("acceptor", acceptor);
			dataMap.put("ccAcceptor", ccAcceptor);
			dataMap.put("emailSubject", emailSubject);
			dataMap.put("ftl", ftl);
			dataMap.put("orderSource", orderSource);
			resultMap = processExportOrders(needExportOrderIds,dataMap);
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			ex.printStackTrace();
			log.error(ex.getMessage(), ex);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+ex.getMessage() );
			return resultMap;
		}
		return resultMap;
	}
	
	
	public Map<String, Object> processExportOrders(List<Long> needExportOrderIds, Map<String,Object> dataMap)throws Exception{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<OrderForEmailExport> lstExportOrderForEmail  = getOrdersForSendEmailNew(needExportOrderIds);
		if(lstExportOrderForEmail.isEmpty())
		{
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "所选订单在进行油量加注分组时发生错误");
			return resultMap;
		}
		//分成年卡和次卡两组
		List<OrderForEmailExport> lstNk = new ArrayList<OrderForEmailExport>();
		List<OrderForEmailExport> lstCk = new ArrayList<OrderForEmailExport>();
		for(int i=0;i<lstExportOrderForEmail.size();i++)
		{
			OrderForEmailExport orderForEmailExport = lstExportOrderForEmail.get(i);
			String cardType = orderForEmailExport.getCardType();
			if(cardType.equals(OrderForEmailExport.NK))
			{
				lstNk.add(orderForEmailExport);
				
			}else if(cardType.equals(OrderForEmailExport.CK))
			{
				lstCk.add(orderForEmailExport);
			}
			
			
		}
		//对年卡和次卡重组
		List<OrderForEmailExportNew> lstNkNew = new ArrayList<OrderForEmailExportNew>();
		List<OrderForEmailExportNew> lstCkNew = new ArrayList<OrderForEmailExportNew>();
		
		//4L特殊处理
		List<OrderForEmailExportNew> lstNkNew4L = new ArrayList<OrderForEmailExportNew>();
		List<OrderForEmailExportNew> lstCkNew4L = new ArrayList<OrderForEmailExportNew>();
		
		//4L除外的
		List<OrderForEmailExportNew> lstNkNewCommon = new ArrayList<OrderForEmailExportNew>();
		List<OrderForEmailExportNew> lstCkNewCommon = new ArrayList<OrderForEmailExportNew>();
		//组装导出的年卡数据
		processNKLst(lstNk,lstNkNew,lstNkNew4L,lstNkNewCommon);
		//组装导出的次卡数据
		processCKLst(lstCk,lstCkNew,lstCkNew4L,lstCkNewCommon);
		
		//组装发邮件的基础数据
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		
		//导出excel,发出邮件
		resultMap =exportExcel.exportDataNew(lstNkNew,lstCkNew,dataMap,context);
		String resultCode = (String) resultMap.get(RESULT_CODE_KEY);
		if(resultCode.equals(SUCCESS_CODE))
		{
			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
					needExportOrderIds, 2000);
			int order_page_account = order_all_page.getTotalPages();
			for (int i = 1; i <= order_page_account; i++) 
			{
				List<Long> tmpOrderIds = order_all_page.getObjects(i);
				reqMap.put("needUpdateOrderIds", tmpOrderIds);
				reqMap.put("isExport", 1);
				orderMapper.updateBatchOrderExportStatus(reqMap);
			}
		}
		return resultMap;
	}

	private void processCKLst(List<OrderForEmailExport> lstCk,
			List<OrderForEmailExportNew> lstCkNew,
			List<OrderForEmailExportNew> lstCkNew4L,
			List<OrderForEmailExportNew> lstCkNewCommon) {
		OrderForEmailExportNew ckLPKOrderForEmailExport = null;
		int j = 0;
		if(null!=lstCk && !lstCk.isEmpty())
		{
			for(int i=0;i<lstCk.size();i++)
			{
				OrderForEmailExport orderForEmailExport = lstCk.get(i);
				String oilString  = orderForEmailExport.getOilInjection();
				String oilInjection = orderForEmailExport.getOilInjection()
						.replace(".0", "L").trim();
				
				int serviceTimes = orderForEmailExport.getOilSndTimes();
				int amount = (orderForEmailExport.getAmount())/serviceTimes;
				int generateCount = serviceTimes;
				
				OrderForEmailExportNew orderForEmailExportNew = new OrderForEmailExportNew();
				orderForEmailExportNew.setAmount(amount);
				orderForEmailExportNew.setCardType(orderForEmailExport.getCardType());
				orderForEmailExportNew.setPlateNumber(orderForEmailExport.getPlateNumber());
				orderForEmailExportNew.setSku(orderForEmailExport.getSku().replaceAll(REPLACE_SKU_PART, ""));
				orderForEmailExportNew.setOilInjection(oilInjection);
				orderForEmailExportNew.setOrderTime(orderForEmailExport.getOrderTime());
				orderForEmailExportNew.setOrderWarehouse(orderForEmailExport.getWarehouse());
				if(oilString.equals("4.0"))
				{
					for(int k1=0;k1<generateCount;k1++)
					{
						lstCkNew4L.add(orderForEmailExportNew);
					}
				}else
				{
						j++;
						lstCkNewCommon.add(orderForEmailExportNew);
						if(j%2==0)
						{
							for(int k1=0;k1<generateCount-1;k1++)
							{
								lstCkNewCommon.add(ckLPKOrderForEmailExport);
								lstCkNewCommon.add(orderForEmailExportNew);
							}
						}
						ckLPKOrderForEmailExport = new OrderForEmailExportNew();
						ckLPKOrderForEmailExport = orderForEmailExportNew;
						
				}
			}
		}
		lstCkNew.addAll(lstCkNew4L);//mod by bo.liu 0607 getOrderFor4L(lstCkNew4L)
		lstCkNew.addAll(lstCkNewCommon);
		
	}

	private void processNKLst(List<OrderForEmailExport> lstNk,List<OrderForEmailExportNew> lstNkNew,
			List<OrderForEmailExportNew> lstNkNew4L,
			List<OrderForEmailExportNew> lstNkNewCommon) {
		OrderForEmailExportNew nkLPKOrderForEmailExport = null;
		int j = 0;
		if(null!=lstNk && !lstNk.isEmpty())
		{
			
			for(int i=0;i<lstNk.size();i++)
			{
				OrderForEmailExport orderForEmailExport = lstNk.get(i);
				String oilString  = orderForEmailExport.getOilInjection();
				String oilInjection = orderForEmailExport.getOilInjection()
						.replace(".0", "L").trim();
				int serviceTimes = orderForEmailExport.getOilSndTimes();
				int amount = (orderForEmailExport.getAmount()*3/2)/serviceTimes;
				int generateCount = serviceTimes*2/3;
				
				OrderForEmailExportNew orderForEmailExportNew = new OrderForEmailExportNew();
				orderForEmailExportNew.setAmount(amount);//orderForEmailExport.getAmount()/2 除以2 mod by bo.liu 0607
				orderForEmailExportNew.setCardType(orderForEmailExport.getCardType());
				orderForEmailExportNew.setPlateNumber(orderForEmailExport.getPlateNumber());
				orderForEmailExportNew.setSku(orderForEmailExport.getSku().replace(REPLACE_SKU_PART, ""));
				orderForEmailExportNew.setOilInjection(oilInjection);
				orderForEmailExportNew.setOrderTime(orderForEmailExport.getOrderTime());
				orderForEmailExportNew.setOrderWarehouse(orderForEmailExport.getWarehouse());
				if(oilString.equals("4.0"))
				{
					for(int k=0;k<generateCount;k++)
					{
						lstNkNew4L.add(orderForEmailExportNew);
					}
				}else
				{
					j++;
					lstNkNewCommon.add(orderForEmailExportNew);
					if(j%2==0)
					{
						for(int k=0;k<generateCount-1;k++)
						{
							lstNkNewCommon.add(nkLPKOrderForEmailExport);
							lstNkNewCommon.add(orderForEmailExportNew);
						}
					}
					nkLPKOrderForEmailExport = new OrderForEmailExportNew();
					nkLPKOrderForEmailExport = orderForEmailExportNew;
				}
			}
		}
		lstNkNew.addAll(lstNkNew4L);
		lstNkNew.addAll(lstNkNewCommon);
	}

	public List<OrderForEmailExport> getOrdersForSendEmail(List<Long> needExportOrderIds)throws Exception
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//查询所有符合条件的订单
		reqMap.put("orderIds", needExportOrderIds);
		reqMap.put("nowdate", new Date());
		List<OrderForEmailExport> lstOrders = orderMapper.getOrderForSendEmailByCondition(reqMap);
		Collections.sort(lstOrders, new Comparator<OrderForEmailExport>(){  
			  
	        public int compare(OrderForEmailExport o1, OrderForEmailExport o2) {  
	          
	        	Long d1 = o1.getId();
	        	Long d2 = o2.getId();
	            if(d1 > d2){  
	                return 1;  
	            }  
	            if(d1 == d2){  
	                return 0;  
	            }  
	            return -1;  
	        }  
	    });   
		
		
		//根据订单再进行分组
		List<OrderForEmailExport> lstExportOrders = new ArrayList<OrderForEmailExport>();
		OrderForEmailExport  orderForEmailExportNew = null;
		Long lastOrderId = 0L;//控制->原始的
		Long lastNewOrderId = 0L;//控制->新实例对象
		Long currentOrderId = 0L;//控制->原始当前的
		for(OrderForEmailExport orderVo:lstOrders)
		{
			currentOrderId = orderVo.getId();
			String type = orderVo.getType();
			if(!currentOrderId.equals(lastOrderId))
			{
				String oilInjectionL= orderVo.getOilInjection()
						.replace(".0", "").trim();
				orderForEmailExportNew = new OrderForEmailExport();
				orderForEmailExportNew.setId(currentOrderId);
				orderForEmailExportNew.setPlateNumber(orderVo.getPlateNumber());
				orderForEmailExportNew.setOilInjection(oilInjectionL+"L");
				orderForEmailExportNew.setOrderTime(orderVo.getOrderTime());
				
			}
			lastOrderId = currentOrderId;
			String sku = orderVo.getSku();
			int amount = orderVo.getAmount();
			
			if(null!=sku)
			{
				String exportAmount = "";
				if(null==type)
				{
					type = "";
				}
				
				if(amount!=0)
				{
					
					if(type.equals(OrderForEmailExport._2_TIMES_ORDER_PREFERENTIAL_TYPE[0])||type.equals(OrderForEmailExport._2_TIMES_ORDER_PREFERENTIAL_TYPE[1]))
					{
						amount = amount/2;
					}
					exportAmount = ""+amount;
				}
				
				
				if(sku.equals(OrderForEmailExport.SKUS[0]))
				{
					orderForEmailExportNew.set_LPK264_AMOUNT(exportAmount);
				}
				else if(sku.equals(OrderForEmailExport.SKUS[1]))
				{
					orderForEmailExportNew.set_NJK264_AMOUNT(exportAmount);
				}
				
				if(sku.equals(OrderForEmailExport.SKUS[2]))
				{
					orderForEmailExportNew.set_LPK268_AMOUNT(exportAmount);
				}
				else if(sku.equals(OrderForEmailExport.SKUS[3]))
				{
					orderForEmailExportNew.set_NJK268_AMOUNT(exportAmount);
				}
				
				if(sku.equals(OrderForEmailExport.SKUS[4]))
				{
					orderForEmailExportNew.set_LPK269_AMOUNT(exportAmount);
				}
				else if(sku.equals(OrderForEmailExport.SKUS[5]))
				{
					orderForEmailExportNew.set_NJK269_AMOUNT(exportAmount);
				}
				
				if(sku.equals(OrderForEmailExport.SKUS[6]))
				{
					orderForEmailExportNew.set_LPK263_AMOUNT(exportAmount);
				}
				else if(sku.equals(OrderForEmailExport.SKUS[7]))
				{
					orderForEmailExportNew.set_NJK263_AMOUNT(exportAmount);
				}
			}
			if(!currentOrderId.equals(lastNewOrderId))
			{
				
				lstExportOrders.add(orderForEmailExportNew);
				if(type.equals(OrderForEmailExport._2_TIMES_ORDER_PREFERENTIAL_TYPE[0])||type.equals(OrderForEmailExport._2_TIMES_ORDER_PREFERENTIAL_TYPE[1]))
				{
					lstExportOrders.add(orderForEmailExportNew);
				}
				
			}
			lastNewOrderId = currentOrderId;
		}
		
		//对lstOrders进行重构
		List<OrderForEmailExport> lstNewOrders = new ArrayList<OrderForEmailExport>();
		int indexNo = 0;
		for(OrderForEmailExport orderVo:lstExportOrders)
		{
			indexNo = indexNo+1;
			OrderForEmailExport newOrder = new OrderForEmailExport(); 
			newOrder.setIndexNo(indexNo);
			newOrder.set_LPK263_AMOUNT(orderVo.get_LPK263_AMOUNT());
			newOrder.set_NJK263_AMOUNT(orderVo.get_NJK263_AMOUNT());
			newOrder.set_LPK264_AMOUNT(orderVo.get_LPK264_AMOUNT());
			newOrder.set_NJK264_AMOUNT(orderVo.get_NJK264_AMOUNT());
			newOrder.set_LPK268_AMOUNT(orderVo.get_LPK268_AMOUNT());
			newOrder.set_NJK268_AMOUNT(orderVo.get_NJK268_AMOUNT());
			newOrder.set_LPK269_AMOUNT(orderVo.get_LPK269_AMOUNT());
			newOrder.set_NJK269_AMOUNT(orderVo.get_NJK269_AMOUNT());
			newOrder.setOilInjection(orderVo.getOilInjection());
			newOrder.setPlateNumber(orderVo.getPlateNumber());
			newOrder.setOrderTime(orderVo.getOrderTime());
			lstNewOrders.add(newOrder);
			
		}
		return lstNewOrders;
	}
	
	
	
	
	
	
	public List<OrderForEmailExport> getOrdersForSendEmailNew(List<Long> needExportOrderIds)throws Exception
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<OrderForEmailExport> lstReturnOrders = new ArrayList<OrderForEmailExport>();
		//查询所有符合条件的订单
		ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
				needExportOrderIds, 2000);
		int order_page_account = order_all_page.getTotalPages();
		for (int i = 1; i <= order_page_account; i++) 
		{
			List<Long> tmpOrderIds = order_all_page.getObjects(i);
			reqMap.put("orderIds", tmpOrderIds);
			reqMap.put("nowdate", new Date());
			List<OrderForEmailExport> lstOrders = orderMapper.getOrderForSendEmailByCondition(reqMap);
			lstReturnOrders.addAll(lstOrders);
		}
		
		/*Collections.sort(lstOrders, new Comparator<OrderForEmailExport>(){  
			  
	        public int compare(OrderForEmailExport o1, OrderForEmailExport o2) {  
	          
	        	Long d1 = o1.getId();
	        	Long d2 = o2.getId();
	            if(d1 > d2){  
	                return 1;  
	            }  
	            if(d1 == d2){  
	                return 0;  
	            }  
	            return -1;  
	        }  
	    }); */
		return lstReturnOrders;
	}
	
	
	/**
	 * 针对4L的进行重组
	 * <AUTHOR> 2017-3-16 下午3:01:02
	 * @param lst  要重组的列表
	 * @return
	 */
	public List<OrderForEmailExportNew>  getOrderFor4L(List<OrderForEmailExportNew> lst)
	{
		 List<OrderForEmailExportNew>  returnLst = new ArrayList<OrderForEmailExportNew>();
		 //根据sku进行排序
		 Collections.sort(lst, new Comparator<OrderForEmailExportNew>(){  
			  
		        public int compare(OrderForEmailExportNew o1, OrderForEmailExportNew o2) {  
		          
		        	return o1.getSku().compareTo(o2.getSku());
		        }  
		    }); 
		 
	   OrderForEmailExportNew  orderForEmailExportNew0 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew1 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew2 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew3 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew4 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew5 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew6 = new OrderForEmailExportNew();
	   OrderForEmailExportNew  orderForEmailExportNew7 = new OrderForEmailExportNew();
		for(int i=0;i<lst.size();i++)
		{
			OrderForEmailExportNew orderForEmailExportTemp = lst.get(i);
			String sku = orderForEmailExportTemp.getSku();
			int amount = orderForEmailExportTemp.getAmount();
			String cardType = orderForEmailExportTemp.getCardType();
			String plantNo = orderForEmailExportTemp.getPlateNumber();
			String date = orderForEmailExportTemp.getOrderTime();
			String oilInjectionL= orderForEmailExportTemp.getOilInjection()
					.replace(".0", "L").trim();
			
			if(sku.equals(OrderForEmailExport.SKUS[0]))
			{
				orderForEmailExportNew0.setSku(sku);
				orderForEmailExportNew0.setAmount(orderForEmailExportNew0.getAmount()+amount);
				orderForEmailExportNew0.setCardType(cardType);
				orderForEmailExportNew0.setOilInjection(oilInjectionL);
				orderForEmailExportNew0.setOrderTime(date);
				orderForEmailExportNew0.setPlateNumber(orderForEmailExportNew0.getPlateNumber()+plantNo+",");
				
			}else if(sku.equals(OrderForEmailExport.SKUS[1]))
			{
				orderForEmailExportNew1.setSku(sku);
				orderForEmailExportNew1.setAmount(orderForEmailExportNew1.getAmount()+amount);
				orderForEmailExportNew1.setCardType(cardType);
				orderForEmailExportNew1.setOilInjection(oilInjectionL);
				orderForEmailExportNew1.setOrderTime(date);
				orderForEmailExportNew1.setPlateNumber(orderForEmailExportNew1.getPlateNumber()+plantNo+",");
			}
			else if(sku.equals(OrderForEmailExport.SKUS[2]))
			{
				orderForEmailExportNew2.setSku(sku);
				orderForEmailExportNew2.setAmount(orderForEmailExportNew2.getAmount()+amount);
				orderForEmailExportNew2.setCardType(cardType);
				orderForEmailExportNew2.setOilInjection(oilInjectionL);
				orderForEmailExportNew2.setOrderTime(date);
				orderForEmailExportNew2.setPlateNumber(orderForEmailExportNew2.getPlateNumber()+plantNo+",");
			}
			else if(sku.equals(OrderForEmailExport.SKUS[3]))
			{
				orderForEmailExportNew3.setSku(sku);
				orderForEmailExportNew3.setAmount(orderForEmailExportNew3.getAmount()+amount);
				orderForEmailExportNew3.setCardType(cardType);
				orderForEmailExportNew3.setOilInjection(oilInjectionL);
				orderForEmailExportNew3.setOrderTime(date);
				orderForEmailExportNew3.setPlateNumber(orderForEmailExportNew3.getPlateNumber()+plantNo+",");
			}
			else if(sku.equals(OrderForEmailExport.SKUS[4]))
			{
				orderForEmailExportNew4.setSku(sku);
				orderForEmailExportNew4.setAmount(orderForEmailExportNew4.getAmount()+amount);
				orderForEmailExportNew4.setCardType(cardType);
				orderForEmailExportNew4.setOilInjection(oilInjectionL);
				orderForEmailExportNew4.setOrderTime(date);
				orderForEmailExportNew4.setPlateNumber(orderForEmailExportNew4.getPlateNumber()+plantNo+",");
			}
			else if(sku.equals(OrderForEmailExport.SKUS[5]))
			{
				orderForEmailExportNew5.setSku(sku);
				orderForEmailExportNew5.setAmount(orderForEmailExportNew5.getAmount()+amount);
				orderForEmailExportNew5.setCardType(cardType);
				orderForEmailExportNew5.setOilInjection(oilInjectionL);
				orderForEmailExportNew5.setOrderTime(date);
				orderForEmailExportNew5.setPlateNumber(orderForEmailExportNew5.getPlateNumber()+plantNo+",");
			}
			else if(sku.equals(OrderForEmailExport.SKUS[6]))
			{
				orderForEmailExportNew6.setSku(sku);
				orderForEmailExportNew6.setAmount(orderForEmailExportNew6.getAmount()+amount);
				orderForEmailExportNew6.setCardType(cardType);
				orderForEmailExportNew6.setOilInjection(oilInjectionL);
				orderForEmailExportNew6.setOrderTime(date);
				orderForEmailExportNew6.setPlateNumber(orderForEmailExportNew6.getPlateNumber()+plantNo+",");
			}
			else if(sku.equals(OrderForEmailExport.SKUS[7]))
			{
				orderForEmailExportNew7.setSku(sku);
				orderForEmailExportNew7.setAmount(orderForEmailExportNew7.getAmount()+amount);
				orderForEmailExportNew7.setCardType(cardType);
				orderForEmailExportNew7.setOilInjection(oilInjectionL);
				orderForEmailExportNew7.setOrderTime(date);
				orderForEmailExportNew7.setPlateNumber(orderForEmailExportNew7.getPlateNumber()+plantNo+",");
			}
		}
		
		
		if(null!=orderForEmailExportNew6.getSku()&& !orderForEmailExportNew6.equals(""))
		{
			int length =  orderForEmailExportNew6.getPlateNumber().length()-1;
			orderForEmailExportNew6.setPlateNumber(orderForEmailExportNew6.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew6);
		}
		
		if(null!=orderForEmailExportNew7.getSku()&& !orderForEmailExportNew7.equals(""))
		{
			int length =  orderForEmailExportNew7.getPlateNumber().length()-1;
			orderForEmailExportNew7.setPlateNumber(orderForEmailExportNew7.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew7);
		}
		
		
		
		
		if(null!=orderForEmailExportNew0.getSku()&& !orderForEmailExportNew0.equals(""))
		{
			int length =  orderForEmailExportNew0.getPlateNumber().length()-1;
			orderForEmailExportNew0.setPlateNumber(orderForEmailExportNew0.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew0);
		}
		if(null!=orderForEmailExportNew1.getSku()&& !orderForEmailExportNew1.equals(""))
		{
			int length =  orderForEmailExportNew1.getPlateNumber().length()-1;
			orderForEmailExportNew1.setPlateNumber(orderForEmailExportNew1.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew1);
		}
		
		if(null!=orderForEmailExportNew2.getSku()&& !orderForEmailExportNew2.equals(""))
		{
			int length =  orderForEmailExportNew2.getPlateNumber().length()-1;
			orderForEmailExportNew2.setPlateNumber(orderForEmailExportNew2.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew2);
		}
		
		if(null!=orderForEmailExportNew3.getSku()&& !orderForEmailExportNew3.equals(""))
		{
			int length =  orderForEmailExportNew3.getPlateNumber().length()-1;
			orderForEmailExportNew3.setPlateNumber(orderForEmailExportNew3.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew3);
		}
		
		if(null!=orderForEmailExportNew4.getSku()&& !orderForEmailExportNew4.equals(""))
		{
			int length =  orderForEmailExportNew4.getPlateNumber().length()-1;
			orderForEmailExportNew4.setPlateNumber(orderForEmailExportNew4.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew4);
		}
		
		if(null!=orderForEmailExportNew5.getSku()&& !orderForEmailExportNew5.equals(""))
		{
			int length =  orderForEmailExportNew5.getPlateNumber().length()-1;
			orderForEmailExportNew5.setPlateNumber(orderForEmailExportNew5.getPlateNumber().substring(0, length));
			returnLst.add(orderForEmailExportNew5);
		}
		
		
		return returnLst;
	}
	
	@Override
	public Map<String, Object> updateOrderById(Long id,int status) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			OrderVo order = new OrderVo();
			order.setStatus(String.valueOf(status));
			order.setId(id);
			orderMapper.updateByPrimaryKeySelective(order);
			resultMap.put("code", "success");
		}catch (Exception e) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}
	
	
	
	/**
	 * 
	 * <AUTHOR> 2017-8-10 下午5:25:31
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public boolean confirmOrders(String beginDate,String endDate)
	{
		boolean isSuccess = false;
		try
		{
			List<OrderVo> lstOrders = scanorderService.getNeedConfirmOfOrder(beginDate,endDate);
			scanorderService.updateOrderStatus(lstOrders,OrderCommonVo.ORDER_STATUS[3]);
			isSuccess = true;
		}catch(Exception ex)
		{
			isSuccess = false;
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			ex.printStackTrace();
			log.error(ex.getMessage());
		}
		return isSuccess;
	}
	
	
	/**
	 * 拒绝，，就是让易修车再次确认  还是需要再次确认
	 * <AUTHOR> 2017-8-10 下午5:30:26
	 * @param beginDate
	 * @param endDate
	 * @return
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public boolean refundOrders(String beginDate,String endDate)
	{
		boolean isSuccess = false;
		try
		{
			List<OrderVo> lstOrders = scanorderService.getNeedConfirmOfOrder(beginDate,endDate);
			scanorderService.updateOrderStatus(lstOrders,OrderCommonVo.ORDER_STATUS[9]);
			isSuccess = true;
		}catch(Exception ex)
		{
			isSuccess = false;
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			ex.printStackTrace();
			log.error(ex.getMessage());
		}
		return isSuccess;
	}
	
	@Override
	public Map<String, Object> updateOrderByIds(String[] ids) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Map<String, Object> reqMap = new HashMap<String, Object>();
			reqMap.put("orderIds", new ArrayList<String>(Arrays.asList(ids)));
			reqMap.put("status", OrderCommonVo.ORDER_STATUS[3]);
			orderMapper.updateAllOrderStatusByOrderIds(reqMap);
			resultMap.put("code", "success");
		}catch (Exception e) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> manualInsertXBOrder(ManualXBOrder xbOrder) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			log.info("xbOrder0:"+xbOrder);
			//准备基础数据
			Map<String, Object> baseDataMap = new HashMap<String, Object>();
			WxTUser currentUser = ContextUtil.getCurUser();
			if(!WxTUser.USER_MODEL_CHEVRON.equals(currentUser.getUserModel()))
			{
				Long spid = currentUser.getOrgId();
				OrganizationVo org = organizationVoMapper.selectByPrimaryKey(spid);
				if(null==org)
				{
					throw new Exception("该用户所属机构不支创建订单");
				}
				String orgName = org.getOrganizationName();
				if(orgName.equals(OrderCommonVo.ORDER_SP[0]))
				{
					xbOrder.setOrderSource(OrderCommonVo.ORDER_SOURCE_TYPE[0]);
				}else if(orgName.equals(OrderCommonVo.ORDER_SP[1]))
				{
					xbOrder.setOrderSource(OrderCommonVo.ORDER_SOURCE_TYPE[2]);
				}else 
				{
					throw new Exception("该机构不支创建订单");
				}
				
			}
			
			baseDataMap = generateXBOrderBaseData(xbOrder.getOrderSource());
			//0.根据卡类型，机油注量，购买新保次数获取，，优惠类型
			Map<String,Object> preferentialMap = getOrderPreferentialInfo(xbOrder);
			PreferentialTypeVo	preferentialTypeVo = (PreferentialTypeVo) preferentialMap.get("preferentialTypeVo");
			if(null==preferentialTypeVo)
			{
				log.info("preferentialTypeVo:"+preferentialTypeVo);
				//resultMap.put("code", "syserror");
				//return resultMap;
				throw new Exception("没找到合适的优惠类型");
			}
			log.info("xbOrder1:"+xbOrder);
			//1.构建一条需要录入的新保订单
			OrderVo order = generateXBOrder(xbOrder,baseDataMap);
			order.setType(preferentialTypeVo.getTypeCode());
			order.setServiceAcount(preferentialTypeVo.getOilDeliveryTimes());
			order.setRemainingServiceTimes(preferentialTypeVo.getOilDeliveryTimes());
			order.setPreferentialTypePrice(""+preferentialTypeVo.getOrgPrice());
			order.setViscosity(xbOrder.getOilViscosity());
			log.info("xbOrder2:"+order);
			//2.获取机油规格，机油注量，计算后，返回最终需要录入的订单行信息
			List<OrderLineVo> lstOrderLines = getNeedInsertOrderLines(order);
			if(null==lstOrderLines || lstOrderLines.isEmpty())
			{
				log.info("lstOrderLines:"+lstOrderLines);
				//resultMap.put("code", "syserror");
				//return resultMap;
				throw new Exception("没找到合适产品信息");
			}
			order.setOrderLines(lstOrderLines);
			log.info("xbOrder3:"+order);
			//3.录入订单信息
			List<OrderVo> lstOrderVo = new ArrayList<OrderVo>();//用于添加采购订单的
			lstOrderVo.add(order);
			orderBizService.createOrders(lstOrderVo,SequenceTypes.NEW_CONFIRMED_ORDER,0);
			resultMap.put("code", "success");
		}catch (Exception e) {
			e.printStackTrace();
			log.info("manualInsertXBOrder:"+e.getLocalizedMessage());
			resultMap.put("code", "syserror");
			resultMap.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception")+"异常信息:"+e.getLocalizedMessage());
		}
		return resultMap;
	}
	private OrderVo generateXBOrder(ManualXBOrder xbOrder,
			Map<String, Object> baseDataMap) {
		OrderVo order = new OrderVo();
		order.setPlateNumber(xbOrder.getPlanteNumber());
		order.setOrderType("DP");
		order.setSource((String)baseDataMap.get("orderSource"));
		order.setSourceId(order.getSource()+"1");
		order.setCardType(xbOrder.getCardType());
		order.setOilInjection(xbOrder.getOilIngection());
		order.setCreateTime(new Date());
		order.setUpdateTime(new Date());
		order.setCreator((String)""+baseDataMap.get("userId"));
		order.setStatus((String) baseDataMap.get("orderStatus"));
		order.setRegionName(xbOrder.getRegionName());
		if(null!=xbOrder.getEffectiveTime() && !xbOrder.getEffectiveTime().trim().isEmpty())
		{
			order.setEffectiveTime(DateUtils.parseDate(xbOrder.getEffectiveTime(), 3));
		}
		if(null!=xbOrder.getInvalidTime() && !xbOrder.getInvalidTime().trim().isEmpty())
		{
			order.setInvalidTime(DateUtils.parseDate(xbOrder.getInvalidTime(), 3));
		}
		order.setSettlementModelType(OrderVo.SETTLEMENTMODELTYPE2);
		order.setPoOrderPartnerId((Long)baseDataMap.get("spid"));
		order.setCarType(xbOrder.getCarType());
		return order;
	}

	//end

	private List<OrderLineVo> getNeedInsertOrderLines(OrderVo xbOrder) {
		List<OrderLineVo> lstOrderLineVoRestore = new ArrayList<OrderLineVo>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		try
		{
			float totalOilInjection = Float.parseFloat(xbOrder.getOilInjection());
			Integer oilInjection = Integer.parseInt(xbOrder.getOilInjection().replaceAll(".0", "").trim());
			//根据机油规格查询机油信息
			reqMap.put("oilViscosity", xbOrder.getViscosity());
			List<ProductVo> lstProducts = productMapper.selectProductByMap(reqMap);
			if(null==lstProducts || lstProducts.isEmpty())
			{
				return null;
			}
			//机油
			if(totalOilInjection>4.0f) //>4L
			{
				
				for(ProductVo product:lstProducts)
				{
					String capacity = product.getCapacity();
					int oilAmount = 1;
					if(capacity.equals("1") && product.getSku().contains(REPLACE_SKU_PART))
					{
						oilAmount =  oilInjection-4;
					}
					OrderLineVo orderLineVo = new OrderLineVo();
					if(null!=product)
					{
						orderLineVo.setProductId(product.getId());
						orderLineVo.setPrice(product.getSalePrice());
					}
					orderLineVo.setSku(product.getSku());
					orderLineVo.setProductName(product.getName());
					orderLineVo.setAmount(oilAmount);
					orderLineVo.setUnits(product.getUnits());
					orderLineVo.setStatus("0");
					orderLineVo.setCreateTime(new Date());
					orderLineVo.setUpdateTime(new Date());
					orderLineVo.setCreator(xbOrder.getCreator());
					orderLineVo.setType(ORDER_LINE_OIL_TYPE);
					lstOrderLineVoRestore.add(orderLineVo);
				}
				
			}else //小于等于4L 
			{
				
				ProductVo product =  lstProducts.get(0);
				OrderLineVo orderLineVo4l = new OrderLineVo();
				if(null!=product)
				{
					orderLineVo4l.setProductId(product.getId());
					orderLineVo4l.setPrice(product.getSalePrice());
				}
				orderLineVo4l.setSku(product.getSku());
				orderLineVo4l.setProductName(product.getName());
				orderLineVo4l.setAmount(1);
				orderLineVo4l.setUnits(product.getUnits());
				orderLineVo4l.setStatus("0");
				orderLineVo4l.setCreateTime(new Date());
				orderLineVo4l.setUpdateTime(new Date());
				orderLineVo4l.setCreator(xbOrder.getCreator());
				orderLineVo4l.setType(ORDER_LINE_OIL_TYPE);
				lstOrderLineVoRestore.add(orderLineVo4l);
				
			}
			
			//卡类型
			String cardType = xbOrder.getCardType();
			//对应机油发货次数   即后续服务的次数
			int oilSendTimes = xbOrder.getServiceAcount();
			List<OrderLineVo> lstOrderLineVoRestoreNew = new ArrayList<OrderLineVo>();
			for(OrderLineVo orderLinevo:lstOrderLineVoRestore)
			{
				int amount = orderLinevo.getAmount();
				if(cardType.equals(PreferentialTypeVo.NK_CARDTYPE))
				{
					orderLinevo.setAmount(amount*oilSendTimes*2/3);
				}else
				{
					orderLinevo.setAmount(amount*oilSendTimes);//发几次就是几次的量
				}
				lstOrderLineVoRestoreNew.add(orderLinevo);
			}
			return lstOrderLineVoRestoreNew;
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getNeedInsertOrderLines:"+e.getLocalizedMessage());
		}
	   return null;
	}
	
	private Map<String,Object> getOrderPreferentialInfo(ManualXBOrder xbOrder) {
		
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//数量或者说是次数  购买新保的次数
		int xbBuyedQuantity = xbOrder.getBuyXBTimes();
		String oilCost = "";
		String plateNo = xbOrder.getPlanteNumber();
		//优惠卡类型，年卡，次卡
		String couponCardType = xbOrder.getCardType();
		String oilInjection = xbOrder.getOilIngection();
		float totalOilInjection = Float.parseFloat(oilInjection);
		if(totalOilInjection>4.0f)
		{
			oilCost = OIL_COST_4L;
		}else if(totalOilInjection<=4.0f)
		{
			oilCost = OIL_COST_1L;
		}
		//根据次数与数量去查询对应的订单类型；
		reqMap.put("oilCost", oilCost);
		reqMap.put("xbBuyedQuantity", xbBuyedQuantity);
		reqMap.put("couponCardType", couponCardType);
		reqMap.put("item", "免费");
		List<PreferentialTypeVo> preferentialTypeVolst = preferentialTypeVoMapper.selectByOilCostAndCardTypeAndXbBuyTimes(reqMap);
		if(null==preferentialTypeVolst)
		{
			resultMap.put("code", MessageContants.COUPON_TYPE_NULL_CODE);
			resultMap.put("errorMsg", MessageContants.COUPON_TYPE_NULL_MSG+plateNo);
			return resultMap;
		}
		if(null==preferentialTypeVolst || preferentialTypeVolst.isEmpty())
		{
			resultMap.put("code", MessageContants.COUPON_TYPE_ACCOUNT_CODE);
			resultMap.put("errorMsg", MessageContants.COUPON_TYPE_ACCOUNT_MSG+plateNo);
			return resultMap;
		}
		PreferentialTypeVo preferentialTypeVo = preferentialTypeVolst.get(0);
		resultMap.put("preferentialTypeVo", preferentialTypeVo);
		return resultMap;
	}
	
	
	public Map<String, Object> generateXBOrderBaseData(String orderSourceType) {
		Map<String,Object> baseData = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		WxTUser currentUser = ContextUtil.getCurUser();
		Long mUserId = 0L;
		if(null!=currentUser)
		{
			mUserId = currentUser.getUserId();
		}
		
		String orderStatus = "";
		String orderSource = "";
		if(orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[0]))//大地
		{
			reqMap.put("orgName", OrderCommonVo.ORDER_SP[0]);
			orderStatus = OrderCommonVo.ORDER_STATUS[3];
			orderSource = OrderCommonVo.ORDER_SOURCE_DDBX_VALUE;
		}else if(orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[2]))//易修车
		{
			reqMap.put("orgName", OrderCommonVo.ORDER_SP[1]);
			orderStatus = OrderCommonVo.ORDER_STATUS[0];
			orderSource = OrderCommonVo.ORDER_SOURCE_YXC_VALUE;
		}
		baseData.put("userId", mUserId);
		baseData.put("orderStatus", orderStatus);
		baseData.put("orderSource", orderSource);
		Long spid = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
		baseData.put("spid", spid);
		return baseData;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void importOrderService(Workbook wb) throws Exception {
		Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(wb, OrderVo.class, 
				new String[]{"id", "orgName", "orderNo", "plateNumber", "billId", "receiveUserName", 
			"receivePhoneNo", "deliveryTime",null,null,null,null,null,null},2, 0);
		if("dataInvalid".equals(repsMap.get("result"))){
			throw new WxPltException("Excel解析异常");
		}
		
		List<OrderVo> importList = (List<OrderVo>) repsMap.get("datalst");
		//区分插入更新数据
		ImportDataPageModelUtil batchPageUtil = new ImportDataPageModelUtil(importList, 200);
		int total = batchPageUtil.getTotalPages();
		for (int i = 1; i <= total; i++) {
			List<OrderVo> vo = batchPageUtil.getObjects(i);
			for(OrderVo v:vo) {
				if(v.getId()!=null) {
				OrderVo vs = orderMapper.getOrderDetailById(v.getId());
				 if(vs!=null && vs.getId()!=null) {
				if(StringUtils.isNotEmpty(vs.getBillId())) {
					if(StringUtils.isNotEmpty(v.getBillId())) {
						v.setStatus(vs.getStatus());
					}else{
						v.setStatus(OrderVo.ORDER_STATUS_4);
					}
				}else{
					if(StringUtils.isNotEmpty(v.getBillId())) {
						v.setStatus(OrderVo.ORDER_STATUS_5);
						}else {
							v.setStatus(OrderVo.ORDER_STATUS_4);
						}
				}
				 }
				}
			}
			// 访问数据库 进行批量更新操作
			try {
				orderMapper.updateBatch(vo);
			} catch (Exception e) {
				e.printStackTrace();
			}
//			if(orderMapper.updateBatch(vo) < 1){
//				throw new WxPltException("批量更新数据失败");
//			}
		}		
		
	}
}
