package com.sys.quartz.job;

import com.chevron.localmarketing.business.NonLocalMktFlowFormBizService;
import com.sys.log.util.LogUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class NonLocMktAutoAcceptTask {

    @Autowired
    private NonLocalMktFlowFormBizService nonLocalMktFlowFormBizService;

    private static Logger log = LoggerFactory.getLogger(NonLocMktAutoAcceptTask.class);

    public synchronized void execute() {
        log.info("NonLocMktAutoAcceptTask job start....");
        try {
            nonLocalMktFlowFormBizService.autoAccept();
            log.info("NonLocMktAutoAcceptTask success.");
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.addErrorLog(1l, "com.sys.quartz.job.NonLocMktAutoAcceptTask.execute", "流程自动流转到下个节点执行失败。" + e.getMessage(), null);
        }
    }
}
