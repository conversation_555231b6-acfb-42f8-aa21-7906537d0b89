package com.chevron.master.business.impl;

import cn.hutool.core.lang.Dict;
import com.chevron.master.business.WorkshopEditRecordBizService;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.dao.WorkshopEditRecordMapper;
import com.chevron.master.model.*;
import com.common.constants.Constants;
import com.common.exception.WxPltException;

import java.util.*;

import com.common.util.ContextUtil;
import com.sys.auth.model.WxTUser;
import com.sys.email.service.EmailSenderService;
import org.apache.poi.hssf.util.HSSFColor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.ContextLoader;
import tk.mybatis.mapper.util.StringUtil;

import javax.servlet.ServletContext;

/**
 * Workshop Edit Record业务接口实现类
 * <AUTHOR>
 * @version 1.0 2024-06-24 14:30
 */
@Service
public class WorkshopEditRecordBizServiceImpl implements WorkshopEditRecordBizService {
	@Autowired
	private WorkshopEditRecordMapper workshopEditRecordMapper;

	@Autowired
	WorkshopMasterBizService workshopMasterBizService;

	@Autowired
	EmailSenderService emailSenderService;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(WorkshopEditRecord record) throws WxPltException {
		//校验是否有已提交未审批的，若有，默认撤回，已本次提交为准
		WorkshopEditRecordExample example = new WorkshopEditRecordExample();
		example.createCriteria().andWorkshopIdEqualTo(record.getWorkshopId()).andStatusEqualTo(0);
		List<WorkshopEditRecord> werds = workshopEditRecordMapper.selectByExample(example);
		werds.forEach(werd->{
			//撤回
			werd.setStatus(2);
			werd.setUpdateTime(new Date());
			werd.setUpdateUserId(ContextUtil.getCurUserId());
			werd.setVersion(werd.getVersion()+1);
			werd.setApprover(ContextUtil.getCurUserId());
			workshopEditRecordMapper.updateByPrimaryKeySelective(werd);
		});

		record.setCreateUserId(ContextUtil.getCurUserId());
		record.setUpdateUserId(ContextUtil.getCurUserId());
		record.setCreateTime(new Date());
		record.setUpdateTime(new Date());
		record.setDeleteFlag(0);
		record.setVersion(0);
		record.setStatus(0);
		//获取经销商的flsr应当审批人，并发邮件通知 FLSR
		List<Map<String, Object>>  usermap = null;
		String flsrName = null;
		String flsrEmail = null;
		if(null != record.getPartnerId()){
			usermap = workshopEditRecordMapper.queryFlsrByPartner(record.getPartnerId(),record.getWorkshopId());
		}
		if(null != usermap && !usermap.isEmpty()){
			record.setApprover((Long)usermap.get(0).get("userId"));
			flsrName = usermap.get(0).get("name") == null ? null : usermap.get(0).get("name").toString();
			flsrEmail = usermap.get(0).get("email") == null ? null : usermap.get(0).get("email").toString();
		}
		workshopEditRecordMapper.insertSelective(record);
		//非管理员账号申请发送邮件通知
		if(null != flsrEmail && !"chevronadmin".equals(ContextUtil.getUsername())){
			sendEmailToFlsr(flsrName,flsrEmail);
		}

	}

	private void sendEmailToFlsr(String name, String email){
		//邮件通知flsr
		Dict dataMap = Dict.create();
		dataMap.put("honorific", "尊敬的 " + name + "：");
		StringBuffer message = new StringBuffer();
		message.append("您好！您有门店基础信息修改申请待处理。请登录<i><a style='color:#012169' href=\"https://www.cvx-sh.com\">雪佛龙合伙人</a></i>处理。<br/>");
		dataMap.put("message", message.toString());
		String subject = "门店信息修改申请提醒";
		List<String> acceptEmail = new ArrayList<String>();
		acceptEmail.add(email);
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		emailSenderService.sendEmailForCommon(context, acceptEmail.toArray(new String[acceptEmail.size()]), null,
				subject, dataMap, null, "notify_email.ftl");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(WorkshopEditRecord record) throws WxPltException {
		record.setUpdateUserId(ContextUtil.getCurUserId());
		record.setUpdateTime(new Date());
		record.setVersion(record.getVersion()+1);
		if(record.getStatus()==1){
			//实际审批人
			record.setApprover(ContextUtil.getCurUserId());
			record.setRemark(record.getUpdateTime()+":同意修改门店基本信息。");
			//同时更新门店信息
			final WorkshopMaster workshopMaster = new WorkshopMaster();
			workshopMaster.setId(record.getWorkshopId());
			if(!StringUtil.isEmpty(record.getNewName())){
				workshopMaster.setWorkshopName(record.getNewName());
			}
			if(null != record.getNewDsr()){
				workshopMaster.setExcuteUserId(record.getNewDsr());
			}
			if(!StringUtil.isEmpty(record.getNewContactPerson())){
				workshopMaster.setContactPerson(record.getNewContactPerson());
			}
			if(!StringUtil.isEmpty(record.getNewContactPersonTel())){
				workshopMaster.setContactPersonTel(record.getNewContactPersonTel());
			}
			workshopMasterBizService.update(workshopMaster);
		}
		workshopEditRecordMapper.updateByPrimaryKeySelective(record);
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		WorkshopEditRecordExample example = new WorkshopEditRecordExample();
		example.createCriteria().andIdIn(ids);
		workshopEditRecordMapper.deleteByExample(example);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(WorkshopEditRecordExample example) throws WxPltException {
		workshopEditRecordMapper.deleteByExample(example);
	}

	@Override
	public List<WorkshopEditRecord> queryByExample(WorkshopEditRecordExample example) throws WxPltException {
		return workshopEditRecordMapper.selectByExample(example);
	}

	@Override
    public List<WorkshopEditRecord> queryByParams(Map<String, Object> params) throws WxPltException {
    	return workshopEditRecordMapper.queryByParams(params);
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateForEditPage(WorkshopEditRecord record) throws WxPltException {
		workshopEditRecordMapper.updateForEditPage(record);
	}
	
	@Override
	public WorkshopEditRecord getBean(Long id) throws WxPltException{
		return workshopEditRecordMapper.selectByPrimaryKey(id);
	}

	public void queryForPage(WorkshopEditRecordParams params, Map<String, Object> resultMap) throws WxPltException{
		if (params.getLimit()==0){
			params.setLimit(10);
		}
		params.setField("status asc,id ");
		params.setDirection("DESC");
		resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public List<WorkshopEditRecord> queryForPage(WorkshopEditRecordParams params) throws WxPltException {
		return workshopEditRecordMapper.queryForPage(params);
	}
}
