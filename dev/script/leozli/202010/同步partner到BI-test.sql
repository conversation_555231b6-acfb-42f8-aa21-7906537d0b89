truncate table pp_mid.dbo.bit_partner
insert into pp_mid.dbo.bit_partner (PARTNER_ID,CUSTOMER_CODE,PARTNER_NAME_CN,CUSTOMER_BUSINESS_PROVINCE,CUSTOMER_BUSINESS_CITY,
LGCL_DEL_FG,IMPORT_TIME,CREATE_TIME,UPDATE_USER,DISTRIBUTOR_ID)
select pe.partner_id, pe.sap_code, o.organization_name, 
(select substring((
 SELECT ';'+xx0.province_name FROM (select distinct r3.id province_id, r3.region_name province_name 
 from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) CUSTOMER_BUSINESS_PROVINCE,
(select substring((
 SELECT ';'+xx0.province_name + '-' + xx0.region_name 
	FROM (select distinct r3.id province_id, r3.region_name province_name, r2.id city_id, r2.region_name 
		from wx_t_region_partner rp 
		join wx_t_region r1 on r1.id=rp.region_id
		join wx_t_region r2 on r2.id=r1.parent_id
		join wx_t_region r3 on r3.id=r2.parent_id
		where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) CUSTOMER_BUSINESS_CITY,
case when status=1 then 'N' else 'Y' end del_fg, getdate() import_time, o.create_time, 
(select ch_name from wx_t_user u where u.user_id=o.updator), pe.distributor_id from wx_t_partner_o2o_enterprise pe
left join wx_t_organization o on o.id=pe.partner_id
where len(pe.sap_code)>0 and o.create_time is not null and pe.distributor_id>0


select pe.distributor_id,* 
update p set p.DISTRIBUTOR_ID=pe.distributor_id, IMPORT_TIME=getdate()
from pp_mid.dbo.bit_partner p left join wx_t_partner_o2o_enterprise pe on p.partner_id=pe.partner_id