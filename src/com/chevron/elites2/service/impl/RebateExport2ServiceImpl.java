package com.chevron.elites2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.chevron.elites.dao.ManagerUserMapper;
import com.chevron.elites.model.vo.ManagerVo;
import com.chevron.elites2.constant.ElitesConstant;
import com.chevron.elites2.dao.*;
import com.chevron.elites2.model.DwCustomerRegionSalesSupervisorRel;
import com.chevron.elites2.model.DwPpCustomerCompanyFundQuarter;
import com.chevron.elites2.model.WXTRebateRelated;
import com.chevron.elites2.model.WxTRebateApply2;
import com.chevron.elites2.model.excel.*;
import com.chevron.elites2.model.fund.RebateFund;
import com.chevron.elites2.model.vo.OrgRebate;
import com.chevron.elites2.service.RebateExport2Service;
import com.chevron.elites2.service.RebateFund2Service;
import com.chevron.elites2.util.ExportUtil;
import com.chevron.elites2.util.RebateUtil;
import com.chevron.exportdata.ExportExcel;
import com.common.util.DateUtil;
import com.common.util.EmptyChecker;
import com.google.common.base.Joiner;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import jxl.Workbook;
import jxl.write.WritableWorkbook;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ContextLoader;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.ServletContext;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class RebateExport2ServiceImpl implements RebateExport2Service {

    public final static String[] summaryFields = {"cioQ1Q2EarnBdFund", "cioQ3Q4EarnBdFund",
            "cioTotalEarnBdFund", "cioQ1Q2EarnMktFund", "cioQ3Q4EarnMktFund", "cioTotalEarnMktFund",
            "cioQ1Q2EarnIviFund", "cioQ3Q4EarnIviFund", "cioTotalEarnIviFund", "cdmQ1Q2EarnMktFund",
            "cdmQ3Q4EarnMktFund", "cdmTotalEarnMktFund", "cdmQ1Q2EarnIviFund", "cdmQ3Q4EarnIviFund",
            "cdmTotalEarnIviFund", "totalEarnFund", "cioQ1Q2ReimbursementBdFund", "cioQ3Q4ReimbursementBdFund",
            "cioTotalReimbursementBdFund", "cioQ1Q2ReimbursementMktFund", "cioQ3Q4ReimbursementMktFund",
            "cioTotalReimbursementMktFund", "cioQ1Q2ReimbursementIviFund", "cioQ3Q4ReimbursementIviFund",
            "cioTotalReimbursementIviFund", "cdmQ1Q2ReimbursementMktFund", "cdmQ3Q4ReimbursementMktFund",
            "cdmTotalReimbursementMktFund", "cdmQ1Q2ReimbursementIviFund", "cdmQ3Q4ReimbursementIviFund",
            "cdmTotalReimbursementIviFund", "totalReimbursementFund", "cioTotalBalanceBdFund", "cioTotalBalanceMktFund",
            "cioTotalBalanceIviFund", "cdmTotalBalanceMktFund", "cdmTotalBalanceIviFund", "totalBalanceFund"};
    private static Logger logger = Logger.getLogger(RebateExport2ServiceImpl.class);
    private static DecimalFormat moneyTextFormat = new DecimalFormat("0.00");
    @Autowired
    private WxTRebateApply2Mapper rebateApplyDao;
    @Autowired
    private WXTRebateRelated2Mapper rebateRelatedDao;
    @Autowired
    private OrgRebate2Mapper orgRebate2Mapper;
    @Autowired
    private ManagerUserMapper managerUserMapper;
    @Autowired
    private DwCustomerRegionSalesSupervisorRelV2Mapper dwCustomerRegionSalesSupervisorRelDao;
    @Autowired
    private DwPpCustomerFundQuarterNewMapper dwPpCustomerFundQuarterNewMapper;

    @Autowired
    private DwPpCustomerCompanyFundQuarterMapper dwPpCustomerCompanyFundQuarterMapper;

    @Autowired
    private ExportExcel exportExcelService;
    @Autowired
    private RebateFund2Service rebateFund2Service;

    @Autowired
    private DicService dicService;

    @Override
    public Map<String, Object> exportAuditDetailExcel(List<?> lst, String clazzName, String sheetName, String tmpStorePath,
                                                      String fileName, ServletContext servletContext) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        OutputStream outputStream = null;
        try {

            WritableWorkbook workbook = null;
            File file = new File(servletContext.getRealPath(tmpStorePath) + File.separator + fileName + ".xls");
            outputStream = new FileOutputStream(file);

            workbook = Workbook.createWorkbook(outputStream);
            //创建数据
            if (!EmptyChecker.isEmpty(lst)) {
                workbook = ExportUtil.createExcelData(workbook, lst, clazzName, sheetName);
            }
            workbook.write();
            workbook.close();
            outputStream.flush();
            outputStream.close();
            String dirPath = servletContext.getRealPath(tmpStorePath) + File.separator;
            File dirFile = new File(dirPath);
            String[] filelist = dirFile.list();
            List<String> fileNameLst = new ArrayList<String>();
            for (int i = 0; i < filelist.length; i++) {
                File readfile = new File(dirPath + filelist[i]);
                if (!readfile.isDirectory()) {
                    logger.info("absolutepath=" + readfile.getAbsolutePath() + ", name=" + readfile.getName());
                    fileNameLst.add(dirPath + readfile.getName());
                }
            }
            //附件集合
            File[] attfiles = null;
            if (null != fileNameLst && !fileNameLst.isEmpty()) {
                attfiles = new File[fileNameLst.size()];
                int i = 0;
                for (String tempfileName : fileNameLst) {
                    attfiles[i] = new File(tempfileName);
                    i++;
                }
            }
            resultMap.put("attfiles", attfiles);

        } catch (Exception e) {
            resultMap.put("code", "error");
            resultMap.put("errorMsg", "错误信息：" + e.getMessage());
            logger.error(e.getMessage(), e);
            return resultMap;
        }
        resultMap.put("code", "success");
        return resultMap;
    }

    @Override
    public File exportFinalExcel(Long auditId) {
        List<WXTRebateRelated> rebateRelatedList = rebateRelatedDao.getListByAuditId(auditId);
        List<RebateFinalExcel> lstExportData = new ArrayList<RebateFinalExcel>();
        // 组装EXCEL数据
        for (WXTRebateRelated rebateRelated : rebateRelatedList) {
            RebateFinalExcel vo = new RebateFinalExcel();
            WxTRebateApply2 rebateApply = rebateApplyDao.selectByPrimaryKey(rebateRelated.getRebateId());
            vo.setOrganizationNameCN(rebateApply.getOrganizationName());
            vo.setOrganizationNameEn(
                    dwCustomerRegionSalesSupervisorRelDao.getCustomerEnName(rebateApply.getOrganizationName()));
            vo.setSalesName(rebateApply.getApplyPersonName());
            vo.setProductName("润滑油产品");
            vo.setReimbursedRebateVAT("16%");

            lstExportData.add(vo);
        }

        return this.getExportExcel(lstExportData);
    }

    /**
     * 处理导出的附件
     *
     * @param lstExportData
     * @return
     */
    private File getExportExcel(List<RebateFinalExcel> lstExportData) {

        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();

        // 组装文件路径
        String rebateFileRootStr = "elites_rebate_file" + File.separator;
        String rebateFileRootPath = context.getRealPath(rebateFileRootStr) + File.separator;
        File rebateFileRootDir = new File(rebateFileRootPath);
        if (!rebateFileRootDir.exists() && !rebateFileRootDir.isDirectory()) {
            rebateFileRootDir.mkdir();
        }

        String fileName = "Fund_Reimbursement_Summary_" + System.currentTimeMillis();
        String filePath = rebateFileRootStr + fileName;
        String filePathDir = context.getRealPath(filePath) + File.separator;
        File rebateFile = new File(filePathDir);
        if (!rebateFile.exists() && !rebateFile.isDirectory()) {
            rebateFile.mkdir();
        }

        Map<String, Object> returnMap = exportExcelService.exportDataForCommonAndCreateFile(lstExportData,
                "com.chevron.elites2.model.excel.RebateFinalExcel", "Refund for Approval", filePath, fileName, context);
        File[] attfiles = (File[]) returnMap.get("attfiles");
        return (attfiles != null && attfiles.length > 0) ? attfiles[0] : null;
    }

    @Override
    public File exportDetailExcel(Long auditId) {
        List<WXTRebateRelated> rebateRelatedList = rebateRelatedDao.getListByAuditId(auditId);
        List<RebateDetailExcel> lstExportData = new ArrayList<RebateDetailExcel>();
        //组装EXCEL数据
        for (WXTRebateRelated rebateRelated : rebateRelatedList) {
            RebateDetailExcel vo = new RebateDetailExcel();
            WxTRebateApply2 rebateApply = rebateApplyDao.selectByPrimaryKey(rebateRelated.getRebateId());
            Long orgId = rebateApply.getOrganizationId();
            if (checkListSameOrg(orgId, lstExportData)) {
                continue;
            }
            vo.setOrgId(orgId);
            List<DwCustomerRegionSalesSupervisorRel> customerList = dwCustomerRegionSalesSupervisorRelDao.getCustomerInfoById(orgId);
            // 获取经销商名称
            vo.setOrganizationNameCN(customerList.get(0).getCustomerNameCn());
            // 获取销售信息
            for (DwCustomerRegionSalesSupervisorRel customer : customerList) {
                if (customer.getRegionName().contains("CDM")) {
                    vo.setCdmRegion(customer.getRegionName());
                    vo.setCdmSales(customer.getSalesName());
                }
                if (customer.getRegionName().contains("C&I")) {
                    vo.setCioRegion(customer.getRegionName());
                    vo.setCioSales(customer.getSalesName());
                }
            }
            //获取基金信息
            rebateFund2Service.getFundDetailExcel(vo, orgId);
            lstExportData.add(vo);
        }
        return exportDetailExcelFile(lstExportData);
    }

    /**
     * 检查列表里是否有重复企业名
     *
     * @param orgId
     * @param lstExportData
     * @return
     */
    private Boolean checkListSameOrg(Long orgId, List<RebateDetailExcel> lstExportData) {
        for (RebateDetailExcel data : lstExportData) {
            if (orgId.equals(data.getOrgId())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<Long, DwCustomerRegionSalesSupervisorRel> getManagedOrgList(String permissionCode, WxTUser user, String channel, Integer isEliteCustomer) {
        Map<Long, DwCustomerRegionSalesSupervisorRel> orgList = new HashMap<Long, DwCustomerRegionSalesSupervisorRel>(16);
        List<DwCustomerRegionSalesSupervisorRel> list = null;
        if (user != null && permissionCode != null) {
            Example example = new Example(DwCustomerRegionSalesSupervisorRel.class);
            if (StrUtil.equalsIgnoreCase(permissionCode, "flsrAudit")) {//FLSR
                example.createCriteria().andEqualTo("salesCai", user.getCai());
                list = dwCustomerRegionSalesSupervisorRelDao.selectByExample(example);
            } else if (StrUtil.equalsAnyIgnoreCase(permissionCode, "asmAudit", "flsrAudit,asmAudit", "areaAudit")) {//ASM
                example.createCriteria().andEqualTo("suppervisorCai", user.getCai());
                list = dwCustomerRegionSalesSupervisorRelDao.selectByExample(example);
            } else if (StrUtil.equalsIgnoreCase(permissionCode, "channelAudit")) {//CM
                //list = dwCustomerRegionSalesSupervisorRelDao.getCustomerListByChannelManger(channel, null);
                list = dwCustomerRegionSalesSupervisorRelDao.getCustomerListByChannelMangerCai(user.getCai());
            } else if (StrUtil.equalsIgnoreCase(permissionCode, "bsNorth")) {//BS
                example.createCriteria().andEqualTo("regionName", "C&I-North")
                        .orEqualTo("regionName", "C&I-NE&NW")
                        .orEqualTo("regionName", "CDM-North")
                        .orEqualTo("regionName", "CDM-NE&NW");
                list = dwCustomerRegionSalesSupervisorRelDao.selectByExample(example);
            } else if (StrUtil.equalsIgnoreCase(permissionCode, "bsSouth")) {//BS
                example.createCriteria().andEqualTo("regionName", "C&I-South")
                        .orEqualTo("regionName", "CDM-South");
                list = dwCustomerRegionSalesSupervisorRelDao.selectByExample(example);
            } else if (StrUtil.equalsIgnoreCase(permissionCode, "bsSW")) {//BS
                example.createCriteria().andEqualTo("regionName", "C&I-SW")
                        .orEqualTo("regionName", "CDM-SW");
                example.orderBy("customerNameCn");
                list = dwCustomerRegionSalesSupervisorRelDao.selectByExample(example);
            } else if (StrUtil.equalsAnyIgnoreCase(permissionCode, "bsmAudit", "sdoAudit", "abmAudit") || user.getUserId() == 1L) {
                orgList = dwCustomerRegionSalesSupervisorRelDao.getCustomerList();
            } else if (StrUtil.equalsIgnoreCase(permissionCode, "partner")) {

            }
            if (CollectionUtil.isNotEmpty(list)) {
                //TODO:liyu:通过一条查询优化，目前在内存中交集
                if (isEliteCustomer != null && isEliteCustomer == 1) {
                    List<Long> eliteCustomerIds = dwPpCustomerFundQuarterNewMapper.getCustomerDistributorIdList();
                    for (DwCustomerRegionSalesSupervisorRel item : list) {
                        for (Long customerId : eliteCustomerIds) {
                            if (item.getDistributorId().equals(customerId)) {
                                orgList.put(item.getDistributorId(), item);
                            }
                        }
                    }
                } else {
                    for (DwCustomerRegionSalesSupervisorRel item : list) {
                        orgList.put(item.getDistributorId(), item);
                    }
                }
            }
        }
        return orgList;
    }

    @Override
    public File exportAllDetailExcel(List<Long> orgIdList) {
        List<RebateDetailExcel> lstExportData = new ArrayList<RebateDetailExcel>();
        for (Long orgId : orgIdList) {
            RebateDetailExcel vo = exportOneOrgDetailExcel(orgId);
            if (vo == null) {
                continue;
            }
            lstExportData.add(vo);
        }
        return exportDetailExcelFile(lstExportData);
    }

    @Override
    public RebateDetailExcel exportOneOrgDetailExcel(Long orgId) {
        RebateDetailExcel vo = new RebateDetailExcel();
        vo.setOrgId(orgId);
        List<DwCustomerRegionSalesSupervisorRel> customerList = dwCustomerRegionSalesSupervisorRelDao.getCustomerInfoById(orgId);
        if (CollectionUtil.isEmpty(customerList)) {
            return null;
        }
        // 获取经销商名称
        String orgName = customerList.get(0).getCustomerNameCn();
        vo.setOrganizationNameCN(orgName);
        // 获取销售信息
        for (DwCustomerRegionSalesSupervisorRel customer : customerList) {
            if (customer.getRegionName().contains("CDM")) {
                vo.setCdmRegion(customer.getRegionName());
                vo.setCdmSales(customer.getSalesName());
            }
            if (customer.getRegionName().contains("C&I")) {
                vo.setCioRegion(customer.getRegionName());
                vo.setCioSales(customer.getSalesName());
            }
        }
        //获取基金信息
        rebateFund2Service.getFundDetailExcel(vo, orgId);
        //获取备注信息
        List<String> remarkList = rebateApplyDao.getExportRemarkByOrgId(orgId);
        String remarkStr = Joiner.on(",").skipNulls().join(remarkList);
        vo.setRemark(remarkStr);
        return vo;
    }

    /**
     * 生成导出rebate detail文件
     *
     * @return
     */
    @Override
    public File exportDetailExcelFile(List<RebateDetailExcel> exportData) {//TODO
        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        // 组装文件路径
        String rebateFileRootStr = "elites_rebate_file" + File.separator;
        String rebateFileRootPath = context.getRealPath(rebateFileRootStr) + File.separator;
        File rebateFileRootDir = new File(rebateFileRootPath);
        if (!rebateFileRootDir.exists() && !rebateFileRootDir.isDirectory()) {
            rebateFileRootDir.mkdir();
        }

        String fileName = "Rebate_Fund_Detail_" + DateUtil.toDateStrNew(new Date(), 9);
        String filePath = rebateFileRootStr + fileName;
        String filePathDir = context.getRealPath(filePath) + File.separator;
        File rebateFile = new File(filePathDir);
        if (!rebateFile.exists() && !rebateFile.isDirectory()) {
            rebateFile.mkdir();
        }
        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("UTF-8");
        configuration.setServletContextForTemplateLoading(context, "/WEB-INF/templates");
        Template sheet1 = null;
        try {
            sheet1 = configuration.getTemplate("Rebate_Fund_Detail.ftl");
        } catch (IOException e1) {
            e1.printStackTrace();
            logger.error(e1.getMessage(), e1);
            throw new RuntimeException(e1);
        }
        //计算合计
        Map<String, Double> total = new HashMap<String, Double>(summaryFields.length);
        Map<String, Method> properties = new HashMap<String, Method>(summaryFields.length);
        Set<String> fields = new HashSet<String>(summaryFields.length);
        for (String field : summaryFields) {
            if (fields.contains(field)) {
                throw new RuntimeException("合计属性" + field + "已存在");
            }
            fields.add(field);
        }
        try {
            for (PropertyDescriptor propertyDescriptor :
                    Introspector.getBeanInfo(RebateDetailExcel.class).getPropertyDescriptors()) {
                if (fields.contains(propertyDescriptor.getName()) && propertyDescriptor.getReadMethod() != null) {
                    properties.put(propertyDescriptor.getName(), propertyDescriptor.getReadMethod());
                }
            }
        } catch (IntrospectionException e1) {
            e1.printStackTrace();
        }
        if (properties.size() < summaryFields.length) {
            StringBuilder sBuilder = null;
            for (String field : summaryFields) {
                if (!properties.containsKey(field)) {
                    if (sBuilder == null) {
                        sBuilder = new StringBuilder();
                    } else {
                        sBuilder.append(",");
                    }
                    sBuilder.append(field);
                }
            }
            throw new RuntimeException("属性" + sBuilder.toString() + "未找到获取属性方法");
        }
        for (RebateDetailExcel item : exportData) {
            for (String key : properties.keySet()) {
                try {
                    Object value = properties.get(key).invoke(item);
                    if (value != null) {
                        if (total.containsKey(key)) {
                            total.put(key, total.get(key) + Double.parseDouble(value.toString().replaceAll(",", "")));
                        } else {
                            total.put(key, Double.parseDouble(value.toString().replaceAll(",", "")));
                        }
                    }
                } catch (Exception e) {
                    logger.error("获取属性" + key + "失败", e);
                    throw new RuntimeException("获取属性" + key + "失败", e);
                }
            }
        }
        File file = new File(context.getRealPath(filePath) + File.separator + fileName + ".xls");
        OutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            Map<String, Object> rootMap = new HashMap<String, Object>(5);
            rootMap.put("dataLists", exportData);
            rootMap.put("total", total);
            rootMap.put("dataLength", exportData.size());
            sheet1.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            throw new RuntimeException("生成Excel失败", e);
        } finally {
            IoUtil.close(outputStream);
        }

        return file;
//        Map<String, Object> returnMap = exportAuditDetailExcel(lstExportData,
//                "com.chevron.elites2.model.excel.RebateDetailExcel", "Refund Summary", filePath, fileName, context);
//        File[] attfiles = (File[]) returnMap.get("attfiles");
//        return (attfiles != null && attfiles.length > 0) ? attfiles[0] : null;
    }

    private HashMap<String, List<String>> getSalesList(Long orgId) {
        List<ManagerVo> sales = managerUserMapper.getSalesListByOrgAndChannel(orgId, null);
        List<String> salesList = new ArrayList<String>();
        List<String> salesListCdm = new ArrayList<String>();
        List<String> salesListCio = new ArrayList<String>();
        for (ManagerVo user : sales) {
            if (user != null && user.getManagerName() != null) {
                salesList.add(user.getManagerName());
                if (StrUtil.startWith(user.getRegionName(), "CDM")) {
                    salesListCdm.add(user.getManagerName());
                } else if (StrUtil.startWith(user.getRegionName(), "C&I")) {
                    salesListCio.add(user.getManagerName());
                }
            }
        }
        HashMap<String, List<String>> map = CollUtil.newHashMap(3);
        map.put("ALL", salesList);
        map.put("CDM", salesListCdm);
        map.put("CIO", salesListCio);
        return map;
    }

    /**
     * 导出销售概况表
     *
     * @param orgIdList
     * @return
     */
    @Override
    public File exportSalesMailExcel(List<Long> orgIdList) {//TODO
        List<RebateFinalExcel> excelList = new ArrayList<RebateFinalExcel>();
        List<RebateSalesExcel> excelListCdm = new ArrayList<RebateSalesExcel>();
        List<RebateSalesExcel> excelListCio = new ArrayList<RebateSalesExcel>();
        RebateFinalExcelForSales excelData = null;
        RebateSalesExcel rebateSalesExcel = null;
        BigDecimal sumWithoutTax = BigDecimal.ZERO;
        BigDecimal sumCdmWithoutTax = BigDecimal.ZERO;
        BigDecimal sumCioWithoutTax = BigDecimal.ZERO;
        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal sumCdm = BigDecimal.ZERO;
        BigDecimal sumCio = BigDecimal.ZERO;

        for (Long orgId : orgIdList) {
            HashMap<String, List<String>> salesMap = getSalesList(orgId);
            String sales = Joiner.on(',').skipNulls().join(salesMap.get("ALL"));
            if (EmptyChecker.isEmpty(sales)) {
                continue;
            }


            excelData = new RebateFinalExcelForSales();
            OrgRebate orgRebate = orgRebate2Mapper.getOrgNameById(orgId);
            RebateFund fund = rebateFund2Service.getFundForOrg(orgId, null, 2, null);
            rebateFund2Service.getFinalExcelForSales(excelData, fund);
            BigDecimal cioBdQ1Used = RebateUtil.getNullAsZero(fund.getCioBd().getQ1UsedFund());
            BigDecimal cioBdQ2Used = RebateUtil.getNullAsZero(fund.getCioBd().getQ2UsedFund());
            BigDecimal cioBdQ3Q4Used = RebateUtil.getNullAsZero(fund.getCioBd().getQ3q4UsedFund());
            BigDecimal cioMktQ1Used = RebateUtil.getNullAsZero(fund.getCioMkt().getQ1UsedFund());
            BigDecimal cioMktQ2Used = RebateUtil.getNullAsZero(fund.getCioMkt().getQ2UsedFund());
            BigDecimal cioMktQ3Q4Used = RebateUtil.getNullAsZero(fund.getCioMkt().getQ3q4UsedFund());
            BigDecimal cioIviQ1Used = RebateUtil.getNullAsZero(fund.getCioIvi().getQ1UsedFund());
            BigDecimal cioIviQ2Used = RebateUtil.getNullAsZero(fund.getCioIvi().getQ2UsedFund());
            BigDecimal cioIviQ3Q4Used = RebateUtil.getNullAsZero(fund.getCioIvi().getQ3q4UsedFund());
            BigDecimal cdmMktQ1Used = RebateUtil.getNullAsZero(fund.getCdmMkt().getQ1UsedFund());
            BigDecimal cdmMktQ2Used = RebateUtil.getNullAsZero(fund.getCdmMkt().getQ2UsedFund());
            BigDecimal cdmMktQ3Q4Used = RebateUtil.getNullAsZero(fund.getCdmMkt().getQ3q4UsedFund());
            BigDecimal cdmIviQ1Used = RebateUtil.getNullAsZero(fund.getCdmIvi().getQ1UsedFund());
            BigDecimal cdmIviQ2Used = RebateUtil.getNullAsZero(fund.getCdmIvi().getQ2UsedFund());
            BigDecimal cdmIviQ3Q4Used = RebateUtil.getNullAsZero(fund.getCdmIvi().getQ3q4UsedFund());

            //Q1
            RebateFinalExcel highTaxFinalExcelRow = new RebateFinalExcel();
            highTaxFinalExcelRow.setOrganizationNameCN(orgRebate.getOrgName());
            highTaxFinalExcelRow.setOrganizationNameEn(orgRebate.getOrgEnName());
            highTaxFinalExcelRow.setSalesName(sales);
            highTaxFinalExcelRow.setProductName("润滑油产品");
            highTaxFinalExcelRow.setMonth("Q1");
            highTaxFinalExcelRow.setReimbursedRebate(excelData.getAmountWithoutHighTax());//不含税金额
            highTaxFinalExcelRow.setReimbursedRebateVAT(excelData.getHighTaxRate());//税率
            highTaxFinalExcelRow.setRefundAmountWithTax(excelData.getAmountWithHighTax());//含税金额
            excelList.add(highTaxFinalExcelRow);

            sumWithoutTax = sumWithoutTax.add(RebateUtil.parseDecimal(excelData.getAmountWithoutHighTax()));
            sum = sum.add(RebateUtil.parseDecimal(excelData.getAmountWithHighTax()));

            //Q234
            RebateFinalExcel lowTaxfinalExcelRow = new RebateFinalExcel();
            lowTaxfinalExcelRow.setOrganizationNameCN(orgRebate.getOrgName());
            lowTaxfinalExcelRow.setOrganizationNameEn(orgRebate.getOrgEnName());
            lowTaxfinalExcelRow.setSalesName(sales);
            lowTaxfinalExcelRow.setProductName("润滑油产品");
            lowTaxfinalExcelRow.setMonth("Q2");
            lowTaxfinalExcelRow.setReimbursedRebate(excelData.getAmountWithoutLowTax());
            lowTaxfinalExcelRow.setReimbursedRebateVAT(excelData.getLowTaxRate());
            lowTaxfinalExcelRow.setRefundAmountWithTax(excelData.getAmountWithLowTax());
            excelList.add(lowTaxfinalExcelRow);

            sumWithoutTax = sumWithoutTax.add(RebateUtil.parseDecimal(excelData.getAmountWithoutLowTax()));
            sum = sum.add(RebateUtil.parseDecimal(excelData.getAmountWithLowTax()));

            //CDM Sheet
            BigDecimal amountWithTaxQ1 = RebateUtil.formatDecimal(cdmMktQ1Used.add(cdmIviQ1Used));
            BigDecimal amountWithTaxQ234 = RebateUtil.formatDecimal(cdmMktQ2Used.add(cdmMktQ3Q4Used)
                    .add(cdmIviQ2Used).add(cdmIviQ3Q4Used));
            Map<String, DwPpCustomerCompanyFundQuarter> companyFundListQ1 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(orgId, "CDM",
                    CollUtil.newArrayList("Q1"));
            Map<String, DwPpCustomerCompanyFundQuarter> companyFundListQ234 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(orgId, "CDM",
                    CollUtil.newArrayList("Q2", "Q3", "Q4"));
            BigDecimal mktRate, bdRate, iviRate;

            for (String companyCode : companyFundListQ1.keySet()) {
                rebateSalesExcel = new RebateSalesExcel();
                rebateSalesExcel.setOrganizationNameCN(orgRebate.getOrgName());
                rebateSalesExcel.setOrganizationNameEn(orgRebate.getOrgEnName());
                rebateSalesExcel.setSalesName(Joiner.on(',').skipNulls().join(salesMap.get("CDM")));
                rebateSalesExcel.setProductName("润滑油产品");
                rebateSalesExcel.setMonth("Q1");
                rebateSalesExcel.setCompanyCode(companyCode);

                //rebateSalesExcel.setReimbursedRebateVAT(excelData.getHighTaxRate()); //税率
                rebateSalesExcel.setReimbursedRebateVATCc(excelData.getHighTaxRate()); //税率
                //rebateSalesExcel.setReimbursedRebate(RebateUtil.format(RebateUtil.div(amountWithTaxQ1, ElitesConstant.Q1_TAX_RATE, 2)));//Q1;//不含税金额
                //rebateSalesExcel.setRefundAmountWithTax(RebateUtil.format(amountWithTaxQ1));//含税金额

                DicItemVo officeItem = getOfficeConfigFromDic(companyCode);
                rebateSalesExcel.setOfficeName(officeItem.getDicItemName());

                mktRate = RebateUtil.getNullAsZero(companyFundListQ1.get(companyCode).getMarketingFundRate());
                iviRate = RebateUtil.getNullAsZero(companyFundListQ1.get(companyCode).getIviFundRate());
                BigDecimal totalFundEarned = cdmMktQ1Used.multiply(mktRate).add(iviRate.multiply(cdmIviQ1Used));
                if (amountWithTaxQ1.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ1, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                rebateSalesExcel.setTotalFundEarned(RebateUtil.format(totalFundEarned));
                BigDecimal totalFundEarnedWithoutTax = RebateUtil.div(totalFundEarned, ElitesConstant.Q1_TAX_RATE, 2);
                rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(totalFundEarnedWithoutTax));
                excelListCdm.add(rebateSalesExcel);
                sumCdmWithoutTax = sumCdmWithoutTax.add(totalFundEarnedWithoutTax);
                sumCdm = sumCdm.add(totalFundEarned);
            }

            //CDM
            for (String companyCode : companyFundListQ234.keySet()) {
                rebateSalesExcel = new RebateSalesExcel();
                rebateSalesExcel.setOrganizationNameCN(orgRebate.getOrgName());
                rebateSalesExcel.setOrganizationNameEn(orgRebate.getOrgEnName());
                rebateSalesExcel.setSalesName(Joiner.on(',').skipNulls().join(salesMap.get("CDM")));
                rebateSalesExcel.setProductName("润滑油产品");
                rebateSalesExcel.setMonth("Q2");
                rebateSalesExcel.setCompanyCode(companyCode);

                //rebateSalesExcel.setReimbursedRebateVAT(excelData.getLowTaxRate()); //税率
                rebateSalesExcel.setReimbursedRebateVATCc(excelData.getLowTaxRate()); //税率
                //rebateSalesExcel.setReimbursedRebate(RebateUtil.format(RebateUtil.div(amountWithTaxQ234, ElitesConstant.TAX_RATE, 2)));//Q1;//不含税金额
                //rebateSalesExcel.setRefundAmountWithTax(RebateUtil.format(amountWithTaxQ234));//含税金额

                DicItemVo officeItem = getOfficeConfigFromDic(companyCode);
                rebateSalesExcel.setOfficeName(officeItem.getDicItemName());
                mktRate = RebateUtil.getNullAsZero(companyFundListQ234.get(companyCode).getMarketingFundRate());
                iviRate = RebateUtil.getNullAsZero(companyFundListQ234.get(companyCode).getIviFundRate());
                BigDecimal totalFundEarned = (cdmMktQ2Used.add(cdmMktQ3Q4Used)).multiply(mktRate)
                        .add(iviRate.multiply(cdmIviQ2Used.add(cdmIviQ3Q4Used)));
                if (amountWithTaxQ234.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ234, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                rebateSalesExcel.setTotalFundEarned(RebateUtil.format(totalFundEarned));
                BigDecimal totalFundEarnedWithoutTax = RebateUtil.div(totalFundEarned, ElitesConstant.TAX_RATE, 2);
                rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(totalFundEarnedWithoutTax));
                excelListCdm.add(rebateSalesExcel);
                sumCdmWithoutTax = sumCdmWithoutTax.add(totalFundEarnedWithoutTax);
                sumCdm = sumCdm.add(totalFundEarned);
            }

            //CIO Sheet
            amountWithTaxQ1 = RebateUtil.formatDecimal(cioBdQ1Used.add(cioMktQ1Used).add(cioIviQ1Used));
            amountWithTaxQ234 = RebateUtil.formatDecimal(cioBdQ2Used.add(cioBdQ3Q4Used)
                    .add(cioMktQ2Used).add(cioMktQ3Q4Used)
                    .add(cioIviQ2Used).add(cioIviQ3Q4Used));
            companyFundListQ1 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(orgId, "C&I",
                    CollUtil.newArrayList("Q1"));
            companyFundListQ234 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(orgId, "C&I",
                    CollUtil.newArrayList("Q2", "Q3", "Q4"));

            for (String companyCode : companyFundListQ1.keySet()) {
                rebateSalesExcel = new RebateSalesExcel();
                rebateSalesExcel.setOrganizationNameCN(orgRebate.getOrgName());
                rebateSalesExcel.setOrganizationNameEn(orgRebate.getOrgEnName());
                rebateSalesExcel.setSalesName(Joiner.on(',').skipNulls().join(salesMap.get("CIO")));
                rebateSalesExcel.setProductName("润滑油产品");
                rebateSalesExcel.setMonth("Q1");
                rebateSalesExcel.setCompanyCode(companyCode);

                //rebateSalesExcel.setReimbursedRebateVAT(excelData.getHighTaxRate()); //税率
                rebateSalesExcel.setReimbursedRebateVATCc(excelData.getHighTaxRate()); //税率
                //rebateSalesExcel.setReimbursedRebate(RebateUtil.format(RebateUtil.div(amountWithTaxQ1, ElitesConstant.Q1_TAX_RATE, 2)));//Q1;//不含税金额
                //rebateSalesExcel.setRefundAmountWithTax(RebateUtil.format(amountWithTaxQ1));//含税金额

                DicItemVo officeItem = getOfficeConfigFromDic(companyCode);
                rebateSalesExcel.setOfficeName(officeItem.getDicItemName());
                mktRate = RebateUtil.getNullAsZero(companyFundListQ1.get(companyCode).getMarketingFundRate());
                bdRate = RebateUtil.getNullAsZero(companyFundListQ1.get(companyCode).getBdFundRate());
                iviRate = RebateUtil.getNullAsZero(companyFundListQ1.get(companyCode).getIviFundRate());
                BigDecimal totalFundEarned = cioMktQ1Used.multiply(mktRate)
                        .add(iviRate.multiply(cioIviQ1Used))
                        .add(cioBdQ1Used.multiply(bdRate));
                if (amountWithTaxQ1.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ1, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                rebateSalesExcel.setTotalFundEarned(RebateUtil.format(totalFundEarned));
                BigDecimal totalFundEarnedWithoutTax = RebateUtil.div(totalFundEarned, ElitesConstant.Q1_TAX_RATE, 2);
                rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(totalFundEarnedWithoutTax));
                excelListCio.add(rebateSalesExcel);
                sumCioWithoutTax = sumCioWithoutTax.add(totalFundEarnedWithoutTax);
                sumCio = sumCio.add(totalFundEarned);
            }

            for (String companyCode : companyFundListQ234.keySet()) {
                rebateSalesExcel = new RebateSalesExcel();
                rebateSalesExcel.setOrganizationNameCN(orgRebate.getOrgName());
                rebateSalesExcel.setOrganizationNameEn(orgRebate.getOrgEnName());
                rebateSalesExcel.setSalesName(Joiner.on(',').skipNulls().join(salesMap.get("CIO")));
                rebateSalesExcel.setProductName("润滑油产品");
                rebateSalesExcel.setMonth("Q2");
                rebateSalesExcel.setCompanyCode(companyCode);

                //rebateSalesExcel.setReimbursedRebateVAT(excelData.getLowTaxRate()); //税率
                rebateSalesExcel.setReimbursedRebateVATCc(excelData.getLowTaxRate()); //税率
                //rebateSalesExcel.setReimbursedRebate(RebateUtil.format(RebateUtil.div(amountWithTaxQ234, ElitesConstant.TAX_RATE, 4)));//Q1;//不含税金额
                //rebateSalesExcel.setRefundAmountWithTax(RebateUtil.format(amountWithTaxQ234));//含税金额

                DicItemVo officeItem = getOfficeConfigFromDic(companyCode);
                rebateSalesExcel.setOfficeName(officeItem.getDicItemName());
                mktRate = RebateUtil.getNullAsZero(companyFundListQ234.get(companyCode).getMarketingFundRate());
                bdRate = RebateUtil.getNullAsZero(companyFundListQ234.get(companyCode).getBdFundRate());
                iviRate = RebateUtil.getNullAsZero(companyFundListQ234.get(companyCode).getIviFundRate());
                BigDecimal totalFundEarned = (cioMktQ2Used.add(cioBdQ3Q4Used)).multiply(mktRate)
                        .add(iviRate.multiply(cioIviQ2Used.add(cioIviQ3Q4Used)))
                        .add((cioBdQ2Used.add(cioBdQ3Q4Used)).multiply(bdRate));
                if (amountWithTaxQ234.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ234, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                rebateSalesExcel.setTotalFundEarned(RebateUtil.format(totalFundEarned));
                BigDecimal totalFundEarnedWithoutTax = RebateUtil.div(totalFundEarned, ElitesConstant.TAX_RATE, 2);
                rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(totalFundEarnedWithoutTax));
                excelListCio.add(rebateSalesExcel);
                sumCioWithoutTax = sumCioWithoutTax.add(totalFundEarnedWithoutTax);
                sumCio = sumCio.add(totalFundEarned);
            }
        }

        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        //组装文件路径
        String rebateFileRootStr = "elites_rebate_file" + File.separator;
        String rebateFileRootPath = context.getRealPath(rebateFileRootStr) + File.separator;
        File rebateFileRootDir = new File(rebateFileRootPath);
        if (!rebateFileRootDir.exists() && !rebateFileRootDir.isDirectory()) {
            rebateFileRootDir.mkdir();
        }

        String fileName = "Fund_Reimbursement_Summary_" + DateUtil.toDateStrNew(new Date(), 9);
        String filePath = rebateFileRootStr + fileName;
        String filePathDir = context.getRealPath(filePath) + File.separator;
        File rebateFile = new File(filePathDir);
        if (!rebateFile.exists() && !rebateFile.isDirectory()) {
            rebateFile.mkdir();
        }

        List<ExcelSheet> sheets = new ArrayList<ExcelSheet>(1);
        RebateFinalExcel summaryExcelRow = new RebateFinalExcel();
        summaryExcelRow.setOrganizationNameCN("汇总");
        summaryExcelRow.setReimbursedRebate(RebateUtil.format(sumWithoutTax));
        summaryExcelRow.setRefundAmountWithTax(RebateUtil.format(sum));
        excelList.add(summaryExcelRow);
        sheets.add(new ExcelSheet<RebateFinalExcel>(excelList, "com.chevron.elites2.model.excel.RebateFinalExcel",
                "Fund Reimbursement Summary", 0,
                new String[]{"String", "String", "String", "String", "String", "THOUSANDS_FLOAT", "String", "THOUSANDS_FLOAT"}));
        //liyu:增加
        rebateSalesExcel = new RebateSalesExcel();
        rebateSalesExcel.setOrganizationNameCN("汇总");
        rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(sumCdmWithoutTax));
        rebateSalesExcel.setTotalFundEarned(RebateUtil.format(sumCdm));
        excelListCdm.add(rebateSalesExcel);
        sheets.add(new ExcelSheet<RebateSalesExcel>(excelListCdm, "com.chevron.elites2.model.excel.RebateSalesExcel",
                "CDM", 1,
                new String[]{"String", "String", "String", "String", "String", "String", "String", "String", "String", "THOUSANDS_FLOAT", "String", "THOUSANDS_FLOAT"}));

        rebateSalesExcel = new RebateSalesExcel();
        rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(sumCioWithoutTax));
        rebateSalesExcel.setTotalFundEarned(RebateUtil.format(sumCio));
        excelListCio.add(rebateSalesExcel);
        sheets.add(new ExcelSheet<RebateSalesExcel>(excelListCio, "com.chevron.elites2.model.excel.RebateSalesExcel", "C&I",
                2, new String[]{"String", "String", "String", "String", "String", "String", "String", "String", "String", "THOUSANDS_FLOAT", "String", "THOUSANDS_FLOAT"}));

        Map<String, Object> returnMap = ExportUtil.exportRebateExcel(sheets, filePath, fileName, context, null, 3);
        File[] attfiles = (File[]) returnMap.get("attfiles");

        return attfiles[0];
    }

    /**
     * 导出销售概况表
     *
     * @param orgIdList
     * @return
     */
    /*@Override
    public File exportSalesMailExcel(List<Long> orgIdList) {
        List<RebateFinalExcel> excelList = new ArrayList<RebateFinalExcel>();
        List<RebateSalesExcel> excelListCdm = new ArrayList<RebateSalesExcel>();
        List<RebateSalesExcel> excelListCio = new ArrayList<RebateSalesExcel>();
        RebateSalesExcel rebateSalesExcel = null;
        FundSumVo sumVo = new FundSumVo();


        Map<String, String> officeList = getOfficeMapFromDic();
        for (Long orgId : orgIdList) {
            exportOneSalesMailExcel(orgId, officeList, excelList, excelListCdm, excelListCio, sumVo);
        }

        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        //组装文件路径
        String rebateFileRootStr = "elites_rebate_file" + File.separator;
        String rebateFileRootPath = context.getRealPath(rebateFileRootStr) + File.separator;
        File rebateFileRootDir = new File(rebateFileRootPath);
        if (!rebateFileRootDir.exists() && !rebateFileRootDir.isDirectory()) {
            rebateFileRootDir.mkdir();
        }

        String fileName = "Fund_Reimbursement_Summary_" + DateUtil.toDateStrNew(new Date(), 9);
        String filePath = rebateFileRootStr + fileName;
        String filePathDir = context.getRealPath(filePath) + File.separator;
        File rebateFile = new File(filePathDir);
        if (!rebateFile.exists() && !rebateFile.isDirectory()) {
            rebateFile.mkdir();
        }

        List<ExcelSheet> sheets = new ArrayList<ExcelSheet>(1);
        RebateFinalExcel summaryExcelRow = new RebateFinalExcel();
        summaryExcelRow.setOrganizationNameCN("汇总");
        summaryExcelRow.setReimbursedRebate(RebateUtil.format(sumVo.getSumWithoutTax()));
        summaryExcelRow.setRefundAmountWithTax(RebateUtil.format(sumVo.getSum()));
        excelList.add(summaryExcelRow);
        sheets.add(new ExcelSheet<RebateFinalExcel>(excelList, "com.chevron.elites2.model.excel.RebateFinalExcel",
                "Fund Reimbursement Summary", 0,
                new String[]{"String", "String", "String", "String", "String", "THOUSANDS_FLOAT", "String", "THOUSANDS_FLOAT"}));
        //liyu:增加
        rebateSalesExcel = new RebateSalesExcel();
        rebateSalesExcel.setOrganizationNameCN("汇总");
        rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(sumVo.getSumCdmWithoutTax()));
        rebateSalesExcel.setTotalFundEarned(RebateUtil.format(sumVo.getSumCdm()));
        excelListCdm.add(rebateSalesExcel);
        sheets.add(new ExcelSheet<RebateSalesExcel>(excelListCdm, "com.chevron.elites2.model.excel.RebateSalesExcel",
                "CDM", 1,
                new String[]{"String", "String", "String", "String", "String", "String", "String", "String", "String", "THOUSANDS_FLOAT", "String", "THOUSANDS_FLOAT"}));

        rebateSalesExcel = new RebateSalesExcel();
        rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(sumVo.getSumCioWithoutTax()));
        rebateSalesExcel.setTotalFundEarned(RebateUtil.format(sumVo.getSumCio()));
        excelListCio.add(rebateSalesExcel);
        sheets.add(new ExcelSheet<RebateSalesExcel>(excelListCio, "com.chevron.elites2.model.excel.RebateSalesExcel", "C&I",
                2, new String[]{"String", "String", "String", "String", "String", "String", "String", "String", "String", "THOUSANDS_FLOAT", "String", "THOUSANDS_FLOAT"}));

        Map<String, Object> returnMap = ExportUtil.exportRebateExcel(sheets, filePath, fileName, context, null, 3);
        File[] attfiles = (File[]) returnMap.get("attfiles");

        return attfiles[0];
    }*/
    @Override
    public RebateSalesExcel getCompanyFundItem(Long orgId, String companyCode, String channel, String quarter,
                                               RebateFund fund, DwPpCustomerCompanyFundQuarter companyFund) {
        BigDecimal cioBdQ1Used = RebateUtil.getNullAsZero(fund.getCioBd().getQ1UsedFund());
        BigDecimal cioBdQ2Used = RebateUtil.getNullAsZero(fund.getCioBd().getQ2UsedFund());
        BigDecimal cioBdQ3Q4Used = RebateUtil.getNullAsZero(fund.getCioBd().getQ3q4UsedFund());
        BigDecimal cioMktQ1Used = RebateUtil.getNullAsZero(fund.getCioMkt().getQ1UsedFund());
        BigDecimal cioMktQ2Used = RebateUtil.getNullAsZero(fund.getCioMkt().getQ2UsedFund());
        BigDecimal cioMktQ3Q4Used = RebateUtil.getNullAsZero(fund.getCioMkt().getQ3q4UsedFund());
        BigDecimal cioIviQ1Used = RebateUtil.getNullAsZero(fund.getCioIvi().getQ1UsedFund());
        BigDecimal cioIviQ2Used = RebateUtil.getNullAsZero(fund.getCioIvi().getQ2UsedFund());
        BigDecimal cioIviQ3Q4Used = RebateUtil.getNullAsZero(fund.getCioIvi().getQ3q4UsedFund());
        BigDecimal cdmMktQ1Used = RebateUtil.getNullAsZero(fund.getCdmMkt().getQ1UsedFund());
        BigDecimal cdmMktQ2Used = RebateUtil.getNullAsZero(fund.getCdmMkt().getQ2UsedFund());
        BigDecimal cdmMktQ3Q4Used = RebateUtil.getNullAsZero(fund.getCdmMkt().getQ3q4UsedFund());
        BigDecimal cdmIviQ1Used = RebateUtil.getNullAsZero(fund.getCdmIvi().getQ1UsedFund());
        BigDecimal cdmIviQ2Used = RebateUtil.getNullAsZero(fund.getCdmIvi().getQ2UsedFund());
        BigDecimal cdmIviQ3Q4Used = RebateUtil.getNullAsZero(fund.getCdmIvi().getQ3q4UsedFund());

        OrgRebate orgRebate = orgRebate2Mapper.getOrgNameById(orgId);
        HashMap<String, List<String>> salesMap = getSalesList(orgId);

        BigDecimal amountWithTaxQ1 = BigDecimal.ZERO;
        BigDecimal amountWithTaxQ234 = BigDecimal.ZERO;

        if (StrUtil.equalsIgnoreCase(channel, "CDM")) {
            amountWithTaxQ1 = RebateUtil.formatDecimal(cdmMktQ1Used.add(cdmIviQ1Used));
            amountWithTaxQ234 = RebateUtil.formatDecimal(cdmMktQ2Used.add(cdmMktQ3Q4Used)
                    .add(cdmIviQ2Used).add(cdmIviQ3Q4Used));
        } else {
            amountWithTaxQ1 = RebateUtil.formatDecimal(cioBdQ1Used.add(cioMktQ1Used).add(cioIviQ1Used));
            amountWithTaxQ234 = RebateUtil.formatDecimal(cioBdQ2Used.add(cioBdQ3Q4Used)
                    .add(cioMktQ2Used).add(cioMktQ3Q4Used)
                    .add(cioIviQ2Used).add(cioIviQ3Q4Used));
        }

        RebateSalesExcel rebateSalesExcel = null;
        BigDecimal mktRate, bdRate, iviRate;

        rebateSalesExcel = new RebateSalesExcel();
        rebateSalesExcel.setOrganizationNameCN(orgRebate.getOrgName());
        rebateSalesExcel.setOrganizationNameEn(orgRebate.getOrgEnName());
        rebateSalesExcel.setSalesName(Joiner.on(",").skipNulls().join(salesMap.get(StrUtil.equals(channel, "C&I") ? "CIO" : "CDM")));
        rebateSalesExcel.setProductName("润滑油产品");
        rebateSalesExcel.setMonth(StrUtil.equalsIgnoreCase("Q1", quarter) ? "Q1" : "Q2");
        rebateSalesExcel.setCompanyCode(companyCode);
        DicItemVo officeItem = getOfficeConfigFromDic(companyCode);
        rebateSalesExcel.setOfficeName(officeItem.getDicItemName());

        BigDecimal totalFundEarned = BigDecimal.ZERO;
        BigDecimal taxRate = BigDecimal.ZERO;
        if (StrUtil.equalsIgnoreCase(channel, "CDM")) {
            mktRate = companyFund == null ? BigDecimal.ZERO : RebateUtil.getNullAsZero(companyFund.getMarketingFundRate());
            iviRate = companyFund == null ? BigDecimal.ZERO : RebateUtil.getNullAsZero(companyFund.getIviFundRate());
            if (StrUtil.equals(quarter, "Q1")) {
                rebateSalesExcel.setReimbursedRebateVATCc("16%");//税率
                totalFundEarned = cdmMktQ1Used.multiply(mktRate).add(iviRate.multiply(cdmIviQ1Used));
                if (amountWithTaxQ1.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ1, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                taxRate = ElitesConstant.Q1_TAX_RATE;
            } else {
                rebateSalesExcel.setReimbursedRebateVATCc("13%");//税率
                totalFundEarned = (cdmMktQ2Used.add(cdmMktQ3Q4Used)).multiply(mktRate)
                        .add(iviRate.multiply(cdmIviQ2Used.add(cdmIviQ3Q4Used)));
                if (amountWithTaxQ234.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ234, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                taxRate = ElitesConstant.TAX_RATE;
            }
        } else {
            mktRate = companyFund == null ? BigDecimal.ZERO : RebateUtil.getNullAsZero(companyFund.getMarketingFundRate());
            bdRate = companyFund == null ? BigDecimal.ZERO : RebateUtil.getNullAsZero(companyFund.getBdFundRate());
            iviRate = companyFund == null ? BigDecimal.ZERO : RebateUtil.getNullAsZero(companyFund.getIviFundRate());
            if (StrUtil.equals(quarter, "Q1")) {
                rebateSalesExcel.setReimbursedRebateVATCc("16%");//税率
                totalFundEarned = cioMktQ1Used.multiply(mktRate)
                        .add(iviRate.multiply(cioIviQ1Used))
                        .add(cioBdQ1Used.multiply(bdRate));
                if (amountWithTaxQ1.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ1, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                taxRate = ElitesConstant.Q1_TAX_RATE;
            } else {
                rebateSalesExcel.setReimbursedRebateVATCc("13%");//税率
                totalFundEarned = (cioMktQ2Used.add(cioBdQ3Q4Used)).multiply(mktRate)
                        .add(iviRate.multiply(cioIviQ2Used.add(cioIviQ3Q4Used)))
                        .add((cioBdQ2Used.add(cioBdQ3Q4Used)).multiply(bdRate));
                if (amountWithTaxQ234.compareTo(BigDecimal.ZERO) > 0) {
                    rebateSalesExcel.setFundPercent(RebateUtil.percentFormat(RebateUtil.div(totalFundEarned, amountWithTaxQ234, 4)));
                } else {
                    rebateSalesExcel.setFundPercent("0%");
                }
                taxRate = ElitesConstant.TAX_RATE;
            }

        }
        rebateSalesExcel.setTotalFundEarned(RebateUtil.format(totalFundEarned));
        rebateSalesExcel.setTotalFundEarnedWithoutTax(RebateUtil.format(RebateUtil.div(totalFundEarned, taxRate, 2)));
        return rebateSalesExcel;

    }


    private DicItemVo getOfficeConfigFromDic(String officeCode) {
        return dicService.getDictItem("elites.office.detail", officeCode);
    }

    @Override
    public Map<String, DicItemVo> getCompanyCodeList() {
        Map<String, DicItemVo> map = new HashMap<String, DicItemVo>(4);
        Map<String, Object> resultLst = dicService.getDicItemByDicTypeCode("elites.office.detail");
        List<DicItemVo> itemList = (ArrayList<DicItemVo>) resultLst.get("data");
        for (DicItemVo dicItem : itemList) {
            map.put(dicItem.getDicItemCode(), dicItem);
        }
        return map;
    }

    public Map<String, String> getOfficeMapFromDic() {
        Map<String, String> map = new HashMap<String, String>(4);
        Map<String, DicItemVo> resultLst = getCompanyCodeList();
        for (String code : resultLst.keySet()) {
            map.put(code, resultLst.get(code).getDicItemName());
        }
        return map;
    }
}
