(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0c772a98"],{"007d":function(t,e,n){var i=n("3eba");n("cb8f"),n("a96b"),n("42f6"),i.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),i.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){})},"0156":function(t,e,n){var i=n("6d8b");function r(t,e,n){n=n||{};var r=t.coordinateSystem,a=e.axis,o={},s=a.getAxesOnZeroOf()[0],l=a.position,u=s?"onZero":l,c=a.dim,h=r.getRect(),d=[h.x,h.x+h.width,h.y,h.y+h.height],f={left:0,right:1,top:0,bottom:1,onZero:2},p=e.get("offset")||0,g="x"===c?[d[2]-p,d[3]+p]:[d[0]-p,d[1]+p];if(s){var v=s.toGlobalCoord(s.dataToCoord(0));g[f.onZero]=Math.max(Math.min(v,g[1]),g[0])}o.position=["y"===c?g[f[u]]:d[0],"x"===c?g[f[u]]:d[3]],o.rotation=Math.PI/2*("x"===c?0:1);var m={top:-1,bottom:1,left:-1,right:1};o.labelDirection=o.tickDirection=o.nameDirection=m[l],o.labelOffset=s?g[f[l]]-g[f.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),i.retrieve(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var y=e.get("axisLabel.rotate");return o.labelRotate="top"===u?-y:y,o.z2=1,o}e.layout=r},"01ed":function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("2306");n("5aa9"),n("af24"),i.extendComponentView({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new a.Rect({shape:t.coordinateSystem.getRect(),style:r.defaults({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),i.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})},"0352":function(t,e,n){var i=n("6cb7"),r=n("b12f"),a=n("0f99"),o=a.detectSourceFormat,s=n("93d0"),l=s.SERIES_LAYOUT_BY_COLUMN;i.extend({type:"dataset",defaultOption:{seriesLayoutBy:l,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){o(this)}}),r.extend({type:"dataset"})},"04f6":function(t,e){var n=32,i=7;function r(t){var e=0;while(t>=n)e|=1&t,t>>=1;return t+e}function a(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){while(r<n&&i(t[r],t[r-1])<0)r++;o(t,e,r)}else while(r<n&&i(t[r],t[r-1])>=0)r++;return r-e}function o(t,e,n){n--;while(e<n){var i=t[e];t[e++]=t[n],t[n--]=i}}function s(t,e,n,i,r){for(i===e&&i++;i<n;i++){var a,o=t[i],s=e,l=i;while(s<l)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:while(u>0)t[s+u]=t[s+u-1],u--}t[s]=o}}function l(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])>0){s=i-r;while(l<s&&a(t,e[n+r+l])>0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s),o+=r,l+=r}else{s=r+1;while(l<s&&a(t,e[n+r-l])<=0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}o++;while(o<l){var c=o+(l-o>>>1);a(t,e[n+c])>0?o=c+1:l=c}return l}function u(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])<0){s=r+1;while(l<s&&a(t,e[n+r-l])<0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}else{s=i-r;while(l<s&&a(t,e[n+r+l])>=0)o=l,l=1+(l<<1),l<=0&&(l=s);l>s&&(l=s),o+=r,l+=r}o++;while(o<l){var c=o+(l-o>>>1);a(t,e[n+c])<0?l=c:o=c+1}return l}function c(t,e){var n,r,a=i,o=0,s=0;o=t.length;var c=[];function h(t,e){n[s]=t,r[s]=e,s+=1}function d(){while(s>1){var t=s-2;if(t>=1&&r[t-1]<=r[t]+r[t+1]||t>=2&&r[t-2]<=r[t]+r[t-1])r[t-1]<r[t+1]&&t--;else if(r[t]>r[t+1])break;p(t)}}function f(){while(s>1){var t=s-2;t>0&&r[t-1]<r[t+1]&&t--,p(t)}}function p(i){var a=n[i],o=r[i],c=n[i+1],h=r[i+1];r[i]=o+h,i===s-3&&(n[i+1]=n[i+2],r[i+1]=r[i+2]),s--;var d=u(t[c],t,a,o,0,e);a+=d,o-=d,0!==o&&(h=l(t[a+o-1],t,c,h,h-1,e),0!==h&&(o<=h?g(a,o,c,h):v(a,o,c,h)))}function g(n,r,o,s){var h=0;for(h=0;h<r;h++)c[h]=t[n+h];var d=0,f=o,p=n;if(t[p++]=t[f++],0!==--s)if(1!==r){var g,v,m,y=a;while(1){g=0,v=0,m=!1;do{if(e(t[f],c[d])<0){if(t[p++]=t[f++],v++,g=0,0===--s){m=!0;break}}else if(t[p++]=c[d++],g++,v=0,1===--r){m=!0;break}}while((g|v)<y);if(m)break;do{if(g=u(t[f],c,d,r,0,e),0!==g){for(h=0;h<g;h++)t[p+h]=c[d+h];if(p+=g,d+=g,r-=g,r<=1){m=!0;break}}if(t[p++]=t[f++],0===--s){m=!0;break}if(v=l(c[d],t,f,s,0,e),0!==v){for(h=0;h<v;h++)t[p+h]=t[f+h];if(p+=v,f+=v,s-=v,0===s){m=!0;break}}if(t[p++]=c[d++],1===--r){m=!0;break}y--}while(g>=i||v>=i);if(m)break;y<0&&(y=0),y+=2}if(a=y,a<1&&(a=1),1===r){for(h=0;h<s;h++)t[p+h]=t[f+h];t[p+s]=c[d]}else{if(0===r)throw new Error;for(h=0;h<r;h++)t[p+h]=c[d+h]}}else{for(h=0;h<s;h++)t[p+h]=t[f+h];t[p+s]=c[d]}else for(h=0;h<r;h++)t[p+h]=c[d+h]}function v(n,r,o,s){var h=0;for(h=0;h<s;h++)c[h]=t[o+h];var d=n+r-1,f=s-1,p=o+s-1,g=0,v=0;if(t[p--]=t[d--],0!==--r)if(1!==s){var m=a;while(1){var y=0,x=0,_=!1;do{if(e(c[f],t[d])<0){if(t[p--]=t[d--],y++,x=0,0===--r){_=!0;break}}else if(t[p--]=c[f--],x++,y=0,1===--s){_=!0;break}}while((y|x)<m);if(_)break;do{if(y=r-u(c[f],t,n,r,r-1,e),0!==y){for(p-=y,d-=y,r-=y,v=p+1,g=d+1,h=y-1;h>=0;h--)t[v+h]=t[g+h];if(0===r){_=!0;break}}if(t[p--]=c[f--],1===--s){_=!0;break}if(x=s-l(t[d],c,0,s,s-1,e),0!==x){for(p-=x,f-=x,s-=x,v=p+1,g=f+1,h=0;h<x;h++)t[v+h]=c[g+h];if(s<=1){_=!0;break}}if(t[p--]=t[d--],0===--r){_=!0;break}m--}while(y>=i||x>=i);if(_)break;m<0&&(m=0),m+=2}if(a=m,a<1&&(a=1),1===s){for(p-=r,d-=r,v=p+1,g=d+1,h=r-1;h>=0;h--)t[v+h]=t[g+h];t[p]=c[f]}else{if(0===s)throw new Error;for(g=p-(s-1),h=0;h<s;h++)t[g+h]=c[h]}}else{for(p-=r,d-=r,v=p+1,g=d+1,h=r-1;h>=0;h--)t[v+h]=t[g+h];t[p]=c[f]}else for(g=p-(s-1),h=0;h<s;h++)t[g+h]=c[h]}n=[],r=[],this.mergeRuns=d,this.forceMergeRuns=f,this.pushRun=h}function h(t,e,i,o){i||(i=0),o||(o=t.length);var l=o-i;if(!(l<2)){var u=0;if(l<n)return u=a(t,i,o,e),void s(t,i,o,i+u,e);var h=new c(t,e),d=r(l);do{if(u=a(t,i,o,e),u<d){var f=l;f>d&&(f=d),s(t,i,i+f,i+u,e),u=f}h.pushRun(i,u),h.mergeRuns(),l-=u,i+=u}while(0!==l);h.forceMergeRuns()}}t.exports=h},"0655":function(t,e,n){var i=n("8728"),r=1e-8;function a(t,e){return Math.abs(t-e)<r}function o(t,e,n){var r=0,o=t[0];if(!o)return!1;for(var s=1;s<t.length;s++){var l=t[s];r+=i(o[0],o[1],l[0],l[1],e,n),o=l}var u=t[0];return a(o[0],u[0])&&a(o[1],u[1])||(r+=i(o[0],o[1],u[0],u[1],e,n)),0!==r}e.contain=o},"06ad":function(t,e,n){var i=n("4436"),r=n("41ef"),a=n("6d8b"),o=a.isArrayLike,s=Array.prototype.slice;function l(t,e){return t[e]}function u(t,e,n){t[e]=n}function c(t,e,n){return(e-t)*n+t}function h(t,e,n){return n>.5?e:t}function d(t,e,n,i,r){var a=t.length;if(1===r)for(var o=0;o<a;o++)i[o]=c(t[o],e[o],n);else{var s=a&&t[0].length;for(o=0;o<a;o++)for(var l=0;l<s;l++)i[o][l]=c(t[o][l],e[o][l],n)}}function f(t,e,n){var i=t.length,r=e.length;if(i!==r){var a=i>r;if(a)t.length=r;else for(var o=i;o<r;o++)t.push(1===n?e[o]:s.call(e[o]))}var l=t[0]&&t[0].length;for(o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var u=0;u<l;u++)isNaN(t[o][u])&&(t[o][u]=e[o][u])}function p(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;r<i;r++)if(t[r]!==e[r])return!1}else{var a=t[0].length;for(r=0;r<i;r++)for(var o=0;o<a;o++)if(t[r][o]!==e[r][o])return!1}return!0}function g(t,e,n,i,r,a,o,s,l){var u=t.length;if(1===l)for(var c=0;c<u;c++)s[c]=v(t[c],e[c],n[c],i[c],r,a,o);else{var h=t[0].length;for(c=0;c<u;c++)for(var d=0;d<h;d++)s[c][d]=v(t[c][d],e[c][d],n[c][d],i[c][d],r,a,o)}}function v(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function m(t){if(o(t)){var e=t.length;if(o(t[0])){for(var n=[],i=0;i<e;i++)n.push(s.call(t[i]));return n}return s.call(t)}return t}function y(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function x(t){var e=t[t.length-1].value;return o(e&&e[0])?2:1}function _(t,e,n,a,s,l){var u=t._getter,m=t._setter,_="spline"===e,b=a.length;if(b){var w,S=a[0].value,M=o(S),T=!1,C=!1,I=M?x(a):0;a.sort(function(t,e){return t.time-e.time}),w=a[b-1].time;for(var D=[],A=[],k=a[0].value,O=!0,P=0;P<b;P++){D.push(a[P].time/w);var L=a[P].value;if(M&&p(L,k,I)||!M&&L===k||(O=!1),k=L,"string"===typeof L){var E=r.parse(L);E?(L=E,T=!0):C=!0}A.push(L)}if(l||!O){var R=A[b-1];for(P=0;P<b-1;P++)M?f(A[P],R,I):!isNaN(A[P])||isNaN(R)||C||T||(A[P]=R);M&&f(u(t._target,s),R,I);var N,B,z,F,V,H,W=0,G=0;if(T)var j=[0,0,0,0];var Y=function(t,e){var n;if(e<0)n=0;else if(e<G){for(N=Math.min(W+1,b-1),n=N;n>=0;n--)if(D[n]<=e)break;n=Math.min(n,b-2)}else{for(n=W;n<b;n++)if(D[n]>e)break;n=Math.min(n-1,b-2)}W=n,G=e;var i=D[n+1]-D[n];if(0!==i)if(B=(e-D[n])/i,_)if(F=A[n],z=A[0===n?n:n-1],V=A[n>b-2?b-1:n+1],H=A[n>b-3?b-1:n+2],M)g(z,F,V,H,B,B*B,B*B*B,u(t,s),I);else{if(T)r=g(z,F,V,H,B,B*B,B*B*B,j,1),r=y(j);else{if(C)return h(F,V,B);r=v(z,F,V,H,B,B*B,B*B*B)}m(t,s,r)}else if(M)d(A[n],A[n+1],B,u(t,s),I);else{var r;if(T)d(A[n],A[n+1],B,j,1),r=y(j);else{if(C)return h(A[n],A[n+1],B);r=c(A[n],A[n+1],B)}m(t,s,r)}},U=new i({target:t._target,life:w,loop:t._loop,delay:t._delay,onframe:Y,ondestroy:n});return e&&"spline"!==e&&(U.easing=e),U}}}var b=function(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||l,this._setter=i||u,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};b.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:m(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){var n,i=this,r=0,a=function(){r--,r||i._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=_(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var w=b;t.exports=w},"07d7":function(t,e,n){var i=n("6d8b"),r=n("41ef"),a=n("607d"),o=n("22d1"),s=n("eda2"),l=i.each,u=s.toCamelCase,c=["","-webkit-","-moz-","-o-"],h="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";function d(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",n="left "+t+"s "+e+",top "+t+"s "+e;return i.map(c,function(t){return t+"transition:"+n}).join(";")}function f(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();return i&&e.push("color:"+i),e.push("font:"+t.getFont()),n&&e.push("line-height:"+Math.round(3*n/2)+"px"),l(["decoration","align"],function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)}),e.join(";")}function p(t){var e=[],n=t.get("transitionDuration"),i=t.get("backgroundColor"),a=t.getModel("textStyle"),c=t.get("padding");return n&&e.push(d(n)),i&&(o.canvasSupported?e.push("background-Color:"+i):(e.push("background-Color:#"+r.toHex(i)),e.push("filter:alpha(opacity=70)"))),l(["width","color","radius"],function(n){var i="border-"+n,r=u(i),a=t.get(r);null!=a&&e.push(i+":"+a+("color"===n?"":"px"))}),e.push(f(a)),null!=c&&e.push("padding:"+s.normalizeCssArray(c).join("px ")+"px"),e.join(";")+";"}function g(t,e){if(o.wxa)return null;var n=document.createElement("div"),i=this._zr=e.getZr();this.el=n,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(n),this._container=t,this._show=!1,this._hideTimeout;var r=this;n.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},n.onmousemove=function(e){if(e=e||window.event,!r._enterable){var n=i.handler;a.normalizeEvent(t,e,!0),n.dispatch("mousemove",e)}},n.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}g.prototype={constructor:g,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),n=t.style;"absolute"!==n.position&&"absolute"!==e.position&&(n.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=h+p(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n,i=this._zr;i&&i.painter&&(n=i.painter.getViewportRootOffset())&&(t+=n.offsetLeft,e+=n.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(i.bind(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var n=document.defaultView.getComputedStyle(this.el);n&&(t+=parseInt(n.borderLeftWidth,10)+parseInt(n.borderRightWidth,10),e+=parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10))}return{width:t,height:e}}};var v=g;t.exports=v},"0b44":function(t,e,n){var i=n("607d"),r=function(){this._track=[]};function a(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function o(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}r.prototype={constructor:r,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var r=t.touches;if(r){for(var a={points:[],touches:[],target:e,event:t},o=0,s=r.length;o<s;o++){var l=r[o],u=i.clientToLocal(n,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},_recognize:function(t){for(var e in s)if(s.hasOwnProperty(e)){var n=s[e](this._track,t);if(n)return n}}};var s={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&r.length>1&&i&&i.length>1){var s=a(i)/a(r);!isFinite(s)&&(s=1),e.pinchScale=s;var l=o(i);return e.pinchX=l[0],e.pinchY=l[1],{type:"pinch",target:t[0].target,event:e}}}}},l=r;t.exports=l},"0cde":function(t,e,n){var i=n("1687"),r=n("401b"),a=i.identity,o=5e-5;function s(t){return t>o||t<-o}var l=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},u=l.prototype;u.transform=null,u.needLocalTransform=function(){return s(this.rotation)||s(this.position[0])||s(this.position[1])||s(this.scale[0]-1)||s(this.scale[1]-1)};var c=[];u.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),r=this.transform;if(n||e){r=r||i.create(),n?this.getLocalTransform(r):a(r),e&&(n?i.mul(r,t.transform,r):i.copy(r,t.transform)),this.transform=r;var o=this.globalScaleRatio;if(null!=o&&1!==o){this.getGlobalScale(c);var s=c[0]<0?-1:1,l=c[1]<0?-1:1,u=((c[0]-s)*o+s)/c[0]||0,h=((c[1]-l)*o+l)/c[1]||0;r[0]*=u,r[1]*=u,r[2]*=h,r[3]*=h}this.invTransform=this.invTransform||i.create(),i.invert(this.invTransform,r)}else r&&a(r)},u.getLocalTransform=function(t){return l.getLocalTransform(this,t)},u.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},u.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var h=[],d=i.create();u.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,r=this.scale;s(e-1)&&(e=Math.sqrt(e)),s(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],r[0]=e,r[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e)}},u.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(i.mul(h,t.invTransform,e),e=h);var n=this.origin;n&&(n[0]||n[1])&&(d[4]=n[0],d[5]=n[1],i.mul(h,e,d),h[4]-=n[0],h[5]-=n[1],e=h),this.setLocalTransform(e)}},u.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},u.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&r.applyTransform(n,n,i),n},u.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&r.applyTransform(n,n,i),n},l.getLocalTransform=function(t,e){e=e||[],a(e);var n=t.origin,r=t.scale||[1,1],o=t.rotation||0,s=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),i.scale(e,e,r),o&&i.rotate(e,e,o),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=s[0],e[5]+=s[1],e};var f=l;t.exports=f},"0da8":function(t,e,n){var i=n("19eb"),r=n("9850"),a=n("6d8b"),o=n("5e76");function s(t){i.call(this,t)}s.prototype={constructor:s,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=o.createOrUpdateImage(i,this._image,this,this.onload);if(r&&o.isImageReady(r)){var a=n.x||0,s=n.y||0,l=n.width,u=n.height,c=r.width/r.height;if(null==l&&null!=u?l=u*c:null==u&&null!=l?u=l/c:null==l&&null==u&&(l=r.width,u=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var h=n.sx||0,d=n.sy||0;t.drawImage(r,h,d,n.sWidth,n.sHeight,a,s,l,u)}else if(n.sx&&n.sy){h=n.sx,d=n.sy;var f=l-h,p=u-d;t.drawImage(r,h,d,f,p,a,s,l,u)}else t.drawImage(r,a,s,l,u);null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new r(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},a.inherits(s,i);var l=s;t.exports=l},"0f99":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("e0d3")),a=r.makeInner,o=r.getDataItemValue,s=n("6d8b"),l=s.createHashMap,u=s.each,c=s.map,h=s.isArray,d=s.isString,f=s.isObject,p=s.isTypedArray,g=s.isArrayLike,v=s.extend,m=(s.assert,n("ec6f")),y=n("93d0"),x=y.SOURCE_FORMAT_ORIGINAL,_=y.SOURCE_FORMAT_ARRAY_ROWS,b=y.SOURCE_FORMAT_OBJECT_ROWS,w=y.SOURCE_FORMAT_KEYED_COLUMNS,S=y.SOURCE_FORMAT_UNKNOWN,M=y.SOURCE_FORMAT_TYPED_ARRAY,T=y.SERIES_LAYOUT_BY_ROW,C={Must:1,Might:2,Not:3},I=a();function D(t){var e=t.option.source,n=S;if(p(e))n=M;else if(h(e)){0===e.length&&(n=_);for(var i=0,r=e.length;i<r;i++){var a=e[i];if(null!=a){if(h(a)){n=_;break}if(f(a)){n=b;break}}}}else if(f(e)){for(var o in e)if(e.hasOwnProperty(o)&&g(e[o])){n=w;break}}else if(null!=e)throw new Error("Invalid data");I(t).sourceFormat=n}function A(t){return I(t).source}function k(t){I(t).datasetMap=l()}function O(t){var e=t.option,n=e.data,i=p(n)?M:x,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=z(t);if(l){var u=l.option;n=u.source,i=I(l).sourceFormat,r=!0,a=a||u.seriesLayoutBy,null==o&&(o=u.sourceHeader),s=s||u.dimensions}var c=P(n,i,a,o,s);I(t).source=new m({data:n,fromDataset:r,seriesLayoutBy:a,sourceFormat:i,dimensionsDefine:c.dimensionsDefine,startIndex:c.startIndex,dimensionsDetectCount:c.dimensionsDetectCount,encodeDefine:e.encode})}function P(t,e,n,i,r){if(!t)return{dimensionsDefine:L(r)};var a,s;if(e===_)"auto"===i||null==i?E(function(t){null!=t&&"-"!==t&&(d(t)?null==s&&(s=1):s=0)},n,t,10):s=i?1:0,r||1!==s||(r=[],E(function(t,e){r[e]=null!=t?t:""},n,t)),a=r?r.length:n===T?t.length:t[0]?t[0].length:null;else if(e===b)r||(r=R(t));else if(e===w)r||(r=[],u(t,function(t,e){r.push(e)}));else if(e===x){var l=o(t[0]);a=h(l)&&l.length||1}return{startIndex:s,dimensionsDefine:L(r),dimensionsDetectCount:a}}function L(t){if(t){var e=l();return c(t,function(t,n){if(t=v({},f(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t})}}function E(t,e,n,i){if(null==i&&(i=1/0),e===T)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var a=n[0]||[];for(r=0;r<a.length&&r<i;r++)t(a[r],r)}}function R(t){var e,n=0;while(n<t.length&&!(e=t[n++]));if(e){var i=[];return u(e,function(t,e){i.push(e)}),i}}function N(t,e,n){var i={},r=z(e);if(!r||!t)return i;var a,o,s=[],l=[],c=e.ecModel,h=I(c).datasetMap,d=r.uid+"_"+n.seriesLayoutBy;t=t.slice(),u(t,function(e,n){!f(e)&&(t[n]={name:e}),"ordinal"===e.type&&null==a&&(a=n,o=v(t[n])),i[e.name]=[]});var p=h.get(d)||h.set(d,{categoryWayDim:o,valueWayDim:0});function g(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function v(t){var e=t.dimsDef;return e?e.length:1}return u(t,function(t,e){var n=t.name,r=v(t);if(null==a){var o=p.valueWayDim;g(i[n],o,r),g(l,o,r),p.valueWayDim+=r}else if(a===e)g(i[n],0,r),g(s,0,r);else{o=p.categoryWayDim;g(i[n],o,r),g(l,o,r),p.categoryWayDim+=r}}),s.length&&(i.itemName=s),l.length&&(i.seriesName=l),i}function B(t,e,n){var i={},r=z(t);if(!r)return i;var a,o=e.sourceFormat,s=e.dimensionsDefine;o!==b&&o!==w||u(s,function(t,e){"name"===(f(t)?t.name:t)&&(a=e)});var l=function(){for(var t={},i={},r=[],l=0,u=Math.min(5,n);l<u;l++){var c=V(e.data,o,e.seriesLayoutBy,s,e.startIndex,l);r.push(c);var h=c===C.Not;if(h&&null==t.v&&l!==a&&(t.v=l),(null==t.n||t.n===t.v||!h&&r[t.n]===C.Not)&&(t.n=l),d(t)&&r[t.n]!==C.Not)return t;h||(c===C.Might&&null==i.v&&l!==a&&(i.v=l),null!=i.n&&i.n!==i.v||(i.n=l))}function d(t){return null!=t.v&&null!=t.n}return d(t)?t:d(i)?i:null}();if(l){i.value=l.v;var c=null!=a?a:l.n;i.itemName=[c],i.seriesName=[c]}return i}function z(t){var e=t.option,n=e.data;if(!n)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function F(t,e){return V(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function V(t,e,n,i,r,a){var s,l,u,c=5;if(p(t))return C.Not;if(i){var g=i[a];f(g)?(l=g.name,u=g.type):d(g)&&(l=g)}if(null!=u)return"ordinal"===u?C.Must:C.Not;if(e===_)if(n===T){for(var v=t[a],m=0;m<(v||[]).length&&m<c;m++)if(null!=(s=I(v[r+m])))return s}else for(m=0;m<t.length&&m<c;m++){var y=t[r+m];if(y&&null!=(s=I(y[a])))return s}else if(e===b){if(!l)return C.Not;for(m=0;m<t.length&&m<c;m++){var S=t[m];if(S&&null!=(s=I(S[l])))return s}}else if(e===w){if(!l)return C.Not;v=t[l];if(!v||p(v))return C.Not;for(m=0;m<v.length&&m<c;m++)if(null!=(s=I(v[m])))return s}else if(e===x)for(m=0;m<t.length&&m<c;m++){S=t[m];var M=o(S);if(!h(M))return C.Not;if(null!=(s=I(M[a])))return s}function I(t){var e=d(t);return null!=t&&isFinite(t)&&""!==t?e?C.Might:C.Not:e&&"-"!==t?C.Must:void 0}return C.Not}e.BE_ORDINAL=C,e.detectSourceFormat=D,e.getSource=A,e.resetSourceDefaulter=k,e.prepareSource=O,e.makeSeriesEncodeForAxisCoordSys=N,e.makeSeriesEncodeForNameBased=B,e.guessOrdinal=F},"133d":function(t,e,n){var i=n("6d8b"),r=n("e0d3");function a(t,e){var n,a=[],o=t.seriesIndex;if(null==o||!(n=e.getSeriesByIndex(o)))return{point:[]};var s=n.getData(),l=r.queryDataIndex(s,t);if(null==l||l<0||i.isArray(l))return{point:[]};var u=s.getItemGraphicEl(l),c=n.coordinateSystem;if(n.getTooltipPosition)a=n.getTooltipPosition(l)||[];else if(c&&c.dataToPoint)a=c.dataToPoint(s.getValues(i.map(c.dimensions,function(t){return s.mapDimension(t)}),l,!0))||[];else if(u){var h=u.getBoundingRect().clone();h.applyTransform(u.transform),a=[h.x+h.width/2,h.y+h.height/2]}return{point:a,el:u}}t.exports=a},1418:function(t,e,n){var i=n("6d8b"),r=n("a15a"),a=r.createSymbol,o=n("2306"),s=n("3842"),l=s.parsePercent,u=n("c775"),c=u.getDefaultLabel;function h(t,e,n){o.Group.call(this),this.updateData(t,e,n)}var d=h.prototype,f=h.getSymbolSize=function(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]};function p(t){return[t[0]/2,t[1]/2]}function g(t,e){this.parent.drift(t,e)}d._createSymbol=function(t,e,n,i,r){this.removeAll();var o=e.getItemVisual(n,"color"),s=a(t,-1,-1,2,2,o,r);s.attr({z2:100,culling:!0,scale:p(i)}),s.drift=g,this._symbolType=t,this.add(s)},d.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},d.getSymbolPath=function(){return this.childAt(0)},d.getScale=function(){return this.childAt(0).scale},d.highlight=function(){this.childAt(0).trigger("emphasis")},d.downplay=function(){this.childAt(0).trigger("normal")},d.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},d.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},d.updateData=function(t,e,n){this.silent=!1;var i=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,a=f(t,e),s=i!==this._symbolType;if(s){var l=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(i,t,e,a,l)}else{var u=this.childAt(0);u.silent=!1,o.updateProps(u,{scale:p(a)},r,e)}if(this._updateCommon(t,e,a,n),s){u=this.childAt(0);var c=n&&n.fadeIn,h={scale:u.scale.slice()};c&&(h.style={opacity:u.style.opacity}),u.scale=[0,0],c&&(u.style.opacity=0),o.initProps(u,h,r,e)}this._seriesModel=r};var v=["itemStyle"],m=["emphasis","itemStyle"],y=["label"],x=["emphasis","label"];function _(t,e){if(!this.incremental&&!this.useHoverLayer)if("emphasis"===e){var n=this.__symbolOriginalScale,i=n[1]/n[0],r={scale:[Math.max(1.1*n[0],n[0]+3),Math.max(1.1*n[1],n[1]+3*i)]};this.animateTo(r,400,"elasticOut")}else"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}d._updateCommon=function(t,e,n,r){var a=this.childAt(0),s=t.hostModel,u=t.getItemVisual(e,"color");"image"!==a.type?a.useStyle({strokeNoScale:!0}):a.setStyle({opacity:null,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var h=r&&r.itemStyle,d=r&&r.hoverItemStyle,f=r&&r.symbolRotate,g=r&&r.symbolOffset,b=r&&r.labelModel,w=r&&r.hoverLabelModel,S=r&&r.hoverAnimation,M=r&&r.cursorStyle;if(!r||t.hasItemOption){var T=r&&r.itemModel?r.itemModel:t.getItemModel(e);h=T.getModel(v).getItemStyle(["color"]),d=T.getModel(m).getItemStyle(),f=T.getShallow("symbolRotate"),g=T.getShallow("symbolOffset"),b=T.getModel(y),w=T.getModel(x),S=T.getShallow("hoverAnimation"),M=T.getShallow("cursor")}else d=i.extend({},d);var C=a.style;a.attr("rotation",(f||0)*Math.PI/180||0),g&&a.attr("position",[l(g[0],n[0]),l(g[1],n[1])]),M&&a.attr("cursor",M),a.setColor(u,r&&r.symbolInnerColor),a.setStyle(h);var I=t.getItemVisual(e,"opacity");null!=I&&(C.opacity=I);var D=t.getItemVisual(e,"liftZ"),A=a.__z2Origin;null!=D?null==A&&(a.__z2Origin=a.z2,a.z2+=D):null!=A&&(a.z2=A,a.__z2Origin=null);var k=r&&r.useNameLabel;function O(e,n){return k?t.getName(e):c(t,e)}o.setLabelStyle(C,d,b,w,{labelFetcher:s,labelDataIndex:e,defaultText:O,isRectText:!0,autoColor:u}),a.__symbolOriginalScale=p(n),a.hoverStyle=d,a.highDownOnUpdate=S&&s.isAnimationEnabled()?_:null,o.setHoverStyle(a)},d.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,(!e||!e.keepLabel)&&(n.style.text=null),o.updateProps(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},i.inherits(h,o.Group);var b=h;t.exports=b},1548:function(t,e,n){var i=n("6d8b"),r=n("3301"),a=n("697e"),o=n("2023"),s=n("4319"),l=n("f934");l.getLayoutRect;e.getLayoutRect=l.getLayoutRect;var u=n("ee1a"),c=u.enableDataStack,h=u.isDimensionStacked,d=u.getStackedDimension,f=n("862d");e.completeDimensions=f;var p=n("b1d4");e.createDimensions=p;var g=n("a15a");function v(t){return r(t.getSource(),t)}e.createSymbol=g.createSymbol;var m={isDimensionStacked:h,enableDataStack:c,getStackedDimension:d};function y(t,e){var n=e;s.isInstance(e)||(n=new s(e),i.mixin(n,o));var r=a.createScaleByModel(n);return r.setExtent(t[0],t[1]),a.niceScaleExtent(r,n),r}function x(t){i.mixin(t,o)}e.createList=v,e.dataStack=m,e.createScale=y,e.mixinAxisModelCommonMethods=x},"15af":function(t,e,n){var i=n("3eba");n("cb69"),n("abff");var r=n("7f96"),a=n("87c3");n("01ed"),i.registerVisual(r("scatter","circle")),i.registerLayout(a("scatter"))},1687:function(t,e){var n="undefined"===typeof Float32Array?Array:Float32Array;function i(){var t=new n(6);return r(t),t}function r(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function a(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function o(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],a=e[0]*n[2]+e[2]*n[3],o=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function s(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function l(t,e,n){var i=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],u=Math.sin(n),c=Math.cos(n);return t[0]=i*c+o*u,t[1]=-i*u+o*c,t[2]=r*c+s*u,t[3]=-r*u+c*s,t[4]=c*a+u*l,t[5]=c*l-u*a,t}function u(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function c(t,e){var n=e[0],i=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=n*o-a*i;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-o*r)*l,t[5]=(a*r-n*s)*l,t):null}function h(t){var e=i();return a(e,t),e}e.create=i,e.identity=r,e.copy=a,e.mul=o,e.translate=s,e.rotate=l,e.scale=u,e.invert=c,e.clone=h},"17b8":function(t,e,n){var i=n("3014"),r=i.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return!!this.get("large")&&this.get("progressive")},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},defaultOption:{clip:!0,roundCap:!1}});t.exports=r},"17d6":function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=n("e0d3"),o=a.makeInner,s=o(),l=i.each;function u(t,e,n){if(!r.node){var i=e.getZr();s(i).records||(s(i).records={}),c(i,e);var a=s(i).records[t]||(s(i).records[t]={});a.handler=n}}function c(t,e){function n(n,i){t.on(n,function(n){var r=p(e);l(s(t).records,function(t){t&&i(t,n,r.dispatchAction)}),h(r.pendings,e)})}s(t).initialized||(s(t).initialized=!0,n("click",i.curry(f,"click")),n("mousemove",i.curry(f,"mousemove")),n("globalout",d))}function h(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function d(t,e,n){t.handler("leave",null,n)}function f(t,e,n,i){e.handler(t,n,i)}function p(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}function g(t,e){if(!r.node){var n=e.getZr(),i=(s(n).records||{})[t];i&&(s(n).records[t]=null)}}e.register=u,e.unregister=g},"18b6":function(t,e,n){},"18c0":function(t,e,n){var i=n("6d8b"),r=n("e0d8"),a=n("8e43"),o=r.prototype,s=r.extend({type:"ordinal",init:function(t,e){t&&!i.isArray(t)||(t=new a({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"===typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),o.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return o.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(o.scale.call(this,t))},getTicks:function(){var t=[],e=this._extent,n=e[0];while(n<=e[1])t.push(n),n++;return t},getLabel:function(t){if(!this.isBlank())return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:i.noop,niceExtent:i.noop});s.create=function(){return new s};var l=s;t.exports=l},"19eb":function(t,e,n){var i=n("6d8b"),r=n("2b61"),a=n("d5b7"),o=n("9e2e");function s(t){for(var e in t=t||{},a.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new r(t.style,this),this._rect=null,this.__clipPaths=null}s.prototype={constructor:s,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?a.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new r(t,this),this.dirty(!1),this},calculateTextPosition:null},i.inherits(s,a),i.mixin(s,o);var l=s;t.exports=l},"1fab":function(t,e){var n=Array.prototype.slice,i=function(t){this._$handlers={},this._$eventProcessor=t};function r(t,e){var n=t._$eventProcessor;return null!=e&&n&&n.normalizeQuery&&(e=n.normalizeQuery(e)),e}function a(t,e,n,i,a,o){var s=t._$handlers;if("function"===typeof n&&(a=i,i=n,n=null),!i||!e)return t;n=r(t,n),s[e]||(s[e]=[]);for(var l=0;l<s[e].length;l++)if(s[e][l].h===i)return t;var u={h:i,one:o,query:n,ctx:a||t,callAtLast:i.zrEventfulCallAtLast},c=s[e].length-1,h=s[e][c];return h&&h.callAtLast?s[e].splice(c,0,u):s[e].push(u),t}i.prototype={constructor:i,one:function(t,e,n,i){return a(this,t,e,n,i,!0)},on:function(t,e,n,i){return a(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,a=n[t].length;r<a;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var r=arguments,a=r.length;a>3&&(r=n.call(r,1));for(var o=e.length,s=0;s<o;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(a){case 1:l.h.call(l.ctx);break;case 2:l.h.call(l.ctx,r[1]);break;case 3:l.h.call(l.ctx,r[1],r[2]);break;default:l.h.apply(l.ctx,r);break}l.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var r=arguments,a=r.length;a>4&&(r=n.call(r,1,r.length-1));for(var o=r[r.length-1],s=e.length,l=0;l<s;){var u=e[l];if(i&&i.filter&&null!=u.query&&!i.filter(t,u.query))l++;else{switch(a){case 1:u.h.call(o);break;case 2:u.h.call(o,r[1]);break;case 3:u.h.call(o,r[1],r[2]);break;default:u.h.apply(o,r);break}u.one?(e.splice(l,1),s--):l++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var o=i;t.exports=o},2023:function(t,e,n){var i=n("6d8b"),r={getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!==typeof n&&!i.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!==typeof n&&!i.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:i.noop,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}};t.exports=r},2039:function(t,e,n){var i=n("6d8b"),r={};function a(){this._coordinateSystems=[]}a.prototype={constructor:a,create:function(t,e){var n=[];i.each(r,function(i,r){var a=i.create(t,e);n=n.concat(a||[])}),this._coordinateSystems=n},update:function(t,e){i.each(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},a.register=function(t,e){r[t]=e},a.get=function(t){return r[t]};var o=a;t.exports=o},"20c8":function(t,e,n){var i=n("4a3f"),r=n("401b"),a=n("e263"),o=n("9850"),s=n("2cf4"),l=s.devicePixelRatio,u={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},c=[],h=[],d=[],f=[],p=Math.min,g=Math.max,v=Math.cos,m=Math.sin,y=Math.sqrt,x=Math.abs,_="undefined"!==typeof Float32Array,b=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};b.prototype={constructor:b,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,n){n=n||0,this._ux=x(n/l/t)||0,this._uy=x(n/l/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(u.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=x(t-this._xi)>this._ux||x(e-this._yi)>this._uy||this._len<5;return this.addData(u.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,a){return this.addData(u.C,t,e,n,i,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,a):this._ctx.bezierCurveTo(t,e,n,i,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,n,i){return this.addData(u.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,a){return this.addData(u.A,t,e,n,n,i,r-i,0,a?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,a),this._xi=v(r)*n+t,this._yi=m(r)*n+e,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(u.R,t,e,n,i),this},closePath:function(){this.addData(u.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!_||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();_&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[i++]=a[o];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,c=t-l,h=e-u,d=y(c*c+h*h),f=l,v=u,m=o.length;c/=d,h/=d,a<0&&(a=r+a),a%=r,f-=a*c,v-=a*h;while(c>0&&f<=t||c<0&&f>=t||0===c&&(h>0&&v<=e||h<0&&v>=e))i=this._dashIdx,n=o[i],f+=c*n,v+=h*n,this._dashIdx=(i+1)%m,c>0&&f<l||c<0&&f>l||h>0&&v<u||h<0&&v>u||s[i%2?"moveTo":"lineTo"](c>=0?p(f,t):g(f,t),h>=0?p(v,e):g(v,e));c=f-t,h=v-e,this._dashOffset=-y(c*c+h*h)},_dashedBezierTo:function(t,e,n,r,a,o){var s,l,u,c,h,d=this._dashSum,f=this._dashOffset,p=this._lineDash,g=this._ctx,v=this._xi,m=this._yi,x=i.cubicAt,_=0,b=this._dashIdx,w=p.length,S=0;for(f<0&&(f=d+f),f%=d,s=0;s<1;s+=.1)l=x(v,t,n,a,s+.1)-x(v,t,n,a,s),u=x(m,e,r,o,s+.1)-x(m,e,r,o,s),_+=y(l*l+u*u);for(;b<w;b++)if(S+=p[b],S>f)break;s=(S-f)/_;while(s<=1)c=x(v,t,n,a,s),h=x(m,e,r,o,s),b%2?g.moveTo(c,h):g.lineTo(c,h),s+=p[b]/_,b=(b+1)%w;b%2!==0&&g.lineTo(a,o),l=a-c,u=o-h,this._dashOffset=-y(l*l+u*u)},_dashedQuadraticTo:function(t,e,n,i){var r=n,a=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,_&&(this.data=new Float32Array(t)))},getBoundingRect:function(){c[0]=c[1]=d[0]=d[1]=Number.MAX_VALUE,h[0]=h[1]=f[0]=f[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,s=0,l=0;l<t.length;){var p=t[l++];switch(1===l&&(e=t[l],n=t[l+1],i=e,s=n),p){case u.M:i=t[l++],s=t[l++],e=i,n=s,d[0]=i,d[1]=s,f[0]=i,f[1]=s;break;case u.L:a.fromLine(e,n,t[l],t[l+1],d,f),e=t[l++],n=t[l++];break;case u.C:a.fromCubic(e,n,t[l++],t[l++],t[l++],t[l++],t[l],t[l+1],d,f),e=t[l++],n=t[l++];break;case u.Q:a.fromQuadratic(e,n,t[l++],t[l++],t[l],t[l+1],d,f),e=t[l++],n=t[l++];break;case u.A:var g=t[l++],y=t[l++],x=t[l++],_=t[l++],b=t[l++],w=t[l++]+b;l+=1;var S=1-t[l++];1===l&&(i=v(b)*x+g,s=m(b)*_+y),a.fromArc(g,y,x,_,b,w,S,d,f),e=v(w)*x+g,n=m(w)*_+y;break;case u.R:i=e=t[l++],s=n=t[l++];var M=t[l++],T=t[l++];a.fromLine(i,s,i+M,s+T,d,f);break;case u.Z:e=i,n=s;break}r.min(c,c,d),r.max(h,h,f)}return 0===l&&(c[0]=c[1]=h[0]=h[1]=0),new o(c[0],c[1],h[0]-c[0],h[1]-c[1])},rebuildPath:function(t){for(var e,n,i,r,a,o,s=this.data,l=this._ux,c=this._uy,h=this._len,d=0;d<h;){var f=s[d++];switch(1===d&&(i=s[d],r=s[d+1],e=i,n=r),f){case u.M:e=i=s[d++],n=r=s[d++],t.moveTo(i,r);break;case u.L:a=s[d++],o=s[d++],(x(a-i)>l||x(o-r)>c||d===h-1)&&(t.lineTo(a,o),i=a,r=o);break;case u.C:t.bezierCurveTo(s[d++],s[d++],s[d++],s[d++],s[d++],s[d++]),i=s[d-2],r=s[d-1];break;case u.Q:t.quadraticCurveTo(s[d++],s[d++],s[d++],s[d++]),i=s[d-2],r=s[d-1];break;case u.A:var p=s[d++],g=s[d++],y=s[d++],_=s[d++],b=s[d++],w=s[d++],S=s[d++],M=s[d++],T=y>_?y:_,C=y>_?1:y/_,I=y>_?_/y:1,D=Math.abs(y-_)>.001,A=b+w;D?(t.translate(p,g),t.rotate(S),t.scale(C,I),t.arc(0,0,T,b,A,1-M),t.scale(1/C,1/I),t.rotate(-S),t.translate(-p,-g)):t.arc(p,g,T,b,A,1-M),1===d&&(e=v(b)*y+p,n=m(b)*_+g),i=v(A)*y+p,r=m(A)*_+g;break;case u.R:e=i=s[d],n=r=s[d+1],t.rect(s[d++],s[d++],s[d++],s[d++]);break;case u.Z:t.closePath(),i=e,r=n}}}},b.CMD=u;var w=b;t.exports=w},"216a":function(t,e,n){var i=n("6d8b"),r=n("3842"),a=n("eda2"),o=n("944e"),s=n("89e3"),l=s.prototype,u=Math.ceil,c=Math.floor,h=1e3,d=60*h,f=60*d,p=24*f,g=function(t,e,n,i){while(n<i){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n},v=s.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return a.formatTime(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=p,e[1]+=p),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-p}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=r.round(c(e[0]/i)*i)),t.fixMax||(e[1]=r.round(u(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,a=i[1]-i[0],s=a/t;null!=e&&s<e&&(s=e),null!=n&&s>n&&(s=n);var l=m.length,h=g(m,s,0,l),d=m[Math.min(h,l-1)],f=d[1];if("year"===d[0]){var p=a/f,v=r.nice(p/t,!0);f*=v}var y=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,x=[Math.round(u((i[0]-y)/f)*f+y),Math.round(c((i[1]-y)/f)*f+y)];o.fixExtent(x,i),this._stepLvl=d,this._interval=f,this._niceExtent=x},parse:function(t){return+r.parseDate(t)}});i.each(["contain","normalize"],function(t){v.prototype[t]=function(e){return l[t].call(this,this.parse(e))}});var m=[["hh:mm:ss",h],["hh:mm:ss",5*h],["hh:mm:ss",10*h],["hh:mm:ss",15*h],["hh:mm:ss",30*h],["hh:mm\nMM-dd",d],["hh:mm\nMM-dd",5*d],["hh:mm\nMM-dd",10*d],["hh:mm\nMM-dd",15*d],["hh:mm\nMM-dd",30*d],["hh:mm\nMM-dd",f],["hh:mm\nMM-dd",2*f],["hh:mm\nMM-dd",6*f],["hh:mm\nMM-dd",12*f],["MM-dd\nyyyy",p],["MM-dd\nyyyy",2*p],["MM-dd\nyyyy",3*p],["MM-dd\nyyyy",4*p],["MM-dd\nyyyy",5*p],["MM-dd\nyyyy",6*p],["week",7*p],["MM-dd\nyyyy",10*p],["week",14*p],["week",21*p],["month",31*p],["week",42*p],["month",62*p],["week",70*p],["quarter",95*p],["month",31*p*4],["month",31*p*5],["half-year",380*p/2],["month",31*p*8],["month",31*p*10],["year",380*p]];v.create=function(t){return new v({useUTC:t.ecModel.get("useUTC")})};var y=v;t.exports=y},"217b":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("3301")),a=n("4f85"),o=a.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return r(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});t.exports=o},"22d1":function(t,e){var n={};n="object"===typeof wx&&"function"===typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"===typeof document&&"undefined"!==typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"===typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:r(navigator.userAgent);var i=n;function r(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),a&&(n.edge=!0,n.version=a[1]),o&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!==typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11),domSupported:"undefined"!==typeof document}}t.exports=i},2306:function(t,e,n){var i=n("6d8b"),r=n("342d"),a=n("41ef"),o=n("1687"),s=n("401b"),l=n("cbe5"),u=n("0cde"),c=n("0da8");e.Image=c;var h=n("e1fc");e.Group=h;var d=n("76a5");e.Text=d;var f=n("d9fc");e.Circle=f;var p=n("4aa2");e.Sector=p;var g=n("4573");e.Ring=g;var v=n("87b1");e.Polygon=v;var m=n("d498");e.Polyline=m;var y=n("c7a2");e.Rect=y;var x=n("cb11");e.Line=x;var _=n("ac0f");e.BezierCurve=_;var b=n("8d32");e.Arc=b;var w=n("d4c6");e.CompoundPath=w;var S=n("48a9");e.LinearGradient=S;var M=n("dded");e.RadialGradient=M;var T=n("9850");e.BoundingRect=T;var C=n("392f");e.IncrementalDisplayable=C;var I=n("9cf9"),D=Math.max,A=Math.min,k={},O=1,P={color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},L="emphasis",E="normal",R=1,N={},B={};function z(t){return l.extend(t)}function F(t,e){return r.extendFromString(t,e)}function V(t,e){B[t]=e}function H(t){if(B.hasOwnProperty(t))return B[t]}function W(t,e,n,i){var a=r.createFromString(t,e);return n&&("center"===i&&(n=j(n,a.getBoundingRect())),U(a,n)),a}function G(t,e,n){var i=new c({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(j(e,r))}}});return i}function j(t,e){var n,i=e.width/e.height,r=t.height*i;r<=t.width?n=t.height:(r=t.width,n=r/i);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-n/2,width:r,height:n}}var Y=r.mergePath;function U(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}}function $(t){return I.subPixelOptimizeLine(t.shape,t.shape,t.style),t}function X(t){return I.subPixelOptimizeRect(t.shape,t.shape,t.style),t}var q=I.subPixelOptimize;function Z(t){return null!=t&&"none"!==t}var K=i.createHashMap(),J=0;function Q(t){if("string"!==typeof t)return t;var e=K.get(t);return e||(e=a.lift(t,-.1),J<1e4&&(K.set(t,e),J++)),e}function tt(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(e){var n=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var i=t.style;for(var r in e)null!=e[r]&&(n[r]=i[r]);n.fill=i.fill,n.stroke=i.stroke}else t.__cachedNormalStl=t.__cachedNormalZ2=null}}function et(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var n=t.__zr,i=t.useHoverLayer&&n&&"canvas"===n.painter.type;if(t.__highlighted=i?"layer":"plain",!(t.isGroup||!n&&t.useHoverLayer)){var r=t,a=t.style;i&&(r=n.addHover(t),a=r.style),Mt(a),i||tt(r),a.extendFrom(e),nt(a,e,"fill"),nt(a,e,"stroke"),St(a),i||(t.dirty(!1),t.z2+=O)}}}function nt(t,e,n){!Z(e[n])&&Z(t[n])&&(t[n]=Q(t[n]))}function it(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var n=t.style,i=t.__cachedNormalStl;i&&(Mt(n),t.setStyle(i),St(n));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===O&&(t.z2=r)}}function rt(t,e,n){var i,r=E,a=E;t.__highlighted&&(r=L,i=!0),e(t,n),t.__highlighted&&(a=L,i=!0),t.isGroup&&t.traverse(function(t){!t.isGroup&&e(t,n)}),i&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,a)}function at(t,e){e=t.__hoverStl=!1!==e&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,it(t),et(t))}function ot(t){!ct(this,t)&&!this.__highByOuter&&rt(this,et)}function st(t){!ct(this,t)&&!this.__highByOuter&&rt(this,it)}function lt(t){this.__highByOuter|=1<<(t||0),rt(this,et)}function ut(t){!(this.__highByOuter&=~(1<<(t||0)))&&rt(this,it)}function ct(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function ht(t,e){dt(t,!0),rt(t,at,e)}function dt(t,e){var n=!1===e;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!n||t.__highDownDispatcher){var i=n?"off":"on";t[i]("mouseover",ot)[i]("mouseout",st),t[i]("emphasis",lt)[i]("normal",ut),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!n}}function ft(t){return!(!t||!t.__highDownDispatcher)}function pt(t){var e=N[t];return null==e&&R<=32&&(e=N[t]=R++),e}function gt(t,e,n,r,a,o,s){a=a||k;var l,u=a.labelFetcher,c=a.labelDataIndex,h=a.labelDimIndex,d=n.getShallow("show"),f=r.getShallow("show");(d||f)&&(u&&(l=u.getFormattedLabel(c,"normal",null,h)),null==l&&(l=i.isFunction(a.defaultText)?a.defaultText(c,a):a.defaultText));var p=d?l:null,g=f?i.retrieve2(u?u.getFormattedLabel(c,"emphasis",null,h):null,l):null;null==p&&null==g||(mt(t,n,o,a),mt(e,r,s,a,!0)),t.text=p,e.text=g}function vt(t,e,n){var r=t.style;e&&(Mt(r),t.setStyle(e),St(r)),r=t.__hoverStl,n&&r&&(Mt(r),i.extend(r,n),St(r))}function mt(t,e,n,r,a){return xt(t,e,r,a),n&&i.extend(t,n),t}function yt(t,e,n){var i,r={isRectText:!0};!1===n?i=!0:r.autoColor=n,xt(t,e,r,i)}function xt(t,e,n,r){if(n=n||k,n.isRectText){var a;n.getTextPosition?a=n.getTextPosition(e,r):(a=e.getShallow("position")||(r?null:"inside"),"outside"===a&&(a="top")),t.textPosition=a,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=i.retrieve2(e.getShallow("distance"),r?null:5)}var s,l=e.ecModel,u=l&&l.option.textStyle,c=_t(e);if(c)for(var h in s={},c)if(c.hasOwnProperty(h)){var d=e.getModel(["rich",h]);bt(s[h]={},d,u,n,r)}return t.rich=s,bt(t,e,u,n,r,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function _t(t){var e;while(t&&t!==t.ecModel){var n=(t.option||k).rich;if(n)for(var i in e=e||{},n)n.hasOwnProperty(i)&&(e[i]=1);t=t.parentModel}return e}function bt(t,e,n,r,a,o){n=!a&&n||k,t.textFill=wt(e.getShallow("color"),r)||n.color,t.textStroke=wt(e.getShallow("textBorderColor"),r)||n.textBorderColor,t.textStrokeWidth=i.retrieve2(e.getShallow("textBorderWidth"),n.textBorderWidth),a||(o&&(t.insideRollbackOpt=r,St(t)),null==t.textFill&&(t.textFill=r.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&r.disableBox||(t.textBackgroundColor=wt(e.getShallow("backgroundColor"),r),t.textPadding=e.getShallow("padding"),t.textBorderColor=wt(e.getShallow("borderColor"),r),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function wt(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function St(t){var e,n=t.textPosition,i=t.insideRollbackOpt;if(i&&null==t.textFill){var r=i.autoColor,a=i.isRectText,o=i.useInsideStyle,s=!1!==o&&(!0===o||a&&n&&"string"===typeof n&&n.indexOf("inside")>=0),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function Mt(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Tt(t,e){var n=e&&e.getModel("textStyle");return i.trim([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function Ct(t,e,n,i,r,a){"function"===typeof r&&(a=r,r=null);var o=i&&i.isAnimationEnabled();if(o){var s=t?"Update":"",l=i.getShallow("animationDuration"+s),u=i.getShallow("animationEasing"+s),c=i.getShallow("animationDelay"+s);"function"===typeof c&&(c=c(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"===typeof l&&(l=l(r)),l>0?e.animateTo(n,l,c||0,u,a,!!a):(e.stopAnimation(),e.attr(n),a&&a())}else e.stopAnimation(),e.attr(n),a&&a()}function It(t,e,n,i,r){Ct(!0,t,e,n,i,r)}function Dt(t,e,n,i,r){Ct(!1,t,e,n,i,r)}function At(t,e){var n=o.identity([]);while(t&&t!==e)o.mul(n,t.getLocalTransform(),n),t=t.parent;return n}function kt(t,e,n){return e&&!i.isArrayLike(e)&&(e=u.getLocalTransform(e)),n&&(e=o.invert([],e)),s.applyTransform([],t,e)}function Ot(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return a=kt(a,e,n),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function Pt(t,e,n,r){if(t&&e){var a=o(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var i=l(t);t.attr(l(e)),It(t,i,n,t.dataIndex)}}})}function o(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function l(t){var e={position:s.clone(t.position),rotation:t.rotation};return t.shape&&(e.shape=i.extend({},t.shape)),e}}function Lt(t,e){return i.map(t,function(t){var n=t[0];n=D(n,e.x),n=A(n,e.x+e.width);var i=t[1];return i=D(i,e.y),i=A(i,e.y+e.height),[n,i]})}function Et(t,e){var n=D(t.x,e.x),i=A(t.x+t.width,e.x+e.width),r=D(t.y,e.y),a=A(t.y+t.height,e.y+e.height);if(i>=n&&a>=r)return{x:n,y:r,width:i-n,height:a-r}}function Rt(t,e,n){e=i.extend({rectHover:!0},e);var r=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),i.defaults(r,n),new c(e)):W(t.replace("path://",""),e,n,"center")}function Nt(t,e,n,i,r){for(var a=0,o=r[r.length-1];a<r.length;a++){var s=r[a];if(Bt(t,e,n,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function Bt(t,e,n,i,r,a,o,s){var l=n-t,u=i-e,c=o-r,h=s-a,d=zt(c,h,l,u);if(Ft(d))return!1;var f=t-r,p=e-a,g=zt(f,p,l,u)/d;if(g<0||g>1)return!1;var v=zt(f,p,c,h)/d;return!(v<0||v>1)}function zt(t,e,n,i){return t*i-n*e}function Ft(t){return t<=1e-6&&t>=-1e-6}V("circle",f),V("sector",p),V("ring",g),V("polygon",v),V("polyline",m),V("rect",y),V("line",x),V("bezierCurve",_),V("arc",b),e.Z2_EMPHASIS_LIFT=O,e.CACHED_LABEL_STYLE_PROPERTIES=P,e.extendShape=z,e.extendPath=F,e.registerShape=V,e.getShapeClass=H,e.makePath=W,e.makeImage=G,e.mergePath=Y,e.resizePath=U,e.subPixelOptimizeLine=$,e.subPixelOptimizeRect=X,e.subPixelOptimize=q,e.setElementHoverStyle=at,e.setHoverStyle=ht,e.setAsHighDownDispatcher=dt,e.isHighDownDispatcher=ft,e.getHighlightDigit=pt,e.setLabelStyle=gt,e.modifyLabelStyle=vt,e.setTextStyle=mt,e.setText=yt,e.getFont=Tt,e.updateProps=It,e.initProps=Dt,e.getTransform=At,e.applyTransform=kt,e.transformDirection=Ot,e.groupTransition=Pt,e.clipPointsByRect=Lt,e.clipRectByRect=Et,e.createIcon=Rt,e.linePolygonIntersect=Nt,e.lineLineIntersect=Bt},"26e1":function(t,e,n){var i=n("6d8b"),r=n("e0d3"),a=i.each,o=i.isObject,s=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function l(t){var e=t&&t.itemStyle;if(e)for(var n=0,r=s.length;n<r;n++){var a=s[n],o=e.normal,l=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?i.merge(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),l&&l[a]&&(t[a]=t[a]||{},t[a].emphasis?i.merge(t[a].emphasis,l[a]):t[a].emphasis=l[a],l[a]=null)}}function u(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var r=t[e].normal,a=t[e].emphasis;r&&(n?(t[e].normal=t[e].emphasis=null,i.defaults(t[e],r)):t[e]=r),a&&(t.emphasis=t.emphasis||{},t.emphasis[e]=a)}}function c(t){u(t,"itemStyle"),u(t,"lineStyle"),u(t,"areaStyle"),u(t,"label"),u(t,"labelLine"),u(t,"upperLabel"),u(t,"edgeLabel")}function h(t,e){var n=o(t)&&t[e],i=o(n)&&n.textStyle;if(i)for(var a=0,s=r.TEXT_STYLE_OPTIONS.length;a<s;a++){e=r.TEXT_STYLE_OPTIONS[a];i.hasOwnProperty(e)&&(n[e]=i[e])}}function d(t){t&&(c(t),h(t,"label"),t.emphasis&&h(t.emphasis,"label"))}function f(t){if(o(t)){l(t),c(t),h(t,"label"),h(t,"upperLabel"),h(t,"edgeLabel"),t.emphasis&&(h(t.emphasis,"label"),h(t.emphasis,"upperLabel"),h(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(l(e),d(e));var n=t.markLine;n&&(l(n),d(n));var r=t.markArea;r&&d(r);var a=t.data;if("graph"===t.type){a=a||t.nodes;var s=t.links||t.edges;if(s&&!i.isTypedArray(s))for(var f=0;f<s.length;f++)d(s[f]);i.each(t.categories,function(t){c(t)})}if(a&&!i.isTypedArray(a))for(f=0;f<a.length;f++)d(a[f]);e=t.markPoint;if(e&&e.data){var p=e.data;for(f=0;f<p.length;f++)d(p[f])}n=t.markLine;if(n&&n.data){var g=n.data;for(f=0;f<g.length;f++)i.isArray(g[f])?(d(g[f][0]),d(g[f][1])):d(g[f])}"gauge"===t.type?(h(t,"axisLabel"),h(t,"title"),h(t,"detail")):"treemap"===t.type?(u(t.breadcrumb,"itemStyle"),i.each(t.levels,function(t){c(t)})):"tree"===t.type&&c(t.leaves)}}function p(t){return i.isArray(t)?t:t?[t]:[]}function g(t){return(i.isArray(t)?t[0]:t)||{}}function v(t,e){a(p(t.series),function(t){o(t)&&f(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),a(n,function(e){a(p(t[e]),function(t){t&&(h(t,"axisLabel"),h(t.axisPointer,"label"))})}),a(p(t.parallel),function(t){var e=t&&t.parallelAxisDefault;h(e,"axisLabel"),h(e&&e.axisPointer,"label")}),a(p(t.calendar),function(t){u(t,"itemStyle"),h(t,"dayLabel"),h(t,"monthLabel"),h(t,"yearLabel")}),a(p(t.radar),function(t){h(t,"name")}),a(p(t.geo),function(t){o(t)&&(d(t),a(p(t.regions),function(t){d(t)}))}),a(p(t.timeline),function(t){d(t),u(t,"label"),u(t,"itemStyle"),u(t,"controlStyle",!0);var e=t.data;i.isArray(e)&&i.each(e,function(t){i.isObject(t)&&(u(t,"label"),u(t,"itemStyle"))})}),a(p(t.toolbox),function(t){u(t,"iconStyle"),a(t.feature,function(t){u(t,"iconStyle")})}),h(g(t.axisPointer),"label"),h(g(t.tooltip).axisPointer,"label")}t.exports=v},"282b":function(t,e,n){var i=n("6d8b");function r(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,n,r){for(var a={},o=0;o<t.length;o++){var s=t[o][1];if(!(n&&i.indexOf(n,s)>=0||r&&i.indexOf(r,s)<0)){var l=e.getShallow(s);null!=l&&(a[t[o][0]]=l)}}return a}}t.exports=r},"29a8":function(t,e){var n={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};t.exports=n},"2b17":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=(r.isTypedArray,r.extend),o=(r.assert,r.each),s=r.isObject,l=n("e0d3"),u=l.getDataItemValue,c=l.isDataItemOption,h=n("3842"),d=h.parseDate,f=n("ec6f"),p=n("93d0"),g=p.SOURCE_FORMAT_TYPED_ARRAY,v=p.SOURCE_FORMAT_ARRAY_ROWS,m=p.SOURCE_FORMAT_ORIGINAL,y=p.SOURCE_FORMAT_OBJECT_ROWS;function x(t,e){f.isInstance(t)||(t=f.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;i===g&&(this._offset=0,this._dimSize=e,this._data=n);var r=b[i===v?i+"_"+t.seriesLayoutBy:i];a(this,r)}var _=x.prototype;_.pure=!1,_.persistent=!0,_.getSource=function(){return this._source};var b={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:M},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:w,getItem:S,appendData:M},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;o(t,function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])})}},original:{count:w,getItem:S,appendData:M},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}};function w(){return this._data.length}function S(t){return this._data[t]}function M(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}var T={arrayRows:C,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:C,original:function(t,e,n,i){var r=u(t);return null!=n&&r instanceof Array?r[n]:r},typedArray:C};function C(t,e,n,i){return null!=n?t[n]:t}var I={arrayRows:D,objectRows:function(t,e,n,i){return A(t[e],this._dimensionInfos[e])},keyedColumns:D,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&c(t)&&(this.hasItemOption=!0),A(r instanceof Array?r[i]:r,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}};function D(t,e,n,i){return A(t[i],this._dimensionInfos[e])}function A(t,e){var n=e&&e.type;if("ordinal"===n){var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}return"time"===n&&"number"!==typeof t&&null!=t&&"-"!==t&&(t=+d(t)),null==t||""===t?NaN:+t}function k(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,a=s.index),T[o](i,e,a,r)}}}function O(t,e,n){if(t){var i=t.getProvider().getSource().sourceFormat;if(i===m||i===y){var r=t.getRawDataItem(e);return i!==m||s(r)||(r=null),r?r[n]:void 0}}}e.DefaultDataProvider=x,e.defaultDimValueGetters=I,e.retrieveRawValue=k,e.retrieveRawAttr=O},"2b61":function(t,e,n){var i=n("7d6d"),r=n("82eb"),a=r.ContextCachedBy,o=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],s=function(t){this.extendFrom(t,!1)};function l(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(i=i*n.width+n.x,r=r*n.width+n.x,a=a*n.height+n.y,o=o*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(i,a,r,o);return s}function u(t,e,n){var i=n.width,r=n.height,a=Math.min(i,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*i+n.x,s=s*r+n.y,l*=a);var u=t.createRadialGradient(o,s,0,o,s,l);return u}s.prototype={constructor:s,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var r=this,s=n&&n.style,l=!s||t.__attrCachedBy!==a.STYLE_BIND;t.__attrCachedBy=a.STYLE_BIND;for(var u=0;u<o.length;u++){var c=o[u],h=c[0];(l||r[h]!==s[h])&&(t[h]=i(t,h,r[h]||c[1]))}if((l||r.fill!==s.fill)&&(t.fillStyle=r.fill),(l||r.stroke!==s.stroke)&&(t.strokeStyle=r.stroke),(l||r.opacity!==s.opacity)&&(t.globalAlpha=null==r.opacity?1:r.opacity),(l||r.blend!==s.blend)&&(t.globalCompositeOperation=r.blend||"source-over"),this.hasStroke()){var d=r.lineWidth;t.lineWidth=d/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||!0!==e&&(!1===e?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"===typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i="radial"===e.type?u:l,r=i(t,e,n),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var c=s.prototype,h=0;h<o.length;h++){var d=o[h];d[0]in c||(c[d[0]]=d[1])}s.getGradient=c.getGradient;var f=s;t.exports=f},"2cf4":function(t,e){var n=1;"undefined"!==typeof window&&(n=Math.max(window.devicePixelRatio||1,1));var i=0,r=n;e.debugMode=i,e.devicePixelRatio=r},"2f45":function(t,e,n){var i=n("6d8b"),r=i.each,a=i.createHashMap,o=(i.assert,n("4e08")),s=(o.__DEV__,a(["tooltip","label","itemName","itemId","seriesName"]));function l(t){var e={},n=e.encode={},i=a(),o=[],l=[],c=e.userOutput={dimensionNames:t.dimensions.slice(),encode:{}};r(t.dimensions,function(e){var r=t.getDimensionInfo(e),a=r.coordDim;if(a){var d=r.coordDimIndex;u(n,a)[d]=e,r.isExtraCoord||(i.set(a,1),h(r.type)&&(o[0]=e),u(c.encode,a)[d]=r.index),r.defaultTooltip&&l.push(e)}s.each(function(t,e){var i=u(n,e),a=r.otherDims[e];null!=a&&!1!==a&&(i[a]=r.name)})});var d=[],f={};i.each(function(t,e){var i=n[e];f[e]=i[0],d=d.concat(i)}),e.dataDimsOnCoord=d,e.encodeFirstDimNotExtra=f;var p=n.label;p&&p.length&&(o=p.slice());var g=n.tooltip;return g&&g.length?l=g.slice():l.length||(l=o.slice()),n.defaultedLabel=o,n.defaultedTooltip=l,e}function u(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function c(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function h(t){return!("ordinal"===t||"time"===t)}e.OTHER_DIMENSIONS=s,e.summarizeDimensions=l,e.getDimensionTypeByAxis=c},3014:function(t,e,n){var i=n("4f85"),r=n("3301"),a=i.extend({type:"series.__base_bar__",getInitialData:function(t,e){return r(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),a=i.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return n[o]+=r+a/2,n}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});t.exports=a},3041:function(t,e,n){var i=n("e1fc"),r=n("0da8"),a=n("76a5"),o=n("d9fc"),s=n("c7a2"),l=n("ae69"),u=n("cb11"),c=n("cbe5"),h=n("87b1"),d=n("d498"),f=n("48a9"),p=n("2b61"),g=n("1687"),v=n("342d"),m=v.createFromString,y=n("6d8b"),x=y.isString,_=y.extend,b=y.defaults,w=y.trim,S=y.each,M=/[\s,]+/;function T(t){if(x(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}9===t.nodeType&&(t=t.firstChild);while("svg"!==t.nodeName.toLowerCase()||1!==t.nodeType)t=t.nextSibling;return t}function C(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}C.prototype.parse=function(t,e){e=e||{};var n=T(t);if(!n)throw new Error("Illegal svg");var r=new i;this._root=r;var a=n.getAttribute("viewBox")||"",o=parseFloat(n.getAttribute("width")||e.width),l=parseFloat(n.getAttribute("height")||e.height);isNaN(o)&&(o=null),isNaN(l)&&(l=null),L(n,r,null,!0);var u,c,h=n.firstChild;while(h)this._parseNode(h,r),h=h.nextSibling;if(a){var d=w(a).split(M);d.length>=4&&(u={x:parseFloat(d[0]||0),y:parseFloat(d[1]||0),width:parseFloat(d[2]),height:parseFloat(d[3])})}if(u&&null!=o&&null!=l&&(c=V(u,o,l),!e.ignoreViewBox)){var f=r;r=new i,r.add(f),f.scale=c.scale.slice(),f.position=c.position.slice()}return e.ignoreRootClip||null==o||null==l||r.setClipPath(new s({shape:{x:0,y:0,width:o,height:l}})),{root:r,width:o,height:l,viewBoxRect:u,viewBoxTransform:c}},C.prototype._parseNode=function(t,e){var n,i=t.nodeName.toLowerCase();if("defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0),this._isDefine){var r=D[i];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{r=I[i];r&&(n=r.call(this,t,e),e.add(n))}var s=t.firstChild;while(s)1===s.nodeType&&this._parseNode(s,n),3===s.nodeType&&this._isText&&this._parseText(s,n),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},C.prototype._parseText=function(t,e){if(1===t.nodeType){var n=t.getAttribute("dx")||0,i=t.getAttribute("dy")||0;this._textX+=parseFloat(n),this._textY+=parseFloat(i)}var r=new a({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});k(e,r),L(t,r,this._defs);var o=r.style.fontSize;o&&o<9&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=o/9,r.scale[1]*=o/9);var s=r.getBoundingRect();return this._textX+=s.width,e.add(r),r};var I={g:function(t,e){var n=new i;return k(e,n),L(t,n,this._defs),n},rect:function(t,e){var n=new s;return k(e,n),L(t,n,this._defs),n.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),n},circle:function(t,e){var n=new o;return k(e,n),L(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),n},line:function(t,e){var n=new u;return k(e,n),L(t,n,this._defs),n.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),n},ellipse:function(t,e){var n=new l;return k(e,n),L(t,n,this._defs),n.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),n},polygon:function(t,e){var n=t.getAttribute("points");n&&(n=O(n));var i=new h({shape:{points:n||[]}});return k(e,i),L(t,i,this._defs),i},polyline:function(t,e){var n=new c;k(e,n),L(t,n,this._defs);var i=t.getAttribute("points");i&&(i=O(i));var r=new d({shape:{points:i||[]}});return r},image:function(t,e){var n=new r;return k(e,n),L(t,n,this._defs),n.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),n},text:function(t,e){var n=t.getAttribute("x")||0,r=t.getAttribute("y")||0,a=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0;this._textX=parseFloat(n)+parseFloat(a),this._textY=parseFloat(r)+parseFloat(o);var s=new i;return k(e,s),L(t,s,this._defs),s},tspan:function(t,e){var n=t.getAttribute("x"),r=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=r&&(this._textY=parseFloat(r));var a=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0,s=new i;return k(e,s),L(t,s,this._defs),this._textX+=a,this._textY+=o,s},path:function(t,e){var n=t.getAttribute("d")||"",i=m(n);return k(e,i),L(t,i,this._defs),i}},D={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),n=parseInt(t.getAttribute("y1")||0,10),i=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new f(e,n,i,r);return A(t,a),a},radialgradient:function(t){}};function A(t,e){var n=t.firstChild;while(n){if(1===n.nodeType){var i=n.getAttribute("offset");i=i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var r=n.getAttribute("stop-color")||"#000000";e.addColorStop(i,r)}n=n.nextSibling}}function k(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),b(e.__inheritedStyle,t.__inheritedStyle))}function O(t){for(var e=w(t).split(M),n=[],i=0;i<e.length;i+=2){var r=parseFloat(e[i]),a=parseFloat(e[i+1]);n.push([r,a])}return n}var P={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"};function L(t,e,n,i){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(B(t,e),_(r,F(t)),!i))for(var o in P)if(P.hasOwnProperty(o)){var s=t.getAttribute(o);null!=s&&(r[P[o]]=s)}var l=a?"textFill":"fill",u=a?"textStroke":"stroke";e.style=e.style||new p;var c=e.style;null!=r.fill&&c.set(l,R(r.fill,n)),null!=r.stroke&&c.set(u,R(r.stroke,n)),S(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),S(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=w(r.lineDash).split(M)),c[u]&&"none"!==c[u]&&(e[u]=!0),e.__inheritedStyle=r}var E=/url\(\s*#(.*?)\)/;function R(t,e){var n=e&&t&&t.match(E);if(n){var i=w(n[1]),r=e[i];return r}return t}var N=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g;function B(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var i=null,r=[];n.replace(N,function(t,e,n){r.push(e,n)});for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(i=i||g.create(),s){case"translate":o=w(o).split(M),g.translate(i,i,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=w(o).split(M),g.scale(i,i,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=w(o).split(M),g.rotate(i,i,parseFloat(o[0]));break;case"skew":o=w(o).split(M),console.warn("Skew transform is not supported yet");break;case"matrix":o=w(o).split(M);i[0]=parseFloat(o[0]),i[1]=parseFloat(o[1]),i[2]=parseFloat(o[2]),i[3]=parseFloat(o[3]),i[4]=parseFloat(o[4]),i[5]=parseFloat(o[5]);break}}e.setLocalTransform(i)}}var z=/([^\s:;]+)\s*:\s*([^:;]+)/g;function F(t){var e=t.getAttribute("style"),n={};if(!e)return n;var i,r={};z.lastIndex=0;while(null!=(i=z.exec(e)))r[i[1]]=i[2];for(var a in P)P.hasOwnProperty(a)&&null!=r[a]&&(n[P[a]]=r[a]);return n}function V(t,e,n){var i=e/t.width,r=n/t.height,a=Math.min(i,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+n/2];return{scale:o,position:s}}function H(t,e){var n=new C;return n.parse(t,e)}e.parseXML=T,e.makeViewBoxTransform=V,e.parseSVG=H},"30a3":function(t,e,n){var i=n("6d8b"),r=n("607d"),a=r.Dispatcher,o=n("98b7"),s=n("06ad"),l=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,a.call(this)};l.prototype={constructor:l,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=i.indexOf(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],a=[],o=0;o<i;o++){var s=n[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(o=0;o<i;)n[o]._needsRemove?(n[o]=n[i-1],n.pop(),i--):o++;i=r.length;for(o=0;o<i;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var t=this;function e(){t._running&&(o(e),!t._paused&&t._update())}this._running=!0,o(e)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var n=new s(t,e.loop,e.getter,e.setter);return this.addAnimator(n),n}},i.mixin(l,a);var u=l;t.exports=u},3301:function(t,e,n){var i=n("6d8b"),r=n("6179"),a=n("b1d4"),o=n("93d0"),s=o.SOURCE_FORMAT_ORIGINAL,l=n("2f45"),u=l.getDimensionTypeByAxis,c=n("e0d3"),h=c.getDataItemValue,d=n("2039"),f=n("8b7f"),p=f.getCoordSysInfoBySeries,g=n("ec6f"),v=n("ee1a"),m=v.enableDataStack,y=n("0f99"),x=y.makeSeriesEncodeForAxisCoordSys;function _(t,e,n){n=n||{},g.isInstance(t)||(t=g.seriesDataToSource(t));var o,s=e.get("coordinateSystem"),l=d.get(s),c=p(e);c&&(o=i.map(c.coordSysDims,function(t){var e={name:t},n=c.axisMap.get(t);if(n){var i=n.get("type");e.type=u(i)}return e})),o||(o=l&&(l.getDimensionsInfo?l.getDimensionsInfo():l.dimensions.slice())||["x","y"]);var h,f,v=a(t,{coordDimensions:o,generateCoord:n.generateCoord,encodeDefaulter:n.useEncodeDefaulter?i.curry(x,o,e):null});c&&i.each(v,function(t,e){var n=t.coordDim,i=c.categoryAxisMap.get(n);i&&(null==h&&(h=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(f=!0)}),f||null==h||(v[h].otherDims.itemName=0);var y=m(e,v),_=new r(v,e);_.setCalculationInfo(y);var w=null!=h&&b(t)?function(t,e,n,i){return i===h?n:this.defaultDimValueGetter(t,e,n,i)}:null;return _.hasItemOption=!1,_.initData(t,null,w),_}function b(t){if(t.sourceFormat===s){var e=w(t.data||[]);return null!=e&&!i.isArray(h(e))}}function w(t){var e=0;while(e<t.length&&null==t[e])e++;return t[e]}var S=_;t.exports=S},"342d":function(t,e,n){var i=n("cbe5"),r=n("20c8"),a=n("ee84"),o=Math.sqrt,s=Math.sin,l=Math.cos,u=Math.PI,c=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},h=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(c(t)*c(e))},d=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(h(t,e))};function f(t,e,n,i,r,a,c,f,p,g,v){var m=p*(u/180),y=l(m)*(t-n)/2+s(m)*(e-i)/2,x=-1*s(m)*(t-n)/2+l(m)*(e-i)/2,_=y*y/(c*c)+x*x/(f*f);_>1&&(c*=o(_),f*=o(_));var b=(r===a?-1:1)*o((c*c*(f*f)-c*c*(x*x)-f*f*(y*y))/(c*c*(x*x)+f*f*(y*y)))||0,w=b*c*x/f,S=b*-f*y/c,M=(t+n)/2+l(m)*w-s(m)*S,T=(e+i)/2+s(m)*w+l(m)*S,C=d([1,0],[(y-w)/c,(x-S)/f]),I=[(y-w)/c,(x-S)/f],D=[(-1*y-w)/c,(-1*x-S)/f],A=d(I,D);h(I,D)<=-1&&(A=u),h(I,D)>=1&&(A=0),0===a&&A>0&&(A-=2*u),1===a&&A<0&&(A+=2*u),v.addData(g,M,T,c,f,C,A,m,a)}var p=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,g=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function v(t){if(!t)return new r;for(var e,n=0,i=0,a=n,o=i,s=new r,l=r.CMD,u=t.match(p),c=0;c<u.length;c++){for(var h,d=u[c],v=d.charAt(0),m=d.match(g)||[],y=m.length,x=0;x<y;x++)m[x]=parseFloat(m[x]);var _=0;while(_<y){var b,w,S,M,T,C,I,D=n,A=i;switch(v){case"l":n+=m[_++],i+=m[_++],h=l.L,s.addData(h,n,i);break;case"L":n=m[_++],i=m[_++],h=l.L,s.addData(h,n,i);break;case"m":n+=m[_++],i+=m[_++],h=l.M,s.addData(h,n,i),a=n,o=i,v="l";break;case"M":n=m[_++],i=m[_++],h=l.M,s.addData(h,n,i),a=n,o=i,v="L";break;case"h":n+=m[_++],h=l.L,s.addData(h,n,i);break;case"H":n=m[_++],h=l.L,s.addData(h,n,i);break;case"v":i+=m[_++],h=l.L,s.addData(h,n,i);break;case"V":i=m[_++],h=l.L,s.addData(h,n,i);break;case"C":h=l.C,s.addData(h,m[_++],m[_++],m[_++],m[_++],m[_++],m[_++]),n=m[_-2],i=m[_-1];break;case"c":h=l.C,s.addData(h,m[_++]+n,m[_++]+i,m[_++]+n,m[_++]+i,m[_++]+n,m[_++]+i),n+=m[_-2],i+=m[_-1];break;case"S":b=n,w=i;var k=s.len(),O=s.data;e===l.C&&(b+=n-O[k-4],w+=i-O[k-3]),h=l.C,D=m[_++],A=m[_++],n=m[_++],i=m[_++],s.addData(h,b,w,D,A,n,i);break;case"s":b=n,w=i;k=s.len(),O=s.data;e===l.C&&(b+=n-O[k-4],w+=i-O[k-3]),h=l.C,D=n+m[_++],A=i+m[_++],n+=m[_++],i+=m[_++],s.addData(h,b,w,D,A,n,i);break;case"Q":D=m[_++],A=m[_++],n=m[_++],i=m[_++],h=l.Q,s.addData(h,D,A,n,i);break;case"q":D=m[_++]+n,A=m[_++]+i,n+=m[_++],i+=m[_++],h=l.Q,s.addData(h,D,A,n,i);break;case"T":b=n,w=i;k=s.len(),O=s.data;e===l.Q&&(b+=n-O[k-4],w+=i-O[k-3]),n=m[_++],i=m[_++],h=l.Q,s.addData(h,b,w,n,i);break;case"t":b=n,w=i;k=s.len(),O=s.data;e===l.Q&&(b+=n-O[k-4],w+=i-O[k-3]),n+=m[_++],i+=m[_++],h=l.Q,s.addData(h,b,w,n,i);break;case"A":S=m[_++],M=m[_++],T=m[_++],C=m[_++],I=m[_++],D=n,A=i,n=m[_++],i=m[_++],h=l.A,f(D,A,n,i,C,I,S,M,T,h,s);break;case"a":S=m[_++],M=m[_++],T=m[_++],C=m[_++],I=m[_++],D=n,A=i,n+=m[_++],i+=m[_++],h=l.A,f(D,A,n,i,C,I,S,M,T,h,s);break}}"z"!==v&&"Z"!==v||(h=l.Z,s.addData(h),n=a,i=o),e=h}return s.toStatic(),s}function m(t,e){var n=v(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e)}else{e=t;n.rebuildPath(e)}},e.applyTransform=function(t){a(n,t),this.dirty(!0)},e}function y(t,e){return new i(m(t,e))}function x(t,e){return i.extend(m(t,e))}function _(t,e){for(var n=[],r=t.length,a=0;a<r;a++){var o=t[a];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var s=new i(e);return s.createPathProxy(),s.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},s}e.createFromString=y,e.extendFromString=x,e.mergePath=_},3842:function(t,e,n){var i=n("6d8b"),r=1e-4;function a(t){return t.replace(/^\s+|\s+$/g,"")}function o(t,e,n,i){var r=e[1]-e[0],a=n[1]-n[0];if(0===r)return 0===a?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*a+n[0]}function s(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%";break}return"string"===typeof t?a(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function l(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function u(t){return t.sort(function(t,e){return t-e}),t}function c(t){if(t=+t,isNaN(t))return 0;var e=1,n=0;while(Math.round(t*e)/e!==t)e*=10,n++;return n}function h(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r}function d(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),a=Math.round(n(Math.abs(e[1]-e[0]))/i),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function f(t,e,n){if(!t[e])return 0;var r=i.reduce(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===r)return 0;var a=Math.pow(10,n),o=i.map(t,function(t){return(isNaN(t)?0:t)/r*a*100}),s=100*a,l=i.map(o,function(t){return Math.floor(t)}),u=i.reduce(l,function(t,e){return t+e},0),c=i.map(o,function(t,e){return t-l[e]});while(u<s){for(var h=Number.NEGATIVE_INFINITY,d=null,f=0,p=c.length;f<p;++f)c[f]>h&&(h=c[f],d=f);++l[d],c[d]=0,++u}return l[e]/a}var p=9007199254740991;function g(t){var e=2*Math.PI;return(t%e+e)%e}function v(t){return t>-r&&t<r}var m=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function y(t){if(t instanceof Date)return t;if("string"===typeof t){var e=m.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return null==t?new Date(NaN):new Date(Math.round(t))}function x(t){return Math.pow(10,_(t))}function _(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function b(t,e){var n,i=_(t),r=Math.pow(10,i),a=t/r;return n=e?a<1.5?1:a<2.5?2:a<4?3:a<7?5:10:a<1?1:a<2?2:a<3?3:a<5?5:10,t=n*r,i>=-20?+t.toFixed(i<0?-i:0):t}function w(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],a=n-i;return a?r+a*(t[i]-r):r}function S(t){t.sort(function(t,e){return s(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,a=t[i].close,o=0;o<2;o++)r[o]<=e&&(r[o]=e,a[o]=o?1:1-n),e=r[o],n=a[o];r[0]===r[1]&&a[0]*a[1]!==1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]===(n?-1:1)||!n&&s(t,e,1))}}function M(t){return t-parseFloat(t)>=0}e.linearMap=o,e.parsePercent=s,e.round=l,e.asc=u,e.getPrecision=c,e.getPrecisionSafe=h,e.getPixelPrecision=d,e.getPercentWithPrecision=f,e.MAX_SAFE_INTEGER=p,e.remRadian=g,e.isRadianAroundZero=v,e.parseDate=y,e.quantity=x,e.quantityExponent=_,e.nice=b,e.quantile=w,e.reformIntervals=S,e.isNumeric=M},"38a2":function(t,e,n){var i=n("2b17"),r=i.retrieveRawValue,a=n("eda2"),o=a.getTooltipMarker,s=a.formatTpl,l=n("e0d3"),u=l.getTooltipRenderMode,c=/\{@(.+?)\}/g,h={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),a=n.getName(t),s=n.getRawDataItem(t),l=n.getItemVisual(t,"color"),c=n.getItemVisual(t,"borderColor"),h=this.ecModel.getComponent("tooltip"),d=h&&h.get("renderMode"),f=u(d),p=this.mainType,g="series"===p,v=n.userOutput;return{componentType:p,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:g?this.subType:null,seriesIndex:this.seriesIndex,seriesId:g?this.id:null,seriesName:g?this.name:null,name:a,dataIndex:r,data:s,dataType:e,value:i,color:l,borderColor:c,dimensionNames:v?v.dimensionNames:null,encode:v?v.encode:null,marker:o({color:l,renderMode:f}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,a){e=e||"normal";var o=this.getData(n),l=o.getItemModel(t),u=this.getDataParams(t,n);null!=i&&u.value instanceof Array&&(u.value=u.value[i]);var h=l.get("normal"===e?[a||"label","formatter"]:[e,a||"label","formatter"]);if("function"===typeof h)return u.status=e,u.dimensionIndex=i,h(u);if("string"===typeof h){var d=s(h,u);return d.replace(c,function(e,n){var i=n.length;return"["===n.charAt(0)&&"]"===n.charAt(i-1)&&(n=+n.slice(1,i-1)),r(o,t,n)})}},getRawValue:function(t,e){return r(this.getData(e),t)},formatTooltip:function(){}};t.exports=h},3901:function(t,e,n){var i=n("282b"),r=i([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),a={getLineStyle:function(t){var e=r(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"!==e&&null!=e&&("dashed"===e?[i,i]:[n,n])}};t.exports=a},"392f":function(t,e,n){var i=n("6d8b"),r=i.inherits,a=n("19eb"),o=n("9850");function s(t){a.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}s.prototype.incremental=!0,s.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},s.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},s.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},s.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},s.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},s.prototype.brush=function(t,e){for(var n=this._cursor;n<this._displayables.length;n++){var i=this._displayables[n];i.beforeBrush&&i.beforeBrush(t),i.brush(t,n===this._cursor?null:this._displayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=n;for(n=0;n<this._temporaryDisplayables.length;n++){i=this._temporaryDisplayables[n];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===n?null:this._temporaryDisplayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var l=[];s.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new o(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(l)),t.union(i)}this._rect=t}return this._rect},s.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();if(i.contain(n[0],n[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},r(s,a);var u=s;t.exports=u},"3eba":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("697e7")),a=n("6d8b"),o=n("41ef"),s=n("22d1"),l=n("04f6"),u=n("1fab"),c=n("7e63"),h=n("843e"),d=n("2039"),f=n("ca98"),p=n("fb05"),g=n("d15d"),v=n("6cb7"),m=n("4f85"),y=n("b12f"),x=n("e887"),_=n("2306"),b=n("e0d3"),w=n("88b3"),S=w.throttle,M=n("fd63"),T=n("b809"),C=n("998c"),I=n("69ff"),D=n("c533"),A=n("f219");n("0352");var k=n("ec34"),O=a.assert,P=a.each,L=a.isFunction,E=a.isObject,R=v.parseClassType,N="4.6.0",B={zrender:"4.2.0"},z=1,F=1e3,V=800,H=900,W=5e3,G=1e3,j=1100,Y=2e3,U=3e3,$=3500,X=4e3,q=5e3,Z={PROCESSOR:{FILTER:F,SERIES_FILTER:V,STATISTIC:W},VISUAL:{LAYOUT:G,PROGRESSIVE_LAYOUT:j,GLOBAL:Y,CHART:U,POST_CHART_LAYOUT:$,COMPONENT:X,BRUSH:q}},K="__flagInMainProcess",J="__optionUpdated",Q=/^[a-zA-Z0-9_]+$/;function tt(t,e){return function(n,i,r){e||!this._disposed?(n=n&&n.toLowerCase(),u.prototype[t].call(this,n,i,r)):_t(this.id)}}function et(){u.call(this)}function nt(t,e,n){n=n||{},"string"===typeof e&&(e=Pt[e]),this.id,this.group,this._dom=t;var i="canvas",o=this._zr=r.init(t,{renderer:n.renderer||i,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=S(a.bind(o.flush,o),17);e=a.clone(e);e&&p(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new d;var s=this._api=Mt(this);function c(t,e){return t.__prio-e.__prio}l(Ot,c),l(Dt,c),this._scheduler=new I(this,s,Dt,Ot),u.call(this,this._ecEventProcessor=new Tt),this._messageCenter=new et,this._initEvents(),this.resize=a.bind(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),dt(o,this),a.setAsPrimitive(this)}et.prototype.on=tt("on",!0),et.prototype.off=tt("off",!0),et.prototype.one=tt("one",!0),a.mixin(et,u);var it=nt.prototype;function rt(t,e,n){if(this._disposed)_t(this.id);else{var i,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=b.parseFinder(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}}it._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[J]){var e=this[J].silent;this[K]=!0,ot(this),at.update.call(this),this[K]=!1,this[J]=!1,ct.call(this,e),ht.call(this,e)}else if(t.unfinished){var n=z,i=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),lt(this,i),t.performVisualTasks(i),mt(this,this._model,r,"remain"),n-=+new Date-a}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},it.getDom=function(){return this._dom},it.getZr=function(){return this._zr},it.setOption=function(t,e,n){if(this._disposed)_t(this.id);else{var i;if(E(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[K]=!0,!this._model||e){var r=new f(this._api),a=this._theme,o=this._model=new c;o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,At),n?(this[J]={silent:i},this[K]=!1):(ot(this),at.update.call(this),this._zr.flush(),this[J]=!1,this[K]=!1,ct.call(this,i),ht.call(this,i))}},it.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},it.getModel=function(){return this._model},it.getOption=function(){return this._model&&this._model.getOption()},it.getWidth=function(){return this._zr.getWidth()},it.getHeight=function(){return this._zr.getHeight()},it.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},it.getRenderedCanvas=function(t){if(s.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},it.getSvgDataUrl=function(){if(s.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return a.each(e,function(t){t.stopAnimation(!0)}),t.painter.pathToDataUrl()}},it.getDataURL=function(t){if(!this._disposed){t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;P(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var a="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return P(i,function(t){t.group.ignore=!1}),a}_t(this.id)},it.getConnectedDataURL=function(t){if(this._disposed)_t(this.id);else if(s.canvasSupported){var e=this.group,n=Math.min,i=Math.max,o=1/0;if(Rt[e]){var l=o,u=o,c=-o,h=-o,d=[],f=t&&t.pixelRatio||1;a.each(Et,function(r,o){if(r.group===e){var s=r.getRenderedCanvas(a.clone(t)),f=r.getDom().getBoundingClientRect();l=n(f.left,l),u=n(f.top,u),c=i(f.right,c),h=i(f.bottom,h),d.push({dom:s,left:f.left,top:f.top})}}),l*=f,u*=f,c*=f,h*=f;var p=c-l,g=h-u,v=a.createCanvas();v.width=p,v.height=g;var m=r.init(v);return t.connectedBackgroundColor&&m.add(new _.Rect({shape:{x:0,y:0,width:p,height:g},style:{fill:t.connectedBackgroundColor}})),P(d,function(t){var e=new _.Image({style:{x:t.left*f-l,y:t.top*f-u,image:t.dom}});m.add(e)}),m.refreshImmediately(),v.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},it.convertToPixel=a.curry(rt,"convertToPixel"),it.convertFromPixel=a.curry(rt,"convertFromPixel"),it.containPixel=function(t,e){if(!this._disposed){var n,i=this._model;return t=b.parseFinder(i,t),a.each(t,function(t,i){i.indexOf("Models")>=0&&a.each(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(n|=a.containPoint(e,t))}},this)},this),!!n}_t(this.id)},it.getVisual=function(t,e){var n=this._model;t=b.parseFinder(n,t,{defaultMainType:"series"});var i=t.seriesModel,r=i.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},it.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},it.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var at={prepareAndUpdate:function(t){ot(this),at.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,n),a.performDataProcessorTasks(e,t),lt(this,e),r.update(e,n),pt(e),a.performVisualTasks(e,t),gt(this,e,n,t);var l=e.get("backgroundColor")||"transparent";if(s.canvasSupported)i.setBackgroundColor(l);else{var u=o.parse(l);l=o.stringify(u,"rgb"),0===u[3]&&(l="transparent")}yt(e,n)}},updateTransform:function(t){var e=this._model,n=this,i=this._api;if(e){var r=[];e.eachComponent(function(a,o){var s=n.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,i,t);l&&l.update&&r.push(s)}else r.push(s)});var o=a.createHashMap();e.eachSeries(function(r){var a=n._chartsMap[r.__viewId];if(a.updateTransform){var s=a.updateTransform(r,e,i,t);s&&s.update&&o.set(r.uid,1)}else o.set(r.uid,1)}),pt(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:o}),mt(n,e,i,t,o),yt(e,this._api)}},updateView:function(t){var e=this._model;e&&(x.markUpdateMethod(t,"updateView"),pt(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),gt(this,this._model,this._api,t),yt(e,this._api))},updateVisual:function(t){at.update.call(this,t)},updateLayout:function(t){at.update.call(this,t)}};function ot(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),ft(t,"component",e,n),ft(t,"chart",e,n),n.plan()}function st(t,e,n,i,r){var o=t._model;if(i){var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r);var u=n.excludeSeriesId;null!=u&&(u=a.createHashMap(b.normalizeToArray(u))),o&&o.eachComponent(l,function(e){u&&null!=u.get(e.id)||c(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])},t)}else P(t._componentsViews.concat(t._chartsViews),c);function c(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}}function lt(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function ut(t,e){var n=t.type,i=t.escapeConnect,r=Ct[n],o=r.actionInfo,s=(o.update||"update").split(":"),l=s.pop();s=null!=s[0]&&R(s[0]),this[K]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=a.map(t.batch,function(e){return e=a.defaults(a.extend({},e),t),e.batch=null,e}));var h,d=[],f="highlight"===n||"downplay"===n;P(u,function(t){h=r.action(t,this._model,this._api),h=h||a.extend({},t),h.type=o.event||h.type,d.push(h),f?st(this,l,t,"series"):s&&st(this,l,t,s.main,s.sub)},this),"none"===l||f||s||(this[J]?(ot(this),at.update.call(this,t),this[J]=!1):at[l].call(this,t)),h=c?{type:o.event||n,escapeConnect:i,batch:d}:d[0],this[K]=!1,!e&&this._messageCenter.trigger(h.type,h)}function ct(t){var e=this._pendingActions;while(e.length){var n=e.shift();ut.call(this,n,t)}}function ht(t){!t&&this.trigger("updated")}function dt(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[J]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function ft(t,e,n,i){for(var r="component"===e,a=r?t._componentsViews:t._chartsViews,o=r?t._componentsMap:t._chartsMap,s=t._zr,l=t._api,u=0;u<a.length;u++)a[u].__alive=!1;function c(t){var e="_ec_"+t.id+"_"+t.type,u=o[e];if(!u){var c=R(t.type),h=r?y.getClass(c.main,c.sub):x.getClass(c.sub);u=new h,u.init(n,l),o[e]=u,a.push(u),s.add(u.group)}t.__viewId=u.__id=e,u.__alive=!0,u.__model=t,u.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!r&&i.prepareView(u,t,n,l)}r?n.eachComponent(function(t,e){"series"!==t&&c(e)}):n.eachSeries(c);for(u=0;u<a.length;){var h=a[u];h.__alive?u++:(!r&&h.renderTask.dispose(),s.remove(h.group),h.dispose(n,l),a.splice(u,1),delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}}function pt(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function gt(t,e,n,i){vt(t,e,n,i),P(t._chartsViews,function(t){t.__alive=!1}),mt(t,e,n,i),P(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function vt(t,e,n,i,r){P(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,n,i),St(r,t)})}function mt(t,e,n,i,r){var a,o=t._scheduler;e.eachSeries(function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var s=n.renderTask;o.updatePayload(s,i),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),n.group.silent=!!e.get("silent"),St(e,n),wt(e,n)}),o.unfinished|=a,bt(t,e),T(t._zr.dom,e)}function yt(t,e){P(kt,function(n){n(t,e)})}it.resize=function(t){if(this._disposed)_t(this.id);else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[K]=!0,n&&ot(this),at.update.call(this),this[K]=!1,ct.call(this,i),ht.call(this,i)}}},it.showLoading=function(t,e){if(this._disposed)_t(this.id);else if(E(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Lt[t]){var n=Lt[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},it.hideLoading=function(){this._disposed?_t(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},it.makeActionFromEvent=function(t){var e=a.extend({},t);return e.type=It[t.type],e},it.dispatchAction=function(t,e){this._disposed?_t(this.id):(E(e)||(e={silent:!!e}),Ct[t.type]&&this._model&&(this[K]?this._pendingActions.push(t):(ut.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&s.browser.weChat&&this._throttledZrFlush(),ct.call(this,e.silent),ht.call(this,e.silent))))},it.appendData=function(t){if(this._disposed)_t(this.id);else{var e=t.seriesIndex,n=this.getModel(),i=n.getSeriesByIndex(e);i.appendData(t),this._scheduler.unfinished=!0}},it.on=tt("on",!1),it.off=tt("off",!1),it.one=tt("one",!1);var xt=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function _t(t){}function bt(t,e){var n=t._zr,i=n.storage,r=0;i.traverse(function(t){r++}),r>e.get("hoverLayerThreshold")&&!s.node&&e.eachSeries(function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.group.traverse(function(t){t.useHoverLayer=!0})}})}function wt(t,e){var n=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",n)})})}function St(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function Mt(t){var e=t._coordSysMgr;return a.extend(new h(t),{getCoordinateSystems:a.bind(e.getCoordinateSystems,e),getComponentByElement:function(e){while(e){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function Tt(){this.eventInfo}it._initEvents=function(){P(xt,function(t){var e=function(e){var n,i=this.getModel(),r=e.target,o="globalout"===t;if(o)n={};else if(r&&null!=r.dataIndex){var s=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(n=a.extend({},r.eventData));if(n){var l=n.componentType,u=n.componentIndex;"markLine"!==l&&"markPoint"!==l&&"markArea"!==l||(l="series",u=n.seriesIndex);var c=l&&null!=u&&i.getComponent(l,u),h=c&&this["series"===c.mainType?"_chartsMap":"_componentsMap"][c.__viewId];n.event=e,n.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:n,model:c,view:h},this.trigger(t,n)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),P(It,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},it.isDisposed=function(){return this._disposed},it.clear=function(){this._disposed?_t(this.id):this.setOption({series:[]},!0)},it.dispose=function(){if(this._disposed)_t(this.id);else{this._disposed=!0,b.setAttribute(this.getDom(),zt,"");var t=this._api,e=this._model;P(this._componentsViews,function(n){n.dispose(e,t)}),P(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),delete Et[this.id]}},a.mixin(nt,u),Tt.prototype={constructor:Tt,normalizeQuery:function(t){var e={},n={},i={};if(a.isString(t)){var r=R(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};a.each(t,function(t,r){for(var a=!1,l=0;l<o.length;l++){var u=o[l],c=r.lastIndexOf(u);if(c>0&&c===r.length-u.length){var h=r.slice(0,c);"data"!==h&&(e.mainType=h,e[u.toLowerCase()]=t,a=!0)}}s.hasOwnProperty(r)&&(n[r]=t,a=!0),a||(i[r]=t)})}return{cptQuery:e,dataQuery:n,otherQuery:i}},filter:function(t,e,n){var i=this.eventInfo;if(!i)return!0;var r=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return c(l,o,"mainType")&&c(l,o,"subType")&&c(l,o,"index","componentIndex")&&c(l,o,"name")&&c(l,o,"id")&&c(u,a,"name")&&c(u,a,"dataIndex")&&c(u,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a));function c(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},afterTrigger:function(){this.eventInfo=null}};var Ct={},It={},Dt=[],At=[],kt=[],Ot=[],Pt={},Lt={},Et={},Rt={},Nt=new Date-0,Bt=new Date-0,zt="_echarts_instance_";function Ft(t){var e=0,n=1,i=2,r="__connectUpdateStatus";function a(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[r]=e}}P(It,function(o,s){t._messageCenter.on(s,function(o){if(Rt[t.group]&&t[r]!==e){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];P(Et,function(e){e!==t&&e.group===t.group&&l.push(e)}),a(l,e),P(l,function(t){t[r]!==n&&t.dispatchAction(s)}),a(l,i)}})})}function Vt(t,e,n){var i=Yt(t);if(i)return i;var r=new nt(t,e,n);return r.id="ec_"+Nt++,Et[r.id]=r,b.setAttribute(t,zt,r.id),Ft(r),r}function Ht(t){if(a.isArray(t)){var e=t;t=null,P(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+Bt++,P(e,function(e){e.group=t})}return Rt[t]=!0,t}function Wt(t){Rt[t]=!1}var Gt=Wt;function jt(t){"string"===typeof t?t=Et[t]:t instanceof nt||(t=Yt(t)),t instanceof nt&&!t.isDisposed()&&t.dispose()}function Yt(t){return Et[b.getAttribute(t,zt)]}function Ut(t){return Et[t]}function $t(t,e){Pt[t]=e}function Xt(t){At.push(t)}function qt(t,e){ne(Dt,t,e,F)}function Zt(t){kt.push(t)}function Kt(t,e,n){"function"===typeof e&&(n=e,e="");var i=E(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,O(Q.test(i)&&Q.test(e)),Ct[i]||(Ct[i]={action:n,actionInfo:t}),It[e]=i}function Jt(t,e){d.register(t,e)}function Qt(t){var e=d.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()}function te(t,e){ne(Ot,t,e,G,"layout")}function ee(t,e){ne(Ot,t,e,U,"visual")}function ne(t,e,n,i,r){(L(e)||E(e))&&(n=e,e=i);var a=I.wrapStageHandler(n,r);return a.__prio=e,a.__raw=n,t.push(a),a}function ie(t,e){Lt[t]=e}function re(t){return v.extend(t)}function ae(t){return y.extend(t)}function oe(t){return m.extend(t)}function se(t){return x.extend(t)}function le(t){a.$override("createCanvas",t)}function ue(t,e,n){k.registerMap(t,e,n)}function ce(t){var e=k.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}ee(Y,M),Xt(p),qt(H,g),ie("default",C),Kt({type:"highlight",event:"highlight",update:"highlight"},a.noop),Kt({type:"downplay",event:"downplay",update:"downplay"},a.noop),$t("light",D),$t("dark",A);var he={};e.version=N,e.dependencies=B,e.PRIORITY=Z,e.init=Vt,e.connect=Ht,e.disConnect=Wt,e.disconnect=Gt,e.dispose=jt,e.getInstanceByDom=Yt,e.getInstanceById=Ut,e.registerTheme=$t,e.registerPreprocessor=Xt,e.registerProcessor=qt,e.registerPostUpdate=Zt,e.registerAction=Kt,e.registerCoordinateSystem=Jt,e.getCoordinateSystemDimensions=Qt,e.registerLayout=te,e.registerVisual=ee,e.registerLoading=ie,e.extendComponentModel=re,e.extendComponentView=ae,e.extendSeriesModel=oe,e.extendChartView=se,e.setCanvasCreator=le,e.registerMap=ue,e.getMap=ce,e.dataTool=he;var de=n("b719");(function(){for(var t in de)de.hasOwnProperty(t)&&(e[t]=de[t])})()},"401b":function(t,e){var n="undefined"===typeof Float32Array?Array:Float32Array;function i(t,e){var i=new n(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function r(t,e){return t[0]=e[0],t[1]=e[1],t}function a(t){var e=new n(2);return e[0]=t[0],e[1]=t[1],e}function o(t,e,n){return t[0]=e,t[1]=n,t}function s(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function l(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function u(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function c(t){return Math.sqrt(d(t))}var h=c;function d(t){return t[0]*t[0]+t[1]*t[1]}var f=d;function p(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t}function g(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t}function v(t,e){return t[0]*e[0]+t[1]*e[1]}function m(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function y(t,e){var n=c(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function x(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var _=x;function b(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var w=b;function S(t,e){return t[0]=-e[0],t[1]=-e[1],t}function M(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function T(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function C(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function I(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}e.create=i,e.copy=r,e.clone=a,e.set=o,e.add=s,e.scaleAndAdd=l,e.sub=u,e.len=c,e.length=h,e.lenSquare=d,e.lengthSquare=f,e.mul=p,e.div=g,e.dot=v,e.scale=m,e.normalize=y,e.distance=x,e.dist=_,e.distanceSquare=b,e.distSquare=w,e.negate=S,e.lerp=M,e.applyTransform=T,e.min=C,e.max=I},"41ef":function(t,e,n){var i=n("d51b"),r={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return t=Math.round(t),t<0?0:t>255?255:t}function o(t){return t=Math.round(t),t<0?0:t>360?360:t}function s(t){return t<0?0:t>1?1:t}function l(t){return t.length&&"%"===t.charAt(t.length-1)?a(parseFloat(t)/100*255):a(parseInt(t,10))}function u(t){return t.length&&"%"===t.charAt(t.length-1)?s(parseFloat(t)/100):s(parseFloat(t))}function c(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function h(t,e,n){return t+(e-t)*n}function d(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function f(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var p=new i(20),g=null;function v(t,e){g&&f(g,e),g=p.put(t,g||e.slice())}function m(t,e){if(t){e=e||[];var n=p.get(t);if(n)return f(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in r)return f(e,r[i]),v(t,e),e;if("#"!==i.charAt(0)){var a=i.indexOf("("),o=i.indexOf(")");if(-1!==a&&o+1===i.length){var s=i.substr(0,a),c=i.substr(a+1,o-(a+1)).split(","),h=1;switch(s){case"rgba":if(4!==c.length)return void d(e,0,0,0,1);h=u(c.pop());case"rgb":return 3!==c.length?void d(e,0,0,0,1):(d(e,l(c[0]),l(c[1]),l(c[2]),h),v(t,e),e);case"hsla":return 4!==c.length?void d(e,0,0,0,1):(c[3]=u(c[3]),y(c,e),v(t,e),e);case"hsl":return 3!==c.length?void d(e,0,0,0,1):(y(c,e),v(t,e),e);default:return}}d(e,0,0,0,1)}else{if(4===i.length){var g=parseInt(i.substr(1),16);return g>=0&&g<=4095?(d(e,(3840&g)>>4|(3840&g)>>8,240&g|(240&g)>>4,15&g|(15&g)<<4,1),v(t,e),e):void d(e,0,0,0,1)}if(7===i.length){g=parseInt(i.substr(1),16);return g>=0&&g<=16777215?(d(e,(16711680&g)>>16,(65280&g)>>8,255&g,1),v(t,e),e):void d(e,0,0,0,1)}}}}function y(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=u(t[1]),r=u(t[2]),o=r<=.5?r*(i+1):r+i-r*i,s=2*r-o;return e=e||[],d(e,a(255*c(s,o,n+1/3)),a(255*c(s,o,n)),a(255*c(s,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function x(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(i,r,a),s=Math.max(i,r,a),l=s-o,u=(s+o)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+o):l/(2-s-o);var c=((s-i)/6+l/2)/l,h=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;i===s?e=d-h:r===s?e=1/3+c-d:a===s&&(e=2/3+h-c),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}function _(t,e){var n=m(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:t[i]<0&&(n[i]=0);return D(n,4===n.length?"rgba":"rgb")}}function b(t){var e=m(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function w(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),l=e[r],u=e[o],c=i-r;return n[0]=a(h(l[0],u[0],c)),n[1]=a(h(l[1],u[1],c)),n[2]=a(h(l[2],u[2],c)),n[3]=s(h(l[3],u[3],c)),n}}var S=w;function M(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),l=m(e[r]),u=m(e[o]),c=i-r,d=D([a(h(l[0],u[0],c)),a(h(l[1],u[1],c)),a(h(l[2],u[2],c)),s(h(l[3],u[3],c))],"rgba");return n?{color:d,leftIndex:r,rightIndex:o,value:i}:d}}var T=M;function C(t,e,n,i){if(t=m(t),t)return t=x(t),null!=e&&(t[0]=o(e)),null!=n&&(t[1]=u(n)),null!=i&&(t[2]=u(i)),D(y(t),"rgba")}function I(t,e){if(t=m(t),t&&null!=e)return t[3]=s(e),D(t,"rgba")}function D(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}e.parse=m,e.lift=_,e.toHex=b,e.fastLerp=w,e.fastMapToColor=S,e.lerp=M,e.mapToColor=T,e.modifyHSL=C,e.modifyAlpha=I,e.stringify=D},"42e5":function(t,e){var n=function(t){this.colorStops=t||[]};n.prototype={constructor:n,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var i=n;t.exports=i},"42f6":function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("22d1"),o=n("07d7"),s=n("82f9"),l=n("eda2"),u=n("3842"),c=n("2306"),h=n("133d"),d=n("f934"),f=n("4319"),p=n("17d6"),g=n("697e"),v=n("ff2e"),m=n("e0d3"),y=m.getTooltipRenderMode,x=r.bind,_=r.each,b=u.parsePercent,w=new c.Rect({shape:{x:-1,y:-1,width:2,height:2}}),S=i.extendComponentView({type:"tooltip",init:function(t,e){if(!a.node){var n,i=t.getComponent("tooltip"),r=i.get("renderMode");this._renderMode=y(r),"html"===this._renderMode?(n=new o(e.getDom(),e),this._newLine="<br/>"):(n=new s(e),this._newLine="\n"),this._tooltipContent=n}},render:function(t,e,n){if(!a.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");p.register("itemTooltip",this._api,x(function(t,n,i){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(n,i):"leave"===t&&this._hide(i))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,n=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var i=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&i.manuallyShowTip(t,e,n,{x:i._lastX,y:i._lastY})})}},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!a.node){var r=T(i,n);this._ticket="";var o=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var s=w;s.position=[i.x,i.y],s.update(),s.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:s},r)}else if(o)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,event:{},dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var l=h(i,e),u=l.point[0],c=l.point[1];null!=u&&null!=c&&this._tryShow({offsetX:u,offsetY:c,position:i.position,target:l.el,event:{}},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target,event:{}},r))}},manuallyHideTip:function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(T(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var r=i.seriesIndex,a=i.dataIndex,o=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=a&&null!=o){var s=e.getSeriesByIndex(r);if(s){var l=s.getData();t=M([l.getItemModel(a),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:a,position:i.position}),!0}}},_tryShow:function(t,e){var n=t.target,i=this._tooltipModel;if(i){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):n&&null!=n.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,n,e)):n&&n.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,n,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var n=t.get("showDelay");e=r.bind(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var n=this._ecModel,i=this._tooltipModel,a=[e.offsetX,e.offsetY],o=[],s=[],u=M([e.tooltipOption,i]),c=this._renderMode,h=this._newLine,d={};_(t,function(t){_(t.dataByAxis,function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),i=t.value,a=[];if(e&&null!=i){var u=v.getValueLabel(i,e.axis,n,t.seriesDataIndices,t.valueLabelOpt);r.each(t.seriesDataIndices,function(o){var l=n.getSeriesByIndex(o.seriesIndex),h=o.dataIndexInside,f=l&&l.getDataParams(h);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=g.getAxisRawValue(e.axis,i),f.axisValueLabel=u,f){s.push(f);var p,v=l.formatTooltip(h,!0,null,c);if(r.isObject(v)){p=v.html;var m=v.markers;r.merge(d,m)}else p=v;a.push(p)}});var f=u;"html"!==c?o.push(a.join(h)):o.push((f?l.encodeHTML(f)+h:"")+a.join(h))}})},this),o.reverse(),o=o.join(this._newLine+this._newLine);var f=e.position;this._showOrMove(u,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(u,f,a[0],a[1],this._tooltipContent,s):this._showTooltipContent(u,o,s,Math.random(),a[0],a[1],f,void 0,d)})},_showSeriesItemTooltip:function(t,e,n){var i=this._ecModel,a=e.seriesIndex,o=i.getSeriesByIndex(a),s=e.dataModel||o,l=e.dataIndex,u=e.dataType,c=s.getData(),h=M([c.getItemModel(l),s,o&&(o.coordinateSystem||{}).model,this._tooltipModel]),d=h.get("trigger");if(null==d||"item"===d){var f,p,g=s.getDataParams(l,u),v=s.formatTooltip(l,!1,u,this._renderMode);r.isObject(v)?(f=v.html,p=v.markers):(f=v,p=null);var m="item_"+s.name+"_"+l;this._showOrMove(h,function(){this._showTooltipContent(h,f,g,m,t.offsetX,t.offsetY,t.position,t.target,p)}),n({type:"showTip",dataIndexInside:l,dataIndex:c.getRawIndex(l),seriesIndex:a,from:this.uid})}},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;if("string"===typeof i){var r=i;i={content:r,formatter:r}}var a=new f(i,this._tooltipModel,this._ecModel),o=a.get("content"),s=Math.random();this._showOrMove(a,function(){this._showTooltipContent(a,o,a.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,n,i,r,a,o,s,u){if(this._ticket="",t.get("showContent")&&t.get("show")){var c=this._tooltipContent,h=t.get("formatter");o=o||t.get("position");var d=e;if(h&&"string"===typeof h)d=l.formatTpl(h,n,!0);else if("function"===typeof h){var f=x(function(e,i){e===this._ticket&&(c.setContent(i,u,t),this._updatePosition(t,o,r,a,c,n,s))},this);this._ticket=i,d=h(n,i,f)}c.setContent(d,u,t),c.show(t),this._updatePosition(t,o,r,a,c,n,s)}},_updatePosition:function(t,e,n,i,a,o,s){var l=this._api.getWidth(),u=this._api.getHeight();e=e||t.get("position");var c=a.getSize(),h=t.get("align"),f=t.get("verticalAlign"),p=s&&s.getBoundingRect().clone();if(s&&p.applyTransform(s.transform),"function"===typeof e&&(e=e([n,i],o,a.el,p,{viewSize:[l,u],contentSize:c.slice()})),r.isArray(e))n=b(e[0],l),i=b(e[1],u);else if(r.isObject(e)){e.width=c[0],e.height=c[1];var g=d.getLayoutRect(e,{width:l,height:u});n=g.x,i=g.y,h=null,f=null}else if("string"===typeof e&&s){var v=D(e,p,c);n=v[0],i=v[1]}else{v=C(n,i,a,l,u,h?null:20,f?null:20);n=v[0],i=v[1]}if(h&&(n-=A(h)?c[0]/2:"right"===h?c[0]:0),f&&(i-=A(f)?c[1]/2:"bottom"===f?c[1]:0),t.get("confine")){v=I(n,i,a,l,u);n=v[0],i=v[1]}a.moveTo(n,i)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,n=!!e&&e.length===t.length;return n&&_(e,function(e,i){var r=e.dataByAxis||{},a=t[i]||{},o=a.dataByAxis||[];n&=r.length===o.length,n&&_(r,function(t,e){var i=o[e]||{},r=t.seriesDataIndices||[],a=i.seriesDataIndices||[];n&=t.value===i.value&&t.axisType===i.axisType&&t.axisId===i.axisId&&r.length===a.length,n&&_(r,function(t,e){var i=a[e];n&=t.seriesIndex===i.seriesIndex&&t.dataIndex===i.dataIndex})})}),this._lastDataByCoordSys=t,!!n},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){a.node||(this._tooltipContent.hide(),p.unregister("itemTooltip",e))}});function M(t){var e=t.pop();while(t.length){var n=t.pop();n&&(f.isInstance(n)&&(n=n.get("tooltip",!0)),"string"===typeof n&&(n={formatter:n}),e=new f(n,e,e.ecModel))}return e}function T(t,e){return t.dispatchAction||r.bind(e.dispatchAction,e)}function C(t,e,n,i,r,a,o){var s=n.getOuterSize(),l=s.width,u=s.height;return null!=a&&(t+l+a>i?t-=l+a:t+=a),null!=o&&(e+u+o>r?e-=u+o:e+=o),[t,e]}function I(t,e,n,i,r){var a=n.getOuterSize(),o=a.width,s=a.height;return t=Math.min(t+o,i)-o,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function D(t,e,n){var i=n[0],r=n[1],a=5,o=0,s=0,l=e.width,u=e.height;switch(t){case"inside":o=e.x+l/2-i/2,s=e.y+u/2-r/2;break;case"top":o=e.x+l/2-i/2,s=e.y-r-a;break;case"bottom":o=e.x+l/2-i/2,s=e.y+u+a;break;case"left":o=e.x-i-a,s=e.y+u/2-r/2;break;case"right":o=e.x+l+a,s=e.y+u/2-r/2}return[o,s]}function A(t){return"center"===t||"middle"===t}t.exports=S},4319:function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=n("e0d3"),o=a.makeInner,s=n("625e"),l=s.enableClassExtend,u=s.enableClassCheck,c=n("3901"),h=n("9bdb"),d=n("fe21"),f=n("551f"),p=i.mixin,g=o();function v(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function m(t,e,n){for(var i=0;i<e.length;i++)if(e[i]&&(t=t&&"object"===typeof t?t[e[i]]:null,null==t))break;return null==t&&n&&(t=n.get(e)),t}function y(t,e){var n=g(t).getParent;return n?n.call(t,e):t.parentModel}v.prototype={constructor:v,init:null,mergeOption:function(t){i.merge(this.option,t,!0)},get:function(t,e){return null==t?this.option:m(this.option,this.parsePath(t),!e&&y(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&y(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,i=null==t?this.option:m(this.option,t=this.parsePath(t));return e=e||(n=y(this,t))&&n.getModel(t),new v(i,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i.clone(this.option))},setReadOnly:function(t){},parsePath:function(t){return"string"===typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){g(this).getParent=t},isAnimationEnabled:function(){if(!r.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},l(v),u(v),p(v,c),p(v,h),p(v,d),p(v,f);var x=v;t.exports=x},4436:function(t,e,n){var i=n("74cb");function r(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}r.prototype={constructor:r,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var r=this.easing,a="string"===typeof r?i[r]:r,o="function"===typeof a?a(n):n;return this.fire("frame",o),1===n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var a=r;t.exports=a},4573:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}});t.exports=r},4650:function(t,e,n){var i=n("3eba"),r=n("6d8b");function a(t,e,n){var i,a={},o="toggleSelected"===t;return n.eachComponent("legend",function(n){o&&null!=i?n[i?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?n[t]():(n[t](e.name),i=n.isSelected(e.name));var s=n.getData();r.each(s,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var i=n.isSelected(e);a.hasOwnProperty(e)?a[e]=a[e]&&i:a[e]=i}})}),"allSelect"===t||"inverseSelect"===t?{selected:a}:{name:e.name,selected:a}}i.registerAction("legendToggleSelect","legendselectchanged",r.curry(a,"toggleSelected")),i.registerAction("legendAllSelect","legendselectall",r.curry(a,"allSelect")),i.registerAction("legendInverseSelect","legendinverseselect",r.curry(a,"inverseSelect")),i.registerAction("legendSelect","legendselected",r.curry(a,"select")),i.registerAction("legendUnSelect","legendunselected",r.curry(a,"unSelect"))},"48a9":function(t,e,n){var i=n("6d8b"),r=n("42e5"),a=function(t,e,n,i,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,r.call(this,a)};a.prototype={constructor:a},i.inherits(a,r);var o=a;t.exports=o},"48ac":function(t,e,n){var i=n("3eba"),r=i.extendComponentModel({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),a=r;t.exports=a},"48c7":function(t,e,n){var i=n("6d8b"),r=n("6cb7"),a=n("9e47"),o=n("2023"),s=r.extend({type:"cartesian2dAxis",axis:null,init:function(){s.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){s.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){s.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});function l(t,e){return e.type||(e.data?"category":"value")}i.merge(s.prototype,o);var u={offset:0};a("x",s,l,u),a("y",s,l,u);var c=s;t.exports=c},4942:function(t,e,n){var i=n("2cf4"),r=i.debugMode,a=function(){};1===r&&(a=console.error);var o=a;t.exports=o},"4a3f":function(t,e,n){var i=n("401b"),r=i.create,a=i.distSquare,o=Math.pow,s=Math.sqrt,l=1e-8,u=1e-4,c=s(3),h=1/3,d=r(),f=r(),p=r();function g(t){return t>-l&&t<l}function v(t){return t>l||t<-l}function m(t,e,n,i,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*i+3*a*n)}function y(t,e,n,i,r){var a=1-r;return 3*(((e-t)*a+2*(n-e)*r)*a+(i-n)*r*r)}function x(t,e,n,i,r,a){var l=i+3*(e-n)-t,u=3*(n-2*e+t),d=3*(e-t),f=t-r,p=u*u-3*l*d,v=u*d-9*l*f,m=d*d-3*u*f,y=0;if(g(p)&&g(v))if(g(u))a[0]=0;else{var x=-d/u;x>=0&&x<=1&&(a[y++]=x)}else{var _=v*v-4*p*m;if(g(_)){var b=v/p,w=(x=-u/l+b,-b/2);x>=0&&x<=1&&(a[y++]=x),w>=0&&w<=1&&(a[y++]=w)}else if(_>0){var S=s(_),M=p*u+1.5*l*(-v+S),T=p*u+1.5*l*(-v-S);M=M<0?-o(-M,h):o(M,h),T=T<0?-o(-T,h):o(T,h);x=(-u-(M+T))/(3*l);x>=0&&x<=1&&(a[y++]=x)}else{var C=(2*p*u-3*l*v)/(2*s(p*p*p)),I=Math.acos(C)/3,D=s(p),A=Math.cos(I),k=(x=(-u-2*D*A)/(3*l),w=(-u+D*(A+c*Math.sin(I)))/(3*l),(-u+D*(A-c*Math.sin(I)))/(3*l));x>=0&&x<=1&&(a[y++]=x),w>=0&&w<=1&&(a[y++]=w),k>=0&&k<=1&&(a[y++]=k)}}return y}function _(t,e,n,i,r){var a=6*n-12*e+6*t,o=9*e+3*i-3*t-9*n,l=3*e-3*t,u=0;if(g(o)){if(v(a)){var c=-l/a;c>=0&&c<=1&&(r[u++]=c)}}else{var h=a*a-4*o*l;if(g(h))r[0]=-a/(2*o);else if(h>0){var d=s(h),f=(c=(-a+d)/(2*o),(-a-d)/(2*o));c>=0&&c<=1&&(r[u++]=c),f>=0&&f<=1&&(r[u++]=f)}}return u}function b(t,e,n,i,r,a){var o=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-o)*r+o,c=(l-s)*r+s,h=(c-u)*r+u;a[0]=t,a[1]=o,a[2]=u,a[3]=h,a[4]=h,a[5]=c,a[6]=l,a[7]=i}function w(t,e,n,i,r,o,l,c,h,g,v){var y,x,_,b,w,S=.005,M=1/0;d[0]=h,d[1]=g;for(var T=0;T<1;T+=.05)f[0]=m(t,n,r,l,T),f[1]=m(e,i,o,c,T),b=a(d,f),b<M&&(y=T,M=b);M=1/0;for(var C=0;C<32;C++){if(S<u)break;x=y-S,_=y+S,f[0]=m(t,n,r,l,x),f[1]=m(e,i,o,c,x),b=a(f,d),x>=0&&b<M?(y=x,M=b):(p[0]=m(t,n,r,l,_),p[1]=m(e,i,o,c,_),w=a(p,d),_<=1&&w<M?(y=_,M=w):S*=.5)}return v&&(v[0]=m(t,n,r,l,y),v[1]=m(e,i,o,c,y)),s(M)}function S(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function M(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function T(t,e,n,i,r){var a=t-2*e+n,o=2*(e-t),l=t-i,u=0;if(g(a)){if(v(o)){var c=-l/o;c>=0&&c<=1&&(r[u++]=c)}}else{var h=o*o-4*a*l;if(g(h)){c=-o/(2*a);c>=0&&c<=1&&(r[u++]=c)}else if(h>0){var d=s(h),f=(c=(-o+d)/(2*a),(-o-d)/(2*a));c>=0&&c<=1&&(r[u++]=c),f>=0&&f<=1&&(r[u++]=f)}}return u}function C(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function I(t,e,n,i,r){var a=(e-t)*i+t,o=(n-e)*i+e,s=(o-a)*i+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=n}function D(t,e,n,i,r,o,l,c,h){var g,v=.005,m=1/0;d[0]=l,d[1]=c;for(var y=0;y<1;y+=.05){f[0]=S(t,n,r,y),f[1]=S(e,i,o,y);var x=a(d,f);x<m&&(g=y,m=x)}m=1/0;for(var _=0;_<32;_++){if(v<u)break;var b=g-v,w=g+v;f[0]=S(t,n,r,b),f[1]=S(e,i,o,b);x=a(f,d);if(b>=0&&x<m)g=b,m=x;else{p[0]=S(t,n,r,w),p[1]=S(e,i,o,w);var M=a(p,d);w<=1&&M<m?(g=w,m=M):v*=.5}}return h&&(h[0]=S(t,n,r,g),h[1]=S(e,i,o,g)),s(m)}e.cubicAt=m,e.cubicDerivativeAt=y,e.cubicRootAt=x,e.cubicExtrema=_,e.cubicSubdivide=b,e.cubicProjectPoint=w,e.quadraticAt=S,e.quadraticDerivativeAt=M,e.quadraticRootAt=T,e.quadraticExtremum=C,e.quadraticSubdivide=I,e.quadraticProjectPoint=D},"4a9d":function(t,e,n){var i=n("dcb3"),r=n("ff2e"),a=n("0156"),o=n("6679"),s=i.extend({makeElOption:function(t,e,n,i,o){var s=n.axis,c=s.grid,h=i.get("type"),d=l(c,s).getOtherAxis(s).getGlobalExtent(),f=s.toGlobalCoord(s.dataToCoord(e,!0));if(h&&"none"!==h){var p=r.buildElStyle(i),g=u[h](s,f,d);g.style=p,t.graphicKey=g.type,t.pointer=g}var v=a.layout(c.model,n);r.buildCartesianSingleLabelElOption(e,t,v,n,i,o)},getHandleTransform:function(t,e,n){var i=a.layout(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:r.getTransformedPosition(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n,i){var r=n.axis,a=r.grid,o=r.getGlobalExtent(!0),s=l(a,r).getOtherAxis(r).getGlobalExtent(),u="x"===r.dim?0:1,c=t.position;c[u]+=e[u],c[u]=Math.min(o[1],c[u]),c[u]=Math.max(o[0],c[u]);var h=(s[1]+s[0])/2,d=[h,h];d[u]=c[u];var f=[{verticalAlign:"middle"},{align:"center"}];return{position:c,rotation:t.rotation,cursorPoint:d,tooltipOption:f[u]}}});function l(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var u={line:function(t,e,n){var i=r.makeLineShape([e,n[0]],[e,n[1]],c(t));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),a=n[1]-n[0];return{type:"Rect",shape:r.makeRectShape([e-i/2,n[0]],[i,a],c(t))}}};function c(t){return"x"===t.dim?0:1}o.registerAxisPointerClass("CartesianAxisPointer",s);var h=s;t.exports=h},"4aa2":function(t,e,n){var i=n("cbe5"),r=n("897a"),a=i.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:r(i.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(o),c=Math.sin(o);t.moveTo(u*r+n,c*r+i),t.lineTo(u*a+n,c*a+i),t.arc(n,i,a,o,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,o,l),t.closePath()}});t.exports=a},"4e08":function(t,e,n){(function(t){var n;"undefined"!==typeof window?n=window.__DEV__:"undefined"!==typeof t&&(n=t.__DEV__),"undefined"===typeof n&&(n=!0);var i=n;e.__DEV__=i}).call(this,n("c8ba"))},"4f85":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("22d1"),o=n("eda2"),s=o.formatTime,l=o.encodeHTML,u=o.addCommas,c=o.getTooltipMarker,h=n("e0d3"),d=n("6cb7"),f=n("e47b"),p=n("38a2"),g=n("f934"),v=g.getLayoutParams,m=g.mergeLayoutParam,y=n("f47d"),x=y.createTask,_=n("0f99"),b=_.prepareSource,w=_.getSource,S=n("2b17"),M=S.retrieveRawValue,T=h.makeInner(),C=d.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.dataTask=x({count:A,reset:k}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),b(this);var r=this.getInitialData(t,n);P(r,this),this.dataTask.context.data=r,T(this).dataBeforeProcessed=r,I(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?v(t):{},a=this.subType;d.hasClass(a)&&(a+="Series"),r.merge(t,e.getTheme().get(this.subType)),r.merge(t,this.getDefaultOption()),h.defaultEmphasis(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&m(t,i,n)},mergeOption:function(t,e){t=r.merge(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&m(this.option,t,n),b(this);var i=this.getInitialData(t,e);P(i,this),this.dataTask.dirty(),this.dataTask.context.data=i,T(this).dataBeforeProcessed=i,I(this)},fillDataTextStyle:function(t){if(t&&!r.isTypedArray(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&h.defaultEmphasis(t[n],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=E(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return T(this).data},setData:function(t){var e=E(this);if(e){var n=e.context;n.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),n.outputData=t,e!==this.dataTask&&(n.data=t)}T(this).data=t},getSource:function(){return w(this)},getRawData:function(){return T(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n,i){var a=this;i=i||"html";var o="html"===i?"<br/>":"\n",d="richText"===i,f={},p=0;function g(n){var o=r.reduce(n,function(t,e,n){var i=m.getDimensionInfo(n);return t|(i&&!1!==i.tooltip&&null!=i.displayName)},0),h=[];function g(t,n){var r=m.getDimensionInfo(n);if(r&&!1!==r.otherDims.tooltip){var g=r.type,v="sub"+a.seriesIndex+"at"+p,y=c({color:w,type:"subItem",renderMode:i,markerId:v}),x="string"===typeof y?y:y.content,_=(o?x+l(r.displayName||"-")+": ":"")+l("ordinal"===g?t+"":"time"===g?e?"":s("yyyy/MM/dd hh:mm:ss",t):u(t));_&&h.push(_),d&&(f[v]=w,++p)}}y.length?r.each(y,function(e){g(M(m,t,e),e)}):r.each(n,g);var v=o?d?"\n":"<br/>":"",x=v+h.join(v||", ");return{renderMode:i,content:x,style:f}}function v(t){return{renderMode:i,content:l(u(t)),style:f}}var m=this.getData(),y=m.mapDimension("defaultedTooltip",!0),x=y.length,_=this.getRawValue(t),b=r.isArray(_),w=m.getItemVisual(t,"color");r.isObject(w)&&w.colorStops&&(w=(w.colorStops[0]||{}).color),w=w||"transparent";var S=x>1||b&&!x?g(_):v(x?M(m,t,y[0]):b?_[0]:_),T=S.content,C=a.seriesIndex+"at"+p,I=c({color:w,type:"item",renderMode:i,markerId:C});f[C]=w,++p;var D=m.getName(t),A=this.name;h.isNameSpecified(this)||(A=""),A=A?l(A)+(e?": ":o):"";var k="string"===typeof I?I:I.content,O=e?k+A+T:A+k+(D?l(D)+": "+T:T);return{html:O,markers:f}},isAnimationEnabled:function(){if(a.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel,r=f.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});function I(t){var e=t.name;h.isNameSpecified(t)||(t.name=D(t)||e)}function D(t){var e=t.getRawData(),n=e.mapDimension("seriesName",!0),i=[];return r.each(n,function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)}),i.join(" ")}function A(t){return t.model.getRawData().count()}function k(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),O}function O(t,e){t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function P(t,e){r.each(t.CHANGABLE_METHODS,function(n){t.wrapMethod(n,r.curry(L,e))})}function L(t){var e=E(t);e&&e.setOutputEnd(this.count())}function E(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}r.mixin(C,p),r.mixin(C,f);var R=C;t.exports=R},"4fac":function(t,e,n){var i=n("620b"),r=n("9c2c");function a(t,e,n){var a=e.points,o=e.smooth;if(a&&a.length>=2){if(o&&"spline"!==o){var s=r(a,o,n,e.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var l=a.length,u=0;u<(n?l:l-1);u++){var c=s[2*u],h=s[2*u+1],d=a[(u+1)%l];t.bezierCurveTo(c[0],c[1],h[0],h[1],d[0],d[1])}}else{"spline"===o&&(a=i(a,n)),t.moveTo(a[0][0],a[0][1]);u=1;for(var f=a.length;u<f;u++)t.lineTo(a[u][0],a[u][1])}n&&t.closePath()}}e.buildPath=a},"551f":function(t,e,n){var i=n("282b"),r=i([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),a={getItemStyle:function(t,e){var n=r(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}};t.exports=a},"562e":function(t,e,n){var i=n("6d8b");function r(t){null!=t&&i.extend(this,t),this.otherDims={}}var a=r;t.exports=a},5693:function(t,e){function n(t,e){var n,i,r,a,o,s=e.x,l=e.y,u=e.width,c=e.height,h=e.r;u<0&&(s+=u,u=-u),c<0&&(l+=c,c=-c),"number"===typeof h?n=i=r=a=h:h instanceof Array?1===h.length?n=i=r=a=h[0]:2===h.length?(n=r=h[0],i=a=h[1]):3===h.length?(n=h[0],i=a=h[1],r=h[2]):(n=h[0],i=h[1],r=h[2],a=h[3]):n=i=r=a=0,n+i>u&&(o=n+i,n*=u/o,i*=u/o),r+a>u&&(o=r+a,r*=u/o,a*=u/o),i+r>c&&(o=i+r,i*=c/o,r*=c/o),n+a>c&&(o=n+a,n*=c/o,a*=c/o),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+c-r),0!==r&&t.arc(s+u-r,l+c-r,r,0,Math.PI/2),t.lineTo(s+a,l+c),0!==a&&t.arc(s+a,l+c-a,a,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}e.buildPath=n},"5a0c":function(t,e,n){!function(e,n){t.exports=n()}(0,function(){"use strict";var t="millisecond",e="second",n="minute",i="hour",r="day",a="week",o="month",s="quarter",l="year",u=/^(\d{4})-?(\d{1,2})-?(\d{0,2})[^0-9]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?.?(\d{1,3})?$/,c=/\[([^\]]+)]|Y{2,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(t,e,n){var i=String(t);return!i||i.length>=e?t:""+Array(e+1-i.length).join(n)+t},d={s:h,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),i=Math.floor(n/60),r=n%60;return(e<=0?"+":"-")+h(i,2,"0")+":"+h(r,2,"0")},m:function(t,e){var n=12*(e.year()-t.year())+(e.month()-t.month()),i=t.clone().add(n,o),r=e-i<0,a=t.clone().add(n+(r?-1:1),o);return Number(-(n+(e-i)/(r?i-a:a-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(u){return{M:o,y:l,w:a,d:r,h:i,m:n,s:e,ms:t,Q:s}[u]||String(u||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},f={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},p="en",g={};g[p]=f;var v=function(t){return t instanceof _},m=function(t,e,n){var i;if(!t)return p;if("string"==typeof t)g[t]&&(i=t),e&&(g[t]=e,i=t);else{var r=t.name;g[r]=t,i=r}return n||(p=i),i},y=function(t,e,n){if(v(t))return t.clone();var i=e?"string"==typeof e?{format:e,pl:n}:e:{};return i.date=t,new _(i)},x=d;x.l=m,x.i=v,x.w=function(t,e){return y(t,{locale:e.$L,utc:e.$u,$offset:e.$offset})};var _=function(){function h(t){this.$L=this.$L||m(t.locale,null,!0),this.parse(t)}var d=h.prototype;return d.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(x.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var i=e.match(u);if(i)return n?new Date(Date.UTC(i[1],i[2]-1,i[3]||1,i[4]||0,i[5]||0,i[6]||0,i[7]||0)):new Date(i[1],i[2]-1,i[3]||1,i[4]||0,i[5]||0,i[6]||0,i[7]||0)}return new Date(e)}(t),this.init()},d.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},d.$utils=function(){return x},d.isValid=function(){return!("Invalid Date"===this.$d.toString())},d.isSame=function(t,e){var n=y(t);return this.startOf(e)<=n&&n<=this.endOf(e)},d.isAfter=function(t,e){return y(t)<this.startOf(e)},d.isBefore=function(t,e){return this.endOf(e)<y(t)},d.$g=function(t,e,n){return x.u(t)?this[e]:this.set(n,t)},d.year=function(t){return this.$g(t,"$y",l)},d.month=function(t){return this.$g(t,"$M",o)},d.day=function(t){return this.$g(t,"$W",r)},d.date=function(t){return this.$g(t,"$D","date")},d.hour=function(t){return this.$g(t,"$H",i)},d.minute=function(t){return this.$g(t,"$m",n)},d.second=function(t){return this.$g(t,"$s",e)},d.millisecond=function(e){return this.$g(e,"$ms",t)},d.unix=function(){return Math.floor(this.valueOf()/1e3)},d.valueOf=function(){return this.$d.getTime()},d.startOf=function(t,s){var u=this,c=!!x.u(s)||s,h=x.p(t),d=function(t,e){var n=x.w(u.$u?Date.UTC(u.$y,e,t):new Date(u.$y,e,t),u);return c?n:n.endOf(r)},f=function(t,e){return x.w(u.toDate()[t].apply(u.toDate(),(c?[0,0,0,0]:[23,59,59,999]).slice(e)),u)},p=this.$W,g=this.$M,v=this.$D,m="set"+(this.$u?"UTC":"");switch(h){case l:return c?d(1,0):d(31,11);case o:return c?d(1,g):d(0,g+1);case a:var y=this.$locale().weekStart||0,_=(p<y?p+7:p)-y;return d(c?v-_:v+(6-_),g);case r:case"date":return f(m+"Hours",0);case i:return f(m+"Minutes",1);case n:return f(m+"Seconds",2);case e:return f(m+"Milliseconds",3);default:return this.clone()}},d.endOf=function(t){return this.startOf(t,!1)},d.$set=function(a,s){var u,c=x.p(a),h="set"+(this.$u?"UTC":""),d=(u={},u[r]=h+"Date",u.date=h+"Date",u[o]=h+"Month",u[l]=h+"FullYear",u[i]=h+"Hours",u[n]=h+"Minutes",u[e]=h+"Seconds",u[t]=h+"Milliseconds",u)[c],f=c===r?this.$D+(s-this.$W):s;if(c===o||c===l){var p=this.clone().set("date",1);p.$d[d](f),p.init(),this.$d=p.set("date",Math.min(this.$D,p.daysInMonth())).toDate()}else d&&this.$d[d](f);return this.init(),this},d.set=function(t,e){return this.clone().$set(t,e)},d.get=function(t){return this[x.p(t)]()},d.add=function(t,s){var u,c=this;t=Number(t);var h=x.p(s),d=function(e){var n=y(c);return x.w(n.date(n.date()+Math.round(e*t)),c)};if(h===o)return this.set(o,this.$M+t);if(h===l)return this.set(l,this.$y+t);if(h===r)return d(1);if(h===a)return d(7);var f=(u={},u[n]=6e4,u[i]=36e5,u[e]=1e3,u)[h]||1,p=this.$d.getTime()+t*f;return x.w(p,this)},d.subtract=function(t,e){return this.add(-1*t,e)},d.format=function(t){var e=this;if(!this.isValid())return"Invalid Date";var n=t||"YYYY-MM-DDTHH:mm:ssZ",i=x.z(this),r=this.$locale(),a=this.$H,o=this.$m,s=this.$M,l=r.weekdays,u=r.months,h=function(t,i,r,a){return t&&(t[i]||t(e,n))||r[i].substr(0,a)},d=function(t){return x.s(a%12||12,t,"0")},f=r.meridiem||function(t,e,n){var i=t<12?"AM":"PM";return n?i.toLowerCase():i},p={YY:String(this.$y).slice(-2),YYYY:this.$y,M:s+1,MM:x.s(s+1,2,"0"),MMM:h(r.monthsShort,s,u,3),MMMM:u[s]||u(this,n),D:this.$D,DD:x.s(this.$D,2,"0"),d:String(this.$W),dd:h(r.weekdaysMin,this.$W,l,2),ddd:h(r.weekdaysShort,this.$W,l,3),dddd:l[this.$W],H:String(a),HH:x.s(a,2,"0"),h:d(1),hh:d(2),a:f(a,o,!0),A:f(a,o,!1),m:String(o),mm:x.s(o,2,"0"),s:String(this.$s),ss:x.s(this.$s,2,"0"),SSS:x.s(this.$ms,3,"0"),Z:i};return n.replace(c,function(t,e){return e||p[t]||i.replace(":","")})},d.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},d.diff=function(t,u,c){var h,d=x.p(u),f=y(t),p=6e4*(f.utcOffset()-this.utcOffset()),g=this-f,v=x.m(this,f);return v=(h={},h[l]=v/12,h[o]=v,h[s]=v/3,h[a]=(g-p)/6048e5,h[r]=(g-p)/864e5,h[i]=g/36e5,h[n]=g/6e4,h[e]=g/1e3,h)[d]||g,c?v:x.a(v)},d.daysInMonth=function(){return this.endOf(o).$D},d.$locale=function(){return g[this.$L]},d.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),i=m(t,e,!0);return i&&(n.$L=i),n},d.clone=function(){return x.w(this.$d,this)},d.toDate=function(){return new Date(this.valueOf())},d.toJSON=function(){return this.isValid()?this.toISOString():null},d.toISOString=function(){return this.$d.toISOString()},d.toString=function(){return this.$d.toUTCString()},h}();return y.prototype=_.prototype,y.extend=function(t,e){return t(e,_,y),y},y.locale=m,y.isDayjs=v,y.unix=function(t){return y(1e3*t)},y.en=g[p],y.Ls=g,y})},"5aa9":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.isObject,o=r.each,s=r.map,l=r.indexOf,u=(r.retrieve,n("f934")),c=u.getLayoutRect,h=n("697e"),d=h.createScaleByModel,f=h.ifAxisCrossZero,p=h.niceScaleExtent,g=h.estimateLabelUnionRect,v=n("cbe9"),m=n("ec02"),y=n("2039"),x=n("ee1a"),_=x.getStackedDimension;function b(t,e,n){return t.getCoordSysModel()===e}function w(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}n("8ed2");var S=w.prototype;function M(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,a=t[e],o=n.model,s=o.get("axisLine.onZero"),l=o.get("axisLine.onZeroAxisIndex");if(s){if(null!=l)T(a[l])&&(r=a[l]);else for(var u in a)if(a.hasOwnProperty(u)&&T(a[u])&&!i[c(a[u])]){r=a[u];break}r&&(i[c(r)]=!0)}function c(t){return t.dim+"_"+t.index}}function T(t){return t&&"category"!==t.type&&"time"!==t.type&&f(t)}function C(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}S.type="grid",S.axisPointerEnabled=!0,S.getRect=function(){return this._rect},S.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),o(n.x,function(t){p(t.scale,t.model)}),o(n.y,function(t){p(t.scale,t.model)});var i={};o(n.x,function(t){M(n,"y",t,i)}),o(n.y,function(t){M(n,"x",t,i)}),this.resize(this.model,e)},S.resize=function(t,e,n){var i=c(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=i;var r=this._axesList;function a(){o(r,function(t){var e=t.isHorizontal(),n=e?[0,i.width]:[0,i.height],r=t.inverse?1:0;t.setExtent(n[r],n[1-r]),C(t,e?i.x:i.y)})}a(),!n&&t.get("containLabel")&&(o(r,function(t){if(!t.model.get("axisLabel.inside")){var e=g(t);if(e){var n=t.isHorizontal()?"height":"width",r=t.model.get("axisLabel.margin");i[n]-=e[n]+r,"top"===t.position?i.y+=e.height+r:"left"===t.position&&(i.x+=e.width+r)}}}),a())},S.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},S.getAxes=function(){return this._axesList.slice()},S.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}a(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},S.getCartesians=function(){return this._coordsList.slice()},S.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},S.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},S._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,u=this._coordsList;if(r)n=r.coordinateSystem,l(u,n)<0&&(n=null);else if(a&&o)n=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)i=this.getAxis("x",a.componentIndex);else if(o)i=this.getAxis("y",o.componentIndex);else if(s){var c=s.coordinateSystem;c===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},S.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},S._initCartesian=function(t,e,n){var i={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};if(e.eachComponent("xAxis",s("x"),this),e.eachComponent("yAxis",s("y"),this),!a.x||!a.y)return this._axesMap={},void(this._axesList=[]);function s(n){return function(o,s){if(b(o,t,e)){var l=o.get("position");"x"===n?"top"!==l&&"bottom"!==l&&(l=i.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=i.left?"right":"left"),i[l]=!0;var u=new m(n,d(o),[0,0],o.get("type"),l),c="category"===u.type;u.onBand=c&&o.get("boundaryGap"),u.inverse=o.get("inverse"),o.axis=u,u.model=o,u.grid=this,u.index=s,this._axesList.push(u),r[n][s]=u,a[n]++}}}this._axesMap=r,o(r.x,function(e,n){o(r.y,function(i,r){var a="x"+n+"y"+r,o=new v(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(i)},this)},this)},S._updateScale=function(t,e){function n(t,e,n){o(t.mapDimension(e.dim,!0),function(n){e.scale.unionExtentFromData(t,_(t,n))})}o(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(i){if(A(i)){var r=D(i,t),a=r[0],o=r[1];if(!b(a,e,t)||!b(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=i.getData(),u=s.getAxis("x"),c=s.getAxis("y");"list"===l.type&&(n(l,u,i),n(l,c,i))}},this)},S.getTooltipAxes=function(t){var e=[],n=[];return o(this.getCartesians(),function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),a=i.getOtherAxis(r);l(e,r)<0&&e.push(r),l(n,a)<0&&n.push(a)}),{baseAxes:e,otherAxes:n}};var I=["xAxis","yAxis"];function D(t,e){return s(I,function(e){var n=t.getReferringComponents(e)[0];return n})}function A(t){return"cartesian2d"===t.get("coordinateSystem")}w.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,r){var a=new w(i,t,e);a.name="grid_"+r,a.resize(i,e,!0),i.coordinateSystem=a,n.push(a)}),t.eachSeries(function(e){if(A(e)){var n=D(e,t),i=n[0],r=n[1],a=i.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(i.componentIndex,r.componentIndex)}}),n},w.dimensions=w.prototype.dimensions=v.prototype.dimensions,y.register("cartesian2d",w);var k=w;t.exports=k},"5e68":function(t,e,n){var i=n("6d8b"),r=n("2cf4"),a=r.devicePixelRatio,o=n("2b61"),s=n("dc2f");function l(){return!1}function u(t,e,n){var r=i.createCanvas(),a=e.getWidth(),o=e.getHeight(),s=r.style;return s&&(s.position="absolute",s.left=0,s.top=0,s.width=a+"px",s.height=o+"px",r.setAttribute("data-zr-dom-id",t)),r.width=a*n,r.height=o*n,r}var c=function(t,e,n){var r;n=n||a,"string"===typeof t?r=u(t,e,n):i.isObject(t)&&(r=t,t=r.id),this.id=t,this.dom=r;var o=r.style;o&&(r.onselectstart=l,o["-webkit-user-select"]="none",o["user-select"]="none",o["-webkit-touch-callout"]="none",o["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",o["padding"]=0,o["margin"]=0,o["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};c.prototype={constructor:c,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=u("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,a&&(a.width=t*n,a.height=e*n,1!==n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n,i=this.dom,r=this.ctx,a=i.width,l=i.height,u=(e=e||this.clearColor,this.motionBlur&&!t),c=this.lastFrameAlpha,h=this.dpr;(u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,a/h,l/h)),r.clearRect(0,0,a,l),e&&"transparent"!==e)&&(e.colorStops?(n=e.__canvasGradient||o.getGradient(r,e,{x:0,y:0,width:a,height:l}),e.__canvasGradient=n):e.image&&(n=s.prototype.getCanvasPattern.call(e,r)),r.save(),r.fillStyle=n||e,r.fillRect(0,0,a,l),r.restore());if(u){var d=this.domBack;r.save(),r.globalAlpha=c,r.drawImage(d,0,0,a,l),r.restore()}}};var h=c;t.exports=h},"5e76":function(t,e,n){var i=n("d51b"),r=new i(50);function a(t){if("string"===typeof t){var e=r.get(t);return e&&e.image}return t}function o(t,e,n,i,a){if(t){if("string"===typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=r.get(t),u={hostEl:n,cb:i,cbPayload:a};return o?(e=o.image,!l(e)&&o.pending.push(u)):(e=new Image,e.onload=e.onerror=s,r.put(t,e.__cachedImgObj={image:e,pending:[u]}),e.src=e.__zrImageSrc=t),e}return t}return e}function s(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function l(t){return t&&t.width&&t.height}e.findExistImage=a,e.createOrUpdateImage=o,e.isImageReady=l},"5e97":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("3eba")),a=n("6d8b"),o=n("a15a"),s=o.createSymbol,l=n("2306"),u=n("7919"),c=u.makeBackground,h=n("f934"),d=a.curry,f=a.each,p=l.Group,g=r.extendComponentView({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new p),this._backgroundEl,this.group.add(this._selectorGroup=new p),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,n){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),o=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===o?"right":"left");var s=t.get("selector",!0),l=t.get("selectorPosition",!0);!s||l&&"auto"!==l||(l="horizontal"===o?"end":"start"),this.renderInner(r,t,e,n,s,o,l);var u=t.getBoxLayoutParams(),d={width:n.getWidth(),height:n.getHeight()},f=t.get("padding"),p=h.getLayoutRect(u,d,f),g=this.layoutInner(t,r,p,i,s,l),v=h.getLayoutRect(a.defaults({width:g.width,height:g.height},u),d,f);this.group.attr("position",[v.x-g.x,v.y-g.y]),this.group.add(this._backgroundEl=c(g,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(t,e,n,i,r,o,s){var l=this.getContentGroup(),u=a.createHashMap(),c=e.get("selectedMode"),h=[];n.eachRawSeries(function(t){!t.get("legendHoverLink")&&h.push(t.id)}),f(e.getData(),function(r,a){var o=r.get("name");if(this.newlineDisabled||""!==o&&"\n"!==o){var s=n.getSeriesByName(o)[0];if(!u.get(o))if(s){var f=s.getData(),g=f.getVisual("color"),v=f.getVisual("borderColor");"function"===typeof g&&(g=g(s.getDataParams(0))),"function"===typeof v&&(v=v(s.getDataParams(0)));var _=f.getVisual("legendSymbol")||"roundRect",b=f.getVisual("symbol"),w=this._createItem(o,a,r,e,_,b,t,g,v,c);w.on("click",d(m,o,null,i,h)).on("mouseover",d(y,s.name,null,i,h)).on("mouseout",d(x,s.name,null,i,h)),u.set(o,!0)}else n.eachRawSeries(function(n){if(!u.get(o)&&n.legendVisualProvider){var s=n.legendVisualProvider;if(!s.containName(o))return;var l=s.indexOfName(o),f=s.getItemVisual(l,"color"),p=s.getItemVisual(l,"borderColor"),g="roundRect",v=this._createItem(o,a,r,e,g,null,t,f,p,c);v.on("click",d(m,null,o,i,h)).on("mouseover",d(y,null,o,i,h)).on("mouseout",d(x,null,o,i,h)),u.set(o,!0)}},this)}else l.add(new p({newline:!0}))},this),r&&this._createSelector(r,e,i,o,s)},_createSelector:function(t,e,n,i,r){var a=this.getSelectorGroup();function o(t){var i=t.type,r=new l.Text({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:"all"===i?"legendAllSelect":"legendInverseSelect"})}});a.add(r);var o=e.getModel("selectorLabel"),s=e.getModel("emphasis.selectorLabel");l.setLabelStyle(r.style,r.hoverStyle={},o,s,{defaultText:t.title,isRectText:!1}),l.setHoverStyle(r)}f(t,function(t){o(t)})},_createItem:function(t,e,n,i,r,o,u,c,h,d){var f=i.get("itemWidth"),g=i.get("itemHeight"),m=i.get("inactiveColor"),y=i.get("inactiveBorderColor"),x=i.get("symbolKeepAspect"),_=i.getModel("itemStyle"),b=i.isSelected(t),w=new p,S=n.getModel("textStyle"),M=n.get("icon"),T=n.getModel("tooltip"),C=T.parentModel;r=M||r;var I=s(r,0,0,f,g,b?c:m,null==x||x);if(w.add(v(I,r,_,h,y,b)),!M&&o&&(o!==r||"none"===o)){var D=.8*g;"none"===o&&(o="circle");var A=s(o,(f-D)/2,(g-D)/2,D,D,b?c:m,null==x||x);w.add(v(A,o,_,h,y,b))}var k="left"===u?f+5:-5,O=u,P=i.get("formatter"),L=t;"string"===typeof P&&P?L=P.replace("{name}",null!=t?t:""):"function"===typeof P&&(L=P(t)),w.add(new l.Text({style:l.setTextStyle({},S,{text:L,x:k,y:g/2,textFill:b?S.getTextColor():m,textAlign:O,textVerticalAlign:"middle"})}));var E=new l.Rect({shape:w.getBoundingRect(),invisible:!0,tooltip:T.get("show")?a.extend({content:t,formatter:C.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},T.option):null});return w.add(E),w.eachChild(function(t){t.silent=!0}),E.silent=!d,this.getContentGroup().add(w),l.setHoverStyle(w),w.__legendDataIndex=e,w},layoutInner:function(t,e,n,i,r,a){var o=this.getContentGroup(),s=this.getSelectorGroup();h.box(t.get("orient"),o,t.get("itemGap"),n.width,n.height);var l=o.getBoundingRect(),u=[-l.x,-l.y];if(r){h.box("horizontal",s,t.get("selectorItemGap",!0));var c=s.getBoundingRect(),d=[-c.x,-c.y],f=t.get("selectorButtonGap",!0),p=t.getOrient().index,g=0===p?"width":"height",v=0===p?"height":"width",m=0===p?"y":"x";"end"===a?d[p]+=l[g]+f:u[p]+=c[g]+f,d[1-p]+=l[v]/2-c[v]/2,s.attr("position",d),o.attr("position",u);var y={x:0,y:0};return y[g]=l[g]+f+c[g],y[v]=Math.max(l[v],c[v]),y[m]=Math.min(0,c[m]+d[1-p]),y}return o.attr("position",u),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}});function v(t,e,n,i,r,a){var o;return"line"!==e&&e.indexOf("empty")<0?(o=n.getItemStyle(),t.style.stroke=i,a||(o.stroke=r)):o=n.getItemStyle(["borderWidth","borderColor"]),t.setStyle(o)}function m(t,e,n,i){x(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),y(t,e,n,i)}function y(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function x(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}t.exports=g},"607d":function(t,e,n){var i=n("1fab");e.Dispatcher=i;var r=n("22d1"),a=n("84ec"),o=a.buildTransformer,s="undefined"!==typeof window&&!!window.addEventListener,l=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,u="___zrEVENTSAVED",c=[];function h(t,e,n,i){return n=n||{},i||!r.canvasSupported?d(t,e,n):r.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):d(t,e,n),n}function d(t,e,n){if(t.getBoundingClientRect&&r.domSupported){var i=e.clientX,a=e.clientY;if("CANVAS"===t.nodeName.toUpperCase()){var o=t.getBoundingClientRect();return n.zrX=i-o.left,void(n.zrY=a-o.top)}var s=t[u]||(t[u]={}),l=p(f(t,s),s);if(l)return l(c,i,a),n.zrX=c[0],void(n.zrY=c[1])}n.zrX=n.zrY=0}function f(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,l=a%2,u=(a>>1)%2;s.cssText=["position:absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(o),n.push(o)}return n}function p(t,e){for(var n=e.transformer,i=e.srcCoords,r=!0,a=[],s=[],l=0;l<4;l++){var u=t[l].getBoundingClientRect(),c=2*l,h=u.left,d=u.top;a.push(h,d),r&=i&&h===i[c]&&d===i[c+1],s.push(t[l].offsetLeft,t[l].offsetTop)}return r?n:(e.srcCoords=a,e.transformer=o(a,s))}function g(t){return t||window.event}function v(t,e,n){if(e=g(e),null!=e.zrX)return e;var i=e.type,r=i&&i.indexOf("touch")>=0;if(r){var a="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];a&&h(t,a,e,n)}else h(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&l.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function m(t,e,n,i){s?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}function y(t,e,n,i){s?t.removeEventListener(e,n,i):t.detachEvent("on"+e,n)}var x=s?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function _(t){return 2===t.which||3===t.which}function b(t){return t.which>1}e.clientToLocal=h,e.getNativeEvent=g,e.normalizeEvent=v,e.addEventListener=m,e.removeEventListener=y,e.stop=x,e.isMiddleOrRightButtonOnMouseUpDown=_,e.notLeftMouse=b},6179:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("4319"),o=n("80f0"),s=n("ec6f"),l=n("2b17"),u=l.defaultDimValueGetters,c=l.DefaultDataProvider,h=n("2f45"),d=h.summarizeDimensions,f=n("562e"),p=r.isObject,g="undefined",v=-1,m="e\0\0",y={float:typeof Float64Array===g?Array:Float64Array,int:typeof Int32Array===g?Array:Int32Array,ordinal:Array,number:Array,time:Array},x=typeof Uint32Array===g?Array:Uint32Array,_=typeof Int32Array===g?Array:Int32Array,b=typeof Uint16Array===g?Array:Uint16Array;function w(t){return t._rawCount>65535?x:b}function S(t){var e=t.constructor;return e===Array?t.slice():new e(t)}var M=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],T=["_extent","_approximateExtent","_rawExtent"];function C(t,e){r.each(M.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods,r.each(T,function(n){t[n]=r.clone(e[n])}),t._calculationInfo=r.extend(e._calculationInfo)}var I=function(t,e){t=t||["x","y"];for(var n={},i=[],a={},o=0;o<t.length;o++){var s=t[o];r.isString(s)?s=new f({name:s}):s instanceof f||(s=new f(s));var l=s.name;s.type=s.type||"float",s.coordDim||(s.coordDim=l,s.coordDimIndex=0),s.otherDims=s.otherDims||{},i.push(l),n[l]=s,s.index=o,s.createInvertedIndices&&(a[l]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=d(this),this._invertedIndicesMap=a,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},D=I.prototype;function A(t,e,n,i,r){var a=y[e.type],o=i-1,s=e.name,l=t[s][o];if(l&&l.length<n){for(var u=new a(Math.min(r-o*n,n)),c=0;c<l.length;c++)u[c]=l[c];t[s][o]=u}for(var h=i*n;h<r;h+=n)t[s].push(new a(Math.min(r-h,n)))}function k(t){var e=t._invertedIndicesMap;r.each(e,function(n,i){var r=t._dimensionInfos[i],a=r.ordinalMeta;if(a){n=e[i]=new _(a.categories.length);for(var o=0;o<n.length;o++)n[o]=v;for(o=0;o<t._count;o++)n[t.get(i,o)]=o}})}function O(t,e,n){var i;if(null!=e){var r=t._chunkSize,a=Math.floor(n/r),o=n%r,s=t.dimensions[e],l=t._storage[s][a];if(l){i=l[o];var u=t._dimensionInfos[s].ordinalMeta;u&&u.categories.length&&(i=u.categories[i])}}return i}function P(t){return t}function L(t){return t<this._count&&t>=0?this._indices[t]:-1}function E(t,e){var n=t._idList[e];return null==n&&(n=O(t,t._idDimIdx,e)),null==n&&(n=m+e),n}function R(t){return r.isArray(t)||(t=[t]),t}function N(t,e){var n=t.dimensions,i=new I(r.map(n,t.getDimensionInfo,t),t.hostModel);C(i,t);for(var a=i._storage={},o=t._storage,s=0;s<n.length;s++){var l=n[s];o[l]&&(r.indexOf(e,l)>=0?(a[l]=B(o[l]),i._rawExtent[l]=z(),i._extent[l]=null):a[l]=o[l])}return i}function B(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=S(t[n]);return e}function z(){return[1/0,-1/0]}D.type="list",D.hasItemOption=!0,D.getDimension=function(t){return"number"!==typeof t&&(isNaN(t)||this._dimensionInfos.hasOwnProperty(t))||(t=this.dimensions[t]),t},D.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},D.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},D.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return!0===e?(i||[]).slice():i&&i[e]},D.initData=function(t,e,n){var i=s.isInstance(t)||r.isArrayLike(t);i&&(t=new c(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=u[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=u.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},D.getProvider=function(){return this._rawData},D.appendData=function(t){var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},D.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),u=this._chunkCount,c=0;c<a;c++){var h=r[c];o[h]||(o[h]=z()),i[h]||(i[h]=[]),A(i,this._dimensionInfos[h],n,u,l),this._chunkCount=i[h].length}for(var d=new Array(a),f=s;f<l;f++){for(var p=f-s,g=Math.floor(f/n),v=f%n,m=0;m<a;m++){h=r[m];var y=this._dimValueGetterArrayRows(t[p]||d,h,p,m);i[h][g][v]=y;var x=o[h];y<x[0]&&(x[0]=y),y>x[1]&&(x[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},k(this)},D._initDataFromProvider=function(t,e){if(!(t>=e)){for(var n,i=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,u=this._nameList,c=this._idList,h=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;p<s;p++){var g=o[p];h[g]||(h[g]=z());var v=l[g];0===v.otherDims.itemName&&(n=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),A(a,v,i,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;y<e;y++){m=r.getItem(y,m);for(var x=Math.floor(y/i),_=y%i,b=0;b<s;b++){g=o[b];var w=a[g][x],S=this._dimValueGetter(m,g,y,b);w[_]=S;var M=h[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var T=u[y];if(m&&null==T)if(null!=m.name)u[y]=T=m.name;else if(null!=n){var C=o[n],I=a[C][x];if(I){T=I[_];var D=l[C].ordinalMeta;D&&D.categories.length&&(T=D.categories[T])}}var O=null==m?null:m.id;null==O&&null!=T&&(d[T]=d[T]||0,O=T,d[T]>0&&(O+="__ec__"+d[T]),d[T]++),null!=O&&(c[y]=O)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},k(this)}},D.count=function(){return this._count},D.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array){r=new e(n);for(var i=0;i<n;i++)r[i]=t[i]}else r=new e(t.buffer,0,n)}else{e=w(this);var r=new e(this.count());for(i=0;i<r.length;i++)r[i]=i}return r},D.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._storage;if(!n[t])return NaN;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=n[t][i],o=a[r];return o},D.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._storage[t];if(!n)return NaN;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=n[i];return a[r]},D._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize,r=this._storage[t][n];return r[i]},D.getValues=function(t,e){var n=[];r.isArray(t)||(e=t,t=this.dimensions);for(var i=0,a=t.length;i<a;i++)n.push(this.get(t[i],e));return n},D.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this.get(e[n],t)))return!1;return!0},D.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=z();if(!e)return n;var i,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(i=this._extent[t],i)return i.slice();i=n;for(var o=i[0],s=i[1],l=0;l<r;l++){var u=this._getFast(t,this.getRawIndex(l));u<o&&(o=u),u>s&&(s=u)}return i=[o,s],this._extent[t]=i,i},D.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},D.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},D.getCalculationInfo=function(t){return this._calculationInfo[t]},D.setCalculationInfo=function(t,e){p(t)?r.extend(this._calculationInfo,t):this._calculationInfo[t]=e},D.getSum=function(t){var e=this._storage[t],n=0;if(e)for(var i=0,r=this.count();i<r;i++){var a=this.get(t,i);isNaN(a)||(n+=a)}return n},D.getMedian=function(t){var e=[];this.each(t,function(t,n){isNaN(t)||e.push(t)});var n=[].concat(e).sort(function(t,e){return t-e}),i=this.count();return 0===i?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},D.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],i=n[e];return null==i||isNaN(i)?v:i},D.indexOfName=function(t){for(var e=0,n=this.count();e<n;e++)if(this.getName(e)===t)return e;return-1},D.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;var i=0,r=this._count-1;while(i<=r){var a=(i+r)/2|0;if(e[a]<t)i=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},D.indicesOfNearest=function(t,e,n){var i=this._storage,r=i[t],a=[];if(!r)return a;null==n&&(n=1/0);for(var o=1/0,s=-1,l=0,u=0,c=this.count();u<c;u++){var h=e-this.get(t,u),d=Math.abs(h);d<=n&&((d<o||d===o&&h>=0&&s<0)&&(o=d,s=h,l=0),h===s&&(a[l++]=u))}return a.length=l,a},D.getRawIndex=P,D.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},D.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||O(this,this._nameDimIdx,e)||""},D.getId=function(t){return E(this,this.getRawIndex(t))},D.each=function(t,e,n,i){"use strict";if(this._count){"function"===typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=r.map(R(t),this.getDimension,this);for(var a=t.length,o=0;o<this.count();o++)switch(a){case 0:e.call(n,o);break;case 1:e.call(n,this.get(t[0],o),o);break;case 2:e.call(n,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var s=0,l=[];s<a;s++)l[s]=this.get(t[s],o);l[s]=o,e.apply(n,l)}}},D.filterSelf=function(t,e,n,i){"use strict";if(this._count){"function"===typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=r.map(R(t),this.getDimension,this);for(var a=this.count(),o=w(this),s=new o(a),l=[],u=t.length,c=0,h=t[0],d=0;d<a;d++){var f,p=this.getRawIndex(d);if(0===u)f=e.call(n,d);else if(1===u){var g=this._getFast(h,p);f=e.call(n,g,d)}else{for(var v=0;v<u;v++)l[v]=this._getFast(h,p);l[v]=d,f=e.apply(n,l)}f&&(s[c++]=p)}return c<a&&(this._indices=s),this._count=c,this._extent={},this.getRawIndex=this._indices?L:P,this}},D.selectRange=function(t){"use strict";if(this._count){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);var i=e.length;if(i){var r=this.count(),a=w(this),o=new a(r),s=0,l=e[0],u=t[l][0],c=t[l][1],h=!1;if(!this._indices){var d=0;if(1===i){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;m<v;m++){var y=g[m];(y>=u&&y<=c||isNaN(y))&&(o[s++]=d),d++}h=!0}else if(2===i){f=this._storage[l];var x=this._storage[e[1]],_=t[e[1]][0],b=t[e[1]][1];for(p=0;p<this._chunkCount;p++){g=f[p];var S=x[p];for(v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;m<v;m++){y=g[m];var M=S[m];(y>=u&&y<=c||isNaN(y))&&(M>=_&&M<=b||isNaN(M))&&(o[s++]=d),d++}}h=!0}}if(!h)if(1===i)for(m=0;m<r;m++){var T=this.getRawIndex(m);y=this._getFast(l,T);(y>=u&&y<=c||isNaN(y))&&(o[s++]=T)}else for(m=0;m<r;m++){var C=!0;for(T=this.getRawIndex(m),p=0;p<i;p++){var I=e[p];y=this._getFast(n,T);(y<t[I][0]||y>t[I][1])&&(C=!1)}C&&(o[s++]=this.getRawIndex(m))}return s<r&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?L:P,this}}},D.mapArray=function(t,e,n,i){"use strict";"function"===typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},D.map=function(t,e,n,i){"use strict";n=n||i||this,t=r.map(R(t),this.getDimension,this);var a=N(this,t);a._indices=this._indices,a.getRawIndex=a._indices?L:P;for(var o=a._storage,s=[],l=this._chunkSize,u=t.length,c=this.count(),h=[],d=a._rawExtent,f=0;f<c;f++){for(var p=0;p<u;p++)h[p]=this.get(t[p],f);h[u]=f;var g=e&&e.apply(n,h);if(null!=g){"object"!==typeof g&&(s[0]=g,g=s);for(var v=this.getRawIndex(f),m=Math.floor(v/l),y=v%l,x=0;x<g.length;x++){var _=t[x],b=g[x],w=d[_],S=o[_];S&&(S[m][y]=b),b<w[0]&&(w[0]=b),b>w[1]&&(w[1]=b)}}}return a},D.downSample=function(t,e,n,i){for(var r=N(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],u=this.count(),c=this._chunkSize,h=r._rawExtent[t],d=new(w(this))(u),f=0,p=0;p<u;p+=s){s>u-p&&(s=u-p,o.length=s);for(var g=0;g<s;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/c),y=v%c;o[g]=l[m][y]}var x=n(o),_=this.getRawIndex(Math.min(p+i(o,x)||0,u-1)),b=Math.floor(_/c),S=_%c;l[b][S]=x,x<h[0]&&(h[0]=x),x>h[1]&&(h[1]=x),d[f++]=_}return r._count=f,r._indices=d,r.getRawIndex=L,r},D.getItemModel=function(t){var e=this.hostModel;return new a(this.getRawDataItem(t),e,e&&e.ecModel)},D.diff=function(t){var e=this;return new o(t?t.getIndices():[],this.getIndices(),function(e){return E(t,e)},function(t){return E(e,t)})},D.getVisual=function(t){var e=this._visual;return e&&e[t]},D.setVisual=function(t,e){if(p(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},D.setLayout=function(t,e){if(p(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},D.getLayout=function(t){return this._layout[t]},D.getItemLayout=function(t){return this._itemLayouts[t]},D.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?r.extend(this._itemLayouts[t]||{},e):e},D.clearItemLayouts=function(){this._itemLayouts.length=0},D.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},D.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,p(e))for(var a in e)e.hasOwnProperty(a)&&(i[a]=e[a],r[a]=!0);else i[e]=n,r[e]=!0},D.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var F=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};D.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(F,e)),this._graphicEls[t]=e},D.getItemGraphicEl=function(t){return this._graphicEls[t]},D.eachItemGraphicEl=function(t,e){r.each(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},D.cloneShallow=function(t){if(!t){var e=r.map(this.dimensions,this.getDimensionInfo,this);t=new I(e,this.hostModel)}if(t._storage=this._storage,C(t,this),this._indices){var n=this._indices.constructor;t._indices=new n(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?L:P,t},D.wrapMethod=function(t,e){var n=this[t];"function"===typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(r.slice(arguments)))})},D.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],D.CHANGABLE_METHODS=["filterSelf","selectRange"];var V=I;t.exports=V},"620b":function(t,e,n){var i=n("401b"),r=i.distance;function a(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function o(t,e){for(var n=t.length,i=[],o=0,s=1;s<n;s++)o+=r(t[s-1],t[s]);var l=o/2;l=l<n?n:l;for(s=0;s<l;s++){var u,c,h,d=s/(l-1)*(e?n:n-1),f=Math.floor(d),p=d-f,g=t[f%n];e?(u=t[(f-1+n)%n],c=t[(f+1)%n],h=t[(f+2)%n]):(u=t[0===f?f:f-1],c=t[f>n-2?n-1:f+1],h=t[f>n-3?n-1:f+2]);var v=p*p,m=p*v;i.push([a(u[0],g[0],c[0],h[0],p,v,m),a(u[1],g[1],c[1],h[1],p,v,m)])}return i}t.exports=o},"625e":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=".",o="___EC__COMPONENT__CONTAINER___";function s(t){var e={main:"",sub:""};return t&&(t=t.split(a),e.main=t[0]||"",e.sub=t[1]||""),e}function l(t){r.assert(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function u(t,e){t.$constructor=t,t.extend=function(t){var e=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return r.extend(n.prototype,t),n.extend=this.extend,n.superCall=d,n.superApply=f,r.inherits(n,this),n.superClass=e,n}}var c=0;function h(t){var e=["__\0is_clz",c++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function d(t,e){var n=r.slice(arguments,2);return this.superClass.prototype[e].apply(t,n)}function f(t,e,n){return this.superClass.prototype[e].apply(t,n)}function p(t,e){e=e||{};var n={};function i(t){var e=n[t.main];return e&&e[o]||(e=n[t.main]={},e[o]=!0),e}if(t.registerClass=function(t,e){if(e)if(l(e),e=s(e),e.sub){if(e.sub!==o){var r=i(e);r[e.sub]=t}}else n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[o]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=s(t);var e=[],i=n[t.main];return i&&i[o]?r.each(i,function(t,n){n!==o&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=s(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return r.each(n,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){t=s(t);var e=n[t.main];return e&&e[o]},t.parseClassType=s,e.registerWhenExtend){var a=t.extend;a&&(t.extend=function(e){var n=a.call(this,e);return t.registerClass(n,e.type)})}return t}function g(t,e){}e.parseClassType=s,e.enableClassExtend=u,e.enableClassCheck=h,e.enableClassManagement=p,e.setReadOnly=g},6679:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("3eba")),a=n("cd33"),o=r.extendComponentView({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){this.axisPointerClass&&a.fixValue(t),o.superApply(this,"render",arguments),s(this,t,e,n,i,!0)},updateAxisPointer:function(t,e,n,i,r){s(this,t,e,n,i,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),o.superApply(this,"remove",arguments)},dispose:function(t,e){l(this,e),o.superApply(this,"dispose",arguments)}});function s(t,e,n,i,r,s){var u=o.getAxisPointerClass(t.axisPointerClass);if(u){var c=a.getAxisPointerModel(e);c?(t._axisPointer||(t._axisPointer=new u)).render(e,c,i,s):l(t,i)}}function l(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var u=[];o.registerAxisPointerClass=function(t,e){u[t]=e},o.getAxisPointerClass=function(t){return t&&u[t]};var c=o;t.exports=c},"67cc":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("3eba")),a=n("6d8b"),o=n("2306"),s=n("e7aa"),l=s.setLabel,u=n("4319"),c=n("b5c7"),h=n("cbe5"),d=n("88b3"),f=d.throttle,p=n("b0af"),g=p.createClipPath,v=n("c2be"),m=["itemStyle","barBorderWidth"],y=[0,0];function x(t,e){var n=t.getArea&&t.getArea();if("cartesian2d"===t.type){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}a.extend(u.prototype,c);var _=r.extendChartView({type:"bar",render:function(t,e,n){this._updateDrawMode(t);var i=t.get("coordinateSystem");return"cartesian2d"!==i&&"polar"!==i||(this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n)),this.group},incrementalPrepareRender:function(t,e,n){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e,n,i){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t,e,n){var i,r=this.group,a=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?i=u.isHorizontal():"polar"===l.type&&(i="angle"===u.dim);var c=t.isAnimationEnabled()?t:null,h=t.get("clip",!0),d=x(l,a);r.removeClipPath();var f=t.get("roundCap",!0);a.diff(s).add(function(e){if(a.hasValue(e)){var n=a.getItemModel(e),o=I[l.type](a,e,n);if(h){var s=S[l.type](d,o);if(s)return void r.remove(u)}var u=M[l.type](e,o,i,c,!1,f);a.setItemGraphicEl(e,u),r.add(u),A(u,a,e,n,o,t,i,"polar"===l.type)}}).update(function(e,n){var u=s.getItemGraphicEl(n);if(a.hasValue(e)){var p=a.getItemModel(e),g=I[l.type](a,e,p);if(h){var v=S[l.type](d,g);if(v)return void r.remove(u)}u?o.updateProps(u,{shape:g},c,e):u=M[l.type](e,g,i,c,!0,f),a.setItemGraphicEl(e,u),r.add(u),A(u,a,e,p,g,t,i,"polar"===l.type)}else r.remove(u)}).remove(function(t){var e=s.getItemGraphicEl(t);"cartesian2d"===l.type?e&&T(t,c,e):e&&C(t,c,e)}).execute(),this._data=a},_renderLarge:function(t,e,n){this._clear(),P(t,this.group);var i=t.get("clip",!0)?g(t.coordinateSystem,!1,t):null;i?this.group.setClipPath(i):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){P(e,this.group,!0)},dispose:a.noop,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,n=this._data;t&&t.get("animation")&&n&&!this._isLargeDraw?n.eachItemGraphicEl(function(e){"sector"===e.type?C(e.dataIndex,t,e):T(e.dataIndex,t,e)}):e.removeAll(),this._data=null}}),b=Math.max,w=Math.min,S={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=b(e.x,t.x),a=w(e.x+e.width,t.x+t.width),o=b(e.y,t.y),s=w(e.y+e.height,t.y+t.height);e.x=r,e.y=o,e.width=a-r,e.height=s-o;var l=e.width<0||e.height<0;return n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),l},polar:function(t){return!1}},M={cartesian2d:function(t,e,n,i,r){var s=new o.Rect({shape:a.extend({},e)});if(i){var l=s.shape,u=n?"height":"width",c={};l[u]=0,c[u]=e[u],o[r?"updateProps":"initProps"](s,{shape:c},i,t)}return s},polar:function(t,e,n,i,r,s){var l=e.startAngle<e.endAngle,u=!n&&s?v:o.Sector,c=new u({shape:a.defaults({clockwise:l},e)});if(i){var h=c.shape,d=n?"r":"endAngle",f={};h[d]=n?0:e.startAngle,f[d]=e[d],o[r?"updateProps":"initProps"](c,{shape:f},i,t)}return c}};function T(t,e,n){n.style.text=null,o.updateProps(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function C(t,e,n){n.style.text=null,o.updateProps(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}var I={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=k(n,i),a=i.width>0?1:-1,o=i.height>0?1:-1;return{x:i.x+a*r/2,y:i.y+o*r/2,width:i.width-a*r,height:i.height-o*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}};function D(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function A(t,e,n,i,r,s,u,c){var h=e.getItemVisual(n,"color"),d=e.getItemVisual(n,"opacity"),f=e.getVisual("borderColor"),p=i.getModel("itemStyle"),g=i.getModel("emphasis.itemStyle").getBarItemStyle();c||t.setShape("r",p.get("barBorderRadius")||0),t.useStyle(a.defaults({stroke:D(r)?"none":f,fill:D(r)?"none":h,opacity:d},p.getBarItemStyle()));var v=i.getShallow("cursor");v&&t.attr("cursor",v);var m=u?r.height>0?"bottom":"top":r.width>0?"left":"right";c||l(t.style,g,i,h,s,n,m),D(r)&&(g.fill=g.stroke="none"),o.setHoverStyle(t,g)}function k(t,e){var n=t.get(m)||0;return Math.min(n,Math.abs(e.width),Math.abs(e.height))}var O=h.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var n=e.points,i=this.__startPoint,r=this.__baseDimIdx,a=0;a<n.length;a+=2)i[r]=n[a+r],t.moveTo(i[0],i[1]),t.lineTo(n[a],n[a+1])}});function P(t,e,n){var i=t.getData(),r=[],a=i.getLayout("valueAxisHorizontal")?1:0;r[1-a]=i.getLayout("valueAxisStart");var o=new O({shape:{points:i.getLayout("largePoints")},incremental:!!n,__startPoint:r,__baseDimIdx:a,__largeDataIndices:i.getLayout("largeDataIndices"),__barWidth:i.getLayout("barWidth")});e.add(o),R(o,t,i),o.seriesIndex=t.seriesIndex,t.get("silent")||(o.on("mousedown",L),o.on("mousemove",L))}var L=f(function(t){var e=this,n=E(e,t.offsetX,t.offsetY);e.dataIndex=n>=0?n:null},30,!1);function E(t,e,n){var i=t.__baseDimIdx,r=1-i,a=t.shape.points,o=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];y[0]=e,y[1]=n;for(var u=y[i],c=y[1-i],h=u-s,d=u+s,f=0,p=a.length/2;f<p;f++){var g=2*f,v=a[g+i],m=a[g+r];if(v>=h&&v<=d&&(l<=m?c>=l&&c<=m:c>=m&&c<=l))return o[f]}return-1}function R(t,e,n){var i=n.getVisual("borderColor")||n.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}t.exports=_},"68ab":function(t,e,n){var i=n("4a3f"),r=i.quadraticProjectPoint;function a(t,e,n,i,a,o,s,l,u){if(0===s)return!1;var c=s;if(u>e+c&&u>i+c&&u>o+c||u<e-c&&u<i-c&&u<o-c||l>t+c&&l>n+c&&l>a+c||l<t-c&&l<n-c&&l<a-c)return!1;var h=r(t,e,n,i,a,o,l,u,null);return h<=c/2}e.containStroke=a},"697e":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("18c0"),o=n("89e3"),s=n("e0d8"),l=n("3842"),u=n("9d57"),c=u.prepareLayoutBarSeries,h=u.makeColumnLayout,d=u.retrieveColumnLayout,f=n("9850");function p(t,e){var n,i,a,o=t.type,s=e.getMin(),u=e.getMax(),d=null!=s,f=null!=u,p=t.getExtent();"ordinal"===o?n=e.getCategories().length:(i=e.get("boundaryGap"),r.isArray(i)||(i=[i||0,i||0]),"boolean"===typeof i[0]&&(i=[0,0]),i[0]=l.parsePercent(i[0],1),i[1]=l.parsePercent(i[1],1),a=p[1]-p[0]||Math.abs(p[0])),null==s&&(s="ordinal"===o?n?0:NaN:p[0]-i[0]*a),null==u&&(u="ordinal"===o?n?n-1:NaN:p[1]+i[1]*a),"dataMin"===s?s=p[0]:"function"===typeof s&&(s=s({min:p[0],max:p[1]})),"dataMax"===u?u=p[1]:"function"===typeof u&&(u=u({min:p[0],max:p[1]})),(null==s||!isFinite(s))&&(s=NaN),(null==u||!isFinite(u))&&(u=NaN),t.setBlank(r.eqNaN(s)||r.eqNaN(u)||"ordinal"===o&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(s>0&&u>0&&!d&&(s=0),s<0&&u<0&&!f&&(u=0));var v=e.ecModel;if(v&&"time"===o){var m,y=c("bar",v);if(r.each(y,function(t){m|=t.getBaseAxis()===e.axis}),m){var x=h(y),_=g(s,u,e,x);s=_.min,u=_.max}}return[s,u]}function g(t,e,n,i){var a=n.axis.getExtent(),o=a[1]-a[0],s=d(i,n.axis);if(void 0===s)return{min:t,max:e};var l=1/0;r.each(s,function(t){l=Math.min(t.offset,l)});var u=-1/0;r.each(s,function(t){u=Math.max(t.offset+t.width,u)}),l=Math.abs(l),u=Math.abs(u);var c=l+u,h=e-t,f=1-(l+u)/o,p=h/f-h;return e+=p*(u/c),t-=p*(l/c),{min:t,max:e}}function v(t,e){var n=p(t,e),i=null!=e.getMin(),r=null!=e.getMax(),a=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:a,fixMin:i,fixMax:r,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function m(t,e){if(e=e||t.get("type"),e)switch(e){case"category":return new a(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new o;default:return(s.getClass(e)||o).create(t)}}function y(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}function x(t){var e=t.getLabelModel().get("formatter"),n="category"===t.type?t.scale.getExtent()[0]:null;return"string"===typeof e?(e=function(e){return function(n){return n=t.scale.getLabel(n),e.replace("{value}",null!=n?n:"")}}(e),e):"function"===typeof e?function(i,r){return null!=n&&(r=i-n),e(_(t,i),r)}:function(e){return t.scale.getLabel(e)}}function _(t,e){return"category"===t.type?t.scale.getLabel(e):e}function b(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,r,a="category"===t.type,o=n.getExtent();a?r=n.count():(i=n.getTicks(),r=i.length);var s,l=t.getLabelModel(),u=x(t),c=1;r>40&&(c=Math.ceil(r/40));for(var h=0;h<r;h+=c){var d=i?i[h]:o[0]+h,f=u(d),p=l.getTextRect(f),g=w(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function w(t,e){var n=e*Math.PI/180,i=t.plain(),r=i.width,a=i.height,o=r*Math.cos(n)+a*Math.sin(n),s=r*Math.sin(n)+a*Math.cos(n),l=new f(i.x,i.y,o,s);return l}function S(t){var e=t.get("interval");return null==e?"auto":e}function M(t){return"category"===t.type&&0===S(t.getLabelModel())}n("216a"),n("8c2a"),e.getScaleExtent=p,e.niceScaleExtent=v,e.createScaleByModel=m,e.ifAxisCrossZero=y,e.makeLabelFormatter=x,e.getAxisRawValue=_,e.estimateLabelUnionRect=b,e.getOptionCategoryInterval=S,e.shouldShowAllLabels=M},"697e7":function(t,e,n){var i=n("de00"),r=n("22d1"),a=n("6d8b"),o=n("d2cf"),s=n("afa0"),l=n("ed21"),u=n("30a3"),c=n("cdaa"),h=!r.canvasSupported,d={canvas:l},f={},p="4.2.0";function g(t,e){var n=new _(i(),t,e);return f[n.id]=n,n}function v(t){if(t)t.dispose();else{for(var e in f)f.hasOwnProperty(e)&&f[e].dispose();f={}}return this}function m(t){return f[t]}function y(t,e){d[t]=e}function x(t){delete f[t]}var _=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,l=new s,f=n.renderer;if(h){if(!d.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");f="vml"}else f&&d[f]||(f="canvas");var p=new d[f](e,l,n,t);this.storage=l,this.painter=p;var g=r.node||r.worker?null:new c(p.getViewportRoot(),p.root);this.handler=new o(l,p,g,p.root),this.animation=new u({stage:{update:a.bind(this.flush,this)}}),this.animation.start(),this._needsRefresh;var v=l.delFromStorage,m=l.addToStorage;l.delFromStorage=function(t){v.call(l,t),t&&t.removeSelfFromZr(i)},l.addToStorage=function(t){m.call(l,t),t.addSelfToZr(i)}};_.prototype={constructor:_,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var n=this.painter.addHover(t,e);return this.refreshHover(),n}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,x(this.id)}},e.version=p,e.init=g,e.dispose=v,e.getInstance=m,e.registerPainter=y},"69ff":function(t,e,n){var i=n("6d8b"),r=i.each,a=i.map,o=i.isFunction,s=i.createHashMap,l=i.noop,u=n("f47d"),c=u.createTask,h=n("8918"),d=h.getUID,f=n("7e63"),p=n("843e"),g=n("e0d3"),v=g.normalizeToArray;function m(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=s()}var y=m.prototype;function x(t,e,n,i,a){var o;function s(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}a=a||{},r(e,function(e,r){if(!a.visualType||a.visualType===e.visualType){var l=t._stageTaskMap.get(e.uid),u=l.seriesTaskMap,c=l.overallTask;if(c){var h,d=c.agentStubMap;d.each(function(t){s(a,t)&&(t.dirty(),h=!0)}),h&&c.dirty(),_(c,i);var f=t.getPerformArgs(c,a.block);d.each(function(t){t.perform(f)}),o|=c.perform(f)}else u&&u.each(function(r,l){s(a,r)&&r.dirty();var u=t.getPerformArgs(r,a.block);u.skip=!e.performRawSeries&&n.isSeriesFiltered(r.context.model),_(r,i),o|=r.perform(u)})}}),t.unfinished|=o}y.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},y.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,a=r?n.step:null,o=i&&i.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},y.getPipeline=function(t){return this._pipelineMap.get(t)},y.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData(),r=i.count(),a=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:a,modDataCount:s,large:o}},y.restorePipelines=function(t){var e=this,n=e._pipelineMap=s();t.eachSeries(function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),P(e,t,t.dataTask)})},y.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),n=this.api;r(this._allHandlers,function(i){var r=t.get(i.uid)||t.set(i.uid,[]);i.reset&&b(this,i,r,e,n),i.overallReset&&w(this,i,r,e,n)},this)},y.prepareView=function(t,e,n,i){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=n,a.api=i,r.__block=!t.incrementalPrepareRender,P(this,e,r)},y.performDataProcessorTasks=function(t,e){x(this,this._dataProcessorHandlers,t,e,{block:!0})},y.performVisualTasks=function(t,e,n){x(this,this._visualHandlers,t,e,n)},y.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},y.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var _=y.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};function b(t,e,n,i,r){var a=n.seriesTaskMap||(n.seriesTaskMap=s()),o=e.seriesType,l=e.getTargetSeries;function u(n){var o=n.uid,s=a.get(o)||a.set(o,c({plan:I,reset:D,count:O}));s.context={model:n,ecModel:i,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},P(t,n,s)}e.createOnAllSeries?i.eachRawSeries(u):o?i.eachRawSeriesByType(o,u):l&&l(i,r).each(u);var h=t._pipelineMap;a.each(function(t,e){h.get(e)||(t.dispose(),a.removeKey(e))})}function w(t,e,n,i,a){var o=n.overallTask=n.overallTask||c({reset:S});o.context={ecModel:i,api:a,overallReset:e.overallReset,scheduler:t};var l=o.agentStubMap=o.agentStubMap||s(),u=e.seriesType,h=e.getTargetSeries,d=!0,f=e.modifyOutputEnd;function p(e){var n=e.uid,i=l.get(n);i||(i=l.set(n,c({reset:M,onDirty:C})),o.dirty()),i.context={model:e,overallProgress:d,modifyOutputEnd:f},i.agent=o,i.__block=d,P(t,e,i)}u?i.eachRawSeriesByType(u,p):h?h(i,a).each(p):(d=!1,r(i.getSeries(),p));var g=t._pipelineMap;l.each(function(t,e){g.get(e)||(t.dispose(),o.dirty(),l.removeKey(e))})}function S(t){t.overallReset(t.ecModel,t.api,t.payload)}function M(t,e){return t.overallProgress&&T}function T(){this.agent.dirty(),this.getDownstream().dirty()}function C(){this.agent&&this.agent.dirty()}function I(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function D(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=v(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?a(e,function(t,e){return k(e)}):A}var A=k(0);function k(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(i,a);else r&&r.progress&&r.progress(e,i)}}function O(t){return t.data.count()}function P(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);!r.head&&(r.head=n),r.tail&&r.tail.pipe(n),r.tail=n,n.__idxInPipeline=r.count++,n.__pipeline=r}function L(t){E=null;try{t(R,N)}catch(e){}return E}m.wrapStageHandler=function(t,e){return o(t)&&(t={overallReset:t,seriesType:L(t)}),t.uid=d("stageHandler"),e&&(t.visualType=e),t};var E,R={},N={};function B(t,e){for(var n in e.prototype)t[n]=l}B(R,f),B(N,p),R.eachSeriesByType=R.eachRawSeriesByType=function(t){E=t},R.eachComponent=function(t){"series"===t.mainType&&t.subType&&(E=t.subType)};var z=m;t.exports=z},"6ba1":function(t,e,n){},"6c5d":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{position:"relative"}},[n("div",{staticStyle:{"font-size":"14px",color:"rgb(8, 47, 109)","font-weight":"bold","margin-bottom":"10px","margin-left":"5px"}},[t._v("\n    业务员拜访情况总览\n  ")]),n("el-row",{attrs:{gutter:10}},[n("el-col",{staticStyle:{width:"260px",padding:"5px 0 5px 0"}},[n("div",{staticClass:"filter-container"},[n("div",{staticClass:"filter-title"},[t._v("经营属性")]),n("el-checkbox-group",{model:{value:t.params.BusinessProperty,callback:function(e){t.$set(t.params,"BusinessProperty",e)},expression:"params.BusinessProperty"}},[n("el-checkbox-button",{attrs:{label:"德乐"}}),n("el-checkbox-button",{attrs:{label:"金富力"}}),n("el-checkbox-button",{attrs:{label:"金富力,德乐"}})],1)],1)]),n("el-col",{staticStyle:{width:"160px",padding:"5px 0 5px 0"}},[n("div",{staticClass:"filter-container"},[n("div",{staticClass:"filter-title"},[t._v("客户类型")]),n("el-checkbox-group",{model:{value:t.params.subType,callback:function(e){t.$set(t.params,"subType",e)},expression:"params.subType"}},[n("el-checkbox-button",{attrs:{label:"店招店"}}),n("el-checkbox-button",{attrs:{label:"其它"}})],1)],1)]),n("el-col",{staticStyle:{width:"340px",padding:"5px 0 5px 0"}},[n("div",{staticClass:"filter-container"},[n("div",{staticClass:"filter-title"},[t._v("客户状态")]),n("el-checkbox-group",{model:{value:t.params.CustomerStatus,callback:function(e){t.$set(t.params,"CustomerStatus",e)},expression:"params.CustomerStatus"}},[n("el-checkbox-button",{attrs:{label:"NA"}}),n("el-checkbox-button",{attrs:{label:"活跃客户"}}),n("el-checkbox-button",{attrs:{label:"流失客户"}}),n("el-checkbox-button",{attrs:{label:"非活跃客户"}})],1)],1)]),n("el-col",{staticStyle:{width:"830px",padding:"5px 0 5px 0"}},[n("div",{staticClass:"filter-container"},[n("div",{staticClass:"filter-title"},[t._v("客户来源")]),n("el-checkbox-group",{model:{value:t.radio3,callback:function(e){t.radio3=e},expression:"radio3"}},[n("el-checkbox-button",{attrs:{label:"乘用车DSR业务"}}),n("el-checkbox-button",{attrs:{label:"乘用车直播"}}),n("el-checkbox-button",{attrs:{label:"商用油DSR业务"}}),n("el-checkbox-button",{attrs:{label:"商用油直播"}}),n("el-checkbox-button",{attrs:{label:"新零售业务"}}),n("el-checkbox-button",{attrs:{label:"工程机械DSR业务"}}),n("el-checkbox-button",{attrs:{label:"建筑渣土车队DSR业务"}})],1)],1)])],1),n("el-row",{attrs:{gutter:5,span:24}},[n("el-col",{attrs:{xs:24,sm:24,md:12,lg:12,xl:12}},[n("chart1")],1),n("el-col",{attrs:{xs:24,sm:24,md:12,lg:12,xl:12}},[n("chart2")],1)],1),n("div",{staticStyle:{margin:"5px 0 10px 0"}},[n("span",{staticStyle:{"font-size":"13px",padding:"5px","font-weight":"400"}},[t._v("\n      巡店是否记录停留时间：")]),n("el-radio",{attrs:{label:1},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v("是")]),n("el-radio",{attrs:{label:0},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v("否")])],1),n("contentList"),n("pagination")],1)},r=[],a=(n("8e6e"),n("ac6a"),n("456d"),n("bd86")),o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{attrs:{inline:!0,"label-width":"70px","label-position":"right"}},[n("el-col",{attrs:{xs:24,sm:24,md:24,lg:24,xl:24}},[n("el-col",{attrs:{xs:24,sm:24,md:10,lg:10,xl:10}},[n("el-form-item",{attrs:{label:"DSR："}},[n("el-input",{model:{value:t.params.name,callback:function(e){t.$set(t.params,"name",e)},expression:"params.name"}})],1)],1),n("el-col",{attrs:{xs:24,sm:24,md:10,lg:10,xl:10}},[n("el-form-item",{attrs:{label:"拜访日期："}},[n("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.innerValue,callback:function(e){t.innerValue=e},expression:"innerValue"}})],1)],1),n("el-col",{attrs:{xs:24,sm:24,md:4,lg:4,xl:4}},[n("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")])],1)],1)],1)],1)},s=[],l=(n("386d"),n("2f62")),u=n("5a0c"),c=n.n(u);function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(n,!0).forEach(function(e){Object(a["a"])(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(n).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var f={name:"activity-search",data:function(){return{value:null}},computed:d({},Object(l["c"])({params:function(t){return t.list.search}}),{innerValue:{get:function(){return this.value},set:function(t){this.value=t,t?this.$store.commit("UPDATE_ACTIVITY_DATE_RANGE",{start:c()(t[0]).format("YYYY-MM-DD"),end:c()(t[1]).format("YYYY-MM-DD")}):this.$store.commit("UPDATE_ACTIVITY_DATE_RANGE",{start:null,end:null})}}}),methods:{search:function(){this.$store.commit("CLEAR_ACTIVITY_RESULT"),this.$store.dispatch("getActivityList")}}},p=f,g=(n("ea41"),n("2877")),v=Object(g["a"])(p,o,s,!1,null,"33273597",null),m=v.exports,y=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.$router.push({name:"activity",params:{partnerName:"广州市华臣润滑油有限公司",clients:[],dsr:"",remark:"",date:""}})}}},[t._v("新增拜访计划")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.list.loading,expression:"list.loading"}],staticStyle:{width:"100%","margin-top":"15px","font-size":"12px"},attrs:{data:t.list.data,"row-key":"id",size:"small",border:"",lazy:"","empty-text":t.list.loading?t.list.loadingText:"没有数据"}},[t._v("\n    >\n    "),n("el-table-column",{attrs:{label:"经销商",align:"center",prop:"partnerName"}}),n("el-table-column",{attrs:{label:"DSR",align:"center",prop:"dsr"}}),n("el-table-column",{attrs:{label:"拜访日期",align:"center",prop:"date"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.dateformat(e.row.date)))]}}])}),n("el-table-column",{attrs:{label:"拜访客户","min-width":"120",align:"center",prop:"clients"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.clients.join(";")))]}}])}),n("el-table-column",{attrs:{label:"备注","min-width":"120",align:"center",prop:"remark"}}),n("el-table-column",{attrs:{label:"操作","min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-row",{staticStyle:{color:"green","font-weight":"bold"}},[n("router-link",{staticStyle:{"text-decoration":"none"},attrs:{to:{name:"activity",query:Object.assign({submited:1,view:0,id:e.row.id,index:e.$index},t.$route.query),params:e.row}}},[n("el-link",{staticStyle:{margin:"0 3px 0 4px"},attrs:{type:"primary",underline:!1}},[t._v("编辑")])],1),n("el-popconfirm-customize",{attrs:{title:"请确认删除?"},on:{confirm:function(n){return t.deleteActivity(e.$index)}}},[n("el-link",{staticStyle:{color:"red",margin:"0 4px 0 4px"},attrs:{type:"primary",underline:!1}},[t._v("删除")])],1),n("el-link",{staticStyle:{margin:"0 3px 0 4px"},attrs:{type:"primary",underline:!1},on:{click:function(n){return t.onSelect(e.row.dsr)}}},[t._v("轨迹详情")])],1)]}}])})],1)],1)},x=[],_=n("768b"),b=(n("96cf"),n("3b8d"));function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)}return n}function S(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(n,!0).forEach(function(e){Object(a["a"])(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(n).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var M={name:"activity-table",created:function(){var t=Object(b["a"])(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}},t)}));function e(){return t.apply(this,arguments)}return e}(),computed:S({},Object(l["c"])({list:function(t){return t.list.list}})),methods:{dateformat:function(t){return t?c()(t).format("YYYY-MM-DD"):""},deleteActivity:function(){var t=Object(b["a"])(regeneratorRuntime.mark(function t(e){return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:this.$store.commit("DELETE_CLIENT",e);case 1:case"end":return t.stop()}},t,this)}));function e(e){return t.apply(this,arguments)}return e}(),onRelease:function(){var t=Object(b["a"])(regeneratorRuntime.mark(function t(e){var n,i,r,a,o;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:return n={id:e.id,enableFlag:0==e.enableFlag?1:0},t.next=3,this.$store.dispatch("submitActivity",n);case 3:if(i=t.sent,r=Object(_["a"])(i,2),a=r[0],o=r[1],a){t.next=11;break}this.$notify.error({message:o.errorMsg}),t.next=14;break;case 11:return t.next=13,this.$store.dispatch("getActivityList");case 13:this.$notify.success({message:"更新成功"});case 14:case"end":return t.stop()}},t,this)}));function e(e){return t.apply(this,arguments)}return e}(),isEditable:function(t){return!0},isDeletable:function(t){return!0},onSelect:function(t){this.$store.commit("SELECT_TAB",{name:"map",dsrs:[t]})}}},T=M,C=Object(g["a"])(T,y,x,!1,null,null,null),I=C.exports,D=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.list.total?n("div",{staticClass:"text-center",staticStyle:{"padding-top":"30px"}},[n("el-pagination",{attrs:{background:"",layout:"prev, pager, next",total:t.list.total,"current-page":t.list.page},on:{"update:currentPage":function(e){return t.$set(t.list,"page",e)},"update:current-page":function(e){return t.$set(t.list,"page",e)},"current-change":t.change}})],1):t._e()},A=[],k={name:"activity-pagination",computed:Object(l["c"])({list:function(t){return t.list.list}}),methods:{change:function(){this.$store.dispatch("getActivityList",{})}}},O=k,P=Object(g["a"])(O,D,A,!1,null,null,null),L=P.exports,E=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{border:"1px solid rgb(237,237,237)",margin:"5px"}},[n("div",{staticStyle:{background:"rgb(8,47,109)",color:"white","font-size":"17px","text-align":"center",margin:"0 0 10px 0",padding:"5px 0 5px 0"}},[t._v("\n    月巡店数vs总客户数\n  ")]),n("el-form",{attrs:{"label-width":"50px"}},[n("el-row",[n("el-col",{attrs:{xs:16,sm:20,md:20,lg:20,xl:20}},[n("el-form-item",{staticStyle:{height:"30px"}})],1),n("el-col",{attrs:{xs:8,sm:4,md:4,lg:4,xl:4}},[n("el-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{type:"text"},on:{click:function(e){t.visible=!0}}},[t._v("查看详情")])],1)],1)],1),n("div",{ref:"chart",staticStyle:{height:"300px"}}),n("el-dialog",{attrs:{title:"月巡店数VS总客户数",visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[n("el-table",{attrs:{data:t.list.data,border:"",size:"mini"}},[n("el-table-column",{attrs:{property:"month",label:"月份",width:"150",sortable:""}}),n("el-table-column",{attrs:{property:"xd",label:"月巡店数",width:"200",sortable:""}}),n("el-table-column",{attrs:{property:"kh",label:"总客户数",sortable:""}})],1)],1)],1)},R=[],N=n("3eba"),B=n.n(N),z=(n("ef97"),n("94b1"),n("d28f"),n("007d"),function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=t.map(function(t){return t.xd}),n=t.map(function(t){return t.kh}),i=t.map(function(t){return t.month});return{legend:{left:15,data:["客户总数","巡店次数"]},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:i},yAxis:{type:"value"},series:[{name:"客户总数",data:n,type:"bar",barWidth:20,color:"rgb(0,104,173)"},{name:"巡店次数",data:e,type:"line",lineStyle:{width:3,color:"rgb(153,0,0)"}}]}}),F=n("b212");function V(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)}return n}function H(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?V(n,!0).forEach(function(e){Object(a["a"])(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):V(n).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var W={data:function(){return{loading:!1,chart:null,visible:!1,list:{data:[]}}},computed:H({},Object(l["c"])({activeName:function(t){return t.tab.activeName},params:function(t){return H({},t.plan.chart1,{},t.plan.params)}})),watch:{activeName:{handler:function(t){"list"==t&&this.refresh()}},params:{handler:function(){this.refresh()},deep:!0}},mounted:function(){var t=this;this.chart=B.a.init(this.$refs.chart),this.chart.setOption(z()),window.addEventListener("resize",function(){t.chart.resize()},!1)},created:function(){this.refresh()},methods:{refresh:function(){var t=Object(b["a"])(regeneratorRuntime.mark(function t(){var e,n,i,r,a;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,t.next=3,F["a"].getChart(this.params);case 3:if(e=t.sent,n=Object(_["a"])(e,2),i=n[0],r=n[1],this.loading=!1,i){t.next=11;break}return this.$notify.error({message:r.errorMsg}),t.abrupt("return");case 11:a=r.data.map(function(t){return H({},t,{xd:parseInt(t.xd)||0,kh:parseInt(t.kh)||0})}),this.list.data=a,this.chart.setOption(z(a)),this.chart.resize();case 15:case"end":return t.stop()}},t,this)}));function e(){return t.apply(this,arguments)}return e}()}},G=W,j=Object(g["a"])(G,E,R,!1,null,null,null),Y=j.exports,U=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{border:"1px solid rgb(237, 237, 237)",margin:"5px"}},[n("div",{staticStyle:{background:"rgb(8, 47, 109)",color:"white","font-size":"17px","text-align":"center",margin:"0 0 10px 0",padding:"5px 0 5px 0"}},[t._v("\n    巡店数vs销量\n  ")]),n("el-form",{attrs:{"label-width":"50px",inline:!0}},[n("el-form-item",{attrs:{label:"渠道："}},[n("el-select",{attrs:{multiple:"","collapse-tags":""},model:{value:t.options.Channel,callback:function(e){t.$set(t.options,"Channel",e)},expression:"options.Channel"}},t._l([{id:"Commercial",label:"商用油"},{id:"Consumer",label:"乘用车"},{id:"Other",label:"其它"}],function(t){return n("el-option",{key:t.id,attrs:{label:t.label,value:t.id}})}),1)],1),n("el-form-item",{attrs:{label:"月份："}},[n("el-select",{model:{value:t.options.Month,callback:function(e){t.$set(t.options,"Month",e)},expression:"options.Month"}},t._l(["01","02","03","04","05","06","07","09","10","11","12"],function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})}),1)],1),n("el-button",{staticStyle:{float:"right","margin-right":"10px"},attrs:{type:"text"},on:{click:function(e){t.visible=!0}}},[t._v("查看详情")])],1),n("div",{ref:"chart",staticStyle:{height:"300px"}}),n("el-dialog",{attrs:{title:"巡店数vs销量",visible:t.visible},on:{"update:visible":function(e){t.visible=e}}},[n("el-table",{attrs:{data:t.list.data,border:"",size:"mini"}},[n("el-table-column",{attrs:{property:"end_market_name_cn",label:"客户名称"}}),n("el-table-column",{attrs:{property:"times",label:"巡店数",width:"200",sortable:""}}),n("el-table-column",{attrs:{property:"sellin",label:"销量(L)",width:"200",sortable:""}})],1)],1)],1)},$=[],X=(n("15af"),n("75fc")),q=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=t.slice(0,10),n=e.map(function(t){return t.end_market_name_cn}),i=e.map(function(t){return t.sellin}),r=e.map(function(t){return t.times});return{legend:{left:15,data:["销量","巡店次数"],selectedMode:!1},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:n,axisLabel:{color:"#636364",align:"right",rotate:30}},yAxis:[{min:0,max:Math.max.apply(Math,Object(X["a"])(i))},{min:Math.min.apply(Math,Object(X["a"])(r)),max:Math.max.apply(Math,Object(X["a"])(r)),interval:5,show:!1}],series:[{name:"销量",data:i,type:"bar",barWidth:20,color:"rgb(0,104,173)",yAxisIndex:0},{name:"巡店次数",data:r,type:"scatter",yAxisIndex:1,color:"#000",label:{show:!0,position:"top",color:"#fff",backgroundColor:"rgb(229,68,0)",padding:[4,5,4,5]}}]}};function Z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)}return n}function K(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Z(n,!0).forEach(function(e){Object(a["a"])(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Z(n).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var J={data:function(){return{loading:!1,chart:null,visible:!1,list:{data:[]}}},computed:K({},Object(l["c"])({activeName:function(t){return t.tab.activeName},options:function(t){return t.plan.chart2},params:function(t){return K({},t.plan.chart2,{},t.plan.params)}})),watch:{activeName:{handler:function(t){"list"==t&&this.refresh()}},params:{handler:function(){this.refresh()},deep:!0}},mounted:function(){var t=this;this.chart=B.a.init(this.$refs.chart),this.chart.setOption(q()),window.addEventListener("resize",function(){t.chart.resize()},!1)},created:function(){this.refresh()},methods:{refresh:function(){var t=Object(b["a"])(regeneratorRuntime.mark(function t(){var e,n,i,r,a;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,t.next=3,F["a"].getChart(this.params);case 3:if(e=t.sent,n=Object(_["a"])(e,2),i=n[0],r=n[1],this.loading=!1,i){t.next=11;break}return this.$notify.error({message:r.errorMsg}),t.abrupt("return");case 11:a=r.data.map(function(t){return K({},t,{times:parseInt(t.times)||0,sellin:parseFloat(t.sellin)||0})}),this.list.data=a,this.chart.setOption(q(a)),this.chart.resize();case 15:case"end":return t.stop()}},t,this)}));function e(){return t.apply(this,arguments)}return e}()}},Q=J,tt=(n("cbdc"),Object(g["a"])(Q,U,$,!1,null,"6bccc5b8",null)),et=tt.exports;function nt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)}return n}function it(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nt(n,!0).forEach(function(e){Object(a["a"])(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nt(n).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var rt={name:"activity-list",components:{search:m,contentList:I,pagination:L,chart1:Y,chart2:et},data:function(){return{radio1:"乘用车",radio2:"金富力",radio3:[]}},computed:it({},Object(l["c"])({radio:function(t){return t.map.isRecordInterval?1:0},params:function(t){return t.plan.params}}))},at=rt,ot=(n("7f76"),Object(g["a"])(at,i,r,!1,null,"eaa97ed6",null));e["default"]=ot.exports},"6cb7":function(t,e,n){var i=n("6d8b"),r=n("4319"),a=n("8918"),o=n("625e"),s=o.enableClassManagement,l=o.parseClassType,u=n("e0d3"),c=u.makeInner,h=n("f934"),d=n("de1c"),f=c(),p=r.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){r.call(this,t,e,n,i),this.uid=a.getUID("ec_cpt_model")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,r=n?h.getLayoutParams(t):{},a=e.getTheme();i.merge(t,a.get(this.mainType)),i.merge(t,this.getDefaultOption()),n&&h.mergeLayoutParam(t,r,n)},mergeOption:function(t,e){i.merge(this.option,t,!0);var n=this.layoutMode;n&&h.mergeLayoutParam(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){var t=f(this);if(!t.defaultOption){var e=[],n=this.constructor;while(n){var r=n.prototype.defaultOption;r&&e.push(r),n=n.superClass}for(var a={},o=e.length-1;o>=0;o--)a=i.merge(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});function g(t){var e=[];return i.each(p.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=i.map(e,function(t){return l(t).main}),"dataset"!==t&&i.indexOf(e,"dataset")<=0&&e.unshift("dataset"),e}s(p,{registerWhenExtend:!0}),a.enableSubTypeDefaulter(p),a.enableTopologicalTravel(p,g),i.mixin(p,d);var v=p;t.exports=v},"6d8b":function(t,e){var n={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},i={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},r=Object.prototype.toString,a=Array.prototype,o=a.forEach,s=a.filter,l=a.slice,u=a.map,c=a.reduce,h={};function d(t,e){"createCanvas"===t&&(y=null),h[t]=e}function f(t){if(null==t||"object"!==typeof t)return t;var e=t,a=r.call(t);if("[object Array]"===a){if(!q(t)){e=[];for(var o=0,s=t.length;o<s;o++)e[o]=f(t[o])}}else if(i[a]){if(!q(t)){var l=t.constructor;if(t.constructor.from)e=l.from(t);else{e=new l(t.length);for(o=0,s=t.length;o<s;o++)e[o]=f(t[o])}}}else if(!n[a]&&!q(t)&&!z(t))for(var u in e={},t)t.hasOwnProperty(u)&&(e[u]=f(t[u]));return e}function p(t,e,n){if(!R(e)||!R(t))return n?f(e):t;for(var i in e)if(e.hasOwnProperty(i)){var r=t[i],a=e[i];!R(a)||!R(r)||P(a)||P(r)||z(a)||z(r)||N(a)||N(r)||q(a)||q(r)?!n&&i in t||(t[i]=f(e[i],!0)):p(r,a,n)}return t}function g(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=p(n,t[i],e);return n}function v(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function m(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}var y,x=function(){return h.createCanvas()};function _(){return y||(y=x().getContext("2d")),y}function b(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function w(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function S(t,e,n){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,m(t,e,n)}function M(t){if(t)return"string"!==typeof t&&"number"===typeof t.length}function T(t,e,n){if(t&&e)if(t.forEach&&t.forEach===o)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(n,t[a],a,t)}function C(t,e,n){if(t&&e){if(t.map&&t.map===u)return t.map(e,n);for(var i=[],r=0,a=t.length;r<a;r++)i.push(e.call(n,t[r],r,t));return i}}function I(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===c)return t.reduce(e,n,i);for(var r=0,a=t.length;r<a;r++)n=e.call(i,n,t[r],r,t);return n}}function D(t,e,n){if(t&&e){if(t.filter&&t.filter===s)return t.filter(e,n);for(var i=[],r=0,a=t.length;r<a;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function A(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function k(t,e){var n=l.call(arguments,2);return function(){return t.apply(e,n.concat(l.call(arguments)))}}function O(t){var e=l.call(arguments,1);return function(){return t.apply(this,e.concat(l.call(arguments)))}}function P(t){return"[object Array]"===r.call(t)}function L(t){return"function"===typeof t}function E(t){return"[object String]"===r.call(t)}function R(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function N(t){return!!n[r.call(t)]}function B(t){return!!i[r.call(t)]}function z(t){return"object"===typeof t&&"number"===typeof t.nodeType&&"object"===typeof t.ownerDocument}function F(t){return t!==t}function V(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function H(t,e){return null!=t?t:e}function W(t,e,n){return null!=t?t:null!=e?e:n}function G(){return Function.call.apply(l,arguments)}function j(t){if("number"===typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function Y(t,e){if(!t)throw new Error(e)}function U(t){return null==t?null:"function"===typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}h.createCanvas=function(){return document.createElement("canvas")};var $="__ec_primitive__";function X(t){t[$]=!0}function q(t){return t[$]}function Z(t){var e=P(t);this.data={};var n=this;function i(t,i){e?n.set(t,i):n.set(i,t)}t instanceof Z?t.each(i):t&&T(t,i)}function K(t){return new Z(t)}function J(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function Q(){}Z.prototype={constructor:Z,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var n in void 0!==e&&(t=k(t,e)),this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}},e.$override=d,e.clone=f,e.merge=p,e.mergeAll=g,e.extend=v,e.defaults=m,e.createCanvas=x,e.getContext=_,e.indexOf=b,e.inherits=w,e.mixin=S,e.isArrayLike=M,e.each=T,e.map=C,e.reduce=I,e.filter=D,e.find=A,e.bind=k,e.curry=O,e.isArray=P,e.isFunction=L,e.isString=E,e.isObject=R,e.isBuiltInObject=N,e.isTypedArray=B,e.isDom=z,e.eqNaN=F,e.retrieve=V,e.retrieve2=H,e.retrieve3=W,e.slice=G,e.normalizeCssArray=j,e.assert=Y,e.trim=U,e.setAsPrimitive=X,e.isPrimitive=q,e.createHashMap=K,e.concatArray=J,e.noop=Q},"71ad":function(t,e,n){var i=n("6d8b"),r={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},a={};a.categoryAxis=i.merge({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},r),a.valueAxis=i.merge({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},r),a.timeAxis=i.defaults({scale:!0,min:"dataMin",max:"dataMax"},a.valueAxis),a.logAxis=i.defaults({scale:!0,logBase:10},a.valueAxis);var o=a;t.exports=o},"74cb":function(t,e){var n={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-n.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*n.bounceIn(2*t):.5*n.bounceOut(2*t-1)+.5}},i=n;t.exports=i},"76a5":function(t,e,n){var i=n("19eb"),r=n("6d8b"),a=n("e86a"),o=n("a73c"),s=n("82eb"),l=s.ContextCachedBy,u=function(t){i.call(this,t)};u.prototype={constructor:u,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&o.normalizeTextStyle(n,!0),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),o.needDrawText(i,n)?(this.setTransform(t),o.renderText(this,t,i,n,null,e),this.restoreTransform(t)):t.__attrCachedBy=l.NONE},getBoundingRect:function(){var t=this.style;if(this.__dirty&&o.normalizeTextStyle(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var n=a.getBoundingRect(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,o.getStroke(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},r.inherits(u,i);var c=u;t.exports=c},7919:function(t,e,n){var i=n("f934"),r=i.getLayoutRect,a=i.box,o=i.positionElement,s=n("eda2"),l=n("2306");function u(t,e,n){var i=e.getBoxLayoutParams(),s=e.get("padding"),l={width:n.getWidth(),height:n.getHeight()},u=r(i,l,s);a(e.get("orient"),t,e.get("itemGap"),u.width,u.height),o(t,i,l,s)}function c(t,e){var n=s.normalizeCssArray(e.get("padding")),i=e.getItemStyle(["color","opacity"]);i.fill=e.get("backgroundColor");t=new l.Rect({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1});return t}e.layout=u,e.makeBackground=c},"7d6d":function(t,e){var n={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1};function i(t,e,i){return n.hasOwnProperty(e)?i*t.dpr:i}t.exports=i},"7e63":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.each,o=r.filter,s=r.map,l=r.isArray,u=r.indexOf,c=r.isObject,h=r.isString,d=r.createHashMap,f=r.assert,p=r.clone,g=r.merge,v=r.extend,m=r.mixin,y=n("e0d3"),x=n("4319"),_=n("6cb7"),b=n("8971"),w=n("e47b"),S=n("0f99"),M=S.resetSourceDefaulter,T="\0_ec_inner",C=x.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new x(n),this._optionManager=i},setOption:function(t,e){f(!(T in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):A.call(this,i),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=n.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o.length&&a(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){var e=this.option,n=this._componentsMap,i=[];function r(i,r){var o=y.normalizeToArray(t[i]),s=y.mappingToExists(n.get(i),o);y.makeIdAndName(s),a(s,function(t,e){var n=t.option;c(n)&&(t.keyInfo.mainType=i,t.keyInfo.subType=O(i,n,t.exist))});var l=k(n,r);e[i]=[],n.set(i,[]),a(s,function(t,r){var a=t.exist,o=t.option;if(f(c(o)||a,"Empty component definition"),o){var s=_.getClass(i,t.keyInfo.subType,!0);if(a&&a.constructor===s)a.name=t.keyInfo.name,a.mergeOption(o,this),a.optionUpdated(o,!1);else{var u=v({dependentModels:l,componentIndex:r},t.keyInfo);a=new s(o,this,this,u),v(a,u),a.init(o,this,this,u),a.optionUpdated(null,!0)}}else a.mergeOption({},this),a.optionUpdated({},!1);n.get(i)[r]=a,e[i][r]=a.option},this),"series"===i&&P(this,n.get("series"))}M(this),a(t,function(t,n){null!=t&&(_.hasClass(n)?n&&i.push(n):e[n]=null==e[n]?p(t):g(e[n],t,!0))}),_.topologicalTravel(i,_.getAllClassMainTypes(),r,this),this._seriesIndicesMap=d(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=p(this.option);return a(t,function(e,n){if(_.hasClass(n)){e=y.normalizeToArray(e);for(var i=e.length-1;i>=0;i--)y.isIdInner(e[i])&&e.splice(i,1);t[n]=e}}),delete t[T],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,a=t.name,c=this._componentsMap.get(e);if(!c||!c.length)return[];if(null!=i)l(i)||(i=[i]),n=o(s(i,function(t){return c[t]}),function(t){return!!t});else if(null!=r){var h=l(r);n=o(c,function(t){return h&&u(r,t.id)>=0||!h&&t.id===r})}else if(null!=a){var d=l(a);n=o(c,function(t){return d&&u(a,t.name)>=0||!d&&t.name===a})}else n=c.slice();return L(n,t)},findComponents:function(t){var e=t.query,n=t.mainType,i=a(e),r=i?this.queryComponents(i):this._componentsMap.get(n);return s(L(r,t));function a(t){var e=n+"Index",i=n+"Id",r=n+"Name";return!t||null==t[e]&&null==t[i]&&null==t[r]?null:{mainType:n,index:t[e],id:t[i],name:t[r]}}function s(e){return t.filter?o(e,t.filter):e}},eachComponent:function(t,e,n){var i=this._componentsMap;if("function"===typeof t)n=e,e=t,i.each(function(t,i){a(t,function(t,r){e.call(n,i,t,r)})});else if(h(t))a(i.get(t),e,n);else if(c(t)){var r=this.findComponents(t);a(r,e,n)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return o(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return o(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){E(this),a(this._seriesIndices,function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)},this)},eachRawSeries:function(t,e){a(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){E(this),a(this._seriesIndices,function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)},this)},eachRawSeriesByType:function(t,e,n){return a(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return E(this),null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){E(this);var n=o(this._componentsMap.get("series"),t,e);P(this,n)},restoreData:function(t){var e=this._componentsMap;P(this,e.get("series"));var n=[];e.each(function(t,e){n.push(e)}),_.topologicalTravel(n,_.getAllClassMainTypes(),function(n,i){a(e.get(n),function(e){("series"!==n||!I(e,t))&&e.restoreData()})})}});function I(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}function D(t,e){var n=t.color&&!t.colorLayer;a(e,function(e,i){"colorLayer"===i&&n||_.hasClass(i)||("object"===typeof e?t[i]=t[i]?g(t[i],e,!1):p(e):null==t[i]&&(t[i]=e))})}function A(t){t=t,this.option={},this.option[T]=1,this._componentsMap=d({series:[]}),this._seriesIndices,this._seriesIndicesMap,D(t,this._theme.option),g(t,b,!1),this.mergeOption(t)}function k(t,e){l(e)||(e=e?[e]:[]);var n={};return a(e,function(e){n[e]=(t.get(e)||[]).slice()}),n}function O(t,e,n){var i=e.type?e.type:n?n.subType:_.determineSubType(t,e);return i}function P(t,e){t._seriesIndicesMap=d(t._seriesIndices=s(e,function(t){return t.componentIndex})||[])}function L(t,e){return e.hasOwnProperty("subType")?o(t,function(t){return t.subType===e.subType}):t}function E(t){}m(C,w);var R=C;t.exports=R},"7f76":function(t,e,n){"use strict";var i=n("18b6"),r=n.n(i);r.a},"7f96":function(t,e,n){var i=n("6d8b"),r=i.isFunction;function a(t,e,n){return{seriesType:t,performRawSeries:!0,reset:function(t,i,a){var o=t.getData(),s=t.get("symbol"),l=t.get("symbolSize"),u=t.get("symbolKeepAspect"),c=r(s),h=r(l),d=c||h,f=!c&&s?s:e,p=h?null:l;if(o.setVisual({legendSymbol:n||f,symbol:f,symbolSize:p,symbolKeepAspect:u}),!i.isSeriesFiltered(t))return{dataEach:o.hasItemOption||d?g:null};function g(e,n){if(d){var i=t.getRawValue(n),r=t.getDataParams(n);c&&e.setItemVisual(n,"symbol",s(i,r)),h&&e.setItemVisual(n,"symbolSize",l(i,r))}if(e.hasItemOption){var a=e.getItemModel(n),o=a.getShallow("symbol",!0),u=a.getShallow("symbolSize",!0),f=a.getShallow("symbolKeepAspect",!0);null!=o&&e.setItemVisual(n,"symbol",o),null!=u&&e.setItemVisual(n,"symbolSize",u),null!=f&&e.setItemVisual(n,"symbolKeepAspect",f)}}}}}t.exports=a},"80f0":function(t,e){function n(t){return t}function i(t,e,i,r,a){this._old=t,this._new=e,this._oldKeyGetter=i||n,this._newKeyGetter=r||n,this.context=a}function r(t,e,n,i,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[i](t[a],a),s=e[o];null==s?(n.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}i.prototype={constructor:i,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,n={},i={},a=[],o=[];for(r(t,n,a,"_oldKeyGetter",this),r(e,i,o,"_newKeyGetter",this),c=0;c<t.length;c++){var s=a[c],l=i[s];if(null!=l){var u=l.length;u?(1===u&&(i[s]=null),l=l.shift()):i[s]=null,this._update&&this._update(l,c)}else this._remove&&this._remove(c)}for(var c=0;c<o.length;c++){s=o[c];if(i.hasOwnProperty(s)){l=i[s];if(null==l)continue;if(l.length){var h=0;for(u=l.length;h<u;h++)this._add&&this._add(l[h])}else this._add&&this._add(l)}}}};var a=i;t.exports=a},"82eb":function(t,e){var n={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},i=9;e.ContextCachedBy=n,e.WILL_BE_RESTORED=i},"82f9":function(t,e,n){var i=n("6d8b"),r=n("76a5");function a(t){this._zr=t.getZr(),this._show=!1,this._hideTimeout}a.prototype={constructor:a,_enterable:!0,update:function(){},show:function(t){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,n){this.el&&this._zr.remove(this.el);var i={},a=t,o="{marker",s="|}",l=a.indexOf(o);while(l>=0){var u=a.indexOf(s),c=a.substr(l+o.length,u-l-o.length);c.indexOf("sub")>-1?i["marker"+c]={textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[c],textOffset:[3,0]}:i["marker"+c]={textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[c]},a=a.substr(u+1),l=a.indexOf("{marker")}this.el=new r({style:{rich:i,text:t,textLineHeight:20,textBackgroundColor:n.get("backgroundColor"),textBorderRadius:n.get("borderRadius"),textFill:n.get("textStyle.color"),textPadding:n.get("padding")},z:n.get("z")}),this._zr.add(this.el);var h=this;this.el.on("mouseover",function(){h._enterable&&(clearTimeout(h._hideTimeout),h._show=!0),h._inContent=!0}),this.el.on("mouseout",function(){h._enterable&&h._show&&h.hideLater(h._hideDelay),h._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){this.el&&this.el.attr("position",[t,e])},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(i.bind(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var o=a;t.exports=o},"843e":function(t,e,n){var i=n("6d8b"),r=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];function a(t){i.each(r,function(e){this[e]=i.bind(t[e],t)},this)}var o=a;t.exports=o},"84ce":function(t,e,n){var i=n("6d8b"),r=i.each,a=i.map,o=n("3842"),s=o.linearMap,l=o.getPixelPrecision,u=o.round,c=n("e073"),h=c.createAxisTicks,d=c.createAxisLabels,f=c.calculateCategoryInterval,p=[0,1],g=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1};function v(t,e){var n=t[1]-t[0],i=e,r=n/i/2;t[0]+=r,t[1]-=r}function m(t,e,n,i){var a=e.length;if(t.onBand&&!n&&a){var o,s,l=t.getExtent();if(1===a)e[0].coord=l[0],o=e[1]={coord:l[0]};else{var c=e[a-1].tickValue-e[0].tickValue,h=(e[a-1].coord-e[0].coord)/c;r(e,function(t){t.coord-=h/2});var d=t.scale.getExtent();s=1+d[1]-e[a-1].tickValue,o={coord:e[a-1].coord+h*s},e.push(o)}var f=l[0]>l[1];p(e[0].coord,l[0])&&(i?e[0].coord=l[0]:e.shift()),i&&p(l[0],e[0].coord)&&e.unshift({coord:l[0]}),p(l[1],o.coord)&&(i?o.coord=l[1]:e.pop()),i&&p(o.coord,l[1])&&e.push({coord:l[1]})}function p(t,e){return t=u(t),e=u(e),f?t>e:t<e}}g.prototype={constructor:g,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return l(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&(n=n.slice(),v(n,i.count())),s(t,p,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&(n=n.slice(),v(n,i.count()));var r=s(t,n,p,e);return this.scale.scale(r)},pointToData:function(t,e){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),n=h(this,e),i=n.ticks,r=a(i,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),o=e.get("alignWithLabel");return m(this,r,o,t.clamp),r},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var n=this.scale.getMinorTicks(e),i=a(n,function(t){return a(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this);return i},getViewLabels:function(){return d(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return f(this)}};var y=g;t.exports=y},"84d5":function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("4319"),o=n("e0d3"),s=o.isNameSpecified,l=n("29a8"),u=l.legend.selector,c={all:{type:"all",title:r.clone(u.all)},inverse:{type:"inverse",title:r.clone(u.inverse)}},h=i.extendComponentModel({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){h.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var e=t.selector;!0===e&&(e=t.selector=["all","inverse"]),r.isArray(e)&&r.each(e,function(t,n){r.isString(t)&&(t={type:t}),e[n]=r.merge(t,c[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],n=[];t.eachRawSeries(function(i){var r,a=i.name;if(n.push(a),i.legendVisualProvider){var o=i.legendVisualProvider,l=o.getAllNames();t.isSeriesFiltered(i)||(n=n.concat(l)),l.length?e=e.concat(l):r=!0}else r=!0;r&&s(i)&&e.push(i.name)}),this._availableNames=n;var i=this.get("data")||e,o=r.map(i,function(t){return"string"!==typeof t&&"number"!==typeof t||(t={name:t}),new a(t,this,this.ecModel)},this);this._data=o},getData:function(){return this._data},select:function(t){var e=this.option.selected,n=this.get("selectedMode");if("single"===n){var i=this._data;r.each(i,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;r.each(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,e=this.option.selected;r.each(t,function(t){var n=t.get("name",!0);e.hasOwnProperty(n)||(e[n]=!0),e[n]=!e[n]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&r.indexOf(this._availableNames,t)>=0},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}}),d=h;t.exports=d},"84ec":function(t,e){var n=Math.log(2);function i(t,e,r,a,o,s){var l=a+"-"+o,u=t.length;if(s.hasOwnProperty(l))return s[l];if(1===e){var c=Math.round(Math.log((1<<u)-1&~o)/n);return t[r][c]}var h=a|1<<r,d=r+1;while(a&1<<d)d++;for(var f=0,p=0,g=0;p<u;p++){var v=1<<p;v&o||(f+=(g%2?-1:1)*t[r][p]*i(t,e-1,d,h,o|v,s),g++)}return s[l]=f,f}function r(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],r={},a=i(n,8,0,0,0,r);if(0!==a){for(var o=[],s=0;s<8;s++)for(var l=0;l<8;l++)null==o[l]&&(o[l]=0),o[l]+=((s+l)%2?-1:1)*i(n,7,0===s?1:0,1<<s,1<<l,r)/a*e[s];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}e.buildTransformer=r},"857d":function(t,e){var n=2*Math.PI;function i(t){return t%=n,t<0&&(t+=n),t}e.normalizeRadian=i},"862d":function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.each,o=i.isString,s=i.defaults,l=i.extend,u=i.isObject,c=i.clone,h=n("e0d3"),d=h.normalizeToArray,f=n("0f99"),p=f.guessOrdinal,g=f.BE_ORDINAL,v=n("ec6f"),m=n("2f45"),y=m.OTHER_DIMENSIONS,x=n("562e");function _(t,e,n){v.isInstance(e)||(e=v.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var i=(n.dimsDef||[]).slice(),h=r(),f=r(),m=[],_=b(e,t,i,n.dimCount),S=0;S<_;S++){var M=i[S]=l({},u(i[S])?i[S]:{name:i[S]}),T=M.name,C=m[S]=new x;null!=T&&null==h.get(T)&&(C.name=C.displayName=T,h.set(T,S)),null!=M.type&&(C.type=M.type),null!=M.displayName&&(C.displayName=M.displayName)}var I=n.encodeDef;!I&&n.encodeDefaulter&&(I=n.encodeDefaulter(e,_)),I=r(I),I.each(function(t,e){if(t=d(t).slice(),1===t.length&&!o(t[0])&&t[0]<0)I.set(e,!1);else{var n=I.set(e,[]);a(t,function(t,i){o(t)&&(t=h.get(t)),null!=t&&t<_&&(n[i]=t,A(m[t],e,i))})}});var D=0;function A(t,e,n){null!=y.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,f.set(e,!0))}a(t,function(t,e){var n,i,r;if(o(t))n=t,t={};else{n=t.name;var l=t.ordinalMeta;t.ordinalMeta=null,t=c(t),t.ordinalMeta=l,i=t.dimsDef,r=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var h=I.get(n);if(!1!==h){h=d(h);if(!h.length)for(var f=0;f<(i&&i.length||1);f++){while(D<m.length&&null!=m[D].coordDim)D++;D<m.length&&h.push(D++)}a(h,function(e,a){var o=m[e];if(A(s(o,t),n,a),null==o.name&&i){var l=i[a];!u(l)&&(l={name:l}),o.name=o.displayName=l.name,o.defaultTooltip=l.defaultTooltip}r&&s(o.otherDims,r)})}});var k=n.generateCoord,O=n.generateCoordCount,P=null!=O;O=k?O||1:0;for(var L=k||"value",E=0;E<_;E++){C=m[E]=m[E]||new x;var R=C.coordDim;null==R&&(C.coordDim=w(L,f,P),C.coordDimIndex=0,(!k||O<=0)&&(C.isExtraCoord=!0),O--),null==C.name&&(C.name=w(C.coordDim,h)),null!=C.type||p(e,E,C.name)!==g.Must&&(!C.isExtraCoord||null==C.otherDims.itemName&&null==C.otherDims.seriesName)||(C.type="ordinal")}return m}function b(t,e,n,i){var r=Math.max(t.dimensionsDetectCount||1,e.length,n.length,i||0);return a(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function w(t,e,n){if(n||null!=e.get(t)){var i=0;while(null!=e.get(t+i))i++;t+=i}return e.set(t,!0),t}var S=_;t.exports=S},8728:function(t,e){function n(t,e,n,i,r,a){if(a>e&&a>i||a<e&&a<i)return 0;if(i===e)return 0;var o=i<e?1:-1,s=(a-e)/(i-e);1!==s&&0!==s||(o=i<e?.5:-.5);var l=s*(n-t)+t;return l===r?1/0:l>r?o:0}t.exports=n},"87b1":function(t,e,n){var i=n("cbe5"),r=n("4fac"),a=i.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){r.buildPath(t,e,!0)}});t.exports=a},"87c3":function(t,e,n){var i=n("6d8b"),r=i.map,a=n("cccd"),o=n("ee1a"),s=o.isDimensionStacked;function l(t){return{seriesType:t,plan:a(),reset:function(t){var e=t.getData(),n=t.coordinateSystem,i=t.pipelineContext,a=i.large;if(n){var o=r(n.dimensions,function(t){return e.mapDimension(t)}).slice(0,2),l=o.length,u=e.getCalculationInfo("stackResultDimension");return s(e,o[0])&&(o[0]=u),s(e,o[1])&&(o[1]=u),l&&{progress:c}}function c(t,e){for(var i=t.end-t.start,r=a&&new Float32Array(i*l),s=t.start,u=0,c=[],h=[];s<t.end;s++){var d;if(1===l){var f=e.get(o[0],s);d=!isNaN(f)&&n.dataToPoint(f,null,h)}else{f=c[0]=e.get(o[0],s);var p=c[1]=e.get(o[1],s);d=!isNaN(f)&&!isNaN(p)&&n.dataToPoint(c,null,h)}a?(r[u++]=d?d[0]:NaN,r[u++]=d?d[1]:NaN):e.setItemLayout(s,d&&d.slice()||[NaN,NaN])}a&&e.setLayout("symbolPoints",r)}}}}t.exports=l},"88b3":function(t,e){var n="\0__throttleOriginMethod",i="\0__throttleRate",r="\0__throttleType";function a(t,e,n){var i,r,a,o,s,l=0,u=0,c=null;function h(){u=(new Date).getTime(),c=null,t.apply(a,o||[])}e=e||0;var d=function(){i=(new Date).getTime(),a=this,o=arguments;var t=s||e,d=s||n;s=null,r=i-(d?l:u)-t,clearTimeout(c),d?c=setTimeout(h,t):r>=0?h():c=setTimeout(h,-r),l=i};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){s=t},d}function o(t,e,o,s){var l=t[e];if(l){var u=l[n]||l,c=l[r],h=l[i];if(h!==o||c!==s){if(null==o||!s)return t[e]=u;l=t[e]=a(u,o,"debounce"===s),l[n]=u,l[r]=s,l[i]=o}return l}}function s(t,e){var i=t[e];i&&i[n]&&(t[e]=i[n])}e.throttle=a,e.createOrUpdate=o,e.clear=s},8918:function(t,e,n){var i=n("6d8b"),r=n("625e"),a=r.parseClassType,o=0;function s(t){return[t||"",o++,Math.random().toFixed(5)].join("_")}function l(t){var e={};return t.registerSubTypeDefaulter=function(t,n){t=a(t),e[t.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=a(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r},t}function u(t,e){function n(t){var n={},o=[];return i.each(t,function(s){var l=r(n,s),u=l.originalDeps=e(s),c=a(u,t);l.entryCount=c.length,0===l.entryCount&&o.push(s),i.each(c,function(t){i.indexOf(l.predecessor,t)<0&&l.predecessor.push(t);var e=r(n,t);i.indexOf(e.successor,t)<0&&e.successor.push(s)})}),{graph:n,noEntryList:o}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function a(t,e){var n=[];return i.each(t,function(t){i.indexOf(e,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,e,r,a){if(t.length){var o=n(e),s=o.graph,l=o.noEntryList,u={};i.each(t,function(t){u[t]=!0});while(l.length){var c=l.pop(),h=s[c],d=!!u[c];d&&(r.call(a,c,h.originalDeps.slice()),delete u[c]),i.each(h.successor,d?p:f)}i.each(u,function(){throw new Error("Circle dependency may exists")})}function f(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function p(t){u[t]=!0,f(t)}}}e.getUID=s,e.enableSubTypeDefaulter=l,e.enableTopologicalTravel=u},8971:function(t,e){var n="";"undefined"!==typeof navigator&&(n=navigator.platform||"");var i={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:n.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};t.exports=i},"897a":function(t,e,n){var i=n("22d1"),r=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];function a(t){return i.browser.ie&&i.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var a=0;a<n.length;a++){var o=n[a],s=o&&o.shape,l=o&&o.type;if(s&&("sector"===l&&s.startAngle===s.endAngle||"rect"===l&&(!s.width||!s.height))){for(var u=0;u<r.length;u++)r[u][2]=i[r[u][0]],i[r[u][0]]=r[u][1];e=!0;break}}if(t.apply(this,arguments),e)for(u=0;u<r.length;u++)i[r[u][0]]=r[u][2]}:t}t.exports=a},"89e3":function(t,e,n){var i=n("3842"),r=n("eda2"),a=n("e0d8"),o=n("944e"),s=i.round,l=a.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=o.getIntervalPrecision(t)},getTicks:function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,a=[];if(!e)return a;var o=1e4;n[0]<i[0]&&(t?a.push(s(i[0]-e)):a.push(n[0]));var l=i[0];while(l<=i[1]){if(a.push(l),l=s(l+e,r),l===a[a.length-1])break;if(a.length>o)return[]}var u=a.length?a[a.length-1]:i[1];return n[1]>u&&(t?a.push(u+e):a.push(n[1])),a},getMinorTicks:function(t){for(var e=this.getTicks(!0),n=[],r=this.getExtent(),a=1;a<e.length;a++){var o=e[a],s=e[a-1],l=0,u=[],c=o-s,h=c/t;while(l<t-1){var d=i.round(s+(l+1)*h);d>r[0]&&d<r[1]&&u.push(d),l++}n.push(u)}return n},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=i.getPrecisionSafe(t)||0:"auto"===n&&(n=this._intervalPrecision),t=s(t,n,!0),r.addCommas(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var a=o.intervalScaleNiceTicks(i,t,e,n);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax?e[0]-=n/2:(e[1]+=n/2,e[0]-=n/2)}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=s(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=s(Math.ceil(e[1]/r)*r))}});l.create=function(){return new l};var u=l;t.exports=u},"8b7f":function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.createHashMap,o=(r.retrieve,r.each);function s(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=a(),this.categoryAxisMap=a(),this.firstCategoryDimIndex=null}function l(t){var e=t.get("coordinateSystem"),n=new s(e),i=u[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}var u={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",a),c(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),c(a)&&(i.set("y",a),e.firstCategoryDimIndex,e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],n.set("single",r),c(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",a),n.set("angle",o),c(a)&&(i.set("radius",a),e.firstCategoryDimIndex=0),c(o)&&(i.set("angle",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),s=e.coordSysDims=a.dimensions.slice();o(a.parallelAxisIndex,function(t,a){var o=r.getComponent("parallelAxis",t),l=s[a];n.set(l,o),c(o)&&null==e.firstCategoryDimIndex&&(i.set(l,o),e.firstCategoryDimIndex=a)})}};function c(t){return"category"===t.get("type")}e.getCoordSysInfoBySeries=l},"8c2a":function(t,e,n){var i=n("6d8b"),r=n("e0d8"),a=n("3842"),o=n("89e3"),s=r.prototype,l=o.prototype,u=a.getPrecisionSafe,c=a.round,h=Math.floor,d=Math.ceil,f=Math.pow,p=Math.log,g=r.extend({type:"log",base:10,$constructor:function(){r.apply(this,arguments),this._originalScale=new o},getTicks:function(t){var e=this._originalScale,n=this._extent,r=e.getExtent();return i.map(l.getTicks.call(this,t),function(t){var i=a.round(f(this.base,t));return i=t===n[0]&&e.__fixMin?v(i,r[0]):i,i=t===n[1]&&e.__fixMax?v(i,r[1]):i,i},this)},getMinorTicks:l.getMinorTicks,getLabel:l.getLabel,scale:function(t){return t=s.scale.call(this,t),f(this.base,t)},setExtent:function(t,e){var n=this.base;t=p(t)/p(n),e=p(e)/p(n),l.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=s.getExtent.call(this);e[0]=f(t,e[0]),e[1]=f(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=v(e[0],i[0])),n.__fixMax&&(e[1]=v(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=p(t[0])/p(e),t[1]=p(t[1])/p(e),s.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=a.quantity(n),r=t/n*i;r<=.5&&(i*=10);while(!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0)i*=10;var o=[a.round(d(e[0]/i)*i),a.round(h(e[1]/i)*i)];this._interval=i,this._niceExtent=o}},niceExtent:function(t){l.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});function v(t,e){return c(t,u(e))}i.each(["contain","normalize"],function(t){g.prototype[t]=function(e){return e=p(e)/p(this.base),s[t].call(this,e)}}),g.create=function(){return new g};var m=g;t.exports=m},"8d32":function(t,e,n){var i=n("cbe5"),r=i.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),u=Math.sin(a);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,a,o,!s)}});t.exports=r},"8e43":function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.isObject,o=i.map;function s(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}s.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&o(n,c);return new s({categories:i,needCollect:!i,deduplication:!1!==e.dedplication})};var l=s.prototype;function u(t){return t._map||(t._map=r(t.categories))}function c(t){return a(t)&&null!=t.value?t.value:t+""}l.getOrdinal=function(t){return u(this).get(t)},l.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!==typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=u(this);return e=i.get(t),null==e&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e};var h=s;t.exports=h},"8ed2":function(t,e,n){n("48c7");var i=n("6cb7"),r=i.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});t.exports=r},"903c":function(t,e){function n(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0})}t.exports=n},"93d0":function(t,e){var n="original",i="arrayRows",r="objectRows",a="keyedColumns",o="unknown",s="typedArray",l="column",u="row";e.SOURCE_FORMAT_ORIGINAL=n,e.SOURCE_FORMAT_ARRAY_ROWS=i,e.SOURCE_FORMAT_OBJECT_ROWS=r,e.SOURCE_FORMAT_KEYED_COLUMNS=a,e.SOURCE_FORMAT_UNKNOWN=o,e.SOURCE_FORMAT_TYPED_ARRAY=s,e.SERIES_LAYOUT_BY_COLUMN=l,e.SERIES_LAYOUT_BY_ROW=u},"944e":function(t,e,n){var i=n("3842"),r=i.round;function a(t,e,n,a){var s={},u=t[1]-t[0],c=s.interval=i.nice(u/e,!0);null!=n&&c<n&&(c=s.interval=n),null!=a&&c>a&&(c=s.interval=a);var h=s.intervalPrecision=o(c),d=s.niceTickExtent=[r(Math.ceil(t[0]/c)*c,h),r(Math.floor(t[1]/c)*c,h)];return l(d,t),s}function o(t){return i.getPrecisionSafe(t)+2}function s(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function l(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),s(t,0,e),s(t,1,e),t[0]>t[1]&&(t[0]=t[1])}e.intervalScaleNiceTicks=a,e.getIntervalPrecision=o,e.fixExtent=l},"94b1":function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("9d57"),o=a.layout,s=a.largeLayout;n("5aa9"),n("17b8"),n("67cc"),n("01ed"),i.registerLayout(i.PRIORITY.VISUAL.LAYOUT,r.curry(o,"bar")),i.registerLayout(i.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,s),i.registerVisual({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}})},9680:function(t,e){function n(t,e,n,i,r,a,o){if(0===r)return!1;var s=r,l=0,u=t;if(o>e+s&&o>i+s||o<e-s&&o<i-s||a>t+s&&a>n+s||a<t-s&&a<n-s)return!1;if(t===n)return Math.abs(a-t)<=s/2;l=(e-i)/(t-n),u=(t*i-n*e)/(t-n);var c=l*a-o+u,h=c*c/(l*l+1);return h<=s/2*s/2}e.containStroke=n},9850:function(t,e,n){var i=n("401b"),r=n("1687"),a=i.applyTransform,o=Math.min,s=Math.max;function l(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}l.prototype={constructor:l,union:function(t){var e=o(t.x,this.x),n=o(t.y,this.y);this.width=s(t.x+t.width,this.x+this.width)-e,this.height=s(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,a(t,t,r),a(e,e,r),a(n,n,r),a(i,i,r),this.x=o(t[0],e[0],n[0],i[0]),this.y=o(t[1],e[1],n[1],i[1]);var l=s(t[0],e[0],n[0],i[0]),u=s(t[1],e[1],n[1],i[1]);this.width=l-this.x,this.height=u-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,a=r.create();return r.translate(a,a,[-e.x,-e.y]),r.scale(a,a,[n,i]),r.translate(a,a,[t.x,t.y]),a},intersect:function(t){if(!t)return!1;t instanceof l||(t=l.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,u=t.y,c=t.y+t.height;return!(i<o||s<n||a<u||c<r)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new l(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},l.create=function(t){return new l(t.x,t.y,t.width,t.height)};var u=l;t.exports=u},"98b7":function(t,e){var n="undefined"!==typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)};t.exports=n},"998c":function(t,e,n){var i=n("6d8b"),r=n("2306"),a=Math.PI;function o(t,e){e=e||{},i.defaults(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var n=new r.Rect({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),o=new r.Arc({shape:{startAngle:-a/2,endAngle:-a/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),s=new r.Rect({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});o.animateShape(!0).when(1e3,{endAngle:3*a/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*a/2}).delay(300).start("circularInOut");var l=new r.Group;return l.add(o),l.add(s),l.add(n),l.resize=function(){var e=t.getWidth()/2,i=t.getHeight()/2;o.setShape({cx:e,cy:i});var r=o.shape.r;s.setShape({x:e-r,y:i-r,width:2*r,height:2*r}),n.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},l.resize(),l}t.exports=o},"9bdb":function(t,e,n){var i=n("282b"),r=i([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),a={getAreaStyle:function(t,e){return r(this,t,e)}};t.exports=a},"9c2c":function(t,e,n){var i=n("401b"),r=i.min,a=i.max,o=i.scale,s=i.distance,l=i.add,u=i.clone,c=i.sub;function h(t,e,n,i){var h,d,f,p,g=[],v=[],m=[],y=[];if(i){f=[1/0,1/0],p=[-1/0,-1/0];for(var x=0,_=t.length;x<_;x++)r(f,f,t[x]),a(p,p,t[x]);r(f,f,i[0]),a(p,p,i[1])}for(x=0,_=t.length;x<_;x++){var b=t[x];if(n)h=t[x?x-1:_-1],d=t[(x+1)%_];else{if(0===x||x===_-1){g.push(u(t[x]));continue}h=t[x-1],d=t[x+1]}c(v,d,h),o(v,v,e);var w=s(b,h),S=s(b,d),M=w+S;0!==M&&(w/=M,S/=M),o(m,v,-w),o(y,v,S);var T=l([],b,m),C=l([],b,y);i&&(a(T,T,f),r(T,T,p),a(C,C,f),r(C,C,p)),g.push(T),g.push(C)}return n&&g.push(g.shift()),g}t.exports=h},"9cf9":function(t,e){var n=Math.round;function i(t,e,i){var r=i&&i.lineWidth;if(e&&r){var o=e.x1,s=e.x2,l=e.y1,u=e.y2;n(2*o)===n(2*s)?t.x1=t.x2=a(o,r,!0):(t.x1=o,t.x2=s),n(2*l)===n(2*u)?t.y1=t.y2=a(l,r,!0):(t.y1=l,t.y2=u)}}function r(t,e,n){var i=n&&n.lineWidth;if(e&&i){var r=e.x,o=e.y,s=e.width,l=e.height;t.x=a(r,i,!0),t.y=a(o,i,!0),t.width=Math.max(a(r+s,i,!1)-t.x,0===s?0:1),t.height=Math.max(a(o+l,i,!1)-t.y,0===l?0:1)}}function a(t,e,i){var r=n(2*t);return(r+n(e))%2===0?r/2:(r+(i?1:-1))/2}e.subPixelOptimizeLine=i,e.subPixelOptimizeRect=r,e.subPixelOptimize=a},"9d57":function(t,e,n){var i=n("6d8b"),r=n("3842"),a=r.parsePercent,o=n("ee1a"),s=o.isDimensionStacked,l=n("cccd"),u="__ec_stack_",c=.5,h="undefined"!==typeof Float32Array?Float32Array:Array;function d(t){return t.get("stack")||u+t.seriesIndex}function f(t){return t.dim+t.index}function p(t){var e=[],n=t.axis,r="axis0";if("category"===n.type){for(var a=n.getBandWidth(),o=0;o<t.count;o++)e.push(i.defaults({bandWidth:a,axisKey:r,stackId:u+o},t));var s=y(e),l=[];for(o=0;o<t.count;o++){var c=s[r][u+o];c.offsetCenter=c.offset+c.width/2,l.push(c)}return l}}function g(t,e){var n=[];return e.eachSeriesByType(t,function(t){w(t)&&!S(t)&&n.push(t)}),n}function v(t){var e={};i.each(t,function(t){var n=t.coordinateSystem,i=n.getBaseAxis();if("time"===i.type||"value"===i.type)for(var r=t.getData(),a=i.dim+"_"+i.index,o=r.mapDimension(i.dim),s=0,l=r.count();s<l;++s){var u=r.get(o,s);e[a]?e[a].push(u):e[a]=[u]}});var n=[];for(var r in e)if(e.hasOwnProperty(r)){var a=e[r];if(a){a.sort(function(t,e){return t-e});for(var o=null,s=1;s<a.length;++s){var l=a[s]-a[s-1];l>0&&(o=null===o?l:Math.min(o,l))}n[r]=o}}return n}function m(t){var e=v(t),n=[];return i.each(t,function(t){var i,r=t.coordinateSystem,o=r.getBaseAxis(),s=o.getExtent();if("category"===o.type)i=o.getBandWidth();else if("value"===o.type||"time"===o.type){var l=o.dim+"_"+o.index,u=e[l],c=Math.abs(s[1]-s[0]),h=o.scale.getExtent(),p=Math.abs(h[1]-h[0]);i=u?c/p*u:c}else{var g=t.getData();i=Math.abs(s[1]-s[0])/g.count()}var v=a(t.get("barWidth"),i),m=a(t.get("barMaxWidth"),i),y=a(t.get("barMinWidth")||1,i),x=t.get("barGap"),_=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:v,barMaxWidth:m,barMinWidth:y,barGap:x,barCategoryGap:_,axisKey:f(o),stackId:d(t)})}),y(n)}function y(t){var e={};i.each(t,function(t,n){var i=t.axisKey,r=t.bandWidth,a=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=a.stacks;e[i]=a;var s=t.stackId;o[s]||a.autoWidthCount++,o[s]=o[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!o[s].width&&(o[s].width=l,l=Math.min(a.remainedWidth,l),a.remainedWidth-=l);var u=t.barMaxWidth;u&&(o[s].maxWidth=u);var c=t.barMinWidth;c&&(o[s].minWidth=c);var h=t.barGap;null!=h&&(a.gap=h);var d=t.barCategoryGap;null!=d&&(a.categoryGap=d)});var n={};return i.each(e,function(t,e){n[e]={};var r=t.stacks,o=t.bandWidth,s=a(t.categoryGap,o),l=a(t.gap,1),u=t.remainedWidth,c=t.autoWidthCount,h=(u-s)/(c+(c-1)*l);h=Math.max(h,0),i.each(r,function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,u-=i+l*i,c--}else{var i=h;e&&e<i&&(i=Math.min(e,u)),n&&n>i&&(i=n),i!==h&&(t.width=i,u-=i+l*i,c--)}}),h=(u-s)/(c+(c-1)*l),h=Math.max(h,0);var d,f=0;i.each(r,function(t,e){t.width||(t.width=h),d=t,f+=t.width*(1+l)}),d&&(f-=d.width*l);var p=-f/2;i.each(r,function(t,i){n[e][i]=n[e][i]||{bandWidth:o,offset:p,width:t.width},p+=t.width*(1+l)})}),n}function x(t,e,n){if(t&&e){var i=t[f(e)];return null!=i&&null!=n&&(i=i[d(n)]),i}}function _(t,e){var n=g(t,e),r=m(n),a={},o={};i.each(n,function(t){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),l=d(t),u=r[f(i)][l],c=u.offset,h=u.width,p=n.getOtherAxis(i),g=t.get("barMinHeight")||0;a[l]=a[l]||[],o[l]=o[l]||[],e.setLayout({bandWidth:u.bandWidth,offset:c,size:h});for(var v=e.mapDimension(p.dim),m=e.mapDimension(i.dim),y=s(e,v),x=p.isHorizontal(),_=M(i,p,y),b=0,w=e.count();b<w;b++){var S=e.get(v,b),T=e.get(m,b);if(!isNaN(S)&&!isNaN(T)){var C,I,D,A,k=S>=0?"p":"n",O=_;if(y&&(a[l][T]||(a[l][T]={p:_,n:_}),O=a[l][T][k]),x){var P=n.dataToPoint([S,T]);C=O,I=P[1]+c,D=P[0]-_,A=h,Math.abs(D)<g&&(D=(D<0?-1:1)*g),y&&(a[l][T][k]+=D)}else{P=n.dataToPoint([T,S]);C=P[0]+c,I=O,D=h,A=P[1]-_,Math.abs(A)<g&&(A=(A<=0?-1:1)*g),y&&(a[l][T][k]+=A)}e.setItemLayout(b,{x:C,y:I,width:D,height:A})}}},this)}var b={seriesType:"bar",plan:l(),reset:function(t){if(w(t)&&S(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),a=e.mapDimension(r.dim),o=e.mapDimension(i.dim),s=r.isHorizontal(),l=s?0:1,u=x(m([t]),i,t).width;return u>c||(u=c),{progress:d}}function d(t,e){var c,d=t.count,f=new h(2*d),p=new h(d),g=[],v=[],m=0,y=0;while(null!=(c=t.next()))v[l]=e.get(a,c),v[1-l]=e.get(o,c),g=n.dataToPoint(v,null,g),f[m++]=g[0],f[m++]=g[1],p[y++]=c;e.setLayout({largePoints:f,largeDataIndices:p,barWidth:u,valueAxisStart:M(i,r,!1),valueAxisHorizontal:s})}}};function w(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function S(t){return t.pipelineContext&&t.pipelineContext.large}function M(t,e,n){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}e.getLayoutOnAxis=p,e.prepareLayoutBarSeries=g,e.makeColumnLayout=m,e.retrieveColumnLayout=x,e.layout=_,e.largeLayout=b},"9e2e":function(t,e,n){var i=n("a73c"),r=n("9850"),a=n("82eb"),o=a.WILL_BE_RESTORED,s=new r,l=function(){};l.prototype={constructor:l,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&i.normalizeTextStyle(n,!0);var r=n.text;if(null!=r&&(r+=""),i.needDrawText(r,n)){t.save();var a=this.transform;n.transformText?this.setTransform(t):a&&(s.copy(e),s.applyTransform(a),e=s),i.renderText(this,t,r,n,e,o),t.restore()}}};var u=l;t.exports=u},"9e47":function(t,e,n){var i=n("6d8b"),r=n("71ad"),a=n("6cb7"),o=n("f934"),s=o.getLayoutParams,l=o.mergeLayoutParam,u=n("8e43"),c=["value","category","time","log"];function h(t,e,n,o){i.each(c,function(a){e.extend({type:t+"Axis."+a,mergeDefaultAndTheme:function(e,r){var o=this.layoutMode,u=o?s(e):{},c=r.getTheme();i.merge(e,c.get(a+"Axis")),i.merge(e,this.getDefaultOption()),e.type=n(t,e),o&&l(e,u,o)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=u.createByAxisModel(this))},getCategories:function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:i.mergeAll([{},r[a+"Axis"],o],!0)})}),a.registerSubTypeDefaulter(t+"Axis",i.curry(n,t))}t.exports=h},"9f51":function(t,e,n){var i=n("857d"),r=i.normalizeRadian,a=2*Math.PI;function o(t,e,n,i,o,s,l,u,c){if(0===l)return!1;var h=l;u-=t,c-=e;var d=Math.sqrt(u*u+c*c);if(d-h>n||d+h<n)return!1;if(Math.abs(i-o)%a<1e-4)return!0;if(s){var f=i;i=r(o),o=r(f)}else i=r(i),o=r(o);i>o&&(o+=a);var p=Math.atan2(c,u);return p<0&&(p+=a),p>=i&&p<=o||p+a>=i&&p+a<=o}e.containStroke=o},"9f82":function(t,e,n){var i=n("ee1a"),r=i.isDimensionStacked,a=n("6d8b"),o=a.map;function s(t,e,n){var i,a=t.getBaseAxis(),s=t.getOtherAxis(a),u=l(s,n),c=a.dim,h=s.dim,d=e.mapDimension(h),f=e.mapDimension(c),p="x"===h||"radius"===h?1:0,g=o(t.dimensions,function(t){return e.mapDimension(t)}),v=e.getCalculationInfo("stackResultDimension");return(i|=r(e,g[0]))&&(g[0]=v),(i|=r(e,g[1]))&&(g[1]=v),{dataDimsForPoint:g,valueStart:u,valueAxisDim:h,baseAxisDim:c,stacked:!!i,valueDim:d,baseDim:f,baseDataOffset:p,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function l(t,e){var n=0,i=t.scale.getExtent();return"start"===e?n=i[0]:"end"===e?n=i[1]:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]),n}function u(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var a=t.baseDataOffset,o=[];return o[a]=n.get(t.baseDim,i),o[1-a]=r,e.dataToPoint(o)}e.prepareDataCoordInfo=s,e.getStackedOnPoint=u},a15a:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("9850"),o=n("e86a"),s=o.calculateTextPosition,l=r.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i+a),t.lineTo(n-r,i+a),t.closePath()}}),u=r.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i),t.lineTo(n,i+a),t.lineTo(n-r,i),t.closePath()}}),c=r.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=i-a+o+s,u=Math.asin(s/o),c=Math.cos(u)*o,h=Math.sin(u),d=Math.cos(u),f=.6*o,p=.7*o;t.moveTo(n-c,l+s),t.arc(n,l,o,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+c-h*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-c+h*f,l+s+d*f,n-c,l+s),t.closePath()}}),h=r.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,a=e.y,o=i/3*2;t.moveTo(r,a),t.lineTo(r+o,a+n),t.lineTo(r,a+n/4*3),t.lineTo(r-o,a+n),t.lineTo(r,a),t.closePath()}}),d={line:r.Line,rect:r.Rect,roundRect:r.Rect,square:r.Rect,circle:r.Circle,diamond:u,pin:c,arrow:h,triangle:l},f={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var a=Math.min(n,i);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},p={};i.each(d,function(t,e){p[e]=new t});var g=r.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=s(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=p[i];r||(i="rect",r=p[i]),f[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function v(t,e){if("image"!==this.type){var n=this.style,i=this.shape;i&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1)}}function m(t,e,n,i,o,s,l){var u,c=0===t.indexOf("empty");return c&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),u=0===t.indexOf("image://")?r.makeImage(t.slice(8),new a(e,n,i,o),l?"center":"cover"):0===t.indexOf("path://")?r.makePath(t.slice(7),{},new a(e,n,i,o),l?"center":"cover"):new g({shape:{symbolType:t,x:e,y:n,width:i,height:o}}),u.__isEmptyBrush=c,u.setColor=v,u.setColor(s),u}e.createSymbol=m},a73c:function(t,e,n){var i=n("6d8b"),r=i.retrieve2,a=i.retrieve3,o=i.each,s=i.normalizeCssArray,l=i.isString,u=i.isObject,c=n("e86a"),h=n("5693"),d=n("5e76"),f=n("7d6d"),p=n("82eb"),g=p.ContextCachedBy,v=p.WILL_BE_RESTORED,m=c.DEFAULT_FONT,y={left:1,right:1,center:1},x={top:1,bottom:1,middle:1},_=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],b={},w={};function S(t){return M(t),o(t.rich,M),t}function M(t){if(t){t.font=c.makeFont(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||y[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||x[n]?n:"top";var i=t.textPadding;i&&(t.textPadding=s(t.textPadding))}}function T(t,e,n,i,r,a){i.rich?I(t,e,n,i,r,a):C(t,e,n,i,r,a)}function C(t,e,n,i,r,a){"use strict";var o,s=O(i),l=!1,u=e.__attrCachedBy===g.PLAIN_TEXT;a!==v?(a&&(o=a.style,l=!s&&u&&o),e.__attrCachedBy=s?g.NONE:g.PLAIN_TEXT):u&&(e.__attrCachedBy=g.NONE);var h=i.font||m;l&&h===(o.font||m)||(e.font=h);var d=t.__computedFont;t.__styleFont!==h&&(t.__styleFont=h,d=t.__computedFont=e.font);var p=i.textPadding,y=i.textLineHeight,x=t.__textCotentBlock;x&&!t.__dirtyText||(x=t.__textCotentBlock=c.parsePlainText(n,d,p,y,i.truncate));var b=x.outerHeight,S=x.lines,M=x.lineHeight,T=E(w,t,i,r),C=T.baseX,I=T.baseY,D=T.textAlign||"left",k=T.textVerticalAlign;A(e,i,r,C,I);var L=c.adjustTextY(I,b,k),R=C,z=L;if(s||p){var V=c.getWidth(n,d),H=V;p&&(H+=p[1]+p[3]);var W=c.adjustTextX(C,H,D);s&&P(t,e,i,W,L,H,b),p&&(R=F(C,D,p),z+=p[0])}e.textAlign=D,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var G=0;G<_.length;G++){var j=_[G],Y=j[0],U=j[1],$=i[Y];l&&$===o[Y]||(e[U]=f(e,U,$||j[2]))}z+=M/2;var X=i.textStrokeWidth,q=l?o.textStrokeWidth:null,Z=!l||X!==q,K=!l||Z||i.textStroke!==o.textStroke,J=N(i.textStroke,X),Q=B(i.textFill);if(J&&(Z&&(e.lineWidth=X),K&&(e.strokeStyle=J)),Q&&(l&&i.textFill===o.textFill||(e.fillStyle=Q)),1===S.length)J&&e.strokeText(S[0],R,z),Q&&e.fillText(S[0],R,z);else for(G=0;G<S.length;G++)J&&e.strokeText(S[G],R,z),Q&&e.fillText(S[G],R,z),z+=M}function I(t,e,n,i,r,a){a!==v&&(e.__attrCachedBy=g.NONE);var o=t.__textCotentBlock;o&&!t.__dirtyText||(o=t.__textCotentBlock=c.parseRichText(n,i)),D(t,e,o,i,r)}function D(t,e,n,i,r){var a=n.width,o=n.outerWidth,s=n.outerHeight,l=i.textPadding,u=E(w,t,i,r),h=u.baseX,d=u.baseY,f=u.textAlign,p=u.textVerticalAlign;A(e,i,r,h,d);var g=c.adjustTextX(h,o,f),v=c.adjustTextY(d,s,p),m=g,y=v;l&&(m+=l[3],y+=l[0]);var x=m+a;O(i)&&P(t,e,i,g,v,o,s);for(var _=0;_<n.lines.length;_++){var b,S=n.lines[_],M=S.tokens,T=M.length,C=S.lineHeight,I=S.width,D=0,L=m,R=x,N=T-1;while(D<T&&(b=M[D],!b.textAlign||"left"===b.textAlign))k(t,e,b,i,C,y,L,"left"),I-=b.width,L+=b.width,D++;while(N>=0&&(b=M[N],"right"===b.textAlign))k(t,e,b,i,C,y,R,"right"),I-=b.width,R-=b.width,N--;L+=(a-(L-m)-(x-R)-I)/2;while(D<=N)b=M[D],k(t,e,b,i,C,y,L+b.width/2,"center"),L+=b.width,D++;y+=C}}function A(t,e,n,i,r){if(n&&e.textRotation){var a=e.textOrigin;"center"===a?(i=n.width/2+n.x,r=n.height/2+n.y):a&&(i=a[0]+n.x,r=a[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function k(t,e,n,i,o,s,l,u){var c=i.rich[n.styleName]||{};c.text=n.text;var h=n.textVerticalAlign,d=s+o/2;"top"===h?d=s+n.height/2:"bottom"===h&&(d=s+o-n.height/2),!n.isLineHolder&&O(c)&&P(t,e,c,"right"===u?l-n.width:"center"===u?l-n.width/2:l,d-n.height/2,n.width,n.height);var f=n.textPadding;f&&(l=F(l,u,f),d-=n.height/2-f[2]-n.textHeight/2),R(e,"shadowBlur",a(c.textShadowBlur,i.textShadowBlur,0)),R(e,"shadowColor",c.textShadowColor||i.textShadowColor||"transparent"),R(e,"shadowOffsetX",a(c.textShadowOffsetX,i.textShadowOffsetX,0)),R(e,"shadowOffsetY",a(c.textShadowOffsetY,i.textShadowOffsetY,0)),R(e,"textAlign",u),R(e,"textBaseline","middle"),R(e,"font",n.font||m);var p=N(c.textStroke||i.textStroke,v),g=B(c.textFill||i.textFill),v=r(c.textStrokeWidth,i.textStrokeWidth);p&&(R(e,"lineWidth",v),R(e,"strokeStyle",p),e.strokeText(n.text,l,d)),g&&(R(e,"fillStyle",g),e.fillText(n.text,l,d))}function O(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function P(t,e,n,i,r,a,o){var s=n.textBackgroundColor,c=n.textBorderWidth,f=n.textBorderColor,p=l(s);if(R(e,"shadowBlur",n.textBoxShadowBlur||0),R(e,"shadowColor",n.textBoxShadowColor||"transparent"),R(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),R(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),p||c&&f){e.beginPath();var g=n.textBorderRadius;g?h.buildPath(e,{x:i,y:r,width:a,height:o,r:g}):e.rect(i,r,a,o),e.closePath()}if(p)if(R(e,"fillStyle",s),null!=n.fillOpacity){var v=e.globalAlpha;e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=v}else e.fill();else if(u(s)){var m=s.image;m=d.createOrUpdateImage(m,null,t,L,s),m&&d.isImageReady(m)&&e.drawImage(m,i,r,a,o)}if(c&&f)if(R(e,"lineWidth",c),R(e,"strokeStyle",f),null!=n.strokeOpacity){v=e.globalAlpha;e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=v}else e.stroke()}function L(t,e){e.image=t}function E(t,e,n,i){var r=n.x||0,a=n.y||0,o=n.textAlign,s=n.textVerticalAlign;if(i){var l=n.textPosition;if(l instanceof Array)r=i.x+z(l[0],i.width),a=i.y+z(l[1],i.height);else{var u=e&&e.calculateTextPosition?e.calculateTextPosition(b,n,i):c.calculateTextPosition(b,n,i);r=u.x,a=u.y,o=o||u.textAlign,s=s||u.textVerticalAlign}var h=n.textOffset;h&&(r+=h[0],a+=h[1])}return t=t||{},t.baseX=r,t.baseY=a,t.textAlign=o,t.textVerticalAlign=s,t}function R(t,e,n){return t[e]=f(t,e,n),t[e]}function N(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function B(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function z(t,e){return"string"===typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function F(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function V(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}e.normalizeTextStyle=S,e.renderText=T,e.getBoxPosition=E,e.getStroke=N,e.getFill=B,e.parsePercent=z,e.needDrawText=V},a7fd:function(t,e,n){},a96b:function(t,e,n){var i=n("3eba"),r=i.extendComponentModel({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});t.exports=r},abff:function(t,e,n){var i=n("3eba"),r=n("f706"),a=n("c965"),o=n("87c3");i.extendChartView({type:"scatter",render:function(t,e,n){var i=t.getData(),r=this._updateSymbolDraw(i,t);r.updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},incrementalPrepareRender:function(t,e,n){var i=t.getData(),r=this._updateSymbolDraw(i,t);r.incrementalPrepareUpdate(i),this._finished=!1},incrementalRender:function(t,e,n){this._symbolDraw.incrementalUpdate(t,e.getData(),{clipShape:this._getClipShape(e)}),this._finished=t.end===e.getData().count()},updateTransform:function(t,e,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4||!this._symbolDraw.isPersistent())return{update:!0};var r=o().reset(t);r.progress&&r.progress({start:0,end:i.count()},i),this._symbolDraw.updateLayout(i)},_getClipShape:function(t){var e=t.coordinateSystem,n=e&&e.getArea&&e.getArea();return t.get("clip",!0)?n:null},_updateSymbolDraw:function(t,e){var n=this._symbolDraw,i=e.pipelineContext,o=i.large;return n&&o===this._isLargeDraw||(n&&n.remove(),n=this._symbolDraw=o?new a:new r,this._isLargeDraw=o,this.group.removeAll()),this.group.add(n.group),n},remove:function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},dispose:function(){}})},ac0f:function(t,e,n){var i=n("cbe5"),r=n("401b"),a=n("4a3f"),o=a.quadraticSubdivide,s=a.cubicSubdivide,l=a.quadraticAt,u=a.cubicAt,c=a.quadraticDerivativeAt,h=a.cubicDerivativeAt,d=[];function f(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?h:u)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?h:u)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?c:l)(t.x1,t.cpx1,t.x2,e),(n?c:l)(t.y1,t.cpy1,t.y2,e)]}var p=i.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,a=e.y2,l=e.cpx1,u=e.cpy1,c=e.cpx2,h=e.cpy2,f=e.percent;0!==f&&(t.moveTo(n,i),null==c||null==h?(f<1&&(o(n,l,r,f,d),l=d[1],r=d[2],o(i,u,a,f,d),u=d[1],a=d[2]),t.quadraticCurveTo(l,u,r,a)):(f<1&&(s(n,l,c,r,f,d),l=d[1],c=d[2],r=d[3],s(i,u,h,a,f,d),u=d[1],h=d[2],a=d[3]),t.bezierCurveTo(l,u,c,h,r,a)))},pointAt:function(t){return f(this.shape,t,!1)},tangentAt:function(t){var e=f(this.shape,t,!0);return r.normalize(e,e)}});t.exports=p},ae69:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=.5522848,i=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*n,l=o*n;t.moveTo(i-a,r),t.bezierCurveTo(i-a,r-l,i-s,r-o,i,r-o),t.bezierCurveTo(i+s,r-o,i+a,r-l,i+a,r),t.bezierCurveTo(i+a,r+l,i+s,r+o,i,r+o),t.bezierCurveTo(i-s,r+o,i-a,r+l,i-a,r),t.closePath()}});t.exports=r},af24:function(t,e,n){n("48c7"),n("f273")},afa0:function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=n("e1fc"),o=n("04f6");function s(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var l=function(){this._roots=[],this._displayList=[],this._displayListLen=0};l.prototype={constructor:l,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,a=e.length;i<a;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,r.canvasSupported&&o(n,s)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];var r=i,a=t;while(r)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof a&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof a&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array){e=0;for(var r=t.length;e<r;e++)this.delRoot(t[e])}else{var o=i.indexOf(this._roots,t);o>=0&&(this.delFromStorage(t),this._roots.splice(o,1),t instanceof a&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:s};var u=l;t.exports=u},b0af:function(t,e,n){var i=n("2306"),r=n("3842"),a=r.round;function o(t,e,n){var r=t.getArea(),a=t.getBaseAxis().isHorizontal(),o=r.x,s=r.y,l=r.width,u=r.height,c=n.get("lineStyle.width")||2;o-=c/2,s-=c/2,l+=c,u+=c;var h=new i.Rect({shape:{x:o,y:s,width:l,height:u}});return e&&(h.shape[a?"width":"height"]=0,i.initProps(h,{shape:{width:l,height:u}},n)),h}function s(t,e,n){var r=t.getArea(),o=new i.Sector({shape:{cx:a(t.cx,1),cy:a(t.cy,1),r0:a(r.r0,1),r:a(r.r,1),startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}});return e&&(o.shape.endAngle=r.startAngle,i.initProps(o,{shape:{endAngle:r.endAngle}},n)),o}function l(t,e,n){return t?"polar"===t.type?s(t,e,n):"cartesian2d"===t.type?o(t,e,n):null:null}e.createGridClipPath=o,e.createPolarClipPath=s,e.createClipPath=l},b12f:function(t,e,n){var i=n("e1fc"),r=n("8918"),a=n("625e"),o=function(){this.group=new i,this.uid=r.getUID("viewComponent")};o.prototype={constructor:o,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){},filterForExposedEvent:null};var s=o.prototype;s.updateView=s.updateLayout=s.updateVisual=function(t,e,n,i){},a.enableClassExtend(o),a.enableClassManagement(o,{registerWhenExtend:!0});var l=o;t.exports=l},b1d4:function(t,e,n){var i=n("862d");function r(t,e){return e=e||{},i(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})}t.exports=r},b212:function(t,e,n){"use strict";n("8e6e"),n("ac6a"),n("456d");var i=n("bd86"),r=n("768b"),a=(n("96cf"),n("3b8d")),o=n("d225"),s=n("b0b4"),l=n("4d20");function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(n,!0).forEach(function(e){Object(i["a"])(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(n).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var h=function(){function t(){Object(o["a"])(this,t)}return Object(s["a"])(t,[{key:"getChart",value:function(){var t=Object(a["a"])(regeneratorRuntime.mark(function t(){var e,n,i,a,o,s=arguments;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:return e=s.length>0&&void 0!==s[0]?s[0]:{},t.next=3,Object(l["a"])({method:"post",path:"reportview/report/data.do",contentType:"form",params:c({},e,{BusinessProperty:e.BusinessProperty.join(","),CustomerStatus:e.CustomerStatus.join(","),subType:e.subType.join(","),Channel:e.Channel&&e.Channel.join(",")})});case 3:return n=t.sent,i=Object(r["a"])(n,2),a=i[0],o=i[1],t.abrupt("return",[a,o]);case 8:case"end":return t.stop()}},t)}));function e(){return t.apply(this,arguments)}return e}()}]),t}();e["a"]=new h},b5c7:function(t,e,n){var i=n("282b"),r=i([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),a={getBarItemStyle:function(t){var e=r(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}};t.exports=a},b719:function(t,e,n){var i=n("697e7");e.zrender=i;var r=n("1687");e.matrix=r;var a=n("401b");e.vector=a;var o=n("6d8b"),s=n("41ef");e.color=s;var l=n("2306"),u=n("3842");e.number=u;var c=n("eda2");e.format=c;var h=n("88b3");h.throttle;e.throttle=h.throttle;var d=n("1548");e.helper=d;var f=n("bda7");e.parseGeoJSON=f;var p=n("6179");e.List=p;var g=n("4319");e.Model=g;var v=n("84ce");e.Axis=v;var m=n("22d1");e.env=m;var y=f,x={};o.each(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){x[t]=o[t]});var _={};o.each(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){_[t]=l[t]}),e.parseGeoJson=y,e.util=x,e.graphic=_},b809:function(t,e,n){var i=n("6d8b"),r=n("29a8"),a=n("2b17"),o=a.retrieveRawValue;function s(t,e){var n=e.getModel("aria");if(n.get("show"))if(n.get("description"))t.setAttribute("aria-label",n.get("description"));else{var a=0;e.eachSeries(function(t,e){++a},this);var s,l=n.get("data.maxCount")||10,u=n.get("series.maxCount")||10,c=Math.min(a,u);if(!(a<1)){var h=v();s=h?p(g("general.withTitle"),{title:h}):g("general.withoutTitle");var d=[],f=a>1?"series.multiple.prefix":"series.single.prefix";s+=p(g(f),{seriesCount:a}),e.eachSeries(function(t,e){if(e<c){var n,i=t.get("name"),r="series."+(a>1?"multiple":"single")+".";n=g(i?r+"withName":r+"withoutName"),n=p(n,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:m(t.subType)});var s=t.getData();window.data=s,s.count()>l?n+=p(g("data.partialData"),{displayCnt:l}):n+=g("data.allData");for(var u=[],h=0;h<s.count();h++)if(h<l){var f=s.getName(h),v=o(s,h);u.push(p(g(f?"data.withName":"data.withoutName"),{name:f,value:v}))}n+=u.join(g("data.separator.middle"))+g("data.separator.end"),d.push(n)}}),s+=d.join(g("series.multiple.separator.middle"))+g("series.multiple.separator.end"),t.setAttribute("aria-label",s)}}function p(t,e){if("string"!==typeof t)return t;var n=t;return i.each(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n}function g(t){var e=n.get(t);if(null==e){for(var i=t.split("."),a=r.aria,o=0;o<i.length;++o)a=a[i[o]];return a}return e}function v(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function m(t){return r.series.typeNames[t]||"自定义图"}}t.exports=s},bd6b:function(t,e,n){var i=n("06ad"),r=n("4942"),a=n("6d8b"),o=a.isString,s=a.isFunction,l=a.isObject,u=a.isArrayLike,c=a.indexOf,h=function(){this.animators=[]};function d(t,e,n,i,r,a,l,u){o(i)?(a=r,r=i,i=0):s(r)?(a=r,r="linear",i=0):s(i)?(a=i,i=0):s(n)?(a=n,n=500):n||(n=500),t.stopAnimation(),f(t,"",t,e,n,i,u);var c=t.animators.slice(),h=c.length;function d(){h--,h||a&&a()}h||a&&a();for(var p=0;p<c.length;p++)c[p].done(d).start(r,l)}function f(t,e,n,i,r,a,o){var s={},c=0;for(var h in i)i.hasOwnProperty(h)&&(null!=n[h]?l(i[h])&&!u(i[h])?f(t,e?e+"."+h:h,n[h],i[h],r,a,o):(o?(s[h]=n[h],p(t,e,h,i[h])):s[h]=i[h],c++):null==i[h]||o||p(t,e,h,i[h]));c>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function p(t,e,n,i){if(e){var r={};r[e]={},r[e][n]=i,t.attr(r)}else t.attr(n,i)}h.prototype={constructor:h,animate:function(t,e){var n,a=!1,o=this,s=this.__zr;if(t){var l=t.split("."),u=o;a="shape"===l[0];for(var h=0,d=l.length;h<d;h++)u&&(u=u[l[h]]);u&&(n=u)}else n=o;if(n){var f=o.animators,p=new i(n,e);return p.during(function(t){o.dirty(a)}).done(function(){f.splice(c(f,p),1)}),f.push(p),s&&s.animation.addAnimator(p),p}r('Property "'+t+'" is not existed in element '+o.id)},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,a){d(this,t,e,n,i,r,a)},animateFrom:function(t,e,n,i,r,a){d(this,t,e,n,i,r,a,!0)}};var g=h;t.exports=g},bda7:function(t,e,n){var i=n("6d8b"),r=n("f279");function a(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i],a=r.geometry,s=a.coordinates,l=a.encodeOffsets,u=0;u<s.length;u++){var c=s[u];if("Polygon"===a.type)s[u]=o(c,l[u],e);else if("MultiPolygon"===a.type)for(var h=0;h<c.length;h++){var d=c[h];c[h]=o(d,l[u][h],e)}}return t.UTF8Encoding=!1,t}function o(t,e,n){for(var i=[],r=e[0],a=e[1],o=0;o<t.length;o+=2){var s=t.charCodeAt(o)-64,l=t.charCodeAt(o+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=a,r=s,a=l,i.push([s/n,l/n])}return i}function s(t){return a(t),i.map(i.filter(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var e=t.properties,n=t.geometry,a=n.coordinates,o=[];"Polygon"===n.type&&o.push({type:"polygon",exterior:a[0],interiors:a.slice(1)}),"MultiPolygon"===n.type&&i.each(a,function(t){t[0]&&o.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var s=new r(e.name,o,e.cp);return s.properties=e,s})}t.exports=s},c2be:function(t,e,n){var i=n("2306"),r=i.extendShape,a=r({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=.5*(a-r),s=r+o,l=e.startAngle,u=e.endAngle,c=e.clockwise,h=Math.cos(l),d=Math.sin(l),f=Math.cos(u),p=Math.sin(u),g=c?u-l<2*Math.PI:l-u<2*Math.PI;g&&(t.moveTo(h*r+n,d*r+i),t.arc(h*s+n,d*s+i,o,-Math.PI+l,l,!c)),t.arc(n,i,a,l,u,!c),t.moveTo(f*a+n,p*a+i),t.arc(f*s+n,p*s+i,o,u-2*Math.PI,u-Math.PI,!c),0!==r&&(t.arc(n,i,r,u,l,c),t.moveTo(h*r+n,p*r+i)),t.closePath()}});t.exports=a},c533:function(t,e){var n=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],i={color:n,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],n]};t.exports=i},c775:function(t,e,n){var i=n("2b17"),r=i.retrieveRawValue;function a(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return r(t,e,n[0]);if(i){for(var a=[],o=0;o<n.length;o++){var s=r(t,e,n[o]);a.push(s)}return a.join(" ")}}e.getDefaultLabel=a},c7a2:function(t,e,n){var i=n("cbe5"),r=n("5693"),a=n("9cf9"),o=a.subPixelOptimizeRect,s={},l=i.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,a,l;this.subPixelOptimize?(o(s,e,this.style),n=s.x,i=s.y,a=s.width,l=s.height,s.r=e.r,e=s):(n=e.x,i=e.y,a=e.width,l=e.height),e.r?r.buildPath(t,e):t.rect(n,i,a,l),t.closePath()}});t.exports=l},c965:function(t,e,n){var i=n("2306"),r=n("a15a"),a=r.createSymbol,o=n("392f"),s=4,l=i.extendShape({shape:{points:null},symbolProxy:null,softClipShape:null,buildPath:function(t,e){var n=e.points,i=e.size,r=this.symbolProxy,a=r.shape,o=t.getContext?t.getContext():t,l=o&&i[0]<s;if(!l)for(var u=0;u<n.length;){var c=n[u++],h=n[u++];isNaN(c)||isNaN(h)||(this.softClipShape&&!this.softClipShape.contain(c,h)||(a.x=c-i[0]/2,a.y=h-i[1]/2,a.width=i[0],a.height=i[1],r.buildPath(t,a,!0)))}},afterBrush:function(t){var e=this.shape,n=e.points,i=e.size,r=i[0]<s;if(r){this.setTransform(t);for(var a=0;a<n.length;){var o=n[a++],l=n[a++];isNaN(o)||isNaN(l)||(this.softClipShape&&!this.softClipShape.contain(o,l)||t.fillRect(o-i[0]/2,l-i[1]/2,i[0],i[1]))}this.restoreTransform(t)}},findDataIndex:function(t,e){for(var n=this.shape,i=n.points,r=n.size,a=Math.max(r[0],4),o=Math.max(r[1],4),s=i.length/2-1;s>=0;s--){var l=2*s,u=i[l]-a/2,c=i[l+1]-o/2;if(t>=u&&e>=c&&t<=u+a&&e<=c+o)return s}return-1}});function u(){this.group=new i.Group}var c=u.prototype;c.isPersistent=function(){return!this._incremental},c.updateData=function(t,e){this.group.removeAll();var n=new l({rectHover:!0,cursor:"default"});n.setShape({points:t.getLayout("symbolPoints")}),this._setCommon(n,t,!1,e),this.group.add(n),this._incremental=null},c.updateLayout=function(t){if(!this._incremental){var e=t.getLayout("symbolPoints");this.group.eachChild(function(t){if(null!=t.startIndex){var n=2*(t.endIndex-t.startIndex),i=4*t.startIndex*2;e=new Float32Array(e.buffer,i,n)}t.setShape("points",e)})}},c.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clearIncremental(),t.count()>2e6?(this._incremental||(this._incremental=new o({silent:!0})),this.group.add(this._incremental)):this._incremental=null},c.incrementalUpdate=function(t,e,n){var i;this._incremental?(i=new l,this._incremental.addDisplayable(i,!0)):(i=new l({rectHover:!0,cursor:"default",startIndex:t.start,endIndex:t.end}),i.incremental=!0,this.group.add(i)),i.setShape({points:e.getLayout("symbolPoints")}),this._setCommon(i,e,!!this._incremental,n)},c._setCommon=function(t,e,n,i){var r=e.hostModel;i=i||{};var o=e.getVisual("symbolSize");t.setShape("size",o instanceof Array?o:[o,o]),t.softClipShape=i.clipShape||null,t.symbolProxy=a(e.getVisual("symbol"),0,0,0,0),t.setColor=t.symbolProxy.setColor;var l=t.shape.size[0]<s;t.useStyle(r.getModel("itemStyle").getItemStyle(l?["color","shadowBlur","shadowColor"]:["color"]));var u=e.getVisual("color");u&&t.setColor(u),n||(t.seriesIndex=r.seriesIndex,t.on("mousemove",function(e){t.dataIndex=null;var n=t.findDataIndex(e.offsetX,e.offsetY);n>=0&&(t.dataIndex=n+(t.startIndex||0))}))},c.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},c._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()};var h=u;t.exports=h},ca98:function(t,e,n){var i=n("6d8b"),r=n("e0d3"),a=n("6cb7"),o=i.each,s=i.clone,l=i.map,u=i.merge,c=/^(min|max)?(.+)$/;function h(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function d(t,e,n){var r,a,s=[],l=[],u=t.timeline;if(t.baseOption&&(a=t.baseOption),(u||t.options)&&(a=a||{},s=(t.options||[]).slice()),t.media){a=a||{};var c=t.media;o(c,function(t){t&&t.option&&(t.query?l.push(t):r||(r=t))})}return a||(a=t),a.timeline||(a.timeline=u),o([a].concat(s).concat(i.map(l,function(t){return t.option})),function(t){o(e,function(e){e(t,n)})}),{baseOption:a,timelineOptions:s,mediaDefault:r,mediaList:l}}function f(t,e,n){var r={width:e,height:n,aspectratio:e/n},a=!0;return i.each(t,function(t,e){var n=e.match(c);if(n&&n[1]&&n[2]){var i=n[1],o=n[2].toLowerCase();p(r[o],t,i)||(a=!1)}}),a}function p(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}function g(t,e){return t.join(",")===e.join(",")}function v(t,e){e=e||{},o(e,function(e,n){if(null!=e){var i=t[n];if(a.hasClass(n)){e=r.normalizeToArray(e),i=r.normalizeToArray(i);var o=r.mappingToExists(i,e);t[n]=l(o,function(t){return t.option&&t.exist?u(t.exist,t.option,!0):t.exist||t.option})}else t[n]=u(i,e,!0)}})}h.prototype={constructor:h,setOption:function(t,e){t&&i.each(r.normalizeToArray(t.series),function(t){t&&t.data&&i.isTypedArray(t.data)&&i.setAsPrimitive(t.data)}),t=s(t);var n=this._optionBackup,a=d.call(this,t,e,!n);this._newBaseOption=a.baseOption,n?(v(n.baseOption,a.baseOption),a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=l(e.timelineOptions,s),this._mediaList=l(e.mediaList,s),this._mediaDefault=s(e.mediaDefault),this._currentMediaIndices=[],s(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=s(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,a=[],o=[];if(!i.length&&!r)return o;for(var u=0,c=i.length;u<c;u++)f(i[u].query,e,n)&&a.push(u);return!a.length&&r&&(a=[-1]),a.length&&!g(a,this._currentMediaIndices)&&(o=l(a,function(t){return s(-1===t?r.option:i[t].option)})),this._currentMediaIndices=a,o}};var m=h;t.exports=m},cb11:function(t,e,n){var i=n("cbe5"),r=n("9cf9"),a=r.subPixelOptimizeLine,o={},s=i.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,r,s;this.subPixelOptimize?(a(o,e,this.style),n=o.x1,i=o.y1,r=o.x2,s=o.y2):(n=e.x1,i=e.y1,r=e.x2,s=e.y2);var l=e.percent;0!==l&&(t.moveTo(n,i),l<1&&(r=n*(1-l)+r*l,s=i*(1-l)+s*l),t.lineTo(r,s))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}});t.exports=s},cb69:function(t,e,n){var i=n("3301"),r=n("4f85"),a=r.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(t,e){return i(this.getSource(),this,{useEncodeDefaulter:!0})},brushSelector:"point",getProgressive:function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},getProgressiveThreshold:function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},clip:!0}});t.exports=a},cb6d:function(t,e){function n(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function i(t,e){return{target:t,topTarget:e&&e.topTarget}}n.prototype={constructor:n,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(i(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,r=t.offsetY,a=n-this._x,o=r-this._y;this._x=n,this._y=r,e.drift(a,o,t),this.dispatchToElement(i(e,t),"drag",t.event);var s=this.findHover(n,r,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.dispatchToElement(i(l,t),"dragleave",t.event),s&&s!==l&&this.dispatchToElement(i(s,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(i(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(i(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var r=n;t.exports=r},cb8f:function(t,e,n){var i=n("3eba"),r=n("6d8b"),a=n("cd33"),o=n("eb6b");n("48ac"),n("d4b1"),n("4a9d"),i.registerPreprocessor(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!r.isArray(e)&&(t.axisPointer.link=[e])}}),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=a.collect(t,e)}),i.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},o)},cbdc:function(t,e,n){"use strict";var i=n("a7fd"),r=n.n(i);r.a},cbe5:function(t,e,n){var i=n("19eb"),r=n("6d8b"),a=n("20c8"),o=n("d833"),s=n("dc2f"),l=s.prototype.getCanvasPattern,u=Math.abs,c=new a(!0);function h(t){i.call(this,t),this.path=null}h.prototype={constructor:h,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var n,i=this.style,r=this.path||c,a=i.hasStroke(),o=i.hasFill(),s=i.fill,u=i.stroke,h=o&&!!s.colorStops,d=a&&!!u.colorStops,f=o&&!!s.image,p=a&&!!u.image;(i.bind(t,this,e),this.setTransform(t),this.__dirty)&&(h&&(n=n||this.getBoundingRect(),this._fillGradient=i.getGradient(t,s,n)),d&&(n=n||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,u,n)));h?t.fillStyle=this._fillGradient:f&&(t.fillStyle=l.call(s,t)),d?t.strokeStyle=this._strokeGradient:p&&(t.strokeStyle=l.call(u,t));var g=i.lineDash,v=i.lineDashOffset,m=!!t.setLineDash,y=this.getGlobalScale();if(r.setScale(y[0],y[1],this.segmentIgnoreThreshold),this.__dirtyPath||g&&!m&&a?(r.beginPath(t),g&&!m&&(r.setLineDash(g),r.setLineDashOffset(v)),this.buildPath(r,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o)if(null!=i.fillOpacity){var x=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,r.fill(t),t.globalAlpha=x}else r.fill(t);if(g&&m&&(t.setLineDash(g),t.lineDashOffset=v),a)if(null!=i.strokeOpacity){x=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,r.stroke(t),t.globalAlpha=x}else r.stroke(t);g&&m&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new a},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new a),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var o=e.lineWidth,s=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),s>1e-10&&(r.width+=o/s,r.height+=o/s,r.x-=o/s/2,r.y-=o/s/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var s=r.lineWidth,l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(s=Math.max(s,this.strokeContainThreshold)),o.containStroke(a,s/l,t,e)))return!0}if(r.hasFill())return o.contain(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):i.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(r.isObject(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&u(t[0]-1)>1e-10&&u(t[3]-1)>1e-10?Math.sqrt(u(t[0]*t[3]-t[2]*t[1])):1}},h.extend=function(t){var e=function(e){h.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var i=this.shape;for(var r in n)!i.hasOwnProperty(r)&&n.hasOwnProperty(r)&&(i[r]=n[r])}t.init&&t.init.call(this,e)};for(var n in r.inherits(e,h),t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},r.inherits(h,i);var d=h;t.exports=d},cbe9:function(t,e,n){var i=n("6d8b"),r=n("9850"),a=n("cf7e");function o(t){a.call(this,t)}o.prototype={constructor:o,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return n=n||[],n[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),a=i.getExtent(),o=n.parse(t[0]),s=i.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return e=e||[],e[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]),a=Math.max(t[0],t[1])-n,o=Math.max(e[0],e[1])-i,s=new r(n,i,a,o);return s}},i.inherits(o,a);var s=o;t.exports=s},cccd:function(t,e,n){var i=n("e0d3"),r=i.makeInner;function a(){var t=r();return function(e){var n=t(e),i=e.pipelineContext,r=n.large,a=n.progressiveRender,o=n.large=i.large,s=n.progressiveRender=i.progressiveRender;return!!(r^o||a^s)&&"reset"}}t.exports=a},cd33:function(t,e,n){var i=n("6d8b"),r=n("4319"),a=i.each,o=i.curry;function s(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return l(n,t,e),n.seriesInvolved&&c(n,t),n}function l(t,e,n){var i=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),s=r.get("link",!0)||[],l=[];a(n.getCoordinateSystems(),function(n){if(n.axisPointerEnabled){var c=m(n.model),d=t.coordSysAxesInfo[c]={};t.coordSysMap[c]=n;var f=n.model,p=f.getModel("tooltip",i);if(a(n.getAxes(),o(_,!1,null)),n.getTooltipAxes&&i&&p.get("show")){var g="axis"===p.get("trigger"),y="cross"===p.get("axisPointer.type"),x=n.getTooltipAxes(p.get("axisPointer.axis"));(g||y)&&a(x.baseAxes,o(_,!y||"cross",g)),y&&a(x.otherAxes,o(_,"cross",!1))}}function _(i,a,o){var c=o.model.getModel("axisPointer",r),f=c.get("show");if(f&&("auto"!==f||i||v(c))){null==a&&(a=c.get("triggerTooltip")),c=i?u(o,p,r,e,i,a):c;var g=c.get("snap"),y=m(o.model),x=a||g||"category"===o.type,_=t.axesInfo[y]={key:y,axis:o,coordSys:n,axisPointerModel:c,triggerTooltip:a,involveSeries:x,snap:g,useHandle:v(c),seriesModels:[]};d[y]=_,t.seriesInvolved|=x;var b=h(s,o);if(null!=b){var w=l[b]||(l[b]={axesInfo:{}});w.axesInfo[y]=_,w.mapper=s[b].mapper,_.linkGroup=w}}}})}function u(t,e,n,o,s,l){var u=e.getModel("axisPointer"),c={};a(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){c[t]=i.clone(u.get(t))}),c.snap="category"!==t.type&&!!l,"cross"===u.get("type")&&(c.type="line");var h=c.label||(c.label={});if(null==h.show&&(h.show=!1),"cross"===s){var d=u.get("label.show");if(h.show=null==d||d,!l){var f=c.lineStyle=u.get("crossStyle");f&&i.defaults(h,f.textStyle)}}return t.model.getModel("axisPointer",new r(c,n,o))}function c(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&!1!==i&&"item"!==i&&!1!==r&&!1!==e.get("axisPointer.show",!0)&&a(t.coordSysAxesInfo[m(n.model)],function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function h(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(d(a[i+"AxisId"],n.id)||d(a[i+"AxisIndex"],n.componentIndex)||d(a[i+"AxisName"],n.name))return r}}function d(t,e){return"all"===t||i.isArray(t)&&i.indexOf(t,e)>=0||t===e}function f(t){var e=p(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,a=n.get("status"),o=n.get("value");null!=o&&(o=i.parse(o));var s=v(n);null==a&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function p(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[m(t)]}function g(t){var e=p(t);return e&&e.axisPointerModel}function v(t){return!!t.get("handle.show")}function m(t){return t.type+"||"+t.id}e.collect=s,e.fixValue=f,e.getAxisInfo=p,e.getAxisPointerModel=g,e.makeKey=m},cdaa:function(t,e,n){var i=n("607d"),r=i.addEventListener,a=i.removeEventListener,o=i.normalizeEvent,s=i.getNativeEvent,l=n("6d8b"),u=n("1fab"),c=n("22d1"),h=300,d=c.domSupported,f=function(){var t=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],n={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=l.map(t,function(t){var e=t.replace("mouse","pointer");return n.hasOwnProperty(e)?e:t});return{mouse:t,touch:e,pointer:i}}(),p={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function g(t){return"mousewheel"===t&&c.browser.firefox?"DOMMouseScroll":t}function v(t){var e=t.pointerType;return"pen"===e||"touch"===e}function m(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}function y(t){t&&(t.zrByTouch=!0)}function x(t,e){return o(t.dom,new b(t,e),!0)}function _(t,e){var n=!1;do{e=e&&e.parentNode}while(e&&9!==e.nodeType&&!(n=e===t.painterRoot));return n}function b(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var w=b.prototype;w.stopPropagation=w.stopImmediatePropagation=w.preventDefault=l.noop;var S={mousedown:function(t){t=o(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=o(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||A(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=o(this.dom,t),A(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=o(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=_(this,e),this.trigger("mouseout",t)},touchstart:function(t){t=o(this.dom,t),y(t),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),S.mousemove.call(this,t),S.mousedown.call(this,t)},touchmove:function(t){t=o(this.dom,t),y(t),this.handler.processGesture(t,"change"),S.mousemove.call(this,t)},touchend:function(t){t=o(this.dom,t),y(t),this.handler.processGesture(t,"end"),S.mouseup.call(this,t),+new Date-this._lastTouchMoment<h&&S.click.call(this,t)},pointerdown:function(t){S.mousedown.call(this,t)},pointermove:function(t){v(t)||S.mousemove.call(this,t)},pointerup:function(t){S.mouseup.call(this,t)},pointerout:function(t){v(t)||S.mouseout.call(this,t)}};l.each(["click","mousewheel","dblclick","contextmenu"],function(t){S[t]=function(e){e=o(this.dom,e),this.trigger(t,e)}});var M={pointermove:function(t){v(t)||M.mousemove.call(this,t)},pointerup:function(t){M.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;A(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function T(t,e){var n=e.domHandlers;c.pointerEventsSupported?l.each(f.pointer,function(i){I(e,i,function(e){n[i].call(t,e)})}):(c.touchEventsSupported&&l.each(f.touch,function(i){I(e,i,function(r){n[i].call(t,r),m(e)})}),l.each(f.mouse,function(i){I(e,i,function(r){r=s(r),e.touching||n[i].call(t,r)})}))}function C(t,e){function n(n){function i(i){i=s(i),_(t,i.target)||(i=x(t,i),e.domHandlers[n].call(t,i))}I(e,n,i,{capture:!0})}c.pointerEventsSupported?l.each(p.pointer,n):c.touchEventsSupported||l.each(p.mouse,n)}function I(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,r(t.domTarget,g(e),n,i)}function D(t){var e=t.mounted;for(var n in e)e.hasOwnProperty(n)&&a(t.domTarget,g(n),e[n],t.listenerOpts[n]);t.mounted={}}function A(t,e){if(t._mayPointerCapture=null,d&&t._pointerCapturing^e){t._pointerCapturing=e;var n=t._globalHandlerScope;e?C(t,n):D(n)}}function k(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function O(t,e){u.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new k(t,S),d&&(this._globalHandlerScope=new k(document,M)),this._pointerCapturing=!1,this._mayPointerCapture=null,T(this,this._localHandlerScope)}var P=O.prototype;P.dispose=function(){D(this._localHandlerScope),d&&D(this._globalHandlerScope)},P.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},l.mixin(O,u);var L=O;t.exports=L},cf7e:function(t,e,n){var i=n("6d8b");function r(t){return this._axes[t]}var a=function(t){this._axes={},this._dimList=[],this.name=t||""};a.prototype={constructor:a,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return i.map(this._dimList,r,this)},getAxesByScale:function(t){return t=t.toLowerCase(),i.filter(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var a=n[r],o=this._axes[a];i[a]=o[e](t[a])}return i}};var o=a;t.exports=o},d15d:function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.each;function o(t){var e=r();t.eachSeries(function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(a)}}),e.each(s)}function s(t){a(t,function(e,n){var i=[],r=[NaN,NaN],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,function(a,l,u){var c,h,d=o.get(e.stackedDimension,u);if(isNaN(d))return r;s?h=o.getRawIndex(u):c=o.get(e.stackedByDimension,u);for(var f=NaN,p=n-1;p>=0;p--){var g=t[p];if(s||(h=g.data.rawIndexOf(g.stackedByDimension,c)),h>=0){var v=g.data.getByRawIndex(g.stackResultDimension,h);if(d>=0&&v>0||d<=0&&v<0){d+=v,f=v;break}}}return i[0]=d,i[1]=f,i});o.hostModel.setData(l),e.data=l})}t.exports=o},d28f:function(t,e,n){var i=n("3eba");n("84d5"),n("4650"),n("5e97");var r=n("903c"),a=n("6cb7");i.registerProcessor(i.PRIORITY.PROCESSOR.SERIES_FILTER,r),a.registerSubTypeDefaulter("legend",function(){return"plain"})},d2cf:function(t,e,n){var i=n("6d8b"),r=n("401b"),a=n("cb6d"),o=n("1fab"),s=n("607d"),l=n("0b44"),u="silent";function c(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:h}}function h(){s.stop(this.event)}function d(){}d.prototype.dispose=function(){};var f=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],p=function(t,e,n,i){o.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new d,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,a.call(this),this.setHandlerProxy(n)};function g(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){var i,r=t;while(r){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||u}return!1}function v(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}p.prototype={constructor:p,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(i.each(f,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,n=t.zrY,i=v(this,e,n),r=this._hovered,a=r.target;a&&!a.__zr&&(r=this.findHover(r.x,r.y),a=r.target);var o=this._hovered=i?{x:e,y:n}:this.findHover(e,n),s=o.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,n=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&!n&&this.trigger("globalout",{type:"globalout",event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){t=t||{};var i=t.target;if(!i||!i.silent){var r="on"+e,a=c(e,t,n);while(i)if(i[r]&&(a.cancelBubble=i[r].call(i,a)),i.trigger(e,a),i=i.parent,a.cancelBubble)break;a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"===typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},a=i.length-1;a>=0;a--){var o;if(i[a]!==n&&!i[a].ignore&&(o=g(i[a],t,e))&&(!r.topTarget&&(r.topTarget=i[a]),o!==u)){r.target=i[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new l);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r,this.dispatchToElement({target:i.target},r,i.event)}}},i.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){p.prototype[t]=function(e){var n,i,a=e.zrX,o=e.zrY,s=v(this,a,o);if("mouseup"===t&&s||(n=this.findHover(a,o),i=n.target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||r.dist(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}),i.mixin(p,o),i.mixin(p,a);var m=p;t.exports=m},d498:function(t,e,n){var i=n("cbe5"),r=n("4fac"),a=i.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){r.buildPath(t,e,!1)}});t.exports=a},d4b1:function(t,e,n){var i=n("3eba"),r=n("17d6"),a=i.extendComponentView({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),a=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";r.register("axisPointer",n,function(t,e,n){"none"!==a&&("leave"===t||a.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){r.unregister(e.getZr(),"axisPointer"),a.superApply(this._model,"remove",arguments)},dispose:function(t,e){r.unregister("axisPointer",e),a.superApply(this._model,"dispose",arguments)}}),o=a;t.exports=o},d4c6:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),i.prototype.getBoundingRect.call(this)}});t.exports=r},d4d1:function(t,e,n){var i=n("cbe5"),r=n("401b"),a=n("897a"),o=r.min,s=r.max,l=r.scaleAndAdd,u=r.copy,c=[],h=[],d=[];function f(t){return isNaN(t[0])||isNaN(t[1])}function p(t,e,n,i,r,a,o,s,l,u,c){return"none"!==u&&u?g.apply(this,arguments):v.apply(this,arguments)}function g(t,e,n,i,r,a,o,s,l,c,p){for(var g=0,v=n,m=0;m<i;m++){var y=e[v];if(v>=r||v<0)break;if(f(y)){if(p){v+=a;continue}break}if(v===n)t[a>0?"moveTo":"lineTo"](y[0],y[1]);else if(l>0){var x=e[g],_="y"===c?1:0,b=(y[_]-x[_])*l;u(h,x),h[_]=x[_]+b,u(d,y),d[_]=y[_]-b,t.bezierCurveTo(h[0],h[1],d[0],d[1],y[0],y[1])}else t.lineTo(y[0],y[1]);g=v,v+=a}return m}function v(t,e,n,i,a,p,g,v,m,y,x){for(var _=0,b=n,w=0;w<i;w++){var S=e[b];if(b>=a||b<0)break;if(f(S)){if(x){b+=p;continue}break}if(b===n)t[p>0?"moveTo":"lineTo"](S[0],S[1]),u(h,S);else if(m>0){var M=b+p,T=e[M];if(x)while(T&&f(e[M]))M+=p,T=e[M];var C=.5,I=e[_];T=e[M];if(!T||f(T))u(d,S);else{var D,A;if(f(T)&&!x&&(T=S),r.sub(c,T,I),"x"===y||"y"===y){var k="x"===y?0:1;D=Math.abs(S[k]-I[k]),A=Math.abs(S[k]-T[k])}else D=r.dist(S,I),A=r.dist(S,T);C=A/(A+D),l(d,S,c,-m*(1-C))}o(h,h,v),s(h,h,g),o(d,d,v),s(d,d,g),t.bezierCurveTo(h[0],h[1],d[0],d[1],S[0],S[1]),l(h,S,c,m*C)}else t.lineTo(S[0],S[1]);_=b,b+=p}return w}function m(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var a=t[r];a[0]<n[0]&&(n[0]=a[0]),a[1]<n[1]&&(n[1]=a[1]),a[0]>i[0]&&(i[0]=a[0]),a[1]>i[1]&&(i[1]=a[1])}return{min:e?n:i,max:e?i:n}}var y=i.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:a(i.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,r=n.length,a=m(n,e.smoothConstraint);if(e.connectNulls){for(;r>0;r--)if(!f(n[r-1]))break;for(;i<r;i++)if(!f(n[i]))break}while(i<r)i+=p(t,n,i,r,r,1,a.min,a.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),x=i.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:a(i.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,a=n.length,o=e.smoothMonotone,s=m(n,e.smoothConstraint),l=m(i,e.smoothConstraint);if(e.connectNulls){for(;a>0;a--)if(!f(n[a-1]))break;for(;r<a;r++)if(!f(n[r]))break}while(r<a){var u=p(t,n,r,a,a,1,s.min,s.max,e.smooth,o,e.connectNulls);p(t,i,r+u-1,u,a,-1,l.min,l.max,e.stackedOnSmooth,o,e.connectNulls),r+=u+1,t.closePath()}}});e.Polyline=y,e.Polygon=x},d51b:function(t,e){var n=function(){this.head=null,this.tail=null,this._len=0},i=n.prototype;i.insert=function(t){var e=new r(t);return this.insertEntry(e),e},i.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},i.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},i.len=function(){return this._len},i.clear=function(){this.head=this.tail=null,this._len=0};var r=function(t){this.value=t,this.next,this.prev},a=function(t){this._list=new n,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},o=a.prototype;o.put=function(t,e){var n=this._list,i=this._map,a=null;if(null==i[t]){var o=n.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=n.head;n.remove(l),delete i[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new r(e),s.key=t,n.insertEntry(s),i[t]=s}return a},o.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},o.clear=function(){this._list.clear(),this._map={}};var s=a;t.exports=s},d5b7:function(t,e,n){var i=n("de00"),r=n("1fab"),a=n("0cde"),o=n("bd6b"),s=n("6d8b"),l=function(t){a.call(this,t),r.call(this,t),o.call(this,t),this.id=t.id||i()};l.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"===typeof t)this.attrKV(t,e);else if(s.isObject(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},s.mixin(l,o),s.mixin(l,a),s.mixin(l,r);var u=l;t.exports=u},d833:function(t,e,n){var i=n("20c8"),r=n("9680"),a=n("e7d2"),o=n("68ab"),s=n("9f51"),l=n("857d"),u=l.normalizeRadian,c=n("4a3f"),h=n("8728"),d=i.CMD,f=2*Math.PI,p=1e-4;function g(t,e){return Math.abs(t-e)<p}var v=[-1,-1,-1],m=[-1,-1];function y(){var t=m[0];m[0]=m[1],m[1]=t}function x(t,e,n,i,r,a,o,s,l,u){if(u>e&&u>i&&u>a&&u>s||u<e&&u<i&&u<a&&u<s)return 0;var h=c.cubicRootAt(e,i,a,s,u,v);if(0===h)return 0;for(var d,f,p=0,g=-1,x=0;x<h;x++){var _=v[x],b=0===_||1===_?.5:1,w=c.cubicAt(t,n,r,o,_);w<l||(g<0&&(g=c.cubicExtrema(e,i,a,s,m),m[1]<m[0]&&g>1&&y(),d=c.cubicAt(e,i,a,s,m[0]),g>1&&(f=c.cubicAt(e,i,a,s,m[1]))),2===g?_<m[0]?p+=d<e?b:-b:_<m[1]?p+=f<d?b:-b:p+=s<f?b:-b:_<m[0]?p+=d<e?b:-b:p+=s<d?b:-b)}return p}function _(t,e,n,i,r,a,o,s){if(s>e&&s>i&&s>a||s<e&&s<i&&s<a)return 0;var l=c.quadraticRootAt(e,i,a,s,v);if(0===l)return 0;var u=c.quadraticExtremum(e,i,a);if(u>=0&&u<=1){for(var h=0,d=c.quadraticAt(e,i,a,u),f=0;f<l;f++){var p=0===v[f]||1===v[f]?.5:1,g=c.quadraticAt(t,n,r,v[f]);g<o||(v[f]<u?h+=d<e?p:-p:h+=a<d?p:-p)}return h}p=0===v[0]||1===v[0]?.5:1,g=c.quadraticAt(t,n,r,v[0]);return g<o?0:a<e?p:-p}function b(t,e,n,i,r,a,o,s){if(s-=e,s>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);v[0]=-l,v[1]=l;var c=Math.abs(i-r);if(c<1e-4)return 0;if(c%f<1e-4){i=0,r=f;var h=a?1:-1;return o>=v[0]+t&&o<=v[1]+t?h:0}if(a){l=i;i=u(r),r=u(l)}else i=u(i),r=u(r);i>r&&(r+=f);for(var d=0,p=0;p<2;p++){var g=v[p];if(g+t>o){var m=Math.atan2(s,g);h=a?1:-1;m<0&&(m=f+m),(m>=i&&m<=r||m+f>=i&&m+f<=r)&&(m>Math.PI/2&&m<1.5*Math.PI&&(h=-h),d+=h)}}return d}function w(t,e,n,i,l){for(var u=0,c=0,f=0,p=0,v=0,m=0;m<t.length;){var y=t[m++];switch(y===d.M&&m>1&&(n||(u+=h(c,f,p,v,i,l))),1===m&&(c=t[m],f=t[m+1],p=c,v=f),y){case d.M:p=t[m++],v=t[m++],c=p,f=v;break;case d.L:if(n){if(r.containStroke(c,f,t[m],t[m+1],e,i,l))return!0}else u+=h(c,f,t[m],t[m+1],i,l)||0;c=t[m++],f=t[m++];break;case d.C:if(n){if(a.containStroke(c,f,t[m++],t[m++],t[m++],t[m++],t[m],t[m+1],e,i,l))return!0}else u+=x(c,f,t[m++],t[m++],t[m++],t[m++],t[m],t[m+1],i,l)||0;c=t[m++],f=t[m++];break;case d.Q:if(n){if(o.containStroke(c,f,t[m++],t[m++],t[m],t[m+1],e,i,l))return!0}else u+=_(c,f,t[m++],t[m++],t[m],t[m+1],i,l)||0;c=t[m++],f=t[m++];break;case d.A:var w=t[m++],S=t[m++],M=t[m++],T=t[m++],C=t[m++],I=t[m++];m+=1;var D=1-t[m++],A=Math.cos(C)*M+w,k=Math.sin(C)*T+S;m>1?u+=h(c,f,A,k,i,l):(p=A,v=k);var O=(i-w)*T/M+w;if(n){if(s.containStroke(w,S,T,C,C+I,D,e,O,l))return!0}else u+=b(w,S,T,C,C+I,D,O,l);c=Math.cos(C+I)*M+w,f=Math.sin(C+I)*T+S;break;case d.R:p=c=t[m++],v=f=t[m++];var P=t[m++],L=t[m++];A=p+P,k=v+L;if(n){if(r.containStroke(p,v,A,v,e,i,l)||r.containStroke(A,v,A,k,e,i,l)||r.containStroke(A,k,p,k,e,i,l)||r.containStroke(p,k,p,v,e,i,l))return!0}else u+=h(A,v,A,k,i,l),u+=h(p,k,p,v,i,l);break;case d.Z:if(n){if(r.containStroke(c,f,p,v,e,i,l))return!0}else u+=h(c,f,p,v,i,l);c=p,f=v;break}}return n||g(f,v)||(u+=h(c,f,p,v,i,l)||0),0!==u}function S(t,e,n){return w(t,0,!1,e,n)}function M(t,e,n,i){return w(t,e,!0,n,i)}e.contain=S,e.containStroke=M},d9fc:function(t,e,n){var i=n("cbe5"),r=i.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}});t.exports=r},dc2f:function(t,e){var n=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};n.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var i=n;t.exports=i},dcb3:function(t,e,n){var i=n("6d8b"),r=n("625e"),a=n("2306"),o=n("cd33"),s=n("607d"),l=n("88b3"),u=n("e0d3"),c=u.makeInner,h=c(),d=i.clone,f=i.bind;function p(){}function g(t,e,n,i){v(h(n).lastProp,i)||(h(n).lastProp=i,e?a.updateProps(n,i,t):(n.stopAnimation(),n.attr(i)))}function v(t,e){if(i.isObject(t)&&i.isObject(e)){var n=!0;return i.each(e,function(e,i){n=n&&v(t[i],e)}),!!n}return t===e}function m(t,e){t[e.get("label.show")?"show":"hide"]()}function y(t){return{position:t.position.slice(),rotation:t.rotation||0}}function x(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}p.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,r){var o=e.get("value"),s=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,r||this._lastValue!==o||this._lastStatus!==s){this._lastValue=o,this._lastStatus=s;var l=this._group,u=this._handle;if(!s||"hide"===s)return l&&l.hide(),void(u&&u.hide());l&&l.show(),u&&u.show();var c={};this.makeElOption(c,o,t,e,n);var h=c.graphicKey;h!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=h;var d=this._moveAnimation=this.determineAnimation(t,e);if(l){var f=i.curry(g,e,d);this.updatePointerEl(l,c,f,e),this.updateLabelEl(l,c,f,e)}else l=this._group=new a.Group,this.createPointerEl(l,c,t,e),this.createLabelEl(l,c,t,e),n.getZr().add(l);x(l,e,!0),this._renderHandle(o)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===n||null==n){var s=this.animationThreshold;if(r&&i.getBandWidth()>s)return!0;if(a){var l=o.getAxisInfo(t).seriesDataCount,u=i.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return!0===n},makeElOption:function(t,e,n,i,r){},createPointerEl:function(t,e,n,i){var r=e.pointer;if(r){var o=h(t).pointerEl=new a[r.type](d(e.pointer));t.add(o)}},createLabelEl:function(t,e,n,i){if(e.label){var r=h(t).labelEl=new a.Rect(d(e.label));t.add(r),m(r,i)}},updatePointerEl:function(t,e,n){var i=h(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=h(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),m(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e,n=this._axisPointerModel,r=this._api.getZr(),o=this._handle,u=n.getModel("handle"),c=n.get("status");if(!u.get("show")||!c||"hide"===c)return o&&r.remove(o),void(this._handle=null);this._handle||(e=!0,o=this._handle=a.createIcon(u.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){s.stop(t.event)},onmousedown:f(this._onHandleDragMove,this,0,0),drift:f(this._onHandleDragMove,this),ondragend:f(this._onHandleDragEnd,this)}),r.add(o)),x(o,n,!1);var h=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];o.setStyle(u.getItemStyle(null,h));var d=u.get("size");i.isArray(d)||(d=[d,d]),o.attr("scale",[d[0]/2,d[1]/2]),l.createOrUpdate(this,"_doDispatchAxisPointer",u.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},_moveHandleToValue:function(t,e){g(this._axisPointerModel,!e&&this._moveAnimation,this._handle,y(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(y(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(y(i)),h(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},_onHandleDragEnd:function(t){this._dragging=!1;var e=this._handle;if(e){var n=this._axisPointerModel.get("value");this._moveHandleToValue(n),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}},p.prototype.constructor=p,r.enableClassExtend(p);var _=p;t.exports=_},dded:function(t,e,n){var i=n("6d8b"),r=n("42e5"),a=function(t,e,n,i,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=a||!1,r.call(this,i)};a.prototype={constructor:a},i.inherits(a,r);var o=a;t.exports=o},de00:function(t,e){var n=2311;function i(){return n++}t.exports=i},de1c:function(t,e){var n={getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}};t.exports=n},e073:function(t,e,n){var i=n("6d8b"),r=n("e86a"),a=n("e0d3"),o=a.makeInner,s=n("697e"),l=s.makeLabelFormatter,u=s.getOptionCategoryInterval,c=s.shouldShowAllLabels,h=o();function d(t){return"category"===t.type?p(t):m(t)}function f(t,e){return"category"===t.type?v(t,e):{ticks:t.scale.getTicks()}}function p(t){var e=t.getLabelModel(),n=g(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}function g(t,e){var n,r,a=y(t,"labels"),o=u(e),s=x(a,o);return s||(i.isFunction(o)?n=T(t,o):(r="auto"===o?b(t):o,n=M(t,r)),_(a,o,{labels:n,labelCategoryInterval:r}))}function v(t,e){var n,r,a=y(t,"ticks"),o=u(e),s=x(a,o);if(s)return s;if(e.get("show")&&!t.scale.isBlank()||(n=[]),i.isFunction(o))n=T(t,o,!0);else if("auto"===o){var l=g(t,t.getLabelModel());r=l.labelCategoryInterval,n=i.map(l.labels,function(t){return t.tickValue})}else r=o,n=M(t,r,!0);return _(a,o,{ticks:n,tickCategoryInterval:r})}function m(t){var e=t.scale.getTicks(),n=l(t);return{labels:i.map(e,function(e,i){return{formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function y(t,e){return h(t)[e]||(h(t)[e]=[])}function x(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function _(t,e,n){return t.push({key:e,value:n}),n}function b(t){var e=h(t).autoInterval;return null!=e?e:h(t).autoInterval=t.calculateCategoryInterval()}function w(t){var e=S(t),n=l(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,a=t.scale,o=a.getExtent(),s=a.count();if(o[1]-o[0]<1)return 0;var u=1;s>40&&(u=Math.max(1,Math.floor(s/40)));for(var c=o[0],d=t.dataToCoord(c+1)-t.dataToCoord(c),f=Math.abs(d*Math.cos(i)),p=Math.abs(d*Math.sin(i)),g=0,v=0;c<=o[1];c+=u){var m=0,y=0,x=r.getBoundingRect(n(c),e.font,"center","top");m=1.3*x.width,y=1.3*x.height,g=Math.max(g,m,7),v=Math.max(v,y,7)}var _=g/f,b=v/p;isNaN(_)&&(_=1/0),isNaN(b)&&(b=1/0);var w=Math.max(0,Math.floor(Math.min(_,b))),M=h(t.model),T=t.getExtent(),C=M.lastAutoInterval,I=M.lastTickCount;return null!=C&&null!=I&&Math.abs(C-w)<=1&&Math.abs(I-s)<=1&&C>w&&M.axisExtend0===T[0]&&M.axisExtend1===T[1]?w=C:(M.lastTickCount=s,M.lastAutoInterval=w,M.axisExtend0=T[0],M.axisExtend1=T[1]),w}function S(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function M(t,e,n){var i=l(t),r=t.scale,a=r.getExtent(),o=t.getLabelModel(),s=[],u=Math.max((e||0)+1,1),h=a[0],d=r.count();0!==h&&u>1&&d/u>2&&(h=Math.round(Math.ceil(h/u)*u));var f=c(t),p=o.get("showMinLabel")||f,g=o.get("showMaxLabel")||f;p&&h!==a[0]&&m(a[0]);for(var v=h;v<=a[1];v+=u)m(v);function m(t){s.push(n?t:{formattedLabel:i(t),rawLabel:r.getLabel(t),tickValue:t})}return g&&v-u!==a[1]&&m(a[1]),s}function T(t,e,n){var r=t.scale,a=l(t),o=[];return i.each(r.getTicks(),function(t){var i=r.getLabel(t);e(t,i)&&o.push(n?t:{formattedLabel:a(t),rawLabel:i,tickValue:t})}),o}e.createAxisLabels=d,e.createAxisTicks=f,e.calculateCategoryInterval=w},e0d3:function(t,e,n){var i=n("6d8b"),r=n("22d1"),a=i.each,o=i.isObject,s=i.isArray,l="series\0";function u(t){return t instanceof Array?t:null==t?[]:[t]}function c(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var a=n[i];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}var h=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function d(t){return!o(t)||s(t)||t instanceof Date?t:t.value}function f(t){return o(t)&&!(t instanceof Array)}function p(t,e){e=(e||[]).slice();var n=i.map(t||[],function(t,e){return{exist:t}});return a(e,function(t,i){if(o(t)){for(var r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(r=0;r<n.length;r++){var a=n[r].exist;if(!n[r].option&&(null==a.id||null==t.id)&&null!=t.name&&!m(t)&&!m(a)&&a.name===t.name+"")return n[r].option=t,void(e[i]=null)}}}),a(e,function(t,e){if(o(t)){for(var i=0;i<n.length;i++){var r=n[i].exist;if(!n[i].option&&!m(r)&&null==t.id){n[i].option=t;break}}i>=n.length&&n.push({option:t})}}),n}function g(t){var e=i.createHashMap();a(t,function(t,n){var i=t.exist;i&&e.set(i.id,t)}),a(t,function(t,n){var r=t.option;i.assert(!r||null==r.id||!e.get(r.id)||e.get(r.id)===t,"id duplicates: "+(r&&r.id)),r&&null!=r.id&&e.set(r.id,t),!t.keyInfo&&(t.keyInfo={})}),a(t,function(t,n){var i=t.exist,r=t.option,a=t.keyInfo;if(o(r)){if(a.name=null!=r.name?r.name+"":i?i.name:l+n,i)a.id=i.id;else if(null!=r.id)a.id=r.id+"";else{var s=0;do{a.id="\0"+a.name+"\0"+s++}while(e.get(a.id))}e.set(a.id,t)}})}function v(t){var e=t.name;return!(!e||!e.indexOf(l))}function m(t){return o(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function y(t,e){var n={},i={};return r(t||[],n),r(e||[],i,n),[a(n),a(i)];function r(t,e,n){for(var i=0,r=t.length;i<r;i++)for(var a=t[i].seriesId,o=u(t[i].dataIndex),s=n&&n[a],l=0,c=o.length;l<c;l++){var h=o[l];s&&s[h]?s[h]=null:(e[a]||(e[a]={}))[h]=1}}function a(t,e){var n=[];for(var i in t)if(t.hasOwnProperty(i)&&null!=t[i])if(e)n.push(+i);else{var r=a(t[i],!0);r.length&&n.push({seriesId:i,dataIndex:r})}return n}}function x(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?i.isArray(e.dataIndex)?i.map(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?i.isArray(e.name)?i.map(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function _(){var t="__\0ec_inner_"+b+++"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}var b=0;function w(t,e,n){if(i.isString(e)){var r={};r[e+"Index"]=0,e=r}var o=n&&n.defaultMainType;!o||S(e,o+"Index")||S(e,o+"Id")||S(e,o+"Name")||(e[o+"Index"]=0);var s={};return a(e,function(r,a){r=e[a];if("dataIndex"!==a&&"dataIndexInside"!==a){var o=a.match(/^(\w+)(Index|Id|Name)$/)||[],l=o[1],u=(o[2]||"").toLowerCase();if(!(!l||!u||null==r||"index"===u&&"none"===r||n&&n.includeMainTypes&&i.indexOf(n.includeMainTypes,l)<0)){var c={mainType:l};"index"===u&&"all"===r||(c[u]=r);var h=t.queryComponents(c);s[l+"Models"]=h,s[l+"Model"]=h[0]}}else s[a]=r}),s}function S(t,e){return t&&t.hasOwnProperty(e)}function M(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function T(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function C(t){return"auto"===t?r.domSupported?"html":"richText":t||"html"}function I(t,e){var n=i.createHashMap(),r=[];return i.each(t,function(t){var i=e(t);(n.get(i)||(r.push(i),n.set(i,[]))).push(t)}),{keys:r,buckets:n}}e.normalizeToArray=u,e.defaultEmphasis=c,e.TEXT_STYLE_OPTIONS=h,e.getDataItemValue=d,e.isDataItemOption=f,e.mappingToExists=p,e.makeIdAndName=g,e.isNameSpecified=v,e.isIdInner=m,e.compressBatches=y,e.queryDataIndex=x,e.makeInner=_,e.parseFinder=w,e.setAttribute=M,e.getAttribute=T,e.getTooltipRenderMode=C,e.groupData=I},e0d8:function(t,e,n){var i=n("625e");function r(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}r.prototype.parse=function(t){return t},r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},r.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},r.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r.prototype.getLabel=null,i.enableClassExtend(r),i.enableClassManagement(r,{registerWhenExtend:!0});var a=r;t.exports=a},e1fc:function(t,e,n){var i=n("6d8b"),r=n("d5b7"),a=n("9850"),o=function(t){for(var e in t=t||{},r.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};o.prototype={constructor:o,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof o&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,r=this._children,a=i.indexOf(r,t);return a<0?this:(r.splice(a,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(n)),e&&e.refresh(),this)},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof o&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof o&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new a(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var s=i[o];if(!s.ignore&&!s.invisible){var l=s.getBoundingRect(),u=s.getLocalTransform(r);u?(n.copy(l),n.applyTransform(u),e=e||n.clone(),e.union(n)):(e=e||l.clone(),e.union(l))}}return e||n}},i.inherits(o,r);var s=o;t.exports=s},e263:function(t,e,n){var i=n("401b"),r=n("4a3f"),a=Math.min,o=Math.max,s=Math.sin,l=Math.cos,u=2*Math.PI,c=i.create(),h=i.create(),d=i.create();function f(t,e,n){if(0!==t.length){var i,r=t[0],s=r[0],l=r[0],u=r[1],c=r[1];for(i=1;i<t.length;i++)r=t[i],s=a(s,r[0]),l=o(l,r[0]),u=a(u,r[1]),c=o(c,r[1]);e[0]=s,e[1]=u,n[0]=l,n[1]=c}}function p(t,e,n,i,r,s){r[0]=a(t,n),r[1]=a(e,i),s[0]=o(t,n),s[1]=o(e,i)}var g=[],v=[];function m(t,e,n,i,s,l,u,c,h,d){var f,p=r.cubicExtrema,m=r.cubicAt,y=p(t,n,s,u,g);for(h[0]=1/0,h[1]=1/0,d[0]=-1/0,d[1]=-1/0,f=0;f<y;f++){var x=m(t,n,s,u,g[f]);h[0]=a(x,h[0]),d[0]=o(x,d[0])}for(y=p(e,i,l,c,v),f=0;f<y;f++){var _=m(e,i,l,c,v[f]);h[1]=a(_,h[1]),d[1]=o(_,d[1])}h[0]=a(t,h[0]),d[0]=o(t,d[0]),h[0]=a(u,h[0]),d[0]=o(u,d[0]),h[1]=a(e,h[1]),d[1]=o(e,d[1]),h[1]=a(c,h[1]),d[1]=o(c,d[1])}function y(t,e,n,i,s,l,u,c){var h=r.quadraticExtremum,d=r.quadraticAt,f=o(a(h(t,n,s),1),0),p=o(a(h(e,i,l),1),0),g=d(t,n,s,f),v=d(e,i,l,p);u[0]=a(t,s,g),u[1]=a(e,l,v),c[0]=o(t,s,g),c[1]=o(e,l,v)}function x(t,e,n,r,a,o,f,p,g){var v=i.min,m=i.max,y=Math.abs(a-o);if(y%u<1e-4&&y>1e-4)return p[0]=t-n,p[1]=e-r,g[0]=t+n,void(g[1]=e+r);if(c[0]=l(a)*n+t,c[1]=s(a)*r+e,h[0]=l(o)*n+t,h[1]=s(o)*r+e,v(p,c,h),m(g,c,h),a%=u,a<0&&(a+=u),o%=u,o<0&&(o+=u),a>o&&!f?o+=u:a<o&&f&&(a+=u),f){var x=o;o=a,a=x}for(var _=0;_<o;_+=Math.PI/2)_>a&&(d[0]=l(_)*n+t,d[1]=s(_)*r+e,v(p,d,p),m(g,d,g))}e.fromPoints=f,e.fromLine=p,e.fromCubic=m,e.fromQuadratic=y,e.fromArc=x},e47b:function(t,e,n){var i=n("e0d3"),r=i.makeInner,a=i.normalizeToArray,o=r();function s(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var l={clearColorPalette:function(){o(this).colorIdx=0,o(this).colorNameMap={}},getColorFromPalette:function(t,e,n){e=e||this;var i=o(e),r=i.colorIdx||0,l=i.colorNameMap=i.colorNameMap||{};if(l.hasOwnProperty(t))return l[t];var u=a(this.get("color",!0)),c=this.get("colorLayer",!0),h=null!=n&&c?s(c,n):u;if(h=h||u,h&&h.length){var d=h[r];return t&&(l[t]=d),i.colorIdx=(r+1)%h.length,d}}};t.exports=l},e7aa:function(t,e,n){var i=n("2306"),r=n("c775"),a=r.getDefaultLabel;function o(t,e,n,r,o,l,u){var c=n.getModel("label"),h=n.getModel("emphasis.label");i.setLabelStyle(t,e,c,h,{labelFetcher:o,labelDataIndex:l,defaultText:a(o.getData(),l),isRectText:!0,autoColor:r}),s(t),s(e)}function s(t,e){"outside"===t.textPosition&&(t.textPosition=e)}e.setLabel=o},e7d2:function(t,e,n){var i=n("4a3f");function r(t,e,n,r,a,o,s,l,u,c,h){if(0===u)return!1;var d=u;if(h>e+d&&h>r+d&&h>o+d&&h>l+d||h<e-d&&h<r-d&&h<o-d&&h<l-d||c>t+d&&c>n+d&&c>a+d&&c>s+d||c<t-d&&c<n-d&&c<a-d&&c<s-d)return!1;var f=i.cubicProjectPoint(t,e,n,r,a,o,s,l,c,h,null);return f<=d/2}e.containStroke=r},e86a:function(t,e,n){var i=n("9850"),r=n("5e76"),a=n("6d8b"),o=a.getContext,s=a.extend,l=a.retrieve2,u=a.retrieve3,c=a.trim,h={},d=0,f=5e3,p=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,g="12px sans-serif",v={};function m(t,e){v[t]=e}function y(t,e){e=e||g;var n=t+":"+e;if(h[n])return h[n];for(var i=(t+"").split("\n"),r=0,a=0,o=i.length;a<o;a++)r=Math.max(O(i[a],e).width,r);return d>f&&(d=0,h={}),d++,h[n]=r,r}function x(t,e,n,i,r,a,o,s){return o?b(t,e,n,i,r,a,o,s):_(t,e,n,i,r,a,s)}function _(t,e,n,r,a,o,s){var l=P(t,e,a,o,s),u=y(t,e);a&&(u+=a[1]+a[3]);var c=l.outerHeight,h=w(0,u,n),d=S(0,c,r),f=new i(h,d,u,c);return f.lineHeight=l.lineHeight,f}function b(t,e,n,r,a,o,s,l){var u=L(t,{rich:s,truncate:l,font:e,textAlign:n,textPadding:a,textLineHeight:o}),c=u.outerWidth,h=u.outerHeight,d=w(0,c,n),f=S(0,h,r);return new i(d,f,c,h)}function w(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function S(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function M(t,e,n){var i=e.textPosition,r=e.textDistance,a=n.x,o=n.y;r=r||0;var s=n.height,l=n.width,u=s/2,c="left",h="top";switch(i){case"left":a-=r,o+=u,c="right",h="middle";break;case"right":a+=r+l,o+=u,h="middle";break;case"top":a+=l/2,o-=r,c="center",h="bottom";break;case"bottom":a+=l/2,o+=s+r,c="center";break;case"inside":a+=l/2,o+=u,c="center",h="middle";break;case"insideLeft":a+=r,o+=u,h="middle";break;case"insideRight":a+=l-r,o+=u,c="right",h="middle";break;case"insideTop":a+=l/2,o+=r,c="center";break;case"insideBottom":a+=l/2,o+=s-r,c="center",h="bottom";break;case"insideTopLeft":a+=r,o+=r;break;case"insideTopRight":a+=l-r,o+=r,c="right";break;case"insideBottomLeft":a+=r,o+=s-r,h="bottom";break;case"insideBottomRight":a+=l-r,o+=s-r,c="right",h="bottom";break}return t=t||{},t.x=a,t.y=o,t.textAlign=c,t.textVerticalAlign=h,t}function T(t,e,n){var i={textPosition:t,textDistance:n};return M({},i,e)}function C(t,e,n,i,r){if(!e)return"";var a=(t+"").split("\n");r=I(e,n,i,r);for(var o=0,s=a.length;o<s;o++)a[o]=D(a[o],r);return a.join("\n")}function I(t,e,n,i){i=s({},i),i.font=e;n=l(n,"...");i.maxIterations=l(i.maxIterations,2);var r=i.minChar=l(i.minChar,0);i.cnCharWidth=y("国",e);var a=i.ascCharWidth=y("a",e);i.placeholder=l(i.placeholder,"");for(var o=t=Math.max(0,t-1),u=0;u<r&&o>=a;u++)o-=a;var c=y(n,e);return c>o&&(n="",c=0),o=t-c,i.ellipsis=n,i.ellipsisWidth=c,i.contentWidth=o,i.containerWidth=t,i}function D(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var a=y(t,i);if(a<=n)return t;for(var o=0;;o++){if(a<=r||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?A(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=y(t,i)}return""===t&&(t=e.placeholder),t}function A(t,e,n,i){for(var r=0,a=0,o=t.length;a<o&&r<e;a++){var s=t.charCodeAt(a);r+=0<=s&&s<=127?n:i}return a}function k(t){return y("国",t)}function O(t,e){return v.measureText(t,e)}function P(t,e,n,i,r){null!=t&&(t+="");var a=l(i,k(e)),o=t?t.split("\n"):[],s=o.length*a,u=s,c=!0;if(n&&(u+=n[0]+n[2]),t&&r){c=!1;var h=r.outerHeight,d=r.outerWidth;if(null!=h&&u>h)t="",o=[];else if(null!=d)for(var f=I(d-(n?n[1]+n[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),p=0,g=o.length;p<g;p++)o[p]=D(o[p],f)}return{lines:o,height:s,outerHeight:u,lineHeight:a,canCacheByTextString:c}}function L(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;var i,a=p.lastIndex=0;while(null!=(i=p.exec(t))){var o=i.index;o>a&&E(n,t.substring(a,o)),E(n,i[2],i[1]),a=p.lastIndex}a<t.length&&E(n,t.substring(a,t.length));var s=n.lines,c=0,h=0,d=[],f=e.textPadding,g=e.truncate,v=g&&g.outerWidth,m=g&&g.outerHeight;f&&(null!=v&&(v-=f[1]+f[3]),null!=m&&(m-=f[0]+f[2]));for(var x=0;x<s.length;x++){for(var _=s[x],b=0,w=0,S=0;S<_.tokens.length;S++){var M=_.tokens[S],T=M.styleName&&e.rich[M.styleName]||{},I=M.textPadding=T.textPadding,D=M.font=T.font||e.font,A=M.textHeight=l(T.textHeight,k(D));if(I&&(A+=I[0]+I[2]),M.height=A,M.lineHeight=u(T.textLineHeight,e.textLineHeight,A),M.textAlign=T&&T.textAlign||e.textAlign,M.textVerticalAlign=T&&T.textVerticalAlign||"middle",null!=m&&c+M.lineHeight>m)return{lines:[],width:0,height:0};M.textWidth=y(M.text,D);var O=T.textWidth,P=null==O||"auto"===O;if("string"===typeof O&&"%"===O.charAt(O.length-1))M.percentWidth=O,d.push(M),O=0;else{if(P){O=M.textWidth;var L=T.textBackgroundColor,R=L&&L.image;R&&(R=r.findExistImage(R),r.isImageReady(R)&&(O=Math.max(O,R.width*A/R.height)))}var N=I?I[1]+I[3]:0;O+=N;var B=null!=v?v-w:null;null!=B&&B<O&&(!P||B<N?(M.text="",M.textWidth=O=0):(M.text=C(M.text,B-N,D,g.ellipsis,{minChar:g.minChar}),M.textWidth=y(M.text,D),O=M.textWidth+N))}w+=M.width=O,T&&(b=Math.max(b,M.lineHeight))}_.width=w,_.lineHeight=b,c+=b,h=Math.max(h,w)}n.outerWidth=n.width=l(e.textWidth,h),n.outerHeight=n.height=l(e.textHeight,c),f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(x=0;x<d.length;x++){M=d[x];var z=M.percentWidth;M.width=parseInt(z,10)/100*h}return n}function E(t,e,n){for(var i=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:n,text:s,isLineHolder:!s&&!i};if(o)a.push({tokens:[l]});else{var u=(a[a.length-1]||(a[0]={tokens:[]})).tokens,c=u.length;1===c&&u[0].isLineHolder?u[0]=l:(s||!c||i)&&u.push(l)}}}function R(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&c(e)||t.textFont||t.font}v.measureText=function(t,e){var n=o();return n.font=e||g,n.measureText(t)},e.DEFAULT_FONT=g,e.$override=m,e.getWidth=y,e.getBoundingRect=x,e.adjustTextX=w,e.adjustTextY=S,e.calculateTextPosition=M,e.adjustTextPositionOnRect=T,e.truncateText=C,e.getLineHeight=k,e.measureText=O,e.parsePlainText=P,e.parseRichText=L,e.makeFont=R},e887:function(t,e,n){var i=n("6d8b"),r=i.each,a=n("e1fc"),o=n("8918"),s=n("625e"),l=n("e0d3"),u=n("2306"),c=n("f47d"),h=c.createTask,d=n("cccd"),f=l.makeInner(),p=d();function g(){this.group=new a,this.uid=o.getUID("viewChart"),this.renderTask=h({plan:x,reset:_}),this.renderTask.context={view:this}}g.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){y(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){y(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var v=g.prototype;function m(t,e,n){if(t&&(t.trigger(e,n),t.isGroup&&!u.isHighDownDispatcher(t)))for(var i=0,r=t.childCount();i<r;i++)m(t.childAt(i),e,n)}function y(t,e,n){var i=l.queryDataIndex(t,e),a=e&&null!=e.highlightKey?u.getHighlightDigit(e.highlightKey):null;null!=i?r(l.normalizeToArray(i),function(e){m(t.getItemGraphicEl(e),n,a)}):t.eachItemGraphicEl(function(t){m(t,n,a)})}function x(t){return p(t.model)}function _(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&f(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,n,i,r),b[l]}v.updateView=v.updateLayout=v.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},s.enableClassExtend(g,["dispose"]),s.enableClassManagement(g,{registerWhenExtend:!0}),g.markUpdateMethod=function(t,e){f(t).updateMethod=e};var b={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},w=g;t.exports=w},ea41:function(t,e,n){"use strict";var i=n("6ba1"),r=n.n(i);r.a},eb6b:function(t,e,n){var i=n("6d8b"),r=n("e0d3"),a=r.makeInner,o=n("cd33"),s=n("133d"),l=i.each,u=i.curry,c=a();function h(t,e,n){var r=t.currTrigger,a=[t.x,t.y],o=t,c=t.dispatchAction||i.bind(n.dispatchAction,n),h=e.getComponent("axisPointer").coordSysAxesInfo;if(h){b(a)&&(a=s({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var f=b(a),w=o.axesInfo,S=h.axesInfo,M="leave"===r||b(a),T={},C={},I={list:[],map:{}},D={showPointer:u(p,C),showTooltip:u(g,I)};l(h.coordSysMap,function(t,e){var n=f||t.containPoint(a);l(h.coordSysAxesInfo[e],function(t,e){var i=t.axis,r=x(w,t);if(!M&&n&&(!w||r)){var o=r&&r.value;null!=o||f||(o=i.pointToData(a)),null!=o&&d(t,o,D,!1,T)}})});var A={};return l(S,function(t,e){var n=t.linkGroup;n&&!C[e]&&l(n.axesInfo,function(e,i){var r=C[i];if(e!==t&&r){var a=r.value;n.mapper&&(a=t.axis.scale.parse(n.mapper(a,_(e),_(t)))),A[t.key]=a}})}),l(A,function(t,e){d(S[e],t,D,!0,T)}),v(C,S,T),m(I,a,t,c),y(S,c,n),T}}function d(t,e,n,r,a){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e))if(t.involveSeries){var s=f(e,t),l=s.payloadBatch,u=s.snapToValue;l[0]&&null==a.seriesIndex&&i.extend(a,l[0]),!r&&t.snap&&o.containData(u)&&null!=u&&(e=u),n.showPointer(t,e,l,a),n.showTooltip(t,s,u)}else n.showPointer(t,e)}function f(t,e){var n=e.axis,i=n.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return l(e.seriesModels,function(e,u){var c,h,d=e.getData().mapDimension(i,!0);if(e.getAxisTooltipData){var f=e.getAxisTooltipData(d,t,n);h=f.dataIndices,c=f.nestestValue}else{if(h=e.getData().indicesOfNearest(d[0],t,"category"===n.type?.5:null),!h.length)return;c=e.getData().get(d[0],h[0])}if(null!=c&&isFinite(c)){var p=t-c,g=Math.abs(p);g<=o&&((g<o||p>=0&&s<0)&&(o=g,s=p,r=c,a.length=0),l(h,function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:a,snapToValue:r}}function p(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function g(t,e,n,i){var r=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;if(e.triggerTooltip&&r.length){var u=e.coordSys.model,c=o.makeKey(u),h=t.map[c];h||(h=t.map[c]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get("label.precision"),formatter:l.get("label.formatter")},seriesDataIndices:r.slice()})}}function v(t,e,n){var i=n.axesInfo=[];l(e,function(e,n){var r=e.axisPointerModel.option,a=t[n];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function m(t,e,n,i){if(!b(e)&&t.list.length){var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}else i({type:"hideTip"})}function y(t,e,n){var r=n.getZr(),a="axisPointerLastHighlights",o=c(r)[a]||{},s=c(r)[a]={};l(t,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&l(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;s[e]=t})});var u=[],h=[];i.each(o,function(t,e){!s[e]&&h.push(t)}),i.each(s,function(t,e){!o[e]&&u.push(t)}),h.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:h}),u.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:u})}function x(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function _(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function b(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}t.exports=h},ec02:function(t,e,n){var i=n("6d8b"),r=n("84ce"),a=function(t,e,n,i,a){r.call(this,t,e,n),this.type=i||"value",this.position=a||"bottom"};a.prototype={constructor:a,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},i.inherits(a,r);var o=a;t.exports=o},ec34:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=r.createHashMap,o=r.isString,s=r.isArray,l=r.each,u=(r.assert,n("3041")),c=u.parseXML,h=a(),d={registerMap:function(t,e,n){var i;return s(e)?i=e:e.svg?i=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),i=[{type:"geoJSON",source:e,specialAreas:n}]),l(i,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var n=f[e];n(t)}),h.set(t,i)},retrieveMap:function(t){return h.get(t)}},f={geoJSON:function(t){var e=t.source;t.geoJSON=o(e)?"undefined"!==typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=c(t.source)}};t.exports=d},ec6f:function(t,e,n){var i=n("6d8b"),r=i.createHashMap,a=i.isTypedArray,o=n("625e"),s=o.enableClassCheck,l=n("93d0"),u=l.SOURCE_FORMAT_ORIGINAL,c=l.SERIES_LAYOUT_BY_COLUMN,h=l.SOURCE_FORMAT_UNKNOWN,d=l.SOURCE_FORMAT_TYPED_ARRAY,f=l.SOURCE_FORMAT_KEYED_COLUMNS;function p(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===f?{}:[]),this.sourceFormat=t.sourceFormat||h,this.seriesLayoutBy=t.seriesLayoutBy||c,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&r(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}p.seriesDataToSource=function(t){return new p({data:t,sourceFormat:a(t)?d:u,fromDataset:!1})},s(p);var g=p;t.exports=g},ed21:function(t,e,n){var i=n("2cf4"),r=i.devicePixelRatio,a=n("6d8b"),o=n("4942"),s=n("9850"),l=n("04f6"),u=n("5e68"),c=n("98b7"),h=n("0da8"),d=n("22d1"),f=1e5,p=314159,g=.01,v=.001;function m(t){return parseInt(t,10)}function y(t){return!!t&&(!!t.__builtin__||"function"===typeof t.resize&&"function"===typeof t.refresh)}var x=new s(0,0,0,0),_=new s(0,0,0,0);function b(t,e,n){return x.copy(t.getBoundingRect()),t.transform&&x.applyTransform(t.transform),_.width=e,_.height=n,!x.intersect(_)}function w(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}function S(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function M(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}var T=function(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=a.extend({},n||{}),this.dpr=n.devicePixelRatio||r,this._singleCanvas=i,this.root=t;var o=t.style;o&&(o["-webkit-tap-highlight-color"]="transparent",o["-webkit-user-select"]=o["user-select"]=o["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var s=this._zlevelList=[],l=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var c=t.width,h=t.height;null!=n.width&&(c=n.width),null!=n.height&&(h=n.height),this.dpr=n.devicePixelRatio||1,t.width=c*this.dpr,t.height=h*this.dpr,this._width=c,this._height=h;var d=new u(t,this,this.dpr);d.__builtin__=!0,d.initContext(),l[p]=d,d.zlevel=p,s.push(p),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var f=this._domRoot=M(this._width,this._height);t.appendChild(f)}this._hoverlayer=null,this._hoverElements=[]};T.prototype={constructor:T,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r=n[i],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===i?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return n.__from=t,t.__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=a.indexOf(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){l(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(f));var i={};n.ctx.save();for(var r=0;r<e;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,n,!0,i))):(t.splice(r,1),o.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(f)},_paintList:function(t,e,n){if(this._redrawId===n){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var r=this;c(function(){r._paintList(t,e,n)})}}},_compositeManually:function(){var t=this.getLayer(p).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer(function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i],o=this._layers[r];o.__builtin__&&o!==this._hoverlayer&&(o.__dirty||e)&&n.push(o)}for(var s=!0,l=0;l<n.length;l++){o=n[l];var u=o.ctx,c={};u.save();var h=e?o.__startIndex:o.__drawIndex,f=!e&&o.incremental&&Date.now,p=f&&Date.now(),g=o.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(o.__startIndex===o.__endIndex)o.clear(!1,g);else if(h===o.__startIndex){var v=t[h];v.incremental&&v.notClear&&!e||o.clear(!1,g)}-1===h&&(console.error("For some unknown reason. drawIndex is -1"),h=o.__startIndex);for(var m=h;m<o.__endIndex;m++){var y=t[m];if(this._doPaintEl(y,o,e,c),y.__dirty=y.__dirtyText=!1,f){var x=Date.now()-p;if(x>15)break}}o.__drawIndex=m,o.__drawIndex<o.__endIndex&&(s=!1),c.prevElClipPaths&&u.restore(),u.restore()}return d.wxa&&a.each(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),s},_doPaintEl:function(t,e,n,i){var r=e.ctx,a=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!a||a[0]||a[3])&&(!t.culling||!b(t,this._width,this._height))){var o=t.__clipPaths,s=i.prevElClipPaths;s&&!w(o,s)||(s&&(r.restore(),i.prevElClipPaths=null,i.prevEl=null),o&&(r.save(),S(o,r),i.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=p);var n=this._layers[t];return n||(n=new u("zr_"+t,this,this.dpr),n.zlevel=t,n.__builtin__=!0,this._layerConfig[t]&&a.merge(n,this._layerConfig[t],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,a=null,s=-1,l=this._domRoot;if(n[t])o("ZLevel "+t+" has been used already");else if(y(e)){if(r>0&&t>i[0]){for(s=0;s<r-1;s++)if(i[s]<t&&i[s+1]>t)break;a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var u=a.dom;u.nextSibling?l.insertBefore(e.dom,u.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom)}else o("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++){var i=t[n];if(i.zlevel!==t[n-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var r=null,a=0;for(n=0;n<t.length;n++){i=t[n];var s,l=i.zlevel;i.incremental?(s=this.getLayer(l+v,this._needsManuallyCompositing),s.incremental=!0,a=1):s=this.getLayer(l+(a>0?g:0),this._needsManuallyCompositing),s.__builtin__||o("ZLevel "+l+" has been used by unkown layer "+s.id),s!==r&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.incremental?s.__drawIndex=-1:s.__drawIndex=n,e(n),r=s),i.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=n))}e(n),this.eachBuiltinLayer(function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?a.merge(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+g){var o=this._layers[r];a.merge(o,n[t],!0)}}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(a.indexOf(n,t),1))},resize:function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||e!==this._height){for(var r in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);a.each(this._progressiveLayers,function(n){n.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(p).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[p].dom;var e=new u("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[i]||m(s[n])||m(o.style[n]))-(m(s[r])||0)-(m(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,u=a.hasStroke()?a.lineWidth:0,c=Math.max(u/2,-s+o),d=Math.max(u/2,s+o),f=Math.max(u/2,-l+o),p=Math.max(u/2,l+o),g=r.width+c+d,v=r.height+f+p;n.width=g*e,n.height=v*e,i.scale(e,e),i.clearRect(0,0,g,v),i.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[c-r.x,f-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var y=h,x=new y({style:{x:0,y:0,image:n}});return null!=m.position&&(x.position=t.position=m.position),null!=m.rotation&&(x.rotation=t.rotation=m.rotation),null!=m.scale&&(x.scale=t.scale=m.scale),x}};var C=T;t.exports=C},eda2:function(t,e,n){var i=n("6d8b"),r=n("e86a"),a=n("3842");function o(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function s(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var l=i.normalizeCssArray,u=/([&<>"'])/g,c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function h(t){return null==t?"":(t+"").replace(u,function(t,e){return c[e]})}var d=["a","b","c","d","e","f","g"],f=function(t,e){return"{"+t+(null==e?"":e)+"}"};function p(t,e,n){i.isArray(e)||(e=[e]);var r=e.length;if(!r)return"";for(var a=e[0].$vars||[],o=0;o<a.length;o++){var s=d[o];t=t.replace(f(s),f(s,0))}for(var l=0;l<r;l++)for(var u=0;u<a.length;u++){var c=e[l][a[u]];t=t.replace(f(d[u],l),n?h(c):c)}return t}function g(t,e,n){return i.each(e,function(e,i){t=t.replace("{"+i+"}",n?h(e):e)}),t}function v(t,e){t=i.isString(t)?{color:t,extraCssText:e}:t||{};var n=t.color,r=t.type,a=(e=t.extraCssText,t.renderMode||"html"),o=t.markerId||"X";return n?"html"===a?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+h(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+h(n)+";"+(e||"")+'"></span>':{renderMode:a,content:"{marker"+o+"|}  ",style:{color:n}}:""}function m(t,e){return t+="","0000".substr(0,e-t.length)+t}function y(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=a.parseDate(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),s=i["get"+r+"Month"]()+1,l=i["get"+r+"Date"](),u=i["get"+r+"Hours"](),c=i["get"+r+"Minutes"](),h=i["get"+r+"Seconds"](),d=i["get"+r+"Milliseconds"]();return t=t.replace("MM",m(s,2)).replace("M",s).replace("yyyy",o).replace("yy",o%100).replace("dd",m(l,2)).replace("d",l).replace("hh",m(u,2)).replace("h",u).replace("mm",m(c,2)).replace("m",c).replace("ss",m(h,2)).replace("s",h).replace("SSS",m(d,3)),t}function x(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}var _=r.truncateText;function b(t){return r.getBoundingRect(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function w(t,e,n,i,a,o,s,l){return r.getBoundingRect(t,e,n,i,a,l,o,s)}e.addCommas=o,e.toCamelCase=s,e.normalizeCssArray=l,e.encodeHTML=h,e.formatTpl=p,e.formatTplSimple=g,e.getTooltipMarker=v,e.formatTime=y,e.capitalFirst=x,e.truncateText=_,e.getTextBoundingRect=b,e.getTextRect=w},ee1a:function(t,e,n){var i=n("6d8b"),r=i.each,a=i.isString;function o(t,e,n){n=n||{};var i,o,s,l,u=n.byIndex,c=n.stackedCoordDimension,h=!(!t||!t.get("stack"));if(r(e,function(t,n){a(t)&&(e[n]=t={name:t}),h&&!t.isExtraCoord&&(u||i||!t.ordinalMeta||(i=t),o||"ordinal"===t.type||"time"===t.type||c&&c!==t.coordDim||(o=t))}),!o||u||i||(u=!0),o){s="__\0ecstackresult",l="__\0ecstackedover",i&&(i.createInvertedIndices=!0);var d=o.coordDim,f=o.type,p=0;r(e,function(t){t.coordDim===d&&p++}),e.push({name:s,coordDim:d,coordDimIndex:p,type:f,isExtraCoord:!0,isCalculationCoord:!0}),p++,e.push({name:l,coordDim:l,coordDimIndex:p,type:f,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:o&&o.name,stackedByDimension:i&&i.name,isStackedByIndex:u,stackedOverDimension:l,stackResultDimension:s}}function s(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function l(t,e){return s(t,e)?t.getCalculationInfo("stackResultDimension"):e}e.enableDataStack=o,e.isDimensionStacked=s,e.getStackedDimension=l},ee84:function(t,e,n){var i=n("20c8"),r=n("401b"),a=r.applyTransform,o=i.CMD,s=[[],[],[]],l=Math.sqrt,u=Math.atan2;function c(t,e){var n,i,r,c,h,d=t.data,f=o.M,p=o.C,g=o.L,v=o.R,m=o.A,y=o.Q;for(r=0,c=0;r<d.length;){switch(n=d[r++],c=r,i=0,n){case f:i=1;break;case g:i=1;break;case p:i=3;break;case y:i=2;break;case m:var x=e[4],_=e[5],b=l(e[0]*e[0]+e[1]*e[1]),w=l(e[2]*e[2]+e[3]*e[3]),S=u(-e[1]/w,e[0]/b);d[r]*=b,d[r++]+=x,d[r]*=w,d[r++]+=_,d[r++]*=b,d[r++]*=w,d[r++]+=S,d[r++]+=S,r+=2,c=r;break;case v:M[0]=d[r++],M[1]=d[r++],a(M,M,e),d[c++]=M[0],d[c++]=M[1],M[0]+=d[r++],M[1]+=d[r++],a(M,M,e),d[c++]=M[0],d[c++]=M[1]}for(h=0;h<i;h++){var M=s[h];M[0]=d[r++],M[1]=d[r++],a(M,M,e),d[c++]=M[0],d[c++]=M[1]}}}t.exports=c},ef97:function(t,e,n){var i=n("3eba");n("217b"),n("f17f");var r=n("7f96"),a=n("87c3"),o=n("fdde");n("01ed"),i.registerVisual(r("line","circle","line")),i.registerLayout(a("line")),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,o("line"))},f123:function(t,e,n){var i=n("9f82"),r=i.prepareDataCoordInfo,a=i.getStackedOnPoint;function o(t,e){var n=[];return e.diff(t).add(function(t){n.push({cmd:"+",idx:t})}).update(function(t,e){n.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){n.push({cmd:"-",idx:t})}).execute(),n}function s(t,e,n,i,s,l,u,c){for(var h=o(t,e),d=[],f=[],p=[],g=[],v=[],m=[],y=[],x=r(s,e,u),_=r(l,t,c),b=0;b<h.length;b++){var w=h[b],S=!0;switch(w.cmd){case"=":var M=t.getItemLayout(w.idx),T=e.getItemLayout(w.idx1);(isNaN(M[0])||isNaN(M[1]))&&(M=T.slice()),d.push(M),f.push(T),p.push(n[w.idx]),g.push(i[w.idx1]),y.push(e.getRawIndex(w.idx1));break;case"+":var C=w.idx;d.push(s.dataToPoint([e.get(x.dataDimsForPoint[0],C),e.get(x.dataDimsForPoint[1],C)])),f.push(e.getItemLayout(C).slice()),p.push(a(x,s,e,C)),g.push(i[C]),y.push(e.getRawIndex(C));break;case"-":C=w.idx;var I=t.getRawIndex(C);I!==C?(d.push(t.getItemLayout(C)),f.push(l.dataToPoint([t.get(_.dataDimsForPoint[0],C),t.get(_.dataDimsForPoint[1],C)])),p.push(n[C]),g.push(a(_,l,t,C)),y.push(I)):S=!1}S&&(v.push(w),m.push(m.length))}m.sort(function(t,e){return y[t]-y[e]});var D=[],A=[],k=[],O=[],P=[];for(b=0;b<m.length;b++){C=m[b];D[b]=d[C],A[b]=f[C],k[b]=p[C],O[b]=g[C],P[b]=v[C]}return{current:D,next:A,stackedOnCurrent:k,stackedOnNext:O,status:P}}t.exports=s},f17f:function(t,e,n){var i=n("4e08"),r=(i.__DEV__,n("6d8b")),a=n("f706"),o=n("1418"),s=n("f123"),l=n("2306"),u=n("e0d3"),c=n("d4d1"),h=c.Polyline,d=c.Polygon,f=n("e887"),p=n("9f82"),g=p.prepareDataCoordInfo,v=p.getStackedOnPoint,m=n("b0af"),y=m.createGridClipPath,x=m.createPolarClipPath;function _(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function b(t){return"number"===typeof t?t:t?.5:0}function w(t,e,n){if(!n.valueDim)return[];for(var i=[],r=0,a=e.count();r<a;r++)i.push(v(n,t,e,r));return i}function S(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,a=[],o=0;o<t.length-1;o++){var s=t[o+1],l=t[o];a.push(l);var u=[];switch(n){case"end":u[r]=s[r],u[1-r]=l[1-r],a.push(u);break;case"middle":var c=(l[r]+s[r])/2,h=[];u[r]=h[r]=c,u[1-r]=l[1-r],h[1-r]=s[1-r],a.push(u),a.push(h);break;default:u[r]=l[r],u[1-r]=s[1-r],a.push(u)}}return t[o]&&a.push(t[o]),a}function M(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()&&"cartesian2d"===e.type){for(var i,a,o=n.length-1;o>=0;o--){var s=n[o].dimension,u=t.dimensions[s],c=t.getDimensionInfo(u);if(i=c&&c.coordDim,"x"===i||"y"===i){a=n[o];break}}if(a){var h=e.getAxis(i),d=r.map(a.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),f=d.length,p=a.outerColors.slice();f&&d[0].coord>d[f-1].coord&&(d.reverse(),p.reverse());var g=10,v=d[0].coord-g,m=d[f-1].coord+g,y=m-v;if(y<.001)return"transparent";r.each(d,function(t){t.offset=(t.coord-v)/y}),d.push({offset:f?d[f-1].offset:.5,color:p[1]||"transparent"}),d.unshift({offset:f?d[0].offset:.5,color:p[0]||"transparent"});var x=new l.LinearGradient(0,0,0,0,d,!0);return x[i]=v,x[i+"2"]=m,x}}}function T(t,e,n){var i=t.get("showAllSymbol"),a="auto"===i;if(!i||a){var o=n.getAxesByScale("ordinal")[0];if(o&&(!a||!C(o,e))){var s=e.mapDimension(o.dim),l={};return r.each(o.getViewLabels(),function(t){l[t.tickValue]=1}),function(t){return!l.hasOwnProperty(e.get(s,t))}}}}function C(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),a=Math.max(1,Math.round(r/5)),s=0;s<r;s+=a)if(1.5*o.getSymbolSize(e,s)[t.isHorizontal()?1:0]>i)return!1;return!0}function I(t,e,n){if("cartesian2d"===t.type){var i=t.getBaseAxis().isHorizontal(),r=y(t,e,n);if(!n.get("clip",!0)){var a=r.shape,o=Math.max(a.width,a.height);i?(a.y-=o,a.height+=2*o):(a.x-=o,a.width+=2*o)}return r}return x(t,e,n)}var D=f.extend({type:"line",init:function(){var t=new l.Group,e=new a;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i=t.coordinateSystem,a=this.group,o=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=o.mapArray(o.getItemLayout),c="polar"===i.type,h=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,v=this._lineGroup,m=t.get("animation"),y=!l.isEmpty(),x=l.get("origin"),C=g(i,o,x),D=w(i,o,C),A=t.get("showSymbol"),k=A&&!c&&T(t,o,i),O=this._data;O&&O.eachItemGraphicEl(function(t,e){t.__temp&&(a.remove(t),O.setItemGraphicEl(e,null))}),A||d.remove(),a.add(v);var P,L=!c&&t.get("step");i&&i.getArea&&t.get("clip",!0)&&(P=i.getArea(),null!=P.width?(P.x-=.1,P.y-=.1,P.width+=.2,P.height+=.2):P.r0&&(P.r0-=.5,P.r1+=.5)),this._clipShapeForSymbol=P,f&&h.type===i.type&&L===this._step?(y&&!p?p=this._newPolygon(u,D,i,m):p&&!y&&(v.remove(p),p=this._polygon=null),v.setClipPath(I(i,!1,t)),A&&d.updateData(o,{isIgnore:k,clipShape:P}),o.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),_(this._stackedOnPoints,D)&&_(this._points,u)||(m?this._updateAnimation(o,D,i,n,L,x):(L&&(u=S(u,i,L),D=S(D,i,L)),f.setShape({points:u}),p&&p.setShape({points:u,stackedOnPoints:D})))):(A&&d.updateData(o,{isIgnore:k,clipShape:P}),L&&(u=S(u,i,L),D=S(D,i,L)),f=this._newPolyline(u,i,m),y&&(p=this._newPolygon(u,D,i,m)),v.setClipPath(I(i,!0,t)));var E=M(o,i)||o.getVisual("color");f.useStyle(r.defaults(s.getLineStyle(),{fill:"none",stroke:E,lineJoin:"bevel"}));var R=t.get("smooth");if(R=b(t.get("smooth")),f.setShape({smooth:R,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var N=o.getCalculationInfo("stackedOnSeries"),B=0;p.useStyle(r.defaults(l.getAreaStyle(),{fill:E,opacity:.7,lineJoin:"bevel"})),N&&(B=b(N.get("smooth"))),p.setShape({smooth:R,stackedOnSmooth:B,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=o,this._coordSys=i,this._stackedOnPoints=D,this._points=u,this._step=L,this._valueOrigin=x},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),a=u.queryDataIndex(r,i);if(!(a instanceof Array)&&null!=a&&a>=0){var s=r.getItemGraphicEl(a);if(!s){var l=r.getItemLayout(a);if(!l)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(l[0],l[1]))return;s=new o(r,a),s.position=l,s.setZ(t.get("zlevel"),t.get("z")),s.ignore=isNaN(l[0])||isNaN(l[1]),s.__temp=!0,r.setItemGraphicEl(a,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else f.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),a=u.queryDataIndex(r,i);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay())}else f.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new h({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new d({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n,n},_updateAnimation:function(t,e,n,i,r,a){var o=this._polyline,u=this._polygon,c=t.hostModel,h=s(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,a),d=h.current,f=h.stackedOnCurrent,p=h.next,g=h.stackedOnNext;r&&(d=S(h.current,n,r),f=S(h.stackedOnCurrent,n,r),p=S(h.next,n,r),g=S(h.stackedOnNext,n,r)),o.shape.__points=h.current,o.shape.points=d,l.updateProps(o,{shape:{points:p}},c),u&&(u.setShape({points:d,stackedOnPoints:f}),l.updateProps(u,{shape:{points:p,stackedOnPoints:g}},c));for(var v=[],m=h.status,y=0;y<m.length;y++){var x=m[y].cmd;if("="===x){var _=t.getItemGraphicEl(m[y].idx1);_&&v.push({el:_,ptIdx:y})}}o.animators&&o.animators.length&&o.animators[0].during(function(){for(var t=0;t<v.length;t++){var e=v[t].el;e.attr("position",o.shape.__points[v[t].ptIdx])}})},remove:function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});t.exports=D},f219:function(t,e){var n="#eee",i=function(){return{axisLine:{lineStyle:{color:n}},axisTick:{lineStyle:{color:n}},axisLabel:{textStyle:{color:n}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:n}}}},r=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],a={color:r,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:n},crossStyle:{color:n},label:{color:"#000"}}},legend:{textStyle:{color:n}},textStyle:{color:n},title:{textStyle:{color:n}},toolbox:{iconStyle:{normal:{borderColor:n}}},dataZoom:{textStyle:{color:n}},visualMap:{textStyle:{color:n}},timeline:{lineStyle:{color:n},itemStyle:{normal:{color:r[1]}},label:{normal:{textStyle:{color:n}}},controlStyle:{normal:{color:n,borderColor:n}}},timeAxis:i(),logAxis:i(),valueAxis:i(),categoryAxis:i(),line:{symbol:"circle"},graph:{color:r},gauge:{title:{textStyle:{color:n}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};a.categoryAxis.splitLine.show=!1;var o=a;t.exports=o},f273:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("fab22"),o=n("6679"),s=n("0156"),l=["axisLine","axisTickLabel","axisName"],u=["splitArea","splitLine","minorSplitLine"],c=o.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,o){this.group.removeAll();var h=this._axisGroup;if(this._axisGroup=new r.Group,this.group.add(this._axisGroup),t.get("show")){var d=t.getCoordSysModel(),f=s.layout(d,t),p=new a(t,f);i.each(l,p.add,p),this._axisGroup.add(p.getGroup()),i.each(u,function(e){t.get(e+".show")&&this["_"+e](t,d)},this),r.groupTransition(h,this._axisGroup,t),c.superCall(this,"render",t,e,n,o)}},remove:function(){this._splitAreaColors=null},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var a=t.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color");s=i.isArray(s)?s:[s];for(var l=e.coordinateSystem.getRect(),u=n.isHorizontal(),c=0,h=n.getTicksCoords({tickModel:a}),d=[],f=[],p=o.getLineStyle(),g=0;g<h.length;g++){var v=n.toGlobalCoord(h[g].coord);u?(d[0]=v,d[1]=l.y,f[0]=v,f[1]=l.y+l.height):(d[0]=l.x,d[1]=v,f[0]=l.x+l.width,f[1]=v);var m=c++%s.length,y=h[g].tickValue;this._axisGroup.add(new r.Line({anid:null!=y?"line_"+h[g].tickValue:null,subPixelOptimize:!0,shape:{x1:d[0],y1:d[1],x2:f[0],y2:f[1]},style:i.defaults({stroke:s[m]},p),silent:!0}))}}},_minorSplitLine:function(t,e){var n=t.axis,i=t.getModel("minorSplitLine"),a=i.getModel("lineStyle"),o=e.coordinateSystem.getRect(),s=n.isHorizontal(),l=n.getMinorTicksCoords();if(l.length)for(var u=[],c=[],h=a.getLineStyle(),d=0;d<l.length;d++)for(var f=0;f<l[d].length;f++){var p=n.toGlobalCoord(l[d][f].coord);s?(u[0]=p,u[1]=o.y,c[0]=p,c[1]=o.y+o.height):(u[0]=o.x,u[1]=p,c[0]=o.x+o.width,c[1]=p),this._axisGroup.add(new r.Line({anid:"minor_line_"+l[d][f].tickValue,subPixelOptimize:!0,shape:{x1:u[0],y1:u[1],x2:c[0],y2:c[1]},style:h,silent:!0}))}},_splitArea:function(t,e){var n=t.axis;if(!n.scale.isBlank()){var a=t.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),l=e.coordinateSystem.getRect(),u=n.getTicksCoords({tickModel:a,clamp:!0});if(u.length){var c=s.length,h=this._splitAreaColors,d=i.createHashMap(),f=0;if(h)for(var p=0;p<u.length;p++){var g=h.get(u[p].tickValue);if(null!=g){f=(g+(c-1)*p)%c;break}}var v=n.toGlobalCoord(u[0].coord),m=o.getAreaStyle();s=i.isArray(s)?s:[s];for(p=1;p<u.length;p++){var y,x,_,b,w=n.toGlobalCoord(u[p].coord);n.isHorizontal()?(y=v,x=l.y,_=w-y,b=l.height,v=y+_):(y=l.x,x=v,_=l.width,b=w-x,v=x+b);var S=u[p-1].tickValue;null!=S&&d.set(S,f),this._axisGroup.add(new r.Rect({anid:null!=S?"area_"+S:null,shape:{x:y,y:x,width:_,height:b},style:i.defaults({fill:s[f]},m),silent:!0})),f=(f+1)%c}this._splitAreaColors=d}}}});c.extend({type:"xAxis"}),c.extend({type:"yAxis"})},f279:function(t,e,n){var i=n("9850"),r=n("e263"),a=n("401b"),o=n("0655");function s(t,e,n){if(this.name=t,this.geometries=e,n)n=[n[0],n[1]];else{var i=this.getBoundingRect();n=[i.x+i.width/2,i.y+i.height/2]}this.center=n}s.prototype={constructor:s,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],o=[-e,-e],s=[],l=[],u=this.geometries,c=0;c<u.length;c++)if("polygon"===u[c].type){var h=u[c].exterior;r.fromPoints(h,s,l),a.min(n,n,s),a.max(o,o,l)}return 0===c&&(n[0]=n[1]=o[0]=o[1]=0),this._rect=new i(n[0],n[1],o[0]-n[0],o[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++)if("polygon"===n[i].type){var a=n[i].exterior,s=n[i].interiors;if(o.contain(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(o.contain(s[l]))continue t;return!0}}return!1},transformTo:function(t,e,n,r){var o=this.getBoundingRect(),s=o.width/o.height;n?r||(r=n/s):n=s*r;for(var l=new i(t,e,n,r),u=o.calculateTransform(l),c=this.geometries,h=0;h<c.length;h++)if("polygon"===c[h].type){for(var d=c[h].exterior,f=c[h].interiors,p=0;p<d.length;p++)a.applyTransform(d[p],d[p],u);for(var g=0;g<(f?f.length:0);g++)for(p=0;p<f[g].length;p++)a.applyTransform(f[g][p],f[g][p],u)}o=this._rect,o.copy(l),this.center=[o.x+o.width/2,o.y+o.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new s(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var l=s;t.exports=l},f47d:function(t,e,n){var i=n("6d8b"),r=(i.assert,i.isArray),a=n("4e08");a.__DEV__;function o(t){return new s(t)}function s(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}var l=s.prototype;l.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var a=this.context;a.data=a.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,s=f(this._modBy),l=this._modDataCount||0,u=f(t&&t.modBy),d=t&&t.modDataCount||0;function f(t){return!(t>=1)&&(t=1),t}s===u&&l===d||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=h(this,i)),this._modBy=u,this._modDataCount=d;var p=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var g=this._dueIndex,v=Math.min(null!=p?this._dueIndex+p:1/0,this._dueEnd);if(!i&&(o||g<v)){var m=this._progress;if(r(m))for(var y=0;y<m.length;y++)c(this,m[y],g,v,u,d);else c(this,m,g,v,u,d)}this._dueIndex=v;var x=null!=this._settedOutputEnd?this._settedOutputEnd:v;this._outputDueEnd=x}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var u=function(){var t,e,n,i,r,a={reset:function(l,u,c,h){e=l,t=u,n=c,i=h,r=Math.ceil(i/n),a.next=n>1&&i>0?s:o}};return a;function o(){return e<t?e++:null}function s(){var a=e%r*n+Math.ceil(e/r),o=e>=t?null:a<i?a:e;return e++,o}}();function c(t,e,n,i,r,a){u.reset(n,i,r,a),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:u.next},t.context)}function h(t,e){var n,i;t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null,!e&&t._reset&&(n=t._reset(t.context),n&&n.progress&&(i=n.forceFirstProgress,n=n.progress),r(n)&&!n.length&&(n=null)),t._progress=n,t._modBy=t._modDataCount=null;var a=t._downstream;return a&&a.dirty(),i}l.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},l.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},l.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},l.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},l.getUpstream=function(){return this._upstream},l.getDownstream=function(){return this._downstream},l.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},e.createTask=o},f706:function(t,e,n){var i=n("2306"),r=n("1418"),a=n("6d8b"),o=a.isObject;function s(t){this.group=new i.Group,this._symbolCtor=t||r}var l=s.prototype;function u(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function c(t){return null==t||o(t)||(t={isIgnore:t}),t||{}}function h(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}l.updateData=function(t,e){e=c(e);var n=this.group,r=t.hostModel,a=this._data,o=this._symbolCtor,s=h(t);a||n.removeAll(),t.diff(a).add(function(i){var r=t.getItemLayout(i);if(u(t,r,i,e)){var a=new o(t,i,s);a.attr("position",r),t.setItemGraphicEl(i,a),n.add(a)}}).update(function(l,c){var h=a.getItemGraphicEl(c),d=t.getItemLayout(l);u(t,d,l,e)?(h?(h.updateData(t,l,s),i.updateProps(h,{position:d},r)):(h=new o(t,l),h.attr("position",d)),n.add(h),t.setItemGraphicEl(l,h)):n.remove(h)}).remove(function(t){var e=a.getItemGraphicEl(t);e&&e.fadeOut(function(){n.remove(e)})}).execute(),this._data=t},l.isPersistent=function(){return!0},l.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,n){var i=t.getItemLayout(n);e.attr("position",i)})},l.incrementalPrepareUpdate=function(t){this._seriesScope=h(t),this._data=null,this.group.removeAll()},l.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}n=c(n);for(var r=t.start;r<t.end;r++){var a=e.getItemLayout(r);if(u(e,a,r,n)){var o=new this._symbolCtor(e,r,this._seriesScope);o.traverse(i),o.attr("position",a),this.group.add(o),e.setItemGraphicEl(r,o)}}},l.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var d=s;t.exports=d},f934:function(t,e,n){var i=n("6d8b"),r=n("9850"),a=n("3842"),o=a.parsePercent,s=n("eda2"),l=i.each,u=["left","right","top","bottom","width","height"],c=[["width","left","right"],["height","top","bottom"]];function h(t,e,n,i,r){var a=0,o=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,u){var c,h,d=l.position,f=l.getBoundingRect(),p=e.childAt(u+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);c=a+v,c>i||l.newline?(a=0,c=v,o+=s+n,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);h=o+m,h>r||l.newline?(a+=s+n,o=0,h=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=c+n:o=h+n)})}var d=h,f=i.curry(h,"vertical"),p=i.curry(h,"horizontal");function g(t,e,n){var i=e.width,r=e.height,a=o(t.x,i),l=o(t.y,r),u=o(t.x2,i),c=o(t.y2,r);return(isNaN(a)||isNaN(parseFloat(t.x)))&&(a=0),(isNaN(u)||isNaN(parseFloat(t.x2)))&&(u=i),(isNaN(l)||isNaN(parseFloat(t.y)))&&(l=0),(isNaN(c)||isNaN(parseFloat(t.y2)))&&(c=r),n=s.normalizeCssArray(n||0),{width:Math.max(u-a-n[1]-n[3],0),height:Math.max(c-l-n[0]-n[2],0)}}function v(t,e,n){n=s.normalizeCssArray(n||0);var i=e.width,a=e.height,l=o(t.left,i),u=o(t.top,a),c=o(t.right,i),h=o(t.bottom,a),d=o(t.width,i),f=o(t.height,a),p=n[2]+n[0],g=n[1]+n[3],v=t.aspect;switch(isNaN(d)&&(d=i-c-g-l),isNaN(f)&&(f=a-h-p-u),null!=v&&(isNaN(d)&&isNaN(f)&&(v>i/a?d=.8*i:f=.8*a),isNaN(d)&&(d=v*f),isNaN(f)&&(f=d/v)),isNaN(l)&&(l=i-c-d-g),isNaN(u)&&(u=a-h-f-p),t.left||t.right){case"center":l=i/2-d/2-n[3];break;case"right":l=i-d-g;break}switch(t.top||t.bottom){case"middle":case"center":u=a/2-f/2-n[0];break;case"bottom":u=a-f-p;break}l=l||0,u=u||0,isNaN(d)&&(d=i-g-l-(c||0)),isNaN(f)&&(f=a-p-u-(h||0));var m=new r(l+n[3],u+n[0],d,f);return m.margin=n,m}function m(t,e,n,a,o){var s=!o||!o.hv||o.hv[0],l=!o||!o.hv||o.hv[1],u=o&&o.boundingMode||"all";if(s||l){var c;if("raw"===u)c="group"===t.type?new r(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(c=t.getBoundingRect(),t.needLocalTransform()){var h=t.getLocalTransform();c=c.clone(),c.applyTransform(h)}e=v(i.defaults({width:c.width,height:c.height},e),n,a);var d=t.position,f=s?e.x-c.x:0,p=l?e.y-c.y:0;t.attr("position","raw"===u?[f,p]:[d[0]+f,d[1]+p])}}function y(t,e){return null!=t[c[e][0]]||null!=t[c[e][1]]&&null!=t[c[e][2]]}function x(t,e,n){!i.isObject(n)&&(n={});var r=n.ignoreSize;!i.isArray(r)&&(r=[r,r]);var a=s(c[0],0),o=s(c[1],1);function s(n,i){var a={},o=0,s={},c=0,d=2;if(l(n,function(e){s[e]=t[e]}),l(n,function(t){u(e,t)&&(a[t]=s[t]=e[t]),h(a,t)&&o++,h(s,t)&&c++}),r[i])return h(e,n[1])?s[n[2]]=null:h(e,n[2])&&(s[n[1]]=null),s;if(c!==d&&o){if(o>=d)return a;for(var f=0;f<n.length;f++){var p=n[f];if(!u(a,p)&&u(t,p)){a[p]=t[p];break}}return a}return s}function u(t,e){return t.hasOwnProperty(e)}function h(t,e){return null!=t[e]&&"auto"!==t[e]}function d(t,e,n){l(t,function(t){e[t]=n[t]})}d(c[0],t,a),d(c[1],t,o)}function _(t){return b({},t)}function b(t,e){return e&&t&&l(u,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t}e.LOCATION_PARAMS=u,e.HV_NAMES=c,e.box=d,e.vbox=f,e.hbox=p,e.getAvailableSize=g,e.getLayoutRect=v,e.positionElement=m,e.sizeCalculable=y,e.mergeLayoutParam=x,e.getLayoutParams=_,e.copyLayoutParams=b},fab22:function(t,e,n){var i=n("6d8b"),r=i.retrieve,a=i.defaults,o=i.extend,s=i.each,l=(i.map,n("eda2")),u=n("2306"),c=n("4319"),h=n("3842"),d=h.isRadianAroundZero,f=h.remRadian,p=n("a15a"),g=p.createSymbol,v=n("1687"),m=n("401b"),y=m.applyTransform,x=n("697e"),_=x.shouldShowAllLabels,b=Math.PI,w=function(t,e){this.opt=e,this.axisModel=t,a(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new u.Group;var n=new u.Group({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};w.prototype={constructor:w,hasBuilder:function(t){return!!S[t]},add:function(t){S[t].call(this)},getGroup:function(){return this.group}};var S={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],a=[n[1],0];i&&(y(r,r,i),y(a,a,i));var l=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new u.Line({anid:"line",subPixelOptimize:!0,shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1}));var c=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize"),d=e.get("axisLine.symbolOffset")||0;if("number"===typeof d&&(d=[d,d]),null!=c){"string"===typeof c&&(c=[c,c]),"string"!==typeof h&&"number"!==typeof h||(h=[h,h]);var f=h[0],p=h[1];s([{rotate:t.rotation+Math.PI/2,offset:d[0],r:0},{rotate:t.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,n){if("none"!==c[n]&&null!=c[n]){var i=g(c[n],-f/2,-p/2,f,p,l.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];i.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(i)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=L(this,t,e),i=R(this,t,e);D(t,i,n),E(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,n=r(t.axisName,e.get("name"));if(n){var i,a,s=e.get("nameLocation"),c=t.nameDirection,h=e.getModel("nameTextStyle"),d=e.get("nameGap")||0,f=this.axisModel.axis.getExtent(),p=f[0]>f[1]?-1:1,g=["start"===s?f[0]-p*d:"end"===s?f[1]+p*d:(f[0]+f[1])/2,O(s)?t.labelOffset+c*d:0],v=e.get("nameRotate");null!=v&&(v=v*b/180),O(s)?i=T(t.rotation,null!=v?v:t.rotation,c):(i=C(t,s,v||0,f),a=t.axisNameAvailableWidth,null!=a&&(a=Math.abs(a/Math.sin(i.rotation)),!isFinite(a)&&(a=null)));var m=h.getFont(),y=e.get("nameTruncate",!0)||{},x=y.ellipsis,_=r(t.nameTruncateMaxWidth,y.maxWidth,a),w=null!=x&&null!=_?l.truncateText(n,_,m,x,{minChar:2,placeholder:y.placeholder}):n,S=e.get("tooltip",!0),D=e.mainType,A={componentType:D,name:n,$vars:["name"]};A[D+"Index"]=e.componentIndex;var k=new u.Text({anid:"name",__fullText:n,__truncatedText:w,position:g,rotation:i.rotation,silent:I(e),z2:1,tooltip:S&&S.show?o({content:n,formatter:function(){return n},formatterParams:A},S):null});u.setTextStyle(k.style,h,{text:w,textFont:m,textFill:h.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:h.get("align")||i.textAlign,textVerticalAlign:h.get("verticalAlign")||i.textVerticalAlign}),e.get("triggerEvent")&&(k.eventData=M(e),k.eventData.targetType="axisName",k.eventData.name=n),this._dumbGroup.add(k),k.updateTransform(),this.group.add(k),k.decomposeTransform()}}},M=w.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},T=w.innerTextLayout=function(t,e,n){var i,r,a=f(e-t);return d(a)?(r=n>0?"top":"bottom",i="center"):d(a-b)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=a>0&&a<b?n>0?"right":"left":n>0?"left":"right"),{rotation:a,textAlign:i,textVerticalAlign:r}};function C(t,e,n,i){var r,a,o=f(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return d(o-b/2)?(a=l?"bottom":"top",r="center"):d(o-1.5*b)?(a=l?"top":"bottom",r="center"):(a="middle",r=o<1.5*b&&o>b/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}var I=w.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)};function D(t,e,n){if(!_(t.axis)){var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],n=n||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],c=n[1],h=n[n.length-1],d=n[n.length-2];!1===i?(A(a),A(u)):k(a,o)&&(i?(A(o),A(c)):(A(a),A(u))),!1===r?(A(s),A(h)):k(l,s)&&(r?(A(l),A(d)):(A(s),A(h)))}}function A(t){t&&(t.ignore=!0)}function k(t,e,n){var i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r){var a=v.identity([]);return v.rotate(a,a,-t.rotation),i.applyTransform(v.mul([],a,t.getLocalTransform())),r.applyTransform(v.mul([],a,e.getLocalTransform())),i.intersect(r)}}function O(t){return"middle"===t||"center"===t}function P(t,e,n,i,r){for(var a=[],o=[],s=[],l=0;l<t.length;l++){var c=t[l].coord;o[0]=c,o[1]=0,s[0]=c,s[1]=n,e&&(y(o,o,e),y(s,s,e));var h=new u.Line({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,silent:!0});a.push(h)}return a}function L(t,e,n){var i=e.axis,r=e.getModel("axisTick");if(r.get("show")&&!i.scale.isBlank()){for(var o=r.getModel("lineStyle"),s=n.tickDirection*r.get("length"),l=i.getTicksCoords(),u=P(l,t._transform,s,a(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),c=0;c<u.length;c++)t.group.add(u[c]);return u}}function E(t,e,n){var i=e.axis,r=e.getModel("minorTick");if(r.get("show")&&!i.scale.isBlank()){var o=i.getMinorTicksCoords();if(o.length)for(var s=r.getModel("lineStyle"),l=n.tickDirection*r.get("length"),u=a(s.getLineStyle(),a(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),c=0;c<o.length;c++)for(var h=P(o[c],t._transform,l,u,"minorticks_"+c),d=0;d<h.length;d++)t.group.add(h[d])}}function R(t,e,n){var i=e.axis,a=r(n.axisLabelShow,e.get("axisLabel.show"));if(a&&!i.scale.isBlank()){var o=e.getModel("axisLabel"),l=o.get("margin"),h=i.getViewLabels(),d=(r(n.labelRotate,o.get("rotate"))||0)*b/180,f=T(n.rotation,d,n.labelDirection),p=e.getCategories&&e.getCategories(!0),g=[],v=I(e),m=e.get("triggerEvent");return s(h,function(r,a){var s=r.tickValue,h=r.formattedLabel,d=r.rawLabel,y=o;p&&p[s]&&p[s].textStyle&&(y=new c(p[s].textStyle,o,e.ecModel));var x=y.getTextColor()||e.get("axisLine.lineStyle.color"),_=i.dataToCoord(s),b=[_,n.labelOffset+n.labelDirection*l],w=new u.Text({anid:"label_"+s,position:b,rotation:f.rotation,silent:v,z2:10});u.setTextStyle(w.style,y,{text:h,textAlign:y.getShallow("align",!0)||f.textAlign,textVerticalAlign:y.getShallow("verticalAlign",!0)||y.getShallow("baseline",!0)||f.textVerticalAlign,textFill:"function"===typeof x?x("category"===i.type?d:"value"===i.type?s+"":s,a):x}),m&&(w.eventData=M(e),w.eventData.targetType="axisLabel",w.eventData.value=d),t._dumbGroup.add(w),w.updateTransform(),g.push(w),t.group.add(w),w.decomposeTransform()}),g}}var N=w;t.exports=N},fb05:function(t,e,n){var i=n("6d8b"),r=i.each,a=i.isArray,o=i.isObject,s=n("26e1"),l=n("e0d3"),u=l.normalizeToArray;function c(t,e){e=e.split(",");for(var n=t,i=0;i<e.length;i++)if(n=n&&n[e[i]],null==n)break;return n}function h(t,e,n,i){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(i||null==a[e[o]])&&(a[e[o]]=n)}function d(t){r(f,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var f=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],p=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"];function g(t,e){s(t,e),t.series=u(t.series),r(t.series,function(t){if(o(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var n=c(t,"pointer.color");null!=n&&h(t,"itemStyle.color",n)}d(t)}}),t.dataRange&&(t.visualMap=t.dataRange),r(p,function(e){var n=t[e];n&&(a(n)||(n=[n]),r(n,function(t){d(t)}))})}t.exports=g},fd63:function(t,e,n){var i=n("42e5"),r=n("6d8b"),a=r.isFunction,o={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),r=(t.visualColorAccessPath||"itemStyle.color").split("."),o=t.get(r),s=!a(o)||o instanceof i?null:o;o&&!s||(o=t.getColorFromPalette(t.name,null,e.getSeriesCount())),n.setVisual("color",o);var l=(t.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),u=t.get(l);if(n.setVisual("borderColor",u),!e.isSeriesFiltered(t)){s&&n.each(function(e){n.setItemVisual(e,"color",s(t.getDataParams(e)))});var c=function(t,e){var n=t.getItemModel(e),i=n.get(r,!0),a=n.get(l,!0);null!=i&&t.setItemVisual(e,"color",i),null!=a&&t.setItemVisual(e,"borderColor",a)};return{dataEach:n.hasItemOption?c:null}}}};t.exports=o},fdde:function(t,e){var n={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},i=function(t,e){return Math.round(t.length/2)};function r(t){return{seriesType:t,modifyOutputEnd:!0,reset:function(t,e,r){var a=t.getData(),o=t.get("sampling"),s=t.coordinateSystem;if("cartesian2d"===s.type&&o){var l,u=s.getBaseAxis(),c=s.getOtherAxis(u),h=u.getExtent(),d=h[1]-h[0],f=Math.round(a.count()/d);if(f>1)"string"===typeof o?l=n[o]:"function"===typeof o&&(l=o),l&&t.setData(a.downSample(a.mapDimension(c.dim),1/f,l,i))}}}}t.exports=r},fe21:function(t,e,n){var i=n("e86a"),r=n("2306"),a=["textStyle","color"],o={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(a):null)},getFont:function(){return r.getFont({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return i.getBoundingRect(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}};t.exports=o},ff2e:function(t,e,n){var i=n("6d8b"),r=n("2306"),a=n("e86a"),o=n("eda2"),s=n("1687"),l=n("697e"),u=n("fab22");function c(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle(),e.fill=null):"shadow"===n&&(e=i.getAreaStyle(),e.stroke=null),e}function h(t,e,n,i,r){var s=n.get("value"),l=f(s,e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),u=n.getModel("label"),c=o.normalizeCssArray(u.get("padding")||0),h=u.getFont(),p=a.getBoundingRect(l,h),g=r.position,v=p.width+c[1]+c[3],m=p.height+c[0]+c[2],y=r.align;"right"===y&&(g[0]-=v),"center"===y&&(g[0]-=v/2);var x=r.verticalAlign;"bottom"===x&&(g[1]-=m),"middle"===x&&(g[1]-=m/2),d(g,v,m,i);var _=u.get("backgroundColor");_&&"auto"!==_||(_=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:v,height:m,r:u.get("borderRadius")},position:g.slice(),style:{text:l,textFont:h,textFill:u.getTextColor(),textPosition:"inside",textPadding:c,fill:_,stroke:u.get("borderColor")||"transparent",lineWidth:u.get("borderWidth")||0,shadowBlur:u.get("shadowBlur"),shadowColor:u.get("shadowColor"),shadowOffsetX:u.get("shadowOffsetX"),shadowOffsetY:u.get("shadowOffsetY")},z2:10}}function d(t,e,n,i){var r=i.getWidth(),a=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,a)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function f(t,e,n,r,a){t=e.scale.parse(t);var o=e.scale.getLabel(t,{precision:a.precision}),s=a.formatter;if(s){var u={value:l.getAxisRawValue(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};i.each(r,function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&u.seriesData.push(r)}),i.isString(s)?o=s.replace("{value}",o):i.isFunction(s)&&(o=s(u))}return o}function p(t,e,n){var i=s.create();return s.rotate(i,i,n.rotation),s.translate(i,i,n.position),r.applyTransform([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function g(t,e,n,i,r,a){var o=u.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),h(e,i,r,a,{position:p(i.axis,t,n),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function v(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}}function m(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}function y(t,e,n,i,r,a){return{cx:t,cy:e,r0:n,r:i,startAngle:r,endAngle:a,clockwise:!0}}e.buildElStyle=c,e.buildLabelElOption=h,e.getValueLabel=f,e.getTransformedPosition=p,e.buildCartesianSingleLabelElOption=g,e.makeLineShape=v,e.makeRectShape=m,e.makeSectorShape=y}}]);
//# sourceMappingURL=chunk-0c772a98.1afe5ff8.js.map