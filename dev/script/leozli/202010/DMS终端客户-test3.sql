create PROCEDURE [dbo].[pr_syn_dms_cube_to_bi]
as
begin 
	set xact_abort on
	SET NOCOUNT ON
	begin try
	declare @start_time datetime /*同步开始时间*/
	declare @end_time datetime/*同步截止时间*/
	set @end_time=DATEADD(MINUTE, -1, getdate())
	select @start_time=max(xx.update_time) from PP_MID.DBO.pp_user xx

	update mu set mu.user_name_cn=u.ch_name, mu.enable_flag=u.status,mu.update_time=@end_time
	from PP_MID.DBO.pp_user mu 
	left join wx_t_user u on u.user_id=mu.user_code 
	where u.xg_time between @start_time and @end_time

	insert into PP_MID.DBO.pp_user (user_name_cn,user_code,org_id,user_type,enable_flag,create_time,update_time)
	select u.ch_name, u.user_id, u.org_id,'distributor', u.status,@end_time,@end_time 
	from wx_t_user u left join wx_t_organization o on o.id=u.org_id
	where o.type=1 and o.status=1 and (u.type is null or u.type !='1') and o.id!=9
	and u.xz_time between @start_time and @end_time
	and not exists (select 1 from PP_MID.DBO.pp_user bu where bu.user_code=u.user_id)

	select @start_time=max(xx.update_time) from PP_MID.DBO.pp_dist_customer_inspections xx
	update bi set bi.enable_flag=(case when s.task_status='4' then 1 else 0 end),update_time= @end_time
	from PP_MID.DBO.pp_dist_customer_inspections bi 
	left join wx_task_sub s on bi.id=s.task_id LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
	where s.xg_sj between @start_time and @end_time

	insert into PP_MID.dbo.pp_dist_customer_inspections (id,customer_type,customer_id,inspections_time,
		business_type,enable_flag,dsr_code,create_time,update_time) 
	select s.task_id, 
	case when m.tmb_type_code='TT_2_FLEET_ZF' then 2 else 1 end customer_type,
	s.org_id, s.xg_sj, 
	case when m.tmb_type_code in ('TT_2_FLEET_ZF', 'TT_2_XD_CAI') then 2 else 1 end business_type,
	1 enable_flag, m.excute_user_id, @end_time , @end_time 
	from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
	left join wx_t_organization o on o.id=m.tenant_id
	where m.tmb_type_code IN ('TT_2_XD','TT_2_XD_CAI','TT_2_FLEET_ZF') --TT_2_PROJECT_CLIENT_VISIT
	and s.task_status='4' and m.tenant_id!=9 and o.status=1 and s.xg_sj between @start_time and @end_time
	and not exists (select 1 from PP_MID.dbo.pp_dist_customer_inspections bi where bi.id=s.task_id)

	select @start_time=max(xx.update_time) from PP_MID.DBO.pp_sell_through xx
	insert into PP_MID.dbo.pp_sell_through ([customer_type]
      ,[customer_id]
      ,[year_month]
      ,[order_no]
      ,[product_code]
      ,[sales_unit]
      ,[sales_amount]
      ,[sales_quantity]
      ,[sales_liters]
      ,[saler_code]
      ,[order_creator]
      ,[order_creator_mobile]
      ,[order_status]
      ,[order_create_time]
      ,[order_paied_time]
      ,[order_cancel_time]
      ,[order_delivery_time]
      ,[create_time]
      ,[update_time])
	SELECT 1 [customer_type], wto.work_shop_id, wto.create_time,
		wto.order_no, isnull(tbc.sku,tp.sku) sku, tp.units, null,wol.amount [sales_quantity],
		wol.amount*convert(float, isnull(tbp.capacity, tp.capacity)) [sales_liters],
		isnull((select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=wto.creator),
			 isnull(ws.excute_user_id, isnull((select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
					where s.org_id=ws.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc), 
					(select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=ws.creator)))) [saler_code], 
		null [order_creator], null, 100 [order_status], wto.create_time [order_create_time],
		null [order_paied_time], null [order_cancel_time], wto.update_time [order_delivery_time],@end_time,@end_time
					FROM wx_t_order wto
		LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
		LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
		left join temp_bundle_config tbc on tbc.bsku=wol.sku
		left join wx_t_product tbp ON tbc.sku = tbp.sku
		LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
		LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
		left join wx_t_organization o on o.id=wp.partner_id
	WHERE (wto.status=11 or (wto.partner_confirm_type = 1 and wto.status!=6) or EXISTS (
						SELECT 1
						FROM wx_t_db2b_order db_or
						WHERE  charindex(db_or.order_no, wto.source) > 0
							AND db_or.delete_flag = 0
							AND db_or.status IN (1)
					))
		AND ws.delete_flag = 0
		AND wol.id IS NOT NULL
		AND wto.order_type not in('DA','DP','SPDA')
		AND wp.partner_id!=9 
		and o.status=1 and wto.create_time between @start_time and @end_time


 	/*job成功记录*/
	insert into wx_log (operator,log_type,ext_property1,ext_property2, create_time) values (1,'success.synDmsCubeToBi','同步DMS CUBE数据成功',CONVERT(varchar(100), @start_time, 21),@end_time);
 	end try
	begin catch
	 	/*job失败记录*/
		insert into wx_log (operator,log_type,ext_property1,ext_property2,create_time) values (1,'error.synDmsCubeToBi','同步DMS CUBE数据失败',ERROR_MESSAGE(),@end_time);
	end catch
end