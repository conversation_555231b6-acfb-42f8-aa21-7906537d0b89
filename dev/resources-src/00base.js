;(function($){
	if(window.UI){
		return;
	}
	UI = {
		fitCls: 'ui-size-fit',
		hideCls: 'hide-important',
		preloadedImgs: {},
		preloadImg: function(img){
			if(!this._temp){
				this._temp = $('<div class="ui-temp" style="position: fixed;visibility: hidden;"></div>').appendTo('body');
			}
			if(!this[img]){
				this[img] = $('<img class="loading-img" src="' + img + '"/>').appendTo(this._temp);
			}
		},
		/**
		 * 添加控件的尺寸自适应功能
		 * @param instance [必填]控件实例
		 * @param target [必填]接收自适应事件的目标元素jQuery对象
		 * @param callback [必填]处理自适应事件的回调函数。格式function(width, height)
		 */
		addFitAbility: function(instance, target, callback){
			instance._uiFitSize = callback;
			target.addClass(this.fitCls).data('ui.fit', instance);
		},
		/**
		 * 触发自适应事件
		 * @param content [必填]触发自适应事件的容器
		 */
		triggerFitSize: function(content){
			var panel, thiz = this;
			if(content == window){
				panel = $('body>.' + this.fitCls);
				content = $(window);
			}else{
				panel = content.find('>.' + this.fitCls);
			}
			if(panel.length == 1){
				panel.addClass(this.hideCls);
				setTimeout(function(){
					var width = thiz.getInnerWidth(content), height = thiz.getInnerHeight(content);
					panel.removeClass(thiz.hideCls).data('ui.fit')._uiFitSize.call(UI, width, height);
				}, 1);
			}
		},
		/**
		 * 设置元素宽度
		 * @param $d [必填]操作元素jQuery对象
		 * @param width [必填]元素新宽度值
		 */
		setWidth: function($d, width){
			if(width < 0){
				$d.css('width', 'auto');
			}else{
				$d.css('width', (width - $d.outerWidth(true) + $d.width()) + 'px');
			}
		},
		/**
		 * 设置元素高度
		 * @param $d [必填]操作元素jQuery对象
		 * @param height [必填]元素新高度值
		 */
		setHeight: function($d, height){
			if(height < 0){
				$d.css('height', 'auto');
			}else{
				$d.css('height', (height - $d.outerHeight(true) + $d.height()) + 'px');
			}
		},
		/**
		 * 获取jQuery对象的内容宽度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的内容宽度
		 */
		getInnerWidth: function($d){
			return $d.width();
		},
		/**
		 * 获取jQuery对象的内容高度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的内容高度
		 */
		getInnerHeight: function($d){
			return $d.height();
		},
		/**
		 * 获取jQuery对象的总宽度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的总宽度
		 */
		getOuterWidth: function($d){
			return $d.outerWidth(true);
		},
		/**
		 * 获取jQuery对象的总高度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的总高度
		 */
		getOuterHeight: function($d){
			return $d.outerHeight(true);
		},
		/**
		 * 创建按钮html代码
		 * @param text [必填]按钮显示文本
		 * @param attrs [必填]按钮属性JSON对象
		 * @return 返回buttonhtml
		 */
		createButton: function(text, attrs){
			var h = ['<button'];
			for(var k in attrs){
				h.push(' ', k, '="', attrs[k] + '"');
			}
			h.push('>', text, '</button>');
			return h.join('');
		},
		/**
		 * 更新按钮状态
		 * @param $button [必填]按钮jQuery对象
		 * @param disabled [必填]按钮新状态
		 */
		updateBtnStatus: function($button, disabled){
			$button.prop('disabled', disabled);
		},
		alertError: function(msg){
			common.alertMes(msg, 'error');
		},
		/**
		 * 类继承
		 * @param parent [必填]父类
		 * @param child [必填]子类
		 * @return 继承后子类
		 */
		extend: function(parent, child){
			var newChild = $.extend({}, parent, child);
			if(parent.base && child.base){
				newChild.base = $.extend(newChild.base, parent.base);
			}
			if(parent.defaults && child.defaults){
				newChild.defaults = $.extend(newChild.defaults, parent.defaults);
			}
			return newChild;
		},
		/**
		 * 构建二叉树
		 * @node 新进节点
		 * @parentNode 树节点
		 */
		buildBinaryTree: function(node, parentNode){
			if(this._isLeftNode(node, parentNode)){
				if(parentNode.leftNode){
					this.buildBinaryTree(node, parentNode.leftNode);
				}else{
					parentNode.leftNode = node;
				}
			}else{
				if(parentNode.rightNode){
					this.buildBinaryTree(node, parentNode.rightNode);
				}else{
					parentNode.rightNode = node;
				}
			}
		},
		//判断新节点是否是树节点的左节点
		_isLeftNode: function(node, parentNode){
			if(node.values){
				for(var i = 0; i < node.values.length; i++){
					if(node.values[i] < parentNode.values[i]){
						return true;
					}else if(node.values[i] > parentNode.values[i]){
						return false;
					}
				}
				return false;
			}else{
				return node.value < parentNode.value;
			}
		},
		/**
		 * 遍历二叉树
		 * @param parentNode 二叉树父节点
		 * @param eachCallback 遍历回调
		 */
		traverseBinaryTree: function(parentNode, eachCallback){
			if(parentNode.leftNode){
				this.traverseBinaryTree(parentNode.leftNode, eachCallback);
			}
			eachCallback(parentNode);
			if(parentNode.rightNode){
				this.traverseBinaryTree(parentNode.rightNode, eachCallback);
			}
		},
		/**
		 * 行列转置
		 * @param records 数据集合
		 * @param keyProperty 转置键属性(空表示只汇总)
		 * @param valueProperty 转置值属性
		 * @param sumProperties 汇总属性集合
		 * @param ukProperties 唯一键属性集合
		 * @param enumCallback 遍历回调
		 * @param ukSeparator 唯一键分隔符。默认'/'
		 */
		rowToColumn: function(records, keyProperty, valueProperty, sumProperties, ukProperties, enumCallback, ukSeparator){
			var currentItem = null;
			ukSeparator = ukSeparator || '/';
			for(var i = 0, len = records.length; i < len; i++){
				var item = records[i];
				item.__uk = ''
				for(var j = 0; j < ukProperties.length; j++){
					item.__uk += ukSeparator + item[ukProperties[j]];
				}
				if(currentItem == null || currentItem.__uk != item.__uk){
					currentItem = item;
					enumCallback.call(this, item, item.__uk);
				}else if(sumProperties){
					//汇总数据
					for(var j = 0; j < sumProperties.length; j++){
						var p = sumProperties[j];
						currentItem[p] = (currentItem[p] || 0) + item[p];
					}
				}
				if(keyProperty && item[valueProperty] && item[keyProperty]){
					currentItem[item[keyProperty]] = (currentItem[item[keyProperty]] || 0) + item[valueProperty];
				}
			}
		}
	};
	
	UI.Base = {
		/** 类 */
		clazz: null,
		/** 实例初始化配置中支持的事件名称集合 */
		configEvents: null,
		/**
		 * 类创建实例
		 * @param opts [可选] 实例配置
		 * @return 创建的实例对象
		 */
		newInstance: function(opts){
			var instance = {clazz: this};
			if(this.defaults){
				$.extend(instance, this.defaults);
			}
			if(opts){
				instance = $.extend(instance, opts);
			}
			if(this.configEvents){
				for(var i = 0; i < this.configEvents.length; i++){
					if(instance[this.configEvents[i]]){
						if(!instance._events){
							instance._events = {};
						}
						instance._events[this.configEvents[i]] = [instance[this.configEvents[i]]];
					}
				}
			}
			if(this.base){
				instance = $.extend(instance, this.base);
			}
			instance.clazz = this;
			return instance;
		},
		base: {
			/**
			 * 触发事件
			 * @param eventName [必填]触发事件名称
			 * @param args [可选]触发事件参数
			 */
			trigger: function(eventName){
				if(this._suspendEvents && this._suspendEvents[eventName]){
					//事件暂停
					return;
				}
				var events = null;
				if(this._events){
					events = this._events[eventName];
				}
				var args = Array.prototype.slice.call(arguments, 1);
				//执行事件前置操作
				if(this.beforeEvents && this.beforeEvents[eventName]){
					if(this.beforeEvents[eventName].apply(this, args) === false){
						return false;
					}
				}
				//触发外部绑定事件
				if(events){
					for(var i = 0; i < events.length; i++){
						if(events[i].apply(this, args) === false){
							return false;
						}
					}
				}
				//执行事件后置操作
				if(this.afterEvents && this.afterEvents[eventName]){
					if(this.afterEvents[eventName].apply(this, args) === false){
						return false;
					}
				}
			},
			/**
			 * 绑定事件
			 * @param eventName [必填]绑定事件名称。格式eventName[.id]。为事件加ID方便解绑事件
			 * @param fun [必填]事件回调
			 */
			on: function(eventName, fun){
				var en = eventName, i = eventName.indexOf('.');
				if(i > 0){
					//事件附加加ID
					en = en.substring(0, i);
					if(!this._eventMap){
						this._eventMap = {};
					}
					this._eventMap[eventName] = fun;
				}
				this._events = this._events || [];
				var events = this._events[en];
				if(events){
					events.push(fun);
				}else{
					this._events[en] = [fun];
				}
			},
			/**
			 * 解绑事件
			 * @param eventName [必填]绑定事件名称
			 */
			off: function(eventName){
				var i = eventName.indexOf('.');
				if(i > 0){
					//解绑指定ID的事件
					var fun = this._eventMap[eventName];
					if(fun){
						var events = this._events[eventName.substring(0, i)];
						for(var i = 0; i < events.length; i++){
							if(events[i] == fun){
								events.splice(i, 1);
							}
						}
					}
				}else{
					//解绑指定事件名称的所有事件
					delete this._events[eventName];
				}
			},
			/**
			 * 暂停指定事件
			 * @param eventName 事件名称
			 */
			suspend: function(eventName){
				if(!this._suspendEvents){
					this._suspendEvents = {};
				}
				this._suspendEvents[eventName] = true;
			},
			/**
			 * 恢复开始指定事件
			 * @param eventName 事件名称
			 */
			start: function(eventName){
				if(this._suspendEvents){
					this._suspendEvents[eventName] = false;
				}
			}
		}
	};
	
	UI.DomBase = UI.extend(UI.Base, {
		/** 控件键值 */
		key: undefined,
		/** 控件绑定DOM的jQuery对象 */
		element: null,
		/** 控件初始化配置DOM ID映射表 */
		initOptsIdMap: {},
		/**
		 * 创建控件实例
		 * @param $d [必填]操作dom的jQuery对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		newInstance: function($d, opts){
			var optsJsonStr = $d.attr('opts'), id = $d.attr('id'), instance = this.createInitInstance($d);
			if(optsJsonStr){
				$.extend(instance, eval('(' + opts + ')'));
			}
			if(id && this.initOptsIdMap[id]){
				$.extend(instance, this.initOptsIdMap[id]);
			}
			if(opts){
				instance = $.extend(instance, opts);
			}
			if(this.configEvents){
				for(var i = 0; i < this.configEvents.length; i++){
					if(instance[this.configEvents[i]]){
						if(!instance._events){
							instance._events = {};
						}
						instance._events[this.configEvents[i]] = [instance[this.configEvents[i]]];
					}
				}
			}
			if(this.base){
				instance = $.extend(instance, this.base);
			}
			instance.element = $d;
			instance.clazz = this;
			if(this.key){
				$d.data(this.key, instance);
			}
			return instance;
		},
		/**
		 * 创建控件初始实例
		 * @param $d 控件绑定DOM的jQuery对象
		 * @return 控件初始实例
		 */
		createInitInstance: function($d){
			return $.extend({}, this.defaults);
		},
		/**
		 * 获取指定jQuery对象绑定的控件实例
		 * @param element 控件绑定的DOM的jQuery对象
		 * @return 控件实例
		 */
		getInstance: function(element){
			if(this.key){
				return element.data(this.key);
			}
			return null;
		},
		base: {
			/**
			 * 获取元素高度
			 * @return {number} 返回元素高度
			 */
			getHeight: function(){
				return UI.getOuterHeight(this.element);
			},
			/**
			 * 获取元素宽度
			 * @return {number} 返回元素宽度
			 */
			getWidth: function(){
				return UI.getOuterWidth(this.element);
			}
		}
	});
	$(window).resize(function(){
		UI.triggerFitSize(window);
	});
	//初始化UI全局事件
	UI.EventUtils = UI.Base.newInstance({});
	$(document).bind('click', function(e){
		UI.EventUtils.trigger('click', e);
	}).bind('keydown', function(e){
		UI.EventUtils.trigger('keydown', e);
	}).bind('keypress', function(e){
		UI.EventUtils.trigger('keypress', e);
	}).bind('keyup', function(e){
		UI.EventUtils.trigger('keyup', e);
	}).bind('mousemove', function(e){
		UI.EventUtils.trigger('mousemove', e);
	}).bind('mouseup', function(e){
		UI.EventUtils.trigger('mouseup', e);
	});
})(jQuery);