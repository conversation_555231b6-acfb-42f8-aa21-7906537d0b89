package com.chevron.report.model;

import java.sql.ResultSet;
import java.sql.SQLException;

public enum PartnerProductOverallPerformanceField {

	PARTNER_ID("partner_id"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setPartnerId(resultSet.getLong(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	PARTNER_NAME_CN("partner_name_cn"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setPartnerNameCn(resultSet.getString(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	PRODUCT_CHANNEL("product_channel"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setProductChannel(resultSet.getString(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	PRODUCT_CATEGORY("product_category"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setProductCategory(resultSet.getString(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	YTD_SELL_IN("ytd_sell_in_l"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setYtdSellIn(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	YTD_SELL_THROUGH("ytd_sell_through_l"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setYtdSellThrough(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	ROLLING_12_MONTH_SELL_IN("rolling_12_month_sell_in_l"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setRolling12MonthSellIn(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	ROLLING_12_MONTH_SELL_THROUGH("rolling_12_month_sell_through_l"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setRolling12MonthSellThrough(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	ROLLING_12_MONTH_AVG_SELL_IN_MARGIN_RMB("rolling_12_month_avg_sell_in_margin_rmb"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setRolling12MonthAvgSellInMarginRmb(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	ROLLING_12_MONTH_AVG_SELL_THROUGH_VOL("rolling_12_month_avg_sell_through_vol"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setRolling12MonthAvgSellThroughVol(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	},
	SHOP_FOR_SHOP_END_MARKET_AVG_SELL_THROUGH("shop_for_shop_end_market_avg_sell_through"){

		@Override
		public void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException {
			item.setShopForShopEndMarketAvgSellThrough(resultSet.getDouble(column));
		}

		@Override
		public void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact) {
			if(outputDim.length() > 0) {
				outputDim.append(",");
			}
			outputDim.append(column);
		}
	};

	protected String column;

	/**
	 * @param column
	 */
	private PartnerProductOverallPerformanceField(String column) {
		this.column = column;
	}

	public abstract void fillByResultSet(PartnerProductOverallPerformanceVo item, ResultSet resultSet) throws SQLException;
	
	public abstract void buildOutputTemplate(StringBuilder outputDim, StringBuilder outputFact);

	public String getColumn() {
		return column;
	}
}
