	select distinct u.* from dw_customer_org_sales cr 
	left join wx_t_partner_responsible_main m on m.region_name=cr.region
	left join wx_t_user u on u.user_id=m.user_id
	where cr.distributor_id=208 and m.fun_flag='mkt_specialist'

	select distinct u.login_name,u.ch_name,m.* 
--update m set m.user_id=(select u1.user_id from wx_t_user u1 where u1.login_name='WZPQ' and u1.status=1)
from dw_customer_org_sales cr 
	left join wx_t_partner_responsible_main m on m.region_name=cr.region
	left join wx_t_user u on u.user_id=m.user_id
	where --cr.distributor_id=208 and 
	m.fun_flag='mkt_specialist'
	--u.login_name='LHL'
	--m.region_name='SC'