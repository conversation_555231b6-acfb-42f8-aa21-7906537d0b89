{"version": 3, "sources": ["webpack:///./src/views/credit/apply/temp/submit.vue?f5e2", "webpack:///src/views/credit/apply/temp/submit.vue", "webpack:///./src/views/credit/apply/temp/submit.vue?4772", "webpack:///./src/views/credit/apply/temp/submit.vue", "webpack:///./node_modules/number-precision/build/index.js", "webpack:///./node_modules/core-js/modules/_string-trim.js", "webpack:///./node_modules/core-js/modules/es6.number.constructor.js", "webpack:///./node_modules/core-js/modules/_string-ws.js"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "title", "id", "staticRenderFns", "submitvue_type_script_lang_js_", "name", "components", "TitlePiece", "Basic", "temp", "Buttons", "_pieces_button", "Upload", "upload", "data", "$route", "query", "created", "_this", "params", "creditType", "$store", "dispatch", "then", "_ref", "_ref2", "Object", "slicedToArray", "status", "result", "$router", "replace", "temp_submitvue_type_script_lang_js_", "component", "componentNormalizer", "__webpack_exports__", "strip", "num", "precision", "parseFloat", "toPrecision", "digitLength", "eSplit", "toString", "split", "len", "length", "float2Fixed", "indexOf", "Number", "dLen", "Math", "pow", "checkBoundary", "_boundaryCheckingState", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "console", "warn", "times", "num1", "num2", "others", "_i", "arguments", "apply", "concat", "slice", "num1Changed", "num2Changed", "baseNum", "leftValue", "plus", "max", "minus", "divide", "round", "ratio", "base", "defineProperty", "exports", "value", "enableBoundaryChecking", "flag", "index", "$export", "__webpack_require__", "defined", "fails", "spaces", "space", "non", "ltrim", "RegExp", "rtrim", "exporter", "KEY", "exec", "ALIAS", "exp", "FORCE", "fn", "trim", "P", "F", "string", "TYPE", "String", "module", "global", "has", "cof", "inheritIfRequired", "toPrimitive", "gOPN", "f", "gOPD", "dP", "$trim", "NUMBER", "$Number", "Base", "proto", "prototype", "BROKEN_COF", "TRIM", "toNumber", "argument", "it", "third", "radix", "maxCode", "first", "charCodeAt", "NaN", "code", "digits", "i", "l", "parseInt", "that", "valueOf", "call", "key", "keys", "j", "constructor"], "mappings": "yHAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAAA,EAAA,eAAmCE,MAAA,CAAOC,MAAA,4CAAmD,CAAAH,EAAA,WAAgBE,MAAA,CAAOE,GAAAR,EAAAQ,OAAa,GAAAJ,EAAA,SAAAA,EAAA,eAC1NK,EAAA,2ECeAC,EAAA,CACAC,KAAA,2BACAC,WAAA,CACAC,WAAAN,EAAA,KACAO,MAAAC,EAAA,KACAC,QAAAC,EAAA,KACAC,OAAAC,EAAA,MAEAC,KARA,WASA,OACAZ,GAAAP,KAAAoB,OAAAC,MAAAd,KAGAe,QAbA,WAaA,IAAAC,EAAAvB,KACAwB,EAAA,CAAAC,WAAA,uBACAzB,KAAAO,KACAiB,EAAAjB,GAAAP,KAAAO,IAGAP,KAAA0B,OAAAC,SAAA,WAAAH,GAAAI,KAAA,SAAAC,GAAA,IAAAC,EAAAC,OAAAC,EAAA,KAAAD,CAAAF,EAAA,GAAAI,EAAAH,EAAA,GAAAX,EAAAW,EAAA,GACA,IAAAG,EAAA,SAEAd,EAAAe,OAAAf,KAAAZ,KAAAgB,EAAAhB,KACAgB,EAAAhB,GAAAY,EAAAe,OAAAf,KAAAZ,GACAgB,EAAAY,QAAAC,QAAA,0BAAAb,EAAAhB,SCxC6X8B,EAAA,cCO7XC,EAAgBP,OAAAQ,EAAA,KAAAR,CACdM,EACAvC,EACAU,GACF,EACA,KACA,KACA,MAIegC,EAAA,WAAAF,+CCNf,SAAAG,EAAAC,EAAAC,GAEA,YADA,IAAAA,IAA+BA,EAAA,KAC/BC,WAAAF,EAAAG,YAAAF,IAMA,SAAAG,EAAAJ,GAEA,IAAAK,EAAAL,EAAAM,WAAAC,MAAA,QACAC,GAAAH,EAAA,GAAAE,MAAA,aAAAE,SAAAJ,EAAA,OACA,OAAAG,EAAA,EAAAA,EAAA,EAMA,SAAAE,EAAAV,GACA,QAAAA,EAAAM,WAAAK,QAAA,KACA,OAAAC,OAAAZ,EAAAM,WAAAZ,QAAA,SAEA,IAAAmB,EAAAT,EAAAJ,GACA,OAAAa,EAAA,EAAAd,EAAAC,EAAAc,KAAAC,IAAA,GAAAF,IAAAb,EAMA,SAAAgB,EAAAhB,GACAiB,IACAjB,EAAAY,OAAAM,kBAAAlB,EAAAY,OAAAO,mBACAC,QAAAC,KAAArB,EAAA,iFAOA,SAAAsB,EAAAC,EAAAC,GAEA,IADA,IAAAC,EAAA,GACAC,EAAA,EAAoBA,EAAAC,UAAAlB,OAAuBiB,IAC3CD,EAAAC,EAAA,GAAAC,UAAAD,GAEA,GAAAD,EAAAhB,OAAA,EACA,OAAAa,EAAAM,WAAA,GAAAN,EAAAC,EAAAC,GAAAC,EAAA,IAAAI,OAAAJ,EAAAK,MAAA,KAEA,IAAAC,EAAArB,EAAAa,GACAS,EAAAtB,EAAAc,GACAS,EAAA7B,EAAAmB,GAAAnB,EAAAoB,GACAU,EAAAH,EAAAC,EAEA,OADAhB,EAAAkB,GACAA,EAAApB,KAAAC,IAAA,GAAAkB,GAKA,SAAAE,EAAAZ,EAAAC,GAEA,IADA,IAAAC,EAAA,GACAC,EAAA,EAAoBA,EAAAC,UAAAlB,OAAuBiB,IAC3CD,EAAAC,EAAA,GAAAC,UAAAD,GAEA,GAAAD,EAAAhB,OAAA,EACA,OAAA0B,EAAAP,WAAA,GAAAO,EAAAZ,EAAAC,GAAAC,EAAA,IAAAI,OAAAJ,EAAAK,MAAA,KAEA,IAAAG,EAAAnB,KAAAC,IAAA,GAAAD,KAAAsB,IAAAhC,EAAAmB,GAAAnB,EAAAoB,KACA,OAAAF,EAAAC,EAAAU,GAAAX,EAAAE,EAAAS,MAKA,SAAAI,EAAAd,EAAAC,GAEA,IADA,IAAAC,EAAA,GACAC,EAAA,EAAoBA,EAAAC,UAAAlB,OAAuBiB,IAC3CD,EAAAC,EAAA,GAAAC,UAAAD,GAEA,GAAAD,EAAAhB,OAAA,EACA,OAAA4B,EAAAT,WAAA,GAAAS,EAAAd,EAAAC,GAAAC,EAAA,IAAAI,OAAAJ,EAAAK,MAAA,KAEA,IAAAG,EAAAnB,KAAAC,IAAA,GAAAD,KAAAsB,IAAAhC,EAAAmB,GAAAnB,EAAAoB,KACA,OAAAF,EAAAC,EAAAU,GAAAX,EAAAE,EAAAS,MAKA,SAAAK,EAAAf,EAAAC,GAEA,IADA,IAAAC,EAAA,GACAC,EAAA,EAAoBA,EAAAC,UAAAlB,OAAuBiB,IAC3CD,EAAAC,EAAA,GAAAC,UAAAD,GAEA,GAAAD,EAAAhB,OAAA,EACA,OAAA6B,EAAAV,WAAA,GAAAU,EAAAf,EAAAC,GAAAC,EAAA,IAAAI,OAAAJ,EAAAK,MAAA,KAEA,IAAAC,EAAArB,EAAAa,GACAS,EAAAtB,EAAAc,GAGA,OAFAR,EAAAe,GACAf,EAAAgB,GACAV,EAAAS,EAAAC,EAAAlB,KAAAC,IAAA,GAAAX,EAAAoB,GAAApB,EAAAmB,KAKA,SAAAgB,EAAAvC,EAAAwC,GACA,IAAAC,EAAA3B,KAAAC,IAAA,GAAAyB,GACA,OAAAF,EAAAxB,KAAAyB,MAAAjB,EAAAtB,EAAAyC,OAlHApD,OAAAqD,eAAAC,EAAA,cAA8CC,OAAA,IAoH9C,IAAA3B,GAAA,EAKA,SAAA4B,EAAAC,QACA,IAAAA,IAA0BA,GAAA,GAC1B7B,EAAA6B,EAEA,IAAAC,EAAA,CAAahD,QAAAoC,OAAAE,QAAAf,QAAAgB,SAAAC,QAAAnC,cAAAM,cAAAmC,0BAEbF,EAAA5C,QACA4C,EAAAR,OACAQ,EAAAN,QACAM,EAAArB,QACAqB,EAAAL,SACAK,EAAAJ,QACAI,EAAAvC,cACAuC,EAAAjC,cACAiC,EAAAE,yBACAF,EAAA,WAAAI,wBC1IA,IAAAC,EAAcC,EAAQ,QACtBC,EAAcD,EAAQ,QACtBE,EAAYF,EAAQ,QACpBG,EAAaH,EAAQ,QACrBI,EAAA,IAAAD,EAAA,IACAE,EAAA,KACAC,EAAAC,OAAA,IAAAH,IAAA,KACAI,EAAAD,OAAAH,IAAA,MAEAK,EAAA,SAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAA,GACAC,EAAAZ,EAAA,WACA,QAAAC,EAAAO,MAAAL,EAAAK,MAAAL,IAEAU,EAAAF,EAAAH,GAAAI,EAAAH,EAAAK,GAAAb,EAAAO,GACAE,IAAAC,EAAAD,GAAAG,GACAhB,IAAAkB,EAAAlB,EAAAmB,EAAAJ,EAAA,SAAAD,IAMAG,EAAAP,EAAAO,KAAA,SAAAG,EAAAC,GAIA,OAHAD,EAAAE,OAAApB,EAAAkB,IACA,EAAAC,IAAAD,IAAA1E,QAAA6D,EAAA,KACA,EAAAc,IAAAD,IAAA1E,QAAA+D,EAAA,KACAW,GAGAG,EAAA5B,QAAAe,qCC5BA,IAAAc,EAAavB,EAAQ,QACrBwB,EAAUxB,EAAQ,QAClByB,EAAUzB,EAAQ,QAClB0B,EAAwB1B,EAAQ,QAChC2B,EAAkB3B,EAAQ,QAC1BE,EAAYF,EAAQ,QACpB4B,EAAW5B,EAAQ,QAAgB6B,EACnCC,EAAW9B,EAAQ,QAAgB6B,EACnCE,EAAS/B,EAAQ,QAAc6B,EAC/BG,EAAYhC,EAAQ,QAAgBgB,KACpCiB,EAAA,SACAC,EAAAX,EAAAU,GACAE,EAAAD,EACAE,EAAAF,EAAAG,UAEAC,EAAAb,EAAqBzB,EAAQ,OAARA,CAA0BoC,KAAAH,EAC/CM,EAAA,SAAAlB,OAAAgB,UAGAG,EAAA,SAAAC,GACA,IAAAC,EAAAf,EAAAc,GAAA,GACA,oBAAAC,KAAAlF,OAAA,GACAkF,EAAAH,EAAAG,EAAA1B,OAAAgB,EAAAU,EAAA,GACA,IACAC,EAAAC,EAAAC,EADAC,EAAAJ,EAAAK,WAAA,GAEA,QAAAD,GAAA,KAAAA,GAEA,GADAH,EAAAD,EAAAK,WAAA,GACA,KAAAJ,GAAA,MAAAA,EAAA,OAAAK,SACK,QAAAF,EAAA,CACL,OAAAJ,EAAAK,WAAA,IACA,gBAAAH,EAAA,EAAoCC,EAAA,GAAc,MAClD,iBAAAD,EAAA,EAAqCC,EAAA,GAAc,MACnD,eAAAH,EAEA,QAAAO,EAAAC,EAAAR,EAAA7D,MAAA,GAAAsE,EAAA,EAAAC,EAAAF,EAAA1F,OAAoE2F,EAAAC,EAAOD,IAI3E,GAHAF,EAAAC,EAAAH,WAAAI,GAGAF,EAAA,IAAAA,EAAAJ,EAAA,OAAAG,IACO,OAAAK,SAAAH,EAAAN,IAEJ,OAAAF,GAGH,IAAAR,EAAA,UAAAA,EAAA,QAAAA,EAAA,SACAA,EAAA,SAAAvC,GACA,IAAA+C,EAAAhE,UAAAlB,OAAA,IAAAmC,EACA2D,EAAAjJ,KACA,OAAAiJ,aAAApB,IAEAI,EAAApC,EAAA,WAA0CkC,EAAAmB,QAAAC,KAAAF,KAA4B7B,EAAA6B,IAAArB,GACtEP,EAAA,IAAAS,EAAAK,EAAAE,IAAAY,EAAApB,GAAAM,EAAAE,IAEA,QAMAe,EANAC,EAAkB1D,EAAQ,QAAgB4B,EAAAO,GAAA,6KAM1C7E,MAAA,KAAAqG,EAAA,EAA2BD,EAAAlG,OAAAmG,EAAiBA,IAC5CnC,EAAAW,EAAAsB,EAAAC,EAAAC,MAAAnC,EAAAU,EAAAuB,IACA1B,EAAAG,EAAAuB,EAAA3B,EAAAK,EAAAsB,IAGAvB,EAAAG,UAAAD,EACAA,EAAAwB,YAAA1B,EACElC,EAAQ,OAARA,CAAqBuB,EAAAU,EAAAC,wBCnEvBZ,EAAA5B,QAAA", "file": "js/chunk-fc06bb8e.b8829000.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('title-piece',{attrs:{\"title\":\"Temporary Credit Request Form 临时信用额度申请表\"}},[_c('buttons',{attrs:{\"id\":_vm.id}})],1),_c('basic'),_c('upload')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <title-piece title=\"Temporary Credit Request Form 临时信用额度申请表\">\n      <buttons :id=\"id\"/>\n    </title-piece>\n    <basic/>\n    <upload/>\n  </div>\n</template>\n\n<script>\nimport Title<PERSON>iece from '../_pieces/title'\nimport Basic from '../_pieces/basic/temp'\nimport Buttons from '../_pieces/button'\nimport Upload from '../_pieces/upload'\n\nexport default {\n  name: 'credit-apply-temp-submit',\n  components: {\n    TitlePiece,\n    Basic,\n    Buttons,\n    Upload\n  },\n  data () {\n    return {\n      id: this.$route.query.id\n    }\n  },\n  created () {\n    let params = { creditType: 'TEMP_CREDIT_REQUEST' }\n    if (this.id) {\n      params.id = this.id\n    }\n\n    this.$store.dispatch('getDraft', params).then(([status, data]) => {\n      if (!status) return false\n        \n      if (data.result.data.id && !this.id) {\n        this.id = data.result.data.id\n        this.$router.replace('/credit/temp/submit?id='+this.id)\n      }\n    })\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./submit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./submit.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./submit.vue?vue&type=template&id=8f8d9e08&\"\nimport script from \"./submit.vue?vue&type=script&lang=js&\"\nexport * from \"./submit.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n/**\r\n * @desc 解决浮动运算问题，避免小数点后产生多位数和计算精度损失。\r\n * 问题示例：2.3 + 2.4 = 4.699999999999999，1.0 - 0.9 = 0.09999999999999998\r\n */\r\n/**\r\n * 把错误的数据转正\r\n * strip(0.09999999999999998)=0.1\r\n */\r\nfunction strip(num, precision) {\r\n    if (precision === void 0) { precision = 12; }\r\n    return +parseFloat(num.toPrecision(precision));\r\n}\r\n/**\r\n * Return digits length of a number\r\n * @param {*number} num Input number\r\n */\r\nfunction digitLength(num) {\r\n    // Get digit length of e\r\n    var eSplit = num.toString().split(/[eE]/);\r\n    var len = (eSplit[0].split('.')[1] || '').length - (+(eSplit[1] || 0));\r\n    return len > 0 ? len : 0;\r\n}\r\n/**\r\n * 把小数转成整数，支持科学计数法。如果是小数则放大成整数\r\n * @param {*number} num 输入数\r\n */\r\nfunction float2Fixed(num) {\r\n    if (num.toString().indexOf('e') === -1) {\r\n        return Number(num.toString().replace('.', ''));\r\n    }\r\n    var dLen = digitLength(num);\r\n    return dLen > 0 ? strip(num * Math.pow(10, dLen)) : num;\r\n}\r\n/**\r\n * 检测数字是否越界，如果越界给出提示\r\n * @param {*number} num 输入数\r\n */\r\nfunction checkBoundary(num) {\r\n    if (_boundaryCheckingState) {\r\n        if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {\r\n            console.warn(num + \" is beyond boundary when transfer to integer, the results may not be accurate\");\r\n        }\r\n    }\r\n}\r\n/**\r\n * 精确乘法\r\n */\r\nfunction times(num1, num2) {\r\n    var others = [];\r\n    for (var _i = 2; _i < arguments.length; _i++) {\r\n        others[_i - 2] = arguments[_i];\r\n    }\r\n    if (others.length > 0) {\r\n        return times.apply(void 0, [times(num1, num2), others[0]].concat(others.slice(1)));\r\n    }\r\n    var num1Changed = float2Fixed(num1);\r\n    var num2Changed = float2Fixed(num2);\r\n    var baseNum = digitLength(num1) + digitLength(num2);\r\n    var leftValue = num1Changed * num2Changed;\r\n    checkBoundary(leftValue);\r\n    return leftValue / Math.pow(10, baseNum);\r\n}\r\n/**\r\n * 精确加法\r\n */\r\nfunction plus(num1, num2) {\r\n    var others = [];\r\n    for (var _i = 2; _i < arguments.length; _i++) {\r\n        others[_i - 2] = arguments[_i];\r\n    }\r\n    if (others.length > 0) {\r\n        return plus.apply(void 0, [plus(num1, num2), others[0]].concat(others.slice(1)));\r\n    }\r\n    var baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));\r\n    return (times(num1, baseNum) + times(num2, baseNum)) / baseNum;\r\n}\r\n/**\r\n * 精确减法\r\n */\r\nfunction minus(num1, num2) {\r\n    var others = [];\r\n    for (var _i = 2; _i < arguments.length; _i++) {\r\n        others[_i - 2] = arguments[_i];\r\n    }\r\n    if (others.length > 0) {\r\n        return minus.apply(void 0, [minus(num1, num2), others[0]].concat(others.slice(1)));\r\n    }\r\n    var baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));\r\n    return (times(num1, baseNum) - times(num2, baseNum)) / baseNum;\r\n}\r\n/**\r\n * 精确除法\r\n */\r\nfunction divide(num1, num2) {\r\n    var others = [];\r\n    for (var _i = 2; _i < arguments.length; _i++) {\r\n        others[_i - 2] = arguments[_i];\r\n    }\r\n    if (others.length > 0) {\r\n        return divide.apply(void 0, [divide(num1, num2), others[0]].concat(others.slice(1)));\r\n    }\r\n    var num1Changed = float2Fixed(num1);\r\n    var num2Changed = float2Fixed(num2);\r\n    checkBoundary(num1Changed);\r\n    checkBoundary(num2Changed);\r\n    return times((num1Changed / num2Changed), Math.pow(10, digitLength(num2) - digitLength(num1)));\r\n}\r\n/**\r\n * 四舍五入\r\n */\r\nfunction round(num, ratio) {\r\n    var base = Math.pow(10, ratio);\r\n    return divide(Math.round(times(num, base)), base);\r\n}\r\nvar _boundaryCheckingState = true;\r\n/**\r\n * 是否进行边界检查，默认开启\r\n * @param flag 标记开关，true 为开启，false 为关闭，默认为 true\r\n */\r\nfunction enableBoundaryChecking(flag) {\r\n    if (flag === void 0) { flag = true; }\r\n    _boundaryCheckingState = flag;\r\n}\r\nvar index = { strip: strip, plus: plus, minus: minus, times: times, divide: divide, round: round, digitLength: digitLength, float2Fixed: float2Fixed, enableBoundaryChecking: enableBoundaryChecking };\n\nexports.strip = strip;\nexports.plus = plus;\nexports.minus = minus;\nexports.times = times;\nexports.divide = divide;\nexports.round = round;\nexports.digitLength = digitLength;\nexports.float2Fixed = float2Fixed;\nexports.enableBoundaryChecking = enableBoundaryChecking;\nexports['default'] = index;\n", "var $export = require('./_export');\nvar defined = require('./_defined');\nvar fails = require('./_fails');\nvar spaces = require('./_string-ws');\nvar space = '[' + spaces + ']';\nvar non = '\\u200b\\u0085';\nvar ltrim = RegExp('^' + space + space + '*');\nvar rtrim = RegExp(space + space + '*$');\n\nvar exporter = function (KEY, exec, ALIAS) {\n  var exp = {};\n  var FORCE = fails(function () {\n    return !!spaces[KEY]() || non[KEY]() != non;\n  });\n  var fn = exp[KEY] = FORCE ? exec(trim) : spaces[KEY];\n  if (ALIAS) exp[ALIAS] = fn;\n  $export($export.P + $export.F * FORCE, 'String', exp);\n};\n\n// 1 -> String#trimLeft\n// 2 -> String#trimRight\n// 3 -> String#trim\nvar trim = exporter.trim = function (string, TYPE) {\n  string = String(defined(string));\n  if (TYPE & 1) string = string.replace(ltrim, '');\n  if (TYPE & 2) string = string.replace(rtrim, '');\n  return string;\n};\n\nmodule.exports = exporter;\n", "'use strict';\nvar global = require('./_global');\nvar has = require('./_has');\nvar cof = require('./_cof');\nvar inheritIfRequired = require('./_inherit-if-required');\nvar toPrimitive = require('./_to-primitive');\nvar fails = require('./_fails');\nvar gOPN = require('./_object-gopn').f;\nvar gOPD = require('./_object-gopd').f;\nvar dP = require('./_object-dp').f;\nvar $trim = require('./_string-trim').trim;\nvar NUMBER = 'Number';\nvar $Number = global[NUMBER];\nvar Base = $Number;\nvar proto = $Number.prototype;\n// Opera ~12 has broken Object#toString\nvar BROKEN_COF = cof(require('./_object-create')(proto)) == NUMBER;\nvar TRIM = 'trim' in String.prototype;\n\n// 7.1.3 ToNumber(argument)\nvar toNumber = function (argument) {\n  var it = toPrimitive(argument, false);\n  if (typeof it == 'string' && it.length > 2) {\n    it = TRIM ? it.trim() : $trim(it, 3);\n    var first = it.charCodeAt(0);\n    var third, radix, maxCode;\n    if (first === 43 || first === 45) {\n      third = it.charCodeAt(2);\n      if (third === 88 || third === 120) return NaN; // Number('+0x1') should be NaN, old V8 fix\n    } else if (first === 48) {\n      switch (it.charCodeAt(1)) {\n        case 66: case 98: radix = 2; maxCode = 49; break; // fast equal /^0b[01]+$/i\n        case 79: case 111: radix = 8; maxCode = 55; break; // fast equal /^0o[0-7]+$/i\n        default: return +it;\n      }\n      for (var digits = it.slice(2), i = 0, l = digits.length, code; i < l; i++) {\n        code = digits.charCodeAt(i);\n        // parseInt parses a string to a first unavailable symbol\n        // but ToNumber should return NaN if a string contains unavailable symbols\n        if (code < 48 || code > maxCode) return NaN;\n      } return parseInt(digits, radix);\n    }\n  } return +it;\n};\n\nif (!$Number(' 0o1') || !$Number('0b1') || $Number('+0x1')) {\n  $Number = function Number(value) {\n    var it = arguments.length < 1 ? 0 : value;\n    var that = this;\n    return that instanceof $Number\n      // check on 1..constructor(foo) case\n      && (BROKEN_COF ? fails(function () { proto.valueOf.call(that); }) : cof(that) != NUMBER)\n        ? inheritIfRequired(new Base(toNumber(it)), that, $Number) : toNumber(it);\n  };\n  for (var keys = require('./_descriptors') ? gOPN(Base) : (\n    // ES3:\n    'MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,' +\n    // ES6 (in case, if modules with ES6 Number statics required before):\n    'EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,' +\n    'MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger'\n  ).split(','), j = 0, key; keys.length > j; j++) {\n    if (has(Base, key = keys[j]) && !has($Number, key)) {\n      dP($Number, key, gOPD(Base, key));\n    }\n  }\n  $Number.prototype = proto;\n  proto.constructor = $Number;\n  require('./_redefine')(global, NUMBER, $Number);\n}\n", "module.exports = '\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003' +\n  '\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n"], "sourceRoot": ""}