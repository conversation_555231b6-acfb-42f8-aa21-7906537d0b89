package com.chevron.disb2b.service.impl;

import com.chevron.disb2b.activity.business.Db2bActivityBizService;
import com.chevron.disb2b.business.Db2bDistSettingBizService;
import com.chevron.disb2b.business.Db2bOrderBizService;
import com.chevron.disb2b.dao.Db2bGiftProductMapper;
import com.chevron.disb2b.dao.Db2bShoppingCarMapper;
import com.chevron.disb2b.model.*;
import com.chevron.disb2b.service.Db2bOrderService;
import com.chevron.disb2b.service.Db2bProductService;
import com.chevron.disb2b.util.Db2bConstants;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopEmployeeExample;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.exception.WxPltException;
import com.common.util.*;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单操作RPC接口实现
 *
 * <AUTHOR>
 * @version 1.0 2019-12-09 15:37
 */
@Service
public class Db2bOrderServiceImpl implements Db2bOrderService {

    @Autowired
    private Db2bOrderBizService db2bOrderBizService;
    @Autowired
    private WorkshopEmployeeMapper workshopEmployeeMapper;
    @Autowired
    private Db2bDistSettingBizService db2bDistSettingBizService;
    @Autowired
 	private WorkshopMasterMapper workshopMasterMapper;
    @Autowired
    Db2bShoppingCarMapper db2bShoppingCarMapper;
    @Autowired
    Db2bProductService db2bProductService;
    @Autowired
    Db2bGiftProductMapper db2bGiftProductMapper;
    @Autowired
    Db2bActivityBizService db2bActivityBizService;

    private final static Logger log = Logger.getLogger(Db2bOrderServiceImpl.class);

    @Override
    public JsonResponse save(Db2bOrder record) {
        JsonResponse map = new JsonResponse();
        log.info("save: " + JsonUtil.writeValue(record));
        Map<String, Object> resultMap;
        try {
            if (record.getId() == null) {
                resultMap = db2bOrderBizService.insert(record);
            } else {
                resultMap = db2bOrderBizService.update(record);
            }
            log.info("save success.");
            if (resultMap.size() > 0) {
                for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                    map.put(entry.getKey(), entry.getValue());
                }
            }
            map.put("code", MessageContants.SUCCESS_CODE);
            map.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.disb2b.service.impl.Db2bOrderServiceImpl.save", JsonUtil.writeValue(record));
        }
        return map;
    }

    @Override
    public JsonResponse saveForEditPage(Db2bOrder record) {
        JsonResponse map = new JsonResponse();
        log.info("saveForEditPage: " + JsonUtil.writeValue(record));
        try {
            if (record.getId() == null) {
                db2bOrderBizService.insert(record);
            } else {
                db2bOrderBizService.updateForEditPage(record);
            }
            log.info("saveForEditPage success.");
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.disb2b.service.impl.Db2bOrderServiceImpl.saveForEditPage", JsonUtil.writeValue(record));
        }
        return map;
    }

    @Override
    public JsonResponse delete(List<Long> ids) {
        JsonResponse map = new JsonResponse();
        log.info("delete: " + JsonUtil.writeValue(ids));
        try {
            db2bOrderBizService.delete(ids);
            log.info("delete success.");
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.disb2b.service.impl.Db2bOrderServiceImpl.delete", JsonUtil.writeValue(ids));
        }
        return map;
    }

    @Override
    public JsonResponse getBean(Long id) {
        JsonResponse map = new JsonResponse();
        log.info("getBean: " + id.toString());
        try {
            map.put("bean", db2bOrderBizService.getBean(id));
            log.info("getBean success.");
        } catch (WxPltException e) {
            map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            map.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.disb2b.service.impl.Db2bOrderServiceImpl.getBean", id.toString());
        }
        return map;
    }

    @Override
    public JsonResponse saveProductToCart(Db2bShoppingCar db2bShoppingCar) {
        JsonResponse resultMap = new JsonResponse();
        try {
            db2bOrderBizService.insertShoppingCart(db2bShoppingCar);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (WxPltException e) {
            log.error("saveProductToCart exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    @Override
    public JsonResponse queryShopCartByParams(Db2bShoppingCar db2bShoppingCar) {
        JsonResponse resultMap = new JsonResponse();
        try {
            List<Db2bShoppingCar> db2bShoppingCars = db2bOrderBizService.queryShopCartByParams(db2bShoppingCar);
            //产品有促销信息，但是都没有满足促销条件
            //promotionTips(db2bShoppingCars,resultMap);
            resultMap.put("resultList", db2bShoppingCars);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (WxPltException e) {
            log.error("queryShopCartByParams exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", e.getExpMsg());
        }
        return resultMap;
    }

    public JsonResponse updateProductFromCart(Db2bShoppingCar db2bShoppingCar) {
        JsonResponse resultMap = new JsonResponse();
        try {
            if (Db2bConstants.PLATFORM_APP.equals(db2bShoppingCar.getPlatform())) {
                db2bShoppingCar.setUserKey(String.valueOf(db2bShoppingCar.getWorkshopId()));
            }
            if (null == db2bShoppingCar.getAmount() || db2bShoppingCar.getAmount() <= 0) {
                db2bShoppingCarMapper.deleteByPrimaryKey(db2bShoppingCar.getId());
            } else {
                db2bOrderBizService.updateProductFromCart(db2bShoppingCar);
                if (db2bShoppingCar.getAmount() < db2bShoppingCar.getRewardAmount()) {
                    Db2bGiftProductExample example = new Db2bGiftProductExample();
                    example.createCriteria().andShopCarIdEqualTo(db2bShoppingCar.getId());
                    db2bGiftProductMapper.deleteByExample(example);
                }
            }
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (WxPltException e) {
            log.error("queryShopCartByParams exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public JsonResponse getProductOrderInfos(Db2bOrderParams db2bOrderParams) {
        JsonResponse resultMap = new JsonResponse();
        try {
            if (Db2bConstants.PLATFORM_APP.equals(db2bOrderParams.getPlatform())) {
                db2bOrderParams.setTechnicianName(ContextUtil.getCurUser().getCai());
            } else {
                if (!StringUtils.isNotEmpty(db2bOrderParams.getTechnicianMobile())) {
                    throw new WxPltException("技师手机号不可以为空");
                }
                WorkshopEmployeeExample example = new WorkshopEmployeeExample();
                example.createCriteria().andMobileEqualTo(db2bOrderParams.getTechnicianMobile()).andEnableFlagEqualTo(1);
                List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
                if (!list.isEmpty()) {
                    db2bOrderParams.setWorkshopId(list.get(0).getWorkshopId());
                } else {
                    throw new WxPltException("门店不存在");
                }
            }
            List<Db2bOrder> db2bOrders = db2bOrderBizService.queryAppForPage(db2bOrderParams);
            resultMap.put("resultList", db2bOrders);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("getProductOrderInfos exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public JsonResponse cancelOrders(Db2bOrder record) {
        JsonResponse resultMap = new JsonResponse();
        try {
            db2bOrderBizService.cancelOrders(record);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("cancelOrders exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public JsonResponse getPayMentCode(Db2bOrderParams db2bOrderParams) {
        JsonResponse resultMap = new JsonResponse();
        try {
            Long partnerIdLong;
            if (Db2bConstants.PLATFORM_APP.equals(db2bOrderParams.getPlatform())) {
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("workshopId", db2bOrderParams.getWorkshopId());
                partnerIdLong = db2bOrderBizService.getPartnerIdByWorkshopId(params);
            } else {
                if (!StringUtils.isNotEmpty(db2bOrderParams.getTechnicianMobile())) {
                    throw new WxPltException("技师手机号不可以为空");
                }
                WorkshopEmployeeExample example = new WorkshopEmployeeExample();
                example.createCriteria().andMobileEqualTo(db2bOrderParams.getTechnicianMobile()).andEnableFlagEqualTo(1);
                List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
                partnerIdLong = list.get(0).getPartnerId();
            }
            if (partnerIdLong != 0L) {
                Db2bDistSetting distSetting = db2bDistSettingBizService.getBeanByPartnerId(partnerIdLong);
                resultMap.put("resultList", distSetting);
                resultMap.put("code", MessageContants.SUCCESS_CODE);
                resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
            } else {
                throw new WxPltException("门店不存在");
            }
        } catch (Exception e) {
            log.error("cancelOrders exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public JsonResponse getDb2bOrderLinesByOrderId(Long id) {
        JsonResponse resultMap = new JsonResponse();
        try {
            Map<String, Object> result = db2bOrderBizService.getDb2bOrderLinesByOrderId(id);
            resultMap.put("orderVo", result.get("orderVo"));
            //resultMap.put("orderLineVo", result.get("orderLineVo"));
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("getDb2bOrderLinesByOrderId exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public JsonResponse getDb2bWorkShop(Db2bOrder record) throws WxPltException {
        JsonResponse resultMap = new JsonResponse();
        Map<String, Object> params = new HashMap<String, Object>();
        if (Db2bConstants.PLATFORM_APP.equals(record.getPlatform())) {
			params.put("id", record.getWorkshopId());
        } else {
            String userKey = record.getTechnicianMobile();
            if (!StringUtils.isNotEmpty(userKey)) {
                throw new WxPltException("技师手机号不可以为空");
            }
			params.put("employeeMobile", userKey);
        }
        try {
			List<WorkshopMaster> list = workshopMasterMapper.querySimpleByParams(params);
			if(list.isEmpty()) {
				throw new WxPltException("门店不存在");
			}
			resultMap.put("resultList", list.get(0));
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("getDb2bWorkShop exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    @Override
    public JsonResponse getWechatPayMentConfig(Db2bOrder record) {
        JsonResponse resultMap = new JsonResponse();
        try {
            //查询订单信息
            if (record.getId() == null) {
                throw new WxPltException("订单Id不可以为空");
            }
            Map<String, Object> mapResMap = db2bOrderBizService.getWechatPaymentConfig(record, false);
            resultMap.putAll(mapResMap);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("getWechatPayMentConfig exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public JsonResponse oneClickOrder(Db2bShoppingCar db2bShoppingCar) {
        JsonResponse resultMap = new JsonResponse();
        try {
            List<Db2bShoppingCar> db2bShoppingCars = db2bOrderBizService.createVirtualOrder(db2bShoppingCar);
            resultMap.put("resultList", db2bShoppingCars);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (WxPltException e) {
            log.error("oneClickOrder exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", e.getExpMsg());
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> saveGiftProductList(List<Db2bGiftProduct> db2bGiftProducts) {
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            //根据购物车Id查询促销信息是否存在，存在则删除，否则新增
            if (!db2bGiftProducts.isEmpty()) {
                Db2bGiftProductExample example = new Db2bGiftProductExample();
                example.createCriteria().andShopCarIdEqualTo(db2bGiftProducts.get(0).getShopCarId());
                db2bGiftProductMapper.deleteByExample(example);
                db2bGiftProductMapper.insertBatch(db2bGiftProducts);
            }
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
            log.info("save success.");
        } catch (Exception e) {
            e.printStackTrace();
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("com.chevron.performance.service.impl.SalesPerEvalFormServiceImpl.save", e);
        }
        return map;
    }

    @Override
    public Map<String, Object> checkPromotionInfos(Db2bOrder record) {
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            boolean flag = db2bOrderBizService.checkPromotionInfos(record);
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
            map.put("isFlag", flag);
            log.info("save success.");
        } catch (Exception e) {
            e.printStackTrace();
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("com.chevron.performance.service.impl.SalesPerEvalFormServiceImpl.save", e);
        }
        return map;
    }

    public void promotionTips(List<Db2bShoppingCar> db2bShoppingCars, Map<String, Object> resultMap) {
        resultMap.put("promotionTips", "");
        boolean isHasPromotion = false;
        boolean isAttendPromotion = true;
        if (!db2bShoppingCars.isEmpty()) {
            for (Db2bShoppingCar db2bShoppingCar : db2bShoppingCars) {
                if (db2bShoppingCar.isHasPromotion()) {
                    isHasPromotion = true;
                }
                if (db2bShoppingCar.isAttendPromotion()) {
                    isAttendPromotion = false;
                }
            }
            if (isHasPromotion && isAttendPromotion) {
                resultMap.put("promotionTips", "所选产品有促销，但是没有达到促销条件");
            }
        }
    }

    @Override
    public Map<String, Object> preBuildOrder(Db2bOrder record) {
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            map.put(Constants.RESULT_LST_KEY, db2bOrderBizService.preBuildOrder(record));
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
            log.info("save success.");
        } catch (Exception e) {
            e.printStackTrace();
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("com.chevron.disb2b.service.impl.preBuildOrder", e);
        }
        return map;
    }

    public Map<String, Object> getGiftProductByParams(Db2bBuyAgroupToBgroup record) {
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            Map<String, Object> giftList = db2bActivityBizService.getGiftList(record.getProductPromotionId(),
                    record.getQuantity());
            map.putAll(giftList);
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
            log.info("getGiftProductByParams success.");
        } catch (Exception e) {
            e.printStackTrace();
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("com.chevron.disb2b.service.impl.getGiftProductByParams", e);
        }
        return map;
    }

    @Override
    public Map<String, Object> deleteShoppingCarByIds(List<Long> ids) {
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            db2bOrderBizService.deleteShoppingCarByIds(ids);
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
            log.info("delete success.");
        } catch (Exception e) {
            e.printStackTrace();
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("com.chevron.disb2b.service.impl.deleteShoppingCarByIds", e);
        }
        return map;
    }

    public JsonResponse getPcDb2bOrderLinesByOrderId(Long id) {
        JsonResponse resultMap = new JsonResponse();
        try {
            Map<String, Object> result = db2bOrderBizService.getPcDb2bOrderLinesByOrderId(id);
            resultMap.put("orderVo", result.get("orderVo"));
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("getDb2bOrderLinesByOrderId exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }

    public Map<String, Object> updateOrderStatus(Db2bOrder record) {
        JsonResponse resultMap = new JsonResponse();
        try {
            db2bOrderBizService.updateOrderStatus(record);
            resultMap.put("code", MessageContants.SUCCESS_CODE);
            resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
        } catch (Exception e) {
            log.error("cancelOrders exception", e);
            resultMap.put("code", MessageContants.EXCEPTION_CODE);
            resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
        }
        return resultMap;
    }
}
