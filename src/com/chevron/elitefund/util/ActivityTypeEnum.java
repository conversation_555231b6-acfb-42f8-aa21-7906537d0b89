package com.chevron.elitefund.util;

public enum ActivityTypeEnum {
    //BD Activity Type
    BUSINESS_FUND("1", "1", "业务发展基金"),
    OFFICE_CAR("6", "1", "购置办公车辆"),
    TRANSPORT_CAR("7", "1", "购置运输车辆"),
    BUY_EQUIPMENT("8", "1", "购买办公设备"),
    RENT_OFFICE("9", "1", "租赁办公室"),
    RENT_STORAGE_ROOM("10", "1", "租赁库房"),
    CONFERENCE("11", "1", "差旅费（参加雪佛龙会议）"),
    TRAIN("12", "1", "差旅费（参加雪佛龙培训）"),
    OTHER("13", "1", "其他"),

    //MKT Activity Type
    CUSTOMER_INTERACTION("2", "2", "客户互动"),
    BILLBOARD_USED("3", "2", "广告牌使用"),
    SMALL_EQUIPMENT("4", "2", "小型设备使用"),
    LOCAL_PROMOTION("5", "2", "当地促销");


    private String code;
    private String fundCode;
    private String name;

    ActivityTypeEnum(String code, String fundCode, String name) {
        this.code = code;
        this.fundCode = fundCode;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (ActivityTypeEnum type : ActivityTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
