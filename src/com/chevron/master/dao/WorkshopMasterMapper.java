package com.chevron.master.dao;

import com.chevron.master.model.*;
import com.chevron.pms.model.WorkShopDistInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 门店-wx_t_work_shop数据库操作对象
 * <AUTHOR>
 * @version 1.0 2019-07-05 13:52
 */
public interface WorkshopMasterMapper {

	/**
	 * 根据主键更新门店对象(非空属性)
	 * @param record 新门店对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(WorkshopMaster record);

    /**
     * 根据主键删除门店对象
     * @param id 删除门店对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询门店对象
	 * @param id 门店对象主键值
	 * @return 门店对象结果
	 */
    WorkshopMaster selectByPrimaryKey(Long id);

 	/**
	 * 插入门店对象(非空属性)
	 * @param record 被插入门店对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(WorkshopMaster record);

	/**
	 * 更新门店对象
	 * @param record 被修改门店对象
	 * @param example 过滤门店对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") WorkshopMaster record, @Param("example") WorkshopMasterExample example);

    int batchUpdateRouteById(List<WorkshopMaster> list);

	/**
	 * 删除门店对象
	 * @param example 被删除门店对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(WorkshopMasterExample example);
    
 	/**
	 * 查询满足条件的门店对象数量
	 * @param example 过滤门店对象条件
	 * @return 满足条件门店对象数量
	 */
    int countByExample(WorkshopMasterExample example);

	/**
	 * 单表查询门店对象
	 * @param example 过滤门店对象条件
	 * @return 满足条件门店对象集合
	 */
    List<WorkshopMaster> selectByExample(WorkshopMasterExample example);

	/**
	 * 列表查询门店对象
	 * @param params 查询条件
	 * @return 满足条件门店对象集合
	 */
    List<WorkshopMaster> queryByParams(Map<String, Object> params);

	/**
	 * 分页查询门店对象
	 * @param params 查询条件
	 * @return 满足条件门店对象集合
	 */
    List<WorkshopMaster> queryForPage(WorkshopMasterParams params);

	long totalForPage(WorkshopMasterParams params);
    
    WorkshopMaster selectDmsWorkshopByKey(@Param("dmsKey")String dmsKey);
    
    List<WorkshopMaster> queryForCtrl(WorkshopCtrlParams params);
    
    List<WorkshopMaster> queryMatchDmsWorkshop(Map<String, Object> params);
    
    List<WorkshopMaster> queryDmsWorkshopForPage(WorkshopMasterParams params);
    
    long countDmsWorkshopByParams(Map<String, Object> params);
    
    List<WorkshopMaster> queryWorkShopByParams(Map<String, Object> params);
    
    /**
         * 经销商确认查询
     * @param params
     * @return
     */
    List<WorkshopMaster> queryWorkShopInfoForPage(WorkshopMasterParams params);
    
    List<WorkshopMaster> queryDeloMgxActivityForPage(WorkshopMasterParams params);
    
    List<WorkshopMaster> queryDeloMgxActivityAtts(@Param("workshopIds")List<Long> workshopIds, @Param("fleetIds")List<Long> fleetIds);

    List<WorkshopMaster> getMktCustomerList(WorkshopMasterParams params);

	List<WorkshopMaster> getMktApplyCustomerList(WorkshopMasterParams params);
    
    List<ReNameWorkShopVo> getReNameWorkShopList();
    
    /**
     * 逻辑删除客户信息
     */
    void updateDeleteFlagByIds(List<Long> list);

	/**
	 * 列表查询门店对象(包含门店基本信息)
	 * @param params 查询条件
	 * @return 满足条件门店对象集合
	 */
    List<WorkshopMaster> querySimpleByParams(Map<String, Object> params);

	/**
	 * 分页查询门店对象(包含门店基本信息)
	 * @param params 查询条件
	 * @return 满足条件门店对象集合
	 */
    List<WorkshopMaster> querySimpleForPage(WorkshopMasterParams params);

    long totalSimpleForPage(WorkshopMasterParams params);

	List<WorkShopDistInfo> queryWorkShopDistanceById(Map<String, Object> params);
	
	List<WorkShopDistInfo> queryFleetInfoDistanceById(Map<String, Object> params);
	
	List<WorkShopDistInfo> queryProjectMachineryDistanceById(Map<String, Object> params);

	Long checkWorkShop(Map<String, Object> params);

    List<String> getAvailableRoutes(@Param("route") String route);
}
