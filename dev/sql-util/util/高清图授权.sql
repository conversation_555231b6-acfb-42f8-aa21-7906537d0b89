insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) 
select 'PublishPhoto.operationPermission', r.ch_role_name, pf.level_code, 1, 1 from wx_t_rolesource rs 
 left join wx_t_role r on r.role_id=rs.role_id 
 left join wx_t_publish_photo_folder pf on pf.parent_id=-1
 where rs.source_id=50110
 and not exists (select 1 from wx_t_dic_item di where di.dic_type_code='PublishPhoto.operationPermission' and di.dic_item_code=r.ch_role_name)
 and r.ch_role_name !='admin'
 --and r.ch_role_name='Chevron_SOP_Specialist'
 
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) 
select 'PublishPhoto.operationPermission', di.dic_item_code, pf.level_code, di.dic_item_desc, 1 
from wx_t_dic_item di
 left join wx_t_publish_photo_folder pf on pf.parent_id=-1
 where di.dic_item_name='_'
 and not exists (select 1 from wx_t_dic_item di1 where di1.dic_type_code='PublishPhoto.operationPermission' and di1.dic_item_code=di.dic_item_code
 and di1.dic_item_name=pf.level_code) 