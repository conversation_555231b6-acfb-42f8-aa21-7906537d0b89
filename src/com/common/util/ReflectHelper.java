package com.common.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang3.reflect.FieldUtils;

import com.chevron.autozi.model.AutoziAnnotation;

public class ReflectHelper {

	public static Object getFieldValue(Object obj, String fieldName) {

		if (obj == null) {
			return null;
		}

		Field targetField = getTargetField(obj.getClass(), fieldName);

		try {
			return FieldUtils.readField(targetField, obj, true);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Field getTargetField(Class<?> targetClass, String fieldName) {
		Field field = null;

		try {
			if (targetClass == null) {
				return field;
			}

			if (Object.class.equals(targetClass)) {
				return field;
			}

			field = FieldUtils.getDeclaredField(targetClass, fieldName, true);
			if (field == null) {
				field = getTargetField(targetClass.getSuperclass(), fieldName);
			}
		} catch (Exception e) {
		}

		return field;
	}

	public static void setFieldValue(Object obj, String fieldName, Object value) {
		if (null == obj) {
			return;
		}
		Field targetField = getTargetField(obj.getClass(), fieldName);
		try {
			FieldUtils.writeField(targetField, obj, value);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 通过反射，获取排序的字段，及对应值的映射
	 * <AUTHOR> 2017-1-3 下午5:25:06
	 * @param zcProductPurchaseBean   请求类
	 * @param zcReqHeaderBean		    请求头
	 * @param allParams               所有要排序的
	 * @param actualParams            实际需要排序的字段
	 * @param paramMap                存放需要前面的字段键值对
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws InvocationTargetException
	 */
	public static void getParametersFromJavaBean(Object zcProductPurchaseBean,
			Object zcReqHeaderBean, List<String> allParams,
			List<String> actualParams, HashMap<String, String> paramMap)
			throws IllegalArgumentException, IllegalAccessException,
			InvocationTargetException {
		// 得到类对象
		Class productPurchaseCla = (Class) zcProductPurchaseBean.getClass();
		Field[] fs = productPurchaseCla.getDeclaredFields();
		for (int i = 0; i < fs.length; i++) {
			Field f = fs[i];
			String filedName = f.getName();
			f.setAccessible(true); // 设置些属性是可以访问的
			Object val = f.get(zcProductPurchaseBean);// 得到此属性的值
			allParams.add(filedName);
			if(!filedName.equals("header") && !filedName.equals("body") && !filedName.equals("sign"))
			{
				if (null!=val && !StringUtils.isEmpty((String) val)) {
					actualParams.add(filedName);
					paramMap.put(filedName, (String) val);
				}
			}
		}
		
		
		//获取头部
		Class zcReqHeaderCla = (Class) zcReqHeaderBean.getClass();
		Field[] fs1 = zcReqHeaderCla.getDeclaredFields();
		for (int i = 0; i < fs1.length; i++) {
			Field f = fs1[i];
			String filedName = f.getName();
			f.setAccessible(true); // 设置些属性是可以访问的
			Object val = f.get(zcReqHeaderBean);// 得到此属性的值
			allParams.add(f.getName());
			if (null!=val && !StringUtils.isEmpty((String) val)) {
				actualParams.add(filedName);
				paramMap.put(filedName, (String) val);
			}
		}
	}
	
	
	
	/**
	 * 
	 * <AUTHOR> 2017-1-3 下午11:18:15
	 * @param zcProductPurchaseBean           实体类
	 * @param zcReqHeaderBean                 头部类
	 * @param zcCommonallParams               实体类必须签名的所有属性
	 * @param zcCommonParams                  实体类公共的属性
	 * @param headerParams                    头部必须签名的所有属性
	 * @param headerParams                    头部属性
	 * @param paramMap                        存放签名的字段和属性
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws InvocationTargetException
	 */
	public static void getParametersFromJavaBean(Object zcProductPurchaseBean,
			Object zcReqHeaderBean, List<String> zcCommonallParams,
			List<String> zcCommonParams,List<String> headerallParams,List<String> headerParams, HashMap<String, String> paramMap, List<String> mustParamLst)
			throws IllegalArgumentException, IllegalAccessException,
			InvocationTargetException {
		// 得到类对象
		Class productPurchaseCla = (Class) zcProductPurchaseBean.getClass();
		Field[] fs = productPurchaseCla.getDeclaredFields();
		for (int i = 0; i < fs.length; i++) {
			Field f = fs[i];
			String filedName = f.getName();
			AutoziAnnotation autozi = f.getAnnotation(AutoziAnnotation.class);
			boolean isneedParams = false;
			if(null!=autozi)
			{
				 isneedParams = autozi.isNeedReqParam();
			}
			
			//System.out.println("---------------ljc======getParametersFromJavaBean:"+isneedParams);
			f.setAccessible(true); // 设置些属性是可以访问的
			Object val = f.get(zcProductPurchaseBean);// 得到此属性的值
			if(!filedName.equals("header") && !filedName.equals("body") && !filedName.equals("sign"))
			{
				zcCommonallParams.add(filedName);
				if (null!=val && !StringUtils.isEmpty((String) val)) {
					zcCommonParams.add(filedName);
					paramMap.put(filedName, (String) val);
				}else if(null==val || val.toString().trim().isEmpty())//空字符串需要占位处理
				{
					zcCommonParams.add(filedName);
					paramMap.put(filedName, "");
					if(null!=autozi)
					{
						if(isneedParams)
						{
							mustParamLst.add(filedName);
							//System.out.println("---------------ljc======getParametersFromJavaBean22222:"+isneedParams);
						}
					}
				}
			}
		}
		
		
		//获取头部
		Class zcReqHeaderCla = (Class) zcReqHeaderBean.getClass();
		Field[] fs1 = zcReqHeaderCla.getDeclaredFields();
		for (int i = 0; i < fs1.length; i++) {
			Field f = fs1[i];
			String filedName = f.getName();
			AutoziAnnotation autoziHeader = f.getAnnotation(AutoziAnnotation.class);
			boolean isneedParams = false;
			if(null!=autoziHeader)
			{
				 isneedParams = autoziHeader.isNeedReqParam();
			}
			
			//System.out.println("---------------ljc======zcReqHeaderBean:"+isneedParams);
			f.setAccessible(true); // 设置些属性是可以访问的
			Object val = f.get(zcReqHeaderBean);// 得到此属性的值
			headerallParams.add(f.getName());
			if (null!=val && !StringUtils.isEmpty((String) val)) {
				headerParams.add(filedName);
				paramMap.put(filedName, (String) val);
			}else if(null==val || val.toString().trim().isEmpty())//空字符串需要占位处理
			{
				headerParams.add(filedName);
				paramMap.put(filedName, "");
				if(null!=autoziHeader)
				{
					if(isneedParams)
					{
						mustParamLst.add(filedName);
						//System.out.println("---------------ljc======zcReqHeaderBean:"+isneedParams);
					}
				}
			}
		}
	}
}