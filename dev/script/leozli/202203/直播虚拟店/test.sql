select mobile from wx_t_workshop_employee we group by mobile having count(1)>1

select distinct o.id, o.organization_name, u.ch_name, u.mobile_tel,u.user_id
from wx_t_user u join wx_t_organization o on o.id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where o.type=1 and (u.type is null or u.type != '1') and u.status=1 and o.status=1
and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')
and not exists (select 1 from wx_t_workshop_employee we where we.mobile=u.mobile_tel)
and not exists (select 1 from wx_t_user u1 
left join wx_t_userrole ur1 on u1.user_id=ur1.user_id
left join wx_t_role r1 on r1.role_id=ur1.role_id
where (u1.type is null or u1.type != '1') and u1.status=1 
and r1.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer') and u1.mobile_tel=u.mobile_tel and u1.user_id<u.user_id)
order by o.id

select u.ch_name,u.mobile_tel,u.user_id from wx_t_user u where u.cai in (
select cs1.sales_cai from dw_customer_org_sales cs1 where cs1.region in (select di.dic_item_code from wx_t_dic_item di where di.dic_type_code='Region.Indirect'))
and u.status=1 and not exists (select 1 from wx_t_workshop_employee we where we.mobile=u.mobile_tel)

select o.id,o.organization_name from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
where pe.eto_company_id is null and o.id in (33455,
44696,
56352,
56359,
56360,
56362,
56363,
56364,
56365,
56367,
56368,
56369,
56371,
56373,
56374,
56375,
56378,
56381,
56382,
56393,
56394,
56400,
56403,
56404,
56405,
56407,
56408,
56409,
56412,
56413,
56420,
56422,
56429,
56431,
56432,
56437,
56443,
56446,
56448,
56451,
56452,
56454,
56457,
56458,
56461,
56462,
56464,
56465,
56466,
56468,
56469,
56470,
56472,
56473,
56480,
56481,
56482,
56484,
56485,
56488,
56491,
56493,
56495,
56496,
56499,
56502,
56505,
56508,
56510,
56512,
56513,
56514,
56515,
56516,
56517,
56519,
56520,
56524,
56525,
56526,
56529,
56532,
56545,
56553,
56554,
56557,
56562,
56563,
56570,
56571,
56593,
56602,
56604,
56616,
56620,
56622,
56623,
56626,
56627,
56628,
56635,
56637,
56640,
56642,
56643,
56645,
56647,
56648,
56651,
56652,
56653,
56654,
56656,
56661,
56663,
56665,
56667,
56669,
56670,
56677,
56683,
56686,
56742,
56744,
56745,
56746,
56749,
56750,
56751,
56753,
56757,
56759,
56762,
56763,
56764,
56765,
56768,
56770,
56771,
56774,
56775,
56776,
56777,
56801,
56814,
56816,
56817,
56818,
56819,
56820,
56821,
56823,
56824,
56825,
56826,
56827,
56828,
56829,
56831,
56834,
56835,
56836,
56837,
56838,
56839,
56840,
56842,
56843,
56844,
56845,
56846,
56847,
56848,
56850,
56851,
56852,
56853,
56854,
56856,
56858,
56859,
56862,
56867,
56868,
9)

select we.* into wx_t_workshop_employee20220519_dist 
--delete we
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
where w.partner_id=9 
and exists (select 1 from wx_t_user u left join wx_t_organization o on u.org_id=o.id 
where o.type=1 and o.status=1 and u.status=1 and o.id!=9 and we.mobile=u.mobile_tel)
and w.id!=187470

select we.* into wx_t_workshop_employee20220519_distd 
--delete we
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
where w.delete_flag=1 
and exists (select 1 from wx_t_user u left join wx_t_organization o on u.org_id=o.id 
where o.type=1 and o.status=1 and u.status=1 and o.id!=9 and we.mobile=u.mobile_tel)
and w.id!=187470

select we.* into wx_t_workshop_employee20220519_sales
--delete we
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
where w.partner_id=9 
and exists (select 1 from wx_t_user u where u.cai in (
select cs1.sales_cai from dw_customer_org_sales cs1 where cs1.region in (select di.dic_item_code from wx_t_dic_item di where di.dic_type_code='Region.Indirect'))
and u.status=1 and u.mobile_tel=we.mobile)
and w.id!=187470

select distinct u.ch_name,u.mobile_tel,u.user_id from wx_t_user u where u.cai in (
select cs1.sales_cai from dw_customer_org_sales cs1 where cs1.region in (select di.dic_item_code from wx_t_dic_item di where di.dic_type_code='Region.Indirect'))
and u.status=1 and not exists (select 1 from wx_t_workshop_employee we where we.mobile=u.mobile_tel)
and not exists (select 1 from wx_t_user u1 where u1.status=1 and u1.mobile_tel=u.mobile_tel and u1.user_id>u.user_id)

select u.ch_name,u.mobile_tel,u.user_id from wx_t_user u where u.cai in (
select cs1.sales_cai from dw_customer_org_sales cs1 where cs1.region in (select di.dic_item_code from wx_t_dic_item di where di.dic_type_code='Region.Indirect'))
and u.status=1 and not exists (select 1 from wx_t_workshop_employee we where we.mobile=u.mobile_tel and we.workshop_id=187470)

select distinct o.id, o.organization_name, u.ch_name, u.mobile_tel,u.user_id
from wx_t_user u join wx_t_organization o on o.id=u.org_id
where o.type=1 and (u.type is null or u.type != '1') and u.status=1 and o.status=1
and not exists (select 1 from wx_t_workshop_employee we where we.mobile=u.mobile_tel)
and not exists (select 1 from wx_t_user u1 
where (u1.type is null or u1.type != '1') and u1.status=1 
and u1.mobile_tel=u.mobile_tel and u1.user_id<u.user_id)
and o.id in (33455,
44696,
56352,
56359,
56360,
56362,
56363,
56364,
56365,
56367,
56368,
56369,
56371,
56373,
56374,
56375,
56378,
56381,
56382,
56393,
56394,
56400,
56403,
56404,
56405,
56407,
56408,
56409,
56412,
56413,
56420,
56422,
56429,
56431,
56432,
56437,
56443,
56446,
56448,
56451,
56452,
56454,
56457,
56458,
56461,
56462,
56464,
56465,
56466,
56468,
56469,
56470,
56472,
56473,
56480,
56481,
56482,
56484,
56485,
56488,
56491,
56493,
56495,
56496,
56499,
56502,
56505,
56508,
56510,
56512,
56513,
56514,
56515,
56516,
56517,
56519,
56520,
56524,
56525,
56526,
56529,
56532,
56545,
56553,
56554,
56557,
56562,
56563,
56570,
56571,
56593,
56602,
56604,
56616,
56620,
56622,
56623,
56626,
56627,
56628,
56635,
56637,
56640,
56642,
56643,
56645,
56647,
56648,
56651,
56652,
56653,
56654,
56656,
56661,
56663,
56665,
56667,
56669,
56670,
56677,
56683,
56686,
56742,
56744,
56745,
56746,
56749,
56750,
56751,
56753,
56757,
56759,
56762,
56763,
56764,
56765,
56768,
56770,
56771,
56774,
56775,
56776,
56777,
56801,
56814,
56816,
56817,
56818,
56819,
56820,
56821,
56823,
56824,
56825,
56826,
56827,
56828,
56829,
56831,
56834,
56835,
56836,
56837,
56838,
56839,
56840,
56842,
56843,
56844,
56845,
56846,
56847,
56848,
56850,
56851,
56852,
56853,
56854,
56856,
56858,
56859,
56862,
56867,
56868,
22955,
56312,
56354,
56356,
56357,
56376,
56383,
56395,
56397,
56398,
56399,
56414,
56415,
56421,
56433,
56463,
56474,
56487,
56494,
56503,
56506,
56507,
56531,
56591,
56605,
56614,
56633,
56634,
56636,
56638,
56646,
56662,
56664,
56682,
56688,
56758,
56766,
56767,
56769,
56772,
56815,
56822,
56830,
56832,
56833,
56841)
order by o.id