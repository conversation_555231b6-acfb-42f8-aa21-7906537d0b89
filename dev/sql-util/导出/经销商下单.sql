/****** <PERSON><PERSON>t for SelectTopNRows command from SSMS  ******/
SELECT c.distributor_id,o.organization_name--,(select distinct customer_name_cn from dw_customer_org_sales s where s.distributor_id=c.distributor_id) customer_name
      ,c.[sap_code]
      ,c.[ship_to_code]
	  ,c.payment_term
      ,[address]
      ,[contact_person]
      ,[contact_person_tel]
  FROM [PP_MID].[dbo].[mid_partner_sale_config] c
  left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=c.distributor_id
  left join wx_t_organization o on o.id=pe.partner_id 
  where o.status=1 and getdate() between c.date_from and c.date_to and o.id!=9
  and len(c.[address])>0
  order by o.id
  
  
/****** Script for SelectTopNRows command from SSMS  ******/
select a.distributor_id,a.organization_name, count(1) "地址数量" from (
SELECT distinct c.distributor_id,o.organization_name--,(select distinct customer_name_cn from dw_customer_org_sales s where s.distributor_id=c.distributor_id) customer_name
      ,[address]
  FROM [PP_MID].[dbo].[mid_partner_sale_config] c
  left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=c.distributor_id
  left join wx_t_organization o on o.id=pe.partner_id 
  where o.status=1 and getdate() between c.date_from and c.date_to and o.id!=9
  and len(c.[address])>0) a
  group by a.distributor_id,a.organization_name
  order by a.distributor_id
  
select o.id, o.organization_name, count(1) "SKU下单数量" from (
select distinct po.partner_id, pol.sku from wx_t_partner_order po left join wx_t_partner_order_line pol on pol.partner_order_id=po.id
where po.status in (1,2,3,5,6,7,8,15)
) a
left join wx_t_organization o on o.id=a.partner_id
where o.status=1
group by o.id, o.organization_name
order by o.id  