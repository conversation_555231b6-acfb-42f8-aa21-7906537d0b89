SELECT
	(SELECT di1.dic_item_name FROM wx_t_dic_item di1 WHERE di1.dic_type_code= 'Workshop.customerType' AND di1.dic_item_code & a.customer_type > 0 ) as '客户类型',
	substring((case when a.business_weight & 1 > 0  then ',金富力' else '' end
	+ case when a.business_weight & 2 > 0 then ',德乐' else '' end
	+ case when a.business_weight & 4> 0 then ',工程机械' else '' end),2,1000) as '品牌',
	a.partner_name as '经销商名称',
	a.work_shop_name as '客户名称',
	a.province_name as '所属省份',
	a.city_name as '所属城市',
	a.dist_name as '所属区域',
	a.work_shop_address as '客户地址',
	a.ch_name as '执行人',
	a.sale_major_delo_product '主要销售产品',
	substring((case when a.workshop_property & 1024 > 0 then '，B2B店' else '' end + 
	case when a.workshop_property & 1073741824 > 0 then '，导航店' else '' end + 
	case when a.workshop_property & 16384 > 0 then '，黑金店' else '' end +
	case when a.workshop_property & 2 > 0 then '，陈列之星' else '' end + 
	case when a.workshop_property & 2048 > 0 then '，店招店' else '' end + 
	case when a.workshop_property & 32768 > 0 then '，陈列大比拼' else '' end + 
	case when a.workshop_property & 4096 > 0 then '，定位店' else '' end +
	case when a.workshop_property & 131072 > 0 then '，直播' else '' end +
	case when a.workshop_property & 8 > 0 then '，DMS店' else '' end + 
	case when a.workshop_property & 8192 > 0 then '，参与德乐400系列陈列活动' else '' end),2,1000) as '客户标签',
	a.create_time '客户录入时间',
	a.activation_time '客户激活时间',
	a.longitude '经度',
	a.latitude '纬度',
	a.remark '备注'
FROM
	(
SELECT
	t1.id,
	t1.work_shop_name,
	t1.customer_type,
	t1.ext_flag2 volitional_work_shop,
	t1.work_shop_address,
	t1.longitude,
	t1.latitude,
	t1.from_source,
	t1.business_weight,
	t1.remark,
	t1.ext_flag,
	t1.ext_property10 sale_major_delo_product,
	( SELECT di1.dic_item_name FROM wx_t_dic_item di1 WHERE di1.dic_type_code= 'workshop.type' AND di1.dic_item_code= t1.type ) type_text,
	(case when t1.customer_type=1/*****门店*****/ then
				 (case when t1.ext_flag & 65536>0 then 2 else 0 end/*陈列之星-1*/)
				 + (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 1024 else 0 end/*B2B店-10*/)
				 + (case when exists (select 1 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
						where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.org_id=t1.id) then 32768 else 0 end/*陈列大比拼-15*/)
				  + (case when t1.ext_flag & 6144>0 then 2048 else 0 end/*店招店(11,12)-11*/) 
				  + (case when t1.ext_flag & 32768>0 then 4096 else 0 end/*定位店(15)-12*/)
				  + (case when t1.ext_flag & 1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
				  + (case when t1.ext_flag & 16>0 then 1073741824 else 0 end/*导航店(4)-30*/)
				  + (t1.ext_flag & 16384) /*黑金店(14)-14*/
				  + (t1.ext_flag & 131072) /*直播(17位)*/
				 + (case when t1.from_source!=8 and t1.dms_key is not null then 8 else 0 end/*PP中匹配的DMS门店-3*/) 
			when t1.customer_type=2/*****车队*****/ then (case when t1.ext_flag & 1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
			else 0 end)  workshop_property,
	r1.region_name dist_name,
	r2.region_name city_name,
	r3.region_name province_name,
	o.organization_name partner_name,
	u.ch_name,
	t1.activation_time,
	t1.create_time
FROM
	wx_t_work_shop t1
	LEFT JOIN wx_t_partner_o2o_enterprise wp1 ON wp1.partner_id= t1.partner_id
	JOIN wx_t_organization o ON wp1.partner_id= o.id
	LEFT JOIN wx_t_region r1 ON t1.region_id= r1.id
	LEFT JOIN wx_t_region r2 ON r1.parent_id= r2.id
	LEFT JOIN wx_t_region r3 ON r2.parent_id= r3.id
	LEFT JOIN wx_att_file tt_file ON tt_file.uuid= t1.photo_id 
	LEFT JOIN wx_t_user u ON t1.excute_user_id= u.user_id 
	WHERE o.status= '1'  AND CONVERT (INT, t1.status ) != '-3' and t1.id in ($workshopIds$)
	) a
WHERE
	1 = 1 
order by id desc