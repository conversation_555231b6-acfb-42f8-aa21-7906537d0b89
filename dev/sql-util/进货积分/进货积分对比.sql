 --select sum(fs) from (
 select isnull(b.pending_score, 0)-isnull(a.pending_score, 0) fs, a.pending_score, 
 b.pending_score b_pending_score,a.partner_name,a.partner_id,a.product_sku,
 b.bi_sellin_line_id from (
    SELECT
        distributor_id,
		partner_id,
		partner_name,
		bi_sellin_line_id,
		pending_score,
		status,
		sales_channel_name,
		creation_time,
		created_by,
		last_update_time,
		last_updated_by,
		liters,
		product_sku,
		trans_time
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            dw.base_trans_sell_in_id AS bi_sellin_line_id,
            sum( round( dw.abp_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.product_channel sales_channel_name,
            getdate() AS creation_time,
            1 AS created_by,
            getdate() AS last_update_time,
            1 AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            dw.product_code_sap product_sku,
            dw.trans_time
          from PP_MID.dbo.syn_dw_to_pp_abp dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = isnull(dw.partner_id,en.partner_id)
          WHERE
            1 = 1
            AND (dw.trans_time >= '2021-01-01' and trans_time < '2021-02-01')
            AND dw.product_channel = 'Commercial'
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = dw.distributor_id
                  AND re.partner_id = dw.partner_id
                  AND re.product_sku = dw.product_code_sap
                  AND re.sales_channel_name = dw.sales_channel_name
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
            dw.product_code_sap,
            dw.product_channel,
            dw.trans_time,
            dw.base_trans_sell_in_id
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0) a 
	  full outer join
	  (select x.* from wx_t_point_pending_record x where (x.trans_time >= '2021-01-01' and x.trans_time < '2021-02-01')
            AND x.sales_channel_name = 'Commercial') b on b.partner_id=a.partner_id 
	and a.product_sku=b.product_sku and a.bi_sellin_line_id=b.bi_sellin_line_id
	where isnull(a.pending_score, 0)!= isnull(b.pending_score, 0) --or b.pending_score is null
--	) xx1
	
	
	
	
	
select sum(pending_score) from wx_t_point_pending_record dw 
where (dw.trans_time >= '2021-01-01' and trans_time < '2021-02-01')
            AND dw.sales_channel_name = 'Consumer' and dw.partner_id=56640
            
            
select a.partner_id, max(a.partner_name) partner_name, sum(a.pending_score) 
from (select * from wx_t_point_pending_record dw
 where dw.trans_time >= '2021-01-01' and trans_time < '2021-02-01'
 and bi_sellin_line_id<0 and sales_channel_name='Consumer') a
 group by a.partner_id            
	
 
 
 
 
 
 select xx1.partner_id, max(xx1.partner_name) partner_name, sum(xx1.pending_score) from  (
 select isnull(b.pending_score, 0)-isnull(a.pending_score, 0) fs, a.pending_score, 
 b.pending_score b_pending_score,a.partner_name,a.partner_id,a.product_sku,
 a.bi_sellin_line_id,a.update_time from (
    SELECT
        distributor_id,
		partner_id,
		partner_name,
		bi_sellin_line_id,
		pending_score,
		status,
		sales_channel_name,
		creation_time,
		created_by,
		last_update_time,
		last_updated_by,
		liters,
		product_sku,
		trans_time,
		update_time
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            dw.base_trans_sell_in_id AS bi_sellin_line_id,
            sum( round( dw.abp_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.product_channel sales_channel_name,
            getdate() AS creation_time,
            1 AS created_by,
            getdate() AS last_update_time,
            1 AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            dw.product_code_sap product_sku,
            dw.trans_time,
			max(dw.update_time) update_time
          from PP_MID.dbo.syn_dw_to_pp_abp dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = isnull(dw.partner_id,en.partner_id)
          WHERE
            1 = 1
            AND (dw.trans_time >= '2021-01-01' and trans_time < '2021-02-01')
            AND dw.product_channel = 'Consumer'
			--and dw.del_flag=0
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = dw.distributor_id
                  AND re.partner_id = dw.partner_id
                  AND re.product_sku = dw.product_code_sap
                  AND re.sales_channel_name = dw.sales_channel_name
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
            dw.product_code_sap,
            dw.product_channel,
            dw.trans_time,
            dw.base_trans_sell_in_id
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0) a 
	  full outer join
	  (select x.* from wx_t_point_pending_record x where (x.trans_time >= '2021-01-01' and x.trans_time < '2021-02-01')
            AND x.sales_channel_name = 'Consumer') b on b.partner_id=a.partner_id 
	and a.product_sku=b.product_sku and a.bi_sellin_line_id=b.bi_sellin_line_id
	where isnull(a.pending_score, 0)!= isnull(b.pending_score, 0) --or b.pending_score is null
	) xx1  group by xx1.partner_id 