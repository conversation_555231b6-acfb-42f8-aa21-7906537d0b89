var rpcClient = common.rpcClient;
var loadMask;
var productConditionQuery = null;

//引入执行的controller
var fooProductManage = new $.JsonRpcClient({
	ajaxUrl: '/wxPublicRpc.do'
});

BUI.use('bui/mask', function (Mask) {
	loadMask = new Mask.LoadMask({
		el: 'body',
		msg: '加载数据中'
	});
});



$(document).ready(function(){	
	getOrderDetail();	
	$(":radio").click(function(){
		showAddressType($(this).val());
	});
});
var partnerId = null;
var partnerOrderLineVoLst = [];
var partnerOrderAddressProductList = [];
function getOrderDetail(){
	rpcClient.call('partnerOrderService.queryPartnerOrderDetail', [""+partnerOrderId],
		function (result) {
			if (result.code == "success") {
				showDetail(result.partnerOrderVo,result.orderLineVoLst,result.partnerOrderDetailLstForAll,result.addressProduct);
			} else {
				common.alertMes(result.errorMsg, 'error');
			}
		}
	);
}

var partnerProperty = null;
function getPartnerProperty(partnerId) {
	rpcClient.call('partnerServiceImpl.getPartnerProperty',[partnerId],
		function (result) {
			partnerProperty = result;
		},function(error){
			common.ajaxTimeout(error);
		});
}
var initPage = true;
function showDetail(partnerOrderVo,orderLineVoLst,partnerOrderDetailLstForAll,addressProductList){
	//显示合伙人名称  订单编号
	$("#partnerName").text(partnerOrderVo.partnerName);
	$("#orderNo").text(partnerOrderVo.orderNo);
	/*$("#remark").text(partnerOrderVo.remark);*/
	partnerId = partnerOrderVo.partnerId;
	
	getPartnerSaleConfig(partnerId,partnerOrderVo.partnerSaleConfigId);
	
	$("#statusMeaning").html(partnerOrderVo.statusMeaning);
	if(partnerOrderVo.status != '4'){
		$("#resubmitBtn").hide();
		$("#returnBtn").hide();
	}
	getPartnerProperty(partnerOrderVo.partnerId);
	//加载合伙人地址空间
	initAddress(partnerOrderVo.partnerId,null);
	initAddressProduct(partnerOrderVo.partnerId);
	
	/*//加载赠品列表
	orderLineGiftList = orderLineGiftLst;
	if(orderLineGiftLst.length >0){
		for(var i = 0;i < orderLineGiftLst.length; i++ ){
			var orderLineGift = orderLineGiftLst[i];
			var sku = orderLineGift.promotionSku;
			if(sku == null){
				sku = orderLineGift.sku;
			}
			orderLineGiftSkuList.push(sku);
			orderLineGiftSkuMap[sku] = orderLineGift;
		}
	}*/
	//加载订单产品
	initProductDictItem();
	partnerOrderLineVoLst = orderLineVoLst;
	queryProductsByParam();
	
	/*listGiftSelected(orderLineGiftList);*/
	partnerOrderAddressProductList = addressProductList;
	$("#productOneAddressGrid").show();
	listSapOrderLineSelected(partnerOrderDetailLstForAll);
}

var partnerSaleConfigCtrl = null;
var partnerSaleConfigList = [];
function getPartnerSaleConfig(partnerId,partnerSaleConfigId) { 
	var items = [];
	rpcClient.call('partnerOrderService.getPartnerSaleConfig',[partnerId],
		function (result) {
		if (result.code == 'success') {
			if(result.resultLst != null && result.resultLst.length > 0){
				$("#partnerSaleConfig_area").show();
				
				items.push({
					text : '一般订单',
					value : '' ,
					quotaRemainderDetail:''
				});
				for(var i = 0; i < result.resultLst.length; i++){					
					var item = result.resultLst[i];
					items.push({
						text : item.purpose,
						value : item.id,
						quotaRemainderDetail:item.quotaRemainderDetail
					});
					partnerSaleConfigList.push({
						id:item.id,
						partnerSaleConfig:item
					});
				}				
				if(partnerSaleConfigCtrl == null){
					BUI.use('bui/select',function(Select){
						partnerSaleConfigCtrl = new Select.Select({  
							  render:'#partnerSaleConfig_c',
							  valueField:'#partnerSaleConfig',
							  elCls: 'control-select',
							  elStyle: {'white-space': 'nowrap'},
							  multipleSelect:false,
							  items:items
						});
						partnerSaleConfigCtrl.render();
						partnerSaleConfigCtrl.on('change', function(ev){
							$("#partnerSaleConfigSpan").html( ev.item.quotaRemainderDetail);
						});
					});
				}else{
					partnerSaleConfigCtrl.set('items', items);
				}
				$("#partnerSaleConfig_area .control-select .bui-select-input, .bui-select .bui-select-input, .bui-combox .bui-combox-input").css("width","400px");
				if(partnerSaleConfigId != null && partnerSaleConfigId != ""){
					partnerSaleConfigCtrl.setSelectedValue(partnerSaleConfigId);
				}
				partnerSaleConfigCtrl.disable();
			}else{
				$("#partnerSaleConfig_area").hide();
			}			
		} else {
			common.alertMes('加载价格配置失败','error');
		}
			
		},function(error){
			common.ajaxTimeout(error);
		});
}


var addressCtrl = null;
function initAddress(partnerId,addressId) {
	var items = [];

	rpcClient.call('partnerAddressService.selAddressByPartnerId',[partnerId],
			function (result) {
				if (result.success) {
					for(var i = 0; i < result.addressList.length; i++){
						
						var item = result.addressList[i];
						items.push({
							text : item.provinceName + " "+ item.cityName + " " + item.distName + " " + item.address + " " + item.mobile,
							value : item.id 
						});
					}
					if(addressCtrl == null){
						BUI.use('bui/select',function(Select){
							addressCtrl = new Select.Select({  
						          render:'#partnerAddressHid_c',
						          valueField:'#partnerAddressHid',
						          elCls: 'control-select',
						          elStyle: {'white-space': 'nowrap'},
						          multipleSelect:false,
						          items:items
						  	});
							addressCtrl.render();
						});
					}else{
						addressCtrl.set('items', items);
					}
					addressCtrl.setSelectedValue(addressId);
					if(addressId == null && items.length > 0 ){
						addressCtrl.setSelectedValue(items[0].value);
					}
				} else {
					common.alertMes('加载地址失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
}
var addressProductCtrl = null;
function initAddressProduct(partnerId,addressId) {
	var items = [];

	rpcClient.call('partnerAddressService.selAddressByPartnerId',[partnerId],
			function (result) {
				if (result.success) {
					for(var i = 0; i < result.addressList.length; i++){
						
						var item = result.addressList[i];
						items.push({
							text : item.provinceName + " "+ item.cityName + " " + item.distName + " " + item.address + " " + item.mobile,
							value : item.id 
						});
					}
					if(addressProductCtrl == null){
						BUI.use('bui/select',function(Select){
							addressProductCtrl = new Select.Select({  
						          render:'#partnerAddressProductHid_c',
						          valueField:'#partnerAddressProductHid',
						          elCls: 'control-select',
						          elStyle: {'white-space': 'nowrap'},
						          multipleSelect:false,
						          items:items
						  	});
							addressProductCtrl.render();
						});
					}else{
						addressProductCtrl.set('items', items);
					}
					addressProductCtrl.setSelectedValue(addressId);
					if(addressId == null && items.length > 0 ){
						addressProductCtrl.setSelectedValue(items[0].value);
					}
				} else {
					common.alertMes('加载地址失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
}
var partnerOrderMinLiterCount = 0;
function initProductDictItem() {
	rpcClient.call('productServiceImpl.queryProductDictItem',[],
			function (result) {
				if (result.code = "success") {
					var categoryList = result.category;
					var categoryHtml = '';
					for(var i = 0; i < categoryList.length; i++){
						var category = categoryList[i];
						categoryHtml = categoryHtml + '<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="category" value="' + 
						category.dicItemCode + '">' + category.dicItemName + '</label>';
					}
					$("#categoryDiv").html(categoryHtml);	
					
					var viscosityList = result.viscosity;
					var viscosityHtml = '';
					for(var i = 0; i < viscosityList.length; i++){
						var viscosity = viscosityList[i];
						viscosityHtml = viscosityHtml +'<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="viscosity" value="' + 
						viscosity.dicItemCode + '">' + viscosity.dicItemName + '</label>';
					}
					$("#viscosityDiv").html(viscosityHtml);
					
					var oiltypeList = result.oiltype;
					var oiltypeHtml = '';
					for(var i = 0; i < oiltypeList.length; i++){
						var oiltype = oiltypeList[i];
						oiltypeHtml = oiltypeHtml + '<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="oiltype" value="' + 
						oiltype.dicItemCode + '">' + oiltype.dicItemName + '</label>';
					}
					$("#oiltypeDiv").html(oiltypeHtml);
					
					var capacityList = result.capacity;
					var capacityHtml = '';
					for(var i = 0; i < capacityList.length; i++){
						var capacity = capacityList[i];
						capacityHtml = capacityHtml + '<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="capacity" value="' + 
						capacity.dicItemCode + '">' + capacity.dicItemName + '</label>';
					}
					$("#capacityDiv").html(capacityHtml);	
					$("input[type='checkbox']").click(function() {  
						var partnerId = $("#partnerId").val();
						if(partnerId != ''){
							queryProductsByParam( );
						}
					});
					partnerOrderMinLiterCount = result.partnerOrderMinLiterCount;
				} else {
					common.alertMes('加载地址失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
}

//模糊查询
function queryProductsByParam( ) {	
	productConditionQuery = new Object();
	
	var categoryList = $("[name = category]:checkbox")/*.attr("checked")*/;
	var categorySelectList = new Array();	
	for(var i = 0 ; i < categoryList.length ; i++){
		var categoryCheck = categoryList[i];
		if($(categoryCheck).prop("checked")){
			categorySelectList.push($(categoryCheck).val());
		};
	}
	productConditionQuery.categoryList = categorySelectList;
	
	var viscosityList = $("[name = viscosity]:checkbox")/*.attr("checked")*/;
	var viscositySelectList = new Array();	
	for(var i = 0 ; i < viscosityList.length ; i++){
		var viscosityCheck = viscosityList[i];
		if($(viscosityCheck).prop("checked")){
			viscositySelectList.push($(viscosityCheck).val());
		};
	}
	productConditionQuery.viscosityList = viscositySelectList;
	
	var oiltypeList = $("[name = oiltype]:checkbox")/*.attr("checked")*/;
	var oiltypeSelectList = new Array();	
	for(var i = 0 ; i < oiltypeList.length ; i++){
		var oiltypeCheck = oiltypeList[i];
		if($(oiltypeCheck).prop("checked")){
			oiltypeSelectList.push($(oiltypeCheck).val());
		};
	}
	productConditionQuery.oilTypeList = oiltypeSelectList;
	
	var capacityList = $("[name = capacity]:checkbox")/*.attr("checked")*/;
	var capacitySelectList = new Array();	
	for(var i = 0 ; i < capacityList.length ; i++){
		var capacityCheck = capacityList[i];
		if($(capacityCheck).prop("checked")){
			capacitySelectList.push($(capacityCheck).val());
		};
	}
	productConditionQuery.capacityList = capacitySelectList;
	
	listToJsonProductsByParam("","", "", productConditionQuery,partnerId);
};
function clearSelectionPra(){
	$("input[type='checkbox']").attr("checked",false);
	if(partnerId != ''){
		queryProductsByParam( );
	}
}

var curPage = null;
//根据条件显示产品信息列表
var productSelected = [];
var productSelectedBef = [];
var tableData;
var productGrid;
function listToJsonProductsByParam(queryField, sku, name, productConditionQuery,partnerId ) {
	LoadMask.show();
	rpcClient.call('productServiceImpl.queryProductAndPartnerInventoryByMultiParam', [queryField, sku, name, productConditionQuery,partnerId],
		function (result) {
			if ("syserror" == result.code) {
				$("#grid").html('系统错误..');
			} else {
				$("#grid").html("");
				tableData = result.lst;
				$.each(tableData, function (i, value) {
					if (value.isCollect == 1) {
						value.isCollect = "是";
					} else if (value.isCollect == 0) {
						value.isCollect = "否";
					}
					if (value.isCompeting == 1) {
						value.isCompeting = "是";
					} else if (value.isCompeting == 0) {
						value.isCompeting = "否";
					}
				});
				
				// 初始化已选择产品
				for (var k = 0; k < tableData.length; k++) {
					for (var i = 0; i < partnerOrderLineVoLst.length; i++) {
						if (tableData[k].id == partnerOrderLineVoLst[i].productId) {
							if(partnerOrderLineVoLst[i].promotionSku == null){
								tableData[k].count = partnerOrderLineVoLst[i].amount;
								tableData[k].capacityTotalAmount = partnerOrderLineVoLst[i].boxCapacity * partnerOrderLineVoLst[i].amount;
							}else{
								if(partnerOrderLineVoLst[i].promotionSku == partnerOrderLineVoLst[i].sku){
									tableData[k].count = partnerOrderLineVoLst[i].amount;
									tableData[k].capacityTotalAmount = partnerOrderLineVoLst[i].boxCapacity * partnerOrderLineVoLst[i].amount;
								}
							}
						}
					}
				}
				
				// 如果不是搜索框查询（门店名称修改），刷新临时保存数据列表-robjiang
				if (productSelectedBef.length == 0) {
					productSelectedBef = tableData;
				} else {
					for (var k = 0; k < tableData.length; k++) {
						for (var i = 0; i < productSelectedBef.length; i++) {
							if (tableData[k].id == productSelectedBef[i].id) {
								if(productSelectedBef[i].promotionSku == null){
									tableData[k].count = productSelectedBef[i].count;
									tableData[k].capacityTotalAmount = productSelectedBef[i].capacityTotalAmount;
								}
							}
						}
					}
				}
				productSelected = tableData;
				listProductSelected(tableData);
				if(initPage == true){
					var selections = [];
					var selectedLength = productSelectedBef.length;
					if(selectedLength > 0){						
						for(var i = 0;i < selectedLength; i ++){
							var item = productSelectedBef[i];
							var capacity = item.capacity;
							var count = item.count;
							if(typeof(item.count) != "undefined" && item.count > 0){
								selections.push(item);	
							}
						}
					}
					/*if(selections.length == 0){
						common.alertMes("请确认相关产品！", 'error');
						return;
					}*/
					$("#setupBtn").hide();
					$("#queryDiv").hide();
					$("#resubmitBtn").show();
					$("#returnBtn").show();
					$("#addressChoice").show();
					/*listProductSelected(selections);*/
					$("#productSelectedGrid").hide();
					$("#address").hide();
					
					//加载地址
					
					if(partnerOrderAddressProductList.length > 1){
					    
					    getProductConfirmedArray();
						tempProductConfirmArrayForAddress = [];
						var selectedLength = productConfirmArrayForAddress.length;
						if(selectedLength > 0){						
							for(var i = 0;i < selectedLength; i ++){	
								var item = productConfirmArrayForAddress[i];
								var newAddress = $.extend({},item);
								tempProductConfirmArrayForAddress.push(newAddress);	
							}
						}
								    
					    for(var k = 0 ; k < partnerOrderAddressProductList.length; k++ ){
					    	var addressProduct = partnerOrderAddressProductList[k];
					    	
					    	if(addressProductID.length == 0){
					    		addressProductID.push(addressProduct.addressId );
					    	}else{
						    	for(var i = 0; i < addressProductID.length; i++){
				            		if(addressProduct.addressId == addressProductID[i]){
				            			break;
				            		}
				            		addressProductID.push(addressProduct.addressId );
				            	}
					    	}
			            								    	
					    	var addressHtml = '<div id="block' + addressProduct.addressId + '">'+
				    		'<div class="info-block-header" >' + addressProduct.address +
					    		'&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" onclick="creatAddressProduct(' +addressProduct.addressId + ')">修改数量</a>'+
			            		'&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" onclick="deleteBlock(' + addressProduct.addressId + ')">删除地址</a>'+
			            		'<i class="icon" onclick="showBlock(this);"></i>'+
				    			'</div>' + 
				    			'<div class="info-block">'+ 
					    			'<div class="cols-6">'		+ 		
					    				'<div class="field-group">'+ 
					    					'<div class="control-group" id="grid'+addressProduct.addressId+'">'+ 	            						
					    					'</div>'+ 
					    				'</div>'+ 
					    			'</div>	'+ 
					    		'</div>'+ 
					    	'</div>' ;
					    	$("#addressProductContent").append(addressHtml);
					    	
					    	
					    	var addressProductList = []/*$.extend(true,{},tempProductConfirmArrayForAddress)*/;
							
							var selectedLength = tempProductConfirmArrayForAddress.length;
							if(selectedLength > 0){						
								for(var i = 0;i < selectedLength; i ++){	
									var item = tempProductConfirmArrayForAddress[i];
									var addressProductDetailList = addressProduct.addressProduct;
									for(var m = 0; m < addressProductDetailList.length; m ++){
										if(item.id == addressProductDetailList[m].productId){
											var itemTemp = $.extend(true,{},item);
											itemTemp.addressId = addressProduct.addressId;
											itemTemp.count = addressProductDetailList[m].amount;
											itemTemp.tempCount = itemTemp.count;
											addressProductList.push(itemTemp);
											addressProductArray.push(itemTemp);	
											
											for(var j = 0; j < productConfirmArrayForAddress.length; j++){
												var tempProduct = productConfirmArrayForAddress[j];
												if(item.id == tempProduct.id){
													tempProduct.count = tempProduct.count - itemTemp.tempCount;
												}
											}
										}
									}
								}
							}	
				    		            	 
					    	listAddressProductSpecified(addressProduct.addressId,addressProductList);
					    }
					    
					    $("#productSelectedGrid").hide();
						$("#address").hide();
						$("#productAddress").show();
					    
					}else{

					    if(partnerOrderAddressProductList.length > 0){
					    	addressCtrl.setSelectedValue(partnerOrderAddressProductList[0].addressId);
					    }
					    
					    $("#productAddress").hide();
						$("#productSelectedGrid").hide();
						$("#address").show();
					}
				}
				initPage = false;
				LoadMask.hide();
			}
		}, function (error) {
			loadMask.hide();
			common.ajaxTimeout(error);
		});
}

var selectGrid;
function listProductSelected(selections) {
	if (selections == null) {
		$("#productSelectedGrid").html('没有选择任何产品');
	} else {
		$("#productSelectedGrid").html("");
		var tableData = selections;
		BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '序号', dataIndex: 'productIndex', width: '65px',sortable: false },
						{ title: '产品编号', dataIndex: 'sku', width: '20%' },
						{ title: '产品信息', dataIndex: 'name', width: '40%',
							renderer:function(value,item,index){
								var text = '';
								if (/^.*(ZWL|ZLE)$/.test(item.sku)) { // 产品包
									text = item.remark || value; // 后端封装好了样式
								} else { // 单个产品
									text = value;
									if(item.iconId){
										text = '<div class="photo-icon"><img src="'
											+common.ctx+'downloadAttachmentFile.do?sourceType=5&attId='
											+item.iconId+'"/></div><span>'+value + '</span>';
									}
								}
								return text;
							}},
						/*{ title: '产品类别', dataIndex: 'category', width: '15%' },*/
						{ title: '规格', dataIndex: 'viscosity', width: '8%' },
						{ title: '升/箱', dataIndex: 'boxCapacity', width: '80px', elCls: 'text-right', renderer: function(value, item, index){
							if(value){
								value = value + '';
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },						
						/*{ title: '零售价格(元)', dataIndex: 'salePrice', elCls: 'text-right', width: '120px',summary:true},*/
						{
							title : '库存',
							dataIndex : 'inventoryCount',
							elCls: 'text-right',
							width : '80px',
							renderer : function(value,obj,index){
								return value;
							}
						},
						{
							title : '订货数量(箱)',
							dataIndex : 'count',
							sortable: false,
							width : '100px',
							elCls: 'text-right',
							renderer : function(value,obj,index){
								if(value != null){
									return '<input id="'+obj.id+'" onfocus="this.select();"  min="1" max="10000" style="width:60px;height:25px;font-size:18px;padding-right:15px;" ' + 
										'value="'+value+'" class="text-right quantity-input" onchange="setProductBuyCount(\''+index+'\',\''+obj.id+'\')"></input>';
								}else{
									return '<input id="'+obj.id+'" onfocus="this.select();"  min="1" max="10000" style="width:60px;height:25px;font-size:18px;padding-right:15px;" ' + 
									'class="text-right quantity-input" onchange="setProductBuyCount(\''+index+'\',\''+obj.id+'\')"></input>';
								}
				        	}
						},{
							title : '合计升数(L)',
							dataIndex : 'capacityTotalAmount',
							elCls: 'text-right',
							sortable: false,
							width : '80px',
							renderer : function(value,obj,index){
								if(value == null){
									return '<span id="' + obj.id + 'capacityTotalAmount' + '"></span>';
								}else{
									return '<span id="' + obj.id + 'capacityTotalAmount' + '">' + value.toFixed(2)  +'</span>';
								}
							}
						}],
					store = new Store({
						data: tableData,
						autoLoad: true,
						pageSize:100 
					}),

					editing = new Grid.Plugins.CellEditing(
							{
								triggerSelected : false //触发编辑的时候不选中行
							});
					selectGrid = new Grid.Grid({
						render: '#productSelectedGrid',
						columns: columns,
						loadMask: true, // 加载数据时显示屏蔽层
						stripeRows: false,
						store: store,
						itemStatusFields : {
			    			selected : 'checked',
			    			disabled : 'disable'
			            },
						plugins: [editing,Grid.Plugins.AutoFit],
						bbar: { pagingBar:true,width:'100%'}
					});	
					selectGrid.on('aftershow', function(e) {
						calculateOrderTotalLiterAndAmount(tableData);
					});
					
					selectGrid.render();
					common.initGrid(selectGrid, [], true);
				if (curPage) {
					grid.get('bbar').getItemAt(0).jumpToPage(curPage);
					curPage = null;
				}
			});
	}
}
//显示赠送数量
var giftGrid;
function listGiftSelected(giftList) {
	if(giftList == null || giftList.length == 0) {
		$("#productGiftDiv").hide();
		$("#productGiftGrid").html('没有任何赠品');
	}else{
		$("#productGiftDiv").show();
		$("#productGiftGrid").html("");
		var tableData = giftList;
		BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '产品编号', dataIndex: 'sku', width: '20%' },
						{ title: '产品信息', dataIndex: 'productName', width: '65%',
							renderer:function(value,item,index){
								var text = '';
								if (/^.*(ZWL|ZLE)$/.test(item.sku)) { // 产品包
									text = item.remark || value; // 后端封装好了样式
								} else { // 单个产品
									text = value;
									if(item.iconId){
										text = '<div class="photo-icon"><img src="'
											+common.ctx+'downloadAttachmentFile.do?sourceType=5&attId='
											+item.iconId+'"/></div><span>'+value + '</span>';
									}
								}
								return text;
							}},
						/*{ title: '产品类别', dataIndex: 'category', width: '15%' },*/
						{ title: '规格', dataIndex: 'viscosity', width: '15%' },
						{ title: '升/箱', dataIndex: 'boxCapacity', width: '80px', elCls: 'text-right', renderer: function(value, item, index){
							if(value){
								value = value + '';
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },						
						{
							title : '赠送数量(箱)',
							dataIndex : 'freeAmount',
							sortable: false,
							width : '120px',
							elCls: 'text-right',
							renderer : function(value,obj,index){
								return value;
				        	}
						},
						{
							title : '促销活动',
							dataIndex : 'promotionTitle',
							sortable: false,
							width : '200px',
							elCls: 'text-right',
							renderer : function(value,obj,index){
								return value;
				        	}
						},
						{
							title : '合计升数(L)',
							dataIndex : 'totalLiterCount',
							elCls: 'text-right',
							sortable: false,
							width : '130px',
							renderer : function(value,obj,index){
								if(value != null){
									return value.toFixed(2);
								}

							}
						}],
					store = new Store({
						data: tableData,
						autoLoad: true,
						pageSize:100 
					}),

					giftGrid = new Grid.Grid({
						render: '#productGiftGrid',
						columns: columns,
						loadMask: true, // 加载数据时显示屏蔽层
						stripeRows: false,
						store: store,
						itemStatusFields : {
			    			selected : 'checked',
			    			disabled : 'disable'
			            },
						plugins: [Grid.Plugins.AutoFit],
						bbar: { pagingBar:true,width:'100%'}
					});	
					
					giftGrid.render();	
					common.initGrid(giftGrid, [], true);
			});
	}
}

// 购买数量修改
function setProductBuyCount(index,value){
	var productObj = selectGrid.getItemAt(index); 
	var countInput = parseInt($("#"+value+"").val());
	if(countInput != ''){
		if(!common.checkInt(countInput)){
			common.alertMes("购买数量只能是大于0的整数！", 'error');
			$("#"+value+"").val(productObj.count);
			return;
		}
		countInput = parseInt($("#"+value+"").val());
	}else{
		countInput = 0;
	}

	var productObjCount = 0;
	var sku = productObj.sku;
	
	
	productObjCount = parseInt(productObj.count);
	if(typeof(productObj.count) == "undefined" || countInput > productObjCount){
		for(var i = 0; i < productSelected.length; i++){
			var tempProduct  = productSelected[i];
			if(tempProduct.id == value){
				if(countInput > 0){					
					tempProduct.count = countInput;
					tempProduct.capacityTotalAmount = tempProduct.boxCapacity * tempProduct.count;
					break;
				}else{
					countInput = 0;
					if(countInput < tempProduct.count){
						//如果修改的数量小于原来的数量
						clearAllAddressProduct();
					}
					tempProduct.count = 0;
					tempProduct.capacityTotalAmount = 0;
					break;
				}
			}
		}
		// 修改购买数量时，保存至临时列表中-robjiang
		for(var i = 0; i < productSelectedBef.length; i++){
			var tempProduct  = productSelectedBef[i];
			if(tempProduct.id == value){
				tempProduct.count = $("#"+value+"").val();
				var boxCapacity = 0;
				if(tempProduct.boxCapacity != ''){
					boxCapacity = parseInt(tempProduct.boxCapacity);
				}
				tempProduct.capacityTotalAmount = boxCapacity * tempProduct.count;
				break;
			}
		}			
	}else if(productObjCount > 0 && countInput < productObjCount){
		common.confirmMes('订货数量小于原有订货数量，订单发货地址将需要重新分配，确认修改？',function(){
			 setTimeout(function(){
				 $("#"+value+"").val(countInput);
				 for(var i = 0; i < productSelected.length; i++){
					 var tempProduct  = productSelected[i];
					 if(tempProduct.id == value){
						 if(countInput > 0){					
							tempProduct.count = countInput;
							var capacity = 0;
							if(tempProduct.boxCapacity != ''){
								capacity = parseFloat(tempProduct.boxCapacity);
							}
							tempProduct.capacityTotalAmount = capacity * tempProduct.count;
							$("#" + value + "capacityTotalAmount").text(tempProduct.capacityTotalAmount);
							break;
						}else{
							countInput = 0;
							if(countInput < tempProduct.count){
								//如果修改的数量小于原来的数量
								//clearAllAddressProduct();
							}
							tempProduct.count = 0;
							tempProduct.capacityTotalAmount = 0;
							break;
						}
					}
				}
				// 修改购买数量时，保存至临时列表中-robjiang
				for(var i = 0; i < productSelectedBef.length; i++){
					var tempProduct  = productSelectedBef[i];
					if(tempProduct.id == value){
						tempProduct.count = $("#"+value+"").val();
						var boxCapacity = 0;
						if(tempProduct.boxCapacity != ''){
							boxCapacity = parseInt(tempProduct.boxCapacity);
						}
						tempProduct.capacityTotalAmount = capacity * tempProduct.count;
						break;
					}
				}	
			 }, 0);
		 },'question');
		$("#"+value+"").val(productObj.count);
	}	
}

var productSelectedSap = [];
var partnerSaleConfig = null;
var skuUsingPartnerSaleConfigMap = null;
var salesOrg = null;
var salesCode = null;
function getSapPartnerOrderLine(){
	//获取所有有数量的产品
	var selections = [];
	var selectedLength = productSelectedBef.length;
	if(selectedLength > 0){						
		for(var i = 0;i < selectedLength; i ++){
			var item = productSelectedBef[i];
			var capacity = item.capacity;
			var count = item.count;
			if(typeof(item.count) != "undefined" && item.count > 0){
				selections.push(item);	
			}
		}
	}
	var orderLines = [];
	$(selections).each(function (i, item) {
		var orderline = {};
		orderline.productId = item.id;
		orderline.sku = item.sku;
		orderline.productName = item.name;
		orderline.amount = item.count;
		orderline.bottlesperbox = item.bottlesperbox;
		orderline.capacity = item.boxCapacity;
		if(parseInt(item.boxCapacity) == 200 ){
			orderline.units = "barrel";
		}else{
			orderline.units = "box";
		}
		/*orderline.price = item.salePrice;*/
		/*orderline.ladderPrice = item.ladderPrice;
		orderline.discountedPrice = item.discountedPrice;*/
		orderline.type = item.category;
		orderline.inventoryCount = item.inventoryCount;
		orderLines.push(orderline);
		
	});
	var partnerSaleConfigId = null;
	if(partnerSaleConfigCtrl != null){
		partnerSaleConfigId = partnerSaleConfigCtrl.getSelectedValue();
	}
	LoadMask.show();
	rpcClient.call("partnerOrderService.getSapOrderUtil", [partnerId,partnerSaleConfigId,orderLines],
	        function (result) {
	            LoadMask.hide();
	            if (result.code == "success") {
	            	salesOrg = result.salesOrg;
	            	salesCode = result.salesCode;
	            	
	            	if(result.partnerSaleConfig != null){
	            		partnerSaleConfig = result.partnerSaleConfig;
	            		/*if(partnerSaleConfig.quotaRatio != null){
	            			var quotaRatio = partnerSaleConfig.quotaRatio;
	            			var sapOrderLineList = result.sapOrderLineList;
	            			$(sapOrderLineList).each(function (i, item) {
	            				item.remark = "下单数量：" + item.count + ",自动配额数量：" + item.count * (quotaRatio -1);
	            				item.count = item.count * quotaRatio;
	            				item.capacityTotalAmount = item.capacityTotalAmount * quotaRatio;
	            				item.totalValue = item.totalValue * quotaRatio;
	            			});
	            			result.sapOrderLineList = sapOrderLineList;
	            			var orderLineDetailList = result.orderLineDetailList;
	            			$(orderLineDetailList).each(function (i, item) {
	            				item.remark = "下单数量：" + item.actualAmount + ",自动配额数量：" + item.actualAmount * (quotaRatio -1);
	            				item.actualAmount = item.actualAmount * quotaRatio;
	            				item.amount = item.amount * quotaRatio;
	            				item.totalLiterCount = item.totalLiterCount * quotaRatio;
	            				item.totalValue = item.totalValue * quotaRatio;
	            				
	            			});
	            			result.orderLineDetailList = orderLineDetailList;
	            		}	*/ 
	            	}
	            	productSelectedSap = result.sapOrderLineList;
	            	/*listSapOrderLineSelected(result.sapOrderLineList);*/
	            	listSapOrderLineSelected(result.orderLineDetailList);
	            	calculateOrderTotalLiterAndAmount(productSelectedSap);
	            	getProductConfirmedArray();
	            	// 满足折扣满减后，提示用户
	            	confirmPromoteOrderRemind();

	            	if(result.saleConfigValidate == false){		            		
	            		/*common.alertMes(result.alertMsg + "购买升数大于优惠额度，将不能获得优惠价", 'error');	  */
	            		common.alertMes("您购买的产品升数已经大于给您分配的优惠额度，不能下单，请调整到可用额度内进行下单", 'error');
	            		$("#resubmitBtn").attr("disabled","disabled");
	        		}else{
	        			$("#resubmitBtn").attr("disabled",false);
	        		}
	            	skuUsingPartnerSaleConfigMap = result.skuUsingPartnerSaleConfigMap;
	            } else{
	                common.alertMes(result.errorMsg, "error");
	            }
	        }, function (error) {
	            common.ajaxTimeout(error);
	            LoadMask.hide();
	        });
	
}
var newOrderLiterCount = 0;
var SNAboveTotalLiterCount = 0;
var SNBelowTotalLiterCount = 0;
function calculateOrderTotalLiterAndAmount(productSelectedArray){
	var selects = productSelectedArray;
	var selectedLength = selects.length;
	var totalCount = 0;
	newOrderLiterCount = 0;
	SNAboveTotalLiterCount = 0;
	SNBelowTotalLiterCount = 0;
	var orderTotalAmount = 0;
	var isSkuCountRebate = false;
	if(selectedLength > 0){						
		for(var i = 0;i < selectedLength; i ++){
			var item = selects[i];
			var boxCapacity = item.boxCapacity;
			var count = item.count;
			if(typeof(item.count) != "undefined" && item.count > 0 && typeof(item.capacity) != "undefined" && item.boxCapacity > 0){			
				totalCount = totalCount + boxCapacity * count;	
				if(item.productProperty == "SN Above"){
					SNAboveTotalLiterCount = SNAboveTotalLiterCount + boxCapacity * count;
				}else if(item.productProperty == "SN Below"){
					SNBelowTotalLiterCount = SNBelowTotalLiterCount + boxCapacity * count;
				}
			}			
		}
	}
	newOrderLiterCount = totalCount.toFixed(2);
	$("#totalLiterCount").html(totalCount.toFixed(2));
	if(partnerSaleConfig != null){
		var quotaRemainder = partnerSaleConfig.quotaRemainder;
		if(quotaRemainder < newOrderLiterCount){
			common.alertMes("购买总升数大于Quato剩余量", 'error');
			$("#createBtn").hide();
		}else{
			$("#createBtn").show();
		}
	}else{
		$("#createBtn").show();
	}
}
function setupBill(){
	if(typeof(productSelected) == "undefined"){
		common.alertMes("请确认相关产品！", 'error');
		return;
	}
	var selections = [];
	var selectedLength = productSelectedBef.length;
	if(selectedLength > 0){						
		for(var i = 0;i < selectedLength; i ++){
			var item = productSelectedBef[i];
			var boxCapacity = item.boxCapacity;
			var count = item.count;
			if(typeof(item.count) != "undefined" && item.count > 0){
				selections.push(item);	
			}
		}
	}
	if(selections.length == 0){
		common.alertMes("请确认相关产品！", 'error');
		return;
	}
	// confirmPromoteCalcRemind(function () {
		$("#setupBtn").hide();
		$("#queryDiv").hide();
		$("#resubmitBtn").show();
		$("#returnBtn").show();
		$("#addressChoice").show();
		$("#address").hide();
		$("#priceDiv").show();
		$("#productOneAddressGrid").show();
		$("#productSelectedGrid").hide();
		getSapPartnerOrderLine();	
		partnerSaleConfigCtrl.disable();
	// })
}
function returnToProductSelections(){
	
	$("#setupBtn").show();
	$("#queryDiv").show();
	$("#resubmitBtn").hide();
	$("#address").hide();
	$("#returnBtn").hide();
	$("#addressChoice").hide();
	$("#productOneAddressGrid").hide();
	$("#priceDiv").show();
	$("#productSelectedGrid").show();
	listProductSelected(productSelectedBef);
	partnerSaleConfigCtrl.enable();
	
	
}
function clearAllAddressProduct(){
	$(":radio").each(function(){
		$(this).attr("checked", false);
	});
	productConfirmArrayForAddress = [];
	addressProductID = [];
	addressProductArray = [];
	$("#productOneAddressGrid").html("");
	$("#productAddress").hide();
	$("#addressProductContent").html("")
}
function confirmCreatePartnerOrder(){
	common.confirmMes('确定提交订单吗?',function(){
		 setTimeout(function(){
			 createPartnerOrder();
		 }, 0);
	 },'question');
}
function confirmReturn(){
	common.confirmMes('确定返回订单列表吗?',function(){		 
		 setTimeout(function(){
			 window.history.go(-1);
		 }, 0);
	 },'question');
}
var sapOrderLineGrid;
function listSapOrderLineSelected(selections) {
	if (selections == null || selections.length == 0) {
		$("#productOneAddressGrid").html("Sales Org:" + salesOrg + ", Sales Code:" + salesCode + "没有配置价格");
	} else {
		$('#productOneAddressGrid').html("");
		var orderLineColumns = [];
		orderLineColumns = [{ 
			title: '产品编号', 
			dataIndex: 'sku', 
			sortable : false,
			width: '100px' 
		},{ 
			title: '产品名称', 
			dataIndex: 'productName', 
			width: '50%',
			sortable : false,
			renderer:function(value,item,index){
				var text = '';
				if (/^.*(ZWL|ZLE)$/.test(item.sku)) { // 产品包
					text = item.remark || value; // 后端封装好了样式
				} else { // 单个产品
					text = value;
					if(item.productPhoto){
						text = '<div class="photo-icon"><img src="'
							+common.ctx+'downloadAttachmentFile.do?sourceType=5&attId='
							+item.productPhoto+'"/></div><span>'+value + '</span>';
					}
				}
				return text;
			}
		},{
			title : '订货数量 + 赠送数量',
			dataIndex : 'actualAmount',
			width : '140px',
			sortable : false,
			elCls : 'text-right',
			renderer : function(value,obj,index){
				var units = "瓶";
				if (obj.units != null) {
					if(obj.units == "box"){
						units =  "箱";
					}else if(obj.units == "barrel"){
						units =  "桶";
					}else if(obj.units == "package"){
						units =  "";
					}
				} 					
				if(obj.freeAmount != null){
					return value + " + " + obj.freeAmount +  units;
				}else{
					return value + units;
				}
			}
		},{
			title: '合计升数(L)',
			dataIndex: 'totalLiterCount',
			elCls: 'text-right',
			sortable: false,
			summary: true,
			width: '90px',
			renderer: function (value, obj, index) {
				if(value != null && !isNaN(value)){
					return value.toFixed(2);
				}else{
					return "";
				} 
			}
		},{ 
			title: '促销活动', 
			dataIndex: 'promotionTitle', 
			elCls: 'text-right', 
			width: '180px',
			sortable : false,
			summary:false,
			renderer : function(value,obj,index){
				return value;
			}
		}, { 
			title: '价格(元)', 
			dataIndex: 'ladderPrice', 
			elCls: 'text-right', 
			width: '90px',
			sortable : false,
			summary:false,
			renderer : function(value,obj,index){
				if(value != null && !isNaN(value)){
					if(value > 0){
						return value.toFixed(2);
					}else{
						return "赠品";
					}					
				}else{
					return "赠品";
				} 
			}
		},{ 
			title: '合计金额(元)', 
			dataIndex: 'totalValue', 
			elCls: 'text-right', 
			width: '90px',
			sortable : false,
			summary:true,
			renderer : function(value,obj,index){
				if(value != null && !isNaN(value)){
					if(value > 0){
						return value.toFixed(2);
					}else{
						return "";
					}
				}else{
					return "";
				} 
			}
		},{ 
			title: '备注', 
			dataIndex: 'remark', 
			elCls: 'text-right', 
			width: '50%',
			sortable : false,
			summary:false,
			renderer : function(value,obj,index){
				return value;
			}
		}];
				
		BUI.use(['bui/extensions/treegrid','bui/grid'],function (TreeGrid,Grid) {
			  var summary = new Grid.Plugins.Summary(
					{
						
					});
		      var tree = new TreeGrid({
		        render : '#productOneAddressGrid',
		        nodes : selections,
		        dirCls: 'arrow-up',
		        leafCls:'icon-gift',
		        columns : orderLineColumns,
		        plugins:[summary]
		      });
		     tree.render();
		 });
		
	}
}
function resubmitPartnerOrder(){
	/*if(partnerProperty != "KA"){
		if(totalCount < 5000){
			common.alertMes("产品最低订货量为" + partnerOrderMinLiterCount + "L", 'info');
			return false;
		}		
	}*/
	var addressType = $('input:radio:checked').val();
	if(addressType == undefined){
		common.alertMes("请输入收货地址！", "info");
		return false;
	}
	if(selectGrid == null ){
		common.alertMes("请选择相关产品！", 'error');
		return false;
	}
	var selections = [];
	var selectedLength = productSelectedSap.length;
	if(selectedLength > 0){						
		for(var i = 0;i < selectedLength; i ++){
			var item = productSelectedSap[i];
			var capacity = item.capacity;
			var count = item.count;
			if(typeof(item.count) != "undefined" && item.count > 0){
				selections.push(item);	
			}
		}
	}
	
	if(selections.length == 0){
		common.alertMes("请确认相关产品！", 'error');
		return;
	}
	var order = {}, orderLines = [];
	order.id = partnerOrderId;
	order.partnerId = $('#partnerId').val();
	/*order.addressId = $('#partnerAddressHid').val();*/
	var remark = $('#remark').val();
	order.remark = remark;
	order.salesOrg = salesOrg;
	order.salesCode = salesCode;
	
	var partnerSaleConfigId = null;
	if(partnerSaleConfigCtrl != null){
		partnerSaleConfigId = partnerSaleConfigCtrl.getSelectedValue();
		if(typeof(partnerSaleConfigId) != "undefined"){
			order.partnerSaleConfigId = partnerSaleConfigId;
		}
	}
	
	var countValidation = true;
	$(selections).each(function (i, item) {
		var orderline = {};
		orderline.productId = item.id;
		orderline.sku = item.sku;
		orderline.productName = item.name;
		orderline.amount = item.count;
		orderline.bottlesperbox = item.bottlesperbox;
		orderline.capacity = item.boxCapacity;
		if(parseInt(item.boxCapacity) == 200 ){
			orderline.units = "barrel";
		}else{
			orderline.units = "box";
		}
		orderline.price = item.boxPrice;
		orderline.ladderPrice = item.discountedPrice;
		/*orderline.discountedPrice = item.discountedPrice;*/
		orderline.type = item.category;
		orderline.inventoryCount = item.inventoryCount;
		orderline.promotionSku = item.promotionSku;
		orderline.promotionTitle = item.promotionTitle;
		if(partnerSaleConfigId != null && partnerSaleConfigId != "" && typeof(partnerSaleConfigId) != "undefined"){
			var partnerSaleConfigIdBoolean = skuUsingPartnerSaleConfigMap[item.sku];
			if(partnerSaleConfigIdBoolean == true){
				orderline.partnerSaleConfigId = order.partnerSaleConfigId;
			}
		}
		orderline.remark = item.remark;
		orderline.discountedTotalValue = item.totalValue;
		orderLines.push(orderline);
		
		if(typeof(item.count) == "undefined" || item.count == 0){
			countValidation = false;			
		}
	});
	if(!countValidation){
		common.alertMes("购买数量不能为空，请输入！", 'error');
		return false;
	}
	
	//单个地址类型
	var orderAddressProducts = [];
	if(addressType == 1){
		var addressId = $('#partnerAddressHid').val();
		if(addressId == ''){
			common.alertMes("请输入收货地址！", 'error');
			return false;
		}
		getProductConfirmedArray();
		$(productConfirmArrayForAddress).each(function (i, item) {
			var orderAddressProduct = {};
			orderAddressProduct.addressId = addressId;
			orderAddressProduct.productId = item.id;
			orderAddressProduct.sku = item.sku;
			orderAddressProduct.productName = item.name;
			orderAddressProduct.amount = item.count;
			orderAddressProduct.units = "box";
			/*orderline.price = item.salePrice;*/
			orderAddressProduct.type = item.category;
			orderAddressProduct.inventoryCount = item.inventoryCount;
			orderAddressProducts.push(orderAddressProduct);
		});
	}else if(addressType == 2){
		if(productConfirmArrayForAddress.length == 0){
			common.alertMes("请确保所有产品分配到对应地址！", 'error');
			return;
		}
		for(var i = 0; i < productConfirmArrayForAddress.length; i++){
			var tempProduct = productConfirmArrayForAddress[i];
			if(tempProduct.count > 0){
				common.alertMes("请确保所有产品分配到对应地址！", 'error');
				return;
			}
		}
		$(addressProductArray).each(function (i, item) {
			var orderAddressProduct = {};
			orderAddressProduct.addressId = item.addressId;
			orderAddressProduct.productId = item.id;
			orderAddressProduct.sku = item.sku;
			orderAddressProduct.productName = item.name;
			orderAddressProduct.amount = item.count;
			orderAddressProduct.units = "box";
			/*orderline.price = item.salePrice;*/
			orderAddressProduct.type = item.category;
			orderAddressProduct.inventoryCount = item.inventoryCount;
			orderAddressProducts.push(orderAddressProduct);
		});
	}
	
	LoadMask.show();
	fooProductManage.call("partnerOrderService.resubmitPartnerOrderMultiAddressSap", [order,orderLines,orderAddressProducts],
	        function (result) {
	            LoadMask.hide();
	            if (result.code == "success") {
	            	common.alertMes("订单提交成功！", "info");
	            	window.history.go(-1);
	            } else{
	                common.alertMes(result.errorMsg, "error");
	            }
	        }, function (error) {
	            common.ajaxTimeout(error);
	            LoadMask.hide();
	        });
	
}

//地址相关
var provinceCtrl = null;
function initProvince() {
	if(provinceCtrl == null){
	var items = [];	
	rpcClient.call("baseDataService.findAllProvinces",
			[],
			function (result) {
				if (result.success) {
					for(var i = 0; i < result.data.length; i++){
						
						var item = result.data[i];
						items.push({
							text : item.regionName,
							value : item.id
						});
					}
					BUI.use('bui/select',function(Select){
						provinceCtrl = new Select.Select({  
					          render:'#provinceCodeHid_c',
					          valueField:'#provinceCodeHid',
					          elCls: 'control-select',
					          elStyle: {'white-space': 'nowrap'},
					          multipleSelect:false,
					          items:items
					  	});
						provinceCtrl.render();
						provinceCtrl.on('change', function(e){
							initCity(e.value);
							/*if(cityCtrl != null){
								cityCtrl.setSelectedValue('');
							}
							if(distCtrl != null){
								distCtrl.setSelectedValue('');
							}*/
						});
					});					
				} else {
					common.alertMes('加载省份失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
	}
}
var cityCtrl = null;
function initCity(provId) {
	var items = [];
	rpcClient.call("baseDataService.findCities",
			[provId],
			function (result) {
				if (result.success) {
					for(var i = 0; i < result.data.length; i++){
						
						var item = result.data[i];
						items.push({
							text : item.regionName,
							value : item.id
						});
					}
					if(cityCtrl == null){
						BUI.use('bui/select',function(Select){
							cityCtrl = new Select.Select({  
						          render:'#cityCodeHid_c',
						          valueField:'#cityCodeHid',
						          elCls: 'control-select',
						          elStyle: {'white-space': 'nowrap'},
						          multipleSelect:false,
						          items:items
						  	});
							cityCtrl.render();
						});
						cityCtrl.on('change', function(e){
							initDist(e.value);
						});
					}else{
						cityCtrl.set('items', items);
					}
					
				} else {
					common.alertMes('加载城市失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
}
var distCtrl = null;
function initDist(cityId) {
	var items = [];

	rpcClient.call("baseDataService.findDistricts",
			[cityId],
			function (result) {
				if (result.success) {
					for(var i = 0; i < result.data.length; i++){
						
						var item = result.data[i];
						items.push({
							text : item.regionName,
							value : item.id
						});
					}
					if(distCtrl == null){
						BUI.use('bui/select',function(Select){
							distCtrl = new Select.Select({  
						          render:'#distCodeHid_c',
						          valueField:'#distCodeHid',
						          elCls: 'control-select',
						          elStyle: {'white-space': 'nowrap'},
						          multipleSelect:false,
						          items:items
						  	});
							distCtrl.render();
						});
					}else{
						distCtrl.set('items', items);
					}
					/*distCtrl.setSelectedValue('');*/
					
				} else {
					common.alertMes('加载区县失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
}
var editDialog = null;
function initEditDialog(){
	if(editDialog == null){
		BUI.use('bui/overlay',function(Overlay){
			editDialog = new Overlay.Dialog({
	        	title:'',
	            width:590,
	            bodyContent:$('#editDialog').show(),
	            buttons : [{ text:'提交', elCls : 'btn-submit', handler : function(){ 
	            	var provinceCode = $("#editDialog input[name=provinceCode]").val();
	            	if(provinceCode == ''){
	            		common.alertMes('请选择省份！','error');
	            		return;
	            	}
	            	var cityCode = $("#editDialog input[name=cityCode]").val();
	            	if(cityCode == ''){
	            		common.alertMes('请选择城市！','error');
	            		return;
	            	}
	            	var distCode = $("#editDialog input[name=distCode]").val();
	            	if(distCode == ''){
	            		common.alertMes('请选择区县！','error');
	            		return;
	            	}
	            	var address = $("#editDialog input[name=address]").val();
	            	if(address == ''){
	            		common.alertMes('请输入详细地址！','error');
	            		return;
	            	}
	            	var contactPerson = $("#editDialog input[name=contactPerson]").val();
	            	if(contactPerson == ''){
	            		common.alertMes('请输入联系人！','error');
	            		return;
	            	}
	            	var mobile = $("#editDialog input[name=mobile]").val();
	            	if(!common.checkMobile(mobile)){
	            		common.alertMes('请输入正确的手机号！','error');
	            		return;
	            	}            	
	            	var addressId = $("#editDialog input[name=addressId]").val(),
	            		method = null
	            		addressObj = null;
	      
            		method = 'partnerAddressService.insert';
            		addressObj = {};
            		addressObj.partnerId = $('#partnerId').val();
            		/*addressObj.addressAlias = $("#editDialog input[name=addressAlias]").val();*/
            		addressObj.provinceCode = $("#editDialog input[name=provinceCode]").val();
            		addressObj.cityCode = $("#editDialog input[name=cityCode]").val();
            		addressObj.distCode = $("#editDialog input[name=distCode]").val();
            		addressObj.address = $("#editDialog textarea[name=address]").val();
            		addressObj.contactPerson = $("#editDialog input[name=contactPerson]").val();
            		addressObj.mobile = $("#editDialog input[name=mobile]").val();
            		/*addressObj.telephone = $("#editDialog input[name=telephone]").val();*/
            		rpcClient.call(method, [addressObj], 
						function(result) {
		    				if(result.success){
		    					common.alertMes('提交成功', 'success');
		    					editDialog.close();
		    					addressObj =  result.address;
		    					initAddress($('#partnerId').val(),addressObj.id);
		    					initAddressProduct($('#partnerId').val(),addressObj.id);
		    				}
					}, function(error) {
							common.ajaxTimeout(error);
					});
	            } },
	            { text:'取消', elCls : 'btn-close', handler : function(){this.close(); } }]
	          });
	 });
	}
	return editDialog;
}
function creatAddress(){
	var dialog = initEditDialog();
	dialog.set('title', "新增地址");
    dialog.show();
    initProvince();
	initCity();
	initDist();
	if(provinceCtrl != null){
		provinceCtrl.setSelectedValue('');
	}
	if(cityCtrl != null){
		cityCtrl.setSelectedValue('');
	}
	if(distCtrl != null){
		distCtrl.setSelectedValue('');
	}
	$("#editDialog input[name=addressId]").val('');
	$("#editDialog input[name=addressAlias]").val('');
	$("#editDialog input[name=provinceCode]").val('');
	$("#editDialog input[name=cityCode]").val('');
	$("#editDialog input[name=distCode]").val('');
	$("#editDialog textarea[name=address]").val('');
	$("#editDialog input[name=contactPerson]").val('');
	$("#editDialog input[name=mobile]").val('');
	/*$("#editDialog input[name=telephone]").val('');*/
}
function showAddressType(type){
	if(type == '1'){
		$("#productAddress").hide();
		$("#productSelectedGrid").hide();
		$("#address").show();
			
	}else if(type = '2'){
		$("#productSelectedGrid").hide();
		$("#address").hide();
		$("#productAddress").show();
	}
}
var productAddressDialog = null;
var addressProductID = [];
var addressProductArray = [];
//定义对话框宽度
var screenWidth = $(window).width();
var dialogWidth = 750; 
var gridWidth = 800;
if((screenWidth * 0.8) > 750){
	dialogWidth = screenWidth * 0.8;
	gridWidth = dialogWidth - 50;
	if(gridWidth <= 800){
		gridWidth = 800;
	}
}
function initProductAddressDialog(){	
	if(productAddressDialog == null){
		BUI.use('bui/overlay',function(Overlay){
			productAddressDialog = new Overlay.Dialog({
	        	title:'',
	            width:dialogWidth,
	            height:480,
	            bodyContent:$('#selectProductAddressDiv').show(), 
	            buttons : [{ text:'确定', elCls : 'btn-submit', handler : function(){ 	            	
	            	if(addressProductCtrl.getSelectedValue() == ''){
	            		common.alertMes("请选择相关地址！", 'error');
						return;
	            	}
	            	var addressExisted = false;
	            	for(var i = 0; i < addressProductID.length; i++){
	            		if(addressProductCtrl.getSelectedValue() == addressProductID[i]){
	            			addressExisted = true;	            			
	            			if(editModel == "add"){
	            				common.alertMes("此地址已分配相关商品，请选择其他地址！", 'error');
	            				return;
	            			}
	            			break;
	            		}
	            	}
	            	if(addressExisted == false){
		            	addressProductID.push(addressProductCtrl.getSelectedValue());
		            	var addressProductList = []/*$.extend(true,{},tempProductConfirmArrayForAddress)*/;
		            	
		            	var selectedLength = tempProductConfirmArrayForAddress.length;
		            	if(selectedLength > 0){						
		            		for(var i = 0;i < selectedLength; i ++){	
		            			var item = tempProductConfirmArrayForAddress[i];
		            			if(item.tempCount != '' && item.tempCount > 0){
		            				var itemTemp = $.extend(true,{},item);
		            				itemTemp.addressId = addressProductCtrl.getSelectedValue();
		            				itemTemp.count = itemTemp.tempCount;
		            				addressProductList.push(itemTemp);
		            				addressProductArray.push(itemTemp);	
		            				
		            				for(var j = 0; j < productConfirmArrayForAddress.length; j++){
			        					var tempProduct = productConfirmArrayForAddress[j];
			        					if(item.id == tempProduct.id){
			        						tempProduct.count = parseInt(tempProduct.count) - parseInt(item.tempCount);
			        					}
			        				}
		            			}	            			
		            		}
		            	}	
		            	if(addressProductList.length == 0){
		            		common.alertMes("请选择相关产品！", 'error');
							return;
		            	}
		            	var addressHtml = '<div id="block' + addressProductCtrl.getSelectedValue() + '">'+
		            		'<div class="info-block-header">'+addressProductCtrl.getSelectedText()+
		            		'&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" onclick="creatAddressProduct(' +addressProductCtrl.getSelectedValue() + ')">修改数量</a>'+
		            		'&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" onclick="deleteBlock(' + addressProductCtrl.getSelectedValue() + ')">删除地址</a>'+
		            		'<i class="icon" onclick="showBlock(this);"></i>'+
		            		'</div>' + 
		            		'<div class="info-block">'+ 
		            			'<div class="cols-6">'		+ 		
		            				'<div class="field-group">'+ 
		            					'<div class="control-group" id="grid'+addressProductCtrl.getSelectedValue()+'">'+ 	            						
		            					'</div>'+ 
		            				'</div>'+ 
		            			'</div>	'+ 
		            		'</div>'+ 
		            	'</div>' ;
		            	$("#addressProductContent").append(addressHtml);
		            		            	 
		            	listAddressProductSpecified(addressProductCtrl.getSelectedValue(),addressProductList);
		            }else{
		            	var addressProductList = [];
		            	
		            	var selectedLength = tempProductConfirmArrayForAddress.length;
		            	if(selectedLength > 0){						
		            		for(var i = 0;i < selectedLength; i ++){	
		            			var item = tempProductConfirmArrayForAddress[i];
		            			if(item.tempCount != '' && item.tempCount > 0){
		            				var itemTemp = $.extend(true,{},item);
		            				itemTemp.addressId = addressProductCtrl.getSelectedValue();
		            				itemTemp.count = itemTemp.tempCount;
		            				addressProductList.push(itemTemp);
		            				
		            				/*addressProductArray.push(itemTemp);	*/
		            				//判断以前是否添加过这个产品
		            				var checkItemExited = false;
		            				for(var k = 0; k < addressProductArray.length; k++){
		    		            		var itemExisted = addressProductArray[k];
		    		            		if(itemExisted.addressId == addressProductCtrl.getSelectedValue() && itemExisted.sku == itemTemp.sku ){
		    		            			checkItemExited = true;
		    		            			itemExisted.count = parseInt(itemTemp.count);
		    		            			itemExisted.tempCount = itemExisted.count;
		    		            			break;
		    		            		}
		    		            	}
		            				if(checkItemExited == false){
		            					addressProductArray.push(itemTemp);
		            				}
		            				
		            				//更新可选数量
		            				for(var j = 0; j < productConfirmArrayForAddress.length; j++){
			        					var tempProduct = productConfirmArrayForAddress[j];
			        					if(item.id == tempProduct.id){
			        						tempProduct.count = parseInt(tempProduct.count) - parseInt(item.tempCount);
			        					}
			        				}
		            			}	            			
		            		}
		            	}	
		            	if(addressProductList.length == 0){
		            		common.alertMes("请选择相关产品！", 'error');
							return;
		            	}
		            	addressProductList = [];
		            	for(var k = 0; k < addressProductArray.length; k++){
		            		var item = addressProductArray[k];
		            		if(item.addressId == addressProductCtrl.getSelectedValue()){
		            			var itemTemp = $.extend(true,{},item);
		            			addressProductList.push(itemTemp);
		            		}
		            	}
		            		            	 
		            	listAddressProductSpecified(addressProductCtrl.getSelectedValue(),addressProductList);
	            	}
	            	            	
	            	productAddressDialog.close();
	            } },
	            { text:'取消', elCls : 'btn-close', handler : function(){this.close(); } }]
	          });
	 });
	}
	return productAddressDialog;
}
var tempProductConfirmArrayForAddress = [];
var editModel;
function creatAddressProduct(addressId){
	if(addressId != null){
		editModel = "update";
		addressProductCtrl.setSelectedValue(addressId);
		addressProductCtrl.disable();
		$("#createNewAddressBtn").hide();
	}else{
		addressProductCtrl.enable();
		editModel = "add";
		$("#createNewAddressBtn").show();
	}
	var dialog = initProductAddressDialog();
	dialog.set('title', "选择寄送地址");
	dialog.show();
		
	refreshAddressProductForAddress();
	if(addressId != null){
		for(var i = 0 ; i < tempProductConfirmArrayForAddress.length; i++){
			var item = tempProductConfirmArrayForAddress[i];	
			for(var k = 0; k < addressProductArray.length; k++){
        		var itemExisted = addressProductArray[k];
        		if(itemExisted.addressId == addressId && itemExisted.sku == item.sku ){
        			item.count = parseInt(itemExisted.count) + parseInt(item.count);
        			item.tempCount = parseInt(itemExisted.count);
        			break;
        		}
        	}
		}
	}
	listAddressProductSelected(tempProductConfirmArrayForAddress);
}
function refreshAddressProductForAddress(){
	getProductConfirmedArray();
	tempProductConfirmArrayForAddress = [];
	var selectedLength = productConfirmArrayForAddress.length;
	if(selectedLength > 0){						
		for(var i = 0;i < selectedLength; i ++){	
			var item = productConfirmArrayForAddress[i];		
			if(addressProductArray.length > 0) {
				for(var j = 0; j < addressProductArray.length ; j++){
					var addressProductTemp = addressProductArray[j];
					if(item.id == addressProductTemp.id){
						item.count = parseInt(item.count) - parseInt(addressProductTemp.tempCount);
					}
				}				
			}
			var newAddress = $.extend({},item);
			tempProductConfirmArrayForAddress.push(newAddress);	
		}
	}
}
var productConfirmArrayForAddress = [];
function getProductConfirmedArray(){
	var selections = [];
	var selectedLength = productSelectedSap.length;
	if(selectedLength > 0){						
		for(var i = 0;i < selectedLength; i ++){
			var item = productSelectedSap[i];
			var boxCapacity = item.boxCapacity;
			var count = item.count;
			if(typeof(item.count) != "undefined" && item.count > 0){
				var selectTemp = $.extend({},item);
				selections.push(selectTemp);	
			}
		}
	}
	
	productConfirmArrayForAddress = selections;
}
//购买地址数量关系
function setAddressProductBuyCount(obj,value){
	for(var i = 0; i < tempProductConfirmArrayForAddress.length; i++){
		var tempAddressProduct  = tempProductConfirmArrayForAddress[i];
		if(tempAddressProduct.id == value){
			var countInput = $("#addressProduct"+value+"").val();
			if(countInput != ''){
				if(!common.checkInt(countInput)){
					common.alertMes("购买数量只能是大于0的整数！", 'error');
					$("#addressProduct"+value+"").val("");
					return;
				}
				var countInputInt = parseInt(countInput);
				if(countInputInt > tempAddressProduct.count){
					common.alertMes("购买数量大于已选数量！", 'error');
					$("#addressProduct"+value+"").val("");
					return;
				}
				/*for(var i = 0; i < productConfirmArrayForAddress.length; i++){
					var tempProduct = productConfirmArrayForAddress[i];
					if(tempAddressProduct.id == tempProduct.id){
						tempProduct.count = tempProduct.count - countInput;
					}
				}*/
				/*tempAddressProduct.count = $("#addressProduct"+value+"").val();*/
				tempAddressProduct.tempCount = $("#addressProduct"+value+"").val();
				tempAddressProduct.capacityTotalAmount = tempAddressProduct.boxCapacity * tempAddressProduct.tempCount;
				break;
			}else{
				tempAddressProduct.count = 0;
				tempAddressProduct.capacityTotalAmount = 0;
				break;
			}
		}
	}
}
function listProductConfirmed(selections) {
	if (selections == null) {
		$("#productOneAddressGrid").html('没有选择任何产品');
	} else {
		$("#productOneAddressGrid").html("");
		var tableData = selections;
		BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '序号', dataIndex: 'productIndex', width: '65px',sortable: false },
						{ title: '产品编号', dataIndex: 'sku', width: '20%' },
						{ title: '产品信息', dataIndex: 'name', width: '40%',
							renderer:function(value,item,index){
								var text = '';
								if (/^.*(ZWL|ZLE)$/.test(item.sku)) { // 产品包
									text = item.remark || value; // 后端封装好了样式
								} else { // 单个产品
									text = value;
									if(item.iconId){
										text = '<div class="photo-icon"><img src="'
											+common.ctx+'downloadAttachmentFile.do?sourceType=5&attId='
											+item.iconId+'"/></div><span>'+value + '</span>';
									}
								}
								return text;
							}},
						/*{ title: '产品类别', dataIndex: 'category', width: '15%' },*/
						{ title: '规格', dataIndex: 'viscosity', width: '8%' },
						{ title: '升/箱', dataIndex: 'boxCapacity', width: '80px', elCls: 'text-right', renderer: function(value, item, index){							
							if(value){
								value = value + '';
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },					
						/*{ title: '零售价格(元)', dataIndex: 'salePrice', elCls: 'text-right', width: '120px',summary:true},*/
						{
							title : '库存',
							dataIndex : 'inventoryCount',
							elCls: 'text-right',
							width : '80px',
							renderer : function(value,obj,index){
								return value;
							}
						},
						{
							title : '订货数量(箱)',
							dataIndex : 'count',
							sortable: false,
							width : '100px',
							elCls: 'text-right',
							renderer : function(value,obj,index){
								return value
				        	}
						},{
							title : '合计升数(L)',
							dataIndex : 'capacityTotalAmount',
							elCls: 'text-right',
							sortable: false,
							width : '100px',
							renderer : function(value,obj,index){
								if(value == null){
									return '<span id="' + obj.id + 'capacityTotalAmount' + '"></span>';
								}else{
									return '<span id="' + obj.id + 'capacityTotalAmount' + '">' + value.toFixed(2)  +'</span>';
								}
							}
						}],
					data = tableData; // 显示的数据
					store = new Store({
						data: data,
						autoLoad: true
					}),

					editing = new Grid.Plugins.CellEditing(
							{
								triggerSelected : false //触发编辑的时候不选中行
							}),
					selectGrid = new Grid.Grid({
						render: '#productOneAddressGrid',
						columns: columns,
						loadMask: true, // 加载数据时显示屏蔽层
						stripeRows: false,
						store: store,
						itemStatusFields : {
			    			selected : 'checked',
			    			disabled : 'disable'
			            },
						plugins: [editing,Grid.Plugins.AutoFit],
						bbar: { width:'100%'}
					});	
					
					selectGrid.render();
					common.initGrid(selectGrid, [], true);
				/*if (curPage) {
					grid.get('bbar').getItemAt(0).jumpToPage(curPage);
					curPage = null;
				}*/
			});
	}
}
function listAddressProductSelected(selections) {
	if (selections == null) {
		$("#productConfirmGrid").html('没有选择任何产品');
	} else {
		$("#productConfirmGrid").html("");
		var tableData = selections;
		BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '序号', dataIndex: 'productIndex', width: '65px',sortable: false },
						{ title: '产品编号', dataIndex: 'sku', width: '20%' },
						{ title: '产品信息', dataIndex: 'name', width: '40%',
							renderer:function(value,item,index){
								var text = '';
								if (/^.*(ZWL|ZLE)$/.test(item.sku)) { // 产品包
									text = item.remark || value; // 后端封装好了样式
								} else { // 单个产品
									text = value;
									if(item.iconId){
										text = '<div class="photo-icon"><img src="'
											+common.ctx+'downloadAttachmentFile.do?sourceType=5&attId='
											+item.iconId+'"/></div><span>'+value + '</span>';
									}
								}
								return text;
							}},
						/*{ title: '产品类别', dataIndex: 'category', width: '15%' },*/
						{ title: '规格', dataIndex: 'viscosity', width: '8%' },
						{ title: '升/箱', dataIndex: 'boxCapacity', width: '80px', elCls: 'text-right', renderer: function(value, item, index){
							if(value){
								value = value + '';
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },					
						/*{ title: '零售价格(元)', dataIndex: 'salePrice', elCls: 'text-right', width: '120px',summary:true},*/
						{
							title : '库存',
							dataIndex : 'inventoryCount',
							elCls: 'text-right',
							width : '80px',
							renderer : function(value,obj,index){
								return value;
							}
						},{
							title : '可订数量',
							dataIndex : 'count',
							elCls: 'text-right',
							width : '80px',
							renderer : function(value,obj,index){
								return value;
							}
						},
						{
							title : '订货数量',
							sortable: false,
							width : '90px',
							elCls: 'text-right',
							renderer : function(value,obj,index){
								if(obj.count > 0 && obj.tempCount > 0){
									return '<input id="addressProduct'+obj.id+'" onfocus="this.select();"  min="1" max="10000" style="width:60px;height:25px;font-size:18px;padding-right:15px;" ' + 
									'class="text-right quantity-input" onchange="setAddressProductBuyCount(\''+obj+'\',\''+obj.id+'\')" value = "' + obj.tempCount + '"></input>';
								}else if(obj.count > 0){
									return '<input id="addressProduct'+obj.id+'" onfocus="this.select();"  min="1" max="10000" style="width:60px;height:25px;font-size:18px;padding-right:15px;" ' + 
									'class="text-right quantity-input" onchange="setAddressProductBuyCount(\''+obj+'\',\''+obj.id+'\')"></input>';
								}
				        	}
						}],
					data = tableData; // 显示的数据
					store = new Store({
						data: data,
						autoLoad: true
					}),

					editing = new Grid.Plugins.CellEditing(
							{
								triggerSelected : false //触发编辑的时候不选中行
							}),
					selectGrid = new Grid.Grid({
						render: '#productConfirmGrid',
						columns: columns,
						loadMask: true, // 加载数据时显示屏蔽层
						stripeRows: false,
						store: store,
						itemStatusFields : {
			    			selected : 'checked',
			    			disabled : 'disable'
			            },
						plugins: [editing,Grid.Plugins.AutoFit],
						bbar: { width:'100%'},
						width:gridWidth
					});	
					
					selectGrid.render();
				if (curPage) {
					grid.get('bbar').getItemAt(0).jumpToPage(curPage);
					curPage = null;
				}
			});
	}
}
function listAddressProductSpecified(contectDivId,selections) {
	if (selections == null) {
		$('#grid' + contectDivId).html('没有选择任何产品');
	} else {
		$('#grid' + contectDivId).html("");
		var tableData = selections;
		BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '序号', dataIndex: 'productIndex', width: '65px',sortable: false },
						{ title: '产品编号', dataIndex: 'sku', width: '20%' },
						{ title: '产品信息', dataIndex: 'name', width: '40%',
							renderer:function(value,item,index){
								var text = '';
								if (/^.*(ZWL|ZLE)$/.test(item.sku)) { // 产品包
									text = item.remark || value; // 后端封装好了样式
								} else { // 单个产品
									text = value;
									if(item.iconId){
										text = '<div class="photo-icon"><img src="'
											+common.ctx+'downloadAttachmentFile.do?sourceType=5&attId='
											+item.iconId+'"/></div><span>'+value + '</span>';
									}
								}
								return text;
							}},
						/*{ title: '产品类别', dataIndex: 'category', width: '15%' },*/
						{ title: '规格', dataIndex: 'viscosity', width: '15%' },
						{ title: '升/箱', dataIndex: 'boxCapacity', width: '80px', elCls: 'text-right', renderer: function(value, item, index){
							if(value){
								value = value + '';
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },						
						/*{ title: '零售价格(元)', dataIndex: 'salePrice', elCls: 'text-right', width: '120px',summary:true},*/
						{
							title : '库存',
							dataIndex : 'inventoryCount',
							elCls: 'text-right',
							width : '10%',
							renderer : function(value,obj,index){
								return value;
							}
						},
						{
							title : '订货数量',
							dataIndex : 'count',
							sortable: false,
							width : '90px',
							elCls: 'text-right',
							renderer : function(value,obj,index){
								return value;
				        	}
						}/*,{
							title : '合计升数(L)',
							dataIndex : 'capacityTotalAmount',
							elCls: 'text-right',
							sortable: false,
							width : '80px',
							renderer : function(value,obj,index){
								if(value == null){
									return '<span id="' + obj.id + 'capacityTotalAmount' + '"></span>';
								}else{
									return '<span id="' + obj.id + 'capacityTotalAmount' + '">' + value +'</span>';
								}
							}
						},*/],
					data = tableData; // 显示的数据
					store = new Store({
						data: data,
						autoLoad: true
					}),

					editing = new Grid.Plugins.CellEditing(
							{
								triggerSelected : false //触发编辑的时候不选中行
							}),
					selectGrid = new Grid.Grid({
						render: '#grid' + contectDivId,
						columns: columns,
						loadMask: true, // 加载数据时显示屏蔽层
						stripeRows: false,
						store: store,
						itemStatusFields : {
			    			selected : 'checked',
			    			disabled : 'disable'
			            },
						plugins: [editing,Grid.Plugins.AutoFit],
						bbar: { width:'100%'}
					});	
					
					selectGrid.render();
				if (curPage) {
					grid.get('bbar').getItemAt(0).jumpToPage(curPage);
					curPage = null;
				}
			});
	}
}
function showBlock(el){
	var $group = $(el).parent().parent();
	if($group.hasClass('show-block')){
		$group.removeClass('show-block');
	}else{
		$group.addClass('show-block');
	}
}
function deleteBlock(addressId){
	$("#block"+addressId).remove();
	//1 删除已选地址列中的地址ID
	for(var i = addressProductID.length - 1 ; i >= 0; i--){
		if(addressId == addressProductID[i]){
			addressProductID.splice(i,1);
		}
	}
	//2 删除分配地址商品中的对应的商品，同时还原可选数目
	var addressProductList = [];
	if(addressProductArray.length > 0) {
		for(var i = addressProductArray.length - 1; i >= 0; i--){
			var item = addressProductArray[i];
			if(addressId == item.addressId){
						
				for(var j = 0; j < productConfirmArrayForAddress.length; j++){
					var tempProduct = productConfirmArrayForAddress[j];
					if(item.id == tempProduct.id){
						tempProduct.count = parseInt(tempProduct.count) + parseInt(item.tempCount);
					}
				}
				
				addressProductArray.splice(i,1);
			}	            			
		}
	}
	return;
}

// 参加活动的 sku 列表
var promoteSKU = ["500244NJK","500244LPK","500245NJK","500245LPK","503032NJK","503032LPK","503033LPK","500246NJK","500246LPK","503034NJB","503034LPB","503035NJB","503035LPB","500247NJK","500247LPK","500249NJK","500249LPK"];
var promoteNumber = 5000;
var promoteMultiple = promoteNumber + 100;
// 参加活动的提醒
function confirmPromoteCalcRemind (callback){
	var total = calcPromoteOrderPrice();
	var message = '';
	var num = total%promoteMultiple;

	if (total < promoteNumber) {
		message += '您下单的促销产品未满 ' + promoteNumber + ' L，不能享受折扣，';
	} else if (total < promoteMultiple) {
		return callback();
	} else if (num !== 0) {
		message += '您下单参加本次促销的产品中有 '+ total +' L, 按照 ' + promoteMultiple + ' 倍数享受折扣的规则，其中有 ' + num + ' L 不享受促销，';
	} else {
		return callback();
	}

	message += '请确认是否提交订单？';

	common.confirmMes(message, callback, 'question');
}
function confirmPromoteOrderRemind (){
	var total = calcPromoteOrderPrice();
	if (total >= 5000) {
		common.confirmMes('满车的折扣优惠将会在线下订单创建时加入，请您在收到公司发出的订单确认时核对折扣信息。 谢谢！', function() {}, 'question');
	}
}
// 计算参加活动的商品是否满足提醒条件
function calcPromoteOrderPrice () {
	// 计算参加活动的 sku 的总升数
	var total = 0;
	productSelectedBef.map(function (item) {
		// 判断是否填写了数量
		if (typeof(item.count) != "undefined" && item.count > 0) {
			// 判断是不是促销产品
			if (promoteSKU.indexOf(item.sku) > -1) {
				total += parseInt(item.boxCapacity * item.count)
			}
		}
	})
	return total;
	confirmPromoteRemind(total, callback)
}