
CREATE VIEW [dbo].[view_nonlocal_sale_report]
AS
(
/*仅用于非本地营销报表页 仅有被窜货经销商的数据*/
select mlm1.distributor_id to_distributor_id,if1.from_distributor_id,mpi1.reporting_region_id, mpi1.sku,mpi1.scan_time,
mpi1.product_time,mpi1.product_batch,mpi1.product_out_no,mpi1.logistics_code,
mpi1.auth_channel channel,mpi1.display_channel,mlm1.reprot_type,if1.req_time 
from wx_t_non_local_mkt_flow_form if1
left join wx_t_loc_mkt_product_info mpi1 on if1.id=mpi1.flow_id
left join wx_t_main_local_marketing mlm1 on mlm1.id=if1.report_id
where mlm1.distributor_id is not null
/*union all 
select null to_distributor_id,t1.from_distributor_id,t1.report_region_id, t1.sku,t1.out_date scan_time,
t1.product_date product_time,t1.product_batch,t1.out_order_no product_out_no,t1.logistics_code,
t1.auth_channel channel,t1.display_channel,2 reprot_type,t1.report_time req_time
 from wx_t_non_local_user_report t1
 where not exists (select 1 from wx_t_loc_mkt_product_info impi1 where impi1.logistics_code=t1.logistics_code)*/
)