package com.sys.dic.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.chevron.sellin.model.PointBaseInfo;
import com.common.util.JsonUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.dao.DicTypeVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.DicItemVoExample;
import com.sys.dic.model.DicTypeVo;
import com.sys.dic.model.DicTypeVoExample;
import com.sys.dic.model.DicItemVoExample.Criteria;
import com.sys.dic.service.DicService;

@Service
public class DicServiceImpl implements DicService {

	@Resource
	DicItemVoMapper dicItemVoMapper;

	@Resource
	DicTypeVoMapper dicTypeVoMapper;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> createDicType(DicTypeVo dicTypeVo) {
		Map<String, Object> map = new HashMap<String, Object>();

		// SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		// 先查看是否有重复typeCode的数据
		DicTypeVoExample example = new DicTypeVoExample();
		com.sys.dic.model.DicTypeVoExample.Criteria creteria = example
				.createCriteria();
		creteria.andDicTypeCodeEqualTo(dicTypeVo.getDicTypeCode());
		List<DicTypeVo> result = dicTypeVoMapper.selectByExample(example);
		if (result.size() > 0) {
			map.put("result", "typeCodeIsExist");
			return map;
		}

		// 完善vo数据
		dicTypeVo.setCreator(ContextUtil.getCurUserId());
		dicTypeVo.setCreateTime(new Date());

		dicTypeVo.setUpdateTime(dicTypeVo.getCreateTime());
		dicTypeVo.setUpdator(dicTypeVo.getCreator());

		dicTypeVoMapper.insertSelective(dicTypeVo);
		map.put("result", "success");

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> createDicItem(DicItemVo dicItemVo) {
		Map<String, Object> map = new HashMap<String, Object>();

		// 查看是否有重复itemCode的选项
		DicItemVoExample example = new DicItemVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andDicItemCodeEqualTo(dicItemVo.getDicItemCode());
		criteria.andDicTypeCodeEqualTo(dicItemVo.getDicTypeCode());
		List<DicItemVo> result = dicItemVoMapper.selectByExample(example);
		if (result.size() > 0) {
			map.put("result", "itemCodeIsExist");
			return map;
		}
		// 完善dicItemVo
		dicItemVo.setCreator(ContextUtil.getCurUserId());
		dicItemVo.setCreateTime(new Date());
		dicItemVo.setUpdator(dicItemVo.getCreator());
		dicItemVo.setUpdateTime(dicItemVo.getCreateTime());
		// 存储到数据库
		dicItemVoMapper.insertSelective(dicItemVo);
		map.put("result", "success");

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> delDicTypeByCode(String code) {
		Map<String, Object> map = new HashMap<String, Object>();

		dicTypeVoMapper.deleteByCode(code);
		map.put("result", "success");

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> delDicItemByCode(String code) {
		Map<String, Object> map = new HashMap<String, Object>();

		dicItemVoMapper.deleteByCode(code);
		map.put("result", "success");

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> delDicTypeById(long id) {
		Map<String, Object> map = new HashMap<String, Object>();

		dicTypeVoMapper.deleteByPrimaryKey(id);
		map.put("result", "success");

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> delDicItemById(long id) {
		Map<String, Object> map = new HashMap<String, Object>();

		dicItemVoMapper.deleteByPrimaryKey(id);
		map.put("result", "success");

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateDicType(DicTypeVo dicTypeVo) {
		Map<String, Object> map = new HashMap<String, Object>();

		// 检查新的typeCode是否有重复的
		DicTypeVoExample example = new DicTypeVoExample();
		com.sys.dic.model.DicTypeVoExample.Criteria criteria = example
				.createCriteria();
		criteria.andDicTypeCodeEqualTo(dicTypeVo.getDicTypeCode());
		List<DicTypeVo> result = dicTypeVoMapper.selectByExample(example);
		// 如果是原来的数据不存在，就报异常
		if (!((result.size() > 0)	&& result.get(0).getId().equals(dicTypeVo.getId()))) {
			map.put("result", "typeDontExist");
			return map;
		}
		// 获取老的dicType
		DicTypeVo oldDicType = dicTypeVoMapper.selectByPrimaryKey(dicTypeVo
				.getId());

		try {
			// 更新修改时间
			dicTypeVo.setUpdateTime(new Date());
			dicTypeVo.setUpdator(ContextUtil.getCurUserId());
			// 更新dicType
			dicTypeVoMapper.updateByPrimaryKey(dicTypeVo);
			// 级联更新dicItem
			dicItemVoMapper.updateDicTypeCodeByOldDicTypeCode(
					oldDicType.getDicTypeCode(), dicTypeVo.getDicTypeCode());

			map.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			map.put("result", "error");
			e.printStackTrace();
		}

		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateDicItem(DicItemVo dicItemVo) {
		Map<String, Object> map = new HashMap<String, Object>();

		dicItemVoMapper.updateByPrimaryKey(dicItemVo);
		map.put("result", "success");

		return map;
	}

	@Override
	public Map<String, Object> getDicTypes() {
		Map<String, Object> map = new HashMap<String, Object>();
		List<DicTypeVo> list = new ArrayList<DicTypeVo>();
		DicTypeVoExample example = new DicTypeVoExample();
		list.addAll(dicTypeVoMapper.selectByExample(example));
		map.put("data", list);
		map.put("result", "success");
		return map;
	}

	@Override
	public Map<String, Object> getDicItemByDicTypeCode(String dicTypeCode) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		try {
			List<DicItemVo> list = new ArrayList<DicItemVo>();

			DicItemVoExample example = new DicItemVoExample();
			Criteria criteria = example.createCriteria();
			criteria.andDicTypeCodeEqualTo(dicTypeCode).andStatusEqualTo("1");
			example.setOrderByClause("sort_numb asc");
			list.addAll(dicItemVoMapper.selectByExample(example));
			resultMap.put("result", "success");
			resultMap.put("data", list);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			e.printStackTrace();
		}

		return resultMap;
	}
	
	@Override
	public DicItemVo getDictItem(String dicTypeCode,String dicItemCode){
		Map<String, Object> resultLst =  getDicItemByDicTypeCode(dicTypeCode);
		List<DicItemVo> itemLst = (ArrayList<DicItemVo>) resultLst.get("data");
		for(DicItemVo dicItem : itemLst){
			if(dicItem.getDicItemCode().equals(dicItemCode)) {
				return dicItem;
			}
		}
		return null;
	}

	@Override
	public Map<String, Object> getDicTypeById(Long id) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		DicTypeVo typeVo = dicTypeVoMapper.selectByPrimaryKey(id);
		if (typeVo != null) {
			resultMap.put("dicType", typeVo);
			resultMap.put("result", "success");
		} else {
			resultMap.put("result", "fail");
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getDicTypeByParam(String typeName,
			String typeCode) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		DicTypeVoExample example = new DicTypeVoExample();
		com.sys.dic.model.DicTypeVoExample.Criteria criteria = example
				.createCriteria();
		if (typeName != null && !typeName.trim().equals("")) {
			criteria.andDicTypeNameLike("%" + typeName.trim() + "%");
		}
		
		if (typeCode != null && !typeCode.equals("")) {
			criteria.andDicTypeCodeEqualTo(typeCode);
		}
		List<DicTypeVo> resultList = dicTypeVoMapper.selectByExample(example);
		resultMap.put("result", "success");
		resultMap.put("data", resultList);

		return resultMap;
	}

	@Override
	public Map<String, Object> getDicItemByParam(String typeCode,
			String itemName, String itemCode) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		DicItemVoExample example = new DicItemVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andDicTypeCodeEqualTo(typeCode);
		
		if (itemName != null && !itemName.trim().equals("")) {
			criteria.andDicItemNameLike("%" + itemName.trim() + "%");
		}
		if (itemCode != null && !itemCode.equals("")) {
			criteria.andDicItemCodeEqualTo(itemCode);
		}
		List<DicItemVo> resultList = dicItemVoMapper.selectByExample(example);
		resultMap.put("result", "success");
		resultMap.put("data", resultList);

		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> delDicTypeByIds(long[] id) {
		Map<String, Object> map = new HashMap<String, Object>();

		try {
			for (int i = 0; i < id.length; i++) {
				dicTypeVoMapper.deleteByPrimaryKey(id[i]);
			}
			map.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			map.put("result", "error");
			e.printStackTrace();
		}

		return map;

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> delDicItemByIds(long[] id) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			for (int i = 0; i < id.length; i++) {
				dicItemVoMapper.deleteByPrimaryKey(id[i]);
			}
			map.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			map.put("result", "error");
			e.printStackTrace();
		}

		return map;
	}

	@Override
	public JsonResponse getItemsByCode(String code) {
		JsonResponse resultMap = new JsonResponse();
		if(StringUtils.isBlank(code)){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("数据字典编码不能为空");
			return resultMap;
		}
		try {
			DicItemVoExample example = new DicItemVoExample();
			example.createCriteria().andDicTypeCodeEqualTo(code).andStatusEqualTo("1");
			resultMap.setListResult(dicItemVoMapper.selectByExample(example));
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.sys.dic.service.impl.DicServiceImpl.getItemsByCode", code);
		}
		return resultMap;
	}

	@Override
    public List<PointBaseInfo> getPointBaseInfo(){
        List<PointBaseInfo> list = new ArrayList<PointBaseInfo>();
        List<DicItemVo> dicItemVos = dicItemVoMapper.selectByCode(Constants.PROMOTION_POINT_CATEGORY);
        if(CollectionUtil.isEmpty(dicItemVos)){
            return list;
        }
        for (DicItemVo dicItemVo : dicItemVos) {
            PointBaseInfo pointBaseInfo = JsonUtil.json2obj(dicItemVo.getDicItemDesc(), PointBaseInfo.class);
            if(pointBaseInfo==null){
                continue;
            }
            pointBaseInfo.setPointName(dicItemVo.getDicItemName());
            pointBaseInfo.setPointType(dicItemVo.getDicItemCode());
            list.add(pointBaseInfo);
        }
        return list;
    }

}
