package com.sys.quartz.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.report.model.MtdEmailConfig;
import com.common.exception.WxPltException;
import com.common.util.StringUtils;
import com.sys.log.util.LogUtils;

import java.util.Date;

public class SendEmailForRenamWorkShop {

	@Autowired
	WorkshopMasterBizService workshopMasterBizService;
	
	private static Logger log = LoggerFactory.getLogger(SendEmailForRenamWorkShop.class);
	
	public synchronized void doSendEmailForRenamWorkShop()
	{
		log.info("SendEmailForRenamWorkShop job start....");
		LogUtils.addInfoLog(1l, "WorkshopMasterBizServiceImpl.sendEmailForRenam", "开始发送同一经销售下面相同门店名称报警邮件："+new Date());
		try {
			workshopMasterBizService.sendEmailForRenam();
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.addErrorLog(1l, "WorkshopMasterBizServiceImpl.sendEmailForRenam", "发送同一经销售下面相同门店名称报警邮件失败。" + e.getMessage(), null);
		}
		log.info("SendEmailForRenamWorkShop job finish....");
		LogUtils.addInfoLog(1l, "WorkshopMasterBizServiceImpl.sendEmailForRenam", "发送同一经销售下面相同门店名称报警邮件结束："+new Date());
	}
}
