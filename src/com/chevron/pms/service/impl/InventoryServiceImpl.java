package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.pms.dao.InventoryVoMapper;
import com.chevron.pms.dao.WorkshopInventoryOutRecordMapper;
import com.chevron.pms.model.InventoryQueryParams;
import com.chevron.pms.model.InventoryVo;
import com.chevron.pms.model.ProductConditionQueryVo;
import com.chevron.pms.model.WorkshopInventoryOutRecord;
import com.chevron.pms.model.WsVerifOutStockParams;
import com.chevron.pms.service.InventoryService;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;

@Service
public class InventoryServiceImpl implements InventoryService {
	@Resource
	private InventoryVoMapper inventoryVoMapper;
	@Resource
	private WorkshopInventoryOutRecordMapper recordMapper;
	
	private Logger logger = LoggerFactory.getLogger(InventoryServiceImpl.class);

	@Override
	public Map<String, Object> findInventoryDetail(Long inventoryId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("bean", inventoryVoMapper.selectInventoryDetail(inventoryId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> selectInventoryPagination(InventoryQueryParams params){
		Map<String, Object> map = new HashMap<String, Object>(2);
		map.put("rows", inventoryVoMapper.selectInventory(params));
		map.put("total", params.getTotalCount());
		return map;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Map<String, Object> updateInventory(Long inventoryId, Integer quantity) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try{
			InventoryVo inventory = inventoryVoMapper.selectByPrimaryKey(inventoryId);
			inventoryVoMapper.updateInventoryQuantity(inventory.getWorkshopId(), inventory.getSku(), quantity);		
			map.put("success", true);
		}catch(Exception e){
			map.put("success", false);
		}
		return map;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Map<String, Object> updateInventoryBatch(InventoryVo[] inventoryList) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try{
			for(InventoryVo temp : inventoryList){
				InventoryVo  inventory = inventoryVoMapper.queryInventoryByWorkshopAndSku(temp.getWorkshopId(), temp.getSku());
				if(inventory != null){
					inventory.setUpdateTime(new Date());
					inventory.setQuantity(temp.getQuantity());
					inventoryVoMapper.updateByPrimaryKeySelective(inventory);
				}else{
					temp.setCreator( String.valueOf(ContextUtil.getCurUser().getUserId()));
					temp.setUpdateTime(new Date());
					inventoryVoMapper.insertSelective(temp);
				}
			}	
			map.put("success", true);
		}catch(Exception e){
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> queryOnWayStock(InventoryQueryParams params) {
		Map<String, Object> map = new HashMap<String, Object>(8);
		try {
			params.setPaging(false);
			map.put("data", inventoryVoMapper.selectOnWayStock(params));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
			map.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
		}
		return map;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Map<String, Object> updateWsInventoryByVerificationList(
			List<WsVerifOutStockParams> verifcationList) {
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
		try {
			Map<String, Object> skuWorkshopIdVerificationListMap = new HashMap<String, Object>(); 
			List<String> logisticsCodeList = new ArrayList<String>();
			
			for(WsVerifOutStockParams verification : verifcationList){
				if(verification.getSku() == null || verification.getWorkshopId() == null){
					resultMap.put("errorMsg", "核销数据sku与门店ID不能为空！");
					resultMap.put("success", true);
					return resultMap;
				}else{
					String skuWorkShopId = verification.getSku() + "," + verification.getWorkshopId().toString();
					if(skuWorkshopIdVerificationListMap.get(skuWorkShopId) == null){
						List<WsVerifOutStockParams> listTemp = new ArrayList<WsVerifOutStockParams>();
						listTemp.add(verification);
						skuWorkshopIdVerificationListMap.put(skuWorkShopId, listTemp);
					}else{
						List<WsVerifOutStockParams> listTemp = (List<WsVerifOutStockParams>)skuWorkshopIdVerificationListMap.get(skuWorkShopId);
						listTemp.add(verification);
						skuWorkshopIdVerificationListMap.put(skuWorkShopId, listTemp);
					}
					if(StringUtils.isNotEmpty(verification.getLogisticsCode())){
						logisticsCodeList.add(verification.getSku() + "," + verification.getWorkshopId().toString() + "," + verification.getLogisticsCode());
					}
				}
			}
			if(skuWorkshopIdVerificationListMap != null){
				Set<String> skuworkshopidSet = skuWorkshopIdVerificationListMap.keySet();
				for(String skuworkshopid : skuworkshopidSet){
					String[] skuworkshidArray =  skuworkshopid.split(",");
					String sku = skuworkshidArray[0];
					String workshopId = skuworkshidArray[1];
					InventoryVo  inventory = inventoryVoMapper.queryInventoryByWorkshopAndSku(Long.valueOf(workshopId), sku);
					List<WsVerifOutStockParams> listTemp = (List<WsVerifOutStockParams>)skuWorkshopIdVerificationListMap.get(skuworkshopid);
					if(listTemp != null && listTemp.size() > 0){
						if(inventory != null){
							Integer quantity = inventory.getQuantity();
							quantity = quantity - listTemp.size();
							inventory.setQuantity(quantity);
							inventory.setUpdateTime(new Date());
							inventoryVoMapper.updateByPrimaryKeySelective(inventory);
						}else{
							InventoryVo inventoryTemp = new InventoryVo();
							inventoryTemp.setSku(sku);
							inventoryTemp.setWorkshopId(Long.valueOf(workshopId));
							Integer quantity = 0;
							quantity = quantity - listTemp.size();
							inventoryTemp.setStatus("1");
							inventoryTemp.setQuantity(quantity);
							inventoryTemp.setCreateTime(new Date());
							inventoryTemp.setUpdateTime(new Date());
							inventoryVoMapper.insertSelective(inventoryTemp);
						}
					}
				}
			}
			if(logisticsCodeList != null && logisticsCodeList.size() > 0){
				List<WorkshopInventoryOutRecord> outrecordList = new ArrayList<WorkshopInventoryOutRecord>();
				for(String logisticsCode : logisticsCodeList){
					String[] skuworkshidCodeArray =  logisticsCode.split(",");
					
					WorkshopInventoryOutRecord record = new WorkshopInventoryOutRecord();
					record.setSku(skuworkshidCodeArray[0]);
					record.setWorkshopId(Long.valueOf(skuworkshidCodeArray[1]));
					record.setCode(skuworkshidCodeArray[2]);
					record.setCodeType(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);
					record.setCreateTime(new Date());
					outrecordList.add(record);
				}
				recordMapper.batchInsert(outrecordList);
			}
			resultMap.put("success", true);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();			
			resultMap.put("success", false);
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryInventoryByWorkshopId(Long workshopId, ProductConditionQueryVo productMultiParamQueryVo,
			boolean containsAllOilProducts) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("productMultiParamQueryVo", productMultiParamQueryVo);
			params.put("workshopId", workshopId);
			params.put("containsAllOilProducts", containsAllOilProducts);
			params.put("partnerId", ContextUtil.getCurUser().getOrgId());
			if(productMultiParamQueryVo != null) {
				params.put("productName", productMultiParamQueryVo.getKeywords());
			}
			map.put(Constants.RESULT_LST_KEY, inventoryVoMapper.selectInventoryByParams(params));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}	
}
