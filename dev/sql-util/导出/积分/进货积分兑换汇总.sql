SELECT
            --case when a.APPLICATION_TYPE='b2b' then (select w.partner_id from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id where we.code=a.APPLICATION_USER_ID) ELSE a.APPLICATION_ORG_ID END as partner_id,
			--ISNULL(a.PROMOTION_TYPE, a.APPLICATION_TYPE) as point_type,
            CONVERT(varchar(7), a.CREATION_TIME, 23) as "月份",
			sum(cast(pb.ATTRIBUTE1 as decimal(12,2))*(case when pb.BUSINESS_TYPE_CODE='ROLLBACK_POINT' then -1 else 1 end)) AS "总兑换"
            FROM dbo.wx_t_material_application a
            LEFT JOIN wx_t_organization o ON o.id = a.APPLICATION_ORG_ID
            LEFT JOIN wx_t_point_business pb on a.APPLICATION_CODE = pb.RELATED_CODE
            LEFT JOIN wx_t_user u on u.user_id = a.CREATED_BY
            WHERE 1 = 1
            AND a.DELETE_FLAG = 0
            and pb.BUSINESS_STATUS = 'DONE'
            AND (a.ATTRIBUTE1 is null or a.ATTRIBUTE1 != 'PROMOTION_EXCHANGE')
            AND a.CREATION_TIME>='2020-01-01'
			and a.APPLICATION_TYPE='caltex'--'cdm_stock'
			group by CONVERT(varchar(7), a.CREATION_TIME, 23)
			order by CONVERT(varchar(7), a.CREATION_TIME, 23)