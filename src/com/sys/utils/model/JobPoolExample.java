package com.sys.utils.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 任务池单表查询条件
 * <AUTHOR>
 * @version 1.0 2019-01-09 22:29
 */
public class JobPoolExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public JobPoolExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andJobDescIsNull() {
            addCriterion("job_desc is null");
            return (Criteria) this;
        }

        public Criteria andJobDescIsNotNull() {
            addCriterion("job_desc is not null");
            return (Criteria) this;
        }

        public Criteria andJobDescEqualTo(String value) {
            addCriterion("job_desc =", value, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescNotEqualTo(String value) {
            addCriterion("job_desc <>", value, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescGreaterThan(String value) {
            addCriterion("job_desc >", value, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescGreaterThanOrEqualTo(String value) {
            addCriterion("job_desc >=", value, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescLessThan(String value) {
            addCriterion("job_desc <", value, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescLessThanOrEqualTo(String value) {
            addCriterion("job_desc <=", value, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescIn(List<String> values) {
            addCriterion("job_desc in", values, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescNotIn(List<String> values) {
            addCriterion("job_desc not in", values, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescBetween(String value1, String value2) {
            addCriterion("job_desc between", value1, value2, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andJobDescNotBetween(String value1, String value2) {
            addCriterion("job_desc not between", value1, value2, "jobDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionIsNull() {
            addCriterion("schedule_expression is null");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionIsNotNull() {
            addCriterion("schedule_expression is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionEqualTo(String value) {
            addCriterion("schedule_expression =", value, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionNotEqualTo(String value) {
            addCriterion("schedule_expression <>", value, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionGreaterThan(String value) {
            addCriterion("schedule_expression >", value, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionGreaterThanOrEqualTo(String value) {
            addCriterion("schedule_expression >=", value, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionLessThan(String value) {
            addCriterion("schedule_expression <", value, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionLessThanOrEqualTo(String value) {
            addCriterion("schedule_expression <=", value, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionIn(List<String> values) {
            addCriterion("schedule_expression in", values, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionNotIn(List<String> values) {
            addCriterion("schedule_expression not in", values, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionBetween(String value1, String value2) {
            addCriterion("schedule_expression between", value1, value2, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andScheduleExpressionNotBetween(String value1, String value2) {
            addCriterion("schedule_expression not between", value1, value2, "scheduleExpression");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEnableFlagIsNull() {
            addCriterion("enable_flag is null");
            return (Criteria) this;
        }

        public Criteria andEnableFlagIsNotNull() {
            addCriterion("enable_flag is not null");
            return (Criteria) this;
        }

        public Criteria andEnableFlagEqualTo(Integer value) {
            addCriterion("enable_flag =", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagNotEqualTo(Integer value) {
            addCriterion("enable_flag <>", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagGreaterThan(Integer value) {
            addCriterion("enable_flag >", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable_flag >=", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagLessThan(Integer value) {
            addCriterion("enable_flag <", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagLessThanOrEqualTo(Integer value) {
            addCriterion("enable_flag <=", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagIn(List<Integer> values) {
            addCriterion("enable_flag in", values, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagNotIn(List<Integer> values) {
            addCriterion("enable_flag not in", values, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagBetween(Integer value1, Integer value2) {
            addCriterion("enable_flag between", value1, value2, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("enable_flag not between", value1, value2, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Integer value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Integer value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Integer value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Integer value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Integer value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Integer> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Integer> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andJobTypeIsNull() {
            addCriterion("job_type is null");
            return (Criteria) this;
        }

        public Criteria andJobTypeIsNotNull() {
            addCriterion("job_type is not null");
            return (Criteria) this;
        }

        public Criteria andJobTypeEqualTo(String value) {
            addCriterion("job_type =", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotEqualTo(String value) {
            addCriterion("job_type <>", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeGreaterThan(String value) {
            addCriterion("job_type >", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeGreaterThanOrEqualTo(String value) {
            addCriterion("job_type >=", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeLessThan(String value) {
            addCriterion("job_type <", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeLessThanOrEqualTo(String value) {
            addCriterion("job_type <=", value, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeIn(List<String> values) {
            addCriterion("job_type in", values, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotIn(List<String> values) {
            addCriterion("job_type not in", values, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeBetween(String value1, String value2) {
            addCriterion("job_type between", value1, value2, "jobType");
            return (Criteria) this;
        }

        public Criteria andJobTypeNotBetween(String value1, String value2) {
            addCriterion("job_type not between", value1, value2, "jobType");
            return (Criteria) this;
        }

        public Criteria andLockFlagIsNull() {
            addCriterion("lock_flag is null");
            return (Criteria) this;
        }

        public Criteria andLockFlagIsNotNull() {
            addCriterion("lock_flag is not null");
            return (Criteria) this;
        }

        public Criteria andLockFlagEqualTo(String value) {
            addCriterion("lock_flag =", value, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagNotEqualTo(String value) {
            addCriterion("lock_flag <>", value, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagGreaterThan(String value) {
            addCriterion("lock_flag >", value, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagGreaterThanOrEqualTo(String value) {
            addCriterion("lock_flag >=", value, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagLessThan(String value) {
            addCriterion("lock_flag <", value, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagLessThanOrEqualTo(String value) {
            addCriterion("lock_flag <=", value, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagIn(List<String> values) {
            addCriterion("lock_flag in", values, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagNotIn(List<String> values) {
            addCriterion("lock_flag not in", values, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagBetween(String value1, String value2) {
            addCriterion("lock_flag between", value1, value2, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andLockFlagNotBetween(String value1, String value2) {
            addCriterion("lock_flag not between", value1, value2, "lockFlag");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeIsNull() {
            addCriterion("next_execute_time is null");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeIsNotNull() {
            addCriterion("next_execute_time is not null");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeEqualTo(Date value) {
            addCriterion("next_execute_time =", value, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeNotEqualTo(Date value) {
            addCriterion("next_execute_time <>", value, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeGreaterThan(Date value) {
            addCriterion("next_execute_time >", value, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("next_execute_time >=", value, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeLessThan(Date value) {
            addCriterion("next_execute_time <", value, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeLessThanOrEqualTo(Date value) {
            addCriterion("next_execute_time <=", value, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeIn(List<Date> values) {
            addCriterion("next_execute_time in", values, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeNotIn(List<Date> values) {
            addCriterion("next_execute_time not in", values, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeBetween(Date value1, Date value2) {
            addCriterion("next_execute_time between", value1, value2, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andNextExecuteTimeNotBetween(Date value1, Date value2) {
            addCriterion("next_execute_time not between", value1, value2, "nextExecuteTime");
            return (Criteria) this;
        }

        public Criteria andInstanceClassIsNull() {
            addCriterion("instance_class is null");
            return (Criteria) this;
        }

        public Criteria andInstanceClassIsNotNull() {
            addCriterion("instance_class is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceClassEqualTo(String value) {
            addCriterion("instance_class =", value, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassNotEqualTo(String value) {
            addCriterion("instance_class <>", value, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassGreaterThan(String value) {
            addCriterion("instance_class >", value, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassGreaterThanOrEqualTo(String value) {
            addCriterion("instance_class >=", value, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassLessThan(String value) {
            addCriterion("instance_class <", value, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassLessThanOrEqualTo(String value) {
            addCriterion("instance_class <=", value, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassIn(List<String> values) {
            addCriterion("instance_class in", values, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassNotIn(List<String> values) {
            addCriterion("instance_class not in", values, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassBetween(String value1, String value2) {
            addCriterion("instance_class between", value1, value2, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andInstanceClassNotBetween(String value1, String value2) {
            addCriterion("instance_class not between", value1, value2, "instanceClass");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodIsNull() {
            addCriterion("execute_method is null");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodIsNotNull() {
            addCriterion("execute_method is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodEqualTo(String value) {
            addCriterion("execute_method =", value, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodNotEqualTo(String value) {
            addCriterion("execute_method <>", value, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodGreaterThan(String value) {
            addCriterion("execute_method >", value, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodGreaterThanOrEqualTo(String value) {
            addCriterion("execute_method >=", value, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodLessThan(String value) {
            addCriterion("execute_method <", value, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodLessThanOrEqualTo(String value) {
            addCriterion("execute_method <=", value, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodIn(List<String> values) {
            addCriterion("execute_method in", values, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodNotIn(List<String> values) {
            addCriterion("execute_method not in", values, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodBetween(String value1, String value2) {
            addCriterion("execute_method between", value1, value2, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExecuteMethodNotBetween(String value1, String value2) {
            addCriterion("execute_method not between", value1, value2, "executeMethod");
            return (Criteria) this;
        }

        public Criteria andExtAttr1IsNull() {
            addCriterion("ext_attr1 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr1IsNotNull() {
            addCriterion("ext_attr1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr1EqualTo(String value) {
            addCriterion("ext_attr1 =", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotEqualTo(String value) {
            addCriterion("ext_attr1 <>", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1GreaterThan(String value) {
            addCriterion("ext_attr1 >", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr1 >=", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1LessThan(String value) {
            addCriterion("ext_attr1 <", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1LessThanOrEqualTo(String value) {
            addCriterion("ext_attr1 <=", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1In(List<String> values) {
            addCriterion("ext_attr1 in", values, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotIn(List<String> values) {
            addCriterion("ext_attr1 not in", values, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1Between(String value1, String value2) {
            addCriterion("ext_attr1 between", value1, value2, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotBetween(String value1, String value2) {
            addCriterion("ext_attr1 not between", value1, value2, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr2IsNull() {
            addCriterion("ext_attr2 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr2IsNotNull() {
            addCriterion("ext_attr2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr2EqualTo(String value) {
            addCriterion("ext_attr2 =", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotEqualTo(String value) {
            addCriterion("ext_attr2 <>", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2GreaterThan(String value) {
            addCriterion("ext_attr2 >", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr2 >=", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2LessThan(String value) {
            addCriterion("ext_attr2 <", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2LessThanOrEqualTo(String value) {
            addCriterion("ext_attr2 <=", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2In(List<String> values) {
            addCriterion("ext_attr2 in", values, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotIn(List<String> values) {
            addCriterion("ext_attr2 not in", values, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2Between(String value1, String value2) {
            addCriterion("ext_attr2 between", value1, value2, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotBetween(String value1, String value2) {
            addCriterion("ext_attr2 not between", value1, value2, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr3IsNull() {
            addCriterion("ext_attr3 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr3IsNotNull() {
            addCriterion("ext_attr3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr3EqualTo(String value) {
            addCriterion("ext_attr3 =", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotEqualTo(String value) {
            addCriterion("ext_attr3 <>", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3GreaterThan(String value) {
            addCriterion("ext_attr3 >", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr3 >=", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3LessThan(String value) {
            addCriterion("ext_attr3 <", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3LessThanOrEqualTo(String value) {
            addCriterion("ext_attr3 <=", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3In(List<String> values) {
            addCriterion("ext_attr3 in", values, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotIn(List<String> values) {
            addCriterion("ext_attr3 not in", values, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3Between(String value1, String value2) {
            addCriterion("ext_attr3 between", value1, value2, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotBetween(String value1, String value2) {
            addCriterion("ext_attr3 not between", value1, value2, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
