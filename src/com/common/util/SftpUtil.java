package com.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import org.apache.log4j.Logger;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
/**
 * 
 * @Author: bo.liu  2017-9-7 下午2:39:20 
 * @Version: $Id$
 * @Desc: <p>sftp上传工具类</p>
 */
public class SftpUtil {
	public final static Logger log = Logger.getLogger(SftpUtil.class);
	public final static int SESSION_CONNECT_TIMEOUT=600000;
	public final static int CHANNEL_CONNECT_TIMEOUT=10000000;
	/**
	 * TODO 如果只有一个sftp文件服务器，，可以考虑用构造方法，属性的方式去设置ip,user,psw等固定信息
	 */
	/**
	 * 密码方式登录
	 * <AUTHOR> 2017-9-7 下午2:39:17
	 * @param ip   
	 * @param user
	 * @param psw
	 * @param port
	 * @param sPath
	 * @param dPath
	 */
    public static boolean sshSftp(String ip, String user, String psw, int port,
            List<String> sPaths, String dPath) {
    	log.info("sshSftp password login");
        Session session = null;
        JSch jsch = new JSch();
        try {
            if (port <= 0) {
                // 连接服务器，采用默认端口
                session = jsch.getSession(user, ip);
            } else {
                // 采用指定的端口连接服务器
                session = jsch.getSession(user, ip, port);
            }

            // 如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new Exception("session is null");
            }

            // 设置登陆主机的密码
            session.setPassword(psw);// 设置密码
            // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
            session.setConfig("StrictHostKeyChecking", "no");
            // 设置登陆超时时间
            session.connect(SESSION_CONNECT_TIMEOUT);
            upLoadFile(session, sPaths, dPath);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("sshSftp failed");
            return false;
        }
        log.info("sshSftp success");
        return true;
      
    }

   /**
    * 密匙方式登录
    * <AUTHOR> 2017-9-7 下午2:41:53
    * @param ip
    * @param user
    * @param port
    * @param privateKey
    * @param passphrase
    * @param sPath
    * @param dPath
    */
    public static void sshSftp2(String ip, String user, int port,
            String privateKey, String passphrase, List<String> sPaths, String dPath) {
    	log.info("sshSftp2 privateKey login");
        Session session = null;
        JSch jsch = new JSch();
        try {
            // 设置密钥和密码
            // 支持密钥的方式登陆，只需在jsch.getSession之前设置一下密钥的相关信息就可以了
            if (privateKey != null && !"".equals(privateKey)) {
                if (passphrase != null && "".equals(passphrase)) {
                    // 设置带口令的密钥
                    jsch.addIdentity(privateKey, passphrase);
                } else {
                    // 设置不带口令的密钥
                    jsch.addIdentity(privateKey);
                }
            }
            if (port <= 0) {
                // 连接服务器，采用默认端口
                session = jsch.getSession(user, ip);
            } else {
                // 采用指定的端口连接服务器
                session = jsch.getSession(user, ip, port);
            }
            // 如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new Exception("session is null");
            }
            // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
            session.setConfig("StrictHostKeyChecking", "no");
            // 设置登陆超时时间
            session.connect(SESSION_CONNECT_TIMEOUT);
            upLoadFile(session, sPaths, dPath);
            log.info("sshSftp2 success");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    
    
    public static boolean sshSftpFile(String ip, String user, String psw, int port,
            File[] files,String desFileName, String dPath) {
    	log.info("sshSftp password login");
        Session session = null;
        JSch jsch = new JSch();
        try {
            if (port <= 0) {
                // 连接服务器，采用默认端口
                session = jsch.getSession(user, ip);
            } else {
                // 采用指定的端口连接服务器
                session = jsch.getSession(user, ip, port);
            }

            // 如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new Exception("session is null");
            }

            // 设置登陆主机的密码
            session.setPassword(psw);// 设置密码
            // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
            session.setConfig("StrictHostKeyChecking", "no");
            // 设置登陆超时时间
            session.connect(SESSION_CONNECT_TIMEOUT);
            upLoadSftpFiles(session, files,desFileName, dPath);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("sshSftp failed");
            return false;
        }
        log.info("sshSftp success");
        return true;
      
    }
    
    
    
    public static void upLoadFile(Session session, List<String> sPaths, String dPath) {

        Channel channel = null;
        try {
            channel = (Channel) session.openChannel("sftp");
            channel.connect(CHANNEL_CONNECT_TIMEOUT);
            ChannelSftp sftp = (ChannelSftp) channel;
            try {
                sftp.cd(dPath);
                //Scanner scanner = new Scanner(System.in);
                /*System.out.println(dPath + ":此目录已存在,文件可能会被覆盖!是否继续y/n?");
                String next = scanner.next();
                if (!next.toLowerCase().equals("y")) {
                    return;
                }*/

            } catch (SftpException e) {
            	log.error("not find exception,must be mkdir");
                sftp.mkdir(dPath);
                sftp.cd(dPath);

            }
            
            for(String sPath:sPaths)
            {
	            File file = new File(sPath);
	            copyFile(sftp, file, sftp.pwd());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            session.disconnect();
            channel.disconnect();
        }
    }
    
    
    
    public static void upLoadSftpFiles(Session session, File[] files, String desFileName, String dPath) {

        Channel channel = null;
        try {
            channel = (Channel) session.openChannel("sftp");
            channel.connect(CHANNEL_CONNECT_TIMEOUT);
            ChannelSftp sftp = (ChannelSftp) channel;
            try {
                sftp.cd(dPath);
                //Scanner scanner = new Scanner(System.in);
                /*System.out.println(dPath + ":此目录已存在,文件可能会被覆盖!是否继续y/n?");
                String next = scanner.next();
                if (!next.toLowerCase().equals("y")) {
                    return;
                }*/

            } catch (SftpException e) {
            	log.error("not find exception,must be mkdir");
                sftp.mkdir(dPath);
                sftp.cd(dPath);

            }
            for(File file : files)
            {
            	copyFile(sftp, file, sftp.pwd(),desFileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            session.disconnect();
            channel.disconnect();
        }
    }

    public static void copyFile(ChannelSftp sftp, File file, String pwd) {

        if (file.isDirectory()) {
            File[] list = file.listFiles();
            try {
                try {
                    String fileName = file.getName();
                    sftp.cd(pwd);
                    log.info("正在创建目录:" + sftp.pwd() + "/" + fileName);
                    sftp.mkdir(fileName);
                    log.error("目录创建成功:" + sftp.pwd() + "/" + fileName);
                } catch (Exception e) {
                    // TODO: handle exception
                }
                pwd = pwd + "/" + file.getName();
                try {

                    sftp.cd(file.getName());
                } catch (SftpException e) {
                    // TODO: handle exception
                    e.printStackTrace();
                }
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            for (int i = 0; i < list.length; i++) {
                copyFile(sftp, list[i], pwd);
            }
        } else {

            try {
                sftp.cd(pwd);

            } catch (SftpException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
            log.info("正在上传文件:" + file.getAbsolutePath());
            InputStream instream = null;
            OutputStream outstream = null;
            try {
                outstream = sftp.put(file.getName());
                instream = new FileInputStream(file);

                byte b[] = new byte[1024];
                int n;
                try {
                    while ((n = instream.read(b)) != -1) {
                        outstream.write(b, 0, n);
                    }
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

            } catch (SftpException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } finally {
                try {
                    outstream.flush();
                    outstream.close();
                    instream.close();

                } catch (Exception e2) {
                    // TODO: handle exception
                    e2.printStackTrace();
                }
            }
        }
    }
    
    
    
    public static void copyFile(ChannelSftp sftp, File file, String pwd, String desFileName) {

        if (file.isDirectory()) {
            File[] list = file.listFiles();
            try {
                try {
                    String fileName = file.getName();
                    sftp.cd(pwd);
                    log.info("正在创建目录:" + sftp.pwd() + "/" + fileName);
                    sftp.mkdir(fileName);
                    log.error("目录创建成功:" + sftp.pwd() + "/" + fileName);
                } catch (Exception e) {
                    // TODO: handle exception
                }
                pwd = pwd + "/" + file.getName();
                try {

                    sftp.cd(file.getName());
                } catch (SftpException e) {
                    // TODO: handle exception
                    e.printStackTrace();
                }
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            for (int i = 0; i < list.length; i++) {
                copyFile(sftp, list[i], pwd);
            }
        } else {

            try {
                sftp.cd(pwd);

            } catch (SftpException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
            log.info("正在上传文件:" + file.getAbsolutePath());
            InputStream instream = null;
            OutputStream outstream = null;
            try {
                outstream = sftp.put(desFileName);
                instream = new FileInputStream(file);

                byte b[] = new byte[1024];
                int n;
                try {
                    while ((n = instream.read(b)) != -1) {
                        outstream.write(b, 0, n);
                    }
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

            } catch (SftpException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } finally {
                try {
                    outstream.flush();
                    outstream.close();
                    instream.close();

                } catch (Exception e2) {
                    // TODO: handle exception
                    e2.printStackTrace();
                }
            }
        }
    }
    
    public static boolean sshSftpDeleteFile(String ip, String user, String psw, int port,
    		String directory, String deleteFile) {
    	log.info("sshSftpDeleteFile password login");
        Session session = null;
        JSch jsch = new JSch();
        try {
            if (port <= 0) {
                // 连接服务器，采用默认端口
                session = jsch.getSession(user, ip);
            } else {
                // 采用指定的端口连接服务器
                session = jsch.getSession(user, ip, port);
            }

            // 如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new Exception("session is null");
            }

            // 设置登陆主机的密码
            session.setPassword(psw);// 设置密码
            // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
            session.setConfig("StrictHostKeyChecking", "no");
            // 设置登陆超时时间
            session.connect(SESSION_CONNECT_TIMEOUT);
            deleteFile(session,directory, deleteFile);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("sshSftpDeleteFile failed");
            return false;
        }
        log.info("sshSftpDeleteFile success");
        return true;
      
    }
    
    public static void deleteFile(Session session,String directory, String deleteFile) throws Exception { 
    	Channel channel = null;
        try {
            channel = (Channel) session.openChannel("sftp");
            channel.connect(CHANNEL_CONNECT_TIMEOUT);
            ChannelSftp sftp = (ChannelSftp) channel;
            try {
                sftp.cd(directory);
                sftp.rm(deleteFile); 
                log.info("deleteFile success");
            } catch (SftpException e) {
            	log.error("deleteFile exception");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            session.disconnect();
            channel.disconnect();
        }
    } 
    
    
}
