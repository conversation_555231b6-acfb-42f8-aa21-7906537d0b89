package com.sys.utils.service.impl;

import com.chevron.report.business.ExpenseBizService;
import com.chevron.report.model.*;
import com.chevron.report.util.ExpenseItem;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.DicItemVoExample;
import com.sys.log.util.LogUtils;
import com.sys.utils.service.CommonService;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CommonServiceImpl implements CommonService {

	@Resource
	private ExpenseBizService expenseBizService;
	@Resource
	private WxTUserMapper userMapper;

	private Logger log = LoggerFactory.getLogger(CommonServiceImpl.class);

	@Autowired
    private DicItemVoMapper dicItemVoMapper;

	@Override
	public JsonResponse pushWeChatMessage(String eventId, String alarmName,
			String alarmType, String entityName, String alarmContent) {
		JsonResponse resultMap = new JsonResponse();
		Long uid = ContextUtil.getCurUserId();
		log.info("pushWeChatMessage: " + eventId + "," + alarmName + "," + alarmType + "," + entityName + "," + alarmContent);
		try {
			WeixinMessageUtil.weixinAlarmMessagePush(eventId, alarmName, alarmType, entityName, alarmContent);
			log.info("pushWeChatMessage success.");
		} catch (Exception e) {
			resultMap.handleException(e, this, uid, 
					"com.sys.utils.service.impl.CommonServiceImpl.pushWeChatMessage", eventId + "," + alarmName);
		}
		return resultMap;
	}

	@Override
	public JsonResponse sendEmail(String[] toEmails, String[] ccEmails,
			String subject, String content) {
		JsonResponse resultMap = new JsonResponse();
		Long uid = ContextUtil.getCurUserId();
		if(toEmails == null || toEmails.length == 0){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("邮件接收邮箱地址不能为空");
			LogUtils.addErrorLog(uid, "com.sys.utils.service.impl.CommonServiceImpl.sendEmail", 
					"邮件接收邮箱地址不能为空",  subject + "," + content);
			return resultMap;
		}
		log.info("sendEmail: " + subject + "," + content);
		try {
			if(EmailSender.send(toEmails,ccEmails, subject,
					content, null, "text/html", EmailSender.DEFAULT_SENDER_NICK,null)){
				resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
				resultMap.setErrorMsg("发送邮件失败");
				LogUtils.addErrorLog(uid, "com.sys.utils.service.impl.CommonServiceImpl.sendEmail", 
						"发送邮件失败",  subject + "," + content);
				return resultMap;
			}
			log.info("sendEmail success.");
		} catch (Exception e) {
			resultMap.handleException(e, this, uid, "com.sys.utils.service.impl.CommonServiceImpl.sendEmail", subject + "," + content);
		}
		return resultMap;
	}

	@Override
	public JsonResponse sendSms(String mobile, String content) {
		JsonResponse resultMap = new JsonResponse();
		Long uid = ContextUtil.getCurUserId();
		if(StringUtils.isBlank(mobile)){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("短信接收手机号不能为空");
			LogUtils.addErrorLog(uid, "com.sys.utils.service.impl.CommonServiceImpl.sendSms", 
					"短信接收手机号不能为空",  mobile);
			return resultMap;
		}
		log.info("sendSms: " + mobile + "," + content);
		try {
			SMSUtil.sendSms(mobile, content, SMSUtil.APP_ID_UNKNOWN);
			log.info("sendSms success.");
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, uid, "com.sys.utils.service.impl.CommonServiceImpl.sendSms", mobile);
		}
		return resultMap;
	}

	@Override
	public JsonResponse sendSmsByApp(String mobile, String content, String appId) {
		JsonResponse resultMap = new JsonResponse();
		Long uid = ContextUtil.getCurUserId();
		if(StringUtils.isBlank(mobile)){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("短信接收手机号不能为空");
			LogUtils.addErrorLog(uid, "com.sys.utils.service.impl.CommonServiceImpl.sendSms", 
					"短信接收手机号不能为空",  mobile);
			return resultMap;
		}
		log.info("sendSms: " + mobile + "," + content);
		try {
			SMSUtil.sendSms(mobile, content, appId);
			log.info("sendSms success.");
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, uid, "com.sys.utils.service.impl.CommonServiceImpl.sendSms", mobile);
		}
		return resultMap;
	}

	@Override
	public JsonResponse kuaidiQuery(String num,String phone) {
		JsonResponse jsonResponse = new JsonResponse();
		if(StringUtils.isBlank(num)){
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			return jsonResponse;
		}
		Kuaidi100Util.KuaidiQueryResult kuaidiQueryResult = Kuaidi100Util.kuaidiQuery(num,phone);
		if(kuaidiQueryResult != null){
			if("ok".equals(kuaidiQueryResult.getMessage())){
				jsonResponse.setListResult(kuaidiQueryResult.getData());
				Map<String,Object> stateMap = new HashMap<String,Object>();
				stateMap.put("state",kuaidiQueryResult.getState());
				jsonResponse.setDataResult(stateMap);
			} else {
				String message = kuaidiQueryResult.getMessage();
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
				jsonResponse.setErrorMsg(StringUtils.isBlank(message)?"查询快递信息失败":message);
			}
		} else {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
			jsonResponse.setErrorMsg("查询快递信息失败");
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse kuaidiQuery(String num) {
		return kuaidiQuery(num,null);
	}

	@Override
	public JsonResponse queryBudgetAndExpenseByDistributor(Long distributorId, String expenseCode, int year,
			boolean includeAsm) {
		log.info("queryBudgetAndExpenseByDistributor, distributorId: {}, expenseCode: {}, year: {}, includeAsm: {}",
				distributorId, expenseCode, year, includeAsm);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			ExpenseItem expenseItem = ExpenseItem.getExpenseItemByCode(expenseCode);
			if(expenseItem == null) {
				throw new WxPltException("费用项" + expenseCode + "不存在");
			}
			int brand = 0;
			if(year > 2020) {
				brand = expenseItem.getChannelWeight();
			}
			jsonResponse.setDataResult(queryBudgetAndExpenseByDistributor(distributorId, expenseItem, year, includeAsm, brand));
			log.info("queryBudgetAndExpenseByDistributor success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.service.impl.CommonServiceImpl.queryBudgetAndExpenseByDistributor", distributorId + "," + expenseCode);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryBudgetAndExpenseByDistributorAfter2021(Long distributorId, 
			int year, int brand) {
		log.info("queryBudgetAndExpenseByDistributorAfter2021, distributorId: {}, year: {}, brand: {}",
				distributorId, year, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			jsonResponse.setDataResult(queryBudgetAndExpenseByDistributor(distributorId, null, year, false, brand));
			log.info("queryBudgetAndExpenseByDistributorAfter2021 success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.service.impl.CommonServiceImpl.queryBudgetAndExpenseByDistributorAfter2021", distributorId + "," + brand);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryProjectBudgetAndExpenseByDistributor(Long distributorId, int year, int brand) {
		log.info("queryProjectBudgetAndExpenseByDistributor, distributorId: {}, year: {}, brand: {}",
				distributorId, year, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			DistributorExpenseVo expenseVo = expenseBizService.queryProjectBudgetAndExpenseByDistributor(distributorId, year, brand);
			jsonResponse.setDataResult(expenseVo);
			log.info("queryProjectBudgetAndExpenseByDistributor success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryProjectBudgetAndExpenseByDistributor", distributorId + "," + brand);
		}
		return jsonResponse;
	}

	protected DistributorExpenseVo queryBudgetAndExpenseByDistributor(Long distributorId, ExpenseItem expenseItem, int year,
			boolean includeAsm, int brand) throws WxPltException {
		DistributorExpenseVo expenseVo = expenseBizService.queryBudgetAndExpenseByDistributor(distributorId, expenseItem, year, includeAsm, brand);
		if(year < 2021) {
			if(expenseItem.getChannelWeight() == 1) {
				expenseVo.setBudgetNote("参与星火计划的经销商，钱从星火计划中扣；非星火计划的经销商，钱从销售预算中扣除");
			}else if(expenseItem.getChannelWeight() == 2) {
				expenseVo.setBudgetNote("有星火计划支持的，优先扣除星火的钱，不够的部分，再从销售预算中扣除。否则，直接从销售预算中扣除");
			}
		}
		return expenseVo;
	}

    @Override
    public ResponseMap kuaidiQuery(String num, String phone, String company) {
        ResponseMap jsonResponse = new ResponseMap();
        if (StringUtils.isBlank(company) || StringUtils.isBlank(num)) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            jsonResponse.setErrorMsg("查询无结果。");
            return jsonResponse;
        }
        DicItemVoExample example = new DicItemVoExample();
        example.createCriteria().andDicTypeCodeEqualTo("EXPRESS_COMPANY").andDicItemNameEqualTo(company);
        List<DicItemVo> dicItemVos = dicItemVoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(dicItemVos)) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            jsonResponse.setErrorMsg("查询无结果。");
            return jsonResponse;
        }
        Kuaidi100Util.KuaidiQueryResult kuaidiQueryResult = Kuaidi100Util.kuaidiQuery(num, phone);
        if ("ok".equals(kuaidiQueryResult.getMessage())) {
            jsonResponse.setListResult(kuaidiQueryResult.getData());
            Map<String, Object> stateMap = new HashMap<String, Object>();
            stateMap.put("state", kuaidiQueryResult.getState());
            jsonResponse.setDataResult(stateMap);
        } else {
            String message = kuaidiQueryResult.getMessage();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(StringUtils.isBlank(message) ? "查询快递信息失败" : message);
        }
        return jsonResponse;
    }

	@Override
	public JsonResponse queryPartnerSignageTips(Long partnerId, Integer brand) {
		log.info("queryPartnerSignageTips, partnerId: {}, brand: {}", partnerId, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			PartnerSignageVo partnerSignageVo = expenseBizService.queryPartnerSignage(partnerId, brand);
			if (partnerSignageVo == null) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}
			jsonResponse.setDataResult(partnerSignageVo);
			log.info("queryPartnerSignageTips success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryPartnerSignageTips", partnerId + "," + brand);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryPartnerStoreSignageTips(Long partnerId, Integer brand, Long storeId) {
		log.info("queryPartnerStoreSignageTips, partnerId: {}, brand: {}, storeId: {}", partnerId, brand, storeId);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			PartnerStoreSignageVo partnerStoreSignageVo = expenseBizService.queryPartnerStoreSignage(partnerId, brand, storeId);
			if (partnerStoreSignageVo == null) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}
			jsonResponse.setDataResult(partnerStoreSignageVo);
			log.info("queryPartnerStoreSignageTips success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryPartnerStoreSignageTips", partnerId + "," + brand+ "," + storeId);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryPartnerOverallPerformanceTips(Long partnerId, Integer brand) {
		log.info("queryPartnerOverallPerformanceTips, partnerId: {}, brand: : {}", partnerId, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			PartnerOverallPerformanceVo partnerOverallPerformanceVo = expenseBizService.queryPartnerOverallPerformance(partnerId, brand);
			if (partnerOverallPerformanceVo == null) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}
			jsonResponse.setDataResult(partnerOverallPerformanceVo);
			log.info("queryPartnerOverallPerformanceTips success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryPartnerOverallPerformanceTips", partnerId + "," + brand);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryPartnerProductOverallPerformanceTips(Long partnerId, Integer brand) {
		log.info("queryPartnerProductOverallPerformanceTips, partnerId: {}, brand: : {}", partnerId, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			List<PartnerProductOverallPerformanceVo> list = expenseBizService.queryPartnerProductOverallPerformance(partnerId, brand);
			if (list == null) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}
			jsonResponse.setDataResult(list);
			log.info("queryPartnerProductOverallPerformanceTips success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryPartnerProductOverallPerformanceTips", partnerId + "," + brand);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryDistributorCostCenter(Long partnerId, Integer brand) {
		log.info("queryDistributorCostCenter, partnerId: {}, brand: : {}", partnerId, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			PartnerCostCenterVo partnerCostCenterVo = expenseBizService.queryPartnerCostCenter(partnerId, brand);
			if (partnerCostCenterVo == null) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}
			jsonResponse.setDataResult(partnerCostCenterVo);
			log.info("queryDistributorCostCenter success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryDistributorCostCenter", partnerId + "," + brand);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse queryPartnerSeminarTips(Long partnerId, Integer brand) {
		log.info("queryPartnerSeminarTips, partnerId: {}, brand: : {}", partnerId, brand);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			PartnerSeminarVo partnerSeminarVo = expenseBizService.queryPartnerSeminar(partnerId, brand);
			if (partnerSeminarVo == null) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}
			jsonResponse.setDataResult(partnerSeminarVo);
			log.info("queryPartnerSeminarTips success.");
		} catch (WxPltException e) {
			jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			jsonResponse.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.queryPartnerSeminarTips", partnerId + "," + brand);
		}
		return jsonResponse;
	}

	@Override
	public JsonResponse querySignageMaterialType(Integer brand, String applyType, String localMake) {
		log.info("querySignageMaterialType, applyType: {}, brand: : {}, localMake: : {}", applyType, brand,localMake);
		JsonResponse jsonResponse = new JsonResponse();
		try {
			List<Map<String,String>> list = new ArrayList<Map<String, String>>();
			String dicTypeCode = "";
			if("STORE_SIGN".equals(applyType)){
				if("Y".equals(localMake)){
					dicTypeCode = "storeSign14a.materialType";
				}else {
					dicTypeCode = "storeSign.materialType";
				}
			}else if("OUTSIDE_AD".equals(applyType)){
				dicTypeCode = "outsideAd14a.materialType";
			}else if("CAR_AD".equals(applyType)){
				dicTypeCode = "carAd14a.materialType";
			}
			DicItemVoExample example = new DicItemVoExample();
			example.createCriteria().andDicTypeCodeEqualTo(dicTypeCode);
			List<DicItemVo> dicItemVos = dicItemVoMapper.selectByExample(example);
			if (CollectionUtils.isEmpty(dicItemVos)) {
				jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				jsonResponse.setErrorMsg("查询无结果。");
				return jsonResponse;
			}

			DicItemVoExample example2 = new DicItemVoExample();
			example2.createCriteria().andDicTypeCodeEqualTo("signage.flow.config");
			List<DicItemVo> dicItemVos2 = dicItemVoMapper.selectByExample(example2);

			for (DicItemVo dicItemVo : dicItemVos) {
				Map<String,String> map = new HashMap<String, String>();
				map.put("code",dicItemVo.getDicItemCode());
				map.put("value",dicItemVo.getDicItemName());
				if("STORE_SIGN".equals(applyType)){
					if("Y".equals(localMake)){
						for (DicItemVo dicItemVo2 : dicItemVos2) {
							if(dicItemVo.getDicItemCode().equals(dicItemVo2.getDicItemCode())){
								map.put("limit",dicItemVo2.getDicItemName());
							}
						}
					}
				}
				list.add(map);
			}
			jsonResponse.setDataResult(list);
			log.info("querySignageMaterialType success.");
		} catch (Exception e) {
			jsonResponse.handleException(e, this, ContextUtil.getCurUserId(),
					"com.sys.utils.service.impl.CommonServiceImpl.querySignageMaterialType", brand + "," + applyType+ "," + localMake);
		}
		return jsonResponse;
	}

	@Override
	public String queryRoleByLogUser() {
		String userRole = "";
		try {
			Long curUserId = ContextUtil.getCurUserId();
			List<Long> roleList = userMapper.selectRoleByUserId(curUserId);
			if ( roleList.contains(11190L) || roleList.contains(11229L)){
				userRole = Constants.IS_MKT;
			}else if (roleList.contains(11221L) || roleList.contains(11224L)){
				userRole = Constants.IS_ABM;
			}else if (roleList.contains(11225L) || roleList.contains(11228L) ){
				userRole =  Constants.IS_ASM;
			}else {
				userRole = Constants.IS_SALES;
			}

		} catch (Exception e) {
			return userRole;
		}
		return userRole;
	}

}
