
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Caltex_Dealer'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='CDM_DIST'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Service_Partner_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Service_Partner_Manager'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern'), 1, getdate());

insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Caltex_Dealer'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Promote_Sales'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Promote_Sales'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_C&I_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_C&I_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_BD'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Chevron_BD'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Service_Partner_Manager'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Service_Partner_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='CDM_DIST'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern_BD'), 1, getdate());

insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern'), (select r.role_id from wx_t_role r where r.ch_role_name='Consumer_Retailer_Pattern_BD'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern'), (select r.role_id from wx_t_role r where r.ch_role_name='Commercial_Retailer_Pattern_BD'), 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern'), 'App', 8, 1, 'APP权限。8-经销商管理员', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern'), 'App', 8, 1, 'APP权限。8-经销商管理员', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern_BD'), 'App', 512, 1, 'APP权限。512-经销商销售', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern_BD'), 'App', 512, 1, 'APP权限。512-经销商销售', 1, getdate());

insert into wx_t_resource_sql (sqlid, resource_id,creation_time,created_by) values ('com.sys.organization.dao.OrganizationVoMapper.queryPartnerForCtrl-workshopPage',(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店管理' and m.module_name='合伙人管理'),getdate(),1)

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'POINT_ADJUST_2021_WEIGHT', 8, 1, '积分及下单礼品促销兑换：8-分销商积分划拨', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'POINT_ADJUST_2021_WEIGHT', 8, 1, '积分及下单礼品促销兑换：8-分销商积分划拨', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'POINT_ADJUST_2021_WEIGHT', 8, 1, '积分及下单礼品促销兑换：8-分销商积分划拨', 1, getdate());

--数据权限
update wx_t_resource set resource_type_code='WS_FULL_RETAILER' where resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店管理' and m.module_name='合伙人管理')
update wx_t_resource set resource_type_code='SP_FULL_RETAILER' where resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='促销活动奖励查看' and m.module_name='市场支持')
update wx_t_resource set resource_type_code='SP_FULL_RETAILER' where resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='新客户录入&拜访打卡' and m.module_name='终端客户应用及任务')

update wx_t_subject_res_permission set permission_type_id=33 where permission_type_id=4 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店管理' and m.module_name='合伙人管理')
update wx_t_subject_res_permission set permission_type_id=34 where permission_type_id=9 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店管理' and m.module_name='合伙人管理')
update wx_t_subject_res_permission set permission_type_id=35 where permission_type_id=8 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店管理' and m.module_name='合伙人管理')
update wx_t_subject_res_permission set permission_type_id=36 where permission_type_id=21 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店管理' and m.module_name='合伙人管理')

update wx_t_subject_res_permission set permission_type_id=37 where permission_type_id=1 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='促销活动奖励查看' and m.module_name='市场支持')
update wx_t_subject_res_permission set permission_type_id=38 where permission_type_id=3 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='促销活动奖励查看' and m.module_name='市场支持')
update wx_t_subject_res_permission set permission_type_id=39 where permission_type_id=7 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='促销活动奖励查看' and m.module_name='市场支持')
update wx_t_subject_res_permission set permission_type_id=37 where permission_type_id=1 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='新客户录入&拜访打卡' and m.module_name='终端客户应用及任务')
update wx_t_subject_res_permission set permission_type_id=38 where permission_type_id=3 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='新客户录入&拜访打卡' and m.module_name='终端客户应用及任务')
update wx_t_subject_res_permission set permission_type_id=39 where permission_type_id=7 and resource_id=(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='新客户录入&拜访打卡' and m.module_name='终端客户应用及任务')

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern'), 'point.exchange.type.permission', 256, 1, '积分及下单礼品促销兑换。256-商用油进货积分', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern'), 'point.exchange.type.permission', 8, 1, '积分及下单礼品促销兑换。8-乘用车进货积分', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern'), 'Dsr.kpi', 16, 1, 'DSR绩效。16-经销商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern'), 'Dsr.kpi', 16, 1, 'DSR绩效。16-经销商', 1, getdate());
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern'), 'newCustomer.tab.permission', 1, 1, '', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern'), 'newCustomer.tab.permission', 1, 1, '', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Dsr.kpi', 4096, 1, 'KPI经销商确认-销量确认tab', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Dsr.kpi', 4096, 1, 'KPI经销商确认-销量确认tab', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Dsr.kpi', 4096, 1, 'KPI经销商确认-销量确认tab', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Commercial_Retailer_Pattern'), 'Dsr.kpi', 2048, 1, 'KPI经销商确认-经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Consumer_Retailer_Pattern'), 'Dsr.kpi', 2048, 1, 'KPI经销商确认-经销商老板确认', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('MaterialPlatform.PP.Retailer.maxPoints', '300', '礼品平台PP兑换分销商最高礼品价值积分数');
