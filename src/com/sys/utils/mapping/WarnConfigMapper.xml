<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.utils.dao.WarnConfigMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.utils.model.WarnConfig">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="warn_title" property="warnTitle" jdbcType="VARCHAR"/>
        <result column="notify_code" property="notifyCode" jdbcType="VARCHAR"/>
		<result column="notify_type" property="notifyType" jdbcType="INTEGER"/>
		<result column="warn_sql" property="warnSql" jdbcType="VARCHAR"/>
		<result column="warn_desc_field" property="warnDescField" jdbcType="VARCHAR"/>
		<result column="warn_detail_field" property="warnDetailField" jdbcType="VARCHAR"/>
		<result column="sort_numb" property="sortNumb" jdbcType="NUMERIC"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,warn_title,notify_type,warn_sql,warn_desc_field,warn_detail_field,sort_numb,enable_flag,delete_flag,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.sys.utils.model.WarnConfig">
		update wx_t_warn_config set
				warn_title = #{warnTitle,jdbcType=VARCHAR},
				notify_code = #{notifyCode,jdbcType=VARCHAR},
				notify_type = #{notifyType,jdbcType=INTEGER},
				warn_sql = #{warnSql,jdbcType=VARCHAR},
				warn_desc_field = #{warnDescField,jdbcType=VARCHAR},
				warn_detail_field = #{warnDetailField,jdbcType=VARCHAR},
				sort_numb = #{sortNumb,jdbcType=NUMERIC},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.utils.model.WarnConfig">
		update wx_t_warn_config
		<set>
			<if test="warnTitle != null" >
				warn_title = #{warnTitle,jdbcType=VARCHAR},
			</if>
			<if test="notifyType != null" >
				notify_type = #{notifyType,jdbcType=INTEGER},
			</if>
            <if test="notifyCode != null" >
                notify_code = #{notifyCode,jdbcType=VARCHAR},
            </if>
			<if test="warnSql != null" >
				warn_sql = #{warnSql,jdbcType=VARCHAR},
			</if>
			<if test="warnDescField != null" >
				warn_desc_field = #{warnDescField,jdbcType=VARCHAR},
			</if>
			<if test="warnDetailField != null" >
				warn_detail_field = #{warnDetailField,jdbcType=VARCHAR},
			</if>
			<if test="sortNumb != null" >
				sort_numb = #{sortNumb,jdbcType=NUMERIC},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.utils.model.WarnConfigExample">
    	delete from wx_t_warn_config
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.utils.model.WarnConfig">
		insert into wx_t_warn_config
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="warnTitle != null">
				warn_title,
			</if>
            <if test="notifyCode != null">
                notify_code,
            </if>
			<if test="notifyType != null">
				notify_type,
			</if>
			<if test="warnSql != null">
				warn_sql,
			</if>
			<if test="warnDescField != null">
				warn_desc_field,
			</if>
			<if test="warnDetailField != null">
				warn_detail_field,
			</if>
			<if test="sortNumb != null">
				sort_numb,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="warnTitle != null">
				#{warnTitle,jdbcType=VARCHAR},
			</if>
            <if test="notifyCode != null">
                #{notifyCode,jdbcType=INTEGER},
            </if>
			<if test="notifyType != null">
				#{notifyType,jdbcType=INTEGER},
			</if>
			<if test="warnSql != null">
				#{warnSql,jdbcType=VARCHAR},
			</if>
			<if test="warnDescField != null">
				#{warnDescField,jdbcType=VARCHAR},
			</if>
			<if test="warnDetailField != null">
				#{warnDetailField,jdbcType=VARCHAR},
			</if>
			<if test="sortNumb != null">
				#{sortNumb,jdbcType=NUMERIC},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_warn_config
		<set>
			<if test="record.warnTitle != null">
				warn_title = #{record.warnTitle,jdbcType=VARCHAR},
			</if>
            <if test="record.notifyCode != null">
                notify_code = #{record.notifyCode,jdbcType=INTEGER},
            </if>
			<if test="record.notifyType != null">
				notify_type = #{record.notifyType,jdbcType=INTEGER},
			</if>
			<if test="record.warnSql != null">
				warn_sql = #{record.warnSql,jdbcType=VARCHAR},
			</if>
			<if test="record.warnDescField != null">
				warn_desc_field = #{record.warnDescField,jdbcType=VARCHAR},
			</if>
			<if test="record.warnDetailField != null">
				warn_detail_field = #{record.warnDetailField,jdbcType=VARCHAR},
			</if>
			<if test="record.sortNumb != null">
				sort_numb = #{record.sortNumb,jdbcType=NUMERIC},
			</if>
			<if test="record.enableFlag != null">
				enable_flag = #{record.enableFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.utils.model.WarnConfigExample">
		delete from wx_t_warn_config
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.utils.model.WarnConfigExample" resultType="int">
		select count(1) from wx_t_warn_config
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.utils.model.WarnConfigExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_warn_config
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.utils.model.WarnConfigExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_warn_config
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.warn_title, t1.notify_code, t1.notify_type, t1.warn_sql, t1.warn_desc_field, t1.warn_detail_field, t1.sort_numb,
			 t1.enable_flag, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_warn_config t1
		 where 1=1
		<if test="warnTitle != null and warnTitle != ''">
			and t1.warn_title like '%' + #{warnTitle, jdbcType=VARCHAR} + '%'
		</if>
		<if test="notifyType != null and notifyType > 0">
			and t1.notify_type &amp; #{notifyType, jdbcType=INTEGER} > 0
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
        <if test="deleteFlag != null">
            and t1.delete_flag = #{deleteFlag, jdbcType=INTEGER}
        </if>
        <if test="id != null">
            and t1.id = #{id, jdbcType=INTEGER}
        </if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.utils.model.WarnConfigParams">
		select t1.id, t1.warn_title, t1.notify_code, t1.notify_type, t1.warn_sql, t1.warn_desc_field, t1.warn_detail_field, t1.sort_numb,
			 t1.enable_flag, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_warn_config t1
		 where t1.delete_flag=0
		<if test="warnTitle != null and warnTitle != ''">
			and t1.warn_title like '%' + #{warnTitle, jdbcType=VARCHAR} + '%'
		</if>
		<if test="notifyType != null and notifyType > 0">
			and t1.notify_type &amp; #{notifyType, jdbcType=INTEGER} > 0
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
	</select>
</mapper>
