package com.chevron.o2oorder.Constant;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Map;

public class Sha1 {
	   public static String getSHA1(String token, String timestamp, String nonce,String url) throws AesException {
	        try {
	            String[] array = new String[]{token, timestamp, nonce,url};
	            StringBuffer sb = new StringBuffer();
	            // 字符串排序
	            Arrays.sort(array);
	            for (int i = 0; i < 3; i++) {
	                sb.append(array[i]);
	            }
	            String str = sb.toString();
	            // SHA1签名生成
	            MessageDigest md = MessageDigest.getInstance("SHA-1");
	            md.update(str.getBytes("UTF-8"));
	            byte[] digest = md.digest();

	            StringBuffer hexstr = new StringBuffer();
	            String shaHex = "";
	            for (int i = 0; i < digest.length; i++) {
	                shaHex = Integer.toHexString(digest[i] & 0xFF);
	                if (shaHex.length() < 2) {
	                    hexstr.append(0);
	                }
	                hexstr.append(shaHex);
	            }
	            return hexstr.toString();
	        } catch (Exception e) {
	            e.printStackTrace();
	            throw new AesException(AesException.ComputeSignatureError);
	        }
	    }
	   
	   public static String getSignature(String token, String timestamp, String nonce,String url) throws AesException {
		   try {
	            String str = "jsapi_ticket="+token+"&noncestr="+nonce+"&timestamp="+timestamp+"&url="+url; 
	            // SHA1签名生成
	            MessageDigest md = MessageDigest.getInstance("SHA-1");
	            md.update(str.getBytes("UTF-8"));
	            byte[] digest = md.digest();
	            StringBuffer hexstr = new StringBuffer();
	            String shaHex = "";
	            for (int i = 0; i < digest.length; i++) {
	                shaHex = Integer.toHexString(digest[i] & 0xFF);
	                if (shaHex.length() < 2) {
	                    hexstr.append(0);
	                }
	                hexstr.append(shaHex);
	            }
	            return hexstr.toString();
	        } catch (Exception e) {
	            e.printStackTrace();
	            throw new AesException(AesException.ComputeSignatureError);
	        }
	   }
	   
	   public static String getSignByMd5(String signTemp) throws AesException {
		try {
			 MessageDigest md = MessageDigest.getInstance("MD5");
			 md.update(signTemp.getBytes("UTF-8"));
	         byte[] digest = md.digest();
	         StringBuffer hexstr = new StringBuffer();
	         String shaHex = "";
	         for (int i = 0; i < digest.length; i++) {
	             shaHex = Integer.toHexString(digest[i] & 0xFF);
	             if (shaHex.length() < 2) {
	                 hexstr.append(0);
	             }
	             hexstr.append(shaHex);
	         }
			 return hexstr.toString();
		}catch (Exception e) {
            e.printStackTrace();
            throw new AesException(AesException.ComputeSignatureError);
        }
	   }
	   
}
