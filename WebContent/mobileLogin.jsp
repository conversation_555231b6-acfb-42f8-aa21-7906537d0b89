<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter"%>
<%@ page import="org.apache.shiro.authc.LockedAccountException "%>
<%@ page import="com.common.exception.auth.WxAuthException "%>
<c:set var="ctx" value="${pageContext.request.contextPath}/" />
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	String failMessage = (String)request.getAttribute("failMessage");
%>
<!DOCTYPE html>
<html>
<head>
<title>登录</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width,height=device-height,inital-scale=1.0,maximum-scale=1.0,user-scalable=no;">  
<meta name="apple-mobile-web-app-capable" content="yes">  
<meta name="apple-mobile-web-app-status-bar-style" content="black">  
<meta name="format-detection" content="telephone=no">  
<script type="text/javascript">
	if(top != window){
		top.location = window.location;
	}
</script>
<script type="text/javascript" src="${ctx }common/hplus/js/jquery.min.js"></script>
<link href="${ctx }css/style.css" rel="stylesheet" type="text/css" media="all" />
<link href="${ctx }css/login.css" rel="stylesheet" />
<link rel="shortcut icon " href="${ctx }images/favicon.ico">
<style type="text/css">
#validationCodeImg {
	position: absolute;
	right: 4px;
	top: 4px;
	display: inline;
	float: right;
	cursor: pointer;
	padding: 4px 5px 4px 2px;
	margin: 0 0;
	background: #fff;
}
</style>
</head>
<body>
	<header>
		<img style="height: 100%;" src="/images/logo2.png">
		<!-- <img style="height: 70%;margin-right: 6px;/* clip-path: inset(0px 9px 0px 0px); */" src="/images/caltex_logo2.png"> -->
		<div class="header-des">雪佛龙合伙人</div>
	</header>
	<div class="main">
		<div class="content">
			<div class="details">
				<div class="loginContainer Hidden">
					<div class="login-frame">
						<form name="loginForm" id="loginForm" action="login.do" method="post">
							<div class="mdLogin">
								<div class="user">用户登录</div>
								<% if(failMessage != null){ %>
									<div class="warnningDiv" id='msg' style="display:block;"><%=failMessage%></div>
								<% } else {%>
									<div class="warnningDiv" id='msg'></div>
								<% } %>
								<div class="inputDiv Relative" id="emailDiv">
									<div class="emailIcon"></div>
									<input type="text" class="txtBox" id="usernameForm" autocomplete="off" placeholder="帐号"/>
									<input type="hidden" name="username"/>
								</div>
								<div class="inputDiv Relative">
									<div class="passwordIcon"></div>
									<input type="password" autocomplete="off" class=" txtBox txtBoxPassword" id="txtPassword" placeholder="密码" />
									<input type="hidden" name="password">
									<div class="Clear"></div>
								</div>

								<div class="inputDiv Relative">
									<div class="validateCodeIcon"></div>
									<input type="text" autocomplete="off"
										class="txtBox " onkeydown="if(event.keyCode == 13){formsubmit();};" id="validateCode" placeholder="验证码">
									<img id="validationCodeImg" src="" title="点击刷新">
									<input type="hidden" name="validateCode">
								</div>
								<input id="loginMode" value="company" type="hidden">
								<div class="loginDiv">
									<input type="button" class="btnLogin" onclick="formsubmit();" id="btnLogin" value="登 录">
								</div>
							</div>
						</form>
					</div>
					<div style="clear: both;"></div>
				</div>
			</div>
		</div>
	</div>

	<script type="text/javascript" src="${ctx }common/js/qrcode.js"></script>
	<script type="text/javascript" src="${ctx }common/js/jquery.qrcode.js"></script>
	<script type="text/javascript" src="${ctx }common/js/common.js"></script>
	<script type="text/javascript" src="${ctx }common/js/mobileLogin.js?v=0.2"></script>
	
<!-- 添加友盟统计代码 并隐藏 -->
<%@include file="/common/jsp/umeng.jsp"%>
</body>
</html>
