
insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSale.T3CategoryReport.excelColumn', '非本地营销T3Category报表导出列配置', '非本地营销T3Category报表导出列配置');
--delete d from wx_t_dic_item d where dic_type_code='NonLocalSale.T3CategoryReport.excelColumn'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', 'T3 Category', 't3_category', '{"width":20}', 1, '10');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', '非本地营销大区', 'from_distributor_name', '{"width":40}', 1, '20');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', 'Admin手工录入', 'admin_record_times', '{"align":"RIGHT","dataType":"number"}', 1, '30');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', '合伙人App扫码', 'app_scan_times', '{"align":"RIGHT","dataType":"number"}', 1, '40');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', '技师核销扫码', 'mechanic_scan_times', '{"align":"RIGHT","dataType":"number"}', 1, '50');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', '用户扫码验真', 'user_scan_times', '{"align":"RIGHT","dataType":"number"}', 1, '60');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.T3CategoryReport.excelColumn', 'Total', 'total_times', '{"align":"RIGHT","dataType":"number"}', 1, '70');
