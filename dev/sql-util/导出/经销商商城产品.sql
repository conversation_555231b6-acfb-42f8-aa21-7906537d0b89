select dp.sku, dp.product_name "产品名称", 
case when dp.product_photo is null then '' else 'https://www.cvx-sh.com/downloadAttachmentFile.do?appToken=2d092a31622c88b5b490fe30699db1d37f0888d7&attId=' + convert(nvarchar(30),dp.product_photo) end "产品缩略图",
case when dp.product_hd_photo is null then '' else 'https://www.cvx-sh.com/downloadAttachmentFile.do?appToken=2d092a31622c88b5b490fe30699db1d37f0888d7&attId=' + convert(nvarchar(30), dp.product_hd_photo) end "产品高清图",
dp.product_desc "产品详情"
  from wx_t_db2b_product dp 
where dp.partner_id=-1
