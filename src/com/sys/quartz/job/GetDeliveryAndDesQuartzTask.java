package com.sys.quartz.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.sellin.business.SellInPromotionDeliveryBizService;
import com.sys.log.util.LogUtils;

import java.util.Date;

public class GetDeliveryAndDesQuartzTask {

	@Autowired
	private SellInPromotionDeliveryBizService sellInPromotionDeliveryBizService;
	
	private static Logger log = LoggerFactory.getLogger(GetDeliveryAndDesQuartzTask.class);
	
	public synchronized void doGetDeliveryAndDesQuartzTask()
	{
		log.info("GetDeliveryAndDesQuartzTask job start....");
		LogUtils.addInfoLog(1l, "sellInPromotionDeliveryBizService.sendDeliveryAndDes", "开始拉取促销发放/促销规则信息："+new Date());
		try {
			sellInPromotionDeliveryBizService.sendDeliveryAndDes();
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.addErrorLog(1l, "sellInPromotionDeliveryBizService.sendDeliveryAndDes", "拉取促销发放/促销规则信息失败。" + e.getMessage(), null);
		}
		log.info("SendEmailForRenamWorkShop job finish....");
		LogUtils.addInfoLog(1l, "WorkshopMasterBizServiceImpl.sendEmailForRenam", "发送同一经销售下面相同门店名称报警邮件结束："+new Date());
	}
}
