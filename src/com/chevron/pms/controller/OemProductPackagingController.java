package com.chevron.pms.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.common.exception.auth.WxAuthException;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.common.constants.Constants;
import com.chevron.log.model.WxTImportLog;
import com.chevron.log.service.WxTImportLogService;
import com.chevron.pms.model.GenericPaginationQueryParams;
import com.chevron.pms.model.OemProductPackaging;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.WorkshopParams;
import com.chevron.pms.service.OemProductPackagingService;
import com.common.util.DateUtils;
import com.common.util.ServletUtils;
import com.common.util.StringUtils;
import com.sys.quartz.service.OemImportService;

@Controller
public class OemProductPackagingController {

	@Autowired
	private OemProductPackagingService oppService;
	
	@Autowired
	private OemImportService importService;

	@Autowired
	private WxTImportLogService logService;

	private Logger log = LoggerFactory
			.getLogger(OemProductPackagingController.class);

	private static final String ROWS = "rows";
	private static final String TOTAL = "total";
	private static final String RANK_TYPE = "direction";
	private static final String SORT_FILED = "field";
	private static final String LIMIT = "limit";
	private static final String START = "start";

	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";

	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";

	@ResponseBody
	@RequestMapping("/oem/importproduct.do")
	public List<Map<String, Object>> data() {
		List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
		String filePath = "C:\\chevrontest";
		File file = new File(filePath);
		try {
			List<WxTImportLog> logList = logService
					.selectLogByImportResultAndType(Constants.SUCCESS,
							Constants.OEM_PRODUCT_PACKAGING_TYPE);
			Set<String> sucFileNameSet = new HashSet<String>();
			for (WxTImportLog temp : logList) {
				sucFileNameSet.add(temp.getFileName());
			}

			String[] filelist = file.list();
			for (int i = 0; i < filelist.length; i++) {
				File readfile = new File(filePath + "\\" + filelist[i]);

				String fileName = readfile.getName();
				if (sucFileNameSet.contains(fileName)) {
					continue;
				}

				System.out.println(filePath + "\\" + filelist[i]);
				Map<String, Object> importReult = oppService
						.importOemProductPackaging(fileName,readfile);
				resultList.add(importReult);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultList;
	}

	@ResponseBody
	@RequestMapping(value = "/oem/importProductCodes.do", method = RequestMethod.POST)
	public Object importZsPartsBrandBat(
			@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try {
			CommonsMultipartFile cf = (CommonsMultipartFile) myfiles[0];
			DiskFileItem fi = (DiskFileItem) cf.getFileItem();

			File readfile = fi.getStoreLocation();
			/*List<WxTImportLog> logList = logService
					.selectLogByImportResultAndType(Constants.SUCCESS,
							Constants.OEM_PRODUCT_PACKAGING_TYPE);
			Set<String> sucFileNameSet = new HashSet<String>();
			for (WxTImportLog temp : logList) {
				sucFileNameSet.add(temp.getFileName());
			}
			String fileName = readfile.getName();
			if (sucFileNameSet.contains(fileName)) {
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息：该文件已经导入，不能重复导入！"  );
			}*/
			String fileName = myfiles[0].getOriginalFilename();
			Map<String, Object> importReult = importService
					.importOemProductPackaging(fileName,readfile);
			return importReult;
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/oem/queryProcut.do", method = { RequestMethod.POST })
	public Map<String, Object> queryOrders(HttpServletRequest request,
			HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try {
			// 1, collect request params
			Map<String, String> requestParamMap = ServletUtils
					.collectParamValues(request);
			log.info("requestParamMap: {}", requestParamMap);

			// 2, convert request param to generic pagination query param
			GenericPaginationQueryParams genericPagQeryParam = convertRequestParamToQueryParam(requestParamMap);

			// 3, query data from db
			long oppCount = oppService
					.countProductPackingByConditionWithPagination(genericPagQeryParam);
			List<OemProductPackaging> oppList = oppService
					.queryProductPackingByConditionWithPagination(genericPagQeryParam);

			// 4, generate return data
			resultMap.put(TOTAL, oppCount);
			resultMap.put(ROWS, oppList);
		} catch (Exception e) {
			log.error(e.getMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	private GenericPaginationQueryParams convertRequestParamToQueryParam(
			Map<String, String> requestParamMap) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		genericPagQueryParam
				.setStart(Long.parseLong(requestParamMap.get(START)));
		genericPagQueryParam.setLimit(Integer.parseInt(requestParamMap
				.get(LIMIT)));

		String orderByFiled = requestParamMap.get(SORT_FILED);
		String orderByRankType = requestParamMap.get(RANK_TYPE);
		String orderBy = StringUtils.camelToUnderline(orderByFiled) + " "
				+ orderByRankType;
		genericPagQueryParam.setOrderBy(orderBy);

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();

		String productId = requestParamMap.get("productId");
		if (null != productId && !productId.isEmpty()) {
			queryParamsMap.put("productId", productId);
		}

		String productBatch = requestParamMap.get("productBatch");
		if (null != productBatch && !productBatch.isEmpty()) {
			queryParamsMap.put("productBatch", productBatch);
		}

		String productBanci = requestParamMap.get("productBanci");
		if (null != productBanci && !productBanci.isEmpty()) {
			queryParamsMap.put("productBanci", productBanci);
		}

		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		return genericPagQueryParam;
	}

}
