<%@page import="com.sys.auth.model.WxTUser"%> <%@page
import="com.common.util.ContextUtil"%> <%@ page language="java"
contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%> <%@ taglib
prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set
  var="initParams"
  value="${param.cacheParams ? sessionScope.workshopMaster_params : null}"
/>
<% Integer permissionWeight = (Integer)request.getAttribute("permissionWeight");
Long userId = (Long)request.getAttribute("userId"); WxTUser user =
ContextUtil.getCurUser(); %>

<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>门店</title>
    <%@include file="/common/jsp/common.jsp"%>
    <script
      type="text/javascript"
      src="${ctx }business/master/js/workshopMasterPage.js?v=${version}1"
    ></script>
    <script
      type="text/javascript"
      src="${ctx }business/master/js/upload.js"
    ></script>
    <style type="text/css">
      .query-panel div.cond-wlabel1 {
        display: none;
      }
      div.wlabel1 div.cond-wlabel1 {
        display: inline-block;
      }
      .error-info {
        display: none;
        margin-top: 20px;
        margin-bottom: 20px;
        background-color: #fff;
        border: 1px solid transparent;
        border-radius: 4px;
        -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
        border-color: #ddd;
      }
      .error-info-panel {
        padding: 15px;
      }
    </style>
    <script type="text/javascript">
          var initParams = {
          		start: '${initParams.start}',
          		limit: '${initParams.limit}',
          		field: '${initParams.field}',
          		direction: '${initParams.direction}'
          	};
          var toItem = '${param.toItem}';
          var includeSalesChannels = <%=user.getIncludeChannels() %>;
          var loginOrgType = <%=user.getOrgType() %>;
          var userId = ${userId}, userModel = '${userModel}', permissionWeight = ${empty permissionWeight ? -1 : permissionWeight},
          		workshopPropertySource = ${workshopPropertySource},
          		fromSourceSource = ${fromSourceSource},
          		customerTypeSource = ${customerTypeSource};
    </script>
  </head>
  <body class="gray-bg">
    <div class="content-wrapper">
      <div id="nav" class="ui-layout-north">
        <div class="tab content-panel">
          <ul class="nav nav-tabs">
            <li class="bui-tab-panel-item">
              <a href="${ctx }SPA/store/index.jsp">地图</a>
            </li>
            <li class="bui-tab-panel-item active">
              <a href="${ctx }workshopmaster/index.do">列表</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="content-panel query-panel query-adv" id="queryPanel">
        <div class="field-group key-search">
          <label class="field-label width-auto">搜索：</label>
          <div class="control-group">
            <input
              type="text"
              id="gKeyWord"
              name="gKeyWord"
              value="${initParams.queryField}"
              class="control-text"
            />
          </div>
        </div>
        <div
          class="field-group cond-adv hide-important"
          id="partnerCondWrapper"
        >
          <label class="field-label">经销商：</label>
          <div class="control-group">
            <input
              id="partnerId"
              name="partnerId"
              value="${initParams.partnerId }"
              text="${initParams.partnerName }"
              type="hidden"
            />
          </div>
        </div>

        <div class="field-group cond-adv">
          <label class="field-label">客户名称：</label>
          <div class="control-group">
            <input
              type="text"
              id="workshopName"
              name="workshopName"
              value="${empty workshopName ? initParams.workshopName : workshopName}"
              class="control-text"
            />
          </div>
        </div>

        <div class="field-group cond-adv">
          <label class="field-label">执行人：</label>
          <div class="control-group">
            <input
              id="executeUserId"
              name="executeUserId"
              value="${initParams.executeUserId }"
              text="${initParams.executeUserName }"
              type="hidden"
            />
          </div>
        </div>
        <div class="field-group cond-adv">
          <label class="field-label">省：</label>
          <div class="control-group">
            <input
              type="hidden"
              name="prov"
              id="provHid"
              value="${empty initParams.prov ? '-1' : initParams.prov}"
            />
          </div>
        </div>
        <div class="field-group cond-adv">
          <label class="field-label">市：</label>
          <div class="control-group">
            <input
              type="hidden"
              name="city"
              id="cityHid"
              value="${empty initParams.city ? '-1' : initParams.city}"
            />
          </div>
        </div>
        <div class="field-group cond-adv">
          <label class="field-label">区：</label>
          <div class="control-group">
            <input
              type="hidden"
              name="dist"
              id="distHid"
              value="${empty initParams.dist ? '-1' : initParams.dist}"
            />
          </div>
        </div>
        <div class="field-group cond-adv" id="fromSourceCond">
          <label class="field-label">客户类型：</label>
          <div class="control-group">
            <input
              type="hidden"
              id="customerType"
              name="customerType"
              value="${empty initParams.customerType ? '-1' : initParams.customerType}"
            />
          </div>
        </div>
        <%--
        <div class="field-group cond-adv" id="fromSourceCond">
          --%> <%-- <label class="field-label">客户来源：</label>--%> <%--
          <div class="control-group">
            --%> <%--
            <input
              type="hidden"
              id="fromSource"
              name="fromSource"
              value="${empty initParams.fromSource ? '-1' : initParams.fromSource}"
            />--%> <%--
          </div>
          --%> <%--
        </div>
        --%>
        <div class="field-group cond-adv" id="workshopPropertiesCond">
          <label class="field-label">客户标签：</label>
          <div class="control-group">
            <input
              type="hidden"
              id="workshopProperties"
              name="workshopProperties"
              value="${empty initParams.workshopProperties ? '-1' : initParams.workshopProperties}"
            />
          </div>
        </div>
        <!-- <div class="field-group cond-adv cond-wlabel1">
					<label class="field-label">门店等级：</label>
					<div class="control-group">
						<input type="hidden" id="gradeAbc" name="gradeAbc" value="{empty initParams.gradeAbc ? '-1' : initParams.gradeAbc}"/>
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">门店类型：</label>
					<div class="control-group">
						<input type="hidden" id="type" name="type" value="{empty initParams.type ? '-1' : initParams.type}"/>
					</div>
				</div> -->
        <div class="field-group cond-adv">
          <label class="field-label">客户状态：</label>
          <div class="control-group">
            <input
              type="hidden"
              name="status"
              id="status"
              value="${empty initParams.status ? '-99' : initParams.status}"
            />
          </div>
        </div>
        <div class="field-group cond-adv">
          <label class="field-label">品牌：</label>
          <div class="control-group">
            <input
              type="hidden"
              name="businessWeights"
              id="businessWeights"
              value="${empty initParams.businessWeights ? '-999' : initParams.businessWeights}"
            />
          </div>
        </div>

        <div class="field-group cond-adv">
          <label class="field-label">激活开始日期：</label>
          <div class="control-group">
            <input
              type="text"
              id="activeDateFrom"
              name="activeDateFrom"
              value="${initParams.activeDateFromStr }"
              placeholder="开始时间"
              class="control-text control-calendar"
              onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'activeDateTo\')}'});"
            />
          </div>
        </div>
        <div class="field-group cond-adv">
          <label class="field-label">激活结束日期：</label>
          <div class="control-group">
            <input
              type="text"
              id="activeDateTo"
              name="activeDateTo"
              value="${initParams.activeDateToStr }"
              placeholder="结束时间"
              class="control-text control-calendar"
              onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'activeDateFrom\')}'});"
            />
          </div>
        </div>
        <!-- todo -->
        <div class="field-group cond-adv">
          <label class="field-label">路线：</label>
          <div class="control-group">
            <input
              type="hidden"
              name="routes"
              id="routes"
              value="${SCFLSRWeight}"
              class="control-text"
            />
          </div>
        </div>
        <%--
        <div class="field-group cond-adv">
          <label class="field-label">是否停用：</label>
          <div class="control-group">
            <input
              type="hidden"
              id="deleteFlag"
              name="deleteFlag"
              value="${empty initParams.deleteFlag ? '0' : initParams.deleteFlag}"
            />
          </div>
        </div>
        --%>
        <div class="query-btns">
          <div class="query-btn field-label">
            <button onclick="query();" class="btn-query">查询</button>
          </div>
          <!-- 
					<div class="adv-toggle-btn">
						<a href="javascript: void(0);" onclick="var el = $(this), queryPanel = el.parents('.query-panel:first');if(queryPanel.hasClass('query-adv')){queryPanel.removeClass('query-adv');el.text('高级搜索');}else{queryPanel.addClass('query-adv');el.text('收起');}">高级搜索</a>
					</div> -->
        </div>
      </div>
      <div class="content-panel tools-panel">
        <c:if test="${dmsMatchPermission }">
          <button
            type="button"
            class="btn-success"
            onclick="window.location='${ctx }SPA/DoorMap/index.jsp';"
          >
            DMS门店匹配
          </button>
        </c:if>
        <%if(permissionWeight != null && (permissionWeight & 32) > 0){ %>
        <button
          type="button"
          class="btn-create"
          onclick="window.location='${ctx }business/master/editWorkshopMaster.jsp?fromPage=list&partnerId=${orgId }&fromSource=4';"
        >
          创建新EC门店
        </button>
        <%} %> <%if(permissionWeight != null && permissionWeight > 0 &&
        (permissionWeight & 1024) > 0){ %>
        <button
          type="button"
          class="btn-create"
          onclick="window.location='${ctx }SPA/sales-client/index.jsp#/detail';"
        >
          创建个人/批发商
        </button>
        <%} %>
        <button type="button" class="btn-export" onclick="exportGrid();">
          导出
        </button>
        <!-- todo -->
        <button class="btn-success" onclick="importBat('routes')">
          <i class="fa fa-upload"></i> 导入
        </button>
      </div>
      <div class="content-panel" id="grid"></div>
      <div style="display: none">
        <div id="import" style="text-align: center">
          <div class="import-border" style="height: 50px">
            <div class="import-header">步骤一：模版下载</div>
            <span
              style="color: #319dfc; text-decoration: none; cursor: pointer"
              onclick="downloadExcelTemp()"
              class="excel"
              >模版下载</span
            >
          </div>
          <div class="import-border">
            <div class="import-header">步骤二：整理数据并导入</div>
            <form id="uploadfile">
              <input type="file" name="myfiles" accept=".xls,.xlsx,.xlsm" />
            </form>
            <span
              id="fileUpload"
              style="
                width: 80px important;
                display: inline;
                color: white;
                float: left;
                margin: 12px 0px 0px 6px;
              "
            ></span>
          </div>
        </div>
        <div id="errorMSG">
          <div id="errorGrid"></div>
        </div>
      </div>
    </div>
    <div class="hide-important"></div>
    <form
      action=""
      id="downloadForm"
      name="downloadForm"
      method="post"
      target="downloadWin"
      style="display: none"
    ></form>
    <iframe name="downloadWin" id="downloadWin" style="display: none"></iframe>
<!-- 添加友盟统计代码 并隐藏 -->
<%@include file="/common/jsp/umengapi.jsp"%>
<%@include file="/common/jsp/umeng.jsp"%>

  </body>
</html>
