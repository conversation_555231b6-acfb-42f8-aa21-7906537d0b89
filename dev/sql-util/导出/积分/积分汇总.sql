SELECT  pp.point_name "积分类型",max(o.organization_name) "经销商名称", --u.login_name "登录名", u.ch_name "姓名", 
sum(pde.POINT_VALUE) as "发放积分", 
--sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
(select sum(pde1.POINT_VALUE-pde1.POINT_PAYED) from wx_t_point_value_detail pde1 where pde1.POINT_ACCOUNT_ID=pde.POINT_ACCOUNT_ID and pde1.point_type=pde.point_type) "总剩余",
case when o.status=1 then 'Active' else 'Inactive' end "经销商状态"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on pp.point_code='DSR_CIO_POINT' and u.user_id = pac.POINT_ACCOUNT_OWNER_ID
		left join wx_t_organization o on o.id=(case when pp.point_code='DSR_CIO_POINT' then u.org_id else pac.POINT_ACCOUNT_OWNER_ID end)
        WHERE bu.BUSINESS_TYPE_CODE not in ('ADJUST_POINT','ROLLBACK_POINT','CLEAR_POINT')
		and pde.TRANS_TIME>='2021-01-01'
		--and bu.CREATION_TIME>'2021-01-01'
		group by o.id,o.status,pp.point_name,pde.point_type,pde.POINT_ACCOUNT_ID
		order by pp.point_name
		