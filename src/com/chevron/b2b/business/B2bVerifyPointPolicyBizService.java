package com.chevron.b2b.business;

import com.chevron.b2b.model.B2bVerifyPointPolicy;
import com.chevron.b2b.model.B2bVerifyPointPolicyExample;
import com.chevron.b2b.model.B2bVerifyPointPolicyParams;
import com.common.exception.WxPltException;

import java.util.List;
import java.util.Map;

/**
 * B2B核销积分政策业务接口
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
public interface B2bVerifyPointPolicyBizService {
	/**
	 * 修改B2B核销积分政策
	 * @param record 被修改B2B核销积分政策
	 * @throws WxPltException
	 */
	public void save(B2bVerifyPointPolicy record) throws WxPltException;
	
	/**
	 * 删除B2B核销积分政策
	 * @param ids 被删除B2B核销积分政策id集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的B2B核销积分政策
	 * @param example 被删除B2B核销积分政策满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(B2bVerifyPointPolicyExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<B2bVerifyPointPolicy> queryByExample(B2bVerifyPointPolicyExample example) throws WxPltException;
	
//	/**
//	 * 列表查询B2B核销积分政策对象
//	 * @param params 查询条件
//	 * @return 满足条件B2B核销积分政策对象集合
//	 */
//    public List<B2bVerifyPointPolicy> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 获取指定主键的B2B核销积分政策对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public B2bVerifyPointPolicy getBean(Long id) throws WxPltException;	

	/**
	 * 分页查询B2B核销积分政策
	 * @param params 查询参数
	 * @param resultMap 结果对象
	 * @throws WxPltException
	 */
	public void queryForPage(B2bVerifyPointPolicyParams params, Map<String, Object> resultMap) throws WxPltException;
}
