
			select * from (SELECT ma.APPLICATION_CODE "订单号",
			case when ma.APPLICATION_STATUS='VIRTUAL_SUBMIT' then '已提交' 
				when ma.APPLICATION_STATUS='VIRTUAL_SUCCESS' then '兑换成功'
				when ma.APPLICATION_STATUS='VIRTUAL_FAILED' then '兑换失败'
				else ma.APPLICATION_STATUS end "状态",
			case when ma.APPLICATION_TYPE='b2b' then '大咖会' when ma.APPLICATION_TYPE='dsr_cio' then 'DSR' else ma.APPLICATION_TYPE end "订单类型",
			 CONVERT(varchar(100), ma.APPLICATION_TIME, 20) "时间",
			 ma.CONTACT_NUMBER "充值号码", case when ma.APPLICATION_TYPE='b2b' THEN ma.WORKSHOP_NAME else o.organization_name end "组织机构",
			case when ma.APPLICATION_TYPE='b2b' THEN aa.name else u.CH_NAME end "充值人",
			md1.MATERIAL_NAME "礼品名称", m1.MATERIAL_CODE "礼品编码", s1.supplier_name "货源", mad.MATERIAL_PRICE "积分数",
			mad.APPLICATION_QTY "数量"
					FROM wx_t_material_application ma
			left join wx_t_material_application_detail mad on mad.DELETE_FLAG = 0 AND mad.APPLICATION_ID = ma.id
	left join wx_t_point_material pm1 on pm1.id=mad.MATERIAL_ID
		  left join wx_t_material2021 m1 on pm1.material_id=m1.id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
				LEFT JOIN wx_t_user u ON ma.APPLICATION_PERSON_ID = u.user_id
				LEFT JOIN wx_t_organization o ON ma.APPLICATION_ORG_ID = o.id
				LEFT JOIN wx_t_region dist ON dist.region_code = ma.ADDRESS_REGION
				LEFT JOIN wx_t_region city ON dist.parent_id = city.id
				LEFT JOIN wx_t_region province ON city.parent_id = province.id
				LEFT JOIN wx_t_workshop_employee aa ON aa.code = ma.APPLICATION_USER_ID
			WHERE ma.delete_flag = 0 and ma.version_no>=202109
				AND ma.APPLICATION_STATUS IN ('VIRTUAL_SUBMIT', 'VIRTUAL_SUCCESS', 'VIRTUAL_FAILED')
				AND (ma.application_type IN ('b2b','dsr_cio')
					OR ma.promotion_type IN ('b2b','dsr_cio'))


			union all SELECT ma.APPLICATION_CODE "订单号",
			case when ma.APPLICATION_STATUS='VIRTUAL_SUBMIT' then '已提交' 
				when ma.APPLICATION_STATUS='VIRTUAL_SUCCESS' then '兑换成功'
				when ma.APPLICATION_STATUS='VIRTUAL_FAILED' then '兑换失败'
				else ma.APPLICATION_STATUS end "状态",
			case when ma.APPLICATION_TYPE='b2b' then '大咖会' when ma.APPLICATION_TYPE='dsr_cio' then 'DSR' else ma.APPLICATION_TYPE end "订单类型",
			 CONVERT(varchar(100), ma.APPLICATION_TIME, 20) "时间",
			 ma.CONTACT_NUMBER "充值号码", case when ma.APPLICATION_TYPE='b2b' THEN ma.WORKSHOP_NAME else o.organization_name end "组织机构",
			case when ma.APPLICATION_TYPE='b2b' THEN aa.name else u.CH_NAME end "充值人",
			m.MATERIAL_NAME "礼品名称", m.MATERIAL_CODE "礼品编码", mw.WAREHOUSE_NAME "货源", mad.MATERIAL_PRICE "积分数",
			mad.APPLICATION_QTY "数量"
					FROM wx_t_material_application ma
			left join wx_t_material_application_detail mad on mad.DELETE_FLAG = 0 AND mad.APPLICATION_ID = ma.id
	LEFT JOIN wx_t_material m
	ON m.ID = mad.MATERIAL_ID
	LEFT JOIN wx_t_material_sku s
	ON s.MATERIAL_ID = mad.MATERIAL_ID
		AND s.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE
	LEFT JOIN wx_t_material_warehouse mw ON mad.warehouse_id = mw.id
	LEFT JOIN wx_t_dic_item di
	ON di.DIC_TYPE_CODE = 'material.type'
		AND di.DIC_ITEM_CODE = m.MATERIAL_TYPE
				LEFT JOIN wx_t_user u ON ma.APPLICATION_PERSON_ID = u.user_id
				LEFT JOIN wx_t_organization o ON ma.APPLICATION_ORG_ID = o.id
				LEFT JOIN wx_t_region dist ON dist.region_code = ma.ADDRESS_REGION
				LEFT JOIN wx_t_region city ON dist.parent_id = city.id
				LEFT JOIN wx_t_region province ON city.parent_id = province.id
				LEFT JOIN wx_t_workshop_employee aa ON aa.code = ma.APPLICATION_USER_ID
			WHERE ma.delete_flag = 0 and ma.version_no=0
				AND ma.APPLICATION_STATUS IN ('VIRTUAL_SUBMIT', 'VIRTUAL_SUCCESS', 'VIRTUAL_FAILED')
				AND (ma.application_type IN ('b2b','dsr_cio')
					OR ma.promotion_type IN ('b2b','dsr_cio'))) a order by a.[时间] desc
	
