--中商数据清理
select * into wx_t_oem_product_packaging_code20230321 from wx_t_oem_product_packaging_code  pc where pc.creation_time>'2022-01-01'
delete pc from wx_t_oem_product_packaging_code  pc where pc.creation_time>'2022-01-01'

select * into wx_t_oem_delivery_product20230321 from wx_t_oem_delivery_product  pc where pc.creation_time>'2022-01-01'
delete pc from wx_t_oem_delivery_product  pc where pc.creation_time>'2022-01-01'


--清理流程数据
delete wiel1
from wx_t_workflow_instance wi1
		 left join wx_t_workflow_step_instance wsi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 where wi1.apply_time<'2022-01-01' 
		 
delete wsh1
from wx_t_workflow_instance wi1
		 left join wx_t_workflow_step_instance wsi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_workflow_step_history wsh1 on wsh1.step_instance_id=wsi1.step_instance_id
		 where wi1.apply_time<'2022-01-01' 
		 
delete wsi1
from wx_t_workflow_instance wi1
		 left join wx_t_workflow_step_instance wsi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_workflow_step_history wsh1 on wsh1.step_instance_id=wsi1.step_instance_id
		 where wi1.apply_time<'2022-01-01' 
		 
delete wi1
from wx_t_workflow_instance wi1
		 left join wx_t_workflow_step_instance wsi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_workflow_step_history wsh1 on wsh1.step_instance_id=wsi1.step_instance_id
		 where wi1.apply_time<'2022-01-01' 		 