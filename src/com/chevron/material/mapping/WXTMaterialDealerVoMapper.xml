<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialDealerVoMapper">
	<resultMap id="BaseResultMap" type="com.chevron.material.model.MaterialDealerVo" > 
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="organization_code" property="organizationCode" jdbcType="NVARCHAR" />
    <result column="organization_name" property="organizationName" jdbcType="NVARCHAR" />
    <result column="region_name" property="regionName" jdbcType="NVARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="BIGINT" />
    <result column="esspflag" property="esspflag" jdbcType="INTEGER" />
    <result column="sap_code" property="sapCode" jdbcType="NVARCHAR" />
    <result column="address_count" property="addressCount" jdbcType="NVARCHAR" />
    <result column="promotion_point" property="promotionPoint" jdbcType="NUMERIC" />
    <result column="caltex_point" property="caltexPoint" jdbcType="NUMERIC" />
    <result column="partner_property" property="partnerProperty" jdbcType="NVARCHAR" />
    <result column="kpi_target" property="kpiTarget" jdbcType="BIGINT" />
    <result column="liters" property="liters" jdbcType="NUMERIC" />
    <result column="kpi_percent" property="kpiPercent" jdbcType="NUMERIC" />
    <result column="count_pending_point" property="countPendingPoint" jdbcType="BIGINT" />
    <result column="count_changed_record" property="countChangedRecord" jdbcType="BIGINT" />
    
    <result column="cai_gift_promotion" property="caiGiftPromotion" jdbcType="NUMERIC" />
    <result column="cdm_gift_promotion" property="cdmGiftPromotion" jdbcType="NUMERIC" />
    <result column="cdm_stock_point" property="cdmStockPoint" jdbcType="NUMERIC" />
    <result column="cdm_material_point" property="cdmMaterialPoint" jdbcType="NUMERIC" />
    <result column="cdm_promotion_point" property="cdmPromotionPoint" jdbcType="NUMERIC" />
	<result column="cdm_red_bag_point" property="cdmRedBagPoint" jdbcType="NUMERIC" />
    <result column="oem_stock_point" property="oemStockPoint" jdbcType="NUMERIC" /> 
    <result column="cdm_pit_pack_point" property="cdmPitPackPoint" jdbcType="NUMERIC" /> 
    <result column="cdm_store_open_point" property="cdmStoreOpenPoint" jdbcType="NUMERIC" /> 
    <result column="frozen_type" property="frozenType" jdbcType="NVARCHAR"/>
	<result column="consumer_hero_point" property="consumerHeroPoint" jdbcType="NUMERIC" />
	<result column="commercial_hero_point" property="commercialHeroPoint" jdbcType="NUMERIC" />
  </resultMap>

	<sql id="query_sales_channel_relation">
		(SELECT DISTINCT
		v.distributor_id,
		v.channel_weight 
	FROM
		view_customer_region_sales_channel v
		
		)
	</sql>
  <select id="queryDealerByParam" resultMap="BaseResultMap" parameterType="com.chevron.material.model.MaterialDealerParams">
 SELECT distinct d.id,d.organization_name,d.region_name,d.sap_code,d.address_count,d.caltex_point,d.cdm_material_point,d.cdm_promotion_point,d.cdm_stock_point,<!-- d.cdm_red_bag_point,d.cdm_store_open_point, -->d.frozen_type,
		d.oem_stock_point,d.promotion_point,d.cai_gift_promotion,d.cdm_gift_promotion,commercial_hero_point,consumer_hero_point from (
	SELECT
	o.organization_name,crsc.region_name,
	o.id,
	e.sap_code,
	(
		SELECT
			count(*)
		FROM
			wx_t_material_address_config mac
		WHERE
			mac.PARTNER_ID = o.id
	) AS address_count,
	isnull((
				SELECT
					SUM(gp.available_quantity)
				FROM
					wx_t_promotion_gift_pool gp
				WHERE
					gp.application_type = 'promotion'
				AND partner_id = o.id),0
				) as cai_gift_promotion,
			isnull((
				SELECT
					SUM(gp.available_quantity)
				FROM
					wx_t_promotion_gift_pool gp
				WHERE
					gp.application_type = 'cdm_promotion'
				AND partner_id = o.id),0
				) as cdm_gift_promotion,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				1 = 1
				AND pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'PROMOTION_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as promotion_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CALTEX_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as caltex_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				1 = 1
				AND pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CDM_STOCK_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as cdm_stock_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CDM_MATERIAL_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as cdm_material_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'CDM_PROMOTION_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as cdm_promotion_point,
	  <!-- isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'CDM_RED_BAG_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as cdm_red_bag_point,
	  	isnull(
			(
		SELECT SUM
			( pvd.POINT_VALUE - pvd.POINT_PAYED ) 
		FROM
			wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON pc.ID = pvd.POINT_ACCOUNT_ID 
		WHERE
			pvd.DELETE_FLAG = 0 
			AND pvd.POINT_TYPE = 'CDM_STORE_OPEN_POINT' 
			AND pc.POINT_ACCOUNT_OWNER_ID = o.id 
			),
			0 
			) AS cdm_newstore_open_point, -->
	  	isnull(
			(
		SELECT SUM
			( pvd.POINT_VALUE - pvd.POINT_PAYED ) 
		FROM
			wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON pc.ID = pvd.POINT_ACCOUNT_ID 
		WHERE
			pvd.DELETE_FLAG = 0 
			AND pvd.POINT_TYPE = 'CONSUMER_HERO_PRODUCT_POINT' 
			AND pc.POINT_ACCOUNT_OWNER_ID = o.id 
			),
			0 
			) AS consumer_hero_point,
	  	isnull(
			(
		SELECT SUM
			( pvd.POINT_VALUE - pvd.POINT_PAYED ) 
		FROM
			wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON pc.ID = pvd.POINT_ACCOUNT_ID 
		WHERE
			pvd.DELETE_FLAG = 0 
			AND pvd.POINT_TYPE = 'COMMERCIAL_HERO_PRODUCT_POINT' 
			AND pc.POINT_ACCOUNT_OWNER_ID = o.id 
			),
			0 
			) AS commercial_hero_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'OEM_STOCK_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as oem_stock_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'CDM_STORE_OPEN_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as cdm_store_open_point,
	  
	isnull(out_pc.frozen_type, '') as frozen_type,
	pvds.POINT_TYPE
FROM
	wx_t_organization o
LEFT JOIN wx_t_partner_o2o_enterprise e ON
	o.id = e.partner_id
LEFT JOIN view_customer_region_sales_channel  crsc ON e.distributor_id = crsc.distributor_id
<!-- LEFT JOIN <include refid="query_sales_channel_relation"/> dwrsr ON dwrsr.distributor_id = e.distributor_id -->
LEFT JOIN wx_t_point_account out_pc ON
	  out_pc.POINT_ACCOUNT_OWNER_ID = o.id
left join wx_t_point_value_detail pvds on out_pc.ID = pvds.POINT_ACCOUNT_ID
WHERE
	1 = 1
  <!--   AND	crsc.sales_channel_name ='Indirect' -->
	<!-- 
	AND(
		e.partner_property = 'Delo Distributor'
		OR(
			e.partner_property = 'NORMAL'
			AND o.create_time &gt; '2018-01-01'
		)		
	)
	 -->
	<!-- <if test="salesChannelName == 'Consumer'">
	and dwrsr.channel_weight &amp;1>0
	</if>
	<if test="salesChannelName == 'Commercial'">
	and dwrsr.channel_weight &amp;2>0
	</if>
	<if test="salesChannelName == 'OEM'">
	and dwrsr.channel_weight = 4
	</if>-->
	<if test="channelWeight > 0">
		<!-- and (dwrsr.channel_weight &amp; #{channelWeight} >0 -->
		AND (
		EXISTS (SELECT 1 FROM view_customer_region_sales_channel dwrsr WHERE dwrsr.distributor_id = e.distributor_id AND dwrsr.channel_weight &amp; #{channelWeight} >0 )
		OR EXISTS (
		SELECT
			1 
		FROM
			wx_t_user u1
			LEFT JOIN wx_t_userrole ur1 ON ur1.user_id = u1.user_id
			LEFT JOIN wx_t_role r1 ON r1.role_id= ur1.role_id
			LEFT JOIN wx_t_value_transform_map vtm1 ON vtm1.transform_type= 'ChannelWieghtMapping' 
			AND vtm1.value_before_transform= r1.sales_channel
		WHERE
			( u1.type IS NULL OR u1.type != '1' ) 
			AND u1.org_id= o.id 
			AND u1.status= 1 
			AND vtm1.value_after_transform &amp; #{channelWeight} > 0 
			)
			)
	</if>
	<if test="key != null">
		and (o.organization_name like '%'+#{key}+'%'
		or e.sap_code like '%'+#{key}+'%')
	</if>
	<if test="partnerId != null">
		and o.id=#{partnerId}
	</if>
	$Permission_Clause$
	group by
	o.id,
	e.partner_property,
	e.sap_code,
	o.organization_name,
	crsc.region_name,
	out_pc.frozen_type,
	pvds.POINT_TYPE
	)d
  </select>
  <select id="queryDealerByPromotion" resultMap="BaseResultMap" parameterType="com.chevron.material.model.MaterialDealerParams">
 SELECT distinct d.id,d.organization_name,d.region_name,d.sap_code,d.address_count,d.caltex_point,d.cdm_material_point,d.cdm_promotion_point,d.cdm_red_bag_point,d.cdm_stock_point,d.cdm_store_open_point,d.frozen_type,
		d.oem_stock_point,d.promotion_point,d.cai_gift_promotion,d.cdm_gift_promotion,d.cdm_pit_pack_point from (
	SELECT
	o.organization_name,crsc.region_name,
	o.id,
	e.sap_code,
	(
		SELECT
			count(*)
		FROM
			wx_t_material_address_config mac
		WHERE
			mac.PARTNER_ID = o.id
	) AS address_count,
	isnull((
				SELECT
					SUM(gp.available_quantity)
				FROM
					wx_t_promotion_gift_pool gp
				WHERE
					gp.application_type = 'promotion'
				AND partner_id = o.id),0
				) as cai_gift_promotion,
			isnull((
				SELECT
					SUM(gp.available_quantity)
				FROM
					wx_t_promotion_gift_pool gp
				WHERE
					gp.application_type = 'cdm_promotion'
				AND partner_id = o.id),0
				) as cdm_gift_promotion,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				1 = 1
				AND pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'PROMOTION_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as promotion_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				1 = 1
				AND pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CDM_PIT_PACK'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as cdm_pit_pack_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CALTEX_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as caltex_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				1 = 1
				AND pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CDM_STOCK_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as cdm_stock_point,
	isnull(
		(
			SELECT
				sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
			FROM
				wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON
				pc.ID = pvd.POINT_ACCOUNT_ID
			WHERE
				pvd.DELETE_FLAG = 0
				AND pvd.POINT_TYPE = 'CDM_MATERIAL_POINT'
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id
		),
		0
	) as cdm_material_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'CDM_PROMOTION_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as cdm_promotion_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'CDM_RED_BAG_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as cdm_red_bag_point,
	  	isnull(
			(
		SELECT SUM
			( pvd.POINT_VALUE - pvd.POINT_PAYED ) 
		FROM
			wx_t_point_value_detail pvd
			LEFT JOIN wx_t_point_account pc ON pc.ID = pvd.POINT_ACCOUNT_ID 
		WHERE
			pvd.DELETE_FLAG = 0 
			AND pvd.POINT_TYPE = 'CDM_STORE_OPEN_POINT' 
			AND pc.POINT_ACCOUNT_OWNER_ID = o.id 
			),
			0 
			) AS cdm_newstore_open_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'OEM_STOCK_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as oem_stock_point,
	  isnull(
	  (
	  SELECT
	  sum( pvd.POINT_VALUE - pvd.POINT_PAYED )
	  FROM
	  wx_t_point_value_detail pvd
	  LEFT JOIN wx_t_point_account pc ON
	  pc.ID = pvd.POINT_ACCOUNT_ID
	  WHERE
	  pvd.DELETE_FLAG = 0
	  AND pvd.POINT_TYPE = 'CDM_STORE_OPEN_POINT'
	  AND pc.POINT_ACCOUNT_OWNER_ID = o.id
	  ),
	  0
	  ) as cdm_store_open_point,
	  
	isnull(out_pc.frozen_type, '') as frozen_type,
	pvds.POINT_TYPE
FROM
	wx_t_organization o
LEFT JOIN wx_t_partner_o2o_enterprise e ON
	o.id = e.partner_id
LEFT JOIN view_customer_region_sales_channel  crsc ON e.distributor_id = crsc.distributor_id
<!-- LEFT JOIN <include refid="query_sales_channel_relation"/> dwrsr ON dwrsr.distributor_id = e.distributor_id -->
LEFT JOIN wx_t_point_account out_pc ON
	  out_pc.POINT_ACCOUNT_OWNER_ID = o.id
left join wx_t_point_value_detail pvds on out_pc.ID = pvds.POINT_ACCOUNT_ID
WHERE
	1 = 1
  	AND	crsc.sales_channel_name ='Indirect' AND o.status = 1
	AND (EXISTS (SELECT 1 FROM wx_t_promotion_gift_pool gp where gp.application_type in('promotion','cdm_promotion') AND gp.partner_id = o.id)
				OR EXISTS (SELECT 1 FROM wx_t_point_value_detail pvd
				LEFT JOIN wx_t_point_account pc ON pc.ID = pvd.POINT_ACCOUNT_ID
				where pvd.POINT_TYPE IN ('CDM_PROMOTION_POINT','CDM_MATERIAL_POINT','CDM_PROMOTION_POINT','CDM_RED_BAG_POINT','CDM_PIT_PACK')
				AND pc.POINT_ACCOUNT_OWNER_ID = o.id))
	<if test="channelWeight > 0">
		AND (
		EXISTS (SELECT 1 FROM view_customer_region_sales_channel dwrsr WHERE dwrsr.distributor_id = e.distributor_id AND dwrsr.channel_weight &amp; #{channelWeight} >0 )
		OR EXISTS (
		SELECT
			1 
		FROM
			wx_t_user u1
			LEFT JOIN wx_t_userrole ur1 ON ur1.user_id = u1.user_id
			LEFT JOIN wx_t_role r1 ON r1.role_id= ur1.role_id
			LEFT JOIN wx_t_value_transform_map vtm1 ON vtm1.transform_type= 'ChannelWieghtMapping' 
			AND vtm1.value_before_transform= r1.sales_channel
		WHERE
			( u1.type IS NULL OR u1.type != '1' ) 
			AND u1.org_id= o.id 
			AND u1.status= 1 
			AND vtm1.value_after_transform &amp; #{channelWeight} > 0 
			)
			)
	</if>
	<if test="key != null">
		and (o.organization_name like '%'+#{key}+'%'
		or e.sap_code like '%'+#{key}+'%')
	</if>
	<if test="partnerId != null">
		and o.id=#{partnerId}
	</if>
	$Permission_Clause$
	group by
	o.id,
	e.partner_property,
	e.sap_code,
	o.organization_name,
	crsc.region_name,
	out_pc.frozen_type,
	pvds.POINT_TYPE
	)d
  </select>
  
  
  
</mapper>
