insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 128, 1, '驻地培训>课程管理：128-新增课程', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 256, 1, '驻地培训>课程管理：256-导入课程 ', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 512, 1, '驻地培训>课程管理：512-上传培训材料', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 1024, 1, '驻地培训>课程管理：1024-更改课程状态(上架/下架)', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 2048, 1, '驻地培训>课程管理：2048-修改课程', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 4096, 1, '驻地培训>课程管理：4096-删除课程', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 8192, 1, '驻地培训>驻地培训工作台：8192-新增培训安排', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 16384, 1, '驻地培训>驻地培训工作台：16384-导入培训安排 ', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 32768, 1, '驻地培训>驻地培训工作台：32768-删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'Training.page.plan', 65536, 1, '驻地培训>驻地培训工作台：65536-编辑培训安排', 1, getdate());
