package com.chevron.kpi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.chevron.dsrkpi.business.DsrKpiBizService;
import com.chevron.dsrkpi.business.impl.DsrKpiBizServiceImpl;
import com.chevron.dsrkpi.dao.CiDsrKpiHistoryMapper;
import com.chevron.dsrkpi.dao.DsrKpiMapper;
import com.chevron.dsrkpi.model.DsrKpi;
import com.chevron.dsrkpi.service.impl.DsrKpiServiceImpl;
import com.chevron.kpi.component.KPIComponentFacade;
import com.chevron.kpi.dao.KPIConfigItemMapper;
import com.chevron.kpi.dao.KPIConfigMapper;
import com.chevron.kpi.dao.KPIConfigObjectMapper;
import com.chevron.kpi.dao.KPIInfoMapper;
import com.chevron.kpi.dao.KPILogMapper;
import com.chevron.kpi.dao.KPITypeMapper;
import com.chevron.kpi.model.ExportBonus;
import com.chevron.kpi.model.KPIConfig;
import com.chevron.kpi.model.KPIConfigExample;
import com.chevron.kpi.model.KPIConfigItem;
import com.chevron.kpi.model.KPIConfigItemExample;
import com.chevron.kpi.model.KPIConfigObject;
import com.chevron.kpi.model.KPIConfigObjectExample;
import com.chevron.kpi.model.KPIInfoListVo;
import com.chevron.kpi.model.KPIInfoVO;
import com.chevron.kpi.model.KPILog;
import com.chevron.kpi.model.KPIType;
import com.chevron.kpi.model.KPITypeExample;
import com.chevron.kpi.model.KPITypeExample.Criteria;
import com.chevron.kpi.model.KpiCalInfo;
import com.chevron.kpi.service.KPIService;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.model.PartnerCtrlParam;
import com.chevron.pms.service.WorkshopEmployeeService;
import com.chevron.pms.service.WorkshopPartnerService;
import com.chevron.report.business.BiProcedureBizService;
import com.chevron.report.model.DistributorPerformanceField;
import com.chevron.report.model.DistributorPerformanceParam;
import com.chevron.report.model.DistributorPerformanceVo;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.utils.business.ReportViewBizService;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
public class KPIServiceImpl implements KPIService {
	public static final Logger log = LoggerFactory.getLogger(KPIServiceImpl.class);

	@Resource
	OrganizationVoMapper organizationVoMapper;
	@Resource
	KPIConfigMapper kpiConfigMapper;
	@Resource
	KPIConfigObjectMapper kpiConfigObjectMapper;
	@Resource
	KPIConfigItemMapper kpiConfigItemMapper;
	@Resource
	KPITypeMapper kpiTypeMapper;
	@Resource
	WorkshopPartnerService workshopPartnerService;
	@Resource
	WorkshopEmployeeService workshopEmployeeService;
	@Resource
	KPILogMapper kpiLogMapper;
	@Resource
	WorkshopMasterMapper workshopMasterMapper;
	@Resource
	DicService dicService;
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	@Resource
	KPIInfoMapper kPIInfoMapper;

	@Autowired
	private ReportViewBizService reportViewBizService;

	@Autowired
	private WorkshopEmployeeBizService workshopEmpBizService;

	@Autowired
	private DsrKpiServiceImpl dsrKpiService;

    @Autowired
    private DsrKpiBizService dsrKpiBizService;

    @Autowired
    private CiDsrKpiHistoryMapper ciDsrKpiHistoryMapper;

    @Autowired
    private DsrKpiMapper dsrKpiMapper;
    
    @Autowired
    private BiProcedureBizService biProcedureBizService;

	/**
	 * 获取所有雪佛龙销售BD用户
	 */
	@Override
	public Map<String, Object> getChevronBD4Ctrl() throws Exception {

		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", kpiConfigObjectMapper.selectChevronBD());
			map.put("success", true);
		} catch (Exception e) {
			log.error("Exception: ", e);
			map.put("success", false);
		}
		return map;

	}

	/**
	 * 获取合伙人列表(合伙人快速选择控件)
	 */
	@Override
	public Map<String, Object> getPartners4Ctrl(PartnerCtrlParam param) throws Exception {

		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", organizationVoMapper.selPartners(param));
			map.put("success", true);
		} catch (Exception e) {
			log.error("Exception: ", e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> getWorkshops4Ctrl(Long partnerId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("partnerId", partnerId);
			params.put("status", WorkshopMaster.WORKSHOP_STATUS3);
			params.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
			map.put("data", workshopMasterMapper.querySimpleByParams(params));
			map.put("success", true);
		} catch (Exception e) {
			log.error("Exception: ", e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> getSalesmen4Ctrl(Long partnerId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", kpiConfigObjectMapper.selectUserByPartnerId(partnerId));
			map.put("success", true);
		} catch (Exception e) {
			log.error("Exception: ", e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> getTechnicians4Ctrl(Long workshopId) throws Exception {

		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", workshopEmpBizService.queryWorkshopEmpList(null, workshopId, null, DateUtil.getMinimumDate() , DateUtil.getMaximumDate()));
			map.put("success", true);
		} catch (Exception e) {
			log.error("Exception: ", e);
			map.put("success", false);
		}
		return map;

	}

	@Override
	public Map<String, Object> getKPITypes() throws Exception {

		Map<String, Object> map = new HashMap<String, Object>(2);
		boolean isSuccess = true;
		Map<String, Object> data = new HashMap<String, Object>(3);
		KPITypeExample example = new KPITypeExample();
		example.createCriteria().andDeleteFlagEqualTo(false);
		List<KPIType> allKPIList = null;
		try {
			allKPIList = kpiTypeMapper.selectByExample(example);
		} catch (Exception e) {
			log.error("Exception: ", e);
			isSuccess = false;
			map.put("errorMsg", "获取指标类型列表失败, 系统异常");
			map.put("exception", e.getMessage());
		}

		List<KPIType> chevronKPIList = new ArrayList<KPIType>();
		List<KPIType> partnerKPIList = new ArrayList<KPIType>();
		List<KPIType> workshopKPIList = new ArrayList<KPIType>();
		if (null != allKPIList && !allKPIList.isEmpty()) {
			for(KPIType type : allKPIList) {
				if (null != type && type.getIsEnableForCs() ) {
					chevronKPIList.add(type);
				}
				if (null != type && type.getIsEnableForSp() ) {
					partnerKPIList.add(type);
				}
				if (null != type && type.getIsEnableForWs() ) {
					workshopKPIList.add(type);
				}
			}
		}
		data.put("all", allKPIList);
		data.put("chevron", chevronKPIList);
		data.put("partner", partnerKPIList);
		data.put("workshop", workshopKPIList);
		map.put("data", data);
		map.put("success", isSuccess);
		return map;
	}

	@Override
	public Map<String, Object> getKPITypes4ObjectType(String objectType) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		boolean isSuccess = true;
		KPITypeExample example = new KPITypeExample();
		Criteria criteria = example.createCriteria();
		criteria.andDeleteFlagEqualTo(false);
		if ("SC".equals(objectType)) {
			criteria.andIsEnableForCsEqualTo(true);
		}
		if ("SP".equals(objectType) || "BD".equals(objectType)) {
			criteria.andIsEnableForSpEqualTo(true);
		}
		if ("WS".equals(objectType)) {
			criteria.andIsEnableForWsEqualTo(true);
		}
		List<KPIType> kpiList = null;
		try {
			kpiList = kpiTypeMapper.selectByExample(example);
		} catch (Exception e) {
			log.error("Exception: ", e);
			isSuccess = false;
			map.put("errorMsg", "获取指标类型列表失败, 系统异常");
			map.put("exception", e.getMessage());
		}

		map.put("data", kpiList);
		map.put("success", isSuccess);
		return map;
	}

	@Override
	public Map<String, Object> getKPITypes4ObjectTypeAndId(String objectType, Long objectId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		boolean isSuccess = true;
		List<KPIType> kpiList = null;

		if (null == objectId) {
			return getKPITypes4ObjectType(objectType);
		}
		try {
			kpiList = kpiTypeMapper.selectByObjectTypeAndId(objectId, objectType);
		} catch (Exception e) {
			log.error("Exception: ", e);
			isSuccess = false;
			map.put("errorMsg", "获取指标类型列表失败, 系统异常");
			map.put("exception", e.getMessage());
		}
		map.put("data", kpiList);
		map.put("success", isSuccess);
		return map;
	}

	@Override
	@Transactional
	public Map<String, Object> syncKPIConfig(boolean isNew, KPIConfig config, List<KPIConfigObject> configObjects,
			List<KPIConfigItem> configItems) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		WxTUser curUser = ContextUtil.getCurUser();
		Date now = DateUtil.getCurrentDate();

		boolean isSuccess = true;
		try {
			if (isNew) {
				config.setId(null);
				config.setConfigType("CM");
				config.setConfigStatus("");
				config.setDeleteFlag(false);
				config.setLastUpdatedBy(curUser.getUserId());
				config.setLastUpdateTime(now);
				config.setCreatedBy(curUser.getUserId());
				config.setCreationTime(now);
				kpiConfigMapper.insertSelective(config);
			} else {
				config.setConfigStatus("");
				config.setLastUpdatedBy(curUser.getUserId());
				config.setLastUpdateTime(now);
				int updateRes = kpiConfigMapper.updateByPrimaryKeySelective(config);

				if (updateRes == 1) {
					KPIConfigObjectExample kpiConfigObjectExample = new KPIConfigObjectExample();
					kpiConfigObjectExample.createCriteria().andConfigIdEqualTo(config.getId());
					kpiConfigObjectMapper.deleteByExample(kpiConfigObjectExample);

					KPIConfigItemExample kpiConfigItemExample = new KPIConfigItemExample();
					kpiConfigItemExample.createCriteria().andConfigIdEqualTo(config.getId());
					kpiConfigItemMapper.deleteByExample(kpiConfigItemExample);
				}
			}

			for (KPIConfigObject o : configObjects) {
				o.setConfigId(config.getId());
				o.setDeleteFlag(false);
				o.setLastUpdatedBy(curUser.getUserId());
				o.setLastUpdateTime(now);
				o.setCreatedBy(curUser.getUserId());
				o.setCreationTime(now);
			}
			kpiConfigObjectMapper.insertBatch(configObjects);
			for (KPIConfigItem o : configItems) {
				o.setConfigId(config.getId());
				o.setDeleteFlag(false);
				o.setLastUpdatedBy(curUser.getUserId());
				o.setLastUpdateTime(now);
				o.setCreatedBy(curUser.getUserId());
				o.setCreationTime(now);
			}
			kpiConfigItemMapper.insertBatch(configItems);

		} catch (Exception e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				log.error("KPIService.syncKPIConfig() exception", e);
				map.put("success", false);
				map.put("errorMsg", "绩效考核配置创建失败");
				map.put("error", "程序异常，回滚变更:" + e.getMessage());
				return map;
		}

		if (isNew) {
			map.put("msg", "绩效考核配置创建成功");
		} else {
			map.put("msg", "绩效考核配置更新成功");
		}
		if (null != config.getIsEnable() && config.getIsEnable()) {
			refreshKPILogData(config.getId());
		}

		map.put("data", null);
		map.put("success", isSuccess);
		return map;
	}

	@Override
	public Map<String, Object> getConfigInfo(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		boolean isSuccess = true;
		if (null == configId) {
			isSuccess = false;
			map.put("success", false);
			map.put("errorMsg", "绩效考核配置获取失败, 传入ID为空");
			return map;
		}
		KPIConfig config = kpiConfigMapper.selectByPrimaryKey(configId);
		map.put("data", config);
		map.put("msg", "绩效考核配置获取成功");
		map.put("success", isSuccess);
		return map;
	}

	@Override
	public Map<String, Object> getConfigItems(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		boolean isSuccess = true;
		if (null == configId) {
			isSuccess = false;
			map.put("success", false);
			map.put("errorMsg", "绩效考核配置指标列表获取失败, 传入ID为空");
			return map;
		}
		KPIConfigItemExample example = new KPIConfigItemExample();
		example.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigItem> data = kpiConfigItemMapper.selectByExample(example);
		map.put("data", data);
		map.put("msg", "绩效考核配置指标列表获取成功");
		map.put("success", isSuccess);
		return map;
	}

	@Override
	public Map<String, Object> getConfigObjects(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		boolean isSuccess = true;
		if (null == configId) {
			isSuccess = false;
			map.put("success", false);
			map.put("errorMsg", "绩效考核配置对象列表获取失败, 传入ID为空");
			return map;
		}
		KPIConfigObjectExample example = new KPIConfigObjectExample();
		example.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigObject> data = kpiConfigObjectMapper.selectByExample(example);
		map.put("data", data);
		map.put("msg", "绩效考核配置对象列表获取成功");
		map.put("success", isSuccess);
		return map;
	}

	@Override
	public Map<String, Object> enableConfig(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		if (null == configId) {
			map.put("success", false);
			map.put("errorMsg", "激活绩效考核失败, 传入ID为空");
			return map;
		}
		KPIConfig record  = new KPIConfig();
		record.setId(configId);
		record.setIsEnable(true);
		int res = kpiConfigMapper.updateByPrimaryKeySelective(record);

		refreshKPILogData(configId);
		map.put("msg", "激活绩效考核成功");
		map.put("success", res == 1);
		return map;
	}

	@Override
	public Map<String, Object> disableConfig(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		if (null == configId) {
			map.put("success", false);
			map.put("errorMsg", "中止绩效考核失败, 传入ID为空");
			return map;
		}
		KPIConfig record  = new KPIConfig();
		record.setId(configId);
		record.setIsEnable(false);
		int res = kpiConfigMapper.updateByPrimaryKeySelective(record);
		map.put("msg", "中止绩效考核成功");
		map.put("success", res == 1);
		return map;
	}


	@Override
	public Map<String, Object> deleteConfig(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		if (null == configId) {
			map.put("success", false);
			map.put("errorMsg", "删除绩效考核失败, 传入ID为空");
			return map;
		}
		KPIConfig record  = new KPIConfig();
		record.setId(configId);
		record.setDeleteFlag(true);
		int res = kpiConfigMapper.updateByPrimaryKeySelective(record);
		map.put("msg", "删除绩效考核成功");
		map.put("success", res == 1);
		return map;
	}

	@Override
	public Map<String, Object> getConfigLog(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		Map<String, Object> data = new HashMap<String, Object>(2);
		if (null == configId) {
			map.put("success", false);
			map.put("errorMsg", "获取数据失败, 传入ID为空");
			return map;
		}
		KPIConfig config = kpiConfigMapper.selectByPrimaryKey(configId);

		KPIConfigItemExample example = new KPIConfigItemExample();
		example.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigItem> items = kpiConfigItemMapper.selectByExample(example);

		KPIConfigObjectExample example2 = new KPIConfigObjectExample();
		example2.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigObject> objects = kpiConfigObjectMapper.selectByExample(example2);


		List<KPILog> log = kpiLogMapper.selectByConfigId(configId);

		data.put("data", log);
		data.put("config", config);
		data.put("configItems", items);
		data.put("configObjects", objects);
		map.put("data", data);

		map.put("msg", "获取绩效数据成功");
		map.put("success", true);
		return map;
	}

	@Override
	public Map<String, Object> refreshLog(Long configId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(2);
		if (null == configId) {
			map.put("success", false);
			map.put("errorMsg", "刷新失败, 传入ID为空");
			return map;
		}
		refreshKPILogData(configId);
		map.put("msg", "刷新成功");
		map.put("success", true);
		return map;
	}

	private  void refreshKPILogData(Long configId) {
		KPIConfig config = kpiConfigMapper.selectByPrimaryKey(configId);

		KPIConfigItemExample example = new KPIConfigItemExample();
		example.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigItem> configItems = kpiConfigItemMapper.selectByExample(example);

		KPIConfigObjectExample example2 = new KPIConfigObjectExample();
		example2.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigObject> configObjects = kpiConfigObjectMapper.selectByExample(example2);
		new KPIComponentFacade().refreshAllRecord(config, configItems, configObjects);
	}
	@Override
	public Map<String, Object> getKPIData(String objectTypeCode,Long objectId, Long kpiTypeId, String periodTypeCode,
			@RequestParam(value = "index", required = false)Long index) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(3);
		if (null == objectTypeCode || objectId == null ) {
			map.put("success", false);
			map.put("code", "error");
			map.put("codeMsg", "获取数据失败, 传入参数为空");
			map.put("errorMsg", "获取数据失败, 传入参数为空");
			return map;
		}

		// index 值为空或者负值, 视为获取所有数据
		if(index == null || index < 0) {
			index =null;
		}

		// kpiTypeId 值为空或者负值, 视为获取所有数据
		if(kpiTypeId == null || kpiTypeId < 0) {
			kpiTypeId = null;
		}

		if(index !=null && index > 0 && StringUtils.isEmpty(periodTypeCode)) {
			map.put("success", false);
			map.put("code", "error");
			map.put("codeMsg", "获取具体KPI值的场合, 周期类型必须指定具体值");
			map.put("errorMsg", "获取具体KPI值的场合, 周期类型必须指定具体值");
			return map;
		}


		List<KPILog> log = null;
		if (null != index && 999L == index) {
			log = kpiLogMapper.getLatestKPIData(objectTypeCode, objectId, kpiTypeId, null, periodTypeCode, index);
		} else {
			log = kpiLogMapper.getKPIData(objectTypeCode, objectId, kpiTypeId, null, periodTypeCode, index);
		}
		map.put("data", log);
		map.put("code", "success");
		map.put("codeMsg", "获取KPI数据成功");
		map.put("msg", "获取KPI数据成功");
		map.put("success", true);
		return map;
	}

	@Override
	public Map<String, Object> getDicItemByDicTypeCode(String dicTypeCode) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		boolean isSPuser = WxTUser.USER_MODEL_SP.equals(ContextUtil.getCurUser().getUserModel());
		boolean isChevronUser = WxTUser.USER_MODEL_CHEVRON.equals(ContextUtil.getCurUser().getUserModel());
		boolean isWSUser = WxTUser.USER_MODEL_WS.equals(ContextUtil.getCurUser().getUserModel());

		resultMap = dicService.getDicItemByDicTypeCode(dicTypeCode);
		if ("kpi.objectType".equals(dicTypeCode)) {
			List<DicItemVo> data = null;
			List<DicItemVo> newData = new ArrayList<DicItemVo>();
			if (null != resultMap && !resultMap.isEmpty() && resultMap.containsKey("data") && null != resultMap.get("data")) {
				data = (List<DicItemVo>) resultMap.get("data");
				if (null != data && !data.isEmpty()) {
					for (DicItemVo vo : data ) {
						if (isSPuser){
							if (curUser.isService_Partner_BD()) {
								if ("BD".equals(vo.getDicItemCode()) || "WS".equals(vo.getDicItemCode())) {
									newData.add(vo);
								}
							} else {
								if ("SP".equals(vo.getDicItemCode())  || "BD".equals(vo.getDicItemCode()) || "WS".equals(vo.getDicItemCode())) {
									newData.add(vo);
								}
							}
						}

						if (isChevronUser){
							newData.add(vo);
						}

						if (isWSUser) {
							if ("WS".equals(vo.getDicItemCode())) {
								newData.add(vo);
							}
						}
					}
				}
			}
			resultMap.put("data", newData);
		}

		return resultMap;
	}

	@Override
	public void collectionDataTask() {
		// 1. 查询所有已激活KPI配置
		KPIConfigExample example = new KPIConfigExample();
		example.createCriteria().andIsEnableEqualTo(true);
		List<KPIConfig> configList = kpiConfigMapper.selectByExample(example);

		// 2. 逐个调用获取前一天数据
		if (null != configList && !configList.isEmpty()) {
			for (KPIConfig config : configList) {
				refreshLatestKPILogData(config.getId());
			}
		}
	}

	private  void refreshLatestKPILogData(Long configId) {
		KPIConfig config = kpiConfigMapper.selectByPrimaryKey(configId);
		KPIConfigItemExample example = new KPIConfigItemExample();
		example.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigItem> configItems = kpiConfigItemMapper.selectByExample(example);

		KPIConfigObjectExample example2 = new KPIConfigObjectExample();
		example2.createCriteria().andConfigIdEqualTo(configId);
		List<KPIConfigObject> configObjects = kpiConfigObjectMapper.selectByExample(example2);
		new KPIComponentFacade().refreshLatestRecord(config, configItems, configObjects);
	}

	@Override
	public JsonResponse getKpiByDistributor(Long executor, String dateFrom, String dateTo) {
		JsonResponse map = new JsonResponse();
		log.info("getKpiByDistributor: " + executor + "," + dateFrom + "," + dateTo);
		WxTUser user = ContextUtil.getCurUser();
		try {
			Map<String, Object> params = new HashMap<String, Object>(5);
			params.put("executor", executor);
			params.put("loginOrgId", user.getOrgId());
			params.put("dateFrom", dateFrom);
			params.put("dateTo", dateTo);
			List<Map<String, String>> dataList = reportViewBizService.queryForData("app", "DistributorKpi", params, "0");
			map.setDataResult(dataList.get(0));
			log.info("getKpiByDistributor success.");
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, user.getUserId(),
					"com.chevron.kpi.service.impl.KPIServiceImpl.getKpiByDistributor", executor + "," + dateFrom + "," + dateTo);
		}
		return map;
	}

	@Override
	public int checkOrgIsRow(String orgCai, Date date) {
		return kPIInfoMapper.checkOrgIsRow(orgCai);
	}

	@SuppressWarnings("deprecation")
	@Override
	public JsonResponse queryKPIInfo(String customerNameCn,String orgCai, String date) {
		WxTUser curUser = ContextUtil.getCurUser();
		JsonResponse resMap = new JsonResponse();
		Long userId = curUser.getUserId();
		//默认销售权限查看
		int permissionWeight = 16;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		//判断是否有权限
		try {
			if(null == orgCai) {
				orgCai = curUser.getCai();
				//获取当前用户的查看权限
				permissionWeight = operationPermissionBizService.getPermissionWeight(userId, "CAI_KPI_INFO");
				if(1 == (permissionWeight&1)) {
					permissionWeight = 1;
                    orgCai = null;
				}else if(4 == (permissionWeight&4)) {
					permissionWeight = 4;
				}else if(8 == (permissionWeight&8)) {
					permissionWeight = 8;
				}else if(64 == (permissionWeight&64)) {
					permissionWeight = 64;
				}else if(16 == (permissionWeight&16)) {
					 String userModel = curUser.getUserModel();
					if(WxTUser.USER_MODEL_SP.equals(userModel)) {
						permissionWeight = 32; //销售老板赋有fdsr的权限，且特殊处理
						paramMap.put("partnerId", curUser.getOrgId());
                        orgCai = null;
					}else {
						permissionWeight = 16;
					}
				}else {
					resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
					resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
					return resMap;
				}
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
		log.info("orgType =========================================="+ permissionWeight);
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-01-01 00:00:00.000");
		Date startDate = DateUtil.parseDate(date,"yyyy-MM-dd");
		Date endDate = DateUtil.addMonths(startDate, 1);
        paramMap.put("orgCai", orgCai);
		paramMap.put("orgType", permissionWeight);
		paramMap.put("startDate", dateFormat.format(startDate));
		paramMap.put("endDate", dateFormat.format(endDate));
		paramMap.put("startYTDDate", dateFormat1.format(startDate));
		paramMap.put("customerNameCn", customerNameCn);
		//判断查询时间是否存在历史数据
        paramMap.put("month", DateUtil.getMonth(startDate));
        paramMap.put("year", DateUtil.getYear(startDate));

        //默认不显示审核页面确认按钮   ---0不显示，1显示
        resMap.setDataResult((permissionWeight==32 || permissionWeight==64)?16:permissionWeight);
        resMap.put("isShow", 0);
        if(permissionWeight == 1 && customerNameCn == null) {
            //检查是否已经发放积分
            int count = dsrKpiMapper.checkWhetherPoints(date);
            if(count == 0){
                resMap.put("isShow", 1);
            }
        }
        //先查询历史表中有无数据
        List<KpiCalInfo> kpiCalInfos = ciDsrKpiHistoryMapper.selectCiKpiHistory(paramMap);
        if(CollectionUtil.isNotEmpty(kpiCalInfos)){
            log.debug("历史表中有数据，直接查询历史表...");
            //拼装数据，按照region汇总数据
            List<KPIInfoVO> regionDatas = this.buildCiKpiHistoryDatas(kpiCalInfos);
            KPIInfoVO kpiInfoVO = this.buildOneKPIInfoVO(regionDatas);
            resMap.put("countKPIInfo", kpiInfoVO);
            resMap.setListResult(regionDatas);
            return resMap;
        }
        List<KPIInfoVO> queryKPIInfo = kPIInfoMapper.queryKPIInfo(paramMap);
		if(CollectionUtil.isEmpty(queryKPIInfo)){
            resMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
            resMap.put(JsonResponse.KEY_ERROR_MSG,"查询无数据。");
            return resMap;
        }
        KPIInfoVO kpiInfoVO = this.buildCountVo(queryKPIInfo);
        resMap.put("countKPIInfo", kpiInfoVO);
		resMap.setListResult(queryKPIInfo);
		return resMap;
	}

	@Override
	public JsonResponse queryKPIInfoList(KPIInfoListVo vo,String orgCai, String date) {
		JsonResponse resMap = new JsonResponse();
		WxTUser curUser = ContextUtil.getCurUser();
		orgCai = curUser.getCai();
		Long userId = curUser.getUserId();
		String roleType; //ytd销量类型
		String cai;
		int permissionWeight;
		Map<String, Object> paramMap = new HashMap<String, Object>();
		//判断是否有权限
		try {
			//获取当前用户的查看权限
			permissionWeight = operationPermissionBizService.getPermissionWeight(userId, "CAI_KPI_INFO");
			if(1 == (permissionWeight&1)) {
				permissionWeight = 1;
				roleType = "admin";
				cai = null;
			}else if(4 == (permissionWeight&4)) {
				permissionWeight = 4;
				roleType = "channel manager";
				cai = orgCai;
			}else if(8 == (permissionWeight&8)) {
				permissionWeight = 8;
				roleType = "asm";
				cai = orgCai;
			}else if(64 == (permissionWeight&64)) {
				permissionWeight = 64;
				roleType = "teamleader";
				cai = orgCai;
			}else if(16 == (permissionWeight&16)) {
				String userModel = curUser.getUserModel();
				if(WxTUser.USER_MODEL_SP.equals(userModel)) {
					permissionWeight = 32; //销售老板赋有fdsr的权限，且特殊处理
					paramMap.put("partnerId", curUser.getOrgId());
					roleType = "admin";
					cai = null;
				}else {
					permissionWeight = 16;
					roleType = "flsr";
					cai = orgCai;
				}
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
        Date startDate = DateUtil.parseDate(date, "yyyy-MM-dd");
        Date endDate = DateUtil.addMonths(startDate, 1);
        paramMap.put("orgCai", cai);
        paramMap.put("orgType", permissionWeight);
        paramMap.put("startDate", dateFormat.format(startDate));
        paramMap.put("endDate", dateFormat.format(endDate));
        paramMap.put("yTDStartDate", new SimpleDateFormat("yyyy-01-01 00:00:00.000").format(startDate));
        paramMap.put("vo", vo);
        //判断查询时间是否存在历史数据
        paramMap.put("month", DateUtil.getMonth(startDate));
        paramMap.put("year", DateUtil.getYear(startDate));
        //先查询历史表中有无数据
        int checkWhetherPoints = dsrKpiMapper.checkWhetherPoints(dateFormat.format(startDate));
        if (checkWhetherPoints > 0) {
            log.debug("历史表中有数据，直接查询历史表...");
            //手动分页，查询页数，根据partnerID排序
            Long count = ciDsrKpiHistoryMapper.kpiByPartnerPageCount(paramMap);
            if (count == 0) {
                return resMap;
            }
            int pageSize = vo.getPageSize();
            int page = this.calPage(vo.getPage(),vo.getPageSize(), count);
            paramMap.put("pageSize", pageSize);
            paramMap.put("endRowNum", (page - 1) * pageSize);
            List<KpiCalInfo> kpiCalInfos = ciDsrKpiHistoryMapper.kpiByPartnerPage(paramMap);
            List<KPIInfoListVo> kpiInfoListVos = this.buildHistoryKPIInfoListVos(kpiCalInfos);
            //获取ytd销量
            this.setYtdSellIn(kpiInfoListVos, startDate, roleType, cai);
            resMap.setListResult(kpiInfoListVos);
            resMap.setTotalOfPaging(count);
            return resMap;
        }
        Long countNum = kPIInfoMapper.countKPIInfoNum(paramMap);
        int pageSize = vo.getPageSize();
        int page = this.calPage(vo.getPage(),vo.getPageSize(), countNum);
        paramMap.put("pageSize", pageSize);
        paramMap.put("endRowNum", (page - 1) * pageSize);
        List<KPIInfoListVo> queryKPIInfoList = kPIInfoMapper.queryKPIInfoList(paramMap);
        for (KPIInfoListVo item : queryKPIInfoList) {
            item.setYtdNum(NumberUtil.add(item.getYtdShopNum(), item.getYtdFleetNum()).longValue());
            item.setNewCustomer(NumberUtil.add(item.getNewFleetNum(), item.getNewWkShopNum()).longValue());
        }
        //获取ytd销量
        this.setYtdSellIn(queryKPIInfoList, startDate, roleType, cai);
        resMap.setListResult(queryKPIInfoList);
        resMap.setTotalOfPaging(countNum);
        return resMap;
    }

    private void setYtdSellIn(List<KPIInfoListVo> resultList, Date startDate, String roleType, String cai) {
        DistributorPerformanceParam param = new DistributorPerformanceParam();
        param.setYear(Integer.parseInt(new SimpleDateFormat("yyyy").format(startDate)));
        param.setCustomerCategory("commercial");
        param.setProductChannel("commercial");
        List<DistributorPerformanceVo> list = biProcedureBizService.queryDistributorPerformance(param,
                DistributorPerformanceField.DISTRIBUTOR_ID,
                DistributorPerformanceField.PRODUCT_CHANNEL,
                DistributorPerformanceField.ACT_LITERS,
                DistributorPerformanceField.ACT_MARGIN_RMB,
                DistributorPerformanceField.ACT_MARGIN_USD,
                DistributorPerformanceField.BASELINE_TARGET_LITERS,
				DistributorPerformanceField.ELITE_ACTUAL_LITERS);
        for (KPIInfoListVo kpiInfoListVo : resultList) {
            for (DistributorPerformanceVo vo : list) {
                if (kpiInfoListVo.getDistributorId().equals(vo.getDistributorId())) {
                    kpiInfoListVo.setYtdSellIn(vo.getActLiters());
                    kpiInfoListVo.setYtdTarget(vo.getBaselineTargetLiters());
                }
            }
        }
    }

	private int calPage(int page,int pageSize, Long countNum){
        if(pageSize <= 0) {
            pageSize = 10;
        }
        if(page <= 0) {
            page = 1;
        }
        if((page-1)*pageSize >= countNum){
            if(countNum%pageSize == 0) {
                page = (int) (countNum/pageSize);
            }else {
                page = (int) (countNum/pageSize+1);
            }
        }
        return page;
    }

	@Override
	public JsonResponse queryKPIDSRInfoBySalesCai(Integer partnerId,String orgCai, String date,Integer page,Integer pageSize) {
		JsonResponse resMap = new JsonResponse();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		if(null == orgCai) {
			resMap.setErrorMsg("查无此数据");
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		Date startDate = DateUtil.parseDate(date,"yyyy-MM-dd");
		Date endDate = DateUtil.addMonths(startDate, 1);
		paramMap.put("orgCai", orgCai);
		paramMap.put("startDate", dateFormat.format(startDate));
		paramMap.put("endDate", dateFormat.format(endDate));
		paramMap.put("partnerId", partnerId);
		paramMap.put("yTDStartDate", new SimpleDateFormat("yyyy-01-01 00:00:00.000").format(startDate));
        //判断查询时间是否存在历史数据
        paramMap.put("month", DateUtil.getMonth(startDate));
        paramMap.put("year", DateUtil.getYear(startDate));
        //先查询历史表中有无数据
        int checkWhetherPoints = dsrKpiMapper.checkWhetherPoints(dateFormat.format(startDate));
        if (checkWhetherPoints > 0) {
            log.debug("历史表中有数据，直接查询历史表...");
            //手动分页，查询页数，根据partnerID排序
            Long count = ciDsrKpiHistoryMapper.kpiDsrByPartnerPageCount(paramMap);
            if (count == 0) {
                return resMap;
            }
            page = this.calPage(page,pageSize, count);
            paramMap.put("pageSize", pageSize);
            paramMap.put("endRowNum", (page - 1) * pageSize);
            List<KpiCalInfo> kpiCalInfos = ciDsrKpiHistoryMapper.kpiDsrByPartnerPage(paramMap);
            List<KPIInfoListVo> kpiInfoListVos = this.buildHistoryKPIInfoListVos(kpiCalInfos);
            resMap.setListResult(kpiInfoListVos);
            resMap.setTotalOfPaging(count);
            return resMap;
        }
		Long countNum = kPIInfoMapper.countKPIDSRInfoBySalesCaiNum(paramMap);
        page = this.calPage(page,pageSize, countNum);
		paramMap.put("pageSize", pageSize);
		paramMap.put("endRowNum", (page-1)*pageSize);
		List<KPIInfoListVo> queryKPIDSRInfo = kPIInfoMapper.queryKPIDSRInfoBySalesCai(paramMap);
        for (KPIInfoListVo item : queryKPIDSRInfo) {
            item.setYtdNum(NumberUtil.add(item.getYtdShopNum(), item.getYtdFleetNum()).longValue());
            item.setNewCustomer(NumberUtil.add(item.getNewWkShopNum(), item.getNewFleetNum()).longValue());
        }
		resMap.setListResult(queryKPIDSRInfo);
		resMap.setTotalOfPaging(countNum);
		return resMap;
	}

    @Override
    public List<KpiCalInfo> buildExportBonusDetail(String date) throws Exception {
        WxTUser curUser = ContextUtil.getCurUser();
        String orgCai = curUser.getCai();
        Long userId = curUser.getUserId();
        String roleType = null; //ytd销量类型
        String cai = null;
        int permissionWeight;
        Map<String, Object> paramMap = new HashMap<String, Object>();
        //判断是否有权限
        //获取当前用户的查看权限
        permissionWeight = operationPermissionBizService.getPermissionWeight(userId, "CAI_KPI_INFO");
        if (1 == (permissionWeight & 1)) {
            permissionWeight = 1;
            roleType = "admin";
            cai = null;
        } else if (4 == (permissionWeight & 4)) {
            permissionWeight = 4;
            roleType = "channel manager";
            cai = orgCai;
        } else if (8 == (permissionWeight & 8)) {
            permissionWeight = 8;
            roleType = "asm";
            cai = orgCai;
        } else if (64 == (permissionWeight & 64)) {
            permissionWeight = 64;
            roleType = "teamleader";
            cai = orgCai;
        }else if (16 == (permissionWeight & 16)) {
            String userModel = curUser.getUserModel();
            if (WxTUser.USER_MODEL_SP.equals(userModel)) {
                permissionWeight = 32; //销售老板赋有fdsr的权限，且特殊处理
                paramMap.put("partnerId", curUser.getOrgId());
                roleType = "admin";
                cai = null;
            } else {
                permissionWeight = 16;
                roleType = "flsr";
                cai = orgCai;
            }
        }else {
            throw new WxPltException("无权限");
        }
        paramMap.put("orgCai", cai);
        paramMap.put("orgType", permissionWeight);
        Date now = new Date();
        if(StringUtils.isNotBlank(date)){
            now = DateUtil.parseDate(date, "yyyy-MM-dd");
        }
        String year = DateUtil.getYear(now);
        String month = DateUtil.getMonth(now);
        paramMap.put("year", year);
        //获取历史的kpi数据
        List<KpiCalInfo> kpiCalInfos = ciDsrKpiHistoryMapper.getKpiHistoryByYear(paramMap);
        //获取历史积分记录
        List<KpiCalInfo> points = dsrKpiMapper.getHistoryDsrKpiPoint(paramMap);
        HashSet<Integer> set = new HashSet<Integer>();
        for (KpiCalInfo history : kpiCalInfos) {
            set.add(Integer.parseInt(history.getMonth()));
        }
        //获取已经生效的手动调整的积分总和
        List<KpiCalInfo> adjustPoints = dsrKpiMapper.getAdjustPoint(paramMap);
        List<String> months = this.exportMonthDatas(set, year, month);
        for (String mm : months) {
            String calDate = year + "-" + mm + "-01";
            //计算当前月份的kpi数据
            kpiCalInfos.addAll(dsrKpiBizService.calculateKpi(calDate, true, paramMap));
            List<DsrKpi> dsrKpis = dsrKpiBizService.calculateBonus(calDate);
            points.addAll(this.collectPointByDsr(dsrKpis, year, mm));
        }
        //获取带发放的手动调整的积分总和
        paramMap.put("months", months);
        adjustPoints.addAll(dsrKpiMapper.getPreAdjustPointCollect(paramMap));
        //过滤掉只计算ytd的数据
        this.calculateAdjustPoint(kpiCalInfos,adjustPoints);
        this.mixPointToKpiDetail(kpiCalInfos, points);
        this.setLeftPoints(kpiCalInfos,year,months,paramMap);
        this.exportSetSellInData(now, roleType, cai, kpiCalInfos);
        //过滤掉总积分为0并且没有调整只计算了ytd的数据
        Iterator<KpiCalInfo> iterator = kpiCalInfos.iterator();
        while (iterator.hasNext()) {
            KpiCalInfo next = iterator.next();
            if ((next.getYtdPoint() == null || next.getYtdPoint() == 0) &&
                    (next.getAdjustPoint() == null || next.getAdjustPoint() == 0) && next.getFlag()) {
                iterator.remove();
            }
        }
        return kpiCalInfos;
    }

    @Override
    public List<ExportBonus> buildExportBonus(String date,boolean year) throws Exception {
        WxTUser curUser = ContextUtil.getCurUser();
        String orgCai = curUser.getCai();
        Long userId = curUser.getUserId();
        String roleType = null; //ytd销量类型
        String cai = null;
        int permissionWeight;
        Map<String, Object> paramMap = new HashMap<String, Object>();
        //判断是否有权限
        //获取当前用户的查看权限
        permissionWeight = operationPermissionBizService.getPermissionWeight(userId, "CAI_KPI_INFO");
        if (1 == (permissionWeight & 1)) {
            permissionWeight = 1;
            roleType = "admin";
            cai = null;
        } else if (4 == (permissionWeight & 4)) {
            permissionWeight = 4;
            roleType = "channel manager";
            cai = orgCai;
        } else if (8 == (permissionWeight & 8)) {
            permissionWeight = 8;
            roleType = "asm";
            cai = orgCai;
        }else if (64 == (permissionWeight & 64)) {
            permissionWeight = 64;
            roleType = "teamleader";
            cai = orgCai;
        } else if (16 == (permissionWeight & 16)) {
            String userModel = curUser.getUserModel();
            if (WxTUser.USER_MODEL_SP.equals(userModel)) {
                permissionWeight = 32; //销售老板赋有fdsr的权限，且特殊处理
                paramMap.put("partnerId", curUser.getOrgId());
                roleType = "admin";
                cai = null;
            } else {
                permissionWeight = 16;
                roleType = "flsr";
                cai = orgCai;
            }
        } else {
            throw new WxPltException("无权限");
        }
        paramMap.put("orgCai", cai);
        paramMap.put("roleType", roleType);
        paramMap.put("orgType", permissionWeight);
        Date dataDate;
        if(date!=null){
            dataDate = DateUtil.parseDate(date, "yyyy-MM-dd");
        }else{
            dataDate = new Date();
        }
        String currentDate = DateUtil.getDateStr(dataDate, "yyyy-MM-01");
        String yyyy = DateUtil.getDateStr(dataDate, "yyyy");
        String mm = DateUtil.getDateStr(dataDate, "MM");
        paramMap.put("year", yyyy);
        //如果指定了日期只查询指定的月份
        List<ExportBonus> exportBonuses = new ArrayList<ExportBonus>();
        List<String> months = new ArrayList<String>();
        if(!year){
            List<DsrKpi> dsrKpis = dsrKpiBizService.calculateBonus(currentDate);
            if (CollectionUtil.isNotEmpty(dsrKpis)) {
                List<ExportBonus> currentExportBonus = this.buildCurrentExportBonus(dsrKpis, paramMap, yyyy, mm);
                exportBonuses.addAll(currentExportBonus);
            }
            paramMap.put("month", mm);
            List<KpiCalInfo> preAdjustPoint = dsrKpiMapper.getPreAdjustPoint(paramMap);
            this.buildAdjustPoint(exportBonuses,preAdjustPoint);
            months.add(mm);
        }else{
            List<ExportBonus> historys = dsrKpiMapper.buildExportBonus(paramMap);
            exportBonuses.addAll(historys);
            HashSet<Integer> set = new HashSet<Integer>();
            for (ExportBonus history : historys) {
                set.add(Integer.parseInt(history.getMonth()));
            }
            //获取已经生效的手动调整的积分总和
            List<KpiCalInfo> adjustPoints = dsrKpiMapper.getAdjustPointDetails(paramMap);
            //获取需要导出的月份，如果历史表没有都直接计算
            months = this.exportMonthDatas(set, yyyy ,mm);
            for (String month : months) {
                String calDate = yyyy + "-" + month + "-01";
                List<DsrKpi> dsrKpis = dsrKpiBizService.calculateBonus(calDate);
                if (CollectionUtil.isNotEmpty(dsrKpis)) {
                    List<ExportBonus> currentExportBonus = this.buildCurrentExportBonus(dsrKpis, paramMap, yyyy, month);
                    exportBonuses.addAll(currentExportBonus);
                }
            }
            //获取带发放的手动调整的积分总和
            paramMap.put("months", months);
            adjustPoints.addAll(dsrKpiMapper.getPreAdjustPoint(paramMap));
            this.buildAdjustPoint(exportBonuses,adjustPoints);
        }
        //计算总剩余积分 计算ytd积分
        List<KpiCalInfo> ciDsrLeftPoint = dsrKpiMapper.getCiDsrLeftPoint(paramMap);
        this.buildBonusData(exportBonuses, ciDsrLeftPoint, months);
        //去掉ytd和本月获得总积分都为0的数据
        Iterator<ExportBonus> iterator = exportBonuses.iterator();
        while (iterator.hasNext()){
            ExportBonus next = iterator.next();
            if(next.getYTDBonus() == 0 && next.getBonus() == 0){
                iterator.remove();
            }
        }
        return exportBonuses;
    }

    /**
     * 计算手动调整的积分
     */
    private void buildAdjustPoint(List<ExportBonus> exportBonuses, List<KpiCalInfo> adjustPoints) {
        HashMap<String, ExportBonus> map = new HashMap<String, ExportBonus>();
        for (KpiCalInfo kpiCalInfo : adjustPoints) {
            boolean flag = true;
            for (ExportBonus exportBonus : exportBonuses) {
                if (exportBonus.getDsrId().equals(kpiCalInfo.getDsrId()) && Integer.parseInt(kpiCalInfo.getMonth()) == Integer.parseInt(exportBonus.getMonth())) {
                    HashMap<String, KpiCalInfo> adjustBonusMap = exportBonus.getAdjustBonusMap();
                    if (adjustBonusMap == null) {
                        adjustBonusMap = new HashMap<String, KpiCalInfo>();
                    }
                    adjustBonusMap.put(kpiCalInfo.getAdjustReasonCode(), kpiCalInfo);
                    exportBonus.setAdjustBonusMap(adjustBonusMap);
                    exportBonus.setAdjustBonus(NumberUtil.add(new Double(exportBonus.getAdjustBonus()), kpiCalInfo.getAdjustPoint()));
                    flag = false;
                }
            }
            if (flag) {
                String key = kpiCalInfo.getMonth().concat(kpiCalInfo.getDsrId().toString());
                ExportBonus exportBonus = map.get(key);
                if (exportBonus == null) {
                    exportBonus = new ExportBonus();
                    BeanUtil.copyProperties(kpiCalInfo, exportBonus);
                    HashMap<String, KpiCalInfo> adjustBonusMap = new HashMap<String, KpiCalInfo>();
                    adjustBonusMap.put(kpiCalInfo.getAdjustReasonCode(), kpiCalInfo);
                    exportBonus.setAdjustBonusMap(adjustBonusMap);
                } else {
                    exportBonus.getAdjustBonusMap().put(kpiCalInfo.getAdjustReasonCode(), kpiCalInfo);
                }
                exportBonus.setAdjustBonus(NumberUtil.add(new Double(exportBonus.getAdjustBonus()), kpiCalInfo.getAdjustPoint()));
                map.put(key, exportBonus);
            }
        }
        exportBonuses.addAll(map.values());
    }

    /**
     * 如果有ytd积分但是没有在本月统计的数据中，手动加入这部分数据
     */
    private void buildBonusData(List<ExportBonus> exportBonuses, List<KpiCalInfo> ciDsrLeftPoint, List<String> months) {
        for (String month : months) {
            ArrayList<ExportBonus> list = new ArrayList<ExportBonus>();
            for (KpiCalInfo kpiCalInfo : ciDsrLeftPoint) {
                boolean flag = true;
                for (ExportBonus exportBonus : exportBonuses) {
                    if(kpiCalInfo.getDsrId().equals(exportBonus.getDsrId())){
                        exportBonus.setBonusBalance(kpiCalInfo.getYtdPointBalance());
                        exportBonus.setYTDBonus(kpiCalInfo.getYtdPoint());
                    }
                    if (kpiCalInfo.getDsrId().equals(exportBonus.getDsrId()) && Integer.parseInt(month) == Integer.parseInt(exportBonus.getMonth())) {
                        flag = false;
                    }
                }
                if (flag && kpiCalInfo.getYtdPoint() > 0) {
                    ExportBonus exportBonus = new ExportBonus();
                    BeanUtil.copyProperties(kpiCalInfo, exportBonus);
                    exportBonus.setYTDBonus(kpiCalInfo.getYtdPoint());
                    exportBonus.setBonusBalance(kpiCalInfo.getYtdPointBalance());
                    exportBonus.setBonus(0);
                    exportBonus.setFleetBonus(0);
                    exportBonus.setVisitBonus(0);
                    exportBonus.setNewCusBonus(0);
                    exportBonus.setMonth(Integer.valueOf(month).toString());
                    list.add(exportBonus);
                }
            }
            exportBonuses.addAll(list);
        }
        for (ExportBonus exportBonus : exportBonuses) {
            exportBonus.setBonus(exportBonus.getNewCusBonus() + exportBonus.getFleetBonus()
                    + exportBonus.getVisitBonus() + exportBonus.getAdjustBonus());
        }
    }

    private List<String> exportMonthDatas(HashSet<Integer> months,String year,String month){
        //获取需要计算的月份
        ArrayList<String> calMonths = new ArrayList<String>();
        for (int i = 1; i <= Integer.parseInt(month); i++) {
            if("2020".equals(year) && i <3){
                continue;
            }
            if(!months.contains(i)){
                calMonths. add(String.valueOf(i));
            }
        }
        return calMonths;

    }

    private List<ExportBonus> buildCurrentExportBonus(List<DsrKpi> dsrKpis, Map<String, Object> paramMap, String year, String month) {
        HashSet<Long> dsrIds = new HashSet<Long>();
        for (DsrKpi dsrKpi : dsrKpis) {
            dsrIds.add(dsrKpi.getDsrId());
        }
        paramMap.put("dsrs", dsrIds);
        //获取dsr相关的组织信息
        List<ExportBonus> dsrInfoList = dsrKpiMapper.getDsrInfoByParams(paramMap);
        HashMap<Long, ExportBonus> maps = new HashMap<Long, ExportBonus>();
        for (ExportBonus item : dsrInfoList) {
            maps.put(item.getDsrId(), item);
        }
        List<ExportBonus> result = new ArrayList<ExportBonus>();
        for (ExportBonus exportBonus : dsrInfoList) {
            exportBonus.setYear(year);
            exportBonus.setMonth(Integer.valueOf(month).toString());
            for (DsrKpi dsrKpi : dsrKpis) {
                if (exportBonus.getDsrId().equals(dsrKpi.getDsrId())) {
                    if (DsrKpiBizServiceImpl.KPI_CODE_DAYS_OF_MONTH.equals(dsrKpi.getKpiCode())) {
                        exportBonus.setVisitBonus(dsrKpi.getAwardPoint());
                    } else if (DsrKpiBizServiceImpl.KPI_CODE_AMOUNT_OF_CERTIFICATE.equals(dsrKpi.getKpiCode())) {
                        exportBonus.setFleetBonus(dsrKpi.getAwardPoint());
                    } else if (DsrKpiBizServiceImpl.KPI_CODE_TOP_RANK.equals(dsrKpi.getKpiCode())) {
                        exportBonus.setNewCusBonus(dsrKpi.getAwardPoint());
                    }
                }
            }
            result.add(exportBonus);
        }
        return result;
    }

    private void calculateAdjustPoint(List<KpiCalInfo> kpiCalInfos, List<KpiCalInfo> adjustPoints){
        ArrayList<KpiCalInfo> list = new ArrayList<KpiCalInfo>();
        for (KpiCalInfo adjustPoint : adjustPoints) {
            boolean flag = true;
            for (KpiCalInfo kpiCalInfo : kpiCalInfos) {
                if(kpiCalInfo.getDsrId().equals(adjustPoint.getDsrId()) && Integer.parseInt(kpiCalInfo.getMonth()) == Integer.parseInt(adjustPoint.getMonth())){
                    flag = false;
                    kpiCalInfo.setAdjustPoint(adjustPoint.getAdjustPoint());
                    break;
                }
            }
            if(flag){
                list.add(adjustPoint);
            }
        }
        kpiCalInfos.addAll(list);
    }

    /**
     * 设置剩余的积分数量
     */
    private void setLeftPoints(List<KpiCalInfo> kpiCalInfos,String year,List<String> months,Map<String,Object> params){
        List<KpiCalInfo> leftPoints = dsrKpiMapper.getCiDsrLeftPoint(params);
        for (String month : months) {
            HashSet<Long> dsrIds = new HashSet<Long>();
            for (KpiCalInfo kpiCalInfo : kpiCalInfos) {
                if(Integer.parseInt(kpiCalInfo.getMonth()) == Integer.parseInt(month)){
                    dsrIds.add(kpiCalInfo.getDsrId());
                }
            }
            ArrayList<KpiCalInfo> haveLeft = new ArrayList<KpiCalInfo>();
            for (KpiCalInfo leftPoint : leftPoints) {
                if(!dsrIds.contains(leftPoint.getDsrId()) && leftPoint.getYtdPoint() > 0){
                    leftPoint.setMonth(month);
                    leftPoint.setYear(year);
                    leftPoint.setFlag(false);
                    haveLeft.add(leftPoint);
                }
            }
            for (KpiCalInfo leftPoint : leftPoints) {
                for (KpiCalInfo dsr : kpiCalInfos) {
                    if(dsr.getDsrId().equals(leftPoint.getDsrId())){
                        dsr.setYtdPointBalance(leftPoint.getYtdPointBalance());
                        dsr.setYtdPoint(leftPoint.getYtdPoint());
                    }
                }
            }
            kpiCalInfos.addAll(haveLeft);
        }
    }

    /**
     * 设置ytd销量数据
     */
    private void exportSetSellInData(Date date,String roleType,String cai,List<KpiCalInfo> kpiCalInfos){
        DistributorPerformanceParam param = new DistributorPerformanceParam();
        param.setYear(Integer.parseInt(new SimpleDateFormat("yyyy").format(date)));
        param.setCustomerCategory("commercial");
        param.setProductChannel("commercial");
        List<DistributorPerformanceVo> list = biProcedureBizService.queryDistributorPerformance(param,
                DistributorPerformanceField.DISTRIBUTOR_ID,
                DistributorPerformanceField.PRODUCT_CHANNEL,
                DistributorPerformanceField.ACT_LITERS,
                DistributorPerformanceField.ACT_MARGIN_RMB,
                DistributorPerformanceField.ACT_MARGIN_USD,
                DistributorPerformanceField.BASELINE_TARGET_LITERS,
				DistributorPerformanceField.ELITE_ACTUAL_LITERS);
        for (KpiCalInfo item : kpiCalInfos) {
            for (DistributorPerformanceVo vo : list) {
                if(item.getDistributorId().equals(vo.getDistributorId())){
                    item.setYtdSellIn(vo.getActLiters()/1000);
                    item.setYtdTargetSellIn(vo.getBaselineTargetLiters()/1000);
                }
            }
        }
    }

    /**
     * 组合数据，计算积分总和等
     */
    private void mixPointToKpiDetail(List<KpiCalInfo> kpiDetails, List<KpiCalInfo> kpiCalInfos) {
        for (KpiCalInfo kpiDetail : kpiDetails) {
            kpiDetail.setCustomer(NumberUtil.add(kpiDetail.getNewShop(), kpiDetail.getNewFleet()).longValue());
            kpiDetail.setFleetPerformanceTotalCount(NumberUtil.add(kpiDetail.getFleetPerformance(), kpiDetail.getHighFleetPerformance()).longValue());
            kpiDetail.setPotentialCustomer(NumberUtil.add(kpiDetail.getPotentialFleet(), kpiDetail.getPotentialShop()).longValue());
            for (KpiCalInfo kpiCalInfo : kpiCalInfos) {
                if (kpiDetail.getDsrId().equals(kpiCalInfo.getDsrId()) && kpiDetail.getMonth().equals(kpiCalInfo.getMonth())) {
                    kpiDetail.setAwardPoint(kpiCalInfo.getAwardPoint());
                    break;
                }
            }
            kpiDetail.setAwardPoint(NumberUtil.add(kpiDetail.getAwardPoint(), kpiDetail.getAdjustPoint()));
        }
        HashMap<String, List<KpiCalInfo>> tempMap = new HashMap<String, List<KpiCalInfo>>();
        for (KpiCalInfo kpiDetail : kpiDetails) {
            String key = kpiDetail.getPartnerId() + kpiDetail.getMonth();
            List<KpiCalInfo> list = tempMap.get(key);
            if(list == null){
                list = new ArrayList<KpiCalInfo>();
                list.add(kpiDetail);
                tempMap.put(key,list);
            }else{
                list.add(kpiDetail);
            }
        }
        List<List<KpiCalInfo>> groupByMonth = new ArrayList<List<KpiCalInfo>>(tempMap.values());
        //经销商平均DSR新增客户数、经销商平均DSR拜访客户数
        for (List<KpiCalInfo> calInfos : groupByMonth) {
            long totelVisit = 0L;
            for (KpiCalInfo calInfo : calInfos) {
                totelVisit += calInfo.getEffectiveVisits() == null? 0 : calInfo.getEffectiveVisits();
            }
            Double visitAvg = NumberUtil.div(new Double(totelVisit), new Double(calInfos.size()), 1, RoundingMode.HALF_UP);
            for (KpiCalInfo calInfo : calInfos) {
                calInfo.setVisitAvg(visitAvg);
            }
            long totelNewCustomer = 0L;
            for (KpiCalInfo calInfo : calInfos) {
                totelNewCustomer += calInfo.getCustomer() == null ? 0 : calInfo.getCustomer();
            }
            Double newCustomerAvg = NumberUtil.div(new Double(totelNewCustomer), new Double(calInfos.size()), 1, RoundingMode.HALF_UP);
            for (KpiCalInfo calInfo : calInfos) {
                calInfo.setNewCustomerAvg(newCustomerAvg);
            }
        }

    }

    /**
     * 根据dsr累计积分
     */
    private List<KpiCalInfo> collectPointByDsr(List<DsrKpi> dsrKpis,String year,String month){
        ArrayList<KpiCalInfo> list = new ArrayList<KpiCalInfo>();
        HashSet<Long> dsrIds = new HashSet<Long>();
        for (DsrKpi dsrKpi : dsrKpis) {
            dsrIds.add(dsrKpi.getDsrId());
        }
        for (Long dsrId : dsrIds) {
            KpiCalInfo dsr = null;
            for (DsrKpi item : dsrKpis) {
                if(dsrId.equals(item.getDsrId())){
                    if(dsr==null){
                        dsr = new KpiCalInfo();
                        dsr.setDsrId(dsrId);
                        dsr.setAwardPoint(item.getAwardPoint());
                        dsr.setMonth(month);
                        dsr.setYear(year);
                    }else{
                        dsr.setAwardPoint(dsr.getAwardPoint() + item.getAwardPoint());
                    }
                }
            }
            if(dsr!=null){
                list.add(dsr);
            }
        }
        return list;
    }

    /**
     * 属性拷贝
     */
    private void coverDatas(List<KpiCalInfo> list,KpiCalInfo target){
        for (KpiCalInfo item : list) {
            if(item.getDsrId().equals(target.getDsrId())){
                BeanUtil.copyProperties(item, target, CopyOptions.create().setIgnoreNullValue(true));
            }
        }
    }

    /**
     * 用来计算计算经销商维度当月kpi的情况，暂时不用
     */
    private void getData(String date) throws Exception{
        Date queryDate = DateUtil.parseDate(date,"yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-01-01 00:00:00.000");
        String yearstart = dateFormat1.format(queryDate);
        String startDate = dateFormat.format(queryDate);
        String endDate = dateFormat.format(DateUtil.addMonths(queryDate, 1));
        List<KpiCalInfo> partnerYTDNewShopKpis = kPIInfoMapper.partnerNewShopKpi(yearstart, endDate);
        //将属性设置到ytd上面
        for (KpiCalInfo partnerYTDNewShopKpi : partnerYTDNewShopKpis) {
            partnerYTDNewShopKpi.setYtdShop(partnerYTDNewShopKpi.getNewShop());
            partnerYTDNewShopKpi.setNewShop(null);
        }
        List<KpiCalInfo> partnerYTDNewFleetKpis = kPIInfoMapper.partnerNewFleetKpi(yearstart, endDate);
        for (KpiCalInfo partnerYTDNewShopKpi : partnerYTDNewShopKpis) {
            partnerYTDNewShopKpi.setYtdFleet(partnerYTDNewShopKpi.getNewFleet());
            partnerYTDNewShopKpi.setNewFleet(null);
        }
        List<KpiCalInfo> partnerNewShopKpis = kPIInfoMapper.partnerNewShopKpi(startDate, endDate);
        List<KpiCalInfo> partnerNewFleetKpis = kPIInfoMapper.partnerNewFleetKpi(startDate, endDate);
        List<KpiCalInfo> partnerEffectiveDays = kPIInfoMapper.partnerEffectiveDays(startDate, endDate);
        HashSet<Long> partnerIds = new HashSet<Long>();
        partnerIds.addAll(this.getAllPartnerId(partnerYTDNewShopKpis));
        partnerIds.addAll(this.getAllPartnerId(partnerYTDNewFleetKpis));
        partnerIds.addAll(this.getAllPartnerId(partnerYTDNewFleetKpis));
        partnerIds.addAll(this.getAllPartnerId(partnerNewShopKpis));
        partnerIds.addAll(this.getAllPartnerId(partnerNewFleetKpis));
        partnerIds.addAll(this.getAllPartnerId(partnerEffectiveDays));
        List<KpiCalInfo> joinPartners = kPIInfoMapper.getJoinPartners(partnerIds);
        //组合所有的数据
        for (KpiCalInfo joinPartner : joinPartners) {
            joinPartner.initData();
            joinPartner.setMonth(DateUtil.getMonth(queryDate));
            joinPartner.setYear(DateUtil.getYear(queryDate));
            this.coverDatas(partnerYTDNewShopKpis,joinPartner);
            this.coverDatas(partnerYTDNewFleetKpis,joinPartner);
            this.coverDatas(partnerNewShopKpis,joinPartner);
            this.coverDatas(partnerNewFleetKpis,joinPartner);
            this.coverDatas(partnerEffectiveDays,joinPartner);
        }
        //计算ytd销量
        for (KpiCalInfo joinPartner : joinPartners) {
            joinPartner.setYtdCustomer(NumberUtil.add(joinPartner.getYtdFleet(),joinPartner.getYtdShop()).longValue());
            joinPartner.setCustomer(NumberUtil.add(joinPartner.getNewFleet(),joinPartner.getNewShop()).longValue());
        }
        //通过region汇总数据
        HashSet<String> regionDatas = new HashSet<String>();
        for (KpiCalInfo item : joinPartners) {
            regionDatas.add(item.getRegionName());
        }
        for (String regionData : regionDatas) {
            KPIInfoVO kpiInfoVO = null;
            for (KpiCalInfo joinPartner : joinPartners) {
                if(kpiInfoVO == null){
                    kpiInfoVO = new KPIInfoVO();
                    kpiInfoVO.setRegionName(joinPartner.getRegionCnName());
                    kpiInfoVO.setNewFleetNum(NumberUtil.add(joinPartner.getNewFleet(), kpiInfoVO.getNewFleetNum()).longValue());
                    kpiInfoVO.setNewWKShopNum(NumberUtil.add(joinPartner.getNewShop(), kpiInfoVO.getNewWKShopNum()).longValue());
                }
            }
        }

    }

    private HashSet<Long> getAllPartnerId(List<KpiCalInfo> list) {
        HashSet<Long> ids = new HashSet<Long>();
        for (KpiCalInfo kpiCalInfo : list) {
            ids.add(kpiCalInfo.getPartnerId());
        }
        return ids;
    }

    /**
     * 组装kpi历史数据给前端饼图
     */
    private List<KPIInfoVO> buildCiKpiHistoryDatas(List<KpiCalInfo> kpiCalInfos){
        HashSet<String> regions = new HashSet<String>();
        for (KpiCalInfo kpiCalInfo : kpiCalInfos) {
            regions.add(kpiCalInfo.getRegionName());
        }
        List<KPIInfoVO> regionDatas = new ArrayList<KPIInfoVO>();
        for (String region : regions) {
            HashSet<Long> newShopPartnerIds = new HashSet<Long>();
            HashSet<Long> newCustomerPartnerIds = new HashSet<Long>();
            HashSet<Long> newYtdCustomerPartnerIds = new HashSet<Long>();
            HashSet<Long> newFleetNumPartnerIds = new HashSet<Long>();
            HashSet<Long> newYTDShopPartnerIds = new HashSet<Long>();
            HashSet<Long> newYTDFleetNumPartnerIds = new HashSet<Long>();
            HashSet<Long> newProofNumPartnerIds = new HashSet<Long>();
            HashSet<Long> excuPartnerIds = new HashSet<Long>();
            KPIInfoVO regionData = null;
            for (KpiCalInfo item : kpiCalInfos) {
                if(region.equals(item.getRegionName())){
                    if(regionData == null){
                        regionData = new KPIInfoVO();
                        regionData.initData();
                        regionData.setRegionName(item.getRegionCnName());
                    }
                    if(item.getNewShop() > 0){
                        newShopPartnerIds.add(item.getPartnerId());
                    }
                    if(item.getNewFleet() > 0){
                        newFleetNumPartnerIds.add(item.getPartnerId());
                    }
                    if(item.getHighFleetPerformance() > 0 || item.getFleetPerformance() > 0){
                        newProofNumPartnerIds.add(item.getPartnerId());
                    }
                    if(item.getYtdShop()>0){
                        newYTDShopPartnerIds.add(item.getPartnerId());
                    }
                    if(item.getYtdFleet()>0){
                        newYTDFleetNumPartnerIds.add(item.getPartnerId());
                    }
                    if(item.getEffectiveDays() >= Long.parseLong(item.getVisitEffectiveAimDay())){
                        excuPartnerIds.add(item.getPartnerId());
                    }
                    regionData.setNewWKShopNum(regionData.getNewWKShopNum() + item.getNewShop());
                    regionData.setNewFleetNum(regionData.getNewFleetNum() + item.getNewFleet());
                    regionData.setYtdShopNum(regionData.getYtdShopNum() + item.getYtdShop());
                    regionData.setYtdFleetNum(regionData.getYtdFleetNum()+ item.getYtdFleet());
                    regionData.setProofPerNum(regionData.getProofPerNum() + item.getFleetPerformance() + item.getHighFleetPerformance());
                    regionData.setExcuNum(regionData.getExcuNum() + (item.getEffectiveDays() >= Long.parseLong(item.getVisitEffectiveAimDay()) ? 1 : 0));
                }
            }
            newCustomerPartnerIds.addAll(newShopPartnerIds);
            newCustomerPartnerIds.addAll(newFleetNumPartnerIds);
            newYtdCustomerPartnerIds.addAll(newYTDShopPartnerIds);
            newYtdCustomerPartnerIds.addAll(newYTDFleetNumPartnerIds);
            if(regionData!=null){
                regionData.setYtdNum(regionData.getYtdFleetNum() + regionData.getYtdShopNum());
                regionData.setNewCustomerNum(regionData.getNewFleetNum() + regionData.getNewWKShopNum());
                regionData.setNewWKShopCustNum((long) newShopPartnerIds.size());
                regionData.setNewFleetCustNum((long) newFleetNumPartnerIds.size());
                regionData.setYtdShopCustNum((long) newYTDShopPartnerIds.size());
                regionData.setYtdFleetCustNum((long) newYTDFleetNumPartnerIds.size());
                regionData.setExcuCustNum((long) excuPartnerIds.size());
                regionData.setProofPerCustNum((long) newProofNumPartnerIds.size());
                regionData.setYtdCustNum((long) newYtdCustomerPartnerIds.size());
                regionData.setNewCustomerCustNum((long) newCustomerPartnerIds.size());
                regionDatas.add(regionData);
            }
        }
        return regionDatas;
    }

    private KPIInfoVO buildCountVo(List<KPIInfoVO> queryKPIInfo){
        long newWKShopNum = 0L;
        long newWKShopCustNum = 0L;
        long newFleetNum = 0;
        long newFleetCustNum = 0;
        long excuNum = 0L;
        long excuCustNum = 0L;
        long proofPerNum = 0L;
        long proofPerCustNum = 0L;
        long newCustomerNum = 0;
        long newCustomerCustNum = 0;
        long ytdShopNum = 0;
        long ytdShopCustNum = 0;
        long ytdFleetNum = 0;
        long ytdFleetCustNum = 0;
        long yTDNum = 0L;
        long yTDCustNum = 0L;
        if(!CollectionUtils.isEmpty(queryKPIInfo)) {
            for (KPIInfoVO kpiInfoVO : queryKPIInfo) {
                newWKShopNum+=(kpiInfoVO.getNewWKShopNum()==null ? 0 : kpiInfoVO.getNewWKShopNum());
                newWKShopCustNum+=(kpiInfoVO.getNewWKShopCustNum() ==null ? 0 : kpiInfoVO.getNewWKShopCustNum());
                newFleetNum+=(kpiInfoVO.getNewFleetNum()==null ? 0 : kpiInfoVO.getNewFleetNum());
                newFleetCustNum+=(kpiInfoVO.getNewFleetCustNum() ==null ? 0 : kpiInfoVO.getNewFleetCustNum());
                newCustomerNum = newWKShopNum + newFleetNum;
                newCustomerCustNum += (kpiInfoVO.getNewCustomerCustNum() ==null ? 0 : kpiInfoVO.getNewCustomerCustNum());
                excuNum+=(kpiInfoVO.getExcuNum() ==null ? 0 : kpiInfoVO.getExcuNum());
                excuCustNum+=(kpiInfoVO.getExcuCustNum() ==null ? 0 : kpiInfoVO.getExcuCustNum());
                ytdShopNum += (kpiInfoVO.getYtdShopNum() ==null ? 0 : kpiInfoVO.getYtdShopNum());
                ytdShopCustNum += (kpiInfoVO.getYtdShopCustNum() ==null ? 0 : kpiInfoVO.getYtdShopCustNum());
                ytdFleetNum += (kpiInfoVO.getYtdFleetNum() ==null ? 0 : kpiInfoVO.getYtdFleetNum());
                ytdFleetCustNum += (kpiInfoVO.getYtdFleetCustNum() ==null ? 0 : kpiInfoVO.getYtdFleetCustNum());
                yTDNum = ytdShopNum + ytdFleetNum;
                yTDCustNum += (kpiInfoVO.getYtdCustNum() ==null ? 0 : kpiInfoVO.getYtdCustNum());
                proofPerNum+=(kpiInfoVO.getProofPerNum() ==null ? 0 : kpiInfoVO.getProofPerNum());
                proofPerCustNum+=(kpiInfoVO.getProofPerCustNum() ==null ? 0 : kpiInfoVO.getProofPerCustNum());
            }
        }
        KPIInfoVO kpiInfoVO = new KPIInfoVO();
        kpiInfoVO.setExcuCustNum(excuCustNum);
        kpiInfoVO.setExcuNum(excuNum);
        kpiInfoVO.setNewWKShopCustNum(newWKShopCustNum);
        kpiInfoVO.setNewWKShopNum(newWKShopNum);
        kpiInfoVO.setNewFleetNum(newFleetNum);
        kpiInfoVO.setNewFleetCustNum(newFleetCustNum);
        kpiInfoVO.setNewCustomerNum(newCustomerNum);
        kpiInfoVO.setNewCustomerCustNum(newCustomerCustNum);
        kpiInfoVO.setYtdFleetNum(ytdFleetNum);
        kpiInfoVO.setYtdFleetCustNum(ytdFleetCustNum);
        kpiInfoVO.setYtdShopNum(ytdShopNum);
        kpiInfoVO.setYtdShopCustNum(ytdShopCustNum);
        kpiInfoVO.setYtdNum(yTDNum);
        kpiInfoVO.setYtdCustNum(yTDCustNum);
        kpiInfoVO.setProofPerCustNum(proofPerCustNum);
        kpiInfoVO.setProofPerNum(proofPerNum);
        return kpiInfoVO;
    }

    /**
     * 组装kpi历史数据给前端饼图
     */
    private KPIInfoVO buildOneKPIInfoVO(List<KPIInfoVO> regionDatas){
        KPIInfoVO kpiInfoVO = new KPIInfoVO();
        kpiInfoVO.initData();
        for (KPIInfoVO item : regionDatas) {
            kpiInfoVO.setExcuNum(kpiInfoVO.getExcuNum() + item.getExcuNum());
            kpiInfoVO.setExcuCustNum(kpiInfoVO.getExcuCustNum() + item.getExcuCustNum());
            kpiInfoVO.setNewCustomerNum(kpiInfoVO.getNewCustomerNum() + item.getNewCustomerNum());
            kpiInfoVO.setNewCustomerCustNum(kpiInfoVO.getNewCustomerCustNum() + item.getNewCustomerCustNum());
            kpiInfoVO.setNewWKShopNum(kpiInfoVO.getNewWKShopNum() + item.getNewWKShopNum());
            kpiInfoVO.setNewWKShopCustNum(kpiInfoVO.getNewWKShopCustNum() + item.getNewWKShopCustNum());
            kpiInfoVO.setNewFleetNum(kpiInfoVO.getNewFleetNum() + item.getNewFleetNum());
            kpiInfoVO.setNewFleetCustNum(kpiInfoVO.getNewFleetCustNum() + item.getNewFleetCustNum());
            kpiInfoVO.setYtdNum(kpiInfoVO.getYtdNum() + item.getYtdNum());
            kpiInfoVO.setYtdCustNum(kpiInfoVO.getYtdCustNum()+ item.getYtdCustNum());
            kpiInfoVO.setYtdShopNum(kpiInfoVO.getYtdShopNum()+ item.getYtdShopNum());
            kpiInfoVO.setYtdShopCustNum(kpiInfoVO.getYtdShopCustNum()+ item.getYtdShopCustNum());
            kpiInfoVO.setYtdFleetNum(kpiInfoVO.getYtdFleetNum()+ item.getYtdFleetNum());
            kpiInfoVO.setYtdFleetCustNum(kpiInfoVO.getYtdFleetCustNum()+ item.getYtdFleetCustNum());
            kpiInfoVO.setProofPerNum(kpiInfoVO.getProofPerNum()+ item.getProofPerNum());
            kpiInfoVO.setProofPerCustNum(kpiInfoVO.getProofPerCustNum()+ item.getProofPerCustNum());
        }
        return kpiInfoVO;
    }

    private List<KPIInfoListVo> buildHistoryKPIInfoListVos(List<KpiCalInfo> kpiCalInfos){
        List<KPIInfoListVo> result = new ArrayList<KPIInfoListVo>();
        for (KpiCalInfo item : kpiCalInfos) {
            KPIInfoListVo kpiInfoListVo = new KPIInfoListVo();
            kpiInfoListVo.setChName(item.getDsrName());
            kpiInfoListVo.setCustomerNameCn(item.getPartnerName());
            kpiInfoListVo.setCustomerId(item.getPartnerId());
            kpiInfoListVo.setRegionName(item.getRegionCnName());
            kpiInfoListVo.setSalesName(item.getSalesName());
            kpiInfoListVo.setSuppervisorName(item.getSuppervisorName());
            kpiInfoListVo.setSalesCai(item.getSalesCai());
            kpiInfoListVo.setExcuNum(item.getEffectiveDays());
            kpiInfoListVo.setNewFleetNum(item.getNewFleet());
            kpiInfoListVo.setNewWkShopNum(item.getNewShop());
            kpiInfoListVo.setNewCustomer(item.getNewFleet() + item.getNewShop());
            kpiInfoListVo.setProofPerNum(item.getFleetPerformance() + item.getHighFleetPerformance());
            kpiInfoListVo.setYtdFleetNum(item.getYtdFleet());
            kpiInfoListVo.setYtdShopNum(item.getYtdShop());
            kpiInfoListVo.setDistributorId(item.getDistributorId());
            kpiInfoListVo.setYtdNum(item.getYtdFleet() + item.getYtdShop());
            result.add(kpiInfoListVo);
        }
        return result;
    }
}
