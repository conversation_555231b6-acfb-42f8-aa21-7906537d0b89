ALTER TABLE wx_t_in_stock ADD CONSTRAINT uk_in_stock UNIQUE (stock_in_no);
ALTER TABLE wx_t_in_stock ADD CONSTRAINT pk_in_stock PRIMARY KEY NONCLUSTERED (id);
CREATE INDEX ix_in_stock_order ON wx_t_in_stock (order_type,order_no);

ALTER TABLE wx_t_in_stock_line ADD CONSTRAINT pk_in_stock_line PRIMARY KEY NONCLUSTERED (id);
CREATE INDEX ix_in_stock_line_in_stock_in_no ON wx_t_in_stock_line (stock_in_no);

ALTER TABLE wx_t_in_stock_product ADD CONSTRAINT pk_in_stock_product PRIMARY KEY NONCLUSTERED (id);
CREATE INDEX ix_in_stock_product_in_stock_in_no ON wx_t_in_stock_product (stock_in_no);

ALTER TABLE wx_t_order ADD CONSTRAINT uk_order UNIQUE (order_no);
CREATE INDEX ix_order_source ON wx_t_order (source,source_id);

ALTER TABLE wx_t_out_stock ADD CONSTRAINT uk_out_stock UNIQUE (stock_out_no);
ALTER TABLE wx_t_out_stock ADD CONSTRAINT pk_out_stock PRIMARY KEY NONCLUSTERED (id);

ALTER TABLE wx_t_out_stock_line ADD CONSTRAINT pk_out_stock_line PRIMARY KEY NONCLUSTERED (id);

ALTER TABLE wx_t_out_stock_product ADD CONSTRAINT pk_out_stock_product PRIMARY KEY NONCLUSTERED (id);

--update o set o.order_no='P07180108000509' from wx_t_partner_order o where o.id=579
ALTER TABLE wx_t_partner_order ADD CONSTRAINT uk_partner_order UNIQUE (order_no);
ALTER TABLE wx_t_partner_order ADD CONSTRAINT pk_partner_order PRIMARY KEY NONCLUSTERED (id);


