package com.sys.workflow.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 工作流步骤单表查询条件
 * <AUTHOR>
 * @version 1.0 2020-01-11 15:27
 */
public class WorkflowStepExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkflowStepExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andStepIdIsNull() {
            addCriterion("step_id is null");
            return (Criteria) this;
        }

        public Criteria andStepIdIsNotNull() {
            addCriterion("step_id is not null");
            return (Criteria) this;
        }

        public Criteria andStepIdEqualTo(Long value) {
            addCriterion("step_id =", value, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdNotEqualTo(Long value) {
            addCriterion("step_id <>", value, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdGreaterThan(Long value) {
            addCriterion("step_id >", value, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdGreaterThanOrEqualTo(Long value) {
            addCriterion("step_id >=", value, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdLessThan(Long value) {
            addCriterion("step_id <", value, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdLessThanOrEqualTo(Long value) {
            addCriterion("step_id <=", value, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdIn(List<Long> values) {
            addCriterion("step_id in", values, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdNotIn(List<Long> values) {
            addCriterion("step_id not in", values, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdBetween(Long value1, Long value2) {
            addCriterion("step_id between", value1, value2, "stepId");
            return (Criteria) this;
        }

        public Criteria andStepIdNotBetween(Long value1, Long value2) {
            addCriterion("step_id not between", value1, value2, "stepId");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeIsNull() {
            addCriterion("workflow_code is null");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeIsNotNull() {
            addCriterion("workflow_code is not null");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeEqualTo(String value) {
            addCriterion("workflow_code =", value, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeNotEqualTo(String value) {
            addCriterion("workflow_code <>", value, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeGreaterThan(String value) {
            addCriterion("workflow_code >", value, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeGreaterThanOrEqualTo(String value) {
            addCriterion("workflow_code >=", value, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeLessThan(String value) {
            addCriterion("workflow_code <", value, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeLessThanOrEqualTo(String value) {
            addCriterion("workflow_code <=", value, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeIn(List<String> values) {
            addCriterion("workflow_code in", values, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeNotIn(List<String> values) {
            addCriterion("workflow_code not in", values, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeBetween(String value1, String value2) {
            addCriterion("workflow_code between", value1, value2, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andWorkflowCodeNotBetween(String value1, String value2) {
            addCriterion("workflow_code not between", value1, value2, "workflowCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeIsNull() {
            addCriterion("step_code is null");
            return (Criteria) this;
        }

        public Criteria andStepCodeIsNotNull() {
            addCriterion("step_code is not null");
            return (Criteria) this;
        }

        public Criteria andStepCodeEqualTo(String value) {
            addCriterion("step_code =", value, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeNotEqualTo(String value) {
            addCriterion("step_code <>", value, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeGreaterThan(String value) {
            addCriterion("step_code >", value, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeGreaterThanOrEqualTo(String value) {
            addCriterion("step_code >=", value, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeLessThan(String value) {
            addCriterion("step_code <", value, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeLessThanOrEqualTo(String value) {
            addCriterion("step_code <=", value, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeIn(List<String> values) {
            addCriterion("step_code in", values, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeNotIn(List<String> values) {
            addCriterion("step_code not in", values, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeBetween(String value1, String value2) {
            addCriterion("step_code between", value1, value2, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepCodeNotBetween(String value1, String value2) {
            addCriterion("step_code not between", value1, value2, "stepCode");
            return (Criteria) this;
        }

        public Criteria andStepNameIsNull() {
            addCriterion("step_name is null");
            return (Criteria) this;
        }

        public Criteria andStepNameIsNotNull() {
            addCriterion("step_name is not null");
            return (Criteria) this;
        }

        public Criteria andStepNameEqualTo(String value) {
            addCriterion("step_name =", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotEqualTo(String value) {
            addCriterion("step_name <>", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameGreaterThan(String value) {
            addCriterion("step_name >", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameGreaterThanOrEqualTo(String value) {
            addCriterion("step_name >=", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameLessThan(String value) {
            addCriterion("step_name <", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameLessThanOrEqualTo(String value) {
            addCriterion("step_name <=", value, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameIn(List<String> values) {
            addCriterion("step_name in", values, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotIn(List<String> values) {
            addCriterion("step_name not in", values, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameBetween(String value1, String value2) {
            addCriterion("step_name between", value1, value2, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepNameNotBetween(String value1, String value2) {
            addCriterion("step_name not between", value1, value2, "stepName");
            return (Criteria) this;
        }

        public Criteria andStepIconIsNull() {
            addCriterion("step_icon is null");
            return (Criteria) this;
        }

        public Criteria andStepIconIsNotNull() {
            addCriterion("step_icon is not null");
            return (Criteria) this;
        }

        public Criteria andStepIconEqualTo(String value) {
            addCriterion("step_icon =", value, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconNotEqualTo(String value) {
            addCriterion("step_icon <>", value, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconGreaterThan(String value) {
            addCriterion("step_icon >", value, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconGreaterThanOrEqualTo(String value) {
            addCriterion("step_icon >=", value, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconLessThan(String value) {
            addCriterion("step_icon <", value, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconLessThanOrEqualTo(String value) {
            addCriterion("step_icon <=", value, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconIn(List<String> values) {
            addCriterion("step_icon in", values, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconNotIn(List<String> values) {
            addCriterion("step_icon not in", values, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconBetween(String value1, String value2) {
            addCriterion("step_icon between", value1, value2, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andStepIconNotBetween(String value1, String value2) {
            addCriterion("step_icon not between", value1, value2, "stepIcon");
            return (Criteria) this;
        }

        public Criteria andListOperateNameIsNull() {
            addCriterion("list_operate_name is null");
            return (Criteria) this;
        }

        public Criteria andListOperateNameIsNotNull() {
            addCriterion("list_operate_name is not null");
            return (Criteria) this;
        }

        public Criteria andListOperateNameEqualTo(String value) {
            addCriterion("list_operate_name =", value, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameNotEqualTo(String value) {
            addCriterion("list_operate_name <>", value, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameGreaterThan(String value) {
            addCriterion("list_operate_name >", value, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameGreaterThanOrEqualTo(String value) {
            addCriterion("list_operate_name >=", value, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameLessThan(String value) {
            addCriterion("list_operate_name <", value, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameLessThanOrEqualTo(String value) {
            addCriterion("list_operate_name <=", value, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameIn(List<String> values) {
            addCriterion("list_operate_name in", values, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameNotIn(List<String> values) {
            addCriterion("list_operate_name not in", values, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameBetween(String value1, String value2) {
            addCriterion("list_operate_name between", value1, value2, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andListOperateNameNotBetween(String value1, String value2) {
            addCriterion("list_operate_name not between", value1, value2, "listOperateName");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasIsNull() {
            addCriterion("accept_alias is null");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasIsNotNull() {
            addCriterion("accept_alias is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasEqualTo(String value) {
            addCriterion("accept_alias =", value, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasNotEqualTo(String value) {
            addCriterion("accept_alias <>", value, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasGreaterThan(String value) {
            addCriterion("accept_alias >", value, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasGreaterThanOrEqualTo(String value) {
            addCriterion("accept_alias >=", value, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasLessThan(String value) {
            addCriterion("accept_alias <", value, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasLessThanOrEqualTo(String value) {
            addCriterion("accept_alias <=", value, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasIn(List<String> values) {
            addCriterion("accept_alias in", values, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasNotIn(List<String> values) {
            addCriterion("accept_alias not in", values, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasBetween(String value1, String value2) {
            addCriterion("accept_alias between", value1, value2, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andAcceptAliasNotBetween(String value1, String value2) {
            addCriterion("accept_alias not between", value1, value2, "acceptAlias");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightIsNull() {
            addCriterion("operation_permission_weight is null");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightIsNotNull() {
            addCriterion("operation_permission_weight is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightEqualTo(Integer value) {
            addCriterion("operation_permission_weight =", value, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightNotEqualTo(Integer value) {
            addCriterion("operation_permission_weight <>", value, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightGreaterThan(Integer value) {
            addCriterion("operation_permission_weight >", value, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("operation_permission_weight >=", value, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightLessThan(Integer value) {
            addCriterion("operation_permission_weight <", value, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightLessThanOrEqualTo(Integer value) {
            addCriterion("operation_permission_weight <=", value, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightIn(List<Integer> values) {
            addCriterion("operation_permission_weight in", values, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightNotIn(List<Integer> values) {
            addCriterion("operation_permission_weight not in", values, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightBetween(Integer value1, Integer value2) {
            addCriterion("operation_permission_weight between", value1, value2, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andOperationPermissionWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("operation_permission_weight not between", value1, value2, "operationPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightIsNull() {
            addCriterion("biz_permission_weight is null");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightIsNotNull() {
            addCriterion("biz_permission_weight is not null");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightEqualTo(Long value) {
            addCriterion("biz_permission_weight =", value, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightNotEqualTo(Long value) {
            addCriterion("biz_permission_weight <>", value, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightGreaterThan(Long value) {
            addCriterion("biz_permission_weight >", value, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_permission_weight >=", value, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightLessThan(Long value) {
            addCriterion("biz_permission_weight <", value, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightLessThanOrEqualTo(Long value) {
            addCriterion("biz_permission_weight <=", value, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightIn(List<Long> values) {
            addCriterion("biz_permission_weight in", values, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightNotIn(List<Long> values) {
            addCriterion("biz_permission_weight not in", values, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightBetween(Long value1, Long value2) {
            addCriterion("biz_permission_weight between", value1, value2, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andBizPermissionWeightNotBetween(Long value1, Long value2) {
            addCriterion("biz_permission_weight not between", value1, value2, "bizPermissionWeight");
            return (Criteria) this;
        }

        public Criteria andRejectToStepIsNull() {
            addCriterion("reject_to_step is null");
            return (Criteria) this;
        }

        public Criteria andRejectToStepIsNotNull() {
            addCriterion("reject_to_step is not null");
            return (Criteria) this;
        }

        public Criteria andRejectToStepEqualTo(String value) {
            addCriterion("reject_to_step =", value, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepNotEqualTo(String value) {
            addCriterion("reject_to_step <>", value, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepGreaterThan(String value) {
            addCriterion("reject_to_step >", value, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepGreaterThanOrEqualTo(String value) {
            addCriterion("reject_to_step >=", value, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepLessThan(String value) {
            addCriterion("reject_to_step <", value, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepLessThanOrEqualTo(String value) {
            addCriterion("reject_to_step <=", value, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepIn(List<String> values) {
            addCriterion("reject_to_step in", values, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepNotIn(List<String> values) {
            addCriterion("reject_to_step not in", values, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepBetween(String value1, String value2) {
            addCriterion("reject_to_step between", value1, value2, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectToStepNotBetween(String value1, String value2) {
            addCriterion("reject_to_step not between", value1, value2, "rejectToStep");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeIsNull() {
            addCriterion("reject_notify_type is null");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeIsNotNull() {
            addCriterion("reject_notify_type is not null");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeEqualTo(Integer value) {
            addCriterion("reject_notify_type =", value, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeNotEqualTo(Integer value) {
            addCriterion("reject_notify_type <>", value, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeGreaterThan(Integer value) {
            addCriterion("reject_notify_type >", value, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("reject_notify_type >=", value, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeLessThan(Integer value) {
            addCriterion("reject_notify_type <", value, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("reject_notify_type <=", value, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeIn(List<Integer> values) {
            addCriterion("reject_notify_type in", values, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeNotIn(List<Integer> values) {
            addCriterion("reject_notify_type not in", values, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeBetween(Integer value1, Integer value2) {
            addCriterion("reject_notify_type between", value1, value2, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("reject_notify_type not between", value1, value2, "rejectNotifyType");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcIsNull() {
            addCriterion("reject_email_cc is null");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcIsNotNull() {
            addCriterion("reject_email_cc is not null");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcEqualTo(String value) {
            addCriterion("reject_email_cc =", value, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcNotEqualTo(String value) {
            addCriterion("reject_email_cc <>", value, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcGreaterThan(String value) {
            addCriterion("reject_email_cc >", value, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcGreaterThanOrEqualTo(String value) {
            addCriterion("reject_email_cc >=", value, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcLessThan(String value) {
            addCriterion("reject_email_cc <", value, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcLessThanOrEqualTo(String value) {
            addCriterion("reject_email_cc <=", value, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcIn(List<String> values) {
            addCriterion("reject_email_cc in", values, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcNotIn(List<String> values) {
            addCriterion("reject_email_cc not in", values, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcBetween(String value1, String value2) {
            addCriterion("reject_email_cc between", value1, value2, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectEmailCcNotBetween(String value1, String value2) {
            addCriterion("reject_email_cc not between", value1, value2, "rejectEmailCc");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateIsNull() {
            addCriterion("reject_notify_template is null");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateIsNotNull() {
            addCriterion("reject_notify_template is not null");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateEqualTo(String value) {
            addCriterion("reject_notify_template =", value, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateNotEqualTo(String value) {
            addCriterion("reject_notify_template <>", value, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateGreaterThan(String value) {
            addCriterion("reject_notify_template >", value, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateGreaterThanOrEqualTo(String value) {
            addCriterion("reject_notify_template >=", value, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateLessThan(String value) {
            addCriterion("reject_notify_template <", value, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateLessThanOrEqualTo(String value) {
            addCriterion("reject_notify_template <=", value, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateIn(List<String> values) {
            addCriterion("reject_notify_template in", values, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateNotIn(List<String> values) {
            addCriterion("reject_notify_template not in", values, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateBetween(String value1, String value2) {
            addCriterion("reject_notify_template between", value1, value2, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectNotifyTemplateNotBetween(String value1, String value2) {
            addCriterion("reject_notify_template not between", value1, value2, "rejectNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeIsNull() {
            addCriterion("todo_notify_type is null");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeIsNotNull() {
            addCriterion("todo_notify_type is not null");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeEqualTo(Integer value) {
            addCriterion("todo_notify_type =", value, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeNotEqualTo(Integer value) {
            addCriterion("todo_notify_type <>", value, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeGreaterThan(Integer value) {
            addCriterion("todo_notify_type >", value, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("todo_notify_type >=", value, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeLessThan(Integer value) {
            addCriterion("todo_notify_type <", value, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("todo_notify_type <=", value, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeIn(List<Integer> values) {
            addCriterion("todo_notify_type in", values, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeNotIn(List<Integer> values) {
            addCriterion("todo_notify_type not in", values, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeBetween(Integer value1, Integer value2) {
            addCriterion("todo_notify_type between", value1, value2, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("todo_notify_type not between", value1, value2, "todoNotifyType");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcIsNull() {
            addCriterion("todo_email_cc is null");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcIsNotNull() {
            addCriterion("todo_email_cc is not null");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcEqualTo(String value) {
            addCriterion("todo_email_cc =", value, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcNotEqualTo(String value) {
            addCriterion("todo_email_cc <>", value, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcGreaterThan(String value) {
            addCriterion("todo_email_cc >", value, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcGreaterThanOrEqualTo(String value) {
            addCriterion("todo_email_cc >=", value, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcLessThan(String value) {
            addCriterion("todo_email_cc <", value, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcLessThanOrEqualTo(String value) {
            addCriterion("todo_email_cc <=", value, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcIn(List<String> values) {
            addCriterion("todo_email_cc in", values, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcNotIn(List<String> values) {
            addCriterion("todo_email_cc not in", values, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcBetween(String value1, String value2) {
            addCriterion("todo_email_cc between", value1, value2, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoEmailCcNotBetween(String value1, String value2) {
            addCriterion("todo_email_cc not between", value1, value2, "todoEmailCc");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateIsNull() {
            addCriterion("todo_notify_template is null");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateIsNotNull() {
            addCriterion("todo_notify_template is not null");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateEqualTo(String value) {
            addCriterion("todo_notify_template =", value, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateNotEqualTo(String value) {
            addCriterion("todo_notify_template <>", value, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateGreaterThan(String value) {
            addCriterion("todo_notify_template >", value, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateGreaterThanOrEqualTo(String value) {
            addCriterion("todo_notify_template >=", value, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateLessThan(String value) {
            addCriterion("todo_notify_template <", value, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateLessThanOrEqualTo(String value) {
            addCriterion("todo_notify_template <=", value, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateIn(List<String> values) {
            addCriterion("todo_notify_template in", values, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateNotIn(List<String> values) {
            addCriterion("todo_notify_template not in", values, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateBetween(String value1, String value2) {
            addCriterion("todo_notify_template between", value1, value2, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoNotifyTemplateNotBetween(String value1, String value2) {
            addCriterion("todo_notify_template not between", value1, value2, "todoNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeIsNull() {
            addCriterion("pre_recall_notify_type is null");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeIsNotNull() {
            addCriterion("pre_recall_notify_type is not null");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeEqualTo(Integer value) {
            addCriterion("pre_recall_notify_type =", value, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeNotEqualTo(Integer value) {
            addCriterion("pre_recall_notify_type <>", value, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeGreaterThan(Integer value) {
            addCriterion("pre_recall_notify_type >", value, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("pre_recall_notify_type >=", value, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeLessThan(Integer value) {
            addCriterion("pre_recall_notify_type <", value, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("pre_recall_notify_type <=", value, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeIn(List<Integer> values) {
            addCriterion("pre_recall_notify_type in", values, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeNotIn(List<Integer> values) {
            addCriterion("pre_recall_notify_type not in", values, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeBetween(Integer value1, Integer value2) {
            addCriterion("pre_recall_notify_type between", value1, value2, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallNotifyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("pre_recall_notify_type not between", value1, value2, "preRecallNotifyType");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcIsNull() {
            addCriterion("pre_recall_email_cc is null");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcIsNotNull() {
            addCriterion("pre_recall_email_cc is not null");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcEqualTo(String value) {
            addCriterion("pre_recall_email_cc =", value, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcNotEqualTo(String value) {
            addCriterion("pre_recall_email_cc <>", value, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcGreaterThan(String value) {
            addCriterion("pre_recall_email_cc >", value, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcGreaterThanOrEqualTo(String value) {
            addCriterion("pre_recall_email_cc >=", value, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcLessThan(String value) {
            addCriterion("pre_recall_email_cc <", value, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcLessThanOrEqualTo(String value) {
            addCriterion("pre_recall_email_cc <=", value, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcIn(List<String> values) {
            addCriterion("pre_recall_email_cc in", values, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcNotIn(List<String> values) {
            addCriterion("pre_recall_email_cc not in", values, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcBetween(String value1, String value2) {
            addCriterion("pre_recall_email_cc between", value1, value2, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailCcNotBetween(String value1, String value2) {
            addCriterion("pre_recall_email_cc not between", value1, value2, "preRecallEmailCc");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempIsNull() {
            addCriterion("pre_recall_email_temp is null");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempIsNotNull() {
            addCriterion("pre_recall_email_temp is not null");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempEqualTo(String value) {
            addCriterion("pre_recall_email_temp =", value, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempNotEqualTo(String value) {
            addCriterion("pre_recall_email_temp <>", value, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempGreaterThan(String value) {
            addCriterion("pre_recall_email_temp >", value, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempGreaterThanOrEqualTo(String value) {
            addCriterion("pre_recall_email_temp >=", value, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempLessThan(String value) {
            addCriterion("pre_recall_email_temp <", value, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempLessThanOrEqualTo(String value) {
            addCriterion("pre_recall_email_temp <=", value, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempIn(List<String> values) {
            addCriterion("pre_recall_email_temp in", values, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempNotIn(List<String> values) {
            addCriterion("pre_recall_email_temp not in", values, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempBetween(String value1, String value2) {
            addCriterion("pre_recall_email_temp between", value1, value2, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallEmailTempNotBetween(String value1, String value2) {
            addCriterion("pre_recall_email_temp not between", value1, value2, "preRecallEmailTemp");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeIsNull() {
            addCriterion("finish_rate_type is null");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeIsNotNull() {
            addCriterion("finish_rate_type is not null");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeEqualTo(Integer value) {
            addCriterion("finish_rate_type =", value, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeNotEqualTo(Integer value) {
            addCriterion("finish_rate_type <>", value, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeGreaterThan(Integer value) {
            addCriterion("finish_rate_type >", value, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("finish_rate_type >=", value, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeLessThan(Integer value) {
            addCriterion("finish_rate_type <", value, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("finish_rate_type <=", value, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeIn(List<Integer> values) {
            addCriterion("finish_rate_type in", values, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeNotIn(List<Integer> values) {
            addCriterion("finish_rate_type not in", values, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeBetween(Integer value1, Integer value2) {
            addCriterion("finish_rate_type between", value1, value2, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("finish_rate_type not between", value1, value2, "finishRateType");
            return (Criteria) this;
        }

        public Criteria andFinishRateIsNull() {
            addCriterion("finish_rate is null");
            return (Criteria) this;
        }

        public Criteria andFinishRateIsNotNull() {
            addCriterion("finish_rate is not null");
            return (Criteria) this;
        }

        public Criteria andFinishRateEqualTo(Double value) {
            addCriterion("finish_rate =", value, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateNotEqualTo(Double value) {
            addCriterion("finish_rate <>", value, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateGreaterThan(Double value) {
            addCriterion("finish_rate >", value, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateGreaterThanOrEqualTo(Double value) {
            addCriterion("finish_rate >=", value, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateLessThan(Double value) {
            addCriterion("finish_rate <", value, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateLessThanOrEqualTo(Double value) {
            addCriterion("finish_rate <=", value, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateIn(List<Double> values) {
            addCriterion("finish_rate in", values, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateNotIn(List<Double> values) {
            addCriterion("finish_rate not in", values, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateBetween(Double value1, Double value2) {
            addCriterion("finish_rate between", value1, value2, "finishRate");
            return (Criteria) this;
        }

        public Criteria andFinishRateNotBetween(Double value1, Double value2) {
            addCriterion("finish_rate not between", value1, value2, "finishRate");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNull() {
            addCriterion("ext_property1 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNotNull() {
            addCriterion("ext_property1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1EqualTo(String value) {
            addCriterion("ext_property1 =", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotEqualTo(String value) {
            addCriterion("ext_property1 <>", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThan(String value) {
            addCriterion("ext_property1 >", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property1 >=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThan(String value) {
            addCriterion("ext_property1 <", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThanOrEqualTo(String value) {
            addCriterion("ext_property1 <=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1In(List<String> values) {
            addCriterion("ext_property1 in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotIn(List<String> values) {
            addCriterion("ext_property1 not in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1Between(String value1, String value2) {
            addCriterion("ext_property1 between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotBetween(String value1, String value2) {
            addCriterion("ext_property1 not between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNull() {
            addCriterion("ext_property2 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNotNull() {
            addCriterion("ext_property2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2EqualTo(String value) {
            addCriterion("ext_property2 =", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotEqualTo(String value) {
            addCriterion("ext_property2 <>", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThan(String value) {
            addCriterion("ext_property2 >", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property2 >=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThan(String value) {
            addCriterion("ext_property2 <", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThanOrEqualTo(String value) {
            addCriterion("ext_property2 <=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2In(List<String> values) {
            addCriterion("ext_property2 in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotIn(List<String> values) {
            addCriterion("ext_property2 not in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2Between(String value1, String value2) {
            addCriterion("ext_property2 between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotBetween(String value1, String value2) {
            addCriterion("ext_property2 not between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNull() {
            addCriterion("ext_property3 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNotNull() {
            addCriterion("ext_property3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3EqualTo(String value) {
            addCriterion("ext_property3 =", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotEqualTo(String value) {
            addCriterion("ext_property3 <>", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThan(String value) {
            addCriterion("ext_property3 >", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property3 >=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThan(String value) {
            addCriterion("ext_property3 <", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThanOrEqualTo(String value) {
            addCriterion("ext_property3 <=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3In(List<String> values) {
            addCriterion("ext_property3 in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotIn(List<String> values) {
            addCriterion("ext_property3 not in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3Between(String value1, String value2) {
            addCriterion("ext_property3 between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotBetween(String value1, String value2) {
            addCriterion("ext_property3 not between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNull() {
            addCriterion("ext_property4 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNotNull() {
            addCriterion("ext_property4 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4EqualTo(String value) {
            addCriterion("ext_property4 =", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotEqualTo(String value) {
            addCriterion("ext_property4 <>", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThan(String value) {
            addCriterion("ext_property4 >", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property4 >=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThan(String value) {
            addCriterion("ext_property4 <", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThanOrEqualTo(String value) {
            addCriterion("ext_property4 <=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4In(List<String> values) {
            addCriterion("ext_property4 in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotIn(List<String> values) {
            addCriterion("ext_property4 not in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4Between(String value1, String value2) {
            addCriterion("ext_property4 between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotBetween(String value1, String value2) {
            addCriterion("ext_property4 not between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty5IsNull() {
            addCriterion("ext_property5 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty5IsNotNull() {
            addCriterion("ext_property5 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty5EqualTo(String value) {
            addCriterion("ext_property5 =", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotEqualTo(String value) {
            addCriterion("ext_property5 <>", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5GreaterThan(String value) {
            addCriterion("ext_property5 >", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property5 >=", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5LessThan(String value) {
            addCriterion("ext_property5 <", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5LessThanOrEqualTo(String value) {
            addCriterion("ext_property5 <=", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5In(List<String> values) {
            addCriterion("ext_property5 in", values, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotIn(List<String> values) {
            addCriterion("ext_property5 not in", values, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5Between(String value1, String value2) {
            addCriterion("ext_property5 between", value1, value2, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotBetween(String value1, String value2) {
            addCriterion("ext_property5 not between", value1, value2, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty6IsNull() {
            addCriterion("ext_property6 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty6IsNotNull() {
            addCriterion("ext_property6 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty6EqualTo(String value) {
            addCriterion("ext_property6 =", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6NotEqualTo(String value) {
            addCriterion("ext_property6 <>", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6GreaterThan(String value) {
            addCriterion("ext_property6 >", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property6 >=", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6LessThan(String value) {
            addCriterion("ext_property6 <", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6LessThanOrEqualTo(String value) {
            addCriterion("ext_property6 <=", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6In(List<String> values) {
            addCriterion("ext_property6 in", values, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6NotIn(List<String> values) {
            addCriterion("ext_property6 not in", values, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6Between(String value1, String value2) {
            addCriterion("ext_property6 between", value1, value2, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6NotBetween(String value1, String value2) {
            addCriterion("ext_property6 not between", value1, value2, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty7IsNull() {
            addCriterion("ext_property7 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty7IsNotNull() {
            addCriterion("ext_property7 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty7EqualTo(String value) {
            addCriterion("ext_property7 =", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7NotEqualTo(String value) {
            addCriterion("ext_property7 <>", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7GreaterThan(String value) {
            addCriterion("ext_property7 >", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property7 >=", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7LessThan(String value) {
            addCriterion("ext_property7 <", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7LessThanOrEqualTo(String value) {
            addCriterion("ext_property7 <=", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7In(List<String> values) {
            addCriterion("ext_property7 in", values, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7NotIn(List<String> values) {
            addCriterion("ext_property7 not in", values, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7Between(String value1, String value2) {
            addCriterion("ext_property7 between", value1, value2, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7NotBetween(String value1, String value2) {
            addCriterion("ext_property7 not between", value1, value2, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty8IsNull() {
            addCriterion("ext_property8 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty8IsNotNull() {
            addCriterion("ext_property8 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty8EqualTo(String value) {
            addCriterion("ext_property8 =", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8NotEqualTo(String value) {
            addCriterion("ext_property8 <>", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8GreaterThan(String value) {
            addCriterion("ext_property8 >", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property8 >=", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8LessThan(String value) {
            addCriterion("ext_property8 <", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8LessThanOrEqualTo(String value) {
            addCriterion("ext_property8 <=", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8In(List<String> values) {
            addCriterion("ext_property8 in", values, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8NotIn(List<String> values) {
            addCriterion("ext_property8 not in", values, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8Between(String value1, String value2) {
            addCriterion("ext_property8 between", value1, value2, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8NotBetween(String value1, String value2) {
            addCriterion("ext_property8 not between", value1, value2, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty9IsNull() {
            addCriterion("ext_property9 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty9IsNotNull() {
            addCriterion("ext_property9 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty9EqualTo(String value) {
            addCriterion("ext_property9 =", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9NotEqualTo(String value) {
            addCriterion("ext_property9 <>", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9GreaterThan(String value) {
            addCriterion("ext_property9 >", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property9 >=", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9LessThan(String value) {
            addCriterion("ext_property9 <", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9LessThanOrEqualTo(String value) {
            addCriterion("ext_property9 <=", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9In(List<String> values) {
            addCriterion("ext_property9 in", values, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9NotIn(List<String> values) {
            addCriterion("ext_property9 not in", values, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9Between(String value1, String value2) {
            addCriterion("ext_property9 between", value1, value2, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9NotBetween(String value1, String value2) {
            addCriterion("ext_property9 not between", value1, value2, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andSortNumbIsNull() {
            addCriterion("sort_numb is null");
            return (Criteria) this;
        }

        public Criteria andSortNumbIsNotNull() {
            addCriterion("sort_numb is not null");
            return (Criteria) this;
        }

        public Criteria andSortNumbEqualTo(Double value) {
            addCriterion("sort_numb =", value, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbNotEqualTo(Double value) {
            addCriterion("sort_numb <>", value, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbGreaterThan(Double value) {
            addCriterion("sort_numb >", value, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbGreaterThanOrEqualTo(Double value) {
            addCriterion("sort_numb >=", value, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbLessThan(Double value) {
            addCriterion("sort_numb <", value, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbLessThanOrEqualTo(Double value) {
            addCriterion("sort_numb <=", value, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbIn(List<Double> values) {
            addCriterion("sort_numb in", values, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbNotIn(List<Double> values) {
            addCriterion("sort_numb not in", values, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbBetween(Double value1, Double value2) {
            addCriterion("sort_numb between", value1, value2, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andSortNumbNotBetween(Double value1, Double value2) {
            addCriterion("sort_numb not between", value1, value2, "sortNumb");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateIsNull() {
            addCriterion("reject_sms_notify_template is null");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateIsNotNull() {
            addCriterion("reject_sms_notify_template is not null");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateEqualTo(String value) {
            addCriterion("reject_sms_notify_template =", value, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateNotEqualTo(String value) {
            addCriterion("reject_sms_notify_template <>", value, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateGreaterThan(String value) {
            addCriterion("reject_sms_notify_template >", value, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateGreaterThanOrEqualTo(String value) {
            addCriterion("reject_sms_notify_template >=", value, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateLessThan(String value) {
            addCriterion("reject_sms_notify_template <", value, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateLessThanOrEqualTo(String value) {
            addCriterion("reject_sms_notify_template <=", value, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateIn(List<String> values) {
            addCriterion("reject_sms_notify_template in", values, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateNotIn(List<String> values) {
            addCriterion("reject_sms_notify_template not in", values, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateBetween(String value1, String value2) {
            addCriterion("reject_sms_notify_template between", value1, value2, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andRejectSmsNotifyTemplateNotBetween(String value1, String value2) {
            addCriterion("reject_sms_notify_template not between", value1, value2, "rejectSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateIsNull() {
            addCriterion("todo_sms_notify_template is null");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateIsNotNull() {
            addCriterion("todo_sms_notify_template is not null");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateEqualTo(String value) {
            addCriterion("todo_sms_notify_template =", value, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateNotEqualTo(String value) {
            addCriterion("todo_sms_notify_template <>", value, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateGreaterThan(String value) {
            addCriterion("todo_sms_notify_template >", value, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateGreaterThanOrEqualTo(String value) {
            addCriterion("todo_sms_notify_template >=", value, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateLessThan(String value) {
            addCriterion("todo_sms_notify_template <", value, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateLessThanOrEqualTo(String value) {
            addCriterion("todo_sms_notify_template <=", value, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateIn(List<String> values) {
            addCriterion("todo_sms_notify_template in", values, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateNotIn(List<String> values) {
            addCriterion("todo_sms_notify_template not in", values, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateBetween(String value1, String value2) {
            addCriterion("todo_sms_notify_template between", value1, value2, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andTodoSmsNotifyTemplateNotBetween(String value1, String value2) {
            addCriterion("todo_sms_notify_template not between", value1, value2, "todoSmsNotifyTemplate");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempIsNull() {
            addCriterion("pre_recall_sms_temp is null");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempIsNotNull() {
            addCriterion("pre_recall_sms_temp is not null");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempEqualTo(String value) {
            addCriterion("pre_recall_sms_temp =", value, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempNotEqualTo(String value) {
            addCriterion("pre_recall_sms_temp <>", value, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempGreaterThan(String value) {
            addCriterion("pre_recall_sms_temp >", value, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempGreaterThanOrEqualTo(String value) {
            addCriterion("pre_recall_sms_temp >=", value, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempLessThan(String value) {
            addCriterion("pre_recall_sms_temp <", value, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempLessThanOrEqualTo(String value) {
            addCriterion("pre_recall_sms_temp <=", value, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempIn(List<String> values) {
            addCriterion("pre_recall_sms_temp in", values, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempNotIn(List<String> values) {
            addCriterion("pre_recall_sms_temp not in", values, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempBetween(String value1, String value2) {
            addCriterion("pre_recall_sms_temp between", value1, value2, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andPreRecallSmsTempNotBetween(String value1, String value2) {
            addCriterion("pre_recall_sms_temp not between", value1, value2, "preRecallSmsTemp");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagIsNull() {
            addCriterion("predefined_flag is null");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagIsNotNull() {
            addCriterion("predefined_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagEqualTo(Integer value) {
            addCriterion("predefined_flag =", value, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagNotEqualTo(Integer value) {
            addCriterion("predefined_flag <>", value, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagGreaterThan(Integer value) {
            addCriterion("predefined_flag >", value, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("predefined_flag >=", value, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagLessThan(Integer value) {
            addCriterion("predefined_flag <", value, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagLessThanOrEqualTo(Integer value) {
            addCriterion("predefined_flag <=", value, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagIn(List<Integer> values) {
            addCriterion("predefined_flag in", values, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagNotIn(List<Integer> values) {
            addCriterion("predefined_flag not in", values, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagBetween(Integer value1, Integer value2) {
            addCriterion("predefined_flag between", value1, value2, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andPredefinedFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("predefined_flag not between", value1, value2, "predefinedFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Long value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Long value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Long value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Long value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Long value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Long value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Long> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Long> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Long value1, Long value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Long value1, Long value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
