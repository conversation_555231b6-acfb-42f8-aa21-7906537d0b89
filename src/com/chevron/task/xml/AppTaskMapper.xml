<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.task.dao.AppTaskDao">
    <resultMap id="SubTaskMap" type="com.chevron.task.model.app.SubTask">
        <id column="task_id" jdbcType="BIGINT" property="subTaskId"/>
        <result column="task_main_id" jdbcType="BIGINT" property="taskMainId"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime"/>
        <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime"/>
        <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser"/>
        <result column="task_create_user_display_name" jdbcType="VARCHAR" property="taskCreateUserDisplayName"/>
        <result column="work_shop_id" jdbcType="BIGINT" property="workShopId"/>
        <result column="work_shop_code" jdbcType="VARCHAR" property="workShopCode"/>
        <result column="work_shop_name" jdbcType="VARCHAR" property="workShopName"/>
        <result column="work_shop_address" jdbcType="VARCHAR" property="workShopAddress"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="task_priority" jdbcType="VARCHAR" property="priority"/>
        <result column="exec_user" jdbcType="BIGINT" property="executeUserId"/>
        <result column="exec_user_name" jdbcType="VARCHAR" property="executeUserName"/>

        <!-- add by bo.liu 2017 01 19 start 针对门店入库确认任务类型新增 -->
        <result column="in_stock_no" jdbcType="VARCHAR" property="inStockNo"/>
        <result column="out_stock_no" jdbcType="VARCHAR" property="outStockNo"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <!-- add by bo.liu 2017 01 19 end 针对门店入库确认任务类型新增  -->
        <result column="work_shop_range" property="workShopRange" jdbcType="NUMERIC"/>
        <result column="tmb_type_name" jdbcType="VARCHAR" property="tmbTypeName"/>
        <result column="workshop_photo_id" jdbcType="BIGINT" property="workshopPhotoId"/>
        <result column="workshop_area" jdbcType="VARCHAR" property="workshopArea"/>
        <result column="workshop_type" jdbcType="VARCHAR" property="workshopType"/>
        <result column="scale" jdbcType="VARCHAR" property="scale"/>
        <result column="workshop_status" jdbcType="VARCHAR" property="workshopStatus"/>
        <result column="fleet_name" jdbcType="VARCHAR" property="fleetName"/>
        <result column="attribute1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="attribute2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="attribute3" jdbcType="VARCHAR" property="attribute3"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_address" jdbcType="VARCHAR" property="projectAddress"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="customer_type" property="customerType" jdbcType="INTEGER"/>
    </resultMap>

    <select id="queryPendingVisitSubTaskForPage" parameterType="com.chevron.task.model.SubtaskParams"
            resultMap="SubTaskMap">
        SELECT tt.* FROM (
        SELECT
        wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wts.org_id,
        wts.brand,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user,
        wtu.ch_name as task_create_user_display_name,
        wws.id AS work_shop_id,
        <!-- wws.work_shop_code, -->
        wws.work_shop_name,
        wws.work_shop_address,
        wws.longitude,
        wws.latitude,
        wws.ext_property15 as scale,
        wws.status workshop_status,
        wtm.task_priority,
        wts.exec_user,
        wtm.in_stock_no,
        wtm.out_stock_no,
        wtm.order_no,
        att_file.att_id workshop_photo_id,
        <!--  wws.area workshop_area, -->
        wws.type workshop_type,
        wws.customer_type,
        <choose>
            <when test="longitude == null">
                null work_shop_range,
            </when>
            <otherwise>
                dbo.fun_cal_distance(${longitude}, ${latitude}, wws.longitude, wws.latitude) work_shop_range,
            </otherwise>
        </choose>
        (select u.ch_name from wx_t_user u where u.user_id = wts.exec_user) as exec_user_name
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        LEFT JOIN wx_att_file att_file ON wws.photo_id = att_file.uuid
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE
        wts.task_status in
        <foreach item="taskStatus" index="index" collection="taskStatusList" open="(" separator="," close=")">
            ${taskStatus}
        </foreach>
        <if test="execUser != null and execUser != ''">
            AND wts.exec_user = #{execUser}
        </if>
        AND wtm.tmb_type_code in
        ('TT_2_CLZX','TT_2_XD_CAI','TT_2_FLEET','TT_2_FLEET_ZF','TT_2_CDYJZM','TT_2_7ne/','TT_2_7nf/')
        <choose>
            <when test="shopOrFleetType != null">
                and wws.customer_type = #{shopOrFleetType}
            </when>
            <otherwise>
                and wws.customer_type &amp; 3 > 0
            </otherwise>
        </choose>

        <if test="workshopStatusArray != null">
            and wws.status in
            <foreach item="item" index="index" collection="workshopStatusArray" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <!-- add by bo.liu 1122 -->
        <if test="workshopNoGdTask == 'Y'.toString()">
            AND wts.org_id NOT IN(SELECT t_gd_shop.workshop_id FROM wx_task_has_gd_workshop t_gd_shop)
        </if>
        <if test="spId != null and spId != ''">
            and wts.exec_user in (select sp_user.user_id from wx_t_user sp_user where sp_user.org_id = #{spId} )
        </if>
        <if test="startDateFrom != null">
            and wtm.task_start_time &gt;= #{startDateFrom, jdbcType=TIMESTAMP}
        </if>
        <if test="startDateTo != null">
            and wtm.task_start_time &lt; DATEADD(DAY, 1, #{startDateTo, jdbcType=TIMESTAMP})
        </if>
        <if test="finishDateFrom != null">
            and wtm.task_finish_time &gt;= #{finishDateFrom, jdbcType=TIMESTAMP}
        </if>
        <if test="finishDateTo != null">
            and wtm.task_finish_time &lt; DATEADD(DAY, 1, #{finishDateTo, jdbcType=TIMESTAMP})
        </if>
        <if test="regionId != null">
            and wws.region_id=#{regionId}
        </if>
        <if test="keyword != null and keyword != ''">
            and (wtm.task_name like '%' + #{keyword} + '%' or wws.work_shop_name like '%' + #{keyword} + '%')
        </if>
        <if test="brand != null and brand != ''">
            and wts.brand = #{brand}
        </if>
        ) tt WHERE 1=1
        <if test="taskTypeList != null">
            and tt.tmb_type_code in
            <foreach item="taskType" index="index" collection="taskTypeList" open="(" separator="," close=")">
                '${taskType}'
            </foreach>
        </if>
    </select>

    <select id="querySubTaskForPage" parameterType="com.chevron.task.model.SubtaskParams" resultMap="SubTaskMap">
        SELECT
        wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wts.org_id,
        wts.attribute1,
        wts.attribute2,
        wts.attribute3,
        wts.brand,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user,
        wtu.ch_name as task_create_user_display_name,
        wws.id AS work_shop_id,
        <!-- wws.work_shop_code, -->
        wws.work_shop_name,
        wws.work_shop_address,
        wws.customer_type,
        wws.longitude,
        wws.latitude,
        <!-- wws.scale, -->
        wws.status workshop_status,
        wtm.task_priority,
        wts.exec_user,
        wtm.in_stock_no,
        wtm.out_stock_no,
        wtm.order_no,
        att_file.att_id workshop_photo_id,
        <!-- wws.area workshop_area, -->
        wws.type workshop_type,
        wws.work_shop_name as fleet_name,
        wws.work_shop_name as project_name,
        wws.work_shop_address as project_address,
        wws.type as project_type,
        <!--  wtfi.fleet_name,
         pc.project_name,
         pc.project_address,
         pc.project_type, -->
        <choose>
            <when test="longitude == null">
                null work_shop_range,
            </when>
            <otherwise>
                dbo.fun_cal_distance(${longitude}, ${latitude}, wws.longitude, wws.latitude) work_shop_range,
                <!-- 	<choose>
                        <when test='taskTypeList  != null and taskTypeList.contains("TT_2_FLEET_ZF")'>
                            dbo.fun_cal_distance(${longitude}, ${latitude}, wtfi.longitude, wtfi.latitude) work_shop_range,
                        </when>
                        <when test='taskTypeList  != null and taskTypeList.contains("TT_2_PROJECT_CLIENT_VISIT")'>
                            dbo.fun_cal_distance(${longitude}, ${latitude}, pc.longitude, pc.latitude) work_shop_range,
                        </when>
                        <otherwise>
                            dbo.fun_cal_distance(${longitude}, ${latitude}, wws.longitude, wws.latitude) work_shop_range,
                        </otherwise>
                    </choose> -->
            </otherwise>
        </choose>
        (select u.ch_name from wx_t_user u where u.user_id = wts.exec_user) as exec_user_name
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        LEFT JOIN wx_att_file att_file ON wws.photo_id = att_file.uuid
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        <!--  LEFT JOIN wx_t_fleet_info wtfi ON wts.org_id = wtfi.id
         LEFT JOIN wx_t_project_client pc ON wts.org_id = pc.id -->
        WHERE (wws.id is null or wws.delete_flag=0)
        <choose>
            <when test='taskTypeList  != null and (taskTypeList.contains("TT_2_CDYJZM") or taskTypeList.contains("TT_2_PROJECT_CLIENT_YJZM") or taskTypeList.contains("TT_2_7nk/"))'>
                and ( (ISNULL(wts.attribute1,'') != '' and wts.attribute2 = '4' ) or
                wts.task_status in
                <foreach item="taskStatus" index="index" collection="taskStatusList" open="(" separator=","
                         close=")">
                    ${taskStatus}
                </foreach>
                )
            </when>
            <otherwise>
                and wts.task_status in
                <foreach item="taskStatus" index="index" collection="taskStatusList" open="(" separator="," close=")">
                    ${taskStatus}
                </foreach>
            </otherwise>
        </choose>
        <if test="execUser != null and execUser != ''">
            AND wts.exec_user = #{execUser}
        </if>
        <if test="taskTypeList != null">
            and wtm.tmb_type_code in
            <foreach item="taskType" index="index" collection="taskTypeList" open="(" separator="," close=")">
                '${taskType}'
            </foreach>
        </if>
        <if test="workshopStatusArray != null">
            and wws.status in
            <foreach item="item" index="index" collection="workshopStatusArray" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <!-- add by bo.liu 1122 -->
        <if test="workshopNoGdTask == 'Y'.toString()">
            AND wts.org_id NOT IN(SELECT t_gd_shop.workshop_id FROM wx_task_has_gd_workshop t_gd_shop)
        </if>
        <if test="spId != null and spId != ''">
            and wts.exec_user in (select sp_user.user_id from wx_t_user sp_user where sp_user.org_id = #{spId} )
        </if>
        <if test="startDateFrom != null">
            and wtm.task_start_time &gt;= #{startDateFrom, jdbcType=TIMESTAMP}
        </if>
        <if test="startDateTo != null">
            and wtm.task_start_time &lt; DATEADD(DAY, 1, #{startDateTo, jdbcType=TIMESTAMP})
        </if>
        <if test="finishDateFrom != null">
            and wtm.task_finish_time &gt;= #{finishDateFrom, jdbcType=TIMESTAMP}
        </if>
        <if test="finishDateTo != null">
            and wtm.task_finish_time &lt; DATEADD(DAY, 1, #{finishDateTo, jdbcType=TIMESTAMP})
        </if>
        <if test="regionId != null">
            and wws.region_id=#{regionId}
        </if>
        <if test="keyword != null and keyword != ''">
            and (wtm.task_name like '%' + #{keyword} + '%' or wws.work_shop_name like '%' + #{keyword} + '%')
        </if>
        <if test="brand != null and brand != ''">
            and wts.brand = #{brand}
        </if>
    </select>

    <select id="querySubTask" parameterType="map" resultMap="SubTaskMap">
        SELECT
        wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wts.attribute1,
        wts.attribute2,
        wts.attribute3,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user,
        wtu.ch_name as task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wtm.in_stock_no,
        wtm.out_stock_no,
        wtm.order_no,
        (select u.ch_name from wx_t_user u where u.user_id = wts.exec_user) as exec_user_name
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE
        wts.task_status in
        <foreach item="taskStatus" index="index" collection="taskStatusList" open="(" separator="," close=")">
            #{taskStatus}
        </foreach>
        <if test="execUser != null and execUser != ''">
            AND wts.exec_user = #{execUser}
        </if>
        <if test="spId != null and spId != ''">
            and wts.exec_user in (select sp_user.user_id from wx_t_user sp_user where sp_user.org_id = #{spId} )
        </if>
        <if test="taskTypeList != null">
            and wtm.tmb_type_code in
            <foreach item="taskType" index="index" collection="taskTypeList" open="(" separator="," close=")">
                #{taskType}
            </foreach>
        </if>
        <if test="workshopNoGdTask == 'Y'.toString()">
            and not exists (select 1 from wx_task_sub wts2 join wx_task_main wtm2 on wtm2.task_main_id =
            wts2.task_main_id where wts2.org_id = wts.org_id and wtm2.tmb_type_code = 'TT_2_LD')
        </if>

        AND (wtm.task_start_time between #{startDateFrom, jdbcType=TIMESTAMP} and #{startDateTo, jdbcType=TIMESTAMP}
        or wtm.task_finish_time between #{finishDateFrom, jdbcType=TIMESTAMP} and #{finishDateTo, jdbcType=TIMESTAMP})
        order by wtm.task_priority desc, wtm.task_finish_time desc
    </select>
    <select id="querySubTaskByParams" parameterType="map" resultMap="SubTaskMap">
        SELECT
        wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wts.xg_sj task_finish_time,
        wtm.task_create_user,
        wtu.ch_name as task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wtm.in_stock_no,
        wtm.out_stock_no,
        wtm.order_no,
        (select u.ch_name from wx_t_user u where u.user_id = wts.exec_user) as exec_user_name
        <if test="includeTaskTypeName">
            ,(select d.data_name from wx_t_data d where d.data_code=wtm.tmb_type_code) as tmb_type_name
        </if>
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE 1=1
        <if test="taskStatusList != null">
            and wts.task_status in
            <foreach item="taskStatus" index="index" collection="taskStatusList" open="(" separator="," close=")">
                #{taskStatus}
            </foreach>
        </if>
        <if test="taskStatus != null">
            and wts.task_status=#{taskStatus}
        </if>
        <if test="execUser != null and execUser != ''">
            AND wts.exec_user = #{execUser}
        </if>
        <if test="spId != null and spId != ''">
            and wts.exec_user in (select sp_user.user_id from wx_t_user sp_user where sp_user.org_id = #{spId} )
        </if>
        <if test="taskTypeList != null">
            and wtm.tmb_type_code in
            <foreach item="taskType" index="index" collection="taskTypeList" open="(" separator="," close=")">
                #{taskType}
            </foreach>
        </if>
        <if test="finishStartTime != null">
            and wts.xg_sj>=#{finishStartTime, jdbcType=TIMESTAMP}
        </if>
        <if test="finishEndTime != null">
            and wts.xg_sj&lt;#{finishEndTime, jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="querySubTaskBySubTaskId" parameterType="map" resultMap="SubTaskMap">
        SELECT
        wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user,
        wtu.ch_name as task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        (select u.ch_name from wx_t_user u where u.user_id = wts.exec_user) as exec_user_name
        FROM wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE wts.task_id = #{subTaskId}
    </select>

    <select id="queryWorkShopLastTaskFeedback" parameterType="map" resultType="java.lang.String">
        select top 1 t.remark
        from wx_task_instance_check t
        join wx_task_sub wts on t.sub_task_id = wts.task_id
        join wx_task_check wtc on t.check_id = wtc.check_id
        where 1=1
        and wts.org_id = #{workShopId, jdbcType=BIGINT}
        <!-- QUESTION_FEEDBACK represent feedback -->
        and wtc.check_code = 'QUESTION_FEEDBACK'
        <!-- less than current sub task id -->
        and t.sub_task_id &lt; #{subTaskId, jdbcType=BIGINT}
        order by t.id desc
    </select>
    <select id="queryWorkShopLastTaskCheckResult" parameterType="map" resultType="java.lang.String">
        select top 1 sub.check_result
        from wx_task_sub sub join wx_task_main main on sub.task_main_id = main.task_main_id
        where sub.org_id = #{workShopId, jdbcType=BIGINT}
        and sub.is_pass is not null
        and main.tmb_type_code = #{tmbTypeCode}
        order by sub.task_id desc
    </select>


    <!-- 我的巡店任务历史 -->
    <resultMap id="MyXDTaskMap" type="com.chevron.task.model.app.MyXDTask">
        <id column="task_id" jdbcType="BIGINT" property="subTaskId"/>
        <result column="task_main_id" jdbcType="BIGINT" property="taskMainId"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime"/>
        <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime"/>
        <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser"/>
        <result column="task_create_user_display_name" jdbcType="VARCHAR" property="taskCreateUserDisplayName"/>
        <result column="work_shop_id" jdbcType="BIGINT" property="workShopId"/>
        <result column="work_shop_code" jdbcType="VARCHAR" property="workShopCode"/>
        <result column="work_shop_name" jdbcType="VARCHAR" property="workShopName"/>
        <result column="work_shop_address" jdbcType="VARCHAR" property="workShopAddress"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="task_priority" jdbcType="VARCHAR" property="priority"/>
    </resultMap>
    <select id="queryMyXDTaskList" parameterType="map" resultMap="MyXDTaskMap">
        SELECT TOP ${pageCount} *
        FROM
        (SELECT * ,
        row_number() over(order by t.task_id desc) AS rownumber
        FROM
        (SELECT wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wtm.tmb_type_code,
        wws.work_shop_name+'-'+wtm.task_name task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user, wtu.ch_name AS task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wtm.in_stock_no, wtm.out_stock_no, wtm.order_no,
        (SELECT u.ch_name
        FROM wx_t_user u
        WHERE u.user_id = wts.exec_user) AS exec_user_name
        FROM wx_task_sub wts
        LEFT JOIN wx_t_work_shop wws
        ON wts.org_id = wws.id
        JOIN wx_task_main wtm
        ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu
        ON wtm.task_create_user = wtu.user_id
        WHERE wtm.tmb_type_code='TT_2_XD'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId} ) t ) t2
        WHERE rownumber>#{startPage, jdbcType=INTEGER}
    </select>


    <select id="countMyXDTaskList" parameterType="map" resultType="Long">
        SELECT
        count(1)
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE
        wtm.tmb_type_code='TT_2_XD'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId}
    </select>


    <!-- 我的扫店任务历史 -->
    <resultMap id="MySDTaskMap" type="com.chevron.task.model.app.MySDTask">
        <id column="task_id" jdbcType="BIGINT" property="subTaskId"/>
        <result column="task_main_id" jdbcType="BIGINT" property="taskMainId"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime"/>
        <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser"/>
        <result column="task_create_user_display_name" jdbcType="VARCHAR" property="taskCreateUserDisplayName"/>
        <result column="work_shop_id" jdbcType="BIGINT" property="workShopId"/>
        <result column="work_shop_code" jdbcType="VARCHAR" property="workShopCode"/>
        <result column="work_shop_name" jdbcType="VARCHAR" property="workShopName"/>
        <result column="work_shop_address" jdbcType="VARCHAR" property="workShopAddress"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="task_priority" jdbcType="VARCHAR" property="priority"/>
        <result column="work_shop_type" jdbcType="VARCHAR" property="workShopType"/>
        <result column="work_shop_scale" jdbcType="VARCHAR" property="workShopScale"/>
        <result column="is_pass" jdbcType="VARCHAR" property="taskIsPass"/>
        <result column="check_result" jdbcType="VARCHAR" property="taskCheckRemark"/>
        <result column="workshop_photo_id" jdbcType="BIGINT" property="workshopPhotoId"/>
        <result column="fleet_name" jdbcType="VARCHAR" property="fleetName"/>
        <result column="attribute3" jdbcType="VARCHAR" property="attribute3"/>
        <result column="attribute2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
    </resultMap>
    <select id="queryMySDTaskList" parameterType="map" resultMap="MySDTaskMap">
        SELECT TOP ${pageCount} *
        FROM
        (SELECT * ,
        row_number() over(order by t.task_id desc) AS rownumber
        FROM
        (SELECT wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wtm.tmb_type_code,
        wws.work_shop_name+'-'+wtm.task_name task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user, wtu.ch_name AS task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.type work_shop_type,
        wws.scale work_shop_scale,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wts.is_pass,
        wts.check_result,
        wtm.in_stock_no, wtm.out_stock_no, wtm.order_no,
        (SELECT u.ch_name
        FROM wx_t_user u
        WHERE u.user_id = wts.exec_user) AS exec_user_name
        FROM wx_task_sub wts
        LEFT JOIN wx_t_work_shop wws
        ON wts.org_id = wws.id
        JOIN wx_task_main wtm
        ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu
        ON wtm.task_create_user = wtu.user_id
        WHERE wtm.tmb_type_code='TT_2_SD'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId} ) t ) t2
        WHERE rownumber>#{startPage, jdbcType=INTEGER}
    </select>

    <select id="queryForPage" parameterType="com.chevron.pms.model.TaskParams" resultMap="MySDTaskMap">
        SELECT wts.task_id,
        wts.task_main_id,
        wts.task_status,
        case when wts.task_status=4 then wts.xg_sj else null end complete_time,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user, wtu.ch_name AS task_create_user_display_name,
        wws.id AS work_shop_id,
        <!--  wws.work_shop_code, -->
        wws.work_shop_name,
        wws.work_shop_address,
        wws.type work_shop_type,
        wws.ext_property15 work_shop_scale,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wts.is_pass,
        wts.attribute3,
        wts.attribute2,
        wts.org_id,
        wts.check_result,
        att_file.att_id workshop_photo_id,
        wws.work_shop_name fleet_name,
        wtm.in_stock_no, wtm.out_stock_no, wtm.order_no,
        (SELECT u.ch_name
        FROM wx_t_user u
        WHERE u.user_id = wts.exec_user) AS exec_user_name
        FROM wx_task_sub wts
        LEFT JOIN wx_t_work_shop wws
        ON wts.org_id = wws.id
        LEFT JOIN wx_att_file att_file ON wws.photo_id = att_file.uuid
        JOIN wx_task_main wtm
        ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu
        ON wtm.task_create_user = wtu.user_id
        <!-- LEFT JOIN wx_t_fleet_info wtfi ON wts.org_id = wtfi.id AND wtm.tmb_type_code IN('TT_2_FLEET','TT_2_FLEET_ZF','TT_2_CDYJZM') -->
        WHERE 1=1

        <if test="taskTypeCode == 'TT_2_FEELING_GOODS'">
            AND EXISTS (SELECT 1 FROM wx_t_Main_Local_marketing lm where 1 = 1
            AND lm.id = wts.org_id
            <if test="startTime != null">AND lm.create_time &gt; = #{startTime}</if>
            <if test="endTime != null">AND lm.create_time &lt; = #{endTime}</if>
            )
        </if>

        <if test="executeUserId != null">
            AND (
            (wtm.excute_user_id = #{executeUserId})
            <if test="userRole!= null">
                <choose>
                    <when test="userRole == 8">
                        OR(EXISTS(
                        SELECT 1 FROM
                        wx_t_user u
                        LEFT JOIN wx_t_partner_o2o_enterprise o ON u.org_id = o.partner_id
                        LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON cos.distributor_id =
                        o.distributor_id
                        where 1 = 1 AND u.user_id = wtm.excute_user_id AND cos.sales_cai = (SELECT cai FROM wx_t_user
                        where user_id = #{executeUserId}) )
                        )
                    </when>
                </choose>
            </if>
            )
        </if>
        <if test="taskTypeCode != null and taskTypeCode != ''">
            and wtm.tmb_type_code=#{taskTypeCode}
        </if>
        <if test="taskStatusArray != null">
            AND wtm.task_status in
            <foreach collection="taskStatusArray" index="index" item="item" separator=",">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    ${item}
                </trim>
            </foreach>
        </if>
        <if test="brand != null">
            and wts.brand&amp;#{brand}>0
        </if>
    </select>


    <select id="queryShopOrFleetForPage" parameterType="com.chevron.pms.model.TaskParams" resultMap="MySDTaskMap">
        SELECT tt.* FROM(
        SELECT wts.task_id,
        wts.task_main_id,
        wts.task_status,
        case when wts.task_status=4 then wts.xg_sj else null end complete_time,
        wtm.tmb_type_code,
        wtm.task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user, wtu.ch_name AS task_create_user_display_name,
        wws.id AS work_shop_id,
        <!--  wws.work_shop_code, -->
        wws.work_shop_name,
        wws.work_shop_address,
        wws.type work_shop_type,
        wws.business_weight,
        wws.ext_property15 work_shop_scale,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wts.is_pass,
        wts.check_result,
        att_file.att_id workshop_photo_id,
        wtm.in_stock_no, wtm.out_stock_no, wtm.order_no,
        (SELECT u.ch_name
        FROM wx_t_user u
        WHERE u.user_id = wts.exec_user) AS exec_user_name
        FROM wx_task_sub wts
        LEFT JOIN wx_t_work_shop wws
        ON wts.org_id = wws.id
        LEFT JOIN wx_att_file att_file ON wws.photo_id = att_file.uuid
        JOIN wx_task_main wtm
        ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu
        ON wtm.task_create_user = wtu.user_id
        WHERE 1=1
        <!-- AND wtm.tmb_type_code in ('TT_2_CLZX','TT_2_XD_CAI','TT_2_FLEET','TT_2_FLEET_ZF','TT_2_CDYJZM','TT_2_7ne/','TT_2_7nf/') -->
        AND wtm.tmb_type_code in ('TT_2_CDYJZM','TT_2_7ne/','TT_2_7nf/')
        <if test="executeUserId != null">
            AND wtm.excute_user_id = #{executeUserId}
        </if>
        <if test="taskTypeCode != null and taskTypeCode != ''">
            and wtm.tmb_type_code=#{taskTypeCode}
        </if>
        ) tt WHERE 1=1
        <if test="taskTypeList != null">
            and tt.tmb_type_code in
            <foreach item="taskType" index="index" collection="taskTypeList" open="(" separator="," close=")">
                '${taskType}'
            </foreach>
        </if>
        <if test="taskStatusArray != null">
            AND tt.task_status in
            <foreach collection="taskStatusArray" index="index" item="item" separator=",">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    ${item}
                </trim>
            </foreach>
        </if>
        <if test="brand != null">
            and tt.business_weight=#{brand}
        </if>
    </select>

    <select id="countMySDTaskList" parameterType="map" resultType="Long">
        SELECT
        count(1)
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE
        wtm.tmb_type_code='TT_2_SD'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId}
    </select>


    <!-- 我的攻店任务历史 -->
    <resultMap id="MyGDTaskMap" type="com.chevron.task.model.app.MyGDTask">
        <id column="task_id" jdbcType="BIGINT" property="subTaskId"/>
        <result column="task_main_id" jdbcType="BIGINT" property="taskMainId"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime"/>
        <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime"/>
        <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser"/>
        <result column="task_create_user_display_name" jdbcType="VARCHAR" property="taskCreateUserDisplayName"/>
        <result column="work_shop_id" jdbcType="BIGINT" property="workShopId"/>
        <result column="work_shop_code" jdbcType="VARCHAR" property="workShopCode"/>
        <result column="work_shop_name" jdbcType="VARCHAR" property="workShopName"/>
        <result column="work_shop_address" jdbcType="VARCHAR" property="workShopAddress"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="task_priority" jdbcType="VARCHAR" property="priority"/>
        <result column="att_id" jdbcType="BIGINT" property="workShopIconId"/>
        <result column="access_count" jdbcType="INTEGER" property="workShopAccessCount"/>
    </resultMap>
    <select id="queryMyGDTaskList" parameterType="map" resultMap="MyGDTaskMap">
        SELECT TOP ${pageCount} *
        FROM
        (SELECT * ,
        row_number() over(order by t.task_id desc) AS rownumber
        FROM
        (SELECT wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wtm.tmb_type_code,
        wws.work_shop_name+'-'+wtm.task_name task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user, wtu.ch_name AS task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.type work_shop_type,
        wws.scale work_shop_scale,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wts.is_pass,
        wts.check_result,
        wtm.in_stock_no, wtm.out_stock_no, wtm.order_no,
        (SELECT u.ch_name
        FROM wx_t_user u
        WHERE u.user_id = wts.exec_user) AS exec_user_name,
        (SELECT
        count(*)
        FROM
        wx_t_task_exec_trace
        WHERE
        work_shop_id = wws.id
        AND
        sub_task_id = wts.task_id) AS access_count,
        tt_file.storage_name,
        tt_file.att_id
        FROM wx_task_sub wts
        LEFT JOIN wx_t_work_shop wws
        ON wts.org_id = wws.id
        JOIN wx_task_main wtm
        ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu
        ON wtm.task_create_user = wtu.user_id
        JOIN wx_att_file tt_file
        ON tt_file.uuid=wws.photo_id
        WHERE
        tt_file.storage_name IS NOT NULL
        <!--  AND tt_file.file_type ='image/jpeg'  -->
        AND wtm.tmb_type_code='TT_2_LD'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId} ) t ) t2
        WHERE rownumber>#{startPage, jdbcType=INTEGER}
    </select>


    <select id="countMyGDTaskList" parameterType="map" resultType="Long">
        SELECT
        count(1)
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        join wx_t_user wtu on wtm.task_create_user = wtu.user_id
        JOIN wx_att_file tt_file ON tt_file.uuid=wws.photo_id
        WHERE
        tt_file.storage_name IS NOT NULL
        <!--  AND tt_file.file_type ='image/jpeg' -->
        AND wtm.tmb_type_code='TT_2_LD'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId}
    </select>


    <!-- 我的攻店任务历史 -->
    <resultMap id="MySITaskMap" type="com.chevron.task.model.app.MySITask">
        <id column="task_id" jdbcType="BIGINT" property="subTaskId"/>
        <result column="task_main_id" jdbcType="BIGINT" property="taskMainId"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode"/>
        <result column="task_description" jdbcType="VARCHAR" property="taskDescription"/>
        <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime"/>
        <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime"/>
        <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser"/>
        <result column="task_create_user_display_name" jdbcType="VARCHAR" property="taskCreateUserDisplayName"/>
        <result column="work_shop_id" jdbcType="BIGINT" property="workShopId"/>
        <result column="work_shop_code" jdbcType="VARCHAR" property="workShopCode"/>
        <result column="work_shop_name" jdbcType="VARCHAR" property="workShopName"/>
        <result column="work_shop_address" jdbcType="VARCHAR" property="workShopAddress"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="in_stock_no" jdbcType="VARCHAR" property="inStockNo"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
    </resultMap>
    <select id="queryMySITaskList" parameterType="map" resultMap="MySITaskMap">
        SELECT TOP ${pageCount} *
        FROM
        (SELECT * ,
        row_number() over(order by t.task_id desc) AS rownumber
        FROM
        (SELECT wts.task_id,
        wts.task_main_id,
        wts.task_status,
        wtm.tmb_type_code,
        wws.work_shop_name+'-'+wtm.task_name task_name,
        wtm.task_description,
        wtm.task_start_time,
        wtm.task_finish_time,
        wtm.task_create_user, wtu.ch_name AS task_create_user_display_name,
        wws.id AS work_shop_id,
        wws.work_shop_code,
        wws.work_shop_name,
        wws.work_shop_address,
        wws.type work_shop_type,
        wws.scale work_shop_scale,
        wws.longitude,
        wws.latitude,
        wtm.task_priority,
        wts.exec_user,
        wts.is_pass,
        wts.check_result,
        wtm.in_stock_no, wtm.out_stock_no, wtm.order_no,
        (SELECT u.ch_name
        FROM wx_t_user u
        WHERE u.user_id = wts.exec_user) AS exec_user_name
        FROM wx_task_sub wts
        LEFT JOIN wx_t_work_shop wws
        ON wts.org_id = wws.id
        JOIN wx_task_main wtm
        ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu
        ON wtm.task_create_user = wtu.user_id
        WHERE
        wtm.tmb_type_code='TT_2_SI'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId} ) t ) t2
        WHERE rownumber>#{startPage, jdbcType=INTEGER}
    </select>


    <select id="countMySITaskList" parameterType="map" resultType="Long">
        SELECT
        count(1)
        FROM
        wx_task_sub wts
        left JOIN wx_t_work_shop wws ON wts.org_id = wws.id
        JOIN wx_task_main wtm ON wts.task_main_id = wtm.task_main_id
        JOIN wx_t_user wtu on wtm.task_create_user = wtu.user_id
        WHERE
        wtm.tmb_type_code='TT_2_SI'
        AND wtm.task_status = '4'
        AND wtm.excute_user_id = #{userId}
    </select>

    <sql id="xd_table">
        SELECT
        COUNT (tt.t) tNum,
        tt.t
        FROM
        (
        SELECT
        tt.*, ROW_NUMBER () OVER (
        partition BY tt.org_id
        ORDER BY
        tt.t
        ) rowNum
        FROM
        (
        SELECT DISTINCT
        CONVERT (nvarchar(10), s.xg_sj, 102) t,
        s.org_id,ws.customer_type type
        FROM
        wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        LEFT JOIN wx_t_work_shop ws ON s.org_id = ws.id
        WHERE
        <!-- w.tmb_type_code = 'TT_2_XD_CAI' -->
        w.tmb_type_code in ('TT_2_XD_CAI','TT_2_FLEET_ZF')
        AND ws.status != -3
        AND s.task_status = 4
        AND w.excute_user_id = #{orgId, jdbcType=BIGINT}
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        <!-- UNION ALL
        SELECT DISTINCT
            CONVERT (nvarchar(10), s.xg_sj, 102) t,
            s.org_id,2 type
        FROM
            wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        LEFT JOIN wx_t_fleet_info wf ON s.org_id = wf.id
        WHERE
            w.tmb_type_code = 'TT_2_FLEET_ZF'
        AND w.excute_user_id = #{orgId, jdbcType=BIGINT}
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        AND wf.status != -3
        AND s.task_status = 4 -->
        ) tt
        ) tt
        WHERE
        tt.rowNum &lt;= (SELECT DISTINCT code FROM wx_t_properties WHERE codetype = 'mon_air_days')
        GROUP BY
        tt.t
    </sql>

    <select id="getEffectiveVisitingDays" parameterType="map" resultType="String">
        SELECT
        xdDays.t
        FROM
        (
        <include refid="xd_table"/>
        ) xdDays
        WHERE
        xdDays.tNum >= (select code from wx_t_properties where codetype = 'aimTimes')
    </select>

    <select id="getVisitingDaysNum" parameterType="map" resultType="java.lang.Long">
        SELECT
        COUNT (tNum)
        FROM
        (
        <include refid="xd_table"/>
        ) xdDays
        WHERE
        xdDays.tNum >= (select code from wx_t_properties where codetype = 'aimTimes')
    </select>

    <resultMap id="taskKPIMap" type="com.chevron.task.model.app.TaskKPI">
        <result column="num1" jdbcType="BIGINT" property="num1"/>
        <result column="num2" jdbcType="BIGINT" property="num2"/>
        <result column="num3" jdbcType="BIGINT" property="num3"/>
    </resultMap>

    <select id="getNewWorkShopNum" parameterType="map" resultMap="taskKPIMap">
        SELECT
        *
        FROM
        (
        SELECT
        sdTable.exec_user num1,
        sdTable.num2,
        ROW_NUMBER () OVER (
        ORDER BY
        sdTable.num2 DESC,
        sdTable.max_time ASC
        ) AS num3
        FROM
        (
        SELECT tt.exec_user,COUNT(tt.exec_user) num2, MAX(tt.xg_sj) max_time FROM (
        SELECT DISTINCT
        s.xg_sj,
        s.exec_user,
        s.org_id,
        p.customer_type type
        FROM
        wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        LEFT JOIN wx_t_work_shop p ON p.id = s.org_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = s.tenant_id
        LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON crss.distributor_id = pe.distributor_id
        WHERE
        <!-- w.tmb_type_code = 'TT_2_CLZX' -->
        w.tmb_type_code IN ('TT_2_CLZX','TT_2_FLEET')
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        AND p.status = '3'
        AND crss.region_name = (
        SELECT DISTINCT
        crss.region_name
        FROM
        wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = s.tenant_id
        LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON crss.distributor_id = pe.distributor_id
        LEFT JOIN dw_region_sales_channel_rel rsc ON rsc.region_name = crss.region_name
        WHERE
        w.tmb_type_code IN ('TT_2_CLZX','TT_2_FLEET')
        <!-- 	w.tmb_type_code = 'TT_2_CLZX' -->
        AND w.excute_user_id = #{orgId, jdbcType=BIGINT}
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        <!--  AND crss.region_name LIKE <![CDATA['C&I%']]> -->
        AND exists (select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' and
        vtm1.value_after_transform=rsc.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I%']]>,
        'CDM'))
        )
        <!-- UNION ALL
        SELECT DISTINCT
            s.xg_sj,
            s.exec_user,
            s.org_id,
            2 type
        FROM
            wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        LEFT JOIN wx_t_fleet_info wf ON wf.id = s.org_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = s.tenant_id
        LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON crss.distributor_id = pe.distributor_id
        WHERE
            w.tmb_type_code = 'TT_2_FLEET'
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        AND wf.status = '3'
        AND crss.region_name = (
            SELECT DISTINCT
                crss.region_name
            FROM
                wx_task_main w
            LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
            LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = s.tenant_id
            LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON crss.distributor_id = pe.distributor_id
            LEFT JOIN dw_region_sales_channel_rel rsc ON rsc.region_name = crss.region_name
            WHERE
                w.tmb_type_code = 'TT_2_FLEET'
            AND w.excute_user_id = #{orgId, jdbcType=BIGINT}
            AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
            AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
            AND exists (select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' and vtm1.value_after_transform=rsc.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I%']]>, 'CDM'))

         )-->
        ) tt
        GROUP BY tt.exec_user
        ) sdTable
        ) res_table
        WHERE
        num3 = (select code from wx_t_properties where codetype = 'rankNum')
        OR num1 = #{orgId, jdbcType=BIGINT}
    </select>

    <select id="getProofOfPerformance" parameterType="map" resultMap="taskKPIMap">
        SELECT
        num1, num2,num1-num2 num3
        FROM
        (
        SELECT
        COUNT (1) num1
        FROM
        (
        SELECT
        exec_user,
        check_evaluation
        FROM
        wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        WHERE
        w.tmb_type_code = 'TT_2_CDYJZM'
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        AND w.excute_user_id = #{orgId, jdbcType=BIGINT}
        AND s.task_status = 4

        ) t1
        ) t12,
        (
        SELECT
        COUNT(1) num2
        FROM
        (
        SELECT
        exec_user,
        check_evaluation
        FROM
        wx_task_main w
        LEFT JOIN wx_task_sub s ON w.task_main_id = s.task_main_id
        WHERE
        w.tmb_type_code = 'TT_2_CDYJZM'
        AND s.xg_sj &gt;= #{startDate, jdbcType=NVARCHAR}
        AND s.xg_sj &lt; #{endDate, jdbcType=NVARCHAR}
        AND w.excute_user_id = #{orgId, jdbcType=BIGINT}
        AND s.check_evaluation > 0
        AND s.task_status = 4
        ) t2
        ) t21
    </select>

    <!-- 德乐巡店门店/车队分页列表 -->
    <resultMap type="com.chevron.task.model.app.ShopOrFleetParam" id="ShopOrFleetMap">
        <result column="region_id" jdbcType="BIGINT" property="regionId"/>
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="status" jdbcType="BIGINT" property="status"/>
        <result column="excute_user_id" jdbcType="BIGINT" property="excuteUserId"/>
        <result column="shopOrFleetType" jdbcType="BIGINT" property="shopOrFleetType"/>
        <result column="volitional_type" jdbcType="BIGINT" property="volitionalType"/>
        <result column="task_id" jdbcType="BIGINT" property="taskSubId"/>
        <result column="task_main_id" jdbcType="BIGINT" property="taskMainId"/>
        <result column="is_pass" jdbcType="BIGINT" property="isPass"/>
        <result column="att_id" property="attId" jdbcType="BIGINT"/>
        <result column="partnerId" jdbcType="BIGINT" property="partnerId"/>
        <result column="from_source" jdbcType="BIGINT" property="fromSource"/>
        <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode"/>
        <result column="longitude" jdbcType="NUMERIC" property="longitude"/>
        <result column="latitude" jdbcType="NUMERIC" property="latitude"/>
        <result column="shopOrFleetName" jdbcType="VARCHAR" property="shopOrFleetName"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="photo_id" jdbcType="VARCHAR" property="photoId"/>
        <result column="region_name" jdbcType="VARCHAR" property="regionName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="excutor_name" jdbcType="VARCHAR" property="excutorName"/>
        <result column="relative_distance" jdbcType="VARCHAR" property="relativeDistance"/>
        <result column="contact_person" jdbcType="VARCHAR" property="contactPerson"/>
        <result column="contact_person_tel" jdbcType="VARCHAR" property="contactPersonTel"/>
        <result column="work_shop_address" jdbcType="VARCHAR" property="workShopAddress"/>
        <result column="work_shop_name" jdbcType="VARCHAR" property="workShopName"/>
        <result column="business_weight" property="businessWeight" jdbcType="INTEGER"/>
        <result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
        <result column="join_ck_plan" property="joinCkPlan" jdbcType="INTEGER"/>
        <result column="ext_flags_text" property="extFlagsText" jdbcType="VARCHAR"/>
        <result column="route" property="route" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getShopOrFleetParamByPage" parameterType="com.chevron.task.model.app.ShopOrFleetParam" resultMap="ShopOrFleetMap">
        select a.*, t4.ch_name as excutor_name
        from (
            select t.customer_type, t.id, t.work_shop_name shopOrFleetName, t.region_id, t.work_shop_address address,
                t.longitude, t.latitude, t.business_weight, t.create_time, t.update_time,
                case when t.delete_flag = 1 then 6 else t.status end status,
                t.type, t.photo_id,
                case when t.ext_property16='' then null else t.ext_property16 end volitional_type,
                (
                    select att_file.att_id
                    from wx_att_file att_file
                    where t.photo_id = att_file.uuid
                ) att_id,
                t_sub.task_id, t_sub.task_main_id, t_sub.tmb_type_code,t_sub.is_pass, t.partner_id partnerId,
                case when t.status = '0' then (
                    select max (task_main.excute_user_id)
                    from wx_task_main task_main
                    join wx_task_sub ts1 ON ts1.task_main_id = task_main.task_main_id
                    where ts1.org_id = t.id
                    and task_main.tmb_type_code = 'TT_2_SD'
                ) else t.excute_user_id end excute_user_id,
                t.from_source, t.contact_person, t.contact_person_tel, t.work_shop_address, t.work_shop_name, t.ext_flag,
                t.join_ck_plan, t.route, t.customer_type shopOrFleetType,
                <choose>
                    <when test="longitude == null">
                        null relative_distance
                    </when>
                    <otherwise>
                        dbo.fun_cal_distance(${longitude}, ${latitude}, t.longitude, t.latitude) relative_distance
                    </otherwise>
                </choose>
            from wx_t_work_shop t
            left join (
                select m1.tmb_type_code,s1.* from wx_task_sub s1
                left join wx_task_main m1 ON s1.task_main_id = m1.task_main_id where m1.tmb_type_code IN ('TT_2_7n3/', 'TT_2_7n7/')
            ) t_sub on t_sub.org_id = t.id and t.status=0 and (t.ext_property16 &amp; 1 = 0 or t_sub.is_pass=0) /*潜在客户 非意向合作或抽查不合格 可转合作*/
            where 1 = 1
            <include refid="getShopOrFleetWhere"/>
        ) a
        left join wx_t_user t4 on a.excute_user_id = t4.user_id
    </select>

    <select id="totalShopOrFleetParamForPage" parameterType="com.chevron.task.model.app.ShopOrFleetParam" resultType="long">
        select count(*)
        from wx_t_work_shop t
        where 1 = 1
        <include refid="getShopOrFleetWhere"/>
    </select>

    <sql id="getShopOrFleetWhere">
        <choose>
            <when test="deleteFlag == 2">
                and 1 = 1
            </when>
            <when test="deleteFlag == 1">
                and t.delete_flag = 1
            </when>
            <otherwise>
                and t.delete_flag = 0
            </otherwise>
        </choose>
        <if test="partnerId != null">
            and t.partner_id = #{partnerId,jdbcType=BIGINT}
        </if>
        <if test="amStatus != null">
            <foreach collection="amStatus" item="item" index="index" open=" and t.status in (" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        <if test="businessWeight != null">
            and t.business_weight &amp; #{businessWeight} > 0
        </if>
        <if test="regionId != null ">
            and t.region_id = #{regionId, jdbcType=BIGINT}
        </if>
        <if test="shopOrFleetName != null">
            and t.work_shop_name like '%'+ #{shopOrFleetName} +'%'
        </if>
        <if test="route != null and route != ''">
            and t.route = #{route}
        </if>
        <if test="fromSource != null and fromSource != ''">
            and t.from_source &amp; #{fromSource} &gt; 0
        </if>
        <choose>
            <when test="shopOrFleetType != null">
                and t.customer_type &amp; #{shopOrFleetType} > 0
            </when>
            <otherwise>
                and t.customer_type &amp; 3 > 0
            </otherwise>
        </choose>
    </sql>

    <resultMap id="BaseResultUserMap" type="com.sys.auth.model.WxTUser">
        <id column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="ch_name" property="chName" jdbcType="VARCHAR"/>
        <result column="cai" property="cai" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="org_id" property="orgId" jdbcType="BIGINT"/>
        <result column="login_name" property="loginName" jdbcType="VARCHAR"/>

        <result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR"/>
        <result column="fixed_tel" property="fixedTel" jdbcType="VARCHAR"/>
        <result column="photo_id" property="photoId" jdbcType="BIGINT"/>
        <result column="org_id" property="orgId" jdbcType="BIGINT"/>
        <result column="org_type" property="orgType" jdbcType="INTEGER"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="postion" property="postion" jdbcType="VARCHAR"/>
        <result column="postion_name" property="postionName" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName"/>
        <result column="user_intime" property="userIntime" jdbcType="TIMESTAMP"/>
        <result column="user_outtime" property="userOuttime" jdbcType="TIMESTAMP"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
        <result column="receive_msg" property="receiveMsg" jdbcType="INTEGER"/>
        <result column="post" property="post" jdbcType="BIGINT"/>
        <result column="is_valid" property="isValid" jdbcType="VARCHAR"/>
        <result column="xz_time" property="xzTime" jdbcType="TIMESTAMP"/>
        <result column="xz_user" property="xzUser" jdbcType="VARCHAR"/>
        <result column="xg_time" property="xgTime" jdbcType="TIMESTAMP"/>
        <result column="xg_user" property="xgUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>


    <select id="getFlsrUserById" resultMap="BaseResultUserMap">
        SELECT
        DISTINCT u.*
        FROM
        wx_t_user u
        LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON u.cai = cos.sales_cai
        LEFT JOIN wx_t_partner_o2o_enterprise o ON o.distributor_id = cos.distributor_id
        LEFT JOIN wx_t_user u1 ON u1.org_id = o.partner_id
        WHERE
        u1.user_id = #{dsrUserId}

    </select>

    <select id="getAsmUser" resultMap="BaseResultUserMap" parameterType="map">
        SELECT DISTINCT t.* FROM (
        <if test="userId != null">
            SELECT DISTINCT u.* FROM wx_t_user u
            LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON u.cai = cos.supervisor_cai
            LEFT JOIN wx_t_partner_o2o_enterprise o ON o.distributor_id = cos.distributor_id
            LEFT JOIN wx_t_user u1 ON u1.org_id = o.partner_id
            WHERE u1.user_id = #{userId}
            UNION ALL
            SELECT DISTINCT u.* FROM wx_t_user u
            LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON u.cai = cos.supervisor_cai
            where EXISTS (SELECT * FROM wx_t_user u where u.user_id = #{userId} AND u.cai = cos.sales_cai)
        </if>
        <if test="userId != null and cityRegionId != null">
            UNION ALL
        </if>
        <if test="cityRegionId != null">
            SELECT DISTINCT u.* FROM wx_t_user u
            LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON u.cai = cos.supervisor_cai
            LEFT JOIN wx_t_partner_o2o_enterprise o ON o.distributor_id = cos.distributor_id
            LEFT JOIN wx_t_region_partner p ON o.partner_id = p.partner_id
            LEFT JOIN wx_t_region r ON r.id = p.region_id
            WHERE 1 = 1 AND r.parent_id = #{cityRegionId}
        </if>

        ) t

    </select>

    <select id="getRegionName" resultType="String">
        SELECT top 1 t.* FROM (SELECT DISTINCT cos.region FROM PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos
        LEFT JOIN wx_t_partner_o2o_enterprise o ON o.distributor_id = cos.distributor_id
        LEFT JOIN wx_t_user u1 ON u1.org_id = o.partner_id
        WHERE u1.user_id = #{userId}
        UNION ALL
        SELECT DISTINCT cos.region FROM PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos
        where EXISTS (SELECT * FROM wx_t_user u where u.user_id = #{userId} AND u.cai = cos.sales_cai))t
    </select>

    <select id="getAvailableRoutes" resultType="java.lang.String" parameterType="string">
        select distinct route from wx_t_work_shop where delete_flag=0 and route_owner_account=#{loginName}  and status>=0 and route is not null order by route
    </select>
</mapper>