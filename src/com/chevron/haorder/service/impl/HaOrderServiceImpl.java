package com.chevron.haorder.service.impl;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.chevron.haorder.business.HaOrderBizService;
import com.chevron.haorder.controller.HaOrderController;
import com.chevron.haorder.dao.HaOrderApplyBatchVoMapper;
import com.chevron.haorder.dao.HaOrderLineVoMapper;
import com.chevron.haorder.dao.HaOrderProductPriceInfoVoMapper;
import com.chevron.haorder.dao.HaOrderUploadLineVoMapper;
import com.chevron.haorder.dao.HaOrderUploadVoMapper;
import com.chevron.haorder.dao.HaOrderVoMapper;
import com.chevron.haorder.model.HaOrderApplyBatchVo;
import com.chevron.haorder.model.HaOrderConditions;
import com.chevron.haorder.model.HaOrderLineVo;
import com.chevron.haorder.model.HaOrderProductPriceInfoVo;
import com.chevron.haorder.model.HaOrderUploadLineVo;
import com.chevron.haorder.model.HaOrderUploadVo;
import com.chevron.haorder.model.HaOrderVo;
import com.chevron.haorder.model.importvo.ImportHavolinePrices;
import com.chevron.haorder.model.importvo.ImportHavolineWarehouse;
import com.chevron.haorder.model.importvo.ImportPriceList;
import com.chevron.haorder.model.importvo.ImportTcp;
import com.chevron.haorder.model.importvo.NeedPpOrder;
import com.chevron.haorder.model.importvo.NeedUploadOrder;
import com.chevron.haorder.service.IHaOrderService;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.importdata.ImportDataUtil;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.dao.WxtOrderYYFWSetsVoMapper;
import com.chevron.pms.model.BaseParams;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.WxtOrderYYFWSetsVo;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.MessageResourceUtil;
import com.common.util.XxlsUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.email.service.EmailSenderService;

@Service
public class HaOrderServiceImpl  implements IHaOrderService {
	public final Logger logerr = Logger.getLogger(this.getClass());
	@Resource
	private HaOrderApplyBatchVoMapper haOrderApplyMapper;
	@Resource
	WxtOrderYYFWSetsVoMapper orderYYFWSetsMapper;
	@Resource
	private WxTPropertiesMapper propertiesMapper;
	
	@Resource
	private HaOrderProductPriceInfoVoMapper priceInfoMapper;
	@Resource
	private HaOrderUploadVoMapper haOrderUploadMapper;
	@Resource
	private HaOrderUploadLineVoMapper haOrderUploadLineMapper;
	@Resource
	private HaOrderVoMapper haOrderMapper;
	@Resource
	private HaOrderLineVoMapper haOrderLineMapper;
	@Resource
	PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	@Resource 
	EmailSenderService emailSenderService;
	@Resource 
	HaOrderBizService haOrderBizService;
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_CURRENT_ORDER_KEY = "currentOrder";
	public final static String RESULT_TOTALRECORD = "totalRecord";
	
	//天猫商城
	public static final String TM_PARTNER_NAME = "雪佛龙天猫旗舰店";
	//京东商城
	public static final String JD_PARTNER_NAME = "JD pop";
	
	public static final String TM_PARTNER_ID = "tmPartnerId";
	public static final String JD_PARTNER_ID = "jdPartnerId";
	public static final String TCP_TOATAL_TEXT = "总计";
	public static final String BATCH_ID = "bachId";
	public static final String WAREHOUSE_HAORDER_CODE = "WAREHOUSE_HAORDER_CODE";
	DecimalFormat   df  = new DecimalFormat("###########0.00");   
	public static final String HAORDER_REFUND_TYPE = "haorderrefund";
	public static final String HAORDER_CONFIRM_TYPE = "haorderconfirm";
	public String  special_product_netunitprice= "";
	public String  special_product_totalnetunitprice= "";
	public static String SPECIAL_PRODUCT_SKU="510725URE";
	
	
	@Override
	public Map<String, Object> importHAOrderBatchDataFromExcel(DiskFileItem dsiItem,int sheetIndex,Object obj,String[] models)
	{
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<ImportTcp> importOrderTcpLst = new ArrayList<ImportTcp>();
		try {
			    CommonsMultipartFile cf = new CommonsMultipartFile(dsiItem);
			    InputStream in = cf.getInputStream();
			 	System.out.println("importHAOrderBatchDataFromExcel:"+in);
			 	XxlsUtils xxls = new XxlsUtils();
			 	xxls.processOneSheet(in, sheetIndex,obj,models);
			 	resultMap.put("dataList", xxls.getList());
			
		} catch (Exception ex) {
				ex.printStackTrace();
				logerr.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
				return resultMap;

		}
			logerr.info("msg:ljc importHAOrderBatchDataFromExcel====================HA订单导入成功,sheetIndex:"+sheetIndex);
			resultMap.put("code", "success");
			resultMap.put("codeMsg", "导入成功,总数量,sheetIndex"+sheetIndex +":"+ importOrderTcpLst.size()+",");
			return resultMap;
	}
	
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> generateHAOrdersByMap(Map<String,Object> reqMap) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			/**
			 * 1.基础数据准备，登录用户，订单来源查询获取合伙人，当前日期
			 */
			Map<String,Object> baseDataMap = new HashMap<String,Object>();
			baseDataMap.put("currentUser", ContextUtil.getCurUser());
			baseDataMap.put("currentDate", new Date());
			
			
			/**
			 * 2.根据常量获取审批类型对应的模板信息，包含部署流程定义的key...当然可以直接指定的key,
			 */
			String pdKey =  HaOrderApplyBatchVo.APPLY_PRO_KEY; //暂时用常量代替
			baseDataMap.put("pdKey", pdKey);
			baseDataMap.put("pdName", HaOrderApplyBatchVo.APPLY_PRO_NAME);
			//暂时这样处理..
			//天猫合伙人
			reqMap.put("sourceName", TM_PARTNER_NAME);
			List<WxtOrderYYFWSetsVo>  lstYYFW = orderYYFWSetsMapper.queryOrderYYFWInfoByMap(reqMap);
			if(null==lstYYFW||lstYYFW.isEmpty())
			{
				throw new Exception("合伙人不存在或未配置："+TM_PARTNER_NAME);
			}
			
			baseDataMap.put(TM_PARTNER_NAME, lstYYFW.get(0));
			//京东合伙人
			reqMap.put("sourceName", JD_PARTNER_NAME);
			List<WxtOrderYYFWSetsVo>  lstYYFWJD = orderYYFWSetsMapper.queryOrderYYFWInfoByMap(reqMap);
			if(null==lstYYFWJD||lstYYFWJD.isEmpty())
			{
				throw new Exception("合伙人不存在或未配置："+JD_PARTNER_NAME);
			}
			baseDataMap.put(JD_PARTNER_NAME, lstYYFWJD.get(0));
			
			/**
			 * 3.录入模板批次信息，返回批次号
			 */
			HaOrderApplyBatchVo haOrderApply = generateHAOrderApplyBatchObj(baseDataMap);
			logerr.info("generateHAOrders haOrderApply:"+haOrderApply.getBatchid());
			baseDataMap.put(BATCH_ID, haOrderApply.getBatchid());
			
			/**
			 * 4.解析订单，并重组订单信息，//订单的唯一标识是，仓库名称？？
			 */
			List<HaOrderVo> lstAllHaOrderVo = new ArrayList<HaOrderVo>();//录入数据库表，用于后续PP页面维护
			List<HaOrderUploadVo> lstAllHaUploadOrderVo = new ArrayList<HaOrderUploadVo>();//录入数据库表，用于生成文件，上传到文件服务器
			List<HaOrderProductPriceInfoVo> lstAllHaOrderProductPriceInfoVo = new ArrayList<HaOrderProductPriceInfoVo>();//录入数据库表，用于邮件确认
			
			
			List<NeedPpOrder> lstAllNeedPpOrders = new ArrayList<NeedPpOrder>();
			List<NeedUploadOrder> lstAllNeedUploadOrders = new ArrayList<NeedUploadOrder>();
			
			//处理TCP sheet
			Map<String,Object> returnMap1 = processHAOrdersByTcpMap(reqMap);
			List<NeedPpOrder> lstTmpNeedPpOrder1  = (List<NeedPpOrder>) returnMap1.get("needPpOrderLst");
			if(null!=lstTmpNeedPpOrder1 && !lstTmpNeedPpOrder1.isEmpty())
			{
				lstAllNeedPpOrders.addAll(lstTmpNeedPpOrder1);
			}
			List<NeedUploadOrder> lstTmpNeedUploadOrder1  = (List<NeedUploadOrder>) returnMap1.get("needUploadOrderLst");
			if(null!=lstTmpNeedUploadOrder1 && !lstTmpNeedUploadOrder1.isEmpty())
			{
				lstAllNeedUploadOrders.addAll(lstTmpNeedUploadOrder1);
			}
			
			//处理haprice sheet
			Map<String,Object> returnMap2 = processHAOrdersByHaPriceMap(reqMap);
			List<NeedPpOrder> lstTmpNeedPpOrder2  = (List<NeedPpOrder>) returnMap2.get("needPpOrderLst2");
			if(null!=lstTmpNeedPpOrder2 && !lstTmpNeedPpOrder2.isEmpty())
			{
				lstAllNeedPpOrders.addAll(lstTmpNeedPpOrder2);
			}
			
			//处理hawarehouse sheet
			Map<String,Object> returnMap3 = processHAOrdersByHaWareHouseMap(reqMap);
			List<NeedUploadOrder> lstTmpNeedUploadOrder2  = (List<NeedUploadOrder>) returnMap3.get("needUploadOrderLst2");
			if(null!=lstTmpNeedUploadOrder2 && !lstTmpNeedUploadOrder2.isEmpty())
			{
				lstAllNeedUploadOrders.addAll(lstTmpNeedUploadOrder2);
			}
			
			//处理price List sheet 并生成目标价格列表,录入数据库中
			List<HaOrderProductPriceInfoVo> allPriceInfoList =  generateTargetPriceListByMap(reqMap,baseDataMap);
			
			
			//对维数据进行排序
			Map<String,Object> sortMap = sortOrder(lstAllNeedUploadOrders,lstAllNeedPpOrders);
			//生成目标HA pp订单
			Map<String,Object> targetHaOrderMap =  generateTargetHaOrdersByMap(sortMap,baseDataMap);
			List<HaOrderVo> lstHaOrder = (List<HaOrderVo>) targetHaOrderMap.get("lstHaorderVos");
			List<HaOrderLineVo> allHaOrderLineVosLst = (List<HaOrderLineVo>) targetHaOrderMap.get("allHaOrderLineVosLst");
			
			
			//生成目标上传到文件服务订单
			Map<String,Object> targetHaUploadOrderMap =  generateTargetHaUploadOrdersByMap(sortMap,baseDataMap);
			List<HaOrderUploadVo> lstUploadOrder = (List<HaOrderUploadVo>) targetHaUploadOrderMap.get("lstHaUploadOrders");
			List<HaOrderUploadLineVo> allHaUploadOrderLinesLst = (List<HaOrderUploadLineVo>) targetHaUploadOrderMap.get("allHaUploadOrderLinesLst");
			
			/**
			 * 5.批量录入订单信息
			 */
			boolean isSuccess = false;
		    isSuccess = insertHaPriceListInfo(allPriceInfoList);  
		    if(!isSuccess)
		    {
		    	throw new Exception("价格信息列表录入失败");
		    }
			
		    isSuccess = insertHaOrderList(lstHaOrder);  
		    if(!isSuccess)
		    {
		    	throw new Exception("Ha订单信息录入失败");
		    }
		    
		    isSuccess = insertHaOrderLineList(allHaOrderLineVosLst); 
		    if(!isSuccess)
		    {
		    	throw new Exception("Ha订单行信息==录入失败");
		    }
		    
		    
		    isSuccess = insertHaOrderUploadList(lstUploadOrder); 
		    if(!isSuccess)
		    {
		    	throw new Exception("生成文件上传到文件服务器的=HA订单=录入失败");
		    }
		    
		    isSuccess = insertHaOrderUploadLineList(allHaUploadOrderLinesLst); 
		    if(!isSuccess)
		    {
		    	throw new Exception("生成文件上传到文件服务器的==HA订单行==录入失败");
		    }
			
			/**
			 * 6.获取审批者1信息（需要发送邮件的) 通过邮箱配置和角色，，获取审批者2信息（邮箱信息，作为流程变量传递）  通过邮箱配置和角色
			 */
		    baseDataMap.put(HaOrderBizService.CHROLE_NAME, Constants.CHEVRON_PRICE_AUDIT);
			Map<String,Object> emailInfoMap = haOrderBizService.getSndEmailInfo(baseDataMap);
			if(null==emailInfoMap)
			{
				throw new Exception("审批者邮箱信息没有设置");
			}
			emailInfoMap.put("allPriceInfoList", allPriceInfoList);
			/**
			 * 7.根据部署key启动流程实例，设置流程变量applyKey 常量，  设置当前申请批次实体常量applyBatchObj,  设置appBatchId,
			 */
			    //设置流程变量  taskApprovers1（登录名）    设置流程变量taskApprovers2（登录名）    设置流程变量审批者2的实体
			    //TODO ........后续完善
			
			/**
			 * 8.发送邮件信息
			 */
			//quartz中使用此获取上下文
			ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
			emailInfoMap.put("msgcontent", "您有新的TM/JD的订单需要确认审批");
			boolean isSendResult = emailSenderService.sendEmailForCommon(context,
					(String[])emailInfoMap.get("accepters"), (String[])emailInfoMap.get("ccaccepters"), MyPropertyConfigurer.getVal("mail.ha.order.confirm.subject"),emailInfoMap, null, MyPropertyConfigurer.getVal("mail.ha.order.confirm.ftl"));
			resultMap.put("code", "success");
		}catch (Exception e) {
			logerr.error("generateHAOrders Exception:"+e.getLocalizedMessage());
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "操作接口错误信息："+e.getLocalizedMessage());
		}
		return resultMap;
	}
	

	

	/*private Map<String, Object> getSndEmailInfo(Map<String,Object> baseDataMap)
	{
		//组装邮件数据map
		Map<String,Object> dataMap = new HashMap<String,Object>();
		try
		{
			//批次号
			Long batchId = (Long) baseDataMap.get(BATCH_ID);
			//接收者
			String[] accepters = null;
			//邮件抄送者
			String[] ccaccepters = null;
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("funFlag", "order_confirm");
			List<String> orgNames = new ArrayList<String>();
			orgNames.add(OrderCommonVo.ORDER_SP[3]);
			orgNames.add(OrderCommonVo.ORDER_SP[4]);
			reqMap.put("organizationNames",orgNames);
			reqMap.put("userChRoleName",Constants.CHEVRON_PRICE_AUDIT);
			List<PartnerResponsibleVo> lstPartnerResponsibleVo =  partnerResponsibleVoMapper.queryEmailInfoForFWBOrderConfirm(reqMap);
			if(null==lstPartnerResponsibleVo || lstPartnerResponsibleVo.isEmpty())
			{
				logerr.info("getSndEmailInfo lstPartnerResponsibleVoInfo is null");
				return null;
			}
			logerr.info("accepters-----:");
			logerr.info("ccaccepters-----:");
			String acceptersAll = "";
			String ccacceptersAll = "";
			for(PartnerResponsibleVo partnerResponsibleVo:lstPartnerResponsibleVo)
			{
				String tmpAcept = partnerResponsibleVo.getResponsiblePersonEmail();
				String tmpCC=partnerResponsibleVo.getDayReportCc();
				if(!acceptersAll.contains(tmpAcept))
				{
					acceptersAll+= partnerResponsibleVo.getResponsiblePersonEmail()+";";
				}
				if(!ccacceptersAll.contains(tmpCC))
				{
					ccacceptersAll+= partnerResponsibleVo.getDayReportCc()+";";
				}
			}
			acceptersAll = acceptersAll.substring(0, acceptersAll.length()-1);
			ccacceptersAll = ccacceptersAll.substring(0, ccacceptersAll.length()-1);
			accepters = acceptersAll.split(";");
			ccaccepters = ccacceptersAll.split(";");
			logerr.info("accepters-----:"+accepters[0]);
			logerr.info("ccaccepters-----:"+ccaccepters[0]);
			
			
			String acceptname = accepters[0].substring(0, accepters[0].lastIndexOf("@"));
			if(accepters.length>1)
			{
				acceptname = "ALL";
			}
			dataMap.put("acceptUserName", acceptname);
			dataMap.put("msgcontent", "有新的Havoline订单需要确认审批！");
			HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
			String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
			String confirmLink = basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), HAORDER_CONFIRM_TYPE, "/workflow/flowHaOrderMyTask.jsp", "/workflow/flowHaOrderMyTask.jsp?batchId="+batchId, false,""+batchId);
			dataMap.put("confirmlink", confirmLink);
			String refudLink =  basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), HAORDER_REFUND_TYPE, "/workflow/flowHaOrderMyTask.jsp", "/workflow/flowHaOrderMyTask.jsp?batchId="+batchId, true,""+batchId);//直接拒绝
			dataMap.put("refudLink", refudLink);
			dataMap.put("accepters", accepters);
			dataMap.put("ccaccepters", ccaccepters);
		}catch (Exception e) {
			logerr.debug("insertHaOrderList:"+e.getMessage());
			e.printStackTrace();
			return null;
		}
		return dataMap;
	}*/


	public boolean insertHaOrderList(List<HaOrderVo> lstHaOrder)
	{
		try
		{
			ImportDataPageModelUtil lstPage = new ImportDataPageModelUtil(
					lstHaOrder, 100);
			int toltalPage = lstPage.getTotalPages();
			for(int i=1;i<=toltalPage;i++)
			{
				@SuppressWarnings("unchecked")
				List<HaOrderVo> tmpLst  = lstPage.getObjects(i);
				haOrderMapper.insertHaOrderBatch(tmpLst);
			}
		}catch (Exception e) {
			logerr.debug("insertHaOrderList:"+e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}

	
	
	public boolean insertHaOrderLineList(List<HaOrderLineVo> lstHaLineOrder)
	{
		try
		{
			ImportDataPageModelUtil lstPage = new ImportDataPageModelUtil(
					lstHaLineOrder, 100);
			int toltalPage = lstPage.getTotalPages();
			for(int i=1;i<=toltalPage;i++)
			{
				@SuppressWarnings("unchecked")
				List<HaOrderLineVo> tmpLst  = lstPage.getObjects(i);
				haOrderLineMapper.insertHaOrderLineBatch(tmpLst);
			}
		}catch (Exception e) {
			logerr.debug("insertHaOrderLineList:"+e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	private boolean insertHaOrderUploadList(List<HaOrderUploadVo> lstHaOrderUpload)
	{
		try
		{
			ImportDataPageModelUtil lstPage = new ImportDataPageModelUtil(
					lstHaOrderUpload, 100);
			int toltalPage = lstPage.getTotalPages();
			for(int i=1;i<=toltalPage;i++)
			{
				@SuppressWarnings("unchecked")
				List<HaOrderUploadVo> tmpLst  = lstPage.getObjects(i);
				haOrderUploadMapper.insertHaOrderUploadBatch(tmpLst);
			}
		}catch (Exception e) {
			logerr.debug("insertHaOrderUploadList:"+e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	
	private boolean insertHaOrderUploadLineList(List<HaOrderUploadLineVo> lstHaOrderUploadLine)
	{
		try
		{
			ImportDataPageModelUtil lstPage = new ImportDataPageModelUtil(
					lstHaOrderUploadLine, 100);
			int toltalPage = lstPage.getTotalPages();
			for(int i=1;i<=toltalPage;i++)
			{
				@SuppressWarnings("unchecked")
				List<HaOrderUploadLineVo> tmpLst  = lstPage.getObjects(i);
				haOrderUploadLineMapper.insertHaOrderUploadLineBatch(tmpLst);
			}
		}catch (Exception e) {
			logerr.debug("insertHaOrderUploadLineList:"+e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	

	private boolean insertHaPriceListInfo(
			List<HaOrderProductPriceInfoVo> allPriceInfoList)
	{
		try
		{
			ImportDataPageModelUtil lstPage = new ImportDataPageModelUtil(
					allPriceInfoList, 100);
			int toltalPage = lstPage.getTotalPages();
			for(int i=1;i<=toltalPage;i++)
			{
				@SuppressWarnings("unchecked")
				List<HaOrderProductPriceInfoVo> tmpLst  = lstPage.getObjects(i);
				priceInfoMapper.insertHaProductPriceInfoBatch(tmpLst);
			}
		}catch (Exception e) {
			logerr.debug("insertHaPriceListInfo:"+e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}


	private HaOrderApplyBatchVo generateHAOrderApplyBatchObj(
			Map<String,Object> baseData)throws Exception {
		HaOrderApplyBatchVo haOrderApplyBatchVo = new HaOrderApplyBatchVo();
		haOrderApplyBatchVo.setApproveStatus(HaOrderApplyBatchVo.APPLY_STATUS_APPROVING);
		Date createDate = (Date) baseData.get("currentDate");
		String batchTitle = baseData.get("pdName")+"_"+DateUtil.toDateStrNew(createDate, 0);
		haOrderApplyBatchVo.setBatchTitle(batchTitle);
		haOrderApplyBatchVo.setCreateTime(createDate);
		haOrderApplyBatchVo.setProkey((String)baseData.get("pdKey"));
		haOrderApplyMapper.insertSelective(haOrderApplyBatchVo);
		return haOrderApplyBatchVo;
	}


	@Override
	public Map<String, Object> queryHAOrdersByConditions(
			HaOrderConditions orderConditions) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<HaOrderVo> lstOrders = new ArrayList<HaOrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		
		try
		{
			if(null==orderConditions)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			
			if(null!=orderConditions.getOrderCreateTime())
			{
				 Date createDate =	DateUtils.stringToDate(orderConditions.getOrderCreateTime(), 3);
				 orderConditions.setOrderCreateTime(DateUtils.toDateStr(createDate));
			}
			
			if(null!=orderConditions.getOrderCreateEndTime() && !orderConditions.getOrderCreateEndTime().isEmpty())
			{
				 Date createEndDate =	DateUtils.addDays(DateUtils.stringToDate(orderConditions.getOrderCreateEndTime(), 3), 1) ;
				 orderConditions.setOrderCreateEndTime(DateUtils.toDateStr(createEndDate));
			}
	
			if(null==orderConditions.getOrderStatus()|| orderConditions.getOrderStatus().isEmpty()||orderConditions.getOrderStatus().equals("null"))
			{
				orderConditions.setOrderStatus(null);
			}
			if(null==orderConditions.getOrderSource() || orderConditions.getOrderSource().isEmpty())
			{
				orderConditions.setOrderSource(null);
			}
			if(null==orderConditions.getOrderCode() || orderConditions.getOrderCode().isEmpty())
			{
				orderConditions.setOrderCode(null);
			}else
			{
				orderConditions.setOrderCode(orderConditions.getOrderCode().trim());
			}
			
			if(null==orderConditions.getOrderBatchId() || orderConditions.getOrderBatchId()==-1)
			{
				orderConditions.setOrderBatchId(null);
			}
			
			int queryType = orderConditions.getQueryType();
			if(queryType == BaseParams.ADVANCED_SEARCH_TYPE)//高级搜索
			{
				orderConditions.setQueryField(null);
			}
			Long totalRecord = 0L;
			lstOrders = haOrderMapper.getHaOrdersByCondition(orderConditions);
			totalRecord = orderConditions.getTotalCount();
			resultMap.put(RESULT_LST_KEY, lstOrders);
			resultMap.put(RESULT_TOTALRECORD, totalRecord);
			
		}catch (Exception e) {
			logerr.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> queryHAOrderLineByOrderNo(String orderNo) {
		Map<String, Object> resultMap = new HashMap<String,Object>();
		Map<String, Object> reqMap = new HashMap<String,Object>();
		try
		{
			reqMap.put("orderNo", orderNo);
			List<HaOrderLineVo> lstHaOrderLines =  haOrderLineMapper.getHaOrderDetails(reqMap);
			//主要是获取对应的订单头的信息【订单号，订单状态，合伙人，导入时间/创建时间】
			resultMap.put("currentHaOrder", lstHaOrderLines.get(0));
			resultMap.put("lstHaOrderLines", lstHaOrderLines);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			logerr.info(e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	
	private Map<String, Object> processHAOrdersByTcpMap(
			Map<String, Object> reqMap)throws Exception
	{
		 Map<String, Object> returnMap = new HashMap<String,Object>();
		//数据准备
		List<ImportTcp> lstTcpOrders = (List<ImportTcp>) reqMap.get(HaOrderController.LSTIMPORTTCPS);
		//开始解析，遍历  //1个店铺对应1种PP的订单，，，1个仓库对应一个上传的订单
		List<NeedPpOrder> needPpOrderLst = new ArrayList<NeedPpOrder>();//供PP后台保存
		List<NeedUploadOrder> needUploadOrderLst = new ArrayList<NeedUploadOrder>();//供上传到服务器
		NeedUploadOrder needUploadOrder = null;
		NeedPpOrder needPpOrder = null;
		int totalAmount = 0;
		for(int i=3;i<lstTcpOrders.size();i++)
		{
			ImportTcp imprtTcp = lstTcpOrders.get(i);
			String wareHouse =  imprtTcp.getWareHouse();
			String sku = imprtTcp.getSku();
			if(sku.endsWith("01"))
        	{
        		sku = sku.substring(0, sku.length()-2);
        	}else if (sku.endsWith("01A"))
        	{
        		sku = sku.substring(0, sku.length()-3);
        	}
			if(null==wareHouse || wareHouse.isEmpty() || TCP_TOATAL_TEXT.equals(wareHouse))
			{
				special_product_netunitprice = imprtTcp.getNetunitprice();
				special_product_totalnetunitprice = imprtTcp.getTotalnetunitprice();
				if(null==special_product_netunitprice||!ImportDataUtil.isDataDouble(special_product_netunitprice))
				{
					throw new Exception("TCP sheet表中[总计]行[Net unit price]列存在空的情况");
				}
				if(null==special_product_totalnetunitprice||!ImportDataUtil.isDataDouble(special_product_totalnetunitprice))
				{
					throw new Exception("TCP sheet表中[总计]行[Net unit price]后面一列存在空的情况");
				}
				break;
			}
			
			if(null==wareHouse || wareHouse.isEmpty())
			{
				throw new Exception("TCP sheet表中[仓库/店]列存在空的情况");
			}
			
			if(i%3==0)
			{
				if(null!=needUploadOrder)
				{
					needUploadOrder.setAmount(totalAmount);
					needUploadOrderLst.add(needUploadOrder);
					
				}
				needUploadOrder = new NeedUploadOrder();
				needUploadOrder.setWareHouse(wareHouse);
				needUploadOrder.setSku(sku);
				needUploadOrder.setCategory(sku);
				totalAmount = 0;
				
			}else
			{
				needPpOrder = new NeedPpOrder();
				needPpOrder.setWorkShopname(imprtTcp.getWareHouse());
				if(null==imprtTcp.getAmount() || !ImportDataUtil.isDataInteger(imprtTcp.getAmount()))
				{
					throw new Exception("TCP sheet表中[Sum of 数量]列存在空的情况");
				}
				needPpOrder.setAmount(Integer.parseInt(imprtTcp.getAmount()));
				needPpOrder.setSku(sku);
				needPpOrder.setCategory(sku);
				if(null==imprtTcp.getAmount() || !ImportDataUtil.isDataInteger(imprtTcp.getAmount()))
				{
					throw new Exception("TCP sheet表中[Confirmed Qty]列存在空的情况");
				}
				totalAmount += Integer.parseInt(imprtTcp.getConfirmAmount());
				needPpOrderLst.add(needPpOrder);
			}
			
		}
		if(null!=needUploadOrder)
		{
			needUploadOrder.setAmount(totalAmount);
			needUploadOrderLst.add(needUploadOrder);
		}
		returnMap.put("needUploadOrderLst", needUploadOrderLst);
		returnMap.put("needPpOrderLst", needPpOrderLst);
		return returnMap;
	}
	
	
	private Map<String, Object> processHAOrdersByHaPriceMap(
			Map<String, Object> reqMap)throws Exception
	{
		
		 Map<String, Object> returnMap = new HashMap<String,Object>();
		 //数据准备
		 List<ImportHavolinePrices> lstHaPriceOrders = (List<ImportHavolinePrices>) reqMap.get(HaOrderController.LSTIMPORTHAVOLINEPRICES);
		 List<NeedPpOrder> needPpOrderLst = new ArrayList<NeedPpOrder>();//供PP后台保存
		 String lastSku ="";
		 String currentSku  = "";
		 String lastUnitPrice = "";
		 String currentUnitPrice = "";
		 String lastTotalNetUnitPrice="";
		 String totalNetUnitPrice = "";
		 for(int i=3;i<lstHaPriceOrders.size();i++)
		 {
			 ImportHavolinePrices haPriceOrder = lstHaPriceOrders.get(i);
			 String workShopname = haPriceOrder.getWorkShopname();
			 String category = haPriceOrder.getCategory();
			 currentSku = haPriceOrder.getSku();
			 currentUnitPrice =  haPriceOrder.getNetunitprice();
			 totalNetUnitPrice = haPriceOrder.getTotalnetunitprice();
			 if(null==category || category.isEmpty() || TCP_TOATAL_TEXT.equals(category))
			 {
				break;
			 }
			 if(null==category || category.isEmpty())
			 {
				throw new Exception("Havoline Prices sheet表中[category]列存在空的情况");
			 }
			 
			 if(null==currentSku || currentSku.isEmpty())
			 {
				 currentSku = lastSku;
				 if(null==lastSku || lastSku.isEmpty())
				 {
					 throw new Exception("Havoline Prices sheet表中[货品编号]列存在不合法的情况");
				 }
			 }
			 
			 if(null==workShopname || workShopname.isEmpty())
			 {
				throw new Exception("Havoline Prices sheet表中[workShopname]列存在空的情况");
			 }
			 
			 if(null==haPriceOrder.getAmount() || !ImportDataUtil.isDataInteger(haPriceOrder.getAmount()))
			 {
				throw new Exception("Havoline Prices sheet表中[求和项:数量]列存在空的情况");
			 }
			 
			 if(null==currentUnitPrice || !ImportDataUtil.isDataDouble(currentUnitPrice))
			 {
				 throw new Exception("Havoline Prices sheet表中[Net unit price]列存在空的情况");
			 }
			 
			 if(null==totalNetUnitPrice || !ImportDataUtil.isDataDouble(totalNetUnitPrice))
			 {
				 throw new Exception("Havoline Prices sheet表中[Net unit price]后面一列存在空的情况");
			 }
			 if(currentSku.endsWith("01"))
         	 {
				 currentSku = currentSku.substring(0, currentSku.length()-2);
         	 }else if (currentSku.endsWith("01A"))
         	 {
         		 currentSku = currentSku.substring(0, currentSku.length()-3);
         	 }
			 
			 NeedPpOrder needPpOrder = new NeedPpOrder();
			 needPpOrder.setAmount(Integer.parseInt(haPriceOrder.getAmount()));
			 needPpOrder.setCategory(category);
			 if(null==currentSku||currentSku.isEmpty())
			 {
				 needPpOrder.setSku(lastSku);
				 needPpOrder.setUnitprice(lastUnitPrice);
				 needPpOrder.setUnitprice(lastTotalNetUnitPrice);
			 }else
			 {
				 needPpOrder.setSku(currentSku);
				 needPpOrder.setUnitprice(currentUnitPrice);
				 needPpOrder.setTotalnetunitprice(totalNetUnitPrice);
			 }
			 needPpOrder.setWorkShopname(workShopname);
			 lastSku = currentSku;
			 lastUnitPrice = currentUnitPrice;
			 lastTotalNetUnitPrice = totalNetUnitPrice;
			 needPpOrderLst.add(needPpOrder);
		 }
		 
		 returnMap.put("needPpOrderLst2", needPpOrderLst);
		return returnMap;
	}
	
	
	private Map<String, Object> processHAOrdersByHaWareHouseMap(
			Map<String, Object> reqMap)throws Exception
	{
		 Map<String, Object> returnMap = new HashMap<String,Object>();
		 //数据准备
		 List<ImportHavolineWarehouse> lstHaWareHouseOrders = (List<ImportHavolineWarehouse>) reqMap.get(HaOrderController.LSTIMPORTHAVOLINEWAREHOUSES);
		 List<NeedUploadOrder> needUploadOrderLst = new ArrayList<NeedUploadOrder>();//供上传到服务器
		 NeedUploadOrder needUploadOrder = null;
		 for(int i=4;i<lstHaWareHouseOrders.size();i++)
		 {
			 	ImportHavolineWarehouse haWareHouseOrder = lstHaWareHouseOrders.get(i);
			 	String wareHouse = haWareHouseOrder.getWareHouse();
			 	if(null==wareHouse || wareHouse.isEmpty() || TCP_TOATAL_TEXT.equals(wareHouse))
				{
					break;
				}
			 	
			 	if(null==wareHouse || wareHouse.isEmpty())
			 	{
			 		throw new Exception("Havoline by Warehouse sheet表中[仓库]列存在空的情况");
			 	}
			 	if(null==haWareHouseOrder.getCategory() || haWareHouseOrder.getCategory().isEmpty())
			 	{
			 		throw new Exception("Havoline by Warehouse sheet表中[category]列存在空的情况");
			 	}
			 	if(null==haWareHouseOrder.getSku() || haWareHouseOrder.getSku().isEmpty())
			 	{
			 		throw new Exception("Havoline by Warehouse sheet表中[货品编号]列存在空的情况");
			 	}
			 	if(null==haWareHouseOrder.getActAmount() || !ImportDataUtil.isDataInteger(haWareHouseOrder.getActAmount()))
			 	{
			 		throw new Exception("Havoline by Warehouse sheet表中[扣减后数量]列存在空或不合法的情况");
			 	}
			    needUploadOrder = new NeedUploadOrder();
				needUploadOrder.setWareHouse(wareHouse);
				String sku = haWareHouseOrder.getSku();
				if(sku.endsWith("01"))
            	{
            		sku = sku.substring(0, sku.length()-2);
            	}else if(sku.endsWith("01A"))
            	{
            		sku = sku.substring(0, sku.length()-3);
            	}
				needUploadOrder.setSku(sku);
				needUploadOrder.setCategory(haWareHouseOrder.getCategory());
				needUploadOrder.setAmount(Integer.parseInt(haWareHouseOrder.getActAmount()));
				needUploadOrderLst.add(needUploadOrder);
		 }
		 returnMap.put("needUploadOrderLst2", needUploadOrderLst);
		 return returnMap;
	}

	private Map<String, Object> sortOrder(
			List<NeedUploadOrder> lstAllNeedUploadOrders,
			List<NeedPpOrder> lstAllNeedPpOrders)
	{

		Map<String,Object> resultMap = new HashMap<String,Object>();
		Collections.sort(lstAllNeedUploadOrders, new Comparator<NeedUploadOrder>() {
	            public int compare(NeedUploadOrder o1, NeedUploadOrder o2) {
					return o1.getWareHouse().compareTo(o2.getWareHouse());
	            }
	        });
		
		Collections.sort(lstAllNeedPpOrders, new Comparator<NeedPpOrder>() {
            public int compare(NeedPpOrder o1, NeedPpOrder o2) {
				return o1.getWorkShopname().compareTo(o2.getWorkShopname());
            }
        });
		resultMap.put("lstAllNeedUploadOrders", lstAllNeedUploadOrders);
		resultMap.put("lstAllNeedPpOrders", lstAllNeedPpOrders);
		return resultMap;
	}

	
	private Map<String, Object> generateTargetHaOrdersByMap(
			Map<String, Object> sortMap, Map<String, Object> baseDataMap)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//获取基础数据
		Long batchId = (Long) baseDataMap.get(BATCH_ID);
		Date currentDate = (Date) baseDataMap.get("currentDate");
		
		//获取订单数据
		List<NeedPpOrder> lstPpOrders = (List<NeedPpOrder>) sortMap.get("lstAllNeedPpOrders");
		
		//组装维护PP后台页面的订单
		List<HaOrderVo> lstHaOrderVos = new ArrayList<HaOrderVo>();
		List<HaOrderLineVo> lstHaOrderLineVos = null;
		List<HaOrderLineVo> allHaOrderLineVosLst = new ArrayList<HaOrderLineVo>();
		String currentWorkshopName="";
		String lastWorkshopName = "";
		String lastSku = "";
		String currentSku = "";
		HaOrderVo haOrder = null;
		HaOrderVo lastHaOrder = null;
		HaOrderLineVo haOrderLine = null;
		WxtOrderYYFWSetsVo orderSetsInfo = null;
		
		//String lastunitPrice = "";
		String currentunitPrice = "";
		String currenttotalnetUnitPrice = "";
		
		int allamount = 0;
		int i = 0;
		boolean isNotSameFlag = false;
		for(NeedPpOrder ppOrder:lstPpOrders)
		{
			currentWorkshopName = ppOrder.getWorkShopname();
			orderSetsInfo = (WxtOrderYYFWSetsVo) baseDataMap.get(currentWorkshopName);
			String category =ppOrder.getCategory();
			if(!lastWorkshopName.equals(currentWorkshopName))
			{
				isNotSameFlag = true;
				haOrder = new HaOrderVo();
				lstHaOrderLineVos = new ArrayList<HaOrderLineVo>();;
				haOrder.setBatchid(batchId);
				haOrder.setCreateTime(currentDate);
				haOrder.setTradeTime(currentDate);
				haOrder.setDeldate(currentDate);
				//生成订单号
				String orderNoSequnceName = orderSetsInfo.getOrderNoSequnceName();
				String orderNoFlag = orderSetsInfo.getOrderNoFlag();
				reqMap.put("orderNoSequnceName", orderNoSequnceName);
				Long tmpSequenceNo = propertiesMapper.getSequenceBySequenceName(reqMap);
				String sequenceNo = CommonUtil.addZeroForStr(""+tmpSequenceNo, 6, 1);
				haOrder.setOrderNo(CommonUtil.generateOrderCode(orderNoFlag)+sequenceNo);
				haOrder.setOrderPartnerid(orderSetsInfo.getPartnerId());
				haOrder.setOrderPartnername(currentWorkshopName);
				haOrder.setOrderSource(orderSetsInfo.getSourceValue());
				haOrder.setOrderType(orderSetsInfo.getSourceType());
			}else
			{
				isNotSameFlag = false;
			}
			currentSku = ppOrder.getSku();
			if(null==ppOrder.getUnitprice() || ppOrder.getUnitprice().isEmpty())
			{
				if(currentSku.equals(SPECIAL_PRODUCT_SKU))
				{
					currentunitPrice=""+df.format(Double.parseDouble(special_product_netunitprice));
					currenttotalnetUnitPrice=""+df.format(Double.parseDouble(special_product_totalnetunitprice));
				}else
				{
					currentunitPrice="0.00";
					currenttotalnetUnitPrice ="0.00";
				}
			}else
			{
				currentunitPrice = ""+df.format(Double.parseDouble(ppOrder.getUnitprice()));
				currenttotalnetUnitPrice = ""+df.format(Double.parseDouble(ppOrder.getTotalnetunitprice()));
			}
			if(!lastSku.equals(currentSku) || isNotSameFlag)//add by bo.liu 1020 isNotSameFlag 
			{
				allamount = 0;
				haOrderLine = new HaOrderLineVo();
				haOrderLine.setBatchid(batchId);
				haOrderLine.setOrderNo(haOrder.getOrderNo());
				haOrderLine.setSku(currentSku);
				haOrderLine.setAmount(ppOrder.getAmount());
				haOrderLine.setCategory(category);
				haOrderLine.setUnitprice(currentunitPrice);
				haOrderLine.setTotalnetunitprice(currenttotalnetUnitPrice);
				allHaOrderLineVosLst.add(haOrderLine);
				lstHaOrderLineVos.add(haOrderLine);
				allamount += ppOrder.getAmount();
				
			}else
			{
				allamount += ppOrder.getAmount();
				haOrderLine.setAmount(allamount);
			}
			lastSku = currentSku;
			//lastunitPrice = currentunitPrice;
			haOrder.setLstHaOrderLineVos(lstHaOrderLineVos);
			if(isNotSameFlag && (i!=0))
	    	{
				lstHaOrderVos.add(lastHaOrder);
	    	}
			
			lastHaOrder = new HaOrderVo();
			lastHaOrder = haOrder;
	    	i++;
	    	lastWorkshopName = currentWorkshopName;
		}
		lstHaOrderVos.add(haOrder);
		resultMap.put("lstHaorderVos", lstHaOrderVos);
		resultMap.put("allHaOrderLineVosLst", allHaOrderLineVosLst);
		return resultMap;
	}
	
	private Map<String, Object> generateTargetHaUploadOrdersByMap(
			Map<String, Object> sortMap, Map<String, Object> baseDataMap)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//获取基础数据
		Long batchId = (Long) baseDataMap.get(BATCH_ID);
		Date currentDate = (Date) baseDataMap.get("currentDate");
		
		//获取订单数据
		List<NeedUploadOrder> lstNeedUploadOrders =  (List<NeedUploadOrder>) sortMap.get("lstAllNeedUploadOrders");
		
		List<HaOrderUploadVo> lstHaUploadOrders = new ArrayList<HaOrderUploadVo>();
		List<HaOrderUploadLineVo> lstHaUploadOrderLines = null;
		List<HaOrderUploadLineVo> allHaUploadOrderLinesLst = new ArrayList<HaOrderUploadLineVo>();
		
		String currentWareHouse = "";
		String lastWareHouse = "";
		HaOrderUploadVo currentHaUploadOrder = null;
		HaOrderUploadVo lastHaUploadOrder = null;
		
		int i=0;
		boolean isNotSameFlag = false;
		for(NeedUploadOrder needUploadOrder:lstNeedUploadOrders)
		{
			currentWareHouse = needUploadOrder.getWareHouse();
			if(!lastWareHouse.equals(currentWareHouse))
			{
				isNotSameFlag = true;
				currentHaUploadOrder = new HaOrderUploadVo();
				lstHaUploadOrderLines = new ArrayList<HaOrderUploadLineVo>();
				currentHaUploadOrder.setBatchid(batchId);
				currentHaUploadOrder.setCreateTime(currentDate);
				currentHaUploadOrder.setDeldate(currentDate);
				//生成订单号
				String orderNoFlag = CommonUtil.orderTypes[17];
				reqMap.put("orderNoSequnceName", WAREHOUSE_HAORDER_CODE);
				Long tmpSequenceNo = propertiesMapper.getSequenceBySequenceName(reqMap);
				String sequenceNo = CommonUtil.addZeroForStr(""+tmpSequenceNo, 6, 1);
				currentHaUploadOrder.setOrderNo(CommonUtil.generateOrderCode(orderNoFlag)+sequenceNo);
				currentHaUploadOrder.setWarehouseName(currentWareHouse);
				currentHaUploadOrder.setPricingdt(currentDate);
				currentHaUploadOrder.setTradeTime(currentDate);
			}else
			{
				isNotSameFlag = false;
			}
			
			HaOrderUploadLineVo haOrderUploadLineVo = new HaOrderUploadLineVo();
			haOrderUploadLineVo.setBatchid(batchId);
			haOrderUploadLineVo.setAmount(needUploadOrder.getAmount());
			haOrderUploadLineVo.setSku(needUploadOrder.getSku());
			haOrderUploadLineVo.setCategory(needUploadOrder.getCategory());
			haOrderUploadLineVo.setUploadOrderNo(currentHaUploadOrder.getOrderNo());
			lstHaUploadOrderLines.add(haOrderUploadLineVo);
			allHaUploadOrderLinesLst.add(haOrderUploadLineVo);
			currentHaUploadOrder.setLstHaOrderUploadLineVo(lstHaUploadOrderLines);
			if(isNotSameFlag && (i!=0))
	    	{
				lstHaUploadOrders.add(lastHaUploadOrder);
	    	}
			
			lastHaUploadOrder = new HaOrderUploadVo();
			lastHaUploadOrder = currentHaUploadOrder;
	    	i++;
	    	lastWareHouse = currentWareHouse;
			
		}
		lstHaUploadOrders.add(currentHaUploadOrder);
		resultMap.put("lstHaUploadOrders", lstHaUploadOrders);
		resultMap.put("allHaUploadOrderLinesLst", allHaUploadOrderLinesLst);
		return resultMap;
	}
	

	private List<HaOrderProductPriceInfoVo> generateTargetPriceListByMap(
			Map<String, Object> reqMap,Map<String,Object> baseDataMap)throws Exception
	{
		//获取基础数据
		Long batchId = (Long) baseDataMap.get(BATCH_ID);
		Date currentDate = (Date) baseDataMap.get("currentDate");
		//获取订单数据
	    List<ImportPriceList> lstImportPriceList =  (List<ImportPriceList>) reqMap.get(HaOrderController.LSTIMPORTPRICES);
		List<HaOrderProductPriceInfoVo> lstPriceList = new ArrayList<HaOrderProductPriceInfoVo>();
		for(ImportPriceList tmpImportPrice:lstImportPriceList)
		{
			if(null==tmpImportPrice.getSku() || tmpImportPrice.getSku().isEmpty())
		 	{
		 		throw new Exception("Price List sheet表中[A]列存在空的情况");
		 	}
			if(null==tmpImportPrice.getPrice() || !ImportDataUtil.isDataDouble(tmpImportPrice.getPrice()))
		 	{
		 		throw new Exception("Price List sheet表中[B]列存在空或不合法的情况");
		 	}
			if(null==tmpImportPrice.getAmount() || !ImportDataUtil.isDataInteger(tmpImportPrice.getAmount()))
		 	{
		 		throw new Exception("Price List sheet表中[C]列存在空或不合法的情况");
		 	}
			if(null==tmpImportPrice.getUnits() || tmpImportPrice.getUnits().isEmpty())
		 	{
		 		throw new Exception("Price List sheet表中[D]列存在空的情况");
		 	}
			if(null==tmpImportPrice.getTime1() || !(Boolean)ImportDataUtil.isValidDate1(tmpImportPrice.getTime1(), "yyyy/MM/dd").get("isDate"))
		 	{
		 		throw new Exception("Price List sheet表中[E]列存在空或不合法的情况");
		 	}
			if(null==tmpImportPrice.getTime2() || !(Boolean)ImportDataUtil.isValidDate1(tmpImportPrice.getTime2(), "yyyy/MM/dd").get("isDate"))
		 	{
		 		throw new Exception("Price List sheet表中[F]列存在空或不合法的情况");
		 	}
			HaOrderProductPriceInfoVo haOrderProductPriceInfoVo = new HaOrderProductPriceInfoVo();
			haOrderProductPriceInfoVo.setBatchId(batchId);
			haOrderProductPriceInfoVo.setCreateTime(currentDate);
			haOrderProductPriceInfoVo.setProductAmount(Integer.parseInt(tmpImportPrice.getAmount()));
			haOrderProductPriceInfoVo.setProductPrices(""+df.format(Double.parseDouble(tmpImportPrice.getPrice())));
			haOrderProductPriceInfoVo.setProductUnits(tmpImportPrice.getUnits());
			String sku = tmpImportPrice.getSku();
			if(sku.endsWith("01"))
        	{
        		sku = sku.substring(0, sku.length()-2);
        	}else if (sku.endsWith("01A"))
        	{
        		sku = sku.substring(0, sku.length()-3);
        	}
			haOrderProductPriceInfoVo.setSku(sku);
			haOrderProductPriceInfoVo.setTradeTime1(tmpImportPrice.getTime1());
			haOrderProductPriceInfoVo.setSndTime2(tmpImportPrice.getTime2());
			lstPriceList.add(haOrderProductPriceInfoVo);
			
		}
		return lstPriceList;
	}

}
