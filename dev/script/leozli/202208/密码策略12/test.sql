update wx_t_user set password='f92274ff7c2f4914da79760b2e9ad872ceff0d20', salt='27B157064129F4E5',allow_login='T' where --org_id=9--
login_name='liuwan<PERSON>' and status=1

--修改最后登录时间
select l.* 
--update l set l.xg_sj='2022-12-16 23:20'
--update l set l.xg_sj='2022-12-09 23:20'
from wx_t_log l left join wx_t_user u on u.user_id=l.user_id where u.login_name in ('cs-test','cs-dl','cs-login' )

--修改密码修改时间
select l.*, u.ext_flag
--delete l 
--update l set create_time='2022-01-08 23:00'
from wx_t_pwd_change_log l 
left join wx_t_user u on u.user_id=l.user_id where u.login_name='jd'
--select u.ext_flag,* from wx_t_user u where u.ext_flag&(2+4+8+16)>0

--用户状态查询
select 
CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
				AND ll1.xg_sj > '2022-12-01'
		) THEN (
			SELECT TOP 1 ll1.xg_sj
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
			ORDER BY ll1.log_id DESC
		)
		WHEN u.xz_time > '2022-12-01' THEN u.xz_time
		ELSE '2022-12-01'
	END
,datediff(day, CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
				AND ll1.xg_sj > '2022-12-01'
		) THEN (
			SELECT TOP 1 ll1.xg_sj
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
			ORDER BY ll1.log_id DESC
		)
		WHEN u.xz_time > '2022-12-01' THEN u.xz_time
		ELSE '2022-12-01'
	END, getdate()),
	(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='frozenLimitDays' and di1.status=1)
						 - datediff(day, (case when exists (select 1 from wx_t_log ll1 where ll1.user_id=u.user_id and ll1.xg_sj>'2022-12-01') 
								then (select top 1 ll1.xg_sj from wx_t_log ll1 where ll1.user_id=u.user_id order by ll1.log_id desc) 
								when u.xz_time>'2022-12-01' then u.xz_time else '2022-12-01' end),CONVERT(varchar(100), GETDATE(), 23)/*邮件发送和打标记都统一成0点*/) frozen_day /*账号停用天数*/,
								isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
								(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1)) 
- datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate()) pwd_expire_day /*密码过期天数*/
,*
--UPDATE u
--SET xg_time = '2022-12-14 11:37:06', xg_user = '1', ext_flag = ext_flag | '8'
FROM wx_t_user u
WHERE 1 = 1
	AND u.status = 1
	and u.login_name='cs-t'
	--AND u.ext_flag & 8 = 0
	

select l.* 
--update l set l.xg_sj='2022-12-25 23:20'
from wx_t_log l left join wx_t_user u on u.user_id=l.user_id where --u.login_name='leozli' and
l.log_id=(select max(l1.log_id) from wx_t_log l1 where l1.user_id=l.user_id)

select * 
--update u set u.ext_flag=u.ext_flag-24
from wx_t_user u where --u.login_name='leozli' and u.status=1
ext_flag&24=24

select 
CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
				AND ll1.xg_sj > '2022-12-01'
		) THEN (
			SELECT TOP 1 ll1.xg_sj
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
			ORDER BY ll1.log_id DESC
		)
		WHEN u.xz_time > '2022-12-01' THEN u.xz_time
		ELSE '2022-12-01'
	END
,datediff(day, CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
				AND ll1.xg_sj > '2022-12-01'
		) THEN (
			SELECT TOP 1 ll1.xg_sj
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
			ORDER BY ll1.log_id DESC
		)
		WHEN u.xz_time > '2022-12-01' THEN u.xz_time
		ELSE '2022-12-01'
	END, getdate()),
	(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='frozenLimitDays' and di1.status=1)
						 - datediff(day, (case when exists (select 1 from wx_t_log ll1 where ll1.user_id=u.user_id and ll1.xg_sj>'2022-12-01') 
								then (select top 1 ll1.xg_sj from wx_t_log ll1 where ll1.user_id=u.user_id order by ll1.log_id desc) 
								when u.xz_time>'2022-12-01' then u.xz_time else '2022-12-01' end),CONVERT(varchar(100), GETDATE(), 23)/*邮件发送和打标记都统一成0点*/),
								isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), (select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1)) 
- datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate())
,*
--UPDATE u
--SET xg_time = '2022-12-14 11:37:06', xg_user = '1', ext_flag = ext_flag | '8'
FROM wx_t_user u
WHERE 1 = 1
	AND u.status = 1
	and u.login_name='cs-dl'
	--AND u.ext_flag & 8 = 0
	
	
	
--select * from wx_t_user where ext_flag&8=0 and status=1
--update u set u.ext_flag=u.ext_flag-8 from wx_t_user u where ext_flag&8=8 and status=1
--select * from wx_t_user where login_name='liuwanhua'
select 
CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
				AND ll1.xg_sj > '2022-12-01'
		) THEN (
			SELECT TOP 1 ll1.xg_sj
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
			ORDER BY ll1.log_id DESC
		)
		WHEN u.xz_time > '2022-12-01' THEN u.xz_time
		ELSE '2022-12-01'
	END
,*
--UPDATE u
--SET xg_time = '2022-12-14 11:37:06', xg_user = '1', ext_flag = ext_flag | '8'
FROM wx_t_user u
WHERE 1 = 1
	AND u.status = 1
	--and u.login_name='linzhuangwei'
	--AND u.ext_flag & 8 = 0
	AND datediff(day, CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
				AND ll1.xg_sj > '2022-12-01'
		) THEN (
			SELECT TOP 1 ll1.xg_sj
			FROM wx_t_log ll1
			WHERE ll1.user_id = u.user_id
			ORDER BY ll1.log_id DESC
		)
		WHEN u.xz_time > '2022-12-01' THEN u.xz_time
		ELSE '2022-12-01'
	END, getdate()) > (
		SELECT di1.dic_item_desc
		FROM wx_t_dic_item di1
		WHERE di1.dic_type_code = 'User.pwdPolicy'
			AND di1.dic_item_code = 'frozenLimitDays'
			AND di1.status = 1
	)
	
	
	