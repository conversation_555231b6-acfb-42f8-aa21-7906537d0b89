<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.sellin.dao.PromotionDeliveryDetailMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.sellin.model.PromotionDeliveryDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
		<result column="award_type" property="awardType" jdbcType="INTEGER"/>
		<result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
		<result column="source_id" property="sourceId" jdbcType="BIGINT"/>
		<result column="award_quantity" property="awardQuantity" jdbcType="INTEGER"/>
		<result column="promotion_desc" property="promotionDesc" jdbcType="VARCHAR"/>
		<result column="delivery_status" property="deliveryStatus" jdbcType="INTEGER"/>
		<result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
		<result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
		<result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
		<result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,promotion_id,award_type,source_type,source_id,award_quantity,promotion_desc,delivery_status,delivery_time,
		ext_property1,ext_property2,ext_property3,ext_property4,ext_property5,ext_property6,ext_property7,ext_property8,
		ext_property9,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.sellin.model.PromotionDeliveryDetail">
		update wx_t_promotion_delivery_detail set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.sellin.model.PromotionDeliveryDetail">
		update wx_t_promotion_delivery_detail
		<set>
			<if test="promotionId != null" >
				promotion_id = #{promotionId,jdbcType=BIGINT},
			</if>
			<if test="awardType != null" >
				award_type = #{awardType,jdbcType=INTEGER},
			</if>
			<if test="sourceType != null" >
				source_type = #{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="sourceId != null" >
				source_id = #{sourceId,jdbcType=BIGINT},
			</if>
			<if test="awardQuantity != null" >
				award_quantity = #{awardQuantity,jdbcType=INTEGER},
			</if>
			<if test="promotionDesc != null" >
				promotion_desc = #{promotionDesc,jdbcType=VARCHAR},
			</if>
			<if test="deliveryStatus != null" >
				delivery_status = #{deliveryStatus,jdbcType=INTEGER},
			</if>
			<if test="deliveryTime != null" >
				delivery_time = #{deliveryTime,jdbcType=DATE},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null" >
				ext_property6 = #{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null" >
				ext_property7 = #{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null" >
				ext_property8 = #{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null" >
				ext_property9 = #{extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.sellin.model.PromotionDeliveryDetailExample">
    	delete from wx_t_promotion_delivery_detail
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.sellin.model.PromotionDeliveryDetail">
		insert into wx_t_promotion_delivery_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="promotionId != null">
				promotion_id,
			</if>
			<if test="awardType != null">
				award_type,
			</if>
			<if test="sourceType != null">
				source_type,
			</if>
			<if test="sourceId != null">
				source_id,
			</if>
			<if test="awardQuantity != null">
				award_quantity,
			</if>
			<if test="promotionDesc != null">
				promotion_desc,
			</if>
			<if test="deliveryStatus != null">
				delivery_status,
			</if>
			<if test="deliveryTime != null">
				delivery_time,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="extProperty6 != null">
				ext_property6,
			</if>
			<if test="extProperty7 != null">
				ext_property7,
			</if>
			<if test="extProperty8 != null">
				ext_property8,
			</if>
			<if test="extProperty9 != null">
				ext_property9,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="promotionId != null">
				#{promotionId,jdbcType=BIGINT},
			</if>
			<if test="awardType != null">
				#{awardType,jdbcType=INTEGER},
			</if>
			<if test="sourceType != null">
				#{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="sourceId != null">
				#{sourceId,jdbcType=BIGINT},
			</if>
			<if test="awardQuantity != null">
				#{awardQuantity,jdbcType=INTEGER},
			</if>
			<if test="promotionDesc != null">
				#{promotionDesc,jdbcType=VARCHAR},
			</if>
			<if test="deliveryStatus != null">
				#{deliveryStatus,jdbcType=INTEGER},
			</if>
			<if test="deliveryTime != null">
				#{deliveryTime,jdbcType=DATE},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null">
				#{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null">
				#{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null">
				#{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null">
				#{extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_promotion_delivery_detail
		<set>
			<if test="record.promotionId != null">
				promotion_id = #{record.promotionId,jdbcType=BIGINT},
			</if>
			<if test="record.awardType != null">
				award_type = #{record.awardType,jdbcType=INTEGER},
			</if>
			<if test="record.sourceType != null">
				source_type = #{record.sourceType,jdbcType=VARCHAR},
			</if>
			<if test="record.sourceId != null">
				source_id = #{record.sourceId,jdbcType=BIGINT},
			</if>
			<if test="record.awardQuantity != null">
				award_quantity = #{record.awardQuantity,jdbcType=INTEGER},
			</if>
			<if test="record.promotionDesc != null">
				promotion_desc = #{record.promotionDesc,jdbcType=VARCHAR},
			</if>
			<if test="record.deliveryStatus != null">
				delivery_status = #{record.deliveryStatus,jdbcType=INTEGER},
			</if>
			<if test="record.deliveryTime != null">
				delivery_time = #{record.deliveryTime,jdbcType=DATE},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty5 != null">
				ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty6 != null">
				ext_property6 = #{record.extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty7 != null">
				ext_property7 = #{record.extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty8 != null">
				ext_property8 = #{record.extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty9 != null">
				ext_property9 = #{record.extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.sellin.model.PromotionDeliveryDetailExample">
		delete from wx_t_promotion_delivery_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.sellin.model.PromotionDeliveryDetailExample" resultType="int">
		select count(1) from wx_t_promotion_delivery_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.PromotionDeliveryDetailExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_promotion_delivery_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.PromotionDeliveryDetailExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_promotion_delivery_detail
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.promotion_id, t1.award_type, t1.source_type, t1.source_id, t1.award_quantity, t1.promotion_desc,
			 t1.delivery_status, t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.ext_property4, t1.ext_property5,
			 t1.ext_property6, t1.ext_property7, t1.ext_property8, t1.ext_property9, t1.update_user_id, t1.update_time
		  from wx_t_promotion_delivery_detail t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_promotion_delivery_detail (promotion_id, award_type, source_type, source_id, award_quantity, promotion_desc, delivery_status, ext_property1, ext_property2, ext_property3, ext_property4, ext_property5, ext_property6, ext_property7, ext_property8, ext_property9, update_user_id, update_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.promotionId, jdbcType=BIGINT}, #{item.awardType, jdbcType=INTEGER}, #{item.sourceType, jdbcType=VARCHAR}, #{item.sourceId, jdbcType=BIGINT}, #{item.awardQuantity, jdbcType=INTEGER}, #{item.promotionDesc, jdbcType=VARCHAR}, #{item.deliveryStatus, jdbcType=INTEGER}, #{item.extProperty1, jdbcType=VARCHAR}, #{item.extProperty2, jdbcType=VARCHAR}, #{item.extProperty3, jdbcType=VARCHAR}, #{item.extProperty4, jdbcType=VARCHAR}, #{item.extProperty5, jdbcType=VARCHAR}, #{item.extProperty6, jdbcType=VARCHAR}, #{item.extProperty7, jdbcType=VARCHAR}, #{item.extProperty8, jdbcType=VARCHAR}, #{item.extProperty9, jdbcType=VARCHAR}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	
	<resultMap id="BaseResultDeliveryMap" type="com.chevron.sellin.model.PromotionDeliveryDetailVo">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
		<result column="award_type" property="awardType" jdbcType="INTEGER"/>
		<result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
		<result column="source_id" property="sourceId" jdbcType="BIGINT"/>
		<result column="award_quantity" property="awardQuantity" jdbcType="INTEGER"/>
		<result column="promotion_desc" property="promotionDesc" jdbcType="VARCHAR"/>
		<result column="delivery_status" property="deliveryStatus" jdbcType="INTEGER"/>
		<result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
		<result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
		<result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
		<result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	
		<result column="promotion_name" property="promotionName" jdbcType="VARCHAR"/>
		
		<result column="delivery_id" property="deliveryId" jdbcType="BIGINT"/>
		<result column="sales_order" property="salesOrder" jdbcType="VARCHAR"/>
		<result column="post_year_month" property="postYearMonth" jdbcType="TIMESTAMP"/>
		<result column="pricing_date" property="pricingDate" jdbcType="TIMESTAMP"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
		<result column="product_sku" property="productSku" jdbcType="VARCHAR"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="pack_units" property="packUnits" jdbcType="VARCHAR"/>
		<result column="liters" property="liters" jdbcType="NUMERIC"/>
		<result column="revenue_rmb" property="revenueRmb" jdbcType="NUMERIC"/>
		<result column="sold_to_code" property="soldToCode" jdbcType="VARCHAR"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<!--cz add-->
		<result column="start_date_min" property="startDateMin" jdbcType="TIMESTAMP"/>
		<result column="end_date_max" property="endDateMax" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<select id="getDetailListById" resultMap="BaseResultDeliveryMap" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryParams">
        SELECT case when exists (select 1 from wx_t_promotion_delivery_detail dd1
        LEFT JOIN wx_t_delivery_order_rel r1 ON dd1.id = r1.delivery_detail_id
        LEFT JOIN wx_t_sell_in_promotion_delivery pd1 ON pd1.delivery_id = r1.delivery_id where dd.id=dd1.id and pd1.delivery_id <![CDATA[ < ]]>  pd.delivery_id) then 0 else dd.award_quantity end point,
        pd.*
        , (
        SELECT DISTINCT p.name
        FROM wx_t_product p
        WHERE p.sku = pd.product_sku
        ) AS product_name
        FROM wx_t_promotion_delivery_detail dd
        LEFT JOIN wx_t_delivery_order_rel r ON dd.id = r.delivery_detail_id
        LEFT JOIN wx_t_sell_in_promotion_delivery pd ON pd.delivery_id = r.delivery_id
        WHERE 1 = 1
		<if test="id != null">
			and dd.id = #{id}
		</if>
	</select>

    <select id="getDetailListByParams" resultMap="BaseResultDeliveryMap" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryParams">
        SELECT case when exists ( select 1
                         from wx_t_promotion_delivery_detail dd1
                                  LEFT JOIN wx_t_delivery_order_rel r1 ON dd1.id = r1.delivery_detail_id
                                  LEFT JOIN wx_t_sell_in_promotion_delivery pd1 ON pd1.delivery_id = r1.delivery_id
                         where dd.id = dd1.id
                           and pd1.delivery_id <![CDATA[ < ]]>  pd.delivery_id) then 0
             else dd.award_quantity end point,
        pd.*, (
        SELECT DISTINCT
        p.name
        FROM
        wx_t_product p
        WHERE
        p.sku = pd.product_sku
        ) AS product_name
        FROM
        wx_t_promotion_delivery_detail dd
        left join wx_t_delivery_order_rel r on dd.id=r.delivery_detail_id
        LEFT JOIN wx_t_sell_in_promotion_delivery pd ON pd.delivery_id = r.delivery_id
        where  1 = 1 and dd.promotion_id = #{promotionId} and dd.ext_property2 = #{pointType} and dd.ext_property5 = #{deliveryCode}
        and dd.ext_property7 = #{distributorId}
    </select>
	
	<select id="getDetailList" resultMap="BaseResultDeliveryMap" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryParams">
			SELECT MAX(p.promotion_name) AS promotion_name, xx1.promotion_id
				, MAX(p.update_time) AS u_time,
		<!--CASE WHEN MIN(p.start_date) IS NULL THEN MIN(p.post_year_month_start) ELSE MIN(p.start_date) END AS startDateMin,
		CASE WHEN MAX(p.end_date) IS NULL THEN MAX(p.post_year_month_end) ELSE MAX(p.end_date) END AS endDateMax-->
		MAX(del.post_year_month) AS post_year_month
				, del.distributor_id
				, (
					SELECT DISTINCT cos.customer_name_cn
					FROM PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos
					WHERE cos.distributor_id = del.distributor_id
				) AS customer_name, del.product_sku, MAX(p1.name) AS product_name
				, SUM(del.quantity) AS quantity, MAX(del.pack_units) AS pack_units
				, SUM(del.liters) AS liters, SUM(del.revenue_rmb) AS revenue_rmb
				, del.region, xx1.award_type, xx1.source_type, 
				SUM(xx1.award_quantity) AS award_quantity
				, MAX(xx1.promotion_desc) AS promotion_desc, xx1.delivery_status,xx1.delivery_time
				, MAX(xx1.ext_property1) AS ext_property1, MAX(xx1.ext_property2) AS ext_property2
				, MAX(xx1.ext_property3) AS ext_property3, xx1.offer_key,tt.*
			FROM (SELECT t1.promotion_id, t1.award_type, t1.source_type, SUM(t1.award_quantity) AS award_quantity
						, MAX(t1.promotion_desc) AS promotion_desc, t1.delivery_status AS deliveryStatus
						, MAX(t1.ext_property1) AS ext_property1, MAX(t1.ext_property2) AS ext_property2
						, MAX(t1.ext_property3) AS ext_property3, t1.offer_key,t1.ext_property7, t1.delivery_status,t1.delivery_time,t1.ext_property9
					FROM wx_t_promotion_delivery_detail t1
					where 1=1
		<if test="promotionId != null and promotionId != ''">
			AND t1.promotion_id  = (select isnull(sp1.source_id, sp1.id) from wx_t_sell_in_promotion sp1 where sp1.id=#{promotionId})
		</if>
		<if test="promotionIds != null and promotionIds != ''">
			AND t1.promotion_id  in (select isnull(sp1.source_id, sp1.id) from wx_t_sell_in_promotion sp1 where sp1.id in( select * from SplitIn(#{promotionIds}, ',')))
		</if>
 		<choose>
           <when test="extProperty1Fift != null and extProperty1Fift != '' and extProperty1Integral != null and extProperty1Integral != ''">
               AND ((t1.ext_property2 = #{extProperty1Fift} AND t1.award_type = 1) OR (t1.ext_property2 = #{extProperty1Integral} AND t1.award_type = 2))
           </when>
           <when test="extProperty1Fift != null and extProperty1Fift != ''">
               AND (t1.ext_property2 = #{extProperty1Fift} AND t1.award_type = 1)
           </when>
           <when test="extProperty1Integral != null and extProperty1Integral != ''">
               AND (t1.ext_property2 = #{extProperty1Integral} AND t1.award_type = 2)
           </when>
        </choose>
		<if test="awardType != null">
			AND t1.award_type = #{awardType}
		</if>
		<if test="sourceType != null and sourceType != ''">
			AND t1.source_type = #{sourceType}
		</if>
		<if test="deliveryStatus != null and deliveryStatus != ''">
			AND t1.delivery_status = #{deliveryStatus}
		</if>
		<if test="promotionDesc != null and promotionDesc != '' ">
			AND t1.promotion_desc like '%' + #{promotionDesc} + '%'
		</if>
		<if test="extProperty1 != null and extProperty1 != ''">
			AND t1.ext_property1 like '%'+ #{extProperty1} +'%'
		</if>
					GROUP BY t1.award_type, t1.promotion_id, t1.source_type, t1.delivery_status,t1.delivery_time, 
						t1.offer_key,t1.ext_property7, t1.delivery_status,t1.ext_property9) xx1
		LEFT JOIN (SELECT MIN(tt1.delivery_time) AS startDateMin,
		MAX(tt1.delivery_time) AS endDateMax from wx_t_promotion_delivery_detail tt1
		WHERE 1=1
		<if test="promotionId != null and promotionId != ''">
			AND tt1.promotion_id  = (select isnull(sp1.source_id, sp1.id) from wx_t_sell_in_promotion sp1 where sp1.id=#{promotionId})
		</if>
		<if test="promotionIds != null and promotionIds != ''">
			AND tt1.promotion_id  in (select isnull(sp1.source_id, sp1.id) from wx_t_sell_in_promotion sp1 where sp1.id in( select * from SplitIn(#{promotionIds}, ',')))
		</if>

		<choose>
			<when test="extProperty1Fift != null and extProperty1Fift != '' and extProperty1Integral != null and extProperty1Integral != ''">
				AND ((tt1.ext_property2 = #{extProperty1Fift} AND tt1.award_type = 1) OR (tt1.ext_property2 = #{extProperty1Integral} AND tt1.award_type = 2))
			</when>
			<when test="extProperty1Fift != null and extProperty1Fift != ''">
				AND (tt1.ext_property2 = #{extProperty1Fift} AND tt1.award_type = 1)
			</when>
			<when test="extProperty1Integral != null and extProperty1Integral != ''">
				AND (tt1.ext_property2 = #{extProperty1Integral} AND tt1.award_type = 2)
			</when>
		</choose>
		<if test="awardType != null">
			AND tt1.award_type = #{awardType}
		</if>
		<if test="sourceType != null and sourceType != ''">
			AND tt1.source_type = #{sourceType}
		</if>
		<if test="deliveryStatus != null and deliveryStatus != ''">
			AND tt1.delivery_status = #{deliveryStatus}
		</if>
		<if test="promotionDesc != null and promotionDesc != '' ">
			AND tt1.promotion_desc like '%' + #{promotionDesc} + '%'
		</if>
		<if test="extProperty1 != null and extProperty1 != ''">
			AND tt1.ext_property1 like '%'+ #{extProperty1} +'%'
		</if>
		)tt on 1=1
				left join wx_t_sell_in_promotion_delivery del on del.distributor_id=xx1.ext_property7
				and exists (select 1 from wx_t_promotion_delivery_detail t1
						LEFT JOIN wx_t_delivery_order_rel dor ON t1.id = dor.delivery_detail_id
						where t1.offer_key=xx1.offer_key and t1.ext_property7=xx1.ext_property7 and t1.ext_property9=xx1.ext_property9 and del.delivery_id=dor.delivery_id)
				LEFT JOIN wx_t_product p1 ON p1.sku = del.product_sku
				LEFT JOIN wx_t_sell_in_promotion p ON p.id = xx1.promotion_id
			WHERE 1=1
		<if test="promotionName != null and promotionName != ''">
			AND p.promotion_name like '%' + #{promotionName} + '%'
		</if>
		<if test="productSku != null and productSku != ''">
			AND del.product_sku like '%' + #{productSku} + '%'
		</if>
		<if test="distributorId != null and distributorId != ''">
			AND del.distributor_id = #{distributorId}
			<!-- AND EXISTS (SELECT  1 FROM wx_t_partner_o2o_enterprise where partner_id =  #{distributorId} and  distributor_id = del.distributor_id) -->
		</if>
		<if test="partnerId != null and partnerId != ''">
			<!-- AND del.distributor_id = #{distributorId} -->
			AND EXISTS (SELECT  1 FROM wx_t_partner_o2o_enterprise where partner_id =  #{partnerId} and  distributor_id = del.distributor_id)
		</if>
		<if test="salesOrder != null and salesOrder != ''">
			AND del.sales_order like '%' + #{salesOrder} + '%'
		</if>
		<if test="postYearMonthStart != null and postYearMonthStart != ''">
			AND del.post_year_month &gt;= #{postYearMonthStart}
		</if>
		<if test="postYearMonthEnd != null and postYearMonthEnd !=''">
			AND del.post_year_month &lt;= #{postYearMonthEnd}
		</if>
		<if test="productName != null and productName != ''">
			AND EXISTS (SELECT DISTINCT sku FROM wx_t_product where name like '%'+#{productName}+'%' AND del.product_sku = sku)
		</if>
		<if test="customerName != null and customerName != ''">
			AND EXISTS (SELECT DISTINCT distributor_id FROM PP_MID.dbo.syn_dw_to_pp_customer_org_sales where customer_name_cn like '%'+ #{customerName} +'%' AND del.distributor_id = distributor_id)
		</if>
			GROUP BY del.distributor_id, del.product_sku, xx1.award_type, xx1.promotion_id, xx1.source_type, del.region, xx1.delivery_status,xx1.delivery_time, xx1.offer_key,tt.startDateMin,tt.endDateMax
	</select>

    <insert id="insertPointAccountByDeliveries" parameterType="map">
INSERT INTO [dbo].[wx_t_point_account]([POINT_ACCOUNT_TYPE],[POINT_ACCOUNT_OWNER_ID],[IS_ENABLED],
[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY]) 
select distinct 'SP',pe.partner_id,1,0,#{deliveryTime, jdbcType=TIMESTAMP},1,getdate(),1  from wx_t_promotion_delivery_detail pdd
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
where pdd.award_type=2 and pdd.source_type='delivery' and pdd.delivery_status=10 
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
and not exists (select 1 from [wx_t_point_account] pa where pa.[POINT_ACCOUNT_OWNER_ID]=pe.partner_id and pa.[POINT_ACCOUNT_TYPE]='SP')
	</insert>
	<insert id="insertPointBusinessByDeliveries" parameterType="map">
INSERT INTO [dbo].[wx_t_point_business] ([BUSINESS_TYPE_CODE],[RELATED_CODE],[BUSINESS_DESC],[BUSINESS_STATUS],
[DELETE_FLAG], [CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY],ATTRIBUTE1,ATTRIBUTE2,ATTRIBUTE3,ATTRIBUTE4)  
select 'SELL_IN_PROMOTION_DELIVERY_V2', #{deliveryBatchNo} + '/' + pdd.ext_property7 + '/' + convert(varchar(20), pdd.promotion_id) + '/' + pdd.ext_property2, 
'下单促销奖励', 'DONE',0,#{deliveryTime, jdbcType=TIMESTAMP},1,getdate(),1,
max(sp.promotion_name),pdd.ext_property2,pp.point_type,#{deliveryBatchNo}
from wx_t_promotion_delivery_detail pdd
left join wx_t_promotion_point pp on pdd.ext_property2=pp.point_code
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
left join wx_t_sell_in_promotion sp on sp.id=pdd.promotion_id
where pdd.award_type=2 and pdd.source_type='delivery' and pdd.delivery_status=10 
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
			<foreach collection="ids" item="item" separator=",">
				#{item, jdbcType=BIGINT}
			</foreach>
	</trim>
</if>
group by pdd.ext_property2, pdd.promotion_id, pdd.ext_property7,pp.point_type
</insert>
	<insert id="insertPointDetailByDeliveries" parameterType="map">
INSERT INTO [dbo].[wx_t_point_value_detail] ([POINT_TYPE],[POINT_ACCOUNT_ID],[POINT_VALUE],[POINT_PAYED],
[BUSINESS_ID],[COMMENTS],[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY]) 
select pdd.ext_property2, pa.ID, sum(pdd.award_quantity), 0, pb.id, 
'下单促销奖励', 0,
#{deliveryTime, jdbcType=TIMESTAMP},1,getdate(),1 
from wx_t_promotion_delivery_detail pdd
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
left join [wx_t_point_account] pa on pa.[POINT_ACCOUNT_OWNER_ID]=pe.partner_id and pa.[POINT_ACCOUNT_TYPE]='SP'
left join wx_t_point_business pb on pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' 
	and pb.RELATED_CODE=#{deliveryBatchNo} + '/' + pdd.ext_property7 + '/' + convert(varchar(20), pdd.promotion_id) + '/' + pdd.ext_property2 
where pdd.award_type=2 and pdd.source_type='delivery' and pdd.delivery_status=10 
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
group by pdd.ext_property2, pdd.promotion_id, pdd.ext_property7, pa.ID,pb.id
	</insert>
	<insert id="insertPointDetailLogByDeliveries" parameterType="map">
INSERT INTO [dbo].[wx_t_point_value_detail_log]([POINT_VALUE_ID],[MODIFIED_VALUE],[POINT_ACCOUNT_ID],
[BUSINESS_ID],[COMMENTS],[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY]) 
select pvd.ID, sum(pdd.award_quantity), pa.ID, pb.id, 
'下单促销奖励', 
0,#{deliveryTime, jdbcType=TIMESTAMP},1,getdate(),1 
from wx_t_promotion_delivery_detail pdd
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
left join [wx_t_point_account] pa on pa.[POINT_ACCOUNT_OWNER_ID]=pe.partner_id and pa.[POINT_ACCOUNT_TYPE]='SP'
left join wx_t_point_business pb on pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' 
	and pb.RELATED_CODE=#{deliveryBatchNo} + '/' + pdd.ext_property7 + '/' + convert(varchar(20), pdd.promotion_id) + '/' + pdd.ext_property2
left join [wx_t_point_value_detail] pvd on pvd.[BUSINESS_ID]=pb.id
where pdd.award_type=2 and pdd.source_type='delivery' and pdd.delivery_status=10 
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
group by pdd.ext_property2, pdd.promotion_id, pdd.ext_property7, pa.ID,pb.id,pvd.ID
	</insert>
	
	<insert id="initGiftPoolByDeliveries" parameterType="map">
insert into [wx_t_promotion_gift_pool] ([distributor_id]
      ,[partner_id]
      ,[application_type]
      ,point_code
      ,[available_quantity]
      ,[material_id]
      ,[material_code]
      ,[ext_property1]
      ,[create_user_id]
      ,[create_time])
select distinct pe.[distributor_id]
      ,pe.[partner_id]
      ,pp.point_type [application_type]
      ,pp.point_code
      ,0 [available_quantity]
      ,-1 [ID]
      ,pdd.ext_property2 [material_code] 
      ,pdd.ext_property8 [ext_property1]
	  ,#{createUserId, jdbcType=BIGINT} [create_user_id]
      ,#{deliveryTime, jdbcType=TIMESTAMP} [create_time]
from wx_t_promotion_delivery_detail pdd
left join wx_t_promotion_point pp on pdd.ext_property3=pp.point_code
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
where pdd.award_type=1 and pdd.source_type='delivery' and pdd.delivery_status=10
and not exists (select 1 from [wx_t_promotion_gift_pool] pgp 
		where pgp.application_type=pp.point_type and pgp.point_code=pp.point_code
		and pgp.[partner_id]=pe.partner_id and pgp.material_id=-1 and pgp.material_code=pdd.ext_property2)
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
	</insert>

	<insert id="insertGiftPoolLogByDeliveries" parameterType="map">
insert into [wx_t_promotion_gift_modify_log] ([pool_id]
      ,[modify_time]
      ,[modify_type]
      ,[modify_quantity]
      ,[business_type]
      ,[business_key]
      ,[comments]
      ,[ext_property1]
      ,[ext_property2]
      ,[create_user_id]
      ,[create_time])
select pgp.id [pool_id]
      ,#{deliveryTime, jdbcType=TIMESTAMP} [modify_time]
      ,'modifyTypeAdd' [modify_type]
      ,sum(pdd.award_quantity) [modify_quantity]
      ,'SELL_IN_PROMOTION_DELIVERY_V2' [business_type]
      ,#{deliveryBatchNo} + '/' + pdd.ext_property7 + '/' + convert(varchar(20), pdd.promotion_id) + '/' + pdd.ext_property2 [business_key]
      ,'下单促销奖励' [comments]
      ,#{deliveryBatchNo} [ext_property1]
      ,max(sp.promotion_name) [ext_property2]
      ,#{createUserId, jdbcType=BIGINT} [create_user_id]
      ,getdate() [create_time]
from wx_t_promotion_delivery_detail pdd
left join wx_t_sell_in_promotion sp on sp.id=pdd.promotion_id
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
left join [wx_t_promotion_gift_pool] pgp on pgp.partner_id=pe.partner_id
	and pgp.material_id=-1 and pgp.material_code=pdd.ext_property2 and pgp.point_code=pdd.ext_property3 and pgp.ext_property1=pdd.ext_property8
where pdd.award_type=1 and pdd.source_type='delivery' and pdd.delivery_status=10
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
group by pdd.ext_property2, pdd.promotion_id, pdd.ext_property7,pgp.id
	</insert>
	<insert id="insertGiftPoolUpdateLogByDeliveries" parameterType="map">
insert into wx_t_pro_gift_pool_mod_log ([gift_pool_id]
      ,[value_before]
      ,[value_after]
      ,[value_change]
      ,business_type
      ,business_key
      ,[create_user_id]
      ,[create_time])
select pgp.id, pgp.available_quantity,pgp.available_quantity+x.modify_quantity,x.modify_quantity,
'SELL_IN_PROMOTION_DELIVERY_V2' business_type,#{deliveryBatchNo} business_key,
#{createUserId, jdbcType=BIGINT},#{deliveryTime, jdbcType=TIMESTAMP}
from [wx_t_promotion_gift_pool] pgp join (
select l.pool_id, sum(l.modify_quantity) modify_quantity from [wx_t_promotion_gift_modify_log] l
where l.business_type='SELL_IN_PROMOTION_DELIVERY_V2' and l.ext_property1=#{deliveryBatchNo}
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
group by l.pool_id ) x on pgp.id=x.pool_id
	</insert>
	<update id="updateGiftPoolByDeliveries" parameterType="map">
update pgp set pgp.available_quantity=pgp.available_quantity+x.modify_quantity,pgp.update_user_id=#{createUserId, jdbcType=BIGINT},pgp.update_time=getdate()
from [wx_t_promotion_gift_pool] pgp join (
select l.pool_id , sum(l.modify_quantity) modify_quantity from [wx_t_promotion_gift_modify_log] l
where l.business_type='SELL_IN_PROMOTION_DELIVERY_V2' and l.ext_property1=#{deliveryBatchNo}
<if test="ids != null">
and pdd.id in
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<foreach collection="ids" item="item" separator=",">
			#{item, jdbcType=BIGINT}
		</foreach>
	</trim>
</if>
group by l.pool_id ) x on pgp.id=x.pool_id
	</update>
	
    <select id="checkPromotionDelivery" resultType="int" parameterType="map">
        select (case when isnull((select sum(d.POINT_VALUE)
					from wx_t_point_value_detail d 
					where exists (select 1 from dbo.wx_t_point_business pb
						where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' and pb.id=d.BUSINESS_ID and pb.ATTRIBUTE4=#{deliveryBatchNo})),0)=isnull((
							select sum(pdd.award_quantity)
							from wx_t_promotion_delivery_detail pdd
							where pdd.award_type=2 and pdd.source_type='delivery' and pdd.ext_property5=#{deliveryBatchNo}),0) then 0 else 1 end) + 
				(case when isnull((select sum(l.value_change)
					from wx_t_pro_gift_pool_mod_log l 
					where l.business_type='SELL_IN_PROMOTION_DELIVERY_V2' and l.business_key=#{deliveryBatchNo}),0)=isnull((
							select sum(pdd.award_quantity)
							from wx_t_promotion_delivery_detail pdd
							where pdd.award_type=1 and pdd.source_type='delivery' and pdd.ext_property5=#{deliveryBatchNo}),0) then 0 else 2 end)			
    </select>
    
  <update id="synExpense" parameterType="map" statementType="CALLABLE">
    {call pp_sp_process_points_to_expense_promotion(#{batchNo})} 
  </update>
	
	<insert id="insertMidPromotionPoints" parameterType="map">
insert into [PP_MID].[dbo].[bit_abp_promotion_point] ([sales_order]
      ,[sold_to_code]
      ,[product_code]
      ,[region]
      ,[promotion_points]
      ,[posting_year_month]
      ,[delete_flag]
      ,[create_time]
      ,[update_time])
select pd.sales_order, pd.[sold_to_code], pd.product_sku, pd.region, pdd.award_quantity,#{deliveryTime, jdbcType=TIMESTAMP}, 0, getdate(), getdate()
from wx_t_promotion_delivery_detail pdd
left join wx_t_sell_in_promotion_delivery pd on pdd.source_id=pd.delivery_id
where pdd.award_type=2 and pdd.source_type='delivery' and pdd.delivery_status=20 and pdd.ext_property3='1' 
and pdd.ext_property5=#{deliveryBatchNo}
	</insert>

    <select id="getDeliveryMsgPushUser" resultMap="com.sys.auth.dao.WxTUserMapper.BaseResultMap">
        select DISTINCT c.* from wx_t_promotion_delivery_detail a inner join wx_t_partner_o2o_enterprise b on CAST (a.ext_property7 as int) = b.distributor_id
        inner join wx_t_user c on c.org_id = b.partner_id  and c.status = 1
        inner join wx_t_userrole d on c.user_id = d.user_id and d.status  = 1
        inner join wx_t_role e on e.role_id = d.role_id
        inner join wx_t_dic_item f on f.dic_item_code = e.ch_role_name and f.dic_type_code = 'promotion.delivery.msg.role'
        where a.source_type='delivery' and a.delivery_status=10
        <if test="ids != null">
            and a.id in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="ids" item="item" separator=",">
                    #{item, jdbcType=BIGINT}
                </foreach>
            </trim>
        </if>
    </select>

    <select id="getStockDeliveryDetail" resultMap="BaseResultDeliveryMap">
        select SUBSTRING(t2.sales_reference_doc,2,len(t2.sales_reference_doc)) as sales_order,t2.product_code_SAP as product_sku,t3.name as product_name,t2.pricing_date,t2.trans_time as post_year_month,
        (t2.liters-t2.below_standard_price_liters_business -t2.free_gift_liters_business) as quantity,t.pending_score as point from wx_t_point_pending_record t inner join
        [PP_MID].[dbo].[syn_dw_to_pp_sap_sell_in] t2 on t.bi_sellin_line_id = t2.base_trans_sell_in_id
        left join wx_t_product t3 on t3.sku = t2.product_code_SAP
        where t.partner_id = #{partnerId} and t.point_business_id = #{businessId}
    </select>

	<resultMap id="LeftPromotionResultMap" type="com.chevron.sellin.model.PartnerPromotionVo">
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
		<result column="promotion_name" property="promotionName" jdbcType="VARCHAR"/>
		<result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
		<result column="point_type" property="pointType" jdbcType="VARCHAR"/>
		<result column="point_code" property="pointCode" jdbcType="VARCHAR"/>
		<result column="point_name" property="pointName" jdbcType="VARCHAR"/>
		<result column="balance" property="balance" jdbcType="NUMERIC"/>
		<result column="type" property="type" jdbcType="VARCHAR"/>
		<result column="promotion_flag" property="promotionFlag"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="predefine_flag" property="predefineFlag"/>
		<result column="coupon_count" property="couponCount"/>
		<result column="retailer_flag" property="retailerFlag"/>
	</resultMap>

	<select id="getLeftPromotionPointByPartner" resultMap="LeftPromotionResultMap">
		select 'point' as [type],t4.id as partner_id,
		<if test="params.byRetailer == null or params.byRetailer == false">
			(case when t4.type=3 then r1o1.organization_name + ' - ' else '' end) +
		</if>
		t4.organization_name as partner_name,
		sum(t3.POINT_VALUE-t3.POINT_PAYED) as balance,t5.point_type,
		case when t4.type=3 then 1 else 0 end retailer_flag from
		wx_t_point_account t1
		inner join wx_t_partner_o2o_enterprise t2 on t1.POINT_ACCOUNT_OWNER_ID = t2.partner_id
		left join wx_t_organization t4 on t2.partner_id  = t4.id
		left join wx_t_organization r1o1 on t4.type=3 and r1o1.id=t2.ext_property1
		left join wx_t_point_value_detail t3 on t1.id = t3.POINT_ACCOUNT_ID
		inner join wx_t_promotion_point t5 on t5.point_code = t3.POINT_TYPE
		where t1.DELETE_FLAG != 'true'
		and t5.POINT_TYPE in ('cdm_promotion','promotion')
		and t1.POINT_ACCOUNT_TYPE = 'SP'
		and t3.DELETE_flag = 0
		<choose>
			<when test="params.distributorIds!=null and params.distributorIds.size > 0">
				and t2.distributor_id in
				<foreach collection="params.distributorIds" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
			</when>
			<otherwise>
				and 1!=1
			</otherwise>
		</choose>
		group by t4.id,t4.organization_name,t5.point_type,t4.type,r1o1.organization_name
	</select>


</mapper>
