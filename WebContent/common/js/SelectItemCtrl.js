SelectItemCtrl = function($render, dialogContent, dialogTitle, url, gridRender, columns, opts){
	if(opts){
		$.extend(this, opts);
	}
	var self = this;
	self.updateRender($render);
	if(this.selectedItems){
		this.setSelectedItems(this.selectedItems);
	}else{
		this._selectedStatusMap = {};
		this.selectedItems = [];
	}
	if(this.remotePage){
		this.handler = this.handlers.remote;
	}else{
		this.handler = this.handlers.local;
	}
	this.dialogContent = dialogContent;
	this.gridRender = gridRender;
	this.dialogTitle = dialogTitle;
	this.url = url;
	this.columns = columns;
}

SelectItemCtrl.prototype = {
		_selectedStatusMap: null,
		_render: null,
		remotePage: false, //是否后端分页
		remoteStoreRoot: 'resultLst',
		valueField: 'id',
		textField: 'text',
		selectedField: 'checked',
		searchField: null,
		columns: null,
		_grid: null,
		_dialog: null,
		buildButtons: function(){
			var self = this;
			return [{
				text: '确认',
				elCls: 'button btn-create',
				handler: function () {
					self.dialogSubmit();
					self.closeDialog();
				}
			},
			{
				text: "取消",
				elCls: 'button btn-cancel',
				handler: function () {
					self.closeDialog();
				}
			}];
		},
		updateRender: function($render){
			if(this._render && this._render[0] == $render[0]){
				return;
			}
			var self = this;
			self._render = $render.on('click', function(e){
				var $t = $(e.target);
				if($t.hasClass('select-item-close')){
					var itemEl = $t.parents('.selected-item:first');
					self._removeSelectedItem(itemEl, itemEl.attr('value'));
				}
			});
		},
		closeDialog: function(){
			this._dialog.close();
		},
		/**
		 * 外部调用弹出对话框，选择备选项
		 */
		selectItems: function(params){
			if(this._dialog == null){
				this.beforeCreateDialog(params);
				this.createDialog(params);
			}else{
				this.refreshGrid(params);
			}
			this._dialog.show();
		},
		refreshGrid: function(params){
			this.handler.refreshGrid.call(this, params);
		},
		getGridDataParams: function(){
			return [];
		},
		beforeCreateDialog: function(params){

		},
		createDialog: function(){
			var self = this, dialogOpts = {
					title: self.dialogTitle,
					width: 600,
					bodyContent: self.dialogContent,
					buttons: self.buildButtons()
			};
			if(this.dialogOpts){
				dialogOpts = $.extend(dialogOpts, this.dialogOpts);
			}
			BUI.use('bui/overlay', function (Overlay) {
				self._dialog = new Overlay.Dialog(dialogOpts);
				self._dialog.render();
			});
			this.buildItems();
		},
		setSelectedItems: function(selectedItems){
			this.selectedItems = selectedItems;
			this._selectedStatusMap = {};
			this._render.empty();
			if(this.selectedItems){
				var hs = [];
				for(var i = 0; i < this.selectedItems.length; i++){
					this._addSelectedItem(this.selectedItems[i], hs);
					this._selectedStatusMap[this.selectedItems[i][this.valueField]] = this.selectedItems[i];
				}
				this._render.append(hs.join(''));
			}else{
				this.selectedItems = [];
			}
		},
		dialogSubmit: function(){
			this.handler.dialogSubmit.call(this);
		},
		handlers: {
			//后端分页处理器
			remote: {
				dialogSubmit: function(){
					var oldSelectedStatusMap = {};
					for(var i = 0; i < this.selectedItems.length; i++){
						oldSelectedStatusMap[this.selectedItems[i][this.valueField]] = this.selectedItems[i];
					}
					for(var k in this._selectedStatusMap){
						if(this._selectedStatusMap[k] && !oldSelectedStatusMap[k]){
							this.selectedItems.push(this._selectedStatusMap[k]);
							this._addSelectedItem(this._selectedStatusMap[k]);
						}else if(!this._selectedStatusMap[k] && oldSelectedStatusMap[k]){
							this._selectedStatusMap[k] = false;
							this._removeSelectedItem(null, k);
						}
					}
				},
				refreshGrid: function(params){
					this.store.load($.extend({}, params, {start: 0}));
				}
			},
			//前端分页处理器
			local: {
				dialogSubmit: function(){
					this.selectedItems = [];
					this._selectedStatusMap = {};
					var hs = [];
					for(var i = 0; i < this._data.length; i++){
						if(this._data[i][this.selectedField]){
							this.selectedItems.push(this._data[i]);
							this._addSelectedItem(this._data[i], hs);
							this._selectedStatusMap[this._data[i][this.valueField]] = true;
						}
					}
					this._render.empty();
					this._render.append(hs.join(''));
				},
				refreshGrid: function(){
					this._updateSelectedStatus();
					this.store.setResult(this._data);
				}
			}
		},
		/**
		 * 表格初始化完成事件（内部初始化表格事件）
		 */
		afterGridInited: function(grid){
			
		},
		/**
		 * 构建备选数据
		 */
		buildItems: function(){
			var self = this;
			BUI.use(['bui/grid', 'bui/data'],function(Grid, Data){
				var storeOpts = {
						pageSize: 10
				};
				if(self.remotePage){
					storeOpts = $.extend(storeOpts, {
						url: common.ctx + self.url,
						autoLoad:false,
						totalProperty : 'total',
						root: self.remoteStoreRoot,
						remoteSort: true,
						pageSize:10,
						proxy : {
							method : 'post'
						}
					});
				}else{
					storeOpts = $.extend(storeOpts, {
						data: []
					});
				}
				if(self.storeOpts){
					storeOpts = $.extend(storeOpts, self.storeOpts);
				}
				self.store = new Data.Store(storeOpts);
				var gridOpts = {
						render: self.gridRender,
						columns : self.columns,
						height: 250,
						store : self.store,
						itemStatusFields : {
							selected: self.selectedField
						},
						bbar: {
							pagingBar: true
						},
						plugins : [Grid.Plugins.CheckSelection,Grid.Plugins.ColumnChecked]
					};
				if(self.gridOpts){
					gridOpts = $.extend(gridOpts, self.gridOpts);
				};
				self._grid = new Grid.Grid(gridOpts);
				self._grid.render();
				$(self.gridRender).find('.bui-grid-hd-empty').css('width', '17px');
				common.initGrid(self._grid, null, false);
				if(!self.remotePage){
					//前端分页
					LoadMask.show();
					common.rpcClient.call(self.url, self.getGridDataParams(),
							function (result) {
						LoadMask.hide();
						self.processGridData(result);
					}, function (error) {
						LoadMask.hide();
						common.ajaxTimeout(error);
					});
				}else{
					//后端分页，添加预处理数据
					self.store.on('beforeprocessload', function(e){
						self.remoteDataPreprocess(e.data);
					});
					//实时处理选中行
					self._grid.on("itemselected", function(e){self._selectedStatusMap[e.item[self.valueField]] = e.item;});
					self._grid.on("itemunselected", function(e){self._selectedStatusMap[e.item[self.valueField]] = false;});
				}
				self.afterGridInited(self._grid);
			});
		},
		processGridData: function(result){
			if(result.success){
				this._data = result.data;
				this._updateSelectedStatus();
				this.store.setResult(result.data);
			}else{
				this._data = [];
				this.store.setResult([]);
				common.alertMes(result.errorMsg, 'error');
			}
		},
		remoteDataPreprocess: function(result){
			if(result.code == "success"){
				this._data = result[this.remoteStoreRoot];
				this._updateSelectedStatus();
			}else{
				this._data = [];
				result[this.remoteStoreRoot] = [];
				common.alertMes(result.errorMsg, 'error');
			}
		},
		beforeBuildItems: function(){

		},
		/**
		 * 系统默认按照搜索文本过滤结果集
		 * @param searchText 搜索文本
		 */
		filter: function(searchText){
			if(searchText == ''){
				this.store.setResult(this._data);
				return;
			}
			if(this.searchField == null){
				this.searchField = [this.textField];
			}
			var result = [];

			if (this._data) {
				for(var i = 0; i < this._data.length; i++){
					for(var j = 0; j < this.searchField.length; j++){
						if(this._data[i][this.searchField[j]] && (this._data[i][this.searchField[j]] + '').indexOf(searchText) >= 0){
							result.push(this._data[i]);
							break;
						}
					}
				}
			}
			this.store.setResult(result);
		},
		_updateSelectedStatus: function(){
			if (this._data) {
				for(var i = 0; i < this._data.length; i++){
					this._data[i][this.selectedField] = this._selectedStatusMap[this._data[i][this.valueField]];
				}
			}
		},
		_addSelectedItem: function(item, hs){
			var itemHtml = ['<span class="selected-item selected-item-', item[this.valueField], '" value="', item[this.valueField],
							'"><span>', item[this.textField], '</span><a class="select-item-close" href="javascript: void(0);">&nbsp;</a></span>'].join('');
			if(hs){
				//刷新选中项显示区
				hs.push(itemHtml);
			}else{
				//更新选中项显示区
				this._render.append(itemHtml);
			}
		},
		_removeSelectedItem: function(itemEl, value){
			if(!itemEl){
				itemEl = this._render.find('.selected-item-' + value);
			}
			for(var i = 0; i < this.selectedItems.length; i++){
				if(this.selectedItems[i][this.valueField] == value){
					this._selectedStatusMap[this.selectedItems[i][this.valueField]] = false;
					this.selectedItems.splice(i, 1);
					break;
				}
			}
			itemEl.remove();
		}
}