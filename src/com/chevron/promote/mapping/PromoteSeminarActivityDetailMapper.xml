<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promote.dao.PromoteSeminarActivityDetailMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promote.model.detail.PromoteSeminarActivityDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="invited_customers_count" property="invitedCustomersCount" jdbcType="INTEGER" />
    <result column="seminar_purpose" property="seminarPurpose" jdbcType="VARCHAR" />
    <result column="seminar_content" property="seminarContent" jdbcType="VARCHAR" />
    <result column="b2b_wechat_platforms_count" property="b2bWechatPlatformsCount" jdbcType="INTEGER" />
    <result column="estimated_order_quantity" property="estimatedOrderQuantity" jdbcType="INTEGER" />
    <result column="apply_packs_count" property="applyPacksCount" jdbcType="INTEGER" />
    <result column="venue_meal" property="venueMeal" jdbcType="NUMERIC" />
    <result column="activity_meal" property="activityMeal" jdbcType="NUMERIC" />
    
    <result column="a14_apply_form" property="a14ApplyForm" jdbcType="VARCHAR" />
    <result column="compliance_invoice" property="complianceInvoice" jdbcType="VARCHAR" />
    <result column="conference_schedule" property="conferenceSchedule" jdbcType="VARCHAR" />
    <result column="check_in_slip" property="checkInSlip" jdbcType="VARCHAR" />
    <result column="conference_photo" property="conferencePhoto" jdbcType="VARCHAR" />
    <result column="hotel_fee_statement" property="hotelFeeStatement" jdbcType="VARCHAR" />
    <result column="tripartite_agreement" property="tripartiteAgreement" jdbcType="VARCHAR" />
    <result column="proof_purchase" property="proofPurchase" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="apply_points" property="applyPoints" jdbcType="INTEGER" />
    
    <result column="apply_high_packs" property="applyHighPacks" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, activity_id, activity_type, invited_customers_count, seminar_purpose, seminar_content, 
    b2b_wechat_platforms_count, estimated_order_quantity, apply_packs_count, venue_meal, 
    a14_apply_form, compliance_invoice, conference_schedule, check_in_slip, conference_photo, 
    hotel_fee_statement, tripartite_agreement, proof_purchase, remark,activity_meal, apply_points,
    apply_high_packs
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promote.model.PromoteSeminarActivityDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_promote_seminar_activity_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_seminar_activity_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_seminar_activity_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promote.model.PromoteSeminarActivityDetailExample" >
    delete from wx_promote_seminar_activity_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promote.model.detail.PromoteSeminarActivityDetail" >
    insert into wx_promote_seminar_activity_detail (id, activity_id, activity_type, 
      invited_customers_count, seminar_purpose, 
      seminar_content, b2b_wechat_platforms_count, 
      estimated_order_quantity, apply_packs_count, 
      venue_meal, a14_apply_form, compliance_invoice, 
      conference_schedule, check_in_slip, conference_photo, 
      hotel_fee_statement, tripartite_agreement, 
      proof_purchase, remark, activity_meal, apply_points, apply_high_packs)
    values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, #{activityType,jdbcType=VARCHAR}, 
      #{invitedCustomersCount,jdbcType=INTEGER}, #{seminarPurpose,jdbcType=VARCHAR}, 
      #{seminarContent,jdbcType=VARCHAR}, #{b2bWechatPlatformsCount,jdbcType=INTEGER}, 
      #{estimatedOrderQuantity,jdbcType=INTEGER}, #{applyPacksCount,jdbcType=INTEGER}, 
      #{venueMeal,jdbcType=NUMERIC}, #{a14ApplyForm,jdbcType=VARCHAR}, #{complianceInvoice,jdbcType=VARCHAR}, 
      #{conferenceSchedule,jdbcType=VARCHAR}, #{checkInSlip,jdbcType=VARCHAR}, #{conferencePhoto,jdbcType=VARCHAR}, 
      #{hotelFeeStatement,jdbcType=VARCHAR}, #{tripartiteAgreement,jdbcType=VARCHAR}, 
      #{proofPurchase,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{activityMeal,jdbcType=NUMERIC}, #{applyPoints,jdbcType=INTEGER}
      , #{applyHighPacks,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.promote.model.detail.PromoteSeminarActivityDetail" >
    insert into wx_promote_seminar_activity_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="activityId != null" >
        activity_id,
      </if>
      <if test="activityType != null" >
        activity_type,
      </if>
      <if test="invitedCustomersCount != null" >
        invited_customers_count,
      </if>
      <if test="seminarPurpose != null" >
        seminar_purpose,
      </if>
      <if test="seminarContent != null" >
        seminar_content,
      </if>
      <if test="b2bWechatPlatformsCount != null" >
        b2b_wechat_platforms_count,
      </if>
      <if test="estimatedOrderQuantity != null" >
        estimated_order_quantity,
      </if>
      <if test="applyPacksCount != null" >
        apply_packs_count,
      </if>
      <if test="venueMeal != null" >
        venue_meal,
      </if>
      <if test="activityMeal != null" >
        activity_meal,
      </if>
      <if test="a14ApplyForm != null" >
        a14_apply_form,
      </if>
      <if test="complianceInvoice != null" >
        compliance_invoice,
      </if>
      <if test="conferenceSchedule != null" >
        conference_schedule,
      </if>
      <if test="checkInSlip != null" >
        check_in_slip,
      </if>
      <if test="conferencePhoto != null" >
        conference_photo,
      </if>
      <if test="hotelFeeStatement != null" >
        hotel_fee_statement,
      </if>
      <if test="tripartiteAgreement != null" >
        tripartite_agreement,
      </if>
      <if test="proofPurchase != null" >
        proof_purchase,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="applyPoints != null" >
        apply_points,
      </if>
      <if test="applyHighPacks != null" >
        apply_high_packs,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null" >
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityType != null" >
        #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="invitedCustomersCount != null" >
        #{invitedCustomersCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPurpose != null" >
        #{seminarPurpose,jdbcType=VARCHAR},
      </if>
      <if test="seminarContent != null" >
        #{seminarContent,jdbcType=VARCHAR},
      </if>
      <if test="b2bWechatPlatformsCount != null" >
        #{b2bWechatPlatformsCount,jdbcType=INTEGER},
      </if>
      <if test="estimatedOrderQuantity != null" >
        #{estimatedOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="applyPacksCount != null" >
        #{applyPacksCount,jdbcType=INTEGER},
      </if>
      <if test="venueMeal != null" >
        #{venueMeal,jdbcType=NUMERIC},
      </if>
       <if test="activityMeal != null" >
        #{activityMeal,jdbcType=NUMERIC},
      </if>
      <if test="a14ApplyForm != null" >
        #{a14ApplyForm,jdbcType=VARCHAR},
      </if>
      <if test="complianceInvoice != null" >
        #{complianceInvoice,jdbcType=VARCHAR},
      </if>
      <if test="conferenceSchedule != null" >
        #{conferenceSchedule,jdbcType=VARCHAR},
      </if>
      <if test="checkInSlip != null" >
        #{checkInSlip,jdbcType=VARCHAR},
      </if>
      <if test="conferencePhoto != null" >
        #{conferencePhoto,jdbcType=VARCHAR},
      </if>
      <if test="hotelFeeStatement != null" >
        #{hotelFeeStatement,jdbcType=VARCHAR},
      </if>
      <if test="tripartiteAgreement != null" >
        #{tripartiteAgreement,jdbcType=VARCHAR},
      </if>
      <if test="proofPurchase != null" >
        #{proofPurchase,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyPoints != null" >
        #{applyPoints,jdbcType=INTEGER},
      </if>
      <if test="applyHighPacks != null" >
        #{applyHighPacks,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_promote_seminar_activity_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.activityId != null" >
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.activityType != null" >
        activity_type = #{record.activityType,jdbcType=VARCHAR},
      </if>
      <if test="record.invitedCustomersCount != null" >
        invited_customers_count = #{record.invitedCustomersCount,jdbcType=INTEGER},
      </if>
      <if test="record.seminarPurpose != null" >
        seminar_purpose = #{record.seminarPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.seminarContent != null" >
        seminar_content = #{record.seminarContent,jdbcType=VARCHAR},
      </if>
      <if test="record.b2bWechatPlatformsCount != null" >
        b2b_wechat_platforms_count = #{record.b2bWechatPlatformsCount,jdbcType=INTEGER},
      </if>
      <if test="record.estimatedOrderQuantity != null" >
        estimated_order_quantity = #{record.estimatedOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.applyPacksCount != null" >
        apply_packs_count = #{record.applyPacksCount,jdbcType=INTEGER},
      </if>
      <if test="record.venueMeal != null" >
        venue_meal = #{record.venueMeal,jdbcType=NUMERIC},
      </if>
      <if test="record.activityMeal != null" >
        activity_meal = #{record.activityMeal,jdbcType=NUMERIC},
      </if>
      <if test="record.a14ApplyForm != null" >
        a14_apply_form = #{record.a14ApplyForm,jdbcType=VARCHAR},
      </if>
      <if test="record.complianceInvoice != null" >
        compliance_invoice = #{record.complianceInvoice,jdbcType=VARCHAR},
      </if>
      <if test="record.conferenceSchedule != null" >
        conference_schedule = #{record.conferenceSchedule,jdbcType=VARCHAR},
      </if>
      <if test="record.checkInSlip != null" >
        check_in_slip = #{record.checkInSlip,jdbcType=VARCHAR},
      </if>
      <if test="record.conferencePhoto != null" >
        conference_photo = #{record.conferencePhoto,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelFeeStatement != null" >
        hotel_fee_statement = #{record.hotelFeeStatement,jdbcType=VARCHAR},
      </if>
      <if test="record.tripartiteAgreement != null" >
        tripartite_agreement = #{record.tripartiteAgreement,jdbcType=VARCHAR},
      </if>
      <if test="record.proofPurchase != null" >
        proof_purchase = #{record.proofPurchase,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.applyPoints != null" >
        apply_points = #{record.applyPoints,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_promote_seminar_activity_detail
    set id = #{record.id,jdbcType=BIGINT},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      activity_type = #{record.activityType,jdbcType=VARCHAR},
      invited_customers_count = #{record.invitedCustomersCount,jdbcType=INTEGER},
      seminar_purpose = #{record.seminarPurpose,jdbcType=VARCHAR},
      seminar_content = #{record.seminarContent,jdbcType=VARCHAR},
      b2b_wechat_platforms_count = #{record.b2bWechatPlatformsCount,jdbcType=INTEGER},
      estimated_order_quantity = #{record.estimatedOrderQuantity,jdbcType=INTEGER},
      apply_packs_count = #{record.applyPacksCount,jdbcType=INTEGER},
      venue_meal = #{record.venueMeal,jdbcType=NUMERIC},
      activity_meal = #{record.activityMeal,jdbcType=NUMERIC},
      a14_apply_form = #{record.a14ApplyForm,jdbcType=VARCHAR},
      compliance_invoice = #{record.complianceInvoice,jdbcType=VARCHAR},
      conference_schedule = #{record.conferenceSchedule,jdbcType=VARCHAR},
      check_in_slip = #{record.checkInSlip,jdbcType=VARCHAR},
      conference_photo = #{record.conferencePhoto,jdbcType=VARCHAR},
      hotel_fee_statement = #{record.hotelFeeStatement,jdbcType=VARCHAR},
      tripartite_agreement = #{record.tripartiteAgreement,jdbcType=VARCHAR},
      proof_purchase = #{record.proofPurchase,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      apply_points = #{record.applyPoints,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promote.model.detail.PromoteSeminarActivityDetail" >
    update wx_promote_seminar_activity_detail
    <set >
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="invitedCustomersCount != null" >
        invited_customers_count = #{invitedCustomersCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPurpose != null" >
        seminar_purpose = #{seminarPurpose,jdbcType=VARCHAR},
      </if>
      <if test="seminarContent != null" >
        seminar_content = #{seminarContent,jdbcType=VARCHAR},
      </if>
      <if test="b2bWechatPlatformsCount != null" >
        b2b_wechat_platforms_count = #{b2bWechatPlatformsCount,jdbcType=INTEGER},
      </if>
      <if test="estimatedOrderQuantity != null" >
        estimated_order_quantity = #{estimatedOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="applyPacksCount != null" >
        apply_packs_count = #{applyPacksCount,jdbcType=INTEGER},
      </if>
      <if test="venueMeal != null" >
        venue_meal = #{venueMeal,jdbcType=NUMERIC},
      </if>
      <if test="activityMeal != null" >
        activity_meal = #{activityMeal,jdbcType=NUMERIC},
      </if>
      <if test="a14ApplyForm != null" >
        a14_apply_form = #{a14ApplyForm,jdbcType=VARCHAR},
      </if>
      <if test="complianceInvoice != null" >
        compliance_invoice = #{complianceInvoice,jdbcType=VARCHAR},
      </if>
      <if test="conferenceSchedule != null" >
        conference_schedule = #{conferenceSchedule,jdbcType=VARCHAR},
      </if>
      <if test="checkInSlip != null" >
        check_in_slip = #{checkInSlip,jdbcType=VARCHAR},
      </if>
      <if test="conferencePhoto != null" >
        conference_photo = #{conferencePhoto,jdbcType=VARCHAR},
      </if>
      <if test="hotelFeeStatement != null" >
        hotel_fee_statement = #{hotelFeeStatement,jdbcType=VARCHAR},
      </if>
      <if test="tripartiteAgreement != null" >
        tripartite_agreement = #{tripartiteAgreement,jdbcType=VARCHAR},
      </if>
      <if test="proofPurchase != null" >
        proof_purchase = #{proofPurchase,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyPoints != null" >
        apply_points = #{applyPoints,jdbcType=INTEGER},
      </if>
      <if test="applyHighPacks != null" >
        apply_high_packs = #{applyHighPacks,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promote.model.detail.PromoteSeminarActivityDetail" >
    update wx_promote_seminar_activity_detail
    set activity_id = #{activityId,jdbcType=BIGINT},
      activity_type = #{activityType,jdbcType=VARCHAR},
      invited_customers_count = #{invitedCustomersCount,jdbcType=INTEGER},
      seminar_purpose = #{seminarPurpose,jdbcType=VARCHAR},
      seminar_content = #{seminarContent,jdbcType=VARCHAR},
      b2b_wechat_platforms_count = #{b2bWechatPlatformsCount,jdbcType=INTEGER},
      estimated_order_quantity = #{estimatedOrderQuantity,jdbcType=INTEGER},
      apply_packs_count = #{applyPacksCount,jdbcType=INTEGER},
      venue_meal = #{venueMeal,jdbcType=NUMERIC},
      activity_meal = #{activityMeal,jdbcType=NUMERIC},
      a14_apply_form = #{a14ApplyForm,jdbcType=VARCHAR},
      compliance_invoice = #{complianceInvoice,jdbcType=VARCHAR},
      conference_schedule = #{conferenceSchedule,jdbcType=VARCHAR},
      check_in_slip = #{checkInSlip,jdbcType=VARCHAR},
      conference_photo = #{conferencePhoto,jdbcType=VARCHAR},
      hotel_fee_statement = #{hotelFeeStatement,jdbcType=VARCHAR},
      tripartite_agreement = #{tripartiteAgreement,jdbcType=VARCHAR},
      proof_purchase = #{proofPurchase,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      apply_points = #{applyPoints,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <!-- 活动明细 -->
   <resultMap id="SeminarBaseResultMap" type="com.chevron.promote.model.response.ResponsePromoteSeminarDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="invited_customers_count" property="invitedCustomersCount" jdbcType="INTEGER" />
    <result column="seminar_purpose" property="seminarPurpose" jdbcType="VARCHAR" />
    <result column="seminar_content" property="seminarContent" jdbcType="VARCHAR" />
    <result column="b2b_wechat_platforms_count" property="b2bWechatPlatformsCount" jdbcType="INTEGER" />
    <result column="estimated_order_quantity" property="estimatedOrderQuantity" jdbcType="INTEGER" />
    <result column="apply_packs_count" property="applyPacksCount" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="venue_meal" property="venueMeal" jdbcType="NUMERIC" />
    <result column="activity_meal" property="activityMeal" jdbcType="NUMERIC" />
    <result column="apply_points" property="applyPoints" jdbcType="INTEGER" />
    <result column="apply_high_packs" property="applyHighPacks" jdbcType="INTEGER" />
    <!-- 公共 -->
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="source_plan_id" property="sourcePlanId" jdbcType="BIGINT" />
    <result column="source_distribution_id" property="sourceDistributionId" jdbcType="BIGINT" />
    <result column="activity_subject" property="activitySubject" jdbcType="VARCHAR" />
    <result column="activity_start_time" property="activityStartTime" jdbcType="TIMESTAMP" />
    <result column="activity_end_time" property="activityEndTime" jdbcType="TIMESTAMP" />
    <result column="activity_address" property="activityAddress" jdbcType="VARCHAR" />
    <result column="activity_organizers" property="activityOrganizers" jdbcType="VARCHAR" />
    <result column="activity_organizers_id" property="activityOrganizersId" jdbcType="BIGINT" />
    <result column="activity_amount" property="activityAmount" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="contact_address" property="contactAddress" jdbcType="VARCHAR" />
    <result column="contact_person" property="contactPerson" jdbcType="VARCHAR" />
    <result column="contact_tel" property="contactTel" jdbcType="VARCHAR" />
  </resultMap>
  
  <select id="getSeminarActivityDetail" parameterType="long" resultMap="SeminarBaseResultMap">
    SELECT t_seminar_activity_detail.*,
    t_promote_activity.apply_batch_id,t_promote_activity.source_plan_id,
    t_promote_activity.source_distribution_id,t_promote_activity.activity_subject,
    t_promote_activity.activity_start_time,t_promote_activity.activity_end_time,
    t_promote_activity.activity_address,
    t_promote_activity.activity_organizers,t_promote_activity.activity_organizers_id,
    t_promote_activity.activity_amount,t_promote_activity.create_time,t_promote_activity.release_time,
    t_promote_activity.contact_address,t_promote_activity.contact_person,t_promote_activity.contact_tel,
    t_apply.creator_id,tt_user.ch_name creator
    FROM wx_promote_activity t_promote_activity
    INNER JOIN wx_promote_seminar_activity_detail t_seminar_activity_detail
    ON t_seminar_activity_detail.activity_id = t_promote_activity.id
    INNER JOIN wx_promote_application_batch t_apply
    ON t_apply.batchid = t_promote_activity.apply_batch_id
    INNER JOIN wx_t_user tt_user
    ON tt_user.user_id = t_apply.creator_id
    WHERE t_seminar_activity_detail.activity_id = #{activityId}
  
  </select>
  
   <update id="updateByActivityIdSelective" parameterType="com.chevron.promote.model.detail.PromoteSeminarActivityDetail" >
    update wx_promote_seminar_activity_detail
    <set >
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="invitedCustomersCount != null" >
        invited_customers_count = #{invitedCustomersCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPurpose != null" >
        seminar_purpose = #{seminarPurpose,jdbcType=VARCHAR},
      </if>
      <if test="seminarContent != null" >
        seminar_content = #{seminarContent,jdbcType=VARCHAR},
      </if>
      <if test="b2bWechatPlatformsCount != null" >
        b2b_wechat_platforms_count = #{b2bWechatPlatformsCount,jdbcType=INTEGER},
      </if>
      <if test="estimatedOrderQuantity != null" >
        estimated_order_quantity = #{estimatedOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="applyPacksCount != null" >
        apply_packs_count = #{applyPacksCount,jdbcType=INTEGER},
      </if>
      <if test="venueMeal != null" >
        venue_meal = #{venueMeal,jdbcType=NUMERIC},
      </if>
      <if test="activityMeal != null" >
        activity_meal = #{activityMeal,jdbcType=NUMERIC},
      </if>
      <if test="a14ApplyForm != null" >
        a14_apply_form = #{a14ApplyForm,jdbcType=VARCHAR},
      </if>
      <if test="complianceInvoice != null" >
        compliance_invoice = #{complianceInvoice,jdbcType=VARCHAR},
      </if>
      <if test="conferenceSchedule != null" >
        conference_schedule = #{conferenceSchedule,jdbcType=VARCHAR},
      </if>
      <if test="checkInSlip != null" >
        check_in_slip = #{checkInSlip,jdbcType=VARCHAR},
      </if>
      <if test="conferencePhoto != null" >
        conference_photo = #{conferencePhoto,jdbcType=VARCHAR},
      </if>
      <if test="hotelFeeStatement != null" >
        hotel_fee_statement = #{hotelFeeStatement,jdbcType=VARCHAR},
      </if>
      <if test="tripartiteAgreement != null" >
        tripartite_agreement = #{tripartiteAgreement,jdbcType=VARCHAR},
      </if>
      <if test="proofPurchase != null" >
        proof_purchase = #{proofPurchase,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="applyPoints != null" >
        apply_points = #{applyPoints,jdbcType=INTEGER},
      </if>
      <if test="applyHighPacks != null" >
        apply_high_packs = #{applyHighPacks,jdbcType=INTEGER},
      </if>
    </set>
    where activity_id = #{activityId,jdbcType=BIGINT}
  </update>
  
  
  
  <select id="getHasApplyPacks" parameterType="map" resultMap="BaseResultMap" >
      SELECT 
      sum(t_activity_detail.apply_packs_count)apply_packs_count,
      sum(t_activity_detail.apply_points)apply_points,
      sum(t_activity_detail.venue_meal)venue_meal,
      sum(t_activity_detail.apply_high_packs)apply_high_packs
      FROM wx_promote_activity t_activity
      INNER JOIN wx_promote_seminar_activity_detail t_activity_detail
      ON t_activity.id = t_activity_detail.activity_id
      INNER JOIN wx_promote_application_batch t_apply
      ON t_apply.batchid = t_activity.apply_batch_id
      WHERE t_activity.source_plan_id = #{sourcePlanId}
       <if test="userId!=null">
      AND t_activity.creator_id = #{userId}
      </if>
      <if test="status!=null">
        and
         t_apply.approve_status NOT IN 
        <foreach item="statu" index="index" collection="status" open="(" separator="," close=")">  
             #{statu}  
        </foreach> 
      </if>
  </select>
</mapper>