package com.sys.utils.service.impl;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.utils.business.BusinessBetweenSpConfBizService;
import com.sys.utils.model.BusinessBetweenSpConf;
import com.sys.utils.model.BusinessBetweenSpConfMain;
import com.sys.utils.service.BusinessBetweenSpConfService;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 合伙人之间合作设置操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2017-10-25 10:50
 */
@Service
public class BusinessBetweenSpConfServiceImpl implements BusinessBetweenSpConfService {
	@Autowired
	private BusinessBetweenSpConfBizService businessBetweenSpConfBizService;
	
	private final static Logger log = Logger.getLogger(BusinessBetweenSpConfServiceImpl.class);
	
	@Override
	public Map<String, Object> save(BusinessBetweenSpConf record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				businessBetweenSpConfBizService.insert(record);
			}else{
				businessBetweenSpConfBizService.update(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> delete(Long id) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			businessBetweenSpConfBizService.delete(id);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}

	@Override
	public Map<String, Object> saveBean(BusinessBetweenSpConfMain record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			businessBetweenSpConfBizService.saveBean(record, map);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}

	@Override
	public Map<String, Object> deleteBean(BusinessBetweenSpConfMain record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			businessBetweenSpConfBizService.deleteBean(record.getId(), map);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
	
	
}
