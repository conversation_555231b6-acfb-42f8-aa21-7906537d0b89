select o.organization_name "经销商名称", c.CONTACTS "联系人", c.CONTACT_NUMBER "联系电话",
p.region_name "省份", c1.region_name "城市", d.region_name "区县", c.ADDRESS_DETAIL "地址"
from wx_t_material_address_config c
left join wx_t_organization o on c.partner_id=o.id
left join wx_t_region d on d.id = c.ADDRESS_REGION
        left join wx_t_region c1 on c1.id = d.parent_id
        left join wx_t_region p on p.id = c1.parent_id
where o.status=1 and c.DELETE_FLAG=0
order by o.id