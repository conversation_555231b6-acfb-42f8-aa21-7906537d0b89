package com.sys.utils.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.EmailSendUtils;
import com.common.util.StringUtils;
import com.common.util.WeixinMessageUtil;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.WarnConfigBizService;
import com.sys.utils.dao.WarnConfigMapper;
import com.sys.utils.model.WarnConfig;
import com.sys.utils.model.WarnConfigExample;
import com.sys.utils.model.WarnConfigParams;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sys.utils.model.WarnMessageInfo;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ContextLoader;

/**
 * 告警配置业务接口实现类
 * <AUTHOR>
 * @version 1.0 2020-07-09 18:10
 */
@Service
public class WarnConfigBizServiceImpl implements WarnConfigBizService {
	@Autowired
	private WarnConfigMapper warnConfigMapper;
	@Autowired
    private JdbcTemplate wxJdbcTemplate;
	@Autowired
    private DicService dicService;

	private static Logger log = Logger.getLogger(WarnConfigBizServiceImpl.class);

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(WarnConfig record) throws WxPltException {
		record.setEnableFlag(1);
		record.setDeleteFlag(0);
		record.setCreateUserId(ContextUtil.getCurUserId());
		record.setCreateTime(DateUtil.getCurrentDate());
		warnConfigMapper.insertSelective(record);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(WarnConfig record) throws WxPltException {
		record.setUpdateUserId(ContextUtil.getCurUserId());
		record.setUpdateTime(DateUtil.getCurrentDate());
		warnConfigMapper.updateByPrimaryKeySelective(record);
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		WarnConfigExample example = new WarnConfigExample();
		example.createCriteria().andIdIn(ids);
		warnConfigMapper.deleteByExample(example);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(WarnConfigExample example) throws WxPltException {
		warnConfigMapper.deleteByExample(example);
	}

	@Override
	public List<WarnConfig> queryByExample(WarnConfigExample example) throws WxPltException {
		return warnConfigMapper.selectByExample(example);
	}

	@Override
    public List<WarnConfig> queryByParams(Map<String, Object> params) throws WxPltException {
    	return warnConfigMapper.queryByParams(params);
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateForEditPage(WarnConfig record) throws WxPltException {
		record.setUpdateUserId(ContextUtil.getCurUserId());
		record.setUpdateTime(DateUtil.getCurrentDate());
		warnConfigMapper.updateForEditPage(record);
	}
	
	@Override
	public WarnConfig getBean(Long id) throws WxPltException{
		return warnConfigMapper.selectByPrimaryKey(id);
	}	

	@Override
	public void queryForPage(WarnConfigParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}
	
	@Override
	public List<WarnConfig> queryForPage(WarnConfigParams params) throws WxPltException {
		return warnConfigMapper.queryForPage(params);
	}


    @Override
    public void excuteWarnningTask(Long id) throws WxPltException {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("enableFlag", 1);
        params.put("deleteFlag", 0);
        params.put("id", id);
        List<WarnConfig> warnConfigs = warnConfigMapper.queryByParams(params);
        if (CollectionUtil.isEmpty(warnConfigs)) {
            return;
        }
        for (WarnConfig warnConfig : warnConfigs) {
            try {
                this.pushNotifyMessage(warnConfig);
            } catch (Exception e) {
                log.error(StrUtil.format("系统自定义告警任务推送失败,任务ID：{}，任务标题：{}", warnConfig.getId(), warnConfig.getWarnTitle()));
                e.printStackTrace();
                LogUtils.addInfoLog(1L, "WarnConfigBizServiceImpl.excuteWarnningTask", StrUtil.format("系统自定义告警任务推送失败,任务ID：{}，任务标题：{}", warnConfig.getId(), warnConfig.getWarnTitle()));
            }
        }
    }

    private void pushNotifyMessage(final WarnConfig warnConfig) throws Exception{
        if (StringUtils.isBlank(warnConfig.getWarnSql()) || warnConfig.getNotifyType() == null) {
            return;
        }
        //获取具体的告警信息
        List<WarnMessageInfo> result = wxJdbcTemplate.query(warnConfig.getWarnSql(), new RowMapper<WarnMessageInfo>() {
            @Override
            public WarnMessageInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
                WarnMessageInfo row = new WarnMessageInfo();
                row.setWarnTitle(warnConfig.getWarnTitle());
                row.setWarnDetail(rs.getString(warnConfig.getWarnDetailField()));
                row.setWarnDesc(rs.getString(warnConfig.getWarnDescField()));
                return row;
            }
        });
        //获取提醒类型
        Integer notifyType = warnConfig.getNotifyType();
        //邮件通知
        if((notifyType & 1) == 1){
            //获取配置的提醒人
            DicItemVo dictItem = dicService.getDictItem(Constants.WARNING_CONFIG_CODE_TYPE, warnConfig.getNotifyCode());
            if(dictItem == null || StringUtils.isBlank(dictItem.getDicItemName())){
                return;
            }
            String[] to = dictItem.getDicItemName().split(";");
            for (WarnMessageInfo warnMessageInfo : result) {
                //开始执行告警推送操作
                Map<String, Object> dataMap = new HashMap<String, Object>();
                dataMap.put("warnTitle", warnMessageInfo.getWarnTitle());
                dataMap.put("warnDesc", warnMessageInfo.getWarnDesc());
                dataMap.put("warnDetail", warnMessageInfo.getWarnDetail());
                EmailSendUtils.sendEmailForListContent(ContextLoader
                                .getCurrentWebApplicationContext().getServletContext(), to, null,warnMessageInfo.getWarnTitle(), dataMap,
                        null, "/warning_notify_mail.ftl");
                LogUtils.addInfoLog(1L, "WarnConfigBizServiceImpl.excuteWarnningTask", "系统自定义告警。");
            }
        }

        //微信通知
        if((notifyType & 2) == 2){
            for (WarnMessageInfo warnMessageInfo : result) {
                WeixinMessageUtil.weixinAlarmMessagePush("系统自定义告警", warnMessageInfo.getWarnDesc(),
                        "系统自定义告警", warnMessageInfo.getWarnTitle(), warnMessageInfo.getWarnDetail());
                LogUtils.addInfoLog(1L, "WarnConfigBizServiceImpl.excuteWarnningTask", "系统自定义告警。");
            }
        }
    }
}
