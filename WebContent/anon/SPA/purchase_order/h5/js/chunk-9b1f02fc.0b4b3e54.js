(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9b1f02fc"],{"2c38":function(s,t,e){},"332a":function(s,t,e){"use strict";var i=e("2c38"),a=e.n(i);a.a},"563c":function(s,t,e){s.exports=e.p+"h5/img/noAddress.d76a1cef.png"},afa3:function(s,t,e){s.exports=e.p+"h5/img/line.05bf1c84.jpg"},bb9a:function(s,t,e){"use strict";e.r(t);var i=function(){var s=this,t=s.$createElement,i=s._self._c||t;return i("div",{ref:"container",staticClass:"address-management",class:s.addressList.length<1&&s.page>1?"on":""},[s.addressList.length>0?i("div",{staticClass:"line"},[i("img",{attrs:{src:e("afa3")}})]):s._e(),s._l(s.addressList,(function(t,e){return i("div",{key:e,staticClass:"item"},[i("div",{staticClass:"address"},[i("div",{staticClass:"consignee"},[s._v(" 收货人："+s._s(t.contactPerson)+" "),i("span",{staticClass:"phone"},[s._v(s._s(t.contactPersonTel))])]),i("div",[s._v("收货地址："+s._s(t.address))])]),i("div",{staticClass:"operation acea-row row-between-wrapper"},[i("div",{staticClass:"select-btn"}),i("div",{staticClass:"acea-row row-middle"},[i("div",{staticStyle:{padding:"0 15px 0 15px"},on:{click:function(t){return s.editAddress(e)}}},[s._v("编辑")]),i("div",{staticStyle:{padding:"0 15px 0 15px"},on:{click:function(t){return s.selectAddress(e)}}},[s._v("选择")])])])])})),i("Loading",{attrs:{loaded:s.loadend,loading:s.loading}}),s.addressList.length<1&&s.page>1?i("div",{staticClass:"noCommodity"},[s._m(0)]):s._e()],2)},a=[function(){var s=this,t=s.$createElement,i=s._self._c||t;return i("div",{staticClass:"noPictrue"},[i("img",{staticClass:"image",attrs:{src:e("563c")}})])}],d=(e("a434"),e("3835")),n=e("c24f"),o=e("3a5e"),r=e("ed08"),c=e("74f9"),l={components:{Loading:o["a"]},data:function(){return{page:1,limit:20,addressList:[],loadTitle:"",loading:!1,loadend:!1,isWechat:Object(r["d"])()}},mounted:function(){this.addressList=this.$store.getters.addressList,console.log(this.addressList)},methods:{AddressList:function(){var s=this;s.loading||s.loadend||(s.loading=!0,Object(n["d"])({page:s.page,limit:s.limit}).then((function(t){var e=Object(d["a"])(t,2),i=e[0],a=e[1];console.log("".concat(i)),s.loading=!1,console.log(JSON.stringify(a)),i?(s.addressList.push.apply(s.addressList,a.result.addressList),s.loadend=a.result.addressList.length<s.limit,s.page=s.page+1):s.loadend=!0})))},editAddress:function(s){this.$router.push({path:"/user/add_address/"+this.addressList[s].id}),localStorage.setItem("EDIT_ADDRESS_INFO",JSON.stringify(this.addressList[s]))},selectAddress:function(s){this.$router.go(-1),this.$store.commit("UPDATE_ADDRESS",this.addressList[s])},delAddress:function(s){var t=this,e=this.addressList[s],i=e.id;Object(n["e"])(i).then((function(){t.$dialog.toast({mes:"删除成功!",callback:function(){t.addressList.splice(s,1),t.$set(t,"addressList",t.addressList)}})}))},radioChange:function(s){var t=this,e=this.addressList[s],i=e.id;Object(n["c"])(i).then((function(){for(var e=0,i=t.addressList.length;e<i;e++)t.addressList[e].is_default=e===s?1:0;t.$set(t,"addressList",t.addressList)}))},addAddress:function(){this.$router.push({path:"/user/add_address"})},getAddress:function(){var s=this;Object(c["d"])().then((function(t){s.$dialog.loading.open(),Object(n["F"])({real_name:t.userName,phone:t.telNumber,address:{province:t.provinceName,city:t.cityName,district:t.countryName},detail:t.detailInfo,post_code:t.postalCode,wx_export:1}).then((function(){s.page=1,s.loading=!1,s.loadend=!1,s.addressList=[],s.AddressList(),s.$dialog.loading.close(),s.$dialog.toast({mes:"添加成功"})})).catch((function(t){s.$dialog.loading.close(),s.$dialog.error(t.msg||"添加失败")}))}))}}},u=l,g=(e("332a"),e("2877")),p=Object(g["a"])(u,i,a,!1,null,"1de3f768",null);t["default"]=p.exports}}]);
//# sourceMappingURL=chunk-9b1f02fc.0b4b3e54.js.map