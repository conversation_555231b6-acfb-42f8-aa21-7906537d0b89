package com.sys.quartz.job;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.sys.utils.business.MessageNotifyScheduleBizService;
import com.sys.utils.model.MessageNotifyScheduleParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 批量短信推送
 */
public class MessageNotifyScheduleTask {

    @Autowired
    private MessageNotifyScheduleBizService messageNotifyScheduleBizService;

    private static Logger log = LoggerFactory.getLogger(MessageNotifyScheduleTask.class);

    public synchronized void execute() {
        log.info("MessageNotifyScheduleTask job start....");
        try {
            Object isProduct = Constants.getSystemPropertyByCodeType(Constants.IS_PRODUCT);
            if(isProduct == null || !Boolean.valueOf(isProduct.toString())){
                return;
            }
            messageNotifyScheduleBizService.processPushMessage(new MessageNotifyScheduleParams());
        } catch (WxPltException e) {
            e.printStackTrace();
            log.error("MessageNotifyScheduleTask job break....", e);
        }
    }
}
