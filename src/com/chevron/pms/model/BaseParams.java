package com.chevron.pms.model;

import java.util.Map;

import com.common.util.CommonUtil;
import com.common.util.StringUtils;

/**
 * 列表分页查询参数基础类
 * <AUTHOR>
 *
 */
public abstract class BaseParams {
	/** 分页起始位置 */
	private long start;
	
	/** 每页记录条数 */
	private int limit;
	
	/** 排序字段 */
	private String field;
	
	/** 是否用于分页查询 */
	private boolean isPaging = true;

	/** 是否查询总记录条数 */
	private boolean queryTotal = true;

	/** 查询总记录条数 */
	private long totalCount;
	
	/** 排序类型 */
	private String direction = "ASC";

	/**
	 * 获取字段映射表
	 * @return 字段映射表
	 */
	protected abstract Map<String, String> getFieldMap();
	
	public final static String DIRECTION_ASC = "ASC";
	public final static String DIRECTION_DESC = "DESC";
	
	/**
	 * 查询类型  高级搜索、全局搜索
	 */
	private int queryType = 2;//默认是高级搜索
	public static final int ADVANCED_SEARCH_TYPE = 1;//高级搜索类型
	public static final int GLOBAL_SEARCH_TYPE = 2;//全局搜索类型  关键字搜索
	private String queryField;//全局搜索字段
	public static final String RESULTLST = "resultlst";
	public static final String TOTALRECORD = "totalRecord";
	public String firtPyForQueryField;
	//数据拦截器开关权限
	private String isOpernCustomMybatisInterceptor = "1";//1代表开启，2代表关闭了   数据权限拦截器
	

	public long getStart() {
		return start;
	}

	public int getLimit() {
		return limit;
	}

	public void setStart(long start) {
		this.start = start;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getField() {
		return field;
	}

	public String getDirection() {
		return direction;
	}

	public void setField(String field) {
		if(StringUtils.isNotBlank(field)){
			this.field = field;
		}
	}
	
	public void closeOrder(){
		field = null;
		isPaging = false;
	}

	public void setDirection(String direction) {
		if(StringUtils.isNotBlank(direction)){
			this.direction = direction;
		}
	}
	
	public String getOrder(){
		String sortField;
		if(getFieldMap() == null || !getFieldMap().containsKey(field)){
			sortField = humpToLine(field);
		}else{
			sortField = getFieldMap().get(field);
		}
		String order = sortField + " " + getDirection();
		CommonUtil.checkSqlParam(order);
		return order;
	}
	
	public String getOrderBy(){
		if(StringUtils.isNull(field)){
			return "";
		}
		return getOrder();
	}
	
	//驼峰转下划线
	private String humpToLine(String str){
        return str!=null?str.replaceAll("[A-Z]", "_$0").toLowerCase():"";
    }

	public int getQueryType() {
		return queryType;
	}

	public void setQueryType(int queryType) {
		this.queryType = queryType;
	}

	public String getQueryField() {
		if(null==queryField || queryField.isEmpty())
		{
			queryField = null;
		}
		return queryField;
	}

	public void setQueryField(String queryField) {
		this.queryField = queryField;
		setFirtPyForQueryField(this.queryField);
	}

	public boolean isPaging() {
		return isPaging;
	}

	public long getTotalCount() {
		return totalCount;
	}

	public void setPaging(boolean isPaging) {
		this.isPaging = isPaging;
	}

	public boolean isQueryTotal() {
		return queryTotal;
	}

	public void setQueryTotal(boolean queryTotal) {
		this.queryTotal = queryTotal;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}

	public String getFirtPyForQueryField() {
		if(null==firtPyForQueryField || firtPyForQueryField.isEmpty())
		{
			firtPyForQueryField = null;
		}
		return firtPyForQueryField;
	}

	public void setFirtPyForQueryField(String firtPyForQueryField) {
//		if(null!=firtPyForQueryField && !firtPyForQueryField.isEmpty())
//		{
//			this.firtPyForQueryField =  GetPinyinStringUtil.getPinYinHeadCharForSourceStr(firtPyForQueryField);
//		}else
//		{
			this.firtPyForQueryField = firtPyForQueryField;
//		}
	}

	public String getIsOpernCustomMybatisInterceptor() {
		return isOpernCustomMybatisInterceptor;
	}

	public void setIsOpernCustomMybatisInterceptor(
			String isOpernCustomMybatisInterceptor) {
		this.isOpernCustomMybatisInterceptor = isOpernCustomMybatisInterceptor;
	}


	@Override
	public String toString() {
		final StringBuffer sb = new StringBuffer("BaseParams{");
		sb.append("start=").append(start);
		sb.append(", limit=").append(limit);
		sb.append(", field='").append(field).append('\'');
		sb.append(", isPaging=").append(isPaging);
		sb.append(", totalCount=").append(totalCount);
		sb.append(", direction='").append(direction).append('\'');
		sb.append(", queryType=").append(queryType);
		sb.append(", queryField='").append(queryField).append('\'');
		sb.append('}');
		return sb.toString();
	}
	
	public void clearOrderField(){
	    this.field = null;
    }
}
