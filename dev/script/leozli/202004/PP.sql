/*==============================================================*/
/* Module: PP                                                   */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_statistics_cond_config                           */
/*==============================================================*/
create table wx_t_statistics_cond_config (
	id                               bigint                            not null identity,
	statistics_type                  nvarchar(64)                               not null,
	effective_from_date              date                                       not null,
	effective_to_date                date                                       not null,
	config_desc                      nvarchar(512)                                  null,
	enable_flag                      tinyint                                        null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sta_cond_con_sta_type ON wx_t_statistics_cond_config (statistics_type);

/*==============================================================*/
/* Table: wx_t_sta_cond_con_value                               */
/*==============================================================*/
create table wx_t_sta_cond_con_value (
	item_value                       nvarchar(64)                               not null,
	config_id                        bigint                                     not null,
	primary key (item_value, config_id)
);

/*==============================================================*/
/* Table: wx_message                                            */
/*==============================================================*/
create table wx_message (
	id                               bigint                                     not null,
	effective_from_date              date                                           null,
	effective_to_date                date                                           null,
	app_available                    tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_product                                          */
/*==============================================================*/
create table wx_t_product (
	id                               bigint                            not null identity,
	sku                              nvarchar(64)                               not null,
	name                             nvarchar(256)                              not null,
	name_en                          nvarchar(256)                                  null,
	sale_price                       decimal(20,6)                                  null,
	pack                             decimal(20,6)                                  null,
	size                             nvarchar(128)                                  null,
	pack_type                        nvarchar(128)                                  null,
	pack_code                        nvarchar(64)                                   null,
	product_channel                  nvarchar(256)                                  null,
	grade_abc                        nvarchar(64)                                   null,
	t1_category                      nvarchar(64)                                   null,
	t2_category                      nvarchar(64)                                   null,
	t3_category                      nvarchar(64)                                   null,
	digit_product_code               nvarchar(64)                                   null,
	inner_brand                      nvarchar(64)                                   null,
	product_sector                   nvarchar(64)                                   null,
	hydraulic_ago_ci                 nvarchar(256)                                  null,
	hyd_tra_ago_ind_com              nvarchar(256)                                  null,
	can_verify                       tinyint                                    not null,
	capacity                         nvarchar(20)                                   null,
	viscosity                        nvarchar(20)                                   null,
	oil_type                         nvarchar(30)                                   null,
	remark                           nvarchar(64)                                   null,
	status                           tinyint                                    not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	creator                          nvarchar(128)                                  null,
	support_order                    tinyint                                        null,
	product_property                 nvarchar(64)                                   null,
	cdm_category                     nvarchar(64)                                   null,
	ci_category                      nvarchar(64)                                   null,
	primary key (id)
);
ALTER TABLE wx_t_product ADD CONSTRAINT uk_product UNIQUE (sku);
CREATE INDEX ix_product_product_channel ON wx_t_product (product_channel);
CREATE INDEX ix_product_grade_abc ON wx_t_product (grade_abc);
CREATE INDEX ix_product_t1_category ON wx_t_product (t1_category);
CREATE INDEX ix_product_t2_category ON wx_t_product (t2_category);
CREATE INDEX ix_product_t3_category ON wx_t_product (t3_category);
CREATE INDEX ix_product_inner_brand ON wx_t_product (inner_brand);

/*==============================================================*/
/* Table: wx_t_user                                             */
/*==============================================================*/
create table wx_t_user (
	user_id                          bigint                                     not null,
	cai                              nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (user_id)
);
CREATE INDEX ix_user_cai ON wx_t_user (cai);

/*==============================================================*/
/* Table: wx_t_business_region_config                           */
/*==============================================================*/
create table wx_t_business_region_config (
	id                               bigint                            not null identity,
	business_name                    nvarchar(256)                              not null,
	sales_channel                    nvarchar(256)                                  null,
	region_name                      nvarchar(256)                              not null,
	region_alias                     nvarchar(256)                                  null,
	enable_flag                      tinyint                                        null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_bus_reg_con_bus_name ON wx_t_business_region_config (business_name);

/*==============================================================*/
/* Table: wx_t_partner_responsible_main                         */
/*==============================================================*/
create table wx_t_partner_responsible_main (
	id                               bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	include_actual_rel               tinyint                                    not null,
	sales_cai                        nvarchar(64)                                   null,
	config_type                      tinyint                                    not null,
	operator_type                    nvarchar(64)                                   null,
	cc_to_customer                   tinyint                                    not null,
	cc_to_sales                      tinyint                                    not null,
	cc_to_suppervisor                tinyint                                    not null,
	cc_to_channel_manager            tinyint                                    not null,
	cc_to_bu_manager                 tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_common_menu                                      */
/*==============================================================*/
create table wx_t_common_menu (
	id                               bigint                    not null identity(1000,1),
	module_id                        bigint                                     not null,
	menu_id                          bigint                                     not null,
	menu_name_alias                  nvarchar(64)                                   null,
	fun_icon                         nvarchar(128)                                  null,
	order_numb                       decimal(20,8)                                  null,
	fun_desc                         nvarchar(256)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_common_menu_module                               */
/*==============================================================*/
create table wx_t_common_menu_module (
	id                               bigint                     not null identity(100,1),
	module_name                      nvarchar(256)                                  null,
	module_banner                    nvarchar(256)                                  null,
	module_theme                     nvarchar(256)                                  null,
	order_numb                       decimal(20,8)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_role_common_menu                                 */
/*==============================================================*/
create table wx_t_role_common_menu (
	role_id                          bigint                                     not null,
	common_menu_id                   bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (role_id, common_menu_id)
);

/*==============================================================*/
/* Table: wx_t_oil_ver_award_det                                */
/*==============================================================*/
create table wx_t_oil_ver_award_det (
	id                               bigint                                     not null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	reach_standard_flag              int                                            null,
	review_flag                      tinyint                                        null,
	remark                           nvarchar(512)                                  null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_role                                             */
/*==============================================================*/
create table wx_t_role (
	role_id                          bigint                                     not null,
	sales_channel                    nvarchar(256)                                  null,
	user_flag                        int                                        not null,
	primary key (role_id)
);

/*==============================================================*/
/* Table: wx_t_interface_data                                   */
/*==============================================================*/
create table wx_t_interface_data (
	id                               bigint                                     not null,
	url                              nvarchar(256)                                  null,
	status                           tinyint                                    not null,
	remarks                          nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_interface_data_detail                            */
/*==============================================================*/
create table wx_t_interface_data_detail (
	property_name                    nvarchar(256)                              not null,
	interface_data_id                bigint                                     not null,
	property_value                   nvarchar(1000)                                 null,
	value_processor                  nvarchar(64)                                   null,
	primary key (property_name, interface_data_id)
);

/*==============================================================*/
/* Table: wx_t_operation_permission                             */
/*==============================================================*/
create table wx_t_operation_permission (
	id                               bigint                            not null identity,
	role_id                          bigint                                     not null,
	module_code                      nvarchar(128)                              not null,
	permission_weight                int                                        not null,
	enable_flag                      tinyint                                        null,
	remark                           nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_ope_per_role_id ON wx_t_operation_permission (role_id);
CREATE INDEX ix_ope_per_mod_code ON wx_t_operation_permission (module_code);

/*==============================================================*/
/* Table: wx_t_tace_detail                                      */
/*==============================================================*/
create table wx_t_tace_detail (
	trace_no                         nvarchar(64)                               not null,
	sku                              nvarchar(64)                               not null,
	primary key (trace_no)
);

/*==============================================================*/
/* Table: wx_t_sale_for_plant_all                               */
/*==============================================================*/
create table wx_t_sale_for_plant_all (
	id                               bigint                            not null identity,
	customer_code                    nvarchar(64)                                   null,
	customer_name_cn                 nvarchar(256)                                  null,
	customer_name_en                 nvarchar(256)                                  null,
	plant_code                       nvarchar(30)                               not null,
	allocate_rate                    decimal(16,15)                                 null,
	effective_from_date              date                                           null,
	effective_to_date                date                                           null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sal_for_pla_all_cus_nam_en ON wx_t_sale_for_plant_all (customer_name_en);

/*==============================================================*/
/* Table: wx_t_forecasting_worksheet                            */
/*==============================================================*/
create table wx_t_forecasting_worksheet (
	id                               bigint                            not null identity,
	worksheet_title                  nvarchar(256)                              not null,
	sales_start_date                 date                                           null,
	sales_end_date                   date                                           null,
	supervisor_start_date            date                                           null,
	supervisor_end_date              date                                           null,
	status                           tinyint                                    not null,
	sales_channel                    nvarchar(256)                                  null,
	forecasting_month                date                                           null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	delete_flag                      tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_forecasting_approve                              */
/*==============================================================*/
create table wx_t_forecasting_approve (
	id                               bigint                            not null identity,
	worksheet_id                     bigint                                     not null,
	approver                         bigint                                         null,
	status                           smallint                                   not null,
	submit_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	record_version                   bigint                                     not null,
	temp_save_finished               tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_forecasting_approve ADD CONSTRAINT uk_forecasting_approve UNIQUE (worksheet_id,approver);

/*==============================================================*/
/* Table: wx_t_forecasting_sales                                */
/*==============================================================*/
create table wx_t_forecasting_sales (
	id                               bigint                            not null identity,
	worksheet_id                     bigint                                     not null,
	approve_id                       bigint                                         null,
	sales                            bigint                                         null,
	status                           smallint                                   not null,
	submit_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	record_version                   bigint                                     not null,
	temp_save_finished               tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_forecasting_sales ADD CONSTRAINT uk_forecasting_sales UNIQUE (worksheet_id,sales);

/*==============================================================*/
/* Table: wx_t_forecasting_detail                               */
/*==============================================================*/
create table wx_t_forecasting_detail (
	id                               bigint                            not null identity,
	forecasting_id                   bigint                                         null,
	sku                              nvarchar(64)                               not null,
	customer_name_cn                 nvarchar(256)                                  null,
	customer_name_en                 nvarchar(256)                                  null,
	forecasting_value                decimal(20,6)                                  null,
	fixed_value                      decimal(20,6)                                  null,
	avg_12                           decimal(20,6)                                  null,
	value_last_year                  decimal(20,6)                                  null,
	month_offset                     tinyint                                    not null,
	forecast_month                   nvarchar(6)                                    null,
	status                           bigint                                     not null,
	def_forecasting_value            decimal(20,6)                                  null,
	forecast_leading_time            int                                            null,
	latest_forecast_value            decimal(20,6)                                  null,
	primary key (id)
);
CREATE INDEX ix_forecasting_detail_sku ON wx_t_forecasting_detail (sku);
CREATE INDEX ix_for_det_cus_name_cn ON wx_t_forecasting_detail (customer_name_cn);
CREATE INDEX ix_for_det_for_month ON wx_t_forecasting_detail (forecast_month);

/*==============================================================*/
/* Table: wx_t_qr_code_detail                                   */
/*==============================================================*/
create table wx_t_qr_code_detail (
	code_id                          bigint                                     not null,
	qr_owner                         nvarchar(30)                                   null,
	primary key (code_id)
);

/*==============================================================*/
/* Table: wx_t_partner_o2o_enterprise                           */
/*==============================================================*/
create table wx_t_partner_o2o_enterprise (
	id                               bigint                                     not null,
	distributor_id                   bigint                                         null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_sms_log                                          */
/*==============================================================*/
create table wx_t_sms_log (
	id                               bigint                            not null identity,
	mobile                           nvarchar(20)                               not null,
	from_app                         nvarchar(64)                                   null,
	status                           tinyint                                    not null,
	request_time                     int                                            null,
	status_msg                       nvarchar(255)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sms_log_mobile ON wx_t_sms_log (mobile);

/*==============================================================*/
/* Table: wx_t_work_shop                                        */
/*==============================================================*/
create table wx_t_work_shop (
	id                               bigint                            not null identity,
	work_shop_name                   nvarchar(300)                              not null,
	region_id                        bigint                                         null,
	work_shop_address                nvarchar(500)                                  null,
	longitude                        decimal(10,6)                                  null,
	latitude                         decimal(10,6)                                  null,
	grade_abc                        nvarchar(64)                                   null,
	type                             nvarchar(100)                                  null,
	signboard_type                   nvarchar(256)                                  null,
	contact_person                   nvarchar(100)                                  null,
	contact_person_tel               nvarchar(18)                                   null,
	is_multiple                      tinyint                                        null,
	area                             nvarchar(10)                                   null,
	seats_num                        int                                            null,
	trench_numb                      int                                            null,
	two_column_elevator_numb         int                                            null,
	four_column_elevator_numb        int                                            null,
	monthly_oil_sales_volume         bigint                                         null,
	monthly_chevron_oil_sales_volume bigint                                         null,
	from_source                      int                                        not null,
	delete_flag                      tinyint                                    not null,
	business_weight                  int                                        not null,
	work_shop_code                   nvarchar(30)                                   null,
	employees_num                    int                                            null,
	service_scope                    nvarchar(100)                                  null,
	business_license_code            nvarchar(50)                                   null,
	reserve_service_tel              nvarchar(20)                                   null,
	business_detail                  nvarchar(500)                                  null,
	harvest_person                   nvarchar(100)                                  null,
	harvest_person_tel               nvarchar(18)                                   null,
	butt_in_charge_person            nvarchar(100)                                  null,
	butt_in_charge_person_tel        nvarchar(18)                                   null,
	bank_acount_name                 nvarchar(20)                                   null,
	bank_acount                      nvarchar(20)                                   null,
	bank                             nvarchar(50)                                   null,
	credit_rating                    nvarchar(20)                                   null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	creator                          nvarchar(50)                                   null,
	remark                           nvarchar(300)                                  null,
	status                           nvarchar(20)                                   null,
	business_license_expiry_date     date                                           null,
	mechanic_count                   int                                            null,
	excute_user_id                   bigint                                         null,
	photo_id                         nvarchar(30)                                   null,
	source                           nvarchar(30)                                   null,
	signed_type                      nvarchar(20)                                   null,
	scale                            nvarchar(10)                                   null,
	region_name                      nvarchar(100)                                  null,
	work_shop_name_py                nvarchar(50)                                   null,
	active_fee_flag                  int                                            null,
	management_fee_flag              int                                            null,
	workshopscore                    nvarchar(50)                                   null,
	active_seats_num                 int                                            null,
	isgd_flag                        nvarchar(50)                                   null,
	workshop_owner                   nvarchar(127)                                  null,
	workshop_owner_mobile            nvarchar(64)                                   null,
	workshop_tel                     nvarchar(32)                                   null,
	operation_year                   int                                            null,
	operation_periods                nvarchar(255)                                  null,
	is_holiday_close                 tinyint                                        null,
	payment_type                     nvarchar(255)                                  null,
	digital_management_intention     tinyint                                        null,
	level_promotion_intention        tinyint                                        null,
	auth_product_agency              nvarchar(255)                                  null,
	sale_major_oil_viscosity         nvarchar(255)                                  null,
	sale_major_oil_type              nvarchar(255)                                  null,
	sale_major_oil_brand             nvarchar(255)                                  null,
	car_maintain_numb                int                                            null,
	other_equipment                  nvarchar(255)                                  null,
	other_collocation                nvarchar(255)                                  null,
	nearby_village_numb              nvarchar(127)                                  null,
	spread_way                       nvarchar(255)                                  null,
	marketing_concept                tinyint                                        null,
	has_warehouse                    tinyint                                        null,
	management_maturity              nvarchar(127)                                  null,
	support_maintain_flag            tinyint                                        null,
	shop_recruitment                 int                                            null,
	join_shop_gift_plan              int                                            null,
	join_location_plan               int                                            null,
	shop_recruitment_update_time     datetime                                       null,
	sales_channels                   nvarchar(256)                              not null,
	delo_type                        nvarchar(256)                                  null,
	volume_month                     decimal(20,6)                                  null,
	sale_major_delo_product          nvarchar(256)                                  null,
	delete_batch_no                  nvarchar(64)                                   null,
	dms_key                          nvarchar(256)                                  null,
	dms_match_update_time            datetime                                       null,
	dms_match_update_user            bigint                                         null,
	recruitment_amount               decimal(20,6)                                  null,
	recruitment_confirmed            tinyint                                    not null,
	business_scope                   nvarchar(256)                                  null,
	ext_flag                         int                                        not null,
	shop_numb                        int                                            null,
	primary key (id)
);
CREATE INDEX ix_work_shop_grade_abc ON wx_t_work_shop (grade_abc);

/*==============================================================*/
/* Table: wx_task_sub                                           */
/*==============================================================*/
create table wx_task_sub (
	task_id                          bigint                            not null identity,
	check_evaluation                 int                                            null,
	primary key (task_id)
);

/*==============================================================*/
/* Table: wx_t_locker                                           */
/*==============================================================*/
create table wx_t_locker (
	locker_id                        nvarchar(64)                               not null,
	locker_name                      nvarchar(64)                                   null,
	locked_by                        nvarchar(64)                                   null,
	verion_no                        bigint                                         null,
	effective_time                   int                                        not null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (locker_id)
);

/*==============================================================*/
/* Table: wx_t_wechat_temp_code                                 */
/*==============================================================*/
create table wx_t_wechat_temp_code (
	id                               bigint                            not null identity,
	version_no                       int                                        not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_promotion                                        */
/*==============================================================*/
create table wx_t_promotion (
	id                               bigint                            not null identity,
	partner_id                       bigint                                     not null,
	year                             int                                        not null,
	quarter                          int                                        not null,
	promotion                        decimal(20,6)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_promotion ADD CONSTRAINT uk_promotion UNIQUE (partner_id,year,quarter);

/*==============================================================*/
/* Table: wx_t_amount_config_by_month                           */
/*==============================================================*/
create table wx_t_amount_config_by_month (
	id                               bigint                            not null identity,
	module_name                      nvarchar(256)                              not null,
	config_type                      nvarchar(64)                               not null,
	amount                           decimal(20,6)                                  null,
	effictive_from_month             date                                           null,
	effictive_to_month               date                                           null,
	remark                           nvarchar(1024)                                 null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_amo_con_by_month_mod_name ON wx_t_amount_config_by_month (module_name);
CREATE INDEX ix_amo_con_by_month_con_type ON wx_t_amount_config_by_month (config_type);

/*==============================================================*/
/* Table: wx_t_out_stock_line                                   */
/*==============================================================*/
create table wx_t_out_stock_line (
	id                               bigint                            not null identity,
	sku                              nvarchar(32)                                   null,
	product_count                    int                                            null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_random_code6                                     */
/*==============================================================*/
create table wx_t_random_code6 (
	code                             int                                        not null,
	version_no                       bigint                                     not null,
	used_flag                        int                                        not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (code)
);

/*==============================================================*/
/* Table: wx_t_workshop_employee                                */
/*==============================================================*/
create table wx_t_workshop_employee (
	id                               bigint                            not null identity,
	wechat_open_id                   nvarchar(64)                                   null,
	b2b_open_id                      nvarchar(64)                                   null,
	primary key (id)
);
CREATE INDEX ix_wor_emp_wec_open_id ON wx_t_workshop_employee (wechat_open_id);
CREATE INDEX ix_wor_emp_b2b_open_id ON wx_t_workshop_employee (b2b_open_id);

/*==============================================================*/
/* Table: wx_t_user_property                                    */
/*==============================================================*/
create table wx_t_user_property (
	id                               bigint                            not null identity,
	user_id                          bigint                                     not null,
	property_name                    nvarchar(256)                              not null,
	property_value                   nvarchar(256)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_user_property_user_id ON wx_t_user_property (user_id);

/*==============================================================*/
/* Table: wx_t_supplier                                         */
/*==============================================================*/
create table wx_t_supplier (
	id                               bigint                            not null identity,
	supplier_name                    nvarchar(256)                              not null,
	code                             nvarchar(64)                               not null,
	fun_flag                         bigint                                         null,
	login_name                       nvarchar(64)                                   null,
	enable_flag                      tinyint                                    not null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_supplier ADD CONSTRAINT uk_supplier UNIQUE (code);

/*==============================================================*/
/* Table: wx_t_supplier_partner                                 */
/*==============================================================*/
create table wx_t_supplier_partner (
	partner_id                       bigint                                     not null,
	supplier_id                      bigint                                     not null,
	primary key (partner_id, supplier_id)
);

/*==============================================================*/
/* Table: wx_t_res_permission_type                              */
/*==============================================================*/
create table wx_t_res_permission_type (
	id                               bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_sys_simple_config                                */
/*==============================================================*/
create table wx_t_sys_simple_config (
	id                               bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	module_name                      nvarchar(256)                              not null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(256)                                  null,
	remark                           nvarchar(1024)                                 null,
	primary key (id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_st_co_co_va_st_co_co             */
/*==============================================================*/
CREATE INDEX ix_st_co_co_va_st_co_co ON wx_t_sta_cond_con_value (config_id);

/*==============================================================*/
/* Foreign Key(only index): ix_com_menu_com_menu_mod            */
/*==============================================================*/
CREATE INDEX ix_com_menu_com_menu_mod ON wx_t_common_menu (module_id);

/*==============================================================*/
/* Foreign Key(only index): ix_role_com_menu_com_menu           */
/*==============================================================*/
CREATE INDEX ix_role_com_menu_com_menu ON wx_t_role_common_menu (common_menu_id);

/*==============================================================*/
/* Foreign Key(only index): ix_int_data_det_int_data            */
/*==============================================================*/
CREATE INDEX ix_int_data_det_int_data ON wx_t_interface_data_detail (interface_data_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_app_for_wor                  */
/*==============================================================*/
CREATE INDEX ix_for_app_for_wor ON wx_t_forecasting_approve (worksheet_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_sales_for_wor                */
/*==============================================================*/
CREATE INDEX ix_for_sales_for_wor ON wx_t_forecasting_sales (worksheet_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_sales_for_app                */
/*==============================================================*/
CREATE INDEX ix_for_sales_for_app ON wx_t_forecasting_sales (approve_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_det_for_sales                */
/*==============================================================*/
CREATE INDEX ix_for_det_for_sales ON wx_t_forecasting_detail (forecasting_id);

/*==============================================================*/
/* Foreign Key(only index): ix_supplier_partner_supplier        */
/*==============================================================*/
CREATE INDEX ix_supplier_partner_supplier ON wx_t_supplier_partner (supplier_id);
