package com.chevron.elites2.model;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "wx_t_rebate_apply_v2")
public class WxTRebateApply2 implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private Long id;
    private Long organizationId;//经销商ID
    private String organizationName;//经销商名称

    private BigDecimal applyFund;
    private BigDecimal applyFundCdm;
    private BigDecimal applyFundCio;
    private BigDecimal applyFundPlan;

    private BigDecimal totalFund;
    private BigDecimal totalFundCdm;
    private BigDecimal totalFundCio;
    private BigDecimal totalFundIvi;

    private BigDecimal usedFund;
    private BigDecimal usedFundCdm;
    private BigDecimal usedFundCio;
    private BigDecimal usedFundIvi;

    private BigDecimal remainFund;
    private BigDecimal remainFundCdm;
    private BigDecimal remainFundCio;
    private BigDecimal remainFundIvi;
    private BigDecimal remainIviFund;//上期结余

    private String fundType;//基金类型
    private String fundOwnership;//基金归属
    private String status;//状态
    private String applyPersonName;
    private Date createTime;
    private Long createBy;
    private Date updateTime;
    private Long updateBy;

    private BigDecimal invoiceAmountTotal;
    private BigDecimal invoiceRedundant;
    private Integer process;
    private Integer currentStep;

    private String activityType;//活动类型
    private String activityDesc;//活动说明
    private String billboardType;//广告牌安装类型
    private String exportRemark;
    private String customerInteractionDesc;

    private Date startDate;
    private Date endDate;
    private String descirption;

    private Date startDateActivity;

    private Date endDateActivity;

    private Date startDateBillboard;
    private Date endDateBillboard;
    private Date startDatePromote;
    private Date endDatePromote;

    private String activityTheme;

    private Integer customerNumber;

    private String billboardContent;

    private String deviceDetail;

    private String promoteProduct;

    private String promoteType;

    private Integer promoteNumber;
    private Integer hasRentInvoice;


    private BigDecimal useInvoiceAmount;
    private BigDecimal useIviFund;
    

    public WxTRebateApply2() {
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public BigDecimal getApplyFundCdm() {
        return applyFundCdm;
    }

    public void setApplyFundCdm(BigDecimal applyFundCdm) {
        this.applyFundCdm = applyFundCdm;
    }

    public BigDecimal getApplyFundCio() {
        return applyFundCio;
    }

    public void setApplyFundCio(BigDecimal applyFundCio) {
        this.applyFundCio = applyFundCio;
    }

    public BigDecimal getTotalFundCdm() {
        return totalFundCdm;
    }

    public void setTotalFundCdm(BigDecimal totalFundCdm) {
        this.totalFundCdm = totalFundCdm;
    }

    public BigDecimal getTotalFundCio() {
        return totalFundCio;
    }

    public void setTotalFundCio(BigDecimal totalFundCio) {
        this.totalFundCio = totalFundCio;
    }

    public BigDecimal getUsedFundCdm() {
        return usedFundCdm;
    }

    public void setUsedFundCdm(BigDecimal usedFundCdm) {
        this.usedFundCdm = usedFundCdm;
    }

    public BigDecimal getUsedFundCio() {
        return usedFundCio;
    }

    public void setUsedFundCio(BigDecimal usedFundCio) {
        this.usedFundCio = usedFundCio;
    }

    public BigDecimal getRemainFundCdm() {
        return remainFundCdm;
    }

    public void setRemainFundCdm(BigDecimal remainFundCdm) {
        this.remainFundCdm = remainFundCdm;
    }

    public BigDecimal getRemainFundCio() {
        return remainFundCio;
    }

    public void setRemainFundCio(BigDecimal remainFundCio) {
        this.remainFundCio = remainFundCio;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundOwnership() {
        return fundOwnership;
    }

    public void setFundOwnership(String fundOwnership) {
        this.fundOwnership = fundOwnership;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityDesc() {
        return activityDesc;
    }

    public void setActivityDesc(String activityDesc) {
        this.activityDesc = activityDesc;
    }

    public String getBillboardType() {
        return billboardType;
    }

    public void setBillboardType(String billboardType) {
        this.billboardType = billboardType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApplyPersonName() {
        return applyPersonName;
    }

    public void setApplyPersonName(String applyPersonName) {
        this.applyPersonName = applyPersonName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getCustomerInteractionDesc() {
        return customerInteractionDesc;
    }

    public void setCustomerInteractionDesc(String customerInteractionDesc) {
        this.customerInteractionDesc = customerInteractionDesc;
    }

    public BigDecimal getInvoiceAmountTotal() {
        return invoiceAmountTotal;
    }

    public void setInvoiceAmountTotal(BigDecimal invoiceAmountTotal) {
        this.invoiceAmountTotal = invoiceAmountTotal;
    }

    public String getExportRemark() {
        return exportRemark;
    }

    public void setExportRemark(String exportRemark) {
        this.exportRemark = exportRemark;
    }

    public Integer getProcess() {
        return process;
    }

    public void setProcess(Integer process) {
        this.process = process;
    }

    public String getDescirption() {
        return descirption;
    }

    public void setDescirption(String descirption) {
        this.descirption = descirption;
    }

    public Date getStartDateActivity() {
        return startDateActivity;
    }

    public void setStartDateActivity(Date startDateActivity) {
        this.startDateActivity = startDateActivity;
    }

    public Date getEndDateActivity() {
        return endDateActivity;
    }

    public void setEndDateActivity(Date endDateActivity) {
        this.endDateActivity = endDateActivity;
    }

    public Date getStartDateBillboard() {
        return startDateBillboard;
    }

    public void setStartDateBillboard(Date startDateBillboard) {
        this.startDateBillboard = startDateBillboard;
    }

    public Date getEndDateBillboard() {
        return endDateBillboard;
    }

    public void setEndDateBillboard(Date endDateBillboard) {
        this.endDateBillboard = endDateBillboard;
    }

    public Date getStartDatePromote() {
        return startDatePromote;
    }

    public void setStartDatePromote(Date startDatePromote) {
        this.startDatePromote = startDatePromote;
    }

    public Date getEndDatePromote() {
        return endDatePromote;
    }

    public void setEndDatePromote(Date endDatePromote) {
        this.endDatePromote = endDatePromote;
    }

    public String getActivityTheme() {
        return activityTheme;
    }

    public void setActivityTheme(String activityTheme) {
        this.activityTheme = activityTheme;
    }

    public Integer getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(Integer customerNumber) {
        this.customerNumber = customerNumber;
    }

    public String getBillboardContent() {
        return billboardContent;
    }

    public void setBillboardContent(String billboardContent) {
        this.billboardContent = billboardContent;
    }

    public String getDeviceDetail() {
        return deviceDetail;
    }

    public void setDeviceDetail(String deviceDetail) {
        this.deviceDetail = deviceDetail;
    }

    public String getPromoteProduct() {
        return promoteProduct;
    }

    public void setPromoteProduct(String promoteProduct) {
        this.promoteProduct = promoteProduct;
    }

    public String getPromoteType() {
        return promoteType;
    }

    public void setPromoteType(String promoteType) {
        this.promoteType = promoteType;
    }

    public Integer getPromoteNumber() {
        return promoteNumber;
    }

    public void setPromoteNumber(Integer promoteNumber) {
        this.promoteNumber = promoteNumber;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getCurrentStep() {
        return currentStep;
    }

    public void setCurrentStep(Integer currentStep) {
        this.currentStep = currentStep;
    }

    public BigDecimal getRemainFund() {
        return remainFund;
    }

    public void setRemainFund(BigDecimal remainFund) {
        this.remainFund = remainFund;
    }

    public BigDecimal getApplyFund() {
        return applyFund;
    }

    public void setApplyFund(BigDecimal applyFund) {
        this.applyFund = applyFund;
    }

    public BigDecimal getUseIviFund() {
        return useIviFund;
    }

    public void setUseIviFund(BigDecimal useIviFund) {
        this.useIviFund = useIviFund;
    }

    public BigDecimal getUseInvoiceAmount() {
        return useInvoiceAmount;
    }

    public void setUseInvoiceAmount(BigDecimal useInvoiceAmount) {
        this.useInvoiceAmount = useInvoiceAmount;
    }



    public BigDecimal getUsedFund() {
        return usedFund;
    }

    public void setUsedFund(BigDecimal usedFund) {
        this.usedFund = usedFund;
    }

    public BigDecimal getTotalFund() {
        return totalFund;
    }

    public void setTotalFund(BigDecimal totalFund) {
        this.totalFund = totalFund;
    }

    public BigDecimal getRemainFundIvi() {
        return remainFundIvi;
    }

    public void setRemainFundIvi(BigDecimal remainFundIvi) {
        this.remainFundIvi = remainFundIvi;
    }

    public BigDecimal getUsedFundIvi() {
        return usedFundIvi;
    }

    public void setUsedFundIvi(BigDecimal usedFundIvi) {
        this.usedFundIvi = usedFundIvi;
    }

    public BigDecimal getTotalFundIvi() {
        return totalFundIvi;
    }

    public void setTotalFundIvi(BigDecimal totalFundIvi) {
        this.totalFundIvi = totalFundIvi;
    }

    public BigDecimal getInvoiceRedundant() {
        return invoiceRedundant;
    }

    public void setInvoiceRedundant(BigDecimal invoiceRedundant) {
        this.invoiceRedundant = invoiceRedundant;
    }

    public BigDecimal getApplyFundPlan() {
        return applyFundPlan;
    }

    public void setApplyFundPlan(BigDecimal applyFundPlan) {
        this.applyFundPlan = applyFundPlan;
    }

    public BigDecimal getRemainIviFund() {
        return remainIviFund;
    }

    public void setRemainIviFund(BigDecimal remainIviFund) {
        this.remainIviFund = remainIviFund;
    }

    public Integer getHasRentInvoice() {
        return hasRentInvoice;
    }

    public void setHasRentInvoice(Integer hasRentInvoice) {
        this.hasRentInvoice = hasRentInvoice;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WxTRebateApply2{");
        sb.append("id=").append(id);
        sb.append(", organizationId=").append(organizationId);
        sb.append(", organizationName='").append(organizationName).append('\'');
        sb.append(", applyFund=").append(applyFund);
        sb.append(", applyFundCdm=").append(applyFundCdm);
        sb.append(", applyFundCio=").append(applyFundCio);
        sb.append(", totalFund=").append(totalFund);
        sb.append(", totalFundCdm=").append(totalFundCdm);
        sb.append(", totalFundCio=").append(totalFundCio);
        sb.append(", totalFundIvi=").append(totalFundIvi);
        sb.append(", usedFund=").append(usedFund);
        sb.append(", usedFundCdm=").append(usedFundCdm);
        sb.append(", usedFundCio=").append(usedFundCio);
        sb.append(", usedFundIvi=").append(usedFundIvi);
        sb.append(", remainFund=").append(remainFund);
        sb.append(", remainFundCdm=").append(remainFundCdm);
        sb.append(", remainFundCio=").append(remainFundCio);
        sb.append(", remainFundIvi=").append(remainFundIvi);
        sb.append(", fundType='").append(fundType).append('\'');
        sb.append(", fundOwnership='").append(fundOwnership).append('\'');
        sb.append(", status='").append(status).append('\'');
        sb.append(", applyPersonName='").append(applyPersonName).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", invoiceAmountTotal=").append(invoiceAmountTotal);
        sb.append(", invoiceRedundant=").append(invoiceRedundant);
        sb.append(", process=").append(process);
        sb.append(", currentStep=").append(currentStep);
        sb.append(", activityType='").append(activityType).append('\'');
        sb.append(", activityDesc='").append(activityDesc).append('\'');
        sb.append(", billboardType='").append(billboardType).append('\'');
        sb.append(", exportRemark='").append(exportRemark).append('\'');
        sb.append(", customerInteractionDesc='").append(customerInteractionDesc).append('\'');
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", descirption='").append(descirption).append('\'');
        sb.append(", startDateActivity=").append(startDateActivity);
        sb.append(", endDateActivity=").append(endDateActivity);
        sb.append(", startDateBillboard=").append(startDateBillboard);
        sb.append(", endDateBillboard=").append(endDateBillboard);
        sb.append(", startDatePromote=").append(startDatePromote);
        sb.append(", endDatePromote=").append(endDatePromote);
        sb.append(", activityTheme='").append(activityTheme).append('\'');
        sb.append(", customerNumber=").append(customerNumber);
        sb.append(", billboardContent='").append(billboardContent).append('\'');
        sb.append(", deviceDetail='").append(deviceDetail).append('\'');
        sb.append(", promoteProduct='").append(promoteProduct).append('\'');
        sb.append(", promoteType='").append(promoteType).append('\'');
        sb.append(", promoteNumber=").append(promoteNumber);
        sb.append(", useInvoiceAmount=").append(useInvoiceAmount);
        sb.append(", useIviFund=").append(useIviFund);
        sb.append('}');
        return sb.toString();
    }
}
