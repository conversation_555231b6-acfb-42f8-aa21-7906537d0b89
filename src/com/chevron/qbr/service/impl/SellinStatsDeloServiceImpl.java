package com.chevron.qbr.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.chevron.qbr.dao.SellinStatsDeloMapper;
import com.chevron.qbr.model.SellinStatsByTypeItem;
import com.chevron.qbr.model.SellinStatsItem;
import com.chevron.qbr.model.StatsQueryParam;
import com.chevron.qbr.service.SellinStatsDeloService;
import com.chevron.qbr.util.QbrUtil;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;

@Service
public class SellinStatsDeloServiceImpl implements SellinStatsDeloService{
	
	private Logger logger = LoggerFactory.getLogger(SellinStatsDeloServiceImpl.class);

    @Resource
    SellinStatsDeloMapper sellinStatsDeloMapper;

    @Resource
    WxTUserMapper wxTUserMapper;

    
    
    
	@Override
	public JsonResponse querySellinStatsByMonth(StatsQueryParam param) {
		JsonResponse jsonResponse = new JsonResponse();
//        String paraCheckResult = QbrUtil.queryTimeAdaptation(param);
//        param.setChannelManager(false);
//        if(StringUtils.isBlank(paraCheckResult)){
//            if(param.getPartnerId() == null){
//            	WxTUser wxTUser = ContextUtil.getCurUser();
//                String cai = wxTUser.getCai();
//				Map<String, Object> userInfoParams = new HashMap<String, Object>();
//				userInfoParams.put("cai", cai);
//				WxTUser cmTUser = wxTUserMapper.getChevronUserInfo(userInfoParams);
//                if(wxTUser.getUserId() != 1L && StringUtils.isEmpty(cai)){
//                    paraCheckResult = "partner或者cai的参数不能为空";
//                } else {
//                	if ("BuManager".equals(cmTUser.getUserModel())||"ChannelManager".equals(cmTUser.getUserModel())) {
//						param.setChannelManager(true);
//					} 
//                    setParamCai(wxTUser, param);
//                }
//            }
//        }
//
//        if(StringUtils.isNotBlank(paraCheckResult)){
//            jsonResponse.setErrorMsg(paraCheckResult);
//            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//            return jsonResponse;
//        }
//        QbrUtil.salesChannelAdaptation(param);
//        int quarter = param.getQuarter() == null?0: param.getQuarter();
//        int year = param.getYear();
//        try {
//            List<SellinStatsItem> resultList = new ArrayList<SellinStatsItem>();
//            List<SellinStatsItem> sellinStatsByMonthItems = sellinStatsDeloMapper.querySellinStatsByMonth(param);
//            Map<String,SellinStatsItem> sellinStatsByMonthMap = convertToMap(sellinStatsByMonthItems);
//            int resultSize = sellinStatsByMonthItems == null?0:sellinStatsByMonthItems.size();
//            int totalMonth = quarter == 0? param.getMonth():quarter * 3;
//            if(resultSize != totalMonth || param.getPartnerId() == null){
//                List<SellinStatsItem> targetValuesItems = sellinStatsDeloMapper.selectTargetValueByMonth(param);
//                Map<String,SellinStatsItem> targetValuesMap = convertToMap(targetValuesItems);
//                for(int curMonth=1;curMonth <= totalMonth; curMonth++){
//                    String yearMonthKey= String.format("%d-%02d",year,curMonth);
//                    SellinStatsItem sellinStatsItem = sellinStatsByMonthMap.get(yearMonthKey);
//                    SellinStatsItem targetItem = targetValuesMap.get(yearMonthKey);
//                    if(sellinStatsItem == null){
//                        sellinStatsItem = targetItem;
//                    }
//                    if(sellinStatsItem == null){
//                        sellinStatsItem = new SellinStatsItem();
//                        sellinStatsItem.setPartnerId(param.getPartnerId());
//                        sellinStatsItem.setSalesChannel(param.getSalesChannel());
//                        Calendar calc = Calendar.getInstance();
//                        calc.set(Calendar.YEAR,year);
//                        calc.set(Calendar.MONTH,curMonth -1);
//                        calc.set(Calendar.DAY_OF_MONTH,1);
//                        calc.set(Calendar.HOUR_OF_DAY,0);
//                        calc.set(Calendar.MINUTE,0);
//                        calc.set(Calendar.SECOND,0);
//                        sellinStatsItem.setTransTime(calc.getTime());
//                    }
//                    if(targetItem!=null && targetItem.getTargetValue()!=null
//                            && (sellinStatsItem.getTargetValue()==null || targetItem.getTargetValue().compareTo(sellinStatsItem.getTargetValue()) != 0)){
//                        sellinStatsItem.setTargetValue(targetItem.getTargetValue());
//                    }
//                    resultList.add(sellinStatsItem);
//                }
//            } else {
//                resultList = sellinStatsByMonthItems;
//            }
//            jsonResponse.setListResult(resultList);
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error(e.getLocalizedMessage());
//            jsonResponse.handleException(e, this, ContextUtil.getCurUserId(), com.chevron.qbr.service.SellinStatsService.class.getSimpleName(), param.getYear() + param.getSalesChannel());
//        }

        return jsonResponse;
	}

	@Override
	public JsonResponse querySellinStatsByMonthAndType(StatsQueryParam param) {
		JsonResponse jsonResponse = new JsonResponse();
        // 处理那些参数
        String paraCheckResult = QbrUtil.queryTimeAdaptation(param);

//        if(StringUtils.isBlank(paraCheckResult)){
//            paraCheckResult = QbrUtil.isValidGatherType(param);
//        }

        if(StringUtils.isBlank(paraCheckResult)){
            paraCheckResult = QbrUtil.isValidSalesChannel(param);
        }

        if(StringUtils.isBlank(paraCheckResult)){
            if(param.getPartnerId() == null){
                WxTUser wxTUser = ContextUtil.getCurUser();
                String cai = wxTUser.getCai();
                if(wxTUser.getUserId() != 1L && StringUtils.isEmpty(cai)){
                    paraCheckResult = "partner或者cai的参数不能为空";
                } else {
                    setParamCai(wxTUser, param);
                }
            }
        }

        if(StringUtils.isNotBlank(paraCheckResult)){
            jsonResponse.setErrorMsg(paraCheckResult);
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            return jsonResponse;
        }

        QbrUtil.salesChannelAdaptation(param);

        try {
            List<SellinStatsByTypeItem> sellinStatsByTypeItemList = new ArrayList<SellinStatsByTypeItem>();
            Map<String,SellinStatsItem> sellinStatsItemMap = new LinkedHashMap<String, SellinStatsItem>();
            List<SellinStatsItem> result = sellinStatsDeloMapper.querySellinStatsByMonthAndType(param);
            if(result != null && result.size() >0){
                for(SellinStatsItem sellinStatsItem:result){
                    sellinStatsItemMap.put(getStatProductKey(sellinStatsItem.getProductType(),sellinStatsItem.getTransTime().getTime()),sellinStatsItem);
                }
            }
            Date startDate = param.getStartDate();
            Date endDate = param.getEndDate();
            Calendar curCal = Calendar.getInstance();
            curCal.setTime(new Date(startDate.getTime()));
//            curCal.add(Calendar.SECOND,1);
            if(curCal.get(Calendar.SECOND)!=0){
                curCal.add(Calendar.SECOND,1);
            }
            String[] productTypesKey = null;
            Map<String,String> productTypesMap = null;
            if("CDM".equals(param.getSalesChannel())){
                productTypesMap = QbrUtil.CDM_SELLIN_PRODUCT_TYPES;
                productTypesKey = productTypesMap.keySet().toArray(new String[productTypesMap.size()]);
            } else {
                productTypesMap = QbrUtil.CIO_SELLIN_PRODUCT_TYPES;
                productTypesKey = productTypesMap.keySet().toArray(new String[productTypesMap.size()]);
            }
            while (curCal.getTime().compareTo(endDate) <= 0){
                SellinStatsByTypeItem sellinStatsByTypeItem = null;
                for(String productType:productTypesKey){
                    String statProductKey = getStatProductKey(productType,curCal.getTime().getTime());
                    SellinStatsItem relatedStatsItem = sellinStatsItemMap.get(statProductKey);
                    if(relatedStatsItem == null){
                        relatedStatsItem = new SellinStatsItem();
                        relatedStatsItem.setTransTime(curCal.getTime());
                        relatedStatsItem.setProductType(productType);
                        relatedStatsItem.setActualValue(0D);
                        relatedStatsItem.setTargetValue(0D);
                        relatedStatsItem.setPartnerId(param.getPartnerId());
                    }
                    if(sellinStatsByTypeItem == null){
                        sellinStatsByTypeItem = new SellinStatsByTypeItem(relatedStatsItem);
                    }

                    relatedStatsItem.setProductType(productTypesMap.get(productType));
                    sellinStatsByTypeItem.getStatList().add(relatedStatsItem);
                }
                sellinStatsByTypeItemList.add(sellinStatsByTypeItem);
                curCal.add(Calendar.MONTH,1);
            }
            jsonResponse.setListResult(sellinStatsByTypeItemList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getLocalizedMessage());
            jsonResponse.handleException(e, this, ContextUtil.getCurUserId(), com.chevron.qbr.service.QbrBasicService.class.getSimpleName(), param.getYear() + param.getSalesChannel());
        }

        return jsonResponse;
	}

	@Override
	public JsonResponse querySellinStatsByMonthAndSku(StatsQueryParam param) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JsonResponse queryTotalSellinStatsYTD(StatsQueryParam param) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JsonResponse queryTotalSellinStatsByQuarter(StatsQueryParam param) {
		JsonResponse jsonResponse = new JsonResponse();
        try{
            SellinStatsItem totalSellinStats =  sellinStatsDeloMapper.queryTotalSellinStatsByQuarter(param);
            jsonResponse.setDataResult(totalSellinStats);
        }catch(Exception e){
            logger.error(e.getLocalizedMessage());
            jsonResponse.handleException(e, this, ContextUtil.getCurUserId(), com.chevron.qbr.service.QbrBasicService.class.getSimpleName(), param.getYear() + param.getSalesChannel());
        }
        return jsonResponse;
	}

	@Override
	public JsonResponse queryTotalSellinStatsByPartnerId(StatsQueryParam param) {
		JsonResponse jsonResponse = new JsonResponse();
        try{
            String checkResult = QbrUtil.queryQTDAdaptation(param);
            if(StringUtils.isNotBlank(checkResult)){
                jsonResponse.setErrorMsg(checkResult);
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                return jsonResponse;
            }
            SellinStatsItem totalSellinStats =  sellinStatsDeloMapper.queryTotalSellinStatsByPartnerId(param);
            jsonResponse.setDataResult(totalSellinStats);
        }catch(Exception e){
            logger.error(e.getLocalizedMessage());
            jsonResponse.handleException(e, this, ContextUtil.getCurUserId(), com.chevron.qbr.service.QbrBasicService.class.getSimpleName(), param.getYear() + param.getSalesChannel());
        }
        return jsonResponse;
	}
	
	private String getStatProductKey(String productType,Long transTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(transTime));
        return productType+"_"+calendar.get(Calendar.MONTH);
    }

    private Map<String,SellinStatsItem> convertToMap(List<SellinStatsItem> sellinStatsItemList){
        Map<String,SellinStatsItem> map = new LinkedHashMap<String, SellinStatsItem>();
       if(sellinStatsItemList!=null && sellinStatsItemList.size() >0){
           for(SellinStatsItem sellinStatsItem:sellinStatsItemList){
               String yearMonthKey = getYearMonthKey(sellinStatsItem.getTransTime());
               map.put(yearMonthKey, sellinStatsItem);
           }
       }
       return map;
    }

    private String getYearMonthKey(Date transTime){
        Calendar cal = Calendar.getInstance();
        cal.setTime(transTime);
        String yearMonthKey = String.format("%d-%02d",cal.get(Calendar.YEAR),cal.get(Calendar.MONTH)+1);
        return yearMonthKey;
    }

    private void setParamCai(WxTUser wxTUser, StatsQueryParam param) {
        Map<String, Object> userInfoParams = new HashMap<String, Object>();
        String cai = wxTUser.getCai();
        if (StringUtils.isNotBlank(cai)) {
            userInfoParams.put("cai", cai);
            WxTUser chevronUser = wxTUserMapper.getChevronUserInfo(userInfoParams);
            if (chevronUser != null) {
                String model = chevronUser.getUserModel();
                if ("BuManager".equals(model) || "ChannelManager".equals(model)
                        || "Supervisor".equals(model) || "Sales".equals(chevronUser.getUserModel())) {
                    param.setCai(cai);
                }
            }
        }
    }

}
