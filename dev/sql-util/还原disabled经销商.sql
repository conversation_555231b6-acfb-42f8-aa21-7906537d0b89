select *
--update o set o.status=1
 from wx_t_organization o where status=0 and id in (56468,
56421,
56431)-- order by update_time desc

select * 
--update u set u.status=1
from wx_t_user u where u.user_id in (
select ext_property1 from wx_log where log_type='UserDelete' and create_time>'2020-05-28' --order by id desc
)

select * 
--update w set w.delete_flag=0, w.status=l.ext_property2
from wx_t_work_shop w  
left join wx_log l on w.id=l.ext_property1
where l.log_type='WorkshopDelete' and l.create_time>'2020-05-28'
--select * from wx_log where log_type='WorkshopDelete' and create_time>'2020-05-28' --order by id desc