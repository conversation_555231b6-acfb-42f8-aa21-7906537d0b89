package com.chevron.cdm_mkt.model;

import com.sys.utils.model.Supplier;

public class MktUser {

    private Long userId;
    private String userName;
    private String phone;
    private String email;
    private String cai;
    private String loginName;
    
    public MktUser() {
    	
    }
    
    public MktUser(Supplier supplier) {
    	userId = supplier.getUserId();
    	userName = supplier.getUserName();
    	phone = supplier.getPhone();
    	email = supplier.getEmail();
    	cai = supplier.getCai();
    	loginName = supplier.getLoginName();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCai() {
        return cai;
    }

    public void setCai(String cai) {
        this.cai = cai;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("MktUser{");
        sb.append("userId=").append(userId);
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", phone='").append(phone).append('\'');
        sb.append(", email='").append(email).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
