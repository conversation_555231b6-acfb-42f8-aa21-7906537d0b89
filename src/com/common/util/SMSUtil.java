package com.common.util;

import cn.emay.ResultModel;
import cn.emay.eucp.inter.framework.dto.CustomSmsIdAndMobile;
import cn.emay.eucp.inter.http.v1.dto.request.SmsBatchOnlyRequest;
import cn.emay.eucp.inter.http.v1.dto.request.SmsBatchRequest;
import cn.emay.eucp.inter.http.v1.dto.request.SmsSingleRequest;
import cn.emay.util.AES;
import cn.emay.util.GZIPUtils;
import cn.emay.util.JsonHelper;
import cn.emay.util.http.*;
import cn.hutool.core.collection.CollectionUtil;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.utils.dao.SmsLogMapper;
import com.sys.utils.model.SmsLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 接口文档地址：http://www.b2m.cn/static/doc/sms/moresms_custom.html
 *
 */
public class SMSUtil {
	private static Logger log = LoggerFactory.getLogger(SMSUtil.class);
	private Map<String, String> APPIDS = new HashMap<String, String>();

	// 扩展码
	private String EXTEND_CODE = null;

	// 加密算法
	public static String ALGORITHM = "AES";/// ECB/PKCS5Padding

	// 是否压缩
	public static boolean IS_GZIP = true;

	// 接口地址
	// bjmtn.b2m.cn 北京
	// shmtn.b2m.cn 上海
	private String HOST = null;

	private String MESSAGE_SIGNATURE = null;

	public static long CAPTCHA_TIMEOUT = 1000 * 60 * 10; // 十分钟
	
	public final static String APP_ID_PP = "PP";
	
	public final static String APP_ID_UNKNOWN = "UNKNOWN";

    private String OILSELECT_MSG_EMY_BATCH_SDK = null;

    private String OILSELECT_MSG_EMY_BATCH_KEY = null;
	
	private static SmsLogMapper smsLogMapper = SpringUtils.getBean(SmsLogMapper.class);

	public Map<String, String> getAPPIDS() {
		return APPIDS;
	}

	public void setAPPIDS(Map<String, String> aPPIDS) {
		APPIDS = aPPIDS;
	}

	public String getEXTEND_CODE() {
		return EXTEND_CODE;
	}

	public void setEXTEND_CODE(String eXTEND_CODE) {
		EXTEND_CODE = eXTEND_CODE;
	}

	public String getHOST() {
		return HOST;
	}

	public void setHOST(String hOST) {
		HOST = hOST;
	}

	public String getMESSAGE_SIGNATURE() {
		return MESSAGE_SIGNATURE;
	}

	public void setMESSAGE_SIGNATURE(String mESSAGE_SIGNATURE) {
		MESSAGE_SIGNATURE = mESSAGE_SIGNATURE;
	}

    private ResultModel request(String appId, String secretKey, String algorithm, Object content, String url,final boolean isGzip) {
		Map<String, String> headers = new HashMap<String, String>();
		EmayHttpRequestBytes request = null;
		try {
			headers.put("appId", appId);
			String requestJson = JsonHelper.toJsonString(content);
			byte[] bytes = requestJson.getBytes("UTF-8");
			if (isGzip) {
				headers.put("gzip", "on");
				bytes = GZIPUtils.compress(bytes);
			}
			byte[] parambytes = AES.encrypt(bytes, secretKey.getBytes("UTF-8"), algorithm);
			request = new EmayHttpRequestBytes(url, "UTF-8", "POST", headers, null, parambytes);
		} catch (Exception e) {
			log.error("", e);
		}
		EmayHttpClient client = new EmayHttpClient();
		String code = null;
		String result = null;
		try {
			EmayHttpResponseBytes res = client.service(request, new EmayHttpResponseBytesPraser());
			if (res.getResultCode().equals(EmayHttpResultCode.SUCCESS)) {
				if (res.getHttpCode() == 200) {
					code = res.getHeaders().get("result");
					System.out.println("result code:" + code);
					if (code.equals("SUCCESS")) {
						byte[] data = res.getResultBytes();
						data = AES.decrypt(data, secretKey.getBytes("UTF-8"), algorithm);
						if (isGzip) {
							data = GZIPUtils.decompress(data);
						}
						result = new String(data, "UTF-8");
					}
				} else {
					log.error("请求接口异常,请求码:" + res.getHttpCode());
				}
			} else {
				log.error("请求接口网络异常:" + res.getResultCode().getCode());
			}
		} catch (Exception e) {
			log.error("解析失败", e);
		}
		log.debug("result:" + result);
		ResultModel re = new ResultModel(code, result);
		return re;
	}

	public void initMessageProperty() {
		EXTEND_CODE = (String) Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_EXTENDCODE);
        APPIDS.put((String) Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_SDK),
                (String) Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_KEY));
        OILSELECT_MSG_EMY_BATCH_SDK = (String) Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_BATCH_SDK);
        OILSELECT_MSG_EMY_BATCH_KEY = (String) Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_BATCH_KEY);
		HOST = (String) Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_HOST);
		MESSAGE_SIGNATURE = "【" + Constants.getSystemPropertyByCodeType(Constants.OILSELECT_MSG_EMY_SIGNATURE)
				+ "】";
	}
	
	public static boolean sendSms(final String mobile, String content, final String appId) throws WxPltException {
		if(StringUtils.isBlank(mobile)) {
			throw new WxPltException("发送短信手机号不能为空");
		}
		SMSUtil simpleSMSUtil = new SMSUtil();
		if(simpleSMSUtil.getAPPIDS().size()==0) {
			simpleSMSUtil.initMessageProperty();
		}
		content = simpleSMSUtil.getMESSAGE_SIGNATURE() + content;
		final Date now = DateUtil.getCurrentDate();
		//验证
		Map<String, Object> params = new HashMap<String, Object>(5);
		params.put("mobile", mobile);
		params.put("validateTime", now);
		params.put("appId", appId);
		final SmsLog validateBean = smsLogMapper.validateByParams(params);
//		final Long userId = ContextUtil.getCurUser().getUserId();
		final SmsLog log = new SmsLog();
		log.setCreateTime(now);
		log.setCreateUserId(1l);
		log.setMobile(mobile);
		log.setFromApp(appId);
		log.setContent(content);
		boolean success = false;
		try {
			if(validateBean != null) {
				if((validateBean.getValidateFlag() & 1) == 1) {
					throw new RuntimeException("当前应用已被禁止发送短信");
				}
				if(validateBean.getWarningLimitInOneMin() != null && validateBean.getWarningLimitInOneMin() <= validateBean.getCountByOneMin()) {
					WeixinMessageUtil.weixinAlarmMessagePush("PP-短信发送", "1分钟内发送短信超过" + validateBean.getWarningLimitInOneMin() + "次", 
							"短信攻击告警", validateBean.getAppName() + "短信攻击告警", validateBean.getAppName() + "1分钟内给" + mobile + "发送短信" + 
							validateBean.getCountByOneMin() + "次");
				}
				if(validateBean.getLimitInOneMin() != null && validateBean.getLimitInOneMin() <= validateBean.getCountByOneMinSuccess()) {
					//记录拒绝发送短信
					throw new WxPltException("1分钟内发送短信不能超过" + validateBean.getLimitInOneMin() + "次，请稍后再试！");
				}
				if(validateBean.getDayLimit() != null && validateBean.getDayLimit() <= validateBean.getCountByDay()) {
					throw new WxPltException("24小时内发送短信不能超过" + validateBean.getDayLimit() + "次，请稍后再试！");
				}
			}
			
			//发送短信
			for(String ai : simpleSMSUtil.getAPPIDS().keySet()){
				if(ai==null) {
					throw new RuntimeException("短信密钥为空");
				}
				String secretKey = simpleSMSUtil.getAPPIDS().get(ai);
				SmsSingleRequest pamars = new SmsSingleRequest();
				pamars.setContent(content);
				pamars.setCustomSmsId(mobile);
				pamars.setExtendedCode(simpleSMSUtil.getEXTEND_CODE());
				pamars.setMobile(mobile);
				long currentTime = System.currentTimeMillis();
				ResultModel result = simpleSMSUtil.request(ai,secretKey,SMSUtil.ALGORITHM,pamars, "http://" + simpleSMSUtil.getHOST() + "/inter/sendSingleSMS",SMSUtil.IS_GZIP);
				log.setRequestTime((int)(System.currentTimeMillis() - currentTime) / 1000);
				log.setStatusMsg(result.getResult());
				if("SUCCESS".equals(result.getCode())){
					success = true;
					log.setStatus(SmsLog.STATUS_SUCCESS);
					break;
				}else {
					throw new RuntimeException("短信发送失败");
				}
			}
		} catch (WxPltException e) {
			log.setStatus(SmsLog.STATUS_REJECT);
			log.setStatusMsg(e.getMessage());
			LogUtils.addErrorLog(1l, "com.common.util.SMSUtil.sendSms", e.getMessage(), appId + "," + mobile);
		} catch (RuntimeException e) {
			log.setStatus(SmsLog.STATUS_FAIL);
			log.setStatusMsg(e.getMessage());
			LogUtils.addErrorLog(1l, "com.common.util.SMSUtil.sendSms", e.getMessage(), appId + "," + mobile);
			throw e;
		} finally {
			LogUtils.addLog(new LogTask() {
				
				@Override
				public void execute() throws Exception {
					smsLogMapper.insertSelective(log);
				}
			});
		}
		return success;
	}

    public static boolean batchSendSms(final List<String> mobiles, String content, final String appId) throws WxPltException {
        SMSUtil simpleSMSUtil = new SMSUtil();
        if (CollectionUtil.isEmpty(mobiles)) {
            return true;
        }
        simpleSMSUtil.initMessageProperty();
        if (StringUtils.isBlank(simpleSMSUtil.OILSELECT_MSG_EMY_BATCH_SDK) || StringUtils.isBlank(simpleSMSUtil.OILSELECT_MSG_EMY_BATCH_KEY)) {
            throw new WxPltException("批量发送短信的秘钥为空，请检查系统配置");
        }
        final Date now = DateUtil.getCurrentDate();
        final SmsLog log = new SmsLog();
        log.setCreateTime(now);
        log.setCreateUserId(1l);
        log.setMobile(CollectionUtil.join(mobiles, ","));
        log.setFromApp(appId);
        log.setStatus(SmsLog.STATUS_FAIL);
        log.setContent(content);
        boolean success = false;
        //发送短信
        try {
            List<List<String>> split = CollectionUtil.split(mobiles, 200);
            for (List<String> ms : split) {
                SmsBatchOnlyRequest batchRequest = new SmsBatchOnlyRequest();
                batchRequest.setContent(content);
                batchRequest.setMobiles(ms.toArray(new String[]{}));
                batchRequest.setExtendedCode(simpleSMSUtil.getEXTEND_CODE());
                batchRequest.setRequestValidPeriod(30);
                batchRequest.setRequestTime(System.currentTimeMillis());
                ResultModel result = simpleSMSUtil.request(simpleSMSUtil.OILSELECT_MSG_EMY_BATCH_SDK,
                        simpleSMSUtil.OILSELECT_MSG_EMY_BATCH_KEY,
                        SMSUtil.ALGORITHM,
                        batchRequest,
                        "http://" + simpleSMSUtil.getHOST() + "/inter/sendBatchOnlySMS",
                        SMSUtil.IS_GZIP);
                log.setRequestTime((int) (System.currentTimeMillis() - System.currentTimeMillis()) / 1000);
                if ("SUCCESS".equals(result.getCode())) {
                    success = true;
                    log.setStatus(SmsLog.STATUS_SUCCESS);
                }
            }
        } finally {
            LogUtils.addLog(new LogTask() {
                @Override
                public void execute() throws Exception {
                    smsLogMapper.insertSelective(log);
                }
            });
        }
        return success;
    }
}
