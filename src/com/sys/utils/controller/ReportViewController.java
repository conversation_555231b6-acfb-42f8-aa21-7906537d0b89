package com.sys.utils.controller;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.StringUtils;
import com.sys.file.web.FileManager;
import com.sys.utils.business.ReportViewBizService;
import com.sys.utils.model.AsynProcessStatus;
import com.sys.utils.model.ReportViewParams;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Report ViewSpring MVC Controller
 * <AUTHOR>
 * @version 1.0 2018-12-27 14:20
 */
@Controller
@RequestMapping(value="/reportview")
public class ReportViewController {
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	private final static Logger log = Logger.getLogger(ReportViewController.class);

	/**
	 * Report View列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(ReportViewParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			reportViewBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}

	protected Map<String, Object> buildRequestParams(HttpServletRequest request){
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Enumeration<String> names = request.getParameterNames();
		while(names.hasMoreElements()) {
			String name = names.nextElement();
			paramMap.put(name, request.getParameter(name));
		}
		return paramMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/report/data.do")
	public Map<String,Object> queryReportData(@RequestParam("packageName")String packageName, 
			@RequestParam("viewName")String viewName, @RequestParam(value="defaultValue",required=false)String defaultValue, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			Map<String, Object> paramMap = buildRequestParams(request);
			resultMap.put("data", reportViewBizService.queryForData(packageName, viewName, paramMap, StringUtils.isNotBlank(defaultValue) ? defaultValue : ""));
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryReportData fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/report/pagedata.do")
	public Map<String,Object> queryReportData(@RequestParam("packageName")String packageName, 
			@RequestParam("viewName")String viewName, @RequestParam(value="defaultValue",required=false)String defaultValue, 
			@RequestParam("limit")int limit, @RequestParam("start")long start, 
			@RequestParam("field")String field, @RequestParam("direction")String direction, 
			HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();

		try {
			Map<String, Object> paramMap = buildRequestParams(request);
			reportViewBizService.buildDataForPage(packageName, viewName, limit, start,
					paramMap, StringUtils.isNotBlank(defaultValue) ? defaultValue : "", resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryReportData fail. " + e.getMessage(), e);
		}
		log.warn("resultMap===>"+resultMap);
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/report/chartdata.do")
	public Map<String,Object> queryChartData(@RequestParam("packageName")String packageName, 
			@RequestParam("viewName")String viewName, @RequestParam("xfieldName")String xfieldName,
			@RequestParam("xitems")String[] xitems, @RequestParam(value="zfieldName",required=false)String zfieldName, 
			@RequestParam(value="zitems",required=false)String[] zitems, @RequestParam("yfieldName")String yfieldName, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			Map<String, Object> paramMap = buildRequestParams(request);
			if(StringUtils.isNotBlank(zfieldName)) {
				resultMap.put("data", reportViewBizService.queryChartDataByXyz(packageName, viewName,
						paramMap, xfieldName, xitems, zfieldName, zitems, yfieldName));
			}else {
				//仅有XY轴的数据 
			}
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryReportData fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/report/chartdataxyzbyxz.do")
	public Map<String,Object> queryChartDataXyzByXz(@RequestParam("packageName")String packageName, 
			@RequestParam("viewName")String viewName, @RequestParam("xfieldName")String xfieldName,
			@RequestParam("xitems")String[] xitems, @RequestParam(value="zfieldNames",required=false)String[] zfieldNames, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			Map<String, Object> paramMap = buildRequestParams(request);
			resultMap.put("data", reportViewBizService.queryChartDataByXz(packageName, viewName, paramMap, xfieldName, xitems, zfieldNames));
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryReportData fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
	
	@RequestMapping(value = "/excel/export.do")
	public String exportByExcel(@RequestParam("packageName")String packageName, 
			@RequestParam("viewName")String viewName, @RequestParam("fileName")String fileName, 
			@RequestParam(value="columnInfoDictKey", required=false) String columnInfoDictKey,
			HttpServletRequest request, HttpServletResponse response){
		try {
			CommonUtil.setExportResponseHeader(request, response, fileName, "xlsx");
			Map<String, Object> paramMap = buildRequestParams(request);
			reportViewBizService.exportByExcel(paramMap.get("packageName").toString(), paramMap.get("viewName").toString(), columnInfoDictKey, paramMap, "Sheet1", response.getOutputStream(), true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value = "/excel/synexport.do")
	public JsonResponse synExportByExcel(@RequestParam("packageName")final String packageName, 
			@RequestParam("viewName")final String viewName,
			@RequestParam(value="columnInfoDictKey", required=false)final String columnInfoDictKey,
			HttpServletRequest request, HttpServletResponse response){
		JsonResponse resultMap = new JsonResponse();
		String p = packageName + "." + viewName;
		log.info("synExportByExcel: " + p);
		try {
			Long userId = ContextUtil.getCurUserId();
//			CommonUtil.setExportResponseHeader(request, response, fileName, "xlsx");
			final AsynProcessStatus processStatus = new AsynProcessStatus("com.sys.utils.controller.ReportViewController.synExportByExcel." + p, userId);
			processStatus.setAttrs(new HashMap<String, Object>(2));
			processStatus.setMessage("正在获取数据，请等待......");
			processStatus.save();
			final Map<String, Object> paramMap = buildRequestParams(request);
			new Thread(){
				public void run() {
					try {
						String fileName = System.currentTimeMillis() + "" + CommonUtil.generateSequenceCode("ReportViewExportFile", 2);
						String folderPath = FileManager.fileUploadPath + "ReportViewExport/";
						String filePath = folderPath + fileName + ".xlsx";
						File folder = new File(folderPath);
						if(!folder.exists()){
							folder.mkdirs();
						}
						final File file = new File(filePath);
						log.info("exportFilePath: " + file.getAbsolutePath());
						reportViewBizService.exportByExcel(paramMap.get("packageName").toString(), paramMap.get("viewName").toString(), columnInfoDictKey, paramMap, "Sheet1", new FileOutputStream(file), true);
						processStatus.getAttrs().put("filePath", filePath);
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
						processStatus.save();
					}
				};
			}.start();
			resultMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
			log.info("queryForPageByDeloMgxActivity success." );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.controller.ReportViewController.synExportByExcel", p);
		}
		return resultMap;
	}
	
	@RequestMapping(value = "/excel/exportbycols.do")
	public String exportExcelByCols(@RequestParam("packageName")String packageName, 
			@RequestParam("viewName")String viewName, @RequestParam("fileName")String fileName, 
			@RequestParam(value="columnsDictKey", required=false) String columnsDictKey,
			HttpServletRequest request, HttpServletResponse response){
		try {
			CommonUtil.setExportResponseHeader(request, response, fileName, "xlsx");
			Map<String, Object> paramMap = buildRequestParams(request);
			reportViewBizService.exportExcelByCols(paramMap.get("packageName").toString(), paramMap.get("viewName").toString(), fileName, columnsDictKey, paramMap, "Sheet1", response.getOutputStream(), true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

}
