
/*==============================================================*/
/* Table: pp_user                                               */
/*==============================================================*/
create table pp_user (
	user_code                        nvarchar(64)                               not null,
	user_name_cn                     nvarchar(256)                                  null,
	user_type                        nvarchar(64)                                   null,
	org_id                           bigint                                     not null,
	enable_flag                      tinyint                                    not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (user_code)
);

/*==============================================================*/
/* Table: pp_dist_customer_inspections                          */
/*==============================================================*/
create table pp_dist_customer_inspections (
	id                               bigint                                     not null,
	customer_type                    int                                        not null,
	customer_id                      bigint                                     not null,
	inspections_time                 datetime                                   not null,
	business_type                    int                                        not null,
	enable_flag                      tinyint                                    not null,
	dsr_code                         nvarchar(64)                                   null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_dist_cus_ins_cus_id ON pp_dist_customer_inspections (customer_id);
CREATE INDEX ix_dist_cus_ins_dsr_code ON pp_dist_customer_inspections (dsr_code);

/*==============================================================*/
/* Table: pp_sell_through                                       */
/*==============================================================*/
create table pp_sell_through (
	id                               bigint                            not null identity,
	customer_type                    int                                        not null,
	customer_id                      bigint                                     not null,
	year_month                       date                                           null,
	order_no                         nvarchar(32)                               not null,
	product_code                     nvarchar(32)                               not null,
	sales_unit                       nvarchar(64)                                   null,
	sales_amount                     decimal(20,6)                                  null,
	sales_quantity                   decimal(20,6)                                  null,
	sales_liters                     decimal(20,6)                                  null,
	saler_code                       nvarchar(64)                                   null,
	order_creator                    nvarchar(64)                                   null,
	order_creator_mobile             nvarchar(20)                                   null,
	order_status                     int                                            null,
	order_create_time                datetime                                       null,
	order_paied_time                 datetime                                       null,
	order_cancel_time                datetime                                       null,
	order_delivery_time              datetime                                       null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sell_through_customer_id ON pp_sell_through (customer_id);
CREATE INDEX ix_sell_through_order_no ON pp_sell_through (order_no);
CREATE INDEX ix_sell_through_product_code ON pp_sell_through (product_code);
