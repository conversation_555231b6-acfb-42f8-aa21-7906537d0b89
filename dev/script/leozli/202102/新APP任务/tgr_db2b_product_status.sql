create trigger tgr_db2b_product_status
on wx_t_db2b_product
    for update
as
    declare @oldStatus int, @newStatus int, @sku nvarchar(256), @partnerId bigint;
    --更新前的数据
    select @oldStatus = status, @sku=sku, @partnerId=partner_id from deleted;
    --更新后的数据
    select @newStatus = status from inserted;
    if (@partnerId = -1 and @oldStatus!=@newStatus)
        begin
            update wx_t_db2b_product set status=@newStatus where sku=@sku and partner_id>-1;
        end
go