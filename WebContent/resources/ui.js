;(function($){
	if(!window.UI){
		$(window).resize(function(){
			UI.triggerFitSize(window);
		});
	}
	UI = {
		fitCls: 'ui-size-fit',
		hideCls: 'hide-important',
		preloadedImgs: {},
		preloadImg: function(img){
			if(!this._temp){
				this._temp = $('<div class="ui-temp" style="position: fixed;visibility: hidden;"></div>').appendTo('body');
			}
			if(!this[img]){
				this[img] = $('<img class="loading-img" src="' + img + '"/>').appendTo(this._temp);
			}
		},
		/**
		 * 添加控件的尺寸自适应功能
		 * @param instance [必填]控件实例
		 * @param target [必填]接收自适应事件的目标元素jQuery对象
		 * @param callback [必填]处理自适应事件的回调函数。格式function(width, height)
		 */
		addFitAbility: function(instance, target, callback){
			instance._uiFitSize = callback;
			target.addClass(this.fitCls).data('ui.fit', instance);
		},
		/**
		 * 触发自适应事件
		 * @param content [必填]触发自适应事件的容器
		 */
		triggerFitSize: function(content){
			var panel, thiz = this;
			if(content == window){
				panel = $('body>.' + this.fitCls);
				content = $(window);
			}else{
				panel = content.find('>.' + this.fitCls);
			}
			if(panel.length == 1){
				panel.addClass(this.hideCls);
				setTimeout(function(){
					var width = thiz.getInnerWidth(content), height = thiz.getInnerHeight(content);
					panel.removeClass(thiz.hideCls).data('ui.fit')._uiFitSize.call(UI, width, height);
				}, 1);
			}
		},
		/**
		 * 设置元素宽度
		 * @param $d [必填]操作元素jQuery对象
		 * @param width [必填]元素新宽度值
		 */
		setWidth: function($d, width){
			if(width < 0){
				$d.css('width', 'auto');
			}else{
				$d.css('width', (width - $d.outerWidth(true) + $d.width()) + 'px');
			}
		},
		/**
		 * 设置元素高度
		 * @param $d [必填]操作元素jQuery对象
		 * @param height [必填]元素新高度值
		 */
		setHeight: function($d, height){
			if(height < 0){
				$d.css('height', 'auto');
			}else{
				$d.css('height', (height - $d.outerHeight(true) + $d.height()) + 'px');
			}
		},
		/**
		 * 获取jQuery对象的内容宽度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的内容宽度
		 */
		getInnerWidth: function($d){
			return $d.width();
		},
		/**
		 * 获取jQuery对象的内容高度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的内容高度
		 */
		getInnerHeight: function($d){
			return $d.height();
		},
		/**
		 * 获取jQuery对象的总宽度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的总宽度
		 */
		getOuterWidth: function($d){
			return $d.outerWidth(true);
		},
		/**
		 * 获取jQuery对象的总高度
		 * @param $d 操作jQuery对象
		 * @return jQuery对象的总高度
		 */
		getOuterHeight: function($d){
			return $d.outerHeight(true);
		},
		/**
		 * 创建按钮html代码
		 * @param text [必填]按钮显示文本
		 * @param attrs [必填]按钮属性JSON对象
		 * @return 返回buttonhtml
		 */
		createButton: function(text, attrs){
			var h = ['<button'];
			for(var k in attrs){
				h.push(' ', k, '="', attrs[k] + '"');
			}
			h.push('>', text, '</button>');
			return h.join('');
		},
		/**
		 * 更新按钮状态
		 * @param $button [必填]按钮jQuery对象
		 * @param disabled [必填]按钮新状态
		 */
		updateBtnStatus: function($button, disabled){
			$button.prop('disabled', disabled);
		},
		alertError: function(msg){
			common.alertMes(msg, 'error');
		},
		/**
		 * 类继承
		 * @param parent [必填]父类
		 * @param child [必填]子类
		 * @return 继承后子类
		 */
		extend: function(parent, child){
			var newChild = $.extend({}, parent, child);
			if(parent.base && child.base){
				newChild.base = $.extend(newChild.base, parent.base);
			}
			if(parent.defaults && child.defaults){
				newChild.defaults = $.extend(newChild.defaults, parent.defaults);
			}
			return newChild;
		},
		/**
		 * 构建二叉树
		 * @node 新进节点
		 * @parentNode 树节点
		 */
		buildBinaryTree: function(node, parentNode){
			if(this._isLeftNode(node, parentNode)){
				if(parentNode.leftNode){
					this.buildBinaryTree(node, parentNode.leftNode);
				}else{
					parentNode.leftNode = node;
				}
			}else{
				if(parentNode.rightNode){
					this.buildBinaryTree(node, parentNode.rightNode);
				}else{
					parentNode.rightNode = node;
				}
			}
		},
		//判断新节点是否是树节点的左节点
		_isLeftNode: function(node, parentNode){
			if(node.values){
				for(var i = 0; i < node.values.length; i++){
					if(node.values[i] < parentNode.values[i]){
						return true;
					}else if(node.values[i] > parentNode.values[i]){
						return false;
					}
				}
				return false;
			}else{
				return node.value < parentNode.value;
			}
		},
		/**
		 * 遍历二叉树
		 * @param parentNode 二叉树父节点
		 * @param eachCallback 遍历回调
		 */
		traverseBinaryTree: function(parentNode, eachCallback){
			if(parentNode.leftNode){
				this.traverseBinaryTree(parentNode.leftNode, eachCallback);
			}
			eachCallback(parentNode);
			if(parentNode.rightNode){
				this.traverseBinaryTree(parentNode.rightNode, eachCallback);
			}
		},
		/**
		 * 行列转置
		 * @param records 数据集合
		 * @param keyProperty 转置键属性(空表示只汇总)
		 * @param valueProperty 转置值属性
		 * @param sumProperties 汇总属性集合
		 * @param ukProperties 唯一键属性集合
		 * @param enumCallback 遍历回调
		 * @param ukSeparator 唯一键分隔符。默认'/'
		 */
		rowToColumn: function(records, keyProperty, valueProperty, sumProperties, ukProperties, enumCallback, ukSeparator){
			var currentItem = null;
			ukSeparator = ukSeparator || '/';
			for(var i = 0, len = records.length; i < len; i++){
				var item = records[i];
				item.__uk = ''
				for(var j = 0; j < ukProperties.length; j++){
					item.__uk += ukSeparator + item[ukProperties[j]];
				}
				if(currentItem == null || currentItem.__uk != item.__uk){
					currentItem = item;
					enumCallback.call(this, item, item.__uk);
				}else if(sumProperties){
					//汇总数据
					for(var j = 0; j < sumProperties.length; j++){
						var p = sumProperties[j];
						currentItem[p] = (currentItem[p] || 0) + item[p];
					}
				}
				if(keyProperty && item[valueProperty] && item[keyProperty]){
					currentItem[item[keyProperty]] = (currentItem[item[keyProperty]] || 0) + item[valueProperty];
				}
			}
		}
	};
	
	UI.Base = {
		/** 类 */
		clazz: null,
		/** 实例初始化配置中支持的事件名称集合 */
		configEvents: null,
		/**
		 * 类创建实例
		 * @param opts [可选] 实例配置
		 * @return 创建的实例对象
		 */
		newInstance: function(opts){
			var instance = {clazz: this};
			if(this.defaults){
				$.extend(instance, this.defaults);
			}
			if(opts){
				instance = $.extend(instance, opts);
			}
			if(this.configEvents){
				for(var i = 0; i < this.configEvents.length; i++){
					if(instance[this.configEvents[i]]){
						if(!instance._events){
							instance._events = {};
						}
						instance._events[this.configEvents[i]] = [instance[this.configEvents[i]]];
					}
				}
			}
			if(this.base){
				instance = $.extend(instance, this.base);
			}
			instance.clazz = this;
			return instance;
		},
		base: {
			/**
			 * 触发事件
			 * @param eventName [必填]触发事件名称
			 * @param args [可选]触发事件参数
			 */
			trigger: function(eventName){
				if(this._suspendEvents && this._suspendEvents[eventName]){
					//事件暂停
					return;
				}
				var events = null;
				if(this._events){
					events = this._events[eventName];
				}
				var args = Array.prototype.slice.call(arguments, 1);
				//执行事件前置操作
				if(this.beforeEvents && this.beforeEvents[eventName]){
					if(this.beforeEvents[eventName].apply(this, args) === false){
						return false;
					}
				}
				//触发外部绑定事件
				if(events){
					for(var i = 0; i < events.length; i++){
						if(events[i].apply(this, args) === false){
							return false;
						}
					}
				}
				//执行事件后置操作
				if(this.afterEvents && this.afterEvents[eventName]){
					if(this.afterEvents[eventName].apply(this, args) === false){
						return false;
					}
				}
			},
			/**
			 * 绑定事件
			 * @param eventName [必填]绑定事件名称。格式eventName[.id]。为事件加ID方便解绑事件
			 * @param fun [必填]事件回调
			 */
			on: function(eventName, fun){
				var en = eventName, i = eventName.indexOf('.');
				if(i > 0){
					//事件附加加ID
					en = en.substring(0, i);
					if(!this._eventMap){
						this._eventMap = {};
					}
					this._eventMap[eventName] = fun;
				}
				this._events = this._events || [];
				var events = this._events[en];
				if(events){
					events.push(fun);
				}else{
					this._events[en] = [fun];
				}
			},
			/**
			 * 解绑事件
			 * @param eventName [必填]绑定事件名称
			 */
			off: function(eventName){
				var i = eventName.indexOf('.');
				if(i > 0){
					//解绑指定ID的事件
					var fun = this._eventMap[eventName];
					if(fun){
						var events = this._events[eventName.substring(0, i)];
						for(var i = 0; i < events.length; i++){
							if(events[i] == fun){
								events.splice(i, 1);
							}
						}
					}
				}else{
					//解绑指定事件名称的所有事件
					delete this._events[eventName];
				}
			},
			/**
			 * 暂停指定事件
			 * @param eventName 事件名称
			 */
			suspend: function(eventName){
				if(!this._suspendEvents){
					this._suspendEvents = {};
				}
				this._suspendEvents[eventName] = true;
			},
			/**
			 * 恢复开始指定事件
			 * @param eventName 事件名称
			 */
			start: function(eventName){
				if(this._suspendEvents){
					this._suspendEvents[eventName] = false;
				}
			}
		}
	};
	
	UI.DomBase = UI.extend(UI.Base, {
		/** 控件键值 */
		key: undefined,
		/** 控件绑定DOM的jQuery对象 */
		element: null,
		/** 控件初始化配置DOM ID映射表 */
		initOptsIdMap: {},
		/**
		 * 创建控件实例
		 * @param $d [必填]操作dom的jQuery对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		newInstance: function($d, opts){
			var optsJsonStr = $d.attr('opts'), id = $d.attr('id'), instance = this.createInitInstance($d);
			if(optsJsonStr){
				$.extend(instance, eval('(' + opts + ')'));
			}
			if(id && this.initOptsIdMap[id]){
				$.extend(instance, this.initOptsIdMap[id]);
			}
			if(opts){
				instance = $.extend(instance, opts);
			}
			if(this.configEvents){
				for(var i = 0; i < this.configEvents.length; i++){
					if(instance[this.configEvents[i]]){
						if(!instance._events){
							instance._events = {};
						}
						instance._events[this.configEvents[i]] = [instance[this.configEvents[i]]];
					}
				}
			}
			if(this.base){
				instance = $.extend(instance, this.base);
			}
			instance.element = $d;
			instance.clazz = this;
			if(this.key){
				$d.data(this.key, instance);
			}
			return instance;
		},
		/**
		 * 创建控件初始实例
		 * @param $d 控件绑定DOM的jQuery对象
		 * @return 控件初始实例
		 */
		createInitInstance: function($d){
			return $.extend({}, this.defaults);
		},
		/**
		 * 获取指定jQuery对象绑定的控件实例
		 * @param element 控件绑定的DOM的jQuery对象
		 * @return 控件实例
		 */
		getInstance: function(element){
			if(this.key){
				return element.data(this.key);
			}
			return null;
		},
		base: {
			/**
			 * 获取元素高度
			 * @return {number} 返回元素高度
			 */
			getHeight: function(){
				return UI.getOuterHeight(this.element);
			},
			/**
			 * 获取元素宽度
			 * @return {number} 返回元素宽度
			 */
			getWidth: function(){
				return UI.getOuterWidth(this.element);
			}
		}
	});
})(jQuery);;(function($){
	UI.XScrollbar = UI.extend(UI.DomBase, {
		key: 'ui.xscroll',
		cls: 'ui-x-scrollbar', //样式
		configEvents: ['scroll'],
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), o = this.newInstance($d, opts);
			o.contentEl = $('<div class="ui-scrollbar-holder"></div>').appendTo($d.addClass(this.cls).addClass(this.cls + '-' + o.overflow));
			$d.scroll(function(){
				o.triggerScroll();
			});
			return o;
		},
		base: {
			setWidth: function(width){
				UI.setWidth(this.element, width);
				return this;
			},
			setContentWidth: function(contentWidth){
				UI.setWidth(this.contentEl, contentWidth - 0.5);
				return this;
			},
			scrollLeft: function(scrollLeft){
				if(scrollLeft == undefined){
					return this.element.scrollLeft();
				}
				this.element.scrollLeft(scrollLeft);
			},
			triggerScroll: function(){
				this.trigger('scroll', this.element.scrollLeft());
			}
		},
		defaults: {
			scroll: null, //滚动事件
			overflow: 'auto' //scroll, auto
		}
	});
})(jQuery);;(function($){
	UI.YScrollbar = UI.extend(UI.DomBase, {
		key: 'ui.yscroll',
		cls: 'ui-y-scrollbar', //样式
		configEvents: ['scroll'],
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), o = this.newInstance($d, opts);
			o.contentEl = $('<div class="ui-scrollbar-holder"></div>').appendTo($d.addClass(this.cls).addClass(this.cls + '-' + o.overflow));
			$d.scroll(function(){
				o.triggerScroll();
			});
			return o;
		},
		base: {
			setHeight: function(height){
				UI.setHeight(this.element, height);
				return this;
			},
			setContentHeight: function(contentHeight){
				UI.setHeight(this.contentEl, contentHeight - 0.5);
				return this;
			},
			triggerScroll: function(){
				this.trigger('scroll', this.element.scrollTop());
			},
			scrollTop: function(scrollTop){
				if(scrollTop == undefined){
					return this.element.scrollTop();
				}
				this.element.scrollTop(scrollTop);
			},
			scrollOffset: function(offset){
				var st = this.element.scrollTop();
				st += offset;
				if(st < 0){
					s = 0;
				}
				this.element.scrollTop(st);
				this.trigger('scroll', this.element.scrollTop());
			}
		},
		defaults: {
			scroll: null, //滚动事件
			overflow: 'auto' //scroll, auto
		}
	});
})(jQuery);;(function($){
	UI.Panel = UI.extend(UI.DomBase, {
		key: 'ui.panel',
		configEvents: ['resize'],
		HEADER_CLS: 'ui-panel-header',
		CONTENT_CLS: 'ui-panel-content',
		FOOTER_CLS: 'ui-panel-footer',
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), p = this.newInstance($d, opts);
			if(p.content && p.content.length == 0){
				p.content = null;
			}
			if(p.header && p.header.length == 0){
				p.header = null;
			}
			if(p.footer && p.footer.length == 0){
				p.footer = null;
			}
			if(p.content == null){
				if(p.header != null || p.footer != null){
					throw 'UI.Panel异常。未设置面板conent属性';
				}else{
					p.content = $d;
				}
			}
			if(p.scrollType != 'none'){
				//面板可滚动
				p.scroll = UI.Scrollable.init(this.center[0], {
					scrollType: p.scrollType
				});
				//可滚动面板均要监听其内部元素尺寸变动事件
				p.listenResize = true;
			}
			if(p.fit){
				//添加panel的尺寸自适应功能
				UI.addFitAbility(p, p.scroll ? p.scroll.wrapper : $d, function(width, height){
					p.resize(width, height);
				});
			}
			return p;
		},
		createInitInstance: function($d){
			return $.extend({}, this.defaults, {
				header: $d.find('>.' + this.HEADER_CLS),
				content: $d.find('>.' + this.CONTENT_CLS),
				footer: $d.find('>.' + this.FOOTER_CLS)
			});
		},
		//调整滚动状态
		_fixScroll: function(props){
			if(props.scroll && !props._fixScrollClose){
				props.scroll.trigger('resize');
			}
		},
		base: {
			afterEvents: {
				resize: function(){
					//调整滚动
					UI.Panel._fixScroll(this);
					UI.triggerFitSize(this.element);
				}
			},
			/**
			 * 折叠panel内容区
			 */
			collapse: function(){
				this.content.hide();
				this.element.addClass(this.collapseCls);
				this.trigger('resize');
			},
			/**
			 * 展开panel内容区
			 */
			expand: function(){
				this.content.show();
				this.element.removeClass(this.collapseCls);
				this.trigger('resize');
			},
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度。undefined表示控件宽度不变
			 * @param height [可选]控件新高度。不填表示高度不变
			 */
			resize: function(width, height){
				if(this._sizeInited && (this.width == width || width === undefined) 
						&& (this.height == height || height === undefined)){
					return;
				}
				this._fixScrollClose = true;
				//计算实际设置宽度
				if(width !== undefined && (!this._sizeInited || width != this.width)){
					this.setWidth(width);
					this.width = width;
				}
				//计算实际设置高度
				if(height !== undefined && (!this._sizeInited || height != this.height)){
					if(height > 0){
						//去除头部高度
						var _h = height;
						if(this.header){
							_h -= UI.getOuterHeight(this.header);
						}
						//去除面板尾高度
						if(this.footer){
							_h -= UI.getOuterHeight(this.footer);
						}
					}
					this.setHeight(height);
					this.height = height;
				}
				this.trigger('resize');
				this._fixScrollClose = false;
				this._sizeInited = true;
			},
			/**
			 * 获取元素高度
			 * @return {number} 返回元素高度
			 */
			getHeight: function(){
				if(this.scroll){
					return UI.getOuterHeight(this.scroll.wrapper);
				}else{
					return UI.getOuterHeight(this.element);
				}
			},
			/**
			 * 获取元素宽度
			 * @return {number} 返回元素宽度
			 */
			getWidth: function(){
				if(this.scroll){
					return UI.getOuterWidth(this.scroll.wrapper);
				}else{
					return UI.getOuterWidth(this.element);
				}
			},
			/**
			 * 设置面板宽度
			 * @param width 面板新宽度
			 */
			setWidth: function(width){
				if(this.scroll){
					return UI.setWidth(this.scroll.wrapper, width);
				}else{
					return UI.setWidth(this.element, width);
				}
			},
			/**
			 * 设置面板高度
			 * @param width 面板新高度
			 */
			setHeight: function(height){
				if(this.scroll){
					return UI.setHeight(this.scroll.wrapper, height);
				}else{
					return UI.setHeight(this.element, height);
				}
			}
		},
		defaults: {
			collapseCls: 'ui-panel-collapse',
			fit: false,
			height: -1, //-1表示面板高度自适应
			width: 0,
			minContentWidth: 0, 
			scrollType: 'none', //none, both, x, y
			header: null,
			content: null,
			footer: null
		}
	});
})(jQuery);;(function($){
	/**
	 * 布局器。高度和宽度均不支持自适应。分north, south, west, east, center 5类区域。<br/>
	 * 其中center区有且仅有一个，其他的可有多个，并且north和south区不能滚动，east和west区的宽度固定
	 */
	UI.Layout = UI.extend(UI.DomBase, {
		key: 'ui.layout',
		NORTH_CLS: 'ui-layout-north',
		SOUTH_CLS: 'ui-layout-south',
		CENTER_CLS: 'ui-layout-center',
		WEST_CLS: 'ui-layout-west',
		EAST_CLS: 'ui-layout-east',
		configEvents: ['beforeresize', 'onresize'],
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), p = this.newInstance($d, opts);
			if(p.east && p.east.length == 0){
				p.east = null;
			}
			if(p.west && p.west.length == 0){
				p.west = null;
			}
			if(p.north && p.north.length == 0){
				p.north = null;
			}
			if(p.south && p.south.length == 0){
				p.south = null;
			}
			if(p.center.length != 1){
				throw 'UI.Layout初始化失败。' + (p.center.length == 0 ? 'Layout无center区块' : ('Layout有' + p.center.length + "个区块"));
			}
			if(p.east || p.west){
				if(p.north || p.south){
					p._$centerWrapper = $('<div class="ui-layout-middle-wrapper"></div>').insertBefore(p.center.first());
					if(p.west){
						p._$centerWrapper.append(p.west.css({'float':'left'}));
					}
					p._$centerWrapper.append(p.center.css({'float':'left'}));
					if(p.east){
						p._$centerWrapper.append(p.east.css({'float':'left'}));
					}
					p._$centerWrapper.append('<div style="clear: both;"/>');
				}
			}
			//初始化north区
			if(p.north){
				p.north.each(function(){
					var $d = $(this), instance = UI.Panel.init(this, {
						scrollType: 'none',
						resize: function(){
							this._lheight = this.getHeight();
							p.trigger('resize');
						}
					});
				});
			}
			//初始化south区
			if(p.south){
				p.south.each(function(){
					var $d = $(this), instance = UI.Panel.init(this, {
						scrollType: 'none',
						resize: function(){
							this._lheight = instance.getHeight();
							p.trigger('resize');
						}
					});
				});
			}
			//初始化west区
			if(p.west){
				p.west.each(function(){
					var instance = UI.Panel.init(this);
					instance._lwidth = instance.getWidth();
				});
			}
			//初始化east区
			if(p.east){
				p.east.each(function(){
					var instance = UI.Panel.init(this);
					instance._lwidth = instance.getWidth();
				});
			}
			//初始化center区
			p.center.each(function(){
				var instance = UI.Panel.init(this);
			});
			//初始化scroll
			if(p.scrollType != 'none'){
//				p.scroll = UI.Scrollable.init(dom);
			}
			//添加自适应功能
			if(p.fit){
				UI.addFitAbility(p, p.scroll ? p.scroll.wrapper : $d, function(width, height){
					p.resize(width, height);
				});
			}
			return p;
		},
		createInitInstance: function($d){
			return $.extend({}, this.defaults, {
				north: $d.find('>.' + this.NORTH_CLS),
				south: $d.find('>.' + this.SOUTH_CLS),
				center: $d.find('>.' + this.CENTER_CLS),
				west: $d.find('>.' + this.WEST_CLS),
				east: $d.find('>.' + this.EAST_CLS)
			});
		},
		//调整滚动状态
		_fixScroll: function(props){
			if(props.scroll && !props._fixScrollClose){
				props.scroll.trigger('onresize');
			}
		},
		base: {
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度。undefined表示控件宽度不变
			 * @param height [可选]控件新高度。不填表示高度不变
			 */
			resize: function(width, height){
				//1. 修正参数
				if(width !== undefined && width < this.minWidth){
					width = this.minWidth;
				}
				if(height !== undefined && height < this.minHeight){
					height = this.minHeight;
				}
				//2. 判断尺寸是否有变化
				if(this._sizeInited && (this.width == width || width === undefined) 
						&& (this.height == height || height === undefined)){
					return;
				}
				//3. 准备工作
				this.trigger('beforeresize', width, height);
				this.suspend('resize'); //暂停resize的触发事件，防止变动尺寸中触发resize事件
				var thiz = this;

				//4. 重新设置宽度
				if(!this._sizeInited || (width !== undefined && width != this.width)){
					this.width = width;
					//4.1 设置整体宽度
					this.setWidth(this.width);
					this._contentWidth = UI.getInnerWidth(this.element)
					//4.2 重置north和south区宽度
					if(this.north){
						this.north.each(function(){
							UI.Panel.getInstance($(this)).resize(thiz._contentWidth);
						});
					}
					if(this.south){
						this.south.each(function(){
							UI.Panel.getInstance($(this)).resize(thiz._contentWidth);
						});
					}
				}
				//5. 更新高度
				if(height !== undefined){
					this.height = height;
				}
//				this.center.each(function(){
//					UI.Panel.getInstance($(this)).resize(centerWidth);
//				});
				//6. 触发resize事件
				this._fixScrollClose = true; //关闭滚动条的调整，需要手动调整尺寸
				this.start('resize');
				this.trigger('resize');
				//7. 更新scroll尺寸
				if(this.scroll){
					this.scroll.resize(instance.width, instance.height);
				}
				this._fixScrollClose = false;
				this._sizeInited = true;
			},
			beforeEvents: {
				resize: function(){
					var height = this.height, centerWidth = this._contentWidth, thiz = this;
					//1. 计算center面板height
					if(height > 0){
						if(height < this.minHeight){
							this.height = this.minHeight;
						}else{
							this.height = height;
						}
						this.setHeight(this.height - 0.5);
						height = UI.getInnerHeight(this.element); 
						if(this.north){
							this.north.each(function(){
								var $d = $(this), instance = UI.Panel.getInstance($d);
								if(!instance._lheight){
									instance._lheight = instance.getHeight();
								}
								height -= instance._lheight;
							});
						}
						if(this.south){
							this.south.each(function(){
								var $d = $(this), instance = UI.Panel.getInstance($d);
								if(!instance._lheight){
									instance._lheight = instance.getHeight();
								}
								height -= instance._lheight;
							});
						}
						//重置west区高度
						if(this.west){
							this.west.each(function(){
								UI.Panel.getInstance($(this)).resize(undefined, height);
							});
						}
						//重置east区高度
						if(this.east){
							this.east.each(function(){
								UI.Panel.getInstance($(this)).resize(undefined, height);
							});
						}
					}
					//2. 计算center面板width
					if(this.east){
						this.east.each(function(){
							var $d = $(this), ins = UI.Panel.getInstance($d);
							if(!ins._lwidth){
								ins._lwidth = ins.getWidth();
							}
							centerWidth -= ins._lwidth;
						});
					}
					if(this.west){
						this.west.each(function(){
							var $d = $(this), ins = UI.Panel.getInstance($d);
							if(!ins._lwidth){
								ins._lwidth = ins.getWidth();
							}
							centerWidth -= ins._lwidth;
						});
					}
					//3. 重置center区尺寸
					this.center.each(function(){
						UI.Panel.getInstance($(this)).resize(centerWidth, height);
					});
				}
			},
			afterEvents: {
				resize: function(){
					//调整layout滚动状态
					UI.Layout._fixScroll(this);
				}
			},
			/**
			 * 获取元素高度
			 * @return {number} 返回元素高度
			 */
			getHeight: function(){
				if(this.scroll){
					return UI.getOunterHeight(this.scroll.wrapper);
				}else{
					return UI.getOunterHeight(this.element);
				}
			},
			/**
			 * 获取元素宽度
			 * @return {number} 返回元素宽度
			 */
			getWidth: function($d){
				if(this.scroll){
					return UI.getOunterWidth(this.scroll.wrapper);
				}else{
					return UI.getOunterWidth(this.element);
				}
			},
			/**
			 * 设置面板宽度
			 * @param width 面板新宽度
			 */
			setWidth: function(width){
				if(this.scroll){
					return UI.setWidth(this.scroll.wrapper, width);
				}else{
					return UI.setWidth(this.element, width);
				}
			},
			/**
			 * 设置面板高度
			 * @param width 面板新高度
			 */
			setHeight: function(height){
				if(this.scroll){
					return UI.setHeight(this.scroll.wrapper, height);
				}else{
					return UI.setHeight(this.element, height);
				}
			}
		},
		//默认属性
		defaults: {
			fit: true,
			north: null,
			south: null,
			west: null,
			east: null,
			center: null,
			minWidth: 0, //确保north和south区不出现滚动条
			minHeight: 0, //确保north和south区不出现滚动条
			srollType: 'none'
		}
	});
})(jQuery);;(function($){
	UI.Input = {
		jsonToAttr: function(obj){
			var strs = null;
			for(var k in obj){
				if(strs){
					strs.push(',');
				}else{
					strs = ['{'];
				}
				if(typeof obj[k] == 'string'){
					strs.push(k, ":'", obj[k], "'");
				}else if(typeof obj[k] == 'object'){
					strs.push(k, ":", UI.Input.jsonToAttr(obj[k]));
				}else{
					strs.push(k, ":", obj[k]);
				}
			}
			strs.push("}");
			return strs.join('');
		}
	};
	UI.Button = {
		attrs: ['id', 'style', 'name'],
		create: function(writer, text, onclick, opts){
			var cls = 'ui-button';
			if(opts && opts['class']){
				cls += ' ' + opts['class'];
			}
			writer.push('<span class="', cls, '"><button onmouseenter="$(this).addClass(\'ui-button-hover\');" onmouseout="$(this).removeClass(\'ui-button-hover\');" onclick="', onclick, '"');
			if(opts){
				if(opts.disabled){
					writer.push(' disabled="disabled" class="ui-button-disabled"');
				}
				//处理其他属性
				for(var i = 0; i < this.attrs.length; i++){
					var attr = this.attrs[i];
					if(opts[attr]){
						writer.push(' ', attr, '="', opts[attr], '"');
					}
				}
			}
			writer.push('>', text, '</button></span>');
		},
		setDisabled: function($d, disabled){
			$d = $d.find('button');
			if(disabled){
				$d.addClass('ui-button-disabled');
				$d.removeClass('ui-button-hover');
			}else{
				$d.removeClass('ui-button-disabled');
			}
			$d.prop('disabled', disabled);
		}
	};
	UI.Text = {
		'class': 'UI.Text',
		defValCssClass: 'default-value',
		attrs: ['id', 'value', 'style', 'class', 'name'],
		processOpts: function(opts){
			return opts;
		},
		create: function(writer, opts){
			writer.push('<input type="text" onfocus="this.select();');
			opts = this.processOpts(opts);
			if(opts){
				//处理文本输入默认值
				if(opts.defValue !== undefined){
					opts.required = false;
					if(opts.value === undefined || opts.value === "" || opts.value === null){
						opts.value = opts.defValue;
						opts['class'] = this.defValCssClass + (opts['class'] ? ' ' + opts['class'] : '');
					}
					writer.push("if($(this).hasClass('", this.defValCssClass, 
							"')){this.value = '';$(this).removeClass('", this.defValCssClass, "');}");
					opts.onchange = (opts.onchange ? opts.onchange + ';' : '') + "if(this.value==''){this.value='" 
							+ opts.defValue + "';$(this).addClass('" + this.defValCssClass + "');}";
				}
				//处理验证
				if(opts.validator){
					var validator = this.buildValidator(opts.validator);
					writer.push(this['class'], '.resetValidateStatus(this);');
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('" onchange="if(', this['class'], '.validate(this) === false){return false;}');
					if(opts.onchange){
						writer.push(opts.onchange);
					}
					writer.push('" validator="', UI.Input.jsonToAttr(validator), '"');
				}else{
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('"');
					if(opts.onchange){
						writer.push(' onchange"', opts.onchange, '"');
					}
				}
				//处理其他属性
				for(var i = 0; i < this.attrs.length; i++){
					var attr = this.attrs[i];
					if(opts[attr]){
						writer.push(' ', attr, '="', opts[attr], '"');
					}
				}
				if(opts.readonly){
					writer.push(' readonly="readonly"');
				}
				if(opts.disabled){
					writer.push(' disabled="disabled"');
				}
			}
		},
		buildValidator: function(validator){
			var v = {};
			//处理非空验证
			if(validator.required !== undefined){
				if(typeof validator.required == 'object'){
					v.required = validator.required;
				}else{
					v.required = {value: validator.required};
				}
				if(!v.required.msg){
					v.required.msg = this.getDefRequiredMsg();
				}
			}
			//处理正则验证
			if(validator.pattern){
				if(typeof validator.pattern == 'object'){
					v.pattern = validator.pattern;
				}else{
					v.pattern = {value: validator.pattern};
				}
				if(!v.pattern.msg){
					v.pattern.msg = this.getDefPatternMsg();
				}
			}
			//处理最大长度验证
			if(validator.maxLen){
				if(typeof validator.maxLen == 'object'){
					v.maxLen = validator.maxLen;
				}else{
					v.maxLen = {value: validator.maxLen};
				}
				if(!v.maxLen.msg){
					v.maxLen.msg = this.getDefMaxLenMsg(v.maxLen.value);
				}
			}
			//处理最小长度验证
			if(validator.minLen){
				if(typeof validator.minLen == 'object'){
					v.minLen = validator.minLen;
				}else{
					v.minLen = {value: validator.minLen};
				}
				if(!v.minLen.msg){
					v.minLen.msg = this.getDefMinLenMsg(v.minLen.value);
				}
			}
			return $.extend(validator, v);
		},
		validate: function(dom){
			var $d = $(dom), validator = this.processValidator(eval('(' + $d.attr('validator') + ')'));
			//非空验证
			if(validator.required && validator.required.value && dom.value == ''){
				this.showError($d, validator.required.msg);
				return false;
			}
			//最小长度验证
			if(validator.minLen && dom.value.length < validator.minLen.value){
				this.showError($d, validator.minLen.msg);
				return false;
			}
			//最大长度验证
			if(validator.maxLen && dom.value.length > validator.maxLen.value){
				this.showError($d, validator.maxLen.msg);
				return false;
			}
			//正则验证
			if(validator.pattern && !validator.pattern.value.test(dom.value)){
				this.showError($d, validator.pattern.msg);
				return false;
			}
			return true;
		},
		processValidator: function(validator){
			return validator;
		},
		resetValidateStatus: function(dom){
			
		},
		getDefRequiredMsg: function(){
			return "不能为空";
		},
		getDefPatternMsg: function(){
			return '输入格式不对';
		},
		getDefMaxLenMsg: function(maxLen){
			return '长度不能大于' + maxLen;
		},
		getDefMinLenMsg: function(minLen){
			return '长度不能小于' + minLen;
		},
		setDisabled: function($d, disabled){
			$d.prop('disabled', disabled);
		},
		setReadonly: function($d, readonly){
			$d.prop('readonly', readonly);
		},
		setValue: function($d, value){
			$d.val(value);
		},
		showError: function($d, msg){
			alert(msg);
		}
	};
	UI.Email = $.extend({}, UI.Text, {
		'class': 'UI.Email',
		processValidator: function(validator){
			validator.pattern = {value: /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/};
			if(validator.msg){
				validator.pattern.msg = validator.msg;
			}else{
				validator.pattern.msg = "邮箱地址格式不正确";
			}
			return validator;
		}
	});
	UI.Mobile = $.extend({}, UI.Text, {
		'class': 'UI.Mobile',
		processValidator: function(validator){
			validator.pattern = {value: /^[0-9]{11}$/};
			if(validator.msg){
				validator.pattern.msg = validator.msg;
			}else{
				validator.pattern.msg = "手机号码格式不正确";
			}
			return validator;
		}
	});
	UI.Telphone = $.extend({}, UI.Text, {
		'class': 'UI.Telphone',
		processValidator: function(validator){
			validator.pattern = {value: /^[+]{0,1}(\d){1,3}[ ]?([-]?((\d)|[ ]){1,12})+$/};
			if(validator.msg){
				validator.pattern.msg = validator.msg;
			}else{
				validator.pattern.msg = "电话号码格式不正确";
			}
			return validator;
		}
	});
	
	UI.Number = {
		'class': 'UI.Number',
		defValCssClass: 'default-value',
		attrs: ['id', 'value', 'style', 'class', 'name'],
		processOpts: function(opts){
			if(!opts){
				opts = {validator: {msg: this.getDefPatternMsg()}};
			}else if(!opts.validator){
				opts.validator = {msg: this.getDefPatternMsg()};
			}else if(!opts.validator.msg){
				opts.validator.msg = this.getDefPatternMsg();
			}
			return opts;
		},
		create: function(writer, opts){
			writer.push('<input type="text" onfocus="this.select();');
			opts = this.processOpts(opts);
			if(opts){
				//处理文本输入默认值
				if(opts.defValue !== undefined){
					opts.required = false;
					if(opts.value === undefined || opts.value === "" || opts.value === null){
						opts.value = opts.defValue;
						opts['class'] = this.defValCssClass + (opts['class'] ? ' ' + opts['class'] : '');
					}
					writer.push("if($(this).hasClass('", this.defValCssClass, 
							"')){this.value = '';$(this).removeClass('", this.defValCssClass, "');}");
					opts.onchange = (opts.onchange ? opts.onchange + ';' : '') + "if(this.value==''){this.value='" 
							+ opts.defValue + "';$(this).addClass('" + this.defValCssClass + "');}";
				}
				//处理验证
				if(opts.validator){
					var validator = this.buildValidator(opts.validator);
					writer.push(this['class'], '.resetValidateStatus(this);');
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('" onchange="if(', this['class'], '.validate(this) === false){return false;}');
					if(opts.onchange){
						writer.push(opts.onchange);
					}
					writer.push('" validator="', UI.Input.jsonToAttr(validator), '"');
				}else{
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('"');
					if(opts.onchange){
						writer.push(' onchange"', opts.onchange, '"');
					}
				}
				//处理其他属性
				for(var i = 0; i < this.attrs.length; i++){
					var attr = this.attrs[i];
					if(opts[attr]){
						writer.push(' ', attr, '="', opts[attr], '"');
					}
				}
				if(opts.readonly){
					writer.push(' readonly="readonly"');
				}
				if(opts.disabled){
					writer.push(' disabled="disabled"');
				}
			}
			writer.push('/>');
		},
		buildValidator: function(validator){
			var v = {};
			//处理非空验证
			if(validator.required !== undefined){
				if(typeof validator.required == 'object'){
					v.required = validator.required;
				}else{
					v.required = {value: validator.required};
				}
				if(!v.required.msg){
					v.required.msg = this.getDefRequiredMsg();
				}
			}
			//处理最大值验证
			if(validator.max){
				if(typeof validator.max == 'object'){
					v.max = validator.max;
				}else{
					v.max = {value: validator.max};
				}
				if(!v.max.msg){
					v.max.msg = this.getDefMaxMsg(v.max.value);
				}
			}
			//处理最小值验证
			if(validator.min){
				if(typeof validator.min == 'object'){
					v.min = validator.min;
				}else{
					v.min = {value: validator.min};
				}
				if(!v.min.msg){
					v.min.msg = this.getDefMinMsg(v.min.value);
				}
			}
			return $.extend(validator, v);
		},
		validate: function(dom){
			var $d = $(dom), validator = this.processValidator(eval('(' + $d.attr('validator') + ')'));
			//非空验证
			if(validator.required && validator.required.value && dom.value == ''){
				this.showError($d, validator.required.msg);
				return false;
			}
			//数字格式验证
			if(this.patternValidate(dom) === false){
				this.showError($d, validator.msg);
				return false;
			}
			//最小值验证
			if(validator.min && parseFloat(dom.value) < validator.min.value){
				this.showError($d, validator.min.msg);
				return false;
			}
			//最大值验证
			if(validator.max && parseFloat(dom.value) > validator.max.value){
				this.showError($d, validator.max.msg);
				return false;
			}
			return true;
		},
		patternValidate: function(dom){
			if(isNaN(dom.value)){
				return false;
			}
			return true;
		},
		processValidator: function(validator){
			return validator;
		},
		resetValidateStatus: function(dom){
			
		},
		getDefPatternMsg: function(){
			return "数字格式不对";
		},
		getDefRequiredMsg: function(){
			return "值不能为空";
		},
		getDefMaxMsg: function(max){
			return '值不能大于' + max;
		},
		getDefMinMsg: function(min){
			return '值不能小于' + min;
		},
		setDisabled: function($d, disabled){
			$d.prop('disabled', disabled);
		},
		setReadonly: function($d, readonly){
			$d.prop('readonly', readonly);
		},
		setValue: function($d, value){
			$d.val(value);
		},
		setValidator: function($d, validator){
			$d.attr('validator', UI.Input.jsonToAttr(this.buildValidator(validator)));
		},
		showError: function($d, msg){
			alert(msg);
		}
	};
	UI.Int = $.extend({}, UI.Number, {
		patternValidate: function(dom){
			if(!/^[-]?[1-9]\\d*$/.test(this.value)){
				return false;
			}
			return true;
		},
		getDefPatternMsg: function(){
			return "整数格式不对";
		}
	});
	UI.Date = {
			
	};
	UI.Checkbox = {
			
	};
	UI.Radio = {
			
	};
})(jQuery);
;(function($){
	//技师记录汇总数据
	var calculateSummary = function(records, grid){
		var sumRecord = {};
		for(var i = 0; i < grid.columns.length; i++){
			var col = grid.columns[i];
			if(col.summary){
				calculateColSummary(sumRecord, col, records);
			}
		}
		return sumRecord;
	};
	var calculateColSummary = function(summaryRecord, col, records){
		summaryRecord[col.property] = null;
		for(var j = 0; j < records.length; j++){
			var v = col.propertyProcessor(records[j], col.property);
			if(v === 0 || v){
				summaryRecord[col.property] = (summaryRecord[col.property] || 0) + (v - 0);
			}
		}
	};
	//用排序二叉树构建数据数组
	var buildList = function(list, node){
		if(node._leftNode){
			buildList(list, node._leftNode);
		}
		list.push(node);
		if(node._rightNode){
			buildList(list, node._rightNode);
		}
	};

	//计算总页数
	var calculateTotalPage = function(totalSize, pageSize){
		if(totalSize == 0){
			return 1;
		}
		return totalSize % pageSize == 0 ? totalSize / pageSize : (Math.floor(totalSize / pageSize) + 1);
	};
	
	//分页处理
	var pagingProcess = function(records, store){
		var i = (store.params.pageNo - 1) * store.grid.paging.pageSize, 
			endIndex = Math.min(i + store.grid.paging.pageSize, records.length); r = [];
		for(; i < endIndex; i++){
			r.push(records[i]);
		}
		return r;
	};

	UI.Store = UI.extend(UI.Base, {
		configEvents: ['beforeload', 'afterload'],
		init: function(opts){
			var instance = this.newInstance(opts);
			//初始化数据仓库的数据处理器
			if(instance.url){
				instance._dataHandler = this.dataHandlers.ajax;
			}else{
				instance._dataHandler = this.dataHandlers.local;
			}
			instance._dataHandler.init(instance);
			//扩展数据处理器默认属性
			if(instance._dataHandler.defaults){
				instance = $.extend({}, instance._dataHandler.defaults, instance);
			}
			//扩展数据处理器接口
			if(instance._dataHandler.base){
				instance = $.extend(instance, instance._dataHandler.base);
			}
			if(!instance.params){
				instance.params = {};
			}
			return instance;
		},
		dataHandlers: {
			ajax: {
				init: function(store){
					//初始化数据仓库分页处理器
//					if(instance.paging){
//						instance._pagingHandler = this.dataHandlers.paging;
//					}else{
//						instance._dataHandler = this.dataHandlers.unpaging;
//					}
				},
				base: {
					//表格结果集排序
					sort: function(grid, data, filter, searchText){
					},
					//刷新表格
					load: function(params){
						if(params){
							this.params = $.extend(this.params, params);
						}
						if(this.grid.paging){
							this.clazz.dataHandlers.ajax.pagingProcessBeforeRequest(this);
						}
						this.trigger('beforeload', params);
						this.clazz.dataHandlers.ajax.request(this);
					},
					//刷新列，重新计算列合计
					refreshCols: function(cols){
						throw '后端分页不支持刷新列';
					}
				},
				defaults: {
					url: null,
					method: 'post',
					totalProperty: 'total',
					recordsProperty: 'resultLst',
					errorMsgPrefix: '数据加载失败。',
					summaryProperty: 'summary'
				},
				pagingProcessBeforeRequest: function(instance){
					var pageNo = instance.params.pageNo || instance.grid.paging.pageNo;
					instance.params.limit = instance.grid.paging.pageSize;
					instance.params.start = instance.grid.paging.pageSize * (pageNo - 1);
				},
				pagingProcessAfterRequest: function(instance, result){
					var totalCount = result[instance.totalProperty], totalPage = calculateTotalPage(totalCount, instance.grid.paging.pageSize);
					instance.params.pageNo = Math.min(instance.params.pageNo, totalPage);
					instance.grid.paging.update(totalCount, instance.params.pageNo, totalPage, instance.grid);
				},
				request: function(instance){
					var self = this;
					if(instance.method == 'post'){
						$.post(instance.url, instance.params, function(result){
							self.afterRequestProcess(result, instance);
						}, 'json');
					}else{
						$.getJson(instance.url, instance.params, function(result){
							self.afterRequestProcess(result, instance);
						});
					}
				},
				afterRequestProcess: function(result, instance){
					if(this.validateResponse(result, instance) !== false){
						var data = {
								records: result[instance.recordsProperty]
						};
						if(instance.grid.hasSummary){
							data.summary = result[instance.summaryProperty];
						}
						if(instance.grid.paging){
							this.pagingProcessAfterRequest(instance, result);
						}
						instance.trigger('afterload', data);
						instance.grid.renderData(data);
						instance.trigger('aftershow');
					}
				},
				validateResponse: function(result, instance){
					if(result.code == 'success'){
						return true;
					}else{
						UI.alertError(instance.errorMsgPrefix + result.errorMsg)
						return false;
					}
				}
			},
			local: {
				init: function(store){
					//初始化数据仓库分页处理器
//					if(instance.paging){
//						instance._pagingHandler = this.dataHandlers.paging;
//					}else{
//						instance._dataHandler = this.dataHandlers.unpaging;
//					}
				},
				base: {
					//设置表格结果集
					setResult: function(data){
						this.data = data;
						this._summaryRecord = null;
						this.trigger('afterloaddata', this.data);
						this.load({pageNumb: 1});
					},
					//全局搜索
					search: function(searchText){
						this._searchText = searchText;
						this.load({pageNumb: 1});
					},
					//表格结果集排序
					sort: function(grid, data, filter, searchText){
//						if(data && data.length > 0){
//							var newData = [], len = data.length, root = null;
//							for(var i = 0; i < len; i++){
//								var item = data[i];
//								if(filter && filter.call(grid, item, searchText)){
//									//初始化二叉树链接
//									item._leftNode = item._rightNode = null;
//									if(root == null){
//										root = data[i];
//									}else{
//										root = buildBTree(root, data[i]);
//									}
//								}
//							}
//							buildList(newData, root);
//							return newData;
//						}
//						return data;
					},
					//刷新表格
					load: function(params){
						if(params){
							this.params = $.extend(this.params, params);
						}
						this.trigger('beforeload', params);
						var records = this.data;
						var data = {};
						if(records && records.length > 0){
							//TODO 分页排序处理
							//						grid._data = this.sort(grid, grid.data, grid._searchText ? grid.filter : null, grid._searchText);
							//TODO 排序处理
							//分页处理
							if(this.grid.paging){
								this.params.pageNo = this.params.pageNo || this.grid.paging.pageNo;
								var totalPage = calculateTotalPage(records.length, this.grid.paging.pageSize);
								this.params.pageNo = Math.min(this.params.pageNo, totalPage);
								records = pagingProcess(records, this);
								this.grid.paging.update(this.data.length, this.params.pageNo, totalPage, this.grid);
							}
							data.records = records;
							if(this.grid.hasSummary){
								if(this._summaryRecord){
									data.summary = this._summaryRecord;
								}else{
									this._summaryRecord = data.summary = calculateSummary(this.data, this.grid);
								}
							}
						}
//						grid.trigger('_datachange', grid._data);
						this.grid.renderData(data);
//						grid._dataHandler.renderData(grid, grid._data, params);
						this.trigger('afterload');
					},
					//刷新列，重新计算列合计
					refreshCols: function(cols){
						if(this._summaryRecord){
							for(var i = 0; i < cols.length; i++){
								var col = cols[i];
								if(col.summary){
									calculateColSummary(this._summaryRecord, col, this.data);
								}
							}
						}
					}
				}
			}
		},
		base: {
			bindGrid: function(grid){
				this.grid = grid;
			},
			refresh: function(){
				this.load();
			},
			load: function(params){
				throw '为实现load方法';
			}
		},
		defaults: {
			autoLoad: true //自动加载数据
		}
	});
})(jQuery);;(function($){
	UI.Paging = UI.extend(UI.Base, {
		init: function(opts){
			var instance = this.newInstance(opts);
			instance.style = this.styles[instance.style];
			return instance;
		},
		base: {
			update: function(totalCount, pageNo, totalPage, grid){
				this.style.update(totalCount, pageNo, totalPage, grid);
			}
		},
		defaults: {
			pageSize: 10,
			pageNo: 1,
			style: 'default'
		},
		styles: {
			'default': {
				update: function(totalCount, pageNo, totalPage, grid){
					if(!grid._paging){
						var h = ['<div class="page-size-bar">每页显示<input class="page-size-value" type="hidden" value="',
						         grid.paging.pageSize, '"/><span class="page-size-select"></span>条记录</div><div class="ui-paging-bar ui-paging-bar-default">'];
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: 1});', {
							'class': 'ui-paging-first-btn ui-paging-btn',
							disabled: pageNo == 1
						});
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: grid.store.params.pageNo - 1});', {
							'class': 'ui-paging-pre-btn ui-paging-btn',
							disabled: pageNo == 1
						});
						h.push('<span class="ui-page-bar-separator"/><span class="ui-total-page">共<span class="ui-total-page-value">', totalPage, 
						'</span>页</span><span class="ui-input-page-no">第');
						UI.Int.create(h, {
							defValue: 1,
							value: pageNo,
							disabled: totalPage == 1,
							'class': 'ui-page-no-int',
							validator: {
								min: 1,
								max: totalPage
							}
						});
						h.push('页');
						UI.Button.create(h, '确定', "var $g = $(this).parents('." + UI.Grid.keyCls + 
								":first'), grid = UI.Grid.getInstance($g), pageNo = grid._paging.find('.ui-input-page-no>input').val();if(!isNaN(pageNo)){grid.store.load({pageNo: parseInt(pageNo)});}", {
							'class': 'ui-page-no-go-btn',
							disabled: totalPage == 1
						});
						h.push('</span><span class="ui-page-bar-separator"/>');
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: grid.store.params.pageNo + 1});', {
							'class': 'ui-paging-next-btn ui-paging-btn',
							disabled: pageNo == totalPage
						});
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: grid._totalPage});', {
							'class': 'ui-paging-last-btn ui-paging-btn',
							disabled: pageNo == totalPage
						});
						h.push('<span class="ui-page-bar-separator"/><span class="ui-total-record">共<span class="ui-total-record-value">',
								totalCount, '</span>条记录</span></div>');
						grid._paging = $(h.join('')).appendTo(grid.pagingContainer);
						BUI.use('bui/select',function(Select){
							var items = ['10', '20', '25', '50', '100'];
							for(var i = 0; i < items.length; i++){
								if(grid.paging.pageSize < items[i] - 0){
									items.splice(i, 0, grid.paging.pageSize + '');
									break;
								}else if(grid.paging.pageSize == items[i] - 0){
									break;
								}
							}
							var ctrl = new Select.Select({
								render:grid._paging.find('.page-size-select'),
								valueField: grid._paging.find('.page-size-value'),
								elStyle: {'white-space': 'nowrap'},
								items: items
							});
							ctrl.render();
							ctrl.on('change', function(e){
								grid.paging.pageSize = e.value - 0;
								grid.store.load({pageNo: 1});
							});
						});
					}else{
						UI.Button.setDisabled(grid._paging.find('.ui-paging-first-btn'), pageNo == 1);
						UI.Button.setDisabled(grid._paging.find('.ui-paging-pre-btn'), pageNo == 1);
						UI.Button.setDisabled(grid._paging.find('.ui-page-no-go-btn'), totalPage == 1);
						UI.Button.setDisabled(grid._paging.find('.ui-paging-next-btn'), pageNo == totalPage);
						UI.Button.setDisabled(grid._paging.find('.ui-paging-last-btn'), pageNo == totalPage);
						var $int = grid._paging.find('.ui-page-no-int');
						UI.Int.setDisabled($int, totalPage == 1);
						UI.Int.setValue($int, pageNo);
						grid._paging.find('.ui-total-page-value').text(totalPage);
						grid._paging.find('.ui-total-record-value').text(totalCount);
					}
					grid._totalPage = totalPage;
				}
			}
		}
	});
})(jQuery);;(function($){
	//构建表头
	var buildHeader = function(headers, columns, grid){
		var sheet = [], firstRow = {cls: grid.clazz.HEADER_ROW_CLS, cells: [], id: grid.id + '_row' + grid.counter++};
		sheet.push(firstRow);
		for(var i = 0; i < headers.length; i++){
			buildHeaderCell(headers[i], 0, firstRow.cells.length, firstRow.cells, sheet, columns, grid);
		}
		return sheet;
	};
	//构建表头单元格
	var buildHeaderCell = function(column, rowIndex, colIndex, cells, sheet, columns, grid){
		var cell = cells[cells.length] = $.extend({}, grid.clazz.cellDefaults, {
			rowIndex: rowIndex,
			colIndex: colIndex,
			rowId: sheet[rowIndex].id,
			title: column.title,
			valign: 'middle',
			tdCls: (column.tdCls ? column.tdCls + ' ' : '') + 'ui-grid-col' + colIndex,
			cls: grid.clazz.HEADER_CELL_CLS,
			id: grid.id + '_cell' + grid.counter++
		});
		grid.headerElMap[cell.id] = cell;
		var cs = grid.headerCells[colIndex];
		if(!cs){
			cs = grid.headerCells[colIndex] = [];
		}
		cs.push(cell);
		if(column.children && column.children.length > 0){
			//初始化新行
			var newRow, newRowIndex = rowIndex + 1;
			if(sheet.length == newRowIndex){
				//创建新行
				newRow = {cls: grid.clazz.HEADER_ROW_CLS, cells: [], id: grid.id + '_row' + grid.counter++};
				sheet.push(newRow);
				//更新前面列的rowSpan属性
				for(var i = 0; i < colIndex; i++){
					newRow.cells.push($.extend({}, grid.clazz.cellDefaults, {
						colIndex: i,
						rowIndex: rowIndex,
						mergedBy: sheet[rowIndex].cells[i].addRowSpan(1, newRow.id).getPhysicalCell()
					}));
				}
			}else{
				newRow = sheet[newRowIndex];
			}
			for(var i = 0; i < column.children.length; i++){
				if(i > 0){
					var newColIndex = newRow.cells.length;
					//创建新列，更新前一列的colSpan属性
					for(var j = 0; j < newRowIndex; j++){
						var c = sheet[j].cells[newColIndex - 1];
						sheet[j].cells.push($.extend({}, grid.clazz.cellDefaults, {
							rowIndex: j,
							colIndex: newColIndex,
							mergedBy: sheet[j].cells[newColIndex - 1].addColSpan(1, newColIndex).getPhysicalCell()
						}));
					}
				}
				buildHeaderCell(column.children[i], newRowIndex, newRow.cells.length, newRow.cells, sheet, columns, grid);
			}
		}else{
			//叶子column
			columns.push($.extend({}, grid.clazz.columnDefaults, column));
			if(column.endLock){
				grid.endLockIndex = colIndex;
			}
			if(column.summary && grid.summary){
				if(!column.property){
					throw '合计列' + column.title + '的property属性不能为空';
				}
				grid.hasSummary = true;
			}
			cell.tdCls += ' ui-grid-header-leaf';
			//补齐叶子节点下方的合并单元格
			var rowNo = rowIndex + 1;
			if(sheet.length > rowNo){
				for(var i = rowNo; i < sheet.length; i++){
					sheet[i].cells.push($.extend({}, grid.clazz.cellDefaults, {
						rowIndex: i,
						colIndex: colIndex,
						mergedBy: cell.addRowSpan(1, sheet[rowIndex].id).getPhysicalCell()
					}));
				}
			}
		}
	};
	//构建部分sheet
	var buildPartSheet = function(sheet, startColIndex, endColIndex, partFlag, elMap){
		var partSheet = [];
		for(var i = 0; i < sheet.length; i++){
			var row = sheet[i], isMergedRow = true, cells = [];
			for(var j = startColIndex; j <= endColIndex; j++){
				//构建新行数据
				var cell = row.cells[j];
				if(cell.mergedBy == null){
					isMergedRow = false;
				}
				cells.push(cell);
			}
			if(isMergedRow){
				partSheet[partSheet.length - 1].mergeRows.push(row.id);
				//整行数据被合并
				for(var j = startColIndex; j <= endColIndex; j++){
					row.cells[j].addRowSpan(-1);
				}
			}else{
				var newRow = partSheet[partSheet.length] = {
						id: row.id,
						domId: row.id + partFlag, //rowId用来定位插入新数据
						cls: row.cls,
						cells: cells,
						mergeRows: [],
						events: row.events
					};
				elMap[newRow.domId] = newRow;
				if(!row.domIds){
					row.domIds = [];
				}
				row.domIds.push(newRow.domId);
			}
		}
		for(var i = 0; i < partSheet.length; i++){
			var lastCell = partSheet[i].cells[partSheet[i].cells.length - 1].getPhysicalCell();
			if(lastCell.tdCls){
				lastCell.tdCls += ' ui-grid-part-last-td';
			}
		}
		return partSheet;
	};

	//渲染表内容
	var renderSheetBody = function(sheet, htmlBuffer, grid){
		for(var i = 0; i < sheet.length; i++){
			var row = sheet[i];
			htmlBuffer.push('<tr id="', row.domId ? row.domId : row.id, '" class="', row.cls, '"');
			if(row.events){
				for(var k in row.events){
					htmlBuffer.push(' ', k, '="', row.events[k], '"');
				}
			}
			htmlBuffer.push('>');
			for(var j = 0; j < row.cells.length; j += row.cells[j].colSpan){
				var cell = row.cells[j];
				//有行合并
				if(cell.mergedBy){
					continue;
				}
				htmlBuffer.push('<td align="center" valign="', cell.valign, '" id="', cell.id, '"');
				if(cell.colSpan > 1){
					htmlBuffer.push(' colSpan="', cell.colSpan, '"');
				}
				var tdCls = 'ui-grid-td ui-grid-td-colspan' + cell.colSpan + ' ui-grid-td-rowspan' + cell.rowSpan + ' ' + cell.tdCls, cls = 'ui-grid-cell ' + cell.cls;
				htmlBuffer.push(' class="', tdCls, '"');
				if(cell.rowSpan > 1){
					htmlBuffer.push(' rowSpan="', cell.rowSpan, '"');
				}
				var title = cell.title === 0 ? 0 : (cell.title || '&nbsp;');
				htmlBuffer.push('><div class="', cls, '">', title, '</div></td>');
			}
			htmlBuffer.push('</tr>');
		}
	};
	//刷新列
	var refreshCol = function(colIndex, grid){
		for(var i = 0; i < grid.dataRows.length; i++){
			var row = grid.dataRows[i];
			if(row.notAvailable){
				continue;
			}
			var cell = row.cells[colIndex];
			if(cell.mergedBy || cell.fixedTitle){
				continue;
			}
			if(!cell.cellElement){
				cell.cellElement = $('#' + cell.id).find('>.ui-grid-cell');
			}
			var record = row.record, col = grid.columns[colIndex],
				title = col.formatter(row.fixedProperty ? record[col.property] : col.propertyProcessor(record, col.property), record, i, row.id);
			cell.cellElement.empty().html(title);
			//重置单元格尺寸
			cell.width = null;
			row.heightReseted = true;
		}
	};
	//刷新行
	var refreshRow = function(rowId, grid){
		var curRow = grid.dataElMap[rowId];
		for(var i = 0; i < grid.dataRows.length; i++){
			var row = grid.dataRows[i];
			if(row != curRow){
				continue;
			}
			for(var j = 0, len = row.cells.length; j < len; j++){
				var cell = row.cells[j];
				if(cell.mergedBy || cell.fixedTitle){
					continue;
				}
				if(!cell.cellElement){
					cell.cellElement = $('#' + cell.id).find('>.ui-grid-cell');
				}
				var record = row.record, col = grid.columns[j],
					title = col.formatter(row.fixedProperty ? record[col.property] : col.propertyProcessor(record, col.property), record, i, row.id);
				cell.cellElement.empty().html(title);
				//重置单元格尺寸
				cell.width = null;
				row.heightReseted = true;
			}
			break;
		}
	};

	//列锁定处理器
	var renderHandlers = {
			colLock: {
				lockFlag: '_locked',
				unlockFlag: '_unlocked',
				//渲染x轴滚动区
				renderXScroll: function(grid){
					grid.lockedXScrollEmpty = $('<div class="ui-grid-x-scroll-empty"></div>').appendTo(grid.xScrollContainer);
					grid.xScrollbar = UI.XScrollbar.init($('<div class="ui-grid-x-scroll-bar"></div>')
							.appendTo(grid.xScrollContainer)[0], {
						overflow: grid.xOverflow,
						scroll: function(scrollLeft){
							renderHandlers.colLock.xscroll(scrollLeft, grid);
						}
					});
					grid.bodyContainer.on('mousewheel', function(e){
						grid.yScrollbar.scrollOffset(e.originalEvent.wheelDelta * -1);
					});
				},
				//渲染表
				buildTableHtml: function(sheet, elMap, grid){
					//渲染锁定区域
					var h = ['<div class="ui-grid-locked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>'];
					//渲染表格宽度组
					for(var i = 0; i <= grid.endLockIndex; i++){
						h.push('<col/>');
					}
					h.push('</colgroup><tbody>');
					//拆锁定区域sheet
					var lockedSheet = buildPartSheet(sheet, 0, grid.endLockIndex, this.lockFlag, elMap);
					//渲染锁定区域表
					renderSheetBody(lockedSheet, h, grid);
					h.push('</tbody></table></div><div class="ui-grid-unlocked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>');
					//渲染表格宽度组
					for(var i = grid.endLockIndex + 1; i < grid.columns.length; i++){
						h.push('<col/>');
					}
					h.push('</colgroup><tbody>');
					//拆未锁定区域sheet
					var unlockedSheet = buildPartSheet(sheet, grid.endLockIndex + 1, grid.columns.length - 1, this.unlockFlag, elMap);
					//渲染未锁定区域表
					renderSheetBody(unlockedSheet, h, grid);
					h.push('</tbody></table></div>');
					return h;
				},
				emptySummary: function(grid){
					if(grid.sumRender == 'header'){
						grid.headerContainer.find('>div>table>tbody>.ui-grid-summary-row').remove();
					}else{
						grid.summaryContainer.empty();
					}
				},
				renderSummary: function(summarySheet, grid){
					//拆锁定区域sheet
					var lockedSheet = buildPartSheet(summarySheet, 0, grid.endLockIndex, this.lockFlag, grid.dataElMap);
					//拆未锁定区域sheet
					var unlockedSheet = buildPartSheet(summarySheet, grid.endLockIndex + 1, grid.columns.length - 1, this.unlockFlag, grid.dataElMap);
					if(grid.sumRender == 'header'){
						var h = [];
						//给合计行加样式
						for(var i = 0; i < summarySheet.length; i++){
							var row = summarySheet[i];
							if(row.cls){
								row.cls += ' ui-grid-summary-row';
							}
						}
						//渲染锁定区域表
						renderSheetBody(lockedSheet, h, grid);
						grid.headerContainer.find('>.ui-grid-locked>table>tbody').append(h.join(''));
						h = [];
						//渲染未锁定区域表
						renderSheetBody(unlockedSheet, h, grid);
						grid.headerContainer.find('>.ui-grid-unlocked>table>tbody').append(h.join(''));
					}else{
						//渲染锁定区域
						var h = ['<div class="ui-grid-locked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>'];
						//渲染表格宽度组
						for(var i = 0; i <= grid.endLockIndex; i++){
							h.push('<col/>');
						}
						h.push('</colgroup><tbody>');
						//渲染锁定区域表
						renderSheetBody(lockedSheet, h, grid);
						h.push('</tbody></table></div><div class="ui-grid-unlocked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>');
						//渲染表格宽度组
						for(var i = grid.endLockIndex + 1; i < grid.columns.length; i++){
							h.push('<col/>');
						}
						h.push('</colgroup><tbody>');
						//渲染未锁定区域表
						renderSheetBody(unlockedSheet, h, grid);
						h.push('</tbody></table></div>');
						grid.summaryContainer.append(h.join(''));
					}
				},
				fixResize: function(width, height, grid, scrollbarChanged, scrollbarWidth){
					if(grid.width == width){
						return;
					}
					if(!scrollbarChanged){
						grid.yScrollbar._scrollTop = grid.yScrollbar.scrollTop();
						grid.yScrollbar.setContentHeight(0).setHeight(50);
						scrollbarWidth = UI.getOuterWidth(grid.yScrollbar.element);
					}
					var allCells = [], gridWidth = width - scrollbarWidth;
					//设置列宽
					this._fixColWidth(gridWidth, allCells, grid);
					//设置分页区宽度
					if(grid.pagingContainer){
						UI.setWidth(grid.pagingContainer, gridWidth);
					}
					//设置行高
					this._fixRowHeight(height, allCells, grid);
					var newScrollbarWidth = UI.getOuterWidth(grid.yScrollbar.element);
					if(newScrollbarWidth != scrollbarWidth){
						this.fixResize(width, height, grid, true, newScrollbarWidth);
						return;
					}else if(scrollbarWidth > 1){
						UI.setWidth(grid.headerYScrollEmpty, scrollbarWidth);
						if(grid.summaryYScrollEmpty){
							UI.setWidth(grid.summaryYScrollEmpty, scrollbarWidth);
						}
						UI.setWidth(grid.xyScrollEmpty, scrollbarWidth);
//						UI.setWidth(grid.yScrollContainer, scrollbarWidth + 1);
					}
					UI.setWidth(grid.headerContainer, gridWidth);
					UI.setWidth(grid.bodyContainer, gridWidth);
					if(grid.summaryContainer){
						UI.setWidth(grid.summaryContainer, gridWidth);
					}
					UI.setWidth(grid.xScrollContainer, gridWidth);
					if(grid.pagingContainer){
						UI.setWidth(grid.pagingContainer, gridWidth);
					}
				},
				//添加记录行样式
				addRowClass: function(rowId, cls){
					$('#' + rowId + this.lockFlag).addClass(cls);
					$('#' + rowId + this.unlockFlag).addClass(cls);
				},
				//移除记录行样式
				removeRowClass: function(rowId, cls){
					$('#' + rowId + this.lockFlag).removeClass(cls);
					$('#' + rowId + this.unlockFlag).removeClass(cls);
				},
				//x轴滚动
				xscroll: function(scrollLeft, grid){
					var left = -scrollLeft;
					grid.headerContainer.find('>.ui-grid-unlocked>table').css('left', left + 'px');
					grid.bodyContainer.find('>.ui-grid-unlocked>table').css('left', left + 'px');
					if(grid.summaryContainer){
						grid.summaryContainer.find('>.ui-grid-unlocked>table').css('left', left + 'px');
					}
				},
				//y轴滚动
				yscroll: function(scrollTop, grid){
					var top = -scrollTop;
					grid.bodyContainer.find('>.ui-grid-locked>table').css('top', top + 'px');
					grid.bodyContainer.find('>.ui-grid-unlocked>table').css('top', top + 'px');
				},
				insertRowAfter: function(afterRow, dataSheet, grid){
					var h = [], currentRowIndex = -1, currentRowId = null, gcl = grid.columns.length;
					//拆锁定区域sheet
					var lockedSheet = buildPartSheet(dataSheet, 0, grid.endLockIndex, this.lockFlag, grid.dataElMap);
					//渲染锁定区域表
					renderSheetBody(lockedSheet, h, grid);
					if(afterRow){
						for(var i = 0; i <= grid.endLockIndex; i++){
							var c = afterRow.cells[i].getPhysicalCell();
							if(c.rowIndex > currentRowIndex){
								currentRowId = c.rowId;
								currentRowIndex= c.rowIndex;
							}
						}
						$(h.join('')).insertAfter($('#' + currentRowId + this.lockFlag));
					}else{
						grid.bodyContainer.find('>.ui-grid-locked>table>tbody').prepend(h.join(''));
					}
					h = [];
					//拆未锁定区域sheet
					var unlockedSheet = buildPartSheet(dataSheet, grid.endLockIndex + 1, gcl - 1, this.unlockFlag, grid.dataElMap);
					//渲染未锁定区域表
					renderSheetBody(unlockedSheet, h, grid);
					currentRowIndex = -1;
					if(afterRow){
						for(var i = grid.endLockIndex + 1; i < gcl; i++){
							var c = afterRow.cells[i].getPhysicalCell();
							if(c.rowIndex > currentRowIndex){
								currentRowId = c.rowId;
								currentRowIndex= c.rowIndex;
							}
						}
						$(h.join('')).insertAfter($('#' + currentRowId + this.unlockFlag));
					}else{
						grid.bodyContainer.find('>.ui-grid-unlocked>table>tbody').prepend(h.join(''));
					}
				},
				insertBlankRowAfter: function(afterRow, rowId, title, grid){
					var h = [], currentRowIndex = -1, currentRowId = null, gcl = grid.columns.length;
					//渲染锁定区域表
					renderSheetBody([{id: rowId, cls: 'ui-grid-blank', domId: rowId + this.lockFlag, cells: [$.extend({}, grid.clazz.cellDefaults, {
						rowId: rowId,
						title: title,
						cls: grid.clazz.alignCls['left'],
						valign: 'middle',
						colSpan: grid.endLockIndex + 1,
						tdCls: 'ui-grid-td-more ui-grid-td-blank',
						id: grid.id + '_cell' + grid.counter++
					})]}], h, grid);
					if(afterRow){
						for(var i = 0; i <= grid.endLockIndex; i++){
							var c = afterRow.cells[i].getPhysicalCell();
							if(c.rowIndex > currentRowIndex){
								currentRowId = c.rowId;
								currentRowIndex= c.rowIndex;
							}
						}
						$(h.join('')).insertAfter($('#' + currentRowId + this.lockFlag));
					}else{
						grid.bodyContainer.find('>.ui-grid-locked>table>tbody').prepend(h.join(''));
					}
					h = [];
					//渲染未锁定区域表
					renderSheetBody([{id: rowId, cls: 'ui-grid-blank', domId: rowId + this.unlockFlag, cells: [$.extend({}, grid.clazz.cellDefaults, {
						rowId: rowId,
						title: "",
						cls: grid.clazz.alignCls['left'],
						tdCls: 'ui-grid-td-more',
						valign: 'middle',
						colSpan: gcl - grid.endLockIndex - 1,
						id: grid.id + '_cell' + grid.counter++
					})]}], h, grid);
					currentRowIndex = -1;
					if(afterRow){
						for(var i = grid.endLockIndex + 1; i < gcl; i++){
							var c = afterRow.cells[i].getPhysicalCell();
							if(c.rowIndex > currentRowIndex){
								currentRowId = c.rowId;
								currentRowIndex= c.rowIndex;
							}
						}
						$(h.join('')).insertAfter($('#' + currentRowId + this.unlockFlag));
					}else{
						grid.bodyContainer.find('>.ui-grid-unlocked>table>tbody').prepend(h.join(''));
					}
				},
				removeRow: function(rowId, grid){
					$('#' + rowId + this.lockFlag).remove();
					$('#' + rowId + this.unlockFlag).remove();
					var row = grid.dataElMap[rowId];
					if(row){
						for(var i = 0, len = grid.dataRows.length; i < len; i++){
							if(grid.dataRows[i] == row){
								grid.dataRows.splice(i, 1);
								break;
							}
						}
					}
				},
				//设置行高
				_fixRowHeight: function(height, allCells, grid){
					var rowHeightMap = {}, rowGroupMap = {};
					//初始化行高集合和行组集合
					for(var i = 0; i < allCells.length; i++){
						var cell = allCells[i];
						if(cell.spanRows){
							rowGroupMap[cell.id] = {height: 0, totalHeight: cell.height, rowId: cell.rowId, rows: cell.spanRows};
						}else{
							rowHeightMap[cell.rowId] = Math.max(rowHeightMap[cell.rowId] || 0, cell.height);
						}
					}
					//分配行组的每行高度
					this._distributeRowHeight(rowGroupMap, rowHeightMap, grid);
					//设置每行高度
					var thiz = this;
					//表头标题区单元格高度设置
					for(var i = 0; i < grid.headerSheet.length; i++){
						thiz._setRowHeight(rowHeightMap, grid.headerSheet[i], grid.headerElMap, grid);
					}
//					grid.headerContainer.find('.ui-grid-header-row').each(function(){
//					});
					for(var i = 0; i < grid.dataRows.length; i++){
						var row = grid.dataRows[i];
						if(!row.hide && !row.notAvailable){
							thiz._setRowHeight(rowHeightMap, row, grid.dataElMap, grid);
						}
					}
//					//表头汇总区单元格高度设置
//					grid.headerContainer.find('.ui-grid-data-row').each(function(){
//						thiz._setRowHeight($(this), rowHeightMap, grid.dataElMap[this.id], grid);
//					});
//					//数据体单元格高度设置
//					grid.bodyContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row').each(function(){
//						thiz._setRowHeight($(this), rowHeightMap, grid.dataElMap[this.id], grid);
//					});
//					if(grid.summaryContainer){
//						//汇总区单元格高度设置
//						grid.summaryContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row').each(function(){
//							thiz._setRowHeight($(this), rowHeightMap, grid.dataElMap[this.id], grid);
//						});
//					}
					//设置header区高度
					var h = UI.getOuterHeight(grid.headerContainer.find('>.ui-grid-locked')), contentViewHeight = height;
					UI.setHeight(grid.headerContainer, h);
					UI.setHeight(grid.headerYScrollEmpty, h);
					contentViewHeight -= h;
					//设置内容区高度
					var $bodyLocked = grid.bodyContainer.find('>.ui-grid-locked');
					if($bodyLocked.length > 0){
						UI.setHeight($bodyLocked, -1);
						h = UI.getOuterHeight($bodyLocked);
						UI.setHeight(grid.bodyContainer, h);
						grid.yScrollbar.setContentHeight(h);
					}else{
						grid.yScrollbar.setContentHeight(0);
					}
					//设置汇总区高度
					if(grid.summaryContainer){
						h = UI.getOuterHeight(grid.summaryContainer.find('>.ui-grid-locked'));
						UI.setHeight(grid.summaryContainer, h);
						UI.setHeight(grid.summaryYScrollEmpty, h);
						contentViewHeight -= h;
					}
//					if(grid.pagingContainer){
//						contentViewHeight -= UI.getOuterHeight(grid.pagingContainer);
//					}
					//计算滚动条高度
					h = UI.getOuterHeight(grid.xScrollContainer);
					UI.setHeight(grid.xyScrollEmpty, h);
					contentViewHeight -= h;
					//计算分页高度
					if(grid.pagingContainer){
						contentViewHeight -= UI.getOuterHeight(grid.pagingContainer);
					}
					UI.setHeight(grid.bodyContainer, contentViewHeight);
					if($bodyLocked.length > 0){
						grid.bodyContainer.find('>.ui-grid-locked,>.ui-grid-unlocked').each(function(){
							UI.setHeight($(this), contentViewHeight);
						});
					}else{
						UI.setHeight(grid.bodyContainer.find('>.no-data-comment'), contentViewHeight);
					}
					grid.yScrollbar.setHeight(contentViewHeight);
					if(grid.yScrollbar._scrollTop){
						grid.yScrollbar.scrollTop(grid.yScrollbar._scrollTop);
					}
					grid.yScrollbar.triggerScroll();
				},
				//设置行高
				_setRowHeight: function(rowHeightMap, row, elMap, grid){
					if(!row.domRows){
						row.domRows = [];
						if(row.domIds){
							for(var i = 0; i < row.domIds.length; i++){
								var domId = row.domIds[i], domRow = elMap[domId];
								domRow.element = $('#' + domId);
								row.domRows.push(domRow);
							}
						}else{
							row.element = $('#' + row.id);
							if(row.element.length == 1){
								row.domRows.push(row);
							}
						}
					}
					for(var i = 0; i < row.domRows.length; i++){
						var domRow = row.domRows[i];
						var h = rowHeightMap[domRow.id] + grid._cellTotalPaddingY;
						if(domRow.mergeRows){
							for(var j = 0; j < domRow.mergeRows.length; j++){
								h += rowHeightMap[domRow.mergeRows[j]] ? (rowHeightMap[domRow.mergeRows[j]] + grid._cellTotalPaddingY) : 0;
							}
						}
						if(domRow.height != h || row.heightReseted){
							UI.setHeight(domRow.element, h);
							domRow.height = h;
							row.heightReseted = false;
						}
					}
				},
				//分配行高
				_distributeRowHeight: function(rowGroupMap, rowHeightMap, grid){
					//重置各行组行高
					this._resetRowGroupHeight(rowGroupMap, rowHeightMap, grid);
					var done = true;
					for(var k in rowGroupMap){
						//将多余行高分配给单元格所在行
						rowHeightMap[rowGroupMap[k].rowId] += rowGroupMap[k].totalHeight - rowGroupMap[k]._cth;
						delete rowGroupMap[k];
						done = false;
						break;
					}
					if(!done){
						//重新分配行高
						this._distributeRowHeight(rowGroupMap, rowHeightMap, grid);
					}
				},
				//重置行组合高度
				_resetRowGroupHeight: function(rowGroupMap, rowHeightMap, grid){
					for(var k in rowGroupMap){
						var rowGroup = rowGroupMap[k], height = rowGroup.hegith = Math.max(rowGroup.height, rowHeightMap[rowGroup.rowId]);
						for(var i = 0; i < rowGroup.rows.length; i++){
							height += rowHeightMap[rowGroup.rows[i]] + grid._cellTotalPaddingY;
						}
						//当单元格高度不大于总行高，处理完成
						if(height >= rowGroup.totalHeight){
							delete rowGroupMap[k];
						}else{
							rowGroup._cth = height; //行组当前总行高
						}
					}
				},
				//设置列宽
				_fixColWidth: function(gridWidth, allCells, grid){
					//初始化表格最小宽度和最小固定宽度
					if(!grid._minWidth){
						grid._fixedWidth = 0; //表格固定列总宽度
						grid._unfixedWidth = 0; //表格非固定列总宽度
						grid._fixedWidthCols = 0; //表格固定列列数
						grid._unfixedWidthCols = 0; //表格非固定列列数
						grid._cellTotalPaddingX = grid.cellPaddingX * 2; //单元格X总padding
						grid._cellTotalPaddingY = grid.cellPaddingY * 2; //单元格Y总padding
						for(var i = 0; i < grid.columns.length; i++){
							var col = grid.columns[i];
							if(col.fixedWidth){
								grid._fixedWidth += col.width;
								grid._fixedWidthCols++;
							}else{
								grid._unfixedWidth += col.width;
								grid._unfixedWidthCols++;
							}
						}
						grid._fixedWidthPadding = grid._fixedWidthCols * grid._cellTotalPaddingX; //固定宽度列占用总padding
						grid._unfixedWidthPadding = grid._unfixedWidthCols * grid._cellTotalPaddingX; //非固定宽度列占用总padding
						grid._minWidth = grid._fixedWidth + grid._unfixedWidth + grid._fixedWidthPadding + grid._unfixedWidthPadding;
						grid._actualFixedWidth = grid._fixedWidth + grid._fixedWidthPadding; //表格固定列实际总宽度
					}
					var unfixedActualWidth, contentWidth;
					if(gridWidth >= grid._minWidth){
						//无需锁定列
						unfixedActualWidth = gridWidth - grid._actualFixedWidth;
						contentWidth = gridWidth;
					}else{
						//需锁定列
						unfixedActualWidth = grid._minWidth - grid._actualFixedWidth;
						contentWidth = grid._minWidth;
					}
					var lockedWidth = 0, unfixedWidthRate = unfixedActualWidth / grid._unfixedWidth, leftWidth = 0, newWidth = 0;
					//锁定列
					for(var i = 0; i <= grid.endLockIndex; i++){
						var col = grid.columns[i];
						if(col.fixedWidth){
							lockedWidth += col.width + grid._cellTotalPaddingX;
						}else{
							newWidth = unfixedWidthRate * col.width + leftWidth;
							col._actualWidth = parseInt(newWidth);
							leftWidth = newWidth - col._actualWidth;
							lockedWidth += col._actualWidth;
						}
					}
					var uncompleteMap = {};
					//设置锁定区列宽
					this._setLockedAreaWidth(lockedWidth, grid);
					for(var i = 0; i <= grid.endLockIndex; i++){
						var col = grid.columns[i], widthByPercent, w;
						if(col.fixedWidth){
							w = col.width;
							widthByPercent = 100 * (w + grid._cellTotalPaddingX) / lockedWidth + '%';
						}else{
							w = col._actualWidth - grid._cellTotalPaddingX;
							widthByPercent = 100 * col._actualWidth / lockedWidth + '%';
						}
						this._setColWidth(i, widthByPercent, w, uncompleteMap, allCells, grid);
					}
					UI.setWidth(grid.xScrollContainer.find('.ui-grid-x-scroll-empty'), lockedWidth);
					//设置内容区非固定列宽度
					var unlockedWidth = contentWidth - lockedWidth - 0.5, unlockedViewWidth = gridWidth - lockedWidth - 0.5;
					this._setUnlockedAreaWidth(unlockedWidth, unlockedViewWidth, grid);
					uncompleteMap = {};
					for(var i = grid.endLockIndex + 1; i < grid.columns.length; i++){
						var col = grid.columns[i], widthByPercent, w;
						if(col.fixedWidth){
							w = col.width;
							widthByPercent = 100 * (w + grid._cellTotalPaddingX) / unlockedWidth + '%';
						}else{
							newWidth = unfixedWidthRate * col.width + leftWidth;
							col._actualWidth = parseInt(newWidth);
							leftWidth = newWidth - col._actualWidth;
							w = col._actualWidth - grid._cellTotalPaddingX;
							widthByPercent = 100 * col._actualWidth / unlockedWidth + '%';
						}
						this._setColWidth(i, widthByPercent, w, uncompleteMap, allCells, grid);
					}
					grid.xScrollbar.setWidth(unlockedViewWidth).setContentWidth(unlockedWidth).triggerScroll();
					var barHeight = UI.getOuterHeight(grid.xScrollbar.element);
					if(barHeight < 2){
						grid.xScrollContainer.height(0);
					}else{
						UI.setHeight(grid.xScrollContainer, barHeight);
						UI.setHeight(grid.xScrollContainer.find('.ui-grid-x-scroll-empty'), barHeight);
					}
				},
				_setUnlockedAreaWidth: function(contentWidth, viewWidth, grid){
					UI.setWidth(grid.headerContainer.find('.ui-grid-unlocked>table'), contentWidth);
					UI.setWidth(grid.headerContainer.find('.ui-grid-unlocked'), viewWidth);
					UI.setWidth(grid.bodyContainer.find('>.ui-grid-unlocked>table'), contentWidth);
					UI.setWidth(grid.bodyContainer.find('>.ui-grid-unlocked'), viewWidth);
					if(grid.summaryContainer){
						UI.setWidth(grid.summaryContainer.find('.ui-grid-unlocked>table'), contentWidth);
						UI.setWidth(grid.summaryContainer.find('.ui-grid-unlocked'), viewWidth);
					}
				},
				_setLockedAreaWidth: function(width, grid){
					UI.setWidth(grid.headerContainer.find('.ui-grid-locked>table'), width);
					UI.setWidth(grid.bodyContainer.find('>.ui-grid-locked>table'), width);
					if(grid.summaryContainer){
						UI.setWidth(grid.summaryContainer.find('>.ui-grid-locked>table'), width);
					}
				},
				_setColWidth: function(colIndex, widthByPercent, width, uncompleteMap, allCells, grid){
					var thiz = this;
					//处理未完成单元格宽度设置
					for(var k in uncompleteMap){
						var c = uncompleteMap[k];
						c.width += width + grid._cellTotalPaddingX;
						if(--c.leftCols == 0){
							//单元格宽度处理完成
							delete uncompleteMap[k];
							if(c.cell.width != c.width){
								c.cell.width = c.width;
								UI.setWidth(c.cell.cellElement, c.width);
	//							UI.setHeight($cell, -1);
								c.cell.height = UI.getOuterHeight(c.cell.cellElement);
							}
							allCells.push(c.cell);
						}
					}
					if(!grid.headerCols){
						grid.headerCols = [];
						grid.headerContainer.find('col').each(function(){
							grid.headerCols.push($(this));
						});
					}
					grid.headerCols[colIndex].css('width', widthByPercent);
					//表头标题区单元格宽度设置
//					grid.headerContainer.find('.ui-grid-header-row .ui-grid-col' + colIndex).each(function(){
//						thiz._setCellWidth($(this), width, uncompleteMap, grid.headerElMap[this.id], allCells, grid);
//					});
					var cs = grid.headerCells[colIndex];
					for(var i = 0; i < cs.length; i++){
						var cell = cs[i];
						thiz._setCellWidth(width, uncompleteMap, cell, allCells, grid);
					}
					for(var i = 0; i < grid.dataRows.length; i++){
						var row = grid.dataRows[i];
						if(row.hide || row.notAvailable){
							continue;
						}
						var cell = row.cells[colIndex];
						if(!cell.mergedBy){
							thiz._setCellWidth(width, uncompleteMap, cell, allCells, grid);
						}
					}
//					//表头汇总区单元格宽度设置
//					grid.headerContainer.find('.ui-grid-data-row .ui-grid-col' + colIndex).each(function(){
//					});
					//数据体宽度设置
					if(!grid.dataCols){
						grid.dataCols = [];
						grid.bodyContainer.find('>div>.ui-grid-table>colgroup>col').each(function(){
							grid.dataCols.push($(this));
						});
					}
					if(grid.dataCols.length > 0){
						grid.dataCols[colIndex].css('width', widthByPercent);
					}
//					//数据体单元格宽度设置
//					grid.bodyContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row>.ui-grid-col' + colIndex + ':visible').each(function(){
//						thiz._setCellWidth($(this), width, uncompleteMap, grid.dataElMap[this.id], allCells, grid);
//					});
					if(grid.summaryContainer){
						if(!grid.summaryCols){
							grid.summaryCols = [];
							grid.summaryContainer.find('col').each(function(){
								grid.summaryCols.push($(this));
							});
						}
						if(grid.summaryCols.length > 0){
							grid.summaryCols[colIndex].css('width', widthByPercent);
						}
//						//汇总区单元格宽度设置
//						grid.summaryContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row>.ui-grid-col' + colIndex).each(function(){
//							thiz._setCellWidth($(this), width, uncompleteMap, grid.dataElMap[this.id], allCells, grid);
//						});
					}
				},
				_setCellWidth: function(width, uncompleteMap, cell, allCells, grid){
					if(!cell.cellElement){
						cell.cellElement = $('#' + cell.id).find('>.ui-grid-cell');
					}
					//单元格有合并列
					if(cell.spanCols){
						uncompleteMap[cell.id] = {width: width, id: cell.id, leftCols: cell.spanCols.length, cell: cell};
					}else{
						if(cell.width != width){
							cell.width = width;
							UI.setWidth(cell.cellElement, width);
//							UI.setHeight($cell, -1);
							cell.height = UI.getOuterHeight(cell.cellElement);
						}
						allCells.push(cell);
					}
				}
			},
			colUnlock: {
				
			}
		};
	UI.Grid = UI.extend(UI.DomBase, {
		key: 'ui.grid',
		keyCls: 'ui-grid-key',
		cls: 'ui-grid',
		counter: 1,
		idPrefix: 'ui_grid', //grid ID前缀
		configEvents: ['beforerender', 'afterrender'],
		DATA_CELL_CLS: 'ui-grid-data-cell', //数据单元格样式
		HEADER_CELL_CLS: 'ui-grid-header-cell', //表头单元格样式
		DATA_ROW_CLS: 'ui-grid-data-row', //数据行样式
		HEADER_ROW_CLS: 'ui-grid-header-row', //表头行样式
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom).addClass(this.cls).addClass(this.keyCls), p = this.newInstance($d, opts);
			p.__hided = $d.hasClass(UI.hideCls)
			if(!p.__hided){
				$d.addClass(UI.hideCls);
			}
			//初始化容器为panel
			p.panel = UI.Panel.init(dom, {
				fit: p.fit,
				resize: function(){
					p.trigger('resize');
				}
			});
			p.columns = [];
			p.endLockIndex = -1;
			p.hasSummary = false; //表格是否有汇总
			//生成grid ID
			p.id = this.idPrefix + this.counter++;
			//初始化表格的计数器，用以生成row和cell的ID
			p.counter = 1; 
			//初始化表格row和cell元素对象的ID映射表
			p.headerElMap = {};
			p.headerCells = [];
			//构建表头
			p.headerSheet = buildHeader(p.headers, p.columns, p);
			//初始化列属性映射表
			p.propertyColIndexMap = {};
			for(var i = 0; i < p.columns.length; i++){
				var col = p.columns[i];
				if(col.property){
					p.propertyColIndexMap[col.property] = i;
				}
			}
			//初始化表格渲染处理器
			p.renderHandler = (p.endLockIndex > -1 && p.endLockIndex != p.columns.length - 1) ? 
					renderHandlers.colLock : renderHandlers.colUnlock;
			p.yScrollContainer = $('<div class="ui-grid-y-scroll"></div>').appendTo($d);
			//渲染表头
			p.headerContainer = $('<div class="ui-grid-header"></div>').appendTo($d);
			p.headerElement = $(p.renderHandler.buildTableHtml(p.headerSheet, p.headerElMap, p).join('')).appendTo(p.headerContainer);
			p.headerYScrollEmpty = $('<div class="ui-grid-y-scroll-empty"></div>').appendTo(p.yScrollContainer);
			//body
			p.bodyContainer = $('<div class="ui-grid-body"></div>').appendTo($d);
			p.yScrollbar = UI.YScrollbar.init($('<div class="ui-grid-y-scroll-bar ui-grid-y-scroll-' + p.yOverflow + 
					'"></div>').appendTo(p.yScrollContainer)[0], {
				overflow: p.yOverflow,
				scroll: function(scrollTop) {
					p.renderHandler.yscroll(scrollTop, p);
				}
			});
			//汇总
			if(p.hasSummary && p.sumRender == 'footer'){
				p.summaryContainer = $('<div class="ui-grid-summary"></div>').appendTo($d);
				p.summaryYScrollEmpty = $('<div class="ui-grid-y-scroll-empty"></div>').appendTo(p.yScrollContainer);
			}
			p.xScrollContainer = $('<div class="ui-grid-x-scroll"></div>').appendTo($d);
			p.renderHandler.renderXScroll(p);
			p.xyScrollEmpty = $('<div class="ui-grid-x-scroll-empty ui-grid-y-scroll-empty"></div>').appendTo(p.yScrollContainer);
			//初始化分页控件
			if(p.paging){
				p.pagingContainer = $('<div class="ui-grid-paging"></div>').appendTo($d);
				p.paging = UI.Paging.init(p === true ? null : p.paging);
			}
			//初始化表格数据仓库
			if(!p.store){
				p.store = UI.Store.init(p);
			}
			p.store.bindGrid(p);
			this.afterInit(p);
			if(p.store.autoLoad){
				setTimeout(function(){
					p.store.load();
				}, 1);
			}
			return p;
		},
		//确保子类在数据加载前处理
		afterInit: function(instance){
			
		},
		alignCls: {
			left: 'text-left',
			right: 'text-right',
			center: 'text-center'
		},
		buildDataSheet: function(records, grid){
			var dataSheet = [], len = records.length;
			for(var i = 0; i < len; i++){
				var cls = grid.clazz.DATA_ROW_CLS;
				if(grid.dataRowLoopCls && grid.dataRowLoopCls.length > 0){
					cls += (' ' + grid.dataRowLoopCls[i % grid.dataRowLoopCls.length]);
				}
				var record = records[i], clen = grid.columns.length, 
					row = {cls: cls, cells: [], id: grid.id + '_data' + grid.counter++, record: record};
				this.bindDataRowEvents(row, grid);
				dataSheet.push(row);
				grid.dataElMap[row.id] = row;
				grid.dataRows.push(row);
				for(var j = 0; j < clen; j++){
					var col = grid.columns[j], cell = row.cells[j] = $.extend({}, grid.clazz.cellDefaults, {
						rowId: row.id,
						colIndex: j,
						rowIndex: i
					});
					if(col.rowMergeable && i > 0){
						//行可合并
						prerowCell = dataSheet[i - 1].cells[j];
						if(j > 0 && prerowCell.mergedBy && prerowCell.mergedBy == row.cells[j - 1].mergedBy){
							//单元格处于合并区内
							cell.mergedBy = prerowCell.mergedBy;
							continue;
						}else{
							if(col.valueProperty){
								cell.value = col.propertyProcessor(record, col.valueProperty);
							}else{
								cell.value = col.propertyProcessor(record, col.property);
							}
							if(prerowCell.getPhysicalCell().colIndex == j && prerowCell.getPhysicalCell().value == cell.value){
								//合并同一列
								cell.mergedBy = prerowCell.addRowSpan(1, row.id).getPhysicalCell();
								continue;
							}
						}
					}else{
						if(col.valueProperty){
							cell.value = col.propertyProcessor(record, col.valueProperty);
						}else{
							cell.value = col.propertyProcessor(record, col.property);
						}
					}
					cell.title = col.formatter((!col.valueProperty && cell.value != undefined) ? 
							cell.value : col.propertyProcessor(record, col.property), record, i, row.id);
					if(col.colMergeable && j != 0 && j != grid.endLockIndex + 1){
						//列可合并
						if(cell.title == grid.colMergeableText && (dataSheet[i].cells[j - 1].mergedBy == null 
								|| dataSheet[i].cells[j - 1].mergedBy.rowIndex == i)){
							cell.mergedBy = dataSheet[i].cells[j - 1].addColSpan(1, j).getPhysicalCell();
							continue;
						}
					}
					//未合并
					cell.id = grid.id + '_cell' + grid.counter++;
					cell.cls = grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[col.align];
					cell.tdCls = (col.tdCls ? col.tdCls + ' ' : '') + 'ui-grid-col' + j;
					if(col.dataTdClsGetter){
						var dataTdCls = col.dataTdClsGetter(record);
						if(dataTdCls){
							cell.tdCls += ' ' + dataTdCls;
						}
					}
					cell.valign = 'middle';
					grid.dataElMap[cell.id] = cell;
				}
			}
			return dataSheet;
		},
		//绑定数据行事件
		bindDataRowEvents: function(row, grid){
			if(grid.dataRowEvents){
				row.events = {};
				for(var k in grid.dataRowEvents){
					row.events[k] = 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
						+ grid.clazz.cls + ':first\')); grid.dataRowEvents[\'' + k + '\'].call(grid, \'' + row.id + '\')';
				}
			}
		},
		//构建汇总二维表
		buildSummarySheet: function(data, grid){
			var summarySheet = [], i = 0;
			if(data.pageSummary){
				summarySheet.push(this.buildSummaryRow(grid.pageSummaryTitle, data.pageSummary, i++, grid));
			}
			summarySheet.push(this.buildSummaryRow(grid.summaryTitle, data.summary, i++, grid));
			return summarySheet;
		},
		//构建汇总行
		buildSummaryRow: function(title, record, rowIndex, grid){
			var row = {id: grid.id + '_data' + grid.counter++, cls: grid.clazz.DATA_ROW_CLS + ' ui-grid-summary-row', fixedProperty: true, cells: [], record: record},
				firstCol = grid.columns[0],
				titleCell = $.extend({}, grid.clazz.cellDefaults, {
					title: title,
					fixedTitle: true,
					colIndex: 0,
					tdCls: (firstCol.tdCls ? firstCol.tdCls + ' ' : '') + 'ui-grid-col0',
					cls: grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[firstCol.align],
					rowId: row.id,
					valign: 'middle',
					id: grid.id + '_cell' + grid.counter++,
					rowIndex: rowIndex
				});
			this.bindDataRowEvents(row, grid);
			if(firstCol.dataTdClsGetter){
				var dataTdCls = firstCol.dataTdClsGetter(record);
				if(dataTdCls){
					titleCell.tdCls += ' ' + dataTdCls;
				}
			}
			grid.dataElMap[row.id] = row;
			row.cells.push(titleCell);
			grid.dataElMap[titleCell.id] = titleCell;
			grid.dataRows.push(row);
			var i = 1, maxSpanCol = grid.endLockIndex >= 0 ? grid.endLockIndex : 100000;
			//处理标题合并单元格
			for(; i <= maxSpanCol && !grid.columns[i].summary; i++){
				row.cells.push($.extend({}, grid.clazz.cellDefaults, {
					colIndex: i,
					mergedBy: titleCell.addColSpan(1, i),
					rowIndex: rowIndex
				}));
			}
			//处理非合并部分合计单元格
			for(; i < grid.columns.length; i++){
				var col = grid.columns[i], cell = row.cells[row.cells.length] = $.extend({}, grid.clazz.cellDefaults, {
					title: col.summary ? col.formatter(record[col.property], record, rowIndex, row.id) : '',
					rowId: row.id,
					tdCls: (col.tdCls ? col.tdCls + ' ' : '') + 'ui-grid-col' + i,
					cls: grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[col.align],
					colIndex: i,
					valign: 'middle',
					id: grid.id + '_cell' + grid.counter++,
					rowIndex: rowIndex
				});
				if(col.dataTdClsGetter){
					var dataTdCls = col.dataTdClsGetter(record);
					if(dataTdCls){
						cell.tdCls += ' ' + dataTdCls;
					}
				}
				grid.dataElMap[cell.id] = cell;
			}
			return row;
		},
		base: {
			beforeEvents: {
				resize: function(){
					this.sizeInited = true;
					if(!this.dataRows){
						return;
					}
					if(!this.__hided){
						this.element.removeClass(UI.hideCls);
					}
					var width = UI.getInnerWidth(this.element), height = this.panel.height;
					if(height > 0){
						height = UI.getInnerHeight(this.element);
//						UI.setHeight(this.yScrollContainer, height);
					}
					this.renderHandler.fixResize(width, height, this);
				}
			},
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度
			 * @param height [必选]控件新高度
			 */
			resize: function(width, height){
				this.panel.resize(width, height);
			},
			//根据记录ID获取记录
			getRecordById: function(rowId){
				var row = this.dataElMap[rowId];
				return row ? row.record : null;
			},
			//获取列
			getColByProperty: function(propertyName){
				return this.columns[this.propertyColIndexMap[propertyName]];
			},
			//重置数据缓存
			resetDataCache: function(){
				//初始化数据
				this.dataElMap = {};
				this.dataRows = [];
				this.dataCols = null;
				this.summaryCols = null;
			},
			//渲染数据
			renderData: function(data){
				this.trigger('beforerender', data);
				this.resetDataCache();
				this.bodyContainer.empty();
				if(this.hasSummary){
					this.renderHandler.emptySummary(this);
				}
				//渲染数据
				if(!data.records || data.records.length == 0){
					if(this.renderBodyWithNoData){
						this.bodyContainer.append(this.renderHandler.buildTableHtml([], this.dataElMap, this).join(''));
					}else{
						this.bodyContainer.append('<div class="no-data-comment">' + this.noDataMsg + '</div>');
					}
				}else{
					this.bodyContainer.append(this.renderHandler.buildTableHtml(this.clazz.buildDataSheet(data.records, this), this.dataElMap, this).join(''));
				}
				//渲染汇总
				if(data.summary){
					var summarySheet = this.clazz.buildSummarySheet(data, this);
					this.renderHandler.renderSummary(summarySheet, this);
				}
				this.trigger('afterrender');
				this.trigger('resize');
			},
			//移除行
			removeRow: function(rowId){
				this.renderHandler.removeRow(rowId, this);
			},
			//插入行到指定位置
			insertRowsAfter: function(afterRowId, records){
				this.renderHandler.insertRowAfter(this.dataElMap[afterRowId], this.clazz.buildDataSheet(records, this), this);
			},
			//插入空白行到指定位置
			insertBlankRowAfter: function(afterRowId, title){
				var rowId = this.id + '_row' + this.counter++;
				this.renderHandler.insertBlankRowAfter(this.dataElMap[afterRowId], rowId, title, this);
				return rowId;
			},
			//获取记录行ID
			getRowId: function(record){
				for(var i = 0, len = this.dataRows.length; i < len; i++){
					if(this.dataRows[i].record == record){
						return this.dataRows[i].id;
					}
				}
			},
			//刷新指定行
			refreshRow: function(rowId){
				refreshRow(rowId, this);
			},
			//刷新指定单元格内容
			refreshCell: function(rowId, propertyName){
				var curRow = this.dataElMap[rowId];
				for(var i = 0; i < this.dataRows.length; i++){
					//循环取行号
					var row = this.dataRows[i];
					if(row != curRow){
						continue;
					}
					for(var j = 0, len = row.cells.length; j < len; j++){
						var col = this.columns[j];
						if(col.property != propertyName){
							continue;
						}
						var cell = row.cells[j];
						if(!cell.cellElement){
							cell.cellElement = $('#' + cell.id).find('>.ui-grid-cell');
						}
						var record = row.record,
							title = col.formatter(row.fixedProperty ? record[col.property] : col.propertyProcessor(record, col.property), record, i, row.id);
						cell.cellElement.empty().html(title);
						//重置单元格尺寸
						cell.width = null;
						row.heightReseted = true;
					}
					break;
				}
				this.trigger('resize');
			},
			//按列刷新表格
			refreshCols: function(properties){
				if(this.store.refreshCols){
					//刷新汇总记录
					if(!properties){
						//未指定刷新列属性，刷新所有列
						this.store.refreshCols(this.columns);
					}else{
						var cols = [];
						for(var i = 0; i < properties.length; i++){
							cols.push(this.columns[this.propertyColIndexMap[properties[i]]]);
						}
						this.store.refreshCols(cols);
					}
				}
				if(properties){
					//刷新指定列
					for(var i = 0; i < properties.length; i++){
						refreshCol(this.propertyColIndexMap[properties[i]], this);
					}
				}else{
					//刷新所有列
					for(var i = 0; i < this.columns.length; i++){
						refreshCol(i, this);
					}
				}
				this.trigger('resize');
			},
			addRowClass: function(rowId, cls){
				this.renderHandler.addRowClass(rowId, cls);
			},
			removeRowClass: function(rowId, cls){
				this.renderHandler.removeRowClass(rowId, cls);
			}
		},
		//单元格基类
		cellDefaults: {
			rowIndex: 0,
			colIndex: 0,
			colSpan: 1,
			rowSpan: 1,
			mergedBy: null,
			spanCols: null, //被合并的单元格列索引集合
			spanRows: null, //被合并单元格行ID集合
			addColSpan: function(offset, mergedColIndex){
				var spanCell = null;
				if(this.mergedBy == null){
					this.colSpan += offset;
					spanCell = this;
				}else if(this.rowIndex == this.mergedBy.rowIndex){
					this.mergedBy.colSpan += offset;
					spanCell = this.mergedBy;
				}
				if(mergedColIndex != undefined && spanCell){
					if(spanCell.spanCols == null){
						spanCell.spanCols = [mergedColIndex];
					}else{
						spanCell.spanCols.push(mergedColIndex);
					}
				}
				return this;
			},
			addRowSpan: function(offset, mergedRowId){
				var spanCell = null;
				if(this.mergedBy == null){
					this.rowSpan += offset;
					spanCell = this;
				}else if(this.colIndex == this.mergedBy.colIndex){
					this.mergedBy.rowSpan += offset;
					spanCell = this.mergedBy;
				}
				if(mergedRowId != undefined && spanCell){
					if(spanCell.spanRows == null){
						spanCell.spanRows = [mergedRowId];
					}else{
						spanCell.spanRows.push(mergedRowId);
					}
				}
				return this;
			},
			getPhysicalCell: function(){
				return this.mergedBy || this;
			}
		},
		columnDefaults: {
			title: '', //列表头
			property: '', //列显示对应属性
			valueProperty: null, //列值对应属性(rowMergeable为true时有效)，默认为列显示对应属性
			endLock: false, //是否最后的锁定列(叶子列的最后一列为true列有效)
			formatter: function(value, record, index, rowId){return value ? value : (value === 0 ? '0' : '');}, //列显示文本格式化
			'export': true, //是否需要导出
			tdCls: '', //列td样式
			rowMergeable: false, //行是否可合并的。true-合并同一列相邻行valuePorperty相等的单元格。默认为false。
			colMergeable: false, //列是否可合并。true-同一行，合并显示文本为表格对象的colMergeableText值的单元格与同一行前一个单元格。默认为false
			align: 'left', //显示文本水平对齐方式
			nowrap: false, //强制不换行
			width: 20, //列宽(如果有列锁定且所有列的宽度总和大于总宽度时为列实际宽度，否则代表列宽占的比例)
			minWidth: 20, //最小列宽(固定宽度列或锁行时无效)
			fixedWidth: false, //列固定宽度,
			children: null, //子表头
			sortable: true, //列是否排序
			summary: false, //合计列。为true时，property属性不能为空
			propertyProcessor: function(record, propertyName){return record[propertyName];}, //非合计行属性值处理器
			dataTdClsGetter: null //function(record){return cls;} 单元格样式处理器
		},
		defaults: {
			headers: null, //列定义数组
			paging: false, //是否分页
			store: null, //数据仓库实例
			autoLoad: true, //自动加载数据
			sortable: false, //表格是否排序总开关
			fit: false, //是否自适应高度
			summary: false, //汇总总开关
			sumRender: 'footer', //合计行渲染位置。header-表头，footer-表尾
			summaryTitle: '总合计', //合计标题
			pageSummaryTitle: '本页合计', //本页合计标题
			showPageSummary: true, //是否显示分页汇总
			dataRowLoopCls: ['ui-grid-data-row-even', 'ui-grid-data-odd'], //数据行循环样式
			colMergeableText: '<merge/>', //列可合并特殊显示文本
			renderBodyWithNoData: false, //无数据时是否渲染表体
			noDataMsg: '无数据', //没有数据显示文本
			cellPaddingX: 1.5, //单元格X轴方向间距
			cellPaddingY: 1.5, //单元格Y轴方向间距
			xOverflow: 'auto', //X轴滚动。scroll, auto
			yOverflow: 'scroll', //Y轴滚动。scroll, auto
			dataRowEvents: {}, //数据行事件。支持：onclick，ondblclick等。支持参数rowId
			drill: function(record){} //下钻
		}
	});
})(jQuery);;(function($){
	var buildPartSheetByRow = function(sheet, startRow, endRow){
//		var newSheet = [];
//		for(var i = startRow; i < endRow; i++){
//			newSheet.push(sheet[i]);
//		}
		return sheet.slice(startRow, endRow);
	};
	UI.TreeGrid = UI.extend(UI.Grid, {
		cls: 'ui-tree-grid',
		idPrefix: 'ui_tree_grid',
		COLLAPASE_ITEM_CLS: 'ui-tree-grid-collapse-item',
		afterInit: function(grid){
			grid.titleColIndex = -1;
			for(var i = 0; i < grid.columns.length; i++){
				if(grid.columns[i].titleCol){
					grid.titleColIndex = i;
				}
			}
			if(grid.titleColIndex <0){
				grid.titleColIndex = 0;
			}
			var titleCol = grid.columns[0], formatter = titleCol.formatter, thiz = this;
			titleCol.formatter = function(value, record, index, rowId){
				var title = formatter.apply(this, arguments), row = grid.dataElMap[rowId];
				return thiz.buildTitleColumnTitle(title, row.level, row, grid);
			};
			if(grid.nodeLoadingImg){
				UI.preloadImg(grid.nodeLoadingImg);
			}
		},
		buildTitleColumnTitle: function(title, level, row, grid){
			if(!level){
				return title;
			}
			var children = row.record[grid.childrenProperty], statusFlag = 'collapse';
			if((row.record[grid.expandProperty] || !grid.asynLoadChildren) && (!children || children.length == 0)){
				statusFlag = 'leaf';
			}else if(row.record[grid.expandProperty]){
				statusFlag = 'expand';
				row.expand = true;
			}
			return this.buildTreeNodeTitle(title, level, statusFlag, row.id, grid);
		},
		buildTreeNodeTitle: function(title, level, statusFlag, rowId, grid){
			var h = ['<div class="ui-tree-grid-node ui-tree-grid-node-', statusFlag, '" nodestatus="', statusFlag, '"'];
			if(level > 1){
				h.push(' style="padding-left: ', (level - 1) * grid.indent, 'px;"');
			}
			h.push('>');
			h.push('<span class="ui-tree-grid-node-icon" onmouseover="var $el=$(this).parent();$el.addClass(\'ui-tree-grid-node-\' + $el.attr(\'nodestatus\') + \'-hover\');"',
					' onmouseout="var $el=$(this).parent();$el.removeClass(\'ui-tree-grid-node-\' + $el.attr(\'nodestatus\') + \'-hover\');""');
			if(statusFlag != 'leaf'){
				h.push(' onclick="var $el = $(this).parent(), grid = UI.TreeGrid.getInstance($el.parents(\'.', this.cls, 
						':first\'));if($el.attr(\'nodestatus\') == \'collapse\'){grid.expand(\'', rowId, 
						'\');}else{grid.collapse(\'', rowId, '\');}event.cancelBubble = true;event.stopPropagation();"');
			}
			h.push('></span>');
			h.push(title, '</div>');
			return h.join('');
		},
		buildDataSheet: function(records, grid, parentRow, level, dataSheet, collapse){
			dataSheet = dataSheet || [];
			level = level || 1;
			for(var i = 0; i < records.length; i++){
				var cls = grid.clazz.DATA_ROW_CLS + ' ' + grid.levelClsPrefix + level, rowHide = false;
				if(collapse){
					cls += (' ' + grid.clazz.COLLAPASE_ITEM_CLS);
					rowHide = true;
				}
				var record = records[i], clen = grid.columns.length, 
					row = {cls: cls, hide: rowHide, cells: [], id: grid.id + '_data' + grid.counter++, record: record, level: level}, 
					children = record[grid.childrenProperty], rowIndex = dataSheet.length;
				this.bindDataRowEvents(row, grid);
				dataSheet.push(row);
				if(parentRow){
					if(!parentRow.children){
						parentRow.children = [];
					}
					parentRow.children.push(row.id);
				}
				grid.dataElMap[row.id] = row;
				grid.dataRows.push(row);
				//树title列处理
				if((record[grid.expandProperty] || !grid.asynLoadChildren) && (!children || children.length == 0)){
					row.statusFlag = 'leaf';
				}else if(record[grid.expandProperty]){
					row.statusFlag = 'expand';
					row.expand = true;
				}
				for(var j = 0; j < clen; j++){
					var col = grid.columns[j], cell = row.cells[j] = $.extend({}, grid.clazz.cellDefaults, {
						rowId: row.id,
						colIndex: j,
						rowIndex: rowIndex
					});
					if(col.rowMergeable && ((record[grid.expandProperty] || !grid.asynLoadChildren) && (!children || children.length == 0))){
						//非叶子节点记录不可合并
						if(i > 0){
							//行可合并
							prerowCell = dataSheet[rowIndex - 1].cells[j];
							if(j > 0 && prerowCell.mergedBy && prerowCell.mergedBy == row.cells[j - 1].mergedBy){
								//单元格处于合并区内
								cell.mergedBy = prerowCell.mergedBy;
								continue;
							}else{
								if(col.valueProperty){
									cell.value = col.propertyProcessor(record, col.valueProperty);
								}else{
									cell.value = col.propertyProcessor(record, col.property);
								}
								if(prerowCell.getPhysicalCell().colIndex == j && prerowCell.getPhysicalCell().value == cell.value){
									//合并同一列
									cell.mergedBy = prerowCell.addRowSpan(1, row.id).getPhysicalCell();
									continue;
								}
							}
						}else{
							if(col.valueProperty){
								cell.value = col.propertyProcessor(record, col.valueProperty);
							}else{
								cell.value = col.propertyProcessor(record, col.property);
							}
						}
					}
					cell.title = col.formatter((!col.valueProperty && cell.value != undefined) ? 
							cell.value : col.propertyProcessor(record, col.property), record, i, row.id);
					if(col.colMergeable && j != 0 && j != grid.endLockIndex + 1){
						//列可合并
						if(cell.title == grid.colMergeableText && (dataSheet[rowIndex].cells[j - 1].mergedBy == null 
								|| dataSheet[rowIndex].cells[j - 1].mergedBy.rowIndex == rowIndex)){
							cell.mergedBy = dataSheet[rowIndex].cells[j - 1].addColSpan(1, j).getPhysicalCell();
							continue;
						}
					}
					//未合并
					cell.id = grid.id + '_cell' + grid.counter++;
					cell.cls = grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[col.align];
					cell.tdCls = (col.tdCls ? col.tdCls + ' ' : '') + 'ui-grid-col' + j;
					if(col.dataTdClsGetter){
						var dataTdCls = col.dataTdClsGetter(record);
						if(dataTdCls){
							cell.tdCls += ' ' + dataTdCls;
						}
					}
					cell.valign = 'middle';
					grid.dataElMap[cell.id] = cell;
					if(j == grid.titleColIndex){
						row.nodeCellId = cell.id;
					}
				}
				if(children && children.length > 0){
					var startRow = dataSheet.length;
					this.buildDataSheet(children, grid, row, level + 1, dataSheet, collapse || !record[grid.expandProperty]);
					row.childrenSheet = buildPartSheetByRow(dataSheet, startRow, dataSheet.length);
				}
			}
			return dataSheet;
		},
		//绑定数据行事件
		bindDataRowEvents: function(row, grid){
			if(grid.dataRowEvents){
				row.events = {};
				for(var k in grid.dataRowEvents){
					row.events[k] = 'var grid = UI.TreeGrid.getInstance($(this).parents(\'.' 
						+ grid.clazz.cls + ':first\')); grid.dataRowEvents[\'' + k + '\'].call(grid, \'' + row.id + '\')';
				}
			}
		},
		base: {
			collapse: function(rowId, childrenCacadeUpdate){
				var row = this.dataElMap[rowId];
				if((!childrenCacadeUpdate && !row.expand) || (childrenCacadeUpdate && !row.expand)){
					//当操作触发节点的状态是已折叠的或子节点级联折叠，但是子节点是未展开的，不处理
					return;
				}
				if(!childrenCacadeUpdate){
					this.beforecollapse(row);
					row.expand = false;
					//设置操作触发节点状态
					$('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-expand')
						.removeClass('ui-tree-grid-node-expand-hover')
						.addClass('ui-tree-grid-node-collapse').attr('nodestatus', 'collapse');
					row.record[this.expandProperty] = false;
				}
				for(var i = 0, l = (row.childrenEndIndex ? row.childrenEndIndex : row.children.length); i < l; i++){
					//折叠子节点
					var childId = row.children[i];
					this.dataElMap[childId].hide = true;
					this.renderHandler.addRowClass(childId, this.clazz.COLLAPASE_ITEM_CLS);
					this.collapse(childId, true);
				}
				if(row.moreRowId){
					this.renderHandler.addRowClass(row.moreRowId, this.clazz.COLLAPASE_ITEM_CLS);
				}
				if(!childrenCacadeUpdate){
					this.trigger('resize');
					this.aftercollapse(row);
				}
			},
			expand: function(rowId, childrenCacadeUpdate){
				var row = this.dataElMap[rowId];
				if((!childrenCacadeUpdate && row.expand) || (childrenCacadeUpdate && !row.expand)){
					//当操作触发节点的状态是已展开的或子节点级联展开，但是子节点是未展开的，不处理
					return;
				}
				if(!childrenCacadeUpdate){
					this.beforeexpand(row);
					//设置操作触发节点状态
					row.expand = true;
					var $treeNode = $('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-collapse')
						.removeClass('ui-tree-grid-node-collapse-hover')
						.addClass('ui-tree-grid-node-expand').attr('nodestatus', 'expand');
					row.record[this.expandProperty] = true;
					if((!row.children || row.children.length == 0) && this.asynLoadChildren){
						//异步加载子节点
						if(this.nodeLoadingImg){
							$treeNode.addClass('ui-tree-grid-node-loading');
							if(!row.$loading){
								row.$loading = $('<img class="loading-img" src="' + this.nodeLoadingImg + '"/>').appendTo($treeNode.find('.ui-tree-grid-node-icon'));
							}
						}
						var children = this.loadChildren(row.record, rowId);
						if(children){
							this.appendChildren(rowId, children);
						}else if(children !== false && this.nodeLoadingImg){
							$treeNode.removeClass('ui-tree-grid-node-loading');
						}
						return;
					}
				}
				for(var i = 0, l = (row.childrenEndIndex ? row.childrenEndIndex : row.children.length); i < l; i++){
					//展开子节点
					var childId = row.children[i];
					this.dataElMap[childId].hide = false;
					this.renderHandler.removeRowClass(childId, this.clazz.COLLAPASE_ITEM_CLS);
					this.expand(childId, true);
				}
				if(row.moreRowId){
					this.renderHandler.removeRowClass(row.moreRowId, this.clazz.COLLAPASE_ITEM_CLS);
				}
				if(!childrenCacadeUpdate){
					this.trigger('resize');
					this.afterexpand(row);
				}
			},
			appendChildren: function(rowId, children){
				var row = this.dataElMap[rowId], 
					dataSheet = this.clazz.buildDataSheet(children, this, row, row.level + 1, dataSheet, false);
				if(dataSheet.length == 0){
					if(this.nodeLoadingImg){
						$('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-loading');
					}
					return;
				}
				row.childrenSheet = dataSheet;
				if(this.maxAppendChildren > 0 && dataSheet.length > this.maxAppendChildren){
					row.childrenEndIndex = this.maxAppendChildren;
					row.lastChildRow = dataSheet[this.maxAppendChildren - 1];
					row.moreRowId = grid.id + '_more' + grid.counter++;
					this.renderHandler.insertRowAfter(row, buildPartSheetByRow(dataSheet, 0, this.maxAppendChildren), this);
					this.renderHandler.insertBlankRowAfter(row.lastChildRow, row.moreRowId,
							this.clazz.buildTreeNodeTitle(this.buildMoreNodesTitle(row), row.level + 1, 'leaf', row.moreRowId, grid), this);
					for(var i = row.childrenEndIndex, l = dataSheet.length; i < l; i++){
						dataSheet[i].notAvailable = true;
					}
				}else{
					this.renderHandler.insertRowAfter(row, dataSheet, this);
				}
				row.record[this.childrenProperty] = children;
				this.trigger('resize');
				this.afterexpand(row);
				if(this.nodeLoadingImg){
					$('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-loading');
				}
			},
			insertChildrenAfter: function(parentRowId, index, children){
				var parentRow = this.dataElMap[parentRowId], beforeRow = null, 
				oldChildren = parentRow.record[this.childrenProperty] = (parentRow.record[this.childrenProperty] || []), 
				dataSheet = this.clazz.buildDataSheet(children, this, parentRow, parentRow.level + 1, dataSheet, false);
				parentRow.childrenSheet = parentRow.childrenSheet || [];
				//初始化前一行和新children和datasheet数据
				if(index < 0){
					beforeRow = parentRow;
					index = 0;
				}else{
					beforeRow = parentRow.childrenSheet[index];
					index++;
				}
				//插入新child数据
				for(var i = 0, l = children.length; i < l; i++){
					var r = dataSheet[i];
					r.notAvailable = beforeRow.notAvailable;
					oldChildren.splice(index, 0, children[i]);
					parentRow.childrenSheet.splice(index++, 0, r);
				}
				//如果前一行不在更多行展示，那么新插入行展示
				if(!beforeRow.notAvailable){
					this.renderHandler.insertRowAfter(beforeRow, dataSheet, this);
					if(parentRow.moreRowId){
						parentRow.childrenEndIndex += children.length;
					}
				}
				this.trigger('resize');
			},
			moreNodes: function(parentRowId){
				var parentRow = this.dataElMap[parentRowId], csl = parentRow.childrenSheet.length;
				if(parentRow.childrenEndIndex >= csl){
					//已经是最后数据
					return;
				}
				this.beforemorenodes(parentRow);
				var startIndex = parentRow.childrenEndIndex;
				parentRow.childrenEndIndex += this.maxAppendChildren;
				if(parentRow.childrenEndIndex > csl){
					parentRow.childrenEndIndex = csl;
					this.renderHandler.removeRow(parentRow.moreRowId, this);
					parentRow.moreRowId = null;
				}
				for(var i = startIndex; i < parentRow.childrenEndIndex; i++){
					parentRow.childrenSheet[i].notAvailable = false;
				}
				this.renderHandler.insertRowAfter(parentRow.lastChildRow, buildPartSheetByRow(parentRow.childrenSheet, startIndex, parentRow.childrenEndIndex), this);
				parentRow.lastChildRow = parentRow.childrenSheet[parentRow.childrenEndIndex  - 1];
				this.trigger('resize');
				this.aftermorenodes(parentRow);
			}
		},
		columnDefaults: {
			title: '', //列表头
			property: '', //列显示对应属性
			valueProperty: '', //列值对应属性(rowMergeable为true时有效)，默认为列显示对应属性
			endLock: false, //是否最后的锁定列(叶子列的最后一列为true列有效)
			formatter: function(value, record, index, rowId){return value ? value : (value === 0 ? '0' : '');}, //列显示文本格式化
			'export': true, //是否需要导出
			tdCls: '', //列td样式
			rowMergeable: false, //行是否可合并的。true-合并同一列相邻行valuePorperty相等的单元格。默认为false。
			colMergeable: false, //列是否可合并。true-同一行，合并显示文本为表格对象的colMergeableText值的单元格与同一行前一个单元格。默认为false
			align: 'left', //显示文本水平对齐方式
			nowrap: false, //强制不换行
			width: 20, //列宽(如果有列锁定且所有列的宽度总和大于总宽度时为列实际宽度，否则代表列宽占的比例)
			minWidth: 20, //最小列宽(固定宽度列或锁行时无效)
			fixedWidth: false, //列固定宽度,
			children: null, //子表头
			sortable: true, //列是否排序
			summary: false, //合计列。为true时，property属性不能为空
			propertyProcessor: function(record, propertyName){return record[propertyName];}, //属性值处理器
			titleCol: false //树标题列，第一个标题列有效。默认第一列
		},
		defaults: {
			headers: null, //列定义数组
			store: null, //数据仓库实例
			autoLoad: true, //自动加载数据
			indent: 20, //第一列标题缩进
			sumRender: 'footer', //合计行渲染位置。header-表头，footer-表尾
			summaryTitle: '总合计', //合计标题
			summary: false, //汇总总开关
			colMergeableText: '<merge/>', //列可合并特殊显示文本
			noDataMsg: '无数据', //没有数据显示文本
			cellPaddingX: 1.5, //单元格X轴方向间距
			cellPaddingY: 1.5, //单元格Y轴方向间距
			xOverflow: 'auto', //X轴滚动。scroll, auto
			yOverflow: 'scroll', //Y轴滚动。scroll, auto
			childrenProperty: 'children', //下级数据属性
			expandProperty: 'expand', //记录展开下级数据属性
			levelClsPrefix: 'ui-tree-grid-record-level', //记录层级样式前缀
			asynLoadChildren: false, //是否异步加载数据
			loadChildren: function(record, rowId){}, //异步加载
			nodeLoadingImg: null, //节点加载图片
			beforecollapse: function(row){}, //折叠前操作
			aftercollapse: function(row){}, //折叠后操作
			beforeexpand: function(row){}, //展开前操作
			afterexpand: function(row){}, //展开后操作
			maxAppendChildren: -1, //最大附加子节点数
			buildMoreNodesTitle: function(parentRow){
				return '<a href="javascript: void(0);" onclick="var $el = $(this).parent(), grid = UI.TreeGrid.getInstance($el.parents(\'.' 
				+ this.clazz.cls + ':first\')); grid.moreNodes(\'' + parentRow.id + '\');">更多......</a>';
			},
			beforemorenodes: function(parentRow){},
			aftermorenodes: function(parentRow){},
			drill: function(record){} //下钻
		}
	});
})(jQuery);