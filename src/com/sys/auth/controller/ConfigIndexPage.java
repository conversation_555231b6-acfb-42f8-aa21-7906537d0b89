package com.sys.auth.controller;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.chevron.answerquestion.service.QuestionBizService;
import com.chevron.o2oorder.Constant.WexConstants;
import com.common.util.*;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.dao.*;
import com.sys.auth.model.*;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.DicItemVoExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.support.RequestContext;

import com.chevron.promote.model.CustomerRegionUser;
import com.common.base.BaseController;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.auth.WxAuthException;
import com.redis.tockenMgnt.service.TokenMgntService;
import com.sys.auth.service.UserServiceI;
import com.sys.auth.service.WxUserServiceI;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.sys.push.business.MessageBizService;
import com.sys.push.dao.WxPushMessageMapper;
import com.sys.push.model.PushMessageParams;
import com.sys.push.model.WxPushMessage;

import cn.hutool.core.util.StrUtil;
import net.sf.json.JSONArray;

@Controller
public class ConfigIndexPage extends BaseController {
	@Resource
	WxRoleServiceImpl wxRoleService;
	@Resource
	WxCompanyInfoMapper wxCompanyInfoMapper;
	@Resource
	WxTMenuMapper wxTMenuMapper;
	@Resource
	WxUserServiceI wxUserService;
	@Resource
	UserServiceI userService;
	@Resource
	WxTOrgMapper wxOrgMapper;
	@Resource
	public WxTRoleMapper wxTRoleMapper;
	@Resource
	public WxTUserroleMapper wxTUserroleMapper;
	@Autowired
	private MessageBizService messageBizService;
	@Autowired
	protected RedisTemplate<Serializable, Serializable> redisTemplate;

	@Autowired
	private DicItemVoMapper dicItemVoMapper;

	@Autowired
	private OperationPermissionBizService operationPermissionBizService;

	@Resource
	private WxPushMessageMapper wxPushMessageMapper;

	@Resource
	private WxTUserMapper wxTUserMapper;

	@Resource
	private QuestionBizService questionBizService;
	
	public final static String REDIS_KEY_PERMITTED_MENU_SET = "PERMITTED_MENU_SET";

	private static TokenMgntService tokenMgntService = SpringUtils.getBean(TokenMgntService.class);

	public static final Logger log = LoggerFactory.getLogger(ConfigIndexPage.class);

	private static Comparator<MenuItem> menuComparator = new Comparator<MenuItem>() {
		public int compare(MenuItem s1, MenuItem s2) {
			if (s1.getSort() != s2.getSort()) {
				return s1.getSort() - s2.getSort();
			} else {
				if (!s1.getMenuId().equals(s2.getMenuId())) {
					return s1.getMenuId().compareTo(s2.getMenuId());
				}
			}
			return -1;
		}
	};

	@RequestMapping(value = "/index.do")
	public Object loadIndexPage(HttpServletRequest request, @RequestBody String hasanswered) throws Exception {
		WxTUser currentUser = ContextUtil.getCurUser();
		Boolean isFlsr = false;
		//是否答过题
		Boolean hasAnswered = true;
		//当月有上架题库
		Boolean hasQuestion = true;

		if(!StringUtils.isNull(hasanswered)){
			request.getSession().setAttribute("hasAnswered",hasanswered);
		}
		//是否已答题
		if(null == request.getSession().getAttribute("hasAnswered") && StringUtils.isNull(hasanswered)){
			hasAnswered = false;
		}
		//未答题，判断是否有答题题目
		if(!hasAnswered){
			JsonResponse question = questionBizService.oneQuestion();
			if(null==question.get("data")){
				hasQuestion=false;
			}
		}

		//非经销商，判断是否 flsr 通过角色操作权限(1024)判断
		if(!currentUser.USER_MODEL_SP.equals(currentUser.getUserModel()) && !hasAnswered && hasQuestion){
			int appPermissionWeight = operationPermissionBizService.getPermissionWeight(currentUser.getUserId(), "App");
			if(appPermissionWeight>0 && (appPermissionWeight&1024)>0){
				isFlsr = true;
			}
		}
		//有题，且未答题：经销商（KA不开放）和flsr需要强制答题
		if(hasQuestion && !hasAnswered && (currentUser.USER_MODEL_SP.equals(currentUser.getUserModel()) || isFlsr)){
			//该经销商是否为KA,KA不开放，不需答题
			if(wxTUserMapper.countKaByOrgId(currentUser.getOrgId())==0){
				//字典未配置或code='0'表示关闭答题
				DicItemVoExample dicItemVoExample = new DicItemVoExample();
				dicItemVoExample.createCriteria().andDicTypeCodeEqualTo("answer_question")
						.andStatusEqualTo("1");
				List<DicItemVo> dicItemVos = dicItemVoMapper.selectByExample(dicItemVoExample);
				if(null != dicItemVos && dicItemVos.size()>0 && !"0".equals(dicItemVos.get(0).getDicItemCode())){
					if(StringUtils.isNull(hasanswered)){
						//判断是否已答题-未答过题
						if(null == request.getSession().getAttribute("hasAnswered")
								|| StringUtils.isNull(request.getSession().getAttribute("hasAnswered").toString())){
							return new ModelAndView("redirect:/SPA/login-force-answer/index.jsp#/loginanswer");
						}
					}else {
						request.getSession().setAttribute("hasAnswered",hasanswered);
					}
				}
			}
		}
		if(StrUtil.isNotBlank(currentUser.getDefaultLocale()));
		HttpSessionGets.setLocale(request, currentUser.getDefaultLocale());

		// 加载未读消息
		loadMessageInfo(request);

		// 加载用户菜单(根据不同权限)
		request.setAttribute("menuItemList", getUserMenuList(request));
		
		//从session中转移登录信息到request
		String loginCode = (String)request.getSession().getAttribute("loginCode");
		request.setAttribute("loginCode", loginCode);
		request.setAttribute("loginMsg", request.getSession().getAttribute("loginMsg"));
		if(WxAuthException.PWD_WARNING_EXPIRE.equals(loginCode)) {
			request.getSession().setAttribute("loginCode", null);
			request.getSession().setAttribute("loginMsg", null);
		}

		return new ModelAndView("index");
	}

	/**
	 * 加载用户菜单列表,
	 * @return  menuItemList
	 * @throws Exception
	 */
	private List<MenuItem> getUserMenuList(HttpServletRequest request) throws Exception {
		WxTUser currentUser = ContextUtil.getCurUser();
		WxTRolesource rs = new WxTRolesource();
		CustomerRegionUser customerRegionUser = userService.getCustomerRegionUser();
		rs.setRsType(2);
		rs.setXgUser(currentUser.getUserId());
		rs.setTenantId(currentUser.getOrgId());// modify by bo.liu 0818 currentUser.getTenantId()
		List<WxTMenu> wxTMenulist = null;
		try {
			wxTMenulist = wxTMenuMapper.selMenuByPermissions(rs);
		} catch (Exception ex) {
			log.error("获取用户菜单列表出错, 异常发生: ", ex);
			throw ex;
		}

		List<MenuItem> menuItemList = new ArrayList<MenuItem>();

		if (null == wxTMenulist || wxTMenulist.isEmpty()) {
			return menuItemList;
		}

		Map<Long, MenuItem> menuMap = new TreeMap<Long, MenuItem>();
		RequestContext rc = new RequestContext(request);
		HashSet<String> permittedMenus = new HashSet<String>();
        String promoteUserType="";
        String pageType ="";
		for (WxTMenu wxTMenu : wxTMenulist) {
			MenuItem menuItem = new MenuItem();
			menuItem.setMenuId(String.valueOf(wxTMenu.getMenuId()));

			if (StrUtil.isNotBlank(wxTMenu.getI18nCode()) && rc.getMessage(wxTMenu.getI18nCode()) != null)
				menuItem.setText(rc.getMessage(wxTMenu.getI18nCode()));
			else
				menuItem.setText(wxTMenu.getMenuName());

			if (wxTMenu.getMenuUrl() != null) {
				permittedMenus.add(wxTMenu.getMenuUrl());
				if (wxTMenu.getMenuUrl().endsWith("id=")) {
					// DMS菜单
					menuItem.setHref(wxTMenu.getMenuUrl() + userService.encryptUserId(currentUser.getUserId()));
				} else if (wxTMenu.getMenuUrl().indexOf("?") > 0) {
					menuItem.setHref(wxTMenu.getMenuUrl() + "&mid=" + menuItem.getMenuId());
				} else {
					// C&I地促加速计划
					if (wxTMenu.getMenuUrl().endsWith(Constants.PROMOTE_PLAN_MANAGE_PAGE)
							|| wxTMenu.getMenuUrl().endsWith(Constants.PROMOTE_ACTIVITY_MANAGE_PAGE)
							|| wxTMenu.getMenuUrl().endsWith(Constants.PROMOTE_ACTIVITY_FEEDBACK_PAGE)
							||  wxTMenu.getMenuUrl().endsWith(Constants.PROMOTE_NEW_ACTIVITY_MANAGE_PAGE)
							|| wxTMenu.getMenuUrl().equals(Constants.PROMOTE_NEW_ACTIVITY_MANAGE_PAGES)) {
						if (Constants.PROMOTE_DEALER.equals(customerRegionUser.getCustomerRoleName()))// 经销商
						{
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=dealer&pageType=feedback");
						} else if (Constants.PROMOTE_SALES.equals(customerRegionUser.getCustomerRoleName()))// 销售
						{
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=sales&pageType=apply");
							promoteUserType = "sales";
							pageType = "apply";
						} else if (Constants.PROMOTE_SUPERVISOR.equals(customerRegionUser.getCustomerRoleName()))// 小区经理
						{
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=supervisor&pageType=approve");
							promoteUserType = "supervisor";
							pageType = "approve";
						} else if (Constants.PROMOTE_CHANNELMANAGER.equals(customerRegionUser.getCustomerRoleName()))// 大区经理
						{
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=channelManager&pageType=approve");
							promoteUserType = "channelManager";
							pageType = "approve";

						} else if (Constants.PROMOTE_MARKETING.equals(customerRegionUser.getCustomerRoleName()))// marketing
						{
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=marketing&pageType=approve");
							promoteUserType = "marketing";
							pageType = "approve";

						} else if (Constants.PROMOTE_CAI_CHEVRON_MANAGER
								.equals(customerRegionUser.getCustomerRoleName()))// 雪佛龙管理员
						{
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=marketing&pageType=approve");
							promoteUserType = "marketing";
							pageType = "approve";
						} else {
							menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId()
									+ "&promoteUserType=marketing&pageType=apply");
							promoteUserType = "marketing";
							pageType = "apply";
						}

					} else {
						menuItem.setHref(wxTMenu.getMenuUrl() + "?mid=" + menuItem.getMenuId());
					}
				}
			}
			menuItem.setPid(wxTMenu.getMenuPid());
			menuItem.setSort(wxTMenu.getSort());
			menuItem.setIcon(wxTMenu.getMenuImgpath());
			menuMap.put(wxTMenu.getMenuId(), menuItem);
			HttpSession sessions = HttpSessionGets.getSession();
			sessions.setAttribute("promoteUserType",promoteUserType);
			sessions.setAttribute("pageType",pageType);
		}

		//redis缓存授权菜单
		tokenMgntService.setUserProperties(currentUser.getUserId(), REDIS_KEY_PERMITTED_MENU_SET, permittedMenus);
		for (MenuItem menuItem : menuMap.values()) {
			if (menuItem.getPid() == 0) {
				menuItemList.add(menuItem);
			} else {
				MenuItem parentItem = menuMap.get(menuItem.getPid());
				if (parentItem != null) {
					if (parentItem.getItems() != null) {
						parentItem.getItems().add(menuItem);
					} else {
						List<MenuItem> items = new ArrayList<MenuItem>();
						items.add(menuItem);
						parentItem.setItems(items);
					}
				}
			}
		}

		Collections.sort(menuItemList, menuComparator);
		for (MenuItem menuItem : menuItemList) {
			List<MenuItem> subMenuItemList = menuItem.getItems();
			if (null != subMenuItemList && !subMenuItemList.isEmpty()) {
				Collections.sort(subMenuItemList, menuComparator);
			}
		}

		return menuItemList;
	}
	
	/**
	 * 判断URL是否有访问权限
	 * @param templateUrl
	 * @param loginUserId
	 * @return
	 */
	public static boolean isMenuPermitted(String templateUrl, Long loginUserId) {
		@SuppressWarnings("unchecked")
		Set<String> permittedMenus = (Set<String>) tokenMgntService.getUserProperties(loginUserId, REDIS_KEY_PERMITTED_MENU_SET);
		if(permittedMenus == null) {
			throw new RuntimeException("未初始化授权菜单");
		}
		for(String url : permittedMenus) {
			if(url.contains(templateUrl)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 加载未读消息到请求头, 以便JSP初始化加载未读消息
	 * @param request HttpServletRequest
	 */
	private void loadMessageInfo(HttpServletRequest request) {
		if (null == request) {
			return;
		}
		// 加载未读消息
		PushMessageParams pushMessageParams = new PushMessageParams();
		pushMessageParams.setPaging(false);
		pushMessageParams.setField("createTime");
		pushMessageParams.setDirection("DESC");
		pushMessageParams.setStatus(new Integer[] { 1 });
		pushMessageParams.setUserId(ContextUtil.getCurUserId());
        pushMessageParams.setMessageTypes(Arrays.asList(WxPushMessage.MSG_TYPE_TIPS,WxPushMessage.MSG_TYPE_PUBLIC));
		List<WxPushMessage> messages = wxPushMessageMapper.queryPushedMessage(pushMessageParams);
		request.setAttribute("unreadMessageSize", messages.size());
		request.setAttribute("unreadMessages", messages);
		// 加载提示消息
		if (request.getSession().getAttribute("REMAINDER_MESSAGE_LOADED") == null) {
			List<WxPushMessage> remainderMessages = messageBizService.loadRemainderMessages();
			if (remainderMessages != null && !remainderMessages.isEmpty()) {
				request.setAttribute("remainderMessages", JSONArray.fromObject(remainderMessages).toString());
			}
			request.getSession().setAttribute("REMAINDER_MESSAGE_LOADED", true);
		}
	}

	/**
	 * 设置用户角色信息
	 * 此方法已废弃, 目前没有任何Java/JSP文件调用<br>
	 * 适当的时候, 应该删除此方法<br>
	 * 为了校验用户是否包含某个角色反复请求数据库, 性能较差<br>
	 * add comments by Ervin
	 * @param currentUser
	 */
	@Deprecated
	private void setUserRoles(WxTUser currentUser) {
		HttpSession sessions = HttpSessionGets.getSession();
		WxTUser sessionUser = currentUser;
		Long roleId;
		boolean isContainRole;
		// 查询是否为管理员
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("超级管理员").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setManger(isContainRole);
		// test
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("test").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setIstest(isContainRole);
		// test fun
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("test_fun").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setIstest_fun(isContainRole);
		// Service_Partner_BD = 1185
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("Service_Partner_BD").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setService_Partner_BD(isContainRole);

		// Service_Partner_Manager = 1186
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("Service_Partner_Manager").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setService_Partner_Manager(isContainRole);

		// Service_Partner_CSR = 1187
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("Service_Partner_CSR").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setService_Partner_CSR(isContainRole);

		// Chevron_Mnaager = 1188
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("Chevron_Mnaager").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setChevron_Manager(isContainRole);

		// Workshop_Manager = 1189
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("Workshop_Manager").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setWorkshop_Manager(isContainRole);

		// Workshop_Mechanic = 1190
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("Workshop_Mechanic").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setWorkshop_Mechanic(isContainRole);

		// DataRoleTestOrganization = 1191
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("DataRoleTestOrganization").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setDataRoleTestOrganization(isContainRole);

		// DataRoleMaillist
		roleId = Long.parseLong(MyPropertyConfigurer.getVal("DataRoleMaillist").trim());
		isContainRole = wxUserService.isIncludeRole(currentUser.getUserId(), roleId);
		sessionUser.setDataRoleMaillist(isContainRole);
		sessions.setAttribute("sessionUser", sessionUser);
	}

	/**
	 * 此方法已废弃, 目前没有任何Java/JSP文件调用<br>
	 * 适当的时候, 应该删除此方法<br>
	 * add comments by Ervin
	 * @param partnerId
	 */
	@Deprecated
	private void getOrgsByPartnerId(Long partnerId) {
		List<WxTOrg> lstOrgs;
		if (null != partnerId && partnerId != 1) {
			lstOrgs = wxOrgMapper.getOrgsByPartnerId(partnerId);// 雪佛龙用户
		} else {
			lstOrgs = wxOrgMapper.getOrgsByChevronUser();
		}

		// 按区域名称排序
		SortList<WxTOrg> sortList = new SortList<WxTOrg>();
		sortList.Sort(lstOrgs, "getOrgName", null);

		HttpSession sessions = HttpSessionGets.getSession();
		sessions.setAttribute("lstOrgs", lstOrgs);
	}

}
