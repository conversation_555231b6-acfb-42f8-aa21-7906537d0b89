UPDATE  wx_t_user SET email='<EMAIL>' WHERE email IS NOT NULL AND email!=''
UPDATE wx_t_partner_responsible_main SET day_report_cc = '<EMAIL>' WHERE day_report_cc LIKE '%<EMAIL>%';
update t set dic_item_name='<EMAIL>' from wx_t_dic_item t where dic_item_name like '%<EMAIL>%'
update t set dic_item_desc='<EMAIL>' from wx_t_dic_item t where dic_item_desc like '%<EMAIL>%'
update t set dic_item_code='<EMAIL>' from wx_t_dic_item t where dic_item_code like '%<EMAIL>%'
update t set code='<EMAIL>' from wx_t_properties t where code like '%<EMAIL>%'
update t set codename='<EMAIL>' from wx_t_properties t where codename like '%<EMAIL>%'

