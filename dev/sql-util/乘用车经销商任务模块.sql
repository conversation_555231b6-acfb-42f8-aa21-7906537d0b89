--***********
--武陵区弘业润滑油经营部
--1. 设置业务权限
insert into wx_t_dealer_business_fun (dealer_id, business_fun_code, business_custom_id)
select o.id, 'TASK', -1 from wx_t_organization o where o.type=1 and o.status=1 and exists (select 1 
from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where (u.type is null or u.type != 1) and u.org_id=o.id and r.ch_role_name='CDM_DIST')
and not exists (select 1 from wx_t_dealer_business_fun bf where bf.dealer_id=o.id and bf.business_fun_code='TASK');
insert into wx_t_dealer_business_fun (dealer_id, business_fun_code, business_custom_id)

insert into wx_t_dealer_business_fun (dealer_id, business_fun_code, business_custom_id)
select o.id, 'DIST_B2B', -1 from wx_t_organization o where o.type=1 and o.status=1 and exists (select 1 
from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where (u.type is null or u.type != 1) and u.org_id=o.id and r.ch_role_name='CDM_DIST')
and not exists (select 1 from wx_t_dealer_business_fun bf where bf.dealer_id=o.id and bf.business_fun_code='DIST_B2B')


--2. 设置菜单权限
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50376 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50377 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50325 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50327 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50329 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50328 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=40050 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)

--3. 设置数据权限设置 CDM_DIST
--4. 设置tab权限
update wx_t_dic_item set dic_item_desc='Service_Partner_BD,Service_Partner_Manager,CDM_DIST' where id=2452
update wx_t_dic_item set dic_item_desc='Service_Partner_BD,Service_Partner_Manager,CDM_DIST' where id=2451

--5. 设置销售订单下单产品权限
insert into wx_t_dea_sell_thr_pro_per (partner_id, product_sku)
select distinct o.id, p.sku from wx_t_organization o
left join wx_t_product p on p.support_sell_through=1 where o.type=1 and o.status=1 and exists (select 1 
from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where (u.type is null or u.type != 1) and u.org_id=o.id and r.ch_role_name='CDM_DIST')
and not exists (select 1 from wx_t_dea_sell_thr_pro_per bf where bf.partner_id=o.id and bf.product_sku=p.sku)
