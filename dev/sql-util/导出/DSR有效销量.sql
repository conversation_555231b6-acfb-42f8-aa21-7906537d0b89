    SELECT 
	(select top 1 sales_name_cn from dw_customer_org_sales dr where dr.distributor_id=pe.distributor_id) "FLSR",
	max(o.organization_name) "经销商名称"
            ,max(u.ch_name) "DSR", max(u.login_name) 'DSR登录名'
			,CASE WHEN product.ext_flag & '2' > 0 THEN '英雄产品'
                  WHEN product.ext_flag & '4' > 0 THEN '辅油'
             ELSE '其他'
             END "产品分类"
            ,SUM(tt_order_line.amount * CONVERT(decimal(9,2), CASE WHEN product.capacity = '' THEN NULL ELSE product.capacity END )) AS "销量"
    FROM wx_t_order_line tt_order_line
    JOIN wx_t_order tt_order ON tt_order.id = tt_order_line.order_id
    JOIN dbo.wx_t_work_shop ws on ws.id = tt_order.work_shop_id
    LEFT JOIN wx_t_product product ON tt_order_line.sku = product.sku
    LEFT JOIN PP_MID.dbo.syn_dw_to_pp_product bp ON bp.product_code_SAP = tt_order_line.sku
 	left join wx_t_user u on u.user_id=tt_order.creator
	left join wx_t_organization o on o.id=ws.partner_id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
    --WHERE YEAR(tt_order.create_time) = @year
    --AND tt_order.create_time >= DATEADD(mm, DATEDIFF(mm,0,getdate()), 0)
    WHERE tt_order.partner_confirm_time >='2021-01-01'
    AND tt_order.partner_confirm_time <= getdate()
    AND tt_order.status in ('11') --11：经销商老板已确认
    AND tt_order.partner_confirm_type = 1  --2021-07-05 zhentao确认经销商老板确认的销量还需要加上这个条件
    AND product.capacity is not null
    AND product.capacity != ''
    AND ws.partner_id <> 9
    AND ws.delete_flag = 0
--20211220 Mifar: 12月之前使用旧数据源，12月之后切换新数据源
    AND tt_order.create_time>='2021-12-01'
	AND tt_order.create_time<'2021-12-08'
   --AND product.ext_flag & '2' > 0  --英雄产品
    GROUP BY 
        tt_order.creator,
        --product.ext_flag,
        ws.partner_id,pe.distributor_id
		,CASE WHEN product.ext_flag & '2' > 0 THEN '英雄产品'
                  WHEN product.ext_flag & '4' > 0 THEN '辅油'
             ELSE '其他'
             END
	order by (select top 1 sales_name_cn from dw_customer_org_sales dr where dr.distributor_id=pe.distributor_id),
	ws.partner_id,tt_order.creator