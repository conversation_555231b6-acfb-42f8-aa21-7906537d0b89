create PROCEDURE [dbo].[pr_syn_bi_data]
as
begin 
	set xact_abort on
	SET NOCOUNT ON
	begin try
	/*同步region数据*/
truncate table [dbo].[dw_region_sales_channel_rel];
INSERT INTO [dbo].[dw_region_sales_channel_rel]
           ([region_name]
           ,[channel_manager_cai]
           ,[channel_manager_name]
           ,[sales_channel_name]
           ,[bu]
           ,[bu_manager_name]
           ,[bu_manager_cai])
SELECT [region_name]
      ,[channel_manager_cai]
      ,[channel_manager_name]
      ,[sales_channel_name]
      ,[bu]
      ,[bu_manager_name]
      ,[bu_manager_cai]
  FROM [chemid].[chevronDW_Base].dbo.view_region_sales_channel_rel_pp
  
    /*同步BU CHANNEL REGION结构*/
truncate table  [dbo].[dw_customer_region_sales_supervisor_rel]
INSERT INTO [dbo].[dw_customer_region_sales_supervisor_rel]
           ([customer_name_cn]
           ,[region_name]
           ,[sales_cai]
           ,[sales_name]
           ,[suppervisor_cai]
           ,[suppervisor_name])
SELECT [customer_name_cn]
      ,[region_name]
      ,[sales_cai]
      ,[sales_name]
      ,[suppervisor_cai]
      ,[suppervisor_name]
  FROM [chemid].[chevronDW_Base].dbo.view_customer_region_sales_supervisor_rel_pp
  
  /*更新销售业务设置*/
  update prm set prm.user_id=(select top 1 u1.user_id from dw_customer_region_sales_supervisor_rel crss
		left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
		left join wx_t_user u1 on u1.cai=
			case when prm.operator_type='suppervisor' then crss.suppervisor_cai 
				when prm.operator_type='channel_manager' then rsc.channel_manager_cai
				when prm.operator_type='bu_manager' then rsc.bu_manager_cai
				else crss.sales_cai end
		where crss.sales_cai=prm.sales_cai)
 from wx_t_partner_responsible_main prm 
where prm.sales_cai is not null and not exists (select 1 from dw_customer_region_sales_supervisor_rel crss
		left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
		left join wx_t_user u1 on u1.cai=
			case when prm.operator_type='suppervisor' then crss.suppervisor_cai 
				when prm.operator_type='channel_manager' then rsc.channel_manager_cai
				when prm.operator_type='bu_manager' then rsc.bu_manager_cai
				else crss.sales_cai end
		where crss.sales_cai=prm.sales_cai and prm.user_id=u1.user_id)
		
    /*同步sales target*/
truncate table dw_sales_target_actual_by_month;

INSERT INTO [dbo].[dw_sales_target_actual_by_month]
           ([customer_code]
           ,[customer_name]
           ,[sales]
           ,[sales_cai]
           ,[supervisor]
           ,[supervisor_cai]
           ,[region]
           ,[actual_volume]
           ,[target_volume]
           ,[pecent])
SELECT [customer_code]
      ,[customer_name]
      ,[sales]
      ,[sales_cai]
      ,[supervisor]
      ,[supervisor_cai]
      ,[region]
      ,[actual_volume]
      ,[target_volume]
      ,[pecent]
  FROM [chemid].chevronDW_Base.dbo.view_sales_target_actual_by_month

    /*同步sales target detail*/
 truncate table dw_sales_detail_by_month;

INSERT INTO [dbo].dw_sales_detail_by_month
           ([customer_code]
      ,[customer_name_cn]
      ,[region_name]
      ,[product_code_SAP]
      ,[product_name]
      ,[sales]
      ,[sales_cai]
      ,[supervisor]
      ,[supervisor_cai]
      ,[date]
      ,[liters])
SELECT [customer_code]
      ,[customer_name_cn]
      ,[region_name]
      ,[product_code_SAP]
      ,[product_name]
      ,[sales]
      ,[sales_cai]
      ,[supervisor]
      ,[supervisor_cai]
      ,[date]
      ,[liters]
  FROM [chemid].chevronDW_Base.dbo.view_sales_detail_by_month

	insert into wx_log (operator,log_type,ext_property1, create_time) values (1,'syn.bi.success','BI数据同步',getdate());
	end try
	begin catch
		insert into wx_log (operator,log_type,ext_property1,ext_property2,create_time) values (1,'syn.bi.error','BI数据同步',ERROR_MESSAGE(),getdate());
	end catch
end