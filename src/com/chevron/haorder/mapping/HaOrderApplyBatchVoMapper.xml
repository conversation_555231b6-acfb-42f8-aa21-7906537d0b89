<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.haorder.dao.HaOrderApplyBatchVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.haorder.model.HaOrderApplyBatchVo" >
    <id column="batchid" property="batchid" jdbcType="BIGINT" />
    <result column="batch_title" property="batchTitle" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="approve_status" property="approveStatus" jdbcType="NVARCHAR" />
    <result column="prokey" property="prokey" jdbcType="NVARCHAR" />
    <result column="prodef_id" property="prodefId" jdbcType="NVARCHAR" />
    <result column="proinstance_id" property="proinstanceId" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    batchid, batch_title, create_time, approve_status, prokey, prodef_id, proinstance_id, 
    remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.haorder.model.HaOrderApplyBatchVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_haorder_application_batch
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_haorder_application_batch
    where batchid = #{batchid,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_haorder_application_batch
    where batchid = #{batchid,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.haorder.model.HaOrderApplyBatchVoExample" >
    delete from wx_t_haorder_application_batch
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.haorder.model.HaOrderApplyBatchVo" >
    insert into wx_t_haorder_application_batch (batchid, batch_title, create_time, 
      approve_status, prokey, prodef_id, 
      proinstance_id, remark)
    values (#{batchid,jdbcType=BIGINT}, #{batchTitle,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{approveStatus,jdbcType=NVARCHAR}, #{prokey,jdbcType=NVARCHAR}, #{prodefId,jdbcType=NVARCHAR}, 
      #{proinstanceId,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.haorder.model.HaOrderApplyBatchVo" useGeneratedKeys="true" keyProperty="batchid">
    insert into wx_t_haorder_application_batch
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="batchid != null" >
        batchid,
      </if>
      <if test="batchTitle != null" >
        batch_title,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="approveStatus != null" >
        approve_status,
      </if>
      <if test="prokey != null" >
        prokey,
      </if>
      <if test="prodefId != null" >
        prodef_id,
      </if>
      <if test="proinstanceId != null" >
        proinstance_id,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="batchid != null" >
        #{batchid,jdbcType=BIGINT},
      </if>
      <if test="batchTitle != null" >
        #{batchTitle,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approveStatus != null" >
        #{approveStatus,jdbcType=NVARCHAR},
      </if>
      <if test="prokey != null" >
        #{prokey,jdbcType=NVARCHAR},
      </if>
      <if test="prodefId != null" >
        #{prodefId,jdbcType=NVARCHAR},
      </if>
      <if test="proinstanceId != null" >
        #{proinstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_haorder_application_batch
    <set >
      <if test="record.batchid != null" >
        batchid = #{record.batchid,jdbcType=BIGINT},
      </if>
      <if test="record.batchTitle != null" >
        batch_title = #{record.batchTitle,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approveStatus != null" >
        approve_status = #{record.approveStatus,jdbcType=NVARCHAR},
      </if>
      <if test="record.prokey != null" >
        prokey = #{record.prokey,jdbcType=NVARCHAR},
      </if>
      <if test="record.prodefId != null" >
        prodef_id = #{record.prodefId,jdbcType=NVARCHAR},
      </if>
      <if test="record.proinstanceId != null" >
        proinstance_id = #{record.proinstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_haorder_application_batch
    set batchid = #{record.batchid,jdbcType=BIGINT},
      batch_title = #{record.batchTitle,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      approve_status = #{record.approveStatus,jdbcType=NVARCHAR},
      prokey = #{record.prokey,jdbcType=NVARCHAR},
      prodef_id = #{record.prodefId,jdbcType=NVARCHAR},
      proinstance_id = #{record.proinstanceId,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.haorder.model.HaOrderApplyBatchVo" >
    update wx_t_haorder_application_batch
    <set >
      <if test="batchTitle != null" >
        batch_title = #{batchTitle,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus,jdbcType=NVARCHAR},
      </if>
      <if test="prokey != null" >
        prokey = #{prokey,jdbcType=NVARCHAR},
      </if>
      <if test="prodefId != null" >
        prodef_id = #{prodefId,jdbcType=NVARCHAR},
      </if>
      <if test="proinstanceId != null" >
        proinstance_id = #{proinstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where batchid = #{batchid,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.haorder.model.HaOrderApplyBatchVo" >
    update wx_t_haorder_application_batch
    set batch_title = #{batchTitle,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      approve_status = #{approveStatus,jdbcType=NVARCHAR},
      prokey = #{prokey,jdbcType=NVARCHAR},
      prodef_id = #{prodefId,jdbcType=NVARCHAR},
      proinstance_id = #{proinstanceId,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR}
    where batchid = #{batchid,jdbcType=BIGINT}
  </update>
  
  <select id="getPendingApprovalByMap" resultMap="BaseResultMap" parameterType="map">
        select t_ha_apply.* from wx_t_haorder_application_batch t_ha_apply
        LEFT JOIN wx_t_approve_history t_apply_his 
             ON t_apply_his.order_batch_id = t_ha_apply.batchid
             AND t_ha_apply.prokey = t_apply_his.apply_templet_type
        WHERE t_apply_his.order_batch_id IS NULL
            and
            1=1
            <if test="bachId !=null ">
              and  t_ha_apply.batchid = #{bachId}
            </if>
            <if test="processKey !=null ">
              and  t_ha_apply.prokey = #{processKey}
            </if>
            <if test="approveStatus !=null ">
              and  t_ha_apply.approve_status = #{approveStatus}
            </if>
             <if test="hasApprovedStatus !=null ">
              and  (t_ha_apply.approve_status = '0' OR t_ha_apply.approve_status = '1')
            </if>


  </select>
  
  <select id="getPendingApprovalForCSR" resultMap="BaseResultMap" parameterType="map">
  SELECT t_ha_apply.*, t_apply_his.order_batch_id, t_apply_his.approve_status, t_apply_his.approve_remark
    FROM wx_t_haorder_application_batch t_ha_apply 
    LEFT JOIN wx_t_approve_history t_apply_his 
    ON t_apply_his.order_batch_id = t_ha_apply.batchid
        AND t_ha_apply.prokey = t_apply_his.apply_templet_type
    WHERE t_apply_his.approve_status = '1'
        AND t_ha_apply.approve_status = '-1'
        AND NOT EXISTS (SELECT 1
                        FROM wx_t_approve_history tt
                    WHERE tt.order_batch_id = t_ha_apply.batchid
                        AND t_ha_apply.prokey = tt.apply_templet_type
                        AND tt.approve_status = '-1')
  
  </select>
  
  <select id="getHasApprovaledByMap" resultMap="BaseResultMap" parameterType="map">
        select t_ha_apply.* from wx_t_haorder_application_batch t_ha_apply
        WHERE 
            1=1
            <if test="bachId !=null ">
              and  t_ha_apply.batchid = #{bachId}
            </if>
            <if test="processKey !=null ">
              and  t_ha_apply.prokey = #{processKey}
            </if>
            <if test="hasApprovedStatus !=null ">
              and  (t_ha_apply.approve_status = '0' OR t_ha_apply.approve_status = '1')
            </if>
  </select>
  
  
  
</mapper>