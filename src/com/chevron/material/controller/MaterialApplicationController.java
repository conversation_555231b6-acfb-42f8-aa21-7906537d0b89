package com.chevron.material.controller;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;

import com.chevron.material.business.ApplicationBizService;
import com.chevron.material.dao.WXTMaterialApplicationDetailVoMapper;
import com.chevron.material.model.B2BApplicationParams;
import com.chevron.material.model.WXTMaterialWarehouseOutVo;
import com.chevron.material.service.B2BApplicationService;
import com.common.constants.Constants;
import com.common.exception.auth.WxAuthException;
import com.common.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.exportdata.ExportExcel;
import com.chevron.material.dao.WXTMaterialApplicationVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseOutVoMapper;
import com.chevron.material.model.ApplicationParams;
import com.chevron.material.model.ApplicationState;
import com.chevron.material.model.PointOrder4ExcelVo;
import com.chevron.material.model.PointOrderLine4Excel;
import com.chevron.material.model.WXTMaterialApplicationDetailVo;
import com.chevron.material.model.WXTMaterialApplicationVo;
import com.chevron.material.service.ApplicationService;
import com.chevron.material.service.MaterialService;
import com.chevron.material.util.ApplicationUtil;
import com.chevron.material.util.DealerPermissUtil;
import com.chevron.point.dto.PointType;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.PDFUtil;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogUtils;

@Controller
@RequestMapping(value = "/material/application")
public class MaterialApplicationController {
	public static final Logger log = LoggerFactory.getLogger(MaterialManageController.class);
	@Autowired
	private WXTMaterialApplicationVoMapper wxtMaterialApplicationVoMapper;
	@Autowired
	private WXTMaterialWarehouseOutVoMapper wxtMaterialWarehouseOutVoMapper;
    @Autowired
    private WXTMaterialApplicationDetailVoMapper wxtMaterialApplicationDetailVoMapper;

	@Autowired
	private MaterialService materialService;
	@Autowired
	private DicService dicService;
	@Autowired
	private ExportExcel exportExcel;
	@Autowired
	private ApplicationService applicationService;
    @Autowired
    private B2BApplicationService b2BApplicationService;
    @Autowired
    private ApplicationBizService applicationBizService;
    
    @Autowired
    private OperationPermissionBizService operationPermissionBizService;
    
    /**
     * 帮充话费订单回调订单
     * @param sign 签名验证: MD5(uid+order_no+user_order_no+{APP_KEY})， APP_KEY由帮充对接商务提供
     * @param order_no 帮充订单号
     * @param user_order_no 用户订单号
     * @param status 订单状态：200:  充值成功 202:  充值失败
     * @param memo 充值成功或失败原因
     * @param order_money 订单扣款金额，单位：元,充值失败时，扣款金额都是0
     * @param operator_order_no 官方订单号
     * @return
     */
	@ResponseBody
	@RequestMapping(value="/bchfcallback.do")
	public String bcHfCallback(@RequestParam("sign")String sign, @RequestParam("order_no")String order_no, 
			@RequestParam("user_order_no")String user_order_no, @RequestParam("status")String status, @RequestParam(value="memo", required=false)String memo, 
			@RequestParam("order_money")String order_money, @RequestParam(value="operator_order_no", required=false)String operator_order_no){
		String params = buildVirtualOrderCallbackParams(sign, order_no, user_order_no, status, memo, order_money, operator_order_no);
		log.info("bcHfCallback: " + params);
		try {
			LogUtils.addInfoLog(1l, "com.chevron.material.controller.MaterialApplicationController.bcHfCallback", params.toString());
			applicationBizService.completeHfVirtualOrder(sign, order_no, user_order_no, status, memo, order_money, operator_order_no, params);
			log.info("bcHfCallback success: " + params);
			return "success";
		} catch (Exception e) {
			LogUtils.addErrorLog(1l, "com.chevron.material.controller.MaterialApplicationController.bcHfCallback", e.getMessage(), params.toString());
			log.error(e.getMessage() + "。" + params, e);
			CommonUtil.notifyIt(e.getMessage() + "：" + params + "<br/>" + e.getStackTrace().toString());
		}
		return "error";
	}
    
    /**
     * 帮充视频会员订单回调订单
     * @param sign 签名验证: MD5(uid+order_no+user_order_no+{APP_KEY})， APP_KEY由帮充对接商务提供
     * @param order_no 帮充订单号
     * @param user_order_no 用户订单号
     * @param status 订单状态：200:  充值成功 202:  充值失败
     * @param memo 充值成功或失败原因
     * @param order_money 订单扣款金额，单位：元,充值失败时，扣款金额都是0
     * @param operator_order_no 官方订单号
     * @return
     */
	@ResponseBody
	@RequestMapping(value="/bcvipcallback.do")
	public String bcVipCallback(@RequestParam("sign")String sign, @RequestParam("order_no")String order_no, 
			@RequestParam("user_order_no")String user_order_no, @RequestParam("status")String status, @RequestParam(value="memo", required=false)String memo, 
			@RequestParam("order_money")String order_money, @RequestParam(value="operator_order_no", required=false)String operator_order_no){
		String params = buildVirtualOrderCallbackParams(sign, order_no, user_order_no, status, memo, order_money, operator_order_no);
		log.info("bcVipCallback: " + params);
		try {
			LogUtils.addInfoLog(1l, "com.chevron.material.controller.MaterialApplicationController.bcVipCallback", params.toString());
			applicationBizService.completeVipVirtualOrder(sign, order_no, user_order_no, status, memo, order_money, operator_order_no, params);
			log.info("bcVipCallback success: " + params);
			return "success";
		} catch (Exception e) {
			LogUtils.addErrorLog(1l, "com.chevron.material.controller.MaterialApplicationController.bcVipCallback", e.getMessage(), params.toString());
			log.error(e.getMessage() + "。" + params, e);
			CommonUtil.notifyIt(e.getMessage() + "：" + params + "<br/>" + e.getStackTrace().toString());
		}
		return "error";
	}
	
	private String buildVirtualOrderCallbackParams(String sign, String order_no, 
			String user_order_no, String status, String memo, String order_money, String operator_order_no) {
		Map<String, Object> params = new HashMap<String, Object>(8);
		params.put("sign", sign);
		params.put("order_no", order_no);
		params.put("user_order_no", user_order_no);
		params.put("status", status);
		params.put("memo", memo);
		params.put("order_money", order_money);
		params.put("operator_order_no", operator_order_no);
		return JsonUtil.writeValue(params);
	}
	
	private int getOutboundPermission(Long userId) {
		try {
			return operationPermissionBizService.getPermissionWeight(userId, "MaterialApplication.Outbound");
		} catch (WxPltException e) {
			throw new RuntimeException(e);
		}
	}
	
	@SuppressWarnings("unchecked")
	@RequestMapping("/exportPointOrder.do")
	public Map<String, Object> exportPointOrder(HttpServletResponse response,
			HttpServletRequest request,
			ApplicationParams params) throws Exception {//TODO

		Map<String, Object> resultMap = new HashMap<String, Object>();
		//最多拿一千条
//		params.setLimit(1000);
		params.setPaging(false);
		params.setExportStartDateProperty("ApplicationParams.exportStartDateProperty");
        List<WXTMaterialApplicationVo> rows = new ArrayList<WXTMaterialApplicationVo>();
        boolean out = false;
        if(params.getWarehouseMode()!=null && params.getWarehouseMode()){
            //如果是出库单导出那么导出需要出库的所有订单
            out = true;
    		WxTUser user = ContextUtil.getCurUser();
    		int permissionWeight = getOutboundPermission(user.getUserId());
    		if((permissionWeight & 3) == 2) {
    			params.setSupplierLoginName(user.getLoginName());
    		}
            List<WXTMaterialWarehouseOutVo> wxtMaterialWarehouseOutVos = wxtMaterialWarehouseOutVoMapper.selectByPagination(params);
            for (WXTMaterialWarehouseOutVo wxtMaterialWarehouseOutVo : wxtMaterialWarehouseOutVos) {
                WXTMaterialApplicationVo temp = new WXTMaterialApplicationVo();
                BeanUtil.copyProperties(wxtMaterialWarehouseOutVo, temp);
                temp.setApplicationStatus(wxtMaterialWarehouseOutVo.getStatus());
                rows.add(temp);
            }
        }else{
            Map<String,Object> reqMap=getlist(params);
            rows = (List<WXTMaterialApplicationVo>) reqMap.get("rows");
        }
        List<PointOrder4ExcelVo> orderList = new ArrayList<PointOrder4ExcelVo>();
		List<PointOrderLine4Excel> orderLineList=new ArrayList<PointOrderLine4Excel>();
		List<Long> applicationIds = new ArrayList<Long>(rows.size());
		Map<Long, PointOrder4ExcelVo> applicationMap = new HashMap<Long, PointOrder4ExcelVo>(rows.size());
        for(WXTMaterialApplicationVo vo : rows) {
			PointOrder4ExcelVo order=new PointOrder4ExcelVo();
            order.setAddressDetail(vo.getAddressRegionName().concat(" ").concat(vo.getAddressDetail() == null ? "" : vo.getAddressDetail()));
			order.setApplicationCode(vo.getApplicationCode());
			order.setApplicationOrgName(vo.getApplicationOrgName());
			order.setContactNumber(vo.getContactNumber());
			order.setContacts(vo.getContacts());
			order.setCreationTime(vo.getCreationTime());
			order.setStatus(ApplicationState.getText(vo.getApplicationStatus()));
            String typeName = ApplicationUtil.getPointTypeNameIncludePromoteType(vo.getApplicationType(), vo.getBusinessTypeCode());
            typeName = typeName == null ? ApplicationUtil.getPointTypeNameIncludePromoteType(vo.getPromotionType(), vo.getBusinessTypeCode()) : typeName;
            order.setPointTypeName(typeName);
			order.setComments(vo.getComments());
            order.setWorkShopName(vo.getWorkshopName());
			orderList.add(order);
			applicationIds.add(vo.getId());
			applicationMap.put(vo.getId(), order);
		}
        //获取详情
        List<WXTMaterialApplicationDetailVo> lines = new ArrayList<WXTMaterialApplicationDetailVo>();
        String applicationIdsStr = StringUtils.arrayToDelimited(applicationIds.toArray(), ",");
        try{
            lines = out ? wxtMaterialApplicationDetailVoMapper.selectLinesByWarehouseOutId(applicationIdsStr) : wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(applicationIdsStr);
        }catch (Exception e){
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
			return resultMap;
        }
        for(WXTMaterialApplicationDetailVo detailVo: lines) {
        	PointOrder4ExcelVo order = applicationMap.get(out ? detailVo.getWarehouseOutId() : detailVo.getApplicationId());
            PointOrderLine4Excel line=new PointOrderLine4Excel();
            line.setColorAndSize(detailVo.getMaterialSkuCode().split("-")[1]+","+detailVo.getMaterialSkuCode().split("-")[2]);
            line.setMaterialCode(detailVo.getMaterialCode());
            line.setMaterialName(detailVo.getMaterialName());
            line.setMaterialPrice(detailVo.getMaterialPrice());
            line.setMaterialTypeName(detailVo.getMaterialTypeName());
            line.setApplicationQty(detailVo.getApplicationQty());
            line.setWarehouseName(detailVo.getWarehouseName());
            line.setApplicationCode(order.getApplicationCode());
            line.setPointTypeName(order.getPointTypeName());
            orderLineList.add(line);
        }
//        for(WXTMaterialApplicationVo vo : rows) {
//			PointOrder4ExcelVo order=new PointOrder4ExcelVo();
//            order.setAddressDetail(vo.getAddressRegionName().concat(" ").concat(vo.getAddressDetail() == null ? "" : vo.getAddressDetail()));
//			order.setApplicationCode(vo.getApplicationCode());
//			order.setApplicationOrgName(vo.getApplicationOrgName());
//			order.setContactNumber(vo.getContactNumber());
//			order.setContacts(vo.getContacts());
//			order.setCreationTime(vo.getCreationTime());
//			order.setStatus(ApplicationState.getText(vo.getApplicationStatus()));
//            String typeName = ApplicationUtil.getPointTypeNameIncludePromoteType(vo.getApplicationType(), vo.getBusinessTypeCode());
//            typeName = typeName == null ? ApplicationUtil.getPointTypeNameIncludePromoteType(vo.getPromotionType(), vo.getBusinessTypeCode()) : typeName;
//            order.setPointTypeName(typeName);
//			order.setComments(vo.getComments());
//            order.setWorkShopName(vo.getWorkshopName());
//            //获取详情
//            List<WXTMaterialApplicationDetailVo> lines = new ArrayList<WXTMaterialApplicationDetailVo>();
//            try{
//                lines = out ? wxtMaterialApplicationDetailVoMapper.selectLinesByWarehouseOutId(vo.getId()) : wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(vo.getId());
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//            for(WXTMaterialApplicationDetailVo detailVo: lines) {
//                PointOrderLine4Excel line=new PointOrderLine4Excel();
//                line.setColorAndSize(detailVo.getMaterialSkuCode().split("-")[1]+","+detailVo.getMaterialSkuCode().split("-")[2]);
//                line.setMaterialCode(detailVo.getMaterialCode());
//                line.setMaterialName(detailVo.getMaterialName());
//                line.setMaterialPrice(detailVo.getMaterialPrice());
//                line.setMaterialTypeName(detailVo.getMaterialTypeName());
//                line.setApplicationQty(detailVo.getApplicationQty());
//                line.setWarehouseName(detailVo.getWarehouseName());
//                line.setApplicationCode(order.getApplicationCode());
//                line.setPointTypeName(order.getPointTypeName());
//                orderLineList.add(line);
//            }
//			orderList.add(order);
//		}
		List<List<?>> dataLists=new ArrayList<List<?>>();
		dataLists.add(orderList);
		dataLists.add(orderLineList);
		List<String> classNameList=new ArrayList<String>();
		classNameList.add("com.chevron.material.model.PointOrder4ExcelVo");
		classNameList.add("com.chevron.material.model.PointOrderLine4Excel");
		List<String> sheetNameList=new ArrayList<String>();
		sheetNameList.add("订单头");
		sheetNameList.add("订单详情");
		exportExcel.setResponseHeader(response, "积分订单");
		exportExcel.exportDataForMultiSheet(dataLists, classNameList, response, "积分订单", sheetNameList,request);
		return resultMap;
	}

    @ResponseBody
    @RequestMapping(value = "/getList.do", method = {RequestMethod.POST, RequestMethod.GET})
    public Map<String, Object> getlist(ApplicationParams params) {
        if (params.getB2bApplication() != null && params.getB2bApplication()) {
            B2BApplicationParams b2BApplicationParams = new B2BApplicationParams();
            BeanUtil.copyProperties(params, b2BApplicationParams);
            JsonResponse map = b2BApplicationService.getApplicationList(b2BApplicationParams);
            map.put("rows", map.get(JsonResponse.KEY_LIST));
            map.put(JsonResponse.KEY_LIST, null);
            map.put("code", "success");
            map.put("errorMsg", "成功");
            return map;
        }
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap = applicationService.getlist(params);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
    }
	
	@ResponseBody
	@RequestMapping(value="/getListCount.do")
	public Map<String,Object> getListCount(ApplicationParams params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		if(params.getApplicationType() != null && params.getApplicationType().size() == 1) {
			PointType pointType = ApplicationUtil.getPointTypeByAppType(params.getApplicationType().get(0));
			if(pointType != null){
                pointType.processOrderParams(params, curUser);
            }
		}
		if (curUser!= null && WxTUser.USER_MODEL_SP.equals(curUser.getUserModel())) {
			params.setApplicationOrgId(curUser.getOrgId());
		} else {
			params.setApplicationOrgId(null);
		}
		params.setStart(0);
		params.setLimit(10);
		try {
			wxtMaterialApplicationVoMapper.selectByPagination(params);
			resultMap.put("rowsCount", params.getTotalCount());
			resultMap.put("code", "success");
			resultMap.put("errorMsg", "成功");
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.clear();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/getOutboundList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getOutboundList(ApplicationParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser user = ContextUtil.getCurUser();
		int permissionWeight = getOutboundPermission(user.getUserId());
		if((permissionWeight & 3) == 2) {
			params.setSupplierLoginName(user.getLoginName());
		}
		try {
			resultMap.put("pointPermission",DealerPermissUtil.getCurUserDealerPermissCode(dicService));
			resultMap.put("rows", wxtMaterialWarehouseOutVoMapper.selectByPagination(params));
			resultMap.put("total", params.getTotalCount());
			resultMap.put("code", "success");
			resultMap.put("errorMsg", "成功");
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.clear();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/getTodoList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getTodoList(ApplicationParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		if (curUser!= null && WxTUser.USER_MODEL_SP.equals(curUser.getUserModel())) {
			params.setApplicationOrgId(curUser.getOrgId());
		} else {
			params.setApplicationOrgId(null);
		}
		params.setApplicantTodoList(true);
		try {
			resultMap.put("rows", wxtMaterialApplicationVoMapper.selectByPagination(params));
			resultMap.put("total", params.getTotalCount());
			resultMap.put("code", "success");
			resultMap.put("errorMsg", "成功");
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.clear();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/getDoneList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getDoneList(ApplicationParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		if (curUser!= null && WxTUser.USER_MODEL_SP.equals(curUser.getUserModel())) {
			params.setApplicationOrgId(curUser.getOrgId());
		} else {
			params.setApplicationOrgId(null);
		}
		params.setApplicantDoneList(true);
		try {
			resultMap.put("rows", wxtMaterialApplicationVoMapper.selectByPagination(params));
			resultMap.put("total", params.getTotalCount());
			resultMap.put("code", "success");
			resultMap.put("errorMsg", "成功");
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.clear();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value="/downloadWarehouseOutBoundPDF.do")
	public void downloadOutBoundPDF(HttpServletRequest request,HttpServletResponse response,@RequestParam("id")Long id) {
		Map<String,Object> variables = new HashMap<String,Object>();
		String ftlName="warehouse_outbound_template.ftl";
		String logoPath = "images\\logo2.png";
		String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + "/";
		variables.put("baseUrl", baseUrl);

		// 获取订单信息
		Map<String,Object> outBoundVo = materialService.generateOutboundPdf(id);
		variables.putAll(outBoundVo);
		String outboundCode=(String)outBoundVo.get("fileName");

		// 打印
		try {
			PDFUtil.setFileDownloadHeader(response, outboundCode, ".pdf");
			OutputStream bos = PDFUtil.createPDF(request, response, ftlName, variables, logoPath);/** 字节 */
		} catch (Exception e) {
			log.error("出库单生成失败", e);
		}
	}
}
