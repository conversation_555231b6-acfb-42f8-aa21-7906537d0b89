package com.chevron.esorder.service.impl;

import com.chevron.esorder.business.EsOrderBizService;
import com.chevron.esorder.dao.*;
import com.chevron.esorder.model.*;
import com.chevron.esorder.service.EsOrderService;
import com.chevron.exportdata.Export;
import com.chevron.haorder.business.HaOrderBizService;
import com.chevron.haorder.dao.HaOrderApplyBatchVoMapper;
import com.chevron.haorder.model.HaOrderApplyBatchVo;
import com.chevron.haorder.model.HaOrderLineVo;
import com.chevron.haorder.model.HaOrderUploadView;
import com.chevron.haorder.model.HaOrderVo;
import com.chevron.haorder.model.uploadto.UploadHaOrderInfo;
import com.chevron.haorder.service.IHaOrderService;
import com.chevron.haorder.service.impl.HaOrderServiceImpl;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.partnerorder.dao.PartnerInventoryOutRecordMapper;
import com.chevron.partnerorder.model.PartnerInventoryOutRecord;
import com.chevron.plc.dao.OutStockLineVoMapper;
import com.chevron.plc.dao.OutStockVoMapper;
import com.chevron.pms.dao.WxtOrderYYFWSetsVoMapper;
import com.chevron.pms.model.WxtOrderYYFWSetsVo;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.util.*;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.workflow.dao.ApproveHistoryMapper;
import com.workflow.model.ApproveHistory;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.JavaIdentifierTransformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;

@Service
public class EsOrderServiceImpl implements EsOrderService {

	private static Logger log = LoggerFactory.getLogger(EsOrderBizService.class);
	
	@Resource 
	EsOrderBizService esOrderService;
	@Resource 
	WXTEsSellBackOrderVoMapper esSellBackOrderVoMapper;
	@Resource 
	WXTEsSellBackOrderLineVoMapper esSellBackOrderLineVoMapper;
	@Resource 
	WXTEsSalesOrderVoMapper esSalesOrderVoMapper;
	@Resource
	private Export exportExcel;
	@Resource
	public WxTPropertiesMapper propertiesMapper;
	@Resource
	WXTEsStockoutOrderMapper esStockoutOrderMapper;
	@Resource
	WXTEsStockoutOrderInfoMapper esStockoutOrderInfoMapper;
	@Resource
	OutStockVoMapper outStockMapper;
	@Resource
	OutStockLineVoMapper outStockLineMapper;
	@Resource
	WxtOrderYYFWSetsVoMapper orderYYFWSetsMapper;
	@Resource
	HaOrderBizService haOrderBizService;
	@Resource
	private HaOrderApplyBatchVoMapper haOrderApplyMapper;
	@Resource
	private ApproveHistoryMapper approveHistoryMapper;
	@Autowired
	private PartnerInventoryOutRecordMapper partnerInventoryOutRecordMapper;
	
	@Resource
	IHaOrderService haOrderService;
	
	DecimalFormat   df  = new DecimalFormat("###########0.00");   
	DecimalFormat   df1  = new DecimalFormat("###########0.000000");   
	DecimalFormat   df2  = new DecimalFormat("###########0.00000");   
	DecimalFormat   accountDf   = new DecimalFormat("###########0");   //整形
	
	public static final String WECHATSHOPNAME = "微信avrato店";

	
	
	
	@Override  //每日相应时间点定时任务获取销售汇总详情
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> getSalesInfo(String beginTime,String endTime,boolean isUpdateOrderDetailTime) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//0.组织查询的起止时间
			Date createDate = new Date();
			//针对每日定时扫描情况，下一次扫描的时间点
			String nextBeginTime = DateUtil.getDateStr(new Date(), "HH");
			//String queryBeginTime = DateUtil.getDateStr(createDate, "yyyy-MM-dd")+" "+beginTime+":00:00";
			//String queryEndTime = DateUtil.getDateStr(createDate, "yyyy-MM-dd")+" "+endTime+":00:00";
			resultMap  = esOrderService.getSalesOrders(beginTime, endTime);
			String code = (String) resultMap.get("code");
			if(!"0".equals(code))
			{
				resultMap.put("code", "error");
				return resultMap;
			}
			String codeMsg = (String) resultMap.get("codeMsg");
			int pageTotalRecorde = Integer.parseInt(codeMsg);
			if(0==pageTotalRecorde)//0条记录
			{
				resultMap.put("code", "success");
				resultMap.put("saleacount", "0");
				return resultMap;
			}
			//1.解析销售订单
			Map<String,Object> dataMap = paseSaleOrdersResp(resultMap);
			List<OrderSaleInfoResp> allSaleOrder = (List<OrderSaleInfoResp>) dataMap.get("saleOrders");
			if(0==allSaleOrder.size())//0条记录
			{
				resultMap.put("code", "success");
				resultMap.put("saleacount", "0");
				return resultMap;
			}
			
			//2.重组销售订单数据，用于录入数据库表中
			List<WXTEsSalesOrderVo> lstSaleOrders = ReEsSaleOrders(allSaleOrder, createDate);
			
			
			//3.分页插入销售订单数据
			ImportDataPageModelUtil esSaleOrderPages = new ImportDataPageModelUtil(
					lstSaleOrders, 55);
			int es_sale_order_page_account = esSaleOrderPages.getTotalPages();
			for (int i = 1; i <= es_sale_order_page_account; i++) {
				List<WXTEsSalesOrderVo> lstSaleOrder= esSaleOrderPages
						.getObjects(i);
				esSalesOrderVoMapper.insertBatchSelective(lstSaleOrder);
			}
			
			if(isUpdateOrderDetailTime)//需要更新下一次时间点
			{
				esOrderService.updateBeginTimeForOrderDetail(nextBeginTime);
			}
			
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getSalesInfo exception", ex);
			String errorMsg = ThrowableUtil.getStackTrace(ex);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			log.info("getSalesInfo-------------errorMsg:"+errorMsg);
			return resultMap;
			
		}
		
		resultMap.put("code", "success");
		return resultMap;
		
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> getSellBackInfo(String beginTime,String endTime) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			log.info("----getSellBackInfo  0");
			//0.获取退换货订单列表
			Date createDate = new Date();
			resultMap  = esOrderService.getSellBackOrders("1",beginTime,endTime);
			String code = (String) resultMap.get("code");
			if(!"0".equals(code))
			{
				resultMap.put("code", "error");
				return resultMap;
			}
			//1.解析退换货信息列表
			Map<String,Object> dataMap = paseSellBackOrdersResp(resultMap,beginTime,endTime);
			List<SellbackOrdersResp> allSellBackOrder = (List<SellbackOrdersResp>) dataMap.get("lstSellBackOrdersResp");
			if(null==allSellBackOrder)
			{
				return dataMap;
			}
			
			//2.重组退换货订单信息、退换货详细信息
			List<WXTEsSellBackOrderVo> lstEsSellBackOrders = ReEsSellBackOrders(allSellBackOrder,createDate);
			List<WXTEsSellBackOrderLineVo> lstEsSellBackOrderLineVos = new ArrayList<WXTEsSellBackOrderLineVo>();
			for(WXTEsSellBackOrderVo wxtEsSellBackOrderVo:lstEsSellBackOrders)
			{
				lstEsSellBackOrderLineVos.addAll(wxtEsSellBackOrderVo.getLstOrderInfos());
			}
			
			//3.分页插入退货货订单
			ImportDataPageModelUtil esSellBackOrderPages = new ImportDataPageModelUtil(
					lstEsSellBackOrders, 50);
			int es_sellback_order_page_account = esSellBackOrderPages.getTotalPages();
			for (int i = 1; i <= es_sellback_order_page_account; i++) {
				List<WXTEsSellBackOrderVo> lstSellBackOrder= esSellBackOrderPages
						.getObjects(i);
				esSellBackOrderVoMapper.insertBatchSelective(lstSellBackOrder);
				
			}
			
			//4.分页插入退货订单详情
			ImportDataPageModelUtil esSellBackOrderLinePages = new ImportDataPageModelUtil(
					lstEsSellBackOrderLineVos, 50);
			int es_sellback_order_line_page_account = esSellBackOrderLinePages.getTotalPages();
			for (int i = 1; i <= es_sellback_order_line_page_account; i++) {
				List<WXTEsSellBackOrderLineVo> lstSellBackOrderLine = esSellBackOrderLinePages
						.getObjects(i);
				esSellBackOrderLineVoMapper.insertBatchSelective(lstSellBackOrderLine);
				
			}
			
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getSellBackInfo exception", ex);
			String errorMsg = ThrowableUtil.getStackTrace(ex);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
			
		}
		
		resultMap.put("code", "success");
		return resultMap;
	}
	
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> getSaleInfoFromPPandSndEamil(String beginTime,String endTime) {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			/**
			 * 第一大步骤
			 */
			Date currentDateTime = new Date();
			//0.最先是获取最后一次统计code的日期a  yyyy-MM-dd
			String lastDateAsBeginDate = "";//esOrderService.getLastTimeAsBeginTime();
			//1.获取销售订单的code,判断是否第一次获 b   如果是null就是第一次获取  时间点 HH
			String lastOrderDetailBeginTime = "null";//esOrderService.getLastTimeAsBeginTimeForOrderDetail();
			//2.获取当前时间c  yyyy-MM-dd HH:mm
			String currentTime = DateUtil.getDateStr(currentDateTime, "yyyy-MM-dd HH:mm")+":00";//not use
			//3.获取当前日期d  yyyy-MM-dd
			String currentDate ="";//DateUtil.getDateStr(currentDateTime, "yyyy-MM-dd");
			
			//add by bo.liu 0426  用于外部手动触发一次
			if(null!=beginTime&&null!=endTime)
			{
				lastDateAsBeginDate = beginTime;
				currentDate = endTime;
				
			}else
			{
				lastDateAsBeginDate = esOrderService.getLastTimeAsBeginTime(Constants.ES_SCAN_LAST_DATE);
				lastOrderDetailBeginTime = esOrderService.getLastTimeAsBeginTimeForOrderDetail();
				currentDate =DateUtil.getDateStr(currentDateTime, "yyyy-MM-dd");
			}
			//add by bo.liu 0426
			
			
			
			
			
			 /**第二大步骤（两种情况）：
			 *  若为第一次：
			 *  0.销售数据（从最后一次统计code时间（a） -当前日期12点58分(c)）；
			 *  1.退换货数据（从最后一次统计code时间(a)--当前日期12点58分(c)）;
			 *  
			 *  非第一次获取情况：
			 *  0.获取退换货订单详情（最后一次统计的日期(a)-当前时间(c)）
			 *  1.获取销售订单最后一个时间段的数据（当天18点-当前时间（c））
			 **/
			 if(lastOrderDetailBeginTime.equals("null"))
			 {
				 log.info("----------------------getSaleInfoFromPPandSndEamil init----------------------");
				 resultMap = processESOrderInit(lastDateAsBeginDate,currentDate);
				 
			 }else
			 {
				 log.info("----------------------getSaleInfoFromPPandSndEamil not init----------------------");
				 String queryOrderDetailBeginTime = DateUtil.getDateStr(currentDateTime, "yyyy-MM-dd")+" "+lastOrderDetailBeginTime+":00:00";
				 resultMap = processESOrder(queryOrderDetailBeginTime, lastDateAsBeginDate, currentDate);
			 }
			
			 if(resultMap.get("code").equals("error"))
			 {
				 throw new Exception("从网店管家总获取订单信息失败");
			 }
			

			 /**  第三大步骤：
			 *  0.更新销售订单code对应的时间点(b)对应的时间，为0点
			 *  1.更新获取最后一次统计code的日期为(当前日期d的下一日格式 :yyyy-MM-dd)
			 *  2.统计汇总
			 *  3.发送邮件
			 **/
			 //更新下一次扫描的日期及时间点   第一个参数设置为null,即代表非每天都拉取，，，，暂时这样，，，后续可以修改为每天都拉取销售订单数据。
			 Date nextCountOrderScanDate = DateUtil.addDays(currentDateTime, 1);
			 esOrderService.updateBeginTimeForOrderCount("null", DateUtil.getDateStr(nextCountOrderScanDate, "yyyy-MM-dd"));// esOrderService.updateBeginTimeForOrderCount("00", DateUtil.getDateStr(nextCountOrderScanDate, "yyyy-MM-dd"));
			
			 
			 //统计汇总
			 //0.统计特竞产品sku数据
			 resultMap = proccessCountPAOrder(null,null);
			 if(resultMap.get("code").equals("error"))
			 {
				 throw new Exception("统计特竞产品sku数据失败");
			 }
			 EsPAOrderInfo esPAOrderInfo = (EsPAOrderInfo) resultMap.get("esPAOrderInfo");
			 if(null!=esPAOrderInfo)
			 {
				 log.info("1----proccessCountPAOrder  esPAOrderInfo:"+esPAOrderInfo.toString());
			 }
			 
			 
			 //1.统计非特竞产品数据
			 resultMap = proccessCountHAOrder(null,null);
			 if(resultMap.get("code").equals("error"))
			 {
				 throw new Exception("统计非特竞的所有产品数据失败");
			 }
			 EsHAOrderInfo esHAOrderInfo = (EsHAOrderInfo) resultMap.get("esHAOrderInfo");
			 if(null!=esHAOrderInfo)
			 {
				 log.info("2----proccessCountHAOrder  esHAOrderInfo:"+esHAOrderInfo.toString());
			 }
			 //发送邮件
			 if(null!=esPAOrderInfo || null!=esHAOrderInfo)
			 {
				 //发送邮件
				 WebApplicationContext webApplicationContext = ContextLoader
							.getCurrentWebApplicationContext();
					ServletContext servletContext = webApplicationContext
							.getServletContext();
					servletContext.setAttribute("currentDate", currentDate);
				 exportExcel.exportDataForEsOrder(esHAOrderInfo, esPAOrderInfo, "网店管家订单统计_"+currentDate, servletContext);
				 
			 }
			
			
		}catch (Exception e) {
			
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getSaleInfoFromPPandSndEamil exception", e);
			String errorMsg = ThrowableUtil.getStackTrace(e);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
		}
		resultMap.put("code", "success");
		return resultMap;
	}
	
	
	/**
	 * 解析退换货订单
	 * <AUTHOR> 2017-4-15 下午5:42:39
	 * @param dataMap
	 * @return
	 */
	public Map<String,Object> paseSellBackOrdersResp(Map<String,Object> dataMap,String beginTime,String endTime)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//获取总页数
		String codeMsg = (String) dataMap.get("codeMsg");
		int pageTotalRecorde = Integer.parseInt(codeMsg);//当前页记录总数
		List<SellbackOrdersResp> allSellBackOrder = new ArrayList<SellbackOrdersResp>();
		if (pageTotalRecorde == 0){
			resultMap.put("lstSellBackOrdersResp", null);
			resultMap.put("code", "success");
			return resultMap;
		}else
		{
			//计算第一页
			Object responseContent = (Object) dataMap.get("responseContent");
			JSONObject result = JSONObject.fromObject(responseContent);
			JSONArray array = result.getJSONArray("sellback_orders");
			for(int i = 0; i < array.size(); i++)
			{
				JSONObject jsonObject = array.getJSONObject(i);  
				Map<String, Class<OrderPurchaseInfoResp>> map = new HashMap<String, Class<OrderPurchaseInfoResp>>(); 
		        map.put("order_infos", OrderPurchaseInfoResp.class); // key为SellbackOrdersResp私有变量的属性名 
		        SellbackOrdersResp sellbackOrdersResp = (SellbackOrdersResp) JSONObject.toBean(jsonObject, SellbackOrdersResp.class, map); 
				allSellBackOrder.add(sellbackOrdersResp); 
			}
		}
		
		if(pageTotalRecorde >= Integer.parseInt(Constants.ES_SELLBACL_PAGE_SIZE))
		{
			int i = 2;
			while(true)
			{
				resultMap  = esOrderService.getSellBackOrders(""+i,beginTime,endTime);
				log.info("----paseSellBackOrdersResp while i:"+i);
				String code1 = (String) resultMap.get("code");
				if(!"0".equals(code1))
				{
					resultMap.put("lstSellBackOrdersResp", null);
					resultMap.put("code", "error");
					return resultMap;
				}
				String codeMsg1 = (String) resultMap.get("codeMsg");
				int pageTotalRecorde1 = Integer.parseInt(codeMsg1);
				if(pageTotalRecorde1==0)
				{
					resultMap.put("lstSellBackOrdersResp", allSellBackOrder);
					resultMap.put("code", "success");
					return resultMap;
				}
				
				Object responseContent1 = (Object) resultMap.get("responseContent");
				JSONObject result1 = JSONObject.fromObject(responseContent1);
				JSONArray array1 = result1.getJSONArray("sellback_orders");
				for(int j = 0; j < array1.size(); j++)
				{
					JSONObject jsonObject1 = array1.getJSONObject(j);  
			        Map<String, Class<OrderPurchaseInfoResp>> map = new HashMap<String, Class<OrderPurchaseInfoResp>>(); 
			        map.put("order_infos", OrderPurchaseInfoResp.class); // key为SellbackOrdersResp私有变量的属性名 
			        SellbackOrdersResp sellbackOrdersResp = (SellbackOrdersResp) JSONObject.toBean(jsonObject1, SellbackOrdersResp.class, map); 
					allSellBackOrder.add(sellbackOrdersResp); 
				}
				if(pageTotalRecorde1<Integer.parseInt(Constants.ES_SELLBACL_PAGE_SIZE))
				{
					resultMap.put("lstSellBackOrdersResp", allSellBackOrder);
					resultMap.put("code", "success");
					return resultMap;
				}
				i++;
			}
		}
		resultMap.put("lstSellBackOrdersResp", allSellBackOrder);
		resultMap.put("code", "success");
		log.info("----paseSellBackOrdersResp:"+allSellBackOrder.size());
		return resultMap;
	}
	
	/**
	 * 重组退换货订单
	 * <AUTHOR> 2017-4-15 下午6:04:23
	 * @param lst
	 * @param creatTime
	 * @return
	 */
	public List<WXTEsSellBackOrderVo> ReEsSellBackOrders(List<SellbackOrdersResp> lst,Date creatTime)
	{
		List<WXTEsSellBackOrderVo> lstEsSellBackOrderVos = new ArrayList<WXTEsSellBackOrderVo>();
		//java.text.DecimalFormat   df  =new  java.text.DecimalFormat("#.00");  
		for(SellbackOrdersResp sellBackOrderResp : lst)
		{
			WXTEsSellBackOrderVo esSellBackOrder = new WXTEsSellBackOrderVo();
			esSellBackOrder.setTradeId(Integer.parseInt(sellBackOrderResp.getTrade_id()));
			esSellBackOrder.setOldId(sellBackOrderResp.getOld_id());
			esSellBackOrder.setTradeNo(sellBackOrderResp.getTrade_no());
			esSellBackOrder.setTradeStatus(Integer.parseInt(sellBackOrderResp.getTrade_status()));
			esSellBackOrder.setRegTime(DateUtils.parseDate(sellBackOrderResp.getReg_time(), 0));
			esSellBackOrder.setRawNo(sellBackOrderResp.getRaw_no());
			esSellBackOrder.setNewId(sellBackOrderResp.getNew_id());
			esSellBackOrder.setOldshopName(sellBackOrderResp.getOldshop_name());
			esSellBackOrder.setNewshopName(sellBackOrderResp.getNewshop_name());
			esSellBackOrder.setChargeTime(DateUtils.parseDate(sellBackOrderResp.getCharge_time(), 0));
			esSellBackOrder.setSeller(sellBackOrderResp.getSeller());
			esSellBackOrder.setWarehouseId(Integer.parseInt(sellBackOrderResp.getWarehouse_id()));
			esSellBackOrder.setWarehouseId2(Integer.parseInt(sellBackOrderResp.getWarehouse_id2()));
			esSellBackOrder.setLogisticId(Integer.parseInt(sellBackOrderResp.getLogistic_id()));
			esSellBackOrder.setLogisticNo(sellBackOrderResp.getLogistic_no());
			esSellBackOrder.setTotalRcv(Double.valueOf(sellBackOrderResp.getTotal_rcv().replace(",", "")));
			esSellBackOrder.setTotalPay(Double.valueOf(sellBackOrderResp.getTotal_pay().replace(",", "")));
			esSellBackOrder.setPostageTotal(Double.valueOf(sellBackOrderResp.getPostage_total().replace(",", "")));
			esSellBackOrder.setReturnTotal(Double.valueOf(sellBackOrderResp.getReturn_total().replace(",", "")));
			esSellBackOrder.setPayTotal(Double.valueOf(sellBackOrderResp.getPay_total().replace(",", "")));
			esSellBackOrder.setGoodsCost(Double.valueOf(sellBackOrderResp.getGoods_cost().replace(",", "")));
			esSellBackOrder.setPackageTotal(Double.valueOf(sellBackOrderResp.getPackage_total().replace(",", "")));
			esSellBackOrder.setTotalProfit(Double.valueOf(sellBackOrderResp.getTotal_profit().replace(",", "")));
			esSellBackOrder.setCause(sellBackOrderResp.getCause());
			esSellBackOrder.setRemark(sellBackOrderResp.getRemark());
			esSellBackOrder.setTradetype(sellBackOrderResp.getTradetype());
			esSellBackOrder.setRcvtime(DateUtils.parseDate(sellBackOrderResp.getRcvtime(), 0));
			esSellBackOrder.setCustomerid(sellBackOrderResp.getCustomerid());
			esSellBackOrder.setCustomername(sellBackOrderResp.getCustomername());
			esSellBackOrder.setNickname(sellBackOrderResp.getNickname());
			esSellBackOrder.setTel(sellBackOrderResp.getTel());
			esSellBackOrder.setAdr(sellBackOrderResp.getAdr());
			esSellBackOrder.setCreatetime(creatTime);
			esSellBackOrder.setReturntype(sellBackOrderResp.getReturntype());
			List<OrderPurchaseInfoResp>  lstOrderPurchaseInfoResp =  sellBackOrderResp.getOrder_infos();
			List<WXTEsSellBackOrderLineVo> esSellBackOrderLineVos = new ArrayList<WXTEsSellBackOrderLineVo>();
			for(OrderPurchaseInfoResp orderPurchaseInfoResp:lstOrderPurchaseInfoResp)
			{
				WXTEsSellBackOrderLineVo esSellBackOrderLineVo = new WXTEsSellBackOrderLineVo();
				esSellBackOrderLineVo.setGoodsId(orderPurchaseInfoResp.getGoods_id());
				esSellBackOrderLineVo.setSpecId(orderPurchaseInfoResp.getSpec_id());
				esSellBackOrderLineVo.setGoodsName(orderPurchaseInfoResp.getGoods_name());
				esSellBackOrderLineVo.setSellCount(orderPurchaseInfoResp.getSell_count());
				esSellBackOrderLineVo.setGoodsCode(orderPurchaseInfoResp.getGoods_code());
				esSellBackOrderLineVo.setSpecCode(orderPurchaseInfoResp.getSpec_code());
				esSellBackOrderLineVo.setSpecName(orderPurchaseInfoResp.getSpec_name());
				esSellBackOrderLineVo.setGoodsUnit(orderPurchaseInfoResp.getGoods_unit());
				esSellBackOrderLineVo.setSellPrice(orderPurchaseInfoResp.getSell_price());
				esSellBackOrderLineVo.setRemark(orderPurchaseInfoResp.getRemark());
				esSellBackOrderLineVo.setBarcode(orderPurchaseInfoResp.getBarcode());
				esSellBackOrderLineVo.setSellTotal(orderPurchaseInfoResp.getSell_total());
				esSellBackOrderLineVo.setCode(orderPurchaseInfoResp.getCode());
				esSellBackOrderLineVo.setSellbackTradeId(esSellBackOrder.getTradeId());
				esSellBackOrderLineVo.setRecno(orderPurchaseInfoResp.getRecno());
				esSellBackOrderLineVo.setCreatetime(creatTime);
				/**
				 * 添加recid/gift    记录id（唯一标识）/赠品标识   bo.liu 0808
				 */
				esSellBackOrderLineVo.setRecid(orderPurchaseInfoResp.getRecid());
				esSellBackOrderLineVo.setGift(orderPurchaseInfoResp.getGift());
				esSellBackOrderLineVos.add(esSellBackOrderLineVo);
				
				
			}
			esSellBackOrder.setLstOrderInfos(esSellBackOrderLineVos);
			lstEsSellBackOrderVos.add(esSellBackOrder);
			
		}
		return lstEsSellBackOrderVos;
	}
	
	/**
	 * 解析销售订单信息类别
	 * <AUTHOR> 2017-4-17 下午4:01:06
	 * @param dataMap
	 * @return
	 */
	public Map<String,Object> paseSaleOrdersResp(Map<String,Object> dataMap)
	{
		
		List<OrderSaleInfoResp> allSaleOrders = new ArrayList<OrderSaleInfoResp>();
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Object responseContent = (Object) dataMap.get("responseContent");
		JSONObject result = JSONObject.fromObject(responseContent);
		JSONArray array = result.getJSONArray("details");
		for(int i = 0; i < array.size(); i++)
		{
			JSONObject jsonObject = array.getJSONObject(i);  
		    JsonConfig config = new JsonConfig();
	        config.setJavaIdentifierTransformer(new JavaIdentifierTransformer() {

	            @Override
	            public String transformToJavaIdentifier(String str) {
	                char[] chars = str.toCharArray();
	                chars[0] = Character.toLowerCase(chars[0]);
	                return new String(chars);
	            }

	        });
	        
	        config.setRootClass(OrderSaleInfoResp.class);
	        OrderSaleInfoResp saleOrdersResp = (OrderSaleInfoResp)  JSONObject.toBean(jsonObject, config);
			allSaleOrders.add(saleOrdersResp); 
		}
		resultMap.put("saleOrders", allSaleOrders);
		return resultMap;
	}
	
	/**
	 * 重组销售订单
	 * <AUTHOR> 2017-4-17 下午4:54:46
	 * @param lst
	 * @param creatTime
	 * @return
	 * @throws Exception 
	 */
	public List<WXTEsSalesOrderVo> ReEsSaleOrders(List<OrderSaleInfoResp> lst,Date creatTime) throws Exception
	{
		List<WXTEsSalesOrderVo> lstSaleOrders = new ArrayList<WXTEsSalesOrderVo>();
		//add by bo.liu 0905 start
		WxtOrderYYFWSetsVo orderSetsInfo = null;
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("sourceName", WECHATSHOPNAME);
		List<WxtOrderYYFWSetsVo>  lstYYFWJD = orderYYFWSetsMapper.queryOrderYYFWInfoByMap(reqMap);
		if(null==lstYYFWJD||lstYYFWJD.isEmpty())
		{
			throw new Exception("合伙人不存在或未配置："+WECHATSHOPNAME);
		}
		orderSetsInfo = lstYYFWJD.get(0);
		
		//排序
		Collections.sort(lst, new Comparator<OrderSaleInfoResp>() {
            public int compare(OrderSaleInfoResp o1, OrderSaleInfoResp o2) {
				return o1.getSndTime().compareTo(o2.getSndTime());
            }
        });
		
		
		Collections.sort(lst, new Comparator<OrderSaleInfoResp>() {
            public int compare(OrderSaleInfoResp o1, OrderSaleInfoResp o2) {
				return o1.getWarehouseName().compareTo(o2.getWarehouseName());
            }
        });
		
		String currentWarehouse = "";
		String lastWarehouse = "";
		
		String currentSndTime = "";
		String lastSndTime = "";
		
		boolean isSameFlag = false;
		String orderNo = "";
		//add by bo.liu 0905 end
		
		
		
		for(OrderSaleInfoResp orderSaleInfoResp:lst)
		{
			currentWarehouse = orderSaleInfoResp.getWarehouseName();
			currentSndTime = DateUtils.toDateStrNew(DateUtils.parseDate(orderSaleInfoResp.getSndTime(), 3), 3) ;
			WXTEsSalesOrderVo wxtEsSalesOrderVo = new WXTEsSalesOrderVo();
			if(lastWarehouse.equals(currentWarehouse) && lastSndTime.equals(currentSndTime))
			{
				isSameFlag = true;
			}else
			{
				isSameFlag = false;
			}
			
			wxtEsSalesOrderVo.setShopId(orderSaleInfoResp.getShopID());
			wxtEsSalesOrderVo.setShopName(orderSaleInfoResp.getShopName());
			wxtEsSalesOrderVo.setTradeId(orderSaleInfoResp.getTradeID());
			wxtEsSalesOrderVo.setTradeNo(orderSaleInfoResp.getTradeNo());
			wxtEsSalesOrderVo.setTradeNoYs(orderSaleInfoResp.getTradeNoYS());
			wxtEsSalesOrderVo.setWarehouseId(orderSaleInfoResp.getWarehouseID());
			wxtEsSalesOrderVo.setWarehouseName(orderSaleInfoResp.getWarehouseName());
			if(null!=orderSaleInfoResp.getTotal() && !orderSaleInfoResp.getTotal().isEmpty())
			{
				wxtEsSalesOrderVo.setTotal(Double.parseDouble(orderSaleInfoResp.getTotal().replace(",", "")));
			}
			if(null!=orderSaleInfoResp.getAllTotal() && !orderSaleInfoResp.getAllTotal().isEmpty())
			{
				wxtEsSalesOrderVo.setAllTotal(Double.parseDouble(orderSaleInfoResp.getAllTotal().replace(",", "")));
			}
			if(null!=orderSaleInfoResp.getAllTotalShare() && !orderSaleInfoResp.getAllTotalShare().isEmpty())
			{
				wxtEsSalesOrderVo.setAllTotalShare(Double.parseDouble(orderSaleInfoResp.getAllTotalShare().replace(",", "")));
			}
			wxtEsSalesOrderVo.setProvince(orderSaleInfoResp.getProvince());
			wxtEsSalesOrderVo.setCity(orderSaleInfoResp.getCity());
			wxtEsSalesOrderVo.setTown(orderSaleInfoResp.getTown());
			wxtEsSalesOrderVo.setPostid(orderSaleInfoResp.getPostID());
			wxtEsSalesOrderVo.setTradetime(DateUtils.parseDate(orderSaleInfoResp.getTradeTime(), 0));
			wxtEsSalesOrderVo.setSndtime(DateUtils.parseDate(orderSaleInfoResp.getSndTime(), 0));
			wxtEsSalesOrderVo.setGoodsno(orderSaleInfoResp.getGoodsNO());
			wxtEsSalesOrderVo.setGoodsname(orderSaleInfoResp.getGoodsName());
			wxtEsSalesOrderVo.setGoodsspace(orderSaleInfoResp.getSpecName());
			wxtEsSalesOrderVo.setBarcode(orderSaleInfoResp.getBarCode());
			wxtEsSalesOrderVo.setNickName(orderSaleInfoResp.getNickName());
			wxtEsSalesOrderVo.setCategory(orderSaleInfoResp.getClassName());
			wxtEsSalesOrderVo.setBrand(orderSaleInfoResp.getBrand());
			wxtEsSalesOrderVo.setUnit(orderSaleInfoResp.getUnit());
			if(null!=orderSaleInfoResp.getSellCount() && !orderSaleInfoResp.getSellCount().isEmpty())
			{
				wxtEsSalesOrderVo.setCount(Double.parseDouble(orderSaleInfoResp.getSellCount().replace(",", "")));
			}
			if(null!=orderSaleInfoResp.getGoodsTotal() && !orderSaleInfoResp.getGoodsTotal().isEmpty())
			{
				wxtEsSalesOrderVo.setSalescount(Double.parseDouble(orderSaleInfoResp.getGoodsTotal().replace(",", "")));
			}
			if(null!=orderSaleInfoResp.getProfit() && !orderSaleInfoResp.getProfit().isEmpty())
			wxtEsSalesOrderVo.setGrossprofit(Double.parseDouble(orderSaleInfoResp.getProfit().replace(",", "")));
			if(null!=orderSaleInfoResp.getGoodsCost() && !orderSaleInfoResp.getGoodsCost().isEmpty())
			{
				wxtEsSalesOrderVo.setCost(Double.parseDouble(orderSaleInfoResp.getGoodsCost().replace(",", "")));
			}
			if(null!=orderSaleInfoResp.getAverageprice() && !orderSaleInfoResp.getAverageprice().isEmpty())
			{
				wxtEsSalesOrderVo.setAverageprice(Double.parseDouble(orderSaleInfoResp.getAverageprice().replace(",", "")));
			}
			wxtEsSalesOrderVo.setCreatetime(creatTime);
			/**
			 * 添加recid/gift    记录id（唯一标识）/赠品标识   bo.liu 0808
			 */
			wxtEsSalesOrderVo.setRecId(orderSaleInfoResp.getRecID());
			wxtEsSalesOrderVo.setGift(orderSaleInfoResp.getGift());
			//add by bo.liu 0905 start
			//生成订单号
			if(!isSameFlag)
			{
				String orderNoSequnceName = orderSetsInfo.getOrderNoSequnceName();
				String orderNoFlag = orderSetsInfo.getOrderNoFlag();
				reqMap.put("orderNoSequnceName", orderNoSequnceName);
				Long tmpSequenceNo = propertiesMapper.getSequenceBySequenceName(reqMap);
				String sequenceNo = CommonUtil.addZeroForStr(""+tmpSequenceNo, 6, 1);
				orderNo = CommonUtil.generateOrderCode(orderNoFlag)+sequenceNo;
			}
			wxtEsSalesOrderVo.setOrderNewNo2(orderNo);
			//add by bo.liu 0905 end
			lstSaleOrders.add(wxtEsSalesOrderVo);
			lastSndTime = currentSndTime;
			lastWarehouse = currentWarehouse;
		}
		
		return lstSaleOrders;
	}
	
	/**
	 * 初始化第一次，从网店管家拉取数据
	 * <AUTHOR> 2017-4-18 下午2:23:14
	 * @param beginDateTime
	 * @param endDateTime
	 */
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String,Object> processESOrderInit(String beginDateTime,String endDateTime)throws Exception
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//0.销售数据（从最后一次统计code时间（a） -当前日期12点58分(c)）；
			resultMap = getSalesInfo(beginDateTime, endDateTime,false);
			if(resultMap.get("code").equals("error"))
			{
				throw new Exception("获取销售订单数据失败");
				//return resultMap;
			}
		    //1.退换货数据（从最后一次统计code时间(a)--当前日期12点58分(c)）;
			resultMap = getSellBackInfo(beginDateTime, endDateTime);
			if(resultMap.get("code").equals("error"))
			{
				throw new Exception("获取退换货订单失数据失败");
			}
			
		}catch (Exception e) {
			
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getSaleInfoFromPPandSndEamil exception", e);
			String errorMsg = ThrowableUtil.getStackTrace(e);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
		}
		return resultMap;
		
	}
	
	
	/**
	 * 非第一次从网店管家扫描数据订单数据进行统计
	 * <AUTHOR> 2017-4-18 下午3:09:15
	 * @param getOrderbeginTime  获取销售订单开始时间
	 * @param getSellBackOrderBeginTime 获取退换货订单开始时间
	 * @param endDateTime 查询结束时间
	 * @return
	 * @throws Exception
	 */
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String,Object> processESOrder(String getOrderbeginTime,String getSellBackOrderBeginTime,String endDateTime)throws Exception
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//0.获取销售订单最后一个时间段的数据（当天18点-当前时间（c））
			resultMap = getSalesInfo(getOrderbeginTime, endDateTime,false);
			if(resultMap.get("code").equals("error"))
			{
				throw new Exception("获取销售订单数据失败");
				//return resultMap;
			}
		    //1.退换货数据（从最后一次统计code时间(a)--当前日期12点58分(c)）;
			resultMap = getSellBackInfo(getSellBackOrderBeginTime, endDateTime);
			if(resultMap.get("code").equals("error"))
			{
				throw new Exception("获取退换货订单失数据失败");
			}
			
		}catch (Exception e) {
			
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getSaleInfoFromPPandSndEamil exception", e);
			String errorMsg = ThrowableUtil.getStackTrace(e);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
		}
		return resultMap;
		
	}
	
	
	/**
	 * 处理统计销售订单  特竞产品的汇总
	 * <AUTHOR> 2017-4-19 下午2:08:16
	 * @param beginTime 统计开始时间  备用  默认查询所有
	 * @param endTime   统计结束时间  备用  默认查询所有
	 * @return
	 */
	public Map<String,Object> proccessCountPAOrder(String beginTime,String endTime)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			String sku = "";
			String workshopname ="";
			Map<String,Object> reqMap1 = new HashMap<String,Object>();
			reqMap1.put("codetype",Constants.ES_ORDER_TESU_SUK);
			WxTProperties properties  = propertiesMapper.selectByMap(reqMap1);
			if(null!=properties)
			{
				sku = properties.getCode();
				
			}
			if(null==sku || sku.isEmpty())
			{
				sku = MyPropertyConfigurer.getVal("es.order.count.prodsku");
			}
			
			
			reqMap1.put("codetype",Constants.ES_ORDER_WORKSHOPNAME);
			WxTProperties properties1  = propertiesMapper.selectByMap(reqMap1);
			if(null!=properties1)
			{
				workshopname = properties1.getCode();
				
			}
			if(null==workshopname || workshopname.isEmpty())
			{
				workshopname = MyPropertyConfigurer.getVal("es.order.count.workshopname");
			}
			
//			String sku = MyPropertyConfigurer.getVal("es.order.count.prodsku");
//			String workshopname = MyPropertyConfigurer.getVal("es.order.count.workshopname");
			reqMap.put("workshopname",workshopname);
			reqMap.put("sku", sku);
			//0.获取指定特竞产品的所有销售订单数据
			List<WXTEsSalesOrderVo> lstWxtEsSalesOrderVos =  esSalesOrderVoMapper.getAllWXTEsSalesOrderVoByWarehouseGroup(reqMap);
			List<EsPAOrderContentPart> lstEsPAOrderContentParts = new ArrayList<EsPAOrderContentPart>();
			
			if(null==lstWxtEsSalesOrderVos || lstWxtEsSalesOrderVos.size()==0)
			{
				log.info("0----proccessCountPAOrder  暂无特竞数据");
				resultMap.put("esPAOrderInfo", null);
				resultMap.put("code", "success");
				return resultMap;
			}
			
			Double skuCount = 0.0;//统计后的所有订单sku总数
			Double allTotalShare = 0.0;//统计后的所有订单分摊总计
			for(WXTEsSalesOrderVo wxtEsSalesOrderVo:lstWxtEsSalesOrderVos)
			{
				EsPAOrderContentPart esPAOrderContentPart = new EsPAOrderContentPart();
				esPAOrderContentPart.setWarehouseName(wxtEsSalesOrderVo.getWarehouseName());
				esPAOrderContentPart.setCount(""+accountDf.format(wxtEsSalesOrderVo.getCount()));
				esPAOrderContentPart.setAllTotalShare(""+wxtEsSalesOrderVo.getAllTotalShare());
				lstEsPAOrderContentParts.add(esPAOrderContentPart);
				skuCount+=wxtEsSalesOrderVo.getCount();
				allTotalShare+=wxtEsSalesOrderVo.getAllTotalShare();
				
			}
			//构造一条数据，汇总的数据
			EsPAOrderContentPart esPAOrderContentPart1 = new EsPAOrderContentPart();
			esPAOrderContentPart1.setWarehouseName("Grand Total");
			esPAOrderContentPart1.setCount(""+accountDf.format(skuCount));
			esPAOrderContentPart1.setAllTotalShare(""+df.format(allTotalShare));
			//lstEsPAOrderContentParts.add(esPAOrderContentPart1);
			
			//1.获取指定的特竞产品的所有退货订单数据
			WXTEsSalesOrderVo wxtRefundOrderInfo =  esSalesOrderVoMapper.getAllRefundOrderCount(reqMap);
			//退货sku总数
			Double refundCount = 0.0;
			//退货sku总计
			Double refundTotal = 0.0;
			if(null!=wxtRefundOrderInfo)
			{
				refundCount = wxtRefundOrderInfo.getCount();
				refundTotal = wxtRefundOrderInfo.getTotal();
			}
			
			
			
			
			//2.组装发送邮件的数据
			//2.1组装头部
			EsPAOrderHeadPart esPAOrderHeadPart = new EsPAOrderHeadPart();
			esPAOrderHeadPart.setSku(sku);
			esPAOrderHeadPart.setWorkshopname(workshopname);
			
			//2.2组装尾部
			List<EsPAOrderFooterPart> lstEsPAOrderFooterPart = new ArrayList<EsPAOrderFooterPart>();
			//Sales
			EsPAOrderFooterPart esPAOrderFooterPart = new EsPAOrderFooterPart();
			esPAOrderFooterPart.setTradeType("Sales");
			esPAOrderFooterPart.setQuantity(""+accountDf.format(skuCount));
			Double espaAmount = allTotalShare/1.17*0.967;
			esPAOrderFooterPart.setAmount(""+df.format(espaAmount));
			Double vat = espaAmount*0.17;
			esPAOrderFooterPart.setVat(""+df.format(vat));
			Double totalAmount = espaAmount+vat;
			esPAOrderFooterPart.setTotalAmount(""+df.format(totalAmount));
			Double listedPrice = espaAmount/skuCount;
			esPAOrderFooterPart.setListedPrice(""+df2.format(listedPrice));
			lstEsPAOrderFooterPart.add(esPAOrderFooterPart);
			
			//Refund
			EsPAOrderFooterPart esPAOrderFooterPart1 = new EsPAOrderFooterPart();
			esPAOrderFooterPart1.setTradeType("Refund");
			esPAOrderFooterPart1.setQuantity("-"+accountDf.format(refundCount));
			Double espaAmount1 = refundTotal/1.17*0.992;//417/1.17*(1-0.8%)
			esPAOrderFooterPart1.setAmount("-"+df.format(espaAmount1));
			Double vat1 = espaAmount1*0.17;
			esPAOrderFooterPart1.setVat("-"+df.format(vat1));
			Double totalAmount1 = espaAmount1+vat1;
			esPAOrderFooterPart1.setTotalAmount("-"+df.format(totalAmount1));
			lstEsPAOrderFooterPart.add(esPAOrderFooterPart1);
			
			//Total
			EsPAOrderFooterPart esPAOrderFooterPart2 = new EsPAOrderFooterPart();
			esPAOrderFooterPart2.setTradeType("Total");
			esPAOrderFooterPart2.setQuantity(""+accountDf.format((skuCount-refundCount)));
			Double espaAmount2 = espaAmount-espaAmount1;
			esPAOrderFooterPart2.setAmount(""+df.format(espaAmount2));
			Double vat2 = espaAmount2*0.17;
			esPAOrderFooterPart2.setVat(""+df.format(vat2));
			Double totalAmount2 = espaAmount2+vat2;
			esPAOrderFooterPart2.setTotalAmount(""+df.format(totalAmount2));
			Double listedPrice2 = espaAmount2/(skuCount-refundCount);
			esPAOrderFooterPart2.setListedPrice(""+df2.format(listedPrice2));
			lstEsPAOrderFooterPart.add(esPAOrderFooterPart2);
			
			//2.3组装内容页
			//实际收益对应的系数是
			Double actVar = listedPrice2;
			List<EsPAOrderContentPart> lstEsPAOrderContentPartsNew = new ArrayList<EsPAOrderContentPart>();
			for(EsPAOrderContentPart exContentPart : lstEsPAOrderContentParts)
			{
				exContentPart.setActulAmount(""+df1.format(((Double.parseDouble(exContentPart.getCount()))*actVar)));
				lstEsPAOrderContentPartsNew.add(exContentPart);
			}
			lstEsPAOrderContentPartsNew.add(esPAOrderContentPart1);
			
			
			EsPAOrderInfo esPAOrderInfo = new EsPAOrderInfo();
			esPAOrderInfo.setEsPAOrderHeadPart(esPAOrderHeadPart);
			esPAOrderInfo.setLstEsPAOrderContentPart(lstEsPAOrderContentPartsNew);
			esPAOrderInfo.setLstEsPAOrderFooterPart(lstEsPAOrderFooterPart);
			
			resultMap.put("esPAOrderInfo", esPAOrderInfo);
			resultMap.put("code", "success");
			
			
		}catch (Exception e) {
			
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("proccessCountPAOrder exception", e);
			String errorMsg = ThrowableUtil.getStackTrace(e);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
		}
		return resultMap;
		
	}
	
	
	
	/**
	 * 处理统计销售订单  非特竞产品的汇总
	 * <AUTHOR> 2017-4-19 下午2:08:16
	 * @param beginTime 统计开始时间  备用  默认查询所有
	 * @param endTime   统计结束时间  备用  默认查询所有
	 * @return
	 */
	public Map<String,Object> proccessCountHAOrder(String beginTime,String endTime)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			String sku = "";
			String workshopname ="";
			Map<String,Object> reqMap1 = new HashMap<String,Object>();
			reqMap1.put("codetype",Constants.ES_ORDER_TESU_SUK);
			WxTProperties properties  = propertiesMapper.selectByMap(reqMap1);
			if(null!=properties)
			{
				sku = properties.getCode();
				
			}
			if(null==sku || sku.isEmpty())
			{
				sku = MyPropertyConfigurer.getVal("es.order.count.prodsku");
			}
			
			
			reqMap1.put("codetype",Constants.ES_ORDER_WORKSHOPNAME);
			WxTProperties properties1  = propertiesMapper.selectByMap(reqMap1);
			if(null!=properties1)
			{
				workshopname = properties1.getCode();
				
			}
			if(null==workshopname || workshopname.isEmpty())
			{
				workshopname = MyPropertyConfigurer.getVal("es.order.count.workshopname");
			}
			reqMap.put("workshopname",workshopname);
			reqMap.put("sku", sku);
			//0.获取非特竞产品的所有销售订单数据
			List<WXTEsSalesOrderVo> lstWxtEsSalesOrderVos =  esSalesOrderVoMapper.getAllWXTEsSalesOrderVoNotContainTJ(reqMap);
			List<EsHAOrderContentPart> lstEsHAOrderContentParts = new ArrayList<EsHAOrderContentPart>();
			
			if(null==lstWxtEsSalesOrderVos || lstWxtEsSalesOrderVos.size()==0)
			{
				log.info("0----proccessCountHAOrder  暂无销售数据");
				resultMap.put("esHAOrderInfo", null);
				resultMap.put("code", "success");
				return resultMap;
			}
			
			Double skuCount = 0.0;//统计后的所有订单sku总数
			Double allTotalShare = 0.0;//统计后的所有订单分摊总计
			for(WXTEsSalesOrderVo wxtEsSalesOrderVo:lstWxtEsSalesOrderVos)
			{
				EsHAOrderContentPart esHAOrderContentPart = new EsHAOrderContentPart();
				esHAOrderContentPart.setWarehouseName(wxtEsSalesOrderVo.getWarehouseName());
				esHAOrderContentPart.setCount(""+accountDf.format(wxtEsSalesOrderVo.getCount()));
				esHAOrderContentPart.setAllTotalShare(""+wxtEsSalesOrderVo.getAllTotalShare());
				if(wxtEsSalesOrderVo.getCategory1().equals("礼品"))
				{
					esHAOrderContentPart.setCategory("礼品");
				}else
				{
					esHAOrderContentPart.setCategory(wxtEsSalesOrderVo.getCategory());
				}
				esHAOrderContentPart.setGoodsno(wxtEsSalesOrderVo.getGoodsno());
				esHAOrderContentPart.setEndCharType(wxtEsSalesOrderVo.getEndcharType());
				
				lstEsHAOrderContentParts.add(esHAOrderContentPart);
				skuCount+=wxtEsSalesOrderVo.getCount();
				allTotalShare+=wxtEsSalesOrderVo.getAllTotalShare();
				
			}
			//构造一条数据，汇总的数据
			EsHAOrderContentPart esHAOrderContentPart1 = new EsHAOrderContentPart();
			esHAOrderContentPart1.setWarehouseName("Grand Total");
			esHAOrderContentPart1.setCount(""+accountDf.format(skuCount));
			esHAOrderContentPart1.setAllTotalShare(""+df.format(allTotalShare));
			esHAOrderContentPart1.setCategory("");
			esHAOrderContentPart1.setGoodsno("");
			esHAOrderContentPart1.setLitterVolume("100.00%");
			//lstEsPAOrderContentParts.add(esPAOrderContentPart1);
			
			//1.获取非指定的特竞产品的所有退货订单数据
			WXTEsSalesOrderVo wxtRefundOrderInfo =  esSalesOrderVoMapper.getAllRefundOrderCountNotContainTJ(reqMap);
			//退货sku总数
			Double refundCount = 0.0;
			//退货sku总计
			Double refundTotal = 0.0;
			if(null!=wxtRefundOrderInfo)
			{
				refundCount = wxtRefundOrderInfo.getCount();
				refundTotal = wxtRefundOrderInfo.getTotal();
			}
			
			
			
			
			//2.组装发送邮件的数据
			//2.1组装头部
			EsHAOrderHeadPart esHAOrderHeadPart = new EsHAOrderHeadPart();
			esHAOrderHeadPart.setWorkshopname(workshopname);
			
			//2.2组装尾部
			List<EsHAOrderFooterPart> lstEsHAOrderFooterPart = new ArrayList<EsHAOrderFooterPart>();
			//Sales
			EsHAOrderFooterPart esHAOrderFooterPart = new EsHAOrderFooterPart();
			esHAOrderFooterPart.setTradeType("Sales");
			esHAOrderFooterPart.setQuantity(""+accountDf.format(skuCount));
			Double eshaAmount = allTotalShare/1.17*0.967;
			esHAOrderFooterPart.setAmount(""+df.format(eshaAmount));
			Double vat = eshaAmount*0.17;
			esHAOrderFooterPart.setVat(""+df.format(vat));
			Double totalAmount = eshaAmount+vat;
			esHAOrderFooterPart.setTotalAmount(""+df.format(totalAmount));
			Double listedPrice = eshaAmount/skuCount;
			esHAOrderFooterPart.setListedPrice(""+df2.format(listedPrice));
			lstEsHAOrderFooterPart.add(esHAOrderFooterPart);
			
			//Refund
			EsHAOrderFooterPart esHAOrderFooterPart1 = new EsHAOrderFooterPart();
			esHAOrderFooterPart1.setTradeType("Refund");
			esHAOrderFooterPart1.setQuantity("-"+refundCount);
			Double eshaAmount1 = refundTotal/1.17*0.992;//417/1.17*(1-0.8%)
			esHAOrderFooterPart1.setAmount("-"+df.format(eshaAmount1));
			Double vat1 = eshaAmount1*0.17;
			esHAOrderFooterPart1.setVat("-"+df.format(vat1));
			Double totalAmount1 = eshaAmount1+vat1;
			esHAOrderFooterPart1.setTotalAmount("-"+df.format(totalAmount1));
			lstEsHAOrderFooterPart.add(esHAOrderFooterPart1);
			
			//Total
			EsHAOrderFooterPart esHAOrderFooterPart2 = new EsHAOrderFooterPart();
			esHAOrderFooterPart2.setTradeType("Total");
			esHAOrderFooterPart2.setQuantity(""+(skuCount-refundCount));
			Double eshaAmount2 = eshaAmount-eshaAmount1;
			esHAOrderFooterPart2.setAmount(""+df.format(eshaAmount2));
			Double vat2 = eshaAmount2*0.17;
			esHAOrderFooterPart2.setVat(""+df.format(vat2));
			Double totalAmount2 = eshaAmount2+vat2;
			esHAOrderFooterPart2.setTotalAmount(""+df.format(totalAmount2));
			Double listedPrice2 = eshaAmount2/(skuCount-refundCount);
			esHAOrderFooterPart2.setListedPrice(""+df2.format(listedPrice2));
			lstEsHAOrderFooterPart.add(esHAOrderFooterPart2);
			
			//2.3组装内容页
			//百分比数据
			List<EsHAOrderContentPart> lstEsHAOrderContentPartsNew = new ArrayList<EsHAOrderContentPart>();
			String lastWareHouse = "";
			String lastCategory="";	
			String lastEndCharType="";
			
			String currentWareHouse = "";
			String currentCategory = "";
			String currentEndCharType="";
			
			EsHAOrderContentPart  lastEsHAOrderContentPart = null;
			EsHAOrderContentPart  currentEsHAOrderContentPart = null;
			
			int i=1;//控制当前
			int j=0;//控制总记录条数
			int size = lstEsHAOrderContentParts.size();//总记录条数
			NumberFormat numberFormat = NumberFormat.getInstance();  
	        // 设置精确到小数点后2位  
	        numberFormat.setMaximumFractionDigits(2);  
			for(EsHAOrderContentPart exContentPart : lstEsHAOrderContentParts)
			{
				j=j+1;
				currentWareHouse = exContentPart.getWarehouseName();
				currentCategory = exContentPart.getCategory();
				currentEndCharType = exContentPart.getEndCharType();
				currentEsHAOrderContentPart = exContentPart;
				if(lastWareHouse.equals(currentWareHouse) &&currentCategory.equals(lastCategory) && (currentEndCharType.endsWith("LPK01A")||currentEndCharType.endsWith("NJK01A")
						||currentEndCharType.endsWith("LPK")||currentEndCharType.endsWith("NJK")) )
				{
					if(i%2==0)
					{
						if(lastEsHAOrderContentPart.getGoodsno().contains("LPK01A") && currentEsHAOrderContentPart.getGoodsno().contains("NJK01A"))
						{
							Double  lastBiliValue = Double.valueOf(lastEsHAOrderContentPart.getCount())*4;
							Double  currentBiliValue = Double.valueOf(currentEsHAOrderContentPart.getCount());
							Double  totatlBiliValue = lastBiliValue+currentBiliValue;
							lastEsHAOrderContentPart.setLitterVolume(""+df.format(lastBiliValue/totatlBiliValue*100)+"%");
							currentEsHAOrderContentPart.setLitterVolume(""+df.format(currentBiliValue/totatlBiliValue*100)+"%");
						}else if(lastEsHAOrderContentPart.getGoodsno().contains("NJK01A") && currentEsHAOrderContentPart.getGoodsno().contains("LPK01A"))
						{
							Double  lastBiliValue = Double.valueOf(lastEsHAOrderContentPart.getCount());
							Double  currentBiliValue = Double.valueOf(currentEsHAOrderContentPart.getCount())*4;
							Double  totatlBiliValue = lastBiliValue+currentBiliValue;
							lastEsHAOrderContentPart.setLitterVolume(""+df.format(lastBiliValue/totatlBiliValue*100)+"%");
							currentEsHAOrderContentPart.setLitterVolume(""+df.format(currentBiliValue/totatlBiliValue*100)+"%");
						}
						
						else if(lastEsHAOrderContentPart.getGoodsno().endsWith("LPK") && currentEsHAOrderContentPart.getGoodsno().endsWith("NJK"))
						{
							Double  lastBiliValue = Double.valueOf(lastEsHAOrderContentPart.getCount())*4;
							Double  currentBiliValue = Double.valueOf(currentEsHAOrderContentPart.getCount());
							Double  totatlBiliValue = lastBiliValue+currentBiliValue;
							lastEsHAOrderContentPart.setLitterVolume(""+df.format(lastBiliValue/totatlBiliValue*100)+"%");
							currentEsHAOrderContentPart.setLitterVolume(""+df.format(currentBiliValue/totatlBiliValue*100)+"%");
						}else if(lastEsHAOrderContentPart.getGoodsno().endsWith("NJK") && currentEsHAOrderContentPart.getGoodsno().endsWith("LPK"))
						{
							Double  lastBiliValue = Double.valueOf(lastEsHAOrderContentPart.getCount());
							Double  currentBiliValue = Double.valueOf(currentEsHAOrderContentPart.getCount())*4;
							Double  totatlBiliValue = lastBiliValue+currentBiliValue;
							lastEsHAOrderContentPart.setLitterVolume(""+df.format(lastBiliValue/totatlBiliValue*100)+"%");
							currentEsHAOrderContentPart.setLitterVolume(""+df.format(currentBiliValue/totatlBiliValue*100)+"%");
						}
						lstEsHAOrderContentPartsNew.add(lastEsHAOrderContentPart);
						lstEsHAOrderContentPartsNew.add(currentEsHAOrderContentPart);
					}
					
					
				}else
				{
					if(i%2==0)
					{
						lastEsHAOrderContentPart.setLitterVolume("100.00%");
						lstEsHAOrderContentPartsNew.add(lastEsHAOrderContentPart);
						i=1;
					}
					lastEsHAOrderContentPart = currentEsHAOrderContentPart;
					
				}
				i=i+1;
				lastWareHouse = exContentPart.getWarehouseName();
				lastCategory = exContentPart.getCategory();
				lastEndCharType = exContentPart.getEndCharType();
				
				if(j==size && i%2==0)
				{
					lastEsHAOrderContentPart.setLitterVolume("100.00%");
					lstEsHAOrderContentPartsNew.add(lastEsHAOrderContentPart);
				}
				
				
//				exContentPart.setLitterVolume("100.00%");
//				lstEsHAOrderContentPartsNew.add(exContentPart);
			}
			lstEsHAOrderContentPartsNew.add(esHAOrderContentPart1);
			
			
			EsHAOrderInfo esHAOrderInfo = new EsHAOrderInfo();
			esHAOrderInfo.setesHAOrderHeadPart(esHAOrderHeadPart);
			esHAOrderInfo.setLstEsHAOrderContentPart(lstEsHAOrderContentPartsNew);
			esHAOrderInfo.setLstEsHAOrderFooterPart(lstEsHAOrderFooterPart);
			
			resultMap.put("esHAOrderInfo", esHAOrderInfo);
			resultMap.put("code", "success");
			
			
		}catch (Exception e) {
			
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("proccessCountHAOrder exception", e);
			String errorMsg = ThrowableUtil.getStackTrace(e);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
		}
		return resultMap;
		
	}
	
	/**
	 * 获取出库订单信息
	 * <AUTHOR> 2017-7-7  下午5:42:39
	 * @param dataMap
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> getStockOutOrderInfo(String beginTime,
			String endTime) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Date createDate = new Date();
		resultMap.put("code", "success");
		try
		{
			if(null==beginTime || beginTime.isEmpty())
			{
				beginTime = esOrderService.getLastTimeAsBeginTime(Constants.ES_GET_OUTSTOCK_ORDER_LAST_TIME);
			}
			
			if(null==endTime || endTime.isEmpty())
			{
				 Date scanEndDate = DateUtil.addDays(createDate, -1);
				 endTime = DateUtil.getDateStr(scanEndDate, "yyyy-MM-dd");
			}
			log.info("----getStockOutOrderInfo  init beginTime:"+beginTime+"  endTime:"+endTime);
			log.info("----getStockOutOrderInfo  0："+DateUtils.toDateStrNew(createDate, 0));
			//0.获取销售出库单数据
			resultMap  = esOrderService.getStockoutOrders("1",beginTime,endTime);
			String code = (String) resultMap.get("code");
			if(!"0".equals(code))
			{
				resultMap.put("code", "error");
				resultMap.put("msg", "拉取网店管家出库单数据失败，拉取时间："+DateUtils.toDateStrNew(createDate, 0));
				log.info("----getStockOutOrderInfo  拉取网店管家出库单数据失败，拉取时间："+DateUtils.toDateStrNew(createDate, 0));
				return resultMap;
			}
			//1.解析出库单信息列表
			Map<String,Object> dataMap = paseStockoutsOrderResp(resultMap,beginTime,endTime);
			List<StockoutsOrderResp> allStockoutsOrder = (List<StockoutsOrderResp>) dataMap.get("lstStockoutsOrderResp");
			if(null==allStockoutsOrder)
			{
				if(null!=dataMap.get("code") && dataMap.get("code").equals("error"))
				{
					throw new Exception("获取获取出库订单信息数据失败");
				}
				return dataMap;
			}
			
			//2.重组销售出库订单信息、货品详细信息
			List<WXTEsStockoutOrder> lstEsStockoutOrders = ReEsStockoutOrders(allStockoutsOrder,createDate);
			List<WXTEsStockoutOrderInfo> lstEsStockoutOrderLineVos = new ArrayList<WXTEsStockoutOrderInfo>();
			for(WXTEsStockoutOrder wxtEsStockoutOrder:lstEsStockoutOrders)
			{
				lstEsStockoutOrderLineVos.addAll(wxtEsStockoutOrder.getLstStockoutOrderInfos());
			}
			
			//3.分页插入出库订单
			log.info("----getStockOutOrderInfo  1："+DateUtils.toDateStrNew(createDate, 0));
			ImportDataPageModelUtil esStockoutOrderPages = new ImportDataPageModelUtil(
					lstEsStockoutOrders, 50);
			int es_stockoutorder_page_account = esStockoutOrderPages.getTotalPages();
			for (int i = 1; i <= es_stockoutorder_page_account; i++) {
				List<WXTEsStockoutOrder> lstStockoutOrder= esStockoutOrderPages
						.getObjects(i);
				esStockoutOrderMapper.insertBatchSelective(lstStockoutOrder);
			}
			
			//4.分页插入出库商品详情
			log.info("----getStockOutOrderInfo  2："+DateUtils.toDateStrNew(createDate, 0));
			ImportDataPageModelUtil esStockoutOrderLinePages = new ImportDataPageModelUtil(
					lstEsStockoutOrderLineVos, 50);
			int es_stockoutorder_line_page_account = esStockoutOrderLinePages.getTotalPages();
			for (int i = 1; i <= es_stockoutorder_line_page_account; i++) {
				List<WXTEsStockoutOrderInfo> lstStockoutOrderLine = esStockoutOrderLinePages
						.getObjects(i);
				esStockoutOrderInfoMapper.insertBatchSelective(lstStockoutOrderLine);
				
			}
			
			// 更新下次获取时间
			esOrderService.updateLatestBeginTime(DateUtil.getDateStr(createDate, "yyyy-MM-dd"), Constants.ES_GET_OUTSTOCK_ORDER_LAST_TIME);
			
			
			//5. 关联查询订单，从订单表中，，，查找到订单号匹配的订单,并组装
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("createTime", DateUtils.toDateStrNew(createDate, 3));
			//reqMap.put("createTime", "2017-07-07");
			List<OutStockOrder> lstOutStockOrders = esStockoutOrderMapper.selectStockOutOrder(reqMap);
			if(null==lstOutStockOrders || lstOutStockOrders.isEmpty())
			{
				resultMap.put("msg", "无符合条件的出库单录入到PP系统,时间："+DateUtils.toDateStrNew(createDate, 0));
				log.info("----getStockOutOrderInfo  无符合条件的出库单录入到PP系统，时间："+DateUtils.toDateStrNew(createDate, 0));
				return resultMap;
			}
			
			
			//6. 录入到pp系统出库单表和出库单详情表
			//录入出库单表
			log.info("----getStockOutOrderInfo  3："+DateUtils.toDateStrNew(createDate, 0));
			ImportDataPageModelUtil OutStockOrderPages = new ImportDataPageModelUtil(
					lstOutStockOrders, 100);
			int outstock_order_page_account = OutStockOrderPages.getTotalPages();
			for (int i = 1; i <= outstock_order_page_account; i++) {
				List<OutStockOrder> lstOutStockOrder= OutStockOrderPages
						.getObjects(i);
				outStockMapper.insertBatchSelective(lstOutStockOrder);
			}
			
			//录入详情
			List<OutStockOrder> lstOutStockOrderInfos = esStockoutOrderMapper.selectStockOutOrderInfo(reqMap);
			List<OutStockOrderInfo> lstOutStockOrderInfo = new ArrayList<OutStockOrderInfo>();
			List<PartnerInventoryOutRecord> lstPartnerInventoryOutRecord = new ArrayList<PartnerInventoryOutRecord>();
			for(OutStockOrder outStockOrder:lstOutStockOrderInfos)
			{
				String sidcode = outStockOrder.getCode();
				String stockOutNo = outStockOrder.getStockOutNo();
				String codeType = outStockOrder.getCodeType();
				Date scanDate = outStockOrder.getOutTime();
				if(null==sidcode || sidcode.isEmpty())
				{
					continue;
				}
				String[] codes = sidcode.split(",");
				for(String sid:codes)
				{
					OutStockOrderInfo outStockOrderInfo = new OutStockOrderInfo();
					outStockOrderInfo.setCode(sid);
					outStockOrderInfo.setCodeType(codeType);
					outStockOrderInfo.setScanTime(scanDate);
					outStockOrderInfo.setStockOutNo(stockOutNo);
					lstOutStockOrderInfo.add(outStockOrderInfo);
					
					//插入网点管家的出库
					PartnerInventoryOutRecord record = new PartnerInventoryOutRecord();
					record.setStockOutNo(stockOutNo);
					record.setCode(sid);
					record.setCodeType(codeType);
					record.setPartnerId(null);
					record.setPartnerName("wdgj");
					record.setCreateTime(scanDate);
					lstPartnerInventoryOutRecord.add(record);
				}
			}
			if(null==lstOutStockOrderInfo || lstOutStockOrderInfo.isEmpty())
			{
				resultMap.put("msg", "无出库订单详情录入PP系统,时间："+DateUtils.toDateStrNew(createDate, 0));
				log.info("----getStockOutOrderInfo  无出库订单详情录入PP系统，时间："+DateUtils.toDateStrNew(createDate, 0));
				return resultMap;
				//throw new Exception("无出库订单详情录入PP系统,时间："+DateUtils.toDateStrNew(createDate, 0));
			}
			log.info("----getStockOutOrderInfo  4："+DateUtils.toDateStrNew(createDate, 0));
			ImportDataPageModelUtil OutStockOrderInfoPages = new ImportDataPageModelUtil(
					lstOutStockOrderInfo, 200);
			int outstock_orderinfo_page_account = OutStockOrderInfoPages.getTotalPages();
			for (int i = 1; i <= outstock_orderinfo_page_account; i++) {
				List<OutStockOrderInfo> lstTmpOutStockOrderInfo= OutStockOrderInfoPages
						.getObjects(i);
				outStockLineMapper.insertBatchSelective(lstTmpOutStockOrderInfo);
			}
			
			ImportDataPageModelUtil partnerInventoryOutRecordPages = new ImportDataPageModelUtil(
					lstPartnerInventoryOutRecord, 200);
			int partnerInventoryOutRecord_page_account = partnerInventoryOutRecordPages.getTotalPages();
			for (int i = 1; i <= partnerInventoryOutRecord_page_account; i++) {
				List<PartnerInventoryOutRecord> tempListPartnerInventoryOutRecord= partnerInventoryOutRecordPages
						.getObjects(i);
				partnerInventoryOutRecordMapper.insertBatch(tempListPartnerInventoryOutRecord);
			}
			
			// 更新下次获取时间
			//esOrderService.updateLatestBeginTime(DateUtil.getDateStr(createDate, "yyyy-MM-dd"), Constants.ES_GET_OUTSTOCK_ORDER_LAST_TIME);
			resultMap.put("msg", "录入出库单相关信息，操作成功，录入开始时间："+DateUtils.toDateStrNew(createDate, 0));
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getStockOutOrderInfo exception", ex);
			String errorMsg = ThrowableUtil.getStackTrace(ex);
			resultMap.put("code", "error");
			resultMap.put("msg", "异常信息："+errorMsg);
			return resultMap;
		}
		return resultMap;
	}
	
	/**
	 * 解析出库订单
	 * <AUTHOR> 2017-7-7  下午5:42:39
	 * @param dataMap
	 * @return
	 */
	public Map<String,Object> paseStockoutsOrderResp(Map<String,Object> dataMap,String beginTime,String endTime)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//获取当前页总页数
		String codeMsg = (String) dataMap.get("codeMsg");
		int pageTotalRecorde = Integer.parseInt(codeMsg);
		List<StockoutsOrderResp> allStockoutsOrderResp = new ArrayList<StockoutsOrderResp>();
		if (pageTotalRecorde ==0 ){
			resultMap.put("lstStockoutsOrderResp", null);
			resultMap.put("code", "success");
			return resultMap;
		}else 
		{
			//计算第一页
			Object responseContent = (Object) dataMap.get("responseContent");
			JSONObject result = JSONObject.fromObject(responseContent);
			JSONArray array = result.getJSONArray("stockouts");
			for(int i = 0; i < array.size(); i++)
			{
				JSONObject jsonObject = array.getJSONObject(i);  
				Map<String, Class<StockoutsOrderInfoResp>> map = new HashMap<String, Class<StockoutsOrderInfoResp>>(); 
		        map.put("order_infos", StockoutsOrderInfoResp.class); // key为StockoutsOrderResp私有变量的属性名 
		        StockoutsOrderResp stockoutsOrderResp = (StockoutsOrderResp) JSONObject.toBean(jsonObject, StockoutsOrderResp.class, map); 
		        allStockoutsOrderResp.add(stockoutsOrderResp); 
			}
		}
		if(pageTotalRecorde >= Integer.parseInt(Constants.ES_SELLBACL_PAGE_SIZE))
		{
		
			int i = 2;
			while(true)
			{
				resultMap  = esOrderService.getStockoutOrders(""+i,beginTime,endTime);
				log.info("====-----paseStockoutsOrderResp while:"+i);
				String code1 = (String) resultMap.get("code");
				if(!"0".equals(code1))
				{
					log.info("====-----paseStockoutsOrderResp error i:"+i);
					resultMap.put("lstStockoutsOrderResp", null);
					resultMap.put("code", "error");
					return resultMap;
				}
				String codeMsg1 = (String) resultMap.get("codeMsg");
				int pageTotalRecorde1 = Integer.parseInt(codeMsg1);
				if(pageTotalRecorde1==0)
				{
					resultMap.put("lstStockoutsOrderResp", allStockoutsOrderResp);
					resultMap.put("code", "success");
					return resultMap;
				}
				
				Object responseContent1 = (Object) resultMap.get("responseContent");
				JSONObject result1 = JSONObject.fromObject(responseContent1);
				JSONArray array1 = result1.getJSONArray("stockouts");
				for(int j = 0; j < array1.size(); j++)
				{
					JSONObject jsonObject1 = array1.getJSONObject(j);  
					
			        Map<String, Class<StockoutsOrderInfoResp>> map = new HashMap<String, Class<StockoutsOrderInfoResp>>(); 
			        map.put("order_infos", StockoutsOrderInfoResp.class); // key为StockoutsOrderResp私有变量的属性名 
			        StockoutsOrderResp stockoutsOrderResp = (StockoutsOrderResp) JSONObject.toBean(jsonObject1, StockoutsOrderResp.class, map); 
			        allStockoutsOrderResp.add(stockoutsOrderResp); 
				}
				if(pageTotalRecorde1<Integer.parseInt(Constants.ES_SELLBACL_PAGE_SIZE))
				{
					resultMap.put("lstStockoutsOrderResp", allStockoutsOrderResp);
					resultMap.put("code", "success");
					return resultMap;
				}
				i++;
			}
		}
		resultMap.put("lstStockoutsOrderResp", allStockoutsOrderResp);
		resultMap.put("code", "success");
		log.info("-----paseStockoutsOrderResp.size():"+allStockoutsOrderResp.size());
		return resultMap;
	}
	
	
	/**
	 * 重组销售出库单数据
	 * <AUTHOR> 2017-7-7 下午6:04:23
	 * @param lst
	 * @param creatTime
	 * @return
	 */
	public List<WXTEsStockoutOrder> ReEsStockoutOrders(List<StockoutsOrderResp> lst,Date creatTime)
	{
		List<WXTEsStockoutOrder> lstEsStockoutOrders = new ArrayList<WXTEsStockoutOrder>();
		for(StockoutsOrderResp stockoutsOrderResp : lst)
		{
			WXTEsStockoutOrder esStockoutOrder = new WXTEsStockoutOrder();
			esStockoutOrder.setStockoutId(stockoutsOrderResp.getStockout_id());
			esStockoutOrder.setStockoutNo(stockoutsOrderResp.getStockout_no());
			esStockoutOrder.setWarehouseId(stockoutsOrderResp.getWarehouse_id());
			esStockoutOrder.setOutTime(DateUtils.parseDate(stockoutsOrderResp.getOut_time(), 0));
			esStockoutOrder.setOperatorPers(stockoutsOrderResp.getOperator_pers());
			esStockoutOrder.setGoodsTotal(Double.valueOf(stockoutsOrderResp.getGoods_total().replace(",", "")));
			esStockoutOrder.setOtherFee(Double.valueOf(stockoutsOrderResp.getGoods_total().replace(",", "")));
			esStockoutOrder.setStatus(stockoutsOrderResp.getStatus());
			esStockoutOrder.setOrdertype(stockoutsOrderResp.getOrdertype());
			esStockoutOrder.setSummary(stockoutsOrderResp.getSummary());
			esStockoutOrder.setChkTime(DateUtils.parseDate(stockoutsOrderResp.getChk_time(), 0));
			esStockoutOrder.setChkOperator(stockoutsOrderResp.getChk_operator());
			esStockoutOrder.setCorrelationid(stockoutsOrderResp.getCorrelationid());
			esStockoutOrder.setOperationtype(stockoutsOrderResp.getOperationtype());
			esStockoutOrder.setRemark(stockoutsOrderResp.getRemark());
			esStockoutOrder.setWriteoffid(stockoutsOrderResp.getWriteoffid());
			esStockoutOrder.setTradeno2(stockoutsOrderResp.getTradeno2());
			esStockoutOrder.setCreatetime(creatTime);
			List<StockoutsOrderInfoResp>  lstStockoutsOrderInfoResp =  stockoutsOrderResp.getOrder_infos();
			List<WXTEsStockoutOrderInfo> esStockoutsOrderInfos = new ArrayList<WXTEsStockoutOrderInfo>();
			for(StockoutsOrderInfoResp stockoutsOrderInfoResp :lstStockoutsOrderInfoResp)
			{
				WXTEsStockoutOrderInfo esStockoutsOrderInfo = new WXTEsStockoutOrderInfo();
				esStockoutsOrderInfo.setGoodsId(stockoutsOrderInfoResp.getGoods_id());
				esStockoutsOrderInfo.setGoodsCode(stockoutsOrderInfoResp.getGoods_code());
				esStockoutsOrderInfo.setGoodsName(stockoutsOrderInfoResp.getGoods_name());
				esStockoutsOrderInfo.setSpecId(stockoutsOrderInfoResp.getSpec_id());
				esStockoutsOrderInfo.setSpecCode(stockoutsOrderInfoResp.getSpec_code());
				esStockoutsOrderInfo.setSpecName(stockoutsOrderInfoResp.getSpec_name());
				esStockoutsOrderInfo.setGoodsUnit(stockoutsOrderInfoResp.getGoods_unit());
				esStockoutsOrderInfo.setSellCount(Double.valueOf(stockoutsOrderInfoResp.getSell_count().replace(",", "")));
				esStockoutsOrderInfo.setSellPrice(Double.valueOf(stockoutsOrderInfoResp.getSell_price().replace(",", "")));
				esStockoutsOrderInfo.setSellTotal(Double.valueOf(stockoutsOrderInfoResp.getSell_total().replace(",", "")));
				esStockoutsOrderInfo.setRemark(stockoutsOrderInfoResp.getRemark());	
				esStockoutsOrderInfo.setSid(stockoutsOrderInfoResp.getSid());
				esStockoutsOrderInfo.setStockoutId(esStockoutOrder.getStockoutId());
				esStockoutsOrderInfo.setStockoutNo(esStockoutOrder.getStockoutNo());
				esStockoutsOrderInfo.setCreatetime(creatTime);
				
				esStockoutsOrderInfos.add(esStockoutsOrderInfo);
			}
			esStockoutOrder.setLstStockoutOrderInfos(esStockoutsOrderInfos);
			lstEsStockoutOrders.add(esStockoutOrder);
			
		}
		return lstEsStockoutOrders;
	}
	
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> getAvratoOrders(String beginTime, String endTime)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Date createDate = new Date();
		Date scanDate = DateUtil.addDays(createDate, -1);
		resultMap.put("code", "success");
		try
		{
			if(null==beginTime || beginTime.isEmpty())
			{
				beginTime = esOrderService.getLastTimeAsBeginTime(Constants.ES_GET_AVRATO_ORDER_LAST_TIME);
			}
			
			if(null==beginTime||beginTime.isEmpty())
			{
				beginTime = DateUtil.getDateStr(scanDate, "yyyy-MM-dd");
			}
			
			if(null==endTime || endTime.isEmpty())
			{
				 endTime = DateUtil.getDateStr(scanDate, "yyyy-MM-dd");
			}
			//获取订单，并插入到数据库中
			Map<String,Object> returnMap = getSalesInfo(beginTime, endTime, false);
			if(returnMap.get("code").equals("error"))
			{
				throw new Exception("获取AvratoOrder订单数据失败");
			}
			String saleacount = (String)returnMap.get("saleacount");
			if(null!=saleacount && saleacount.equals("0"))
			{
				throw new Exception("无有效的AvratoOrder订单数据");
			}
			//查询数据，用组装数据，然后生成目标文件
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("queryDateTime", DateUtil.getDateStr(createDate, "yyyy-MM-dd"));
			List<HaOrderUploadView> lstOrderView =  esSalesOrderVoMapper.getNeedUploadHaWechatOrders(reqMap);
			if(null==lstOrderView || lstOrderView.isEmpty())
			{
				throw new Exception("要上传的订单信息丢失或没有找到");
			}
			
			
			//TODO 准备基础数据 1019
			/**
			 * 基础数据准备，登录用户，订单来源查询获取合伙人，当前日期
			 */
			Map<String,Object> baseDataMap = new HashMap<String,Object>();
			baseDataMap.put("currentUser", ContextUtil.getCurUser());
			baseDataMap.put("currentDate", new Date());
			/**
			 * 根据常量获取审批类型对应的模板信息，包含部署流程定义的key...当然可以直接指定的key,
			 */
			String pdKey =  HaOrderApplyBatchVo.APPLY_PRO_KEY; //暂时用常量代替
			baseDataMap.put("pdKey", pdKey);
			baseDataMap.put("pdName", HaOrderApplyBatchVo.WECHAT_APPLY_PRO_NAME);
			//微信avrato店
			reqMap.put("sourceName", WECHATSHOPNAME);
			List<WxtOrderYYFWSetsVo>  lstYYFW = orderYYFWSetsMapper.queryOrderYYFWInfoByMap(reqMap);
			if(null==lstYYFW||lstYYFW.isEmpty())
			{
				throw new Exception("合伙人不存在或未配置："+WECHATSHOPNAME);
			}
			baseDataMap.put(WECHATSHOPNAME, lstYYFW.get(0));
			//需要创建一个HA订单申请审批记录，录入到wx_t_haorder_application_batch，，默认状态通过，状态值:1
			HaOrderApplyBatchVo haOrderApply = generateHAOrderApplyBatchObj(baseDataMap);
			log.info("getAvratoOrders haOrderApply:"+haOrderApply.getBatchid());
			baseDataMap.put(HaOrderServiceImpl.BATCH_ID, haOrderApply.getBatchid());
			//同时需要录入到wx_t_approve_history审批历史表中，默认为（chevronadmin管理员）通过，，1条记录即可
			insertApproveHis(""+haOrderApply.getBatchid(),"1",null);
			//需要按一定的格式吧订单录入到wx_t_haorder 和wx_t_haorder_line目录中
			//获取来源订单
			List<WXTEsSalesOrderVo> lstNeedGenerateHaOrders =  esSalesOrderVoMapper.getNeedGenerateHaOrders(reqMap);
			//构建目标ha订单，和haorderline
			Map<String,Object> targetHaOrderMap  = generateTargetHaOrdersByMap(lstNeedGenerateHaOrders, baseDataMap);
			List<HaOrderVo> lstHaOrder = (List<HaOrderVo>) targetHaOrderMap.get("lstHaorderVos");
			List<HaOrderLineVo> allHaOrderLineVosLst = (List<HaOrderLineVo>) targetHaOrderMap.get("allHaOrderLineVosLst");
			/**
			 * 批量录入ha订单
			 */
			boolean isSuccess = false;
			isSuccess = haOrderService.insertHaOrderList(lstHaOrder);  
		    if(!isSuccess)
		    {
		    	throw new Exception("getAvratoOrders Ha订单信息录入失败");
		    }
		    
		    isSuccess = haOrderService.insertHaOrderLineList(allHaOrderLineVosLst); 
		    if(!isSuccess)
		    {
		    	throw new Exception("getAvratoOrders Ha订单行信息==录入失败");
		    }
		    
		    
			List<UploadHaOrderInfo> targetOrderLst = haOrderBizService.processHaOrders(lstOrderView);
			//生成文件
			Map<String,Object> uploadMap = haOrderBizService.generateUploadOrderFiles(targetOrderLst,HaOrderBizService.ZGIS);
			List<String> lstFileNamePaths = (List<String>) uploadMap.get("lstFileNamePaths");
			//上传到sftp文件服务器
			String ip=(String) Constants.getSystemPropertyByCodeType(HaOrderBizService.HAORDER_SFTP_IP);
			String user=(String) Constants.getSystemPropertyByCodeType(HaOrderBizService.HAORDER_SFTP_USER);
			String passwd =(String) Constants.getSystemPropertyByCodeType(HaOrderBizService.HAORDER_SFTP_PWD);
			String port=(String) Constants.getSystemPropertyByCodeType(HaOrderBizService.HAORDER_SFTP_PORT);
			String dPath_ZGIS = MyPropertyConfigurer.getVal(HaOrderBizService.HAORDER_SFTP_PROPERTIES_DESTINATIONPATH_ZGIS);
			/*String key = MyPropertyConfigurer.getVal(HaOrderBizService.HAORDER_SFTP_PROPERTIES_KEY);
			byte[] enk = ThreeDesUtil.hex(key);
			byte[] reqPassword  = Base64.decode(passwd);
		    byte[] srcBytes = ThreeDesUtil.decryptMode(enk,reqPassword);  */
			isSuccess = SftpUtil.sshSftp(ip, user, passwd, Integer.parseInt(port), lstFileNamePaths, dPath_ZGIS);
			if(!isSuccess)
			{
				throw new Exception("getAvratoOrders上传文件失败");
			}
			
			//更新最近一次获取的订单日期
			esOrderService.updateLatestBeginTime(DateUtil.getDateStr(createDate, "yyyy-MM-dd"), Constants.ES_GET_AVRATO_ORDER_LAST_TIME);
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			log.error("getAvratoOrders exception", ex);
			String errorMsg = ThrowableUtil.getStackTrace(ex);
			resultMap.put("code", "error");
			resultMap.put("msg", "getAvratoOrders 异常信息："+errorMsg);
			return resultMap;
		}
		return resultMap;
	}
	
	
	private Map<String, Object> generateTargetHaOrdersByMap (
			List<WXTEsSalesOrderVo> lstOrders, Map<String, Object> baseDataMap)throws Exception
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//获取基础数据
		Long batchId = (Long) baseDataMap.get(HaOrderServiceImpl.BATCH_ID);
		Date currentDate = (Date) baseDataMap.get("currentDate");
		
		//组装维护PP后台页面的订单
		List<HaOrderVo> lstHaOrderVos = new ArrayList<HaOrderVo>();
		List<HaOrderLineVo> lstHaOrderLineVos = null;
		List<HaOrderLineVo> allHaOrderLineVosLst = new ArrayList<HaOrderLineVo>();
		String currentOrderNo = "";
		String lastOrderNo = "";
		String lastSku = "";
		String currentSku = "";
		HaOrderVo haOrder = null;
		HaOrderVo lastHaOrder = null;
		HaOrderLineVo haOrderLine = null;
		WxtOrderYYFWSetsVo orderSetsInfo = null;
		
		int allamount = 0;
		int i = 0;
		boolean isNotSameFlag = false;
		for(WXTEsSalesOrderVo ppOrder:lstOrders)
		{
			currentOrderNo = ppOrder.getOrderNewNo2();
			String workshopName = ppOrder.getShopName();
			orderSetsInfo = (WxtOrderYYFWSetsVo) baseDataMap.get(workshopName);
			String category =ppOrder.getCategory();
			String unitnetPrice = ""+df.format(ppOrder.getAverageprice());
			if(!lastOrderNo.equals(currentOrderNo))
			{
				isNotSameFlag = true;
				haOrder = new HaOrderVo();
				lstHaOrderLineVos = new ArrayList<HaOrderLineVo>();;
				haOrder.setBatchid(batchId);
				haOrder.setCreateTime(currentDate);
				haOrder.setTradeTime(ppOrder.getTradetime());
				haOrder.setDeldate(ppOrder.getSndtime());
				haOrder.setOrderNo(currentOrderNo);
				haOrder.setOrderPartnerid(orderSetsInfo.getPartnerId());
				haOrder.setOrderPartnername(workshopName);
				haOrder.setOrderSource(orderSetsInfo.getSourceValue());
				haOrder.setOrderType(orderSetsInfo.getSourceType());
			}else
			{
				isNotSameFlag = false;
			}
			currentSku = ppOrder.getGoodsno();
			if(currentSku.endsWith("01"))
        	{
				currentSku = currentSku.substring(0, currentSku.length()-2);
        	}else if (currentSku.endsWith("01A"))
        	{
        		currentSku = currentSku.substring(0, currentSku.length()-3);
        	}
			if(!lastSku.equals(currentSku) || isNotSameFlag)
			{
				allamount = 0;
				haOrderLine = new HaOrderLineVo();
				haOrderLine.setBatchid(batchId);
				haOrderLine.setOrderNo(haOrder.getOrderNo());
				haOrderLine.setSku(currentSku);
				haOrderLine.setAmount(Integer.parseInt(""+accountDf.format(ppOrder.getCount())));
				haOrderLine.setCategory(category);
				haOrderLine.setUnitprice(unitnetPrice);
				haOrderLine.setTotalnetunitprice(""+df.format(ppOrder.getAverageprice()*1000));
				allHaOrderLineVosLst.add(haOrderLine);
				lstHaOrderLineVos.add(haOrderLine);
				allamount += Integer.parseInt(""+accountDf.format(ppOrder.getCount()));
				
			}else
			{
				allamount += Integer.parseInt(""+accountDf.format(ppOrder.getCount()));
				haOrderLine.setAmount(allamount);
			}
			lastSku = currentSku;
			haOrder.setLstHaOrderLineVos(lstHaOrderLineVos);
			if(isNotSameFlag && (i!=0))
	    	{
				lstHaOrderVos.add(lastHaOrder);
	    	}
			
			lastHaOrder = new HaOrderVo();
			lastHaOrder = haOrder;
	    	i++;
	    	lastOrderNo = currentOrderNo;
		}
		lstHaOrderVos.add(haOrder);
		resultMap.put("lstHaorderVos", lstHaOrderVos);
		resultMap.put("allHaOrderLineVosLst", allHaOrderLineVosLst);
		return resultMap;
	}
	
	
	private HaOrderApplyBatchVo generateHAOrderApplyBatchObj(
			Map<String,Object> baseData)throws Exception {
		HaOrderApplyBatchVo haOrderApplyBatchVo = new HaOrderApplyBatchVo();
		haOrderApplyBatchVo.setApproveStatus(HaOrderApplyBatchVo.APPLY_STATUS_APPROVED);
		Date createDate = (Date) baseData.get("currentDate");
		String batchTitle = baseData.get("pdName")+"_"+DateUtil.toDateStrNew(createDate, 0);
		haOrderApplyBatchVo.setBatchTitle(batchTitle);
		haOrderApplyBatchVo.setCreateTime(createDate);
		haOrderApplyBatchVo.setProkey((String)baseData.get("pdKey"));
		haOrderApplyMapper.insertSelective(haOrderApplyBatchVo);
		return haOrderApplyBatchVo;
	}
	
	private  boolean insertApproveHis(String applyBatchId,
			String status, String approveRemark)
	{
		ApproveHistory approveHis = new ApproveHistory();
		approveHis.setOrderBatchId(Long.parseLong(applyBatchId));
		approveHis.setApprover("1");
		if(null==approveRemark||approveRemark.isEmpty())
		{
			if(status.equals(HaOrderApplyBatchVo.APPLY_STATUS_UNAPPROVED))
			{
				approveHis.setApproveRemark(HaOrderApplyBatchVo.APPLY_STATUS_UNAPPROVED_REMARK);
			}else
			{
				approveHis.setApproveRemark(HaOrderApplyBatchVo.APPLY_STATUS_APPROVED_REMARK);
			}
		}else
		{
			approveHis.setApproveRemark(approveRemark);
		}
		approveHis.setApproveStatus(status);
		approveHis.setApplyTempletType(HaOrderApplyBatchVo.APPLY_PRO_KEY);
		approveHis.setApproveTime(new Date());
		approveHistoryMapper.insertSelective(approveHis);
		return true;
	}
}
