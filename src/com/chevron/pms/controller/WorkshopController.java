package com.chevron.pms.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.common.exception.WxPltException;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopCtrlParams;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterParams;
import com.chevron.pms.dao.RegionPartnerVoMapper;
import com.chevron.pms.dao.RegionVoMapper;
import com.chevron.pms.dao.WorkShopStatisticsMapper;
import com.chevron.pms.dao.WorkshopPartnerVoMapper;
import com.chevron.pms.model.PartnerCtrlParam;
import com.chevron.pms.model.WorkShopDistInfo;
import com.chevron.pms.service.RegionService;
import com.chevron.pms.service.WorkShopService;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.WxUserServiceI;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.service.OrganizationService;

/**
 * 门店审批控制器
 *
 * <AUTHOR>
 *
 */
@Api(value = "门店审批控制器Controller", tags = "门店审批控制器Controller")
@Controller
public class WorkshopController {
	@Resource
	private WorkshopMasterBizService workshopMasterBizService;
	@Resource
	private WorkshopMasterMapper workshopMasterMapper;
	@Resource
	private RegionVoMapper regionVoMapper;
	@Resource
	private WorkShopStatisticsMapper statisticMapper;
	@Resource
	RegionPartnerVoMapper regionPartnerMapper;
	
	@Resource
	OrganizationVoMapper organizationVoMapper;
	@Resource
	WxUserServiceI wxUserService;

	@Resource
	OrganizationService organizationServiceImpl;
	@Resource
	WxRoleServiceImpl wxRoleService;

	@Resource
	WorkshopPartnerVoMapper workshopPartnerVoMapper;

	@Resource
	WorkShopService workShopServiceImpl;

	@Resource
	RegionService regionService;
	
	private Logger logger = LoggerFactory.getLogger(OrderController.class);

	public final static String PARAMS_KEY = "com.chevron.pms.controller.WorkshopController.params";

	public final static String INVENTORY_PARAMS_KEY = "com.chevron.pms.controller.WorkshopController.inventoryParams";

//	@ResponseBody
//	@RequestMapping("/workshop/activePush.do")
//	public Map<String, Object> active(HttpServletRequest request) {
//		Map<String,Object> resultMap = new HashMap<String,Object>(5);
//		try {
//			Map<String, Object> paramMap = new HashMap<String, Object>();
//			if(StringUtils.isNotBlank(request.getParameter("activeDateFrom"))){
//				paramMap.put("activeDateFrom", request.getParameter("activeDateFrom"));
//			}
//			if(StringUtils.isNotBlank(request.getParameter("activeDateTo"))){
//				paramMap.put("activeDateTo", request.getParameter("activeDateTo"));
//			}
//			List<WorkshopMaster> workShopVos = workShopVoMapper.queryActiveWorkshopByParams(paramMap);
//			if(!workShopVos.isEmpty()){
//				for(WorkShopVo workShopVo : workShopVos){
//					workShopServiceImpl.activeWorkshop(workShopVo.getId(), workShopVo.getPartnerId());
//				}
//			}
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		} catch (Exception e) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			//add by bo.liu 180111
//			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			logger.error(e.getMessage(), e);
//		}
//		return resultMap;
//	}
//	@ResponseBody
//	@RequestMapping("/workshop/testActivePush.do")
//	public Map<String, Object> testActivePush(HttpServletRequest request) {
//		Map<String,Object> resultMap = new HashMap<String,Object>(5);
//		try {
//			LogUtils.addInfoLog(1l, "WorkshopController.testActivePush", "测试推送门店激活department=" + request.getParameter("department") +
//					"&id=" + request.getParameter("id"));
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		} catch (Exception e) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			//add by bo.liu 180111
//			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			logger.error(e.getMessage(), e);
//		}
//		return resultMap;
//	}
	@ResponseBody
	@ApiOperation(value="data",  httpMethod="POST", notes="data")
	@RequestMapping("/workshop/data.do")
	public Map<String, Object> data(WorkshopMasterParams params, HttpServletRequest request) {
		Map<String, Object> resultMap = workShopServiceImpl.queryWorkshopForPage(params);
		request.getSession().setAttribute(PARAMS_KEY, params);
		return resultMap;
	}
//	@RequestMapping("/workshop/export.do")
//	public String export(WorkshopParams params, HttpServletRequest request, HttpServletResponse response) {
//		try {
//			// 组装登录用户信息参数
//			WxTUser curUser = ContextUtil.getCurUser();
//			logger.debug("0320 WorkshopController export currentUser:{} , WorkshopParams:{}",curUser.getUserId(),params==null?"":params.toString());
//			params.setOrgId(curUser.getOrgId());
//			params.setOrgType(curUser.getType());
//			params.setUserType(WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel()) ? 1l : 0l);
//			params.setPaging(false);
//			params.setLimit(0);
//
//			List<WorkShopVo> rows = workShopVoMapper.selWorkshopForPage(params);
//			if(params.getUserType() == 1 && rows != null && !rows.isEmpty()){
//				params.closeOrder();
//				List<WorkShopVo> relations = workShopVoMapper.selectRelationsByWorkshopParams(params);
//				Map<Long, String> partnersMap = new HashMap<Long, String>(rows.size());
//				for(WorkShopVo relation : relations){
//					String partners = partnersMap.get(relation.getId());
//					if(partners == null){
//						partners = relation.getPartnerName();
//					}else{
//						partners += ("," + relation.getPartnerName());
//					}
//					partnersMap.put(relation.getId(), partners);
//				}
//				for(WorkShopVo workShopVo : rows){
//					workShopVo.setPartnerName(partnersMap.get(workShopVo.getId()));
//				}
//			}
//			writeExcel(response, request, rows);
//		} catch (Exception e) {
//			logger.error(e.getMessage(), e);
//			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
//			return "forward:/common/jsp/downloadError.jsp";
//		}
//		return null;
//	}
//
//	protected void writeExcel(HttpServletResponse response, HttpServletRequest request, List<WorkShopVo> dataList) throws Exception{
//		CommonUtil.setExportResponseHeader(request, response, "门店", "xlsx");
//		// 导出列定义
//		List<ExportCol> exportCols = new ArrayList<ExportCol>();
//		ExportCol col = new ExportCol("partnerName", "合伙人名称", WorkShopVo.class);
//		col.setWidth(40);
//		exportCols.add(col);
//		col = new ExportCol("workShopName", "门店名称", WorkShopVo.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("provinceName", "所属省份", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("cityName", "所属城市", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("regionName", "所属区域", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("workShopAddress", "门店地址", WorkShopVo.class);
//		col.setWidth(40);
//		exportCols.add(col);
//		col = new ExportCol("type", "门店类型", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("excutorName", "执行人", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("longitude", "经度", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("latitude", "纬度", WorkShopVo.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("", "参与门店定位计划");
//		col.setWidth(20);
//		col.setPropertyHelper(new IPropertyHelper() {
//			
//			@Override
//			public Object getProperty(String propertyName, Object bean) throws Exception {
//				return ((Integer)1).equals(((WorkShopVo)bean).getJoinLocationPlan()) ? "是" : "否";
//			}
//		});
//		exportCols.add(col);
//		col = new ExportCol("remark", "备注", WorkShopVo.class);
//		col.setWidth(35);
//		exportCols.add(col);
//		PoiWriteExcel.exportLargeData(dataList, exportCols, response.getOutputStream(), "Sheet1", true);
//	}

//	@ResponseBody
//	@RequestMapping("/workshop/queryWorkshopWithInventory.do")
//	public Map<String, Object> queryWorkshopWithInventory(WorkshopParams params, HttpServletRequest request) {
//		// 组装登录用户信息参数
//		WxTUser curUser = ContextUtil.getCurUser();
//		params.setOrgId(curUser.getOrgId());
//		params.setOrgType(curUser.getType());
//		params.setUserType(curUser.getmUserTypes());
//
//
//		//只查看已激活状态的门店
//		params.setStatus("3");
//
//		Map<String, Object> map = new HashMap<String, Object>(2);
//		List<WorkShopVo> rows = workShopVoMapper.selWorkshopWithInventory(params);
//		if(curUser.getmUserTypes() == 1 && rows != null){
//			for(WorkShopVo ws : rows){
//				List<OrganizationVo> ps = organizationServiceImpl.getAllPartnersByWorkshop(ws.getId());
//				if(ps != null){
//					String pNames = null;
//					for(OrganizationVo sp : ps){
//						if(pNames == null){
//							pNames = sp.getOrganizationName();
//						}else{
//							pNames += ("," + sp.getOrganizationName());
//						}
//					}
//					ws.setPartnerName(pNames);
//				}
//			}
//		}
//		map.put("rows", rows);
//		map.put("total", params.getTotalCount());
//		request.getSession().setAttribute(INVENTORY_PARAMS_KEY, params);
//		return map;
//	}

	@ResponseBody
	@ApiOperation(value="suggest",  httpMethod="POST", notes="suggest")
	@RequestMapping("/workshop/suggest.do")
	public Map<String, Object> suggest(WorkshopCtrlParams params) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		List<Map<String, Object>> value = new ArrayList<Map<String, Object>>();
		map.put("value", value);
		WxTUser user = ContextUtil.getCurUser();
		if(params == null){
			params = new WorkshopCtrlParams();
		}
		if(!WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) && params.getIncludeRetailer() == null){
			params.setPartnerId(user.getOrgId());
		}
		if(params.getIncludeChannels() == -1) {
			params.setIncludeChannels(user.getIncludeChannels());
		}else {
			params.setIncludeChannels(params.getIncludeChannels() & user.getIncludeChannels());
		}
		List<WorkshopMaster> list = workshopMasterMapper.queryForCtrl(params);
		for (WorkshopMaster workShopVo : list) {
			Map<String, Object> item = new HashMap<String, Object>(1);
			item.put("text", workShopVo.getWorkshopName());
			value.add(item);
		}
		return map;
	}
	
	@ResponseBody
	@ApiOperation(value="autoSuggest",  httpMethod="POST", notes="autoSuggest")
	@RequestMapping("/workshop/autoSuggest.do")
	public Map<String, Object> autoSuggest(WorkshopCtrlParams params) {
		Map<String, Object> map = new HashMap<String, Object>();
		if(params == null){
			params = new WorkshopCtrlParams();
		}
		map.put("workshopName", params.getWorkshopName());
		map.put("fromSource", params.getSource());
		List<WorkshopMaster> list = workshopMasterMapper.queryWorkShopByParams(map);
//		for(WorkshopMaster shop:list) {
//			shop.setRegionName(o2oOrderServiceImpl.getWorkShopRegionNames(shop.getRegionId()));
//		}
		map.put(Constants.RESULT_LST_KEY, list);
		map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return map;
	}
	
	@ResponseBody
	@ApiOperation(value="selectSuggest",  httpMethod="POST", notes="selectSuggest")
	@RequestMapping("/workshop/selectsuggest.do")
	public Map<String, Object> selectSuggest(WorkshopCtrlParams params) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		List<Map<String, Object>> value = new ArrayList<Map<String, Object>>();
		map.put("value", value);
		WxTUser user = ContextUtil.getCurUser();
		if(params == null){
			params = new WorkshopCtrlParams();
		}
		if(params.getLimit() < 100){
			params.setLimit(100);
		}
//		if(!WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel())){
//			params.setPartnerId(user.getOrgId());
//		}
		if(params.getIncludeChannels() == -1) {
			params.setIncludeChannels(user.getIncludeChannels());
		}else {
			params.setIncludeChannels(params.getIncludeChannels() & user.getIncludeChannels());
		}
		List<WorkshopMaster> list = workshopMasterMapper.queryForCtrl(params);
		for (WorkshopMaster workShopVo : list) {
			Map<String, Object> item = new HashMap<String, Object>(3);
			item.put("text", workShopVo.getWorkshopName());
			item.put("value", workShopVo.getId());
			item.put("node", workShopVo);
			value.add(item);
		}
		return map;
	}
//
//	@RequestMapping(value = "/workshop/mapstatistics.do", method = RequestMethod.GET)
//	public ModelAndView index(HttpServletRequest request) {
//		ModelAndView mav = new ModelAndView("business/workshop/mapstatistics");
//		/* mav.addObject("rows", workShopVoMapper.selWorkshop(param)); */
//		request.setAttribute("rows", JsonGenerator
//				.getJsonContent(workShopVoMapper.selWorkshopListAll()));
//		return mav;
//	}

//	/**
//	 * 1. 目前系统里面有多少家门店,有多少家已经扫店的门店,有多少家正在攻店的,有多少家是正式激活的门店; 2. 激活门店的技师数量和店主数量;
//	 */
//	@RequestMapping(value = "/workshop/statisticsWorkshop.do", method = RequestMethod.GET)
//	public ModelAndView statisticsWorkshop(HttpServletRequest request) {
//		ModelAndView mav = new ModelAndView(
//				"business/workshop/workshopDashboard");
//
//		WxTUser user = ContextUtil.getCurUser();
//		long userType = ContextUtil.getCurUser().getmUserTypes();
//
//		Map<String, Object> statisPra = new HashMap<String, Object>();
//
//		// 如果用户属于雪佛龙用户 ，根据雪佛龙ADMIN的orgID去查询所有用户
//		if (userType != 1) {
//			statisPra.put("partnerId", user.getOrgId());
//		}
//
//		statisPra.put("employeeType", "Mechanic");
//		List<WorkShopStatistics> employeeList = statisticMapper
//				.getWorkshopEmployeeListByPartnerIdAndOtherPra(statisPra);
//		Long mechanicsCount = Long.valueOf(0);
//		if (employeeList != null && !employeeList.isEmpty()) {
//			mechanicsCount = Long.valueOf(employeeList.size());
//		}
//		/*
//		 * if (null != employeeList && employeeList.size() > 0) { for
//		 * (WorkShopStatistics temp : employeeList) { if
//		 * (temp.getWorkShopRegionId() != null) { String regionId =
//		 * temp.getWorkShopRegionId().toString(); if (regionId.substring(0,
//		 * 4).equals("4403")) { mechanicsCount = mechanicsCount + 1; } } } }
//		 */
//		request.setAttribute("mechanicsCount", mechanicsCount.toString());
//		request.setAttribute("showMap", WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()));
//		request.setAttribute("showTdReport", user.getUserId() == 1 || wxRoleService.isIncludeRole(user.getUserId(), Constants.CHEVRON_MANAGER_CODE));
//		request.setAttribute("yesterday", DateUtil.getDateStr(DateUtil.addDays(DateUtil.getCurrentDate(), -1), "yyyy年M月d号"));
//		return mav;
//	}

//	@ResponseBody
//	@RequestMapping("/workshop/getWorkshopData.do")
//	public Map<String, Object> getWorkshopData() {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		WxTUser curUser = ContextUtil.getCurUser();
//
//		WorkshopVoAuthParams params = new WorkshopVoAuthParams();
//		if(!WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel())){
//			params.setPartnerId(curUser.getOrgId());
//		}
//		List<WorkshopCountReportView> workShopCountList = workShopVoMapper.getWorkshopCountsGroupByProvince(params);
//
//		List<RegionVo> regionList = regionVoMapper.selectByExample(null);
//		Map<String, RegionVo> mapProvince = new HashMap<String, RegionVo>();
//		Map<String, String> mapProvinceName = new HashMap<String, String>();
//		for (RegionVo tempReg : regionList) {
//			if (tempReg.getParentId().equals(Long.valueOf("1"))) {
//				mapProvince.put(tempReg.getRegionCode(), tempReg);
//				String provinceName = tempReg.getRegionName();
//				if(provinceName.contains("内蒙古")){
//					mapProvinceName.put(tempReg.getRegionCode(),
//							"内蒙古");
//				}else if(provinceName.contains("黑龙江")){
//					mapProvinceName.put(tempReg.getRegionCode(),
//							"黑龙江");
//				}else {
//				mapProvinceName.put(tempReg.getRegionCode(),
//					tempReg.getRegionName().substring(0, 2));
//				}
//			}
//		}
//
//		Map<String,Long> provinceCountMap = new HashMap<String, Long>();
//
//		for (WorkshopCountReportView workShopTemp : workShopCountList) {
//			if(workShopTemp.getRegionCode() != null){
//				provinceCountMap.put(workShopTemp.getRegionCode(), workShopTemp.getCount());
//			}
//		}
//		Set<String> provinceSet = provinceCountMap.keySet();
//		List<EcharDataPojo> provinceCountList = new ArrayList<EcharDataPojo>();
//		for (String provinceCode :provinceSet) {
//			EcharDataPojo temp = new EcharDataPojo();
//			temp.setName(mapProvinceName.get(provinceCode));
//			temp.setValue(provinceCountMap.get(provinceCode));
//			provinceCountList.add(temp);
//		}
//		resultMap.put("provinceCountMap", provinceCountList);
//		return resultMap;
//	}

//	@ResponseBody
//	@SuppressWarnings("unchecked")
//	@RequestMapping("/workshop/getUserAccountForShenzheng.do")
//	public Map<String, Object> getUserLstByParentIdForShenZhengDemo() {
//		Map<String, Object> reqMap = new HashMap<String, Object>();
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<RegionPartnerVo> lstPartner = regionPartnerMapper
//				.getPartnerVosForShengZhengDemo(reqMap);
//		List<WxTUser> userLst = new ArrayList<WxTUser>();
//		for (RegionPartnerVo regionPartnerVo : lstPartner) {
//			List<Long> orgIds = organizationVoMapper
//					.getAllNewChildOrgId(regionPartnerVo.getPartnerId());
//			reqMap.put("orgmodel", 1);// 查询选中的一个组织的所有用户
//			reqMap.put("orgIdLst", orgIds);
//			reqMap.put("mUserNameOrAccount", null);
//			reqMap.put("userId", 0);
//			ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
//					orgIds, 1000);
//			int toltalPage = newPage.getTotalPages();
//			for (int i = 1; i <= toltalPage; i++) {
//				List<Long> orgIdsDemo = newPage.getObjects(i);
//				reqMap.put("orgIdLst", orgIdsDemo);
//				List<WxTUser> userLstDemo = new ArrayList<WxTUser>();
//				userLstDemo = wxUserService.getUsersByOrgList(reqMap);
//				userLst.addAll(userLstDemo);
//			}
//		}
//		resultMap.put("userAccountForShenzheng", userLst.size());
//		return resultMap;
//	}
//
//	@ResponseBody
//	@SuppressWarnings("unchecked")
//	@RequestMapping("/workshop/getUserAccount.do")
//	public Map<String, Object> getUserLstByParentId() {
//		Map<String, Object> reqMap = new HashMap<String, Object>();
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//
//		long orgId = ContextUtil.getCurUser().getOrgId();
//		long userType = ContextUtil.getCurUser().getmUserTypes();
//
//		// 如果用户属于雪佛龙用户 ，根据雪佛龙ADMIN的orgID去查询所有用户
//		if (userType == 1) {
//			orgId = 1;
//		}
//
//		List<WxTUser> userLst = new ArrayList<WxTUser>();
//		List<Long> orgIds = organizationVoMapper.getAllNewChildOrgId(orgId);
//		reqMap.put("orgmodel", 1);// 查询选中的一个组织的所有用户
//		reqMap.put("orgIdLst", orgIds);
//		reqMap.put("mUserNameOrAccount", null);
//		reqMap.put("userId", 0);
//		ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(orgIds,
//				1000);
//		int toltalPage = newPage.getTotalPages();
//		for (int i = 1; i <= toltalPage; i++) {
//			List<Long> orgIdsDemo = newPage.getObjects(i);
//			reqMap.put("orgIdLst", orgIdsDemo);
//			List<WxTUser> userLstDemo = new ArrayList<WxTUser>();
//			userLstDemo = wxUserService.getUsersByOrgList(reqMap);
//			userLst.addAll(userLstDemo);
//		}
//		resultMap.put("userAccount", userLst.size());
//
//		return resultMap;
//	}
//
//	@ResponseBody
//	@RequestMapping("/workshop/getCurSpRegion.do")
//	public Map<String,Object> getCurSpRegion(@RequestParam("regionId")String regionId){
//		if(regionId==null) {
//			regionId="0";
//		}
////		Map<String,Object> resultMap = new HashMap<String, Object>();
//		WxTUser user = ContextUtil.getCurUser();
//		Long rId = Long.parseLong(regionId);
//
//		return regionService.findSpRegionInfo(rId, user.getOrgId());
//	}
//
//	@ResponseBody
//	@RequestMapping("/workshop/getPartnerCount.do")
//	public Map<String, Object> getPartnerCount() {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//
//		WxTUser user = ContextUtil.getCurUser();
//		long isChevronManager = user.getmUserTypes();
//		resultMap.put("isChevronManager", isChevronManager);
//
//		OrganizationParam orgParam = new OrganizationParam();
//		Long  partnerCount = organizationVoMapper.getPartnersCount(orgParam);
//		resultMap.put("partnerCount", partnerCount);
//
//		return resultMap;
//	}


	/**
	 * 获取所有合伙人
	 * <AUTHOR> 2016-11-25 下午5:07:02
	 * @param request
	 * @return
	 */
	@ResponseBody
	@ApiOperation(value="获取所有合伙人",  httpMethod="POST", notes="获取所有合伙人")
	@RequestMapping("/workshop/getPartnersInfo.do")
	public Map<String, Object> getPartnersInfo(PartnerCtrlParam param) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		if(param == null){
			param = new PartnerCtrlParam();
		}
		try {
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel())){
				param.setChevron(true);
			}
//			param.setSpResource(true);
			param.setLoginUser(user.getUserId());
			param.setLoginUserOrg(user.getOrgId());
			map.put("data", organizationVoMapper.selPartnersNew(param));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getLocalizedMessage());
			map.put("success", false);
		}
		return map;
	}
//
//	@ResponseBody
//	@RequestMapping(value="/workshop/getJihuoCount.do")
//	public Map<String, Object> getJihuoCount() {
//		WorkshopParams params = new WorkshopParams();
//		params.setField("id");
//		// 组装登录用户信息参数
//		WxTUser curUser = ContextUtil.getCurUser();
//		if(!WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel())){
//			params.setPartnerId(curUser.getOrgId());
//		}
//		params.setStatus("3");
//
//		Map<String, Object> map = new HashMap<String, Object>(2);
//		List<WorkShopVo> rows = workShopVoMapper.selWorkshopStaus(params);
//		map.put("totalRecord", params.getTotalCount());
//		return map;
//	}
//
//	@ResponseBody
//	@RequestMapping(value="/workshop/getStatusCount.do")
//	public Map<String, Object> getStatusCount() {
//
//		Map<String, Object> map = new HashMap<String, Object>(2);
//		WxTUser curUser = ContextUtil.getCurUser();
//		//已激活
//		WorkshopVoAuthParams params = new WorkshopVoAuthParams();
//		if(!WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel())){
//			params.setPartnerId(curUser.getOrgId());
//		}
//		List<WorkshopCountReportView> countList = workShopVoMapper.getWorkshopCountsGroupByStatus(params);
//		for(WorkshopCountReportView countView : countList){
//			if(countView.getStatus() != null && countView.getStatus().equals("3")){
//				map.put("jihuoTotalRecord", countView.getCount());
//			}
//			if(countView.getStatus() != null && countView.getStatus().equals("0")){
//				map.put("saodianTotalRecord", countView.getCount());
//			}
//			if(countView.getStatus() != null && countView.getStatus().equals("1")){
//				map.put("gongdianTotalRecord", countView.getCount());
//			}
//		}
//		if(map.get("jihuoTotalRecord") == null){
//			map.put("jihuoTotalRecord", 0);
//		}
//		if(map.get("saodianTotalRecord") == null){
//			map.put("saodianTotalRecord", 0);
//		}
//		if(map.get("gongdianTotalRecord") == null){
//			map.put("gongdianTotalRecord", 0);
//		}
//
//		return map;
//	}

	@ResponseBody
	@ApiOperation(value="queryForCtrlPage",  httpMethod="POST", notes="queryForCtrlPage")
	@RequestMapping("/workshop/queryforctrlpage.do")
	public JsonResponse queryForCtrlPage(WorkshopMasterParams params) {
		JsonResponse resultMap = new JsonResponse();
		try {
			resultMap.setListResult(workshopMasterBizService.querySimpleForPage(params));
			resultMap.setTotalOfPaging(params.getTotalCount());
			logger.info("queryForCtrlPage success");
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.pms.controller.WorkshopController.queryForCtrlPage", params == null ? "null" : JsonUtil.writeValue(params));
		}
		return resultMap;
	}

	@ResponseBody
	@ApiOperation(value="queryForPromotionPage",  httpMethod="POST", notes="queryForPromotionPage")
	@RequestMapping("/workshop/queryForPromotionPage.do")
	public JsonResponse queryForPromotionPage(WorkshopMasterParams params) {
		JsonResponse resultMap = new JsonResponse();
		try {
			params.setPartnerId(ContextUtil.getCurUser().getOrgId());
			params.setStatus("3");
			params.setBusinessWeight(1);
			workshopMasterBizService.querySimpleForPage(params, resultMap);
			logger.info("queryForPromotionPage success");
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.pms.controller.WorkshopController.queryForPromotionPage", params == null ? "null" : JsonUtil.writeValue(params));
		}
		return resultMap;
	}

	@ResponseBody
	@ApiOperation(value="queryWorkShopDistanceById",  httpMethod="POST", notes="queryWorkShopDistanceById")
	@RequestMapping("/workshop/queryWorkShopDistanceById.do")
	public JsonResponse queryWorkShopDistanceById(WorkshopMaster params) {
		JsonResponse resultMap = new JsonResponse();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("id", params.getId());
			List<WorkShopDistInfo> queryWorkShopDistanceById = workshopMasterMapper.queryWorkShopDistanceById(paramMap);
			/*Boolean isShow = false;
			if(!CollectionUtil.isEmpty(queryWorkShopDistanceById)) {
				if(queryWorkShopDistanceById.get(0).getWorkShopNum() <=  queryWorkShopDistanceById.size()) {
					isShow = true;
				}
			}
			resultMap.put("isShow", isShow);*/
			resultMap.setTotalOfPaging(queryWorkShopDistanceById.size());
			resultMap.setListResult(queryWorkShopDistanceById);
		} catch (Exception e) {
		    e.printStackTrace();
			resultMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resultMap.put(JsonResponse.KEY_ERROR_MSG,"查询指定距离内门店数有误，请核对参数！");
		}
		return resultMap;
	}
	
	@ResponseBody
	@ApiOperation(value="queryFleetInfoDistanceById",  httpMethod="POST", notes="queryFleetInfoDistanceById")
	@RequestMapping("/fleetInfo/queryFleetInfoDistanceById.do")
	public JsonResponse queryFleetInfoDistanceById(Long id) {
		JsonResponse resultMap = new JsonResponse();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("id", id);
			List<WorkShopDistInfo> queryWorkShopDistanceById = workshopMasterMapper.queryFleetInfoDistanceById(paramMap);
			/*Boolean isShow = false;
			if(!CollectionUtil.isEmpty(queryWorkShopDistanceById)) {
				if(queryWorkShopDistanceById.get(0).getWorkShopNum() <=  queryWorkShopDistanceById.size()) {
					isShow = true;
				}
			}
			resultMap.put("isShow", isShow);*/
			resultMap.setTotalOfPaging(queryWorkShopDistanceById.size());
			resultMap.setListResult(queryWorkShopDistanceById);
		} catch (Exception e) {
			resultMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resultMap.put(JsonResponse.KEY_ERROR_MSG,"查询指定距离内车队数有误，请核对参数！");
		}
		return resultMap;
	}
	
	@ResponseBody
	@ApiOperation(value="queryProjectMachineryDistanceById",  httpMethod="POST", notes="queryProjectMachineryDistanceById")
	@RequestMapping("/projectMachiner/queryProjectMachineryDistanceById.do")
	public JsonResponse queryProjectMachineryDistanceById(Long id) {
		JsonResponse resultMap = new JsonResponse();
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("id", id);
			List<WorkShopDistInfo> queryWorkShopDistanceById = workshopMasterMapper.queryProjectMachineryDistanceById(paramMap);
			/*Boolean isShow = false;
			if(!CollectionUtil.isEmpty(queryWorkShopDistanceById)) {
				if(queryWorkShopDistanceById.get(0).getWorkShopNum() <=  queryWorkShopDistanceById.size()) {
					isShow = true;
				}
			}
			resultMap.put("isShow", isShow);*/
			resultMap.setTotalOfPaging(queryWorkShopDistanceById.size());
			resultMap.setListResult(queryWorkShopDistanceById);
		} catch (Exception e) {
			resultMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resultMap.put(JsonResponse.KEY_ERROR_MSG,"查询指定距离内工程机械数有误，请核对参数！");
		}
		return resultMap;
	}

	@ResponseBody
	@ApiOperation(value="批量同步有效门店至直播平台",  httpMethod="POST", notes="批量同步门店至直播平台")
	@RequestMapping("/workshop/synEtoWorkShops.do")
	public JsonResponse workshop(String isFall) {
		JsonResponse resultMap = new JsonResponse();
		try {
			workShopServiceImpl.synEtoWorkShops(isFall);
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(JsonResponse.KEY_ERROR_MSG,e.getMessage());
		}
		return resultMap;
	}
}
