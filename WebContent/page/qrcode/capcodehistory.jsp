<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page import="com.common.util.ContextUtil"%>
<%@ page import="com.sys.auth.model.WxTMenu"%>
<%@ page import="java.util.List"%>
<!DOCTYPE html>

<c:set var="ctx" value="${pageContext.request.contextPath}/" />
<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>QRCode管理</title>
<%@include file="/common/jsp/common.jsp"%>

<script type="text/javascript"
	src="${ctx }page/qrcode/js/capcodehistory.js?v=${version}"></script>
<style type="text/css">
</style>

</head>

<body class="gray-bg">
	<div class="content-wrapper">
		<!-- <div id="nav" class="ui-layout-north">
		<c:set var="currentPage" value="cap"/>
		<-%@include file="/page/qrcode/capcodeNav.jsp"%>
		</div> -->
		<div id="queryPanel" class="content-panel query-panel">
		<!-- <div class="field-group" style="width: 50%">
				<label class="field-label width-auto">搜索：</label>
				<div class="control-group" style="width: 85%">
					<input type="text" id="productName" name="productName" placeholder="请输入产品名称、SUK" style="width:100%"
						class="control-text" value="" />
				</div>
			</div> -->
			<!-- <div class="field-group" id="queryContent">
				<label class="field-label">产品名称：</label>
				<div class="control-group" id="productName_c">
					<input type="hidden" id="productName" name="productName"
						class="control-text" value="" />
				</div>
			</div> -->
			<div class="col-merge-2">
			<div class="field-group">
				<label class="field-label width-auto">生成起始日期：</label>
				<div class="control-group">
					<input type="text" id="dateFrom" readonly="readonly"
						class="control-calendar control-text"
						onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'dateTo\')}'});" /></input>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">生成截止日期：</label>
				<div class="control-group">
					<input type="text" id="dateTo" readonly="readonly"
						class="control-text control-calendar"
						onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'dateFrom\')}'});" /></input>
				</div>
			</div>
			</div>
			<div class="query-btns">
				<div class="query-btn field-label">
					<button onclick="query();" id="queryBtn" class="btn-query">查询</button>
				</div>
			</div>
		</div>
		<div class="content-panel tools-panel">
			<button class="btn-create" onclick="openGenerateDialog(false,null,null);">生成瓶盖码</button>
			<!-- <button class="btn-create" onclick="downloadRecord(true);">批量下载</button> -->
		</div>
		<div class="content-panel" id="grid" style="min-height: 350px;"></div>
	</div>
	<div style="display: none;" id="generateDialog" class="content-panel query-panel">
		<div class="field-group" style="width:100%">
			<label class="field-label"><span class="required-flag">*</span>URL：</label>
			<div class="control-group" style="width:85%">
				<input id="verifyUrl" name="verifyUrl" type="text" class="control-text" style="width:80%"/>
			</div>
		</div>
		<!-- <div class="field-group" style="width:100%">
			<label class="field-label"><span class="required-flag">*</span>生成类型：</label>
			<div class="control-group" style="width:85%" id="skuSelect">
				<input id="sku" class="input-small control-text" name="sku" type="hidden" value="" />
			</div>
		</div> -->
		<div class="field-group" style="width:100%">
			<label class="field-label"><span class="required-flag">*</span>生成数量：</label>
			<div class="control-group" style="width:85%">
				<input id="count" name="count" type="text" class="control-text" style="width:80%"/>
			</div>
		</div>
	</div>
	<form action="" method="post" target=downloadWin style="display: none;"
		id="downloadForm"></form>
	<iframe name="downloadWin" id="downloadWin" style="display: none;"></iframe>
</body>
</html>