<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="java.util.List"%>
<%@page import="com.common.util.SpringUtils"%>
<%@page import="com.common.util.ContextUtil"%>
<%@page import="com.sys.auth.model.WxTUser"%>
<!DOCTYPE html>
<%
	WxTUser user = ContextUtil.getCurUser();
	String pageFlag = request.getParameter("pageFlag");
	if (pageFlag == null || !"A".equals(pageFlag)) {
		pageFlag = "P";
	}
	
%>
<html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>服务订单</title>
<%@include file="/common/jsp/common.jsp"%>
<script type='text/javascript'
	src="${ctx }business/order_ddbx/js/orderDdfwPage1.js?v=2017101803"></script>
<script src="${ctx }common/date/WdatePicker.js" type="text/javascript"></script>
<script type="text/javascript">
	common.curUserId = "${curUserId}";
	var userType = <%=WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? "1" : "0" %>, 
		orgId = '<%=user.getOrgId()%>';
	var cnName = '<%=user.getCnName()%>';
</script>
<style text="text/css">
     .btn-export{
          margin-bottom:15px;
     }
</style>
</head>
<body class="gray-body">
	<div class="content-wrapper">
		<div id="queryPanel" class="content-panel query-panel">
		   <div class="field-group" style="width:50%">
				<label class="field-label width-auto">搜索：</label>
				<div class="control-group"  style="width:85%">
					<input type="text" id="gKeyWord" name="gKeyWord"
						style="width:100%" placeholder="请输入发货单号、车牌号、收货人、收货电话" class="control-text" />
				</div>
			</div>
			<div class="field-group cond-adv">
				<label class="field-label">车牌号：</label>
				<div class="control-group">
					<input type="text" name="plateNumber" id="plateNumber"
						placeholder="车牌号" class="control-text" />
				</div>
			</div>
			<div class="field-group cond-adv">
				<label class="field-label">收货人姓名：</label>
				<div class="control-group">
					<input type="text" name="receiveUserName" id="receiveUserName"
						placeholder="收货人姓名" class="control-text" />
				</div>
			</div>
			<div class="field-group cond-adv">
				<label class="field-label">收货人电话：</label>
				<div class="control-group">
					<input type="text" name="receivePhoneNo" id="receivePhoneNo"
						placeholder="收货人电话" class="control-text" />
				</div>
			</div>
			<div class="field-group cond-adv" id="orderPriceTypeCtrl" style="display: none;">
				<label class="field-label">订单类型：</label>
				<div class="control-group">
					<input type="text" name="preferentialTypePrice"
						id="preferentialTypePrice" placeholder="订单类型"
						class="control-text" />
				</div>
			</div>
			<div class="field-group cond-adv">
				<label class="field-label">发货时间：</label>
				<div class="control-group">
					<input type="text" id="deliveryTime" name="deliveryTime"
						placeholder="发货时间" class="control-text control-calendar"
						onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'});" />
				</div>
			</div>
			<div class="field-group cond-adv">
				<label class="field-label">状态：</label>
				<div class="control-group" id="stateIdC">
					<input id="stateId" name="stateId" class="input-small control-text"
						type="hidden" value="-1" />
				</div>
			</div>
			<div class="field-group cond-adv" id="orderSourceCtrl" style="display: none;">
				<label class="field-label">订单来源：</label>
				<div class="control-group" id="orderSourceCONT">
					<input id="orderSourceId" name="orderSourceId"
						class="input-small control-text" type="hidden" />
				</div>
			</div>
			
			<div class="field-group cond-adv">
				<label class="field-label">创建起始时间：</label>
				<div class="control-group">
					<input type="text" id="orderCreateTime" name="orderCreateTime" placeholder="订单创建起始时间"
						class="control-text control-calendar"
						onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'orderCreateTime\')}'});" />
				</div>
			</div>
			<div class="field-group cond-adv">
			<label class="field-label">创建截至时间：</label>
				<div class="control-group">
					<input type="text" id="orderCreateEndTime" name="orderCreateEndTime" placeholder="订单创建截至时间"
						class="control-text control-calendar"
						onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'orderCreateEndTime\')}'});" />
				</div>
			</div>
			
			<div class="query-btns">
				<div class="query-btn field-label">
					<button onclick="queryOrderByParams()" id="queryBtn"
						class="btn-query">查询</button>
				</div>
				<div class="adv-toggle-btn">
					<a href="javascript: void(0);"
						onclick="var el = $(this), queryPanel = el.parents('.query-panel:first');if(queryPanel.hasClass('query-adv')){queryPanel.removeClass('query-adv');el.text('高级搜索');}else{queryPanel.addClass('query-adv');el.text('收起');}">高级搜索</a>
				</div>
			</div>
			<div class="content-panel">
					<button onclick="exportGrid();" class="btn-export">导出</button>
					<button class="btn-export" onclick="importUtil.open();">导入</button>
			</div>
			<form action="" id="downloadForm" name="downloadForm" method="post" target="downloadWin" style="display: none;">
             </form>
            <iframe name="downloadWin" id="downloadWin" style="display: none;"></iframe>
			<input type="hidden" id="currentItemId" value="">
			<div id="orderDdFw_grid"></div>
			<%@include file="./orderDdfwDetail1.jsp"%>
		</div>
	</div>
</body>

</html>
