var rpcClient = common.rpcClient;


$(document).ready(function(){
	initTab();
/*	bindBackspaceUrl('business/partnerorder/partnerOrderManageNew.jsp?cacheParams=true&toItem=' + partnerOrderId);
	orderDetail(partnerOrderId);*/
	initPartnerCtrl();
	getPartnerProductPriceRulePower();
	
	getProductDetail(sku);
	buildladderPriceGrid();
	
	getKAPriceRulePower();
	buildKAPriceGrid();
	
	getPCPriceRulePower();
	buildPCPriceGrid();
});

function initTab(){
	BUI.use(['bui/tab','bui/mask'],function(Tab){
		var tab = new Tab.TabPanel({
			render : '#tabs',
			elCls : 'nav-tabs',
			panelContainer : '#tab_content',//如果内部有容器，那么会跟标签项一一对应，如果没有会自动生成
			autoRender: true,
			children:[
				{title:'进货价格',value:'1'},
				/*{title:'KA价格',value:'2'},*/
				{title:'促销活动',value:'2'}
			]
		});
		tab.setSelected(tab.getItemAt(0));
		tab.on('selectedchange', function(ev){
			var item = ev.item,_value = item.get('value'),_text = item.get('text');
			$(window).trigger('resize');
		});
	});
}

//阶梯价格相关js------------------
var createDialog;
var grid = null;
var gridDetail = null;
var priceStore = null;
var productName = null;
var productGuidingPrice = null;
var productPriceRulePower = null;

function initPartnerCtrl(){
	var partnerCtrl = Ctrls.PartnerAutoSelect.init('#partnerId', {
		params: {resourceId: 'partnerPage',spResource: true},
		placeholder: '全部'
		
	});
}
var partnerInsertCtrl = null;
function initPartnerInsertCtrl(){
	if(partnerInsertCtrl == null){
		partnerInsertCtrl = Ctrls.PartnerAutoSelect.init('#partnerIdInsert', {
			params: {resourceId: 'partnerPage',spResource: true},
			placeholder: '全部'
			
		});
	}
}
function getPartnerProductPriceRulePower(){
	rpcClient.call("partnerProductPriceService.getPartnerProductPriceRulePower", [], function (result) {
		var partnerProductPriceRulePower = result.data;
		if(partnerProductPriceRulePower == "off"){
			/*$("#queryPanel").hide();*/
			$("#partnerInsertDiv").hide();
			$("#partnerUpdateDiv").hide();
		}
		
	});
}
function getProductDetail(sku){
	rpcClient.call("productServiceImpl.getProduct", [sku], function (result) {
		objp = result.productVo;	
		productName = objp.name;
		productGuidingPrice = objp.boxPrice;
		if(productGuidingPrice == null){
			productGuidingPrice = '';
		}
		$('#productDetailDiv #sku').text(sku);
		$('#productDetailDiv #productName').text(productName);
		$('#productDetailDiv #salePrice').text(productGuidingPrice);
		
		$('#createProductPriceForm #skuTitle').text(sku);
		$('#createProductPriceForm #productSku').text(sku);
		$('#createProductPriceForm #productGuidingPrice').text(productGuidingPrice);
		$('#createProductPriceForm #productName').text(productName);
		
		$('#createKAPriceForm #skuTitle').text(sku);
		$('#createKAPriceForm #productSku').text(sku);
		$('#createKAPriceForm #productGuidingPrice').text(productGuidingPrice);
		$('#createKAPriceForm #productName').text(productName);
	});
}
function returnUrl(){
	window.history.back(-1);
}
function buildladderPriceGrid() {
	$('#ladderPriceGrid').empty();
	BUI.use(
		[ 'bui/grid', 'bui/data' ],
		function(Grid, Data) {
			var columns = [
                { title: 'sku', dataIndex: 'productSku', width: '10%' },
                { title: '产品名称', dataIndex: 'productName', width: '25%' },
				{ title: '合伙人名称', dataIndex: 'partnerName', width: '10%' },
				/*{ title: '产品数量下限', dataIndex: 'productMinAmount', width: '10%' },
				{ title: '产品数量上限', dataIndex: 'productMaxAmount', width: '10%' },*/
				{ title: '牌价(含税)', dataIndex: 'productGuidingPrice', width: '10%' },
				{ title: '进货价(含税)', dataIndex: 'ladderPrice', width: '10%' },
				{ title: '是否抵扣', dataIndex: 'deductible', width: '10%',renderer: function (value, obj, index) {
						var deductible = '是';
						if(value == "0"){
							deductible = '否';
						}
						return deductible;
					}
				},
				{ title: '备注', dataIndex: 'remark', width: '15%' },
				{
					title: '操作', dataIndex: '', sortable: false, width: '100px', renderer: function (value, obj, index) {
						var updateStr = "<a href='javascript:void(0);' class='updateBtn' onclick='updateProductPrice(" + index + ")'>修改</a>";
						var deleteStr = "<a href='javascript: void(0);' class='delbtn' onclick='deleteProductPrice(" + obj.id + ")'>删除</a>";
						return updateStr + "&nbsp;" + deleteStr;
					}
				}
			];
			priceStore = new Data.Store({
				url: '/productprice/queryProductPrice.do',
				autoLoad:true,
				totalProperty : 'totalRecord',
				root: "resultLst",
				remoteSort: true,
				sortField: 'createTime',
				sortDirection: 'DESC',
			    proxy : {
					method : 'post'
				},
				params: {productSku:sku},
				pageSize:10
			});
			var editing = new Grid.Plugins.CellEditing();
			grid = new Grid.Grid({
				render : '#ladderPriceGrid',
				// width : window.rightWidth,
				columns : columns,
				idField : 'id',
				store : priceStore,
				plugins : [ Grid.Plugins.AutoFit], // 插件形式引入多选表格
				bbar : {
					pagingBar : true
				}
			});
			grid.render();
			common.initPageSizeBar(grid);
		});

}
//创建产品价格规则
function createProductPriceBtn() {
    $('#createProductPriceForm #partnerId').val("");
    $('#createProductPriceForm #productMinAmount').val("");
    $('#createProductPriceForm #productMaxAmount').val("");
    $('#createProductPriceForm #ladderPrice').val("");
    $('#createProductPriceForm #remark').val("");
    $("#createProductPriceForm input[name='deductible'][value='1']").prop("checked", true);
    if (createDialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	createDialog = new Overlay.Dialog({
                title: "创建价格规则",
                width: 480,
                height: 450,
                //配置DOM容器的编号
                contentId: 'createContent',
                success: function () {
                	createProductPriceSubmit();
                }
            });
        });
    }
    createDialog.show();
    initPartnerInsertCtrl();
    partnerInsertCtrl.empty();
    
}
//修改产品价格规则
var updateDialog = null;
function updateProductPrice(index) {
	var productPrictObj = grid.getItemAt(index); 
	$('#updateProductPriceForm #id').val(productPrictObj.id);
	$('#updateProductPriceForm #productName').text(productPrictObj.productName);
	$('#updateProductPriceForm #productSku').text(productPrictObj.productSku);
	$('#updateProductPriceForm #productGuidingPrice').text(productPrictObj.productGuidingPrice);
    $('#updateProductPriceForm #partnerName').text(productPrictObj.partnerName);
    /*$('#updateProductPriceForm #productMinAmount').val(productPrictObj.productMinAmount);
    $('#updateProductPriceForm #productMaxAmount').val(productPrictObj.productMaxAmount);*/
    $('#updateProductPriceForm #ladderPrice').val(productPrictObj.ladderPrice);
    $('#updateProductPriceForm #remark').val(productPrictObj.remark);
    $("#updateProductPriceForm input[name='deductible'][value=" + productPrictObj.deductible + "]").prop("checked", true);
    if (updateDialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	updateDialog = new Overlay.Dialog({
                title: "修改价格规则",
                width: 480,
                height: 550,
                //配置DOM容器的编号
                contentId: 'updateContent',
                success: function () {
                	updateProductPriceSubmit();
                }
            });
        });
    }
    updateDialog.show();
}

//创建价格规则
function createProductPriceSubmit() {
    var productPriceVo = {};
    productPriceVo.productSku = sku;
    productPriceVo.partnerId =  $('#partnerIdInsert').val();
    /*productPriceVo.productMinAmount = $('#createProductPriceForm #productMinAmount').val();
    productPriceVo.productMaxAmount = $('#createProductPriceForm #productMaxAmount').val();*/
    productPriceVo.ladderPrice = $('#createProductPriceForm #ladderPrice').val();
    productPriceVo.remark = $('#createProductPriceForm #remark').val();
    productPriceVo.deductible = $("#createProductPriceForm input[name='deductible']:checked").val();
    LoadMask.show();
    rpcClient.call("partnerProductPriceService.insertSelective", [productPriceVo],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
                createDialog.close();
                common.alertMes("新建成功!", "info");
                queryProductPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
                createDialog.close();
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
            createDialog.close();
        });
}
//修改价格规则
function updateProductPriceSubmit() {
    var productPriceVo = {};
    productPriceVo.id = $('#updateProductPriceForm #id').val();
    /*productPriceVo.productMinAmount = $('#updateProductPriceForm #productMinAmount').val();
    productPriceVo.productMaxAmount = $('#updateProductPriceForm #productMaxAmount').val();*/
    productPriceVo.ladderPrice = $('#updateProductPriceForm #ladderPrice').val();
    productPriceVo.remark = $('#updateProductPriceForm #remark').val();
    productPriceVo.deductible = $("#updateProductPriceForm input[name='deductible']:checked").val();
    LoadMask.show();
    rpcClient.call("partnerProductPriceService.updateByExampleSelective", [productPriceVo],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
                updateDialog.close();
                common.alertMes("修改成功!", "info");
                queryProductPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
                updateDialog.close();
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
            updateDialog.close();
        });
}
//删除
function deleteProductPrice(id){
	LoadMask.show();
    rpcClient.call("partnerProductPriceService.deleteById", [id],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
                common.alertMes("删除成功!", "info");
                queryProductPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
        });
}
// 模糊查询
function queryProductPriceByParams() {
	var partnerId = $('#partnerId').val();
	priceStore.load({
		productSku:sku,
		partnerId: partnerId
	});
}

//------KA价格----

var createKADialog;
var gridKA = null;
var gridKADetail = null;
var priceKAStore = null;
var productName = null;
var productGuidingPrice = null;
var productPriceRulePower = null;

var partnerKAInsertCtrl = null;
function initKAPartnerInsertCtrl(){
	if(partnerKAInsertCtrl == null){
		partnerKAInsertCtrl = Ctrls.PartnerAutoSelect.init('#kapartnerIdInsert', {
			params: {resourceId: null},
			placeholder: '全部'
		});
	}
}
function getKAPriceRulePower(){
	rpcClient.call("kapartnerProductPriceService.getKapartnerProductPriceRulePower", [], function (result) {
		var kapartnerProductPriceRulePower = result.data;
		if(kapartnerProductPriceRulePower == "off"){
			/*$("#queryPanel").hide();*/
			$("#kapartnerInsertDiv").hide();
			$("#kapartnerUpdateDiv").hide();
		}
		
	});
}
function buildKAPriceGrid() {
	$('#kaPriceGrid').empty();
	BUI.use(
		[ 'bui/grid', 'bui/data' ],
		function(Grid, Data) {
			var columns = [
                { title: 'sku', dataIndex: 'productSku', width: '10%' },
                { title: '产品名称', dataIndex: 'productName', width: '25%' },
				{ title: '合伙人名称', dataIndex: 'partnerName', width: '10%' },
				{ title: '牌价(含税)', dataIndex: 'productGuidingPrice', width: '10%' },
				{ title: '进货(含税)', dataIndex: 'basePrice', width: '10%' },
				{ title: '折扣价格', dataIndex: 'discountPrice', width: '10%' },
				{ title: '开始时间', dataIndex: 'startTimeStr', width: '10%' },
				{ title: '结束时间', dataIndex: 'endTimeStr', width: '10%' },
				{ 	title: '状态', dataIndex: 'status', width: '10%',renderer: function (value, obj, index) {
						if(value == '1'){
							return "启用";
						}
						return "停用";
					} 
				},
				{ title: '备注', dataIndex: 'remark', width: '15%' },
				{
					title: '操作', dataIndex: '', sortable: false, width: '100px', renderer: function (value, obj, index) {
						var updateStr = "<a href='javascript:void(0);' class='updateBtn' onclick='updateKAPrice(" + index + ")'>修改</a>";
						var deleteStr = "<a href='javascript: void(0);' class='delbtn' onclick='deleteKAPrice(" + obj.id + ")'>删除</a>";
						return updateStr + "&nbsp;" + deleteStr;
					}
				}
			];
			priceKAStore = new Data.Store({
				url: '/kaproductprice/queryProductPrice.do',
				autoLoad:true,
				totalProperty : 'totalRecord',
				root: "resultLst",
				remoteSort: true,
				sortField: 'createTime',
				sortDirection: 'DESC',
			    proxy : {
					method : 'post'
				},
				params: {productSku:sku},
				pageSize:10
			});
			var editing = new Grid.Plugins.CellEditing();
			gridKA = new Grid.Grid({
				render : '#kaPriceGrid',
				// width : window.rightWidth,
				columns : columns,
				idField : 'id',
				store : priceKAStore,
				plugins : [ Grid.Plugins.AutoFit], // 插件形式引入多选表格
				bbar : {
					pagingBar : true
				}
			});
			gridKA.render();
			common.initPageSizeBar(gridKA);
		});

}

//创建产品价格规则
function createKAPriceBtn() {
    $('#createKAPriceForm #partnerId').val("");
    $('#createKAPriceForm #basePrice').val("");
    $('#createKAPriceForm #discountPrice').val("");
    $('#createKAPriceForm #startTime').val("");
    $('#createKAPriceForm #endTime').val("");
    $("#createKAPriceForm input[name='status'][value='1']").prop("checked", true);
    $('#createKAPriceForm #remark').val("");
    if (createKADialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	createKADialog = new Overlay.Dialog({
                title: "创建价格规则",
                width: 480,
                height: 650,
                //配置DOM容器的编号
                contentId: 'createKAPriceContent',
                success: function () {
                	createKAPriceSubmit();
                }
            });
        });
    }
    createKADialog.show();
    initKAPartnerInsertCtrl();
    partnerKAInsertCtrl.empty();
    
}
//修改产品价格规则
var updateKApriceDialog = null;
function updateKAPrice(index) {
	var kaPriceObj = gridKA.getItemAt(index); 
	$('#updateKAPriceForm #id').val(kaPriceObj.id);
	$('#updateKAPriceForm #productName').text(kaPriceObj.productName);
	$('#updateKAPriceForm #productSku').text(kaPriceObj.productSku);
	$('#updateKAPriceForm #productGuidingPrice').text(kaPriceObj.productGuidingPrice);
    $('#updateKAPriceForm #partnerName').text(kaPriceObj.partnerName);
    $('#updateKAPriceForm #basePrice').val(kaPriceObj.basePrice);
    $('#updateKAPriceForm #discountPrice').val(kaPriceObj.discountPrice);
    $('#updateKAPriceForm #startTime').val(kaPriceObj.startTimeStr);
    $('#updateKAPriceForm #endTime').val(kaPriceObj.endTimeStr);
    $("#updateKAPriceForm input[name='status'][value=" + kaPriceObj.status + "]").prop("checked", true);
    $('#updateKAPriceForm #remark').val(kaPriceObj.remark);
    if (updateKApriceDialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	updateKApriceDialog = new Overlay.Dialog({
                title: "修改价格规则",
                width: 480,
                height: 650,
                //配置DOM容器的编号
                contentId: 'updateKAPriceContent',
                success: function () {
                	updateKAPriceSubmit();
                }
            });
        });
    }
    updateKApriceDialog.show();
}

//创建价格规则
function createKAPriceSubmit() {
    var kaPriceVo = {};
    kaPriceVo.productSku = sku;
    kaPriceVo.partnerId =  $('#createKAPriceForm #partnerIdInsert').val();
    kaPriceVo.basePrice = $('#createKAPriceForm #basePrice').val();
    kaPriceVo.discountPrice = $('#createKAPriceForm #discountPrice').val();
    kaPriceVo.startTime = $('#createKAPriceForm #startTime').val();
    kaPriceVo.endTime = $('#createKAPriceForm #endTime').val();
    kaPriceVo.status = $("#createKAPriceForm input[name='status']:checked").val();
    kaPriceVo.remark = $('#createKAPriceForm #remark').val();
    LoadMask.show();
    rpcClient.call("kapartnerProductPriceService.insertSelective", [kaPriceVo],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
            	createKADialog.close();
                common.alertMes("新建成功!", "info");
                queryKAPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
                createKADialog.close();
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
            createKADialog.close();
        });
}
//修改价格规则
function updateKAPriceSubmit() {
    var kaPriceVo = {};
    kaPriceVo.id = $('#updateKAPriceForm #id').val();
    kaPriceVo.basePrice = $('#updateKAPriceForm #basePrice').val();
    kaPriceVo.discountPrice = $('#updateKAPriceForm #discountPrice').val();
    kaPriceVo.startTime = $('#updateKAPriceForm #startTime').val();
    kaPriceVo.endTime = $('#updateKAPriceForm #endTime').val();
    kaPriceVo.status = $("#updateKAPriceForm input[name='status']:checked").val();
    kaPriceVo.remark = $('#updateKAPriceForm #remark').val();
    LoadMask.show();
    rpcClient.call("kapartnerProductPriceService.updateByExampleSelective", [kaPriceVo],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
            	updateKApriceDialog.close();
                common.alertMes("修改成功!", "info");
                queryKAPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
                updateKApriceDialog.close();
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
            updateKApriceDialog.close();
        });
}
//删除
function deleteKAPrice(id){
	LoadMask.show();
    rpcClient.call("kapartnerProductPriceService.deleteById", [id],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
                common.alertMes("删除成功!", "info");
                queryKAPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
        });
}
// 模糊查询
function queryKAPriceByParams() {
	var partnerId = $('#partnerId').val();
	priceKAStore.load({
		productSku:sku,
		partnerId: partnerId
	});
}

//-------KA价格-----end

var gridPC = null;
var gridPCDetail = null;
var pricePCStore = null;

function buildPCPriceGrid() {
	$('#promotinalCampaignGrid').empty();
	BUI.use(
		[ 'bui/grid', 'bui/data' ],
		function(Grid, Data) {
			var columns = [
                /*{ title: 'sku', dataIndex: 'productSku', width: '10%' },*/
                { title: '促销名称', dataIndex: 'promotionTitle',sortable: false, width: '12%' },
				{ title: '合伙人名称', dataIndex: 'partnerName',sortable: false, width: '10%' },
				{ title: '促销类型', dataIndex: 'typeStr', sortable: false,width: '10%' },
				{ title: '起订量', dataIndex: 'conditionCount',sortable: false, width: '10%' },
				{ title: '买赠比例', dataIndex: 'proportionCount', sortable: false,width: '10%' },
				{ title: '赠送产品SKU', dataIndex: 'bz2',sortable: false, width: '10%' },
				{ title: '赠送数量', dataIndex: 'giftCount',sortable: false, width: '10%' },
				{ title: '赠品价格', dataIndex: 'bz3',sortable: false, width: '10%' },
				{ title: '最低起订量', dataIndex: 'productMinAmount',sortable: false, width: '10%' },
				{ title: '最高起订量', dataIndex: 'productMaxAmount', sortable: false,width: '10%' },
				{ title: '活动价格', dataIndex: 'ladderPrice', sortable: false,width: '10%' },
				{ title: '开始时间', dataIndex: 'startTimeStr',sortable: false, width: '10%' },
				{ title: '结束时间', dataIndex: 'endTimeStr',sortable: false, width: '10%' },
				{ 	title: '是否抵扣', dataIndex: 'bz1',sortable: false, width: '10%',renderer: function (value, obj, index) {
						if(value == '1'){
							return "参加抵扣";
						}
						return "不参加抵扣";
					} 
				},
				{ 	title: '状态', dataIndex: 'status', sortable: false,width: '8%',renderer: function (value, obj, index) {
						if(value == '1'){
							return "启用";
						}
						return "停用";
					} 
				},
				/*{ title: '备注', dataIndex: 'remark', sortable: false,width: '15%' },*/
				{
					title: '操作', dataIndex: '', sortable: false, width: '100px', renderer: function (value, obj, index) {
						var updateStr = "<a href='javascript:void(0);' class='updateBtn' onclick='updatePCPrice(" + index + ")'>修改</a>";
						var deleteStr = "<a href='javascript: void(0);' class='delbtn' onclick='deletePCPrice(" + obj.id + ")'>删除</a>";
						return updateStr + "&nbsp;" + deleteStr;
					}
				}
			];
			pricePCStore = new Data.Store({
				url: '/pcproductprice/queryPromotionalCampaign.do',
				autoLoad:true,
				totalProperty : 'totalRecord',
				root: "resultLst",
				remoteSort: true,
				sortField: 'createTime',
				sortDirection: 'DESC',
			    proxy : {
					method : 'post'
				},
				params: {productSku:sku},
				pageSize:10
			});
			var editing = new Grid.Plugins.CellEditing();
			gridPC = new Grid.Grid({
				render : '#promotinalCampaignGrid',
				// width : window.rightWidth,
				columns : columns,
				idField : 'id',
				store : pricePCStore,
				plugins : [ Grid.Plugins.AutoFit], // 插件形式引入多选表格
				bbar : {
					pagingBar : true
				}
			});
			gridPC.render();
			common.initPageSizeBar(gridPC);
		});

}

//促销类型相关
var promotionCampaignTypeCtrl = null;
function initPromotionCampaignTypeCtrl() {
	if(promotionCampaignTypeCtrl == null){
	var items = [];	
	rpcClient.call("dicService.getDicItemByDicTypeCode",
			['pricepromotioncampaign.type'],
			function (result) {
				if (result.code == 'success') {
					for(var i = 0; i < result.data.length; i++){
						
						var item = result.data[i];
						items.push({
							text : item.dicItemName,
							value : item.dicItemCode
						});
					}
					BUI.use('bui/select',function(Select){
						promotionCampaignTypeCtrl = new Select.Select({  
					          render:'#typeInsert_c',
					          valueField:'#typeInsert',
					          elCls: 'control-select',
					          elStyle: {'white-space': 'nowrap'},
					          multipleSelect:false,
					          items:items
					  	});
						promotionCampaignTypeCtrl.render();
						promotionCampaignTypeCtrl.on('change', function(e){							
							if(e.value == 'fullgift'){
								$("#createPCPriceForm #sendTypeDiv").show();
								$("#createPCPriceForm #ladderTypeDiv").hide();
								$("#createPCPriceForm #bz2").hide();
							}else if(e.value == 'fullgiftothersku'){
								$("#createPCPriceForm #sendTypeDiv").show();
								$("#createPCPriceForm #ladderTypeDiv").hide();
								$("#createPCPriceForm #bz2").show();
								$("#createPCPriceForm #bz3").show();
							}else{
								$("#createPCPriceForm #sendTypeDiv").hide();
								$("#createPCPriceForm #ladderTypeDiv").show();
							}
						});
					});					
				} else {
					common.alertMes('加载促销活动类型失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
	}
}

var partnerPCInsertCtrl = null;
function initPCInsertCtrl(){
	if(partnerPCInsertCtrl == null){
		partnerPCInsertCtrl = Ctrls.PartnerAutoSelect.init('#pcpartnerIdInsert', {
			params: {resourceId: null},
			placeholder: '全部'
		});
	}
}
function getPCPriceRulePower(){
	rpcClient.call("promotionalCampaignService.getPromotionalCampaignRulePower", [], function (result) {
		var pcPriceRulePower = result.data;
		if(pcPriceRulePower == "off"){
			$("#pcpartnerInsertDiv").hide();
			$("#pcpartnerUpdateDiv").hide();
		}
		
	});
}

//创建产品价格规则
var createPCDialog = null; 
function createPCPriceBtn() {
    $('#createPCPriceForm #partnerId').val("");
    $('#createPCPriceForm #promotionTitle').val("");
    $('#createPCPriceForm #type').val("");
    $('#createPCPriceForm #conditionCount').val("");
    $('#createPCPriceForm #proportionCount').val("");
    $('#createPCPriceForm #giftCount').val("");
    $('#createPCPriceForm #productMinAmount').val("");
    $('#createPCPriceForm #productMaxAmount').val("");
    $('#createPCPriceForm #ladderPrice').val("");
    $('#createPCPriceForm #startTime').val("");
    $('#createPCPriceForm #endTime').val("");
    $('#createPCPriceForm #bz2').val("");
    $('#createPCPriceForm #bz3').val("");
    $("#createPCPriceForm input[name='bz1'][value='1']").prop("checked", true);
    $("#createPCPriceForm input[name='status'][value='1']").prop("checked", true);
    $('#createPCPriceForm #remark').val("");
    if (createPCDialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	createPCDialog = new Overlay.Dialog({
                title: "创建促销活动规则",
                width: 800,
                height: 550,
                //配置DOM容器的编号
                contentId: 'createPCPriceContent',
                success: function () {
                	createPCPriceSubmit();
                }
            });
        });
    }
    $("#sendTypeDiv").hide();
	$("#ladderTypeDiv").hide();
    createPCDialog.show();
    initPCInsertCtrl();   
    initPromotionCampaignTypeCtrl();
    partnerPCInsertCtrl.empty();
    
}
//创建价格规则
function createPCPriceSubmit() {
    var pcPriceVo = {};
    pcPriceVo.productSku = sku;
    pcPriceVo.promotionTitle =  $('#createPCPriceForm #promotionTitle').val();
    pcPriceVo.partnerId =  $('#createPCPriceForm #pcpartnerIdInsert').val();
    pcPriceVo.type = $("#createPCPriceForm input[name=typeInsert]").val();
    pcPriceVo.conditionCount = $('#createPCPriceForm #conditionCount').val();
    pcPriceVo.proportionCount = $('#createPCPriceForm #proportionCount').val();
    pcPriceVo.giftCount = $('#createPCPriceForm #giftCount').val();
    pcPriceVo.productMinAmount = $('#createPCPriceForm #productMinAmount').val();
    pcPriceVo.productMaxAmount = $('#createPCPriceForm #productMaxAmount').val();
    pcPriceVo.ladderPrice = $('#createPCPriceForm #ladderPrice').val();
    pcPriceVo.startTime = $('#createPCPriceForm #startTime').val();
    pcPriceVo.endTime = $('#createPCPriceForm #endTime').val();
    pcPriceVo.bz2 = $('#createPCPriceForm #bz2').val();
    pcPriceVo.bz3 = $('#createPCPriceForm #bz3').val();
    pcPriceVo.bz1 = $("#createPCPriceForm input[name='bz1']:checked").val();
    pcPriceVo.status = $("#createPCPriceForm input[name='status']:checked").val();
    pcPriceVo.remark = $('#createPCPriceForm #remark').val();
        
    LoadMask.show();
    rpcClient.call("promotionalCampaignService.insertSelective", [pcPriceVo],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
            	createPCDialog.close();
                common.alertMes("新建成功!", "info");
                queryPCPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
                createPCDialog.close();
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
            createPCDialog.close();
        });
}

//修改产品价格规则
var updatePCpriceDialog = null;
function updatePCPrice(index) {
	var pcPriceObj = gridPC.getItemAt(index); 
	$('#updatePCPriceForm #id').val(pcPriceObj.id);
	$('#updatePCPriceForm #promotionTitle').text(pcPriceObj.promotionTitle);
	$('#updatePCPriceForm #productName').text(pcPriceObj.productName);
	$('#updatePCPriceForm #productSku').text(pcPriceObj.productSku);
    $('#updatePCPriceForm #partnerName').text(pcPriceObj.partnerName);
    
    $("#updatePCPriceForm #type").text(pcPriceObj.typeStr);
    if(pcPriceObj.type == 'fullgift'){
		$("#updatePCPriceForm #sendTypeDiv").show();
		$("#updatePCPriceForm #ladderTypeDiv").hide();
		$("#updatePCPriceForm #bz2").hide();
		$("#updatePCPriceForm #bz3").hide();
	}else if(pcPriceObj.type == 'fullgiftothersku'){
		$("#updatePCPriceForm #sendTypeDiv").show();
		$("#updatePCPriceForm #ladderTypeDiv").hide();
		$("#updatePCPriceForm #bz2").show();
		$("#updatePCPriceForm #bz3").show();
	}else{
		$("#updatePCPriceForm #sendTypeDiv").hide();
		$("#updatePCPriceForm #ladderTypeDiv").show();
	}
    $('#updatePCPriceForm #conditionCount').text(pcPriceObj.conditionCount);
    $('#updatePCPriceForm #proportionCount').text(pcPriceObj.proportionCount);
    $('#updatePCPriceForm #giftCount').text(pcPriceObj.giftCount);
    $('#updatePCPriceForm #productMinAmount').text(pcPriceObj.productMinAmount);
    if(pcPriceObj.productMaxAmount != null && pcPriceObj.productMaxAmount != 'null'){
    	$('#updatePCPriceForm #productMaxAmount').text(pcPriceObj.productMaxAmount);
    }
    if(pcPriceObj.ladderPrice != null){
    	$('#updatePCPriceForm #ladderPrice').text(pcPriceObj.ladderPrice);
    }
    $('#updatePCPriceForm #startTime').text(pcPriceObj.startTimeStr);
    $('#updatePCPriceForm #endTime').text(pcPriceObj.endTimeStr);
    $('#updatePCPriceForm #bz2').text(pcPriceObj.bz2);
    $('#updatePCPriceForm #bz3').text(pcPriceObj.bz3);
    $("#updatePCPriceForm input[name='bz1'][value=" + pcPriceObj.bz1 + "]").prop("checked", true);
    $("#updatePCPriceForm input[name='status'][value=" + pcPriceObj.status + "]").prop("checked", true);
    $('#updatePCPriceForm #remark').val(pcPriceObj.remark);
    
    if (updatePCpriceDialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	updatePCpriceDialog = new Overlay.Dialog({
                title: "修改促销规则",
                width: 800,
                height: 470,
                //配置DOM容器的编号
                contentId: 'updatePCPriceContent',
                success: function () {
                	updatePCPriceSubmit();
                }
            });
        });
    }
    updatePCpriceDialog.show();
}
//修改价格规则
function updatePCPriceSubmit() {
	var pcPriceVo = {};
	pcPriceVo.id = $('#updatePCPriceForm #id').val();
	/*pcPriceVo.conditionCount = $('#updatePCPriceForm #conditionCount').val();
	pcPriceVo.proportionCount = $('#updatePCPriceForm #proportionCount').val();
	pcPriceVo.giftCount = $('#updatePCPriceForm #giftCount').val();
	pcPriceVo.productMinAmount = $('#updatePCPriceForm #productMinAmount').val();
	pcPriceVo.productMaxAmount = $('#updatePCPriceForm #productMaxAmount').val();
	pcPriceVo.ladderPrice = $('#updatePCPriceForm #ladderPrice').val();
    pcPriceVo.startTime = $('#updatePCPriceForm #startTime').val();
    pcPriceVo.endTime = $('#updatePCPriceForm #endTime').val();
    pcPriceVo.bz1 = $("#updatePCPriceForm input[name='bz1']:checked").val();
    pcPriceVo.bz2 = $('#updatePCPriceForm #bz2').val();*/
    pcPriceVo.status = $("#updatePCPriceForm input[name='status']:checked").val();
    pcPriceVo.remark = $('#updatePCPriceForm #remark').val();
    LoadMask.show();
    rpcClient.call("promotionalCampaignService.updateByExampleSelective", [pcPriceVo],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
            	updatePCpriceDialog.close();
                common.alertMes("修改成功!", "info");
                queryPCPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
                updatePCpriceDialog.close();
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
            updatePCpriceDialog.close();
        });
}

//删除
function deletePCPrice(id){
	LoadMask.show();
    rpcClient.call("promotionalCampaignService.deleteById", [id],
        function (result) {
            LoadMask.hide();
            if (result.code == "success") {
                common.alertMes("删除成功!", "info");
                queryPCPriceByParams();
            } else if (result.code == "error") {
                common.alertMes(result.msg, "error");
            } else {
                common.alertMes("系统错误", "error");
            }
            LoadMask.hide();
        }, function (error) {
            common.ajaxTimeout(error);
            LoadMask.hide();
        });
}

//模糊查询
function queryPCPriceByParams() {
	var partnerId = $('#partnerId').val();
	pricePCStore.load({
		productSku:sku,
		partnerId: partnerId
	});
}
