package com.chevron.sop.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chevron.mktresource.model.MktDealerQuarter;
import com.chevron.mktresource.service.MktResourceCostBudgetService;
import com.chevron.sop.dao.SopAmountAllotMapper;
import com.chevron.sop.dao.SopOtherSupportDetailMapper;
import com.chevron.sop.dao.SopSupportAdjustApplyMapper;
import com.chevron.sop.dao.SopSupportFieldHisMapper;
import com.chevron.sop.dao.SopSupportSummaryMapper;
import com.chevron.sop.model.SopAmountAllot;
import com.chevron.sop.model.SopAmountAllotExample;
import com.chevron.sop.model.SopOtherSupportDetail;
import com.chevron.sop.model.SopSupportAdjustApply;
import com.chevron.sop.model.SopSupportAdjustApplyExample;
import com.chevron.sop.model.SopSupportFieldHis;
import com.chevron.sop.model.SopSupportFieldQuery;
import com.chevron.sop.model.SopSupportParams;
import com.chevron.sop.model.SopSupportSummary;
import com.chevron.sop.model.SopSupportSummaryExample;
import com.chevron.sop.model.enums.SopApplyStatus;
import com.chevron.sop.model.enums.SopPartnerSummaryStatus;
import com.chevron.sop.service.SopService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.organization.service.PartnerCtrlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * @author: chenBin
 * @date: 29/06/2020
 */
@Service
public class SopServiceImpl implements SopService {

    private static final Logger log = LoggerFactory.getLogger(SopServiceImpl.class);

    @Autowired
    private SopOtherSupportDetailMapper sopOtherSupportDetailMapper;
    @Autowired
    private SopSupportSummaryMapper sopSupportSummaryMapper;
    @Autowired
    private SopSupportFieldHisMapper sopSupportFieldHisMapper;
    @Autowired
    private MktResourceCostBudgetService mktResourceCostBudgetService;
    @Autowired
    private SopSupportAdjustApplyMapper sopSupportAdjustApplyMapper;
    @Autowired
    private OperationPermissionBizService operationPermissionBizService;
    @Autowired
    private PartnerCtrlService partnerCtrlService;
    @Autowired
    private SopAmountAllotMapper sopAmountAllotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SopOtherSupportDetail addSopOtherAmountSupport(SopOtherSupportDetail sopOtherSupportDetail) throws WxPltException {
        String targetMonth = sopOtherSupportDetail.getTargetMonth();
        String quarter = null;
        if(sopOtherSupportDetail.getPartnerId() == null || sopOtherSupportDetail.getAmount() == null || sopOtherSupportDetail.getTargetMonth() == null
        || sopOtherSupportDetail.getRemarkCode() == null || sopOtherSupportDetail.getChannel() == null){
            throw new WxPltException("参数不全");
        }
        try {
            Date date = DateUtil.parseDate(targetMonth, "yyyy-MM");
            quarter = DateUtil.getQuarter(date);
        } catch (Exception e) {
            e.printStackTrace();
            throw new WxPltException("调整日期不正确");
        }
        sopOtherSupportDetail.setQuarter(quarter);
        Long userId = ContextUtil.getCurUserId();
        sopOtherSupportDetail.setUpdateUserId(userId);
        sopOtherSupportDetail.setCreateUserId(userId);
        sopOtherSupportDetail.setCreateTime(new Date());
        sopOtherSupportDetail.setUpdateTime(new Date());
        sopOtherSupportDetailMapper.insertSelective(sopOtherSupportDetail);
        return sopOtherSupportDetail;
    }

    @Override
    public List<SopOtherSupportDetail> sopOtherAmountSupportHistory(SopSupportParams params) {
        params.setField("id");
        return sopOtherSupportDetailMapper.sopOtherAmountSupportHistory(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SopSupportSummary editSopPartnerSummary(SopSupportSummary sopSupportSummary) throws WxPltException {
        Long userId = ContextUtil.getCurUserId();
        List<SopSupportFieldHis> fieldHis = new ArrayList<SopSupportFieldHis>();
        //先取出旧数据
        SopSupportSummary oldData = this.getOldData(sopSupportSummary);
        this.checkSopAmount(sopSupportSummary);
        if(oldData == null){
            sopSupportSummary.setCreateUserId(userId);
            sopSupportSummary.setCreateTime(new Date());
            sopSupportSummary.setStatus(SopPartnerSummaryStatus.DRAFT.getStatus());
            sopSupportSummaryMapper.insertSelective(sopSupportSummary);
        }else{
            sopSupportSummary.setQuarter(null);
            sopSupportSummary.setYear(null);
            sopSupportSummary.setPartnerId(null);
            sopSupportSummary.setChannel(null);
            sopSupportSummary.setId(oldData.getId());
            sopSupportSummary.setUpdateTime(new Date());
            sopSupportSummary.setUpdateUserId(userId);
            sopSupportSummaryMapper.updateByPrimaryKeySelective(sopSupportSummary);
        }
        fieldHis = this.fieldHisList(sopSupportSummary,oldData, userId);
        for (SopSupportFieldHis fieldHi : fieldHis) {
            fieldHi.setSourceId(sopSupportSummary.getId().toString());
        }
        if(CollectionUtil.isNotEmpty(fieldHis)){
            sopSupportFieldHisMapper.insertBatch(fieldHis);
        }
        return sopSupportSummary;
    }

    /**
     * 检查是否超过星火计划预算
     */
    private void checkSopAmount(SopSupportSummary sopSupportSummary) throws WxPltException {
        //先判断星火计划调整金额是否超过了分配金额的总和
        SopAmountAllotExample example = new SopAmountAllotExample();
        example.createCriteria().andPartnerIdEqualTo(sopSupportSummary.getPartnerId()).andSopAmountGreaterThan(0d);
        List<SopAmountAllot> sopAmountAllots = sopAmountAllotMapper.selectByExample(example);
        Double amount = NumberUtil.add(sopSupportSummary.getSignage(), sopSupportSummary.getSeminar(), sopSupportSummary.getOilChangeCar(), sopSupportSummary.getEquipmentTools()).doubleValue();
        if (amount <= 0d) {
            return;
        }
        if (CollectionUtil.isEmpty(sopAmountAllots)) {
            throw new WxPltException("没有分配星火计划的费用");
        }
        SopAmountAllot sopAmountAllot = sopAmountAllots.get(0);
        //获取该经销商所有已经申请的数据
        Double totalAmout = sopSupportSummaryMapper.sumSparkAmountByPartnerId(sopSupportSummary.getPartnerId());
        if (NumberUtil.add(totalAmout, amount) > sopAmountAllot.getSopAmount()) {
            throw new WxPltException("超过星火总预算" + sopAmountAllot.getSopAmount());
        }
    }

    private SopSupportSummary getOldData(SopSupportSummary sopSupportSummary) throws WxPltException {
        SopSupportSummary old = null;
        if (StringUtils.isBlank(sopSupportSummary.getQuarter())) {
            throw new WxPltException("季度参数未填写。");
        }
        if (StringUtils.isBlank(sopSupportSummary.getYear())) {
            throw new WxPltException("日期参数未填写。");
        }
        if (sopSupportSummary.getPartnerId() == null) {
            throw new WxPltException("经销商未选择。");
        }
        if (StringUtils.isBlank(sopSupportSummary.getChannel())) {
            throw new WxPltException("请选择渠道。");
        }
        SopSupportSummaryExample example = new SopSupportSummaryExample();
        example.createCriteria().andChannelEqualTo(sopSupportSummary.getChannel()).andPartnerIdEqualTo(sopSupportSummary.getPartnerId())
                .andQuarterEqualTo(sopSupportSummary.getQuarter()).andYearEqualTo(sopSupportSummary.getYear());
        List<SopSupportSummary> sopSupportSummaries = sopSupportSummaryMapper.selectByExample(example);
        if (sopSupportSummaries.size() > 1) {
            throw new WxPltException("该经销商本季度数据存在多条，请联系管理员");
        }
        if (sopSupportSummaries.size() == 1) {
            old = sopSupportSummaries.get(0);
        }
        if (old != null && old.getStatus() > SopPartnerSummaryStatus.SUBMITTED.getStatus()) {
            throw new WxPltException("已经确认的数据无法编辑。");
        }
        return old;
    }

    @Override
    public void sopPartnerSummaryRecall(Long id) throws WxPltException {
        //查询提交记录如果不是提交状态无法撤回
        SopSupportSummary sopSupportSummary = sopSupportSummaryMapper.selectByPrimaryKey(id);
        if(sopSupportSummary==null){
            throw new WxPltException("记录不存在。");
        }
        if(!SopPartnerSummaryStatus.SUBMITTED.getStatus().equals(sopSupportSummary.getStatus())){
            throw new WxPltException("只有已提交状态可以撤回。");
        }
        SopSupportSummary update = new SopSupportSummary();
        update.setId(id);
        update.setStatus(SopPartnerSummaryStatus.DRAFT.getStatus());
        sopSupportSummaryMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public List<SopSupportSummary> findSopPartnerSummary(SopSupportParams sopSupportParams) throws WxPltException {
        sopSupportParams.setField("id");
        WxTUser curUser = ContextUtil.getCurUser();
        sopSupportParams.setUserId(curUser.getUserId());
        //如果partnerId为空查询的是列表数据需要判断权限
//        if(sopSupportParams.getPartnerId() == null){
//            int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "ctrl.indirect.dealer");
//            if((permissionWeight & 1) == 1 || permissionWeight == -1){
//                //看所有的不处理
//            }else if(((permissionWeight & 8) > 0)){
//                //按照region查看
//                //sopSupportParams.setRegionFlag(true);
//            }
//        }
        //这里需要判断是否是列表查询 如果是列表查询需要按照经销商分页查询
        List<SopSupportSummary> sopPartnerSummary = null;
        if(sopSupportParams.getPartnerId()!=null){
            sopPartnerSummary = sopSupportSummaryMapper.findSopPartnerSummary(sopSupportParams);
        }else{
            //先调用分页查询出经销商ID 再去查询所有这些经销商的数据
            List<SopSupportSummary> pagePartnerIds = sopSupportSummaryMapper.getPagePartnerIds(sopSupportParams);
            if(CollectionUtil.isEmpty(pagePartnerIds)){
                return new ArrayList<SopSupportSummary>();
            }
            ArrayList<Long> ids = new ArrayList<Long>();
            for (SopSupportSummary pagePartnerId : pagePartnerIds) {
                ids.add(pagePartnerId.getPartnerId());
            }
            SopSupportParams pageQuery = new SopSupportParams();
            BeanUtil.copyProperties(sopSupportParams, pageQuery);
            pageQuery.setPartnerIds(ids);
            pageQuery.setPaging(false);
            pageQuery.clearOrderField();
            sopPartnerSummary = sopSupportSummaryMapper.findSopPartnerSummary(pageQuery);
        }
        if (CollectionUtil.isEmpty(sopPartnerSummary) && sopSupportParams.getPartnerId() == null) {
            return sopPartnerSummary;
        }
        Long distributorId = null;
        //如果是单个经销商来查询，如果没有初始值需要返回销量等信息  没有传channel需要补充渠道的数据
        if (sopSupportParams.getPartnerId() != null) {
            distributorId = sopSupportSummaryMapper.getDistributorIdByOrgId(sopSupportParams.getPartnerId());
            this.initData(sopSupportParams, sopPartnerSummary, distributorId);
            this.checkSopAllot(sopSupportParams, sopPartnerSummary);
        }

        //为每一个经销商设置销量信息等
        List<MktDealerQuarter> qbrMktDealerQuarters = this.getMktDealerInfo(sopSupportParams, sopPartnerSummary, distributorId);
        for (SopSupportSummary supportSummary : sopPartnerSummary) {
            for (MktDealerQuarter item : qbrMktDealerQuarters) {
                if (item.getDistributorId().equals(supportSummary.getDistributorId())
                        && item.getSalesChannel().equals(supportSummary.getChannel())
                ) {
                    supportSummary.setSalesYtd(item.getTargetLiters());
                    supportSummary.setSalesSparkTarget(item.getTotalVolume());
                    supportSummary.setSalesAboveYtd(item.getHighTargetLiters());
                    supportSummary.setSalesAboveSparkTarget(StringUtils.isNotBlank(item.getTotalSnAboveVolume()) ? Double.parseDouble(item.getTotalSnAboveVolume()) : null);
                    if(supportSummary.getChannel().equals("Commercial")){
                        supportSummary.setSalesSparkTarget(item.getTotalPlanVolume());
                    }
                }
            }
            supportSummary.setSopAndOthers(NumberUtil.add(supportSummary.getSignage(), supportSummary.getSeminar(),
                    supportSummary.getEquipmentTools(), supportSummary.getOilChangeCar(),
                    supportSummary.getSignageApply()).doubleValue());
        }
        return sopPartnerSummary;
    }

    /**
     * 获取经销商销量等数据
     */
    private List<MktDealerQuarter> getMktDealerInfo(SopSupportParams sopSupportParams, List<SopSupportSummary> sopPartnerSummary, Long distributorId){
        MktDealerQuarter query = new MktDealerQuarter();
        query.setYear(Integer.parseInt(sopSupportParams.getYear()));
        query.setSalesChannel(sopSupportParams.getChannel());
        query.setQuater(0);
        query.setDistributorId(distributorId);
        //分渠道查询目标数据
        List<MktDealerQuarter> qbrMktDealerQuarters = new ArrayList<MktDealerQuarter>();
        HashSet<String> channels = new HashSet<String>();
        for (SopSupportSummary sopSupportSummary : sopPartnerSummary) {
            channels.add(sopSupportSummary.getChannel());
        }
        for (String channel : channels) {
            query.setSalesChannel(channel);
            qbrMktDealerQuarters.addAll(mktResourceCostBudgetService.queryQbrDealerQuarterList(query));
        }
        return qbrMktDealerQuarters;
    }

    /**
     * 如果没有传渠道那么需要补全经销商两个渠道
     */
    private void initData(SopSupportParams sopSupportParams, List<SopSupportSummary> sopPartnerSummary, Long distributorId) {
        if (StringUtils.isBlank(sopSupportParams.getChannel())) {
            List<String> indirectDealrChannelWeight = partnerCtrlService.getIndirectPartnerChannelWeight(null, sopSupportParams.getPartnerId());
            for (String channel : indirectDealrChannelWeight) {
                boolean flag = true;
                for (SopSupportSummary sopSupportSummary : sopPartnerSummary) {
                    if (sopSupportSummary.getChannel().equals(channel)) {
                        flag = false;
                    }
                }
                if (flag) {
                    SopSupportSummary item = new SopSupportSummary();
                    BeanUtil.copyProperties(sopSupportParams, item);
                    item.setChannel(channel);
                    item.setDistributorId(distributorId);
                    sopPartnerSummary.add(item);
                }
            }
        } else {
            if(CollectionUtil.isEmpty(sopPartnerSummary)){
                //传了渠道那么只补全查询的渠道
                SopSupportSummary item = new SopSupportSummary();
                BeanUtil.copyProperties(sopSupportParams, item);
                item.setDistributorId(distributorId);
                sopPartnerSummary.add(item);
            }
        }
        //获取其他调整的数据
        List<SopOtherSupportDetail> list = sopOtherSupportDetailMapper.otherSopSupportAmount(sopSupportParams.getYear(),sopSupportParams.getQuarter(),sopSupportParams.getPartnerId());
        for (SopOtherSupportDetail item : list) {
            for (SopSupportSummary sopSupportSummary : sopPartnerSummary) {
                if(sopSupportSummary.getChannel().equals(item.getChannel())){
                    sopSupportSummary.setOtherAmount(item.getAmount());
                }
            }
        }

    }

    /**
     * 判断经销商SOP是否可以填星火调整数据
     */
    private void checkSopAllot(SopSupportParams sopSupportParams,List<SopSupportSummary> list) {
        for (SopSupportSummary sopSupportSummary : list) {
            if(!sopSupportSummary.getChannel().equals("Commercial")){
                continue;
            }
            //判断经销商SOP是否可以填星火调整数据
            SopAmountAllotExample example = new SopAmountAllotExample();
            example.createCriteria().andYearEqualTo(sopSupportParams.getYear()).andPartnerIdEqualTo(sopSupportParams.getPartnerId());
            List<SopAmountAllot> sopAmountAllots = sopAmountAllotMapper.selectByExample(example);
            if(CollectionUtil.isNotEmpty(sopAmountAllots)){
                SopAmountAllot sopAmountAllot = sopAmountAllots.get(0);
                if(sopAmountAllot.getSopAmount() != null && sopAmountAllot.getSopAmount() > 0d){
                    sopSupportSummary.setSopAmountAllot(sopAmountAllot);
                }
            }
        }
    }

    @Override
    public List<SopSupportFieldHis> findSopSupportFieldHis(SopSupportFieldQuery query) {
        if(query.getSourceId() == null){
            return new ArrayList<SopSupportFieldHis>();
        }
        List<SopSupportFieldHis> result = sopSupportFieldHisMapper.findSopSupportFieldHis(query);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sopPartnerSummarySubmit(Long id) throws WxPltException {
        SopSupportSummary sopSupportSummary = sopSupportSummaryMapper.selectByPrimaryKey(id);
        if(sopSupportSummary==null){
            throw new WxPltException("非法输入");
        }
        if(sopSupportSummary.getStatus() >= SopPartnerSummaryStatus.SUBMITTED.getStatus()){
            throw new WxPltException("流程状态不对，无法提交。");
        }
        SopSupportSummary update = new SopSupportSummary();
        update.setId(id);
        update.setStatus(SopPartnerSummaryStatus.SUBMITTED.getStatus());
        //获取sop其他调整的汇总值
        SopSupportParams params = new SopSupportParams();
        params.setPartnerId(sopSupportSummary.getPartnerId());
        params.setQuarter(sopSupportSummary.getQuarter());
        params.setYear(sopSupportSummary.getYear());
        params.setChannel(sopSupportSummary.getChannel());
        params.setPaging(false);
        params.clearOrderField();
        Double otherAmount = sopOtherSupportDetailMapper.selectSummaryByParams(params);
        otherAmount= otherAmount ==null?0:otherAmount;
        update.setOtherAmount(otherAmount);
        sopSupportSummaryMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public SopSupportSummary calSopSupportInfo(String year,String channel,String region,String distributorId) throws WxPltException {
        SopSupportAdjustApplyExample example = new SopSupportAdjustApplyExample();
        example.createCriteria().andYearEqualTo(year).andFormStatusEqualTo(SopApplyStatus.Complete.getStatus())
        .andChannelEqualTo(channel);
        List<SopSupportAdjustApply> sopSupportAdjustApplies = sopSupportAdjustApplyMapper.selectByExample(example);
        List<String> quarters = new ArrayList<String>();
        for (SopSupportAdjustApply sopSupportAdjustApply : sopSupportAdjustApplies) {
            quarters.add(sopSupportAdjustApply.getQuarter());
        }
        WxTUser user = ContextUtil.getCurUser();
        //根据数据权限去查询经销商数据
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("quarters",quarters);
        map.put("year", year);
        map.put("channel", channel.toLowerCase());
        map.put("region", region);
        map.put("distributorId", distributorId);
        //获取当前用户的查看权限
        int permissionWeight = operationPermissionBizService.getPermissionWeight(user.getUserId(), "asm_flsr_abm");
        if((permissionWeight & 1) == 1){
        }else if((permissionWeight & 16) == 16){
            map.put("teamLeaderCai", user.getCai());
        }else if((permissionWeight & 2) == 2 || permissionWeight == 0){
            map.put("salesCai", user.getCai());
        }else if((permissionWeight & 4) == 4){
            map.put("suppervisorCai", user.getCai());
        }else if((permissionWeight & 8) == 8){
            map.put("channelManagerCai", user.getCai());
        }else{
            throw new WxPltException("权限不足");
        }
        map.put("status", SopPartnerSummaryStatus.COMPLETE.getStatus());
        SopSupportSummary result = null;
        try {
        result = sopSupportSummaryMapper.calSopSupportInfo(map);
        }catch (Exception e){
            e.printStackTrace();
        }
        if(result!=null){
            result.calSopSparkTotal();
        }
        return result;
    }

    @Override
    public SopSupportSummary sopPartnerSummaryAbort(Long id) throws WxPltException {
        SopSupportSummary sopSupportSummary = sopSupportSummaryMapper.selectByPrimaryKey(id);
        if(sopSupportSummary==null){
            throw new WxPltException("非法输入");
        }
        SopSupportSummary update = new SopSupportSummary();
        update.setStatus(SopPartnerSummaryStatus.ABORT.getStatus());
        sopSupportSummaryMapper.updateByPrimaryKeySelective(update);
        return sopSupportSummary;
    }

    @Override
    public SopSupportSummary sopPartnerSummaryPass(Long id) throws WxPltException {
        SopSupportSummary sopSupportSummary = sopSupportSummaryMapper.selectByPrimaryKey(id);
        if(sopSupportSummary==null){
            throw new WxPltException("非法输入");
        }
        SopSupportSummary update = new SopSupportSummary();
        update.setStatus(SopPartnerSummaryStatus.PASS.getStatus());
        sopSupportSummaryMapper.updateByPrimaryKeySelective(update);
        return sopSupportSummary;
    }

    @Override
    public List<SopSupportSummary> qbrSopActualSupport(String year, String channel,String partnerId) {
        SopSupportSummary query = new SopSupportSummary();
        query.setYear(year);
        query.setChannel(channel);
        query.setStatus(0);
        List<SopSupportSummary> list = sopSupportSummaryMapper.qbrSopActualSupport(query);
        //计算ytd数据
        SopSupportSummary ytd = new SopSupportSummary();
        for (SopSupportSummary item : list) {
            ytd.setSignage(NumberUtil.add(ytd.getSignage(),item.getSignage()));
            ytd.setSeminar(NumberUtil.add(ytd.getSeminar(),item.getSeminar()));
            ytd.setOilChangeCar(NumberUtil.add(ytd.getOilChangeCar(),item.getOilChangeCar()));
            ytd.setEquipmentTools(NumberUtil.add(ytd.getEquipmentTools(),item.getEquipmentTools()));
        }
        list.add(ytd);
        return list;
    }

    @Override
    public List<SopOtherSupportDetail> qbrOtherSopSupport(String year, String channel, String partnerId) {
        List<SopOtherSupportDetail> list = sopOtherSupportDetailMapper.qbrOtherSopSupport(year,channel,0);
        return list;
    }

    private List<SopSupportFieldHis> fieldHisList(SopSupportSummary update,SopSupportSummary old,Long userId) throws WxPltException {
        List<SopSupportFieldHis> sopSupportFieldHis = new ArrayList<SopSupportFieldHis>();
        old = old == null ? new SopSupportSummary() : old;
        //初始化数据
        if(update.getSignage()!=null && !ObjectUtil.equal(update.getSignage(),old.getSignage())){
            SopSupportFieldHis signageHis = new SopSupportFieldHis();
            signageHis.setAfterValue(update.getSignage().toString());
            signageHis.setBeforeValue(old.getSignage()==null?null:old.getSignage().toString());
            signageHis.setFieldName("signage");
            signageHis.setCreateTime(new Date());
            signageHis.setCreateUserId(userId);
            sopSupportFieldHis.add(signageHis);
        }
        if(update.getSeminar()!=null && !ObjectUtil.equal(update.getSeminar(),old.getSeminar())){
            SopSupportFieldHis signageHis = new SopSupportFieldHis();
            signageHis.setAfterValue(update.getSeminar().toString());
            signageHis.setBeforeValue(old.getSeminar()==null?null:old.getSeminar().toString());
            signageHis.setFieldName("seminar");
            signageHis.setCreateTime(new Date());
            signageHis.setCreateUserId(userId);
            sopSupportFieldHis.add(signageHis);
        }
        if(update.getOilChangeCar()!=null && !ObjectUtil.equal(update.getOilChangeCar(),old.getOilChangeCar())){
            SopSupportFieldHis signageHis = new SopSupportFieldHis();
            signageHis.setAfterValue(update.getOilChangeCar().toString());
            signageHis.setBeforeValue(old.getOilChangeCar()==null?null:old.getOilChangeCar().toString());
            signageHis.setFieldName("oilChangeCar");
            signageHis.setCreateTime(new Date());
            signageHis.setCreateUserId(userId);
            sopSupportFieldHis.add(signageHis);
        }
        if(update.getEquipmentTools() !=null && !ObjectUtil.equal(update.getEquipmentTools(),old.getEquipmentTools())){
            SopSupportFieldHis signageHis = new SopSupportFieldHis();
            signageHis.setAfterValue(update.getEquipmentTools().toString());
            signageHis.setBeforeValue(old.getEquipmentTools()==null?null:old.getEquipmentTools().toString());
            signageHis.setFieldName("equipmentTools");
            signageHis.setCreateTime(new Date());
            signageHis.setCreateUserId(userId);
            sopSupportFieldHis.add(signageHis);
        }
        return sopSupportFieldHis;
    }
}
