package com.chevron.training.model;

import com.wordnik.swagger.annotations.ApiModel;
import com.wordnik.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 培训安排-wx_t_plan
 *
 * <AUTHOR>
 * @version 1.0 2020-10-26 11:03
 */
@ApiModel(value = "培训安排", description = "培训安排")
public class Plan {

    @ApiModelProperty(value = "ID", name = "id")
    private Long id;

    @ApiModelProperty(value = "课程ID", name = "curriculumId")
    private Long curriculumId;

    @ApiModelProperty(value = "课程名称", name = "curriculumName")
    private String curriculumName;

    @ApiModelProperty(value = "开始时间", name = "startTime")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", name = "endTime")
    private Date endTime;

    @ApiModelProperty(value = "培训天数", name = "traningDays")
    private Integer traningDays;

    @ApiModelProperty(value = "经销商ID", name = "distributorId")
    private Long distributorId;

    @ApiModelProperty(value = "经销商名称", name = "distributorName")
    private String distributorName;

    @ApiModelProperty(value = "经销商联络人", name = "distributorConctor")
    private String distributorConctor;

    @ApiModelProperty(value = "经销商地址", name = "distributorAddress")
    private String distributorAddress;

    @ApiModelProperty(value = "经销商手机号码", name = "distributorPhone")
    private String distributorPhone;

    @ApiModelProperty(value = "大区经理CAI", name = "asmCai")
    private String asmCai;

    @ApiModelProperty(value = "大区经理", name = "asmName")
    private String asmName;

    @ApiModelProperty(value = "销售", name = "salesCai")
    private String salesCai;

    @ApiModelProperty(value = "销售姓名", name = "salesName")
    private String salesName;
    @ApiModelProperty(value = "销售电话", name = "salesPhone")
    private String salesPhone;

    @ApiModelProperty(value = "培训老师用户ID", name = "userId")
    private Long userId;

    @ApiModelProperty(value = "培训老师", name = "traningTeacher")
    private String traningTeacher;

    @ApiModelProperty(value = "培训老师手机号码", name = "teacherPhone")
    private String teacherPhone;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "删除标记(0-未删除。1-删除)", name = "deleteFlag")
    private Integer deleteFlag;

    @ApiModelProperty(value = "创建用户ID", name = "createUserId")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间", name = "createTime")
    private Date createTime;

    @ApiModelProperty(value = "最后修改用户ID", name = "updateUserId")
    private Long updateUserId;

    @ApiModelProperty(value = "最后修改时间", name = "updateTime")
    private Date updateTime;
    @ApiModelProperty(value = "月份", name = "month")
    private Integer month;

    @ApiModelProperty(value = "反馈表是否已提交",name = "surveyCommit")
    private Integer surveyCommit;

    @ApiModelProperty(value = "业务员评估表是否已提交",name = "assessCommit")
    private Integer assessCommit;

    @ApiModelProperty(value = "驻地辅导跟踪报告是否已提交",name = "reportCommit")
    private Integer reportCommit;


    public Integer getSurveyCommit() {
        return surveyCommit;
    }

    public void setSurveyCommit(Integer surveyCommit) {
        this.surveyCommit = surveyCommit;
    }

    public Integer getAssessCommit() {
        return assessCommit;
    }

    public void setAssessCommit(Integer assessCommit) {
        this.assessCommit = assessCommit;
    }

    public Integer getReportCommit() {
        return reportCommit;
    }

    public void setReportCommit(Integer reportCommit) {
        this.reportCommit = reportCommit;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getSalesPhone() {
        return salesPhone;
    }

    public void setSalesPhone(String salesPhone) {
        this.salesPhone = salesPhone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCurriculumId() {
        return curriculumId;
    }

    public void setCurriculumId(Long curriculumId) {
        this.curriculumId = curriculumId;
    }

    public Date getStartTime() {
        return startTime;
    }


    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }


    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getTraningDays() {
        return traningDays;
    }

    public void setTraningDays(Integer traningDays) {
        this.traningDays = traningDays;
    }

    public Long getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(Long distributorId) {
        this.distributorId = distributorId;
    }

    public String getDistributorConctor() {
        return distributorConctor;
    }

    public void setDistributorConctor(String distributorConctor) {
        this.distributorConctor = distributorConctor;
    }

    public String getDistributorAddress() {
        return distributorAddress;
    }

    public void setDistributorAddress(String distributorAddress) {
        this.distributorAddress = distributorAddress;
    }

    public String getDistributorPhone() {
        return distributorPhone;
    }

    public void setDistributorPhone(String distributorPhone) {
        this.distributorPhone = distributorPhone;
    }

    public String getAsmCai() {
        return asmCai;
    }

    public void setAsmCai(String asmCai) {
        this.asmCai = asmCai;
    }

    public String getAsmName() {
        return asmName;
    }

    public void setAsmName(String asmName) {
        this.asmName = asmName;
    }

    public String getSalesCai() {
        return salesCai;
    }

    public void setSalesCai(String salesCai) {
        this.salesCai = salesCai;
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName;
    }

    public String getTraningTeacher() {
        return traningTeacher;
    }

    public void setTraningTeacher(String traningTeacher) {
        this.traningTeacher = traningTeacher;
    }

    public String getTeacherPhone() {
        return teacherPhone;
    }

    public void setTeacherPhone(String teacherPhone) {
        this.teacherPhone = teacherPhone;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    public String getCurriculumName() {
        return curriculumName;
    }

    public void setCurriculumName(String curriculumName) {
        this.curriculumName = curriculumName;
    }
}
