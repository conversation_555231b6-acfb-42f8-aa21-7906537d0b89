insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'EliteFundV2.apply', 32, 1, '2020精英计划申请权限。32-下载《精英计划报销操作手册(经销商)》', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'EliteFundV2.apply', 32, 1, '2020精英计划申请权限。32-下载《精英计划报销操作手册(经销商)》', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'EliteFundV2.apply', 32, 1, '2020精英计划申请权限。32-下载《精英计划报销操作手册(经销商)》', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'EliteFundV2.apply', 64, 1, '2020精英计划申请权限。64-下载《精英计划报销操作手册(销售)》', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'EliteFundV2.apply', 64, 1, '2020精英计划申请权限。64-下载《精英计划报销操作手册(销售)》', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'EliteFundV2.apply', 64, 1, '2020精英计划申请权限。64-下载《精英计划报销操作手册(销售)》', 1, getdate());

update rm set rm.user_id=(select user_id from wx_t_user where login_name='Vicky' and status=1)
from wx_t_partner_responsible_main rm where user_id=130971

update el set el.executor=(select user_id from wx_t_user where login_name='Vicky' and status=1)
from wx_t_workflow_instance wi 
left join wx_t_workflow_step_instance si on wi.flow_instance_id=si.workflow_instance_id
left join wx_t_wor_ins_exe_list el on el.step_instance_id=si.step_instance_id
left join wx_t_workflow_step_history wsh on wsh.step_instance_id=si.step_instance_id
where el.executor=(select user_id from wx_t_user u where u.login_name='LIFJ' and u.status=1) 
and wi.workflow_code='ELITE_FUND_V2020'

update wsh set wsh.executor=(select user_id from wx_t_user where login_name='Vicky' and status=1),wsh.actual_executor=(select user_id from wx_t_user u where u.login_name='LIFJ' and u.status=1) 
from wx_t_workflow_instance wi 
left join wx_t_workflow_step_instance si on wi.flow_instance_id=si.workflow_instance_id
left join wx_t_wor_ins_exe_list el on el.step_instance_id=si.step_instance_id
left join wx_t_workflow_step_history wsh on wsh.step_instance_id=si.step_instance_id
where wsh.executor=(select user_id from wx_t_user u where u.login_name='LIFJ' and u.status=1) 
and wi.workflow_code='ELITE_FUND_V2020'

update el set el.source_key=(select u1.user_id from wx_t_user u1 where u1.login_name='QIOY' and u1.status=1)
from wx_t_workflow_exe_list el
left join wx_t_workflow_step ws on el.step_id=ws.step_id
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP'
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER'
where ws.workflow_code='ELITE_COLLECT_V2020' --and eg.group_name='申请FLSR'
and ws.step_code='SOP_Manager_APPROVE'

update el set el.executor=(select user_id from wx_t_user where login_name='QIOY' and status=1)
from wx_t_workflow_instance wi 
left join wx_t_workflow_step_instance si on wi.flow_instance_id=si.workflow_instance_id
left join wx_t_wor_ins_exe_list el on el.step_instance_id=si.step_instance_id
left join wx_t_workflow_step_history wsh on wsh.step_instance_id=si.step_instance_id
where el.executor=131747
and wi.workflow_code='ELITE_COLLECT_V2020'

update wsh set wsh.executor=(select user_id from wx_t_user where login_name='QIOY' and status=1) 
from wx_t_workflow_instance wi 
left join wx_t_workflow_step_instance si on wi.flow_instance_id=si.workflow_instance_id
left join wx_t_wor_ins_exe_list el on el.step_instance_id=si.step_instance_id
left join wx_t_workflow_step_history wsh on wsh.step_instance_id=si.step_instance_id
where wsh.executor=131747
and wi.workflow_code='ELITE_COLLECT_V2020'


update p set p.code='q3q4'
from wx_t_properties p where codetype='EliteFund.applyBatch'

update p set p.permission_weight=1
from wx_t_operation_permission p where module_code='EliteFundV2.apply' and p.permission_weight=0

update f set f.apply_phase='q3q4'
from wx_t_v2_elite_fund_form f 
where delete_flag=0 and f.form_status<50

--西安骏峰优品供应链有限公司 调整到第二阶段报销
update f set f.apply_phase='q3q4', f.form_status=30
from wx_t_v2_elite_fund_form f 
where f.id=129
delete from wx_t_v2_elite_collect_form_det where detail_id=129

insert into wx_t_elite_fund2020_config (distributor_id, ext_flag, sop_spark_amount)
select distinct distributor_id, 0, 0 from dw_mid_cal_elite_fund

/*
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=53
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=56
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=63
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=123
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=127
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=146
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=151
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=157
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=182
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=188
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=198
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=208
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=233
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=251
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=275
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=326
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=333
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=337
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=384
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=387
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=388
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=395
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=399
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=403
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=417
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=422
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=430
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=434
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=444
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=503
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=565
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=582
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=607
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=619
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=673
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=686
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=688
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=724
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1063
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1069
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1090
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1097
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1123
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1156
update wx_t_elite_fund2020_config set ext_flag=ext_flag|1 where distributor_id=1159

update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=57
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=157
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=159
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=182
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=188
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=193
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=198
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=208
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=222
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=227
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=233
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=251
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=268
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=273
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=275
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=300
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=306
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=328
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=333
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=337
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=345
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=374
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=384
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=387
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=388
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=396
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=403
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=417
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=422
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=434
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=440
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=492
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=494
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=496
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=501
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=565
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=607
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=619
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=638
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=641
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=663
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=670
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=673
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=686
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=687
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=688
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=689
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=719
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=741
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=742
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=749
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=750
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1063
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1081
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1099
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1127
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1128
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1137
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1143
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1154
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1157
update wx_t_elite_fund2020_config set ext_flag=ext_flag|2 where distributor_id=1183*/
