package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleBatteryDetail {
    private Long id;

    private String tModelId;

    private String batteryName;

    private String batteryPartsNo;

    private String batteryType;

    private String batteryVoltage;

    private String batteryCurrent;

    private String batteryCapacity;

    private String batteryPurchasePrice;

    private String batterySalePrice;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getBatteryName() {
        return batteryName;
    }

    public void setBatteryName(String batteryName) {
        this.batteryName = batteryName == null ? null : batteryName.trim();
    }

    public String getBatteryPartsNo() {
        return batteryPartsNo;
    }

    public void setBatteryPartsNo(String batteryPartsNo) {
        this.batteryPartsNo = batteryPartsNo == null ? null : batteryPartsNo.trim();
    }

    public String getBatteryType() {
        return batteryType;
    }

    public void setBatteryType(String batteryType) {
        this.batteryType = batteryType == null ? null : batteryType.trim();
    }

    public String getBatteryVoltage() {
        return batteryVoltage;
    }

    public void setBatteryVoltage(String batteryVoltage) {
        this.batteryVoltage = batteryVoltage == null ? null : batteryVoltage.trim();
    }

    public String getBatteryCurrent() {
        return batteryCurrent;
    }

    public void setBatteryCurrent(String batteryCurrent) {
        this.batteryCurrent = batteryCurrent == null ? null : batteryCurrent.trim();
    }

    public String getBatteryCapacity() {
        return batteryCapacity;
    }

    public void setBatteryCapacity(String batteryCapacity) {
        this.batteryCapacity = batteryCapacity == null ? null : batteryCapacity.trim();
    }

    public String getBatteryPurchasePrice() {
        return batteryPurchasePrice;
    }

    public void setBatteryPurchasePrice(String batteryPurchasePrice) {
        this.batteryPurchasePrice = batteryPurchasePrice == null ? null : batteryPurchasePrice.trim();
    }

    public String getBatterySalePrice() {
        return batterySalePrice;
    }

    public void setBatterySalePrice(String batterySalePrice) {
        this.batterySalePrice = batterySalePrice == null ? null : batterySalePrice.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}