{"version": 3, "sources": ["webpack:///./src/views/order/ReturnList.vue?51aa", "webpack:///./src/views/order/ReturnList.vue?0776", "webpack:///src/views/order/ReturnList.vue", "webpack:///./src/views/order/ReturnList.vue?a140", "webpack:///./src/views/order/ReturnList.vue", "webpack:///./src/assets/images/noOrder.png"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "ref", "staticClass", "_l", "order", "key", "order_id", "_status", "_type", "_e", "_v", "_s", "cart", "id", "on", "$event", "$router", "push", "path", "attrs", "productInfo", "image", "stopPropagation", "store_name", "cart_num", "attrInfo", "suk", "price", "cartInfo", "length", "pay_price", "orderList", "page", "_m", "loaded", "loading", "staticRenderFns", "name", "components", "Loading", "data", "limit", "methods", "getOrderList", "type", "mounted", "$scroll", "$refs", "container", "component", "module", "exports"], "mappings": "kHAAA,yBAA2f,EAAG,G,2CCA9f,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,IAAI,YAAYC,YAAY,eAAe,CAACP,EAAIQ,GAAIR,EAAa,WAAE,SAASS,GAAO,OAAOL,EAAG,MAAM,CAACM,IAAID,EAAME,SAASJ,YAAY,eAAe,EAA2B,IAAzBE,EAAMG,QAAQC,MAAcT,EAAG,MAAM,CAACG,YAAY,sCAAsCP,EAAIc,MAA+B,IAAzBL,EAAMG,QAAQC,MAAcT,EAAG,MAAM,CAACG,YAAY,4BAA4BP,EAAIc,KAAKV,EAAG,MAAM,CAACG,YAAY,YAAY,CAACP,EAAIe,GAAG,OAAOf,EAAIgB,GAAGP,EAAME,aAAaX,EAAIQ,GAAIC,EAAc,UAAE,SAASQ,GAAM,OAAOb,EAAG,MAAM,CAACM,IAAIO,EAAKC,GAAGX,YAAY,oCAAoCY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOpB,EAAIqB,QAAQC,KAAK,CAAEC,KAAM,iBAAmBd,EAAME,cAAe,CAACP,EAAG,MAAM,CAACG,YAAY,WAAW,CAACH,EAAG,MAAM,CAACG,YAAY,QAAQiB,MAAM,CAAC,IAAMP,EAAKQ,YAAYC,OAAOP,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOO,kBAAyB3B,EAAIqB,QAAQC,KAAK,CAAEC,KAAM,WAAaN,EAAKQ,YAAYP,WAAYd,EAAG,MAAM,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,gCAAgC,CAACH,EAAG,MAAM,CAACG,YAAY,cAAc,CAACP,EAAIe,GAAGf,EAAIgB,GAAGC,EAAKQ,YAAYG,eAAexB,EAAG,MAAM,CAACG,YAAY,OAAO,CAACP,EAAIe,GAAG,KAAKf,EAAIgB,GAAGC,EAAKY,eAAgBZ,EAAKQ,YAAoB,SAAErB,EAAG,MAAM,CAACG,YAAY,cAAc,CAACP,EAAIe,GAAG,IAAIf,EAAIgB,GAAGC,EAAKQ,YAAYK,SAASC,KAAK,OAAO3B,EAAG,MAAM,CAACG,YAAY,cAAc,CAACP,EAAIe,GAAGf,EAAIgB,GAAGC,EAAKQ,YAAYG,eAAexB,EAAG,MAAM,CAACG,YAAY,SAAS,CAACP,EAAIe,GAAG,IAAIf,EAAIgB,GAAGC,EAAKQ,YAAYO,iBAAgB5B,EAAG,MAAM,CAACG,YAAY,YAAY,CAACP,EAAIe,GAAG,KAAKf,EAAIgB,GAAGP,EAAMwB,SAASC,QAAU,GAAG,YAAY9B,EAAG,OAAO,CAACG,YAAY,wBAAwB,CAACP,EAAIe,GAAG,IAAIf,EAAIgB,GAAGP,EAAM0B,iBAAiB,MAA+B,IAAzBnC,EAAIoC,UAAUF,QAAgBlC,EAAIqC,KAAO,EAAGjC,EAAG,MAAM,CAACG,YAAY,UAAU,CAACP,EAAIsC,GAAG,KAAKtC,EAAIc,KAAKV,EAAG,UAAU,CAACoB,MAAM,CAAC,OAASxB,EAAIuC,OAAO,QAAUvC,EAAIwC,YAAY,IAC5yDC,EAAkB,CAAC,WAAa,IAAIzC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACG,YAAY,WAAW,CAACH,EAAG,MAAM,CAACoB,MAAM,CAAC,IAAM,EAAQ,e,oCCsD3K,GACEkB,KAAM,aACNC,WAAY,CACVC,QAAJ,QAEEC,KALF,WAMI,MAAO,CACLT,UAAW,GACXC,KAAM,EACNS,MAAO,GACPN,SAAS,EACTD,QAAQ,IAGZQ,QAAS,CACPC,aADJ,WACA,WACA,yBACU/C,KAAKuC,SAAWvC,KAAKsC,SACzBtC,KAAKuC,SAAU,EACf,OAAN,OAAM,CAAN,CACQH,KAAR,EACQS,MAAR,EACQG,MAAO,IACf,kBACQ,EAAR,qCACQ,EAAR,WACQ,EAAR,uBACQ,EAAR,aAIEC,QA/BF,WA+BA,WACIjD,KAAK+C,eACL/C,KAAKkD,QAAQlD,KAAKmD,MAAMC,WAAW,YAChC,EAAP,+BCzFmW,I,wBCQ/VC,EAAY,eACd,EACAvD,EACA0C,GACA,EACA,KACA,WACA,MAIa,aAAAa,E,gCCnBfC,EAAOC,QAAU,IAA0B,+B", "file": "h5/js/chunk-a1adafea.0510c299.js", "sourcesContent": ["import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReturnList.vue?vue&type=style&index=0&id=3ea61d14&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReturnList.vue?vue&type=style&index=0&id=3ea61d14&scoped=true&lang=css&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{ref:\"container\",staticClass:\"return-list\"},[_vm._l((_vm.orderList),function(order){return _c('div',{key:order.order_id,staticClass:\"goodWrapper\"},[(order._status._type === -1)?_c('div',{staticClass:\"iconfont icon-tuikuanzhong powder\"}):_vm._e(),(order._status._type === -2)?_c('div',{staticClass:\"iconfont icon-yituikuan\"}):_vm._e(),_c('div',{staticClass:\"orderNum\"},[_vm._v(\"订单号：\"+_vm._s(order.order_id))]),_vm._l((order.cartInfo),function(cart){return _c('div',{key:cart.id,staticClass:\"item acea-row row-between-wrapper\",on:{\"click\":function($event){return _vm.$router.push({ path: '/order/detail/' + order.order_id })}}},[_c('div',{staticClass:\"pictrue\"},[_c('img',{staticClass:\"image\",attrs:{\"src\":cart.productInfo.image},on:{\"click\":function($event){$event.stopPropagation();return _vm.$router.push({ path: '/detail/' + cart.productInfo.id })}}})]),_c('div',{staticClass:\"text\"},[_c('div',{staticClass:\"acea-row row-between-wrapper\"},[_c('div',{staticClass:\"name line1\"},[_vm._v(_vm._s(cart.productInfo.store_name))]),_c('div',{staticClass:\"num\"},[_vm._v(\"x \"+_vm._s(cart.cart_num))])]),(cart.productInfo.attrInfo)?_c('div',{staticClass:\"attr line1\"},[_vm._v(\" \"+_vm._s(cart.productInfo.attrInfo.suk)+\" \")]):_c('div',{staticClass:\"attr line1\"},[_vm._v(_vm._s(cart.productInfo.store_name))]),_c('div',{staticClass:\"money\"},[_vm._v(\"￥\"+_vm._s(cart.productInfo.price))])])])}),_c('div',{staticClass:\"totalSum\"},[_vm._v(\" 共\"+_vm._s(order.cartInfo.length || 0)+\"件商品，总金额 \"),_c('span',{staticClass:\"font-color-red price\"},[_vm._v(\"￥\"+_vm._s(order.pay_price))])])],2)}),(_vm.orderList.length === 0 && _vm.page > 1)?_c('div',{staticClass:\"noCart\"},[_vm._m(0)]):_vm._e(),_c('Loading',{attrs:{\"loaded\":_vm.loaded,\"loading\":_vm.loading}})],2)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":require(\"@assets/images/noOrder.png\")}})])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"return-list\" ref=\"container\">\n    <div class=\"goodWrapper\" v-for=\"order in orderList\" :key=\"order.order_id\">\n      <div\n        class=\"iconfont icon-tuikuanzhong powder\"\n        v-if=\"order._status._type === -1\"\n      ></div>\n      <div\n        class=\"iconfont icon-yituikuan\"\n        v-if=\"order._status._type === -2\"\n      ></div>\n      <div class=\"orderNum\">订单号：{{ order.order_id }}</div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-for=\"cart in order.cartInfo\"\n        :key=\"cart.id\"\n        @click=\"$router.push({ path: '/order/detail/' + order.order_id })\"\n      >\n        <div class=\"pictrue\">\n          <img\n            :src=\"cart.productInfo.image\"\n            class=\"image\"\n            @click.stop=\"\n              $router.push({ path: '/detail/' + cart.productInfo.id })\n            \"\n          />\n        </div>\n        <div class=\"text\">\n          <div class=\"acea-row row-between-wrapper\">\n            <div class=\"name line1\">{{ cart.productInfo.store_name }}</div>\n            <div class=\"num\">x {{ cart.cart_num }}</div>\n          </div>\n          <div class=\"attr line1\" v-if=\"cart.productInfo.attrInfo\">\n            {{ cart.productInfo.attrInfo.suk }}\n          </div>\n          <div class=\"attr line1\" v-else>{{ cart.productInfo.store_name }}</div>\n          <div class=\"money\">￥{{ cart.productInfo.price }}</div>\n        </div>\n      </div>\n      <div class=\"totalSum\">\n        共{{ order.cartInfo.length || 0 }}件商品，总金额\n        <span class=\"font-color-red price\">￥{{ order.pay_price }}</span>\n      </div>\n    </div>\n    <div class=\"noCart\" v-if=\"orderList.length === 0 && page > 1\">\n      <div class=\"pictrue\"><img src=\"@assets/images/noOrder.png\" /></div>\n    </div>\n    <Loading :loaded=\"loaded\" :loading=\"loading\"></Loading>\n  </div>\n</template>\n\n<script>\nimport { getOrderList } from \"@api/order\";\nimport Loading from \"@components/Loading\";\n\nexport default {\n  name: \"ReturnList\",\n  components: {\n    Loading\n  },\n  data() {\n    return {\n      orderList: [],\n      page: 1,\n      limit: 20,\n      loading: false,\n      loaded: false\n    };\n  },\n  methods: {\n    getOrderList() {\n      const { page, limit } = this;\n      if (this.loading || this.loaded) return;\n      this.loading = true;\n      getOrderList({\n        page,\n        limit,\n        type: -3\n      }).then(res => {\n        this.orderList = this.orderList.concat(res.data);\n        this.loading = false;\n        this.loaded = res.data.length < limit;\n        this.page++;\n      });\n    }\n  },\n  mounted() {\n    this.getOrderList();\n    this.$scroll(this.$refs.container, () => {\n      !this.loading && this.getOrderList();\n    });\n  }\n};\n</script>\n\n<style scoped>\n.noCart {\n  margin-top: 0.17rem;\n  padding-top: 0.1rem;\n}\n\n.noCart .pictrue {\n  width: 4rem;\n  height: 3rem;\n  margin: 0.7rem auto 0.5rem auto;\n}\n\n.noCart .pictrue img {\n  width: 100%;\n  height: 100%;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReturnList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReturnList.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ReturnList.vue?vue&type=template&id=3ea61d14&scoped=true&\"\nimport script from \"./ReturnList.vue?vue&type=script&lang=js&\"\nexport * from \"./ReturnList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ReturnList.vue?vue&type=style&index=0&id=3ea61d14&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3ea61d14\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"h5/img/noOrder.90017ce2.png\";"], "sourceRoot": ""}