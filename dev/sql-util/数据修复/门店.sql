--门店激活
select * 
--update w set w.status=3
--update w set w.activation_time=ws.create_time
from wx_t_work_shop w
left join wx_t_workshop_status ws on ws.workshop_id=w.id and ws.workshop_with_status=3
 where w.id=169374
 
insert into wx_t_workshop_status (workshop_id, workshop_with_status,create_time)
select workshop_id,3,getdate() from wx_t_workshop_status where workshop_id=169374

select * 
--update w set w.status=3
--update w set w.activation_time=ws.create_time
from wx_t_work_shop w
join wx_t_workshop_status ws on ws.workshop_id=w.id and ws.workshop_with_status=3
 where w.status<>3 and w.delete_flag=0
 
 insert into wx_t_workshop_status (workshop_id, workshop_with_status,create_time)
select id,3,getdate() from wx_t_work_shop
 where id in (182207,181654,181745)
 
 select ext_property22,* 
--update w set w.status=3
--update w set w.activation_time=ws.create_time
--update w set w.ext_flag=ext_flag|256,ext_property22=ws.create_time
from wx_t_work_shop w
join wx_t_workshop_status ws on ws.workshop_id=w.id and ws.workshop_with_status=3
 where w.id in (182207,181654,181745)