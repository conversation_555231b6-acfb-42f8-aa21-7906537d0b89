package com.zs.pms.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxtZSVehicleTypeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WxtZSVehicleTypeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNull() {
            addCriterion("t_model_id is null");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNotNull() {
            addCriterion("t_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andTModelIdEqualTo(String value) {
            addCriterion("t_model_id =", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotEqualTo(String value) {
            addCriterion("t_model_id <>", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThan(String value) {
            addCriterion("t_model_id >", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_id >=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThan(String value) {
            addCriterion("t_model_id <", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThanOrEqualTo(String value) {
            addCriterion("t_model_id <=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLike(String value) {
            addCriterion("t_model_id like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotLike(String value) {
            addCriterion("t_model_id not like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdIn(List<String> values) {
            addCriterion("t_model_id in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotIn(List<String> values) {
            addCriterion("t_model_id not in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdBetween(String value1, String value2) {
            addCriterion("t_model_id between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotBetween(String value1, String value2) {
            addCriterion("t_model_id not between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTIdIsNull() {
            addCriterion("t_id is null");
            return (Criteria) this;
        }

        public Criteria andTIdIsNotNull() {
            addCriterion("t_id is not null");
            return (Criteria) this;
        }

        public Criteria andTIdEqualTo(String value) {
            addCriterion("t_id =", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdNotEqualTo(String value) {
            addCriterion("t_id <>", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdGreaterThan(String value) {
            addCriterion("t_id >", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdGreaterThanOrEqualTo(String value) {
            addCriterion("t_id >=", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdLessThan(String value) {
            addCriterion("t_id <", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdLessThanOrEqualTo(String value) {
            addCriterion("t_id <=", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdLike(String value) {
            addCriterion("t_id like", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdNotLike(String value) {
            addCriterion("t_id not like", value, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdIn(List<String> values) {
            addCriterion("t_id in", values, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdNotIn(List<String> values) {
            addCriterion("t_id not in", values, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdBetween(String value1, String value2) {
            addCriterion("t_id between", value1, value2, "tId");
            return (Criteria) this;
        }

        public Criteria andTIdNotBetween(String value1, String value2) {
            addCriterion("t_id not between", value1, value2, "tId");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNull() {
            addCriterion("factory_name is null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNotNull() {
            addCriterion("factory_name is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameEqualTo(String value) {
            addCriterion("factory_name =", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotEqualTo(String value) {
            addCriterion("factory_name <>", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThan(String value) {
            addCriterion("factory_name >", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("factory_name >=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThan(String value) {
            addCriterion("factory_name <", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThanOrEqualTo(String value) {
            addCriterion("factory_name <=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLike(String value) {
            addCriterion("factory_name like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotLike(String value) {
            addCriterion("factory_name not like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIn(List<String> values) {
            addCriterion("factory_name in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotIn(List<String> values) {
            addCriterion("factory_name not in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameBetween(String value1, String value2) {
            addCriterion("factory_name between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotBetween(String value1, String value2) {
            addCriterion("factory_name not between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("brand_name is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("brand_name is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("brand_name =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("brand_name <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("brand_name >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("brand_name >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("brand_name <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("brand_name <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("brand_name like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("brand_name not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("brand_name in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("brand_name not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("brand_name between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("brand_name not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andDmpimpIsNull() {
            addCriterion("dmpimp is null");
            return (Criteria) this;
        }

        public Criteria andDmpimpIsNotNull() {
            addCriterion("dmpimp is not null");
            return (Criteria) this;
        }

        public Criteria andDmpimpEqualTo(String value) {
            addCriterion("dmpimp =", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpNotEqualTo(String value) {
            addCriterion("dmpimp <>", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpGreaterThan(String value) {
            addCriterion("dmpimp >", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpGreaterThanOrEqualTo(String value) {
            addCriterion("dmpimp >=", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpLessThan(String value) {
            addCriterion("dmpimp <", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpLessThanOrEqualTo(String value) {
            addCriterion("dmpimp <=", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpLike(String value) {
            addCriterion("dmpimp like", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpNotLike(String value) {
            addCriterion("dmpimp not like", value, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpIn(List<String> values) {
            addCriterion("dmpimp in", values, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpNotIn(List<String> values) {
            addCriterion("dmpimp not in", values, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpBetween(String value1, String value2) {
            addCriterion("dmpimp between", value1, value2, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andDmpimpNotBetween(String value1, String value2) {
            addCriterion("dmpimp not between", value1, value2, "dmpimp");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(String value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(String value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(String value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(String value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLike(String value) {
            addCriterion("vehicle_type like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotLike(String value) {
            addCriterion("vehicle_type not like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<String> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<String> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(String value1, String value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(String value1, String value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIsNull() {
            addCriterion("vehicle_series is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIsNotNull() {
            addCriterion("vehicle_series is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEqualTo(String value) {
            addCriterion("vehicle_series =", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotEqualTo(String value) {
            addCriterion("vehicle_series <>", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesGreaterThan(String value) {
            addCriterion("vehicle_series >", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_series >=", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLessThan(String value) {
            addCriterion("vehicle_series <", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLessThanOrEqualTo(String value) {
            addCriterion("vehicle_series <=", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLike(String value) {
            addCriterion("vehicle_series like", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotLike(String value) {
            addCriterion("vehicle_series not like", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIn(List<String> values) {
            addCriterion("vehicle_series in", values, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotIn(List<String> values) {
            addCriterion("vehicle_series not in", values, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesBetween(String value1, String value2) {
            addCriterion("vehicle_series between", value1, value2, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotBetween(String value1, String value2) {
            addCriterion("vehicle_series not between", value1, value2, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopIsNull() {
            addCriterion("vehicle_series_sop is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopIsNotNull() {
            addCriterion("vehicle_series_sop is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopEqualTo(String value) {
            addCriterion("vehicle_series_sop =", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopNotEqualTo(String value) {
            addCriterion("vehicle_series_sop <>", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopGreaterThan(String value) {
            addCriterion("vehicle_series_sop >", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_series_sop >=", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopLessThan(String value) {
            addCriterion("vehicle_series_sop <", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopLessThanOrEqualTo(String value) {
            addCriterion("vehicle_series_sop <=", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopLike(String value) {
            addCriterion("vehicle_series_sop like", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopNotLike(String value) {
            addCriterion("vehicle_series_sop not like", value, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopIn(List<String> values) {
            addCriterion("vehicle_series_sop in", values, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopNotIn(List<String> values) {
            addCriterion("vehicle_series_sop not in", values, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopBetween(String value1, String value2) {
            addCriterion("vehicle_series_sop between", value1, value2, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesSopNotBetween(String value1, String value2) {
            addCriterion("vehicle_series_sop not between", value1, value2, "vehicleSeriesSop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopIsNull() {
            addCriterion("vehicle_series_eop is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopIsNotNull() {
            addCriterion("vehicle_series_eop is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopEqualTo(String value) {
            addCriterion("vehicle_series_eop =", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopNotEqualTo(String value) {
            addCriterion("vehicle_series_eop <>", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopGreaterThan(String value) {
            addCriterion("vehicle_series_eop >", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_series_eop >=", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopLessThan(String value) {
            addCriterion("vehicle_series_eop <", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopLessThanOrEqualTo(String value) {
            addCriterion("vehicle_series_eop <=", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopLike(String value) {
            addCriterion("vehicle_series_eop like", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopNotLike(String value) {
            addCriterion("vehicle_series_eop not like", value, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopIn(List<String> values) {
            addCriterion("vehicle_series_eop in", values, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopNotIn(List<String> values) {
            addCriterion("vehicle_series_eop not in", values, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopBetween(String value1, String value2) {
            addCriterion("vehicle_series_eop between", value1, value2, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEopNotBetween(String value1, String value2) {
            addCriterion("vehicle_series_eop not between", value1, value2, "vehicleSeriesEop");
            return (Criteria) this;
        }

        public Criteria andTTypeIdIsNull() {
            addCriterion("t_type_id is null");
            return (Criteria) this;
        }

        public Criteria andTTypeIdIsNotNull() {
            addCriterion("t_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andTTypeIdEqualTo(String value) {
            addCriterion("t_type_id =", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdNotEqualTo(String value) {
            addCriterion("t_type_id <>", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdGreaterThan(String value) {
            addCriterion("t_type_id >", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdGreaterThanOrEqualTo(String value) {
            addCriterion("t_type_id >=", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdLessThan(String value) {
            addCriterion("t_type_id <", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdLessThanOrEqualTo(String value) {
            addCriterion("t_type_id <=", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdLike(String value) {
            addCriterion("t_type_id like", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdNotLike(String value) {
            addCriterion("t_type_id not like", value, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdIn(List<String> values) {
            addCriterion("t_type_id in", values, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdNotIn(List<String> values) {
            addCriterion("t_type_id not in", values, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdBetween(String value1, String value2) {
            addCriterion("t_type_id between", value1, value2, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIdNotBetween(String value1, String value2) {
            addCriterion("t_type_id not between", value1, value2, "tTypeId");
            return (Criteria) this;
        }

        public Criteria andTTypeIsNull() {
            addCriterion("t_type is null");
            return (Criteria) this;
        }

        public Criteria andTTypeIsNotNull() {
            addCriterion("t_type is not null");
            return (Criteria) this;
        }

        public Criteria andTTypeEqualTo(String value) {
            addCriterion("t_type =", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeNotEqualTo(String value) {
            addCriterion("t_type <>", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeGreaterThan(String value) {
            addCriterion("t_type >", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeGreaterThanOrEqualTo(String value) {
            addCriterion("t_type >=", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeLessThan(String value) {
            addCriterion("t_type <", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeLessThanOrEqualTo(String value) {
            addCriterion("t_type <=", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeLike(String value) {
            addCriterion("t_type like", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeNotLike(String value) {
            addCriterion("t_type not like", value, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeIn(List<String> values) {
            addCriterion("t_type in", values, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeNotIn(List<String> values) {
            addCriterion("t_type not in", values, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeBetween(String value1, String value2) {
            addCriterion("t_type between", value1, value2, "tType");
            return (Criteria) this;
        }

        public Criteria andTTypeNotBetween(String value1, String value2) {
            addCriterion("t_type not between", value1, value2, "tType");
            return (Criteria) this;
        }

        public Criteria andTModelNameIsNull() {
            addCriterion("t_model_name is null");
            return (Criteria) this;
        }

        public Criteria andTModelNameIsNotNull() {
            addCriterion("t_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andTModelNameEqualTo(String value) {
            addCriterion("t_model_name =", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotEqualTo(String value) {
            addCriterion("t_model_name <>", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameGreaterThan(String value) {
            addCriterion("t_model_name >", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_name >=", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameLessThan(String value) {
            addCriterion("t_model_name <", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameLessThanOrEqualTo(String value) {
            addCriterion("t_model_name <=", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameLike(String value) {
            addCriterion("t_model_name like", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotLike(String value) {
            addCriterion("t_model_name not like", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameIn(List<String> values) {
            addCriterion("t_model_name in", values, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotIn(List<String> values) {
            addCriterion("t_model_name not in", values, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameBetween(String value1, String value2) {
            addCriterion("t_model_name between", value1, value2, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotBetween(String value1, String value2) {
            addCriterion("t_model_name not between", value1, value2, "tModelName");
            return (Criteria) this;
        }

        public Criteria andModelYearIsNull() {
            addCriterion("model_year is null");
            return (Criteria) this;
        }

        public Criteria andModelYearIsNotNull() {
            addCriterion("model_year is not null");
            return (Criteria) this;
        }

        public Criteria andModelYearEqualTo(String value) {
            addCriterion("model_year =", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotEqualTo(String value) {
            addCriterion("model_year <>", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearGreaterThan(String value) {
            addCriterion("model_year >", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearGreaterThanOrEqualTo(String value) {
            addCriterion("model_year >=", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearLessThan(String value) {
            addCriterion("model_year <", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearLessThanOrEqualTo(String value) {
            addCriterion("model_year <=", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearLike(String value) {
            addCriterion("model_year like", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotLike(String value) {
            addCriterion("model_year not like", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearIn(List<String> values) {
            addCriterion("model_year in", values, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotIn(List<String> values) {
            addCriterion("model_year not in", values, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearBetween(String value1, String value2) {
            addCriterion("model_year between", value1, value2, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotBetween(String value1, String value2) {
            addCriterion("model_year not between", value1, value2, "modelYear");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpIsNull() {
            addCriterion("t_model_msrp is null");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpIsNotNull() {
            addCriterion("t_model_msrp is not null");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpEqualTo(String value) {
            addCriterion("t_model_msrp =", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpNotEqualTo(String value) {
            addCriterion("t_model_msrp <>", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpGreaterThan(String value) {
            addCriterion("t_model_msrp >", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_msrp >=", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpLessThan(String value) {
            addCriterion("t_model_msrp <", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpLessThanOrEqualTo(String value) {
            addCriterion("t_model_msrp <=", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpLike(String value) {
            addCriterion("t_model_msrp like", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpNotLike(String value) {
            addCriterion("t_model_msrp not like", value, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpIn(List<String> values) {
            addCriterion("t_model_msrp in", values, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpNotIn(List<String> values) {
            addCriterion("t_model_msrp not in", values, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpBetween(String value1, String value2) {
            addCriterion("t_model_msrp between", value1, value2, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelMsrpNotBetween(String value1, String value2) {
            addCriterion("t_model_msrp not between", value1, value2, "tModelMsrp");
            return (Criteria) this;
        }

        public Criteria andTModelSopIsNull() {
            addCriterion("t_model_sop is null");
            return (Criteria) this;
        }

        public Criteria andTModelSopIsNotNull() {
            addCriterion("t_model_sop is not null");
            return (Criteria) this;
        }

        public Criteria andTModelSopEqualTo(String value) {
            addCriterion("t_model_sop =", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopNotEqualTo(String value) {
            addCriterion("t_model_sop <>", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopGreaterThan(String value) {
            addCriterion("t_model_sop >", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_sop >=", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopLessThan(String value) {
            addCriterion("t_model_sop <", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopLessThanOrEqualTo(String value) {
            addCriterion("t_model_sop <=", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopLike(String value) {
            addCriterion("t_model_sop like", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopNotLike(String value) {
            addCriterion("t_model_sop not like", value, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopIn(List<String> values) {
            addCriterion("t_model_sop in", values, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopNotIn(List<String> values) {
            addCriterion("t_model_sop not in", values, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopBetween(String value1, String value2) {
            addCriterion("t_model_sop between", value1, value2, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelSopNotBetween(String value1, String value2) {
            addCriterion("t_model_sop not between", value1, value2, "tModelSop");
            return (Criteria) this;
        }

        public Criteria andTModelEopIsNull() {
            addCriterion("t_model_eop is null");
            return (Criteria) this;
        }

        public Criteria andTModelEopIsNotNull() {
            addCriterion("t_model_eop is not null");
            return (Criteria) this;
        }

        public Criteria andTModelEopEqualTo(String value) {
            addCriterion("t_model_eop =", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopNotEqualTo(String value) {
            addCriterion("t_model_eop <>", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopGreaterThan(String value) {
            addCriterion("t_model_eop >", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_eop >=", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopLessThan(String value) {
            addCriterion("t_model_eop <", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopLessThanOrEqualTo(String value) {
            addCriterion("t_model_eop <=", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopLike(String value) {
            addCriterion("t_model_eop like", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopNotLike(String value) {
            addCriterion("t_model_eop not like", value, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopIn(List<String> values) {
            addCriterion("t_model_eop in", values, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopNotIn(List<String> values) {
            addCriterion("t_model_eop not in", values, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopBetween(String value1, String value2) {
            addCriterion("t_model_eop between", value1, value2, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andTModelEopNotBetween(String value1, String value2) {
            addCriterion("t_model_eop not between", value1, value2, "tModelEop");
            return (Criteria) this;
        }

        public Criteria andEngineIsNull() {
            addCriterion("engine is null");
            return (Criteria) this;
        }

        public Criteria andEngineIsNotNull() {
            addCriterion("engine is not null");
            return (Criteria) this;
        }

        public Criteria andEngineEqualTo(String value) {
            addCriterion("engine =", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotEqualTo(String value) {
            addCriterion("engine <>", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineGreaterThan(String value) {
            addCriterion("engine >", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineGreaterThanOrEqualTo(String value) {
            addCriterion("engine >=", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineLessThan(String value) {
            addCriterion("engine <", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineLessThanOrEqualTo(String value) {
            addCriterion("engine <=", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineLike(String value) {
            addCriterion("engine like", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotLike(String value) {
            addCriterion("engine not like", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineIn(List<String> values) {
            addCriterion("engine in", values, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotIn(List<String> values) {
            addCriterion("engine not in", values, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineBetween(String value1, String value2) {
            addCriterion("engine between", value1, value2, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotBetween(String value1, String value2) {
            addCriterion("engine not between", value1, value2, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcIsNull() {
            addCriterion("engine_displacement_cc is null");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcIsNotNull() {
            addCriterion("engine_displacement_cc is not null");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcEqualTo(String value) {
            addCriterion("engine_displacement_cc =", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcNotEqualTo(String value) {
            addCriterion("engine_displacement_cc <>", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcGreaterThan(String value) {
            addCriterion("engine_displacement_cc >", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcGreaterThanOrEqualTo(String value) {
            addCriterion("engine_displacement_cc >=", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcLessThan(String value) {
            addCriterion("engine_displacement_cc <", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcLessThanOrEqualTo(String value) {
            addCriterion("engine_displacement_cc <=", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcLike(String value) {
            addCriterion("engine_displacement_cc like", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcNotLike(String value) {
            addCriterion("engine_displacement_cc not like", value, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcIn(List<String> values) {
            addCriterion("engine_displacement_cc in", values, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcNotIn(List<String> values) {
            addCriterion("engine_displacement_cc not in", values, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcBetween(String value1, String value2) {
            addCriterion("engine_displacement_cc between", value1, value2, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementCcNotBetween(String value1, String value2) {
            addCriterion("engine_displacement_cc not between", value1, value2, "engineDisplacementCc");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLIsNull() {
            addCriterion("engine_displacement_l is null");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLIsNotNull() {
            addCriterion("engine_displacement_l is not null");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLEqualTo(String value) {
            addCriterion("engine_displacement_l =", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotEqualTo(String value) {
            addCriterion("engine_displacement_l <>", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLGreaterThan(String value) {
            addCriterion("engine_displacement_l >", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLGreaterThanOrEqualTo(String value) {
            addCriterion("engine_displacement_l >=", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLLessThan(String value) {
            addCriterion("engine_displacement_l <", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLLessThanOrEqualTo(String value) {
            addCriterion("engine_displacement_l <=", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLLike(String value) {
            addCriterion("engine_displacement_l like", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotLike(String value) {
            addCriterion("engine_displacement_l not like", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLIn(List<String> values) {
            addCriterion("engine_displacement_l in", values, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotIn(List<String> values) {
            addCriterion("engine_displacement_l not in", values, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLBetween(String value1, String value2) {
            addCriterion("engine_displacement_l between", value1, value2, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotBetween(String value1, String value2) {
            addCriterion("engine_displacement_l not between", value1, value2, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andPowerIsNull() {
            addCriterion("power is null");
            return (Criteria) this;
        }

        public Criteria andPowerIsNotNull() {
            addCriterion("power is not null");
            return (Criteria) this;
        }

        public Criteria andPowerEqualTo(String value) {
            addCriterion("power =", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerNotEqualTo(String value) {
            addCriterion("power <>", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerGreaterThan(String value) {
            addCriterion("power >", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerGreaterThanOrEqualTo(String value) {
            addCriterion("power >=", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerLessThan(String value) {
            addCriterion("power <", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerLessThanOrEqualTo(String value) {
            addCriterion("power <=", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerLike(String value) {
            addCriterion("power like", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerNotLike(String value) {
            addCriterion("power not like", value, "power");
            return (Criteria) this;
        }

        public Criteria andPowerIn(List<String> values) {
            addCriterion("power in", values, "power");
            return (Criteria) this;
        }

        public Criteria andPowerNotIn(List<String> values) {
            addCriterion("power not in", values, "power");
            return (Criteria) this;
        }

        public Criteria andPowerBetween(String value1, String value2) {
            addCriterion("power between", value1, value2, "power");
            return (Criteria) this;
        }

        public Criteria andPowerNotBetween(String value1, String value2) {
            addCriterion("power not between", value1, value2, "power");
            return (Criteria) this;
        }

        public Criteria andTurboBoostIsNull() {
            addCriterion("turbo_boost is null");
            return (Criteria) this;
        }

        public Criteria andTurboBoostIsNotNull() {
            addCriterion("turbo_boost is not null");
            return (Criteria) this;
        }

        public Criteria andTurboBoostEqualTo(String value) {
            addCriterion("turbo_boost =", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostNotEqualTo(String value) {
            addCriterion("turbo_boost <>", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostGreaterThan(String value) {
            addCriterion("turbo_boost >", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostGreaterThanOrEqualTo(String value) {
            addCriterion("turbo_boost >=", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostLessThan(String value) {
            addCriterion("turbo_boost <", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostLessThanOrEqualTo(String value) {
            addCriterion("turbo_boost <=", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostLike(String value) {
            addCriterion("turbo_boost like", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostNotLike(String value) {
            addCriterion("turbo_boost not like", value, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostIn(List<String> values) {
            addCriterion("turbo_boost in", values, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostNotIn(List<String> values) {
            addCriterion("turbo_boost not in", values, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostBetween(String value1, String value2) {
            addCriterion("turbo_boost between", value1, value2, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andTurboBoostNotBetween(String value1, String value2) {
            addCriterion("turbo_boost not between", value1, value2, "turboBoost");
            return (Criteria) this;
        }

        public Criteria andGearboxDescIsNull() {
            addCriterion("gearbox_desc is null");
            return (Criteria) this;
        }

        public Criteria andGearboxDescIsNotNull() {
            addCriterion("gearbox_desc is not null");
            return (Criteria) this;
        }

        public Criteria andGearboxDescEqualTo(String value) {
            addCriterion("gearbox_desc =", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotEqualTo(String value) {
            addCriterion("gearbox_desc <>", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescGreaterThan(String value) {
            addCriterion("gearbox_desc >", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescGreaterThanOrEqualTo(String value) {
            addCriterion("gearbox_desc >=", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescLessThan(String value) {
            addCriterion("gearbox_desc <", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescLessThanOrEqualTo(String value) {
            addCriterion("gearbox_desc <=", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescLike(String value) {
            addCriterion("gearbox_desc like", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotLike(String value) {
            addCriterion("gearbox_desc not like", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescIn(List<String> values) {
            addCriterion("gearbox_desc in", values, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotIn(List<String> values) {
            addCriterion("gearbox_desc not in", values, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescBetween(String value1, String value2) {
            addCriterion("gearbox_desc between", value1, value2, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotBetween(String value1, String value2) {
            addCriterion("gearbox_desc not between", value1, value2, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andDrivingModelIsNull() {
            addCriterion("driving_model is null");
            return (Criteria) this;
        }

        public Criteria andDrivingModelIsNotNull() {
            addCriterion("driving_model is not null");
            return (Criteria) this;
        }

        public Criteria andDrivingModelEqualTo(String value) {
            addCriterion("driving_model =", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelNotEqualTo(String value) {
            addCriterion("driving_model <>", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelGreaterThan(String value) {
            addCriterion("driving_model >", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelGreaterThanOrEqualTo(String value) {
            addCriterion("driving_model >=", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelLessThan(String value) {
            addCriterion("driving_model <", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelLessThanOrEqualTo(String value) {
            addCriterion("driving_model <=", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelLike(String value) {
            addCriterion("driving_model like", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelNotLike(String value) {
            addCriterion("driving_model not like", value, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelIn(List<String> values) {
            addCriterion("driving_model in", values, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelNotIn(List<String> values) {
            addCriterion("driving_model not in", values, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelBetween(String value1, String value2) {
            addCriterion("driving_model between", value1, value2, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andDrivingModelNotBetween(String value1, String value2) {
            addCriterion("driving_model not between", value1, value2, "drivingModel");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNull() {
            addCriterion("import_time is null");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNotNull() {
            addCriterion("import_time is not null");
            return (Criteria) this;
        }

        public Criteria andImportTimeEqualTo(Date value) {
            addCriterion("import_time =", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotEqualTo(Date value) {
            addCriterion("import_time <>", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThan(Date value) {
            addCriterion("import_time >", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("import_time >=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThan(Date value) {
            addCriterion("import_time <", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThanOrEqualTo(Date value) {
            addCriterion("import_time <=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeIn(List<Date> values) {
            addCriterion("import_time in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotIn(List<Date> values) {
            addCriterion("import_time not in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeBetween(Date value1, Date value2) {
            addCriterion("import_time between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotBetween(Date value1, Date value2) {
            addCriterion("import_time not between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}