SELECT YEAR( active_time ) [year]
   ,DATEPART(QUARTER,active_time) [quarter]
   ,partner_id
   ,COUNT(1) act
  FROM (
   SELECT MIN(ws.create_time) active_time
    ,ws.workshop_id
    ,w.partner_id
   FROM wx_t_workshop_status ws
   LEFT JOIN wx_t_work_shop w ON ws.workshop_id = w.id
   WHERE ws.workshop_with_status = '3'
    AND w.customer_type = 1 --终端客户类型＿-门店,2-车队＿-工程机械＿-项目工地
    AND w.business_weight = 1
   GROUP BY ws.workshop_id
    ,w.partner_id
  ) act
  WHERE YEAR( act.active_time ) >= 2021
  GROUP BY YEAR( active_time ) 
   ,DATEPART(QUARTER,active_time)
   ,partner_id