<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.task.dao.WxTaskAttachMapper">
  <resultMap id="BaseResultMap" type="com.chevron.task.model.WxTaskAttach">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    <id column="att_id" jdbcType="BIGINT" property="attId" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="storage_name" jdbcType="VARCHAR" property="storageName" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="upload_user" jdbcType="BIGINT" property="uploadUser" />
    <result column="att_type" jdbcType="VARCHAR" property="attType" />
    <result column="zt" jdbcType="VARCHAR" property="zt" />
    <result column="xz_sj" jdbcType="TIMESTAMP" property="xzSj" />
    <result column="xz_user" jdbcType="BIGINT" property="xzUser" />
    <result column="xg_sj" jdbcType="TIMESTAMP" property="xgSj" />
    <result column="xg_user" jdbcType="BIGINT" property="xgUser" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
   <resultMap id="SimpResultMap" type="com.chevron.task.model.WxTaskAttachSimp">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    <id column="att_id" jdbcType="BIGINT" property="attId" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="storage_name" jdbcType="VARCHAR" property="storageName" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="upload_user" jdbcType="BIGINT" property="uploadUser" />
    <result column="att_type" jdbcType="VARCHAR" property="attType" />
  </resultMap> 
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    att_id, source_id, file_name, storage_name, file_type, source_type, file_size, upload_user, 
    att_type, zt, xz_sj, xz_user, xg_sj, xg_user, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.chevron.task.model.WxTaskAttachExample" resultMap="SimpResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_task_attach
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from wx_task_attach
    where att_id = #{attId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    delete from wx_task_attach
    where att_id = #{attId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.task.model.WxTaskAttachExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    delete from wx_task_attach
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.task.model.WxTaskAttach">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    insert into wx_task_attach (att_id, source_id, file_name, 
      storage_name, file_type, source_type, 
      file_size, upload_user, att_type, 
      zt, xz_sj, xz_user, 
      xg_sj, xg_user, tenant_id
      )
    values (#{attId,jdbcType=BIGINT}, #{sourceId,jdbcType=BIGINT}, #{fileName,jdbcType=VARCHAR}, 
      #{storageName,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, #{sourceType,jdbcType=VARCHAR}, 
      #{fileSize,jdbcType=BIGINT}, #{uploadUser,jdbcType=BIGINT}, #{attType,jdbcType=VARCHAR}, 
      #{zt,jdbcType=VARCHAR}, #{xzSj,jdbcType=TIMESTAMP}, #{xzUser,jdbcType=BIGINT}, 
      #{xgSj,jdbcType=TIMESTAMP}, #{xgUser,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective"  useGeneratedKeys="true" keyProperty="attId" parameterType="com.chevron.task.model.WxTaskAttach">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    insert into wx_task_attach
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="attId != null">
        att_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="storageName != null">
        storage_name,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="uploadUser != null">
        upload_user,
      </if>
      <if test="attType != null">
        att_type,
      </if>
      <if test="zt != null">
        zt,
      </if>
      <if test="xzSj != null">
        xz_sj,
      </if>
      <if test="xzUser != null">
        xz_user,
      </if>
      <if test="xgSj != null">
        xg_sj,
      </if>
      <if test="xgUser != null">
        xg_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="attId != null">
        #{attId,jdbcType=BIGINT},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="storageName != null">
        #{storageName,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="uploadUser != null">
        #{uploadUser,jdbcType=BIGINT},
      </if>
      <if test="attType != null">
        #{attType,jdbcType=VARCHAR},
      </if>
      <if test="zt != null">
        #{zt,jdbcType=VARCHAR},
      </if>
      <if test="xzSj != null">
        #{xzSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xzUser != null">
        #{xzUser,jdbcType=BIGINT},
      </if>
      <if test="xgSj != null">
        #{xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xgUser != null">
        #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.task.model.WxTaskAttachExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    select count(*) from wx_task_attach
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    update wx_task_attach
    <set>
      <if test="record.attId != null">
        att_id = #{record.attId,jdbcType=BIGINT},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=BIGINT},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.storageName != null">
        storage_name = #{record.storageName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.fileSize != null">
        file_size = #{record.fileSize,jdbcType=BIGINT},
      </if>
      <if test="record.uploadUser != null">
        upload_user = #{record.uploadUser,jdbcType=BIGINT},
      </if>
      <if test="record.attType != null">
        att_type = #{record.attType,jdbcType=VARCHAR},
      </if>
      <if test="record.zt != null">
        zt = #{record.zt,jdbcType=VARCHAR},
      </if>
      <if test="record.xzSj != null">
        xz_sj = #{record.xzSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xzUser != null">
        xz_user = #{record.xzUser,jdbcType=BIGINT},
      </if>
      <if test="record.xgSj != null">
        xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xgUser != null">
        xg_user = #{record.xgUser,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    update wx_task_attach
    set att_id = #{record.attId,jdbcType=BIGINT},
      source_id = #{record.sourceId,jdbcType=BIGINT},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      storage_name = #{record.storageName,jdbcType=VARCHAR},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      source_type = #{record.sourceType,jdbcType=VARCHAR},
      file_size = #{record.fileSize,jdbcType=BIGINT},
      upload_user = #{record.uploadUser,jdbcType=BIGINT},
      att_type = #{record.attType,jdbcType=VARCHAR},
      zt = #{record.zt,jdbcType=VARCHAR},
      xz_sj = #{record.xzSj,jdbcType=TIMESTAMP},
      xz_user = #{record.xzUser,jdbcType=BIGINT},
      xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      xg_user = #{record.xgUser,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.task.model.WxTaskAttach">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    update wx_task_attach
    <set>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="storageName != null">
        storage_name = #{storageName,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="uploadUser != null">
        upload_user = #{uploadUser,jdbcType=BIGINT},
      </if>
      <if test="attType != null">
        att_type = #{attType,jdbcType=VARCHAR},
      </if>
      <if test="zt != null">
        zt = #{zt,jdbcType=VARCHAR},
      </if>
      <if test="xzSj != null">
        xz_sj = #{xzSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xzUser != null">
        xz_user = #{xzUser,jdbcType=BIGINT},
      </if>
      <if test="xgSj != null">
        xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xgUser != null">
        xg_user = #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where att_id = #{attId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.task.model.WxTaskAttach">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 03 00:40:46 CST 2015.
    -->
    update wx_task_attach
    set source_id = #{sourceId,jdbcType=BIGINT},
      file_name = #{fileName,jdbcType=VARCHAR},
      storage_name = #{storageName,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=BIGINT},
      upload_user = #{uploadUser,jdbcType=BIGINT},
      att_type = #{attType,jdbcType=VARCHAR},
      zt = #{zt,jdbcType=VARCHAR},
      xz_sj = #{xzSj,jdbcType=TIMESTAMP},
      xz_user = #{xzUser,jdbcType=BIGINT},
      xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      xg_user = #{xgUser,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where att_id = #{attId,jdbcType=BIGINT}
  </update>
</mapper>