<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.master.dao.PurchaseIncentiveSkuMapper">

    <select id="querySkuDetail" resultType="com.chevron.master.model.PurchaseIncentiveSku">
        select tab1.stock_to as workShopId, try_convert(datetime, tab1.dateStr, 126) as outDate, tab1.sku, tab1.name,
            tab1.qhc_type as productType, tab1.liter
        from (
            select wtos.stock_to, format(wtos.out_time, 'yyyy-MM-dd') as dateStr, wtosp.sku, max(product.name) as name,
                sum(convert(decimal, product.capacity)*ISNULL(wtosp.actual_out_count, 0)) as liter,
                max(qhc_sku.qhc_type) as qhc_type
            from wx_t_out_stock wtos
            inner join wx_t_out_stock_product wtosp on wtos.stock_out_no = wtosp.stock_out_no
            inner join wx_t_product product ON wtosp.sku = product.sku
            inner join PP_MID.dbo.mid_qhc_sku_product qhc_sku on wtosp.sku = qhc_sku.sku and qhc_sku.sku_type = 'qhc_1_and_4'
            where wtos.out_time between #{startTime} and #{endTime} and wtos.order_type = 'workshop' and wtos.status = '2'
            and wtos.stock_to in (
                <foreach collection="workShopIds" item="workShopId" separator=",">${workShopId}</foreach>
            )
            group by wtos.stock_to, format(wtos.out_time, 'yyyy-MM-dd'), wtosp.sku
        ) tab1
        inner join (
            select stock_to, min(dateStr) as dateStr from  (
                select wtos.stock_to, format(wtos.out_time, 'yyyy-MM-dd') as dateStr
                from wx_t_out_stock wtos
                inner join wx_t_out_stock_product wtosp on wtos.stock_out_no = wtosp.stock_out_no
                inner join wx_t_product product ON wtosp.sku = product.sku
                where wtos.out_time between #{startTime} and #{endTime} and wtos.order_type = 'workshop' and wtos.status = '2'
                and wtos.stock_to in (
                    <foreach collection="workShopIds" item="workShopId" separator=",">${workShopId}</foreach>
                )
                and wtosp.sku in (
                    select sku from PP_MID.dbo.mid_qhc_sku_product where sku_type = 'qhc_1_and_4'
                )
                group by wtos.stock_to, format(wtos.out_time, 'yyyy-MM-dd') having sum(convert(decimal, product.capacity)* ISNULL(wtosp.actual_out_count, 0)) >= ${capacity}
            ) tab2 group by stock_to
        ) tab3 on tab1.stock_to = tab3.stock_to and tab1.dateStr = tab3.dateStr
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into wx_t_purchase_incentive_sku (purchase_incentive_id, work_shop_id, out_date, sku, name, product_type,
            liter, create_user_id, create_time, update_user_id, update_time) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.purchaseIncentiveId,jdbcType=BIGINT}, #{item.workShopId,jdbcType=BIGINT}, #{item.outDate,jdbcType=TIMESTAMP},
                #{item.sku,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.productType,jdbcType=VARCHAR},
                #{item.liter,jdbcType=DECIMAL}, #{item.createUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateUserId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>