select isnull(s1.dic_item_name, cs1.customer_name_cn) from_distributor_name, 
isnull(cs1.region, s1.dic_item_name) from_region,
cs1.sales_name_cn from_flsr, cs1.supervisor_name_cn from_asm, diac1.dic_item_name channel_text,
count(1) report_times, 
(select (select IIF(ir3.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(ir3.punish_flag & 2 > 0 and ir3.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,ir3.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(ir3.punish_flag & 32 > 0 and ir3.attribute8 is not null, '扣除' + ir3.attribute3 + '年第' + 
case when ir3.attribute7='1' then '一' when ir3.attribute7='2' then '二' 
when ir3.attribute7='3' then '三'
when ir3.attribute7='4' then '四' end + '季度的季度奖励的' + ir3.attribute8 + '%;','')
+ IIF(ir3.punish_flag & 16 > 0 and ir3.attribute6 is not null, '扣除' + ir3.attribute3 + '年的年终奖励的' + ir3.attribute6 + '%;','') from wx_t_non_local_mkt_flow_form if1
			left join wx_t_non_local_mkt_flow_pro_res ir3 on ir3.flow_id = if1.id and ir3.setp_no = 'ABM_CONFIRM'
			where if1.from_distributor_id=rv.from_distributor_id 
			and ir3.channel & rv.channel>0 and ir3.kpi_result=0
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or if1.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or if1.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
			 FOR XML PATH,TYPE
 ).value('.','varchar(max)')) non_local_punish,rv.from_distributor_id,rv.channel
 from view_nonlocal_sale_report rv
        left join wx_t_dic_item s1 on cast(s1.dic_item_code as bigint) = rv.from_distributor_id and s1.dic_type_code = 'non_local_special_partner'
        left join dw_customer_org_sales cs1 on cs1.distributor_id = rv.from_distributor_id and cs1.channel_weight&rv.channel>0
	  left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=rv.channel
where (#VARCHAR,fromRegion# is null or #VARCHAR,fromRegion#='' or isnull(cs1.region, s1.dic_item_name)=#VARCHAR,fromRegion#)
and (#BIGINT,fromDistributorId# is null or rv.from_distributor_id=#BIGINT,fromDistributorId#)
and (#INTEGER,channel# is null or rv.channel&#INTEGER,channel#>0)
and (#INTEGER,permissionWeight#&1>0 or (#INTEGER,permissionWeight#&1024>0 and #BIGINT,fromDistributorId# is not null) or (#INTEGER,permissionWeight#&10>0 and exists (select 1 from dw_sales_role dp_001_sr1 where dp_001_sr1.[sales_cai]=cs1.sales_cai and dp_001_sr1.sales_cai_level like '%_'+#VARCHAR,cai#+'%')))
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or rv.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or rv.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
group by s1.dic_item_name,cs1.customer_name_cn,cs1.region,cs1.sales_name_cn,cs1.supervisor_name_cn,diac1.dic_item_name,rv.from_distributor_id,rv.channel