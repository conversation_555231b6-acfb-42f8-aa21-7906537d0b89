update wx_promote_plan set open_shop_gd_packs_count=70, seminar_packs_high_count=10,seminar_packs_count=12 where id=1019;

SELECT t_distribution.* /*SUM(t_distribution.open_shop_packs_distribution_count) AS open_shop_packs_distribution_count, SUM(t_distribution.open_shop_gd_packs_distribution_count) AS open_shop_gd_packs_distribution_count
	, SUM(t_distribution.road_show_activi_packs_distribution_count) AS road_show_activi_packs_distribution_count, SUM(t_distribution.road_show_consumer_packs_distribution_count) AS road_show_consumer_packs_distribution_count
	, SUM(t_distribution.seminar_packs_distribution_count) AS seminar_packs_distribution_count, SUM(t_distribution.points_distribution_count) AS points_distribution_count
	, SUM(t_distribution.venue_meal_distribution) AS venue_meal_distribution, SUM(t_distribution.seminar_high_packs_distribution_count) AS seminar_high_packs_distribution_count
	, SUM(t_distribution.store_packs_distribution_count) AS store_packs_distribution_count, SUM(t_distribution.agriculture_packs_distribution_count) AS agriculture_packs_distribution_count
	, SUM(t_distribution.try_packs_distribution_count) AS try_packs_distribution_count, SUM(t_distribution.advert_packs_distribution_count) AS advert_packs_distribution_count*/
FROM wx_promote_distribution t_distribution
	LEFT JOIN wx_promote_application_batch t_aplly ON t_aplly.batchid = t_distribution.apply_batch_id
WHERE (1 = 1
	AND t_aplly.approve_status = 1
	AND t_aplly.prokey = 'promoteDistribution'
	AND t_distribution.distribution_user_id = '129902')

--修改CM分配给ASM
update wx_promote_distribution set open_shop_gd_packs_distribution_count=39,seminar_packs_distribution_count=8,seminar_high_packs_distribution_count=4 where id=1317;
--孙宇峰
update wx_promote_distribution set open_shop_gd_packs_distribution_count=31,seminar_packs_distribution_count=7,seminar_high_packs_distribution_count=2 where id=1316;

select crss.region_name, crss.suppervisor_name, crss.sales_name, a.distributor_id, a.activity_organizers, a.资源包, a.数量, a.月份, a.release_time from (
select 'Seminar[优质](包含场地费用和赠送TCP)' "资源包", convert(varchar(6), t_activity.release_time, 112) "月份", t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, t_seminar_activity_detail.apply_high_packs "数量"
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_seminar_activity_detail t_seminar_activity_detail ON t_seminar_activity_detail.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	and t_activity.activity_type='YTH'
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and t_seminar_activity_detail.apply_high_packs>0
/*Seminar[普通](包含场地费用和赠送TCP)*/
union all select 'Seminar[普通](包含场地费用和赠送TCP)' expense_code, convert(varchar(6), t_activity.release_time, 112) month, t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, t_seminar_activity_detail.apply_packs_count
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_seminar_activity_detail t_seminar_activity_detail ON t_seminar_activity_detail.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	and t_activity.activity_type='YTH'
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and t_seminar_activity_detail.apply_packs_count>0
/*新店开业礼包[优质]*/
union all select '新店开业礼包[优质]' expense_code,convert(varchar(6), t_activity.release_time, 112) month, t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, t_xd_open.gd_packs_number
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_xd_open_detail t_xd_open ON t_xd_open.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and t_xd_open.gd_packs_number>0
	and t_activity.activity_type='DLXDKY'
/*新店开业礼包[普通]*/
union all select '新店开业礼包[普通]' expense_code,convert(varchar(6), t_activity.release_time, 112) month, t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, t_xd_open.packs_number
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_xd_open_detail t_xd_open ON t_xd_open.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and t_xd_open.packs_number>0
	and t_activity.activity_type='DLXDKY'
/*路演物料包*/
union all select '路演物料包' expense_code,convert(varchar(6), t_activity.release_time, 112) month, t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, t_roadshow_detail.road_show_gif_package_count
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_roadshow_activity_detail t_roadshow_detail ON t_roadshow_detail.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and t_roadshow_detail.road_show_gif_package_count>0
	and t_activity.activity_type='DLLY'
/*路演物料包*/
union all select '客户互动包' expense_code,convert(varchar(6), t_activity.release_time, 112) month, t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, t_roadshow_detail.road_show_consumer_packs_count
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_roadshow_activity_detail t_roadshow_detail ON t_roadshow_detail.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and t_roadshow_detail.road_show_consumer_packs_count>0
	and t_activity.activity_type='DLLY'
/*农垦/矿山/工程机械物料包*/
union all select '农垦/矿山/工程机械物料包' expense_code,convert(varchar(6), t_activity.release_time, 112) month, t_activity.release_time, 
pe.distributor_id, t_activity.activity_organizers, d.packs_number 
FROM wx_promote_application_batch promote_appl_batch
	INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
	INNER JOIN wx_promote_agriculture_detail d ON d.activity_id = t_activity.id
	left join wx_t_partner_o2o_enterprise pe on t_activity.activity_organizers_id=pe.partner_id
	where promote_appl_batch.approve_status = 3
	AND promote_appl_batch.current_step = 'marketing'
	AND promote_appl_batch.prokey = 'promoteApplication'
	and d.packs_number>0
	and t_activity.activity_type='NKKSGCJX'
) a 
left join [dw_customer_region_sales_supervisor_rel] crss on a.distributor_id=crss.distributor_id
where crss.region_name like 'C&I%'
order by crss.region_name, crss.suppervisor_name, crss.sales_name, a.distributor_id, a.资源包, a.月份
