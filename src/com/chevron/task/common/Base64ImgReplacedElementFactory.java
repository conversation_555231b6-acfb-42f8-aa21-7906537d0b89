package com.chevron.task.common;

import com.lowagie.text.BadElementException;
import com.lowagie.text.Image;
import com.lowagie.text.pdf.codec.Base64;
import org.w3c.dom.Element;
import org.xhtmlrenderer.extend.FSImage;
import org.xhtmlrenderer.extend.ReplacedElement;
import org.xhtmlrenderer.extend.ReplacedElementFactory;
import org.xhtmlrenderer.extend.UserAgentCallback;
import org.xhtmlrenderer.layout.LayoutContext;
import org.xhtmlrenderer.pdf.ITextFSImage;
import org.xhtmlrenderer.pdf.ITextImageElement;
import org.xhtmlrenderer.render.BlockBox;
import org.xhtmlrenderer.simple.extend.FormSubmissionListener;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * @author: chenBin
 * @date: 20/04/2020
 */
public class Base64ImgReplacedElementFactory implements ReplacedElementFactory {

    /**
     *  * 实现createReplacedElement 替换html中的Img标签
     *  * 
     *  * @param c 上下文
     *  * @param box 盒子
     *  * @param uac 回调
     *  * @param cssWidth css宽
     *  * @param cssHeight css高
     *  * @return ReplacedElement
     *  
     */
    @Override
    public ReplacedElement createReplacedElement(LayoutContext c, BlockBox box, UserAgentCallback uac,
                                                 int cssWidth, int cssHeight) {
        Element e = box.getElement();
        if (e == null) {
            return null;
        }
        String nodeName = e.getNodeName();
        // 找到img标签
        if (nodeName.equals("img")) {
            String attribute = e.getAttribute("src");
            FSImage fsImage;
            try {
                // 生成itext图像
                fsImage = buildImage(attribute, uac);
            } catch (BadElementException e1) {
                fsImage = null;
            } catch (IOException e1) {
                fsImage = null;
            }
            if (fsImage != null) {
                // 对图像进行缩放
                if (cssWidth != -1 || cssHeight != -1) {
                    fsImage.scale(cssWidth, cssHeight);
                }
                return new ITextImageElement(fsImage);
            }


        }


        return null;
    }


    /**
     *  * 编解码base64并生成itext图像  
     *  
     */
    protected ITextFSImage buildImage(String srcAttr, UserAgentCallback uac) throws IOException,
            BadElementException {
        ITextFSImage fiImg = null;
        if (srcAttr.toLowerCase().startsWith("data:image/")) {
            String base64Code = srcAttr.substring(srcAttr.indexOf("base64,") + "base64,".length(),
                    srcAttr.length());
            // 解码
            byte[] decodedBytes = Base64.decode(base64Code);
            fiImg = new ITextFSImage(Image.getInstance(decodedBytes));
        } else {
            fiImg = (ITextFSImage) uac.getImageResource(srcAttr).getImage();
        }
        //压缩图片
        BufferedImage bufferedImage = ImageIO.read(fiImg.getImage().getUrl());
        int scaledWidth = bufferedImage.getWidth(null) / 4;
        int scaledHeight = bufferedImage.getHeight(null) / 4;
        BufferedImage scaledAwtImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = scaledAwtImage.createGraphics();
        g.drawImage(bufferedImage, 0, 0, scaledWidth, scaledHeight, null);
        g.dispose();

        ByteArrayOutputStream bout = new ByteArrayOutputStream();
        ImageIO.write(scaledAwtImage, "jpeg", bout);
        byte[] imageBytes = bout.toByteArray();

        fiImg = new ITextFSImage(Image.getInstance(imageBytes));
        return fiImg;
    }

    @Override
    public void reset() {

    }

    @Override
    public void remove(Element element) {

    }

    @Override
    public void setFormSubmissionListener(FormSubmissionListener formSubmissionListener) {

    }
}
