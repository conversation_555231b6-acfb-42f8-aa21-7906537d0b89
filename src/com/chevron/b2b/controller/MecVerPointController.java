package com.chevron.b2b.controller;

import com.chevron.b2b.business.MecVerPointBizService;
import com.chevron.b2b.model.B2bVerifyPointPolicyParams;
import com.chevron.b2b.model.MecVerPointParams;
import com.chevron.disb2b.model.Db2bOrderParams;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.exception.auth.WxAuthException;
import com.common.util.ContextUtil;
import com.common.util.DateUtils;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.StringUtils;
import com.sys.file.web.FileManager;
import com.sys.utils.business.ReportViewBizService;
import com.sys.utils.model.AsynProcessStatus;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 技师核销积分Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2018-11-29 15:46
 */
@Controller
@RequestMapping(value="/mecverpoint")
public class MecVerPointController {
	@Autowired
	private MecVerPointBizService mecVerPointBizService;
	
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	private final static Logger log = Logger.getLogger(MecVerPointController.class);

	/**
	 * 技师核销积分列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(MecVerPointParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			mecVerPointBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}

	/**
	 * 技师核销积分列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/freezedetaildata.do")
	public Map<String,Object> queryForFreezeDetailPage(MecVerPointParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			mecVerPointBizService.queryForFreezeDetailPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}

	/**
	 * 技师核销积分列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/unfreezedetaildata.do")
	public Map<String,Object> queryForUnfreezeDetailPage(MecVerPointParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			mecVerPointBizService.queryForUnfreezeDetailPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
	

	/**
	 * B2B瓶盖扫码数据
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/capdata.do")
	public Map<String,Object> queryCapForPage(MecVerPointParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			mecVerPointBizService.queryCapForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			request.getSession().setAttribute("cap_params", params);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
	
	/**
	 * 瓶盖扫码导出
	 * @param params
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/export.do")
	public ResponseMap export(final MecVerPointParams params,final HttpServletRequest request, HttpServletResponse response) {
		final ResponseMap resMap = new ResponseMap();
		final Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("dateFrom", params.getCreateDateFrom());
        paramMap.put("dateTo", params.getCreateDateTo());
        paramMap.put("queryField", params.getQueryField()==""?null:params.getQueryField());
        paramMap.put("workshopId", params.getWorkshopId());
        paramMap.put("partnerId", params.getDistributorId());
        paramMap.put("productName", params.getProductName()==""?null:params.getProductName());
        paramMap.put("mechanicName", params.getMechanicName()==""?null:params.getMechanicName());
		log.info("瓶盖扫码导出 export: ");
		try {
		//异步加载
		final Long userId = ContextUtil.getCurUserId();
		final AsynProcessStatus processStatus = new AsynProcessStatus("com.chevron.b2b.controller.MecVerPointController.export", userId);
		processStatus.setAttrs(new HashMap<String, Object>(2));
		processStatus.save();
		new Thread(){
			public void run() {
				try {
						Long start = System.currentTimeMillis();
						log.debug("处理开始：" + start+ "ms");
						processStatus.setStatus(AsynProcessStatus.STATUS_PROCESSING);
						processStatus.setMessage("加载数据中...");
						processStatus.save();
						//创建文件
						String fileName = "瓶盖扫码"+DateUtils.getTimestamp(new Date());
						String folderPath = FileManager.fileUploadPath + "瓶盖扫码/";
						String filePath = folderPath + fileName + ".xlsx";
						File folder = new File(folderPath);
						if(!folder.exists()){
							folder.mkdirs();
						}
						final File file = new File(filePath);
						processStatus.getAttrs().put("filePath", filePath);
						processStatus.getAttrs().put("fileName", file.getName());
				
			
						OutputStream outputStream = null;
						outputStream = new FileOutputStream(file);
						processStatus.setMessage("写入数据中......");
						processStatus.save();
						reportViewBizService.exportByExcel("b2bCapScanning","ExportCapScanning","b2b.cap.export",paramMap,"Sheet1",outputStream,true);
						outputStream.flush();
						outputStream.close();
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
					
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
					processStatus.setMessage("导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
					processStatus.save();
				}
			};
		}.start();
		
		resMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
		resMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		} catch (Exception e) {
			log.error(e.getMessage());
			resMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resMap.setErrorMsg(WxAuthException.System_Exception_msg);
		}
		return resMap;

	}
}
