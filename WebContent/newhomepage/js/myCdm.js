$(function() {
    if(isMenuPermitted == false){
        $(".dmsURL").hide();
    }
    myCdmFunc.init();
});
var gridStore=null;
var grid = null;
var rpc = common.rpcClient;
var totalSellin = 0.00;
var totalTargetSellin = 0.00;
function openSellinAnalysisPage(partnerId,partnerName) {
    // myCdmFunc.openUrl("newhomepage/jsp/asm/SpSellInInfoAnalysis.jsp?partnerId="+partnerId + "&partnerName=" + encodeURIComponent(encodeURIComponent(partnerName)),"SpSellInInfoAnalysis","经销商Sell-in数据分析");
    myCdmFunc.openUrl("newhomepage/jsp/asm/SpSellInInfoAnalysis.jsp?year=" + currentYear + "&month=" + currentMonth,"SpSellInInfoAnalysis","合伙人sell in数据分析");
}
function openSalesThroughAnalysisPage(partnerId,partnerName) {
    myCdmFunc.openUrl("newhomepage/jsp/asm/SpSalesThroughAnalysis.jsp?year=" + currentYear + "&month=" + currentMonth + "&partnerId="+partnerId + "&partnerName=" + encodeURIComponent(encodeURIComponent(partnerName)),"SpSalesThroughAnalysis","合伙人sell through数据分析");
}
function openSalesPerfAnalysis(partnerId,partnerName) {
    myCdmFunc.openUrl("newhomepage/jsp/asm/SpSalesPerfAnalysis.jsp?year=" + currentYear + "&month=" + currentMonth + "&partnerId="+partnerId + "&partnerName=" + encodeURIComponent(encodeURIComponent(partnerName)),"SpSalesPerfAnalysis","合伙人营销情况分析");
}
function getTopSpItem() {
    var dataList = gridStore.getResult();
    var topItem = null;
    if(dataList && dataList.length >0){
        topItem = gridStore.getResult()[0];
    }
    return topItem
}
function openSalesPerfAnalysisPage() {
    var topItem = getTopSpItem();
    if(topItem){
        myCdmFunc.openUrl("newhomepage/jsp/asm/SpSalesPerfAnalysis.jsp?partnerId="+topItem.id + "&partnerName=" + encodeURIComponent(encodeURIComponent(topItem.organizationName)),"SpSalesPerfAnalysis","合伙人营销情况分析");
    } else {
        myCdmFunc.openUrl("newhomepage/jsp/asm/SpSalesPerfAnalysis.jsp","SpSalesPerfAnalysis","合伙人营销情况分析");
    }
}
function isSp(partnerPropertyText) {
    var result = false;
    if(partnerPropertyText === '普通合伙人') {
        result = true;
    }
    return result;
}
function genSalesPerfAnalysisHtml(value,item) {
    if(value!=null && value!=undefined){
        var opt= "<a href='javascript:void(0);' class='black_url' onclick='openSalesPerfAnalysis(" + item.id + "," + "\"" + item.organizationName + "\"" +")'>"+value+"</a>&nbsp;";
        return opt;
    } else {
        return value
    }
}
function genSellinAnalysisPageHtml(value,item) {
    if(value!=null && value!=undefined){
        var opt= "<a href='javascript:void(0);' class='black_url' onclick='openSellinAnalysisPage(" + item.id + "," + "\"" + item.organizationName + "\"" +")'>"+value+"</a>&nbsp;";
        return opt;
    } else {
        return value
    }
}
function genSalesThroughAnalysisPageHtml(value,item) {
    if(value!=null && value!=undefined){
        var opt= "<a href='javascript:void(0);' class='black_url' onclick='openSalesThroughAnalysisPage(" + item.id + ","+ "\"" + item.organizationName + "\"" +")'>"+value+"</a>&nbsp;";
        return opt;
    } else {
        return value;
    }
}

function genShowFundAndPointValueHtml(grossAndMktValue,item) {
    if (grossAndMktValue != null && grossAndMktValue != undefined) {
        var organId = "fundAndPointDetail" + item.id;
        var detailContent = "<div class='tooltip_templates'><div id='" + organId + "'>";
        detailContent += "<table><tr><td><span class='mkt-span' style='min-width: 60px;width: 60px;margin-right: 5px;'>基金:</span><span>" + myCdmFunc.toThousands(grossAndMktValue.totalFundValue) + "元</span></td>" + "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>进货积分:</span><span>" + myCdmFunc.toThousands(grossAndMktValue.cmdStockPoint) + "元</span></td>" + "</tr>";
        detailContent += "</table></div></div>";
        var opt = "<div style='display: inline-block' data-tooltip-content='#" + organId + "' class='gross-mkt black_url'>" + myCdmFunc.toThousands(grossAndMktValue.totalFundValue + grossAndMktValue.cmdStockPoint) + "</div>&nbsp;" + detailContent;
        return opt;
    } else {
        return (grossAndMktValue.totalFundValue + grossAndMktValue.cmdStockPoint);
    }
}

function genShowGrossAndMktValueHtml(grossAndMktValue,item) {
    if(grossAndMktValue!=null && grossAndMktValue!=undefined){
        var organId = "grossDetail" + item.id;
        var detailContent = "<div class='tooltip_templates'><div id='"+ organId +"'>";
        // var mktCostValue = grossAndMktValue.mktCostValue;
        var mktCostValue = grossAndMktValue.trainingCost + grossAndMktValue.cdmMaterialPoint + grossAndMktValue.newShopCost + grossAndMktValue.seminarCost  + grossAndMktValue.promotionValue + grossAndMktValue.b2bPointValue*0.25;
        // if(G_isAsmAndAbove){
            detailContent += "<table>";
            // detailContent += "<tr><td><span class='mkt-span' style='min-width: 60px;width: 60px;margin-right: 5px;'>基金:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.totalFundValue) +"元</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>培训:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.trainingCost) +"元</span></td>" +"</tr>";
            // detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>进货积分:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.cmdStockPoint) +"元</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>物料积分:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.cdmMaterialPoint) +"元</span></td></tr>";
            detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>培训:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.trainingCost) +"元</span></td>";
            detailContent += "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>物料积分:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.cdmMaterialPoint) +"元</span></td></tr>";
            detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>店招:</span><span>" + "<span>"+  grossAndMktValue.storeFrontCount +"家</span>/" +  myCdmFunc.toThousands(grossAndMktValue.newShopCost) +"元</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>研讨会:</span><span>" +  "<span>"+  grossAndMktValue.meetingCount +"场</span>/" +  myCdmFunc.toThousands(grossAndMktValue.seminarCost) +"元</span></td></tr>";
            detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>B2b Incentive:</span><span>"+ myCdmFunc.toThousands(grossAndMktValue.b2bPointValue*0.25) +"元</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>促销支持:</span><span>"+ myCdmFunc.toThousands(grossAndMktValue.promotionValue) + "元</span></td></tr>";
        // } else {
        //     detailContent += "<table>";
        //     // detailContent += "<tr><td><span class='mkt-span' style='min-width: 60px;width: 60px;margin-right: 5px;'>基金:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.totalFundValue) +"元</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>培训:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.trainingDays) +"天</span></td>" +"</tr>";
        //     // detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>进货积分:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.cmdStockPoint) +"元</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>物料积分:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.cdmMaterialPoint) +"分</span></td></tr>";
        //     detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>培训:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.trainingDays) +"天</span></td>";
        //     detailContent += "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>物料积分:</span><span>"+  myCdmFunc.toThousands(grossAndMktValue.cdmMaterialPoint) +"分</span></td></tr>";
        //     detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>店招:</span><span>"+  grossAndMktValue.storeFrontCount +"家</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>研讨会:</span><span>"+  grossAndMktValue.meetingCount +"场</span></td></tr>";
        //     detailContent += "<tr><td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px;'>B2b Incentive:</span><span>"+ myCdmFunc.toThousands(grossAndMktValue.b2bPointValue) +"分</span></td>"+ "<td><span class='mkt-span'  style='min-width: 60px;width: 60px;margin-right: 5px'>promotion:</span><span>"+ myCdmFunc.toThousands(grossAndMktValue.promotionValue) +"元</span></td></tr>";
        // }
        detailContent += "</table></div></div>";
        var opt= "<div style='display: inline-block' data-tooltip-content='#"+ organId +"' class='gross-mkt black_url'>" +  myCdmFunc.toThousands(mktCostValue) + "</div>&nbsp;" + detailContent;
        return opt;
    } else {
        return grossAndMktValue.mktCostValue;
    }
}
// #perf_grid
PerfGridLoadMask = {
    show: function(){
        if(PerfGridLoadMask._inited){
            PerfGridLoadMask._loadMask.show();
        }else{
            BUI.use('bui/mask', function(Mask) {
                PerfGridLoadMask._loadMask = new Mask.LoadMask( {
                    el : '#perf_grid',
                    msg : 'loading...'
                });
            });
            PerfGridLoadMask._loadMask.show();
            PerfGridLoadMask._inited = true;
        }
    },
    hide: function(){
        if(PerfGridLoadMask._loadMask){
            PerfGridLoadMask._loadMask.hide();
        }
    }
};
function refreshGrid() {
    if(gridStore == null){
        BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
            var beginCol = 7;
            var colGroup = new Grid.Plugins.ColumnGroup({
                groups: [{
                    title : '第一季度',
                    from : beginCol,
                    to : beginCol + 2
                },
                    {
                        title : '第二季度',
                        from : beginCol + 3,
                        to : beginCol + 5
                    },
                    {
                        title : '第三季度',
                        from :  beginCol + 6,
                        to : beginCol + 8
                    },
                    {
                        title : '第四季度',
                        from : beginCol + 9,
                        to :  beginCol + 11
                    }]
            });
            var promotionSummary=new Grid.Plugins.Summary({
                summaryTitle: '总合计', //合计标题
                pageSummaryTitle: '本页合计', //本页合计标题
            });
            var Grid = Grid,
                Store = Data.Store;
            var columns = [
                {title : '序号',sortable: false, width: '2%', renderer: function (value, item, index) { return index + 1; } },
                {title : '经销商名称',sortable: false, dataIndex: 'organizationName', width: '8%',elCls:'sp-name',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return value;
                        }
                        return genSalesPerfAnalysisHtml(value,item);
                }}
                ,{title : 'YTD 目标(KL)', sortable:false, summary: true, dataIndex: 'totalSellThroughTarget', elCls:'text-right', width: '6%',renderer: function (value,item,index) {
                        // 如果index是undefined的表示是合计
                        // if(index === undefined){
                        //     return undefined;
                        // }
//                        var toThousandsValue = myCdmFunc.toThousands(value/1000);
                		var toThousandsValue = parseFloat(value)/1000
                		toThousandsValue = toThousandsValue.toFixed(2);
                        if(!isSp(item.partnerPropertyText)){
                            return toThousandsValue;
                        }
                        return genSellinAnalysisPageHtml(toThousandsValue,item);
                 } }
                ,{ title : 'YTD 销量(KL)', sortable:false, summary: true, dataIndex: 'totalSellThrough',  elCls:'text-right', width: '6%',renderer: function (value,item,index) {
                        // 如果index是undefined的表示是合计
                        // if(index === undefined){
                        //     return undefined;
                        // }
	                	var toThousandsValue = parseFloat(value)/1000
	            		toThousandsValue = toThousandsValue.toFixed(2);
                        if(!isSp(item.partnerPropertyText)){
                            return toThousandsValue;
                        }
                        return genSellinAnalysisPageHtml(toThousandsValue,item);
                    } },
                {title : '毛利\n(CNY)', sortable:false,  dataIndex: 'grossAndMktValue', width: '6%',elCls:'text-right',renderer: function (value,item,index) {
                        var toThousandsValue = myCdmFunc.toThousands(value.grossActualValue);
                        return toThousandsValue;
                    } },
                {title : '基金/进货积分\n(CNY)', sortable:false,  dataIndex: 'grossAndMktValue', width: '6%',elCls:'text-right',renderer: function (value,item,index) {
                        return genShowFundAndPointValueHtml(value,item);
                    } },
                { title : '市场费用\n(CNY)', sortable:false,  dataIndex: 'grossAndMktValue', width: '6%',elCls:'text-right',renderer: function (value,item,index) {
                        return genShowGrossAndMktValueHtml(value,item);
                    } },
                { title : '扫码出库',dataIndex: 'scanResult1',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '录店', dataIndex: 'gdResult1',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '本地销售', dataIndex: 'violationRecord1',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '扫码出库', dataIndex: 'scanResult2',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '录店', dataIndex: 'gdResult2',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '本地销售', dataIndex: 'violationRecord2',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '扫码出库', dataIndex: 'scanResult3',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '录店', dataIndex: 'gdResult3',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '本地销售', dataIndex: 'violationRecord3',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '扫码出库', dataIndex: 'scanResult4',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '录店', dataIndex: 'gdResult4',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } },
                { title: '本地销售', dataIndex: 'violationRecord4',sortable:false, width: '5%',renderer: function (value,item,index) {
                        if(!isSp(item.partnerPropertyText)){
                            return null;
                        }
                        return genSalesThroughAnalysisPageHtml(value,item);
                    } }
            ];
            gridStore = new Store({
                url: '/myCdm/queryPerfGridData.do',
                pageSize: 10,
                params: genQueryListParam(),
                remoteSort: true,
                direction: "DESC",
                root: "data",
                totalProperty: "total",
                autoLoad: true,
                proxy: {
                    method: 'post'
                }
            }).on("beforeprocessload",function(e){
                PerfGridLoadMask.hide();
                if (e.data.isError) {
                    common.alertMes(e.data.errorMsg, 'error');
                }
            });
            grid = new Grid.Grid({
                render: '#perf_grid',
                columns: columns,
                store: gridStore,
                loadMask: false, // 加载数据时显示屏蔽层
                width: '100%',
                bbar: {
                    pagingBar: true
                },
                forceFit: false,
                plugins : [colGroup,promotionSummary]
            });
            grid.on('aftershow', function(){
                $('#perf_grid .gross-mkt').tooltipster();
            });
            grid.render();
            PerfGridLoadMask.show();
            common.initGrid(grid, null, true);
            // $("#perf_grid .bui-grid-column-group .bui-grid-hd").first().css("width", "50px");
        });
    } else {
        gridStore.load(genQueryListParam());
    }
}
function genQueryListParam() {
    var params = {};
    params.start = 0;
    params.direction = "DESC";
    params.field = "partnerPropertyLevel";
    params.year = currentYear;
    params.month = currentMonth;
    params.roleType = roleType;
    return params;
}
var myCdmFunc = (function($) {
    // 查询出来的summary结果的vue
    var todoDivVue = new Vue({
        el: '#todo-div',
        data: {
            todoInfo: {storeFrontTodoNum:0,seminarTodoNum:0,monthlyPlanTodoInfo:0,monthlyReportInfo:0,flsrExchangePendingReviewCount:0,asmExpensePendingReviewCount:0
                ,asmExchangePendingReviewCount:0,cmExchangePendingReviewCount:0,creditPendingCount:0}
        }
    });
    var util = {
        gridAutoResize: function(grid){
            $(window).on('resize',function(){
                util.autoFit(grid);
            });
        },
        autoFit: function(grid) {
            if(grid._resizeHandler != null){
                clearTimeout(grid._resizeHandler); //防止resize短时间内反复调用
            }
            grid._resizeHandler = setTimeout(function(){
                var w = grid.get('el').parent().width();
                if(w > 0){
                    grid.set('width', w);
                }
            }, 100);
        },
        openMenu: function(url, newUrl) {
            if(top.openMenuTab){
                top.openMenuTab(url, newUrl);
            }else{
                window.open(newUrl || url);
            }
        },
        openUrl: function(url, menuId, menuTitle) {
            if(top.openMenu){
                top.openMenu(url, menuId, menuTitle);
            }else{
                window.open(url);
            }
        },
        openPage: function(url, type) {
            window.location.href = url + "?type=" + type;
        },
        toThousandsInteger: function(n) {
            var value = n;
            if (n != undefined && n != null) {
                value = parseFloat(parseFloat(n).toFixed(0)).toLocaleString();
            }
            return value;
        },
        toThousands: function(n) {
            var value = n;
            if (n != undefined && n != null) {
                value = parseFloat(n).toLocaleString();
                var xsd = (n + "").split(".");
                if (xsd.length === 1) {
                    value = n.toLocaleString() + ".00";
                } else if (xsd.length > 1) {
                    if (xsd[1].length < 2) {
                        value = n.toLocaleString() + "0";
                    } else if(xsd[1].length === 2){
                        if (typeof n.toFixed === "function") {
                            var localValue = n.toFixed(2);
                            value = parseFloat(localValue).toLocaleString();
                        }
                    }
                }
            }
            return value;
        },
        divToThousands: function(num,fixedNum) {
            if(fixedNum == null || fixedNum === undefined){
                fixedNum = 2;
            }
            var result = null;
            if(num) {
                result = num.toFixed(fixedNum) / 1000;
                result = result.toFixed(fixedNum);
            }
            return result;
        },
        showSellinAndGrossPieOption: function (sellinTitle, sellinData, sellinInfoColor, divObj, unit) {
            var sellinAndGrossOption = {
                backgroundColor: '#ffffff',
                title: {
                    text: sellinTitle,
                    left: 'center',
                    top: 0,
                    textStyle: {
                        color: '#000',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: "{b}:{c}"
                },
                legend: {
                    orient: 'horizontal',
                    left: 'center',
                    bottom: 0,
                    data: ['目标', '实际销量']
                },
                series: [
                    {
                        name: sellinTitle,
                        type: 'gauge',
                        min: 0,
                        max: sellinData[0].value,
//                        max: 0,
                        splitNumber: 12,       // 分割段数，默认为5
                        axisLine: {            // 坐标轴线
                            lineStyle: {       // 属性lineStyle控制线条样式
                                color: [[1/6, '#C23531'],[1/2, '#EFC631'],[5/6, '#63869E'],[1, '#91C793']],
                                width: 8
                            }
                        },
                        axisTick: {            // 坐标轴小标记
                            //splitNumber: 0,   // 每份split细分多少段
                            length :11,        // 属性length控制线长
                            lineStyle: {       // 属性lineStyle控制线条样式
                                color: 'auto'
                            }
                        },
                        axisLabel: {           // 坐标轴文本标签，详见axis.axisLabel
                            show:true,
                            textStyle: {       // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                                color: 'auto',
                                fontSize:8
                            },
                            formatter:function(v){
                                return v.toFixed(0);
                            }
                        },
                        splitLine: {           // 分隔线
                            show: true,        // 默认显示，属性show控制显示与否
                            length :20,         // 属性length控制线长
                            lineStyle: {       // 属性lineStyle（详见lineStyle）控制线条样式
                                color: 'auto'
                            }
                        },
                        pointer : {
                            width : 5
                        },
                        center: ['50%', '50%'],
                        data: sellinData[1],
                        title:{
                          show: false
                        },
                        detail: {
                            fontSize:12,
                            fontWeight: 'bold',
                            color:"#333333",
                            // formatter:'\n\n\n\n\n{value} {name}%'
                            offsetCenter: [0, 80],
                            formatter:function(v){
                                return "已经完成{emphasize|" + v + unit + "},完成目标的{emphasize|" + sellinData[1].percent + "%}";
//                                return "已经完成{emphasize|" + v + unit + "}";
                            },
                            rich: {
                                emphasize: {
                                    color: 'red',
                                    fontWeight: "normal",
                                    fontSize: 11,
                                    padding:0
                                },
                                normal: {
                                    color: '#333333'
                                }
                            }
                        }
                    }
                ],
                itemStyle: {
                    emphasis: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                color: sellinInfoColor
            };

            var grossInfoDiv = echarts.init(divObj);
            $(window).resize(grossInfoDiv.resize);
            grossInfoDiv.setOption(sellinAndGrossOption);
            grossInfoDiv.on("click",function () {
                // var dataList = gridStore.getResult();
                // if(dataList && dataList.length >0){
                //     var topItem = gridStore.getResult()[0];
                //     openSellinAnalysisPage(topItem.id,topItem.organizationName);
                // } else {
            	openSellinAnalysisPage('', '');
//                    myCdmFunc.openUrl("newhomepage/jsp/asm/SpSellInInfoAnalysis.jsp","SpSellInInfoAnalysis","合伙人sell in分析");
                // }
            });
        },
        nullOrNA: function(info,property){
            if(info && info[property]){
                return info[property];
            } else {
                return "";
            }
        },
        showRankInfo:function(curMonthInfo,preMonthInfo,divObj){
            $('.cur-month-sales-rank .rank-value',divObj).text(util.nullOrNA(curMonthInfo,"rankSales"));
            $('.cur-month-gross-rank .rank-value',divObj).text(util.nullOrNA(curMonthInfo,"rankGross"));

            $('.pre-month-sales-rank .rank-value',divObj).text(util.nullOrNA(preMonthInfo,"rankSales"));
            $('.pre-month-gross-rank .rank-value',divObj).text(util.nullOrNA(preMonthInfo,"rankGross"));
        }
    };
    var req = {
        getSysMessage: function(callback) {
            var maxLength = 5;
            $.post(common.ctx + "messagepush/personaldata.do",{
                start:0,
                limit:maxLength,
                pageIndex:0,
                field:"createTime",
                direction:"DESC"
            }, function(data){
                $.isFunction(callback) && callback(data);
            });
        },
        getTotalReceivedSupport:function (callback) {
            $.post(common.ctx + "myCdm/queryTotalReceivedSupport.do?year=" + currentYear + "&t=" + new Date().getTime(),
                function(data){
                    if(data.code == 'success'){
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.errorMsg, 'error');
                        return;
                    }
            });
        },
        getSellinAndGrossInfo: function (currentYear,customerCategory,roleType,loginCai,distributorId ,callback) {
//            $.post(common.ctx + "myCdm/querySellinAndGrossInfo.do?year=" + currentYear + "&month=" + currentMonth + "&quarter=" + parseInt((currentMonth + 2) / 3) + "&t=" + new Date().getTime(),
//                function(data){
//                    if(data.code == 'success'){
//                        $.isFunction(callback) && callback(data.data);
//                    } else {
//                        common.alertMes(data.errorMsg, 'error');
//                        return;
//                    }
//                });
        	rpc.call("biProcedureBizService.querySellInByMonth", [ currentYear,customerCategory,roleType,loginCai,distributorId ], function(result) {
				
    			callback(result);
    				
    		});
        },
        getRanksInfo: function (currentYear,currentMonth,roleType,loginCai,callback) {
//            $.post(common.ctx + "myCdm/queryRanksInfo.do?year=" + currentYear + "&month=" + currentMonth + "&t=" + new Date().getTime(),
//                function(data){
//                    if(data.code == 'success'){
//                        $.isFunction(callback) && callback(data.data);
//                    } else {
//                        common.alertMes(data.errorMsg, 'error');
//                        return;
//                    }
//                });
        	rpc.call("biProcedureBizService.querySalesRank", [ currentYear,currentMonth,roleType,loginCai ], function(result) {
				
    			callback(result);
    				
    			});
        },
        getTodoList: function (callback) {
            $.post(common.ctx + "myCdm/queryTodoList.do" +"?t=" + new Date().getTime(),
                function(data){
                    if(data.code == 'success'){
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.errorMsg, 'error');
                        return;
                    }
                });
        },
        getWorkshopDetail: function(callback) {
            $.get(common.ctx + "newhomepage/workshop.do?t=" + new Date().getTime(), function(data){
                if(data.code == '0000'){
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        }
    };
    var loader = {
        loaderTotalSalesGrid: function(data) {
            BUI.use(['bui/grid','bui/data'], function(Grid, Data){

                if(totalSalesGrid) {
                    totalSalesGrid.showData(data.totalSalesList);
                    return;
                }

                var colGroup = new Grid.Plugins.ColumnGroup({
                    groups: [{
                        title : '总销量跟踪（升）：',
                        from : 0,
                        to : 0
                    },{
                        title : '截止1季度末',
                        from : 1,
                        to : 2
                    },{
                        title : '截止2季度末',
                        from : 3,
                        to : 4
                    },{
                        title : '截止3季度季度末',
                        from : 5,
                        to : 6
                    },{
                        title : '截止4季度季度末（全年）',
                        from : 7,
                        to : 8
                    }]
                });

                totalSalesGrid = new Grid.Grid({
                    columns:[{
                        sortable : false,
                        dataIndex : 'filedName',
                        width: '20%'
                    }, {
                        title : '目标',
                        sortable : false,
                        dataIndex : 'q1target',
                        width: '10%',
                        renderer : function (v, item) {
                            if(item.filedName == "完成季度任务的%") {
                                return "";
                            } else {
                                return v;
                            }
                        }
                    },{
                        title : '实际',
                        sortable : false,
                        dataIndex : 'q1actual',
                        width: '10%'

                    }, {
                        title : '目标',
                        sortable : false,
                        dataIndex : 'q2target',
                        width: '10%',
                        renderer : function (v, item) {
                            if(item.filedName == "完成季度任务的%") {
                                return "";
                            } else {
                                return v;
                            }
                        }
                    },{
                        title : '实际',
                        sortable : false,
                        dataIndex : 'q2actual',
                        width: '10%'
                    }, {
                        title : '目标',
                        sortable : false,
                        dataIndex : 'q3target',
                        width: '10%',
                        renderer : function (v, item) {
                            if(item.filedName == "完成季度任务的%") {
                                return "";
                            } else {
                                return v;
                            }
                        }
                    },{
                        title : '实际',
                        sortable : false,
                        dataIndex : 'q3actual',
                        width: '10%'
                    }, {
                        title : '目标',
                        sortable : false,
                        dataIndex : 'q4target',
                        width: '10%',
                        renderer : function (v, item) {
                            if(item.filedName == "完成季度任务的%") {
                                return "";
                            } else {
                                return v;
                            }
                        }
                    },{
                        title : '实际',
                        sortable : false,
                        dataIndex : 'q4actual',
                        width: '10%'
                    }],
                    render: '#totalSalesGrid',
                    forceFit : false,
                    plugins : [colGroup]
                });
                totalSalesGrid.showData(data.totalSalesList);
                totalSalesGrid.render();
                // $("#totalSalesGrid .bui-grid-column-group .bui-grid-hd").first().css("width", "20%");
                util.gridAutoResize(totalSalesGrid);
            });
        },
        loadSysMessage: function() {
            req.getSysMessage(function(result) {
                if(result.code == "success") {
                    var data = result.resultLst;
                    if(data.length==0){
                        $('.sys-message .more-btn').hide();
                        $("#sys_message").append('<h2 style="color: #bbb; text-align: center;">没有系统公告</h2>');
                    } else {
                        var len = data.length;
                        var h = [];
                        for(var i=0;i<len;i++){
                            h.push('<div class="content-panel">',
                                '<div class="message-title message-content"><span style="cursor: pointer;" onclick="top.common.viewMessage(' + data[i].messageId
                                + ',\'' + data[i].messageTitle + '\', false)">', i + 1, '. ',
                                common.formatDate(new Date(data[i].createTime), "yyyy-MM-dd"),
                                ' ', data[i].messageTitle,
                                '</span></div>',
                                '</div>');
                        }
                        $("#sys_message").append(h.join(''));
                    }
                }
            })
        },
        loadTotalReceivedSupport: function () {
            req.getTotalReceivedSupport(function (result) {
                $('#rebate-div .value-span').text(myCdmFunc.toThousandsInteger(result.rebateInfo));

                $($('#cdm-stock-point-div .value-span')[0]).text(myCdmFunc.toThousands(result.totalPointInfo.CDM_STOCK_POINT));
                $($('#cdm-material-point-div .value-span')[0]).text(myCdmFunc.toThousands(result.totalPointInfo.CDM_MATERIAL_POINT));
                $($('#cdm-promotion-div .value-span')[0]).text(myCdmFunc.toThousands(result.promotionValue));

                // if(G_isAsmAndAbove) {
                //     $($('#point-div .value-span')[1]).text(myCdmFunc.toThousands(result.totalPointInfo));
                // }

                $($('#new-workshop-div .value-span')[0]).text(result.storeFrontCount);
                // if(G_isAsmAndAbove) {
                    $($('#new-workshop-div .value-span')[1]).text(myCdmFunc.toThousands(result.storeFrontCost));
                // }

                var meetingCount = result.meetingCount?result.meetingCount:0;
                $($('#meeting-subsidy-div .value-span')[0]).text(meetingCount);
                // if(G_isAsmAndAbove) {
                    $($('#meeting-subsidy-div .value-span')[1]).text(myCdmFunc.toThousands(result.meetingSubsidyInfo));
                // }

                var trainingDays = result.trainingInfo?result.trainingInfo.days:0;
                $($('#training-subsidy-div .value-span')[0]).text(trainingDays);
                // if(G_isAsmAndAbove) {
                    var trainingPrice = result.trainingInfo?result.trainingInfo.price:0;
                    $($('#training-subsidy-div .value-span')[1]).text(myCdmFunc.toThousands(trainingPrice));
                // }
            });
        },
        loadSellinAndGrossInfo: function () {
            req.getSellinAndGrossInfo(currentYear,customerCategory,roleType,loginCai,distributorId,function (result) {
                var sellinInfoColor = ['rgb(0,102,178)','rgb(118,146,49)'];
                var sellinData = [ {value:myCdmFunc.divToThousands(result.sellChart.targetValue,0), name:'目标'},
                    {value:myCdmFunc.divToThousands(result.sellChart.actualValue,0), name:'实际销量',percent:''}];
                if(sellinData[1].value == null) {
                    sellinData[1].value = 0;
                }
                totalSellin = result.sellChart.actualValue;
                totalTargetSellin = result.sellChart.targetValue;
                if(sellinData[0].value != undefined && sellinData[0].value!=null && sellinData[0].value!=0){
                    var sellinPercent = (result.sellChart.actualValue*100 / result.sellChart.targetValue);
                    if(typeof sellinPercent.toFixed === "function" ){
                        sellinData[1].percent = sellinPercent.toFixed(2);
                    }
                }else{
                	sellinData[0].value = 0;
                	sellinData[1].percent = 0;
                }
                var nowQuarter = parseInt((currentMonth + 2) / 3);
                var sellinTitle = currentYear + "年Q" + nowQuarter + '销量(KL)';
                util.showSellinAndGrossPieOption(sellinTitle, sellinData, sellinInfoColor, $('#sell-in-info')[0],"KL");

                var grossInfoColor = ['rgb(0,102,178)','rgb(132,156,199)'];
                var grossData = [ {value: myCdmFunc.divToThousands(result.grossChart.targetValue,0), name:'目标'},
                    {value: myCdmFunc.divToThousands(result.grossChart.actualValue,0), name:'实际销量',percent:0}];
                if(grossData[1].value == null) {
                    grossData[1].value = 0;
                }
                if(grossData[0].value != undefined && grossData[0].value!=null && grossData[0].value!=null){
                    var grossPercent = (result.grossChart.actualValue*100 / result.grossChart.targetValue);
                    if(typeof grossPercent.toFixed === "function" ){
                        grossData[1].percent = grossPercent.toFixed(2);
                    }
                }else{
                	grossData[0].value = 0;
                }
                var grossTitle = currentYear + "年Q" + nowQuarter + '毛利(KUSD)';
                util.showSellinAndGrossPieOption(grossTitle, grossData, grossInfoColor, $('#gross-info')[0],"KUSD");
            });
        },
        loadRanksInfo: function () {
//        	var cm = currentYear + (currentMonth > 9 ? '' + currentMonth : '0' + currentMonth),
//	    		pm = currentMonth == 1 ? (currentYear - 1 + '12') : currentYear + (currentMonth > 10 ? '' + (currentMonth - 1) : '0' + (currentMonth - 1)),
//	    		viewName = 'FlsrSellinRank';
//	    		if((roleWeight & 4) > 0){
//	    			viewName = 'ChannelManagerSellinRank';
//	    		}else if((roleWeight & 2) > 0){
//	    			viewName = 'AsmSellinRank';
//	    		}
//	    	$.post(common.ctx + 'reportview/report/data.do', {packageName: 'homepage', viewName: viewName, 
//	    		currentMonth: cm, preMonth: pm, cai: loginCai}, function(result){
//	            if (result.code == 'success') {
//	                var currentData = null, preData = null;
//	                if(result.data){
//	                	for(var i = 0; i < result.data.length; i++){
//	                		if(result.data[i].month == cm){
//	                			currentData = result.data[i];
//	                		}else if(result.data[i].month == pm){
//	                			preData = result.data[i];
//	                		}
//	                	}
//	                }
//	                if(currentData){
//		                $('#cur_year .cur-month-sales-rank .rank-value').text(currentData['volume_rank']);
//		                $('#cur_year .cur-month-gross-rank .rank-value').text(currentData['margin_rank']);
//	                }else{
//		                $('#cur_year .cur-month-sales-rank .rank-value').text('-');
//		                $('#cur_year .cur-month-gross-rank .rank-value').text('-');
//	                }
//	                if(preData){
//		                $('#cur_year .pre-month-sales-rank .rank-value').text(preData['volume_rank']);
//		                $('#cur_year .pre-month-gross-rank .rank-value').text(preData['margin_rank']);
//	                }else{
//		                $('#cur_year .pre-month-sales-rank .rank-value').text('-');
//		                $('#cur_year .pre-month-gross-rank .rank-value').text('-');
//	                }
//	            } else {
//	    			common.alertMes("加载本区域内排名失败。" + result.errorMsg, "error");
//	            }
//	    	}, 'json');
        	currentMonth > 9 ? '' + currentMonth : '0' + currentMonth;
	        req.getRanksInfo(currentYear,currentMonth,roleType,loginCai,function (result) {
	        	
	        	console.log(result);
	        	if(result.length==2){
	                $('#cur_year .cur-month-sales-rank .rank-value').text(result[1].rank_volume);
	                $('#cur_year .cur-month-gross-rank .rank-value').text(result[1].rank_margin);
	                $('#cur_year .pre-month-sales-rank .rank-value').text(result[0].rank_volume);
	                $('#cur_year .pre-month-gross-rank .rank-value').text(result[0].rank_margin);
	        	}else if(result.length==1){
	        		$('#cur_year .cur-month-sales-rank .rank-value').text('-');
	                $('#cur_year .cur-month-gross-rank .rank-value').text('-');
	        		$('#cur_year .pre-month-sales-rank .rank-value').text(result[0].rank_volume);
	                $('#cur_year .pre-month-gross-rank .rank-value').text(result[0].rank_margin);
	        	}
                
//	        	if(currentData){
//	                $('#cur_year .cur-month-sales-rank .rank-value').text(currentData['volume_rank']);
//	                $('#cur_year .cur-month-gross-rank .rank-value').text(currentData['margin_rank']);
//                }else{
//	                $('#cur_year .cur-month-sales-rank .rank-value').text('-');
//	                $('#cur_year .cur-month-gross-rank .rank-value').text('-');
//                }
//                if(preData){
//	                $('#cur_year .pre-month-sales-rank .rank-value').text(preData['volume_rank']);
//	                $('#cur_year .pre-month-gross-rank .rank-value').text(preData['margin_rank']);
//                }else{
//	                $('#cur_year .pre-month-sales-rank .rank-value').text('-');
//	                $('#cur_year .pre-month-gross-rank .rank-value').text('-');
//                }
//	        	}
	        });
        	
        	
        },
        loadTodoInfo: function () {
            req.getTodoList(function (result) {
                //{"code":"success","data":{"flsrMonthlyPlanTodoDates":[],"storeFrontTodoNum":0,"flsrMonthlyReportTodoDates":[],"seminarTodoNum":0}}
                todoDivVue.todoInfo = result;
                todoDivVue.todoInfo.monthlyPlanTodoInfo = todoDivVue.todoInfo.flsrMonthlyPlanTodoDates.length;
                todoDivVue.todoInfo.monthlyReportInfo = todoDivVue.todoInfo.flsrMonthlyReportTodoDates.length;
                // if(todoDivVue.todoInfo.flsrMonthlyPlanTodoDates && todoDivVue.todoInfo.flsrMonthlyPlanTodoDates.length > 0){
                //     var monthlyPlanTodoDate = new Date(todoDivVue.todoInfo.flsrMonthlyPlanTodoDates[0]);
                //     todoDivVue.todoInfo.monthlyPlanTodoInfo = monthlyPlanTodoDate.getMonth() + "月";
                // }
                // if(todoDivVue.todoInfo.flsrMonthlyReportTodoDates && todoDivVue.todoInfo.flsrMonthlyReportTodoDates.length > 0){
                //     var monthlyReportDate = new Date(todoDivVue.todoInfo.flsrMonthlyReportTodoDates[0]);
                //     todoDivVue.todoInfo.monthlyReportInfo = monthlyReportDate.getMonth() + "月";
                // }
            });
        },
        initOrganizationSelect: function () {
            // 初始化，机构下拉框
            organizationCtrl = OrgAutoSelect.init('#organizationId');
        }
    };
    return {
        init: function() {
            loader.loadSysMessage();
            loader.loadTotalReceivedSupport();
            loader.loadSellinAndGrossInfo();
            loader.loadRanksInfo();
            loader.loadTodoInfo();
            refreshGrid();
        },
        openUrl: function(url, menuId, menuTitle) {
            util.openUrl(url, menuId, menuTitle);
        },
        openMenu: function(url, newUrl) {
            util.openMenu(url, newUrl);
        },
        openPage: function(url, type) {
            util.openPage(url, type);
        },
        showSellinAndGrossPieOption:function (sellinTitle, sellinData, sellinInfoColor) {
            util.showSellinAndGrossPieOption(sellinTitle, sellinData, sellinInfoColor)
        },
        divToThousands: function (num,fixedNum) {
            return util.divToThousands(num,fixedNum);
        },
        toThousandsInteger: function (num) {
            return util.toThousandsInteger(num);
        },
        toThousands: function(n) {
            return util.toThousands(n);
        },
        gridAutoResize: function(grid) {
            util.gridAutoResize(grid);
        }
};
}($));