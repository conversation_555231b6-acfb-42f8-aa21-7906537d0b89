{"version": 3, "sources": ["webpack:///./src/views/order/OrderSubmission.vue?f444", "webpack:///src/views/order/OrderSubmission.vue", "webpack:///./src/views/order/OrderSubmission.vue?24e0", "webpack:///./src/views/order/OrderSubmission.vue", "webpack:///./src/components/OrderGoods.vue?97d5", "webpack:///src/components/OrderGoods.vue", "webpack:///./src/components/OrderGoods.vue?8945", "webpack:///./src/components/OrderGoods.vue", "webpack:///./src/views/order/OrderSubmission.vue?3170", "webpack:///./src/assets/images/line.jpg"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "isVisual", "_e", "style", "store_self_mention", "shipping_type", "on", "<PERSON><PERSON><PERSON><PERSON>", "addressInfo", "detail", "real_name", "_v", "_s", "phone", "regionString", "system_store", "name", "_detailed_address", "_m", "attrs", "orderGroupInfo", "cartInfo", "directives", "rawName", "value", "expression", "domProps", "$event", "target", "composing", "contacts", "contactsTel", "staticStyle", "mobile", "remainedPoints", "undefined", "orderPrice", "total_price", "pay_postage", "coupon_price", "deduction_price", "pay_price", "createOrder", "staticRenderFns", "NAME", "components", "OrderGoods", "props", "data", "offlinePayStatus", "from", "_is<PERSON>ei<PERSON>", "deduction", "isWeixin", "pinkId", "active", "showCoupon", "showAddress", "couponId", "priceGroup", "usableCoupon", "addressLoaded", "useIntegral", "mark", "userInfo", "payConfig", "watch", "computedPrice", "$route", "n", "getCartInfo", "computed", "find", "$store", "getters", "defaultAddress", "mounted", "that", "query", "pinkid", "methods", "countMoneyWithChecked", "cartIds", "$dialog", "error", "$router", "go", "cartListWithChecked", "getPayMethod", "getAddressList", "getUserPoints", "push", "changeCoupon", "coupon", "payItem", "index", "changeAddress", "component", "length", "_l", "cart", "key", "uniqueId", "productInfo", "image_base", "store_name", "cart_num", "attrInfo", "suk", "true_price", "price", "evaluate", "path", "unique", "promotionItem", "id", "Number", "type", "Array", "default", "module", "exports"], "mappings": "gJAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAAGN,EAAIO,SAA4oCP,EAAIQ,KAAtoCJ,EAAG,MAAM,CAACE,YAAY,aAAaG,MAAOT,EAAIU,mBAAqB,GAAK,uBAAwB,CAAwB,IAAtBV,EAAIW,cAAqBP,EAAG,MAAM,CAACE,YAAY,uCAAuCM,GAAG,CAAC,MAAQZ,EAAIa,cAAc,CAAEb,EAAIc,YAAYC,QAAUf,EAAIc,YAAYE,UAAWZ,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAIc,YAAYE,WAAW,KAAKZ,EAAG,OAAO,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGlB,EAAIc,YAAYK,YAAYf,EAAG,MAAM,CAAEJ,EAAIc,YAAsB,WAAEV,EAAG,OAAO,CAACE,YAAY,0BAA0B,CAACN,EAAIiB,GAAG,UAAUjB,EAAIQ,KAAKR,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAIc,YAAYM,cAAc,IAAIpB,EAAIkB,GAAGlB,EAAIc,YAAYC,QAAQ,SAASX,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACN,EAAIiB,GAAG,cAAcb,EAAG,MAAM,CAACE,YAAY,4BAA4BF,EAAG,MAAM,CAACE,YAAY,wCAAwC,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAIqB,aAAaC,MAAM,KAAKlB,EAAG,OAAO,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGlB,EAAIqB,aAAaF,YAAYf,EAAG,MAAM,CAACJ,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAIqB,aAAaE,mBAAmB,WAAWvB,EAAIwB,GAAG,KAAcpB,EAAG,aAAa,CAACqB,MAAM,CAAC,SAAW,EAAE,SAAWzB,EAAI0B,eAAeC,YAAYvB,EAAG,MAAM,CAACE,YAAY,WAAW,CAAwB,IAAtBN,EAAIW,cAAqBP,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,UAAUb,EAAG,MAAM,CAACE,YAAY,YAAY,CAACN,EAAIiB,GAAG,aAAab,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,SAASb,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,QAAQ,CAACwB,WAAW,CAAC,CAACN,KAAK,QAAQO,QAAQ,UAAUC,MAAO9B,EAAY,SAAE+B,WAAW,aAAaN,MAAM,CAAC,KAAO,OAAO,YAAc,aAAaO,SAAS,CAAC,MAAShC,EAAY,UAAGY,GAAG,CAAC,MAAQ,SAASqB,GAAWA,EAAOC,OAAOC,YAAqBnC,EAAIoC,SAASH,EAAOC,OAAOJ,eAAc1B,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,UAAUb,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,QAAQ,CAACwB,WAAW,CAAC,CAACN,KAAK,QAAQO,QAAQ,UAAUC,MAAO9B,EAAe,YAAE+B,WAAW,gBAAgBN,MAAM,CAAC,KAAO,OAAO,YAAc,aAAaO,SAAS,CAAC,MAAShC,EAAe,aAAGY,GAAG,CAAC,MAAQ,SAASqB,GAAWA,EAAOC,OAAOC,YAAqBnC,EAAIqC,YAAYJ,EAAOC,OAAOJ,iBAAiB9B,EAAY,SAAEI,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,UAAUb,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,QAAQ,CAACwB,WAAW,CAAC,CAACN,KAAK,QAAQO,QAAQ,UAAUC,MAAO9B,EAAU,OAAE+B,WAAW,WAAWO,YAAY,CAAC,aAAa,SAASb,MAAM,CAAC,KAAO,OAAO,YAAc,YAAYO,SAAS,CAAC,MAAShC,EAAU,QAAGY,GAAG,CAAC,MAAQ,SAASqB,GAAWA,EAAOC,OAAOC,YAAqBnC,EAAIuC,OAAON,EAAOC,OAAOJ,eAAc9B,EAAIQ,OAAOJ,EAAG,MAAM,CAACE,YAAY,WAAW,CAAwB,IAAtBN,EAAIW,cAAqBP,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,UAAUb,EAAG,MAAM,CAACE,YAAY,YAAY,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAIwC,gBAAgB,SAASxC,EAAIQ,OAAOJ,EAAG,MAAM,CAACE,YAAY,aAAa,MAAiCmC,IAA/BzC,EAAI0C,WAAWC,YAA2BvC,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,YAAYb,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGlB,EAAI0C,WAAWC,kBAAkB3C,EAAIQ,KAAMR,EAAI0C,WAAWE,YAAc,EAAGxC,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,SAASb,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAI0C,WAAWE,kBAAkB5C,EAAIQ,KAAMR,EAAI0C,WAAWG,aAAe,EAAGzC,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,YAAYb,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAG,KAAKjB,EAAIkB,GAAGlB,EAAI0C,WAAWG,mBAAmB7C,EAAIQ,KAAMR,EAAI0C,WAAWI,gBAAkB,EAAG1C,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,WAAWb,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAG,KAAKjB,EAAIkB,GAAGlB,EAAI0C,WAAWI,sBAAsB9C,EAAIQ,OAAOJ,EAAG,MAAM,CAACkC,YAAY,CAAC,OAAS,YAAYlC,EAAG,MAAM,CAACE,YAAY,uCAAuC,CAACF,EAAG,MAAM,CAACJ,EAAIiB,GAAG,SAASb,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACF,EAAG,OAAO,CAACkC,YAAY,CAAC,YAAY,SAAS,CAACtC,EAAIiB,GAAG,QAAQjB,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAI0C,WAAWK,gBAAgB3C,EAAG,MAAM,CAACE,YAAY,aAAaM,GAAG,CAAC,MAAQZ,EAAIgD,cAAc,CAAChD,EAAIiB,GAAG,aAAa,IAC30IgC,EAAkB,CAAC,WAAa,IAAIjD,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACqB,MAAM,CAAC,IAAM,EAAQ,e,oMCyTxK,oBACA,mBACA,GACEH,KAAM4B,EACNC,WAAY,CACVC,WAAJ,QAIEC,MAAO,GACPC,KAAM,WACJ,MAAO,CACLC,iBAAkB,EAClBC,KAAMC,EAAY,SAAW,WAC7BC,WAAW,EACXC,SAAUF,EACVG,OAAQ,EAERC,OAAQ,SACRC,YAAY,EACZC,aAAa,EAEbC,SAAU,EACVtC,eAAgB,CACduC,WAAY,IAEdC,aAAc,GACdC,eAAe,EACfC,aAAa,EACb1B,WAAY,CACVK,UAAW,OAEbsB,KAAM,GACN9B,OAAQ,GACRlB,aAAc,GACdV,cAAe,EACfyB,SAAU,GACVC,YAAa,GACb3B,mBAAoB,EACpB4D,SAAU,GACVC,UAAW,KAGfC,MAAO,CACLJ,YADJ,WAEMnE,KAAKwE,iBAEPC,OAJJ,SAIA,GACUC,EAAErD,OAAS4B,GAEbjD,KAAK2E,eAGTjE,cAVJ,WAWMV,KAAKwE,kBAGTI,SAAU,CACRtE,SADJ,WAEM,OAAQN,KAAKyB,eAAeC,UAAY,IAAImD,MAAK,SAAvD,kCAEIhE,YAJJ,WAKM,OAAOb,KAAK8E,OAAOC,QAAQC,gBAE7BzC,eAPJ,WAQM,OAAOvC,KAAK8E,OAAOC,QAAQxC,iBAG/B0C,QAAS,WACP,IAAJ,OAEIC,EAAKP,mBAC4BnC,IAA7B0C,EAAKT,OAAOU,MAAMC,SAC1B,iCAEEC,QAAS,CAaP,aAbJ,kJAcA,kBAdA,sCAcA,EAdA,KAcA,EAdA,KAeA,IACA,eACA,oCAjBA,gDAoBIb,cApBJ,WAuBMxE,KAAKyC,WAAa,CAChBC,YAAa1C,KAAK8E,OAAOC,QAAQO,sBACjCxC,UAAW9C,KAAK8E,OAAOC,QAAQO,wBAmBnCX,YA5CJ,WA6CM,IAAN,wBACM,IAAKY,EAEH,OADAvF,KAAKwF,QAAQC,MAAM,QACZzF,KAAK0F,QAAQC,IAAI,GAE1B3F,KAAKyB,eAAiB,CACpBC,SAAU1B,KAAK8E,OAAOC,QAAQa,qBAGhC5F,KAAK6F,eACL7F,KAAK8F,iBACL9F,KAAKwE,gBACLxE,KAAK+F,iBAgBP,cAzEJ,sIA0EA,uCA1EA,gDA4EI,eA5EJ,sIA6EA,wCA7EA,gDA+EInF,YA/EJ,WAiFMZ,KAAK0F,QAAQM,KAAK,sBAgBpBC,aAAc,SAAlB,GAIQjG,KAAKiE,aAHFiC,GACiB,CAA5B,4BAIMlG,KAAKwE,iBAEP2B,QAAS,SAAb,GACMnG,KAAK4D,OAASwC,GAEhBC,cA5GJ,SA4GA,GACMrG,KAAKa,YAAcA,GAErB,YA/GJ,wGAgHA,OACA,gFACA,EAlHA,mBAmHA,4CAnHA,yCAoHA,uCApHA,OAsHA,kCACA,sBACA,iBACA,gBACA,OACA,SACA,oBACA,mBACA,CACA,SACA,aAhIA,0BAqIA,4CArIA,0CAsIA,qCAtIA,WAuIA,2BAvIA,0CAwIA,sCAxIA,WAyIA,uBAzIA,0CA0IA,wCA1IA,YA2IA,mBA3IA,oBA6IA,4CACA,mBA9IA,0CAgJA,0CAhJA,WAiJA,iDAjJA,0CAkJA,uCAlJA,WAoJA,gDApJA,0CAqJA,uCArJA,QAwJA,iBAxJA,iDA2JI,aA3JJ,8HA4JA,qBACA,+BACA,8CACA,wCACA,2CAhKA,yCAiKA,kCAjKA,cAoKA,mCACA,GACA,wBACA,uBACA,mBACA,8BACA,iCACA,iCAEA,mBACA,0CACA,iDACA,yCACA,eACA,mBACA,6BAnLA,kCAwLA,uCAxLA,yCAwLA,EAxLA,KAwLA,EAxLA,KAyLA,EAzLA,wBA0LA,6BACA,oCA3LA,mBA4LA,GA5LA,QA8LA,IA9LA,6BA8LA,wFACA,kBA/LA,6OAiMA,6BACA,wGAlMA,6EAuQI,WAvQJ,SAuQA,GAvQA,mGAwQA,6BACA,qCAzQA,kCA0QA,gBACA,KAEA,YA7QA,yCA0QA,EA1QA,KA+QA,SACA,SACA,8BACA,EAlRA,0CAmRA,uDAnRA,QAqRA,2CACA,wDAtRA,mDCrYwW,I,wBCQpWyF,EAAY,eACd,EACAxG,EACAkD,GACA,EACA,KACA,WACA,MAIa,aAAAsD,E,6CCnBf,IAAIxG,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAI2B,SAAS6E,QAAQ,SAASpG,EAAG,MAAM,CAACE,YAAY,eAAeN,EAAIyG,GAAIzG,EAAY,UAAE,SAAS0G,GAAM,OAAOtG,EAAG,MAAM,CAACuG,IAAID,EAAKE,UAAU,CAACxG,EAAG,MAAM,CAACE,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQmB,MAAM,CAAC,IAAMiF,EAAKG,YAAYC,gBAAgB1G,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGwF,EAAKG,YAAYE,eAAe3G,EAAG,MAAM,CAACE,YAAY,OAAO,CAACN,EAAIiB,GAAG,KAAKjB,EAAIkB,GAAGwF,EAAKM,eAAgBN,EAAKG,YAAoB,SAAEzG,EAAG,MAAM,CAACE,YAAY,cAAc,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGwF,EAAKG,YAAYI,SAASC,KAAK,OAAOlH,EAAIQ,KAAKJ,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,OAAO,CAACkC,YAAY,CAAC,YAAY,SAAS,CAACtC,EAAIiB,GAAG,SAASjB,EAAIiB,GAAGjB,EAAIkB,GAAGwF,EAAKG,YAAYM,YAAY,KAAMT,EAAKG,YAAYM,aAAeT,EAAKG,YAAYO,MAAOhH,EAAG,OAAO,CAACkC,YAAY,CAAC,YAAY,OAAO,kBAAkB,eAAe,MAAQ,SAAS,CAACtC,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGwF,EAAKG,YAAYO,UAAUpH,EAAIQ,OAAyB,IAAjBR,EAAIqH,SAAgBjH,EAAG,MAAM,CAACE,YAAY,WAAWM,GAAG,CAAC,MAAQ,SAASqB,GAAQ,OAAOjC,EAAI2F,QAAQM,KAAK,CAAEqB,KAAM,mBAAqBZ,EAAKa,YAAa,CAACvH,EAAIiB,GAAG,UAAUjB,EAAIQ,SAAUkG,EAAkB,cAAE1G,EAAIyG,GAAIC,EAAkB,eAAE,SAASc,GAAe,OAAOpH,EAAG,MAAM,CAACuG,IAAIa,EAAcC,GAAGnH,YAAY,qCAAqC,CAACF,EAAG,MAAM,CAACE,YAAY,UAAUgC,YAAY,CAAC,SAAW,aAAa,CAAClC,EAAG,MAAM,CAACE,YAAY,QAAQmB,MAAM,CAAC,IAAM+F,EAAcV,cAAc1G,EAAG,MAAM,CAACkC,YAAY,CAAC,SAAW,WAAW,OAAS,IAAI,KAAO,IAAI,MAAQ,IAAI,WAAa,iBAAiB,aAAa,SAAS,MAAQ,SAAS,CAACtC,EAAIiB,GAAG,UAAUb,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGsG,EAAcT,eAAe3G,EAAG,MAAM,CAACE,YAAY,OAAO,CAACN,EAAIiB,GAAG,KAAKjB,EAAIkB,GAAGsG,EAAcR,eAAgBQ,EAAsB,SAAEpH,EAAG,MAAM,CAACE,YAAY,cAAc,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGsG,EAAcP,SAASC,KAAK,OAAOlH,EAAIQ,YAAWR,EAAIQ,MAAM,MAAK,MAC1tEyC,EAAkB,GC4DtB,G,UAAA,CACE3B,KAAM,aACN+B,MAAO,CACLgE,SAAUK,OACV/F,SAAU,CACRgG,KAAMC,MACNC,QAAS,WAAf,YAGEvE,KAAM,WACJ,MAAO,IAET4B,QAAS,aACTI,QAAS,KC1EyU,I,YCOhViB,EAAY,eACd,EACAxG,EACAkD,GACA,EACA,KACA,KACA,MAIa,OAAAsD,E,6CClBf,yBAAggB,EAAG,G,qBCAngBuB,EAAOC,QAAU,IAA0B", "file": "h5/js/chunk-861da7a2.f666687d.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"order-submission\"},[(!_vm.isVisual)?_c('div',{staticClass:\"allAddress\",style:(_vm.store_self_mention ? '' : 'padding-top: 0.2rem')},[(_vm.shipping_type === 0)?_c('div',{staticClass:\"address acea-row row-between-wrapper\",on:{\"click\":_vm.editAddress}},[(_vm.addressInfo.detail || _vm.addressInfo.real_name)?_c('div',{staticClass:\"addressCon\"},[_c('div',{staticClass:\"name\"},[_vm._v(\" \"+_vm._s(_vm.addressInfo.real_name)+\" \"),_c('span',{staticClass:\"phone\"},[_vm._v(_vm._s(_vm.addressInfo.phone))])]),_c('div',[(_vm.addressInfo.is_default)?_c('span',{staticClass:\"default font-color-red\"},[_vm._v(\"[默认]\")]):_vm._e(),_vm._v(\" \"+_vm._s(_vm.addressInfo.regionString)+\" \"+_vm._s(_vm.addressInfo.detail)+\" \")])]):_c('div',{staticClass:\"addressCon\"},[_c('div',{staticClass:\"setaddress\"},[_vm._v(\"设置收货地址\")])]),_c('div',{staticClass:\"iconfont icon-jiantou\"})]):_c('div',{staticClass:\"address acea-row row-between-wrapper\"},[_c('div',{staticClass:\"addressCon\"},[_c('div',{staticClass:\"name\"},[_vm._v(\" \"+_vm._s(_vm.system_store.name)+\" \"),_c('span',{staticClass:\"phone\"},[_vm._v(_vm._s(_vm.system_store.phone))])]),_c('div',[_vm._v(\" \"+_vm._s(_vm.system_store._detailed_address)+\" \")])])]),_vm._m(0)]):_vm._e(),_c('OrderGoods',{attrs:{\"evaluate\":0,\"cartInfo\":_vm.orderGroupInfo.cartInfo}}),_c('div',{staticClass:\"wrapper\"},[(_vm.shipping_type === 0)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"快递费用\")]),_c('div',{staticClass:\"discount\"},[_vm._v(\" 免运费 \")])]):_c('div',[_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"联系人\")]),_c('div',{staticClass:\"discount\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.contacts),expression:\"contacts\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请填写您的联系姓名\"},domProps:{\"value\":(_vm.contacts)},on:{\"input\":function($event){if($event.target.composing){ return; }_vm.contacts=$event.target.value}}})])]),_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"联系电话\")]),_c('div',{staticClass:\"discount\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.contactsTel),expression:\"contactsTel\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请填写您的联系电话\"},domProps:{\"value\":(_vm.contactsTel)},on:{\"input\":function($event){if($event.target.composing){ return; }_vm.contactsTel=$event.target.value}}})])])]),(_vm.isVisual)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"手机号码\")]),_c('div',{staticClass:\"discount\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.mobile),expression:\"mobile\"}],staticStyle:{\"text-align\":\"right\"},attrs:{\"type\":\"text\",\"placeholder\":\"请填写充值手机号\"},domProps:{\"value\":(_vm.mobile)},on:{\"input\":function($event){if($event.target.composing){ return; }_vm.mobile=$event.target.value}}})])]):_vm._e()]),_c('div',{staticClass:\"wrapper\"},[(_vm.shipping_type === 0)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"可用积分\")]),_c('div',{staticClass:\"discount\"},[_vm._v(\" \"+_vm._s(_vm.remainedPoints)+\" \")])]):_vm._e()]),_c('div',{staticClass:\"moneyList\"},[(_vm.orderPrice.total_price !== undefined)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"商品总积分：\")]),_c('div',{staticClass:\"money\"},[_vm._v(_vm._s(_vm.orderPrice.total_price))])]):_vm._e(),(_vm.orderPrice.pay_postage > 0)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"运费：\")]),_c('div',{staticClass:\"money\"},[_vm._v(\"￥\"+_vm._s(_vm.orderPrice.pay_postage))])]):_vm._e(),(_vm.orderPrice.coupon_price > 0)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"优惠券抵扣：\")]),_c('div',{staticClass:\"money\"},[_vm._v(\"-￥\"+_vm._s(_vm.orderPrice.coupon_price))])]):_vm._e(),(_vm.orderPrice.deduction_price > 0)?_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',[_vm._v(\"积分抵扣：\")]),_c('div',{staticClass:\"money\"},[_vm._v(\"-￥\"+_vm._s(_vm.orderPrice.deduction_price))])]):_vm._e()]),_c('div',{staticStyle:{\"height\":\"1.2rem\"}}),_c('div',{staticClass:\"footer acea-row row-between-wrapper\"},[_c('div',[_vm._v(\" 合计: \"),_c('span',{staticClass:\"font-color-red\"},[_c('span',{staticStyle:{\"font-size\":\"10px\"}},[_vm._v(\"积分\")]),_vm._v(\" \"+_vm._s(_vm.orderPrice.pay_price))])]),_c('div',{staticClass:\"settlement\",on:{\"click\":_vm.createOrder}},[_vm._v(\"立即结算\")])])],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"line\"},[_c('img',{attrs:{\"src\":require(\"@assets/images/line.jpg\")}})])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"order-submission\">\n    <div\n      v-if=\"!isVisual\"\n      class=\"allAddress\"\n      :style=\"store_self_mention ? '' : 'padding-top: 0.2rem'\">\n      <!-- <div class=\"nav acea-row\">\n        <div\n          class=\"item font-color-red\"\n          :class=\"shipping_type === 0 ? 'on' : 'on2'\"\n          @click=\"addressType(0)\"\n          v-if=\"store_self_mention\"\n        ></div>\n        <div\n          class=\"item font-color-red\"\n          :class=\"shipping_type === 1 ? 'on' : 'on2'\"\n          @click=\"addressType(1)\"\n          v-if=\"store_self_mention\"\n        ></div>\n      </div> -->\n      <div\n        class=\"address acea-row row-between-wrapper\"\n        v-if=\"shipping_type === 0\"\n        @click=\"editAddress\">\n        <!-- @click=\"addressTap\"> -->\n        <div class=\"addressCon\" v-if=\"addressInfo.detail || addressInfo.real_name\">\n          <div class=\"name\">\n            {{ addressInfo.real_name }}\n            <span class=\"phone\">{{ addressInfo.phone }}</span>\n          </div>\n          <div>\n            <span class=\"default font-color-red\" v-if=\"addressInfo.is_default\"\n              >[默认]</span\n            >\n            {{ addressInfo.regionString }}\n            {{ addressInfo.detail }}\n          </div>\n        </div>\n        <div class=\"addressCon\" v-else>\n          <div class=\"setaddress\">设置收货地址</div>\n        </div>\n        <div class=\"iconfont icon-jiantou\"></div>\n      </div>\n      <div class=\"address acea-row row-between-wrapper\" v-else>\n        <div class=\"addressCon\">\n          <div class=\"name\">\n            {{ system_store.name }}\n            <span class=\"phone\">{{ system_store.phone }}</span>\n          </div>\n          <div>\n            {{ system_store._detailed_address }}\n          </div>\n        </div>\n      </div>\n      <div class=\"line\">\n        <img src=\"@assets/images/line.jpg\" />\n      </div>\n    </div>\n    <OrderGoods :evaluate=\"0\" :cartInfo=\"orderGroupInfo.cartInfo\"></OrderGoods>\n    <div class=\"wrapper\">\n      <!-- <div\n        class=\"item acea-row row-between-wrapper\"\n        @click=\"couponTap\"\n        v-if=\"deduction === false\">\n        <div>优惠券</div>\n        <div class=\"discount\">\n          {{ usableCoupon.coupon_title || \"请选择\" }}\n          <span class=\"iconfont icon-jiantou\"></span>\n        </div>\n      </div> -->\n      <!-- <div class=\"item acea-row row-between-wrapper\" v-if=\"deduction === false\">\n        <div>积分抵扣</div>\n        <div class=\"discount\">\n          <div class=\"select-btn\">\n            <div class=\"checkbox-wrapper\">\n              <label class=\"well-check\">\n                <input type=\"checkbox\" v-model=\"useIntegral\" />\n                <i class=\"icon\"></i>\n                <span class=\"integral\">\n                  当前积分\n                  <span class=\"num font-color-red\">\n                    {{ userInfo.integral || 0 }}\n                  </span>\n                </span>\n              </label>\n            </div>\n          </div>\n        </div>\n      </div> -->\n      <!-- <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"\n          orderGroupInfo.priceGroup.vipPrice > 0 &&\n            userInfo.vip &&\n            pinkId == 0 &&\n            orderGroupInfo.bargain_id == 0 &&\n            orderGroupInfo.combination_id == 0 &&\n            orderGroupInfo.seckill_id == 0\">\n        会员优惠\n        <div class=\"discount\">￥{{ orderGroupInfo.priceGroup.vipPrice }}</div>\n      </div> -->\n      <div class=\"item acea-row row-between-wrapper\" v-if=\"shipping_type === 0\">\n        <div>快递费用</div>\n        <div class=\"discount\">\n          <!-- {{\n            orderGroupInfo.priceGroup.storePostage > 0\n              ? orderGroupInfo.priceGroup.storePostage\n              : \"免运费\"\n          }} -->\n          免运费\n        </div>\n      </div>\n      <div v-else>\n        <div class=\"item acea-row row-between-wrapper\">\n          <div>联系人</div>\n          <div class=\"discount\">\n            <input\n              type=\"text\"\n              placeholder=\"请填写您的联系姓名\"\n              v-model=\"contacts\"\n            />\n          </div>\n        </div>\n        <div class=\"item acea-row row-between-wrapper\">\n          <div>联系电话</div>\n          <div class=\"discount\">\n            <input\n              type=\"text\"\n              placeholder=\"请填写您的联系电话\"\n              v-model=\"contactsTel\"\n            />\n          </div>\n        </div>\n      </div>\n      <div class=\"item acea-row row-between-wrapper\" v-if=\"isVisual\">\n        <div>手机号码</div>\n        <div class=\"discount\">\n          <input\n              type=\"text\"\n              style=\"text-align: right;\"\n              placeholder=\"请填写充值手机号\"\n              v-model=\"mobile\"\n          />\n        </div>\n      </div>\n      <!-- <div class=\"item\">\n        <div>备注信息</div>\n        <textarea\n          placeholder=\"请添加备注（150字以内）\"\n          v-model=\"mark\"\n        ></textarea>\n      </div> -->\n    </div>\n    <div class=\"wrapper\">\n      <div class=\"item acea-row row-between-wrapper\" v-if=\"shipping_type === 0\">\n        <div>可用积分</div>\n        <div class=\"discount\"> {{remainedPoints}}\n        </div>\n      </div>\n    </div>\n    <div class=\"moneyList\">\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderPrice.total_price !== undefined\"\n      >\n        <div>商品总积分：</div>\n        <div class=\"money\">{{ orderPrice.total_price }}</div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderPrice.pay_postage > 0\"\n      >\n        <div>运费：</div>\n        <div class=\"money\">￥{{ orderPrice.pay_postage }}</div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderPrice.coupon_price > 0\"\n      >\n        <div>优惠券抵扣：</div>\n        <div class=\"money\">-￥{{ orderPrice.coupon_price }}</div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderPrice.deduction_price > 0\"\n      >\n        <div>积分抵扣：</div>\n        <div class=\"money\">-￥{{ orderPrice.deduction_price }}</div>\n      </div>\n    </div>\n    <div style=\"height:1.2rem\"></div>\n    <div class=\"footer acea-row row-between-wrapper\">\n      <div>\n        合计:\n        <span class=\"font-color-red\"><span style=\"font-size:10px\">积分</span> {{ orderPrice.pay_price }}</span>\n      </div>\n      <div class=\"settlement\" @click=\"createOrder\">立即结算</div>\n    </div>\n    <!-- <CouponListWindow\n      v-on:couponchange=\"changecoupon($event)\"\n      v-model=\"showCoupon\"\n      :price=\"orderPrice.total_price\"\n      :checked=\"usableCoupon.id\"\n      @checked=\"changeCoupon\"\n    ></CouponListWindow> -->\n    <!-- <AddressWindow\n      @checked=\"changeAddress\"\n      @redirect=\"addressRedirect\"\n      v-model=\"showAddress\"\n      :checked=\"addressInfo.id\"\n      ref=\"mychild\"\n    ></AddressWindow> -->\n  </div>\n</template>\n<style scoped>\n.order-submission .wrapper .shipping select {\n  color: #999;\n  padding-right: 0.15rem;\n}\n.order-submission .wrapper .shipping .iconfont {\n  font-size: 0.3rem;\n  color: #515151;\n}\n.order-submission .allAddress {\n  width: 100%;\n  background-image: linear-gradient(to bottom, #e93323 0%, #f5f5f5 100%);\n  background-image: -webkit-linear-gradient(\n    to bottom,\n    #e93323 0%,\n    #f5f5f5 100%\n  );\n  background-image: -moz-linear-gradient(to bottom, #e93323 0%, #f5f5f5 100%);\n  padding-top: 1rem;\n}\n.order-submission .allAddress .nav {\n  width: 7.1rem;\n  margin: 0 auto;\n}\n.order-submission .allAddress .nav .item {\n  width: 3.55rem;\n}\n.order-submission .allAddress .nav .item.on {\n  position: relative;\n  width: 2.5rem;\n}\n.order-submission .allAddress .nav .item.on:before {\n  position: absolute;\n  bottom: 0;\n  content: \"快递配送\";\n  font-size: 0.28rem;\n  display: block;\n  height: 0;\n  width: 3.55rem;\n  border-width: 0 0.2rem 0.8rem 0;\n  border-style: none solid solid;\n  border-color: transparent transparent #fff;\n  z-index: 9;\n  border-radius: 0.07rem 0.3rem 0 0;\n  text-align: center;\n  line-height: 0.8rem;\n}\n.order-submission .allAddress .nav .item:nth-of-type(2).on:before {\n  content: \"到店自提\";\n  border-width: 0 0 0.8rem 0.2rem;\n  border-radius: 0.3rem 0.07rem 0 0;\n}\n.order-submission .allAddress .nav .item.on2 {\n  position: relative;\n}\n.order-submission .allAddress .nav .item.on2:before {\n  position: absolute;\n  bottom: 0;\n  content: \"到店自提\";\n  font-size: 0.28rem;\n  display: block;\n  height: 0;\n  width: 4.6rem;\n  border-width: 0 0 0.6rem 0.6rem;\n  border-style: none solid solid;\n  border-color: transparent transparent #f7c1bd;\n  border-radius: 0.4rem 0.06rem 0 0;\n  text-align: center;\n  line-height: 0.6rem;\n}\n.order-submission .allAddress .nav .item:nth-of-type(1).on2:before {\n  content: \"快递配送\";\n  border-width: 0 0.6rem 0.6rem 0;\n  border-radius: 0.06rem 0.4rem 0 0;\n}\n.order-submission .allAddress .address {\n  width: 7.1rem;\n  height: 1.5rem;\n  margin: 0 auto;\n}\n.order-submission .allAddress .line {\n  width: 7.1rem;\n  margin: 0 auto;\n}\n.order-submission .wrapper .item .discount input::placeholder {\n  color: #ccc;\n}\n</style>\n<script>\nimport OrderGoods from \"@components/OrderGoods\";\n// import CouponListWindow from \"@components/CouponListWindow\";\n// import AddressWindow from \"@components/AddressWindow\";\n// import { postOrderConfirm, postOrderComputed, createOrder } from \"@api/order\";\n// import { getUser } from \"@api/user\";\n// import { pay } from \"@libs/wechat\";\nimport { isWeixin } from \"@utils\";\nimport { getPayCode, updateOrderStatus } from \"@api/order\";\nimport { postCartDel } from \"@api/store\";\nimport points from '@utils/points'\n\nconst NAME = \"OrderSubmission\",\n  _isWeixin = isWeixin();\nexport default {\n  name: NAME,\n  components: {\n    OrderGoods,\n    // CouponListWindow,\n    // AddressWindow\n  },\n  props: {},\n  data: function() {\n    return {\n      offlinePayStatus: 2,\n      from: _isWeixin ? \"weixin\" : \"weixinh5\",\n      deduction: true,\n      isWeixin: _isWeixin,\n      pinkId: 0,\n      // active: _isWeixin ? \"weixin\" : \"yue\",\n      active: \"wechat\",\n      showCoupon: false,\n      showAddress: false,\n      // addressInfo: {},\n      couponId: 0,\n      orderGroupInfo: {\n        priceGroup: {}\n      },\n      usableCoupon: {},\n      addressLoaded: false,\n      useIntegral: false,\n      orderPrice: {\n        pay_price: \"计算中\"\n      },\n      mark: \"\",\n      mobile: \"\",\n      system_store: {},\n      shipping_type: 0,\n      contacts: \"\",\n      contactsTel: \"\",\n      store_self_mention: 0,\n      userInfo: {},\n      payConfig: {},\n    };\n  },\n  watch: {\n    useIntegral() {\n      this.computedPrice();\n    },\n    $route(n) {\n      if (n.name === NAME) {\n        // this.getUserInfo();\n        this.getCartInfo();\n      }\n    },\n    shipping_type() {\n      this.computedPrice();\n    }\n  },\n  computed: {\n    isVisual() {\n      return (this.orderGroupInfo.cartInfo || []).find(x => x.materialType == 'XN')\n    },\n    addressInfo () {\n      return this.$store.getters.defaultAddress\n    },\n    remainedPoints () {\n      return this.$store.getters.remainedPoints\n    }\n  },\n  mounted: function() {\n    let that = this;\n    // that.getUserInfo();\n    that.getCartInfo();\n    if (that.$route.query.pinkid !== undefined)\n      that.pinkId = that.$route.query.pinkid;\n  },\n  methods: {\n    // getUserInfo() {\n    //   getUser()\n    //     .then(res => {\n    //       this.userInfo = res.data;\n    //     })\n    //     .catch(() => {});\n    // },\n    // addressType: function(index) {\n    //   if (index && !this.system_store.id)\n    //     return this.$dialog.error(\"暂无门店信息，您无法选择到店自提！\");\n    //   this.shipping_type = index;\n    // },\n    async getPayMethod () {\n      const [status, res] = await getPayCode()\n      if (status) {\n        console.log(res)\n        this.payConfig = res.result.resultList\n      }\n    },\n    computedPrice() {\n\n      // this.orderGroupInfo\n      this.orderPrice = {\n        total_price: this.$store.getters.countMoneyWithChecked,\n        pay_price: this.$store.getters.countMoneyWithChecked\n      }\n      // let shipping_type = this.shipping_type;\n      // postOrderComputed(this.orderGroupInfo.orderKey, {\n      //   addressId: this.addressInfo.id,\n      //   useIntegral: this.useIntegral ? 1 : 0,\n      //   couponId: this.usableCoupon.id || 0,\n      //   shipping_type: parseInt(shipping_type) + 1\n      // }).then(res => {\n      //   const data = res.data;\n      //   if (data.status === \"EXTEND_ORDER\") {\n      //     this.$router.replace({\n      //       path: \"/order/detail/\" + data.result.orderId\n      //     });\n      //   } else {\n      //     this.orderPrice = data.result;\n      //   }\n      // });\n    },\n    getCartInfo () {\n      const cartIds = this.$route.params.id;\n      if (!cartIds) {\n        this.$dialog.error(\"参数有误\");\n        return this.$router.go(-1);\n      }\n      this.orderGroupInfo = {\n        cartInfo: this.$store.getters.cartListWithChecked\n      }\n\n      this.getPayMethod()\n      this.getAddressList()\n      this.computedPrice()\n      this.getUserPoints()\n      // postOrderConfirm(cartIds)\n      //   .then(res => {\n      //     this.offlinePayStatus = res.data.offline_pay_status;\n      //     this.orderGroupInfo = res.data;\n      //     this.deduction = res.data.deduction;\n      //     this.usableCoupon = res.data.usableCoupon || {};\n      //     this.addressInfo = res.data.addressInfo || {};\n      //     this.system_store = res.data.system_store || {};\n      //     this.store_self_mention = res.data.store_self_mention;\n      //     this.computedPrice();\n      //   })\n      //   .catch(() => {\n      //     this.$dialog.error(\"加载订单数据失败\");\n      //   });\n    },\n    async getUserPoints () {\n      await this.$store.dispatch('getUserPoints')\n    },\n    async getAddressList () {\n      await this.$store.dispatch('getAddressList')\n    },\n    editAddress () {\n      //this.$router.push('/user/add_address/'+this.addressInfo.id)\n      this.$router.push('/user/add_manage/')\n    },\n    // addressTap: function() {\n    //   this.showAddress = true;\n    //   if (!this.addressLoaded) {\n    //     this.addressLoaded = true;\n    //     this.$refs.mychild.getAddressList();\n    //   }\n    // },\n    // addressRedirect() {\n    //   this.addressLoaded = false;\n    //   this.showAddress = false;\n    // },\n    // couponTap: function() {\n    //   this.showCoupon = true;\n    // },\n    changeCoupon: function(coupon) {\n      if (!coupon) {\n        this.usableCoupon = { coupon_title: \"不使用优惠券\", id: 0 };\n      } else {\n        this.usableCoupon = coupon;\n      }\n      this.computedPrice();\n    },\n    payItem: function(index) {\n      this.active = index;\n    },\n    changeAddress(addressInfo) {\n      this.addressInfo = addressInfo;\n    },\n    async createOrder() {\n      let that = this;\n      const isVisual = this.orderGroupInfo.cartInfo.find(x => x.materialType == 'XN')\n      if (isVisual) {\n        if (!/^1(3|4|5|7|8|9|6)\\d{9}$/.test(this.mobile)) {\n          return this.$dialog.toast({ mes: \"请填写正确的手机号\" });\n        }\n        console.log(this.$dialog.confirm)\n        this.$dialog.confirm({\n          title: '请确认您的手机号',\n          mes: this.mobile,\n          opts: [{\n            txt: '确定',\n            callback: () => {\n              that._createOrder()\n            }}, {\n              color: false,\n              txt: '取消',\n            }]\n        });\n      } else {\n        // if (!this.active) return this.$dialog.toast({ mes: \"请选择支付方式\" });\n        if (!this.addressInfo.detail && !this.shipping_type)\n          return this.$dialog.toast({ mes: \"请选择收货地址\" });\n        if (!this.addressInfo.real_name)\n          return this.$dialog.toast({ mes: \"请选择收货人姓名\" });\n        if (!this.addressInfo.phone)\n          return this.$dialog.toast({ mes: \"请选择收货人联系方式\" });\n        if (this.shipping_type) {\n          if (\n            (this.contacts === \"\" || this.contactsTel === \"\") &&\n            this.shipping_type\n          )\n            return this.$dialog.toast({ mes: \"请填写联系人或联系人电话\" });\n          if (!/^1(3|4|5|7|8|9|6)\\d{9}$/.test(this.contactsTel)) {\n            return this.$dialog.toast({ mes: \"请填写正确的手机号\" });\n          }\n          if (!/^[\\u4e00-\\u9fa5\\w]{2,16}$/.test(this.contacts)) {\n            return this.$dialog.toast({ mes: \"请填写您的真实姓名\" });\n          }\n        }\n        that._createOrder()\n      }\n    },\n    async _createOrder () {\n      let shipping_type = this.shipping_type;\n      const totalPoints = this.orderGroupInfo.cartInfo\n        .map(x => x.price * x.cart_num)\n        .reduce((prev, cur) => prev + cur, 0)\n      if (totalPoints > (points.number(this.remainedPoints) || 0)) {\n          return this.$dialog.toast({ mes: \"积分不足\" });\n      }\n\n      this.$dialog.loading.open(\"生成订单中\");\n      let params = {\n        real_name: this.contacts,\n        phone: this.contactsTel,\n        mobile: this.mobile,\n        addressId: this.addressInfo.id,\n        useIntegral: this.useIntegral ? 1 : 0,\n        couponId: this.usableCoupon.id || 0,\n        // payType: payType,\n        pinkId: this.pinkId,\n        seckill_id: this.orderGroupInfo.seckill_id,\n        combinationId: this.orderGroupInfo.combination_id,\n        bargainId: this.orderGroupInfo.bargain_id,\n        from: this.from,\n        mark: this.mark || \"\",\n        shipping_type: parseInt(shipping_type) + 1\n      }\n      // if (this.active === 'wechat') {\n      //   params.appId = this.$store.getters.wechatConfig.wechat\n      // }\n      const [status, res] = await this.$store.dispatch('createOrder', params)\n      if (!status) {\n        this.$dialog.loading.close();\n        this.$dialog.error(res.msg || \"创建订单失败\");\n        return false\n      }\n      for (let product of this.orderGroupInfo.cartInfo) {\n        postCartDel(product)\n      }\n      this.$dialog.loading.close();\n      this.$router.replace(`/order/status/${res.result.applicationId || res.result.data.applicationId}/1`)\n\n      // if (this.active === 'wechatOnline') {\n      //   if (this.$store.getters.userInfo.platform === 'public') {\n      //     let _this = this\n      //     this.$dialog.loading.close();\n      //     this.$dialog.loading.open(\"微信支付中\");\n      //     WeixinJSBridge.invoke(\n      //       'getBrandWCPayRequest', {\n      //         appId: res.result.appId,\n      //         timeStamp: res.result.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符\n      //         nonceStr: res.result.nonceStr, // 支付签名随机串，不长于 32 位\n      //         package: res.result.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\\*\\*\\*）\n      //         signType: res.result.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'\n      //         paySign: res.result.sign, // 支付签名\n      //       }, response => {\n      //         if(res.err_msg == \"get_brand_wcpay_request:ok\" ){\n      //           _this.confirmPay(res.result.orderId)\n      //         // 使用以上方式判断前端返回,微信团队郑重提示：\n      //               //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。\n      //         } else {\n      //           this.$dialog.loading.close();\n      //           _this.$dialog.error(response.msg || \"创建支付失败，请尝试其他支付方式\");\n      //           _this.$router.replace({path: `/order/detail/${res.result.orderId}`});\n      //         }\n      //      });\n      //     return\n      //   } else if (this.$store.getters.userInfo.platform === 'miniprograms') {\n      //     wx.miniProgram.navigateTo({\n      //       url: `/pages/order-pay/order-pay?orderId=${res.result.orderInfos.orderId}&totalFee=${res.result.orderInfos.totalPayMoney}&payId=${res.result.mchId}&paySecret=${res.result.apiKey}`\n      //     })\n      //   }\n      // }\n      // this.$dialog.loading.close();\n      // this.$router.replace({path: `/order/paycode/${res.result.orderId}?paymentMethod=${this.active}`});\n      // const data = res.data;\n      // let url = \"/order/detail/\" + data.result.orderId;\n      // switch (data.status) {\n      //   case \"ORDER_EXIST\":\n      //   case \"EXTEND_ORDER\":\n      //   case \"PAY_DEFICIENCY\":\n      //   case \"PAY_ERROR\":\n      //     this.$dialog.toast({ mes: res.msg });\n      //     this.$router.replace({\n      //       path: url + \"/0?msg=\" + res.msg\n      //     });\n      //     break;\n      //   case \"SUCCESS\":\n      //     this.$dialog.success(res.msg);\n      //     this.$router.replace({\n      //       path: url + \"/1\"\n      //     });\n      //     break;\n      //   case \"WECHAT_H5_PAY\":\n      //     this.$router.replace({\n      //       path: url + \"/2\"\n      //     });\n      //     setTimeout(() => {\n      //       location.href = data.result.jsConfig.mweb_url;\n      //     }, 100);\n      //     break;\n      //   case \"WECHAT_PAY\":\n      //     pay(data.result.jsConfig).finally(() => {\n      //       this.$router.replace({\n      //         path: url + \"/4\"\n      //       });\n      //     });\n      // }\n    },\n    async confirmPay (id) {\n      this.$dialog.loading.close();\n      this.$dialog.loading.open(\"支付结果确认中\");\n      const [status] = await updateOrderStatus({\n        id: id,\n        // files: res.attachmentFileList.map(item => item.attId).join(';'),\n        status: 1\n      })\n      alert(status)\n      alert(status)\n      this.$dialog.loading.close()\n      if (status) {\n        return this.$router.replace(`/order/status/${id}/1`)\n      } else {\n        this.$dialog.toast({ mes: \"请求未能成功处理，请稍后再试\" });\n        this.$router.replace({path: `/order/detail/${id}`});\n      }\n    },\n  }\n};\n</script>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OrderSubmission.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OrderSubmission.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OrderSubmission.vue?vue&type=template&id=29482ef0&scoped=true&\"\nimport script from \"./OrderSubmission.vue?vue&type=script&lang=js&\"\nexport * from \"./OrderSubmission.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OrderSubmission.vue?vue&type=style&index=0&id=29482ef0&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"29482ef0\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"orderGoods\"},[_c('div',{staticClass:\"total\"},[_vm._v(\"共\"+_vm._s(_vm.cartInfo.length)+\"件商品\")]),_c('div',{staticClass:\"goodWrapper\"},_vm._l((_vm.cartInfo),function(cart){return _c('div',{key:cart.uniqueId},[_c('div',{staticClass:\"item acea-row row-between-wrapper\"},[_c('div',{staticClass:\"pictrue\"},[_c('img',{staticClass:\"image\",attrs:{\"src\":cart.productInfo.image_base}})]),_c('div',{staticClass:\"text\"},[_c('div',{staticClass:\"acea-row row-between-wrapper\"},[_c('div',{staticClass:\"name line1\"},[_vm._v(_vm._s(cart.productInfo.store_name))]),_c('div',{staticClass:\"num\"},[_vm._v(\"x \"+_vm._s(cart.cart_num))])]),(cart.productInfo.attrInfo)?_c('div',{staticClass:\"attr line1\"},[_vm._v(\" \"+_vm._s(cart.productInfo.attrInfo.suk)+\" \")]):_vm._e(),_c('div',{staticClass:\"money font-color-red\"},[_c('span',{staticStyle:{\"font-size\":\"10px\"}},[_vm._v(\"积分 \")]),_vm._v(_vm._s(cart.productInfo.true_price)+\" \"),(cart.productInfo.true_price !== cart.productInfo.price)?_c('span',{staticStyle:{\"font-size\":\"12px\",\"text-decoration\":\"line-through\",\"color\":\"#999\"}},[_vm._v(\"￥\"+_vm._s(cart.productInfo.price))]):_vm._e()]),(_vm.evaluate === 3)?_c('div',{staticClass:\"evaluate\",on:{\"click\":function($event){return _vm.$router.push({ path: '/goods_evaluate/' + cart.unique })}}},[_vm._v(\" 评价 \")]):_vm._e()])]),(cart.promotionInfo)?_vm._l((cart.promotionInfo),function(promotionItem){return _c('div',{key:promotionItem.id,staticClass:\"item acea-row row-between-wrapper\"},[_c('div',{staticClass:\"pictrue\",staticStyle:{\"position\":\"relative\"}},[_c('img',{staticClass:\"image\",attrs:{\"src\":promotionItem.image_base}}),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"left\":\"0\",\"right\":\"0\",\"background\":\"rgba(0,0,0,.5)\",\"text-align\":\"center\",\"color\":\"#fff\"}},[_vm._v(\"赠品\")])]),_c('div',{staticClass:\"text\"},[_c('div',{staticClass:\"acea-row row-between-wrapper\"},[_c('div',{staticClass:\"name line1\"},[_vm._v(_vm._s(promotionItem.store_name))]),_c('div',{staticClass:\"num\"},[_vm._v(\"x \"+_vm._s(promotionItem.cart_num))])]),(promotionItem.attrInfo)?_c('div',{staticClass:\"attr line1\"},[_vm._v(\" \"+_vm._s(promotionItem.attrInfo.suk)+\" \")]):_vm._e()])])}):_vm._e()],2)}),0)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"orderGoods\">\n    <div class=\"total\">共{{ cartInfo.length }}件商品</div>\n    <div class=\"goodWrapper\">\n      <div\n        v-for=\"cart in cartInfo\"\n        :key=\"cart.uniqueId\">\n        <div class=\"item acea-row row-between-wrapper\">\n          <div class=\"pictrue\">\n            <img :src=\"cart.productInfo.image_base\" class=\"image\" />\n          </div>\n          <div class=\"text\">\n            <div class=\"acea-row row-between-wrapper\">\n              <div class=\"name line1\">{{ cart.productInfo.store_name }}</div>\n              <div class=\"num\">x {{ cart.cart_num }}</div>\n            </div>\n            <div class=\"attr line1\" v-if=\"cart.productInfo.attrInfo\">\n              {{ cart.productInfo.attrInfo.suk }}\n            </div>\n            <div class=\"money font-color-red\">\n              <span style=\"font-size:10px\">积分 </span>{{ cart.productInfo.true_price }}\n              <span\n                v-if=\"cart.productInfo.true_price !== cart.productInfo.price\"\n                style=\"font-size: 12px;text-decoration: line-through;color: #999;\">￥{{ cart.productInfo.price }}</span>\n            </div>\n\n            <div\n              class=\"evaluate\"\n              v-if=\"evaluate === 3\"\n              @click=\"$router.push({ path: '/goods_evaluate/' + cart.unique })\">\n              评价\n            </div>\n          </div>\n        </div>\n        <!-- 赠品信息 -->\n        <template v-if=\"cart.promotionInfo\">\n          <div\n            v-for=\"promotionItem in cart.promotionInfo\"\n            :key=\"promotionItem.id\"\n            class=\"item acea-row row-between-wrapper\">\n            <div class=\"pictrue\" style=\"position: relative;\">\n              <img :src=\"promotionItem.image_base\" class=\"image\" />\n              <div style=\"position: absolute;bottom: 0;left: 0;right: 0;background: rgba(0,0,0,.5);text-align: center;color: #fff;\">赠品</div>\n            </div>\n            <div class=\"text\">\n              <div class=\"acea-row row-between-wrapper\">\n                <div class=\"name line1\">{{ promotionItem.store_name }}</div>\n                <div class=\"num\">x {{ promotionItem.cart_num }}</div>\n              </div>\n              <div class=\"attr line1\" v-if=\"promotionItem.attrInfo\">\n                {{ promotionItem.attrInfo.suk }}\n              </div>\n              <!-- <div class=\"money font-color-red\">￥{{ promotionItem.true_price }}</div> -->\n            </div>\n          </div>\n        </template>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nexport default {\n  name: \"OrderGoods\",\n  props: {\n    evaluate: Number,\n    cartInfo: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data: function() {\n    return {};\n  },\n  mounted: function() {},\n  methods: {}\n};\n</script>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OrderGoods.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OrderGoods.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OrderGoods.vue?vue&type=template&id=379ff0b9&\"\nimport script from \"./OrderGoods.vue?vue&type=script&lang=js&\"\nexport * from \"./OrderGoods.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OrderSubmission.vue?vue&type=style&index=0&id=29482ef0&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OrderSubmission.vue?vue&type=style&index=0&id=29482ef0&scoped=true&lang=css&\"", "module.exports = __webpack_public_path__ + \"h5/img/line.05bf1c84.jpg\";"], "sourceRoot": ""}