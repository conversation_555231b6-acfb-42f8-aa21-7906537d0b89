package com.chevron.promote.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.material.model.WXTMaterialVo;
import com.chevron.promote.business.ApproveHistoryService;
import com.chevron.promote.business.PromoteBizService;
import com.chevron.promote.dao.PromoteActivityFeedbackMapper;
import com.chevron.promote.dao.PromoteActivityGifMapper;
import com.chevron.promote.dao.PromoteActivityMapper;
import com.chevron.promote.dao.PromoteActivitySpecialGiftMapper;
import com.chevron.promote.dao.PromoteAdvertisementDetailMapper;
import com.chevron.promote.dao.PromoteAgricultureDetailMapper;
import com.chevron.promote.dao.PromoteApplicationBatchMapper;
import com.chevron.promote.dao.PromoteDistributionMapper;
import com.chevron.promote.dao.PromoteGDOpenDetailMapper;
import com.chevron.promote.dao.PromoteOrderDetailMapper;
import com.chevron.promote.dao.PromoteOrderLogisticsMapper;
import com.chevron.promote.dao.PromoteOrderMapper;
import com.chevron.promote.dao.PromotePacksConfigMapper;
import com.chevron.promote.dao.PromotePacksMaterialsMapper;
import com.chevron.promote.dao.PromotePlanPacksDetailMapper;
import com.chevron.promote.dao.PromotePromotionalDetailMapper;
import com.chevron.promote.dao.PromoteRegionalDetailMapper;
import com.chevron.promote.dao.PromoteRoadshowActivityDetailMapper;
import com.chevron.promote.dao.PromoteSeminarActivityDetailMapper;
import com.chevron.promote.dao.PromoteShopSignsDetailMapper;
import com.chevron.promote.dao.PromoteTryOilDetailMapper;
import com.chevron.promote.dao.PromoteXDOpenDetailMapper;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.CountUsedPacksInfo;
import com.chevron.promote.model.CustomerRegionUser;
import com.chevron.promote.model.PromoteActivity;
import com.chevron.promote.model.PromoteActivityFeedback;
import com.chevron.promote.model.PromoteActivityGif;
import com.chevron.promote.model.PromoteActivityName;
import com.chevron.promote.model.PromoteActivityPreData;
import com.chevron.promote.model.PromoteActivitySpecialGift;
import com.chevron.promote.model.PromoteActivityType;
import com.chevron.promote.model.PromoteApplicationBatch;
import com.chevron.promote.model.PromoteDistribution;
import com.chevron.promote.model.PromoteOrder;
import com.chevron.promote.model.PromoteOrderDetail;
import com.chevron.promote.model.PromoteOrderLogistics;
import com.chevron.promote.model.PromotePacksAmountInfo;
import com.chevron.promote.model.PromotePacksConfig;
import com.chevron.promote.model.PromotePacksMaterials;
import com.chevron.promote.model.PromotePlanPacksDetail;
import com.chevron.promote.model.PromotePromotionalDetail;
import com.chevron.promote.model.PromoteRegionalPointsLog;
import com.chevron.promote.model.PromoteRoleEnum;
import com.chevron.promote.model.PromoteStatusEnum;
import com.chevron.promote.model.ResponseActivityFeedbackDetail;
import com.chevron.promote.model.ResponseActivityInfoMobile;
import com.chevron.promote.model.ResponseActivityPacksDetail;
import com.chevron.promote.model.ResponseFeedBackFile;
import com.chevron.promote.model.detail.PromoteAdvertisementDetail;
import com.chevron.promote.model.detail.PromoteAgricultureDetail;
import com.chevron.promote.model.detail.PromoteGDOpenDetail;
import com.chevron.promote.model.detail.PromoteRegionalDetail;
import com.chevron.promote.model.detail.PromoteRoadshowActivityDetail;
import com.chevron.promote.model.detail.PromoteSeminarActivityDetail;
import com.chevron.promote.model.detail.PromoteShopSignsDetail;
import com.chevron.promote.model.detail.PromoteTryOilDetail;
import com.chevron.promote.model.detail.PromoteXDOpenDetail;
import com.chevron.promote.model.response.ResponsePromoteAdvertisementDetail;
import com.chevron.promote.model.response.ResponsePromoteAgricultureDetail;
import com.chevron.promote.model.response.ResponsePromoteRegionalDetail;
import com.chevron.promote.model.response.ResponsePromoteRoadShowDetail;
import com.chevron.promote.model.response.ResponsePromoteSeminarDetail;
import com.chevron.promote.model.response.ResponsePromoteShopSignsDetail;
import com.chevron.promote.model.response.ResponsePromoteTryOilDetail;
import com.chevron.promote.model.response.ResponsePromoteXDOpenDetail;
import com.chevron.promote.service.IPromoteApplicationService;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.EmptyChecker;
import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.model.WxAttFile;

@Service
public class PromoteApplicationServiceImpl extends BasePromote implements IPromoteApplicationService{
	private static Logger log = LoggerFactory.getLogger(PromoteDistributionServiceImpl.class);
	@Resource 
	PromoteActivityMapper promoteActiviMapper;
	@Resource
	PromoteBizService promoteBizService;
	@Resource
	PromoteXDOpenDetailMapper xdOpenDetailMapper;
	@Resource
	PromoteGDOpenDetailMapper gdOpenDetailMapper;
	@Resource
	PromotePromotionalDetailMapper promotionalDetailMapper;
	@Resource
	PromoteRoadshowActivityDetailMapper roadshowActivityDetailMapper;
	@Resource
	PromoteSeminarActivityDetailMapper promoteSeminarActivityDetailMapper;
	@Resource
	PromoteRegionalDetailMapper promoteRegionalDetailMapper;
	@Resource
	PromotePacksConfigMapper promotePacksConfigMapper;
	@Resource
	PromotePlanPacksDetailMapper planPacksDetailMapper;
	@Resource
	PromoteApplicationBatchMapper promoteApplicationBatchMapper;
	@Resource
	WxAttFileMapper wxAttFileMapper;
	@Resource
	PromoteActivityFeedbackMapper promoteActivityFeedbackMapper;
	@Resource
	PromoteOrderMapper promoteOrderMapper;
	@Resource
	PromoteDistributionMapper promoteDistributionMapper;
	@Resource
	PromotePacksMaterialsMapper promotePacksMaterialsMapper;
	@Resource
	PromoteActivityGifMapper promoteGifMapper;
	@Resource
	PromoteOrderDetailMapper promoteOrderDetailMapper;
	@Resource
	ApproveHistoryService approveHisService;
	@Autowired
	DicService dicService;
	@Resource
	PromoteOrderLogisticsMapper orderLogisticsMapper;
	@Resource
	PromoteActivitySpecialGiftMapper specialGiftMapper;
	
	@Autowired
	private PromoteShopSignsDetailMapper promoteShopSignsDetailMapper;
	
	@Autowired
	private PromoteAdvertisementDetailMapper promoteAdvertisementDetailMapper;
	
	@Autowired
	private PromoteAgricultureDetailMapper promoteAgricultureDetailMapper;
	
	@Autowired
	private PromoteTryOilDetailMapper promoteTryOilDetailMapper;
	
	@Override
	public Map<String, Object> getActivityPreData() {
		log.info("PromoteApplicationServiceImpl getActivityPreData");
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			//获取当前用户信息	
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("userId", customerRegionUser.getUserId());
			reqMap.put("sourcePlanId", customerRegionUser.getSourcePlanId());
			//获取当前用户总可申请的礼包数量
			PromoteDistribution promoteDistribution = doGetTotalPacks(customerRegionUser);
			PromoteActivityPreData promoteActivityPreData = new PromoteActivityPreData();
			//0.获取活动类型
			doGetActivityTypes(promoteActivityPreData);
			
			//1.返回攻店小礼品
			doGetPromoteGdGif(promoteActivityPreData,customerRegionUser.getSourcePlanId());
			if(null==promoteDistribution)
			{
				resultMap.put("promoteActivityPreData", promoteActivityPreData);
				return resultMap;
			}
			
			//2.获取促销品积分数量（可兑换礼物，如：卡车司机）
			//TODO  是否现在状态???状态
			//doGetEnablePromotionPoints(promoteActivityPreData,customerRegionUser,promoteDistribution);
			//根据礼包类型，获取单个礼包的积分数量总计
			doGetPromoteionPoints(promoteActivityPreData,customerRegionUser,promoteDistribution);
			
			
			reqMap.putAll(PromoteBizService.getStatus());
			//3.获取可用入店礼包、攻店礼包的可用数量
			//TODO  是否现在状态
			doGetEnableXdOpenPacks(promoteActivityPreData,reqMap,promoteDistribution);
			
			//4.获取可用路演活动礼包、消费者互动补充礼包
			//TODO  是否现在状态
			doGetEnableRoadShowPacks(promoteActivityPreData,reqMap,promoteDistribution);
			
			//5.获取可用研讨会礼包
			//TODO  是否现在状态
			doGetEnableSeminarPacks(promoteActivityPreData,reqMap,promoteDistribution);
			
			//6.获取可用地促积分数量
			//TODO  是否现在状态
			//doGetEnabelPointsNumber(promoteActivityPreData,reqMap,promoteDistribution);
			
			//获取可用店招礼包
			doGetEnableShopSignsPacks(promoteActivityPreData,reqMap,promoteDistribution);
			//获取可用户外广告礼包
			doGetEnableAdvertisementPacks(promoteActivityPreData, reqMap, promoteDistribution);
			//获取可用农垦礼包
			doGetEnableAgriculturePacks(promoteActivityPreData, reqMap, promoteDistribution);
			//获取可用试用油品礼包
			doGetEnableTryOilPacks(promoteActivityPreData, reqMap, promoteDistribution);
			
			log.info("PromoteApplicationServiceImpl getActivityPreData promoteActivityPreData:{}",promoteActivityPreData.toString());
			resultMap.put("promoteActivityPreData", promoteActivityPreData);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getActivityTypes"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	private PromoteActivityPreData doGetPromoteionPoints(
			PromoteActivityPreData promoteActivityPreData,
			CustomerRegionUser customerRegionUser,
			PromoteDistribution promoteDistribution)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		Long sourcePlanId = customerRegionUser.getSourcePlanId();
		reqMap.put("sourcePlanId", sourcePlanId);
		//返回预置数据 可兑换礼品的积分数量
		reqMap.put("packsFlag", PROMOTE_FLAG);
		List<PromotePlanPacksDetail> lst1 = planPacksDetailMapper.getPoints(reqMap);
		Map<String,Object> pointsMap = new HashMap<String,Object>();
		for(PromotePlanPacksDetail promotePlanPacksDetail:lst1)
		{
			pointsMap.put(promotePlanPacksDetail.getPacksCode(), promotePlanPacksDetail.getPointsAmount());
		}
		
		//1.返回入店礼包用于兑换物料的总积分数量
		Integer rdPromotePointsCount = pointsMap.get(PROMOTE_ACTIVITY_RDLB)==null?0:(Integer)pointsMap.get(PROMOTE_ACTIVITY_RDLB);
		promoteActivityPreData.setRdPromotePointsCount(rdPromotePointsCount);
		//2.返回消费者互动礼包用于兑换物料的总积分数量
		Integer consumerInteractionPromotePointsCount = pointsMap.get(PROMOTE_ACTIVITY_XFZHDBCB)==null?0:(Integer)pointsMap.get(PROMOTE_ACTIVITY_XFZHDBCB);
		promoteActivityPreData.setConsumerInteractionPromotePointsCount(consumerInteractionPromotePointsCount);
		return promoteActivityPreData;
	}



	private PromoteActivityPreData doGetEnabelPointsNumber(
			PromoteActivityPreData promoteActivityPreData,
			Map<String, Object> reqMap, PromoteDistribution promoteDistribution)throws Exception {
		//总的积分数
		int totalPoints = promoteDistribution.getPointsDistributionCount();
		//获取已经申请的积分数量
		PromoteRegionalDetail promoteRegionalDetail = promoteRegionalDetailMapper.getHasApplyPoints(reqMap);
		if(null!=promoteRegionalDetail)
		{
			promoteActivityPreData.setEnablePoins(totalPoints-
					(promoteRegionalDetail.getApplyPromotionSupportPoints()==null?0:promoteRegionalDetail.getApplyPromotionSupportPoints()));
		}else
		{
			promoteActivityPreData.setEnablePoins(totalPoints);
		}
		return promoteActivityPreData;
	}



	private PromoteActivityPreData doGetEnableSeminarPacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String, Object> reqMap, PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
	    int totalSeminarPacks = promoteDistribution.getSeminarPacksDistributionCount();
	    int toatlPoints = promoteDistribution.getPointsDistributionCount();
	    Double toatlVenueMeal = promoteDistribution.getVenueMealDistribution();
	    int totalHighPacks = promoteDistribution.getSeminarHighPacksDistributionCount();
	    //获取已经申请的礼包数
	    PromoteSeminarActivityDetail promoteSeminarActivityDetail = promoteSeminarActivityDetailMapper.getHasApplyPacks(reqMap);
	    if(null!=promoteSeminarActivityDetail)
	    {
	    	promoteActivityPreData.setEnableTghwlbPacks(totalSeminarPacks-
	    			(promoteSeminarActivityDetail.getApplyPacksCount()==null?0:promoteSeminarActivityDetail.getApplyPacksCount()));
	    	promoteActivityPreData.setEnablePoins(toatlPoints-
	    			(promoteSeminarActivityDetail.getApplyPoints()==null?0:promoteSeminarActivityDetail.getApplyPoints()));
	    	promoteActivityPreData.setEnableVenueMeal(toatlVenueMeal-
	    			(promoteSeminarActivityDetail.getVenueMeal()==null?0.0:promoteSeminarActivityDetail.getVenueMeal()));
	    	promoteActivityPreData.setEnableYzythPacks(totalHighPacks-
	    			(promoteSeminarActivityDetail.getApplyHighPacks()==null?0:promoteSeminarActivityDetail.getApplyHighPacks()));
	    }else
	    {
	    	promoteActivityPreData.setEnableTghwlbPacks(totalSeminarPacks);
	    	promoteActivityPreData.setEnableVenueMeal(toatlVenueMeal);
	    	promoteActivityPreData.setEnablePoins(toatlPoints);
	    	promoteActivityPreData.setEnableYzythPacks(totalHighPacks);
	    }
		
		return promoteActivityPreData;
	}



	private PromoteActivityPreData doGetEnableRoadShowPacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String, Object> reqMap, PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
		int totalRoadShowGifPacks = promoteDistribution.getRoadShowActiviPacksDistributionCount();
		int totalRoadShowConsumerPacks= promoteDistribution.getRoadShowConsumerPacksDistributionCount();
		//获取已经申请的礼包数
		PromoteRoadshowActivityDetail promoteRoadshowActivityDetail = roadshowActivityDetailMapper.getHasApplyRoadshowPacks(reqMap);
		if(null!=promoteRoadshowActivityDetail)
		{
			promoteActivityPreData.setEnableLywlbPacks(totalRoadShowGifPacks-
					(promoteRoadshowActivityDetail.getRoadShowGifPackageCount()==null?0:promoteRoadshowActivityDetail.getRoadShowGifPackageCount()));
			promoteActivityPreData.setEnableXfzhdbcbPacks(totalRoadShowConsumerPacks-
					(promoteRoadshowActivityDetail.getRoadShowConsumerPacksCount()==null?0:promoteRoadshowActivityDetail.getRoadShowConsumerPacksCount()));
		}else
		{
			promoteActivityPreData.setEnableLywlbPacks(totalRoadShowGifPacks);
			promoteActivityPreData.setEnableXfzhdbcbPacks(totalRoadShowConsumerPacks);
		}
		return promoteActivityPreData;
	}



	private PromoteActivityPreData doGetEnableXdOpenPacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String,Object> reqMap,
			PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
		int totalRDPacks = promoteDistribution.getOpenShopPacksDistributionCount();
		int totalGDPacks = promoteDistribution.getOpenShopGdPacksDistributionCount();
		//获取已经申请的礼包数
		PromoteXDOpenDetail prXdOpenDetail =  xdOpenDetailMapper.getHasApplyXDOpenPacks(reqMap);
		if(null!=prXdOpenDetail)
		{
			promoteActivityPreData.setEnableRdPacks(totalRDPacks-(prXdOpenDetail.getPacksNumber()==null?0:prXdOpenDetail.getPacksNumber()));
			promoteActivityPreData.setEnableGdPacks(totalGDPacks-(prXdOpenDetail.getGdPacksNumber()==null?0:prXdOpenDetail.getGdPacksNumber()));
		}else
		{
			promoteActivityPreData.setEnableGdPacks(totalGDPacks);
			promoteActivityPreData.setEnableRdPacks(totalRDPacks);
		}
		return promoteActivityPreData;
	}

	private PromoteActivityPreData doGetEnableShopSignsPacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String,Object> reqMap,
			PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
		int totalDzPacks = promoteDistribution.getStorePacksDistributionCount();
		//获取已经申请的礼包数
		PromoteShopSignsDetail detail =  promoteShopSignsDetailMapper.getHasApplyPacks(reqMap);
		if(null!=detail)
		{
			promoteActivityPreData.setEnableDzPacks(totalDzPacks-(detail.getPacksNumber()==null?0:detail.getPacksNumber()));
		}else
		{
			promoteActivityPreData.setEnableDzPacks(totalDzPacks);
		}
		return promoteActivityPreData;
	}
	
	private PromoteActivityPreData doGetEnableAdvertisementPacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String,Object> reqMap,
			PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
		int totalHwggPacks = promoteDistribution.getAdvertPacksDistributionCount();
		//获取已经申请的礼包数
		PromoteAdvertisementDetail detail =  promoteAdvertisementDetailMapper.getHasApplyPacks(reqMap);
		if(null!=detail)
		{
			promoteActivityPreData.setEnableHwggPacks(totalHwggPacks-(detail.getPacksNumber()==null?0:detail.getPacksNumber()));
		}else
		{
			promoteActivityPreData.setEnableHwggPacks(totalHwggPacks);
		}
		return promoteActivityPreData;
	}
	
	private PromoteActivityPreData doGetEnableAgriculturePacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String,Object> reqMap,
			PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
		int totalNkPacks = promoteDistribution.getAgriculturePacksDistributionCount();
		//获取已经申请的礼包数
		PromoteAgricultureDetail detail =  promoteAgricultureDetailMapper.getHasApplyPacks(reqMap);
		if(null!=detail)
		{
			promoteActivityPreData.setEnableNkPacks(totalNkPacks-(detail.getPacksNumber()==null?0:detail.getPacksNumber()));
		}else
		{
			promoteActivityPreData.setEnableNkPacks(totalNkPacks);
		}
		return promoteActivityPreData;
	}
	
	private PromoteActivityPreData doGetEnableTryOilPacks(
			PromoteActivityPreData promoteActivityPreData,
			Map<String,Object> reqMap,
			PromoteDistribution promoteDistribution)throws Exception {
		//总的礼包数
		int totalSyPacks = promoteDistribution.getTryPacksDistributionCount();
		//获取已经申请的礼包数
		PromoteTryOilDetail detail =  promoteTryOilDetailMapper.getHasApplyPacks(reqMap);
		if(null!=detail)
		{
			promoteActivityPreData.setEnableSyPacks(totalSyPacks-(detail.getPacksNumber()==null?0:detail.getPacksNumber()));
		}else
		{
			promoteActivityPreData.setEnableSyPacks(totalSyPacks);
		}
		return promoteActivityPreData;
	}

	private PromoteDistribution doGetTotalPacks(
			CustomerRegionUser customerRegionUser)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		Long sourcePlanId = customerRegionUser.getSourcePlanId();
		reqMap.put("sourcePlanId", sourcePlanId);
		reqMap.put("userId", customerRegionUser.getUserId());
		reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
		List<PromoteDistribution> lst2 = promoteDistributionMapper.getCurrentUserPacksInfo(reqMap);
		if(null!=lst2 && !lst2.isEmpty())
		{
			int totalOpenShopPacks = 0;
			int totalOpenGdPacks = 0;
			int totalSeminarPacks = 0;
			int totalRoadShowActiviPacks = 0;
			int totalRoadShowConsumerPacks = 0;
			int totalPoints = 0;
			Double totalVenueMeal = 0.0;
			int totalSeminarHighPacks = 0;
			int totalStorePacks = 0;
			int totalAdvertPacks = 0;
			int totalAgriculturePacks = 0;
			int totalTryPacks = 0;
			for(PromoteDistribution tmpPromoteDistribution:lst2)
			{
				totalOpenShopPacks += tmpPromoteDistribution.getOpenShopPacksDistributionCount();
				totalOpenGdPacks += tmpPromoteDistribution.getOpenShopGdPacksDistributionCount();
				totalSeminarPacks += tmpPromoteDistribution.getSeminarPacksDistributionCount();
				totalRoadShowActiviPacks += tmpPromoteDistribution.getRoadShowActiviPacksDistributionCount();
				totalRoadShowConsumerPacks += tmpPromoteDistribution.getRoadShowConsumerPacksDistributionCount();
				totalPoints += tmpPromoteDistribution.getPointsDistributionCount();
				totalVenueMeal += tmpPromoteDistribution.getVenueMealDistribution();
				totalSeminarHighPacks += tmpPromoteDistribution.getSeminarHighPacksDistributionCount();
				totalStorePacks += tmpPromoteDistribution.getStorePacksDistributionCount();
				totalAdvertPacks += tmpPromoteDistribution.getAdvertPacksDistributionCount();
				totalAgriculturePacks += tmpPromoteDistribution.getAgriculturePacksDistributionCount();
				totalTryPacks += tmpPromoteDistribution.getTryPacksDistributionCount();
			}
			PromoteDistribution promoteDistribution = new PromoteDistribution();
			promoteDistribution.setOpenShopPacksDistributionCount(totalOpenShopPacks);
			promoteDistribution.setOpenShopGdPacksDistributionCount(totalOpenGdPacks);
			promoteDistribution.setSeminarPacksDistributionCount(totalSeminarPacks);
			promoteDistribution.setRoadShowActiviPacksDistributionCount(totalRoadShowActiviPacks);
			promoteDistribution.setRoadShowConsumerPacksDistributionCount(totalRoadShowConsumerPacks);
			promoteDistribution.setPointsDistributionCount(totalPoints);
			promoteDistribution.setVenueMealDistribution(totalVenueMeal);
			promoteDistribution.setSeminarHighPacksDistributionCount(totalSeminarHighPacks);
			promoteDistribution.setStorePacksDistributionCount(totalStorePacks);
			promoteDistribution.setAdvertPacksDistributionCount(totalAdvertPacks);
			promoteDistribution.setAgriculturePacksDistributionCount(totalAgriculturePacks);
			promoteDistribution.setTryPacksDistributionCount(totalTryPacks);
			return promoteDistribution;
			//delete start by bo.liu 180702
			//PromoteDistribution promoteDistribution = lst2.get(0);
			//return promoteDistribution;
			//delete end by bo.liu 180702
		}
		return null;
	}



	private PromoteActivityPreData doGetPromoteGdGif(
			PromoteActivityPreData promoteActivityPreData,Long sourcePlanId)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("packsCode", PROMOTE_ACTIVITY_GDLB);
		reqMap.put("packsFlag", GIF_FLAG);
		reqMap.put("sourcePlanId", sourcePlanId);
		List<PromotePacksMaterials> lst3 = promotePacksMaterialsMapper.selectGif(reqMap);
		promoteActivityPreData.setGdlbGifLst(lst3);
		return promoteActivityPreData;
	}


	//TODO  可 剩余的
	@Deprecated
	private PromoteActivityPreData doGetEnablePromotionPoints(
			PromoteActivityPreData promoteActivityPreData,CustomerRegionUser customerRegionUser,PromoteDistribution promoteDistribution)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		Long sourcePlanId = customerRegionUser.getSourcePlanId();
		reqMap.put("sourcePlanId", sourcePlanId);
		//返回预置数据 可兑换礼品的积分数量
		reqMap.put("packsFlag", PROMOTE_FLAG);
		List<PromotePlanPacksDetail> lst1 = planPacksDetailMapper.getPoints(reqMap);
		Map<String,Object> pointsMap = new HashMap<String,Object>();
		for(PromotePlanPacksDetail promotePlanPacksDetail:lst1)
		{
			pointsMap.put(promotePlanPacksDetail.getPacksCode(), promotePlanPacksDetail.getPointsAmount());
		}
		
		//1.返回入店礼包用于兑换物料的总积分数量
		Integer rdPromotePointsCount = pointsMap.get(PROMOTE_ACTIVITY_RDLB)==null?0:(Integer)pointsMap.get(PROMOTE_ACTIVITY_RDLB);
		Integer currentUserRDTotalPointsCount = rdPromotePointsCount*promoteDistribution.getOpenShopPacksDistributionCount();
		//promoteActivityPreData.setRdPromotePointsCount(rdPromotePointsCount*promoteDistribution.getOpenShopPacksDistributionCount());
		//2.返回消费者互动礼包用于兑换物料的总积分数量
		Integer consumerInteractionPromotePointsCount = pointsMap.get(PROMOTE_ACTIVITY_XFZHDBCB)==null?0:(Integer)pointsMap.get(PROMOTE_ACTIVITY_XFZHDBCB);
		Integer currentUserConsumerInteractionTotalPointsCount = consumerInteractionPromotePointsCount*promoteDistribution.getRoadShowConsumerPacksDistributionCount();
		//promoteActivityPreData.setConsumerInteractionPromotePointsCount(consumerInteractionPromotePointsCount*promoteDistribution.getRoadShowConsumerPacksDistributionCount());
		
		//TODO 计算已经用了的积分数 180703 add
		//3.计算已经用了的积分数量（新店开业）
		reqMap.put("userId", customerRegionUser.getUserId());
		reqMap.put("activityType", PROMOTE_ACTIVITY_DLXDKY);
		reqMap.putAll(PromoteBizService.getStatus());//add by bo.liu 180703
		PromotePromotionalDetail xdOpenPromotionalDetail = promotionalDetailMapper.countHasApplyPoints(reqMap);
		if(null==xdOpenPromotionalDetail)
		{
			promoteActivityPreData.setRdPromotePointsCount(currentUserRDTotalPointsCount);
		}else
		{
			double d = xdOpenPromotionalDetail.getProductTotal()==null?0:xdOpenPromotionalDetail.getProductTotal();
			int hasApplyPoints = (int)d;
			promoteActivityPreData.setRdPromotePointsCount(currentUserRDTotalPointsCount-hasApplyPoints);
		}
		
		reqMap.put("activityType", PROMOTE_ACTIVITY_DLLY);
		PromotePromotionalDetail roadShowPromotionalDetail = promotionalDetailMapper.countHasApplyPoints(reqMap);
		if(null==roadShowPromotionalDetail)
		{
			promoteActivityPreData.setConsumerInteractionPromotePointsCount(currentUserConsumerInteractionTotalPointsCount);
		}else
		{
			double d = roadShowPromotionalDetail.getProductTotal()==null?0:roadShowPromotionalDetail.getProductTotal();
			int hasApplyPoints = (int)d;
			promoteActivityPreData.setConsumerInteractionPromotePointsCount(currentUserConsumerInteractionTotalPointsCount-hasApplyPoints);
		}
		return promoteActivityPreData;
	}


	private PromoteActivityPreData doGetActivityTypes(PromoteActivityPreData promoteActivityPreData)throws Exception {
		List<PromoteActivityType> lst = promoteActiviMapper.getActivityTypes();
		promoteActivityPreData.setLstActivityType(lst);
		return promoteActivityPreData;
	}



	@Deprecated
	@Override
	public Map<String, Object> getActivityTypes() {
		log.info("PromoteApplicationServiceImpl getActivityTypes");
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//0.返回活动类型
			List<PromoteActivityType> lst = promoteActiviMapper.getActivityTypes();
			resultMap.put(RESULT_LST_KEY, lst);
			//获取当前用户信息	
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Long sourcePlanId = customerRegionUser.getSourcePlanId();
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("sourcePlanId", sourcePlanId);
			
			//返回预置数据 可兑换礼品的积分数量
			reqMap.put("packsFlag", PROMOTE_FLAG);
			List<PromotePlanPacksDetail> lst1 = planPacksDetailMapper.getPoints(reqMap);
			Map<String,Object> pointsMap = new HashMap<String,Object>();
			for(PromotePlanPacksDetail promotePlanPacksDetail:lst1)
			{
				pointsMap.put(promotePlanPacksDetail.getPacksCode(), promotePlanPacksDetail.getPointsAmount());
			}
			//获取当前用户所得的礼包信息
			reqMap.put("userId", customerRegionUser.getUserId());
			reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
			List<PromoteDistribution> lst2 = promoteDistributionMapper.getCurrentUserPacksInfo(reqMap);
			if(null!=lst2 && !lst2.isEmpty())
			{
				//1.返回入店礼包用于兑换物料的积分数量
				PromoteDistribution promoteDistribution = lst2.get(0);
				Integer amount = pointsMap.get(PROMOTE_ACTIVITY_RDLB)==null?0:(Integer)pointsMap.get(PROMOTE_ACTIVITY_RDLB);
				resultMap.put(PROMOTE_ACTIVITY_RDLB.toLowerCase()+"Lst", amount*promoteDistribution.getOpenShopPacksDistributionCount());
				
				//2.返回消费者互动礼包用于兑换物料的积分数量
				Integer amount1 = pointsMap.get(PROMOTE_ACTIVITY_XFZHDBCB)==null?0:(Integer)pointsMap.get(PROMOTE_ACTIVITY_XFZHDBCB);
				resultMap.put(PROMOTE_ACTIVITY_XFZHDBCB.toLowerCase()+"Lst", amount1*promoteDistribution.getRoadShowConsumerPacksDistributionCount());
			}
			
			//3.返回攻店小礼品
			reqMap.put("packsCode", PROMOTE_ACTIVITY_GDLB);
			reqMap.put("packsFlag", GIF_FLAG);
			List<PromotePacksMaterials> lst3 = promotePacksMaterialsMapper.selectGif(reqMap);
			resultMap.put(PROMOTE_ACTIVITY_GDLB.toLowerCase()+"Lst", lst3);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getActivityTypes"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> getActivityNamesByType(String type) {
		log.info("PromoteApplicationServiceImpl getActivityNamesByType type:{}",type);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("activityType", type);
		List<PromoteActivityName> lst = promoteActiviMapper.getActivityNamesByType(reqMap);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		resultMap.put(RESULT_LST_KEY, lst);
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> getDealerList() {
		return null;
	}
	
	@Override
	public Map<String, Object> getMaterialsPurchasePoints(Integer price) {
		log.info("PromoteApplicationServiceImpl getMaterialsPurchasePoints price:{}",price);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			reqMap.put("materialPrice", price);
			List<WXTMaterialVo> lst = promoteActiviMapper.getMaterialsPurchasePoints(reqMap);
			resultMap.put(RESULT_LST_KEY, lst);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getMaterialsPurchasePoints"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertOrUpdateActivitys(
			List<PromoteActivity> lst,String activityCode, Long applyBatchId, String status) {
		log.info("insertOrUpdateActivitys lst:{},activityCode:{},applyBatchId:{},status:{}",lst,activityCode,applyBatchId,status);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			//获取活动的具体类型
			//PromotePacksConfig packsConfig =  promotePacksConfigMapper.selectBySubCode(activityCode);//之前传递子活动类型code
			PromotePacksConfig packsConfig =  promotePacksConfigMapper.selectByMainCode(activityCode);
			String activityName = packsConfig.getPacksType();
			
			Map<String,Object> returnMap = getPromotePacksAmountInfo();
			CustomerRegionUser customerRegionUser = (CustomerRegionUser)returnMap.get("customerRegionUser");
			PromotePacksAmountInfo promotePacksAmountInfo = (PromotePacksAmountInfo)returnMap.get("promotePacksAmountInfo");
			if(null==applyBatchId)
			{
				log.info("doInsertActivitys lst:{},activityCode:{},applyBatchId:{},status:{},customerRegionUser:{},promotePacksAmountInfo:{}"
						,lst,activityCode,applyBatchId,status,customerRegionUser,promotePacksAmountInfo);
				//新增(暂存/提交)
				doInsertActivitys(lst,customerRegionUser,activityCode,activityName,status,promotePacksAmountInfo);
				
			}else
			{
				log.info("doUpdateActivitys lst:{},activityCode:{},applyBatchId:{},status:{},customerRegionUser:{},promotePacksAmountInfo:{}"
						,lst,activityCode,applyBatchId,status,customerRegionUser,promotePacksAmountInfo);
				//暂存或者修改或者驳回
				doUpdateActivitys(applyBatchId,lst,customerRegionUser,activityCode,status,promotePacksAmountInfo);
				
			}
			//TODO 一旦提交。。。。就发送邮件给小区经理审批
			
			
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("insertOrUpdateActivitys"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}


	private Map<String, Object> getPromotePacksAmountInfo()throws Exception {
		Map<String,Object> returnMap = new HashMap<String,Object>();
		CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
		Long sourcePlanId = customerRegionUser.getSourcePlanId();
		Long sourceDistributionId = customerRegionUser.getSourceDistributionId();
		log.info("insertOrUpdateActivitys sourcePlanId:{},sourceDistributionId:{}",sourcePlanId,sourceDistributionId);
		//根据源计划id获取对应的  礼包金额
		List<PromotePlanPacksDetail> lst1 = planPacksDetailMapper.getPacksTotalAmount(sourcePlanId);
		PromotePacksAmountInfo promotePacksAmountInfo = new PromotePacksAmountInfo();
		for(PromotePlanPacksDetail tmpPromotePlanPacksDetail:lst1)
		{
			String packsCode = tmpPromotePlanPacksDetail.getPacksCode();
			double packsAmount = tmpPromotePlanPacksDetail.getMaterialsTotalAmount()==null?0:tmpPromotePlanPacksDetail.getMaterialsTotalAmount();
			if(PROMOTE_ACTIVITY_RDLB.equals(packsCode))
			{
				promotePacksAmountInfo.setRdlbAmount(packsAmount);
			}else if(PROMOTE_ACTIVITY_GDLB.equals(packsCode))
			{
				promotePacksAmountInfo.setGdlbAmount(packsAmount);
			}else if(PROMOTE_ACTIVITY_LYWLB.equals(packsCode))
			{
				promotePacksAmountInfo.setLywlbAmount(packsAmount);
			}else if(PROMOTE_ACTIVITY_TGHWLB.equals(packsCode))
			{
				promotePacksAmountInfo.setTghwlbAmount(packsAmount);
			}else if(PROMOTE_ACTIVITY_XFZHDBCB.equals(packsCode))
			{
				promotePacksAmountInfo.setXfzhdbcbAmount(packsAmount);
			} else if(PROMOTE_ACTIVITY_YZYTHLB.equals(packsCode)) {
				promotePacksAmountInfo.setYzythlbAmount(packsAmount);
			} else if(PROMOTE_ACTIVITY_DZLB.equals(packsCode)) {
				promotePacksAmountInfo.setDzlbAmount(packsAmount);
			} else if(PROMOTE_ACTIVITY_HWGGLB.equals(packsCode)) {
				promotePacksAmountInfo.setHwgglbAmount(packsAmount);
			} else if(PROMOTE_ACTIVITY_NKLB.equals(packsCode)) {
				promotePacksAmountInfo.setNklbAmount(packsAmount);
			} else if(PROMOTE_ACTIVITY_SYLB.equals(packsCode)) {
				promotePacksAmountInfo.setSylbAmount(packsAmount);
			} 
		}
		returnMap.put("customerRegionUser", customerRegionUser);
		returnMap.put("promotePacksAmountInfo", promotePacksAmountInfo);
		return returnMap;
	}



	private Double doUpdateActivitys(Long applyBatchId,List<PromoteActivity> lst,
			CustomerRegionUser customerRegionUser, String activityCode,
			String status, PromotePacksAmountInfo promotePacksAmountInfo)throws Exception {
		//更新对应批次状态
		PromoteApplicationBatch updatePromoteApplicationBatch = new PromoteApplicationBatch();
		updatePromoteApplicationBatch.setBatchid(applyBatchId);
		updatePromoteApplicationBatch.setApproveStatus(status);
		if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status))
		{
			//根据批次获取versionFlag 
			PromoteApplicationBatch tmpApplicationBatch = promoteApplicationBatchMapper.selectByPrimaryKey(applyBatchId);
			if(null==tmpApplicationBatch)
			{
				throw new Exception("没找到对应的活动的批次");
			}
			String versionFlag = tmpApplicationBatch.getVersionFlag()==null?"0":tmpApplicationBatch.getVersionFlag();
			updatePromoteApplicationBatch.setApproveStatus(BasePromote.PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
			updatePromoteApplicationBatch.setVersionFlag(""+(Integer.parseInt(versionFlag)+1));//版本
			updatePromoteApplicationBatch.setCurrentStep(BasePromote.PROMOTE_SUPERVISOR);//第一次流向到小区经理
			promoteBizService.doSndMessageForApprove(PROMOTE_SUPERVISOR, null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION,PROMOTE_SUPERVISOR_TIP+PROMOTE_ACTIVITY_APPROVE);//add by bo.liu 180712
			promoteBizService.doSndMailForApprove(PROMOTE_SUPERVISOR, null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION,PROMOTE_SUPERVISOR_TIP+PROMOTE_ACTIVITY_APPROVE);
			//录入一条日志到审批表中
			String parm1 = "首次提交";
			String parm2 = ".销售首次提交活动申请";
			if(!"1".equals(versionFlag))
			{
				parm1 = "再次提交";
				parm2 = ".销售再次提交活动申请";
			}
			approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, applyBatchId, status, parm1
					,versionFlag+parm2);
		}
		updatePromoteApplicationBatch.setRemark("销售最近更新:"+DateUtil.getCurrentDate(DateUtil.DEFAULT_DATE_TIME_PATTERN));
		promoteApplicationBatchMapper.updateByPrimaryKeySelective(updatePromoteApplicationBatch);
		//更新活动
		Double totalAmount = 0.0;
		for(PromoteActivity promoteActivity:lst)
		{
			 totalAmount = 0.0;
			//录入公共活动表
			promoteActivity.setApplyBatchId(applyBatchId);
			promoteActivity.setSourcePlanId(customerRegionUser.getSourcePlanId());
			promoteActivity.setSourceDistributionId(customerRegionUser.getSourceDistributionId());
			promoteActivity.setReleaseTime(new Date());
			promoteActivity.setStatus(status);
			if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status))
			{
				promoteActivity.setStatus(BasePromote.PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
			}
			promoteActiviMapper.updateByPrimaryKeySelective(promoteActivity);
			//根据类型录入活动明细表
			//if(PROMOTE_ACTIVITY_RDLB.equals(activityCode))//入店活动   小类型
			if(PROMOTE_ACTIVITY_DLXDKY.equals(activityCode))//入店活动   大类型
			{
				 doInsertOrUpdateRDActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_UPDATE);
				 log.info("doUpdateActivitys doInsertOrUpdateRDActivityDetail activityAmount:{}",promoteActivity.getActivityAmount());
				 doInsertOrUpdateGifForOpenShopDetail(promoteActivity, OPER_UPDATE);
				 log.info("doUpdateActivitys doInsertOrUpdateGifForOpenShopDetail activityAmount:{}",promoteActivity.getActivityAmount());
				
			}else if(PROMOTE_ACTIVITY_DLLY.equals(activityCode))//路演活动
			{
				doInsertOrUpdateLYActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_UPDATE);
				
			}else if(PROMOTE_ACTIVITY_YTH.equals(activityCode))//推广会活动  研讨会
			{
				doInsertOrUpdateTGHActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_UPDATE);
			}else if(PROMOTE_ACTIVITY_DCJF.equals(activityCode))//地促积分活动?????TODO
			{
				doInsertOrUpdatePromoteRegionalDetail(promoteActivity,OPER_UPDATE);
			} else if(PROMOTE_ACTIVITY_DZHD.equals(activityCode)) {
				doInsertOrUpdateShopSignsDetail(promoteActivity, promotePacksAmountInfo,OPER_UPDATE);
			} else if(PROMOTE_ACTIVITY_HWGG.equals(activityCode)) {
				doInsertOrUpdateAdvertisementDetail(promoteActivity, promotePacksAmountInfo, OPER_UPDATE);
			} else if(PROMOTE_ACTIVITY_NKKSGCJX.equals(activityCode)) {
				doInsertOrUpdateAgricultureDetail(promoteActivity, promotePacksAmountInfo, OPER_UPDATE);
			} else if(PROMOTE_ACTIVITY_SYYP.equals(activityCode)) {
				doInsertOrUpdateTryOilDetail(promoteActivity, promotePacksAmountInfo, OPER_UPDATE);
			}
			/*else if(PROMOTE_ACTIVITY_GDLB.equals(activityCode))//攻店活动 TODO
			{
				totalAmount = doInsertOrUpdateGDActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_UPDATE);
				
			}*//*else if(PROMOTE_ACTIVITY_XFZHDBCB.equals(activityCode))//消费者互动补充包  路演  TODO
			{
				
			}*/
			
			totalAmount = doInsertOrUpdatePromotePromotionalDetail(promoteActivity, OPER_UPDATE);
			//工作服的信息
			if(WORKCLOTHES_TRUE.equals(promoteActivity.getWorkClothesFlag()))
			{
				totalAmount = doInsertOrUpdateSpecialGifDetail(promoteActivity, OPER_UPDATE);
			}
			
			
			//更新公共活动表的总金额
			promoteActivity.setActivityAmount(totalAmount);
			promoteActiviMapper.updateByPrimaryKeySelective(promoteActivity);
			
		}
		return totalAmount;
	}


	private Double doInsertOrUpdateSpecialGifDetail(
			PromoteActivity promoteActivity, String operType)throws Exception {
		
		if(operType.equals(OPER_UPDATE))
		{
			specialGiftMapper.deleteByActivityId(promoteActivity.getId());
		}
		
		if(!EmptyChecker.isEmpty(promoteActivity.getPromoteSpecialGifLst()))
		{
			List<PromoteActivitySpecialGift> lstSpecialGift = promoteActivity.getPromoteSpecialGifLst();
			//获取lstSpecialGift详细信息
			List<Long> lstMaterialIds = new ArrayList<Long>();
			for(PromoteActivitySpecialGift spGif:lstSpecialGift)
			{
				if(!lstMaterialIds.contains(spGif.getMaterialId()))
				{
					lstMaterialIds.add(spGif.getMaterialId());
				}
			}
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("lstMaterialIds", lstMaterialIds);
			reqMap.put("sourcePlanId", promoteActivity.getSourcePlanId());
			reqMap.put("activityType", PROMOTE_ACTIVITY_RDLB);
			List<PromotePlanPacksDetail> lst = planPacksDetailMapper.getSpecialGif(reqMap);
			
			Map<String,Object> sepcialGifMap = new HashMap<String,Object>();
			for(PromotePlanPacksDetail tmp:lst)
			{
				sepcialGifMap.put(""+tmp.getMaterialsId(), tmp);
			}
			//这里暂时不算金额。。。。。
			
			//批量插入
			List<PromoteActivitySpecialGift> lstSpecialGiftNew = new ArrayList<PromoteActivitySpecialGift>(); 
			for(PromoteActivitySpecialGift spGif1:lstSpecialGift)
			{
				if(null==spGif1.getMaterialCount() || spGif1.getMaterialCount()==0)
				{
					continue;
				}
				spGif1.setActivityId(promoteActivity.getId());
				PromotePlanPacksDetail tmpGif = (PromotePlanPacksDetail) sepcialGifMap.get(""+spGif1.getMaterialId());
				spGif1.setMaterialDescrip(tmpGif.getMaterialsDescrp());
				spGif1.setMaterialPrice(tmpGif.getMaterialsPrice()==null?0:tmpGif.getMaterialsPrice());
				spGif1.setMaterialTotalAmount(spGif1.getMaterialCount()*spGif1.getMaterialPrice());
				spGif1.setMaterialCode(tmpGif.getMaterialsName());
				spGif1.setMaterialName(tmpGif.getMaterialsName());
				lstSpecialGiftNew.add(spGif1);
				
			}
			specialGiftMapper.insertBatch(lstSpecialGiftNew);//lstSpecialGift
		}
		
		if(EmptyChecker.isEmpty(promoteActivity.getActivityAmount())) {
			return 0.0;
		} else {
			return promoteActivity.getActivityAmount();
		}
		
	}



	private Double doInsertActivitys(List<PromoteActivity> lst,CustomerRegionUser customerRegionUser,
			String activityCode, String activityName,String status,PromotePacksAmountInfo promotePacksAmountInfo )throws Exception {
		//录入批次
		String applyTitle = DateUtils.getDateStr(new Date())+customerRegionUser.getChName()+activityName;//大类型
		PromoteApplicationBatch promoteApplyBatch = promoteBizService.insertPromoteApplicationBatch(applyTitle, status, 
				ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION,customerRegionUser.getUserId());
		
		//录入活动申请
		Double totalAmount = 0.0;
		for(PromoteActivity promoteActivity:lst)
		{
			totalAmount = 0.0;
			//录入公共活动表
			//promoteActivity.setActivityType(activityType);
			promoteActivity.setApplyBatchId(promoteApplyBatch.getBatchid());
			promoteActivity.setSourcePlanId(customerRegionUser.getSourcePlanId());
			promoteActivity.setSourceDistributionId(customerRegionUser.getSourceDistributionId());
			promoteActivity.setCreateTime(new Date());
			promoteActivity.setReleaseTime(new Date());
			promoteActivity.setCreator(customerRegionUser.getChName());//TODO 到底显示  中文，还是cai
			promoteActivity.setCreatorId(customerRegionUser.getUserId());
			promoteActivity.setStatus(status); // 是否需要同步???
			if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status))
			{
				promoteActivity.setStatus(BasePromote.PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
			}
			promoteActiviMapper.insertSelective(promoteActivity);
			//根据类型录入活动明细表
			//if(PROMOTE_ACTIVITY_RDLB.equals(activityCode))//入店活动  子类型
			if(PROMOTE_ACTIVITY_DLXDKY.equals(activityCode))//新店开业  主类型
			{
				doInsertOrUpdateRDActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_INSERT);
				log.info("doInsertActivitys doInsertOrUpdateRDActivityDetail activityAmount:{}",promoteActivity.getActivityAmount());
				doInsertOrUpdateGifForOpenShopDetail(promoteActivity,OPER_INSERT);
				log.info("doInsertActivitys doInsertOrUpdateGifForOpenShopDetail activityAmount:{}",promoteActivity.getActivityAmount());
				
			}else if(PROMOTE_ACTIVITY_DLLY.equals(activityCode))//路演活动
			{
				doInsertOrUpdateLYActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_INSERT);
				
			}else if(PROMOTE_ACTIVITY_YTH.equals(activityCode))//推广会活动  研讨会
			{
				doInsertOrUpdateTGHActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_INSERT);
			}
			else if(PROMOTE_ACTIVITY_DCJF.equals(activityCode))//地促积分活动?????TODO
			{
				doInsertOrUpdatePromoteRegionalDetail(promoteActivity,OPER_INSERT);
			} else if(PROMOTE_ACTIVITY_DZHD.equals(activityCode)) {
				doInsertOrUpdateShopSignsDetail(promoteActivity, promotePacksAmountInfo,OPER_INSERT);
			} else if(PROMOTE_ACTIVITY_HWGG.equals(activityCode)) {
				doInsertOrUpdateAdvertisementDetail(promoteActivity, promotePacksAmountInfo, OPER_INSERT);
			} else if(PROMOTE_ACTIVITY_NKKSGCJX.equals(activityCode)) {
				doInsertOrUpdateAgricultureDetail(promoteActivity, promotePacksAmountInfo, OPER_INSERT);
			} else if(PROMOTE_ACTIVITY_SYYP.equals(activityCode)) {
				doInsertOrUpdateTryOilDetail(promoteActivity, promotePacksAmountInfo, OPER_INSERT);
			}
			
			/*else if(PROMOTE_ACTIVITY_GDLB.equals(activityCode))//攻店活动 TODO
			{
				totalAmount = doInsertOrUpdateGDActivityDetail(promoteActivity, promotePacksAmountInfo,OPER_INSERT);
				
			}*/
			/*else if(PROMOTE_ACTIVITY_XFZHDBCB.equals(activityCode))//消费者互动补充包  路演  TODO
			{
				
			}*/
			
			
			totalAmount = doInsertOrUpdatePromotePromotionalDetail(promoteActivity,OPER_INSERT);
			//工作服的信息
			if(WORKCLOTHES_TRUE.equals(promoteActivity.getWorkClothesFlag()))
			{
				totalAmount = doInsertOrUpdateSpecialGifDetail(promoteActivity, OPER_UPDATE);
			}
			
			
			//更新公共活动表的总金额
			promoteActivity.setActivityAmount(totalAmount);
			promoteActiviMapper.updateByPrimaryKeySelective(promoteActivity);
		}
		return totalAmount;
	}


	private PromoteActivity doInsertOrUpdateGifForOpenShopDetail(
			PromoteActivity promoteActivity, String operType) {
		List<PromoteActivityGif> lstGif = promoteActivity.getActivityGifLst();
		
		if(operType.equals(OPER_UPDATE))
		{
			promoteGifMapper.deleteByActivityId(promoteActivity.getId());
		}
		
		if(null!=lstGif && !lstGif.isEmpty())
		{
			//这里暂时不算金额
			List<PromoteActivityGif> lstGifNew = new ArrayList<PromoteActivityGif>();
			for(PromoteActivityGif gif: lstGif )
			{
				gif.setActivityId(promoteActivity.getId());
				if(null==gif.getGifAmount() || gif.getGifAmount()==0)
				{
					continue;
				}
				lstGifNew.add(gif);
			}
			
			//批量插入
			promoteGifMapper.insertBatch(lstGifNew);//lstGif
		}
		return promoteActivity;
	}


	private PromoteActivity doInsertOrUpdatePromoteRegionalDetail(
			PromoteActivity promoteActivity,String operType)throws Exception {
		//地促活动
		Double promoteRegionalActivityTotal = 0.0;
		PromoteRegionalDetail promoteRegionalDetail = promoteActivity.getPromoteRegionalDetail();
		promoteRegionalDetail.setActivityId(promoteActivity.getId());
		promoteRegionalDetail.setActivityType(promoteActivity.getActivityType());
		promoteRegionalActivityTotal+=1*promoteRegionalDetail.getApplyPromotionSupportPoints();
		if(OPER_INSERT.equals(operType))
		{
			promoteRegionalDetailMapper.insertSelective(promoteRegionalDetail);
		}else
		{
			promoteRegionalDetailMapper.updateByPrimaryKeySelective(promoteRegionalDetail);
			
		}
		promoteActivity.setActivityAmount(promoteRegionalActivityTotal);
		return promoteActivity;
	}
	
	private PromoteActivity doInsertOrUpdateShopSignsDetail(
			PromoteActivity promoteActivity, PromotePacksAmountInfo promotePacksAmountInfo, String operType)throws Exception {
		PromoteShopSignsDetail detail = promoteActivity.getPromoteShopSignsDetail();
		detail.setActivityId(promoteActivity.getId());
		detail.setActivityType(promoteActivity.getActivityType());
		Double amount = detail.getPacksNumber()*promotePacksAmountInfo.getDzlbAmount();
		if(OPER_INSERT.equals(operType))
		{
			promoteShopSignsDetailMapper.insertSelective(detail);
		}else
		{
			promoteShopSignsDetailMapper.updateByPrimaryKeySelective(detail);
			
		}
		promoteActivity.setActivityAmount(amount);
		return promoteActivity;
	}
	
	private PromoteActivity doInsertOrUpdateAdvertisementDetail(
			PromoteActivity promoteActivity, PromotePacksAmountInfo promotePacksAmountInfo, String operType)throws Exception {
		PromoteAdvertisementDetail detail = promoteActivity.getPromoteAdvertisementDetail();
		detail.setActivityId(promoteActivity.getId());
		detail.setActivityType(promoteActivity.getActivityType());
		Double amount = detail.getPacksNumber()*promotePacksAmountInfo.getHwgglbAmount();
		if(OPER_INSERT.equals(operType))
		{
			promoteAdvertisementDetailMapper.insertSelective(detail);
		}else
		{
			promoteAdvertisementDetailMapper.updateByPrimaryKeySelective(detail);
			
		}
		promoteActivity.setActivityAmount(amount);
		return promoteActivity;
	}
	
	private PromoteActivity doInsertOrUpdateAgricultureDetail(
			PromoteActivity promoteActivity, PromotePacksAmountInfo promotePacksAmountInfo, String operType)throws Exception {
		PromoteAgricultureDetail detail = promoteActivity.getPromoteAgricultureDetail();
		detail.setActivityId(promoteActivity.getId());
		detail.setActivityType(promoteActivity.getActivityType());
		Double amount = detail.getPacksNumber()*promotePacksAmountInfo.getNklbAmount();
		if(OPER_INSERT.equals(operType))
		{
			promoteAgricultureDetailMapper.insertSelective(detail);
		}else
		{
			promoteAgricultureDetailMapper.updateByPrimaryKeySelective(detail);
			
		}
		promoteActivity.setActivityAmount(amount);
		return promoteActivity;
	}
	
	private PromoteActivity doInsertOrUpdateTryOilDetail(
			PromoteActivity promoteActivity, PromotePacksAmountInfo promotePacksAmountInfo, String operType)throws Exception {
		PromoteTryOilDetail detail = promoteActivity.getPromoteTryOilDetail();
		detail.setActivityId(promoteActivity.getId());
		detail.setActivityType(promoteActivity.getActivityType());
		Double amount = detail.getPacksNumber()*promotePacksAmountInfo.getSylbAmount();
		if(OPER_INSERT.equals(operType))
		{
			promoteTryOilDetailMapper.insertSelective(detail);
		}else
		{
			promoteTryOilDetailMapper.updateByPrimaryKeySelective(detail);
			
		}
		promoteActivity.setActivityAmount(amount);
		return promoteActivity;
	}


	private PromoteActivity doInsertOrUpdateTGHActivityDetail(PromoteActivity promoteActivity,
			PromotePacksAmountInfo promotePacksAmountInfo,String operType)throws Exception {
		//研讨会/推广会总金额
		Double tghActivityTotal = 0.0;
		PromoteSeminarActivityDetail promoteSeminarActivityDetail= promoteActivity.getPromoteSeminarActivityDetail();
		promoteSeminarActivityDetail.setActivityId(promoteActivity.getId());
		promoteSeminarActivityDetail.setActivityType(promoteActivity.getActivityType());
		tghActivityTotal+=promotePacksAmountInfo.getLywlbAmount()*promoteSeminarActivityDetail.getApplyPacksCount();
		tghActivityTotal+=promoteSeminarActivityDetail.getApplyPoints()==null?0:promoteSeminarActivityDetail.getApplyPoints();
		tghActivityTotal+=promoteSeminarActivityDetail.getVenueMeal()==null?0:promoteSeminarActivityDetail.getVenueMeal();
		if(OPER_INSERT.equals(operType))
		{
			promoteSeminarActivityDetailMapper.insertSelective(promoteSeminarActivityDetail);
		}else
		{
			promoteSeminarActivityDetailMapper.updateByPrimaryKeySelective(promoteSeminarActivityDetail);
		}
		promoteActivity.setActivityAmount(tghActivityTotal);
		return promoteActivity;
	}


	private PromoteActivity doInsertOrUpdateLYActivityDetail(PromoteActivity promoteActivity,
			PromotePacksAmountInfo promotePacksAmountInfo,String operType)throws Exception {
		//路演活动总金额
		Double lyActivityTotal = 0.0;
		PromoteRoadshowActivityDetail promoteRoadshowActivityDetail = promoteActivity.getPromoteRoadshowActivityDetail();
		promoteRoadshowActivityDetail.setActivityId(promoteActivity.getId());
		promoteRoadshowActivityDetail.setActivityType(promoteActivity.getActivityType());
		lyActivityTotal+=promotePacksAmountInfo.getLywlbAmount()*promoteRoadshowActivityDetail.getRoadShowGifPackageCount();
		lyActivityTotal+=promotePacksAmountInfo.getXfzhdbcbAmount()*promoteRoadshowActivityDetail.getRoadShowConsumerPacksCount();
		if(OPER_INSERT.equals(operType))
		{
			roadshowActivityDetailMapper.insertSelective(promoteRoadshowActivityDetail);
		}else
		{
			roadshowActivityDetailMapper.updateByPrimaryKeySelective(promoteRoadshowActivityDetail);
		}
		promoteActivity.setActivityAmount(lyActivityTotal);
		return promoteActivity;
	}


	private Double doInsertOrUpdateGDActivityDetail(PromoteActivity promoteActivity,
			PromotePacksAmountInfo promotePacksAmountInfo,String operType)throws Exception  {
		//入店活动总金额
		Double gdActivityTotal = 0.0;
		//录入 入店的
		PromoteGDOpenDetail promoteGDOpenDetail = promoteActivity.getPromoteGDOpenDetail();
		promoteGDOpenDetail.setActivityType(promoteActivity.getActivityType());
		promoteGDOpenDetail.setActivityId(promoteActivity.getId());
		gdActivityTotal+=promoteGDOpenDetail.getPacksNumer()*promotePacksAmountInfo.getGdlbAmount();
		if(OPER_INSERT.equals(operType))
		{
			gdOpenDetailMapper.insertSelective(promoteGDOpenDetail);
		}else
		{
			gdOpenDetailMapper.updateByPrimaryKeySelective(promoteGDOpenDetail);
		}
		return gdActivityTotal;
	}


	/**
	 * 返回总金额
	 * <AUTHOR> 2018-6-22 上午11:22:03
	 * @param promoteActivity
	 * @return
	 * @throws Exception
	 */
	private PromoteActivity doInsertOrUpdateRDActivityDetail(PromoteActivity promoteActivity,PromotePacksAmountInfo promotePacksAmountInfo,String operType)throws Exception {
		//活动总金额
		Double activityTotal = 0.0;
		//入店礼包总金额
		Double rdPacksTotal = 0.0;
		//攻店礼包总金额
		Double gdPacksTotal = 0.0;
		//录入 入店的
		PromoteXDOpenDetail promoteXDOpenDetail = promoteActivity.getPromoteXDOpenDetail();
		promoteXDOpenDetail.setActivityType(promoteActivity.getActivityType());
		promoteXDOpenDetail.setActivityId(promoteActivity.getId());
		rdPacksTotal+=promoteXDOpenDetail.getPacksNumber()*promotePacksAmountInfo.getRdlbAmount();
		gdPacksTotal+=(promoteXDOpenDetail.getGdPacksNumber()==null?0:promoteXDOpenDetail.getGdPacksNumber())*promotePacksAmountInfo.getGdlbAmount();
		if(OPER_INSERT.equals(operType))
		{
			xdOpenDetailMapper.insertSelective(promoteXDOpenDetail);
		}else
		{
			xdOpenDetailMapper.updateByPrimaryKeySelective(promoteXDOpenDetail);
		}
		activityTotal = rdPacksTotal+gdPacksTotal;
		promoteActivity.setActivityAmount(activityTotal);
		promoteActivity.setPromoteXDOpenDetail(promoteXDOpenDetail);
		return promoteActivity;
	}
	
	/**
	 * 录入促销品，，，供积分兑换的物品
	 * <AUTHOR> 2018-6-26 上午6:55:45
	 * @param promoteActivity
	 * @param operType
	 * @return
	 */
	private Double doInsertOrUpdatePromotePromotionalDetail(PromoteActivity promoteActivity,String operType)
	{
		//其实activityDetailId  没有必要 TODO  考虑后续去除
		//录入 卡车司机的...（促销品）
		List<PromotePromotionalDetail> lstPromotePromotionalDetail = null;
		Long activityDetailId = null;
		//TODO 待优化
		if(PROMOTE_ACTIVITY_DLXDKY.equals(promoteActivity.getActivityType()))
		{
			
			PromoteXDOpenDetail	promoteDetail = promoteActivity.getPromoteXDOpenDetail();
			lstPromotePromotionalDetail = promoteActivity.getPromotePromotionalDetailLst();
			activityDetailId = promoteDetail.getId();
		}else if(PROMOTE_ACTIVITY_DLLY.equals(promoteActivity.getActivityType()))
		{
			PromoteRoadshowActivityDetail promoteDetail = promoteActivity.getPromoteRoadshowActivityDetail();
			lstPromotePromotionalDetail = promoteActivity.getPromotePromotionalDetailLst();
			activityDetailId = promoteDetail.getId();
		}
		//TODO 
	
		if(OPER_UPDATE.equals(operType))
		{
			//更新 先删除，后插入。。。。。。
			promotionalDetailMapper.deleteByActivityId(promoteActivity.getId());
		}
		
		Double toatalPointAmount = 0.0;
		if(null!=lstPromotePromotionalDetail && !lstPromotePromotionalDetail.isEmpty())
		{
			for(PromotePromotionalDetail promotePromotionalDetail:lstPromotePromotionalDetail)
			{
				promotePromotionalDetail.setActivityId(promoteActivity.getId());
				promotePromotionalDetail.setActivityDetailId(activityDetailId);
				//  不单独算金额  toatalPointAmount += promotePromotionalDetail.getProductTotal();
			}
			//批量录入卡车司机
			promotionalDetailMapper.inserBatch(lstPromotePromotionalDetail);
		}
		
		if(EmptyChecker.isEmpty(promoteActivity.getActivityAmount())) {
			return toatalPointAmount;
		} else {
			return toatalPointAmount+promoteActivity.getActivityAmount();
		}
	}
	
	
	
	
	@Override
	public Map<String, Object> queryActivityApplyList(String queryStatusType,
			String queryUserType,String queryFiled) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			//获取用户信息
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			//待处理，只有小区经理，查询自己创建的即可
			if(PROMOTE_DISTRIBUTION_PENDING_QUERYTYPE.equals(queryStatusType))
			{
				reqMap.put("creatorId", customerRegionUser.getUserId());
				reqMap.put("pendingStatus", "true");
				reqMap.put("saveStatus", PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS);
				reqMap.put("unapprovedStatus", PROMOTE_DISTRIBUTION_BATCH_UNAPPROVED_STATUS);
				reqMap.put("queryFiled", queryFiled);
				reqMap.put("applyKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION);
				List<PromoteApplicationBatch> lst  = promoteApplicationBatchMapper.getPromoteActivityApplayLstByMap(reqMap);
				resultMap.put(RESULT_LST_KEY, lst);
				return resultMap;
			}
			
			//审核中
			if(PROMOTE_DISTRIBUTION_INAPPROVAL_QUERYTYPE.equals(queryStatusType))
			{
				List<PromoteApplicationBatch> lst1 = new ArrayList<PromoteApplicationBatch>();
				if(PROMOTE_SALES.equals(queryUserType))
				{
					//销售
					lst1 = doGetActivityApplyListBySales(customerRegionUser,queryFiled,PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
				}else if(PROMOTE_SUPERVISOR.equals(queryUserType))
				{
					//小区经理
					lst1 = doGetActivityApplyListInapprovalBySupervisor(customerRegionUser,queryFiled);
				}else if(PROMOTE_CHANNELMANAGER.equals(queryUserType))
				{
					//大区经理
					lst1 = doGetActivityApplyListInapprovalByChannelManager(customerRegionUser,queryFiled);
				}else if(PROMOTE_MARKETING.equals(queryUserType))
				{
					//marketing
					lst1 = doGetActivityApplyListByMarketing(customerRegionUser,queryFiled,PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
					
				}
				resultMap.put(RESULT_LST_KEY, lst1);
				return resultMap;
			}
			
			
			//已经审核
			List<PromoteApplicationBatch> lst2 = new ArrayList<PromoteApplicationBatch>();
			if(PROMOTE_DISTRIBUTION_APPROVED_QUERYTYPE.equals(queryStatusType))
			{
				
				if(PROMOTE_SALES.equals(queryUserType))
				{
					//销售
					lst2 = doGetActivityApplyListBySales(customerRegionUser,queryFiled,PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
				}else if(PROMOTE_MARKETING.equals(queryUserType))
				{
					//marketing
					lst2 = doGetActivityApplyListByMarketing(customerRegionUser,queryFiled,PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
				}
			}
			resultMap.put(RESULT_LST_KEY, lst2);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryActivityApplyList"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	private List<PromoteApplicationBatch> doGetActivityApplyListByMarketing(CustomerRegionUser customerRegionUser,
			String queryFiled,String status)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("queryFiled", queryFiled);
		reqMap.put("approvingStatus", status);
		reqMap.put("currentStep", PROMOTE_MARKETING);
		reqMap.put("applyKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION);
		//需要添加区域过滤 add by bo.liu 180704
		reqMap.put("regionName", customerRegionUser.getRegionName());
		List<PromoteApplicationBatch> lst  = promoteApplicationBatchMapper.getPromoteActivityApplayLstByMap(reqMap);
		return lst;
	}


	private List<PromoteApplicationBatch> doGetActivityApplyListInapprovalByChannelManager(
			CustomerRegionUser customerRegionUser, String queryFiled)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("queryFiled", queryFiled);
		reqMap.put("currentUserCai", customerRegionUser.getCai());
		reqMap.put("approvingStatus", PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
		reqMap.put("businessName", REGION_TYPE);
		reqMap.put("currentStep", PROMOTE_CHANNELMANAGER);
		reqMap.put("applyKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION);
		List<PromoteApplicationBatch> lst  = promoteApplicationBatchMapper.getActivityApplyListInapprovalByChannelManager(reqMap);
		return lst;
	}


	private List<PromoteApplicationBatch> doGetActivityApplyListInapprovalBySupervisor(
			CustomerRegionUser customerRegionUser, String queryFiled)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("queryFiled", queryFiled);
		reqMap.put("currentUserCai", customerRegionUser.getCai());
		reqMap.put("approvingStatus", PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
		reqMap.put("currentStep", PROMOTE_SUPERVISOR);
		reqMap.put("applyKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION);
		List<PromoteApplicationBatch> lst  = promoteApplicationBatchMapper.getPromoteActivityApplayLstForSupervisorByMap(reqMap);
		return lst;
	}


	private List<PromoteApplicationBatch> doGetActivityApplyListBySales(
			CustomerRegionUser customerRegionUser, String queryFiled,String status)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("queryFiled", queryFiled);
		reqMap.put("creatorId", customerRegionUser.getUserId());
		reqMap.put("approvingStatus", status);
		reqMap.put("applyKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION);
		List<PromoteApplicationBatch> lst  = promoteApplicationBatchMapper.getPromoteActivityApplayLstByMap(reqMap);
		return lst;
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> generatePromoteOrder(PromoteOrder promoteOrder) {
		log.info("generatePromoteOrder promoteOrder:{}",promoteOrder.toString());
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			promoteOrderMapper.insertSelective(promoteOrder);
			//需要批量录入订单明细....
			List<PromoteOrderDetail> lst = promoteOrder.getLstDetails();
			log.info("generatePromoteOrder lst:{}",lst.size());
			if(null!=lst && !lst.isEmpty())//订单没有审批通过
			{
				promoteOrderDetailMapper.insertBatch(lst);
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("generatePromoteOrder"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> generatePromotePointsLog(
			PromoteRegionalPointsLog promotePointsLog) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public Map<String, Object> queryActivityDetails(Long activityId,String activityTypeCode) {
		log.info("queryActivityDetails activityId:{},activityTypeCode",activityId,activityTypeCode);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Object obj = null;
			//if(PROMOTE_ACTIVITY_RDLB.equals(activityTypeCode))  子类型
			if(PROMOTE_ACTIVITY_DLXDKY.equals(activityTypeCode)) 
			{
				//入店活动
				obj = doGetRDActivityDetail(activityId);
				
			}else if(PROMOTE_ACTIVITY_DLLY.equals(activityTypeCode))
			{
				//路演活动
				obj = doGetRoadShowActivityDetail(activityId);
			}else if(PROMOTE_ACTIVITY_YTH.equals(activityTypeCode))
			{
				//研讨会推广活动
				obj = doGetSeminarActivityDetail(activityId);
			}else if(PROMOTE_ACTIVITY_DCJF.equals(activityTypeCode))
			{
				//地促积分活动
				obj = doGetRegionalActivityDetail(activityId);
			}
			else if(PROMOTE_ACTIVITY_GDLB.equals(activityTypeCode))
			{
				//攻店活动  
				//TODO...没有前端
			}
			else if(PROMOTE_ACTIVITY_XFZHDBCB.equals(activityTypeCode))
			{
				//路演消费者互动
				//TODO...没后前端,已经合并
			}
			else if(PROMOTE_ACTIVITY_DZHD.equals(activityTypeCode))
			{
				//店招活动
				obj = doGetShopSignsActivityDetail(activityId);
			}
			else if(PROMOTE_ACTIVITY_HWGG.equals(activityTypeCode))
			{
				//户外广告
				obj = doGetAdvertisementActivityDetail(activityId);
			}
			else if(PROMOTE_ACTIVITY_NKKSGCJX.equals(activityTypeCode))
			{
				//农垦/矿山/工程机械物料
				obj = doGetAgricultureActivityDetail(activityId);
			}
			else if(PROMOTE_ACTIVITY_SYYP.equals(activityTypeCode))
			{
				//试用油品/油样检测
				obj = doGetTryOilActivityDetail(activityId);
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put(RESULT_OBJ_KEY, obj);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryActivityList"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	private Object doGetShopSignsActivityDetail(Long activityId) {
		ResponsePromoteShopSignsDetail detail = new ResponsePromoteShopSignsDetail();
		List<ResponsePromoteShopSignsDetail> lst = promoteShopSignsDetailMapper.getShopSignsActivityDetail(activityId);
		if(!EmptyChecker.isEmpty(lst)) {
			detail = lst.get(0);
		}
		return detail;
	}
	
	private Object doGetAdvertisementActivityDetail(Long activityId) {
		ResponsePromoteAdvertisementDetail detail = new ResponsePromoteAdvertisementDetail();
		List<ResponsePromoteAdvertisementDetail> lst = promoteAdvertisementDetailMapper.getActivityDetail(activityId);
		if(!EmptyChecker.isEmpty(lst)) {
			detail = lst.get(0);
		}
		return detail;
	}
	
	private Object doGetAgricultureActivityDetail(Long activityId) {
		ResponsePromoteAgricultureDetail detail = new ResponsePromoteAgricultureDetail();
		List<ResponsePromoteAgricultureDetail> lst = promoteAgricultureDetailMapper.getActivityDetail(activityId);
		if(!EmptyChecker.isEmpty(lst)) {
			detail = lst.get(0);
		}
		return detail;
	}
	
	private Object doGetTryOilActivityDetail(Long activityId) {
		ResponsePromoteTryOilDetail detail = new ResponsePromoteTryOilDetail();
		List<ResponsePromoteTryOilDetail> lst = promoteTryOilDetailMapper.getActivityDetail(activityId);
		if(!EmptyChecker.isEmpty(lst)) {
			detail = lst.get(0);
		}
		return detail;
	}
	
	private Object doGetRegionalActivityDetail(Long activityId)throws Exception {
		ResponsePromoteRegionalDetail promoteRegionalDetail = new ResponsePromoteRegionalDetail();
		List<ResponsePromoteRegionalDetail> lst = promoteRegionalDetailMapper.getRegionalActivityDetail(activityId);
		if(null!=lst && !lst.isEmpty())
		{
			promoteRegionalDetail = lst.get(0);
		}
		return promoteRegionalDetail;
	}


	private Object doGetSeminarActivityDetail(Long activityId)throws Exception {
		ResponsePromoteSeminarDetail responsePromoteSeminarDetail = new ResponsePromoteSeminarDetail();
		List<ResponsePromoteSeminarDetail> lst = promoteSeminarActivityDetailMapper.getSeminarActivityDetail(activityId);
		if(null!=lst && !lst.isEmpty())
		{
			responsePromoteSeminarDetail = lst.get(0);
		}
		return responsePromoteSeminarDetail;
	}


	private ResponsePromoteRoadShowDetail doGetRoadShowActivityDetail(Long activityId)throws Exception {
		ResponsePromoteRoadShowDetail responsePromoteRoadShowDetail = new ResponsePromoteRoadShowDetail();
		List<ResponsePromoteRoadShowDetail> lst = roadshowActivityDetailMapper.getRoadShowActivityDetail(activityId);
		if(null!=lst && !lst.isEmpty())
		{
			responsePromoteRoadShowDetail = lst.get(0);
		}
		//卡车司机促销积分列表
		List<PromotePromotionalDetail> lst2 = promotionalDetailMapper.selectByActivityId(activityId);
		responsePromoteRoadShowDetail.setPromotionalDetailLst(lst2);
		return responsePromoteRoadShowDetail;
	}


	private ResponsePromoteXDOpenDetail doGetRDActivityDetail(Long activityId)throws Exception {
		ResponsePromoteXDOpenDetail responsePromoteXDOpenDetail =  new ResponsePromoteXDOpenDetail();
		List<ResponsePromoteXDOpenDetail> lst = xdOpenDetailMapper.getXDOpenActivityDetail(activityId);
		if(null!=lst && !lst.isEmpty())
		{
			responsePromoteXDOpenDetail = lst.get(0);
		}
		//卡车司机促销积分列表
		List<PromotePromotionalDetail> lst2 = promotionalDetailMapper.selectByActivityId(activityId);
		responsePromoteXDOpenDetail.setPromotionalDetailLst(lst2);
		//小礼品
		List<PromoteActivityGif> lst3 = promoteGifMapper.getGifLstByActivityId(activityId);
		responsePromoteXDOpenDetail.setPromoteActivityGifLst(lst3);
		//特殊礼品 工作服这种
		List<PromoteActivitySpecialGift> lst4 = specialGiftMapper.getSpecialGifLstByActivityId(activityId);
		responsePromoteXDOpenDetail.setLstSpecialGifts(lst4);
		
		return responsePromoteXDOpenDetail;
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> submitActivityFeedBack(
			PromoteActivityFeedback activityFeedBack) {
		log.info("submitActivityFeedBack activityFeedBack:{}",activityFeedBack.toString());
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//0.查询活动是否已经反馈
			PromoteActivity tmpPromoteActivity = promoteActiviMapper.selectByPrimaryKey(activityFeedBack.getActivityId());
			if(PromoteStatusEnum.STATUS_FEEDBACK.getStatusCode().equals(tmpPromoteActivity.getStatus()))
			{
				throw new Exception("活动已反馈，不能再次提交反馈");
			}
			
			Map<String,Object> reqMap = new HashMap<String,Object>();
			//1.先录入到活动反馈表中
			activityFeedBack.setFeedbackTime(new Date());
			promoteActivityFeedbackMapper.insertSelective(activityFeedBack);
			//2.更新对应的图片id，所对应的sourceId
			String attIds = activityFeedBack.getActivityImgIds();
			
			
			
			if(null!=attIds && !attIds.equals("") && !attIds.equals(","))
			{
				String attids[] = attIds.split(",");
				if(attids.length>0)
				{
					Long[] attIDS = new Long[attids.length];
					for(int i=0; i<attIDS.length;i++)
					{
						attIDS[i] = Long.parseLong(attids[i]);
					}
					reqMap.put("sourceId",activityFeedBack.getId());//反馈的id  非活动的id
					reqMap.put("attIds", attIDS);
					wxAttFileMapper.updateWxAttFilesSetSourceId(reqMap);
				}
			}
			//3. 针对研讨会活动，A14
			if(PROMOTE_ACTIVITY_YTH.equals(activityFeedBack.getActivityType()))
			{
				String a14AttIds = activityFeedBack.getA14ApplyForm();
				String complianceInvoice = activityFeedBack.getComplianceInvoice();
				String checkInSlip = activityFeedBack.getCheckInSlip();
				String conferencePhoto = activityFeedBack.getConferencePhoto();
				String conferenceSchedule = activityFeedBack.getConferenceSchedule();
				String hotelFeeStatement = activityFeedBack.getHotelFeeStatement();
				String tripartiteAgreement = activityFeedBack.getTripartiteAgreement();
				String proofPurchase = activityFeedBack.getProofPurchase();
				
				String  attLst = "";
				if(null!=a14AttIds&& !a14AttIds.isEmpty())
				{
					attLst+=a14AttIds+",";
				}
				
				if(null!=complianceInvoice && !complianceInvoice.isEmpty())
				{
					attLst+=complianceInvoice+",";
				}
				if(null!=checkInSlip && !checkInSlip.isEmpty())
				{
					attLst+=checkInSlip+",";
				}
				if(null!=conferencePhoto && !conferencePhoto.isEmpty())
				{
					attLst+=conferencePhoto+",";
				}
				if(null!=conferenceSchedule && !conferenceSchedule.isEmpty())
				{
					attLst+=conferenceSchedule+",";
				}
				if(null!=hotelFeeStatement && !hotelFeeStatement.isEmpty())
				{
					attLst+=hotelFeeStatement+",";
				}
				if(null!=tripartiteAgreement && !tripartiteAgreement.isEmpty())
				{
					attLst+=tripartiteAgreement+",";
				}
				if(null!=proofPurchase && !proofPurchase.isEmpty())
				{
					attLst+=proofPurchase+",";
				}
				
				log.info("submitActivityFeedBack:"+attLst.toString());
				if(null!=attLst && !attLst.equals("") && !attLst.equals(","))
				{
					String attids[] = attLst.split(",");
					if(attids.length>0)
					{
						Long[] attIDS = new Long[attids.length];
						for(int i=0; i<attIDS.length;i++)
						{
							attIDS[i] = Long.parseLong(attids[i]);
						}
						reqMap.put("sourceId",activityFeedBack.getId());//反馈的id  非活动的id
						reqMap.put("attIds", attIDS);
						wxAttFileMapper.updateWxAttFilesSetSourceId(reqMap);
					}
				}
			}
			//4.更新活动状态
			PromoteActivity promoteActivity = new PromoteActivity();
			promoteActivity.setId(activityFeedBack.getActivityId());
			promoteActivity.setStatus(PromoteStatusEnum.STATUS_FEEDBACK.getStatusCode());
			promoteActiviMapper.updateByPrimaryKeySelective(promoteActivity);
			
			//5.录入一条反馈历史
			PromoteApplicationBatch tmpApplicationBatch = promoteApplicationBatchMapper.selectByPrimaryKey(activityFeedBack.getApplyBatchId());
			String versionFlag = tmpApplicationBatch.getVersionFlag()==null?"0":tmpApplicationBatch.getVersionFlag();
			approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, activityFeedBack.getApplyBatchId(), PromoteStatusEnum.STATUS_FEEDBACK.getStatusCode(), 
					"活动反馈",versionFlag+".经销商已反馈");
			
			
			
			
			//3. 针对活动类型为  研讨会的，，需要录入场地餐费
			/*if(PROMOTE_ACTIVITY_YTH.equals(activityFeedBack.getActivityType()))
			{
				if(null!=activityFeedBack.getVenueMeal() && activityFeedBack.getVenueMeal()!=0)
				{
					PromoteSeminarActivityDetail promoteSeminarActivityDetail = new PromoteSeminarActivityDetail();
					promoteSeminarActivityDetail.setActivityId(activityFeedBack.getActivityId());
					promoteSeminarActivityDetail.setA14ApplyForm(activityFeedBack.getA14ApplyForm());
					promoteSeminarActivityDetail.setVenueMeal(activityFeedBack.getVenueMeal());
					promoteSeminarActivityDetail.setComplianceInvoice(activityFeedBack.getComplianceInvoice());
					promoteSeminarActivityDetail.setCheckInSlip(activityFeedBack.getCheckInSlip());
					promoteSeminarActivityDetail.setConferencePhoto(activityFeedBack.getConferencePhoto());
					promoteSeminarActivityDetail.setConferenceSchedule(activityFeedBack.getConferenceSchedule());
					promoteSeminarActivityDetail.setHotelFeeStatement(activityFeedBack.getHotelFeeStatement());
					promoteSeminarActivityDetail.setTripartiteAgreement(activityFeedBack.getTripartiteAgreement());
					promoteSeminarActivityDetail.setProofPurchase(activityFeedBack.getProofPurchase());
					promoteSeminarActivityDetailMapper.updateByActivityIdSelective(promoteSeminarActivityDetail);
				}
			}*/
			
			
			
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("submitActivityFeedBack"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryActivityFeedBack() {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			//合伙人超级管理
			WxTUser tuser = ContextUtil.getCurUser();
			List<WxTRole>  tUserRoleLst = tuser.getRoleList();
			boolean isOper = false;
			for(WxTRole tUserRole:tUserRoleLst)
			{
				if(PromoteRoleEnum.CAI_DEALER.getRoleCode().equals(tUserRole.getChRoleName()))//C&I经销商
				{
					reqMap.put("userId", ContextUtil.getCurUserId()); // TODO  放开
					isOper = true;
				}
			}
			
			//不包含，，，活动地促积分的
			log.info("queryActivityFeedBack regionName:{}",tuser.getRegionName());
			reqMap.put("activityType",PROMOTE_ACTIVITY_DCJF);
			reqMap.put("regionName",tuser.getRegionName());
			List<PromoteActivity> lst = promoteActiviMapper.queryActivityFeedBack(reqMap);
			for(PromoteActivity tmp:lst)
			{
				tmp.setEnableOpertion(isOper);//(isOper); TODO 放开
			}
			resultMap.put(RESULT_LST_KEY, lst);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryActivityFeedBack"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryActivityList(Long applyBatchId,
			String queryUserType) {
		log.info("queryActivityList applyBatchId:{}",applyBatchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			List<PromoteActivity> lst = promoteActiviMapper.getActivityLstByBatchId(applyBatchId);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put(RESULT_LST_KEY, lst);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryActivityList"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryPromoteOrders(String keyWord,String dateStartStr,String dateEndStr) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//默认没有操作权限
		resultMap.put("isUpdateOrderInfo", false);
		try
		{
			//查询所有的，针对仓库 角色
			Map<String,Object> reqMap = new HashMap<String,Object>();
			WxTUser tuser = ContextUtil.getCurUser();
			reqMap = promoteBizService.getQueryPromoteOrderMap(reqMap,keyWord,dateStartStr,dateEndStr);
			List<PromoteOrder> lst = promoteOrderMapper.getAllPromoteOrders(reqMap);
			
			//判断是否有  更新订单的权限
			Map<String, Object> promoteMarketingPower =  dicService.getDicItemByDicTypeCode("promote.order.power");
			List<DicItemVo> promoteMarketingItemlist = (ArrayList)promoteMarketingPower.get("data");
			String insertUsers = "";
			for(DicItemVo dicItem : promoteMarketingItemlist){
				if(dicItem.getDicItemCode().equals("updateInfo")){
					insertUsers = dicItem.getDicItemName();
					break;
				}
			}
			List<WxTRole> lstRole = tuser.getRoleList();
			for(WxTRole role:lstRole)
			{
				String roleName = role.getChRoleName();
				if(insertUsers.contains(roleName))
				{
					//设置有操作权限  维护订单的权限
					resultMap.put("isUpdateOrderInfo", true);
					break;
				}
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put(RESULT_LST_KEY, lst);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryPromoteOrders"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	

	@Override
	public Map<String, Object> queryPromoteOrderDetails(String promoteOrderNo) {
		log.info("queryPromoteOrderDetails promoteOrderNo:{}",promoteOrderNo);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		resultMap = queryPromoteOrderDetails(promoteOrderNo, false);
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryPromoteOrderDetails(String promoteOrderNo,
			boolean isQueryDetail) {
		log.info("queryPromoteOrderDetails promoteOrderNo:{},isQueryDetail:{}",promoteOrderNo,isQueryDetail);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//查询所有的，针对仓库 角色
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("promoteOrderNo", promoteOrderNo);
			//订单详情
			List<PromoteOrderDetail> lst = promoteOrderDetailMapper.getAllPromoteOrderDetail(reqMap);
			//获取物流详情，订单信息
			if(isQueryDetail)
			{
				//物流信息   如果后续要维护物流到什么情况的时候，需要重新设计一个物流表进行关联。。。
				List<PromoteOrderLogistics> orderLogistics = orderLogisticsMapper.getLogistics(promoteOrderNo);
				
				//订单头信息
				PromoteOrder promoteOrder = promoteOrderMapper.getPromoteOrder(reqMap);
				promoteOrder.setLstDetails(lst);
				promoteOrder.setLstOrderLogistics(orderLogistics);
				resultMap.put("promoteOrder", promoteOrder);
			}else
			{
				resultMap.put(RESULT_LST_KEY, lst);
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryPromoteOrderDetails"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	
	@Override
	public ResponseActivityInfoMobile getPromoteActivityInfoMobile(
			String activityUUId) throws Exception {
		ResponseActivityInfoMobile responseActivityInfoMobile =  promoteActiviMapper.getPromoteActivityInfoMobile(activityUUId);
		return responseActivityInfoMobile;
	}
	
	
	
	@Override
	public Map<String,Object> getActivityFeedBackDetail(Long activityId) {
		log.info("getActivityFeedBackDetail activityId:{}",activityId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//PromoteActivity promoteActivity = null;
		try
		{
			//promoteActivity = promoteActiviMapper.queryActivityFeedBackDetail(activityId);
			ResponseActivityFeedbackDetail responseActivityFeedbackDetail = promoteActivityFeedbackMapper.queryActivityFeedBackDetail(activityId);
			//获取公共活动图片
			if(!PROMOTE_ACTIVITY_YTH.equals(responseActivityFeedbackDetail.getActivityType()))
			{
				doGetCommonImgs(responseActivityFeedbackDetail);
			}
			//获取研讨会活动文件信息
			else
			{
				doGetYTHFilesInfo(responseActivityFeedbackDetail);
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put("responseActivityFeedbackDetail", responseActivityFeedbackDetail);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getActivityFeedBackDetail"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}



	private ResponseActivityFeedbackDetail doGetYTHFilesInfo(
			ResponseActivityFeedbackDetail responseActivityFeedbackDetail) throws Exception {
		log.info("doGetYTHFilesInfo start responseActivityFeedbackDetail:{}",responseActivityFeedbackDetail.toString());
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("sourceType", WxAttFile.SourceType_PromoteActivityFeedbacks2);
		//a14
		if(null!=responseActivityFeedbackDetail.getA14ApplyForm() && !responseActivityFeedbackDetail.getA14ApplyForm().isEmpty())
		{
			String[] a14files = responseActivityFeedbackDetail.getA14ApplyForm().split(",");
			reqMap.put("attIds", a14files);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setA14ApplyFormFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setA14ApplyFormImgLst(lstImages);
				
			}
		}
		//complianceInvoice
		if(null!=responseActivityFeedbackDetail.getComplianceInvoice() && !responseActivityFeedbackDetail.getComplianceInvoice().isEmpty())
		{
			String[] getComplianceInvoiceFiles = responseActivityFeedbackDetail.getComplianceInvoice().split(",");
			reqMap.put("attIds", getComplianceInvoiceFiles);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setComplianceInvoiceFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setComplianceInvoiceImgLst(lstImages);
				
			}
		}
		//conferenceSchedule
		if(null!=responseActivityFeedbackDetail.getConferenceSchedule() && !responseActivityFeedbackDetail.getConferenceSchedule().isEmpty())
		{
			String[] getConferenceScheduleFiles = responseActivityFeedbackDetail.getConferenceSchedule().split(",");
			reqMap.put("attIds", getConferenceScheduleFiles);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setConferenceScheduleFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setConferenceScheduleImgLst(lstImages);
				
			}
		}
		//checkInSlip
		if(null!=responseActivityFeedbackDetail.getCheckInSlip() && !responseActivityFeedbackDetail.getCheckInSlip().isEmpty())
		{
			String[] getCheckInSlipFiles = responseActivityFeedbackDetail.getCheckInSlip().split(",");
			reqMap.put("attIds", getCheckInSlipFiles);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setCheckInSlipFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setCheckInSlipImgLst(lstImages);
				
			}
		}
		//conferencePhoto
		if(null!=responseActivityFeedbackDetail.getConferencePhoto() && !responseActivityFeedbackDetail.getConferencePhoto().isEmpty())
		{
			String[] getConferencePhoto = responseActivityFeedbackDetail.getConferencePhoto().split(",");
			reqMap.put("attIds", getConferencePhoto);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setConferencePhotoFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setConferenceScheduleImgLst(lstImages);
				
			}
		}
		//hotelFeeStatement
		if(null!=responseActivityFeedbackDetail.getHotelFeeStatement() && !responseActivityFeedbackDetail.getHotelFeeStatement().isEmpty())
		{
			String[] getHotelFeeStatement = responseActivityFeedbackDetail.getHotelFeeStatement().split(",");
			reqMap.put("attIds", getHotelFeeStatement);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setHotelFeeStatementFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setHotelFeeStatementImgLst(lstImages);
				
			}
		}
		//tripartiteAgreement
		if(null!=responseActivityFeedbackDetail.getTripartiteAgreement() && !responseActivityFeedbackDetail.getTripartiteAgreement().isEmpty())
		{
			String[] getTripartiteAgreement = responseActivityFeedbackDetail.getTripartiteAgreement().split(",");
			reqMap.put("attIds", getTripartiteAgreement);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setTripartiteAgreementFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setTripartiteAgreementImgLst(lstImages);
				
			}
		}
		//proofPurchase
		if(null!=responseActivityFeedbackDetail.getProofPurchase() && !responseActivityFeedbackDetail.getProofPurchase().isEmpty())
		{
			String[] getProofPurchase = responseActivityFeedbackDetail.getProofPurchase().split(",");
			reqMap.put("attIds", getProofPurchase);
			List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
			if(null!=lstResponseFile && !lstResponseFile.isEmpty())
			{
				
				Map<String,Object> retrunMap = doHandleFileOrImg(lstResponseFile);
				List<ResponseFeedBackFile> lstFile = (List<ResponseFeedBackFile>) retrunMap.get("files");
				responseActivityFeedbackDetail.setProofPurchaseFileLst(lstFile);
				
				List<ResponseFeedBackFile> lstImages = (List<ResponseFeedBackFile>) retrunMap.get("images");
				responseActivityFeedbackDetail.setProofPurchaseImgLst(lstImages);
				
			}
		}
		log.info("doGetYTHFilesInfo end responseActivityFeedbackDetail:{}",responseActivityFeedbackDetail.toString());
		return responseActivityFeedbackDetail;
	}



	private Map<String, Object> doHandleFileOrImg(
			List<ResponseFeedBackFile> lstResponseFile)throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//获取文件
		List<ResponseFeedBackFile> fileLst = new ArrayList<ResponseFeedBackFile>();
		//获取图片
		List<ResponseFeedBackFile> imgLst = new ArrayList<ResponseFeedBackFile>();
		for(ResponseFeedBackFile responseFeedBackFile:lstResponseFile)
		{
			if(responseFeedBackFile.getFileType().startsWith("image"))
			{
				imgLst.add(responseFeedBackFile);
			}else
			{
				fileLst.add(responseFeedBackFile);
			}
		}
		resultMap.put("files", fileLst);
		resultMap.put("images", imgLst);
		return resultMap;
	}



	private ResponseActivityFeedbackDetail doGetCommonImgs(
			ResponseActivityFeedbackDetail responseActivityFeedbackDetail)throws Exception {
		log.info("doGetYTHFilesInfo doGetCommonImgs:{}",responseActivityFeedbackDetail.toString());
		if(null==responseActivityFeedbackDetail.getActivityFeedBackImgIds() 
				|| responseActivityFeedbackDetail.getActivityFeedBackImgIds().isEmpty())
		{
			return responseActivityFeedbackDetail;
		}
		String[] commonImgFiles = responseActivityFeedbackDetail.getActivityFeedBackImgIds().split(",");
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("attIds", commonImgFiles);
		reqMap.put("sourceType", WxAttFile.SourceType_PromoteActivityFeedbacks);
		reqMap.put("fileType", "image");
		List<ResponseFeedBackFile> lstResponseFile = promoteActivityFeedbackMapper.queryFilesByList(reqMap);
		responseActivityFeedbackDetail.setCommonImgFileLst(lstResponseFile);
		return responseActivityFeedbackDetail;
	}
	
	
	@Override
	public Map<String, Object> getPacksDetailInfo() {
		log.info("getPacksDetailInfo start");
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Long sourcePlanId = customerRegionUser.getSourcePlanId();
			ResponseActivityPacksDetail responseActivityPacks =  promoteBizService.getPacksDetailInfo(null,sourcePlanId);
			resultMap.put("responseActivityPacks", responseActivityPacks);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			/*CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("AttSourceType", WxAttFile.SourceType_PromoteMaterial);
			reqMap.put("sourcePlanId", customerRegionUser.getSourcePlanId());
			if(null!=filterPacks && !filterPacks.isEmpty())
			{
				reqMap.put("filterPacks", filterPacks);
			}
			List<ResponsePacksDetail> lst =  planPacksDetailMapper.queryActivityPacksDetailBySourcePlanId(reqMap);
			String lastPacksCode = "";
			String currentPacksCode = "";
			List<ResponsePacksDetail> tmpLst = null; 
			Map<String,Object> tmpMap = new HashMap<String,Object>();
			for(ResponsePacksDetail tmp: lst)
			{
				currentPacksCode = tmp.getPacksCode();
				if(!lastPacksCode.equals(currentPacksCode))
				{
					tmpLst = new ArrayList<ResponsePacksDetail>();
					
				}
				tmpLst.add(tmp);
				tmpMap.put(currentPacksCode, tmpLst);
				lastPacksCode = currentPacksCode;
			}
			//新店开业
			ResponseActivityPacksDetail responseActivityPacks = new ResponseActivityPacksDetail();
			responseActivityPacks.setGdPacksLst((List<ResponsePacksDetail> )tmpMap.get(PROMOTE_ACTIVITY_GDLB));
			responseActivityPacks.setRdPacksLst((List<ResponsePacksDetail> )tmpMap.get(PROMOTE_ACTIVITY_RDLB));
			responseActivityPacks.setConsumerInteractionPacksLst((List<ResponsePacksDetail> )tmpMap.get(PROMOTE_ACTIVITY_XFZHDBCB));
			responseActivityPacks.setRoadshowPacksLst((List<ResponsePacksDetail> )tmpMap.get(PROMOTE_ACTIVITY_LYWLB));
			responseActivityPacks.setYthPacksLst((List<ResponsePacksDetail> )tmpMap.get(PROMOTE_ACTIVITY_TGHWLB));*/
			
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPacksDetailInfo error:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> getPacksDetailInfo(Long activityId) {
		log.info("getPacksDetailInfo start activityId:{}",activityId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			PromoteActivity promoteActivity =  promoteActiviMapper.selectByPrimaryKey(activityId);
			ResponseActivityPacksDetail responseActivityPacks =  promoteBizService.getPacksDetailInfo(null,promoteActivity.getSourcePlanId());
			resultMap.put("responseActivityPacks", responseActivityPacks);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPacksDetailInfo error:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertLogisticsInfo(PromoteOrder promoteOrder) {
		log.info("insertLogisticsInfo start promoteOrder:{}",promoteOrder);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			if(null==promoteOrder)
			{
				throw new Exception(PROMOTE_PARMAS_NULL);
			}
			promoteOrder.setLogisticsStatus("1");
			String remark = promoteOrder.getRemark();
			promoteOrder.setRemark(null);
			promoteOrderMapper.updateByPrimaryKeySelective(promoteOrder);
			//录入到订单物流表中
			PromoteOrderLogistics promoteOrderLogistics = new PromoteOrderLogistics();
			promoteOrderLogistics.setLogisticsCompany(promoteOrder.getLogisticsCompany());
			promoteOrderLogistics.setLogisticsNo(promoteOrder.getLogisticsNo());
			promoteOrderLogistics.setLogisticsStatus(promoteOrder.getLogisticsStatus());
			promoteOrderLogistics.setOrderNo(promoteOrder.getOrderNo());
			promoteOrderLogistics.setLogisticsDetail(remark);
			promoteOrderLogistics.setLogisticsTime(new Date());
			orderLogisticsMapper.insertSelective(promoteOrderLogistics);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("insertLogisticsInfo error:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> checkActivityAmount(String activityType,PromoteActivity promoteActivity) {
		log.info("checkActivityAmount activityType:{},promoteActivity:{},status:{}",activityType,promoteActivity);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			Map<String,Object> returnMap = getPromotePacksAmountInfo();
			PromotePacksAmountInfo promotePacksAmountInfo = (PromotePacksAmountInfo)returnMap.get("promotePacksAmountInfo");
			Double totalAmount = 0.0d;
			if(PROMOTE_ACTIVITY_DLXDKY.equals(activityType))
			{
				totalAmount+=promotePacksAmountInfo.getRdlbAmount()*(promoteActivity.getPromoteXDOpenDetail().getPacksNumber()==null?0
						:promoteActivity.getPromoteXDOpenDetail().getPacksNumber());
				totalAmount+=promotePacksAmountInfo.getGdlbAmount()*(promoteActivity.getPromoteXDOpenDetail().getGdPacksNumber()==null?0
						:promoteActivity.getPromoteXDOpenDetail().getGdPacksNumber());
			}else if(PROMOTE_ACTIVITY_DLLY.equals(activityType))
			{
				totalAmount+=promotePacksAmountInfo.getLywlbAmount()*
						(promoteActivity.getPromoteRoadshowActivityDetail().getRoadShowGifPackageCount()==null?0
								:promoteActivity.getPromoteRoadshowActivityDetail().getRoadShowGifPackageCount());
				totalAmount+=promotePacksAmountInfo.getXfzhdbcbAmount()*
						(promoteActivity.getPromoteRoadshowActivityDetail().getRoadShowConsumerPacksCount()==null?0
								:promoteActivity.getPromoteRoadshowActivityDetail().getRoadShowConsumerPacksCount());
				
			}else if(PROMOTE_ACTIVITY_YTH.equals(activityType))
			{
				totalAmount+=promotePacksAmountInfo.getTghwlbAmount()*(promoteActivity.getPromoteSeminarActivityDetail().getApplyPacksCount()==null?0
						:promoteActivity.getPromoteSeminarActivityDetail().getApplyPacksCount());
				log.info("tghpacks total amount:{}",totalAmount);
				
				totalAmount+=promotePacksAmountInfo.getYzythlbAmount()*(promoteActivity.getPromoteSeminarActivityDetail().getApplyHighPacks()==null?0
						:promoteActivity.getPromoteSeminarActivityDetail().getApplyHighPacks());
				log.info("yzythpacks total amount:{}",totalAmount);	
			}else if(PROMOTE_ACTIVITY_DCJF.equals(activityType))
			{
				totalAmount+=promoteActivity.getPromoteRegionalDetail().getApplyPromotionSupportPoints();
			} else if(PROMOTE_ACTIVITY_DZHD.equals(activityType)) {
				totalAmount+=promotePacksAmountInfo.getDzlbAmount()*(promoteActivity.getPromoteShopSignsDetail().getPacksNumber()==null?0
						:promoteActivity.getPromoteShopSignsDetail().getPacksNumber());
			} else if(PROMOTE_ACTIVITY_HWGG.equals(activityType)) {
				totalAmount+=promotePacksAmountInfo.getHwgglbAmount()*(promoteActivity.getPromoteAdvertisementDetail().getPacksNumber()==null?0
						:promoteActivity.getPromoteAdvertisementDetail().getPacksNumber());
			} else if(PROMOTE_ACTIVITY_NKKSGCJX.equals(activityType)) {
				totalAmount+=promotePacksAmountInfo.getNklbAmount()*(promoteActivity.getPromoteAgricultureDetail().getPacksNumber()==null?0
						:promoteActivity.getPromoteAgricultureDetail().getPacksNumber());
			} else if(PROMOTE_ACTIVITY_SYYP.equals(activityType)) {
				totalAmount+=promotePacksAmountInfo.getSylbAmount()*(promoteActivity.getPromoteTryOilDetail().getPacksNumber()==null?0
						:promoteActivity.getPromoteTryOilDetail().getPacksNumber());
			}
			
			
			
			log.info("checkActivityAmount totalAmount:{},promoteBizService.getActivityAmountLimit():{} ",totalAmount,promoteBizService.getActivityAmountLimit());
			if(totalAmount>promoteBizService.getActivityAmountLimit())
			{
				resultMap.put(RESULT_ERROR_MSG_KEY, PROMOTE_ACTIVITY_AMOUNT_EXCESS_TIP);
			}
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("checkActivityAmount error:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> countPacksUsageInfo(String promoteUserType) {
		log.info("countPacksUsageInfo promoteUserType:{}",promoteUserType);
		Map<String, Object> resultMap = new HashMap<String,Object>();
		try
		{
			CountUsedPacksInfo countUsedPacksInfo = new CountUsedPacksInfo();
			//获取当前用户信息	
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("userId", customerRegionUser.getUserId());
			reqMap.put("sourcePlanId", customerRegionUser.getSourcePlanId());
			if(PROMOTE_SALES.equals(promoteUserType))
			{
				//计算总的
				//获取当前用户总可申请的礼包数量
				PromoteDistribution promoteDistribution = doGetTotalPacks(customerRegionUser);
				if(null==promoteDistribution)
				{
					resultMap.put("countUsedPacksInfo", countUsedPacksInfo);
					resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
					return resultMap;
				}
				int totalGDLB = promoteDistribution.getOpenShopGdPacksDistributionCount()==null?
						0:promoteDistribution.getOpenShopGdPacksDistributionCount();
				int totalRDLB = promoteDistribution.getOpenShopPacksDistributionCount()==null?
						0:promoteDistribution.getOpenShopPacksDistributionCount();
				int totalLYWLB = promoteDistribution.getRoadShowActiviPacksDistributionCount()==null?
						0:promoteDistribution.getRoadShowActiviPacksDistributionCount();
				int totalXFZHDBCB = promoteDistribution.getRoadShowConsumerPacksDistributionCount()==null?
						0:promoteDistribution.getRoadShowConsumerPacksDistributionCount();
				int totalTGHWLB = promoteDistribution.getSeminarPacksDistributionCount()==null?
						0:promoteDistribution.getSeminarPacksDistributionCount();
				int totalDCJF = promoteDistribution.getPointsDistributionCount()==null?
						0:promoteDistribution.getPointsDistributionCount();
				Double totalVenueMeal = promoteDistribution.getVenueMealDistribution()==null?
						0.0:promoteDistribution.getVenueMealDistribution();
				int totalYZYTH = promoteDistribution.getSeminarHighPacksDistributionCount()==null?0:promoteDistribution.getSeminarHighPacksDistributionCount();
				int totalDZ = promoteDistribution.getStorePacksDistributionCount()==null?0:promoteDistribution.getStorePacksDistributionCount();
				int totalHWGG = promoteDistribution.getAdvertPacksDistributionCount()==null?0:promoteDistribution.getAdvertPacksDistributionCount();
				int totalNK = promoteDistribution.getAgriculturePacksDistributionCount()==null?0:promoteDistribution.getAgriculturePacksDistributionCount();
				int totalSY = promoteDistribution.getTryPacksDistributionCount()==null?0:promoteDistribution.getTryPacksDistributionCount();
				
				countUsedPacksInfo.setTotalDCJFACT(totalDCJF);
				countUsedPacksInfo.setTotalGDLB(totalGDLB);
				countUsedPacksInfo.setTotalLYWLB(totalLYWLB);
				countUsedPacksInfo.setTotalRDLB(totalRDLB);
				countUsedPacksInfo.setTotalTGHWLB(totalTGHWLB);
				countUsedPacksInfo.setTotalXFZHDBCB(totalXFZHDBCB);
				countUsedPacksInfo.setTotalVenueMeal(totalVenueMeal);
				countUsedPacksInfo.setTotalYZYTHLB(totalYZYTH);
				countUsedPacksInfo.setTotalDZLB(totalDZ);
				countUsedPacksInfo.setTotalHWGGLB(totalHWGG);
				countUsedPacksInfo.setTotalNKLB(totalNK);
				countUsedPacksInfo.setTotalSYLB(totalSY);
				
				// 计算已经用了的
				doGetUsedPacks(reqMap,countUsedPacksInfo);
				
			}
			resultMap.put("countUsedPacksInfo", countUsedPacksInfo);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("countPacksUsageInfo"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}



	private CountUsedPacksInfo doGetUsedPacks(Map<String, Object> reqMap,
			CountUsedPacksInfo countUsedPacksInfo)throws Exception {
		log.info("doGetUsedPacks countUsedPacksInfo:{}",countUsedPacksInfo.toString());
		//新店开业  入店礼包，攻店礼包
		PromoteXDOpenDetail promoteXDOpenDetail = promoteBizService.getHasApplyXDOpenPacks(reqMap);
		if(null!=promoteXDOpenDetail)
		{
			countUsedPacksInfo.setUsedRDLB(promoteXDOpenDetail.getPacksNumber()==null?
					0:promoteXDOpenDetail.getPacksNumber());
			countUsedPacksInfo.setUsedGDLB(promoteXDOpenDetail.getGdPacksNumber()==null?
					0:promoteXDOpenDetail.getGdPacksNumber());
			
		}
		
		//路演     路演物料礼包，路演消费者互动礼包
		PromoteRoadshowActivityDetail roadShowDetail =  promoteBizService.getHasApplyRoadshowPacks(reqMap);
		if(null!=roadShowDetail)
		{
			countUsedPacksInfo.setUsedLYWLB(roadShowDetail.getRoadShowGifPackageCount()==null?
					0:roadShowDetail.getRoadShowGifPackageCount());
			countUsedPacksInfo.setUsedXFZHDBCB(roadShowDetail.getRoadShowConsumerPacksCount()==null?
					0:roadShowDetail.getRoadShowConsumerPacksCount());
		}
		//推广会
		PromoteSeminarActivityDetail seminarDetail =  promoteBizService.getHasApplySeminarPacks(reqMap);
		if(null!=seminarDetail)
		{
			countUsedPacksInfo.setUsedTGHWLB(seminarDetail.getApplyPacksCount()==null?
					0:seminarDetail.getApplyPacksCount());
			countUsedPacksInfo.setUsedDCJFACT(seminarDetail.getApplyPoints()==null?
					0:seminarDetail.getApplyPoints());
			countUsedPacksInfo.setUsedVenueMeal(seminarDetail.getVenueMeal()==null?
					0.0:seminarDetail.getVenueMeal());
			countUsedPacksInfo.setUsedYZYTHLB(seminarDetail.getApplyHighPacks()==null?0:seminarDetail.getApplyHighPacks());
		}
		
		//店招
		PromoteShopSignsDetail shopSignsDetail = promoteBizService.getHasApplyDzPacks(reqMap);
		if(!EmptyChecker.isEmpty(shopSignsDetail)) {
			countUsedPacksInfo.setUsedDZLB(shopSignsDetail.getPacksNumber()==null?0:shopSignsDetail.getPacksNumber());
		}
		
		//户外广告
		PromoteAdvertisementDetail advertisementDetail = promoteBizService.getHasApplyHwggPacks(reqMap);
		if(!EmptyChecker.isEmpty(advertisementDetail)) {
			countUsedPacksInfo.setUsedHWGGLB(advertisementDetail.getPacksNumber()==null?0:advertisementDetail.getPacksNumber());
		}
		
		//农垦
		PromoteAgricultureDetail agricultureDetail = promoteBizService.getHasApplyNkPacks(reqMap);
		if(!EmptyChecker.isEmpty(agricultureDetail)) {
			countUsedPacksInfo.setUsedNKLB(agricultureDetail.getPacksNumber()==null?0:agricultureDetail.getPacksNumber());
		}
		
		//试用
		PromoteTryOilDetail tryOilDetail = promoteBizService.getHasApplySyPacks(reqMap);
		if(!EmptyChecker.isEmpty(tryOilDetail)) {
			countUsedPacksInfo.setUsedSYLB(tryOilDetail.getPacksNumber()==null?0:tryOilDetail.getPacksNumber());
		}
		//地促积分
		/*PromoteRegionalDetail pointsDetail = promoteBizService.getHasApplyPoints(reqMap);
		if(null!=pointsDetail)
		{
			countUsedPacksInfo.setUsedDCJFACT(pointsDetail.getApplyPromotionSupportPoints()==null?
					0:pointsDetail.getApplyPromotionSupportPoints());
		}*/
		return countUsedPacksInfo;
	}
	
}

