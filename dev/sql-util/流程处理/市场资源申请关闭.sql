 
 select *
--update p set p.permission_weight=0,p.remark='公共流程申请权限。256-店招申请'
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
where module_code='WorkflowInstance.todoPage' and p.remark='公共流程申请权限。256-店招申请' and permission_weight&256>0

select *
--update p set p.permission_weight=0,p.remark='公共流程申请权限。512-研讨会申请'
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
where module_code='WorkflowInstance.todoPage' and p.remark='公共流程申请权限。512-研讨会申请' and permission_weight&512>0

select *
--update p set p.permission_weight=0
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
where module_code='WorkflowInstance.todoPage' and p.remark='公共流程申请权限。4096-地区促销申请' and permission_weight&4096>0

--select a.* into  wx_t_place_promotion_apply20221115
--update wi set wi.delete_flag=1
from wx_t_place_promotion_apply a
left join wx_t_workflow_instance wi on wi.form_key=a.id and wi.workflow_code='PLACE_PROMOTION_APPLY'
 where a.form_status=0
 
delete a
from wx_t_place_promotion_apply a
left join wx_t_workflow_instance wi on wi.form_key=a.id and wi.workflow_code='PLACE_PROMOTION_APPLY'
 where a.form_status=0
 
 --select a.* into  wx_t_mkt_signage_apply20221115
--update wi set wi.delete_flag=1
from wx_t_mkt_signage_apply a
left join wx_t_workflow_instance wi on wi.form_key=a.id and wi.workflow_code='SIGNAGE_APPLY'
 where a.form_status=0 
 
delete a
from wx_t_mkt_signage_apply a
left join wx_t_workflow_instance wi on wi.form_key=a.id and wi.workflow_code='SIGNAGE_APPLY'
 where a.form_status=0 
 
  select a.* into  wx_t_mkt_seminar_apply20221115
--update wi set wi.delete_flag=1
from wx_t_mkt_seminar_apply a
left join wx_t_workflow_instance wi on wi.form_key=a.id and wi.workflow_code='SEMINAR_APPLY'
 where a.form_status=0 
 
select a.*
--delete a
from wx_t_mkt_seminar_apply a
left join wx_t_workflow_instance wi on wi.form_key=a.id and wi.workflow_code='SEMINAR_APPLY'
 where a.form_status=0 
