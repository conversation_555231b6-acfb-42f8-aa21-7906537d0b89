<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<!DOCTYPE html>
<%@page import="com.common.util.ContextUtil"%>
<%@page import="com.sys.auth.model.WxTUser"%><html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>门店管理</title>
<%@include file="/common/jsp/common.jsp"%>
<script type='text/javascript' src="${ctx }partnerorder/partnerinventory/js/partnerworkshoponwaystock.js?v=20170427"></script>
<link href="${ctx }business/workshop/css/upload.css" rel="stylesheet" type="text/css" />
<script>
	var userType = '<%=WxTUser.USER_MODEL_CHEVRON.equals(ContextUtil.getCurUser().getUserModel()) ? 1 : 0 %>',
	partnerId = '${param.partnerId}';
	onwaycapacityBigerThanZero = ${empty param.onwaycapacityBigerThanZero ? 'fasle' : param.onwaycapacityBigerThanZero};
</script>
</head>
<style type="text/css">
</style>

<body class="gray-bg">
	<div class="content-wrapper">
		<div class="content-panel header-panel">
    		<div class="header-title" id="headerTitle">合伙人门店在途库存</div>
    		<div class="header-btns">
            	<button class="btn-back" onclick="window.location='${ctx }partnerorder/partnerinventory/partnerinventoryPage.jsp?cacheParams=true';" id="spOrderBtn">返回</button>
    		</div>
    	</div>
		<div class="content-panel" id="workshop_grid"></div>
	</div>
</body>

</html>