package com.common.util.json;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

import com.common.util.StringUtils;
import com.sys.auth.model.LigerUITreeNode;
import com.sys.auth.model.TreeNode;
import com.sys.permission.model.BaseDataPermissionTree;
import com.chevron.task.model.WxTaskmbCheck;
/**
 * Java对象转换为Json子串的接口
 * <AUTHOR>
 * @version 2012-8-14
 */
public class JsonGenerator
{
	public static  List<WxTaskmbCheck> checkListToTree(List<WxTaskmbCheck> list){
		List<WxTaskmbCheck> nodeList = new ArrayList<WxTaskmbCheck>();
		for(WxTaskmbCheck node1 : list){
			boolean mark = false;
			for(WxTaskmbCheck node2 : list){
				if(node1.getCheckPid().compareTo(node2.getCheckId())==0){
					mark = true;
					if(node2.getChildren() == null){
						node2.setChildren(new ArrayList<TreeNode>());
					}
					node2.getChildren().add(node1);
					break;
				}
			}
			if(!mark){
				nodeList.add(node1);
			}
		}
		return nodeList;
	}

	// 把TreeNode list封装成树结构list
	@SuppressWarnings("unchecked")
	public static <E extends TreeNode> List<E> listToTree(List<E> list){
		List<TreeNode> nodeList = new ArrayList<TreeNode>();
		for(TreeNode node1 : list){
			if(node1.getPid() == null){
				nodeList.add(node1);
				continue;
			}
			boolean mark = false;
			for(TreeNode node2 : list){
				if(node1.getPid().equals(node2.getId())){
					mark = true;
					if(node2.getChildren() == null){
						node2.setChildren(new ArrayList<TreeNode>());
					}
					node2.getChildren().add(node1);
					break;
				}
			}
			if(!mark){
				nodeList.add(node1);
			}
		}
		return (List<E>) nodeList;
	}
	
	// 把dataTreeNode list封装成树结构list
	@SuppressWarnings("unchecked")
	public static <E extends BaseDataPermissionTree> List<E> datalistToTree(List<E> list){
		List<BaseDataPermissionTree> nodeList = new ArrayList<BaseDataPermissionTree>();
		for(BaseDataPermissionTree node1 : list){
			if(node1.getPid() == null){
				nodeList.add(node1);
				continue;
			}
			boolean mark = false;
			for(BaseDataPermissionTree node2 : list){ 
				if(node1.getPid().equals(node2.getId()) && node2.getDataType() == 1){
					mark = true;
					if(node2.getChildren() == null){
						node2.setChildren(new ArrayList<BaseDataPermissionTree>());
					}
					node2.getChildren().add(node1);
					break;
				}
			}
			if(!mark){
				nodeList.add(node1);
			}
		}
		return (List<E>) nodeList;
	}

	// 把LigerUITreeNode list封装成树结构list
	public static  List<LigerUITreeNode> listToLigerUITree(List<LigerUITreeNode> list){
		List<LigerUITreeNode> nodeList = new ArrayList<LigerUITreeNode>();
		for(LigerUITreeNode node1 : list){
			boolean mark = false;
			for(LigerUITreeNode node2 : list){
				try{
					if(node1.getPid()!=null){
						if(node1.getPid().equals(node2.getId())){
							mark = true;
							if(node2.getChildren() == null){
								node2.setChildren(new ArrayList<LigerUITreeNode>());
							}
							node2.getChildren().add(node1);
							break;
						}
					}
					
				}catch(Exception ex){
					ex.printStackTrace();
				}
				
			}
			if(!mark){
				nodeList.add(node1);
			}
		}
		for (LigerUITreeNode node : nodeList) {
			if (null == node.getChildren() || node.getChildren().size() == 0) {
				node.setLeaf(true);
			}
		}
		return nodeList;
	}

	/**
	 * 删除所有孤儿节点(找不到父节点的)
	 * 
	 * @param list
	 * @return
	 * <AUTHOR> Yu
	 * @version 2015-07-06
	 */
	public static List<TreeNode> removeOrphanForTreeList(List<TreeNode> list) {
		List<TreeNode> nodeList = new ArrayList<TreeNode>();
		for (TreeNode node1 : list) {
			boolean mark = false;
			for (TreeNode node2 : list) {
				if (node1.getPid().equals(node2.getId())) {
					mark = true;
					break;
				}
			}
			if (mark) {
				nodeList.add(node1);
			}
		}
		return nodeList;
	}

	/**
	 * 生成树的Json格式字符串
	 * 
	 * @param objList
	 * @return
	 * <AUTHOR>
	 * @version 2012-8-28
	 */
	public static String getJsonContent(List<?> objList) {
		String json = JSONArray.fromObject(objList).toString();
		return json;
	}
	/**
	 * 获取 GridJson
	 * @param resource
	 * @param total
	 * @return
	 * <AUTHOR>
	 * @version 2012-8-28
	 */
	public static String getGridTableJsonContent(List<?> resource, int total)
	{
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("Rows", resource);
		map.put("Total", total);
		//生成Json串
		String json = JSONArray.fromObject(map).toString();
		//删除Json串前后"[]"
		json = StringUtils.cutFirstAndLastChar(json);
		return json;
	}
	
	public static String getObjectJsonContent(Object obj)
	{
		//生成Json串
		String json = JSONArray.fromObject(obj).toString();
		
		return json;
	}
	
	@SuppressWarnings("unchecked")
	public static String getObjectJsonObjectContent(Object obj)
	{
		JsonConfig jsonConfig = new JsonConfig();
		jsonConfig.registerDefaultValueProcessor(Integer.class, new net.sf.json.processors.DefaultDefaultValueProcessor() {     
            public Object getDefaultValue(Class type) {     
                return null;
            }
        });
		jsonConfig.registerDefaultValueProcessor(Long.class, new net.sf.json.processors.DefaultDefaultValueProcessor() {     
            public Object getDefaultValue(Class type) {     
                return null;
            }
        });
		//生成Json串
		String json = JSONObject.fromObject(obj, jsonConfig).toString();
		
		return json;
	}
	
/*	public static  List<TaskInstanceCheckVo> instaceCheckListToTree(List<TaskInstanceCheckVo> list){
		List<TaskInstanceCheckVo> nodeList = new ArrayList<TaskInstanceCheckVo>();
		for(TaskInstanceCheckVo node1 : list){
			boolean mark = false;
			for(TaskInstanceCheckVo node2 : list){
				System.out.println("=================="+node1.getStepId()+"-----------"+node2.getStepId());
				if(node1.getStepId() == node2.getStepId())
				{
					mark = true;
					if(node2.getChildren() == null){
						node2.setChildren(new ArrayList<TreeNode>());
					}
					node2.getChildren().add(node1);
					break;
				}
			}
			if(!mark){
				nodeList.add(node1);
			}
		}
		return nodeList;
	}
*/
	
}
