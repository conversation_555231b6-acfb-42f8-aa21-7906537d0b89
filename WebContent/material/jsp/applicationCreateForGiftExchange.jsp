<%@page import="net.sf.json.JSONObject"%>
<%@page import="java.util.Map"%>
<%@page import="com.chevron.point.dto.PointType"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.common.util.ContextUtil"%>
<%@ page import="com.sys.auth.model.WxTUser"%>
<%@ page import="com.chevron.material.util.ApplicationUtil"%>
<!DOCTYPE html>
<%
    String id =  request.getParameter("id");
    Long curUserId = ContextUtil.getCurUser().getUserId();
    Long userType = ContextUtil.getCurUser().getmUserTypes();
    Long orgId = ContextUtil.getCurUser().getOrgId();
    String bizTypePara = request.getParameter("bizType");
    String bizType = (bizTypePara == null || "null".equals(bizTypePara)) ? null:bizTypePara;
    String appType = request.getParameter("pointType");
    String appCreateTitle = ApplicationUtil.getApplicationCreateTitle(appType);
    PointType pointType = PointType.getPointTypeByApplicationType(appType);
    Map<String, Object> orderConfig = null;
    if(pointType != null){
        orderConfig = pointType.getOrderConfig();
    }
%>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>促销礼品兑换</title>
    <%@include file="/common/jsp/common.jsp"%>
    <c:set var="bizType" value="<%=bizType%>" />
    <c:set var="orderConfig" value="<%=orderConfig%>" />
    <c:set var="pointTitle" value='<%=ApplicationUtil.getExchangeType(request.getParameter("pointType")) %>'/>
    <c:set var="curUserId" value="<%=curUserId %>"/>
    <c:set var="id" value="<%=id %>"/>
    <script type="text/javascript" src="${ctx }common/build-bui/config.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }common/build-bui/sea.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }material/js/cityCascadeMenu.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }material/js/applicationCreateForGiftExchange.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }material/js/material_common.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }material/js/jquery.fly.min.js?v=${version}"></script>
    <script type="text/javascript">
        var userType =<%=userType%>;
        var orgId =<%=orgId%>;
        var curUserId = <%=curUserId%>;
        var isSPuser = <%=WxTUser.USER_MODEL_SP.equals(ContextUtil.getCurUser().getUserModel())%>;
        var applicationId = <%=request.getParameter("applicationId")%>;
        var id = <%=request.getParameter("id")%>;
        var pointMode = ${param.pointMode == true} && ${param.fromSource == null};
        var pointModeBase = ${param.pointMode == true};
        var pointType = '${param.pointType}';
        var bizType = <%=(bizType==null || bizType.equals("NOT_CALTEX_POINT_FROM_PROMOTE"))?null:"'"+bizType+"'"%>;
        var appCreateTitle = '<%=appCreateTitle%>';
        var pointTitle = '${pointTitle}';
        var orderConfig = <%=orderConfig == null ? null : JSONObject.fromObject(orderConfig).toString() %>;
        var tabPermission = ${param.tabPermission == true};
        var fromSource = '${param.fromSource}';
        var couponInfo = '${param.couponInfo}';//如果是优惠券，优惠券信息肯定有值
    </script>
    <link href="${ctx }material/css/material.css?v=${version}6" rel="stylesheet">
    <style type="text/css">
        #selectWorkshop{
            height:30px!important
        }
        #workshop_information .control-text{
            padding:0px!important
        }
        .material-list-size .material-sku{
            display: block;
            overflow: hidden;
            margin-bottom: 5px;
        }
        .material-list-size .material-size{
            display: block;
            overflow: hidden;
        }

        .material-list-size .material-sku .active{
            background-color: #1c84c6;
            border-color: #1c84c6;
            color: #ffffff;
        }
        .material-list-size .material-size .active{
            background-color: #1c84c6;
            border-color: #1c84c6;
            color: #ffffff;
        }
        .material-list-size .material-size .disabled{
            background-color: #efefef;
            border-color: #efefef;
            color: #999;
        }

        .material-list-size .material-sku .disabled{
            background-color: #efefef;
            border-color: #efefef;
            color: #999;
        }
    </style>
</head>
<body class="gray-bg">
<div class="content-wrapper">
    <c:if test="${param.tabPermission == true}" >
        <c:choose>
            <c:when test="${param.prePage == 'applicationNew'}">
                <jsp:include page="tabs/materialApplicationTab.jsp"/>
            </c:when>
            <c:when test="${param.prePage == 'approvalNew'}">
                <jsp:include page="tabs/materialApproveTab.jsp"/>
            </c:when>
        </c:choose>
    </c:if>
    <div class="first-step" id="materialSelection">
        <div class="content-panel header-panel" >
            <c:if test="${param.pointMode}" >
                <div id="pageTitle" class="header-title">
                    礼品兑换
<%--                    <%=appCreateTitle%>--%>
<%--                    <span id="totalPointDiv">--%>
<%--							<span style="font-size: 16px;padding: 0 0 10px 50px;line-height: 30px;">可用${pointTitle}--%>
<%--								：--%>
<%--							</span>--%>
<%--							<span class="summary-point-price" style="color:red;font-size: 16px;font-weight:bold;" id="totalPoint"></span>--%>
<%--						</span>--%>
                </div>
            </c:if>
            <c:if test="${param.pointMode != true}" >
                <div id="pageTitle" class="header-title">礼品兑换</div>
            </c:if>
            <div class="header-btns hide">
                <button id="push2ApprovalBtn" class='btn-create' onclick="application_create_widget.showDialog({'type':'2'})">提交</button>
<%--                <button id="saveApplicationBtn" class='btn-update hide' onclick="application_create_widget.showDialog({'type':'1'})" >暂存</button>--%>
<%--                <c:if test="${param.pointMode != true}" >--%>
<%--                    <button id="push2ApprovalBtn" class='btn-create' onclick="application_create_widget.showDialog({'type':'2'})">提交</button>--%>
<%--                </c:if>--%>
<%--                <c:if test="${param.pointMode}" >--%>
<%--                    <button id="push2ApprovalBtn" class='btn-create' onclick="application_create_widget.showDialog({'type':'2'})">结算</button>--%>
<%--                </c:if>--%>
                <button id="closeCreateBtn"  class="btn-cancel" onclick="window.history.go(-1);">返回</button>
            </div>
<%--            <c:if test="${param.pointMode != true}" >--%>
<%--                <div class="page-comment" id="newApplicationBtnTip">注：物料的可申请数量和SP采购机油产品订单量成一定比例。且每个SP每个月只能申请一次物料，如有更多需求，请联系雪佛龙销售负责人。</div>--%>
<%--            </c:if>--%>
        </div>
        <div class="content-panel material-list-page-wrapper">
            <div class="content-panel query-panel">
                <div class="field-group key-search">
                    <div class="control-group">
                        <div class="fuzzy-search-div">
                            <input type="text" id="keyword" name="keyword" placeholder="输入任意关键字，可直接回车搜索" class="control-text"/>
                            <button onclick="application_create_widget.search()" class="btn-query col-group"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                </div>
                <div class="field-group pull-right" style="">
                    <div class="control-group pull-right">
                        <div class="selection-view-mode-group">
                            <button type="button"class="btn fixed-right-btn" id="shoppingCartBtn" onclick="application_create_widget.switch2Shoppingcart()"><i class="fa fa-shopping-cart fa-fw"></i><span style="display:none;" class="shoppingcart-coner-mark">0</span></button>
                        </div>
                    </div>
                </div>
            </div>
            <c:if test="${param.pointType == 'cdm_store_open'}" >
                <div id="workshopInfoDialogWrapper">
                    <form id="workshopInfoForm" class="form-horizontal" method="post">
                        <div id="workshop_information" class="bui-form-group" >
                            <div class="cols-1 hide-important" id="selectWorkshopField">
                                <div class="field-group">
                                    <label class="field-label">门店：</label>
                                    <div class="control-group">
                                        <button id="selectWorkshop" type="button" class='btn-create bui-form-field control-text' onclick="application_create_widget.selectWorkshop()" ><i class="fa fa-plus"></i> 请选择门店</button>
                                        <input type="hidden" name="id" id="id4workshop"/>
                                    </div>
                                    <label class="field-label">门店名称：</label>
                                    <div class="control-group">
                                        <input id="name4workshop" type="text" name="name" class="control-text bui-form-field"/>
                                    </div>
                                    <label class="field-label">门店联系人：</label>
                                    <div class="control-group">
                                        <input id="contacts4workshop" type="text" class="control-text bui-form-field" name="contacts"/>
                                    </div>
                                </div>
                            </div>
                            <div class="field-group">
                                <label class="field-label">门店地址：</label>
                                <div class="control-group">
                                    <input id="address4workshop" type="text" name="address" class="control-text bui-form-field"/>
                                </div>
                                <label class="field-label">门店类型：</label>
                                <div class="control-group">
                                    <input id="type4workshop" type="text" class="control-text bui-form-field" name="type"/>
                                </div>
                                <label class="field-label">门店联系方式：</label>
                                <div class="control-group">
                                    <input id="number4workshop" type="text" class="control-text bui-form-field" name="number"/>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </c:if>
            <div class="" id="selectionListWrapper">
                <div class="m-filter-bar">
                    <div class="m-filter-type"></div>
                    <div class="m-filter-order"></div>
                </div>
                <div id="selectionList"></div>
                <div id="paging" class="bui-grid bui-grid-bbar bui-bar"></div>
            </div>
        </div>
        <div class="content-panel shopping-cart-page-wrapper hide">
            <div class="content-panel">
                <div class="field-group " style="">
                    <div class="control-group shoppingcart-summary ">
                        <span  class="summary-total-label">物料总数：</span><span class="summary-total-qty" id="shopppingCartTotalQty">8</span>
                    </div>
                    <div class="control-group pull-right">
                        <div class="selection-view-mode-group">
                            <button type="button" class="btn" id="selectionListBtn" onclick="application_create_widget.switch2Shelves()"><i class="fa fa-th fa-fw"></i><span>去挑选物料</span></button>
                        </div>
                    </div>
<%--                    <c:if test="${param.pointMode}" >--%>
<%--                        <div class="control-group pull-right shoppingcart-summary"><span class="summary-point-label">总${pointTitle}：</span><span class="summary-point-price" id="shopppingCartTotalPrice">1,234</span></div>--%>
<%--                    </c:if>--%>
                    <div style="clear:both;"></div>
                </div>
            </div>
            <div class="" id="shoppingCartGridWrapper"></div>
        </div>
    </div>
</div>

<div style="display: none;" id="contactInfoDialogWrapper">
    <div id="contactInfoDialog">
        <form id="contactInfoForm" class="form-horizontal" method="post">
            <input type="hidden" id="applicationId" name="applicationId" value="<%=null==request.getParameter("applicationId")?0:request.getParameter("applicationId")%>"class="control-text bui-form-field">
            <div id="other_address_information" class="bui-form-group" >
                <div class="cols-1">
                    <div class="col-group">
                        <div class="field-group">
                            <label class="field-label"><span class="required-flag">*</span>合伙人:</label>
                            <div class="control-group" style="width:72%;">
                                <% if (WxTUser.USER_MODEL_SP.equals(ContextUtil.getCurUser().getUserModel())) { %>
                                <input type="text" style="width:100%;" name="applicationOrgName" id="applicationOrgName" value="<%=ContextUtil.getCurUser().getOrgName()%>" class="control-text bui-form-field bui-form-field-disabled" disabled="disabled"/>
                                <input type="hidden" name="applicationOrgId" id="applicationOrgId" value="<%=ContextUtil.getCurUser().getOrgId()%>" />
                                <% }  else {%>
                                <input type="hidden" name="applicationOrgId" id="applicationOrgId" data-rules="{required:true}" data-messages="{required:'请选择合伙人'}"/>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="cols-1 hide-important" id="selectAddressField">
                    <div class="field-group">
                        <label class="field-label"></label>
                        <div class="control-group">
                            <c:if test="${param.pointMode}" >
                                <button id="selectAddress" type="button" class='btn-create' onclick="application_create_widget.selectAddress()" ><i class="fa fa-plus"></i> 请选择一个收货地址</button>
                            </c:if>
                            <c:if test="${param.pointMode != true}" >
                                <button id="selectAddress" type="button" class='btn-create' onclick="application_create_widget.selectAddress()" ><i class="fa fa-plus"></i> 您可以选择一个历史寄送地址和联系人</button>
                            </c:if>
                        </div>
                    </div>
                </div>
                <c:if test="${param.pointMode}" >
                    <div class="cols-1">
                        <div class="field-group">
                            <label class="field-label"></label>
                            <div class="control-group hide">
                                <input type="checkbox" id="isUrgent" name="isUrgent" value=true class="control-checkbox bui-form-field" onclick=""/><label for="isUrgent">订单加急（经过管理员审批后可快速出库发货）</label>
                            </div>
                        </div>
                    </div>
                </c:if>
                <div class="cols-2">
                    <div class="col-group" >
                        <c:choose>
                            <c:when test="${param.pointMode eq 'true' and (empty orderConfig or not orderConfig.addressEditable)}">
                                <div id="provinceCityDistWrapper">
                                    <div class="field-group">
                                        <label class="field-label">省份：</label>
                                        <div class="control-group" >
                                            <input id="provinceCodeNameFixed" name="provinceCodeName" type="text" value="" class=" control-text bui-form-field " disabled="disabled" />
                                            <input id="provinceCodeFixed" name="provinceCode" type="hidden" value="" class=" control-text bui-form-field"/>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label">城市：</label>
                                        <div class="control-group">
                                            <input id="cityCodeNameFixed" name="cityCodeName" type="text" value="" class=" control-text bui-form-field" disabled="disabled"/>
                                            <input id="cityCodeFixed" name="cityCode" type="hidden" value="" class=" control-text bui-form-field"/>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label">区县：</label>
                                        <div class="control-group">
                                            <input id="distCodeNameFixed" name="distCodeName" type="text" value="" class="control-text bui-form-field" disabled="disabled" />
                                            <input id="distCodeFixed" name="distCode" type="hidden" value="" class="control-text bui-form-field " />
                                        </div>
                                    </div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div id="provinceCityDistWrapper">
                                    <div class="field-group">
                                        <label class="field-label"><span class="required-flag">*</span>省份：</label>
                                        <div class="control-group" id="provinceCodeHid_c">
                                            <input id="provinceCodeHid" name="provinceCode" type="hidden" value="2" class=" control-text bui-form-field"  data-rules="{required:true}" data-messages="{required:'请选择省份'}" />
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label"><span class="required-flag">*</span>城市：</label>
                                        <div class="control-group" id="cityCodeHid_c">
                                            <input id="cityCodeHid" name="cityCode" type="hidden" value="2" class=" control-text bui-form-field" data-rules="{required:true}" data-messages="{required:'请选择城市'}"/>
                                        </div>
                                    </div>
                                    <div class="field-group">
                                        <label class="field-label"><span class="required-flag">*</span>区县：</label>
                                        <div class="control-group" id="distCodeHid_c">
                                            <input id="distCodeHid" name="distCode" type="hidden" value="2" class="control-text bui-form-field" data-rules="{required:true}" data-messages="{required:'请选择区县'}"/>
                                        </div>
                                    </div>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                    <div class="col-group">
                        <c:choose>
                            <c:when test="${param.pointMode eq 'true' and (empty orderConfig or not orderConfig.addressEditable)}">
                                <div class="field-group">
                                    <label class="field-label"><span class="required-flag">*</span>联系人：</label>
                                    <div class="control-group">
                                        <input id="contacts" type="hidden" class="control-text bui-form-field" name="contacts" data-rules="{required:true, maxlength:50}" data-messages="{required:'联系人不能为空'}"/>
                                        <input id="contactsFixed" type="text" class="control-text bui-form-field bui-form-field-disabled" disabled="disabled" name="contactsFixed" data-rules="{required:true, maxlength:50}" data-messages="{required:'联系人不能为空'}"/>
                                    </div>
                                </div>
                                <div class="field-group">
                                    <label class="field-label"><span class="required-flag">*</span>联系电话：</label>
                                    <div class="control-group">
                                        <input id="number" type="hidden" class="control-text bui-form-field " name="number" data-rules="{required:true,telNo:true}" data-messages="{required:'联系人电话不能为空'}"/>
                                        <input id="numberFixed" type="text" class="control-text bui-form-field bui-form-field-disabled" disabled="disabled" name="numberFixed" data-rules="{required:true,telNo:true}" data-messages="{required:'联系人电话不能为空'}"/>
                                    </div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="field-group">
                                    <label class="field-label"><span class="required-flag">*</span>联系人：</label>
                                    <div class="control-group">
                                        <input id="contacts" type="text" class="control-text bui-form-field" name="contacts" data-rules="{required:true, maxlength:50}" data-messages="{required:'联系人不能为空'}"/>
                                    </div>
                                </div>
                                <div class="field-group">
                                    <label class="field-label"><span class="required-flag">*</span>联系电话：</label>
                                    <div class="control-group">
                                        <input id="number" type="text" class="control-text bui-form-field" name="number" data-rules="{required:true,telNo:true}" data-messages="{required:'联系人电话不能为空'}"/>
                                    </div>
                                </div>
                            </c:otherwise>
                        </c:choose>
                        <div class="field-group">
                            <label class="field-label time">期望到达日期:</label>
                            <div class="control-group">
                                <input type="text" class="control-text control-calendar Wdate" data-rules="" data-messages=""
                                       name="recipientDate" id="recipientDate"
                                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d+1}'});"
                                       onChange="" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="cols-1">
                    <div class="col-group">
                        <div class="field-group">
                            <label class="field-label"><span class="required-flag">*</span>详细地址：</label>
                            <c:choose>
                                <c:when test="${param.pointMode eq 'true' and (empty orderConfig or not orderConfig.addressEditable)}">
                                    <div class="control-group" style="width:100%;">
                                        <input id="address" name="address" type="hidden" value="" class="control-text bui-form-field " data-rules="{required:true,maxlength:200}" data-messages="{required:'请选择一个收货地址'}" />
                                        <textarea id="addressFixed" type="hidden" style="width:74%;" name="addressFixed" class="control-textarea control-text bui-form-field bui-form-field-disabled" disabled="disabled" data-rules="{required:true,maxlength:200}" data-messages="{required:'请选择一个收货地址'}"></textarea>
                                            <%-- <textarea id="address" style="width:74%;" name="address" class="control-textarea control-text bui-form-field" data-rules="{required:true,maxlength:200}" data-messages="{required:'请选择或者输入一个收货地址'}"></textarea> --%>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="control-group" style="width:100%;">
                                        <textarea id="address" style="width:74%;" name="address" class="control-textarea control-text bui-form-field" data-rules="{required:true,maxlength:200}" data-messages="{required:'请选择或者输入一个收货地址'}"></textarea>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                        <div class="field-group">
                            <label class="field-label">备注:</label>
                            <div class="control-group" style="width:100%;">
                                <textarea id="comments" style="width:74%;" class="control-textarea control-text bui-form-field" name="comments" data-rules="" data-messages=""></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" name="bizType" value="<%=request.getParameter("bizType")%>"/>
        </form>
    </div>
</div>

<div style = "display:none;">
    <div class="" id="addressSelectionGridWrapper" style="min-height:400px;">
    </div>
</div>
<div style = "display:none;">
    <div class="" id="workshopSelectionGridWrapper" style="min-height:400px;">
        <div class="content-panel query-panel first-content-panel content-panel-large query-panel-2cols">
            <div class="field-group key-search">
                <label class="field-label width-auto">搜索：</label>
                <div class="control-group">
                    <input type="text" name="gKeyWord" placeholder="请输入门店名称" class="control-text"/>
                </div>
            </div>
            <div class="query-btns">
                <div class="query-btn field-label">
                    <button type="button" onclick="application_create_widget.searchWorkShop()" class="btn-query">查询</button>
                </div>
                <!-- <div class="adv-toggle-btn">
                    <a href="javascript: void(0);" onclick="var el = $(this), queryPanel = el.parents('.query-panel:first');if(queryPanel.hasClass('query-adv')){queryPanel.removeClass('query-adv');el.text('高级搜索');}else{queryPanel.addClass('query-adv');el.text('收起');}">高级搜索</a>
                </div> -->
            </div>
        </div>
    </div>
</div>
<div id="shippingListDialogWrapper" style = "display:none;">
    <div class="" id="shippingListDialog" style="min-height:400px;" >
        <div class="shipping-list-before-new-order" id="shippingListWrapper">
            <ul class="applicationListHeader">
                <li class="">
                    <div class="row-fluid application-header-panel app-list-header">
                        <div class="app-info-time span3 align-center" >时间</div>
                        <div class=" span2">订单号</div>
                        <div class="app-info-status span2">状态</div>
                        <c:if test="${param.pointMode != true}">
                            <div class="app-info-partner span3">合伙人</div>
                        </c:if>
                        <div class="app-info-address span5">详细地址</div>
                        <div class="app-info-person span1">收件人</div>
                        <div class="app-info-number span2">联系方式</div>
                        <div class="app-info-toolbar span" style="text-align: left;width: 30px;">操作</div>
                    </div>
                </li>
            </ul>
            <ul class="applicationList"></ul>
        </div>
    </div>
</div>

</body>
</html>
