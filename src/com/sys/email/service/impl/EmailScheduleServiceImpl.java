package com.sys.email.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chevron.task.service.JobManageServiceI;
import com.sys.email.dao.EmailConfigMapper;
import com.sys.email.model.EmailConfig;
import com.sys.email.service.EmailScheduleService;
import com.sys.quartz.dao.ScheduleJobMapper;
import com.sys.quartz.model.ScheduleJob;

@Service
public class EmailScheduleServiceImpl implements EmailScheduleService {
	
	@Resource
	private EmailConfigMapper emailConfigMapper;
	@Resource
	private JobManageServiceI jobManageService;
	@Resource
	private ScheduleJobMapper scheduleJobMapper;
	
	@Override
	public Map<String, Object> updateEmailSchedule(int[] time,String[] date,String[] day,String receive,String cc,Date startTime,Date endTime,String springId,String cyclePeriod) {
		HashMap<String,Object> resultMap = new HashMap<String, Object>();		
		resultMap.put("result","fail");
		try {
			//关闭定时任务
			ScheduleJob job = scheduleJobMapper.getScheduleJobBySpringId(springId);
			jobManageService.stopJob(job);
			
			//更新定时任务
			String cronExpression = createCronExpression(time, cyclePeriod, date, day);
			//更新corn表达式
			job.setCronExpression(cronExpression);
			job.setStartTime(startTime);
			job.setEndTime(endTime);
			job.setUpdateTime(new Date());
			scheduleJobMapper.updateByPrimaryKey(job);
			//更新email config
			EmailConfig emailConfig = emailConfigMapper.getEmailConfigBySpringId(springId);
			emailConfig.setAccepterEmailAddr(receive);
			emailConfig.setCcEmailAddr(cc);
			emailConfig.setUpdateTime(new Date());
			emailConfigMapper.updateAccepterBySpringId(emailConfig);
			
			//打开定时任务
			jobManageService.startJob(job);
			
			resultMap.put("result","success");
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return resultMap;
	}
	@Override
	public Map<String, Object> getAllEmailConfig() {
		HashMap<String,Object> resultMap=new HashMap<String, Object>();
		
		try {
			List<EmailConfig> resultList = emailConfigMapper.getAllEmailConfig();
			resultMap.put("data", resultList);
			resultMap.put("result", "success");
		} catch (Exception e) {
			resultMap.put("result", "fail");
			e.printStackTrace();
		}
		return resultMap;
	}
	
	/**
	 * 创建一个cron表达式
	 * @param startTime 开始时间
	 * @param repetitionType 时间周期类型，day每天循环，month每月固定几天循环，week每周固定几天循环
	 * @param dates 当repetitionType为month时，接受天数的数组
	 * @param days 当repetitionType为month时，接受一周第几天的数组
	 * @return 
	 */
	private String createCronExpression(int[] startTime,String repetitionType,String[] dates,String[] days){
		StringBuilder cronExpression= new StringBuilder();
		cronExpression.append("0 0 ");
		for(int hour:startTime){
			cronExpression.append(hour+",");
		}
		cronExpression.delete(cronExpression.length()-1, cronExpression.length());//删除最后一个逗号
		
		//判断是哪种循环定时方式
		if(repetitionType.equals("day")){
			cronExpression.append(" * * ?");
		} else if(repetitionType.equals("month")){
			cronExpression.append(" ");
			for(String date:dates){
				cronExpression.append(date+",");
			}
			cronExpression.delete(cronExpression.length()-1, cronExpression.length());//删除最后一个逗号
			
			cronExpression.append(" * ?");
		} else if(repetitionType.equals("week")){
			cronExpression.append(" ? * ");
			
			for(String day:days){
				cronExpression.append(day+",");
			}
			cronExpression.delete(cronExpression.length()-1, cronExpression.length());//删除最后一个逗号
		}
		
		return cronExpression.toString();
	}

}
