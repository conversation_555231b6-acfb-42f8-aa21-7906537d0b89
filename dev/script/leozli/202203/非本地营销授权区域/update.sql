/*insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.productChannel2AuthChannelMap', '非本地营销产品渠道转授权区域渠道映射配置', '非本地营销产品渠道转授权区域渠道映射配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'CDM', '1', '乘用车', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'C&I', '2', '商用油', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'OEM', '1', '乘用车', '1',30);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.sku2AuthChannelMap', '非本地营销产品转授权区域渠道映射配置', '非本地营销产品渠道转授权区域渠道映射配置');
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.sku2AuthChannelMap', 'CDM', '1', '乘用车', '1',10);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.productChannel2FormChannelMap', '非本地营销产品渠道转表单渠道映射配置', '非本地营销产品渠道转表单渠道映射配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'CDM', '1', '乘用车', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'C&I', '2', '商用油', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'Others', '4', '其他', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'OEM', '8', 'OEM', '1',30);
*/

select * into wx_t_region_partner202205
from wx_t_region_partner rp1
where exists (select 1 from wx_t_region_partner rp2 
where rp2.partner_id=rp1.partner_id and rp2.region_id=rp1.region_id and rp1.id<rp2.id and rp2.channel_weight=rp1.channel_weight)

--select * 
delete rp1
from wx_t_region_partner rp1
where exists (select 1 from wx_t_region_partner rp2 
where rp2.partner_id=rp1.partner_id and rp2.region_id=rp1.region_id and rp1.id<rp2.id and rp2.channel_weight=rp1.channel_weight)

ALTER TABLE wx_t_region_partner ADD CONSTRAINT uk_region_partner UNIQUE ([region_id],[partner_id],channel_weight);

select * into wx_t_region_partner202205bak
from wx_t_region_partner rp1

select * into wx_t_user_charge_region202205bak from wx_t_user_charge_region
truncate table wx_t_user_charge_region

update  m set m.status=0
from wx_t_menu m where menu_id=50099