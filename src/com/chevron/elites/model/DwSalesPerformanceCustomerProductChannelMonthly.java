package com.chevron.elites.model;

import java.math.BigDecimal;

public class DwSalesPerformanceCustomerProductChannelMonthly {

	private String year;
	private String month;
	private String customerNameCN;
	private String customerGrade;
	
	private BigDecimal totalVolume;
	private Double iviTotalTarget;
	private Double iviPercentage;
	
	private BigDecimal bdFundTotal;
	private BigDecimal bdFundCDM;
	private BigDecimal bdFundCI;
	
	private BigDecimal marketingFundTotal;
	private BigDecimal marketingFundCDM;
	private BigDecimal marketingFundCI;
	
	private BigDecimal carOilVolume;
	private BigDecimal carOilVolumeY1;
	private BigDecimal carOilIncreasedPercentage;
	private Double carOilTarget;
	private Double carOilCompletePercentage;
	
	private BigDecimal commercialOilVolume;
	private BigDecimal commercialOilVolumeY1;
	private BigDecimal commercialOilIncreasedPercentage;
	private Double commercialOilTarget;
	private Double commercialOilCompletePercentage;
	
	private BigDecimal industrialOilVolume;
	private BigDecimal industrialOilVolumeY1;
	private BigDecimal industrialOilIncreasedPercentage;
	private Double industrialOilTarget;
	private Double industrialOilCompletePercentage;
	
	private BigDecimal abpRmb;
	
	private BigDecimal iviFundRmb;
	private BigDecimal iviFundCDMRmb;
	private BigDecimal iviFundCIORmb;

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public String getCustomerNameCN() {
		return customerNameCN;
	}

	public void setCustomerNameCN(String customerNameCN) {
		this.customerNameCN = customerNameCN;
	}

	public String getCustomerGrade() {
		return customerGrade;
	}

	public void setCustomerGrade(String customerGrade) {
		this.customerGrade = customerGrade;
	}

	public BigDecimal getTotalVolume() {
		return totalVolume;
	}

	public void setTotalVolume(BigDecimal totalVolume) {
		this.totalVolume = totalVolume;
	}

	public Double getIviTotalTarget() {
		return iviTotalTarget;
	}

	public void setIviTotalTarget(Double iviTotalTarget) {
		this.iviTotalTarget = iviTotalTarget;
	}

	public Double getIviPercentage() {
		return iviPercentage;
	}

	public void setIviPercentage(Double iviPercentage) {
		this.iviPercentage = iviPercentage;
	}

	public BigDecimal getBdFundTotal() {
		return bdFundTotal;
	}

	public void setBdFundTotal(BigDecimal bdFundTotal) {
		this.bdFundTotal = bdFundTotal;
	}

	public BigDecimal getBdFundCDM() {
		return bdFundCDM;
	}

	public void setBdFundCDM(BigDecimal bdFundCDM) {
		this.bdFundCDM = bdFundCDM;
	}

	public BigDecimal getBdFundCI() {
		return bdFundCI;
	}

	public void setBdFundCI(BigDecimal bdFundCI) {
		this.bdFundCI = bdFundCI;
	}

	public BigDecimal getMarketingFundTotal() {
		return marketingFundTotal;
	}

	public void setMarketingFundTotal(BigDecimal marketingFundTotal) {
		this.marketingFundTotal = marketingFundTotal;
	}

	public BigDecimal getMarketingFundCDM() {
		return marketingFundCDM;
	}

	public void setMarketingFundCDM(BigDecimal marketingFundCDM) {
		this.marketingFundCDM = marketingFundCDM;
	}

	public BigDecimal getMarketingFundCI() {
		return marketingFundCI;
	}

	public void setMarketingFundCI(BigDecimal marketingFundCI) {
		this.marketingFundCI = marketingFundCI;
	}

	public BigDecimal getCarOilVolume() {
		return carOilVolume;
	}

	public void setCarOilVolume(BigDecimal carOilVolume) {
		this.carOilVolume = carOilVolume;
	}

	public BigDecimal getCarOilVolumeY1() {
		return carOilVolumeY1;
	}

	public void setCarOilVolumeY1(BigDecimal carOilVolumeY1) {
		this.carOilVolumeY1 = carOilVolumeY1;
	}

	public BigDecimal getCarOilIncreasedPercentage() {
		return carOilIncreasedPercentage;
	}

	public void setCarOilIncreasedPercentage(BigDecimal carOilIncreasedPercentage) {
		this.carOilIncreasedPercentage = carOilIncreasedPercentage;
	}

	public Double getCarOilTarget() {
		return carOilTarget;
	}

	public void setCarOilTarget(Double carOilTarget) {
		this.carOilTarget = carOilTarget;
	}

	public Double getCarOilCompletePercentage() {
		return carOilCompletePercentage;
	}

	public void setCarOilCompletePercentage(Double carOilCompletePercentage) {
		this.carOilCompletePercentage = carOilCompletePercentage;
	}

	public BigDecimal getCommercialOilVolume() {
		return commercialOilVolume;
	}

	public void setCommercialOilVolume(BigDecimal commercialOilVolume) {
		this.commercialOilVolume = commercialOilVolume;
	}

	public BigDecimal getCommercialOilVolumeY1() {
		return commercialOilVolumeY1;
	}

	public void setCommercialOilVolumeY1(BigDecimal commercialOilVolumeY1) {
		this.commercialOilVolumeY1 = commercialOilVolumeY1;
	}

	public BigDecimal getCommercialOilIncreasedPercentage() {
		return commercialOilIncreasedPercentage;
	}

	public void setCommercialOilIncreasedPercentage(
			BigDecimal commercialOilIncreasedPercentage) {
		this.commercialOilIncreasedPercentage = commercialOilIncreasedPercentage;
	}

	public Double getCommercialOilTarget() {
		return commercialOilTarget;
	}

	public void setCommercialOilTarget(Double commercialOilTarget) {
		this.commercialOilTarget = commercialOilTarget;
	}

	public Double getCommercialOilCompletePercentage() {
		return commercialOilCompletePercentage;
	}

	public void setCommercialOilCompletePercentage(
			Double commercialOilCompletePercentage) {
		this.commercialOilCompletePercentage = commercialOilCompletePercentage;
	}

	public BigDecimal getIndustrialOilVolume() {
		return industrialOilVolume;
	}

	public void setIndustrialOilVolume(BigDecimal industrialOilVolume) {
		this.industrialOilVolume = industrialOilVolume;
	}

	public BigDecimal getIndustrialOilVolumeY1() {
		return industrialOilVolumeY1;
	}

	public void setIndustrialOilVolumeY1(BigDecimal industrialOilVolumeY1) {
		this.industrialOilVolumeY1 = industrialOilVolumeY1;
	}

	public BigDecimal getIndustrialOilIncreasedPercentage() {
		return industrialOilIncreasedPercentage;
	}

	public void setIndustrialOilIncreasedPercentage(
			BigDecimal industrialOilIncreasedPercentage) {
		this.industrialOilIncreasedPercentage = industrialOilIncreasedPercentage;
	}

	public Double getIndustrialOilTarget() {
		return industrialOilTarget;
	}

	public void setIndustrialOilTarget(Double industrialOilTarget) {
		this.industrialOilTarget = industrialOilTarget;
	}

	public Double getIndustrialOilCompletePercentage() {
		return industrialOilCompletePercentage;
	}

	public void setIndustrialOilCompletePercentage(
			Double industrialOilCompletePercentage) {
		this.industrialOilCompletePercentage = industrialOilCompletePercentage;
	}

	public BigDecimal getAbpRmb() {
		return abpRmb;
	}

	public void setAbpRmb(BigDecimal abpRmb) {
		this.abpRmb = abpRmb;
	}

	public BigDecimal getIviFundRmb() {
		return iviFundRmb;
	}

	public void setIviFundRmb(BigDecimal iviFundRmb) {
		this.iviFundRmb = iviFundRmb;
	}

	public BigDecimal getIviFundCDMRmb() {
		return iviFundCDMRmb;
	}

	public void setIviFundCDMRmb(BigDecimal iviFundCDMRmb) {
		this.iviFundCDMRmb = iviFundCDMRmb;
	}

	public BigDecimal getIviFundCIORmb() {
		return iviFundCIORmb;
	}

	public void setIviFundCIORmb(BigDecimal iviFundCIORmb) {
		this.iviFundCIORmb = iviFundCIORmb;
	}
}
