;(function($){
	//单元格基础对象
	var BaseTableCell = {
			rowIndex: 0,
			colIndex: 0,
			colSpan: 1,
			rowSpan: 1,
			mergedBy: null,
			addColSpan: function(offset){
				if(this.mergedBy == null){
					this.colSpan += offset;
				}else if(this.rowIndex == this.mergedBy.rowIndex){
					this.mergedBy.colSpan += offset;
				}
				return this;
			},
			addRowSpan: function(offset){
				if(this.mergedBy == null){
					this.rowSpan += offset;
				}else if(this.colIndex == this.mergedBy.colIndex){
					this.mergedBy.rowSpan += offset;
				}
				return this;
			},
			getPhysicalCell: function(){
				return this.mergedBy || this;
			},
			getOriginalRowIndex: function(){
				if(this.originalRowIndex){
					return this.originalRowIndex;
				}
				return this.rowIndex;
			},
			getOriginalColIndex: function(){
				if(this.originalColIndex){
					return this.originalColIndex;
				}
				return this.colIndex;
			}
	};
	//构建表头
	var buildHeader = function(columns, headerMatrix, leafColumns, grid){
		var firstRow = [];
		headerMatrix.push(firstRow);
		for(var i = 0; i < columns.length; i++){
			buildHeaderCell(columns[i], 0, firstRow.length, firstRow, headerMatrix, leafColumns, grid);
		}
	};
	//构建表头单元格
	var buildHeaderCell = function(column, rowIndex, colIndex, row, headerMatrix, leafColumns, grid){
		row.push($.extend({}, BaseTableCell, {
			rowIndex: rowIndex,
			colIndex: colIndex,
			title: column.title,
			cellId: 'ui_grid_cell' + grid.counter++
		}));
		if(column.children && column.children.length > 0){
			//初始化新行
			var newRow, newRowIndex = rowIndex + 1;
			if(headerMatrix.length == newRowIndex){
				//创建新行
				newRow = [];
				headerMatrix.push(newRow);
				//更新前面列的rowSpan属性
				for(var i = 0; i < colIndex; i++){
					newRow.push($.extend({}, BaseTableCell, {
						colIndex: i,
						rowIndex: rowIndex,
						mergedBy: headerMatrix[rowIndex][i].addRowSpan(1).getPhysicalCell()
					}));
				}
			}else{
				newRow = headerMatrix[newRowIndex];
			}
			for(var i = 0; i < column.children.length; i++){
				if(i > 0){
					var newColIndex = newRow.length;
					//创建新列，更新前一列的colSpan属性
					for(var j = 0; j < newRowIndex; j++){
						var c = headerMatrix[j][newColIndex - 1];
						headerMatrix[j].push($.extend({}, BaseTableCell, {
							rowIndex: j,
							colIndex: colIndex + i,
							mergedBy: headerMatrix[j][newColIndex - 1].addColSpan(1).getPhysicalCell()
						}));
					}
				}
				buildHeaderCell(column.children[i], newRowIndex, newRow.length, newRow, headerMatrix, leafColumns, grid);
			}
		}else{
			//叶子column
			leafColumns.push(column);
			if(column.endLock){
				grid._endLockIndex = colIndex;
			}
			if(column.summary){
				grid.hasSummary = true;
			}
		}
	};
	//渲染表头单元格
	var renderHeaderCell = function(header, output, grid){
		output.push('<td class="grid-header-td" valign="middle"');
		if(header.colSpan > 1){
			output.push(' colSpan="', header.colSpan, '"');
		}
		if(header.rowSpan > 1){
			output.push(' rowSpan="', header.rowSpan, '"');
		}
		output.push('><div class="', grid.headerCls, '"><span class="text-nowrap">', header.title, '</span>');
		output.push('</div></td>');
	};
	//渲染数据表格
	var renderDataCell = function(cell, output, header){
		output.push('<td class="grid-body-td');
		if(header.tdCls){
			output.push(' ', header.tdCls);
		}
		output.push('" valign="middle"');
		if(cell.colSpan){
			output.push(' colSpan="', cell.colSpan, '"');
		}
		if(cell.rowSpan){
			output.push(' rowSpan="', cell.rowSpan, '"');
		}
		output.push('><div class="', UI.Grid.alignCls[header.align], '"><span>', cell.html, '</span></div></td>');
		delete cell.html;
	};
	//构建数据行矩阵
	var buildRowsMatrix = function(grid, rows){
		var rowsMatrix = [], len = rows.length;
		for(var i = 0; i < len; i++){
			var item = rows[i], clen = grid._leafColumns.length;
			rowsMatrix[i] = [];
			for(var j = 0; j < clen; j++){
				var col = grid._leafColumns[j], cell = rowsMatrix[i][j] = {};
				if(col.rowMergeable && i > 0){
					//行可合并
					prerowCell = rowsMatrix[i - 1][j];
					if(j > 0 && col.colMerged && prerowCell.merged && prerowCell.mergeCell.colIndex  < j && 
							prerowCell.mergeCell.rowIndex + prerowCell.mergeCell > i){
						//单元格处于合并区内
						cell.merged = true;
						cell.mergeCell = prerowCell.mergeCell;
						continue;
					}else{
						var v = col.propertyProcessor(record, col.valueProperty);
						if(prerowCell.value == v){
							//合并同一列
							if(prerowCell.merged){
								if(prerowCell.mergeCell.colIndex < j){
									throw '第' + (i + 1) + '行的第' + j + '列数据应和第' + i + '行的第' + j + '列单元格合并';
								}
								prerowCell.mergeCell.rowSpan++;
								cell.mergeCell = prerowCell.mergeCell;
							}else{
								prerowCell.rowSpan = 2;
								cell.mergeCell = prerowCell;
							}
							cell.merged = true;
							continue;
						}
					}
				}
				cell.html = col.formatter(col.propertyProcessor(recod, col.property), record, i, grid._renderHandler.generateRecordId(grid, record));
				if(col.colMergeable && ((j <= grid._endLockIndex && j > 0) || (j > grid._endLockIndex && j > grid._endLockIndex + 1))){
					//列可合并
					if(cell.html == grid.colMergeableText){
						var precolCell = rowsMatrix[i][j - 1];
						if(precolCell.merged){
							if(precolCell.mergeCell.rowIndex < i){
								throw '第' + (i - 1) + '行第' + j + '列单元格和第' + i + '行第' + j + '列单元格应该合并';
							}
							precolCell.mergeCell.rowSpan++;
							cell.mergeCell = precolCell.mergeCell;
						}else{
							precolCell.colSpan = 2;
							cell.mergeCell = precolCell;
						}
						continue;
					}
				}
				cell.rowIndex = i;
				cell.colIndex = j;
			}
		}
	};
	//渲染数据
	var renderData = function(grid, rows){
		//渲染行数据
		grid._rowsMatrix = buildRowsMatrix(grid, rows);
		grid._rowsContainer.append(grid._renderHandler.renderRows(grid, grid._rowsMatrix, rows).join(''));
//		grid.rows = rows;
	};
	//清空表格显示数据
	var emptyData = function(grid){
		grid._rowsContainer.empty();
		grid._rowsMatrix = null;
	};

	var checkbox = {
			align: 'center',
			tdCls: 'col-checkbox',
			fixWidth: true,
			disabledProperty: '', //不可选中属性
			disabled: function(value, item, index){
				return this.disabledProperty && item[this.disabledProperty] === true;
			},
			checked: function(value, item, index, grid){
				return value === true;
			},
			formatter: function(value, item, index, recordId, grid){
				var disabled = this.disabled(value, item, index);
				if(disabled){
					return '<span class="input-checkbox input-checkbox-disabled"/>';
				}else{
					return '<span class="input-checkbox' + (this.selected(value, item, index, grid) ? ' input-checkbox-checked' : '') 
							+ '" onclick="UI.Grid.plugins.checkbox.click(this, ' + index + ');"/>';
				}
			}
		};
	//列表头基础数据
	var baseColHeader = {
			rowSpan: 1, 
			colSpan: 1, 
			_increaseColumn: function(){
				if(this.parent){
					this.parent._increaseColumn();
					this.colSpan++;
				}
			}
		};
	//列锁定处理器
	var renderHandlers = {
			colLock: {
				//渲染x轴滚动区
				renderXScroll: function(grid, gridEl){
					var $xs = $('<div class="grid-xscroll"><div class="grid-empty-collock"></div><div class="grid-block-xscroll"></div><div class="grid-empty-yscroll"></div></div>"')
							.appendTo(gridEl);
					grid._xscroll = $xs.find('.grid-block-xscroll');
				},
				//渲染表头
				renderHeader: function(grid, headerMatrix){
					var h = ['<div class="grid-header"><div class="grid-block-tl"><table cellspacing="1" cellpadding="0" width="100%" border="0"><colgroup>'];
					//渲染锁定列样式定义元素
					for(var j = 0; j <= grid._endLockIndex; j++){
						h.push('<col/>');
					}
					h.push('</colgroup><tbody>');
					//渲染表格左上角块
					for(var i = 0; i < headerMatrix.length; i++){
						var headerRow = headerMatrix[i];
						h.push('<tr>');
						for(var j = 0; j <= grid._endLockIndex; j += headerRow[j].colSpan){
							renderHeaderCell(headerRow[j], h, grid);
						}
						h.push('</tr>');
					}
					h.push('</tbody></table></div><div class="grid-block-tr"><table cellspacing="1" cellpadding="0" width="100%" border="0"><colgroup>');
					//渲染非锁定列样式定义元素
					for(var j = grid._endLockIndex + 1; j < headerRow.length; j++){
						h.push('<col/>');
					}
					h.push('</colgroup><tbody>');
					//渲染表格右上角块
					for(var i = 0; i < headerMatrix.length; i++){
						var headerRow = headerMatrix[i];
						h.push('<tr>');
						for(var j = grid._endLockIndex + 1; j < headerRow.length; j += headerRow[j].colSpan){
							renderHeaderCell(headerRow[j], h, grid);
						}
						h.push('</tr>');
					}
					h.push('</tbody></table></div><div class="grid-empty-yscroll"></div></div>');
					return h;
				},
				//渲染数据行
				renderRows: function(grid, rowsMatrix, rows){
					//渲染锁定区数据单元格
					var rlen = rowsMatrix.length, h = ['<div class="grid-block-ml"><table cellspacing="1" cellpadding="0" width="100%" border="0"><tbody>'], uh = [];
					for(var i = 0; i < rlen; i++){
						this.renderRow(grid, rowsMatrix[i], i, grid.rowCls, h, uh, rows[i]);
					}
					h.push('</tbody></table></div><div class="grid-block-mr"><table cellspacing="1" cellpadding="0" width="100%" border="0"><tbody>', uh.join(''));
					h.push('<tbody></table></div><div class="grid-empty-yscroll"></div>');
					return h;
				},
				renderRow: function(grid, matrixRow, rowIndex, rowCls, lockedBuffer, unlockedBuffer, record){
					grid.elMap[record._rowId] = {matrixRow: matrixRow, record: record};
					//渲染锁定区数据单元格
					var rowId = record._rowId + '_locked';
					lockedBuffer.push('<tr class="' , rowCls);
					if(grid.rowLoopCls && rowLoopCls.length > 0){
						lockedBuffer.push(' ', rowLoopCls[rowIndex%rowLoopCls.length]);
					}
					lockedBuffer.push('">');
					for(var j = 0, k = 0; j <= grid._endLockIndex; j += matrixRow[j].colSpan){
						renderDataCell(matrixRow[j], lockedBuffer, grid._leafColumns[j]);
					}
					lockedBuffer.push('</tr>');
					//渲染未锁定数据单元格
					rowId = record._rowId + '_unlocked';
					unlockedBuffer.push('<tr class="' , rowCls);
					if(grid.rowLoopCls && rowLoopCls.length > 0){
						unlockedBuffer.push(' ', rowLoopCls[rowIndex%rowLoopCls.length]);
					}
					unlockedBuffer.push('">');
					for(var j = grid._endLockIndex + 1; j <= matrixRow.length; j += matrixRow[j].colSpan){
						renderDataCell(matrixRow[j], h, grid._leafColumns[j]);
					}
					unlockedBuffer.push('</tr>');
				},
				//生成记录行Id
				generateRecordId: function(grid, record){
					record._rowId = grid.id + '_row' + grid.counter++;
					return record._rowId;
				},
				//设置表格宽度
				resize: function(grid, width){
					
				}
			},
			colUnlock: {
				
			}
		};
	UI.Grid = UI.extend(UI.DomBase, {
		key: 'ui.grid',
		cssCls: 'ui-grid',
		counter: 1,
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom).addClass(this.cssCls), p = this.newInstance($d, opts), headerMatrix = [];
			p.leafColumns = [];
			p._endLockIndex = -1;
			p.hasSummary = false; //表格是否有汇总
			//生成grid ID
			p.id = 'tree_grid' + this.counter++;
			//初始化表格的计数器，用以生成row和cell的ID
			p.counter = 1; 
			//初始化表格row和cell元素对象的ID映射表
			p.elMap = {};
			//构建表头
			buildHeader(p.columns, headerMatrix, p.leafColumns, p);
			//渲染表格框架
			p._renderHandler = p._endLockIndex > -1 ? renderHandlers.colLock : renderHandlers.colUnlock;
			$d.append(p._renderHandler.renderHeader(p, headerMatrix).join('')).append('<div class="no-data-comment">' + p.noDataMsg + '</div>');
			p._rowsContainer = $('<div class="grid-rows"></div>').appendTo($d);
			p._renderHandler.renderXScroll(p, $d);
			//初始化分页控件
			if(p.paging){
				p.paging = UI.Paging.init(p === true ? null : p.paging);
				p.paging.bindGrid(p);
			}
			//初始化表格数据仓库
			if(!p.store){
				p.store = UI.Store.init(opts);
			}
			p.store.bindGrid(p);
			if(p.store.autoLoad){
				p.store.load();
			}
			return p;
		},
		baseCols: {
			rowNumb: {
				title: '序号',
				align: 'center',
				lock: true,
				tdCls: ['col-row-numb'],
				width: 50,
				fixWidth: true,
				formatter: function(value, item, index, recordId, grid){
					return index + 1;
				}
			},
			textInput: {
				align: 'center',
				tdCls: ['col-text-input'],
				formatter: function(value, item, index, recordId, grid){
					
				},
				getCtrl: function(){
					
				}
			},
			checkbox: checkbox,
			radio: {
				
			},
			selectionCheckbox: $.extend({}, checkbox, {
				title: '<span class="input-checkbox select-all-checkbox" onclick="UI.Grid.baseCheckboxCol.selectAll(this);"/>',
				align: 'center',
				lock: true,
				tdCls: 'col-checkbox',
				width: 50,
				fixWidth: true,
				disabledProperty: '', //不可选中属性
				primaryProperty: '', //记录主键属性
				disabled: function(value, item, index){
					return this.disabledProperty && item[this.disabledProperty] === true;
				},
				checked: function(value, item, index, grid){
					if(grid._selectionStatusCache && grid._selectionStatusCache[this.colCls] && 
							grid._selectionStatusCache[this.colCls][item[this.primaryProperty]] !== undefined){
						return grid._selectionStatusCache[this.colCls][item[this.primaryProperty]];
					}
					return this.selectedProperty && item[this.selectedProperty] === true;
				},
				formatter: function(value, item, index, recordId, grid){
					var disabled = this.disabled(value, item, index);
					if(disabled){
						return '<span class="input-checkbox input-checkbox-disabled"/>';
					}else{
						var checked = this.selected(value, item, index, grid);
						//更新表格全选状态
						if(grid._selectionStatus == 0 && checked){
							this._selectionStatus = 3;
						}else if(grid._selectionStatus == 0 && !checked){
							grid._selectionStatus = 1;
						}else if((grid._selectionStatus == 3 && !checked) || (grid._selectionStatus == 1 && checked)){
							grid._selectionStatus = 2;
						}
						return '<span class="input-checkbox' + (checked ? ' input-checkbox-checked' : '') 
								+ '" onclick="UI.Grid.baseCheckboxCol.selectRow(this, ' + index + ');"/>';
					}
				}
			})
		},
		baseRadioCol: {
			title: '选择',
			align: 'center'
		},
		plugins: {
			checkbox: {
				selectAll: function(dom){
					var $d = $(dom);
					if($d.hasClass('input-checkbox-disabled')){
						return;
					}
					var checked = $d.hasCls('input-checkbox-checked');
					if(checked){
						$d.removeClass('input-checkbox-checked');
					}else{
						$d.addClass('input-checkbox-checked');
					}
					UI.Grid.getInstance($(dom).parents('.' + UI.Grid.cssCls + ':first')).selectAll(!checked);
				},
				selectRow: function(dom, index){
					var $d = $(dom), checked = $d.hasCls('input-checkbox-checked');
					if(checked){
						$d.removeClass('input-checkbox-checked');
					}else{
						$d.addClass('input-checkbox-checked');
					}
					UI.Grid.getInstance($(dom).parents('.' + UI.Grid.cssCls + ':first')).selectRow(!checked, index);
				},
				_selectRow: function(grid, checked, index){
					//var $selAll = grid.element.find('.' + )
				},
				funExtention: {
					selectAll: function(checked){
						
					},
					selectRow: function(checked, index){
						
					}
				},
				afterGridInit: function(grid){
					grid.on('beforerenderdata', function(data){
						this._selectionStatus = 0;
					});
					grid.on('afterrenderdata', function(){
						if(this._selectionStatus == 0){
							//无可选中记录
							this.element.find('.select-all-checkbox').addClass('input-checkbox-disabled')
									.removeClass('input-checkbox-active').removeClass('input-checkbox-checked');
						}else if(this._selectionStatus == 1){
							//可选记录全部未选中
							this.element.find('.select-all-checkbox').removeClass('input-checkbox-disabled')
									.removeClass('input-checkbox-active').removeClass('input-checkbox-checked');
						}else if(this._selectionStatus == 2){
							//有可选记录选中
							this.element.find('.select-all-checkbox').removeClass('input-checkbox-disabled')
									.addClass('input-checkbox-active').removeClass('input-checkbox-checked');
						}else if(this._selectionStatus == 3){
							//所有可选记录选中
							this.element.find('.select-all-checkbox').removeClass('input-checkbox-disabled')
									.removeClass('input-checkbox-active').addClass('input-checkbox-checked');
						}
					});
					var col = this;
					grid.on('afterloaddata', function(){
						//设置表格选中状态缓存
						if(col.primaryProperty){
							this._selectionStatusCache = {};
						}
					});
				}
			},
			redio: {
				
			},
			rowSelection: {
				
			}
		},
		alignCls: {
			left: 'text-left',
			right: 'text-right',
			center: 'text-center'
		},
		base: {
			data: null,
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度
			 */
			resize: function(width){
				this.element.width(width);
				if(this.element.prop('scrollHeight') > this.element.prop('clientHeight')){
					this.isPart = true;
					if(this._flagInited){
						//已有未显示完整标记
						this._flag.show();
					}else{
						//初始化未显示完整标记
						this._flag = $(this.partFlag).appendTo(this.element.wrap('<span class="ui-label-wrapper"></span>'));
					}
					this.element.width(width - this._flag.width());
				}else if(this.isPart){
					this.isPart = false;
					this._flag.hide();
				}
			},
			selectRow: function(rowIndex){
				//TODO
				//if(this.trigger('beforeselectrow', row))
			},
			getRecordByRowId: function(rowId){
				return this.elMap[rowId].record;
			},
			renderRows: function(rows){
				renderData(this, rows);
			},
			//刷新单元格内容
			refreshCell: function(rowId, propertyName){
				
			}
		},
		columnDefaults: {
			title: '', //列表头
			property: '', //列显示对应属性
			valueProperty: '', //列值对应属性(rowMergeable为true时有效)，默认为列显示对应属性
			endLock: false, //是否最后的锁定列(叶子列的最后一列为true列有效)
			formatter: function(value, record, index, recordId){return value ? value : (value === 0 ? '0' : '');}, //列显示文本格式化
			'export': true, //是否需要导出
			tdCls: '', //列td样式
			rowMergeable: false, //行是否可合并的。true-合并同一列相邻行valuePorperty相等的单元格。默认为false。
			colMergeable: false, //列是否可合并。true-同一行，合并显示文本为表格对象的colMergeableText值的单元格与同一行前一个单元格。默认为false
			align: 'left', //显示文本水平对齐方式
			nowrap: false, //强制不换行
			width: 20, //列宽(如果有列锁定且所有列的宽度总和大于总宽度时为列实际宽度，否则代表列宽占的比例)
			minWidth: 20, //最小列宽(固定宽度列时无效)
			fixWidth: false, //列固定宽度,
			hidden: false, //是否隐藏列
			children: null, //子表头
			sortable: true, //列是否排序
			summary: false, //合计列
			propertyProcessor: function(record, propertyName){return record[propertyName];} //属性值处理器
		},
		defaults: {
			headerCls: 'ui-grid-header',
			columns: null, //列定义数组
			cellPaddingX: 1, //单元格X轴方向间距
			cellPaddingY: 1, //单元格Y轴方向间距
			store: null, //数据仓库实例
			colMergeableText: '<merge/>', //列可合并特殊显示文本
			noDataMsg: '无满足条件数据', //没有数据显示文本
			autoLoad: true, //自动加载数据
			sortable: false, //表格是否排序总开关
			sumRender: 'footer', //合计行渲染位置。header-表头，footer-表尾
			rowLoopCls: ['ui-grid-row-even', 'ui-grid-odd'], //行循环样式
			rowCls: 'ui-grid-row', //行样式
			paging: false //是否分页
		}
	});
})(jQuery);