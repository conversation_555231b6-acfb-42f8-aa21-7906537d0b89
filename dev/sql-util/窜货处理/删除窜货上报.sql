select * --into wx_t_loc_mkt_product_info20210513
--delete
from wx_t_loc_mkt_product_info where create_user_id=131709

select * --into wx_t_main_local_marketing20210513
--delete  
from wx_t_main_local_marketing where id=971

select s.* into wx_task_sub20210721
--update s set s.org_id=781
--delete s
from wx_task_sub s left join wx_task_main m on m.task_main_id=s.task_main_id
where m.tmb_type_code='TT_2_FEELING_GOODS' --and s.exec_user=123555
and org_id in (679,
715,
716)
select * 
--update s set s.org_id=781
--delete m
from wx_task_main m left join wx_task_sub s on m.task_main_id=s.task_main_id
where m.tmb_type_code='TT_2_FEELING_GOODS' --and s.exec_user=123555
--and m.task_main_id=476501 
and s.org_id in (751,
752,
753,
754,
755,
756)






select *
 from wx_t_loc_mkt_product_info p where logistics_code in ('102938223108') order by logistics_code
--select logistics_code,count(1) from wx_t_loc_mkt_product_info group by logistics_code having count(1)>1

select * --into wx_t_loc_mkt_product_info20210720
--delete
from wx_t_loc_mkt_product_info where 1=1 --and create_user_id=131709
and report_id in (1605,
1606,
1607,
1608,
1609,
1610,
1611)

select * --into wx_t_main_local_marketing20210720
--delete  
from wx_t_main_local_marketing where ID in (1605,
1606,
1607,
1608,
1609,
1610,
1611)

select * --into wx_t_non_local_mkt_flow_form20210720
-- delete f
from wx_t_non_local_mkt_flow_form f where id in (1502,
1503,
1504,
1505,
1506,
1507,
1508)



select
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '窜货经销商',
cs1.region_name "窜货大区",
 (select substring((
SELECT ','+xx0.province_name  FROM (select distinct re3.region_name province_name from wx_t_region_partner rp
join wx_t_region re1 on re1.id=rp.region_id
join wx_t_region re2 on re2.id=re1.parent_id
join wx_t_region re3 on re3.id=re2.parent_id
where rp.partner_id=pe.partner_id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '销售所属省',
(select substring((
SELECT ','+xx0.region_name FROM (select distinct re3.region_name province_name, re2.region_name from wx_t_region_partner rp
join wx_t_region re1 on re1.id=rp.region_id
join wx_t_region re2 on re2.id=re1.parent_id
join wx_t_region re3 on re3.id=re2.parent_id
where rp.partner_id=pe.partner_id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '销售所属市',
ISNULL(dic1.dic_item_name,cs.customer_name_cn) as '被窜货经销商',
cs.region_name "被窜货大区",
m.report_name as '上报人',
case when f.to_flsr_confirm is null then '' when f.to_flsr_confirm = 0 then '否' else '是' end as '被窜货FLSR是否同意上报',
p.region_name as '实际上报省',
c.region_name as '实际上报市',
convert(nvarchar(20),m.create_time,20) as '上报日期',
year(m.create_time) as '上报年',
month(m.create_time) as '上报月',
day(m.create_time) as '上报日',
d1.dic_item_name as '上报来源',
ISNULL(p1.name,b.product_name) as '产品名称',
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region_partner rp
        join wx_t_region r1 on r1.id=rp.region_id
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where rp.partner_id=pe.partner_id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "省",
        (select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp
        join wx_t_region r1 on r1.id=rp.region_id
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where rp.partner_id=pe.partner_id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "市",
d2.dic_item_name as '产品渠道',
b.scan_time "出库日期",b.product_time "生产日期",b.product_batch "生产批号",
        b.product_out_no "出库单号",b.logistics_code "物流码",
case when f.form_status = 50 then 'ABM已确认' 
when f.form_status = 35 then '不处理'
when f.from_asm_response = -1 then '未确认' 
when f.from_asm_response = 1 then '已接受' 
when f.from_asm_response = 0 then '待仲裁' 
else '-' end as '判定状态',
case when r3.local_marketing_status = 1 then '是' when r3.local_marketing_status = 0 then '否' else '-' end as '非本地营销（ABM）',
IIF(r3.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r3.punish_flag & 2 > 0 and r3.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r3.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r3.punish_flag & 32 > 0 and r3.attribute8 is not null, '扣除2021年第' + 
case when r3.attribute7='1' then '一' when r3.attribute7='2' then '二' 
when r3.attribute7='3' then '三'
when r3.attribute7='4' then '四' end + '季度的季度奖励的' + r3.attribute8 + '%;','')
+ IIF(r3.punish_flag & 16 > 0 and r3.attribute6 is not null, '扣除2021年的年终奖励的' + r3.attribute6 + '%;','')
+ IIF(r3.punish_flag & 4 > 0 and r3.other_reasons is not null,'其他（' + r3.other_reasons +' ）;','') as 'ABM处罚内容',
case when r3.kpi_result = 1 then '合格' when r3.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（ABM）',
r3.handle_info as 'ABM建议',
case when r3.attribute1='1' then '是' else '否' end "处罚文件已确认",
CONVERT(varchar(100), ei.deliver_time, 20) "处罚文件发放时间",
case when r2.local_marketing_status = 1 then '是' when r2.local_marketing_status = 0 then '否' else '-' end as '非本地营销（被窜货ASM）',
IIF(r2.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r2.punish_flag & 2 > 0 and r2.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r2.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r2.punish_flag & 32 > 0 and r2.attribute8 is not null, '扣除2021年第' + 
case when r2.attribute7='1' then '一' when r2.attribute7='2' then '二' 
when r2.attribute7='3' then '三'
when r2.attribute7='4' then '四' end + '季度的季度奖励的' + r2.attribute8 + '%;','')
+ IIF(r2.punish_flag & 16 > 0 and r2.attribute6 is not null, '扣除2021年的年终奖励的' + r2.attribute6 + '%;','')
+ IIF(r2.punish_flag & 4 > 0 and r2.other_reasons is not null,'其他（' + r2.other_reasons +' ）','') as '被窜货ASM处罚内容',
case when r2.kpi_result = 1 then '合格' when r2.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（被窜货ASM）',
r2.handle_info as '被窜货ASM建议',
case when r1.local_marketing_status = 1 then '是' when r1.local_marketing_status = 0 then '否' else '-' end as '非本地营销（窜货ASM）',
IIF(r1.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r1.punish_flag & 2 > 0 and r1.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r1.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r1.punish_flag & 32 > 0 and r1.attribute8 is not null, '扣除2021年第' + 
case when r1.attribute7='1' then '一' when r1.attribute7='2' then '二' 
when r1.attribute7='3' then '三'
when r1.attribute7='4' then '四' end + '季度的季度奖励的' + r1.attribute8 + '%;','')
+ IIF(r1.punish_flag & 16 > 0 and r1.attribute6 is not null, '扣除2021年的年终奖励的' + r1.attribute6 + '%;','')
+ IIF(r1.punish_flag & 4 > 0 and r1.other_reasons is not null,'其他（' + r1.other_reasons +' ）','') as '窜货ASM处罚内容',
case when r1.kpi_result = 1 then '合格' when r1.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（窜货ASM）',
r1.handle_info as '窜货ASM建议',
case when r0.local_marketing_status = 1 then '是' when r0.local_marketing_status = 0 then '否' else '-' end as '非本地营销（窜货FLSR）',
r0.handle_info as '窜货FLSR建议'
from wx_t_Main_Local_marketing m
left join wx_t_non_local_mkt_flow_form f on f.report_id  = m.id 
left join wx_t_loc_mkt_product_info b on f.id = b.flow_id
left join wx_t_non_local_mkt_flow_pro_res r0 on r0.flow_id = b.flow_id and r0.channel  = b.channel  and r0.setp_no  = 'FROM_FLSR_SUBMIT_SURVEY'
left join wx_t_non_local_mkt_flow_pro_res r1 on r1.flow_id = b.flow_id and r1.channel  = b.channel  and r1.setp_no in ('FROM_ASM_SUBMIT_SURVEY','GQ_OEM_HANDLER')
left join wx_t_non_local_mkt_flow_pro_res r2 on r2.flow_id = b.flow_id and r2.channel  = b.channel  and r2.setp_no  = 'TO_ASM_SUBMIT_FEEDBACK'
left join wx_t_non_local_mkt_flow_pro_res r3 on r3.flow_id = b.flow_id and r3.channel  = b.channel  and r3.setp_no  = 'ABM_CONFIRM'
left join (select distinct distributor_id,customer_name_cn,region as region_name,supervisor_cai from dw_customer_org_sales
where product_channel in ('Commercial','Consumer') and del_flag = 0 ) cs on cs.distributor_id = f.to_distributor_id
left join (select distinct distributor_id,customer_name_cn,region as region_name,supervisor_cai from dw_customer_org_sales
where product_channel in ('Commercial','Consumer') and del_flag = 0 ) cs1 on cs1.distributor_id = f.from_distributor_id
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = f.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_dic_item dic1 on cast(dic1.dic_item_code as bigint) = f.to_distributor_id and dic1.dic_type_code = 'non_local_special_partner'
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id = f.from_distributor_id
left join wx_t_region c on c.id = m.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = m.reprot_type and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.channel and d2.dic_type_code  = 'non_local_channel'
left join wx_t_non_local_mkt_emaile_info ei on ei.source_id=r3.id
where b.logistics_code in ('102938223108') 






