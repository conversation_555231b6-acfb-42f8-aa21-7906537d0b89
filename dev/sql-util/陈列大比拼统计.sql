SELECT crss.region_name AS "Region", crss.sales_name AS "FLSR", O.organization_name AS "Dist.", u.ch_name AS "DSR", 
w.id, w.work_shop_name AS "Store"
	, r3.region_name AS "省", r2.region_name AS "市", r1.region_name AS "区", w.work_shop_address AS "门店地址", 
	w.workshop_owner "店主姓名", w.workshop_owner_mobile "店主手机号", w.workshop_tel "门店电话", w.contact_person "门店联系人",
	w.contact_person_tel "门店联系电话", case when w.is_multiple=1 then '是' else '否' end "是否连锁",
	case when w.shop_Recruitment=1 then '是' else '否' end "是否店招",
	case when w.join_Shop_Gift_Plan=1 then '是' else '否' end "雪佛龙养护大咖汇计划",
	case when w.join_Location_Plan=1 then '是' else '否' end "参与门店定位计划",
	case when w.ext_flag & 16>0 then '是' else '否' end "是否加入网站门店导航",
	w.type AS "门店类型", w.area "门店面积(m2)",
	w.seats_Num "工位数",w.trench_Numb "地沟数", w.two_Column_Elevator_Numb "两柱举升机数",
	w.four_Column_Elevator_Numb "四柱举升机数", w.business_Scope "营业范围",
	w.monthly_oil_sales_volume "月机油使用量（升）", 
	w.monthly_chevron_oil_sales_volume "月金富力机油使用量（升）", CASE 
		WHEN w.status = '3' THEN '合作客户'
		WHEN w.status = '1' THEN '已功店'
		when w.status='0' then '潜在客户'
		ELSE ''
	END AS "门店状态", 
	case when w.dms_key is not null and w.dms_key!='' then '是' else '否' end "DMS门店"
	, w.volume_month AS "月销量(L)", w.sale_major_delo_product AS "主要销售产品"
	, case when s.check_evaluation>0 then s.check_evaluation else null end AS "评分（分数）",
	s.check_result "抽查备注", CASE 
		WHEN s.task_status = 3 THEN '不合格'
		WHEN s.check_evaluation > 0 THEN '合格'
		ELSE '未抽查'
	END AS "状态", s.xg_sj AS "提交时间"
FROM wx_task_main m
	LEFT JOIN wx_task_sub s ON m.task_main_id = s.task_main_id
	LEFT JOIN wx_t_work_shop w ON s.org_id = w.id
	LEFT JOIN wx_t_organization o ON o.id = m.tenant_id
	LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
	LEFT JOIN dw_customer_region_sales_supervisor_rel crss
	ON crss.distributor_id = pe.distributor_id
		AND crss.region_name LIKE 'CDM%'
	LEFT JOIN wx_t_user u ON u.user_id = s.exec_user
	LEFT JOIN wx_t_region r1 ON r1.id = w.region_id
	LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
	LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
WHERE m.tmb_type_code = 'TT_2_CDMCLZX'
	AND s.task_status IN ('3', '4')
	and o.id !=9
ORDER BY crss.region_name, crss.sales_cai, o.id, s.exec_user, w.region_id