select o.organization_name, w.id "门店ID", w.work_shop_name, w.work_shop_address, 
w.create_time, w.ext_property22 "确认时间" from wx_t_work_shop w 
left join wx_t_organization o on o.id=w.partner_id
where w.create_time>='2021-04-01' and w.create_time<'2021-05-01'
and w.ext_property22>='2021-05-01'
and w.ext_flag&256>0
order by o.id


--drop table wx_t_work_shop20210512
select * --into wx_t_work_shop20210512 
--update w set w.activation_time=dateadd(day, DATEDIFF(day, w.ext_property22, '2021-04-30'), w.ext_property22),ext_property22=dateadd(day, DATEDIFF(day, w.ext_property22, '2021-04-30'), w.ext_property22)
from wx_t_work_shop w where id in (182517,
182699,
183044,
182535,
183103,
182931,
182901,
183131,
182927,
182658,
182306,
182484,
183006,
182860,
182883,
182875,
182982,
182870,
182885,
182874,
183013,
182852,
182977,
182430,
182991,
182855,
183005,
182344,
182856,
182610,
182276,
183096,
182130,
182268,
182273,
182266,
182073,
183063,
182887,
182412,
182749,
182759,
182064,
182350,
182068,
182153,
182209,
182065,
182143,
183099,
183017,
182725,
182785,
182330,
182472,
182431,
182965,
182938)



--drop table wx_t_workshop_status20210512

select * --into wx_t_workshop_status20210512
--update ws set create_time=dateadd(day, DATEDIFF(day, create_time, '2021-04-30'), create_time)
from wx_t_workshop_status ws
where ws.workshop_with_status=3 and ws.workshop_id in (182517,
182699,
182535,
183044,
183103,
182931,
182901,
183131,
182927,
182658,
182306,
182484,
183006,
182677,
182667,
182860,
182883,
182874,
182870,
182885,
182982,
182875,
183013,
182852,
182977,
182430,
182991,
182855,
183005,
182610,
182856,
182344,
182273,
182268,
182276,
182130,
182266,
183096,
182412,
182068,
182153,
182759,
182350,
182209,
182887,
182073,
182749,
183063,
182064,
182065,
182143,
183099,
183017,
182785,
182725,
182330,
182938,
182472,
182431,
182965)