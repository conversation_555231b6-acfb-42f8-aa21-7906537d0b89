select month(od.partner_confirm_time) "月份", max(o.organization_name) "经销商", 
--w.work_shop_name "客户名称", 
ol.sku, max(p.name) "产品名称", sum(ol.amount*CONVERT(float, p.capacity)) "升数" from wx_t_order od
left join wx_t_work_shop w on w.id=od.work_shop_id
left join wx_t_organization o on o.id=w.partner_id
left join wx_t_order_line ol on od.id=ol.order_id
left join wx_t_product p on p.sku=ol.sku
where od.partner_confirm_type=1 and od.partner_confirm_time>='2021-01-01'
 and od.partner_confirm_time<'2021-05-01'
 group by month(od.partner_confirm_time), o.id,ol.sku
 order by month(od.partner_confirm_time), o.id