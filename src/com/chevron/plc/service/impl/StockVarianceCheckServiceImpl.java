package com.chevron.plc.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chevron.plc.dao.StockVarianceCheckResultVoMapper;
import com.chevron.plc.model.InStockVo;
import com.chevron.plc.model.OutStockVo;
import com.chevron.plc.model.StockVarianceCheckResultVo;
import com.chevron.plc.model.StockVarianceCheckResultVoExample;
import com.chevron.plc.model.StockVarianceCheckResultVoParams;
import com.chevron.plc.service.AppInStockService;
import com.chevron.plc.service.AppOutStockService;
import com.chevron.plc.service.StockVarianceCheckService;
import com.common.constants.Constants;
import com.common.util.MessageResourceUtil;

@Service
public class StockVarianceCheckServiceImpl implements StockVarianceCheckService {

	@Autowired
	private StockVarianceCheckResultVoMapper stockVarianceCheckResultVoMapper;
	@Autowired
	private AppInStockService appInStockService;
	@Autowired
	private AppOutStockService appOutStockService;
	
	private final static Logger log = Logger.getLogger(StockVarianceCheckServiceImpl.class);
	
	@Override
	public void queryForPage(StockVarianceCheckResultVoParams params,
			Map<String, Object> resultMap) throws Exception {
		resultMap.put(Constants.RESULT_LST_KEY, stockVarianceCheckResultVoMapper.queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());

	}
	
	@Override
	public Map<String, Object> stocCheck(StockVarianceCheckResultVoParams params){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String stockNo = params.getStockNo();
		String stockType = params.getStockType();
		boolean checkResult = false;
		try{
			if(params.getId() != null && stockNo != null && !stockNo.equals("")){
				//进行校验
				if(stockType != null && stockType.equals("outstock")){
					//出库校验
					OutStockVo outstock = new OutStockVo();
					outstock.setStockOutNo(stockNo);
					checkResult = appOutStockService.checkOutStockBoxCodeProductAmount(outstock);
				}else if(stockType != null && stockType.equals("instock")){
					//入库校验
					InStockVo instock = new InStockVo();
					instock.setStockInNo(stockNo);
					checkResult = appInStockService.checkInStockBoxCodeProductAmount(instock);
				}
			}
			if(checkResult){//判断校验是否成功
				//根据Id更新状态为0
				StockVarianceCheckResultVo record = new StockVarianceCheckResultVo();
				record.setStatus("0");
				StockVarianceCheckResultVoExample example = new StockVarianceCheckResultVoExample();
				example.createCriteria().andStockNoEqualTo(stockNo);
				stockVarianceCheckResultVoMapper.updateByExampleSelective(record, example);
			}
			resultMap.put("checkResult", checkResult);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		}catch(Exception e){
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}

}
