package com.chevron.b2b.business.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.chevron.b2b.business.B2bVerifyPointPolicyBizService;
import com.chevron.b2b.dao.B2bVerifyPointPolicyMapper;
import com.chevron.b2b.model.B2bVerifyPointPolicy;
import com.chevron.b2b.model.B2bVerifyPointPolicyExample;
import com.chevron.b2b.model.B2bVerifyPointPolicyParams;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.model.ProductVo;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.StringUtils;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.PartnerView;

/**
 * B2B核销积分政策业务接口实现类
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
@Service
public class B2bVerifyPointPolicyBizServiceImpl implements B2bVerifyPointPolicyBizService {
	@Autowired
	private B2bVerifyPointPolicyMapper b2bVerifyPointPolicyMapper;
	
	@Autowired
	private OrganizationVoMapper organizationVoMapper;
	
	@Autowired
	private WorkshopMasterMapper workshopMasterMapper;
	
	@Autowired
	private ProductVoMapper productVoMapper;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void save(B2bVerifyPointPolicy record) throws WxPltException {
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();
		if(record.getEnableFlag() != null){
			//启用/停用操作
			B2bVerifyPointPolicy newPolicy = new B2bVerifyPointPolicy();
			newPolicy.setEnableFlag(record.getEnableFlag());
			newPolicy.setUpdateTime(now);
			newPolicy.setUpdateUserId(userId);
			newPolicy.setId(record.getId());
			b2bVerifyPointPolicyMapper.updateByPrimaryKeySelective(newPolicy);
		}else{
			//删除原政策
			if(record.getId() != null) {
				B2bVerifyPointPolicyExample example = new B2bVerifyPointPolicyExample();
				example.createCriteria().andIdEqualTo(record.getId());
				B2bVerifyPointPolicy newPolicy = new B2bVerifyPointPolicy();
				newPolicy.setDeleteFlag(1);
				newPolicy.setUpdateTime(now);
				newPolicy.setUpdateUserId(userId);
				b2bVerifyPointPolicyMapper.updateByExampleSelective(newPolicy, example);
			}else {
				record.setId(null);
			}
			record.setEnableFlag(1);
			record.setDeleteFlag(0);
			record.setCreateUserId(userId);
			record.setCreateTime(now);
			b2bVerifyPointPolicyMapper.insertSelective(record);
		}
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		B2bVerifyPointPolicyExample example = new B2bVerifyPointPolicyExample();
		example.createCriteria().andIdIn(ids);
		B2bVerifyPointPolicy newPolicy = new B2bVerifyPointPolicy();
		newPolicy.setDeleteFlag(1);
		newPolicy.setUpdateTime(DateUtil.getCurrentDate());
		newPolicy.setUpdateUserId(ContextUtil.getCurUserId());
		b2bVerifyPointPolicyMapper.updateByExampleSelective(newPolicy, example);
//		b2bVerifyPointPolicyMapper.deleteByExample(example);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(B2bVerifyPointPolicyExample example) throws WxPltException {
		b2bVerifyPointPolicyMapper.deleteByExample(example);
	}

	@Override
	public List<B2bVerifyPointPolicy> queryByExample(B2bVerifyPointPolicyExample example) throws WxPltException {
		return b2bVerifyPointPolicyMapper.selectByExample(example);
	}
//
//	@Override
//    public List<B2bVerifyPointPolicy> queryByParams(Map<String, Object> params) throws WxPltException {
//    	return b2bVerifyPointPolicyMapper.queryByParams(params);
//    }

	@Override
	public B2bVerifyPointPolicy getBean(Long id) throws WxPltException{
		return b2bVerifyPointPolicyMapper.selectByPrimaryKey(id);
	}	

	@Override
	public void queryForPage(B2bVerifyPointPolicyParams params, Map<String, Object> resultMap) throws WxPltException {
		//组装经销商数据
		List<B2bVerifyPointPolicy> b2bVerifyPointPolicies = b2bVerifyPointPolicyMapper.queryForPage(params);
		if(!b2bVerifyPointPolicies.isEmpty()) {
			Set<String> skuSet = new HashSet<String>();
			for(B2bVerifyPointPolicy b2bVerifyPointPolicy:b2bVerifyPointPolicies) {
				List<Long> distributorIds = new ArrayList<Long>();
				String distributorIdList = b2bVerifyPointPolicy.getDistributorIds();
				if(StringUtils.isNotEmpty(distributorIdList)) {
					if(distributorIdList.contains(",")) {
						String[] stringDistributos = distributorIdList.split(",");
						for(int i=0;i<stringDistributos.length;i++) {
							distributorIds.add(Long.valueOf(stringDistributos[i]));
						}
					}else {
						distributorIds.add(Long.valueOf(distributorIdList));
					}
				}
				if(distributorIds.size()>0) {
					Map<String, Object> paraMap = new HashMap<String, Object>();
					paraMap.put("distributorIdsList", distributorIds);
				List<PartnerView> distributorIdVo = organizationVoMapper.getDistributors(paraMap);
				b2bVerifyPointPolicy.setDistributorVo(distributorIdVo);
				}
				
				List<Long> workShopIds = new ArrayList<Long>();
				String workShopIdsList = b2bVerifyPointPolicy.getWorkShopIds();
				if(StringUtils.isNotEmpty(workShopIdsList)) {
					if(workShopIdsList.contains(",")) {
						String[] stringWorkShops = workShopIdsList.split(",");
						for(int i=0;i<stringWorkShops.length;i++) {
							workShopIds.add(Long.valueOf(stringWorkShops[i]));
						}
					}else {
						workShopIds.add(Long.valueOf(workShopIdsList));
					}
				}
				if(workShopIds.size()>0) {
					Map<String, Object> paraMap = new HashMap<String, Object>();
					paraMap.put("ids", workShopIds);
					List<WorkshopMaster> workShopVos = workshopMasterMapper.querySimpleByParams(paraMap);
					b2bVerifyPointPolicy.setWorkShopVo(workShopVos);
				}
				if(StringUtils.isNotBlank(b2bVerifyPointPolicy.getSku())) {
					skuSet.addAll(Arrays.asList(b2bVerifyPointPolicy.getSku().split(",")));
				}
			}
			//初始化产品
			if(!skuSet.isEmpty()) {
				initPolicyProducts(b2bVerifyPointPolicies, skuSet);
			}

		}
		resultMap.put(Constants.RESULT_LST_KEY, b2bVerifyPointPolicies);
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}
	
	//初始化规则产品
	protected void initPolicyProducts(List<B2bVerifyPointPolicy> b2bVerifyPointPolicies, Set<String> skuSet) {
		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("skuList", skuSet);
		List<ProductVo> productVos = productVoMapper.getProductsByParam(reqMap);
		if(!productVos.isEmpty()) {
			Map<String, ProductVo> productMap = new HashMap<String, ProductVo>(productVos.size());
			for(ProductVo vo : productVos) {
				productMap.put(vo.getSku(), vo);
			}
			for(B2bVerifyPointPolicy pointPolicy : b2bVerifyPointPolicies) {
				if(StringUtils.isNotBlank(pointPolicy.getSku())) {
					String[] skus = pointPolicy.getSku().split(",");
					pointPolicy.setProducts(new ArrayList<ProductVo>(skus.length));
					for(String sku : skus) {
						if(productMap.containsKey(sku)) {
							pointPolicy.getProducts().add(productMap.get(sku));
						}
					}
				}
			}
		}
	}
}
