package com.chevron.task.dao;

import com.sys.auth.model.WxSimpleUser;
import com.chevron.task.model.WxRelationUser;
import com.chevron.task.model.WxRelationUserExample;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WxRelationUserMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int countByExample(WxRelationUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int deleteByExample(WxRelationUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int deleteByPrimaryKey(Long relationUserId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int insert(WxRelationUser record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int insertSelective(WxRelationUser record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	List<WxRelationUser> selectByExample(WxRelationUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	WxRelationUser selectByPrimaryKey(Long relationUserId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int updateByExampleSelective(@Param("record") WxRelationUser record,
			@Param("example") WxRelationUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int updateByExample(@Param("record") WxRelationUser record,
			@Param("example") WxRelationUserExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int updateByPrimaryKeySelective(WxRelationUser record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_relation_user
	 * @mbggenerated  Thu Jul 16 17:55:17 CST 2015
	 */
	int updateByPrimaryKey(WxRelationUser record);
	
	List<WxSimpleUser> selectExecUserOrWatchUser(WxRelationUserExample example);

	int insertByBatch(List<WxRelationUser> wxRelationUserList);
}