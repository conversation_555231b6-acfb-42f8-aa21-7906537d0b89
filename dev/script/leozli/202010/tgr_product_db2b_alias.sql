alter table wx_t_product
   add db2b_name_alias varchar(256)
   
create trigger tgr_product_db2b_alias
on wx_t_product
    for update
as
    declare @oldName nvarchar(256), @newName nvarchar(256), @sku nvarchar(256);
    --更新前的数据
    select @oldName = db2b_name_alias, @sku=sku from deleted;
    --更新后的数据
    select @newName = db2b_name_alias from inserted;
    if (@newName is not null and @newName!='' and (@oldName is null or @oldName!=@newName) and exists (select 1 from wx_t_db2b_product where sku=@sku))
        begin
            update wx_t_db2b_product set product_name=@newName where sku=@sku;
        end
go