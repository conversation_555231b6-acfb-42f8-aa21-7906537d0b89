package com.chevron.b2b.dao;

import com.chevron.b2b.model.ScheduleInstance;
import com.chevron.b2b.model.ScheduleInstanceExample;
import com.chevron.b2b.model.ScheduleInstanceParams;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 计划实例-wx_t_schedule_instance数据库操作对象
 * <AUTHOR>
 * @version 1.0 2018-12-20 17:16
 */
public interface ScheduleInstanceMapper {

	/**
	 * 根据主键更新计划实例对象(非空属性)
	 * @param record 新计划实例对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(ScheduleInstance record);

    /**
     * 根据主键删除计划实例对象
     * @param id 删除计划实例对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询计划实例对象
	 * @param id 计划实例对象主键值
	 * @return 计划实例对象结果
	 */
    ScheduleInstance selectByPrimaryKey(Long id);

 	/**
	 * 插入计划实例对象(非空属性)
	 * @param record 被插入计划实例对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(ScheduleInstance record);

	/**
	 * 更新计划实例对象
	 * @param record 被修改计划实例对象
	 * @param example 过滤计划实例对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") ScheduleInstance record, @Param("example") ScheduleInstanceExample example);

	/**
	 * 删除计划实例对象
	 * @param example 被删除计划实例对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleInstanceExample example);
    
 	/**
	 * 查询满足条件的计划实例对象数量
	 * @param example 过滤计划实例对象条件
	 * @return 满足条件计划实例对象数量
	 */
    int countByExample(ScheduleInstanceExample example);

	/**
	 * 单表查询计划实例对象
	 * @param example 过滤计划实例对象条件
	 * @return 满足条件计划实例对象集合
	 */
    List<ScheduleInstance> selectByExample(ScheduleInstanceExample example);

	/**
	 * 列表查询计划实例对象
	 * @param params 查询条件
	 * @return 满足条件计划实例对象集合
	 */
    List<ScheduleInstance> queryByParams(Map<String, Object> params);

	/**
	 * 分页查询计划实例对象
	 * @param params 查询条件
	 * @return 满足条件计划实例对象集合
	 */
    List<ScheduleInstance> queryForPage(ScheduleInstanceParams params);
	
	int insertBatchWhenPublish(Map<String, Object> params);
	
    int insertByGetBean(ScheduleInstance record);
    
    ScheduleInstance getBeanByUks(Map<String, Object> uks);
    
    ScheduleInstance selectShareInfo(Map<String, Object> params);
    
    ScheduleInstance selectShareDetail(Map<String, Object> params);
}
