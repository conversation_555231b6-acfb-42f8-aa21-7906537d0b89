update he set he.url='/SPA/purchase-order/index.jsp#/list'
from wx_t_homepagev2_entry he where he.id=110

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('partnersaleconfig.payment.term', 'cash_credit', 'CASH & CREDIT', '', '1', 10);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('ProductChannel', '产品渠道配置', '产品渠道配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('ProductChannel', 'consumer', '乘用车', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('ProductChannel', 'commercial', '商用油', '', '1', 20);

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductChannelMapping','CDM','consumer',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductChannelMapping','C&I','commercial',1,getdate());

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('product.productLine', '产品系列配置', '产品系列配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.productLine', 'DELO400', '德乐400系列', '', '1', 1100);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.productLine', 'GDELO', '金德乐系列', '', '1', 1300);

select * into wx_t_dic_item202107
from wx_t_dic_item d where dic_type_code in ('product.oilType','product.category')

delete d
from wx_t_dic_item d where dic_type_code in ('product.oilType','product.category')

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'GOLD', '金盾', '', '1', 110);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'SILVER', '银盾', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'STAR', '星盾', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'HC', '合成型', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'SFORM', '超级方程式', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'KW', '方程式', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'SCO', '超级精选', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'SENO', '超级加强型', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'ENO', '加强型', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'DELO600', '德乐600超低灰', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'DELO400S', '德乐400超霸', 'DELO400', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'DELO400_MGX', '德乐400 MGX', 'DELO400', '1', 20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'DELO400_RDS', '德乐400 RDS', 'DELO400', '1', 30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'DELO400_NG', '德乐400燃气', 'DELO400', '1', 40);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'SGDELO', '超级金德乐', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'GDELO', '金德乐', 'GDELO', '1', 50);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'GDELO_PLUS', '金德乐优选', 'GDELO', '1', 60);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'GDELO_NG', '金德乐燃气', 'GDELO', '1', 70);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.oilType', 'SDELO', '银德乐', '', '1', 10);

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'JY', '机油', '', '1', 100);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'ANTIFREEZE', '防冻防锈液', '', '1', 200);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'TRANSMISSION_FULID', '变速箱油', '', '1', 300);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'GEAR_OIL', '齿轮油', '', '1', 400);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'GREASES', '润滑脂', '', '1', 500);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'HYD_OIL', '液压油', '', '1', 600);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'TCP', '特劲', '', '1', 700);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'BRAKE', '刹车液', '', '1', 800);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.category', 'OTHERS', '其他', '', '1', 900);

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.capacity', '18', '18L', '', '1', 2.5);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.capacity', '208', '208L', '', '1', 50);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.capacity', '16.000', '16KG', '', '1', 60);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.capacity', '180.000', '180KG', '', '1', 70);

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.viscosity', '80W-90', '80W-90', '', '1', 100);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('product.viscosity', '85W-140', '85W-140', '', '1', 200);


select * into wx_t_product202107 from wx_t_product p

update p set p.support_order=0 from wx_t_product p where p.support_order=1

