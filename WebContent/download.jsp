<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>

<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() 
			+ path + "/"
			+ ":" + request.getServerPort();
	
	/*获取登录信息,此处无须判断是否登录，因为在url访问全县中已经限制*/
	//Subject subject = SecurityUtils.getSubject();		
	//ShiroUser user = (ShiroUser)subject.getPrincipal();
%>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>二维码下载</title>
        <link href="${ctx }common/bootstrap-4.6.2/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
		<link rel= "shortcut icon " href= "${ctx }images/favicon.ico">
		<script type="text/javascript" src="${ctx }common/js/jquery-1.9.1.min.js"></script>
		<script type="text/javascript" src="${ctx }common/bootstrap-4.6.2/js/bootstrap.min.js"></script>

<style type="text/css">
			*{margin:0; padding:0;}
			img{max-width: 100%; height: auto;}
.wxLogTitle {
	color: #FAF6F6 !important;
	font-size:38px;
    position: relative;
    top: -10px;
}
.wxLogTitleImg {
	margin-top: 10px !important;
	margin-bottom: 0px !important;
    float:left;
}
#validateCode:-ms-input-placeholder{
	color:#ff0
}
.header {
	width: 100%;
    background-color: #231F20;
    border-bottom: 1px solid #231F20;
    position:relative;left:0;
}
.mingdaoLogo {
    display: inline-block;
    width: 150px;
    height: 60px;
    background: url(/images/logo.jpg) center center no-repeat;
    background-size:100% 100%;
}
</style>
        <style type="text/css">
		</style>
    </head>
           
    <body>
	<div class="header">
		<span class="mingdaoLogo"></span>
		<span class="wxLogTitle">管店宝手机端下载</span>
    </div>
 		<div style="position:relative;left:20%;width:60%;top:300px">
         <button class="btn btn-default btn-lg" style="width:100%;height:150px;background:#da251c;color:#fff;font-size:36px;display:block;" onclick="gotoIOS()">iPhone 下载</button>
         <button class="btn btn-default btn-lg" style="width:100%;height:150px;background:#da251c;color:#fff;font-size:36px;display:block;margin-top:80px" onclick="gotoAndroid()">Android 下载</button>
        </div>
    </body>
    
 
        <script type="text/javascript">
            /*
             * 智能机浏览器版本信息:
             *
             */
            var browser = {
                versions: function() {
                    var u = navigator.userAgent, app = navigator.appVersion;
                    return {//移动终端浏览器版本信息
                        trident: u.indexOf('Trident') > -1, //IE内核
                        presto: u.indexOf('Presto') > -1, //opera内核
                        webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
                        gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
                        mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/), //是否为移动终端
                        ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
                        android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或者uc浏览器
                        iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1, //是否为iPhone或者QQHD浏览器
                        iPad: u.indexOf('iPad') > -1, //是否iPad
                        webApp: u.indexOf('Safari') == -1 //是否web应该程序，没有头部与底部
                    };
                }(),
                language: (navigator.browserLanguage || navigator.language).toLowerCase()
            };
            
            function is_weixin() {
			    var ua = navigator.userAgent.toLowerCase();
			    if (ua.match(/MicroMessenger/i) == "micromessenger") {
			        return true;
			    } else {
			        return false;
			    }
			}
			
			var isWeixin = is_weixin();
			var winHeight = typeof window.innerHeight != 'undefined' ? window.innerHeight : document.documentElement.clientHeight;
			function loadHtml(){
				var div = document.createElement('div');
				div.id = 'weixin-tip';
				div.innerHTML = '<p><img src="images/live_weixin.png" alt="微信打开"/></p>';
				document.body.appendChild(div);
			}
			
			function loadStyleText(cssText) {
		        var style = document.createElement('style');
		        style.rel = 'stylesheet';
		        style.type = 'text/css';
		        try {
		            style.appendChild(document.createTextNode(cssText));
		        } catch (e) {
		            style.styleSheet.cssText = cssText; //ie9以下
		        }
	            var head=document.getElementsByTagName("head")[0]; //head标签之间加上style样式
	            head.appendChild(style); 
		    }
		    var cssText = "#weixin-tip{position: fixed; left:0; top:0; background: rgba(0,0,0,0.8); filter:alpha(opacity=80); width: 100%; height:100%; z-index: 100;} #weixin-tip p{text-align: center; margin-top: 10%; padding:0 5%;}";
			if(isWeixin){
				loadHtml();
				loadStyleText(cssText);
			}
	        //if (browser.versions.ios || browser.versions.iPhone || browser.versions.iPad) {
	        //     window.location.href="itms-services://?action=download-manifest&url=<%=basePath %>app/anta-plus.plist";
	        //}else if (browser.versions.android) {
	       	//	window.location.href=
	        //}
	        function gotoIOS(){
	        	//var str = "itms-services://?action=download-manifest&url=<%=basePath %>//app/anta-plus.plist";
	        	//alert(str);
	             var str = "https://www.pgyer.com/bPbn";
	             window.location.href=str;
	        }
	        function gotoAndroid(){
	       		window.location.href="${ctx }app/Chevron_pro.apk";
	        }
        </script>
</html>
