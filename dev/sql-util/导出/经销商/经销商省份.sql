select o.organization_name "经销商名称", 
case when o.status=1 then 'Active' else 'Inactive' end "状态",
(select distinct c.region from dw_customer_org_sales c where c.distributor_id=pe.distributor_id and c.region in (select dic_item_code from wx_t_dic_item where dic_type_code='Region.Indirect')) region,
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配城市"
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where o.type=1 --and o.status=1 
and o.id!=9



select o.organization_name, 
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.city_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name city_name, r1.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配城市"
from wx_t_organization o where o.type=1 --and o.status=1 
and o.id!=9

select o.organization_name "经销商名称", pe.sap_code "SAP Code", 
case when o.status=1 then 'Active' else 'Inactive' end "状态",
(select distinct c.region from dw_customer_org_sales c where c.distributor_id=pe.distributor_id and c.region in (select dic_item_code from wx_t_dic_item where dic_type_code='Region.Indirect')) region,
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id and rp.channel_weight&1>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "Consumer授权城市",
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id and rp.channel_weight&2>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "Commercial授权城市"
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where o.type=1 --and o.status=1 
and o.id!=9



select o.organization_name "经销商名称", pe.sap_code "SAP Code", 
case when o.status=1 then 'Active' else 'Inactive' end "状态",
(select distinct c.region from dw_customer_org_sales c where c.distributor_id=pe.distributor_id and c.region in (select dic_item_code from wx_t_dic_item where dic_type_code='Region.Indirect')) region,
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id and rp.channel_weight&1>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "Consumer授权城市",
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id and rp.channel_weight&2>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "Commercial授权城市",
case when exists (select 1 from dw_access_control_customer_org_sales crss where crss.distributor_id=pe.distributor_id and crss.channel_weight&1>0) then '是' else '否' end "Consumer Target",
case when exists (select 1 from dw_access_control_customer_org_sales crss where crss.distributor_id=pe.distributor_id and crss.channel_weight&2>0) then '是' else '否' end "Commercial Target"
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where o.type=1 --and o.status=1 
and o.id!=9
