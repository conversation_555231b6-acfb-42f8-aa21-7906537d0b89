select * from wx_t_db2b_order do left join wx_t_db2b_order_line dl on dl.order_id=do.id
left join wx_t_db2b_product dp on dp.id=dl.product_id
where exists (select 1
from wx_t_dic_item d where dic_type_code='b2b_product_special_field' and dp.product_name like '%' + d.dic_item_name + '%')



select o.organization_name, dp.product_name from wx_t_db2b_product dp left join wx_t_organization o on o.id=dp.partner_id
where dp.status=20 and exists (select 1
from wx_t_dic_item d where dic_type_code='b2b_product_special_field' and dp.product_name like '%' + d.dic_item_name + '%')
order by o.id