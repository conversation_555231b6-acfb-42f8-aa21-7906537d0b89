package com.chevron.sellin.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 促销礼品变更日志单表查询条件
 * <AUTHOR>
 * @version 1.0 2020-08-06 16:43
 */
public class PromotionGiftModifyLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PromotionGiftModifyLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPoolIdIsNull() {
            addCriterion("pool_id is null");
            return (Criteria) this;
        }

        public Criteria andPoolIdIsNotNull() {
            addCriterion("pool_id is not null");
            return (Criteria) this;
        }

        public Criteria andPoolIdEqualTo(Long value) {
            addCriterion("pool_id =", value, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdNotEqualTo(Long value) {
            addCriterion("pool_id <>", value, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdGreaterThan(Long value) {
            addCriterion("pool_id >", value, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pool_id >=", value, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdLessThan(Long value) {
            addCriterion("pool_id <", value, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdLessThanOrEqualTo(Long value) {
            addCriterion("pool_id <=", value, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdIn(List<Long> values) {
            addCriterion("pool_id in", values, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdNotIn(List<Long> values) {
            addCriterion("pool_id not in", values, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdBetween(Long value1, Long value2) {
            addCriterion("pool_id between", value1, value2, "poolId");
            return (Criteria) this;
        }

        public Criteria andPoolIdNotBetween(Long value1, Long value2) {
            addCriterion("pool_id not between", value1, value2, "poolId");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTypeIsNull() {
            addCriterion("modify_type is null");
            return (Criteria) this;
        }

        public Criteria andModifyTypeIsNotNull() {
            addCriterion("modify_type is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTypeEqualTo(String value) {
            addCriterion("modify_type =", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeNotEqualTo(String value) {
            addCriterion("modify_type <>", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeGreaterThan(String value) {
            addCriterion("modify_type >", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeGreaterThanOrEqualTo(String value) {
            addCriterion("modify_type >=", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeLessThan(String value) {
            addCriterion("modify_type <", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeLessThanOrEqualTo(String value) {
            addCriterion("modify_type <=", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeIn(List<String> values) {
            addCriterion("modify_type in", values, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeNotIn(List<String> values) {
            addCriterion("modify_type not in", values, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeBetween(String value1, String value2) {
            addCriterion("modify_type between", value1, value2, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeNotBetween(String value1, String value2) {
            addCriterion("modify_type not between", value1, value2, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityIsNull() {
            addCriterion("modify_quantity is null");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityIsNotNull() {
            addCriterion("modify_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityEqualTo(Integer value) {
            addCriterion("modify_quantity =", value, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityNotEqualTo(Integer value) {
            addCriterion("modify_quantity <>", value, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityGreaterThan(Integer value) {
            addCriterion("modify_quantity >", value, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("modify_quantity >=", value, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityLessThan(Integer value) {
            addCriterion("modify_quantity <", value, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("modify_quantity <=", value, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityIn(List<Integer> values) {
            addCriterion("modify_quantity in", values, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityNotIn(List<Integer> values) {
            addCriterion("modify_quantity not in", values, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityBetween(Integer value1, Integer value2) {
            addCriterion("modify_quantity between", value1, value2, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("modify_quantity not between", value1, value2, "modifyQuantity");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNull() {
            addCriterion("business_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNotNull() {
            addCriterion("business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeEqualTo(String value) {
            addCriterion("business_type =", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotEqualTo(String value) {
            addCriterion("business_type <>", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThan(String value) {
            addCriterion("business_type >", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThanOrEqualTo(String value) {
            addCriterion("business_type >=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThan(String value) {
            addCriterion("business_type <", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThanOrEqualTo(String value) {
            addCriterion("business_type <=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIn(List<String> values) {
            addCriterion("business_type in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotIn(List<String> values) {
            addCriterion("business_type not in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeBetween(String value1, String value2) {
            addCriterion("business_type between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotBetween(String value1, String value2) {
            addCriterion("business_type not between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyIsNull() {
            addCriterion("business_key is null");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyIsNotNull() {
            addCriterion("business_key is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyEqualTo(String value) {
            addCriterion("business_key =", value, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyNotEqualTo(String value) {
            addCriterion("business_key <>", value, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyGreaterThan(String value) {
            addCriterion("business_key >", value, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyGreaterThanOrEqualTo(String value) {
            addCriterion("business_key >=", value, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyLessThan(String value) {
            addCriterion("business_key <", value, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyLessThanOrEqualTo(String value) {
            addCriterion("business_key <=", value, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyIn(List<String> values) {
            addCriterion("business_key in", values, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyNotIn(List<String> values) {
            addCriterion("business_key not in", values, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyBetween(String value1, String value2) {
            addCriterion("business_key between", value1, value2, "businessKey");
            return (Criteria) this;
        }

        public Criteria andBusinessKeyNotBetween(String value1, String value2) {
            addCriterion("business_key not between", value1, value2, "businessKey");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNull() {
            addCriterion("comments is null");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNotNull() {
            addCriterion("comments is not null");
            return (Criteria) this;
        }

        public Criteria andCommentsEqualTo(String value) {
            addCriterion("comments =", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotEqualTo(String value) {
            addCriterion("comments <>", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThan(String value) {
            addCriterion("comments >", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("comments >=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThan(String value) {
            addCriterion("comments <", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThanOrEqualTo(String value) {
            addCriterion("comments <=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsIn(List<String> values) {
            addCriterion("comments in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotIn(List<String> values) {
            addCriterion("comments not in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsBetween(String value1, String value2) {
            addCriterion("comments between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotBetween(String value1, String value2) {
            addCriterion("comments not between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNull() {
            addCriterion("ext_property1 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNotNull() {
            addCriterion("ext_property1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1EqualTo(String value) {
            addCriterion("ext_property1 =", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotEqualTo(String value) {
            addCriterion("ext_property1 <>", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThan(String value) {
            addCriterion("ext_property1 >", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property1 >=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThan(String value) {
            addCriterion("ext_property1 <", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThanOrEqualTo(String value) {
            addCriterion("ext_property1 <=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1In(List<String> values) {
            addCriterion("ext_property1 in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotIn(List<String> values) {
            addCriterion("ext_property1 not in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1Between(String value1, String value2) {
            addCriterion("ext_property1 between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotBetween(String value1, String value2) {
            addCriterion("ext_property1 not between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNull() {
            addCriterion("ext_property2 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNotNull() {
            addCriterion("ext_property2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2EqualTo(String value) {
            addCriterion("ext_property2 =", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotEqualTo(String value) {
            addCriterion("ext_property2 <>", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThan(String value) {
            addCriterion("ext_property2 >", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property2 >=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThan(String value) {
            addCriterion("ext_property2 <", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThanOrEqualTo(String value) {
            addCriterion("ext_property2 <=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2In(List<String> values) {
            addCriterion("ext_property2 in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotIn(List<String> values) {
            addCriterion("ext_property2 not in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2Between(String value1, String value2) {
            addCriterion("ext_property2 between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotBetween(String value1, String value2) {
            addCriterion("ext_property2 not between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNull() {
            addCriterion("ext_property3 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNotNull() {
            addCriterion("ext_property3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3EqualTo(String value) {
            addCriterion("ext_property3 =", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotEqualTo(String value) {
            addCriterion("ext_property3 <>", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThan(String value) {
            addCriterion("ext_property3 >", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property3 >=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThan(String value) {
            addCriterion("ext_property3 <", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThanOrEqualTo(String value) {
            addCriterion("ext_property3 <=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3In(List<String> values) {
            addCriterion("ext_property3 in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotIn(List<String> values) {
            addCriterion("ext_property3 not in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3Between(String value1, String value2) {
            addCriterion("ext_property3 between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotBetween(String value1, String value2) {
            addCriterion("ext_property3 not between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNull() {
            addCriterion("ext_property4 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNotNull() {
            addCriterion("ext_property4 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4EqualTo(String value) {
            addCriterion("ext_property4 =", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotEqualTo(String value) {
            addCriterion("ext_property4 <>", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThan(String value) {
            addCriterion("ext_property4 >", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property4 >=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThan(String value) {
            addCriterion("ext_property4 <", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThanOrEqualTo(String value) {
            addCriterion("ext_property4 <=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4In(List<String> values) {
            addCriterion("ext_property4 in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotIn(List<String> values) {
            addCriterion("ext_property4 not in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4Between(String value1, String value2) {
            addCriterion("ext_property4 between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotBetween(String value1, String value2) {
            addCriterion("ext_property4 not between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty5IsNull() {
            addCriterion("ext_property5 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty5IsNotNull() {
            addCriterion("ext_property5 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty5EqualTo(String value) {
            addCriterion("ext_property5 =", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotEqualTo(String value) {
            addCriterion("ext_property5 <>", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5GreaterThan(String value) {
            addCriterion("ext_property5 >", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property5 >=", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5LessThan(String value) {
            addCriterion("ext_property5 <", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5LessThanOrEqualTo(String value) {
            addCriterion("ext_property5 <=", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5In(List<String> values) {
            addCriterion("ext_property5 in", values, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotIn(List<String> values) {
            addCriterion("ext_property5 not in", values, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5Between(String value1, String value2) {
            addCriterion("ext_property5 between", value1, value2, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotBetween(String value1, String value2) {
            addCriterion("ext_property5 not between", value1, value2, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
