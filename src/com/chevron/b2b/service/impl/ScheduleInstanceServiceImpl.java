package com.chevron.b2b.service.impl;

import com.chevron.b2b.business.ScheduleInstanceBizService;
import com.chevron.b2b.dao.ScheduleInstanceMapper;
import com.chevron.b2b.dao.ScheduleItemInstanceMapper;
import com.chevron.b2b.model.ScheduleInstance;
import com.chevron.b2b.model.ScheduleInstanceParams;
import com.chevron.b2b.model.ScheduleItemInstance;
import com.chevron.b2b.service.ScheduleInstanceService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 计划实例操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2018-12-20 17:16
 */
@Service
public class ScheduleInstanceServiceImpl implements ScheduleInstanceService {
	
	@Autowired
	private ScheduleInstanceBizService scheduleInstanceBizService;
	
	@Resource
	private ScheduleItemInstanceMapper scheduleItemInstanceMapper;
	
	@Resource
	private ScheduleInstanceMapper scheduleInstanceMapper;
	
	private final static Logger log = Logger.getLogger(ScheduleInstanceServiceImpl.class);

	@Override
	public Map<String, Object> save(ScheduleInstance record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				scheduleInstanceBizService.insert(record);
			}else{
				scheduleInstanceBizService.update(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> delete(List<Long> ids) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			scheduleInstanceBizService.delete(ids);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public JsonResponse queryScheduleForePage(ScheduleInstanceParams params) {
		JsonResponse resultMap = new JsonResponse();
		log.info("queryScheduleForePage: " + JsonUtil.writeValue(params));
		try {
			scheduleInstanceBizService.queryForPage(params, resultMap);
			log.info("queryScheduleForePage success " );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.queryScheduleForePage", JsonUtil.writeValue(params));
		}
		return resultMap;
	}

	@Override
	public JsonResponse getScheduleInstance(Long id) {
		JsonResponse resultMap = new JsonResponse();
		log.info("getScheduleInstance: " + id);
		try {
			resultMap.setDataResult(scheduleInstanceBizService.getBean(id));
			log.info("getScheduleInstance success " );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.getScheduleInstance", id.toString());
		}
		return resultMap;
	}

	@Override
	public JsonResponse getScheduleInstanceForm(Long id) {
		JsonResponse resultMap = new JsonResponse();
		log.info("getScheduleInstanceForm: " + id);
		try {
			resultMap.setDataResult(queryScheduleInstanceForm(id));
			log.info("getScheduleInstanceForm success " );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.getScheduleInstanceForm", id.toString());
		}
		return resultMap;
	}
	
	protected List<ScheduleItemInstance> queryScheduleInstanceForm(Long id){
		Map<String, Object> params = new HashMap<String, Object>();
		if(id > 100000000000000l) {
			//不存在的活动执行实例
			params.put("scheduleEntityId", id - id / 10000000l * 10000000l);
		}else {
			params.put("scheduleInstanceId", id);
		}
		params.put("orderBy", "order_numb");
		return scheduleItemInstanceMapper.queryByParams(params);
	}

	@Override
	public JsonResponse saveScheduleInstanceForm(ScheduleInstance bean) {
		JsonResponse resultMap = new JsonResponse();
		log.info("saveScheduleInstanceForm: " + JsonUtil.writeValue(bean));
		try {
			scheduleInstanceBizService.update(bean);
			log.info("saveScheduleInstanceForm success " );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
			log.error(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.saveScheduleInstanceForm", JsonUtil.writeValue(bean));
		}
		return resultMap;
	}

	@Override
	public JsonResponse getApprovalInfo(Long id) {
		JsonResponse resultMap = new JsonResponse();
		log.info("getApprovalInfo: " + id);
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("id", id);
			params.put("includeAgreeNo", true);
			resultMap.setDataResult(scheduleInstanceBizService.queryByParams(params).get(0));
			resultMap.setListResult(queryScheduleInstanceForm(id));
			log.info("getApprovalInfo success " );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.getApprovalInfo", id.toString());
		}
		return resultMap;
	}

	@Override
	public JsonResponse getScheduleShareInfo(Long id, String clientId) {
		JsonResponse resultMap = new JsonResponse();
		log.info("getScheduleShareInfo: " + id);
		try {
			Map<String, Object> params = new HashMap<String, Object>(2);
			params.put("id", id);
			if(StringUtils.isNotBlank(clientId)) {
				params.put("clientId", clientId);
			}
			ScheduleInstance bean = scheduleInstanceMapper.selectShareInfo(params);
			resultMap.put("agreeCount", bean.getAgreeCount());
			resultMap.put("clientAgreeFlag", bean.getClientAgreeFlag());
			log.info("getScheduleShareInfo success " );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.getScheduleShareInfo", id.toString());
		}
		return resultMap;
	}

	@Override
	public JsonResponse getScheduleShareDetail(Long id, String clientId) {
		JsonResponse resultMap = new JsonResponse();
		log.info("getScheduleShareDetail: " + id);
		try {
			Map<String, Object> params = new HashMap<String, Object>(2);
			params.put("id", id);
			if(StringUtils.isNotBlank(clientId)) {
				params.put("clientId", clientId);
			}
			ScheduleInstance bean = scheduleInstanceMapper.selectShareDetail(params);
			bean.setItemList(queryScheduleInstanceForm(id));
			resultMap.setDataResult(bean);
			log.info("getScheduleShareDetail success " );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), "com.chevron.b2b.service.impl.ScheduleInstanceServiceImpl.getScheduleShareDetail", id.toString());
		}
		return resultMap;
	}
}
