select isnull(a.distributor_id, b.distributor_id) distributor_id,
isnull(a.partner_name, b.partner_name) partner_name, a.pending_score "5月积分", b.pending_score "6月积分"  from (
select max(p.partner_name) partner_name, pe.distributor_id, sum(p.pending_score) pending_score 
from wx_t_point_pending_record p 
left join wx_t_partner_o2o_enterprise pe on p.partner_id=pe.partner_id
where p.trans_time >= '2020-05-01' and p.sales_channel_name='Consumer'
group by pe.distributor_id) a
full outer join (   SELECT
        distributor_id,
		partner_name,
		pending_score
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            0 AS bi_sellin_line_id,
            sum( round( dw.abp_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.product_channel sales_channel_name,
            getdate() AS creation_time,
            1AS created_by,
            getdate() AS last_update_time,
            1 AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            dw.trans_time
          from PP_MID.dbo.syn_dw_to_pp_abp dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = en.partner_id
          WHERE
            1 = 1
            AND (dw.trans_time >= '2020-06-01' and trans_time < '2020-07-01')
            AND dw.product_channel = 'Consumer'
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = en.distributor_id
                  AND re.product_sku = dw.product_code_sap
                  AND re.sales_channel_name = dw.sales_channel_name
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
            dw.product_channel,
            dw.trans_time
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0) b on a.distributor_id=b.distributor_id
	  order by isnull(a.distributor_id, b.distributor_id)
