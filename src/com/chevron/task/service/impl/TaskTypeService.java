package com.chevron.task.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.sys.auth.dao.WxTDataMapper;
import com.sys.auth.dao.WxTMenuMapper;
import com.sys.auth.dao.WxTRoleMapper;
import com.sys.auth.dao.WxTRolesourceMapper;
import com.sys.auth.dao.WxTUserbaseMapper;
import com.sys.auth.model.CheckTreeNode;
import com.sys.auth.model.TreeNode;
import com.sys.auth.model.WxSimpleUser;
import com.sys.auth.model.WxTData;
import com.sys.auth.model.WxTMenu;
import com.sys.auth.model.WxTRolesource;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserbase;
import com.sys.auth.service.WxMenuService;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.chevron.task.business.TaskService;
import com.chevron.task.dao.PartnerDefTemplateMapper;
import com.chevron.task.dao.TaskmbStepVoMapper;
import com.chevron.task.dao.WxRelationUserMapper;
import com.chevron.task.dao.WxTaskCheckMapper;
import com.chevron.task.dao.WxTaskMbMapper;
import com.chevron.task.dao.WxTaskmbCheckMapper;
import com.chevron.task.dao.WxTasktypeMbMapper;
import com.chevron.task.model.PartnerDefTemplate;
import com.chevron.task.model.PartnerDefTemplateExample;
import com.chevron.task.model.SelectStep;
import com.chevron.task.model.TaskTypeMbChecked;
import com.chevron.task.model.WxRelationUser;
import com.chevron.task.model.WxRelationUserExample;
import com.chevron.task.model.WxTaskChecked;
import com.chevron.task.model.WxTaskMb;
import com.chevron.task.model.WxTaskmbCheck;
import com.chevron.task.model.WxTaskmbCheckExample;
import com.chevron.task.model.WxTasktypeMb;
import com.chevron.task.model.WxTasktypeMbExample;
import com.chevron.task.service.TaskTypeServiceI;
import com.chevron.userselect.service.impl.OrgTreeService;
import com.chevron.pmp.model.TaskTypeNode;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.json.JsonGenerator;
import com.sys.organization.model.OrganizationVo;

/**
 * 提供任务类型模板配置的服务类
 * <AUTHOR>
 *
 */
@Service
public class TaskTypeService implements TaskTypeServiceI {

	@Resource
	private WxTDataMapper wxTDataMapper;
	@Resource
	private WxTasktypeMbMapper wxTasktypeMbMapper;
	@Resource
	private WxTaskCheckMapper wxTaskCheckMapper;
	@Resource
	private WxTaskmbCheckMapper wxTaskmbCheckMapper;
	@Resource
	private WxTaskMbMapper wxTaskMbMapper;
	@Resource
	private WxRelationUserMapper wxRelationUserMapper;
	@Resource
	private TaskService taskService;
	@Resource
	private OrgTreeService orgTreeService;
	@Resource
	private TaskmbStepVoMapper taskmbStepVoMapper;
	public final static String TASK_TYPE_ROOT_CODE = "TT";

	@Resource
	private WxRoleServiceImpl wxRoleService;
	
	@Resource
	private WxTMenuMapper wxTMenuMapper;
	
	@Resource
	private WxTUserbaseMapper wxTUserbaseMapper;
	
	@Resource
	private WxTRoleMapper wxTRoleMapper;
	
	@Resource
	private WxTRolesourceMapper wxTRolesourceMapper;

	@Resource
	private WxMenuService WxMenuServiceImpl;
	
	@Resource
	private PartnerDefTemplateMapper partnerDefTemplateMapper;
	
	public final static Logger log = Logger.getLogger(TaskTypeService.class);
	
	/**
	 * 初始化维护任务类型配置页面，加载所有任务类型主数据
	 */
	public Map<String,Object> findTaskTypeMainDataList() throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		String dataCode = "TASKTYPE00";
		try {
			List<WxTData> list = wxTDataMapper.selTaskTypeData(dataCode);
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			List<TreeNode> resultJsonList = null;
			if (null != list && list.size() > 0) {
				CheckTreeNode tempObj = null;
				for (WxTData data : list) {
					tempObj = new CheckTreeNode();
					tempObj.setChecked(null);
					tempObj.setId(data.getDataId());
					tempObj.setText(data.getDataName());
					tempObj.setPid(data.getDataPid());
					tempObj.setCode(data.getDataCode());
					if (StringUtils.isNotEmpty(data.getDataDescript())) {
						tempObj.setRemarkType(1);
					}
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 根据任务类型代码查找任务类型，若已有数据则为编辑修改，若不存在则为新增
	 * @param tmbTypeCode
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> editTaskTypeMb(String tmbTypeCode) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			WxTasktypeMb wxTasktypeMb = wxTasktypeMbMapper.selTaskTypeMbByTypeCode(tmbTypeCode);
			if (null != wxTasktypeMb) {
				map.put("ifExist", true);
			} else {
				map.put("ifExist", false);
			}
			map.put("resultData", wxTasktypeMb);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 初始化检查项编辑页面，加载1级、2级任务检查项树结构
	 * 若tmbId = null，则为新增任务类型/实例，否则为编辑修改 (任务模板不需要维护检查项1,2级树)
	 * 若二级检查项已选中，则设为checked
	 */
	public Map<String,Object> findInitialTaskCheckTree(Long taskId, Integer mbCheckType) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			List<WxTaskChecked> list = null;
			OrganizationVo org = orgTreeService.getBranchOrg(ContextUtil.getCurUser().getOrgId());
			if (null != taskId && null != mbCheckType) {
				//编辑修改已有任务类型/实例
				Long mbId = taskId;
				Map<String,Object> paraMap = new HashMap<String,Object>();
				paraMap.put("mbId", mbId);
				paraMap.put("mbCheckType", mbCheckType);
				paraMap.put("orgId", org.getId());
				list = wxTaskCheckMapper.selCheckedTaskCheckList(paraMap);
			} else {
				//新建任务类型/实例
				list = wxTaskCheckMapper.selInitialTaskCheckList(org.getId());
			}
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			List<TreeNode> resultJsonList = null;
			if (null != list && list.size() > 0) {
				TreeNode tempObj = null;
				for (WxTaskChecked taskChecked : list) {
					tempObj = new TreeNode();
					tempObj.setId(taskChecked.getCheckId());
					tempObj.setText(taskChecked.getCheckName());
					tempObj.setPid(taskChecked.getCheckPid());
//					tempObj.setCode(taskChecked.getCheckCode());
					if (null != taskChecked.getChecked() && taskChecked.getChecked() == 1) {
						tempObj.setChecked(true);
					}
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 根据选中的二级节点数组，返回二到四级任务检查项树结构
	 * 传入已选中的所有检查项，若已选中，则设为checked
	 * 若tadkId = null，则为新增任务类型/实例，否则为编辑修改
	 * 若是新增或编辑任务实例，则必须传入已选的上级任务模板对应的ID、类型
	 * 此接口仅针对检查项不下放时的任务类型维护；以及检查项下放时的任务实例维护
	 */
	public Map<String,Object> findTaskCheckTreeByLevelTwoIdList(Long[] rootSecIdList, Long taskId, Integer mbCheckType) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			List<WxTaskChecked> list = null;
			OrganizationVo org = orgTreeService.getBranchOrg(ContextUtil.getCurUser().getOrgId());
			if (null != taskId) {
				//编辑修改
				list = taskService.selectTaskTypeCheckedList(org.getId(), taskId, mbCheckType, Arrays.asList(rootSecIdList));
			} else {
				//新增
				Map<String,Object> paraMap = new HashMap<String,Object>();
				paraMap.put("rootSecIdList", rootSecIdList);
				paraMap.put("orgId", org.getId());
				list = wxTaskCheckMapper.selTaskCheckListByLevelTwoIdList(paraMap);
			}
			System.out.println("list.size(): " + list.size());
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			List<TreeNode> resultJsonList = null;
			if (null != list && list.size() > 0) {
				CheckTreeNode tempObj = null;
				for (WxTaskChecked taskChecked : list) {
					tempObj = new CheckTreeNode();
					tempObj.setId(taskChecked.getCheckId());
					tempObj.setCode(taskChecked.getCheckCode());
					tempObj.setText(taskChecked.getCheckName());
					tempObj.setPid(taskChecked.getCheckPid());
					//新增时默认全部不选中；修改时根据已有wx_taskmb_check记录判断是否选中
					//仅当level为4时才设置为true，树组件会自动向上级联选中父节点；如果非level4的父节点设为true，反而会向下级联全部选中
					tempObj.setChecked(false);
					if (null != taskChecked.getChecked() && taskChecked.getChecked() == 1
							&& null != taskChecked.getLevel() && taskChecked.getLevel() == 4) {
						tempObj.setChecked(true);
					}
					if (null != taskChecked.getLevel() && taskChecked.getLevel() == 4) {
						tempObj.setPhotoType(false);
						tempObj.setCommentType(false);
						tempObj.setRemarkType(0);
					}
					if (null != taskChecked.getPhotoType() && taskChecked.getPhotoType() == 1) {
						tempObj.setPhotoType(true);
					}
					if (null != taskChecked.getCommentType() && taskChecked.getCommentType() == 1) {
						tempObj.setCommentType(true);
					}
					if (null != taskChecked.getRemarkType()) {
						tempObj.setRemarkType(taskChecked.getRemarkType());
					}
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 根据父级别对象，返回二到四级任务检查项树结构
	 * 传入上级任务类型或模板对应的ID、类型
	 * 若检查项已选中，则设为checked
	 * 若tadkId = null，则为新增任务模板/实例，否则为编辑修改
	 * pTaskId和pMbCheckType必须非空
	 * 此接口仅针对检查项不下放时的任务模板维护以及任务实例维护
	 */
	public Map<String,Object> findTaskCheckTreeByParent(Long taskId, Integer mbCheckType, Long pTaskId, Integer pMbCheckType, List<String> checkedIdList) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			List<WxTaskChecked> list = taskService.selectAllCheckList(pTaskId, pMbCheckType, taskId, mbCheckType);
			System.out.println("list.size(): " + list.size());
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			List<TreeNode> resultJsonList = null;
			if (null != list && list.size() > 0) {
				CheckTreeNode tempObj = null;
				for (WxTaskChecked taskChecked : list) {
					tempObj = new CheckTreeNode();
					tempObj.setId(taskChecked.getCheckId());
					tempObj.setCode(taskChecked.getCheckCode());
					tempObj.setText(taskChecked.getCheckName());
					tempObj.setPid(taskChecked.getCheckPid());
					if (null != taskId) {
						//修改时根据已有wx_taskmb_check记录判断是否选中
						//仅当level为4时才设置为true，树组件会自动向上级联选中父节点；如果非level4的父节点设为true，反而会向下级联全部选中
						tempObj.setChecked(false);
						if (null != taskChecked.getChecked() && taskChecked.getChecked() == 1
								&& null != taskChecked.getLevel() && taskChecked.getLevel() == 4) {
							tempObj.setChecked(true);
						}
					} else {
						//新增时加载后默认全部选中
						tempObj.setChecked(true);
						if(checkedIdList != null && checkedIdList.size() > 0){
							tempObj.setChecked(false);
							for(int i=0;i<checkedIdList.size();i++){
								if(tempObj.getCode().equals(checkedIdList.get(i))){
									tempObj.setChecked(true);
								}
							}
						}
					}
					if (null != taskChecked.getLevel() && taskChecked.getLevel() == 4) {
						tempObj.setPhotoType(false);
						tempObj.setCommentType(false);
						tempObj.setRemarkType(0);
					}
					if (null != taskChecked.getPhotoType() && taskChecked.getPhotoType() == 1) {
						tempObj.setPhotoType(true);
					}
					if (null != taskChecked.getCommentType() && taskChecked.getCommentType() == 1) {
						tempObj.setCommentType(true);
					}
					if (null != taskChecked.getRemarkType()) {
						tempObj.setRemarkType(taskChecked.getRemarkType());
					}
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 保存任务类型配置
	 * 若tmbId = null，则为新增任务类型，否则为编辑修改
	 */
	@Transactional
	public Map<String,Object> saveWxTaskTypeMb(String tmbName, String tmbTypeCode,
			Integer tmbClassify, Integer execUserType,
			Integer innerOrgType, Integer checkStatus,
			Integer checkItemStatus, Long checkId,
			Long tmbId, WxTaskmbCheck[] taskmbCheckArray) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			boolean ifCreateNew = false;
			WxTUser cru = ContextUtil.getCurUser();
			//保存wx_tasktype_mb表
			WxTasktypeMb tasktypeMb = null;
			if (null != tmbId) {
				tasktypeMb = wxTasktypeMbMapper.selectByPrimaryKey(tmbId);
			} else {
				ifCreateNew = true;
				tasktypeMb = wxTasktypeMbMapper.selTaskTypeMbByTypeCode(tmbTypeCode);
				if (null != tasktypeMb) {
					//任务类型记录已存在
					map.put("code", "systemerror");
					map.put("errorMessage", "任务类型已存在");
					return map;
				} else {
					tasktypeMb = new WxTasktypeMb();
				}
			}
			tasktypeMb.setTmbName(tmbName);
			tasktypeMb.setTmbTypeCode(tmbTypeCode);
			tasktypeMb.setTmbClassify(tmbClassify);
			if (tmbClassify == 1) {
				tasktypeMb.setExecUserType(execUserType);
			} else {
				tasktypeMb.setExecUserType(null);
			}
			tasktypeMb.setInnerOrgType(innerOrgType);
			tasktypeMb.setCheckStatus(checkStatus);
			tasktypeMb.setCheckItemStatus(checkItemStatus);
			tasktypeMb.setCheckId(checkId);
			tasktypeMb.setCreateUser(cru.getUserId());
			tasktypeMb.setXgUser(cru.getUserId());
			tasktypeMb.setXgSj(new Date());
			tasktypeMb.setStatus(1);
			tasktypeMb.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			if (ifCreateNew) {
				wxTasktypeMbMapper.insertSelective(tasktypeMb);
				tmbId = tasktypeMb.getTmbId();
			} else {
				wxTasktypeMbMapper.updateByPrimaryKey(tasktypeMb);
				//仅当修改已有记录时，批量删除wx_taskmb_check表
				deleteBatchWxTaskmbCheck(1, tmbId);
			}
			//checkItemStatus == 1，不下放检查项，批量插入wx_taskmb_check表
			if (checkItemStatus == 1 && taskmbCheckArray != null && taskmbCheckArray.length > 0) {
				saveBatchWxTaskmbCheck(cru, 1, tmbId, taskmbCheckArray);
			}
			map.put("tmbId", tmbId);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 批量删除检查项记录 wx_taskmb_check
	 * mb_check_type = mbCheckType and task_id = taskId(tmbId)
	 */
	public void deleteBatchWxTaskmbCheck(Integer mbCheckType, Long taskId) {
		WxTaskmbCheckExample example = new WxTaskmbCheckExample();
		example.createCriteria().andMbCheckTypeEqualTo(mbCheckType).andTaskIdEqualTo(taskId);
		wxTaskmbCheckMapper.deleteByExample(example);
	}

	/**
	 * 批量删除任务执行人或观察者记录 wx_relation_user
	 * source_type = sourceType and task_id = taskId(mbId)
	 */
	public void deleteBatchWxRelationUser(Integer sourceType, Long taskId) {
		WxRelationUserExample example = new WxRelationUserExample();
		example.createCriteria().andSourceTypeEqualTo(sourceType).andTaskIdEqualTo(taskId);
		wxRelationUserMapper.deleteByExample(example);
	}

	/**
	 * 批量保存检查项记录 wx_taskmb_check
	 */
	public void saveBatchWxTaskmbCheck(WxTUser cru, Integer mbCheckType, Long taskId, WxTaskmbCheck[] taskmbCheckArray) {
		for (WxTaskmbCheck wxTaskmbCheck : taskmbCheckArray) {
			wxTaskmbCheck.setMbCheckType(mbCheckType);
			wxTaskmbCheck.setTaskId(taskId);
			wxTaskmbCheck.setCreateUser(cru.getUserId());
			wxTaskmbCheck.setXgUser(cru.getUserId());
			wxTaskmbCheck.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
		}
		wxTaskmbCheckMapper.insertByBatch(Arrays.asList(taskmbCheckArray));
		// TODO 测试事务回滚 不起作用
//		WxTaskmbCheck wxTaskmbCheck = new WxTaskmbCheck();
//		wxTaskmbCheck.setComment("123456789012345678901234567890123456789012345678901234567890");
//		wxTaskmbCheckMapper.insertSelective(wxTaskmbCheck);
	}

	/**
	 * 批量保存任务执行人或观察者记录 wx_relation_user
	 */
	public void saveBatchWxRelationUser(WxTUser cru, Integer relationUserType, Integer sourceType, Long mbId, Long[] relationUserIdList) {
		List<WxRelationUser> wxRelationUserList = new ArrayList<WxRelationUser> ();
		for (Long userId : relationUserIdList) {
			if (null != userId && userId > 0) {
				WxRelationUser rUser = new WxRelationUser();
				rUser.setUserId(userId);
				rUser.setRelationUserType(relationUserType);
				rUser.setSourceType(sourceType);
				rUser.setTaskId(mbId);
				rUser.setXgUser(cru.getUserId());
				rUser.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
				wxRelationUserList.add(rUser);
			}
		}
		if (null != wxRelationUserList && wxRelationUserList.size() > 0) {
			wxRelationUserMapper.insertByBatch(wxRelationUserList);
		}
	}

	/**
	 * 初始化任务模板编辑页面
	 * 加载已存在的任务类型列表
	 */
	public Map<String,Object> findTaskTypeMbList() throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			WxTasktypeMbExample example = new WxTasktypeMbExample();
			example.createCriteria().andStatusEqualTo(new Integer(1)).andTenantIdEqualTo(new Long(1));
			List<WxTasktypeMb> list = wxTasktypeMbMapper.selectByExample(example);
			List<TaskTypeMbChecked> tasktypeList = new ArrayList<TaskTypeMbChecked>();
			if (null != list && list.size() > 0) {
				for (WxTasktypeMb taskTypeMb : list) {
					TaskTypeMbChecked tasktype = new TaskTypeMbChecked();
					tasktype.setValue(taskTypeMb.getTmbTypeCode());
					tasktype.setText(taskTypeMb.getTmbName());
					tasktypeList.add(tasktype);
				}
			}
			map.put("code", "success");
			map.put("resultData", tasktypeList);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 根据任务类型tmb_type_code查找任务模板列表
	 * @param tmbTypeCode
	 * @param mbRange (1 公共; 2 私人)
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> findTaskMbList(String tmbTypeCode, Integer mbRange, boolean updatePermission) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			List<WxTaskMb> wxTaskMbList = null;
			WxTUser user = ContextUtil.getCurUser();
			Long userType = WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0;
//			if (mbRange == 1) {
				//查询所有公共模板
			if(updatePermission){
				wxTaskMbList = wxTaskMbMapper.selectPublicMbByTmbTypeCodeForUpdate(tmbTypeCode, user.getOrgId(), userType);
				if(wxTaskMbList != null && !wxTaskMbList.isEmpty()){
					//组装已分配合伙人
					Map<String, Object> params = new HashMap<String, Object>();
					params.put("taskType", tmbTypeCode);
					params.put("orderBy", "partner_id");
					List<PartnerDefTemplate> list = partnerDefTemplateMapper.selectByParams(params);
					//构建模板ID映射表
					Map<Long, WxTaskMb> mbMap = new HashMap<Long, WxTaskMb>(wxTaskMbList.size());
					for(WxTaskMb mb : wxTaskMbList){
						mbMap.put(mb.getMbId(), mb);
					}
					//分装默认模板分配设置
					for(PartnerDefTemplate item : list){
						WxTaskMb mb = mbMap.get(item.getDefTaskTemplate());
						if(mb != null){
							List<PartnerDefTemplate> partnerDefTemplates = mb.getPartnerDefTemplates();
							if(partnerDefTemplates == null){
								partnerDefTemplates = new ArrayList<PartnerDefTemplate>();
								mb.setPartnerDefTemplates(partnerDefTemplates);
							}
							partnerDefTemplates.add(item);
						}
					}
				}
			}else{
				wxTaskMbList = wxTaskMbMapper.selectPublicMbByTmbTypeCode(tmbTypeCode, user.getOrgId(), userType);
				WxTaskMb defMb = wxTaskMbMapper.selectDefTemplate(tmbTypeCode, user.getOrgId(), userType, null);
				if(defMb != null){
					for(WxTaskMb mb : wxTaskMbList){
						if(mb.getMbId().equals(defMb.getMbId())){
							defMb.setIsDefTemplate(1);
						}
					}
				}
			}
//			} else if (mbRange == 2) {
//				//只查询用户所有的私人模板
//				WxTUser cru = ContextUtil.getCurUser();
//				wxTaskMbList = wxTaskMbMapper.selectPrivateMbByTmbTypeCode(tmbTypeCode, 2, cru.getUserId());
//			}
			if (null != wxTaskMbList && wxTaskMbList.size() > 0) {
				map.put("ifExist", true);
			} else {
				map.put("ifExist", false);
			}
			map.put("resultData", wxTaskMbList);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 根据任务模板id查找任务模板
	 * 若新建模板，传入tmbTypeCode；若修改已有模板，传入tmbTypeCode以及mbId
	 * @param tmbTypeCode
	 * @param mbId
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> findWxTaskMbById(String tmbTypeCode, Long mbId) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			WxTasktypeMb wxTasktypeMb = wxTasktypeMbMapper.selTaskTypeMbByTypeCode(tmbTypeCode);
			WxTaskMb wxTaskMb = null;
			if (null != mbId) {
				//修改已有模板
				wxTaskMb = wxTaskMbMapper.selectByPrimaryKey(mbId);
				List<WxSimpleUser> userExecList = taskService.selectMbExecUser(mbId, 1, 2);
				List<WxSimpleUser> userWatchList = taskService.selectMbExecUser(mbId, 2, 2);
				map.put("userExecList", userExecList);
				map.put("userWatchList", userWatchList);
			}
			map.put("wxTasktypeMb", wxTasktypeMb);
			map.put("wxTaskMb", wxTaskMb);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	/**
	 * 保存任务模板信息
	 * 若mbId = null，则为新增任务模板，否则为编辑修改
	 */
	@Transactional
	public Map<String,Object> saveWxTaskMb(String mbName, String tmbTypeCode, String taskDescription,
			Long tmbId, Integer mbRange, Integer checkItemStatus,
			Long mbId, Long[] execUserIdList, Long[] watchUserIdList,
			WxTaskmbCheck[] taskmbCheckArray) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			boolean ifCreateNew = false;
			WxTUser cru = ContextUtil.getCurUser();
			//保存wx_task_mb表
			WxTaskMb taskMb = null;
			if (null != mbId) {
				taskMb = wxTaskMbMapper.selectByPrimaryKey(mbId);
			} else {
				ifCreateNew = true;
				taskMb = new WxTaskMb();
			}
			taskMb.setMbName(mbName);
			taskMb.setTaskDescription(taskDescription);
			taskMb.setTmbTypeCode(tmbTypeCode);
			taskMb.setTmbId(tmbId);
			taskMb.setMbRange(mbRange);
			taskMb.setCreateUser(cru.getUserId());
			taskMb.setCreateOrg(cru.getOrgId());
			taskMb.setXgUser(cru.getUserId());
			taskMb.setXgSj(new Date());
			taskMb.setStatus(1);
			taskMb.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			if (ifCreateNew) {
				wxTaskMbMapper.insertSelective(taskMb);
				mbId = taskMb.getMbId();
			} else {
				wxTaskMbMapper.updateByPrimaryKeySelective(taskMb);
				//仅当修改已有记录时，批量删除wx_taskmb_check表
				deleteBatchWxTaskmbCheck(2, mbId);
				//批量删除执行人及观察者
				deleteBatchWxRelationUser(2, mbId);
			}
			//checkItemStatus == 1，不下放检查项，批量插入wx_taskmb_check表
			if (checkItemStatus == 1 && taskmbCheckArray != null && taskmbCheckArray.length > 0) {
				saveBatchWxTaskmbCheck(cru, 2, mbId, taskmbCheckArray);
			}
			//批量插入执行人
			if (null != execUserIdList && execUserIdList.length > 0) {
				saveBatchWxRelationUser(cru, 1, 2, mbId, execUserIdList);
			}
			//批量插入观察者
			if (null != watchUserIdList && watchUserIdList.length > 0) {
				saveBatchWxRelationUser(cru, 2, 2, mbId, watchUserIdList);
			}
			map.put("mbId", mbId);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	
	
	/**
	 * 获取步骤选择信息
	 * @param taskType 任务类型
	 * @param relateType 关联类型
	 * @param relateId 步骤关联ID
	 * @param mbId 任务使用模板ID
	 * @return 步骤选择信息
	 */
	public Map<String, Object> findSelectSteps(String taskType, int relateType, Long relateId, Long mbId, int fromType){
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			if(relateType == 1 && relateId == null){//not use
				//新建任务
				map.put("data", taskmbStepVoMapper.selSelectSteps(taskType, 2, mbId));
			}else{
				if(fromType==1)//创建或修改模板
				{
					map.put("data", taskmbStepVoMapper.selSelectSteps2(taskType, relateType, relateId));
				}else//创建任务
				{
					map.put("data", taskmbStepVoMapper.selSelectSteps(taskType, relateType, relateId));
				}
			}
			map.put("success", true);
		} catch (Exception e) {
			map.put("success", false);
			log.error("error",e);
		}
		return map;
	}
	
	/**
	 * 保存模板数据
	 * @param mbName 模板名称
	 * @param tmbTypeCode 任务类型
	 * @param taskDescription 模板描述
	 * @param mbId 模板ID
	 * @param selectSteps 选中步骤
	 * @return 操作结果
	 * @throws Exception
	 */
	public Map<String, Object> saveWxTaskMb1(String mbName, String tmbTypeCode, String taskDescription,
			Long mbId, SelectStep[] selectSteps){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			WxTUser cru = ContextUtil.getCurUser();
			//保存wx_task_mb表
			WxTaskMb taskMb = null;
			taskMb = new WxTaskMb();
			taskMb.setMbId(mbId);
			taskMb.setMbName(mbName);
			taskMb.setTaskDescription(taskDescription);
			taskMb.setTmbTypeCode(tmbTypeCode);
			taskMb.setMbRange(1);
			taskMb.setCreateUser(cru.getUserId());
			if(cru.getmUserTypes() == 1l){
				taskMb.setCreateOrg(1l);
			}else{
				taskMb.setCreateOrg(cru.getOrgId());
			}
			taskMb.setXgUser(cru.getUserId());
			taskMb.setXgSj(new Date());
			taskMb.setStatus(1);
			taskMb.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			if (mbId == null) {
				wxTaskMbMapper.insertSelective(taskMb);
				mbId = taskMb.getMbId();
			} else {
				wxTaskMbMapper.updateByPrimaryKeySelective(taskMb);
				//删除关联关系表
				taskmbStepVoMapper.deleteByRelate(2, mbId);
			}
			for(SelectStep selectStep : selectSteps){
				selectStep.setRelateType(2);
				selectStep.setRelateId(mbId);
			}
			//批量保存步骤信息
			if(selectSteps != null && selectSteps.length > 0){
				taskmbStepVoMapper.insertBatch(selectSteps);
			}
			map.put("mbId", mbId);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	@Override
	public Map<String, Object> delTaskMb(Long mbId) {
		Map<String, Object> map = new HashMap<String, Object>(1);
		try {
			//删除模板关联步骤信息
			taskmbStepVoMapper.deleteByRelate(2, mbId);
			wxTaskMbMapper.deleteByPrimaryKey(mbId);
			map.put("success", true);
		} catch (Exception e) {
			map.put("success", false);
			log.error("error",e);
		}
		return map;
	}

	@Override
	public Map<String, Object> findTaskTypeTree(boolean updatePermission) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			//查询任务类型根节点
			WxTData taskTypeRoot = wxTDataMapper.selectByCode(TASK_TYPE_ROOT_CODE);
			if(taskTypeRoot == null){
				taskTypeRoot = new WxTData();
				taskTypeRoot.setDataCode(TASK_TYPE_ROOT_CODE);
				taskTypeRoot.setDataDescript("任务类型数据根节点");
				taskTypeRoot.setDataName("任务类型");
				taskTypeRoot.setStatus(1);
				taskTypeRoot.setOrganizationId(1l);
				wxTDataMapper.insertSelective(taskTypeRoot);
			}
			WxTUser user = ContextUtil.getCurUser();
			Long userType = WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l;
			List<TaskTypeNode> taskTypeNodes;
			if(updatePermission){
				taskTypeNodes = wxTDataMapper.selTaskTypeNodesForUpdate(TASK_TYPE_ROOT_CODE, user.getOrgId(), userType);
			}else{
				taskTypeNodes = wxTDataMapper.selTaskTypeNodes(TASK_TYPE_ROOT_CODE, user.getOrgId(), userType, "data_order asc");
			}
			List<TaskTypeNode> children = JsonGenerator.listToTree(taskTypeNodes);
			//挂跟节点
			for(TaskTypeNode child : children){
				child.setPid(taskTypeRoot.getDataId());
			}
			List<TreeNode> list = new ArrayList<TreeNode>();
			//根节点
			TreeNode root = new TreeNode();
			root.setId(taskTypeRoot.getDataId());
			root.setText(taskTypeRoot.getDataName());
			root.setCode(taskTypeRoot.getDataCode());
			root.setChildren(children);
			list.add(root);
			map.put("code", "success");
			map.put("resultJsonList", list);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> creatNewType(Long dataPid, String dataName,
			String dataCodePrefix, String dataCodePostFix, String dataOrder,
			String dataDescript) {
		//插入主数据记录
		Map<String, Object> map = wxRoleService.creatNewData1(dataPid, dataName, dataCodePrefix, dataCodePostFix, dataOrder, dataDescript);
		if(!"success".equals(map.get("code"))){
			return map;
		}
		try {
			WxTUser cru = ContextUtil.getCurUser();
			//插入菜单记录
			WxTMenu menu = new WxTMenu();
			menu.setMenuName(dataName);
			menu.setMenuPid(33l);
			menu.setMenuUrl("/page/task/taskManage.jsp?tmbTypeCode=" + ((WxTData)map.get("data")).getDataCode());
			menu.setXgSj(new Date());
			menu.setXgUser(cru.getUserId());
			menu.setTenantId(cru.getOrgId() + "");//modify by bo.liu 0818 cru.getTenantId()
			menu.setStatus(1l);
			menu.setSort(99);
			wxTMenuMapper.insertSelective(menu);
			//插入创建用户菜单访问权限
			WxTUserbase wxTUserbase = new WxTUserbase();
			wxTUserbase.setAddFlag("1");
			wxTUserbase.setDelFlag("1");
			wxTUserbase.setViewFlag("1");
			wxTUserbase.setUpdateFlag("1");
			wxTUserbase.setUserId(cru.getUserId());
			wxTUserbase.setRsType(2); //菜单权限
			wxTUserbase.setStatus(1);
			wxTUserbase.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			wxTUserbase.setXgSj(new Date());
			wxTUserbase.setSourceId(menu.getMenuId());
			wxTUserbase.setXgUser(cru.getUserId());
			wxTUserbaseMapper.insertSelective(wxTUserbase);
			//插入可访问角色权限
			if((long)cru.getmUserTypes() == 1){
				//查询可访问角色ID集合
				List<Long> roleIds = wxTRoleMapper.selRoleIdsByNames(new String[]{"Chevron_Manager", "Service_Partner_Manager"});
				for(Long roleId : roleIds){
					WxTRolesource rolesource = new WxTRolesource();
					rolesource.setAddFlag("1");
					rolesource.setDelFlag("1");
					rolesource.setRoleId(roleId);
					rolesource.setRsType(2);
					rolesource.setSourceId(menu.getMenuId());
					rolesource.setStatus(1);
					rolesource.setTenantId(1l);
					rolesource.setUpdateFlag("1");
					rolesource.setViewFlag("1");
					rolesource.setXgUser(cru.getUserId());
					wxTRolesourceMapper.insertSelective(rolesource);
				}
			}
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	@Override
	public Map<String, Object> deleteType(Long typeId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			WxTData taskType = wxTDataMapper.selectByPrimaryKey(typeId);
			if(taskType == null){
				map.put("code", "success");
				return map;
			}
			//查询类型对应菜单
			String menuUrl  = "/page/task/taskManage.jsp?tmbTypeCode=" + taskType.getDataCode();
			List<WxTMenu> menus = WxMenuServiceImpl.selectMenuByMenuUrl(menuUrl);
			
			for(WxTMenu temp : menus){
				//删除用户分配菜单权限
				WxTUserbase example = new WxTUserbase();
				example.setRsType(2);
				example.setSourceId(temp.getMenuId());
				wxTUserbaseMapper.deleteByExample(example);
				//删除角色分配菜单权限
				WxTRolesource roleExample = new WxTRolesource();
				roleExample.setRsType(2);
				roleExample.setSourceId(temp.getMenuId());
				wxTRolesourceMapper.deleteByExample(roleExample);
			}
			map = wxRoleService.delMaintenanceTheData(typeId);
		} catch (Exception e) {
			map.put("code", "systemerror");
			log.error("error",e);
		}
		return map;
	}

	@Override
	public Map<String, Object> updateType(Long dataId, String dataName,
			String dataCodePrefix, String dataCodePostfix, String dataOrder,
			String dataDescript) {
		Map<String, Object> map = wxRoleService.updateTheData1(dataId, dataName, dataCodePrefix, dataCodePostfix, dataOrder, dataDescript);
		return map;
	}

	@Override
	public Map<String, Object> updateDefMb(String tmbTypeCode, Long mbId,
			Long orgId) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(5);
		Date now = new Date();
		try {
			PartnerDefTemplate record = new PartnerDefTemplate();
			record.setDefTaskTemplate(mbId);
			record.setUpdateTime(now);
			record.setUpdateUser(ContextUtil.getCurUserId());
			PartnerDefTemplateExample example = new PartnerDefTemplateExample();
			example.createCriteria().andPartnerIdEqualTo(orgId).andTaskTypeEqualTo(tmbTypeCode);
			if(partnerDefTemplateMapper.updateByExampleSelective(record, example) < 1){
				record.setPartnerId(orgId);
				record.setTaskType(tmbTypeCode);
				record.setCreateTime(now);
				partnerDefTemplateMapper.insertSelective(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> assignDefMb(
			List<PartnerDefTemplate> partnerDefTemplates) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>(5);
		Date now = new Date();
		try {
			for(PartnerDefTemplate item : partnerDefTemplates){
				PartnerDefTemplate record = new PartnerDefTemplate();
				record.setUpdateTime(now);
				record.setUpdateUser(ContextUtil.getCurUserId());
				record.setDefTaskTemplate(item.getDefTaskTemplate());
				PartnerDefTemplateExample example = new PartnerDefTemplateExample();
				example.createCriteria().andPartnerIdEqualTo(item.getPartnerId()).andTaskTypeEqualTo(item.getTaskType());
				if(partnerDefTemplateMapper.updateByExampleSelective(record, example) < 1){
					record.setPartnerId(item.getPartnerId());
					record.setTaskType(item.getTaskType());
					record.setCreateTime(now);
					partnerDefTemplateMapper.insertSelective(record);
				}
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
}
