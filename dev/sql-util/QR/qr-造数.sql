select top 1000 pc.* from wx_t_oem_product_packaging_code pc where 
(select count(1) from wx_t_oem_product_packaging_code pc2 where pc2.code2=pc.code2)=4 and
code1 not in ()
order by code2

CREATE INDEX ix_oem_product_packaging_code_id ON wx_t_oem_product_packaging_code (id);
CREATE INDEX ix_oem_delivery_product_id ON wx_t_oem_delivery_product (id);
--箱瓶码
update c set c.code1='9'+code1
from wx_t_oem_product_packaging_code c where code1 in
()


="update p1 set p1.product_id='"&D2&"', p1.product_batch='"&G2&"' from wx_t_oem_product_packaging_code pc1 left join wx_t_oem_product_packaging p1 on p1.id=pc1.packaging_id where pc1.code2='"&A2&"'"

="union all select (select top 1 d.order_id from wx_t_oem_delivery d where d.recv_distributor_id=(select top 1 distributor_id from dw_customer_org_sales c where c.customer_name_cn='"&A1&"') order by id) order_id"

="update p set p.product_id='"&D2&"', p.delivery_id=(select top 1 d.id from wx_t_oem_delivery d where d.recv_distributor_id=(select top 1 distributor_id from dw_customer_org_sales c where c.customer_name_cn='"&B2&"') order by id) from wx_t_oem_delivery_product p where p.code='"&A2&"'"

="update s set liters=0 from view_zs_sell_in s where s.sku='"&E2&"' and s.delivery_month='"&F2&"' and s.recv_distributor_id="&I2