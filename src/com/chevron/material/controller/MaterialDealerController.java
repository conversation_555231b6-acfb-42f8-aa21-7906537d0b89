package com.chevron.material.controller;

import com.chevron.exportdata.ExpAnnotation;
import com.chevron.exportdata.ExportExcel;
import com.chevron.exportdata.FieldComparator;
import com.chevron.interfaces.business.MaterialPlatformBizService;
import com.chevron.master.service.PartnerGiftPackageService;
import com.chevron.material.dao.WXTMaterialAddressConfigVoMapper;
import com.chevron.material.dao.WXTMaterialDealerVoMapper;
import com.chevron.material.model.MaterialDealerParams;
import com.chevron.material.model.MaterialDealerVo;
import com.chevron.material.model.WXTMaterialAddressConfigVo;
import com.chevron.material.model.WXTMaterialAddressConfigVoExample;
import com.chevron.material.util.ApplicationUtil;
import com.chevron.material.util.DealerPermissUtil;
import com.chevron.point.dao.PointPendingRecordMapper;
import com.chevron.point.dao.WXTPointValueDetailVoMapper;
import com.chevron.point.dto.PointType;
import com.chevron.point.model.PointMonthSum;
import com.chevron.point.model.PointSummary;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.service.DicService;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.write.*;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@RequestMapping("/material/dealer")
@Controller
public class MaterialDealerController {

	@Resource
	WXTMaterialDealerVoMapper materialDealerVoMapper;
	@Resource
	WXTMaterialAddressConfigVoMapper addressMapper;
	@Resource
	private DicService dicService;
	@Resource
	private PointPendingRecordMapper pointPendingRecordMapper;
	@Autowired
	private WXTPointValueDetailVoMapper wXTPointValueDetailVoMapper;
	@Autowired
	private ExportExcel exportExcel;
	@Autowired
    private DicItemVoMapper dicItemVoMapper;
	@Autowired
	private MaterialPlatformBizService materialPlatformBizService;
	@Resource
	private PartnerGiftPackageService partnerGiftPackageService;

	private static final String POINT_MONTHLY_SUM_PATTERN = "yyyy-MM-01 00:00:00";

	@ResponseBody
	@RequestMapping("/getDealerList.do")
	public Map<String,Object> getDealerList(MaterialDealerParams params) {
		ContextUtil.getCurUser().getSalesChannel();
		Map<String,Object> resultMap = new HashMap<String,Object>();
		int channelWeight = Constants.getChannelWeight(params.getSalesChannelName());
		params.setChannelWeight(channelWeight);
		List<MaterialDealerVo> orgList=materialDealerVoMapper.queryDealerByParam(params);
		//取得经销商积分权限
		int pointPermission=DealerPermissUtil.getCurUserDealerPermissCode(dicService);
		
		resultMap.put("pointPermission", pointPermission);
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		resultMap.put(Constants.RESULT_DATA_CODE, orgList);
		
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/getDealerListByPromotion.do")
	public Map<String,Object> getDealerListByPromotion(MaterialDealerParams params) {
		ContextUtil.getCurUser().getSalesChannel();
		Map<String,Object> resultMap = new HashMap<String,Object>();
		int channelWeight = Constants.getChannelWeight(params.getSalesChannelName());
		params.setChannelWeight(channelWeight);
		List<MaterialDealerVo> orgList=materialDealerVoMapper.queryDealerByPromotion(params);
		//取得经销商积分权限
		int pointPermission=DealerPermissUtil.getCurUserDealerPermissCode(dicService);
		
		resultMap.put("pointPermission", pointPermission);
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		resultMap.put(Constants.RESULT_DATA_CODE, orgList);
		
		return resultMap;
	}
	
	@RequestMapping("/toGotoPointpage.do")
	public void toGotoPointpage(ServletRequest request, ServletResponse response) throws IOException{
		String saleChannel = ContextUtil.getCurUser().getSalesChannel();
		String redirectPath = "";
		if(saleChannel == null || saleChannel.equals("Commercial")){
			redirectPath = redirectPath + "/material/jsp/dealerManagePage.jsp";
		}else{
			redirectPath = redirectPath + "/material/jsp/cdmDistributorManagePage.jsp";
		}
        WebUtils.issueRedirect(request, response, redirectPath);
		
	}
	
	@ResponseBody
	@RequestMapping("/getDealerAddress.do")
	public Map<String,Object> getDealerAddress(MaterialDealerParams params){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<WXTMaterialAddressConfigVo> addressList=addressMapper.selectByParams(params);
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		resultMap.put(Constants.RESULT_DATA_CODE, addressList);
		return resultMap;
	}
	
	@Transactional(rollbackFor=Exception.class,propagation=Propagation.REQUIRED)
	@ResponseBody
	@RequestMapping("/createDealerAddress.do")
	public Map<String, Object> createDealerAddress(
            @RequestParam(value = "id",required = false) Long id,
	        @RequestParam("partnerId") Long partnerId,
			@RequestParam(value = "provCode",required = false) String provCode,
			@RequestParam(value = "cityCode",required = false) String cityCode,
			@RequestParam("distCode") String distCode, 
			@RequestParam("contactor") String contactor, 
			@RequestParam("address") String address,
			@RequestParam("contactNumber") String contactNumber,
            @RequestParam(value = "pointType",required = false) String pointType) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
		    if(StringUtils.isNotBlank(pointType)){
                PointType point = PointType.getByApplicationType(pointType);
                if(!point.getEditAddress()){
                    resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
                    resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "该积分类型只能选择默认的地址");
                    return resultMap;
                }
            }
			WXTMaterialAddressConfigVo configVo = new WXTMaterialAddressConfigVo();
			configVo.setAddressRegion(distCode);
			configVo.setAddressDetail(address);
			configVo.setContacts(contactor);
			configVo.setContactNumber(contactNumber);
			configVo.setPartnerId(partnerId);
			configVo.setType("caltex_point");
			configVo.setDeleteFlag(false);
			configVo.setAttribute1(pointType);
            int num;
            if(id == null){
                num = addressMapper.insertSelective(configVo);
                // 发送经销商开客礼包
                partnerGiftPackageService.createPartnerGiftPackage(partnerId);
            }else{
                WXTMaterialAddressConfigVoExample example = new WXTMaterialAddressConfigVoExample();
                example.createCriteria().andIdEqualTo(id);
                num = addressMapper.updateByExampleSelective(configVo,example);
            }
            materialPlatformBizService.notifyPartnerUpdate(partnerId);
            resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "地址更新失败。");
			e.printStackTrace();
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/deleteDealerAddress.do")
	public Map<String,Object> deleteDealerAddress(@RequestParam("addressId")Long addressId){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			WXTMaterialAddressConfigVoExample example = new WXTMaterialAddressConfigVoExample();
			example.createCriteria().andIdEqualTo(addressId);
			WXTMaterialAddressConfigVo vo = addressMapper.selectByExample(example).get(0);
			int num=addressMapper.deleteByExample(example);
			if(num==1) {
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
				materialPlatformBizService.notifyPartnerUpdate(vo.getPartnerId());
			} else {
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "没有删除");
			}
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			e.printStackTrace();
		}
		return resultMap;
	}

    @ResponseBody
    @RequestMapping("/deletePointAddress.do")
    public Map<String,Object> deletePointAddress(@RequestParam("addressId")Long addressId){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        try {
            WXTMaterialAddressConfigVoExample example = new WXTMaterialAddressConfigVoExample();
            example.createCriteria().andIdEqualTo(addressId).andAttribute1IsNotNull();
            int num=addressMapper.deleteByExample(example);
            if(num==1) {
                resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
            } else {
                resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
                resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "没有删除");
            }
        } catch (Exception e) {
            resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
            e.printStackTrace();
        }
        return resultMap;
    }
	
	@ResponseBody
	@RequestMapping("/getCurrentPartnerKpi.do")
	public Map<String,Object> getCurrentPartnerKpi(){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		MaterialDealerParams params=new MaterialDealerParams();
		params.setPartnerId(curUser.getOrgId());
		params.setStart(0);
		params.setLimit(1);
		List<MaterialDealerVo> dealerList=materialDealerVoMapper.queryDealerByParam(params);
		if(dealerList.size()>0) {
			Map<String,Object> kpiMap = new HashMap<String,Object>();
			kpiMap.put("target", dealerList.get(0).getKpiTarget());
			kpiMap.put("percent", dealerList.get(0).getKpiPercent());
			kpiMap.put("liters", dealerList.get(0).getLiters());
			resultMap.put(Constants.RESULT_DATA_CODE, kpiMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		} else {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY,"未找到经销商");
		}
		return resultMap;
	}
	
	/**
	 * 导出OEM相关的积分
	 * @param response
	 * @param request
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportPointSummary.do")
	public void exportPointSummary(HttpServletResponse response,
	                                   HttpServletRequest request,@RequestParam("salesChannel") String salesChannel) throws Exception {
		// step1、根据salesChannel获取对应的pointType的数组
		PointType[] pointTypes = ApplicationUtil.getPointTypeBySalesChannel(salesChannel);


		// 标题的样式
		WritableFont titleFont = new WritableFont(WritableFont.TAHOMA, 11);
		titleFont.setColour(Colour.WHITE);
		WritableCellFormat title_wcf_center = new WritableCellFormat(titleFont);
		title_wcf_center.setBorder(Border.ALL, BorderLineStyle.THIN,
				Colour.GRAY_25);
		title_wcf_center.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
		title_wcf_center.setAlignment(Alignment.CENTRE);
		title_wcf_center.setBackground(Colour.GREEN);
		title_wcf_center.setWrap(true); // 是否换行

       // content的样式
		WritableFont contentFont = new WritableFont(WritableFont.TAHOMA, 11);
		WritableCellFormat content_wcf_center = new WritableCellFormat(contentFont);
		content_wcf_center.setBorder(Border.ALL, BorderLineStyle.THIN,
				Colour.GRAY_25);
		content_wcf_center.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
		content_wcf_center.setAlignment(Alignment.CENTRE);
		content_wcf_center.setWrap(true); // 是否换行

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

		// 遍历pointTypes
		if (pointTypes != null) {
			// 直接写入到的是response.getOutputStream里面
			ServletOutputStream os = response.getOutputStream();
			String excelName = salesChannel + "积分导出";
			WritableWorkbook workbook = Workbook.createWorkbook(os);
			String exportFileName = URLEncoder.encode(excelName, "UTF-8");
			response.reset();
			response.setContentType("application/vnd.ms-excel;charset=GBK");
			response.addHeader("Content-Disposition",
					"attachment; filename=\"" + exportFileName + ".xls\"");

			int sheetNo = 0;
			List<PointType> pointTypeList = new ArrayList<PointType>();
			for(PointType pointType : pointTypes){
				pointTypeList.add(pointType);
			}
			// 导出的时候C&I这个salesChannel需要单独的查询地促积分
			if(ApplicationUtil.CI.equals(salesChannel)){
				pointTypeList.add(null);
			}
			for (PointType pointType : pointTypeList) {
				String bizType = null;
				// 导出的时候C&I这个salesChannel需要单独的查询地促积分
				if(pointType == null && ApplicationUtil.CI.equals(salesChannel) ) {
					pointType = PointType.PROMOTION_POINT;
					bizType = "CALTEX_POINT_FROM_PROMOTE";
				}
				String applicationType = ApplicationUtil.getAppTypeByPointType(pointType);
				// 根据pointType获得appType
				String appType = ApplicationUtil.getAppTypeByPointType(pointType);
				int channelWeight = -1;//Constants.getChannelWeight(salesChannel);
				Map<String, Object> summaryQueryParams = new HashMap<String, Object>();
				// prozenType查询账户的状态
				summaryQueryParams.put("frozenType", appType);
				// pointType查询的积分类型
				summaryQueryParams.put("pointType", pointType.getValue());
				summaryQueryParams.put("salesChannelName", salesChannel);
				summaryQueryParams.put("channelWeight", channelWeight);
				summaryQueryParams.put("applicationType", applicationType);
				summaryQueryParams.put("bizType", bizType);
				//金福立经销商删除的不导出
				if(ApplicationUtil.CDM.equals(salesChannel)) {
					summaryQueryParams.put("orgStatus", 1);
				}
				// 查询积分汇总的列表
				List<PointSummary> pointSummaryList = wXTPointValueDetailVoMapper.selectPointSummaryByPointType(summaryQueryParams);

				// 查询所有导入积分里面最小trans_time---beginCal和最大trans_time---endCal的月份
				Map<String, Object> queryMonthlyMap = new HashMap<String, Object>();
				queryMonthlyMap.put("pointType", pointType.getValue());
				queryMonthlyMap.put("salesChannelName", salesChannel);
				queryMonthlyMap.put("channelWeight", channelWeight);
				queryMonthlyMap.put("applicationType", applicationType);
				queryMonthlyMap.put("bizType", bizType);
				List<PointMonthSum> pointMonthSums = wXTPointValueDetailVoMapper.queryImportPointMonthly(queryMonthlyMap);

				// 那些导入积分的开始日期和结束的日期
				Calendar beginCal = null;
				Calendar endCal = null;
				int hasSumMonthCount = -1;
				Map<String, PointMonthSum> pointMonthSumMap = new HashMap<String, PointMonthSum>();

				// pointMonthSums得到的数据已经排序了，所以，第一个就是最小的月份，最后一个就是最大的月份
				if (pointMonthSums != null && pointMonthSums.size() > 0) {
					Date beginDate = sdf.parse(pointMonthSums.get(0).getImportTime());
					Date endDate = sdf.parse(pointMonthSums.get(pointMonthSums.size() - 1).getImportTime());
					beginCal = Calendar.getInstance();
					beginCal.setTime(beginDate);
					endCal = Calendar.getInstance();
					endCal.setTime(endDate);

					int yearsInBetween = endCal.get(Calendar.YEAR)
							- beginCal.get(Calendar.YEAR);
					int monthsDiff = endCal.get(Calendar.MONTH)
							- beginCal.get(Calendar.MONTH);
					// 如果beginCal和endCal是同一个月，也是有1个月的
					hasSumMonthCount = yearsInBetween * 12 + monthsDiff + 1;
					for (PointMonthSum pointMonthSum : pointMonthSums) {
						pointMonthSumMap.put(pointMonthSum.getImportKey(), pointMonthSum);
					}
				}else {
					//没有积分的类型不处理
					continue;
				}
				// 先是标题行 + 根据 beginCal~endCal 显示导入的标题
				WritableSheet sheet = workbook.createSheet(ApplicationUtil.getPointTypeNameIncludePromoteType(applicationType,bizType) + "(sheet-" + sheetNo + ")", sheetNo);
				ArrayList<Field> annotationFields = new ArrayList<Field>();
				Field[] fields = PointSummary.class.getDeclaredFields();
				Arrays.sort(fields, new FieldComparator());
				// 显示固定的标题
				for (int i = 0, j = 0; i < fields.length; i++) {
					Field field = fields[i];
					if (field.isAnnotationPresent(ExpAnnotation.class)) {
						annotationFields.add(field);
						ExpAnnotation anno = field.getAnnotation(ExpAnnotation.class);
						sheet.setColumnView(j, anno.width());
						sheet.addCell(new Label(j, 0, anno.name(),
								title_wcf_center));
						j++;
					}
				}

				// 如果是CALTEX_POINT积分就需要查询
				Map<String,PointMonthSum>  promoteAsCaltexImportSumMap = new HashMap<String, PointMonthSum>();
				Set<String> promoteAsCaltexImportSet = new LinkedHashSet<String>();
				Map<String, PointMonthSum> returnedPointListMap = new HashMap<String, PointMonthSum>();
				int extraColNum = 0;
				if(PointType.CALTEX_POINT.toString().equals(pointType.getValue())){
					// 那些2018年7月的补充导入的那些退货的积分
//					Map<String,Object> selectReturnedPointMap = new HashMap<String, Object>();
//					selectReturnedPointMap.put("pointType",pointType.getValue());
//					selectReturnedPointMap.put("salesChannelName",salesChannel);
//					List<PointMonthSum> returnedPointList = wXTPointValueDetailVoMapper.selectReturnedPoint(selectReturnedPointMap);
//					if(returnedPointList!=null && returnedPointList.size()>0){
//						for(PointMonthSum pointMonthSum:returnedPointList){
//							returnedPointListMap.put(pointMonthSum.getImportKey(),pointMonthSum);
//						}
//					}
					// 开始显示回退积分的title，这一列在获得地促积分的前面
//					if(returnedPointListMap.size() >0){
//						int returnedPointCol = annotationFields.size() + promoteAsCaltexImportSet.size();
//						sheet.setColumnView(returnedPointCol, 20);
//						sheet.addCell(new Label(returnedPointCol, 0, "退货积分(2018年4月-7月)",
//								title_wcf_center));
//						extraColNum++;
//					}

					// 那些地促积分当作德乐进货积分的
					List<PointMonthSum> promoteAsCaltexImportSum = wXTPointValueDetailVoMapper.queryPromoteAsCaltexImportPointMonthly();
					if(promoteAsCaltexImportSum!=null && promoteAsCaltexImportSum.size() >0){
                        for(PointMonthSum pointMonthSum:promoteAsCaltexImportSum){
	                        promoteAsCaltexImportSumMap.put(pointMonthSum.getImportKey(), pointMonthSum);
	                        promoteAsCaltexImportSet.add(pointMonthSum.getImportTime());
                        }
						Object[] promoteAsCaltexImportSetArray = promoteAsCaltexImportSet.toArray();
						for(int i = annotationFields.size() + extraColNum; i < (annotationFields.size() +  promoteAsCaltexImportSet.size() + extraColNum); i++){
                            int index = i - annotationFields.size() - extraColNum;
							sheet.setColumnView(i, 20);
							sheet.addCell(new Label(i, 0, DateUtil.getDateStr(sdf.parse((String) promoteAsCaltexImportSetArray[index]), DateUtil.DATA_FORMAT_PATTERN_MONTH) + "获得地促积分",
									title_wcf_center));
						}
					}
				} else {
					extraColNum = 0;
				}

				Calendar curCal = Calendar.getInstance();
				curCal.setTime(beginCal.getTime());
				// 然后是"yyyy-MM导入"这样的标题
				for (int i = (annotationFields.size() + promoteAsCaltexImportSet.size() + extraColNum); i < (hasSumMonthCount + annotationFields.size() +  promoteAsCaltexImportSet.size() + extraColNum); i++) {
					sheet.setColumnView(i, 20);
					sheet.addCell(new Label(i, 0, DateUtil.getDateStr(curCal.getTime(), DateUtil.DATA_FORMAT_PATTERN_MONTH) + "导入",
							title_wcf_center));
					curCal.add(Calendar.MONTH, 1);
				}

				// 然后是表格的内容
				if (pointSummaryList != null && pointSummaryList.size() > 0) {
					for (int i = 0; i < pointSummaryList.size(); i++) {
						int row = i + 1;
						int col = 0;
						PointSummary pointSummary = pointSummaryList.get(i);
						// 显示固定字段
						for (; col < annotationFields.size(); col++) {
							// 获得getXXX的方法，并且调用
							Field tempField = annotationFields.get(col);
							String methodName = "get"
									+ tempField.getName().substring(0, 1)
									.toUpperCase()
									+ tempField.getName().substring(1);
							Method method = pointSummary.getClass()
									.getMethod(methodName);

							String tempValue = method.invoke(pointSummary) == null ? ""
									: method.invoke(pointSummary).toString();
							sheet.addCell(new Label(col, row, tempValue, content_wcf_center));
						}

						if(returnedPointListMap.size() > 0){
							for (; col < (annotationFields.size() + extraColNum); col++) {
								String returnedKey = pointSummary.getSapCode() + "-2018-07-01 00:00:00";
								PointMonthSum pointValueDetailVo = returnedPointListMap.get(returnedKey);
								if (pointValueDetailVo != null && pointValueDetailVo.getTotalImportValue() != null) {
									sheet.addCell(new Label(col, row, pointValueDetailVo.getTotalImportValue().toString(), content_wcf_center));
								}
							}
						}

						if(promoteAsCaltexImportSet.size() >0){
							Object[] promoteAsCaltexImportSetArray = promoteAsCaltexImportSet.toArray();
							for (; col < (promoteAsCaltexImportSet.size() + annotationFields.size() + extraColNum); col++) {
								int index = col - annotationFields.size() - extraColNum;
								String sumKey = pointSummary.getSapCode() + "-" + (String) promoteAsCaltexImportSetArray[index];
								PointMonthSum curMonthAsSum = promoteAsCaltexImportSumMap.get(sumKey);
								if (curMonthAsSum != null && curMonthAsSum.getTotalImportValue() != null) {
									sheet.addCell(new Label(col, row, curMonthAsSum.getTotalImportValue().toString(), content_wcf_center));
								}
							}
						}

						// 然后是变化的字段"yyyy-MM导入"的那些数据,继续使用col，row还是在这一行
						curCal.setTime(beginCal.getTime());
						for (; col < (hasSumMonthCount + promoteAsCaltexImportSet.size() + annotationFields.size() + extraColNum); col++) {
							String sumKey = pointSummary.getSapCode() + "-" + DateUtil.getDateStr(curCal.getTime(), POINT_MONTHLY_SUM_PATTERN);
							PointMonthSum curMonthSum = pointMonthSumMap.get(sumKey);
							PointMonthSum curMonthAsSum = promoteAsCaltexImportSumMap.get(sumKey);
							PointMonthSum retunedPointSum = returnedPointListMap.get(sumKey);

							if (curMonthSum != null && curMonthSum.getTotalImportValue() != null) {
								if(curMonthAsSum!=null && curMonthAsSum.getTotalImportValue() >0){
									curMonthSum.setTotalImportValue(curMonthSum.getTotalImportValue() - curMonthAsSum.getTotalImportValue());
								}
								if(retunedPointSum!=null){
									curMonthSum.setTotalImportValue(curMonthSum.getTotalImportValue() - retunedPointSum.getTotalImportValue());
								}
								sheet.addCell(new Label(col, row, curMonthSum.getTotalImportValue().toString(), content_wcf_center));
							}
							curCal.add(Calendar.MONTH, 1);
						}
					}

					// 最后开始下一个pointType，下一个worksheet了
					sheetNo++;
				}
			}
			workbook.write();
			workbook.close();
			os.flush();
			os.close();
		}
	}
}
