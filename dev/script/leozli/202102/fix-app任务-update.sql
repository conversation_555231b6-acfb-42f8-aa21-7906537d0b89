delete from wx_t_dic_item where dic_type_code='workshop.type1'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '4S店', '4S店', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '快修保养店', '快修保养店', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '综合修理厂', '综合修理厂', '', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '汽车美容店', '汽车美容店', '', '1',40);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '汽配店', '汽配店', '', '1',50);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '二级分销商', '二级分销商', '', '1',60);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type1', '其他', '其他', '', '1',70);

update c set c.option_value='{  "showType": "checkbox",  "multiSelect": false,  "datasourceType": "rpc",  "data": null, "url":"dicService.getDicItemByDicTypeCode", "params": ["workshop.type1"],  "textField": "dicItemName",  "valueField": "dicItemCode" }'
from wx_task_check c where check_name='门店类型' and check_order=990 

delete from wx_t_dic_item where dic_type_code='workshop.type2'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type2', '修理厂', '修理厂', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type2', '配件店', '配件店', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type2', '二级分销商', '二级分销商', '', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type2', '其他', '其他', '', '1',40);

update c set c.option_value='{  "showType": "checkbox",  "multiSelect": false,  "datasourceType": "rpc",  "data": null, "url":"dicService.getDicItemByDicTypeCode", "params": ["workshop.type2"],  "textField": "dicItemName",  "valueField": "dicItemCode" }'
from wx_task_check c where check_name='门店类型' and check_order=991 

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('workshop.type4', '工程机械门店类型配置', '工程机械门店类型配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type4', '修理厂', '修理厂', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type4', '配件店', '配件店', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type4', '二级分销商', '二级分销商', '', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('workshop.type4', '其他', '其他', '', '1',40);

update c set c.option_value='{  "showType": "checkbox",  "multiSelect": false,  "datasourceType": "rpc",  "data": null, "url":"dicService.getDicItemByDicTypeCode", "params": ["workshop.type4"],  "textField": "dicItemName",  "valueField": "dicItemCode" }'
from wx_task_check c where check_name='门店类型' and check_order=992 

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Task.synB2bMechnicOn', 'Y', '任务同步大咖汇技师开启');

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('Fleet.type2', '德乐车队及项目类型配置', '德乐车队及项目类型配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type2', '普通物流车队', '普通物流车队', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type2', '危险品运输车队', '危险品运输车队', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type2', '公交巴士车队', '公交巴士车队', '', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type2', '长途巴士车队', '长途巴士车队', '', '1',40);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type2', '商旅租赁车队', '商旅租赁车队', '', '1',50);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type2', '其他', '其他', '', '1',60);

update c set c.option_value='{  "showType": "checkbox",  "multiSelect": false,  "datasourceType": "rpc",  "data": null, "url":"dicService.getDicItemByDicTypeCode", "params": ["Fleet.type2"],  "textField": "dicItemName",  "valueField": "dicItemCode" }'
from wx_task_check c where check_name='车队及项目类型' and check_order=990 

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('Fleet.type4', '工程机械车队及项目类型配置', '工程机械车队及项目类型配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type4', '私人车队', '私人车队', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type4', '混凝土搅拌站', '混凝土搅拌站', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type4', '工程公司', '工程公司', '', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type4', '矿山客户', '矿山客户', '', '1',40);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type4', '整机维保商', '整机维保商', '', '1',50);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status,sort_numb) values ('Fleet.type4', '其他', '其他', '', '1',60);

update c set c.option_value='{  "showType": "checkbox",  "multiSelect": false,  "datasourceType": "rpc",  "data": null, "url":"dicService.getDicItemByDicTypeCode", "params": ["Fleet.type4"],  "textField": "dicItemName",  "valueField": "dicItemCode" }'
from wx_task_check c where check_name='车队及项目类型' and check_order=991