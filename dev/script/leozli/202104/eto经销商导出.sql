	    select t.organization_name, t1.eto_company_id,
				 (select top 1 s.region from dw_customer_org_sales s where s.distributor_id=t1.distributor_id order by isnull(s.channel_weight, *********)) region
		 from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
		     LEFT JOIN wx_t_verification_rule verirule ON verirule.partner_id = t.id AND verirule.enable_flag = 'Y'
	     left join wx_t_ws_distribution_rule dr on dr.workshop_id=-1 and dr.enable_flag='Y' and dr.partner_id=t.id and dr.sku is null
		      where t.type=1 and t1.eto_company_id is not null