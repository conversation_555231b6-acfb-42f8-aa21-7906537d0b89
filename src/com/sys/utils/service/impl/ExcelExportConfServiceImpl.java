package com.sys.utils.service.impl;

import java.util.*;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseStatus;
import com.sys.utils.business.ExcelExportConfBizService;
import com.sys.utils.model.ExcelExportConf;
import com.sys.utils.model.ExcelExportConfExample;
import com.sys.utils.service.ExcelExportConfService;

/**
 * EXCEL导出配置操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2019-10-08 16:55
 */
@Service
public class ExcelExportConfServiceImpl implements ExcelExportConfService {
	
	@Autowired
	private ExcelExportConfBizService excelExportConfBizService;
    @Autowired
    private DicService dicService;
    @Autowired
    private OperationPermissionBizService operationPermissionBizService;
	
	private final static Logger log = Logger.getLogger(ExcelExportConfServiceImpl.class);

	@Override
	public JsonResponse save(ExcelExportConf record) {
		JsonResponse map = new JsonResponse();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				excelExportConfBizService.insert(record);
			}else{
				excelExportConfBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.service.impl.ExcelExportConfServiceImpl.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse saveForEditPage(ExcelExportConf record) {
		JsonResponse map = new JsonResponse();
		log.info("saveForEditPage: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				excelExportConfBizService.insert(record);
			}else{
				excelExportConfBizService.updateForEditPage(record);
			}
			log.info("saveForEditPage success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.service.impl.ExcelExportConfServiceImpl.saveForEditPage", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse delete(List<Long> ids) {
		JsonResponse map = new JsonResponse();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			excelExportConfBizService.delete(ids);
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.service.impl.ExcelExportConfServiceImpl.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}

	@Override
	public Map<String, Object> getAvailableExcels() {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			ExcelExportConfExample example = new ExcelExportConfExample();
			example.createCriteria().andEnableFlagEqualTo(1);
			example.setOrderByClause("order_no asc");
			map.put(Constants.RESULT_LST_KEY, excelExportConfBizService.queryByExample(example));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}

    private final String moduleCode = "DataExport.Module";
    @Override
    public Map<String, Object> getAvailableExcelsByUser() {
        Map<String, Object> map = new HashMap<String, Object>(5);
        try {
            ExcelExportConfExample example = new ExcelExportConfExample();
            example.createCriteria().andEnableFlagEqualTo(1);
            example.setOrderByClause("order_no asc");
            List<ExcelExportConf> excelExportConfs = excelExportConfBizService.queryByExample(example);
            Map<String,List<ExcelExportConf>> resultMap = new LinkedHashMap<String,List<ExcelExportConf>>();

            if (CollectionUtil.isNotEmpty(excelExportConfs)){
                Map<String,DicItemVo> dicItemVoMap = new HashMap<String, DicItemVo>();
                Map<String, Object> dicItemByDicTypeCode = dicService.getDicItemByDicTypeCode(moduleCode);
                int permissionWeight = operationPermissionBizService.getPermissionWeight(ContextUtil.getCurUserId(), moduleCode);
                map.put("permissionWeight", permissionWeight);
                Object dicData = dicItemByDicTypeCode.get("data");
                if (dicData!=null){
                    List<DicItemVo> dicItemVos = sortModuleDictItem(dicData);
                    for (DicItemVo dicItemVo : dicItemVos) {
                        if (StringUtils.isNotBlank(dicItemVo.getDicItemDesc())){
                            JSONObject jsonObject = JSON.parseObject(dicItemVo.getDicItemDesc());
                            // 比较用户与该module需要的权限值
                            if ((jsonObject.getInteger("permissionWeight")&permissionWeight)>0){
                                dicItemVoMap.put(dicItemVo.getDicItemCode(),dicItemVo);
                            }
                        }
                    }
                }
                Map<DicItemVo,List<ExcelExportConf>> sortMap = new TreeMap<DicItemVo, List<ExcelExportConf>>(new Comparator<DicItemVo>() {
                    @Override
                    public int compare(DicItemVo o1, DicItemVo o2) {
                        if (o2.getSortNumb()==null){
                            return -1;
                        }
                        if (o1.getSortNumb()==null){
                            return 1;
                        }
                        return o1.getSortNumb().compareTo(o2.getSortNumb());
                    }
                });

                for (ExcelExportConf excelExportConf : excelExportConfs) {
                    if (StringUtils.isNotBlank(excelExportConf.getModule())){
                        for (String itemCode : excelExportConf.getModule().split(",")) {
                            DicItemVo dicItemVo = dicItemVoMap.get(itemCode);
                            if (dicItemVo!=null){
                                List<ExcelExportConf> list = sortMap.get(dicItemVo);
                                if (list==null){
                                    list = new ArrayList<ExcelExportConf>();
                                    sortMap.put(dicItemVo,list);
                                }
                                list.add(excelExportConf);
                            }
                        }
                    }
                }
                // 包含全部模块的权限
                if (dicItemVoMap.containsKey("1")){
                    resultMap.put("全部",excelExportConfs);
                }
                for (Map.Entry<DicItemVo, List<ExcelExportConf>> entry : sortMap.entrySet()) {
                    resultMap.put(entry.getKey().getDicItemName(),entry.getValue());
                }

            }
            map.put(Constants.RESULT_DATA_CODE, resultMap);
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
        } catch (WxPltException e) {
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error(e);
        }
        return map;
    }

    @Override
    public List<DicItemVo> getAllModule() {
        Map<String, Object> dicItemByDicTypeCode = dicService.getDicItemByDicTypeCode(moduleCode);
        Object dicData = dicItemByDicTypeCode.get("data");
        List<DicItemVo> dicItemVos = sortModuleDictItem(dicData);
        dicItemVos.remove(0);
        return dicItemVos;
    }

    private List<DicItemVo> sortModuleDictItem(Object dicData){
        List<DicItemVo> dicItemVos = new ArrayList<DicItemVo>();
        if (dicData!=null){
            try {
                dicItemVos = CollectionUtil.sort((List<DicItemVo>) dicData, new Comparator<DicItemVo>() {
                    @Override
                    public int compare(DicItemVo o1, DicItemVo o2) {
                        return o1.getSortNumb().compareTo(o2.getSortNumb());
                    }
                });
            }catch (Exception e){
                log.error("解析"+moduleCode+"数据字典出错！"+e.getMessage(),e);
            }
        }
        return dicItemVos;
    }
}
