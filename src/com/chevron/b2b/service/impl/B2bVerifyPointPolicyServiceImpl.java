package com.chevron.b2b.service.impl;

import com.chevron.b2b.business.B2bVerifyPointPolicyBizService;
import com.chevron.b2b.model.B2bVerifyPointPolicy;
import com.chevron.b2b.service.B2bVerifyPointPolicyService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * B2B核销积分政策操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
@Service
public class B2bVerifyPointPolicyServiceImpl implements B2bVerifyPointPolicyService {
	
	@Autowired
	private B2bVerifyPointPolicyBizService b2bVerifyPointPolicyBizService;
	
	private final static Logger log = Logger.getLogger(B2bVerifyPointPolicyServiceImpl.class);

	@Override
	public Map<String, Object> save(B2bVerifyPointPolicy record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			b2bVerifyPointPolicyBizService.save(record);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> delete(List<Long> ids) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			b2bVerifyPointPolicyBizService.delete(ids);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return map;
	}
}
