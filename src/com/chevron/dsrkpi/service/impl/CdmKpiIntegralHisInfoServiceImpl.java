package com.chevron.dsrkpi.service.impl;

import com.chevron.dsrkpi.business.CdmKpiIntegralHisInfoBizService;
import com.chevron.dsrkpi.dao.CdmKpiIntegralHisInfoMapper;
import com.chevron.dsrkpi.model.CdmKpiIntegralHisInfo;
import com.chevron.dsrkpi.model.CdmKpiIntegralHisInfoVo;
import com.chevron.dsrkpi.service.CdmKpiIntegralHisInfoService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Cdm Kpi Integral His Info操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2020-04-07 17:05
 */
@Service
public class CdmKpiIntegralHisInfoServiceImpl implements CdmKpiIntegralHisInfoService {
	
	@Autowired
	private CdmKpiIntegralHisInfoBizService cdmKpiIntegralHisInfoBizService;
	
	@Autowired
	private CdmKpiIntegralHisInfoMapper cdmKpiIntegralHisInfoMapper;
	
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	
	
	private final static Logger log = Logger.getLogger(CdmKpiIntegralHisInfoServiceImpl.class);

	@Override
	public JsonResponse save(CdmKpiIntegralHisInfo record) {
		JsonResponse map = new JsonResponse();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				cdmKpiIntegralHisInfoBizService.insert(record);
			}else{
				cdmKpiIntegralHisInfoBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.CdmKpiIntegralHisInfoServiceImpl.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse saveForEditPage(CdmKpiIntegralHisInfo record) {
		JsonResponse map = new JsonResponse();
		log.info("saveForEditPage: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				cdmKpiIntegralHisInfoBizService.insert(record);
			}else{
				cdmKpiIntegralHisInfoBizService.updateForEditPage(record);
			}
			log.info("saveForEditPage success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.CdmKpiIntegralHisInfoServiceImpl.saveForEditPage", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse delete(List<Long> ids) {
		JsonResponse map = new JsonResponse();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			cdmKpiIntegralHisInfoBizService.delete(ids);
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.CdmKpiIntegralHisInfoServiceImpl.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}

	@Override
	public JsonResponse checkComfirmButten(String integralTime) {
		JsonResponse resMap = new JsonResponse();
		
		//权限控制
		WxTUser curUser = ContextUtil.getCurUser();
		try {
		int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "TaskPage.CDM.KPI");
			if(1 == (permissionWeight&1) || curUser.getUserId() == 1L) {
				permissionWeight = 1;
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
	
		Date parseDate = DateUtil.parseDate(integralTime,DateUtil.DATA_FORMAT_PATTERN_MONTH);
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
		String kpiYear = DateUtil.getYear(parseDate);
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("kpiYear", Long.valueOf(kpiYear));
		if("2020-04".compareTo(integralTime) > 0) {
			resMap.setDataResult(false); //true 显示
			return resMap;
		}
		String maxEffectiveTime = cdmKpiIntegralHisInfoMapper.getMaxEffectiveTime(paraMap);
		if(maxEffectiveTime == null) {
			resMap.setDataResult(true); //true 显示
			return resMap;
		}
		int compareTo = maxEffectiveTime.compareTo(simpleDateFormat.format(parseDate));
		if(compareTo >= 0 ) {
			resMap.setDataResult(false); //false 不显示
		}else {
			resMap.setDataResult(true); //true 显示
		}
		return resMap;
	}

	@Override
	public JsonResponse getCdmKpiIntegralHisByCondition(CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo) {
        cdmKpiIntegralHisInfoVo.setKpiYear(2020l);
		JsonResponse resMap = new JsonResponse();
		WxTUser curUser = ContextUtil.getCurUser();
		String userCai = curUser.getCai();
		//权限控制
		try {
			//获取当前用户的查看权限
			int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "TaskPage.CDM.KPI");
			if(1 == (permissionWeight&1) || curUser.getUserId() == 1L) {
				permissionWeight = 1;
			}else if(2 == (permissionWeight&2)) {
				permissionWeight = 2;
				cdmKpiIntegralHisInfoVo.setBuManagerCai(userCai);
			}else if(4 == (permissionWeight&4)) {
				permissionWeight = 4;
				cdmKpiIntegralHisInfoVo.setChannelManagerCai(userCai);
			}else if(8 == (permissionWeight&8)) {
				permissionWeight = 8;
				cdmKpiIntegralHisInfoVo.setSuppervisorCai(userCai);
			}else if(128 == (permissionWeight&128)) {
				cdmKpiIntegralHisInfoVo.setTeamLeaderCai(userCai);
			}else if(16 == (permissionWeight&16)) {
				cdmKpiIntegralHisInfoVo.setSalesCai(userCai);
			}else if(32 == (permissionWeight&32)){
				String userModel = curUser.getUserModel();
				if(WxTUser.USER_MODEL_SP.equals(userModel)) {						
					permissionWeight = 32; 
					cdmKpiIntegralHisInfoVo.setPartnerId(curUser.getOrgId());
				}
			}else if(64 == (permissionWeight&64)){
				permissionWeight = 1;
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
		
		List<CdmKpiIntegralHisInfoVo> cdmKpiIntegralHisByCondition = cdmKpiIntegralHisInfoMapper.getCdmKpiIntegralHisByCondition(cdmKpiIntegralHisInfoVo);
		Map<Long, CdmKpiIntegralHisInfoVo> integralMap = new HashMap<Long, CdmKpiIntegralHisInfoVo>();
		for (CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo2 : cdmKpiIntegralHisByCondition) {
			
			//经销商数据组装
			if( null == cdmKpiIntegralHisInfoVo2.getDsrId() && null != cdmKpiIntegralHisInfoVo2.getOrgId()) {
				CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo3 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
				if(null == cdmKpiIntegralHisInfoVo3 ) {
					cdmKpiIntegralHisInfoVo3 = new CdmKpiIntegralHisInfoVo();
					cdmKpiIntegralHisInfoVo3.setCustomerNameCn(cdmKpiIntegralHisInfoVo2.getCustomerNameCn());
					cdmKpiIntegralHisInfoVo3.setOrgId(cdmKpiIntegralHisInfoVo2.getOrgId());
					cdmKpiIntegralHisInfoVo3.setTotalPoints(cdmKpiIntegralHisInfoVo2.getTotalPoints());
					cdmKpiIntegralHisInfoVo3.setRemainingPoints(cdmKpiIntegralHisInfoVo2.getRemainingPoints());
					cdmKpiIntegralHisInfoVo3.setYearAwards(cdmKpiIntegralHisInfoVo2.getYearAwards());
					cdmKpiIntegralHisInfoVo3.setKpiYear(cdmKpiIntegralHisInfoVo2.getKpiYear());
					integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegralHisInfoVo3);
				}
			}
					
			//dsr数据组装
			if(null != cdmKpiIntegralHisInfoVo2.getDsrId()) {
				CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo3 = integralMap.get(cdmKpiIntegralHisInfoVo2.getDsrId());
				if(null == cdmKpiIntegralHisInfoVo3) {
					cdmKpiIntegralHisInfoVo3 = new CdmKpiIntegralHisInfoVo();
					cdmKpiIntegralHisInfoVo3.setCustomerNameCn(cdmKpiIntegralHisInfoVo2.getCustomerNameCn());
					cdmKpiIntegralHisInfoVo3.setOrgId(cdmKpiIntegralHisInfoVo2.getOrgId());
					cdmKpiIntegralHisInfoVo3.setDsrId(cdmKpiIntegralHisInfoVo2.getDsrId());
					cdmKpiIntegralHisInfoVo3.setDsrName(cdmKpiIntegralHisInfoVo2.getDsrName());
					cdmKpiIntegralHisInfoVo3.setTotalPoints(cdmKpiIntegralHisInfoVo2.getTotalPoints());
					cdmKpiIntegralHisInfoVo3.setRemainingPoints(cdmKpiIntegralHisInfoVo2.getRemainingPoints());
					cdmKpiIntegralHisInfoVo3.setYearAwards(cdmKpiIntegralHisInfoVo2.getYearAwards());
					cdmKpiIntegralHisInfoVo3.setKpiYear(cdmKpiIntegralHisInfoVo2.getKpiYear());
				}
				getIntegralByMon(integralMap, cdmKpiIntegralHisInfoVo2, cdmKpiIntegralHisInfoVo3);
				integralMap.put(cdmKpiIntegralHisInfoVo2.getDsrId(), cdmKpiIntegralHisInfoVo3);
			}
		}
		List<CdmKpiIntegralHisInfoVo> cdmKpiIntegralHisList = new ArrayList<CdmKpiIntegralHisInfoVo>();
		cdmKpiIntegralHisList.addAll(integralMap.values());
		resMap.setListResult(cdmKpiIntegralHisList);
		return resMap;
	}

	private void getIntegralByMon(Map<Long, CdmKpiIntegralHisInfoVo> integralMap,
			CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo2, CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo3) {
		//获取每月放发积分
		Integer mon = -1;
		mon = cdmKpiIntegralHisInfoVo2.getMon() == null? 0:cdmKpiIntegralHisInfoVo2.getMon();
		switch (mon) {
		case 1:
			cdmKpiIntegralHisInfoVo3.setMonGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegralVo1= integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			Double monGoals = cdmKpiIntegralVo1.getMonGoals() == null ? 0.0 : cdmKpiIntegralVo1.getMonGoals();
			cdmKpiIntegralVo1.setMonGoals((monGoals+ cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegralVo1.setTotalPoints((Double)((cdmKpiIntegralVo1.getTotalPoints() == null ? 0.0 : cdmKpiIntegralVo1.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegralVo1);
			break;
		case 2:
			cdmKpiIntegralHisInfoVo3.setFebGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral2 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral2.setFebGoals((Double)((cdmKpiIntegral2.getFebGoals() == null ? 0.0: cdmKpiIntegral2.getFebGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral2.setTotalPoints((Double)((cdmKpiIntegral2.getTotalPoints() == null ? 0.0 : cdmKpiIntegral2.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral2);
			break;
		case 3:
			cdmKpiIntegralHisInfoVo3.setMarGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral3 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral3.setMarGoals((Double)((cdmKpiIntegral3.getMarGoals() == null ? 0.0 : cdmKpiIntegral3.getMarGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral3.setTotalPoints((Double)((cdmKpiIntegral3.getTotalPoints() == null ? 0.0 : cdmKpiIntegral3.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral3);
			break;
		case 4:
			cdmKpiIntegralHisInfoVo3.setAprGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral4 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral4.setAprGoals((Double)((cdmKpiIntegral4.getAprGoals() == null? 0.0 :cdmKpiIntegral4.getAprGoals())  + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral4.setTotalPoints((Double)((cdmKpiIntegral4.getTotalPoints() == null ? 0.0 : cdmKpiIntegral4.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral4);
			break;
		case 5:
			cdmKpiIntegralHisInfoVo3.setMayGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral5 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral5.setMayGoals((Double)((cdmKpiIntegral5.getMayGoals() == null ?0.0 : cdmKpiIntegral5.getMayGoals())  + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral5.setTotalPoints((Double)((cdmKpiIntegral5.getTotalPoints() == null ? 0.0 : cdmKpiIntegral5.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral5);
			break;
		case 6:
			cdmKpiIntegralHisInfoVo3.setJuneGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral6 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral6.setJuneGoals((Double)((cdmKpiIntegral6.getJuneGoals() == null ? 0.0 : cdmKpiIntegral6.getJuneGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral6.setTotalPoints((Double)((cdmKpiIntegral6.getTotalPoints() == null ? 0.0 : cdmKpiIntegral6.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral6);
			break;
		case 7:
			cdmKpiIntegralHisInfoVo3.setJulyGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral7 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral7.setJulyGoals((Double)((cdmKpiIntegral7.getJulyGoals() == null ? 0.0 : cdmKpiIntegral7.getJulyGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral7.setTotalPoints((Double)((cdmKpiIntegral7.getTotalPoints() == null ? 0.0 : cdmKpiIntegral7.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral7);
			break;
		case 8:
			cdmKpiIntegralHisInfoVo3.setAugGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral8 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral8.setAugGoals((Double)((cdmKpiIntegral8.getAugGoals() == null ? 0.0 : cdmKpiIntegral8.getAugGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral8.setTotalPoints((Double)((cdmKpiIntegral8.getTotalPoints() == null ? 0.0 : cdmKpiIntegral8.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral8);
			break;
		case 9:
			cdmKpiIntegralHisInfoVo3.setSeptGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral9 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral9.setSeptGoals((Double)((cdmKpiIntegral9.getSeptGoals() == null ? 0.0: cdmKpiIntegral9.getSeptGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral9.setTotalPoints((Double)((cdmKpiIntegral9.getTotalPoints() == null ? 0.0 : cdmKpiIntegral9.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral9);
			break;
		case 10:
			cdmKpiIntegralHisInfoVo3.setOctGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral10 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral10.setOctGoals((Double)((cdmKpiIntegral10.getOctGoals() == null ? 0.0 : cdmKpiIntegral10.getOctGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral10.setTotalPoints((Double)((cdmKpiIntegral10.getTotalPoints() == null ? 0.0 : cdmKpiIntegral10.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral10);
			break;
		case 11:
			cdmKpiIntegralHisInfoVo3.setNovGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral11 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral11.setNovGoals((Double)((cdmKpiIntegral11.getNovGoals() == null ? 0.0 : cdmKpiIntegral11.getNovGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral11.setTotalPoints((Double)((cdmKpiIntegral11.getTotalPoints() == null ? 0.0 : cdmKpiIntegral11.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral11);
			break;
		case 12:
			cdmKpiIntegralHisInfoVo3.setDecGoals((Double)cdmKpiIntegralHisInfoVo2.getIntegrals());
			cdmKpiIntegralHisInfoVo3.setTotalPoints((Double)(cdmKpiIntegralHisInfoVo3.getTotalPoints() == null ? 0 :cdmKpiIntegralHisInfoVo3.getTotalPoints())+ cdmKpiIntegralHisInfoVo2.getIntegrals());
			CdmKpiIntegralHisInfoVo cdmKpiIntegral12 = integralMap.get(cdmKpiIntegralHisInfoVo2.getOrgId());
			cdmKpiIntegral12.setDecGoals((Double)((cdmKpiIntegral12.getDecGoals() == null ? 0.0 : cdmKpiIntegral12.getDecGoals()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			cdmKpiIntegral12.setTotalPoints((Double)((cdmKpiIntegral12.getTotalPoints() == null ? 0.0 : cdmKpiIntegral12.getTotalPoints()) + cdmKpiIntegralHisInfoVo2.getIntegrals()));
			integralMap.put(cdmKpiIntegralHisInfoVo2.getOrgId(), cdmKpiIntegral12);
			break;
		default:
			break;
		}
	}
}
