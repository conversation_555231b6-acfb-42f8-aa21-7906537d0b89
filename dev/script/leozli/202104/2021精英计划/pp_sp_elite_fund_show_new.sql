/*    ==Scripting Parameters==

    Source Server Version : SQL Server 2016 (13.0.4604)
    Source Database Engine Edition : Microsoft SQL Server Standard Edition
    Source Database Engine Type : Standalone SQL Server

    Target Server Version : SQL Server 2017
    Target Database Engine Edition : Microsoft SQL Server Standard Edition
    Target Database Engine Type : Standalone SQL Server
*/
/****** Object:  StoredProcedure [dbo].[pp_sp_elite_fund_show]    Script Date: 27/07/2021 11:43:20 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER   PROCEDURE [dbo].[pp_sp_elite_fund_show]
	--@report_month NVARCHAR(7), --2021-01
	@year INT,
	@quarter INT,
	@product_channel NVARCHAR(50),
	@sales_cai NVARCHAR(50),
	@permission_weight INT, --1-全部,2-ASM,4-Team Leader,8-flsr。位操作，多个权限值取合集，以最大的权限处理
	@region NVARCHAR(50),
	@distributor_id INT,
	@delivery_status INT -- 0:未发放；1：已经发放；不传表示全部
AS BEGIN
	/*
	数据来源：季奖，年奖：PP_MID.dbo.mid_cal_elite_fund 。 需要再乘上税 2021目前为1.13
	         扣减：[dbo].[wx_t_qbr_cdm_performance_adjust] 。一个经销商，一个季度，一条记录
	备注：
	op_flag: 操作标记。1-可编辑可发放（wx_t_qbr_cdm_performance_adjust中有数据且dw_mid_cal_elite_fund表中没有数据），0-不可编辑不可发放
	adjust_id：扣减表ID
	delivery_status：只和发放表有关。即经销商当年该季度在表dw_mid_cal_elite_fund中有数据的为已发放 1 ，没数据的为未发放 0。

	*/
	DECLARE @SQL NVARCHAR(MAX),@DTL_FROM_SQL NVARCHAR(MAX) ,@CRITERIA_SQL NVARCHAR(MAX)

	IF LEN(ISNULL(@distributor_id,''))>100 
	   --OR LEN(ISNULL(@sales_cai,''))>4
	   OR ( 1 & @permission_weight = 0 AND @sales_cai IS NOT NULL AND NOT EXISTS (SELECT * FROM dbo.dw_sales_role WHERE sales_cai = @sales_cai ))
	   --OR ISNULL(@month,0) =0 OR ISNULL(@month,0) >12 OR ISNULL(@year,0) =0
	BEGIN 
		PRINT 'please check your parameters'
		RETURN
	END 
	-- process permission
	DECLARE @permission_sql NVARCHAR(MAX)
	IF @distributor_id IS NOT NULL AND NOT EXISTS (SELECT * FROM dbo.dw_sales_role WHERE sales_cai = @sales_cai ) SET @permission_weight = 1
	IF 1 & @permission_weight > 0
	BEGIN
		SET @permission_sql = ' '
		SET @sales_cai = NULL
	END
	ELSE IF 14 & @permission_weight > 0 
		SET @permission_sql = 'AND EXISTS (
									SELECT   *
									FROM     dbo.view_pp_procedure_org org
									WHERE    1=1
									AND CASE WHEN @permission_weight = 1 AND dsoh.bu = ''Indirect'' THEN 1
											 WHEN org.sales_role = ''ABM'' AND dsoh.bu = org.org_level_name AND org.sales_cai = @sales_cai THEN 1
											 WHEN org.sales_role = ''Channel Manager'' AND dsoh.sales_channel = org.org_level_name AND org.sales_cai = @sales_cai THEN 1
											 WHEN org.sales_role = ''ASM'' AND dw.region = org.org_level_name AND org.sales_cai = @sales_cai THEN 1
											 WHEN org.sales_role = ''Team Leader'' AND ( rol.supervisor_cai = org.sales_cai OR dw.sales_cai = org.sales_cai) AND org.sales_cai = @sales_cai THEN 1
											 WHEN org.sales_role = ''FLSR''  AND dw.sales_cai = org.sales_cai AND org.sales_cai = @sales_cai THEN 1
										ELSE 0
										END = 1
							)'
	ELSE SET @permission_sql = ' '

	SET @DTL_FROM_SQL = '
	SELECT   fund.year
			,fund.quarter
			,fund.customer_name_cn
			,dw.region
			,fund.fund_quarterly
			,fund.annual_reward
			,ded.new_entry
			,ded.above_scan
			,ded.flee_goods
			,(fund_quarterly+annual_reward+ISNULL(new_entry,0) + ISNULL(above_scan,0) + ISNULL(flee_goods,0)) total
			,ded.asm_comment
			,ded.adjust_id
			,CASE WHEN ded.year IS NOT NULL AND pp_fund.year IS NULL THEN 1 ELSE 0 END op_flag
			,ded.att_count
			,ded.new_entry_init,ded.above_scan_init,ded.flee_goods_init
			,ded.flsr_comment,fund.distributor_id
	FROM (
		SELECT YEAR,replace(fund.quarter,''Q'','''')quarter,fund.distributor_id,pe.partner_id,o.organization_name customer_name_cn
			,product_channel,region
			,ROUND( SUM(-(ISNULL(commercial_bd_fund,0) + ISNULL(commercial_bd_fund_adjust,0) 
			+ISNULL(commercial_marketing_fund,0) + ISNULL(commercial_marketing_fund_adjust,0) 
			+ISNULL(commercial_ivi_fund,0) + ISNULL(commercial_ivi_fund_adjust,0) 
			+ISNULL(consumer_bd_fund,0) + ISNULL(consumer_bd_fund_adjust,0) 
			+ISNULL(consumer_marketing_fund,0) + ISNULL(consumer_marketing_fund_adjust,0) 
			+ISNULL(consumer_ivi_fund,0) + ISNULL(consumer_ivi_fund_adjust,0) ) * 1.13 ) ,2) fund_quarterly
			,ROUND( SUM(-ISNULL(consumer_annual_reward,0) * 1.13),2)  annual_reward
			-- select *
		FROM PP_MID.dbo.mid_cal_elite_fund fund
		JOIN dbo.wx_t_partner_o2o_enterprise pe on pe.distributor_id = fund.distributor_id
		LEFT JOIN dbo.wx_t_organization o on o.id = pe.partner_id
		GROUP BY year,replace(fund.quarter,''Q'',''''),fund.distributor_id,pe.partner_id,o.organization_name,product_channel,region
	) fund
	JOIN dbo.dw_customer_org_sales dw on dw.distributor_id = fund.distributor_id and dw.product_channel = fund.product_channel
	LEFT JOIN dbo.dw_sales_role rol ON CASE WHEN rol.sales_cai = ''NA'' AND rol.supervisor_cai = dw.supervisor_cai AND rol.sales_cai = dw.sales_cai THEN 1
											WHEN rol.sales_cai <> ''NA'' AND rol.sales_cai = dw.sales_cai THEN 1
									   ELSE 0
									   END = 1
	LEFT JOIN (
		SELECT year,quarter,partner_id,product_channel
			,adj.new_entry,adj.above_scan,adj.flee_goods
			,ISNULL(asm_comment,'''')asm_comment
			,id adjust_id
			,ISNULL(atf.att_count,0)att_count
			,lg.new_entry new_entry_init,lg.above_scan above_scan_init,lg.flee_goods flee_goods_init
			,ISNULL(flsr_comment,'''')flsr_comment
		-- select *
		FROM [dbo].[wx_t_qbr_cdm_performance_adjust] adj
		LEFT JOIN (
			SELECT adjust_id,new_entry,above_scan,flee_goods
			FROM (
				SELECT adjust_id,property_name,value_final
				FROM dbo.wx_t_qbr_cdm_per_adj_log 
				where log_type = ''0''
			) dtl
			PIVOT (SUM(value_final) FOR property_name IN ([new_entry],[above_scan],[flee_goods]) ) PVT
		) lg ON lg.adjust_id = adj.id
		LEFT JOIN (
			SELECT source_id, COUNT(1) att_count
			FROM wx_att_file 
			WHERE source_type=''57''
			GROUP BY source_id
		) atf ON atf.source_id = adj.id
	) ded ON ded.year = fund.year
		AND ded.quarter = fund.quarter
		AND ded.partner_id = fund.partner_id
		AND ded.product_channel = fund.product_channel
	LEFT JOIN (
		SELECT YEAR,replace(quarter,''Q'','''')quarter,product_channel,distributor_id,SUM(total_fund)total_fund
		FROM dbo.dw_mid_cal_elite_fund
		GROUP BY YEAR,replace(quarter,''Q'',''''),product_channel,distributor_id
	)pp_fund ON pp_fund.year = fund.year
		AND pp_fund.quarter = fund.quarter
		AND pp_fund.product_channel = fund.product_channel
		AND pp_fund.distributor_id = fund.distributor_id
	LEFT JOIN (
		select *
		from PP_MID.dbo.syn_dw_to_pp_dim_sales_org_hier
		where level = 3
	)dsoh ON dsoh.region = fund.region
	WHERE fund.YEAR = ISNULL( @year ,fund.YEAR )
	AND fund.quarter = ISNULL( @quarter ,fund.quarter)
	AND fund.product_channel = ISNULL( @product_channel ,fund.product_channel)
	AND fund.region = ISNULL( @region ,fund.region)
	AND CASE WHEN @distributor_id IS NULL THEN 1
			 WHEN @distributor_id IS NOT NULL AND fund.distributor_id IN (SELECT * FROM dbo.SplitIn(@distributor_id,'','') AS si) THEN 1 
		ELSE 0
		END = 1
	AND CASE WHEN pp_fund.year IS NULL THEN 0 ELSE 1 END = ISNULL(@delivery_status, CASE WHEN pp_fund.year IS NULL THEN 0 ELSE 1 END)
	'

	SET @SQL = @DTL_FROM_SQL + @permission_sql
	EXEC pp_sp_print_sql @SQL
	EXEC sys.sp_executesql @SQL 
		,N'@year INT,@quarter INT, @product_channel NVARCHAR(50), @sales_cai NVARCHAR(50), @permission_weight INT, @region NVARCHAR(50), @distributor_id INT, @delivery_status INT' 
		,@year,@quarter,@product_channel,@sales_cai,@permission_weight ,@region,@distributor_id, @delivery_status

END
