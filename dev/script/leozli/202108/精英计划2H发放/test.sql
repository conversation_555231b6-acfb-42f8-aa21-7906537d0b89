select * from dw_mid_cal_elite_fund where year=2021 and quarter='Q3'

update f set f.customer_name_cn=(select distinct r.customer_name_cn from dw_customer_org_sales r where r.distributor_id=f.distributor_id)
from dw_mid_cal_elite_fund f where f.customer_name_cn!=(select distinct r.customer_name_cn from dw_customer_org_sales r where r.distributor_id=f.distributor_id)

select [distributor_id],[customer_name_cn], sum(product_channel) product_channel from (
SELECT distinct [distributor_id]
      ,[customer_name_cn]
      ,case when product_channel='Commercial' then 2 else 1 end product_channel
  FROM dw_mid_cal_elite_fund f where year=2021 and quarter in ('Q3','Q4')
  and f.distributor_id not in (1249,689,151,655)) a 
  group by [distributor_id],[customer_name_cn]