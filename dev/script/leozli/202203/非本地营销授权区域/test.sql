select dr1.region Region, dr1.supervisor_name_cn "ASM", dr1.sales_name_cn "FLSR", w.partner_id,o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "客户名称", 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=w.customer_type) "客户类型",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.businessWeight' and di1.dic_item_code=w.business_weight) "品牌",
 r3.region_name "省份", r2.region_name "城市",r1.region_name "区县",
w.work_shop_address "地址", 
(case when w.ext_flag&6144>0 then '是' else '否' end) "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then '是' else '否' end "B2B店",
(case when w.status='0' then '潜在' when w.status='3' then '合作' when w.status='1' then '已功店'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' else w.status end) "状态", w.contact_person_tel, w.create_time "录入时间",
 w.update_time "最后修改时间",
 case when exists (select 1 from wx_t_region_partner rp2 where rp2.region_id=w.region_id and rp2.partner_id=w.partner_id) then '是' else '否' end "全渠道区域包含"
 --update w set w.delete_flag=0
from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
left join dw_customer_org_sales dr1 on dr1.distributor_id=pe.distributor_id and dr1.channel_weight&(case when w.business_weight=4 then 2 else w.business_weight end)>0
left join wx_t_user u on u.user_id=w.excute_user_id
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id--莒县洪军汽修
		    left join wx_t_region r3 on r2.parent_id=r3.id
where w.delete_flag=0 and o.status=1 and o.id!=9 and w.customer_type in (1,2) and CHARINDEX('V', w.customer_code)!=1 
and w.partner_id not in (56647,56620)
--and CONVERT(varchar(10), w.create_time, 23)='2020-09-20'
and not exists (select 1 from wx_t_region_partner rp1 where rp1.region_id=w.region_id and rp1.partner_id=w.partner_id and rp1.channel_weight=(case when w.business_weight=4 then 2 else w.business_weight end))
order by o.id, w.customer_type

select dr1.region Region, dr1.supervisor_name_cn "ASM", dr1.sales_name_cn "FLSR", w.partner_id,o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "客户名称", 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=w.customer_type) "客户类型",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.businessWeight' and di1.dic_item_code=w.business_weight) "品牌",
 r3.region_name "省份", r2.region_name "城市",r1.region_name "区县",
w.work_shop_address "地址", 
(case when w.ext_flag&6144>0 then '是' else '否' end) "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then '是' else '否' end "B2B店",
(select count(1) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id) "订单数量",
(select max(wto.create_time) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id) "最近下单时间",
(select max(os.out_time) FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
           WHERE wto.source='OUT_STOCK' and os.status='2' and wto.work_shop_id=w.id) "最近出库时间",
(case when w.status='0' then '潜在' when w.status='3' then '合作' when w.status='1' then '已功店'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' else w.status end) "状态", w.contact_person_tel, w.create_time "录入时间",
 w.update_time "最后修改时间",
 case when exists (select 1 from wx_t_region_partner rp2 where rp2.region_id=w.region_id and rp2.partner_id=w.partner_id) then '是' else '否' end "全渠道区域包含"
 --update w set w.delete_flag=0
from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
left join dw_customer_org_sales dr1 on dr1.distributor_id=pe.distributor_id and dr1.channel_weight&(case when w.business_weight=4 then 2 else w.business_weight end)>0
left join wx_t_user u on u.user_id=w.excute_user_id
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id--莒县洪军汽修
		    left join wx_t_region r3 on r2.parent_id=r3.id
where w.delete_flag=0 and o.status=1 and o.id!=9 and w.customer_type in (1,2) and CHARINDEX('V', w.customer_code)!=1 
and w.partner_id not in (56647,56620)
--and CONVERT(varchar(10), w.create_time, 23)='2020-09-20'
and not exists (select 1 from wx_t_region_partner rp1 where rp1.region_id=w.region_id and rp1.partner_id=w.partner_id and rp1.channel_weight=(case when w.business_weight=4 then 2 else w.business_weight end))
order by o.id, w.customer_type


select dr1.region Region, dr1.supervisor_name_cn "ASM", dr1.sales_name_cn "FLSR", w.partner_id,o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "客户名称", 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=w.customer_type) "客户类型",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.businessWeight' and di1.dic_item_code=w.business_weight) "品牌",
 r3.region_name "省份", r2.region_name "城市",r1.region_name "区县",
w.work_shop_address "地址", 
(case when w.ext_flag&6144>0 then '是' else '否' end) "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then '是' else '否' end "B2B店",
(select count(1) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id) "订单数量",
(select max(wto.create_time) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id) "最近下单时间",
(select max(os.out_time) FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
           WHERE wto.source='OUT_STOCK' and os.status='2' and wto.work_shop_id=w.id) "最近出库时间",
(case when w.status='0' then '潜在' when w.status='3' then '合作' when w.status='1' then '已功店'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' else w.status end) "状态", w.contact_person_tel, w.create_time "录入时间",
 w.update_time "最后修改时间",
 case when exists (select 1 from wx_t_region_partner rp2 where rp2.region_id=w.region_id and rp2.partner_id=w.partner_id) then '是' else '否' end "全渠道区域包含"
 , (select substring((select ','+io1.organization_name from wx_t_organization io1 where io1.status=1 and exists (select 1 from wx_t_region_partner irp1 
 where irp1.partner_id=io1.id and irp1.region_id=w.region_id and irp1.channel_weight=(case when w.business_weight=4 then 2 else w.business_weight end)) FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) "区域授权经销商"
 --update w set w.delete_flag=0
from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
left join dw_customer_org_sales dr1 on dr1.distributor_id=pe.distributor_id and dr1.channel_weight&(case when w.business_weight=4 then 2 else w.business_weight end)>0
left join wx_t_user u on u.user_id=w.excute_user_id
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id--莒县洪军汽修
		    left join wx_t_region r3 on r2.parent_id=r3.id
where w.delete_flag=0 and o.status=1 and o.id!=9 and w.customer_type in (1,2) and CHARINDEX('V', w.customer_code)!=1 
and w.partner_id not in (56647,56620)
--and CONVERT(varchar(10), w.create_time, 23)='2020-09-20'
and not exists (select 1 from wx_t_region_partner rp1 where rp1.region_id=w.region_id and rp1.partner_id=w.partner_id and rp1.channel_weight=(case when w.business_weight=4 then 2 else w.business_weight end))
order by o.id, w.customer_type

select dr1.region Region, dr1.supervisor_name_cn "ASM", dr1.sales_name_cn "FLSR", w.partner_id,o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "客户名称", 
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=w.customer_type) "客户类型",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.businessWeight' and di1.dic_item_code=w.business_weight) "品牌",
 r3.region_name "省份", r2.region_name "城市",r1.region_name "区县",
w.work_shop_address "地址", 
(case when w.ext_flag&6144>0 then '是' else '否' end) "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then '是' else '否' end "B2B店",
(select count(1) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id) "订单数量",
(select max(wto.create_time) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id) "最近下单时间",
(select max(wto.create_time) FROM wx_t_order wto
           WHERE wto.work_shop_id=w.id and wto.source like 'WX%') "大咖会最近下单时间",
(select max(os.out_time) FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
           WHERE wto.source='OUT_STOCK' and os.status='2' and wto.work_shop_id=w.id) "最近出库时间",
(case when w.status='0' then '潜在' when w.status='3' then '合作' when w.status='1' then '已功店'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' else w.status end) "状态", w.contact_person_tel, w.create_time "录入时间",
 w.update_time "最后修改时间"
 --update w set w.delete_flag=0
from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
left join dw_customer_org_sales dr1 on dr1.distributor_id=pe.distributor_id and dr1.channel_weight&(case when w.business_weight & 7=0 then 1 when w.business_weight=4 then 2 else w.business_weight end)>0
left join wx_t_user u on u.user_id=w.excute_user_id
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id--莒县洪军汽修
		    left join wx_t_region r3 on r2.parent_id=r3.id
where w.delete_flag=0 and o.status=1 and w.customer_type in (1,2) and CHARINDEX('V', w.customer_code)!=1 
--and w.partner_id in 
--and CONVERT(varchar(10), w.create_time, 23)='2020-09-20'
order by o.id, w.customer_type