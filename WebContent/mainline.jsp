<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import= "com.common.util.ContextUtil"%>
<%@ page import= "com.sys.auth.model.WxTUser"%>
<%
	WxTUser curUser = ContextUtil.getCurUser();
		String userName  = curUser.getChName();
%>	
<style type="text/css">
.wxLogTitle {
	color: #FAF6F6 !important;
	font-size: 21px !important;
}
.wxLogTitleImg {
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}

	
		
	.dl-log-user:hover {
		cursor: pointer;
		color: red;
	}
	
	.liClass1 {
				margin: 0px;
				padding: 0px;
				float: left;
				width: 50%;
				text-align: center;
				background-color: #ffffff;
				height: 45px;
				line-height: 45px;
				cursor: pointer;
			}
			
			.liClass2 {
				margin: 0px;
				padding: 0px;
				float: left;
				width: 50%;
				text-align: center;
				background-color: #eaeaea;
				height: 45px;
				line-height: 45px;
				cursor: pointer;
			}
			
			.div_pubilc {
				margin: 0px;
				padding-left: 15px;
				width: 100%;
				height: 400px;
				float: none;
			}

</style>
<link href="${ctx }common/build-bui/css/main-min-Manager.css" rel="stylesheet" type="text/css" />
<link href="${ctx }common/css/mainline.css" rel="stylesheet">

<script type='text/javascript' src="${ctx }tasks/js/main.js"></script>

<div class="navbar navbar-inverse site-navbar" style="border-radius: 0px;" id="site-navbar">
    <div class="container" style="width:100%;">
        <div class="container-gap">
            <div class="navbar-header">
                <!-- <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span> <span class="icon-bar"></span> <span class="icon-bar"></span>
                </button> -->
                <a class="navbar-brand" style="padding:0px"><img src="${ctx }images/logo.jpg" height="43" class="wxLogTitleImg" data-original-title="" title=""></a>
            	<a class="navbar-brand wxLogTitle">安踏零售精细化管理平台</a>
            </div>
            <div class="navbar-collapse collapse">
                <%-- <ul class="nav navbar-nav">
                    <li><a href="${ctx}tasks/taskCenter.jsp" class="menu">任务中心</a></li>
                    <li><a href="${ctx}tasks/ECharts.jsp" class="menu">任务统计</a></li>
                    <li><a href="${ctx}page/tasktype/taskType.jsp" class="menu">任务模板</a></li>
                    <li><a href="${ctx}basic.do?action=showOrganization" class="menu">人员与组织管理</a></li>
                    <li><a href="${ctx}role/maintenanceTheData.jsp" class="menu">主数据维护</a></li>
                    <li><a href="${ctx}role/rolePage.jsp" class="menu">角色管理</a></li>
                    <li><a href="${ctx}role/permissionsPage.jsp" class="menu">授权管理</a></li>
                    <li><a href="${ctx}role/selLog.jsp" class="menu">日志查看</a></li>
                </ul> --%>
                <ul class="nav navbar-nav navbar-right" style="display: flex;">
                	<li><a href="#" class="menu">
                	欢迎您，<span class="dl-log-user" onclick="updateUserInfo()"><%=userName %></span></a>
                 </li>
                 <li><a href="${ctx }logout.do" class="menu">[退出]</a></li>
                </ul>
            </div>
            <!--/.navbar-collapse -->
        </div>
    </div>
</div>
