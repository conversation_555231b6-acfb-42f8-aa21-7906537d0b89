insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'Dsr.kpi', 1, 1, 'DSR绩效。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Dsr.kpi', 1, 1, 'DSR绩效。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Channel_Manager'), 'Dsr.kpi', 1, 1, 'DSR绩效。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Channel_Manager'), 'Dsr.kpi', 1, 1, 'DSR绩效。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Dsr.kpi', 1, 1, 'DSR绩效。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Dsr.kpi', 1, 1, 'DSR绩效。1-全部', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Dsr.kpi', 2, 1, 'DSR绩效。2-ASM', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Dsr.kpi', 2, 1, 'DSR绩效。2-ASM', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Supervisor'), 'Dsr.kpi', 2, 1, 'DSR绩效。2-ASM', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Supervisor'), 'Dsr.kpi', 2, 1, 'DSR绩效。2-ASM', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Team_Leader'), 'Dsr.kpi', 4, 1, 'DSR绩效。4-Team Leader', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Team_Leader'), 'Dsr.kpi', 4, 1, 'DSR绩效。4-Team Leader', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Dsr.kpi', 8, 1, 'DSR绩效。8-FLSR', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Dsr.kpi', 8, 1, 'DSR绩效。8-FLSR', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'Dsr.kpi', 8, 1, 'DSR绩效。8-FLSR', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'Dsr.kpi', 8, 1, 'DSR绩效。8-FLSR', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Dsr.kpi', 16, 1, 'DSR绩效。16-经销商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'Dsr.kpi', 16, 1, 'DSR绩效。16-经销商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Dsr.kpi', 16, 1, 'DSR绩效。16-经销商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Dsr.kpi', 16, 1, 'DSR绩效。16-经销商', 1, getdate());

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Dsr.kpi', 32, 1, 'DSR绩效2021-KPI设置-mkt', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Dsr.kpi', 32, 1, 'DSR绩效2021-KPI设置-mkt', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Dsr.kpi', 64, 1, 'DSR绩效2021-服务费设置-mkt', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Dsr.kpi', 64, 1, 'DSR绩效2021-服务费设置-mkt', 1, getdate(), NULL, NULL);

/*
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-mkt', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-mkt', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-mkt', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-mkt', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-asm', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-asm', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Team_Leader'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-teamleader', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Team_Leader'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-teamleader', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-flsr', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-flsr', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-abm', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-abm', 1, getdate(), NULL, NULL);
*/

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-abm', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-abm', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-abm', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Dsr.kpi', 128, 1, 'DSR绩效2021-KPI完成情况和服务费发放-abm', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Dsr.kpi', 256, 1, '经销商老板确认', 1, getdate(), NULL, NULL);

