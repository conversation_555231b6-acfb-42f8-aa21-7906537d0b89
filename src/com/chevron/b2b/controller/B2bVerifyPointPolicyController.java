package com.chevron.b2b.controller;

import com.chevron.b2b.business.B2bVerifyPointPolicyBizService;
import com.chevron.b2b.model.B2bVerifyPointPolicyParams;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * B2B核销积分政策Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
@Controller
@RequestMapping(value="/b2bverifypointpolicy")
public class B2bVerifyPointPolicyController {
	@Autowired
	private B2bVerifyPointPolicyBizService b2bVerifyPointPolicyBizService;
	
	private final static Logger log = Logger.getLogger(B2bVerifyPointPolicyController.class);

	/**
	 * B2B核销积分政策列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(B2bVerifyPointPolicyParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			if(StringUtils.isNotBlank(params.getSalesChannel())){
				params.setSalesChannel(ContextUtil.getCurUser().getSalesChannel());
			}
			b2bVerifyPointPolicyBizService.queryForPage(params, resultMap);;
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
}
