package com.chevron.pms.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.pms.service.QrQrCodeService;

@Controller
public class PushGqCodeController {
	
	@Autowired
	private QrQrCodeService qrQrCodeService;
	
	@ResponseBody
	@RequestMapping("/pushGqCode/uploadGqFtp.do")
	public Map<String, Object> uploadGqFtp() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			qrQrCodeService.doPushJob();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/pushGqCode/pushGqCodeByTime.do")
	public Map<String, Object> pushGqCodeByTime(@RequestParam("startTime")String startTime,@RequestParam("endTime")String endTime){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap = qrQrCodeService.pushGqCodeByTime(startTime, endTime);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return resultMap;
	}

}
