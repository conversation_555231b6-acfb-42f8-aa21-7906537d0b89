package com.sys.quartz.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.pms.model.WechatFollow;
import com.chevron.pms.service.QrCodeService;

public class GenerateWechatTempFollowUrlTask {
	private static Logger log = LoggerFactory.getLogger(GenerateWechatTempFollowUrlTask.class);

	@Autowired
	private QrCodeService qrCodeService;
	
	public synchronized void execute(){
		log.info("GenerateWechatTempFollowUrlTask job start....");
		qrCodeService.generateWechatTempFollowInfo(WechatFollow.VERSION_NO_CDM);
		log.info("GenerateWechatTempFollowUrlTask job finish....");
	}
	
	public synchronized void generateDeloCode(){
		log.info("GenerateWechatTempFollowUrlTask.generateDeloCode job start....");
		qrCodeService.generateWechatTempFollowInfo(WechatFollow.VERSION_NO_GENERAL);
		qrCodeService.generateWechatTempFollowInfo(WechatFollow.VERSION_NO_INDUSTRY);
		log.info("GenerateWechatTempFollowUrlTask.generateDeloCode job finish....");
	}
}
