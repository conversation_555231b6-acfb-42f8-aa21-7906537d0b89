package com.common.interceptor;

import com.chevron.pms.model.BaseParams;
import com.common.config.MyPropertyConfigurer;
import com.common.util.CommonUtil;
import com.common.util.PermissionHelper;
import com.common.util.ReflectHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.apache.ibatis.reflection.wrapper.DefaultObjectWrapperFactory;
import org.apache.ibatis.scripting.defaults.DefaultParameterHandler;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.util.*;

@Intercepts({ @Signature(type = StatementHandler.class, method = "prepare", args = Connection.class) })
public class CustomMybatisInterceptor implements Interceptor {

	private static Logger log = LoggerFactory.getLogger(CustomMybatisInterceptor.class);

	private Connection connection;

	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		// add by bo.liu 1125
		// 是否开启过滤
		String is_custommybatisintercepor = MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
		if ("false".equals(is_custommybatisintercepor)) {
			return invocation.proceed();
		}
		// end by bo.liu

		StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
		BoundSql boundSql = statementHandler.getBoundSql();

		connection = (Connection) invocation.getArgs()[0];

		MetaObject metaStatementHandler = MetaObject.forObject(statementHandler, new DefaultObjectFactory(),
				new DefaultObjectWrapperFactory());
		MappedStatement mappedStatement = (MappedStatement) metaStatementHandler.getValue("delegate.mappedStatement");
		String sqlId = mappedStatement.getId();
		String originalSql = boundSql.getSql();

		String newSql = attachAdditionalClause(boundSql, mappedStatement);

		//log.info("rawSql: {}", newSql!=null?"\r\n"+newSql.replaceAll("\\t|\\r|\\n", " ").replaceAll("\\s{2,}"," "):newSql);
		if (!originalSql.equals(newSql)) {
			ReflectHelper.setFieldValue(boundSql, "sql", newSql);
		}

		String sqlStatment = getActualSqlStatement(mappedStatement.getConfiguration(), boundSql);

		Object result = null;
		try {
			result = invocation.proceed();
			log.info("MapperId: [{}]\r\n -------------------------------\r\n{}\r\n -------------------------------\r\n", sqlId, sqlStatment);
		} catch (Exception e) {
			log.error("exception occured when executing ID: {}, Statement: {}, ", sqlId, sqlStatment, e);
		}

		return result;
	}

	private String attachAdditionalClause(BoundSql boundSql, MappedStatement mappedStatement) {

		// 1, 权限
		String sqlWithPermissionClause = assemblePermissionClause(boundSql, mappedStatement);
		// 2, 分页、排序
		return assemblePaginationClause(sqlWithPermissionClause, boundSql, mappedStatement);
	}

	private String assemblePaginationClause(String sql, BoundSql boundSql, MappedStatement mappedStatement) {
		String sqlId = mappedStatement.getId();
		if (sqlId.endsWith("selectByExample")) { // 不处理selectByExample的
			// 直接排序
			return assembleOrderByClause(sql, boundSql, mappedStatement);
		}
		Object parameterObject = boundSql.getParameterObject();
		if (parameterObject == null) {
			// 直接排序
			return assembleOrderByClause(sql, boundSql, mappedStatement);
		}
		// 分页参数必须继承BaseParams
		if (parameterObject instanceof BaseParams) {
			validateBaseParams(parameterObject);
			BaseParams baseParams = (BaseParams) parameterObject;
			boolean isPaging = Boolean.parseBoolean(getParameterValueFromParameterObject(
					mappedStatement.getConfiguration(), "isPaging", parameterObject));
			if (!isPaging) { // 无需分页
				// 直接排序
				return assembleOrderByClause(sql, boundSql, mappedStatement);
			}

			boolean queryTotal = Boolean.parseBoolean(getParameterValueFromParameterObject(
					mappedStatement.getConfiguration(), "queryTotal", parameterObject));
			// 计算总记录数
			if (queryTotal) {
				setTotalRecord(sql, boundSql.getParameterMappings(), baseParams, mappedStatement);
			}

			String subSqlId = StringUtils.substringAfterLast(sqlId, ".");
			if (subSqlId.startsWith("total") && subSqlId.endsWith("ForPage")) {
				return sql;
			}
			return assembleOrderByClause(getPaginationSql(baseParams, sql), boundSql, mappedStatement);
		}
		// 直接排序
		return assembleOrderByClause(sql, boundSql, mappedStatement);
	}

	private void validateBaseParams(Object parameterObject) {
		BeanWrapper beanWrapper = new BeanWrapperImpl(parameterObject);
		PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();
		Set<String> fields = new HashSet<String>();
		for (PropertyDescriptor property : propertyDescriptors) {
			fields.add(property.getName());
		}
//		//校验排序字段是否是参数类字段,不为参数字段则直接置空
//		String field = beanWrapper.getPropertyValue("field").toString();
//		if (StringUtils.isNotBlank(field)&&!fields.contains(field)) {
//			beanWrapper.setPropertyValue("field", null);
//		}

		//校验排序是否是desc或asc，不是则默认desc
		String direction = beanWrapper.getPropertyValue("direction").toString();
		if (StringUtils.isNotBlank(direction)&&(!"desc".equalsIgnoreCase(direction) && !"asc".equalsIgnoreCase(direction))) {
			beanWrapper.setPropertyValue("direction", "desc");
		}
	}

	private String getPaginationSql(BaseParams baseParams, String sql) {
		StringBuilder newSql = new StringBuilder();
		newSql.insert(0, "SELECT TOP " + baseParams.getLimit() + " nest_table3.* FROM ( ")
				.append(getSqlWithRownumber(baseParams, sql)).append(" ) nest_table3 WHERE rownumber > ")
				.append(baseParams.getStart());
		return newSql.toString();
	}

	private String getSqlWithRownumber(BaseParams baseParams, String sql) {
		StringBuilder sqlWithRowNumber = new StringBuilder();
		sqlWithRowNumber.insert(0,
				"SELECT row_number() OVER(ORDER BY " + baseParams.getOrder() + ") AS rownumber, nest_table2.* FROM ( ")
				.append(sql).append(" ) nest_table2");
		return sqlWithRowNumber.toString();
	}

	private String assembleOrderByClause(String sql, BoundSql boundSql, MappedStatement mappedStatement) {
		String sqlId = mappedStatement.getId();
		if (sqlId.endsWith("selectByExample")) { // 不处理selectByExample的
			return sql;
		}
		Object parameterObject = boundSql.getParameterObject();
		if (parameterObject == null) {
			return sql;
		}

		String orderByClause = getParameterValueFromParameterObject(mappedStatement.getConfiguration(), "orderBy",
				boundSql.getParameterObject());
		if (StringUtils.isEmpty(orderByClause)) {
			return sql;
		}
		CommonUtil.checkSqlParam(orderByClause);
		StringBuilder newSql = new StringBuilder();
		newSql.append("SELECT * FROM ( ").append(sql).append(" ) nest_table4 ORDER BY ").append(orderByClause);
		return newSql.toString();
	}

	private String assemblePermissionClause(BoundSql boundSql, MappedStatement mappedStatement) {
		String sqlId = mappedStatement.getId();
		String sql = boundSql.getSql();

		return PermissionHelper.assemblePermissionClause(sqlId, sql, boundSql.getParameterObject());
	}

	@Override
	public Object plugin(Object arg0) {
		//log.debug("Mybatis plugin(): {}", arg0.getClass());
		if (arg0 instanceof StatementHandler) {
			return Plugin.wrap(arg0, this);
		} else {
			return arg0;
		}
	}

	@Override
	public void setProperties(Properties arg0) {
	}

	public String removeBreakingWhitespace(String original) {
		StringTokenizer stringTokenizer = new StringTokenizer(original);
		StringBuilder sb = new StringBuilder();
		while (stringTokenizer.hasMoreTokens()) {
			sb.append(stringTokenizer.nextToken()).append(" ");
		}
		return sb.toString();

	}

	public String getActualSqlStatement(Configuration configuration, BoundSql boundSql) {
		Object parameterObject = boundSql.getParameterObject();
		List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
		String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
		if (!parameterMappings.isEmpty() && parameterObject != null) {
			TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
			try {
				if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
					sql = sql.replaceFirst("\\?", "'" + getParameterValue(parameterObject) + "'");

				} else {
					MetaObject metaObject = configuration.newMetaObject(parameterObject);
					for (ParameterMapping parameterMapping : parameterMappings) {
						String propertyName = parameterMapping.getProperty();
						if (metaObject.hasGetter(propertyName)) {
							Object obj = metaObject.getValue(propertyName);
							sql = sql.replaceFirst("\\?", "'" + getParameterValue(obj) + "'");
						} else if (boundSql.hasAdditionalParameter(propertyName)) {
							Object obj = boundSql.getAdditionalParameter(propertyName);
							sql = sql.replaceFirst("\\?", "'" + getParameterValue(obj) + "'");
						}
					}
				}
			} catch (Exception e) {
				log.warn(e.getMessage(),e);
			}
		}
		return sql;
	}

	private String getParameterValue(Object obj) {
		String value;
		if (obj instanceof String) {
			value = obj.toString();
		} else if (obj instanceof Date) {
			DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
			value = formatter.format(obj);
		} else {
			if (obj != null) {
				value = obj.toString();
			} else {
				value = "";
			}
		}
		return value;
	}

	private String getParameterValueFromParameterObject(Configuration configuration, String paramName,
			Object parameterObject) {
		TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
		if (!typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) { // 复杂类型(HashMap或自定义对象)
			MetaObject metaObject = configuration.newMetaObject(parameterObject);
			if (metaObject.hasGetter(paramName)) {
				Object obj = metaObject.getValue(paramName);
				return getParameterValue(obj);
			}
		}
		return "";
	}

	private void setTotalRecord(String sql, List<ParameterMapping> parameterMappings, BaseParams baseParams,
			MappedStatement mappedStatement) {
		String countSql = getCountSql(sql);
		// 利用Configuration、查询记录数的Sql语句countSql、参数映射关系parameterMappings和参数对象page建立查询记录数对应的BoundSql对象。
		BoundSql countBoundSql = new BoundSql(mappedStatement.getConfiguration(), countSql, parameterMappings,
				baseParams);
		// 通过mappedStatement、参数对象page和BoundSql对象countBoundSql建立一个用于设定参数的ParameterHandler对象
		ParameterHandler parameterHandler = new DefaultParameterHandler(mappedStatement, baseParams, countBoundSql);
		// 通过connection建立一个countSql对应的PreparedStatement对象。
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		log.info("分页统计查询={}", countSql);
		try {
			pstmt = connection.prepareStatement(countSql);
			// 通过parameterHandler给PreparedStatement对象设置参数
			parameterHandler.setParameters(pstmt);
			// 之后就是执行获取总记录数的Sql语句和获取结果了。
			rs = pstmt.executeQuery();
			if (rs.next()) {
				int totalRecord = rs.getInt(1);
				// 给当前的参数page对象设置总记录数
				baseParams.setTotalCount(totalRecord);
			}
		} catch (SQLException e) {
			log.error("calculate totalCount exception", e);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("错误信息", e);
		} finally {
			try {
				if (rs != null)
					rs.close();
				if (pstmt != null)
					pstmt.close();
			} catch (SQLException e) {
				log.error("release db resource after calculate totalCount exception", e);
			}
		}
	}

	private String getCountSql(String sql) {
		// int index = sql.toLowerCase().indexOf("from");
		// return "select count(*) " + sql.substring(index);
		// modify by bo.liu
		return "SELECT COUNT(*) FROM (" + sql + ") AS nest_count_table";
	}

}
