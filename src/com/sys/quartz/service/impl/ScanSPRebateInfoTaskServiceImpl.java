package com.sys.quartz.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import com.chevron.log.dao.WxTImportLogMapper;
import com.chevron.log.model.WxTImportLog;
import com.chevron.partnerorder.dao.PartnerBillMapper;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.token.util.TokenUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.lowagie.text.pdf.BaseFont;
import com.sys.email.service.EmailSenderService;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.quartz.model.SpEmailInfo;
import com.sys.quartz.model.SpInfo;
import com.sys.quartz.model.SpRebateInfo;
import com.sys.quartz.model.SpRebateNew;
import com.sys.quartz.service.ScanSPRebateInfoTaskService;

import freemarker.template.Configuration;
import freemarker.template.Template;
/**
 * 获取合伙人返利信息服务类
 * @Copyright: 本内容仅限于德勤公司内部使用，禁止转发. 
 * @Author: bo.liu  2018-5-22 上午9:49:36 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class ScanSPRebateInfoTaskServiceImpl implements ScanSPRebateInfoTaskService{

	public final Logger logerr = Logger.getLogger(this.getClass());
	DecimalFormat  dfDouble  =  new   DecimalFormat( "#,##0.00");
	DecimalFormat  dfInteger  =  new   DecimalFormat( "#,##0");
	public static String SP_LOG_DATA_TYPE = "SP_REBATE_TYPE";
	public static String PDF_TEMPLATES_DIR="/WEB-INF/templates/";
	public static String TEMPLATES_FONT_PATH="/WEB-INF/simsun.ttc";
	public static String FTLIMAGEPATH = "images\\logo2.png";
	public static final String SP_REBATE_CONFIRM_TYPE = "sprebateconfirm";
	public static final String CONFIRM_STATUS = "1";//已确认的状态 恢复成默认的状态
	
	public static final String SCAN_REBATE_TYPE = "OILVERIFICATION_REBATE";
	public static final String LADER_REBATE_TYPE = "LADDERPRICE_REBATE";
	public static final String LOG_RESULT_NONE = "none";
	public static final String LOG_RESULT_ERROR = "error";
	public static final String FINAL_EMAIL_TYPE = "sprebate.finalemail";
	@Resource 
	private EmailSenderService emailSenderService;
	@Resource
	private OrganizationVoMapper orgMapper;
	@Autowired
	private WxTImportLogMapper importMapper;
	@Resource 
	private PartnerBillMapper partnerBillMapper;
	
	@Override
	public void doHandleSpRebateTask(Long partnerId, String dateTime) {
		WxTImportLog spRebatelog = null;
		try
		{
			//获取查询时间条件
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap = getQueryDate(dateTime);
			reqMap.put("partnerId", partnerId);
			String queryDate = (String) reqMap.get("queryDate");
			//获取SP第一个的扫码升数及返利，（看是遍历查询数据库获取，，还是统一查询获取，最后通过遍历，存放到map中等）
			//List<SpRebateInfo> scanRebates = orgMapper.getSpScanRebate(reqMap);
			
			//获取SP本月的阶梯满减及返利,,同理与扫码升数
			//List<SpRebateInfo> laderRebates = orgMapper.getSpLaderRebate(reqMap);
			
			//组装附件信息实体？ 通过上面的返利进行组织SpRebateInfo的实体对象
			//List<SpRebateInfo> targetSpRebateInfos = getTargetSpRebateInfos(scanRebates,laderRebates);
			
			
			//获取统满足条件统计的数据：
			List<SpRebateNew> lstSpRebate = orgMapper.getSpRebate(reqMap);
			if(null==lstSpRebate || lstSpRebate.isEmpty())
			{
				spRebatelog = new WxTImportLog();
				spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
				spRebatelog.setImportDetailMessage("无返利数据信息lstSpRebate");
				spRebatelog.setImportResult(LOG_RESULT_NONE);
				spRebatelog.setCreateTime(new Date());
				importMapper.insertSelective(spRebatelog);
				logerr.info("ScanSPRebateInfoTaskServiceImpl doHandleSpRebateTask lstSpRebate is null");
				return ;
			}
			//组装附件信息实体？ 通过上面的返利进行组织SpRebateInfo的实体对象
			List<SpRebateInfo> targetSpRebateInfos = getTargetSpRebateInfos(lstSpRebate);
			if(null==targetSpRebateInfos || targetSpRebateInfos.isEmpty())
			{
			    spRebatelog = new WxTImportLog();
				spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
				spRebatelog.setImportDetailMessage("无返利数据信息targetSpRebateInfos");
				spRebatelog.setImportResult(LOG_RESULT_NONE);
				spRebatelog.setCreateTime(new Date());
				importMapper.insertSelective(spRebatelog);
				logerr.info("ScanSPRebateInfoTaskServiceImpl doHandleSpRebateTask targetSpRebateInfos is null");
				return ;
			}
			
			
			//获取合伙人超级管理员的信息
			Map<String,Object> accepterEmailMap = getEmailAccepter();
			
			//获取抄送者邮箱信息，
			Map<String,Object> ccMap = getEmailCC();
			
			//发送件
			sndSpRebateEmail(accepterEmailMap,ccMap,targetSpRebateInfos,queryDate);
			
		}catch (Exception e) {
			e.printStackTrace();
			//录入失败的日志信息
			spRebatelog = new WxTImportLog();
			spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
			String message  = e.getLocalizedMessage();
			if(null==message||message.trim().isEmpty())
			{
				message = "";
			}else
			{
				message = message.trim().substring(0,message.trim().length()-20);
			}
			spRebatelog.setImportDetailMessage("出错了，异常信息："+message);
			spRebatelog.setImportResult(LOG_RESULT_ERROR);
			spRebatelog.setCreateTime(new Date());
			importMapper.insertSelective(spRebatelog);
			logerr.error("ScanSPRebateInfoTaskServiceImpl doHandleSpRebateTask Exception:"+e.getMessage());
		}
		
	}
	
	
	private List<SpRebateInfo> getTargetSpRebateInfos(
			List<SpRebateNew> lstSpRebate)throws Exception {
		logerr.info("ScanSPRebateInfoTaskServiceImpl getTargetSpRebateInfos lstSpRebate.size()："+lstSpRebate.size());
		List<SpRebateInfo> resultSpRebateInfos = new ArrayList<SpRebateInfo>();
		//遍历，生成目标返利列表
		Long lastPartnerId = 0L;
		Long lastNewPartnerId = 0L;//控制->新实例对象
		Long currentPartnerId = 0L;//控制->原始当前的
		SpRebateInfo currentSpRebateInfo = null;
		for(SpRebateNew spRebateInfo: lstSpRebate)
		{
			currentPartnerId = spRebateInfo.getPartnerId();
			int literCount= spRebateInfo.getLiterCount();
			double rebateAmount = spRebateInfo.getRebateAmount();
			String transactionSource = spRebateInfo.getTransactionSource();
			if(!currentPartnerId.equals(lastPartnerId))
			{
				currentSpRebateInfo = new SpRebateInfo();
				currentSpRebateInfo.setSpName(spRebateInfo.getPartnerName());
				currentSpRebateInfo.setPartnerId(spRebateInfo.getPartnerId());
				
			}
			
			//核销扫码
			if(SCAN_REBATE_TYPE.equals(transactionSource))
			{
				currentSpRebateInfo.setScanNumber(literCount);
				currentSpRebateInfo.setScanRebate(rebateAmount);
				currentSpRebateInfo.setScanNumberStr(dfInteger.format(literCount));
				currentSpRebateInfo.setScanRebateStr(dfDouble.format(rebateAmount));
			}else if(LADER_REBATE_TYPE.equals(transactionSource))//阶梯返利
			{
				currentSpRebateInfo.setLaderNumber(literCount);
				currentSpRebateInfo.setLaderRebate(rebateAmount);
				currentSpRebateInfo.setLaderNumberStr(dfInteger.format(literCount));
				currentSpRebateInfo.setLaderRebateStr(dfDouble.format(rebateAmount));
			}
			lastPartnerId = currentPartnerId;
			
			
			if(!currentPartnerId.equals(lastNewPartnerId))
			{
				resultSpRebateInfos.add(currentSpRebateInfo);
			}
			lastNewPartnerId = currentPartnerId;
		}
		logerr.info("ScanSPRebateInfoTaskServiceImpl getTargetSpRebateInfos resultSpRebateInfos.size()："+resultSpRebateInfos.size());
		return resultSpRebateInfos;
	}


	private void sndSpRebateEmail(Map<String, Object> accepterEmailMap,
			Map<String, Object> ccMap, List<SpRebateInfo> targetSpRebateInfos,String queryDate)throws Exception {
		logerr.info("ScanSPRebateInfoTaskServiceImpl sndSpRebateEmail targetSpRebateInfos.size()："+targetSpRebateInfos.size()+" queryDate:"+queryDate);
		WxTImportLog spRebatelog = null;
		//从导入的日志表中去查询，已经录入发送了的SP返利邮件
		Map<String,Object> hasSndEmailMap = getHasSndSpRebateEmailInfo(queryDate);
		for(SpRebateInfo spRebateInfo:targetSpRebateInfos)
		{
			String parnterName = spRebateInfo.getSpName();
			//用于邮件确认....
			Long userId = (Long) accepterEmailMap.get(""+spRebateInfo.getPartnerId());
			//判断是否已经发送了sp返利的邮件
			WxTImportLog hasSpRebateLog = (WxTImportLog) hasSndEmailMap.get(""+spRebateInfo.getPartnerId());
			if(null!=hasSpRebateLog)
			{
				//已经被发送过不需要从新发送，，需要录入到日志表中
				spRebatelog = new WxTImportLog();
				spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
				spRebatelog.setImportDetailMessage("已经被发送过不需要从新发送,合伙人："+parnterName+",文件名："+hasSpRebateLog.getFileName());
				spRebatelog.setImportResult(LOG_RESULT_NONE);
				spRebatelog.setCreateTime(new Date());
				importMapper.insertSelective(spRebatelog);
				continue;
			}
			
			//邮件接收者
			String[] accepterEmails = (String[]) accepterEmailMap.get(parnterName);
			if(null==accepterEmails || accepterEmails.length==0)
			{
				//记录到日志表汇总
				spRebatelog = new WxTImportLog();
				spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
				spRebatelog.setImportDetailMessage("合伙人超级管理员邮箱没有配置,合伙人："+parnterName);
				spRebatelog.setImportResult(LOG_RESULT_NONE);
				spRebatelog.setCreateTime(new Date());
				importMapper.insertSelective(spRebatelog);
				continue;
			}
			//邮件抄送者
			String[] ccEmails = (String[])ccMap.get(parnterName);
			//生成pdf文件
			File[] files = getSpRebatePdfFile(spRebateInfo,queryDate);
			if(null==files || files.length==0)
			{
				//记录到日志表中
				spRebatelog = new WxTImportLog();
				spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
				spRebatelog.setImportDetailMessage("没有找到对应的pdf文件失败,合伙人："+parnterName);
				spRebatelog.setImportResult(LOG_RESULT_NONE);
				spRebatelog.setCreateTime(new Date());
				importMapper.insertSelective(spRebatelog);
				continue;
			}
			
			boolean isSuccess = sndeEmail(accepterEmails,ccEmails,files,spRebateInfo.getPartnerId(),userId,queryDate,parnterName);
			if(isSuccess)
			{
				//录入成功的日志信息，，包含文件名（文件名_年_月），合伙人，录入时间
				spRebatelog = new WxTImportLog();
				spRebatelog.setImportDataType(SP_LOG_DATA_TYPE);
				spRebatelog.setFileName(files[0].getName());
				spRebatelog.setImportDetailMessage("success,发送"+queryDate+"返利邮件成功,合伙人："+parnterName);
				spRebatelog.setImportResult(queryDate);
				spRebatelog.setCreateTime(new Date());
				spRebatelog.setCreatedBy(spRebateInfo.getPartnerId());//存放合伙人id
				importMapper.insertSelective(spRebatelog);
			}
		}
	}


	private Map<String, Object> getHasSndSpRebateEmailInfo(String queryDate)throws Exception {
		logerr.info("ScanSPRebateInfoTaskServiceImpl getHasSndSpRebateEmailInfo  queryDate:"+queryDate);
		//从日志表中查询已经发送了的sp返利  邮件信息
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("importDataType", SP_LOG_DATA_TYPE);
		reqMap.put("importResult", queryDate);
		List<WxTImportLog> lstLog = importMapper.getLogInfo(reqMap);
		
		Map<String,Object> resultMap = new HashMap<String,Object>();
		for(WxTImportLog log:lstLog)
		{
			resultMap.put(""+log.getCreatedBy(), log);//creteBy存放的是合伙人id
		}
		return resultMap;
	}


	private boolean sndeEmail(String[] accepterEmails, String[] ccEmails,
			File[] files,Long partnerId,Long userId,String queryDate,String parnterName)throws Exception  {
		logerr.info("ScanSPRebateInfoTaskServiceImpl sndeEmail  queryDate:"+queryDate+" userId:"+userId+" partnerId:"+partnerId+" fileName:"+files[0].getName());
		//生成确认的链接信息
		String basePath = (String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+"/";//"http://localhost:80/";//
		String confirmLink = "";
		
		confirmLink=basePath+TokenUtil.generateTokenLink(String.valueOf(userId), SP_REBATE_CONFIRM_TYPE, "", "", true,""+partnerId,CONFIRM_STATUS);
		
		Map<String,Object> emailInfoMap = new HashMap<String,Object>();
		emailInfoMap.put("accepters", accepterEmails);
		emailInfoMap.put("ccaccepters", ccEmails);
		emailInfoMap.put("confirmlink", confirmLink);
		emailInfoMap.put("acceptUserName", parnterName+"合伙人");
		
		//发送邮件信息
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		emailInfoMap.put("msgcontent", "附件是您"+queryDate.split("-")[1]+"月份的返利信息，请查收！");
		String subject = queryDate.replaceAll("-", "年")+"月返利"+"("+parnterName+")["+DateUtils.getCurrentDate("yyyyMMddHHmmss")+"]";
		emailSenderService.sendEmailForCommon(context,
				(String[])emailInfoMap.get("accepters"), (String[])emailInfoMap.get("ccaccepters"), subject,emailInfoMap, files, MyPropertyConfigurer.getVal("PARTNER_REBATE_EMAIL_FTL"));
		return true;
	}


	private File[] getSpRebatePdfFile(SpRebateInfo spRebateInfo,String queryDate)throws Exception  {
		logerr.info("ScanSPRebateInfoTaskServiceImpl getSpRebatePdfFile  queryDate:"+queryDate+" spRebateInfo:"+spRebateInfo.toString());
		Map<String,Object> sprebateMap = new HashMap<String,Object>();
		spRebateInfo.setExportDate(DateUtil.getCurrentDate("dd/MM/yyyy"));
		spRebateInfo.setTotalNumber(dfInteger.format(spRebateInfo.getScanNumber()+spRebateInfo.getLaderNumber()));
		spRebateInfo.setTotalRebate(dfDouble.format(spRebateInfo.getScanRebate()+spRebateInfo.getLaderRebate()));
		//quartz中使用此获取上下文
		ServletContext servletContext = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		String filePath = servletContext.getRealPath(MyPropertyConfigurer
				.getVal("PARTNER_REBATE_EMAIL_TMP_DATA"))+File.separator  + spRebateInfo.getSpName() + "_" + queryDate.replaceAll("-", "_")  +".pdf";
		File file = new File(
				filePath.trim());
		OutputStream baos = new FileOutputStream(file);

		String basePath = servletContext.getRealPath("/");//绝对路径 
		String ftlName=MyPropertyConfigurer
				.getVal("PARTNER_REBATE_PDF");
        Configuration cfg = new Configuration();  
        cfg.setLocale(Locale.CHINA);  
        cfg.setEncoding(Locale.CHINA, "UTF-8");  
        //设置编码  
        cfg.setDefaultEncoding("UTF-8");
        //设置模板路径  
        cfg.setDirectoryForTemplateLoading(new File(basePath + PDF_TEMPLATES_DIR));  
        
        //获取模板  
        Template template = cfg.getTemplate(ftlName);  
        template.setEncoding("UTF-8");    
        Writer writer = new StringWriter();  
        
        
        //图片信息：
        String imageName = basePath + FTLIMAGEPATH;
        //解决图片路径问题   设置好图片所选择的路径
        if(imageName!=null && !"".equals(imageName)){
        	sprebateMap.put("imagePath",imageName);
        }
        sprebateMap.put("basePath",basePath);
        
        
        //数据填充模板  
      /*  String baseUrl = servletContext.getScheme()+"://"+request.getServerName()+":"+
                request.getServerPort()+"/";*/
        String baseUrl = (String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+"/";//"http://localhost:80/";//
        sprebateMap.put("baseUrl", baseUrl);
        sprebateMap.put("spRebateInfo", spRebateInfo);
        template.process(sprebateMap, writer);  
        String str = writer.toString();  
        //pdf生成	            	          
        ITextRenderer iTextRenderer = new ITextRenderer();
   	           
        //设置字体  其他字体需要添加字体库  
        ITextFontResolver fontResolver = iTextRenderer.getFontResolver();  
        fontResolver.addFont(basePath + TEMPLATES_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);  
        iTextRenderer.setDocumentFromString(str);  
        iTextRenderer.layout(); 
   
        //生成PDF  
        iTextRenderer.createPDF(baos);  
        baos.close();    
		
		File[] attFiles = new File[1];
		attFiles[0] = file;
		logerr.info("ScanSPRebateInfoTaskServiceImpl getSpRebatePdfFile  success fileName:"+attFiles[0].getName());
		return attFiles;
	}


	private Map<String, Object> getEmailCC()throws Exception {
		logerr.info("ScanSPRebateInfoTaskServiceImpl getEmailCC");
		//获取销售负责人的邮箱信息
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<SpEmailInfo> lstCCEmail = orgMapper.getCCEmail(reqMap);
		//获取固定的抄送者邮箱
		reqMap.put("finalEmailType", FINAL_EMAIL_TYPE);
		List<String> finalCCEmails = orgMapper.getFinalCCEmail(reqMap);
		logerr.info("ScanSPRebateInfoTaskServiceImpl getEmailCC finalCCEmails:"+finalCCEmails.toString());
		//返回
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<String> emailCCs = null;
		String lastPartnerName = "";
		String currentPartnerName = "";
		for(SpEmailInfo emailInfo:lstCCEmail)
		{
			currentPartnerName = emailInfo.getPartnerName();
			if(!currentPartnerName.equals(lastPartnerName))
			{
				emailCCs = new ArrayList<String>();
				//添加固定的抄送者信息
				emailCCs.addAll(finalCCEmails);
			}
			String emailstr = emailInfo.getEmail();
			if(null!=emailstr && !emailstr.isEmpty())
			{
				if(!emailCCs.contains(emailInfo.getEmail()))
				{
					emailCCs.add(emailInfo.getEmail());
				}
			}
			resultMap.put(currentPartnerName, emailCCs.toArray(new String[emailCCs.size()]));
			logerr.info("ScanSPRebateInfoTaskServiceImpl getEmailCC partnerName:"+currentPartnerName+"emailCCs:"+emailCCs.toString());
			lastPartnerName = currentPartnerName;
		}
		return resultMap;
	}


	private Map<String, Object> getEmailAccepter()throws Exception {
		logerr.info("ScanSPRebateInfoTaskServiceImpl getEmailAccepter");
		List<SpEmailInfo> lstAccepterEmail = orgMapper.getSenderEmail(new HashMap<String,Object>());
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<String> emailAccepters = null;
		String lastPartnerName = "";
		String currentPartnerName = "";
		Long partnerId = 0L;
		for(SpEmailInfo emailInfo:lstAccepterEmail)
		{
			currentPartnerName = emailInfo.getPartnerName();
			partnerId = emailInfo.getPartnerId();
			if(!currentPartnerName.equals(lastPartnerName))
			{
				emailAccepters = new ArrayList<String>();
				//取第一个超级管理员的用户id作为   邮件的确认id
				resultMap.put(""+partnerId, emailInfo.getUserId());
			}
			
			if(!emailAccepters.contains(emailInfo.getEmail()))
			{
				emailAccepters.add(emailInfo.getEmail());
			}
			resultMap.put(currentPartnerName, (emailAccepters.toArray(new String[emailAccepters.size()])));
			logerr.info("ScanSPRebateInfoTaskServiceImpl getEmailAccepter partnerName:"+currentPartnerName+"emailAccepters:"+emailAccepters.toString());
			lastPartnerName = currentPartnerName;
		}
		return resultMap;
	}


	private List<SpRebateInfo> getTargetSpRebateInfos(
			List<SpRebateInfo> scanRebates, List<SpRebateInfo> laderRebates)throws Exception {
		List<SpRebateInfo> allSpRebateInfos = new ArrayList<SpRebateInfo>();
		List<SpRebateInfo> resultSpRebateInfos = new ArrayList<SpRebateInfo>();
		allSpRebateInfos.addAll(scanRebates);
		allSpRebateInfos.addAll(laderRebates);
		//排序
		Collections.sort(allSpRebateInfos, new Comparator<SpRebateInfo>(){  
			  
	        public int compare(SpRebateInfo o1, SpRebateInfo o2) {  
	          
	        	Long d1 = o1.getPartnerId();
	        	Long d2 = o2.getPartnerId();
	            if(d1 > d2){  
	                return 1;  
	            }  
	            if(d1 == d2){  
	                return 0;  
	            }  
	            return -1;  
	        }  
	    });   
		
		
		//遍历，生成目标返利列表
		Long lastPartnerId = 0L;
		Long lastNewPartnerId = 0L;//控制->新实例对象
		Long currentPartnerId = 0L;//控制->原始当前的
		SpRebateInfo currentSpRebateInfo = null;
		for(SpRebateInfo spRebateInfo: allSpRebateInfos)
		{
			currentPartnerId = spRebateInfo.getPartnerId();
			int scanNumber= spRebateInfo.getScanNumber();
			double scanRebate = spRebateInfo.getScanRebate();
			
			int laderNumber = spRebateInfo.getLaderNumber();
			double laderRebate = spRebateInfo.getLaderRebate();
			 
			if(!currentPartnerId.equals(lastPartnerId))
			{
				currentSpRebateInfo = new SpRebateInfo();
				currentSpRebateInfo.setSpName(spRebateInfo.getSpName());
				currentSpRebateInfo.setPartnerId(spRebateInfo.getPartnerId());
				
			}
			
			if(scanNumber!=0)
			{
				currentSpRebateInfo.setScanNumber(scanNumber);
				currentSpRebateInfo.setScanRebate(scanRebate);
				currentSpRebateInfo.setScanNumberStr(dfInteger.format(scanNumber));
				currentSpRebateInfo.setScanRebateStr(dfDouble.format(scanRebate));
			}
			
			if(laderNumber!=0)
			{
				currentSpRebateInfo.setLaderNumber(laderNumber);
				currentSpRebateInfo.setLaderRebate(laderRebate);
				currentSpRebateInfo.setLaderNumberStr(dfInteger.format(laderNumber));
				currentSpRebateInfo.setLaderRebateStr(dfDouble.format(laderRebate));
			}
			lastPartnerId = currentPartnerId;
			
			
			if(!currentPartnerId.equals(lastNewPartnerId))
			{
				resultSpRebateInfos.add(currentSpRebateInfo);
			}
			lastNewPartnerId = currentPartnerId;
		}
		
		return resultSpRebateInfos;
	}


	private Map<String, Object> getQueryDate(String dateTime)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		Date currentDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Calendar c = Calendar.getInstance();
		if(null!=dateTime)
		{
			currentDate = DateUtil.parseDate(dateTime,"yyyy-MM-dd");
			
		}
		c.setTime(new Date());
		c.add(Calendar.MONTH, -1);
	    Date m = c.getTime();
	    String startDate = format.format(m);
	    
	    
	    //reqMap.put("startDate", startDate+"-01");
	    reqMap.put("queryDate", startDate);
	    //reqMap.put("endDate", DateUtil.getDateStr(currentDate, "yyyy-MM")+"-01");
		return reqMap;
	}


	private List<SpInfo> getNeedRebateSPInfo(Long partnerId)
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("partnerId", partnerId);
		
		return null;
	}
	
	
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public boolean updateSpRebateByPartnerId(String partnerId, String status) {
		try
		{
			//更新合伙人账单的状态.....
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("partnerId", partnerId);
			reqMap.put("status", status);
			partnerBillMapper.updateStatusByMap(reqMap);
			//CSR去人，更新状态为3，同时需要发送邮件
			if("3".equals(status))
			{
				doHandleSpRebateTask(Long.parseLong(partnerId), null);
			}
			
		}catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			return false;
		}
		return true;
	}

}
