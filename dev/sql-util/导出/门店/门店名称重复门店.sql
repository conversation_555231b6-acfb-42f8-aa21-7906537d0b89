
select wp1.partner_id, o1.organization_name, u.ch_name "DSR", w1.id, w1.work_shop_name "门店名称", w1.work_shop_address, 
case when w1.from_source=1 then '乘用车业务' else '商用油业务' end "门店来源", 
case when w1.shop_recruitment=1 then '是' else '否' end "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w1.id) then '是' else '否' end "B2B店",
case when w1.dms_key is null or w1.dms_key='' then '否' else '是' end "DMS店", w1.contact_person_tel, w1.create_time "录入时间",
w2.id id2, w2.work_shop_name "门店名称2", w2.work_shop_address work_shop_address2, u2.ch_name "DSR2", 
case when w2.from_source=1 then '乘用车业务' else '商用油业务' end "门店来源2", 
case when w2.shop_recruitment=1 then '是' else '否' end "店招店2",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w2.id) then '是' else '否' end "B2B店2",
case when w2.dms_key is null or w2.dms_key='' then '否' else '是' end "DMS店2", w2.contact_person_tel contact_person_tel2, w2.create_time "录入时间2"
from wx_t_work_shop w1
left join wx_t_workshop_partner wp1 on wp1.workshop_id=w1.id
left join wx_t_organization o1 on o1.id=wp1.partner_id
left join wx_t_work_shop w2 on w2.from_source=1 and w2.delete_flag=0
left join wx_t_workshop_partner wp2 on wp2.workshop_id=w2.id
left join wx_t_user u on u.user_id=w1.excute_user_id
left join wx_t_user u2 on u2.user_id=w2.excute_user_id
where w1.delete_flag=0 and w1.id!=w2.id and w1.work_shop_name=w2.work_shop_name and w1.status='3' and w2.status='3' and w2.delete_flag=0
and wp1.partner_id=wp2.partner_id and o1.id!=9 and w1.from_source&3>0 and w2.from_source&3>0
order by wp1.partner_id, w1.work_shop_name, w1.id