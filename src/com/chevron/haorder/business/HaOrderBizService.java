package com.chevron.haorder.business;

import com.chevron.haorder.dao.HaOrderProductPriceInfoVoMapper;
import com.chevron.haorder.dao.HaOrderUploadVoMapper;
import com.chevron.haorder.model.HaOrderProductPriceInfoVo;
import com.chevron.haorder.model.HaOrderUploadView;
import com.chevron.haorder.model.uploadto.UploadHaOrderInfo;
import com.chevron.haorder.model.uploadto.UploadHaOrderLineInfo;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.model.PartnerResponsibleVo;
import com.chevron.pms.model.WarehouseInfoVo;
import com.chevron.thirdorder.model.OrderCommonVo;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.token.util.TokenUtil;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.SftpUtil;
import com.sys.auth.model.WxTRole;
import com.sys.email.service.EmailSenderService;
import com.sys.file.web.FileManager;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @Author: bo.liu  2017-8-22 上午11:48:14 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class HaOrderBizService {

	public final Logger logerr = Logger.getLogger(this.getClass());
	public static final String BATCH_ID = "bachId";
	public static final String CHROLE_NAME="chRoleName";
	public static final String HAORDER_REFUND_TYPE = "haorderrefund";
	public static final String HAORDER_CONFIRM_TYPE = "haorderconfirm";
	public static final String HAORDER_SAP_CSR_REFUND_TYPE = "haorderSapCSRrefund";
	public static final String HAORDER_SAP_CSR_CONFIRM_TYPE = "haorderSapCSRconfirm";
	public static final String PD_KEY =  "pdKey";//流程定义key
	public static final String ZGIS = "ZGIS";
	public static final String ZOOR = "ZOOR";
	public static final String HAORDER_SFTP="haorder.sftp.";
	public static final String HAORDER_SFTP_IP=HAORDER_SFTP+"ip";
	public static final String HAORDER_SFTP_USER=HAORDER_SFTP+"user";
	public static final String HAORDER_SFTP_PWD=HAORDER_SFTP+"pwd";
	public static final String HAORDER_SFTP_PORT=HAORDER_SFTP+"port";
	public static final String HAORDER_SFTP_PROPERTIES = "haorder_sftp_";
	public static final String HAORDER_SFTP_PROPERTIES_KEY = HAORDER_SFTP_PROPERTIES+"secret_key";
	public static final String HAORDER_SFTP_PROPERTIES_DESTINATIONPATH_ZGIS = HAORDER_SFTP_PROPERTIES+"destinationpath_zgis";
	public static final String HAORDER_SFTP_PROPERTIES_DESTINATIONPATH_ZOOR = HAORDER_SFTP_PROPERTIES+"destinationpath_zoor";
	
	@Resource
	PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	@Resource 
	EmailSenderService emailSenderService;
	@Resource
	HaOrderProductPriceInfoVoMapper priceInfoMapper;
	@Resource
	HaOrderUploadVoMapper haOrderUploadVoMapper;
	/**
	 * 处理HA订单，上传到文件服务器
	 * <AUTHOR> 2017-8-22 上午11:53:07
	 * @param applyBatchId  申请处理订单的批次号
	 * @return
	 */
	public Map<String,Object> handleHAOrdersToFileServer(String applyBatchId)throws Exception
	{
		logerr.info("handleHAOrdersToFileServer applyBatchId:"+applyBatchId);
		Map<String,Object> returnMap = new HashMap<String,Object>();
		//TODO 构造目标文件，上传到服务器(做成公共的)
		//查询需要上传的订单
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("batchId", applyBatchId);
		reqMap.put("warehouseType", WarehouseInfoVo.WAREHOUSE_WECHAT_TYPE);
		List<HaOrderUploadView> lstOrderView = haOrderUploadVoMapper.getNeedUploadHaOrders(reqMap);
		if(null==lstOrderView || lstOrderView.isEmpty())
		{
			throw new Exception("要上传的订单信息丢失或没有找到");
		}
		List<UploadHaOrderInfo> targetOrderLst = processHaOrders(lstOrderView);
		//生成文件
		Map<String,Object> uploadMap = generateUploadOrderFiles(targetOrderLst,ZOOR);
		List<String> lstFileNamePaths = (List<String>) uploadMap.get("lstFileNamePaths");
		//上传文件到目标服务器
		//获取ha订单sftp文件服务器的配置：
		String ip=(String) Constants.getSystemPropertyByCodeType(HAORDER_SFTP_IP);
		String user=(String) Constants.getSystemPropertyByCodeType(HAORDER_SFTP_USER);
		String passwd =(String) Constants.getSystemPropertyByCodeType(HAORDER_SFTP_PWD);
		String port=(String) Constants.getSystemPropertyByCodeType(HAORDER_SFTP_PORT);
		String dPath_ZOOR = MyPropertyConfigurer.getVal(HAORDER_SFTP_PROPERTIES_DESTINATIONPATH_ZOOR);
		/*String key = MyPropertyConfigurer.getVal(HAORDER_SFTP_PROPERTIES_KEY);
		byte[] enk = ThreeDesUtil.hex(key);
		byte[] reqPassword  = Base64.decode(passwd);
	    byte[] srcBytes = ThreeDesUtil.decryptMode(enk,reqPassword);  
	    boolean isSuccess = SftpUtil.sshSftp(ip, user, new String(srcBytes), Integer.parseInt(port), lstFileNamePaths, dPath_ZOOR);
	    */
		/*DesEncryptUtil du = new DesEncryptUtil();
		String reqPassword  = du.getDesString(passwd);*/
		boolean isSuccess = SftpUtil.sshSftp(ip, user, passwd, Integer.parseInt(port), lstFileNamePaths, dPath_ZOOR);
	    if(!isSuccess)
		{
			throw new Exception("handleHAOrdersToFileServer上传文件失败");
		}
		returnMap.put("code", "success");
		return returnMap;
		
	}
	
	
	//TODO 这部分待做成通用的
	public Map<String, Object> generateUploadOrderFiles(List<UploadHaOrderInfo> targetOrderLst,String filePartentPath)throws Exception
	{
		Map<String, Object> returnMap = new HashMap<String,Object>();
		List<String> lstFileNamePaths = new ArrayList<String>();
		String storePath = FileManager.fileUploadPath+filePartentPath;
		String tn= "\r\n";
		for(UploadHaOrderInfo targetUploadOrder:targetOrderLst)
		{
			String fileName = filePartentPath+"_"+targetUploadOrder.getOrderNo()+"_"+targetUploadOrder.getFileNameCreatetime()+".TXT";
			
			
			File filePath = new File(storePath);
			if (!filePath.exists()) {
				filePath.mkdirs();
			}
			File tmpFile = new File(filePath,fileName);
			lstFileNamePaths.add(tmpFile.getAbsolutePath());
			FileOutputStream fos=new FileOutputStream(tmpFile);
	 	    OutputStreamWriter osw=new OutputStreamWriter(fos, "UTF-8");
 	        BufferedWriter  bw=new BufferedWriter(osw);
            bw.write(targetUploadOrder.getRecordtype()+tn);
            bw.write(targetUploadOrder.getOrderNo()+tn);
            bw.write(targetUploadOrder.getLegsysid()+tn);
            bw.write(targetUploadOrder.getCreatetime()+tn);
            bw.write(targetUploadOrder.getExtbolnr()+tn);
            bw.write(targetUploadOrder.getWarehouseSoldto()+tn);
            bw.write(targetUploadOrder.getWarehouseShipto()+tn);
            bw.write(targetUploadOrder.getSndTime()+tn);
            bw.write(targetUploadOrder.getTradeTime()+tn);

            List<UploadHaOrderLineInfo> lstLineInfo = targetUploadOrder.getLstUploadHaOrderLineInfo();
            for(UploadHaOrderLineInfo tmpLine:lstLineInfo)
            {
            	bw.write(tmpLine.getItemRecordType()+tn);
            	bw.write(targetUploadOrder.getOrderNo()+tn);
            	bw.write(tmpLine.getItemRecordId()+tn);
            	String sku = tmpLine.getSku();
            	if(sku.endsWith("01"))
            	{
            		sku = sku.substring(0, sku.length()-2);
            	}else if (sku.endsWith("01A"))
            	{
            		sku = sku.substring(0, sku.length()-3);
            	}
            	bw.write(sku+tn);
            	bw.write(tmpLine.getSalesuom()+tn);
            	bw.write(tmpLine.getAmount()+tn);
            	bw.write(tmpLine.getWarehouseCode()+tn);
            }
            bw.close();
	        osw.close();
	        fos.close();
		}
		returnMap.put("lstFileNamePaths", lstFileNamePaths);
		return returnMap;
	}


    
	public List<UploadHaOrderInfo> processHaOrders(List<HaOrderUploadView> lstOrderView)
	{
		 List<UploadHaOrderInfo> lstUploadOrders = new ArrayList<UploadHaOrderInfo>();
		 List<UploadHaOrderLineInfo> lstUploadOrderLines = null;
		 
		 UploadHaOrderInfo currentUploadHaOrderInfo = null;
		 UploadHaOrderInfo lastUploadHaOrderInfo = null;
		 
		 String currentOrderNo = "";
		 String lastOrderNo = "";
		 
		 String lastSku = "";
		 String currentSku = "";
		 UploadHaOrderLineInfo uploadHaOrderLineInfo = null;
		 int allamount = 0;
		 
		 int i = 0;
		 boolean isSameOrder = false;
		 for(HaOrderUploadView uploadOrderView : lstOrderView)
		 {
			 String orderNo = uploadOrderView.getOrderNo();
			 currentOrderNo = orderNo;
		     String createtime = DateUtil.getDateStr(uploadOrderView.getCreateDate(), "MMddyyyy");
		     String filenamecreatetime = createtime;
		     String sndTime = DateUtil.getDateStr(uploadOrderView.getSndTime(), "MMddyyyy");
		     String tradeTime = DateUtil.getDateStr(uploadOrderView.getTradeTime(), "MMddyyyy");
			 String recordtype = uploadOrderView.getRecordtype();
			 String legsysid = uploadOrderView.getLegsysid();
			 String extbolnr = uploadOrderView.getExtbolnr();
			 String warehouseSoldto = uploadOrderView.getWarehouseSoldto();
			 String warehouseShipto = uploadOrderView.getWarehouseShipto();
			 if(!lastOrderNo.equals(currentOrderNo))
			 {
				 isSameOrder = true;
				 currentUploadHaOrderInfo = new UploadHaOrderInfo();
				 lstUploadOrderLines = new ArrayList<UploadHaOrderLineInfo>();
				 currentUploadHaOrderInfo.setCreatetime(createtime);
				 currentUploadHaOrderInfo.setSndTime(sndTime);
				 currentUploadHaOrderInfo.setTradeTime(tradeTime);
				 currentUploadHaOrderInfo.setOrderNo(orderNo);
				 currentUploadHaOrderInfo.setRecordtype(recordtype);
				 currentUploadHaOrderInfo.setLegsysid(legsysid);
				 currentUploadHaOrderInfo.setExtbolnr(extbolnr);
				 currentUploadHaOrderInfo.setWarehouseShipto(warehouseShipto);
				 currentUploadHaOrderInfo.setWarehouseSoldto(warehouseSoldto);
				 currentUploadHaOrderInfo.setFileNameCreatetime(filenamecreatetime);
			 }else
			 {
				 isSameOrder = false;
			 }
			 
			 currentSku = uploadOrderView.getSku();
			 if(!lastSku.equals(currentSku) || !lastOrderNo.equals(currentOrderNo))
			 {
				 allamount = 0;
				 uploadHaOrderLineInfo = new UploadHaOrderLineInfo();
				 uploadHaOrderLineInfo.setAmount(CommonUtil.addZeroForStr(""+uploadOrderView.getAmount(), 15, 1));
				 uploadHaOrderLineInfo.setItemRecordId(CommonUtil.addZeroForStr(""+uploadOrderView.getItemRecordId(), 6, 1));
				 uploadHaOrderLineInfo.setItemRecordType(uploadOrderView.getItemRecordType());
				 uploadHaOrderLineInfo.setSalesuom(uploadOrderView.getSalesuom());
				 uploadHaOrderLineInfo.setSku(CommonUtil.addZeroForStr(""+uploadOrderView.getSku(), 18, 1));
				 uploadHaOrderLineInfo.setWarehouseCode(uploadOrderView.getWarehouseCode());
				 lstUploadOrderLines.add(uploadHaOrderLineInfo);
				 allamount += uploadOrderView.getAmount();
				
			 }else
			 {
				 allamount += uploadOrderView.getAmount();
				 uploadHaOrderLineInfo.setAmount(CommonUtil.addZeroForStr(""+allamount, 15, 1));
			 }
			 lastSku = currentSku;
			 currentUploadHaOrderInfo.setLstUploadHaOrderLineInfo(lstUploadOrderLines);
			 if(isSameOrder && (i!=0))
			 {
				 lstUploadOrders.add(lastUploadHaOrderInfo);
			 }
			 lastUploadHaOrderInfo = new UploadHaOrderInfo();
			 lastUploadHaOrderInfo = currentUploadHaOrderInfo;
			 i++;
			 lastOrderNo = currentOrderNo;
		 }
		 lstUploadOrders.add(currentUploadHaOrderInfo);
		 return lstUploadOrders;
	}



	public Map<String, Object> getSndEmailInfo(Map<String,Object> baseDataMap)
	{
		//组装邮件数据map
		Map<String,Object> dataMap = new HashMap<String,Object>();
		try
		{
			//批次号
			Long batchId =(Long)baseDataMap.get(BATCH_ID);
			String chRoleName = (String) baseDataMap.get(CHROLE_NAME);
			String processKey = (String) baseDataMap.get(PD_KEY);//流程定义的key
			logerr.info("getSndEmailInfo chRoleName:"+chRoleName);
			//接收者
			String[] accepters = null;
			//邮件抄送者
			String[] ccaccepters = null;
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("funFlag", "order_confirm");
			List<String> orgNames = new ArrayList<String>();
			orgNames.add(OrderCommonVo.ORDER_SP[3]);
			orgNames.add(OrderCommonVo.ORDER_SP[4]);
			reqMap.put("organizationNames",orgNames);
			reqMap.put("userChRoleName",chRoleName);
			List<PartnerResponsibleVo> lstPartnerResponsibleVo =  partnerResponsibleVoMapper.queryEmailInfoForFWBOrderConfirm(reqMap);
			if(null==lstPartnerResponsibleVo || lstPartnerResponsibleVo.isEmpty())
			{
				logerr.info("getSndEmailInfo lstPartnerResponsibleVoInfo is null");
				return null;
			}
			logerr.info("getSndEmailInfo accepters-----:");
			logerr.info("getSndEmailInfo ccaccepters-----:");
			String acceptersAll = "";
			String ccacceptersAll = "";
			for(PartnerResponsibleVo partnerResponsibleVo:lstPartnerResponsibleVo)
			{
				String acceptUser = partnerResponsibleVo.getResponsiblePersonEmail();
				String ccUser = partnerResponsibleVo.getDayReportCc();
				
				if(null!=acceptUser && !acceptUser.trim().isEmpty() && !acceptersAll.contains(acceptUser.trim()))
				{
					acceptersAll+= partnerResponsibleVo.getResponsiblePersonEmail()+";";
				}
				if(null!=ccUser && !ccUser.trim().isEmpty() && !ccacceptersAll.contains(ccUser.trim()))
				{
					ccacceptersAll+= partnerResponsibleVo.getDayReportCc()+";";
				}
			}
			if(acceptersAll.isEmpty())
			{
				logerr.error("getSndEmailInfo=========acceptersAll----isEmpty");
				return null;
			}
			
			if(ccacceptersAll.isEmpty())
			{
				logerr.error("getSndEmailInfo=========ccacceptersAll----is null");
				ccacceptersAll = null;
			}
			acceptersAll = acceptersAll.substring(0, acceptersAll.length()-1);
			accepters = acceptersAll.split(";");
			if(null!=ccacceptersAll)
			{
				ccacceptersAll = ccacceptersAll.substring(0, ccacceptersAll.length()-1);
				ccaccepters = ccacceptersAll.split(";");
			}
			logerr.info("getSndEmailInfo accepters-----:"+accepters);
			logerr.info("getSndEmailInfo ccaccepters-----:"+ccaccepters);
			
			
			String acceptname = accepters[0].substring(0, accepters[0].lastIndexOf("@"));
			if(accepters.length>1)
			{
				acceptname = "ALL";
			}
			dataMap.put("acceptUserName", acceptname);
			//dataMap.put("msgcontent", "您有新的TM/JD的订单需要确认审批");
			//HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
			String basePath = (String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+"/";//request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
			String confirmLink = "";
			String refudLink =  "";
			if(chRoleName.equals(Constants.CHEVRON_PRICE_AUDIT))
			{
				logerr.info("getSndEmailInfo CHEVRON_PRICE_AUDIT");
				confirmLink=basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), HAORDER_CONFIRM_TYPE, "/workflow/flowHaOrderMyTask.jsp", "/workflow/flowHaOrderMyTask.jsp?batchId="+batchId, false,""+batchId);//直接拒绝
				refudLink=basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), HAORDER_REFUND_TYPE, "/workflow/flowHaOrderMyTask.jsp", "/workflow/flowHaOrderMyTask.jsp?batchId="+batchId, false,""+batchId);
			}else if(chRoleName.equals(Constants.CHEVRON_SAP_CSR))
			{
				logerr.info("getSndEmailInfo CHEVRON_SAP_CSR");
				confirmLink=basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), HAORDER_SAP_CSR_CONFIRM_TYPE, "", "", true,""+batchId,processKey);//直接拒绝
				refudLink=basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), HAORDER_SAP_CSR_REFUND_TYPE, "", "", true,""+batchId,processKey);
		
			}
					
			dataMap.put("confirmlink", confirmLink);
			dataMap.put("refudLink", refudLink);
			dataMap.put("accepters", accepters);
			dataMap.put("ccaccepters", ccaccepters);
		}catch (Exception e) {
			logerr.debug("getSndEmailInfo:"+e.getMessage());
			e.printStackTrace();
			return null;
		}
		return dataMap;
	}
	
	
	public List<HaOrderProductPriceInfoVo> getPriceListByBatchId(Long applyBatchId)
	{
		List<HaOrderProductPriceInfoVo> list = new ArrayList<HaOrderProductPriceInfoVo>();
		if(null==applyBatchId)
		{
			return list;
		}
		list = priceInfoMapper.getHaOrderProductPriceInfoVoByApplyBatchId(applyBatchId);
		return list;
	}
	
	public static String getCurrentUserRoles()
	{
		List<WxTRole>  roleList= ContextUtil.getCurUser().getRoleList();
		String currentChRoleNames = "";
		for(WxTRole role:roleList)
		{
			currentChRoleNames+=role.getChRoleName()+",";
		}
		return currentChRoleNames;
	}

	
}
