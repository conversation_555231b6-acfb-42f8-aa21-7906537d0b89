
			SELECT *
			FROM (
				SELECT a.APPLICATION_ORG_ID, SUM(de.MATERIAL_PRICE * de.APPLICATION_QTY) AS total_price, a.CREATION_TIME
					, a.APPLICATION_TYPE, '订单:' + a.APPLICATION_CODE AS comments, 0 AS status, a.id AS id
				FROM dbo.wx_t_material_application a
					LEFT JOIN dbo.wx_t_organization o ON o.id = a.APPLICATION_ORG_ID
					LEFT JOIN dbo.wx_t_material_application_detail de ON de.APPLICATION_ID = a.ID
				WHERE 1 = 1
					AND a.DELETE_FLAG = 0
					AND a.APPLICATION_TYPE = 'promotion'
					AND a.APPLICATION_STATUS NOT IN ('REJECTED', 'DRAFT', 'PENDING_4_URGENT')
				GROUP BY a.APPLICATION_CODE, a.APPLICATION_TYPE, a.APPLICATION_STATUS, a.CREATION_TIME, a.LAST_UPDATE_TIME, a.APPLICATION_ORG_ID, a.APPLICATION_PERSON_ID, a.id
				UNION ALL
				SELECT pac.POINT_ACCOUNT_OWNER_ID, pde.POINT_VALUE, pde.CREATION_TIME
					, CASE 
						WHEN pde.POINT_TYPE = 'CALTEX_POINT' THEN 'caltex'
						WHEN pde.POINT_TYPE = 'PROMOTION_POINT' THEN 'promotion'
						WHEN pde.POINT_TYPE = 'CDM_STOCK_POINT' THEN 'cdm_stock'
						WHEN pde.POINT_TYPE = 'CDM_MATERIAL_POINT' THEN 'cdm_material'
						WHEN pde.POINT_TYPE = 'CDM_PROMOTION_POINT' THEN 'cdm_promotion'
						WHEN pde.POINT_TYPE = 'CDM_RED_BAG_POINT' THEN 'cdm_red_bag'
						WHEN pde.POINT_TYPE = 'OEM_STOCK_POINT' THEN 'oem_stock'
					END AS application_type, plog.COMMENTS, 1 AS status, NULL AS id
				FROM wx_t_point_value_detail pde
					LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
					LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
				WHERE 1 = 1
					AND pde.POINT_TYPE = 'PROMOTION_POINT'
					AND pde.POINT_TYPE = 'PROMOTION_POINT'
					AND pde.POINT_VALUE != 0
			) a order by CREATION_TIME;