(self["webpackChunkvue_chevron_desktop"]=self["webpackChunkvue_chevron_desktop"]||[]).push([[878],{75324:function(t,e,a){var r=a(70492),i=a(33051);function s(t,e){i.each(e,(function(e){e.update="updateView",r.registerAction(e,(function(a,r){var i={};return r.eachComponent({mainType:"series",subType:t,query:a},(function(t){t[e.method]&&t[e.method](a.name,a.dataIndex);var r=t.getData();r.each((function(e){var a=r.getName(e);i[a]=t.isSelected(a)||!1}))})),{name:a.name,selected:i,seriesId:a.seriesId}}))}))}t.exports=s},40451:function(t,e,a){var r=a(70492),i=a(33051),s=a(79093),o=s.layout,n=s.largeLayout;a(77532),a(90791),a(33207),a(84617),r.registerLayout(r.PRIORITY.VISUAL.LAYOUT,i.curry(o,"bar")),r.registerLayout(r.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,n),r.registerVisual({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}})},90791:function(t,e,a){var r=a(489),i=r.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return!!this.get("large")&&this.get("progressive")},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});t.exports=i},33207:function(t,e,a){var r=a(38175),i=(r.__DEV__,a(70492)),s=a(33051),o=a(51177),n=a(7905),l=n.setLabel,d=a(4272),c=a(98378),u=a(10712),h=a(8524),p=a(270),g=p.throttle,m=a(22963),f=m.createClipPath,y=a(39529),v=["itemStyle","barBorderWidth"],b=[0,0];function _(t,e){var a=t.getArea&&t.getArea();if("cartesian2d"===t.type){var r=t.getBaseAxis();if("category"!==r.type||!r.onBand){var i=e.getLayout("bandWidth");r.isHorizontal()?(a.x-=i,a.width+=2*i):(a.y-=i,a.height+=2*i)}}return a}s.extend(d.prototype,c);var x=i.extendChartView({type:"bar",render:function(t,e,a){this._updateDrawMode(t);var r=t.get("coordinateSystem");return"cartesian2d"!==r&&"polar"!==r||(this._isLargeDraw?this._renderLarge(t,e,a):this._renderNormal(t,e,a)),this.group},incrementalPrepareRender:function(t,e,a){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e,a,r){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t,e,a){var r,i=this.group,s=t.getData(),n=this._data,l=t.coordinateSystem,d=l.getBaseAxis();"cartesian2d"===l.type?r=d.isHorizontal():"polar"===l.type&&(r="angle"===d.dim);var c=t.isAnimationEnabled()?t:null,u=t.get("clip",!0),p=_(l,s);i.removeClipPath();var g=t.get("roundCap",!0),m=t.get("showBackground",!0),f=t.getModel("backgroundStyle"),y=f.get("barBorderRadius")||0,v=[],b=this._backgroundEls||[];s.diff(n).add((function(e){var a=s.getItemModel(e),o=D[l.type](s,e,a);if(m){var n=D[l.type](s,e),d=V(l,r,n);d.useStyle(f.getBarItemStyle()),"cartesian2d"===l.type&&d.setShape("r",y),v[e]=d}if(s.hasValue(e)){if(u){var h=I[l.type](p,o);if(h)return void i.remove(b)}var b=M[l.type](e,o,r,c,!1,g);s.setItemGraphicEl(e,b),i.add(b),L(b,s,e,a,o,t,r,"polar"===l.type)}})).update((function(e,a){var d=s.getItemModel(e),h=D[l.type](s,e,d);if(m){var _=b[a];_.useStyle(f.getBarItemStyle()),"cartesian2d"===l.type&&_.setShape("r",y),v[e]=_;var x=D[l.type](s,e),w=Z(r,x,l);o.updateProps(_,{shape:w},c,e)}var S=n.getItemGraphicEl(a);if(s.hasValue(e)){if(u){var A=I[l.type](p,h);if(A)return void i.remove(S)}S?o.updateProps(S,{shape:h},c,e):S=M[l.type](e,h,r,c,!0,g),s.setItemGraphicEl(e,S),i.add(S),L(S,s,e,d,h,t,r,"polar"===l.type)}else i.remove(S)})).remove((function(t){var e=n.getItemGraphicEl(t);"cartesian2d"===l.type?e&&A(t,c,e):e&&P(t,c,e)})).execute();var x=this._backgroundGroup||(this._backgroundGroup=new h);x.removeAll();for(var w=0;w<v.length;++w)x.add(v[w]);i.add(x),this._backgroundEls=v,this._data=s},_renderLarge:function(t,e,a){this._clear(),T(t,this.group);var r=t.get("clip",!0)?f(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),T(e,this.group,!0)},dispose:s.noop,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,a=this._data;t&&t.get("animation")&&a&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],a.eachItemGraphicEl((function(e){"sector"===e.type?P(e.dataIndex,t,e):A(e.dataIndex,t,e)}))):e.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}}),w=Math.max,S=Math.min,I={cartesian2d:function(t,e){var a=e.width<0?-1:1,r=e.height<0?-1:1;a<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height);var i=w(e.x,t.x),s=S(e.x+e.width,t.x+t.width),o=w(e.y,t.y),n=S(e.y+e.height,t.y+t.height);e.x=i,e.y=o,e.width=s-i,e.height=n-o;var l=e.width<0||e.height<0;return a<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height),l},polar:function(t){return!1}},M={cartesian2d:function(t,e,a,r,i){var n=new o.Rect({shape:s.extend({},e),z2:1});if(n.name="item",r){var l=n.shape,d=a?"height":"width",c={};l[d]=0,c[d]=e[d],o[i?"updateProps":"initProps"](n,{shape:c},r,t)}return n},polar:function(t,e,a,r,i,n){var l=e.startAngle<e.endAngle,d=!a&&n?y:o.Sector,c=new d({shape:s.defaults({clockwise:l},e),z2:1});if(c.name="item",r){var u=c.shape,h=a?"r":"endAngle",p={};u[h]=a?0:e.startAngle,p[h]=e[h],o[i?"updateProps":"initProps"](c,{shape:p},r,t)}return c}};function A(t,e,a){a.style.text=null,o.updateProps(a,{shape:{width:0}},e,t,(function(){a.parent&&a.parent.remove(a)}))}function P(t,e,a){a.style.text=null,o.updateProps(a,{shape:{r:a.shape.r0}},e,t,(function(){a.parent&&a.parent.remove(a)}))}var D={cartesian2d:function(t,e,a){var r=t.getItemLayout(e),i=a?$(a,r):0,s=r.width>0?1:-1,o=r.height>0?1:-1;return{x:r.x+s*i/2,y:r.y+o*i/2,width:r.width-s*i,height:r.height-o*i}},polar:function(t,e,a){var r=t.getItemLayout(e);return{cx:r.cx,cy:r.cy,r0:r.r0,r:r.r,startAngle:r.startAngle,endAngle:r.endAngle}}};function k(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function L(t,e,a,r,i,n,d,c){var u=e.getItemVisual(a,"color"),h=e.getItemVisual(a,"opacity"),p=e.getVisual("borderColor"),g=r.getModel("itemStyle"),m=r.getModel("emphasis.itemStyle").getBarItemStyle();c||t.setShape("r",g.get("barBorderRadius")||0),t.useStyle(s.defaults({stroke:k(i)?"none":p,fill:k(i)?"none":u,opacity:h},g.getBarItemStyle()));var f=r.getShallow("cursor");f&&t.attr("cursor",f);var y=d?i.height>0?"bottom":"top":i.width>0?"left":"right";c||l(t.style,m,r,u,n,a,y),k(i)&&(m.fill=m.stroke="none"),o.setHoverStyle(t,m)}function $(t,e){var a=t.get(v)||0,r=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),i=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(a,r,i)}var R=u.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var a=e.points,r=this.__startPoint,i=this.__baseDimIdx,s=0;s<a.length;s+=2)r[i]=a[s+i],t.moveTo(r[0],r[1]),t.lineTo(a[s],a[s+1])}});function T(t,e,a){var r=t.getData(),i=[],s=r.getLayout("valueAxisHorizontal")?1:0;i[1-s]=r.getLayout("valueAxisStart");var o=r.getLayout("largeDataIndices"),n=r.getLayout("barWidth"),l=t.getModel("backgroundStyle"),d=t.get("showBackground",!0);if(d){var c=r.getLayout("largeBackgroundPoints"),u=[];u[1-s]=r.getLayout("backgroundStart");var h=new R({shape:{points:c},incremental:!!a,__startPoint:u,__baseDimIdx:s,__largeDataIndices:o,__barWidth:n,silent:!0,z2:0});O(h,l,r),e.add(h)}var p=new R({shape:{points:r.getLayout("largePoints")},incremental:!!a,__startPoint:i,__baseDimIdx:s,__largeDataIndices:o,__barWidth:n});e.add(p),E(p,t,r),p.seriesIndex=t.seriesIndex,t.get("silent")||(p.on("mousedown",C),p.on("mousemove",C))}var C=g((function(t){var e=this,a=N(e,t.offsetX,t.offsetY);e.dataIndex=a>=0?a:null}),30,!1);function N(t,e,a){var r=t.__baseDimIdx,i=1-r,s=t.shape.points,o=t.__largeDataIndices,n=Math.abs(t.__barWidth/2),l=t.__startPoint[i];b[0]=e,b[1]=a;for(var d=b[r],c=b[1-r],u=d-n,h=d+n,p=0,g=s.length/2;p<g;p++){var m=2*p,f=s[m+r],y=s[m+i];if(f>=u&&f<=h&&(l<=y?c>=l&&c<=y:c>=y&&c<=l))return o[p]}return-1}function E(t,e,a){var r=a.getVisual("borderColor")||a.getVisual("color"),i=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(i),t.style.fill=null,t.style.stroke=r,t.style.lineWidth=a.getLayout("barWidth")}function O(t,e,a){var r=e.get("borderColor")||e.get("color"),i=e.getItemStyle(["color","borderColor"]);t.useStyle(i),t.style.fill=null,t.style.stroke=r,t.style.lineWidth=a.getLayout("barWidth")}function Z(t,e,a){var r,i="polar"===a.type;return r=i?a.getArea():a.grid.getRect(),i?{cx:r.cx,cy:r.cy,r0:t?r.r0:e.r0,r:t?r.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:r.x,y:t?r.y:e.y,width:t?e.width:r.width,height:t?r.height:e.height}}function V(t,e,a){var r="polar"===t.type?o.Sector:o.Rect;return new r({shape:Z(e,a,t),silent:!0,z2:0})}t.exports=x},489:function(t,e,a){var r=a(93321),i=a(40488),s=r.extend({type:"series.__base_bar__",getInitialData:function(t,e){return i(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var a=e.dataToPoint(e.clampData(t)),r=this.getData(),i=r.getLayout("offset"),s=r.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return a[o]+=i+s/2,a}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});t.exports=s},98378:function(t,e,a){var r=a(59066),i=r([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),s={getBarItemStyle:function(t){var e=i(this,t);if(this.getBorderLineDash){var a=this.getBorderLineDash();a&&(e.lineDash=a)}return e}};t.exports=s},7905:function(t,e,a){var r=a(51177),i=a(33140),s=i.getDefaultLabel;function o(t,e,a,i,o,l,d){var c=a.getModel("label"),u=a.getModel("emphasis.label");r.setLabelStyle(t,e,c,u,{labelFetcher:o,labelDataIndex:l,defaultText:s(o.getData(),l),isRectText:!0,autoColor:i}),n(t),n(e)}function n(t,e){"outside"===t.textPosition&&(t.textPosition=e)}e.setLabel=o},23263:function(t,e,a){var r=a(55623),i=a(87587),s=a(33051),o=s.extend,n=s.isArray;function l(t,e,a){e=n(e)&&{coordDimensions:e}||o({},e);var s=t.getSource(),l=r(s,e),d=new i(l,t);return d.initData(s,a),d}t.exports=l},94859:function(t,e,a){var r=a(70492),i=a(33051);a(52702),a(42471);var s=a(75324),o=a(69274),n=a(63798),l=a(22528);s("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),r.registerVisual(o("pie")),r.registerLayout(i.curry(n,"pie")),r.registerProcessor(l("pie"))},52702:function(t,e,a){var r=a(70492),i=a(23263),s=a(33051),o=a(32234),n=a(85669),l=n.getPercentWithPrecision,d=a(1501),c=a(68540),u=c.retrieveRawAttr,h=a(61772),p=h.makeSeriesEncodeForNameBased,g=a(72019),m=r.extendSeriesModel({type:"series.pie",init:function(t){m.superApply(this,"init",arguments),this.legendVisualProvider=new g(s.bind(this.getData,this),s.bind(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){m.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(t,e){return i(this,{coordDimensions:["value"],encodeDefaulter:s.curry(p,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),a=[],r=0,i=t.count();r<i;r++)a.push({name:t.getName(r),value:t.get(e,r),selected:u(t,r,"selected")});return a},getDataParams:function(t){var e=this.getData(),a=m.superCall(this,"getDataParams",t),r=[];return e.each(e.mapDimension("value"),(function(t){r.push(t)})),a.percent=l(r,t,e.hostModel.get("percentPrecision")),a.$vars.push("percent"),a},_defaultLabelLine:function(t){o.defaultEmphasis(t,"labelLine",["show"]);var e=t.labelLine,a=t.emphasis.labelLine;e.show=e.show&&t.label.show,a.show=a.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});s.mixin(m,d);var f=m;t.exports=f},42471:function(t,e,a){var r=a(33051),i=a(51177),s=a(75797);function o(t,e,a,r){var i=e.getData(),s=this.dataIndex,o=i.getName(s),l=e.get("selectedOffset");r.dispatchAction({type:"pieToggleSelect",from:t,name:o,seriesId:e.id}),i.each((function(t){n(i.getItemGraphicEl(t),i.getItemLayout(t),e.isSelected(i.getName(t)),l,a)}))}function n(t,e,a,r,i){var s=(e.startAngle+e.endAngle)/2,o=Math.cos(s),n=Math.sin(s),l=a?r:0,d=[o*l,n*l];i?t.animate().when(200,{position:d}).start("bounceOut"):t.attr("position",d)}function l(t,e){i.Group.call(this);var a=new i.Sector({z2:2}),r=new i.Polyline,s=new i.Text;this.add(a),this.add(r),this.add(s),this.updateData(t,e,!0)}var d=l.prototype;d.updateData=function(t,e,a){var s=this.childAt(0),o=this.childAt(1),l=this.childAt(2),d=t.hostModel,c=t.getItemModel(e),u=t.getItemLayout(e),h=r.extend({},u);h.label=null;var p=d.getShallow("animationTypeUpdate");if(a){s.setShape(h);var g=d.getShallow("animationType");"scale"===g?(s.shape.r=u.r0,i.initProps(s,{shape:{r:u.r}},d,e)):(s.shape.endAngle=u.startAngle,i.updateProps(s,{shape:{endAngle:u.endAngle}},d,e))}else"expansion"===p?s.setShape(h):i.updateProps(s,{shape:h},d,e);var m=t.getItemVisual(e,"color");s.useStyle(r.defaults({lineJoin:"bevel",fill:m},c.getModel("itemStyle").getItemStyle())),s.hoverStyle=c.getModel("emphasis.itemStyle").getItemStyle();var f=c.getShallow("cursor");f&&s.attr("cursor",f),n(this,t.getItemLayout(e),d.isSelected(t.getName(e)),d.get("selectedOffset"),d.get("animation"));var y=!a&&"transition"===p;this._updateLabel(t,e,y),this.highDownOnUpdate=d.get("silent")?null:function(t,e){var a=d.isAnimationEnabled()&&c.get("hoverAnimation");"emphasis"===e?(o.ignore=o.hoverIgnore,l.ignore=l.hoverIgnore,a&&(s.stopAnimation(!0),s.animateTo({shape:{r:u.r+d.get("hoverOffset")}},300,"elasticOut"))):(o.ignore=o.normalIgnore,l.ignore=l.normalIgnore,a&&(s.stopAnimation(!0),s.animateTo({shape:{r:u.r}},300,"elasticOut")))},i.setHoverStyle(this)},d._updateLabel=function(t,e,a){var r=this.childAt(1),s=this.childAt(2),o=t.hostModel,n=t.getItemModel(e),l=t.getItemLayout(e),d=l.label,c=t.getItemVisual(e,"color");if(!d||isNaN(d.x)||isNaN(d.y))s.ignore=s.normalIgnore=s.hoverIgnore=r.ignore=r.normalIgnore=r.hoverIgnore=!0;else{var u={points:d.linePoints||[[d.x,d.y],[d.x,d.y],[d.x,d.y]]},h={x:d.x,y:d.y};a?(i.updateProps(r,{shape:u},o,e),i.updateProps(s,{style:h},o,e)):(r.attr({shape:u}),s.attr({style:h})),s.attr({rotation:d.rotation,origin:[d.x,d.y],z2:10});var p=n.getModel("label"),g=n.getModel("emphasis.label"),m=n.getModel("labelLine"),f=n.getModel("emphasis.labelLine");c=t.getItemVisual(e,"color");i.setLabelStyle(s.style,s.hoverStyle={},p,g,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:d.text,autoColor:c,useInsideStyle:!!d.inside},{textAlign:d.textAlign,textVerticalAlign:d.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),s.ignore=s.normalIgnore=!p.get("show"),s.hoverIgnore=!g.get("show"),r.ignore=r.normalIgnore=!m.get("show"),r.hoverIgnore=!f.get("show"),r.setStyle({stroke:c,opacity:t.getItemVisual(e,"opacity")}),r.setStyle(m.getModel("lineStyle").getLineStyle()),r.hoverStyle=f.getModel("lineStyle").getLineStyle();var y=m.get("smooth");y&&!0===y&&(y=.4),r.setShape({smooth:y})}},r.inherits(l,i.Group);var c=s.extend({type:"pie",init:function(){var t=new i.Group;this._sectorGroup=t},render:function(t,e,a,i){if(!i||i.from!==this.uid){var s=t.getData(),n=this._data,d=this.group,c=e.get("animation"),u=!n,h=t.get("animationType"),p=t.get("animationTypeUpdate"),g=r.curry(o,this.uid,t,c,a),m=t.get("selectedMode");if(s.diff(n).add((function(t){var e=new l(s,t);u&&"scale"!==h&&e.eachChild((function(t){t.stopAnimation(!0)})),m&&e.on("click",g),s.setItemGraphicEl(t,e),d.add(e)})).update((function(t,e){var a=n.getItemGraphicEl(e);u||"transition"===p||a.eachChild((function(t){t.stopAnimation(!0)})),a.updateData(s,t),a.off("click"),m&&a.on("click",g),d.add(a),s.setItemGraphicEl(t,a)})).remove((function(t){var e=n.getItemGraphicEl(t);d.remove(e)})).execute(),c&&s.count()>0&&(u?"scale"!==h:"transition"!==p)){for(var f=s.getItemLayout(0),y=1;isNaN(f.startAngle)&&y<s.count();++y)f=s.getItemLayout(y);var v=Math.max(a.getWidth(),a.getHeight())/2,b=r.bind(d.removeClipPath,d);d.setClipPath(this._createClipPath(f.cx,f.cy,v,f.startAngle,f.clockwise,b,t,u))}else d.removeClipPath();this._data=s}},dispose:function(){},_createClipPath:function(t,e,a,r,s,o,n,l){var d=new i.Sector({shape:{cx:t,cy:e,r0:0,r:a,startAngle:r,endAngle:r,clockwise:s}}),c=l?i.initProps:i.updateProps;return c(d,{shape:{endAngle:r+(s?1:-1)*Math.PI*2}},n,o),d},containPoint:function(t,e){var a=e.getData(),r=a.getItemLayout(0);if(r){var i=t[0]-r.cx,s=t[1]-r.cy,o=Math.sqrt(i*i+s*s);return o<=r.r&&o>=r.r0}}}),u=c;t.exports=u},17659:function(t,e,a){var r=a(80423),i=a(85669),s=i.parsePercent,o=Math.PI/180;function n(t,e,a,r,i,s,o,n,l,d){function c(e,a,r,i){for(var s=e;s<a;s++){if(t[s].y+r>l+o)break;if(t[s].y+=r,s>e&&s+1<a&&t[s+1].y>t[s].y+t[s].height)return void u(s,r/2)}u(a-1,r/2)}function u(e,a){for(var r=e;r>=0;r--){if(t[r].y-a<l)break;if(t[r].y-=a,r>0&&t[r].y>t[r-1].y+t[r-1].height)break}}function h(t,e,a,r,i,s){for(var o=e?Number.MAX_VALUE:0,n=0,l=t.length;n<l;n++)if("none"===t[n].labelAlignTo){var d=Math.abs(t[n].y-r),c=t[n].len,u=t[n].len2,h=d<i+c?Math.sqrt((i+c+u)*(i+c+u)-d*d):Math.abs(t[n].x-a);e&&h>=o&&(h=o-10),!e&&h<=o&&(h=o+10),t[n].x=a+h*s,o=h}}t.sort((function(t,e){return t.y-e.y}));for(var p,g=0,m=t.length,f=[],y=[],v=0;v<m;v++){if("outer"===t[v].position&&"labelLine"===t[v].labelAlignTo){var b=t[v].x-d;t[v].linePoints[1][0]+=b,t[v].x=d}p=t[v].y-g,p<0&&c(v,m,-p,i),g=t[v].y+t[v].height}o-g<0&&u(m-1,g-o);for(v=0;v<m;v++)t[v].y>=a?y.push(t[v]):f.push(t[v]);h(f,!1,e,a,r,i),h(y,!0,e,a,r,i)}function l(t,e,a,i,s,o,l,c){for(var u=[],h=[],p=Number.MAX_VALUE,g=-Number.MAX_VALUE,m=0;m<t.length;m++)d(t[m])||(t[m].x<e?(p=Math.min(p,t[m].x),u.push(t[m])):(g=Math.max(g,t[m].x),h.push(t[m])));n(h,e,a,i,1,s,o,l,c,g),n(u,e,a,i,-1,s,o,l,c,p);for(m=0;m<t.length;m++){var f=t[m];if(!d(f)){var y=f.linePoints;if(y){var v,b="edge"===f.labelAlignTo,_=f.textRect.width;v=b?f.x<e?y[2][0]-f.labelDistance-l-f.labelMargin:l+s-f.labelMargin-y[2][0]-f.labelDistance:f.x<e?f.x-l-f.bleedMargin:l+s-f.x-f.bleedMargin,v<f.textRect.width&&(f.text=r.truncateText(f.text,v,f.font),"edge"===f.labelAlignTo&&(_=r.getWidth(f.text,f.font)));var x=y[1][0]-y[2][0];b?f.x<e?y[2][0]=l+f.labelMargin+_+f.labelDistance:y[2][0]=l+s-f.labelMargin-_-f.labelDistance:(f.x<e?y[2][0]=f.x+f.labelDistance:y[2][0]=f.x-f.labelDistance,y[1][0]=y[2][0]+x),y[1][1]=y[2][1]=f.y}}}}function d(t){return"center"===t.position}function c(t,e,a,i,n,d){var c,u,h=t.getData(),p=[],g=!1,m=(t.get("minShowLabelAngle")||0)*o;h.each((function(i){var o=h.getItemLayout(i),l=h.getItemModel(i),d=l.getModel("label"),f=d.get("position")||l.get("emphasis.label.position"),y=d.get("distanceToLabelLine"),v=d.get("alignTo"),b=s(d.get("margin"),a),_=d.get("bleedMargin"),x=d.getFont(),w=l.getModel("labelLine"),S=w.get("length");S=s(S,a);var I=w.get("length2");if(I=s(I,a),!(o.angle<m)){var M,A,P,D,k=(o.startAngle+o.endAngle)/2,L=Math.cos(k),$=Math.sin(k);c=o.cx,u=o.cy;var R,T=t.getFormattedLabel(i,"normal")||h.getName(i),C=r.getBoundingRect(T,x,D,"top"),N="inside"===f||"inner"===f;if("center"===f)M=o.cx,A=o.cy,D="center";else{var E=(N?(o.r+o.r0)/2*L:o.r*L)+c,O=(N?(o.r+o.r0)/2*$:o.r*$)+u;if(M=E+3*L,A=O+3*$,!N){var Z=E+L*(S+e-o.r),V=O+$*(S+e-o.r),B=Z+(L<0?-1:1)*I,z=V;M="edge"===v?L<0?n+b:n+a-b:B+(L<0?-y:y),A=z,P=[[E,O],[Z,V],[B,z]]}D=N?"center":"edge"===v?L>0?"right":"left":L>0?"left":"right"}var W=d.get("rotate");R="number"===typeof W?W*(Math.PI/180):W?L<0?-k+Math.PI:-k:0,g=!!R,o.label={x:M,y:A,position:f,height:C.height,len:S,len2:I,linePoints:P,textAlign:D,verticalAlign:"middle",rotation:R,inside:N,labelDistance:y,labelAlignTo:v,labelMargin:b,bleedMargin:_,textRect:C,text:T,font:x},N||p.push(o.label)}})),!g&&t.get("avoidLabelOverlap")&&l(p,c,u,e,a,i,n,d)}t.exports=c},63798:function(t,e,a){var r=a(85669),i=r.parsePercent,s=r.linearMap,o=a(76172),n=a(17659),l=a(33051),d=2*Math.PI,c=Math.PI/180;function u(t,e){return o.getLayoutRect(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function h(t,e,a,r){e.eachSeriesByType(t,(function(t){var e=t.getData(),r=e.mapDimension("value"),o=u(t,a),h=t.get("center"),p=t.get("radius");l.isArray(p)||(p=[0,p]),l.isArray(h)||(h=[h,h]);var g=i(o.width,a.getWidth()),m=i(o.height,a.getHeight()),f=Math.min(g,m),y=i(h[0],g)+o.x,v=i(h[1],m)+o.y,b=i(p[0],f/2),_=i(p[1],f/2),x=-t.get("startAngle")*c,w=t.get("minAngle")*c,S=0;e.each(r,(function(t){!isNaN(t)&&S++}));var I=e.getSum(r),M=Math.PI/(I||S)*2,A=t.get("clockwise"),P=t.get("roseType"),D=t.get("stillShowZeroSum"),k=e.getDataExtent(r);k[0]=0;var L=d,$=0,R=x,T=A?1:-1;if(e.each(r,(function(t,a){var r;if(isNaN(t))e.setItemLayout(a,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:A,cx:y,cy:v,r0:b,r:P?NaN:_,viewRect:o});else{r="area"!==P?0===I&&D?M:t*M:d/S,r<w?(r=w,L-=w):$+=t;var i=R+T*r;e.setItemLayout(a,{angle:r,startAngle:R,endAngle:i,clockwise:A,cx:y,cy:v,r0:b,r:P?s(t,k,[b,_]):_,viewRect:o}),R=i}})),L<d&&S)if(L<=.001){var C=d/S;e.each(r,(function(t,a){if(!isNaN(t)){var r=e.getItemLayout(a);r.angle=C,r.startAngle=x+T*a*C,r.endAngle=x+T*(a+1)*C}}))}else M=L/$,R=x,e.each(r,(function(t,a){if(!isNaN(t)){var r=e.getItemLayout(a),i=r.angle===w?w:t*M;r.startAngle=R,r.endAngle=R+T*i,R+=T*i}}));n(t,_,o.width,o.height,o.x,o.y)}))}t.exports=h},1501:function(t,e,a){var r=a(33051),i={updateSelectedMap:function(t){this._targetList=r.isArray(t)?t.slice():[],this._selectTargetMap=r.reduce(t||[],(function(t,e){return t.set(e.name,e),t}),r.createHashMap())},select:function(t,e){var a=null!=e?this._targetList[e]:this._selectTargetMap.get(t),r=this.get("selectedMode");"single"===r&&this._selectTargetMap.each((function(t){t.selected=!1})),a&&(a.selected=!0)},unSelect:function(t,e){var a=null!=e?this._targetList[e]:this._selectTargetMap.get(t);a&&(a.selected=!1)},toggleSelected:function(t,e){var a=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=a)return this[a.selected?"unSelect":"select"](t,e),a.selected},isSelected:function(t,e){var a=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return a&&a.selected}};t.exports=i},22528:function(t){function e(t){return{seriesType:t,reset:function(t,e){var a=e.findComponents({mainType:"legend"});if(a&&a.length){var r=t.getData();r.filterSelf((function(t){for(var e=r.getName(t),i=0;i<a.length;i++)if(!a[i].isSelected(e))return!1;return!0}))}}}}t.exports=e},39529:function(t,e,a){var r=a(51177),i=r.extendShape,s=i({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var a=e.cx,r=e.cy,i=Math.max(e.r0||0,0),s=Math.max(e.r,0),o=.5*(s-i),n=i+o,l=e.startAngle,d=e.endAngle,c=e.clockwise,u=Math.cos(l),h=Math.sin(l),p=Math.cos(d),g=Math.sin(d),m=c?d-l<2*Math.PI:l-d<2*Math.PI;m&&(t.moveTo(u*i+a,h*i+r),t.arc(u*n+a,h*n+r,o,-Math.PI+l,l,!c)),t.arc(a,r,s,l,d,!c),t.moveTo(p*s+a,g*s+r),t.arc(p*n+a,g*n+r,o,d-2*Math.PI,d-Math.PI,!c),0!==i&&(t.arc(a,r,i,d,l,c),t.moveTo(u*i+a,g*i+r)),t.closePath()}});t.exports=s},72019:function(t){function e(t,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){var a=e();return a.indexOfName(t)>=0},this.indexOfName=function(e){var a=t();return a.indexOfName(e)},this.getItemVisual=function(e,a){var r=t();return r.getItemVisual(e,a)}}var a=e;t.exports=a},69274:function(t,e,a){var r=a(33051),i=r.createHashMap;function s(t){return{getTargetSeries:function(e){var a={},r=i();return e.eachSeriesByType(t,(function(t){t.__paletteScope=a,r.set(t.uid,t)})),r},reset:function(t,e){var a=t.getRawData(),r={},i=t.getData();i.each((function(t){var e=i.getRawIndex(t);r[e]=t})),a.each((function(e){var s,o=r[e],n=null!=o&&i.getItemVisual(o,"color",!0),l=null!=o&&i.getItemVisual(o,"borderColor",!0);if(n&&l||(s=a.getItemModel(e)),!n){var d=s.get("itemStyle.color")||t.getColorFromPalette(a.getName(e)||e+"",t.__paletteScope,a.count());null!=o&&i.setItemVisual(o,"color",d)}if(!l){var c=s.get("itemStyle.borderColor");null!=o&&i.setItemVisual(o,"borderColor",c)}}))}}}t.exports=s},83878:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return ze}});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("search",{attrs:{params:t.params}}),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{xs:24,sm:12,md:12,lg:12,xl:12}},[a("customerTotal",{staticStyle:{height:"500px"},attrs:{"global-params":t.params}})],1),a("el-col",{attrs:{xs:24,sm:12,md:12,lg:12,xl:12}},[a("customerActivity",{staticStyle:{height:"500px"},attrs:{"global-params":t.params}})],1),a("el-col",{attrs:{xs:24,sm:12,md:12,lg:12,xl:12}},[a("monthlySale",{staticStyle:{height:"500px"},attrs:{"global-params":t.params}})],1),a("el-col",{attrs:{xs:24,sm:12,md:12,lg:12,xl:12}},[a("dsrPerformance",{staticStyle:{height:"500px"},attrs:{"global-params":t.params}})],1),a("el-col",{attrs:{xs:24,sm:12,md:12,lg:12,xl:12}},[a("customerVisit",{staticStyle:{height:"500px"},attrs:{"global-params":t.params}})],1),a("el-col",{attrs:{xs:24,sm:12,md:12,lg:12,xl:12}},[a("saleTarget",{staticStyle:{height:"500px"},attrs:{"global-params":t.params}})],1),2021==t.$route.query.year?a("el-col",{attrs:{span:24}},[a("dealerTable",{attrs:{"global-params":t.params}})],1):t._e()],1)],1)},i=[],s=a(92510),o=a(92137),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"当前月份 ："}},[a("el-date-picker",{staticStyle:{width:"140px"},attrs:{type:"month",clearable:!1,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",minTime:"2020-01",pickerOptions:t.pickerOptions},model:{value:t.params.date,callback:function(e){t.$set(t.params,"date",e)},expression:"params.date"}})],1),t.hasPermissionNotAdmin(16)?t._e():a("el-form-item",{attrs:{label:"区域 ："}},[a("el-select-region-by-resourceId",{attrs:{params:t.regionParams},model:{value:t.params.region,callback:function(e){t.$set(t.params,"region",e)},expression:"params.region"}})],1),t.hasPermissionNotAdmin(16)?t._e():a("el-form-item",{attrs:{label:"经销商 ："}},[a("el-select-dealer-by-resourceId",{staticStyle:{width:"200px"},attrs:{params:t.dealerParams,remote:"",filterable:""},on:{change:t.dealerChange},model:{value:t.params.distributorId,callback:function(e){t.$set(t.params,"distributorId",e)},expression:"params.distributorId"}})],1),a("el-form-item",{attrs:{label:"DSR 名称 ："}},[a("el-select-dsr-by-resourceId",{attrs:{params:t.userParams},model:{value:t.params.dsrId,callback:function(e){t.$set(t.params,"dsrId",e)},expression:"params.dsrId"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.search}},[t._v("查询")]),a("el-button",{attrs:{type:"primary"},on:{click:t.download}},[t._v("导出")])],1),a("el-form-item",[t.grantButton.show?a("el-button",{attrs:{type:"primary",disabled:t.grantButton.disabled},on:{click:t.grant}},[t._v(" 发放 "+t._s(t.month)+" 月服务费 ")]):t._e()],1)],1)},l=[],d=a(49659),c=a(27484),u=a.n(c),h={props:["params"],data(){return{regionParams:{resourceId:"dsr-2021-summary",spResource:"true",bu:"Indirect"},userParams:{resourceId:"dsr-2021-summary",partnerId:null},grantButton:{show:!1,disabled:!0},month:u()(this.params.date).get("month")+1}},computed:{dealerParams(){return{resourceId:"dsr-2021-summary",region:this.params.region}},hasPermissionNotAdmin(){return this.$store.getters.hasPermissionNotAdmin("Dsr.kpi")},pickerOptions(){const t=this.$route.query.year;return{disabledDate(e){const a=u()(e).year();return a!=t}}}},created(){this.getDistributePermission()},methods:{dealerChange(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.userParams.partnerId=t.partnerId,this.params.dsrId=""},search(){this.month=u()(this.params.date).get("month")+1,this.grantButton.show=!1,this.getDistributePermission(),this.$bus.$emit("summary-search")},download(){d.Z.download({path:"/v2dsrscgrantstatus/exportKpiList.do",data:{distributorId:this.params.distributorId,dsrId:this.params.dsrId,month:u()(this.params.date).get("month")+1,region:this.params.region,salesCai:this.params.salesCai,year:u()(this.params.date).get("year")}})},getDistributePermission(){var t=this;return(0,o.Z)((function*(){const e=yield d.Z.requestByDO({method:"post",path:"v2dsrscgrantstatus/grantStatus.do",data:{grantMonth:t.month,grantYear:u()(t.params.date).get("year")}}),a=(0,s.Z)(e,2),r=a[0],i=a[1];r&&(t.grantButton.show=i.data.isShow,t.grantButton.disabled=i.data.isClick)}))()},grant(){var t=this;return(0,o.Z)((function*(){const e=t.$loading({lock:!0,text:"Loading",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a=yield d.Z.requestByDO({method:"post",path:"v2dsrscgrantstatus/grant.do",data:{grantMonth:t.month,grantYear:u()(t.params.date).get("year")}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];e.close(),i?(t.$notify.success("发放成功"),t.grantButton.disabled=!0,t.$bus.$emit("expense-grant")):t.$notify.error(o.errorMsg||"发放失败，请稍后重试")}))()}}},p=h,g=a(12995),m=(0,g.Z)(p,n,l,!1,null,null,null),f=m.exports,y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"p-body"},[a("tabs",{attrs:{params:t.params},on:{change:t.getData}}),a("chart",{attrs:{data:t.data}}),t._m(0)],1)])},v=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{margin:"-30px 0 0 40px"}},[a("div",{staticClass:"color-danger"},[t._v("说明：")]),a("div",[t._v("・ MTD: 统计本月的合作客户数")]),a("div",[t._v("・ YTD: 统计从 2021 年年初到目前为止的合作客户数")]),a("div",[t._v("・ 总共: 统计截止到目前为止的合作客户数")])])}],b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"400px",margin:"auto"}})},_=[],x=a(70492),w=a.n(x),S=(a(94859),a(21865),a(83062),{props:["data"],data(){return{chart:null}},computed:{option(){const t=this;return{color:["#bf0001","#0066b1","#b4d071"],title:{text:"总合作客户数",left:"center",textStyle:{color:"#999"}},tooltip:{trigger:"item"},series:[{name:"总合作客户数",type:"pie",radius:"50%",data:R.map((t=>({value:t.work_shop_qty_act,name:t.brand_name})))(this.data),label:{formatter(e){return`${e.name} ${t.$options.filters.toMoney(e.value)}\n${e.percent}%`}},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)}}),I=S,M=(0,g.Z)(I,b,_,!1,null,null,null),A=M.exports,P=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-radio-group",{staticStyle:{margin:"10px 10px 0"},on:{change:t.change},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[a("el-radio-button",{attrs:{label:"mtd"}},[t._v("MTD")]),a("el-radio-button",{attrs:{label:"ytd"}},[t._v("YTD")]),a("el-radio-button",{attrs:{label:"total"}},[t._v("总共")])],1)},D=[],k={props:["params"],methods:{change(){this.$emit("change")}}},L=k,$=(0,g.Z)(L,P,D,!1,null,null,null),T=$.exports,C={props:["globalParams"],components:{chart:A,tabs:T},data(){return{data:[],params:{category:"mtd"},loading:!1}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=R.merge(t.globalParams,t.params);t.data=[],t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"reportview/report/data.do",params:{packageName:"dsr",viewName:"kpi_dashboard_workshop_by_brand",year:u()(e.date).get("year"),month:u()(e.date).get("month")+1,report_type:e.category,dsr_id:e.dsrId,sales_cai:e.salesCai,permission_weight:e.permissionWeight,region:e.region,distributor_id:e.distributorId}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i&&(t.data=o.data||[])}))()}}},N=C,E=(0,g.Z)(N,y,v,!1,null,null,null),O=E.exports,Z=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"p-body"},[a("chart",{staticStyle:{"padding-top":"10px"},attrs:{data:t.data}}),t._m(0)],1)])},V=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{margin:"-10px 0 0 40px"}},[a("div",{staticClass:"color-danger"},[t._v("说明：")]),a("div",[t._v("・ 新客户数: 当月新开合作客户（当月有订单）")]),a("div",[t._v("・ 活跃客户数: 所有非当月的合作客户中，3 个月内有订单的客户数")]),a("div",[t._v("・ 不活跃客户数: 所有非当月的合作客户中，4-12 个月内有订单的客户数")]),a("div",[t._v("・ 流失客户数: 所有非当月的合作客户中，超过 12 个月没有订单的客户数")])])}],B=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"400px",margin:"auto"}})},z=[],W=(a(40451),a(78511),{props:["data"],data(){return{chart:null}},computed:{option(){const t=this,e=["流失客户","不活跃客户","活跃客户","新客户"],a=["金富力","德乐","工程机械"];return{title:{text:"合作客户活跃度分析",left:"center",textStyle:{color:"#999"}},color:["#666","#999","#5c9bd5","#70ad47"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{top:"10%",data:e},grid:{left:"10%",right:"4%",bottom:"15%",top:"28%"},xAxis:{type:"category",data:a},yAxis:{type:"value"},series:R.map((e=>{const r=R.filter(R.propEq("type",e))(this.data);return{name:e,type:"bar",stack:"total",barWidth:60,label:{show:!0,formatter(e){return t.$options.filters.toMoney(e.value||"")}},data:R.map((t=>{const e=R.find(R.propEq("brand_name",t))(r);return e?e.work_shop_qty_act:0}))(a)}}))(e)}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)}}),q=W,Y=(0,g.Z)(q,B,z,!1,null,null,null),G=Y.exports,H={props:["globalParams"],components:{chart:G},data(){return{data:[],loading:!1}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=t.globalParams;t.data=[],t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"reportview/report/data.do",params:{packageName:"dsr",viewName:"kpi_dashboard_workshop_by_status",report_type:e.category,dsr_id:e.dsrId,sales_cai:e.salesCai,permission_weight:e.permissionWeight,region:e.region,distributor_id:e.distributorId}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i&&(t.data=o.data||[])}))()}}},U=H,K=(0,g.Z)(U,Z,V,!1,null,null,null),X=K.exports,j=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"p-body"},[a("tabs",{attrs:{params:t.params},on:{change:t.getData}}),a("chart",{attrs:{data:t.data}}),t._m(0)],1)])},F=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{margin:"0 0 0 40px"}},[a("div",{staticClass:"color-danger"},[t._v("说明：")]),a("div",[t._v("・ 月度售出销量按照 PP 系统归属客户（金富力/德乐/工程机械）统计")])])}],J=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"400px",margin:"auto"}})},Q=[],tt={props:["data"],data(){return{chart:null}},computed:{option(){const t=this,e=["其他","辅油","英雄产品"],a=["金富力","德乐","工程机械"];return{title:{text:"月度售出（L）",left:"center",textStyle:{color:"#999"}},color:["#ccc","#ed7d31","#bf0001"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{top:"10%",data:e},grid:{left:"14%",right:"4%",bottom:"15%",top:"28%"},xAxis:{type:"category",data:a},yAxis:{type:"value"},series:e.map((e=>{const r=R.filter(R.propEq("type",e))(this.data);return{name:e,type:"bar",stack:"total",barWidth:60,label:{show:!0,formatter(e){return t.$options.filters.toMoney(e.value||"")}},data:R.map((t=>{const e=R.find(R.propEq("brand_name",t))(r);return e?Math.round(e.sell_through_l_act):0}))(a)}}))}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)}},et=tt,at=(0,g.Z)(et,J,Q,!1,null,null,null),rt=at.exports,it=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-radio-group",{staticStyle:{margin:"10px 10px 0"},on:{change:t.change},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[a("el-radio-button",{attrs:{label:"mtd"}},[t._v("MTD")]),a("el-radio-button",{attrs:{label:"ytd"}},[t._v("YTD")])],1)},st=[],ot={props:["params"],methods:{change(){this.$emit("change")}}},nt=ot,lt=(0,g.Z)(nt,it,st,!1,null,null,null),dt=lt.exports,ct={props:["globalParams"],components:{chart:rt,tabs:dt},data(){return{data:[],params:{category:"mtd"},loading:!1}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=R.merge(t.globalParams,t.params);t.data=[],t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"reportview/report/data.do",params:{packageName:"dsr",viewName:"kpi_dashboard_sell_through_by_brand",year:u()(e.date).get("year"),month:u()(e.date).get("month")+1,report_type:e.category,dsr_id:e.dsrId,sales_cai:e.salesCai,permission_weight:e.permissionWeight,region:e.region,distributor_id:e.distributorId}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i&&(t.data=o.data||[])}))()}}},ut=ct,ht=(0,g.Z)(ut,j,F,!1,null,null,null),pt=ht.exports,gt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"p-body"},[t.hasDistributorId?t._e():a("div",{staticClass:"l-no-dealer-prompt"},[t._v("请先选择经销商")]),a("tabs",{attrs:{params:t.params},on:{change:t.getData}}),a("chart",{attrs:{data:t.data}}),t._m(0)],1)])},mt=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{margin:"0 0 0 40px"}},[a("div",{staticClass:"color-danger"},[t._v("说明：")]),a("div",[t._v("・ 新客户数：当月新开合作客户（当月有订单）")]),a("div",[t._v("・ 活跃客户数：所有非当月的合作客户中，3 个月内有订单的客户数")])])}],ft=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"400px",margin:"auto"}})},yt=[],vt={props:["data"],data(){return{chart:null}},computed:{option(){const t=R.pipe(R.map(R.prop("dsr_name")),R.uniq)(this.data),e=["新客户","活跃客户","英雄产品销量","总销量"];return{title:{text:"DSR 绩效占比",left:"center",textStyle:{color:"#999"}},color:["rgb(11,45,113)","rgb(0,157,217)","rgb(68,75,13)","rgb(178,204,52)","rgb(188, 48, 147)","rgb(226,24,54)","rgb(229,96,31)","rgb(250,171,24)","rgb(107,100,111)","rgb(219,220,221)"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter(t){let e=t[0].name+"<br>";return R.map((t=>{if(null===t.value||void 0===t.value)return!1;e+=t.marker+t.seriesName+": "+t.value+"%<br>"}))(t),e}},legend:{top:"10%",data:t},grid:{left:"10%",right:"4%",bottom:"15%",top:"28%"},xAxis:{type:"category",data:e},yAxis:{type:"value",axisLabel:{formatter:"{value} %"}},series:R.map((t=>{const a=R.filter(R.propEq("dsr_name",t))(this.data);return{name:t,type:"bar",label:{show:!0,position:"top",formatter(t){return t.value?t.value+"%":""}},data:R.map((t=>{const e=R.find(R.propEq("type",t))(a);return e?Math.round(100*e[/销量$/.test(t)?"sell_through_l_act":"work_shop_qty_act"]):null}))(e)}}))(t)}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)}},bt=vt,_t=(0,g.Z)(bt,ft,yt,!1,null,null,null),xt=_t.exports,wt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-radio-group",{staticStyle:{margin:"10px 10px 0"},on:{change:t.change},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[a("el-radio-button",{attrs:{label:"mtd"}},[t._v("MTD")]),a("el-radio-button",{attrs:{label:"ytd"}},[t._v("YTD")])],1)},St=[],It={props:["params"],methods:{change(){this.$emit("change")}}},Mt=It,At=(0,g.Z)(Mt,wt,St,!1,null,null,null),Pt=At.exports,Dt={props:["globalParams"],components:{chart:xt,tabs:Pt},data(){return{data:[],params:{category:"mtd"},loading:!1,hasDistributorId:!1}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=R.merge(t.globalParams,t.params);if(t.data=[],t.hasDistributorId=!!e.distributorId,!t.hasDistributorId)return;t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"reportview/report/data.do",params:{packageName:"dsr",viewName:"kpi_dashboard_dsr_performance",year:u()(e.date).get("year"),month:u()(e.date).get("month")+1,report_type:e.category,dsr_id:e.dsrId,sales_cai:e.salesCai,permission_weight:e.permissionWeight,region:e.region,distributor_id:e.distributorId}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i&&(t.data=o.data||[])}))()}}},kt=Dt,Lt=(0,g.Z)(kt,gt,mt,!1,null,"485923f4",null),$t=Lt.exports,Rt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"p-body"},[a("tabs",{attrs:{params:t.params},on:{change:t.getData}}),a("chart",{attrs:{data:t.data}}),t._m(0)],1)])},Tt=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{margin:"-20px 20px 0 40px"}},[a("div",{staticClass:"color-danger"},[t._v("说明：")]),a("div",[t._v("・ 活跃客户拜访率：不重复拜访数（活跃客户）/ 活跃客户")]),a("div",[t._v("・ 现有客户拜访率：不重复拜访数（活跃+非活跃）/（活跃+非活跃）客户")]),a("div",[t._v(" ・ 潜在客户拜访率：（本月拜访且变成新店+本月拜访的非合作客户）/（非合作+本月新店） ")])])}],Ct=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"400px",margin:"auto"}})},Nt=[],Et={props:["data"],data(){return{chart:null}},computed:{option(){const t=this,e=["活跃客户拜访率","现有客户拜访率","潜在客户拜访率"],a=["金富力","德乐","工程机械"];let r=[];return R.map((e=>{let i=[],o=[];R.map((t=>{const a=R.filter(R.propEq("brand_name",t))(this.data);if(["活跃客户拜访率"].indexOf(e)>-1){const t=this.calcSeriesData(a,"活跃客户"),e=(0,s.Z)(t,2),r=e[0],n=e[1];i.push(r),o.push(n)}else if(["现有客户拜访率"].indexOf(e)>-1){const t=this.calcSeriesData(a,"活跃客户","不活跃客户"),e=(0,s.Z)(t,2),r=e[0],n=e[1];i.push(r),o.push(n)}else if(["潜在客户拜访率"].indexOf(e)>-1){const t=this.calcSeriesData(a,"本月拜访的非合作客户","本月拜访且变成新店"),e=(0,s.Z)(t,2),r=e[0],n=e[1];i.push(r),o.push(n)}}))(a);const n="活跃客户拜访率"===e?"#4572c4":"现有客户拜访率"===e?"#ed7d31":"潜在客户拜访率"===e?"#a6a6a6":"#999";r.push({name:e,type:"bar",barWidth:"30px",barGap:"40%",label:{show:!0,fontSize:9,formatter(e){return e.data.realValue&&0!=e.data.realValue?e.data.realValue+"\n"+t.$options.filters.toMoney(e.data.value)+"%":"0"}},itemStyle:{normal:{show:!0,color:n}},data:i}),r.push({name:e,type:"bar",barWidth:"30px",barGap:"40%",xAxisIndex:1,label:{show:!0,position:"top",color:"#666",formatter(e){return t.$options.filters.toMoney(e.data.realValue)}},itemStyle:{normal:{show:!0,color:"rgba(255, 255, 255, 0)",borderColor:n}},data:o})}))(e),{title:{text:"客户拜访率 %",left:"center",textStyle:{color:"#999"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter(t){let e=t[0].name+"<br>";return t.map(((t,a)=>!(a>2)&&(null!==t.value&&void(e+=t.marker+t.seriesName+": "+t.value+"%<br>")))),e}},legend:{top:"10%",data:e},grid:{left:"10%",right:"4%",bottom:"15%",top:"28%"},xAxis:[{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!0},splitArea:{show:!1},splitLine:{show:!1},data:a},{type:"category",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#fff"}},data:a}],yAxis:{type:"value",max:120,axisLabel:{formatter:"{value} %"}},series:r}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)},methods:{calcSeriesData(t,e,a){const r=R.find(R.propEq("type",e))(t)||{},i=R.find(R.propEq("type",a))(t)||{};if(R.isEmpty(r)&&R.isEmpty(i))return[{value:null},{value:null}];const s=math.add(math.bignumber(r.visited_workshop_qty||0),math.bignumber(i.visited_workshop_qty||0)).valueOf(),o=math.add(math.bignumber(r.total_workshop_qty||0),math.bignumber(i.total_workshop_qty||0)).valueOf();return[{value:Math.round(s/o*100),realValue:s},{value:100,realValue:o}]}}},Ot=Et,Zt=(0,g.Z)(Ot,Ct,Nt,!1,null,null,null),Vt=Zt.exports,Bt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-radio-group",{staticStyle:{margin:"10px 10px 0"},on:{change:t.change},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[a("el-radio-button",{attrs:{label:"mtd"}},[t._v("MTD")]),a("el-radio-button",{attrs:{label:"ytd"}},[t._v("YTD")])],1)},zt=[],Wt={props:["params"],methods:{change(){this.$emit("change")}}},qt=Wt,Yt=(0,g.Z)(qt,Bt,zt,!1,null,null,null),Gt=Yt.exports,Ht={props:["globalParams"],components:{chart:Vt,tabs:Gt},data(){return{data:[],params:{category:"mtd"},loading:!1}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=R.merge(t.globalParams,t.params);t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"reportview/report/data.do",params:{packageName:"dsr",viewName:"kpi_dashboard_visit_performance",year:u()(e.date).get("year"),month:u()(e.date).get("month")+1,report_type:e.category,dsr_id:e.dsrId,sales_cai:e.salesCai,permission_weight:e.permissionWeight,region:e.region,distributor_id:e.distributorId}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i&&(t.data=o.data||[])}))()}}},Ut=Ht,Kt=(0,g.Z)(Ut,Rt,Tt,!1,null,null,null),Xt=Kt.exports,jt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"p-body"},[a("tabs",{attrs:{params:t.params},on:{change:t.getData}}),2021==t.$route.query.year?a("chart2021",{attrs:{data:t.data}}):a("chart",{attrs:{data:t.data}})],1)])},Ft=[],Jt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"450px",margin:"auto"}})},Qt=[],te={props:["data"],data(){return{chart:null}},computed:{option(){const t=this,e=this.data[0]?Math.round(100*this.data[0].sell_through_l_target)/100:0,a=R.pipe(R.map(R.prop("sell_through_l_act")),R.sum)(this.data),r=e?Math.round(a/e*1e4)/100:100,i=["金富力","德乐","工程机械"];let s=[{name:"百分比",type:"bar",barWidth:60,xAxisIndex:2,label:{show:!0,position:"top",color:"#666",formatter(){return`完成率 ${r}%`}},itemStyle:{normal:{show:!0,color:"#a6a6a6"}},data:[a]}];return R.map((e=>{s.push({name:e.brand_name,type:"bar",barWidth:60,stack:"actual",label:{show:!0,formatter(e){return t.$options.filters.toMoney(e.value)}},itemStyle:{normal:{show:!0,color:"金富力"===e.brand_name?"#c20002":"德乐"===e.brand_name?"#036fbe":"工程机械"===e.brand_name?"#92d64c":"#999"}},data:[Math.round(e.sell_through_l_act)]})}))(this.data),{title:{text:"销售达成情况",left:"center",textStyle:{color:"#999"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter(a){let r=a[0].name+"<br>";if("目标"===a[0].name){const i=a[a.length-1];if(!i)return r;r+="总和："+t.$options.filters.toMoney(e||0)}else R.map((e=>{if(["目标","百分比"].indexOf(e.seriesName)>-1)return!1;r+=e.marker+e.seriesName+": "+t.$options.filters.toMoney(e.value)+"<br>"}))(a);return r}},legend:{top:"10%",data:i},grid:{left:"14%",right:"4%",bottom:"15%",top:"28%"},xAxis:[{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!0},splitArea:{show:!1},splitLine:{show:!1},data:["实际"]},{type:"category",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#fff"}},data:["实际"]},{type:"category",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#fff"}},data:["实际"]}],yAxis:{type:"value"},series:s}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)}},ee=te,ae=(0,g.Z)(ee,Jt,Qt,!1,null,null,null),re=ae.exports,ie=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chart",staticStyle:{width:"90%",height:"450px",margin:"auto"}})},se=[],oe={props:["data"],data(){return{chart:null}},computed:{option(){const t=this,e=this.data[0]?Math.round(100*this.data[0].sell_through_l_target)/100:0,a=R.pipe(R.map(R.prop("sell_through_l_act")),R.sum)(this.data),r=e?Math.round(a/e*1e4)/100:100,i=["金富力","德乐","工程机械"];let s=[{name:"百分比",type:"bar",barWidth:60,xAxisIndex:2,label:{show:!0,position:"top",color:"#666",formatter(){return`完成率 ${r}%`}},itemStyle:{normal:{show:!0,color:"#a6a6a6"}},data:[null,a]},{name:"目标",type:"bar",barWidth:60,xAxisIndex:1,label:{show:!0,formatter(e){return t.$options.filters.toMoney(e.value)}},itemStyle:{normal:{show:!0,color:"#a6a6a6"}},data:[Math.round(e),null]}];return R.map((e=>{s.push({name:e.brand_name,type:"bar",barWidth:60,stack:"actual",label:{show:!0,formatter(e){return t.$options.filters.toMoney(e.value)}},itemStyle:{normal:{show:!0,color:"金富力"===e.brand_name?"#c20002":"德乐"===e.brand_name?"#036fbe":"工程机械"===e.brand_name?"#92d64c":"#999"}},data:[null,Math.round(e.sell_through_l_act)]})}))(this.data),{title:{text:"销售达成情况",left:"center",textStyle:{color:"#999"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter(a){let r=a[0].name+"<br>";if("目标"===a[0].name){const i=a[a.length-1];if(!i)return r;r+="总和："+t.$options.filters.toMoney(e||0)}else R.map((e=>{if(["目标","百分比"].indexOf(e.seriesName)>-1)return!1;r+=e.marker+e.seriesName+": "+t.$options.filters.toMoney(e.value)+"<br>"}))(a);return r}},legend:{top:"10%",data:i},grid:{left:"14%",right:"4%",bottom:"15%",top:"28%"},xAxis:[{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!0},splitArea:{show:!1},splitLine:{show:!1},data:["目标","实际"]},{type:"category",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#fff"}},data:["目标","实际"]},{type:"category",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#fff"}},data:["目标","实际"]}],yAxis:{type:"value"},series:s}}},watch:{option:{handler(){this.chart.setOption(this.option,!0)},deep:!0}},mounted(){this.chart=w().init(this.$refs.chart),this.chart.setOption(this.option),window.addEventListener("resize",(()=>{this.chart.resize()}),!1)}},ne=oe,le=(0,g.Z)(ne,ie,se,!1,null,null,null),de=le.exports,ce=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-radio-group",{staticStyle:{margin:"10px 10px 0"},on:{change:t.change},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[a("el-radio-button",{attrs:{label:"mtd"}},[t._v("MTD")]),a("el-radio-button",{attrs:{label:"ytd"}},[t._v("YTD")])],1)},ue=[],he={props:["params"],methods:{change(){this.$emit("change")}}},pe=he,ge=(0,g.Z)(pe,ce,ue,!1,null,null,null),me=ge.exports,fe={props:["globalParams"],components:{chart:re,tabs:me,chart2021:de},data(){return{data:[],params:{category:"mtd"},loading:!1}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=R.merge(t.globalParams,t.params);t.data=[],t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"reportview/report/data.do",params:{packageName:"dsr",viewName:"kpi_dashboard_sell_through_completion",year:u()(e.date).get("year"),month:u()(e.date).get("month")+1,report_type:e.category,dsr_id:e.dsrId,sales_cai:e.salesCai,permission_weight:e.permissionWeight,region:e.region,distributor_id:e.distributorId}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i&&(t.data=o.data||[])}))()}}},ye=fe,ve=(0,g.Z)(ye,jt,Ft,!1,null,null,null),be=ve.exports,_e=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.table.loading,expression:"table.loading"}],attrs:{data:t.data,size:"mini","row-style":t.rowStyle}},[a("el-table-column",{attrs:{label:"区域",prop:"region",width:"80px",fixed:""}}),a("el-table-column",{attrs:{label:"经销商",prop:"customerNameCn",width:"200px",fixed:""}}),a("el-table-column",{attrs:{label:"DSR",prop:"dsrName",fixed:""}}),a("el-table-column",{attrs:{label:"从2021.04到当前为止的合计",align:"center"}},[a("el-table-column",{attrs:{label:"实际新客户数",prop:"newCustomerActualYtd"}}),a("el-table-column",{attrs:{label:"拜访天数",prop:"visitDaysActualYtd"}}),a("el-table-column",{attrs:{label:"实际有效销量（L）",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(t._f("toRound")(e.row.salesVolumeActualYtd)))+" ")]}}])}),a("el-table-column",{attrs:{label:"实际英雄产品有效销量（L）",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(t._f("toRound")(e.row.heroProductSalesVolumeActualYtd)))+" ")]}}])})],1),a("el-table-column",{attrs:{label:"本月",align:"center"}},[a("el-table-column",{attrs:{label:"新客户数（家）",align:"center"}},[a("el-table-column",{attrs:{label:"实际",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(e.row.newCustomerActualMtd))+" ")]}}])}),a("el-table-column",{attrs:{label:"目标",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(e.row.newCustomerTargetMtd))+" ")]}}])}),a("el-table-column",{attrs:{label:"KPI 达标率",align:"center",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toRound")(100*e.row.newCustomerKpiMtd))+"% ")]}}])})],1),a("el-table-column",{attrs:{label:"拜访天数",align:"center"}},[a("el-table-column",{attrs:{label:"实际",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(e.row.visitDaysActualMtd))+" ")]}}])}),a("el-table-column",{attrs:{label:"目标",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(e.row.visitDaysTargetMtd))+" ")]}}])}),a("el-table-column",{attrs:{label:"KPI 达标率",align:"center",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toRound")(100*e.row.visitDaysKpiMtd))+"% ")]}}])})],1),a("el-table-column",{attrs:{label:"有效销量（L）",align:"center"}},[a("el-table-column",{attrs:{label:"实际",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(t._f("toRound")(e.row.salesVolumeActualMtd)))+" ")]}}])}),a("el-table-column",{attrs:{label:"目标",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(t._f("toRound")(e.row.salesVolumeTargetMtd)))+" ")]}}])}),a("el-table-column",{attrs:{label:"KPI 达标率",align:"center",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toRound")(100*e.row.salesVolumeKpiMtd))+"% ")]}}])})],1),a("el-table-column",{attrs:{label:"英雄产品有效销量（L）",align:"center"}},[a("el-table-column",{attrs:{label:"实际",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(t._f("toRound")(e.row.heroProductSalesVolumeActualMtd)))+" ")]}}])}),a("el-table-column",{attrs:{label:"目标",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(t._f("toRound")(e.row.heroProductSalesVolumeTargetMtd)))+" ")]}}])}),a("el-table-column",{attrs:{label:"KPI 达标率",align:"center",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toRound")(100*e.row.heroProductSalesVolumeKpiMtd))+"% ")]}}])})],1),a("el-table-column",{attrs:{label:"本月综合 KPI 达标率",align:"center",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toRound")(100*e.row.multipleStandardReachingRateMtd))+"% ")]}}])}),t.hasPermission(1024)?a("el-table-column",{attrs:{label:"本月应发",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("toMoney")(e.row.pointPaidTargetMtd))+" ")]}}],null,!1,2433794323)}):t._e(),a("el-table-column",{attrs:{label:"本月实发",align:"center",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("inputPiece",{attrs:{row:e.row,"global-params":t.globalParams,dialog:t.dialog}})]}}])})],1)],1),t.table.total?a("div",{staticClass:"text-center",staticStyle:{"padding-top":"30px"}},[a("el-pagination",{attrs:{background:"",layout:"sizes, prev, pager, next",total:t.table.total,"current-page":t.filter.page,"page-size":t.filter.pageSize,"page-sizes":[10,20,50]},on:{"update:currentPage":function(e){return t.$set(t.filter,"page",e)},"update:current-page":function(e){return t.$set(t.filter,"page",e)},"update:pageSize":function(e){return t.$set(t.filter,"pageSize",e)},"update:page-size":function(e){return t.$set(t.filter,"pageSize",e)}}})],1):t._e(),a("dialogPiece",{attrs:{dialog:t.dialog.history}})],1)},xe=[],we=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"g-autosave-group"},[t.row.ifCanEdit?a("div",[a("el-input",{class:{"g-autosave--fail":t.fail},attrs:{size:"mini",disabled:t.disabled},on:{change:t.change},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.loading,expression:"loading"}],staticClass:"g-autosave--loading"},[t._v("正在上传")])],1):a("div",{staticClass:"g-autosave-group"},[t._v(" "+t._s(t._f("toMoney")(t.row.pointPaidActualMtd))+" ")]),t.row.ifShowHistory?a("div",{staticClass:"g-history color-primary",on:{click:t.showHistoryDialog}},[t._v(" 历史 ")]):t._e()])},Se=[],Ie={props:["row","globalParams","dialog"],data(){return{value:this.row.pointPaidActualMtd,loading:!1,fail:!1}},computed:{disabled(){return this.loading||!this.row.ifCanEdit}},watch:{"row.pointPaidActualMtd"(t){t!==this.value&&(this.value=t)}},methods:{change(){var t=this;return(0,o.Z)((function*(){const e=t.globalParams;t.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"v2dsrscgrantstatus/adjust.do",data:{adjustYear:u()(e.date).get("year"),adjustMonth:u()(e.date).get("month")+1,dsrId:t.row.dsrId||null,oldValue:t.row.pointPaidActualMtd,partnerId:t.row.partnerId,region:t.row.region,targetValue:t.value}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.loading=!1,i?(t.fail=!1,t.row.pointPaidActualMtd=t.value):(t.fail=!0,t.$notify.error(o.errorMsg||"数据保存失败"))}))()},showHistoryDialog(){this.dialog.history.params=R.merge({date:this.globalParams.date},this.row),this.dialog.history.show=!0}}},Me=Ie,Ae=(0,g.Z)(Me,we,Se,!1,null,null,null),Pe=Ae.exports,De=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:t.dialog.title,visible:t.dialog.show,width:"600px"},on:{"update:visible":function(e){return t.$set(t.dialog,"show",e)}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.list.data,stripe:"",border:"",size:"mini","empty-text":t.list.loading?t.list.loadingText:t.list.emptyText}},[a("el-table-column",{attrs:{label:"操作人员",prop:"createUserName",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"操作类型",prop:"changeType",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"变化值",prop:"changeValue",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"最终值",prop:"newValue",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"操作时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.dayjs(e.row.createTime).format("YYYY-MM-DD HH:mm"))+" ")]}}])})],1),t.list.total?a("div",{staticClass:"text-center",staticStyle:{"padding-top":"30px"}},[a("el-pagination",{attrs:{background:"",layout:"prev, pager, next",total:t.list.total,"current-page":t.list.filters.page},on:{"update:currentPage":function(e){return t.$set(t.list.filters,"page",e)},"update:current-page":function(e){return t.$set(t.list.filters,"page",e)},"current-change":t.getData}})],1):t._e()],1)},ke=[],Le={props:["dialog"],data(){return{list:{loading:!1,loadingText:"正在加载数据",emptyText:"没有数据",total:0,filters:{page:1,pageSize:10},data:[]}}},watch:{"dialog.show"(t){t&&(this.list.filters.page=1,this.list.filters.total=0,this.list.data=[],this.getData())}},methods:{dayjs:u(),getData(){var t=this;return(0,o.Z)((function*(){const e=t.dialog.params;t.list.data=[],t.list.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"v2dsrscgrantstatus/adjustHistory.do",data:{adjustYear:u()(e.date).get("year"),adjustMonth:u()(e.date).get("month")+1,dsrId:e.dsrId,partnerId:e.partnerId,paging:!0,field:"updateTime",direction:"DESC",limit:t.list.filters.pageSize,start:(t.list.filters.page-1)*t.list.filters.pageSize}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.list.loading=!1,i&&(t.list.data=o.resultLst||[],t.list.total=o.total)}))()}}},$e=Le,Re=(0,g.Z)($e,De,ke,!1,null,null,null),Te=Re.exports,Ce={props:["globalParams"],components:{inputPiece:Pe,dialogPiece:Te},data(){return{filter:{page:1,pageSize:10},table:{loading:!1,data:[],total:0},dialog:{history:{title:"调整历史",show:!1}}}},computed:{data(){return this.table.data.slice((this.filter.page-1)*this.filter.pageSize,this.filter.page*this.filter.pageSize)},permissionWeight(){return this.$store.getters.getPermissionData("Dsr.kpi")},hasPermission(){return this.$store.getters.hasPermission("Dsr.kpi")}},mounted(){this.$bus.$on("summary-search",(()=>{this.getData()})),this.$bus.$on("expense-grant",(()=>{this.table.data.map((t=>{t.ifCanEdit=!1}))}))},methods:{getData(){var t=this;return(0,o.Z)((function*(){const e=t.globalParams;t.table.data=[],t.table.loading=!0;const a=yield d.Z.requestByDO({method:"post",path:"v2dsrscgrantstatus/kpiList.do",data:{year:u()(e.date).get("year"),month:u()(e.date).get("month")+1,dsrId:e.dsrId||null,region:e.region||"",distributorId:e.distributorId||null}}),r=(0,s.Z)(a,2),i=r[0],o=r[1];t.table.loading=!1,i&&(t.table.data=o.resultLst||[],t.table.total=(o.resultLst||[]).length)}))()},rowStyle(t){let e=t.row;if(e.ifTotal)return{"background-color":"#eee"}}}},Ne=Ce,Ee=(0,g.Z)(Ne,_e,xe,!1,null,null,null),Oe=Ee.exports,Ze={components:{search:f,customerTotal:O,customerActivity:X,monthlySale:pt,dsrPerformance:$t,customerVisit:Xt,saleTarget:be,dealerTable:Oe},data(){return{params:{date:u()(new Date(this.$route.query.year,u()().month(),u()().day())),region:"",distributorId:"",dsrId:"",permissionWeight:null,salesCai:null}}},computed:{permissionWeight(){return this.$store.getters.getPermissionData("Dsr.kpi")},hasPermissionNotAdmin(){return this.$store.getters.hasPermissionNotAdmin("Dsr.kpi")}},beforeDestroy(){this.$bus.$off("summary-search"),this.$bus.$off("expense-grant")},mounted(){this.getCurrentUser(),null!==this.permissionWeight&&(this.params.permissionWeight=this.permissionWeight)},methods:{getCurrentUser(){var t=this;return(0,o.Z)((function*(){const e=yield t.$store.dispatch("getCurrentUserInfo"),a=(0,s.Z)(e,2),r=a[0],i=a[1];r?(t.params.salesCai=i.cai,null!==t.params.permissionWeight&&(t.hasPermissionNotAdmin(16)&&(t.params.distributorId=i.distributorId),t.$bus.$emit("summary-search"))):t.$notify.error("请求异常，请稍后重试")}))()}}},Ve=Ze,Be=(0,g.Z)(Ve,r,i,!1,null,null,null),ze=Be.exports}}]);
//# sourceMappingURL=878.a3138946.js.map