package com.chevron.haorder.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;

import com.chevron.haorder.business.HaOrderBizService;
import com.chevron.haorder.dao.HaOrderApplyBatchVoMapper;
import com.chevron.haorder.model.HaOrderApplyBatchVo;
import com.chevron.haorder.model.HaOrderProductPriceInfoVo;
import com.chevron.haorder.model.workflow.HaOrderView;
import com.chevron.haorder.service.IHaOrderWorkFlowService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.sys.email.service.EmailSenderService;
import com.workflow.dao.ApproveHistoryMapper;
import com.workflow.model.ApproveHistory;
@Service
public class HaOrderWorkFlowServiceImpl implements IHaOrderWorkFlowService{
	private static Logger log = LoggerFactory.getLogger(HaOrderWorkFlowServiceImpl.class);
	@Resource
	private HaOrderApplyBatchVoMapper haOrderApplyMapper;
	@Resource
	private ApproveHistoryMapper approveHistoryMapper;
	@Resource 
	HaOrderBizService haOrderBizService;
	@Resource 
	EmailSenderService emailSenderService;
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	
	@Override
	public Map<String, Object> queryHAOrderWorkFlowTasks(String loginName,String pKey,String batchId)throws Exception {
		Map<String,Object> returnMap = new HashMap<String,Object>();
		//TODO V2版本后续需要用工作流查询当前登录用户的任务，，并返回查询的任务(申请审批订单信息+任务id)
		List<HaOrderView> lst = new ArrayList<HaOrderView>();
		
		//v1版本先从数据库中获取需要审批的订单批次申请信息
		//根据当前登录用户获取用户角色
		String currentChRoleNames = HaOrderBizService.getCurrentUserRoles();
		log.info("queryHAOrderWorkFlowTasks currentChRoleName:"+currentChRoleNames);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<HaOrderApplyBatchVo> lstHaOrderApplyBatchVos = new ArrayList<HaOrderApplyBatchVo>();
		
		//若是价格审批者  查询审核中的所有申请批次
		if(currentChRoleNames.contains(Constants.CHEVRON_PRICE_AUDIT))
		{
			
			reqMap.put("processKey", HaOrderApplyBatchVo.APPLY_PRO_KEY);
			reqMap.put("approveStatus", HaOrderApplyBatchVo.APPLY_STATUS_APPROVING);//审核中
			lstHaOrderApplyBatchVos = haOrderApplyMapper.getPendingApprovalByMap(reqMap);
			
			
		}
		//若是SAP CSR审批者  需要查询已经被价格审批者审批后的订单，判断对应批次，是否存在审核中的订单
		else if(currentChRoleNames.contains(Constants.CHEVRON_SAP_CSR))
		{
			lstHaOrderApplyBatchVos = haOrderApplyMapper.getPendingApprovalForCSR(reqMap);
		}
		
		for(HaOrderApplyBatchVo haOrderApply:lstHaOrderApplyBatchVos)
		{
			HaOrderView  haOrderView = new HaOrderView();
			haOrderView.setApplyBatchInfo(haOrderApply);
			lst.add(haOrderView);
		}
		
		returnMap.put("resultLst", lst);
		returnMap.put("totalRecord", lst.size());
		return returnMap;
	}
	
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> approveHAOrder(String applyBatchId,
			String status, String approveRemark)
	{
		log.info("approveHAOrder applyBatchId:"+applyBatchId+" status:"+status+" approveRemark:"+approveRemark);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//获取当前用户对应的角色
		String currentChRoleNames = HaOrderBizService.getCurrentUserRoles();
		log.info("approveHAOrder currentChRoleName:"+currentChRoleNames);
		try
		{
			//查询此批订单是否已经被确认，主要房子邮件重复确认，，，
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("bachId", applyBatchId);
			reqMap.put("processKey", HaOrderApplyBatchVo.APPLY_PRO_KEY);
			reqMap.put("hasApprovedStatus", "1|0");//已经被确认过 不能再次确认
			List<HaOrderApplyBatchVo> lst = haOrderApplyMapper.getHasApprovaledByMap(reqMap);
			if(null!=lst && !lst.isEmpty())
			{
				throw new Exception("订单已经被审核，不能进行重复审核");
			}
			
			
			//录入审批意见表中
			insertApproveHis(applyBatchId,status,approveRemark,currentChRoleNames);
			
			HaOrderApplyBatchVo applyVo = new HaOrderApplyBatchVo();
			applyVo.setBatchid(Long.parseLong(applyBatchId));
			applyVo.setApproveStatus(status);
			//不通过，更新审批订单批次状态，
			if(status.equals(HaOrderApplyBatchVo.APPLY_STATUS_UNAPPROVED))
			{
				haOrderApplyMapper.updateByPrimaryKeySelective(applyVo);
			}else
			{
				//通过，若是价格审批者角色，则发送邮件给SAP CSR进行审批
				if(currentChRoleNames.contains(Constants.CHEVRON_PRICE_AUDIT))
				{
					Map<String,Object> baseDataMap = new HashMap<String,Object>();
					baseDataMap.put(HaOrderBizService.BATCH_ID, Long.parseLong(applyBatchId));
					baseDataMap.put(HaOrderBizService.CHROLE_NAME, Constants.CHEVRON_SAP_CSR);
					baseDataMap.put(HaOrderBizService.PD_KEY, HaOrderApplyBatchVo.APPLY_PRO_KEY);
					sndEmail(baseDataMap);
				}
				//通过，若是SAP CSR角色，那么更新订单审批状态为通过，，，然后调用bizService中的方法，构造文件，然后上传到文件服务器中
				else if(currentChRoleNames.contains(Constants.CHEVRON_SAP_CSR))
				{
					haOrderApplyMapper.updateByPrimaryKeySelective(applyVo);
					haOrderBizService.handleHAOrdersToFileServer(applyBatchId);
				}
			
			}
			resultMap.put("code", "success");
		}catch (Exception e) {
			log.error("approveHAOrder Exception:"+e.getLocalizedMessage());
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "操作接口错误信息："+e.getLocalizedMessage());
		}
		return resultMap;
	}
	
	
	/**
	 * TODO V2版本 工作流做
	 */
	@Override
	public Map<String, Object> approveHAOrder(String applyBatchId) {
		return null;
	}
	
	
	@Override
	public Map<String, Object> queryHAOrderByBatchId(Long batchId) {
		return null;
	}
	
	@Override
	public  boolean insertApproveHis(String applyBatchId,
			String status, String approveRemark,String currentChRoleNames)
	{
		ApproveHistory approveHis = new ApproveHistory();
		approveHis.setOrderBatchId(Long.parseLong(applyBatchId));
		approveHis.setApprover(""+ContextUtil.getCurUser().getUserId());
		if(null==approveRemark||approveRemark.isEmpty())
		{
			if(status.equals(HaOrderApplyBatchVo.APPLY_STATUS_UNAPPROVED))
			{
				approveHis.setApproveRemark(HaOrderApplyBatchVo.APPLY_STATUS_UNAPPROVED_REMARK);
			}else
			{
				approveHis.setApproveRemark(HaOrderApplyBatchVo.APPLY_STATUS_APPROVED_REMARK);
			}
		}else
		{
			approveHis.setApproveRemark(approveRemark);
		}
		if(status.equals(HaOrderApplyBatchVo.APPLY_STATUS_APPROVED))
		{
			/*if(currentChRoleNames.contains(Constants.CHEVRON_PRICE_AUDIT))
			{
				approveHis.setApproveStatus(HaOrderApplyBatchVo.APPLY_STATUS_APPROVING);
			}else
			{
				approveHis.setApproveStatus(status);
			}*/
			approveHis.setApproveStatus(status);
		}else
		{
			approveHis.setApproveStatus(status);
		}
		approveHis.setApplyTempletType(HaOrderApplyBatchVo.APPLY_PRO_KEY);
		approveHis.setApproveTime(new Date());
		approveHistoryMapper.insertSelective(approveHis);
		return true;
	}
	
	public Map<String,Object> sndEmail(Map<String,Object> baseDataMap)throws Exception
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> emailInfoMap = haOrderBizService.getSndEmailInfo(baseDataMap);
		if(null==emailInfoMap)
		{
			throw new Exception("SAP CSR审批者邮箱信息没有设置");
		}
		List<HaOrderProductPriceInfoVo> allPriceInfoList = new ArrayList<HaOrderProductPriceInfoVo>();
		allPriceInfoList = haOrderBizService.getPriceListByBatchId((Long) baseDataMap.get(HaOrderBizService.BATCH_ID));
		emailInfoMap.put("allPriceInfoList", allPriceInfoList);
		
		//发送邮件信息
		//quartz中使用此获取上下文
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		emailInfoMap.put("msgcontent", "您有新的TM/JD订单需要到SAP里面维护价格");
		emailSenderService.sendEmailForCommon(context,
				(String[])emailInfoMap.get("accepters"), (String[])emailInfoMap.get("ccaccepters"), MyPropertyConfigurer.getVal("mail.ha.order.confirm.subject1"),emailInfoMap, null, MyPropertyConfigurer.getVal("mail.ha.order.confirm.ftl"));
		resultMap.put("code", "success");
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> getApproveHisByBatchId(Long applyBatchId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			reqMap.put("applyBatchId", applyBatchId);
			reqMap.put("applyTempletType", HaOrderApplyBatchVo.APPLY_PRO_KEY);
			List<ApproveHistory> lstApproveHis = approveHistoryMapper.getApproveHistorys(reqMap);
			resultMap.put(RESULT_LST_KEY, lstApproveHis);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			log.info(e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, e.getLocalizedMessage());
		}
		return resultMap;
	}
	
	
	
}
