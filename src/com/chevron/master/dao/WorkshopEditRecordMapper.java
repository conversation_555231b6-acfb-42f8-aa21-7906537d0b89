package com.chevron.master.dao;

import com.chevron.master.model.*;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * Workshop Edit Record-workshop_edit_record数据库操作对象
 * <AUTHOR>
 * @version 1.0 2024-06-24 14:30
 */
public interface WorkshopEditRecordMapper {

	/**
	 * 修改页面根据主键更新Workshop Edit Record对象(修改页面修改属性)
	 * @param record 新Workshop Edit Record对象
	 * @return 数据库操作影响记录条数
	 */
    int updateForEditPage(WorkshopEditRecord record);
    
	/**
	 * 根据主键更新Workshop Edit Record对象(非空属性)
	 * @param record 新Workshop Edit Record对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(WorkshopEditRecord record);

    /**
     * 根据主键删除Workshop Edit Record对象
     * @param id 删除Workshop Edit Record对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询Workshop Edit Record对象
	 * @param id Workshop Edit Record对象主键值
	 * @return Workshop Edit Record对象结果
	 */
    WorkshopEditRecord selectByPrimaryKey(Long id);

 	/**
	 * 插入Workshop Edit Record对象(非空属性)
	 * @param record 被插入Workshop Edit Record对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(WorkshopEditRecord record);

	/**
	 * 更新Workshop Edit Record对象
	 * @param record 被修改Workshop Edit Record对象
	 * @param example 过滤Workshop Edit Record对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") WorkshopEditRecord record, @Param("example") WorkshopEditRecordExample example);

	/**
	 * 删除Workshop Edit Record对象
	 * @param example 被删除Workshop Edit Record对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(WorkshopEditRecordExample example);
    
 	/**
	 * 查询满足条件的Workshop Edit Record对象数量
	 * @param example 过滤Workshop Edit Record对象条件
	 * @return 满足条件Workshop Edit Record对象数量
	 */
    int countByExample(WorkshopEditRecordExample example);

	/**
	 * 单表查询Workshop Edit Record对象
	 * @param example 过滤Workshop Edit Record对象条件
	 * @return 满足条件Workshop Edit Record对象集合
	 */
    List<WorkshopEditRecord> selectByExample(WorkshopEditRecordExample example);

	/**
	 * 列表查询Workshop Edit Record对象
	 * @param params 查询条件
	 * @return 满足条件Workshop Edit Record对象集合
	 */
    List<WorkshopEditRecord> queryByParams(Map<String, Object> params);

	/**
	 * 分页查询门店对象
	 * @param params 查询条件
	 * @return 满足条件门店对象集合
	 */
	List<WorkshopEditRecord> queryForPage(WorkshopEditRecordParams params);

	/**
	 * 查询flsr
	 * @param partnerId 查询条件
	 * @return 满足条件门店对象集合
	 */
	List<Map<String, Object>> queryFlsrByPartner(@Param("partnerId")Long partnerId, @Param("workshopId")Long workshopId);

}
