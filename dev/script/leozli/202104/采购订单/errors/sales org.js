{
    "createSalesOrderResponse":{
        "SALESDOCUMENT":"",
        "EXTENSIONIN":[

        ],
        "ORDER_CONDITION_EX":[

        ],
        "ORDER_ITEMS_IN":Array[1],
        "ORDER_ITEMS_OUT":Array[0],
        "ORDER_KEYS":[

        ],
        "ORDER_PARTNERS":Array[2],
        "ORDER_SCHEDULES_IN":[
            {
                "REQ_TIME":"00:00:00",
                "TP_DATE":"0000-00-00",
                "REQ_QTY":"30.000",
                "PLAN_SCHED_TYPE":"",
                "REL_TYPE":"",
                "LOAD_DATE":"0000-00-00",
                "DLV_DATE":"0000-00-00",
                "REFLOGSYS":"",
                "MS_DATE":"0000-00-00",
                "SCHED_TYPE":"",
                "TP_TIME":"00:00:00",
                "GI_DATE":"0000-00-00",
                "REQ_DATE":"0000-00-00",
                "DATE_TYPE":"",
                "ITM_NUMBER":"000010",
                "REFOBJTYPE":"",
                "LOAD_TIME":"00:00:00",
                "DLV_TIME":"00:00:00",
                "SCHED_LINE":"0000",
                "REFOBJKEY":"",
                "REQ_DLV_BL":"",
                "MS_TIME":"00:00:00",
                "GI_TIME":"00:00:00"
            }
        ],
        "ORDER_SCHEDULE_EX":[

        ],
        "ORDER_TAX_INFORMATION_EX":[

        ],
        "ORDER_TEXT":[
            {
                "FORMAT_COL":"*",
                "ITM_NUMBER":"000000",
                "LANGU":"E",
                "LANGU_ISO":"",
                "DOC_NUMBER":"",
                "FUNCTION":"",
                "TEXT_LINE":"foshanadmin1",
                "TEXT_ID":"0012"
            },
            {
                "FORMAT_COL":"=",
                "ITM_NUMBER":"000000",
                "LANGU":"E",
                "LANGU_ISO":"",
                "DOC_NUMBER":"",
                "FUNCTION":"",
                "TEXT_LINE":"Simulate Order created",
                "TEXT_ID":"0002"
            }
        ],
        "RETURN":[
            {
                "SYSTEM":"",
                "LOG_NO":"",
                "LOG_MSG_NO":"000000",
                "NUMBER":"002",
                "PARAMETER":"",
                "MESSAGE":"No Valid Doc Type present for combination of Sale Org \"111 \" and Division \" 5 \"",
                "FIELD":"",
                "ROW":0,
                "TYPE":"C",
                "ID":"Before Bapi Call",
                "MESSAGE_V4":"",
                "MESSAGE_V3":"",
                "MESSAGE_V2":"",
                "MESSAGE_V1":""
            },
            {
                "SYSTEM":"",
                "LOG_NO":"",
                "LOG_MSG_NO":"000000",
                "NUMBER":"003",
                "PARAMETER":"",
                "MESSAGE":"Your order is unable to be created contact your Customer Service Center.",
                "FIELD":"",
                "ROW":0,
                "TYPE":"E",
                "ID":"Before Bapi Call",
                "MESSAGE_V4":"",
                "MESSAGE_V3":"",
                "MESSAGE_V2":"",
                "MESSAGE_V1":""
            }
        ],
        "ORDER_IS_INITIAL_FILL_EX":[

        ]
    }
}