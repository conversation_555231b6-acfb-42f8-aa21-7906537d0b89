INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (30           ,''            ,'所有合伙人'           ,'SP_FULL'           ,getdate()            ,1           ,'${cond.excludeTestSp}'            ,1           ,'CHEVRON'            ,'${cond.excludeTestSp}')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (31           ,''            ,'所属合伙人'           ,'SP_FULL'           ,getdate()            ,1           ,'1=1'            ,2           ,'SP'            ,'')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (32           ,''            ,'销售负责合伙人'           ,'SP_FULL'           ,getdate()            ,1           ,'exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales dp_001_cos left join dw_sales_role dp_001_sr1 on dp_001_sr1.[sales_cai]=dp_001_cos.sales_cai and dp_001_sr1.del_flag=0 where dp_001_sr1.sales_cai_level like ''%_${value.loginCai}%'' and dp_001_cos.distributor_id=${field.distributorId} ${030.cond.channelWeightFull})'            ,3           ,'CHEVRON'            ,'(exists(select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales dp_001_cos left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] dp_001_soh1 on dp_001_cos.region=dp_001_soh1.region and dp_001_soh1.level_name=''Region'' and dp_001_cos.del_flag=0 left join [PP_MID].[dbo].[syn_dw_to_pp_sales_role] dp_001_sr1 on dp_001_sr1.[sales_cai]=dp_001_soh1.level_mgr_cai and dp_001_sr1.sales_role=''ASM'' and dp_001_sr1.del_flag=0 where dp_001_sr1.sales_cai_level like ''%${value.loginCai}%'' ${010.cond.regionName.cos} ${030.cond.channelWeightFull}) or exists (select 1 from wx_t_partner_responsible_main dp_001_prm where dp_001_prm.user_id=${value.loginUserId} ${020.cond.regionName.prm} ${cond.permissionConfigFun}))')

update r set r.resource_type_code='SP_FULL'
from wx_t_resource r where resource_name='合伙人订单'