package com.chevron.thirdorder.service.impl;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.o2oorder.service.O2oOrderService;
import com.chevron.pms.dao.OrderLineVoMapper;
import com.chevron.pms.dao.OrderVinCodeMapper;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.dao.RegionVoMapper;
import com.chevron.pms.dao.WxtOrderYYFWSetsVoMapper;
import com.chevron.pms.model.OrderLineVo;
import com.chevron.pms.model.OrderVinCode;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.model.RegionVo;
import com.chevron.pms.service.OrderBizService;
import com.chevron.pms.service.VehicleService;
import com.chevron.thirdorder.business.WebThirdOrderService;
import com.chevron.thirdorder.model.MessageVo;
import com.chevron.thirdorder.model.OrderCommonVo;
import com.chevron.thirdorder.model.OrderFWVo;
import com.chevron.thirdorder.model.OrderProductVo;
import com.chevron.thirdorder.model.OrderSkuInfo;
import com.chevron.thirdorder.model.OrderXBVo;
import com.chevron.thirdorder.model.OrderYYFWVo;
import com.chevron.thirdorder.model.Product;
import com.chevron.thirdorder.model.ReqAddFWOrder;
import com.chevron.thirdorder.model.ReqGeneralFWOrder;
import com.chevron.thirdorder.model.ReqProduct;
import com.chevron.thirdorder.model.ReqXBOrder;
import com.chevron.thirdorder.model.RespFWOrder;
import com.chevron.thirdorder.model.RespProduct;
import com.chevron.thirdorder.model.RespXBOrder;
import com.chevron.thirdorder.service.OrderForThirdOrderService;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.HttpSessionGets;
import com.common.util.JsonUtil;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.dao.WxtValueTransformMapVoMapper;
import com.sys.auth.model.WxTUser;
import com.sys.log.model.Log;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.properties.service.WxTPropertiesService;
import com.sys.quartz.business.ScanOrderForConfirmService;

/**
 * 
 * @Author: bo.liu 2016-10-11 上午11:14:34
 * @Version: $Id$
 * @Desc:
 *        <p>
 *        提供三方订单接口实现类
 *        </p>
 */
@Service
public class OrderForThirdOrderServiceImpl implements OrderForThirdOrderService {
	public final Logger log = Logger.getLogger(this.getClass());

	@Resource
	private WebThirdOrderService webDDBXService;
	@Resource
	OrderVoMapper orderVoMapper;

	@Resource
	RegionVoMapper regionVoMapper;

	@Resource
	private WxTPropertiesService propertiesServiceImpl;

	@Resource
	VehicleService vehicleService;

	@Resource
	ProductVoMapper productVoMapper;
	@Resource
	OrderLineVoMapper orderLineMapper;
	@Resource
	OrganizationVoMapper organizationVoMapper;
//	@Resource
//	WorkShopVoMapper workShopVoMapper;
	@Resource
	WxtValueTransformMapVoMapper valueTransformMapVoMapper;
	@Resource
	O2oOrderService o2oOrderService;
	@Resource
	OrderBizService orderBizService;
	@Resource
	WxtOrderYYFWSetsVoMapper orderYYFWSetsMapper;
	@Resource
	private WxTPropertiesMapper propertiesMapper;
	@Resource
	private ScanOrderForConfirmService confirmService;
	@Resource
	private OrderVinCodeMapper orderVinCodeMapper;

	public static final String KEY_MSGVO = "msgVo";
	public static final String KEY_CODE = "code";
	public static final String KEY_MSG = "codeMsg";
	public static final String CODE_SUCCESS = "success";
	public static final String CODE_ERROR = "syserror";
	public static final String MSG_INVALID_DATA = "参数传递不能为null";
	public static final String MSG_SUCCESS = "操作成功";
	public static final String MSG_PLANTNO_ISNULL = "车牌号不能为null";
	public static final String PRE_ERROR_TIP = "错误信息：";
	public static final String PRE_EXCEPTION_TIP = "系统异常，您可以拨打电话 4006686065 联系管理员！";
	public static final int MAX_ORDER_AMOUNT = 5;

	public static final String KEY_ORDER_STATUS = "orderStatus";
	public static final String PROENDWITH = "0000";

	public static final String ZJ_REGION_NAME = "浙江省";
	public static final String HZ_REMARK = "faywage";
	public static final String APP_URL = "www.cvx-sh.com";
	public static final String BOX_ = "箱";
	public static final String BOTTLE_ = "瓶";
	public static final String GE_ = "个";

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> createXBOrder(List<OrderXBVo> lstOderXB, String orderSourceType) {
		log.info("------createXBOrder:" + JsonUtil.writeValue(lstOderXB));
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		Map<String, Object> respMap = new HashMap<String, Object>();
		MessageVo<OrderCommonVo> msgVo = new MessageVo<OrderCommonVo>();
		// 0.验证数据合法性
		Map<String, Object> checkResultMap0 = checkParamsForOrder(lstOderXB, orderSourceType,
				OrderCommonVo.ORDER_TYPE_DDXB);
		if (null != checkResultMap0) {
			return checkResultMap0;
		}
		String orderStatus = "";
		String source = "";
		String sourceId = "";
		String orderType = "DP";
		String settlementModelType = OrderVo.SETTLEMENTMODELTYPE1;
		orderType = OrderCommonVo.ORDER_TYPE_DDXB;
		Long spid = 0L;
		if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[0]))// 大地
		{
			reqMap.put("orgName", OrderCommonVo.ORDER_SP[0]);
			orderStatus = OrderCommonVo.ORDER_STATUS[3];
			source = OrderCommonVo.ORDER_SOURCE_DDBX_VALUE;
			sourceId = OrderCommonVo.ORDER_SOURCE_DDBX_VALUE + "1";
		} else if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[2]))// 易修车
		{
			reqMap.put("orgName", OrderCommonVo.ORDER_SP[1]);
			orderStatus = OrderCommonVo.ORDER_STATUS[0];// mod by bo.liu 2017.10.19 OrderCommonVo.ORDER_STATUS[0]
			source = OrderCommonVo.ORDER_SOURCE_YXC_VALUE;
			sourceId = OrderCommonVo.ORDER_SOURCE_YXC_VALUE + "1";
		}

		// 1.重新组装
		List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
		WxTUser curUser = ContextUtil.getCurUser();
		log.info("当前用户信息==================：" + curUser.getChName());
		try {
			spid = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
			for (OrderXBVo newOrUpdateOrderVo : lstOderXB) {
				OrderVo orderVo = new OrderVo();
				// 校验参数
				Map<String, Object> checkResultMap = checkParamsForCreateDDXBOrder(newOrUpdateOrderVo);
				if (null == (checkResultMap.get("code"))) {
					return checkResultMap;
				}
				orderVo.setCreateTime(new Date());
				orderVo.setUpdateTime(new Date());
				if (null != curUser) {
					orderVo.setCreator(curUser.getUserId() + "");
				}
				// 查询传递的区域code，获取区域名称
				String regionCode = newOrUpdateOrderVo.getRegionCode();
				if (null == regionCode || regionCode.equals("")) {
					msgVo.setCode(MessageContants.REGIONCODE_IS_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.REGIONCODE_IS_ERROR_MSG);
					resultMap.put(KEY_MSGVO, msgVo);
					return resultMap;
				}
				RegionVo regionVo = regionVoMapper.selectByRegionCodenew(regionCode);
				if (null == regionVo) {
					msgVo.setCode(MessageContants.REGIONCODE_IS_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.REGIONCODE_IS_ERROR_MSG);
					resultMap.put(KEY_MSGVO, msgVo);
					return resultMap;
				}
				String regionName = regionVo.getRegionName();
				// delete by bo.liu 0905 start
				// String sequenceNo =
				// propertiesServiceImpl.getSequenceByType(SequenceTypes.NEW_CONFIRMED_ORDER,6,1);
				// orderVo.setOrderNo(CommonUtil.generateOrderCode(0)+sequenceNo);
				// delete by bo.liu 0905 end

				String cardType = newOrUpdateOrderVo.getCardType();
				if (null == cardType || cardType.isEmpty()) {
					msgVo.setCode(MessageContants.CARDTYPE_DO_NOT_NULL_CODE);
					msgVo.setCodeMsg(
							MessageContants.CARDTYPE_DO_NOT_NULL_MSG + " " + newOrUpdateOrderVo.getPlateNumber());
					resultMap.put(KEY_MSGVO, msgVo);
					return resultMap;
				}
				Integer xbQuantity = newOrUpdateOrderVo.getXbQuantity();
				if (null == xbQuantity || xbQuantity == 0) {
					msgVo.setCode(MessageContants.XBBUYTIMES_IS_INVALID_CODE);
					msgVo.setCodeMsg(
							MessageContants.XBBUYTIMES_IS_INVALID_MSG + " " + newOrUpdateOrderVo.getPlateNumber());
					resultMap.put(KEY_MSGVO, msgVo);
					return resultMap;
				}
				orderVo.setOrderType(orderType);
				orderVo.setSource(source);
				orderVo.setSourceId(sourceId);
				if (null != newOrUpdateOrderVo.getPreferentialTypePrice()) {
					orderVo.setTotalOrderPrice(Double.parseDouble(newOrUpdateOrderVo.getPreferentialTypePrice()));
				}
				orderVo.setStatus(orderStatus);// 已下单 或待确认
				orderVo.setBuyerCardId(newOrUpdateOrderVo.getVinCode());// vin码指定给
				orderVo.setBuyUserName(newOrUpdateOrderVo.getBuyUserName());
				orderVo.setPhoneNo(newOrUpdateOrderVo.getPhoneNo());
				orderVo.setPlateNumber(newOrUpdateOrderVo.getPlateNumber());
				orderVo.setCarType(newOrUpdateOrderVo.getCarType());
				orderVo.setLevelId(newOrUpdateOrderVo.getLevelId());
				orderVo.setVinCode(newOrUpdateOrderVo.getVinCode());
				orderVo.setRegionName(regionName);
				orderVo.setEffectiveTime(newOrUpdateOrderVo.getEffectiveTime());
				orderVo.setInvalidTime(newOrUpdateOrderVo.getInvalidTime());
				// orderVo.setType(newOrUpdateOrderVo.getType());
				orderVo.setCouponCardType(newOrUpdateOrderVo.getCardType());
				orderVo.setCardType(newOrUpdateOrderVo.getCardType());
				orderVo.setQuantity(newOrUpdateOrderVo.getXbQuantity());
				orderVo.setPreferentialTypePrice(newOrUpdateOrderVo.getPreferentialTypePrice());
				orderVo.setRemark(newOrUpdateOrderVo.getRemark());
				orderVo.setPoOrderPartnerId(spid);
				orderVo.setSettlementModelType(settlementModelType);
				orderVo.setRegionId(Long.parseLong(regionCode));
				lstNewOrderVo.add(orderVo);
			}
			// 2.创建新保订单
			respMap = webDDBXService.reCreateXBOrder(lstNewOrderVo, orderSourceType);
			String respCode = (String) respMap.get("code");
			if (!respCode.equals(MessageContants.SUCCESS_CODE)) {
				msgVo.setCode(respCode);
				msgVo.setCodeMsg("" + respMap.get("errorMsg").toString());
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}
		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			msgVo.setCode(MessageContants.EXCEPTION_CODE);
			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + e.getMessage());
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		msgVo.setCode(MessageContants.SUCCESS_CODE);
		msgVo.setCodeMsg(MessageContants.SUCCESS_MSG);
		// 3.返回订单列表，，订单号和对应的车牌号 add by bo.liu 20170208
		List<OrderCommonVo> returnlstOrders = new ArrayList<OrderCommonVo>();
		List<OrderVo> lstOrderVo = extracted(respMap);
		for (OrderVo orderVo : lstOrderVo)// lstNewOrderVo
		{
			OrderCommonVo orderCommonVo = new OrderCommonVo();
			orderCommonVo.setOrderCode(orderVo.getOrderNo());
			orderCommonVo.setPlateNumber(orderVo.getPlateNumber());
			orderCommonVo.setType(orderVo.getType());
			orderCommonVo.setOilType(orderVo.getOilInjection());
			orderCommonVo.setCarType(orderVo.getCarType());// add by bo.liu 1107
			List<OrderLineVo> lstOrderLines = orderVo.getOrderLines();
			if (null != lstOrderLines && !lstOrderLines.isEmpty()) {
				List<OrderSkuInfo> lstSkuInfos = new ArrayList<OrderSkuInfo>();
				for (OrderLineVo orderLine : lstOrderLines) {
					OrderSkuInfo orderSkuInfo = new OrderSkuInfo();
					orderSkuInfo.setOrderId(orderLine.getOrderId());
					orderSkuInfo.setSku(orderLine.getSku());
					orderSkuInfo.setName(orderLine.getProductName());
					orderSkuInfo.setQuantity(orderLine.getAmount());
					orderSkuInfo.setUnits(orderLine.getUnits());
					lstSkuInfos.add(orderSkuInfo);
				}
				orderCommonVo.setLstSkuInfos(lstSkuInfos);
			}
			returnlstOrders.add(orderCommonVo);
		}
		msgVo.setDataList(returnlstOrders);
		// resultMap.put("lstOrderVo", respMap.get("lstOrderVo"));
		resultMap.put(KEY_MSGVO, msgVo);
		return resultMap;
	}

	private List<OrderVo> extracted(Map<String, Object> respMap) {
		List<OrderVo> list = (ArrayList<OrderVo>) respMap.get("lstOrderVo");
		return list;
	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> createFWOrder(List<OrderFWVo> lstOrderDDFW, String orderSourceType) {
//		log.info("------createFWOrder:" + JsonUtil.writeValue(lstOrderDDFW));
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		MessageVo<OrderCommonVo> msgVo = new MessageVo<OrderCommonVo>();
//		// 0.验证数据合法性
//		Map<String, Object> checkResultMap0 = checkParamsForOrder(lstOrderDDFW, orderSourceType,
//				OrderCommonVo.ORDER_TYPE_DDFW);
//		if (null != checkResultMap0) {
//			return checkResultMap0;
//		}
//		String orderStatus = OrderCommonVo.ORDER_STATUS[4];
//		String orderType = "DA";
//		orderType = OrderCommonVo.ORDER_TYPE_DDFW;
//		if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[0]))// 大地
//		{
//			orderStatus = OrderCommonVo.ORDER_STATUS[4];
//		} else if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[2]))// 易修车
//		{
//			orderStatus = OrderCommonVo.ORDER_STATUS[4];
//		}
//
//		List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
//		WxTUser curUser = ContextUtil.getCurUser();
//		try {
//			if (null == lstOrderDDFW || lstOrderDDFW.isEmpty()) {
//				msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
//				msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
//				resultMap.put(KEY_MSGVO, msgVo);
//				return resultMap;
//			}
//
//			if (lstOrderDDFW.size() > MAX_ORDER_AMOUNT) {
//				msgVo.setCode(MessageContants.ORDER_AMOUNT_IS_MAX_CODE);
//				msgVo.setCodeMsg(MessageContants.ORDER_AMOUNT_IS_MAX_MSG);
//				resultMap.put(KEY_MSGVO, msgVo);
//				return resultMap;
//			}
//			// 1.重新组装
//			if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[0])
//					|| orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[2])) {
//				for (OrderFWVo newOrUpdateOrderVo : lstOrderDDFW) {
//					OrderVo orderVo = new OrderVo();
//					// 校验参数
//					String deliveryPlateNumber = newOrUpdateOrderVo.getDeliveryPlateNumber();
//					if (null == deliveryPlateNumber || deliveryPlateNumber.isEmpty()) {
//						msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
//						msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
//						resultMap.put(KEY_MSGVO, msgVo);
//						return resultMap;
//					}
//					Map<String, Object> checkResultMap = checkParamsForCreateDDFWOrder(newOrUpdateOrderVo);
//					if (null == (checkResultMap.get("code"))) {
//						return checkResultMap;
//					}
//
//					orderVo.setRemark(newOrUpdateOrderVo.getSendRemark());
//					if (null != curUser) {
//						orderVo.setCreator(curUser.getUserId() + "");
//					}
//					// 查询传递的区域code，获取区域名称
//					String regionCode = newOrUpdateOrderVo.getReceiveRegionCode();
//					RegionVo regionVo = regionVoMapper.selectByRegionCodenew(regionCode);
//					if (null == regionVo) {
//						msgVo.setCode(MessageContants.REGIONCODE_IS_ERROR_CODE);
//						msgVo.setCodeMsg(MessageContants.REGIONCODE_IS_ERROR_MSG);
//						resultMap.put(KEY_MSGVO, msgVo);
//						return resultMap;
//					}
//					String regionName = regionVo.getRegionName();
//					orderVo.setPlateNumber(newOrUpdateOrderVo.getDeliveryPlateNumber());
//					orderVo.setOrderType(orderType);
//					orderVo.setStatus(orderStatus);// 待发货
//					orderVo.setUpdateTime(new Date());
//					orderVo.setCreateTime(new Date());
//
//					orderVo.setServiceTime(newOrUpdateOrderVo.getServiceTime());
//					orderVo.setWorkshopName(newOrUpdateOrderVo.getWorkshopName());
//					orderVo.setReceiveUserName(newOrUpdateOrderVo.getReceiveUserName());
//					orderVo.setReceivePhoneNo(newOrUpdateOrderVo.getReceivePhoneNo());
//					orderVo.setReceiveRegionName(newOrUpdateOrderVo.getReceiveRegionName());
//					orderVo.setRegionName(regionName);
//					orderVo.setAddress(newOrUpdateOrderVo.getAddress());
//					orderVo.setSendRemark(newOrUpdateOrderVo.getSendRemark());
//					orderVo.setRegionId(Long.parseLong(regionCode));
//					lstNewOrderVo.add(orderVo);
//				}
//			} else if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_12_TYPE)) {
//				for (OrderFWVo newOrUpdateOrderVo : lstOrderDDFW) {
//					String orderNo = newOrUpdateOrderVo.getOrderNo();
//					String plateNo = newOrUpdateOrderVo.getDeliveryPlateNumber();
//					List<OrderFWLineVo> lstOrderFWLineVo = newOrUpdateOrderVo.getOrderLineVoLst();
//					Date serviceTime = newOrUpdateOrderVo.getServiceTime();
//					String workshopName = newOrUpdateOrderVo.getWorkshopName();
//					String receiveUser = newOrUpdateOrderVo.getReceiveUserName();
//					String receivePhoneNo = newOrUpdateOrderVo.getReceivePhoneNo();
//					String receiveRegion = newOrUpdateOrderVo.getReceiveRegionName();
//					String receiveAddress = newOrUpdateOrderVo.getAddress();
//					String neworderSource = newOrUpdateOrderVo.getOrderSource();
//					String remarkStr = newOrUpdateOrderVo.getSendRemark();
//					if (null == neworderSource || neworderSource.equals("")) {
//						neworderSource = OrderCommonVo.ORDER_SOURCE_12_VALUE;
//					}
//					if (!neworderSource.equals(OrderCommonVo.ORDER_SOURCE_12_VALUE)) {
//						msgVo.setCode(MessageContants.ORDER_SOURCE_ERROR_CODE);
//						msgVo.setCodeMsg(MessageContants.ORDER_SOURCE_ERROR_MSG);
//						resultMap.put(KEY_MSGVO, msgVo);
//						return resultMap;
//					}
//
//					String orderSourceId = neworderSource + "_01";
//					if (null == orderNo || orderNo.equals("")) {
//						msgVo.setCode(MessageContants.ORDER_NO_IS_NULL_CODE);
//						msgVo.setCodeMsg(MessageContants.ORDER_NO_IS_NULL_MSG);
//						resultMap.put(KEY_MSGVO, msgVo);
//						return resultMap;
//					}
//					if (null == lstOrderFWLineVo || lstOrderFWLineVo.isEmpty()) {
//						msgVo.setCode(MessageContants.ORDERLINE_IS_NULL_CODE);
//						msgVo.setCodeMsg(MessageContants.ORDERLINE_IS_NULL_MSG + "订单编号：" + orderNo);
//						resultMap.put(KEY_MSGVO, msgVo);
//						return resultMap;
//					}
//
//					// 校验参数
//					Map<String, Object> checkResultMap = checkParamsForCreateDDFWOrder(newOrUpdateOrderVo);
//					if (null == (checkResultMap.get("code"))) {
//						return checkResultMap;
//					}
//					OrderVo orderVo = new OrderVo();
//					List<OrderLineVo> lstProduct = new ArrayList<OrderLineVo>();
//					for (OrderFWLineVo orderFWLineVo : lstOrderFWLineVo) {
//						String productSku = orderFWLineVo.getOilSku();
//						int productAmount = orderFWLineVo.getOilQuantity();
//						if (null == productSku || productSku.equals("") || productAmount == 0) {
//							msgVo.setCode(MessageContants.PRODUCT_PARAM_IS_NULL_CODE);
//							msgVo.setCodeMsg(MessageContants.PRODUCT_PARAM_IS_NULL_MSG);
//							resultMap.put(KEY_MSGVO, msgVo);
//							return resultMap;
//						}
//
//						OrderLineVo orderLineVo = new OrderLineVo();
//						orderLineVo.setSku(orderFWLineVo.getOilSku());
//						orderLineVo.setAmount(orderFWLineVo.getOilQuantity());
//
//						lstProduct.add(orderLineVo);
//					}
//
//					orderVo.setRemark(remarkStr);
//					if (null != curUser) {
//						orderVo.setCreator(curUser.getUserId() + "");
//					}
//					orderVo.setOrderNo(orderNo);
//					orderVo.setPlateNumber(plateNo);
//					orderVo.setOrderType(orderType);
//					orderVo.setStatus(orderStatus);// 待发货
//					orderVo.setUpdateTime(new Date());
//					orderVo.setCreateTime(new Date());
//					orderVo.setSource(neworderSource);
//					orderVo.setSourceId(orderSourceId);
//
//					orderVo.setServiceTime(serviceTime);
//					orderVo.setWorkshopName(workshopName);
//					orderVo.setReceiveUserName(receiveUser);
//					orderVo.setReceivePhoneNo(receivePhoneNo);
//					orderVo.setReceiveRegionName(receiveRegion);
//					orderVo.setRegionName(receiveRegion);
//					orderVo.setAddress(receiveAddress);
//					orderVo.setSendRemark(remarkStr);
//					orderVo.setOrderLines(lstProduct);
//					orderVo.setRegionId(newOrUpdateOrderVo.getReceiveRegionCode() == null ? null
//							: Long.parseLong(newOrUpdateOrderVo.getReceiveRegionCode()));
//					lstNewOrderVo.add(orderVo);
//				}
//			} else {
//				msgVo.setCode(MessageContants.ORDER_SOURCE_IS_ERROR_CODE);
//				msgVo.setCodeMsg(MessageContants.ORDER_SOURCE_IS_ERROR_MSG);
//				resultMap.put(KEY_MSGVO, msgVo);
//				return resultMap;
//			}
//			// 2.创建服务订单
//			Map<String, Object> respMap = new HashMap<String, Object>();
//			if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_DDBX_TYPE)) {
//				respMap = webDDBXService.reCreateFWOrder(lstNewOrderVo, orderSourceType);
//			} else if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_12_TYPE)) {
//				respMap = webDDBXService.createFWOrderFor12(lstNewOrderVo);
//			} else// 其他服务订单
//			{
//				respMap = webDDBXService.reCreateFWOrder(lstNewOrderVo, orderSourceType);
//			}
//			String respCode = (String) respMap.get("code");
//			if (!respCode.equals(MessageContants.SUCCESS_CODE)) {
//				msgVo.setCode(respCode);
//				msgVo.setCodeMsg("" + respMap.get("errorMsg").toString());
//				resultMap.put(KEY_MSGVO, msgVo);
//				return resultMap;
//			}
//
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + ex.getLocalizedMessage());
//			msgVo.setCode(MessageContants.EXCEPTION_CODE);
//			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + ex.getMessage());
//			resultMap.put(KEY_MSGVO, msgVo);
//			return resultMap;
//
//		}
//		msgVo.setCode(MessageContants.SUCCESS_CODE);
//		msgVo.setCodeMsg(MessageContants.SUCCESS_MSG);
//		// 3.返回订单列表，，订单号和对应的车牌号 add by bo.liu 20170209
//		List<OrderCommonVo> returnlstOrders = new ArrayList<OrderCommonVo>();
//		for (OrderVo orderVo : lstNewOrderVo) {
//			OrderCommonVo orderCommonVo = new OrderCommonVo();
//			orderCommonVo.setOrderCode(orderVo.getOrderNo());
//			orderCommonVo.setPlateNumber(orderVo.getPlateNumber());
//			returnlstOrders.add(orderCommonVo);
//		}
//		msgVo.setDataList(returnlstOrders);
//		resultMap.put(KEY_MSGVO, msgVo);
//		return resultMap;
//	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> cancelOrder(List<OrderCommonVo> lstOrder) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		log.info("=====cancelOrder:" + JsonUtil.writeValue(lstOrder));
//		MessageVo<Object> msgVo = new MessageVo<Object>();
//		List<OrderVo> lstOrders = new ArrayList<OrderVo>();// 存放大地新保订单类型
//		try {
//			// 校验参数
//			Map<String, Object> checkResultMap = checkParamsForModifyOrCancelOrder(lstOrder, "1");
//			if (null == (checkResultMap.get("code"))) {
//				return checkResultMap;
//			}
//			// add by bo.liu 1201
//			List<String> lstOrderCodes = new ArrayList<String>();
//			for (OrderCommonVo orderDDBXCommonVo : lstOrder) {
//				String plateNumber = orderDDBXCommonVo.getPlateNumber();
//				OrderVo orderVo = new OrderVo();
//				String orderCode = orderDDBXCommonVo.getOrderCode();
//				if (null == orderCode || orderCode.trim().equals("")) {
//					msgVo.setCode(MessageContants.ORDER_NO_IS_NULL_CODE);
//					msgVo.setCodeMsg(MessageContants.ORDER_NO_IS_NULL_MSG);
//					resultMap.put(KEY_MSGVO, msgVo);
//					return resultMap;
//				}
//				lstOrderCodes.add(orderCode);
//				orderVo.setOrderNo(orderCode.trim());
//				orderVo.setWorkshopName(orderDDBXCommonVo.getWorkshopName());
//				orderVo.setAddress(orderDDBXCommonVo.getReceiveAddress());
//				orderVo.setReceiveUserName(orderDDBXCommonVo.getReceivePerson());
//				orderVo.setReceivePhoneNo(orderDDBXCommonVo.getReceivePhoneNo());
//				Date serviceDate = null;
//				if (null != orderDDBXCommonVo.getServiceTime()) {
//					orderVo.setServiceTime(
//							new SimpleDateFormat("yyyyMMddHHmmss").parse(orderDDBXCommonVo.getServiceTime()));
//					orderVo.setOrderType(orderDDBXCommonVo.getOrderType());
//					String serviceTimeStr = orderDDBXCommonVo.getServiceTime();
//					serviceDate = DateUtils.parseDate(serviceTimeStr, 9);
//				}
//				if (null != serviceDate) {
//					orderVo.setServiceTime(serviceDate);
//				}
//				orderVo.setOrderType(orderDDBXCommonVo.getOrderType());
//				orderVo.setStatus("6");// 设置状态为已取消状态
//				lstOrders.add(orderVo);
//			}
//			// add by bo.liu 1201 加强验证功能，如果对应订单的车牌存在服务订单就不能执行新保订单的取消操作
//			Map<String, Object> checkResultMap2 = checkIsCancelOrders(lstOrderCodes);
//			if (null != checkResultMap2) {
//				return checkResultMap2;
//			}
//
//			Map<String, Object> reqMap = new HashMap<String, Object>();
//			reqMap.put("lstOrders", lstOrders);
//
//			msgVo = webDDBXService.updateOrderByOrderNo(reqMap);
//			resultMap.put(KEY_MSGVO, msgVo);
//		} catch (Exception ex) {
//			log.error("", ex);
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + ex.getLocalizedMessage());
//			msgVo.setCode(MessageContants.EXCEPTION_CODE);
//			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + ex.getMessage());
//			resultMap.put(KEY_MSGVO, msgVo);
//			return resultMap;
//		}
//
//		return resultMap;
//	}

	/**
	 * 用于校验是否可取取消新保订单，用于易修车，如果存在服务订单就不能再取消新保订单操作
	 * 
	 * <AUTHOR> 2017-12-1 下午5:11:03
	 * @param lstOrderCodes
	 * @return
	 */
	private Map<String, Object> checkIsCancelOrders(List<String> lstOrderCodes) {
		// add by bo.liu 1201根据订单号获取对于订单的车牌号
		Map<String, Object> resultMap = new HashMap<String, Object>();
		MessageVo<Object> msgVo = new MessageVo<Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("orderCodes", lstOrderCodes);
		List<OrderVo> lsttmpOrders1 = orderVoMapper.getOrdersByOrderCodes(reqMap);
		if (null == lsttmpOrders1 || lsttmpOrders1.isEmpty()) {
			msgVo.setCode(MessageContants.ORDER_IS_NOT_FOUND_CODE);
			msgVo.setCodeMsg(MessageContants.ORDER_IS_NOT_FOUND_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		// add by bo.liu 1201 获取新保类型订单的取消
		List<String> lstPlateNumbers = new ArrayList<String>();
		Map<String, String> tmpOrderMap = new HashMap<String, String>();

		List<String> returnOrdercodes = new ArrayList<String>();
		for (OrderVo tmpOrder1 : lsttmpOrders1) {
			String ordertype1 = tmpOrder1.getOrderType();
			String platenumber1 = tmpOrder1.getPlateNumber();
			String orderNo1 = tmpOrder1.getOrderNo();
			int servicetimes = tmpOrder1.getServiceAcount();
			int remainingservicetimes = tmpOrder1.getRemainingServiceTimes();
			String orderCode = tmpOrder1.getOrderNo();
			if (null != platenumber1 && !platenumber1.isEmpty()) {
				if (null != ordertype1 && ordertype1.equals(OrderCommonVo.ORDER_TYPE_DDXB)) {
					lstPlateNumbers.add(platenumber1);
					tmpOrderMap.put(platenumber1, orderNo1);
					if (servicetimes > remainingservicetimes) {
						returnOrdercodes.add(orderCode);
					}
				}
			}
		}
		/*
		 * //add by bo.liu 1201 判断对于新保订单对应的车牌号是否有服务订单 v1 if(null!=lstPlateNumbers &&
		 * !lstPlateNumbers.isEmpty()) { reqMap.put("tmpordertype",
		 * OrderCommonVo.ORDER_TYPE_DDFW); reqMap.put("tmpplantenumbers",
		 * lstPlateNumbers); reqMap.put("tmporderstatus",
		 * OrderCommonVo.ORDER_STATUS[6]); List<String> lstPlanteNumbers2 =
		 * orderVoMapper.getPlanteNumbersByConditions(reqMap);
		 * if(null!=lstPlanteNumbers2 && !lstPlanteNumbers2.isEmpty()) { List<String>
		 * resultOrderCodes = new ArrayList<String>(); for(String tp:lstPlanteNumbers2)
		 * { resultOrderCodes.add((String)tmpOrderMap.get(tp)); }
		 * msgVo.setCode(MessageContants.EXIST_FW_ORDER_DO_NOT_CANCEL_XBORDER_CODE);
		 * msgVo.setCodeMsg(MessageContants.EXIST_FW_ORDER_DO_NOT_CANCEL_XBORDER_MSG+
		 * resultOrderCodes.toString()); resultMap.put(KEY_MSGVO, msgVo); return
		 * resultMap; } }
		 */
		// mod by bo.liu 1204 v2
		if (null != returnOrdercodes && !returnOrdercodes.isEmpty()) {
			msgVo.setCode(MessageContants.EXIST_FW_ORDER_DO_NOT_CANCEL_XBORDER_CODE);
			msgVo.setCodeMsg(MessageContants.EXIST_FW_ORDER_DO_NOT_CANCEL_XBORDER_MSG + returnOrdercodes.toString());
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		return null;
	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> modifyOrder(List<OrderCommonVo> lstOrder, String orderType) {
//		log.info("=====modifyOrder:" + JsonUtil.writeValue(lstOrder));
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		MessageVo<Object> msgVo = new MessageVo<Object>();
//		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
//		try {
//			// 校验参数
//			Map<String, Object> checkResultMap = checkParamsForModifyOrCancelOrder(lstOrder, orderType);
//			if (null == (checkResultMap.get("code"))) {
//				return checkResultMap;
//			}
//			String plateNumber = null;
//			for (OrderCommonVo orderDDBXCommonVo : lstOrder) {
//				plateNumber = orderDDBXCommonVo.getPlateNumber();
//				// 0.组装数据到orderVO中
//				OrderVo orderVo = new OrderVo();
//
//				String orderCode = orderDDBXCommonVo.getOrderCode();
//				if (null == orderCode || orderCode.trim().equals("")) {
//					msgVo.setCode(MessageContants.ORDER_NO_IS_NULL_CODE);
//					msgVo.setCodeMsg(MessageContants.ORDER_NO_IS_NULL_MSG);
//					resultMap.put(KEY_MSGVO, msgVo);
//					return resultMap;
//				}
//				orderVo.setOrderNo(orderCode.trim());
//
//				if (orderType.equals(OrderVo.ORDER_TYPES[0]))// 新保订
//				{
//					orderVo.setBuyUserName(orderDDBXCommonVo.getReceivePerson());
//					orderVo.setPhoneNo(orderDDBXCommonVo.getReceivePhoneNo());
//					System.out.println("-1====================ljc:" + orderVo.getPhoneNo());
//
//				} else if (orderType.equals(OrderVo.ORDER_TYPES[1]))// 服务订单
//				{
//					orderVo.setWorkshopName(orderDDBXCommonVo.getWorkshopName());
//					orderVo.setAddress(orderDDBXCommonVo.getReceiveAddress());
//					orderVo.setReceiveUserName(orderDDBXCommonVo.getReceivePerson());
//					orderVo.setReceivePhoneNo(orderDDBXCommonVo.getReceivePhoneNo());
//					System.out.println("-2====================ljc:" + orderVo.getReceivePhoneNo());
//				}
//
//				lstOrders.add(orderVo);
//			}
//			// 1.执行更新操作
//			Map<String, Object> reqMap = new HashMap<String, Object>();
//			reqMap.put("lstOrders", lstOrders);
//			msgVo = webDDBXService.updateOrderByOrderNo(reqMap);
//			resultMap.put(KEY_MSGVO, msgVo);
//
//		} catch (Exception ex) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + ex.getLocalizedMessage());
//			msgVo.setCode(MessageContants.EXCEPTION_CODE);
//			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + ex.getMessage());
//			resultMap.put(KEY_MSGVO, msgVo);
//			return resultMap;
//		}
//		return resultMap;
//	}

	@Override
	public Map<String, Object> getBillNumberForThirdOrder(List<OrderCommonVo> lstOrder) {
		log.info("=====getBillNumberForThirdOrder:" + JsonUtil.writeValue(lstOrder));
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		MessageVo<OrderCommonVo> msgVo = new MessageVo<OrderCommonVo>();
		List<OrderCommonVo> returnlstOrders = new ArrayList<OrderCommonVo>();
		try {
			if (null == lstOrder || lstOrder.size() == 0) {
				msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
				msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			if (lstOrder.size() > MAX_ORDER_AMOUNT) {
				msgVo.setCode(MessageContants.ORDER_AMOUNT_IS_MAX_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_AMOUNT_IS_MAX_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			for (OrderCommonVo orderDDBXCommonVo : lstOrder) {
				String plateNumber = orderDDBXCommonVo.getPlateNumber();
				// modify by bo.liu 20170210 start
				String orderCode = orderDDBXCommonVo.getOrderCode();
				if (null == orderCode || "".equals(orderCode.trim())) {
					msgVo.setCode(MessageContants.ORDER_NO_IS_NULL_CODE);
					msgVo.setCodeMsg(MessageContants.ORDER_NO_IS_NULL_MSG);
					resultMap.put(KEY_MSGVO, msgVo);
					return resultMap;
				}
				reqMap.put("orderSourceType", OrderVo.ORDER_SOURCE_12_TYPE);
				reqMap.put("orderCode", orderCode);
				// modify by bo.liu 20170210 end

				reqMap.put("nowdate", new Date());
				reqMap.put("orderVoType", OrderVo.DDFW_ORDER_TYPE);// 针对服务订单返回billid
				reqMap.put("getBillId", "yes");// 设置一个billid 用于单独查询
				List<OrderVo> orderList = orderVoMapper.getOrdersByMoreConditionForDDBXInterface(reqMap);
				if (null != orderList && orderList.size() > 0) {
					OrderVo orderVo = orderList.get(0);
					String orderNo = orderVo.getOrderNo();
					String billId = orderVo.getBillId();

					log.info("======billId:" + billId);
					// 如果不为空，则组织回传的数据
					if (null != billId && !"".equals(billId)) {
						OrderCommonVo returnOrder = new OrderCommonVo();
						returnOrder.setOrderCode(orderNo);
						returnOrder.setBillNo(billId);
						returnOrder.setPlateNumber(plateNumber);
						returnlstOrders.add(returnOrder);

					} else {
						msgVo.setCode(MessageContants.BILL_ID_IS_NULL_CODE);
						msgVo.setCodeMsg(MessageContants.BILL_ID_IS_NULL_MSG + plateNumber);
						resultMap.put(KEY_MSGVO, msgVo);
						return resultMap;
					}
				} else {
					msgVo.setCode(MessageContants.ORDER_IS_NOT_FOUND_FOR_BILLID_CODE);
					msgVo.setCodeMsg(MessageContants.ORDER_IS_NOT_FOUND_FOR_BILLID_MSG + plateNumber);
					resultMap.put(KEY_MSGVO, msgVo);
					return resultMap;
				}
			}

		} catch (Exception ex) {
			log.info(PRE_EXCEPTION_TIP + ex.getLocalizedMessage());
			msgVo.setCode(MessageContants.EXCEPTION_CODE);
			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + ex.getMessage());
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		msgVo.setCode(MessageContants.SUCCESS_CODE);
		msgVo.setCodeMsg(MessageContants.SUCCESS_MSG);
		msgVo.setDataList(returnlstOrders);
		resultMap.put(KEY_MSGVO, msgVo);
		return resultMap;
	}

	@Override
	public Map<String, Object> getOrderStatus(String orderCode) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		MessageVo msgVo = new MessageVo();
		try {
			resultMap.put(KEY_ORDER_STATUS, null);
			// add by bo.liu 20170210 start
			if (null == orderCode || orderCode.trim().isEmpty()) {
				msgVo.setCode(MessageContants.ORDER_NO_IS_NULL_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_NO_IS_NULL_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}
			if (null != orderCode) {
				reqMap.put("orderNo", orderCode.trim());
			}
			// end

			reqMap.put("orderSourceType", "2");// reqMap.put("orderSourceType", orderSourceType); 都已订单号来进行获取
			// 由于大地保险可能获取多个订单，根据车牌号
			List<OrderVo> orderVoLst = orderVoMapper.getOrderStatusByCondition(reqMap);
			if (null == orderVoLst || orderVoLst.isEmpty()) {
				msgVo.setCode(MessageContants.ORDER_IS_NOT_FOUND_FOR_BILLID_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_IS_NOT_FOUND_FOR_BILLID_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			String status = null;
			OrderVo orderVo = orderVoLst.get(0);
			status = orderVo.getStatus();
			if (null == status) {
				msgVo.setCode(MessageContants.ORDER_STATUS_IS_NULL_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_STATUS_IS_NULL_CODE + orderVo.getOrderNo());
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			if (status.equals("3")) {// 已下单
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_3);
			} else if (status.equals("4")) {// 待发货
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_4);
			} else if (status.equals("5")) {// 已发货
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_5);
			} else if (status.equals("6")) {// 已取消
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_6);
			} else if (status.equals("7")) {
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_7);
			} else if (status.equals("0")) {
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_0);
			} else if (status.equals("1")) {
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_1);
			} else if (status.equals("9")) {// 已拒绝
				resultMap.put(KEY_ORDER_STATUS, MessageContants.ORDER_STATUS_9);
			}

		} catch (Exception ex) {
			log.info(PRE_EXCEPTION_TIP + ex.getLocalizedMessage());
			msgVo.setCode(MessageContants.EXCEPTION_CODE);
			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + ex.getMessage());
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;

		}
		msgVo.setCode(MessageContants.SUCCESS_CODE);
		msgVo.setCodeMsg(MessageContants.SUCCESS_MSG);
		resultMap.put(KEY_MSGVO, msgVo);
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> addProductsForOrder(String orderCode, List<OrderProductVo> lstProducts,
			List<OrderSkuInfo> lstOrderOilProducts) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		MessageVo<Object> msgVo = new MessageVo<Object>();
		WxTUser currentUser = ContextUtil.getCurUser();
		Long mUserId = 0L;
		if (null != currentUser) {
			mUserId = currentUser.getUserId();
		}
		try {

			// 0.验证
			if (null == orderCode || orderCode.isEmpty()) {
				msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
				msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			// 1.查询订单
			reqMap.put("orderCode", orderCode);
			OrderVo order = orderVoMapper.getOrderByOrderNo(reqMap);
			if (null == order) {
				msgVo.setCode(MessageContants.ORDER_IS_NOT_EXITS_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_IS_NOT_EXITS_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			if (!order.getStatus().equals(OrderCommonVo.ORDER_STATUS[0])) {
				msgVo.setCode(MessageContants.ORDER_IS_NOT_SUPPORT_ADD_PRODUCT_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_IS_NOT_SUPPORT_ADD_PRODUCT_MSG);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}

			// 2.给对应订单新增机滤产品
			List<OrderLineVo> filterOrderLineVoLst = new ArrayList<OrderLineVo>();
			if (null != lstProducts && !lstProducts.isEmpty()) {

				for (OrderProductVo orderProduct : lstProducts) {

					OrderLineVo orderLinenew = new OrderLineVo();
					orderLinenew.setProductName(orderProduct.getName());
					orderLinenew.setType(OrderCommonVo.ORDER_LINE_OIL_FILTER_TYPE);
					orderLinenew.setUnits(orderProduct.getUnits());
					orderLinenew.setCreateTime(new Date());
					orderLinenew.setUpdateTime(new Date());
					orderLinenew.setAmount(orderProduct.getQuantity());
					orderLinenew.setActualAmount(orderProduct.getQuantity());
					orderLinenew.setSku(orderProduct.getSku());
					orderLinenew.setStatus("1");
					orderLinenew.setCreator("" + mUserId);
					orderLinenew.setOrderId(order.getId());
					filterOrderLineVoLst.add(orderLinenew);

				}
				// orderLineMapper.insertOrderLineBatch(filterOrderLineVoLst);
			}

			// 3.录入确认后的机油数据
			List<OrderLineVo> oillstOrderLines = new ArrayList<OrderLineVo>();
			if (null != lstOrderOilProducts && !lstOrderOilProducts.isEmpty()) {
				// 录入之前先删掉之前的
				List<Long> orderLineOrderIds = new ArrayList<Long>();
				orderLineOrderIds.add(order.getId());
				reqMap.put("orderLineOrderIds", orderLineOrderIds);
				orderLineMapper.delteBatchOrderLineByOrderIds(reqMap);

				// 组装确认后的订单行信息
				List<String> tempSkus = new ArrayList<String>();
				for (OrderSkuInfo skuInfo : lstOrderOilProducts) {
					if (!tempSkus.contains(skuInfo.getSku())) {
						tempSkus.add(skuInfo.getSku());
					}
				}
				reqMap.put("skus", tempSkus);
				List<ProductVo> lstProduct = productVoMapper.selectProductByMap(reqMap);
				if (null == lstProduct || lstProduct.isEmpty()) {
					throw new Exception("确认机油失败，未找到合适的机油产品");
				}
				Map<String, Object> skuMap = new HashMap<String, Object>();
				for (ProductVo productTmp : lstProduct) {
					skuMap.put(productTmp.getSku(), productTmp);
				}

				for (OrderSkuInfo skuInfoNew : lstOrderOilProducts) {
					OrderLineVo orderLinenew = new OrderLineVo();
					ProductVo product = (ProductVo) skuMap.get(skuInfoNew.getSku());
					orderLinenew.setOrderId(order.getId());
					orderLinenew.setActualAmount(skuInfoNew.getQuantity());
					orderLinenew.setAmount(skuInfoNew.getQuantity());
					orderLinenew.setCapacity(product.getCapacity());
					orderLinenew.setSku(product.getSku());
					orderLinenew.setProductId(product.getId());
					orderLinenew.setUnits(product.getUnits());
					orderLinenew.setStatus("1");
					orderLinenew.setCreator("" + mUserId);
					orderLinenew.setCreateTime(new Date());
					orderLinenew.setUpdateTime(new Date());
					orderLinenew.setProductName(product.getName());
					orderLinenew.setType(product.getCategory());
					orderLinenew.setUnits(product.getUnits());
					orderLinenew.setPrice(product.getSalePrice());
					oillstOrderLines.add(orderLinenew);
				}
				// orderLineMapper.insertOrderLineBatch(oillstOrderLines);

			}
			// 录入确认后的机滤或机油产品
			List<OrderLineVo> allLstOrderLines = new ArrayList<OrderLineVo>();
			allLstOrderLines.addAll(oillstOrderLines);
			allLstOrderLines.addAll(filterOrderLineVoLst);
			if (null != allLstOrderLines && !allLstOrderLines.isEmpty()) {
				orderLineMapper.insertOrderLineBatch(allLstOrderLines);
			}

			// 4.更新订单状态为已确认
			OrderVo ordernew = new OrderVo();
			ordernew.setStatus(OrderCommonVo.ORDER_STATUS[3]);// 易修车已确认 //mod by bo.liu 20171024 直接修改为 已下单
																// OrderCommonVo.ORDER_STATUS[1]
			ordernew.setId(order.getId());
			orderVoMapper.updateByPrimaryKeySelective(ordernew);

		} catch (Exception ex) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.info(PRE_EXCEPTION_TIP + ex.getLocalizedMessage());
			msgVo.setCode(MessageContants.EXCEPTION_CODE);
			msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG + ex.getMessage());
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;

		}
		msgVo.setCode(MessageContants.SUCCESS_CODE);
		msgVo.setCodeMsg(MessageContants.SUCCESS_MSG);
		resultMap.put(KEY_MSGVO, msgVo);
		return resultMap;
	}

	// 验证start
	public Map<String, Object> checkParamsForOrder(List<?> lstOrder, String orderSourceType, String orderType) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		MessageVo<Object> msgVo = new MessageVo<Object>();
		boolean isOKOrderSourceType = false;
		for (int i = 0; i < OrderCommonVo.ORDER_SOURCE_TYPE.length; i++) {
			if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[i])) {
				isOKOrderSourceType = true;
			}
		}
		if (!isOKOrderSourceType) {
			msgVo.setCode(MessageContants.ORDER_SOURCE_IS_ERROR_CODE);
			msgVo.setCodeMsg(MessageContants.ORDER_SOURCE_IS_ERROR_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}

		if (orderType.equals(OrderCommonVo.ORDER_TYPE_DDXB)) {
			if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[1])
					|| orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[3])) {
				msgVo.setCode(MessageContants.ORDER_SERVICE_IS_NOT_SUPPORT_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_SERVICE_IS_NOT_SUPPORT_MSG + orderSourceType);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}
		} else if (orderType.equals(OrderCommonVo.ORDER_TYPE_DDFW)) {
			if (orderSourceType.equals(OrderCommonVo.ORDER_SOURCE_TYPE[3])) {
				msgVo.setCode(MessageContants.ORDER_SERVICE_IS_NOT_SUPPORT_CODE);
				msgVo.setCodeMsg(MessageContants.ORDER_SERVICE_IS_NOT_SUPPORT_MSG + orderSourceType);
				resultMap.put(KEY_MSGVO, msgVo);
				return resultMap;
			}
		}

		if (null == lstOrder || lstOrder.isEmpty()) {
			msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
			msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		if (lstOrder.size() > MAX_ORDER_AMOUNT) {
			msgVo.setCode(MessageContants.ORDER_AMOUNT_IS_MAX_CODE);
			msgVo.setCodeMsg(MessageContants.ORDER_AMOUNT_IS_MAX_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		return null;
	}

	public Map<String, Object> checkParamsForModifyOrCancelOrder(List<OrderCommonVo> lstOrder, String orderType) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		MessageVo msgVo = new MessageVo();
		if (null == lstOrder || lstOrder.isEmpty()) {
			msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
			msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		if (lstOrder.size() > MAX_ORDER_AMOUNT) {
			msgVo.setCode(MessageContants.ORDER_AMOUNT_IS_MAX_CODE);
			msgVo.setCodeMsg(MessageContants.ORDER_AMOUNT_IS_MAX_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}

		if (null == orderType) {
			msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
			msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}

		boolean isValidOrderType = false;
		for (String orderTypeTemp : OrderVo.ORDER_TYPES) {
			if (orderTypeTemp.equals(orderType)) {
				isValidOrderType = true;
			}
		}
		if (!isValidOrderType) {
			msgVo.setCode(MessageContants.ORDER_TYPE_IS_ERROR_CODE);
			msgVo.setCodeMsg(MessageContants.ORDER_TYPE_IS_ERROR_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		resultMap.put("code", "success");
		return resultMap;
	}

	public Map<String, Object> checkParamsForCreateDDFWOrder(OrderFWVo orderFW) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		MessageVo msgVo = new MessageVo();
		Date serviceTime = orderFW.getServiceTime();
		String workshopName = orderFW.getWorkshopName();
		String receiveUserName = orderFW.getReceiveUserName();
		String receivePhoneNo = orderFW.getReceivePhoneNo();
		String receiveRegionCode = orderFW.getReceiveRegionCode();
		String address = orderFW.getAddress();
		if (null == serviceTime || null == workshopName || workshopName.isEmpty() || null == receiveUserName
				|| receiveUserName.isEmpty() || null == receivePhoneNo || receivePhoneNo.isEmpty()
				|| null == receiveRegionCode || receiveRegionCode.isEmpty() || null == address || address.isEmpty()) {
			msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
			msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		resultMap.put("code", "success");
		return resultMap;
	}

	public Map<String, Object> checkParamsForCreateDDXBOrder(OrderXBVo orderXB) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		MessageVo<Object> msgVo = new MessageVo<Object>();
		String plateNumber = orderXB.getPlateNumber();
		Date invalidTime = orderXB.getInvalidTime();
		Date effectiveTime = orderXB.getEffectiveTime();
		String buyUserName = orderXB.getBuyUserName();
		String phoneNo = orderXB.getPhoneNo();
		String regionCode = orderXB.getRegionCode();
		String cardType = orderXB.getCardType();
		int quantity = orderXB.getXbQuantity();
		if (null == plateNumber || plateNumber.isEmpty() || null == invalidTime || null == effectiveTime
				|| null == buyUserName || buyUserName.isEmpty() || null == phoneNo || phoneNo.isEmpty()
				|| null == regionCode || regionCode.isEmpty() || 0 == quantity || null == cardType
				|| cardType.isEmpty()) {
			msgVo.setCode(MessageContants.PARAMS_NULL_CODE);
			msgVo.setCodeMsg(MessageContants.PARAMS_NULL_MSG);
			resultMap.put(KEY_MSGVO, msgVo);
			return resultMap;
		}
		resultMap.put("code", "success");
		return resultMap;
	}
	// 验证end

	@Override
	public Map<String, Object> getJosnDatasTest(List<?> lstOrders, String orderSource) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("lstOrder", lstOrders);
		resultMap.put("orderSource", orderSource);
		return resultMap;
	}

	@Override
	public Map<String, Object> getJosnDatasTest1(String orderCode, String plantNumber, String orderSource) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String levealId1 = vehicleService.getLevelIdByVinCode("LGBH12F229Y022885");
		String levealId2 = vehicleService.getLevelIdByVinCode("LSVNV2183D2277885");
		String levealId3 = vehicleService.getLevelIdByVinCode("LDC661244F3277235");
		System.out.println("---------------levealID1:" + levealId1 + "-----levealID2:" + levealId2 + "-----levealID3:"
				+ levealId3);
		resultMap.put("orderCode", orderCode);
		resultMap.put("plantNumber", plantNumber);
		resultMap.put("orderSource", orderSource);
		return resultMap;
	}

	@Override
	public Map<String, Object> getJosnDatasTestObj(Object obj) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("obj", obj);
		return resultMap;
	}
//
//	// not use
//	public void creteWorkshops(List<String> workshopNames, Map<String, Object> orderddyyfuMap, String creator,
//			String partnerId, String partnerName) throws Exception {
//
//		List<WorkShopVo> lstWorkShop = new ArrayList<WorkShopVo>();
//		for (String workshopname : workshopNames) {
//			WorkShopVo workShopVo = new WorkShopVo();
//			workShopVo.setWorkShopName(workshopname);
//			OrderDDYYFWVo tmporder = (OrderDDYYFWVo) orderddyyfuMap.get(workshopname);
//			workShopVo.setWorkShopAddress(tmporder.getWorkshopAdress());
//			workShopVo.setSource(OrderCommonVo.ORDER_SOURCE_DDYY_VALUE);
//			workShopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//			workShopVo.setReserveServiceTel(tmporder.getContactNumber());
//			workShopVo.setContactPerson(tmporder.getContactName());
//			workShopVo.setContactPersonTel(tmporder.getContactNumber());
//			workShopVo.setHarvestPerson(tmporder.getContactName());
//			workShopVo.setHarvestPersonTel(tmporder.getContactNumber());
//			workShopVo.setButtInChargePerson(tmporder.getContactName());
//			workShopVo.setButtInChargePersonTel(tmporder.getContactNumber());
//			workShopVo.setCreateTime(new Date());
//			workShopVo.setCreator(creator);
//			workShopVo.setPartnerId(partnerId);
//			workShopVo.setPartnerName(partnerName);
//			lstWorkShop.add(workShopVo);
//		}
//		workShopVoMapper.insertWorkShopBatch(lstWorkShop);
//	}

	// 大地保险订单接口新 start=================//
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> createNewXBOrder(ReqXBOrder xbOrder) {
		log.info("------createXBOrder:" + xbOrder);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Date nowDate = new Date();
		try {
			WxTUser curUser = ContextUtil.getCurUser();
			// 0.校验参数
			Map<String, Object> returnMap = checkXBOrder(xbOrder);
			if (null != returnMap) {
				return returnMap;
			}
			// 1.根据uid,查询最近的新保订单，且判断剩余服务次数是否使用完成
			/*
			 * List<OrderVo> lst = getServiceTimesByUid(xbOrder.getUid()); if(null!=lst &&
			 * !lst.isEmpty()) { OrderVo tmpOrder = lst.get(0);
			 * if(tmpOrder.getRemainingServiceTimes()>0) { resultMap.put(KEY_CODE,
			 * MessageContants.ORDER_XB_IS_EXIST_NULL_CODE); resultMap.put(KEY_MSG,
			 * MessageContants.ORDER_XB_IS_EXIST_NULL_MSG+xbOrder.getUid()); return
			 * resultMap; } }
			 */

			// 2.组装订单数据
			OrderVo order = new OrderVo();
			order.setPlateNumber(xbOrder.getUid());
			// 查询传递的区域code，获取区域名称
			Long regionCode = xbOrder.getRegionId();
			RegionVo regionVo = regionVoMapper.selectByRegionCodenew("" + regionCode);
			String regionName = "";
			if (null != regionVo) {
				regionName = regionVo.getRegionName();
			}
			order.setRegionName(regionName);
			order.setOilInjection(xbOrder.getOilInjectin());
			order.setCreateTime(nowDate);
			order.setUpdateTime(nowDate);
			order.setCreator("" + curUser.getUserId());
			order.setOrderType(OrderCommonVo.ORDER_TYPE_DDXB);
			order.setSource(OrderCommonVo.ORDER_SOURCE_DDBX_VALUE);
			order.setStatus(OrderCommonVo.ORDER_STATUS[3]);
			order.setSourceId(OrderCommonVo.ORDER_SOURCE_DDBX_VALUE + "1");
			order.setCarType(xbOrder.getCarType());
			order.setCardType(xbOrder.getCardType());
			order.setBuyerCardId(xbOrder.getLevelId());
			order.setLevelId(xbOrder.getLevelId());
			order.setSettlementModelType(OrderVo.SETTLEMENTMODELTYPE2);
			order.setCouponCardType(xbOrder.getCardType());
			order.setQuantity(xbOrder.getXbTimes());
			order.setRemark(xbOrder.getRemark());
			order.setRegionId(regionCode);
			Map<String, Object> reqMap = new HashMap<String, Object>();
			reqMap.put("orgName", OrderCommonVo.ORDER_SP[0]);
			Long spid = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
			order.setPoOrderPartnerId(spid);
			// 3.插入订单数据
			Map<String, Object> returnMap1 = webDDBXService.createApiXBOrder(order);
			OrderVo tmpOrder = (OrderVo) returnMap1.get("order");
			if (null == tmpOrder) {
				return returnMap1;
			}

			RespXBOrder respXBOrder = new RespXBOrder();
			respXBOrder.setUid(tmpOrder.getPlateNumber());
			respXBOrder.setOrderCode(tmpOrder.getOrderNo());
			List<RespProduct> respProducts = new ArrayList<RespProduct>();
			for (OrderLineVo tmpOrderLine : tmpOrder.getOrderLines()) {
				RespProduct respPro = new RespProduct();
				respPro.setName(tmpOrderLine.getProductName());
				respPro.setSku(tmpOrderLine.getSku());
				respPro.setQuantity(tmpOrderLine.getAmount());
				respPro.setUnits(tmpOrderLine.getUnits());
				respProducts.add(respPro);
			}
			respXBOrder.setLstProducts(respProducts);
			resultMap.put("respXBOrder", respXBOrder);
			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);

		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
		}
		return resultMap;
	}

	private List<OrderVo> getServiceTimesByUid(String uid) {
		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("uid", uid);
		reqMap.put("status", OrderCommonVo.ORDER_STATUS[3]);
		reqMap.put("ordertype", OrderCommonVo.ORDER_TYPE_DDXB);
		reqMap.put("source", OrderCommonVo.ORDER_SOURCE_DDBX_VALUE);
		reqMap.put("settlementType", OrderVo.SETTLEMENTMODELTYPE2);
		List<OrderVo> lstOrder = orderVoMapper.queryOrderByUid(reqMap);
		return lstOrder;
	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> createServFWOrder(ReqAddFWOrder fwOrder) {
//		log.info("------createServFWOrder:" + fwOrder);
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		// 创建日志
//		Log errorlog = new Log();
//		String logNo = "";
//		try {
//			WxTUser curUser = ContextUtil.getCurUser();
//			// 0.校验参数
//			log.info("------createServFWOrder checkFWOrder");
//			Map<String, Object> returnMap = checkFWOrder(fwOrder);
//			if (null != returnMap) {
//				return returnMap;
//			}
//			if (null == fwOrder.getVehicleNo() || fwOrder.getVehicleNo().trim().isEmpty()) {
//				fwOrder.setVehicleNo(null);
//			}
//
//			// 区域验证，及获取
//			log.info("------createServFWOrder selectByRegionCodenew2");
//			String sourceRegionCode = fwOrder.getReceiveRegionCode().trim();
//			RegionVo regionVo = regionVoMapper.selectByRegionCodenew2(sourceRegionCode);
//			if (null == regionVo) {
//
//				resultMap.put(KEY_CODE, MessageContants.REGIONCODE_IS_ERROR_CODE);
//				resultMap.put(KEY_MSG, MessageContants.REGIONCODE_IS_ERROR_MSG);
//				return resultMap;
//			}
//			String receiveRegionName = "";
//			// String addrPrex = "";
//			if (null != regionVo.getCityCode() && regionVo.getCityCode().endsWith(PROENDWITH)) {
//				receiveRegionName = regionVo.getCityName();
//				// addrPrex = receiveRegionName;
//			} else if (null != regionVo.getRegionCode() && regionVo.getRegionCode().endsWith(PROENDWITH)) {
//				receiveRegionName = regionVo.getRegionName();
//				// addrPrex = receiveRegionName+regionVo.getCityName();
//			} else if (null != regionVo.getProvinceCode() && regionVo.getProvinceCode().endsWith(PROENDWITH)) {
//				receiveRegionName = regionVo.getProvinceName();
//				// addrPrex = receiveRegionName+regionVo.getRegionName()+regionVo.getCityName();
//			} else {
//				resultMap.put(KEY_CODE, MessageContants.REGIONCODE_IS_ERROR_CODE);
//				resultMap.put(KEY_MSG, MessageContants.REGIONCODE_IS_ERROR_MSG);
//				return resultMap;
//			}
//			log.info("------createServFWOrder check is need to pushServFWOrderToFWG");
//			if (receiveRegionName.equals(ZJ_REGION_NAME) && !fwOrder.getRemark().equals(HZ_REMARK)) {
//				log.info("by bo.liu 1010 zj pushServFWOrderToFWG receiveRegionName:" + receiveRegionName);
//				String appUrl = (String) Constants.getSystemPropertyByCodeType(Constants.APP_HOST);
//				log.info("------createServFWOrder appUrl:" + appUrl);
//				if (appUrl.contains(APP_URL)) {
//					resultMap = webDDBXService.pushServFWOrderToFWG(fwOrder);
//				} else {
//					resultMap.put("code", MessageContants.SUCCESS_CODE);
//					resultMap.put("codeMsg", "非正式环境不需要推送到啡威格," + MessageContants.SUCCESS_MSG);
//				}
//				return resultMap;
//			}
//			// 创建日志，用于创建服务订单失败时候告警
//			StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
//			Long currentTimeM = System.currentTimeMillis();
//			String requestParams = "null";
//			if (null != fwOrder) {
//				requestParams = "fwOrder:" + fwOrder.toString();
//			}
//			logNo = "" + currentTimeM;
//			createLog(errorlog, logNo, fwOrder, requestParams, stackTrace);// mod by bo.liu 1027
//			// 1.获取最近一次服务包的订单，然后判断服务次数是否已经用完
//			// 录入到雪佛龙库中，判断是否存在新保订单
//			log.info("------createServFWOrder checkLatestFWBOrder");
//			Map<String, Object> checkFWTimesMap = checkLatestFWBOrder(fwOrder);
//			OrderVo latestOrderVo = (OrderVo) checkFWTimesMap.get("latestOrderVo");
//			if (null == latestOrderVo) {
//				errorlog.setExtProperty2(Log.ALARM_USER_EXCEPTION_TYPE + (String) checkFWTimesMap.get("codeMsg"));// 异常消息
//				// errorlog.setExtProperty5("异常信息："+(String)checkFWTimesMap.get("codeMsg"));//异常消息
//				webDDBXService.insertErrorLogMsg(errorlog);
//				return checkFWTimesMap;
//			}
//			// 2.组装订单数据
//			log.info("------createServFWOrder OrderVo");
//			OrderVo order = new OrderVo();
//			order.setStatus(OrderCommonVo.ORDER_STATUS[4]);// mod by bo.liu 1123 去掉定时任务的扫描 //每天5点后，定时任务扫描订单状态为2
//			order.setOrderType(OrderCommonVo.ORDER_TYPE_DDFW);
//			order.setSource(OrderCommonVo.ORDER_SOURCE_DDBX_VALUE);
//			order.setPlateNumber(latestOrderVo.getPlateNumber().trim());
//			order.setAddress(fwOrder.getReceiveAddr().trim());
//			order.setReceiveUserName(fwOrder.getReceiveUserName().trim() + " " + order.getPlateNumber());
//			order.setReceivePhoneNo(fwOrder.getReceiveUserTel().trim());
//			if (null != fwOrder.getRemark()) {
//				order.setRemark(fwOrder.getRemark().trim());
//			}
//			order.setCreator("" + curUser.getUserId());
//			order.setSourceId(OrderCommonVo.ORDER_SOURCE_DDBX_VALUE + "1");
//			order.setRegionName(receiveRegionName);
//			order.setReceiveRegionName(receiveRegionName);
//			order.setWorkshopName(fwOrder.getWorkshopName());
//			order.setServiceTime(DateUtils.parseDate(fwOrder.getServiceTime(), 0));
//			log.info("------createServFWOrder selectByOrganizationName");
//			Map<String, Object> reqMap = new HashMap<String, Object>();
//			reqMap.put("orgName", OrderCommonVo.ORDER_SP[0]);
//			Long partnerId = 0L;
//			if (null != organizationVoMapper.selectByOrganizationName(reqMap)
//					&& !organizationVoMapper.selectByOrganizationName(reqMap).isEmpty()) {
//				partnerId = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
//			}
//			String partnerName = OrderCommonVo.ORDER_SP[0];
//			if (null == partnerId || partnerId == 0) {
//				partnerId = OrderCommonVo.ORDER_SP_ID[0];
//			}
//			order.setOrderPartnerId(partnerId);
//			order.setOrderPartnerName(partnerName);
//			order.setPoOrderPartnerId(partnerId);
//			order.setCarType(latestOrderVo.getCarType());
//			order.setOilInjection(latestOrderVo.getOilInjection());
//			order.setCreateTime(new Date());
//			order.setUpdateTime(new Date());
//			order.setCardType(latestOrderVo.getCardType());
//			order.setServiceAcount(latestOrderVo.getServiceAcount());
//			order.setSettlementModelType(latestOrderVo.getSettlementModelType());
//			order.setRemark(fwOrder.getRemark() + " " + sourceRegionCode);
//			order.setRegionId(sourceRegionCode == null ? null : Long.parseLong(sourceRegionCode));
//
//			// 2.插入订单数据
//			log.info("------createServFWOrder webDDBXService.createServFWOrder");
//			Map<String, Object> returnMap1 = webDDBXService.createServFWOrder(order, latestOrderVo,
//					OrderCommonVo.ORDER_SOURCE_TYPE[0]);// webDDBXService.createServFWOrder(order);
//			OrderVo returnOrder = (OrderVo) returnMap1.get("order");
//			if (null == returnOrder) {
//				errorlog.setExtProperty2(Log.ALARM_USER_EXCEPTION_TYPE + (String) returnMap1.get("errorMsg"));// 异常消息
//				// errorlog.setExtProperty5("异常信息："+(String)returnMap1.get("errorMsg"));//异常消息
//				webDDBXService.insertErrorLogMsg(errorlog);
//				resultMap.put(KEY_CODE, (String) returnMap1.get("code"));
//				resultMap.put(KEY_MSG, (String) returnMap1.get("errorMsg"));
//				return resultMap;
//			}
//			// 3.更新订单服务次数
//			int remainingServiceTimes = latestOrderVo.getRemainingServiceTimes();
//			latestOrderVo.setRemainingServiceTimes(remainingServiceTimes - 1);
//			orderVoMapper.updateByNoSelective(latestOrderVo);
//			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);
//			resultMap.put("orderCode", returnOrder.getOrderNo());
//
//		} catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			e.printStackTrace();
//			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
//			errorlog.setExtProperty2(
//					Log.ALARM_SYSTEM_EXCEPTION_TYPE + ",可能存在网络繁忙出现的异常，请先检查是否存在此服务订单，详情请搜索日志文件关键字，日志编号:" + logNo);// 异常消息
//			// errorlog.setExtProperty3(Log.ALARM_SYSTEM_EXCEPTION_TYPE);
//			// errorlog.setExtProperty5("异常信息："+e.getLocalizedMessage());
//			webDDBXService.insertErrorLogMsg(errorlog);
//			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
//			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
//		}
//		return resultMap;
//	}

	public Log createLog(Log errorlog, String alarmName, ReqAddFWOrder reqFWOrder, String requestParmas,
			StackTraceElement[] stackTrace) {
		Long currentUserId = ContextUtil.getCurUserId();
		errorlog.setLogType(Log.FWORDER_API_TYPE);
		errorlog.setCreateTime(new Date());
		errorlog.setOperator(currentUserId);
		errorlog.setExtProperty1(
				"编号:" + alarmName + ";服务器:" + (String) Constants.getSystemPropertyByCodeType(Constants.APP_HOST)
						+ (String) HttpSessionGets.getRequest().getRequestURI() + ",服务方法:"
						+ stackTrace[1].getClassName() + "." + stackTrace[1].getMethodName());// 项目名称
		errorlog.setExtProperty4(reqFWOrder.getUid());
		errorlog.setExtProperty5(reqFWOrder.getVehicleNo());
		errorlog.setExtProperty6(reqFWOrder.getServiceTime());
		errorlog.setExtProperty7(reqFWOrder.getWorkshopName());
		errorlog.setExtProperty8(reqFWOrder.getReceiveUserName());
		errorlog.setExtProperty9(reqFWOrder.getReceiveUserTel());
		errorlog.setExtProperty10(reqFWOrder.getReceiveRegionCode());
		errorlog.setExtProperty11(reqFWOrder.getReceiveAddr());
		errorlog.setExtProperty12(reqFWOrder.getRemark());
		// errorlog.setExtProperty2(Log.ALARM_EXCEPTION_TITLE);//日志标题
		// errorlog.setExtProperty3(Log.ALARM_USER_EXCEPTION_TYPE);//告警类型
		// errorlog.setExtProperty4(stackTrace[1].getClassName()+"."+stackTrace[1].getMethodName());
		// errorlog.setExtProperty6((String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+(String)HttpSessionGets.getRequest().getRequestURI());
		// errorlog.setExtProperty7(requestParmas);
		return errorlog;
	}

	private Map<String, Object> checkLatestFWBOrder(ReqAddFWOrder fwOrder) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		// 查询服务包订单信息
		reqMap.put("plateNumber", fwOrder.getUid().trim());
		// reqMap.put("vehicleNo", fwOrder.getVehicleNo().trim());
		reqMap.put("status", "3");// 状态为已下单
		reqMap.put("orderVoType", OrderVo.DDXB_ORDER_TYPE);
		reqMap.put("nowdate", null);
		reqMap.put("orderSourceType", "1");
		reqMap.put("orderSource", OrderCommonVo.ORDER_SOURCE_DDBX_VALUE);
		// reqMap.put("ddxbnewflag", "Y");//剩余服务次数标志
		// add by bo.liu 1106 start
		List<OrderVo> lstDDXBOrders0 = null;
		lstDDXBOrders0 = orderVoMapper.getOrdersByMoreConditionForDDBXInterface(reqMap);
		if (null == lstDDXBOrders0 || lstDDXBOrders0.isEmpty()) {
			if (null == fwOrder.getVehicleNo() || fwOrder.getVehicleNo().isEmpty()) {
				resultMap.put("code", MessageContants.FWB_ORDER_IS_INVLID_CODE);
				resultMap.put("codeMsg", MessageContants.FWB_ORDER_IS_INVLID_MSG + fwOrder.getUid().trim());
				return resultMap;
			}

			reqMap.put("plateNumber", fwOrder.getVehicleNo().trim());
			lstDDXBOrders0 = orderVoMapper.getOrdersByMoreConditionForDDBXInterface(reqMap);
			if (null == lstDDXBOrders0 || lstDDXBOrders0.isEmpty()) {
				resultMap.put("code", MessageContants.FWB_ORDER_IS_INVLID_CODE);
				resultMap.put("codeMsg", MessageContants.FWB_ORDER_IS_INVLID_MSG + fwOrder.getUid().trim() + "或"
						+ fwOrder.getVehicleNo().trim());
				return resultMap;
			}
		}
		// add by bo.liu 1106 end

		// 获取新保订单（采购订单的）信息
		reqMap.put("ddxbnewflag", "Y");// 剩余服务次数标志
		List<OrderVo> lstDDXBOrders = null;
		lstDDXBOrders = orderVoMapper.getOrdersByMoreConditionForDDBXInterface(reqMap);
		if (null == lstDDXBOrders || lstDDXBOrders.isEmpty()) {
			if (null == fwOrder.getVehicleNo() || fwOrder.getVehicleNo().isEmpty()) {
				resultMap.put("code", MessageContants.FW_SERVER_TIMES_ORDER_IS_INVLID_CODE);
				resultMap.put("codeMsg", MessageContants.FW_SERVER_TIMES_ORDER_IS_INVLID_MSG + fwOrder.getUid().trim());
				return resultMap;
			}

			reqMap.put("plateNumber", fwOrder.getVehicleNo().trim());
			lstDDXBOrders = orderVoMapper.getOrdersByMoreConditionForDDBXInterface(reqMap);
			if (null == lstDDXBOrders || lstDDXBOrders.isEmpty()) {
				resultMap.put("code", MessageContants.FW_SERVER_TIMES_ORDER_IS_INVLID_CODE);
				resultMap.put("codeMsg", MessageContants.FW_SERVER_TIMES_ORDER_IS_INVLID_MSG + fwOrder.getUid().trim()
						+ "或" + fwOrder.getVehicleNo().trim());
				return resultMap;
			}
		}
		// 判断剩余服务次数是否大于0
		OrderVo latestOrderVo = lstDDXBOrders.get(0);
		// 获取剩余服务次数
		int remainingTimes = latestOrderVo.getRemainingServiceTimes();
		if (remainingTimes <= 0) {
			resultMap.put("code", MessageContants.FWORDER_IS_MAXTIMES_CODE);
			resultMap.put("codeMsg", MessageContants.FWORDER_IS_MAXTIMES_MSG + fwOrder.getUid().trim());
			return resultMap;
		}
		// 3个月不能服务，，
		/*
		 * if(remainingTimes>0) { Date nowDate = new Date(); Date latestOrderTime =
		 * latestOrderVo.getCreateTime(); int diffMonths =
		 * DateUtils.getMonthDiff(DateUtils.parseDate(DateUtils.toDateStrNew(nowDate,
		 * 5), 5), DateUtils.parseDate(DateUtils.toDateStrNew(latestOrderTime, 5), 5));
		 * if(diffMonths<3) { resultMap.put("code",
		 * MessageContants.FWORDER_DATE_IS_LESS_THAN_3MONTHS_CODE);
		 * resultMap.put("errorMsg",
		 * MessageContants.FWORDER_DATE_IS_LESS_THAN_3MONTHS_MSG+fwOrder.getUid().trim()
		 * +MessageContants.FWORDER_DATE_IS_LESS_THAN_3MONTHS_MSG1+DateUtils.
		 * toDateStrNew(latestOrderTime, 5)); return resultMap; } }
		 */
		resultMap.put("latestOrderVo", latestOrderVo);
		return resultMap;
	}

	@Override
	public Map<String, Object> getOrderBillNo(String orderNo) {
		log.info("------getOrderBillNo:" + orderNo);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (null == orderNo || orderNo.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_NO_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_NO_IS_NULL_MSG);
			return resultMap;
		}
		resultMap = o2oOrderService.getOrderBillNo(orderNo);
		return resultMap;
	}

	@Override
	public Map<String, Object> queryServFWOrder(String beginDate, String endDate, String dateType, Integer pageNo,
			Integer pageSize) {
		log.info("------queryServFWOrder beginDate:" + beginDate + ",endDate:" + endDate + ",dateType:" + ",pageNo:"
				+ pageNo + ",pageSize:" + pageSize);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		try {
			// 0.校验参数
			if (null == beginDate || beginDate.isEmpty() || null == endDate || endDate.isEmpty()) {
				beginDate = null;
				endDate = null;

			}
			if (null == dateType || dateType.isEmpty()) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_QUERY_DATETYPE_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_QUERY_DATETYPE_IS_NULL_MSG);
				return resultMap;
			}
			boolean isOKdateType = false;
			for (int i = 0; i < OrderCommonVo.ORDER_QUERY_DATE_TYPE.length; i++) {
				if (dateType.equals(OrderCommonVo.ORDER_QUERY_DATE_TYPE[i])) {
					isOKdateType = true;
				}
			}
			if (!isOKdateType) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_QUERY_DATETYPE_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_QUERY_DATETYPE_IS_NULL_MSG);
				return resultMap;
			}

			if (null == pageNo || pageNo < 0) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_QUERY_PAGENO_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_QUERY_PAGENO_IS_NULL_MSG);
				return resultMap;
			}

			if (null == pageSize || pageSize <= 0 || pageSize > 50) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_QUERY_PAGESIZE_TOO_LONG_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_QUERY_PAGESIZE_TOO_LONG_MSG);
				return resultMap;
			}

			// 1.查询订单
			reqMap.put("beginDate", beginDate);
			reqMap.put("endDate", endDate);
			reqMap.put("dateType", dateType);
			reqMap.put("orderType", OrderCommonVo.ORDER_TYPE_DDFW);
			reqMap.put("orderSource", OrderCommonVo.ORDER_SOURCE_DDBX_VALUE);
			reqMap.put("startPage", pageNo * pageSize);
			reqMap.put("pageCount", pageSize);
			List<OrderVo> currentPageOrders = orderVoMapper.getFWOrderListByMap(reqMap);
			log.info("------queryServFWOrder lstTempOrder:" + currentPageOrders);
			if (null == currentPageOrders || currentPageOrders.isEmpty()) {
				resultMap.put("code", MessageContants.POODER_ORDRE_SIZE_NULL_CODE);
				resultMap.put("codeMsg", MessageContants.POODER_ORDRE_SIZE_NULL_MSG);
				return resultMap;
			}
			// 2.判断是否需要分页，若分页
			List<OrderVo> needAllReturnOrders = new ArrayList<OrderVo>();
			List<OrderLineVo> needAllRetunrOrderLines = new ArrayList<OrderLineVo>();
			Long totalRecord = orderVoMapper.getCountFWOrderListByMap(reqMap);
			resultMap.put("totalRecord", totalRecord);
			needAllRetunrOrderLines.addAll(orderBizService.reGetOrderLines(currentPageOrders));
			needAllReturnOrders = currentPageOrders;
			log.info("------queryServFWOrder needAllReturnOrders:" + needAllReturnOrders);
			// 3.再针对对应的产品信息与订单进行匹配
			List<RespFWOrder> lstOrderInfo = new ArrayList<RespFWOrder>();
			for (OrderVo order : needAllReturnOrders) {
				Long orderId = order.getId();
				RespFWOrder orderInfoResp = new RespFWOrder();
				orderInfoResp.setCreateTime(
						order.getCreateTime() != null ? DateUtils.toDateStrNew(order.getCreateTime(), 0) : "");
				orderInfoResp.setOrderCode(order.getOrderNo());
				orderInfoResp.setUid(order.getPlateNumber());
				orderInfoResp.setServiceTime(
						order.getServiceTime() != null ? DateUtils.toDateStrNew(order.getServiceTime(), 0) : "");
				orderInfoResp.setWorkshopName(order.getWorkshopName());
				orderInfoResp.setReceiveUserName(order.getReceiveUserName());
				orderInfoResp.setReceiveRegionName(order.getRegionName());
				orderInfoResp.setReceiveUserTel(order.getReceivePhoneNo());
				orderInfoResp.setReceiveAddr(order.getAddress());
				orderInfoResp.setBillNo(order.getBillId());
				orderInfoResp.setOrderStatus(order.getStatus());
				orderInfoResp.setDeliveryTime(
						order.getDeliveryTime() != null ? DateUtils.toDateStrNew(order.getDeliveryTime(), 0) : "");
				List<RespProduct> lstOrderInfoProductRespVo = new ArrayList<RespProduct>();
				for (OrderLineVo orderLine : needAllRetunrOrderLines) {

					if (orderLine.getOrderId().equals(orderId)) {
						RespProduct orderInfoProductRespVo = new RespProduct();
						orderInfoProductRespVo.setName(orderLine.getProductName());
						orderInfoProductRespVo.setSku(orderLine.getSku());
						orderInfoProductRespVo.setQuantity(orderLine.getAmount());
						orderInfoProductRespVo.setUnits(orderLine.getUnits());
						lstOrderInfoProductRespVo.add(orderInfoProductRespVo);
					}
				}
				orderInfoResp.setLstProducts(lstOrderInfoProductRespVo);
				lstOrderInfo.add(orderInfoResp);
			}
			log.info("------queryServFWOrder lstOrderInfo:" + lstOrderInfo);
			resultMap.put("code", MessageContants.SUCCESS_CODE);
			resultMap.put("lstRespServFWOrder", lstOrderInfo);

		} catch (Exception e) {
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
		}
		return resultMap;
	}

	public Map<String, Object> checkXBOrder(ReqXBOrder xbOrder) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (null == xbOrder) {
			resultMap.put(KEY_CODE, MessageContants.PARAMS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.PARAMS_NULL_MSG);
			return resultMap;
		}
		String uid = xbOrder.getUid();
		String carType = xbOrder.getCarType();
		String cardType = xbOrder.getCardType();
		String levelId = xbOrder.getLevelId();
		String oilInjectin = xbOrder.getOilInjectin();
		Integer xbTimes = xbOrder.getXbTimes();

		if (null == uid || uid.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_UID_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_UID_IS_NULL_MSG);
			return resultMap;
		}

		if (null == levelId || levelId.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.LEVELID_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.LEVELID_NULL_MSG);
			return resultMap;
		}

		if (null == carType || carType.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.CARTYPE_DO_NOT_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.CARTYPE_DO_NOT_NULL_MSG);
			return resultMap;
		}

		if (null == cardType || cardType.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.CARDTYPE_DO_NOT_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.CARDTYPE_DO_NOT_NULL_MSG);
			return resultMap;
		}

		if (!cardType.equals(OrderCommonVo.ORDER_CARDTYPE_NK_TYPE)
				&& !cardType.equals(OrderCommonVo.ORDER_CARDTYPE_CK_TYPE)) {
			resultMap.put(KEY_CODE, MessageContants.CARDTYPE_IS_INVLID_CODE);
			resultMap.put(KEY_MSG, MessageContants.CARDTYPE_IS_INVLID_MSG);
			return resultMap;
		}

		if (null == xbTimes || xbTimes == 0) {
			resultMap.put(KEY_CODE, MessageContants.XBBUYTIMES_IS_INVALID_CODE);
			resultMap.put(KEY_MSG, MessageContants.XBBUYTIMES_IS_INVALID_MSG);
			return resultMap;
		}

		if (null == oilInjectin || oilInjectin.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_OILINJECTIN_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_OILINJECTIN_IS_NULL_MSG);
			return resultMap;
		}
		return null;
	}

	public Map<String, Object> checkFWOrder(ReqAddFWOrder fwOrder) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (null == fwOrder) {
			resultMap.put(KEY_CODE, MessageContants.PARAMS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.PARAMS_NULL_MSG);
			return resultMap;
		}

		String uid = fwOrder.getUid();
		if (null == uid || uid.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_UID_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_UID_IS_NULL_MSG);
			return resultMap;
		}
		String serviceTime = fwOrder.getServiceTime();
		if (null == serviceTime || serviceTime.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.O2OODER_SERVER_SERVICETIME_ISNULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.O2OODER_SERVER_SERVICETIME_ISNULL_MSG);
			return resultMap;
		}
		String workshopName = fwOrder.getWorkshopName();
		if (null == workshopName || workshopName.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.FWORDER_WORKSHOPNAME_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.FWORDER_WORKSHOPNAME_IS_NULL_MSG);
			return resultMap;
		}

		String receiveUserName = fwOrder.getReceiveUserName();
		if (null == receiveUserName || receiveUserName.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_RECEIVEUSER_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_RECEIVEUSER_IS_NULL_MSG);
			return resultMap;
		}

		String receiveTel = fwOrder.getReceiveUserTel();
		if (null == receiveTel || receiveTel.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_RECEIVETEL_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_RECEIVETEL_IS_NULL_MSG);
			return resultMap;
		}
		String receiveRegionCode = fwOrder.getReceiveRegionCode();
		if (null == receiveRegionCode || receiveRegionCode.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_REGIONID_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_REGIONID_IS_NULL_MSG);
			return resultMap;
		}
		String receiveAddr = fwOrder.getReceiveAddr();
		if (null == receiveAddr || receiveAddr.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_RECEIVEADDR_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_RECEIVEADDR_IS_NULL_MSG);
			return resultMap;
		}
		return null;
	}

	@Override
	public Map<String, Object> getProductLst() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		try {
			reqMap.put("category", "JY");
			List<ProductVo> lstProductVos = productVoMapper.selectProductByMap(reqMap);
			if (null == lstProductVos || lstProductVos.isEmpty()) {
				throw new Exception("获取产品列表数据为空");
			}
			List<Product> lstProducts = new ArrayList<Product>();
			String downloadUrl = (String) Constants.getSystemPropertyByCodeType(Constants.APP_HOST)
					+ "/downloadProductLogo.do?sourceType=5&&attId=";
			for (ProductVo productVo : lstProductVos) {
				Product product = new Product();
				product.setSku(productVo.getSku());
				product.setName(productVo.getName());
				product.setCapacity(productVo.getCapacity());
				product.setCategory(productVo.getCategory());
				product.setOilType(productVo.getOilType());
				product.setUnits(productVo.getUnits());
				product.setProductIconUrl(downloadUrl + productVo.getIconId());
				lstProducts.add(product);

			}
			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);
			resultMap.put("lstProducts", lstProducts);
		} catch (Exception e) {
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getOilProductInfoByVinCode(String vinCode) {
		log.info("------getOilProductInfoByVinCode vinCode:" + vinCode);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (null == vinCode || vinCode.isEmpty()) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_VIN_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_VIN_IS_NULL_MSG);
				return resultMap;
			}
			resultMap = webDDBXService.getOilProductInfoByVinCode(vinCode);
			// 存储vincode,levelid
			OrderVinCode orderVinCode = new OrderVinCode();
			orderVinCode.setCreateTime(new Date());
			orderVinCode.setVincode(vinCode.trim());
			orderVinCode.setLevelId(resultMap.get("levelId") == null ? null : (String) resultMap.get("levelId"));
			orderVinCodeMapper.insertSelective(orderVinCode);

		} catch (Exception e) {
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getVinCodes(String startDate, String endDate) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		try {
			if (null == startDate || startDate.isEmpty() || null == endDate || endDate.isEmpty()) {

				reqMap.put("startDate", DateUtils.toDateStrNew(new Date(), 3));
				reqMap.put("endDate", DateUtils.toDateStrNew(DateUtils.addDays(new Date(), 1), 3));
			} else {
				boolean isValidate = DateUtil.checkDate(startDate, DateUtil.DEFAULT_DATE_PATTERN);
				if (!isValidate) {
					resultMap.put(KEY_CODE, MessageContants.DATE_VALIDATE_CODE);
					resultMap.put(KEY_MSG, MessageContants.DATE_VALIDATE_MSG + startDate);
					return resultMap;
				}
				isValidate = DateUtil.checkDate(endDate, DateUtil.DEFAULT_DATE_PATTERN);
				if (!isValidate) {
					resultMap.put(KEY_CODE, MessageContants.DATE_VALIDATE_CODE);
					resultMap.put(KEY_MSG, MessageContants.DATE_VALIDATE_MSG + endDate);
					return resultMap;
				}
				reqMap.put("startDate", startDate);
				reqMap.put("endDate", endDate);

			}
			List<String> lstVinCodes = new ArrayList<String>();
			lstVinCodes = orderVinCodeMapper.getVinCodesByTime(reqMap);
			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);
			resultMap.put("lstVinCodes", lstVinCodes);
		} catch (Exception e) {
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
		}
		return resultMap;
	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> modifyFWOrder(ReqUpdateFWOrder order) {
//		log.info("------modifyFWOrder order:" + order);
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try {
//			if (null == order) {
//				resultMap.put(KEY_CODE, MessageContants.PARAMS_NULL_CODE);
//				resultMap.put(KEY_MSG, MessageContants.PARAMS_NULL_MSG);
//				return resultMap;
//			}
//
//			String orderCode = order.getOrderCode();
//			if (null == orderCode || orderCode.isEmpty()) {
//				resultMap.put(KEY_CODE, MessageContants.ORDER_NO_IS_NULL_CODE);
//				resultMap.put(KEY_MSG, MessageContants.ORDER_NO_IS_NULL_MSG);
//				return resultMap;
//			}
//			OrderVo tmpOrder = orderVoMapper.selectByOrderNo(orderCode);
//			if (null == tmpOrder) {
//				resultMap.put(KEY_CODE, MessageContants.ORDER_IS_NOT_EXITS_CODE);
//				resultMap.put(KEY_MSG, MessageContants.ORDER_IS_NOT_EXITS_MSG);
//				return resultMap;
//			}
//
//			log.info("------modifyFWOrder tmpOrder.getStatus():" + tmpOrder.getStatus());
//			if (!tmpOrder.getStatus().equals(OrderCommonVo.ORDER_STATUS[2])) {
//				resultMap.put(KEY_CODE, MessageContants.ORDER_HAS_SCAN_CODE);
//				resultMap.put(KEY_MSG, MessageContants.ORDER_HAS_SCAN_MSG);
//				return resultMap;
//			}
//
//			OrderVo orderVo = new OrderVo();
//			orderVo.setOrderNo(orderCode);
//			if (null != order.getServiceTime()) {
//				orderVo.setServiceTime(DateUtils.parseDate(order.getServiceTime(), 0));
//			}
//			orderVo.setWorkshopName(order.getWorkshopName());
//			orderVo.setReceiveUserName(order.getReceiveUserName());
//			orderVo.setReceivePhoneNo(order.getReceiveUserTel());
//			orderVo.setAddress(order.getReceiveAddr());
//			orderVo.setRemark(order.getRemark());
//			RegionVo regionVo = regionVoMapper.selectByRegionCodenew(order.getReceiveRegionCode().trim());
//			if (null == regionVo) {
//
//				resultMap.put(KEY_CODE, MessageContants.REGIONCODE_IS_ERROR_CODE);
//				resultMap.put(KEY_MSG, MessageContants.REGIONCODE_IS_ERROR_MSG);
//				return resultMap;
//			}
//			String receiveRegionName = regionVo.getRegionName();
//			orderVo.setRegionName(receiveRegionName);
//
//			if (null != order.getWorkshopName()) {
//				if (null != tmpOrder.getWorkshopName()
//						&& !order.getWorkshopName().trim().equals(tmpOrder.getWorkshopName().trim())) {
//					log.info("------modifyFWOrder tmpOrder.getPoOrderPartnerId():" + tmpOrder.getPoOrderPartnerId());
//					orderVo.setSource(tmpOrder.getSource());
//					orderVo.setOrderPartnerId(tmpOrder.getPoOrderPartnerId());
//					orderVo.setCreator(tmpOrder.getCreator());
//					Map<String, Object> returnMap = webDDBXService.createWorkshop(orderVo);
//					WorkShopVo workshop = (WorkShopVo) returnMap.get("workShopVo");
//					String newWorkshopFlag = (String) returnMap.get("newWorkshopFlag");
//					orderVo.setWorkShopId(workshop.getId());
//					if (null != newWorkshopFlag && newWorkshopFlag.equals("true")) {
//						webDDBXService.createWorkShopPartnerByOrder(workshop, orderVo);
//					}
//				}
//			}
//			orderVoMapper.updateByNoSelective(orderVo);
//			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);
//
//		} catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
//			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
//			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
//		}
//		return resultMap;
//	}

	@Override
	public Map<String, Object> cancelOrderByOrderCode(String orderNo) {
		log.info("------cancelOrderByOrderCode orderNo:" + orderNo);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (null == orderNo || orderNo.isEmpty()) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_NO_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_NO_IS_NULL_MSG);
				return resultMap;
			}
			OrderVo tmpOrder = orderVoMapper.selectByOrderNo(orderNo);
			if (null == tmpOrder) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_IS_NOT_EXITS_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_IS_NOT_EXITS_MSG);
				return resultMap;
			}

			log.info("------cancelOrderByOrderCode tmpOrder.getStatus():" + tmpOrder.getStatus());
			if (OrderCommonVo.ORDER_TYPE_DDFW.equals(tmpOrder.getOrderType())) {

				if (!tmpOrder.getStatus().equals(OrderCommonVo.ORDER_STATUS[2])) {
					resultMap.put(KEY_CODE, MessageContants.ORDER_HAS_SCAN_CODE);
					resultMap.put(KEY_MSG, MessageContants.ORDER_HAS_SCAN_MSG);
					return resultMap;
				}
			}

			if (OrderCommonVo.ORDER_TYPE_DDXB.equals(tmpOrder.getOrderType())) {

				if (!tmpOrder.getStatus().equals(OrderCommonVo.ORDER_STATUS[3])) {
					resultMap.put(KEY_CODE, MessageContants.ORDER_DO_NOT_CANCEL_CODE);
					resultMap.put(KEY_MSG, MessageContants.ORDER_DO_NOT_CANCEL_MSG);
					return resultMap;
				}
			}
			tmpOrder.setStatus(OrderCommonVo.ORDER_STATUS[6]);
			orderVoMapper.updateByNoSelective(tmpOrder);
			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);

		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
		}
		return resultMap;
	}
	// 大地保险订单接口新 end=================//

	// 预约服务订单导入----start//
//	/**
//	 * 独立滴滴预约服务订单导入
//	 */
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> createDDYYFWOrder(List<OrderDDYYFWVo> lstOrders) throws Exception {
//		log.info("------createDDYYFWOrder:" + JsonUtil.writeValue(lstOrders));
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		// 0.验证数据合法性
//		if (null == lstOrders || lstOrders.isEmpty()) {
//			resultMap.put(KEY_CODE, CODE_SUCCESS);
//			resultMap.put(KEY_MSG, MessageContants.DDORDER_IS_NULL_MSG);
//		}
//
//		String orderStatus = OrderCommonVo.ORDER_STATUS[4];
//		String orderType = "DA";
//		orderType = OrderCommonVo.ORDER_TYPE_DDFW;
//		WxTUser curUser = ContextUtil.getCurUser();
//		Date nowDate = new Date();
//		try {
//			// 1.sku信息查询
//			Map<String, Object> reqMap = new HashMap<String, Object>();
//			List<String> skus = new ArrayList<String>();
//			List<String> lstWorkShopName = new ArrayList<String>();
//			Map<String, Object> orderddyyfwMap = new HashMap<String, Object>();
//
//			List<String> skusNew = new ArrayList<String>();
//			for (OrderDDYYFWVo tempOrder : lstOrders) {
//				String sku = tempOrder.getSku().trim();
//				String workshopName = tempOrder.getWorkshopName().trim();
//				if (!skus.contains(sku)) {
//					skus.add(sku);
//				}
//
//				if (!lstWorkShopName.contains(workshopName)) {
//					lstWorkShopName.add(workshopName);
//					orderddyyfwMap.put(workshopName, tempOrder);
//				}
//
//			}
//			// sku转换 delete by bo.liu 180606
//			/*
//			 * reqMap.put("tempSkus",skus); List<WxtValueTransformMapVo> lst1 =
//			 * valueTransformMapVoMapper.selectBySkus(reqMap); for(OrderDDYYFWVo
//			 * orderDDYYFW:lstOrders) { for(WxtValueTransformMapVo v1:lst1) {
//			 * if(orderDDYYFW.getSku().equals(v1.getValueAfterTransform().trim())) {
//			 * orderDDYYFW.setSku(v1.getSku()); } }
//			 * if(!skusNew.contains(orderDDYYFW.getSku())) {
//			 * skusNew.add(orderDDYYFW.getSku()); } }
//			 */
//			reqMap.put("skus", skus);// reqMap.put("skus", skusNew);
//			List<ProductVo> lstProducts = productVoMapper.selectProductByMap(reqMap);
//			if (null == lstProducts || lstProducts.isEmpty()) {
//				throw new Exception("没有找到合法的产品sku");
//			}
//			Map<String, Object> productMap = new HashMap<String, Object>();
//			for (ProductVo tmpProduct : lstProducts) {
//				productMap.put(tmpProduct.getSku(), tmpProduct);
//			}
//			// 2.合伙人信息查询
//			reqMap.put("orgName", OrderCommonVo.ORDER_SP[2]);
//			Long partnerId = 0L;
//			if (null != organizationVoMapper.selectByOrganizationName(reqMap)
//					&& !organizationVoMapper.selectByOrganizationName(reqMap).isEmpty()) {
//				partnerId = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
//			}
//			String partnerName = OrderCommonVo.ORDER_SP[2];
//			if (null == partnerId || partnerId == 0) {
//				partnerId = OrderCommonVo.ORDER_SP_ID[2];
//			}
//			// 3.解析订单(相同车牌，预约时间，车主姓名)
//			Collections.sort(lstOrders, new Comparator<OrderDDYYFWVo>() {
//				public int compare(OrderDDYYFWVo o1, OrderDDYYFWVo o2) {
//					return o1.getServiceTime().compareTo(o2.getServiceTime());
//				}
//			});
//
//			Collections.sort(lstOrders, new Comparator<OrderDDYYFWVo>() {
//				public int compare(OrderDDYYFWVo o1, OrderDDYYFWVo o2) {
//					return o1.getPlateNumber().compareTo(o2.getPlateNumber());
//				}
//			});
//
//			Collections.sort(lstOrders, new Comparator<OrderDDYYFWVo>() {
//				public int compare(OrderDDYYFWVo o1, OrderDDYYFWVo o2) {
//					return o1.getWorkshopName().compareTo(o2.getWorkshopName());
//				}
//			});
//
//			List<WorkShopVo> lstWorkShop = new ArrayList<WorkShopVo>();
//			String currentServiceTime = "";
//			String lastServiceTime = "";
//			String currentPlantno = "";
//			String lastPlantno = "";
//			String currentWorkshopName = "";
//			String lastWorkshopName = "";
//			List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
//			List<OrderLineVo> lstOrderLineVo = null;
//			OrderVo lastOrderVo = null;
//			OrderVo currentOrderVo = null;
//			int i = 0;
//			Double totalPrice = 0.00;
//			log.info("----lstOrders:" + lstOrders.toString());
//			for (OrderDDYYFWVo orderDDYYFWVo : lstOrders) {
//				Date serviceTime = orderDDYYFWVo.getServiceTime();
//				currentServiceTime = DateUtils.toDateStr(serviceTime);
//				String buyUser = orderDDYYFWVo.getBuyUser().trim();
//				String plantno = orderDDYYFWVo.getPlateNumber().trim();
//				currentPlantno = plantno;
//				String tempworkshopname = orderDDYYFWVo.getWorkshopName();
//				currentWorkshopName = tempworkshopname;
//				String sku = orderDDYYFWVo.getSku();
//				if (null == serviceTime || null == buyUser || buyUser.isEmpty() || null == plantno
//						|| plantno.isEmpty()) {
//					throw new Exception("预约时间、车牌号或车主姓名为空");
//				}
//
//				String unit = orderDDYYFWVo.getUnit();
//				if (null == sku || sku.isEmpty() || null == orderDDYYFWVo.getQuantity()
//						|| orderDDYYFWVo.getQuantity().isEmpty() || null == unit || unit.isEmpty()) {
//					throw new Exception(MessageContants.SKU_IS_NULL_CODE + sku);
//				}
//				if (!BOX_.equals(unit) && !BOTTLE_.equals(unit) && !GE_.equals(unit)) {
//					throw new Exception(MessageContants.UNIT_IS_INVLID_MSG + sku);
//				}
//
//				ProductVo currentProDouct = (ProductVo) productMap.get(sku);
//				if (null == currentProDouct) {
//					throw new Exception(
//							"没有找到有效的sku:" + sku + " 预约时间：" + DateUtils.toDateStr(serviceTime) + " 车主姓名:" + buyUser);
//				}
//				if ((!lastServiceTime.equals(currentServiceTime)) || (!lastPlantno.equals(currentPlantno))
//						|| (!lastWorkshopName.equals(currentWorkshopName))) {
//
//					totalPrice = 0.00;
//					lstOrderLineVo = new ArrayList<OrderLineVo>();
//					currentOrderVo = new OrderVo();
//					currentOrderVo.setOrderType(orderType);
//					currentOrderVo.setSource(OrderCommonVo.ORDER_SOURCE_DDYY_VALUE);
//					currentOrderVo.setSourceId(OrderCommonVo.ORDER_SOURCE_DDYY_VALUE + 1);
//					currentOrderVo.setServiceTime(DateUtils.parseDate(currentServiceTime, 0));
//					currentOrderVo.setBuyUserName(buyUser);
//					currentOrderVo.setPlateNumber(orderDDYYFWVo.getPlateNumber());
//					currentOrderVo.setCarType(orderDDYYFWVo.getCarType());
//					currentOrderVo.setCreator("" + curUser.getUserId());
//					currentOrderVo.setPhoneNo(orderDDYYFWVo.getContactNumber());
//					currentOrderVo.setReceiveUserName(orderDDYYFWVo.getContactName());
//					currentOrderVo.setReceivePhoneNo(orderDDYYFWVo.getContactNumber());
//					currentOrderVo.setWorkshopName(orderDDYYFWVo.getWorkshopName());
//					currentOrderVo.setAddress(orderDDYYFWVo.getWorkshopAdress());
//					currentOrderVo.setRemark("滴滴预约订单导入");
//					currentOrderVo.setCreateTime(nowDate);
//					currentOrderVo.setUpdateTime(nowDate);
//					currentOrderVo.setStatus(orderStatus);
//					currentOrderVo.setPoOrderPartnerId(partnerId);
//					currentOrderVo.setRegionName(orderDDYYFWVo.getReginName());
//					currentOrderVo.setPreferentialTypePrice("滴滴保养");
//					String sequenceNo = propertiesServiceImpl.getSequenceByType(SequenceTypes.DDYY_SERVICE_ORDER, 6, 1);
//					currentOrderVo.setOrderNo(CommonUtil.generateOrderCode(16) + sequenceNo);// 订单处理......
//					// 门店信息
//					String workshopname = orderDDYYFWVo.getWorkshopName();
//					reqMap.put("workShopName", workshopname);// 状态为已下单
//					reqMap.put("source", OrderCommonVo.ORDER_SOURCE_DDYY_VALUE);// 门店来源
//					List<WorkShopVo> lstWokrshopVo = workShopVoMapper.getWorkshopByNameAndSource(reqMap);
//					WorkShopVo workShopVo = null;
//					if (null != lstWokrshopVo && !lstWokrshopVo.isEmpty()) {
//						workShopVo = lstWokrshopVo.get(0);
//					}
//					Long workshopId = 0L;
//					if (null == workShopVo) {
//						workShopVo = new WorkShopVo();
//						workShopVo.setWorkShopName(workshopname);
//						workShopVo.setWorkShopAddress(orderDDYYFWVo.getWorkshopAdress());
//						workShopVo.setSource(OrderCommonVo.ORDER_SOURCE_DDYY_VALUE);
//						workShopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//						workShopVo.setReserveServiceTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setContactPerson(orderDDYYFWVo.getContactName());
//						workShopVo.setContactPersonTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setHarvestPerson(orderDDYYFWVo.getContactName());
//						workShopVo.setHarvestPersonTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setButtInChargePerson(orderDDYYFWVo.getContactName());
//						workShopVo.setButtInChargePersonTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setCreateTime(new Date());
//						workShopVo.setCreator("" + curUser.getUserId());
//						workShopVo.setPartnerId("" + partnerId);
//						workShopVo.setPartnerName(partnerName);
//						workShopVoMapper.insertSelective(workShopVo);
//						lstWorkShop.add(workShopVo);
//					}
//					workshopId = workShopVo.getId();
//					currentOrderVo.setWorkShopId(workshopId);
//
//				}
//
//				OrderLineVo orderLineVo = new OrderLineVo();
//				int amount = 1;
//				if (null != orderDDYYFWVo.getQuantity() && !orderDDYYFWVo.getQuantity().isEmpty()) {
//					amount = Integer.parseInt(orderDDYYFWVo.getQuantity());
//				}
//				// 需要转换成瓶 add by bo.liu 180917
//				if (BOX_.equals(orderDDYYFWVo.getUnit()))// 箱转换成瓶
//				{
//					amount = amount * currentProDouct.getBottleQty();// 转换成瓶码
//				}
//
//				orderLineVo.setAmount(amount);
//				orderLineVo.setActualAmount(amount);
//				orderLineVo.setSku(sku);
//				orderLineVo.setCreateTime(nowDate);
//				orderLineVo.setCreator("" + curUser.getUserId());
//				orderLineVo.setPrice(currentProDouct.getSalePrice());
//				orderLineVo.setProductId(currentProDouct.getId());
//				orderLineVo.setProductName(currentProDouct.getName());
//				orderLineVo.setUnits(currentProDouct.getUnits());
//				orderLineVo.setType(currentProDouct.getCategory());
//				orderLineVo.setStatus("1");
//
//				lstOrderLineVo.add(orderLineVo);
//				currentOrderVo.setOrderLines(lstOrderLineVo);
//				totalPrice += currentProDouct.getSalePrice() * amount;
//				currentOrderVo.setTotalOrderPrice(totalPrice);
//				currentOrderVo.setTotalProductPrice(totalPrice);
//				currentOrderVo.setTotalOrderPreferPrice(totalPrice);
//				currentOrderVo.setTotalOrderPayPrice(totalPrice);
//				currentOrderVo.setTotalProductPreferPrice(totalPrice);
//				if (((!lastServiceTime.equals(currentServiceTime)) || (!lastPlantno.equals(currentPlantno))
//						|| (!lastWorkshopName.equals(currentWorkshopName))) && (i != 0)) {
//					lstNewOrderVo.add(lastOrderVo);
//				}
//				lastOrderVo = new OrderVo();
//				lastOrderVo = currentOrderVo;
//				i++;
//				lastServiceTime = currentServiceTime;
//				lastPlantno = currentPlantno;
//				lastWorkshopName = currentWorkshopName;
//			}
//			lstNewOrderVo.add(currentOrderVo);
//			// 4.录入订单
//			webDDBXService.createOrderAndWorkshopInfoForFWOrder(lstNewOrderVo, lstWorkShop,
//					OrderCommonVo.ORDER_SOURCE_DDYYFW_TYPE, false);
//			resultMap.put(KEY_CODE, "success");
//		} catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
//			resultMap.put(KEY_CODE, CODE_ERROR);
//			resultMap.put(KEY_MSG, e.getLocalizedMessage());
//		}
//		return resultMap;
//	}

//	/**
//	 * 通用预约服务订单导入
//	 */
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> createYYFWOrder(List<OrderYYFWVo> lstOrders) throws Exception {
//		log.info("------createYYFWOrder 0802:" + JsonUtil.writeValue(lstOrders));
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		Map<String, Object> reqMap = new HashMap<String, Object>();
//		try {
//			/**
//			 * 0.准备基础辅助数据
//			 */
//			String orderStatus = OrderCommonVo.ORDER_STATUS[4];
//			String orderType = "DA";
//			orderType = OrderCommonVo.ORDER_TYPE_DDFW;
//			WxTUser curUser = ContextUtil.getCurUser();
//
//			/**
//			 * 1.校验订单必备项，收货人，收货地址，收货电话，产品信息
//			 */
//			Map<String, Object> returnMap = checkYYFWOrder(lstOrders);
//			if (null != returnMap) {
//				return returnMap;
//			}
//			/**
//			 * 2.处理sku
//			 */
//			log.info("-----createYYFWOrder processSkuProduct");
//			Map<String, Object> productMap = processSkuProduct(lstOrders);
//
//			/**
//			 * 3.查询订单来源表，获取合伙人id，订单来源source(DDYYFW),订单的唯一标识字段（最好配置数据库表的时候要注意顺序），并检验配置表信息
//			 */
//			String orderSourceName = lstOrders.get(0).getOrderSourceName().trim();
//			reqMap.put("sourceName", orderSourceName);
//			List<WxtOrderYYFWSetsVo> lstYYFW = orderYYFWSetsMapper.queryOrderYYFWInfoByMap(reqMap);
//			if (null == lstYYFW || lstYYFW.isEmpty()) {
//				resultMap.put(KEY_CODE, MessageContants.ORDER_SOUCE_IS_NOT_FOUND_CODE);
//				resultMap.put(KEY_MSG, MessageContants.ORDER_SOUCE_IS_NOT_FOUND_MSG + orderSourceName);
//				return resultMap;
//			}
//
//			if (null == lstYYFW.get(0).getSourceName() || lstYYFW.get(0).getSourceName().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:sourceName");
//			}
//			if (null == lstYYFW.get(0).getSourceValue() || lstYYFW.get(0).getSourceValue().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:sourceValue");
//			}
//			if (null == lstYYFW.get(0).getOrderFieldFlags() || lstYYFW.get(0).getOrderFieldFlags().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:orderFieldFlags");
//			}
//			if (null == lstYYFW.get(0).getSourceType() || lstYYFW.get(0).getSourceType().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:sourceType");
//			}
//			if (null == lstYYFW.get(0).getOrderNoFlag() || lstYYFW.get(0).getOrderNoFlag().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:orderNoFlag");
//			}
//			if (null == lstYYFW.get(0).getOrderNoSequnceName() || lstYYFW.get(0).getOrderNoSequnceName().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:orderNoSequnceName");
//			}
//			String partnerName = lstYYFW.get(0).getSourceName().trim();
//			Long partnerId = lstYYFW.get(0).getPartnerId();
//			String sourceValue = lstYYFW.get(0).getSourceValue().trim();
//			String orderFieldFlags = lstYYFW.get(0).getOrderFieldFlags().trim();
//			orderType = lstYYFW.get(0).getSourceType().trim();
//			// String orderRemark = lstYYFW.get(0).getRemark().trim();
//			String orderNoFlag = lstYYFW.get(0).getOrderNoFlag().trim();
//			String orderNoSequnceName = lstYYFW.get(0).getOrderNoSequnceName().trim();
//			/**
//			 * 4.获取到唯一标识订单字段后，然后进行订单列表的排序
//			 */
//			if (null == orderFieldFlags || orderFieldFlags.isEmpty()) {
//				resultMap.put(KEY_CODE, MessageContants.ORDER_ONLY_FLAG_IS_NOT_FOUND_CODE);
//				resultMap.put(KEY_MSG, MessageContants.ORDER_ONLY_FLAG_IS_NOT_FOUND_MSG + partnerName);
//				return resultMap;
//			}
//			String fileds[] = orderFieldFlags.trim().split(",");
//			log.info("-----createYYFWOrder sortOrder start");
//			Map<String, Object> sortMap = sortYYFWOrder(fileds, lstOrders);
//			log.info("-----createYYFWOrder sortOrder end lstOrders:" + lstOrders);
//			List<OrderYYFWVo> sortLstOrders = (List<OrderYYFWVo>) sortMap.get("lstOrders");
//			log.info("-----createYYFWOrder sortOrder end sortLstOrders:" + sortLstOrders);
//
//			/**
//			 * 5.解析订单（通过订单唯一标识字段分组订单）；构造目标录入数据库的订单信息；其中有门店的，需要录入门店信息
//			 */
//			Map<String, Object> baseDataMap = new HashMap<String, Object>();
//			baseDataMap.put("orderStatus", orderStatus);
//			baseDataMap.put("creator", "" + curUser.getUserId());
//			baseDataMap.put("orderType", orderType);
//			baseDataMap.put("partnerId", partnerId);
//			baseDataMap.put("partnerName", partnerName);
//			baseDataMap.put("source", sourceValue);// 订单源
//			// baseDataMap.put("orderRemark", orderRemark);//订单描述
//			baseDataMap.put("orderFieldFlags", fileds);// 必须检查项唯一订单标识
//			baseDataMap.put("orderNoFlag", orderNoFlag);// 订单类型标识码
//			baseDataMap.put("orderNoSequnceName", orderNoSequnceName);// 订单生成规则序列号
//
//			log.info("-----createYYFWOrder processOrders start");
//			Map<String, Object> processMap = processOrders(productMap, baseDataMap, sortLstOrders);
//			log.info("-----createYYFWOrder processOrders end");
//
//			/**
//			 * 6.录入订单信息，如果有门店信息，需要关联到合伙人 lstNewOrderVo", lstNewOrderVo);
//			 * resultMap.put("lstWorkShop
//			 */
//			List<OrderVo> lstNewOrderVo = (List<OrderVo>) processMap.get("lstNewOrderVo");
//			List<WorkShopVo> lstWorkShop = (List<WorkShopVo>) processMap.get("lstWorkShop");
//			webDDBXService.createOrderAndWorkshopInfoForFWOrder(lstNewOrderVo, lstWorkShop,
//					OrderCommonVo.ORDER_SOURCE_DDYYFW_TYPE, false);
//			resultMap.put(KEY_CODE, "success");
//		} catch (Exception e) {
//			log.error("",e);
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
//			resultMap.put(KEY_CODE, CODE_ERROR);
//			resultMap.put(KEY_MSG, e.getLocalizedMessage());
//		}
//		return resultMap;
//	}

	public Map<String, Object> checkYYFWOrder(List<OrderYYFWVo> lstOrders) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (null == lstOrders || lstOrders.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.PARAMS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.PARAMS_NULL_MSG);
			return resultMap;
		}

		if (null == lstOrders.get(0).getOrderSourceName() || lstOrders.get(0).getOrderSourceName().isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_SOUCE_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_SOUCE_IS_NULL_MSG);
			return resultMap;
		}

		for (OrderYYFWVo order : lstOrders) {
			String orderSourceName = order.getOrderSourceName();
			if (StringUtils.isBlank(orderSourceName))
				continue;
			String receiveUser = order.getContactName();
			String receivePhone = order.getContactNumber();
			String receiveAddr = order.getWorkshopAdress();
			if (null == receiveUser || receiveUser.isEmpty() || null == receivePhone || receivePhone.isEmpty()
					|| null == receiveAddr || receiveAddr.isEmpty()) {
				resultMap.put(KEY_CODE, MessageContants.ORDER_RECEIVEINFO_NOT_PERFECT_CODE);
				resultMap.put(KEY_MSG, MessageContants.ORDER_RECEIVEINFO_NOT_PERFECT_MSG);
				return resultMap;
			}
			String sku = order.getSku();
			String quantity = order.getQuantity();
			String unit = order.getUnit();
			if (null == sku || sku.isEmpty() || null == quantity || quantity.isEmpty() || null == unit
					|| unit.isEmpty()) {
				resultMap.put(KEY_CODE, MessageContants.SKU_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.SKU_IS_NULL_MSG);
				return resultMap;
			}

			if (!BOX_.equals(unit) && !BOTTLE_.equals(unit) && !GE_.equals(unit)) {
				resultMap.put(KEY_CODE, MessageContants.UNIT_IS_INVLID_CODE);
				resultMap.put(KEY_MSG, MessageContants.UNIT_IS_INVLID_MSG + sku);
				return resultMap;
			}
		}
		return null;
	}

	public Map<String, Object> processSkuProduct(List<OrderYYFWVo> lstOrders) throws Exception {
		Map<String, Object> reqMap = new HashMap<String, Object>();
		List<String> skus = new ArrayList<String>();
		List<String> lstWorkShopName = new ArrayList<String>();
		Map<String, Object> orderddyyfwMap = new HashMap<String, Object>();

		List<String> skusNew = new ArrayList<String>();
		for (OrderYYFWVo tempOrder : lstOrders) {
			String sku = tempOrder.getSku().trim();
			String workshopName = tempOrder.getWorkshopName().trim();
			if (!skus.contains(sku)) {
				skus.add(sku);
			}

			if (!lstWorkShopName.contains(workshopName)) {
				lstWorkShopName.add(workshopName);
				orderddyyfwMap.put(workshopName, tempOrder);
			}

		}
		// sku转换 delete bo.liu 180606
		/*
		 * reqMap.put("tempSkus",skus); List<WxtValueTransformMapVo> lst1 =
		 * valueTransformMapVoMapper.selectBySkus(reqMap); for(OrderYYFWVo
		 * orderYYFW:lstOrders) { for(WxtValueTransformMapVo v1:lst1) {
		 * if(orderYYFW.getSku().equals(v1.getValueAfterTransform().trim())) {
		 * orderYYFW.setSku(v1.getSku()); } } if(!skusNew.contains(orderYYFW.getSku()))
		 * { skusNew.add(orderYYFW.getSku()); } }
		 */
		reqMap.put("skus", skus);
		List<ProductVo> lstProducts = productVoMapper.selectProductByMap(reqMap);
		if (null == lstProducts || lstProducts.isEmpty()) {
			throw new Exception("没有找到合法的产品sku");
		}
		Map<String, Object> productMap = new HashMap<String, Object>();
		for (ProductVo tmpProduct : lstProducts) {
			productMap.put(tmpProduct.getSku(), tmpProduct);
		}
		return productMap;
	}

	public Map<String, Object> sortYYFWOrder(String[] orderFieldFlags, List<OrderYYFWVo> lstOrders) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		for (final String filedName : orderFieldFlags) {
			Collections.sort(lstOrders, new Comparator<OrderYYFWVo>() {
				public int compare(OrderYYFWVo o1, OrderYYFWVo o2) {
					Field uField1;
					Field uField2;
					String value1 = "";
					String value2 = "";
					String filedType = "";
					try {
						uField1 = o1.getClass().getDeclaredField(filedName);
						uField2 = o2.getClass().getDeclaredField(filedName);
						filedType = uField1.getType().toString();
						if (filedType.endsWith("Date")) {
							Date date1 = (Date) uField1.get(o1);
							if (null != date1) {
								value1 = DateUtils.toDateStr(date1);
							}
							Date date2 = (Date) uField2.get(o2);
							if (null != date2) {
								value2 = DateUtils.toDateStr(date2);
							}
						} else {
							if (null != uField1.get(o1)) {
								value1 = (String) uField1.get(o1);
							}
							if (null != uField2.get(o2)) {
								value2 = (String) uField2.get(o2);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						log.error(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
					}
					return value1.compareTo(value2);
				}
			});
		}
		resultMap.put("lstOrders", lstOrders);
		return resultMap;
	}

//	public Map<String, Object> processOrders(Map<String, Object> productMap, Map<String, Object> baseDataMap,
//			List<OrderYYFWVo> lstOrders) throws Exception {
//
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		Map<String, Object> reqMap = new HashMap<String, Object>();
//		List<WorkShopVo> lstWorkShop = new ArrayList<WorkShopVo>();
//		Date nowDate = new Date();
//		log.info("processOrders----orderFieldFlags[]:");
//		String orderFieldFlags[] = (String[]) baseDataMap.get("orderFieldFlags");
//		log.info("processOrders----orderFieldFlags[]:" + orderFieldFlags);
//		Field uField;
//		String filedType = "";
//		int tmpFlag = orderFieldFlags.length;
//		Map<String, String> fileNameMap = new HashMap<String, String>();
//
//		// 定义5组操作字符串，用于判断配置的必备检查项（唯一订单标识） 最多5个项能确定唯一订单
//		String currentStr1 = "";
//		String lastStr1 = "";
//
//		String currentStr2 = "";
//		String lastStr2 = "";
//
//		String currentStr3 = "";
//		String lastStr3 = "";
//
//		String currentStr4 = "";
//		String lastStr4 = "";
//
//		String currentStr5 = "";
//		String lastStr5 = "";
//
//		List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
//		List<OrderLineVo> lstOrderLineVo = null;
//		OrderVo lastOrderVo = null;
//		OrderVo currentOrderVo = null;
//		int i = 0;
//		Double totalPrice = 0.00;
//		log.info("processOrders----lstOrders:" + lstOrders);
//		boolean isNotSameFlag = false;
//		String orderKeyStr = "";
//		for (OrderYYFWVo orderDDYYFWVo : lstOrders) {
//			if (StringUtils.isBlank(orderDDYYFWVo.getOrderSourceName()))
//				continue;
//			int j = 1;
//			for (String filedName : orderFieldFlags) {
//				uField = orderDDYYFWVo.getClass().getDeclaredField(filedName);
//				String value = "";
//				filedType = uField.getType().toString();
//				if (filedType.endsWith("Date")) {
//					Date tmpDate = (Date) uField.get(orderDDYYFWVo);
//					value = DateUtils.toDateStr(tmpDate);
//				} else {
//					value = (String) uField.get(orderDDYYFWVo);
//				}
//				if (null == value || value.isEmpty()) {
//					log.info("error filedName is null: " + filedName);
//					throw new Exception("订单唯一识别字段存在为空的情况");
//				}
//				fileNameMap.put("f" + j, value);
//				j++;
//			}
//			Date serviceTime = orderDDYYFWVo.getServiceTime();
//			String buyUser = orderDDYYFWVo.getBuyUser();
//			String tempworkshopname = orderDDYYFWVo.getWorkshopName();
//			String sku = orderDDYYFWVo.getSku();
//			ProductVo currentProDouct = (ProductVo) productMap.get(sku);
//			if (null == currentProDouct) {
//				throw new Exception("没有找到有效的sku:" + sku + " 收货人:" + orderDDYYFWVo.getContactName().trim());
//			}
//
//			currentStr1 = fileNameMap.get("f1");
//			currentStr2 = fileNameMap.get("f2");
//			currentStr3 = fileNameMap.get("f3");
//			currentStr4 = fileNameMap.get("f4");
//			currentStr5 = fileNameMap.get("f5");
//			switch (tmpFlag) {
//			case 1:
//				orderKeyStr = currentStr1;
//				if ((!lastStr1.equals(currentStr1))) {
//					isNotSameFlag = true;
//				} else {
//					isNotSameFlag = false;
//				}
//				break;
//			case 2:
//				orderKeyStr = currentStr1 + " " + currentStr2;
//				if ((!lastStr1.equals(currentStr1)) || (!lastStr2.equals(currentStr2))) {
//					isNotSameFlag = true;
//				} else {
//					isNotSameFlag = false;
//				}
//				break;
//			case 3:
//				orderKeyStr = currentStr1 + " " + currentStr2 + " " + currentStr3;
//				if ((!lastStr1.equals(currentStr1)) || (!lastStr2.equals(currentStr2))
//						|| (!lastStr3.equals(currentStr3))) {
//					isNotSameFlag = true;
//				} else {
//					isNotSameFlag = false;
//				}
//				break;
//			case 4:
//				orderKeyStr = currentStr1 + " " + currentStr2 + " " + currentStr3 + " " + currentStr4;
//				if ((!lastStr1.equals(currentStr1)) || (!lastStr2.equals(currentStr2))
//						|| (!lastStr3.equals(currentStr3)) || (!lastStr4.equals(currentStr4))) {
//					isNotSameFlag = true;
//				} else {
//					isNotSameFlag = false;
//				}
//
//				break;
//			case 5:
//				orderKeyStr = currentStr1 + " " + currentStr2 + " " + currentStr3 + " " + currentStr4 + " "
//						+ currentStr5;
//				if ((!lastStr1.equals(currentStr1)) || (!lastStr2.equals(currentStr2))
//						|| (!lastStr3.equals(currentStr3)) || (!lastStr4.equals(currentStr4))
//						|| (!lastStr5.equals(currentStr5))) {
//					isNotSameFlag = true;
//				} else {
//					isNotSameFlag = false;
//				}
//				break;
//			default:
//				break;
//			}
//
//			if (isNotSameFlag) {
//
//				String plantNo = orderDDYYFWVo.getPlateNumber();
//				String ordercode = orderDDYYFWVo.getOrderCode();
//				if (null == plantNo) {
//					plantNo = "";
//				}
//				if (null == ordercode) {
//					ordercode = "";
//				}
//				totalPrice = 0.00;
//				lstOrderLineVo = new ArrayList<OrderLineVo>();
//				currentOrderVo = new OrderVo();
//				currentOrderVo.setOrderType((String) baseDataMap.get("orderType"));
//				currentOrderVo.setSource((String) baseDataMap.get("source"));
//				currentOrderVo.setSourceId((String) baseDataMap.get("source") + 1);
//				currentOrderVo.setServiceTime(serviceTime);
//				currentOrderVo.setBuyUserName(buyUser);
//				if ((plantNo + ordercode).trim().isEmpty()) {
//					currentOrderVo.setPlateNumber("" + currentStr1);// 默认取第一个
//				} else {
//					if (!plantNo.trim().isEmpty() && !ordercode.trim().isEmpty()) {
//						currentOrderVo.setPlateNumber("" + plantNo);// 否则都不为空，就取车牌号
//					} else {
//						currentOrderVo.setPlateNumber("" + (plantNo + ordercode));// 否则车牌号要么就是车牌号，要么就是三方订单号
//					}
//				}
//				currentOrderVo.setCarType(orderDDYYFWVo.getCarType());
//				currentOrderVo.setCreator((String) baseDataMap.get("creator"));
//				currentOrderVo.setPhoneNo(orderDDYYFWVo.getContactNumber());
//				currentOrderVo.setReceiveUserName(orderDDYYFWVo.getContactName());
//				currentOrderVo.setReceivePhoneNo(orderDDYYFWVo.getContactNumber());
//				currentOrderVo.setWorkshopName(tempworkshopname);
//				currentOrderVo.setAddress(orderDDYYFWVo.getWorkshopAdress());
//				if (!plantNo.trim().isEmpty()) {
//					if (orderDDYYFWVo.getContactName().contains(plantNo.trim())) {
//						plantNo = "";
//					}
//				}
//				currentOrderVo.setRemark((String) baseDataMap.get("partnerName") + " " + orderDDYYFWVo.getContactName()
//						+ " " + orderKeyStr);
//				currentOrderVo.setCreateTime(nowDate);
//				currentOrderVo.setUpdateTime(nowDate);
//				currentOrderVo.setStatus((String) baseDataMap.get("orderStatus"));
//				currentOrderVo.setPoOrderPartnerId((Long) baseDataMap.get("partnerId"));
//				currentOrderVo.setRegionName(orderDDYYFWVo.getReginName());
//				currentOrderVo.setPreferentialTypePrice("预约服务订单");
//				// 生成订单号
//				String orderNoSequnceName = (String) baseDataMap.get("orderNoSequnceName");
//				String orderNoFlag = (String) baseDataMap.get("orderNoFlag");
//				reqMap.put("orderNoSequnceName", orderNoSequnceName);
//				Long tmpSequenceNo = propertiesMapper.getSequenceBySequenceName(reqMap);
//				String sequenceNo = CommonUtil.addZeroForStr("" + tmpSequenceNo, 6, 1);
//				currentOrderVo.setOrderNo(CommonUtil.generateOrderCode(orderNoFlag) + sequenceNo);
//				// 门店信息
//				String workshopname = orderDDYYFWVo.getWorkshopName();
//				if (null != workshopname && !workshopname.isEmpty()) {
//					reqMap.put("workShopName", workshopname);// 状态为已下单
//					reqMap.put("source", (String) baseDataMap.get("source"));// 门店来源
//					List<WorkShopVo> lstWokrshopVo = workShopVoMapper.getWorkshopByNameAndSource(reqMap);
//					WorkShopVo workShopVo = null;
//					if (null != lstWokrshopVo && !lstWokrshopVo.isEmpty()) {
//						workShopVo = lstWokrshopVo.get(0);
//					}
//
//					Long workshopId = 0L;
//					if (null == workShopVo) {
//						workShopVo = new WorkShopVo();
//						workShopVo.setWorkShopName(workshopname);
//						workShopVo.setWorkShopAddress(orderDDYYFWVo.getWorkshopAdress());
//						workShopVo.setSource((String) baseDataMap.get("source"));
//						workShopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//						workShopVo.setReserveServiceTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setContactPerson(orderDDYYFWVo.getContactName());
//						workShopVo.setContactPersonTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setHarvestPerson(orderDDYYFWVo.getContactName());
//						workShopVo.setHarvestPersonTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setButtInChargePerson(orderDDYYFWVo.getContactName());
//						workShopVo.setButtInChargePersonTel(orderDDYYFWVo.getContactNumber());
//						workShopVo.setCreateTime(new Date());
//						workShopVo.setCreator((String) baseDataMap.get("creator"));
//						workShopVo.setPartnerId("" + (Long) baseDataMap.get("partnerId"));
//						workShopVo.setPartnerName((String) baseDataMap.get("partnerName"));
//						workShopVoMapper.insertSelective(workShopVo);
//						lstWorkShop.add(workShopVo);
//					}
//					workshopId = workShopVo.getId();
//					currentOrderVo.setWorkShopId(workshopId);
//				}
//
//			}
//
//			OrderLineVo orderLineVo = new OrderLineVo();
//			int amount = 1;
//			if (null != orderDDYYFWVo.getQuantity() && !orderDDYYFWVo.getQuantity().isEmpty()) {
//				amount = Integer.parseInt(orderDDYYFWVo.getQuantity());
//			}
//			// 需要转换成瓶 add by bo.liu 180917
//			if (BOX_.equals(orderDDYYFWVo.getUnit().trim()))// 如果是项就转换
//			{
//				amount = amount * currentProDouct.getBottleQty();// 转换成瓶码
//			}
//
//			orderLineVo.setAmount(amount);
//			orderLineVo.setActualAmount(amount);
//			orderLineVo.setSku(sku);
//			orderLineVo.setCreateTime(nowDate);
//			orderLineVo.setCreator((String) baseDataMap.get("creator"));
//			orderLineVo.setPrice(currentProDouct.getSalePrice());
//			orderLineVo.setProductId(currentProDouct.getId());
//			orderLineVo.setProductName(currentProDouct.getName());
//			orderLineVo.setUnits(currentProDouct.getUnits());
//			orderLineVo.setType(currentProDouct.getCategory());
//			orderLineVo.setStatus("1");
//
//			lstOrderLineVo.add(orderLineVo);
//			currentOrderVo.setOrderLines(lstOrderLineVo);
//			totalPrice += (currentProDouct.getSalePrice()!=null?currentProDouct.getSalePrice() * amount:0L);
//			currentOrderVo.setTotalOrderPrice(totalPrice);
//			currentOrderVo.setTotalProductPrice(totalPrice);
//			currentOrderVo.setTotalOrderPreferPrice(totalPrice);
//			currentOrderVo.setTotalOrderPayPrice(totalPrice);
//			currentOrderVo.setTotalProductPreferPrice(totalPrice);
//			if (isNotSameFlag && (i != 0)) {
//				lstNewOrderVo.add(lastOrderVo);
//			}
//			lastOrderVo = new OrderVo();
//			lastOrderVo = currentOrderVo;
//			i++;
//			lastStr1 = currentStr1;
//			lastStr2 = currentStr2;
//			lastStr3 = currentStr3;
//			lastStr4 = currentStr4;
//			lastStr5 = currentStr5;
//		}
//		lstNewOrderVo.add(currentOrderVo);
//		log.info("processOrders----lstNewOrderVo:" + lstNewOrderVo + "----lstWorkShop:" + lstWorkShop);
//		resultMap.put("lstNewOrderVo", lstNewOrderVo);
//		resultMap.put("lstWorkShop", lstWorkShop);
//		return resultMap;
//	}
	
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> createGeneralFWOrder(ReqGeneralFWOrder generalFWOrder) {
//
//		log.info("------createGeneralFWOrder 0814:" + JsonUtil.writeValue(generalFWOrder));
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		Map<String, Object> reqMap = new HashMap<String, Object>();
//		try {
//			/**
//			 * 0.准备基础辅助数据
//			 */
//			String orderStatus = OrderCommonVo.ORDER_STATUS[4];
//			String orderType = "DA";
//			orderType = OrderCommonVo.ORDER_TYPE_DDFW;
//			WxTUser curUser = ContextUtil.getCurUser();
//
//			/**
//			 * 1.校验订单必备项，收货人，收货地址，收货电话，产品信息
//			 */
//			Map<String, Object> returnMap = checkGeneralFWOrder(generalFWOrder);
//			if (null != returnMap) {
//				return returnMap;
//			}
//
//			/**
//			 * 2.查询订单来源表，获取合伙人id，订单来源source(DDYYFW),订单的唯一标识字段（最好配置数据库表的时候要注意顺序），并检验配置表信息
//			 */
//			String orderSourceName = generalFWOrder.getOrderSourceName().trim();
//			reqMap.put("sourceName", orderSourceName);
//			List<WxtOrderYYFWSetsVo> lstYYFW = orderYYFWSetsMapper.queryOrderYYFWInfoByMap(reqMap);
//			if (null == lstYYFW || lstYYFW.isEmpty()) {
//				resultMap.put(KEY_CODE, MessageContants.ORDER_SOUCE_IS_NOT_FOUND_CODE);
//				resultMap.put(KEY_MSG, MessageContants.ORDER_SOUCE_IS_NOT_FOUND_MSG + orderSourceName);
//				return resultMap;
//			}
//
//			if (null == lstYYFW.get(0).getSourceName() || lstYYFW.get(0).getSourceName().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:sourceName");
//			}
//			if (null == lstYYFW.get(0).getSourceValue() || lstYYFW.get(0).getSourceValue().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:sourceValue");
//			}
//
//			if (null == lstYYFW.get(0).getSourceType() || lstYYFW.get(0).getSourceType().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:sourceType");
//			}
//			if (null == lstYYFW.get(0).getOrderNoFlag() || lstYYFW.get(0).getOrderNoFlag().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:orderNoFlag");
//			}
//			if (null == lstYYFW.get(0).getOrderNoSequnceName() || lstYYFW.get(0).getOrderNoSequnceName().isEmpty()) {
//				throw new Exception("预约服务订单配置不完善,字段:orderNoSequnceName");
//			}
//			String partnerName = lstYYFW.get(0).getSourceName().trim();
//			Long partnerId = lstYYFW.get(0).getPartnerId();
//			String sourceValue = lstYYFW.get(0).getSourceValue().trim();
//			orderType = lstYYFW.get(0).getSourceType().trim();
//			String orderNoFlag = lstYYFW.get(0).getOrderNoFlag().trim();
//			String orderNoSequnceName = lstYYFW.get(0).getOrderNoSequnceName().trim();
//			String premumsInfo = lstYYFW.get(0).getPremumsInfo() == null ? "" : lstYYFW.get(0).getPremumsInfo().trim();
//
//			/**
//			 * 3.处理sku
//			 */
//			log.info("-----createGeneralFWOrder processSkuProduct");
//			// add by bo.liu 180320 start
//			// 获取赠品信息
//			if (!"".equals(premumsInfo)) {
//				String products = "";
//				for (ReqProduct tmpProd : generalFWOrder.getLstSkuProducts()) {
//					products = products + tmpProd.getSku() + ";";
//				}
//				String premiumsProduct[] = premumsInfo.split(";");
//				List<ReqProduct> tmpLst = generalFWOrder.getLstSkuProducts();
//				for (String str : premiumsProduct) {
//					if (!products.contains(str)) {
//						ReqProduct premiumsPro = new ReqProduct();
//						premiumsPro.setSku(str);
//						premiumsPro.setQuantity(1);
//						tmpLst.add(premiumsPro);
//						generalFWOrder.setLstSkuProducts(tmpLst);
//					}
//				}
//			}
//			// add by bo.liu 180320 end
//			Map<String, Object> productMap = processGeneralSkuProduct(generalFWOrder);
//
//			/**
//			 * 4.解析订单（通过订单唯一标识字段分组订单）；构造目标录入数据库的订单信息；其中有门店的，需要录入门店信息
//			 */
//			Map<String, Object> baseDataMap = new HashMap<String, Object>();
//			baseDataMap.put("orderStatus", orderStatus);
//			baseDataMap.put("creator", "" + curUser.getUserId());
//			baseDataMap.put("orderType", orderType);
//			baseDataMap.put("partnerId", partnerId);
//			baseDataMap.put("partnerName", partnerName);
//			baseDataMap.put("source", sourceValue);// 订单源
//			baseDataMap.put("orderNoFlag", orderNoFlag);// 订单类型标识码
//			baseDataMap.put("orderNoSequnceName", orderNoSequnceName);// 订单生成规则序列号
//			// add by bo.liu 180320
//			// baseDataMap.put("premumsInfo",premumsInfo);
//
//			log.info("-----createGeneralFWOrder processGeneralOrder start");
//			Map<String, Object> processMap = processGeneralOrder(productMap, baseDataMap, generalFWOrder, true);
//			log.info("-----createGeneralFWOrder processGeneralOrder end");
//
//			/**
//			 * 5.录入订单信息，如果有门店信息，需要关联到合伙人 lstNewOrderVo", lstNewOrderVo);
//			 * resultMap.put("lstWorkShop
//			 */
//			List<OrderVo> lstNewOrderVo = (List<OrderVo>) processMap.get("lstNewOrderVo");
//			List<WorkShopVo> lstWorkShop = (List<WorkShopVo>) processMap.get("lstWorkShop");
//			webDDBXService.createOrderAndWorkshopInfoForFWOrder(lstNewOrderVo, lstWorkShop,
//					OrderCommonVo.ORDER_SOURCE_DDYYFW_TYPE, false);
//			String orderNo = lstNewOrderVo.get(0).getOrderNo();
//			resultMap.put(KEY_CODE, MessageContants.SUCCESS_CODE);
//			resultMap.put("orderCode", orderNo);
//		} catch (Exception e) {
//			log.error("", e);
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			log.info(PRE_EXCEPTION_TIP + e.getLocalizedMessage());
//			resultMap.put(KEY_CODE, MessageContants.EXCEPTION_CODE);
//			resultMap.put(KEY_MSG, MessageContants.EXCEPTION_MSG + e.getLocalizedMessage());
//		}
//		return resultMap;
//	}

	public Map<String, Object> checkGeneralFWOrder(ReqGeneralFWOrder order) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (null == order) {
			resultMap.put(KEY_CODE, MessageContants.PARAMS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.PARAMS_NULL_MSG);
			return resultMap;
		}

		if (null == order.getOrderSourceName() || order.getOrderSourceName().isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_SOUCE_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_SOUCE_IS_NULL_MSG);
			return resultMap;
		}
		String regionName = order.getReginName();
		if (null == regionName || regionName.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.REGION_VALIDATE_IS_NOT_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.REGION_VALIDATE_IS_NOT_NULL_MSG);
			return resultMap;
		}
		String receiveUser = order.getContactName();
		String receivePhone = order.getContactTel();
		String receiveAddr = order.getWorkshopAdress();
		if (null == receiveUser || receiveUser.isEmpty() || null == receivePhone || receivePhone.isEmpty()
				|| null == receiveAddr || receiveAddr.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.ORDER_RECEIVEINFO_NOT_PERFECT_CODE);
			resultMap.put(KEY_MSG, MessageContants.ORDER_RECEIVEINFO_NOT_PERFECT_MSG);
			return resultMap;
		}

		List<ReqProduct> lstSkus = order.getLstSkuProducts();
		if (null == lstSkus || lstSkus.isEmpty()) {
			resultMap.put(KEY_CODE, MessageContants.SKU_IS_NULL_CODE);
			resultMap.put(KEY_MSG, MessageContants.SKU_IS_NULL_MSG);
			return resultMap;
		}

		for (ReqProduct product : lstSkus) {
			String sku = product.getSku();
			Integer acount = product.getQuantity();
			if (null == sku || sku.isEmpty() || null == acount || acount < 1) {
				resultMap.put(KEY_CODE, MessageContants.SKU_IS_NULL_CODE);
				resultMap.put(KEY_MSG, MessageContants.SKU_IS_NULL_MSG);
				return resultMap;
			}
		}
		return null;
	}

	public Map<String, Object> processGeneralSkuProduct(ReqGeneralFWOrder order) throws Exception {
		Map<String, Object> reqMap = new HashMap<String, Object>();
		List<String> skus = new ArrayList<String>();
		List<String> skusNew = new ArrayList<String>();
		List<ReqProduct> lstSkus = order.getLstSkuProducts();
		for (ReqProduct temproduct : lstSkus) {
			String sku = temproduct.getSku().trim();
			if (!skus.contains(sku)) {
				skus.add(sku);
			}
		}
		// sku转换 delete by bo.liu 180606
		/*
		 * reqMap.put("tempSkus",skus); List<WxtValueTransformMapVo> lst1 =
		 * valueTransformMapVoMapper.selectBySkus(reqMap); for(ReqProduct
		 * temproduct:lstSkus) { for(WxtValueTransformMapVo v1:lst1) {
		 * if(temproduct.getSku().equals(v1.getValueAfterTransform().trim())) {
		 * temproduct.setSku(v1.getSku()); } }
		 * if(!skusNew.contains(temproduct.getSku())) {
		 * skusNew.add(temproduct.getSku()); } }
		 */
		reqMap.put("skus", skus);
		List<ProductVo> lstProducts = productVoMapper.selectProductByMap(reqMap);
		if (null == lstProducts || lstProducts.isEmpty()) {
			throw new Exception("没有找到合法的产品sku");
		}
		Map<String, Object> productMap = new HashMap<String, Object>();
		for (ProductVo tmpProduct : lstProducts) {
			productMap.put(tmpProduct.getSku(), tmpProduct);
		}
		return productMap;
	}

//	public Map<String, Object> processGeneralOrder(Map<String, Object> productMap, Map<String, Object> baseDataMap,
//			ReqGeneralFWOrder order, boolean isCreateWorkshop) throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		Map<String, Object> reqMap = new HashMap<String, Object>();
//		List<WorkShopVo> lstWorkShop = new ArrayList<WorkShopVo>();
//		Date nowDate = new Date();
//		// 获取机油产品信息
//		List<ReqProduct> lstProduct = order.getLstSkuProducts();
//
//		// 获取机滤产品信息
//		List<OrderProductVo> lstFilterProduct = order.getLstFilterProducts();
//
//		// 构造订单数据
//		List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
//		String serviceTime = order.getServiceTime();
//		String buyUser = order.getBuyUser();
//		String tempworkshopname = order.getWorkshopName();
//		OrderVo currentOrderVo = new OrderVo();
//		currentOrderVo.setOrderType((String) baseDataMap.get("orderType"));
//		currentOrderVo.setSource((String) baseDataMap.get("source"));
//		currentOrderVo.setSourceId((String) baseDataMap.get("source") + 1);
//		if (null == serviceTime || serviceTime.isEmpty()) {
//			currentOrderVo.setServiceTime(DateUtil.addDays(new Date(), 4));
//
//		} else {
//			currentOrderVo.setServiceTime(DateUtil.parseDate(serviceTime, 0));
//		}
//		currentOrderVo.setBuyUserName(buyUser);
//		currentOrderVo.setPlateNumber(order.getPlateNumber());
//		currentOrderVo.setCarType(order.getCarType());
//		currentOrderVo.setCreator((String) baseDataMap.get("creator"));
//		currentOrderVo.setPhoneNo(order.getContactTel());
//		String planteNo = order.getPlateNumber() == null ? "" : order.getPlateNumber();
//		if (!currentOrderVo.getSource().equals("CTYYFW")) {
//			currentOrderVo.setReceiveUserName((order.getContactName() + " ") + planteNo);
//		} else {
//			currentOrderVo.setReceiveUserName(order.getContactName());
//		}
//		currentOrderVo.setReceivePhoneNo(order.getContactTel());
//		currentOrderVo.setWorkshopName(tempworkshopname);
//		currentOrderVo.setAddress(order.getWorkshopAdress());
//		currentOrderVo
//				.setRemark(((String) baseDataMap.get("partnerName") + " " + order.getContactName() + " ") + planteNo);
//		currentOrderVo.setCreateTime(nowDate);
//		currentOrderVo.setUpdateTime(nowDate);
//		currentOrderVo.setStatus((String) baseDataMap.get("orderStatus"));
//		currentOrderVo.setPoOrderPartnerId((Long) baseDataMap.get("partnerId"));
//		currentOrderVo.setRegionName(order.getReginName());
//		currentOrderVo.setReceiveRegionName(order.getReginName());
//		currentOrderVo.setPreferentialTypePrice("预约服务订单");
//		// 生成订单号
//		String orderNoSequnceName = (String) baseDataMap.get("orderNoSequnceName");
//		String orderNoFlag = (String) baseDataMap.get("orderNoFlag");
//		reqMap.put("orderNoSequnceName", orderNoSequnceName);
//		Long tmpSequenceNo = propertiesMapper.getSequenceBySequenceName(reqMap);
//		String sequenceNo = CommonUtil.addZeroForStr("" + tmpSequenceNo, 6, 1);
//		currentOrderVo.setOrderNo(CommonUtil.generateOrderCode(orderNoFlag) + sequenceNo);
//
//		// 构造门店数据
//		// 门店信息
//		if (isCreateWorkshop) {
//			String workshopname = order.getWorkshopName();
//			if (null != workshopname && !workshopname.isEmpty()) {
//				reqMap.put("workShopName", workshopname);// 状态为已下单
//				reqMap.put("source", (String) baseDataMap.get("source"));// 门店来源
//				List<WorkShopVo> lstWokrshopVo = workShopVoMapper.getWorkshopByNameAndSource(reqMap);
//				WorkShopVo workShopVo = null;
//				if (null != lstWokrshopVo && !lstWokrshopVo.isEmpty()) {
//					workShopVo = lstWokrshopVo.get(0);
//				}
//
//				Long workshopId = 0L;
//				if (null == workShopVo) {
//					workShopVo = new WorkShopVo();
//					workShopVo.setWorkShopName(workshopname);
//					workShopVo.setWorkShopAddress(order.getWorkshopAdress());
//					workShopVo.setSource((String) baseDataMap.get("source"));
//					workShopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//					workShopVo.setReserveServiceTel(order.getContactTel());
//					workShopVo.setContactPerson(order.getContactName());
//					workShopVo.setContactPersonTel(order.getContactTel());
//					workShopVo.setHarvestPerson(order.getContactName());
//					workShopVo.setHarvestPersonTel(order.getContactTel());
//					workShopVo.setButtInChargePerson(order.getContactName());
//					workShopVo.setButtInChargePersonTel(order.getContactTel());
//					workShopVo.setCreateTime(new Date());
//					workShopVo.setCreator((String) baseDataMap.get("creator"));
//					workShopVo.setPartnerId("" + (Long) baseDataMap.get("partnerId"));
//					workShopVo.setPartnerName((String) baseDataMap.get("partnerName"));
//					workShopVoMapper.insertSelective(workShopVo);
//					lstWorkShop.add(workShopVo);
//				}
//				workshopId = workShopVo.getId();
//				currentOrderVo.setWorkShopId(workshopId);
//			}
//		}
//
//		// 重组订单产品数据
//		List<OrderLineVo> lstOrderLineVo = new ArrayList<OrderLineVo>();
//		for (ReqProduct tempProduct : lstProduct) {
//
//			String sku = tempProduct.getSku();
//			ProductVo currentProDouct = (ProductVo) productMap.get(sku);
//			if (null == currentProDouct) {
//				throw new Exception("没有找到有效的sku:" + sku + " 收货人:" + order.getContactName().trim());
//			}
//			OrderLineVo orderLineVo = new OrderLineVo();
//			int amount = 1;
//			if (null != tempProduct.getQuantity()) {
//				amount = tempProduct.getQuantity();
//			}
//			// 需要转换成瓶 add by bo.liu 180917 走的接口
//			// amount = amount*currentProDouct.getBottleQty();//转换成瓶码
//
//			orderLineVo.setAmount(amount);
//			orderLineVo.setActualAmount(amount);
//			orderLineVo.setSku(sku);
//			orderLineVo.setCreateTime(nowDate);
//			orderLineVo.setCreator((String) baseDataMap.get("creator"));
//			orderLineVo.setPrice(currentProDouct.getSalePrice());
//			orderLineVo.setProductId(currentProDouct.getId());
//			orderLineVo.setProductName(currentProDouct.getName());
//			orderLineVo.setUnits(currentProDouct.getUnits());
//			orderLineVo.setType(currentProDouct.getCategory());
//			orderLineVo.setStatus("1");
//			lstOrderLineVo.add(orderLineVo);
//
//		}
//		// 重组机滤
//		if (null != lstFilterProduct && !lstFilterProduct.isEmpty()) {
//			for (OrderProductVo filterProduct : lstFilterProduct) {
//				OrderLineVo orderLineVo = new OrderLineVo();
//				int amount = 1;
//				if (null != filterProduct.getQuantity()) {
//					amount = filterProduct.getQuantity();
//				}
//				orderLineVo.setAmount(amount);
//				orderLineVo.setActualAmount(amount);
//				orderLineVo.setSku(filterProduct.getSku());
//				orderLineVo.setCreateTime(nowDate);
//				orderLineVo.setCreator((String) baseDataMap.get("creator"));
//				orderLineVo.setProductName(filterProduct.getName());
//				orderLineVo.setUnits(filterProduct.getUnits());
//				orderLineVo.setType("JL");
//				orderLineVo.setStatus("1");
//				lstOrderLineVo.add(orderLineVo);
//			}
//		}
//		currentOrderVo.setOrderLines(lstOrderLineVo);
//		lstNewOrderVo.add(currentOrderVo);
//		log.info("processGeneralOrder----lstNewOrderVo:" + lstNewOrderVo);
//		resultMap.put("lstNewOrderVo", lstNewOrderVo);
//		resultMap.put("lstWorkShop", lstWorkShop);
//		return resultMap;
//	}
	// 预约服务订单导入----end//

	@Override
	public Map<String, Object> manualOperationXYCTask() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		confirmService.doHandleOrderForConfirm();
		resultMap.put("code", "success");
		return resultMap;
	}
}
