package com.sys.quartz.job;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.pms.dao.DmpMapper;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;
import com.common.util.WeixinMessageUtil;
import com.sys.log.business.LogBizService;
import com.sys.log.model.Log;

public class DmpSynData {

	@Autowired
	private DmpMapper dmpMapper;
    
    @Autowired
    private LogBizService logBizService;
	
	public synchronized void execute(){
		final Log log = new Log();
		final Date now = DateUtil.getCurrentDate();
		try {
			dmpMapper.synData();
		} catch (Exception e) {
			log.setExtProperty3("ERROR");
			log.setExtProperty4(e.getMessage());
			WeixinMessageUtil.weixinAlarmMessagePush("PP-BI数据同步", "PP推BI数据失败", "数据同步失败告警", "synData", e.getMessage());
		}
		log.setOperator(1l);
		log.setLogType("Dmp.sysLog");
		log.setExtProperty1("synData");
		log.setCreateTime(DateUtil.getCurrentDate());
		log.setExtProperty2((DateUtil.getCurrentDate().getTime() - now.getTime()) + "");
		try {
			logBizService.insert(log);
		} catch (WxPltException e) {
			e.printStackTrace();
		}
	}
}
