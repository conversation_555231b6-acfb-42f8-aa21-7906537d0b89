INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.organization_id', '366', '门店直播机构ID');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.token.url', 'http://ecrm-suzhou.woaap.com/oauth/token', '门店直播获取token URL');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.token.client_id', '100001', '门店直播获取token客户端ID');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.token.client_secret', 'd0TpoNpRr1hnzU4IFlKMeEFWIhASG2jdMQkJdYq1', '门店直播获取token客户端秘钥');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.token.grant_type', 'client_credentials', '门店直播获取token授权类');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.token.scope', '*', '门店直播获取token请求作用域');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synCompany.url', 'http://ecrm-suzhou.woaap.com/public-api/company/save', '门店直播同步经销商URL');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synWorkshop.url', 'http://ecrm-suzhou.woaap.com/public-api/shop/sync', '门店直播同步门店URL');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synWorkshop.province', '110000', '门店直播同步门店province');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synWorkshop.city', '111000', '门店直播同步门店city');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synWorkshop.district', '110101', '门店直播同步门店district');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synWorkshop.brand_id', '72', '门店直播同步门店brand_id');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synWorkshop.brand_name', '雪佛龙品牌', '门店直播同步门店brand_name');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.delWorkshop.url', 'http://ecrm-suzhou.woaap.com/public-api/shop/delete', '门店直播删除门店URL');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.registerWorkshopEmployee.url', 'http://ecrm-suzhou.woaap.com/public-api/member/register', '门店直播注册技师URL');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.updateWorkshopEmployee.url', 'http://ecrm-suzhou.woaap.com/public-api/member/update', '门店直播更新技师URL');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.orderRefund.url', 'http://etoshop-chevron-oms.woaap.com/api/order/index?service_name=RefundClosed', '门店直播订单退款URL');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.appkey', '5143d9Ee84676f8BC426cc9bd5fFBdE4', '门店直播appkey');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.appsecret', 'b0abDFe57441EBb025d3a8aEad41BE49', '门店直播appsecret');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('Eto.synProduct.url', 'http://etoshop-chevron-oms.woaap.com/api/product/index?service_name=Create', '门店直播同步产品URL');

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('CommonUtil.notifyIt.email', '<EMAIL>', '被通知IT邮箱');
INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('WorkshopEmployee.synB2bMechnicOn', 'Y', '技师开启同步大咖汇');
