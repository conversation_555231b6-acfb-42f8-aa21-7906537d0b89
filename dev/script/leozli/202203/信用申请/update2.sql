update cc1 set cc1.bu=credit_rs1.bu
from wx_t_credit_customer_info_final cc1 
left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 ON cc1.sales_cai = credit_bcs1.sales_cai
				LEFT JOIN dw_region_sales_channel_rel credit_rs1 ON credit_rs1.region_name = credit_bcs1.region

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Supervisor'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Supervisor'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Supervisor'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Supervisor'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Supervisor'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Supervisor'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Supervisor'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Supervisor'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Supervisor'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Supervisor'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Channel_Manager'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Channel_Manager'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Channel_Manager'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Channel_Manager'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Channel_Manager'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Channel_Manager'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Channel_Manager'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Channel_Manager'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Channel_Manager'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Channel_Manager'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_CSR'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_CSR'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_CSR'), 'Credit.apply', 128, 1, '信用申请。128-代FLSR申请', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CVX_Direct_ABM'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 32, 1, '信用申请。32-全部tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 32, 1, '信用申请。32-全部tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 32, 1, '信用申请。32-全部tab', 1, getdate());

--insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 64, 1, '信用申请。64-reassign按钮', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 256, 1, '信用申请。256-Excel导出', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 256, 1, '信用申请。256-Excel导出', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 256, 1, '信用申请。256-Excel导出', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 512, 1, '信用申请。512-Credit Team查看', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 512, 1, '信用申请。512-Credit Team查看', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 512, 1, '信用申请。512-Credit Team查看', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 1024, 1, '信用申请。1024-休假设置', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 1024, 1, '信用申请。1024-休假设置', 1, getdate());
--insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 1024, 1, '信用申请。1024-休假设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 2048, 1, '信用申请。2048-Credit Team notify', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 2048, 1, '信用申请。2048-Credit Team notify', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 2048, 1, '信用申请。2048-Credit Team notify', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='China_Finance_Manager'), 'Credit.apply', 4096, 1, '信用申请。4096-计算财务附件', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 4096, 1, '信用申请。4096-计算财务附件', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 4096, 1, '信用申请。4096-计算财务附件', 1, getdate());

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('Credit.workflowStatus', '信用申请流程状态配置', '信用申请流程状态配置');
--delete d from wx_t_dic_item d where dic_type_code='Credit.workflowStatus'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Credit.workflowStatus', '100', 'Approved 已审批', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Credit.workflowStatus', '10|20|40', 'In Approval 审批中', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Credit.workflowStatus', '30', 'Rejected 已拒绝', '', '1',30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Credit.workflowStatus', '0', 'To Be Submitted 未提交', '', '1',40);

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CVX_Direct_ABM'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CVX_Direct_ABM'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CVX_Direct_ABM'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CVX_Direct_ABM'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Team_Lead'), 'Credit.apply', 8192, 1, '信用申请。8192-CV Release按钮权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Local_Credit_Analyst'), 'Credit.apply', 8192, 1, '信用申请。8192-CV Release按钮权限', 1, getdate());

insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Industrial_Sales' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_OEM_Sales' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CVX_Direct_ABM' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_OEM_Supervisor' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_OEM_Channel_Manager' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Industrial_Supervisor' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Industrial_Channel_Manager' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)

insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Industrial_Sales' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_OEM_Sales' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CVX_Direct_ABM' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_OEM_Supervisor' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_OEM_Channel_Manager' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Industrial_Supervisor' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Industrial_Channel_Manager' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)


INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.request.token', N'chevronWorkFlow', N'bpm服务接口令牌', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.get.done', N'http://127.0.0.1:8089/task/getDoneTaskInfo', N'bpm服务，获取已办任务', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.process.start', N'http://127.0.0.1:8089/process/startProcess', N'bpm服务，启动流程', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.complete', N'http://127.0.0.1:8089/task/completeTask', N'bpm服务，完成任务', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.todolist', N'http://127.0.0.1:8089/task/queryToDoTaskList', N'bpm服务，获取待办任务列表', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.donelist', N'http://127.0.0.1:8089/task/queryHasDoneTaskList', N'bpm服务，获取已办任务列表', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.get.todo', N'http://127.0.0.1:8089/task/getToDoTaskInfo', N'bpm服务，获取待办任务', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.history.comment', N'http://127.0.0.1:8089/task/queryTaskDealLog', N'bpm服务，查询任务处理日志', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.history.step', N'http://127.0.0.1:8089/process/getWorkNodes', N'bpm服务，查询完整处理流程节点', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.recall', N'http://127.0.0.1:8089/task/recallTask', N'bpm服务，撤回任务', null, null, null, null);
INSERT INTO wx_t_properties (codetype, code, codename, make_time, operator, modify_time, modify_operator) VALUES (N'bpm.task.reassign', N'http://127.0.0.1:8089/task/transferTask', N'bpm服务，转单', null, null, null, null);

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Sales'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Sales'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Sales'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Sales'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Supervisor'), 'Credit.apply', 1, 1, '信用申请。1-年度申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Supervisor'), 'Credit.apply', 2, 1, '信用申请。2-临时申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Supervisor'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Supervisor'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_EC_Supervisor'), 'Credit.apply', 16, 1, '信用申请。16-我审批的tab', 1, getdate());

insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_EC_Sales' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_EC_Sales' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)

insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_EC_Supervisor' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_EC_Supervisor' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)

insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_SAP_CSR' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_SAP_CSR' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SAP_CSR'), 'Credit.apply', 4, 1, '信用申请。4-CV申请按钮', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SAP_CSR'), 'Credit.apply', 8, 1, '信用申请。8-我发起的tab', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SAP_CSR'), 'Credit.apply', 128, 1, '信用申请。128-代FLSR申请', 1, getdate());
