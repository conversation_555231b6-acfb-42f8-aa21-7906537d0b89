select top 100 * 
--update b set b.RELATED_CODE=replace(b.RELATED_CODE,'CDM_PIT_PACK_20L', 'CONSUMER_HERO_PRODUCT_POINT'), ATTRIBUTE2='CONSUMER_HERO_PRODUCT_POINT',ATTRIBUTE3='consumer_hero_product'
from wx_t_point_business b where b.BUSINESS_TYPE_CODE= 'SELL_IN_PROMOTION_DELIVERY_V2'--B2021051818154/1123/94/CDM_SS_PRODUCT_POINT
and b.RELATED_CODE like '%/95/%'
order by id desc;

select top 100 * from wx_t_promotion_point

select  top 100 * from wx_t_promotion_delivery_detail order by id desc;