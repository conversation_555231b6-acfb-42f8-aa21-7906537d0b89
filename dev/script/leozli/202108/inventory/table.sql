/*==============================================================*/
/* Module: INVENTORY2021                                        */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_product_inventory                                */
/*==============================================================*/
create table wx_t_product_inventory (
	id                               bigint                                     not null,
	org_type                         nvarchar(30)                               not null,
	org_id                           bigint                                     not null,
	sku                              nvarchar(30)                               not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	version_no                       bigint                                     not null,
	current_quantity                 int                                            null,
	primary key (id)
);
ALTER TABLE wx_t_product_inventory ADD CONSTRAINT uk_product_inventory UNIQUE (org_type,org_id,sku);
CREATE INDEX ix_product_inventory_KCZT ON wx_t_product_inventory (org_type,org_id);

/*==============================================================*/
/* Table: wx_t_product_inventory_log                            */
/*==============================================================*/
create table wx_t_product_inventory_log (
	id                               bigint                                     not null,
	inventory_id                     bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	original_quantity                int                                        not null,
	modify_quantity                  int                                        not null,
	business_type                    int                                        not null,
	business_key                     nvarchar(64)                               not null,
	ext_property1                    nvarchar(512)                                  null,
	ext_property2                    nvarchar(512)                                  null,
	ext_property3                    nvarchar(512)                                  null,
	ext_property4                    nvarchar(512)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	primary key (id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_product_inventory_product        */
/*==============================================================*/
CREATE INDEX ix_product_inventory_product ON wx_t_product_inventory (sku);

/*==============================================================*/
/* Foreign Key(only index): ix_pro_inv_log_pro_inv              */
/*==============================================================*/
CREATE INDEX ix_pro_inv_log_pro_inv ON wx_t_product_inventory_log (inventory_id);
