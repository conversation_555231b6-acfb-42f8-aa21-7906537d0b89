<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.b2b.dao.ScheduleItemMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.b2b.model.ScheduleItem">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="input_ctrl_id" property="inputCtrlId" jdbcType="BIGINT"/>
		<result column="schedule_id" property="scheduleId" jdbcType="BIGINT"/>
		<result column="caption" property="caption" jdbcType="VARCHAR"/>
		<result column="input_ctrl_config" property="inputCtrlConfig" jdbcType="VARCHAR"/>
		<result column="required_flag" property="requiredFlag" jdbcType="INTEGER"/>
		<result column="order_numb" property="orderNumb" jdbcType="NUMERIC"/>
		<result column="input_ctrl_name" property="inputCtrlName" jdbcType="VARCHAR"/>
		<result column="ctrl_height" property="ctrlHeight" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,input_ctrl_id,schedule_id,caption,input_ctrl_config,required_flag,order_numb,ctrl_height
	</sql>
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.b2b.model.ScheduleItem">
		update wx_t_schedule_item
		<set>
			<if test="inputCtrlId != null" >
				input_ctrl_id = #{inputCtrlId,jdbcType=BIGINT},
			</if>
			<if test="scheduleId != null" >
				schedule_id = #{scheduleId,jdbcType=BIGINT},
			</if>
			<if test="caption != null" >
				caption = #{caption,jdbcType=VARCHAR},
			</if>
			<if test="inputCtrlConfig != null" >
				input_ctrl_config = #{inputCtrlConfig,jdbcType=VARCHAR},
			</if>
			<if test="requiredFlag != null" >
				required_flag = #{requiredFlag,jdbcType=INTEGER},
			</if>
			<if test="orderNumb != null" >
				order_numb = #{orderNumb,jdbcType=NUMERIC},
			</if>
			<if test="ctrlHeight != null" >
				ctrl_height = #{ctrlHeight,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.b2b.model.ScheduleItemExample">
    	delete from wx_t_schedule_item
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleItemExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_schedule_item
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.b2b.model.ScheduleItem">
		insert into wx_t_schedule_item
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="inputCtrlId != null">
				input_ctrl_id,
			</if>
			<if test="scheduleId != null">
				schedule_id,
			</if>
			<if test="caption != null">
				caption,
			</if>
			<if test="inputCtrlConfig != null">
				input_ctrl_config,
			</if>
			<if test="requiredFlag != null">
				required_flag,
			</if>
			<if test="orderNumb != null">
				order_numb,
			</if>
			<if test="ctrlHeight != null">
				ctrl_height,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="inputCtrlId != null">
				#{inputCtrlId,jdbcType=BIGINT},
			</if>
			<if test="scheduleId != null">
				#{scheduleId,jdbcType=BIGINT},
			</if>
			<if test="caption != null">
				#{caption,jdbcType=VARCHAR},
			</if>
			<if test="inputCtrlConfig != null">
				#{inputCtrlConfig,jdbcType=VARCHAR},
			</if>
			<if test="requiredFlag != null">
				#{requiredFlag,jdbcType=INTEGER},
			</if>
			<if test="orderNumb != null">
				#{orderNumb,jdbcType=NUMERIC},
			</if>
			<if test="ctrlHeight != null">
				#{ctrlHeight,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_schedule_item
		<set>
			<if test="record.inputCtrlId != null">
				input_ctrl_id = #{record.inputCtrlId,jdbcType=BIGINT},
			</if>
			<if test="record.scheduleId != null">
				schedule_id = #{record.scheduleId,jdbcType=BIGINT},
			</if>
			<if test="record.caption != null">
				caption = #{record.caption,jdbcType=VARCHAR},
			</if>
			<if test="record.inputCtrlConfig != null">
				input_ctrl_config = #{record.inputCtrlConfig,jdbcType=VARCHAR},
			</if>
			<if test="record.requiredFlag != null">
				required_flag = #{record.requiredFlag,jdbcType=INTEGER},
			</if>
			<if test="record.orderNumb != null">
				order_numb = #{record.orderNumb,jdbcType=NUMERIC},
			</if>
			<if test="record.ctrlHeight != null">
				ctrl_height = #{record.ctrlHeight,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.b2b.model.ScheduleItemExample">
		delete from wx_t_schedule_item
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.b2b.model.ScheduleItemExample">
		select count(1) from wx_t_schedule_item
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleItemExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_schedule_item
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.input_ctrl_id, t1.schedule_id, t1.caption, t1.input_ctrl_config, t1.required_flag, t1.order_numb, ic.ctrl_title input_ctrl_name,
			 t1.ctrl_height
		  from wx_t_schedule_item t1
		  join wx_t_input_ctrl ic on t1.input_ctrl_id=ic.id
		 where 1=1
		 <if test="scheduleId != null">
		 and t1.schedule_id=#{scheduleId, jdbcType=BIGINT}
		 </if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_schedule_item (input_ctrl_id, schedule_id, caption, input_ctrl_config, required_flag, order_numb, ctrl_height) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.inputCtrlId, jdbcType=BIGINT}, #{item.scheduleId, jdbcType=BIGINT}, #{item.caption, jdbcType=VARCHAR}, #{item.inputCtrlConfig, jdbcType=VARCHAR}, #{item.requiredFlag, jdbcType=INTEGER}, #{item.orderNumb, jdbcType=NUMERIC}, #{item.ctrlHeight, jdbcType=INTEGER}
			</trim>
		</foreach>
	</insert>
</mapper>
