package com.chevron.esorder.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.esorder.business.EsOrderBizService;
import com.chevron.task.controller.TaskServController;
import com.common.util.MessageResourceUtil;

/**
 * 
 * @Author: bo.liu  2017-4-12 下午1:04:21 
 * @Version: $Id$
 * @Desc: <p>网店管家订单控制类</p>
 */
@Controller
@RequestMapping(value = "/esorder")
public class EsOrderController {
	private final static Logger log = Logger.getLogger(TaskServController.class);
	
	@Resource 
	EsOrderBizService esOrderService;
	
	/**
	 * 获取订单汇总，，测试
	 * <AUTHOR> 2017-4-12 下午1:07:54
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getSalesOrders.do", method = { RequestMethod.POST })
	public Map<String, Object> querySalesOrders(HttpServletRequest request,HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			
		//	String resultInfo = esOrderService.getSalesOrders();
		//	log.info("getSalesOrders------resultInfo:"+resultInfo);

		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("error", e);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/getSellBackOrders.do", method = { RequestMethod.POST })
	public Map<String, Object> querySellBackOrders(HttpServletRequest request,HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			
			//String resultInfo = esOrderService.getSellBackOrders();
			//log.info("getSellBackOrders------resultInfo:"+resultInfo);

		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("error", e);
		}
		return resultMap;
	}

}
