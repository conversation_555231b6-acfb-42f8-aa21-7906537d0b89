package com.sys.utils.controller;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.utils.business.BusinessBetweenSpConfBizService;
import com.sys.utils.model.BusinessBetweenSpConfParams;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 合伙人之间合作设置Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2017-10-25 10:50
 */
@Controller
@RequestMapping(value="/businessbetweenspconf")
public class BusinessBetweenSpConfController {
	@Autowired
	private BusinessBetweenSpConfBizService businessBetweenSpConfBizService;
	
	private final static Logger log = Logger.getLogger(BusinessBetweenSpConfController.class);
	
	/**
	 * 合伙人之间合作设置列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(BusinessBetweenSpConfParams params){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			businessBetweenSpConfBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}
	
	/**
	 * 合伙人之间合作设置列表页面详细查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/quertyDetailData.do")
	public Map<String,Object> queryDetailForPage(BusinessBetweenSpConfParams params){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			businessBetweenSpConfBizService.queryDetailForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}
	
}
