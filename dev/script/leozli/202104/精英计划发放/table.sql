/*==============================================================*/
/* Module: QBR                                                  */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_qbr_cdm_performance_adjust                       */
/*==============================================================*/
create table wx_t_qbr_cdm_performance_adjust (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	quarter                          int                                            null,
	asm_comment                      nvarchar(4000)                                 null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_sop_other_support_detail                         */
/*==============================================================*/
create table wx_t_sop_other_support_detail (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	partner_id                       bigint                                         null,
	channel                          nvarchar(64)                                   null,
	quarter                          nvarchar(64)                                   null,
	amount                           decimal(20,6)                                  null,
	target_month                     nvarchar(64)                                   null,
	remark_code                      nvarchar(64)                                   null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_sop_support_summary                              */
/*==============================================================*/
create table wx_t_sop_support_summary (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	partner_id                       bigint                                         null,
	channel                          nvarchar(64)                                   null,
	year                             nvarchar(64)                                   null,
	quarter                          nvarchar(64)                                   null,
	signage                          decimal(20,6)                                  null,
	seminar                          decimal(20,6)                                  null,
	oil_change_car                   decimal(20,6)                                  null,
	equipment_tools                  decimal(20,6)                                  null,
	other_amount                     decimal(20,6)                                  null,
	status                           int                                        not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_sop_support_adjust_apply                         */
/*==============================================================*/
create table wx_t_sop_support_adjust_apply (
	id                               bigint                            not null identity,
	req_no                           nvarchar(64)                                   null,
	req_user_id                      bigint                                         null,
	req_time                         datetime                                       null,
	form_status                      int                                        not null,
	approval_completion_time         datetime                                       null,
	channel                          nvarchar(64)                                   null,
	year                             nvarchar(64)                                   null,
	quarter                          nvarchar(64)                                   null,
	signage                          decimal(20,6)                                  null,
	seminar                          decimal(20,6)                                  null,
	oil_change_car                   decimal(20,6)                                  null,
	equipment_tools                  decimal(20,6)                                  null,
	other_amount                     decimal(20,6)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_sop_support_adjust_apply ADD CONSTRAINT uk_sop_support_adjust_apply UNIQUE (req_no);

/*==============================================================*/
/* Table: wx_t_qbr_partner_remark_record                        */
/*==============================================================*/
create table wx_t_qbr_partner_remark_record (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	year                             nvarchar(64)                                   null,
	quarter                          nvarchar(64)                                   null,
	partner                          nvarchar(64)                                   null,
	remark                           nvarchar(1024)                                 null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_sop_amount_allot                                 */
/*==============================================================*/
create table wx_t_sop_amount_allot (
	id                               bigint                            not null identity,
	year                             nvarchar(64)                                   null,
	partner_id                       bigint                                         null,
	sop_amount                       decimal(20,6)                                  null,
	signage_flag                     bit                                            null,
	seminar_flag                     bit                                            null,
	oil_change_car_flag              bit                                            null,
	equipment_tools_flag             bit                                            null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_qbr_cdm_per_adj_log                              */
/*==============================================================*/
create table wx_t_qbr_cdm_per_adj_log (
	id                               bigint                            not null identity,
	adjust_id                        bigint                                     not null,
	property_name                    nvarchar(64)                                   null,
	log_type                         int                                        not null,
	value_offset                     decimal(20,6)                                  null,
	value_final                      decimal(20,6)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_qb_cd_pe_ad_lo_qb_cd_pe_ad       */
/*==============================================================*/
CREATE INDEX ix_qb_cd_pe_ad_lo_qb_cd_pe_ad ON wx_t_qbr_cdm_per_adj_log (adjust_id);
