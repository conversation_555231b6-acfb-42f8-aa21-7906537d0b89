package com.sys.push.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.common.util.SendMessageUtil;
import com.sys.delegate.model.User;
import net.sf.json.JSONObject;

import org.apache.shiro.crypto.hash.Hash;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.baidu.yun.core.log.YunLogEvent;
import com.baidu.yun.core.log.YunLogHandler;
import com.baidu.yun.push.auth.PushKeyPair;
import com.baidu.yun.push.client.BaiduPushClient;
import com.baidu.yun.push.constants.BaiduPushConstants;
import com.baidu.yun.push.exception.PushClientException;
import com.baidu.yun.push.exception.PushServerException;
import com.baidu.yun.push.model.PushMsgToSingleDeviceRequest;
import com.baidu.yun.push.model.PushMsgToSingleDeviceResponse;
import com.chevron.task.service.JobManageServiceI;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.push.business.MessageBizService;
import com.sys.push.dao.WxPushMessageMapper;
import com.sys.push.model.Message;
import com.sys.push.model.PushMessageParams;
import com.sys.push.model.WxPushMessage;
import com.sys.push.model.WxPushMessageExample;
import com.sys.push.service.MessagePushService;
import com.sys.quartz.dao.ScheduleJobMapper;
import com.sys.quartz.model.ScheduleJob;
import com.sys.quartz.model.ScheduleJobExample;
import com.sys.quartz.service.impl.JobTaskService;

@Service
public class MessagePushServiceImpl implements MessagePushService {

	@Resource
	WxTUserMapper wxTUserMapper;

	@Resource
	WxPushMessageMapper wxPushMessageMapper;
	
	@Autowired
	ScheduleJobMapper scheduleJobMapper;
	
	@Resource
	JobManageServiceI jobManageService;
	
	@Resource
	JobTaskService jobService;
	
	@Autowired
	private MessageBizService messageBizService;
	
	/** 待尝试推送数组 */
	private List<WxPushMessage> tryPushList = new ArrayList<WxPushMessage>();
	
	@Resource
	private DicService dicServiceImpl;
	
	public final static String JOB_ID = "taskExecutor";
	
	private final static int BATCH_SIZE = 160;

	private static Logger logger = LoggerFactory.getLogger(MessagePushService.class);
	
	public MessagePushServiceImpl(){
		//开启尝试推送任务
		new Thread(){
			int maxTryTimes = 1;
			long minInterval = 5000;
			@Override
			public void run() {
				do {
					synchronized (MessagePushServiceImpl.this) {
						if(tryPushList.isEmpty()){
							try {
								MessagePushServiceImpl.this.wait();
							} catch (InterruptedException e) {
								logger.error("消息推送尝试任务线程异常", e);
							}
						}
					}
					//1. 获取尝试推送配置
					getTryPushConfig();
					//2. 取待推送列表
					List<WxPushMessage> taskList;
					synchronized (MessagePushServiceImpl.this) {
						taskList = tryPushList;
						tryPushList = new ArrayList<WxPushMessage>();
					}
					//3. 推送待尝试推送列表中消息
					pushMessage(taskList);
					//4. 等待下一次推送尝试
					try {
						Thread.sleep(minInterval);
					} catch (InterruptedException e) {
						logger.error("消息推送尝试任务线程异常", e);
					}
				} while (true);
			}
			private void pushMessage(List<WxPushMessage> taskList){
				List<WxPushMessage> toAndroidList = new ArrayList<WxPushMessage> ();
				List<WxPushMessage> toIosList = new ArrayList<WxPushMessage> ();
				long time = Calendar.getInstance().getTimeInMillis();
				for (WxPushMessage pushMessage : taskList) {
					if(pushMessage.getTryTimes() >= maxTryTimes){
						//已尝试最大尝试次数
						continue;
					} if(time - pushMessage.getPretryTime() < minInterval){
						//未到时间，进入下一次尝试
						addTryPushTask(pushMessage);
						continue;
					}
					//更新当前尝试次数
					pushMessage.setTryTimes(pushMessage.getTryTimes() + 1);
					//把pushMessage list分别加入Android, iOS两个发送对象列表
					if (pushMessage.getDeviceType().equals("android")) {
						toAndroidList.add(pushMessage);
					}else if (pushMessage.getDeviceType().equals("ios")) {
						toIosList.add(pushMessage);
					}
				}
				//批量发送推送消息给android用户
				pushMessageListToAndroid(toAndroidList);
				//批量发送推送消息给ios用户
				pushMessageListToIOS(toIosList);
			}
			@SuppressWarnings("unchecked")
			private void getTryPushConfig(){
				if(dicServiceImpl != null){
					Map<String, Object> dicMap = dicServiceImpl.getDicItemByDicTypeCode("PushMessage.tryPush");
					if("success".equals(dicMap.get("result"))){
						List<DicItemVo> list = (List<DicItemVo>) dicMap.get("data");
						if(list != null && !list.isEmpty()){
							for(DicItemVo vo : list){
								if("maxTryTimes".equals(vo.getDicItemCode())){
									try {
										maxTryTimes = Integer.parseInt(vo.getDicItemName());
									} catch (Exception e) {
										logger.warn("转换消息尝试推送maxTryTimes配置失败", e);
									}
								}else if("minInterval".equals(vo.getDicItemCode())){
									try {
										minInterval = Integer.parseInt(vo.getDicItemName()) * 1000;
									} catch (Exception e) {
										logger.warn("转换消息尝试推送minInterval配置失败", e);
									}
								}
							}
						}
					}else{
						logger.error("转换消息尝试推送配置失败。" + dicMap.get("errorMsg"));
					}
				}
			}
		}.start();
	}
	
	protected synchronized void addTryPushTask(WxPushMessage pushMessage){
		tryPushList.add(pushMessage);
		notifyAll();
	}

	/**
	 * 扫描所有未发送成功的消息列表，然后推送出去
	 * 也可供定期调用，重新推送未发送成功的消息列表
	 */
	@Override
	public synchronized void sendMessageList() {
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("pushStatus", WxPushMessage.PUSH_STATUS_UNPUSH);
			List<WxPushMessage> messageList = wxPushMessageMapper.queryPushMessageByParams(params);
			if (null != messageList && messageList.size() > 0) {
				List<WxPushMessage> toAndroidList = new ArrayList<WxPushMessage> ();
				List<WxPushMessage> toIosList = new ArrayList<WxPushMessage> ();
				for (WxPushMessage pushMessage : messageList) {
					//把pushMessage list分别加入Android, iOS两个发送对象列表
					if (null != pushMessage && pushMessage.getDeviceType().equals("android") && StringUtils.isNotEmpty(pushMessage.getDeviceId())) {
						toAndroidList.add(pushMessage);
					}
					if (null != pushMessage && pushMessage.getDeviceType().equals("ios") && StringUtils.isNotEmpty(pushMessage.getDeviceId())) {
						toIosList.add(pushMessage);
					}
				}
				//批量发送推送消息给android用户
				pushMessageListToAndroid(toAndroidList);
				
				//批量发送推送消息给ios用户
				pushMessageListToIOS(toIosList);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("发送推送消息失败。" + e.getMessage(), e);
		}
	}

	/**
	 * 批量发送推送消息给android用户
	 */
	private void pushMessageListToAndroid(List<WxPushMessage> messageList) {
		if (null != messageList && messageList.size() > 0) {
			for (WxPushMessage pushMessage : messageList) {
				String apiKey = WxPushMessage.API_KEY_ANDROID;
				String secretKey = WxPushMessage.SECRET_KEY_ANDROID;
				
				//设置消息内容格式
				JSONObject params = new JSONObject();
				params.put("msgID", pushMessage.getMessageId());
				params.put("extraInfo", pushMessage.getExtraInfo());
				JSONObject notification = new JSONObject();
				notification.put("title", (WxPushMessage.MSG_TYPE_PUBLIC.equals(pushMessage.getMessageType()) ? "公共消息" : "个人消息") +
                        "-" + pushMessage.getMessageTitle());	//默认值为app应用名
				notification.put("description", StringUtils.isBlank(pushMessage.getContent())?pushMessage.getMessageTitle():pushMessage.getContent());
				notification.put("open_type", 2);
				notification.put("pkg_content", "#Intent;component=com.chevron.www/.activities.new0407.MsgDetailActivity;end");
				notification.put("custom_content", params.toString());
				//发送端设备的唯一标识
				String channelId = pushMessage.getDeviceId();
				
				PushMsgToSingleDeviceRequest request = new PushMsgToSingleDeviceRequest()
						.addChannelId(channelId)			//设备的唯一标识数组
						.addMessageType(1)					//消息类型: 0为透传消息,1为通知,默认为0
						.addMessage(notification.toString())
						.addDeviceType(3);					//设备类型: 3 for android, 4 for ios
				pushMessage(pushMessage, apiKey, secretKey, request);
					
			}
		}
	}

	
	private void pushMessage(WxPushMessage pushMessage, String apiKey, String secretKey, PushMsgToSingleDeviceRequest request){
		PushKeyPair pair = new PushKeyPair(apiKey, secretKey);
		BaiduPushClient pushClient = new BaiduPushClient(pair, BaiduPushConstants.CHANNEL_REST_URL);
		pushClient.setChannelLogHandler (new YunLogHandler () {
			@Override
			public void onHandle (YunLogEvent event) {
				logger.info("eventMessage: {}", event.getMessage());
			}
		});
		
		try {
			PushMsgToSingleDeviceResponse response = pushClient.pushMsgToSingleDevice(request);
			logger.info("msgId: {}, sendTime: {}", response.getMsgId(), response.getSendTime());
			//发送成功，更新状态
			pushMessage.setPushStatus(WxPushMessage.PUSH_STATUS_PUSHED);
			pushMessage.setXgTime(new Date());
			pushMessage.setPushMsgId(response.getMsgId());
			wxPushMessageMapper.updateByPrimaryKeySelective(pushMessage);
		} catch (PushClientException e) {
			logger.error("push message client exception", e);
			pushMessage.setXgTime(new Date());
			pushMessage.setPushStatus(WxPushMessage.PUSH_STATUS_FAIL);
			pushMessage.setErrorMessage(e.getMessage());
			wxPushMessageMapper.updateByPrimaryKeySelective(pushMessage);
			//更新最后尝试时间
			pushMessage.setPretryTime(Calendar.getInstance().getTimeInMillis());
			addTryPushTask(pushMessage);
		} catch (PushServerException e) {
			logger.error("push message server exception", e);
			pushMessage.setXgTime(new Date());
			pushMessage.setPushStatus(WxPushMessage.PUSH_STATUS_FAIL);
			pushMessage.setErrorMessage(e.getErrorMsg());
			wxPushMessageMapper.updateByPrimaryKeySelective(pushMessage);
			//更新最后尝试时间
			pushMessage.setPretryTime(Calendar.getInstance().getTimeInMillis());
			addTryPushTask(pushMessage);
		}
	}
	
	/**
	 * 批量发送推送消息给ios用户
	 * @param messageList
	 * @throws Exception
	 */
	private void pushMessageListToIOS(List<WxPushMessage> messageList) {
		if (null != messageList && messageList.size() > 0) {
			for (WxPushMessage pushMessage : messageList) {
				String apiKey = WxPushMessage.API_KEY_IOS;
				String secretKey = WxPushMessage.SECRET_KEY_IOS;
				
				//设置消息内容格式
				JSONObject notification = new JSONObject();
				JSONObject jsonAPS = new JSONObject();
                JSONObject alert = new JSONObject();
                alert.put( "summary-arg", "chevron");
                alert.put( "title", (WxPushMessage.MSG_TYPE_PUBLIC.equals(pushMessage.getMessageType()) ? "公共消息" : "个人消息") +
                        "-" + pushMessage.getMessageTitle());
                alert.put( "body", pushMessage.getContent());
                jsonAPS.put("alert", alert.toString());
				jsonAPS.put("sound", "default");
				jsonAPS.put("badge", 1);
				notification.put("aps", jsonAPS);
				notification.put("msgId", pushMessage.getMessageId());
				notification.put("msgType", 0);
                notification.put("extraInfo", pushMessage.getExtraInfo());
				//发送端设备的唯一标识
				String channelId = pushMessage.getDeviceId();
				String deployStatusStr = (String) Constants.getSystemPropertyByCodeType(Constants.PUSH_MESSAGE_DEPLOY_STATUS);
				int deployStatus = 1;
				if(StringUtils.isNotBlank(deployStatusStr)){
					deployStatus = Integer.parseInt(deployStatusStr);
				}
				PushMsgToSingleDeviceRequest request = new PushMsgToSingleDeviceRequest()
						.addChannelId(channelId)			//设备的唯一标识数组
						.addMessageType(1)					//消息类型: 0为透传消息,1为通知,默认为0; IOS只有通知
						.addMessage(notification.toString())
						.addDeviceType(4)					//设备类型: 3 for android, 4 for ios
						.addDeployStatus(deployStatus);				//1: Developer, 2: Production
				
				pushMessage(pushMessage, apiKey, secretKey, request);
		    }
		}
	}


	@Override
	public Map<String, Object> getMessageCount(String messageType) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			if(StringUtils.isNotBlank(messageType)){
				params.put("messageType", messageType);
			}
			params.put("appAvailable", 1);
			params.put("userId", ContextUtil.getCurUserId());
			WxPushMessage message = wxPushMessageMapper.countPushedMessage(params);
			if(message == null){
				map.put("totalCount", 0);
				map.put("unreadCount", 0);
			}else{
				map.put("totalCount", message.getReadCount() + message.getUnreadCount());
				map.put("unreadCount", message.getUnreadCount());
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	/**
	 * 手动触发消息推送一次
	 */
	private void triggerPushJobOnce(){
		
		new Thread(){
			public void run() {
				//1. 临时关闭任务推送后台任务
				ScheduleJobExample example = new ScheduleJobExample();
				example.createCriteria().andSpringIdEqualTo(JOB_ID).andMethodNameEqualTo("pushMessage");
				List<ScheduleJob> jobs = scheduleJobMapper.selectByExample(example);
				if(jobs != null && !jobs.isEmpty()){
					try {
						//任务立即启动
						jobService.runAJobNow(jobs.get(0));
					} catch (SchedulerException e) {
						//任务未启动，直接推送
						sendMessageList();
						logger.warn(e.getMessage());
					}
				}else{
					//未配置任务，直接推送
					sendMessageList();
				}
			};
		}.start();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> pushMessage(String messageType, String messageTitle, String message,JSONObject extraInfo, List<Long> userIdList) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(userIdList != null && userIdList.isEmpty()){
				throw new WxPltException("请传入消息接收人");
			}
			Date now = new Date();
			Long sender = ContextUtil.getCurUserId();
			//1. 保存主消息
			Message record = new Message();
			record.setContent(message);
			record.setMessageTitle(messageTitle);
			record.setMessageType(messageType);
			record.setSentTime(now);
			record.setSentUser(sender);
            record.setExtraInfo(extraInfo == null ? "" : extraInfo.toString());
			record.setStatus(2);
			messageBizService.insert(record);
			//2. 发布消息 
			publishMessage(record, userIdList);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}
	
	/**
	 * 发布信息
	 * @param message
	 * @param userIdList
	 * @throws WxPltException
	 */
	protected void publishMessage(Message message, List<Long> userIdList) throws WxPltException{
		Map<String, Object> params = new HashMap<String, Object>();
		if(userIdList != null && (userIdList.size() != 1 || !new Long(-1).equals(userIdList.get(0)))){
			params.put("userList", userIdList);
		}
		params.put("includeAdmin", true);
		params.put("fun", "pushMessage");
		List<WxTUser> userList = wxTUserMapper.seletUsers(params);
		if (null != userList && !userList.isEmpty()) {
			List<WxPushMessage> messages = new ArrayList<WxPushMessage>();
			//1. 组装每个用户的推送消息
			WxTUser cru = ContextUtil.getCurUser();
			Set<String> terminals = new HashSet<String>(userIdList.size());
			for (WxTUser user : userList) {
				WxPushMessage pushMessage = new WxPushMessage();
				pushMessage.setUserId(user.getUserId());
				pushMessage.setDeviceId(user.getDeviceId());
				pushMessage.setDeviceType(user.getDeviceType());
				pushMessage.setMessageMainId(message.getId());
				pushMessage.setStatus(WxPushMessage.STATUS_UNREAD);
				pushMessage.setPushStatus(WxPushMessage.PUSH_STATUS_SENT);  
				if(!Message.MESSAGE_TYPE_TIPS.equals(message.getMessageType()) && 
						StringUtils.isNotBlank(user.getDeviceId()) && StringUtils.isNotBlank(user.getDeviceType()) 
						&& !new Integer(0).equals(user.getReceiveMsg())&& !new Integer(0).equals(message.getAppAvailable())){
					String terminal = user.getDeviceType() + "-" + user.getDeviceId();
					if(!terminals.contains(terminal)){
						//同一个移动终端只推送一次消息
						pushMessage.setPushStatus(WxPushMessage.PUSH_STATUS_UNPUSH);  
						terminals.add(terminal);
					}
				}
				if(null!=cru)
				{
					pushMessage.setCreateUser(cru.getUserId());
					pushMessage.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
				}else
				{
					pushMessage.setCreateUser(1L);
					pushMessage.setTenantId(1L);//modify by bo.liu 0818 cru.getTenantId()
				}
				pushMessage.setCreateTime(new Date());
				
				messages.add(pushMessage);
			}
			//2. 分批次保存需要推送的消息到表
			List<WxPushMessage> toInsertList = messages;
			int start = 0;
			while(messages.size() > start + BATCH_SIZE){
				int end = start + BATCH_SIZE;
				toInsertList = new ArrayList<WxPushMessage>();
				for(int i = start; i < end; i++){
					toInsertList.add(messages.get(i));
				}
				wxPushMessageMapper.insertByBatch(toInsertList);
				start = end;
			}
			if(start > 0){
				//最后批次
				toInsertList = new ArrayList<WxPushMessage>();
				for(int i = start; i < messages.size(); i++){
					toInsertList.add(messages.get(i));
				}
			}
			wxPushMessageMapper.insertByBatch(toInsertList);
			//3. 有需要推送的消息 立即发送一次消息推送
			if(!terminals.isEmpty()){
				triggerPushJobOnce();
			}
		} else {
			logger.error("接收用户不存在");
			throw new WxPltException("接收用户不存在");
			
		}
	}


	@Override
	public Map<String, Object> queryMessageForPage(PushMessageParams params) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			params.setAppAvailable(1);
			messageBizService.queryPersonalMessageForPage(params, map);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> updateMessageStatusToRead(Long messageId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			WxPushMessage record = new WxPushMessage();
			record.setMessageId(messageId);
			record.setStatus(WxPushMessage.STATUS_READ);
			record.setXgTime(DateUtil.getCurrentDate());
			record.setXgUser(ContextUtil.getCurUserId());
			wxPushMessageMapper.updateByPrimaryKeySelective(record);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getMessage(Long messageId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put("message", wxPushMessageMapper.selectByPrimaryKey(messageId));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> deleteMessages(List<Long> messageIds) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		if(messageIds == null || messageIds.isEmpty()){
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, "请传入需要删除消息的ID");
			return map;
		}
		try {
			WxPushMessageExample example = new WxPushMessageExample();
			example.createCriteria().andMessageIdIn(messageIds);
			WxPushMessage record = new WxPushMessage();
			record.setStatus(WxPushMessage.STATUS_DELETED);
			record.setXgTime(DateUtil.getCurrentDate());
			record.setXgUser(ContextUtil.getCurUserId());
			wxPushMessageMapper.updateByExampleSelective(record, example);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> querySumMessageForPage(PushMessageParams params) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put(Constants.RESULT_LST_KEY, wxPushMessageMapper.querySumMessageForPage(params));
			map.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> saveMainMessageTemp(Message message) {
		message.setStatus(1);
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(message.getId() == null){
				messageBizService.insert(message);
			}else{
				messageBizService.update(message);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> deleteMainMessages(List<Long> messageIds) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			messageBizService.updateToDeleteStatus(messageIds);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> publishMainMessage(Message message,
			List<Long> userIds) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		message.setStatus(2);
		message.setSentTime(new Date());
		message.setSentUser(ContextUtil.getCurUserId());
		try {
			if(message.getId() == null){
				messageBizService.insert(message);
			}else{
				messageBizService.update(message);
			}
			publishMessage(message, userIds);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}

	@Override
	public Map<String, Object> updateMessage(WxPushMessage message) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			message.setXgTime(DateUtil.getCurrentDate());
			message.setXgUser(ContextUtil.getCurUserId());
			wxPushMessageMapper.updateByPrimaryKeySelective(message);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}

    @Override
    public List<WxTUser> pushMessageOrPhoneMsg(String messageType, String messageTitle, String message, JSONObject extraInfo, List<WxTUser> users){
        ArrayList<WxTUser> errors = new ArrayList<WxTUser>();
	    ArrayList<Long> userIds = new ArrayList<Long>();
        HashSet<String> phones = new HashSet<String>();
        for (WxTUser user : users) {
            if(StringUtils.isNotBlank(user.getDeviceId()) && user.getReceiveMsg() != null && user.getReceiveMsg() == 1){
                userIds.add(user.getUserId());
            }else{
                try {
                    //如果发消息已经发过这个手机号码 那么不发送了
                    if(phones.contains(user.getMobileTel())){
                        continue;
                    }
                    SendMessageUtil.sndMessageToPhone(user.getMobileTel(),message);
                    phones.add(user.getMobileTel());
                } catch (Exception e) {
                    logger.warn("用户ID{},信息标题：{},信息内容：{},发送失败。",user.getUserId(),messageTitle,message);
                    errors.add(user);
                    e.printStackTrace();
                }
            }
        }
        if(CollectionUtil.isNotEmpty(userIds)){
            this.pushMessage(messageType, messageTitle, message, extraInfo, userIds);
        }
        return errors;
    }
}
