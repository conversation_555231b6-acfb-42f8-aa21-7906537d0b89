package com.chevron.b2b.service.impl;

import com.chevron.b2b.business.ScheduleBizService;
import com.chevron.b2b.dao.ScheduleDistributorMapper;
import com.chevron.b2b.dao.ScheduleItemMapper;
import com.chevron.b2b.dao.ScheduleWorkshopMapper;
import com.chevron.b2b.model.Schedule;
import com.chevron.b2b.service.ScheduleService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 计划操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
@Service
public class ScheduleServiceImpl implements ScheduleService {
	
	@Autowired
	private ScheduleBizService scheduleBizService;
	
	@Resource
	private ScheduleItemMapper scheduleItemMapper;
	
	@Resource
	private ScheduleDistributorMapper scheduleDistributorMapper;
	
	@Resource
	private ScheduleWorkshopMapper scheduleWorkshopMapper;
	
	private final static Logger log = Logger.getLogger(ScheduleServiceImpl.class);

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> save(Schedule record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				scheduleBizService.insert(record);
			}else{
				scheduleBizService.update(record);
			}
			//发布活动
//			if(record.getStatus() == 5) {
//				scheduleBizService.publishSchedule(record, assignDistributors, assignWorkshops);
//			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}

	@Override
	public Map<String, Object> delete(List<Long> ids) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			scheduleBizService.delete(ids);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getBean(Long id){
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			Schedule bean = scheduleBizService.getBean(id);
			map.put("bean", bean);
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("scheduleId", id);
			bean.setAssignedDistributors(scheduleDistributorMapper.queryByParams(params));
			bean.setAssignedWorkshops(scheduleWorkshopMapper.queryByParams(params));
			params.put("orderBy", "order_numb");
			map.put("scheduleItems", scheduleItemMapper.queryByParams(params));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("get bean fail. " + e.getMessage(), e);
		}
		return map;
	}
}
