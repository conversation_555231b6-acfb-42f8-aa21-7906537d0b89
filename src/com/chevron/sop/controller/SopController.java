package com.chevron.sop.controller;

import com.chevron.sop.model.SopOtherSupportDetail;
import com.chevron.sop.model.SopSupportFieldHis;
import com.chevron.sop.model.SopSupportFieldQuery;
import com.chevron.sop.model.SopSupportParams;
import com.chevron.sop.model.SopSupportSummary;
import com.chevron.sop.service.SopService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @author: chenBin
 * @date: 29/06/2020
 * SOP
 */
@Controller
@RequestMapping("/sop")
public class SopController {
    
    @Autowired
    private SopService sopService;

    /**
     * sop其他调整编辑
     */
    @ResponseBody
    @RequestMapping("/addSopOtherAmountSupport.do")
    public JsonResponse addSopOtherAmountSupport(@RequestBody SopOtherSupportDetail sopOtherSupportDetail){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            SopOtherSupportDetail supportDetail = sopService.addSopOtherAmountSupport(sopOtherSupportDetail);
            jsonResponse.setDataResult(supportDetail);
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     *查询sop其他调整历史数据
     */
    @ResponseBody
    @RequestMapping("/sopOtherAmountSupportHistory.do")
    public JsonResponse sopOtherAmountSupportHistory(@RequestBody SopSupportParams params){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<SopOtherSupportDetail> list = sopService.sopOtherAmountSupportHistory(params);
            jsonResponse.setDataResult(list);
            jsonResponse.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     *修改sop星火计划数据
     */
    @ResponseBody
    @RequestMapping("/editSopPartnerSummary.do")
    public JsonResponse editSopPartnerSummary(@RequestBody SopSupportSummary sopSupportSummary){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            SopSupportSummary result = sopService.editSopPartnerSummary(sopSupportSummary);
            jsonResponse.setDataResult(result);
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     * 经销商sop数据提交（提交了就能在列表上看到了）
     */
    @ResponseBody
    @RequestMapping("/sopPartnerSummarySubmit.do")
    public JsonResponse sopPartnerSummarySubmit(Long id){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            sopService.sopPartnerSummarySubmit(id);
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     * 经销商sop数据撤回
     */
    @ResponseBody
    @RequestMapping("/sopPartnerSummaryRecall.do")
    public JsonResponse sopPartnerSummaryRecall(Long id){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            sopService.sopPartnerSummaryRecall(id);
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     * 经销商sop数据被驳回
     */
    @ResponseBody
    @RequestMapping("/sopPartnerSummaryAbort.do")
    public JsonResponse sopPartnerSummaryAbort(Long id){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            sopService.sopPartnerSummaryAbort(id);
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping("/sopPartnerSummaryPass.do")
    public JsonResponse sopPartnerSummaryPass(Long id){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            sopService.sopPartnerSummaryPass(id);
        }catch (WxPltException e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     * 查询经销商SOP数据
     */
    @ResponseBody
    @RequestMapping("/findSopPartnerSummary.do")
    public JsonResponse findSopPartnerSummary(@RequestBody SopSupportParams sopSupportParams){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<SopSupportSummary> sopPartnerSummary = sopService.findSopPartnerSummary(sopSupportParams);
            jsonResponse.setDataResult(sopPartnerSummary);
            jsonResponse.put(Constants.RESULT_TOTAL_KEY, sopSupportParams.getTotalCount());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

    /**
     * 查询经销商SOP数据字段修改历史记录
     */
    @ResponseBody
    @RequestMapping("/findSopSupportFieldHis.do")
    public JsonResponse findSopSupportFieldHis(@RequestBody SopSupportFieldQuery query){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<SopSupportFieldHis> result = sopService.findSopSupportFieldHis(query);
            jsonResponse.setDataResult(result);
            jsonResponse.put(Constants.RESULT_TOTAL_KEY, query.getTotalCount());
        }catch (Exception e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
        }
        return jsonResponse;
    }

}
