package com.sys.base.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 业务模块单表查询条件
 * <AUTHOR>
 * @version 1.0 2018-01-22 11:16
 */
public class BusinessModuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BusinessModuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameIsNull() {
            addCriterion("business_module_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameIsNotNull() {
            addCriterion("business_module_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameEqualTo(String value) {
            addCriterion("business_module_name =", value, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameNotEqualTo(String value) {
            addCriterion("business_module_name <>", value, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameGreaterThan(String value) {
            addCriterion("business_module_name >", value, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_module_name >=", value, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameLessThan(String value) {
            addCriterion("business_module_name <", value, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameLessThanOrEqualTo(String value) {
            addCriterion("business_module_name <=", value, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameIn(List<String> values) {
            addCriterion("business_module_name in", values, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameNotIn(List<String> values) {
            addCriterion("business_module_name not in", values, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameBetween(String value1, String value2) {
            addCriterion("business_module_name between", value1, value2, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleNameNotBetween(String value1, String value2) {
            addCriterion("business_module_name not between", value1, value2, "businessModuleName");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeIsNull() {
            addCriterion("business_module_code is null");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeIsNotNull() {
            addCriterion("business_module_code is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeEqualTo(String value) {
            addCriterion("business_module_code =", value, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeNotEqualTo(String value) {
            addCriterion("business_module_code <>", value, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeGreaterThan(String value) {
            addCriterion("business_module_code >", value, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("business_module_code >=", value, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeLessThan(String value) {
            addCriterion("business_module_code <", value, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeLessThanOrEqualTo(String value) {
            addCriterion("business_module_code <=", value, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeIn(List<String> values) {
            addCriterion("business_module_code in", values, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeNotIn(List<String> values) {
            addCriterion("business_module_code not in", values, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeBetween(String value1, String value2) {
            addCriterion("business_module_code between", value1, value2, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andBusinessModuleCodeNotBetween(String value1, String value2) {
            addCriterion("business_module_code not between", value1, value2, "businessModuleCode");
            return (Criteria) this;
        }

        public Criteria andEnableFlagIsNull() {
            addCriterion("enable_flag is null");
            return (Criteria) this;
        }

        public Criteria andEnableFlagIsNotNull() {
            addCriterion("enable_flag is not null");
            return (Criteria) this;
        }

        public Criteria andEnableFlagEqualTo(Integer value) {
            addCriterion("enable_flag =", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagNotEqualTo(Integer value) {
            addCriterion("enable_flag <>", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagGreaterThan(Integer value) {
            addCriterion("enable_flag >", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable_flag >=", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagLessThan(Integer value) {
            addCriterion("enable_flag <", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagLessThanOrEqualTo(Integer value) {
            addCriterion("enable_flag <=", value, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagIn(List<Integer> values) {
            addCriterion("enable_flag in", values, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagNotIn(List<Integer> values) {
            addCriterion("enable_flag not in", values, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagBetween(Integer value1, Integer value2) {
            addCriterion("enable_flag between", value1, value2, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEnableFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("enable_flag not between", value1, value2, "enableFlag");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskIsNull() {
            addCriterion("effect_on_task is null");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskIsNotNull() {
            addCriterion("effect_on_task is not null");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskEqualTo(Integer value) {
            addCriterion("effect_on_task =", value, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskNotEqualTo(Integer value) {
            addCriterion("effect_on_task <>", value, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskGreaterThan(Integer value) {
            addCriterion("effect_on_task >", value, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskGreaterThanOrEqualTo(Integer value) {
            addCriterion("effect_on_task >=", value, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskLessThan(Integer value) {
            addCriterion("effect_on_task <", value, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskLessThanOrEqualTo(Integer value) {
            addCriterion("effect_on_task <=", value, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskIn(List<Integer> values) {
            addCriterion("effect_on_task in", values, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskNotIn(List<Integer> values) {
            addCriterion("effect_on_task not in", values, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskBetween(Integer value1, Integer value2) {
            addCriterion("effect_on_task between", value1, value2, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andEffectOnTaskNotBetween(Integer value1, Integer value2) {
            addCriterion("effect_on_task not between", value1, value2, "effectOnTask");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceIsNull() {
            addCriterion("order_sequence is null");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceIsNotNull() {
            addCriterion("order_sequence is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceEqualTo(Double value) {
            addCriterion("order_sequence =", value, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceNotEqualTo(Double value) {
            addCriterion("order_sequence <>", value, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceGreaterThan(Double value) {
            addCriterion("order_sequence >", value, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceGreaterThanOrEqualTo(Double value) {
            addCriterion("order_sequence >=", value, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceLessThan(Double value) {
            addCriterion("order_sequence <", value, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceLessThanOrEqualTo(Double value) {
            addCriterion("order_sequence <=", value, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceIn(List<Double> values) {
            addCriterion("order_sequence in", values, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceNotIn(List<Double> values) {
            addCriterion("order_sequence not in", values, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceBetween(Double value1, Double value2) {
            addCriterion("order_sequence between", value1, value2, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andOrderSequenceNotBetween(Double value1, Double value2) {
            addCriterion("order_sequence not between", value1, value2, "orderSequence");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagIsNull() {
            addCriterion("predefine_flag is null");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagIsNotNull() {
            addCriterion("predefine_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagEqualTo(Integer value) {
            addCriterion("predefine_flag =", value, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagNotEqualTo(Integer value) {
            addCriterion("predefine_flag <>", value, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagGreaterThan(Integer value) {
            addCriterion("predefine_flag >", value, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("predefine_flag >=", value, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagLessThan(Integer value) {
            addCriterion("predefine_flag <", value, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagLessThanOrEqualTo(Integer value) {
            addCriterion("predefine_flag <=", value, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagIn(List<Integer> values) {
            addCriterion("predefine_flag in", values, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagNotIn(List<Integer> values) {
            addCriterion("predefine_flag not in", values, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagBetween(Integer value1, Integer value2) {
            addCriterion("predefine_flag between", value1, value2, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andPredefineFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("predefine_flag not between", value1, value2, "predefineFlag");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
