package com.sys.quartz.job;

import java.sql.Date;
import java.util.Calendar;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.awardpolicy.business.WorkshopAwardDetailBizService;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;

/**
 * 结算上一个月门店激活激励任务
 * <AUTHOR>
 *
 */
public class CloseWorkshopActiveAwardTask {
	@Autowired
	WorkshopAwardDetailBizService workshopAwardDetailBizService;
	
	private static Logger log = LoggerFactory.getLogger(OilEmailTask.class);

	public void execute(){
		log.info("CloseWorkshopActiveAwardTask job start....");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(DateUtil.getCurrentDate());
		calendar.add(Calendar.MONTH, -1);
		try {
			workshopAwardDetailBizService.createAwardDetail(Date.valueOf(DateUtil.getDateStr(calendar.getTime(), "yyyy-MM") + "-01"));
		} catch (WxPltException e) {
			e.printStackTrace();
			log.error("CloseWorkshopActiveAwardTask job break....", e);
		}
		log.info("CloseWorkshopActiveAwardTask job finish....");
	}

}
