/*==============================================================*/
/* Module: PARTNER_ORDER                                        */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_partner_order                                    */
/*==============================================================*/
create table wx_t_partner_order (
	id                               bigint                            not null identity,
	partner_sale_config_id           bigint                                     not null,
	partner_id                       bigint                                     not null,
	dist_id                          bigint                                     not null,
	creator                          nvarchar(200)                                  null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	order_no                         nvarchar(50)                               not null,
	order_type                       nvarchar(50)                                   null,
	source                           nvarchar(100)                                  null,
	source_id                        nvarchar(30)                                   null,
	pay_type                         nvarchar(5)                                    null,
	pay_state                        nvarchar(5)                                    null,
	total_product_price              decimal(15,2)                                  null,
	total_delivery_price             decimal(15,2)                                  null,
	total_order_price                decimal(15,2)                                  null,
	status                           nvarchar(20)                                   null,
	remark                           nvarchar(500)                                  null,
	out_stock_no                     nvarchar(50)                                   null,
	total_liter_count                decimal(20,6)                                  null,
	delete_flag                      tinyint                                    not null,
	sales_org                        nvarchar(50)                                   null,
	sales_code                       nvarchar(50)                                   null,
	version_no                       int                                        not null,
	address                          nvarchar(256)                                  null,
	contact_person                   nvarchar(100)                                  null,
	contact_person_tel               nvarchar(100)                                  null,
	receive_type                     nvarchar(100)                                  null,
	primary key (id)
);
CREATE INDEX ix_partner_order_order_no ON wx_t_partner_order (order_no);

/*==============================================================*/
/* Table: wx_t_partner_order_line                               */
/*==============================================================*/
create table wx_t_partner_order_line (
	id                               bigint                                     not null,
	partner_order_id                 bigint                                     not null,
	sku                              nvarchar(30)                               not null,
	creator                          nvarchar(20)                                   null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	amount                           int                                            null,
	actual_amount                    int                                            null,
	units                            nvarchar(5)                                    null,
	price                            decimal(18,2)                                  null,
	remark                           nvarchar(500)                                  null,
	ladder_price                     decimal(18,2)                                  null,
	discounted_total_value           decimal(18,2)                                  null,
	free_amount                      int                                            null,
	primary key (id)
);

/*==============================================================*/
/* Table: mid_partner_sale_config                               */
/*==============================================================*/
create table mid_partner_sale_config (
	id                               bigint                                     not null,
	dist_id                          bigint                                     not null,
	store_id                         bigint                                     not null,
	created_by                       bigint                                         null,
	creation_time                    datetime                                       null,
	updated_by                       bigint                                         null,
	update_time                      datetime                                       null,
	distributor_id                   bigint                                     not null,
	sap_code                         nvarchar(50)                               not null,
	customer_name                    nvarchar(100)                              not null,
	region_name                      nvarchar(50)                               not null,
	ship_to_code                     nvarchar(50)                                   null,
	payment_term                     nvarchar(50)                                   null,
	sales_org                        nvarchar(50)                                   null,
	price_level                      nvarchar(50)                                   null,
	purpose                          nvarchar(50)                                   null,
	date_from                        date                                           null,
	date_to                          date                                           null,
	quota_type                       nvarchar(50)                                   null,
	is_quota_ratio                   nvarchar(50)                                   null,
	quota_ratio                      bigint                                         null,
	quota_ratio_sales_org            nvarchar(50)                                   null,
	quota_ratio_sales_code           nvarchar(50)                                   null,
	address                          nvarchar(256)                                  null,
	contact_person                   nvarchar(100)                                  null,
	contact_person_tel               nvarchar(100)                                  null,
	primary key (id)
);
CREATE INDEX ix_mid_par_sale_con_dis_id ON mid_partner_sale_config (distributor_id);

/*==============================================================*/
/* Foreign Key(only index): ix_par_order_mid_par_sale_con       */
/*==============================================================*/
CREATE INDEX ix_par_order_mid_par_sale_con ON wx_t_partner_order (partner_sale_config_id);

/*==============================================================*/
/* Foreign Key(only index): ix_partner_order_organization       */
/*==============================================================*/
CREATE INDEX ix_partner_order_organization ON wx_t_partner_order (partner_id);

/*==============================================================*/
/* Foreign Key(only index): ix_partner_order_region             */
/*==============================================================*/
CREATE INDEX ix_partner_order_region ON wx_t_partner_order (dist_id);

/*==============================================================*/
/* Foreign Key(only index): ix_par_order_line_par_order         */
/*==============================================================*/
CREATE INDEX ix_par_order_line_par_order ON wx_t_partner_order_line (partner_order_id);

/*==============================================================*/
/* Foreign Key(only index): ix_partner_order_line_product       */
/*==============================================================*/
CREATE INDEX ix_partner_order_line_product ON wx_t_partner_order_line (sku);

/*==============================================================*/
/* Foreign Key(only index): ix_mid_par_sale_con_reg             */
/*==============================================================*/
CREATE INDEX ix_mid_par_sale_con_reg ON mid_partner_sale_config (dist_id);

/*==============================================================*/
/* Foreign Key(only index): ix_mid_par_sale_con_sto             */
/*==============================================================*/
CREATE INDEX ix_mid_par_sale_con_sto ON mid_partner_sale_config (store_id);
