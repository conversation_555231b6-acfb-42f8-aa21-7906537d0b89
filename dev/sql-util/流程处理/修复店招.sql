select * from wx_t_mkt_flow_node fn where step=1 and status=0
and exists (select 1 from wx_t_mkt_flow_node fn1 where fn1.mkt_apply_id=fn.mkt_apply_id and step=2 and status!=1)
and exists (select 1 from wx_t_mkt_apply a where fn.mkt_apply_id=a.id and a.current_step>1)

select * 
--delete n
from wx_t_mkt_flow_node n where mkt_apply_id=10546
select * from wx_t_mkt_history where mkt_apply_id=10546
select * from wx_t_mkt_apply where id=10546

update n set n.status=1 from wx_t_mkt_flow_node n where id in (4025,4026)
update h set h.audit_status=1 from wx_t_mkt_history h where id=3787
delete from wx_t_mkt_history where id=3794
update a set a.current_step=3 from wx_t_mkt_apply a where id=10517

update n set n.status=1 from wx_t_mkt_flow_node n where id in (4002,
4003)
update h set h.audit_status=1 from wx_t_mkt_history h where id=3787
update a set a.current_step=3 from wx_t_mkt_apply a where id=10667