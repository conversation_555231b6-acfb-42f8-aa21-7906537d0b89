package com.chevron.plc.controller;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.plc.model.StockVarianceCheckResultVoParams;
import com.chevron.plc.service.StockVarianceCheckService;
import com.common.constants.Constants;
import com.common.util.MessageResourceUtil;

@Controller
public class StockVarianceCheckController {
	@Autowired
	private StockVarianceCheckService stockVarianceCheckService;
	
	private final static Logger log = Logger.getLogger(StockVarianceCheckController.class);
	
	@ResponseBody
	@RequestMapping(value="/stockVarianceCheckController/queryForPages.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> queryForPages(StockVarianceCheckResultVoParams params){
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try{
			stockVarianceCheckService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		}catch (Exception e){
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}

}
