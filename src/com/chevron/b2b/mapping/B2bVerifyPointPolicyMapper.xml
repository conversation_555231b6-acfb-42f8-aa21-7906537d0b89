<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.b2b.dao.B2bVerifyPointPolicyMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.b2b.model.B2bVerifyPointPolicy">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="award_point" property="awardPoint" jdbcType="NUMERIC"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="capacity" property="capacity" jdbcType="NUMERIC"/>
		<result column="icon_id" property="iconId" jdbcType="BIGINT"/>
		<result column="product_creation_date" property="productCreationDate" jdbcType="DATE"/>
		<result column="start_date" property="startDate" jdbcType="TIMESTAMP"/>
		<result column="end_date" property="endDate" jdbcType="TIMESTAMP"/>
		<result column="distributor_ids" property="distributorIds" jdbcType="VARCHAR"/>
		<result column="orginal_point" property="orginalPoint" jdbcType="NUMERIC"/>
		<result column="work_shop_ids" property="workShopIds" jdbcType="VARCHAR"/>
		<result column="policy_name" property="policyName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,sku,award_point,orginal_point,enable_flag,delete_flag,start_date,end_date,distributor_ids,work_shop_ids,
		create_user_id,create_time,update_user_id,update_time,policy_name
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicy">
		update wx_t_b2b_verify_point_policy set
				sku = #{sku,jdbcType=VARCHAR},
				award_point = #{awardPoint,jdbcType=NUMERIC},
				orginal_point = #{orginalPoint,jdbcType=NUMERIC},
				start_date = #{startDate,jdbcType=DATE},
				end_date = #{endDate,jdbcType=DATE},
				distributor_ids = #{distributorIds,jdbcType=VARCHAR},
				work_shop_ids = #{workShopIds,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP},
				policy_name = #{policyName,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicy">
		update wx_t_b2b_verify_point_policy
		<set>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="awardPoint != null" >
				award_point = #{awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="orginalPoint != null" >
				orginal_point = #{orginalPoint,jdbcType=NUMERIC},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="startDate != null" >
				start_date = #{startDate,jdbcType=DATE},
			</if>
			<if test="endDate != null" >
				end_date = #{endDate,jdbcType=DATE},
			</if>
			<if test="distributorIds != null" >
				distributor_ids = #{distributorIds,jdbcType=VARCHAR},
			</if>
			<if test="workShopIds != null" >
				work_shop_ids = #{workShopIds,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="policyName != null" >
				policy_name = #{policyName,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicyExample">
    	delete from wx_t_b2b_verify_point_policy
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicy">
		insert into wx_t_b2b_verify_point_policy
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="sku != null">
				sku,
			</if>
			<if test="awardPoint != null">
				award_point,
			</if>
			<if test="orginalPoint != null">
				orginal_point,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="startDate != null">
				start_date,
			</if>
			<if test="endDate != null">
				end_date,
			</if>
			<if test="distributorIds != null">
				distributor_ids,
			</if>
			<if test="workShopIds != null">
				work_shop_ids,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="policyName != null">
				policy_name,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="awardPoint != null">
				#{awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="orginalPoint != null">
				#{orginalPoint,jdbcType=NUMERIC},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="startDate != null">
				#{startDate,jdbcType=DATE},
			</if>
			<if test="endDate != null">
				#{endDate,jdbcType=DATE},
			</if>
			<if test="distributorIds != null">
				#{distributorIds,jdbcType=VARCHAR},
			</if>
			<if test="workShopIds != null">
				#{workShopIds,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="policyName != null">
				#{policyName,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_b2b_verify_point_policy
		<set>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.awardPoint != null">
				award_point = #{record.awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="record.orginalPoint != null">
				orginal_point = #{record.orginalPoint,jdbcType=NUMERIC},
			</if>
			<if test="record.enableFlag != null">
				enable_flag = #{record.enableFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.startDate != null">
				start_date = #{record.startDate,jdbcType=DATE},
			</if>
			<if test="record.endDate != null">
				end_date = #{record.endDate,jdbcType=DATE},
			</if>
			<if test="record.distributorIds != null">
				distributor_ids = #{record.distributorIds,jdbcType=VARCHAR},
			</if>
			<if test="record.workShopIds != null">
				work_shop_ids = #{record.workShopIds,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.policyName != null">
				policy_name = #{record.policyName,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicyExample">
		delete from wx_t_b2b_verify_point_policy
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicyExample" resultType="int">
		select count(1) from wx_t_b2b_verify_point_policy
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicyExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_b2b_verify_point_policy
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicyExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_b2b_verify_point_policy
		where id = #{id,jdbcType=BIGINT}
	</select>
	
	<!-- 列表查询
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.sku, t1.award_point, t1.enable_flag, t1.delete_flag, t1.create_user_id, t1.create_time,t1.start_date,t1.end_date,t1.distributor_ids,
		 t1.orginal_point,t1.work_shop_ids,
		(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = p.id and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id,
		p.capacity, p.name product_name, p.name_en product_name_en,t1.policy_name
		  from wx_t_b2b_verify_point_policy t1
		  left join wx_t_product p on p.sku=t1.sku
		 where t1.delete_flag=0
		<if test="sku != null and sku != ''">
			and t1.sku = #{sku, jdbcType=VARCHAR}
		</if>
		<if test="awardPoint != null">
			and t1.award_point = #{awardPoint, jdbcType=NUMERIC}
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="partnerId !=null and partnerId!=''">
		    and CHARINDEX(','+#{partnerId}+',',','+t1.distributor_ids+',')>0
		</if>
		<if test="nowDate!=null and nowDate != ''" >
		   and (<![CDATA[CONVERT(varchar(100), t1.start_date, 23) <= CONVERT(varchar(100), #{nowDate}, 23)]]>
		   or  t1.start_date is null)
	    </if>
		<if test="nowDate!=null and nowDate != ''" >
			and (<![CDATA[CONVERT(varchar(100), t1.end_date, 23) >=CONVERT(varchar(100),  #{nowDate}, 23)]]>
			or t1.end_date is null)
		</if>
		<if test="isPartnerId">
		    and (t1.distributor_ids is null or t1.distributor_ids='')
		</if>
		<if test="isDate">
		    and t1.start_date is null and  t1.end_date is null
		</if>
		<if test="workShopId !=null and workShopId!=''">
		    and CHARINDEX(','+#{workShopId}+',',','+t1.work_shop_ids+',')>0
		</if>
		<if test="isWorkShopId">
		    and (t1.work_shop_ids is null or t1.work_shop_ids='')
		</if>
		 order by t1.start_date desc,t1.end_date asc
	</select> -->

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicyParams">
		select t1.id, t1.sku, t1.award_point, t1.enable_flag, t1.delete_flag, t1.create_user_id, t1.create_time,
		t1.start_date,t1.end_date,t1.distributor_ids,
		t1.orginal_point,t1.work_shop_ids,t1.policy_name
		  from wx_t_b2b_verify_point_policy t1
		 where t1.delete_flag=0
		<if test="startAwardPoint != null">
			and t1.award_point &gt;= #{startAwardPoint, jdbcType=NUMERIC}
		</if>
		<if test="endAwardPoint != null">
			and t1.award_point &lt;= #{endAwardPoint, jdbcType=NUMERIC}
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="capacity != null or productCreationDateFrom != null or productCreationDateTo != null or (salesChannel != null and salesChannel != '') or (sku != null and sku != '')">
			and exists (select 1 from wx_t_product p where p.status=1 and CHARINDEX(','+p.sku+',',','+t1.sku+',')>0
			<if test="capacity != null">
				and convert(float, p.capacity) = #{capacity}
			</if>
			<if test="productCreationDateFrom != null">
			 	and p.creation_date >= #{productCreationDateFrom, jdbcType=DATE}
			</if>
			<if test="productCreationDateTo != null">
			 	and p.creation_date &lt;= #{productCreationDateTo, jdbcType=DATE}
			</if>
			<if test="salesChannel != null and salesChannel != ''">
				and p.product_channel=#{salesChannel}
			</if>
			<if test="sku != null and sku != ''">
				and (p.sku like '%' + #{sku, jdbcType=VARCHAR} + '%' or p.name like '%' + #{sku, jdbcType=VARCHAR} + '%' or p.name_en like '%' + #{sku, jdbcType=VARCHAR} + '%')
			</if>
			)
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.policy_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
	</select>
	<select id="getVerifyPolicy" resultMap="BaseResultMap" parameterType="map">
		select top 1 t1.id, isnull(t1.award_point, 0) + isnull(t1.orginal_point, 0) award_point,t1.policy_name from wx_t_b2b_verify_point_policy t1 
		where t1.enable_flag=1 and t1.delete_flag=0
		    and (t1.distributor_ids is null or t1.distributor_ids='' or CHARINDEX(','+convert(nvarchar(20),#{partnerId})+',',','+t1.distributor_ids+',')>0)
		    and (t1.work_shop_ids is null or t1.work_shop_ids='' or CHARINDEX(','+convert(nvarchar(20),#{workShopId})+',',','+t1.work_shop_ids+',')>0)
		    and (t1.sku is null or t1.sku='' or CHARINDEX(','+#{sku}+',',','+t1.sku+',')>0)
		   and (t1.start_date is null or <![CDATA[t1.start_date <= #{nowDate,jdbcType=DATE}]]>)
			and (t1.end_date is null OR <![CDATA[t1.end_date >=#{nowDate,jdbcType=DATE}]]>)
		order by isnull(t1.award_point, 0) + isnull(t1.orginal_point, 0) desc
	</select>
	
	<!-- <update id="updateForEdit" parameterType="com.chevron.b2b.model.B2bVerifyPointPolicy">
		update wx_t_b2b_verify_point_policy set
				sku = #{sku,jdbcType=VARCHAR},
				award_point = #{awardPoint,jdbcType=NUMERIC},
				orginal_point = #{orginalPoint,jdbcType=NUMERIC},
				start_date = #{startDate,jdbcType=DATE},
				end_date = #{endDate,jdbcType=DATE},
				distributor_ids = #{distributorIds,jdbcType=VARCHAR},
				work_shop_ids = #{workShopIds,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP},
				policy_name = #{policyName,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update> -->
</mapper>
