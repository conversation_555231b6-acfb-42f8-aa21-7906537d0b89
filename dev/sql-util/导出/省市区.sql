select r1.region_code "省份编码", r1.region_name "省份名称", 
r2.region_code "地市编码", r2.region_name "地市名称", r3.region_code "区县编码", r3.region_name "区县名称" from wx_t_region r1 
left join wx_t_region r2 on r1.id=r2.parent_id
left join wx_t_region r3 on r2.id=r3.parent_id
where r1.region_type='P'
order by r1.id,r2.id, r3.id


select r1.city_code "省份编码", r1.city_name_zh "省份名称", 
r2.city_code "地市编码", r2.city_name_zh "地市名称", r3.city_code "区县编码", r3.city_name_zh "区县名称" 
from tab_citys r1 
left join tab_citys r2 on r1.id=r2.parent_id
left join tab_citys r3 on r2.id=r3.parent_id
where r1.city_level=1
order by r1.id,r2.id, r3.id