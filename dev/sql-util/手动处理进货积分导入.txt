
    SELECT
        distributor_id,
		partner_id,
		partner_name,
		bi_sellin_line_id,
		pending_score,
		status,
		sales_channel_name,
		creation_time,
		created_by,
		last_update_time,
		last_updated_by,
		liters,
		--product_sku,
		trans_time
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            0 AS bi_sellin_line_id,
            sum( round( dw.points_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.sales_channel_name,
            getdate() AS creation_time,
            1 AS created_by,
            getdate() AS last_update_time,
            1 AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            --dw.product_sku,
            dw.trans_time
          from
            dbo.dw_base_trans_sell_in dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = en.partner_id
          WHERE
            1 = 1
            AND (dw.trans_time >= '2019-10-01' and dw.trans_time < '2019-11-01')
            AND dw.sales_channel_name = 'CDM'
			and o.id in (56601,
56433,
56637,
56640,
56639,
56638,
56605,
56415,
56636)
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = en.distributor_id
                  AND re.product_sku = dw.product_sku
                  AND re.sales_channel_name = dw.sales_channel_name
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
           -- dw.product_sku,
            dw.sales_channel_name,
            dw.trans_time
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0
		  ORDER BY
			total_pending_info.partner_name--,
			--total_pending_info.product_sku
			
INSERT INTO [dbo].[wx_t_point_business]
           ([BUSINESS_TYPE_CODE]
           ,[RELATED_ID]
           ,[RELATED_CODE]
           ,[BUSINESS_DESC]
           ,[BUSINESS_CONTEXT]
           ,[BUSINESS_STATUS]
           ,[DELETE_FLAG]
           ,[CREATION_TIME]
           ,[CREATED_BY]
           ,[LAST_UPDATE_TIME]
           ,[LAST_UPDATED_BY])
     VALUES
           ('CALTEX_POINT_FROM_BI'
           ,null
           ,'PI20191017'
           ,'【超级管理员】在20191017手动处理来自BI系统的9个经销商的积分总共83492分'
           ,null
           ,'DONE'
           ,0
           ,getdate()
           ,1
           ,getdate()
           ,1)
 
 
          select
		  dw.*
          from
            dbo.dw_base_trans_sell_in dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = en.partner_id
          WHERE
            1 = 1
            AND (dw.trans_time >= '2019-10-01' and dw.trans_time < '2019-11-01')
            AND dw.sales_channel_name = 'CDM'
			and o.id in (56601,
56433,
56637,
56640,
56639,
56638,
56605,
56415,
56636) and dw.points_rmb is not null and ((dw.base_trans_sell_in_id> 100000000 and dw.base_trans_sell_in_id < 4000038606) 
			or (dw.base_trans_sell_in_id < 100000000 and dw.base_trans_sell_in_id < 160361))
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = en.distributor_id
                  AND re.product_sku = dw.product_sku
                  AND re.sales_channel_name = dw.sales_channel_name
            )
			order by dw.base_trans_sell_in_id
      
--select sum (pending_score), sum (liters) from (
    SELECT
        distributor_id,
		partner_id,
		partner_name,
		bi_sellin_line_id,
		pending_score,
		status,
		sales_channel_name,
		creation_time,
		created_by,
		last_update_time,
		last_updated_by,
		liters,
		--product_sku,
		trans_time
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            0 AS bi_sellin_line_id,
            sum( round( dw.points_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.sales_channel_name,
            getdate() AS creation_time,
            1 AS created_by,
            getdate() AS last_update_time,
            1 AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            --dw.product_sku,
            dw.trans_time
          from
            dbo.dw_base_trans_sell_in dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = en.partner_id
          WHERE
            1 = 1
            AND (dw.trans_time >= '2019-10-01' and dw.trans_time < '2019-11-01') 
			and ((dw.base_trans_sell_in_id> 100000000 and dw.base_trans_sell_in_id < 4000038606) 
			or (dw.base_trans_sell_in_id < 100000000 and dw.base_trans_sell_in_id < 160361))
            AND dw.sales_channel_name = 'CDM'
			and o.id in (56601,
56433,
56637,
56640,
56639,
56638,
56605,
56415,
56636)
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = en.distributor_id
                  AND re.product_sku = dw.product_sku
                  AND re.sales_channel_name = dw.sales_channel_name
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
           -- dw.product_sku,
            dw.sales_channel_name,
            dw.trans_time
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0
		  --ORDER BY total_pending_info.partner_name--,
			--total_pending_info.product_sku
			--) x                
			
select top 1000 * from wx_t_point_value_detail where POINT_TYPE in ('CALTEX_POINT') order by id desc
update l set l.COMMENTS='BI系统积分计算 12月' from wx_t_point_value_detail_log l where comments='BI系统积分计算 01月' and CREATION_TIME>'2020-02-28'

update d set d.COMMENTS='BI系统积分计算 12月' from wx_t_point_value_detail d where comments='BI系统积分计算 01月' and CREATION_TIME>'2020-02-28'
update l set l.COMMENTS='BI系统积分计算 12月' from wx_t_point_value_detail_log l where comments='BI系统积分计算 01月' and CREATION_TIME>'2020-02-28'

			