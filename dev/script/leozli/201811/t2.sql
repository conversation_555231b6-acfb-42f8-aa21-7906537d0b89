/*==============================================================*/
/* Module: B2B                                                  */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_mec_ver_point                                    */
/*==============================================================*/
create table wx_t_mec_ver_point (
	id                               bigint                            not null identity,
	award_policy_id                  bigint                                     not null,
	qrcode                           nvarchar(64)                               not null,
	distributor_id                   bigint                                     not null,
	workshop_id                      bigint                                     not null,
	mechanic_code                    nvarchar(32)                               not null,
	sales_channel                    nvarchar(256)                                  null,
	sku                              nvarchar(32)                               not null,
	liter                            decimal(20,6)                                  null,
	award_point                      decimal(10,2)                                  null,
	status                           int                                        not null,
	unfreeze_time                    datetime                                       null,
	unfreeze_user                    bigint                                         null,
	ext_attr1                        nvarchar(64)                                   null,
	ext_attr2                        nvarchar(256)                                  null,
	ext_attr3                        nvarchar(256)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_mec_ver_point ADD CONSTRAINT mec_ver_point UNIQUE (qrcode);
CREATE INDEX mec_ver_point_dis_id ON wx_t_mec_ver_point (distributor_id);
CREATE INDEX mec_ver_point_workshop_id ON wx_t_mec_ver_point (workshop_id);
CREATE INDEX mec_ver_point_mec_code ON wx_t_mec_ver_point (mechanic_code);
CREATE INDEX mec_ver_point_sku ON wx_t_mec_ver_point (sku);


/*==============================================================*/
/* Table: wx_t_schedule                                         */
/*==============================================================*/
create table wx_t_schedule (
	id                               bigint                            not null identity,
	schedule_name                    nvarchar(255)                              not null,
	icon_id                          bigint                                         null,
	banner_id                        bigint                                         null,
	schedule_type                    nvarchar(64)                               not null,
	execute_user_type                nvarchar(64)                                   null,
	award_point                      decimal(10,2)                                  null,
	left_steps                       int                                            null,
	start_time                       datetime                                       null,
	end_time                         datetime                                       null,
	schedule_desc                    text                                           null,
	assign_distributors              text                                           null,
	assign_workshops                 text                                           null,
	status                           int                                        not null,
	remark                           nvarchar(512)                                  null,
	sales_channel                    nvarchar(256)                                  null,
	publish_time                     datetime                                       null,
	ext_attr1                        nvarchar(64)                                   null,
	ext_attr2                        nvarchar(256)                                  null,
	ext_attr3                        nvarchar(256)                                  null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_schedule_item                                    */
/*==============================================================*/
create table wx_t_schedule_item (
	id                               bigint                            not null identity,
	input_ctrl_id                    bigint                                     not null,
	schedule_id                      bigint                                     not null,
	caption                          nvarchar(4000)                             not null,
	input_ctrl_config                nvarchar(4000)                                 null,
	required_flag                    tinyint                                        null,
	order_numb                       decimal(20,8)                              not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_input_ctrl                                       */
/*==============================================================*/
create table wx_t_input_ctrl (
	id                               bigint                            not null identity,
	ctrl_type                        nvarchar(64)                               not null,
	ctrl_title                       nvarchar(256)                              not null,
	config_editor                    nvarchar(4000)                                 null,
	default_config                   nvarchar(4000)                                 null,
	order_numb                       decimal(20,8)                              not null,
	enable_flag                      tinyint                                    not null,
	remark                           nvarchar(512)                                  null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_schedule_instance                                */
/*==============================================================*/
create table wx_t_schedule_instance (
	id                               bigint                            not null identity,
	schedule_id                      bigint                                     not null,
	distributor_id                   bigint                                         null,
	workshop_id                      bigint                                         null,
	executor_type                    nvarchar(64)                                   null,
	executor_key                     nvarchar(64)                                   null,
	executor_name                    nvarchar(255)                                  null,
	status                           int                                        not null,
	submit_time                      datetime                                       null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX sch_ins_dis_id ON wx_t_schedule_instance (distributor_id);
CREATE INDEX sch_ins_wor_id ON wx_t_schedule_instance (workshop_id);

/*==============================================================*/
/* Table: wx_t_schedule_item_instance                           */
/*==============================================================*/
create table wx_t_schedule_item_instance (
	id                               bigint                            not null identity,
	schedule_item_id                 bigint                                     not null,
	schedule_instance_id             bigint                                     not null,
	input_value                      nvarchar(1000)                                 null,
	primary key (id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_me_ve_po_b2_ve_po_po             */
/*==============================================================*/
CREATE INDEX ix_me_ve_po_b2_ve_po_po ON wx_t_mec_ver_point (award_policy_id);

/*==============================================================*/
/* Foreign Key(only index): ix_schedule_item_input_ctrl         */
/*==============================================================*/
CREATE INDEX ix_schedule_item_input_ctrl ON wx_t_schedule_item (input_ctrl_id);

/*==============================================================*/
/* Foreign Key(only index): ix_schedule_item_schedule           */
/*==============================================================*/
CREATE INDEX ix_schedule_item_schedule ON wx_t_schedule_item (schedule_id);

/*==============================================================*/
/* Foreign Key(only index): ix_schedule_instance_schedule       */
/*==============================================================*/
CREATE INDEX ix_schedule_instance_schedule ON wx_t_schedule_instance (schedule_id);

/*==============================================================*/
/* Foreign Key(only index): ix_sch_item_ins_sch_item            */
/*==============================================================*/
CREATE INDEX ix_sch_item_ins_sch_item ON wx_t_schedule_item_instance (schedule_item_id);

/*==============================================================*/
/* Foreign Key(only index): ix_sch_item_ins_sch_ins             */
/*==============================================================*/
CREATE INDEX ix_sch_item_ins_sch_ins ON wx_t_schedule_item_instance (schedule_instance_id);
