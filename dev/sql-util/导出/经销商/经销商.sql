--乘用车
select cos1.region Region, cos1.supervisor_name_cn "FLSR", o.organization_name "经销商名称", pe.sap_code "SAP Number"--, --pe.distributor_id,
--case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" 
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&1>0
where o.status=1 and 
o.type=1 and o.id!=9
and len(pe.sap_code)>0
and exists (select 1 from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id left join wx_t_role r on r.role_id=ur.role_id
where u.org_id=o.id and u.status=1 and r.ch_role_name in ('Service_Partner_Manager','Service_Partner_Admin','CDM_DIST'))
order by cos1.region, cos1.sales_cai,o.id 

--商用油
select cos1.region Region, cos1.supervisor_name_cn "FLSR", o.organization_name "经销商名称", pe.sap_code "SAP Number"--, --pe.distributor_id,
--case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" 
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&2>0
where o.status=1 and 
o.type=1 and o.id!=9
--and len(pe.sap_code)>0
and exists (select 1 from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id left join wx_t_role r on r.role_id=ur.role_id
where u.org_id=o.id and u.status=1 and r.ch_role_name in ('Caltex_Dealer'))
order by cos1.region, cos1.sales_cai,o.id 

--乘用车
select cos1.region Region, cos1.supervisor_name_cn "FLSR", o.organization_name "经销商名称", pe.sap_code "SAP Number"--, --pe.distributor_id,
--case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" 
,cos1.customer_type 
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&1>0
where o.status=1 and 
o.type=1 and o.id!=9
and len(pe.sap_code)>0
and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=o.id and rp1.channel_weight&1>0)
order by cos1.region, cos1.sales_cai,o.id 

--商用油
select cos1.region Region, cos1.supervisor_name_cn "FLSR", o.organization_name "经销商名称", pe.sap_code "SAP Number"--, --pe.distributor_id,
--case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" 
,cos1.customer_type 
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&2>0
where o.status=1 and 
o.type=1 and o.id!=9
--and len(pe.sap_code)>0
and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=o.id and rp1.channel_weight&2>0)
order by cos1.region, cos1.sales_cai,o.id

--乘用车
select cos1.region Region, cos1.supervisor_name_cn "FLSR", o.organization_name "经销商名称", pe.sap_code "SAP Number"--, --pe.distributor_id,
--case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" 
,cos1.customer_type,(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id and rp.channel_weight&1>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "经销商分配城市" 
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&1>0
where o.status=1 and 
o.type=1 and o.id!=9
and len(pe.sap_code)>0
and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=o.id and rp1.channel_weight&1>0)
order by cos1.region, cos1.sales_cai,o.id 

--商用油
select cos1.region Region, cos1.supervisor_name_cn "FLSR", o.organization_name "经销商名称", pe.sap_code "SAP Number"--, --pe.distributor_id,
--case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" 
,cos1.customer_type,(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id and rp.channel_weight&2>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "经销商分配城市" 
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&2>0
where o.status=1 and 
o.type=1 and o.id!=9
--and len(pe.sap_code)>0
and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=o.id and rp1.channel_weight&2>0)
order by cos1.region, cos1.sales_cai,o.id
