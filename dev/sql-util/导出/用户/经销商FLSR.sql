select region,customer_name_cn, dw.distributor_id, 
(select top 1 u.login_name from wx_t_user u join wx_t_organization o on o.id=u.org_id left join wx_t_userrole ur on u.user_id=ur.user_id left join wx_t_role r on r.role_id=ur.role_id where o.type=1 and (u.type is null or u.type != '1') and r.ch_role_name in ('CDM_DIST', 'Service_Partner_Manager','Caltex_Dealer') and u.status=1 and o.id=pe.partner_id  order by u.user_id asc) "经销商老板登录账号",
product_channel, sales_cai, sales_name_cn, u.login_name from dw_customer_org_sales dw left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=dw.distributor_id left join wx_t_user u on u.status=1 and u.cai=dw.sales_cai
order by region,sales_cai