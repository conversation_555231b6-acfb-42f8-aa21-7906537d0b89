select * 
--update wi set status=0,next_step_no=2
from wx_t_workflow_instance wi where wi.workflow_code='CI_apply' and wi.form_key='125'
select * 
--update a set a.form_status=0
from wx_t_mkt_ci_apply a where id=125

select * 
--update wi set status=0,next_step_no=2
from wx_t_workflow_instance wi where wi.workflow_code='SIGNAGE_APPLY' and 
wi.form_key='33'
select * 
--update a set a.form_status=0
from wx_t_mkt_signage_apply a where id=33

select * 
--update wi set status=0,next_step_no=2
from wx_t_workflow_instance wi where wi.workflow_code='SIGNAGE_APPLY' and wi.form_key='428'
select * 
--update a set a.form_status=0
from wx_t_mkt_signage_apply a where id=428

select * 
--update wi set wi.status=0,next_step_no=2
--update ca set ca.form_status=0
from [wx_t_mkt_signage_apply] ca 
left join wx_t_workflow_instance wi on wi.form_key=ca.id and wi.workflow_code='SIGNAGE_APPLY' where ca.req_no='PCMO440020210816005'
