package com.web.interceptor;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.chevron.autozi.model.ReplenishMentHeaderBean;
import com.chevron.autozi.model.RequestCommonBean;
import com.chevron.autozi.model.RequestCommonHeaderBean;
import com.chevron.autozi.model.ResponseMessageBean;
import com.chevron.autozi.model.ReturnedPurchaseHeaderBean;
import com.chevron.autozi.model.ZCReplenishMentBean;
import com.chevron.autozi.model.ZCReturnedPurchaseBean;
import com.common.constants.Constants;
import com.common.util.EncryptionUtil;
import com.common.util.JsonUtil;
import com.common.util.ListUtil;
import com.common.util.ReflectHelper;
/**
 * 
 * @Author: bo.liu  2017-1-4 下午2:44:08 
 * @Version: $Id$
 * @Desc: <p>对中驰访问本地接口进行拦截，，统一签名验证</p>
 */
public class ZCApiInterceptor implements HandlerInterceptor {

	private Logger log = Logger.getLogger(ZCApiInterceptor.class);

	public ZCApiInterceptor() {
	}

	private List<String> needCheckUrls;// 只需要针对检查的url进行过滤

	public List<String> getNeedCheckUrls() {
		return needCheckUrls;
	}

	public void setNeedCheckUrls(List<String> needCheckUrls) {
		this.needCheckUrls = needCheckUrls;
	}

	/**
	 * 在业务处理器处理请求之前被调用 如果返回false 从当前的拦截器往回执行所有拦截器的afterCompletion(),再退出拦截器链
	 * 如果返回true 执行下一个拦截器,直到所有的拦截器都执行完毕 再执行被拦截的Controller 然后进入拦截器链,
	 * 从最后一个拦截器往回执行所有的postHandle() 接着再从最后一个拦截器往回执行所有的afterCompletion()
	 */
	@Override
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler) throws Exception {
		String url = request.getRequestURL().toString();
		log.info("=ljc=============ZCApiInterceptor执行 preHandle 顺序1================:"+url);
		ResponseMessageBean msgVo = new ResponseMessageBean();
		for (String needCheckURL : needCheckUrls) {
			if (url.endsWith(needCheckURL)) {
				try
				{
					log.info("=ljc=============ZCApiInterceptor url================");
					response.setCharacterEncoding("utf-8");
					response.setContentType("application/json");
					BufferedReader br;
					
					br = new BufferedReader(new InputStreamReader(
							(ServletInputStream) request.getInputStream()));
					String line = null;
					StringBuffer sb = new StringBuffer();
					while ((line = br.readLine()) != null) {
						sb.append(line);
					}
					String appMsg = sb.toString();
					if (null == appMsg || appMsg.isEmpty()) {
						msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1002);
						msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1002);
						String respJSONStr = JsonUtil.writeValue(msgVo);
						returnJson(response, respJSONStr);
						return false;
					}
					JSONObject jsonObj = JSONObject.fromObject(appMsg);
					
					//start 新增类型接口后需要在此添加判断  需要继承RequestCommonHeaderBean RequestCommonBean
					RequestCommonBean zcReqCommonBean = null;
					if(url.endsWith(Constants.RETURN_PRODUCT_DO))
					{
						zcReqCommonBean = (ZCReturnedPurchaseBean) JsonUtil
								.json2obj(appMsg, ZCReturnedPurchaseBean.class);
					}else if(url.endsWith(Constants.REPLEMENT_PRODUCT_DO))
					{
						zcReqCommonBean = (ZCReplenishMentBean) JsonUtil
								.json2obj(appMsg, ZCReplenishMentBean.class);
					}
					//end
					if (null == zcReqCommonBean) {
						msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1002);
						msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1002);
						String respJSONStr = JsonUtil.writeValue(msgVo);
						returnJson(response, respJSONStr);
						return false;
					}
					
					String signData = zcReqCommonBean.getSign();
					if (null == signData || signData.isEmpty()) {
						msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
						msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
						String respJSONStr = JsonUtil.writeValue(msgVo);
						returnJson(response, respJSONStr);
						return false;
					}
					msgVo = validateReturnedPurchaseReqDataNew(jsonObj, zcReqCommonBean);
					String validateCode =  msgVo.getCode();
					if (!validateCode.equals(Constants.ZCAPI_SUCCESS_CODE)) {
						String respJSONStr = JsonUtil.writeValue(msgVo);
						returnJson(response, respJSONStr);
						return false;
					}
					//设置值，用于controller去获取，然后去处理
					request.setAttribute("zcReqCommonBean", zcReqCommonBean);
					
				} catch (Exception e) {
					log.error("=ljc=============preHandle() exception:", e);
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1006);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1006);
					String respJSONStr = JsonUtil.writeValue(msgVo);
					returnJson(response, respJSONStr);
					return false;
				}
			}
		}
		return true;
	}

	// 在业务处理器处理请求执行完成后,生成视图之前执行的动作
	@Override
	public void postHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		log.info("=ljc=============ZCApiInterceptor执行 postHandle 顺序2================");
	}

	/**
	 * 在DispatcherServlet完全处理完请求后被调用
	 * 
	 * 当有拦截器抛出异常时,会从当前拦截器往回执行所有的拦截器的afterCompletion()
	 */
	@Override
	public void afterCompletion(HttpServletRequest request,
			HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		log.info("=ljc=============ZCApiInterceptor执行 afterCompletion 顺序3================");
	}
	
	
	/**
	 * 签名验证，，按照规定的格式设计类(需要继承RequestCommonHeaderBean RequestCommonBean)
	 * <AUTHOR> 2017-1-5 下午9:56:12
	 * @param jsonObj
	 * @param reqCommonBean
	 * @return
	 */
	public ResponseMessageBean validateReturnedPurchaseReqDataNew(JSONObject jsonObj,RequestCommonBean reqCommonBean) {
		ResponseMessageBean msgVo = new ResponseMessageBean();
		try {
			RequestCommonHeaderBean zcHeaderBean = null;
			RequestCommonBean zcReqCommonBean = null;
			//start后续接口新增，需要在此添加类型，同时需要继承RequestCommonHeaderBean RequestCommonBean
			if(reqCommonBean instanceof ZCReturnedPurchaseBean)
			{
				zcReqCommonBean = ((ZCReturnedPurchaseBean)(reqCommonBean));
				zcHeaderBean = ((ZCReturnedPurchaseBean)(reqCommonBean)).getHeader();
			}else if(reqCommonBean instanceof ZCReplenishMentBean)
			{
				zcReqCommonBean = ((ZCReplenishMentBean)(reqCommonBean));
				zcHeaderBean = ((ZCReplenishMentBean)(reqCommonBean)).getHeader();
			}
		    //end
			
			if (null == zcHeaderBean) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}
			HashMap<String, String> paramMap = new HashMap<String, String>();
			List<String> commonAllSignParams = new ArrayList<String>();
			List<String> headerAllSignParams = new ArrayList<String>();
			List<String> commonSignParams = new ArrayList<String>();
			List<String> headerSignParams = new ArrayList<String>();
			
			//如果存在必选项的值为null,则返回错误
			List<String> needParams = new ArrayList<String>();
			ReflectHelper.getParametersFromJavaBean(zcReqCommonBean,
					zcHeaderBean, commonAllSignParams, commonSignParams,
					headerAllSignParams, headerSignParams, paramMap,needParams);
			if (null == commonSignParams || commonSignParams.isEmpty()) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}
			

			if (null == headerSignParams || headerSignParams.isEmpty()) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}

			if (!ListUtil.compare(commonAllSignParams, commonSignParams)) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}

			if (!ListUtil.compare(headerAllSignParams, headerSignParams)) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}
			
			if(null!=needParams && needParams.size()>0)
			{
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1012);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1012+needParams.toString());
				return msgVo;
			}
			
			//的确没有传递，而通过对象检查传入的是null的情况
			JSONObject headerJsonObj = jsonObj.getJSONObject("header");
			for(String headerStr:headerSignParams)
			{
				
				if(!headerJsonObj.containsKey(headerStr))
				{
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
					return msgVo;
				}
			}
			//的确没有传递，而通过对象检查传入的是null的情况
			for(String commonStr:commonSignParams)
			{
				if(!jsonObj.containsKey(commonStr))
				{
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
					return msgVo;
				}
			}

			// 校验签名
			StringBuilder signedParamsStr = new StringBuilder();
			Collections.sort(headerSignParams);
			for (String headerNeedSign : headerSignParams) {
				log.info("=ljc=============validateReturnedPurchaseReqDataNew():"+headerNeedSign + "-------"
						+ paramMap.get(headerNeedSign));
//				System.out.println("=ljc=============validateReturnedPurchaseReqDataNew():"+headerNeedSign + "-------"
//						+ paramMap.get(headerNeedSign));
				String headerSignParamData = paramMap.get(headerNeedSign);
				signedParamsStr.append(headerSignParamData);
				signedParamsStr.append("|");
			}
			
			signedParamsStr.append(paramMap.get(Constants.ZC_API_VALIDTIME)+"|");
			signedParamsStr.append(paramMap.get(Constants.ZC_API_MERID)+"|");
			signedParamsStr.append(Constants.ZC_API_KEY);
			String serverSignData = EncryptionUtil.getMD5Result(signedParamsStr
					.toString());
			String clientSignData = "";
				clientSignData = zcReqCommonBean.getSign();

			if (null != serverSignData
					&& !serverSignData.equals(clientSignData)) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1003);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1003);
				return msgVo;
			}

		} catch (Exception ex) {
			log.info("=ljc=============validateReturnedPurchaseReqDataNew() exception:"+ex.getMessage());
			msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1006);
			msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1006);
			return msgVo;
		}
		msgVo.setCode(Constants.ZCAPI_SUCCESS_CODE);
		return msgVo;
	}
	
	
	
	
	/**
	 * 旧方法
	 * <AUTHOR> 2017-1-4 上午1:46:05
	 * @param type
	 *            1的时候验证退货 2的时候验证补货
	 * @param zcReturnedPurchaseBean
	 * @param zcReplenishMentBean
	 * @return
	 */
	public ResponseMessageBean validateReturnedPurchaseReqData(String type,JSONObject jsonObj,
			ZCReturnedPurchaseBean zcReturnedPurchaseBean,
			ZCReplenishMentBean zcReplenishMentBean) {
		ResponseMessageBean msgVo = new ResponseMessageBean();
		try {
			// 退货头部
			ReturnedPurchaseHeaderBean zcHeaderBean = null;
			// 补货头部
			ReplenishMentHeaderBean zcRepHeaderBean = null;
		
			if (type.equals(Constants.RETURN_PRODUCT_TYPE)) {
				zcHeaderBean = zcReturnedPurchaseBean.getHeader();
				if (null == zcHeaderBean) {
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
					return msgVo;
				}
			} else if (type.equals(Constants.REPLEMENT_PRODUCT_TYPE)) {
				zcRepHeaderBean = zcReplenishMentBean.getHeader();
				if (null == zcRepHeaderBean) {
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
					return msgVo;
				}
			}

			HashMap<String, String> paramMap = new HashMap<String, String>();
			List<String> commonAllSignParams = new ArrayList<String>();
			List<String> headerAllSignParams = new ArrayList<String>();
			List<String> commonSignParams = new ArrayList<String>();
			List<String> headerSignParams = new ArrayList<String>();
			
			List<String> needParams = new ArrayList<String>();
			if (type.equals(Constants.RETURN_PRODUCT_TYPE)) {
				ReflectHelper.getParametersFromJavaBean(zcReturnedPurchaseBean,
						zcHeaderBean, commonAllSignParams, commonSignParams,
						headerAllSignParams, headerSignParams, paramMap,needParams);
			} else if (type.equals(Constants.REPLEMENT_PRODUCT_TYPE)) {
				ReflectHelper.getParametersFromJavaBean(zcReplenishMentBean,
						zcRepHeaderBean, commonAllSignParams, commonSignParams,
						headerAllSignParams, headerSignParams, paramMap,needParams);
			}
			if (null == commonSignParams || commonSignParams.isEmpty()) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}

			if (null == headerSignParams || headerSignParams.isEmpty()) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}

			if (!ListUtil.compare(commonAllSignParams, commonSignParams)) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}

			if (!ListUtil.compare(headerAllSignParams, headerSignParams)) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
				return msgVo;
			}
			
			//的确没有传递，而通过对象检查传入的是null的情况
			JSONObject headerJsonObj = jsonObj.getJSONObject("header");
			for(String headerStr:headerSignParams)
			{
				
				if(!headerJsonObj.containsKey(headerStr))
				{
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
					return msgVo;
				}
			}
			//的确没有传递，而通过对象检查传入的是null的情况
			for(String commonStr:commonSignParams)
			{
				if(!jsonObj.containsKey(commonStr))
				{
					msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1001);
					msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1001);
					return msgVo;
				}
			}
			

			// 校验签名
			StringBuilder signedParamsStr = new StringBuilder();
			Collections.sort(headerSignParams);
			for (String headerNeedSign : headerSignParams) {
				System.out.println(headerNeedSign + "-------"
						+ paramMap.get(headerNeedSign));
				String headerSignParamData = paramMap.get(headerNeedSign);
				signedParamsStr.append(headerSignParamData);
				signedParamsStr.append("|");
			}
			
			signedParamsStr.append(paramMap.get(Constants.ZC_API_VALIDTIME)+"|");
			signedParamsStr.append(paramMap.get(Constants.ZC_API_MERID)+"|");
			signedParamsStr.append(Constants.ZC_API_KEY);
			String serverSignData = EncryptionUtil.getMD5Result(signedParamsStr
					.toString());
			String clientSignData = "";
			if (type.equals(Constants.RETURN_PRODUCT_TYPE)) {
				clientSignData = zcReturnedPurchaseBean.getSign();

			} else if (type.equals(Constants.REPLEMENT_PRODUCT_TYPE)) {
				clientSignData = zcReplenishMentBean.getSign();
			}
			if (null != serverSignData
					&& !serverSignData.equals(clientSignData)) {
				msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1003);
				msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1003);
				return msgVo;
			}

		} catch (Exception ex) {
			msgVo.setCode(Constants.ZCAPI_ERROR_CODE_1006);
			msgVo.setMsg(Constants.ZCAPI_ERROR_MSG_1006);
			return msgVo;
		}
		msgVo.setCode(Constants.ZCAPI_SUCCESS_CODE);
		return msgVo;
	}

	/**
	 * 向客户端写入内容
	 * 
	 * @param data
	 *            需要写入的数据
	 */
	public void write(String data, HttpServletResponse response) {
		if (StringUtils.isEmpty(data)) {
			return;
		}
		Writer writer = null;
		try {
			writer = response.getWriter();
			writer.write(data);
			writer.flush();

		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				writer.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private void returnJson(HttpServletResponse response, String json)
			throws Exception {
		PrintWriter writer = null;
		response.setCharacterEncoding("UTF-8");
		response.setContentType("text/html; charset=utf-8");
		try {
			writer = response.getWriter();
			writer.print(json);

		} catch (IOException e) {
			log.error("response error", e);
		} finally {
			if (writer != null)
				writer.close();
		}
	}

}