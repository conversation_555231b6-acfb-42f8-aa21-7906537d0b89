select t3_category,from_region,
sum(case when report_type=1 then 1 else 0 end) app_scan_times,
sum(case when report_type=2 then 1 else 0 end) user_scan_times,
sum(case when report_type=3 then 1 else 0 end) admin_record_times,
sum(case when report_type=4 then 1 else 0 end) mechanic_scan_times
from (
select rv.[t3_category], rv.report_type ,isnull(cs1.region, s1.dic_item_name) from_region
from view_nonlocal_sale_report rv
left join wx_t_dic_item s1 on cast(s1.dic_item_code as bigint) = rv.from_distributor_id and s1.dic_type_code = 'non_local_special_partner'
left join dw_customer_org_sales cs1 on cs1.distributor_id = rv.from_distributor_id and cs1.channel_weight&rv.channel_weight>0
left join wx_t_region r1 on r1.id = rv.reporting_region_id
left join wx_t_region r2 on r2.id = r1.parent_id
where (#VARCHAR,fromRegion# is null or #VARCHAR,fromRegion#='' or isnull(cs1.region, s1.dic_item_name)=#VARCHAR,fromRegion#)
and (#BIGINT,fromDistributorId# is null or rv.from_distributor_id=#BIGINT,fromDistributorId#)
and (#INTEGER,channel# is null or rv.channel&#INTEGER,channel#>0)
 and (#VARCHAR,t3Category# is null or #VARCHAR,t3Category#='' or rv.[t3_category] like '%' + #VARCHAR,t3Category# + '%')
and (#INTEGER,permissionWeight#&3>0 or (#INTEGER,permissionWeight#&1024>0 and #BIGINT,fromDistributorId# is not null) or (#INTEGER,permissionWeight#&10>0 and exists (select 1 from dw_sales_role dp_001_sr1 where dp_001_sr1.[sales_cai]=cs1.sales_cai and dp_001_sr1.sales_cai_level like '%_'+#VARCHAR,cai#+'%')))
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or rv.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or rv.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
group by isnull(cs1.region, s1.dic_item_name),rv.from_distributor_id,rv.channel,convert(nvarchar(20),rv.req_time,112),rv.to_distributor_id,r1.id,r2.id,rv.report_type,rv.[t3_category]
) tt group by t3_category,from_region