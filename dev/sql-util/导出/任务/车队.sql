select o.organization_name "经销商名称", w.work_shop_name "车队或项目名称", w.work_shop_address  "地址",
max(case when c.check_name='主要销售/使用产品' then tic1.remark else null end) "主要销售/使用产品",
max(case when c.check_name='客户润滑油每月总用量(升)' then tic1.remark else null end) "客户润滑油每月总用量(升)",
max(case when c.check_name='雪佛龙润滑油每月总销量(升)' then tic1.remark else null end) "雪佛龙润滑油每月总销量(升)",
max(case when c.check_name='主要设备类型' then tic1.remark else null end) "主要设备类型",
max(case when c.check_name='设备品牌' then tic1.remark else null end) "设备品牌",
max(case when c.check_name='设备数量' then tic1.remark else null end) "设备数量",
case when w.status=0 then '潜在' when w.status=3 then '合作' end "状态"
from wx_t_work_shop w
left join wx_t_organization o on o.id=w.partner_id
left join wx_task_sub ts1 on ts1.org_id=w.id
left join wx_task_main tm1 on tm1.task_main_id=ts1.task_main_id --and tm1.tmb_type_code in ('TT_2_7n7/')
left join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id
left join wx_task_check c on c.check_id=tic1.check_id
 where w.customer_type=2 and --w.status=3 and 
 w.delete_flag=0 and o.id!=9 and c.check_name in ('主要销售/使用产品', '客户润滑油每月总用量(升)','雪佛龙润滑油每月总销量(升)',
 '主要设备类型','设备品牌','设备数量')
 group by o.organization_name, w.work_shop_name, w.work_shop_address,o.id,w.status order by o.id
 --102