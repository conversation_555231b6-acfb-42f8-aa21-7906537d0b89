select * from (
select wp1.partner_id, o1.organization_name, u.ch_name "DSR", w1.id, w1.work_shop_name "门店1", w1.work_shop_address, 
case when w1.from_source=1 then '乘用车业务' else '商用油业务' end "门店来源", 
case when w1.shop_recruitment=1 then '是' else '否' end "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w1.id) then '是' else '否' end "B2B店",
case when w1.dms_key is null or w1.dms_key='' then '否' else '是' end "DMS店", w1.contact_person_tel, (select max(s.xg_sj)   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD', 'TT_2_XD_CAI')   and s.task_status = 4 and s.org_id=w1.id) "最新拜访时间", w1.create_time "录入时间",
wp2.partner_id partner_id2, o2.organization_name organization_name2, w2.id id2, w2.work_shop_name "门店2", w2.work_shop_address work_shop_address2, u2.ch_name "DSR2", 
case when w2.from_source=1 then '乘用车业务' else '商用油业务' end "门店来源2", 
case when w2.shop_recruitment=1 then '是' else '否' end "店招店2",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w2.id) then '是' else '否' end "B2B店2",
case when w2.dms_key is null or w2.dms_key='' then '否' else '是' end "DMS店2", w2.contact_person_tel contact_person_tel2, (select max(s.xg_sj)   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD', 'TT_2_XD_CAI')   and s.task_status = 4 and s.org_id=w2.id) "最新拜访时间2", w2.create_time "录入时间2",
(case when w1.contact_person_tel is not null and len(w1.contact_person_tel)>6 and w1.contact_person_tel=w2.contact_person_tel then 30000 else 0 end) +
(case when w1.region_id=w2.region_id then 5000 else 0 end) +
(case when wp1.partner_id=wp2.partner_id then 1000 else 0 end) +
(case when charindex('号', w1.work_shop_address) > 0 and charindex(w1.work_shop_address, w2.work_shop_address) > 0 then 100 else 0 end) +
(case when charindex('号', w2.work_shop_address) > 0 and  charindex(w2.work_shop_address, w1.work_shop_address) > 0 then 100 else 0 end) +
(case when charindex(w1.work_shop_name, w2.work_shop_name) > 0 then 300 else 0 end) +
(case when charindex(w2.work_shop_name, w1.work_shop_name) > 0 then 300 else 0 end) match_weight
from wx_t_work_shop w1
left join wx_t_workshop_partner wp1 on wp1.workshop_id=w1.id
left join wx_t_organization o1 on o1.id=wp1.partner_id
left join wx_t_work_shop w2 on w2.delete_flag=0 and w2.from_source&3>0
left join wx_t_workshop_partner wp2 on wp2.workshop_id=w2.id
left join wx_t_organization o2 on o2.id=wp2.partner_id
left join wx_t_user u on u.user_id=w1.excute_user_id
left join wx_t_user u2 on u2.user_id=w2.excute_user_id
where w1.delete_flag=0 and w1.id<w2.id and w1.status='3' and w2.status='3' and w2.from_source&3>0 and wp1.partner_id<>9 and wp2.partner_id<>9
) x1 where x1.match_weight>=6300
order by x1.partner_id, x1.DSR, x1.id, x1.match_weight desc