;(function($){
	UI.YScrollbar = UI.extend(UI.DomBase, {
		key: 'ui.yscroll',
		cls: 'ui-y-scrollbar', //样式
		configEvents: ['scroll'],
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), o = this.newInstance($d, opts);
			o.contentEl = $('<div class="ui-scrollbar-holder"></div>').appendTo($d.addClass(this.cls).addClass(this.cls + '-' + o.overflow));
			$d.scroll(function(){
				o.triggerScroll();
			});
			return o;
		},
		base: {
			setHeight: function(height){
				UI.setHeight(this.element, height);
				return this;
			},
			setContentHeight: function(contentHeight){
				UI.setHeight(this.contentEl, contentHeight - 0.5);
				return this;
			},
			triggerScroll: function(){
				this.trigger('scroll', this.element.scrollTop());
			},
			scrollTop: function(scrollTop){
				if(scrollTop == undefined){
					return this.element.scrollTop();
				}
				this.element.scrollTop(scrollTop);
			},
			scrollOffset: function(offset){
				var st = this.element.scrollTop();
				st += offset;
				if(st < 0){
					s = 0;
				}
				this.element.scrollTop(st);
				this.trigger('scroll', this.element.scrollTop());
			}
		},
		defaults: {
			scroll: null, //滚动事件
			overflow: 'auto' //scroll, auto
		}
	});
})(jQuery);