var foo = new $.JsonRpcClient({
	ajaxUrl : '/wxPublicRpc.do'
});

$(document).ready(function(){
	bindBackspaceUrl('partnerorder/partnerinventory/partnerinventoryPage.jsp?cacheParams=true');
	initGrid();
});

// 查看
function viewItem(item) {
	window.location.href = "/business/inventory/inventoryDetail.jsp?workshopId="+item;
}

var grid = null;
var store = null;
function initGrid(){
	BUI.use(['bui/grid','bui/data'],function(Grid,Data){
		var Grid = Grid,
		Store = Data.Store,
		columns = [];
		if(!partnerId && userType == 1){
			columns.push({title : '合伙人名称',dataIndex : 'partnerName', width:'30%',sortable: false});
		}
		columns.push(
				{title : '门店名称',dataIndex : 'workShopName', width:'40%'},
				{title : '门店地址',dataIndex : 'workShopAddress', sortable: false, width:'45%',renderer: function(value, item, index){
					var str = value || '';
					if(item.regionName){
						str = item.regionName + ' ' + str;
					}
					if(item.cityName){
						str = item.cityName + ' ' + str;
					}
					if(item.provinceName){
						str = item.provinceName + ' ' + str;
					}
					return str;
				}},
				/*{title : '门店状态', width: '18%', dataIndex : 'status',renderer:function(value,obj){
					return WorkshopStatus[value];
				}},*/
				{
					title : '实时库存(L/瓶)', width: '15%', dataIndex : 'inventoryTotal',sortable: false,
					renderer:function(value,obj){
						var text = (obj.inventoryTotal || 0) + 'L/' + (obj.inventoryUnitsCount || 0) + '瓶';
						return "<a href='javascript: void(0);' onclick='viewItem(" + obj.id + ");'>" + text + "</a>";
					}
				},
				{
					title : '在途库存(L/瓶)', width: '15%', dataIndex : 'inventoryTotal',sortable: false,
					renderer:function(value,obj){
						var text = obj.onWayCapacity + 'L/' + obj.onWayQuantity + '瓶';
						if(obj.onWayQuantity == 0){
							return text;
						}else{
							return '<a href="' + common.ctx + 'business/inventory/onWayStockPage.jsp?workshopId=' + obj.id + 
									'&partnerId=' + partnerId + '&onwaycapacityBigerThanZero=' + onwaycapacityBigerThanZero + '">' + text + '</a>';
						}
					}
				}
			);
		store = new Store({
			url: '/workshop/queryWorkshopWithInventory.do',
			autoLoad:false,
			totalProperty : 'total',
			remoteSort: true,
			sortField: 'createTime',
			sortDirection: 'DESC',
		    proxy : {
				method : 'post'
			},
			params : {
				partnerId : partnerId,
				onwaycapacityBigerThanZero : onwaycapacityBigerThanZero
				},				
			pageSize:10,
			start:0
		});
		grid = new Grid.Grid({
			render:'#workshop_grid',
			columns : columns,
			loadMask: false,
			store: store,
			bbar:{
				pagingBar:true
			}
		});
		grid.render();
		common.initGrid(grid, null, true);
	});
}
