INSERT INTO [dbo].[wx_t_point_account]([POINT_ACCOUNT_TYPE],[POINT_ACCOUNT_OWNER_ID],[IS_ENABLED]            ,[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY]) select 'SPBD',u.user_id,1,0,getdate(),1,getdate(),1 from wx_t_user u where u.login_name='UAT_Test' and status=1


INSERT INTO [dbo].[wx_t_point_value_detail] ([POINT_TYPE],[POINT_ACCOUNT_ID],[POINT_VALUE],[POINT_PAYED],[BUSINESS_ID],[COMMENTS]            ,[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY],[SUB_TYPE]) select 'DSR_CIO_POINT', (select pc.ID from wx_t_point_account pc where pc.POINT_ACCOUNT_OWNER_id=u.user_id), 600, 0, (select top 1 pb.id from dbo.wx_t_point_business pb), '测试', 0,getdate(),1,getdate(),1,null from wx_t_user u where u.login_name='UAT_Test' and status=1

INSERT INTO [dbo].[wx_t_point_value_detail_log]([POINT_VALUE_ID],[MODIFIED_VALUE],[POINT_ACCOUNT_ID],[BUSINESS_ID],[COMMENTS]           ,[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY]) select 33176,  20, (select pc.ID from wx_t_point_account pc where pc.POINT_ACCOUNT_OWNER_ID=u.user_id), (select top 1 pb.id from dbo.wx_t_point_business pb order by id), '测试', 0,getdate(),1,getdate(),1 from wx_t_user u where u.login_name='UAT_Test' and status=1

select top 100 * 
update l set l.modified_value=600
from [wx_t_point_value_detail_log] l where point_value_id=33176