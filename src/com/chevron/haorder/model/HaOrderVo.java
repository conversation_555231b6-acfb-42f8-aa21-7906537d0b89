package com.chevron.haorder.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 
 * @Author: bo.liu  2017-8-29 下午1:57:52 
 * @Version: $Id$
 * @Desc: <p>用于PP后台页面维护------同一批次
 * 			  根据同一个店铺作为一个订单
 * 			</p>
 */
public class HaOrderVo {
	/**
	 * 订单自增id
	 */
	private Long orderid;

	/**
	 * 批次号
	 */
	private Long batchid;

	/**
	 * 订单号
	 */
	private String orderNo;


	/**
	 * 订单类型，备用，，，
	 */
	private String orderType;

	/**
	 * 订单来源 API,手工新建
	 */
	private String orderSource;

	/**
	 * 订单状态
	 */
	private String orderStatus;

	/**
	 * 订单合伙人id
	 */
	private Long orderPartnerid;
	
	/**
	 * 订单合伙人名称
	 */
	private String orderPartnername;

	/**
	 * 导入时间
	 */
	private Date createTime;

	/**
	 * 交易时间
	 */
	private Date tradeTime;

	/**
	 * 发货时间
	 */
	private Date deldate;

	private Date pricingdt;

	/**
	 * 运单号
	 */
	private String billNo;

	/**
	 * 固定常量
	 */
	private String recordType = "|HDR";

	/**
	 * 固定常量
	 */
	private String legsysid = "ZMUD";

	/**
	 * 固定常量
	 */
	private String extbolnr = "ECOMMERCE";


	/**
	 * 收货人名字
	 */
	private String receiveUser;

	/**
	 * 收货人电话
	 */
	private String receivePhone;

	/**
	 * 收货地址
	 */
	private String receiveAddr;

	/**
	 * 接收门店 备用
	 */
	private String receiveWorkshop;

	/**
	 * 应收合计
	 */
	private String totalPrice;

	/**
	 * 日期
	 */
	private String delivery;

	/**
	 * 货品规格
	 */
	private String category;

	/**
	 * 客户备注，，Wechart导入
	 */
	private String clientRemark;

	/**
	 * 客服备注，，Wechart接导入
	 */
	private String customServiceRemark;
	
	/**
	 * 合伙人id
	 */
	private Long partnerId;
	
	/**
	 * 审核状态
	 */
	private String approveStatus;

	
	List<HaOrderLineVo> lstHaOrderLineVos = new ArrayList<HaOrderLineVo>();

    public Long getOrderid() {
        return orderid;
    }

    public void setOrderid(Long orderid) {
        this.orderid = orderid;
    }

    public Long getBatchid() {
        return batchid;
    }

    public void setBatchid(Long batchid) {
        this.batchid = batchid;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource == null ? null : orderSource.trim();
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus == null ? null : orderStatus.trim();
    }

    public Long getOrderPartnerid() {
        return orderPartnerid;
    }

    public void setOrderPartnerid(Long orderPartnerid) {
        this.orderPartnerid = orderPartnerid;
    }

    public String getOrderPartnername() {
        return orderPartnername;
    }

    public void setOrderPartnername(String orderPartnername) {
        this.orderPartnername = orderPartnername == null ? null : orderPartnername.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(Date tradeTime) {
        this.tradeTime = tradeTime;
    }

    public Date getDeldate() {
        return deldate;
    }

    public void setDeldate(Date deldate) {
        this.deldate = deldate;
    }

    public Date getPricingdt() {
        return pricingdt;
    }

    public void setPricingdt(Date pricingdt) {
        this.pricingdt = pricingdt;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo == null ? null : billNo.trim();
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType == null ? null : recordType.trim();
    }

    public String getLegsysid() {
        return legsysid;
    }

    public void setLegsysid(String legsysid) {
        this.legsysid = legsysid == null ? null : legsysid.trim();
    }

    public String getExtbolnr() {
        return extbolnr;
    }

    public void setExtbolnr(String extbolnr) {
        this.extbolnr = extbolnr == null ? null : extbolnr.trim();
    }

    public String getReceiveUser() {
        return receiveUser;
    }

    public void setReceiveUser(String receiveUser) {
        this.receiveUser = receiveUser == null ? null : receiveUser.trim();
    }

    public String getReceivePhone() {
        return receivePhone;
    }

    public void setReceivePhone(String receivePhone) {
        this.receivePhone = receivePhone == null ? null : receivePhone.trim();
    }

    public String getReceiveAddr() {
        return receiveAddr;
    }

    public void setReceiveAddr(String receiveAddr) {
        this.receiveAddr = receiveAddr == null ? null : receiveAddr.trim();
    }

    public String getReceiveWorkshop() {
        return receiveWorkshop;
    }

    public void setReceiveWorkshop(String receiveWorkshop) {
        this.receiveWorkshop = receiveWorkshop == null ? null : receiveWorkshop.trim();
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice == null ? null : totalPrice.trim();
    }

    public String getDelivery() {
        return delivery;
    }

    public void setDelivery(String delivery) {
        this.delivery = delivery == null ? null : delivery.trim();
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    public String getClientRemark() {
        return clientRemark;
    }

    public void setClientRemark(String clientRemark) {
        this.clientRemark = clientRemark == null ? null : clientRemark.trim();
    }

    public String getCustomServiceRemark() {
        return customServiceRemark;
    }

    public void setCustomServiceRemark(String customServiceRemark) {
        this.customServiceRemark = customServiceRemark == null ? null : customServiceRemark.trim();
    }

	public List<HaOrderLineVo> getLstHaOrderLineVos()
	{
		return lstHaOrderLineVos;
	}

	public void setLstHaOrderLineVos(List<HaOrderLineVo> lstHaOrderLineVos)
	{
		this.lstHaOrderLineVos = lstHaOrderLineVos;
	}

	
	
	public Long getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Long partnerId) {
		this.partnerId = partnerId;
	}

	public String getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	@Override
	public String toString() {
		return "HaOrderVo [orderid=" + orderid + ", batchid=" + batchid
				+ ", orderNo=" + orderNo + ", orderType=" + orderType
				+ ", orderSource=" + orderSource + ", orderStatus="
				+ orderStatus + ", orderPartnerid=" + orderPartnerid
				+ ", orderPartnername=" + orderPartnername + ", createTime="
				+ createTime + ", tradeTime=" + tradeTime + ", deldate="
				+ deldate + ", pricingdt=" + pricingdt + ", billNo=" + billNo
				+ ", recordType=" + recordType + ", legsysid=" + legsysid
				+ ", extbolnr=" + extbolnr + ", receiveUser=" + receiveUser
				+ ", receivePhone=" + receivePhone + ", receiveAddr="
				+ receiveAddr + ", receiveWorkshop=" + receiveWorkshop
				+ ", totalPrice=" + totalPrice + ", delivery=" + delivery
				+ ", category=" + category + ", clientRemark=" + clientRemark
				+ ", customServiceRemark=" + customServiceRemark
				+ ", partnerId=" + partnerId + ", approveStatus="
				+ approveStatus + ", lstHaOrderLineVos=" + lstHaOrderLineVos
				+ "]";
	}

	
    
    
}