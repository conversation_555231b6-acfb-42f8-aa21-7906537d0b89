package com.chevron.plc.controller;

import com.chevron.plc.dao.OutStockLineVoMapper;
import com.chevron.plc.dao.OutStockProductVoMapper;
import com.chevron.plc.model.CumulativeSaleVo;
import com.chevron.plc.model.OutStockLineVo;
import com.chevron.plc.model.OutStockProductVo;
import com.chevron.plc.model.OutStockVo;
import com.chevron.plc.service.AppOutStockService;
import com.chevron.pms.model.WsVerifOutStockParams;
import com.chevron.pms.service.InventoryService;
import com.common.base.BaseResp;
import com.common.base.ResultCode;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.ResponseMap;
import com.common.util.StringUtils;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.jbarcode.JBarcode;
import org.jbarcode.encode.Code128Encoder;
import org.jbarcode.encode.InvalidAtributeException;
import org.jbarcode.paint.BaseLineTextPainter;
import org.jbarcode.paint.WidthCodedPainter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * 出库单控制器
 * <AUTHOR>
 *
 */
@Controller
@Api(value = "出库单Controller", tags = "出库单Controller")
public class OutStockController {
	
	private Logger log = LoggerFactory.getLogger(OutStockController.class);
	
	@Autowired
	private AppOutStockService outStockService;
	
	@Autowired
	private InventoryService inventoryService;
	
	@Autowired
	private OutStockLineVoMapper outStockLineVoMapper;
	
	@Autowired
	private OutStockProductVoMapper outStockProductVoMapper;
	
	@ResponseBody
    @ApiOperation(value="库存列表页面分页查询接口",  httpMethod="POST", notes="库存列表页面分页查询接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/stockline/list.do")
	public ResponseMap queryStockLineList(@ApiParam(name="codeType", value="被扫码类型", required=false) @RequestParam(value="codeType", required=false) String codeType, 
			@ApiParam(name="code", value="被扫码(多个以逗号分割)", required=false) @RequestParam(value="code", required=false) String code,
			HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("queryStockLineList: " + codeType + "," + code);

		try {
			Map<String, Object> params = new HashMap<String, Object>(3);
			params.put("codeType", codeType);
			if(StringUtils.isNotBlank(code)) {
				params.put("codes", code.split(","));
			}
			resultMap.setListResult(outStockLineVoMapper.queryByParams(params));
			log.info("queryStockLineList success." );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.plc.controller.OutStockController.queryStockLineList", codeType + "," + code);
		}
		return resultMap;
	}
	
	@ResponseBody
    @ApiOperation(value="出库产品列表页面分页查询接口",  httpMethod="POST", notes="出库产品列表页面分页查询接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/stockproduct/list.do")
	public ResponseMap queryStockProductList(@ApiParam(name="orderType", value="订单类型", required=true) @RequestParam(value="orderType", required=true) String orderType, 
			@ApiParam(name="orderNo", value="订单编号", required=true) @RequestParam(value="orderNo", required=true) String orderNo,
			HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("queryStockProductList: " + orderType + "," + orderNo);

		try {
			Map<String, Object> params = new HashMap<String, Object>(3);
			params.put("orderType", orderType);
			params.put("orderNo", orderNo);
			resultMap.setListResult(outStockProductVoMapper.queryByParams(params));
			log.info("queryStockProductList success." );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.plc.controller.OutStockController.queryStockProductList", orderType + "," + orderNo);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/outstock/completeproductinfo.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> completeProductInfo() throws Exception{
		return outStockService.completeOutStockProductInfo();
	}
	
	@ResponseBody
	@RequestMapping(value="/outstock/getOutStockDetailByStockOutNo.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> getOutStockDetailByStockOutNo(@RequestParam("stockOutNo")String stockOutNo) throws Exception{
		return outStockService.getOutStockDetailByStockOutNo(stockOutNo);
	}
	
	@ResponseBody
	@RequestMapping(value="/outstock/queryOutStockListByPage.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> queryInStockListByPage(@RequestParam("stockOutNo")String stockOutNo,
			@RequestParam("status")String status, @RequestParam("startPage")int startPage,@RequestParam("pageCount") int pageCount){
		return outStockService.queryOutStockListByPage(stockOutNo, status, startPage, pageCount);
	}
	
	@ResponseBody
	@RequestMapping(value="/outstock/sanOutStock.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> sanOutStockTest(@RequestParam("stockOutNo")String stockOutNo) throws Exception{
		
		OutStockVo outstock = new OutStockVo();
		outstock.setStockOutNo(stockOutNo);
		/*outstock.setDeliveryNumber("sf");
		outstock.setStockFrom("7");
		outstock.setStockFromType(Constants.STOCK_TYPE_SP);
		outstock.setStockTo("30068");
		outstock.setStockToType(Constants.STOCK_TYPE_WORKSHOP);*/
		outstock.setOutTime(new Date());
		outstock.setScanGunNo("scangun");
		outstock.setScanPersonId(ContextUtil.getCurUserId().toString());
		
		List<OutStockLineVo> outStockLinesList = new ArrayList<OutStockLineVo>();
		OutStockLineVo sl1 = new OutStockLineVo();
		sl1.setStockOutNo(stockOutNo);
		sl1.setCode("102912901450");
		sl1.setCodeType(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);
		sl1.setScanTime(new Date());
		outStockLinesList.add(sl1);
		
		OutStockLineVo sl2 = new OutStockLineVo();
		sl2.setStockOutNo(stockOutNo);
		sl2.setCode("1029120924");
		sl2.setCodeType(Constants.STOCK_LINE_CODE_TYPE_BOX);
		sl2.setScanTime(new Date());
		outStockLinesList.add(sl2);
		
		OutStockLineVo sl3 = new OutStockLineVo();
		sl3.setStockOutNo(stockOutNo);
		sl3.setCode("102912901731");
		sl3.setCodeType(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);
		sl3.setScanTime(new Date());
		outStockLinesList.add(sl3);
		
		/*OutStockLineVo sl4 = new OutStockLineVo();
		sl4.setStockOutNo(stockOutNo);
		sl4.setCode("1029120920");
		sl4.setCodeType(Constants.STOCK_LINE_CODE_TYPE_BOX);
		sl4.setScanTime(new Date());
		outStockLinesList.add(sl4);*/
		
		
		OutStockLineVo[] outStockLines = new OutStockLineVo[outStockLinesList.size()];
		/*
		OutStockLineVo sl3 = new OutStockLineVo();
		sl3.setStockOutNo(stockOutNo);
		sl3.setCode("500268LPK01A");
		sl3.setCodeType(Constants.STOCK_LINE_CODE_TYPE_SKU);
		sl3.setScanTime(new Date());
		outStockLinesList.add(sl3);
		
		OutStockLineVo sl4 = new OutStockLineVo();
		sl4.setStockOutNo(stockOutNo);
		sl4.setCode("500268NJK01A");
		sl4.setCodeType(Constants.STOCK_LINE_CODE_TYPE_SKU);
		sl4.setScanTime(new Date());
		outStockLinesList.add(sl4);*/
		
		
		List<OutStockProductVo> productList = new ArrayList<OutStockProductVo>();
		OutStockProductVo otpVo1 = new OutStockProductVo();
		otpVo1.setSku("500244LPK");
		otpVo1.setStockOutNo(stockOutNo);
		otpVo1.setActualOutCount(Long.valueOf(20));
		productList.add(otpVo1);
		
		OutStockProductVo otpVo2 = new OutStockProductVo();
		otpVo2.setSku("500245LPK");
		otpVo2.setStockOutNo(stockOutNo);
		otpVo2.setActualOutCount(Long.valueOf(15));
		productList.add(otpVo2);
		
		OutStockProductVo[] productLines = new OutStockProductVo[productList.size()];
		
		return outStockService.confirmOutStock(outstock, outStockLinesList.toArray(outStockLines),productList.toArray(productLines));
	}
	
	@ResponseBody
	@RequestMapping(value="/outstock/checkOutStockBoxCodeProductAmount.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> checkOutStockBoxCodeProductAmount(@RequestParam("stockOutNo")String stockOutNo) throws Exception{
		OutStockVo outstock = new OutStockVo();
		outstock.setStockOutNo(stockOutNo);
		outStockService.checkOutStockBoxCodeProductAmount(outstock);
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value="/inventory/workshopinventory.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> workshopinventory() throws Exception{
		List<WsVerifOutStockParams> verifcationList = new ArrayList<WsVerifOutStockParams>();
		WsVerifOutStockParams param1 = new WsVerifOutStockParams();
		param1.setSku("500249LPK");
		param1.setWorkshopId(Long.valueOf(127344));
		param1.setLogisticsCode("logistics01");
		verifcationList.add(param1);
		
		WsVerifOutStockParams param2 = new WsVerifOutStockParams();
		param2.setSku("500249LPK");
		param2.setWorkshopId(Long.valueOf(127344));
		param2.setLogisticsCode("logistics02");
		verifcationList.add(param2);
		
		WsVerifOutStockParams param3 = new WsVerifOutStockParams();
		param3.setSku("500245LPK");
		param3.setWorkshopId(Long.valueOf(127344));
		verifcationList.add(param3);
		
		WsVerifOutStockParams param4 = new WsVerifOutStockParams();
		param4.setSku("500248NJK");
		param4.setWorkshopId(Long.valueOf(127488));
		param4.setLogisticsCode("logistics03");
		verifcationList.add(param4);

		inventoryService.updateWsInventoryByVerificationList(verifcationList);
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value="/outstock/barcodedown.do", method = {RequestMethod.POST, RequestMethod.GET})
	public void download(HttpServletResponse response) throws IOException, InvalidAtributeException {
        try {
        	 BufferedImage bi = null;
        	 
             JBarcode localJBarcode = new JBarcode(Code128Encoder.getInstance(),WidthCodedPainter.getInstance(),BaseLineTextPainter.getInstance());  
  
             // 尺寸，面积，大小
             localJBarcode.setXDimension(Double.valueOf(0.5).doubleValue());
             // 条形码高度
             localJBarcode.setBarHeight(Double.valueOf(17).doubleValue());
             // 宽度率
             localJBarcode.setWideRatio(Double.valueOf(1).doubleValue());
             // 是否校验最后一位，默认是false
             localJBarcode.setShowCheckDigit(false);
  
             // 生成二维码
             bi = localJBarcode.createBarcode("11703300003");
			
             ByteArrayOutputStream out = new ByteArrayOutputStream();

	
			ImageIO.write(bi, "png", out);
	
			byte[] b = out.toByteArray();
	
			response.getOutputStream().write(b);
            
        }
        catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("处理下载文件时发生IO异常");
        }
    }

	/*
	* 实际累计销量
	* */
	@ResponseBody
	@RequestMapping(value = "/outstock/queryCumulativeSales.do", method = RequestMethod.POST, produces = "application/json")
	public BaseResp queryCumulativeSales(@RequestBody CumulativeSaleVo request) {
		try {
			return outStockService.queryCumulativeSales(request);
		} catch (Exception e) {
			log.error("alertExpiration exception:", e);
			return new BaseResp(ResultCode.EXCETPION);
		}
	}

	/*
	* 月平均实际销量
	* */
	@ResponseBody
	@RequestMapping(value = "/outstock/queryAvgSales.do", method = RequestMethod.POST, produces = "application/json")
	public BaseResp queryAvgSales(@RequestBody CumulativeSaleVo request) {
		try {
			return outStockService.queryAvgSales(request);
		} catch (Exception e) {
			log.error("alertExpiration exception:", e);
			return new BaseResp(ResultCode.EXCETPION);
		}
	}

}
