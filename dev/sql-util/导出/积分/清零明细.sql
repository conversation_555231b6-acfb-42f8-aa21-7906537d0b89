--select * from (
select pp.point_name,  pp.point_code, o.organization_name, o.id, o.status, sum(pvd.POINT_VALUE) v, pvd.COMMENTS,pb.creation_time
 FROM dbo.wx_t_point_value_detail pvd
  LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
  LEFT JOIN dbo.wx_t_organization o ON pa.POINT_ACCOUNT_OWNER_ID = o.id
  LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
  left join wx_t_promotion_point pp on pp.point_code=pvd.POINT_TYPE
  left join wx_t_point_business pb on pvd.business_id=pb.id
  where --pp.point_type not in ('cdm_stock','caltex','abp') and 
  o.status=1 and pb.BUSINESS_TYPE_CODE='CLEAR_POINT' --and pvd.POINT_TYPE='CDM_PIT_PACK_20L'
  and o.id=56398
  group by pp.point_name,  pp.point_code, o.organization_name, o.status, o.id,pvd.COMMENTS, pb.creation_time--) a where a.v>0
  order by pb.creation_time desc