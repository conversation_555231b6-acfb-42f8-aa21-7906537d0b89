
			SELECT v.order_no "订单号", v.plate_number "车牌号", v.buy_user_name "车主姓名", v.phone_no "手机号", 
			v.region_name "区域", v.org_name "订单来源", v.preferential_type_price "订单类型",
			convert(varchar(100), v.remaining_service_times)+'次' "剩余服务", 
			case when v.status='0' then '待第三方确认'
			 when v.status='1' then '待CSR确认'
			  when v.status='3' then '已下单'
			   when v.status='6' then '已取消'
			    when v.status='9' then '已拒绝'
				 when v.status='2' then '待下单'
				  when v.status='4' then '待发货'
				   when v.status='5' then '已发货'
				    when v.status='7' then '已签收' else v.status end "订单状态", v.create_time "录入时间"
			FROM (
				SELECT tt_order.*, tt_pre_type.per_hours_subsidy AS per_hours_subsidy, t_org.organization_name AS org_name
				FROM wx_t_order tt_order
					LEFT JOIN wx_t_preferential_type tt_pre_type ON tt_order.type = tt_pre_type.type_code
					LEFT JOIN wx_t_organization t_org ON t_org.id = tt_order.partner_id
				WHERE tt_order.order_type = 'DP'
					AND 1 = 1
			) v
			