select po.order_no, o.organization_name, c.ship_to_code, c.payment_term, po.total_liter_count,pl.sku, p.name, p.box_capacity,pl.actual_amount--, pl.total_value-pl.ladder_price*pl.actual_amount,pl.*,po.* 
from wx_t_partner_order_line pl
left join wx_t_partner_order po on po.id=pl.partner_order_id
left join wx_t_organization o on o.id=po.partner_id
left join [PP_MID].[dbo].[mid_partner_sale_config] c on c.id=po.partner_sale_config_id
left join wx_t_product p on p.sku=pl.sku
where 1=1 --and pl.sku in (select d.dic_item_code from wx_t_dic_item d where d.dic_type_code='sap.bundle.sku.config')
and po.status!=0 and po.version_no>0 and po.id=4634
--and pl.ladder_price*pl.actual_amount<pl.total_value
 --and po.total_liter_count>(select sum(pl.actual_amount * p.box_capacity) from wx_t_partner_order_line pl left join wx_t_product p on p.sku=pl.sku where po.id=pl.partner_order_id)
order by po.order_no, po.create_time desc 


select po.order_no, po.total_liter_count,(select sum(pl.actual_amount * p.box_capacity) from wx_t_partner_order_line pl left join wx_t_product p on p.sku=pl.sku where po.id=pl.partner_order_id) from wx_t_partner_order po 
where po.status!=0 and po.version_no>0  -and po.total_liter_count>(select sum(pl.actual_amount * p.box_capacity) from wx_t_partner_order_line pl left join wx_t_product p on p.sku=pl.sku where po.id=pl.partner_order_id)

select *
--delete pl
from  wx_t_partner_order_line pl
where exists (select 1 from wx_t_partner_order o where pl.partner_order_id=o.id and o.order_no='P07210811004221')

--drop table PP_MID.dbo.wx_t_partner_order_line
select 4295 [partner_order_id]
      ,[product_id]
      ,[sku]
      ,[product_name]
      ,[amount]
      ,[units]
      ,[price]
      ,[status]
      ,[create_time]
      ,[update_time]
      ,[creator]
      ,[remark]
      ,[actual_amount]
      ,[type]
      ,[discount_fee]
      ,[discounted_price]
      ,[total_value]
      ,[discounted_total_value]
      ,[order_no]
      ,[ladder_price]
      ,[deductible_amount]
      ,[total_value_after_deductible]
      ,[free_amount]
      ,[promotion_title]
      ,[promotion_sku]
      ,[package_sell]
      ,[partner_sale_config_id] into PP_MID.dbo.wx_t_partner_order_line
from  wx_t_partner_order_line pl
where exists (select 1 from wx_t_partner_order o where pl.partner_order_id=o.id and o.order_no='P07210806004517')