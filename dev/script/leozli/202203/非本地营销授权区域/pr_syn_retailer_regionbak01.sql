CREATE PROCEDURE [dbo].[pr_syn_retailer_region] 
( 
	@syn_type nvarchar(50) NULL,
    @partner_id bigint NULL
)
as
begin
	set xact_abort on
	SET NOCOUNT ON
	if @syn_type='ALL'
	begin
			delete rp1 
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			left join wx_t_region_partner rp1 on rp1.partner_id=o.id
			where o.type=3 and o.id in (select io1.id from wx_t_organization io1 left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=io1.id
				where io1.status=1 and io1.type=3
				and (exists (select 1 from wx_t_region_partner irp1 left join wx_t_region_partner irp2 on irp2.partner_id=io1.id 
				and irp2.region_id=irp1.region_id and irp2.channel_weight=irp1.channel_weight 
				where irp1.partner_id=pe1.ext_property1 and irp2.id is null)
				or exists (select 1 from wx_t_region_partner irp1 left join wx_t_region_partner irp2 on irp2.partner_id=pe1.ext_property1
				and irp2.region_id=irp1.region_id and irp2.channel_weight=irp1.channel_weight 
				where irp1.partner_id=io1.id and irp2.id is null)))
			
			insert into wx_t_region_partner (region_id, partner_id,create_time,channel_weight)
			select rp1.region_id, o.id, getdate(), channel_weight 
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			left join wx_t_region_partner rp1 on rp1.partner_id=pe.ext_property1
			where o.type=3 and o.id in (select io1.id from wx_t_organization io1 left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=io1.id
				where io1.status=1 and io1.type=3
				and (exists (select 1 from wx_t_region_partner irp1 left join wx_t_region_partner irp2 on irp2.partner_id=io1.id 
				and irp2.region_id=irp1.region_id and irp2.channel_weight=irp1.channel_weight 
				where irp1.partner_id=pe1.ext_property1 and irp2.id is null)
				or exists (select 1 from wx_t_region_partner irp1 left join wx_t_region_partner irp2 on irp2.partner_id=pe1.ext_property1
				and irp2.region_id=irp1.region_id and irp2.channel_weight=irp1.channel_weight 
				where irp1.partner_id=io1.id and irp2.id is null)))
	end 
	else if @syn_type='DISTRIBUTOR'
	begin
		if @partner_id is null
		begin
			PRINT 'partner id is null'
			RETURN
		end 
		else
		begin
			delete rp1 
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			left join wx_t_region_partner rp1 on rp1.partner_id=o.id
			where o.type=3 and o.status=1 and pe.ext_property1=convert(varchar(20),@partner_id)
			
			insert into wx_t_region_partner (region_id, partner_id,create_time,channel_weight)
			select rp1.region_id, o.id, getdate(), channel_weight 
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			left join wx_t_region_partner rp1 on rp1.partner_id=pe.ext_property1
			where o.type=3 and o.status=1 and pe.ext_property1=convert(varchar(20),@partner_id)
		end 
	end 
	else if @syn_type='RETAILER'
	begin
		if @partner_id is null
		begin
			PRINT 'retailer id is null'
			RETURN
		end 
		else
		begin
			insert into wx_t_region_partner (region_id, partner_id,create_time,channel_weight)
			select rp1.region_id, o.id, getdate(), channel_weight 
			from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
			left join wx_t_region_partner rp1 on rp1.partner_id=pe.ext_property1
			where o.type=3 and o.id=@partner_id
		end 
	end 
	else 
	begin
		PRINT 'unkown syn_type'
		RETURN
	end 
END