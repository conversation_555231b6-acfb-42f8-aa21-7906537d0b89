package com.chevron.master.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 经销商客户单表查询条件
 * <AUTHOR>
 * @version 1.0 2020-11-18 10:53
 */
public class WorkshopMasterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkshopMasterExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameIsNull() {
            addCriterion("work_shop_name is null");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameIsNotNull() {
            addCriterion("work_shop_name is not null");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameEqualTo(String value) {
            addCriterion("work_shop_name =", value, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameNotEqualTo(String value) {
            addCriterion("work_shop_name <>", value, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameGreaterThan(String value) {
            addCriterion("work_shop_name >", value, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameGreaterThanOrEqualTo(String value) {
            addCriterion("work_shop_name >=", value, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameLessThan(String value) {
            addCriterion("work_shop_name <", value, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameLessThanOrEqualTo(String value) {
            addCriterion("work_shop_name <=", value, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameIn(List<String> values) {
            addCriterion("work_shop_name in", values, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameNotIn(List<String> values) {
            addCriterion("work_shop_name not in", values, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameBetween(String value1, String value2) {
            addCriterion("work_shop_name between", value1, value2, "workshopName");
            return (Criteria) this;
        }

        public Criteria andWorkshopNameNotBetween(String value1, String value2) {
            addCriterion("work_shop_name not between", value1, value2, "workshopName");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(Long value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(Long value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(Long value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(Long value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(Long value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<Long> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<Long> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(Long value1, Long value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(Long value1, Long value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressIsNull() {
            addCriterion("work_shop_address is null");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressIsNotNull() {
            addCriterion("work_shop_address is not null");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressEqualTo(String value) {
            addCriterion("work_shop_address =", value, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressNotEqualTo(String value) {
            addCriterion("work_shop_address <>", value, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressGreaterThan(String value) {
            addCriterion("work_shop_address >", value, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressGreaterThanOrEqualTo(String value) {
            addCriterion("work_shop_address >=", value, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressLessThan(String value) {
            addCriterion("work_shop_address <", value, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressLessThanOrEqualTo(String value) {
            addCriterion("work_shop_address <=", value, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressIn(List<String> values) {
            addCriterion("work_shop_address in", values, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressNotIn(List<String> values) {
            addCriterion("work_shop_address not in", values, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressBetween(String value1, String value2) {
            addCriterion("work_shop_address between", value1, value2, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andWorkshopAddressNotBetween(String value1, String value2) {
            addCriterion("work_shop_address not between", value1, value2, "workshopAddress");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNull() {
            addCriterion("longitude is null");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNotNull() {
            addCriterion("longitude is not null");
            return (Criteria) this;
        }

        public Criteria andLongitudeEqualTo(Double value) {
            addCriterion("longitude =", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotEqualTo(Double value) {
            addCriterion("longitude <>", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThan(Double value) {
            addCriterion("longitude >", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThanOrEqualTo(Double value) {
            addCriterion("longitude >=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThan(Double value) {
            addCriterion("longitude <", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThanOrEqualTo(Double value) {
            addCriterion("longitude <=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeIn(List<Double> values) {
            addCriterion("longitude in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotIn(List<Double> values) {
            addCriterion("longitude not in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeBetween(Double value1, Double value2) {
            addCriterion("longitude between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotBetween(Double value1, Double value2) {
            addCriterion("longitude not between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNull() {
            addCriterion("latitude is null");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNotNull() {
            addCriterion("latitude is not null");
            return (Criteria) this;
        }

        public Criteria andLatitudeEqualTo(Double value) {
            addCriterion("latitude =", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotEqualTo(Double value) {
            addCriterion("latitude <>", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThan(Double value) {
            addCriterion("latitude >", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThanOrEqualTo(Double value) {
            addCriterion("latitude >=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThan(Double value) {
            addCriterion("latitude <", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThanOrEqualTo(Double value) {
            addCriterion("latitude <=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIn(List<Double> values) {
            addCriterion("latitude in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotIn(List<Double> values) {
            addCriterion("latitude not in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeBetween(Double value1, Double value2) {
            addCriterion("latitude between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotBetween(Double value1, Double value2) {
            addCriterion("latitude not between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andContactPersonIsNull() {
            addCriterion("contact_person is null");
            return (Criteria) this;
        }

        public Criteria andContactPersonIsNotNull() {
            addCriterion("contact_person is not null");
            return (Criteria) this;
        }

        public Criteria andContactPersonEqualTo(String value) {
            addCriterion("contact_person =", value, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonNotEqualTo(String value) {
            addCriterion("contact_person <>", value, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonGreaterThan(String value) {
            addCriterion("contact_person >", value, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonGreaterThanOrEqualTo(String value) {
            addCriterion("contact_person >=", value, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonLessThan(String value) {
            addCriterion("contact_person <", value, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonLessThanOrEqualTo(String value) {
            addCriterion("contact_person <=", value, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonIn(List<String> values) {
            addCriterion("contact_person in", values, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonNotIn(List<String> values) {
            addCriterion("contact_person not in", values, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonBetween(String value1, String value2) {
            addCriterion("contact_person between", value1, value2, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonNotBetween(String value1, String value2) {
            addCriterion("contact_person not between", value1, value2, "contactPerson");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelIsNull() {
            addCriterion("contact_person_tel is null");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelIsNotNull() {
            addCriterion("contact_person_tel is not null");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelEqualTo(String value) {
            addCriterion("contact_person_tel =", value, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelNotEqualTo(String value) {
            addCriterion("contact_person_tel <>", value, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelGreaterThan(String value) {
            addCriterion("contact_person_tel >", value, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelGreaterThanOrEqualTo(String value) {
            addCriterion("contact_person_tel >=", value, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelLessThan(String value) {
            addCriterion("contact_person_tel <", value, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelLessThanOrEqualTo(String value) {
            addCriterion("contact_person_tel <=", value, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelIn(List<String> values) {
            addCriterion("contact_person_tel in", values, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelNotIn(List<String> values) {
            addCriterion("contact_person_tel not in", values, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelBetween(String value1, String value2) {
            addCriterion("contact_person_tel between", value1, value2, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andContactPersonTelNotBetween(String value1, String value2) {
            addCriterion("contact_person_tel not between", value1, value2, "contactPersonTel");
            return (Criteria) this;
        }

        public Criteria andFromSourceIsNull() {
            addCriterion("from_source is null");
            return (Criteria) this;
        }

        public Criteria andFromSourceIsNotNull() {
            addCriterion("from_source is not null");
            return (Criteria) this;
        }

        public Criteria andFromSourceEqualTo(Integer value) {
            addCriterion("from_source =", value, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceNotEqualTo(Integer value) {
            addCriterion("from_source <>", value, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceGreaterThan(Integer value) {
            addCriterion("from_source >", value, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("from_source >=", value, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceLessThan(Integer value) {
            addCriterion("from_source <", value, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceLessThanOrEqualTo(Integer value) {
            addCriterion("from_source <=", value, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceIn(List<Integer> values) {
            addCriterion("from_source in", values, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceNotIn(List<Integer> values) {
            addCriterion("from_source not in", values, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceBetween(Integer value1, Integer value2) {
            addCriterion("from_source between", value1, value2, "fromSource");
            return (Criteria) this;
        }

        public Criteria andFromSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("from_source not between", value1, value2, "fromSource");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Integer value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Integer value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Integer value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Integer value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Integer value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Integer> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Integer> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightIsNull() {
            addCriterion("business_weight is null");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightIsNotNull() {
            addCriterion("business_weight is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightEqualTo(Integer value) {
            addCriterion("business_weight =", value, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightNotEqualTo(Integer value) {
            addCriterion("business_weight <>", value, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightGreaterThan(Integer value) {
            addCriterion("business_weight >", value, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_weight >=", value, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightLessThan(Integer value) {
            addCriterion("business_weight <", value, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightLessThanOrEqualTo(Integer value) {
            addCriterion("business_weight <=", value, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightIn(List<Integer> values) {
            addCriterion("business_weight in", values, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightNotIn(List<Integer> values) {
            addCriterion("business_weight not in", values, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightBetween(Integer value1, Integer value2) {
            addCriterion("business_weight between", value1, value2, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andBusinessWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("business_weight not between", value1, value2, "businessWeight");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdIsNull() {
            addCriterion("excute_user_id is null");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdIsNotNull() {
            addCriterion("excute_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdEqualTo(Long value) {
            addCriterion("excute_user_id =", value, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdNotEqualTo(Long value) {
            addCriterion("excute_user_id <>", value, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdGreaterThan(Long value) {
            addCriterion("excute_user_id >", value, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("excute_user_id >=", value, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdLessThan(Long value) {
            addCriterion("excute_user_id <", value, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdLessThanOrEqualTo(Long value) {
            addCriterion("excute_user_id <=", value, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdIn(List<Long> values) {
            addCriterion("excute_user_id in", values, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdNotIn(List<Long> values) {
            addCriterion("excute_user_id not in", values, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdBetween(Long value1, Long value2) {
            addCriterion("excute_user_id between", value1, value2, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andExcuteUserIdNotBetween(Long value1, Long value2) {
            addCriterion("excute_user_id not between", value1, value2, "excuteUserId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdIsNull() {
            addCriterion("photo_id is null");
            return (Criteria) this;
        }

        public Criteria andPhotoIdIsNotNull() {
            addCriterion("photo_id is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoIdEqualTo(String value) {
            addCriterion("photo_id =", value, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdNotEqualTo(String value) {
            addCriterion("photo_id <>", value, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdGreaterThan(String value) {
            addCriterion("photo_id >", value, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdGreaterThanOrEqualTo(String value) {
            addCriterion("photo_id >=", value, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdLessThan(String value) {
            addCriterion("photo_id <", value, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdLessThanOrEqualTo(String value) {
            addCriterion("photo_id <=", value, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdIn(List<String> values) {
            addCriterion("photo_id in", values, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdNotIn(List<String> values) {
            addCriterion("photo_id not in", values, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdBetween(String value1, String value2) {
            addCriterion("photo_id between", value1, value2, "photoId");
            return (Criteria) this;
        }

        public Criteria andPhotoIdNotBetween(String value1, String value2) {
            addCriterion("photo_id not between", value1, value2, "photoId");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyIsNull() {
            addCriterion("work_shop_name_py is null");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyIsNotNull() {
            addCriterion("work_shop_name_py is not null");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyEqualTo(String value) {
            addCriterion("work_shop_name_py =", value, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyNotEqualTo(String value) {
            addCriterion("work_shop_name_py <>", value, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyGreaterThan(String value) {
            addCriterion("work_shop_name_py >", value, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyGreaterThanOrEqualTo(String value) {
            addCriterion("work_shop_name_py >=", value, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyLessThan(String value) {
            addCriterion("work_shop_name_py <", value, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyLessThanOrEqualTo(String value) {
            addCriterion("work_shop_name_py <=", value, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyIn(List<String> values) {
            addCriterion("work_shop_name_py in", values, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyNotIn(List<String> values) {
            addCriterion("work_shop_name_py not in", values, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyBetween(String value1, String value2) {
            addCriterion("work_shop_name_py between", value1, value2, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andWorkShopNamePyNotBetween(String value1, String value2) {
            addCriterion("work_shop_name_py not between", value1, value2, "workShopNamePy");
            return (Criteria) this;
        }

        public Criteria andExtFlagIsNull() {
            addCriterion("ext_flag is null");
            return (Criteria) this;
        }

        public Criteria andExtFlagIsNotNull() {
            addCriterion("ext_flag is not null");
            return (Criteria) this;
        }

        public Criteria andExtFlagEqualTo(Integer value) {
            addCriterion("ext_flag =", value, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagNotEqualTo(Integer value) {
            addCriterion("ext_flag <>", value, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagGreaterThan(Integer value) {
            addCriterion("ext_flag >", value, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("ext_flag >=", value, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagLessThan(Integer value) {
            addCriterion("ext_flag <", value, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagLessThanOrEqualTo(Integer value) {
            addCriterion("ext_flag <=", value, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagIn(List<Integer> values) {
            addCriterion("ext_flag in", values, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagNotIn(List<Integer> values) {
            addCriterion("ext_flag not in", values, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagBetween(Integer value1, Integer value2) {
            addCriterion("ext_flag between", value1, value2, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("ext_flag not between", value1, value2, "extFlag");
            return (Criteria) this;
        }

        public Criteria andExtFlag2IsNull() {
            addCriterion("ext_flag2 is null");
            return (Criteria) this;
        }

        public Criteria andExtFlag2IsNotNull() {
            addCriterion("ext_flag2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtFlag2EqualTo(Integer value) {
            addCriterion("ext_flag2 =", value, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2NotEqualTo(Integer value) {
            addCriterion("ext_flag2 <>", value, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2GreaterThan(Integer value) {
            addCriterion("ext_flag2 >", value, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2GreaterThanOrEqualTo(Integer value) {
            addCriterion("ext_flag2 >=", value, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2LessThan(Integer value) {
            addCriterion("ext_flag2 <", value, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2LessThanOrEqualTo(Integer value) {
            addCriterion("ext_flag2 <=", value, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2In(List<Integer> values) {
            addCriterion("ext_flag2 in", values, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2NotIn(List<Integer> values) {
            addCriterion("ext_flag2 not in", values, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2Between(Integer value1, Integer value2) {
            addCriterion("ext_flag2 between", value1, value2, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag2NotBetween(Integer value1, Integer value2) {
            addCriterion("ext_flag2 not between", value1, value2, "extFlag2");
            return (Criteria) this;
        }

        public Criteria andExtFlag3IsNull() {
            addCriterion("ext_flag3 is null");
            return (Criteria) this;
        }

        public Criteria andExtFlag3IsNotNull() {
            addCriterion("ext_flag3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtFlag3EqualTo(Integer value) {
            addCriterion("ext_flag3 =", value, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3NotEqualTo(Integer value) {
            addCriterion("ext_flag3 <>", value, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3GreaterThan(Integer value) {
            addCriterion("ext_flag3 >", value, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3GreaterThanOrEqualTo(Integer value) {
            addCriterion("ext_flag3 >=", value, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3LessThan(Integer value) {
            addCriterion("ext_flag3 <", value, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3LessThanOrEqualTo(Integer value) {
            addCriterion("ext_flag3 <=", value, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3In(List<Integer> values) {
            addCriterion("ext_flag3 in", values, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3NotIn(List<Integer> values) {
            addCriterion("ext_flag3 not in", values, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3Between(Integer value1, Integer value2) {
            addCriterion("ext_flag3 between", value1, value2, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andExtFlag3NotBetween(Integer value1, Integer value2) {
            addCriterion("ext_flag3 not between", value1, value2, "extFlag3");
            return (Criteria) this;
        }

        public Criteria andPartnerIdIsNull() {
            addCriterion("partner_id is null");
            return (Criteria) this;
        }

        public Criteria andPartnerIdIsNotNull() {
            addCriterion("partner_id is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerIdEqualTo(Long value) {
            addCriterion("partner_id =", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdNotEqualTo(Long value) {
            addCriterion("partner_id <>", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdGreaterThan(Long value) {
            addCriterion("partner_id >", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("partner_id >=", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdLessThan(Long value) {
            addCriterion("partner_id <", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdLessThanOrEqualTo(Long value) {
            addCriterion("partner_id <=", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdIn(List<Long> values) {
            addCriterion("partner_id in", values, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdNotIn(List<Long> values) {
            addCriterion("partner_id not in", values, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdBetween(Long value1, Long value2) {
            addCriterion("partner_id between", value1, value2, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdNotBetween(Long value1, Long value2) {
            addCriterion("partner_id not between", value1, value2, "partnerId");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNull() {
            addCriterion("customer_type is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNotNull() {
            addCriterion("customer_type is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeEqualTo(Integer value) {
            addCriterion("customer_type =", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotEqualTo(Integer value) {
            addCriterion("customer_type <>", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThan(Integer value) {
            addCriterion("customer_type >", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_type >=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThan(Integer value) {
            addCriterion("customer_type <", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("customer_type <=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIn(List<Integer> values) {
            addCriterion("customer_type in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotIn(List<Integer> values) {
            addCriterion("customer_type not in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeBetween(Integer value1, Integer value2) {
            addCriterion("customer_type between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_type not between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andDmsKeyIsNull() {
            addCriterion("dms_key is null");
            return (Criteria) this;
        }

        public Criteria andDmsKeyIsNotNull() {
            addCriterion("dms_key is not null");
            return (Criteria) this;
        }

        public Criteria andDmsKeyEqualTo(String value) {
            addCriterion("dms_key =", value, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyNotEqualTo(String value) {
            addCriterion("dms_key <>", value, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyGreaterThan(String value) {
            addCriterion("dms_key >", value, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyGreaterThanOrEqualTo(String value) {
            addCriterion("dms_key >=", value, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyLessThan(String value) {
            addCriterion("dms_key <", value, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyLessThanOrEqualTo(String value) {
            addCriterion("dms_key <=", value, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyIn(List<String> values) {
            addCriterion("dms_key in", values, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyNotIn(List<String> values) {
            addCriterion("dms_key not in", values, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyBetween(String value1, String value2) {
            addCriterion("dms_key between", value1, value2, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andDmsKeyNotBetween(String value1, String value2) {
            addCriterion("dms_key not between", value1, value2, "dmsKey");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNull() {
            addCriterion("ext_property1 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNotNull() {
            addCriterion("ext_property1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1EqualTo(String value) {
            addCriterion("ext_property1 =", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotEqualTo(String value) {
            addCriterion("ext_property1 <>", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThan(String value) {
            addCriterion("ext_property1 >", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property1 >=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThan(String value) {
            addCriterion("ext_property1 <", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThanOrEqualTo(String value) {
            addCriterion("ext_property1 <=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1In(List<String> values) {
            addCriterion("ext_property1 in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotIn(List<String> values) {
            addCriterion("ext_property1 not in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1Between(String value1, String value2) {
            addCriterion("ext_property1 between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotBetween(String value1, String value2) {
            addCriterion("ext_property1 not between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNull() {
            addCriterion("ext_property2 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNotNull() {
            addCriterion("ext_property2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2EqualTo(String value) {
            addCriterion("ext_property2 =", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotEqualTo(String value) {
            addCriterion("ext_property2 <>", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThan(String value) {
            addCriterion("ext_property2 >", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property2 >=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThan(String value) {
            addCriterion("ext_property2 <", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThanOrEqualTo(String value) {
            addCriterion("ext_property2 <=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2In(List<String> values) {
            addCriterion("ext_property2 in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotIn(List<String> values) {
            addCriterion("ext_property2 not in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2Between(String value1, String value2) {
            addCriterion("ext_property2 between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotBetween(String value1, String value2) {
            addCriterion("ext_property2 not between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNull() {
            addCriterion("ext_property3 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNotNull() {
            addCriterion("ext_property3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3EqualTo(String value) {
            addCriterion("ext_property3 =", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotEqualTo(String value) {
            addCriterion("ext_property3 <>", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThan(String value) {
            addCriterion("ext_property3 >", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property3 >=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThan(String value) {
            addCriterion("ext_property3 <", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThanOrEqualTo(String value) {
            addCriterion("ext_property3 <=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3In(List<String> values) {
            addCriterion("ext_property3 in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotIn(List<String> values) {
            addCriterion("ext_property3 not in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3Between(String value1, String value2) {
            addCriterion("ext_property3 between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotBetween(String value1, String value2) {
            addCriterion("ext_property3 not between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNull() {
            addCriterion("ext_property4 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNotNull() {
            addCriterion("ext_property4 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4EqualTo(String value) {
            addCriterion("ext_property4 =", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotEqualTo(String value) {
            addCriterion("ext_property4 <>", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThan(String value) {
            addCriterion("ext_property4 >", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property4 >=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThan(String value) {
            addCriterion("ext_property4 <", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThanOrEqualTo(String value) {
            addCriterion("ext_property4 <=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4In(List<String> values) {
            addCriterion("ext_property4 in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotIn(List<String> values) {
            addCriterion("ext_property4 not in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4Between(String value1, String value2) {
            addCriterion("ext_property4 between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotBetween(String value1, String value2) {
            addCriterion("ext_property4 not between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty5IsNull() {
            addCriterion("ext_property5 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty5IsNotNull() {
            addCriterion("ext_property5 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty5EqualTo(String value) {
            addCriterion("ext_property5 =", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotEqualTo(String value) {
            addCriterion("ext_property5 <>", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5GreaterThan(String value) {
            addCriterion("ext_property5 >", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property5 >=", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5LessThan(String value) {
            addCriterion("ext_property5 <", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5LessThanOrEqualTo(String value) {
            addCriterion("ext_property5 <=", value, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5In(List<String> values) {
            addCriterion("ext_property5 in", values, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotIn(List<String> values) {
            addCriterion("ext_property5 not in", values, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5Between(String value1, String value2) {
            addCriterion("ext_property5 between", value1, value2, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty5NotBetween(String value1, String value2) {
            addCriterion("ext_property5 not between", value1, value2, "extProperty5");
            return (Criteria) this;
        }

        public Criteria andExtProperty6IsNull() {
            addCriterion("ext_property6 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty6IsNotNull() {
            addCriterion("ext_property6 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty6EqualTo(String value) {
            addCriterion("ext_property6 =", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6NotEqualTo(String value) {
            addCriterion("ext_property6 <>", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6GreaterThan(String value) {
            addCriterion("ext_property6 >", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property6 >=", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6LessThan(String value) {
            addCriterion("ext_property6 <", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6LessThanOrEqualTo(String value) {
            addCriterion("ext_property6 <=", value, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6In(List<String> values) {
            addCriterion("ext_property6 in", values, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6NotIn(List<String> values) {
            addCriterion("ext_property6 not in", values, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6Between(String value1, String value2) {
            addCriterion("ext_property6 between", value1, value2, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty6NotBetween(String value1, String value2) {
            addCriterion("ext_property6 not between", value1, value2, "extProperty6");
            return (Criteria) this;
        }

        public Criteria andExtProperty7IsNull() {
            addCriterion("ext_property7 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty7IsNotNull() {
            addCriterion("ext_property7 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty7EqualTo(String value) {
            addCriterion("ext_property7 =", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7NotEqualTo(String value) {
            addCriterion("ext_property7 <>", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7GreaterThan(String value) {
            addCriterion("ext_property7 >", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property7 >=", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7LessThan(String value) {
            addCriterion("ext_property7 <", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7LessThanOrEqualTo(String value) {
            addCriterion("ext_property7 <=", value, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7In(List<String> values) {
            addCriterion("ext_property7 in", values, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7NotIn(List<String> values) {
            addCriterion("ext_property7 not in", values, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7Between(String value1, String value2) {
            addCriterion("ext_property7 between", value1, value2, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty7NotBetween(String value1, String value2) {
            addCriterion("ext_property7 not between", value1, value2, "extProperty7");
            return (Criteria) this;
        }

        public Criteria andExtProperty8IsNull() {
            addCriterion("ext_property8 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty8IsNotNull() {
            addCriterion("ext_property8 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty8EqualTo(String value) {
            addCriterion("ext_property8 =", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8NotEqualTo(String value) {
            addCriterion("ext_property8 <>", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8GreaterThan(String value) {
            addCriterion("ext_property8 >", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property8 >=", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8LessThan(String value) {
            addCriterion("ext_property8 <", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8LessThanOrEqualTo(String value) {
            addCriterion("ext_property8 <=", value, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8In(List<String> values) {
            addCriterion("ext_property8 in", values, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8NotIn(List<String> values) {
            addCriterion("ext_property8 not in", values, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8Between(String value1, String value2) {
            addCriterion("ext_property8 between", value1, value2, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty8NotBetween(String value1, String value2) {
            addCriterion("ext_property8 not between", value1, value2, "extProperty8");
            return (Criteria) this;
        }

        public Criteria andExtProperty9IsNull() {
            addCriterion("ext_property9 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty9IsNotNull() {
            addCriterion("ext_property9 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty9EqualTo(String value) {
            addCriterion("ext_property9 =", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9NotEqualTo(String value) {
            addCriterion("ext_property9 <>", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9GreaterThan(String value) {
            addCriterion("ext_property9 >", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property9 >=", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9LessThan(String value) {
            addCriterion("ext_property9 <", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9LessThanOrEqualTo(String value) {
            addCriterion("ext_property9 <=", value, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9In(List<String> values) {
            addCriterion("ext_property9 in", values, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9NotIn(List<String> values) {
            addCriterion("ext_property9 not in", values, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9Between(String value1, String value2) {
            addCriterion("ext_property9 between", value1, value2, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty9NotBetween(String value1, String value2) {
            addCriterion("ext_property9 not between", value1, value2, "extProperty9");
            return (Criteria) this;
        }

        public Criteria andExtProperty10IsNull() {
            addCriterion("ext_property10 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty10IsNotNull() {
            addCriterion("ext_property10 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty10EqualTo(String value) {
            addCriterion("ext_property10 =", value, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10NotEqualTo(String value) {
            addCriterion("ext_property10 <>", value, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10GreaterThan(String value) {
            addCriterion("ext_property10 >", value, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property10 >=", value, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10LessThan(String value) {
            addCriterion("ext_property10 <", value, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10LessThanOrEqualTo(String value) {
            addCriterion("ext_property10 <=", value, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10In(List<String> values) {
            addCriterion("ext_property10 in", values, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10NotIn(List<String> values) {
            addCriterion("ext_property10 not in", values, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10Between(String value1, String value2) {
            addCriterion("ext_property10 between", value1, value2, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty10NotBetween(String value1, String value2) {
            addCriterion("ext_property10 not between", value1, value2, "extProperty10");
            return (Criteria) this;
        }

        public Criteria andExtProperty11IsNull() {
            addCriterion("ext_property11 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty11IsNotNull() {
            addCriterion("ext_property11 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty11EqualTo(String value) {
            addCriterion("ext_property11 =", value, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11NotEqualTo(String value) {
            addCriterion("ext_property11 <>", value, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11GreaterThan(String value) {
            addCriterion("ext_property11 >", value, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property11 >=", value, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11LessThan(String value) {
            addCriterion("ext_property11 <", value, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11LessThanOrEqualTo(String value) {
            addCriterion("ext_property11 <=", value, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11In(List<String> values) {
            addCriterion("ext_property11 in", values, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11NotIn(List<String> values) {
            addCriterion("ext_property11 not in", values, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11Between(String value1, String value2) {
            addCriterion("ext_property11 between", value1, value2, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty11NotBetween(String value1, String value2) {
            addCriterion("ext_property11 not between", value1, value2, "extProperty11");
            return (Criteria) this;
        }

        public Criteria andExtProperty12IsNull() {
            addCriterion("ext_property12 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty12IsNotNull() {
            addCriterion("ext_property12 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty12EqualTo(String value) {
            addCriterion("ext_property12 =", value, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12NotEqualTo(String value) {
            addCriterion("ext_property12 <>", value, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12GreaterThan(String value) {
            addCriterion("ext_property12 >", value, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property12 >=", value, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12LessThan(String value) {
            addCriterion("ext_property12 <", value, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12LessThanOrEqualTo(String value) {
            addCriterion("ext_property12 <=", value, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12In(List<String> values) {
            addCriterion("ext_property12 in", values, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12NotIn(List<String> values) {
            addCriterion("ext_property12 not in", values, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12Between(String value1, String value2) {
            addCriterion("ext_property12 between", value1, value2, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty12NotBetween(String value1, String value2) {
            addCriterion("ext_property12 not between", value1, value2, "extProperty12");
            return (Criteria) this;
        }

        public Criteria andExtProperty13IsNull() {
            addCriterion("ext_property13 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty13IsNotNull() {
            addCriterion("ext_property13 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty13EqualTo(String value) {
            addCriterion("ext_property13 =", value, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13NotEqualTo(String value) {
            addCriterion("ext_property13 <>", value, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13GreaterThan(String value) {
            addCriterion("ext_property13 >", value, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property13 >=", value, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13LessThan(String value) {
            addCriterion("ext_property13 <", value, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13LessThanOrEqualTo(String value) {
            addCriterion("ext_property13 <=", value, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13In(List<String> values) {
            addCriterion("ext_property13 in", values, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13NotIn(List<String> values) {
            addCriterion("ext_property13 not in", values, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13Between(String value1, String value2) {
            addCriterion("ext_property13 between", value1, value2, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty13NotBetween(String value1, String value2) {
            addCriterion("ext_property13 not between", value1, value2, "extProperty13");
            return (Criteria) this;
        }

        public Criteria andExtProperty14IsNull() {
            addCriterion("ext_property14 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty14IsNotNull() {
            addCriterion("ext_property14 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty14EqualTo(String value) {
            addCriterion("ext_property14 =", value, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14NotEqualTo(String value) {
            addCriterion("ext_property14 <>", value, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14GreaterThan(String value) {
            addCriterion("ext_property14 >", value, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property14 >=", value, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14LessThan(String value) {
            addCriterion("ext_property14 <", value, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14LessThanOrEqualTo(String value) {
            addCriterion("ext_property14 <=", value, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14In(List<String> values) {
            addCriterion("ext_property14 in", values, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14NotIn(List<String> values) {
            addCriterion("ext_property14 not in", values, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14Between(String value1, String value2) {
            addCriterion("ext_property14 between", value1, value2, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty14NotBetween(String value1, String value2) {
            addCriterion("ext_property14 not between", value1, value2, "extProperty14");
            return (Criteria) this;
        }

        public Criteria andExtProperty15IsNull() {
            addCriterion("ext_property15 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty15IsNotNull() {
            addCriterion("ext_property15 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty15EqualTo(String value) {
            addCriterion("ext_property15 =", value, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15NotEqualTo(String value) {
            addCriterion("ext_property15 <>", value, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15GreaterThan(String value) {
            addCriterion("ext_property15 >", value, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property15 >=", value, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15LessThan(String value) {
            addCriterion("ext_property15 <", value, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15LessThanOrEqualTo(String value) {
            addCriterion("ext_property15 <=", value, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15In(List<String> values) {
            addCriterion("ext_property15 in", values, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15NotIn(List<String> values) {
            addCriterion("ext_property15 not in", values, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15Between(String value1, String value2) {
            addCriterion("ext_property15 between", value1, value2, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty15NotBetween(String value1, String value2) {
            addCriterion("ext_property15 not between", value1, value2, "extProperty15");
            return (Criteria) this;
        }

        public Criteria andExtProperty16IsNull() {
            addCriterion("ext_property16 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty16IsNotNull() {
            addCriterion("ext_property16 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty16EqualTo(String value) {
            addCriterion("ext_property16 =", value, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16NotEqualTo(String value) {
            addCriterion("ext_property16 <>", value, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16GreaterThan(String value) {
            addCriterion("ext_property16 >", value, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property16 >=", value, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16LessThan(String value) {
            addCriterion("ext_property16 <", value, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16LessThanOrEqualTo(String value) {
            addCriterion("ext_property16 <=", value, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16In(List<String> values) {
            addCriterion("ext_property16 in", values, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16NotIn(List<String> values) {
            addCriterion("ext_property16 not in", values, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16Between(String value1, String value2) {
            addCriterion("ext_property16 between", value1, value2, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty16NotBetween(String value1, String value2) {
            addCriterion("ext_property16 not between", value1, value2, "extProperty16");
            return (Criteria) this;
        }

        public Criteria andExtProperty17IsNull() {
            addCriterion("ext_property17 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty17IsNotNull() {
            addCriterion("ext_property17 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty17EqualTo(String value) {
            addCriterion("ext_property17 =", value, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17NotEqualTo(String value) {
            addCriterion("ext_property17 <>", value, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17GreaterThan(String value) {
            addCriterion("ext_property17 >", value, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property17 >=", value, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17LessThan(String value) {
            addCriterion("ext_property17 <", value, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17LessThanOrEqualTo(String value) {
            addCriterion("ext_property17 <=", value, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17In(List<String> values) {
            addCriterion("ext_property17 in", values, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17NotIn(List<String> values) {
            addCriterion("ext_property17 not in", values, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17Between(String value1, String value2) {
            addCriterion("ext_property17 between", value1, value2, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty17NotBetween(String value1, String value2) {
            addCriterion("ext_property17 not between", value1, value2, "extProperty17");
            return (Criteria) this;
        }

        public Criteria andExtProperty18IsNull() {
            addCriterion("ext_property18 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty18IsNotNull() {
            addCriterion("ext_property18 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty18EqualTo(String value) {
            addCriterion("ext_property18 =", value, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18NotEqualTo(String value) {
            addCriterion("ext_property18 <>", value, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18GreaterThan(String value) {
            addCriterion("ext_property18 >", value, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property18 >=", value, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18LessThan(String value) {
            addCriterion("ext_property18 <", value, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18LessThanOrEqualTo(String value) {
            addCriterion("ext_property18 <=", value, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18In(List<String> values) {
            addCriterion("ext_property18 in", values, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18NotIn(List<String> values) {
            addCriterion("ext_property18 not in", values, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18Between(String value1, String value2) {
            addCriterion("ext_property18 between", value1, value2, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty18NotBetween(String value1, String value2) {
            addCriterion("ext_property18 not between", value1, value2, "extProperty18");
            return (Criteria) this;
        }

        public Criteria andExtProperty19IsNull() {
            addCriterion("ext_property19 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty19IsNotNull() {
            addCriterion("ext_property19 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty19EqualTo(String value) {
            addCriterion("ext_property19 =", value, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19NotEqualTo(String value) {
            addCriterion("ext_property19 <>", value, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19GreaterThan(String value) {
            addCriterion("ext_property19 >", value, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property19 >=", value, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19LessThan(String value) {
            addCriterion("ext_property19 <", value, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19LessThanOrEqualTo(String value) {
            addCriterion("ext_property19 <=", value, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19In(List<String> values) {
            addCriterion("ext_property19 in", values, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19NotIn(List<String> values) {
            addCriterion("ext_property19 not in", values, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19Between(String value1, String value2) {
            addCriterion("ext_property19 between", value1, value2, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty19NotBetween(String value1, String value2) {
            addCriterion("ext_property19 not between", value1, value2, "extProperty19");
            return (Criteria) this;
        }

        public Criteria andExtProperty20IsNull() {
            addCriterion("ext_property20 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty20IsNotNull() {
            addCriterion("ext_property20 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty20EqualTo(Date value) {
            addCriterion("ext_property20 =", value, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20NotEqualTo(Date value) {
            addCriterion("ext_property20 <>", value, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20GreaterThan(Date value) {
            addCriterion("ext_property20 >", value, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20GreaterThanOrEqualTo(Date value) {
            addCriterion("ext_property20 >=", value, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20LessThan(Date value) {
            addCriterion("ext_property20 <", value, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20LessThanOrEqualTo(Date value) {
            addCriterion("ext_property20 <=", value, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20In(List<Date> values) {
            addCriterion("ext_property20 in", values, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20NotIn(List<Date> values) {
            addCriterion("ext_property20 not in", values, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20Between(Date value1, Date value2) {
            addCriterion("ext_property20 between", value1, value2, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty20NotBetween(Date value1, Date value2) {
            addCriterion("ext_property20 not between", value1, value2, "extProperty20");
            return (Criteria) this;
        }

        public Criteria andExtProperty21IsNull() {
            addCriterion("ext_property21 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty21IsNotNull() {
            addCriterion("ext_property21 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty21EqualTo(String value) {
            addCriterion("ext_property21 =", value, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21NotEqualTo(String value) {
            addCriterion("ext_property21 <>", value, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21GreaterThan(String value) {
            addCriterion("ext_property21 >", value, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property21 >=", value, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21LessThan(String value) {
            addCriterion("ext_property21 <", value, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21LessThanOrEqualTo(String value) {
            addCriterion("ext_property21 <=", value, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21In(List<String> values) {
            addCriterion("ext_property21 in", values, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21NotIn(List<String> values) {
            addCriterion("ext_property21 not in", values, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21Between(String value1, String value2) {
            addCriterion("ext_property21 between", value1, value2, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty21NotBetween(String value1, String value2) {
            addCriterion("ext_property21 not between", value1, value2, "extProperty21");
            return (Criteria) this;
        }

        public Criteria andExtProperty22IsNull() {
            addCriterion("ext_property22 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty22IsNotNull() {
            addCriterion("ext_property22 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty22EqualTo(String value) {
            addCriterion("ext_property22 =", value, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22NotEqualTo(String value) {
            addCriterion("ext_property22 <>", value, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22GreaterThan(String value) {
            addCriterion("ext_property22 >", value, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property22 >=", value, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22LessThan(String value) {
            addCriterion("ext_property22 <", value, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22LessThanOrEqualTo(String value) {
            addCriterion("ext_property22 <=", value, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22In(List<String> values) {
            addCriterion("ext_property22 in", values, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22NotIn(List<String> values) {
            addCriterion("ext_property22 not in", values, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22Between(String value1, String value2) {
            addCriterion("ext_property22 between", value1, value2, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty22NotBetween(String value1, String value2) {
            addCriterion("ext_property22 not between", value1, value2, "extProperty22");
            return (Criteria) this;
        }

        public Criteria andExtProperty23IsNull() {
            addCriterion("ext_property23 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty23IsNotNull() {
            addCriterion("ext_property23 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty23EqualTo(String value) {
            addCriterion("ext_property23 =", value, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23NotEqualTo(String value) {
            addCriterion("ext_property23 <>", value, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23GreaterThan(String value) {
            addCriterion("ext_property23 >", value, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property23 >=", value, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23LessThan(String value) {
            addCriterion("ext_property23 <", value, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23LessThanOrEqualTo(String value) {
            addCriterion("ext_property23 <=", value, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23In(List<String> values) {
            addCriterion("ext_property23 in", values, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23NotIn(List<String> values) {
            addCriterion("ext_property23 not in", values, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23Between(String value1, String value2) {
            addCriterion("ext_property23 between", value1, value2, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty23NotBetween(String value1, String value2) {
            addCriterion("ext_property23 not between", value1, value2, "extProperty23");
            return (Criteria) this;
        }

        public Criteria andExtProperty24IsNull() {
            addCriterion("ext_property24 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty24IsNotNull() {
            addCriterion("ext_property24 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty24EqualTo(String value) {
            addCriterion("ext_property24 =", value, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24NotEqualTo(String value) {
            addCriterion("ext_property24 <>", value, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24GreaterThan(String value) {
            addCriterion("ext_property24 >", value, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property24 >=", value, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24LessThan(String value) {
            addCriterion("ext_property24 <", value, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24LessThanOrEqualTo(String value) {
            addCriterion("ext_property24 <=", value, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24In(List<String> values) {
            addCriterion("ext_property24 in", values, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24NotIn(List<String> values) {
            addCriterion("ext_property24 not in", values, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24Between(String value1, String value2) {
            addCriterion("ext_property24 between", value1, value2, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty24NotBetween(String value1, String value2) {
            addCriterion("ext_property24 not between", value1, value2, "extProperty24");
            return (Criteria) this;
        }

        public Criteria andExtProperty25IsNull() {
            addCriterion("ext_property25 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty25IsNotNull() {
            addCriterion("ext_property25 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty25EqualTo(String value) {
            addCriterion("ext_property25 =", value, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25NotEqualTo(String value) {
            addCriterion("ext_property25 <>", value, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25GreaterThan(String value) {
            addCriterion("ext_property25 >", value, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property25 >=", value, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25LessThan(String value) {
            addCriterion("ext_property25 <", value, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25LessThanOrEqualTo(String value) {
            addCriterion("ext_property25 <=", value, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25In(List<String> values) {
            addCriterion("ext_property25 in", values, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25NotIn(List<String> values) {
            addCriterion("ext_property25 not in", values, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25Between(String value1, String value2) {
            addCriterion("ext_property25 between", value1, value2, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty25NotBetween(String value1, String value2) {
            addCriterion("ext_property25 not between", value1, value2, "extProperty25");
            return (Criteria) this;
        }

        public Criteria andExtProperty26IsNull() {
            addCriterion("ext_property26 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty26IsNotNull() {
            addCriterion("ext_property26 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty26EqualTo(String value) {
            addCriterion("ext_property26 =", value, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26NotEqualTo(String value) {
            addCriterion("ext_property26 <>", value, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26GreaterThan(String value) {
            addCriterion("ext_property26 >", value, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property26 >=", value, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26LessThan(String value) {
            addCriterion("ext_property26 <", value, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26LessThanOrEqualTo(String value) {
            addCriterion("ext_property26 <=", value, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26In(List<String> values) {
            addCriterion("ext_property26 in", values, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26NotIn(List<String> values) {
            addCriterion("ext_property26 not in", values, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26Between(String value1, String value2) {
            addCriterion("ext_property26 between", value1, value2, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty26NotBetween(String value1, String value2) {
            addCriterion("ext_property26 not between", value1, value2, "extProperty26");
            return (Criteria) this;
        }

        public Criteria andExtProperty27IsNull() {
            addCriterion("ext_property27 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty27IsNotNull() {
            addCriterion("ext_property27 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty27EqualTo(String value) {
            addCriterion("ext_property27 =", value, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27NotEqualTo(String value) {
            addCriterion("ext_property27 <>", value, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27GreaterThan(String value) {
            addCriterion("ext_property27 >", value, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property27 >=", value, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27LessThan(String value) {
            addCriterion("ext_property27 <", value, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27LessThanOrEqualTo(String value) {
            addCriterion("ext_property27 <=", value, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27In(List<String> values) {
            addCriterion("ext_property27 in", values, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27NotIn(List<String> values) {
            addCriterion("ext_property27 not in", values, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27Between(String value1, String value2) {
            addCriterion("ext_property27 between", value1, value2, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty27NotBetween(String value1, String value2) {
            addCriterion("ext_property27 not between", value1, value2, "extProperty27");
            return (Criteria) this;
        }

        public Criteria andExtProperty28IsNull() {
            addCriterion("ext_property28 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty28IsNotNull() {
            addCriterion("ext_property28 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty28EqualTo(String value) {
            addCriterion("ext_property28 =", value, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28NotEqualTo(String value) {
            addCriterion("ext_property28 <>", value, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28GreaterThan(String value) {
            addCriterion("ext_property28 >", value, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property28 >=", value, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28LessThan(String value) {
            addCriterion("ext_property28 <", value, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28LessThanOrEqualTo(String value) {
            addCriterion("ext_property28 <=", value, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28In(List<String> values) {
            addCriterion("ext_property28 in", values, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28NotIn(List<String> values) {
            addCriterion("ext_property28 not in", values, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28Between(String value1, String value2) {
            addCriterion("ext_property28 between", value1, value2, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty28NotBetween(String value1, String value2) {
            addCriterion("ext_property28 not between", value1, value2, "extProperty28");
            return (Criteria) this;
        }

        public Criteria andExtProperty29IsNull() {
            addCriterion("ext_property29 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty29IsNotNull() {
            addCriterion("ext_property29 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty29EqualTo(String value) {
            addCriterion("ext_property29 =", value, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29NotEqualTo(String value) {
            addCriterion("ext_property29 <>", value, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29GreaterThan(String value) {
            addCriterion("ext_property29 >", value, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property29 >=", value, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29LessThan(String value) {
            addCriterion("ext_property29 <", value, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29LessThanOrEqualTo(String value) {
            addCriterion("ext_property29 <=", value, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29In(List<String> values) {
            addCriterion("ext_property29 in", values, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29NotIn(List<String> values) {
            addCriterion("ext_property29 not in", values, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29Between(String value1, String value2) {
            addCriterion("ext_property29 between", value1, value2, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andExtProperty29NotBetween(String value1, String value2) {
            addCriterion("ext_property29 not between", value1, value2, "extProperty29");
            return (Criteria) this;
        }

        public Criteria andJoinCkPlanEqualTo(Integer value) {
            addCriterion("join_ck_plan =", value, "joinCkPlan");
            return (Criteria) this;
        }

    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
