--select * 
update c set c.check_code='CHEN_LIE_PHOTO'
from wx_task_check c where check_name like '%陈列照：(要求陈列产品和陈列架周边环境清晰：1.包含雪佛龙产品。2.产品陈列在陈列架上。3.包含陈列架周围的环境。)%'
--select * from wx_task_check c where check_code='SALE_MAJOR_OIL'

update c set c.check_code='SALE_MAJOR_OIL'
from wx_task_check c where check_name like '%主要销售/使用产品%'
update c set c.check_code='CHEVRON_OIL_MONTH_LITERS'
from wx_task_check c where check_name like '%雪佛龙润滑油每月总销量（升%'

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('Task.NewCustomer.potentialCustomerSpecialItem', '潜在新客户录入特殊处理字段配置', '潜在新客户录入特殊处理字段配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Task.NewCustomer.potentialCustomerSpecialItem', 'CHEN_LIE_PHOTO', '陈列照', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Task.NewCustomer.potentialCustomerSpecialItem', 'SALE_MAJOR_OIL', '主要销售/使用产品', '', '1', 20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Task.NewCustomer.potentialCustomerSpecialItem', 'CHEVRON_OIL_MONTH_LITERS', '雪佛龙润滑油每月总销量(升)', '', '1', 20);


INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'雪佛龙门店'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'extProperty25'
           ,'options'
           ,'{"showType": "radio", "multiSelect": false, "datasourceType": "data", "data":[{"value": "是"}, {"value": "否"}], "textField": "value", "valueField": "value"}');
