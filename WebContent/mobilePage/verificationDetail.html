<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>技师扫描详细</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">

    <link rel="stylesheet" href="css/mui.min.css">
    <link rel="stylesheet" type="text/css" href="css/mui.picker.min.css"/>
    <link rel="stylesheet" href="css/verification.css">
    <link rel="stylesheet" href="css/loading.css">
    <script src="js/jquery-1.12.2.min.js"></script>
    <script language="javascript" type="text/javascript" src="js/jquery.json-2.4.min.js"></script>
    <script language="javascript" type="text/javascript" src="js/jquery.jsonrpcclient.js"></script>
    <script type="text/javascript" src="js/mui.min.js"></script>
    <script type="text/javascript" src="js/mui.picker.min.js"></script>
    <script src="js/loading.js"></script>
    <script src="js/verificationDetail.js"></script>
    <style>
        #productTable {
            width: 100%;
        }

        #productTable th {
            text-align: center;
            border-bottom: 2px solid #aaaaaa;
        }

        #productTable td {
            text-align: center;
            font-size: 1em;
            border-bottom: 1px solid #fafafa;
        }

        #productTable td p {
            line-height: 32px;
            margin-bottom: 0px;
        }
    </style>
</head>
<body>
<header class="mui-bar mui-bar-nav">
    <h1 class="mui-title">技师扫描详细</h1>
</header>
<div class="mui-content">
    <div class="mui-content-padded">
        <p>
            <span id="currMechanic" class="time mui-text-center">当前技师:全部技师</span>
            <span>&nbsp;</span>
            <a href="#picture" class="btn mui-btn"
               style="padding: 5px 20px;">
                选择技师
            </a>
        </p>
        <button id='startTime' data-options='{"type":"date"}' class="time-btn btn mui-btn">选择起始日期</button>
        <span class="time mui-text-center">至</span>
        <button id='endTime' data-options='{"type":"date"}' class="time-btn btn mui-btn">选择截止日期</button>
        <button type="button" class="custom-button mui-btn mui-pull-right" onclick="query()">查询</button>
    </div>
    <div class="mui-card">
        <table id="productTable">
            <tr>
                <th><h4>日期</h4></th>
                <th><h4>技师</h4></th>
                <th><h4>升数</h4></th>
            </tr>
        </table>
    </div>
    <div id="picture" class="mui-popover mui-popover-action mui-popover-bottom">
        <ul class="mui-table-view" id="mechanicList">
            <li class="mui-table-view-cell">
                <a mCode="-1" href="#">全部技师</a>
            </li>
        </ul>
        <ul class="mui-table-view">
            <li class="mui-table-view-cell">
                <a mCode="-99" href="#picture"><b>取消</b></a>
            </li>
        </ul>
    </div>
    <script>
        var mechanicCode = "-1";
        (function ($) {
            $.init();
            var btns = $('.time-btn');
            btns.each(function (i, btn) {
                btn.addEventListener('tap', function () {
                    var optionsJson = this.getAttribute('data-options') || '{}';
                    var options = JSON.parse(optionsJson);
                    var picker = new $.DtPicker(options);
                    picker.show(function (rs) {
                        btn.innerHTML = rs.text;
                        picker.dispose();
                    });
                }, false);
            });
        })(mui);
        mui('body').on('tap', '.mui-popover-action li>a', function () {
            var a = this, parent;
            //根据点击按钮，反推当前是哪个actionsheet
            for (parent = a.parentNode; parent != document.body; parent = parent.parentNode) {
                if (parent.classList.contains('mui-popover-action')) {
                    break;
                }
            }
            //关闭actionsheet
            mui('#' + parent.id).popover('toggle');
            var code = a.getAttribute('mCode');
            if (code != "-99") {
                mechanicCode = code;
                $('#currMechanic').html("当前技师:" + a.innerHTML);
            }
        })
    </script>
</div>
</body>
</html>