/*==============================================================*/
/* Module: PP                                                   */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_statistics_cond_config                           */
/*==============================================================*/
create table wx_t_statistics_cond_config (
	id                               bigint                            not null identity,
	statistics_type                  nvarchar(64)                               not null,
	effective_from_date              date                                       not null,
	effective_to_date                date                                       not null,
	config_desc                      nvarchar(512)                                  null,
	enable_flag                      tinyint                                        null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sta_cond_con_sta_type ON wx_t_statistics_cond_config (statistics_type);

/*==============================================================*/
/* Table: wx_t_sta_cond_con_value                               */
/*==============================================================*/
create table wx_t_sta_cond_con_value (
	item_value                       nvarchar(64)                               not null,
	config_id                        bigint                                     not null,
	primary key (item_value, config_id)
);

/*==============================================================*/
/* Table: wx_message                                            */
/*==============================================================*/
create table wx_message (
	id                               bigint                                     not null,
	effective_from_date              date                                           null,
	effective_to_date                date                                           null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_product                                          */
/*==============================================================*/
create table wx_t_product (
	id                               bigint                            not null identity,
	sku                              nvarchar(64)                               not null,
	name                             nvarchar(256)                              not null,
	name_en                          nvarchar(256)                                  null,
	sale_price                       decimal(20,6)                                  null,
	pack                             decimal(20,6)                                  null,
	size                             nvarchar(128)                                  null,
	pack_type                        nvarchar(128)                                  null,
	pack_code                        nvarchar(64)                                   null,
	product_channel                  nvarchar(256)                                  null,
	grade_abc                        nvarchar(64)                                   null,
	t1_category                      nvarchar(64)                                   null,
	t2_category                      nvarchar(64)                                   null,
	t3_category                      nvarchar(64)                                   null,
	digit_product_code               nvarchar(64)                                   null,
	inner_brand                      nvarchar(64)                                   null,
	product_sector                   nvarchar(64)                                   null,
	hydraulic_ago_ci                 nvarchar(256)                                  null,
	hyd_tra_ago_ind_com              nvarchar(256)                                  null,
	can_verify                       tinyint                                    not null,
	capacity                         nvarchar(20)                                   null,
	viscosity                        nvarchar(20)                                   null,
	oil_type                         nvarchar(30)                                   null,
	remark                           nvarchar(64)                                   null,
	status                           tinyint                                    not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	creator                          nvarchar(128)                                  null,
	support_order                    tinyint                                        null,
	product_property                 nvarchar(64)                                   null,
	primary key (id)
);
ALTER TABLE wx_t_product ADD CONSTRAINT uk_product UNIQUE (sku);
CREATE INDEX ix_product_product_channel ON wx_t_product (product_channel);
CREATE INDEX ix_product_grade_abc ON wx_t_product (grade_abc);
CREATE INDEX ix_product_t1_category ON wx_t_product (t1_category);
CREATE INDEX ix_product_t2_category ON wx_t_product (t2_category);
CREATE INDEX ix_product_t3_category ON wx_t_product (t3_category);
CREATE INDEX ix_product_inner_brand ON wx_t_product (inner_brand);

/*==============================================================*/
/* Table: wx_t_user                                             */
/*==============================================================*/
create table wx_t_user (
	user_id                          bigint                                     not null,
	cai                              nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (user_id)
);
CREATE INDEX ix_user_cai ON wx_t_user (cai);

/*==============================================================*/
/* Table: wx_t_business_region_config                           */
/*==============================================================*/
create table wx_t_business_region_config (
	id                               bigint                            not null identity,
	business_name                    nvarchar(256)                              not null,
	sales_channel                    nvarchar(256)                                  null,
	region_name                      nvarchar(256)                              not null,
	region_alias                     nvarchar(256)                                  null,
	enable_flag                      tinyint                                        null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_bus_reg_con_bus_name ON wx_t_business_region_config (business_name);

/*==============================================================*/
/* Table: wx_t_partner_responsible_main                         */
/*==============================================================*/
create table wx_t_partner_responsible_main (
	id                               bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	include_actual_rel               tinyint                                    not null,
	sales_cai                        nvarchar(64)                                   null,
	config_type                      tinyint                                    not null,
	operator_type                    nvarchar(64)                                   null,
	cc_to_customer                   tinyint                                    not null,
	cc_to_sales                      tinyint                                    not null,
	cc_to_suppervisor                tinyint                                    not null,
	cc_to_channel_manager            tinyint                                    not null,
	cc_to_bu_manager                 tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_common_menu                                      */
/*==============================================================*/
create table wx_t_common_menu (
	id                               bigint                    not null identity(1000,1),
	module_id                        bigint                                     not null,
	menu_id                          bigint                                     not null,
	menu_name_alias                  nvarchar(64)                                   null,
	fun_icon                         nvarchar(128)                                  null,
	order_numb                       decimal(20,8)                                  null,
	fun_desc                         nvarchar(256)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_common_menu_module                               */
/*==============================================================*/
create table wx_t_common_menu_module (
	id                               bigint                     not null identity(100,1),
	module_name                      nvarchar(256)                                  null,
	module_banner                    nvarchar(256)                                  null,
	module_theme                     nvarchar(256)                                  null,
	order_numb                       decimal(20,8)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_role_common_menu                                 */
/*==============================================================*/
create table wx_t_role_common_menu (
	role_id                          bigint                                     not null,
	common_menu_id                   bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (role_id, common_menu_id)
);

/*==============================================================*/
/* Table: wx_t_oil_ver_award_det                                */
/*==============================================================*/
create table wx_t_oil_ver_award_det (
	id                               bigint                                     not null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	reach_standard_flag              int                                            null,
	review_flag                      tinyint                                        null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_role                                             */
/*==============================================================*/
create table wx_t_role (
	role_id                          bigint                                     not null,
	sales_channel                    nvarchar(256)                                  null,
	primary key (role_id)
);

/*==============================================================*/
/* Table: wx_t_interface_data                                   */
/*==============================================================*/
create table wx_t_interface_data (
	id                               bigint                                     not null,
	url                              nvarchar(256)                                  null,
	status                           tinyint                                    not null,
	remarks                          nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_interface_data_detail                            */
/*==============================================================*/
create table wx_t_interface_data_detail (
	property_name                    nvarchar(256)                              not null,
	interface_data_id                bigint                                     not null,
	property_value                   nvarchar(1000)                                 null,
	value_processor                  nvarchar(64)                                   null,
	primary key (property_name, interface_data_id)
);

/*==============================================================*/
/* Table: wx_t_operation_permission                             */
/*==============================================================*/
create table wx_t_operation_permission (
	id                               bigint                            not null identity,
	role_id                          bigint                                     not null,
	module_code                      nvarchar(128)                              not null,
	permission_weight                int                                        not null,
	enable_flag                      tinyint                                        null,
	remark                           nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_ope_per_role_id ON wx_t_operation_permission (role_id);
CREATE INDEX ix_ope_per_mod_code ON wx_t_operation_permission (module_code);

/*==============================================================*/
/* Table: wx_t_tace_detail                                      */
/*==============================================================*/
create table wx_t_tace_detail (
	trace_no                         nvarchar(64)                               not null,
	sku                              nvarchar(64)                               not null,
	primary key (trace_no)
);

/*==============================================================*/
/* Table: wx_t_sale_for_plant_all                               */
/*==============================================================*/
create table wx_t_sale_for_plant_all (
	id                               bigint                            not null identity,
	customer_code                    nvarchar(64)                                   null,
	customer_name_cn                 nvarchar(256)                                  null,
	customer_name_en                 nvarchar(256)                                  null,
	plant_code                       nvarchar(30)                               not null,
	allocate_rate                    decimal(16,15)                                 null,
	effective_from_date              date                                           null,
	effective_to_date                date                                           null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sal_for_pla_all_cus_nam_en ON wx_t_sale_for_plant_all (customer_name_en);

/*==============================================================*/
/* Table: wx_t_forecasting_worksheet                            */
/*==============================================================*/
create table wx_t_forecasting_worksheet (
	id                               bigint                            not null identity,
	worksheet_title                  nvarchar(256)                              not null,
	sales_start_date                 date                                           null,
	sales_end_date                   date                                           null,
	supervisor_start_date            date                                           null,
	supervisor_end_date              date                                           null,
	status                           tinyint                                    not null,
	sales_channel                    nvarchar(256)                                  null,
	forecasting_month                date                                           null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	delete_flag                      tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_forecasting_approve                              */
/*==============================================================*/
create table wx_t_forecasting_approve (
	id                               bigint                            not null identity,
	worksheet_id                     bigint                                     not null,
	approver                         bigint                                         null,
	status                           smallint                                   not null,
	submit_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	record_version                   bigint                                     not null,
	temp_save_finished               tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_forecasting_approve ADD CONSTRAINT uk_forecasting_approve UNIQUE (worksheet_id,approver);

/*==============================================================*/
/* Table: wx_t_forecasting_sales                                */
/*==============================================================*/
create table wx_t_forecasting_sales (
	id                               bigint                            not null identity,
	worksheet_id                     bigint                                     not null,
	approve_id                       bigint                                         null,
	sales                            bigint                                         null,
	status                           smallint                                   not null,
	submit_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	record_version                   bigint                                     not null,
	temp_save_finished               tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_forecasting_sales ADD CONSTRAINT uk_forecasting_sales UNIQUE (worksheet_id,sales);

/*==============================================================*/
/* Table: wx_t_forecasting_detail                               */
/*==============================================================*/
create table wx_t_forecasting_detail (
	id                               bigint                            not null identity,
	forecasting_id                   bigint                                         null,
	sku                              nvarchar(64)                               not null,
	customer_name_cn                 nvarchar(256)                                  null,
	customer_name_en                 nvarchar(256)                                  null,
	forecasting_value                decimal(20,6)                                  null,
	fixed_value                      decimal(20,6)                                  null,
	avg_12                           decimal(20,6)                                  null,
	value_last_year                  decimal(20,6)                                  null,
	month_offset                     tinyint                                    not null,
	forecast_month                   nvarchar(6)                                    null,
	status                           bigint                                     not null,
	def_forecasting_value            decimal(20,6)                                  null,
	forecast_leading_time            int                                            null,
	primary key (id)
);
CREATE INDEX ix_forecasting_detail_sku ON wx_t_forecasting_detail (sku);
CREATE INDEX ix_for_det_cus_name_cn ON wx_t_forecasting_detail (customer_name_cn);
CREATE INDEX ix_for_det_for_month ON wx_t_forecasting_detail (forecast_month);

/*==============================================================*/
/* Table: wx_t_qr_code_detail                                   */
/*==============================================================*/
create table wx_t_qr_code_detail (
	code_id                          bigint                                     not null,
	qr_owner                         nvarchar(30)                                   null,
	primary key (code_id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_st_co_co_va_st_co_co             */
/*==============================================================*/
CREATE INDEX ix_st_co_co_va_st_co_co ON wx_t_sta_cond_con_value (config_id);

/*==============================================================*/
/* Foreign Key(only index): ix_com_menu_com_menu_mod            */
/*==============================================================*/
CREATE INDEX ix_com_menu_com_menu_mod ON wx_t_common_menu (module_id);

/*==============================================================*/
/* Foreign Key(only index): ix_role_com_menu_com_menu           */
/*==============================================================*/
CREATE INDEX ix_role_com_menu_com_menu ON wx_t_role_common_menu (common_menu_id);

/*==============================================================*/
/* Foreign Key(only index): ix_int_data_det_int_data            */
/*==============================================================*/
CREATE INDEX ix_int_data_det_int_data ON wx_t_interface_data_detail (interface_data_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_app_for_wor                  */
/*==============================================================*/
CREATE INDEX ix_for_app_for_wor ON wx_t_forecasting_approve (worksheet_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_sales_for_wor                */
/*==============================================================*/
CREATE INDEX ix_for_sales_for_wor ON wx_t_forecasting_sales (worksheet_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_sales_for_app                */
/*==============================================================*/
CREATE INDEX ix_for_sales_for_app ON wx_t_forecasting_sales (approve_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_det_for_sales                */
/*==============================================================*/
CREATE INDEX ix_for_det_for_sales ON wx_t_forecasting_detail (forecasting_id);
/*==============================================================*/
/* Module: PP                                                   */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_statistics_cond_config                           */
/*==============================================================*/
create table wx_t_statistics_cond_config (
	id                               bigint                            not null identity,
	statistics_type                  nvarchar(64)                               not null,
	effective_from_date              date                                       not null,
	effective_to_date                date                                       not null,
	config_desc                      nvarchar(512)                                  null,
	enable_flag                      tinyint                                        null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sta_cond_con_sta_type ON wx_t_statistics_cond_config (statistics_type);

/*==============================================================*/
/* Table: wx_t_sta_cond_con_value                               */
/*==============================================================*/
create table wx_t_sta_cond_con_value (
	item_value                       nvarchar(64)                               not null,
	config_id                        bigint                                     not null,
	primary key (item_value, config_id)
);

/*==============================================================*/
/* Table: wx_message                                            */
/*==============================================================*/
create table wx_message (
	id                               bigint                                     not null,
	effective_from_date              date                                           null,
	effective_to_date                date                                           null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_product                                          */
/*==============================================================*/
create table wx_t_product (
	id                               bigint                            not null identity,
	sku                              nvarchar(64)                               not null,
	name                             nvarchar(256)                              not null,
	name_en                          nvarchar(256)                                  null,
	sale_price                       decimal(20,6)                                  null,
	pack                             decimal(20,6)                                  null,
	size                             nvarchar(128)                                  null,
	pack_type                        nvarchar(128)                                  null,
	pack_code                        nvarchar(64)                                   null,
	product_channel                  nvarchar(256)                                  null,
	grade_abc                        nvarchar(64)                                   null,
	t1_category                      nvarchar(64)                                   null,
	t2_category                      nvarchar(64)                                   null,
	t3_category                      nvarchar(64)                                   null,
	digit_product_code               nvarchar(64)                                   null,
	inner_brand                      nvarchar(64)                                   null,
	product_sector                   nvarchar(64)                                   null,
	hydraulic_ago_ci                 nvarchar(256)                                  null,
	hyd_tra_ago_ind_com              nvarchar(256)                                  null,
	can_verify                       tinyint                                    not null,
	capacity                         nvarchar(20)                                   null,
	viscosity                        nvarchar(20)                                   null,
	oil_type                         nvarchar(30)                                   null,
	remark                           nvarchar(64)                                   null,
	status                           tinyint                                    not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	creator                          nvarchar(128)                                  null,
	support_order                    tinyint                                        null,
	product_property                 nvarchar(64)                                   null,
	primary key (id)
);
ALTER TABLE wx_t_product ADD CONSTRAINT uk_product UNIQUE (sku);
CREATE INDEX ix_product_product_channel ON wx_t_product (product_channel);
CREATE INDEX ix_product_grade_abc ON wx_t_product (grade_abc);
CREATE INDEX ix_product_t1_category ON wx_t_product (t1_category);
CREATE INDEX ix_product_t2_category ON wx_t_product (t2_category);
CREATE INDEX ix_product_t3_category ON wx_t_product (t3_category);
CREATE INDEX ix_product_inner_brand ON wx_t_product (inner_brand);

/*==============================================================*/
/* Table: wx_t_user                                             */
/*==============================================================*/
create table wx_t_user (
	user_id                          bigint                                     not null,
	cai                              nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (user_id)
);
CREATE INDEX ix_user_cai ON wx_t_user (cai);

/*==============================================================*/
/* Table: wx_t_business_region_config                           */
/*==============================================================*/
create table wx_t_business_region_config (
	id                               bigint                            not null identity,
	business_name                    nvarchar(256)                              not null,
	sales_channel                    nvarchar(256)                                  null,
	region_name                      nvarchar(256)                              not null,
	region_alias                     nvarchar(256)                                  null,
	enable_flag                      tinyint                                        null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_bus_reg_con_bus_name ON wx_t_business_region_config (business_name);

/*==============================================================*/
/* Table: wx_t_partner_responsible_main                         */
/*==============================================================*/
create table wx_t_partner_responsible_main (
	id                               bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	include_actual_rel               tinyint                                    not null,
	sales_cai                        nvarchar(64)                                   null,
	config_type                      tinyint                                    not null,
	operator_type                    nvarchar(64)                                   null,
	cc_to_customer                   tinyint                                    not null,
	cc_to_sales                      tinyint                                    not null,
	cc_to_suppervisor                tinyint                                    not null,
	cc_to_channel_manager            tinyint                                    not null,
	cc_to_bu_manager                 tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_common_menu                                      */
/*==============================================================*/
create table wx_t_common_menu (
	id                               bigint                    not null identity(1000,1),
	module_id                        bigint                                     not null,
	menu_id                          bigint                                     not null,
	menu_name_alias                  nvarchar(64)                                   null,
	fun_icon                         nvarchar(128)                                  null,
	order_numb                       decimal(20,8)                                  null,
	fun_desc                         nvarchar(256)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_common_menu_module                               */
/*==============================================================*/
create table wx_t_common_menu_module (
	id                               bigint                     not null identity(100,1),
	module_name                      nvarchar(256)                                  null,
	module_banner                    nvarchar(256)                                  null,
	module_theme                     nvarchar(256)                                  null,
	order_numb                       decimal(20,8)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_role_common_menu                                 */
/*==============================================================*/
create table wx_t_role_common_menu (
	role_id                          bigint                                     not null,
	common_menu_id                   bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (role_id, common_menu_id)
);

/*==============================================================*/
/* Table: wx_t_oil_ver_award_det                                */
/*==============================================================*/
create table wx_t_oil_ver_award_det (
	id                               bigint                                     not null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	reach_standard_flag              int                                            null,
	review_flag                      tinyint                                        null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_role                                             */
/*==============================================================*/
create table wx_t_role (
	role_id                          bigint                                     not null,
	sales_channel                    nvarchar(256)                                  null,
	primary key (role_id)
);

/*==============================================================*/
/* Table: wx_t_interface_data                                   */
/*==============================================================*/
create table wx_t_interface_data (
	id                               bigint                                     not null,
	url                              nvarchar(256)                                  null,
	status                           tinyint                                    not null,
	remarks                          nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_interface_data_detail                            */
/*==============================================================*/
create table wx_t_interface_data_detail (
	property_name                    nvarchar(256)                              not null,
	interface_data_id                bigint                                     not null,
	property_value                   nvarchar(1000)                                 null,
	value_processor                  nvarchar(64)                                   null,
	primary key (property_name, interface_data_id)
);

/*==============================================================*/
/* Table: wx_t_operation_permission                             */
/*==============================================================*/
create table wx_t_operation_permission (
	id                               bigint                            not null identity,
	role_id                          bigint                                     not null,
	module_code                      nvarchar(128)                              not null,
	permission_weight                int                                        not null,
	enable_flag                      tinyint                                        null,
	remark                           nvarchar(64)                                   null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_ope_per_role_id ON wx_t_operation_permission (role_id);
CREATE INDEX ix_ope_per_mod_code ON wx_t_operation_permission (module_code);

/*==============================================================*/
/* Table: wx_t_tace_detail                                      */
/*==============================================================*/
create table wx_t_tace_detail (
	trace_no                         nvarchar(64)                               not null,
	sku                              nvarchar(64)                               not null,
	primary key (trace_no)
);

/*==============================================================*/
/* Table: wx_t_sale_for_plant_all                               */
/*==============================================================*/
create table wx_t_sale_for_plant_all (
	id                               bigint                            not null identity,
	customer_code                    nvarchar(64)                                   null,
	customer_name_cn                 nvarchar(256)                                  null,
	customer_name_en                 nvarchar(256)                                  null,
	plant_code                       nvarchar(30)                               not null,
	allocate_rate                    decimal(16,15)                                 null,
	effective_from_date              date                                           null,
	effective_to_date                date                                           null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_sal_for_pla_all_cus_nam_en ON wx_t_sale_for_plant_all (customer_name_en);

/*==============================================================*/
/* Table: wx_t_forecasting_worksheet                            */
/*==============================================================*/
create table wx_t_forecasting_worksheet (
	id                               bigint                            not null identity,
	worksheet_title                  nvarchar(256)                              not null,
	sales_start_date                 date                                           null,
	sales_end_date                   date                                           null,
	supervisor_start_date            date                                           null,
	supervisor_end_date              date                                           null,
	status                           tinyint                                    not null,
	sales_channel                    nvarchar(256)                                  null,
	forecasting_month                date                                           null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	delete_flag                      tinyint                                    not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_forecasting_approve                              */
/*==============================================================*/
create table wx_t_forecasting_approve (
	id                               bigint                            not null identity,
	worksheet_id                     bigint                                     not null,
	approver                         bigint                                         null,
	status                           smallint                                   not null,
	submit_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	record_version                   bigint                                     not null,
	temp_save_finished               tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_forecasting_approve ADD CONSTRAINT uk_forecasting_approve UNIQUE (worksheet_id,approver);

/*==============================================================*/
/* Table: wx_t_forecasting_sales                                */
/*==============================================================*/
create table wx_t_forecasting_sales (
	id                               bigint                            not null identity,
	worksheet_id                     bigint                                     not null,
	approve_id                       bigint                                         null,
	sales                            bigint                                         null,
	status                           smallint                                   not null,
	submit_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	record_version                   bigint                                     not null,
	temp_save_finished               tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_forecasting_sales ADD CONSTRAINT uk_forecasting_sales UNIQUE (worksheet_id,sales);

/*==============================================================*/
/* Table: wx_t_forecasting_detail                               */
/*==============================================================*/
create table wx_t_forecasting_detail (
	id                               bigint                            not null identity,
	forecasting_id                   bigint                                         null,
	sku                              nvarchar(64)                               not null,
	customer_name_cn                 nvarchar(256)                                  null,
	customer_name_en                 nvarchar(256)                                  null,
	forecasting_value                decimal(20,6)                                  null,
	fixed_value                      decimal(20,6)                                  null,
	avg_12                           decimal(20,6)                                  null,
	value_last_year                  decimal(20,6)                                  null,
	month_offset                     tinyint                                    not null,
	forecast_month                   nvarchar(6)                                    null,
	status                           bigint                                     not null,
	def_forecasting_value            decimal(20,6)                                  null,
	forecast_leading_time            int                                            null,
	primary key (id)
);
CREATE INDEX ix_forecasting_detail_sku ON wx_t_forecasting_detail (sku);
CREATE INDEX ix_for_det_cus_name_cn ON wx_t_forecasting_detail (customer_name_cn);
CREATE INDEX ix_for_det_for_month ON wx_t_forecasting_detail (forecast_month);

/*==============================================================*/
/* Table: wx_t_qr_code_detail                                   */
/*==============================================================*/
create table wx_t_qr_code_detail (
	code_id                          bigint                                     not null,
	qr_owner                         nvarchar(30)                                   null,
	primary key (code_id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_st_co_co_va_st_co_co             */
/*==============================================================*/
CREATE INDEX ix_st_co_co_va_st_co_co ON wx_t_sta_cond_con_value (config_id);

/*==============================================================*/
/* Foreign Key(only index): ix_com_menu_com_menu_mod            */
/*==============================================================*/
CREATE INDEX ix_com_menu_com_menu_mod ON wx_t_common_menu (module_id);

/*==============================================================*/
/* Foreign Key(only index): ix_role_com_menu_com_menu           */
/*==============================================================*/
CREATE INDEX ix_role_com_menu_com_menu ON wx_t_role_common_menu (common_menu_id);

/*==============================================================*/
/* Foreign Key(only index): ix_int_data_det_int_data            */
/*==============================================================*/
CREATE INDEX ix_int_data_det_int_data ON wx_t_interface_data_detail (interface_data_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_app_for_wor                  */
/*==============================================================*/
CREATE INDEX ix_for_app_for_wor ON wx_t_forecasting_approve (worksheet_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_sales_for_wor                */
/*==============================================================*/
CREATE INDEX ix_for_sales_for_wor ON wx_t_forecasting_sales (worksheet_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_sales_for_app                */
/*==============================================================*/
CREATE INDEX ix_for_sales_for_app ON wx_t_forecasting_sales (approve_id);

/*==============================================================*/
/* Foreign Key(only index): ix_for_det_for_sales                */
/*==============================================================*/
CREATE INDEX ix_for_det_for_sales ON wx_t_forecasting_detail (forecasting_id);
