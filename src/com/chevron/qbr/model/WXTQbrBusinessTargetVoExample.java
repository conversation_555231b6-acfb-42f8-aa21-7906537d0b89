package com.chevron.qbr.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WXTQbrBusinessTargetVoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WXTQbrBusinessTargetVoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPartnerIdIsNull() {
            addCriterion("partner_id is null");
            return (Criteria) this;
        }

        public Criteria andPartnerIdIsNotNull() {
            addCriterion("partner_id is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerIdEqualTo(Long value) {
            addCriterion("partner_id =", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdNotEqualTo(Long value) {
            addCriterion("partner_id <>", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdGreaterThan(Long value) {
            addCriterion("partner_id >", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("partner_id >=", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdLessThan(Long value) {
            addCriterion("partner_id <", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdLessThanOrEqualTo(Long value) {
            addCriterion("partner_id <=", value, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdIn(List<Long> values) {
            addCriterion("partner_id in", values, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdNotIn(List<Long> values) {
            addCriterion("partner_id not in", values, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdBetween(Long value1, Long value2) {
            addCriterion("partner_id between", value1, value2, "partnerId");
            return (Criteria) this;
        }

        public Criteria andPartnerIdNotBetween(Long value1, Long value2) {
            addCriterion("partner_id not between", value1, value2, "partnerId");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIsNull() {
            addCriterion("sales_channel is null");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIsNotNull() {
            addCriterion("sales_channel is not null");
            return (Criteria) this;
        }

        public Criteria andSalesChannelEqualTo(String value) {
            addCriterion("sales_channel =", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotEqualTo(String value) {
            addCriterion("sales_channel <>", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelGreaterThan(String value) {
            addCriterion("sales_channel >", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelGreaterThanOrEqualTo(String value) {
            addCriterion("sales_channel >=", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLessThan(String value) {
            addCriterion("sales_channel <", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLessThanOrEqualTo(String value) {
            addCriterion("sales_channel <=", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLike(String value) {
            addCriterion("sales_channel like", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotLike(String value) {
            addCriterion("sales_channel not like", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIn(List<String> values) {
            addCriterion("sales_channel in", values, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotIn(List<String> values) {
            addCriterion("sales_channel not in", values, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelBetween(String value1, String value2) {
            addCriterion("sales_channel between", value1, value2, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotBetween(String value1, String value2) {
            addCriterion("sales_channel not between", value1, value2, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("year is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("year is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(Integer value) {
            addCriterion("year =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(Integer value) {
            addCriterion("year <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(Integer value) {
            addCriterion("year >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("year >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThan(Integer value) {
            addCriterion("year <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(Integer value) {
            addCriterion("year <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearIn(List<Integer> values) {
            addCriterion("year in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<Integer> values) {
            addCriterion("year not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(Integer value1, Integer value2) {
            addCriterion("year between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(Integer value1, Integer value2) {
            addCriterion("year not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andQuarterIsNull() {
            addCriterion("quarter is null");
            return (Criteria) this;
        }

        public Criteria andQuarterIsNotNull() {
            addCriterion("quarter is not null");
            return (Criteria) this;
        }

        public Criteria andQuarterEqualTo(Integer value) {
            addCriterion("quarter =", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterNotEqualTo(Integer value) {
            addCriterion("quarter <>", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterGreaterThan(Integer value) {
            addCriterion("quarter >", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterGreaterThanOrEqualTo(Integer value) {
            addCriterion("quarter >=", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterLessThan(Integer value) {
            addCriterion("quarter <", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterLessThanOrEqualTo(Integer value) {
            addCriterion("quarter <=", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterIn(List<Integer> values) {
            addCriterion("quarter in", values, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterNotIn(List<Integer> values) {
            addCriterion("quarter not in", values, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterBetween(Integer value1, Integer value2) {
            addCriterion("quarter between", value1, value2, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterNotBetween(Integer value1, Integer value2) {
            addCriterion("quarter not between", value1, value2, "quarter");
            return (Criteria) this;
        }

        public Criteria andPointIsNull() {
            addCriterion("point is null");
            return (Criteria) this;
        }

        public Criteria andPointIsNotNull() {
            addCriterion("point is not null");
            return (Criteria) this;
        }

        public Criteria andPointEqualTo(BigDecimal value) {
            addCriterion("point =", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotEqualTo(BigDecimal value) {
            addCriterion("point <>", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointGreaterThan(BigDecimal value) {
            addCriterion("point >", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("point >=", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointLessThan(BigDecimal value) {
            addCriterion("point <", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointLessThanOrEqualTo(BigDecimal value) {
            addCriterion("point <=", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointIn(List<BigDecimal> values) {
            addCriterion("point in", values, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotIn(List<BigDecimal> values) {
            addCriterion("point not in", values, "point");
            return (Criteria) this;
        }

        public Criteria andPointBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("point between", value1, value2, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("point not between", value1, value2, "point");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdIsNull() {
            addCriterion("evaluation_id is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdIsNotNull() {
            addCriterion("evaluation_id is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdEqualTo(Integer value) {
            addCriterion("evaluation_id =", value, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdNotEqualTo(Integer value) {
            addCriterion("evaluation_id <>", value, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdGreaterThan(Integer value) {
            addCriterion("evaluation_id >", value, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("evaluation_id >=", value, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdLessThan(Integer value) {
            addCriterion("evaluation_id <", value, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdLessThanOrEqualTo(Integer value) {
            addCriterion("evaluation_id <=", value, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdIn(List<Integer> values) {
            addCriterion("evaluation_id in", values, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdNotIn(List<Integer> values) {
            addCriterion("evaluation_id not in", values, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdBetween(Integer value1, Integer value2) {
            addCriterion("evaluation_id between", value1, value2, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("evaluation_id not between", value1, value2, "evaluationId");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentIsNull() {
            addCriterion("evaluation_content is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentIsNotNull() {
            addCriterion("evaluation_content is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentEqualTo(String value) {
            addCriterion("evaluation_content =", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentNotEqualTo(String value) {
            addCriterion("evaluation_content <>", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentGreaterThan(String value) {
            addCriterion("evaluation_content >", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentGreaterThanOrEqualTo(String value) {
            addCriterion("evaluation_content >=", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentLessThan(String value) {
            addCriterion("evaluation_content <", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentLessThanOrEqualTo(String value) {
            addCriterion("evaluation_content <=", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentLike(String value) {
            addCriterion("evaluation_content like", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentNotLike(String value) {
            addCriterion("evaluation_content not like", value, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentIn(List<String> values) {
            addCriterion("evaluation_content in", values, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentNotIn(List<String> values) {
            addCriterion("evaluation_content not in", values, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentBetween(String value1, String value2) {
            addCriterion("evaluation_content between", value1, value2, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andEvaluationContentNotBetween(String value1, String value2) {
            addCriterion("evaluation_content not between", value1, value2, "evaluationContent");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerIsNull() {
            addCriterion("diesel_customer is null");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerIsNotNull() {
            addCriterion("diesel_customer is not null");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerEqualTo(BigDecimal value) {
            addCriterion("diesel_customer =", value, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerNotEqualTo(BigDecimal value) {
            addCriterion("diesel_customer <>", value, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerGreaterThan(BigDecimal value) {
            addCriterion("diesel_customer >", value, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_customer >=", value, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerLessThan(BigDecimal value) {
            addCriterion("diesel_customer <", value, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerLessThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_customer <=", value, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerIn(List<BigDecimal> values) {
            addCriterion("diesel_customer in", values, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerNotIn(List<BigDecimal> values) {
            addCriterion("diesel_customer not in", values, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_customer between", value1, value2, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_customer not between", value1, value2, "dieselCustomer");
            return (Criteria) this;
        }

        public Criteria andDieselTradeIsNull() {
            addCriterion("diesel_trade is null");
            return (Criteria) this;
        }

        public Criteria andDieselTradeIsNotNull() {
            addCriterion("diesel_trade is not null");
            return (Criteria) this;
        }

        public Criteria andDieselTradeEqualTo(BigDecimal value) {
            addCriterion("diesel_trade =", value, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeNotEqualTo(BigDecimal value) {
            addCriterion("diesel_trade <>", value, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeGreaterThan(BigDecimal value) {
            addCriterion("diesel_trade >", value, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_trade >=", value, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeLessThan(BigDecimal value) {
            addCriterion("diesel_trade <", value, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_trade <=", value, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeIn(List<BigDecimal> values) {
            addCriterion("diesel_trade in", values, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeNotIn(List<BigDecimal> values) {
            addCriterion("diesel_trade not in", values, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_trade between", value1, value2, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andDieselTradeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_trade not between", value1, value2, "dieselTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerIsNull() {
            addCriterion("biz_fleet_customer is null");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerIsNotNull() {
            addCriterion("biz_fleet_customer is not null");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer =", value, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerNotEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer <>", value, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerGreaterThan(BigDecimal value) {
            addCriterion("biz_fleet_customer >", value, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer >=", value, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerLessThan(BigDecimal value) {
            addCriterion("biz_fleet_customer <", value, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerLessThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer <=", value, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_customer in", values, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerNotIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_customer not in", values, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_customer between", value1, value2, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_customer not between", value1, value2, "bizFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeIsNull() {
            addCriterion("biz_fleet_trade is null");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeIsNotNull() {
            addCriterion("biz_fleet_trade is not null");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade =", value, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeNotEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade <>", value, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeGreaterThan(BigDecimal value) {
            addCriterion("biz_fleet_trade >", value, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade >=", value, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeLessThan(BigDecimal value) {
            addCriterion("biz_fleet_trade <", value, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade <=", value, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_trade in", values, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeNotIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_trade not in", values, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_trade between", value1, value2, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_trade not between", value1, value2, "bizFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerIsNull() {
            addCriterion("industry_fleet_customer is null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerIsNotNull() {
            addCriterion("industry_fleet_customer is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer =", value, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerNotEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer <>", value, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerGreaterThan(BigDecimal value) {
            addCriterion("industry_fleet_customer >", value, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer >=", value, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerLessThan(BigDecimal value) {
            addCriterion("industry_fleet_customer <", value, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerLessThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer <=", value, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_customer in", values, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerNotIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_customer not in", values, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_customer between", value1, value2, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_customer not between", value1, value2, "industryFleetCustomer");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeIsNull() {
            addCriterion("industry_fleet_trade is null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeIsNotNull() {
            addCriterion("industry_fleet_trade is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade =", value, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeNotEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade <>", value, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeGreaterThan(BigDecimal value) {
            addCriterion("industry_fleet_trade >", value, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade >=", value, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeLessThan(BigDecimal value) {
            addCriterion("industry_fleet_trade <", value, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade <=", value, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_trade in", values, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeNotIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_trade not in", values, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_trade between", value1, value2, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_trade not between", value1, value2, "industryFleetTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerIsNull() {
            addCriterion("general_customer is null");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerIsNotNull() {
            addCriterion("general_customer is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerEqualTo(BigDecimal value) {
            addCriterion("general_customer =", value, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerNotEqualTo(BigDecimal value) {
            addCriterion("general_customer <>", value, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerGreaterThan(BigDecimal value) {
            addCriterion("general_customer >", value, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("general_customer >=", value, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerLessThan(BigDecimal value) {
            addCriterion("general_customer <", value, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerLessThanOrEqualTo(BigDecimal value) {
            addCriterion("general_customer <=", value, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerIn(List<BigDecimal> values) {
            addCriterion("general_customer in", values, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerNotIn(List<BigDecimal> values) {
            addCriterion("general_customer not in", values, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_customer between", value1, value2, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_customer not between", value1, value2, "generalCustomer");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeIsNull() {
            addCriterion("general_trade is null");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeIsNotNull() {
            addCriterion("general_trade is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeEqualTo(BigDecimal value) {
            addCriterion("general_trade =", value, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeNotEqualTo(BigDecimal value) {
            addCriterion("general_trade <>", value, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeGreaterThan(BigDecimal value) {
            addCriterion("general_trade >", value, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("general_trade >=", value, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeLessThan(BigDecimal value) {
            addCriterion("general_trade <", value, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("general_trade <=", value, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeIn(List<BigDecimal> values) {
            addCriterion("general_trade in", values, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeNotIn(List<BigDecimal> values) {
            addCriterion("general_trade not in", values, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_trade between", value1, value2, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_trade not between", value1, value2, "generalTrade");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerIsNull() {
            addCriterion("other_customer is null");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerIsNotNull() {
            addCriterion("other_customer is not null");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerEqualTo(BigDecimal value) {
            addCriterion("other_customer =", value, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerNotEqualTo(BigDecimal value) {
            addCriterion("other_customer <>", value, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerGreaterThan(BigDecimal value) {
            addCriterion("other_customer >", value, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_customer >=", value, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerLessThan(BigDecimal value) {
            addCriterion("other_customer <", value, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_customer <=", value, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerIn(List<BigDecimal> values) {
            addCriterion("other_customer in", values, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerNotIn(List<BigDecimal> values) {
            addCriterion("other_customer not in", values, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_customer between", value1, value2, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_customer not between", value1, value2, "otherCustomer");
            return (Criteria) this;
        }

        public Criteria andOtherTradeIsNull() {
            addCriterion("other_trade is null");
            return (Criteria) this;
        }

        public Criteria andOtherTradeIsNotNull() {
            addCriterion("other_trade is not null");
            return (Criteria) this;
        }

        public Criteria andOtherTradeEqualTo(BigDecimal value) {
            addCriterion("other_trade =", value, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeNotEqualTo(BigDecimal value) {
            addCriterion("other_trade <>", value, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeGreaterThan(BigDecimal value) {
            addCriterion("other_trade >", value, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_trade >=", value, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeLessThan(BigDecimal value) {
            addCriterion("other_trade <", value, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_trade <=", value, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeIn(List<BigDecimal> values) {
            addCriterion("other_trade in", values, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeNotIn(List<BigDecimal> values) {
            addCriterion("other_trade not in", values, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_trade between", value1, value2, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andOtherTradeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_trade not between", value1, value2, "otherTrade");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerIsNull() {
            addCriterion("dealer2_customer is null");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerIsNotNull() {
            addCriterion("dealer2_customer is not null");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer =", value, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerNotEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer <>", value, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerGreaterThan(BigDecimal value) {
            addCriterion("dealer2_customer >", value, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer >=", value, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerLessThan(BigDecimal value) {
            addCriterion("dealer2_customer <", value, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer <=", value, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerIn(List<BigDecimal> values) {
            addCriterion("dealer2_customer in", values, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerNotIn(List<BigDecimal> values) {
            addCriterion("dealer2_customer not in", values, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_customer between", value1, value2, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_customer not between", value1, value2, "dealer2Customer");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeIsNull() {
            addCriterion("dealer2_trade is null");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeIsNotNull() {
            addCriterion("dealer2_trade is not null");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade =", value, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeNotEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade <>", value, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeGreaterThan(BigDecimal value) {
            addCriterion("dealer2_trade >", value, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade >=", value, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeLessThan(BigDecimal value) {
            addCriterion("dealer2_trade <", value, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade <=", value, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeIn(List<BigDecimal> values) {
            addCriterion("dealer2_trade in", values, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeNotIn(List<BigDecimal> values) {
            addCriterion("dealer2_trade not in", values, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_trade between", value1, value2, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_trade not between", value1, value2, "dealer2Trade");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Short value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Short value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Short value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Short value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Short value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Short value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Short> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Short> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Short value1, Short value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Short value1, Short value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueIsNull() {
            addCriterion("diesel_customer_value is null");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueIsNotNull() {
            addCriterion("diesel_customer_value is not null");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueEqualTo(BigDecimal value) {
            addCriterion("diesel_customer_value =", value, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueNotEqualTo(BigDecimal value) {
            addCriterion("diesel_customer_value <>", value, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueGreaterThan(BigDecimal value) {
            addCriterion("diesel_customer_value >", value, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_customer_value >=", value, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueLessThan(BigDecimal value) {
            addCriterion("diesel_customer_value <", value, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_customer_value <=", value, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueIn(List<BigDecimal> values) {
            addCriterion("diesel_customer_value in", values, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueNotIn(List<BigDecimal> values) {
            addCriterion("diesel_customer_value not in", values, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_customer_value between", value1, value2, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselCustomerValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_customer_value not between", value1, value2, "dieselCustomerValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueIsNull() {
            addCriterion("diesel_trade_value is null");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueIsNotNull() {
            addCriterion("diesel_trade_value is not null");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueEqualTo(BigDecimal value) {
            addCriterion("diesel_trade_value =", value, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueNotEqualTo(BigDecimal value) {
            addCriterion("diesel_trade_value <>", value, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueGreaterThan(BigDecimal value) {
            addCriterion("diesel_trade_value >", value, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_trade_value >=", value, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueLessThan(BigDecimal value) {
            addCriterion("diesel_trade_value <", value, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("diesel_trade_value <=", value, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueIn(List<BigDecimal> values) {
            addCriterion("diesel_trade_value in", values, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueNotIn(List<BigDecimal> values) {
            addCriterion("diesel_trade_value not in", values, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_trade_value between", value1, value2, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andDieselTradeValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("diesel_trade_value not between", value1, value2, "dieselTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueIsNull() {
            addCriterion("biz_fleet_customer_value is null");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueIsNotNull() {
            addCriterion("biz_fleet_customer_value is not null");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer_value =", value, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueNotEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer_value <>", value, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueGreaterThan(BigDecimal value) {
            addCriterion("biz_fleet_customer_value >", value, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer_value >=", value, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueLessThan(BigDecimal value) {
            addCriterion("biz_fleet_customer_value <", value, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_customer_value <=", value, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_customer_value in", values, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueNotIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_customer_value not in", values, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_customer_value between", value1, value2, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetCustomerValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_customer_value not between", value1, value2, "bizFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueIsNull() {
            addCriterion("biz_fleet_trade_value is null");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueIsNotNull() {
            addCriterion("biz_fleet_trade_value is not null");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade_value =", value, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueNotEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade_value <>", value, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueGreaterThan(BigDecimal value) {
            addCriterion("biz_fleet_trade_value >", value, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade_value >=", value, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueLessThan(BigDecimal value) {
            addCriterion("biz_fleet_trade_value <", value, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("biz_fleet_trade_value <=", value, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_trade_value in", values, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueNotIn(List<BigDecimal> values) {
            addCriterion("biz_fleet_trade_value not in", values, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_trade_value between", value1, value2, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andBizFleetTradeValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("biz_fleet_trade_value not between", value1, value2, "bizFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueIsNull() {
            addCriterion("industry_fleet_customer_value is null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueIsNotNull() {
            addCriterion("industry_fleet_customer_value is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer_value =", value, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueNotEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer_value <>", value, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueGreaterThan(BigDecimal value) {
            addCriterion("industry_fleet_customer_value >", value, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer_value >=", value, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueLessThan(BigDecimal value) {
            addCriterion("industry_fleet_customer_value <", value, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_customer_value <=", value, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_customer_value in", values, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueNotIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_customer_value not in", values, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_customer_value between", value1, value2, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetCustomerValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_customer_value not between", value1, value2, "industryFleetCustomerValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueIsNull() {
            addCriterion("industry_fleet_trade_value is null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueIsNotNull() {
            addCriterion("industry_fleet_trade_value is not null");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade_value =", value, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueNotEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade_value <>", value, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueGreaterThan(BigDecimal value) {
            addCriterion("industry_fleet_trade_value >", value, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade_value >=", value, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueLessThan(BigDecimal value) {
            addCriterion("industry_fleet_trade_value <", value, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("industry_fleet_trade_value <=", value, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_trade_value in", values, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueNotIn(List<BigDecimal> values) {
            addCriterion("industry_fleet_trade_value not in", values, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_trade_value between", value1, value2, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andIndustryFleetTradeValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("industry_fleet_trade_value not between", value1, value2, "industryFleetTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueIsNull() {
            addCriterion("general_customer_value is null");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueIsNotNull() {
            addCriterion("general_customer_value is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueEqualTo(BigDecimal value) {
            addCriterion("general_customer_value =", value, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueNotEqualTo(BigDecimal value) {
            addCriterion("general_customer_value <>", value, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueGreaterThan(BigDecimal value) {
            addCriterion("general_customer_value >", value, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("general_customer_value >=", value, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueLessThan(BigDecimal value) {
            addCriterion("general_customer_value <", value, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("general_customer_value <=", value, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueIn(List<BigDecimal> values) {
            addCriterion("general_customer_value in", values, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueNotIn(List<BigDecimal> values) {
            addCriterion("general_customer_value not in", values, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_customer_value between", value1, value2, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralCustomerValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_customer_value not between", value1, value2, "generalCustomerValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueIsNull() {
            addCriterion("general_trade_value is null");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueIsNotNull() {
            addCriterion("general_trade_value is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueEqualTo(BigDecimal value) {
            addCriterion("general_trade_value =", value, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueNotEqualTo(BigDecimal value) {
            addCriterion("general_trade_value <>", value, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueGreaterThan(BigDecimal value) {
            addCriterion("general_trade_value >", value, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("general_trade_value >=", value, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueLessThan(BigDecimal value) {
            addCriterion("general_trade_value <", value, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("general_trade_value <=", value, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueIn(List<BigDecimal> values) {
            addCriterion("general_trade_value in", values, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueNotIn(List<BigDecimal> values) {
            addCriterion("general_trade_value not in", values, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_trade_value between", value1, value2, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andGeneralTradeValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("general_trade_value not between", value1, value2, "generalTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueIsNull() {
            addCriterion("other_customer_value is null");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueIsNotNull() {
            addCriterion("other_customer_value is not null");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueEqualTo(BigDecimal value) {
            addCriterion("other_customer_value =", value, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueNotEqualTo(BigDecimal value) {
            addCriterion("other_customer_value <>", value, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueGreaterThan(BigDecimal value) {
            addCriterion("other_customer_value >", value, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_customer_value >=", value, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueLessThan(BigDecimal value) {
            addCriterion("other_customer_value <", value, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_customer_value <=", value, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueIn(List<BigDecimal> values) {
            addCriterion("other_customer_value in", values, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueNotIn(List<BigDecimal> values) {
            addCriterion("other_customer_value not in", values, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_customer_value between", value1, value2, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherCustomerValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_customer_value not between", value1, value2, "otherCustomerValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueIsNull() {
            addCriterion("other_trade_value is null");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueIsNotNull() {
            addCriterion("other_trade_value is not null");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueEqualTo(BigDecimal value) {
            addCriterion("other_trade_value =", value, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueNotEqualTo(BigDecimal value) {
            addCriterion("other_trade_value <>", value, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueGreaterThan(BigDecimal value) {
            addCriterion("other_trade_value >", value, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_trade_value >=", value, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueLessThan(BigDecimal value) {
            addCriterion("other_trade_value <", value, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_trade_value <=", value, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueIn(List<BigDecimal> values) {
            addCriterion("other_trade_value in", values, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueNotIn(List<BigDecimal> values) {
            addCriterion("other_trade_value not in", values, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_trade_value between", value1, value2, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andOtherTradeValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_trade_value not between", value1, value2, "otherTradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueIsNull() {
            addCriterion("dealer2_customer_value is null");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueIsNotNull() {
            addCriterion("dealer2_customer_value is not null");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer_value =", value, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueNotEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer_value <>", value, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueGreaterThan(BigDecimal value) {
            addCriterion("dealer2_customer_value >", value, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer_value >=", value, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueLessThan(BigDecimal value) {
            addCriterion("dealer2_customer_value <", value, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_customer_value <=", value, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueIn(List<BigDecimal> values) {
            addCriterion("dealer2_customer_value in", values, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueNotIn(List<BigDecimal> values) {
            addCriterion("dealer2_customer_value not in", values, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_customer_value between", value1, value2, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2CustomerValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_customer_value not between", value1, value2, "dealer2CustomerValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueIsNull() {
            addCriterion("dealer2_trade_value is null");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueIsNotNull() {
            addCriterion("dealer2_trade_value is not null");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade_value =", value, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueNotEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade_value <>", value, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueGreaterThan(BigDecimal value) {
            addCriterion("dealer2_trade_value >", value, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade_value >=", value, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueLessThan(BigDecimal value) {
            addCriterion("dealer2_trade_value <", value, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dealer2_trade_value <=", value, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueIn(List<BigDecimal> values) {
            addCriterion("dealer2_trade_value in", values, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueNotIn(List<BigDecimal> values) {
            addCriterion("dealer2_trade_value not in", values, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_trade_value between", value1, value2, "dealer2TradeValue");
            return (Criteria) this;
        }

        public Criteria andDealer2TradeValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dealer2_trade_value not between", value1, value2, "dealer2TradeValue");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}