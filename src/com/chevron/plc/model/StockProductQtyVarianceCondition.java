package com.chevron.plc.model;

import java.util.HashMap;
import java.util.Map;

import com.common.util.StringUtils;

import com.chevron.pms.model.BaseParams;

public class StockProductQtyVarianceCondition extends BaseParams {

	// 前台传递的
	private String orderNo;
	private String stockNo;

	private final static Map<String, String> fieldMap = new HashMap<String, String>(
			12);

	static {
		fieldMap.put("orderNo", "order_no");
		fieldMap.put("stock_no", "stock_no");

	}

	public StockProductQtyVarianceCondition() {
		// 默认排序字段
		setField("createTime");
		setDirection("desc");
	}

	@Override
	protected Map<String, String> getFieldMap() {
		return fieldMap;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		if (StringUtils.isEmpty(orderNo)) {
			this.orderNo = null;
		} else {
			this.orderNo = orderNo;
		}
	}

	public String getStockNo() {
		return stockNo;
	}

	public void setStockNo(String stockNo) {
		if (StringUtils.isEmpty(stockNo)) {
			this.stockNo = null;
		} else {
			this.stockNo = stockNo;
		}
	}

}
