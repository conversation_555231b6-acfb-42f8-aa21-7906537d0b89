select * 
--update u set u.status=1
from wx_log l left join wx_t_user u on u.user_id=l.ext_property1 
where l.log_type='UserDelete' and u.org_id=56665

select * 
--update o set o.status=1, o.organization_name='辽宁众信商贸有限公司'
--update pe set pe.distributor_id=1238
from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
 where o.organization_name like '%辽宁%' and o.id=56665
 --select * from wx_t_partner_o2o_enterprise where distributor_id=1238
 
--合并经销商
SELECT * FROM chemid.chevronDW_Base_202002.chv_ods.master_customer_merge C WHERE [old distributor id] in (1143,1183)--C.[old amended customer name(chinese)]='辽宁九孚工程有限公司'

insert into dw_access_control_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[customer_category]
      ,[customer_type]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[control_type]
      ,[control_type_name]
      ,[product_channel]
      ,[channel_weight]
      ,[from_source])
 select a.* from (
	  SELECT 1183 [distributor_id]
      ,'张家口昌润商贸有限公司' [customer_name_cn]
      ,cs.[org_hier_id]
      ,cs.[region]
      ,cs.[sales_cai]
      ,cs.[sales_name_cn]
      ,cs.[customer_category]
      ,cs.[customer_type]
      ,cs.[create_time]
      ,cs.[update_time]
      ,cs.[del_flag]
      ,cs.[control_type]
      ,cs.[control_type_name]
      ,cs.[product_channel]
	  ,case when so.bu='Indirect' then (case when cs.[customer_category]='Consumer' 
			then 1 when cs.[customer_category]='Commercial' then 2 else 0 end) 
			when so.sales_channel='OEM' then 4 
			when so.sales_channel='Industrial' then 8
			else null end channel_weight
	  ,'PP' from_source
  FROM [PP_MID].[dbo].[syn_dw_to_pp_access_control_customer_org_sales] cs
  left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on cs.org_hier_id=so.org_hier_id
  where cs.distributor_id=1244
 ) a
 
 insert into dw_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[product_channel]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[supervisor_cai]
      ,[supervisor_name_cn]
      ,[effective_from_date]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[customer_type]
      ,[from_source]
	  ,channel_weight)
 select 1183 [distributor_id]
      ,'张家口昌润商贸有限公司' [customer_name_cn]
      ,a.[org_hier_id]
      ,a.[region]
      ,a.[product_channel]
      ,a.[sales_cai]
      ,a.[sales_name_cn]
      ,a.[supervisor_cai]
      ,a.[supervisor_name_cn]
      ,a.[effective_from_date]
      ,a.[create_time]
      ,a.[update_time]
      ,a.[del_flag]
      ,a.[customer_type]
      ,'PP' [from_source],
	  case when so.bu='Indirect' then (case when a.product_channel='Consumer' 
			then 1 when a.product_channel='Commercial' then 2 else 0 end) 
			when so.sales_channel='OEM' then 4 
			when so.sales_channel='Industrial' then 8
			else null end channel_weight 
	from PP_MID.dbo.syn_dw_to_pp_customer_org_sales a
	left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on a.org_hier_id=so.org_hier_id
  where not exists (select 1 from dw_customer_org_sales dac1 where dac1.from_source='PP' and a.region=dac1.region 
	and a.distributor_id=dac1.distributor_id and a.product_channel=dac1.product_channel)
	and a.distributor_id=1244

