package com.chevron.master.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chevron.master.dao.PurchaseIncentiveMapper;
import com.chevron.master.dao.PurchaseIncentiveSkuMapper;
import com.chevron.master.model.PurchaseIncentive;
import com.chevron.master.model.PurchaseIncentiveSku;
import com.chevron.master.model.WorkShopUserVo;
import com.chevron.master.service.PurchaseIncentiveService;
import com.chevron.master.util.GiftPackageConstants;
import com.common.base.BaseResp;
import com.common.util.DateUtil;
import com.sys.utils.business.EtoBizService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description PurchaseIncentiveServiceImpl 2024门店复购激励
 * <AUTHOR> Ma
 * @Date 11:04 05/01/2024
 **/
@Service
public class PurchaseIncentiveServiceImpl implements PurchaseIncentiveService {

    private static Logger log = LoggerFactory.getLogger(PurchaseIncentiveServiceImpl.class);

    @Resource
    private PurchaseIncentiveMapper purchaseIncentiveMapper;
    @Resource
    private EtoBizService etoBizService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private PurchaseIncentiveSkuMapper purchaseIncentiveSkuMapper;

    @Override
    public BaseResp innitData(GiftPackageConstants.QuarterEnum quarterEnum) {
        if (DateUtil.getCurrentDate().after(DateUtil.parseDate("2025-12-02", DateUtil.DEFAULT_DATE_PATTERN))) {
            log.info("放弃执行purchaseIncentive workShop list，已过业务时间：2025-12-01");
            return new BaseResp();
        }

        if (null == quarterEnum) {
            Date yesterday = DateUtil.addDays(DateUtil.getCurrentDate(), -1);
            int month = DateUtil.getMonthReInt(yesterday);
            quarterEnum = GiftPackageConstants.QuarterEnum.getQuarter(month);
        }
        final String quarterStr = quarterEnum.getDesc();
        //List<Long> workShopIds = getWorkShopId(quarterEnum); 2025-04-22 激励按季度
        List<Long> workShopIds = getOutStockWorkShopIdByMonth(quarterEnum);//激励按月份
        log.info("purchaseIncentive workShop list, quarter: {}, workShopIds: {}", quarterStr, workShopIds);
        if (CollectionUtils.isEmpty(workShopIds)) {
            return new BaseResp();
        }

        List<WorkShopUserVo> workShopUsers = queryPurchaseIncentiveUser(workShopIds);
        List<PurchaseIncentiveSku> skuList = querySku(workShopIds, quarterEnum);

        Date currentTime = new Date();
        List<PurchaseIncentive> purchaseIncentives = workShopUsers.stream().map(workShopUser -> {
            PurchaseIncentive purchaseIncentive = new PurchaseIncentive();
            purchaseIncentive.setCreateTime(currentTime);
            purchaseIncentive.setUpdateTime(currentTime);
            purchaseIncentive.setCreateUserId(-1L);
            purchaseIncentive.setUpdateUserId(-1L);
            purchaseIncentive.setYear(GiftPackageConstants.DEFAULT_YEAR);
            purchaseIncentive.setQuarter(quarterStr);
            purchaseIncentive.setPoint(GiftPackageConstants.DEFAULT_POINT);
            purchaseIncentive.setWorkShopId(workShopUser.getWorkShopId());
            purchaseIncentive.setUserId(workShopUser.getUserId());
            purchaseIncentive.setUserName(workShopUser.getUserName());
            purchaseIncentive.setUserMobile(workShopUser.getUserMobile());
            purchaseIncentive.setSyncEtoStatus(GiftPackageConstants.PISyncEtoStatusEnum.SYNC_SUCCESS.getCode());
            return purchaseIncentive;
        }).collect(Collectors.toList());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            public void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                purchaseIncentives.forEach(purchaseIncentive -> purchaseIncentiveMapper.insertSelective(purchaseIncentive));
                fillPurchaseIncentiveId(skuList, purchaseIncentives, currentTime);
                for (List<PurchaseIncentiveSku> subSkuList: CollectionUtil.split(skuList, 100)) {
                    purchaseIncentiveSkuMapper.batchInsert(subSkuList);
                }
            }
        });
        log.info("purchaseIncentive innit data success, quarter: {}, size: {}", quarterStr, purchaseIncentives.size());
        return new BaseResp();
    }

    private List<WorkShopUserVo> queryPurchaseIncentiveUser(List<Long> workShopIds) {
        List<WorkShopUserVo> workShopUsers = new ArrayList<>();
        for (List<Long> subWorkShopIds: CollectionUtil.split(workShopIds, 1000)) {
            if (CollectionUtil.isNotEmpty(subWorkShopIds)) {
                List<WorkShopUserVo> subWorkShopUsers = purchaseIncentiveMapper.queryPurchaseIncentiveUser(subWorkShopIds);
                workShopUsers.addAll(subWorkShopUsers);
            }
        }
        return workShopUsers;
    }

    private void fillPurchaseIncentiveId(List<PurchaseIncentiveSku> skuList, List<PurchaseIncentive> purchaseIncentives, Date currentTime) {
        Map<Long, PurchaseIncentive> purchaseIncentiveMap = purchaseIncentives.stream().collect(Collectors.toMap(PurchaseIncentive::getWorkShopId, Function.identity()));
        for (PurchaseIncentiveSku sku:skuList) {
            sku.setCreateTime(currentTime);
            sku.setUpdateTime(currentTime);
            sku.setCreateUserId(-1L);
            sku.setUpdateUserId(-1L);

            PurchaseIncentive purchaseIncentive = purchaseIncentiveMap.get(sku.getWorkShopId());
            if (purchaseIncentive != null) {
                sku.setPurchaseIncentiveId(purchaseIncentive.getId());
            }
        }
    }

    /**
     * 获取季度激励奖励
     * Q1 蓝盾门店(Q1出库数据符合条件)
     * Q2 蓝盾门店(Q2出库数据符合条件)+合作门店(Q1,Q2出库数据符合条件)
     * Q3 蓝盾门店(Q3出库数据符合条件)+合作门店(Q3出库数据符合条件且Q1,Q2任意有一天出库数据符合条件)
     * Q4 蓝盾门店(Q4出库数据符合条件)+合作门店(Q4出库数据符合条件且Q1,Q2,Q3任意有一天出库数据符合条件)
     */
    private List<Long> getWorkShopId(GiftPackageConstants.QuarterEnum quarterEnum) {
        String startTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + quarterEnum.getStart();
        String endTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + quarterEnum.getEnd();

        // 查询符合条件的蓝盾门店，若明年继续，则需将当年成为全合成门店标签的门店刷入wx_t_blue_shield_work_shop中，再更改DEFAULT_YEAR、DEFAULT_START_TIME、DEFAULT_END_TIME
        List<Long> blueShieldWorkShopIds = purchaseIncentiveMapper.queryBlueShieldPurchaseIncentiveWorkshop(GiftPackageConstants.DEFAULT_YEAR, quarterEnum.getDesc(), GiftPackageConstants.DEFAULT_CAPACITY, startTime, endTime);
        if (GiftPackageConstants.QuarterEnum.ONE.equals(quarterEnum)) {
            return blueShieldWorkShopIds;
        }

        // 查询符合条件的合作门店（本季度与当年之前所有季度取交集）
        List<Long> currQuarterCooWorkShopIds = purchaseIncentiveMapper.queryPurchaseIncentiveWorkshop(GiftPackageConstants.DEFAULT_YEAR, quarterEnum.getDesc(), GiftPackageConstants.DEFAULT_CAPACITY, startTime, endTime, true);
        String preStartTime = getPreStartTime();
        String preEndTime = getPreEndTime(quarterEnum);
        List<Long> preQuarterCooWorkShopIds = purchaseIncentiveMapper.queryPurchaseIncentiveWorkshop(null, null, GiftPackageConstants.DEFAULT_CAPACITY, preStartTime, preEndTime, false);
        List<Long> cooWorkShopIds = currQuarterCooWorkShopIds.stream().filter(preQuarterCooWorkShopIds::contains).collect(Collectors.toList());

        return new ArrayList<>(CollectionUtils.union(blueShieldWorkShopIds, cooWorkShopIds));
    }
    //(月度复购)获取符合复购激励的门店ID
    private List<Long> getOutStockWorkShopIdByMonth(GiftPackageConstants.QuarterEnum quarterEnum) {
        Date yesterday = DateUtil.addDays(DateUtil.getCurrentDate(), -1);
        int month = DateUtil.getMonthReInt(yesterday);

        String StrMonth = "" + month;
        if(month<10){
            StrMonth = "0" + StrMonth;
        }
        //本月第一天
        String startTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL +StrMonth + "-01 00:00:00";
        //本月最后一天
        String endTime = DateUtil.getLastDateStrByYearMonth(Integer.parseInt(GiftPackageConstants.DEFAULT_YEAR), month)+ " 23:59:59";

        //季度开始和结束时间
        String quarterStartTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + quarterEnum.getStart();
        if(quarterEnum.getCode()==2){
            //5月活动开始
            quarterStartTime = "2025-05-01 00:00:00";
        }
        String quarterEndTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + quarterEnum.getEnd();
        // 查询符合条件的蓝盾门店(全合成门店)
        List<Long> blueShieldWorkShopIds = purchaseIncentiveMapper.queryBlueShieldPurchaseIncentiveWorkshopMonth(GiftPackageConstants.DEFAULT_YEAR,
                GiftPackageConstants.DEFAULT_YEAR+"-"+StrMonth, GiftPackageConstants.DEFAULT_CAPACITY,
                startTime, endTime,quarterStartTime,quarterEndTime);
//        if (month<2) {
//            return blueShieldWorkShopIds;
//        }
        //活动期间，新增门店不算，只看固定全合成门店
        return blueShieldWorkShopIds;
       /* // 查询符合条件的合作门店（本月与当年之前所有取交集）
        List<Long> currQuarterCooWorkShopIds = purchaseIncentiveMapper.queryPurchaseIncentiveWorkshopMoth(GiftPackageConstants.DEFAULT_YEAR, GiftPackageConstants.DEFAULT_YEAR+"-"+StrMonth, GiftPackageConstants.DEFAULT_CAPACITY, startTime, endTime, true);
        String preStartTime = getPreStartTime();
        //当前日期的上一月的最后一天
        String preEndTime = DateUtil.getLastDateStrByYearMonth(Integer.parseInt(GiftPackageConstants.DEFAULT_YEAR), month-1)+ " 23:59:59";
        List<Long> preQuarterCooWorkShopIds = purchaseIncentiveMapper.queryPurchaseIncentiveWorkshopMoth(null, null, GiftPackageConstants.DEFAULT_CAPACITY, preStartTime, preEndTime, false);
        List<Long> cooWorkShopIds = currQuarterCooWorkShopIds.stream().filter(preQuarterCooWorkShopIds::contains).collect(Collectors.toList());

        return new ArrayList<>(CollectionUtils.union(blueShieldWorkShopIds, cooWorkShopIds));*/
    }


    private List<PurchaseIncentiveSku> querySku(List<Long> workShopIds, GiftPackageConstants.QuarterEnum quarterEnum) {
        String startTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + quarterEnum.getStart();
        String endTime = GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + quarterEnum.getEnd();

        List<PurchaseIncentiveSku> skuList = new ArrayList<>();
        for (List<Long> subWorkShopIds: CollectionUtil.split(workShopIds, 500)) {
            List<PurchaseIncentiveSku> subSkuList = purchaseIncentiveSkuMapper.querySkuDetail(GiftPackageConstants.DEFAULT_CAPACITY, startTime, endTime, subWorkShopIds);
            skuList.addAll(subSkuList);
        }
        return skuList;
    }

    private String getPreStartTime() {
        return GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + "01-01 00:00:00";
    }

    private String getPreEndTime(GiftPackageConstants.QuarterEnum currentQuarter) {
        if (GiftPackageConstants.QuarterEnum.ONE.equals(currentQuarter)) {
            throw new RuntimeException("当前季度为第一季度，不应该执行到这里");
        }
        if (GiftPackageConstants.QuarterEnum.TWO.equals(currentQuarter)) {
            return GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + "03-31 23:59:59";
        }
        if (GiftPackageConstants.QuarterEnum.THREE.equals(currentQuarter)) {
            return GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + "06-30 23:59:59";
        }
        if (GiftPackageConstants.QuarterEnum.FOUR.equals(currentQuarter)) {
            return GiftPackageConstants.DEFAULT_YEAR + GiftPackageConstants.HORIZONTAL + "09-30 23:59:59";
        }
        throw new RuntimeException("非预期季度，quarter=" + currentQuarter.getDesc());
    }

    /**
     * 当前月份是否处于该季节内
     * @return
     */
    private boolean inQuarter(int month, GiftPackageConstants.QuarterEnum quarterEnum) {
        if (quarterEnum.equals(GiftPackageConstants.QuarterEnum.ONE)) {
            return month >= 1 && month <= 3;
        }
        if (quarterEnum.equals(GiftPackageConstants.QuarterEnum.TWO)) {
            return month >= 4 && month <= 6;
        }
        if (quarterEnum.equals(GiftPackageConstants.QuarterEnum.THREE)) {
            return month >= 7 && month <= 9;
        }
        return month >= 10 && month <= 12;
    }
}
