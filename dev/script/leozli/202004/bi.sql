/*==============================================================*/
/* Module: BI                                                   */
/*==============================================================*/

/*==============================================================*/
/* Table: bit_rate_by_month                                     */
/*==============================================================*/
create table bit_rate_by_month (
	id                               bigint                            not null identity,
	module_name                      nvarchar(256)                              not null,
	effective_month                  date                                       not null,
	rate                             decimal(18,8)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE bit_rate_by_month ADD CONSTRAINT uk_rate_by_month UNIQUE (module_name,effective_month);

/*==============================================================*/
/* Table: pp_distributor_customer                               */
/*==============================================================*/
create table pp_distributor_customer (
	id                               bigint                            not null identity,
	distributor_id                   bigint                                     not null,
	customer_type                    int                                        not null,
	customer_id                      bigint                                     not null,
	customer_subtype                 nvarchar(256)                                  null,
	customer_name                    nvarchar(255)                              not null,
	province_code                    nvarchar(20)                                   null,
	province_name                    nvarchar(64)                                   null,
	city_code                        nvarchar(20)                                   null,
	city_name                        nvarchar(64)                                   null,
	dist_code                        nvarchar(20)                                   null,
	dist_name                        nvarchar(64)                                   null,
	address                          nvarchar(256)                                  null,
	customer_status                  int                                        not null,
	dms_key                          nvarchar(256)                                  null,
	dms_distributor_code             nvarchar(20)                                   null,
	dms_customer_code                nvarchar(128)                                  null,
	contact_person                   nvarchar(100)                                  null,
	contact_person_tel               nvarchar(18)                                   null,
	from_source                      int                                            null,
	customer_create_time             datetime                                       null,
	customer_label                   int                                        not null,
	dsr_name                         nvarchar(64)                                   null,
	business_property                int                                        not null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(256)                                  null,
	delete_flag                      tinyint                                    not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE pp_distributor_customer ADD CONSTRAINT uk_distributor_customer UNIQUE (customer_type,customer_id);
CREATE INDEX ix_dis_cus_dis_id ON pp_distributor_customer (distributor_id);

/*==============================================================*/
/* Table: pp_dict                                               */
/*==============================================================*/
create table pp_dict (
	id                               bigint                            not null identity,
	type_code                        nvarchar(128)                              not null,
	item_code                        nvarchar(128)                              not null,
	item_name                        nvarchar(64)                                   null,
	sort_numb                        decimal(20,10)                                 null,
	enable_flag                      tinyint                                    not null,
	remark                           nvarchar(1024)                                 null,
	delete_flag                      tinyint                                    not null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE pp_dict ADD CONSTRAINT uk_dict UNIQUE (type_code,item_code);
