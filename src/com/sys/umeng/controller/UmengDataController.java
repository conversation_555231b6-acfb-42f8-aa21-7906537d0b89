package com.sys.umeng.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.sys.umeng.model.ImportUmengEventData;
import com.sys.umeng.model.ImportUmengMobileportalData;
import com.sys.umeng.service.UmengDataService;

@Controller
@RequestMapping(value = "/umengDataServ")
public class UmengDataController {

	public static final String UMENG_EVENT_SHEET_MODE[] = new String[]{"eventDateTime","eventName","eventValue","eventPage"};
	public static final String UMENG_ACTIVE_USER_SHEET_MODE[] = new String[]{"dateTime","activeUser","activeUserProportion"};
	public static final int _SHEET_INDEX = 1;
	
	@Resource
	UmengDataService umengDataService;
	
	@ResponseBody
	@RequestMapping(value = "/importUmengEventFromExcel.do", method = RequestMethod.POST)
	public Object importUmengEventFromExcel(
			@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			CommonsMultipartFile cf = (CommonsMultipartFile) myfiles[0];
			DiskFileItem fi = (DiskFileItem) cf.getFileItem();
			Map<String,Object> eventMap =  umengDataService.importUmengEventFromExcel(fi, _SHEET_INDEX, new ImportUmengEventData(), UMENG_EVENT_SHEET_MODE);
			
			if(!"success".equals((String)eventMap.get("code")))
			{
				throw new Exception((String)eventMap.get("codeMsg"));
			}
		    List<ImportUmengEventData> lstImportUmengEventDatas =  (List<ImportUmengEventData>) eventMap.get("dataList");
		    resultMap = umengDataService.insertUmengEvent(lstImportUmengEventDatas);
			
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入umeng事件详细表异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
		return resultMap;

	}
	
	
	
	@ResponseBody
	@RequestMapping(value = "/importUmengMobileportalFromExcel.do", method = RequestMethod.POST)
	public Object importUmengMobileportalFromExcel(
			@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {

			CommonsMultipartFile cf = (CommonsMultipartFile) myfiles[0];
			DiskFileItem fi = (DiskFileItem) cf.getFileItem();
			Map<String,Object> mobileMap =  umengDataService.importUmengMobileportalFromExcel(fi, _SHEET_INDEX, new ImportUmengMobileportalData(), UMENG_ACTIVE_USER_SHEET_MODE);
			
			if(!"success".equals((String)mobileMap.get("code")))
			{
				throw new Exception((String)mobileMap.get("codeMsg"));
			}
		    List<ImportUmengMobileportalData> lstImportMobileportalDatas =  (List<ImportUmengMobileportalData>) mobileMap.get("dataList");
		    resultMap = umengDataService.insertUmengMobileportal(lstImportMobileportalDatas);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入umeng活跃用户数据异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
		return resultMap;

	}
}
