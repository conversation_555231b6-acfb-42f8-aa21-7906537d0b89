package com.chevron.task.controller;

import java.awt.Color;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.chevron.task.service.impl.TaskMainService;
import com.common.exception.auth.WxAuthException;
import com.common.util.PDFUtil;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.esorder.service.EsOrderService;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.task.business.AppTaskBizService;
import com.chevron.task.business.TaskService;
import com.chevron.task.dao.WxTaskMainMapper;
import com.chevron.task.model.MainTaskForAttFileQueryConditions;
import com.chevron.task.model.MainTaskQueryConditions;
import com.chevron.task.model.WxTaskForXDAttFile;
import com.chevron.task.model.WxTaskMainNew;
import com.chevron.task.service.TaskMainServiceI;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.AsynExcelExportHelper;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.FileUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.sys.auth.model.WxTUser;
import com.sys.file.web.FileManager;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVo;
import com.sys.utils.model.AsynProcessStatus;

/**
 * 
 * @Author: bo.liu 2016-11-16 下午2:32:18
 * @Version: $Id$
 * @Desc: <p>
 *        任务管理控制类
 *        </p>
 */
@Controller
@RequestMapping(value = "/taskServ")
public class TaskServController {

	@Resource
	TaskService taskService;
	@Resource 
	TaskMainServiceI taskMainService;

	
	@Resource 
	EsOrderService esOrderService;
	
	@Resource
	private WxTaskMainMapper taskMainMapper;
	
	@Resource
	OrganizationVoMapper orgMapper;
	
	@Autowired
	private AppTaskBizService appTaskBizService;
	
	private final static Map<String, String> TASK_NEW_STATUS_MAP = new HashMap<String, String>(5);
	static {
		TASK_NEW_STATUS_MAP.put("5", "未抽查");
		TASK_NEW_STATUS_MAP.put("3", "不合格");
		TASK_NEW_STATUS_MAP.put("8", "合格");
	}
	
	/** 分页查询参数缓存键值 */
	public final static String PARAMS_CACHE_KEY = "com.chevron.task.controller.TaskServController.params";
	
	private final static Logger log = Logger
			.getLogger(TaskServController.class);
	

	/**
	 * 查询主任务列表
	 * 
	 * <AUTHOR> 2016-11-16 下午2:32:47
	 * @param mainTaskQueryConditions
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryMainTasks.do", method = { RequestMethod.POST })
	public Map<String, Object> queryTaskByTypeCode(
			MainTaskQueryConditions mainTaskQueryConditions, HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap = taskService
					.getMainTaskByTaskTypeCodeDataControl(mainTaskQueryConditions);
			request.getSession().setAttribute(PARAMS_CACHE_KEY, mainTaskQueryConditions);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("error", e);
		}
		return resultMap;
	}

    @ResponseBody
    @RequestMapping(value = "/queryTaskByTypeAndBrand.do", method = {RequestMethod.POST})
    public Map<String, Object> queryTaskByTypeAndBrand(
            MainTaskQueryConditions mainTaskQueryConditions, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            resultMap = taskService
                    .queryTaskByTypeAndBrand(mainTaskQueryConditions);
            request.getSession().setAttribute(PARAMS_CACHE_KEY, mainTaskQueryConditions);
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error", e);
        }
        return resultMap;
    }

	@ResponseBody
    @RequestMapping(value = "/exportTaskByTypeAndBrand.do")
    public JsonResponse exportTaskByTypeAndBrand(
            final MainTaskQueryConditions mainTaskQueryConditions, HttpServletRequest request, HttpServletResponse response) {
		JsonResponse resultMap = new JsonResponse();
    	String p = JsonUtil.writeValue(mainTaskQueryConditions);
    	log.info("exportTaskByTypeAndBrand: " + p);
        try {
        	mainTaskQueryConditions.setPaging(false);
    		// 导出列定义
    		List<ExportCol> exportCols = new ArrayList<ExportCol>();
    		ExportCol col = new ExportCol("taskName", "任务名称", WxTaskMainNew.class);
    		col.setWidth(40);
    		exportCols.add(col);
    		col = new ExportCol("regionName", "大区", WxTaskMainNew.class);
    		col.setWidth(10);
    		exportCols.add(col);
    		col = new ExportCol("", "品牌");
    		col.setWidth(15);
    		col.setPropertyHelper(new IPropertyHelper() {
    			
    			@Override
    			public Object getProperty(String propertyName, Object bean) throws Exception {
    				Integer brand = ((WxTaskMainNew)bean).getBrand();
    				if(brand == null) {
    					return null;
    				}
    				return Constants.getBrandName(brand);
    			}
    		});
    		exportCols.add(col);
    		col = new ExportCol("organizationName", "经销商名称", WxTaskMainNew.class);
    		col.setWidth(40);
    		exportCols.add(col);
    		col = new ExportCol("workShopName", "客户名称", WxTaskMainNew.class);
    		col.setWidth(40);
    		exportCols.add(col);
    		col = new ExportCol("", "完成日期");
    		col.setWidth(20);
    		col.setPropertyHelper(new IPropertyHelper() {
    			
    			@Override
    			public Object getProperty(String propertyName, Object bean) throws Exception {
    				Date date = ((WxTaskMainNew)bean).getTaskFinishTime();
    				if(date == null) {
    					return "";
    				}
    				return DateUtil.getDateStr(date, "yyyy-MM-dd HH:mm");
    			}
    		});
    		exportCols.add(col);
    		col = new ExportCol("", "完成年");
    		col.setWidth(12);
    		col.setPropertyHelper(new IPropertyHelper() {
    			
    			@Override
    			public Object getProperty(String propertyName, Object bean) throws Exception {
    				Date date = ((WxTaskMainNew)bean).getTaskFinishTime();
    				if(date == null) {
    					return "";
    				}
    				return DateUtil.getYear(date);
    			}
    		});
    		exportCols.add(col);
    		col = new ExportCol("", "完成月");
    		col.setWidth(12);
    		col.setPropertyHelper(new IPropertyHelper() {
    			
    			@Override
    			public Object getProperty(String propertyName, Object bean) throws Exception {
    				Date date = ((WxTaskMainNew)bean).getTaskFinishTime();
    				if(date == null) {
    					return "";
    				}
    				return DateUtil.getMonth(date);
    			}
    		});
    		exportCols.add(col);
    		col = new ExportCol("", "完成日");
    		col.setWidth(12);
    		col.setPropertyHelper(new IPropertyHelper() {
    			
    			@Override
    			public Object getProperty(String propertyName, Object bean) throws Exception {
    				Date date = ((WxTaskMainNew)bean).getTaskFinishTime();
    				if(date == null) {
    					return "";
    				}
    				return DateUtil.getDay(date);
    			}
    		});
    		exportCols.add(col);
    		col = new ExportCol("createUserName", "创建人", WxTaskMainNew.class);
    		col.setWidth(20);
    		exportCols.add(col);
    		col = new ExportCol("", "任务状态");
    		col.setWidth(15);
    		col.setPropertyHelper(new IPropertyHelper() {
    			
    			@Override
    			public Object getProperty(String propertyName, Object bean) throws Exception {
    				return TASK_NEW_STATUS_MAP.get(((WxTaskMainNew)bean).getTaskNewStatus());
    			}
    		});
    		exportCols.add(col);
    		final WxTUser loginUser = ContextUtil.getCurUser();
    		mainTaskQueryConditions.setLoginUser(loginUser);
    		new AsynExcelExportHelper<List<?>>(resultMap, loginUser.getUserId(), exportCols) {
    			@Override
    			protected List<?> execute(AsynProcessStatus processStatus) throws WxPltException {
					return appTaskBizService.queryTaskByTypeAndBrand(mainTaskQueryConditions, loginUser);
    			}
			};
        	log.info("exportTaskByTypeAndBrand ");
        } catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.task.controller.TaskServController.exportTaskByTypeAndBrand", p);
        }
        return resultMap;
    }
	
	@ResponseBody
	@RequestMapping(value = "/queryTaskAttForXD.do", method = { RequestMethod.POST })
	public Map<String, Object> queryTaskAttForXD(
			MainTaskForAttFileQueryConditions mainTaskForAttFileConditions, HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			String page = request.getParameter("page");
			String rows =  request.getParameter("rows");
			mainTaskForAttFileConditions.setLimit(Integer.parseInt(rows));
			mainTaskForAttFileConditions.setStart((Integer.parseInt(page)-1)*Integer.parseInt(rows));
			
			resultMap = taskService
					.getMainTaskAttFileForXD(mainTaskForAttFileConditions);

		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put("codeMsg", WxAuthException.System_Exception_msg);
			log.error("error", e);
		}
		return resultMap;
	}
	
	
	public static TreeMap<File, LinkedList<File>> dirFiles = new TreeMap<File, LinkedList<File>>();
	@ResponseBody
	@RequestMapping(value="/downloadTaskImgPdf.do",method={RequestMethod.GET,RequestMethod.POST})
	public Object taskManage(@RequestParam("partnerId")String partnerId,@RequestParam("startTime")String startTime,
			@RequestParam("endTime")String endTime,HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		try {
			//0.先查询attId对应的图片,组装图片路径
			Long partnerid = 0L;
			if(null==partnerId || partnerId.isEmpty())
			{
				partnerid = ContextUtil.getCurUser().getOrgId();
			}else
			{
				partnerid = Long.parseLong(partnerId);
			}
			reqMap.put("partnerId", partnerid);
			reqMap.put("startTime", startTime);
			reqMap.put("endTime", endTime);
			List<WxTaskForXDAttFile> lstMainTaskForXDAttFile = new ArrayList<WxTaskForXDAttFile>();
			lstMainTaskForXDAttFile = taskMainMapper.getExportAttForXD(reqMap);
			//1.根据图片路径，生成图片pdf文档，存放到对应月份目录下，以合伙人_起止时间_当前时间为文件名
			String fileSep = System.getProperty("file.separator");
			String realPath = FileManager.fileUploadPath;
			String  filePath = "";
			List<String> lstFileNames = new ArrayList<String>();
			for(WxTaskForXDAttFile taskForXDAttFile:lstMainTaskForXDAttFile)
			{
				filePath = realPath+taskForXDAttFile.getStoragePath()+fileSep+taskForXDAttFile.getStorageName();
				lstFileNames.add(filePath);
			}
			if(lstFileNames.isEmpty())
			{
				resultMap.put("warning", "没有要导出的图片");
				return resultMap;
			}
			OrganizationVo org = orgMapper.selectByPrimaryKey(partnerid);
			String pdfName = FileManager.fileUploadPath+org.getOrganizationName()+"_"+DateUtil.getCurrentDate("yyyy-MM-dd")+".pdf";
			String downpdfName = org.getOrganizationName()+"_"+DateUtil.getCurrentDate("yyyy-MM-dd")+".pdf";
			createPdf(pdfName,lstFileNames);
			//2.下载存放的文件
			FileUtil.download(response, "3", pdfName, downpdfName);
			resultMap.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			 log.error("异常错误：", e);
		}	
		return resultMap;
	}
	
	public static List<String> createListFileNames()
	{
		File dirFile = new File(
				"C:\\data\\chevron\\uploadfile\\2017-05\\安徽迈迪汽车部件有限公司\\");
		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!dirFile.exists() || !dirFile.isDirectory()) {
			return null;
		}
		
		getDirectoryFiles(dirFile);
        List<String> fileNames = new ArrayList<String>();
        Iterator<File> iterator = dirFiles.keySet().iterator();
        while (iterator.hasNext())
        {
            File dir = iterator.next();
            LinkedList<File> fileInDir = dirFiles.get(dir);
            if (fileInDir != null)
            {
                Iterator<File> it = fileInDir.iterator();
                while (it.hasNext())
                {
                	fileNames.add(it.next().getAbsolutePath());
                }
            }
        }
        return fileNames;
		
	}
	
	
	
	
	public static void createPdf(String pdfName,List<String> sourceFilePaths ) throws MalformedURLException, IOException
	{
		// 创建一个文档对象
		Rectangle pageSize = new Rectangle(PageSize.A3);
		pageSize.rotate();
		Document doc = new Document(pageSize);
		try {
			// 定义输出位置并把文档对象装入输出对象中
			PdfWriter pa =  PdfWriter.getInstance(doc, new FileOutputStream(pdfName));
			// 打开文档对象
			//pa.setViewerPreferences(PdfWriter.PageLayoutTwoColumnLeft);
			doc.open();
			float width=doc.getPageSize().getWidth()-75;//取页面宽度并减去页边距  

			// 设置中文字体
			BaseFont bfChinese = BaseFont.createFont("STSong-Light",
					"UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
			Font FontChinese = new Font(bfChinese, 18, Font.NORMAL);
			FontChinese.setColor(Color.RED);
			
			// 加入文字“”
			PdfPTable table = new PdfPTable(2);
	        table.setWidthPercentage(88); 
			
	        int sum = 0;
	        //Image lastJpg = null;
            for(String fileName:sourceFilePaths)
            {
            	File file = new File(fileName);
            	if(null==file||!file.exists())
            	{
            		System.out.println("file is not exists");
            		continue;
            	}
            	System.out.println("file is found");
            	if(!file.isDirectory())
            	{
            		if((file.getAbsolutePath().toLowerCase().endsWith(".jpg")||file.getAbsolutePath().toUpperCase().endsWith(".JPG")) && !file.getName().startsWith("thumbnail_"))
	                {
            			Image jpg = Image.getInstance(file.getAbsolutePath());
            			    //图片填充方式
    					/*jpg.setAlignment(Image.ALIGN_CENTER);
    					jpg.setBorderWidthBottom(10);*/
            			if(jpg.getWidth()>width){  
            				float f = width/jpg.getWidth()*65;
            				if(f<=30 || f<41)
            				{
            					jpg.scalePercent(width/jpg.getWidth()*65);
            				}
            				
            				if(f>=41)
            				{
            					jpg.scalePercent(width/jpg.getWidth()*80);
            				}
            				
            				if(f>=50)
            				{
            					jpg.setRotation(-45);//旋转 弧度
            					jpg.setRotationDegrees(-45);//旋转 角度
            					jpg.scalePercent(width/jpg.getWidth()*55);
            				}
                        }
            			if(jpg.getWidth()<jpg.getHeight())
            			{
            				jpg.setRotation(-90);//旋转 弧度
        					jpg.setRotationDegrees(-90);//旋转 角度
            			}
            			jpg.scaleAbsoluteWidth(200);
            			jpg.scaleAbsoluteHeight(200);
                		table.addCell(jpg);
                		//lastJpg = jpg;
                		sum=sum+1;
    					//doc.add(jpg);
    					//doc.add(spiltStr);
	                }
            	}
            }
            if(sum%2!=0)
            {
            	String str = "    ";
    			Paragraph spiltStr = new Paragraph(str, FontChinese);
    			spiltStr.setAlignment(Image.ALIGN_CENTER);
    			table.addCell(spiltStr);
            }
            
            
            doc.add(table);
            //if(sum%2==0)
            {
            	String str = "总共门店数："+sum+" 家";
    			Paragraph spiltStr = new Paragraph(str, FontChinese);
    			spiltStr.setAlignment(Image.ALIGN_CENTER);
    			doc.add(spiltStr);
            }
            
            // 关闭文档对象，释放资源
			doc.close();

		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		System.out.println("OK");
		
	}
	

	public static void getDirectoryFiles(File dir) {
		if (!dir.isDirectory()) {
			return;
		}
		LinkedList<File> files = new LinkedList<File>();
		File[] filesinDir = dir.listFiles();
		if (filesinDir.length > 0) {
			for (int i = 0; i < filesinDir.length; i++) {
				files.add(filesinDir[i]);
			}
		} else {
			dirFiles.put(dir, null);
			return;
		}
		dirFiles.put(dir, files);
		for (int i = 0; i < filesinDir.length; i++) {
			if (filesinDir[i].isDirectory()) {
				getDirectoryFiles(filesinDir[i]);
			}
		}

	}

	@RequestMapping("/exportFleetHighPerPdf.do")
    public String exportFleetPerHighPDF(@RequestParam("taskSubId") Long taskSubId, HttpServletRequest request, HttpServletResponse response){
	    try {
            Map<String, Object> data = taskMainService.exportFleetPerHignPDF(taskSubId);
            String fileName = data.get("fleetName") + "乘用车业绩证明设计文件" + DateUtil.getCurrentDate(DateUtil.DATA_TIME_PATTERN_NO_CHAR) + ".pdf";
            PDFUtil.setFileDownloadHeader(response,fileName,".pdf" );
            PDFUtil.createPDF(request, response, TaskMainService.CDYJZM_PROVE_HIGH_PDF,data,"");
            return null;
        }catch (Exception e){
            log.error("导出文件异常:", e);
            request.setAttribute("errorMsg", "导出文件异常!");
            return "forward:/common/jsp/downloadError.jsp";
        }
    }
	
}
