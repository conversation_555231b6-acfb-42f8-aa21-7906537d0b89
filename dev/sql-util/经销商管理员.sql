select o.organization_name "经销商名称", u.ch_name "用户姓名", u.login_name "用户登录名", u.email, u.mobile_tel "手机号", 
max(case when ad.rank=1 then ad.CONTACTS else '' end) "经销商联系人1",
max(case when ad.rank=1 then ad.CONTACT_NUMBER else '' end) "经销商联系电话1", 
max(case when ad.rank=1 then ad.address else '' end) "经销商联系地址1", 
max(case when ad.rank=2 then ad.CONTACTS else '' end) "经销商联系人2",
max(case when ad.rank=2 then ad.CONTACT_NUMBER else '' end) "经销商联系电话2", 
max(case when ad.rank=2 then ad.address else '' end) "经销商联系地址2"  from wx_t_user u
left join wx_t_organization o on o.id=u.org_id
left join (select ROW_NUMBER() OVER (PARTITION BY ac.partner_id ORDER BY ac.id DESC) AS rank,
ac.PARTNER_ID, case when ac.ADDRESS_REGION is null then '' else r3.region_name + ' ' + r2.region_name + ' '
 + r1.region_name + ' ' + ac.ADDRESS_DETAIL end address, ac.CONTACTS, ac.CONTACT_NUMBER
from wx_t_material_address_config ac 
left join wx_t_region r1 on ac.ADDRESS_REGION=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id) ad on ad.partner_id=o.id
where u.status=1 and o.type=1 and o.id<>9
and exists (select 1 from wx_t_userrole ur
left join wx_t_role r on r.role_id=ur.role_id where u.user_id=ur.user_id
and r.ch_role_name in ('Service_Partner_Manager', 'CDM_DIST', 'Caltex_Dealer'))
group by o.id, o.organization_name, u.ch_name, u.login_name, u.email, u.mobile_tel, u.user_id
order by o.id, u.user_id desc