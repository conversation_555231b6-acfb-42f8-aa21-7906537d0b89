{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/resources/utils/tools/download.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/list.js", "webpack:///./src/resources/xhr/config.js", "webpack:///./src/resources/xhr/axios.js", "webpack:///./src/resources/xhr/index.js", "webpack:///./src/resources/utils/tools/download-async.js", "webpack:///./src/resources/service/core.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/local-signage.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/local-seminar.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/local-ck.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/request-signage.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/request-seminar.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_values/request-ck.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-ck-local.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-request-att.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-ck-request.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-signage-local.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-signage-request.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-seminar-local.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/_func/to-seminar-request.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/apply/index.js", "webpack:///./src/projects/market/resource-application-2021/resources/service/apply.js", "webpack:///./src/projects/market/resource-application-2021/resources/service/common.js", "webpack:///./src/components/select/dealer/dealer-by-sales/register.conf.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/dialog.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/global.js", "webpack:///./src/projects/market/resource-application-2021/stores.conf.js", "webpack:///./src/resources/utils/xhr/config.js", "webpack:///./src/resources/utils/xhr/axios.js", "webpack:///./src/resources/utils/xhr/index.js", "webpack:///./src/components sync \\/register\\.conf\\.js$", "webpack:///./src/components/select/options/register.conf.js", "webpack:///./src/components/select/region/region-by-resourceId/register.conf.js", "webpack:///./src/projects/market/resource-application-2021/app.vue?109b", "webpack:///src/projects/market/resource-application-2021/app.vue", "webpack:///./src/projects/market/resource-application-2021/app.vue?5bf6", "webpack:///./src/projects/market/resource-application-2021/app.vue", "webpack:///./src/resources/router/hooks/afterEach/doc-title-replacer.js", "webpack:///./src/resources/router/hooks/index.js", "webpack:///./src/resources/router/index.js", "webpack:///./src/resources/store/modules/dict-options.js", "webpack:///./src/resources/store/modules/permission.js", "webpack:///./src/resources/store/modules/user.js", "webpack:///./src/resources/store/index.js", "webpack:///./src/resources/add-on/math/index.js", "webpack:///./src/resources/add-on/elements/index.js", "webpack:///./src/resources/filters/_func/money.js", "webpack:///./src/resources/filters/index.js", "webpack:///./src/resources/add-on/index.js", "webpack:///./src/projects/market/resource-application-2021/resources/filter/index.js", "webpack:///./src/projects/market/resource-application-2021/main.js", "webpack:///./src/components/select/dealer/dealer-by-resourceId/register.conf.js", "webpack:///./src/components/select/user/user-by-resourceId/register.conf.js", "webpack:///./src/components/select/brand/brand-by-channel/register.conf.js", "webpack:///./src/components/select/dict-options/register.conf.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/products/_values/package.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/products/_values/delo.js", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/products/index.js", "webpack:///./src/components/customize/files/register.conf.js", "webpack:///./src/resources/utils/dialog/notify/index.js", "webpack:///./src/components/select/dealer/retailer-by-distributor/register.conf.js", "webpack:///./src/projects/market/resource-application-2021/routes.conf.js", "webpack:///./src/projects/market/resource-application-2021/app.vue?ab4c", "webpack:///./src/projects/market/resource-application-2021/resources/storeModules/ck_products/index.js", "webpack:///./src/components/select/number/register.conf.js", "webpack:///./src/components/customize/popconfirm/register.conf.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "installedCssChunks", "jsonpScriptSrc", "p", "exports", "module", "l", "e", "promises", "cssChunks", "Promise", "resolve", "reject", "href", "fullhref", "existingLinkTags", "document", "getElementsByTagName", "tag", "dataHref", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onload", "onerror", "event", "request", "target", "src", "err", "Error", "code", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "error", "clearTimeout", "chunk", "errorType", "realSrc", "message", "name", "undefined", "setTimeout", "all", "m", "c", "d", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "window", "oldJsonpFunction", "slice", "path", "params", "options", "loading", "vue", "$loading", "lock", "text", "spinner", "background", "form", "qs", "stringify", "test", "method", "body", "toString", "map", "item", "index", "input", "parseInt", "submit", "close", "state", "done", "searchParams", "brand", "region", "storeName", "dealerId", "applyType", "date<PERSON><PERSON><PERSON>", "pageParams", "limit", "page", "total", "status", "observer", "permissionWeight", "getters", "hasAuthInList", "payload", "isNaN", "mutations", "actions", "getListByDone", "R", "merge", "executor", "salesChannel", "applyService", "getApplyList", "res", "resultLst", "getListByObserver", "getListPermission", "moduleCode", "weight", "GoL<PERSON>in", "top", "location", "StatusErrorHandler", "log", "notify", "title", "duration", "errorMsg", "HTMLContentTypeHandler", "indexOf", "responseURL", "axios", "defaults", "withCredentials", "contentType", "assign", "Math", "random", "contentTypeString", "url", "baseURL", "headers", "Accept", "xhr", "downloadAsync", "attributes", "this", "Loading", "service", "clone", "filePath", "fileName", "run", "getFile<PERSON>ey", "checkProcess", "updateLoading", "closeLoading", "progressStatus", "math", "attrs", "downloadFile", "Notification", "download", "deleteFile", "requestByRPC", "info", "jsonrpc", "id", "requestByDO", "reqNo", "dealerName", "includeDMS", "partnerId", "costCenter", "companyCode", "storeId", "storeCooperationyear", "storeProvince", "storeCity", "storeRegion", "storeAddress", "storeContact", "storeContacts", "storeType", "storeCubicle", "storeWorkshopId", "storeCustomerType", "localMake", "signboardMaterial", "signboardStyleFirst", "signboardStyleSecond", "signboardHeight", "signboardWidth", "signboardArea", "signboardQuote", "signboardDoorQuote", "signboardDecorationQuote", "signboardRequirement", "signboardSupplierId", "otherApplyReason", "otherCompleteAmount", "otherSupplierConcact", "otherSupplierConcacts", "completeTime", "attAppliedVehicle", "attCarQrcode", "attOriginalSignboard", "attOriginalOutdoorAdSign", "attQuotation", "attStampedQuotation", "attDesign", "attApplyForm", "attARIBAOrder", "attInvoice", "attConfirmProof", "attTripleAgreement", "attPaymentProof", "attCompletion", "attCarAdQuali", "attInvoiceConfirm", "createTime", "Date", "bizPermissionWeight", "stepCode", "unitPriceLimit", "<PERSON><PERSON><PERSON><PERSON>", "versionNo", "abortOperationName", "rejectOperationName", "acceptOperationName", "recallOperationName", "workflowInstance", "retailerId", "retailerName", "supplierId", "conferenceEstimatedDate", "conferenceAddress", "conferenceApplicationFee", "conferenceEstimatedPurchaseVolume", "conferenceNumberOfPeople", "conferencePlace", "conferenceQuote", "conferenceActualPurchaseVolume", "attInvoiceCheck", "attMeetingFlow", "attMeetingLocal", "attMeetingSign", "quoteLimit", "organizationName", "oldData", "storeInfo", "applyAttFiles", "signType", "distributorId", "distributorName", "customerType", "budgetAmount", "settlementAmount", "productInfo", "storeSignInfo", "remark", "quoteAmount", "applyBaseInfo", "source", "JSON", "parse", "attId", "fileType", "sourceType", "storageName", "storePath", "remoteUrl", "products", "toRequestAttFormat", "salesInfo", "round", "signage", "LocalSignage", "seminar", "LocalSeminar", "ck", "LocalCk", "applyForm", "hasAuthInBiz", "bit", "CLEAR_APPLY_FORM", "SET_STORE_INFO", "workshopName", "workshopAddress", "<PERSON><PERSON><PERSON>", "contactPersonTel", "provinceName", "cityName", "seatsNum", "regionName", "SET_CK_STORE_INFO", "getApplyFormById", "dispatch", "toSignageLocal", "currentStep", "toCkLocal", "toSeminarLocal", "savaApplyForm", "sales", "toSignageRequest", "RequestSignage", "toCkRequest", "RequestCk", "toSeminarRequest", "RequestSeminar", "operationApplyForm", "ckProducts", "abortApplyForm", "rootState", "list", "find", "set", "deleteApplyForm", "todo", "filter", "exportPDF", "exportExcel", "packageName", "getFundDetail", "getCostCenter", "convertForm", "Service", "startDate", "dayjs", "format", "endDate", "start", "field", "direction", "startApplyTime", "endApplyTime", "viewName", "columnInfoDictKey", "pageType", "mktType", "mktId", "url<PERSON><PERSON>", "replace", "partner<PERSON>ame", "resourceId", "pageSize", "queryType", "keyword", "funFlag", "fromSource", "businessWeight", "mktKey", "oldMktKey", "pageIndex", "global", "store", "show", "loadingText", "signboardStyle", "appliedVehicle", "SHOW_DIALOG", "dialogName", "CLEAR_STORE_LIST", "HIDE_DIALOG", "getStoreByDealerId", "commonService", "previewForm", "SET_GLOBAL", "require", "default", "dialog", "webpackContext", "req", "webpackContextResolve", "keys", "_vm", "_h", "$createElement", "_c", "_self", "staticRenderFns", "watch", "$route", "to", "query", "globalConfig", "$store", "commit", "component", "titles", "matched", "for<PERSON>ach", "handler", "meta", "join", "router", "after<PERSON>ach", "docTitleReplacer", "<PERSON><PERSON>", "use", "Router", "routes", "hooks", "timeInterval", "getOptions", "dictName", "getOptionsData", "_", "UPDATE_OPTIONS", "updateTime", "getTime", "getDictOptions", "coreService", "dicItemCode", "label", "dicItemName", "permissions", "getPermission", "permissionName", "permission", "getPermissionData", "hasPermission", "hasPermissionNotAdmin", "UPDATE_PERMISSION", "getOperationPermissionByUser", "currentUser", "getCurrentUser", "UPDATE_CURRENT_USER", "getCurrentUserInfo", "Vuex", "Store", "dictOptions", "user", "config", "ElementUI", "decimal", "thousands", "prefix", "suffix", "precision", "numberToThousand", "number", "split", "val", "Number", "fmt", "option", "RegisterConfs", "addOn", "stores", "addRoutes", "registerModule", "$bus", "$router", "conf", "componentPath", "1", "2", "3", "productionTip", "render", "h", "app", "$mount", "categoryName", "estimatedPack", "actualPack", "category", "tableData", "initProducts", "packageProducts", "deloProducts", "getProductsSales", "totalActualPack", "tableItem", "dataItem", "productCategory", "rolling12MonthSellIn", "add", "bignumber", "valueOf", "success", "warning", "initCkProducts"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAGnBC,EAAqB,CACxB,4BAA6B,GAM1BjB,EAAkB,CACrB,4BAA6B,GAG1BK,EAAkB,GAGtB,SAASa,EAAe7B,GACvB,OAAOyB,EAAoBK,EAAI,OAAS,GAAG9B,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,MAI5W,SAASyB,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAUgC,QAGnC,IAAIC,EAASL,EAAiB5B,GAAY,CACzCK,EAAGL,EACHkC,GAAG,EACHF,QAAS,IAUV,OANAlB,EAAQd,GAAUW,KAAKsB,EAAOD,QAASC,EAAQA,EAAOD,QAASN,GAG/DO,EAAOC,GAAI,EAGJD,EAAOD,QAKfN,EAAoBS,EAAI,SAAuBlC,GAC9C,IAAImC,EAAW,GAIXC,EAAY,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,GACxFR,EAAmB5B,GAAUmC,EAASvB,KAAKgB,EAAmB5B,IACzB,IAAhC4B,EAAmB5B,IAAkBoC,EAAUpC,IACtDmC,EAASvB,KAAKgB,EAAmB5B,GAAW,IAAIqC,QAAQ,SAASC,EAASC,GAIzE,IAHA,IAAIC,EAAO,QAAU,GAAGxC,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,OACpVyC,EAAWhB,EAAoBK,EAAIU,EACnCE,EAAmBC,SAASC,qBAAqB,QAC7CxC,EAAI,EAAGA,EAAIsC,EAAiBpC,OAAQF,IAAK,CAChD,IAAIyC,EAAMH,EAAiBtC,GACvB0C,EAAWD,EAAIE,aAAa,cAAgBF,EAAIE,aAAa,QACjE,GAAe,eAAZF,EAAIG,MAAyBF,IAAaN,GAAQM,IAAaL,GAAW,OAAOH,IAErF,IAAIW,EAAoBN,SAASC,qBAAqB,SACtD,IAAQxC,EAAI,EAAGA,EAAI6C,EAAkB3C,OAAQF,IAAK,CAC7CyC,EAAMI,EAAkB7C,GACxB0C,EAAWD,EAAIE,aAAa,aAChC,GAAGD,IAAaN,GAAQM,IAAaL,EAAU,OAAOH,IAEvD,IAAIY,EAAUP,SAASQ,cAAc,QACrCD,EAAQF,IAAM,aACdE,EAAQE,KAAO,WACfF,EAAQG,OAASf,EACjBY,EAAQI,QAAU,SAASC,GAC1B,IAAIC,EAAUD,GAASA,EAAME,QAAUF,EAAME,OAAOC,KAAOjB,EACvDkB,EAAM,IAAIC,MAAM,qBAAuB5D,EAAU,cAAgBwD,EAAU,KAC/EG,EAAIE,KAAO,wBACXF,EAAIH,QAAUA,SACP5B,EAAmB5B,GAC1BkD,EAAQY,WAAWC,YAAYb,GAC/BX,EAAOoB,IAERT,EAAQV,KAAOC,EAEf,IAAIuB,EAAOrB,SAASC,qBAAqB,QAAQ,GACjDoB,EAAKC,YAAYf,KACfgB,KAAK,WACPtC,EAAmB5B,GAAW,KAMhC,IAAImE,EAAqBxD,EAAgBX,GACzC,GAA0B,IAAvBmE,EAGF,GAAGA,EACFhC,EAASvB,KAAKuD,EAAmB,QAC3B,CAEN,IAAIC,EAAU,IAAI/B,QAAQ,SAASC,EAASC,GAC3C4B,EAAqBxD,EAAgBX,GAAW,CAACsC,EAASC,KAE3DJ,EAASvB,KAAKuD,EAAmB,GAAKC,GAGtC,IACIC,EADAC,EAAS3B,SAASQ,cAAc,UAGpCmB,EAAOC,QAAU,QACjBD,EAAOE,QAAU,IACb/C,EAAoBgD,IACvBH,EAAOI,aAAa,QAASjD,EAAoBgD,IAElDH,EAAOZ,IAAM7B,EAAe7B,GAG5B,IAAI2E,EAAQ,IAAIf,MAChBS,EAAmB,SAAUd,GAE5Be,EAAOhB,QAAUgB,EAAOjB,OAAS,KACjCuB,aAAaJ,GACb,IAAIK,EAAQlE,EAAgBX,GAC5B,GAAa,IAAV6E,EAAa,CACf,GAAGA,EAAO,CACT,IAAIC,EAAYvB,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChE2B,EAAUxB,GAASA,EAAME,QAAUF,EAAME,OAAOC,IACpDiB,EAAMK,QAAU,iBAAmBhF,EAAU,cAAgB8E,EAAY,KAAOC,EAAU,IAC1FJ,EAAMM,KAAO,iBACbN,EAAMvB,KAAO0B,EACbH,EAAMnB,QAAUuB,EAChBF,EAAM,GAAGF,GAEVhE,EAAgBX,QAAWkF,IAG7B,IAAIV,EAAUW,WAAW,WACxBd,EAAiB,CAAEjB,KAAM,UAAWK,OAAQa,KAC1C,MACHA,EAAOhB,QAAUgB,EAAOjB,OAASgB,EACjC1B,SAASqB,KAAKC,YAAYK,GAG5B,OAAOjC,QAAQ+C,IAAIjD,IAIpBV,EAAoB4D,EAAIxE,EAGxBY,EAAoB6D,EAAI3D,EAGxBF,EAAoB8D,EAAI,SAASxD,EAASkD,EAAMO,GAC3C/D,EAAoBgE,EAAE1D,EAASkD,IAClC1E,OAAOmF,eAAe3D,EAASkD,EAAM,CAAEU,YAAY,EAAMC,IAAKJ,KAKhE/D,EAAoBoE,EAAI,SAAS9D,GACX,qBAAX+D,QAA0BA,OAAOC,aAC1CxF,OAAOmF,eAAe3D,EAAS+D,OAAOC,YAAa,CAAEC,MAAO,WAE7DzF,OAAOmF,eAAe3D,EAAS,aAAc,CAAEiE,OAAO,KAQvDvE,EAAoBwE,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQvE,EAAoBuE,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAK7F,OAAO8F,OAAO,MAGvB,GAFA5E,EAAoBoE,EAAEO,GACtB7F,OAAOmF,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOvE,EAAoB8D,EAAEa,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIR3E,EAAoB+E,EAAI,SAASxE,GAChC,IAAIwD,EAASxD,GAAUA,EAAOmE,WAC7B,WAAwB,OAAOnE,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAP,EAAoB8D,EAAEC,EAAQ,IAAKA,GAC5BA,GAIR/D,EAAoBgE,EAAI,SAASgB,EAAQC,GAAY,OAAOnG,OAAOC,UAAUC,eAAeC,KAAK+F,EAAQC,IAGzGjF,EAAoBK,EAAI,GAGxBL,EAAoBkF,GAAK,SAAShD,GAA2B,MAApBiD,QAAQjC,MAAMhB,GAAYA,GAEnE,IAAIkD,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAWjG,KAAK2F,KAAKM,GAC5CA,EAAWjG,KAAOf,EAClBgH,EAAaA,EAAWG,QACxB,IAAI,IAAI5G,EAAI,EAAGA,EAAIyG,EAAWvG,OAAQF,IAAKP,EAAqBgH,EAAWzG,IAC3E,IAAIU,EAAsBiG,EAI1B/F,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,sICvQM,kBAAoD,6DAAP,GAA1C+F,EAAiD,EAAjDA,KAAiD,IAA3CC,cAA2C,MAAlC,GAAkC,EAA9BpH,EAA8B,EAA9BA,KAA8B,IAAxBqH,eAAwB,MAAd,GAAc,EAC7DC,EAAUC,aAAIC,SAAS,CACzBC,MAAM,EACNC,KAAM,OACNC,QAAS,kBACTC,WAAY,uBAGVC,EAAOhF,SAASQ,cAAc,QAKlC+D,EAASU,IAAGC,UAAUX,GAClBA,IAEAD,EADE,MAAMa,KAAKb,GACN,GAAH,OAAMA,EAAN,YAAcC,GAEX,GAAH,OAAMD,EAAN,YAAcC,IAGtBS,EAAKjD,aAAa,SAAUuC,GAE5BU,EAAKjD,aAAa,KAAM,gBACxBiD,EAAKjD,aAAa,QAAS,kBAC3BiD,EAAKjD,aAAa,OAAQ,gBAC1BiD,EAAKjD,aAAa,SAAUyC,EAAQY,QAAU,QAC9CJ,EAAKjD,aAAa,SAAUyC,EAAQ1D,QAAU,UAE9Cd,SAASqF,KAAK/D,YAAY0D,GA7BuC,eA+BxD1C,GACP,GAAkD,kBAA9C1E,OAAOC,UAAUyH,SAASvH,KAAKZ,EAAKmF,IAEtCnF,EAAKmF,GAAMiD,IAAI,SAACC,EAAMC,GACpB,IAAIC,EAAQ1F,SAASQ,cAAc,SACnCkF,EAAM3D,aAAa,OAAQ,QAC3B2D,EAAM3D,aAAa,OAAnB,UAA8BO,IAC9BoD,EAAM3D,aAAa,QAAS4D,SAASH,IACrCR,EAAK1D,YAAYoE,SAEd,CACL,GAAmB,OAAfvI,EAAKmF,GAAgB,iBACzB,IAAIoD,EAAQ1F,SAASQ,cAAc,SACnCkF,EAAM3D,aAAa,OAAQ,QAC3B2D,EAAM3D,aAAa,OAAQO,GAC3BoD,EAAM3D,aAAa,QAAS5E,EAAKmF,IACjC0C,EAAK1D,YAAYoE,KAhBrB,IAAK,IAAIpD,KAAQnF,EAAM,EAAdmF,GAmBT0C,EAAKY,SACL5F,SAASqF,KAAKjE,YAAY4D,GAE1BP,EAAQoB,U,+FCtDJC,EAAQ,CACZC,KAAM,CACJC,aAAc,CACZC,MAAO,GACPC,OAAQ,GACRC,UAAW,GACXC,SAAU,GACVC,UAAW,GACXC,UAAW,IAEbC,WAAY,CACVC,MAAO,GACPC,KAAM,EACNC,MAAO,GAETC,OAAQ,CACNlC,SAAS,GAEXtH,KAAM,IAERyJ,SAAU,CACRZ,aAAc,CACZC,MAAO,GACPC,OAAQ,GACRC,UAAW,GACXC,SAAU,GACVC,UAAW,GACXC,UAAW,IAEbC,WAAY,CACVC,MAAO,GACPC,KAAM,EACNC,MAAO,GAETC,OAAQ,CACNlC,SAAS,GAEXtH,KAAM,IAER0J,iBAAkB,GAGdC,EAAU,CACdC,cADc,SACAjB,GACZ,OAAO,SAACkB,GACN,SACEA,IACCC,MAAMD,EAAU,IACblB,EAAMe,kBAAqBG,EAAU,EAAM,MAMjDE,EAAY,GAEZC,EAAU,CACRC,cADQ,2KACQtB,EADR,EACQA,MAAOgB,EADf,EACeA,SACvBhB,EAAMC,KAAKY,OAAOlC,QAFV,0CAE0B,GAF1B,cAGNF,EAAS8C,EAAEC,MACf,CACElC,OAAQ,WACRmC,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,cAExBH,EAAEC,MAAMxB,EAAMC,KAAKC,aAAcF,EAAMC,KAAKQ,aAG9CT,EAAMC,KAAKY,OAAOlC,SAAU,EAZhB,SAagBgD,OAAaC,aAAanD,GAb1C,6CAaLoC,EAbK,KAaGgB,EAbH,KAcZ7B,EAAMC,KAAKY,OAAOlC,SAAU,EAExBkC,IACFb,EAAMC,KAAK5I,KAAOwK,EAAIC,UACtB9B,EAAMC,KAAKQ,WAAWG,MAAQiB,EAAIjB,OAlBxB,kBAoBL,CAACC,EAAQgB,IApBJ,mGAsBRE,kBAtBQ,2KAsBY/B,EAtBZ,EAsBYA,MAAOgB,EAtBnB,EAsBmBA,SAC3BhB,EAAMc,SAASD,OAAOlC,QAvBd,0CAuB8B,GAvB9B,cAwBNF,EAAS8C,EAAEC,MACf,CACElC,OAAQ,UACRmC,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,cAExBH,EAAEC,MAAMxB,EAAMc,SAASZ,aAAcF,EAAMc,SAASL,aAGtDT,EAAMc,SAASD,OAAOlC,SAAU,EAjCpB,SAkCgBgD,OAAaC,aAAanD,GAlC1C,6CAkCLoC,EAlCK,KAkCGgB,EAlCH,KAmCZ7B,EAAMc,SAASD,OAAOlC,SAAU,EAE5BkC,IACFb,EAAMc,SAASzJ,KAAOwK,EAAIC,UAC1B9B,EAAMc,SAASL,WAAWG,MAAQiB,EAAIjB,OAvC5B,kBAyCL,CAACC,EAAQgB,IAzCJ,mGA2CRG,kBA3CQ,+KA2CYhC,EA3CZ,EA2CYA,MAAOgB,EA3CnB,EA2CmBA,QACzBvC,EAAS,CACbgD,SAAUT,EAAQS,SAClBQ,WAAY,GAAF,OAA8B,YAAzBjB,EAAQU,aAA6B,UAAqC,OAAzBV,EAAQU,aAAuB,KAAK,UAA1F,WA9CA,SAgDgBC,OAAaK,kBAAkBvD,GAhD/C,6CAgDLoC,EAhDK,KAgDGgB,EAhDH,KAiDRhB,IACFb,EAAMe,iBAAmBc,EAAInJ,OAAOwJ,QAlD1B,kBAoDL,CAACrB,EAAQgB,IApDJ,oGAwDD,cACb7B,QACAoB,YACAC,UACAL,Y,gIClHWmB,EAAO,wDAAG,kKAOZC,MAAQA,IAAIC,SAAW,eAPX,yCAAH,qDAWPC,EAAkB,wDAAG,WAAO7I,GAAP,yFAChC0E,QAAQoE,IAAI9I,GACNpC,EAAOoC,EAAEpC,KACVoC,EAAEpC,KAHyB,gBAKZ,wCAAdoC,EAAE8C,QACJiG,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAAS,qBAGXiG,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAAS,2BAfiB,0BAkBP,cAAdlF,EAAK+D,KAlBgB,iBAmB9BoH,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAASlF,EAAKsL,WAtBc,4BAwBrBtL,EAAK6E,OAA6B,IAApB7E,EAAK6E,MAAMd,KAxBJ,iBAyB9BoH,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAAS,2BA5BmB,4BA8BrBlF,EAAKqB,QAA+B,YAArBrB,EAAKqB,OAAO0C,KA9BN,oBA+BL,iBAArB/D,EAAKqB,OAAO0C,KA/Bc,0CAgCrB+G,KAhCqB,iCAmCzB,EAAC,IAnCwB,0CAAH,sDAuClBS,EAAsB,wDAAG,WAAOnJ,GAAP,qFAEhCA,EAAEpC,KAAKwL,QAAQ,sBAAwB,GAFP,yCAG3BV,KAH2B,YAMhC1I,EAAEsB,QAAQ+H,YAAYD,QAAQ,aAAe,GANb,yCAO3BV,KAP2B,gCAS7B,EAAC,IAT4B,yCAAH,sDChDjCY,IAAMC,SAASC,iBAAkB,EAGnC,8DAAe,iIAAS3D,cAAT,MAAkB,MAAlB,EAAyBd,EAAzB,EAAyBA,KAAzB,IAA+BC,cAA/B,MAAwC,KAAxC,MAA8CpH,YAA9C,MAAqD,KAArD,EAA2D6L,EAA3D,EAA2DA,YAA3D,SAMXzE,EAAS3G,OAAOqL,OAAO,GAAI1E,EAAQ,CAAErB,EAAGgG,KAAKC,WAC7C5E,EAAoB,QAAXa,EAAmBxH,OAAOqL,OAAO,GAAI1E,EAAQpH,GAAQoH,EAE1D6E,EAAoB,kCAEtBA,EADkB,SAAhBJ,EACkB,kCACK,SAAhBA,EACW,mDAEAA,EAGlB7L,GAAwB,SAAhB6L,GAA0B,iBAAiB7D,KAAKC,KAC1DjI,EAAO8H,IAAGC,UAAU/H,IAnBX,UAsBO0L,IAAM,CACtBzD,OAAQA,EACRiE,IAAK,IAAM/E,EACXgF,QACoF,KACpF/E,OAAQA,EACRgF,QAAS,CACP,eAAgBH,EAChBI,OAAQ,OAEVrM,KAAM,iBAAiBgI,KAAKC,GAAUjI,EAAO,KAhCpC,WAsBLwK,EAtBK,SAmCPA,EAAI4B,QAAQ,gBAAgBZ,QAAQ,cAAgB,GAnC7C,kCAoCHD,EAAuBf,GApCpB,iCAqCF,EAAC,EAAOA,EAAIxK,OArCV,aA0CRwK,EAAIxK,MAAQwK,EAAIxK,KAAK+D,MAAQ,CAAC,UAAW,QAAQyH,QAAQhB,EAAIxK,KAAK+D,MAAQ,GAC1EyG,EAAIxK,MAAQwK,EAAIxK,KAAK6E,OACrB2F,EAAIxK,MACHwK,EAAIxK,KAAKqB,QACTmJ,EAAIxK,KAAKqB,OAAO0C,MAChB,CAAC,UAAW,QAAQyH,QAAQhB,EAAIxK,KAAKqB,OAAO0C,MAAQ,GA/C7C,kCAiDHkH,EAAmBT,GAjDhB,iCAkDF,EAAC,EAAOA,EAAIxK,OAlDV,iCAqDJ,EAAC,EAAMwK,EAAIxK,OArDP,sDAuDLiL,EAAmB,EAAD,IAvDb,iCAwDJ,EAAC,EAAO,KAAEjL,OAxDN,wDAAf,sDCPesM,I,gDCGf,SAASC,IAA8C,IAA/BlF,EAA+B,uDAArB,GAAImF,EAAiB,uDAAJ,GAQjD,OAPAC,KAAKnF,QAAUoF,aAAQC,QAAQ,CAAClF,MAAM,EAAKC,KAAM,aACjD+E,KAAKpF,QAAU6C,EAAE0C,MAAMvF,GACvBoF,KAAKD,WAAaA,EAClBC,KAAKjG,IAAM,GACXiG,KAAKI,SAAW,GAChBJ,KAAKK,SAAW,GAChBL,KAAKM,MACE,EAAC,GAGVR,EAAc7L,UAAY,CAClBqM,IADkB,qKAEhBN,KAAKO,aAFW,uBAGhBP,KAAKQ,eAHW,sGAKxBC,cALwB,SAKTxF,GACb+E,KAAKnF,QAAQI,KAAOA,GAEtByF,aARwB,WAStBV,KAAKnF,QAAQoB,SAETsE,WAXkB,iLAYMV,eAAIG,KAAKpF,SAZf,6CAYfmC,EAZe,KAYPgB,EAZO,KAalBhB,IACFiD,KAAKjG,IAAMgE,EAAI4C,eAAe5G,IAC9BiG,KAAKS,cAAc1C,EAAI4C,eAAelI,UAflB,kBAiBf,CAACsE,EAAQgB,IAjBM,sGAmBlByC,aAnBkB,wLAoBMX,eAAI,CAC9BrE,OAAQ,MACRd,KAAM,4BACN0E,YAAa,OACb7L,KAAM,CAACwG,IAAKiG,KAAKjG,IAAKwF,OAAQqB,KAAKrB,YAxBf,yCAoBfxC,EApBe,KAoBPgB,EApBO,MA0BlBhB,IAAUgB,EAAI4C,gBAAgD,YAA9B5C,EAAI4C,eAAe5D,OA1BjC,wBA2BpBiD,KAAKI,SAAWrC,EAAI4C,eAAeE,MAAMT,UAAYJ,KAAKD,WAAWK,SACrEJ,KAAKK,SAAWtC,EAAI4C,eAAeE,MAAMR,UAAYL,KAAKD,WAAWM,SACrEL,KAAKc,eA7Be,kBA8Bb,EAAC,IA9BY,YA+BX/D,IAAUgB,EAAI4C,gBAAgD,UAA9B5C,EAAI4C,eAAe5D,OA/BxC,wBAgCpBiD,KAAKU,eACLK,kBAAa3I,MAAM,CACjBuG,MAAO,OACPlG,QAASsF,EAAI4C,eAAelI,UAnCV,kBAqCb,EAAC,IArCY,QAuChBsF,EAAI4C,gBAAkB5C,EAAI4C,eAAelI,SAC3CuH,KAAKS,cAAc1C,EAAI4C,eAAelI,SAxCpB,eA2CtBG,WAAW,WACT,EAAK4H,gBACJ,KA7CmB,kBA8Cf,EAAC,IA9Cc,uGAgDxBM,aAhDwB,WAiDtBE,eAAS,CACPtG,KAAM,qBACNnH,KAAM,CACJ6M,SAAUJ,KAAKI,SACfC,SAAUL,KAAKK,SACfY,YAAY,GAEdrG,QAAS,CACP1D,OAAQ,WAGZ8I,KAAKU,iBAIM,eAAC9F,EAASmF,GACvB,OAAO,IAAID,EAAclF,EAASmF,IC7ErB,QACbmB,aADa,YACoB,IAAlB1F,EAAkB,EAAlBA,OAAQb,EAAU,EAAVA,OAErB,OADAN,QAAQ8G,KAAK,iBAAkB,CAAEC,QAAS,MAAOC,GAAI,EAAG7F,SAAQb,WACzDkF,EAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CAAE6N,QAAS,MAAOC,GAAI,EAAG7F,SAAQb,aAI3C2G,YAXa,YAW8D,QAA7D9F,cAA6D,MAApD,OAAoD,EAA5Cd,EAA4C,EAA5CA,KAA4C,IAAtC0E,mBAAsC,MAAxB,OAAwB,EAAhB7L,EAAgB,EAAhBA,KAAMoH,EAAU,EAAVA,OAE/D,OADAN,QAAQ8G,KAAK,eAAgB,CAAE3F,SAAQd,OAAM0E,cAAa7L,OAAMoH,WACzDkF,EAAI,CAAErE,SAAQd,OAAM0E,cAAa7L,OAAMoH,YAGhDqG,SAhBa,YAgB6B,IAA/BtG,EAA+B,EAA/BA,KAAMnH,EAAyB,EAAzBA,KAAMoH,EAAmB,EAAnBA,OAAQC,EAAW,EAAXA,QAE7B,OADAP,QAAQ8G,KAAK,YAAa,CAAEzG,OAAMnH,OAAMoH,SAAQC,YACzCoG,eAAS,CAAEtG,OAAMnH,OAAMoH,SAAQC,aAGxCkF,cArBa,YAqBiF,QAA9EtE,cAA8E,MAArE,OAAqE,EAA7Dd,EAA6D,EAA7DA,KAA6D,IAAvD0E,mBAAuD,MAAzC,OAAyC,EAAjC7L,EAAiC,EAAjCA,KAAMoH,EAA2B,EAA3BA,OAAUoF,EAAiB,uDAAJ,GAExF,OADA1F,QAAQ8G,KAAK,mBAAoB,CAAE3F,SAAQd,OAAM0E,cAAa7L,OAAMoH,UAAUoF,GACvED,EAAc,CAAEtE,SAAQd,OAAM0E,cAAa7L,OAAMoH,UAAUoF,M,2GC3BvD,GACbsB,GAAI,GACJE,MAAO,GACPlF,MAAO,GACPI,UAAW,GACXD,SAAU,KACVgF,WAAY,GACZC,WAAY,EACZC,UAAW,GACXC,WAAY,GACZC,YAAa,GAEbC,QAAS,GACTC,qBAAsB,GACtBvF,UAAW,GACXwF,cAAe,GACfC,UAAW,GACXC,YAAa,GACbC,aAAc,GACdC,aAAc,GACdC,cAAe,GACfC,UAAW,GACXC,aAAc,GACdC,gBAAiB,GACjBC,kBAAmB,GAEnBC,UAAW,GACXC,kBAAmB,GACnBC,oBAAqB,GACrBC,qBAAsB,GACtBC,gBAAiB,GACjBC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,mBAAoB,GACpBC,yBAA0B,GAC1BC,qBAAsB,GACtBC,oBAAqB,GAErBC,iBAAkB,GAClBC,oBAAqB,GACrBC,qBAAsB,GACtBC,sBAAuB,GACvBC,aAAc,GAEdC,kBAAmB,GACnBC,aAAc,GACdC,qBAAsB,GACtBC,yBAA0B,GAC1BC,aAAc,GACdC,oBAAqB,GACrBC,UAAW,GACXC,aAAc,GACdC,cAAe,GACfC,WAAY,GACZC,gBAAiB,GACjBC,mBAAoB,GACpBC,gBAAiB,GACjBC,cAAe,GACfC,cAAe,GACfC,kBAAmB,GAGnBC,WAAY,IAAIC,KAChBC,oBAAqB,IACrBC,SAAU,GACVC,eAAgB,GAChBC,YAAa,GACbC,UAAW,GACXC,mBAAoB,GACpBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBC,iBAAkB,GAElBC,WAAY,GACZC,aAAc,IC5ED,GACblE,GAAI,GACJE,MAAO,GACPlF,MAAO,GACPI,UAAW,UACXD,SAAU,KACVkF,UAAW,GACXF,WAAY,GACZgE,WAAY,GACZ7D,WAAY,GACZC,YAAa,GACba,UAAW,GAEXgD,wBAAyB,GACzBC,kBAAmB,GACnBC,yBAA0B,GAC1BC,kCAAmC,GACnCC,yBAA0B,GAC1BC,gBAAiB,GACjBC,gBAAiB,GACjBC,+BAAgC,GAEhC3C,iBAAkB,GAElBY,aAAc,GACdI,mBAAoB,GACpBF,WAAY,GACZ8B,gBAAiB,GACjBC,eAAgB,GAChBC,gBAAiB,GACjBC,eAAgB,GAChBtC,aAAc,GACdQ,gBAAiB,GAEjBI,WAAY,IAAIC,KAChBC,oBAAqB,IACrBC,SAAU,GACVE,YAAa,GACbC,UAAW,GACXC,mBAAoB,GACpBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBiB,WAAY,GACZvB,eAAgB,GAChBO,iBAAkB,GAElBC,WAAY,GACZC,aAAc,IChDD,GACblE,GAAI,GACJE,MAAO,GACP+E,iBAAiB,GACjBjK,MAAO,GACPI,UAAW,GACXD,SAAU,KACVgF,WAAY,GACZC,WAAY,EACZC,UAAW,GACXC,WAAY,GACZC,YAAa,GAEbC,QAAS,GACTC,qBAAsB,GACtBvF,UAAW,GACXwF,cAAe,GACfC,UAAW,GACXC,YAAa,GACbC,aAAc,GACdC,aAAc,GACdC,cAAe,GACfC,UAAW,GACXC,aAAc,GACdC,gBAAiB,GACjBC,kBAAmB,GAEnBC,UAAW,GACXG,qBAAsB,GACtBC,gBAAiB,GACjBC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,mBAAoB,GACpBC,yBAA0B,GAC1BC,qBAAsB,GACtBC,oBAAqB,GAErBC,iBAAkB,GAClBC,oBAAqB,GACrBC,qBAAsB,GACtBC,sBAAuB,GACvBC,aAAc,GAEdK,aAAc,GACdF,qBAAqB,GAErBc,WAAY,IAAIC,KAChBC,oBAAqB,IACrBC,SAAU,GACVC,eAAgB,GAChBC,YAAa,GACbC,UAAW,GACXC,mBAAoB,GACpBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBC,iBAAkB,GAElBC,WAAY,GACZC,aAAc,GACdgB,QAAQ,GACRC,UAAU,GACVC,cAAc,IC9DD,GACbpF,GAAI,GACJhF,MAAO,GACPqK,SAAU,YACVC,cAAe,WACfC,gBAAiB,aACjBjF,WAAY,GACZC,YAAa,GACbC,QAAS,GACTtF,UAAW,GACXkG,UAAW,GACXoE,aAAc,oBACdC,aAAc,iBACdtB,WAAY,sBACZuB,iBAAkB,sBAClBtD,aAAc,GACduD,YAAa,GACbR,UAAW,GACXS,cAAe,GACfR,cAAe,GAGfjL,OAAQ,GACR0L,OAAQ,GACRvJ,SAAU,GACVC,aAAc,GACdoH,UAAW,GAGXM,WAAY,GACZC,aAAc,IC9BD,GACblE,GAAI,GACJhF,MAAO,GACPqK,SAAU,YACVC,cAAe,WACfC,gBAAiB,aACjBjF,WAAY,GACZC,YAAa,GACbuF,YAAa,kBACb1E,UAAW,GACX2E,cAAe,GACfX,cAAe,GAEfjL,OAAQ,GACR0L,OAAQ,GACRvJ,SAAU,GACVC,aAAc,GACdoH,UAAW,GAGXM,WAAY,GACZC,aAAc,ICrBD,GACblE,GAAI,GACJhF,MAAO,GACPqK,SAAU,YACVC,cAAe,WACfC,gBAAiB,aACjBjF,WAAY,GACZC,YAAa,GACbC,QAAS,GACTtF,UAAW,GACXkG,UAAW,GACXoE,aAAc,oBACdC,aAAc,iBACdtB,WAAY,sBACZuB,iBAAkB,sBAClBtD,aAAc,GACduD,YAAa,GACbR,UAAW,GACXS,cAAe,GACfR,cAAe,GAGfjL,OAAQ,GACR0L,OAAQ,GACRvJ,SAAU,GACVC,aAAc,GACdoH,UAAW,GAGXM,WAAY,GACZC,aAAc,I,YC/BD,G,UAAA,SAAC8B,EAAQnQ,GAOtB,IAAK,IAAIwB,KANT2O,EAAOf,iBAAiBpP,EAAOoP,iBAC/Be,EAAOrC,UAAY9N,EAAOmO,iBAAiBL,UACxC9N,EAAOuP,gBACRvP,EAAOuP,cAAca,KAAKC,MAAMrQ,EAAOuP,gBAGxBY,EACf,GAAa,cAAT3O,EACF2O,EAAOrC,UAAY9N,EAAOmO,iBAAiBL,eACvC,GAAa,cAATtM,EACR2O,EAAO5K,UAAYvF,EAAOwP,UAAY,kBACjC,GAAa,aAAThO,EACT2O,EAAO7K,SAAWtF,EAAOyP,mBACpB,GAAa,eAATjO,EACT2O,EAAO7F,WAAatK,EAAO0P,qBACvB,GAAI,UAAUrL,KAAK7C,GACvB2O,EAAO3O,GAAM,GACVxB,EAAOuP,gBACRY,EAAO3O,GAAMxB,EAAOuP,cAAc/N,QAE/B,IAAI,CAAC,aAAc,iBAAiB,aAAaqG,QAAQrG,IAAS,EACvE,SAGA2O,EAAO3O,GAAQxB,EAAOwB,GAS1B,OANI2O,EAAOvD,eACTuD,EAAOvD,aAAa,IAEtBuD,EAAOd,QAAQrP,EACfmQ,EAAOd,QAAQC,UAAYc,KAAKC,MAAMrQ,EAAOsP,WAC7Ca,EAAOd,QAAQS,YAAcM,KAAKC,MAAMrQ,EAAO8P,aACxCK,IClCM,aAAiB,IAAhBnQ,EAAgB,uDAAP,GACvB,OAAOA,EAAOyE,IAAI,SAAAC,GAAI,MAAK,CACzBlD,KAAMkD,EAAKlD,KACX8O,MAAO5L,EAAK4L,MACZC,SAAU7L,EAAK6L,SACfC,WAAY9L,EAAK8L,WACjBC,YAAa/L,EAAK+L,YAClBC,UAAWhM,EAAKgM,UAChBC,UAAWjM,EAAKiM,cCNL,WAACR,EAAQnQ,GACtB,IAAIwB,EAAO,GAQX,IAAKA,KADL2O,EAAOZ,cAAc,GACRY,EACPA,EAAO3O,KAASxB,EAClBmQ,EAAO3O,GAAQxB,EAAOmQ,EAAO3O,IACpBxB,EAAOwB,KAChB2O,EAAO3O,GAAQxB,EAAOwB,IAK1B,IAAKA,KAAQxB,EACE,OAATwB,EAEExB,EAAOmK,GACTgG,EAAOhG,GAAKnK,EAAOmK,UAEZgG,EAAOhG,GAER,YAAY9F,KAAK7C,GAEzB2O,EAAOb,UAAU9N,GAAQxB,EAAOwB,GACvB,gBAAgB6C,KAAK7C,GAE9B2O,EAAOJ,cAAcvO,GAAQxB,EAAOwB,GAC3B,YAAY6C,KAAK7C,GAC1B2O,EAAOJ,cAAcvO,GAAQxB,EAAOwB,GAClB,aAATA,EAET2O,EAAOL,YAAc9P,EAAO4Q,SACnB,UAAUvM,KAAK7C,KACxB2O,EAAOZ,cAAc/N,GAAQqP,EAAmB7Q,EAAOwB,KAK3D,OAFA2O,EAAOZ,cAAgBa,KAAKhM,UAAU+L,EAAOZ,eAEtCY,GC7CM,WAACA,EAAQnQ,GAMtB,IAAK,IAAIwB,KALTxB,EAAOsP,UAAYc,KAAKC,MAAMrQ,EAAOsP,WACrCtP,EAAO+P,cAAgBK,KAAKC,MAAMrQ,EAAO+P,eACzC/P,EAAO8P,YAAcM,KAAKC,MAAMrQ,EAAO8P,aACvC9P,EAAOuP,cAAgBa,KAAKC,MAAMrQ,EAAOuP,eAExBY,EACf,GAAa,cAAT3O,EACF2O,EAAOrC,UAAY9N,EAAOmO,iBAAiBL,eACtC,GAAa,cAATtM,EACT2O,EAAO3O,GAAQxB,EAAO+P,cAAcvO,QAC/B,GAAa,eAATA,EACT2O,EAAO3O,GAAQxB,EAAO+P,cAAcvO,QAC/B,GAAa,cAATA,EACT2O,EAAO5K,UAAYvF,EAAOwP,UAAY,kBACjC,GAAa,aAAThO,EACT2O,EAAO7K,SAAWtF,EAAOyP,mBACpB,GAAa,eAATjO,EACT2O,EAAO7F,WAAatK,EAAO0P,qBACtB,GAAI,YAAYrL,KAAK7C,GAE1B2O,EAAO3O,GAAQxB,EAAOsP,UAAU9N,QAC3B,GAEL,gBAAgB6C,KAAK7C,IACrB,YAAY6C,KAAK7C,GAEjB2O,EAAO3O,GAAQxB,EAAO+P,cAAcvO,QAC/B,GAAI,UAAU6C,KAAK7C,GACxB2O,EAAO3O,GAAQxB,EAAOuP,cAAc/N,IAAS,OACxC,IAAI,CAAC,aAAc,kBAAkBqG,QAAQrG,IAAS,EAC3D,SAGA2O,EAAO3O,GAAQxB,EAAOwB,GAG1B,OAAO2O,GCnCM,WAACA,EAAQnQ,GACtB,IAAIwB,EAAO,GAOX,IAAKA,KAAQ2O,EACPA,EAAO3O,KAASxB,EAClBmQ,EAAO3O,GAAQxB,EAAOmQ,EAAO3O,IACpBxB,EAAOwB,KAChB2O,EAAO3O,GAAQxB,EAAOwB,IAI1B,IAAKA,KAAQxB,EACE,OAATwB,EAEExB,EAAOmK,GACTgG,EAAOhG,GAAKnK,EAAOmK,UAEZgG,EAAOhG,GAEE,cAAT3I,EACT2O,EAAOJ,cAAcvO,GAAQxB,EAAOwB,GAC3B,YAAY6C,KAAK7C,GAE1B2O,EAAOb,UAAU9N,GAAQxB,EAAOwB,GACvB,gBAAgB6C,KAAK7C,GAE9B2O,EAAOJ,cAAcvO,GAAQxB,EAAOwB,GAC3B,YAAY6C,KAAK7C,GAC1B2O,EAAOJ,cAAcvO,GAAQxB,EAAOwB,GAClB,aAATA,EAET2O,EAAOL,YAAc9P,EAAO4Q,SACnB,UAAUvM,KAAK7C,KACxB2O,EAAOZ,cAAc/N,GAAQqP,EAAmB7Q,EAAOwB,KAS3D,OALA2O,EAAOb,UAAYc,KAAKhM,UAAU+L,EAAOb,WACzCa,EAAOJ,cAAgBK,KAAKhM,UAAU+L,EAAOJ,eAC7CI,EAAOL,YAAcM,KAAKhM,UAAU+L,EAAOL,aAC3CK,EAAOZ,cAAgBa,KAAKhM,UAAU+L,EAAOZ,eAEtCY,GCjDM,WAACA,EAAQnQ,GAKtB,IAAK,IAAIwB,KAJTxB,EAAO8Q,UAAYV,KAAKC,MAAMrQ,EAAO8Q,WAAa,MAClD9Q,EAAOkQ,cAAgBE,KAAKC,MAAMrQ,EAAOkQ,eAAiB,MAC1DlQ,EAAOuP,cAAgBa,KAAKC,MAAMrQ,EAAOuP,eAAiB,MAEzCY,EACF,cAAT3O,EACF2O,EAAOrC,UAAY9N,EAAOmO,iBAAiBL,UACzB,cAATtM,EACT2O,EAAO3O,GAAQxB,EAAOkQ,cAAc1O,GAClB,cAATA,EACT2O,EAAO5K,UAAYvF,EAAOwP,UAAY,aACpB,aAAThO,EACT2O,EAAO7K,SAAWtF,EAAOyP,cACP,eAATjO,EACT2O,EAAO7F,WAAatK,EAAO0P,gBAE3B,gBAAgBrL,KAAK7C,IACrB,iBAAiB6C,KAAK7C,IACtB,YAAY6C,KAAK7C,IACjB,YAAY6C,KAAK7C,GAGjB2O,EAAO3O,GAAQxB,EAAOkQ,cAAc1O,GAC3B,UAAU6C,KAAK7C,GACxB2O,EAAO3O,GAAQxB,EAAOuP,cAAc/N,IAAS,GAI7C2O,EAAO3O,GAAQxB,EAAOwB,GAG1B,OAAO2O,GC9BM,WAACA,EAAQnQ,GACtB,IAAIwB,EAAO,GAOX,IAAKA,KAJDxB,EAAO2L,iBAAmB3L,EAAO4L,iBACnC5L,EAAO6L,cAAgBzD,KAAK2I,MAAM/Q,EAAO2L,iBAA2C,IAAxB3L,EAAO4L,gBAAwB,KAGhFuE,EACPA,EAAO3O,KAASxB,EAClBmQ,EAAO3O,GAAQxB,EAAOmQ,EAAO3O,IACpBxB,EAAOwB,KAChB2O,EAAO3O,GAAQxB,EAAOwB,IAI1B,IAAKA,KAAQxB,EACE,OAATwB,EAEExB,EAAOmK,GACTgG,EAAOhG,GAAKnK,EAAOmK,UAEZgG,EAAOhG,GAEE,cAAT3I,EACT2O,EAAOD,cAAc1O,GAAQxB,EAAOwB,GAC3B,iBAAiB6C,KAAK7C,IAAS,YAAY6C,KAAK7C,QAEpCC,IAAjBzB,EAAOwB,IAAwC,KAAjBxB,EAAOwB,KACvC2O,EAAOD,cAAc1O,GAAQxB,EAAOwB,IAE7B,UAAU6C,KAAK7C,KACxB2O,EAAOZ,cAAc/N,GAAQqP,EAAmB7Q,EAAOwB,KAO3D,OAHA2O,EAAOD,cAAgBE,KAAKhM,UAAU+L,EAAOD,eAC7CC,EAAOZ,cAAgBa,KAAKhM,UAAU+L,EAAOZ,eAEtCY,G,YCzBHnL,G,UAAQ,CACZgM,QAASlU,OAAOqL,OAAO,GAAI8I,GAC3BC,QAASpU,OAAOqL,OAAO,GAAIgJ,GAC3BC,GAAItU,OAAOqL,OAAO,GAAIkJ,KAGlBrL,EAAU,CACdsL,UADc,SACJtM,EAAOgB,GACf,OAAOhB,EAAMgB,EAAQU,eAEvB6K,aAJc,SAIDvM,EAAOgB,GAClB,OAAO,SAACE,GACNA,EAAUwD,KAAKnC,IAAIrB,EAAS,GAAK,EAEjC,IAAIgB,EAASlC,EAAMgB,EAAQU,cAAcgH,oBACrC8D,EAAM,EACN9T,GAAS,EAcb,OAbIwI,EAAU,GACRgB,GAAU,YACZA,EAASrC,SAASqC,EAAS,YAC3BhB,GAAoB,GACpBsL,EAAMtK,IAAYhB,EAAU,GAE5BsL,EAAM,EAGRA,EAAMtK,IAAYhB,EAAU,EAG9BxI,KAAYwI,IAAYC,MAAMD,EAAU,IAAe,EAANsL,GAC1C9T,KAKP0I,EAAY,CAChBqL,iBADgB,SACCzM,EAAOkB,GACO,YAAzBA,EAAQQ,aACV1B,EAAMgM,QAAUzK,EAAE0C,MAAMgI,GACS,OAAzB/K,EAAQQ,aAChB1B,EAAMoM,GAAK7K,EAAE0C,MAAMoI,GAEnBrM,EAAMkM,QAAU3K,EAAE0C,MAAMkI,IAG5BO,eAVgB,SAUD1M,EAAOkB,GACpBlB,EAAMgM,QAAQrG,QAAUzE,EAAQiE,IAAM,GACtCnF,EAAMgM,QAAQ3L,UAAYa,EAAQyL,cAAgB,GAClD3M,EAAMgM,QAAQhG,aAAe9E,EAAQ0L,iBAAmB,GACxD5M,EAAMgM,QAAQ9F,cAAgBhF,EAAQ2L,eAAiB,GACvD7M,EAAMgM,QAAQ/F,aAAe/E,EAAQ4L,kBAAoB,GACzD9M,EAAMgM,QAAQ3F,gBAAkBnF,EAAQiE,GACxCnF,EAAMgM,QAAQnG,cAAgB3E,EAAQ6L,cAAgB,GACtD/M,EAAMgM,QAAQlG,UAAY5E,EAAQ8L,UAAY,GAC9ChN,EAAMgM,QAAQ7F,UAAYjF,EAAQvG,MAAQ,GAC1CqF,EAAMgM,QAAQ5F,aAAelF,EAAQ+L,UAAY,GACjDjN,EAAMgM,QAAQjG,YAAc7E,EAAQgM,WACpClN,EAAMgM,QAAQ1F,kBAAoBpF,EAAQyJ,cAC1CwC,kBAvBc,SAuBInN,EAAOkB,GACzBlB,EAAMoM,GAAGzG,QAAUzE,EAAQiE,IAAM,GACjCnF,EAAMoM,GAAG/L,UAAYa,EAAQyL,cAAgB,GAC7C3M,EAAMoM,GAAGpG,aAAe9E,EAAQ0L,iBAAmB,GACnD5M,EAAMoM,GAAGlG,cAAgBhF,EAAQ2L,eAAiB,GAClD7M,EAAMoM,GAAGnG,aAAe/E,EAAQ4L,kBAAoB,GACpD9M,EAAMoM,GAAG/F,gBAAkBnF,EAAQiE,GACnCnF,EAAMoM,GAAGvG,cAAgB3E,EAAQ6L,cAAgB,GACjD/M,EAAMoM,GAAGtG,UAAY5E,EAAQ8L,UAAY,GACzChN,EAAMoM,GAAGjG,UAAYjF,EAAQvG,MAAQ,GACrCqF,EAAMoM,GAAGhG,aAAelF,EAAQ+L,UAAY,GAC5CjN,EAAMoM,GAAGrG,YAAc7E,EAAQgM,WAC/BlN,EAAMoM,GAAG9F,kBAAoBpF,EAAQyJ,eAInCtJ,EAAU,CACR+L,iBADQ,qEACgClM,GADhC,uGACWF,EADX,EACWA,QAASqM,EADpB,EACoBA,SADpB,SAEgB1L,OAAayL,iBAAiB,CACxDjI,GAAIjE,EAAQiE,GACZwD,SAAUzH,EAAQyH,SAClBlH,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,eANZ,6CAELb,EAFK,KAEGgB,EAFH,KAQRhB,IAC2B,YAAzBG,EAAQU,cACV4L,EAAetM,EAAQsL,UAAW/K,EAAEC,MAAMK,EAAInJ,OAAOwG,KAAM2C,EAAInJ,OAAO6U,cAEtEF,EAAS,eAAgBjC,KAAKC,MAAMxJ,EAAInJ,OAAOwG,KAAK4L,eACnB,OAAzB9J,EAAQU,cAChB8L,EAAUxM,EAAQsL,UAAW/K,EAAEC,MAAMK,EAAInJ,OAAOwG,KAAM2C,EAAInJ,OAAO6U,cAEjEF,EAAS,iBAAkBjC,KAAKC,MAAMxJ,EAAInJ,OAAOwG,KAAK4L,eAEtD2C,EAAezM,EAAQsL,UAAW/K,EAAEC,MAAMK,EAAInJ,OAAOwG,KAAM2C,EAAInJ,OAAO6U,eAlB9D,kBAqBL,CAAC1M,EAAQgB,IArBJ,oGAuBR6L,cAvBQ,+KAuBQ1M,EAvBR,EAuBQA,QAChBhG,EAASuG,EAAEC,MACb,CACEC,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,cAExBV,EAAQsL,WAGmB,YAAzBtL,EAAQU,aACV1G,EAAO4Q,SAAWrK,EAAE0C,MAAMjD,EAAQ4K,UACD,OAAzB5K,EAAQU,aAChB1G,EAAO4Q,SAAWrK,EAAE0C,MAAMjD,EAAQ4K,UAElC5Q,EAAO2S,MAAQpM,EAAE0C,MAAMjD,EAAQ2M,OAG3BlP,EACqB,YAAzBuC,EAAQU,aACJkM,EAAiBrM,EAAE0C,MAAM4J,GAAiB7S,GACjB,OAAzBgG,EAAQU,aAAwBoM,EAAYvM,EAAE0C,MAAM8J,GAAY/S,GAC9DgT,EAAiBzM,EAAE0C,MAAMgK,GAAiBjT,GA5CtC,SA8CgB2G,OAAa+L,cAAcjP,GA9C3C,6CA8CLoC,EA9CK,KA8CGgB,EA9CH,KA+CRhB,IACFG,EAAQsL,UAAUnH,GAAKtD,EAAInJ,OAAOyM,IAhDxB,kBAkDL,CAACtE,EAAQgB,IAlDJ,mGAoDRqM,mBApDQ,qEAoDwBhN,GApDxB,yGAoDaF,EApDb,EAoDaA,QACrBhG,EAASuG,EAAEC,MACb,CACEC,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,cAExBH,EAAEC,MAAMR,EAAQsL,UAAWpL,IAGA,YAAzBF,EAAQU,aACV1G,EAAO4Q,SAAWrK,EAAE0C,MAAMjD,EAAQ4K,UACD,OAAzB5K,EAAQU,aAChB1G,EAAO4Q,SAAWrK,EAAE0C,MAAMjD,EAAQmN,YAElCnT,EAAO2S,MAAQpM,EAAE0C,MAAMjD,EAAQ2M,OAG3BlP,EACqB,YAAzBuC,EAAQU,aACJkM,EAAiBrM,EAAE0C,MAAM4J,GAAiB7S,GACjB,OAAzBgG,EAAQU,aACNoM,EAAYvM,EAAE0C,MAAM8J,GAAY/S,GACjCgT,EAAiBzM,EAAE0C,MAAMgK,GAAiBjT,GA1ErC,SA4EgB2G,OAAauM,mBAAmBzP,GA5EhD,6CA4ELoC,EA5EK,KA4EGgB,EA5EH,uBA6EL,CAAChB,EAAQgB,IA7EJ,qGA+ERuM,eA/EQ,qEA+E+BlN,GA/E/B,yGA+ESF,EA/ET,EA+ESA,QAASqN,EA/ElB,EA+EkBA,UAC1B5P,EAAS8C,EAAEC,MACb,CACEC,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,cAExBR,GArFU,SAwFgBS,OAAayM,eAAe3P,GAxF5C,6CAwFLoC,EAxFK,KAwFGgB,EAxFH,KAyFRhB,GACFwN,EAAUC,KAAKxN,SAASzJ,KAAKkX,KAAK,SAAC7O,EAAMC,GACnCD,EAAKyF,KAAOjE,EAAQiE,IACtBvG,aAAI4P,IAAIH,EAAUC,KAAKxN,SAASzJ,KAAKsI,GAAQ,aAAc,KA5FrD,kBAgGL,CAACkB,EAAQgB,IAhGJ,qGAkGR4M,gBAlGQ,qEAkGgCvN,GAlGhC,yGAkGUF,EAlGV,EAkGUA,QAASqN,EAlGnB,EAkGmBA,UAC3B5P,EAAS8C,EAAEC,MACb,CACEC,SAAUT,EAAQS,SAClBC,aAAcV,EAAQU,cAExBR,GAxGU,SA2GgBS,OAAa8M,gBAAgBhQ,GA3G7C,6CA2GLoC,EA3GK,KA2GGgB,EA3GH,KA4GRhB,IACFwN,EAAUC,KAAKI,KAAKrX,KAAOgX,EAAUC,KAAKI,KAAKrX,KAAKsX,OAAO,SAACjP,GAAD,OAAUA,EAAKyF,KAAOjE,EAAQiE,MA7G/E,kBA+GL,CAACtE,EAAQgB,IA/GJ,qGAiHR+M,UAjHQ,qEAiHM1N,GAjHN,8HAkHgBS,OAAaiN,UAAU1N,GAlHvC,6CAkHLL,EAlHK,KAkHGgB,EAlHH,uBAmHL,CAAChB,EAAQgB,IAnHJ,oGAqHdgN,YArHc,WAqHW3N,GAAS,IAApBF,EAAoB,EAApBA,QACNvC,EAAS8C,EAAEC,MACf,CACEsN,YAAsC,YAAzB9N,EAAQU,aAA6B,mBAAqB,oBAEzER,GAGFS,OAAakN,YAAYpQ,IAErBsQ,cA/HQ,qEA+HmB7N,GA/HnB,uGA+HQF,EA/HR,EA+HQA,QACdvC,EAAS8C,EAAEC,MACf,CACErB,MAAOa,EAAQsL,UAAUnM,MACzBG,SAAUU,EAAQsL,UAAUhM,SAC5BC,UAAWS,EAAQsL,UAAU/L,UAC7BmB,aAAcV,EAAQU,aACtByD,GAAInE,EAAQsL,UAAUnH,IAExBjE,GAxIU,SA2IgBS,OAAaoN,cAActQ,GA3I3C,6CA2ILoC,EA3IK,KA2IGgB,EA3IH,uBA4IL,CAAChB,EAAQgB,IA5IJ,oGA8IRmN,cA9IQ,2KA8IQhO,EA9IR,EA8IQA,QA9IR,SA+IgBW,OAAaqN,cAAchO,EAAQsL,WA/InD,6CA+ILzL,EA/IK,KA+IGgB,EA/IH,KAgJRhB,IACFG,EAAQsL,UAAU7G,WAAa5D,EAAInJ,OAAOrB,KAAKoO,WAC/CzE,EAAQsL,UAAU5G,YAAc7D,EAAInJ,OAAOrB,KAAKqO,aAlJtC,kBAoJL,CAAC7E,EAAQgB,IApJJ,mGAwJD,cACb7B,QACAoB,YACAC,UACAL,Y,6GClPIiO,EAAc,SAAC5X,GACnB,IAAI6H,EAAO,CACTiB,MAAO9I,EAAK8I,MACZqK,SAAUnT,EAAKmT,SACfC,cAAepT,EAAKoT,cACpBC,gBAAiBrT,EAAKqT,gBACtBjF,WAAYpO,EAAKoO,WACjBC,YAAarO,EAAKqO,YAClBa,UAAWlP,EAAKkP,UAChB6C,WAAY/R,EAAK+R,WACjBC,aAAchS,EAAKgS,cA6BrB,MA3B0B,YAAtBhS,EAAKqK,cACPxC,EAAKsG,UAAYnO,EAAKmO,UACtBtG,EAAKyG,QAAUtO,EAAKsO,QACpBzG,EAAKmB,UAAYhJ,EAAKgJ,UACtBnB,EAAK0L,aAAevT,EAAKuT,aACzB1L,EAAK2L,iBAAmBxT,EAAKwT,iBACzBxT,EAAKiS,aAAYpK,EAAKoK,WAAajS,EAAKiS,YAE5CpK,EAAKoL,UAAYjT,EAAKiT,UACtBpL,EAAK6L,cAAgB1T,EAAK0T,cAC1B7L,EAAK4L,YAAczT,EAAKyT,YACxB5L,EAAKqL,cAAgBlT,EAAKkT,eACI,OAAtBlT,EAAKqK,cACbxC,EAAKyG,QAAUtO,EAAKsO,QACpBzG,EAAKmB,UAAYhJ,EAAKgJ,UAClBhJ,EAAKiS,aAAYpK,EAAKoK,WAAajS,EAAKiS,YAI5CpK,EAAKqL,cAAgBlT,EAAKkT,gBAE1BrL,EAAK+L,YAAc5T,EAAK4T,YAExB/L,EAAKgM,cAAgB7T,EAAK6T,cAC1BhM,EAAKqL,cAAgBlT,EAAKkT,eAExBlT,EAAK8N,KAAIjG,EAAKiG,GAAK9N,EAAK8N,IACrBjG,GAGHgQ,E,iHACwB,IAAX7X,EAAW,uDAAJ,GACtB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ8N,GAAI,EACJD,QAAS,MACT5F,OAAQ,MAAF,OAA8B,YAAtBjI,EAAKqK,aAA6B,UAAkC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAAvF,uBACNjD,OAAQ,CAACpH,EAAK8N,GAAI9N,EAAKsR,SAAUtR,EAAKoK,e,6CAKZ,IAAXpK,EAAW,uDAAJ,GAC1B,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ8N,GAAI,EACJD,QAAS,MACT5F,OAAQ,yCACRb,OAAQ,CAACpH,EAAK8I,MAAO9I,EAAKkJ,UAAWlJ,EAAKkP,gB,sCAKvB,IAAXlP,EAAW,uDAAJ,GACnB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ8N,GAAI,EACJD,QAAS,MACT5F,OAAQ,MAAF,OAA8B,YAAtBjI,EAAKqK,aAA6B,UAAkC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAAvF,qBACNjD,OAAQ,CAACwQ,EAAY5X,GAAOA,EAAKoK,e,2CAKT,IAAXpK,EAAW,uDAAJ,GACxB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ8N,GAAI,EACJD,QAAS,MACT5F,OAAQ,MAAF,OAA8B,YAAtBjI,EAAKqK,aAA6B,UAAkC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAAvF,wBACJrK,EAAKiI,QAEPb,OAAQ,CAACwQ,EAAY5X,GAAOA,EAAK2T,OAAQ3T,EAAKoK,SAAUpK,EAAKyR,gB,uCAKzC,IAAXzR,EAAW,uDAAJ,GACpB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ8N,GAAI,EACJD,QAAS,MACT5F,OAAQ,MAAF,OAA8B,YAAtBjI,EAAKqK,aAA6B,UAAmC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAAxF,wBACJrK,EAAKiI,QAEPb,OAAQ,CACN,CACE0G,GAAI9N,EAAK8N,U,wCAOQ,IAAX9N,EAAW,uDAAJ,GACrB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,MAAF,OAA8B,YAAtBnH,EAAKqK,aAA6B,UAAmC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAAxF,qBACJwB,YAAa,OACb7L,KAAM,CACJ8N,GAAI9N,EAAK8N,Q,0CAKc,IAAX9N,EAAW,uDAAJ,GACvB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,0DACRb,OAAQ,CAACpH,EAAKoK,SAAUpK,EAAK4K,YAC7BkD,GAAI,O,uCAKgB,IAAX9N,EAAW,uDAAJ,GACpB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,wCACRb,OAAQ,CAACpH,EAAKmO,UAAWnO,EAAK8I,OAC9BgF,GAAI,O,uCAKgB,IAAX9N,EAAW,uDAAJ,GACpB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,wCACRb,OAAQ,CAACpH,EAAKmO,UAAWnO,EAAK8I,OAC9BgF,GAAI,O,4CAKqB,IAAX9N,EAAW,uDAAJ,GACzB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,6CACRb,OAAQ,CAACpH,EAAKmO,UAAWnO,EAAK8I,MAAO9I,EAAKsO,SAC1CR,GAAI,O,kDAK2B,IAAX9N,EAAW,uDAAJ,GAC/B,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,mDACRb,OAAQ,CAACpH,EAAKmO,UAAWnO,EAAK8I,OAC9BgF,GAAI,O,sCAKe,IAAX9N,EAAW,uDAAJ,GACnB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,2CACRb,OAAQ,CAACpH,EAAKmO,UAAWnO,EAAK8I,OAC9BgF,GAAI,O,qCAKc,IAAX9N,EAAW,uDAAJ,GACZ8X,EAAY9X,EAAKmJ,UACnB4O,IAAM/X,EAAKmJ,UAAU,IAAI6O,OAAO,uBAChC,GACEC,EAAUjY,EAAKmJ,UAAY4O,IAAM/X,EAAKmJ,UAAU,IAAI6O,OAAO,uBAAyB,GAE1F,OAAO1L,eAAI,CACTrE,OAAQ,OACRd,KAAM,MAAF,OAA8B,YAAtBnH,EAAKqK,aAA6B,UAAmC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAAxF,iBAA0GrK,EAAKiI,OAA/G,OACJ4D,YAAa,OACb7L,KAAM,CACJqJ,MAAOrJ,EAAKqJ,MACZ6O,OAAQlY,EAAKsJ,KAAO,GAAKtJ,EAAKqJ,MAC9B8O,MAAO,KACPC,UAAW,OACXhF,cAAepT,EAAKiJ,SACpBF,OAAQ/I,EAAK+I,OACbD,MAAO9I,EAAK8I,MACZE,UAAWhJ,EAAKgJ,UAChBmK,SAAUnT,EAAKkJ,UACfmP,eAAgBP,EAChBQ,aAAcL,EACd7N,SAAUpK,EAAKoK,c,yCAKO,IAAXpK,EAAW,uDAAJ,GACtB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,MAAF,OACkB,YAAtBjI,EAAKqK,aAA6B,UAAmC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAD5E,yCAGNjD,OAAQ,CAACpH,EAAK8N,IACdA,GAAI,O,yCAKkB,IAAX9N,EAAW,uDAAJ,GACtB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,MAAF,OACkB,YAAtBjI,EAAKqK,aAA6B,UAAkC,OAAtBrK,EAAKqK,aAAuB,KAAK,UAD3E,uCAGNjD,OAAQ,CAACpH,EAAK8N,IACdA,GAAI,O,kCAKW,IAAX9N,EAAW,uDAAJ,GACf,MAAkB,OAAdA,EAAKsD,KACAmK,eAAS,CACdtG,KAAM,iCACNC,OAAQ,CACN0G,GAAI9N,EAAK8N,MAGU,QAAd9N,EAAKsD,KACPmK,eAAS,CACdtG,KAAM,iCACNC,OAAQ,CACN0G,GAAI9N,EAAK8N,WAJR,I,oCAUc,IAAX9N,EAAW,uDAAJ,GACjB,OAAOyN,eAAS,CACdtG,KAAM,8BACNnH,KAAM,CACJyX,YAAazX,EAAKyX,YAClBc,SAAUvY,EAAKuY,SACfzL,SAAU9M,EAAK8M,SACf0L,kBAAmBxY,EAAKwY,kBACxBC,SAAUzY,EAAKyY,c,sCAKI,IAAXzY,EAAW,uDAAJ,GACnB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,8BACN0E,YAAa,OACbzE,OAAQ,CACN+G,UAAWnO,EAAKiJ,SAChByP,QAAS1Y,EAAKkJ,UACdmB,aAAc,CAAC,KAAKmB,QAAQ,GAAKxL,EAAK8I,QAAU,EAAI,eAAiB,iBACrE6P,MAAO3Y,EAAK8N,Q,6CAKc,IAAX9N,EAAW,uDAAJ,GAC1B,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,2CACRb,OAAQ,CAACpH,EAAKiJ,UACd6E,GAAI,S,KAMG,WAAI+J,G,sFCjVbA,E,6GACoB,IAAX7X,EAAW,uDAAJ,GAClB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ8N,GAAI,EACJD,QAAS,MACT5F,OAAQ,+BACRb,OAAQ,CAACpH,EAAKmO,UAAW,W,yCAKH,IAAXnO,EAAW,uDAAJ,GACtB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,iBACN0E,YAAa,OACb7L,KAAM,CACJ6N,QAAS,MACT5F,OAAQ,0DACRb,OAAQ,CAACpH,EAAKmO,UAAWnO,EAAK8I,OAC9BgF,GAAI,O,4CAKqB,IAAX9N,EAAW,uDAAJ,GAMzB,OALIA,EAAK4Y,QACP5Y,EAAK4Y,QAAU5Y,EAAK4Y,QAAQC,QAAQ,MAAO,KAE3C7Y,EAAK4Y,QAAU,GAEVtM,eAAI,CACTrE,OAAQ,MACRd,KAAM,2CACN0E,YAAa,OACbzE,OAAQ,CACN0R,YAAa9Y,EAAK8Y,YAClBC,WAAY,uBAAF,OAAyB/Y,EAAK4Y,c,2CAKhB,IAAX5Y,EAAW,uDAAJ,GACxB,OAAOsM,eAAI,CACTrE,OAAQ,OACRd,KAAM,kCACN0E,YAAa,OACb7L,KAAM,CACJkY,MAAOlY,EAAKgZ,UAAYhZ,EAAKsJ,KAAO,GACpCD,MAAOrJ,EAAKgZ,SACZC,UAAW,EACX3D,aAActV,EAAKkZ,QACnB/K,UAAWnO,EAAKmO,UAChB4D,WAAY/R,EAAK+R,WACjBvI,OAAQ,EACRV,MAAO9I,EAAK8I,MACZqQ,QAASnZ,EAAKmZ,QACdJ,WAAY,kBACZK,WAAY,IACZC,eAAgB,IAChBC,OAAQtZ,EAAKkJ,UACbqQ,UAAWvZ,EAAKkJ,UAChBsQ,UAAW,EACXrB,MAAO,KACPC,UAAW,c,KAMJ,WAAIP,G,qBCvEnB3V,EAAOD,QAAU,CACfkD,KAAM,mBACNsU,QAAQ,I,yGCLJ9Q,EAAQ,CACZ+Q,MAAO,CACLtO,MAAO,OACPuO,MAAM,EACNvS,OAAQ,GACR6P,KAAM,CACJ1N,MAAO,EACPjC,SAAS,EACTsS,YAAa,SACb5Z,KAAM,GACNsJ,KAAM,EACN0P,SAAU,KAGda,eAAgB,CACdzO,MAAO,SACPuO,MAAM,EACNvS,OAAQ,IAEV0S,eAAgB,CACd1O,MAAO,WACPuO,MAAM,EACNvS,OAAQ,KAINuC,EAAU,GAEVI,EAAY,CAChBgQ,YADgB,SACJpR,EAAOkB,GACjB,IAAI1E,EAAO0E,EAAQmQ,WAEnB,IAAK7U,EAAM,OAAO,EAElBwD,EAAMxD,GAAMwU,MAAO,EACnBhR,EAAMxD,GAAMiG,MAAQvB,EAAQuB,OAASzC,EAAMxD,GAAMiG,MACjDzC,EAAMxD,GAAMiC,OAASyC,EAAQzC,OAEzBuB,EAAMxD,GAAM8R,OACdtO,EAAMxD,GAAM8R,KAAK3N,KAAO,EACxBX,EAAMxD,GAAM8R,KAAK1N,MAAQ,IAG7B0Q,iBAfgB,SAeCtR,GACfA,EAAM+Q,MAAMzC,KAAK1N,MAAQ,EACzBZ,EAAM+Q,MAAMzC,KAAK3N,KAAO,GAE1B4Q,YAnBgB,SAmBJvR,EAAOkB,GACjB,IAAI1E,EAAO0E,EAAQmQ,WAEnB,IAAK7U,EAAM,OAAO,EAElBwD,EAAMxD,GAAMwU,MAAO,IAIjB3P,EAAU,CACRmQ,mBADQ,qEAC+BtQ,GAD/B,qGACalB,EADb,EACaA,MAAOgB,EADpB,EACoBA,QAC5BvC,EAAS,IAETuB,EAAM+Q,MAAMzC,KAAK3P,QAJT,0CAIyB,GAJzB,cAMZqB,EAAM+Q,MAAMzC,KAAKjX,KAAO,GAExBoH,EAAS8C,EAAEC,MACT,CACEb,KAAMX,EAAM+Q,MAAMzC,KAAK3N,KACvB0P,SAAUrQ,EAAM+Q,MAAMzC,KAAK+B,SAC3B9P,UAAWS,EAAQsL,UAAU/L,UAC7BiQ,QAAS,QACT9O,aAAcV,EAAQU,aACtB8D,UAAWxE,EAAQsL,UAAUlD,YAAcpI,EAAQsL,UAAU9G,UAC7DrF,MAAOa,EAAQsL,UAAUnM,OAE3Be,GAGFlB,EAAM+Q,MAAMzC,KAAK3P,SAAU,EArBf,SAsBgB8S,OAAcD,mBAAmB/S,GAtBjD,6CAsBLoC,EAtBK,KAsBGgB,EAtBH,KAuBZ7B,EAAM+Q,MAAMzC,KAAK3P,SAAU,EAEvBkC,IACFb,EAAM+Q,MAAMzC,KAAKjX,KAAOwK,EAAIC,UAC5B9B,EAAM+Q,MAAMzC,KAAK1N,MAAQiB,EAAIjB,OA3BnB,kBA6BL,CAACC,EAAQgB,IA7BJ,sGAiCD,cACb7B,QACAoB,YACAC,UACAL,Y,mEC7FIhB,EAAQ,CACZ0B,aAAc,GACdD,SAAU,GACViQ,aAAa,GAGT1Q,EAAU,CACdU,aADc,SACD1B,GACX,OAAOA,EAAM0B,cAEfD,SAJc,SAILzB,GACP,OAAOA,EAAMyB,UAEfiQ,YAPc,SAOF1R,GACV,OAAOA,EAAM0R,cAIXtQ,EAAY,CAChBuQ,WADgB,SACL3R,EAAOkB,GAChB,IAAK,IAAI1E,KAAQ0E,EACflB,EAAMxD,GAAQ0E,EAAQ1E,KAKtB6E,EAAU,GAED,cACbrB,QACAoB,YACAC,UACAL,Y,uBClCFzH,EAAOD,QAAU,CACfd,MAAOoZ,EAAQ,QAAkCC,QACjDvD,KAAMsD,EAAQ,QAAiCC,QAC/CC,OAAQF,EAAQ,QAAmCC,QACnDf,OAAQc,EAAQ,QAAmCC,QACnDjG,SAAUgG,EAAQ,QAAqCC,QACvD1D,WAAYyD,EAAQ,QAAwCC,U,gICFjD1P,EAAO,wDAAG,kKAOZC,MAAQA,IAAIC,SAAW,eAPX,yCAAH,qDAWPC,EAAkB,wDAAG,WAAO7I,GAAP,yFAChC0E,QAAQoE,IAAI9I,GACNpC,EAAOoC,EAAEpC,KACVoC,EAAEpC,KAHyB,gBAKZ,wCAAdoC,EAAE8C,QACJiG,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAAS,qBAGXiG,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAAS,2BAfiB,0BAkBP,cAAdlF,EAAK+D,KAlBgB,iBAmB9BoH,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAASlF,EAAKsL,WAtBc,4BAwBrBtL,EAAK6E,OAA6B,IAApB7E,EAAK6E,MAAMd,KAxBJ,iBAyB9BoH,OAAOtG,MAAM,CACXuG,MAAO,OACPC,SAAU,IACVnG,QAAS,2BA5BmB,4BA8BrBlF,EAAKqB,QAA+B,YAArBrB,EAAKqB,OAAO0C,KA9BN,oBA+BL,iBAArB/D,EAAKqB,OAAO0C,KA/Bc,0CAgCrB+G,KAhCqB,iCAmCzB,EAAC,IAnCwB,0CAAH,sDAuClBS,EAAsB,wDAAG,WAAOnJ,GAAP,qFAEhCA,EAAEpC,KAAKwL,QAAQ,sBAAwB,GAFP,yCAG3BV,KAH2B,YAMhC1I,EAAEsB,QAAQ+H,YAAYD,QAAQ,aAAe,GANb,yCAO3BV,KAP2B,gCAS7B,EAAC,IAT4B,yCAAH,sDChDjCY,IAAMC,SAASC,iBAAkB,EAGnC,8DAAe,iIAAS3D,cAAT,MAAkB,MAAlB,EAAyBd,EAAzB,EAAyBA,KAAzB,IAA+BC,cAA/B,MAAwC,KAAxC,MAA8CpH,YAA9C,MAAqD,KAArD,EAA2D6L,EAA3D,EAA2DA,YAA3D,SAMXzE,EAAS3G,OAAOqL,OAAO,GAAI1E,EAAQ,CAAErB,EAAGgG,KAAKC,WAC7C5E,EAAoB,QAAXa,EAAmBxH,OAAOqL,OAAO,GAAI1E,EAAQpH,GAAQoH,EAE1D6E,EAAoB,kCACJ,SAAhBJ,EACFI,EAAoB,kCACK,SAAhBJ,IACTI,EAAoB,oDAGlBjM,GAAwB,SAAhB6L,GAA0B,iBAAiB7D,KAAKC,KAC1DjI,EAAO8H,IAAGC,UAAU/H,IAjBX,UAmBO0L,IAAM,CACtBzD,OAAQA,EACRiE,IAAK,IAAM/E,EACXgF,QACoF,KACpF/E,OAAQA,EACRgF,QAAS,CACP,eAAgBH,EAChBI,OAAQ,OAEVrM,KAAM,iBAAiBgI,KAAKC,GAAUjI,EAAO,KA7BpC,WAmBLwK,EAnBK,SAgCPA,EAAI4B,QAAQ,gBAAgBZ,QAAQ,cAAgB,GAhC7C,kCAiCHD,EAAuBf,GAjCpB,iCAkCF,EAAC,EAAOA,EAAIxK,OAlCV,aAuCRwK,EAAIxK,MAAQwK,EAAIxK,KAAK+D,MAAQ,CAAC,UAAW,QAAQyH,QAAQhB,EAAIxK,KAAK+D,MAAQ,GAC1EyG,EAAIxK,MAAQwK,EAAIxK,KAAK6E,OACrB2F,EAAIxK,MACHwK,EAAIxK,KAAKqB,QACTmJ,EAAIxK,KAAKqB,OAAO0C,MAChB,CAAC,UAAW,QAAQyH,QAAQhB,EAAIxK,KAAKqB,OAAO0C,MAAQ,GA5C7C,kCA8CHkH,EAAmBT,GA9ChB,iCA+CF,EAAC,EAAOA,EAAIxK,OA/CV,iCAkDJ,EAAC,EAAMwK,EAAIxK,OAlDP,sDAoDLiL,EAAmB,EAAD,IApDb,iCAqDJ,EAAC,EAAO,KAAEjL,OArDN,wDAAf,sDCPesM,U,gDCFf,IAAIlE,EAAM,CACT,qCAAsC,OACtC,0CAA2C,OAC3C,mDAAoD,OACpD,wDAAyD,OACzD,mDAAoD,OACpD,2DAA4D,OAC5D,yCAA0C,OAC1C,mCAAoC,OACpC,oCAAqC,OACrC,wDAAyD,OACzD,oDAAqD,QAItD,SAASsS,EAAeC,GACvB,IAAI7M,EAAK8M,EAAsBD,GAC/B,OAAOhZ,EAAoBmM,GAE5B,SAAS8M,EAAsBD,GAC9B,IAAIhZ,EAAoBgE,EAAEyC,EAAKuS,GAAM,CACpC,IAAIvY,EAAI,IAAI0B,MAAM,uBAAyB6W,EAAM,KAEjD,MADAvY,EAAE2B,KAAO,mBACH3B,EAEP,OAAOgG,EAAIuS,GAEZD,EAAeG,KAAO,WACrB,OAAOpa,OAAOoa,KAAKzS,IAEpBsS,EAAelY,QAAUoY,EACzB1Y,EAAOD,QAAUyY,EACjBA,EAAe5M,GAAK,Q,qBC3BpB5L,EAAOD,QAAU,CACfkD,KAAM,oBACNsU,QAAQ,I,qBCHVvX,EAAOD,QAAU,CACfkD,KAAM,iCACNsU,QAAQ,I,mGCNN,EAAS,WAAa,IAAIqB,EAAIrO,KAASsO,EAAGD,EAAIE,eAAmBC,EAAGH,EAAII,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAAC3N,MAAM,CAAC,GAAK,QAAQ,CAAC2N,EAAG,gBAAgB,IAC9IE,EAAkB,GCMtB,GACEC,MAAO,CACLC,OADJ,SACA,KACM,IAAN,KACUC,EAAGC,MAAMnR,WACXoR,EAAapR,SAAWkR,EAAGC,MAAMnR,UAE/B,kBAAkBpC,KAAKsT,EAAGnU,QAC5BqU,EAAanR,aAAe,WAE1B,kBAAkBrC,KAAKsT,EAAGnU,QAC5BqU,EAAanR,aAAe,WAE1B,aAAarC,KAAKsT,EAAGnU,QACvBqU,EAAanR,aAAe,MAE9BoC,KAAKgP,OAAOC,OAAO,aAAcF,MCvBoU,I,wBCQvWG,EAAY,eACd,EACA,EACAR,GACA,EACA,KACA,KACA,MAIa,EAAAQ,E,kDChBA,G,wCAAA,SAAAL,GACb,IAAIM,EAAS,GACTC,EAAUP,EAAGO,QAEjBA,EAAQ3U,QAAQ4U,QAAQ,SAAAC,GACtB,IAAI3Q,EAAQ2Q,EAAQC,KAAK5Q,MACzBA,GAASwQ,EAAO9a,KAAKsK,KAGvB,IAAIA,EAAQwQ,EAAOK,KAAK,OACxBpZ,SAASuI,MAAQA,ICXJ,WAAA8Q,GACbA,EAAOC,UAAUC,I,YCCnBC,aAAIC,IAAIC,QACR,IAAML,EAAS,IAAIK,OAAO,CACxBnW,KAAM,OACNoW,OAAQ,KAGVC,EAAMP,GAESA,Q,wECTTQ,EAAe,IAEf/T,EAAQ,CACZtB,QAAS,IAGLsC,EAAU,CACdgT,WADc,SACHhU,GACT,OAAO,SAACiU,GACN,OAAOjU,EAAMtB,QAAQ6P,KAAK,SAAC7P,GAAD,OAAaA,EAAQlC,OAASyX,KAAa,KAGzEC,eANc,SAMCC,EAAGnT,GAChB,OAAO,SAACiT,GACN,IAAMvV,EAAUsC,EAAQgT,WAAWC,GACnC,OAAOvV,EAAUA,EAAQrH,KAAO,MAKhC+J,EAAY,CAChBgT,eADgB,SACDpU,EAAOkB,GAEpBA,EAAQmT,YAAa,IAAI5L,MAAO6L,UAEhC,IAAM5V,EAAUsB,EAAMtB,QAAQ6P,KAAK,SAAC7O,GAClC,OAAOA,EAAKlD,OAAS0E,EAAQ1E,MAAQ1E,OAAOqL,OAAOzD,EAAMwB,MAE1DxC,GAAWsB,EAAMtB,QAAQvG,KAAK+I,KAI7BG,EAAU,CACRkT,eADQ,qEAC4BN,GAD5B,qGACSlB,EADT,EACSA,OAAQ/R,EADjB,EACiBA,QACvBtC,EAAUsC,EAAQgT,WAAWC,KAC/BvV,IAAW,IAAI+J,MAAO6L,UAAY5V,EAAQ2V,YAAcN,GAHhD,yCAKH,EAAC,EAAMrV,IALJ,OAOVqU,EAAO,iBAAkB,CAAEvW,KAAMyX,EAAUpT,OAAQ,UAAWxJ,KAAM,KAP1D,uBAUgBmd,OAAYxP,aAAa,CACnD1F,OAAQ,qCACRb,OAAQ,CAACwV,KAZC,6CAULpT,EAVK,KAUGgB,EAVH,KAcRhB,GACFkS,EAAO,iBAAkB,CACvBvW,KAAMyX,EACNpT,OAAQ,SACRxJ,KAAMwK,EAAInJ,OAAOrB,KAAKoI,IAAI,SAACC,GAAD,MAAW,CACnCnC,MAAO,GAAKmC,EAAK+U,YACjBC,MAAOhV,EAAKiV,iBApBN,kBAwBL,CAAC9T,EAAQG,EAAQgT,WAAWC,KAxBvB,sGA4BD,GACbjU,QACAoB,YACAC,UACAL,WCjEI+S,EAAe,IAEf/T,EAAQ,CACZ4U,YAAa,IAGT5T,EAAU,CACd6T,cADc,SACA7U,GAEZ,OAAO,SAAC8U,GACN,OAAO9U,EAAM4U,YAAYrG,KAAK,SAACwG,GAAD,OAAgBA,EAAWvY,OAASsY,KAAmB,KAGzFE,kBAPc,SAOIb,EAAGnT,GAEnB,OAAO,SAAC8T,GACN,IAAMC,EAAa/T,EAAQ6T,cAAcC,GACzC,OAAOC,EAAaA,EAAW1d,KAAO,IAG1C4d,cAdc,SAcAd,EAAGnT,GAEf,OAAO,SAAC8T,GACN,IAAMC,EAAa/T,EAAQ6T,cAAcC,GACzC,OAAO,SAAC5T,GACN,IAAIgB,EAAS6S,EAAW1d,KACpBmV,EAAM,EAEV,OAAe,GAAXtK,IAEJhB,EAAUwD,KAAKnC,IAAIrB,EAAS,GAAK,EAE7BA,EAAU,GACRgB,GAAU,YACZA,EAASrC,SAASqC,EAAS,YAC3BhB,GAAoB,GACpBsL,EAAMtK,IAAYhB,EAAU,GAE5BsL,EAAM,EAGRA,EAAMtK,IAAYhB,EAAU,KAGpBA,IAAYC,MAAMD,EAAU,IAAe,EAANsL,OAIrD0I,sBA1Cc,SA0CQf,EAAGnT,GAGvB,OAAO,SAAC8T,GACN,IAAMC,EAAa/T,EAAQ6T,cAAcC,GACnCG,EAAgBjU,EAAQiU,cAAcH,GAC5C,OAAO,SAAC5T,GACN,OAA2B,GAApB6T,EAAW1d,MAAc4d,EAAc/T,OAMhDE,EAAY,CAChB+T,kBADgB,SACEnV,EAAOkB,GAEvBA,EAAQmT,YAAa,IAAI5L,MAAO6L,UAEhC,IAAMS,EAAa/U,EAAM4U,YAAYrG,KAAK,SAAC7O,GACzC,OAAOA,EAAKlD,OAAS0E,EAAQ1E,MAAQ1E,OAAOqL,OAAOzD,EAAMwB,MAE1D6T,GAAc/U,EAAM4U,YAAYzc,KAAK+I,KAIpCG,EAAU,CACR+T,6BADQ,qEAC0CN,GAD1C,qGACuB/B,EADvB,EACuBA,OAAQ/R,EAD/B,EAC+BA,QACrC+T,EAAa/T,EAAQ6T,cAAcC,KACrCC,IAAc,IAAItM,MAAO6L,UAAYS,EAAWV,YAAcN,GAHtD,yCAKH,EAAC,EAAMgB,IALJ,OAOVhC,EAAO,oBAAqB,CAAEvW,KAAMsY,EAAgBjU,OAAQ,UAAWxJ,KAAM,KAPnE,uBAUgBmd,OAAYxP,aAAa,CACnD1F,OAAQ,0DACRb,OAAQ,CAAC,KAAMqW,KAZL,6CAULjU,EAVK,KAUGgB,EAVH,KAcRhB,GACFkS,EAAO,oBAAqB,CAC1BvW,KAAMsY,EACNjU,OAAQ,SACRxJ,KAAMwK,EAAInJ,OAAOwJ,SAlBT,kBAqBL,CAACrB,EAAQG,EAAQ6T,cAAcC,KArB1B,sGAyBD,GACb9U,QACAoB,YACAC,UACAL,WCtGIhB,EAAQ,CACZqV,YAAa,IAGTrU,EAAU,CACdsU,eADc,SACCtV,GAEb,OAAOA,EAAMqV,aAEfA,YALc,SAKFrV,GAEV,OAAOA,EAAMqV,cAIXjU,EAAY,CAChBmU,oBADgB,SACIvV,EAAOkB,GACzBlB,EAAMqV,YAAcnU,IAIlBG,EAAU,CACRmU,mBADQ,6KACazC,EADb,EACaA,OAAQ/R,EADrB,EACqBA,QADrB,SAEgBwT,OAAYxP,aAAa,CACnD1F,OAAQ,2BACRb,OAAQ,KAJE,6CAELoC,EAFK,KAEGgB,EAFH,KAMRhB,GACFkS,EAAO,sBAAuBlR,EAAInJ,OAAOrB,MAP/B,kBASL,CAACwJ,EAAQG,EAAQsU,iBATZ,mGAaD,GACbtV,QACAoB,YACAC,UACAL,WClCF0S,aAAIC,IAAI8B,QAEO,UAAIA,OAAKC,MAAM,CAC5Btd,QAAS,CACPud,cACAZ,aACAa,U,wBCVEC,EAAS,GACTnR,EAAO9G,eAAOjB,OAAKkZ,GAEVnR,I,+BCAfgP,aAAIC,IAAImC,KAERpC,aAAI7U,SAAWkF,aAAQC,Q,mCCPjB6R,G,oBAAS,CACbE,QAAS,IACTC,UAAW,IACXC,OAAQ,GACRC,OAAQ,GACRC,UAAW,IAGN,SAASC,IAA8B,IAAbC,EAAa,uDAAJ,GAExC,GADAA,EAAoB,OAAXA,EAAkB,GAAK,GAAKA,EACjCA,EAAOxe,OAASge,EAAOM,UACzBE,EAASA,EAAOC,MAAMT,EAAOE,SAC7BM,EAAO,GAAKA,EAAO,GAChBC,MAAMT,EAAOG,WACb1C,KAAK,IACLpD,QAAQ,sBAAuB2F,EAAOG,WACzCK,EAAO,GAAKA,EAAO,IAAM,GACzBA,EAAO,GACLA,EAAO,GAAGxe,OAASge,EAAOM,UAAYE,EAAO,GAAG9X,MAAM,EAAGsX,EAAOM,WAAaE,EAAO,GAElFA,EAAO,IAAM,EACfA,EAAO,GAAKR,EAAOE,QAAUM,EAAO,GAEpCA,EAAO,GAAK,GAEdA,EAASA,EAAO,GAAKA,EAAO,OACvB,IAAe,KAAXA,EACT,MAAO,GAEPA,EAASA,EAAOC,MAAMT,EAAOE,SAC7BM,EAAO,GAAKA,EAAO,IAAM,GACzBA,EAAO,GACLA,EAAO,GAAGxe,OAASge,EAAOM,UAAYE,EAAO,GAAG9X,MAAM,EAAGsX,EAAOM,WAAaE,EAAO,GAElFA,EAAO,IAAM,IACfA,EAAO,GAAKR,EAAOE,QAAUM,EAAO,IAEtCA,EAASA,EAAO,GAAKA,EAAO,GAE9B,OAAOR,EAAOI,OAASI,EAASR,EAAOK,OCnCzCtX,aAAI+P,OAAO,UAAW,SAAC4H,GACrB,GAAY,OAARA,EAAc,MAAO,GACzB,GAAY,KAARA,EAAY,MAAO,GACvB,IAAMlf,EAAOmf,OAAOD,GACpB,OAAO7R,KAAKvD,MAAM9J,GAAQkf,EAAMH,EAAiB/e,KAGnDuH,aAAI+P,OAAO,UAAW,SAAC4H,GACrB,IAAMlf,EAAOmf,OAAOD,GACpB,OAAO7R,KAAKvD,MAAM9J,GAAQkf,EAAM7R,KAAKqH,MAAM1U,KAG7CuH,aAAI+P,OAAO,eAAgB,SAAC4H,GAC1B,IAAMlf,EAAOmf,OAAOD,GACpB,OAAO7R,KAAKvD,MAAM9J,GAAQkf,EAAe,IAATlf,EAAa,GAAKkf,IAGpD3X,aAAI+P,OAAO,QAAS,SAAC4H,GAAkC,IAA7BE,EAA6B,uDAAvB,mBAC9B,OAAOF,EAAMnH,IAAMmH,GAAKlH,OAAOoH,GAAO,KAGxC7X,aAAI+P,OAAO,cAAe,SAAC4H,EAAKtC,GAC9B,IAAIvV,EAAUE,aAAIkU,OAAO9R,QAAQgT,WAAWC,GAC5C,IAAKvV,EAAS,OAAO6X,EAErB,IAAMG,EAAShY,EAAQrH,KAAKkX,KAAK,SAAC7O,GAAD,MAAU,GAAKA,EAAKnC,QAAU,GAAKgZ,IACpE,OAAOG,EAASA,EAAOhC,MAAQ6B,ICbjC,IAAMhV,GAAIqQ,EAAQ,QAIZ+E,GAAgB/E,UAWP,SAASgF,GAAT,GAAmC,IAAlBC,EAAkB,EAAlBA,OAAQhD,EAAU,EAAVA,OAgBtC,OAfAxV,OAAOqG,KAAOA,EACdrG,OAAOkD,EAAIA,GAEXsS,GAAUN,EAAOuD,UAAUjD,GAE3BgD,GACE/e,OAAOoa,KAAK2E,GAAQpX,IAAI,SAACjD,GACvBuU,EAAMgG,eAAeva,EAAMqa,EAAOra,MAItCoC,aAAIoY,KAAOpY,aAAI7G,UAAUif,KAAO,IAAIpY,aACpCA,aAAIqY,QAAU1D,EACd3U,aAAIkU,OAAS/B,EAEN,CACLA,QACAwC,UA5BJoD,GAAczE,OAAOzS,IAAI,SAACjB,GACxB,IAAM0Y,EAAOP,GAAcnY,GAC3B,GAAI0Y,EAAKpG,OAAQ,CACf,IAAMqG,EAAgB3Y,EAAK0R,QAAQ,8BAA+B,MAClEtR,aAAIoU,UAAUkE,EAAK1a,KAAM,SAAC3C,GAAD,OACvB+X,sCAAQ,OAAC,sBAAeuF,EAAhB,gBAAD,8CCzBbzD,aAAI/E,OAAO,eAAgB,SAACpR,EAAOkZ,GACjC,OAAOlV,EAAE/C,KAAK,CAAC,oBAAqB,cAAejB,IAAU,OAG/DmW,aAAI/E,OAAO,YAAa,SAACpR,GACvB,IAAMkC,EAAM,CACV2X,EAAG,KACHC,EAAG,KACHC,EAAG,QAEL,OAAO7X,EAAIlC,KCHbqB,aAAIiX,OAAO0B,eAAgB,E,OAEDX,GAAM,CAC9BC,WACAhD,aAFM9C,G,GAAAA,MAAOwC,G,GAAAA,OAKf,IAAI3U,aAAI,CACNmS,SACAwC,UACAiE,OAAQ,SAACC,GAAD,OAAOA,EAAEC,MAChBC,OAAO,S,qBCfVpe,EAAOD,QAAU,CACfkD,KAAM,iCACNsU,QAAQ,I,qBCFVvX,EAAOD,QAAU,CACfkD,KAAM,+BACNsU,QAAQ,I,mBCHVvX,EAAOD,QAAU,CACfkD,KAAM,6BACNsU,QAAQ,I,qBCDVvX,EAAOD,QAAU,CACfkD,KAAM,kBACNsU,QAAQ,I,6FCPK,GACb,CACE8G,aAAc,MACdC,cAAe,GACfC,WAAY,IAEd,CACEF,aAAc,MACdC,cAAe,GACfC,WAAY,IAEd,CACEF,aAAc,KACdC,cAAe,GACfC,WAAY,IAEd,CACEC,SAAU,QACVH,aAAc,KACdC,cAAe,GACfC,WAAY,KCpBD,GACb,CACEF,aAAc,UACdC,cAAe,GACfC,WAAY,IAEd,CACEF,aAAc,KACdC,cAAe,GACfC,WAAY,IAEd,CACEC,SAAU,QACVH,aAAc,KACdC,cAAe,GACfC,WAAY,K,YCVV9X,EAAQ,CACZgY,UAAW,IAGPhX,EAAU,CACd4K,SADc,SACL5L,GACP,OAAOA,EAAMgY,YAIX5W,EAAY,GAEZC,EAAU,CACd4W,aADc,WAC6B/W,GAAS,IAArClB,EAAqC,EAArCA,MAAOgB,EAA8B,EAA9BA,QAASqM,EAAqB,EAArBA,SACzBnM,EACFA,EAAQzB,IAAI,SAACC,EAAMC,GACjBf,aAAI4P,IAAIxO,EAAMgY,UAAWrY,EAAOD,KAG9B,CAAC,KAAKmD,QAAQ,GAAK7B,EAAQsL,UAAUnM,QAAU,EAC5CH,EAAMgY,UAAU,IAA0C,QAApChY,EAAMgY,UAAU,GAAGJ,eAC5C5X,EAAMgY,UAAYzW,EAAE0C,MAAMiU,IAEvBlY,EAAMgY,UAAU,IAA0C,cAApChY,EAAMgY,UAAU,GAAGJ,eAC5C5X,EAAMgY,UAAYzW,EAAE0C,MAAMkU,IAGhC9K,EAAS,qBAEL+K,iBAjBQ,+KAiBWpY,EAjBX,EAiBWA,MAAOgB,EAjBlB,EAiBkBA,QAjBlB,SAkBgByQ,OAAc2G,iBAAiBpX,EAAQsL,WAlBvD,6CAkBLzL,EAlBK,KAkBGgB,EAlBH,KAmBRhB,IACEwX,EAAkB,EACtBrY,EAAMgY,UAAUvY,IAAI,SAAC6Y,GACnBzW,EAAInJ,OAAOrB,KAAKoI,IAAI,SAAC8Y,GACfD,EAAUV,eAAiBW,EAASC,kBACtCF,EAAUR,WAAaS,EAASE,qBAChCJ,EAAkB3T,KACfgU,IAAIhU,KAAKiU,UAAUN,GAAkB3T,KAAKiU,UAAUL,EAAUR,aAC9Dc,aAGwB,OAA3BN,EAAUV,eACZU,EAAUR,WAAaO,MA/BjB,kBAmCL,CAACxX,EAAQgB,IAnCJ,mGAuCD,cACb7B,QACAoB,YACAC,UACAL,Y,mBC5DFzH,EAAOD,QAAU,CACfkD,KAAM,sBACNsU,QAAQ,I,kCCFV,gBAEe,QACb+H,QAAShU,kBAAagU,QACtB5T,KAAMJ,kBAAaI,KACnB6T,QAASjU,kBAAaiU,QACtB5c,MAAO2I,kBAAa3I,Q,mBCFtB3C,EAAOD,QAAU,CACfkD,KAAM,qBACNsU,QAAQ,I,4CCNVvX,EAAOD,QAAU,CACf,CACEkF,KAAM,sBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,6BAGX,CACEjE,KAAM,sBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,6BAGX,CACEjE,KAAM,gBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,6BAGX,CACEjE,KAAM,gBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,6BAGX,CACEjE,KAAM,oBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,sCAGX,CACEjE,KAAM,oBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,sCAIX,CACEjE,KAAM,eACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,iCAGX,CACEjE,KAAM,iBACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,wBAGX,CACEjE,KAAM,WACNwU,UAAW,SAACnZ,GAAD,OAAa+X,sCAAQ,OAAC,WAAF,0CAC/ByB,KAAM,CACJ5Q,MAAO,0B,kCC9Db,yBAA2e,EAAG,G,kCCA9e,WAAMzC,EAAQ,CACZgY,UAAW,IAGPhX,EAAU,CACdmN,WADc,SACHnO,GACT,OAAOA,EAAMgY,YAIX5W,EAAY,GAEZC,EAAU,CACd0X,eADc,WAC+B7X,GAAS,IAArClB,EAAqC,EAArCA,MAAqC,EAA9BgB,QAA8B,EAArBqM,SAC/BrN,EAAMgY,UAAU9W,IAIL,cACblB,QACAoB,YACAC,UACAL,Y,mBClBFzH,EAAOD,QAAU,CACfkD,KAAM,mBACNsU,QAAQ,I,mBCNVvX,EAAOD,QAAU,CACfkD,KAAM,0BACNsU,QAAQ", "file": "js/resource-application-2021.709e44e4.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"resource-application-2021\": 0\n \t}\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"resource-application-2021\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"js/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-2b3e981c\":\"bd34a0bd\",\"chunk-2b6a8ef8\":\"eb6dde2c\",\"chunk-3fae9614\":\"f1047847\",\"chunk-40e9784f\":\"73de97be\",\"chunk-60cba748\":\"1c2a2d1d\",\"chunk-6a72cce0\":\"b11dc1e1\",\"chunk-719aca42\":\"8a472808\",\"chunk-7bb4df0c\":\"c90b4dbf\",\"chunk-909089ce\":\"b10b8d3e\",\"chunk-c61b9312\":\"43e8f713\"}[chunkId] + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"chunk-2b6a8ef8\":1,\"chunk-6a72cce0\":1,\"chunk-719aca42\":1,\"chunk-909089ce\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"css/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-2b3e981c\":\"31d6cfe0\",\"chunk-2b6a8ef8\":\"9c6268ce\",\"chunk-3fae9614\":\"31d6cfe0\",\"chunk-40e9784f\":\"31d6cfe0\",\"chunk-60cba748\":\"31d6cfe0\",\"chunk-6a72cce0\":\"ebb99186\",\"chunk-719aca42\":\"90501b8c\",\"chunk-7bb4df0c\":\"31d6cfe0\",\"chunk-909089ce\":\"b8ba40bd\",\"chunk-c61b9312\":\"31d6cfe0\"}[chunkId] + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tlinkTag.onload = resolve;\n \t\t\t\tlinkTag.onerror = function(event) {\n \t\t\t\t\tvar request = event && event.target && event.target.src || fullhref;\n \t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + request + \")\");\n \t\t\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n \t\t\t\t\terr.request = request;\n \t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\treject(err);\n \t\t\t\t};\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tvar head = document.getElementsByTagName(\"head\")[0];\n \t\t\t\thead.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\t// create error before stack unwound to get useful stacktrace later\n \t\t\t\tvar error = new Error();\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n \t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import vue from \"vue\";\r\nimport qs from \"qs\";\r\n\r\nexport default ({ path, params = {}, data, options = {} } = {}) => {\r\n  let loading = vue.$loading({\r\n    lock: true,\r\n    text: \"正在下载\",\r\n    spinner: \"el-icon-loading\",\r\n    background: \"rgba(0, 0, 0, 0.7)\",\r\n  });\r\n\r\n  let form = document.createElement(\"form\");\r\n  if (process.env.NODE_ENV === \"development\") {\r\n    params.appToken = localStorage.getItem(\"user.token\");\r\n    path = localStorage.getItem(\"server.baseUrl\") + path;\r\n  }\r\n  params = qs.stringify(params);\r\n  if (params) {\r\n    if (/\\?/g.test(path)) {\r\n      path = `${path}&${params}`;\r\n    } else {\r\n      path = `${path}?${params}`;\r\n    }\r\n  }\r\n  form.setAttribute(\"action\", path);\r\n\r\n  form.setAttribute(\"id\", \"downloadForm\");\r\n  form.setAttribute(\"style\", \"display: none;\");\r\n  form.setAttribute(\"name\", \"downloadForm\");\r\n  form.setAttribute(\"method\", options.method || \"post\");\r\n  form.setAttribute(\"target\", options.target || \"_blank\");\r\n\r\n  document.body.appendChild(form);\r\n\r\n  for (let name in data) {\r\n    if (Object.prototype.toString.call(data[name]) == \"[object Array]\") {\r\n      // 兼容数组参数\r\n      data[name].map((item, index) => {\r\n        let input = document.createElement(\"input\");\r\n        input.setAttribute(\"type\", \"text\");\r\n        input.setAttribute(\"name\", `${name}`);\r\n        input.setAttribute(\"value\", parseInt(item));\r\n        form.appendChild(input);\r\n      });\r\n    } else {\r\n      if (data[name] === null) continue;\r\n      let input = document.createElement(\"input\");\r\n      input.setAttribute(\"type\", \"text\");\r\n      input.setAttribute(\"name\", name);\r\n      input.setAttribute(\"value\", data[name]);\r\n      form.appendChild(input);\r\n    }\r\n  }\r\n  form.submit();\r\n  document.body.removeChild(form);\r\n\r\n  loading.close();\r\n};\r\n", "import applyService from \"@projects/market/resource-application-2021/resources/service/apply\";\r\n\r\nconst state = {\r\n  done: {\r\n    searchParams: {\r\n      brand: \"\",\r\n      region: \"\",\r\n      storeName: \"\",\r\n      dealerId: \"\",\r\n      applyType: \"\",\r\n      dateRange: \"\",\r\n    },\r\n    pageParams: {\r\n      limit: 10,\r\n      page: 1,\r\n      total: 0,\r\n    },\r\n    status: {\r\n      loading: false,\r\n    },\r\n    data: [],\r\n  },\r\n  observer: {\r\n    searchParams: {\r\n      brand: \"\",\r\n      region: \"\",\r\n      storeName: \"\",\r\n      dealerId: \"\",\r\n      applyType: \"\",\r\n      dateRange: \"\",\r\n    },\r\n    pageParams: {\r\n      limit: 10,\r\n      page: 1,\r\n      total: 0,\r\n    },\r\n    status: {\r\n      loading: false,\r\n    },\r\n    data: [],\r\n  },\r\n  permissionWeight: 0,\r\n};\r\n\r\nconst getters = {\r\n  hasAuthInList(state) {\r\n    return (payload) => {\r\n      return !!(\r\n        payload &&\r\n        !isNaN(payload - 1) &&\r\n        !!((state.permissionWeight >> (payload - 1)) & 1)\r\n      );\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {};\r\n\r\nconst actions = {\r\n  async getListByDone({ state, getters }) {\r\n    if (state.done.status.loading) return false;\r\n    const params = R.merge(\r\n      {\r\n        method: \"donedata\",\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      R.merge(state.done.searchParams, state.done.pageParams)\r\n    );\r\n\r\n    state.done.status.loading = true;\r\n    const [status, res] = await applyService.getApplyList(params);\r\n    state.done.status.loading = false;\r\n\r\n    if (status) {\r\n      state.done.data = res.resultLst;\r\n      state.done.pageParams.total = res.total;\r\n    }\r\n    return [status, res];\r\n  },\r\n  async getListByObserver({ state, getters }) {\r\n    if (state.observer.status.loading) return false;\r\n    const params = R.merge(\r\n      {\r\n        method: \"alldata\",\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      R.merge(state.observer.searchParams, state.observer.pageParams)\r\n    );\r\n\r\n    state.observer.status.loading = true;\r\n    const [status, res] = await applyService.getApplyList(params);\r\n    state.observer.status.loading = false;\r\n\r\n    if (status) {\r\n      state.observer.data = res.resultLst;\r\n      state.observer.pageParams.total = res.total;\r\n    }\r\n    return [status, res];\r\n  },\r\n  async getListPermission({ state, getters }) {\r\n    const params = {\r\n      executor: getters.executor,\r\n      moduleCode: `${getters.salesChannel === \"signage\" ? \"Signage\" : getters.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}.apply`,\r\n    };\r\n    const [status, res] = await applyService.getListPermission(params);\r\n    if (status) {\r\n      state.permissionWeight = res.result.weight;\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import notify from '@utils/dialog/notify'\r\n\r\nexport const Timeout = 20000\r\n\r\nexport const GoLogin = async () => {\r\n  if (process.env.NODE_ENV === 'development') {\r\n    const projectName = window.location.pathname.replace(/\\/([^/]*).*/, '$1')\r\n    if (projectName !== 'dev') {\r\n      return window.location.href = '/dev#/'\r\n    }\r\n  } else {\r\n    return top && (top.location = '/logout.do')\r\n  }\r\n}\r\n\r\nexport const StatusErrorHandler = async (e) => {\r\n  console.log(e)\r\n  const data = e.data\r\n  if (!e.data) {\r\n    // do nothing\r\n    if (e.message === 'Request failed with status code 502') {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '服务器响应失败，请联系管理人员！'\r\n      })\r\n    } else {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n      })\r\n    }\r\n  } else if (data.code === 'errorCode') {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: data.errorMsg\r\n    })\r\n  } else if (data.error && data.error.code !== 0) {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n    })\r\n  } else if (data.result && data.result.code !== 'success') {\r\n    if (data.result.code === 'invalidToken') {\r\n      return GoLogin()\r\n    }\r\n  }\r\n  return [false]\r\n}\r\n\r\n// handler method when content type equals text/html\r\nexport const HTMLContentTypeHandler = async (e) => {\r\n  // Determine if it is a login page\r\n  if (e.data.indexOf('action=\"login.do\"') > -1) {\r\n    return GoLogin()\r\n  }\r\n  // Determine if it is a login page\r\n  if (e.request.responseURL.indexOf('login.do') > -1) {\r\n    return GoLogin()\r\n  }\r\n  return [false]\r\n}\r\n", "import axios from \"axios\";\r\nimport qs from \"qs\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTMLContentTypeHandler } from \"./config\";\r\nimport vue from \"vue\";\r\n\r\nif (process.env.NODE_ENV !== \"development\") {\r\n  axios.defaults.withCredentials = true;\r\n}\r\n\r\nexport default async ({ method = \"get\", path, params = null, data = null, contentType }) => {\r\n  try {\r\n    if (process.env.NODE_ENV === \"development\") {\r\n      params = Object.assign({}, params, { appToken: localStorage.getItem(\"user.token\") });\r\n    }\r\n    // 增加随机数参数，解决 IE 缓存\r\n    params = Object.assign({}, params, { r: Math.random() });\r\n    params = method === \"get\" ? Object.assign({}, params, data) : params;\r\n    // init contentType\r\n    let contentTypeString = \"application/json; charset=utf-8\";\r\n    if (contentType === \"json\") {\r\n      contentTypeString = \"application/json; charset=utf-8\";\r\n    } else if (contentType === \"form\") {\r\n      contentTypeString = \"application/x-www-form-urlencoded; charset=utf-8\";\r\n    } else {\r\n      contentTypeString = contentType;\r\n    }\r\n    // serialize data variable\r\n    if (data && contentType === \"form\" && /put|post|patch/.test(method)) {\r\n      data = qs.stringify(data);\r\n    }\r\n\r\n    const res = await axios({\r\n      method: method,\r\n      url: \"/\" + path,\r\n      baseURL:\r\n        process.env.NODE_ENV === \"development\" ? localStorage.getItem(\"server.baseUrl\") : null,\r\n      params: params,\r\n      headers: {\r\n        \"Content-Type\": contentTypeString,\r\n        Accept: \"*/*\",\r\n      },\r\n      data: /put|post|patch/.test(method) ? data : \"\",\r\n    });\r\n    // return login.do page\r\n    if (res.headers[\"content-type\"].indexOf(\"text/html\") > -1) {\r\n      await HTMLContentTypeHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // return error status\r\n\r\n    if (\r\n      (res.data && res.data.code && [\"success\", \"0000\"].indexOf(res.data.code) < 0) ||\r\n      (res.data && res.data.error) ||\r\n      (res.data &&\r\n        res.data.result &&\r\n        res.data.result.code &&\r\n        [\"success\", \"0000\"].indexOf(res.data.result.code) < 0)\r\n    ) {\r\n      await StatusErrorHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // retrun success\r\n    return [true, res.data];\r\n  } catch (e) {\r\n    await StatusErrorHandler(e);\r\n    return [false, e.data];\r\n  }\r\n};\r\n", "import xhr from './axios'\r\n\r\nexport default xhr", "import download from './download'\r\nimport xhr from '../xhr'\r\nimport { Loading } from 'element-ui'\r\nimport { Notification } from 'element-ui'\r\n\r\nfunction downloadAsync (options = {}, attributes = {}) {\r\n  this.loading = Loading.service({lock: true,text: '正在处理下载数据'})\r\n  this.options = R.clone(options)\r\n  this.attributes = attributes\r\n  this.key = ''\r\n  this.filePath = ''\r\n  this.fileName = ''\r\n  this.run()\r\n  return [true]\r\n}\r\n\r\ndownloadAsync.prototype = {\r\n  async run () {\r\n    await this.getFileKey()\r\n    await this.checkProcess()\r\n  },\r\n  updateLoading (text) {\r\n    this.loading.text = text\r\n  },\r\n  closeLoading () {\r\n    this.loading.close()\r\n  },\r\n  async getFileKey () {\r\n    const [status, res] = await xhr(this.options)\r\n    if (status) {      \r\n      this.key = res.progressStatus.key\r\n      this.updateLoading(res.progressStatus.message)\r\n    }\r\n    return [status, res]\r\n  },\r\n  async checkProcess () {\r\n    const [status, res] = await xhr({\r\n      method: 'get',\r\n      path: 'utils/getprocessstatus.do',\r\n      contentType: \"json\",\r\n      data: {key: this.key, random: math.random()}\r\n    })\r\n    if (status && res.progressStatus && res.progressStatus.status === 'success') {\r\n      this.filePath = res.progressStatus.attrs.filePath || this.attributes.filePath\r\n      this.fileName = res.progressStatus.attrs.fileName || this.attributes.fileName\r\n      this.downloadFile()\r\n      return [true]\r\n    } else if (status && res.progressStatus && res.progressStatus.status === 'error') {\r\n      this.closeLoading()\r\n      Notification.error({\r\n        title: '错误提示',\r\n        message: res.progressStatus.message\r\n      })\r\n      return [false]\r\n    } else {\r\n      if (res.progressStatus && res.progressStatus.message) {\r\n        this.updateLoading(res.progressStatus.message)\r\n      }\r\n    }\r\n    setTimeout(() => {\r\n      this.checkProcess()\r\n    }, 5000)\r\n    return [false]\r\n  },\r\n  downloadFile () {\r\n    download({\r\n      path: '/utils/download.do',\r\n      data: {\r\n        filePath: this.filePath,\r\n        fileName: this.fileName,\r\n        deleteFile: true\r\n      },\r\n      options: {\r\n        target: '_self'\r\n      }\r\n    })\r\n    this.closeLoading()\r\n  }\r\n}\r\n// export default\r\nexport default (options, attributes) => {\r\n  return new downloadAsync(options, attributes)\r\n}\r\n", "import xhr from \"@resources/xhr\";\r\nimport download from \"@resources/utils/tools/download\";\r\nimport downloadAsync from \"@resources/utils/tools/download-async\";\r\n\r\nexport default {\r\n  requestByRPC({ method, params }) {\r\n    console.info(\"Request(RPC): \", { jsonrpc: \"2.0\", id: 2, method, params });\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: { jsonrpc: \"2.0\", id: 2, method, params },\r\n    });\r\n  },\r\n\r\n  requestByDO({ method = \"post\", path, contentType = \"json\", data, params }) {\r\n    console.info(\"Request(DO):\", { method, path, contentType, data, params });\r\n    return xhr({ method, path, contentType, data, params });\r\n  },\r\n\r\n  download({ path, data, params, options }) {\r\n    console.info(\"Download:\", { path, data, params, options });\r\n    return download({ path, data, params, options });\r\n  },\r\n\r\n  downloadAsync({ method = \"post\", path, contentType = \"json\", data, params }, attributes = {}) {\r\n    console.info(\"Download(Async):\", { method, path, contentType, data, params }, attributes);\r\n    return downloadAsync({ method, path, contentType, data, params }, attributes);\r\n  },\r\n};\r\n", "export default {\r\n  id: \"\",\r\n  reqNo: \"\",\r\n  brand: \"\",\r\n  applyType: \"\",\r\n  dealerId: null,\r\n  dealerName: \"\",\r\n  includeDMS: 0,\r\n  partnerId: \"\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  // 客户信息\r\n  storeId: \"\",\r\n  storeCooperationyear: \"\",\r\n  storeName: \"\",\r\n  storeProvince: \"\",\r\n  storeCity: \"\",\r\n  storeRegion: \"\",\r\n  storeAddress: \"\",\r\n  storeContact: \"\",\r\n  storeContacts: \"\",\r\n  storeType: \"\",\r\n  storeCubicle: \"\",\r\n  storeWorkshopId: \"\",\r\n  storeCustomerType: \"\",\r\n  // 店招信息\r\n  localMake: \"\",\r\n  signboardMaterial: \"\",\r\n  signboardStyleFirst: \"\",\r\n  signboardStyleSecond: \"\",\r\n  signboardHeight: \"\",\r\n  signboardWidth: \"\",\r\n  signboardArea: \"\",\r\n  signboardQuote: \"\",\r\n  signboardDoorQuote: \"\",\r\n  signboardDecorationQuote: \"\",\r\n  signboardRequirement: \"\",\r\n  signboardSupplierId: \"\",\r\n  // 其他信息\r\n  otherApplyReason: \"\",\r\n  otherCompleteAmount: \"\",\r\n  otherSupplierConcact: \"\",\r\n  otherSupplierConcacts: \"\",\r\n  completeTime: \"\", // 完工时间\r\n  // 附件\r\n  attAppliedVehicle: [],\r\n  attCarQrcode: [],\r\n  attOriginalSignboard: [],\r\n  attOriginalOutdoorAdSign: [],\r\n  attQuotation: [],\r\n  attStampedQuotation: [],\r\n  attDesign: [],\r\n  attApplyForm: [],\r\n  attARIBAOrder: [],\r\n  attInvoice: [],\r\n  attConfirmProof: [],\r\n  attTripleAgreement: [],\r\n  attPaymentProof: [],\r\n  attCompletion: [],\r\n  attCarAdQuali: [],\r\n  attInvoiceConfirm: [],\r\n\r\n  // 权限\r\n  createTime: new Date(),\r\n  bizPermissionWeight: \"0\",\r\n  stepCode: \"\",\r\n  unitPriceLimit: \"\",\r\n  acceptAlias: \"\",\r\n  versionNo: \"\",\r\n  abortOperationName: \"\",\r\n  rejectOperationName: \"\",\r\n  acceptOperationName: \"\",\r\n  recallOperationName: \"\",\r\n  workflowInstance: {},\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n};\r\n", "export default {\r\n  id: \"\",\r\n  reqNo: \"\",\r\n  brand: \"\",\r\n  applyType: \"SEMINAR\",\r\n  dealerId: null,\r\n  partnerId: \"\",\r\n  dealerName: \"\",\r\n  supplierId: \"\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  localMake: \"\",\r\n  // 研讨会信息\r\n  conferenceEstimatedDate: \"\",\r\n  conferenceAddress: \"\",\r\n  conferenceApplicationFee: \"\",\r\n  conferenceEstimatedPurchaseVolume: \"\",\r\n  conferenceNumberOfPeople: \"\",\r\n  conferencePlace: \"\",\r\n  conferenceQuote: \"\",\r\n  conferenceActualPurchaseVolume: \"\",\r\n  // 其他信息\r\n  otherApplyReason: \"\",\r\n  // 附件\r\n  attApplyForm: [], // 14A 申请表\r\n  attTripleAgreement: [], // 三方协议\r\n  attInvoice: [], // 发票\r\n  attInvoiceCheck: [], // 发票核验\r\n  attMeetingFlow: [], // 会议/活动流程或邀请函\r\n  attMeetingLocal: [], // 会议/活动现场照片\r\n  attMeetingSign: [], // 会议/活动参与人员签到表\r\n  attQuotation: [], // 费用明细单/报价单\r\n  attPaymentProof: [], // 经销商支付给第三方的付款证明\r\n  // 权限\r\n  createTime: new Date(),\r\n  bizPermissionWeight: \"0\",\r\n  stepCode: \"\",\r\n  acceptAlias: \"\",\r\n  versionNo: \"\",\r\n  abortOperationName: \"\",\r\n  rejectOperationName: \"\",\r\n  acceptOperationName: \"\",\r\n  recallOperationName: \"\",\r\n  quoteLimit: \"\", // 预算限额\r\n  unitPriceLimit: \"\", // 平均价格限额\r\n  workflowInstance: {},\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n};\r\n", "export default {\r\n  id: \"\",\r\n  reqNo: \"\",\r\n  organizationName:\"\",\r\n  brand: \"\",\r\n  applyType: \"\",\r\n  dealerId: null,\r\n  dealerName: \"\",\r\n  includeDMS: 0,\r\n  partnerId: \"\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  // 客户信息\r\n  storeId: \"\",\r\n  storeCooperationyear: \"\",\r\n  storeName: \"\",\r\n  storeProvince: \"\",\r\n  storeCity: \"\",\r\n  storeRegion: \"\",\r\n  storeAddress: \"\",\r\n  storeContact: \"\",\r\n  storeContacts: \"\",\r\n  storeType: \"\",\r\n  storeCubicle: \"\",\r\n  storeWorkshopId: \"\",\r\n  storeCustomerType: \"\",\r\n  // 店招信息\r\n  localMake: \"\",\r\n  signboardStyleSecond: \"\",\r\n  signboardHeight: \"\",\r\n  signboardWidth: \"\",\r\n  signboardArea: \"\",\r\n  signboardQuote: \"\",\r\n  signboardDoorQuote: \"\",\r\n  signboardDecorationQuote: \"\",\r\n  signboardRequirement: \"\",\r\n  signboardSupplierId: \"\",\r\n  // 其他信息\r\n  otherApplyReason: \"\",\r\n  otherCompleteAmount: \"\",\r\n  otherSupplierConcact: \"\",\r\n  otherSupplierConcacts: \"\",\r\n  completeTime: \"\", // 完工时间\r\n  // 附件\r\n  attQuotation: [],\r\n  attOriginalSignboard:[],\r\n  // 权限\r\n  createTime: new Date(),\r\n  bizPermissionWeight: \"0\",\r\n  stepCode: \"\",\r\n  unitPriceLimit: \"\",\r\n  acceptAlias: \"\",\r\n  versionNo: \"\",\r\n  abortOperationName: \"\",\r\n  rejectOperationName: \"\",\r\n  acceptOperationName: \"\",\r\n  recallOperationName: \"\",\r\n  workflowInstance: {},\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n  oldData:{},\r\n  storeInfo:{},\r\n  applyAttFiles:{}\r\n};\r\n", "// 赋值的属性，会取对应名字的值\r\nexport default {\r\n  id: \"\",\r\n  brand: \"\",\r\n  signType: \"applyType\",\r\n  distributorId: \"dealerId\",\r\n  distributorName: \"dealerName\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  storeId: \"\",\r\n  storeName: \"\",\r\n  localMake: \"\",\r\n  customerType: \"storeCustomerType\",\r\n  budgetAmount: \"signboardQuote\",\r\n  supplierId: \"signboardSupplierId\",\r\n  settlementAmount: \"otherCompleteAmount\",\r\n  completeTime: \"\",\r\n  productInfo: [],\r\n  storeInfo: {},\r\n  storeSignInfo: {},\r\n  applyAttFiles: {},\r\n\r\n  // 表单以外的字段\r\n  method: \"\",\r\n  remark: \"\",\r\n  executor: \"\",\r\n  salesChannel: \"\",\r\n  versionNo: \"\",\r\n\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n};\r\n", "// 赋值的属性，会取对应名字的值\r\nexport default {\r\n  id: \"\",\r\n  brand: \"\",\r\n  signType: \"applyType\",\r\n  distributorId: \"dealerId\",\r\n  distributorName: \"dealerName\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  quoteAmount: \"conferenceQuote\",\r\n  localMake: \"\",\r\n  applyBaseInfo: {},\r\n  applyAttFiles: {},\r\n  // 表单以外的字段\r\n  method: \"\",\r\n  remark: \"\",\r\n  executor: \"\",\r\n  salesChannel: \"\",\r\n  versionNo: \"\",\r\n\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n};\r\n", "// 赋值的属性，会取对应名字的值\r\nexport default {\r\n  id: \"\",\r\n  brand: \"\",\r\n  signType: \"applyType\",\r\n  distributorId: \"dealerId\",\r\n  distributorName: \"dealerName\",\r\n  costCenter: \"\",\r\n  companyCode: \"\",\r\n  storeId: \"\",\r\n  storeName: \"\",\r\n  localMake: \"\",\r\n  customerType: \"storeCustomerType\",\r\n  budgetAmount: \"signboardQuote\",\r\n  supplierId: \"signboardSupplierId\",\r\n  settlementAmount: \"otherCompleteAmount\",\r\n  completeTime: \"\",\r\n  productInfo: [],\r\n  storeInfo: {},\r\n  storeSignInfo: {},\r\n  applyAttFiles: {},\r\n\r\n  // 表单以外的字段\r\n  method: \"\",\r\n  remark: \"\",\r\n  executor: \"\",\r\n  salesChannel: \"\",\r\n  versionNo: \"\",\r\n\r\n  // 分销商字段\r\n  retailerId: '',\r\n  retailerName: '',\r\n};\r\n", "export default (source, target) => {\r\n  source.organizationName=target.organizationName\r\n  source.versionNo = target.workflowInstance.versionNo;\r\n  if(target.applyAttFiles){\r\n    target.applyAttFiles=JSON.parse(target.applyAttFiles)\r\n  }\r\n\r\n  for (let name in source) {\r\n    if (name === \"versionNo\") {\r\n      source.versionNo = target.workflowInstance.versionNo;\r\n    }else if (name === \"applyType\") {\r\n      source.applyType = target.signType || \"STORE_SIGN\";\r\n    } else if (name === \"dealerId\") {\r\n      source.dealerId = target.distributorId;\r\n    } else if (name === \"dealerName\") {\r\n      source.dealerName = target.distributorName;\r\n    }else if (/^att.*$/.test(name)) {\r\n      source[name]=[]\r\n      if(target.applyAttFiles){\r\n        source[name]=target.applyAttFiles[name]\r\n      }\r\n    } else if ([\"quoteLimit\", \"unitPriceLimit\",\"storeInfo\"].indexOf(name) > -1) {\r\n      continue;\r\n    } else {\r\n      // 其他情况\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n  if(!source.attQuotation){\r\n    source.attQuotation=[]\r\n  }\r\n  source.oldData=target\r\n  source.oldData.storeInfo = JSON.parse(target.storeInfo);\r\n  source.oldData.productInfo = JSON.parse(target.productInfo);\r\n  return source;\r\n};\r\n", "export default (target = []) => {\r\n  return target.map(item => ({\r\n    name: item.name,\r\n    attId: item.attId,\r\n    fileType: item.fileType,\r\n    sourceType: item.sourceType,\r\n    storageName: item.storageName,\r\n    storePath: item.storePath,\r\n    remoteUrl: item.remoteUrl,\r\n  }))\r\n}", "import toRequestAttFormat from \"./to-request-att\";\r\n\r\nexport default (source, target) => {\r\n  let name = \"\";\r\n\r\n  // 懒得在界面去算了，在返回给后端的时候算一次发过去，前端不需要这个值\r\n  // if (target.signboardHeight && target.signboardWidth) {\r\n  //   target.signboardArea = Math.round(target.signboardHeight && target.signboardWidth * 100) / 100;\r\n  // }\r\n\r\n  source.applyAttFiles={}\r\n  for (name in source) {\r\n    if (source[name] in target) {\r\n      source[name] = target[source[name]];\r\n    } else if (target[name]) {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n\r\n\r\n  for (name in target) {\r\n    if (name === \"id\") {\r\n      // 去掉空 id\r\n      if (target.id) {\r\n        source.id = target.id;\r\n      } else {\r\n        delete source.id;\r\n      }\r\n    }else if (/^store.*$/.test(name)) {\r\n      // storeInfo\r\n      source.storeInfo[name] = target[name];\r\n    } else if (/^signboard.*$/.test(name)) {\r\n      // storeSignInfo\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (/^other.*$/.test(name)) {\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (name === \"products\") {\r\n      // productInfo\r\n      source.productInfo = target.products;\r\n    } else if (/^att.*$/.test(name)) {\r\n      source.applyAttFiles[name] = toRequestAttFormat(target[name]);\r\n    }\r\n  }\r\n  source.applyAttFiles = JSON.stringify(source.applyAttFiles);\r\n\r\n  return source;\r\n};\r\n", "export default (source, target) => {\r\n  target.storeInfo = JSON.parse(target.storeInfo);\r\n  target.storeSignInfo = JSON.parse(target.storeSignInfo);\r\n  target.productInfo = JSON.parse(target.productInfo);\r\n  target.applyAttFiles = JSON.parse(target.applyAttFiles);\r\n\r\n  for (let name in source) {\r\n    if (name === \"versionNo\") {\r\n      source.versionNo = target.workflowInstance.versionNo;\r\n    } else if (name === \"partnerId\") {\r\n      source[name] = target.storeSignInfo[name];\r\n    } else if (name === \"includeDMS\") {\r\n      source[name] = target.storeSignInfo[name];\r\n    } else if (name === \"applyType\") {\r\n      source.applyType = target.signType || \"STORE_SIGN\";\r\n    } else if (name === \"dealerId\") {\r\n      source.dealerId = target.distributorId;\r\n    } else if (name === \"dealerName\") {\r\n      source.dealerName = target.distributorName;\r\n    } else if (/^store.*$/.test(name)) {\r\n      // storeInfo\r\n      source[name] = target.storeInfo[name];\r\n    } else if (\r\n      // storeSignInfo\r\n      /^signboard.*$/.test(name) ||\r\n      /^other.*$/.test(name)\r\n    ) {\r\n      source[name] = target.storeSignInfo[name];\r\n    } else if (/^att.*$/.test(name)) {\r\n      source[name] = target.applyAttFiles[name] || [];\r\n    } else if ([\"quoteLimit\", \"unitPriceLimit\"].indexOf(name) > -1) {\r\n      continue;\r\n    } else {\r\n      // 其他情况\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n  return source;\r\n};\r\n", "import toRequestAttFormat from \"./to-request-att\";\r\n\r\nexport default (source, target) => {\r\n  let name = \"\";\r\n\r\n  // 懒得在界面去算了，在返回给后端的时候算一次发过去，前端不需要这个值\r\n  // if (target.signboardHeight && target.signboardWidth) {\r\n  //   target.signboardArea = Math.round(target.signboardHeight && target.signboardWidth * 100) / 100;\r\n  // }\r\n\r\n  for (name in source) {\r\n    if (source[name] in target) {\r\n      source[name] = target[source[name]];\r\n    } else if (target[name]) {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n\r\n  for (name in target) {\r\n    if (name === \"id\") {\r\n      // 去掉空 id\r\n      if (target.id) {\r\n        source.id = target.id;\r\n      } else {\r\n        delete source.id;\r\n      }\r\n    } else if (name === \"partnerId\") {\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (/^store.*$/.test(name)) {\r\n      // storeInfo\r\n      source.storeInfo[name] = target[name];\r\n    } else if (/^signboard.*$/.test(name)) {\r\n      // storeSignInfo\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (/^other.*$/.test(name)) {\r\n      source.storeSignInfo[name] = target[name];\r\n    } else if (name === \"products\") {\r\n      // productInfo\r\n      source.productInfo = target.products;\r\n    } else if (/^att.*$/.test(name)) {\r\n      source.applyAttFiles[name] = toRequestAttFormat(target[name]);\r\n    }\r\n  }\r\n\r\n  source.storeInfo = JSON.stringify(source.storeInfo);\r\n  source.storeSignInfo = JSON.stringify(source.storeSignInfo);\r\n  source.productInfo = JSON.stringify(source.productInfo);\r\n  source.applyAttFiles = JSON.stringify(source.applyAttFiles);\r\n\r\n  return source;\r\n};\r\n", "export default (source, target) => {\r\n  target.salesInfo = JSON.parse(target.salesInfo || \"[]\");\r\n  target.applyBaseInfo = JSON.parse(target.applyBaseInfo || \"{}\");\r\n  target.applyAttFiles = JSON.parse(target.applyAttFiles || \"{}\");\r\n\r\n  for (let name in source) {\r\n    if (name === \"versionNo\") {\r\n      source.versionNo = target.workflowInstance.versionNo;\r\n    } else if (name === \"partnerId\") {\r\n      source[name] = target.applyBaseInfo[name];\r\n    } else if (name === \"applyType\") {\r\n      source.applyType = target.signType || \"STORE_SIGN\";\r\n    } else if (name === \"dealerId\") {\r\n      source.dealerId = target.distributorId;\r\n    } else if (name === \"dealerName\") {\r\n      source.dealerName = target.distributorName;\r\n    } else if (\r\n      /^signboard.*$/.test(name) ||\r\n      /^conference.*$/.test(name) ||\r\n      /^store.*$/.test(name) ||\r\n      /^other.*$/.test(name)\r\n    ) {\r\n      // applyBaseInfo\r\n      source[name] = target.applyBaseInfo[name];\r\n    } else if (/^att.*$/.test(name)) {\r\n      source[name] = target.applyAttFiles[name] || [];\r\n    }\r\n    // 其他情况\r\n    else {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n  return source;\r\n};\r\n", "import toRequestAttFormat from \"./to-request-att\";\r\n\r\nexport default (source, target) => {\r\n  let name = \"\";\r\n\r\n  // 懒得在界面去算了，在返回给后端的时候算一次发过去，前端不需要这个值\r\n  if (target.signboardHeight && target.signboardWidth) {\r\n    target.signboardArea = Math.round(target.signboardHeight && target.signboardWidth * 100) / 100;\r\n  }\r\n\r\n  for (name in source) {\r\n    if (source[name] in target) {\r\n      source[name] = target[source[name]];\r\n    } else if (target[name]) {\r\n      source[name] = target[name];\r\n    }\r\n  }\r\n\r\n  for (name in target) {\r\n    if (name === \"id\") {\r\n      // 去掉空 id\r\n      if (target.id) {\r\n        source.id = target.id;\r\n      } else {\r\n        delete source.id;\r\n      }\r\n    } else if (name === \"partnerId\") {\r\n      source.applyBaseInfo[name] = target[name];\r\n    } else if (/^conference.*$/.test(name) || /^other.*$/.test(name)) {\r\n      // applyBaseInfo\r\n      if (target[name] !== undefined || target[name] !== \"\") {\r\n        source.applyBaseInfo[name] = target[name];\r\n      }\r\n    } else if (/^att.*$/.test(name)) {\r\n      source.applyAttFiles[name] = toRequestAttFormat(target[name]);\r\n    }\r\n  }\r\n\r\n  source.applyBaseInfo = JSON.stringify(source.applyBaseInfo);\r\n  source.applyAttFiles = JSON.stringify(source.applyAttFiles);\r\n\r\n  return source;\r\n};\r\n", "import LocalSignage from \"./_values/local-signage\";\r\nimport LocalSeminar from \"./_values/local-seminar\";\r\nimport LocalCk from \"./_values/local-ck\";\r\nimport RequestSignage from \"./_values/request-signage\";\r\nimport RequestSeminar from \"./_values/request-seminar\";\r\nimport RequestCk from \"./_values/request-ck\";\r\nimport applyService from \"@projects/market/resource-application-2021/resources/service/apply\";\r\nimport toCkLocal from \"./_func/to-ck-local\";\r\nimport toCkRequest from \"./_func/to-ck-request\";\r\nimport toSignageLocal from \"./_func/to-signage-local\";\r\nimport toSignageRequest from \"./_func/to-signage-request\";\r\nimport toSeminarLocal from \"./_func/to-seminar-local\";\r\nimport toSeminarRequest from \"./_func/to-seminar-request\";\r\nimport vue from \"vue\";\r\nimport De from \"element-ui/src/locale/lang/de\";\r\n\r\nconst state = {\r\n  signage: Object.assign({}, LocalSignage),\r\n  seminar: Object.assign({}, LocalSeminar),\r\n  ck: Object.assign({}, LocalCk),\r\n};\r\n\r\nconst getters = {\r\n  applyForm(state, getters) {\r\n    return state[getters.salesChannel];\r\n  },\r\n  hasAuthInBiz(state, getters) {\r\n    return (payload) => {\r\n      payload = math.log(payload, 2) + 1;\r\n\r\n      let weight = state[getters.salesChannel].bizPermissionWeight;\r\n      let bit = 0;\r\n      let result = false;\r\n      if (payload > 32) {\r\n        if (weight >= 4294967296) {\r\n          weight = parseInt(weight / 4294967296);\r\n          payload = payload - 32;\r\n          bit = weight >>> (payload - 1);\r\n        } else {\r\n          bit = 0;\r\n        }\r\n      } else {\r\n        bit = weight >>> (payload - 1);\r\n      }\r\n\r\n      result = !!(payload && !isNaN(payload - 1) && !!(bit & 1));\r\n      return result;\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  CLEAR_APPLY_FORM(state, payload) {\r\n    if (payload.salesChannel === \"signage\") {\r\n      state.signage = R.clone(LocalSignage);\r\n    }else if (payload.salesChannel === \"ck\") {\r\n      state.ck = R.clone(LocalCk);\r\n    } else {\r\n      state.seminar = R.clone(LocalSeminar);\r\n    }\r\n  },\r\n  SET_STORE_INFO(state, payload) {\r\n    state.signage.storeId = payload.id || \"\";\r\n    state.signage.storeName = payload.workshopName || \"\";\r\n    state.signage.storeAddress = payload.workshopAddress || \"\";\r\n    state.signage.storeContacts = payload.contactPerson || \"\";\r\n    state.signage.storeContact = payload.contactPersonTel || \"\";\r\n    state.signage.storeWorkshopId = payload.id;\r\n    state.signage.storeProvince = payload.provinceName || \"\";\r\n    state.signage.storeCity = payload.cityName || \"\";\r\n    state.signage.storeType = payload.type || \"\";\r\n    state.signage.storeCubicle = payload.seatsNum || \"\";\r\n    state.signage.storeRegion = payload.regionName;\r\n    state.signage.storeCustomerType = payload.customerType;\r\n  },SET_CK_STORE_INFO(state, payload) {\r\n    state.ck.storeId = payload.id || \"\";\r\n    state.ck.storeName = payload.workshopName || \"\";\r\n    state.ck.storeAddress = payload.workshopAddress || \"\";\r\n    state.ck.storeContacts = payload.contactPerson || \"\";\r\n    state.ck.storeContact = payload.contactPersonTel || \"\";\r\n    state.ck.storeWorkshopId = payload.id;\r\n    state.ck.storeProvince = payload.provinceName || \"\";\r\n    state.ck.storeCity = payload.cityName || \"\";\r\n    state.ck.storeType = payload.type || \"\";\r\n    state.ck.storeCubicle = payload.seatsNum || \"\";\r\n    state.ck.storeRegion = payload.regionName;\r\n    state.ck.storeCustomerType = payload.customerType;\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getApplyFormById({ getters, dispatch }, payload) {\r\n    const [status, res] = await applyService.getApplyFormById({\r\n      id: payload.id,\r\n      stepCode: payload.stepCode,\r\n      executor: getters.executor,\r\n      salesChannel: getters.salesChannel,\r\n    });\r\n    if (status) {\r\n      if (getters.salesChannel === \"signage\") {\r\n        toSignageLocal(getters.applyForm, R.merge(res.result.form, res.result.currentStep));\r\n        // 更新产品信息\r\n        dispatch(\"initProducts\", JSON.parse(res.result.form.productInfo));\r\n      }else if (getters.salesChannel === \"ck\") {\r\n        toCkLocal(getters.applyForm, R.merge(res.result.form, res.result.currentStep));\r\n        // 更新产品信息\r\n        dispatch(\"initCkProducts\", JSON.parse(res.result.form.productInfo));\r\n      } else {\r\n        toSeminarLocal(getters.applyForm, R.merge(res.result.form, res.result.currentStep));\r\n      }\r\n    }\r\n    return [status, res];\r\n  },\r\n  async savaApplyForm({ getters }) {\r\n    let target = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      getters.applyForm\r\n    );\r\n\r\n    if (getters.salesChannel === \"signage\") {\r\n      target.products = R.clone(getters.products);\r\n    }else if (getters.salesChannel === \"ck\") {\r\n      target.products = R.clone(getters.products);\r\n    } else {\r\n      target.sales = R.clone(getters.sales);\r\n    }\r\n\r\n    const params =\r\n      getters.salesChannel === \"signage\"\r\n        ? toSignageRequest(R.clone(RequestSignage), target)\r\n        : getters.salesChannel === \"ck\" ? toCkRequest(R.clone(RequestCk), target)\r\n          : toSeminarRequest(R.clone(RequestSeminar), target);\r\n\r\n    const [status, res] = await applyService.savaApplyForm(params);\r\n    if (status) {\r\n      getters.applyForm.id = res.result.id;\r\n    }\r\n    return [status, res];\r\n  },\r\n  async operationApplyForm({ getters }, payload) {\r\n    let target = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      R.merge(getters.applyForm, payload)\r\n    );\r\n\r\n    if (getters.salesChannel === \"signage\") {\r\n      target.products = R.clone(getters.products);\r\n    }else if (getters.salesChannel === \"ck\") {\r\n      target.products = R.clone(getters.ckProducts);\r\n    }  else {\r\n      target.sales = R.clone(getters.sales);\r\n    }\r\n\r\n    const params =\r\n      getters.salesChannel === \"signage\"\r\n        ? toSignageRequest(R.clone(RequestSignage), target)\r\n        : getters.salesChannel === \"ck\"\r\n          ? toCkRequest(R.clone(RequestCk), target)\r\n          :toSeminarRequest(R.clone(RequestSeminar), target);\r\n    debugger\r\n    const [status, res] = await applyService.operationApplyForm(params);\r\n    return [status, res];\r\n  },\r\n  async abortApplyForm({ getters, rootState }, payload) {\r\n    let params = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.abortApplyForm(params);\r\n    if (status) {\r\n      rootState.list.observer.data.find((item, index) => {\r\n        if (item.id === payload.id) {\r\n          vue.set(rootState.list.observer.data[index], \"formStatus\", 5);\r\n        }\r\n      });\r\n    }\r\n    return [status, res];\r\n  },\r\n  async deleteApplyForm({ getters, rootState }, payload) {\r\n    let params = R.merge(\r\n      {\r\n        executor: getters.executor,\r\n        salesChannel: getters.salesChannel,\r\n      },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.deleteApplyForm(params);\r\n    if (status) {\r\n      rootState.list.todo.data = rootState.list.todo.data.filter((item) => item.id !== payload.id);\r\n    }\r\n    return [status, res];\r\n  },\r\n  async exportPDF({}, payload) {\r\n    const [status, res] = await applyService.exportPDF(payload);\r\n    return [status, res];\r\n  },\r\n  exportExcel({ getters }, payload) {\r\n    const params = R.merge(\r\n      {\r\n        packageName: getters.salesChannel === \"signage\" ? \"signageapply2021\" : \"seminarapply2021\",\r\n      },\r\n      payload\r\n    );\r\n\r\n    applyService.exportExcel(params);\r\n  },\r\n  async getFundDetail({ getters }, payload) {\r\n    const params = R.merge(\r\n      {\r\n        brand: getters.applyForm.brand,\r\n        dealerId: getters.applyForm.dealerId,\r\n        applyType: getters.applyForm.applyType,\r\n        salesChannel: getters.salesChannel,\r\n        id: getters.applyForm.id,\r\n      },\r\n      payload\r\n    );\r\n\r\n    const [status, res] = await applyService.getFundDetail(params);\r\n    return [status, res];\r\n  },\r\n  async getCostCenter({ getters }) {\r\n    const [status, res] = await applyService.getCostCenter(getters.applyForm);\r\n    if (status) {\r\n      getters.applyForm.costCenter = res.result.data.costCenter;\r\n      getters.applyForm.companyCode = res.result.data.companyCode;\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import xhr from \"@utils/xhr\";\r\nimport dayjs from \"dayjs\";\r\nimport download from \"@utils/tools/download\";\r\n\r\nconst convertForm = (data) => {\r\n  let form = {\r\n    brand: data.brand,\r\n    signType: data.signType,\r\n    distributorId: data.distributorId,\r\n    distributorName: data.distributorName,\r\n    costCenter: data.costCenter,\r\n    companyCode: data.companyCode,\r\n    localMake: data.localMake,\r\n    retailerId: data.retailerId,\r\n    retailerName: data.retailerName,\r\n  };\r\n  if (data.salesChannel === \"signage\") {\r\n    form.partnerId = data.partnerId;\r\n    form.storeId = data.storeId;\r\n    form.storeName = data.storeName;\r\n    form.budgetAmount = data.budgetAmount;\r\n    form.settlementAmount = data.settlementAmount;\r\n    if (data.supplierId) form.supplierId = data.supplierId;\r\n    // JSON 字符串\r\n    form.storeInfo = data.storeInfo;\r\n    form.storeSignInfo = data.storeSignInfo;\r\n    form.productInfo = data.productInfo;\r\n    form.applyAttFiles = data.applyAttFiles;\r\n  }else if (data.salesChannel === \"ck\") {\r\n    form.storeId = data.storeId;\r\n    form.storeName = data.storeName;\r\n    if (data.supplierId) form.supplierId = data.supplierId;\r\n    // JSON 字符串\r\n    // form.storeInfo = JSON.stringify(data.storeInfo);\r\n    // form.productInfo = JSON.stringify(data.productInfo);\r\n    form.applyAttFiles = data.applyAttFiles;\r\n  } else {\r\n    form.quoteAmount = data.quoteAmount;\r\n    // JSON 字符串\r\n    form.applyBaseInfo = data.applyBaseInfo;\r\n    form.applyAttFiles = data.applyAttFiles;\r\n  }\r\n  if (data.id) form.id = data.id;\r\n  return form;\r\n};\r\n\r\nclass Service {\r\n  getApplyFormById(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.detail`,\r\n        params: [data.id, data.stepCode, data.executor],\r\n      },\r\n    });\r\n  }\r\n\r\n  getSignboardMaterial(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.querySignageMaterialType\",\r\n        params: [data.brand, data.applyType, data.localMake],\r\n      },\r\n    });\r\n  }\r\n\r\n  savaApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.save`,\r\n        params: [convertForm(data), data.executor],\r\n      },\r\n    });\r\n  }\r\n\r\n  operationApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.${\r\n          data.method\r\n        }`,\r\n        params: [convertForm(data), data.remark, data.executor, data.versionNo],\r\n      },\r\n    });\r\n  }\r\n\r\n  abortApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}ApplyService.${\r\n          data.method\r\n        }`,\r\n        params: [\r\n          {\r\n            id: data.id,\r\n          },\r\n        ],\r\n      },\r\n    });\r\n  }\r\n\r\n  deleteApplyForm(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: `mkt${data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"}apply/delDraft.do`,\r\n      contentType: \"form\",\r\n      data: {\r\n        id: data.id,\r\n      },\r\n    });\r\n  }\r\n\r\n  getListPermission(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"operationPermissionService.getOperationPermissionByUser\",\r\n        params: [data.executor, data.moduleCode],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  getSeminarTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerSeminarTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getSignageTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerSignageTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getSignageStoreTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerStoreSignageTips\",\r\n        params: [data.partnerId, data.brand, data.storeId],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getOverallPerformanceTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerOverallPerformanceTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getCostCenter(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryDistributorCostCenter\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  getApplyList(data = {}) {\r\n    const startDate = data.dateRange\r\n      ? dayjs(data.dateRange[0]).format(\"YYYY-MM-DD 00:00:00\")\r\n      : \"\";\r\n    const endDate = data.dateRange ? dayjs(data.dateRange[1]).format(\"YYYY-MM-DD 23:59:59\") : \"\";\r\n\r\n    return xhr({\r\n      method: \"post\",\r\n      path: `mkt${data.salesChannel === \"signage\" ? \"signage\" :  data.salesChannel === \"ck\" ?\"ck\":\"seminar\"}apply/${data.method}.do`,\r\n      contentType: \"json\",\r\n      data: {\r\n        limit: data.limit,\r\n        start: (data.page - 1) * data.limit,\r\n        field: \"id\",\r\n        direction: \"DESC\",\r\n        distributorId: data.dealerId,\r\n        region: data.region,\r\n        brand: data.brand,\r\n        storeName: data.storeName,\r\n        signType: data.applyType,\r\n        startApplyTime: startDate,\r\n        endApplyTime: endDate,\r\n        executor: data.executor,\r\n      },\r\n    });\r\n  }\r\n\r\n  getReviewProcess(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${\r\n          data.salesChannel === \"signage\" ? \"Signage\" :  data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"\r\n        }ApplyService.getWorkflowStepInstances`,\r\n        params: [data.id],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  getReviewHistory(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: `mkt${\r\n          data.salesChannel === \"signage\" ? \"Signage\" : data.salesChannel === \"ck\" ?\"Ck\":\"Seminar\"\r\n        }ApplyService.getWorkflowStepHistory`,\r\n        params: [data.id],\r\n        id: 2,\r\n      },\r\n    });\r\n  }\r\n\r\n  exportPDF(data = {}) {\r\n    if (data.type === \"ci\") {\r\n      return download({\r\n        path: \"/mktsignage/export/finalPDF.do\",\r\n        params: {\r\n          id: data.id,\r\n        },\r\n      });\r\n    } else if (data.type === \"cdm\") {\r\n      return download({\r\n        path: \"/mktseminar/export/finalPDF.do\",\r\n        params: {\r\n          id: data.id,\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  exportExcel(data = {}) {\r\n    return download({\r\n      path: \"/reportview/excel/export.do\",\r\n      data: {\r\n        packageName: data.packageName,\r\n        viewName: data.viewName,\r\n        fileName: data.fileName,\r\n        columnInfoDictKey: data.columnInfoDictKey,\r\n        pageType: data.pageType,\r\n      },\r\n    });\r\n  }\r\n\r\n  getFundDetail(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: `mkt/cio/getNewFundDetail.do`,\r\n      contentType: \"form\",\r\n      params: {\r\n        partnerId: data.dealerId,\r\n        mktType: data.applyType,\r\n        salesChannel: [\"1\"].indexOf(\"\" + data.brand) > -1 ? \"Consumer2021\" : \"Commercial2021\",\r\n        mktId: data.id,\r\n      },\r\n    });\r\n  }\r\n\r\n  getPartnerCkShopTips(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"mktCkApplyService.queryPartnerCkShopTips\",\r\n        params: [data.dealerId],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n}\r\n\r\nexport default new Service();\r\n", "import xhr from \"@utils/xhr\";\r\n\r\nclass Service {\r\n  getSuppliers(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        id: 2,\r\n        jsonrpc: \"2.0\",\r\n        method: \"supplierService.getSuppliers\",\r\n        params: [data.partnerId, \"16\"],\r\n      },\r\n    });\r\n  }\r\n\r\n  getProductsSales(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"wxPublicRpc.do\",\r\n      contentType: \"json\",\r\n      data: {\r\n        jsonrpc: \"2.0\",\r\n        method: \"commonService.queryPartnerProductOverallPerformanceTips\",\r\n        params: [data.partnerId, data.brand],\r\n        id: 1,\r\n      },\r\n    });\r\n  }\r\n\r\n  getDealersByKeyword(data = {}) {\r\n    if (data.urlPath) {\r\n      data.urlPath = data.urlPath.replace(/\\//g, \"-\");\r\n    } else {\r\n      data.urlPath = \"\";\r\n    }\r\n    return xhr({\r\n      method: \"get\",\r\n      path: \"partnerController/queryPartnerForCtrl.do\",\r\n      contentType: \"form\",\r\n      params: {\r\n        partnerName: data.partnerName,\r\n        resourceId: `resource-application${data.urlPath}`,\r\n      },\r\n    });\r\n  }\r\n\r\n  getStoreByDealerId(data = {}) {\r\n    return xhr({\r\n      method: \"post\",\r\n      path: \"workshopmaster/mktApply/list.do\",\r\n      contentType: \"form\",\r\n      data: {\r\n        start: data.pageSize * (data.page - 1),\r\n        limit: data.pageSize,\r\n        queryType: 2,\r\n        workshopName: data.keyword,\r\n        partnerId: data.partnerId,\r\n        retailerId: data.retailerId,\r\n        status: 3,\r\n        brand: data.brand,\r\n        funFlag: data.funFlag,\r\n        resourceId: \"cdmMktApplySave\",\r\n        fromSource: \"3\",\r\n        businessWeight: \"2\",\r\n        mktKey: data.applyType,\r\n        oldMktKey: data.applyType,\r\n        pageIndex: 0,\r\n        field: \"id\",\r\n        direction: \"DESC\",\r\n      },\r\n    });\r\n  }\r\n}\r\n\r\nexport default new Service();\r\n", "/**\r\n * update: 2021-01-15 11:07\r\n * 该组件通过参数获取经销商数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-dealer\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "import commonService from \"@projects/market/resource-application-2021/resources/service/common\";\r\n\r\nconst state = {\r\n  store: {\r\n    title: \"选择客户\",\r\n    show: false,\r\n    params: {},\r\n    list: {\r\n      total: 0,\r\n      loading: false,\r\n      loadingText: \"正在加载数据\",\r\n      data: [],\r\n      page: 1,\r\n      pageSize: 10,\r\n    },\r\n  },\r\n  signboardStyle: {\r\n    title: \"店招样式展示\",\r\n    show: false,\r\n    params: {},\r\n  },\r\n  appliedVehicle: {\r\n    title: \"申请车辆照片展示\",\r\n    show: false,\r\n    params: {},\r\n  },\r\n};\r\n\r\nconst getters = {};\r\n\r\nconst mutations = {\r\n  SHOW_DIALOG(state, payload) {\r\n    let name = payload.dialogName;\r\n\r\n    if (!name) return false;\r\n\r\n    state[name].show = true;\r\n    state[name].title = payload.title || state[name].title;\r\n    state[name].params = payload.params;\r\n\r\n    if (state[name].list) {\r\n      state[name].list.page = 1;\r\n      state[name].list.total = 0;\r\n    }\r\n  },\r\n  CLEAR_STORE_LIST(state) {\r\n    state.store.list.total = 0;\r\n    state.store.list.page = 1;\r\n  },\r\n  HIDE_DIALOG(state, payload) {\r\n    let name = payload.dialogName;\r\n\r\n    if (!name) return false;\r\n\r\n    state[name].show = false;\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getStoreByDealerId({ state, getters }, payload) {\r\n    let params = {};\r\n\r\n    if (state.store.list.loading) return false;\r\n\r\n    state.store.list.data = [];\r\n\r\n    params = R.merge(\r\n      {\r\n        page: state.store.list.page,\r\n        pageSize: state.store.list.pageSize,\r\n        applyType: getters.applyForm.applyType,\r\n        funFlag: \"mktCi\",\r\n        salesChannel: getters.salesChannel,\r\n        partnerId: getters.applyForm.retailerId || getters.applyForm.partnerId,\r\n        brand: getters.applyForm.brand,\r\n      },\r\n      payload\r\n    );\r\n\r\n    state.store.list.loading = true;\r\n    const [status, res] = await commonService.getStoreByDealerId(params);\r\n    state.store.list.loading = false;\r\n\r\n    if (status) {\r\n      state.store.list.data = res.resultLst;\r\n      state.store.list.total = res.total;\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import commonService from \"@projects/market/resource-application-2021/resources/service/common\";\r\n\r\nconst state = {\r\n  salesChannel: \"\",\r\n  executor: \"\",\r\n  previewForm: false, // 是否是预览表单\r\n};\r\n\r\nconst getters = {\r\n  salesChannel(state) {\r\n    return state.salesChannel;\r\n  },\r\n  executor(state) {\r\n    return state.executor;\r\n  },\r\n  previewForm(state) {\r\n    return state.previewForm;\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  SET_GLOBAL(state, payload) {\r\n    for (let name in payload) {\r\n      state[name] = payload[name];\r\n    }\r\n  },\r\n};\r\n\r\nconst actions = {};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "module.exports = {\r\n  apply: require(\"./resources/storeModules/apply\").default,\r\n  list: require(\"./resources/storeModules/list\").default,\r\n  dialog: require(\"./resources/storeModules/dialog\").default,\r\n  global: require(\"./resources/storeModules/global\").default,\r\n  products: require(\"./resources/storeModules/products\").default,\r\n  ckProducts: require(\"./resources/storeModules/ck_products\").default,\r\n};\r\n", "import notify from '@utils/dialog/notify'\r\n\r\nexport const Timeout = 20000\r\n\r\nexport const GoLogin = async () => {\r\n  if (process.env.NODE_ENV === 'development') {\r\n    const projectName = window.location.pathname.replace(/\\/([^/]*).*/, '$1')\r\n    if (projectName !== 'dev') {\r\n      return window.location.href = '/dev#/'\r\n    }\r\n  } else {\r\n    return top && (top.location = '/logout.do')\r\n  }\r\n}\r\n\r\nexport const StatusErrorHandler = async (e) => {\r\n  console.log(e)\r\n  const data = e.data\r\n  if (!e.data) {\r\n    // do nothing\r\n    if (e.message === 'Request failed with status code 502') {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '服务器响应失败，请联系管理人员！'\r\n      })\r\n    } else {\r\n      notify.error({\r\n        title: '错误提示',\r\n        duration: 5000,\r\n        message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n      })\r\n    }\r\n  } else if (data.code === 'errorCode') {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: data.errorMsg\r\n    })\r\n  } else if (data.error && data.error.code !== 0) {\r\n    notify.error({\r\n      title: '错误提示',\r\n      duration: 5000,\r\n      message: '网络异常或者服务器响应失败，请稍后重新尝试！'\r\n    })\r\n  } else if (data.result && data.result.code !== 'success') {\r\n    if (data.result.code === 'invalidToken') {\r\n      return GoLogin()\r\n    }\r\n  }\r\n  return [false]\r\n}\r\n\r\n// handler method when content type equals text/html\r\nexport const HTMLContentTypeHandler = async (e) => {\r\n  // Determine if it is a login page\r\n  if (e.data.indexOf('action=\"login.do\"') > -1) {\r\n    return GoLogin()\r\n  }\r\n  // Determine if it is a login page\r\n  if (e.request.responseURL.indexOf('login.do') > -1) {\r\n    return GoLogin()\r\n  }\r\n  return [false]\r\n}\r\n", "import axios from \"axios\";\r\nimport qs from \"qs\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTMLContentTypeHandler } from \"./config\";\r\nimport vue from \"vue\";\r\n\r\nif (process.env.NODE_ENV !== \"development\") {\r\n  axios.defaults.withCredentials = true;\r\n}\r\n\r\nexport default async ({ method = \"get\", path, params = null, data = null, contentType }) => {\r\n  try {\r\n    if (process.env.NODE_ENV === \"development\") {\r\n      params = Object.assign({}, params, { appToken: localStorage.getItem(\"user.token\") });\r\n    }\r\n    // 增加随机数参数，解决 IE 缓存\r\n    params = Object.assign({}, params, { r: Math.random() });\r\n    params = method === \"get\" ? Object.assign({}, params, data) : params;\r\n    // init contentType\r\n    let contentTypeString = \"application/json; charset=utf-8\";\r\n    if (contentType === \"json\") {\r\n      contentTypeString = \"application/json; charset=utf-8\";\r\n    } else if (contentType === \"form\") {\r\n      contentTypeString = \"application/x-www-form-urlencoded; charset=utf-8\";\r\n    }\r\n    // serialize data variable\r\n    if (data && contentType === \"form\" && /put|post|patch/.test(method)) {\r\n      data = qs.stringify(data);\r\n    }\r\n    const res = await axios({\r\n      method: method,\r\n      url: \"/\" + path,\r\n      baseURL:\r\n        process.env.NODE_ENV === \"development\" ? localStorage.getItem(\"server.baseUrl\") : null,\r\n      params: params,\r\n      headers: {\r\n        \"Content-Type\": contentTypeString,\r\n        Accept: \"*/*\",\r\n      },\r\n      data: /put|post|patch/.test(method) ? data : \"\",\r\n    });\r\n    // return login.do page\r\n    if (res.headers[\"content-type\"].indexOf(\"text/html\") > -1) {\r\n      await HTMLContentTypeHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // return error status\r\n\r\n    if (\r\n      (res.data && res.data.code && [\"success\", \"0000\"].indexOf(res.data.code) < 0) ||\r\n      (res.data && res.data.error) ||\r\n      (res.data &&\r\n        res.data.result &&\r\n        res.data.result.code &&\r\n        [\"success\", \"0000\"].indexOf(res.data.result.code) < 0)\r\n    ) {\r\n      await StatusErrorHandler(res);\r\n      return [false, res.data];\r\n    }\r\n    // retrun success\r\n    return [true, res.data];\r\n  } catch (e) {\r\n    await StatusErrorHandler(e);\r\n    return [false, e.data];\r\n  }\r\n};\r\n", "import xhr from './axios'\r\n\r\nexport default xhr", "var map = {\n\t\"./customize/files/register.conf.js\": \"ad3b\",\n\t\"./customize/popconfirm/register.conf.js\": \"f315\",\n\t\"./select/brand/brand-by-channel/register.conf.js\": \"9300\",\n\t\"./select/dealer/dealer-by-resourceId/register.conf.js\": \"7d2f\",\n\t\"./select/dealer/dealer-by-sales/register.conf.js\": \"3c5c\",\n\t\"./select/dealer/retailer-by-distributor/register.conf.js\": \"d54d\",\n\t\"./select/dict-options/register.conf.js\": \"998a\",\n\t\"./select/number/register.conf.js\": \"eea6\",\n\t\"./select/options/register.conf.js\": \"5c33\",\n\t\"./select/region/region-by-resourceId/register.conf.js\": \"6f10\",\n\t\"./select/user/user-by-resourceId/register.conf.js\": \"8e75\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"54be\";", "/**\r\n * update: 2021-01-19 14:38\r\n * 该组件允许传输自定义方法获取下拉选项，或者直接传输 options 作为选项\r\n * getOptions 与 options 同时存在时，取两者的并集\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-options\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-03-15 17:09\r\n * 该组件通过参数获取区域\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-region-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  watch: {\r\n    $route(to, from) {\r\n      let globalConfig = {};\r\n      if (to.query.executor) {\r\n        globalConfig.executor = to.query.executor;\r\n      }\r\n      if (/^\\/signage\\/.*$/.test(to.path)) {\r\n        globalConfig.salesChannel = \"signage\";\r\n      }\r\n      if (/^\\/seminar\\/.*$/.test(to.path)) {\r\n        globalConfig.salesChannel = \"seminar\";\r\n      }\r\n      if (/^\\/ck\\/.*$/.test(to.path)) {\r\n        globalConfig.salesChannel = \"ck\";\r\n      }\r\n      this.$store.commit(\"SET_GLOBAL\", globalConfig);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\nbody {\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n  margin: 0;\r\n  background-color: #f3f3f4;\r\n  padding: 15px;\r\n}\r\n#app {\r\n  box-sizing: border-box;\r\n  padding: 15px;\r\n  background-color: #fff;\r\n  min-width: 600px;\r\n  max-width: 1800px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./app.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./app.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./app.vue?vue&type=template&id=03a96fda&\"\nimport script from \"./app.vue?vue&type=script&lang=js&\"\nexport * from \"./app.vue?vue&type=script&lang=js&\"\nimport style0 from \"./app.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "/**\r\n * replace document title\r\n */\r\nexport default to => {\r\n  let titles = [];\r\n  let matched = to.matched;\r\n\r\n  matched.slice().forEach(handler => {\r\n    let title = handler.meta.title;\r\n    title && titles.push(title);\r\n  });\r\n\r\n  let title = titles.join(\" · \");\r\n  document.title = title;\r\n};\r\n", "import docTitleReplacer from \"./afterEach/doc-title-replacer.js\";\r\n\r\nexport default router => {\r\n  router.afterEach(docTitleReplacer);\r\n};\r\n", "import Vue from \"vue\";\r\nimport hooks from \"./hooks\";\r\nimport Router from \"vue-router\";\r\n\r\nVue.use(Router);\r\nconst router = new Router({\r\n  mode: \"hash\",\r\n  routes: []\r\n});\r\n\r\nhooks(router);\r\n\r\nexport default router;\r\n", "import coreService from \"@resources/service/core\";\r\nimport vue from \"vue\";\r\n\r\nconst timeInterval = 300000;\r\n\r\nconst state = {\r\n  options: [],\r\n};\r\n\r\nconst getters = {\r\n  getOptions(state) {\r\n    return (dictName) => {\r\n      return state.options.find((options) => options.name === dictName) || {};\r\n    };\r\n  },\r\n  getOptionsData(_, getters) {\r\n    return (dictName) => {\r\n      const options = getters.getOptions(dictName);\r\n      return options ? options.data : [];\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  UPDATE_OPTIONS(state, payload) {\r\n    // 刷新更新时间\r\n    payload.updateTime = new Date().getTime();\r\n\r\n    const options = state.options.find((item) => {\r\n      return item.name === payload.name && Object.assign(item, payload);\r\n    });\r\n    !options && state.options.push(payload);\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getDictOptions({ commit, getters }, dictName) {\r\n    const options = getters.getOptions(dictName);\r\n    if (options && new Date().getTime() - options.updateTime <= timeInterval) {\r\n      // 小于等于 timeInterval 的时间间隔内不会重新获取\r\n      return [true, options];\r\n    } else {\r\n      commit(\"UPDATE_OPTIONS\", { name: dictName, status: \"loading\", data: [] });\r\n    }\r\n\r\n    const [status, res] = await coreService.requestByRPC({\r\n      method: \"dicService.getDicItemByDicTypeCode\",\r\n      params: [dictName],\r\n    });\r\n    if (status) {\r\n      commit(\"UPDATE_OPTIONS\", {\r\n        name: dictName,\r\n        status: \"loaded\",\r\n        data: res.result.data.map((item) => ({\r\n          value: \"\" + item.dicItemCode,\r\n          label: item.dicItemName,\r\n        })),\r\n      });\r\n    }\r\n    return [status, getters.getOptions(dictName)];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import coreService from \"@resources/service/core\";\r\n\r\nconst timeInterval = 300000;\r\n\r\nconst state = {\r\n  permissions: [],\r\n};\r\n\r\nconst getters = {\r\n  getPermission(state) {\r\n    // 获取权限对象\r\n    return (permissionName) => {\r\n      return state.permissions.find((permission) => permission.name === permissionName) || {};\r\n    };\r\n  },\r\n  getPermissionData(_, getters) {\r\n    // 获取权限对象的值\r\n    return (permissionName) => {\r\n      const permission = getters.getPermission(permissionName);\r\n      return permission ? permission.data : 0;\r\n    };\r\n  },\r\n  hasPermission(_, getters) {\r\n    // 权限值是否包含 payload 参数\r\n    return (permissionName) => {\r\n      const permission = getters.getPermission(permissionName);\r\n      return (payload) => {\r\n        let weight = permission.data;\r\n        let bit = 0;\r\n\r\n        if (weight == -1) return true; // admin 获得所有权限\r\n\r\n        payload = math.log(payload, 2) + 1;\r\n\r\n        if (payload > 32) {\r\n          if (weight >= 4294967296) {\r\n            weight = parseInt(weight / 4294967296);\r\n            payload = payload - 32;\r\n            bit = weight >>> (payload - 1);\r\n          } else {\r\n            bit = 0;\r\n          }\r\n        } else {\r\n          bit = weight >>> (payload - 1);\r\n        }\r\n\r\n        return !!(payload && !isNaN(payload - 1) && !!(bit & 1));\r\n      };\r\n    };\r\n  },\r\n  hasPermissionNotAdmin(_, getters) {\r\n    // 权限值是否包含 payload 参数，并且排除 admin 角色\r\n    // 例如：权限值包含 8 时，就执行某操作，但是 admin 除外\r\n    return (permissionName) => {\r\n      const permission = getters.getPermission(permissionName);\r\n      const hasPermission = getters.hasPermission(permissionName);\r\n      return (payload) => {\r\n        return permission.data != -1 && hasPermission(payload);\r\n      };\r\n    };\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  UPDATE_PERMISSION(state, payload) {\r\n    // 刷新更新时间\r\n    payload.updateTime = new Date().getTime();\r\n\r\n    const permission = state.permissions.find((item) => {\r\n      return item.name === payload.name && Object.assign(item, payload);\r\n    });\r\n    !permission && state.permissions.push(payload);\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getOperationPermissionByUser({ commit, getters }, permissionName) {\r\n    const permission = getters.getPermission(permissionName);\r\n    if (permission && new Date().getTime() - permission.updateTime <= timeInterval) {\r\n      // 小于等于 timeInterval 的时间间隔内不会重新获取\r\n      return [true, permission];\r\n    } else {\r\n      commit(\"UPDATE_PERMISSION\", { name: permissionName, status: \"loading\", data: [] });\r\n    }\r\n\r\n    const [status, res] = await coreService.requestByRPC({\r\n      method: \"operationPermissionService.getOperationPermissionByUser\",\r\n      params: [null, permissionName],\r\n    });\r\n    if (status) {\r\n      commit(\"UPDATE_PERMISSION\", {\r\n        name: permissionName,\r\n        status: \"loaded\",\r\n        data: res.result.weight,\r\n      });\r\n    }\r\n    return [status, getters.getPermission(permissionName)];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import coreService from \"@resources/service/core\";\r\n\r\nconst state = {\r\n  currentUser: {},\r\n};\r\n\r\nconst getters = {\r\n  getCurrentUser(state) {\r\n    // 获取当前用户\r\n    return state.currentUser;\r\n  },\r\n  currentUser(state) {\r\n    // 获取当前用户\r\n    return state.currentUser;\r\n  },\r\n};\r\n\r\nconst mutations = {\r\n  UPDATE_CURRENT_USER(state, payload) {\r\n    state.currentUser = payload;\r\n  },\r\n};\r\n\r\nconst actions = {\r\n  async getCurrentUserInfo({ commit, getters }) {\r\n    const [status, res] = await coreService.requestByRPC({\r\n      method: \"userService.getLoginUser\",\r\n      params: [],\r\n    });\r\n    if (status) {\r\n      commit(\"UPDATE_CURRENT_USER\", res.result.data);\r\n    }\r\n    return [status, getters.getCurrentUser];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "import Vue from \"vue\";\r\nimport Vuex from \"vuex\";\r\nimport dictOptions from \"./modules/dict-options\";\r\nimport permission from \"./modules/permission\";\r\nimport user from \"./modules/user\";\r\n\r\nVue.use(Vuex);\r\n\r\nexport default new Vuex.Store({\r\n  modules: {\r\n    dictOptions,\r\n    permission,\r\n    user,\r\n  },\r\n});\r\n", "import { create, all } from 'mathjs'\r\n\r\nconst config = { }\r\nconst math = create(all, config)\r\n\r\nexport default math", "import Vue from \"vue\";\r\nimport ElementUI, { Loading } from \"element-ui\";\r\n\r\nimport \"./scss/index.scss\";\r\n\r\nVue.use(ElementUI);\r\n\r\nVue.$loading = Loading.service;\r\n", "const config = {\r\n  decimal: \".\",\r\n  thousands: \",\",\r\n  prefix: \"\",\r\n  suffix: \"\",\r\n  precision: 2\r\n};\r\n\r\nexport function numberToThousand(number = \"\") {\r\n  number = number === null ? \"\" : \"\" + number;\r\n  if (number.length > config.precision) {\r\n    number = number.split(config.decimal);\r\n    number[0] = number[0]\r\n      .split(config.thousands)\r\n      .join(\"\")\r\n      .replace(/\\B(?=(?:\\d{3})+\\b)/g, config.thousands);\r\n    number[1] = number[1] || \"\";\r\n    number[1] =\r\n      number[1].length > config.precision ? number[1].slice(0, config.precision) : number[1];\r\n    // number[1] = number[1].length < config.precision ? number[1] + '000000000000000'.slice(0, config.precision - number[1].length) :number[1]\r\n    if (number[1] >= 1) {\r\n      number[1] = config.decimal + number[1];\r\n    } else {\r\n      number[1] = \"\";\r\n    }\r\n    number = number[0] + number[1];\r\n  } else if (number === \"\") {\r\n    return \"\";\r\n  } else {\r\n    number = number.split(config.decimal);\r\n    number[1] = number[1] || \"\";\r\n    number[1] =\r\n      number[1].length > config.precision ? number[1].slice(0, config.precision) : number[1];\r\n    // number[1] = number[1].length < config.precision ? number[1] + '000000000000000'.slice(0, config.precision - number[1].length) :number[1]\r\n    if (number[1] >= 1) {\r\n      number[1] = config.decimal + number[1];\r\n    }\r\n    number = number[0] + number[1];\r\n  }\r\n  return config.prefix + number + config.suffix;\r\n}\r\nexport function thousandToNumber(money = \"\") {\r\n  if (money === \"0.\" + \"000000000000000\".slice(0, config.precision - 1)) {\r\n    return \"\";\r\n  } else if (money.length === 1) {\r\n    money = \"000000000000000\".slice(0, config.precision) + money;\r\n  } else if (!/\\./.test(money)) {\r\n    money += \"000000000000000\".slice(0, config.precision);\r\n  }\r\n  money = money\r\n    .split(config.decimal)\r\n    .join(\"\")\r\n    .split(config.thousands)\r\n    .join(\"\")\r\n    .replace(/^0+/, \"\")\r\n    .replace(/[^\\d]/g, \"\");\r\n\r\n  if (money.length > config.precision) {\r\n    money = money.replace(new RegExp(\"(\\\\d{\" + config.precision + \"})$\"), config.decimal + \"$1\");\r\n  } else {\r\n    money = (money / Math.pow(10, config.precision)).toFixed(config.precision);\r\n  }\r\n  return money;\r\n}\r\n", "import vue from \"vue\";\r\nimport dayjs from \"dayjs\";\r\nimport { numberToThousand } from \"./_func/money\";\r\n\r\nvue.filter(\"toMoney\", (val) => {\r\n  if (val === null) return \"\";\r\n  if (val === \"\") return \"\";\r\n  const data = Number(val);\r\n  return math.isNaN(data) ? val : numberToThousand(data);\r\n});\r\n\r\nvue.filter(\"toRound\", (val) => {\r\n  const data = Number(val);\r\n  return math.isNaN(data) ? val : math.round(data);\r\n});\r\n\r\nvue.filter(\"zeroToString\", (val) => {\r\n  const data = Number(val);\r\n  return math.isNaN(data) ? val : data === 0 ? \"\" : val;\r\n});\r\n\r\nvue.filter(\"dayjs\", (val, fmt = \"YYYY-MM-DD HH:mm\") => {\r\n  return val ? dayjs(val).format(fmt) : \"\";\r\n});\r\n\r\nvue.filter(\"dictOptions\", (val, dictName) => {\r\n  let options = vue.$store.getters.getOptions(dictName);\r\n  if (!options) return val;\r\n\r\n  const option = options.data.find((item) => \"\" + item.value === \"\" + val);\r\n  return option ? option.label : val;\r\n});\r\n", "/**\r\n * update: 2021-01-13 15:27\r\n *\r\n * 2021 年之后的项目从这里加载，逐渐替代原来的 /utils/add-on\r\n * 为了将来前端独立打包做准备\r\n */\r\nimport vue from \"vue\";\r\nimport router from \"@resources/router\";\r\nimport store from \"@resources/store\";\r\n// 加载函数库，主要解决金额的计算\r\nimport math from \"./math\";\r\n// 加载需要的界面组件\r\nimport \"./elements\";\r\n// 加载全局过滤器\r\nimport \"@resources/filters\";\r\n\r\n// 定义 ramda 变量\r\nconst R = require(\"ramda\");\r\n\r\n// 注册全局组件\r\n// 搜索 register.conf.js 文件，注册应用\r\nconst RegisterConfs = require.context(\"@components\", true, /\\/register\\.conf\\.js$/);\r\nRegisterConfs.keys().map((path) => {\r\n  const conf = RegisterConfs(path);\r\n  if (conf.global) {\r\n    const componentPath = path.replace(/^.\\/(.*)\\/register.conf.js$/, \"$1\");\r\n    vue.component(conf.name, (resolve) =>\r\n      require([`@components/${componentPath}/index.vue`], resolve)\r\n    );\r\n  }\r\n});\r\n\r\nexport default function addOn({ stores, routes }) {\r\n  window.math = math;\r\n  window.R = R;\r\n  // add-on routes\r\n  routes && router.addRoutes(routes);\r\n  // add-on store modules\r\n  stores &&\r\n    Object.keys(stores).map((name) => {\r\n      store.registerModule(name, stores[name]);\r\n    });\r\n\r\n  // 变成全局变量\r\n  vue.$bus = vue.prototype.$bus = new vue();\r\n  vue.$router = router;\r\n  vue.$store = store;\r\n\r\n  return {\r\n    store,\r\n    router,\r\n  };\r\n}\r\n", "import Vue from \"vue\";\r\n\r\nVue.filter(\"reviewStatus\", (value, fmt) => {\r\n  return R.path([\"latestStepHistory\", \"actionName\"], value) || \"草稿\";\r\n});\r\n\r\nVue.filter(\"storeType\", (value) => {\r\n  const map = {\r\n    1: \"门店\",\r\n    2: \"车队\",\r\n    3: \"工程机械\",\r\n  };\r\n  return map[value];\r\n});\r\n", "import vue from \"vue\";\r\nimport app from \"./app.vue\";\r\n\r\nimport stores from \"./stores.conf\";\r\nimport routes from \"./routes.conf\";\r\nimport addOn from \"@resources/add-on\";\r\n\r\nimport \"./resources/filter\";\r\n\r\nvue.config.productionTip = false;\r\n\r\nconst { store, router } = addOn({\r\n  stores,\r\n  routes,\r\n});\r\n\r\nnew vue({\r\n  store,\r\n  router,\r\n  render: (h) => h(app),\r\n}).$mount(\"#app\");\r\n", "/**\r\n * update: 2021-01-15 11:07\r\n * 该组件通过参数获取经销商数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-dealer-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-04-07 11:08\r\n * 该组件通过参数获取用户数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-user-by-resourceId\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-03-19 14:56\r\n * 根据渠道筛选品牌\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-brand-by-channel\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "/**\r\n * update: 2021-01-15 10:48\r\n * 该组件通过请求数据字典表接口获得选项内容\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-dict-options\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "export default [\r\n  {\r\n    categoryName: \"全合成\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    categoryName: \"合成型\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    categoryName: \"其他\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    category: \"Total\",\r\n    categoryName: \"总计\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n];\r\n", "export default [\r\n  {\r\n    categoryName: \"德乐400以上\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    categoryName: \"其他\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n  {\r\n    category: \"Total\",\r\n    categoryName: \"总计\",\r\n    estimatedPack: \"\",\r\n    actualPack: \"\",\r\n  },\r\n];\r\n", "import commonService from \"@projects/market/resource-application-2021/resources/service/common\";\r\nimport packageProducts from \"./_values/package\";\r\nimport deloProducts from \"./_values/delo\";\r\nimport vue from \"vue\";\r\n\r\nconst state = {\r\n  tableData: [],\r\n};\r\n\r\nconst getters = {\r\n  products(state) {\r\n    return state.tableData;\r\n  },\r\n};\r\n\r\nconst mutations = {};\r\n\r\nconst actions = {\r\n  initProducts({ state, getters, dispatch }, payload) {\r\n    if (payload) {\r\n      payload.map((item, index) => {\r\n        vue.set(state.tableData, index, item);\r\n      });\r\n    } else {\r\n      if ([\"1\"].indexOf(\"\" + getters.applyForm.brand) > -1) {\r\n        if (!state.tableData[0] || state.tableData[0].categoryName !== \"全合成\")\r\n          state.tableData = R.clone(packageProducts);\r\n      } else {\r\n        if (!state.tableData[0] || state.tableData[0].categoryName !== \"德乐 400 以上\")\r\n          state.tableData = R.clone(deloProducts);\r\n      }\r\n    }\r\n    dispatch(\"getProductsSales\");\r\n  },\r\n  async getProductsSales({ state, getters }) {\r\n    const [status, res] = await commonService.getProductsSales(getters.applyForm);\r\n    if (status) {\r\n      let totalActualPack = 0;\r\n      state.tableData.map((tableItem) => {\r\n        res.result.data.map((dataItem) => {\r\n          if (tableItem.categoryName === dataItem.productCategory) {\r\n            tableItem.actualPack = dataItem.rolling12MonthSellIn;\r\n            totalActualPack = math\r\n              .add(math.bignumber(totalActualPack), math.bignumber(tableItem.actualPack))\r\n              .valueOf();\r\n          }\r\n        });\r\n        if (tableItem.categoryName === \"总计\") {\r\n          tableItem.actualPack = totalActualPack;\r\n        }\r\n      });\r\n    }\r\n    return [status, res];\r\n  },\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "module.exports = {\r\n  name: \"el-upload-customize\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "import { Notification } from 'element-ui';\r\n\r\nexport default {\r\n  success: Notification.success,\r\n  info: Notification.info,\r\n  warning: Notification.warning,\r\n  error: Notification.error\r\n}", "/**\r\n * 该组件通过参数获取经分销商数据\r\n * 返回结果可以通过 filter 参数来控制显示内容\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-retailer\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "module.exports = [\r\n  {\r\n    path: \"/signage/apply/:id?\",\r\n    component: (resolve) => require([\"./views/apply/signage\"], resolve),\r\n    meta: {\r\n      title: \"Signage Application Form\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/seminar/apply/:id?\",\r\n    component: (resolve) => require([\"./views/apply/seminar\"], resolve),\r\n    meta: {\r\n      title: \"Seminar Application Form\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/signage/list\",\r\n    component: (resolve) => require([\"./views/list/signage\"], resolve),\r\n    meta: {\r\n      title: \"Signage Application List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/seminar/list\",\r\n    component: (resolve) => require([\"./views/list/seminar\"], resolve),\r\n    meta: {\r\n      title: \"Seminar Application List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/signage/observer\",\r\n    component: (resolve) => require([\"./views/list/signage/observer\"], resolve),\r\n    meta: {\r\n      title: \"Signage Application Observer List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/seminar/observer\",\r\n    component: (resolve) => require([\"./views/list/seminar/observer\"], resolve),\r\n    meta: {\r\n      title: \"Seminar Application Observer List\",\r\n    },\r\n  },\r\n\r\n  {\r\n    path: \"/ck/observer\",\r\n    component: (resolve) => require([\"./views/list/ck/observer\"], resolve),\r\n    meta: {\r\n      title: \"CK Application Observer List\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/ck/apply/:id?\",\r\n    component: (resolve) => require([\"./views/apply/ck\"], resolve),\r\n    meta: {\r\n      title: \"CK Application Form\",\r\n    },\r\n  },\r\n  {\r\n    path: \"/ck/list\",\r\n    component: (resolve) => require([\"./views/list/ck\"], resolve),\r\n    meta: {\r\n      title: \"CK Application List\",\r\n    },\r\n  },\r\n];\r\n", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./app.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./app.vue?vue&type=style&index=0&lang=css&\"", "const state = {\r\n  tableData: [],\r\n};\r\n\r\nconst getters = {\r\n  ckProducts(state) {\r\n    return state.tableData;\r\n  },\r\n};\r\n\r\nconst mutations = {};\r\n\r\nconst actions = {\r\n  initCkProducts({ state, getters, dispatch }, payload) {\r\n    state.tableData=payload\r\n  }\r\n};\r\n\r\nexport default {\r\n  state,\r\n  mutations,\r\n  actions,\r\n  getters,\r\n};\r\n", "/**\r\n * update: 2021-01-15 14:33\r\n * 该组件可以选择年份\r\n */\r\nmodule.exports = {\r\n  name: \"el-select-number\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n", "module.exports = {\r\n  name: \"el-popconfirm-customize\",\r\n  global: true, // 常用组件可以定义成全局的\r\n};\r\n"], "sourceRoot": ""}