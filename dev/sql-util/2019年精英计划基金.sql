SELECT t1.Distributor_id AS distributor_id, 2019 year
		, sum(ISNULL(actual_CDM_Marketing_Fund, 0)+ISNULL(actual_CDM_IVI_Fund, 0)
		+ISNULL(actual_CIO_BD_Fund, 0)+ISNULL(actual_CIO_Marketing_Fund, 0)+ISNULL(actual_CIO_IVI_Fund, 0)) total_fund
	FROM dw_pp_customer_fund_quarter_new_view t1
	WHERE t1.year = '2019' and ISNULL(actual_CDM_Marketing_Fund, 0)+ISNULL(actual_CDM_IVI_Fund, 0)
		+ISNULL(actual_CIO_BD_Fund, 0)+ISNULL(actual_CIO_Marketing_Fund, 0)+ISNULL(actual_CIO_IVI_Fund, 0)>0
	GROUP BY t1.Distributor_id