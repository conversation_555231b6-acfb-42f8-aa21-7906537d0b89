select *
--delete l
from wx_t_point_value_detail_log l 
where exists (select 1 from wx_t_point_value_detail d
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID  where pb.id=6113
 and l.POINT_VALUE_ID=d.id)

select *
--delete d
from wx_t_point_value_detail d 
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID where pb.id=6113;

select *
--delete pb
 from dbo.wx_t_point_business pb where pb.id=6113