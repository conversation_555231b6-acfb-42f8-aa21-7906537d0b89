package com.chevron.dsrkpi.business.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.chevron.dsrkpi.business.AwardRuleBizService;
import com.chevron.dsrkpi.business.DsrKpiBizService;
import com.chevron.dsrkpi.dao.CiDsrKpiHistoryMapper;
import com.chevron.dsrkpi.dao.DsrKpiMapper;
import com.chevron.dsrkpi.dao.PreAdjustPointMapper;
import com.chevron.dsrkpi.model.AwardRule;
import com.chevron.dsrkpi.model.AwardRuleExample;
import com.chevron.dsrkpi.model.CiDsrKpiHistory;
import com.chevron.dsrkpi.model.DsrKpi;
import com.chevron.dsrkpi.model.DsrKpiExample;
import com.chevron.dsrkpi.model.DsrKpiMonth;
import com.chevron.dsrkpi.model.DsrKpiMonthParam;
import com.chevron.dsrkpi.model.DsrKpiParams;
import com.chevron.dsrkpi.model.ExcelExportToEmail;
import com.chevron.dsrkpi.model.KpiBasicData;
import com.chevron.dsrkpi.model.PointPersonal;
import com.chevron.dsrkpi.model.PreAdjustPoint;
import com.chevron.kpi.dao.KPIInfoMapper;
import com.chevron.kpi.model.KpiCalInfo;
import com.chevron.point.business.PointAccountBizService;
import com.chevron.point.business.PointBizService;
import com.chevron.point.business.PointBusinessBizService;
import com.chevron.point.business.PointValueDetailBizService;
import com.chevron.point.dao.WXTPointValueDetailLogVoMapper;
import com.chevron.point.dic.BusinessStatus;
import com.chevron.point.dic.BusinessType;
import com.chevron.point.dic.PointAccountType;
import com.chevron.point.dto.PointType;
import com.chevron.point.model.DsrPointDetail;
import com.chevron.point.model.WXTPointAccountVo;
import com.chevron.point.model.WXTPointAccountVoExample;
import com.chevron.point.model.WXTPointBusinessVo;
import com.chevron.point.model.WXTPointValueDetailLogVo;
import com.chevron.point.model.WXTPointValueDetailVo;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.BatchOperationUtil;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.SendMessageUtil;
import com.common.util.StringUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.auth.model.WxTPropertiesExample;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.properties.service.WxTPropertiesService;
import org.apache.log4j.Logger;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * 经销商销售绩效业务接口实现类
 * <AUTHOR>
 * @version 1.0 2019-12-24 09:35
 */
@Service
public class DsrKpiBizServiceImpl implements DsrKpiBizService {
	
	// kpiCode
	public static final String KPI_CODE_AMOUNT_OF_CERTIFICATE = "AMOUNT_OF_CERTIFICATE";//业绩证明
	public static final String KPI_CODE_DAYS_OF_MONTH = "DAYS_OF_MONTH";//门店拜访积分规则
	public static final String KPI_CODE_TOP_RANK = "TOP_RANK";//新门店客户数Top5积分规则
    public static final String KPI_CODE_AMOUNT_OF_CERTIFICATE_HIGH = "AMOUNT_OF_CERTIFICATE_HIGH";//优质业绩证明

	
	//积分规则类型  ruleType
	public static final String AWARD_BY_REACH = "AWARD_BY_REACH";//指标达到奖励
	public static final String AWARD_BY_INCREASE = "AWARD_BY_INCREASE";//指标递增奖励
	public static final String AWARD_BY_REGION  = "AWARD_BY_REGION";//指标区间奖励 1-4 区间值，5-9 每个区间奖励值
	
	@Autowired
	private DicService dicService;
	
	@Autowired
	private DsrKpiMapper dsrKpiMapper;
	
	@Autowired
	private PointBizService pointBizService;
	
	@Autowired
	private AwardRuleBizService awardRuleBizService;

    @Autowired
    private KPIInfoMapper kPIInfoMapper;

    @Autowired
    private CiDsrKpiHistoryMapper ciDsrKpiHistoryMapper;
    
    @Autowired
    private WxTPropertiesMapper wxTPropertiesMapper;

    @Autowired
    private WxTPropertiesService wxTPropertiesService;
    
    @Autowired
    private PreAdjustPointMapper preAdjustPointMapper;
    
	@Autowired
	private PointAccountBizService pointAccountBizService;
	
	@Autowired
	private PointBusinessBizService pointBusinessBizService;

	@Autowired
	private WXTPointValueDetailLogVoMapper pointValueDetailLogMapper;

	@Autowired
	private PointValueDetailBizService pointValueDetailBizService;
	
	private final static Logger log = Logger.getLogger(DsrKpiBizServiceImpl.class);

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(DsrKpi record) throws WxPltException {
		dsrKpiMapper.insertSelective(record);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(DsrKpi record) throws WxPltException {
		dsrKpiMapper.updateByPrimaryKeySelective(record);
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		dsrKpiMapper.deleteByIds(ids);
	}

	/*
	 * @Override
	 * 
	 * @Transactional(propagation = Propagation.REQUIRED, rollbackFor =
	 * Exception.class) public void deleteByExample(DsrKpiExample example) throws
	 * WxPltException { dsrKpiMapper.deleteByExample(example); }
	 */

	@Override
	public List<DsrKpi> queryByExample(DsrKpiExample example) throws WxPltException {
		return dsrKpiMapper.selectByExample(example);
	}

	@Override
    public List<DsrKpi> queryByParams(Map<String, Object> params) throws WxPltException {
    	return dsrKpiMapper.queryByParams(params);
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateForEditPage(DsrKpi record) throws WxPltException {
		dsrKpiMapper.updateForEditPage(record);
	}
	
	@Override
	public DsrKpi getBean(Long id) throws WxPltException{
		return dsrKpiMapper.selectByPrimaryKey(id);
	}	

	@Override
	public void queryForPage(DsrKpiParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}
	
	@Override
	public List<DsrKpi> queryForPage(DsrKpiParams params) throws WxPltException {
		return dsrKpiMapper.queryForPage(params);
	}

    @Override
	public List<DsrKpiMonth> queryForKpiMonthPage(DsrKpiMonthParam params) throws WxPltException {
		// TODO Auto-generated method stub
		return dsrKpiMapper.queryForKpiMonthPage(params);
	}

	@Override
	public List<DsrKpiMonth> queryForKpiMonthRankPage(DsrKpiMonthParam params) throws WxPltException {
		// TODO Auto-generated method stub
		return dsrKpiMapper.queryForKpiMonthRankPage(params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void distributePoints(String date) throws Exception {
        //检查是否已经发放积分
        int count = dsrKpiMapper.checkWhetherPoints(date);
        if(count > 0){
            throw new WxPltException(date + " 当前月份已经已经发放积分");
        }
        List<DsrKpi> dsrKpiList = this.calculateBonus(date);
        Iterator<DsrKpi> iterator = dsrKpiList.iterator();
        while (iterator.hasNext()){
            DsrKpi next = iterator.next();
            if(next.getAwardPoint() == 0){
                iterator.remove();
            }
        }
        //保存绩效积分记录
		BatchOperationUtil<DsrKpi> detailUtil = new BatchOperationUtil<DsrKpi>(dsrKpiList, 50);
		while(detailUtil.hasMoreElements()) {
			dsrKpiMapper.insertForeach(detailUtil.nextElement());
		}
		log.info("成功保存绩效积分记录." );
		//发放积分
        JsonResponse map = new JsonResponse();
        List<AwardRule> ruleList = gainAwardRule(map);
		List<DsrPointDetail> dList = new ArrayList<DsrPointDetail>();
		List<Long> dsrIdList = new ArrayList<Long>();
		for(DsrKpi dsrKpi : dsrKpiList) {
			if(dsrKpi.getAwardPoint().longValue() != 0) {
				DsrPointDetail d = new DsrPointDetail();
				d.setUserId(dsrKpi.getDsrId());
				d.setPoint(dsrKpi.getAwardPoint());
				for(AwardRule rule : ruleList) {
					if(rule.getKpiCode().equals(dsrKpi.getKpiCode())) {
						d.setComments(rule.getRemark() + "【" + dsrKpi.getAwardPoint().longValue() +"】");
					}
				}
				d.setEarnType(dsrKpi.getKpiCode());
				d.setPointType(PointType.DSR_CIO_POINT);
				d.setTransTime(date);
				dsrIdList.add(dsrKpi.getDsrId());
				dList.add(d);
			}
		}
		//todo
		if(!CollectionUtils.isEmpty(dList)) {
            pointBizService.awardDsrPoint(dList);
        }
		//计算kpi数据并写入临时表
        List<KpiCalInfo> kpiCalInfos = this.calculateKpi(date,false,null);
        List<CiDsrKpiHistory> ciDsrKpiHistories = this.buildCiKpiHistorys(kpiCalInfos,date);
        //保存绩效积分记录
        BatchOperationUtil<CiDsrKpiHistory> ciHistorys = new BatchOperationUtil<CiDsrKpiHistory>(ciDsrKpiHistories, 50);
        while(ciHistorys.hasMoreElements()) {
            ciDsrKpiHistoryMapper.insertBatch(ciHistorys.nextElement());
        }
        
        //发送DSR短信
        if(!CollectionUtils.isEmpty(dsrIdList)) {
        	this.sendMobile(date, dsrIdList);
        }
	}

    private List<CiDsrKpiHistory> buildCiKpiHistorys(List<KpiCalInfo> kpiCalInfos,String date) {
        Date time = DateUtil.parseDate(date, "yyyy-MM-01");
        List<CiDsrKpiHistory> ciDsrKpiHistories = new ArrayList<CiDsrKpiHistory>();
        Date now = new Date();
        WxTUser curUser = ContextUtil.getCurUser();
        for (KpiCalInfo item : kpiCalInfos) {
            CiDsrKpiHistory ciDsrKpiHistory = new CiDsrKpiHistory();
            ciDsrKpiHistory.setDsrId(item.getDsrId());
            ciDsrKpiHistory.setPartnerId(item.getPartnerId());
            ciDsrKpiHistory.setNewFleet(item.getNewFleet() == null ? 0 : item.getNewFleet());
            ciDsrKpiHistory.setNewShop(item.getNewShop() == null ? 0 : item.getNewShop());
            ciDsrKpiHistory.setEffectiveDays(item.getEffectiveDays() == null ? 0 : item.getEffectiveDays());
            ciDsrKpiHistory.setEffectiveVisits(item.getEffectiveVisits() == null ? 0 : item.getEffectiveVisits());
            ciDsrKpiHistory.setYtdShop(item.getYtdShop() == null ? 0 : item.getYtdShop());
            ciDsrKpiHistory.setYtdFleet(item.getYtdFleet() == null ? 0 : item.getYtdFleet());
            ciDsrKpiHistory.setPotentialFleet(item.getPotentialFleet() == null ? 0 : item.getPotentialFleet());
            ciDsrKpiHistory.setPotentialShop(item.getPotentialShop() == null ? 0 : item.getPotentialShop());
            ciDsrKpiHistory.setPotentialToPartner(item.getPotentialToPartner() == null ? 0 : item.getPotentialToPartner());
            ciDsrKpiHistory.setHighFleetPerformance(item.getHighFleetPerformance() == null ? 0 : item.getHighFleetPerformance());
            ciDsrKpiHistory.setFleetPerformance(item.getFleetPerformance() == null ? 0 : item.getFleetPerformance());
            ciDsrKpiHistory.setYear(DateUtil.getYear(time));
            ciDsrKpiHistory.setMonth(DateUtil.getMonth(time));
            ciDsrKpiHistory.setEffictiveDate(time);
            ciDsrKpiHistory.setExtProperty1(item.getVisitEffectiveAimDay());
            ciDsrKpiHistory.setCreateTime(now);
            ciDsrKpiHistory.setUpdateTime(now);
            ciDsrKpiHistory.setCreateUserId(curUser.getUserId());
            ciDsrKpiHistory.setUpdateUserId(curUser.getUserId());
            ciDsrKpiHistories.add(ciDsrKpiHistory);
            if(item.getFlag() != null && item.getFlag()){
                ciDsrKpiHistory.setExtProperty3("只有YTD的值");
            }
        }
        return ciDsrKpiHistories;
    }

	    //生成发放积分列表
		public List<DsrKpi> formDistributePoints(KpiBasicData kpiBasicData, AwardRule rule, Integer target){
			List<DsrKpi> dkList = new ArrayList<DsrKpi>();
			String[] yfs = kpiBasicData.getYearMonth().split("-");
	        Calendar calendar = Calendar.getInstance();
	        calendar.set(Calendar.YEAR, Integer.parseInt(yfs[0]));
	        calendar.set(Calendar.MONTH, Integer.parseInt(yfs[1]));
            calendar.add(Calendar.MONTH, -1);
	        int maxDay = calendar.getActualMinimum(Calendar.DATE);
	        calendar.set(Calendar.DAY_OF_MONTH, maxDay);
			if(KPI_CODE_AMOUNT_OF_CERTIFICATE.equals(rule.getKpiCode()) && kpiBasicData.getProofPerNum() != null && kpiBasicData.getProofPerNum() != 0) {
                    //计算证书积分
                    DsrKpi dk = clasifyRuleType(kpiBasicData.getProofPerNum(), rule);
                    dk.setExtProperty3(kpiBasicData.getYearMonth());
                    dk.setEffictiveDate(calendar.getTime());
                    dk.setDsrId(kpiBasicData.getDsrId());
                    dk.setPartnerId(kpiBasicData.getPartnerId());
                    dk.setKpiCode(KPI_CODE_AMOUNT_OF_CERTIFICATE);
                    dkList.add(dk);
			}
            if(KPI_CODE_AMOUNT_OF_CERTIFICATE_HIGH.equals(rule.getKpiCode()) && kpiBasicData.getHighProofPerNum() != null && kpiBasicData.getHighProofPerNum() != 0) {
                    //计算证书积分
                    DsrKpi dk = clasifyRuleType(kpiBasicData.getHighProofPerNum(), rule);
                    dk.setExtProperty3(kpiBasicData.getYearMonth());
                    dk.setEffictiveDate(calendar.getTime());
                    dk.setDsrId(kpiBasicData.getDsrId());
                    dk.setPartnerId(kpiBasicData.getPartnerId());
                    dk.setKpiCode(KPI_CODE_AMOUNT_OF_CERTIFICATE_HIGH);
                    dkList.add(dk);
            }
			if(KPI_CODE_DAYS_OF_MONTH.equals(rule.getKpiCode()) && kpiBasicData.getEffectDays() != null && kpiBasicData.getEffectDays() != 0) {
                    //计算到访积分
                    DsrKpi dk = clasifyRuleType(kpiBasicData.getEffectDays(), rule);
                    dk.setExtProperty3(kpiBasicData.getYearMonth());
                    dk.setEffictiveDate(calendar.getTime());
                    dk.setDsrId(kpiBasicData.getDsrId());
                    dk.setPartnerId(kpiBasicData.getPartnerId());
                    dk.setKpiCode(KPI_CODE_DAYS_OF_MONTH);
                    dkList.add(dk);
			}
			if(KPI_CODE_TOP_RANK.equals(rule.getKpiCode()) && kpiBasicData.getNewClientNum() != null && kpiBasicData.getNewClientNum() != 0) {
                    if(kpiBasicData.getNewClientNum() >= target) {
                        DsrKpi dk = clasifyRuleType(kpiBasicData.getNewClientRank(), rule);
                        dk.setExtProperty3(kpiBasicData.getYearMonth());
                        dk.setEffictiveDate(calendar.getTime());
                        dk.setDsrId(kpiBasicData.getDsrId());
                        dk.setPartnerId(kpiBasicData.getPartnerId());
                        dk.setKpiCode(KPI_CODE_TOP_RANK);
                        dk.setExtProperty1("" + kpiBasicData.getNewClientNum());
                        dkList.add(dk);
				}
			}
			return dkList;
		}
		private DsrKpi assembleDsrKpi(DsrKpi dsrKpi) {
			dsrKpi.setCreateTime(new Date());
			dsrKpi.setCreateUserId(ContextUtil.getCurUser().getUserId());
			dsrKpi.setDeleteFlag(0);
			dsrKpi.setUpdateTime(new Date());
			dsrKpi.setUpdateUserId(dsrKpi.getCreateUserId());
			dsrKpi.setExtProperty1(dsrKpi.getExtProperty1() != null ? dsrKpi.getExtProperty1() : "");
			dsrKpi.setExtProperty2(dsrKpi.getExtProperty2() != null ? dsrKpi.getExtProperty2() : "");
			dsrKpi.setExtProperty3(dsrKpi.getExtProperty3() != null ? dsrKpi.getExtProperty3() : "");
			return dsrKpi;
		}
		public DsrKpi clasifyRuleType(Long kpi, AwardRule rule) {
			DsrKpi dk = new DsrKpi();
			dk.setKpiValue(new Double(kpi));
			assembleDsrKpi(dk);
			if(AWARD_BY_REACH.equals(rule.getRuleType())) {
				return awardByReach(dk, rule);
			}
			if( AWARD_BY_INCREASE.equals(rule.getRuleType())) {
				return awardByIncrease(dk, rule);
			}
			if( AWARD_BY_REGION.equals(rule.getRuleType())) {
				return awardByRegion(dk, rule);
			}
			dk.setAwardPoint(
					Double.valueOf(0));
			return dk;
		}
		
		//"AWARD_BY_REACH";//指标达到奖励
		public DsrKpi awardByReach(DsrKpi dk, AwardRule rule) {
			if(new Double(dk.getKpiValue()).longValue() >= Long.parseLong(rule.getExtProperty1())) {
				dk.setAwardPoint(Double.parseDouble(rule.getExtProperty2()));
				return dk;
			}else {
				dk.setAwardPoint(
						Double.valueOf(0));
				return dk;
			}
			
		}
		// "AWARD_BY_INCREASE";//指标递增奖励
		public DsrKpi awardByIncrease(DsrKpi dk, AwardRule rule) {
			if(new Double(dk.getKpiValue()).longValue() >= Long.parseLong(rule.getExtProperty1())) {
				dk.setAwardPoint(
					Double.valueOf(0) + Long.parseLong(rule.getExtProperty2()) +
					   (dk.getKpiValue() - Long.parseLong(rule.getExtProperty1()))/Long.parseLong(rule.getExtProperty3()) * Long.parseLong(rule.getExtProperty4())
				);
				return dk;
			}else {
				dk.setAwardPoint(
						Double.valueOf(0));
				return dk;
			}
			
		}
		//"AWARD_BY_REGION ";//指标区间奖励 1-4 区间值，5-9 每个区间奖励值
		public DsrKpi awardByRegion(DsrKpi dk, AwardRule rule) {
			if(new Double(dk.getKpiValue()).longValue() <= 0) {
				dk.setAwardPoint(new Double(0));
				return dk;
			}else if(new Double(dk.getKpiValue()).longValue() > 0
					&&new Double(dk.getKpiValue()).longValue() < Long.parseLong(rule.getExtProperty1())) {
				dk.setAwardPoint(Double.parseDouble(rule.getExtProperty5()));
				return dk;
			}else if(!StringUtils.isEmpty(rule.getExtProperty2())
					&& new Double(dk.getKpiValue()).longValue() >= Long.parseLong(rule.getExtProperty1()) 
					&& new Double(dk.getKpiValue()).longValue() <  Long.parseLong(rule.getExtProperty2())) {
				dk.setAwardPoint(Double.parseDouble(rule.getExtProperty6()));
				return dk;
			}else if(!StringUtils.isEmpty(rule.getExtProperty3())
					&& new Double(dk.getKpiValue()).longValue() >= Long.parseLong(rule.getExtProperty2()) 
					&& new Double(dk.getKpiValue()).longValue() <  Long.parseLong(rule.getExtProperty3())){
				dk.setAwardPoint(Double.parseDouble(rule.getExtProperty7()));
				return dk;
			}else if(!StringUtils.isEmpty(rule.getExtProperty4())
					&& new Double(dk.getKpiValue()).longValue() >= Long.parseLong(rule.getExtProperty3()) 
					&& new Double(dk.getKpiValue()).longValue() <  Long.parseLong(rule.getExtProperty4())) {
				dk.setAwardPoint(Double.parseDouble(rule.getExtProperty8()));
				return dk;
			}else {
				dk.setAwardPoint(StringUtils.isEmpty(rule.getExtProperty9()) ? Double.valueOf(0):Double.parseDouble(rule.getExtProperty9()));
				return dk;
			}
		}
		
		//获取积分规则
		public List<AwardRule> gainAwardRule(JsonResponse map){
			List<AwardRule> ruleList = new ArrayList<AwardRule>();
			try {
				@SuppressWarnings("unchecked")
				List<DicItemVo> dicItemVos = (List<DicItemVo>)(dicService.getDicItemByDicTypeCode("DsrKpi.kpiCode").get("data"));
				List<String> kpiCodeList = new ArrayList<String>(); 
				for(DicItemVo itemVo : dicItemVos) {
					kpiCodeList.add(itemVo.getDicItemCode());
				}
				AwardRuleExample example = new AwardRuleExample();
				example.createCriteria().andDeleteFlagEqualTo(0).andEnableFlagEqualTo(1).andKpiCodeIn(kpiCodeList);
				ruleList = awardRuleBizService.queryByExample(example);

			} catch (Exception e) {
				map.handleException(e, this, ContextUtil.getCurUserId(), 
						"com.chevron.dsrkpi.service.impl.DsrKpiServiceImpl.distributePoints", JsonUtil.writeValue("dic中type code :DsrKpi.kpiCode 不存在"));
			}
			return ruleList;
		}
	
	
	

	@Override
	public void selectPointTotal(DsrKpiParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, dsrKpiMapper.selectPointTotal(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public void selectPointPersonal(DsrKpiParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, dsrKpiMapper.selectPointPersonal(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public void personalPointDetail(DsrKpiParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, dsrKpiMapper.personalPointDetail(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		
	}

	@Override
	public List<ExcelExportToEmail> selecExcelExportToEmailsByYearMonth(String yearMonth) throws WxPltException {
		 List<ExcelExportToEmail> excelList =  dsrKpiMapper.selecExcelExportToEmailsByYearMonth(yearMonth);
		 Map<String, Object> params = new HashMap<String, Object>();
		 String[] split = yearMonth.split("-");
		 params.put("year", split[0]);
		 params.put("month", split[1]);
		 params.put("orgType", 1);
		 List<KpiCalInfo> preAdjustPoint = dsrKpiMapper.getPreAdjustPoint(params);
		 Map<Long,ArrayList<KpiCalInfo>> preAdjustMap = new HashMap<Long, ArrayList<KpiCalInfo>>();
		 for (KpiCalInfo kpiCalInfo : preAdjustPoint) {
			 ArrayList<KpiCalInfo> arrayList = preAdjustMap.get(kpiCalInfo.getDsrId());
				if(CollectionUtils.isEmpty(arrayList)) {
					arrayList = new ArrayList<KpiCalInfo>();
				}
				arrayList.add(kpiCalInfo);
				preAdjustMap.put(kpiCalInfo.getDsrId(), arrayList);
		}
		List<ExcelExportToEmail> resExcelList  = new ArrayList<ExcelExportToEmail>();
		for (ExcelExportToEmail excelExportToEmail : excelList) {
			ArrayList<KpiCalInfo> arrayList = preAdjustMap.get(excelExportToEmail.getDsrId());
			if(!CollectionUtils.isEmpty(arrayList)) {
				excelExportToEmail.setAdJustInfoList(arrayList);
				Double adJustPoints = 0.0;
				for (KpiCalInfo kpiCalInfo2 : arrayList) {
					adJustPoints +=(kpiCalInfo2.getAdjustPoint()==null? 0:kpiCalInfo2.getAdjustPoint());
				}
				
				excelExportToEmail.setAllPoint(adJustPoints + (excelExportToEmail.getAllPoint() == null ? 0 : excelExportToEmail.getAllPoint()));
				excelExportToEmail.setLeftPoint(adJustPoints + (excelExportToEmail.getLeftPoint() == null ? 0 : excelExportToEmail.getLeftPoint()));
				excelExportToEmail.setAdJustPoint(adJustPoints);
			}
			resExcelList.add(excelExportToEmail);
			preAdjustMap.remove(excelExportToEmail.getDsrId());
		}
		//添加只有微调积分的dsr
		for (Entry<Long, ArrayList<KpiCalInfo>> entry : preAdjustMap.entrySet()) { 
			  ExcelExportToEmail excelExportToEmail = new ExcelExportToEmail();
			  ArrayList<KpiCalInfo> adPointList = entry.getValue();
			  Double adJustPoints = 0.0;
			  for (KpiCalInfo kpiCalInfo : adPointList) {
				  adJustPoints +=(kpiCalInfo.getAdjustPoint()==null? 0:kpiCalInfo.getAdjustPoint());
				  excelExportToEmail.setOrganizationName(kpiCalInfo.getPartnerName());
				  excelExportToEmail.setRegionName(kpiCalInfo.getRegionCnName());
				  excelExportToEmail.setSuppervisorName(kpiCalInfo.getSuppervisorName());
				  excelExportToEmail.setSalesName(kpiCalInfo.getSalesName());
				  excelExportToEmail.setDsrName(kpiCalInfo.getDsrName());
				  excelExportToEmail.setSalesCai(kpiCalInfo.getSalesCai());
				  excelExportToEmail.setKpiCode("adJustPoints");
				  excelExportToEmail.setSuppervisorCai(kpiCalInfo.getSuppervisorCai());
				  excelExportToEmail.setDsrId(kpiCalInfo.getDsrId());
			  }
			  excelExportToEmail.setAdJustInfoList(adPointList);
			  excelExportToEmail.setAllPoint(adJustPoints);
			  excelExportToEmail.setLeftPoint(adJustPoints);
			  excelExportToEmail.setAdJustPoint(adJustPoints);
			  resExcelList.add(excelExportToEmail);
		} 
	
		 return resExcelList;
	}

    @Override
    public List<DsrKpi> calculateBonus(String date) throws Exception{
        JsonResponse map = new JsonResponse();
        //获取满足计算条件的dsr信息
        List<KpiBasicData> dsrInfo = this.getDsrInfo(date);
        //计算积分
        //获取积分规则
        List<AwardRule> ruleList = gainAwardRule(map);
        if(CollectionUtils.isEmpty(ruleList)) {
            return new ArrayList<DsrKpi>();
        }

        //获取发放积分录店个数要求
        String newCustomerStarget = wxTPropertiesService.getPropertiesCodeByCodeType("ci_kpi_new_customer_starget");
        if (StringUtils.isBlank(newCustomerStarget)) {
            throw new WxPltException("德乐KPI积分发放录入新店个数配置无效，请联系管理员");
        }
        int target = Integer.parseInt(newCustomerStarget);
        List<DsrKpi> dsrKpiList = new ArrayList<DsrKpi>();
        for(KpiBasicData kpiBasicData : dsrInfo) {
            kpiBasicData.setYearMonth(new SimpleDateFormat("yyyy-MM").format(DateUtil.parseDate(date,"yyyy-MM-dd")));
            for(AwardRule rule : ruleList) {
                List<DsrKpi> per = formDistributePoints( kpiBasicData, rule, target);
                if(!CollectionUtils.isEmpty(per)) {
                    dsrKpiList.addAll(per);
                }
            }
        }
        List<DsrKpi> dsrKpiListNew = new ArrayList<DsrKpi>();
        //合并优质业绩证明和普通业绩证明的积分
        HashSet<Long> dsrIds = new HashSet<Long>();
        for (DsrKpi dsrKpi : dsrKpiList) {
            dsrIds.add(dsrKpi.getDsrId());
        }
        for (Long dsrId : dsrIds) {
            DsrKpi cdKpi = null;
            for (DsrKpi dsrKpi : dsrKpiList) {
                if (dsrId.equals(dsrKpi.getDsrId())) {
                    if (KPI_CODE_DAYS_OF_MONTH.equals(dsrKpi.getKpiCode()) || KPI_CODE_TOP_RANK.equals(dsrKpi.getKpiCode())) {
                        if(dsrKpi.getAwardPoint() != null && dsrKpi.getAwardPoint() >=  0d){
                            dsrKpiListNew.add(dsrKpi);
                        }
                    }
                    if (KPI_CODE_AMOUNT_OF_CERTIFICATE.equals(dsrKpi.getKpiCode()) || KPI_CODE_AMOUNT_OF_CERTIFICATE_HIGH.equals(dsrKpi.getKpiCode())) {
                        if (cdKpi == null) {
                            cdKpi = new DsrKpi();
                            BeanUtil.copyProperties(dsrKpi, cdKpi);
                            cdKpi.setKpiCode(KPI_CODE_AMOUNT_OF_CERTIFICATE);
                            cdKpi.setExtProperty1(KPI_CODE_AMOUNT_OF_CERTIFICATE_HIGH.equals(dsrKpi.getKpiCode()) ? String.valueOf(dsrKpi.getKpiValue()) : String.valueOf(0));
                        } else {
                            cdKpi.setKpiValue(cdKpi.getKpiValue() + dsrKpi.getKpiValue());
                            cdKpi.setAwardPoint(cdKpi.getAwardPoint() + dsrKpi.getAwardPoint());
                            double newValue = new Double(cdKpi.getExtProperty1()) + (KPI_CODE_AMOUNT_OF_CERTIFICATE_HIGH.equals(dsrKpi.getKpiCode()) ? dsrKpi.getKpiValue() : 0d);
                            cdKpi.setExtProperty1(String.valueOf(newValue));
                        }
                    }
                }
            }
            if (cdKpi != null) {
                dsrKpiListNew.add(cdKpi);
            }
        }
        return dsrKpiListNew;
    }

    @Override
    public List<KpiBasicData> getDsrInfo(String date) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        Date startDate = DateUtil.parseDate(date, "yyyy-MM-dd");
        Date endDate = DateUtil.addMonths(startDate, 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
        log.info("=====================================startdate" + dateFormat.format(startDate));
        log.info("=====================================enddate" + dateFormat.format(endDate));
        paramMap.put("startDate", dateFormat.format(startDate));
        paramMap.put("endDate", dateFormat.format(endDate));
        //查询dsr 报表数据
        //List<KpiBasicData> dsrInfoList = kPIInfoMapper.getDsrInfo(paramMap);
        //分别查出来四个个指标 然后拼装起来
        List<KpiBasicData> visitKpis = kPIInfoMapper.getVisitKpi(paramMap);
        List<KpiBasicData> enterShopKpis = kPIInfoMapper.getEnterShopKpi(paramMap);
        List<KpiBasicData> fleetPerformanceKpis = kPIInfoMapper.getFleetPerformanceKpi(paramMap);
        ArrayList<KpiBasicData> list = new ArrayList<KpiBasicData>();
        list.addAll(visitKpis);
        list.addAll(enterShopKpis);
        list.addAll(fleetPerformanceKpis);
        HashSet<Long> dsrIds = new HashSet<Long>();
        for (KpiBasicData kpiBasicData : list) {
            dsrIds.add(kpiBasicData.getDsrId());
        }
        List<KpiBasicData> dsrInfoList = new ArrayList<KpiBasicData>();
        for (Long dsrId : dsrIds) {
            KpiBasicData kpi = null;
            for (KpiBasicData kpiBasicData : list) {
                if (dsrId.equals(kpiBasicData.getDsrId())) {
                    if (kpi == null) {
                        kpi = new KpiBasicData();
                        BeanUtil.copyProperties(kpiBasicData, kpi);
                    } else {
                        this.buildKpiBasicData(kpiBasicData,kpi);                    }
                }
            }
            if (kpi != null) {
                dsrInfoList.add(kpi);
            }
        }
        return dsrInfoList;
    }

    @Override
    public List<KpiCalInfo> calculateKpi(String date,boolean accessControl,Map<String,Object> params){
        //获得总积分
        Date start = DateUtil.parseDate(date, "yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-01 00:00:00.000");
        String startDate = dateFormat.format(start);
        String endDate = dateFormat.format(DateUtil.addMonths(start, 1));
        String lastTwoDate = dateFormat.format(DateUtil.addMonths(start, -1));
        String yearStart = new SimpleDateFormat("yyyy-01-01 00:00:00.000").format(start);
        params = params == null ? new HashMap<String,Object>():params;
        if(accessControl){
            params.put("accessControl",true);
        }else{
            params.put("accessControl",false);
        }
        params.put("startDate",startDate);
        params.put("endDate",endDate);
        params.put("yearStart",yearStart);
        //开始计算各项kpi指标情况
        List<KpiCalInfo> joinDsr = kPIInfoMapper.getJoinDsr(params);
        //新增门店数量
        List<KpiCalInfo> newShopKpis = kPIInfoMapper.newShopKpi(startDate, endDate);
        //新增车队数量
        List<KpiCalInfo> newFleetKpis = kPIInfoMapper.newFleetKpi(startDate, endDate);
        //YTD新增门店数量
        List<KpiCalInfo> newYTDShopKpis = kPIInfoMapper.newShopKpi(yearStart, endDate);
        for (KpiCalInfo item : newYTDShopKpis) {
            item.setYtdShop(item.getNewShop());
            item.setNewShop(null);
        }
        //YTD新增车队数量
        List<KpiCalInfo> newYTDFleetKpis = kPIInfoMapper.newFleetKpi(yearStart, endDate);
        for (KpiCalInfo item : newYTDFleetKpis) {
            item.setYtdFleet(item.getNewFleet());
            item.setNewFleet(null);
        }
        //有效天数
        List<KpiCalInfo> effectiveDays = kPIInfoMapper.effectiveDays(startDate, endDate);
        //拜访客户数
        List<KpiCalInfo> effectiveVisits = kPIInfoMapper.effectiveVisits(startDate, endDate);
        //业绩证明数量
        List<KpiCalInfo> fleetPerformances = kPIInfoMapper.fleetPerformance(startDate, endDate);
        //潜在门店数量
        List<KpiCalInfo> potentialShops = kPIInfoMapper.potentialShop(startDate, endDate);
        //潜在车队数量
        List<KpiCalInfo> potentialFleets = kPIInfoMapper.potentialFleet(startDate, endDate);
        //多少个潜在转为合格
        List<KpiCalInfo> kpiBasicData = kPIInfoMapper.potentialToPartner(lastTwoDate, startDate, endDate);

        //获取拜访天数设置
        WxTPropertiesExample wxTPropertiesExample = new WxTPropertiesExample();
        wxTPropertiesExample.createCriteria().andCodetypeEqualTo("aimDay");
        List<WxTProperties> wxTProperties = wxTPropertiesMapper.selectByExample(wxTPropertiesExample);
        long aimDay = Long.parseLong(wxTProperties.get(0).getCode());
        for (KpiCalInfo dsr : joinDsr) {
            dsr.setYear(DateUtil.getYear(start));
            dsr.setMonth(DateUtil.getMonth(start));
            this.coverDatas(newShopKpis,dsr);
            this.coverDatas(newFleetKpis,dsr);
            this.coverDatas(effectiveDays,dsr);
            this.coverDatas(effectiveVisits,dsr);
            this.coverDatas(fleetPerformances,dsr);
            this.coverDatas(potentialShops,dsr);
            this.coverDatas(potentialFleets,dsr);
            this.coverDatas(kpiBasicData,dsr);
            this.coverDatas(newYTDShopKpis,dsr);
            this.coverDatas(newYTDFleetKpis,dsr);
            dsr.setVisitEffectiveAimDay(String.valueOf(aimDay));
        }
        return joinDsr;
    }

    private void coverDatas(List<KpiCalInfo> list,KpiCalInfo target){
        for (KpiCalInfo item : list) {
            if(item.getDsrId().equals(target.getDsrId())){
                BeanUtil.copyProperties(item, target, CopyOptions.create().setIgnoreNullValue(true));
            }
        }
    }

    private void buildKpiBasicData(KpiBasicData source,KpiBasicData target){
        if(target.getProofPerNum() == null){
            target.setProofPerNum(source.getProofPerNum());
        }
        if(target.getHighProofPerNum() == null){
            target.setHighProofPerNum(source.getHighProofPerNum());
        }
        if(target.getNewClientNum() == null){
            target.setNewClientNum(source.getNewClientNum());
        }
        if(target.getNewClientRank() == null){
            target.setNewClientRank(source.getNewClientRank());
        }
        if(target.getEffectDays() == null){
            target.setEffectDays(source.getEffectDays());
        }
        if(target.getRegionName() == null){
            target.setRegionName(source.getRegionName());
        }
    }

	@Override
	@Transactional
	public void sendPersonalPoint(String dateTime) throws WxPltException {
		WxTUser curUser = ContextUtil.getCurUser();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("status", 0);
		params.put("formDate",dateTime);
		params.put("toDate", new SimpleDateFormat("yyyy-MM-dd").format(DateUtils.addMonths(DateUtils.parseDate(dateTime), 1)));
		List<PreAdjustPoint> prePointsList = preAdjustPointMapper.queryByParams(params);
		for (PreAdjustPoint preAdjustPoint : prePointsList) {
			//查询账号表
			WXTPointAccountVo pointAccount = null;
			WXTPointAccountVoExample accountVoExample = new WXTPointAccountVoExample();
			accountVoExample.createCriteria().andPointAccountOwnerIdEqualTo(preAdjustPoint.getDsrId());
			List<WXTPointAccountVo> accountList = pointAccountBizService.queryByExample(accountVoExample);
			if(accountList.isEmpty()) {
				//插入账户
				pointAccount = new WXTPointAccountVo();
				pointAccount.setIsEnabled(true);
				pointAccount.setPointAccountOwnerId(preAdjustPoint.getDsrId());
				pointAccount.setPointAccountOwnerName(preAdjustPoint.getOrganizationName());
				pointAccount.setPointAccountType(PointAccountType.SPBD.name());
				pointAccount.setCreatedBy(ContextUtil.getCurUserId());
				pointAccountBizService.insert(pointAccount);
			}else {
				pointAccount = accountList.get(0);
			}
			
			//初始化businessVo
			WXTPointBusinessVo businessVo = new WXTPointBusinessVo();
			
			//businessVo.setBusinessDesc(dsrPonitsInfoVo.getRemark());
			
			businessVo.setBusinessStatus(BusinessStatus.DONE.name());
			businessVo.setBusinessTypeCode(BusinessType.ADJUST_POINT.getTypeCode());
			businessVo.setCreatedBy(curUser.getUserId());
			businessVo.setCreationTime(new Date());
			businessVo.setDeleteFlag(false);
			businessVo.setRelatedCode(DateUtils.getCurrentDate("yyyyMMddHHmm") + CommonUtil.generateSequenceCode("AwardDsrPoint.relateCode", 4));
			businessVo.setAttribute1(curUser.getUserId().toString());
			businessVo.setAttribute2(preAdjustPoint.getDicItemCode());
			businessVo.setAttribute3(preAdjustPoint.getDicTypeCode());
			pointBusinessBizService.insert(businessVo);
			
			//初始化WXTPointValueDetailVo
			WXTPointValueDetailVo valueDetailVo = new WXTPointValueDetailVo();
			valueDetailVo.setTransTime(DateUtil.parseDate(preAdjustPoint.getTransTime()));
			valueDetailVo.setPointType(PointType.DSR_CIO_POINT.getValue());
			valueDetailVo.setPointAccountId(pointAccount.getId());
			valueDetailVo.setPointAccountVo(pointAccount);
			valueDetailVo.setPointValue(preAdjustPoint.getPrePoints());
			valueDetailVo.setPointPayed(0.0);
			valueDetailVo.setBusinessId(businessVo.getId());
			valueDetailVo.setPointBusinessVo(businessVo);
			valueDetailVo.setComments("手动调整积分");
			valueDetailVo.setAttribute1(preAdjustPoint.getDicTypeCode());
			valueDetailVo.setAttribute2(preAdjustPoint.getDicItemCode());
			valueDetailVo.setDeleteFlag(false);
			valueDetailVo.setCreatedBy(curUser.getUserId());
			valueDetailVo.setCreationTime(new Date());
			pointValueDetailBizService.insert(valueDetailVo);
			
			//初始化WXTPointValueDetailLogVo
			WXTPointValueDetailLogVo detailLogVo = new WXTPointValueDetailLogVo();
			detailLogVo.setPointValueId(valueDetailVo.getId());
			detailLogVo.setDetailVo(valueDetailVo);
			detailLogVo.setBusinessId(businessVo.getId());
			detailLogVo.setBusinessVo(businessVo);
			detailLogVo.setModifiedValue(preAdjustPoint.getPrePoints());
			detailLogVo.setPointAccountId(pointAccount.getId());
			detailLogVo.setAccountVo(pointAccount);
			detailLogVo.setComments(preAdjustPoint.getDicItemCode());
			detailLogVo.setDeleteFlag(false);
			detailLogVo.setCreatedBy(curUser.getUserId());
			detailLogVo.setCreationTime(new Date());
			pointValueDetailLogMapper.insertSelective(detailLogVo);		
		}
	}

	@Override
	public void sendMobile(String yearMonth, List<Long> dsrIdList) {
		String[] strings = yearMonth.split("-");
		//int year = Integer.parseInt(strings[0]);
		int month = Integer.parseInt(strings[1]);
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("dsrIds", dsrIdList);
		List<WxTUser> userList = new ArrayList<WxTUser>();
		userList = dsrKpiMapper.getUserListByIds(param);
		String mobileMessageContent ="【雪佛龙】"+month+"月德乐群英会绩效积分已发放。";
				
        try {
        	Set<String> mobileSet = new HashSet<String>();
            for (WxTUser tuser : userList) {
                String mobile = tuser.getMobileTel();
                if (null != mobile && !mobile.isEmpty()) {
                	mobileSet.add(mobile);
                } else {
                    log.info("doSendMobile is null,user:" + tuser.getLoginName());
                }
            }
            //发送短信
			for (String mobile : mobileSet) {
				 log.info("doSendMobile mobile:" + mobile);
                 SendMessageUtil.sndMessageToPhone(mobile, mobileMessageContent);		
			}
        } catch (Exception e) {
            e.printStackTrace();
            log.error("doSendMobile" + e.getLocalizedMessage());
        }
	}

	@Override
	public List<PointPersonal> appGetPointsForDsr(DsrKpiParams params) {
		List<PointPersonal> selectPointPersonal = dsrKpiMapper.selectPointPersonal(params);
		return selectPointPersonal;
	}

}