select top 10000 *
--update d set d.update_time='2021-11-16'
	from wx_t_oem_delivery d where recv_distributor_id=45 and d.creation_time>='2021-09-01' and d.creation_time<'2021-09-10'
	order by id desc;
	
select top 10000 *
--update d set d.update_time=null
	from wx_t_oem_delivery d where recv_distributor_id=45 and update_time='2021-11-16'
	
select top 10000 *
--update d set d.update_time='2021-11-15'
	from wx_t_oem_delivery d where recv_distributor_id=45 and d.creation_time>='2021-11-16'	
	
select top 10000 *
--update d set d.update_time='2021-12-09'
	from wx_t_oem_delivery d where recv_distributor_id=45 and d.recv_id!='20205' 
	and d.creation_time>='2017-01-01' and d.creation_time<'2020-06-01'
	order by id desc;

select top 10000 *--22865
--update d set d.update_time='2021-12-26'
	from wx_t_oem_delivery d where recv_distributor_id=45 and d.creation_time<='2021-11-16'	
	and exists (select 1 from wx_t_oem_delivery_product dp left join wx_t_oem_product_packaging_code pc 
	on pc.code1=dp.code or pc.code2=dp.code where dp.delivery_id=d.id and pc.creation_time>'2021-11-16')	