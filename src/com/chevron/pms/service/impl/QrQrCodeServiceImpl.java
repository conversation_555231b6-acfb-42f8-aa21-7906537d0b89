package com.chevron.pms.service.impl;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import com.chevron.pms.dao.OemDeliveryProductMapper;
import com.chevron.pms.dao.OemProductPackagingCodeMapper;
import com.chevron.pms.dao.QrCodeDetailMapper;
import com.chevron.pms.model.OemDeliveryProduct;
import com.chevron.pms.model.OemProductPackagingCode;
import com.chevron.pms.model.OemProductPackagingCodeExample;
import com.chevron.pms.model.OemProductPackagingCodeExample.Criteria;
import com.chevron.pms.model.QrCodeDetail;
import com.chevron.pms.service.QrCodeService;
import com.chevron.pms.service.QrQrCodeService;
import com.common.constants.Constants;
import com.common.util.DateUtil;
import com.common.util.SftpUtils;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogUtils;


@Service
public class QrQrCodeServiceImpl implements QrQrCodeService {
	
	@Autowired
	private DicService dicService;
	
	@Autowired
	private OemDeliveryProductMapper oemDeliveryProductMapper;
	
	@Autowired
	OemProductPackagingCodeMapper oemProductPackagingCodeMapper;
	
	@Autowired
	QrCodeService qrCodeService;
	
	@Autowired
	QrCodeDetailMapper qrCodeDetailMapper;
	
	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	private Logger log = LoggerFactory.getLogger(QrQrCodeServiceImpl.class);

	@Override
	public boolean doPushJob( ) {	
		SftpUtils sftp = null;
		File file = null;
		FileOutputStream outputStream = null;
		BufferedOutputStream buffedOutput = null;
		
		String filename = "65579_" + UUID.randomUUID().toString().replace("-", "") 
				+ "^001^L001^L001@2018^*******^127.0.0.1.txt";
		String filePath = new String(); 	
		
		String localPath = "/data/chevron/sftp/gqcode/";
		filePath = localPath + File.separator + filename;
		
		try {
			//获取sftp服务器信息
			Map<String, Object> dicResult =  dicService.getDicItemByDicTypeCode("yesno.sftp");
			List<DicItemVo> itemlist = (ArrayList)dicResult.get("data");
			String sftpHost = "";
			int sftpPort = 0;
			String sftpAccount = "";
			String sftpPassword = "";
			String fileFolder = "";			
			for(DicItemVo dicItem : itemlist){
				if(dicItem.getDicItemCode().equals("yesno.sftp.host")){
					sftpHost = dicItem.getDicItemName();
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.port")){
					sftpPort = Integer.valueOf(dicItem.getDicItemName());
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.account")){
					sftpAccount = dicItem.getDicItemName();
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.password")){
					sftpPassword = dicItem.getDicItemName();
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.qrcodeFolder")){
					fileFolder = dicItem.getDicItemName();
					continue;
				}
			}
			sftp = new SftpUtils(sftpHost, sftpPort, sftpAccount,sftpPassword);
			sftp.connect();
			//sftp.mkdirs(fileFolder + filename);
			file = new File(filePath);
			if(!file.exists()){
				file.createNewFile();
			}
			outputStream = new FileOutputStream(file);
			buffedOutput = new BufferedOutputStream(outputStream);
//			
			Date createDate = DateUtil.addDays(new Date(), -1);
			/*Date createDate = new Date();*/
			Integer year = Integer.parseInt(DateUtil.getYear(createDate));
			Integer month = Integer.parseInt(DateUtil.getMonth(createDate));
			Integer date = Integer.parseInt(DateUtil.getDay(createDate));
			Date startTime = DateUtil.getStartTimeOfDate(year,month,date);
			Date endTime = DateUtil.getEndTimeOfDate(year,month,date);
			//所有高桥物流码
			List<OemDeliveryProduct> deliveryProducts =  oemDeliveryProductMapper.selectByCreationTime(startTime,endTime);
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();//code与codeId建立关系
			List<Long> codeIdList = new ArrayList<Long>();
			List<String> bottleCodeList = new ArrayList<String>();
			List<String> boxCodeList = new ArrayList<String>();
			List<String> stackCodeList = new ArrayList<String>();
			for(int i = 0; i<deliveryProducts.size(); i++) {
				String code = deliveryProducts.get(i).getCode();
				//不推送zhaoxin的码
				if(code.length() != 16){
					String codeLevel = deliveryProducts.get(i).getCodelevel();
					if(codeLevel.equals("1")){//瓶码
						bottleCodeList.add(code);
					}else if(codeLevel.equals("2")) {//箱码
						boxCodeList.add(code);
					}else if(codeLevel.equals("3")) {//垛码
						stackCodeList.add(code);
					}
				}
			}
			if(!bottleCodeList.isEmpty()) {//瓶码				
				int size = bottleCodeList.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<String> subBottleCodeList = null;
					if(cycleTime == cycleTimes-1) {
						subBottleCodeList = bottleCodeList.subList(cycleTime * 2000, size);
					}else {
						subBottleCodeList = bottleCodeList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					OemProductPackagingCodeExample oemProductPackagingCodeExample = new OemProductPackagingCodeExample();
					Criteria criteria = oemProductPackagingCodeExample.createCriteria();
					criteria.andCode1In(subBottleCodeList);
					List<OemProductPackagingCode> packagingCodeList = oemProductPackagingCodeMapper.selectByExample(oemProductPackagingCodeExample);
					for(int i = 0; i< packagingCodeList.size(); i++) {
						String code1 = packagingCodeList.get(i).getCode1();
						String code2 = packagingCodeList.get(i).getCode2();
						Long codeId = qrCodeService.getCodeId(code1);
						codeIdList.add(codeId);
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("code1", code1);
						map.put("code2", code2);
						map.put("codeId", codeId);
						list.add(map);
					}
				}
			}
			if(!boxCodeList.isEmpty()) {//箱码				
				int size = boxCodeList.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<String> subBoxCodeList = null;
					if(cycleTime == cycleTimes-1) {
						subBoxCodeList = boxCodeList.subList(cycleTime * 2000, size);
					}else {
						subBoxCodeList = boxCodeList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					OemProductPackagingCodeExample oemProductPackagingCodeExample = new OemProductPackagingCodeExample();
					Criteria criteria = oemProductPackagingCodeExample.createCriteria();
					criteria.andCode2In(subBoxCodeList);
					List<OemProductPackagingCode> packagingCodeList = oemProductPackagingCodeMapper.selectByExample(oemProductPackagingCodeExample);
					for(int i = 0; i< packagingCodeList.size(); i++) {
						String code1 = packagingCodeList.get(i).getCode1();
						String code2 = packagingCodeList.get(i).getCode2();
						Long codeId = qrCodeService.getCodeId(code1);
						codeIdList.add(codeId);
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("code1", code1);
						map.put("code2", code2);
						map.put("codeId", codeId);
						list.add(map);
					}
				}
			}
			if(!stackCodeList.isEmpty()) {//垛码
				int size = stackCodeList.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<String> subStackCodeList = null;
					if(cycleTime == cycleTimes-1) {
						subStackCodeList = stackCodeList.subList(cycleTime * 2000, size);
					}else {
						subStackCodeList = stackCodeList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					OemProductPackagingCodeExample oemProductPackagingCodeExample = new OemProductPackagingCodeExample();
					Criteria criteria = oemProductPackagingCodeExample.createCriteria();
					criteria.andCode3In(subStackCodeList);
					List<OemProductPackagingCode> packagingCodeList = oemProductPackagingCodeMapper.selectByExample(oemProductPackagingCodeExample);
					for(int i = 0; i< packagingCodeList.size(); i++) {
						String code1 = packagingCodeList.get(i).getCode1();
						String code2 = packagingCodeList.get(i).getCode2();
						Long codeId = qrCodeService.getCodeId(code1);
						codeIdList.add(codeId);
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("code1", code1);
						map.put("code2", code2);
						map.put("codeId", codeId);
						list.add(map);
					}
				}				
			}
			if(!codeIdList.isEmpty()) {//查询qrcode
				NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate (jdbcTemplate.getDataSource());
				Map<String,Object> params = new HashMap<String,Object>();
				int size =  list.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				int counter = 0;
				//jdbcTemplate单次允许最多2100个参数，这里使用2000
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<Map<String,Object>> subList = null;
					List<Long> subCodeIdList = null;
					if(cycleTime == cycleTimes-1) {
						subList = list.subList(cycleTime * 2000, size);
						subCodeIdList = codeIdList.subList(cycleTime * 2000, size);
					}else {
						subList = list.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
						subCodeIdList = codeIdList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					params.put("codeIds", subCodeIdList);
					List<QrCodeDetail> qrCodeDetailList = namedParameterJdbcTemplate.query("select * from wx_t_qr_code_detail where code_id in (:codeIds)",
							params,new RowMapper<QrCodeDetail>() {
								@Override
								public QrCodeDetail mapRow(ResultSet arg0, int arg1) throws SQLException {
									QrCodeDetail qrCodeDetail = new QrCodeDetail();
									qrCodeDetail.setCodeId(arg0.getLong("code_id"));
									qrCodeDetail.setQrCode(arg0.getString("qr_code"));
									return qrCodeDetail;
								}
					});
					for(int i = 0; i< subList.size(); i++) {
						Map<String,Object> mapTemp = subList.get(i);
						boolean flag = false;//标记是否有qrcode记录
						for(int j = 0; j<qrCodeDetailList.size(); j++) {
							QrCodeDetail qrCodeDetailListTemp = qrCodeDetailList.get(j);
							if(((Long)mapTemp.get("codeId")).longValue() == qrCodeDetailListTemp.getCodeId().longValue()) {
								flag = true;
								String qrCode = qrCodeDetailListTemp.getQrCode();
								qrCode = qrCode == null? "" : qrCode;
								String line = "!1," + (++counter) + "," + mapTemp.get("code1") + 
										"," +  qrCode + ",100320,,,,,"+ mapTemp.get("code2") +"\r\n";
								buffedOutput.write(line.getBytes());
								qrCodeDetailList.remove(j);
								break;
							}
						}
						if(!flag) {
							String line = "!1," + (++counter) + "," + mapTemp.get("code1") + 
									"," + ",100320,,,,,"+ mapTemp.get("code2") +"\r\n";
							buffedOutput.write(line.getBytes());
						}
					}
				}
			}
			buffedOutput.flush();
			boolean flag =  sftp.uploadFile(fileFolder, filename, localPath + File.separator, filename);
			sftp.uploadFile("/qrcodeFolder/", filename, localPath + File.separator, filename);
			LogUtils.addInfoLog(1l, "com.chevron.pms.service.impl.QrQrCodeServiceImpl.doPushJob", "GQ Ftp推送成功。");
			return flag;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);
			LogUtils.addErrorLog(1l, "com.chevron.pms.service.impl.QrQrCodeServiceImpl.doPushJob", "GQ Ftp推送失败。" + e.getMessage(), null);
		} finally {
			try {
				if (buffedOutput != null) {
					buffedOutput.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
				log.error(e.getMessage());
			}
			if (sftp != null) {
				sftp.disconnect();
			}
			sftp.deleteFile(filePath);//文件解除占用后删除
		}
		return false;
	}

	@Override
	public Map<String, Object> pushGqCodeByTime(String startTimeStr, String endTimeStr) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS);
		SftpUtils sftp = null;
		File file = null;
		FileOutputStream outputStream = null;
		BufferedOutputStream buffedOutput = null;
		
		String filename = "65579_" + UUID.randomUUID().toString().replace("-", "") 
				+ "^001^L001^L001@2018^*******^127.0.0.1.txt";
		String filePath = new String(); 	
		

		String localPath = "/data/chevron/sftp/gqcode/";
		filePath = localPath + File.separator + filename;
		
		try {
			//获取sftp服务器信息
			Map<String, Object> dicResult =  dicService.getDicItemByDicTypeCode("yesno.sftp");
			List<DicItemVo> itemlist = (ArrayList)dicResult.get("data");
			String sftpHost = "";
			int sftpPort = 0;
			String sftpAccount = "";
			String sftpPassword = "";
			String fileFolder = "";			
			for(DicItemVo dicItem : itemlist){
				if(dicItem.getDicItemCode().equals("yesno.sftp.host")){
					sftpHost = dicItem.getDicItemName();
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.port")){
					sftpPort = Integer.valueOf(dicItem.getDicItemName());
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.account")){
					sftpAccount = dicItem.getDicItemName();
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.password")){
					sftpPassword = dicItem.getDicItemName();
					continue;
				}
				if(dicItem.getDicItemCode().equals("yesno.sftp.qrcodeFolder")){
					fileFolder = dicItem.getDicItemName();
					continue;
				}
			}
			sftp = new SftpUtils(sftpHost, sftpPort, sftpAccount,sftpPassword);
			sftp.connect();
			//sftp.mkdirs(fileFolder + filename);
			file = new File(filePath);
			if(!file.exists()){
				file.createNewFile();
			}
			outputStream = new FileOutputStream(file);
			buffedOutput = new BufferedOutputStream(outputStream);
//			
			Date startDate = new SimpleDateFormat("yyyy-MM-dd").parse(startTimeStr);
			Integer startYear = Integer.parseInt(DateUtil.getYear(startDate));
			Integer startMonth = Integer.parseInt(DateUtil.getMonth(startDate));
			Integer startDay = Integer.parseInt(DateUtil.getDay(startDate));
			Date startTime = DateUtil.getStartTimeOfDate(startYear,startMonth,startDay);
			
			Date endDate = new SimpleDateFormat("yyyy-MM-dd").parse(endTimeStr);
			Integer endYear = Integer.parseInt(DateUtil.getYear(endDate));
			Integer endMonth = Integer.parseInt(DateUtil.getMonth(endDate));
			Integer endDay = Integer.parseInt(DateUtil.getDay(endDate));
			Date endTime = DateUtil.getEndTimeOfDate(endYear,endMonth,endDay);
			//所有高桥物流码
			List<OemDeliveryProduct> deliveryProducts =  oemDeliveryProductMapper.selectByCreationTime(startTime,endTime);
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();//code与codeId建立关系
			List<Long> codeIdList = new ArrayList<Long>();
			List<String> bottleCodeList = new ArrayList<String>();
			List<String> boxCodeList = new ArrayList<String>();
			List<String> stackCodeList = new ArrayList<String>();
			for(int i = 0; i<deliveryProducts.size(); i++) {
				String code = deliveryProducts.get(i).getCode();
				//不推送zhaoxin的码
				if(code.length() != 16){
					String codeLevel = deliveryProducts.get(i).getCodelevel();
					if(codeLevel.equals("1")){//瓶码
						bottleCodeList.add(code);
					}else if(codeLevel.equals("2")) {//箱码
						boxCodeList.add(code);
					}else if(codeLevel.equals("3")) {//垛码
						stackCodeList.add(code);
					}
				}
			}
			if(!bottleCodeList.isEmpty()) {//瓶码				
				int size = bottleCodeList.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<String> subBottleCodeList = null;
					if(cycleTime == cycleTimes-1) {
						subBottleCodeList = bottleCodeList.subList(cycleTime * 2000, size);
					}else {
						subBottleCodeList = bottleCodeList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					OemProductPackagingCodeExample oemProductPackagingCodeExample = new OemProductPackagingCodeExample();
					Criteria criteria = oemProductPackagingCodeExample.createCriteria();
					criteria.andCode1In(subBottleCodeList);
					List<OemProductPackagingCode> packagingCodeList = oemProductPackagingCodeMapper.selectByExample(oemProductPackagingCodeExample);
					for(int i = 0; i< packagingCodeList.size(); i++) {
						String code1 = packagingCodeList.get(i).getCode1();
						String code2 = packagingCodeList.get(i).getCode2();
						Long codeId = qrCodeService.getCodeId(code1);
						codeIdList.add(codeId);
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("code1", code1);
						map.put("code2", code2);
						map.put("codeId", codeId);
						list.add(map);
					}
				}
			}
			if(!boxCodeList.isEmpty()) {//箱码				
				int size = boxCodeList.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<String> subBoxCodeList = null;
					if(cycleTime == cycleTimes-1) {
						subBoxCodeList = boxCodeList.subList(cycleTime * 2000, size);
					}else {
						subBoxCodeList = boxCodeList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					OemProductPackagingCodeExample oemProductPackagingCodeExample = new OemProductPackagingCodeExample();
					Criteria criteria = oemProductPackagingCodeExample.createCriteria();
					criteria.andCode2In(subBoxCodeList);
					List<OemProductPackagingCode> packagingCodeList = oemProductPackagingCodeMapper.selectByExample(oemProductPackagingCodeExample);
					for(int i = 0; i< packagingCodeList.size(); i++) {
						String code1 = packagingCodeList.get(i).getCode1();
						String code2 = packagingCodeList.get(i).getCode2();
						Long codeId = qrCodeService.getCodeId(code1);
						codeIdList.add(codeId);
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("code1", code1);
						map.put("code2", code2);
						map.put("codeId", codeId);
						list.add(map);
					}
				}
			}
			if(!stackCodeList.isEmpty()) {//垛码
				int size = stackCodeList.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<String> subStackCodeList = null;
					if(cycleTime == cycleTimes-1) {
						subStackCodeList = stackCodeList.subList(cycleTime * 2000, size);
					}else {
						subStackCodeList = stackCodeList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					OemProductPackagingCodeExample oemProductPackagingCodeExample = new OemProductPackagingCodeExample();
					Criteria criteria = oemProductPackagingCodeExample.createCriteria();
					criteria.andCode3In(subStackCodeList);
					List<OemProductPackagingCode> packagingCodeList = oemProductPackagingCodeMapper.selectByExample(oemProductPackagingCodeExample);
					for(int i = 0; i< packagingCodeList.size(); i++) {
						String code1 = packagingCodeList.get(i).getCode1();
						String code2 = packagingCodeList.get(i).getCode2();
						Long codeId = qrCodeService.getCodeId(code1);
						codeIdList.add(codeId);
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("code1", code1);
						map.put("code2", code2);
						map.put("codeId", codeId);
						list.add(map);
					}
				}				
			}
			if(!codeIdList.isEmpty()) {//查询qrcode
				NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate (jdbcTemplate.getDataSource());
				Map<String,Object> params = new HashMap<String,Object>();
				int size =  list.size();
				int cycleTimes = size/2000 + ((size % 2000) == 0 ? 0 : 1);
				int counter = 0;
				//jdbcTemplate单次允许最多2100个参数，这里使用2000
				for(int cycleTime = 0; cycleTime < cycleTimes; cycleTime++) {
					List<Map<String,Object>> subList = null;
					List<Long> subCodeIdList = null;
					if(cycleTime == cycleTimes-1) {
						subList = list.subList(cycleTime * 2000, size);
						subCodeIdList = codeIdList.subList(cycleTime * 2000, size);
					}else {
						subList = list.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
						subCodeIdList = codeIdList.subList(cycleTime * 2000, (cycleTime + 1) * 2000);
					}
					params.put("codeIds", subCodeIdList);
					List<QrCodeDetail> qrCodeDetailList = namedParameterJdbcTemplate.query("select * from wx_t_qr_code_detail where code_id in (:codeIds)",
							params,new RowMapper<QrCodeDetail>() {
								@Override
								public QrCodeDetail mapRow(ResultSet arg0, int arg1) throws SQLException {
									QrCodeDetail qrCodeDetail = new QrCodeDetail();
									qrCodeDetail.setCodeId(arg0.getLong("code_id"));
									qrCodeDetail.setQrCode(arg0.getString("qr_code"));
									return qrCodeDetail;
								}
					});
					for(int i = 0; i< subList.size(); i++) {
						Map<String,Object> mapTemp = subList.get(i);
						boolean flag = false;//标记是否有qrcode记录
						for(int j = 0; j<qrCodeDetailList.size(); j++) {
							QrCodeDetail qrCodeDetailListTemp = qrCodeDetailList.get(j);
							if(((Long)mapTemp.get("codeId")).longValue() == qrCodeDetailListTemp.getCodeId().longValue()) {
								flag = true;
								String qrCode = qrCodeDetailListTemp.getQrCode();
								qrCode = qrCode == null? "" : qrCode;
								String line = "!1," + (++counter) + "," + mapTemp.get("code1") + 
										"," +  qrCode + ",100320,,,,,"+ mapTemp.get("code2") +"\r\n";
								buffedOutput.write(line.getBytes());
								qrCodeDetailList.remove(j);
								break;
							}
						}
						if(!flag) {
							String line = "!1," + (++counter) + "," + mapTemp.get("code1") + 
									"," + ",100320,,,,,"+ mapTemp.get("code2") +"\r\n";
							buffedOutput.write(line.getBytes());
						}
					}
				}
			}
			buffedOutput.flush();
			sftp.uploadFile(fileFolder, filename, localPath + File.separator, filename);
			sftp.uploadFile("/qrcodeFolder/", filename, localPath + File.separator, filename);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} finally {
			try {
				if (buffedOutput != null) {
					buffedOutput.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
				log.error(e.getMessage());
			}
			if (sftp != null) {
				sftp.disconnect();
			}
			sftp.deleteFile(filePath);//文件解除占用后删除
		}
		return resultMap;
	}
	
	
}
