package com.sys.auth.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.common.util.SpringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.permission.business.PermissionCache;
import com.wordnik.swagger.annotations.ApiModel;
import com.wordnik.swagger.annotations.ApiModelProperty;

@ApiModel(value="用户", description="用户")
public class WxTUser implements Serializable, Cloneable {
	private static Logger log = LoggerFactory.getLogger(WxTUser.class);
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;
	public static final int USER_TYPE_ORGANIZATION = 0;
	public static final int USER_TYPE_PARTNER = 1;
	public static final int USER_TYPE_WORKSHOP = 2;
	/**普通BD用户 ,spm用户 */
	public static final Long OTHER_USER_ROLE = 0L;
	/**雪佛龙用户  */
	public static final Long CHEVRON_USER_ROLE = 1L;
	/**店长角色用户  */
	public static final Long WORKSHOP_USER_ROLE = 2L;
	/**service partner admin角色用户  */
	public static final Long SERVICE_PARTNER_ADMIN_USER_ROLE = 3L;
	/** 雪佛龙用户类型 */
	public final static String USER_MODEL_CHEVRON = "chevron";
	/** 合伙人用户类型 */
	public final static String USER_MODEL_SP = "sp";
	/** 门店用户类型 */
	public final static String USER_MODEL_WS = "ws";
	private Long userId;
	/** 岗位 标记是否分销商监理  1是监理 */
	private Long post;
	/** 设备类型  android  和 ios */
	private String deviceType;
	/** 是否接收推送消息,0不接收，非0接收 */
	private Integer receiveMsg;
	/** 职位代码  对应主数据代码 */
	private String postion;
	/** 职位名称 */
	private String postionName;
	/** 分支机构名称 */
	private String branchName;
	/** 头像图片id  供获取头像图片时使用  */

	private Long photoId;
	private String loginName;
	private String deviceId;
	private String userNo;
	private String password;
	private String salt;
	private Date pwdLasttime;
	private String chName;
	private String cnName;
	private String pinyin;
	private String allowLogin;
	private String sex;
	private Date birthday;
	private String address;
	private String email;
	private String mobileTel;
	private String fixedTel;
	private Long orgId;
	private String orgName;
	private String defaultLocale;
	
	/**
	 * 用户类型。1-表示门店用户，orgid保存门店ID
	 */
	private String type;

	/**  CAI  **/
	private String cai;
	
	private String regionName;
	
	private String regionNameCn;
	
	/** 所属经销商ID */
	private Long distributorId;

	private boolean isManger;
	private boolean istest;
	private boolean istest_fun;
	private boolean isService_Partner_BD;
	private boolean isService_Partner_Manager;
	private boolean isService_Partner_CSR;
	/** 是否有雪佛龙管理员角色 */
	private boolean isChevron_Manager;
	private boolean isWorkshop_Manager;
	private boolean isWorkshop_Mechanic = false;
	private boolean isDataRoleTestOrganization;
	private boolean isDataRoleMaillist;
	/** 是否系统管理员 */
	private boolean isAdmin = false;
	/** 默认不是雪佛龙 0代表其他用户   1L代表雪佛龙用户  2L代表店长  3L代表service partner admin角色用户 */
	private Long mUserTypes = 0L;
	private String chRoleName = "";

	/** 权限菜单 */
	private List<WxTMenu> menuList;
	private List<WxTRole> roleList;
	/** 权限菜单json */
	private String menuListJson;

	/** BD 查看sp所有  workshop的权限 */
	private String bdWorkshopPower;
	/** 判断是否是ParterDB角色 */
	private boolean isPartnerDB;

	/** 修改密码权限 */
	private int changePwdPermission = 0;

	/** 用户类型。分chevron, sp, ws add by lizhentao 20161212 */
	private String userModel;
	private Date userIntime;

	private Date userOuttime;

	private String description;

	private String isValid;

	private Date xzTime;

	private String xzUser;

	private Date xgTime;

	private String xgUser;

    // 1=正常，0=删除
	private Integer status;

	private Long tenantId;

	private Integer resetFlag;

	private Integer orgType;

	@ApiModelProperty(value="扩展标记字段(位操作。1-登录无需验证密码策略，2-密码已过期，4-即将过期，8-已停用（90天未登录）)", name="extFlag")
    private Integer extFlag;
	
	@ApiModelProperty(value="添加扩展标记字段", name="addExtFlag")
    private Integer addExtFlag;

	@ApiModelProperty(value="取消扩展标记字段", name="removeExtFlag")
    private Integer removeExtFlag;

	/**
	 * 针对技师对应的系统openid add by b0.liu 20170216
	 */
	private String wechatCode;
	
	/** 登录无需验证密码规则 */
	public final static int EXT_FLAG_LOGIN_NO_CHECK_PWD_RULE = 1;
	/** 密码已过期 */
	public final static int EXT_FLAG_PWD_EXPIRED = 2;
	/** 即将过期 */
	public final static int EXT_FLAG_PWD_TOBE_EXPIRE = 4;
	/** 已停用 */
	public final static int EXT_FLAG_USER_FROZEN = 8;
	/** 账号停用邮件标记 */
	public final static int EXT_FLAG_USER_FROZEN_EMAIL = 16;

	/** chevron用户所属BU */
	private String bu;

	/**
	 * 是否中外运管理员角色
	 */
	private boolean isZWY_Admin = false;

	/** 登录用户所属Sales Channel */
	private String salesChannel;

	/**
	 * 是否合伙人出、入库员
	 */
	private boolean isService_Partner_CLERK = false;

	/** 管理页面操作权限编码(1-同一个机构权限，2-限制机构权限) */
	private final static String OPERATION_PERMISSION_MODULE_CODE = "UserPage";

	private Integer pageWeight = null;

	private Boolean pageOrgLimit = null;
	
	/** 用户包含渠道 */
	private int includeChannels;
	
	/** 销售等级 1-按FLSR查数据,其他-按region查询数据权限 */
	private int salesLevel = 0;
	
	/** APP登录权限 */
	private boolean appRight;
	
	/** 用户标记 */
	private int userFlag;
	
	/** 经销商用户的经销商属性 */
	private int partnerFlag;
	
	/** 系统集成账户 */
	private boolean systemIntegrationUser;
	
	public final static int CHANNEL_CDM = 1;
	
	public final static int CHANNEL_C_I = 2;
	
	public final static int CHANNEL_OEM = 4;
	
	public final static int CHANNEL_INDUSTRIAL = 8;
	
	/** 用户验真扫码虚拟用户 */
	public final static long USER_ID_USER_VALIDATION_SCAN = -2L;
	
	/** 技师扫码虚拟用户 */
	public final static long USER_ID_MECHANIC_SCAN = -3L;

	private PermissionCache permissionCache;
	
	/** 扩展属性1（[登录]：过期时间;[过期邮件]：密码到期日期） */
    private String extProperty1;
	
	/** 扩展属性2（[过期邮件]：密码有效期） */
    private String extProperty2;
	
	/** 登录token */
	private String token;

	private List<String> permissionKeyList;

	public List<String> getPermissionKeyList() {
		return permissionKeyList;
	}

	public void setPermissionKeyList(List<String> permissionKeyList) {
		this.permissionKeyList = permissionKeyList;
	}

	public Long getPost() {
		return post;
	}

	public void setPost(Long post) {
		this.post = post;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public Long getPhotoId() {
		return photoId;
	}

	public void setPhotoId(Long photoId) {
		this.photoId = photoId;
	}

	public Integer getReceiveMsg() {
		return receiveMsg;
	}

	public void setReceiveMsg(Integer receiveMsg) {
		this.receiveMsg = receiveMsg;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getSalt() {
		return salt;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.salt
	 *
	 * @param salt the value for wx_t_user.salt
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setSalt(String salt) {
		this.salt = salt;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.pwd_lasttime
	 *
	 * @return the value of wx_t_user.pwd_lasttime
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Date getPwdLasttime() {
		return pwdLasttime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.pwd_lasttime
	 *
	 * @param pwdLasttime the value for wx_t_user.pwd_lasttime
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setPwdLasttime(Date pwdLasttime) {
		this.pwdLasttime = pwdLasttime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.ch_name
	 *
	 * @return the value of wx_t_user.ch_name
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getChName() {
		return chName;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.ch_name
	 *
	 * @param chName the value for wx_t_user.ch_name
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setChName(String chName) {
		this.chName = chName;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.cn_name
	 *
	 * @return the value of wx_t_user.cn_name
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getCnName() {
		return cnName;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.cn_name
	 *
	 * @param cnName the value for wx_t_user.cn_name
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setCnName(String cnName) {
		this.cnName = cnName;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.pinyin
	 *
	 * @return the value of wx_t_user.pinyin
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getPinyin() {
		return pinyin;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.pinyin
	 *
	 * @param pinyin the value for wx_t_user.pinyin
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setPinyin(String pinyin) {
		this.pinyin = pinyin;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.allow_login
	 *
	 * @return the value of wx_t_user.allow_login
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getAllowLogin() {
		return allowLogin;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.allow_login
	 *
	 * @param allowLogin the value for wx_t_user.allow_login
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setAllowLogin(String allowLogin) {
		this.allowLogin = allowLogin;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.sex
	 *
	 * @return the value of wx_t_user.sex
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getSex() {
		return sex;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.sex
	 *
	 * @param sex the value for wx_t_user.sex
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setSex(String sex) {
		this.sex = sex;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.birthday
	 *
	 * @return the value of wx_t_user.birthday
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Date getBirthday() {
		return birthday;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.birthday
	 *
	 * @param birthday the value for wx_t_user.birthday
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.address
	 *
	 * @return the value of wx_t_user.address
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.address
	 *
	 * @param address the value for wx_t_user.address
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.email
	 *
	 * @return the value of wx_t_user.email
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getEmail() {
		return email;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.email
	 *
	 * @param email the value for wx_t_user.email
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setEmail(String email) {
		this.email = email;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.mobile_tel
	 *
	 * @return the value of wx_t_user.mobile_tel
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getMobileTel() {
		return mobileTel;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.mobile_tel
	 *
	 * @param mobileTel the value for wx_t_user.mobile_tel
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setMobileTel(String mobileTel) {
		this.mobileTel = mobileTel;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.fixed_tel
	 *
	 * @return the value of wx_t_user.fixed_tel
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getFixedTel() {
		return fixedTel;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.fixed_tel
	 *
	 * @param fixedTel the value for wx_t_user.fixed_tel
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setFixedTel(String fixedTel) {
		this.fixedTel = fixedTel;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.org_id
	 *
	 * @return the value of wx_t_user.org_id
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Long getOrgId() {
		return orgId;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.org_id
	 *
	 * @param orgId the value for wx_t_user.org_id
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.user_intime
	 *
	 * @return the value of wx_t_user.user_intime
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Date getUserIntime() {
		return userIntime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.user_intime
	 *
	 * @param userIntime the value for wx_t_user.user_intime
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setUserIntime(Date userIntime) {
		this.userIntime = userIntime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.user_outtime
	 *
	 * @return the value of wx_t_user.user_outtime
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Date getUserOuttime() {
		return userOuttime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.user_outtime
	 *
	 * @param userOuttime the value for wx_t_user.user_outtime
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setUserOuttime(Date userOuttime) {
		this.userOuttime = userOuttime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.description
	 *
	 * @return the value of wx_t_user.description
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.description
	 *
	 * @param description the value for wx_t_user.description
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.is_valid
	 *
	 * @return the value of wx_t_user.is_valid
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getIsValid() {
		return isValid;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.is_valid
	 *
	 * @param isValid the value for wx_t_user.is_valid
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setIsValid(String isValid) {
		this.isValid = isValid;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.xz_time
	 *
	 * @return the value of wx_t_user.xz_time
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Date getXzTime() {
		return xzTime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.xz_time
	 *
	 * @param xzTime the value for wx_t_user.xz_time
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setXzTime(Date xzTime) {
		this.xzTime = xzTime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.xz_user
	 *
	 * @return the value of wx_t_user.xz_user
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getXzUser() {
		return xzUser;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.xz_user
	 *
	 * @param xzUser the value for wx_t_user.xz_user
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setXzUser(String xzUser) {
		this.xzUser = xzUser;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.xg_time
	 *
	 * @return the value of wx_t_user.xg_time
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Date getXgTime() {
		return xgTime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.xg_time
	 *
	 * @param xgTime the value for wx_t_user.xg_time
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setXgTime(Date xgTime) {
		this.xgTime = xgTime;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.xg_user
	 *
	 * @return the value of wx_t_user.xg_user
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public String getXgUser() {
		return xgUser;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.xg_user
	 *
	 * @param xgUser the value for wx_t_user.xg_user
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setXgUser(String xgUser) {
		this.xgUser = xgUser;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.status
	 *
	 * @return the value of wx_t_user.status
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Integer getStatus() {
		return status;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.status
	 *
	 * @param status the value for wx_t_user.status
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method returns the value of the database column wx_t_user.tenant_id
	 *
	 * @return the value of wx_t_user.tenant_id
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public Long getTenantId() {
		return tenantId;
	}

	/**
	 * This method was generated by MyBatis Generator.
	 * This method sets the value of the database column wx_t_user.tenant_id
	 *
	 * @param tenantId the value for wx_t_user.tenant_id
	 *
	 * @mbggenerated Wed Jun 03 10:58:07 CST 2015
	 */
	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getPostion() {
		return postion;
	}

	public void setPostion(String postion) {
		this.postion = postion;
	}

	public String getPostionName() {
		return postionName;
	}

	public void setPostionName(String postionName) {
		this.postionName = postionName;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public List<WxTMenu> getMenuList() {
		return menuList;
	}

	public void setMenuList(List<WxTMenu> menuList) {
		this.menuList = menuList;
	}

	public String getMenuListJson() {
		return menuListJson;
	}

	public void setMenuListJson(String menuListJson) {
		this.menuListJson = menuListJson;
	}

	@Override
	public Object clone() {
		WxTUser o = null;
		try {
			o = (WxTUser) super.clone();
		} catch (CloneNotSupportedException e) {
			log.error("", e);
		}
		return o;
	}

	// by bo.liu
	public boolean isManger() {
		return isManger;
	}

	public void setManger(boolean isManger) {
		this.isManger = isManger;
	}

	public boolean isIstest() {
		return istest;
	}

	public void setIstest(boolean istest) {
		this.istest = istest;
	}

	public boolean isIstest_fun() {
		return istest_fun;
	}

	public void setIstest_fun(boolean istest_fun) {
		this.istest_fun = istest_fun;
	}

	public boolean isService_Partner_BD() {
		return isService_Partner_BD;
	}

	public void setService_Partner_BD(boolean isService_Partner_BD) {
		this.isService_Partner_BD = isService_Partner_BD;
	}

	public boolean isService_Partner_Manager() {
		return isService_Partner_Manager;
	}

	public void setService_Partner_Manager(boolean isService_Partner_Manager) {
		this.isService_Partner_Manager = isService_Partner_Manager;
	}

	public boolean isService_Partner_CSR() {
		return isService_Partner_CSR;
	}

	public void setService_Partner_CSR(boolean isService_Partner_CSR) {
		this.isService_Partner_CSR = isService_Partner_CSR;
	}

	public boolean isChevron_Manager() {
		return isChevron_Manager;
	}

	public void setChevron_Manager(boolean isChevron_Manager) {
		this.isChevron_Manager = isChevron_Manager;
	}

	public boolean isWorkshop_Manager() {
		return isWorkshop_Manager;
	}

	public void setWorkshop_Manager(boolean isWorkshop_Manager) {
		this.isWorkshop_Manager = isWorkshop_Manager;
	}

	public boolean isWorkshop_Mechanic() {
		return isWorkshop_Mechanic;
	}

	public void setWorkshop_Mechanic(boolean isWorkshop_Mechanic) {
		this.isWorkshop_Mechanic = isWorkshop_Mechanic;
	}

	public boolean isDataRoleTestOrganization() {
		return isDataRoleTestOrganization;
	}

	public void setDataRoleTestOrganization(boolean isDataRoleTestOrganization) {
		this.isDataRoleTestOrganization = isDataRoleTestOrganization;
	}

	public boolean isDataRoleMaillist() {
		return isDataRoleMaillist;
	}

	public void setDataRoleMaillist(boolean isDataRoleMaillist) {
		this.isDataRoleMaillist = isDataRoleMaillist;
	}

	// end

	public String getChRoleName() {
		return chRoleName;
	}

	public void setChRoleName(String chRoleName) {
		this.chRoleName = chRoleName;
	}

	public String getPartnerName() {
		return orgName;
	}

	@Deprecated
	public Long getmUserTypes() {
		if(USER_MODEL_CHEVRON.equals(userModel)) {
			return 1l;
		}
		return 0l;
	}

	public void setmUserTypes(Long mUserTypes) {
		this.mUserTypes = mUserTypes;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getBdWorkshopPower() {
		return bdWorkshopPower;
	}

	public void setBdWorkshopPower(String bdWorkshopPower) {
		this.bdWorkshopPower = bdWorkshopPower;
	}

	public boolean isPartnerDB() {
		return isPartnerDB;
	}

	public void setPartnerDB(boolean isPartnerDB) {
		this.isPartnerDB = isPartnerDB;
	}

	public List<WxTRole> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<WxTRole> roleList) {
		this.roleList = roleList;
	}

	public String getUserModel() {
		return userModel;
	}

	public void setUserModel(String userModel) {
		this.userModel = userModel;
	}

	public int getChangePwdPermission() {
		return changePwdPermission;
	}

	public void setChangePwdPermission(int changePwdPermission) {
		this.changePwdPermission = changePwdPermission;
	}

	public String getWechatCode() {
		return wechatCode;
	}

	public void setWechatCode(String wechatCode) {
		this.wechatCode = wechatCode;
	}

	public boolean isZWY_Admin() {
		return isZWY_Admin;
	}

	public void setZWY_Admin(boolean isZWY_Admin) {
		this.isZWY_Admin = isZWY_Admin;
	}

	public boolean isService_Partner_CLERK() {
		return isService_Partner_CLERK;
	}

	public void setService_Partner_CLERK(boolean isService_Partner_CLERK) {
		this.isService_Partner_CLERK = isService_Partner_CLERK;
	}

	public boolean isAdmin() {
		return isAdmin;
	}

	public void setAdmin(boolean isAdmin) {
		this.isAdmin = isAdmin;
	}

	@JsonIgnore
	public PermissionCache getPermissionCache() {
		return permissionCache;
	}

	public void setPermissionCache(PermissionCache permissionCache) {
		this.permissionCache = permissionCache;
	}

	public Integer getResetFlag() {
		return resetFlag;
	}

	public void setResetFlag(Integer resetFlag) {
		this.resetFlag = resetFlag;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}

	@Deprecated
	public boolean _isChevronLimitModel() {
		if (isAdmin) {
			return false;
		}
		if (pageOrgLimit == null) {
			if ((getPageWeight() & 2) == 2) {
				pageOrgLimit = true;
			} else {
				Map<String, Object> paramsMap = new HashMap<String, Object>(5);
				paramsMap.put("userId", getUserId());
				paramsMap.put("funFlag", "permission_user_page");
				pageOrgLimit = !SpringUtils.getBean(PartnerResponsibleVoMapper.class).queryPartnerResponsible(paramsMap)
						.isEmpty();
			}
		}
		return pageOrgLimit;
	}

	@Deprecated
	public boolean _isUserPageSameOrgPermission() {
		if (isAdmin) {
			return false;
		}
		return (getPageWeight() & 1) == 1;
	}

	@Deprecated
	private int getPageWeight() {
		if (pageWeight == null) {
			try {
				pageWeight = SpringUtils.getBean(OperationPermissionBizService.class).getPermissionWeight(userId,
						OPERATION_PERMISSION_MODULE_CODE);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
		return pageWeight;
	}

	public String getCai() {
		return cai;
	}

	public void setCai(String cai) {
		this.cai = cai;
	}

	@Deprecated
	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getDefaultLocale() {
		return defaultLocale;
	}

	public void setDefaultLocale(String defaultLocale) {
		this.defaultLocale = defaultLocale;
	}

	public boolean isAppRight() {
		return appRight;
	}

	public void setAppRight(boolean appRight) {
		this.appRight = appRight;
	}

	public int getIncludeChannels() {
		return includeChannels;
	}

	public void setIncludeChannels(int includeChannels) {
		this.includeChannels = includeChannels;
	}

	public boolean isSystemIntegrationUser() {
		return systemIntegrationUser;
	}

	public void setSystemIntegrationUser(boolean systemIntegrationUser) {
		this.systemIntegrationUser = systemIntegrationUser;
	}

	public int getSalesLevel() {
		return salesLevel;
	}

	public void setSalesLevel(int salesLevel) {
		this.salesLevel = salesLevel;
	}

	public int getUserFlag() {
		return userFlag;
	}

	public void setUserFlag(int userFlag) {
		this.userFlag = userFlag;
	}

	public int getPartnerFlag() {
		return partnerFlag;
	}

	public void setPartnerFlag(int partnerFlag) {
		this.partnerFlag = partnerFlag;
	}

	public String getRegionNameCn() {
		return regionNameCn;
	}

	public void setRegionNameCn(String regionNameCn) {
		this.regionNameCn = regionNameCn;
	}

	public Long getDistributorId() {
		return distributorId;
	}

	public void setDistributorId(Long distributorId) {
		this.distributorId = distributorId;
	}

	public Integer getExtFlag() {
		return extFlag;
	}

	public void setExtFlag(Integer extFlag) {
		this.extFlag = extFlag;
	}

	public Integer getAddExtFlag() {
		return addExtFlag;
	}

	public void setAddExtFlag(Integer addExtFlag) {
		this.addExtFlag = addExtFlag;
	}

	public Integer getRemoveExtFlag() {
		return removeExtFlag;
	}

	public void setRemoveExtFlag(Integer removeExtFlag) {
		this.removeExtFlag = removeExtFlag;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getExtProperty1() {
		return extProperty1;
	}

	public void setExtProperty1(String extProperty1) {
		this.extProperty1 = extProperty1;
	}

	public String getExtProperty2() {
		return extProperty2;
	}

	public void setExtProperty2(String extProperty2) {
		this.extProperty2 = extProperty2;
	}
	

}