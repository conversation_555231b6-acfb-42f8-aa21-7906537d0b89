package com.chevron.b2b.business.impl;

import com.chevron.b2b.business.ScheduleInstanceBizService;
import com.chevron.b2b.dao.ActivityAgreeMapper;
import com.chevron.b2b.dao.ScheduleInstanceMapper;
import com.chevron.b2b.dao.ScheduleItemInstanceMapper;
import com.chevron.b2b.model.ActivityAgree;
import com.chevron.b2b.model.ActivityAgreeExample;
import com.chevron.b2b.model.ScheduleInstance;
import com.chevron.b2b.model.ScheduleInstanceExample;
import com.chevron.b2b.model.ScheduleInstanceParams;
import com.chevron.b2b.model.ScheduleItemInstanceExample;
import com.chevron.point.business.PointBizService;
import com.chevron.point.model.B2BPointDetail;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 计划实例业务接口实现类
 * <AUTHOR>
 * @version 1.0 2018-12-20 17:16
 */
@Service
public class ScheduleInstanceBizServiceImpl implements ScheduleInstanceBizService {
	@Autowired
	private ScheduleInstanceMapper scheduleInstanceMapper;
	
	@Resource
	private ScheduleItemInstanceMapper scheduleItemInstanceMapper;
	
	@Autowired
	private PointBizService pointBizService;
	
	@Autowired
	private ActivityAgreeMapper activityAgreeMapper;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(ScheduleInstance record) throws WxPltException {
		record.setDeleteFlag(0);
		record.setCreateUserId(ContextUtil.getCurUserId());
		record.setCreateTime(DateUtil.getCurrentDate());
		scheduleInstanceMapper.insertSelective(record);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(ScheduleInstance record) throws WxPltException {
		ScheduleInstance instance = null;
		if(record.getStatus() != null && record.getStatus() >= ScheduleInstance.STATUS_SUBMIT) {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("id", record.getId());
			instance = scheduleInstanceMapper.queryByParams(params).get(0);
		}
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();
		record.setUpdateUserId(userId);
		record.setUpdateTime(now);
		if(instance != null && record.getStatus() == 15) {
			//设置提交时间
			record.setSubmitTime(now);
			record.setRemark("-1");
			//无后续步骤，活动完成
			if(instance.getLeftSteps() == 0) {
				record.setStatus(ScheduleInstance.STATUS_COMPLETED);
			}
		}else if(record.getStatus() != null && record.getStatus() == ScheduleInstance.STATUS_JOINED) {
			record.setCreateTime(now);
			record.setCreateUserId(userId);
		}
		if(scheduleInstanceMapper.updateByPrimaryKeySelective(record) < 1) {
			throw new WxPltException("任务已完成，不能再修改");
		}
		if(record.getItemList() != null && !record.getItemList().isEmpty()) {
			//删除原数据
			ScheduleItemInstanceExample scheduleItemInstanceExample = new ScheduleItemInstanceExample();
			scheduleItemInstanceExample.createCriteria().andScheduleInstanceIdEqualTo(record.getId());
			scheduleItemInstanceMapper.deleteByExample(scheduleItemInstanceExample);
			//批量插入
			scheduleItemInstanceMapper.insertBatch(record.getItemList());
		}
		if(record.getStatus() != null && record.getStatus() == ScheduleInstance.STATUS_COMPLETED 
				&& instance.getAwardPoint() != null && (instance.getFixedPointFlag() == 0 || instance.getAwardPoint() > 0.0000001)) {
			//完成活动，给奖励积分
			List<B2BPointDetail> list = new ArrayList<B2BPointDetail>();
			B2BPointDetail b2bPointDetail = new B2BPointDetail();
			b2bPointDetail.setComments("【" + DateUtil.getDateStr(now, "yyyy-MM-dd") + "】" + instance.getScheduleTypeText() + 
					"完成奖励积分【" + instance.getAwardPoint() + "】");
			if(instance.getFixedPointFlag() == 0) {
				//积分范围，由传入值确定
				if(record.getAwardPoint() > instance.getMaxPoint() || record.getAwardPoint() < instance.getAwardPoint()) {
					throw new WxPltException("奖励积分超出" + instance.getAwardPoint() + "~" + instance.getMaxPoint());
				}
				b2bPointDetail.setPoint(record.getAwardPoint());
			}else {
				b2bPointDetail.setPoint(instance.getAwardPoint());
			}
			b2bPointDetail.setSalesChannel(instance.getSalesChannel());
			b2bPointDetail.setTradeNo(instance.getId().toString());
			b2bPointDetail.setUserId(instance.getExecutorKey());
			if("activity".equals(instance.getScheduleType())) {
				b2bPointDetail.setEarnType("ACTIVITY_REWARD");
			}else {
				b2bPointDetail.setEarnType("MISSION_REWARD");
			}
			list.add(b2bPointDetail);
			try {
				pointBizService.importB2BPoint(list);
			} catch (WxPltException e) {
				throw e;
			} catch (RuntimeException e) {
				throw e;
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		ScheduleInstanceExample example = new ScheduleInstanceExample();
		example.createCriteria().andIdIn(ids);
		scheduleInstanceMapper.deleteByExample(example);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(ScheduleInstanceExample example) throws WxPltException {
		scheduleInstanceMapper.deleteByExample(example);
	}

	@Override
	public List<ScheduleInstance> queryByExample(ScheduleInstanceExample example) throws WxPltException {
		return scheduleInstanceMapper.selectByExample(example);
	}

	@Override
    public List<ScheduleInstance> queryByParams(Map<String, Object> params) throws WxPltException {
    	return scheduleInstanceMapper.queryByParams(params);
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public ScheduleInstance getBean(Long id) throws WxPltException{
		if(id > 100000000000000l) {
			//活动执行实例未创建
			long mechanicId = id / 10000000l;
			long scheduleEntityId = id - mechanicId * 10000000l;
			mechanicId -= 100000000l;
			ScheduleInstance record = new ScheduleInstance();
			record.setScheduleId(scheduleEntityId);
			record.setMechanicId(mechanicId);
			record.setCreateTime(DateUtil.getCurrentDate());
			record.setCreateUserId(ContextUtil.getCurUserId());
			try {
				scheduleInstanceMapper.insertByGetBean(record);
				id = record.getId();
				scheduleItemInstanceMapper.insertBatchByScheduleInstance(id);
			} catch (Exception e) {
				Map<String, Object> uks = new HashMap<String, Object>(5);
				uks.put("scheduleEntityId", scheduleEntityId);
				uks.put("mechanicId", mechanicId);
				return scheduleInstanceMapper.getBeanByUks(uks);
			}
		}
		return scheduleInstanceMapper.selectByPrimaryKey(id);
	}	

	@Override
	public void queryForPage(ScheduleInstanceParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, scheduleInstanceMapper.queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public void addAgree(Long activityId, String userId) throws WxPltException {
		ActivityAgreeExample example = new ActivityAgreeExample();
		example.createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userId);
		if(activityAgreeMapper.countByExample(example) > 0) {
			throw new WxPltException("不能重复点赞");
		}
		ActivityAgree activityAgree = new ActivityAgree();
		activityAgree.setActivityId(activityId);
		activityAgree.setUserId(userId);
		activityAgreeMapper.insertSelective(activityAgree);
	}

	@Override
	public void cancelAgree(Long activityId, String userId) throws WxPltException {
		ActivityAgreeExample example = new ActivityAgreeExample();
		example.createCriteria().andActivityIdEqualTo(activityId).andUserIdEqualTo(userId);
		activityAgreeMapper.deleteByExample(example);
	}
}
