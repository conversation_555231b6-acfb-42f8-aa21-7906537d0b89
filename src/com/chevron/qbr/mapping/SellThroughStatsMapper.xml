<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.qbr.dao.SellThroughStatsMapper" >
    <resultMap id="BaseResultMap" type="com.chevron.qbr.model.SellThroughStatsItem" >
        <!--private Double liters;-->
        <!--private String customerType;-->
        <result column="customer_type" property="customerType" jdbcType="VARCHAR" />
        <result column="liters" property="liters" jdbcType="NUMERIC" />
    </resultMap>
    <resultMap id="TradingResultMap" type="com.chevron.qbr.model.SellThroughTradingStatsItem" >
        <result column="customer_type" property="customerType" jdbcType="VARCHAR" />
        <result column="trade_count" property="tradeCount" jdbcType="BIGINT" />
    </resultMap>
    <resultMap id="CustomerResultMap" type="com.chevron.qbr.model.SellThroughCustomerStatsItem" >
        <result column="customer_type" property="customerType" jdbcType="VARCHAR" />
        <result column="customer_count" property="customerCount" jdbcType="BIGINT" />
    </resultMap>
    <resultMap id="ComplexResultMap" type="com.chevron.qbr.model.SellThroughComplexStatsItem" >
        <!--private int customerCount;-->
        <!--private String customerType;-->
        <result column="year" property="year" jdbcType="INTEGER" />
        <result column="quarter" property="quarter" jdbcType="INTEGER" />
        <result column="customer_type" property="customerType" jdbcType="VARCHAR" />
        <result column="trade_count" property="tradeCount" jdbcType="BIGINT" />
        <result column="customer_count" property="customerCount" jdbcType="BIGINT" />
    </resultMap>
    <select id="queryTotalStats" resultMap="BaseResultMap" parameterType="com.chevron.qbr.model.StatsQueryParam" >
        select
            round(isnull(sum(st.liters),0),2) liters,
            st.distributor_customer_type customer_type
        from dbo.dw_pp_dms_trans_sell_through st
            left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = st.distributor_id
        where 1=1
            and st.distributor_customer_type != 'NULL'
            and st.distributor_customer_type is not null
            <if test="partnerId != null ">
                and poe.partner_id = #{partnerId, jdbcType=BIGINT}
            </if>
            <if test="startDate != null and endDate != null">
                and trans_time between #{startDate, jdbcType=TIMESTAMP} and  #{endDate, jdbcType=TIMESTAMP}
            </if>
        group by st.distributor_customer_type
        order by st.distributor_customer_type
    </select>

    <select id="queryTradingStats" resultMap="TradingResultMap" parameterType="com.chevron.qbr.model.StatsQueryParam" >
        select
        isnull(count(1),0) trade_count,
        st.distributor_customer_type customer_type
        from dbo.dw_pp_dms_trans_sell_through st
            left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = st.distributor_id
        where 1=1
        and st.distributor_customer_type != 'NULL'
        and st.distributor_customer_type is not null
        <if test="partnerId != null ">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        <if test="startDate != null and endDate != null">
            and trans_time between #{startDate, jdbcType=TIMESTAMP} and  #{endDate, jdbcType=TIMESTAMP}
        </if>
        group by st.distributor_customer_type
        order by st.distributor_customer_type
    </select>

    <select id="queryCustomerStats" resultMap="CustomerResultMap" parameterType="com.chevron.qbr.model.StatsQueryParam" >
        select count(customer_info.distributor_customer_name) customer_count,customer_info.distributor_customer_type customer_type
        from(
            select
            distinct st.customer_id,st.distributor_customer_type,st.distributor_customer_name
            from dbo.dw_pp_dms_trans_sell_through st
                left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = st.distributor_id
            where 1=1
            and st.distributor_customer_type != 'NULL'
            and st.distributor_customer_type is not null
            <if test="partnerId != null ">
                and poe.partner_id = #{partnerId, jdbcType=BIGINT}
            </if>
            <if test="startDate != null and endDate != null">
                and trans_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate, jdbcType=TIMESTAMP}
            </if>
        ) customer_info
        group by customer_info.distributor_customer_type
        order by customer_info.distributor_customer_type
    </select>
    <select id="queryCustomerAndTradeStatsByQuarter" resultMap="ComplexResultMap" parameterType="com.chevron.qbr.model.StatsQueryParam" >
        select sum(customer_count) as customer_count,sum(trade_count) trade_count,customer_type,year,quarter from
        (
        select
        isnull(count(1),0) trade_count,
        null customer_count,
        st.distributor_customer_type customer_type,
        datepart(year,st.trans_time) year,
        datepart(quarter,st.trans_time) quarter
        from dbo.dw_pp_dms_trans_sell_through st
        left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = st.distributor_id
        where 1=1
        and st.distributor_customer_type != 'NULL'
        and st.distributor_customer_type is not null
        <if test="partnerId != null ">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        and datepart(year,st.trans_time) = #{year, jdbcType=INTEGER}
        and datepart(quarter,st.trans_time) = #{curQuarter, jdbcType=INTEGER}
        group by st.distributor_customer_type,datepart(year,st.trans_time),datepart(quarter,st.trans_time)
        union all
        select null trade_count,count(customer_info.distributor_customer_name) customer_count,customer_info.distributor_customer_type customer_type,year,quarter
        from(
        select
        distinct st.customer_id,st.distributor_customer_type,
                  st.distributor_customer_name,
                  datepart(year, st.trans_time)    year,
                  datepart(quarter, st.trans_time) quarter
        from dbo.dw_pp_dms_trans_sell_through st
        left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = st.distributor_id
        where 1=1
        and st.distributor_customer_type != 'NULL'
        and st.distributor_customer_type is not null
        <if test="partnerId != null ">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
         and datepart(year,st.trans_time) = #{year, jdbcType=INTEGER}
         and datepart(quarter,st.trans_time) = #{curQuarter, jdbcType=INTEGER}
        ) customer_info
        group by customer_info.distributor_customer_type, customer_info.year, customer_info.quarter
        ) total_info
        group by customer_type,year,quarter
        order by year,quarter,customer_type
    </select>
</mapper>