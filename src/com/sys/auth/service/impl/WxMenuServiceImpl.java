package com.sys.auth.service.impl;

import com.common.util.json.JsonGenerator;
import com.sys.auth.dao.WxTMenuMapper;
import com.sys.auth.dao.WxTRolesourceMapper;
import com.sys.auth.dao.WxTUserbaseMapper;
import com.sys.auth.model.*;
import com.sys.auth.model.WxTMenuExample.Criteria;
import com.sys.auth.service.WxMenuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.*;

@Service
public class WxMenuServiceImpl implements WxMenuService {

	@Resource
	private WxTMenuMapper menuMapper;
	@Resource
	private WxTRolesourceMapper rolesourceMapper;
	@Resource
	private WxTUserbaseMapper userbaseMapper;

	/**
	 * 初始化页面加载全部菜单
	 */
	@Override
	public Map<String, Object> getMenuList() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("code", "success");
		try {
			List<WxTMenu> menuList = menuMapper.selAllMenu();
			List<MenuVoTree> menuVoNodeList = new ArrayList<MenuVoTree>();
			List<MenuVoTree> resultJsonList = null;
			for (WxTMenu menu : menuList) {
				MenuVoTree mvt = new MenuVoTree();
				mvt.setId(menu.getMenuId());
				mvt.setPid(menu.getMenuPid());
				mvt.setText(menu.getMenuName());
				mvt.setMenuName(menu.getMenuName());
				mvt.setStatus(menu.getStatus());
				mvt.setMenuImgPath(menu.getMenuImgpath());
				mvt.setMenuUrl(menu.getMenuUrl());
				mvt.setSort(menu.getSort());
				mvt.setI18nCode(menu.getI18nCode());
                mvt.setExtFlag(menu.getExtFlag());
				menuVoNodeList.add(mvt);
			}
			resultJsonList = JsonGenerator.listToTree(menuVoNodeList);
			resultMap.put("resultData", resultJsonList);
		} catch (Exception e) {
			resultMap.put("code", "systemerror");
			e.printStackTrace();
		}
		return resultMap;
	}

	/**
	 * 按照id删除数据 id读过来是String，由这里改为Long
	 */
	@Override
	@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> delete(String id) {
		Long idLong = Long.parseLong(id);

		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 先通过id检查是否有子目录，有子目录则不准删除（没有子目录的根目录也可以删除）
		if (menuMapper.selectCountAllChildMenu(idLong) != 0) {
			resultMap.put("success", false);
			return resultMap;
		}

		// 删除该条数据
		try {
			//删除用户分配菜单权限
			WxTUserbase example = new WxTUserbase();
			example.setRsType(2);
			example.setSourceId(idLong);
			userbaseMapper.deleteByExample(example);
			//删除角色分配菜单权限
			WxTRolesource roleExample = new WxTRolesource();
			roleExample.setRsType(2);
			roleExample.setSourceId(idLong);
			rolesourceMapper.deleteByExample(roleExample);

			//再删除userbase表中的数据
			menuMapper.deleteByPrimaryKey(idLong);
			resultMap.put("success", true);
		} catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("success", false);
			e.printStackTrace();
		}

		return resultMap;
	}

	@Override
	@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> updateMenu(WxTMenu menu) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("code", "success");
		try {
			if (menu != null) {
				menuMapper.updateByPrimaryKeySelective(menu);
			} else {
				resultMap.put("code", "isNullUpdateObj");
			}
		} catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertMenu(WxTMenu menu) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		if(menu==null){
			//如果menu为空，则直接返回
			resultMap.put("code", "isNullInsertObj");
			return resultMap;
		}
		//如果pid为空，则添加为0，为根目录
		if (menu.getMenuPid() == null) {
			menu.setMenuPid(0L);
		}
			
		resultMap.put("code", "success");
		
		try {
			menu.setXgUser(1L);
			menu.setStatus(1L);
			menu.setTenantId("1");
			menu.setXgSj(Calendar.getInstance().getTime());
			menuMapper.insertSelective(menu);//已经自动向menu中添加了id
			menu.setMenuCode(menu.getMenuId().toString()); 
			menuMapper.updateByPrimaryKeySelective(menu);
			
			WxTRolesource rolesource = new WxTRolesource();
			rolesource.setSourceId(menu.getMenuId());
			rolesource.setRoleId(1001L);
			rolesource.setRsType(2);
			rolesource.setXgUser(1L);
			rolesource.setStatus(1);
			rolesource.setTenantId(1L);
			rolesource.setAddFlag("1");
			rolesourceMapper.insertSelective(rolesource);
		} catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public WxTMenu selectMenuByMenuName(String menuName) {
		WxTMenuExample example = new WxTMenuExample();
		Criteria criteria = example.createCriteria();
		criteria.andMenuNameEqualTo(menuName);
		List<WxTMenu> list = menuMapper.selectByExample(example);
		if(null!= list && list.size()>0){
			return list.get(0);
		}else{
			return null;
		}
	}

	@Override
	public List<WxTMenu> selectMenuByMenuUrl(String menuUrl) {
		WxTMenuExample example = new WxTMenuExample();
		Criteria criteria = example.createCriteria();
		criteria.andMenuUrlEqualTo(menuUrl);
		return menuMapper.selectByExample(example);
	}

}
