select o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "门店名称",  r3.region_name "省份", r2.region_name "城市",r1.region_name "区县",
w.work_shop_address "门店地址", 
(select di.dic_item_name from wx_t_dic_item di where di.dic_type_code='Workshop.fromSource' and di.dic_item_code=w.from_source) "门店来源",
case when w.shop_recruitment=1 then '是' else '否' end "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then '是' else '否' end "B2B店",
case when w.dms_key is null or w.dms_key='' then '否' else '是' end "DMS店",
(case when w.status='0' then '潜在' when w.status='3' then '合作' when w.status='1' then '已功店'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' else w.status end) "门店状态", w.contact_person_tel, w.create_time "录入时间",
 w.update_time "最后修改时间"
 --update w set w.delete_flag=0
from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=w.excute_user_id
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
where w.delete_flag=1 and w.update_time>='2020-05-01' and o.status=1 and o.id!=9 and work_shop_name like '%青岛文拓商贸有限公司%'
--order by o.id, w.update_time desc
