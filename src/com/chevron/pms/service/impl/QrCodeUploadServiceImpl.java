package com.chevron.pms.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ContextLoader;

import com.chevron.pms.service.QrCodeService;
import com.chevron.pms.service.QrCodeUploadService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.token.util.TokenUtil;
import com.common.util.ContextUtil;
import com.common.util.SftpUtil;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.UserServiceI;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.email.service.EmailSenderService;

@Service
public class QrCodeUploadServiceImpl implements QrCodeUploadService {

	private static Logger log = LoggerFactory
			.getLogger(QrCodeUploadServiceImpl.class);

	public String UPLOAD_FLODER = "Qr folder";
	public static final String QRCODE_UPLOAD_CONFIRM_TYPE = "qrcodeconfirm";
	public static final String TJ_QRCODE_UPLOAD_CONFIRM_TYPE = "qrcodeTjconfirm";
	public static final String CONFIRM_STATUS = "2";// 已确认的状态

	@Autowired
	private DicService dicService;
	@Resource
	private EmailSenderService emailSenderService;
	@Resource
	private UserServiceI userService;
	@Autowired
	private QrCodeService qrCodeService;

	@Override
	public Map<String, Object> uploadQRCodeToSftp(File file, String batchId)
			throws Exception {
		log.info("batchId: {}", batchId);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> SFTPMap = null;
		String sourceFileName = file.getName();
		try {
			// 上传到牡丹
			{
				// 临时存放文件的路径
				// String desFilePath =
				// request.getRealPath("/")+MyPropertyConfigurer.getVal("DES_SAVE_QRCODE_FILE");
				// 获取sftp文件上传的路径信息
				SFTPMap = getSftpServerInfo();
				String uploadFolder = (String) SFTPMap.get("sftpUplodFoler");
				UPLOAD_FLODER = (uploadFolder == null || uploadFolder.isEmpty()) ? UPLOAD_FLODER
						: uploadFolder;
				// 1.上传到sftp
				log.info("fileNamePath: {},   fileName:{}, sourceFileName:{}",
						file.getAbsolutePath(), file.getName(), sourceFileName);
				File[] files = new File[] { file };
				doUploadFile(SFTPMap, files, sourceFileName);
				// 2.发送邮件
				Map<String, Object> baseDataMap = new HashMap<String, Object>();
				// UPLOAD_FLODER
				// ===null||?UPLOAD_FLODER:(String)SFTPMap.get("sftpUplodFoler");
				log.info("sftpUplodFoler: {}", UPLOAD_FLODER);
				baseDataMap.put("fileFolder", UPLOAD_FLODER);
				baseDataMap.put("fileName", sourceFileName);
				baseDataMap.put("fileSize", file.length());
				baseDataMap.put("batchId", batchId);
				sndEmail(baseDataMap);
				// 3.更新批次状态，为已经上传
				qrCodeService.updateQRcodeUploadFlagByBatchId(batchId, "1");
			}
		} catch (Exception e) {
			// 如果异常需要删除sftp上的文件
			if (null != SFTPMap) {
				doDeleteFile(SFTPMap, UPLOAD_FLODER, sourceFileName);
			}
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "上传异常:" + e.getLocalizedMessage());
			return resultMap;
		} finally {
			/*
			 * 删除临时文件 log.info("delete desFilePath: {}",desFilePath); File
			 * desFile = new File(desFilePath); if(desFile.isFile() &&
			 * desFile.exists()) { desFile.delete(); }
			 */
		}
		resultMap.put("code", "success");
		return resultMap;
	}

	@Override
	public Map<String, Object> uploadQRCodeToTjSftp(File file, String batchId)
			throws Exception {
		log.info("batchId: {}", batchId);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> tjSFTPMap = null;
		String sourceFileName = file.getName();
		try {
			// 上传到中商天津工厂
			{
				tjSFTPMap = getTjfactorySftpServerInfo();
				String uploadFolder = (String) tjSFTPMap.get("sftpUplodFoler");
				UPLOAD_FLODER = (uploadFolder == null || uploadFolder.isEmpty()) ? UPLOAD_FLODER
						: uploadFolder;
				// 1.上传到sftp
				log.info("fileNamePath: {},   fileName:{}, sourceFileName:{}",
						file.getAbsolutePath(), file.getName(), sourceFileName);
				File[] files = new File[] { file };
				doUploadFile(tjSFTPMap, files, sourceFileName);
				// 2.发送邮件
				Map<String, Object> baseDataMap = new HashMap<String, Object>();
				log.info("sftpUplodFoler: {}", UPLOAD_FLODER);
				baseDataMap.put("fileFolder", UPLOAD_FLODER);
				baseDataMap.put("fileName", sourceFileName);
				baseDataMap.put("fileSize", file.length());
				baseDataMap.put("batchId", batchId);
				sndTjEmail(baseDataMap);
				// 3.更新批次状态，为已经上传
				qrCodeService.updateQRcodeTjUploadFlagByBatchId(batchId, "1");
			}
		} catch (Exception e) {
			// TODO 如果异常需要删除sftp上的文件
			if (null != tjSFTPMap) {
				doDeleteFile(tjSFTPMap, UPLOAD_FLODER, sourceFileName);
			}
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "上传异常:" + e.getLocalizedMessage());
			return resultMap;
		} finally {
			/*
			 * 删除临时文件 log.info("delete desFilePath: {}",desFilePath); File
			 * desFile = new File(desFilePath); if(desFile.isFile() &&
			 * desFile.exists()) { desFile.delete(); }
			 */
		}
		resultMap.put("code", "success");
		return resultMap;
	}

	@Override
	public Map<String, Object> getSftpServerInfo() throws Exception {
		// 获取sftp服务器信息
		Map<String, Object> dicResult = dicService
				.getDicItemByDicTypeCode("qrcode.sftp");
		List<DicItemVo> itemlist = (ArrayList) dicResult.get("data");
		String sftpHost = "";
		int sftpPort = 0;
		String sftpAccount = "";
		String sftpPassword = "";
		String uplodfoler = "";
		for (DicItemVo dicItem : itemlist) {
			if (dicItem.getDicItemCode().equals("qrcode.sftp.host")) {
				sftpHost = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("qrcode.sftp.port")) {
				sftpPort = Integer.valueOf(dicItem.getDicItemName());
				continue;
			}
			if (dicItem.getDicItemCode().equals("qrcode.sftp.account")) {
				sftpAccount = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("qrcode.sftp.password")) {
				sftpPassword = dicItem.getDicItemName();
				continue;
			}
			if ("qrcode.sftp.uploadfolder".equals(dicItem.getDicItemCode())) {
				uplodfoler = dicItem.getDicItemName();
				continue;
			}
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("sftpHost", sftpHost);
		resultMap.put("sftpAccount", sftpAccount);
		resultMap.put("sftpPassword", sftpPassword);
		resultMap.put("sftpPort", sftpPort);
		resultMap.put("sftpUplodFoler", uplodfoler);
		return resultMap;
	}

	@Override
	public Map<String, Object> getTjfactorySftpServerInfo() throws Exception {
		// 获取sftp服务器信息
		Map<String, Object> dicResult = dicService
				.getDicItemByDicTypeCode("svc.cntjbc.sftp");
		List<DicItemVo> itemlist = (ArrayList) dicResult.get("data");
		String sftpHost = "";
		int sftpPort = 0;
		String sftpAccount = "";
		String sftpPassword = "";
		String uplodfoler = "";
		for (DicItemVo dicItem : itemlist) {
			if (dicItem.getDicItemCode().equals("svc.cntjbc.sftp.host")) {
				sftpHost = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("svc.cntjbc.sftp.port")) {
				sftpPort = Integer.valueOf(dicItem.getDicItemName());
				continue;
			}
			if (dicItem.getDicItemCode().equals("svc.cntjbc.sftp.account")) {
				sftpAccount = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("svc.cntjbc.sftp.password")) {
				sftpPassword = dicItem.getDicItemName();
				continue;
			}
			if ("svc.cntjbc.sftp.qrcode.uploadfolder".equals(dicItem
					.getDicItemCode())) {
				uplodfoler = dicItem.getDicItemName();
				continue;
			}
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("sftpHost", sftpHost);
		resultMap.put("sftpAccount", sftpAccount);
		resultMap.put("sftpPassword", sftpPassword);
		resultMap.put("sftpPort", sftpPort);
		resultMap.put("sftpUplodFoler", uplodfoler);
		return resultMap;
	}

	@Override
	public boolean doUploadFile(Map<String, Object> sftpMap, File[] files,
			String desFileName) throws Exception {
		// 获取sftp服务器信息
		String sftpHost = (String) sftpMap.get("sftpHost");
		int sftpPort = (Integer) sftpMap.get("sftpPort");
		String sftpAccount = (String) sftpMap.get("sftpAccount");
		String sftpPassword = (String) sftpMap.get("sftpPassword");
		SftpUtil.sshSftpFile(sftpHost, sftpAccount, sftpPassword, sftpPort,
				files, desFileName, UPLOAD_FLODER);
		return true;
	}

	@Override
	public boolean doDeleteFile(Map<String, Object> sftpMap, String desFloder,
			String desFileName) throws Exception {
		// 获取sftp服务器信息
		String sftpHost = (String) sftpMap.get("sftpHost");
		int sftpPort = (Integer) sftpMap.get("sftpPort");
		String sftpAccount = (String) sftpMap.get("sftpAccount");
		String sftpPassword = (String) sftpMap.get("sftpPassword");
		SftpUtil.sshSftpDeleteFile(sftpHost, sftpAccount, sftpPassword,
				sftpPort, desFloder, desFileName);
		return true;
	}

	@Override
	public Map<String, Object> sndEmail(Map<String, Object> baseDataMap)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> emailInfoMap = getSndEmailInfo(baseDataMap);
		if (null == emailInfoMap) {
			throw new Exception("qrcode确认者邮箱没有配置");
		}
		emailInfoMap.put("fileFolder", (String) baseDataMap.get("fileFolder"));
		emailInfoMap.put("fileName", (String) baseDataMap.get("fileName"));
		emailInfoMap.put("fileSize", (Long) baseDataMap.get("fileSize"));

		// 发送邮件信息
		ServletContext context = ContextLoader
				.getCurrentWebApplicationContext().getServletContext();
		emailInfoMap.put("msgcontent", "您有新的QRcode已经上传到sftp服务器，请确认！");
		emailSenderService.sendEmailForCommonNoDeleteAttachment(context,
				(String[]) emailInfoMap.get("accepters"),
				(String[]) emailInfoMap.get("ccaccepters"),
				MyPropertyConfigurer.getVal("mail.qrcode.upload.subject"),
				emailInfoMap, null,
				MyPropertyConfigurer.getVal("mail.qrcode.upload.ftl"));
		resultMap.put("code", "success");
		return resultMap;
	}

	@Override
	public Map<String, Object> getSndEmailInfo(Map<String, Object> paramMap)
			throws Exception {
		// 获取邮箱信息
		Map<String, Object> dicResult = dicService
				.getDicItemByDicTypeCode("qrcode.email");
		List<DicItemVo> itemlist = (ArrayList) dicResult.get("data");
		String accepterStr = "";
		String ccStr = "";
		String operLoginName = "";
		for (DicItemVo dicItem : itemlist) {
			if (dicItem.getDicItemCode().equals("qrcode.email.accepter")) {
				accepterStr = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("qrcode.email.cc")) {
				ccStr = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("qrcode.email.loginname")) {
				operLoginName = dicItem.getDicItemName();
				continue;
			}

		}
		// 获取确认者信息
		/*WxTUser tuser = userService.findUserByLoginName(operLoginName);*/
		WxTUser tuser = ContextUtil.getCurUser();
		if (accepterStr.isEmpty()) {
			log.info("getSndEmailInfo=========accepterStr----isEmpty");
			return null;
		}

		if (ccStr.isEmpty()) {
			log.info("getSndEmailInfo=========ccStr----is null");
			ccStr = null;
		}

		// 接收者
		String[] accepters = accepterStr.split(";");
		// 邮件抄送者
		String[] ccaccepters = null;
		if (null != ccStr) {
			ccaccepters = ccStr.split(";");
		}

		// Token链接
		/*String basePath = (String) Constants
				.getSystemPropertyByCodeType(Constants.APP_HOST) + "/";// "http://localhost:80/";//
		String confirmLink = "";
		confirmLink = basePath
				+ TokenUtil.generateTokenLink(
						String.valueOf(tuser.getUserId()),
						QRCODE_UPLOAD_CONFIRM_TYPE, "", "", true,
						(String) paramMap.get("batchId"), CONFIRM_STATUS);// 直接拒绝
*/
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("accepters", accepters);
		resultMap.put("ccaccepters", ccaccepters);
		/*resultMap.put("confirmlink", confirmLink);*/
		resultMap.put("acceptUserName", accepters[0].split("@")[0]);
		return resultMap;
	}

	@Override
	public Map<String, Object> sndTjEmail(Map<String, Object> baseDataMap)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> emailInfoMap = getTjSndEmailInfo(baseDataMap);
		if (null == emailInfoMap) {
			throw new Exception("qrcode确认者邮箱没有配置");
		}
		emailInfoMap.put("fileFolder", (String) baseDataMap.get("fileFolder"));
		emailInfoMap.put("fileName", (String) baseDataMap.get("fileName"));
		emailInfoMap.put("fileSize", (Long) baseDataMap.get("fileSize"));

		// 发送邮件信息
		ServletContext context = ContextLoader
				.getCurrentWebApplicationContext().getServletContext();
		emailInfoMap.put("msgcontent", "您有新的QRcode已经上传到sftp服务器，请确认！");
		emailSenderService.sendEmailForCommonNoDeleteAttachment(context,
				(String[]) emailInfoMap.get("accepters"),
				(String[]) emailInfoMap.get("ccaccepters"),
				MyPropertyConfigurer.getVal("mail.qrcode.upload.subject"),
				emailInfoMap, null,
				MyPropertyConfigurer.getVal("mail.qrcode.upload.ftl"));
		resultMap.put("code", "success");
		return resultMap;
	}

	@Override
	public Map<String, Object> getTjSndEmailInfo(Map<String, Object> paramMap)
			throws Exception {
		// 获取邮箱信息
		Map<String, Object> dicResult = dicService
				.getDicItemByDicTypeCode("svc.cntjbc.sftp");
		List<DicItemVo> itemlist = (ArrayList) dicResult.get("data");
		String accepterStr = "";
		String ccStr = "";
		String operLoginName = "";
		for (DicItemVo dicItem : itemlist) {
			if (dicItem.getDicItemCode().equals(
					"svc.cntjbc.sftp.email.accepter")) {
				accepterStr = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals("svc.cntjbc.sftp.email.cc")) {
				ccStr = dicItem.getDicItemName();
				continue;
			}
			if (dicItem.getDicItemCode().equals(
					"svc.cntjbc.sftp.email.loginname")) {
				operLoginName = dicItem.getDicItemName();
				continue;
			}

		}
		// 获取确认者信息
		WxTUser tuser = userService.findUserByLoginName(operLoginName);
		if (accepterStr.isEmpty()) {
			log.info("getSndEmailInfo=========accepterStr----isEmpty");
			return null;
		}

		if (ccStr.isEmpty()) {
			log.info("getSndEmailInfo=========ccStr----is null");
			ccStr = null;
		}

		// 接收者
		String[] accepters = accepterStr.split(";");
		// 邮件抄送者
		String[] ccaccepters = null;
		if (null != ccStr) {
			ccaccepters = ccStr.split(";");
		}

		// Token链接
		/*String basePath = (String) Constants
				.getSystemPropertyByCodeType(Constants.APP_HOST) + "/";// "http://localhost:80/";//
		String confirmLink = "";
		confirmLink = basePath
				+ TokenUtil.generateTokenLink(
						String.valueOf(tuser.getUserId()),
						TJ_QRCODE_UPLOAD_CONFIRM_TYPE, "", "", true,
						(String) paramMap.get("batchId"), CONFIRM_STATUS);// 直接拒绝
*/
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("accepters", accepters);
		resultMap.put("ccaccepters", ccaccepters);
		/*resultMap.put("confirmlink", confirmLink);*/
		resultMap.put("acceptUserName", accepters[0].split("@")[0]);
		return resultMap;
	}

}
