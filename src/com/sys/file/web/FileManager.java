package com.sys.file.web;

import com.chevron.budget.business.BudgetActualExpenseBizService;
import com.chevron.budget.business.BudgetImportExpenseBizService;
import com.chevron.budget.dao.BudgetExpenseItemMapper;
import com.chevron.budget.model.BudgetActualExpense;
import com.chevron.budget.model.BudgetExpenseItem;
import com.chevron.budget.model.BudgetImportExpense;
import com.chevron.budget2021.business.Budget2021ExpenseImportBizService;
import com.chevron.core.conf.business.PromotionalCampaignBizService;
import com.chevron.core.conf.model.PromotionalCampaign;
import com.chevron.exportdata.ExportExcel;
import com.chevron.importdata.ImportDataUtil;
import com.chevron.log.dao.WxTImportLogMapper;
import com.chevron.log.model.WxTImportLog;
import com.chevron.o2oorder.Constant.HttpClientUtils;
import com.chevron.partnerorder.business.SapSellinBizService;
import com.chevron.plc.model.InStockProductBugRecordVo;
import com.chevron.plc.service.AppInStockProductBugRecordService;
import com.chevron.pms.business.DDWorkshopServiceBillBizService;
import com.chevron.pms.business.OilVerificationBizService;
import com.chevron.pms.business.WorkshopBizService;
import com.chevron.pms.service.*;
import com.chevron.pms.service.impl.OrderDDBXServiceImpl;
import com.chevron.point.dto.PointType;
import com.chevron.task.service.TaskCheckServiceI;
import com.chevron.userselect.service.OrgTreeServiceI;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.WxUserServiceI;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.model.AttFileParams;
import com.sys.file.model.WxAttFile;
import com.sys.file.service.FileManagerServiceI;
import com.sys.log.util.LogUtils;
import com.sys.utils.model.AsynProcessStatus;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import com.zs.pms.service.*;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URLDecoder;
import java.util.*;

/**
 * SpringMVC中的文件上传,下载,更新
 *
 * <AUTHOR>
 * @create June 29, 2015
 */
@Controller
public class FileManager {
	private static Logger log = LoggerFactory.getLogger(FileManager.class);
	@Resource
	private WxUserServiceI wxUserService;

	@Resource
	private FileManagerServiceI fileManagerService;

	@Resource
	private OrgTreeServiceI orgTreeService;

	@Resource
	private TaskCheckServiceI taskCheckService;

	@Resource
	private QrCodeService qrCodeService;

	@Resource
	private CapCodeService capCodeService;

	@Resource
	private ImportDataService importDataServiceImpl;

	@Resource
	private WorkShopService workshopServiceImpl;// modify by bo.liu

	@Resource
	private OrderDDBXService orderDDBXServiceImpl;

	@Resource
	private DDWorkshopServiceBillBizService ddServiceBillBizService;

	@Resource
	private OilVerificationBizService oilVerificationBizService;

	@Resource
	private SapSellinBizService sapSellinBizServiceImpl;

	//
	@Resource
	private BudgetActualExpenseBizService budgetActualExpenseBizService ;
	@Resource
	private BudgetImportExpenseBizService budgetImportExpenseBizService ;
	@Autowired
	private AppInStockProductBugRecordService inStockProductBugRecordService;

	@Autowired
	private WorkshopBizService workshopBizService;

	@Autowired
	private WxTImportLogMapper importLogMapper;

	@Autowired
	private WxtZSVehicleFitlerDetailService zsFitlerDetailService;

	@Autowired
	private WxtZSVehicleDiscDetailService zsDiscDetailService;

	@Autowired
	private WxtZSVehicleOilDetailService zsOilDetailService;

	@Autowired
	private WxtZSVehicleBatteryDetailService zsBatteryService;

	@Autowired
	private WxtZSVehicleBafService zsBafService;

	@Autowired
	private WxtZSAntifreezeFluidService zsAntiFreezeFluidService;

	@Autowired
	private WxtZSBrakeFluidService zsBrakeFluidService;

	@Autowired
	private WxtZSTyreService zsTyreService;

	@Autowired
	private WxtZSVehicleMaintenanceRulesService zsMaintenanceRuleService;

	@Autowired
	private WxtZSVehicleTyreDetailService zsVehicleTyreDetailService;

	@Autowired
	private WxtZSVehicleRainwiperDetailService zsVehicleRainwiperDetailService;

	@Autowired
	private WxtZSVehicleSparkplugsDetailService zsVehicleSparkplugsDetailService;

	@Autowired
	private WxtZSVehicleTypeService zSVehicleTypeService;
	
	@Autowired
	private BudgetExpenseItemMapper budgetExpenseItemMapper;
	
	@Autowired
	private PromotionalCampaignBizService promotionalCampaignBizService;
	
	@Autowired
	private WxAttFileMapper attFileMapper;

	@Autowired
	private Budget2021ExpenseImportBizService budget2021ExpenseImportBizService;

	public static final String SUFFIX_XLS = ".xls";
	public static final String SUFFIX_XLSX = ".xlsx";
	public static final String SUFFIX_XLSM = ".xlsm";

	public static String defaultfileType = "image/jpeg";
	public static String fileUploadPath = null;
	private static String folderGrainSize = null;
	private static String remoteUser = null;
	private static String remoteUserPassword = null;
	public static String remoteFileUploadPath1 = null;
	public static String remoteFileUploadPath = null;
	public static final Map<String, String> sourceTypeMap = new HashMap<String, String>();
	public static final String LOCAL = "LOCAL";
	public static final String REMOTE = "REMOTE";
	/** 存储路径目录和源类型表 */
	public static Map<String, String> storePathFolderMap = new HashMap<String, String>();
	public static Map<String, Integer> thumbnailWidthMap = new HashMap<String, Integer>();
	public static Set<String> sharedFileSourceTypes = new HashSet<String>();

	static {
		sourceTypeMap.put(WxAttFile.MKT_HD_ICON, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PRODUCT_ICON, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PRODUCT_HD_ICON, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_TaskMain, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_People, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_Check, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_MainCheck, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_ProductIcon, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_App, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_Wrokshop, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_WrokshopContract, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_DDServiceBill, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_VerificationRewardContract, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_MaterialPicture, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_LogisticPicture, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PublishPicture, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PromoteMaterial, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PromoteActivity, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PromoteActivityFeedbacks, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PromoteActivityFeedbacks2, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_SellInOrderWorkFlow, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_RebateBillboard, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_RebateFile, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_ScanGanOrderPoints, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_PointAdjust, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_ThirdParty, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_B2B, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_SCHEDULE_BANNER, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_SCHEDULE_ICON, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_SharedFile, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_MktFile, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_Excel, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_QBR, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_QBR_OTHER, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_CioMktFile, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_CREDIT_APP, "LOCAL");
		sourceTypeMap.put(WxAttFile.SOURCETYPE_O2O_PICTURE, "LOCAL");
		sourceTypeMap.put(WxAttFile.SOURCETYPE_O2O_COMMENTS_PICTURE, "LOCAL");
		sourceTypeMap.put(WxAttFile.SourceType_WORKFLOW_ATT, "LOCAL");
		sourceTypeMap.put(WxAttFile.DB2B_ORDER_PICTURE, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCETYPE_TASK_YJZM_PDF, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCETYPE_TASK_YJZM_FILING, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCETYPE_QBR_REMARK, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCETYPE_SELL_IN, "LOCAL");
        sourceTypeMap.put(WxAttFile.NON_LOCAL_MAIN,"LOCAL");
        sourceTypeMap.put(WxAttFile.NON_LOCAL_MAIN_FILE,"LOCAL");
		sourceTypeMap.put(WxAttFile.CURRICULUM_DATA,"LOCAL");
		sourceTypeMap.put(WxAttFile.SOURCETYPE_OIL_SAMPLE_APPLY,"LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCE_TYPE_ELITE_DELIVERY, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCE_TYPE_Material2021_Picture, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCE_TYPE_Material_DIC_Att_Picture, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCE_TYPE_TRAINING, "LOCAL");
        sourceTypeMap.put(WxAttFile.SOURCE_TYPE_NON_LOCAL_FORM, "LOCAL");
		sourceTypeMap.put(WxAttFile.SOURCE_TYPE_ANSWERQUESTION_Picture, "LOCAL");
		sourceTypeMap.put(WxAttFile.SOURCE_TYPE_PAYMENT_NOTICE_EXCEL, "LOCAL");

		storePathFolderMap.put(WxAttFile.SourceType_PublishPicture, "publishpicture/");
		storePathFolderMap.put(WxAttFile.SourceType_SharedFile, "shared/");
		storePathFolderMap.put(WxAttFile.SourceType_B2B, "b2b/");
		storePathFolderMap.put(WxAttFile.SourceType_Excel, "import/");
		storePathFolderMap.put(WxAttFile.SourceType_QBR, "qbr/");
		storePathFolderMap.put(WxAttFile.SourceType_QBR_OTHER, "qbr_other/");
		storePathFolderMap.put(WxAttFile.SourceType_CREDIT_APP, "credit_app/");
		storePathFolderMap.put(WxAttFile.SourceType_WORKFLOW_ATT, "workflow/");
        storePathFolderMap.put(WxAttFile.SOURCETYPE_OIL_SAMPLE_APPLY, "workflow/oil_sample_apply");
		storePathFolderMap.put(WxAttFile.SOURCE_TYPE_Material2021_Picture, "material/");
		storePathFolderMap.put(WxAttFile.SOURCE_TYPE_TRAINING, "training/");
		storePathFolderMap.put(WxAttFile.SourceType_CREDIT_APP, "credit_app/");
		storePathFolderMap.put(WxAttFile.SOURCETYPE_SELL_IN, "sellIn/");
		storePathFolderMap.put(WxAttFile.SOURCE_TYPE_PAYMENT_NOTICE_EXCEL, "payment_notice_import/");

		thumbnailWidthMap.put(WxAttFile.SOURCE_TYPE_TRAINING, 200);
		thumbnailWidthMap.put(WxAttFile.SourceType_PublishPicture, 200);

		sharedFileSourceTypes.add(WxAttFile.SourceType_SharedFile);
		sharedFileSourceTypes.add(WxAttFile.SourceType_B2B);
		sharedFileSourceTypes.add(WxAttFile.SourceType_PRODUCT_ICON);
		sharedFileSourceTypes.add(WxAttFile.SourceType_PRODUCT_HD_ICON);
		sharedFileSourceTypes.add(WxAttFile.SourceType_ProductIcon);
		sharedFileSourceTypes.add(WxAttFile.SOURCETYPE_O2O_PICTURE);
		sharedFileSourceTypes.add(WxAttFile.SourceType_Check);
		sharedFileSourceTypes.add(WxAttFile.SourceType_Wrokshop);
		sharedFileSourceTypes.add(WxAttFile.SOURCE_TYPE_Material2021_Picture);

	}

	FileManager() {
		fileUploadPath = (String) Constants.getSystemPropertyByCodeType(Constants.file_upload_path);
		folderGrainSize = (String) Constants.getSystemPropertyByCodeType(Constants.folder_grain_size);
		remoteUser = (String) Constants.getSystemPropertyByCodeType(Constants.remote_user);
		remoteUserPassword = (String) Constants.getSystemPropertyByCodeType(Constants.remote_user_password);
		remoteFileUploadPath1 = "smb://" + remoteUser + ":" + remoteUserPassword + "@**************/smbusr/";
		remoteFileUploadPath = "smb://" + remoteUser + ":" + remoteUserPassword + "@"
				+ (String) Constants.getSystemPropertyByCodeType(Constants.remote_url);
	}
	
	@ResponseBody
    @ApiOperation(value="附件列表页面分页查询接口",  httpMethod="POST", notes="附件列表页面分页查询接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/attfile/data.do")
	public ResponseMap queryForPage(@ApiParam(name="params", value="查询对象", required=true) AttFileParams params, HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("queryForPage: " + JsonUtil.writeValue(params));

		try {
			resultMap.put(Constants.RESULT_LST_KEY, attFileMapper.queryForPage(params));
			resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			log.info("queryForPage success." );
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.file.web.FileManager.queryForPage", JsonUtil.writeValue(params));
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/uploadAttchmentFile.do", method = RequestMethod.POST, produces = "text/html;charset=UTF-8")
	public Object uploadAttchmentFile(@RequestParam("sourceType") String sourceType,
			@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request) throws Exception {

		Map<String, Object> doUploadFile = handleUploadRequest(sourceType, myfiles, request);
		return JsonUtil.toJSONString(doUploadFile);
	}

	@ResponseBody
	@RequestMapping(value = "/uploadForAppAttchmentFile.do", method = RequestMethod.POST)
	public Map<String, Object> uploadForAppAttchmentFile(@RequestParam("sourceType") String sourceType,
			@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request) throws Exception {
		Map<String, Object> resMap = handleUploadRequest(sourceType, myfiles, request);
		return resMap;
	}

	@ResponseBody
	@RequestMapping(value = "/uploadSingleAttchmentFile.do", method = RequestMethod.POST)
	public Map<String, Object> uploadAttchmentFileForSingle(@RequestParam("sourceId") String sourceId,
			@RequestParam("sourceType") String sourceType, @RequestParam("myFile") MultipartFile myFile,
			HttpServletRequest request) throws Exception {

		Map<String, Object> resMap = new HashMap<String, Object>();
		WxAttFile wxAttFile = fileManagerService.searchAttBySourceIdAndSourceType(sourceId, sourceType);

		if (wxAttFile == null) {
			request.setAttribute("sourceId", sourceId);
			MultipartFile[] myfiles = new MultipartFile[] { myFile };
			resMap = uploadForAppAttchmentFile(sourceType, myfiles, request);

			if (null == resMap || (!resMap.containsKey("code")) || (!"success".equals(resMap.get("code")))) {
				return resMap;
			}

			if (resMap.containsKey("attachmentFileList") && null != resMap.get("attachmentFileList")) {
				List<WxAttFile> attachmentFileList = (List<WxAttFile>) resMap.get("attachmentFileList");
				resMap.put("url", "/?sourceType=" + sourceType + "&attId="
						+ attachmentFileList.get(0).getAttId());
			}
		} else {
			resMap = updateAttchmentFile(String.valueOf(wxAttFile.getAttId()), myFile, request);

			if (null == resMap || (!resMap.containsKey("code")) || (!"success".equals(resMap.get("code")))) {
				return resMap;
			}

			resMap.put("url", "/downloadAttachmentFile.do?sourceType=" + sourceType + "&attId=" + wxAttFile.getAttId());
		}
		return resMap;
	}

	@ResponseBody
	@RequestMapping(value = "/uploadForAppInStockProductRecordAttchmentFile.do", method = RequestMethod.POST)
	public Map<String, Object> uploadForAppInStockProductRecordAttchmentFile(
			@RequestParam("partnerId") String partnerId, @RequestParam("workshopId") String workshopId,
			@RequestParam("logisticsNumber") String logisticsNumber, @RequestParam("bugComment") String bugComment,
			@RequestParam("uploadTime") Date uploadTime, @RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) {

		/*InStockProductBugRecordVo bugRecordVo = inStockProductBugRecordService.insertInStockProductBugRecord(partnerId,
				workshopId, logisticsNumber, bugComment, uploadTime);*/
		Map<String, Object> bugRecordVoResult = inStockProductBugRecordService.insertInStockProductBugRecord(partnerId,
				workshopId, logisticsNumber, bugComment, uploadTime);
		if (bugRecordVoResult.get("code").toString().equals("error")) {
			return bugRecordVoResult;
		}
		InStockProductBugRecordVo bugRecordVo = (InStockProductBugRecordVo) bugRecordVoResult.get("data");
		String orgName = bugRecordVo.getPartnerName();
		String workshopName = bugRecordVo.getWorkshopName();
		String sourceType = WxAttFile.SourceType_LogisticPicture;
		Long sourceId = bugRecordVo.getId();

		// 构造附件存储结构: 合伙人组织名/门店名/
		String subStorePath = "";
		if (StringUtils.isNotEmpty(orgName)) {
			subStorePath = subStorePath + "/" + orgName;
			if (!StringUtils.isNotEmpty(workshopName)) {
				subStorePath = subStorePath + "/" + workshopName;
			}
		}

		Map<String, Object> resMap = doUploadFile(myfiles, subStorePath + "/stockinbugpic", sourceId, sourceType);
		return resMap;
	}

	public Map<String, Object> handleUploadRequest(String sourceType, MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {

		String sourceIdStr = request.getParameter("sourceId");
		if (StringUtils.isEmpty(sourceIdStr)) {
			sourceIdStr = (String) request.getAttribute("sourceId");
		}
		Long sourceId = null;
		if (StringUtils.isNotEmpty(sourceIdStr)) {
			sourceId = Long.valueOf(sourceIdStr);
		}

		String uniqueName = request.getParameter("uniqueName");
		if (!StringUtils.isEmpty(uniqueName) && Boolean.parseBoolean(uniqueName)) {
			// 判断文件是否重复上传
			if (null != myfiles && myfiles.length > 0) {
				if (1 == myfiles.length) {
					MultipartFile myfile = myfiles[0];
					String fileName = myfile.getOriginalFilename();
					Boolean sameName = fileManagerService.isSameName(sourceId, sourceType, fileName);
					if (sameName) {
						Map<String, Object> resMap = new HashMap<String, Object>();
						resMap.put("code", "error");
						resMap.put("codeMsg", "文件重复上传：" + fileName);
						return resMap;
					}
				} else {
					List<MultipartFile> newList = new ArrayList<MultipartFile>();
					for (int i = 0; i < myfiles.length; i++) {
						MultipartFile myfile = myfiles[i];
						String fileName = myfile.getOriginalFilename();
						Boolean sameName = fileManagerService.isSameName(sourceId, sourceType, fileName);
						if (!sameName) {
							newList.add(myfile);
						}
					}
					myfiles = newList.toArray(new MultipartFile[newList.size()]);
				}

			}
		}

		// 针对workshopname可以考虑识别是任务类型，如：xx门店_扫店
		String workshopName = request.getParameter("workshopName");
		String excuteUserId = request.getParameter("excuteUserId");
		String containOrgName = request.getParameter("pathContainOrgName");
		String sourceTypeName = request.getParameter("sourceTypeName");
		String orgName = ContextUtil.getCurUser().getOrgName();

		// 如果有执行人, 则取执行人的组织名(合伙人组织名)
		if (StringUtils.isNotEmpty(excuteUserId)) {
			Map<String, Object> map = wxUserService.getWxTUserInfoByUseridNew(excuteUserId);
			if (null != map && null != map.get("resultData")) {
				WxTUser user = (WxTUser) map.get("resultData");
				orgName = user.getOrgName();
			}
		}

		// 构造附件存储结构: 合伙人组织名/门店名/
		String subStorePath = "";
		/*if (StringUtils.isNotEmpty(sourceTypeName)){
			subStorePath = subStorePath + "/" + sourceTypeName;
		}*/
		if (!"false".equals(containOrgName) && StringUtils.isNotEmpty(orgName)) {
			subStorePath = subStorePath + "/" + orgName;
			if (!StringUtils.isEmpty(workshopName)) {
				String[] workshopNames = workshopName.split(";");
				if (workshopNames.length == 1) {
					// 只有一个门店的时候，就上传到指定店铺名目录下, 否则就在合伙人根目录
					subStorePath = subStorePath + "/" + workshopName;
				}
			}
		}
		Map<String, Object> res = doUploadFile(myfiles, subStorePath, sourceId, sourceType);
		return res;
	}

	/**
	 * 实现文件上传到指定文件夹, 以及更新相关数据表, 若包含空文件对象, 则全部失败
	 * @param myfiles
	 * @param subStorePath
	 * @param sourceId
	 * @param sourceType
	 * @return result Map
	 */
	private Map<String, Object> doUploadFile(MultipartFile[] myfiles, String subStorePath, Long sourceId,
			String sourceType) {
		Map<String, Object> resMap = new HashMap<String, Object>();
		List<WxAttFile> attFileList = new ArrayList<WxAttFile>();
		try {
			if (null != myfiles && myfiles.length > 0) {
				for (MultipartFile myfile : myfiles) {
					if (myfile.isEmpty()) {
						log.warn("文件未上传");
						resMap.put("code", "error");
						resMap.put("codeMsg", "文件未上传");
						return resMap;
					}

					// 准备参数
					String fileName = myfile.getOriginalFilename();
					String fileType = myfile.getContentType();
					String storageName = StringUtils.getSeqNextval() + "." + StringUtils.getFileSuffixName(fileName);
					Long fileSize = myfile.getSize();
					String name = myfile.getName();
					String storePath = FileUtil.getFolderName(Integer.parseInt(folderGrainSize)) + subStorePath;
					String realPath = getFileRealPath(sourceType, storePath);
					log.info("========================================");
					log.info("上传文件长度: " + fileSize);
					log.info("上传文件类型: " + fileType);
					log.info("上传文件名称: " + name);
					log.info("上传文件原名: " + fileName);
					log.info("上传文件位置: " + realPath);
					log.info("========================================");

					// add by bo.liu 1116 start
					if (null == fileSize || fileSize == 0L) {
						log.warn("文件长度异常");
						resMap.put("code", "error");
						resMap.put("codeMsg", "文件长度异常,为null或为0");
						return resMap;
					}

					// 上传文件
					if (null == fileType || fileType.equals("null") || fileType.isEmpty()) {
						fileType = defaultfileType;
					}
					// end
					FileUtil.storeFile(realPath, storageName, myfile.getInputStream(), sourceType, fileType);

					// 更新附件信息表
					WxAttFile wxAttFile = fileManagerService.addAttFile(sourceId, fileName, storageName, fileType,
							sourceType, storePath, fileSize, ContextUtil.getCurUserId(), "1");
					//wxAttFile.setCreateTime(null);
					wxAttFile.setXgSj(null);
					attFileList.add(wxAttFile);
				}
			}

			if (!attFileList.isEmpty()) {
				resMap.put("code", "success");
				resMap.put("attachmentFileList", attFileList);
				resMap.put("url", "/downloadAttachmentFile.do");
			} else {
				resMap.put("code", "noAttachmentFile");
			}
		} catch (Exception e) {
			log.error("upload file exception", e);
			resMap.put("code", "error");
			resMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
		}

		return resMap;
	}

	public static String getFileRealPath(String sourceType, String storePath) {
		String realPath;
		if (storePathFolderMap.containsKey(sourceType)) {
			storePath = storePathFolderMap.get(sourceType) + storePath;
		}
		if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
			realPath = fileUploadPath + storePath;
		} else {
			realPath = remoteFileUploadPath + storePath;
		}
		return realPath;
	}

	public static void deleteAttFile(WxAttFile wxAttFile, WxAttFileMapper wxAttFileMapper) throws IOException {
		String sourceType = wxAttFile.getSourceType();
		wxAttFileMapper.deleteByPrimaryKey(wxAttFile.getAttId());
		String fileSep = System.getProperty("file.separator");
		String realPath;
		if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
			realPath = FileManager.getFileRealPath(wxAttFile.getSourceType(), wxAttFile.getStorePath()) + fileSep;
			FileUtil.deleteFileNew(realPath + wxAttFile.getStorageName(), realPath);
		} else {
			realPath = FileManager.getFileRealPath(wxAttFile.getSourceType(), wxAttFile.getStorePath());
			FileUtil.deleteRemoteFile(realPath + fileSep + wxAttFile.getStorageName());
		}
	}

	/**
	 * 提供外网图片下载, 不需要登录
	 * @param imageId 附件ID
	 * @param sourceType 类型
	 * @param request HttpServletRequest
	 * @param response HttpServletResponse
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping(value = "/publicImage/{imageId}/{sourceType}/download.do", method = { RequestMethod.POST,
			RequestMethod.GET })
	public void anonImageDownload(@PathVariable("imageId") String imageId,
			@PathVariable("sourceType") String sourceType, HttpServletRequest request, HttpServletResponse response)
			throws IOException {
		WxAttFile att = fileManagerService.searchAttByKey(imageId);
		String filePath = getFileRealPath(sourceType, att.getStorePath()) + System.getProperty("file.separator")
				+ att.getStorageName();
		FileUtil.download(response, sourceType, filePath, att.getFileName());
	}

	@RequestMapping(value = "/anon/downloadsharedfile.do")
	public void downloadSharedFile(@RequestParam("attId") String attId, HttpServletRequest request,
			HttpServletResponse response) throws IOException {
		WxAttFile att = fileManagerService.searchAttByKey(attId);
		if (!sharedFileSourceTypes.contains(att.getSourceType())) {
			throw new RuntimeException("无文件访问权限");
		}
		String filePath = getFileRealPath(att.getSourceType(), att.getStorePath())
				+ System.getProperty("file.separator") + att.getStorageName();
		FileUtil.download(response, att.getSourceType(), filePath, att.getFileName());
	}

	@RequestMapping(value = "/downloadAttachmentFile.do")
	public void downloadAttachmentFile(@RequestParam("attId") String attId,
			@RequestParam(value = "sourceType", required = false) String sourceType, 
			@RequestParam(value = "inline", required = false) Boolean inline, 
			HttpServletRequest request, HttpServletResponse response) {
		try {
			if (attId != null && (!StringUtils.isNull(attId)) && (StringUtils.isNumeric(attId))) {
				WxAttFile att = fileManagerService.searchAttByKey(attId);
				String fileSep = System.getProperty("file.separator");
				String realPath = "";
				if (att != null) {
//					if (StringUtils.isBlank(sourceType)) {
						sourceType = att.getSourceType();
//					}
					realPath = getFileRealPath(sourceType, att.getStorePath());
					// if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
					// realPath = fileUploadPath + getFileRealPath(sourceType, att.getStorePath());
					// } else {
					// realPath = remoteFileUploadPath + getFileRealPath(sourceType,
					// att.getStorePath());
					// }
					String storageName = "", filePath = "", fileName = "";
					storageName = att.getStorageName();
					fileName = att.getFileName();
					filePath = realPath + fileSep + storageName;
					FileUtil.download(response, sourceType, att.getFileType(), Boolean.TRUE.equals(inline), filePath, fileName);
				}
			} else {
				log.info("处理下载文件时发生异常,文件ID为空：" + "attId:" + attId + "sourceType:" + sourceType);
			}
		} catch (IOException e) {
			log.error("处理下载文件时发生异常：" + "attId:" + attId + "sourceType:" + sourceType, e);
		} catch (Exception e) {
			log.error("处理下载文件时发生异常：" + "attId:" + attId + "sourceType:" + sourceType, e);
		}
	}

	@RequestMapping(value = "/downloadThumbnailFile.do")
	public void downloadThumbnailFile(@RequestParam("attId") String attId,
			@RequestParam("width") int width, HttpServletRequest request,
			HttpServletResponse response) {
		try {
			if (attId != null && (!StringUtils.isNull(attId)) && (StringUtils.isNumeric(attId))) {
				WxAttFile att = fileManagerService.searchAttByKey(attId);
				String fileSep = System.getProperty("file.separator");
				String realPath = "";
				if (att != null) {
					String sourceType = att.getSourceType();
					realPath = getFileRealPath(sourceType, att.getStorePath());
					// if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
					// realPath = fileUploadPath + getFileRealPath(sourceType, att.getStorePath());
					// } else {
					// realPath = remoteFileUploadPath + getFileRealPath(sourceType,
					// att.getStorePath());
					// }
					String storageName = "", filePath = "", fileName = "";
					storageName = att.getStorageName();
					fileName = att.getFileName();
					filePath = realPath + fileSep + storageName;
					FileUtil.downloadByThumbnail(response, sourceType, filePath, fileName, width);
				}
			} else {
				log.info("处理下载文件时发生异常,文件ID为空：" + "attId:" + attId);
			}
		} catch (IOException e) {
			log.error("处理下载文件时发生异常：" + "attId:" + attId, e);
		} catch (Exception e) {
			log.error("处理下载文件时发生异常：" + "attId:" + attId, e);
		}
	}

	/**
	 * 下载缩略图
	 * @param attId
	 * @param width
	 * @param sourceType
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/downloadAttachmentFileThumbnail.do")
	public void downloadAttachmentFileThumbnail(@RequestParam("attId") String attId,
			@RequestParam("width") String width,
			@RequestParam(value = "sourceType", required = false) String sourceType, HttpServletRequest request,
			HttpServletResponse response) throws IOException {
		WxAttFile att = fileManagerService.searchAttByKey(attId);
		if (StringUtils.isBlank(sourceType)) {
			sourceType = att.getSourceType();
		}
		String fileSep = System.getProperty("file.separator");
		String realPath = getFileRealPath(sourceType, att.getStorePath());
		// if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
		// realPath = fileUploadPath + att.getStorePath();
		// } else {
		// realPath = remoteFileUploadPath + att.getStorePath();
		// }
		String storageName = "", filePath = "", fileName = "";
		storageName = att.getStorageName();
		fileName = ThumbnailUtil.THUMBNAIL_PREFIX + width + "_" + storageName;
		filePath = realPath + fileSep + fileName;
		File file = new File(filePath);
		if (file.exists()) {
			FileUtil.download(response, sourceType, filePath, att.getFileName());
		}
	}

	@RequestMapping(value = "/downloadQrCode.do")
	public String downloadQrCode(@RequestParam("batchId") Long batchId, @RequestParam("sku") String sku,
			HttpServletRequest request, HttpServletResponse response) {
		log.info("batchId: {}, sku: {}", batchId, sku);
		if (batchId == null || StringUtils.isNull(sku)) {
			request.setAttribute("errorMsg", "下载失败：未传入下载批次号");
			return "forward:/common/jsp/downloadError.jsp";
		}
		try {
			qrCodeService.downLoadQrcode(batchId, sku, null, null, request, response);
		} catch (Exception e) {
			log.error("下载失败",e);
			request.setAttribute("errorMsg", "下载失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

	@RequestMapping(value = "/downloadQrCodeNew.do")
	public String downloadQrCodeNew(@RequestParam("batchId") Long batchId,
			@RequestParam("moduleName") String moduleName, 
			@RequestParam(value="sku",required=false) String sku, HttpServletRequest request, HttpServletResponse response) {
		log.info("batchId: {}, moduleName: {}", batchId, moduleName);
		if (batchId == null) {
			request.setAttribute("errorMsg", "下载失败：未传入下载批次号");
			return "forward:/common/jsp/downloadError.jsp";
		}
		try {
			qrCodeService.downLoadQrcodeNew(batchId, sku, moduleName, null, null, request, response);
		} catch (Exception e) {
			log.error("下载失败",e);
			request.setAttribute("errorMsg", "下载失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@RequestMapping(value = "/downloadDidiQrCode.do")
	public String downloadDidiQrCode(@RequestParam("batchId") Long batchId, @RequestParam("sku") String sku,
			HttpServletRequest request, HttpServletResponse response) {
		log.info("batchId: {}, sku: {}", batchId, sku);
		if (batchId == null || StringUtils.isNull(sku)) {
			request.setAttribute("errorMsg", "下载失败：未传入下载批次号");
			return "forward:/common/jsp/downloadError.jsp";
		}
		try {
			qrCodeService.downLoadDidiQrcode(batchId, sku, null, null, request, response);
		} catch (Exception e) {
			log.error("下载失败",e);
			request.setAttribute("errorMsg", "下载失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

	@RequestMapping(value = "/downloadCapCode.do")
	public String downloadCapCode(@RequestParam("batchId") Long batchId, @RequestParam("sku") String sku,
			@RequestParam("productName") String productName, HttpServletRequest request, HttpServletResponse response) {
		log.info("batchId: {}, sku: {}", batchId, sku);
		if (batchId == null) {
			request.setAttribute("errorMsg", "下载失败：未传入下载批次号");
			return "forward:/common/jsp/downloadError.jsp";
		}
		try {
			capCodeService.downLoadCapcode(batchId, sku, productName, null, null, request, response);
		} catch (Exception e) {
			log.error("下载失败",e);
			request.setAttribute("errorMsg", "下载失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

	@RequestMapping(value = "/downloadQrCodeBatch.do")
	public String downloadQrCodeBatch(@RequestParam("batchIds") Long[] batchIds, @RequestParam("skus") String skus[],
			HttpServletRequest request, HttpServletResponse response) {
		log.info("batchIds: {}, skus: {}", batchIds, skus);
		if (batchIds == null || skus == null || batchIds.length != skus.length) {
			request.setAttribute("errorMsg", "批量下载失败：未传入下载批次号");
			return "forward:/common/jsp/downloadError.jsp";
		}
		Set<String> existFileNames = new HashSet<String>();
		ZipOutputStream zipOutputStream = null;
		try {
			CommonUtil.setExportResponseHeader(request, response, "批量下载QrCode", "zip");
			zipOutputStream = new ZipOutputStream(response.getOutputStream());
			zipOutputStream.setEncoding("GBK");
			int i = 0;
			for (Long batchId : batchIds) {
				String sku = skus[i++];
				qrCodeService.downLoadQrcode(batchId, sku, zipOutputStream, existFileNames, request, response);
			}
		} catch (Exception e) {
			log.error("下载失败",e);
			request.setAttribute("errorMsg", "批量下载失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		} finally {
			if (zipOutputStream != null) {
				try {
					zipOutputStream.close();
				} catch (IOException e) {
					log.warn(e.getMessage(), e);
				}
			}
		}
		return null;
	}

	@RequestMapping(value = "/downloadProductLogo.do")
	public void downloadProductLogo(@RequestParam("attId") String attId, @RequestParam("sourceType") String sourceType,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			if (attId != null && (!StringUtils.isNull(attId))) {
				if (sourceType == null || !WxAttFile.SourceType_ProductIcon.equals(sourceType.trim())) {
					return;
				}
				WxAttFile att = fileManagerService.searchAttByKey(attId);
				if (att.getSourceType() == null || !WxAttFile.SourceType_ProductIcon.equals(att.getSourceType())) {
					return;
				}
				String fileSep = System.getProperty("file.separator");
				String realPath = "";
				if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
					realPath = fileUploadPath + att.getStorePath();
				} else {
					realPath = remoteFileUploadPath + att.getStorePath();
				}
				String storageName = "", filePath = "", fileName = "";
				storageName = att.getStorageName();
				fileName = att.getFileName();
				filePath = realPath + fileSep + storageName;
				FileUtil.download(response, sourceType, filePath, fileName);
			}
		} catch (IOException e) {
			log.error("处理下载文件时发生异常：" + "attId:" + attId + ", sourceType:" + sourceType, e);
		}
	}

	@RequestMapping(value = "/download.do")
	public void downloadByUUID(@RequestParam("uuid") String attId, HttpServletRequest request,
			HttpServletResponse response) throws IOException {
		String sourceType = "1";
		WxAttFile att = fileManagerService.searchAttByUuid(attId);
		if (att == null) {
			return;
		}
		String fileSep = System.getProperty("file.separator");
		String realPath = "";
		if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
			realPath = fileUploadPath + att.getStorePath();
		} else {
			realPath = remoteFileUploadPath + att.getStorePath();
		}
		String storageName = "", filePath = "", fileName = "";
		storageName = att.getStorageName();
		fileName = att.getFileName();
		filePath = realPath + fileSep + storageName;
		FileUtil.download(response, sourceType, att.getFileType(), filePath, fileName);
	}

	@ResponseBody
	@RequestMapping(value = "/updateAttchmentFile.do", method = RequestMethod.POST)
	public Map<String, Object> updateAttchmentFile(@RequestParam("attId") String attId,
			@RequestParam("myFile") MultipartFile myFile, HttpServletRequest request) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		Long curUserID = ContextUtil.getCurUserId();
		WxAttFile wxAttFile = fileManagerService.searchAttByKey(attId);
		if (wxAttFile != null) {
			String sourceType = wxAttFile.getSourceType();
			String fileSep = System.getProperty("file.separator");
			String fileName = "";
			String storageName = "";
			String fileType = "";
			String storePath = "";
			Long fileSize = null;
			if (myFile.isEmpty()) {
				resMap.put("code", "file did not upload");
				return resMap;
			} else {
				fileName = myFile.getOriginalFilename();
				fileType = myFile.getContentType();
				storageName = StringUtils.getSeqNextval();
				String fileSuffix = StringUtils.getFileSuffixName(fileName);
				storePath = FileUtil.getFolderName(Integer.parseInt(folderGrainSize));
				storageName = storageName + "." + fileSuffix;
				String realPath = getFileRealPath(sourceType, storePath);
				// add by bo.liu 1120 start
				System.out.println("updateAttchmentFile fileType:" + fileType);
				if (null == fileType || fileType.equals("null") || fileType.isEmpty()) {
					fileType = defaultfileType;
				}
				// end
				fileSize = myFile.getSize();
				FileUtil.storeFile(realPath, storageName, myFile.getInputStream(), sourceType, fileType);
			}
			String oldRealPath = getFileRealPath(sourceType, wxAttFile.getStorePath());
			String oldStorageName = "";
			String oldFilePath = "";
			oldStorageName = wxAttFile.getStorageName();
			oldFilePath = oldRealPath + fileSep + oldStorageName;
			if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
				FileUtil.deleteFileNew(oldFilePath, oldRealPath + fileSep);
			} else {
				FileUtil.deleteRemoteFile(oldFilePath);
			}
			log.info("start upload file ******************");
			log.info("fileName =" + fileName);
			log.info("storageName =" + storageName);
			log.info("fileType =" + fileType);
			log.info("storePath =" + storePath);
			log.info("fileSize =" + fileSize);
			log.info("curUserID =" + curUserID);

			wxAttFile = fileManagerService.updateAttFileByattId(wxAttFile, fileName, storageName, fileType, storePath,
					fileSize, curUserID);
			resMap.put("code", "success");
			resMap.put("attachmentFile", wxAttFile);
		} else {
			resMap.put("code", "cannot find the attachment file");
		}
		return resMap;
	}

	@ResponseBody
	@RequestMapping(value = "/uploadServiceBillForDadiWorkshop.do", method = RequestMethod.POST)
	public Object uploadServiceBillForDadiWorkshop(@RequestParam("mechanicCode") String mechanicCode,
			@RequestParam("carNo") String carNo, @RequestParam("billPhoto") MultipartFile billPhoto,
			HttpServletRequest request) {

		Map<String, Object> returnMap = new HashMap<String, Object>();
		returnMap.put("id", 1);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("code", "success");
		try {
			if (StringUtils.isNull(mechanicCode) || StringUtils.isNull(carNo) || billPhoto == null) {
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			}
			// 1, save business data
			Long serviceBillId = ddServiceBillBizService.saveServiceBillInfo(URLDecoder.decode(mechanicCode, "UTF-8"),
					URLDecoder.decode(carNo, "UTF-8"));
			// 2, save photo
			request.setAttribute("sourceId", String.valueOf(serviceBillId));
			MultipartFile[] myfiles = new MultipartFile[] { billPhoto };
			resultMap = uploadForAppAttchmentFile(WxAttFile.SourceType_DDServiceBill, myfiles, request);
			if (!((String) resultMap.get("code")).equals("success")) {
				resultMap.put("code", "error");
				// return resultMap;
			}
		} catch (WxPltException e) {
			log.error("uploadServiceBillForDadiWorkshop exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("uploadServiceBillForDadiWorkshop unexpected exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		returnMap.put("result", resultMap);
		return returnMap;
	}

	/**
	 * 大地保险新保订单导出功能
	 * <AUTHOR> 2016-9-24 下午3:54:33
	 * @param ddxbOrderIds
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/downloadDDXBOrderProductExcel.do")
	public Map<String, Object> exportExcelDataForDDXBOrderProduct(@RequestParam("ddxbOrderIds") String ddxbOrderIds,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 导出全部
		if (null == ddxbOrderIds || ddxbOrderIds.equals("null")) {
			log.info("1-----ljc----exportExcelDataForDDXBOrderProduct:EXPORT_ALL");
			resultMap = orderDDBXServiceImpl.exportDDXBOrder(null, request, response, OrderDDBXServiceImpl.EXPORT_ALL,
					ExportExcel.EXPORT_TYPE);

		} else {// 批量导出
			log.info("2-----ljc----exportExcelDataForDDXBOrderProduct:EXPORT_BATCH");
			resultMap = orderDDBXServiceImpl.exportDDXBOrder(ddxbOrderIds, request, response,
					OrderDDBXServiceImpl.EXPORT_BATCH, ExportExcel.EXPORT_TYPE);
		}
		String ResultCode = (String) resultMap.get(OrderDDBXServiceImpl.RESULT_CODE_KEY);
		if (ResultCode.equals(OrderDDBXServiceImpl.ERROR_CODE)) {
			String errorMsg = (String) resultMap.get(OrderDDBXServiceImpl.RESULT_ERROR_MSG_KEY);
			request.setAttribute("errorMsg", errorMsg);
		}
		return resultMap;
	}

	/**
	 * 预处理EXCEL表格文件
	 * @param file EXCEL表格文件
	 * @return Workbook
	 * @throws IOException 如果发生IO异常则抛出待上层逻辑处理
	 */
	public static Workbook preProcessingWb(MultipartFile file) throws IOException {
		InputStream is = file.getInputStream();
		String fileName = file.getOriginalFilename();
		Workbook wb = null;
		try {
			if (fileName.toLowerCase().endsWith(SUFFIX_XLS)) {
				wb = new HSSFWorkbook(new POIFSFileSystem(is));
			} else if (fileName.toLowerCase().endsWith(SUFFIX_XLSX) || fileName.toLowerCase().endsWith(SUFFIX_XLSM)) {
				wb = new XSSFWorkbook(is);
			}
		} catch (IOException e) {
			log.error("生成workbook失败【" + fileName + "】, 异常消息:" + e.getMessage(), e);
			throw e;
		}
		return wb;
	}

	@ResponseBody
	@RequestMapping(value = "/getDataProgressStatus.do")
	public Map<String, Object> getDataProgressStatus(
			@RequestParam(value = "progressKey", required = false) String progressKey, HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			if (StringUtils.isBlank(progressKey)) {
				throw new WxPltException("进度状态键值为空");
			}
			resultMap.put("progressStatus", request.getSession().getAttribute(progressKey));
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY,
					MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/importOrgBat.do", method = RequestMethod.POST)
	public Object importOrgBat(@RequestParam("myfiles") MultipartFile[] myfiles,
			@RequestParam("autoCity") boolean autoCity, HttpServletRequest request) throws Exception {
		return orgTreeService.importOrgBat(preProcessingWb(myfiles[0]), autoCity);
	}

//	@ResponseBody
//	@RequestMapping(value = "/importWorkshopBat.do", method = RequestMethod.POST)
//	public Object importWorkshopBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
//			throws Exception {
//		return workshopServiceImpl.importDataForWorkShopBatchContainRelation(preProcessingWb(myfiles[0]), true);
//	}

	@ResponseBody
	@RequestMapping(value = "/importUserBat.do", method = RequestMethod.POST)
	public Object importUserBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {

		return wxUserService.importUserBat(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importCheckBat.do", method = RequestMethod.POST)
	public Object importCheckBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return taskCheckService.importTaskCheckBat(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importWholeVehicleOilBat.do", method = RequestMethod.POST)
	public Object importWholeVehicleOilBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return importDataServiceImpl.importWholeVehicleOilBat(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsFilterBat.do", method = RequestMethod.POST)
	public Object importZsFilterBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsFitlerDetailService.importVehicleFitlerDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsDiscBat.do", method = RequestMethod.POST)
	public Object importZsDiscBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsDiscDetailService.importVehicleDiscDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsOilBat.do", method = RequestMethod.POST)
	public Object importZsOilBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		MultipartFile file = myfiles[0]; 
	    CommonsMultipartFile cf= (CommonsMultipartFile)file; 
	    DiskFileItem fi = (DiskFileItem)cf.getFileItem(); 

	    File f = fi.getStoreLocation();
	        
	        
		return zsOilDetailService.importVehicleOilDetailUpdate(f);
	}

	@ResponseBody
	@RequestMapping(value = "/importZsBatteryBat.do", method = RequestMethod.POST)
	public Object importZsBatteryBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsBatteryService.importVehicleBatteryDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsBafBat.do", method = RequestMethod.POST)
	public Object importZsBafBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsBafService.importVehicleBafDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsAntiFreezeFluidBat.do", method = RequestMethod.POST)
	public Object importZsAntiFreezeFluidBat(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {
		return zsAntiFreezeFluidService.importAntifreezeFluid(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsBrakeFluidBat.do", method = RequestMethod.POST)
	public Object importZsBrakeFluidBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsBrakeFluidService.importBrakeFluid(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsTyreBat.do", method = RequestMethod.POST)
	public Object importZsTyreBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsTyreService.importTyre(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsVehcileTyreBat.do", method = RequestMethod.POST)
	public Object importVehicleTyreBat(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsVehicleTyreDetailService.importVehicleTyreDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsVehcileRainwiperBat.do", method = RequestMethod.POST)
	public Object importZsVehcileRainwiperBat(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {
		return zsVehicleRainwiperDetailService.importVehicleRainwiperDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importZsVehcileSparkplugsBat.do", method = RequestMethod.POST)
	public Object importZsVehcileSparkplugsBat(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {
		return zsVehicleSparkplugsDetailService.importVehicleSparkplugsDetail(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importVehicleTypeLevelId.do", method = RequestMethod.POST)
	public Object importVehicleTypeLevelId(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zSVehicleTypeService.importVehicleTypeLevelId(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importMaintenanceRules.do", method = RequestMethod.POST)
	public Object importMaintenanceRules(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		return zsMaintenanceRuleService.importMaintenanceRules(preProcessingWb(myfiles[0]));
	}

	@ResponseBody
	@RequestMapping(value = "/importWholeVehicleOilBatPlus.do", method = RequestMethod.POST)
	public Object importWholeVehicleOilBatPlus(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {
		Map<String, Object> result = importDataServiceImpl.importWholeVehicleOilBatPlus(preProcessingWb(myfiles[0]));
		WxTImportLog importLog = new WxTImportLog();
		importLog.setFileName(myfiles[0].getOriginalFilename());
		importLog.setImportTime(new Date());
		importLog.setImportDataType("vehicleoil(全车油率)");
		importLog.setCreateTime(new Date());
		importLog.setImportResult((String) result.get("code"));
		importLog.setImportDetailMessage((String) result.get("codeMsg"));
		importLogMapper.insertSelective(importLog);
		return result;
	}

	// 优化导入数据-批量插入
	@ResponseBody
	@RequestMapping(value = "/importBatchData.do", method = RequestMethod.POST)
	public Object importBatchData(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		Map<String, Object> result = importDataServiceImpl.importBatchData(preProcessingWb(myfiles[0]));
		WxTImportLog importLog = new WxTImportLog();
		importLog.setFileName(myfiles[0].getOriginalFilename());
		importLog.setImportTime(new Date());
		importLog.setImportDataType("vehicletype(车型)");
		importLog.setCreateTime(new Date());
		importLog.setImportResult((String) result.get("code"));
		importLog.setImportDetailMessage((String) result.get("codeMsg"));
		importLogMapper.insertSelective(importLog);
		return result;
	}

//	/* ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑导入数据服务↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑*/
//
//	@ResponseBody
//	@RequestMapping(value = "/importOrderBatchData.do", method = RequestMethod.POST)
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Object importBatchOrderDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
//			@RequestParam("operType") String operType, HttpServletRequest request, HttpServletResponse response)
//			throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		if (null == operType || operType.trim().equals("")) {
//			resultMap.put("code", "error");
//			resultMap.put("codeMsg", "导入订单类型异常!!!");
//			return resultMap;
//		}
//		try {
//			// 导入模板分开
//			if (operType.equals(ImportDataUtil.OPER_INERT_ORDER_TYPE)) {
//				// 采购订单
//
//				log.info("msg:ljc 0927=======================================新保订单导入开始");
//				resultMap = importDataServiceImpl.importBatchDDXBOrderDataFromExcel(preProcessingWb(myfiles[0]));
//				// 发送邮件
//				String respCode = (String) resultMap.get("code");
//				if (!respCode.equals("syserror")) {
//					resultMap = orderDDBXServiceImpl.exportDDXBOrderNewForExcel(null, request, response,
//							OrderDDBXServiceImpl.EXPORT_ALL, ExportExcel.EMAIL_TYPE,
//							OrderCommonVo.ORDER_SOURCE_DDBX_VALUE, OrderVo.SETTLEMENTMODELTYPE1);
//				}
//
//				return resultMap;
//			} else if (operType.equals(ImportDataUtil.OPER_UPDATE_ORDER_TYPE)) {// 发货订单
//
//				log.info("msg:ljc 0927=======================================服务订单导入开始");
//				return importDataServiceImpl.importBatchDDFWOrderDataFromExcel(preProcessingWb(myfiles[0]));
//			} else if (operType.equals(ImportDataUtil.OPER_JSBFW_ORDER_TYPE)) {// 技师帮服务订单
//
//				log.info("msg:ljc 1028=======================================技师帮服务订单");
//				return importDataServiceImpl.importBatchJSBFWOrderDataFromExcel(preProcessingWb(myfiles[0]));
//			} else {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "导入订单类型不存在");
//				return resultMap;
//
//			}
//		} catch (Exception ex) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			ex.printStackTrace();
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "导入订单类型异常:" + ex.getLocalizedMessage());
//			return resultMap;
//		}
//
//		// 导入模板合并在一起的时候
//		// return
//		// importDataServiceImpl.importBatchOrderDataFromExcel(preProcessingWb(myfiles[0]),operType);
//	}

	@ResponseBody
	@RequestMapping(value = "/importOilScan.do", method = RequestMethod.POST)
	public Object importOilScan(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		try {
			return oilVerificationBizService.importOilVerification(preProcessingWb(myfiles[0]));
		} catch (Exception e) {
			log.error("import oil scan data fail: " + e.getMessage());
			Map<String, Object> resultMap = new HashMap<String, Object>(5);
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入扫码数据异常");
			return resultMap;
		}
	}

	@ResponseBody
	@RequestMapping(value = "/importOilByTraceCode.do", method = RequestMethod.POST)
	public Object importOilByTraceCode(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		try {
			return oilVerificationBizService.importOilByTraceCode(preProcessingWb(myfiles[0]), request);
		} catch (Exception e) {
			Map<String, Object> resultMap = new HashMap<String, Object>(5);
			log.error("import oil scan data fail: " + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入扫码数据异常");
			return resultMap;
		}
	}

	@ResponseBody
	@RequestMapping(value = "/importSapSellin.do", method = RequestMethod.POST)
	public Object importSapSellin(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			sapSellinBizServiceImpl.importSapSellin(preProcessingWb(myfiles[0]));
			resultMap.put("code", "success");
		} catch (Exception e) {
			log.error("import sap sellin fail: " + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入Sap sellin数据异常。" + e.getMessage());
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/importCdmPrice.do", method = RequestMethod.POST)
	public Object importCdmPrice(final @RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			final Long userId = ContextUtil.getCurUser().getUserId();
			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(FileManager.preProcessingWb(myfiles[0]), PromotionalCampaign.class, 
					new String[]{null, "partnerType", "partnerName", "promotionTitle", "productSku", "remark", null, null, "startTime", "endTime", 
				null, "ladderPrice"}, 2, 1);
			if("dataInvalid".equals(repsMap.get("result"))){
				throw new WxPltException("Excel解析异常");
			}	
			@SuppressWarnings("unchecked")
			final List<PromotionalCampaign> importList = (List<PromotionalCampaign>) repsMap.get("datalst");
			//保存Excel
			String fileName = myfiles[0].getOriginalFilename();
			String fileType = myfiles[0].getContentType();
			String storageName = StringUtils.getSeqNextval();
			String fileSuffix = StringUtils.getFileSuffixName(fileName);
			String storePath = FileUtil.getFolderName(GregorianCalendar.DATE);
			storageName = storageName + "." + fileSuffix;
			String realPath = FileManager.getFileRealPath(WxAttFile.SourceType_Excel, storePath);
			long fileSize = myfiles[0].getSize();
			try {
				FileUtil.storeFile(realPath, storageName, myfiles[0].getInputStream(), 
						WxAttFile.SourceType_Excel, fileType);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

			// 更新附件信息表
			final WxAttFile wxAttFile = fileManagerService.addAttFile(null, fileName, storageName, 
					fileType, WxAttFile.SourceType_SharedFile, storePath, fileSize, userId, "1");
			final AsynProcessStatus processStatus = new AsynProcessStatus("AsynProcessStatus.importCdmPrice", userId);
			processStatus.setMessage("解析数据......");
			processStatus.save();
			new Thread(){
				public void run() {
					try {
						promotionalCampaignBizService.importDataByExcel(processStatus, importList, wxAttFile.getAttId(), userId);
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
					} catch (WxPltException e) {
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("数据导入失败。" + e.getMessage());
						processStatus.save();
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("数据导入失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
						processStatus.save();
					}
				};
			}.start();
			resultMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
			log.info("importCdmPrice success. ");
			resultMap.put("code", "success");
//		} catch (WxPltException e) {
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("import cdm price fail: " + e.getMessage());
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/importWorkshopActiveFee.do", method = RequestMethod.POST)
	public Object importWorkshopActiveFee(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		try {
			return workshopBizService.importWorkshopActiveFee(preProcessingWb(myfiles[0]));
		} catch (Exception e) {
			log.error("import workshop active fee data fail: " + e.getMessage());
			Map<String, Object> resultMap = new HashMap<String, Object>(5);
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入门店激活数据异常: " + e.getMessage());
			return resultMap;
		}
	}

//	@ResponseBody
//	@RequestMapping(value = "/importDDYYOrderBatchData.do", method = RequestMethod.POST)
//	public Object importBatchDDYYOrderDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
//			HttpServletRequest request, HttpServletResponse response) throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//
//		try {
//			log.info("msg:ljc 0706=======================================滴滴预约服务订单导入");
//			return importDataServiceImpl.importBatchDDYYOrderDataFromExcel(preProcessingWb(myfiles[0]));
//
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "导入订单异常:" + ex.getLocalizedMessage());
//			return resultMap;
//		}
//
//	}

//	@ResponseBody
//	@RequestMapping(value = "/importYYFWOrderBatchData.do", method = RequestMethod.POST)
//	public Object importYYFWOrderBatchDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
//			HttpServletRequest request, HttpServletResponse response) throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try {
//			log.info("msg:ljc 0801=======================================预约服务订单通用导入");
//			return importDataServiceImpl.importYYFWOrderBatchDataFromExcel(preProcessingWb(myfiles[0]));
//
//		} catch (Exception ex) {
//			log.error("", ex);
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "导入订单异常:" + ex.getLocalizedMessage());
//			return resultMap;
//		}
//	}

	@ResponseBody
	@RequestMapping(value = "/importCaltexPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importCaltexPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入积分excel==============-");
			return importDataServiceImpl.importCaltexPointBatchDataFromExcel(preProcessingWb(myfiles[0]));
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入订单异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	@ResponseBody
	@RequestMapping(value = "/importCdmStockPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importCdmPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入物料积分excel==============-");
			return importDataServiceImpl.importCdmStockBatchDataFromExcel(preProcessingWb(myfiles[0]));
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入订单异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	@ResponseBody
	@RequestMapping(value = "/importPromotionPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importPromotionPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入促销积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					PointType.PROMOTION_POINT);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入订单异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	/**
	 * 导入cdm的促销积分
	 * @param myfiles
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/importCdmPromotionPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importCdmPromotionPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入CMD促销积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					PointType.CDM_PROMOTION_POINT);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入CMD促销积分异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	@ResponseBody
	@RequestMapping(value = "/importCdmMaterialPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importCdmMaterialPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入物料积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					PointType.CDM_MATERIAL_POINT);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入订单异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	/**
	 * 导入红包积分
	 * @param myfiles
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/importCdmRedBagPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importCdmRedBagPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入" + PointType.CDM_RED_BAG_POINT.getText()
					+ "积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					PointType.CDM_RED_BAG_POINT);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY,
					"导入" + PointType.CDM_RED_BAG_POINT.getText() + "积分异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}
	
	/**
	 * 导入积分积分
	 * @param myfiles
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/importPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			@RequestParam("pointCode") String pointCode, HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			PointType pointType = PointType.getPointTypeByPointCode(pointCode);
			log.info("=============================导入" + pointType.getText()
					+ "积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					pointType);
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.sys.file.web.FileManager.importPointDataFromExcel", 
					"导入积分失败。" + ex.getMessage(), pointCode);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY,
					"导入" + PointType.CDM_RED_BAG_POINT.getText() + "积分异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	/**
	 * 导入oem进货积分
	 * @param myfiles
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/importOemStockPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importOemStockPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入OEM积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					PointType.OEM_STOCK_POINT);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入OEM积分异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}
	
	@ResponseBody
	@RequestMapping(value = "/importCdmStoreOpenPointDataFromExcel.do", method = RequestMethod.POST)
	public Object importCdmStoreOpenPointDataFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("=============================导入金富力新店开业礼包兑换积分excel==============-");
			return importDataServiceImpl.importPointBatchDataFromExcel(preProcessingWb(myfiles[0]),
					PointType.CDM_STORE_OPEN_POINT);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入OEM积分异常:" + ex.getLocalizedMessage());
			return resultMap;
		}
	}

	@RequestMapping(value = "/getProductImg")
	public void getProductImg(@RequestParam("attId") String attId, @RequestParam("sourceType") String sourceType,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			if (attId != null && (!StringUtils.isNull(attId))) {
				if (sourceType == null || !WxAttFile.SourceType_ProductIcon.equals(sourceType.trim())) {
					return;
				}
				WxAttFile att = fileManagerService.searchAttByKey(attId);
				if (att.getSourceType() == null || !WxAttFile.SourceType_ProductIcon.equals(att.getSourceType())) {
					return;
				}
				String fileSep = System.getProperty("file.separator");
				String realPath = "";
				if (LOCAL.equals(sourceTypeMap.get(sourceType))) {
					realPath = fileUploadPath + att.getStorePath();
				} else {
					realPath = remoteFileUploadPath + att.getStorePath();
				}
				String storageName = "", filePath = "", fileName = "";
				storageName = att.getStorageName();
				fileName = att.getFileName();
				filePath = realPath + fileSep + storageName;
				FileUtil.showImgToBrowser(response, sourceType, filePath, fileName);
			}
		} catch (IOException e) {
			log.error("处理下载文件时发生异常：" + "attId:" + attId + "sourceType:" + sourceType + e.getMessage());
		}
	}

	/*@ResponseBody
	@RequestMapping(value="/importMaterialCustomerProportion.do",method=RequestMethod.POST)
	public Map<String,Object> importMaterialCustomerProportion(
			@RequestParam("myfiles") final MultipartFile[] myfiles,
			final HttpServletRequest request) throws IOException{
		final Map<String, Object> progressStatus = new HashMap<String, Object>();
		final Map<String,Object> resultMap = new HashMap<String,Object>();
		progressStatus.put("status", "progressing");
		progressStatus.put("progressKey", "importCustomerProportionDataFromExcel");
		progressStatus.put("desc", "正在解析...");
		final Workbook workbook=preProcessingWb(myfiles[0]);
		new Thread(new Runnable() {
	
			@Override
			public void run() {
				progressStatus.put("desc", "正在导入...");
				importDataServiceImpl.importCustomerProportionDataFromExcel(workbook,progressStatus);
				progressStatus.put("status", "success");
				progressStatus.put("desc", "导入成功");
				
			}
			
		}).start();
		request.getSession().setAttribute("importCustomerProportionDataFromExcel",progressStatus);
		resultMap.put("progressStatus", progressStatus);
		resultMap.put("code","success");
		return resultMap;
	//		return importDataServiceImpl.importCustomerProportionDataFromExcel(preProcessingWb(myfiles[0]));
	}*/

	@ResponseBody
	@RequestMapping(value = "/importMasterProductFromExcel.do", method = RequestMethod.POST)
	public Object importMasterProductFromExcel(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Throwable {
		log.info("=============================导入产品start==============-");
		try {
			String userId = ContextUtil.getCurUserId().toString();
			/*log.info("=============================导入产品==============-");
			Map<String, Object> resultMap = new HashMap<String, Object>(8);
			final Map<String, Object> progressStatus = new HashMap<String, Object>();
			progressStatus.put("status", "progressing");
			progressStatus.put("progressKey", "importMasterProductFromExcel");
			progressStatus.put("desc", "正在解析...");
			new Thread(){
				public void run() {
					try {
						progressStatus.put("desc", "正在导入...");
						importDataServiceImpl.importMasterProductFromExcel1(preProcessingWb(myfiles[0]),userId);
						log.info("导入产品异常:=======11==========================================");
						progressStatus.put("status", "success");
						progressStatus.put("desc", "导入成功");
					} catch (Exception e) {
						e.printStackTrace();
						log.error("导入产品异常:" + e.getLocalizedMessage());
						progressStatus.put("status", "error");
						progressStatus.put("desc", "导入产品异常: " + e.getMessage());
					}
				};
			}.start();
			request.getSession().setAttribute("importMasterProductFromExcel", progressStatus);
			resultMap.put("progressStatus", progressStatus);
			resultMap.put("code", "success");
			//return importDataServiceImpl.importMasterProductFromExcel1(preProcessingWb(myfiles[0])); */
			log.info("====导入产品,获取当前用户信息userId="+userId);
			return importDataServiceImpl.importMasterProductFromExcel1(preProcessingWb(myfiles[0]),userId);
		} catch (Throwable ex) {
			log.error("导入产品error",ex);
			throw ex;
		}
	}

	@ResponseBody
	@RequestMapping(value = "/importMasterProductFromExcel2.do", method = RequestMethod.POST)
	public Object importMasterProductFromExcel2(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String userId = ContextUtil.getCurUserId().toString();
		/*try {
			log.info("=============================导入产品2==============");
			final Map<String, Object> progressStatus = new HashMap<String, Object>();
			progressStatus.put("status", "progressing");
			progressStatus.put("progressKey", "importMasterProductFromExcel2");
			progressStatus.put("desc", "正在解析...");
			new Thread(){
				public void run() {
					try {
						progressStatus.put("desc", "正在导入...");
						importDataServiceImpl.importMasterProductFromExcel2(preProcessingWb(myfiles[0]),userId);
						log.info("导入产品异常:=======11==========================================");
						progressStatus.put("status", "success");
						progressStatus.put("desc", "导入成功");
					} catch (Exception e) {
						e.printStackTrace();
						log.error("导入产品异常:" + e.getLocalizedMessage());
						progressStatus.put("status", "error");
						progressStatus.put("desc", "导入产品异常: " + e.getMessage());
					}
				};
			}.start();
			request.getSession().setAttribute("importMasterProductFromExcel2", progressStatus);
			resultMap.put("progressStatus", progressStatus);
			resultMap.put("code", "success");
			//return importDataServiceImpl.importMasterProductFromExcel2(preProcessingWb(myfiles[0]));
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "导入产品异常:" + ex.getLocalizedMessage());
		}
		return resultMap;*/
		return importDataServiceImpl.importMasterProductFromExcel2(preProcessingWb(myfiles[0]),userId);
	}

	@ResponseBody
	@RequestMapping(value = "/thirdupload.do", method = RequestMethod.POST)
	public Map<String, Object> thirdupload(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request)
			throws Exception {
		Map<String, Object> resMap = doUploadFileForThirdupload(myfiles, "thirdUpload/");
		return resMap;
	}

	/**
	 * 实现文件上传到指定文件夹, 以及更新相关数据表, 若包含空文件对象, 则全部失败
	 * @param myfiles
	 * @param subStorePath
	 * @return result Map
	 */
	private Map<String, Object> doUploadFileForThirdupload(MultipartFile[] myfiles, String subStorePath) {
		Map<String, Object> resMap = new HashMap<String, Object>();
		List<WxAttFile> attFileList = new ArrayList<WxAttFile>();
		try {
			if (null != myfiles && myfiles.length > 0) {
				for (MultipartFile myfile : myfiles) {
					if (myfile.isEmpty()) {
						log.warn("文件未上传");
						resMap.put("code", "error");
						resMap.put("codeMsg", "文件未上传");
						return resMap;
					}

					// 准备参数
					String fileName = myfile.getOriginalFilename();
					String fileType = myfile.getContentType();
					/*String storageName = StringUtils.getSeqNextval() + "." + StringUtils.getFileSuffixName(fileName);*/
					String storageName = fileName;
					Long fileSize = myfile.getSize();
					String name = myfile.getName();
					String storePath = subStorePath + FileUtil.getFolderName(5);
					String realPath = getFileRealPath("24", storePath);
					log.info("========================================");
					log.info("上传文件长度: " + fileSize);
					log.info("上传文件类型: " + fileType);
					log.info("上传文件名称: " + name);
					log.info("上传文件原名: " + fileName);
					log.info("上传文件位置: " + realPath);
					log.info("========================================");

					// add by bo.liu 1116 start
					if (null == fileSize || fileSize == 0L) {
						log.warn("文件长度异常");
						resMap.put("code", "error");
						resMap.put("codeMsg", "文件长度异常,为null或为0");
						return resMap;
					}

					// 上传文件
					System.out.println("doUploadFile fileType:" + fileType);
					if (null == fileType || fileType.equals("null") || fileType.isEmpty()) {
						fileType = defaultfileType;
					}
					// end
					FileUtil.storeFile(realPath, storageName, myfile.getInputStream(), "24", fileType);

					WxAttFile attFile = new WxAttFile();
					attFile.setFileName(fileName);
					attFile.setStorageName(storageName);
					attFile.setFileType(fileType);
					attFile.setSourceType("24");
					attFile.setStorePath(storePath);
					attFile.setCreateTime(new Date());
					attFile.setFileSize(fileSize);
					attFileList.add(attFile);
				}
			}

			if (!attFileList.isEmpty()) {
				resMap.put("code", "success");
				resMap.put("attachmentFileList", attFileList);
				/*resMap.put("url", "/downloadAttachmentFile.do");*/
			} else {
				resMap.put("code", "noAttachmentFile");
			}
		} catch (Exception e) {
			log.error("upload file exception", e);
			resMap.put("code", "error");
			resMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
		}

		return resMap;
	}


//	@ResponseBody
//	@RequestMapping(value = "/importBudget2021Expense.do", method = RequestMethod.POST)
//	public Object importBudget2021Expense(final @RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
//							HttpServletResponse response) throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>(5);
//		try {
//			final Long userId = ContextUtil.getCurUser().getUserId();
//			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(FileManager.preProcessingWb(myfiles[0]),
//					Budget2021ExpenseImport.class, new String[]{
//							"expenseYear", "expenseMonth", "sapCode", "distributorName", "brandName", "expenseMainType", "expenseType", "payValue", "remark"},
//					1, 0);
//			if("dataInvalid".equals(repsMap.get("result"))){
//				throw new WxPltException("Excel解析异常");
//			}
//
//			@SuppressWarnings("unchecked")
//			final List<Budget2021ExpenseImport> importList = (List<Budget2021ExpenseImport>) repsMap.get("datalst");
//			//保存Excel
//			String fileName = myfiles[0].getOriginalFilename();
//			String fileType = myfiles[0].getContentType();
//			String storageName = StringUtils.getSeqNextval();
//			String fileSuffix = StringUtils.getFileSuffixName(fileName);
//			String storePath = FileUtil.getFolderName(GregorianCalendar.DATE);
//			storageName = storageName + "." + fileSuffix;
//			String realPath = FileManager.getFileRealPath(WxAttFile.SourceType_Excel, storePath);
//
//			try {
//				FileUtil.storeFile(realPath, storageName, myfiles[0].getInputStream(),
//						WxAttFile.SourceType_Excel, fileType);
//			} catch (Exception e) {
//				throw new RuntimeException(e);
//			}
//
//			try {
//				budget2021ExpenseImportBizService.batchImportSync(importList, userId);
//				log.info("importBudget2021Expense success. ");
//				resultMap.put("code", "success");
//			}catch (Exception e){
//				log.error("importBudget2021Expense fail: " + e.getMessage());
//				resultMap.put("code", "error");
//				resultMap.put("codeMsg", e.getMessage());
//			}
//
////			final AsynProcessStatus processStatus = new AsynProcessStatus("AsynProcessStatus.importBudget2021Expense", userId);
////			processStatus.setMessage("解析数据......");
////			processStatus.save();
////			new Thread(){
////				public void run() {
////					try {
////						budget2021ExpenseImportBizService.batchImport(processStatus, importList, userId);
////						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
////						processStatus.save();
////					} catch (WxPltException e) {
////						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
////						processStatus.setMessage("数据导入失败。" + e.getMessage());
////						processStatus.save();
////					} catch (Exception e) {
////						log.error(e.getMessage(), e);
////						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
////						processStatus.setMessage("数据导入失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
////						processStatus.save();
////					}
////				};
////			}.start();
////			resultMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
////			log.info("importBudget2021Expense success. ");
////			resultMap.put("code", "success");
//		} catch (Exception e) {
//			log.error("importBudget2021Expense fail: " + e.getMessage());
//			e.printStackTrace();
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
//		}
//			return resultMap;
//	}
	
	//budgetActualExpense

	@ResponseBody
	@RequestMapping(value = "/importBudgetExpense.do", method = RequestMethod.POST)
	public Object importBae(final @RequestParam("myfiles") MultipartFile[] myfiles, final @RequestParam("expenseMonth")String expenseMonth, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			final Long userId = ContextUtil.getCurUser().getUserId();
			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(FileManager.preProcessingWb(myfiles[0]), 
					BudgetActualExpense.class, new String[]{
						null, "soldToCode", "distributorName", "posmAmount", "tmmAmount", "promotionsAmount", "lsddpAmount", "ditAmount"}, 
					1, 0);
			if("dataInvalid".equals(repsMap.get("result"))){
				throw new WxPltException("Excel解析异常");
			}	
			@SuppressWarnings("unchecked")
			final List<BudgetActualExpense> importList = (List<BudgetActualExpense>) repsMap.get("datalst");
			  //保存Excel
			String fileName = myfiles[0].getOriginalFilename();
			String fileType = myfiles[0].getContentType();
			String storageName = StringUtils.getSeqNextval();
			String fileSuffix = StringUtils.getFileSuffixName(fileName);
			String storePath = FileUtil.getFolderName(GregorianCalendar.DATE);
			storageName = storageName + "." + fileSuffix;
			String realPath = FileManager.getFileRealPath(WxAttFile.SourceType_Excel, storePath);
			long fileSize = myfiles[0].getSize();
			
			try {
				FileUtil.storeFile(realPath, storageName, myfiles[0].getInputStream(), 
						WxAttFile.SourceType_Excel, fileType);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

			// 更新附件信息表
			final WxAttFile wxAttFile = fileManagerService.addAttFile(null, fileName, storageName, 
					fileType, WxAttFile.SourceType_SharedFile, storePath, fileSize, userId, "1");
					
			final AsynProcessStatus processStatus = new AsynProcessStatus("AsynProcessStatus.importCdmPrice", userId);
			processStatus.setMessage("解析数据......");
			processStatus.save();
			new Thread(){
				public void run() {
					try {
						budgetActualExpenseBizService.importDataByExcel(processStatus, importList, wxAttFile.getAttId(), userId, expenseMonth);
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
					} catch (WxPltException e) {
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("数据导入失败。" + e.getMessage());
						processStatus.save();
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("数据导入失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
						processStatus.save();
					}
				};
			}.start();
			resultMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
			log.info("importBudgetExpense success. ");
			resultMap.put("code", "success");
			
//		} catch (WxPltException e) {
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("import BudgetActualExpense fail: " + e.getMessage());
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/budget/importExpense.do", method = RequestMethod.POST)
	public Object importBudgetExpense(final @RequestParam("myfiles") MultipartFile[] myfiles, final @RequestParam("expenseMonth")String expenseMonth,
			final @RequestParam("bu")String bu, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			//查询当前配置导入费用项
			Map<String, Object> paramsMap = new HashMap<String, Object>(5);
			paramsMap.put("bu", bu);
			paramsMap.put("actualFromImportFlag", 1);
			paramsMap.put("enableFlag", 1);
			paramsMap.put("orderBy", "sort_numb");
			final List<BudgetExpenseItem> expenseItems = budgetExpenseItemMapper.queryByParams(paramsMap);
//			final Map<String, BudgetExpenseItem> propertyMap = new HashMap<String, BudgetExpenseItem>(list.size());
			String[] importProperties = new String[expenseItems.size() + 2];
			//importProperties[1] = "soldToCode";
			importProperties[1] = "distributorName";
			for(int i = 0; i < expenseItems.size(); i++) {
				String p = "expenseValue" + i;
//				propertyMap.put(p, list.get(i));
				importProperties[i + 2] = p;
			}
			
			final Long userId = ContextUtil.getCurUser().getUserId();
			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(FileManager.preProcessingWb(myfiles[0]), 
					BudgetImportExpense.class, importProperties, 
					1, 0);
			if("dataInvalid".equals(repsMap.get("result"))){
				throw new WxPltException("Excel解析异常");
			}	
			@SuppressWarnings("unchecked")
			final List<BudgetImportExpense> importList = (List<BudgetImportExpense>) repsMap.get("datalst");
			  //保存Excel
			String fileName = myfiles[0].getOriginalFilename();
			String fileType = myfiles[0].getContentType();
			String storageName = StringUtils.getSeqNextval();
			String fileSuffix = StringUtils.getFileSuffixName(fileName);
			String storePath = FileUtil.getFolderName(GregorianCalendar.DATE);
			storageName = storageName + "." + fileSuffix;
			String realPath = FileManager.getFileRealPath(WxAttFile.SourceType_Excel, storePath);
			long fileSize = myfiles[0].getSize();
			
			try {
				FileUtil.storeFile(realPath, storageName, myfiles[0].getInputStream(), 
						WxAttFile.SourceType_Excel, fileType);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

			// 更新附件信息表
			final WxAttFile wxAttFile = fileManagerService.addAttFile(null, fileName, storageName, 
					fileType, WxAttFile.SourceType_SharedFile, storePath, fileSize, userId, "1");
					
			final AsynProcessStatus processStatus = new AsynProcessStatus("AsynProcessStatus.importBudgetExpense", userId);
			processStatus.setMessage("解析数据......");
			processStatus.save();
			new Thread(){
				public void run() {
					try {
						budgetImportExpenseBizService.importDataByExcel(processStatus, importList, wxAttFile.getAttId(), userId, expenseMonth, bu, expenseItems);;
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
					} catch (WxPltException e) {
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("数据导入失败。" + e.getMessage());
						processStatus.save();
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("数据导入失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
						processStatus.save();
					}
				};
			}.start();
			resultMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
			log.info("importBudgetExpense success. ");
			resultMap.put("code", "success");
			
//		} catch (WxPltException e) {
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("import BudgetActualExpense fail: " + e.getMessage());
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/importOrderService.do", method = RequestMethod.POST)
	public Object importOrderService(@RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			orderDDBXServiceImpl.importOrderService(preProcessingWb(myfiles[0]));
			resultMap.put("code", "success");
		} catch (Exception e) {
			log.error("import 服务订单 fail: " + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入服务订单数据异常。" + e.getMessage());
		}
		return resultMap;
	}
	
	
	@RequestMapping(value = "/uploadForAppImages.do", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> uploadForAppImages(@RequestParam("sourceType") String sourceType,@RequestParam("id") String id) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if(StringUtils.isNull(sourceType) ||StringUtils.isNull(id) ) {
			resultMap.put("code", MessageContants.PARAMS_NULL_CODE);
			resultMap.put("codeMsg", MessageContants.PARAMS_NULL_MSG);
			return resultMap;
		}
		//保存图片
		WxAttFile wxAttFile=null;
		try {
		   HttpURLConnection httpUrlConn = HttpClientUtils.httpRequest(id);
           InputStream inputStream = httpUrlConn.getInputStream();  
           Map<String, List<String>> map = httpUrlConn.getHeaderFields();
           Long fileSize = (long) httpUrlConn.getContentLength();
           String fileType = httpUrlConn.getContentType();
           String fileName = id+getExt(httpUrlConn.getContentType());
	       String subStorePath="";
	       String storageName = StringUtils.getSeqNextval() + "." + StringUtils.getFileSuffixName(fileName);
	       String storePath = FileUtil.getFolderName(Integer.parseInt(folderGrainSize)) + subStorePath;
	       String realPath = getFileRealPath(sourceType, storePath);
			FileUtil.storeFile(realPath, storageName,inputStream, sourceType, fileType);
			// 更新附件信息表
			wxAttFile = fileManagerService.addAttFile(null, fileName, storageName, fileType,
					sourceType, storePath, fileSize, ContextUtil.getCurUserId(), "1");
			if (wxAttFile!=null) {
				resultMap.put("code", "success");
				resultMap.put("attachmentFileList", wxAttFile);
				resultMap.put("url", "/downloadAttachmentFile.do");
			} else {
				resultMap.put("code", "noAttachmentFile");
			} 
			 inputStream.close();  
			 httpUrlConn.disconnect();
		} catch (Exception e) {
			log.error("upload file exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
		}
		return resultMap;
	}
	 private String getExt(String contentType){
		 if("image/jpeg".equals(contentType)) {
			 return ".jpg";
		 }else if("image/png".equals(contentType)) {
			 return ".png";
		 }else if("image/gif".equals(contentType)) {
			 return ".gif";
		 }
         return null;
     }
	
}
