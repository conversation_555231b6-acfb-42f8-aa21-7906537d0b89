1. 获取用户组用户 RPC
com.sys.bpm.service.impl.BmpServiceImpl.getUserListByGroup(List<String> groupNames, Map<String, String> params)
{"params":[["Credit BU Sales Manager"],{"bu":"Direct","soldTo":"84208390"}],"id":"1","jsonrpc":"2.0","method":"bmpService.getUserListByGroup"}
返回：
code：标识服务执行结果。
		success-成功
		E102-非法输入
		E104-系统异常
errorMsg：错误信息		
user.userId: id
	chName: 姓名
	email：邮箱（发邮件）
	mobileTel：手机号（发短信）
