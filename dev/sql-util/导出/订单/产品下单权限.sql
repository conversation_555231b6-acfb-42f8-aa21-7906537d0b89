="union all select (select case when p.support_order=1 then '是' else '否' end from wx_t_product p where p.sku='"&A2&"') support_order"
="union all select (select case when p.status=1 then 'Active' else 'Inactive' end from wx_t_product p where p.sku='"&A2&"') status"

select dp.product_sku SKU, o.organization_name "经销商名称", 
(select distinct c.region from dw_customer_org_sales c where c.distributor_id=pe.distributor_id) region
from wx_t_dealer_product_permission dp left join wx_t_organization o on o.id=dp.dealer_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where dp.product_sku='503035DNK' and o.id!=9
order by (select distinct c.region from dw_customer_org_sales c where c.distributor_id=pe.distributor_id)

select o.organization_name "经销商名称", 
case when o.status=1 then 'Active' else 'Inactive' end "状态",
(select distinct c.region from dw_customer_org_sales c where c.distributor_id=pe.distributor_id and c.region in (select dic_item_code from wx_t_dic_item where dic_type_code='Region.Indirect')) region,
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配城市"
from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where o.type=1 --and o.status=1 
and o.id!=9
