/****** <PERSON><PERSON>t for SelectTopNRows command from SSMS  ******/
SELECT TOP (1000) [id]
      ,[customer_name]
      ,[payment_term]
      ,[price_level_type]
      ,[price_level_value]
      ,[date_from]
      ,[date_to]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[updated_by]
      ,[lgcl_del_fg]
      ,[partner_id]
	  --update c set c.date_from='2022-09-01'
  FROM [PP_MID].[dbo].[pp_customer_pricing_level_cfg] c where [price_level_value]='8473778'
  
/****** Script for SelectTopNRows command from SSMS  ******/
SELECT TOP (1000) [id]
      ,[distributor_id]
      ,[planning_customer_name]
      ,[sap_code]
      ,[customer_name]
      ,[region_name]
      ,[channel]
      ,[partner_function]
      ,[ship_to_code]
      ,[partner_name_with_type]
      ,[payment_term]
      ,[sales_org]
      ,[price_level]
      ,[purpose]
      ,[date_from]
      ,[date_to]
      ,[quota_type]
      ,[quota]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[updated_by]
      ,[quota_ratio]
      ,[is_quota_ratio]
      ,[quota_ratio_sales_org]
      ,[quota_ratio_sales_code]
      ,[dist_id]
      ,[address]
      ,[contact_person]
      ,[contact_person_tel]
      ,[store_id]
	  --update c set c.date_from='2022-09-01'
  FROM [PP_MID].[dbo].[mid_partner_sale_config] c where [ship_to_code]='8473778'
  
update c set c.date_to=psc.date_to, c.[update_time]=getdate()
from [PP_MID].[dbo].[pp_customer_pricing_level_cfg] c
left join (select psc1.*,ROW_NUMBER() OVER (PARTITION BY psc1.payment_term, psc1.ship_to_code,psc1.price_level ORDER BY psc1.update_time DESC) as rn 
FROM [PP_MID].dbo.mid_partner_sale_config psc1
) psc on psc.ship_to_code=c.price_level_value and psc.payment_term=c.payment_term and psc.price_level='ship_to_code'
LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
LEFT JOIN wx_t_organization org ON org.id = poe.partner_id
where price_level_type='ship_to_code' and c.partner_id=org.id and org.id!=9
	and c.date_from=psc.date_from and c.date_to>getdate() and c.date_to is not null and c.date_to!=convert(date,psc.date_to)

/*失效已修改数据*/
update c set c.date_to=dateadd(dd,-day(getdate()),getdate()), c.[update_time]=getdate(),c.lgcl_del_fg='Y'
from [PP_MID].[dbo].[pp_customer_pricing_level_cfg] c
where price_level_type='ship_to_code' and c.date_to>getdate()
and not exists (select 1 FROM [PP_MID].dbo.mid_partner_sale_config psc where psc.ship_to_code=c.price_level_value and psc.payment_term=c.payment_term and psc.price_level='ship_to_code'
and psc.date_from=c.date_from)

/*新增数据。配额数据特殊处理date_from*/	
insert into [PP_MID].[dbo].[pp_customer_pricing_level_cfg] ([customer_name]
      ,[payment_term]
      ,[price_level_type]
      ,[price_level_value]
      ,[date_from]
      ,[date_to]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[updated_by]
      ,[lgcl_del_fg]
	  ,partner_id)
select org.organization_name [customer_name]
      ,psc.[payment_term]
      ,'ship_to_code' [price_level_type]
      ,psc.ship_to_code [price_level_value]
      /*,case when psc.is_quota_ratio=1 or psc.date_from is null then dateadd(month, datediff(month, 0, getdate()), 0) else psc.date_from end [date_from]
	  配额从当月起*/
      ,case when psc.date_from is null then dateadd(month, datediff(month, 0, getdate()), 0) else psc.date_from end [date_from]
      ,isnull(psc.date_to, '2100-01-01') [date_to]
      ,getdate() [creation_time]
      ,0 [created_by]
      ,getdate() [update_time]
      ,0 [updated_by]
      ,'N' [lgcl_del_fg]
	  ,org.id partner_id
FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					LEFT JOIN wx_t_organization org ON org.id = poe.partner_id
					where org.status=1 and org.id!=9 and psc.price_level='ship_to_code'
					and (psc.date_to is null or psc.date_to>getdate())
					and not exists (select 1 from [PP_MID].[dbo].[pp_customer_pricing_level_cfg] c
						where psc.ship_to_code=c.price_level_value 
					and psc.payment_term=c.payment_term and dateadd(day, 1, c.date_to)>getdate())
	and len(psc.[payment_term])>0						
  