/*==============================================================*/
/* Module: 非本地营销                                                */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_main_local_marketing                             */
/*==============================================================*/
create table wx_t_main_local_marketing (
	id                               bigint                    not null identity(true,1),
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	report_id                        nvarchar(64)                                   null,
	report_region_id                 bigint                                         null,
	report_name                      nvarchar(64)                                   null,
	reprot_type                      bigint                                         null,
	distributor_id                   bigint                                     not null,
	reprot_status                    bigint                                         null,
	ext_flag                         int                                            null,
	delete_flag                      tinyint                                        null,
	attribute1                       nvarchar(64)                                   null,
	attribute2                       nvarchar(64)                                   null,
	attribute3                       nvarchar(64)                                   null,
	remark                           nvarchar(64)                                   null,
	report_latitude                  decimal(20,6)                                  null,
	report_longitude                 decimal(20,6)                                  null,
	photo_latitude                   decimal(20,6)                                  null,
	photo_longitude                  decimal(20,6)                                  null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_non_local_mkt_flow_form                          */
/*==============================================================*/
create table wx_t_non_local_mkt_flow_form (
	id                               bigint                            not null identity,
	report_id                        bigint                                     not null,
	req_no                           nvarchar(64)                                   null,
	req_user_id                      bigint                                         null,
	req_time                         datetime                                       null,
	form_status                      int                                        not null,
	to_distributor_id                bigint                                         null,
	from_distributor_id              bigint                                         null,
	from_asm_survey_submit_time      datetime                                       null,
	to_asm_feedback_submit_time      datetime                                       null,
	from_asm_response                tinyint                                        null,
	from_asm_response_submit_time    datetime                                       null,
	abm_submit_time                  datetime                                       null,
	approval_completion_time         datetime                                       null,
	local_marketing_status           tinyint                                        null,
	ext_flag                         int                                            null,
	delete_flag                      tinyint                                        null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	attribute4                       nvarchar(512)                                  null,
	attribute5                       nvarchar(512)                                  null,
	update_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	create_user_id                   bigint                                         null,
	from_ka_flag                     tinyint                                    not null,
	primary key (id)
);
ALTER TABLE wx_t_non_local_mkt_flow_form ADD CONSTRAINT uk_non_local_mkt_flow_form UNIQUE (req_no);

/*==============================================================*/
/* Table: wx_t_loc_mkt_product_info                             */
/*==============================================================*/
create table wx_t_loc_mkt_product_info (
	id                               bigint                            not null identity,
	report_id                        bigint                                     not null,
	flow_id                          bigint                                         null,
	from_distributor_id              bigint                                         null,
	product_name                     nvarchar(64)                                   null,
	sku                              nvarchar(32)                                   null,
	reporting_region_id              nvarchar(64)                                   null,
	scan_time                        datetime                                       null,
	product_time                     datetime                                       null,
	product_batch                    nvarchar(64)                                   null,
	product_out_no                   nvarchar(64)                                   null,
	logistics_code                   nvarchar(64)                                   null,
	channel                          nvarchar(64)                                   null,
	photo_att_id                     nvarchar(64)                                   null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	from_distributor_regions         nvarchar(max)                                  null,
	to_distributor_regions           nvarchar(max)                                  null,
	auth_channel                     int                                        not null,
	display_channel                  nvarchar(64)                                   null,
	primary key (id)
);
CREATE INDEX ix_loc_mkt_product_info_sku ON wx_t_loc_mkt_product_info (sku);

/*==============================================================*/
/* Table: wx_t_non_local_mkt_flow_pro_res                       */
/*==============================================================*/
create table wx_t_non_local_mkt_flow_pro_res (
	id                               bigint                            not null identity,
	flow_id                          bigint                                     not null,
	channel                          nvarchar(64)                                   null,
	setp_no                          nvarchar(64)                                   null,
	local_marketing_status           tinyint                                        null,
	handle_info                      nvarchar(4000)                                 null,
	punish_flag                      int                                            null,
	deduct_bonus                     decimal(20,6)                                  null,
	other_reasons                    nvarchar(64)                                   null,
	kpi_result                       tinyint                                        null,
	ext_flag                         int                                            null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	attribute4                       nvarchar(512)                                  null,
	attribute5                       nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	attribute6                       nvarchar(512)                                  null,
	attribute7                       nvarchar(512)                                  null,
	attribute8                       nvarchar(512)                                  null,
	attribute9                       nvarchar(512)                                  null,
	attribute10                      nvarchar(64)                                   null,
	attribute11                      nvarchar(64)                                   null,
	attribute12                      nvarchar(64)                                   null,
	attribute13                      nvarchar(64)                                   null,
	attribute14                      nvarchar(64)                                   null,
	attribute15                      nvarchar(64)                                   null,
	attribute16                      nvarchar(64)                                   null,
	attribute17                      nvarchar(64)                                   null,
	attribute18                      nvarchar(64)                                   null,
	attribute19                      nvarchar(64)                                   null,
	punish_version                   int                                        not null,
	display_channel                  nvarchar(64)                                   null,
	auth_channel                     int                                        not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_non_local_mkt_emaile_info                        */
/*==============================================================*/
create table wx_t_non_local_mkt_emaile_info (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	flow_id                          bigint                                         null,
	source_id                        bigint                                         null,
	from_distributor_id              bigint                                         null,
	region_name                      nvarchar(100)                                  null,
	channel                          nvarchar(64)                                   null,
	warn_info                        nvarchar(4000)                                 null,
	warn_month                       nvarchar(64)                                   null,
	warn_year                        nvarchar(64)                                   null,
	deliver_status                   nvarchar(64)                                   null,
	deliver_time                     datetime                                       null,
	delete_flag                      tinyint                                        null,
	ext_flag                         int                                            null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_non_local_user_report                            */
/*==============================================================*/
create table wx_t_non_local_user_report (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	from_distributor_id              bigint                                         null,
	sku                              nvarchar(100)                                  null,
	report_region_id                 bigint                                         null,
	product_date                     datetime                                       null,
	out_date                         datetime                                       null,
	product_batch                    nvarchar(200)                                  null,
	out_order_no                     nvarchar(64)                                   null,
	logistics_code                   nvarchar(64)                                   null,
	report_latitude                  decimal(20,6)                                  null,
	report_longitude                 decimal(20,6)                                  null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	ext_flag                         int                                        not null,
	from_distributor_regions         nvarchar(max)                                  null,
	auth_channel                     int                                        not null,
	display_channel                  nvarchar(64)                                   null,
	report_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_non_local_user_report ADD CONSTRAINT uk_non_local_user_report UNIQUE (logistics_code);

/*==============================================================*/
/* Foreign Key(only index): ix_no_lo_mk_fl_fo_ma_lo_ma          */
/*==============================================================*/
CREATE INDEX ix_no_lo_mk_fl_fo_ma_lo_ma ON wx_t_non_local_mkt_flow_form (report_id);

/*==============================================================*/
/* Foreign Key(only index): ix_lo_mk_pr_in_ma_lo_ma             */
/*==============================================================*/
CREATE INDEX ix_lo_mk_pr_in_ma_lo_ma ON wx_t_loc_mkt_product_info (report_id);

/*==============================================================*/
/* Foreign Key(only index): ix_n_l_m_f_p_r_n_l_m_f_f            */
/*==============================================================*/
CREATE INDEX ix_n_l_m_f_p_r_n_l_m_f_f ON wx_t_non_local_mkt_flow_pro_res (flow_id);
