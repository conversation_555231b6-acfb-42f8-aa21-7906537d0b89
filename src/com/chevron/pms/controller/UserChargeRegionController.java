package com.chevron.pms.controller;

import com.chevron.pms.business.UserChargeRegionBizService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.auth.model.UserCtrlParams;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 用户负责区域Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2017-07-03 14:47
 */
@Controller
@RequestMapping(value="/userchargeregion")
public class UserChargeRegionController {
	@Autowired
	private UserChargeRegionBizService userChargeRegionBizService;
	
	private final static Logger log = Logger.getLogger(UserChargeRegionController.class);
	
	/**
	 * 用户负责区域列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(UserCtrlParams params){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			userChargeRegionBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}
	
}
