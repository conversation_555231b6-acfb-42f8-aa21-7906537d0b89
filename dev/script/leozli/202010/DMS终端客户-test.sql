/*同步user*/
truncate table PP_MID.DBO.pp_user
insert into PP_MID.DBO.pp_user (user_name_cn,user_code,org_id,user_type,enable_flag,create_time,update_time)
select u.ch_name, u.user_id, u.org_id,'distributor', u.status,getdate(),getdate() from wx_t_user u left join wx_t_organization o on o.id=u.org_id
where o.type=1 and o.status=1 and (u.type is null or u.type !='1') and o.id!=9

update mu set mu.user_name_cn=u.ch_name, mu.enable_flag=u.status,mu.update_time=getdate()
from PP_MID.DBO.pp_user mu 
left join wx_t_user u on u.user_id=mu.user_code 
where u.xg_time>=(select max(mu1.update_time) from PP_MID.DBO.pp_user mu1)

/*巡店*/
truncate table PP_MID.dbo.pp_dist_customer_inspections
insert into PP_MID.dbo.pp_dist_customer_inspections (id,customer_type,customer_id,inspections_time,
	business_type,enable_flag,dsr_code,create_time,update_time) 
select s.task_id, 
case when m.tmb_type_code='TT_2_FLEET_ZF' then 2 else 1 end customer_type,
s.org_id, s.xg_sj, 
case when m.tmb_type_code in ('TT_2_FLEET_ZF', 'TT_2_XD_CAI') then 2 else 1 end business_type,
1 enable_flag, m.excute_user_id, getdate(), getdate()
from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
left join wx_t_organization o on o.id=m.tenant_id
where m.tmb_type_code IN ('TT_2_XD','TT_2_XD_CAI','TT_2_FLEET_ZF') --TT_2_PROJECT_CLIENT_VISIT
and s.task_status='4' and m.tenant_id!=9 and o.status=1

/*同步门店*/
truncate table PP_MID.[dbo].[pp_distributor_customer20201112]
INSERT INTO PP_MID.[dbo].[pp_distributor_customer20201112]
           ([partner_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[business_property]
           ,[delete_flag]
           ,[create_time]
           ,[update_time]
           ,[customer_segement]
           ,[dsr_code]
           ,[customer_onboard_time]
           , customer_subtype)
select partner_id
           ,1 [customer_type]
           ,id [customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,case when a.min_time is not null and a.min_time < a.[customer_create_time] then a.min_time else a.[customer_create_time] end [customer_create_time]
           ,case when [customer_label]=0 then 65536 else [customer_label] end [customer_label]
           ,[business_property]
           ,0 [delete_flag], 
           getdate() [create_time]
           ,getdate() [update_time]
           ,[customer_segement]
           ,isnull(a.excute_user_id, isnull((select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=a.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc), 
				(select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=a.creator))) [dsr_code]
           ,isnull((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=a.id and ws.workshop_with_status='3'), a.[customer_create_time]) [customer_onboard_time]
           , (SELECT top 1 d.item_code
                           FROM PP_MID.[dbo].pp_dict20201112 d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & (case when [customer_label]=0 then 65536 else [customer_label] end) > 0 order by d.sort_numb) from (
select pe.partner_id, w.id,
w.work_shop_name [customer_name], w.region_id [dist_code], w.work_shop_address [address], 
case when w.delete_flag=1 then -100 else w.status end [customer_status],
w.dms_key [dms_key], dw.Distributor_Code dms_distributor_code, dw.[Customer_Code] [dms_customer_code], 
w.[contact_person] [contact_person], 
w.[contact_person_tel] [contact_person_tel], w.[from_source] [from_source], w.excute_user_id,
case when w.from_source=1 then w.type when w.from_source=2 then w.delo_type else null end [customer_segement],
w.create_time [customer_create_time], w.creator,(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id) min_time,
(w.from_source & 2) +
+ (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then 1024 else 0 end/*B2B店*/)
+ (case when exists (select 1   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.org_id=w.id) then 32768 else 0 end/*陈列大比拼*/)
			 + (case when w.shop_recruitment=1 then 2048 else 0 end/*店招店*/) + (case when w.join_location_plan=1 then 4096 else 0 end/*定位店*/)
			  + (case when w.ext_flag&1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动*/)
			  + (case when w.ext_flag&16384>0 then 131072 else 0 end/*黑金店*/)
			  + (case when w.ext_flag&16>0 then 16384 else 0 end/*导航店*/) [customer_label],
w.business_weight|w.from_source [business_property] from wx_t_work_shop w 
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join dw_dms_workshop dw on dw.[Distributor_Code] + '/' + dw.[Customer_Code]=w.dms_key
left join wx_t_organization o on o.id=wp.partner_id
where --not exists (select 1 from PP_MID.dbo.pp_distributor_customer20201112 c where c.customer_type=1 and c.customer_id=w.id) and 
o.status=1 and w.status='3' and w.delete_flag=0
and wp.partner_id!=9 and pe.distributor_id is not null 
--and not exists (select 1 from wx_t_dic_item di1 where di1.dic_type_code='SynToBi.DistributorCustomer.excludeDistributor' and di1.dic_item_code=pe.distributor_id)
and w.from_source&3>0) a

/*同步车队*/
INSERT INTO PP_MID.[dbo].[pp_distributor_customer20201112]
           ([partner_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[business_property]
           ,[delete_flag]
           ,[create_time]
           ,[update_time]
           ,[customer_segement]
           ,[dsr_code]
           ,[customer_onboard_time]
           , customer_subtype)
select partner_id
           ,2 [customer_type]
           ,id [customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,case when [customer_label]=0 then ********* else [customer_label] end [customer_label]
           ,[business_property]
           ,0 [delete_flag], 
           getdate() [create_time]
           ,getdate() [update_time]
           ,[customer_segement]
           ,(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=a.id and m.tmb_type_code IN ('TT_2_FLEET','TT_2_FLEET_ZF') order by s.xg_sj desc) [dsr_code]
           ,[customer_create_time] [customer_onboard_time]
           , (SELECT top 1 d.item_code
                           FROM PP_MID.[dbo].pp_dict20201112 d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & (case when [customer_label]=0 then ********* else [customer_label] end) > 0 order by d.sort_numb) from (
select f.[partner_id], f.id,
f.[fleet_name] [customer_name], f.region_id [dist_code], f.[fleet_address] [address], 
case when f.delete_flag=1 then -100 else f.status end [customer_status],
null [dms_key], null dms_distributor_code, null [dms_customer_code], 
null [contact_person], 
[fleet_phone] [contact_person_tel], 2 [from_source],
f.[fleet_type] [customer_segement],
f.create_time [customer_create_time], 
(case when f.ext_flag&1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动*/) [customer_label],
2 [business_property] 
from [wx_t_fleet_info] f
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=f.[partner_id]
left join wx_t_organization o on o.id=pe.partner_id
where --not exists (select 1 from PP_MID.dbo.pp_distributor_customer20201112 c where c.customer_type=1 and c.customer_id=w.id) and 
o.status=1 and f.status='3' --and w.delete_flag=0
and f.[partner_id]!=9 and pe.distributor_id is not null
) a

/*项目工地*/
INSERT INTO PP_MID.[dbo].[pp_distributor_customer20201112]
           ([partner_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[business_property]
           ,[delete_flag]
           ,[create_time]
           ,[update_time]
           ,[customer_segement]
           ,[dsr_code]
           ,[customer_onboard_time]
           , customer_subtype)
select partner_id
           ,3 [customer_type]
           ,id [customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,case when [customer_label]=0 then ********* else [customer_label] end [customer_label]
           ,[business_property]
           ,0 [delete_flag], 
           getdate() [create_time]
           ,getdate() [update_time]
           ,[customer_segement]
           ,(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=a.id and m.tmb_type_code IN ('TT_2_FLEET','TT_2_FLEET_ZF') order by s.xg_sj desc) [dsr_code]
           ,[customer_create_time] [customer_onboard_time]
           , (SELECT top 1 d.item_code
                           FROM PP_MID.[dbo].pp_dict20201112 d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & (case when [customer_label]=0 then ********* else [customer_label] end) > 0 order by d.sort_numb) from (
select c.[partner_id], c.id,
c.[project_name] [customer_name], c.region_id [dist_code], c.[project_address] [address], 
case when c.delete_flag=1 then -100 else 3 end [customer_status],
null [dms_key], null dms_distributor_code, null [dms_customer_code], 
null [contact_person], 
NULL [contact_person_tel], 4 [from_source],
NULL [customer_segement],
c.create_time [customer_create_time], 
0 [customer_label],
4 [business_property] 
from [wx_t_construction_machinery] c
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=c.[partner_id]
left join wx_t_organization o on o.id=pe.partner_id
where o.status=1 and c.delete_flag=0 and c.[partner_id]!=9 and pe.distributor_id is not null
) a

/* 建筑渣土车队 */
INSERT INTO PP_MID.[dbo].[pp_distributor_customer20201112]
           ([partner_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[business_property]
           ,[delete_flag]
           ,[create_time]
           ,[update_time]
           ,[customer_segement]
           ,[dsr_code]
           ,[customer_onboard_time]
           , customer_subtype)
select partner_id
           ,2 [customer_type]
           ,id [customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,case when [customer_label]=0 then ********* else [customer_label] end [customer_label]
           ,[business_property]
           ,0 [delete_flag], 
           getdate() [create_time]
           ,getdate() [update_time]
           ,[customer_segement]
           ,(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=a.id and m.tmb_type_code IN ('TT_2_PROJECT_FLEET') order by s.xg_sj desc) [dsr_code]
           ,[customer_create_time] [customer_onboard_time]
           , (SELECT top 1 d.item_code
                           FROM PP_MID.[dbo].pp_dict20201112 d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & (case when [customer_label]=0 then ********* else [customer_label] end) > 0 order by d.sort_numb) from (
select c.[partner_id], c.id,
c.company_name [customer_name], c.region_id [dist_code], c.fleet_address [address], 
case when c.delete_flag=1 then -100 else 3 end [customer_status],
null [dms_key], null dms_distributor_code, null [dms_customer_code], 
c.contact_person [contact_person], 
c.contact_tel [contact_person_tel], 64 [from_source],
c.fleet_type [customer_segement],
c.create_time [customer_create_time], 
0 [customer_label],
1073741824 [business_property] 
from wx_t_con_mac_fleet c
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=c.[partner_id]
left join wx_t_organization o on o.id=pe.partner_id
where o.status=1 and c.delete_flag=0 and c.[partner_id]!=9 and pe.distributor_id is not null and c.status=3
) a

/* 工程机械车队 */
INSERT INTO PP_MID.[dbo].[pp_distributor_customer20201112]
           ([partner_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[business_property]
           ,[delete_flag]
           ,[create_time]
           ,[update_time]
           ,[customer_segement]
           ,[dsr_code]
           ,[customer_onboard_time]
           , customer_subtype)
select partner_id
           ,4 [customer_type]
           ,id [customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,case when a.min_time is not null and a.min_time < a.[customer_create_time] then a.min_time else a.[customer_create_time] end [customer_create_time]
           ,case when [customer_label]=0 then ********* else [customer_label] end [customer_label]
           ,[business_property]
           ,0 [delete_flag], 
           getdate() [create_time]
           ,getdate() [update_time]
           ,[customer_segement]
           ,isnull(a.excute_user_id, isnull((select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=a.id and m.tmb_type_code IN ('TT_2_PROJECT_CLIENT', 'TT_2_PROJECT_CLIENT_VISIT') order by s.xg_sj desc), 
				(select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=a.creator))) [dsr_code]
           ,isnull((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=a.id and ws.workshop_with_status='3'), a.[customer_create_time]) [customer_onboard_time]
           , (SELECT top 1 d.item_code
                           FROM PP_MID.[dbo].pp_dict20201112 d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & (case when [customer_label]=0 then ********* else [customer_label] end) > 0 order by d.sort_numb) from (
select pe.partner_id, w.id,
w.work_shop_name [customer_name], w.region_id [dist_code], w.work_shop_address [address], 
case when w.delete_flag=1 then -100 else w.status end [customer_status],
w.dms_key [dms_key], null dms_distributor_code, null [dms_customer_code], 
w.[contact_person] [contact_person], 
w.[contact_person_tel] [contact_person_tel], 4 [from_source], w.excute_user_id,
w.type [customer_segement],
w.create_time [customer_create_time], w.creator,(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id) min_time,
0 [customer_label],
4 [business_property] from wx_t_work_shop w 
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join wx_t_organization o on o.id=wp.partner_id
where o.status=1 and w.status='3' and w.delete_flag=0
and wp.partner_id!=9 and pe.distributor_id is not null 
and w.from_source&16>0) a

/**同步sell through*/
truncate table PP_MID.dbo.pp_sell_through
insert into PP_MID.dbo.pp_sell_through ([customer_type]
      ,[customer_id]
      ,[year_month]
      ,[order_no]
      ,[product_code]
      ,[sales_unit]
      ,[sales_amount]
      ,[sales_quantity]
      ,[sales_liters]
      ,[saler_code]
      ,[order_creator]
      ,[order_creator_mobile]
      ,[order_status]
      ,[order_create_time]
      ,[order_paied_time]
      ,[order_cancel_time]
      ,[order_delivery_time]
      ,[create_time]
      ,[update_time])
SELECT 1 [customer_type], wto.work_shop_id, wto.create_time,
	wto.order_no, isnull(tbc.sku,tp.sku) sku, tp.units, null,wol.amount [sales_quantity],
	wol.amount*convert(float, isnull(tbp.capacity, tp.capacity)) [sales_liters],
    isnull((select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=wto.creator),
	     isnull(ws.excute_user_id, isnull((select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=ws.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc), 
				(select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=ws.creator)))) [saler_code], 
	null [order_creator], null, 100 [order_status], wto.create_time [order_create_time],
	null [order_paied_time], null [order_cancel_time], wto.update_time [order_delivery_time],getdate(),getdate()
				FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	left join temp_bundle_config tbc on tbc.bsku=wol.sku
	left join wx_t_product tbp ON tbc.sku = tbp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
	left join wx_t_organization o on o.id=wp.partner_id
WHERE (wto.status=11 or (wto.partner_confirm_type = 1 and wto.status!=6) or EXISTS (
					SELECT 1
					FROM wx_t_db2b_order db_or
					WHERE  charindex(db_or.order_no, wto.source) > 0
						AND db_or.delete_flag = 0
						AND db_or.status IN (1)
				))
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-01-01'
	AND wto.order_type not in('DA','DP','SPDA')
	AND wp.partner_id!=9 
	and o.status=1
	
	/*
insert into PP_MID.dbo.pp_sell_through ([customer_type]
      ,[customer_id]
      ,[year_month]
      ,[order_no]
      ,[product_code]
      ,[sales_unit]
      ,[sales_amount]
      ,[sales_quantity]
      ,[sales_liters]
      ,[saler_code]
      ,[order_creator]
      ,[order_creator_mobile]
      ,[order_status]
      ,[order_create_time]
      ,[order_paied_time]
      ,[order_cancel_time]
      ,[order_delivery_time]
      ,[create_time]
      ,[update_time])
SELECT 1 [customer_type], t1.workshop_id, t.create_time,
	t1.order_no, p.sku, p.product_unit,ol.total_price,ol.amount [sales_quantity],
	ol.amount*convert(float, p1.capacity) [sales_liters],
	isnull(t2.excute_user_id, (select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
		where s.org_id=t2.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD') order by s.xg_sj desc)) [saler_code], 
	t1.technician_name [order_creator], t1.technician_mobile, 100 [order_status], t1.create_time [order_create_time],
	null [order_paied_time], null [order_cancel_time], t.update_time [order_delivery_time],getdate(),getdate()
				FROM wx_t_db2b_order t1
				left join wx_t_db2b_order_line ol on ol.order_id=t1.id
				left join wx_t_db2b_product p on p.id=ol.product_id
				left join wx_t_product p1 on p1.sku=p.sku
					LEFT JOIN wx_t_work_shop t2 ON t1.workshop_id = t2.id
					LEFT JOIN wx_t_organization t3 ON t1.partner_id = t3.id
					LEFT JOIN wx_t_order t
					ON t1.order_no = substring(t.source, charindex('-', t.source) + 1, len(t.source) - charindex('-', t.source))
						AND t.source_id LIKE '%_B2B%'
				WHERE t1.delete_flag = 0 and t.status = 11 AND t.partner_confirm_type = 1 and t1.partner_id!=9*/

SELECT TOP (1000) st.sales_quantity*convert(float, tp.capacity),*
	  --update st set st.sales_liters=st.sales_quantity*convert(float, tp.capacity)
  FROM [PP_MID].[dbo].[pp_sell_through] st 
  LEFT JOIN wx_t_product tp ON st.[product_code] = tp.sku
  where [sales_liters]=0
  
update dc set dc.dsr_code=
(select top 1 u.user_id from wx_t_user u
  left join wx_t_organization o on o.id=u.org_id
  where (u.type is null or u.type !='1') and o.type=1 and u.status='1' and u.org_id=dc.partner_id order by u.user_id)
  FROM [PP_MID].[dbo].[pp_distributor_customer20201112] dc
  left join wx_t_work_shop w on dc.customer_id=w.id
   where [dsr_code] is null  
   
update w set w.excute_user_id= (select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc)--,* 
				from wx_t_work_shop w 
where excute_user_id=1   

	  update c set c.dsr_code=(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=c.customer_id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc)
  FROM [PP_MID].[dbo].[pp_distributor_customer20201112] c 
  where not exists (select 1 from [PP_MID].[dbo].[pp_user] u where u.user_code=c.dsr_code)
  

select 
(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI','TT_2_MR',
				'TT_2_CDMCLZX','TT_2_XD_CAI','TT_2_CDYJZM','TT_2_FLEET','TT_2_FLEET_ZF','TT_2_GC_JX',
				'TT_2_PROJECT_FLEET','TT_2_PROJECT_CLIENT','TT_2_PROJECT_CLIENT_VISIT','TT_2_PROJECT_CLIENT_YJZM',
				'TT_2_FEELING_GOODS')
				and exists (select 1 from wx_t_user u where u.user_id=m.excute_user_id and u.status=1) order by s.xg_sj desc),
				w.*
update w set w.excute_user_id=(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI','TT_2_MR',
				'TT_2_CDMCLZX','TT_2_XD_CAI','TT_2_CDYJZM','TT_2_FLEET','TT_2_FLEET_ZF','TT_2_GC_JX',
				'TT_2_PROJECT_FLEET','TT_2_PROJECT_CLIENT','TT_2_PROJECT_CLIENT_VISIT','TT_2_PROJECT_CLIENT_YJZM',
				'TT_2_FEELING_GOODS')
				and exists (select 1 from wx_t_user u where u.user_id=m.excute_user_id and u.status=1) order by s.xg_sj desc) 				
/*update w set w.excute_user_id= (select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc)--,* 
*/				from wx_t_work_shop w 
where --excute_user_id is null 
not exists (select 1 from wx_t_user u where u.user_id=w.excute_user_id and u.status=1)
--and w.status=3 and w.delete_flag=0
and w.excute_user_id!=(select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI','TT_2_MR',
				'TT_2_CDMCLZX','TT_2_XD_CAI','TT_2_CDYJZM','TT_2_FLEET','TT_2_FLEET_ZF','TT_2_GC_JX',
				'TT_2_PROJECT_FLEET','TT_2_PROJECT_CLIENT','TT_2_PROJECT_CLIENT_VISIT','TT_2_PROJECT_CLIENT_YJZM',
				'TT_2_FEELING_GOODS')
				and exists (select 1 from wx_t_user u where u.user_id=m.excute_user_id and u.status=1) order by s.xg_sj desc)
				

select 
(select top 1 u.user_id from wx_t_user u
  left join wx_t_organization o on o.id=u.org_id
  where (u.type is null or u.type !='1') and o.type=1 and u.status='1' and u.org_id=w.partner_id
  and exists (select 1 from wx_t_userrole ur
left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id
and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')) order by u.user_id)
,w.* 
update w set w.excute_user_id=(select top 1 u.user_id from wx_t_user u
  left join wx_t_organization o on o.id=u.org_id
  where (u.type is null or u.type !='1') and o.type=1 and u.status='1' and u.org_id=w.partner_id
  and exists (select 1 from wx_t_userrole ur
left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id
and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')) order by u.user_id)			
/*update w set w.excute_user_id= (select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
				where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc)--,* 
*/				from wx_t_work_shop w 
where --excute_user_id is null 
not exists (select 1 from wx_t_user u where u.user_id=w.excute_user_id and u.status=1)
--and w.status=3 and w.delete_flag=0
and (select top 1 u.user_id from wx_t_user u
  left join wx_t_organization o on o.id=u.org_id
  where (u.type is null or u.type !='1') and o.type=1 and u.status='1' and u.org_id=w.partner_id
  and exists (select 1 from wx_t_userrole ur
left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id
and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')) order by u.user_id) is not null
				
select top 100 (case when customer_type=4 then '3' when customer_type=8 then '4' else convert(nvarchar(10),customer_type) end) + '2020120715' + RIGHT('0000000'+convert(nvarchar(10), id),8),* 
--update w set w.customer_code=(case when customer_type=4 then '3' when customer_type=8 then '4' else convert(nvarchar(10),customer_type) end) + '2020120715' + RIGHT('0000000'+convert(nvarchar(10), id),8)
from wx_t_work_shop w where len(customer_code)<18
order by id desc