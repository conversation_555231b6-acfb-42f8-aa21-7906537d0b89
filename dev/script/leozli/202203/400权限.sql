insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'index_forward', 1, 1, '首页跳转我的主页配置。1-非经销商', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'ChevronHomePage', 1, 1, '首页报表。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'home_page_v2', 2, 1, '首页报表。2-ABM', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'point.exchange.type.permission', 2, 1, '积分及下单礼品促销兑换。2-查看所有积分类型', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'Workshop.editPage', 8, 1, '终端客户管理。8-门店管理删除权限', 1, getdate());

update u set cai=null
from wx_t_user u join wx_t_organization o on o.id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where u.status=1 and o.status=1 and u.user_id!=1
and r.ch_role_name in ('Chevron_Customer_Service')
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50357 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'WorkflowInstance.todoPage', -1, 1, '公共流程申请权限。-1 - 全部权限', 1, getdate());

insert into wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag,remark, create_user_id, create_time)
select distinct r1.role_id, p.module_code, p.permission_weight, 1, p.remark, 1, getdate() 
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
left join wx_t_role r1 on r1.ch_role_name='Chevron_Customer_Service'
where r.ch_role_name='Chevron_CDM_Channel_Manager'
and p.module_code='WorkflowInstance.allPage'
and not exists (select 1 from wx_t_operation_permission p1 where p1.module_code=p.module_code and p1.role_id=r1.role_id
and p1.permission_weight=p.permission_weight)

insert into wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag,remark, create_user_id, create_time)
select distinct r1.role_id, p.module_code, p.permission_weight, 1, p.remark, 1, getdate() 
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
left join wx_t_role r1 on r1.ch_role_name='Chevron_Customer_Service'
where r.ch_role_name='Chevron_Material_Admin'
and p.module_code='MATERIAL_2021_WEIGHT'
and not exists (select 1 from wx_t_operation_permission p1 where p1.module_code=p.module_code and p1.role_id=r1.role_id
and p1.permission_weight=p.permission_weight)

insert into wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag,remark, create_user_id, create_time)
select distinct r1.role_id, p.module_code, p.permission_weight, 1, p.remark, 1, getdate() 
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
left join wx_t_role r1 on r1.ch_role_name='Chevron_Customer_Service'
where r.ch_role_name='Chevron_SOP_Manager'
and p.module_code='qbr.sop.permission'
and not exists (select 1 from wx_t_operation_permission p1 where p1.module_code=p.module_code and p1.role_id=r1.role_id
and p1.permission_weight=p.permission_weight)

insert into wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag,remark, create_user_id, create_time)
select distinct r1.role_id, p.module_code, p.permission_weight, 1, p.remark, 1, getdate() 
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
left join wx_t_role r1 on r1.ch_role_name='Chevron_Customer_Service'
where r.ch_role_name='Chevron_SOP_Manager'
and p.module_code='ctrl.indirect.dealer'
and not exists (select 1 from wx_t_operation_permission p1 where p1.module_code=p.module_code and p1.role_id=r1.role_id
and p1.permission_weight=p.permission_weight)

insert into wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag,remark, create_user_id, create_time)
select distinct r1.role_id, p.module_code, p.permission_weight, 1, p.remark, 1, getdate() 
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
left join wx_t_role r1 on r1.ch_role_name='Chevron_Customer_Service'
where r.ch_role_name='Chevron_CDM_Channel_Manager'
and p.module_code='FLEEING.GOODS'
and not exists (select 1 from wx_t_operation_permission p1 where p1.module_code=p.module_code and p1.role_id=r1.role_id
and p1.permission_weight=p.permission_weight)


insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50364 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50236 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50373 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50373 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50094 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50094 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50355 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50358 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)


insert into wx_t_subject_res_permission (subject_type, subject_id, resource_id,permission_type_id,creation_time,created_by) values ('ROLE',(select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Customer_Service'),(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='促销活动奖励查看' and m.module_name='市场支持'),(select permission_type_id from wx_t_res_permission_type where permission_type_desc='所有合伙人' and resource_type_code='SP_FULL_RETAILER'),getdate(),1)

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'PartnerPage', 1, 1, '合伙人管理。1-取消合作权限', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'Credit.apply', -1, 1, '信用申请。', 1, getdate());
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)	
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_pid, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Chevron_Customer_Service' and m.menu_id=50259 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_pid)
