package com.sys.quartz.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.sellin.business.SellInPromotionBizService;
import com.sys.log.util.LogUtils;

public class SellInSentInfoToPPTask {

	@Autowired
	private SellInPromotionBizService sellInPromotionBizService;
	
	private static Logger log = LoggerFactory.getLogger(SellInSentInfoToPPTask.class);
	public synchronized void execute(){
		try {
			sellInPromotionBizService.synToBi();
			log.info(" 同步促销规则到pp中间表 success." );
		} catch (Exception e) {
			log.info(" 同步促销规则到pp中间表 failed." );
			e.printStackTrace();
			LogUtils.addErrorLog(1l, "com.sys.quartz.job.SellInSentInfoToPPTask.execute", "同步促销规则到pp中间表失败。" + e.getMessage(), null);
		}
	}
	
}
