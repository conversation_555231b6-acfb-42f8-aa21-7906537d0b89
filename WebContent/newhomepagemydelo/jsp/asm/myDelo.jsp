<%@page import="com.common.util.StringUtils"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import= "com.common.util.ContextUtil"%>
<%@page import= "com.sys.auth.model.WxTUser"%>
<%@page import="com.sys.auth.model.WxTRole"%>
<%@page import="java.util.List"%>
<%@page import="java.util.Calendar"%>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.lang.Boolean" %>
<%@page import="com.sys.auth.controller.ConfigIndexPage"%>
<%
	int year = 0;
	int currentMonth = 12;
	Calendar cal = Calendar.getInstance();
	int currentYear = cal.get(Calendar.YEAR);
	if(StringUtils.isNotBlank(request.getParameter("year"))){
		year = Integer.parseInt(request.getParameter("year"));
		if(year == currentYear){
			currentMonth = cal.get(Calendar.MONTH) + 1;
		}
	}else{
		year = cal.get(Calendar.YEAR);
		currentMonth = cal.get(Calendar.MONTH) + 1;
	}
	int lastYear = year - 1;
    WxTUser curUser = ContextUtil.getCurUser();
    Long userId = curUser.getUserId();
    int isAsmAndAbove = 0;
    boolean isAsm = false;
    boolean isChannelManager = false;
    int roleWeight = 1;
    String roleType = "";
    String distributorId = null;
    String customerCategory = "Commercial";
    if (WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel())) {
        // 如果是chevron的user model

        List<WxTRole> roleList = curUser.getRoleList();
         for(WxTRole role : roleList) {
             boolean bChannelManager = role.getChRoleName().equals("Chevron_C&I_Channel_Manager");
             boolean bSuppervisorManager = role.getChRoleName().equals("Chevron_C&I_Suppervisor");
            if(bSuppervisorManager || bChannelManager) {
                if(bChannelManager) {
                    isChannelManager = true;
                    roleWeight = roleWeight | 4;
                }else if(bSuppervisorManager) {
                    isAsm = true;
                    roleWeight = roleWeight | 2;
                }
                isAsmAndAbove = 1;
            }
            
            if(role.getChRoleName().equals("Chevron_CDM_Suppervisor") || role.getChRoleName().equals("Chevron_C&I_Suppervisor")){
            	roleType = "asm";
            }else if(role.getChRoleName().equals("Chevron_BD") || role.getChRoleName().equals("Chevron_Promote_Sales")){
            	roleType = "flsr";
            }else {
            	roleType = "admin";
            }
        }
    }
    String higherReceivedDivClass = "higherReceivedDiv";
	
    boolean isMenuPermitted = false;
    isMenuPermitted = ConfigIndexPage.isMenuPermitted("/dms/CustomersaleReport.jsp", userId);
%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>我的金富力</title>
    <%@include file="/common/jsp/common.jsp" %>
    <link href="${ctx }newhomepage/css/myCdm.css?v=${version}10" rel="stylesheet">
    <link href="${ctx}newhomepage/tooltipster/css/tooltipster.bundle.css" rel="stylesheet">
    <script type="text/javascript" src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
    <script type="text/javascript">
	    var currentYear = <%=year%>;
	    var lastYear = <%=lastYear%>;
	    var roleType = '<%=roleType%>';
	    var currentMonth = <%=currentMonth%>;
        var currentQuarter = parseInt((currentMonth + 2) / 3);
        var G_isAsmAndAbove = <%=isAsmAndAbove%>;
        var isMenuPermitted = <%=isMenuPermitted%>;
        var roleWeight = <%=roleWeight %>;
        var loginCai = '<%=curUser.getCai() %>';
        var customerCategory = '<%=customerCategory%>';
        var distributorId = <%=distributorId%>;
    </script>
</head>
<c:set var="isAsmAndAbove" value="<%=isAsmAndAbove%>"/>
<c:set var="isChannelManager" value="<%=isChannelManager%>"/>
<c:set var="isAsm" value="<%=isAsm%>"/>
<c:set var="higherReceivedDivClass" value="<%=higherReceivedDivClass%>"/>
<body class="gray-bg">
<div class="not-top">
    <div class="row">
        <div class="block-col">
            <div class="content-box height-content">
                <div class="content-panel block-header">
                    <i class="fa fa-group"></i>
                    <c:if test="${isAsmAndAbove eq 0}" ><span>本区域内排名</span></c:if>
                    <c:if test="${isAsmAndAbove eq 1}" ><span>排名</span></c:if>
                </div>
                <div class="content-panel" id="rank_info">
                    <div id="cur_year" class="block-col rank_info-sub-div" style="height: 190px;">
                            <p class="year-title"><%=year%>年</p>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="cur-month-sales-rank">销量YTD 本月排名:<br/><span class="rank-value"></span></p>
                            <p class="pre-month-sales-rank">上月排名:<br/><span class="rank-value"></span></p>
                        </div>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="cur-month-gross-rank">毛利YTD 本月排名:<br/><span class="rank-value"></span></p>
                            <p class="pre-month-gross-rank">上月排名:<br/><span class="rank-value"></span></p>
                        </div>
                    </div>
                    <div id="pre_year" class="block-col rank_info-sub-div" style="display:none">
                    <p class="year-title"><%=lastYear%>年</p>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="cur-month-sales-rank">销量YTD 本月排名:<span class="rank-value"></span></p>
                            <p class="cur-month-gross-rank">毛利YTD 本月排名: <span class="rank-value"></span></p>
                        </div>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="pre-month-sales-rank">上月排名:<span class="rank-value"></span></p>
                            <p class="pre-month-gross-rank">上月排名:<span class="rank-value"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box height-content">
                <div class="content-panel block-header">
                    <i class="fa fa-bar-chart"></i><span>Sales Target</span>                  
                </div>
                <div class="content-panel" id="sales_target">
                    <div id="sell-in-info" class="block-col sales_target-pie-div" style="padding-right: 0;padding-left: 0;">
                    </div>
                    <div id="gross-info" class="block-col sales_target-pie-div"  style="padding-right: 0;padding-left: 0;">
                    </div>
                </div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box height-content" >
                <div class="content-panel block-header">
                    <i class="fa fa-commenting"></i><span>待办事项及快捷入口</span>                  
                </div>
                <div class="content-panel" id="todo-div">
                    <div>
                         <ul class="todo-list" style="width: calc(50% - 35px); margin-right: 0;float: left;">
                         	<c:if test="${isAsmAndAbove eq 0}">
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}forecastingworksheet/salesforcast.do')">销售预测</li>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">德乐店招申请</li>
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyPlanTodoInfo}}</span>&nbsp;份销售月计划待提交</li>
                             	<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyReportInfo}}</span>&nbsp;份综合月报告待提交</li>
                             	<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}promotev2/jsp/promoteNewActivityApplyPage.jsp')">2020市场资源包申请</li>
                             	<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/oil-sample/index.jsp')">油样申请</li> 
                            </c:if>
                         	<c:if test="${isAsm}">
                                <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}forecastingworksheet/salesforcast.do')">销售预测</li>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}evaluationprogram/index.do')">销售绩效考核审核</li>                         		
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyPlanTodoInfo}}</span>&nbsp;份销售月计划待提交</li>   
                                <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}promotev2/jsp/promoteNewActivityApplyPage.jsp')">2020市场资源包审批</li>
                                <li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/oil-sample/index.jsp')">油样申请审核</li> 
                             </c:if>
                             <c:if test="${isChannelManager}">
                                <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}evaluationprogram/index.do')">销售绩效考核审核</li>   
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}promote/jsp/promoteDistributePlanApprovePage2.jsp')">德乐市场资源分配</li>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}promote/jsp/promoteActivityApplyPage.jsp')">德乐市场资源审批</li>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}promotev2/jsp/promoteNewActivityApplyPage.jsp')">2020市场资源包审批</li>
                         		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/oil-sample/index.jsp')">油样申请审核</li> 
                             </c:if>
                             <li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}credit_app/index.jsp')">{{todoInfo.creditPendingCount}}</span>&nbsp;条&nbsp;信用申请待审批</li>                       
                             <%-- <c:if test="${isAsmAndAbove eq 0}">
                             <li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyPlanTodoInfo}}</span>&nbsp;份销售月计划待提交</li>
                             <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyReportInfo}}</span>&nbsp;份综合月报告待提交</li>
                                  <li><span class="num-span"
                                            onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.flsrExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核
                                  </li>
                             </c:if>
                             <c:if test="${isAsm}">
                                 <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateListPage.jsp')">{{todoInfo.asmExpensePendingReviewCount}}</span>&nbsp;条精英计划基金花费申请待审核</li>
                                 <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.asmExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>
                             </c:if>
                              <c:if test="${isChannelManager}">
                                  <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.cmExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>
                              </c:if>
                              <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}credit_app/index.jsp')">{{todoInfo.creditPendingCount}}</span>&nbsp;条&nbsp;信用申请待审批</li> --%>
                         </ul>
                        <ul class="todo-list" style="width: calc(50% - 35px);margin-right: 0;float: left;">
                            <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}QBR/index.jsp#/qbr/review')">QBR</li>
                            <c:if test="${isAsmAndAbove eq 0}">
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">{{todoInfo.storeMktAuditTodoCount}}</span>&nbsp;条德乐店招待审核</li>
                         		<%-- <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">德乐店招审核</li> --%>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}sys/utils/publishPhotoPage.jsp')">经销商高清图下载</li>                       	
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}elites2/rebate/jsp/rebateListPage.jsp')">2019精英计划基花费申请</li>   
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.flsrExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>                       	                        	
                         		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/dsr/index.jsp#/kpi/cio')">DSR绩效审核</li>                       	                        	
              	           		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/dsr/index.jsp#/points/cio')">DSR积分查看</li>
                         	</c:if>
                         	<c:if test="${isAsm}">
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">{{todoInfo.storeMktAuditTodoCount}}</span>&nbsp;条德乐店招待审核</li>
                         		<%-- <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">德乐店招审核</li> --%>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}sys/utils/publishPhotoPage.jsp')">经销商高清图下载</li>                       	
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}elites2/rebate/jsp/rebateListPage.jsp')">{{todoInfo.flsrExchangePendingReviewCount}}</span>&nbsp;条精英计划基金花费申请待审核</li>    
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.flsrExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>                    	                        	                       	
                         		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/dsr/index.jsp#/kpi/cio')">DSR绩效审核</li>                       	                        	
              	           		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/dsr/index.jsp#/points/cio')">DSR积分查看</li>        
                         	</c:if>
                         	<c:if test="${isChannelManager}">
                                <li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">{{todoInfo.storeMktAuditTodoCount}}</span>&nbsp;条德乐店招待审核</li>
                         		<%-- <li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cio/list')">德乐店招审核</li> --%>
                         		<li style="cursor: pointer" onclick="myDeloFunc.openMenu('${ctx}sys/utils/publishPhotoPage.jsp')">经销商高清图下载</li>                       	
                         		<li><span class="num-span" onclick="myDeloFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.flsrExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>  
                         		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/dsr/index.jsp#/kpi/cio')">DSR绩效审核</li>                       	                        	
              	           		<li style="cursor: pointer"  onclick="myDeloFunc.openMenu('${ctx}SPA/dsr/index.jsp#/points/cio')">DSR积分查看</li>
                             </c:if>
                            <%-- <c:if test="${isAsmAndAbove eq 0}">
                            </c:if>
                            <c:if test="${isAsmAndAbove eq 1}">
                                <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}evaluationprogram/index.do')">销售绩效考核审核</li>
                            </c:if>
                            <li style="cursor: pointer;" class="dmsURL" onclick="myCdmFunc.openMenu('/dms/CustomersaleReport.jsp')">客户及销售业绩统计分析</li>
                            <c:if test="${isAsmAndAbove eq 0}">
                                <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateListPage.jsp')">
                                    2019精英计划基金花费申请
                                </li>
                            </c:if> --%>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="not-top">
    <div class="row">
        <div class="block-col">
            <div class="content-box  ${higherReceivedDivClass}">
                <div class="content-panel block-header">
                    <i class="fa fa-star"></i>
                    <span><%=year%>年所负责合伙人累计获取支持</span>
                </div>
                <div class="supports-received">
                    <div class="row">
                        <div id="cioMarketingFund-div" class="block-col four-div" style="margin-right: 6px;height: 90px;">
                            <p><span class="text-span">基金金额</span></p>
                            <p><span class="value-span" onclick="myDeloFunc.openMenu('${ctx }elites2/rebate/jsp/rebateAuditListPage.jsp')"></span>
                                <span>元</span></p>
                        </div>
                        <div id="caltexPoint-div" class="block-col four-div" style="margin-left: 6px;margin-right: 6px;height: 90px;">
                            <p><span class="text-span">进货积分</span></p>
                            <p><span class="value-span" onclick="myDeloFunc.openMenu('${ctx }material/dealer/toGotoPointpage.do?mid=50171')"></span>
                                <span>元</span>                              
                            </p>
                        </div>
                        <div id="cioPosm-div" class="block-col four-div" style="margin-left: 6px;margin-right: 6px;height: 90px;">
                            <p><span class="text-span">POSM</span></p>
                            <p><span class="value-span">0</span>
                                <span>元</span>                               
                            </p>
                        </div>
                        <div id="cioPromotion-div" class="block-col four-div" style="margin-left: 6px; margin-right: 6px;height: 90px;">
                            <p><span class="text-span">促销支持</span></p>
                            <p><span class="value-span">0</span>
                                <span>元</span>                             
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div id="cioSignage-div" class="block-col three-div" style="margin-right: 6px;">
                            <p><span class="text-span">店招</span></p>
							<p><span class="value-span">0</span>
                                <span>元</span>                             
                            </p>
                        </div>
                        <div id="cioResourcePackage-div" class="block-col three-div" style="margin-left: 6px;margin-right: 6px;">
                            <p><span class="text-span">资源包</span></p>                           
                            <p><span class="value-span" onclick="myDeloFunc.openMenu('${ctx}promote/jsp/promoteActivityApplyPage.jsp')"></span>
                            	<span>元</span>          
                            </p>
                        </div>
                        <div id="cioPilot-div" class="block-col three-div" style="margin-left: 6px;">
                            <p><span class="text-span">德乐计划</span></p>
                            <p><span class="value-span">0</span>
                                <span>元</span>                             
                            </p>
                        </div>
                    </div>
                  <!--    <div class="content-panel grid-desc" > -->
				<!--  	<p style="color:red;">由于线下数据收集同步滞后，其中POSM,促销支持和店招只更新到2019年4月底的数据。</p>-->
				<!--  </div>-->
                </div>
            	
            </div>
        </div>
        <div class="block-col">
            <div class="content-box ${higherReceivedDivClass}">
                <div class="content-panel block-header">
                    <i class="fa fa-commenting"></i><span>系统公告</span>
                    <a class="more-btn" href="javascript: void(0);"
                       onclick="myCdmFunc.openUrl('${ctx }sys/push/personalMessagePage.jsp', 'mymessage', '我的消息')">更多...</a>
                </div>
                <div class="content-panel" id="sys_message"></div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box  ${higherReceivedDivClass}">
                <div class="content-panel block-header">
                    <i class="fa fa-question-circle"></i><span>系统支持</span>
                </div>
                <div class="content-panel support-item"><i class="fa fa-file-o"></i>
                    <a href="javascript: void(0);"
                       onclick="myCdmFunc.openUrl('${ctx }operationManual.jsp', 'documentDownload', '文档下载');"
                       style="font-weight: 700;">
                        <spring:message code="Click"/></a> <spring:message code="Open_the_document_support_page"/></div>
                <div class="content-panel support-item">
                    <i class="fa fa-phone"></i>技术服务热线（系统报错及操作）：
                    <a href="tel:************">************</a>
                </div>
                <div class="content-panel support-item">
                    <i class="fa fa-phone"></i>积分服务热线（礼品及订单咨询）：
                    <a href="tel:************">************</a>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="content-panel" style="padding-left: 15px;padding-right: 15px;background: transparent;">
    <div id="perf_grid"  style="width: calc(100%);margin-bottom: 15px;">

    </div>
</div>
		<div class="subject-indexes-wrapper1">
			<ul>
				<li onclick="myDeloFunc.openMenu('myDelo/index.do');" class='<%=currentYear == year ? "active" : "" %>'>
					<%=currentYear %> 我的德乐
				</li>
				<li onclick="myDeloFunc.openMenu('myDelo/index.do', '/myDelo/index.do?year=<%=currentYear - 1 %>', '');" class='<%=currentYear - 1 == year ? "active" : "" %>'>
					<%=currentYear - 1 %> 我的德乐
				</li>
			</ul>
		</div>
</body>
<script type="text/javascript" src="${ctx}newhomepagemydelo/tooltipster/js/tooltipster.bundle.min.js"></script>
<script type="text/javascript" src="/common/js/vue.js"></script>
<script type="text/javascript" src="${ctx}newhomepagemydelo/js/myDelo.js?v=${version}10"></script>
</html>