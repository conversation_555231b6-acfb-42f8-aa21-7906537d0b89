var foo = new $.JsonRpcClient({
    ajaxUrl: '/wxPublicRpc.do'
});

var detailDialog;
var grid = null;
var gridDetail = null;

$(document).ready(function () {
	buildGrid();
	initQueryCtrl();
});


function initQueryCtrl(){
    if(userType == 1){
        $('#partnerCondWrapper').show();
        var partnerCtrl = Ctrls.PartnerAutoSelect.init('#partnerId', {
            placeholder: '全部',
            params: {resourceId: 'task'},
            onchange: function(value){
            	refreshRebateGrid(value);
            }
        });
    }
}

//返利总金额查看
var reBatestore = null;
function buildGrid(partnerId) {
    $('#grid').html("");
    BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
        var columns = [
            {
                title: '序号', dataIndex: 'partnerId', width: 65, renderer: function (value, obj, index) {
                    return index + 1;
                }
            },
            { title: '合伙人', dataIndex: 'partnerName', width: '25%' },
            { title: '截止当前已获得返利金额(元)', dataIndex: 'totalAmount', width: '25%',summary: true,
                      renderer: function (value, obj, index) {
                            return "<a href='javascript:void(0);' class='updateBtn' onclick='showGridDetial(" + obj.partnerId + ")'>"+formatMoney(obj.totalAmount,2)+"</a>";
                      }
            
            },
            { title: '截止当前已申请金额(元)', dataIndex: 'applyFund', width: '25%', summary: true,
                      renderer: function (value, obj, index) {
                            return ""+formatMoney(value,2)+"";
                      }
            },
            { title: '截止当前已审批金额(元)', dataIndex: 'usedAmount', width: '25%', summary: true,
                      renderer: function (value, obj, index) {
                            return ""+formatMoney(value,2)+"";
                      }
            }
            
        ];
        reBatestore = new Data.Store({
        	url: '/partnerRebateServ/getPartnerTotalRebateAmountInfoByConditions.do',
			autoLoad:true,
			totalProperty : 'totalRecord',
			root: 'resultLst',
			remoteSort: true,
			sortField: 'partnerName',
			sortDirection: 'DESC',
			pageSize:10,
		    proxy : {
				method : 'post'
			}
        });
        var sumPlugin = new Grid.Plugins.Summary({
			summaryTitle: '总合计'
			});
        grid = new Grid.Grid({
            render: '#grid',
            width: window.rightWidth,
            columns: columns,
            idField: 'partnerName',
            store: reBatestore,
            plugins: [sumPlugin],
            bbar: {
                pagingBar: true
            }
        });
        grid.render();
        common.initPageSizeBar(grid);
    });

}
function refreshRebateGrid(partnerId) {
	reBatestore.load({
		partnerId: partnerId,
		start: 0
	});
}
//返利明细
var isChevronChevronCdmChannelManager = null;
function showGridDetial(partnerId) {
    $('#detailGrid').empty();
    foo.call('partnerRebateService.getPartnerTotalRebateAmountDetail', [partnerId],
        function (result) {
            if (result.code == "success") {
                dialogDetail(result.resultLst);
                isChevronChevronCdmChannelManager = result.isChevronChevronCdmChannelManager;
                if (detailDialog == null) {
                    BUI.use('bui/overlay', function (Overlay) {
                        detailDialog = new Overlay.Dialog({
                            title: "查看返利明细",
                            width: 800,
                            height: 400,
                            contentId: 'detailDialog',
                            buttons: [

                                {
                                    text: '关闭',
                                    elCls: 'button btn-close',
                                    handler: function () {
                                        this.close();
                                    }
                                }]
                        });
                    });
                }
                detailDialog.show();
            } else {
                common.alertMes('拉取数据失败', 'error');
            }
        })


}


function dialogDetail(result) {
    var lst = result;
    BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
        var columns = [
            {
                title: '序号', dataIndex: 'index', width: '65', renderer: function (value, obj, index) {
                    return index + 1;
                }
            },
            {	 title: '月份', dataIndex: 'transType', width: '20%',renderer: function (value, obj, index) {
            	var month = parseInt(obj.monthStr);
        		var monthStr = obj.monthStr; 
        		if(month < 10){
        			monthStr = '0' + month;
        		}
        		var yearMonthStr = obj.yearStr + '-' + monthStr;
            	return yearMonthStr;
            	}	
            },
            { title: '返利金额', dataIndex: 'transAmount', width: '25%',
                      renderer: function (value, obj, index) {
                          return ""+formatMoney(value,2)+"";
                     }
            },
            /*{ title: '返利时间', dataIndex: 'transTime', width: '25%', renderer: function (value, obj, index) {
                    return formatTimeMillis(obj.transTime);
                }
            },*/
            { title: '状态', dataIndex: 'status', width: '30%', renderer: function (value, obj, index) {
	                if(obj.status == null || obj.status == '1'){
	                	return "区域经理已确认";
	                }else if(obj.status == '2'){
	                	return "渠道经理已确认";
	                }
	            }
	        },
            { title: '备注', dataIndex: 'transRemark', width: '20%' },
            {
                title: '操作',
                dataIndex: 'status',
                sortable: false,
                 width: '100px',
            	renderer : function(value, obj, index)
	            {
            		var month = parseInt(obj.monthStr);
            		var monthStr = obj.monthStr; 
            		if(month < 10){
            			monthStr = '0' + month;
            		}
            		var yearMonthStr = obj.yearStr + '-' + monthStr;
	                /*var editRebate = "<a href='javascript:void(0);' class='updateBtn' onclick='editPartnerRebateDetail(" + obj.partnerId + ",\"" + yearMonthStr + "\",\"" + obj.partnerName + "\")'>返利修改</a>";  
            		if(isChevronChevronCdmChannelManager){
            			return editRebate;
            		}else{
            			return "<a href='javascript:void(0);' class='updateBtn' onclick='partnerRebateDetail(" + obj.partnerId + ",\"" + yearMonthStr + "\",\"" + obj.partnerName + "\")'>返利详情</a>";  
            		}	*/            				            
            		return "<a href='javascript:void(0);' class='updateBtn' onclick='partnerRebateDetail(" + obj.partnerId + ",\"" + yearMonthStr + "\",\"" + obj.partnerName + "\")'>返利详情</a>";  
	            }
            }

        ];
        var store = new Data.Store({
            data: lst,
            autoLoad: true,
            pageSize: 5
        });
        var editing = new Grid.Plugins.CellEditing();
        gridDetail = new Grid.Grid({
            render: '#detailGrid',
            width: window.rightWidth,
            columns: columns,
            idField: 'detailGrid',
            store: store,
            bbar: {
                pagingBar: true
            }
        });
        gridDetail.render();
    });

}
function editPartnerRebateDetail(partnerId, yearMonthStr,partnerName) {
	openMenu("business/partnerorder/partnerRebateManagement.jsp","/business/partnerorder/partnerRebateManagement.jsp?partnerId=" + partnerId + "&yearMonthStr=" + yearMonthStr + "&partnerName=" + partnerName);
}
function partnerRebateDetail(partnerId, yearMonthStr,partnerName) {
	openMenu("business/partnerorder/partnerRebate.jsp","/business/partnerorder/partnerRebate.jsp?partnerId=" + partnerId + "&yearMonthStr=" + yearMonthStr + "&partnerName=" + partnerName);
}
function openMenu(urlMenu,urlDetail){
	if(top.openMenuTab){
		top.openMenuTab(urlMenu,urlDetail);
	}else{
		window.open(urlDetail);
	}
}


function query() {
    var partnerId = $("#partnerId").val();
    refreshRebateGrid(partnerId);

}

function formatTimeMillis(timeMillis) {
    var date = new Date(timeMillis);
    var monthOfDay = formatCurDay(date.getMonth() + 1);
    var dateOfDay = formatCurDay(date.getDate());
    var hourofDay = formatCurDay(date.getHours());
    var mituteofDay = formatCurDay(date.getMinutes());
    var dateFormat = date.getFullYear() + "-" + monthOfDay + "-" + dateOfDay + " " + hourofDay + ":" + mituteofDay;
    return dateFormat;
}

function formatCurDay(formatPara) {
    if (formatPara < 10) {
        return "0" + formatPara;
    } else {
        return formatPara;
    }
}

function final_formatTimeToMillis(time, startFlag) {
    var dateTime = time.split(" ");
    var dateArray = dateTime[0].split("-");
    var date;
    if (startFlag) {
        date = new Date(dateArray[0], dateArray[1] - 1, dateArray[2]);
    } else {
        date = new Date(dateArray[0], dateArray[1] - 1, dateArray[2], 23, 59, 59);
    }
    return date.getTime();
}

function final_formatCurDay(formatPara) {
    if (formatPara < 10) {
        return "0" + formatPara;
    } else {
        return formatPara;
    }
}


/* 
     * formatMoney(s,type) 
     * 功能：金额按千位逗号分隔，负号用括号
     * 参数：s，需要格式化的金额数值. 
     * 参数：type,判断格式化后的金额是否需要小数位. 
     * 返回：返回格式化后的数值字符串. 
     */ 
function formatMoney(s, type) {
        var result = s;
        if (s < 0)
            s = 0 - s;
        if (/[^0-9\.]/.test(s))
            return "0.00";
        if (s == null || s == "null" || s == "")
            return "0.00";
        if (type > 0)
            s = new Number(s).toFixed(type);
        s = s.toString().replace(/^(\d*)$/, "$1.");
        s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
        s = s.replace(".", ",");
        var re = /(\d)(\d{3},)/;
        while (re.test(s))
            s = s.replace(re, "$1,$2");
        s = s.replace(/,(\d\d)$/, ".$1");
        if (type == 0) {
            var a = s.split(".");
            if (a[1] == "00") {
                s = a[0];
            }
        }
        if (result < 0)
            result = "(" + s + ")";
        else
            result = s;
        return result;
}

