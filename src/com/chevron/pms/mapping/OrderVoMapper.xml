<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.OrderVoMapper">
	<resultMap id="BaseResultMap" type="com.chevron.pms.model.OrderVo">
		<id column="id" property="id" jdbcType="BIGINT" />
	    <result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
	    <result column="order_type" property="orderType" jdbcType="NVARCHAR" />
	    <result column="buy_user_no" property="buyUserNo" jdbcType="BIGINT" />
	    <result column="buy_user_name" property="buyUserName" jdbcType="NVARCHAR" />
	    <result column="buyer_card_id" property="buyerCardId" jdbcType="NVARCHAR" />
	    <result column="invoice_info" property="invoiceInfo" jdbcType="NVARCHAR" />
	    <result column="source" property="source" jdbcType="NVARCHAR" />
	    <result column="source_id" property="sourceId" jdbcType="NVARCHAR" />
	    <result column="pay_type" property="payType" jdbcType="NVARCHAR" />
	    <result column="pay_state" property="payState" jdbcType="NVARCHAR" />
	    <result column="total_product_price" property="totalProductPrice" jdbcType="NUMERIC" />
	    <result column="total_delivery_price" property="totalDeliveryPrice" jdbcType="NUMERIC" />
	    <result column="total_order_price" property="totalOrderPrice" jdbcType="NUMERIC" />
	    <result column="total_product_prefer_price" property="totalProductPreferPrice" jdbcType="NUMERIC" />
	    <result column="total_delivery_prefer_price" property="totalDeliveryPreferPrice" jdbcType="NUMERIC" />
	    <result column="total_order_prefer_price" property="totalOrderPreferPrice" jdbcType="NUMERIC" />
	    <result column="total_order_pay_price" property="totalOrderPayPrice" jdbcType="NUMERIC" />
	    <result column="total_order_need_pay_price" property="totalOrderNeedPayPrice" jdbcType="NUMERIC" />
	    <result column="work_shop_id" property="workShopId" jdbcType="BIGINT" />
	    <result column="reference_order_no" property="referenceOrderNo" jdbcType="NVARCHAR" />
	    <result column="reference_order_type" property="referenceOrderType" jdbcType="NVARCHAR" />
	    <result column="status" property="status" jdbcType="NVARCHAR" />
	    <result column="status_meaning" property="statusMeaning" jdbcType="NVARCHAR"/>
	    <result column="partner_confirm_type" property="partnerConfirmType" jdbcType="NVARCHAR" />
	    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
	    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	    <result column="creator" property="creator" jdbcType="NVARCHAR" />
	    <result column="remark" property="remark" jdbcType="NVARCHAR" />
	    <result column="work_shop_name" property="workshopName" jdbcType="NVARCHAR"/>
	    
	    <result column="plate_number" property="plateNumber" jdbcType="NVARCHAR"/>
	    <result column="address" property="address" jdbcType="NVARCHAR"/>
	    <result column="receive_user_name" property="receiveUserName" jdbcType="NVARCHAR"/>
	    <result column="receive_phone_no" property="receivePhoneNo" jdbcType="NVARCHAR"/>
	    <result column="effective_time" property="effectiveTime" jdbcType="TIMESTAMP"/>
	    <result column="type" property="type" jdbcType="NVARCHAR"/>
	    <result column="bill_id" property="billId" jdbcType="NVARCHAR"/>
	    <result column="snd_type" property="sndType" jdbcType="NVARCHAR"/>
		 <result column="phone_no" property="phoneNo" jdbcType="NVARCHAR"/>
		<result column="service_time" property="serviceTime" jdbcType="TIMESTAMP"/>
		
		
		<result column="creator_display_name" property="creatorDisplayName" jdbcType="NVARCHAR"/>
		<result column="invalid_time" property="invalidTime" jdbcType="TIMESTAMP" />
		
		<result column="region_name" property="regionName" jdbcType="NVARCHAR"/>
		<result column="is_export" property="isExport" jdbcType="INTEGER"/>
		<result column="car_type" property="carType" jdbcType="INTEGER"/>
		<result column="preferential_type_price" property="preferentialTypePrice" jdbcType="NVARCHAR"/>
		<result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP"/>
		<result column="per_hours_subsidy" property="perHoursSubsidy" jdbcType="NVARCHAR"/>
		<result column="oil_injection" property="oilInjection" jdbcType="NVARCHAR"/>
		<result column="org_id" property="orgId" jdbcType="NVARCHAR"/>
		<result column="org_name" property="orgName" jdbcType="NVARCHAR"/>
		<result column="order_workshopname" property="orderWorkshopName" jdbcType="NVARCHAR"/>
		<result column="execut_name" property="executName" jdbcType="NVARCHAR"/>
		
		<result column="memberid" property="memberid" jdbcType="NVARCHAR"/>
		<result column="promotioncode" property="promotionCode" jdbcType="NVARCHAR"/>
		<result column="isreplace_filter" property="isreplaceFilter" jdbcType="BIT" />
		<result column="couponcode" property="couponCode" jdbcType="NVARCHAR"/>
		
		<result column="sp_address_id" property="spAddressId" jdbcType="BIGINT"/>
		<result column="stock_out_no" property="stockOutNo" jdbcType="NVARCHAR" />
    	<result column="stock_in_no" property="stockInNo" jdbcType="NVARCHAR" />
    	<result column="cardno" property="cardno" jdbcType="NVARCHAR"/>
    	<result column="partner_id" property="poOrderPartnerId" jdbcType="BIGINT"/>
    	<result column="saporderno" property="sapOrderNo" jdbcType="NVARCHAR"/>
    	<result column="card_type" property="cardType" jdbcType="NVARCHAR"/>
    	<result column="service_acount" property="serviceAcount" jdbcType="INTEGER"/>
    	<result column="remaining_service_times" property="remainingServiceTimes" jdbcType="INTEGER"/>
    	<result column="settlement_batch_id" property="settlementBatchId" jdbcType="BIGINT"/>
    	<result column="settlement_model_type" property="settlementModelType" jdbcType="NVARCHAR"/>
    	
		 <result column="order_type_text" property="orderTypeText" jdbcType="NVARCHAR"/>
		 <result column="oil_type_text" property="oilTypeText" jdbcType="NVARCHAR"/>
 		 <result column="status_text" property="statusText" jdbcType="NVARCHAR"/>
 		 <result column="viscosity" property="viscosity" jdbcType="NVARCHAR"/>
		 <result column="card_enable_times" property="cardEnableTimes" jdbcType="INTEGER"/>
		 
		 <result column="is_settled" property="isSettled" jdbcType="INTEGER"/>
		 <result column="settled_num" property="settledNum" jdbcType="INTEGER" />
		 <result column="region_id" property="regionId" jdbcType="BIGINT"/>
		 <result column="address_type" property="addressType" jdbcType="NVARCHAR" />
		 <result column="completion_time" property="completionTime" jdbcType="TIMESTAMP" />
		  <result column="product_evalute" property="productEvalute" jdbcType="NVARCHAR" />
		  <result column="experience_evalute" property="experienceEvalute" jdbcType="NVARCHAR" />
		  <result column="technician_evalute" property="technicianEvalute" jdbcType="NVARCHAR" />
		  <result column="environ_evalute" property="environEvalute" jdbcType="NVARCHAR" />
		  <result column="other_suggest" property="otherSuggest" jdbcType="NVARCHAR" />
		  <result column="verificate_code" property="verificateCode" jdbcType="NVARCHAR" />
		   <result column="verificate_flag" property="verificateFlag" jdbcType="INTEGER" />
		   <result column="b2b_status" property="b2bStatus" jdbcType="INTEGER" />
	    <result column="region" property="region" jdbcType="VARCHAR"/>
	    <result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="partner_name" property="partnerName" jdbcType="NVARCHAR"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
		<result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
		<result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
		<result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
		<result column="ext_property10" property="extProperty10" jdbcType="VARCHAR"/>
		<result column="from_store_name" property="fromStoreName" jdbcType="VARCHAR"/>
		<result column="trans_workshop" property="transWorkshop" jdbcType="INTEGER"/>
		<result column="customer_type" property="customerType" jdbcType="VARCHAR"/>
		<result column="customer_type_text" property="customerTypeText" jdbcType="VARCHAR"/>
		<result column="workshop_address" property="workshopAddress" jdbcType="VARCHAR"/>
		<result column="logistics_provider_user_name" property="logisticsProviderUserName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<resultMap id="SettleResultMap" type="com.chevron.ordersettlement.model.OrderSettlementVo" >
		<result column="plate_number" property="plateNumber" jdbcType="NVARCHAR"/>
		<result column="order_no" property="orderNo" jdbcType="NVARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="work_shop_name" property="workshopName" jdbcType="NVARCHAR"/>
		<result column="address" property="address" jdbcType="NVARCHAR"/>
		<result column="receive_user_name" property="receiverUserName" jdbcType="NVARCHAR"/>
		<result column="receive_phone_no" property="receiverPhone" jdbcType="NVARCHAR"/>
		<result column="service_time" property="serviceTime" jdbcType="TIMESTAMP"/>
		<result column="effective_time" property="effectiveTime" jdbcType="TIMESTAMP"/>
		<result column="invalid_time" property="invalidTime" jdbcType="TIMESTAMP"/>
		<result column="region_name" property="regionName" jdbcType="NVARCHAR"/>
		<result column="card_type" property="cardType" jdbcType="NVARCHAR" />
		<result column="viscosity" property="viscosity" jdbcType="NVARCHAR" />
		<result column="oil_injection" property="oilInjection" jdbcType="NVARCHAR"/>
		<result column="is_settled" property="isSettled" jdbcType="INTEGER"/>
		<result column="car_type" property="carType" jdbcType="NVARCHAR"/>
		<result column="service_acount" property="serviceAcount" jdbcType="INTEGER"/>
		<result column="settled_num" property="settledNum" jdbcType="INTEGER" />
		<result column="card_num" property="cardNum" jdbcType="INTEGER" />
	</resultMap>
	
	<sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
	<sql id="Base_Column_List">
		 id, order_no, order_type, buy_user_no, buy_user_name, buyer_card_id, invoice_info, 
    source, source_id, pay_type, pay_state, total_product_price, total_delivery_price, 
    total_order_price, total_product_prefer_price, total_delivery_prefer_price, total_order_prefer_price, 
    total_order_pay_price, total_order_need_pay_price, work_shop_id, reference_order_no, 
    reference_order_type, status, create_time, update_time, creator, remark,work_shop_name,plate_number,
    address,receive_user_name,receive_phone_no,effective_time,type,bill_id,snd_type,phone_no,service_time,invalid_time,region_name,is_export,
    car_type,preferential_type_price,delivery_time,oil_injection,sp_address_id,partner_id,ext_property1,ext_property2,ext_property3,ext_property4,
		ext_property5,ext_property6,ext_property7,ext_property8,ext_property9,ext_property10
	</sql>
	
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.OrderVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_order
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
   <!--  <if test="orderByClause != null" >
      order by ${orderByClause} desc
    </if> -->
    order by create_time desc
  </select>
  
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		tt_order.*,t_org.organization_name org_name
		from wx_t_order tt_order
		LEFT JOIN wx_t_organization t_org 
        ON t_org.id = tt_order.partner_id
		where tt_order.id = #{id,jdbcType=BIGINT}
	</select>
	<select id="selectByOrderNo" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from wx_t_order
		where order_no = #{orderNo,jdbcType=NVARCHAR}
	</select>
	
	<select id="selectBySapOrderNo" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from wx_t_order
		where saporderno = #{saporderno,jdbcType=NVARCHAR}
	</select>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from wx_t_order
		where id = #{id,jdbcType=BIGINT}
	</delete>
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.OrderVo">
		insert into wx_t_order (order_no, order_type, 
      buy_user_no, buy_user_name, buyer_card_id, 
      invoice_info, source, source_id, 
      pay_type, pay_state, total_product_price, 
      total_delivery_price, total_order_price, total_product_prefer_price, 
      total_delivery_prefer_price, total_order_prefer_price, 
      total_order_pay_price, total_order_need_pay_price, 
      work_shop_id, reference_order_no, reference_order_type, 
      status, create_time, update_time, 
      creator, remark,sp_address_id)
    values (#{orderNo,jdbcType=NVARCHAR}, #{orderType,jdbcType=NVARCHAR}, 
      #{buyUserNo,jdbcType=BIGINT}, #{buyUserName,jdbcType=NVARCHAR}, #{buyerCardId,jdbcType=NVARCHAR}, 
      #{invoiceInfo,jdbcType=NVARCHAR}, #{source,jdbcType=NVARCHAR}, #{sourceId,jdbcType=NVARCHAR}, 
      #{payType,jdbcType=NVARCHAR}, #{payState,jdbcType=NVARCHAR}, #{totalProductPrice,jdbcType=NUMERIC}, 
      #{totalDeliveryPrice,jdbcType=NUMERIC}, #{totalOrderPrice,jdbcType=NUMERIC}, #{totalProductPreferPrice,jdbcType=NUMERIC}, 
      #{totalDeliveryPreferPrice,jdbcType=NUMERIC}, #{totalOrderPreferPrice,jdbcType=NUMERIC}, 
      #{totalOrderPayPrice,jdbcType=NUMERIC}, #{totalOrderNeedPayPrice,jdbcType=NUMERIC}, 
      #{workShopId,jdbcType=BIGINT}, #{referenceOrderNo,jdbcType=NVARCHAR}, #{referenceOrderType,jdbcType=NVARCHAR}, 
      #{status,jdbcType=NVARCHAR}, getdate(), getdate(), 
      #{creator,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR},#{spAddressId,jdbcType=BIGINT})
	</insert>
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.OrderVo" >
    insert into wx_t_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="orderType != null" >
        order_type,
      </if>
      <if test="buyUserNo != null" >
        buy_user_no,
      </if>
      <if test="buyUserName != null" >
        buy_user_name,
      </if>
      <if test="buyerCardId != null" >
        buyer_card_id,
      </if>
      <if test="invoiceInfo != null" >
        invoice_info,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="sourceId != null" >
        source_id,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="payState != null" >
        pay_state,
      </if>
      <if test="totalProductPrice != null" >
        total_product_price,
      </if>
      <if test="totalDeliveryPrice != null" >
        total_delivery_price,
      </if>
      <if test="totalOrderPrice != null" >
        total_order_price,
      </if>
      <if test="totalProductPreferPrice != null" >
        total_product_prefer_price,
      </if>
      <if test="totalDeliveryPreferPrice != null" >
        total_delivery_prefer_price,
      </if>
      <if test="totalOrderPreferPrice != null" >
        total_order_prefer_price,
      </if>
      <if test="totalOrderPayPrice != null" >
        total_order_pay_price,
      </if>
      <if test="totalOrderNeedPayPrice != null" >
        total_order_need_pay_price,
      </if>
      <if test="workShopId != null" >
        work_shop_id,
      </if>
      <if test="referenceOrderNo != null" >
        reference_order_no,
      </if>
      <if test="referenceOrderType != null" >
        reference_order_type,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="workshopName != null" >
        work_shop_name,
      </if>
      <if test="plateNumber != null" >
        plate_number,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="receiveUserName != null" >
        receive_user_name,
      </if>
      <if test="receivePhoneNo != null" >
        receive_phone_no,
      </if>
      <if test="effectiveTime != null" >
        effective_time,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="billId != null" >
        bill_id,
      </if>
      <if test="sndType != null" >
        snd_type,
      </if>
       <if test="phoneNo != null" >
        phone_no,
      </if>
      <if test="serviceTime != null" >
        service_time,
      </if>
      <if test="invalidTime != null" >
        invalid_time,
      </if>
      <if test="regionName != null" >
        region_name,
      </if>
      <if test="isExport != null" >
        is_export,
      </if>
       <if test="carType != null" >
        car_type,
      </if>
      <if test="preferentialTypePrice != null" >
        preferential_type_price,
      </if>
    
      <if test="deliveryTime != null" >
        delivery_time,
      </if>
      <if test="oilInjection != null" >
        oil_injection,
      </if>
       <if test="memberid != null" >
        memberid,
      </if>
      <if test="promotionCode != null" >
        promotioncode,
      </if>
      <if test="isreplaceFilter != null" >
        isreplace_filter,
      </if>
       <if test="couponCode != null" >
        couponcode,
      </if>
      <if test="spAddressId != null" >
        sp_address_id,
      </if>
      <if test="cardno != null" >
        cardno,
      </if>
      <if test="poOrderPartnerId != null" >
        partner_id,
      </if>
      <if test="sapOrderNo != null" >
        saporderno,
      </if>
      <if test="cardType != null" >
        card_type,
      </if>
      <if test="serviceAcount != null" >
        service_acount,
      </if>
      <if test="remainingServiceTimes != null" >
        remaining_service_times,
      </if>
      <if test="settlementModelType != null" >
        settlement_model_type,
      </if>
      <if test="regionId != null" >
        region_id,
      </if>
       <if test="addressType != null" >
        address_type, 
      </if>
      <if test="partnerConfirmTime != null" >
        partner_confirm_time,
      </if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="extProperty6 != null">
				ext_property6,
			</if>
			<if test="extProperty7 != null">
				ext_property7,
			</if>
			<if test="extProperty8 != null">
				ext_property8,
			</if>
			<if test="extProperty9 != null">
				ext_property9,
			</if>
			<if test="extProperty10 != null">
				ext_property10,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=NVARCHAR},
      </if>
      <if test="buyUserNo != null" >
        #{buyUserNo,jdbcType=BIGINT},
      </if>
      <if test="buyUserName != null" >
        #{buyUserName,jdbcType=NVARCHAR},
      </if>
      <if test="buyerCardId != null" >
        #{buyerCardId,jdbcType=NVARCHAR},
      </if>
      <if test="invoiceInfo != null" >
        #{invoiceInfo,jdbcType=NVARCHAR},
      </if>
      <if test="source != null" >
        #{source,jdbcType=NVARCHAR},
      </if>
      <if test="sourceId != null" >
        #{sourceId,jdbcType=NVARCHAR},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=NVARCHAR},
      </if>
      <if test="payState != null" >
        #{payState,jdbcType=NVARCHAR},
      </if>
      <if test="totalProductPrice != null" >
        #{totalProductPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalDeliveryPrice != null" >
        #{totalDeliveryPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPrice != null" >
        #{totalOrderPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalProductPreferPrice != null" >
        #{totalProductPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalDeliveryPreferPrice != null" >
        #{totalDeliveryPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPreferPrice != null" >
        #{totalOrderPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPayPrice != null" >
        #{totalOrderPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderNeedPayPrice != null" >
        #{totalOrderNeedPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="workShopId != null" >
        #{workShopId,jdbcType=BIGINT},
      </if>
      <if test="referenceOrderNo != null" >
        #{referenceOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="referenceOrderType != null" >
        #{referenceOrderType,jdbcType=NVARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="workshopName != null" >
        #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="plateNumber != null" >
        #{plateNumber,jdbcType=NVARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=NVARCHAR},
      </if>
      <if test="receiveUserName != null" >
        #{receiveUserName,jdbcType=NVARCHAR},
      </if>
      <if test="receivePhoneNo != null" >
        #{receivePhoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="effectiveTime != null" >
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="billId != null" >
        #{billId,jdbcType=NVARCHAR},
      </if>
      <if test="sndType != null" >
        #{sndType,jdbcType=NVARCHAR},
      </if>
      <if test="phoneNo != null" >
        #{phoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="serviceTime != null" >
        #{serviceTime,jdbcType=TIMESTAMP},
      </if>
       <if test="invalidTime != null" >
         #{invalidTime,jdbcType=TIMESTAMP},
      </if>
       <if test="regionName != null" >
        #{regionName,jdbcType=NVARCHAR},
      </if>
      <if test="isExport != null" >
        #{isExport,jdbcType=INTEGER},
      </if>
      <if test="carType != null" >
        #{carType,jdbcType=NVARCHAR},
      </if>
      <if test="preferentialTypePrice != null" >
         #{preferentialTypePrice,jdbcType=NVARCHAR},
      </if>
      <if test="deliveryTime != null" >
          #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
       <if test="oilInjection != null" >
        #{oilInjection,jdbcType=NVARCHAR},
      </if>
       <if test="memberid != null" >
        #{memberid,jdbcType=VARCHAR},
      </if>
      <if test="promotionCode != null" >
        #{promotionCode,jdbcType=VARCHAR},
      </if>
      <if test="isreplaceFilter != null" >
        #{isreplaceFilter,jdbcType=BIT},
      </if>
      <if test="couponCode != null" >
         #{couponCode,jdbcType=VARCHAR},
      </if>
       <if test="spAddressId != null" >
         #{spAddressId,jdbcType=BIGINT},
      </if>
       <if test="cardno != null" >
         #{cardno,jdbcType=VARCHAR},
      </if>
      <if test="poOrderPartnerId != null" >
         #{poOrderPartnerId,jdbcType=BIGINT},
      </if>
       <if test="sapOrderNo != null" >
        #{sapOrderNo,jdbcType=NVARCHAR},
      </if>
       <if test="cardType != null" >
        #{cardType,jdbcType=NVARCHAR},
      </if>
       <if test="serviceAcount != null" >
        #{serviceAcount,jdbcType=INTEGER},
      </if>
      <if test="remainingServiceTimes != null" >
         #{remainingServiceTimes,jdbcType=INTEGER},
      </if>
      <if test="settlementModelType != null" >
         #{settlementModelType,jdbcType=NVARCHAR},
      </if>
      <if test="regionId != null" >
         #{regionId,jdbcType=BIGINT},
      </if>
       <if test="addressType != null" >
         #{addressType,jdbcType=NVARCHAR},
      </if>
      <if test="partnerConfirmTime != null" >
        #{partnerConfirmTime,jdbcType=TIMESTAMP},
      </if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null">
				#{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null">
				#{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null">
				#{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null">
				#{extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="extProperty10 != null">
				#{extProperty10,jdbcType=VARCHAR},
			</if>
    </trim>
  </insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.OrderVo" >
    update wx_t_order
    <set >
      <if test="orderNo != null and orderNo != '' " >
        order_no = #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="orderType != null and orderType != ''" >
        order_type = #{orderType,jdbcType=NVARCHAR},
      </if>
      <if test="buyUserNo != null" >
        buy_user_no = #{buyUserNo,jdbcType=BIGINT},
      </if>
      <if test="buyUserName != null and buyUserName != '' " >
        buy_user_name = #{buyUserName,jdbcType=NVARCHAR},
      </if>
      <if test="buyerCardId != null and buyerCardId != ''" >
        buyer_card_id = #{buyerCardId,jdbcType=NVARCHAR},
      </if>
      <if test="invoiceInfo != null and invoiceInfo != ''" >
        invoice_info = #{invoiceInfo,jdbcType=NVARCHAR},
      </if>
      <if test="source != null and source != ''" >
        source = #{source,jdbcType=NVARCHAR},
      </if>
      <if test="sourceId != null and sourceId != ''" >
        source_id = #{sourceId,jdbcType=NVARCHAR},
      </if>
      <if test="payType != null and payType != ''" >
        pay_type = #{payType,jdbcType=NVARCHAR},
      </if>
      <if test="payState != null and payState != ''" >
        pay_state = #{payState,jdbcType=NVARCHAR},
      </if>
      <if test="totalProductPrice != null and totalProductPrice != 0" >
        total_product_price = #{totalProductPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalDeliveryPrice != null and totalDeliveryPrice != 0" >
        total_delivery_price = #{totalDeliveryPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPrice != null and totalOrderPrice != 0" >
        total_order_price = #{totalOrderPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalProductPreferPrice != null and totalProductPreferPrice != 0" >
        total_product_prefer_price = #{totalProductPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalDeliveryPreferPrice != null and totalDeliveryPreferPrice != 0" >
        total_delivery_prefer_price = #{totalDeliveryPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPreferPrice != null and totalOrderPreferPrice != 0" >
        total_order_prefer_price = #{totalOrderPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPayPrice != null and totalOrderPayPrice != 0" >
        total_order_pay_price = #{totalOrderPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderNeedPayPrice != null and totalOrderNeedPayPrice != 0" >
        total_order_need_pay_price = #{totalOrderNeedPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="workShopId != null" >
        work_shop_id = #{workShopId,jdbcType=BIGINT},
      </if>
      <if test="referenceOrderNo != null and referenceOrderNo != ''" >
        reference_order_no = #{referenceOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="referenceOrderType != null and referenceOrderType != ''" >
        reference_order_type = #{referenceOrderType,jdbcType=NVARCHAR},
      </if>
      <if test="status != null and status != ''" >
        status = #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null and remark != '' " >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="workshopName != null and workshopName != '' " >
        work_shop_name = #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="plateNumber != null and plateNumber != '' " >
        plate_number = #{plateNumber,jdbcType=NVARCHAR},
      </if>
      <if test="address != null and address != '' " >
        address = #{address,jdbcType=NVARCHAR},
      </if>
      <if test="receiveUserName != null and receiveUserName != '' " >
        receive_user_name = #{receiveUserName,jdbcType=NVARCHAR},
      </if>
      <if test="receivePhoneNo != null and receivePhoneNo != '' " >
        receive_phone_no = #{receivePhoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="effectiveTime != null" >
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null and type != ''" >
        type = #{type,jdbcType=NVARCHAR},
      </if>
      <if test="billId != null and billId != '' " >
        bill_id = #{billId,jdbcType=NVARCHAR},
      </if>
      <if test="sndType != null and sndType != ''" >
        snd_type = #{sndType,jdbcType=NVARCHAR},
      </if>
       <if test="phoneNo!= null and phoneNo!= '' " >
        phone_no = #{phoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="serviceTime!= null" >
        service_time = #{serviceTime,jdbcType=TIMESTAMP},
      </if>
       <if test="invalidTime != null" >
        invalid_time = #{invalidTime,jdbcType=TIMESTAMP},
      </if>
       <if test="regionName != null and regionName != '' " >
        region_name = #{regionName,jdbcType=NVARCHAR},
      </if>
      <if test="isExport != null" >
        is_export = #{isExport,jdbcType=INTEGER},
      </if>
       <if test="carType != null and carType != '' " >
        car_type = #{carType,jdbcType=NVARCHAR},
      </if>
      <if test="preferentialTypePrice != null and preferentialTypePrice != '' " >
        preferential_type_price = #{preferentialTypePrice,jdbcType=NVARCHAR},
      </if>
      <if test="deliveryTime != null" >
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
       <if test="oilInjection != null and oilInjection != ''" >
        oil_injection = #{oilInjection,jdbcType=NVARCHAR},
      </if>
      <if test="partnerConfirmTime != null" >
        partner_confirm_time  = #{partnerConfirmTime,jdbcType=TIMESTAMP},
      </if>
        <if test="partnerConfirmType != null" >
            partner_confirm_type  = #{partnerConfirmType,jdbcType=NVARCHAR},
        </if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null" >
				ext_property6 = #{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null" >
				ext_property7 = #{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null" >
				ext_property8 = #{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null" >
				ext_property9 = #{extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="extProperty10 != null" >
				ext_property10 = #{extProperty10,jdbcType=VARCHAR},
			</if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
	<update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.OrderVo" >
    update wx_t_order
    set order_no = #{orderNo,jdbcType=NVARCHAR},
      order_type = #{orderType,jdbcType=NVARCHAR},
      buy_user_no = #{buyUserNo,jdbcType=BIGINT},
      buy_user_name = #{buyUserName,jdbcType=NVARCHAR},
      buyer_card_id = #{buyerCardId,jdbcType=NVARCHAR},
      invoice_info = #{invoiceInfo,jdbcType=NVARCHAR},
      source = #{source,jdbcType=NVARCHAR},
      source_id = #{sourceId,jdbcType=NVARCHAR},
      pay_type = #{payType,jdbcType=NVARCHAR},
      pay_state = #{payState,jdbcType=NVARCHAR},
      total_product_price = #{totalProductPrice,jdbcType=NUMERIC},
      total_delivery_price = #{totalDeliveryPrice,jdbcType=NUMERIC},
      total_order_price = #{totalOrderPrice,jdbcType=NUMERIC},
      total_product_prefer_price = #{totalProductPreferPrice,jdbcType=NUMERIC},
      total_delivery_prefer_price = #{totalDeliveryPreferPrice,jdbcType=NUMERIC},
      total_order_prefer_price = #{totalOrderPreferPrice,jdbcType=NUMERIC},
      total_order_pay_price = #{totalOrderPayPrice,jdbcType=NUMERIC},
      total_order_need_pay_price = #{totalOrderNeedPayPrice,jdbcType=NUMERIC},
      work_shop_id = #{workShopId,jdbcType=BIGINT},
      reference_order_no = #{referenceOrderNo,jdbcType=NVARCHAR},
      reference_order_type = #{referenceOrderType,jdbcType=NVARCHAR},
      status = #{status,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR},
      work_shop_name =#{workshopName,jdbcType=NVARCHAR},
      plate_number =#{plateNumber,jdbcType=NVARCHAR},
      address =#{address,jdbcType=NVARCHAR},
      receive_user_name =#{receiveUserName,jdbcType=NVARCHAR},
      receive_phone_no = #{receivePhoneNo,jdbcType=NVARCHAR},
      effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=NVARCHAR},
      bill_id = #{billId,jdbcType=NVARCHAR},
      snd_type = #{sndType,jdbcType=NVARCHAR},
      phone_no = #{phoneNo,jdbcType=NVARCHAR},
      service_time = #{serviceTime,jdbcType=TIMESTAMP},
      invalid_time = #{invalidTime,jdbcType=TIMESTAMP}
     
    where id = #{id,jdbcType=BIGINT}
  </update>

	<!-- 根据条件查询订单列表-->
	<select id="queryOrdersByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
		select 
		<include refid="Base_Column_List" />
		,(select u.CH_NAME from wx_t_user u where u.USER_ID = t.creator) as creator_display_name
		,(select o.work_shop_name FROM wx_t_work_shop o WHERE o.id = t.work_shop_id) as work_shop_name
		 from wx_t_order t
		where 1 = 1
		<if test="orderNo != null">
			and t.order_no = #{orderNo,jdbcType=VARCHAR}
		</if>
		<if test="orderType != null">
			and t.order_type = #{orderType,jdbcType=VARCHAR}
		</if>
		<if test="orderId != null">
			and t.id = #{orderId,jdbcType=VARCHAR}
		</if>
	</select>
	
	<!-- 根据条件查询订单列表,数据库分页-->
	<select id="queryOrdersByConditionWithPagination" parameterType="com.chevron.pms.model.GenericPaginationQueryParams" resultMap="BaseResultMap">
		select
		 	<if test="limit != -1">
		 		top ${limit}
		 	</if>
		 <!-- <include refid="Base_Column_List" /> -->
		 t.id, t.order_no, t.order_type, 
		t.buy_user_no, 
		t.buy_user_name, 
		t.buyer_card_id, 
		t.invoice_info, 
		t.source, 
		t.source_id, 
		t.pay_type, pay_state, 
		t.total_product_price, 
		t.total_delivery_price, 
		t.total_order_price, 
		t.total_product_prefer_price, 
		t.total_delivery_prefer_price, 
		t.total_order_prefer_price, 
		t.total_order_pay_price, 
		t.total_order_need_pay_price, 
		t.work_shop_id, 
		t.reference_order_no, 
		t.reference_order_type, 
		t.status, 
		t.create_time, 
		t.update_time, 
		t.creator, 
		t.remark,
		t.work_shop_name,
		t.plate_number, 
		t.address,
		t.receive_user_name,
		t.receive_phone_no,
		t.effective_time,
		t.type,
		t.bill_id,
		t.snd_type,
		t.phone_no,
		t.service_time,
		t.invalid_time,
		t.region_name,
		t.is_export, 
		t.car_type,
		t.preferential_type_price,
		t.delivery_time,
		t.oil_injection
		,t.orgname as org_name 
		,t.t_order_workshopname AS order_workshopname
		,(select u.CH_NAME from wx_t_user u where u.USER_ID = t.creator) as creator_display_name
		,(select o.work_shop_name FROM wx_t_work_shop o WHERE o.id = t.work_shop_id) as work_shop_name
		,(select p.codename from wx_t_properties p where p.codetype = 'order_status' and p.code = t.status) as status_meaning 
		  from 
		  (
		  	  select 
		  	    <if test="orderBy != null">
		  	    	row_number() over(order by ${orderBy}) as rownumber,
		  	    </if>
		  	  <!-- <include refid="Base_Column_List" /> -->
		  	  tt_order.id, tt_order.order_no, tt_order.order_type, 
				tt_order.buy_user_no, 
				tt_order.buy_user_name, 
				tt_order.buyer_card_id, 
				tt_order.invoice_info, 
				tt_order.source, 
				tt_order.source_id, 
				tt_order.pay_type, pay_state, 
				tt_order.total_product_price, 
				tt_order.total_delivery_price, 
				tt_order.total_order_price, 
				tt_order.total_product_prefer_price, 
				tt_order.total_delivery_prefer_price, 
				tt_order.total_order_prefer_price, 
				tt_order.total_order_pay_price, 
				tt_order.total_order_need_pay_price, 
				tt_order.work_shop_id, 
				tt_order.reference_order_no, 
				tt_order.reference_order_type, 
				tt_order.status, 
				tt_order.create_time, 
				tt_order.update_time, 
				tt_order.creator, 
				tt_order.remark,
				tt_order.work_shop_name,
				tt_order.plate_number, 
				tt_order.address,
				tt_order.receive_user_name,
				tt_order.receive_phone_no,
				tt_order.effective_time,
				tt_order.type,
				tt_order.bill_id,
				tt_order.snd_type,
				tt_order.phone_no,
				tt_order.service_time,
				tt_order.invalid_time,
				tt_order.region_name,
				tt_order.is_export, 
				tt_order.car_type,
				tt_order.preferential_type_price,
				tt_order.delivery_time,
				tt_order.oil_injection,
		  	    tt_org.organization_name AS orgname,
		  	    tt_workshop.work_shop_name AS t_order_workshopname 
		  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
                LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
                LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
			   where 1 = 1
			    <!-- AND tt_order.work_shop_id IS NOT NULL              AND       tt_org.organization_name IS NOT NULL  -->
			  <if test="queryParamsMap.mPartnerId != null">
			  	 AND tt_org.id= #{queryParamsMap.mPartnerId}
			  </if>
			  <if test="queryParamsMap.orderNo != null and queryParamsMap.orderNo != '' ">
				 and tt_order.order_no = #{queryParamsMap.orderNo,jdbcType=VARCHAR}
			  </if>
			  <if test="queryParamsMap.workshopId != null and queryParamsMap.workshopId != ''">
		     	and tt_order.WORK_SHOP_ID = #{queryParamsMap.workshopId, jdbcType=VARCHAR}
		  	  </if>
			  <if test="queryParamsMap.dateFrom != null">
			    and tt_order.CREATE_TIME &gt;= #{queryParamsMap.dateFrom, jdbcType=TIMESTAMP}
			  </if>
			  <if test="queryParamsMap.dateTo != null">
			    and tt_order.CREATE_TIME &lt;= #{queryParamsMap.dateTo, jdbcType=TIMESTAMP}
			  </if>
			  <if test="queryParamsMap.orderType != null and queryParamsMap.orderType != '' ">
				and tt_order.order_type = #{queryParamsMap.orderType,jdbcType=VARCHAR}
			  </if>
			  <if test="queryParamsMap.status != null">
			  	and tt_order.status = #{queryParamsMap.status}
			  </if>
			  <if test="queryParamsMap.source != null">
			  	and tt_order.source = #{queryParamsMap.source}
			  </if>
			  <!-- add by bo.liu 1026 start -->
			  <if test="queryParamsMap.orderType == null">
			 	and tt_order.order_type != #{queryParamsMap.orderTypeDA,jdbcType=VARCHAR}
			 	and tt_order.order_type != #{queryParamsMap.orderTypeDP,jdbcType=VARCHAR}
		  	 </if>
		 	 <if test="queryParamsMap.workshopName != null">
		  		and tt_workshop.work_shop_name like '%' + #{queryParamsMap.workshopName} + '%' 
		 	 </if>
		 	 <if test="queryParamsMap.queryField != null">
		  		and (tt_workshop.work_shop_name like '%' + #{queryParamsMap.queryField} + '%' 
		  				or tt_order.order_no like '%' + #{queryParamsMap.queryField} + '%' 
		  				or tt_order.source like '%' + #{queryParamsMap.queryField} + '%' 
		  				or tt_org.organization_name like '%' + #{queryParamsMap.queryField} + '%' 
		  			)
		 	 </if>
		 	 
		 	 <!-- add by bo.liu 1026 end -->
		  ) t 
		 where 1 = 1
		 <if test="start != -1 and orderBy != null">
		     and t.rownumber > #{start, jdbcType=INTEGER} 
		 </if>
	</select>
	
	<!-- 根据条件查询订单数量,数据库分页-->
	<select id="queryOrdersCountByConditionWithPagination" resultType="long" parameterType="com.chevron.pms.model.GenericPaginationQueryParams">
	  	  select count(1)
	  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
            LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
            LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
		   where 1 = 1
		    <!-- AND tt_order.work_shop_id IS NOT NULL              AND       tt_org.organization_name IS NOT NULL  -->
		  <if test="queryParamsMap.mPartnerId != null">
			  	 AND tt_org.id= #{queryParamsMap.mPartnerId}
		  </if>
		  <if test="queryParamsMap.orderNo != null and queryParamsMap.orderNo != '' ">
			 and tt_order.order_no = #{queryParamsMap.orderNo,jdbcType=VARCHAR}
		  </if>
		  <if test="queryParamsMap.workshopId != null and queryParamsMap.workshopId != ''">
		     and tt_order.WORK_SHOP_ID = #{queryParamsMap.workshopId, jdbcType=VARCHAR}
		  </if>
		  <if test="queryParamsMap.dateFrom != null">
		     and tt_order.CREATE_TIME &gt;= #{queryParamsMap.dateFrom, jdbcType=TIMESTAMP}
		  </if>
		  <if test="queryParamsMap.dateTo != null">
		     and tt_order.CREATE_TIME &lt;= #{queryParamsMap.dateTo, jdbcType=TIMESTAMP}
		  </if>
		  <if test="queryParamsMap.orderType != null and queryParamsMap.orderType != '' ">
			 and tt_order.order_type = #{queryParamsMap.orderType,jdbcType=VARCHAR}
		  </if>
		  <if test="queryParamsMap.status != null">
		  	and tt_order.status = #{queryParamsMap.status}
		  </if>
		  <if test="queryParamsMap.source != null">
		  	and tt_order.source = #{queryParamsMap.source}
		  </if>
		   <!-- add by bo.liu 1026 start -->
		  <if test="queryParamsMap.orderType == null">
			 and tt_order.order_type != #{queryParamsMap.orderTypeDA,jdbcType=VARCHAR}
			 and tt_order.order_type != #{queryParamsMap.orderTypeDP,jdbcType=VARCHAR}
		  </if>
		  <if test="queryParamsMap.workshopName != null">
		  	and tt_workshop.work_shop_name like '%' + #{queryParamsMap.workshopName} + '%' 
		  </if>
		   <!-- add by bo.liu 1026 end -->
	</select>
	<select id="queryActualOrdersByWorkshop" parameterType="map" resultMap="AppOrderResultMap">
	
		SELECT
	    o.ID as order_id,
	    o.ORDER_NO,
	    o.CREATE_TIME as order_date,
	    o.TOTAL_ORDER_PRICE,
	    l.ID as order_line_id,
	    L.PRODUCT_ID,
	    L.SKU,
	    L.PRODUCT_NAME,
	    L.AMOUNT,
	    L.UNITS,
	    L.PRICE,
	    L.ACTUAL_AMOUNT
	FROM
	    WX_T_ORDER_LINE l
	JOIN
	    wx_t_order o
	ON
	    l.ORDER_ID = o.ID
	WHERE 1 = 1
	  and o.WORK_SHOP_ID = #{workshopId, jdbcType=VARCHAR}
	  and o.ORDER_TYPE = 'A'
	  and o.CREATE_TIME between #{dateFrom, jdbcType=TIMESTAMP} and #{dateTo, jdbcType=TIMESTAMP}
  </select>
  <resultMap id="AppOrderResultMap" type="com.chevron.pms.model.app.Order">
		<id column="order_id" property="orderId" jdbcType="BIGINT" />
	    <result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
	    <result column="order_date" property="orderDate" jdbcType="TIMESTAMP" />
	    <result column="total_order_price" property="orderTotalPrice" jdbcType="DOUBLE" />
	    <collection property="orderLineList" ofType="com.chevron.pms.model.app.OrderLine">
	    	<id column="order_line_id" property="orderLineId"/>
	    	<result column="product_id" property="productId" />
	    	<result column="order_id" property="orderId"/>
	    	<result column="sku" property="sku" />
	    	<result column="product_name" property="productName" />
	    	<result column="amount" property="amount" />
	    	<result column="units" property="units" />
	    	<result column="price" property="price" />
	    	<result column="actual_amount" property="actualAmount" />
	    </collection>
	</resultMap>
	<update id="updateByNoSelective" parameterType="com.chevron.pms.model.OrderVo" >
    update wx_t_order
    <set >
      <if test="orderType != null and orderType != '' " >
        order_type = #{orderType,jdbcType=NVARCHAR},
      </if>
      <if test="buyUserNo != null" >
        buy_user_no = #{buyUserNo,jdbcType=BIGINT},
      </if>
      <if test="buyUserName != null and buyUserName != '' " >
        buy_user_name = #{buyUserName,jdbcType=NVARCHAR},
      </if>
      <if test="buyerCardId != null and buyerCardId != '' " >
        buyer_card_id = #{buyerCardId,jdbcType=NVARCHAR},
      </if>
      <if test="invoiceInfo != null and invoiceInfo != '' " >
        invoice_info = #{invoiceInfo,jdbcType=NVARCHAR},
      </if>
      <if test="source != null and source != '' " >
        source = #{source,jdbcType=NVARCHAR},
      </if>
      <if test="sourceId != null and sourceId != '' " >
        source_id = #{sourceId,jdbcType=NVARCHAR},
      </if>
      <if test="payType != null and payType != '' " >
        pay_type = #{payType,jdbcType=NVARCHAR},
      </if>
      <if test="payState != null and payState != '' " >
        pay_state = #{payState,jdbcType=NVARCHAR},
      </if>
      <if test="totalProductPrice != null and totalProductPrice != 0" >
        total_product_price = #{totalProductPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalDeliveryPrice != null and totalDeliveryPrice != 0" >
        total_delivery_price = #{totalDeliveryPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPrice != null and totalOrderPrice != 0" >
        total_order_price = #{totalOrderPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalProductPreferPrice != null and totalProductPreferPrice != 0 " >
        total_product_prefer_price = #{totalProductPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalDeliveryPreferPrice != null and totalDeliveryPreferPrice != 0 " >
        total_delivery_prefer_price = #{totalDeliveryPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPreferPrice != null and totalOrderPreferPrice != 0" >
        total_order_prefer_price = #{totalOrderPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderPayPrice != null and totalOrderPreferPrice != 0" >
        total_order_pay_price = #{totalOrderPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalOrderNeedPayPrice != null and totalOrderNeedPayPrice != 0" >
        total_order_need_pay_price = #{totalOrderNeedPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="workShopId != null" >
        work_shop_id = #{workShopId,jdbcType=BIGINT},
      </if>
      <if test="referenceOrderNo != null and referenceOrderNo != ''" >
        reference_order_no = #{referenceOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="referenceOrderType != null and referenceOrderType != ''" >
        reference_order_type = #{referenceOrderType,jdbcType=NVARCHAR},
      </if>
      <if test="status != null and status != ''" >
        status = #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="workshopName != null and workshopName != ''" >
        work_shop_name = #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="plateNumber != null and plateNumber != ''" >
        plate_number = #{plateNumber,jdbcType=NVARCHAR},
      </if>
      <if test="address != null and address != ''" >
        address = #{address,jdbcType=NVARCHAR},
      </if>
      <if test="receiveUserName != null and receiveUserName != ''" >
        receive_user_name = #{receiveUserName,jdbcType=NVARCHAR},
      </if>
      <if test="receivePhoneNo != null and receivePhoneNo != ''" >
        receive_phone_no = #{receivePhoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="effectiveTime != null" >
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null and type != ''" >
        type = #{type,jdbcType=NVARCHAR},
      </if>
      <if test="billId != null and billId != ''" >
        bill_id = #{billId,jdbcType=NVARCHAR},
      </if>
      <if test="sndType != null and sndType != ''" >
        snd_type = #{sndType,jdbcType=NVARCHAR},
      </if>
       <if test="phoneNo!= null and phoneNo!= ''" >
        phone_no = #{phoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="serviceTime!= null" >
        service_time = #{serviceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null" >
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
       <if test="oilInjection != null and oilInjection != ''" >
        oil_injection = #{oilInjection,jdbcType=NVARCHAR},
      </if>
       <if test="memberid != null and memberid!=''" >
        memberid = #{memberid},
      </if>
      <if test="promotionCode != null" >
        promotioncode=#{promotionCode},
      </if>
      <if test="isreplaceFilter != null" >
        isreplace_filter=#{isreplaceFilter},
      </if>
       <if test="couponCode != null" >
        couponcode=#{couponCode},
      </if>
      <if test="spAddressId != null" >
        sp_address_id=#{spAddressId},
      </if>
      <if test="cardno != null" >
        cardno=#{cardno},
      </if>
      <if test="poOrderPartnerId != null" >
        partner_id=#{poOrderPartnerId},
      </if>
      <if test="sapOrderNo != null" >
        saporderno=#{sapOrderNo},
      </if>
      <if test="remainingServiceTimes != null" >
        remaining_service_times=#{remainingServiceTimes},
      </if>
      
      
    </set>
    where order_no = #{orderNo,jdbcType=NVARCHAR}
  </update>
  
  
  
  <!-- by bo.liu 0907 start-->
  <update id="updateOrderBatchForThridAppUse" parameterType="map" >
   <foreach collection="lstOrders" item="item" index="index" open="begin" close="end;" separator=";">
    update wx_t_order
    <set >
     <if test="item.orderNo != null and item.orderNo != ''" >
        order_no = #{item.orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.orderType != null and item.orderType != ''" >
        order_type = #{item.orderType,jdbcType=NVARCHAR},
      </if>
      <if test="item.buyUserNo != null and item.buyUserNo != ''" >
        buy_user_no = #{item.buyUserNo,jdbcType=BIGINT},
      </if>
      <if test="item.buyUserName != null and item.buyUserName != ''" >
        buy_user_name = #{item.buyUserName,jdbcType=NVARCHAR},
      </if>
      <if test="item.buyerCardId != null and item.buyerCardId != ''" >
        buyer_card_id = #{item.buyerCardId,jdbcType=NVARCHAR},
      </if>
      <if test="item.invoiceInfo != null and item.invoiceInfo != ''" >
        invoice_info = #{item.invoiceInfo,jdbcType=NVARCHAR},
      </if>
      <if test="item.source != null and item.source != ''" >
        source = #{item.source,jdbcType=NVARCHAR},
      </if>
      <if test="item.sourceId != null and item.sourceId != '' " >
        source_id = #{item.sourceId,jdbcType=NVARCHAR},
      </if>
      <if test="item.payType != null and item.payType != '' " >
        pay_type = #{item.payType,jdbcType=NVARCHAR},
      </if>
      <if test="item.payState != null and item.payState != ''" >
        pay_state = #{item.payState,jdbcType=NVARCHAR},
      </if>
      <if test="item.totalProductPrice != null and item.totalProductPrice != ''" >
        total_product_price = #{item.totalProductPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalDeliveryPrice != null and item.totalDeliveryPrice != ''" >
        total_delivery_price = #{item.totalDeliveryPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderPrice != null and item.totalOrderPrice != ''" >
        total_order_price = #{item.totalOrderPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalProductPreferPrice != null and item.totalProductPreferPrice != ''" >
        total_product_prefer_price = #{item.totalProductPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalDeliveryPreferPrice != null and item.totalDeliveryPreferPrice != ''" >
        total_delivery_prefer_price = #{item.totalDeliveryPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderPreferPrice != null and item.totalOrderPreferPrice != ''" >
        total_order_prefer_price = #{item.totalOrderPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderPayPrice != null and item.totalOrderPayPrice != ''" >
        total_order_pay_price = #{item.totalOrderPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderNeedPayPrice != null and item.totalOrderNeedPayPrice != ''" > 
        total_order_need_pay_price = #{item.totalOrderNeedPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.workShopId != null and item.workShopId != '' " >
        work_shop_id = #{item.workShopId,jdbcType=BIGINT},
      </if>
      <if test="item.referenceOrderNo != null and item.referenceOrderNo != '' " >
        reference_order_no = #{item.referenceOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.referenceOrderType != null and item.referenceOrderType != ''" >
        reference_order_type = #{item.referenceOrderType,jdbcType=NVARCHAR},
      </if>
      <if test="item.status != null and item.status != ''" >
        status = #{item.status,jdbcType=NVARCHAR},
      </if>
      <if test="item.createTime != null and item.createTime != ''" >
        create_time = #{item.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.updateTime != null and item.updateTime != ''" >
        update_time = #{item.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.creator != null and item.creator != ''" >
        creator = #{item.creator,jdbcType=NVARCHAR},
      </if>
      <if test="item.remark != null and item.remark != ''" >
        remark = #{item.remark,jdbcType=NVARCHAR},
      </if>
      <if test="item.workshopName != null and item.workshopName != ''" >
        work_shop_name = #{item.workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="item.plateNumber != null and item.plateNumber != ''" >
        plate_number = #{item.plateNumber,jdbcType=NVARCHAR},
      </if>
      <if test="item.address != null and item.address != ''" >
        address = #{item.address,jdbcType=NVARCHAR},
      </if>
      <if test="item.receiveUserName != null and item.receiveUserName != ''" >
        receive_user_name = #{item.receiveUserName,jdbcType=NVARCHAR},
      </if>
      <if test="item.receivePhoneNo != null and item.receivePhoneNo != ''" >
        receive_phone_no = #{item.receivePhoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.effectiveTime != null and item.effectiveTime != ''" >
        effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.type != null and item.type != ''" >
        type = #{item.type,jdbcType=NVARCHAR},
      </if>
      <if test="item.billId != null and item.billId != ''" >
        bill_id = #{item.billId,jdbcType=NVARCHAR},
      </if>
      <if test="item.sndType != null and item.sndType != ''" >
        snd_type = #{item.sndType,jdbcType=NVARCHAR},
      </if>
       <if test="item.phoneNo != null and item.phoneNo != ''" >
        phone_no = #{item.phoneNo,jdbcType=NVARCHAR},
      </if>
        <if test="item.serviceTime != null and item.serviceTime != ''" >
	    service_time =  #{item.serviceTime,jdbcType=TIMESTAMP},
	   </if>
	   <if test="item.invalidTime != null and item.invalidTime != ''" >
	    invalid_time =  #{item.invalidTime,jdbcType=TIMESTAMP},
	    </if>
	  <if test="item.regionName != null and item.regionName != ''" >
        region_name = #{item.regionName,jdbcType=NVARCHAR},
      </if>
      <if test="item.isExport != null and item.isExport != ''" >
        is_export = #{item.isExport,jdbcType=INTEGER},
      </if>
       <if test="item.carType != null and item.carType != ''" >
        car_type = #{item.carType,jdbcType=NVARCHAR},
      </if>
      <if test="item.preferentialTypePrice != null and item.preferentialTypePrice != ''" >
        preferential_type_price = #{item.preferentialTypePrice,jdbcType=NVARCHAR},
      </if>
      
      <if test="item.deliveryTime != null and item.deliveryTime != ''" >
        delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
      </if>
       <if test="item.oilInjection != null and item.oilInjection != ''" >
        oil_injection = #{item.oilInjection,jdbcType=NVARCHAR},
      </if>
    </set>
    where 
    	<if test="orderSourceType==1">
    	id = #{item.id,jdbcType=NVARCHAR}
    	and
    	</if>
    	<if test="orderSourceType==2">
    	order_no = #{item.orderNo,jdbcType=NVARCHAR}
    	and
    	</if>
     1=1
    </foreach>
  </update>
  
  
   <select id="queryOrderLstForThridAppUse" parameterType="map" resultMap="BaseResultMap">
    	select * from wx_t_order
    	where
   		 <if test="lstOrders!=null">
   		 	<if test="orderSourceType==1">
	    	plate_number in
	    	<foreach item="item" index="index" collection="lstOrders" open="(" separator="," close=")">  
				 #{item.plateNumber,jdbcType=NVARCHAR}  
			</foreach> 
			and ((bill_id IS NOT NULL and bill_id !='') or status = 5)
			<if test="orderType==1">
			and order_type='DP'
			</if>
			<if test="orderType==2">
			and order_type='DA'
			</if>
			and 
			</if>
			<if test="orderSourceType==2">
			order_no in
			<foreach item="item" index="index" collection="lstOrders" open="(" separator="," close=")">  
				 #{item.orderNo,jdbcType=NVARCHAR}  
			</foreach> 
			and ((bill_id IS NOT NULL and bill_id !='') or status = 5)
			<if test="orderType==1">
			and order_type='DP'
			</if>
			<if test="orderType==2">
			and order_type='DA'
			</if>
			and
			</if>
	    </if>
    	<![CDATA[  CONVERT(varchar(100), getdate(), 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
	    1 = 1
  </select>
  
  <!-- by bo.liu 0907 end-->
  <!-- by bo.liu 0912 start -->
	<!-- 批量插入数据   采购订单-->
	<insert id="insertBatchOrderFromExcelData" useGeneratedKeys="true" keyProperty="id"  parameterType="java.util.List" >
		<foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
	    insert into wx_t_order
	    <trim prefix="(" suffix=")" suffixOverrides="," >
	      <if test="item.id != null" >
	        id,
	      </if>
	      <if test="item.orderNo != null" >
	        order_no,
	      </if>
	      <if test="item.orderType != null" >
	        order_type,
	      </if>
	      <if test="item.buyUserNo != null" >
	        buy_user_no,
	      </if>
	      <if test="item.buyUserName != null" >
	        buy_user_name,
	      </if>
	      <if test="item.buyerCardId != null" >
	        buyer_card_id,
	      </if>
	      <if test="item.invoiceInfo != null" >
	        invoice_info,
	      </if>
	      <if test="item.source != null" >
	        source,
	      </if>
	      <if test="item.sourceId != null" >
	        source_id,
	      </if>
	      <if test="item.payType != null" >
	        pay_type,
	      </if>
	      <if test="item.payState != null" >
	        pay_state,
	      </if>
	      <if test="item.totalProductPrice != null" >
	        total_product_price,
	      </if>
	      <if test="item.totalDeliveryPrice != null" >
	        total_delivery_price,
	      </if>
	      <if test="item.totalOrderPrice != null" >
	        total_order_price,
	      </if>
	      <if test="item.totalProductPreferPrice != null" >
	        total_product_prefer_price,
	      </if>
	      <if test="item.totalDeliveryPreferPrice != null" >
	        total_delivery_prefer_price,
	      </if>
	      <if test="item.totalOrderPreferPrice != null" >
	        total_order_prefer_price,
	      </if>
	      <if test="item.totalOrderPayPrice != null" >
	        total_order_pay_price,
	      </if>
	      <if test="item.totalOrderNeedPayPrice != null" >
	        total_order_need_pay_price,
	      </if>
	      <if test="item.workShopId != null" >
	        work_shop_id,
	      </if>
	      <if test="item.referenceOrderNo != null" >
	        reference_order_no,
	      </if>
	      <if test="item.referenceOrderType != null" >
	        reference_order_type,
	      </if>
	      <if test="item.status != null" >
	        status,
	      </if>
	      <if test="item.createTime != null" >
	        create_time,
	      </if>
	      <if test="item.updateTime != null" >
	        update_time,
	      </if>
	      <if test="item.creator != null" >
	        creator,
	      </if>
	      <if test="item.remark != null" >
	        remark,
	      </if>
	      <if test="item.workshopName != null" >
	        work_shop_name,
	      </if>
	      <if test="item.plateNumber != null" >
	        plate_number,
	      </if>
	      <if test="item.address != null" >
	        address,
	      </if>
	      <if test="item.receiveUserName != null" >
	        receive_user_name,
	      </if>
	      <if test="item.receivePhoneNo != null" >
	        receive_phone_no,
	      </if>
	      <if test="item.effectiveTime != null" >
	        effective_time,
	      </if>
	      <if test="item.type != null" >
	        type,
	      </if>
	      <if test="item.billId != null" >
	        bill_id,
	      </if>
	      <if test="item.sndType != null" >
	        snd_type,
	      </if>
	       <if test="item.phoneNo != null" >
	        phone_no,
	      </if>
	      <if test="item.serviceTime != null" >
	        service_time,
	      </if>
	      <if test="item.invalidTime != null" >
	        invalid_time,
	      </if>
	       <if test="item.regionName != null" >
            region_name,
     	  </if>
     	  <if test="item.isExport != null" >
        	is_export,
      	  </if>
      	  <if test="item.carType != null" >
        	car_type,
      	 </if>
      	  <if test="item.preferentialTypePrice != null" >
            preferential_type_price,
          </if>
         <if test="item.deliveryTime != null" >
        	delivery_time,
      	</if>
      	<if test="item.oilInjection != null" >
            oil_injection,
        </if>
	    </trim>
	    <trim prefix="values (" suffix=")" suffixOverrides="," >
	      <if test="item.id != null" >
	        #{item.id,jdbcType=BIGINT},
	      </if>
	      <if test="item.orderNo != null" >
	        #{item.orderNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.orderType != null" >
	        #{item.orderType,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.buyUserNo != null" >
	        #{item.buyUserNo,jdbcType=BIGINT},
	      </if>
	      <if test="item.buyUserName != null" >
	        #{item.buyUserName,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.buyerCardId != null" >
	        #{item.buyerCardId,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.invoiceInfo != null" >
	        #{item.invoiceInfo,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.source != null" >
	        #{item.source,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.sourceId != null" >
	        #{item.sourceId,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.payType != null" >
	        #{item.payType,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.payState != null" >
	        #{item.payState,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.totalProductPrice != null" >
	        #{item.totalProductPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalDeliveryPrice != null" >
	        #{item.totalDeliveryPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalOrderPrice != null" >
	        #{item.totalOrderPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalProductPreferPrice != null" >
	        #{item.totalProductPreferPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalDeliveryPreferPrice != null" >
	        #{item.totalDeliveryPreferPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalOrderPreferPrice != null" >
	        #{item.totalOrderPreferPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalOrderPayPrice != null" >
	        #{item.totalOrderPayPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.totalOrderNeedPayPrice != null" >
	        #{item.totalOrderNeedPayPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="item.workShopId != null" >
	        #{item.workShopId,jdbcType=BIGINT},
	      </if>
	      <if test="item.referenceOrderNo != null" >
	        #{item.referenceOrderNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.referenceOrderType != null" >
	        #{item.referenceOrderType,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.status != null" >
	        #{item.status,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.createTime != null" >
	        #{item.createTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.updateTime != null" >
	        #{item.updateTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.creator != null" >
	        #{item.creator,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.remark != null" >
	        #{item.remark,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.workshopName != null" >
	        #{item.workshopName,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.plateNumber != null" >
	        #{item.plateNumber,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.address != null" >
	        #{item.address,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.receiveUserName != null" >
	        #{item.receiveUserName,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.receivePhoneNo != null" >
	        #{item.receivePhoneNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.effectiveTime != null" >
	        #{item.effectiveTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.type != null" >
	        #{item.type,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.billId != null" >
	        #{item.billId,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.sndType != null" >
	        #{item.sndType,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.phoneNo != null" >
	        #{item.phoneNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="item.serviceTime != null" >
	        #{item.serviceTime,jdbcType=TIMESTAMP},
	      </if>
	       <if test="item.invalidTime != null" >
	        #{item.invalidTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="item.regionName != null" >
             #{item.regionName,jdbcType=NVARCHAR},
     	  </if>
      	  <if test="item.isExport != null" >
        	 #{item.isExport,jdbcType=INTEGER},
      	  </if>
      	  <if test="item.carType != null" >
       		  #{item.carType,jdbcType=NVARCHAR},
     	 </if>
     	  <if test="item.preferentialTypePrice != null" >
        	  #{item.preferentialTypePrice,jdbcType=NVARCHAR},
     	 </if>
     	 <if test="item.deliveryTime != null" >
        	 #{item.deliveryTime,jdbcType=TIMESTAMP},
     	 </if>
     	 <if test="item.oilInjection != null" >
            #{item.oilInjection,jdbcType=NVARCHAR},
        </if>
	    </trim>
	    </foreach>
  </insert>
	

	<select id="getShopOrderByIds" resultType="string" parameterType="list">
	SELECT DISTINCT source FROM wx_t_order where  source like '%B2B%'  
	 AND id in 
	 <trim prefix="(" suffix=")" suffixOverrides="," >
		 <foreach collection="list" item="item" index="index" >
		 	${item},
		 </foreach>
	 </trim>
	
	</select>
	
	
	<!-- 批量更新数据  针对发货订单 -->
 <update id="updateBatchOrderFromExcelData"  parameterType="java.util.List" >
   <foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
    update wx_t_order
    <set >
     <if test="item.orderNo != null" >
        order_no = #{item.orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.orderType != null" >
        order_type = #{item.orderType,jdbcType=NVARCHAR},
      </if>
      <if test="item.buyUserNo != null" >
        buy_user_no = #{item.buyUserNo,jdbcType=BIGINT},
      </if>
      <if test="item.buyUserName != null" >
        buy_user_name = #{item.buyUserName,jdbcType=NVARCHAR},
      </if>
      <if test="item.buyerCardId != null" >
        buyer_card_id = #{item.buyerCardId,jdbcType=NVARCHAR},
      </if>
      <if test="item.invoiceInfo != null" >
        invoice_info = #{item.invoiceInfo,jdbcType=NVARCHAR},
      </if>
      <if test="item.source != null" >
        source = #{item.source,jdbcType=NVARCHAR},
      </if>
      <if test="item.sourceId != null" >
        source_id = #{item.sourceId,jdbcType=NVARCHAR},
      </if>
      <if test="item.payType != null" >
        pay_type = #{item.payType,jdbcType=NVARCHAR},
      </if>
      <if test="item.payState != null" >
        pay_state = #{item.payState,jdbcType=NVARCHAR},
      </if>
      <if test="item.totalProductPrice != null" >
        total_product_price = #{item.totalProductPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalDeliveryPrice != null" >
        total_delivery_price = #{item.totalDeliveryPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderPrice != null" >
        total_order_price = #{item.totalOrderPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalProductPreferPrice != null" >
        total_product_prefer_price = #{item.totalProductPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalDeliveryPreferPrice != null" >
        total_delivery_prefer_price = #{item.totalDeliveryPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderPreferPrice != null" >
        total_order_prefer_price = #{item.totalOrderPreferPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderPayPrice != null" >
        total_order_pay_price = #{item.totalOrderPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.totalOrderNeedPayPrice != null" >
        total_order_need_pay_price = #{item.totalOrderNeedPayPrice,jdbcType=NUMERIC},
      </if>
      <if test="item.workShopId != null" >
        work_shop_id = #{item.workShopId,jdbcType=BIGINT},
      </if>
      <if test="item.referenceOrderNo != null" >
        reference_order_no = #{item.referenceOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.referenceOrderType != null" >
        reference_order_type = #{item.referenceOrderType,jdbcType=NVARCHAR},
      </if>
      <if test="item.status != null" >
        status = #{item.status,jdbcType=NVARCHAR},
      </if>
      <if test="item.createTime != null" >
        create_time = #{item.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.updateTime != null" >
        update_time = #{item.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.creator != null" >
        creator = #{item.creator,jdbcType=NVARCHAR},
      </if>
      <if test="item.remark != null" >
        remark = #{item.remark,jdbcType=NVARCHAR},
      </if>
      <if test="item.workshopName != null" >
        work_shop_name = #{item.workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="item.plateNumber != null" >
        plate_number = #{item.plateNumber,jdbcType=NVARCHAR},
      </if>
      <if test="item.address != null" >
        address = #{item.address,jdbcType=NVARCHAR},
      </if>
      <if test="item.receiveUserName != null" >
        receive_user_name = #{item.receiveUserName,jdbcType=NVARCHAR},
      </if>
      <if test="item.receivePhoneNo != null" >
        receive_phone_no = #{item.receivePhoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.effectiveTime != null" >
        effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.type != null" >
        type = #{item.type,jdbcType=NVARCHAR},
      </if>
      <if test="item.billId != null" >
        bill_id = #{item.billId,jdbcType=NVARCHAR},
      </if>
      <if test="item.sndType != null" >
        snd_type = #{item.sndType,jdbcType=NVARCHAR},
      </if>
       <if test="item.phoneNo= null" >
        phone_no = #{item.phoneNo,jdbcType=NVARCHAR},
      </if>
      <if test="item.serviceTime != null" >
	    service_time =  #{item.serviceTime,jdbcType=TIMESTAMP},
	   </if>
	   <if test="item.invalidTime != null" >
	    invalid_time =  #{item.invalidTime,jdbcType=TIMESTAMP},
	    </if>
	  <if test="item.regionName != null" >
        region_name = #{item.regionName,jdbcType=NVARCHAR},
      </if>
      <if test="item.isExport != null" >
        is_export = #{item.isExport,jdbcType=INTEGER},
      </if>
       <if test="item.carType != null" >
        car_type = #{item.carType,jdbcType=NVARCHAR},
      </if>
       <if test="item.preferentialTypePrice != null" >
        preferential_type_price = #{item.preferentialTypePrice,jdbcType=NVARCHAR},
      </if>
      <if test="item.deliveryTime != null" >
        delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.oilInjection != null" >
          oil_injection =  #{item.oilInjection,jdbcType=NVARCHAR},
        </if>
    </set>
    where id = #{item.id,jdbcType=NVARCHAR}
    </foreach>
  </update>
  
  
   <select id="getImportOrderByPlateNumber" parameterType="map" resultMap="BaseResultMap">
   	select *
   	from
   		wx_t_order tt_order
   	where
   		tt_order.plate_number = #{plateNumber,jdbcType=NVARCHAR}
   		and
   		(tt_order.status = #{status,jdbcType=NVARCHAR} or tt_order.status = #{status4,jdbcType=NVARCHAR} or tt_order.status = #{status5,jdbcType=NVARCHAR})
   		and
   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
    	
    	1=1
   		
   </select>
   
    <select id="getOrdersByMoreConditionForDDBXInterface" parameterType="map" resultMap="BaseResultMap">
   	select *
   	from
   		wx_t_order tt_order
   	where
   		<if test="orderSourceType==1">
   		tt_order.plate_number = #{plateNumber,jdbcType=NVARCHAR}
   		and
   		</if>
   		<if test="orderSourceType==2">
   		tt_order.order_no = #{orderCode,jdbcType=NVARCHAR}
   		and
   		</if>
   		
   		(
   		<if test="status!=null">
   		tt_order.status = #{status}  
   		and
   		</if>
   		<if test="status4!=null">
   		tt_order.status = #{status4,jdbcType=NVARCHAR} 
   		or
   		</if>
   		<if test="status5!=null">
   		 tt_order.status = #{status5,jdbcType=NVARCHAR}
   		 or
   		</if>
   		 1=1
   		)
   		and
   		<if test="orderVoType!=null">
   		 tt_order.order_type = #{orderVoType,jdbcType=NVARCHAR}
   		 and
   		</if>
   		<if test="orderSource!=null and orderSource!='' ">
   		 tt_order.source = #{orderSource,jdbcType=NVARCHAR}
   		 and
   		</if>
   		
   		
   		
   		<if test="getBillId!=null">
   	     ((bill_id IS NOT NULL and bill_id !='') or status = 5)
		 and
   		</if>
   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
   		<if test="nowdate!=null and orderVoType!='DA'">
    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
    	</if>
    	<if test="ddxbnewflag!=null">
    	 tt_order.remaining_service_times >0
    	 and
    	</if>
    	
    	
    	1=1
    order by tt_order.create_time ASC <!-- , tt_order.settlement_model_type desc -->
   		
   </select>
   
   
   
    <select id="getDDXBOrdersByCondition" parameterType="map" resultMap="BaseResultMap">
	   select tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		<if test="orderSource!=null">
	   		tt_order.source = #{orderSource,jdbcType=NVARCHAR}
	   		and
	   		</if>
	   		<!-- 后续补上条件过滤 -->
	   		<if test="status!=null">
   				tt_order.status = #{status,jdbcType=NVARCHAR}  
   			and
   			</if>
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
	   		
	   		<if test="orderSource==null">
	   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
	    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
	    	</if>
	    	
	    	1=1
	    order by tt_order.create_time desc
   </select>
   
   <!-- 分页开始 -->
   <select id="getDDXBOrdersByConditionNew" parameterType="map" resultMap="BaseResultMap">
	   select top ${pageSize} *
	   from
	   (select row_number() over(order by tt_order.id) as rownumber,  tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		<if test="orderSource!=null">
	   		tt_order.source = #{orderSource,jdbcType=NVARCHAR}
	   		and
	   		</if>
	   		<!-- 后续补上条件过滤 -->
	   		<if test="status!=null">
   				tt_order.status = #{status,jdbcType=NVARCHAR}  
   			and
   			</if>
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
	   		
	   		<if test="orderSource==null">
	   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
	    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
	    	</if>
	    	1=1) as t where t.rownumber > #{start, jdbcType=INTEGER} order by t.create_time desc
   </select>
   
   <select id="countDDXBOrdersByConditionByPageNew" parameterType="map" resultType="int">
	   select
	   	count(1) 
	    from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		<if test="orderSource!=null">
	   		tt_order.source = #{orderSource,jdbcType=NVARCHAR}
	   		and
	   		</if>
	   		<!-- 后续补上条件过滤 -->
	   		<if test="status!=null">
   				tt_order.status = #{status,jdbcType=NVARCHAR}  
   			and
   			</if>
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
	   		
	   		<if test="orderSource==null">
	   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
	    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
	    	</if>
	    	1=1
   </select>
   <!-- 分页结束 -->
   
   
    <select id="getImportDDBXOrderByPlateNumber" parameterType="map" resultMap="BaseResultMap">
   	select *
   	from
   		wx_t_order tt_order
   	where
   		tt_order.plate_number = #{plateNumber,jdbcType=NVARCHAR}
   		and
   		<if test="status!=null and status!='' ">
   			<if test="status!='0'.toString() ">
   			tt_order.status = #{status,jdbcType=NVARCHAR}  
   		and
   			</if>
   			<if test="status=='0'.toString() ">
   			(tt_order.status = #{status,jdbcType=NVARCHAR} 
   			 or 
   			 tt_order.status = '1'
   			 or
   			 tt_order.status = '3'
   			 )
   		and
   			</if>
   		</if>
   		<if test="orderType!=null">
   			tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
   		and
   		</if>
   		<if test="source!=null and source!='' ">
   			tt_order.source = #{source,jdbcType=NVARCHAR}
   		and
   		</if>
   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
    	<!-- <![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]> -->
    	 <![CDATA[tt_order.invalid_time>= #{nowdate} and]]>
    	
    	1=1
    order by tt_order.create_time desc
   		
   </select>
   
   <select id="getOrderIdsByCondition" parameterType="map" resultType="Long">
   	select tt_order.id
   	from
   		wx_t_order tt_order
   	where
   		<if test="orderIds!=null">
   			tt_order.id in 
   			<foreach item="id" index="index" collection="orderIds" open="(" separator="," close=")"> 
				 #{id}  
			</foreach> 
   			and
   		</if>
   		<if test="isExport!=null">
   			tt_order.is_export = #{isExport}
   			and
   		</if>
   		<if test="orderType!=null">
   			tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
   		and
   		</if>
   		<if test="orderSource!=null">
   		   tt_order.source = #{orderSource,jdbcType=NVARCHAR}
   		and
   		</if>
   		<if test="settlementModelType!=null">
           tt_order.settlement_model_type = #{settlementModelType,jdbcType=NVARCHAR}
        and
        </if> 
   		<!-- <![CDATA[  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), effective_time, 23)  and ]]> -->
   		
   		
    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
    	
    	1=1
        and tt_order.status = '3'
    order by tt_order.create_time desc
   		
   </select>
   
   
   <update id="updateBatchOrderExportStatus" parameterType="map" >
    	update wx_t_order
    	set is_export = #{isExport}
    	where
    	<if test="needUpdateOrderIds!=null">
	    	id in
	    	<foreach item="id" index="index" collection="needUpdateOrderIds" open="(" separator="," close=")">  
				 #{id}  
			</foreach> 
			and
	    </if>
	    1 = 1
    </update>
   
   <select id="selectForDDXB" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select tt_order.*,t_pp.per_hours_subsidy,t_org.organization_name org_name 
		from wx_t_order tt_order
		LEFT JOIN wx_t_preferential_type t_pp
		ON t_pp.type_code = tt_order.type
		LEFT JOIN wx_t_organization t_org 
		ON t_org.id = tt_order.partner_id
  		where tt_order.id =#{id,jdbcType=BIGINT}
	</select>
   
   
   <select id="getOrdersByOrderIds" parameterType="map" resultMap="BaseResultMap">
	    select tt_order.id,tt_order.oil_injection from wx_t_order tt_order
		where 
		<if test="orderLst != null">
			tt_order.id in
	    	<foreach item="item" index="index" collection="orderLst" open="(" separator="," close=")">
	    		#{item}
	    	</foreach>
	    	and 
		</if>
		tt_order.oil_injection is not NULL
		and
		1 = 1  
	</select>
	
	<select id="getFWOrdersByCondition" parameterType="map" resultMap="BaseResultMap">
		<if test="orderSource==null">
	    select tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
	    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
	    	1=1
	    
	    UNION
	    	select tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
        ON tt_order1.type = tt_pre_type1.type_code
	   	where
	   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		tt_order1.source = 'OD12'
	   		and
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order1.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order1.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order1.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order1.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order1.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order1.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order1.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order1.delivery_time = #{orderConditions.deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order1.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
	    	1=1
	    	
	     UNION
	    	select tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
        ON tt_order1.type = tt_pre_type1.type_code
	   	where
	   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		tt_order1.source = 'JSBOD'
	   		and
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order1.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order1.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order1.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order1.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order1.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order1.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order1.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order1.delivery_time = #{orderConditions.deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order1.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
	    	1=1
	    order by tt_order.create_time desc
		</if>
		
		<if test="orderSource!=null">
			select tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
        ON tt_order1.type = tt_pre_type1.type_code
	   	where
	   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		tt_order1.source = #{orderSource,jdbcType=NVARCHAR}
	   		and
   			<if test="orderConditions!=null">
   				<if test="orderConditions.plateNumber!=null" >
   					tt_order1.plate_number like '%' + #{orderConditions.plateNumber} + '%' <!-- = #{orderConditions.plateNumber,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.buyUserName!=null" >
   					tt_order1.buy_user_name like  '%' + #{orderConditions.buyUserName} + '%' <!-- = #{orderConditions.buyUserName,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="orderConditions.preferentialTypePrice!=null" >
   					tt_order1.preferential_type_price like '%' + #{orderConditions.preferentialTypePrice} + '%' <!-- = #{orderConditions.preferentialTypePrice,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderConditions.regionName!=null" >
   					tt_order1.region_name like '%' + #{orderConditions.regionName} + '%'  <!-- = #{orderConditions.regionName,jdbcType=NVARCHAR}   -->
   				and
   				</if>
   				<if test="orderEffectiveTime!=null" >
   					tt_order1.effective_time = #{orderEffectiveTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.receiveUserName!=null" >
   					tt_order1.receive_user_name like '%' + #{orderConditions.receiveUserName} + '%' <!-- = #{orderConditions.receiveUserName,jdbcType=NVARCHAR} -->  
   				and
   				</if>
   				<if test="orderConditions.receiveUserPhone!=null" >
   					tt_order1.receive_phone_no like '%' + #{orderConditions.receiveUserPhone} + '%' <!-- = #{orderConditions.receiveUserPhone,jdbcType=NVARCHAR}  --> 
   				and
   				</if>
   				<if test="deliveryTime!=null" >
   					tt_order1.delivery_time = #{orderConditions.deliveryTime,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderConditions.sendStatus!=null" >
   					tt_order1.status = #{orderConditions.sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			</if>
   			<if test="orderSource=='DDBX'">
   			<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
   			</if>
	    	1=1
	    	order by tt_order1.create_time desc
		</if>
		
	</select>
	
	<select id="getOrderStatusByCondition" parameterType="map" resultMap="BaseResultMap">
		select tt_order1.*
		from
		wx_t_order tt_order1
		where
			<if test="orderSourceType==1">
				<if test="plateNumber!=null">
				   tt_order1.plate_number = #{plateNumber}
				   and
				</if>
			</if>
			<if test="orderSourceType==2">
				<if test="orderNo!=null">
				   tt_order1.order_no = #{orderNo}
				   and
				</if>
			</if>
		1=1
		order by tt_order1.create_time desc
	</select>
	
	
	<select id="getOrderContainPartnerInfoByOrderId" parameterType="map" resultMap="BaseResultMap">
		select  
				DISTINCT tt_order.id,
				tt_order.*,
				tt_org.id as org_id,
				tt_org.organization_name AS org_name,
		  	    tt_workshop.work_shop_name AS order_workshopname, 
		  	    tt_user.ch_name AS creator_display_name,
		  	    onstk.stock_out_no,instk.stock_in_no,
		  	    (select p.codename from wx_t_properties p where p.codetype = 'order_status' and p.code = tt_order.status) as status_meaning 
		from 	wx_t_order tt_order 
				LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
                LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
                LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
                LEFT JOIN wx_t_user tt_user ON tt_order.creator = tt_user.user_id
                left join wx_t_out_stock onstk on tt_order.order_no = onstk.order_no
 				left join wx_t_in_stock instk on tt_order.order_no = instk.order_no
         where
         	tt_order.id = #{orderId}
         	and
         1= 1
	</select>
	
	 <update id="updateOrderByCondition" parameterType="map" >
    	update wx_t_order
    	set 
    	  <if test="totalProductPrice!=null">
    	  	total_order_price = #{totalProductPrice},
    	  	total_product_price = #{totalProductPrice}
    	  </if>
    	  <if test="totalProductPrice==null">
	    	  <if test="status!=null">
	    	  status = #{status}
	    	  </if>
	    	  <if test="orderType!=null">
	    	    , order_type = #{orderType}
	    	  </if>
    	  </if>
    	  
    	where
    	  id = #{orderId}
    	  and
	    1 = 1
    </update>
    
    
    <update id="batchUpdateOrderLstByOrderNo"  parameterType="map" >
	   <foreach collection="orderLst" item="item" index="index" open="begin" close="end;" separator=";">
				update wx_t_order 
				<set>
					 update_time = getDate(), 
					<if test="item.billId != null and item.billId != '' " >
	        			bill_id =  #{item.billId},
	      			</if>
	      			<if test="item.deliveryTime != null" >
				       delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
				    </if>
				    <if test="item.status != null" >
				       status = #{item.status,jdbcType=NVARCHAR}
				    </if>
				</set>
				where order_no = #{item.orderNo}
	   </foreach>
    </update>
	
	<!-- flushCache="true"每次执行，不需要从缓存中去获取 -->
	<select  id="getOrderNoSequence" parameterType="map" resultType="Long"  flushCache="true">
	   SELECT NEXT VALUE FOR
	    <if test="morderNoType==1">	
	    	 OrderDDXBSequenceNo
	    </if>
	    <if test="morderNoType==2">
	    	OrderDDFWSequenceNo
	    </if>
	     <if test="morderNoType==3">
	    	 OrderASequenceNo
	    </if>
	     <if test="morderNoType==4">
	    	 OrderPSequenceNo
	    </if>
	     <if test="morderNoType==5">
	    	 OrderSPSequenceNo
	    </if>
	     <if test="morderNoType==6">
	    	 OrderJSBSequenceNo
	    </if>
	     <if test="morderNoType==7">
	    	 OrderSPOWNERSequenceNo
	    </if>
	</select>
	
	<select id="getFWBOrdersByCondition" parameterType="com.chevron.pms.model.OrderDDBXCondition" resultMap="BaseResultMap">
	<!-- 数据权限开启 -->
	select v.* from (
	<if test="isOpernCustomMybatisInterceptor==1">
	    select
	    tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy,t_org.organization_name org_name from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
        LEFT JOIN wx_t_organization t_org 
        ON t_org.id = tt_order.partner_id
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		<!-- add by bo.liu 0102 -->
            <if test="regionIds!=null">
                 tt_order.region_id in 
                 <foreach item="regionids" index="index" collection="regionIds" open="(" separator="," close=")">  
                 '${regionids}'  
            </foreach> 
            and
            </if>
	   		<!-- <if test="status!=null and status != ''"> -->
	   		<if test="sendStatus==null or sendStatus=='' ">
   				 (tt_order.status = #{status,jdbcType=NVARCHAR}  or tt_order.status='0' or tt_order.status='1'  or tt_order.status='6'  or tt_order.status='9') 
   			and
   			</if>
   			<if test="sendStatus!=null">
   				 (tt_order.status = #{sendStatus,jdbcType=NVARCHAR}) 
   			and
   			</if>
   			<!-- 关键字搜索 -->
	   	    <!-- <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField != ''">
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	</if>
	   	    <!-- </if> -->
	   		
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
				<if test="plateNumber!=null and plateNumber!=''" >
					tt_order.plate_number like '%' + #{plateNumber} + '%' 
				and
				</if>
				<if test="buyUserName!=null and buyUserName != ''" >
					tt_order.buy_user_name like  '%' + #{buyUserName} + '%' 
				and
				</if>
				<if test="preferentialTypePrice!=null and preferentialTypePrice != ''" >
					tt_order.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
				and
				</if>
				<if test="regionName!=null and regionName != ''" >
					tt_order.region_name like '%' + #{regionName} + '%'  
				and
				</if>
				<if test="orderEffectiveDate!=null and orderEffectiveDate != ''" >
					tt_order.effective_time = #{orderEffectiveDate,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="receiveUserName!=null and receiveUserName != ''" >
					tt_order.receive_user_name like '%' + #{receiveUserName} + '%'
				and
				</if>
				<if test="receiveUserPhone!=null and receiveUserPhone != ''" >
					tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%' 
				and
				</if>
				<if test="deliveryTime!=null and deliveryTime != ''" >
					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="sendStatus!=null" >
					tt_order.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
				
				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order.create_time<#{orderCreateEndTime} AND]]>
				</if>
				
				<if test="orderSource!=null" >
                    tt_order.source = #{orderSource,jdbcType=NVARCHAR}  
                and
                </if>
                <if test="orderCode!=null" >
                    tt_order.order_no = #{orderCode,jdbcType=NVARCHAR}  
                and
                </if>
			</if>
	    	1=1
	 </if>	
	 ) v where ((1=1 $Permission_Clause$) or exists (select 1 from wx_t_business_between_sp_conf bbsc join wx_t_bus_bet_sp_conf_sp bbscs on bbscs.config_id=bbsc.id
		 	where bbsc.partner_id=$currentPartnerId$ and bbsc.business_name='ServiceOrder' and bbsc.enable_flag=1 and bbscs.partner_id=v.partner_id))
	 <!-- 数据权限没有开启 -->
	 <if test="isOpernCustomMybatisInterceptor==2">
	   select top ${limit} *
	   from
	   (select row_number() over(order by ${order}) as rownumber,  tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		<if test="status!=null and status != ''">
   				tt_order.status = #{status,jdbcType=NVARCHAR}  
   			and
   			</if>
   			<!-- 关键字搜索 -->
	   	    <!-- <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField != ''">
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	</if>
	   	   <!--  </if> -->
	   		
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
				<if test="plateNumber!=null and plateNumber != ''" >
					tt_order.plate_number like '%' + #{plateNumber} + '%' 
				and
				</if>
				<if test="buyUserName!=null and buyUserName != ''" >
					tt_order.buy_user_name like  '%' + #{buyUserName} + '%'  
				and
				</if>
				<if test="preferentialTypePrice!=null and preferentialTypePrice!=''" >
					tt_order.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
				and
				</if>
				<if test="regionName!=null and regionName!= ''" >
					tt_order.region_name like '%' + #{regionName} + '%'  
				and
				</if>
				<if test="orderEffectiveDate!=null and orderEffectiveDate!=''" >
					tt_order.effective_time = #{orderEffectiveDate,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="receiveUserName!=null and receiveUserName!=''" >
					tt_order.receive_user_name like '%' + #{receiveUserName} + '%'
				and
				</if>
				<if test="receiveUserPhone!=null and receiveUserPhone!=''" >
					tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="deliveryTime!=null and deliveryTime!=''" >
					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="sendStatus!=null and sendStatus!='' " >
					tt_order.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="orderCreateTime!=null and orderCreateTime!=''" >
					SUBSTRING(CONVERT(CHAR(19), tt_order.create_time, 120),1,10) = #{orderCreateTime,jdbcType=NVARCHAR}  
				and
				</if>
			</if>
	    	1=1) as t where t.rownumber > #{start, jdbcType=INTEGER} 
	</if>
  </select>
  
  
  
  <select id="countGetFWBOrdersByCondition" parameterType="com.chevron.pms.model.OrderDDBXCondition" resultType="Long">
	   select
	   	count(1) 
	    from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		and
	   		<if test="status!=null and status != ''">
   				tt_order.status = #{status,jdbcType=NVARCHAR}  
   			and
   			</if>
   			<!-- 关键字搜索 -->
	   	    <!-- <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField!=''">
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	</if>
	   	    <!-- </if> -->
	   		
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
				<if test="plateNumber!=null and plateNumber!=''" >
					tt_order.plate_number like '%' + #{plateNumber} + '%'  
				and
				</if>
				<if test="buyUserName!=null and buyUserName!=''" >
					tt_order.buy_user_name like  '%' + #{buyUserName} + '%'  
				and
				</if>
				<if test="preferentialTypePrice!=null and preferentialTypePrice!=''" >
					tt_order.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
				and
				</if>
				<if test="regionName!=null and regionName!=''" >
					tt_order.region_name like '%' + #{regionName} + '%'  
				and
				</if>
				<if test="orderEffectiveDate!=null and orderEffectiveDate!=''" >
					tt_order.effective_time = #{orderEffectiveDate,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="receiveUserName!=null and receiveUserName!= ''" >
					tt_order.receive_user_name like '%' + #{receiveUserName} + '%'
				and
				</if>
				<if test="receiveUserPhone!=null and receiveUserPhone!= ''" >
					tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="deliveryTime!=null and deliveryTime!=''" >
					tt_order.delivery_time = #{deliveryTime,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="sendStatus!=null and sendStatus!= ''" >
					tt_order.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="orderCreateTime!=null and orderCreateTime!= ''" >
					SUBSTRING(CONVERT(CHAR(19), tt_order.create_time, 120),1,10) = #{orderCreateTime,jdbcType=NVARCHAR}  
				and
				</if>
			</if>
	    	1=1
   </select>
  
  
  
  <select id="getFWOrdersByConditionNew" parameterType="com.chevron.pms.model.OrderDDBXCondition" resultMap="BaseResultMap">
	select v.* from (	
    <!-- 数据权限开启 -->
	<if test="isOpernCustomMybatisInterceptor==1">
		<if test="orderSource==null">
	    select tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy,t_org.organization_name org_name  from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
        LEFT JOIN wx_t_organization t_org 
        ON t_org.id = tt_order.partner_id
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		<!-- add by bo.liu 0102 -->
            <if test="regionIds!=null">
            and
                 tt_order.region_id in 
                  <foreach item="regionids" index="index" collection="regionIds" open="(" separator="," close=")">  
                       '${regionids}'   
                  </foreach> 
            </if>
	   		<!-- 关键字搜索 -->
	   	    <!-- <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField!= ''">
	   	    	and
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	1=1
	   	    	</if>
	   	  <!--   </if> -->
	   	    
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
	   			and
				<if test="plateNumber!=null and plateNumber!=''" >
					tt_order.plate_number like '%' + #{plateNumber} + '%'  
				and
				</if>
				<if test="buyUserName!=null and buyUserName!=''" >
					tt_order.buy_user_name like  '%' + #{buyUserName} + '%'   
				and
				</if>
				<if test="preferentialTypePrice!=null and preferentialTypePrice!=''" >
					tt_order.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
				and
				</if>
				<if test="regionName!=null and regionName!=''" >
					tt_order.region_name like '%' + #{regionName} + '%'  
				and
				</if>
				<if test="receiveUserName!=null and receiveUserName!=''" >
					tt_order.receive_user_name like '%' + #{receiveUserName} + '%'  
				and
				</if>
				<if test="receiveUserPhone!=null and receiveUserPhone!=''" >
					tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="sendProductTime!=null and sendProductTime!=''" >
					Convert(varchar(10),tt_order.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order.create_time<#{orderCreateEndTime} AND]]>
				</if>
				<if test="sendStatus!=null and sendStatus!=''" >
					tt_order.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
		    	1=1
		    
		    UNION
		    	select tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy,t_org1.organization_name org_name  from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
	        ON tt_order1.type = tt_pre_type1.type_code
	        LEFT JOIN wx_t_organization t_org1 
            ON t_org1.id = tt_order1.partner_id
		   	where
		   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
		   		<!-- add by bo.liu 0102 -->
	            <if test="regionIds!=null">
	            and
	                 tt_order1.region_id in 
                     <foreach item="regionids" index="index" collection="regionIds" open="(" separator="," close=")">  
                         '${regionids}'   
                     </foreach> 
	            </if>
		   		and
		   		(tt_order1.source = 'OD12' or tt_order1.source = 'JSBOD')
		   		and
				<if test="plateNumber!=null and plateNumber!=''" >
					tt_order1.plate_number like '%' + #{plateNumber} + '%' 
				and
				</if>
				<if test="buyUserName!=null and buyUserName!=''" >
					tt_order1.buy_user_name like  '%' + #{buyUserName} + '%'   
				and
				</if>
				<if test="preferentialTypePrice!=null and preferentialTypePrice!=''" >
					tt_order1.preferential_type_price like '%' + #{preferentialTypePrice} + '%'
				and
				</if>
				<if test="regionName!=null and regionName!=''" >
					tt_order1.region_name like '%' + #{regionName} + '%' 
				and
				</if>
				<if test="receiveUserName!=null and receiveUserName!=''" >
					tt_order1.receive_user_name like '%' + #{receiveUserName} + '%'
				and
				</if>
				<if test="receiveUserPhone!=null and receiveUserPhone!=''" >
					tt_order1.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="sendProductTime!=null and sendProductTime!=''" >
					Convert(varchar(10),tt_order1.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order1.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order1.create_time<#{orderCreateEndTime} AND]]>
				</if>
				<if test="sendStatus!=null and sendStatus!=''" >
					tt_order1.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
		    	1=1
		    </if>
		</if>
		
		
		<if test="orderSource!=null">
			select tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy,t_org1.organization_name org_name  from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
        ON tt_order1.type = tt_pre_type1.type_code
        LEFT JOIN wx_t_organization t_org1 
        ON t_org1.id = tt_order1.partner_id
	   	where
	   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
	   		<!-- add by bo.liu 0102 -->
            <if test="regionIds!=null">
            and
                 tt_order1.region_id in 
                 <foreach item="regionids" index="index" collection="regionIds" open="(" separator="," close=")">  
                  '${regionids}'  
                 </foreach> 
            </if>
	   		<!-- 关键字搜索 -->
	   	    <!-- <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField!=''">
	   	    	and
	   	    		(tt_order1.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order1.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order1.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order1.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	1=1
	   	    	</if>
	   	    <!-- </if> -->
	   	    
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
	   		    and
	   			tt_order1.source = #{orderSource,jdbcType=NVARCHAR}
	   			and
   				<if test="plateNumber!=null and plateNumber!=''" >
   					tt_order1.plate_number like '%' + #{plateNumber} + '%'  
   				and
   				</if>
   				<if test="buyUserName!=null and buyUserName!=''" >
   					tt_order1.buy_user_name like  '%' + #{buyUserName} + '%'  
   				and
   				</if>
   				<if test="preferentialTypePrice!=null and preferentialTypePrice!=''" >
   					tt_order1.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
   				and
   				</if>
   				<if test="regionName!=null and regionName!=''" >
   					tt_order1.region_name like '%' + #{regionName} + '%' 
   				and
   				</if>
   				<if test="receiveUserName!=null and receiveUserName!=''" >
   					tt_order1.receive_user_name like '%' + #{receiveUserName} + '%' 
   				and
   				</if>
   				<if test="receiveUserPhone!=null and receiveUserPhone!=''" >
   					tt_order1.receive_phone_no like '%' + #{receiveUserPhone} + '%' 
   				and
   				</if>
   				<if test="sendProductTime!=null and sendProductTime!=''" >
					Convert(varchar(10),tt_order1.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
   				<if test="sendStatus!=null and sendStatus!=''" >
   					tt_order1.status = #{sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order1.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order1.create_time<#{orderCreateEndTime} AND]]>
				</if>
   			
	   			<if test="orderSource=='DDBX'">
	   			</if>
	   		 </if>
	    	1=1
	    </if>
	  </if>
	  ) v where ((1=1 $Permission_Clause$) or exists (select 1 from wx_t_business_between_sp_conf bbsc join wx_t_bus_bet_sp_conf_sp bbscs on bbscs.config_id=bbsc.id
		 	where bbsc.partner_id=$currentPartnerId$ and bbsc.business_name='ServiceOrder' and bbsc.enable_flag=1 and bbscs.partner_id=v.partner_id))
	  
	  <!-- 数据权限控制关闭 -->
	<if test="isOpernCustomMybatisInterceptor==2">
		<if test="orderSource==null">
	   select top ${limit} *
	   from
	   (select row_number() over(order by ${order}) as rownumber,tt_order_temp.*
        from
        (SELECT 
	    tt_order.*,tt_pre_type.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
        ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		
	   		
	   		<!-- 关键字搜索 -->
	   	   <!--  <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and  queryField!= ''">
	   	    	and
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	1=1
	   	    	</if>
	   	   <!--  </if> -->
	   	    
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
	   			and
				<if test="plateNumber!=null" >
					tt_order.plate_number like '%' + #{plateNumber} + '%'  
				and
				</if>
				<if test="buyUserName!=null" >
					tt_order.buy_user_name like  '%' + #{buyUserName} + '%'   
				and
				</if>
				<if test="preferentialTypePrice!=null" >
					tt_order.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
				and
				</if>
				<if test="regionName!=null" >
					tt_order.region_name like '%' + #{regionName} + '%'  
				and
				</if>
				<if test="receiveUserName!=null" >
					tt_order.receive_user_name like '%' + #{receiveUserName} + '%'  
				and
				</if>
				<if test="receiveUserPhone!=null" >
					tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%' 
				and
				</if>
				<if test="sendProductTime!=null" >
					Convert(varchar(10),tt_order.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
				<if test="sendStatus!=null" >
					tt_order.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
		    	1=1
		    
		    UNION
		    	select tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
	        ON tt_order1.type = tt_pre_type1.type_code
		   	where
		   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
		   		and
		   		(tt_order1.source = 'OD12' or tt_order1.source = 'JSBOD')
		   		and
				<if test="plateNumber!=null" >
					tt_order1.plate_number like '%' + #{plateNumber} + '%' 
				and
				</if>
				<if test="buyUserName!=null" >
					tt_order1.buy_user_name like  '%' + #{buyUserName} + '%'   
				and
				</if>
				<if test="preferentialTypePrice!=null" >
					tt_order1.preferential_type_price like '%' + #{preferentialTypePrice} + '%'
				and
				</if>
				<if test="regionName!=null" >
					tt_order1.region_name like '%' + #{regionName} + '%' 
				and
				</if>
				<if test="receiveUserName!=null" >
					tt_order1.receive_user_name like '%' + #{receiveUserName} + '%'
				and
				</if>
				<if test="receiveUserPhone!=null" >
					tt_order1.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="sendProductTime!=null" >
					Convert(varchar(10),tt_order1.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
				<if test="sendStatus!=null" >
					tt_order1.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
		    	1=1 ) AS tt_order_temp
		    </if>
		    ) as  t where t.rownumber > #{start, jdbcType=INTEGER} 
		</if>
		
		
		<if test="orderSource!=null">
	   select top ${limit} *
	   from
	   (
	    select row_number() over(order by ${order}) as rownumber,
	    tt_order1.*,tt_pre_type1.per_hours_subsidy per_hours_subsidy from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
        ON tt_order1.type = tt_pre_type1.type_code
	   	where
	   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
	   		
	   		<!-- 关键字搜索 -->
	   	   <!--  <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField!=''">
	   	    	and
	   	    		(tt_order1.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order1.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order1.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order1.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	1=1
	   	    	</if>
	   	    <!-- </if> -->
	   	    
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
	   			and
	   			tt_order1.source = #{orderSource,jdbcType=NVARCHAR}
	   			and
   				<if test="plateNumber!=null" >
   					tt_order1.plate_number like '%' + #{plateNumber} + '%'  
   				and
   				</if>
   				<if test="buyUserName!=null" >
   					tt_order1.buy_user_name like  '%' + #{buyUserName} + '%'  
   				and
   				</if>
   				<if test="preferentialTypePrice!=null" >
   					tt_order1.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
   				and
   				</if>
   				<if test="regionName!=null" >
   					tt_order1.region_name like '%' + #{regionName} + '%' 
   				and
   				</if>
   				<if test="receiveUserName!=null" >
   					tt_order1.receive_user_name like '%' + #{receiveUserName} + '%' 
   				and
   				</if>
   				<if test="receiveUserPhone!=null" >
   					tt_order1.receive_phone_no like '%' + #{receiveUserPhone} + '%'
   				and
   				</if>
   				<if test="sendProductTime!=null" >
					Convert(varchar(10),tt_order1.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
   				<if test="sendStatus!=null" >
   					tt_order1.status = #{sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   			
	   			<if test="orderSource=='DDBX'">
	   			</if>
	   		 </if>
	    	1=1) as  t where t.rownumber > #{start, jdbcType=INTEGER} 
	    </if>
	  </if>
	</select>



	<select id="countGetFWOrdersByCondition" parameterType="com.chevron.pms.model.OrderDDBXCondition" resultType="Long">
  		<if test="orderSource==null">
	    select 
        count(1)
        from(
           SELECT tt_order.*
           from wx_t_order  tt_order LEFT JOIN wx_t_preferential_type tt_pre_type
           ON tt_order.type = tt_pre_type.type_code
	   	where
	   		tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
	   		
	   		<!-- 关键字搜索 -->
	   	   <!--  <if test="queryType==2"> -->
	   	    	<if test="queryField!=null and queryField!=''">
	   	    	and
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	1=1
	   	    	</if>
	   	    	
	   	    <!-- </if> -->
	   	    
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
	   		    and
				<if test="plateNumber!=null" >
					tt_order.plate_number like '%' + #{plateNumber} + '%'  
				and
				</if>
				<if test="buyUserName!=null" >
					tt_order.buy_user_name like  '%' + #{buyUserName} + '%'   
				and
				</if>
				<if test="preferentialTypePrice!=null" >
					tt_order.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
				and
				</if>
				<if test="regionName!=null" >
					tt_order.region_name like '%' + #{regionName} + '%'  
				and
				</if>
				<if test="receiveUserName!=null" >
					tt_order.receive_user_name like '%' + #{receiveUserName} + '%'  
				and
				</if>
				<if test="receiveUserPhone!=null" >
					tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="sendProductTime!=null" >
					Convert(varchar(10),tt_order.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
				<if test="sendStatus!=null" >
					tt_order.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order.create_time<#{orderCreateEndTime} AND]]>
				</if>
				
		    	1=1
		    
		    UNION
		    	select tt_order1.* from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
	        ON tt_order1.type = tt_pre_type1.type_code
		   	where
		   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
		   		and
		   		(tt_order1.source = 'OD12' or tt_order1.source = 'JSBOD')
		   		and
				<if test="plateNumber!=null" >
					tt_order1.plate_number like '%' + #{plateNumber} + '%' 
				and
				</if>
				<if test="buyUserName!=null" >
					tt_order1.buy_user_name like  '%' + #{buyUserName} + '%'   
				and
				</if>
				<if test="preferentialTypePrice!=null" >
					tt_order1.preferential_type_price like '%' + #{preferentialTypePrice} + '%'
				and
				</if>
				<if test="regionName!=null" >
					tt_order1.region_name like '%' + #{regionName} + '%' 
				and
				</if>
				<if test="receiveUserName!=null" >
					tt_order1.receive_user_name like '%' + #{receiveUserName} + '%'
				and
				</if>
				<if test="receiveUserPhone!=null" >
					tt_order1.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				and
				</if>
				<if test="sendProductTime!=null" >
					Convert(varchar(10),tt_order1.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
				<if test="sendStatus!=null" >
					tt_order1.status = #{sendStatus,jdbcType=NVARCHAR}  
				and
				</if>
				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order1.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order1.create_time<#{orderCreateEndTime} AND]]>
				</if>
				
		    	1=1) AS t_total
		    </if>
		</if>
		
		
		<if test="orderSource!=null">
	    select count(1) from wx_t_order tt_order1 LEFT JOIN wx_t_preferential_type tt_pre_type1
        ON tt_order1.type = tt_pre_type1.type_code
	   	where
	   		tt_order1.order_type = #{orderType,jdbcType=NVARCHAR}
	   		
	   		<!-- 关键字搜索 -->
	   	   <!--  <if test="queryType==2"> -->
	   	    	<if test="queryField!=null">
	   	    	and
	   	    		(tt_order1.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order1.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.preferential_type_price like '%' + #{queryField} + '%'
	   	    	or   tt_order1.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order1.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order1.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	    	and
	   	    	1=1
	   	    	</if>
	   	   <!--  </if> -->
	   	    
	   		<!-- 高级搜索 -->
	   		<!-- <if test="queryType==1"> -->
	   		<if test="queryField==null">
	   			and
	   			tt_order1.source = #{orderSource,jdbcType=NVARCHAR}
	   			and
   				<if test="plateNumber!=null" >
   					tt_order1.plate_number like '%' + #{plateNumber} + '%'  
   				and
   				</if>
   				<if test="buyUserName!=null" >
   					tt_order1.buy_user_name like  '%' + #{buyUserName} + '%' 
   				and
   				</if>
   				<if test="preferentialTypePrice!=null" >
   					tt_order1.preferential_type_price like '%' + #{preferentialTypePrice} + '%' 
   				and
   				</if>
   				<if test="regionName!=null" >
   					tt_order1.region_name like '%' + #{regionName} + '%' 
   				and
   				</if>
   				<if test="receiveUserName!=null" >
   					tt_order1.receive_user_name like '%' + #{receiveUserName} + '%' 
   				and
   				</if>
   				<if test="receiveUserPhone!=null" >
   					tt_order1.receive_phone_no like '%' + #{receiveUserPhone} + '%' 
   				and
   				</if>
   				<if test="sendProductTime!=null" >
					Convert(varchar(10),tt_order1.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				and
				</if>
   				<if test="sendStatus!=null" >
   					tt_order1.status = #{sendStatus,jdbcType=NVARCHAR}  
   				and
   				</if>
   				<if test="orderCreateTime!=null and orderCreateTime != ''" >
					<![CDATA[tt_order1.create_time>= #{orderCreateTime} AND]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					<![CDATA[tt_order1.create_time<#{orderCreateEndTime} AND]]>
				</if>
   			
	   			<if test="orderSource=='DDBX'">
	   			</if>
	   		 </if>
	    	1=1
	    </if>
  	</select>
  	<!-- 统计订单数量　数据权限 -->
  	<select id="countCGOrdersList" parameterType="com.chevron.pms.model.OrderVoAuthParams" resultType="long">
  			select 
		  	    count(tt_order.id)
		  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
                LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
                LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
		 where 1 = 1　$Permission_Clause$
			 	and tt_order.order_type != 'DA'
			 	and tt_order.order_type != 'DP'
			 	<if test="status != null">
				  	and tt_order.status = #{status}
				</if>
				<if test="partnerId != null">
				  	and tt_par.partner_id = #{partnerId}
				</if>
	</select>	 
  	
  	
  	<!-- 采购订单查询——带有数据权限控制处理的分页查询 -->
  	<select id="getCGOrdersByCondition" parameterType="com.chevron.pms.model.OrderCondition" resultMap="BaseResultMap">
		  select 
		  	    tt_order.id, tt_order.order_no, tt_order.order_type, 
				tt_order.buy_user_no, 
				tt_order.buy_user_name, 
				tt_order.buyer_card_id, 
				tt_order.invoice_info, 
				tt_order.source, 
				tt_order.source_id, 
				tt_order.pay_type, pay_state, 
				tt_order.total_product_price, 
				tt_order.total_delivery_price, 
				tt_order.total_order_price, 
				tt_order.total_product_prefer_price, 
				tt_order.total_delivery_prefer_price, 
				tt_order.total_order_prefer_price, 
				tt_order.total_order_pay_price, 
				tt_order.total_order_need_pay_price, 
				tt_order.work_shop_id,
				tt_order.work_shop_id as workshop_id, 
				tt_order.reference_order_no, 
				tt_order.reference_order_type, 
				tt_order.status, 
				tt_order.create_time, 
				tt_order.update_time, 
				tt_order.creator, 
				tt_order.remark,
				tt_order.plate_number, 
				tt_order.address,
				tt_order.receive_user_name,
				tt_order.receive_phone_no,
				tt_order.effective_time,
				tt_order.type,
				tt_order.bill_id,
				tt_order.snd_type,
				tt_order.phone_no,
				tt_order.service_time,
				tt_order.invalid_time,
				tt_order.region_name,
				tt_order.is_export, 
				tt_order.car_type,
				tt_order.preferential_type_price,
				tt_order.delivery_time,
				tt_order.oil_injection,
		  	    tt_org.organization_name AS orgname,
		  	    tt_workshop.work_shop_name AS t_order_workshopname,
		  	    (select u.CH_NAME from wx_t_user u where u.USER_ID = tt_order.creator) as creator_display_name,
		        (select o.work_shop_name FROM wx_t_work_shop o WHERE o.id = tt_order.work_shop_id) as work_shop_name,
	        	(select p.dic_item_name from wx_t_dic_item p where p.dic_type_code = 'order.status'
                <!-- and p.dic_item_code = tt_order.status-->
                AND p.dic_item_code = (case WHEN tt_order.status = 11 and tt_order.partner_confirm_type = 1 THEN 13 ELSE tt_order.status END)
	        	) as status_meaning ,
				(
				SELECT
					top 1 db_or.status
				FROM
					wx_t_db2b_order db_or
				WHERE
					1 = 1
				AND charindex(
					db_or.order_no,
					tt_order.source
				) > 0
				AND db_or.delete_flag = 0
				) AS b2b_status
		  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
                LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
                LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
		 where 1 = 1
		 	  <!-- add by bo.liu 1026 start -->
			  <if test="orderType == null">
			 	and tt_order.order_type != #{notqueryOrderTypeDA,jdbcType=VARCHAR}
			 	and tt_order.order_type != #{notqueryOrderTypeDP,jdbcType=VARCHAR}
			 	and tt_order.order_type != #{notqueryOrderTypeSPDA,jdbcType=VARCHAR}
		  	  </if>
		 
				  <if test="mPartnerId != null">
				  	 AND tt_org.id= #{mPartnerId}
				  </if>
				  <if test="orderNo != null and orderNo != '' ">
					 and tt_order.order_no = #{orderNo,jdbcType=VARCHAR}
				  </if>
				  <if test="workshopId != null and workshopId != ''">
			     	and tt_order.WORK_SHOP_ID = #{workshopId, jdbcType=VARCHAR}
			  	  </if>
				  <if test="extProperty8 != null and extProperty8 != ''">
			     	and tt_order.ext_property8 = #{extProperty8, jdbcType=VARCHAR}
			  	  </if>
				  <if test="dateStart != null">
				    and tt_order.CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
				  </if>
				  <if test="dateEnd != null">
				    and tt_order.CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
				  </if>
				  <if test="orderType != null and orderType != '' ">
					and tt_order.order_type = #{orderType,jdbcType=VARCHAR}
				  </if>
				  <if test="orderStatus != null">
				  	and tt_order.status = #{orderStatus}
				  </if>
				  <if test="creator != null">
				  	and tt_order.creator = #{creator}
				  </if>
				  <if test="source != null">
				  	and tt_order.source = #{source}
				  </if>
				  <if test="workshopName != null">
			  		and tt_workshop.work_shop_name like '%' + #{workshopName} + '%' 
			 	  </if>
			 	  <if test="orderStatusLists != null and  (orderStatusLists.size() > 0) or outStatus != null or endStatus != null ">
			 	  	and (
				 	  	<trim prefixOverrides="or">
				 	   		<if test="orderStatusLists != null and  (orderStatusLists.size() > 0)"  >
				 	   			 or tt_order.status in 
						 	  	<foreach collection="orderStatusLists" index="index" item="item" separator="," open="(" close=")">
			    					${item}
			    			 	</foreach>
				 	   		</if>
				 	   		<if test="endStatus != null">
				 	   			or (tt_order.status = #{endStatus} and tt_order.partner_confirm_type is null)
				 	   		</if>
				 	   		<if test="outStatus != null">
				 	   		or (tt_order.status = 11 and tt_order.partner_confirm_type = 1 )
				 	   		</if>
				 	  	</trim>
			 	  	)
			 	  </if>
			 	   <if test=" b2bOrderStatusList != null and (b2bOrderStatusList.size() > 0)">
			 	 	AND EXISTS (SELECT * FROM wx_t_db2b_order  db_or where 1 = 1 and  charindex(db_or.order_no,tt_order.source) > 0 
				  		AND db_or.delete_flag = 0
				  		and db_or.status in 
				 	  	<foreach collection="b2bOrderStatusList" index="index" item="item" separator="," open="(" close=")">
	    					${item}
	    			 	</foreach>
	    			 	)
			 	  </if>
			 	 <if test="queryField!= null">
			  		and (tt_workshop.work_shop_name like '%' + #{queryField} + '%' 
			  				or tt_order.order_no like '%' + #{queryField} + '%' 
			  				or tt_order.source like '%' + #{queryField} + '%' 
			  				or tt_org.organization_name like '%' + #{queryField} + '%' 
			  			)
			 	 </if>
			 	 <choose>
			 	 	<when test="funFlag == 'APP_OUTSTOCK'">
			 	 	and tt_order.source='OUT_STOCK' and tt_order.status!='6'
			 	 	</when>
			 	 </choose>
	</select>
	
	
	<select id="countGetCGOrdersByCondition" resultType="long" parameterType="com.chevron.pms.model.OrderCondition">
	  	  select 
		  	    count(1)
		  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
                LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
                LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
		 where 1 = 1
		 	  <!-- add by bo.liu 1026 start -->
			  <if test="orderType == null">
			 	and tt_order.order_type != #{notqueryOrderTypeDA,jdbcType=VARCHAR}
			 	and tt_order.order_type != #{notqueryOrderTypeDP,jdbcType=VARCHAR}
		  	  </if>
		 
		     <if test="queryType == 1">
				  <if test="mPartnerId != null">
				  	 AND tt_org.id= #{mPartnerId}
				  </if>
				  <if test="orderNo != null and orderNo != '' ">
					 and tt_order.order_no = #{orderNo,jdbcType=VARCHAR}
				  </if>
				  <if test="workshopId != null and workshopId != ''">
			     	and tt_order.WORK_SHOP_ID = #{workshopId, jdbcType=VARCHAR}
			  	  </if>
				  <if test="dateStart != null">
				    and tt_order.CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
				  </if>
				  <if test="dateEnd != null">
				    and tt_order.CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
				  </if>
				  <if test="orderType != null and orderType != '' ">
					and tt_order.order_type = #{orderType,jdbcType=VARCHAR}
				  </if>
				  <if test="orderStatus != null">
				  	and tt_order.status = #{orderStatus}
				  </if>
				  <if test="creator != null">
				  	and tt_order.creator = #{creator}
				  </if>
				  <if test="source != null">
				  	and tt_order.source = #{source}
				  </if>
				  <if test="workshopName != null">
			  		and tt_workshop.work_shop_name like '%' + #{workshopName} + '%'  
			 	  </if>
			 	<if test="orderStatusLists != null and  (orderStatusLists.size() > 0) or outStatus != null or endStatus != null ">
			 	  	and (
				 	  	<trim prefixOverrides="or">
				 	   		<if test="orderStatusLists != null and  (orderStatusLists.size() > 0)"  >
				 	   			 or tt_order.status in 
						 	  	<foreach collection="orderStatusLists" index="index" item="item" separator="," open="(" close=")">
			    					${item}
			    			 	</foreach>
				 	   		</if>
				 	   		<if test="endStatus != null">
				 	   			or (tt_order.status = #{endStatus} and tt_order.partner_confirm_type is null)
				 	   		</if>
				 	   		<if test="outStatus != null">
				 	   		or (tt_order.status = 11 and tt_order.partner_confirm_type = 1 )
				 	   		</if>
				 	  	</trim>
			 	  	)
			 	  </if>
			 	   <if test="b2bOrderStatusList != null and (b2bOrderStatusList.size() > 0)">
			 	 	AND EXISTS (SELECT * FROM wx_t_db2b_order  db_or where 1 = 1 and  charindex(db_or.order_no,tt_order.source) > 0 
				  		AND db_or.delete_flag = 0
				  		and db_or.status in 
				 	  	<foreach collection="b2bOrderStatusList" index="index" item="item" separator="," open="(" close=")">
	    					${item}
	    			 	</foreach>
	    			 	)
			 	  </if>
			 </if>
			 <if test="queryType == 2">
			 	 <if test="queryField!= null">
			  		and (tt_workshop.work_shop_name like '%' + #{queryField} + '%' 
			  				or tt_order.order_no like '%' + #{queryField} + '%' 
			  				or tt_order.source like '%' + #{queryField} + '%' 
			  				or tt_org.organization_name like '%' + #{queryField} + '%' 
			  			)
			 	 </if>
		 	 </if>
	</select>
	
	
	<!-- 获取已发货的订单，状态为5的订单 -->
	<select id="getOrdersByStatus"  parameterType="map"  resultMap="BaseResultMap">
	   	select DISTINCT t_order.*
	   	<if test="orderConfirmFlag!=null">
	   	,t_p.viscosity,t_p_type.oil_delivery_times card_enable_times
	   	</if>
		from wx_t_order t_order 
		<if test="orderConfirmFlag!=null">
		inner JOIN wx_t_order_line t_o_line
		 	ON t_o_line.order_id = t_order.id
		inner JOIN wx_t_product t_p
		 	ON t_p.id= t_o_line.product_id
		INNER JOIN wx_t_preferential_type t_p_type
		 	ON t_p_type.type_code = t_order.type
		</if>
 	    where
	   	1=1
	    <if test="orderStatus!= null">
		  and 
		  t_order.status = #{orderStatus}
	    </if>
        <if test="orderType!= null">
		  and 
		  t_order.order_type = #{orderType}
       </if>
       <if test="orderSource1!=null">
          and 
          t_order.source = #{orderSource1}
       </if>
       <if test="isexportflag!=null">
          and 
          t_order.is_export = #{isexportflag}
       </if>
        <if test="beginTime!= null">
        <![CDATA[and t_order.create_time>= #{beginTime}]]>
       </if>
        <if test="endTime!= null">
		  <![CDATA[and t_order.create_time< #{endTime}]]>
       </if>
	</select>
	
	
	<resultMap id="EmailOrderBaseResultMap" type="com.chevron.pms.model.OrderForEmailExport">
		<id column="id" property="id" jdbcType="BIGINT" />
	    <result column="plate_number" property="plateNumber" jdbcType="NVARCHAR"/>
		<result column="oil_injection" property="oilInjection" jdbcType="NVARCHAR"/>
		<result column="order_time" property="orderTime" jdbcType="NVARCHAR" />
		<result column="sku" property="sku" jdbcType="NVARCHAR" />
		<result column="product_name" property="productName" jdbcType="NVARCHAR" />
		<result column="amount" property="amount" jdbcType="INTEGER" />
		<result column="type" property="type" jdbcType="NVARCHAR" />
		<result column="card_type" property="cardType" jdbcType="NVARCHAR" />
		<result column="warehouse_name" property="warehouse" jdbcType="NVARCHAR" />
		<result column="oil_delivery_times" property="oilSndTimes" jdbcType="INTEGER" />
		
		
	</resultMap>
	
	<select id="getOrderForSendEmailByCondition" parameterType="map" resultMap="EmailOrderBaseResultMap">
   select tt_order.type,tt_pre_type.card_type card_type,tt_pre_type.oil_delivery_times, tt_order.id,tt_order.plate_number,tt_order.oil_injection,CONVERT(varchar(100),tt_order.create_time, 23)order_time,tt_order_line.sku,tt_order_line.amount,tt_order_line.product_name,LEFT(tt_order_line.sku,6 ) sku_no
   ,tt_warehouse.warehouse_name
   	from
   		wx_t_order tt_order
   		LEFT JOIN
   		wx_t_order_line tt_order_line
   		ON
   	    tt_order.id = tt_order_line.order_id AND tt_order_line.type='JY'
   	    LEFT JOIN
   	    wx_t_preferential_type tt_pre_type
   	    ON tt_order.type = tt_pre_type.type_code
   	    left JOIN
   	    wx_t_order_ddbxwarehouse tt_warehouse
   	    ON tt_order.region_name = tt_warehouse.region_name
   	where
   		<if test="orderIds!=null">
   			tt_order.id in 
   			<foreach item="id" index="index" collection="orderIds" open="(" separator="," close=")"> 
				 #{id}  
			</foreach> 
   			and
   		</if>
   		<if test="isExport!=null">
   			tt_order.is_export = #{isExport}
   			and
   		</if>
   		<if test="orderType!=null">
   			tt_order.order_type = #{orderType,jdbcType=NVARCHAR}
   		and
   		</if>
   	
    	<![CDATA[   CONVERT(varchar(100), #{nowdate}, 23) <= CONVERT(varchar(100), invalid_time, 23)    and ]]>
    	
    	1=1
    order by tt_order.oil_injection ASC,sku_no ASC,plate_number,sku
   		
   </select>
   
   <!-- 根据条件查询订单列表,数据库分页-->
	<select id="queryOrdersByConditionWithPaginationMap" parameterType="map" resultMap="BaseResultMap">
		select
		 	<if test="reqmaps.limit != -1">
		 		top ${reqmaps.limit}
		 	</if>
		t.id, t.order_no, t.order_type, 
		t.buy_user_no, 
		t.buy_user_name, 
		t.buyer_card_id, 
		t.invoice_info, 
		t.source, 
		t.source_id, 
		t.pay_type, pay_state, 
		t.total_product_price, 
		t.total_delivery_price, 
		t.total_order_price, 
		t.total_product_prefer_price, 
		t.total_delivery_prefer_price, 
		t.total_order_prefer_price, 
		t.total_order_pay_price, 
		t.total_order_need_pay_price, 
		t.work_shop_id, 
		t.reference_order_no, 
		t.reference_order_type, 
		t.status, 
		t.create_time, 
		t.update_time, 
		t.creator, 
		t.remark,
		t.work_shop_name,
		t.plate_number, 
		t.address,
		t.receive_user_name,
		t.receive_phone_no,
		t.effective_time,
		t.type,
		t.bill_id,
		t.snd_type,
		t.phone_no,
		t.service_time,
		t.invalid_time,
		t.region_name,
		t.is_export, 
		t.car_type,
		t.preferential_type_price,
		t.delivery_time,
		t.oil_injection
		,t.orgname as org_name 
		,t.t_order_workshopname AS order_workshopname
		,(select u.CH_NAME from wx_t_user u where u.USER_ID = t.creator) as creator_display_name
		,(select o.work_shop_name FROM wx_t_work_shop o WHERE o.id = t.work_shop_id) as work_shop_name
		,(select p.codename from wx_t_properties p where p.codetype = 'order_status' and p.code = t.status) as status_meaning 
		  from 
		  (
		  	  select 
		  	    <if test="reqmaps.orderBy != null">
		  	    	row_number() over(order by ${reqmaps.orderBy}) as rownumber,
		  	    </if>
		  	    tt_order.id, tt_order.order_no, tt_order.order_type, 
				tt_order.buy_user_no, 
				tt_order.buy_user_name, 
				tt_order.buyer_card_id, 
				tt_order.invoice_info, 
				tt_order.source, 
				tt_order.source_id, 
				tt_order.pay_type, pay_state, 
				tt_order.total_product_price, 
				tt_order.total_delivery_price, 
				tt_order.total_order_price, 
				tt_order.total_product_prefer_price, 
				tt_order.total_delivery_prefer_price, 
				tt_order.total_order_prefer_price, 
				tt_order.total_order_pay_price, 
				tt_order.total_order_need_pay_price, 
				tt_order.work_shop_id, 
				tt_order.reference_order_no, 
				tt_order.reference_order_type, 
				tt_order.status, 
				tt_order.create_time, 
				tt_order.update_time, 
				tt_order.creator, 
				tt_order.remark,
				tt_order.work_shop_name,
				tt_order.plate_number, 
				tt_order.address,
				tt_order.receive_user_name,
				tt_order.receive_phone_no,
				tt_order.effective_time,
				tt_order.type,
				tt_order.bill_id,
				tt_order.snd_type,
				tt_order.phone_no,
				tt_order.service_time,
				tt_order.invalid_time,
				tt_order.region_name,
				tt_order.is_export, 
				tt_order.car_type,
				tt_order.preferential_type_price,
				tt_order.delivery_time,
				tt_order.oil_injection,
		  	    tt_org.organization_name AS orgname,
		  	    tt_workshop.work_shop_name AS t_order_workshopname 
		  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
                LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
                LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
			   where 1 = 1
			  <if test="reqmaps.queryParamsMap.mPartnerId != null">
			  	 AND tt_org.id= #{reqmaps.queryParamsMap.mPartnerId}
			  </if>
			  <if test="reqmaps.queryParamsMap.orderNo != null and reqmaps.queryParamsMap.orderNo != '' ">
				 and tt_order.order_no = #{reqmaps.queryParamsMap.orderNo,jdbcType=VARCHAR}
			  </if>
			  <if test="reqmaps.queryParamsMap.workshopId != null and reqmaps.queryParamsMap.workshopId != ''">
		     	and tt_order.WORK_SHOP_ID = #{reqmaps.queryParamsMap.workshopId, jdbcType=VARCHAR}
		  	  </if>
			  <if test="reqmaps.queryParamsMap.dateFrom != null">
			    and tt_order.CREATE_TIME &gt;= #{reqmaps.queryParamsMap.dateFrom, jdbcType=TIMESTAMP}
			  </if>
			  <if test="reqmaps.queryParamsMap.dateTo != null">
			    and tt_order.CREATE_TIME &lt;= #{reqmaps.queryParamsMap.dateTo, jdbcType=TIMESTAMP}
			  </if>
			  <if test="reqmaps.queryParamsMap.orderType != null and reqmaps.queryParamsMap.orderType != '' ">
				and tt_order.order_type = #{reqmaps.queryParamsMap.orderType,jdbcType=VARCHAR}
			  </if>
			  <if test="reqmaps.queryParamsMap.status != null">
			  	and tt_order.status = #{reqmaps.queryParamsMap.status}
			  </if>
			  <if test="reqmaps.queryParamsMap.source != null">
			  	 <if test="reqmaps.queryParamsMap.source_yxc == null">
		  			and tt_order.source = #{reqmaps.queryParamsMap.source}
		  	 	</if>
		  		 <if test="reqmaps.queryParamsMap.source_yxc != null">
		  			and (tt_order.source = #{reqmaps.queryParamsMap.source} or tt_order.source = #{reqmaps.queryParamsMap.source_yxc})
		  	 	</if>
			  </if>
			  <if test="reqmaps.queryParamsMap.sourceValues != null">
			  	and tt_order.source in 
			  	 <foreach collection="reqmaps.queryParamsMap.sourceValues" index="index" item="item" separator="," open="(" close=")">
    				#{item}
    			 </foreach>
			  </if>
			  <!-- add by bo.liu 1026 start -->
			  <if test="reqmaps.queryParamsMap.orderType == null">
			 	and tt_order.order_type != #{reqmaps.queryParamsMap.orderTypeDA,jdbcType=VARCHAR}
			 	and tt_order.order_type != #{reqmaps.queryParamsMap.orderTypeDP,jdbcType=VARCHAR}
		  	 </if>
		 	 <if test="reqmaps.queryParamsMap.workshopName != null">
		  		and tt_workshop.work_shop_name like '%' + #{reqmaps.queryParamsMap.workshopName} + '%' 
		 	 </if>
		 	 <if test="reqmaps.queryParamsMap.queryField != null">
		  		and (tt_workshop.work_shop_name like '%' + #{reqmaps.queryParamsMap.queryField} + '%' 
		  				or tt_order.order_no like '%' + #{reqmaps.queryParamsMap.queryField} + '%'  
		  				or tt_order.source like '%' + #{reqmaps.queryParamsMap.queryField} + '%'  
		  				or tt_org.organization_name like '%' + #{reqmaps.queryParamsMap.queryField} + '%'  
		  			)
		 	 </if>
		 	 <!-- add by bo.liu 1026 end -->
		  ) t 
		 where 1 = 1
		 <if test="reqmaps.start != -1 and reqmaps.orderBy != null">
		     and t.rownumber > #{reqmaps.start, jdbcType=INTEGER} 
		 </if>
	</select>
	
	<!-- 根据条件查询订单数量,数据库分页-->
	<select id="queryOrdersCountByConditionWithPaginationMap" resultType="long" parameterType="map">
	  	  select count(1)
	  	    from wx_t_order tt_order LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
            LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id 
            LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
		   where 1 = 1
		  <if test="reqmaps.queryParamsMap.mPartnerId != null">
			  	 AND tt_org.id= #{reqmaps.queryParamsMap.mPartnerId}
		  </if>
		  <if test="reqmaps.queryParamsMap.orderNo != null and reqmaps.queryParamsMap.orderNo != '' ">
			 and tt_order.order_no = #{reqmaps.queryParamsMap.orderNo,jdbcType=VARCHAR}
		  </if>
		  <if test="reqmaps.queryParamsMap.workshopId != null and reqmaps.queryParamsMap.workshopId != ''">
		     and tt_order.WORK_SHOP_ID = #{reqmaps.queryParamsMap.workshopId, jdbcType=VARCHAR}
		  </if>
		  <if test="reqmaps.queryParamsMap.dateFrom != null">
		     and tt_order.CREATE_TIME &gt;= #{reqmaps.queryParamsMap.dateFrom, jdbcType=TIMESTAMP}
		  </if>
		  <if test="reqmaps.queryParamsMap.dateTo != null">
		     and tt_order.CREATE_TIME &lt;= #{reqmaps.queryParamsMap.dateTo, jdbcType=TIMESTAMP}
		  </if>
		  <if test="reqmaps.queryParamsMap.orderType != null and reqmaps.queryParamsMap.orderType != '' ">
			 and tt_order.order_type = #{reqmaps.queryParamsMap.orderType,jdbcType=VARCHAR}
		  </if>
		  <if test="reqmaps.queryParamsMap.status != null">
		  	and tt_order.status = #{reqmaps.queryParamsMap.status}
		  </if>
		  <if test="reqmaps.queryParamsMap.source != null">
		 	 <if test="reqmaps.queryParamsMap.source_yxc == null">
		  	and tt_order.source = #{reqmaps.queryParamsMap.source}
		  	 </if>
		  	 <if test="reqmaps.queryParamsMap.source_yxc != null">
		  	and (tt_order.source = #{reqmaps.queryParamsMap.source} or tt_order.source = #{reqmaps.queryParamsMap.source_yxc})
		  	 </if>
		  </if>
		  <if test="reqmaps.queryParamsMap.sourceValues != null">
			  	and tt_order.source in 
			  	 <foreach collection="reqmaps.queryParamsMap.sourceValues" index="index" item="item" separator="," open="(" close=")">
    				#{item}
    			 </foreach>
		  </if>
		   <!-- add by bo.liu 1026 start -->
		  <if test="reqmaps.queryParamsMap.orderType == null">
			 and tt_order.order_type != #{reqmaps.queryParamsMap.orderTypeDA,jdbcType=VARCHAR}
			 and tt_order.order_type != #{reqmaps.queryParamsMap.orderTypeDP,jdbcType=VARCHAR}
		  </if>
		  <if test="reqmaps.queryParamsMap.workshopName != null">
		  	and tt_workshop.work_shop_name like '%' + #{reqmaps.queryParamsMap.workshopName} + '%'  
		  </if>
		   <!-- add by bo.liu 1026 end -->
	</select>
    <select id="getOrderByOrderNo" parameterType="map" resultMap="BaseResultMap">
	   	select tt_order.*
	   	from
	   		wx_t_order tt_order
	   	where
	   		tt_order.order_no = #{orderCode,jdbcType=NVARCHAR}
   	</select>
   
   
   
    <select id="getO2OOrderBookingIn" parameterType="map" resultMap="BaseResultMap">
	   	select tt_order.*
	   	from
	   		wx_t_order tt_order
	   	where
	   		tt_order.memberid = #{memberid,jdbcType=NVARCHAR}
	   	and
	   		(tt_order.status = #{status,jdbcType=NVARCHAR} or tt_order.status = #{status1,jdbcType=NVARCHAR})
   	</select>
   
   
   
   
    <update id="updateO2OOrderVo" parameterType="map" >
    update wx_t_order
    <set >
	     <if test="o2oorderVo.orderNo != null and o2oorderVo.orderNo != ''" >
	        order_no = #{o2oorderVo.orderNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.orderType != null and o2oorderVo.orderType != ''" >
	        order_type = #{o2oorderVo.orderType,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.buyUserNo != null and o2oorderVo.buyUserNo != ''" >
	        buy_user_no = #{o2oorderVo.buyUserNo,jdbcType=BIGINT},
	      </if>
	      <if test="o2oorderVo.buyUserName != null and o2oorderVo.buyUserName != ''" >
	        buy_user_name = #{o2oorderVo.buyUserName,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.buyerCardId != null and o2oorderVo.buyerCardId != ''" >
	        buyer_card_id = #{o2oorderVo.buyerCardId,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.invoiceInfo != null and o2oorderVo.invoiceInfo != ''" >
	        invoice_info = #{o2oorderVo.invoiceInfo,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.source != null and o2oorderVo.source != ''" >
	        source = #{o2oorderVo.source,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.sourceId != null and o2oorderVo.sourceId != '' " >
	        source_id = #{o2oorderVo.sourceId,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.payType != null and o2oorderVo.payType != '' " >
	        pay_type = #{o2oorderVo.payType,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.payState != null and o2oorderVo.payState != ''" >
	        pay_state = #{o2oorderVo.payState,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.totalProductPrice != null and o2oorderVo.totalProductPrice != ''" >
	        total_product_price = #{o2oorderVo.totalProductPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalDeliveryPrice != null and o2oorderVo.totalDeliveryPrice != ''" >
	        total_delivery_price = #{o2oorderVo.totalDeliveryPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalOrderPrice != null and o2oorderVo.totalOrderPrice != ''" >
	        total_order_price = #{o2oorderVo.totalOrderPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalProductPreferPrice != null and o2oorderVo.totalProductPreferPrice != ''" >
	        total_product_prefer_price = #{o2oorderVo.totalProductPreferPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalDeliveryPreferPrice != null and o2oorderVo.totalDeliveryPreferPrice != ''" >
	        total_delivery_prefer_price = #{o2oorderVo.totalDeliveryPreferPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalOrderPreferPrice != null and o2oorderVo.totalOrderPreferPrice != ''" >
	        total_order_prefer_price = #{o2oorderVo.totalOrderPreferPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalOrderPayPrice != null and o2oorderVo.totalOrderPayPrice != ''" >
	        total_order_pay_price = #{o2oorderVo.totalOrderPayPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.totalOrderNeedPayPrice != null and o2oorderVo.totalOrderNeedPayPrice != ''" > 
	        total_order_need_pay_price = #{o2oorderVo.totalOrderNeedPayPrice,jdbcType=NUMERIC},
	      </if>
	      <if test="o2oorderVo.workShopId != null and o2oorderVo.workShopId != '' " >
	        work_shop_id = #{o2oorderVo.workShopId,jdbcType=BIGINT},
	      </if>
	      <if test="o2oorderVo.referenceOrderNo != null and o2oorderVo.referenceOrderNo != '' " >
	        reference_order_no = #{o2oorderVo.referenceOrderNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.referenceOrderType != null and o2oorderVo.referenceOrderType != ''" >
	        reference_order_type = #{o2oorderVo.referenceOrderType,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.status != null and o2oorderVo.status != ''" >
	        status = #{o2oorderVo.status,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.createTime != null and o2oorderVo.createTime != ''" >
	        create_time = #{o2oorderVo.createTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="o2oorderVo.updateTime != null and o2oorderVo.updateTime != ''" >
	        update_time = #{o2oorderVo.updateTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="o2oorderVo.creator != null and o2oorderVo.creator != ''" >
	        creator = #{o2oorderVo.creator,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.remark != null and o2oorderVo.remark != ''" >
	        remark = #{o2oorderVo.remark,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.workshopName != null and o2oorderVo.workshopName != ''" >
	        work_shop_name = #{o2oorderVo.workshopName,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.plateNumber != null and o2oorderVo.plateNumber != ''" >
	        plate_number = #{o2oorderVo.plateNumber,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.address != null and o2oorderVo.address != ''" >
	        address = #{o2oorderVo.address,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.receiveUserName != null and o2oorderVo.receiveUserName != ''" >
	        receive_user_name = #{o2oorderVo.receiveUserName,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.receivePhoneNo != null and o2oorderVo.receivePhoneNo != ''" >
	        receive_phone_no = #{o2oorderVo.receivePhoneNo,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.effectiveTime != null and o2oorderVo.effectiveTime != ''" >
	        effective_time = #{o2oorderVo.effectiveTime,jdbcType=TIMESTAMP},
	      </if>
	      <if test="o2oorderVo.type != null and o2oorderVo.type != ''" >
	        type = #{o2oorderVo.type,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.billId != null and o2oorderVo.billId != ''" >
	        bill_id = #{o2oorderVo.billId,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.sndType != null and o2oorderVo.sndType != ''" >
	        snd_type = #{o2oorderVo.sndType,jdbcType=NVARCHAR},
	      </if>
	       <if test="o2oorderVo.phoneNo != null and o2oorderVo.phoneNo != ''" >
	        phone_no = #{o2oorderVo.phoneNo,jdbcType=NVARCHAR},
	      </if>
	        <if test="o2oorderVo.serviceTime != null and o2oorderVo.serviceTime != ''" >
		    service_time =  #{o2oorderVo.serviceTime,jdbcType=TIMESTAMP},
		   </if>
		   <if test="o2oorderVo.invalidTime != null and o2oorderVo.invalidTime != ''" >
		    invalid_time =  #{o2oorderVo.invalidTime,jdbcType=TIMESTAMP},
		    </if>
		  <if test="o2oorderVo.regionName != null and o2oorderVo.regionName != ''" >
	        region_name = #{o2oorderVo.regionName,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.isExport != null and o2oorderVo.isExport != ''" >
	        is_export = #{o2oorderVo.isExport,jdbcType=INTEGER},
	      </if>
	       <if test="o2oorderVo.carType != null and o2oorderVo.carType != ''" >
	        car_type = #{o2oorderVo.carType,jdbcType=NVARCHAR},
	      </if>
	      <if test="o2oorderVo.preferentialTypePrice != null and o2oorderVo.preferentialTypePrice != ''" >
	        preferential_type_price = #{o2oorderVo.preferentialTypePrice,jdbcType=NVARCHAR},
	      </if>
	      
	      <if test="o2oorderVo.deliveryTime != null and o2oorderVo.deliveryTime != ''" >
	        delivery_time = #{o2oorderVo.deliveryTime,jdbcType=TIMESTAMP},
	      </if>
	       <if test="o2oorderVo.oilInjection != null and o2oorderVo.oilInjection != ''" >
	        oil_injection = #{o2oorderVo.oilInjection,jdbcType=NVARCHAR},
	      </if>
	    </set>
    where 
    	<if test="o2oorderVo.orderNo!=null and o2oorderVo.orderNo!=''">
    	order_no = #{o2oorderVo.orderNo,jdbcType=NVARCHAR}
    	and
    	</if>
     1=1
  </update>
   
   
   <select id="getWorkShopIdsTOO2OOrderComment" parameterType="map" resultMap="BaseResultMap">
   SELECT t_order.work_shop_id,t_order.order_no
   FROM wx_t_order t_order
   WHERE t_order.source LIKE #{orderSouce}
	  AND t_order.order_type = #{orderType}
	  AND t_order.status= #{orderStatus}
	  AND t_order.work_shop_id IS NOT NULL
	  AND DATEDIFF(DAY,CONVERT(varchar(100), t_order.update_time, 21),CONVERT(varchar(100), GETDATE(), 21))>= #{orderDayDiff} 
	  
   		
   </select>
   
   <select id="getO2OOrderByMembIdAndCardNo" parameterType="map" resultMap="BaseResultMap">
   SELECT t_order.*
   FROM wx_t_order t_order
   WHERE 
       t_order.memberid = #{memberId}
   and 
       t_order.order_type = #{orderType}
   and 
       t_order.cardno = #{cardno}
   </select>
   
   
   <select id="getPoOrderDeatilByMap" parameterType="map" resultMap="BaseResultMap">
   SELECT t_order.*
   FROM wx_t_order t_order
   WHERE 
       t_order.order_no = #{orderNo}
   and 
       t_order.work_shop_id = #{workshopId}
   and 
       t_order.partner_id = #{partnerId}
   </select>
   
   
   <select id="getPoOrderLstByMap" parameterType="map" resultMap="BaseResultMap">
   	SELECT DISTINCT tt_order.id, tt_order.order_no,tt_order.status,tt_order.create_time 
	FROM wx_t_order  tt_order 
	LEFT JOIN wx_t_order_line tt_order_line
	ON tt_order_line.order_id = tt_order.id 
	WHERE tt_order.partner_id=#{partnerId}
	AND tt_order.work_shop_id=#{workshopId} 
	AND (tt_order_line.sku  LIKE '%' + #{productKeyWord,jdbcType=NVARCHAR} + '%'
	OR tt_order_line.product_name  LIKE '%' + #{productKeyWord,jdbcType=NVARCHAR} + '%'
	)
	ORDER BY tt_order.status,tt_order.create_time desc
   </select>
   
   <select id="queryOrderForApp" parameterType="com.chevron.pms.model.OrderParams" resultMap="BaseResultMap">
   select * from (
   	select o.id, o.order_no, 'DA_DDBX' order_type, 'O2O服务单' order_type_text, o.status, dis.dic_item_name status_text, o.create_time,o.work_shop_id
  from wx_t_order o left join wx_t_dic_item dis on dis.dic_type_code='order.status_o2o' and dis.dic_item_code=o.status
 where o.order_type = 'DA' and o.source = 'DDBX'
  <if test="workshopId != null">
  and o.work_shop_id=#{workshopId}
  </if>
union all select o.id, o.order_no, 'A' order_type, '销售订单' order_type_text, o.status, dis.dic_item_name status_text, o.create_time,o.work_shop_id 
  from wx_t_order o left join wx_t_dic_item dis on dis.dic_type_code='order.status' and dis.dic_item_code=o.status
 where o.order_type = 'A'
  <if test="workshopId != null">
  and o.work_shop_id=#{workshopId}
  </if>
union all select o.id, o.order_no, 'DA_DD' order_type, '保养卡订单' order_type_text, o.status, null status_text, o.create_time,o.work_shop_id 
 from wx_t_order o where o.order_type = 'DA' and o.source='O2O_DD'
   <if test="workshopId != null">
  and o.work_shop_id=#{workshopId}
  </if>
 ) a where 1=1
 	<if test="orderTypes != null">
	and a.order_type in 
    <foreach collection="orderTypes" index="index" item="item" separator="," open="(" close=")">
    	'${item}'
    </foreach>
 	</if>
   </select>
   
   <select id="getO2oOrder" parameterType="map" resultMap="BaseResultMap">
   select distinct o.id, o.order_no, o.receive_user_name, o.service_time, o.plate_number, o.oil_injection, 
p.viscosity, o.couponCode, o.create_time, (select dis.dic_item_name
from wx_t_dic_item dis where dis.dic_type_code='product.oilType' and dis.dic_item_code=p.oil_type) oil_type_text,
(select dis.dic_item_name
from wx_t_dic_item dis where dis.dic_type_code='order.status_o2o' and dis.dic_item_code=o.status) status_text from wx_t_order o 
left join wx_t_order_line ol on ol.order_id=o.id and ol.type='JY'
left join wx_t_product p on p.id=ol.product_id
where o.id=#{id}
   </select>
   
   <select id="getWorkshopOrder" parameterType="map" resultMap="BaseResultMap">
   select o.*,
(select dis.dic_item_name
from wx_t_dic_item dis where dis.dic_type_code='order.status' and dis.dic_item_code=o.status) status_text from wx_t_order o 
where o.id=#{id}
   </select>
   <select id="getO2oCardOrder" parameterType="map" resultMap="BaseResultMap">
   select o.*,
ci.card_enable_times from wx_t_order o 
left join wx_t_o2o_cardowner_info ci on ci.cardno=o.cardno
where o.id=#{id}
   </select>
   
   
 <select id="queryOrderByUid" parameterType="map" resultMap="BaseResultMap">
   select o.*
	from wx_t_order o 
	where o.plate_number=#{uid}
	<if test="status!=null">
	and  o.status = #{status}
	</if>
	<if test="ordertype!=null">
	and  o.order_type = #{ordertype}
	</if>
	<if test="source!=null">
    and  o.source = #{source}
    </if>
	<if test="settlementType!=null">
    and  o.settlement_model_type = #{settlementType}
    </if>
    order by o.create_time desc
	
 </select>
 <select id="getFWOrderListByMap" parameterType="map" resultMap="BaseResultMap">
  SELECT TOP  ${pageCount} *
	 FROM 
	(SELECT * ,
	        row_number() over(order by t.id desc) AS rownumber
	 FROM 
	(
 	SELECT * FROM wx_t_order t_order
	WHERE
		t_order.order_type=#{orderType}
		AND
		t_order.source=#{orderSource}
		<if test="beginDate!=null">
			<if test="dateType=='createTime' ">
				<![CDATA[and t_order.create_time>= #{beginDate}]]>
				<![CDATA[and t_order.create_time<= #{endDate}]]>
			</if>
			<if test="dateType=='sndTime' ">
				<![CDATA[and t_order.delivery_time>= #{beginDate}]]>
				<![CDATA[and t_order.delivery_time<= #{endDate}]]>
			</if>
		</if>
	) t ) t2
	WHERE rownumber>#{startPage, jdbcType=INTEGER}  
		
 </select>
 
  <select id="getCountFWOrderListByMap" parameterType="map" resultType="Long">
 	SELECT count(1) FROM wx_t_order t_order
	WHERE
		t_order.order_type=#{orderType}
		AND
		t_order.source=#{orderSource}
		<if test="beginDate!=null">
			<if test="dateType=='createTime' ">
				<![CDATA[and t_order.create_time>= #{beginDate}]]>
				<![CDATA[and t_order.create_time<= #{endDate}]]>
			</if>
			<if test="dateType=='sndTime' ">
				<![CDATA[and t_order.delivery_time>= #{beginDate}]]>
				<![CDATA[and t_order.delivery_time<= #{endDate}]]>
			</if>
		</if>
 </select>
 	<select id="getAllOrderByOrderIds"  parameterType="map" resultMap="BaseResultMap">
		SELECT tt_order.*
		FROM wx_t_order tt_order
		WHERE
		<if test="orderIds!=null">
			tt_order.id IN
			<foreach item="order_id" index="index" collection="orderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
		</if>
	</select>  
	<update id="updateAllOrderStatusByOrderIds"  parameterType="map">
		update wx_t_order  set
		<trim suffixOverrides=",">
			<if test="status != null and status != ''">
			 	status = #{status},
			</if>
			<if test="remark!=null ">
				remark = #{remark},
			</if>
			<if test="updateTime != null and updateTime != ''">
				update_time = #{updateTime, jdbcType=TIMESTAMP},
			</if>
			<if test="partnerConfirmType != null and partnerConfirmType != ''">
				partner_confirm_type = #{partnerConfirmType},
			</if>
			<if test="partnerConfirmType != null and (partnerConfirmType == 1 or partnerConfirmType == '1')" >
		        partner_confirm_time  = getDate(),
		    </if>
		</trim>
		WHERE
		<if test="orderIds!=null">
			id IN
			<foreach item="order_id" index="index" collection="orderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
		</if>
	</update>  
	<update id="updateOrderRemainingServiceTimesByIds"  parameterType="map">
        update wx_t_order  set remaining_service_times=remaining_service_times-1
        WHERE
        <if test="orderIds!=null">
            id IN
            <foreach item="order_id" index="index" collection="orderIds" open="(" separator="," close=")">  
                 #{order_id}  
            </foreach> 
        </if>
    </update> 
    
    <select id="getBillNoByOrderNoOrPlanteNo" parameterType="map" resultMap="BaseResultMap">
        select tt_order.*
        from
            wx_t_order tt_order
        where
            (tt_order.order_no = #{orderCode,jdbcType=NVARCHAR}
        or
            tt_order.plate_number = #{orderCode,jdbcType=NVARCHAR})
        and
            tt_order.order_type ='DA'
        <!-- 
        and 
            tt_order.bill_id!=''
        and 
            tt_order.bill_id is not null
         -->
        order by tt_order.create_time desc
            
    </select>
    
    <select id="getFWOrderExceptZheJiang" parameterType="map" resultMap="BaseResultMap">
		SELECT * FROM wx_t_order 
		WHERE source='DDBX' 
		AND order_type='DA' 
		AND region_name IN ('黑龙江省','辽宁省') 
		<if test="startDate !=null">
		AND create_time &gt;= #{startDate}
		</if>
		<if test="endDate !=null">
		AND create_time &lt; #{endDate}
		</if>
		order by plate_number asc

	</select>
	
	<select id="getDDXBOrderExceptZheJiangNianka" parameterType="map"	resultMap="SettleResultMap">
		select distinct order_base.*,p.viscosity from 
		(
		SELECT 
		<choose>
		<when test="settleOrderStatus == 'settleable'">
		service_acount/3-settled_num-remaining_service_times/3 as 'card_num',
		</when>
		<when test="settleOrderStatus == 'settled'">
		settled_num as 'card_num',
		</when>
		</choose>
		* FROM wx_t_order 
		WHERE order_type='DP' 
		AND card_type='年卡' 
		<choose>
		<when test="settleOrderStatus == 'settleable'">
		AND is_settled = 0 
		AND remaining_service_times	&lt; service_acount 
		AND settled_num*3+remaining_service_times &lt; service_acount
		</when>
		<when test="settleOrderStatus == 'settled'">
		AND (is_settled = 1 
		OR settled_num > 0 )
		</when>
		</choose>
		AND plate_number IN 
		(SELECT DISTINCT plate_number FROM wx_t_order 
		WHERE order_type='DA' 
		AND source='DDBX' 
		AND region_name IN ('黑龙江省', '辽宁省') 
		AND card_type='年卡'
		<if test="startDate !=null">
		AND create_time &gt;= #{startDate}
		</if>
		<if test="endDate !=null">
		AND create_time &lt; #{endDate}
		</if>
		)
		) order_base 
		left join wx_t_order_line ol on ol.order_id=order_base.id
		left join wx_t_product p on p.id= ol.product_id
	</select>
	
	<select id="getDDXBOrderExceptZheJiangDanci" parameterType="map" resultMap="SettleResultMap">
		select distinct order_base.*,p.viscosity from 
		(
		SELECT 
		<choose>
		<when test="settleOrderStatus == 'settleable'">
		service_acount-remaining_service_times-settled_num AS 'card_num',
		</when>
		<when test="settleOrderStatus == 'settled'">
		settled_num as 'card_num',
		</when>
		</choose>
		* FROM wx_t_order WHERE source='DDBX' 
		AND order_type='DP' 
		AND card_type='单次卡'
		<choose>
		<when test="settleOrderStatus == 'settleable'">
		AND is_settled=0
		AND remaining_service_times &lt; service_acount
		AND settled_num+remaining_service_times &lt; service_acount
		</when>
		<when test="settleOrderStatus == 'settled'">
		AND (is_settled=1
		OR settled_num > 0)
		</when>
		</choose>
		
		AND plate_number IN 
		(select DISTINCT plate_number from wx_t_order 
    	where source='DDBX' 
    	and region_name IN ('黑龙江省', '辽宁省') 
    	and order_type='DA' 
    	AND card_type='单次卡'
		<if test="startDate !=null">
		AND create_time &gt;= #{startDate}
		</if>
		<if test="endDate !=null">
		AND create_time &lt; #{endDate}
		</if>
		)
		) order_base 
		left join wx_t_order_line ol on ol.order_id=order_base.id
		left join wx_t_product p on p.id= ol.product_id
	</select>
    
    <select id="getDDFWCountByPlateNumberDanci" parameterType="map" resultMap="SettleResultMap">
    select plate_number,count(*) from wx_t_order 
    	where source='DDBX' 
    	and region_name IN ('黑龙江省', '辽宁省') 
    	and order_type='DA' 
    	and card_type='单次卡'
    	<if test="startDate !=null">
			AND create_time &gt;= #{startDate} 
		</if>
		<if test="endDate !=null">
			AND create_time &lt; #{endDate}
		</if>
	    group by plate_number
    </select>
    <update id="updateOrderSettledNumByOrderNo" parameterType="map">
    	update wx_t_order set settled_num=#{settledNum} where order_no=#{orderNo}
    </update>
    
    <update id="updateBatchOrderSettledStatus" parameterType="map">
    	update wx_t_order
    	set is_settled=#{isSettled}
    	where order_no in
	    	<foreach item="order" index="index" collection="settledOrderList" open="(" separator="," close=")">  
				 #{order.orderNo} 
			</foreach> 
	    <if test="type == 'danci'">
	    	and  remaining_service_times=0
	    </if>
    </update>
    
	<select id="getOrdersByOrderCodes"  parameterType="map" resultMap="BaseResultMap">
        SELECT tt_order.*
        FROM wx_t_order tt_order
        WHERE
        <if test="orderCodes!=null">
            tt_order.order_no IN
            <foreach item="orderno" index="index" collection="orderCodes" open="(" separator="," close=")">  
                 #{orderno}  
            </foreach> 
        </if>
    </select>  
    
    <select id="getPlanteNumbersByConditions"  parameterType="map" resultType="String">
        SELECT distinct tt_order.plate_number
        FROM wx_t_order tt_order
        WHERE
        <if test="tmpordertype!=null">
             tt_order.order_type =#{tmpordertype} 
          and
        </if>
         <if test="tmporderstatus!=null">
             tt_order.status !=#{tmporderstatus} 
          and
        </if>
        <if test="tmpplantenumbers!=null">
            tt_order.plate_number IN
            <foreach item="planteNo" index="index" collection="tmpplantenumbers" open="(" separator="," close=")">  
                 #{planteNo}  
            </foreach> 
        </if>
    </select> 
    
	<select id="selectOrderList" resultMap="BaseResultMap"
		parameterType="java.util.List">
		select tt_order.*,t_org.organization_name org_name
		from wx_t_order tt_order
		LEFT JOIN wx_t_organization t_org 
        ON t_org.id = tt_order.partner_id
		where 1=1 and tt_order.id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
	</select>
	<update id="updateBatch" parameterType="map" >
    <foreach collection="orderVos" item="item" index="index" open="begin" close="end;" separator=";">
			update wx_t_order
			    <set>
                  bill_id = #{item.billId,jdbcType=NVARCHAR},
                  status = #{item.status,jdbcType=NVARCHAR},
                  delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP}
                 </set>
			where wx_t_order.id = #{item.id,jdbcType=BIGINT} 
			</foreach>
    </update>
    
	<update id="updateOrderInfos" parameterType="com.chevron.pms.model.OrderVo">
	     update wx_t_order
	     <set>
	     <if test="serviceTime!=null and serviceTime !='' ">
		 service_time = #{serviceTime,jdbcType=TIMESTAMP},
		</if>
		 <if test="status!=null and status != '' ">
		 status = #{status,jdbcType=NVARCHAR},
		</if>
		 <if test="completionTime!=null and completionTime !='' ">
		 completion_time = #{completionTime,jdbcType=TIMESTAMP},
		</if>
		 <if test="plateNumber!=null and plateNumber !='' ">
		 plate_number = #{plateNumber,jdbcType=NVARCHAR},
		</if>
		 <if test="workshopName!=null and workshopName !='' ">
		 work_shop_name = #{workshopName,jdbcType=NVARCHAR},
		</if>
		 <if test="productEvalute!=null and productEvalute !='' ">
		 product_evalute = #{productEvalute,jdbcType=NVARCHAR},
		</if>
		 <if test="experienceEvalute!=null and experienceEvalute !='' ">
		 experience_evalute = #{experienceEvalute,jdbcType=NVARCHAR},
		</if>
		 <if test="technicianEvalute!=null and technicianEvalute !='' ">
		 technician_evalute = #{technicianEvalute,jdbcType=NVARCHAR},
		</if>
		<if test="environEvalute!=null and environEvalute !='' ">
		 environ_evalute = #{environEvalute,jdbcType=NVARCHAR},
		</if>
		<if test="otherSuggest!=null and otherSuggest !='' ">
		 other_suggest = #{otherSuggest,jdbcType=NVARCHAR},
		</if>
		<if test="verificateCode!=null and verificateCode !='' ">
		 verificate_code = #{verificateCode,jdbcType=NVARCHAR},
		</if>
		<if test="workShopId != null" >
             work_shop_id = #{workShopId,jdbcType=BIGINT},
        </if>
        <if test="verificateFlag !=null" >
           verificate_flag = #{verificateFlag,jdbcType=INTEGER},
        </if>
	     </set>
	     where 1=1 and (wx_t_order.id = #{id,jdbcType=BIGINT} or wx_t_order.order_no = #{orderNo,jdbcType=NVARCHAR})
	</update>
	
	<select id="getOrderList"  parameterType="com.chevron.o2oorder.model.OrderO2OPageVo" resultMap="BaseResultMap">
        select * from wx_t_order tt_order
	   	where tt_order.order_type = 'O2O_SHOP'
	   	<if test="queryField!=null and queryField!= ''">
	   	    	and
	   	    		(tt_order.plate_number like '%' + #{queryField} + '%' 
	   	    	or   tt_order.buy_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.region_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_user_name like '%' + #{queryField} + '%'
	   	    	or   tt_order.receive_phone_no like '%' + #{queryField} + '%'
	   	    	or 	 tt_order.order_no like '%' + #{queryField} + '%'
	   	    	     )
	   	  </if>
	   		<!-- 高级搜索 -->
	   	<if test="queryField==null">
				<if test="plateNumber!=null and plateNumber!=''" >
					and tt_order.plate_number like '%' + #{plateNumber} + '%'  
				</if>
				<if test="buyUserName!=null and buyUserName!=''" >
				 and tt_order.buy_user_name like  '%' + #{buyUserName} + '%'   
				</if>
				<if test="receiveUserName!=null and receiveUserName!=''" >
				  and	tt_order.receive_user_name like '%' + #{receiveUserName} + '%'  
				</if>
				<if test="receiveUserPhone!=null and receiveUserPhone!=''" >
				  and	tt_order.receive_phone_no like '%' + #{receiveUserPhone} + '%'  
				</if>
				<if test="sendProductTime!=null and sendProductTime!=''" >
				 and	Convert(varchar(10),tt_order.delivery_time,120)  like '%' + #{sendProductTime} + '%' 
				</if>
				<if test="orderCreateTime!=null and orderCreateTime != ''" >
				  and	<![CDATA[tt_order.create_time>= #{orderCreateTime}]]>
				</if>
				<if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
					and <![CDATA[tt_order.create_time<#{orderCreateEndTime}]]>
				</if>
			    <if test="workShopId != null ">
                    and tt_order.work_shop_id=#{workShopId}
                </if>
		       <if test="phoneNo!=null and phoneNo!=''" >
			       and tt_order.phone_no = #{phoneNo,jdbcType=NVARCHAR}
		      </if>
		       <choose>
		         <when test="statusList != null and statusList.size()>0">
		           and tt_order.status in
		           <foreach collection="statusList" item="item" index="index"  open="(" separator="," close=")">
		              ${item}
		          </foreach>
		    </when>
		  <otherwise>
		    <if test="status != null and status !='' " >
			 and tt_order.status = #{status,jdbcType=NVARCHAR}
		    </if>
          </otherwise>
		 </choose>
		</if>
		 and 1=1
	</select>
	
	<select id="getOrderDetailById" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		tt_order.*
		from wx_t_order tt_order
		where tt_order.id = #{id,jdbcType=BIGINT}
	</select>
	
	<select id="getOrderDetailByCode" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		tt_order.*
		from wx_t_order tt_order
		where tt_order.verificate_code = #{code,jdbcType=BIGINT}
	</select>
	
	<update id="updateOrderCodeById" parameterType="com.chevron.pms.model.OrderVo">
	     update wx_t_order
	     <set>
		 verificate_code = #{verificateCode,jdbcType=NVARCHAR},
	     </set>
	     where 1=1 and wx_t_order.id = #{id,jdbcType=BIGINT}
	</update>
	
	<select id="queryBatchByOrderNo" resultMap="BaseResultMap">
	 select
		tt_order.*
		from wx_t_order tt_order
		where tt_order.order_no in
		 <foreach collection="list" item="item" index="index"  open="(" separator="," close=")">
		         #{item,jdbcType=NVARCHAR}
		 </foreach>
	</select>
    
    <delete id="delteBatchOrderByOrderIds" parameterType="map">
  	 delete
  	 	from wx_t_order
  	 	where
   		 <if test="orderIds!=null">
	    	id in
	    	<foreach item="orderId" index="index" collection="orderIds" open="(" separator="," close=")">  
				 #{orderId}  
			</foreach> 
	    </if>
   </delete>
   
   <select id="getOrdersConfrimListByCondition" parameterType="com.chevron.pms.model.OrderCondition" resultMap="BaseResultMap">
	   SELECT
		DISTINCT tt_order.*,tt_workshop.work_shop_name order_workshopname, u.ch_name execut_name,
		(
			SELECT
				p.dic_item_name
			FROM
				wx_t_dic_item p
			WHERE
				p.dic_type_code = 'order.status'
			AND p.dic_item_code = tt_order.status
		) AS status_meaning,cos.region, cos.sales_name_cn sales_name, o.organization_name partner_name
		FROM
			wx_t_order tt_order
		LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
		LEFT JOIN wx_t_user u  ON u.user_id = tt_order.creator
		LEFT JOIN wx_t_region r1 ON tt_workshop.region_id = r1.id
		LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
		LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
		left JOIN wx_t_organization o ON tt_workshop.partner_id = o.id
		left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
		left join dw_customer_org_sales cos on cos.distributor_id=pe.distributor_id and cos.channel_weight&amp;(case when tt_workshop.business_weight&amp;1>0 then 1 else 2 end)>0
			and exists (select 1 from dw_region_sales_channel_rel rsc where rsc.region_name=cos.region and rsc.bu='Indirect')
		WHERE
			1 = 1
			AND tt_order.create_time >= '2020-04-01 00:00:00.000'
		<if test="partnerId != null and partnerId != ''">
			AND tt_workshop.partner_id = #{partnerId}
		</if>
		<if test="orderStatus != null and orderStatus != ''">
			AND tt_order.status = #{orderStatus}
		</if>
		<if test="executeUserId">
			AND tt_order.creator = #{executeUserId}
		</if>
		<if test="executeUserName != null and executeUserName != '' ">
			AND u.ch_name like  '%'+ #{executeUserName} +'%'
		</if>
		
		<if test="dateStart != null">
		    and tt_order.CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
	  	</if>
	 	<if test="dateEnd != null">
	   		and tt_order.CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
	  	</if>
		<if test="workshopName != null and workshopName != ''">
			AND tt_workshop.work_shop_name like '%'+ #{workshopName} +'%'
		</if>
		<if test="source">
			AND tt_order.source = #{source}
		</if>
		<if test="prov != null">
			and r3.id = #{prov,jdbcType=BIGINT}
		</if>
		<if test="city != null">
			and r2.id = #{city,jdbcType=BIGINT}
		</if>
		<if test="dist != null">
			and r1.id = #{dist,jdbcType=BIGINT}
		</if>
		<if test="regionName != null and regionName != ''">
		and cos.region=#{regionName}
		</if>
		<if test="salesCai != null and salesCai != ''">
		and cos.sales_cai=#{salesCai}
		</if>
   </select>
   
   <select id="queryBatchByOrderId" resultMap="BaseResultMap">
   	 select
		tt_order.*
		from wx_t_order tt_order
		where tt_order.id in
		 <foreach collection="list" item="item" index="index"  open="(" separator="," close=")">
		         #{item,jdbcType=BIGINT}
		 </foreach>
   </select>
   
	<!-- 基础查询条件 -->
	<sql id="base_cond">
	</sql>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select 
		t1.id, t1.order_no, t1.order_type, 
		t1.source, 
		t1.source_id, 
		t1.pay_type, pay_state, 
		t1.total_product_price, 
		t1.total_delivery_price, 
		t1.total_order_price, 
		t1.total_product_prefer_price, 
		t1.total_delivery_prefer_price, 
		t1.total_order_prefer_price, 
		t1.total_order_pay_price, 
		t1.total_order_need_pay_price, 
		t1.work_shop_id, 
		t1.status, 
		t1.create_time, 
		t1.update_time, 
		t1.creator, 
		t1.remark,
		t1.address,
		t1.receive_user_name,
		t1.receive_phone_no,
		t1.type,
		t1.phone_no,
		t1.region_name
		,(select u.CH_NAME from wx_t_user u where u.USER_ID = t1.creator) as creator_display_name
		,w1.work_shop_name,w1.customer_type
		,(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=w1.customer_type) customer_type_text
		,(select p.codename from wx_t_properties p where p.codetype = 'order_status' and p.code = t1.status) as status_meaning 
		,t1.ext_property1,t1.ext_property2, t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.ext_property6, t1.ext_property7,
			 t1.ext_property8, t1.ext_property9, t1.ext_property10,
			 s1.name from_store_name, w2.work_shop_name trans_workshop, w1.work_shop_address workshop_address, u1.ch_name logistics_provider_user_name
		  from wx_t_order t1
		  left join wx_t_work_shop w1 on w1.id = t1.work_shop_id
		  left join wx_t_store2021 s1 on t1.source='OUT_STOCK' and s1.id=t1.ext_property1
		  left join wx_t_work_shop w2 on t1.source='OUT_STOCK' and w2.id = t1.ext_property6
		  left join wx_t_user u1 on t1.source='OUT_STOCK' and u1.user_id=t1.ext_property8
		 where 1=1
		 <if test="id != null">
		 and t1.id=#{id}
		 </if>
		 <include refid="base_cond"/>
	</select>
</mapper>