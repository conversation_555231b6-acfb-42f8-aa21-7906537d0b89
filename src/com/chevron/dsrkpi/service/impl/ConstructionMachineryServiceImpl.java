package com.chevron.dsrkpi.service.impl;

import com.chevron.dsrkpi.service.ConstructionMachineryService;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;

import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Construction Machinery操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2020-05-12 15:34
 */
@Service
public  class ConstructionMachineryServiceImpl implements ConstructionMachineryService {
	
	@Autowired
	private WorkshopMasterBizService  workshopMasterBizServiceImpl;
	
	private final static Logger log = Logger.getLogger(ConstructionMachineryServiceImpl.class);

	@Override
	public JsonResponse save(WorkshopMaster record) {
		JsonResponse map = new JsonResponse();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				workshopMasterBizServiceImpl.insert(record);
			}else{
				workshopMasterBizServiceImpl.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.ConstructionMachineryServiceImpl.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse saveForEditPage(WorkshopMaster record) {
		JsonResponse map = new JsonResponse();
		log.info("saveForEditPage: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				workshopMasterBizServiceImpl.insert(record);
			}else{
				workshopMasterBizServiceImpl.update(record);
			}
			log.info("saveForEditPage success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.ConstructionMachineryServiceImpl.saveForEditPage", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse delete(List<Long> ids) {
		JsonResponse map = new JsonResponse();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			workshopMasterBizServiceImpl.delete(ids);
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.ConstructionMachineryServiceImpl.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}

	@Override
	public JsonResponse getBean(Long id){
		JsonResponse map = new JsonResponse();
		log.info("getBean: " + id.toString());
		try {
			map.put("bean", workshopMasterBizServiceImpl.getBean(id));
			log.info("getBean success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.ConstructionMachineryServiceImpl.getBean", id.toString());
		}
		return map;
	}
	
	@Override
	public JsonResponse getMachineryListByContion(WorkshopMaster record) {
		JsonResponse resMap = new JsonResponse();
		/*try {
			WxTUser curUser = ContextUtil.getCurUser();
			record.setPartnerId(curUser.getOrgId());
			//List<WorkshopMaster> machineryListByContion = workshopMasterBizServiceImpl.getMachineryListByContion(record);
			//resMap.setListResult(machineryListByContion);
			//resMap.setTotalOfPaging(record.getTotalCount());
		} catch (WxPltException e) {
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,e.getMessage());
			log.error("constructionMachineryBizService.getMachineryListByContion:" + e.getMessage());
		}*/
		return resMap;
	}

	@Override
	public JsonResponse deleteByLogic(List<Long> ids) {
		JsonResponse map = new JsonResponse();
	/*	log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			//constructionMachineryBizService.deleteByLogic(ids);
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.ConstructionMachineryServiceImpl.delete", JsonUtil.writeValue(ids));
		}*/
		return map;
	}
}
