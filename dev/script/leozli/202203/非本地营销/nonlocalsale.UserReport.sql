select CONVERT(varchar(100), lur1.report_time, 20) report_time, r3.region_name report_province,
r2.region_name report_city, p1.name product_name, lur1.sku, did1.dic_item_name display_channel_text,
lur1.display_channel, lur1.logistics_code, 
        (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',lur1.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as product_province,
        (select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',lur1.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as product_city,
		cos1.customer_name_cn from_distributor_name, lur1.from_distributor_id,
		cos1.region from_region,lur1.product_date, lur1.out_date, lur1.product_batch, lur1.out_order_no,
		case when lur1.ext_flag&2048>0 then '否' else '是' end local_sale_text
from wx_t_non_local_user_report lur1 
left join wx_t_region r2 on r2.region_code=lur1.report_region_id
left join wx_t_region r3 on r3.id=r2.parent_id
left join wx_t_product p1 on p1.sku=lur1.sku
left join wx_t_dic_item did1 on did1.dic_type_code='NonLocalSales.displayChannel' and did1.dic_item_code=lur1.display_channel
left join dw_customer_org_sales cos1 on cos1.distributor_id=lur1.from_distributor_id and cos1.channel_weight&lur1.auth_channel>0
where (#VARCHAR,fromRegion# is null or #VARCHAR,fromRegion#='' or cos1.region=#VARCHAR,fromRegion#)
and (#BIGINT,fromDistributorId# is null or lur1.from_distributor_id#BIGINT,fromDistributorId#)
and (#INTEGER,displayChannel# is null or lur1.display_channel&#INTEGER,displayChannel#>0)
and (#INTEGER,permissionWeight#&1>0 or (#INTEGER,permissionWeight#&10>0 and exists (select 1 from dw_sales_role dp_001_sr1 where dp_001_sr1.[sales_cai]=cos1.sales_cai and dp_001_sr1.sales_cai_level like '%_'+#VARCHAR,cai#+'%')))
and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or lur1.report_time>=#VARCHAR,reportDateFrom#)
and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or lur1.report_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
and (#INTEGER,localSaleFlag# is null or #INTEGER,localSaleFlag#<0 or (#INTEGER,localSaleFlag#=1 and lur1.ext_flag&2048=0) or (#INTEGER,localSaleFlag#=0 and lur1.ext_flag&2048>0))

