select * from view_mkt_apply where channel='C&I'
SELECT CASE WHEN mkt_type IN ('STORE_FRONT', 'STORE_IN_STORE') AND local_make = '0' AND (id2 IS NULL OR
                  current_step2 < 2) THEN 0 /*非当地制作，供应商报价前没有价格*/ WHEN mkt_type IN ('STORE_FRONT', 'STORE_IN_STORE') AND id2 IS NULL AND channel = 'C&I' AND 
                  current_step1 < 2 THEN 0 /*FLSR提交前，没有价格*/ WHEN id3 IS NOT NULL AND ((local_make = '0' AND current_step3 > 1) OR
                  (local_make = '1' AND curr_auditor_id3 > 2)) THEN isnull(CONVERT(float, materiel_money), cost_money) ELSE cost_money END AS price, id, id2, id3, flow_finish1, flow_finish2, flow_finish3, channel, mkt_type, partner_id, organization_name, 
                  create_time, create_by, apply_user, sales_cai, apply_person_name, apply_user_email, apply_user_mobile, current_step1, pre_auditor_id1, pre_auditor1, pre_auditor_role1, curr_auditor_id1, curr_auditor1, curr_auditor_role1, 
                  audit_status1, audit_time1, store_name, CONVERT(bigint, workshop_id) AS workshop_id, seminar_cost, cost_money, materiel_money, state, expiration_date, is_alert, volumes, mkt_type2, offer_time, offer_uid, offer_user, current_step2, 
                  pre_auditor_id2, pre_auditor2, pre_auditor_role2, curr_auditor_id2, curr_auditor2, curr_auditor_role2, audit_status2, audit_time2, mkt_type3, reimbursement_time, reimbursement_uid, reimbursement_user, current_step3, pre_auditor_id3, 
                  pre_auditor3, pre_auditor_role3, curr_auditor_id3, curr_auditor3, curr_auditor_role3, audit_status3, audit_time3, asm_audit_status, asm_audit_time, asm_audit_status1, asm_audit_time1, asm_audit_status2, asm_audit_time2, local_make, 
                  isnull(materiel_money, cost_money) AS cost_money1, mkt_type + '/' + store_mkt_type + '/' + workshop_id AS apply_key, store_mkt_type
FROM     (SELECT t1.id, t2.id AS id2, t3.id AS id3, t1.flow_finish AS flow_finish1, t2.flow_finish AS flow_finish2, t3.flow_finish AS flow_finish3, t1.channel, t1.mkt_type, t1.partner_id, t1.organization_name, t1.create_time, t1.create_by, 
                                    t10.login_name AS apply_user, t10.cai AS sales_cai, t10.ch_name AS apply_person_name, t10.email AS apply_user_email, t10.mobile_tel AS apply_user_mobile, t1.current_step AS current_step1,
                                        (SELECT TOP 1 operator
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t1.id AND step = t1.current_step - 1
                                         ORDER BY step) AS pre_auditor_id1,
                                        (SELECT TOP 1 operator_name
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t1.id AND step = t1.current_step - 1
                                         ORDER BY step) AS pre_auditor1,
                                        (SELECT TOP 1 node_user_role
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t1.id AND step = t1.current_step - 1
                                         ORDER BY step) AS pre_auditor_role1,
                                        (SELECT TOP 1 operator
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t1.id AND step = t1.current_step
                                         ORDER BY step) AS curr_auditor_id1,
                                        (SELECT TOP 1 operator_name
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t1.id AND step = t1.current_step
                                         ORDER BY step) AS curr_auditor1,
                                        (SELECT TOP 1 node_user_role
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t1.id AND step = t1.current_step
                                         ORDER BY step) AS curr_auditor_role1,
                                        (SELECT TOP 1 audit_status
                                         FROM      dbo.wx_t_mkt_history t6
                                         WHERE   mkt_apply_id = t1.id
                                         ORDER BY id DESC) AS audit_status1,
                                        (SELECT TOP 1 audit_time
                                         FROM      dbo.wx_t_mkt_history t6
                                         WHERE   mkt_apply_id = t1.id
                                         ORDER BY step DESC) AS audit_time1,
                                        (SELECT TOP 1 field_value
                                         FROM      dbo.wx_t_mkt_field t5
                                         WHERE   mkt_apply_id = t1.id AND field_name = 'storeNameDoor') AS store_name,
                                        (SELECT TOP 1 field_value
                                         FROM      dbo.wx_t_mkt_field t5
                                         WHERE   mkt_apply_id = t1.id AND field_name = 'workShopId') AS workshop_id, ISNULL
                                        ((SELECT TOP 1 field_value
                                          FROM      dbo.wx_t_mkt_field t5
                                          WHERE   mkt_apply_id = t1.id AND field_name = 'moneyCost'), 0) AS seminar_cost, ISNULL
                                        ((SELECT TOP 1 field_value
                                          FROM      dbo.wx_t_mkt_field t5
                                          WHERE   mkt_apply_id = CASE WHEN NOT EXISTS
                                                                (SELECT 1
                                                                 FROM      dbo.wx_t_mkt_field mf001
                                                                 WHERE   mf001.mkt_apply_id = t2.id AND mf001.field_name = 'moneyCost') THEN t1.id ELSE t2.id END AND field_name = 'moneyCost'), 0) AS cost_money,
                                        (SELECT TOP 1 field_value
                                         FROM      dbo.wx_t_mkt_field t5
                                         WHERE   mkt_apply_id = t3.id AND field_name = 'materielMoney') AS materiel_money, t1.state, t7.expiration_date, t7.is_alert, t7.volumes, t2.mkt_type AS mkt_type2, t2.create_time AS offer_time, t2.create_by AS offer_uid, 
                                    t9.ch_name AS offer_user, t2.current_step AS current_step2,
                                        (SELECT TOP 1 operator
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t2.current_step - 1
                                         ORDER BY step) AS pre_auditor_id2,
                                        (SELECT TOP 1 operator_name
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t2.current_step - 1
                                         ORDER BY step) AS pre_auditor2,
                                        (SELECT TOP 1 node_user_role
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t2.current_step - 1
                                         ORDER BY step) AS pre_auditor_role2,
                                        (SELECT TOP 1 operator
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t2.current_step
                                         ORDER BY step) AS curr_auditor_id2,
                                        (SELECT TOP 1 operator_name
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t2.current_step
                                         ORDER BY step) AS curr_auditor2,
                                        (SELECT TOP 1 node_user_role
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t2.current_step
                                         ORDER BY step) AS curr_auditor_role2,
                                        (SELECT TOP 1 audit_status
                                         FROM      dbo.wx_t_mkt_history t6
                                         WHERE   mkt_apply_id = t2.id
                                         ORDER BY id DESC) AS audit_status2,
                                        (SELECT TOP 1 audit_time
                                         FROM      dbo.wx_t_mkt_history t6
                                         WHERE   mkt_apply_id = t2.id
                                         ORDER BY step DESC) AS audit_time2, t3.mkt_type AS mkt_type3, t3.create_time AS reimbursement_time, t3.create_by AS reimbursement_uid, t8.ch_name AS reimbursement_user, t3.current_step AS current_step3,
                                        (SELECT TOP 1 operator
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t3.current_step - 1
                                         ORDER BY step) AS pre_auditor_id3,
                                        (SELECT TOP 1 operator_name
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t3.current_step - 1
                                         ORDER BY step) AS pre_auditor3,
                                        (SELECT TOP 1 node_user_role
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t2.id AND step = t3.current_step - 1
                                         ORDER BY step) AS pre_auditor_role3,
                                        (SELECT TOP 1 operator
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t3.id AND step = t3.current_step
                                         ORDER BY step) AS curr_auditor_id3,
                                        (SELECT TOP 1 operator_name
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t3.id AND step = t3.current_step
                                         ORDER BY step) AS curr_auditor3,
                                        (SELECT TOP 1 node_user_role
                                         FROM      dbo.wx_t_mkt_flow_node t4
                                         WHERE   mkt_apply_id = t3.id AND step = t3.current_step
                                         ORDER BY step) AS curr_auditor_role3,
                                        (SELECT TOP 1 audit_status
                                         FROM      dbo.wx_t_mkt_history t6
                                         WHERE   mkt_apply_id = t3.id
                                         ORDER BY id DESC) AS audit_status3,
                                        (SELECT TOP 1 audit_time
                                         FROM      dbo.wx_t_mkt_history t8
                                         WHERE   mkt_apply_id = t3.id
                                         ORDER BY step) AS audit_time3, t1.asm_audit_status, t1.asm_audit_time, t1.asm_audit_status AS asm_audit_status1, t1.asm_audit_time AS asm_audit_time1, t1.asm_audit_status AS asm_audit_status2, 
                                    t1.asm_audit_time AS asm_audit_time2,
                                        (SELECT field_value
                                         FROM      dbo.wx_t_mkt_field mf
                                         WHERE   t1.id = mkt_apply_id AND field_name = 'localMake') AS local_make,
                                        (SELECT TOP 1 field_value
                                         FROM      dbo.wx_t_mkt_field t5
                                         WHERE   mkt_apply_id = t1.id AND field_name = 'storeMktType') AS store_mkt_type
                  FROM      dbo.wx_t_mkt_apply t1 LEFT JOIN
                                    dbo.wx_t_mkt_apply t2 ON t1.id = t2.related_id LEFT JOIN
                                    dbo.wx_t_mkt_apply t3 ON t2.id = t3.related_id LEFT JOIN
                                    dbo.wx_t_user t10 ON t10.user_id = t1.create_by LEFT JOIN
                                    dbo.wx_t_user t9 ON t9.user_id = t2.create_by LEFT JOIN
                                    dbo.wx_t_user t8 ON t8.user_id = t3.create_by LEFT JOIN
                                    dbo.wx_t_mkt_attrs t7 ON t7.apply_id = t1.id
                  WHERE   t1.channel = 'C&I' AND t1.mkt_type IN ('STORE_FRONT', 'STORE_IN_STORE', 'STORE_OTHERS', 'SEMINAR')) ma