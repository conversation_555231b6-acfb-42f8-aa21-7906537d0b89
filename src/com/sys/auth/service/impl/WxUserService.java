package com.sys.auth.service.impl;

import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.chevron.master.service.PartnerGiftPackageService;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springside.modules.security.utils.Digests;
import org.springside.modules.utils.Encodes;

import com.chevron.interfaces.business.MaterialPlatformBizService;
import com.chevron.interfaces.utils.MaterialPlatformUpdateType;
import com.chevron.pms.dao.UserChargeRegionMapper;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.SaltUtil;
import com.common.util.StringUtils;
import com.sys.auth.business.UserBizService;
import com.sys.auth.dao.*;
import com.sys.auth.model.*;
import com.sys.auth.model.WxTUserExample.Criteria;
import com.sys.auth.service.WxRoleServiceI;
import com.sys.auth.service.WxUserRoleService;
import com.sys.auth.service.WxUserServiceI;
import net.sf.json.JSONArray;

@Service
public class WxUserService implements WxUserServiceI{

	@Resource
	WxTMenuMapper wxTMenuMapper;

	@Resource
	WxTOrgMapper wxTOrgMapper;

	@Resource
	WxTRolesourceMapper wxTRolesourceMapper;

	@Resource
	WxTUserbaseMapper wxTUserbaseMapper;

	@Resource
	WxTPlogMapper wxTPlogMapper;

	@Resource
	WxTDataMapper wxTDataMapper;

	@Resource
	WxTUserMapper wxTUserMapper;

	@Resource
	WxTUserBakMapper wxTUserBakMapper;

	@Resource
	WxUserRoleService wxUserRoleServiceImpl;

	@Resource
	WxRoleServiceI wxRoleServiceImpl;
	
	@Resource
	UserChargeRegionMapper userChargeRegionMapper;
	
	@Autowired
	private MaterialPlatformBizService materialPlatformBizService;
	
	@Autowired
	private UserBizService userBizService;
	@Autowired
	private WxTLoginErrorLogMapper wxTLoginErrorLogMapper;
	@Resource
	private PartnerGiftPackageService partnerGiftPackageService;

	private final static Logger log = Logger.getLogger(WxUserService.class);

	public List<Long> getChildrenOrgList(WxTOrg targetOrg){
		List<Long> list = new ArrayList<Long>();
		list.add(targetOrg.getOrgId());
		if(targetOrg.getDeptType() == 0){
			Long[] arr = new Long[list.size()];
			list.toArray(arr);
			List<Long> chilrenList = wxTOrgMapper.selOrgIdListByParent(arr, ContextUtil.getCurUser().getTenantId());
			for(Long child:chilrenList){
				list.addAll(getChildrenOrgList(wxTOrgMapper.selectByPrimaryKey(child)));
			}
		}
		return list;
	}

	@Override
	public Map<String, Object> findWholeOrgTree() throws Exception {
		Map<String, Object> map = new HashMap<String,Object>();
//		String cityOrgType = WxTOrg.ORG_TYPE_CITY;
		String projectOrgType = WxTOrg.ORG_TYPE_PROJECT;
		String positionOrgType = WxTOrg.ORG_TYPE_POSTION;
		try {
			List<WxTOrg> list = wxTOrgMapper.selInitialWholeOrgTree(ContextUtil.getCurUser().getTenantId(),ContextUtil.getCurUserId(), projectOrgType, positionOrgType);
			List<LigerUITreeNode> treeList = new ArrayList<LigerUITreeNode>();
			if (null != list && list.size() > 0) {
				LigerUITreeNode tempObj = null;
				for (WxTOrg org : list) {
					tempObj = new LigerUITreeNode();
					tempObj.setId(org.getOrgId());
					tempObj.setText(org.getOrgName());
					tempObj.setPid(org.getOrgPid());
//					if (org.getOrgId() != 1 && org.getOrgPid() != 1) {
//						tempObj.setIsExpand(false);
//					}
					treeList.add(tempObj);
				}
			}
			map.put("code", "success");
			map.put("resultData", treeList);
		} catch (Exception e) {
			log.error("",e);
			map.put("code", "systemerror");
		}
		return map;
	}

	@Override
	public Map<String,Object> findAllOrg(){
		Map<String, Object> map = new HashMap<String,Object>();
//		String cityOrgType = WxTOrg.ORG_TYPE_CITY;
//		String projectOrgType = WxTOrg.ORG_TYPE_PROJECT;
//		String positionOrgType = WxTOrg.ORG_TYPE_POSTION;
		try {
			List<WxTOrg> list = wxTOrgMapper.selWholeOrgTreeByTenantId(ContextUtil.getCurUser().getTenantId());
			List<LigerUITreeNode> treeList = new ArrayList<LigerUITreeNode>();
			if (null != list && list.size() > 0) {
				LigerUITreeNode tempObj = null;
				for (WxTOrg org : list) {
					tempObj = new LigerUITreeNode();
					tempObj.setId(org.getOrgId());
					tempObj.setText(org.getOrgName());
					tempObj.setPid(org.getOrgPid());
//					if (org.getOrgId() != 1 && org.getOrgPid() != 1) {
//						tempObj.setIsExpand(false);
//					}
					treeList.add(tempObj);
				}
			}
			map.put("code", "success");
			map.put("resultData", treeList);
		} catch (Exception e) {
			log.error("",e);
			map.put("code", "systemerror");
		}
		return map;
	}

	@Override
	public Map<String,Object> createWxTUser(String loginName, String userNo, String chName, String cnName,
			String allowLogin, String sex, Date birthday, String address, String email,
			String mobileTel, String fixedTel, Long orgId, String orgName, String postion, Date userIntime,
			String description, Long post, String postionName, String password, Long tenantId, Long roleId,String roleIds) throws Exception{
		Map<String, Object> map = new HashMap<String,Object>();
		int count = wxTUserMapper.countUserByLoginName(loginName, null) +  wxTUserMapper.findUserByUserNo(userNo, null);
		if(count == 0){
			WxTUser user = new WxTUser();
			user.setLoginName(loginName);
			user.setUserNo(userNo);
			user.setChName(chName);
			user.setCnName(cnName);
			user.setAllowLogin(allowLogin);
			user.setSex(sex);
			user.setBirthday(birthday);
			user.setAddress(address);
			user.setEmail(email);
			user.setMobileTel(mobileTel);
			user.setFixedTel(fixedTel);
			user.setOrgId(orgId);
			user.setPost(post);
			user.setUserIntime(userIntime);
			user.setDescription(description);
			user.setPostionName(postionName);
			user.setPostion(postion);
			user.setPassword(password);
			//salt暂时用hardcode
			String salt = SaltUtil.getSalt();
			user.setSalt(salt);
			byte[] hashPassword = Digests.sha1(password.getBytes(),	Encodes.decodeHex(salt), Constants.HASH_INTERATIONS);
			String pwd = Encodes.encodeHex(hashPassword);
			//String hashPassword = SaltUtil.getHash(password, salt);
			user.setPassword(pwd);
			user.setPwdLasttime(new Date());

			user.setIsValid("1");
			user.setStatus(1);

			user.setXgTime(new Date());
			user.setXgUser(ContextUtil.getCurUser().getLoginName());
			user.setXzTime(new Date());
			user.setXzUser(ContextUtil.getCurUser().getLoginName());
			boolean bool = false;
			if(tenantId != null){
				user.setTenantId(tenantId);
				bool= true;
			}else{
				user.setTenantId(ContextUtil.getCurUser().getOrgId());////modify by bo.liu 0818 cru.getTenantId()
			}
			wxTUserMapper.insertSelective(user);
			//设置用户权限为超级管理员
			if(bool){
				long id  = user.getUserId();
				WxTUserrole record = new WxTUserrole();
				record.setTenantId(tenantId);
				record.setUserId(id);
				record.setRoleId(roleId);
				record.setGrantUserid(id);
				record.setStatus(1);
				record.setXgSj(new Date());
				wxUserRoleServiceImpl.createUserRole(record);
			}
			map.put("resultData", "success");
		}
		else{
			map.put("resultData", "fail");
		}
		return map;
	}

	@Override
	public Map<String,Object> getWxTUserInfoByUserid(String userId){
		Map<String, Object> map = new HashMap<String,Object>();
		Long id = Long.parseLong(userId);
		WxTUser user = wxTUserMapper.selUserByUserid(id);
		map.put("resultData",user);
		return map;
	}

	@Override
	public Map<String,Object> updateWxTUser(String userId, String loginName, String userNo, String chName, String cnName,
			String allowLogin, String sex, Date birthday, String address, String email,
			String mobileTel, String fixedTel, Long orgId, String orgName, String postion, Date userIntime,
			String description, Long post, String postionName, String password, String newPassword,Long tenantId,String roleIds){
		Map<String, Object> map = new HashMap<String,Object>();
		Long id = Long.parseLong(userId);
		int count = wxTUserMapper.findUserByLoginNameOrUserNoOrUserId(loginName, userNo, id);
		WxTUser user = wxTUserMapper.selUserByUserid(id);
		if(user != null && count == 1){
			user.setLoginName(loginName);
			user.setUserNo(userNo);
			user.setChName(chName);
			user.setCnName(cnName);
			user.setAllowLogin(allowLogin);
			user.setSex(sex);
			user.setBirthday(birthday);
			user.setAddress(address);
			user.setEmail(email);
			user.setMobileTel(mobileTel);
			user.setFixedTel(fixedTel);
			user.setOrgId(orgId);
			user.setPost(post);
			user.setUserIntime(userIntime);
			user.setDescription(description);
			user.setPostionName(postionName);
			user.setPostion(postion);
			user.setTenantId(tenantId);
			if(password != null && !"".equals(password)){
				boolean oldPasswordOk = checkPasswd(password, user.getPassword(), user.getSalt());
				if (oldPasswordOk){
					String newEncryptPassword = toPasswd(newPassword, Encodes.decodeHex(user.getSalt()));
					user.setPassword(newEncryptPassword);
				} else{
					map.put("resultData", "fail");
					map.put("resultmsg", "old password is error!");
					return map;
				}
				user.setPwdLasttime(new Date());
			}

			user.setXgTime(new Date());
			user.setXgUser(ContextUtil.getCurUserId() + "");

			wxTUserMapper.updateByPrimaryKeySelective(user);
			map.put("resultData", "success");
		}else {
			map.put("resultData", "fail");
			map.put("resultmsg", "user is not exsit!");
		}
		return map;
	}
	public static boolean checkPasswd(String inputPasswd, String storePasswd,String salt){
		boolean ok = false;
		try{
			byte[] saltBys = Encodes.decodeHex(salt);
			String inPwd = toPasswd(inputPasswd, saltBys);
			ok = inPwd.equals(storePasswd);
		}catch(Exception ex){
			ex.printStackTrace();
			ok = false;
		}
		return ok;
	}

	private static String toPasswd(String inputPasswd, byte[] salt){
			String pwd = "";
			byte[] hashPassword = Digests.sha1(inputPasswd.getBytes(),	salt, Constants.HASH_INTERATIONS);
			pwd = Encodes.encodeHex(hashPassword);
			return pwd;
		}

	@Override
	public Map<String,Object> removeWxTUser(String userId){
		Map<String, Object> map = new HashMap<String,Object>();
		WxTUser user = wxTUserMapper.selUserByUserid(Long.parseLong(userId));
		if(user != null && user.getStatus() == 1){
			wxTUserMapper.removeUserByUserid(Long.parseLong(userId));
			map.put("resultData", "success");
		}
		else {
			map.put("resultData", "fail");
		}
		return map;
	}

	@Override
	public Map<String,Object> importUserBat(Workbook wb){
		Map<String, Object> map = new HashMap<String,Object>();
		Long currenyUserId = ContextUtil.getCurUser().getOrgId();//modify by bo.liu 0818 cru.getTenantId()
		Long tempFlag = new Date().getTime();
//		Long currenyUserId = ContextUtil.getCurUserId();
		Sheet sheet = wb.getSheetAt(0);
		int rowNum = sheet.getLastRowNum();
		int flag = 0;
		List<Map<String,Object>> errorList = new ArrayList<Map<String,Object>>();
		if(rowNum <= 1){
			map.put("code", "warning");
			map.put("warning", "没有数据");
			return map;
		}
		Row row = sheet.getRow(0);
		if(row.getLastCellNum() != 15){
			map.put("code", "warning");
			map.put("warning", "表格格式错误");
			return map;
		}
		if(!StringUtils.getCellStringValue(row.getCell(0)).equals("登录名") ||
			!StringUtils.getCellStringValue(row.getCell(1)).equals("员工工号") ||
			!StringUtils.getCellStringValue(row.getCell(2)).equals("密码") ||
			!StringUtils.getCellStringValue(row.getCell(3)).equals("中文姓名") ||
			!StringUtils.getCellStringValue(row.getCell(4)).equals("是否允许登录") ||
			!StringUtils.getCellStringValue(row.getCell(5)).equals("性别") ||
			!StringUtils.getCellStringValue(row.getCell(6)).equals("生日") ||
			!StringUtils.getCellStringValue(row.getCell(7)).equals("通讯地址") ||
			!StringUtils.getCellStringValue(row.getCell(8)).equals("电子邮箱") ||
			!StringUtils.getCellStringValue(row.getCell(9)).equals("手机号码") ||
			!StringUtils.getCellStringValue(row.getCell(10)).equals("所属组织") ||
			!StringUtils.getCellStringValue(row.getCell(11)).equals("职位代码") ||
			!StringUtils.getCellStringValue(row.getCell(12)).equals("职位名称") ||
			!StringUtils.getCellStringValue(row.getCell(13)).equals("岗位") ||
			!StringUtils.getCellStringValue(row.getCell(14)).equals("入职日期")){
			map.put("code", "warning");
			map.put("warning", "表格格式错误");
			return map;
		}

		for(int i=1; i<=rowNum; i++){
			flag = 0;
			try {
				WxTUser user = wxTUserMapper.selMaxIdData();
				Long a = (user != null) ? user.getUserId() : 0;
				WxTUserBak userbak = wxTUserBakMapper.selMaxIdData();
				Long b = (userbak != null) ? userbak.getUserId() : 0;
				Long userId = Math.max(a, b) + 1;
				row = sheet.getRow(i);

				//检查该行是否为空行
				int blank_flag = 0;
				for(int j=0;j<=14;j++){
					if(!StringUtils.getCellStringValue(row.getCell(j)).equals("")){
						blank_flag = 1;
						break;
					}
				}
				if(blank_flag == 0){
					continue;
				}


				//todo:对每一个录入数据做格式检查
				String loginName = StringUtils.getCellStringValue(row.getCell(0));
				String userNo = StringUtils.getCellStringValue(row.getCell(1));
				String password = StringUtils.getCellStringValue(row.getCell(2));
				String chName = StringUtils.getCellStringValue(row.getCell(3));
				String allowLogin = StringUtils.getCellStringValue(row.getCell(4));
				String sex = StringUtils.getCellStringValue(row.getCell(5));
				Date birthday = null;
				try{
					birthday = row.getCell(6).getDateCellValue();
				}catch(Exception e){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "生日须按照'YYYY/MM/DD'格式");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}
				String address = StringUtils.getCellStringValue(row.getCell(7));
				String email = StringUtils.getCellStringValue(row.getCell(8));
				String mobile = StringUtils.getCellStringValue(row.getCell(9));
				String orgName = StringUtils.getCellStringValue(row.getCell(10));
				String postion = StringUtils.getCellStringValue(row.getCell(11));
				String postionName = StringUtils.getCellStringValue(row.getCell(12));
				String post = StringUtils.getCellStringValue(row.getCell(13));
				Date userIntime = null;
				try{
					userIntime = row.getCell(14).getDateCellValue();
					userIntime.getTime();
				}catch(Exception e){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "用户入职日期须按照'YYYY-MM-DD'格式");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}

				if(!StringUtils.checkStringNoChinese(password)){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "密码不能包含中文字符");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}
				if(!allowLogin.equals("是") & !allowLogin.equals("否")){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "是否允许登录一项只能填写'是'或'否'");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}
				if(!sex.equals("男") & !sex.equals("女")){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "性别一项只能填写'男'或'女'");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}

				//根据org_name查找对应的org_id
				WxTOrg org = wxTOrgMapper.selOrgByOrgNameAndTenantID(orgName, currenyUserId);
				Long orgId = org.getOrgId();

				if(!post.equals("是") & !post.equals("否")){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "是否分销商一项只能填写'是'或'否'");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}

				//在WxTUser表和WxTUserBak表中检查loginName, userNo是否已经存在
				int x = wxTUserMapper.countUserByLoginName(loginName, currenyUserId) + wxTUserBakMapper.findUserByLoginName(loginName, currenyUserId, tempFlag);
				int y = wxTUserMapper.findUserByUserNo(userNo, currenyUserId) + wxTUserBakMapper.findUserByUserNo(userNo, currenyUserId, tempFlag);
				if(x > 0){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "登录名" + loginName + "已经存在，请检查");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}

				if(y > 0){
					Map<String,Object> error = new HashMap<String,Object>();
					error.put("error", "员工工号" + userNo + "已经存在，请检查");
					error.put("line", i);
					errorList.add(error);
					flag = 1;
				}

				//保存记录
				if(flag == 0){
					String salt = SaltUtil.getSalt();
					user.setSalt(salt);
					byte[] hashPassword = Digests.sha1(password.getBytes(),	Encodes.decodeHex(salt), Constants.HASH_INTERATIONS);
					String pwd = Encodes.encodeHex(hashPassword);
					wxTUserBakMapper.save(userId, loginName, userNo, pwd, salt, chName, (allowLogin.equals("是"))?"T":"F", (sex.equals("男"))?"M":"F", birthday, address,
							email, mobile, orgId, orgName, postion, postionName, userIntime, (post.equals("是"))?"1":"0", currenyUserId, tempFlag);
				}
			}catch(Exception e){
				e.printStackTrace();
				Map<String,Object> error = new HashMap<String,Object>();
				error.put("error", "未知错误");
				error.put("line", i);
				errorList.add(error);
			}
		}
		if(errorList.size() == 0){
			wxTUserBakMapper.copyUserBakToUser(tempFlag);
			map.put("code", "success");
		}
		else{
			map.put("code", "error");
			map.put("error", errorList);
		}
		wxTUserBakMapper.delAllUserByTempFlag(tempFlag);
		return map;
	}

//根据当前orgid获取所有子orgId
	public  List<Long> getChildOrgsByOrgId(String orgId)
	{
		List<Long> orgIdlst = wxTOrgMapper.getAllChildOrgId(Long.parseLong(orgId));
		return orgIdlst;
	}

	/**
	 * 判断指定的用户（userid） 是否包含指定的角色（roleId）
	 * @param userId
	 * @param roleId
	 */
	public boolean isIncludeRole(Long userId,Long roleId){
		WxTUserrole userRole = wxUserRoleServiceImpl.selectUserRoleByUserIdAndRoleId(userId,roleId);
		if(null != userRole){
			return true;
		}else{
			return false;
		}

	}

	@Override
	public Map<String,Object> findBds(String partnerId){
		Map<String,Object> map = new HashMap<String,Object>();
		Long tenantId = Long.parseLong(partnerId);
		List<WxTUser> userList = wxTUserMapper.selUserByPartnerId(tenantId);
		for(int i=0;i<userList.size();i++){
			WxTUser user  = userList.get(i);
			WxTRole role = wxRoleServiceImpl.selectRoleByChRoleName(Constants.SERVICE_PARTNER_BD_CODE);
			long roleId = role.getRoleId();
			boolean bool = this.isIncludeRole(user.getUserId(),roleId);
			if(!bool){
				userList.remove(user);
			}
		}
		map.put("data", userList);
		map.put("success", true);
		return map;
	}

	@Override
	public Map<String,Object> getWxTUserInfoByUseridNew(String userId) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		Long id = Long.parseLong(userId);
		WxTUser user = wxTUserMapper.selUserByUseridNew(id);
		List <WxTUserrole> list = wxUserRoleServiceImpl.selectUserRoleByUserId(id);;
		StringBuffer roleIds = new StringBuffer();
		for(int i=0;i<list.size();i++){
			WxTUserrole wtur = list.get(i);
			roleIds.append(wtur.getRoleId());
			if(i<list.size()-1){
				roleIds.append(",");
			}
		}
		map.put("roleIds", roleIds.toString());
		map.put("resultData",user);
		return map;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String,Object> updateWxTUserNew(String userId, String loginName, String userNo, String chName, String cnName,
			String allowLogin, String sex, Date birthday, String address, String email,
			String mobileTel, String fixedTel, Long orgId, String orgName, String postion, Date userIntime,
			String description, Long post, String postionName, String password, String newPassword,String roleIds, 
			String cai, WxTUser user){
		Map<String,Object> map = new HashMap<String,Object>();
		Long id = Long.parseLong(userId);
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("cai", cai);
		paramsMap.put("excludeUserId", id);
		List<WxTUser> list = wxTUserMapper.checkUserExists(paramsMap);
		if(!list.isEmpty()){
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			map.put(Constants.RESULT_ERROR_MSG_KEY, "CAI已存在");
			return map;
		}
//		int count = wxTUserMapper.findUserByLoginNameOrUserNoOrUserId(loginName, userNo, id);
		WxTUser oldUser = wxTUserMapper.selUserByUseridNew(id);
		if(oldUser != null){
			if(user == null) {
				user = new WxTUser();
			}
			user.setUserId(id);
			user.setLoginName(loginName);
			user.setUserNo(userNo);
			user.setChName(chName);
			user.setCnName(cnName);
			user.setAllowLogin(allowLogin);
			user.setSex(sex);
			user.setBirthday(birthday);
			user.setAddress(address);
			user.setEmail(email);
			user.setMobileTel(mobileTel);
			user.setFixedTel(fixedTel);
			user.setOrgId(orgId);
			user.setPost(post);
			user.setUserIntime(userIntime);
			user.setDescription(description);
			user.setPostionName(postionName);
			user.setPostion(postion);
			user.setCai(cai);
//			user.setTenantId(tenantId);
			if(newPassword != null){
				String newEncryptPassword = toPasswd(newPassword, Encodes.decodeHex(oldUser.getSalt()));
				user.setPassword(newEncryptPassword);
				user.setPwdLasttime(new Date());
			}

			user.setXgTime(new Date());
			user.setXgUser(ContextUtil.getCurUserId() + "");

			try {
				wxTUserMapper.updateByPrimaryKeySelective(user);

				List<WxTUserrole> userroleList  = getRoleList(user.getUserId(),roleIds);
				if(userroleList.size()>0 && null != user.getUserId()){
					wxUserRoleServiceImpl.delUserRoleByUserId(user.getUserId());
					wxUserRoleServiceImpl.batchInsert(userroleList);
				}
				if("T".equals(user.getAllowLogin())) {
					//密码修改后，密码错误状态重置
					wxTLoginErrorLogMapper.resetStatus(user.getUserId());
				}
				map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
				materialPlatformBizService.notifyUserUpdate(user.getUserId(), MaterialPlatformUpdateType.UPDATE);
			} catch (Exception e) {
				//手动触发回滚
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				e.printStackTrace();
				map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
				map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
				log.error(e.getMessage(), e);
			}

		}else {
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			map.put(Constants.RESULT_ERROR_MSG_KEY, "user is not exsit!");
		}
		return map;
	}
	/**
	 *
	 * @param roleIds
	 * @return
	 */
	private List<WxTUserrole> getRoleList(Long userId,String roleIds) {
		List<WxTUserrole> list = new ArrayList<WxTUserrole>();
		if(null!=roleIds && !"".equals(roleIds)){
			String[] idArr = roleIds.split(",");
			for(int i =0;i<idArr.length;i++){
				WxTUserrole wtu = new WxTUserrole();
				wtu.setRoleId(Long.valueOf(idArr[i]));
				wtu.setUserId(userId);
				wtu.setStatus(1);
				wtu.setXgSj(new Date());
				wtu.setGrantUserid(ContextUtil.getCurUserId());
				list.add(wtu);
			}
		}
		return list;
	}

	@Override
	public Map<String, Object> modifyUserStatus(String userId, String status) {
		Map<String, Object> resultMap = new HashMap<String,Object>();
		Map<String, Object> reqMap = new HashMap<String,Object>();
		resultMap.put("code", "success");
		try
		{
			MaterialPlatformUpdateType updateType = MaterialPlatformUpdateType.ENABLE;
			reqMap.put("userId", userId);
			reqMap.put("status", status);
			reqMap.put("xgTime", new Date());
			reqMap.put("xgUser", ContextUtil.getCurUserId());
			if ("0".equals(status)) {
				updateType = MaterialPlatformUpdateType.DISABLE;
				//替换欲删除执行人为最早经销售老板
				wxTUserMapper.changeCustomerExcuteUserId(Long.parseLong(userId));
			/*	// 假删除用户
				int validateResult = wxTUserMapper.validateUserDelete(Long.parseLong(userId));
				if((validateResult & 1) == 1){
					resultMap.put("code", "error");
					resultMap.put("errorMsg", "该用户是门店执行人，不能删除");
					return resultMap;
				}*/
			}
			wxTUserMapper.modifyUserStatus(reqMap);
			materialPlatformBizService.notifyUserUpdate(Long.parseLong(userId), updateType);

		}catch(Exception ex)
		{
			resultMap.put("code", "syserror");
			log.error("",ex);
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String,Object> createWxTUserNew(String loginName, String userNo, String chName, String cnName,
			String allowLogin, String sex, Date birthday, String address, String email,
			String mobileTel, String fixedTel, Long orgId, String orgName, String postion, Date userIntime,
			String description, Long post, String postionName, String password,Long roleId,String roleIds, 
			String orgType, String cai) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put("cai", cai);
		paramsMap.put("loginName", loginName);
		List<WxTUser> list = wxTUserMapper.checkUserExists(paramsMap);
//		int count = wxTUserMapper.countUserByLoginName(loginName, null) ;//+  wxTUserMapper.findUserByUserNo(userNo, null);
		if(list.isEmpty()){
			WxTUser loginUser = ContextUtil.getCurUser();
			WxTUser user = new WxTUser();
			user.setLoginName(loginName);
			user.setUserNo(userNo);
			user.setChName(chName);
			user.setCnName(cnName);
			user.setAllowLogin(allowLogin);
			user.setSex(sex);
			user.setBirthday(birthday);
			user.setAddress(address);
			user.setEmail(email);
			user.setMobileTel(mobileTel);
			user.setFixedTel(fixedTel);
			user.setOrgId(orgId);
			user.setPost(post);
			user.setUserIntime(userIntime);
			user.setDescription(description);
			user.setPostionName(postionName);
			user.setPostion(postion);
			//salt暂时用hardcode
			String salt = SaltUtil.getSalt();
			user.setSalt(salt);
			user.setCai(cai);
			if(password == null){
				user.setResetFlag(1);
			}else{
				// Hex加密密码，根据获取的user的salt值
				String encryptPwd = userBizService.toPasswd(password, user.getSalt());
				// 后台验证密码合法性
				JSONArray rules = JSONArray.fromObject(userBizService.getPwdPolicyConfig().get("rules"));
				if(!userBizService.checkPwdPolicy(user.getUserId(), password, encryptPwd, rules, UserBizService.PWD_FUN_FLAG_LOGIN, true)) {
					map.put("rules", rules);
					map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
					map.put(Constants.RESULT_ERROR_MSG_KEY, "密码不符合规则");
					return map;
				}
//				byte[] hashPassword = Digests.sha1(password.getBytes(),	Encodes.decodeHex(salt), Constants.HASH_INTERATIONS);
//				String pwd = Encodes.encodeHex(hashPassword);
				//String hashPassword = SaltUtil.getHash(password, salt);
				user.setPassword(encryptPwd);
				user.setPwdLasttime(new Date());
			}
			user.setType(orgType);

			user.setIsValid("1");
			user.setStatus(1);

			user.setXgTime(new Date());
			user.setXgUser(ContextUtil.getCurUserId()+"");
			user.setXzTime(new Date());
			user.setXzUser(ContextUtil.getCurUserId()+"");
			try {
				wxTUserMapper.insertSelective(user);
				if(null!=roleIds && !"".equals(roleIds.trim())){
					List<WxTUserrole> userroleList = new ArrayList<WxTUserrole>();
					String[] idsArr  = roleIds.split(",");
					for(int i =0;i<idsArr.length;i++){
						long id  = user.getUserId();
						WxTUserrole record = new WxTUserrole();
						record.setUserId(id);
						record.setRoleId(Long.valueOf(idsArr[i]));
						record.setGrantUserid(id);
						record.setStatus(1);
						record.setXgSj(new Date());
						userroleList.add(record);
					}
					if(userroleList.size() > 0){
						wxUserRoleServiceImpl.batchInsert(userroleList);
					}
				}
				//SP用户创建SP用户时，初始化相同的负责区域
				if(WxTUser.USER_MODEL_SP.equals(loginUser.getUserModel()) && !"1".equals(orgType)){
					userChargeRegionMapper.insertSame(user.getUserId(), loginUser.getUserId());
				}
				//同步用户更新
				materialPlatformBizService.notifyUserUpdate(user.getUserId(), MaterialPlatformUpdateType.ADD);
				// 发送经销商开客礼包
				if (null != orgId) {
					partnerGiftPackageService.createPartnerGiftPackage(orgId);
				}
				map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
			} catch (Exception e) {
				//手动触发回滚
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				e.printStackTrace();
				map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
				map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
				log.error(e.getMessage(), e);
			}
		}else{
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			if(list.get(0).getLoginName().equalsIgnoreCase(loginName)){
				map.put(Constants.RESULT_ERROR_MSG_KEY, "登录名已存在");
			}else{
				map.put(Constants.RESULT_ERROR_MSG_KEY, "CAI已存在");
			}
		}
		return map;
	}
	@Override
	public Map<String,Object> removeWxTUserNew(String userId){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("resultData", "success");
		try
		{
			wxTUserMapper.deleteByPrimaryKey(Long.parseLong(userId));
		}catch(Exception ex)
		{
			map.put("resultData", "fail");
			log.error("",ex);
		}

		return map;
	}

	@Override
	public List<WxTUser> findUserByOrganizationId(String organizationId, String type){
		long orgId = Long.parseLong(organizationId);
		WxTUserExample example = new WxTUserExample();
		com.sys.auth.model.WxTUserExample.Criteria criteria = example.createCriteria();
		criteria.andOrgIdEqualTo(orgId);
		if(type != null && !"".equals(type)){
			criteria.andTypeEqualTo(type);
		}
		List<WxTUser> userList = wxTUserMapper.selectByExample(example );
		return userList;
	}

	@Override
	public List<WxTUser> findUserByOrgIdAndRoleName(String organizationId,String chRoleName) {
		long orgId = Long.parseLong(organizationId);
		WxTUserExample example = new WxTUserExample();
		com.sys.auth.model.WxTUserExample.Criteria criteria = example.createCriteria();
		criteria.andOrgIdEqualTo(orgId);
		List<WxTUser> userList = wxTUserMapper.selectByExample(example );
		for(int i=0;i<userList.size();i++){
			WxTUser user  = userList.get(i);
			WxTRole role = wxRoleServiceImpl.selectRoleByChRoleName(chRoleName);
			long roleId = role.getRoleId();
			boolean bool = this.isIncludeRole(user.getUserId(),roleId);
			if(!bool){
				userList.remove(user);
			}
		}
		return userList;
	}

	@Override
	public List<WxTUser> findUserByLoginName(String loginName) {
		WxTUserExample example = new WxTUserExample();
		Criteria criteria = example.createCriteria();
		criteria.andLoginNameEqualTo(loginName).andStatusNotEqualTo(0);
		return wxTUserMapper.selectByExample(example);
	}
	
	@Override
	public WxTUser getChevronUserInfo(Map<String, Object> params) {
		return wxTUserMapper.getChevronUserInfo(params);
	}

	@Override
	public WxTUser getChevronUser(String loginName) {
		//Map<String, Object> map = new HashMap<String, Object>();
		WxTUser user = null;
		List<WxTUser> list = findUserByLoginName(loginName);
		if (CollUtil.isNotEmpty(list)) {
			user = list.get(0);
			if (user != null) {
				Map<String, Object> params = new HashMap<String, Object>();
				params.put("cai", user.getCai());
				WxTUser chevronUser = wxTUserMapper.getChevronUserInfo(params);
				if (chevronUser != null) {
					user.setUserModel(chevronUser.getUserModel());
					user.setSalesChannel(chevronUser.getSalesChannel());
					user.setBu(chevronUser.getBu());
				}
			}
		}
		//map.put("result", "success");
		//map.put("user", user);
		return user;
	}

	@Override
	public Map<String, Object> createUser(WxTUser wtu) {
		Map<String,Object> map = new HashMap<String,Object>();
		wxTUserMapper.insertSelective(wtu);
		map.put("result", "success");
		map.put("userid", wtu.getUserId());
		return map;
	}

	@Override
	public Map<String, Object> findWorkshopManagers(Long workshopId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", wxTUserMapper.selWorkshopManagers(workshopId, Constants.WORKSHOP_MANAGER_CODE));
			map.put("success", true);
		} catch (Exception e) {
			log.error("",e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public List<WxTUser> getUsersByPartnerIdsForWorkshopUpdate(
			Map<String, Object> reqMap) {
		return wxTUserMapper.getUsersByPartnerIdsForWorkshopUpdate(reqMap);
	}

	@Override
	public List<WxTUser> getUsersByOrgList(Map<String, Object> reqMap) {
		return wxTUserMapper.getUsersByOrgList(reqMap);
	}

	@Override
	public int batchInsertUser(List<WxTUser> list) {
		return wxTUserMapper.insertByBatch(list);
	}

	@Override
	public List<WxTUser> getMerchainUserByMobiles(List<String> list) {
		return wxTUserMapper.selectMechainUser(list);
	}

}