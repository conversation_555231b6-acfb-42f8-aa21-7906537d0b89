INSERT [dbo].[wx_t_report_view] ([package_name], [view_name], [view_sql]) VALUES (N'nonLocalMkt', N'QueryProductByTrackNo', N'select top 1 CONVERT(varchar(100), p.product_date, 23) product_date, CONVERT(varchar(100), dp.scantime, 23) delivery_time, p1.name product_name, p.product_id sku,
p.product_batch, d.order_id do from wx_t_oem_product_packaging_code pc 
left join wx_t_oem_product_packaging p on p.id=pc.packaging_id
left join wx_t_oem_delivery_product dp on (dp.codelevel=1 and dp.code = pc.code1) or (dp.codelevel=2 and dp.code=pc.code2)
left join wx_t_oem_delivery d on d.id=dp.delivery_id
left join wx_t_product p1 on p1.sku=p.product_id
where pc.code1 = #code# or pc.code2=#code#
order by dp.scantime desc')

SET   IDENTITY_INSERT wx_t_user on;
insert into wx_t_user (user_id, login_name,ch_name,status,user_no)
values (-1, '', 'auto',0,'')
insert into wx_t_user (user_id, login_name,ch_name,status,user_no)
values (-2, '', '用户扫码验真',0,'')
insert into wx_t_user (user_id, login_name,ch_name,status,user_no)
values (-3, '', '技师核销扫码',0,'')
SET   IDENTITY_INSERT wx_t_user off;

INSERT INTO wx_t_properties (codetype, code, codename) VALUES ('MainLocalMarketing.reportName.type2', '用户扫码验真', '非本地营销用户验真上报人');

INSERT [dbo].[wx_t_workflow] ([workflow_code], [workflow_name], [handle_bean], [page_themes], [workflow_version], [end_notify], [end_notify_template], [end_email_cc], [def_todo_email_temp], [def_reject_notify_template], [sms_notify_template], [def_sms_notify_temp], [flow_status], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'NON_LOCK_MKT', N'非本地营销上报', N'', N'', 20201020, 3, N'', N'', N'localmkt/local_mkt_approve_mail.ftl', N'localmkt/local_mkt_approve_mail.ftl', N'您有待处理的本地营销上报，请登录雪佛龙合伙人平台查看。', N'您有待处理的本地营销上报，请登录雪佛龙合伙人平台查看。', 0, 0, 1, CAST(N'2020-10-21T14:57:52.987' AS DateTime), NULL, NULL)
INSERT [dbo].[wx_t_workflow_step] ([workflow_code], [step_code], [step_name], [step_icon], [list_operate_name], [accept_alias], [operation_permission_weight], [biz_permission_weight], [reject_to_step], [reject_notify_type], [reject_email_cc], [reject_notify_template], [todo_notify_type], [todo_email_cc], [todo_notify_template], [pre_recall_notify_type], [pre_recall_email_cc], [pre_recall_email_temp], [finish_rate_type], [finish_rate], [ext_property1], [ext_property2], [ext_property3], [ext_property4], [ext_property5], [ext_property6], [ext_property7], [ext_property8], [ext_property9], [sort_numb], [reject_sms_notify_template], [todo_sms_notify_template], [pre_recall_sms_temp], [remark], [predefined_flag], [delete_flag], [create_user_id], [create_time], [history_step_name]) VALUES (N'NON_LOCK_MKT', N'FROM_ASM_SUBMIT_SURVEY', N'提交调查结果', NULL, N'', N'提交', 1, 3, N'', 0, N'', NULL, 3, N'', NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(3.0000000000 AS Decimal(20, 10)), NULL, NULL, NULL, N'', 0, 0, 1, CAST(N'2020-10-21T14:59:54.283' AS DateTime), NULL)
INSERT [dbo].[wx_t_workflow_step] ([workflow_code], [step_code], [step_name], [step_icon], [list_operate_name], [accept_alias], [operation_permission_weight], [biz_permission_weight], [reject_to_step], [reject_notify_type], [reject_email_cc], [reject_notify_template], [todo_notify_type], [todo_email_cc], [todo_notify_template], [pre_recall_notify_type], [pre_recall_email_cc], [pre_recall_email_temp], [finish_rate_type], [finish_rate], [ext_property1], [ext_property2], [ext_property3], [ext_property4], [ext_property5], [ext_property6], [ext_property7], [ext_property8], [ext_property9], [sort_numb], [reject_sms_notify_template], [todo_sms_notify_template], [pre_recall_sms_temp], [remark], [predefined_flag], [delete_flag], [create_user_id], [create_time], [history_step_name]) VALUES (N'NON_LOCK_MKT', N'TO_ASM_SUBMIT_FEEDBACK', N'提交反馈', NULL, N'', N'提交', 3, 5, N'', 0, N'', NULL, 3, N'', NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(4.0000000000 AS Decimal(20, 10)), NULL, NULL, NULL, N'', 0, 0, 1, CAST(N'2020-10-21T14:59:54.300' AS DateTime), NULL)
INSERT [dbo].[wx_t_workflow_step] ([workflow_code], [step_code], [step_name], [step_icon], [list_operate_name], [accept_alias], [operation_permission_weight], [biz_permission_weight], [reject_to_step], [reject_notify_type], [reject_email_cc], [reject_notify_template], [todo_notify_type], [todo_email_cc], [todo_notify_template], [pre_recall_notify_type], [pre_recall_email_cc], [pre_recall_email_temp], [finish_rate_type], [finish_rate], [ext_property1], [ext_property2], [ext_property3], [ext_property4], [ext_property5], [ext_property6], [ext_property7], [ext_property8], [ext_property9], [sort_numb], [reject_sms_notify_template], [todo_sms_notify_template], [pre_recall_sms_temp], [remark], [predefined_flag], [delete_flag], [create_user_id], [create_time], [history_step_name]) VALUES (N'NON_LOCK_MKT', N'FROM_ASM_RESPONSE', N'接受/仲裁', NULL, N'', N'提交', 3, 8, N'TO_ASM_SUBMIT_FEEDBACK', 3, N'', NULL, 3, N'', NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(5.0000000000 AS Decimal(20, 10)), N'您有待处理的本地营销上报，请登录雪佛龙合伙人平台查看。', NULL, NULL, N'', 0, 0, 1, CAST(N'2020-10-21T14:59:54.300' AS DateTime), NULL)
INSERT [dbo].[wx_t_workflow_step] ([workflow_code], [step_code], [step_name], [step_icon], [list_operate_name], [accept_alias], [operation_permission_weight], [biz_permission_weight], [reject_to_step], [reject_notify_type], [reject_email_cc], [reject_notify_template], [todo_notify_type], [todo_email_cc], [todo_notify_template], [pre_recall_notify_type], [pre_recall_email_cc], [pre_recall_email_temp], [finish_rate_type], [finish_rate], [ext_property1], [ext_property2], [ext_property3], [ext_property4], [ext_property5], [ext_property6], [ext_property7], [ext_property8], [ext_property9], [sort_numb], [reject_sms_notify_template], [todo_sms_notify_template], [pre_recall_sms_temp], [remark], [predefined_flag], [delete_flag], [create_user_id], [create_time], [history_step_name]) VALUES (N'NON_LOCK_MKT', N'ABM_CONFIRM', N'ABM确认', NULL, N'', N'确认', 3, 17, N'', 0, N'', NULL, 3, N'', NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(6.0000000000 AS Decimal(20, 10)), NULL, NULL, NULL, N'', 0, 0, 1, CAST(N'2020-10-21T14:59:54.317' AS DateTime), NULL)
INSERT [dbo].[wx_t_workflow_step] ([workflow_code], [step_code], [step_name], [step_icon], [list_operate_name], [accept_alias], [operation_permission_weight], [biz_permission_weight], [reject_to_step], [reject_notify_type], [reject_email_cc], [reject_notify_template], [todo_notify_type], [todo_email_cc], [todo_notify_template], [pre_recall_notify_type], [pre_recall_email_cc], [pre_recall_email_temp], [finish_rate_type], [finish_rate], [ext_property1], [ext_property2], [ext_property3], [ext_property4], [ext_property5], [ext_property6], [ext_property7], [ext_property8], [ext_property9], [sort_numb], [reject_sms_notify_template], [todo_sms_notify_template], [pre_recall_sms_temp], [remark], [predefined_flag], [delete_flag], [create_user_id], [create_time], [history_step_name]) VALUES (N'NON_LOCK_MKT', N'END', N'结束', NULL, N'', N'', 0, 0, N'', 0, NULL, NULL, 0, N'', NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(22.0000000000 AS Decimal(20, 10)), NULL, NULL, NULL, N'', 1, 0, 1, CAST(N'2020-10-21T14:59:54.317' AS DateTime), NULL)
INSERT [dbo].[wx_t_workflow_step] ([workflow_code], [step_code], [step_name], [step_icon], [list_operate_name], [accept_alias], [operation_permission_weight], [biz_permission_weight], [reject_to_step], [reject_notify_type], [reject_email_cc], [reject_notify_template], [todo_notify_type], [todo_email_cc], [todo_notify_template], [pre_recall_notify_type], [pre_recall_email_cc], [pre_recall_email_temp], [finish_rate_type], [finish_rate], [ext_property1], [ext_property2], [ext_property3], [ext_property4], [ext_property5], [ext_property6], [ext_property7], [ext_property8], [ext_property9], [sort_numb], [reject_sms_notify_template], [todo_sms_notify_template], [pre_recall_sms_temp], [remark], [predefined_flag], [delete_flag], [create_user_id], [create_time], [history_step_name]) VALUES (N'NON_LOCK_MKT', N'GQ_OEM_HANDLER', N'GQ/OEM审批', NULL, N'', N'确认', 3, 17, N'', 3, NULL, NULL, 3, N'', NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(6.0000000000 AS Decimal(20, 10)), NULL, NULL, NULL, N'', 0, 0, 1, CAST(N'2020-11-24T14:08:01.180' AS DateTime), NULL)

insert into wx_t_workflow_exe_list (source_type, source_key, step_id, remark) values ('GROUP',(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责ASM'),(select step_id from wx_t_workflow_step where step_code='FROM_ASM_SUBMIT_SURVEY' and workflow_code='NON_LOCK_MKT' and delete_flag=0),'经销商负责ASM');
insert into wx_t_workflow_exe_list (source_type, source_key, step_id, remark) values ('GROUP',(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责ASM'),(select step_id from wx_t_workflow_step where step_code='TO_ASM_SUBMIT_FEEDBACK' and workflow_code='NON_LOCK_MKT' and delete_flag=0),'经销商负责ASM');
insert into wx_t_workflow_exe_list (source_type, source_key, step_id, remark) values ('GROUP',(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责ASM'),(select step_id from wx_t_workflow_step where step_code='FROM_ASM_RESPONSE' and workflow_code='NON_LOCK_MKT' and delete_flag=0),'经销商负责ASM');
insert into wx_t_workflow_exe_list (source_type, source_key, step_id, remark) values ('USER',(select user_id from wx_t_user where status=1 and login_name='cyana'),(select step_id from wx_t_workflow_step where step_code='ABM_CONFIRM' and workflow_code='NON_LOCK_MKT' and delete_flag=0),'cyana');

CREATE INDEX ix_oem_delivery_product_code ON wx_t_oem_delivery_product (code);