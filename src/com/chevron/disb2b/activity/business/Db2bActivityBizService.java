package com.chevron.disb2b.activity.business;

import com.chevron.disb2b.activity.model.*;
import com.chevron.disb2b.model.Db2bShoppingCar;
import com.common.exception.WxPltException;

import java.util.List;
import java.util.Map;

/**
 * 活动配置业务接口
 *
 * <AUTHOR>
 * @version 1.0 2020-11-20 10:29
 */
public interface Db2bActivityBizService {

    /**
     * 保存活动配置
     *
     * @param record 被插入活动配置
     * @throws WxPltException
     */
    public Long insert(Db2bActivity record) throws WxPltException;

    /**
     * 修改活动配置
     *
     * @param record 被修改活动配置
     * @throws WxPltException
     */
    public void update(Db2bActivity record) throws WxPltException;

    /**
     * 删除活动配置
     *
     * @param ids 被删除活动配置id集合
     * @throws WxPltException
     */
    public void delete(List<Long> ids) throws WxPltException;

    /**
     * 删除满足条件的活动配置
     *
     * @param example 被删除活动配置满足条件
     * @throws WxPltException
     */
    public void deleteByExample(Db2bActivityExample example) throws WxPltException;

    /**
     * 列表查询
     *
     * @param example 查询条件
     * @throws WxPltException
     */
    public List<Db2bActivity> queryByExample(Db2bActivityExample example) throws WxPltException;

    /**
     * 列表查询活动配置对象
     *
     * @param params 查询条件
     * @return 满足条件活动配置对象集合
     */
    public List<Db2bActivity> queryByParams(Map<String, Object> params) throws WxPltException;

    /**
     * 修改页面修改活动配置
     *
     * @param record 被修改活动配置
     * @throws WxPltException
     */
    public Long updateForEditPage(Db2bActivity record) throws WxPltException;

    /**
     * 获取指定主键的活动配置对象
     *
     * @param id 主键值
     * @throws WxPltException
     */
    public Db2bActivity getBean(Long id) throws WxPltException;

    /**
     * 分页查询活动配置
     *
     * @param params    查询参数
     * @param resultMap 结果对象
     * @throws WxPltException
     */
    public void queryForPage(Db2bActivityParams params, Map<String, Object> resultMap) throws WxPltException;

    /**
     * 分页查询活动配置
     *
     * @param params 查询参数
     * @return 查询结果
     * @throws WxPltException
     */
    public List<Db2bActivity> queryForPage(Db2bActivityParams params) throws WxPltException;


    List<Db2bPromotion> queryByMap(Map<String, Object> map);

    /**
     * 通过购物车查询商品参加的促销列表
     *
     * @param db2bShoppingCar 购物车对象
     * @return 促销列表
     * @throws WxPltException
     */
    OrderPromotionResult getPromotionsByCar(Db2bShoppingCar db2bShoppingCar,Map<String, Object> params ) throws WxPltException;

    OrderPromotionResult getPromotionsByProductId(Map<String, Object> params);

    /**
     * 根据产品ID集合查询产品参与的活动
     * @param params
     * @return
     */
    List<OrderPromotionResult> getResultByProductIDS(Map<String, Object> params);

    List<Db2bPromotion> getAllFulls(Long workShopId);

    /**
     * 查询赠品列表
     *
     * @param promotionId 促销ID
     * @param quantity    数量
     * @return 赠品列表和数量
     */
    Map<String, Object> getGiftList(Long promotionId, int quantity);
}
