package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.pms.dao.WorkshopPartnerVoMapper;
import com.chevron.pms.model.WorkshopPartnerVo;
import com.chevron.pms.model.WorkshopPartnerVoExample;
import com.chevron.pms.model.WorkshopPartnerVoExample.Criteria;
import com.chevron.pms.service.WorkshopPartnerService;
import com.sys.organization.service.OrganizationService;
@Service
public class WorkshopPartnerServiceImpl implements WorkshopPartnerService{

	@Resource
	WorkshopPartnerVoMapper workshopPartnerVoMapper;
	
	@Resource
	OrganizationService organizationServiceImpl;
	
	@Override
	public List<WorkshopPartnerVo> getWorkShopPartnerByPartnerId(long partnerId) {
		List<WorkshopPartnerVo> workshopPartnerList = new ArrayList<WorkshopPartnerVo>();
		WorkshopPartnerVoExample example = new WorkshopPartnerVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andPartnerIdEqualTo(partnerId);
		List<WorkshopPartnerVo> list = workshopPartnerVoMapper.selectByExample(example);
		if(null!=list && list.size()>0){
			workshopPartnerList.addAll(list);
		}
		return workshopPartnerList;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> batchInsertWorkShopPartner(List<WorkshopPartnerVo> wpvList,boolean isInsertOrg) {
		
		Map<String, Object> map = new HashMap<String, Object>();
        if(null!=wpvList && wpvList.size()>0){
        	try {
				 workshopPartnerVoMapper.batchInsert(wpvList);
				 //delete by bo.liu 1123 不需要录入到wx_t_organization中去
				/*if(isInsertOrg)
				 {
					 organizationServiceImpl.createOrganizationByWorkShopPartnerVo(wpvList);
				 }*/
				 map.put("result", "success");
			} catch (Exception e) {
			   //手动触发回滚
			   TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			   map.put("result", "fail");
			   e.printStackTrace();
			}
        }else{
        	map.put("result", "fail");
        	map.put("resultMsg", "params vo length is 0");
        }
		return map;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertWorkShopPartner(WorkshopPartnerVo wpv) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		List<WorkshopPartnerVo> list  = new ArrayList<WorkshopPartnerVo>();
		list.add(wpv);
        if(null!=wpv ){
        	try {
				workshopPartnerVoMapper.insert(wpv);
				//delete by bo.liu 1123 不需要录入到wx_t_organization中去
				//organizationServiceImpl.createOrganizationByWorkShopPartnerVo(list);
				map.put("result", "success");
			} catch (Exception e) {
				//手动触发回滚
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
				e.printStackTrace();
				map.put("result", "fail");
			}
        }else{
        	map.put("result", "fail");
        	map.put("resultMsg", "params vo is null");
        }
		return map;
	}

	@Override
	/**
	 * 根据workshopId 查找所有的关联partner
	 * return  WorkshopPartnerVo List
	 */
	public List<WorkshopPartnerVo> getSelectedPartnerByWorkshopId(long workshopId) {
		List<WorkshopPartnerVo> workshopPartnerList = new ArrayList<WorkshopPartnerVo>();
		WorkshopPartnerVoExample example = new WorkshopPartnerVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andWorkshopIdEqualTo(workshopId);
		workshopPartnerList.addAll(workshopPartnerVoMapper.selectByExample(example));
		return workshopPartnerList;
	}

	@Override
	/**
	 * 根据workshopId 删除关联的partner
	 * return 删除是否成功标识 int
	 */
	public int deleteWorkshopPartnerVoByWorkshopId(Long workshopId) {
		Map<String,Object> requestMap = new HashMap<String,Object>();
		requestMap.put("workshopId", workshopId);
		int result = workshopPartnerVoMapper.deleteWorkshopPartnerVoByWorkshopId(requestMap);
		return result;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public int insertSelective(WorkshopPartnerVo wpv) {
		try {
			workshopPartnerVoMapper.insertSelective(wpv);
			//delete by bo.liu 1123 不需要插入到wx_t_organization中去
//			List<WorkshopPartnerVo>  list = new ArrayList<WorkshopPartnerVo>();
//			list.add(wpv);
//			organizationServiceImpl.createOrganizationByWorkShopPartnerVo(list);
			return 1;
		} catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			return 0;
		}
		
	}

	@Override
	public List<WorkshopPartnerVo> getWorkShopPartnerByPartnerIdAndWorkshopId(Long partnerId,
			Long workshopId) {
		WorkshopPartnerVoExample example = new WorkshopPartnerVoExample();
		Criteria criteria   = example.createCriteria();
		criteria.andPartnerIdEqualTo(partnerId);
		criteria.andWorkshopIdEqualTo(workshopId);
		List<WorkshopPartnerVo> workshopPartnerList = workshopPartnerVoMapper.selectByExample(example );
		return workshopPartnerList;
	}
	
	@Override
	public Long getTradePartnerIdByWorkshopId(Long workshopId) {
		WorkshopPartnerVoExample example = new WorkshopPartnerVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andWorkshopIdEqualTo(workshopId);
		criteria.andRelationTypeEqualTo("trade");
		List<WorkshopPartnerVo> workshopPartnerList = workshopPartnerVoMapper.selectByExample(example);
		if (workshopPartnerList != null && !workshopPartnerList.isEmpty()){
			return workshopPartnerList.get(0).getPartnerId();
		}
		return null;
	}
	
}
