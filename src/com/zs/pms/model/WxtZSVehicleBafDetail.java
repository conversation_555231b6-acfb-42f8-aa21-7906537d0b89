package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleBafDetail {
    private Long id;

    private String tModelId;

    private String bafName;

    private String bafNo;

    private String bafLevel;

    private String bafConsumption;

    private String bafFreezepoint;

    private String bafColor;

    private String bafPurchasePrice;

    private String bafSalePrice;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getBafName() {
        return bafName;
    }

    public void setBafName(String bafName) {
        this.bafName = bafName == null ? null : bafName.trim();
    }

    public String getBafNo() {
        return bafNo;
    }

    public void setBafNo(String bafNo) {
        this.bafNo = bafNo == null ? null : bafNo.trim();
    }

    public String getBafLevel() {
        return bafLevel;
    }

    public void setBafLevel(String bafLevel) {
        this.bafLevel = bafLevel == null ? null : bafLevel.trim();
    }

    public String getBafConsumption() {
        return bafConsumption;
    }

    public void setBafConsumption(String bafConsumption) {
        this.bafConsumption = bafConsumption == null ? null : bafConsumption.trim();
    }

    public String getBafFreezepoint() {
        return bafFreezepoint;
    }

    public void setBafFreezepoint(String bafFreezepoint) {
        this.bafFreezepoint = bafFreezepoint == null ? null : bafFreezepoint.trim();
    }

    public String getBafColor() {
        return bafColor;
    }

    public void setBafColor(String bafColor) {
        this.bafColor = bafColor == null ? null : bafColor.trim();
    }

    public String getBafPurchasePrice() {
        return bafPurchasePrice;
    }

    public void setBafPurchasePrice(String bafPurchasePrice) {
        this.bafPurchasePrice = bafPurchasePrice == null ? null : bafPurchasePrice.trim();
    }

    public String getBafSalePrice() {
        return bafSalePrice;
    }

    public void setBafSalePrice(String bafSalePrice) {
        this.bafSalePrice = bafSalePrice == null ? null : bafSalePrice.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}