/*老店招流程*/ SELECT v.partner_id distributor_id, v.channel, CASE WHEN v.mkt_type = 'STORE_IN_STORE' THEN 'STORE_FRONT' ELSE v.mkt_type END mkt_type, v.price, v.create_time request_time, 
                  CASE WHEN v.create_time < '2020-01-01' THEN 0 WHEN st.spark_budget > 0 THEN v.price ELSE 0 END spark_amount, CASE WHEN v.create_time < '2020-01-01' THEN 0 WHEN st.spark_budget > 0 THEN 0 ELSE v.price END online_amount, 
                  isnull(v.flow_finish3, 0) end_flag
FROM     view_mkt_apply v LEFT JOIN
                      (SELECT isnull(SUM(t .q1), 0) + isnull(SUM(t .q2), 0) + isnull(SUM(t .q3), 0) + isnull(SUM(t .q4), 0) spark_budget, t .distributor_id, t .sales_channel
                       FROM      wx_t_mkt_dealer_quarter t
                       GROUP BY t .distributor_id, t .sales_channel) st ON st.distributor_id = v.partner_id AND st.sales_channel = (CASE WHEN v.channel = 'C&I' THEN 'Commercial' ELSE 'Consumer' END)
WHERE  v.price > 0
/*线下数据*/ UNION ALL
SELECT pe.distributor_id, CASE WHEN w.from_source = 1 THEN 'CDM' ELSE 'C&I' END channel, 'STORE_FRONT' mkt_type, w.recruitment_amount, CASE WHEN w.shop_recruitment_update_time IS NULL OR
                  w.shop_recruitment_update_time >= '2020-01-01' THEN '2019-04-30' ELSE w.shop_recruitment_update_time END request_time, 0 spark_amount, 0 online_amount, 1 end_flag
FROM     wx_t_work_shop w LEFT JOIN
                  wx_t_workshop_partner wp ON w.id = wp.workshop_id LEFT JOIN
                  wx_t_partner_o2o_enterprise pe ON pe.partner_id = wp.partner_id
WHERE  w.recruitment_amount > 0
/*新金富力店招流程*/ UNION ALL
SELECT ca.distributor_id, 'CDM' channel, ca.sign_type, 
CASE WHEN ca.form_status >= 15 and ca.sign_type='STORE_FRONT' THEN ca.settlement_amount ELSE ca.quote_amount END price, ca.req_time, 
                  CASE WHEN ca.form_status >= 15 and ca.sign_type='STORE_FRONT' THEN ca.settlement_spark_amount ELSE ca.quote_spark_amount END spark_amount, 
                  CASE WHEN ca.form_status >= 15 and ca.sign_type='STORE_FRONT' THEN ca.settlement_online_amount ELSE ca.quote_online_amount END online_amount, CASE WHEN ca.form_status >= 45 THEN 1 ELSE 0 END end_flag
FROM     wx_t_mkt_cdm_apply ca LEFT JOIN
                      (SELECT isnull(SUM(t .q1), 0) + isnull(SUM(t .q2), 0) + isnull(SUM(t .q3), 0) + isnull(SUM(t .q4), 0) spark_budget, t .distributor_id, t .sales_channel
                       FROM      wx_t_mkt_dealer_quarter t
                       GROUP BY t .distributor_id, t .sales_channel) st ON st.distributor_id = ca.distributor_id AND st.sales_channel = 'Consumer'
WHERE  ca.form_status >= 10
/*新德乐店招流程*/ UNION ALL
SELECT ca.distributor_id, 'C&I' channel, 'STORE_FRONT' mkt_type, CASE WHEN ca.form_status >= 50 THEN ca.settlement_amount ELSE ca.budget_amount END price, ca.req_time, 
                  CASE WHEN ca.form_status >= 50 THEN ca.settlement_spark_amount ELSE ca.quote_spark_amount END spark_amount, 
                  CASE WHEN ca.form_status >= 50 THEN ca.settlement_online_amount ELSE ca.quote_online_amount END online_amount, CASE WHEN wi.status = 20 THEN 1 ELSE 0 END end_flag
FROM     wx_t_mkt_ci_apply ca LEFT JOIN
                  wx_t_workflow_instance wi ON ca.id = wi.form_key AND wi.workflow_code = 'CDM_APPLY'
WHERE  ca.form_status >= 10