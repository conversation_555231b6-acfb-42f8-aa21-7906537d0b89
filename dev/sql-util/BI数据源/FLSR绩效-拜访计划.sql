CREATE VIEW [dbo].[view_pp_flsr_visit_report]
AS
(
	SELECT vp.region_name "区域", u.ch_name "销售", CASE 
			WHEN customer_type = '1' THEN (
				SELECT top 1 pp.customer_name_cn
				FROM dw_customer_org_sales pp
				WHERE pp.distributor_id = vp.customer_id
			)
			ELSE (
				SELECT DISTINCT pc.customer_name
				FROM wx_t_potential_customer pc
				WHERE pc.id = vp.customer_id
			)
		END AS "拜访客户", vp.start_time "拜访时间", vp.stay_time "停留时间(天)",vp.leave_time "离开时间", 
		year(vp.leave_time) "离开年", month(leave_time) "离开月",
		(select (
 SELECT vt1.target_title FROM wx_t_customer_visit_target vt1 
where vt1.plan_id=vp.id  FOR XML PATH,TYPE
 ).value('.','varchar(max)')) "销售拜访目的",
		case when vp.execute_status=0 then '未执行' when vp.execute_status=10 then '未按计划执行' when vp.execute_status=20 then '按计划执行' end "是否按计划完成拜访",
		vp.visit_report "拜访报告", vp.asm_comment_time "大区经理审阅日期", vp.asm_comment "大区经理评语"
	FROM wx_t_visit_plan vp
		LEFT JOIN wx_t_user u ON vp.sales_id = u.user_id
	WHERE 1 = 1
		AND vp.delete_flag = '0'
		AND vp.status IN (10, 20, 25, 28, 29, 30, 35, 40)
		/*0-草稿，3-撤回，10-已提交，20-ASM审批通过，25-ASM驳回 ，28—ASM审批撤回，30-FLSR提交报告，35—FLSR报告提交撤回,40-ASM评语，45—ASM评价撤回*/
)