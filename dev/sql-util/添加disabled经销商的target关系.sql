
insert into dw_access_control_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[customer_category]
      ,[customer_type]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[control_type]
      ,[control_type_name]
      ,[product_channel]
      ,[channel_weight]
      ,[from_source])
select [distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[sales_cai]
      ,[sales_name_cn]
      ,null [customer_category]
      ,[customer_type]
      ,getdate() [create_time]
      ,getdate() [update_time]
      ,[del_flag]
      ,null [control_type]
      ,null [control_type_name]
      ,[product_channel]
	  ,case when [product_channel]='Consumer' 
			then 1 when [product_channel]='Commercial' then 2 else 0 end channel_weight
	  ,'PP' from_source
from PP_MID.dbo.syn_dw_to_pp_customer_org_sales r where --r.customer_name_cn in ('中凯之星（北京）贸易有限公司', '内蒙古拉凯商贸有限公司')
distributor_id in ('1225',
'1227',
'1223',
'1228',
'1229')

select * 
--delete s
from dw_access_control_customer_org_sales s where [distributor_id]=1228 and channel_weight=1