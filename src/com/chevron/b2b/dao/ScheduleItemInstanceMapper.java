package com.chevron.b2b.dao;

import com.chevron.b2b.model.ScheduleItemInstance;
import com.chevron.b2b.model.ScheduleItemInstanceExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 计划事项实例-wx_t_schedule_item_instance数据库操作对象
 * <AUTHOR>
 * @version 1.0 2018-12-18 15:32
 */
public interface ScheduleItemInstanceMapper {

	/**
	 * 根据主键更新计划事项实例对象(非空属性)
	 * @param record 新计划事项实例对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(ScheduleItemInstance record);

    /**
     * 根据主键删除计划事项实例对象
     * @param id 删除计划事项实例对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询计划事项实例对象
	 * @param id 计划事项实例对象主键值
	 * @return 计划事项实例对象结果
	 */
    ScheduleItemInstance selectByPrimaryKey(Long id);

 	/**
	 * 插入计划事项实例对象(非空属性)
	 * @param record 被插入计划事项实例对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(ScheduleItemInstance record);

	/**
	 * 更新计划事项实例对象
	 * @param record 被修改计划事项实例对象
	 * @param example 过滤计划事项实例对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") ScheduleItemInstance record, @Param("example") ScheduleItemInstanceExample example);

	/**
	 * 删除计划事项实例对象
	 * @param example 被删除计划事项实例对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleItemInstanceExample example);
    
 	/**
	 * 查询满足条件的计划事项实例对象数量
	 * @param example 过滤计划事项实例对象条件
	 * @return 满足条件计划事项实例对象数量
	 */
    int countByExample(ScheduleItemInstanceExample example);

	/**
	 * 单表查询计划事项实例对象
	 * @param example 过滤计划事项实例对象条件
	 * @return 满足条件计划事项实例对象集合
	 */
    List<ScheduleItemInstance> selectByExample(ScheduleItemInstanceExample example);

	/**
	 * 列表查询计划事项实例对象
	 * @param params 查询条件
	 * @return 满足条件计划事项实例对象集合
	 */
    List<ScheduleItemInstance> queryByParams(Map<String, Object> params);

    /**
     * 批量插入计划事项实例对象
     * @param records 插入计划事项实例集合
     * @return 数据库操作影响记录条数
     */
	int insertBatch(@Param("records") List<ScheduleItemInstance> records);
	
	int insertBatchWhenPublish(Map<String, Object> params);
	
	int insertBatchByScheduleInstance(@Param("scheduleInstanceId") Long scheduleInstanceId);
}
