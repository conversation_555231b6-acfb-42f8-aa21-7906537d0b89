package com.sys.master.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chevron.disb2b.dao.Db2bProductMapper;
import com.chevron.disb2b.model.Db2bProduct;
import com.chevron.disb2b.model.Db2bProductExample;
import com.chevron.pms.dao.MapBundleProductMapper;
import com.chevron.pms.model.MapBundleProduct;
import com.chevron.pms.model.MapBundleProductExample;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.dealer.dao.DeaSellThrProPerMapper;
import com.sys.dealer.dao.DealerProductPermissionMapper;
import com.sys.dealer.model.DeaSellThrProPer;
import com.sys.dealer.model.DeaSellThrProPerExample;
import com.sys.dealer.model.DealerProductPermission;
import com.sys.dealer.model.DealerProductPermissionExample;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogUtils;
import com.sys.master.business.ProductMasterBizService;
import com.sys.master.constant.ImportCodeEnum;
import com.sys.master.dao.DwProductSalesChannelMapper;
import com.sys.master.dao.DwProductSectorMapper;
import com.sys.master.dao.DwQbrProductChannelMapper;
import com.sys.master.model.ImportProduct;
import com.sys.master.model.ProductMaster;
import com.sys.master.service.DwProductService;
import com.sys.master.service.ProductMasterService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
//import sun.misc.Contended;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2018-06-14 10:42
 */
@Service
public class ProductMasterServiceImpl implements ProductMasterService {
	
	@Autowired
	private ProductMasterBizService productMasterBizService;
	
	@Autowired
	private DwProductService dwProductService;
	
	@Autowired
	private DwProductSalesChannelMapper salesChannelDao;
	
	@Autowired
	private DwProductSectorMapper sectorDao;
	
	@Autowired
	private DwQbrProductChannelMapper qbrProductChannelDao;
	
	@Autowired
	private DicService dicService;

	@Autowired
    private DealerProductPermissionMapper dealerProductPermissionMapper;

	@Autowired
    private DeaSellThrProPerMapper deaSellThrProPerMapper;

	@Autowired
    private MapBundleProductMapper mapBundleProductMapper;

	@Autowired
    private Db2bProductMapper db2bProductMapper;
	
	private final static Logger log = Logger.getLogger(ProductMasterServiceImpl.class);

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> save(ProductMaster record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			String userId = ContextUtil.getCurUserId().toString();
			productMasterBizService.save(record, userId);
            updateDealerProductPermission(record);
            updateSellThroughDealerProductPermission(record);
            updateBundleSubProduct(record);
            inertdb2bProduct(record);
            map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
		}
		return map;
	}

    //先按照sku删除原来的配置 再写入
    private void updateDealerProductPermission(ProductMaster record) {
        DealerProductPermissionExample example = new DealerProductPermissionExample();
        example.createCriteria().andProductSkuEqualTo(record.getSku());
        dealerProductPermissionMapper.deleteByExample(example);
        if(record.getSupportOrder() != null && record.getSupportOrder() == 0){
            return;
        }
        if (CollectionUtil.isNotEmpty(record.getPartnerIds())) {
            ArrayList<DealerProductPermission> dealerProductPermissions = new ArrayList<DealerProductPermission>();
            for (Long partnerId : record.getPartnerIds()) {
                DealerProductPermission item = new DealerProductPermission();
                item.setProductSku(record.getSku());
                item.setDealerId(partnerId);
                dealerProductPermissions.add(item);
            }
            List<List<DealerProductPermission>> split = CollectionUtil.split(dealerProductPermissions, 50);
            for (List<DealerProductPermission> productPermissions : split) {
                dealerProductPermissionMapper.insertBatch(productPermissions);
            }
        }
    }

    //支持sell-through的经销商 先按照sku删除原来的配置 再写入
    private void updateSellThroughDealerProductPermission(ProductMaster record) {
        DeaSellThrProPerExample example = new DeaSellThrProPerExample();
        example.createCriteria().andProductSkuEqualTo(record.getSku());
        deaSellThrProPerMapper.deleteByExample(example);
        if(record.getSupportSellThrough() != null && record.getSupportSellThrough() == 0){
            return;
        }
        if (CollectionUtil.isNotEmpty(record.getSupportSellThroughPartnerIds())) {
            ArrayList<DeaSellThrProPer> dealerProductPermissions = new ArrayList<DeaSellThrProPer>();
            for (Long partnerId : record.getSupportSellThroughPartnerIds()) {
                DeaSellThrProPer item = new DeaSellThrProPer();
                item.setProductSku(record.getSku());
                item.setPartnerId(partnerId);
                dealerProductPermissions.add(item);
            }
            List<List<DeaSellThrProPer>> split = CollectionUtil.split(dealerProductPermissions, 50);
            for (List<DeaSellThrProPer> productPermissions : split) {
                deaSellThrProPerMapper.insertBatch(productPermissions);
            }
        }
    }

    private void inertdb2bProduct(ProductMaster record){
        if(record.getSupportSellThrough() == null || record.getSupportSellThrough() == 0){
            return;
        }
        Db2bProductExample example = new Db2bProductExample();
        example.createCriteria().andSkuEqualTo(record.getSku()).andPartnerIdEqualTo(-1l);
        List<Db2bProduct> db2bProducts = db2bProductMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(db2bProducts)){
            return;
        }
        //为雪佛龙产品表创建一条数据
        Db2bProduct db2bProduct = new Db2bProduct();
        db2bProduct.setSku(record.getSku());
        db2bProduct.setProductName(record.getName());
        db2bProduct.setPartnerId(-1l);
        db2bProduct.setProductType(0);
        db2bProduct.setProductPrice(0d);
        db2bProduct.setStatus(10);
        db2bProduct.setDetailFlag(0);
        db2bProduct.setDeleteFlag(0l);
        db2bProduct.setProductBrand("1");
        db2bProduct.setProductUnit("瓶");
        db2bProduct.setUpdateTime(new Date());
        db2bProduct.setCreateTime(new Date());
        Long userId = ContextUtil.getCurUserId();
        db2bProduct.setCreateUserId(userId);
        db2bProduct.setUpdateUserId(userId);
        db2bProductMapper.insertSelective(db2bProduct);
    }

    /** 更新绑定的子产品 **/
    private void updateBundleSubProduct(ProductMaster record) {
        MapBundleProductExample example = new MapBundleProductExample();
        example.createCriteria().andBskuEqualTo(record.getSku());
        mapBundleProductMapper.deleteByExample(example);
        if (CollectionUtil.isEmpty(record.getMapBundleProducts())) {
            return;
        }
        ArrayList<MapBundleProduct> list = new ArrayList<MapBundleProduct>();
        for (MapBundleProduct mapBundleProduct : record.getMapBundleProducts()) {
            list.add(new MapBundleProduct(record.getSku(), mapBundleProduct.getSku()));
        }
        mapBundleProductMapper.insertBatch(list);
    }

    @Override
	public Map<String, Object> delete(List<Long> ids) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			productMasterBizService.delete(ids);
			for(Long id : ids) {
				String sku = productMasterBizService.getBean(id).getSku();
				dwProductService.delete(sku);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getBean(Long id){
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
            ProductMaster bean = productMasterBizService.getBean(id);
            //获取产品限制的经销商
            bean.setPartnerSelectInfoList(dealerProductPermissionMapper.getDealerProductPermissionBySku(bean.getSku()));
            bean.setSellThroughPartnerSelectInfoList(deaSellThrProPerMapper.getProductPermissionBySku(bean.getSku()));
            bean.setMapBundleProducts(mapBundleProductMapper.getByBSku(bean.getSku()));
            map.put("bean", bean);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("get bean fail. " + e.getMessage(), e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> getDic(String type) {
		Map<String, Object> map = new HashMap<String, Object>();
		if("salesChannel".equals(type)) {
			map.put(Constants.RESULT_DATA_CODE, salesChannelDao.selectAll());
		} else if("sector".equals(type)) {
			map.put(Constants.RESULT_DATA_CODE, sectorDao.selectAll());
		} else if("qbrChannel".equals(type)) {
			map.put(Constants.RESULT_DATA_CODE, qbrProductChannelDao.selectAll());
		} else {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("get dic fail. ");
			return map;
		}
		map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return map;
	}
	
	@Override
	public Map<String, Object> updateImportExcel(List<ProductMaster> importProductList, String userId) {
		//原数据列表
		List<ImportProduct> oldList = new ArrayList<ImportProduct>();
		List<ProductMaster> productList = productMasterBizService.selectAllProduct();
		log.info("导入产品,excel解析完成后，获取原数据列表productList 共"+productList.size()+"条");
		for(ProductMaster product : productList) {
			ImportProduct old = new ImportProduct();
			old.setId(product.getId());
			old.setSku(product.getSku());
			old.setImportCode(ImportCodeEnum.DELETE.getCode());
			oldList.add(old);
		}
		
		//导入数据列表
		List<ImportProduct> importList = new ArrayList<ImportProduct>();
		for(ProductMaster importProduct : importProductList) {
			ImportProduct importData = new ImportProduct();
			importData.setSku(importProduct.getSku());
			importData.setImportCode(ImportCodeEnum.INSERT.getCode());
			importList.add(importData);
		}
		
		//对于非同步数据不需要修改
		log.info(userId+" 导入产品,excel解析完成后，对于非同步数据不需要修改");
		for(ImportProduct old : oldList) {
			//非同步数据不需要操作
			Map<String, Object> resultLst =  dicService.getDicItemByDicTypeCode("product.nosync");
			@SuppressWarnings("unchecked")
			List<DicItemVo> itemLst = (ArrayList<DicItemVo>) resultLst.get("data");
			for(DicItemVo item : itemLst) {
				if(old.getSku().equals(item.getDicItemCode())) {
					old.setImportCode(ImportCodeEnum.NO_CHANGE.getCode());
				}
			}
		}
		
		//寻找修改数据
		log.info(userId+" 导入产品,excel解析完成后，寻找修改数据");
		for(ProductMaster product : productList) {
			for(ProductMaster importProduct : importProductList) {
				if(product.getSku().equals(importProduct.getSku())) {
					log.info(userId+" 导入产品,寻找修改数据start,product_sku=" + product.getSku()+"; importProduct_sku="+ JsonUtil.toJSONString(importProduct)
					+";product=" + JsonUtil.toJSONString(product));
					if((EmptyChecker.isEmpty(product.getCreationDate()) && !EmptyChecker.isEmpty(importProduct.getCreationDate()))
							|| (!EmptyChecker.isEmpty(product.getCreationDate()) && EmptyChecker.isEmpty(importProduct.getCreationDate()))) {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						continue;
					}
					
					if((EmptyChecker.isEmpty(product.getBlockDate()) && !EmptyChecker.isEmpty(importProduct.getBlockDate()))
							|| (!EmptyChecker.isEmpty(product.getBlockDate()) && EmptyChecker.isEmpty(importProduct.getBlockDate()))) {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						continue;
					}
					
					if((EmptyChecker.isEmpty(product.getUnblockDate()) && !EmptyChecker.isEmpty(importProduct.getUnblockDate()))
							|| (!EmptyChecker.isEmpty(product.getUnblockDate()) && EmptyChecker.isEmpty(importProduct.getUnblockDate()))) {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						continue;
					}
					
					if((EmptyChecker.isEmpty(product.getBaseUn()) && !EmptyChecker.isEmpty(importProduct.getBaseUn()))
							|| (!EmptyChecker.isEmpty(product.getBaseUn()) && EmptyChecker.isEmpty(importProduct.getBaseUn()))) {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						continue;
					}
					
					if((EmptyChecker.isEmpty(product.getGradeAbc()) && !EmptyChecker.isEmpty(importProduct.getGradeAbc()))
							|| (!EmptyChecker.isEmpty(product.getGradeAbc()) && EmptyChecker.isEmpty(importProduct.getGradeAbc()))) {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						continue;
					}
					
					if((EmptyChecker.isEmpty(product.getBottleQty()) && !EmptyChecker.isEmpty(importProduct.getBottleQty()))
							|| (!EmptyChecker.isEmpty(product.getBottleQty()) && EmptyChecker.isEmpty(importProduct.getBottleQty()))) {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						continue;
					}
					if(StringUtils.equals(product.getNameEn(),importProduct.getNameEn())
							&& StringUtils.equals(product.getName(),importProduct.getName())
							&& (!EmptyChecker.isEmpty(product.getCreationDate()) && product.getCreationDate().equals(importProduct.getCreationDate()))
							&& StringUtils.equals(product.getMasterStatus(),importProduct.getMasterStatus())
							&& (!EmptyChecker.isEmpty(product.getBlockDate()) && product.getBlockDate().equals(importProduct.getBlockDate()))
							&& (!EmptyChecker.isEmpty(product.getUnblockDate()) && product.getUnblockDate().equals(importProduct.getUnblockDate()))
							&& (!EmptyChecker.isEmpty(product.getBaseUn()) && product.getBaseUn().equals(importProduct.getBaseUn()))
							&& product.getPack().equals(importProduct.getPack())
							&& StringUtils.equals(product.getPackType(),importProduct.getPackType())
							&& (!EmptyChecker.isEmpty(product.getBaseUn()) && product.getGradeAbc().equals(importProduct.getGradeAbc()))
							&& StringUtils.equals(product.getPackCode(),importProduct.getPackCode())
							&& (!EmptyChecker.isEmpty(product.getBottleQty()) && product.getBottleQty().equals(importProduct.getBottleQty()))) {
						changeImportCode(oldList, ImportCodeEnum.NO_CHANGE.getCode(), product.getSku(), null);
						changeImportCode(importList, ImportCodeEnum.NO_CHANGE.getCode(), product.getSku(), null);
					} else {
						changeImportCode(oldList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
						changeImportCode(importList, ImportCodeEnum.UPDATE.getCode(), product.getSku(), product.getId());
					}
					log.info(userId+" 导入产品,寻找修改数据start,product_sku=" + product.getSku());
				}
			}
		}
		
		//产品数据归类
		List<ProductMaster> delList = new ArrayList<ProductMaster>();
		List<ProductMaster> insertList = new ArrayList<ProductMaster>();
		List<ProductMaster> updateList = new ArrayList<ProductMaster>();
		importDataClassification(oldList, ImportCodeEnum.DELETE.getCode(), productList, delList);
		importDataClassification(importList, ImportCodeEnum.INSERT.getCode(), importProductList, insertList);
		importDataClassification(importList, ImportCodeEnum.UPDATE.getCode(), importProductList, updateList);
		String mess = "delList共 "+delList.size()+"条,updateList共 "+updateList.size()+"条,insertList共 "+insertList.size()+"条,";
		log.info("====导入产品,"+mess);
		//操作数据
		LogUtils.addInfoLog(Long.parseLong(userId), "com.sys.master.service.impl.ProductMasterServiceImpl.updateImportExcel", "保存数据"+mess);
		try {
			operateData(delList, ImportCodeEnum.DELETE.getCode(), userId);
			operateData(insertList, ImportCodeEnum.INSERT.getCode(), userId);
			operateData(updateList, ImportCodeEnum.UPDATE.getCode(), userId);
		} catch (WxPltException e) {
			log.error(e.getMessage(), e);
			LogUtils.addErrorLog(Long.parseLong(userId), "com.sys.master.service.impl.ProductMasterServiceImpl.updateImportExcel", e.getMessage(), null);
			throw new RuntimeException(e);
		}
		LogUtils.addInfoLog(Long.parseLong(userId), "com.sys.master.service.impl.ProductMasterServiceImpl.updateImportExcel", "导入成功");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return map;
	}
	
	/***
	 * 修改对应SKU的操作位
	 * @param importList
	 * @param sku
	 * @param importCode
	 */
	private void changeImportCode(List<ImportProduct> importList, String importCode, 
			String sku, Long id) {
		for(ImportProduct importData : importList) {
			if(sku.equals(importData.getSku())) {
				importData.setImportCode(importCode);
				if(ImportCodeEnum.UPDATE.getCode().equals(importCode)) {
					importData.setId(id);	
				}
			}
		}
	}
	
	/**
	 * 将对应SKU的产品放入操作列表中
	 * @param importList
	 * @param importCode
	 * @param productList
	 * @param initialDataList
	 */
	private void importDataClassification(List<ImportProduct> importList, String importCode,
			List<ProductMaster> productList, List<ProductMaster> initialDataList) {
		for(ImportProduct importData : importList) {
			if(importCode.equals(importData.getImportCode())) {
				for(ProductMaster product : productList) {
					if(importData.getSku().equals(product.getSku())) {
						initialDataList.add(product);
						product.setId(importData.getId());
					}
				}
			}
		}
	}
	
	/**
	 * 操作数据
	 * @throws WxPltException 
	 */
	private void operateData(List<ProductMaster> productList, String importCode, String userId) throws WxPltException {
		for(ProductMaster product : productList) {
			if(ImportCodeEnum.INSERT.getCode().equals(importCode)) {
				product.setIsCollect(0);
				product.setIsCompeting(0);
				product.setSupportOrder(0);
				product.setForecastLeadingTime(3);
				productMasterBizService.save(product, userId);
			} else if(ImportCodeEnum.UPDATE.getCode().equals(importCode)) {
				productMasterBizService.save(product, userId);
			} else if(ImportCodeEnum.DELETE.getCode().equals(importCode)){
				List<Long> ids = new ArrayList<Long>();
				ids.add(product.getId());
				delete(ids);
			}
		}
	}

	@Override
	public ResponseMap queryByParams(ProductMaster params) {
		ResponseMap responseMap = new ResponseMap();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		if(StringUtils.isNotBlank(params.getName())) {
			reqMap.put("name",params.getName() );
		}
		responseMap.setListResult(productMasterBizService.queryByParams(reqMap));
		return responseMap;
	}
}
