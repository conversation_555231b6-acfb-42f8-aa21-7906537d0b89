package com.chevron.plc.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.common.exception.auth.WxAuthException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.partnerorder.dao.PartnerInventoryInRecordMapper;
import com.chevron.partnerorder.dao.PartnerInventoryOutRecordMapper;
import com.chevron.partnerorder.dao.PartnerInventoryVoMapper;
import com.chevron.partnerorder.model.PartnerInventoryInRecord;
import com.chevron.partnerorder.model.PartnerInventoryInRecordExample;
import com.chevron.partnerorder.model.PartnerInventoryOutRecord;
import com.chevron.partnerorder.model.PartnerInventoryOutRecordExample;
import com.chevron.partnerorder.model.PartnerInventoryVo;
import com.chevron.partnerorder.model.PartnerInventoryVoExample;
import com.chevron.plc.dao.InStockLineVoMapper;
import com.chevron.plc.dao.InStockProductVoMapper;
import com.chevron.plc.dao.InStockVoMapper;
import com.chevron.plc.dao.OutStockLineVoMapper;
import com.chevron.plc.dao.OutStockProductVoMapper;
import com.chevron.plc.dao.OutStockVoMapper;
import com.chevron.plc.dao.StockProductQtyVarianceVoMapper;
import com.chevron.plc.model.InStockLineVo;
import com.chevron.plc.model.InStockVo;
import com.chevron.plc.model.InStockVoExample;
import com.chevron.plc.model.OutStockLineVo;
import com.chevron.plc.model.OutStockLineVoExample;
import com.chevron.plc.model.OutStockVo;
import com.chevron.plc.model.OutStockVoExample;
import com.chevron.plc.model.StockProductQtyVarianceCondition;
import com.chevron.plc.model.StockProductQtyVarianceView;
import com.chevron.plc.model.StockProductQtyVarianceVo;
import com.chevron.plc.model.StockProductQtyVarianceVoExample;
import com.chevron.plc.model.StockVarianceView;
import com.chevron.plc.service.AppOutStockService;
import com.chevron.plc.service.StockProductQtyVarianceService;
import com.chevron.pms.dao.InventoryVoMapper;
import com.chevron.pms.dao.OemProductPackagingCodeMapper;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.dao.WorkshopInventoryInRecordMapper;
import com.chevron.pms.model.InventoryVo;
import com.chevron.pms.model.OemProductPackagingCode;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.model.WorkshopInventoryInRecord;
import com.chevron.pms.model.WorkshopInventoryInRecordExample;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;

@Service
public class StockProductQtyVarianceServiceImpl implements StockProductQtyVarianceService {
	
	private Logger log = LoggerFactory.getLogger(StockProductQtyVarianceServiceImpl.class);
	
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	
	@Autowired
	private StockProductQtyVarianceVoMapper stockProductQtyVarianceVoMapper;
	@Autowired
	private InStockVoMapper inStockMapper;
	@Autowired
	private OutStockVoMapper outStockMapper;
	@Autowired
	private InStockLineVoMapper inStockLineMapper;
	@Autowired
	private OutStockLineVoMapper outStockLineMapper;
	@Autowired
	private InStockProductVoMapper inStockProductMapper;
	@Autowired
	private OutStockProductVoMapper outStockProductMapper;
	@Autowired
	private OemProductPackagingCodeMapper oemProductPackingCodeMapper;
	@Autowired
	private PartnerInventoryOutRecordMapper partnerInventoryOutRecordMapper;
	@Autowired
	private PartnerInventoryInRecordMapper partnerInventoryInRecordMapper;
	@Autowired
	private PartnerInventoryVoMapper partnerInventoryVoMapper;
	@Autowired
	private WorkshopInventoryInRecordMapper workshopInventoryInRecordMapper;
	@Autowired
	private InventoryVoMapper workshopInventoryMapper;
	@Autowired
	private ProductVoMapper productVoMapper;
	@Autowired
	private JdbcTemplate jdbcTemplate;
	@Autowired
	private AppOutStockService outStockService;

	@Override
	public Map<String, Object> selectByPage(
			StockProductQtyVarianceCondition praContition) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<StockProductQtyVarianceView> lstQtyVariance = new ArrayList<StockProductQtyVarianceView>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(null==praContition)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			if("false".equals(is_custommybatisintercepor))
			{
				praContition.setIsOpernCustomMybatisInterceptor("2");//关闭的
			}
			praContition.setField("create_time");
			praContition.setDirection("desc");
			//1.查询数据
			Long totalRecord = 0L;
			lstQtyVariance = stockProductQtyVarianceVoMapper.selectByPage(praContition);
			totalRecord = praContition.getTotalCount();
			resultMap.put(RESULT_LST_KEY, lstQtyVariance);
			resultMap.put("totalRecord", totalRecord);
					
		}catch (Exception e) {
			log.error(e.getMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> selectDistinctStockNoByPage(
			StockProductQtyVarianceCondition praContition) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<StockVarianceView> lstQtyVariance = new ArrayList<StockVarianceView>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(null==praContition)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			if("false".equals(is_custommybatisintercepor))
			{
				praContition.setIsOpernCustomMybatisInterceptor("2");//关闭的
			}
			praContition.setField("create_time");
			praContition.setDirection("desc");
			//1.查询数据
			Long totalRecord = 0L;
			lstQtyVariance = stockProductQtyVarianceVoMapper.selectStockNoByPage(praContition);
			totalRecord = praContition.getTotalCount();
			resultMap.put(RESULT_LST_KEY, lstQtyVariance);
			resultMap.put("totalRecord", totalRecord);
					
		}catch (Exception e) {
			log.error(e.getMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}
	
	//String stockNo, String stockType,String code
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> manualStockCodeConfimr(Long id) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		StockProductQtyVarianceVoExample  stockProductQtyVarianceExample = new StockProductQtyVarianceVoExample();
		stockProductQtyVarianceExample.createCriteria().andIdEqualTo(id);
		StockProductQtyVarianceVo stockProductQtyVariance = stockProductQtyVarianceVoMapper.selectByExample(stockProductQtyVarianceExample).get(0);
		String code = stockProductQtyVariance.getCode();
		String stockType = stockProductQtyVariance.getStockType();
		String stockNo = stockProductQtyVariance.getStockNo();
		try
		{
			List<OemProductPackagingCode> packagingList  = oemProductPackingCodeMapper.queryProductPackingCodeByCode(code);
			if(packagingList == null || packagingList.isEmpty()){
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "code无效，确认失败！");
				return resultMap;
			}
			OemProductPackagingCode codePackaging = packagingList.get(0);
			String codeType = "";
			if(codePackaging.getCode1().equals(code)){
				codeType = Constants.STOCK_LINE_CODE_TYPE_LOGISTIC;
			}else if (codePackaging.getCode2().equals(code)){
				codeType = Constants.STOCK_LINE_CODE_TYPE_BOX;
			}else if (codePackaging.getCode3().equals(code)){
				codeType = Constants.STOCK_LINE_CODE_TYPE_STACK;
			}
			Set<String> bottleCodeSet = new HashSet<String>();
			for(OemProductPackagingCode tempCodePackaging : packagingList){
				bottleCodeSet.add(tempCodePackaging.getCode1());
			}
			
			if(stockType.equals(StockProductQtyVarianceVo.stockType_outstock)){
				OutStockVoExample osExp = new OutStockVoExample();
				osExp.setDistinct(true);
				OutStockVoExample.Criteria osCriteria = osExp.createCriteria();
				osCriteria.andStockOutNoEqualTo(stockNo);
				List<OutStockVo> osList = outStockMapper.selectByExample(osExp);		
				OutStockVo outStockExisted = osList.get(0);
				
				OutStockLineVo oslVo = new OutStockLineVo();
				oslVo.setCode(code);
				oslVo.setCodeType(codeType);
				oslVo.setScanTime(new Date());
				oslVo.setStockOutNo(stockNo);
				oslVo.setRemark("手动确认");
				outStockLineMapper.insertSelective(oslVo);
				
				//插入有效的出库bottlecode记录
				List<PartnerInventoryOutRecord> insertPartOutList = new ArrayList<PartnerInventoryOutRecord>();
				//出库扫码只会有从中外运、合伙人出库
				for(String tempBottleCode : bottleCodeSet){
					if(outStockExisted.getStockFromType().equals(Constants.STOCK_TYPE_ZWY) || outStockExisted.getStockFromType().equals(Constants.STOCK_TYPE_SP)){
						PartnerInventoryOutRecordExample partnerInventoryOutRecordExample = new PartnerInventoryOutRecordExample();
						PartnerInventoryOutRecordExample.Criteria  partnerInventoryOutRecodeCriteria = partnerInventoryOutRecordExample.createCriteria();
						partnerInventoryOutRecodeCriteria.andPartnerIdEqualTo(Long.valueOf(outStockExisted.getStockFrom()));
						partnerInventoryOutRecodeCriteria.andCodeEqualTo(tempBottleCode);
						partnerInventoryOutRecodeCriteria.andCodeTypeEqualTo(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);						
						List<PartnerInventoryOutRecord> outRecordListExisted = partnerInventoryOutRecordMapper.selectByExample(partnerInventoryOutRecordExample); 
						if(outRecordListExisted == null || outRecordListExisted.isEmpty()){
							PartnerInventoryOutRecord record = new PartnerInventoryOutRecord();
							record.setStockOutNo(outStockExisted.getStockOutNo());
							record.setCode(tempBottleCode);
							record.setCodeType(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);
							record.setPartnerId(Long.valueOf(outStockExisted.getStockFrom()));
							record.setPartnerName(outStockExisted.getStockFromOrgname());
							record.setCreateTime(new Date());
							insertPartOutList.add(record);
						}
					}
				}
				if(!insertPartOutList.isEmpty()){
					ImportDataPageModelUtil insertPage = new ImportDataPageModelUtil(
							insertPartOutList, 50);
					int insertTotal = insertPage.getTotalPages();
					for (int i = 1; i <= insertTotal; i++) {
						List<PartnerInventoryOutRecord> volst = insertPage.getObjects(i);
						// 访问数据库 进行批量更新操作
						partnerInventoryOutRecordMapper.insertBatch(volst);
					}					
				}
				// 更新库存
				if(outStockExisted.getStockFromType().equals(Constants.STOCK_TYPE_SP)){
					StringBuffer sb= new StringBuffer();
					String bottleCode = insertPartOutList.get(0).getCode();
					sb.append("select top 1 d.sku from wx_t_qr_code_detail d  where d.code_id =(-2910000000 + convert(bigint,'");
					sb.append(bottleCode);
					sb.append("'))");
					List<String> skuList = jdbcTemplate.queryForList(sb.toString(), new Object[]{}, String.class);
					String sku = skuList.get(0);
					Long skuCount = Long.valueOf( insertPartOutList.size());
					
					PartnerInventoryVo piv = new PartnerInventoryVo();
					PartnerInventoryVoExample pive = new PartnerInventoryVoExample();
					PartnerInventoryVoExample.Criteria piveCriteria = pive.createCriteria();
					piveCriteria.andPartnerIdEqualTo(Long.valueOf(outStockExisted.getStockFrom()));
					piveCriteria.andSkuEqualTo(sku);
					List<PartnerInventoryVo> inverntoryList = partnerInventoryVoMapper.selectByExample(pive);
					if(inverntoryList != null && !inverntoryList.isEmpty()){
						piv = inverntoryList.get(0);
						Long inventory = piv.getQuantity() - skuCount;
						piv.setQuantity(Integer.valueOf(inventory.toString()));
						piv.setUpdateTime(new Date());
						piv.setUpdator(ContextUtil.getCurUserId() == null ? null : ContextUtil.getCurUserId().toString());
						partnerInventoryVoMapper.updateByExample(piv, pive);
					}else{
						piv.setSku(sku);
						ProductVo product = productVoMapper.selectBySku(sku);
						piv.setProductName(product.getName());
						piv.setPartnerId(Long.valueOf(outStockExisted.getStockFrom()));
						piv.setPartnerName(outStockExisted.getStockFromOrgname());
						piv.setQuantity(0 - skuCount.intValue());
						piv.setStatus("1");
						piv.setCreateTime(new Date());
						partnerInventoryVoMapper.insertSelective(piv);
					}
					if(outStockExisted.getStockToType().equals(Constants.STOCK_TYPE_WORKSHOP)){
						//合伙人出库核销
						OutStockLineVo[] oslLines = new OutStockLineVo[]{oslVo};
						outStockService.processVerification(Long.valueOf(outStockExisted.getStockFrom()), outStockExisted.getStockFromOrgname(), Long.valueOf(outStockExisted.getStockTo()), oslLines);
					}
				}			
			}else if (stockType.equals(StockProductQtyVarianceVo.stockType_instock)){
				InStockVoExample insExp = new InStockVoExample();
				insExp.setDistinct(true);
				InStockVoExample.Criteria insCriteria = insExp.createCriteria();
				insCriteria.andStockInNoEqualTo(stockNo);
				List<InStockVo> insList = inStockMapper.selectByExample(insExp);		
				InStockVo inStockExisted = insList.get(0);
				
				InStockLineVo inslVo = new InStockLineVo();
				inslVo.setCode(code);
				inslVo.setCodeType(codeType);
				inslVo.setScanTime(new Date());
				inslVo.setStockInNo(stockNo);
				inslVo.setRemark("手动确认");
				inStockLineMapper.insertSelective(inslVo);
				
				//出库扫码只会有从中外运、合伙人出库
				if(inStockExisted.getStockToType().equals(Constants.STOCK_TYPE_SP)){
					//插入有效的入库bottlecode记录
					List<PartnerInventoryInRecord> insertPartInList = new ArrayList<PartnerInventoryInRecord>();
					
					for(String tempBottleCode : bottleCodeSet){
						PartnerInventoryInRecordExample partnerInventoryInRecordExample = new PartnerInventoryInRecordExample();
						PartnerInventoryInRecordExample.Criteria  partnerInventoryInRecodeCriteria = partnerInventoryInRecordExample.createCriteria();
						partnerInventoryInRecodeCriteria.andPartnerIdEqualTo(Long.valueOf(inStockExisted.getStockTo()));
						partnerInventoryInRecodeCriteria.andCodeEqualTo(tempBottleCode);
						partnerInventoryInRecodeCriteria.andCodeTypeEqualTo(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);						
						List<PartnerInventoryInRecord> inRecordListExisted = partnerInventoryInRecordMapper.selectByExample(partnerInventoryInRecordExample); 
						if(inRecordListExisted == null || inRecordListExisted.isEmpty()){
							PartnerInventoryInRecord record = new PartnerInventoryInRecord();
							record.setStockInNo(inStockExisted.getStockInNo());
							record.setCode(tempBottleCode);
							record.setCodeType(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);
							record.setPartnerId(Long.valueOf(inStockExisted.getStockTo()));
							record.setPartnerName(inStockExisted.getStockToOrgname());
							record.setCreateTime(new Date());
							insertPartInList.add(record);
						}
					}
					if(!insertPartInList.isEmpty()){
						ImportDataPageModelUtil insertPage = new ImportDataPageModelUtil(
								insertPartInList, 50);
						int insertTotal = insertPage.getTotalPages();
						for (int i = 1; i <= insertTotal; i++) {
							List<PartnerInventoryInRecord> volst = insertPage.getObjects(i);
							// 访问数据库 进行批量更新操作
							partnerInventoryInRecordMapper.insertBatch(volst);
						}					
					}
					// 更新库存
					StringBuffer sb= new StringBuffer();
					String bottleCode = insertPartInList.get(0).getCode();
					sb.append("select top 1 d.sku from wx_t_qr_code_detail d  where d.code_id =(-2910000000 + convert(bigint,'");
					sb.append(bottleCode);
					sb.append("'))");
					List<String> skuList = jdbcTemplate.queryForList(sb.toString(), new Object[]{}, String.class);
					String sku = skuList.get(0);
					Long skuCount = Long.valueOf( insertPartInList.size());
					
					PartnerInventoryVo piv = new PartnerInventoryVo();
					PartnerInventoryVoExample pive = new PartnerInventoryVoExample();
					PartnerInventoryVoExample.Criteria piveCriteria = pive.createCriteria();
					piveCriteria.andPartnerIdEqualTo(Long.valueOf(inStockExisted.getStockTo()));
					piveCriteria.andSkuEqualTo(sku);
					List<PartnerInventoryVo> inverntoryList = partnerInventoryVoMapper.selectByExample(pive);
					if(inverntoryList != null && !inverntoryList.isEmpty()){
						piv = inverntoryList.get(0);
						Long inventory = piv.getQuantity() + skuCount;
						piv.setQuantity(Integer.valueOf(inventory.toString()));
						piv.setUpdateTime(new Date());
						piv.setUpdator(ContextUtil.getCurUserId() == null ? null : ContextUtil.getCurUserId().toString());
						partnerInventoryVoMapper.updateByExample(piv, pive);
					}else{
						piv.setSku(sku);
						ProductVo product = productVoMapper.selectBySku(sku);
						piv.setProductName(product.getName());
						piv.setPartnerId(Long.valueOf(inStockExisted.getStockTo()));
						piv.setPartnerName(inStockExisted.getStockToOrgname());
						piv.setQuantity(skuCount.intValue());
						piv.setStatus("1");
						piv.setCreateTime(new Date());
						partnerInventoryVoMapper.insertSelective(piv);
					}	
				}else if(inStockExisted.getStockToType().equals(Constants.STOCK_TYPE_WORKSHOP)){
					List<WorkshopInventoryInRecord> insertWorkshopInList = new ArrayList<WorkshopInventoryInRecord>();
					for(String tempBottleCode : bottleCodeSet){
						WorkshopInventoryInRecordExample workshopInventoryInRecordExample = new WorkshopInventoryInRecordExample();
						WorkshopInventoryInRecordExample.Criteria  workshopInventoryInRecodeCriteria = workshopInventoryInRecordExample.createCriteria();
						workshopInventoryInRecodeCriteria.andCodeEqualTo(tempBottleCode);
						workshopInventoryInRecodeCriteria.andCodeTypeEqualTo(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);						
						List<WorkshopInventoryInRecord> inRecordListExisted = workshopInventoryInRecordMapper.selectByExample(workshopInventoryInRecordExample); 
						if(inRecordListExisted == null || inRecordListExisted.isEmpty()){
							WorkshopInventoryInRecord record = new WorkshopInventoryInRecord();
							record.setStockInNo(inStockExisted.getStockInNo());
							record.setCode(tempBottleCode);
							record.setCodeType(Constants.STOCK_LINE_CODE_TYPE_LOGISTIC);
							record.setWorkshopId(Long.valueOf(inStockExisted.getStockTo()));
							record.setWorkshopName(inStockExisted.getStockToOrgname());
							record.setCreateTime(new Date());
							insertWorkshopInList.add(record);
						}
					}
					if(!insertWorkshopInList.isEmpty()){
						ImportDataPageModelUtil insertPage = new ImportDataPageModelUtil(
								insertWorkshopInList, 50);
						int insertTotal = insertPage.getTotalPages();
						for (int i = 1; i <= insertTotal; i++) {
							List<WorkshopInventoryInRecord> volst = insertPage.getObjects(i);
							// 访问数据库 进行批量更新操作
							workshopInventoryInRecordMapper.insertBatch(volst);
						}					
					}
					// 更新库存
					StringBuffer sb= new StringBuffer();
					String bottleCode = insertWorkshopInList.get(0).getCode();
					sb.append("select top 1 d.sku from wx_t_qr_code_detail d  where d.code_id =(-2910000000 + convert(bigint,'");
					sb.append(bottleCode);
					sb.append("'))");
					List<String> skuList = jdbcTemplate.queryForList(sb.toString(), new Object[]{}, String.class);
					String sku = skuList.get(0);
					Long skuCount = Long.valueOf( insertWorkshopInList.size());
		
					InventoryVo piv = workshopInventoryMapper
							.queryInventoryByWorkshopAndSku(
									Long.valueOf(inStockExisted.getStockTo()),
									sku);
					if (piv != null) {
						Long inventory = piv.getQuantity() + skuCount;
						piv.setQuantity(Integer.valueOf(inventory.toString()));
						piv.setUpdateTime(new Date());
						workshopInventoryMapper.updateByPrimaryKey(piv);
					} else {
						piv = new InventoryVo();
						piv.setSku(sku);
						ProductVo product = productVoMapper.selectBySku(sku);
						piv.setProductName(product.getName());
						piv.setWorkshopId(Long.valueOf(inStockExisted.getStockTo()));
						piv.setWorkshopName(inStockExisted.getStockToOrgname());
						piv.setQuantity(skuCount.intValue());
						piv.setStatus("1");
						piv.setCreateTime(new Date());
						workshopInventoryMapper.insertSelective(piv);
					}					
				}
			}
			stockProductQtyVariance.setStatus(StockProductQtyVarianceVo.varianceType_scan_not_checked_status_0);
			stockProductQtyVarianceVoMapper.updateByExampleSelective(stockProductQtyVariance, stockProductQtyVarianceExample);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			log.info("code：" + code+ "手动确认失败");		
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;

	}
	
	

}
