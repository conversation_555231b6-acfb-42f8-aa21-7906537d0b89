truncate table [PP_MID].[dbo].[pp_customer_pricing_level_cfg_bak0309] 
insert into [PP_MID].[dbo].[pp_customer_pricing_level_cfg_bak0309] ([customer_name]
      ,[payment_term]
      ,[price_level_type]
      ,[price_level_value]
      ,[date_from]
      ,[date_to]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[updated_by]
      ,[lgcl_del_fg]
	  ,partner_id)
select c.[customer_name]
      ,c.[payment_term]
      ,[price_level_type]
      ,[price_level_value]
      ,c.[date_from]
      ,c.[date_to]
      ,c.[creation_time]
      ,c.[created_by]
      ,c.[update_time]
      ,c.[updated_by]
      ,[lgcl_del_fg]
	  ,o.id partner_id
from [PP_MID].[dbo].[pp_customer_pricing_level_cfg] c 
left join wx_t_organization o on o.organization_name=c.[customer_name]

update c set c.date_to=dateadd(day, -1, getdate()), c.[update_time]=getdate()
from [PP_MID].[dbo].[pp_customer_pricing_level_cfg_bak0309] c
where price_level_type='ship_to_code' 
and date_to>getdate()
and not exists (select 1 FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					LEFT JOIN wx_t_organization org ON org.id = poe.partner_id
					where org.status=1 and psc.ship_to_code=c.price_level_value 
					and psc.payment_term=c.payment_term and psc.price_level='ship_to_code') 

insert into [PP_MID].[dbo].[pp_customer_pricing_level_cfg_bak0309] ([customer_name]
      ,[payment_term]
      ,[price_level_type]
      ,[price_level_value]
      ,[date_from]
      ,[date_to]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[updated_by]
      ,[lgcl_del_fg]
	  ,partner_id)
select org.organization_name [customer_name]
      ,psc.[payment_term]
      ,'ship_to_code' [price_level_type]
      ,psc.ship_to_code [price_level_value]
      ,getdate() [date_from]
      ,'2100-01-01' [date_to]
      ,getdate() [creation_time]
      ,0 [created_by]
      ,getdate() [update_time]
      ,0 [updated_by]
      ,'N' [lgcl_del_fg]
	  ,org.id partner_id
FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					LEFT JOIN wx_t_organization org ON org.id = poe.partner_id
					where org.status=1 and psc.price_level='ship_to_code'
					and not exists (select 1 from [PP_MID].[dbo].[pp_customer_pricing_level_cfg_bak0309] c
						where psc.ship_to_code=c.price_level_value 
					and psc.payment_term=c.payment_term and c.date_to>getdate())					