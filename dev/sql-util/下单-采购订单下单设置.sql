--初始化采购订单
insert into wx_t_partner_bill ([partner_id]
      ,[partner_name]
      ,[init_amount]
      ,[total_amount]
      ,[update_time]
      ,[remark]
      ,[status])
select o.id, o.organization_name, 0, 0, getdate(), null, 1 from wx_t_organization o left join 
wx_t_partner_bill pb on o.id=pb.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where exists (select 1 from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id
left join wx_t_role r on r.role_id=ur.role_id where u.user_id=ur.user_id
and r.ch_role_name in ('Service_Partner_Manager') and (u.type is null or u.type!='1') and u.org_id=o.id) and o.status=1 and o.type=1
and pb.id is null

insert into wx_t_partner_manage_fee ([partner_id]
      ,[partner_name]
      ,[init_amount]
      ,[total_amount]
      ,[update_time]
      ,[remark]
      ,[status])
select o.id, o.organization_name, 0, 0, getdate(), null, 1 from wx_t_organization o left join 
wx_t_partner_manage_fee pb on o.id=pb.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where exists (select 1 from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id
left join wx_t_role r on r.role_id=ur.role_id where u.user_id=ur.user_id
and r.ch_role_name in ('Service_Partner_Manager') and (u.type is null or u.type!='1') and u.org_id=o.id) and o.status=1 and o.type=1
and pb.id is null

/*
insert into dw_access_control_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[customer_category]
      ,[customer_type]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[control_type]
      ,[control_type_name]
      ,[product_channel]
      ,[channel_weight]
      ,[from_source])

select a.new_id [distributor_id]
      ,a.organization_name [customer_name_cn]
      ,cs.[org_hier_id]
      ,cs.[region]
      ,cs.[sales_cai]
      ,cs.[sales_name_cn]
      ,cs.[customer_category]
      ,cs.[customer_type]
      ,cs.[create_time]
      ,cs.[update_time]
      ,cs.[del_flag]
      ,cs.[control_type]
      ,cs.[control_type_name]
      ,cs.[product_channel]
	  ,1 channel_weight
	  ,'PP' from_source from dw_access_control_customer_org_sales cs
left join (select pe.distributor_id new_id, c.distributor_id, o.organization_name from wx_t_partner_o2o_enterprise pe 
left join wx_t_organization o on o.id=pe.partner_id
left join PP_MID.dbo.[syn_dw_to_pp_customer] c on pe.sap_code=c.customer_code where pe.distributor_id in (-1,-6)) a on cs.distributor_id=a.distributor_id
where a.new_id is not null and cs.channel_weight&1>0*/