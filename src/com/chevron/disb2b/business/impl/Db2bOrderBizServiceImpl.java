package com.chevron.disb2b.business.impl;

import com.alibaba.fastjson.JSON;
import com.chevron.disb2b.activity.business.Db2bActivityBizService;
import com.chevron.disb2b.activity.dao.Db2bPromotionMapper;
import com.chevron.disb2b.activity.model.Db2bActivity;
import com.chevron.disb2b.activity.model.Db2bPromotion;
import com.chevron.disb2b.activity.model.OrderPromotionResult;
import com.chevron.disb2b.activity.util.PromotionType;
import com.chevron.disb2b.activity.util.config.BuyGiftsConfig;
import com.chevron.disb2b.activity.util.config.DiscountConfig;
import com.chevron.disb2b.activity.util.config.FullReductionConfig;
import com.chevron.disb2b.business.Db2bDistSettingBizService;
import com.chevron.disb2b.business.Db2bOrderBizService;
import com.chevron.disb2b.business.Db2bPointLogBizService;
import com.chevron.disb2b.dao.*;
import com.chevron.disb2b.model.*;
import com.chevron.disb2b.service.Db2bProductService;
import com.chevron.disb2b.util.BaseProductPromotion;
import com.chevron.disb2b.util.Db2bConstants;
import com.chevron.o2oorder.Constant.AesException;
import com.chevron.o2oorder.Constant.HttpClientUtils;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.dao.WorkshopPartnerVoMapper;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopEmployeeExample;
import com.chevron.pms.model.WorkshopPartnerVo;
import com.chevron.pms.service.OrderBizService;
import com.chevron.pms.service.OrderService;
import com.chevron.pms.service.WorkshopPartnerService;
import com.chevron.task.model.app.Inventory;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.google.common.collect.Lists;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.utils.dao.WorkshopRelationshipMapper;
import com.sys.utils.model.WorkshopRelationship;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单业务接口实现类
 *
 * <AUTHOR>
 * @version 1.0 2019-12-09 15:37
 */
@Service
public class Db2bOrderBizServiceImpl implements Db2bOrderBizService {
    @Autowired
    private Db2bOrderMapper db2bOrderMapper;

    @Autowired
    Db2bShoppingCarMapper db2bShoppingCarMapper;
    @Autowired
    WorkshopEmployeeMapper workshopEmployeeMapper;
    @Autowired
    WorkshopPartnerService workshopPartnerService;
    @Autowired
    Db2bDistSettingBizService db2bDistSettingBizService;
    @Autowired
    Db2bPointLogBizService db2bPointLogBizService;
    @Autowired
    Db2bOrderLineMapper db2bOrderLineMapper;
    @Autowired
    Db2bProductMapper db2bProductMapper;
    @Autowired
    Db2bProductCutMapper db2bProductCutMapper;
    @Autowired
    WxTPropertiesMapper wxTPropertiesMapper;
    @Autowired
    Db2bPointDetialMapper db2bPointDetialMapper;
    @Autowired
    Db2bProductRelationMapper db2bProductRelationMapper;
    @Autowired
    private WorkshopPartnerVoMapper workshopPartnerVoMapper;
    @Autowired
    private OrderBizService orderBizService;
    @Autowired
    private Db2bProductService db2bProductService;
    @Autowired
    private Db2bProductPointsMapper db2bProductPointsMapper;
    @Autowired
    private Db2bDistSettingMapper db2bDistSettingMapper;
    @Autowired
    private Db2bDistAppleteConfigMapper db2bDistAppleteConfigMapper;
    @Autowired
    private WorkshopRelationshipMapper workshopRelationship;
    @Autowired
    private Db2bBuyAgroupToBgroupMapper db2bBuyAgroupToBgroupMapper;
    @Autowired
    private Db2bOrderPromotionMapper db2bOrderPromotionMapper;
    @Autowired
    private PromotionBgroupProductMapper promotionBgroupProductMapper;
    @Autowired
    private OrderService salesOrderService;
    @Autowired
    private Db2bActivityBizService db2bActivityBizService;
    @Autowired
    private Db2bPromotionMapper db2bPromotionMapper;

    public static final Integer MAX_AMOUNT = 999999999;
    public static final Integer MIN_AMOUNT = -1;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> insert(Db2bOrder record) throws Exception {
        Map<String, Object> resultMap;
        Date nowDate = DateUtil.getCurrentDate();
        record.setDeleteFlag(0);
        record.setCreateUserId(ContextUtil.getCurUserId());
        record.setCreateTime(nowDate);
        //获取门店信息根据手机号
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        record.setPlatForm(record.getPlatform());
        if (Db2bConstants.PLATFORM_APP.equals(record.getPlatform())) {
            paramsMap.put("workshopId", record.getWorkshopId());
            record.setPartnerId(getPartnerIdByWorkshopId(paramsMap));
            record.setTechnicianName(ContextUtil.getUsername());//dsr销售Cai
            record.setTechnicianMobile(ContextUtil.getCurUser().getMobileTel());//dsr销售手机号
        } else {
            paramsMap.put("mobile", record.getTechnicianMobile());
            paramsMap.put("enableFlag", 1);
            List<WorkshopEmployee> list = workshopEmployeeMapper.selectByParams(paramsMap);
            if (!list.isEmpty()) {
                record.setWorkshopId(list.get(0).getWorkshopId());
                record.setTechnicianName(list.get(0).getName());
                //根据门店获取经销商Id
                record.setPartnerId(list.get(0).getPartnerId());
            } else {
                throw new WxPltException("门店不存在");
            }
        }
        //生成订单号
        String orderNo = "B2B" + DateUtils.getCurrentDate("yyyyMMddHHmm")
                + CommonUtil.generateSequenceCode("CreateOrderNoByTime", 4);
        record.setOrderNo(orderNo);

        //获取系统产品版本快照
        WxTProperties wxTProperties = getProductVersion();
        String productVersion;
        if (wxTProperties == null) {
            throw new WxPltException("产品版本号不存在，请到系统维护界面设置");
        } else {
            productVersion = wxTProperties.getCodename();
        }
        //查询经销商积分规则
        Db2bDistSetting db2bDistSetting = db2bDistSettingBizService.getBeanByPartnerId(record.getPartnerId());
        if (db2bDistSetting != null) {
            record.setPointId(db2bDistSetting.getId());
        }
        double totalPriceDouble = 0D;
        double totalPayPriceDouble = 0D;
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("workShopId", record.getWorkshopId());
        List<Db2bShoppingCar> db2bShoppingCars = record.getDb2bShoppingCars();
        // 所有促销活动信息集合
        List<OrderPromotionResult> list = new ArrayList<OrderPromotionResult>();
        // 缓存购物车信息，进行促销校验和处理
        Map<Long, Db2bShoppingCar> map = new HashMap<Long, Db2bShoppingCar>();
        // 缓存产品折扣信息，方便保存订单信息时候使用
        Map<Long, OrderPromotionResult> promotionResultMap = new HashMap<Long, OrderPromotionResult>();
        for (Db2bShoppingCar shoppingCar : db2bShoppingCars) {
            Db2bProduct db2bProduct = db2bProductMapper.selectByPrimaryKey(shoppingCar.getProductId());
            shoppingCar.setProductPrice(db2bProduct.getProductPrice());
            OrderPromotionResult result = db2bActivityBizService.getPromotionsByCar(shoppingCar, params);
            list.add(result);
            map.put(shoppingCar.getProductId(), shoppingCar);
        }
        List<OrderPromotionResult> db2bOrderPromotions = process(list, map);

        for (OrderPromotionResult result : db2bOrderPromotions) {
            promotionResultMap.put(result.getProductId(), result);
        }
        //保存订单明细
        List<Db2bOrderLine> db2bOrderLines = new ArrayList<Db2bOrderLine>();
        for (Long aLong : map.keySet()) {
            Db2bShoppingCar shoppingCar = map.get(aLong);
            //删除购物车
            if (shoppingCar.getId() != null) {
                db2bShoppingCarMapper.deleteByPrimaryKey(shoppingCar.getId());
            }
            //保存产品快照
            Db2bProductCut db2bProductCut = new Db2bProductCut();
            //根据产品Id查询产品详情
            Db2bProduct db2bProduct = db2bProductMapper.selectByPrimaryKey(shoppingCar.getProductId());
            db2bProductCut.setProductId(db2bProduct.getId());
            db2bProductCut.setProductName(db2bProduct.getProductName());
            db2bProductCut.setProductPrice(db2bProduct.getProductPrice());
            db2bProductCut.setCreateTime(DateUtil.getCurrentDate());
            db2bProductCut.setCreateUserId(ContextUtil.getCurUserId());
            db2bProductCut.setProductDetail(JSON.toJSONString(db2bProduct));
            db2bProductCut.setProductDetailVersion(productVersion);
            db2bProductCutMapper.insertSelective(db2bProductCut);

            //产品为打包产品则需要关联中间表
            List<Db2bProductRelation> relations;

            OrderPromotionResult result = promotionResultMap.get(aLong);
            //促销优惠Remark
            String remark = result.getRemark();

            //产品订单明细
            Db2bOrderLine orderLine = new Db2bOrderLine();
            orderLine.setProductId(shoppingCar.getProductId());
            orderLine.setAmount(shoppingCar.getAmount());
            orderLine.setOrderId(record.getId());
            orderLine.setProductCutId(db2bProductCut.getId());//产品快照Id
            orderLine.setRemark(remark);
            orderLine.setDeleteFlag(0);
            orderLine.setRefundFlag(0);
            orderLine.setPrice(shoppingCar.getProductPrice());
            orderLine.setTotalPrice(String.valueOf(db2bProduct.getProductPrice() * shoppingCar.getAmount()));
            orderLine.setAdditionalFlag(0);
            db2bOrderLines.add(orderLine);
            //实际产品订单是产品包则子产品作为附加产品处理，无价格，无积分
            if (db2bProduct.getProductType() == 1) {
                Map<String, Object> paramMap = new HashMap<String, Object>();
                paramMap.put("parentProductId", db2bProduct.getId());
                relations = db2bProductRelationMapper.queryByParams(paramMap);
                if (null != relations && relations.size() > 0) {
                    for (Db2bProductRelation relation : relations) {
                        Db2bOrderLine orderLine2 = new Db2bOrderLine();
                        orderLine2.setOrderId(record.getId());
                        orderLine2.setRemark(remark);
                        orderLine2.setDeleteFlag(0);
                        orderLine2.setRefundFlag(0);
                        orderLine2.setAdditionalFlag(1);
                        orderLine2.setProductId(relation.getRelationProuctId());
                        orderLine2.setRemark(Db2bConstants.GIFT_REMARK);
                        int amount = shoppingCar.getAmount() * relation.getAmount();
                        orderLine2.setAmount(amount);
                        db2bOrderLines.add(orderLine2);
                    }
                }
            }
            totalPriceDouble += db2bProduct.getProductPrice() * shoppingCar.getAmount();
            totalPayPriceDouble += shoppingCar.getProductPrice() * shoppingCar.getAmount();
        }
        // 当前所有的满减

        List<Db2bPromotion> allFulls = db2bActivityBizService.getAllFulls(record.getWorkshopId());
        Db2bPromotion fulls = null;
        for (Db2bPromotion allFull : allFulls) {
            FullReductionConfig configResult = (FullReductionConfig) allFull.getConfigResult();
            Double totalPrice1 = configResult.getTotalPrice();
            if (totalPayPriceDouble > totalPrice1) {
                totalPayPriceDouble = BigDecimal.valueOf(totalPayPriceDouble)
                        .subtract(BigDecimal.valueOf(configResult.getMinusPrice())).doubleValue();
                fulls = allFull;
                break;
            }
        }
        record.setTotalPayMoney(totalPayPriceDouble);
        record.setTotalPrice(String.valueOf(totalPriceDouble));//去掉折扣后的价格，
        record.setTotalPayPoint(0d);
        //订单状态待确认
        db2bOrderMapper.insertSelective(record);

        List<PromotionBgroupProduct> pBgroupProductList = new ArrayList<PromotionBgroupProduct>();
        //保存订单促销
        for (Db2bOrderPromotion promotion : record.getDb2bOrderPromotion()) {
            promotion.setOrderId(record.getId());
            promotion.setExtProperty2("3");
            db2bOrderPromotionMapper.insertSelective(promotion);
            List<PromotionBgroupProduct> promotionBgroupProductList = promotion.getPromotionBgroupProductList();
            for (PromotionBgroupProduct bgroupProduct : promotionBgroupProductList) {
                bgroupProduct.setOrderPromotionId(promotion.getId());
                bgroupProduct.setPromotionId(promotion.getPromotionId());
                pBgroupProductList.add(bgroupProduct);
            }
        }
        //订单级促销明细
        if (!pBgroupProductList.isEmpty()) {
            for (PromotionBgroupProduct pBgroupProduct : pBgroupProductList) {
                Db2bOrderLine db2bOrderLin = new Db2bOrderLine();
                db2bOrderLin.setDeleteFlag(0);
                db2bOrderLin.setProductId(pBgroupProduct.getProductbId());
                db2bOrderLin.setPromotionId(pBgroupProduct.getPromotionId());
                db2bOrderLin.setAdditionalFlag(1);
                db2bOrderLin.setAmount(pBgroupProduct.getQuantity());
                db2bOrderLin.setRefundFlag(0);
                db2bOrderLin.setRemark(Db2bConstants.GIFT_REMARK);
                db2bOrderLines.add(db2bOrderLin);
            }
        }
        for (Db2bOrderLine db2bOrderLine : db2bOrderLines) {
            db2bOrderLine.setPrice(db2bOrderLine.getPrice() == null ? null : db2bOrderLine.getPrice());
            db2bOrderLine.setAwardTotalPoints(null);
            db2bOrderLine.setOrderId(record.getId());
        }
        db2bOrderLineMapper.insertBatch(db2bOrderLines);

        // 满减信息
        if (fulls != null) {
            String config = fulls.getConfig();
            Db2bOrderPromotion promotion = new Db2bOrderPromotion();
            promotion.setActivityLevel(fulls.getActivityLevel());
            promotion.setOrderId(record.getId());
            promotion.setPromotionId(fulls.getId());
            promotion.setExtProperty1(config);
            promotion.setExtProperty2("4");
            db2bOrderPromotionMapper.insertSelective(promotion);
        }
        //保存促销赠品
        if (pBgroupProductList.size() > 0) {
            promotionBgroupProductMapper.insertBatch(pBgroupProductList);
        }

        //获取微信支付配置信息
        resultMap = getWechatPaymentConfig(record, true);
        resultMap.put("orderId", record.getId());
        //生成订单之后需要 将订单入库

        //判断该门店是否属于EC门店，是则不生成出库单，否则生成
        if (!db2bProductService.isEcFlag(record.getWorkshopId())) {
            createOutStock(record, db2bOrderLines);
        }
        return resultMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> update(Db2bOrder record) throws WxPltException {
        record.setUpdateUserId(ContextUtil.getCurUserId());
        record.setUpdateTime(DateUtil.getCurrentDate());
        //订单状态为已收货，则扣积分和增加积分

        // 0 待付款 1 已付款，2,已出库 3 取消订单 4.订单 5.退款/售后

        if (Db2bConstants.MINI_PROGRAM_NAMES.equals(record.getPlatform())) {
            if (!OrderVo.ORDER_STATUS_1.equals(String.valueOf(record.getStatus()))) {
                throw new WxPltException("小程序不可以更改其他状态");
            }
        }
        if (OrderVo.ORDER_STATUS_1.equals(String.valueOf(record.getStatus()))) {
            //收货成功之后，执行扣积分和增加积分
            //根据订单id查询订单信息
            Db2bOrder db2bOrder = db2bOrderMapper.selectByPrimaryKey(record.getId());
            db2bOrder.setPlatform(record.getPlatform());
            if (db2bProductService.isEcFlag(db2bOrder.getWorkshopId())) {
                //有扣除积分则先减积分
                Db2bPointLog db2bPointLog = new Db2bPointLog();
                db2bPointLog.setModuleName(Db2bPointLog.MODULE_NAME_DB2B);
                db2bPointLog.setChargeType(Db2bPointLog.CHARGE_TYPE_DIST);
                db2bPointLog.setChargeKey(String.valueOf(db2bOrder.getPartnerId()));//经销商id
                db2bPointLog.setOwnerType(Db2bPointLog.OWNER_TYPE_WORKSHOP);
                db2bPointLog.setOwnerKey(String.valueOf(db2bOrder.getWorkshopId()));//门店id
                String sourceType = Db2bPointLog.SOURCE_TYPE;
                if (Db2bConstants.PLATFORM_APP.equals(db2bOrder.getPlatform())) {
                    sourceType = Db2bConstants.PLATFORM_APP;
                } else if (Db2bConstants.PUBLIC_NAME.equals(db2bOrder.getPlatForm())) {
                    sourceType = Db2bConstants.PUBLIC_NAME;
                } else if (Db2bConstants.MINI_PROGRAM_NAME.equals(db2bOrder.getPlatform())) {
                    sourceType = Db2bConstants.MINI_PROGRAM_NAME;
                }
                db2bPointLog.setSourceType(sourceType);
                db2bPointLog.setSourceKey(db2bOrder.getOrderNo());
                if (db2bOrder.getTotalPayPoint() > 0.0) {
                    db2bPointLog.setPointValue(db2bOrder.getTotalPayPoint());//积分
                    String logDesc = "订单【" + db2bOrder.getOrderNo()
                            + "】扣除积分【" + db2bOrder.getTotalPayPoint() + "】";
                    db2bPointLog.setLogDesc(logDesc);
                    db2bPointLogBizService.consumePoint(db2bPointLog);
                }
                //金额换积分
                Double awardPoinDouble = getAwardPoint(db2bOrder.getPartnerId(), Double.valueOf(db2bOrder.getTotalPrice()));
                if (awardPoinDouble == null) {
                    awardPoinDouble = 0D;
                }
                db2bPointLog.setPointValue(awardPoinDouble);//积分
                String logDesc = "订单【" + db2bOrder.getOrderNo() + "】奖励积分【" + awardPoinDouble + "】";
                db2bPointLog.setLogDesc(logDesc);
                db2bPointLogBizService.awardPoint(db2bPointLog);
            }
            //该经销商是否参与雪佛龙产品额外增加积分
            if (isPartnerPoints(db2bOrder.getPartnerId())) {
                //产品为雪佛龙的产品则额外给经销商积分
                getAwardProductPoint(db2bOrder);
            }
        }
        db2bOrderMapper.updateByPrimaryKeySelective(record);
        Map<String, Object> parMap = new HashMap<String, Object>();
        parMap.put("orderId", record.getId());
        return parMap;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delete(List<Long> ids) throws WxPltException {
        Db2bOrderExample example = new Db2bOrderExample();
        example.createCriteria().andIdIn(ids);
        db2bOrderMapper.deleteByExample(example);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteByExample(Db2bOrderExample example) throws WxPltException {
        db2bOrderMapper.deleteByExample(example);
    }

    @Override
    public List<Db2bOrder> queryByExample(Db2bOrderExample example) throws WxPltException {
        return db2bOrderMapper.selectByExample(example);
    }

    @Override
    public List<Db2bOrder> queryByParams(Map<String, Object> params) throws WxPltException {
        return db2bOrderMapper.queryByParams(params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateForEditPage(Db2bOrder record) throws WxPltException {
        record.setUpdateUserId(ContextUtil.getCurUserId());
        record.setUpdateTime(DateUtil.getCurrentDate());
        db2bOrderMapper.updateForEditPage(record);
    }

    @Override
    public Db2bOrder getBean(Long id) throws WxPltException {
        return db2bOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public void queryForPage(Db2bOrderParams params, Map<String, Object> resultMap) throws WxPltException {
        resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
        resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
    }

    @Override
    public List<Db2bOrder> queryForPage(Db2bOrderParams params) throws WxPltException {
        List<Db2bOrder> db2bOrders = db2bOrderMapper.queryForPage(params);
        if (!db2bOrders.isEmpty()) {
            for (Db2bOrder order : db2bOrders) {
                Map<String, Object> paramsMap = new HashMap<String, Object>();
                paramsMap.put("additionalFlag", 0);
                paramsMap.put("orderId", order.getId());
                List<Db2bOrderLine> db2bOrderLines = db2bOrderLineMapper.queryByParams(paramsMap);
                if (!db2bOrderLines.isEmpty()) {
                    order.setDb2bOrderLine(db2bOrderLines);
                    for (Db2bOrderLine db2bOrderLine : db2bOrderLines) {
                        paramsMap.put("additionalFlag", 1);
                        //查询促销产品
                        if (db2bOrderLine.getPromotionId() != null) {
                            Db2bPromotion db2bProductPromotion = db2bPromotionMapper.selectByPrimaryKey(db2bOrderLine.getPromotionId());
                            if (db2bProductPromotion != null) {
                                db2bOrderLine.setPromotionType(db2bProductPromotion.getPromotionType());
                            }
                        }
                        db2bOrderLine.setGiftDb2bProducts(db2bProductMapper.getOrderLineByParams(paramsMap));
                    }
                }
            }
        }
        return db2bOrders;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertShoppingCart(Db2bShoppingCar db2bShoppingCar) throws WxPltException {
        //根据商品Id,sourceType，userKey来判断是否存数据，如果存在更新否则新增
        Map<String, Object> params = new HashMap<String, Object>();
        if (Db2bConstants.PLATFORM_APP.equals(db2bShoppingCar.getPlatform())) {
            params.put("sourceType", db2bShoppingCar.getPlatform());
            params.put("userKey", String.valueOf(db2bShoppingCar.getWorkshopId()));
            db2bShoppingCar.setUserKey(String.valueOf(db2bShoppingCar.getWorkshopId()));
            db2bShoppingCar.setSourceType(Db2bConstants.PLATFORM_APP);
        } else {
            params.put("sourceType", WorkshopRelationship.B2B_PRODUCT_PROMOTION);
            params.put("userKey", db2bShoppingCar.getUserKey());
            db2bShoppingCar.setSourceType(WorkshopRelationship.B2B_PRODUCT_PROMOTION);
        }
        params.put("productId", db2bShoppingCar.getProductId());
        List<Db2bShoppingCar> db2bShoppingCars = db2bShoppingCarMapper.queryByParams(params);
        if (!db2bShoppingCars.isEmpty()) {
            db2bShoppingCar.setUpdateTime(DateUtil.getCurrentDate());
            db2bShoppingCar.setUpdateUserId(ContextUtil.getCurUserId());
            db2bShoppingCar.setId(db2bShoppingCars.get(0).getId());
            int amount = db2bShoppingCar.getAmount() + db2bShoppingCars.get(0).getAmount();
            db2bShoppingCar.setAmount(amount);
            db2bShoppingCarMapper.updateByPrimaryKeySelective(db2bShoppingCar);
        } else {
            db2bShoppingCar.setCreateUserId(ContextUtil.getCurUserId());
            db2bShoppingCar.setCreateTime(DateUtil.getCurrentDate());
            db2bShoppingCarMapper.insertSelective(db2bShoppingCar);
        }
    }

    @Override
    public List<Db2bShoppingCar> queryShopCartByParams(Db2bShoppingCar db2bShoppingCar) throws WxPltException {
        return queryNewShopCartByParams(db2bShoppingCar);
    }

    @Override
    public void updateProductFromCart(Db2bShoppingCar db2bShoppingCar) {
        //购物车产品价格根据促销情况而定
        db2bShoppingCarMapper.updateByPrimaryKeySelective(db2bShoppingCar);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void cancelOrders(Db2bOrder record) {
        try {
            //更新订单信息
            this.update(record);
            Db2bOrder order = db2bOrderMapper.selectByPrimaryKey(record.getId());
            if (order != null && StringUtils.isNotBlank(order.getOrderNo())) {
                //根据订单号查询采购订单信息
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("orderNo", order.getOrderNo());
                List<Long> idList = db2bOrderMapper.getSalesOrderIdByParams(params);
                String ids = "";
                if (idList != null) {
                    ids = StringUtils.join(idList.toArray(), ",");
                }
                //更改采购订单状态
                if (StringUtils.isNotBlank(ids)) {
                    salesOrderService.cancelOrdersAndOrderLinesByOrderIDs(ids);
                }
            }
        } catch (WxPltException e) {
            e.printStackTrace();
        }
    }

    /**
     * 金额换成奖励的积分
     *
     * @param partnerId  经销商ID
     * @param totalPrice 总价
     * @return 积分
     * @throws WxPltException 业务异常
     */
    public Double getAwardPoint(Long partnerId, Double totalPrice) throws WxPltException {
        Double awardTotalPointDouble = null;
        //根据经销商设置获取积分兑换规则，生成该订单积分数量
        Db2bDistSetting db2bDistSetting = db2bDistSettingBizService.getBeanByPartnerId(partnerId);
        //1代表允许使用积分兑换规则，0表示不使用积分规则
        if (db2bDistSetting.getOrderPointSwitch() == 1) {
            //换算成积分数
            if (db2bDistSetting.getAwardPointRate() != null) {
                awardTotalPointDouble = totalPrice * db2bDistSetting.getAwardPointRate();
                awardTotalPointDouble = (new BigDecimal(awardTotalPointDouble).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }
        return awardTotalPointDouble;
    }

    public WxTProperties getProductVersion() {
        Map<String, Object> reqMap = new HashMap<String, Object>();
        reqMap.put("codetype", "product_version");
        return wxTPropertiesMapper.selectByMap(reqMap);
    }

    public Map<String, Object> getDb2bOrderLinesByOrderId(Long id) {
        //查询订单明细
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Db2bOrder db2bOrder = new Db2bOrder();
        db2bOrder.setId(id);
        try {
            db2bOrder = db2bOrderMapper.queryDb2bOrderById(id);
            getOrderVo(db2bOrder);
        } catch (Exception e) {
            e.printStackTrace();
        }
        resultMap.put("orderVo", db2bOrder);
        return resultMap;
    }

    @Override
    public Long getPartnerIdByWorkshopId(Map<String, Object> parmas) throws WxPltException {
        List<WorkshopPartnerVo> workshopPartnerVos = workshopPartnerVoMapper.getSelectedPartnerByWorkshopId(parmas);
        if (!workshopPartnerVos.isEmpty()) {
            return workshopPartnerVos.get(0).getPartnerId();
        } else {
            throw new WxPltException("不存在该经销商");
        }
    }

    public void createOutStock(Db2bOrder order, List<Db2bOrderLine> orderLine) throws Exception {
        //转化，字段一一对应
        OrderVo orderVo = new OrderVo();
        List<Inventory> inventoryList = new ArrayList<Inventory>();
        Long workshopId = order.getWorkshopId();
        String address = order.getReceiveAddress();
        String receiveUserName = order.getReceivePerson();
        String receivePhoneNo = order.getReceivePersonTel();
        Long orderId = null;
        String orderSource;
        String orderSourceId;
        orderVo.setOrderNo(order.getOrderNo());//订单编号
        if (Db2bConstants.PLATFORM_APP.equals(order.getPlatform())) {
            orderSource = OrderVo.DSR_B2B_SOURCE;
            orderSourceId = OrderVo.DSR_B2B_SOURCE_ID;
        } else {
            orderSource = OrderVo.WX_B2B_SOURCE;
            orderSourceId = OrderVo.WX_B2B_SOURCE_ID;
        }
        orderSource = orderSource + "-" + order.getOrderNo();

        //订单明细
        for (Db2bOrderLine orderLine2 : orderLine) {
            Db2bProduct db2bProduct = db2bProductMapper.selectByPrimaryKey(orderLine2.getProductId());
            Inventory inventory = new Inventory();
            //只有雪佛龙的产品才可以生成出库单
            if (db2bProduct.getProductType() == 0) {
                inventory.setSku(db2bProduct.getSku());
                inventory.setName(db2bProduct.getProductName());
                inventory.setPrice(orderLine2.getPrice());
                inventory.setQuantity(orderLine2.getAmount());
                inventory.setSalePrice(orderLine2.getPrice());
                inventory.setBrand(db2bProduct.getProductBrand());
                inventory.setIconId(db2bProduct.getProductPhoto());
                inventory.setReplenishQuantity(orderLine2.getAmount());
                inventoryList.add(inventory);
            }
        }
        if (inventoryList.size() > 0) {
            orderBizService.createOrderAndOrderLine(inventoryList, workshopId, orderId, orderSource, orderSourceId, address, receiveUserName, receivePhoneNo, OrderVo.ORDER_STATUS_INSERT);
        }
    }

    public void getAwardProductPoint(Db2bOrder db2bOrder) {
        //查询订单产品明细
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("orderId", db2bOrder.getId());
        List<Db2bOrderLine> orderLineVos = db2bOrderLineMapper.queryByParams(params);
        for (Db2bOrderLine orderLine : orderLineVos) {
            //雪佛龙产品
            if (orderLine.getProductType() == 0) {
                //查询改产品是否参加了产品积分规则
                params.put("sku", orderLine.getSku());
                List<Db2bProductPoints> productPoints = db2bProductPointsMapper.queryByParams(params);
                if (!productPoints.isEmpty()) {
                    //如果该经销商设定的价格需要和标准的雪佛龙价格做对比，经销商设定的价格小于标准价格，则不产生积分，否则产生积分
                    double productPrice = orderLine.getProductPrice() == null ? 0D : orderLine.getProductPrice();
                    double productStandPrice = productPoints.get(0).getPrice() == null ? 0D : productPoints.get(0).getPrice();
                    if (db2bProductService.isEcFlag(db2bOrder.getWorkshopId()) || isChevronPriceSwitch(db2bOrder.getPartnerId()) || (!isChevronPriceSwitch(db2bOrder.getPartnerId()) && (productPrice >= productStandPrice))) {
                        Double pointsDouble = productPoints.get(0).getPointValue();
                        Db2bPointLog db2bPointLog = new Db2bPointLog();
                        db2bPointLog.setProductPointId(productPoints.get(0).getId());
                        db2bPointLog.setModuleName(Db2bPointLog.MODULE_NAME_DB2B);
                        db2bPointLog.setChargeType(Db2bPointLog.CHARGE_TYPE_DIST);
                        db2bPointLog.setChargeKey(String.valueOf(db2bOrder.getPartnerId()));//经销商id
                        db2bPointLog.setOwnerType(Db2bPointLog.OWNER_TYPE_WORKSHOP);
                        db2bPointLog.setOwnerKey(String.valueOf(db2bOrder.getWorkshopId()));//门店id
                        String sourceType = Db2bPointLog.SOURCE_TYPE;
                        if (Db2bConstants.PLATFORM_APP.equals(db2bOrder.getPlatform())) {
                            sourceType = Db2bConstants.PLATFORM_APP;
                        } else if (Db2bConstants.PUBLIC_NAME.equals(db2bOrder.getPlatForm())) {
                            sourceType = Db2bConstants.PUBLIC_NAME;
                        } else if (Db2bConstants.MINI_PROGRAM_NAME.equals(db2bOrder.getPlatform())) {
                            sourceType = Db2bConstants.MINI_PROGRAM_NAME;
                        }
                        db2bPointLog.setSourceType(sourceType);
                        db2bPointLog.setSourceKey(db2bOrder.getOrderNo());
                        db2bPointLog.setPointValue(pointsDouble);//积分
                        String logDesc = "订单【" + db2bOrder.getOrderNo() + "】奖励积分【" + pointsDouble + "】";
                        db2bPointLog.setLogDesc(logDesc);
                        if (pointsDouble != null && pointsDouble > 0D) {
                            try {
                                db2bPointLogBizService.awardPoint(db2bPointLog);
                            } catch (WxPltException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
        }
    }

    public boolean isPartnerPoints(Long partnerId) {
        Db2bDistSettingExample example = new Db2bDistSettingExample();
        example.createCriteria().andPartnerIdEqualTo(partnerId).andDeleteFlagEqualTo(0L);
        List<Db2bDistSetting> db2bDistSettings = db2bDistSettingMapper.selectByExample(example);
        if (!db2bDistSettings.isEmpty()) {
            return db2bDistSettings.get(0).getExtSwitch() != null && db2bDistSettings.get(0).getExtSwitch() == 1;
        }
        return false;
    }

    public boolean isChevronPriceSwitch(Long partnerId) {
        Db2bDistSettingExample example = new Db2bDistSettingExample();
        example.createCriteria().andPartnerIdEqualTo(partnerId).andDeleteFlagEqualTo(0L);
        List<Db2bDistSetting> db2bDistSettings = db2bDistSettingMapper.selectByExample(example);
        if (!db2bDistSettings.isEmpty()) {
            return db2bDistSettings.get(0).getChevronPriceSwitch() == 1;
        }
        return false;
    }

    public void getAppleteConfig(Map<String, Object> params) {
        Long partnerId = (Long) params.get("partnerId");
        String appleteName = (String) params.get("appleteName");
        Db2bDistAppleteConfigExample example = new Db2bDistAppleteConfigExample();
        example.createCriteria().andPartnerIdEqualTo(partnerId).andAppletNameEqualTo(appleteName);
        List<Db2bDistAppleteConfig> db2bDistSettings = db2bDistAppleteConfigMapper.selectByExample(example);
        params.put("appId", "");
        params.put("apiKey", "");
        params.put("mchId", "");
        params.put("appletType", "");
        if (!db2bDistSettings.isEmpty()) {
            Db2bDistAppleteConfig db2bDistAppleteConfig = db2bDistSettings.get(0);
            params.put("appId", db2bDistAppleteConfig.getAppId());
            params.put("apiKey", db2bDistAppleteConfig.getApiKey());
            params.put("mchId", db2bDistAppleteConfig.getMchId());
            params.put("appletType", db2bDistAppleteConfig.getAppletType());
        }
    }

    public Map<String, Object> getWechatPaymentConfig(Db2bOrder recordParams, boolean flag) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //为了保证生成时的订单信息和再次请求的信息一致，重新查订单信息
        Db2bOrder record = db2bOrderMapper.selectByPrimaryKey(recordParams.getId());
        record.setPlatform(recordParams.getPlatform());//数据来源
        record.setAppId(recordParams.getAppId());// 关联账户名称
        record.setPayType(recordParams.getPayType());//支付类型
        try {
            if (Db2bConstants.WECHAT_PAY.equals(record.getPayType()) && StringUtils.isNotEmpty(record.getAppId())) {
                WorkshopEmployeeExample example = new WorkshopEmployeeExample();
                example.createCriteria().andMobileEqualTo(record.getTechnicianMobile()).andEnableFlagEqualTo(1);
                List<WorkshopEmployee> employees = workshopEmployeeMapper.selectByExample(example);
                if (!employees.isEmpty()) {
                    record.setOpenId(employees.get(0).getB2bOpenId());
                }
                //订单提交之后，调用微信的接口获取微信相关信息
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("orderInfos", record);
                params.put("partnerId", record.getPartnerId());
                params.put("appleteName", record.getAppId());
                getAppleteConfig(params);
                if (Db2bConstants.PUBLIC_NAME.equals(record.getPlatform())) {
                    //先判断订单是否失效，失效时间为两个小时,未失效的话，重新获取支付信息，否则，重新生成支付信息
                    resultMap = HttpClientUtils.wxPay(params);

                } else if (Db2bConstants.MINI_PROGRAM_NAME.equals(record.getPlatform())) {
                    resultMap = params;
                }
            }
        } catch (AesException e) {
            e.printStackTrace();
        }

        return resultMap;
    }


    @Override
    public boolean isHasWorkShop(Map<String, Object> paramsHashMap) throws WxPltException {
        boolean isHasWorkShop = true;
        List<Db2bActivity> db2bPromotionActivities = db2bActivityBizService.queryByParams(paramsHashMap);
        if (!db2bPromotionActivities.isEmpty()) {
            paramsHashMap.put("activityId", db2bPromotionActivities.get(0).getId());
            paramsHashMap.put("relationType", WorkshopRelationship.B2B_PRODUCT_PROMOTION);
            List<WorkshopRelationship> relations = workshopRelationship.queryByParams(paramsHashMap);
            isHasWorkShop = !relations.isEmpty();
        }
        return isHasWorkShop;
    }


    @Override
    public List<Db2bShoppingCar> createVirtualOrder(Db2bShoppingCar db2bShoppingCar) throws WxPltException {
        List<Db2bShoppingCar> shopCarsList = new ArrayList<Db2bShoppingCar>();
        shopCarsList.add(db2bShoppingCar);
        queryNewShopCartByParams(shopCarsList, getWorkShopPartnerId(db2bShoppingCar));
        return shopCarsList;
    }

    public List<Db2bShoppingCar> queryNewShopCartByParams(Db2bShoppingCar db2bShoppingCar) throws WxPltException {
        Map<String, Object> params = getWorkShopPartnerId(db2bShoppingCar);
        List<Db2bShoppingCar> shopCarsList = db2bShoppingCarMapper.queryByParams(params);
        queryNewShopCartByParams(shopCarsList, params);
        return shopCarsList;
    }

    public Map<String, Object> getWorkShopPartnerId(Db2bShoppingCar db2bShoppingCar) throws WxPltException {
        Map<String, Object> params = new HashMap<String, Object>();
        Long workShopId = null;
        Long partnerId = -1L;
        if (Db2bConstants.PLATFORM_APP.equals(db2bShoppingCar.getPlatform())) {
            params.put("userKey", db2bShoppingCar.getWorkshopId());
            params.put("sourceType", db2bShoppingCar.getPlatform());
            params.put("createUserId", ContextUtil.getCurUserId());
            workShopId = db2bShoppingCar.getWorkshopId();
        } else {
            params.put("userKey", db2bShoppingCar.getUserKey());
            params.put("sourceType", WorkshopRelationship.B2B_PRODUCT_PROMOTION);
            //获取门店信息根据手机号
            Map<String, Object> reqMap = new HashMap<String, Object>();
            reqMap.put("mobile", db2bShoppingCar.getUserKey());
            reqMap.put("enableFlag", 1);
            List<WorkshopEmployee> list = workshopEmployeeMapper.selectByParams(reqMap);
            if (!list.isEmpty()) {
                workShopId = list.get(0).getWorkshopId();
                partnerId = list.get(0).getPartnerId();
            }
            if (workShopId == null) {
                throw new WxPltException("门店不存在");
            }
        }
        params.put("workShopId", workShopId);
        params.put("partnerId", partnerId);
        return params;
    }

    public List<Db2bShoppingCar> queryNewShopCartByParams(List<Db2bShoppingCar> shopCarsList, Map<String, Object> params) throws WxPltException {
        Long workShopId = Long.parseLong(params.get("workShopId").toString());
        Date nowDate = DateUtils.getCurrentDate();

        //购物车产品价格根据促销类型展示
        for (Db2bShoppingCar shopCars : shopCarsList) {
            Map<String, Object> paraMap = new HashMap<String, Object>();
            //获取产品信息
            Db2bProduct db2bProduct = db2bProductMapper.selectByPrimaryKey(shopCars.getProductId());
            if (db2bProduct == null) {
                continue;
            }
            shopCars.setDb2bProduct(db2bProduct);
            paraMap.put("startDate", nowDate);
            paraMap.put("endDate", nowDate);
            paraMap.put("productId", shopCars.getProductId());
            paraMap.put("workShopId", workShopId);
            paraMap.put("relationType", WorkshopRelationship.B2B_PRODUCT_PROMOTION);
            paraMap.put("partnerId", params.get("partnerId"));
            OrderPromotionResult result = db2bActivityBizService.getPromotionsByCar(shopCars, paraMap);
            shopCars.setHasPromotion(result.hasA2B() || result.hasDiscount());
            List<Db2bProduct> promotionList = new ArrayList<Db2bProduct>();
            if (result.hasA2B()) {
                List<Db2bPromotion> a2b = result.getA2b();
                Db2bPromotion promotion = a2b.get(0);
                shopCars.setPromotionType(promotion.getPromotionType());
                BuyGiftsConfig configResult = (BuyGiftsConfig) promotion.getConfigResult();
                //查询购物车的赠品信息
                if (shopCars.getId() != null) {
                    Map<String, Object> hashMap = new HashMap<String, Object>();
                    hashMap.put("shopCarId", shopCars.getId());
                    promotionList = db2bProductMapper.getGiftInfosByParams(hashMap);
                }
                boolean rule = configResult.rule(shopCars.getAmount());
                if (rule) {
                    shopCars.setAttendPromotion(true);
                    shopCars.setBuyGiveActivityName(configResult.getName());
                } else {
                    shopCars.setProductPrice(db2bProduct.getProductPrice());
                }
            }
            if (result.hasDiscount()) {
                List<Db2bPromotion> discounts = result.getDiscount();
                for (Db2bPromotion discount : discounts) {
                    DiscountConfig configResult = (DiscountConfig) discount.getConfigResult();
                    boolean rule = configResult.rule(shopCars.getAmount());
                    if (rule) {
                        shopCars.setPromotionType(discount.getPromotionType());
                        shopCars.setAttendPromotion(true);
                        shopCars.setProductPrice(configResult.getPrice());
                        break;
                    }
                }
            }
            if (!shopCars.isHasPromotion()) {
                //没有促销活动
                shopCars.setProductPrice(db2bProduct.getProductPrice());
                shopCars.setProductName(db2bProduct.getProductName());
            }
            //赠品信息
            shopCars.setGiftDb2bProducts(promotionList);
        }
        return shopCarsList;
    }

    public boolean checkPromotionInfos(Db2bOrder record) throws WxPltException {
        Date nowDate = DateUtil.getCurrentDate();
        record.setDeleteFlag(0);
        record.setCreateUserId(ContextUtil.getCurUserId());
        record.setCreateTime(nowDate);
        //获取门店信息根据手机号
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        record.setPlatForm(record.getPlatform());
        if (Db2bConstants.PLATFORM_APP.equals(record.getPlatform())) {
            paramsMap.put("workshopId", record.getWorkshopId());
            record.setPartnerId(getPartnerIdByWorkshopId(paramsMap));
            record.setTechnicianName(ContextUtil.getUsername());//dsr销售Cai
            record.setTechnicianMobile(ContextUtil.getCurUser().getMobileTel());//dsr销售手机号
        } else {
            paramsMap.put("mobile", record.getTechnicianMobile());
            paramsMap.put("enableFlag", 1);
            List<WorkshopEmployee> list = workshopEmployeeMapper.selectByParams(paramsMap);
            if (!list.isEmpty()) {
                record.setWorkshopId(list.get(0).getWorkshopId());
                record.setTechnicianName(list.get(0).getName());
                record.setPartnerId(list.get(0).getPartnerId());
            } else {
                throw new WxPltException("门店不存在");
            }
        }

        HashMap<String, Object> paramsHashMap = new HashMap<String, Object>();
        paramsHashMap.put("startDate", nowDate);
        paramsHashMap.put("endDate", nowDate);
        paramsHashMap.put("enableFlag", 1);
        paramsHashMap.put("partnerId", record.getPartnerId());
        boolean isHasWorkShop = isHasWorkShop(paramsHashMap);
        if (!record.getDb2bShoppingCars().isEmpty()) {
            for (Db2bShoppingCar shoppingCar : record.getDb2bShoppingCars()) {
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("partnerId", record.getPartnerId());
                params.put("productId", shoppingCar.getProductId());
                params.put("workShopId", record.getWorkshopId());
                params.put("date", nowDate);
                params.put("isHasWorkShop", isHasWorkShop);
                params.put("relationType", WorkshopRelationship.B2B_PRODUCT_PROMOTION);
                List<Db2bPromotion> dnDb2bProductPromotions = db2bActivityBizService.queryByMap(params);
                Db2bPromotion productPromotion;
                if (!dnDb2bProductPromotions.isEmpty()) {
                    productPromotion = dnDb2bProductPromotions.get(0);
                    if (PromotionType.BUY_A_GIVEAWAY_B.getType().equals(productPromotion.getPromotionType())) {
                        //买A组赠B组数据
                        String config = productPromotion.getConfig();
                        BuyGiftsConfig configByType = (BuyGiftsConfig) PromotionType.getConfigByType(productPromotion.getPromotionType(), config);
                        if (configByType == null) {
                            throw new WxPltException("买赠配置信息丢失");
                        }
                        int rewardAmount = configByType.getProductNum();
                        int giftAmounts = configByType.getGiftNum();
                        if (shoppingCar.getAmount() >= rewardAmount) {
                            //查询购物车赠品信息
                            Map<String, Object> hashMap = new HashMap<String, Object>();
                            hashMap.put("shopCarId", shoppingCar.getId());
                            List<Db2bProduct> giftProductList = db2bProductMapper.getGiftInfosByParams(hashMap);
                            int shopCartTotalGiftAmount = 0;
                            if (giftProductList != null && !giftProductList.isEmpty()) {
                                for (Db2bProduct db2bProduct2 : giftProductList) {
                                    shopCartTotalGiftAmount += db2bProduct2.getAmount();
                                }
                            }
                            int totalGiftAmount = (shoppingCar.getAmount() / rewardAmount) * giftAmounts;
                            if (shopCartTotalGiftAmount > totalGiftAmount) {
                                return false;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public List<Db2bProduct> getBuyAgroupGiftBgroupList(Map<String, Object> paramsMap) {
        List<Db2bBuyAgroupToBgroup> buyAgroupGiftBgroup = db2bBuyAgroupToBgroupMapper.queryByParams(paramsMap);
        List<Db2bProduct> db2bProducts = new ArrayList<Db2bProduct>();
        if (buyAgroupGiftBgroup != null && !buyAgroupGiftBgroup.isEmpty()) {
            for (Db2bBuyAgroupToBgroup db2b : buyAgroupGiftBgroup) {
                Db2bProduct db2bProduct = new Db2bProduct();
                db2bProduct.setId(db2b.getRelationProductId());
                db2bProduct.setProductPhoto(db2b.getProductPhoto());
                db2bProduct.setProductHdPhoto(db2b.getProductHdPhoto());
                db2bProduct.setProductName(db2b.getGiftProductName());
                db2bProduct.setStatus(db2b.getStatus());
                db2bProduct.setProductDesc(db2b.getProductDesc());
                db2bProducts.add(db2bProduct);
            }
        }
        return db2bProducts;
    }

    @Override
    public Db2bOrder preBuildOrder(Db2bOrder record) throws WxPltException {
        Db2bShoppingCar shoppingCar = new Db2bShoppingCar();
        shoppingCar.setPlatform(record.getPlatform());//平台
        shoppingCar.setWorkshopId(record.getWorkshopId());
        shoppingCar.setUserKey(record.getTechnicianMobile());
        Map<String, Object> params = getWorkShopPartnerId(shoppingCar);
        if (record.getDb2bShoppingCars() != null && record.getDb2bShoppingCars().size() > 0) {
            getDb2bShoppingCars(record, params);
        }
        return record;
    }


    //组装订单级促销信息
    public Db2bOrder getDb2bShoppingCars(Db2bOrder record, Map<String, Object> params) throws WxPltException {
        boolean isHasPromotion = false;//产品是否有促销
        List<OrderPromotionResult> list = new ArrayList<OrderPromotionResult>();
        Map<Long, Db2bShoppingCar> map = new HashMap<Long, Db2bShoppingCar>();
        for (Db2bShoppingCar shopping : record.getDb2bShoppingCars()) {
            Db2bProduct db2bProduct = db2bProductMapper.selectByPrimaryKey(shopping.getProductId());
            shopping.setDb2bProduct(db2bProduct);
            //查询该产品促销信息
            shopping.setProductPrice(db2bProduct.getProductPrice());
            OrderPromotionResult result = db2bActivityBizService.getPromotionsByCar(shopping, params);
            isHasPromotion = result.hasA2B();//产品是否有买赠促销
            list.add(result);
            map.put(shopping.getProductId(), shopping);
        }
        Map<Long, Db2bPromotion> discountMap = new HashMap<Long, Db2bPromotion>();
        List<Db2bOrderPromotion> orderPromotion = handle(list, map, discountMap);
        record.setDb2bOrderPromotion(orderPromotion);
        if (isHasPromotion && orderPromotion.isEmpty()) {
            //整个订单产品有参与促销，但是又全部不满足促销条件则提示
            record.setHasPromotionTips(true);
        }
        List<Db2bShoppingCar> carList = new ArrayList<Db2bShoppingCar>();
        BigDecimal totalPrice = new BigDecimal(0);
        for (Long aLong : map.keySet()) {
            Db2bShoppingCar db2bShoppingCar = map.get(aLong);
            Double productPrice = db2bShoppingCar.getProductPrice();
            BigDecimal multiply = new BigDecimal(db2bShoppingCar.getAmount())
                    .multiply(BigDecimal.valueOf(productPrice));
            totalPrice = totalPrice.add(multiply);
            carList.add(db2bShoppingCar);
            // 20201225周慧君要求，如果产品参与了折扣活动，不再给出提示
            if (discountMap.containsKey(aLong)){
                record.setHasPromotionTips(false);
            }
        }
        record.setDb2bShoppingCars(carList);
        record.setTotalPrice(String.valueOf(totalPrice.doubleValue()));
        // 当前所有的满减
        List<Db2bPromotion> orderPromotions = new ArrayList<Db2bPromotion>();
        List<Db2bPromotion> allFulls = db2bActivityBizService.getAllFulls((Long) params.get("workShopId"));
        if (allFulls != null && !allFulls.isEmpty()) {
        	Set<Db2bPromotion> sortedPromotions = new TreeSet<Db2bPromotion>(new Comparator<Db2bPromotion>() {

				@Override
				public int compare(Db2bPromotion arg0, Db2bPromotion arg1) {
					return ((FullReductionConfig) arg1.getConfigResult()).getTotalPrice()
							.compareTo(((FullReductionConfig) arg0.getConfigResult()).getTotalPrice());
				}
			});
        	sortedPromotions.addAll(allFulls);
            for (Db2bPromotion allFull : sortedPromotions) {
                FullReductionConfig configResult = (FullReductionConfig) allFull.getConfigResult();
                Double totalPrice1 = configResult.getTotalPrice();
                if (totalPrice.doubleValue() >= totalPrice1) {
                    totalPrice = totalPrice.subtract(BigDecimal.valueOf(configResult.getMinusPrice()));
                    record.setTotalPayMoney(totalPrice.doubleValue());
                    orderPromotions.add(allFull);
                    break;
                }
            }
        }
        record.setOrderPromotion(orderPromotions);
        return record;
    }

    /**
     * 购物车促销和折扣处理
     *
     * @param list        所有产品和参与的所有活动列表
     * @param map         购物车
     * @param discountMap 产品参与得折扣活动
     * @return 参与的促销列表
     */
    private List<Db2bOrderPromotion> handle(List<OrderPromotionResult> list,
                                            Map<Long, Db2bShoppingCar> map,
                                            Map<Long, Db2bPromotion> discountMap) {
        // 参与买赠活动的商品 key：买赠活动 ID，value:参与该活动的购物车商品列表
        Map<Long, List<Db2bShoppingCar>> a2bs = new HashMap<Long, List<Db2bShoppingCar>>();
        // 买赠活动暂存
        Map<Long, Db2bPromotion> buyGiftConfigs = new HashMap<Long, Db2bPromotion>();
        // 商品价格，用于折扣被去除后的价格还原
        Map<Long, Double> priceMap = new HashMap<Long, Double>();
        handlePromotion(list, map, priceMap, discountMap, a2bs, buyGiftConfigs);
        // 处理参与了买赠活动的产品,并且获取活动详情
        return getHandleResult(a2bs, buyGiftConfigs, discountMap, priceMap, list);
    }

    /**
     * 处理参与了买赠活动的产品，获取活动详情，并且设置产品实际参与的活动
     *
     * @param a2bs           key为买赠活动ID，value为参与该活动的购物车商品列表
     * @param buyGiftConfigs 所有买赠活动，用于获取活动配置
     * @param discountMap    key为产品ID，value为产品参与的折扣活动，用于同产品活动优先级比较
     * @param priceMap       key为产品ID，value为产品价格，用于可能存在的产品价格还原。
     * @param list           产品列表参与的活动，用于设置订单中产品真正参与了的活动realDiscount，realA2B
     * @return 活动详情列表
     */
    private List<Db2bOrderPromotion> getHandleResult(Map<Long, List<Db2bShoppingCar>> a2bs,
                                                     Map<Long, Db2bPromotion> buyGiftConfigs,
                                                     Map<Long, Db2bPromotion> discountMap,
                                                     Map<Long, Double> priceMap,
                                                     List<OrderPromotionResult> list) {
        List<Db2bOrderPromotion> orderPromotion = new ArrayList<Db2bOrderPromotion>();
        // 处理参与了买赠活动的产品,并且获取活动详情
        for (Long id : a2bs.keySet()) {
            Db2bOrderPromotion orderP = new Db2bOrderPromotion();
            Db2bPromotion promotion = buyGiftConfigs.get(id);
            List<Db2bShoppingCar> shoppingCars = a2bs.get(id);
            BuyGiftsConfig configResult = (BuyGiftsConfig) promotion.getConfigResult();
            int totalAmount = 0;
            for (Db2bShoppingCar shoppingCar : shoppingCars) {
                Long productId = shoppingCar.getProductId();
                Db2bPromotion promotion1 = discountMap.get(productId);
                // 如果已经参与过折扣的产品并且折扣的优先级比买赠高，不在参与促销,
                if (promotion1 != null) {
                    if (promotion1.getActivityLevel() > promotion.getActivityLevel()) {
                        continue;
                    }
                }
                Integer amount = shoppingCar.getAmount();
                totalAmount = totalAmount + amount;
            }
            if (configResult.rule(totalAmount)) {
                for (Db2bShoppingCar shoppingCar : shoppingCars) {
                    Long productId = shoppingCar.getProductId();
                    Db2bPromotion promotion1 = discountMap.get(productId);
                    if (promotion1 != null) {
                        if (promotion1.getActivityLevel() < promotion.getActivityLevel()) {
                            // 价格还原，如果参与了促销，并且促销的优 先级比折扣高，这时候需要进行价格还原
                            shoppingCar.setProductPrice(priceMap.get(productId));
                            // 从折扣活动map中去除
                            discountMap.remove(productId);
                            for (OrderPromotionResult result : list) {
                                if (result.getProductId().equals(productId)) {
                                    result.setRealDiscount(null);
                                }
                            }
                        }
                    }
                    // 对真正参与买赠活动得进行处理
                    for (OrderPromotionResult result : list) {
                        if (result.getProductId().equals(productId)) {
                            List<Db2bPromotion> realA2B = result.getRealA2B();
                            if (CollectionUtils.isEmpty(realA2B)) {
                                result.setRealA2B(Lists.newArrayList(promotion));
                            } else {
                                result.getRealA2B().add(promotion);
                            }
                        }
                    }
                }
                int quantity = totalAmount / configResult.getProductNum();
                orderP.setBuyGiveActivityName(configResult.getName());
                orderP.setPromotionId(promotion.getId());
                orderP.setQuantity(quantity);
                orderP.setActivityLevel(promotion.getActivityLevel());
                orderP.setExtProperty1(promotion.getConfig());
                orderP.setExtProperty2(String.valueOf(PromotionType.BUY_A_GIVEAWAY_B.getType()));
                orderPromotion.add(orderP);
            }
        }
        return orderPromotion;
    }

    /**
     * 处理产品参与的活动，购物车中产品参与的活动
     *
     * @param list           产品参与的活动列表
     * @param map            产品ID-购物车关系
     * @param priceMap       产品价格暂存，用于折扣后参与买赠的价格回滚
     * @param discountMap    key为产品ID，value为活动，产品参与的折扣
     * @param a2bs           key为活动ID，参与买赠的购物车产品
     * @param buyGiftConfigs key为活动ID，value为活动，所有的买赠活动存储，用于获取活动配置
     */
    private void handlePromotion(List<OrderPromotionResult> list,
                                 Map<Long, Db2bShoppingCar> map,
                                 Map<Long, Double> priceMap,
                                 Map<Long, Db2bPromotion> discountMap,
                                 Map<Long, List<Db2bShoppingCar>> a2bs,
                                 Map<Long, Db2bPromotion> buyGiftConfigs) {
        for (OrderPromotionResult result : list) {
            Long productId = result.getProductId();
            Db2bShoppingCar db2bShoppingCar = map.get(productId);
            Integer amount = db2bShoppingCar.getAmount();
            if (result.hasDiscount()) {
                List<Db2bPromotion> discounts = result.getDiscount();
                for (Db2bPromotion discount : discounts) {
                    DiscountConfig discountConfig = (DiscountConfig) discount.getConfigResult();
                    boolean rule = discountConfig.rule(amount);
                    if (rule) {
                        Double productPrice = db2bShoppingCar.getProductPrice();
                        priceMap.put(productId, productPrice);
                        db2bShoppingCar.setProductPrice(discountConfig.getPrice());
                        map.put(productId, db2bShoppingCar);
                        discountMap.put(productId, discount);
                        result.setRealDiscount(discount);
                        break;
                    }
                }
            }
            if (result.hasA2B()) {
                List<Db2bPromotion> a2b = result.getA2b();
                for (Db2bPromotion promotion : a2b) {
                    Long id = promotion.getId();
                    Db2bShoppingCar shoppingCar = map.get(result.getProductId());
                    List<Db2bShoppingCar> cars = a2bs.get(id);
                    if (CollectionUtils.isEmpty(cars)) {
                        List<Db2bShoppingCar> shoppingCars = Lists.newArrayList(shoppingCar);
                        a2bs.put(id, shoppingCars);
                    } else {
                        cars.add(shoppingCar);
                        a2bs.put(id, cars);
                    }
                    buyGiftConfigs.put(id, promotion);
                }
            }
        }
    }

    /**
     * 购物车促销和折扣处理
     *
     * @param list 所有产品和参与的所有活动列表
     * @param map  购物车
     * @return 处理后参与促销和折扣得情况
     */
    private List<OrderPromotionResult> process(List<OrderPromotionResult> list, Map<Long, Db2bShoppingCar> map) {
        handle(list, map, new HashMap<Long, Db2bPromotion>());
        return list;
    }

    /**
     * 给订单产品打标记，是否参与了促销
     *
     * @param order 订单信息
     */
    public List<PromotionBgroupProduct> promotionMarking(Db2bOrder order) {
        List<Db2bOrderPromotion> db2bOrderPromotions = new ArrayList<Db2bOrderPromotion>();
        List<PromotionBgroupProduct> promotionBgroupProducts = new ArrayList<PromotionBgroupProduct>();
        if (order.getDb2bOrderPromotion() != null && order.getDb2bOrderPromotion().size() > 0) {
            db2bOrderPromotions = order.getDb2bOrderPromotion();
        }
        for (Db2bOrderPromotion orderPromotion : db2bOrderPromotions) {
            List<PromotionBgroupProduct> promotionBgroupProductList = orderPromotion.getPromotionBgroupProductList();
            if (promotionBgroupProductList != null && promotionBgroupProductList.size() > 0) {
                for (PromotionBgroupProduct bgroupProduct : promotionBgroupProductList) {
                    bgroupProduct.setPromotionId(orderPromotion.getPromotionId());
                    promotionBgroupProducts.add(bgroupProduct);
                }
            }
        }
        return promotionBgroupProducts;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteShoppingCarByIds(List<Long> ids) throws WxPltException {
        Db2bShoppingCarExample example = new Db2bShoppingCarExample();
        example.createCriteria().andIdIn(ids);
        db2bShoppingCarMapper.deleteByExample(example);
    }


    @Override
    public List<Db2bOrder> queryAppForPage(Db2bOrderParams params) {
        params.setAdditionalFlag(0);
        return db2bOrderMapper.queryAppForPage(params);
    }

    public void getOrderVo(Db2bOrder order) {
        List<Db2bOrderLine> orderProductList = new ArrayList<Db2bOrderLine>();//订单产品List
        List<Db2bOrderLine> orderGiftList = new ArrayList<Db2bOrderLine>();//订单赠品List
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId", order.getId());
        List<Db2bOrderLine> db2bOrderLines = db2bOrderLineMapper.queryByParams(paramsMap);
        //获取订单级促销
        List<Db2bOrderPromotion> db2bOrderPromotion = db2bOrderPromotionMapper.queryByParams(paramsMap);
        if (db2bOrderLines != null && !db2bOrderLines.isEmpty()) {
            for (Db2bOrderLine orderLine : db2bOrderLines) {
                if (orderLine.getPromotionId() != null) {
                    Db2bPromotion db2bProductPromotion = db2bPromotionMapper.selectByPrimaryKey(orderLine.getPromotionId());
                    if (db2bProductPromotion != null) {
                        orderLine.setPromotionType(db2bProductPromotion.getPromotionType());
                        paramsMap.put("productId", orderLine.getProductCutId());
                    }
                }
                if (orderLine.getAdditionalFlag() == 0) {
                    if (orderLine.getPromotionType() != null &&
                            (BaseProductPromotion.BUY_A_GIFT_A_TYPE == orderLine.getPromotionType()
                                    || BaseProductPromotion.BUY_B_GIFT_B_TYPE == orderLine.getPromotionType())) {
                        paramsMap.put("promotionId", orderLine.getPromotionId());
                        paramsMap.put("additionalFlag", 1);
                        orderLine.setGiftDb2bProducts(db2bProductMapper.getOrderLineByParams(paramsMap));
                    }
                    orderProductList.add(orderLine);
                } else if (orderLine.getAdditionalFlag() == 1) {
                    orderGiftList.add(orderLine);
                }
            }
        }
        //订单级数据组合
        order.setDb2bOrderLine(orderProductList);
        order.setGiftDb2bOrderLines(orderGiftList);
        order.setDb2bOrderPromotion(db2bOrderPromotion);
    }


    @Override
    public Map<String, Object> getPcDb2bOrderLinesByOrderId(Long id) {
        //查询订单明细
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> params = new HashMap<String, Object>();
        List<Db2bOrderLine> db2bOrderLines = null;
        Db2bOrder db2bOrder = new Db2bOrder();
        db2bOrder.setId(id);
        params.put("orderId", id);
        //params.put("additionalFlag", 0);
        try {
            db2bOrder = db2bOrderMapper.queryDb2bOrderById(id);
            db2bOrderLines = db2bOrderLineMapper.queryByParams(params);
            db2bOrder.setDb2bOrderLine(db2bOrderLines);
        } catch (Exception e) {
            e.printStackTrace();
        }
        resultMap.put("orderVo", db2bOrder);
        return resultMap;
    }


    @Override
    public void updateOrderStatus(Db2bOrder record) {
        String source = record.getSource();
        if (StringUtils.isNotBlank(source) && source.contains("-")) {
            try {
                //根据订单号查询订单id
                String orderNo = source.substring(source.lastIndexOf("-") + 1);
                Db2bOrderExample example = new Db2bOrderExample();
                example.createCriteria().andOrderNoEqualTo(orderNo);
                List<Db2bOrder> order = db2bOrderMapper.selectByExample(example);
                for (Db2bOrder r : order) {
                    r.setStatus(Db2bConstants.ORDER_STATUS_2);
                    this.update(r);
                }
            } catch (WxPltException e) {
                e.printStackTrace();
            }
        }
    }

}



