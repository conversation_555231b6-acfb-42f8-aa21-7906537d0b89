select max(ods.create_time) from wx_t_promotion_delivery_detail pdd 
left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on ods.campaign_offer_detail_summary_id=pdd.ext_property9

truncate table wx_t_sell_in_promotion_delivery;
truncate table wx_t_promotion_delivery_detail;
truncate table wx_t_delivery_order_rel;

select * from wx_t_sell_in_promotion_delivery;
select * from wx_t_promotion_delivery_detail;
select * from wx_t_delivery_order_rel;

insert into wx_t_sell_in_promotion_delivery (sales_order, region,[distributor_id], sold_to_code,product_sku,post_year_month,
pack_units,quantity, liters, revenue_rmb, [base_trans_sell_in_id],[pricing_date])
select distinct bsi.[sales_reference_doc] sales_order, bso.region,bsi.[distributor_id],
		bsi.[customer_code] sold_to_code,bsi.[product_code_SAP] product_sku, bsi.[trans_time] post_year_month,
		di.dic_item_name pack_units,
		convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0)) quantity, 
		round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0) * bp.unit liters,
		bsi.revenue_rmb, cod.[base_trans_sell_in_id],bsi.[pricing_date]
		from [PP_MID].[cmt].[campaign_offer_detail] cod 
left join [PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi on cod.[base_trans_sell_in_id]=bsi.[base_trans_sell_in_id]
		left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] bso on bsi.org_hier_id=bso.org_hier_id
		left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
		left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
where not exists (select 1 from wx_t_sell_in_promotion_delivery pd where pd.base_trans_sell_in_id=cod.base_trans_sell_in_id)

insert into [wx_t_promotion_delivery_detail] ([promotion_id]
      ,[award_type]
      ,[source_type]
      ,[source_id]
      ,[award_quantity]
      ,[promotion_desc]
      ,[delivery_status]
      ,[ext_property1]
      ,[ext_property2]
      ,[ext_property3]
      ,[ext_property4]
	  ,[ext_property7]
	  ,[ext_property8]
      ,[ext_property9]
      ,[create_time]
      ,[offer_key])
select convert(bigint, [campaign_code]) [promotion_id],
 convert(int, rc.[ext_property1]) [award_type],
 'delivery' [source_type],
 -1 [source_id],
 ods.[value] [award_quantity],
 o.ext_property1 [promotion_desc],
 10 [delivery_status],
 rc.[reward_category_name] [ext_property1],
 rc.[ext_property2] [ext_property2],
 rc.[ext_property3] [ext_property3],
 rc.ext_property4 [ext_property4],
 ods.distributor_id [ext_property7],
 ods.product_channel [ext_property8],
 ods.[campaign_offer_detail_summary_id] [ext_property9],
 getdate() create_time,
 ods.offer_id offer_key
  FROM [PP_MID].[cmt].[campaign_offer_detail_summary] ods
  left join [PP_MID].[cmt].[offer] o on ods.offer_id=o.offer_id
  left join [PP_MID].[cmt].[campaign] c on o.campaign_id=c.campaign_id
  left join [PP_MID].[cmt].[reward_category] rc on rc.reward_category_id=o.reward_category_id

insert into wx_t_delivery_order_rel (delivery_detail_id, delivery_id)
select distinct pdd.id, pd.delivery_id from [wx_t_promotion_delivery_detail] pdd
left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
left join [PP_MID].[cmt].[campaign_offer_detail] cod on ods.offer_id=cod.offer_id and ods.distributor_id=cod.distributor_id
	and CONVERT(varchar(100), ods.create_time, 112)=CONVERT(varchar(100), cod.create_time, 112)
left join wx_t_sell_in_promotion_delivery pd on pd.base_trans_sell_in_id=cod.base_trans_sell_in_id



---------------------------------------
SELECT max(p.promotion_name) promotion_name, MAX(p.create_time) AS c_time, MAX(p.update_time) AS u_time
	, MAX(del.post_year_month) AS post_year_month, del.distributor_id
	, (
		SELECT DISTINCT cos.customer_name_cn
		FROM PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos
		WHERE cos.distributor_id = del.distributor_id
	) AS customer_name, del.product_sku
	, MAX(p1.name) AS product_name, SUM(del.quantity) AS quantity, MAX(del.pack_units) pack_units
	, SUM(del.liters) AS liters, SUM(del.revenue_rmb) AS revenue_rmb
	, del.region, t1.award_type, t1.source_type, SUM(t1.award_quantity) AS award_quantity
	, MAX(t1.promotion_desc) promotion_desc, t1.delivery_status AS deliveryStatus, MAX(t1.ext_property1) AS ext_property1, 
	max(t1.ext_property2) ext_property2, max(t1.ext_property3) ext_property3,t1.offer_key
FROM wx_t_sell_in_promotion_delivery del
LEFT JOIN wx_t_product p1 ON p1.sku = del.product_sku
	LEFT JOIN wx_t_delivery_order_rel ord ON del.delivery_id = ord.delivery_id
	LEFT JOIN wx_t_promotion_delivery_detail t1 ON t1.id = ord.delivery_detail_id
	LEFT JOIN wx_t_sell_in_promotion p ON p.id = t1.promotion_id
WHERE t1.id is not null 
GROUP BY del.distributor_id, del.product_sku, t1.award_type, p.id,
t1.source_type, del.region,  t1.delivery_status, t1.offer_key
order by p.id desc,del.distributor_id, t1.offer_key

truncate table [wx_t_promotion_gift_pool];
insert into [wx_t_promotion_gift_pool] ([distributor_id]
      ,[partner_id]
      ,[application_type]
      ,[available_quantity]
      ,[material_id]
      ,[material_code]
	  ,[ext_property1]
      ,[create_user_id]
      ,[create_time])
select distinct pe.[distributor_id]
      ,pe.[partner_id]
      ,(case when pdd.ext_property8='Commercial' then 'promotion' else 'cdm_promotion' end) [application_type]
      ,0 [available_quantity]
      ,m.id [ID]
      ,vtm.value_after_transform [material_code] 
	  ,pdd.ext_property8
	  ,1 [create_user_id]
      ,getdate() [create_time]
from wx_t_promotion_delivery_detail pdd
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
left join wx_t_value_transform_map vtm on vtm.transform_type='SellinPromotionMaterialMap.' + pdd.ext_property8
	and vtm.value_before_transform=pdd.ext_property2
left join wx_t_material m on m.material_code=vtm.value_after_transform
where pdd.award_type=1 and pdd.source_type='delivery' and pdd.delivery_status=10
and not exists (select 1 from [wx_t_promotion_gift_pool] pgp 
		where pgp.application_type=(case when pdd.ext_property8='Commercial' then 'promotion' else 'cdm_promotion' end)
		and pgp.[partner_id]=pe.partner_id and pgp.material_id=m.ID)
		
insert into [wx_t_promotion_gift_modify_log] ([pool_id]
      ,[modify_time]
      ,[modify_type]
      ,[modify_quantity]
      ,[business_type]
      ,[business_key]
      ,[comments]
      ,[ext_property2]
      ,[ext_property3]
      ,[create_user_id]
      ,[create_time])
select pgp.id [pool_id]
      ,getdate() [modify_time]
      ,'1' [modify_type]
      ,pdd.award_quantity [modify_quantity]
      ,'SELL_IN_PROMOTION_DELIVERY' [business_type]
      ,pdd.id [business_key]
      ,'下单促销奖励' [comments]
      ,sp.promotion_name [ext_property2]
      ,pdd.id [ext_property3]
      ,1 [create_user_id]
      ,getdate() [create_time]
from wx_t_promotion_delivery_detail pdd
left join wx_t_sell_in_promotion sp on sp.id=pdd.promotion_id
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7
left join wx_t_value_transform_map vtm on vtm.transform_type='SellinPromotionMaterialMap.' + pdd.ext_property8
	and vtm.value_before_transform=pdd.ext_property2
left join wx_t_material m on m.material_code=vtm.value_after_transform
left join [wx_t_promotion_gift_pool] pgp on pgp.partner_id=pe.partner_id
	and pgp.material_id=m.ID and pgp.ext_property1=pdd.ext_property8
where pdd.award_type=1 and pdd.source_type='delivery' and pdd.delivery_status=10
		


select *
--delete l
from wx_t_point_value_detail_log l 
where exists (select 1 from wx_t_point_value_detail d
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID  where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY'
 and l.POINT_VALUE_ID=d.id)

select *
--delete d
from wx_t_point_value_detail d 
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY'

select top 1000 *
--delete pb
 from dbo.wx_t_point_business pb where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY'

truncate table wx_t_promotion_gift_pool;
truncate table wx_t_promotion_gift_modify_log;
truncate table wx_t_pro_gift_pool_mod_log;

select * 
--update d set d.delivery_status=10
from wx_t_promotion_delivery_detail d

-----------------------------------------------------

select sum(d.POINT_VALUE)
from wx_t_point_value_detail d 
where exists (select 1 from dbo.wx_t_point_business pb
	left join wx_t_promotion_delivery_detail pdd on pb.RELATED_ID=convert(VARCHAR(100),pdd.id)
	where d.BUSINESS_ID = pb.ID and pdd.award_type=2 and pdd.source_type='delivery'
	and pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY') 

select sum(pdd.award_quantity)
from wx_t_promotion_delivery_detail pdd
where pdd.award_type=2 and pdd.source_type='delivery' --and pdd.delivery_status=10 	

select sum(l.modify_quantity) from wx_t_promotion_gift_modify_log l where l.business_type='SELL_IN_PROMOTION_DELIVERY'
select sum(pdd.award_quantity)
from wx_t_promotion_delivery_detail pdd
where pdd.award_type=1 and pdd.source_type='delivery'


insert into wx_t_pro_gift_pool_mod_log ([gift_pool_id]
      ,[value_before]
      ,[value_after]
      ,[value_change]
      ,[create_user_id]
      ,[create_time])
select pgp.id, pgp.available_quantity,pgp.available_quantity+x.modify_quantity,x.modify_quantity,1,getdate()
--update pgp set pgp.available_quantity=pgp.available_quantity+x.modify_quantity
from [wx_t_promotion_gift_pool] pgp join (
select l.pool_id , sum(l.modify_quantity) modify_quantity from [wx_t_promotion_gift_modify_log] l
left join wx_t_promotion_delivery_detail pdd on convert(nvarchar(100),pdd.id)=l.business_key
where l.business_type='SELL_IN_PROMOTION_DELIVERY' and pdd.award_type=1 and pdd.source_type='delivery'
group by l.pool_id ) x on pgp.id=x.pool_id

select p.available_quantity,x.s, p.available_quantity-x.s from wx_t_promotion_gift_pool p left join (
select pool_id, sum(case when modify_type='modifyTypeAdd' then modify_quantity else -modify_quantity end)  s
from wx_t_promotion_gift_modify_log l
group by pool_id) x on p.id=x.pool_id

select max(ods.create_time) from [wx_t_promotion_delivery_detail] pdd 
left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.[ext_property9]=ods.[campaign_offer_detail_summary_id]

insert into wx_t_sell_in_promotion_delivery (sales_order, region,[distributor_id], sold_to_code,product_sku,post_year_month,
pack_units,quantity, liters, revenue_rmb, [base_trans_sell_in_id],[pricing_date])
select distinct bsi.[sales_reference_doc] sales_order, bso.region,bsi.[distributor_id],
		bsi.[customer_code] sold_to_code,bsi.[product_code_SAP] product_sku, bsi.[trans_time] post_year_month,
		di.dic_item_name pack_units,
		convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0)) quantity, 
		round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0) * bp.unit liters,
		bsi.revenue_rmb, cod.[base_trans_sell_in_id],bsi.[pricing_date]
		from [PP_MID].[cmt].[campaign_offer_detail] cod 
left join [PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi on cod.[base_trans_sell_in_id]=bsi.[base_trans_sell_in_id]
		left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] bso on bsi.org_hier_id=bso.org_hier_id
		left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
		left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
where not exists (select 1 from wx_t_sell_in_promotion_delivery pd where pd.base_trans_sell_in_id=cod.base_trans_sell_in_id)

insert into [wx_t_promotion_delivery_detail] ([promotion_id]
      ,[award_type]
      ,[source_type]
      ,[source_id]
      ,[award_quantity]
      ,[promotion_desc]
      ,[delivery_status]
      ,[ext_property1]
      ,[ext_property2]
      ,[ext_property3]
      ,[ext_property4]
	  ,[ext_property7]
	  ,[ext_property8]
      ,[ext_property9]
      ,[create_time]
      ,[offer_key])
select convert(bigint, [campaign_code]) [promotion_id],
 convert(int, rc.[ext_property1]) [award_type],
 'delivery' [source_type],
 -1 [source_id],
 ods.[value] [award_quantity],
 o.ext_property1 [promotion_desc],
 10 [delivery_status],
 rc.[reward_category_name] [ext_property1],
 rc.[ext_property2] [ext_property2],
 rc.[ext_property3] [ext_property3],
 rc.ext_property4 [ext_property4],
 ods.distributor_id [ext_property7],
 ods.product_channel [ext_property8],
 ods.[campaign_offer_detail_summary_id] [ext_property9],
 getdate() create_time,
 ods.offer_id offer_key--,ods.create_time
  FROM [PP_MID].[cmt].[campaign_offer_detail_summary] ods
  left join [PP_MID].[cmt].[offer] o on ods.offer_id=o.offer_id
  left join [PP_MID].[cmt].[campaign] c on o.campaign_id=c.campaign_id
  left join [PP_MID].[cmt].[reward_category] rc on rc.reward_category_id=o.reward_category_id
  where ods.create_time>'2020-08-25 09:11:38.417'

insert into wx_t_delivery_order_rel (delivery_detail_id, delivery_id)
select distinct pdd.id, pd.delivery_id from [wx_t_promotion_delivery_detail] pdd
left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
left join [PP_MID].[cmt].[campaign_offer_detail] cod on ods.offer_id=cod.offer_id and ods.distributor_id=cod.distributor_id
	and CONVERT(varchar(100), ods.create_time, 112)=CONVERT(varchar(100), cod.create_time, 112)
left join wx_t_sell_in_promotion_delivery pd on pd.base_trans_sell_in_id=cod.base_trans_sell_in_id
where ods.create_time>'2020-08-25 09:11:38.417'




/****** Script for SelectTopNRows command from SSMS  ******/
SELECT TOP (1000) [campaign_id]
      ,[campaign_code]
      ,[campaign_name]
      ,[campaign_type_id]
      ,[effective_from_date]
      ,[effective_to_date]
      ,[post_year_month_from]
      ,[post_year_month_to]
      ,[create_by]
      ,[create_time]
      ,[update_by]
      ,[update_time]
      ,[active_flag]
      ,[priority]

	  --update c set c.post_year_month_from='2020-08-01', c.post_year_month_to='2020-12-01'
  FROM [PP_MID].[cmt].[campaign] c where c.campaign_id=45
  
  select * 
--update d set d.delivery_status=30
from wx_t_promotion_delivery_detail d where promotion_id=38 and d.delivery_status=10




select sum(d.POINT_VALUE)
from wx_t_point_value_detail d 
where exists (select 1 from dbo.wx_t_point_business pb
	left join wx_t_promotion_delivery_detail pdd on pb.RELATED_ID=convert(VARCHAR(100),pdd.id)
	where d.BUSINESS_ID = pb.ID and pdd.award_type=2 and pdd.source_type='delivery' and pdd.delivery_time='2020-09-02'
	and pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY') 

select sum(pdd.award_quantity)
from wx_t_promotion_delivery_detail pdd
where pdd.award_type=2 and pdd.source_type='delivery'  and pdd.delivery_time='2020-09-02'

select sum(l.modify_quantity) from wx_t_promotion_gift_modify_log l
left join [wx_t_promotion_delivery_detail] pdd on l.business_key=pdd.id 
 where l.business_type='SELL_IN_PROMOTION_DELIVERY' and pdd.delivery_time='2020-09-02'
select sum(pdd.award_quantity)
from wx_t_promotion_delivery_detail pdd
where pdd.award_type=1 and pdd.source_type='delivery' and pdd.delivery_time='2020-09-02'

select o.organization_name, pdd.* from wx_t_promotion_delivery_detail pdd
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pdd.ext_property7 
left join wx_t_organization o on o.id=pe.partner_id
where delivery_status=10
order by o.id

select *
--delete l
from wx_t_point_value_detail_log l 
where exists (select 1 from wx_t_point_value_detail d
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID  where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2'
 and l.POINT_VALUE_ID=d.id)

select *
--delete d
from wx_t_point_value_detail d 
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2'

select top 1000 *
--delete pb
 from dbo.wx_t_point_business pb where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2'

truncate table wx_t_promotion_gift_pool;
truncate table wx_t_promotion_gift_modify_log;
truncate table wx_t_pro_gift_pool_mod_log;

select * 
--update d set d.delivery_status=10
from wx_t_promotion_delivery_detail d

		insert into wx_t_sell_in_promotion_delivery (sales_order, region,[distributor_id], sold_to_code,product_sku,post_year_month,
		pack_units,quantity, liters, revenue_rmb, [base_trans_sell_in_id],[pricing_date])
		select distinct bsi.[sales_reference_doc] sales_order, bso.region,bsi.[distributor_id],
				bsi.[customer_code] sold_to_code,bsi.[product_code_SAP] product_sku, bsi.[trans_time] post_year_month,
				di.dic_item_name pack_units,
				convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0)) quantity, 
				round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0) * bp.unit liters,
				bsi.revenue_rmb, bsi.[base_trans_sell_in_id],bsi.[pricing_date]
				from [PP_MID].[cmt].[view_campaign_offer_detail_value] cod 
				left join [PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi on cod.[sales_reference_doc]=bsi.[sales_reference_doc]
					and bsi.liters>0
					and exists (select 1 from [PP_MID].[cmt].[offer_product_rel] opr where cod.[offer_id]=opr.[offer_id] and opr.[product_code]=bsi.[product_code_SAP])
				left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] bso on bsi.org_hier_id=bso.org_hier_id
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
				left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
		where not exists (select 1 from wx_t_sell_in_promotion_delivery pd where pd.base_trans_sell_in_id=bsi.base_trans_sell_in_id) and cod.value>0 
		and (cod.points_time>'2020-10-16' or not exists (select 1 from [PP_MID].[cmt].[offer] o where o.offer_id=cod.offer_id and o.campaign_id=45))

		insert into [wx_t_promotion_delivery_detail] ([promotion_id]
		      ,[award_type]
		      ,[source_type]
		      ,[source_id]
		      ,[award_quantity]
		      ,[promotion_desc]
		      ,[delivery_status]
		      ,[ext_property1]
		      ,[ext_property2]
		      ,[ext_property3]
		      ,[ext_property4]
			  ,[ext_property7]
			  ,[ext_property8]
			  ,[ext_property9]
		      ,[create_time]
		      ,[offer_key])
		select convert(bigint, [campaign_code]) [promotion_id],
		 convert(int, rc.[ext_property1]) [award_type],
		 'delivery' [source_type],
		 -1 [source_id],
		 cod.[value] [award_quantity],
		 o.ext_property1 [promotion_desc],
		 10 [delivery_status],
		 rc.[reward_category_name] [ext_property1],
		 rc.[ext_property2] [ext_property2],
		 rc.[ext_property3] [ext_property3],
		 rc.ext_property4 [ext_property4],
		 cod.distributor_id [ext_property7],
		 cod.product_channel [ext_property8],
		 cod.[sales_reference_doc] [ext_property9],
		 getdate() create_time,
		 cod.offer_id offer_key
		  FROM [PP_MID].[cmt].[view_campaign_offer_detail_value] cod
		  left join [PP_MID].[cmt].[offer] o on cod.offer_id=o.offer_id
		  left join [PP_MID].[cmt].[campaign] c on o.campaign_id=c.campaign_id
		  left join [PP_MID].[cmt].[reward_category] rc on rc.reward_category_id=o.reward_category_id
		  where cod.[value]>0 and (cod.points_time>'2020-10-16' or not exists (select 1 from [PP_MID].[cmt].[offer] o where o.offer_id=cod.offer_id and o.campaign_id=45))
		
		  insert into wx_t_delivery_order_rel (delivery_detail_id, delivery_id)
			select distinct pdd.id, pd.delivery_id from [wx_t_promotion_delivery_detail] pdd
			left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
			left join [PP_MID].[cmt].[campaign_offer_detail] cod on ods.offer_id=cod.offer_id and ods.distributor_id=cod.distributor_id
				and CONVERT(varchar(100), ods.create_time, 112)=CONVERT(varchar(100), cod.create_time, 112)
			left join wx_t_sell_in_promotion_delivery pd on pd.sales_order=pdd.ext_property9 
			and exists (select 1 from [PP_MID].[cmt].[offer_product_rel] opr where pdd.offer_key=opr.[offer_id] and opr.[product_code]=pd.product_sku)
			where pdd.create_time>'2020-10-20'
			
select sum(award_quantity), ext_property1 from wx_t_promotion_delivery_detail 
group by ext_property1, ext_property2			
			

select isnull((select sum(d.POINT_VALUE)
					from wx_t_point_value_detail d 
					where d.POINT_VALUE > 0 and exists (select 1 from dbo.wx_t_point_business pb
						where pb.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' and pb.id=d.BUSINESS_ID and pb.ATTRIBUTE4='B2020111710270')),0) c1,isnull((
							select sum(pdd.award_quantity)
							from wx_t_promotion_delivery_detail pdd
							where pdd.award_type=2 and pdd.source_type='delivery' and pdd.ext_property5='B2020111710270' and pdd.award_quantity>0),0) c2,
				 isnull((select sum(l.value_change)
					from wx_t_pro_gift_pool_mod_log l 
					where l.business_type='SELL_IN_PROMOTION_DELIVERY_V2' and l.business_key='B2020111710270' and l.value_change<0),0) c3, isnull((
							select sum(pdd.award_quantity)
							from wx_t_promotion_delivery_detail pdd
							where pdd.award_type=1 and pdd.source_type='delivery' and pdd.ext_property5='B2020111710270'),0) c4