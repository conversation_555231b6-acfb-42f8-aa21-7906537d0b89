<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.budget.dao.BudgetTotalMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.budget.model.BudgetTotal">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="program_id" property="programId" jdbcType="BIGINT"/>
		<result column="expense_code" property="expenseCode" jdbcType="VARCHAR"/>
		<result column="expense_title" property="expenseTitle" jdbcType="VARCHAR"/>
		<result column="expense_order" property="expenseOrder" jdbcType="NUMERIC"/>
		<result column="input_user" property="inputUser" jdbcType="INTEGER"/>
		<result column="input_user_id" property="inputUserId" jdbcType="BIGINT"/>
		<result column="budget_value" property="budgetValue" jdbcType="NUMERIC"/>
		<result column="sales_channels" property="salesChannels" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="version_no" property="versionNo" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="has_edit_log_flag" property="hasEditLogFlag" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,program_id,expense_code,expense_title,expense_order,input_user,input_user_id,budget_value,sales_channels,status,
		version_no,create_user_id,create_time
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.budget.model.BudgetTotal">
		update wx_t_budget_total
		<set>
			<if test="programId != null" >
				program_id = #{programId,jdbcType=BIGINT},
			</if>
			<if test="expenseCode != null" >
				expense_code = #{expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="expenseTitle != null" >
				expense_title = #{expenseTitle,jdbcType=VARCHAR},
			</if>
			<if test="expenseOrder != null" >
				expense_order = #{expenseOrder,jdbcType=NUMERIC},
			</if>
			<if test="inputUser != null" >
				input_user = #{inputUser,jdbcType=INTEGER},
			</if>
			<if test="inputUserId != null" >
				input_user_id = #{inputUserId,jdbcType=BIGINT},
			</if>
			<if test="budgetValue != null" >
				budget_value = #{budgetValue,jdbcType=NUMERIC},
			</if>
			<if test="salesChannels != null" >
				sales_channels = #{salesChannels,jdbcType=INTEGER},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="versionNo != null" >
				version_no = #{versionNo,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.budget.model.BudgetTotalExample">
    	delete from wx_t_budget_total
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.budget.model.BudgetTotal">
		insert into wx_t_budget_total
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="programId != null">
				program_id,
			</if>
			<if test="expenseCode != null">
				expense_code,
			</if>
			<if test="expenseTitle != null">
				expense_title,
			</if>
			<if test="expenseOrder != null">
				expense_order,
			</if>
			<if test="inputUser != null">
				input_user,
			</if>
			<if test="inputUserId != null">
				input_user_id,
			</if>
			<if test="budgetValue != null">
				budget_value,
			</if>
			<if test="salesChannels != null">
				sales_channels,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="versionNo != null">
				version_no,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="programId != null">
				#{programId,jdbcType=BIGINT},
			</if>
			<if test="expenseCode != null">
				#{expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="expenseTitle != null">
				#{expenseTitle,jdbcType=VARCHAR},
			</if>
			<if test="expenseOrder != null">
				#{expenseOrder,jdbcType=NUMERIC},
			</if>
			<if test="inputUser != null">
				#{inputUser,jdbcType=INTEGER},
			</if>
			<if test="inputUserId != null">
				#{inputUserId,jdbcType=BIGINT},
			</if>
			<if test="budgetValue != null">
				#{budgetValue,jdbcType=NUMERIC},
			</if>
			<if test="salesChannels != null">
				#{salesChannels,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="versionNo != null">
				#{versionNo,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_budget_total
		<set>
			<if test="record.programId != null">
				program_id = #{record.programId,jdbcType=BIGINT},
			</if>
			<if test="record.expenseCode != null">
				expense_code = #{record.expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="record.expenseTitle != null">
				expense_title = #{record.expenseTitle,jdbcType=VARCHAR},
			</if>
			<if test="record.expenseOrder != null">
				expense_order = #{record.expenseOrder,jdbcType=NUMERIC},
			</if>
			<if test="record.inputUser != null">
				input_user = #{record.inputUser,jdbcType=INTEGER},
			</if>
			<if test="record.inputUserId != null">
				input_user_id = #{record.inputUserId,jdbcType=BIGINT},
			</if>
			<if test="record.budgetValue != null">
				budget_value = #{record.budgetValue,jdbcType=NUMERIC},
			</if>
			<if test="record.salesChannels != null">
				sales_channels = #{record.salesChannels,jdbcType=INTEGER},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.versionNo != null">
				version_no = #{record.versionNo,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.budget.model.BudgetTotalExample">
		delete from wx_t_budget_total
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.budget.model.BudgetTotalExample" resultType="int">
		select count(1) from wx_t_budget_total
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetTotalExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_budget_total
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetTotalExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_budget_total
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.program_id, t1.expense_code, t1.expense_title, t1.expense_order, t1.input_user, t1.input_user_id,
			 t1.budget_value, t1.sales_channels, t1.status, t1.version_no, t1.create_user_id, t1.create_time, 
			 case when exists (select 1 from wx_t_budget_edit_log bel where bel.program_id = t1.program_id and bel.log_key='/' + convert(nvarchar(20), t1.input_user) + '/' + t1.expense_code and bel.log_type != 0) then 1 else 0 end has_edit_log_flag
		  from wx_t_budget_total t1
		 where 1=1
		 <if test="programId != null">
		 and t1.program_id=#{programId}
		 </if>
		 <if test="versionNo != null">
		 and t1.version_no=#{versionNo}
		 </if>
		 <if test="inputUserId != null">
		 and (#{inputUserId}=0 or t1.input_user_id&amp;#{inputUserId}>0)
		 </if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_budget_total (program_id, expense_code, expense_title, expense_order, input_user, input_user_id, budget_value, sales_channels, status, version_no, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.programId, jdbcType=BIGINT}, #{item.expenseCode, jdbcType=VARCHAR}, #{item.expenseTitle, jdbcType=VARCHAR}, #{item.expenseOrder, jdbcType=NUMERIC}, #{item.inputUser, jdbcType=INTEGER}, #{item.inputUserId, jdbcType=BIGINT}, #{item.budgetValue, jdbcType=NUMERIC}, #{item.salesChannels, jdbcType=INTEGER}, #{item.status, jdbcType=INTEGER}, #{item.versionNo, jdbcType=BIGINT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
