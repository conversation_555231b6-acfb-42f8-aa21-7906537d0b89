package com.chevron.pms.model;

import java.util.ArrayList;
import java.util.List;

public class VehicleTypeVoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public VehicleTypeVoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLevelIdIsNull() {
            addCriterion("level_id is null");
            return (Criteria) this;
        }

        public Criteria andLevelIdIsNotNull() {
            addCriterion("level_id is not null");
            return (Criteria) this;
        }

        public Criteria andLevelIdEqualTo(String value) {
            addCriterion("level_id =", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdNotEqualTo(String value) {
            addCriterion("level_id <>", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdGreaterThan(String value) {
            addCriterion("level_id >", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdGreaterThanOrEqualTo(String value) {
            addCriterion("level_id >=", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdLessThan(String value) {
            addCriterion("level_id <", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdLessThanOrEqualTo(String value) {
            addCriterion("level_id <=", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdLike(String value) {
            addCriterion("level_id like", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdNotLike(String value) {
            addCriterion("level_id not like", value, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdIn(List<String> values) {
            addCriterion("level_id in", values, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdNotIn(List<String> values) {
            addCriterion("level_id not in", values, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdBetween(String value1, String value2) {
            addCriterion("level_id between", value1, value2, "levelId");
            return (Criteria) this;
        }

        public Criteria andLevelIdNotBetween(String value1, String value2) {
            addCriterion("level_id not between", value1, value2, "levelId");
            return (Criteria) this;
        }

        public Criteria andFactoryIsNull() {
            addCriterion("factory is null");
            return (Criteria) this;
        }

        public Criteria andFactoryIsNotNull() {
            addCriterion("factory is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryEqualTo(String value) {
            addCriterion("factory =", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryNotEqualTo(String value) {
            addCriterion("factory <>", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryGreaterThan(String value) {
            addCriterion("factory >", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryGreaterThanOrEqualTo(String value) {
            addCriterion("factory >=", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryLessThan(String value) {
            addCriterion("factory <", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryLessThanOrEqualTo(String value) {
            addCriterion("factory <=", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryLike(String value) {
            addCriterion("factory like", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryNotLike(String value) {
            addCriterion("factory not like", value, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryIn(List<String> values) {
            addCriterion("factory in", values, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryNotIn(List<String> values) {
            addCriterion("factory not in", values, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryBetween(String value1, String value2) {
            addCriterion("factory between", value1, value2, "factory");
            return (Criteria) this;
        }

        public Criteria andFactoryNotBetween(String value1, String value2) {
            addCriterion("factory not between", value1, value2, "factory");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIsNull() {
            addCriterion("vehicle_series is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIsNotNull() {
            addCriterion("vehicle_series is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEqualTo(String value) {
            addCriterion("vehicle_series =", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotEqualTo(String value) {
            addCriterion("vehicle_series <>", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesGreaterThan(String value) {
            addCriterion("vehicle_series >", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_series >=", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLessThan(String value) {
            addCriterion("vehicle_series <", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLessThanOrEqualTo(String value) {
            addCriterion("vehicle_series <=", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLike(String value) {
            addCriterion("vehicle_series like", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotLike(String value) {
            addCriterion("vehicle_series not like", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIn(List<String> values) {
            addCriterion("vehicle_series in", values, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotIn(List<String> values) {
            addCriterion("vehicle_series not in", values, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesBetween(String value1, String value2) {
            addCriterion("vehicle_series between", value1, value2, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotBetween(String value1, String value2) {
            addCriterion("vehicle_series not between", value1, value2, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(String value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(String value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(String value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(String value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLike(String value) {
            addCriterion("vehicle_type like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotLike(String value) {
            addCriterion("vehicle_type not like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<String> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<String> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(String value1, String value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(String value1, String value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andSalesNameIsNull() {
            addCriterion("sales_name is null");
            return (Criteria) this;
        }

        public Criteria andSalesNameIsNotNull() {
            addCriterion("sales_name is not null");
            return (Criteria) this;
        }

        public Criteria andSalesNameEqualTo(String value) {
            addCriterion("sales_name =", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameNotEqualTo(String value) {
            addCriterion("sales_name <>", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameGreaterThan(String value) {
            addCriterion("sales_name >", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameGreaterThanOrEqualTo(String value) {
            addCriterion("sales_name >=", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameLessThan(String value) {
            addCriterion("sales_name <", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameLessThanOrEqualTo(String value) {
            addCriterion("sales_name <=", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameLike(String value) {
            addCriterion("sales_name like", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameNotLike(String value) {
            addCriterion("sales_name not like", value, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameIn(List<String> values) {
            addCriterion("sales_name in", values, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameNotIn(List<String> values) {
            addCriterion("sales_name not in", values, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameBetween(String value1, String value2) {
            addCriterion("sales_name between", value1, value2, "salesName");
            return (Criteria) this;
        }

        public Criteria andSalesNameNotBetween(String value1, String value2) {
            addCriterion("sales_name not between", value1, value2, "salesName");
            return (Criteria) this;
        }

        public Criteria andModelYearIsNull() {
            addCriterion("model_year is null");
            return (Criteria) this;
        }

        public Criteria andModelYearIsNotNull() {
            addCriterion("model_year is not null");
            return (Criteria) this;
        }

        public Criteria andModelYearEqualTo(Integer value) {
            addCriterion("model_year =", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotEqualTo(Integer value) {
            addCriterion("model_year <>", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearGreaterThan(Integer value) {
            addCriterion("model_year >", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("model_year >=", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearLessThan(Integer value) {
            addCriterion("model_year <", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearLessThanOrEqualTo(Integer value) {
            addCriterion("model_year <=", value, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearIn(List<Integer> values) {
            addCriterion("model_year in", values, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotIn(List<Integer> values) {
            addCriterion("model_year not in", values, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearBetween(Integer value1, Integer value2) {
            addCriterion("model_year between", value1, value2, "modelYear");
            return (Criteria) this;
        }

        public Criteria andModelYearNotBetween(Integer value1, Integer value2) {
            addCriterion("model_year not between", value1, value2, "modelYear");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardIsNull() {
            addCriterion("emission_standard is null");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardIsNotNull() {
            addCriterion("emission_standard is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardEqualTo(String value) {
            addCriterion("emission_standard =", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardNotEqualTo(String value) {
            addCriterion("emission_standard <>", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardGreaterThan(String value) {
            addCriterion("emission_standard >", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardGreaterThanOrEqualTo(String value) {
            addCriterion("emission_standard >=", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardLessThan(String value) {
            addCriterion("emission_standard <", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardLessThanOrEqualTo(String value) {
            addCriterion("emission_standard <=", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardLike(String value) {
            addCriterion("emission_standard like", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardNotLike(String value) {
            addCriterion("emission_standard not like", value, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardIn(List<String> values) {
            addCriterion("emission_standard in", values, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardNotIn(List<String> values) {
            addCriterion("emission_standard not in", values, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardBetween(String value1, String value2) {
            addCriterion("emission_standard between", value1, value2, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andEmissionStandardNotBetween(String value1, String value2) {
            addCriterion("emission_standard not between", value1, value2, "emissionStandard");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryIsNull() {
            addCriterion("vehicle_category is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryIsNotNull() {
            addCriterion("vehicle_category is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryEqualTo(String value) {
            addCriterion("vehicle_category =", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotEqualTo(String value) {
            addCriterion("vehicle_category <>", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryGreaterThan(String value) {
            addCriterion("vehicle_category >", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_category >=", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryLessThan(String value) {
            addCriterion("vehicle_category <", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryLessThanOrEqualTo(String value) {
            addCriterion("vehicle_category <=", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryLike(String value) {
            addCriterion("vehicle_category like", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotLike(String value) {
            addCriterion("vehicle_category not like", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryIn(List<String> values) {
            addCriterion("vehicle_category in", values, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotIn(List<String> values) {
            addCriterion("vehicle_category not in", values, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryBetween(String value1, String value2) {
            addCriterion("vehicle_category between", value1, value2, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotBetween(String value1, String value2) {
            addCriterion("vehicle_category not between", value1, value2, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andSalesYearIsNull() {
            addCriterion("sales_year is null");
            return (Criteria) this;
        }

        public Criteria andSalesYearIsNotNull() {
            addCriterion("sales_year is not null");
            return (Criteria) this;
        }

        public Criteria andSalesYearEqualTo(Integer value) {
            addCriterion("sales_year =", value, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearNotEqualTo(Integer value) {
            addCriterion("sales_year <>", value, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearGreaterThan(Integer value) {
            addCriterion("sales_year >", value, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_year >=", value, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearLessThan(Integer value) {
            addCriterion("sales_year <", value, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearLessThanOrEqualTo(Integer value) {
            addCriterion("sales_year <=", value, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearIn(List<Integer> values) {
            addCriterion("sales_year in", values, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearNotIn(List<Integer> values) {
            addCriterion("sales_year not in", values, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearBetween(Integer value1, Integer value2) {
            addCriterion("sales_year between", value1, value2, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesYearNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_year not between", value1, value2, "salesYear");
            return (Criteria) this;
        }

        public Criteria andSalesMonthIsNull() {
            addCriterion("sales_month is null");
            return (Criteria) this;
        }

        public Criteria andSalesMonthIsNotNull() {
            addCriterion("sales_month is not null");
            return (Criteria) this;
        }

        public Criteria andSalesMonthEqualTo(Integer value) {
            addCriterion("sales_month =", value, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthNotEqualTo(Integer value) {
            addCriterion("sales_month <>", value, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthGreaterThan(Integer value) {
            addCriterion("sales_month >", value, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_month >=", value, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthLessThan(Integer value) {
            addCriterion("sales_month <", value, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthLessThanOrEqualTo(Integer value) {
            addCriterion("sales_month <=", value, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthIn(List<Integer> values) {
            addCriterion("sales_month in", values, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthNotIn(List<Integer> values) {
            addCriterion("sales_month not in", values, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthBetween(Integer value1, Integer value2) {
            addCriterion("sales_month between", value1, value2, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andSalesMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_month not between", value1, value2, "salesMonth");
            return (Criteria) this;
        }

        public Criteria andProductYearIsNull() {
            addCriterion("product_year is null");
            return (Criteria) this;
        }

        public Criteria andProductYearIsNotNull() {
            addCriterion("product_year is not null");
            return (Criteria) this;
        }

        public Criteria andProductYearEqualTo(Integer value) {
            addCriterion("product_year =", value, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearNotEqualTo(Integer value) {
            addCriterion("product_year <>", value, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearGreaterThan(Integer value) {
            addCriterion("product_year >", value, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_year >=", value, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearLessThan(Integer value) {
            addCriterion("product_year <", value, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearLessThanOrEqualTo(Integer value) {
            addCriterion("product_year <=", value, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearIn(List<Integer> values) {
            addCriterion("product_year in", values, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearNotIn(List<Integer> values) {
            addCriterion("product_year not in", values, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearBetween(Integer value1, Integer value2) {
            addCriterion("product_year between", value1, value2, "productYear");
            return (Criteria) this;
        }

        public Criteria andProductYearNotBetween(Integer value1, Integer value2) {
            addCriterion("product_year not between", value1, value2, "productYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearIsNull() {
            addCriterion("stop_production_year is null");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearIsNotNull() {
            addCriterion("stop_production_year is not null");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearEqualTo(Integer value) {
            addCriterion("stop_production_year =", value, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearNotEqualTo(Integer value) {
            addCriterion("stop_production_year <>", value, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearGreaterThan(Integer value) {
            addCriterion("stop_production_year >", value, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("stop_production_year >=", value, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearLessThan(Integer value) {
            addCriterion("stop_production_year <", value, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearLessThanOrEqualTo(Integer value) {
            addCriterion("stop_production_year <=", value, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearIn(List<Integer> values) {
            addCriterion("stop_production_year in", values, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearNotIn(List<Integer> values) {
            addCriterion("stop_production_year not in", values, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearBetween(Integer value1, Integer value2) {
            addCriterion("stop_production_year between", value1, value2, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionYearNotBetween(Integer value1, Integer value2) {
            addCriterion("stop_production_year not between", value1, value2, "stopProductionYear");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusIsNull() {
            addCriterion("stop_production_status is null");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusIsNotNull() {
            addCriterion("stop_production_status is not null");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusEqualTo(String value) {
            addCriterion("stop_production_status =", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusNotEqualTo(String value) {
            addCriterion("stop_production_status <>", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusGreaterThan(String value) {
            addCriterion("stop_production_status >", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusGreaterThanOrEqualTo(String value) {
            addCriterion("stop_production_status >=", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusLessThan(String value) {
            addCriterion("stop_production_status <", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusLessThanOrEqualTo(String value) {
            addCriterion("stop_production_status <=", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusLike(String value) {
            addCriterion("stop_production_status like", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusNotLike(String value) {
            addCriterion("stop_production_status not like", value, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusIn(List<String> values) {
            addCriterion("stop_production_status in", values, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusNotIn(List<String> values) {
            addCriterion("stop_production_status not in", values, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusBetween(String value1, String value2) {
            addCriterion("stop_production_status between", value1, value2, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andStopProductionStatusNotBetween(String value1, String value2) {
            addCriterion("stop_production_status not between", value1, value2, "stopProductionStatus");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsIsNull() {
            addCriterion("vehicle_kinds is null");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsIsNotNull() {
            addCriterion("vehicle_kinds is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsEqualTo(String value) {
            addCriterion("vehicle_kinds =", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsNotEqualTo(String value) {
            addCriterion("vehicle_kinds <>", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsGreaterThan(String value) {
            addCriterion("vehicle_kinds >", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_kinds >=", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsLessThan(String value) {
            addCriterion("vehicle_kinds <", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsLessThanOrEqualTo(String value) {
            addCriterion("vehicle_kinds <=", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsLike(String value) {
            addCriterion("vehicle_kinds like", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsNotLike(String value) {
            addCriterion("vehicle_kinds not like", value, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsIn(List<String> values) {
            addCriterion("vehicle_kinds in", values, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsNotIn(List<String> values) {
            addCriterion("vehicle_kinds not in", values, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsBetween(String value1, String value2) {
            addCriterion("vehicle_kinds between", value1, value2, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andVehicleKindsNotBetween(String value1, String value2) {
            addCriterion("vehicle_kinds not between", value1, value2, "vehicleKinds");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeIsNull() {
            addCriterion("cylinder_volume is null");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeIsNotNull() {
            addCriterion("cylinder_volume is not null");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeEqualTo(String value) {
            addCriterion("cylinder_volume =", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeNotEqualTo(String value) {
            addCriterion("cylinder_volume <>", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeGreaterThan(String value) {
            addCriterion("cylinder_volume >", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeGreaterThanOrEqualTo(String value) {
            addCriterion("cylinder_volume >=", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeLessThan(String value) {
            addCriterion("cylinder_volume <", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeLessThanOrEqualTo(String value) {
            addCriterion("cylinder_volume <=", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeLike(String value) {
            addCriterion("cylinder_volume like", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeNotLike(String value) {
            addCriterion("cylinder_volume not like", value, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeIn(List<String> values) {
            addCriterion("cylinder_volume in", values, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeNotIn(List<String> values) {
            addCriterion("cylinder_volume not in", values, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeBetween(String value1, String value2) {
            addCriterion("cylinder_volume between", value1, value2, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andCylinderVolumeNotBetween(String value1, String value2) {
            addCriterion("cylinder_volume not between", value1, value2, "cylinderVolume");
            return (Criteria) this;
        }

        public Criteria andDisplacementIsNull() {
            addCriterion("displacement is null");
            return (Criteria) this;
        }

        public Criteria andDisplacementIsNotNull() {
            addCriterion("displacement is not null");
            return (Criteria) this;
        }

        public Criteria andDisplacementEqualTo(String value) {
            addCriterion("displacement =", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementNotEqualTo(String value) {
            addCriterion("displacement <>", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementGreaterThan(String value) {
            addCriterion("displacement >", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementGreaterThanOrEqualTo(String value) {
            addCriterion("displacement >=", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementLessThan(String value) {
            addCriterion("displacement <", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementLessThanOrEqualTo(String value) {
            addCriterion("displacement <=", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementLike(String value) {
            addCriterion("displacement like", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementNotLike(String value) {
            addCriterion("displacement not like", value, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementIn(List<String> values) {
            addCriterion("displacement in", values, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementNotIn(List<String> values) {
            addCriterion("displacement not in", values, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementBetween(String value1, String value2) {
            addCriterion("displacement between", value1, value2, "displacement");
            return (Criteria) this;
        }

        public Criteria andDisplacementNotBetween(String value1, String value2) {
            addCriterion("displacement not between", value1, value2, "displacement");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormIsNull() {
            addCriterion("air_intake_form is null");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormIsNotNull() {
            addCriterion("air_intake_form is not null");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormEqualTo(String value) {
            addCriterion("air_intake_form =", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormNotEqualTo(String value) {
            addCriterion("air_intake_form <>", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormGreaterThan(String value) {
            addCriterion("air_intake_form >", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormGreaterThanOrEqualTo(String value) {
            addCriterion("air_intake_form >=", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormLessThan(String value) {
            addCriterion("air_intake_form <", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormLessThanOrEqualTo(String value) {
            addCriterion("air_intake_form <=", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormLike(String value) {
            addCriterion("air_intake_form like", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormNotLike(String value) {
            addCriterion("air_intake_form not like", value, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormIn(List<String> values) {
            addCriterion("air_intake_form in", values, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormNotIn(List<String> values) {
            addCriterion("air_intake_form not in", values, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormBetween(String value1, String value2) {
            addCriterion("air_intake_form between", value1, value2, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAirIntakeFormNotBetween(String value1, String value2) {
            addCriterion("air_intake_form not between", value1, value2, "airIntakeForm");
            return (Criteria) this;
        }

        public Criteria andFuelTypeIsNull() {
            addCriterion("fuel_type is null");
            return (Criteria) this;
        }

        public Criteria andFuelTypeIsNotNull() {
            addCriterion("fuel_type is not null");
            return (Criteria) this;
        }

        public Criteria andFuelTypeEqualTo(String value) {
            addCriterion("fuel_type =", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotEqualTo(String value) {
            addCriterion("fuel_type <>", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeGreaterThan(String value) {
            addCriterion("fuel_type >", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeGreaterThanOrEqualTo(String value) {
            addCriterion("fuel_type >=", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeLessThan(String value) {
            addCriterion("fuel_type <", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeLessThanOrEqualTo(String value) {
            addCriterion("fuel_type <=", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeLike(String value) {
            addCriterion("fuel_type like", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotLike(String value) {
            addCriterion("fuel_type not like", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeIn(List<String> values) {
            addCriterion("fuel_type in", values, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotIn(List<String> values) {
            addCriterion("fuel_type not in", values, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeBetween(String value1, String value2) {
            addCriterion("fuel_type between", value1, value2, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotBetween(String value1, String value2) {
            addCriterion("fuel_type not between", value1, value2, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelLabelIsNull() {
            addCriterion("fuel_label is null");
            return (Criteria) this;
        }

        public Criteria andFuelLabelIsNotNull() {
            addCriterion("fuel_label is not null");
            return (Criteria) this;
        }

        public Criteria andFuelLabelEqualTo(String value) {
            addCriterion("fuel_label =", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelNotEqualTo(String value) {
            addCriterion("fuel_label <>", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelGreaterThan(String value) {
            addCriterion("fuel_label >", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelGreaterThanOrEqualTo(String value) {
            addCriterion("fuel_label >=", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelLessThan(String value) {
            addCriterion("fuel_label <", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelLessThanOrEqualTo(String value) {
            addCriterion("fuel_label <=", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelLike(String value) {
            addCriterion("fuel_label like", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelNotLike(String value) {
            addCriterion("fuel_label not like", value, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelIn(List<String> values) {
            addCriterion("fuel_label in", values, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelNotIn(List<String> values) {
            addCriterion("fuel_label not in", values, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelBetween(String value1, String value2) {
            addCriterion("fuel_label between", value1, value2, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andFuelLabelNotBetween(String value1, String value2) {
            addCriterion("fuel_label not between", value1, value2, "fuelLabel");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerIsNull() {
            addCriterion("maximum_horsepower is null");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerIsNotNull() {
            addCriterion("maximum_horsepower is not null");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerEqualTo(String value) {
            addCriterion("maximum_horsepower =", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerNotEqualTo(String value) {
            addCriterion("maximum_horsepower <>", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerGreaterThan(String value) {
            addCriterion("maximum_horsepower >", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerGreaterThanOrEqualTo(String value) {
            addCriterion("maximum_horsepower >=", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerLessThan(String value) {
            addCriterion("maximum_horsepower <", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerLessThanOrEqualTo(String value) {
            addCriterion("maximum_horsepower <=", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerLike(String value) {
            addCriterion("maximum_horsepower like", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerNotLike(String value) {
            addCriterion("maximum_horsepower not like", value, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerIn(List<String> values) {
            addCriterion("maximum_horsepower in", values, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerNotIn(List<String> values) {
            addCriterion("maximum_horsepower not in", values, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerBetween(String value1, String value2) {
            addCriterion("maximum_horsepower between", value1, value2, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumHorsepowerNotBetween(String value1, String value2) {
            addCriterion("maximum_horsepower not between", value1, value2, "maximumHorsepower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerIsNull() {
            addCriterion("maximum_power is null");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerIsNotNull() {
            addCriterion("maximum_power is not null");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerEqualTo(String value) {
            addCriterion("maximum_power =", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerNotEqualTo(String value) {
            addCriterion("maximum_power <>", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerGreaterThan(String value) {
            addCriterion("maximum_power >", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerGreaterThanOrEqualTo(String value) {
            addCriterion("maximum_power >=", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerLessThan(String value) {
            addCriterion("maximum_power <", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerLessThanOrEqualTo(String value) {
            addCriterion("maximum_power <=", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerLike(String value) {
            addCriterion("maximum_power like", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerNotLike(String value) {
            addCriterion("maximum_power not like", value, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerIn(List<String> values) {
            addCriterion("maximum_power in", values, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerNotIn(List<String> values) {
            addCriterion("maximum_power not in", values, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerBetween(String value1, String value2) {
            addCriterion("maximum_power between", value1, value2, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andMaximumPowerNotBetween(String value1, String value2) {
            addCriterion("maximum_power not between", value1, value2, "maximumPower");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementIsNull() {
            addCriterion("cylinder_arrangement is null");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementIsNotNull() {
            addCriterion("cylinder_arrangement is not null");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementEqualTo(String value) {
            addCriterion("cylinder_arrangement =", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementNotEqualTo(String value) {
            addCriterion("cylinder_arrangement <>", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementGreaterThan(String value) {
            addCriterion("cylinder_arrangement >", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementGreaterThanOrEqualTo(String value) {
            addCriterion("cylinder_arrangement >=", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementLessThan(String value) {
            addCriterion("cylinder_arrangement <", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementLessThanOrEqualTo(String value) {
            addCriterion("cylinder_arrangement <=", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementLike(String value) {
            addCriterion("cylinder_arrangement like", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementNotLike(String value) {
            addCriterion("cylinder_arrangement not like", value, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementIn(List<String> values) {
            addCriterion("cylinder_arrangement in", values, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementNotIn(List<String> values) {
            addCriterion("cylinder_arrangement not in", values, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementBetween(String value1, String value2) {
            addCriterion("cylinder_arrangement between", value1, value2, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andCylinderArrangementNotBetween(String value1, String value2) {
            addCriterion("cylinder_arrangement not between", value1, value2, "cylinderArrangement");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersIsNull() {
            addCriterion("number_of_cylinders is null");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersIsNotNull() {
            addCriterion("number_of_cylinders is not null");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersEqualTo(String value) {
            addCriterion("number_of_cylinders =", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersNotEqualTo(String value) {
            addCriterion("number_of_cylinders <>", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersGreaterThan(String value) {
            addCriterion("number_of_cylinders >", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersGreaterThanOrEqualTo(String value) {
            addCriterion("number_of_cylinders >=", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersLessThan(String value) {
            addCriterion("number_of_cylinders <", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersLessThanOrEqualTo(String value) {
            addCriterion("number_of_cylinders <=", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersLike(String value) {
            addCriterion("number_of_cylinders like", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersNotLike(String value) {
            addCriterion("number_of_cylinders not like", value, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersIn(List<String> values) {
            addCriterion("number_of_cylinders in", values, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersNotIn(List<String> values) {
            addCriterion("number_of_cylinders not in", values, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersBetween(String value1, String value2) {
            addCriterion("number_of_cylinders between", value1, value2, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andNumberOfCylindersNotBetween(String value1, String value2) {
            addCriterion("number_of_cylinders not between", value1, value2, "numberOfCylinders");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderIsNull() {
            addCriterion("valves_per_cylinder is null");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderIsNotNull() {
            addCriterion("valves_per_cylinder is not null");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderEqualTo(String value) {
            addCriterion("valves_per_cylinder =", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderNotEqualTo(String value) {
            addCriterion("valves_per_cylinder <>", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderGreaterThan(String value) {
            addCriterion("valves_per_cylinder >", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderGreaterThanOrEqualTo(String value) {
            addCriterion("valves_per_cylinder >=", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderLessThan(String value) {
            addCriterion("valves_per_cylinder <", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderLessThanOrEqualTo(String value) {
            addCriterion("valves_per_cylinder <=", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderLike(String value) {
            addCriterion("valves_per_cylinder like", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderNotLike(String value) {
            addCriterion("valves_per_cylinder not like", value, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderIn(List<String> values) {
            addCriterion("valves_per_cylinder in", values, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderNotIn(List<String> values) {
            addCriterion("valves_per_cylinder not in", values, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderBetween(String value1, String value2) {
            addCriterion("valves_per_cylinder between", value1, value2, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andValvesPerCylinderNotBetween(String value1, String value2) {
            addCriterion("valves_per_cylinder not between", value1, value2, "valvesPerCylinder");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeIsNull() {
            addCriterion("gearbox_type is null");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeIsNotNull() {
            addCriterion("gearbox_type is not null");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeEqualTo(String value) {
            addCriterion("gearbox_type =", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeNotEqualTo(String value) {
            addCriterion("gearbox_type <>", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeGreaterThan(String value) {
            addCriterion("gearbox_type >", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeGreaterThanOrEqualTo(String value) {
            addCriterion("gearbox_type >=", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeLessThan(String value) {
            addCriterion("gearbox_type <", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeLessThanOrEqualTo(String value) {
            addCriterion("gearbox_type <=", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeLike(String value) {
            addCriterion("gearbox_type like", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeNotLike(String value) {
            addCriterion("gearbox_type not like", value, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeIn(List<String> values) {
            addCriterion("gearbox_type in", values, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeNotIn(List<String> values) {
            addCriterion("gearbox_type not in", values, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeBetween(String value1, String value2) {
            addCriterion("gearbox_type between", value1, value2, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxTypeNotBetween(String value1, String value2) {
            addCriterion("gearbox_type not between", value1, value2, "gearboxType");
            return (Criteria) this;
        }

        public Criteria andGearboxDescIsNull() {
            addCriterion("gearbox_desc is null");
            return (Criteria) this;
        }

        public Criteria andGearboxDescIsNotNull() {
            addCriterion("gearbox_desc is not null");
            return (Criteria) this;
        }

        public Criteria andGearboxDescEqualTo(String value) {
            addCriterion("gearbox_desc =", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotEqualTo(String value) {
            addCriterion("gearbox_desc <>", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescGreaterThan(String value) {
            addCriterion("gearbox_desc >", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescGreaterThanOrEqualTo(String value) {
            addCriterion("gearbox_desc >=", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescLessThan(String value) {
            addCriterion("gearbox_desc <", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescLessThanOrEqualTo(String value) {
            addCriterion("gearbox_desc <=", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescLike(String value) {
            addCriterion("gearbox_desc like", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotLike(String value) {
            addCriterion("gearbox_desc not like", value, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescIn(List<String> values) {
            addCriterion("gearbox_desc in", values, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotIn(List<String> values) {
            addCriterion("gearbox_desc not in", values, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescBetween(String value1, String value2) {
            addCriterion("gearbox_desc between", value1, value2, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andGearboxDescNotBetween(String value1, String value2) {
            addCriterion("gearbox_desc not between", value1, value2, "gearboxDesc");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsIsNull() {
            addCriterion("number_of_gears is null");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsIsNotNull() {
            addCriterion("number_of_gears is not null");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsEqualTo(String value) {
            addCriterion("number_of_gears =", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsNotEqualTo(String value) {
            addCriterion("number_of_gears <>", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsGreaterThan(String value) {
            addCriterion("number_of_gears >", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsGreaterThanOrEqualTo(String value) {
            addCriterion("number_of_gears >=", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsLessThan(String value) {
            addCriterion("number_of_gears <", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsLessThanOrEqualTo(String value) {
            addCriterion("number_of_gears <=", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsLike(String value) {
            addCriterion("number_of_gears like", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsNotLike(String value) {
            addCriterion("number_of_gears not like", value, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsIn(List<String> values) {
            addCriterion("number_of_gears in", values, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsNotIn(List<String> values) {
            addCriterion("number_of_gears not in", values, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsBetween(String value1, String value2) {
            addCriterion("number_of_gears between", value1, value2, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andNumberOfGearsNotBetween(String value1, String value2) {
            addCriterion("number_of_gears not between", value1, value2, "numberOfGears");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeIsNull() {
            addCriterion("front_brake_type is null");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeIsNotNull() {
            addCriterion("front_brake_type is not null");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeEqualTo(String value) {
            addCriterion("front_brake_type =", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeNotEqualTo(String value) {
            addCriterion("front_brake_type <>", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeGreaterThan(String value) {
            addCriterion("front_brake_type >", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("front_brake_type >=", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeLessThan(String value) {
            addCriterion("front_brake_type <", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeLessThanOrEqualTo(String value) {
            addCriterion("front_brake_type <=", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeLike(String value) {
            addCriterion("front_brake_type like", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeNotLike(String value) {
            addCriterion("front_brake_type not like", value, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeIn(List<String> values) {
            addCriterion("front_brake_type in", values, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeNotIn(List<String> values) {
            addCriterion("front_brake_type not in", values, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeBetween(String value1, String value2) {
            addCriterion("front_brake_type between", value1, value2, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andFrontBrakeTypeNotBetween(String value1, String value2) {
            addCriterion("front_brake_type not between", value1, value2, "frontBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeIsNull() {
            addCriterion("back_brake_type is null");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeIsNotNull() {
            addCriterion("back_brake_type is not null");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeEqualTo(String value) {
            addCriterion("back_brake_type =", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeNotEqualTo(String value) {
            addCriterion("back_brake_type <>", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeGreaterThan(String value) {
            addCriterion("back_brake_type >", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("back_brake_type >=", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeLessThan(String value) {
            addCriterion("back_brake_type <", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeLessThanOrEqualTo(String value) {
            addCriterion("back_brake_type <=", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeLike(String value) {
            addCriterion("back_brake_type like", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeNotLike(String value) {
            addCriterion("back_brake_type not like", value, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeIn(List<String> values) {
            addCriterion("back_brake_type in", values, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeNotIn(List<String> values) {
            addCriterion("back_brake_type not in", values, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeBetween(String value1, String value2) {
            addCriterion("back_brake_type between", value1, value2, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andBackBrakeTypeNotBetween(String value1, String value2) {
            addCriterion("back_brake_type not between", value1, value2, "backBrakeType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeIsNull() {
            addCriterion("assist_type is null");
            return (Criteria) this;
        }

        public Criteria andAssistTypeIsNotNull() {
            addCriterion("assist_type is not null");
            return (Criteria) this;
        }

        public Criteria andAssistTypeEqualTo(String value) {
            addCriterion("assist_type =", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeNotEqualTo(String value) {
            addCriterion("assist_type <>", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeGreaterThan(String value) {
            addCriterion("assist_type >", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeGreaterThanOrEqualTo(String value) {
            addCriterion("assist_type >=", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeLessThan(String value) {
            addCriterion("assist_type <", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeLessThanOrEqualTo(String value) {
            addCriterion("assist_type <=", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeLike(String value) {
            addCriterion("assist_type like", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeNotLike(String value) {
            addCriterion("assist_type not like", value, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeIn(List<String> values) {
            addCriterion("assist_type in", values, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeNotIn(List<String> values) {
            addCriterion("assist_type not in", values, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeBetween(String value1, String value2) {
            addCriterion("assist_type between", value1, value2, "assistType");
            return (Criteria) this;
        }

        public Criteria andAssistTypeNotBetween(String value1, String value2) {
            addCriterion("assist_type not between", value1, value2, "assistType");
            return (Criteria) this;
        }

        public Criteria andEnginePositionIsNull() {
            addCriterion("engine_position is null");
            return (Criteria) this;
        }

        public Criteria andEnginePositionIsNotNull() {
            addCriterion("engine_position is not null");
            return (Criteria) this;
        }

        public Criteria andEnginePositionEqualTo(String value) {
            addCriterion("engine_position =", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionNotEqualTo(String value) {
            addCriterion("engine_position <>", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionGreaterThan(String value) {
            addCriterion("engine_position >", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionGreaterThanOrEqualTo(String value) {
            addCriterion("engine_position >=", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionLessThan(String value) {
            addCriterion("engine_position <", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionLessThanOrEqualTo(String value) {
            addCriterion("engine_position <=", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionLike(String value) {
            addCriterion("engine_position like", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionNotLike(String value) {
            addCriterion("engine_position not like", value, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionIn(List<String> values) {
            addCriterion("engine_position in", values, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionNotIn(List<String> values) {
            addCriterion("engine_position not in", values, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionBetween(String value1, String value2) {
            addCriterion("engine_position between", value1, value2, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andEnginePositionNotBetween(String value1, String value2) {
            addCriterion("engine_position not between", value1, value2, "enginePosition");
            return (Criteria) this;
        }

        public Criteria andDrivingModeIsNull() {
            addCriterion("driving_mode is null");
            return (Criteria) this;
        }

        public Criteria andDrivingModeIsNotNull() {
            addCriterion("driving_mode is not null");
            return (Criteria) this;
        }

        public Criteria andDrivingModeEqualTo(String value) {
            addCriterion("driving_mode =", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeNotEqualTo(String value) {
            addCriterion("driving_mode <>", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeGreaterThan(String value) {
            addCriterion("driving_mode >", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeGreaterThanOrEqualTo(String value) {
            addCriterion("driving_mode >=", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeLessThan(String value) {
            addCriterion("driving_mode <", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeLessThanOrEqualTo(String value) {
            addCriterion("driving_mode <=", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeLike(String value) {
            addCriterion("driving_mode like", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeNotLike(String value) {
            addCriterion("driving_mode not like", value, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeIn(List<String> values) {
            addCriterion("driving_mode in", values, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeNotIn(List<String> values) {
            addCriterion("driving_mode not in", values, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeBetween(String value1, String value2) {
            addCriterion("driving_mode between", value1, value2, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andDrivingModeNotBetween(String value1, String value2) {
            addCriterion("driving_mode not between", value1, value2, "drivingMode");
            return (Criteria) this;
        }

        public Criteria andWheelBaseIsNull() {
            addCriterion("wheel_base is null");
            return (Criteria) this;
        }

        public Criteria andWheelBaseIsNotNull() {
            addCriterion("wheel_base is not null");
            return (Criteria) this;
        }

        public Criteria andWheelBaseEqualTo(String value) {
            addCriterion("wheel_base =", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseNotEqualTo(String value) {
            addCriterion("wheel_base <>", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseGreaterThan(String value) {
            addCriterion("wheel_base >", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseGreaterThanOrEqualTo(String value) {
            addCriterion("wheel_base >=", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseLessThan(String value) {
            addCriterion("wheel_base <", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseLessThanOrEqualTo(String value) {
            addCriterion("wheel_base <=", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseLike(String value) {
            addCriterion("wheel_base like", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseNotLike(String value) {
            addCriterion("wheel_base not like", value, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseIn(List<String> values) {
            addCriterion("wheel_base in", values, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseNotIn(List<String> values) {
            addCriterion("wheel_base not in", values, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseBetween(String value1, String value2) {
            addCriterion("wheel_base between", value1, value2, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andWheelBaseNotBetween(String value1, String value2) {
            addCriterion("wheel_base not between", value1, value2, "wheelBase");
            return (Criteria) this;
        }

        public Criteria andDoorNumberIsNull() {
            addCriterion("door_number is null");
            return (Criteria) this;
        }

        public Criteria andDoorNumberIsNotNull() {
            addCriterion("door_number is not null");
            return (Criteria) this;
        }

        public Criteria andDoorNumberEqualTo(String value) {
            addCriterion("door_number =", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberNotEqualTo(String value) {
            addCriterion("door_number <>", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberGreaterThan(String value) {
            addCriterion("door_number >", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberGreaterThanOrEqualTo(String value) {
            addCriterion("door_number >=", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberLessThan(String value) {
            addCriterion("door_number <", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberLessThanOrEqualTo(String value) {
            addCriterion("door_number <=", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberLike(String value) {
            addCriterion("door_number like", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberNotLike(String value) {
            addCriterion("door_number not like", value, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberIn(List<String> values) {
            addCriterion("door_number in", values, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberNotIn(List<String> values) {
            addCriterion("door_number not in", values, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberBetween(String value1, String value2) {
            addCriterion("door_number between", value1, value2, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andDoorNumberNotBetween(String value1, String value2) {
            addCriterion("door_number not between", value1, value2, "doorNumber");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsIsNull() {
            addCriterion("number_of_seats is null");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsIsNotNull() {
            addCriterion("number_of_seats is not null");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsEqualTo(String value) {
            addCriterion("number_of_seats =", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsNotEqualTo(String value) {
            addCriterion("number_of_seats <>", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsGreaterThan(String value) {
            addCriterion("number_of_seats >", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsGreaterThanOrEqualTo(String value) {
            addCriterion("number_of_seats >=", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsLessThan(String value) {
            addCriterion("number_of_seats <", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsLessThanOrEqualTo(String value) {
            addCriterion("number_of_seats <=", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsLike(String value) {
            addCriterion("number_of_seats like", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsNotLike(String value) {
            addCriterion("number_of_seats not like", value, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsIn(List<String> values) {
            addCriterion("number_of_seats in", values, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsNotIn(List<String> values) {
            addCriterion("number_of_seats not in", values, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsBetween(String value1, String value2) {
            addCriterion("number_of_seats between", value1, value2, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andNumberOfSeatsNotBetween(String value1, String value2) {
            addCriterion("number_of_seats not between", value1, value2, "numberOfSeats");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeIsNull() {
            addCriterion("front_tire_size is null");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeIsNotNull() {
            addCriterion("front_tire_size is not null");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeEqualTo(String value) {
            addCriterion("front_tire_size =", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeNotEqualTo(String value) {
            addCriterion("front_tire_size <>", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeGreaterThan(String value) {
            addCriterion("front_tire_size >", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeGreaterThanOrEqualTo(String value) {
            addCriterion("front_tire_size >=", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeLessThan(String value) {
            addCriterion("front_tire_size <", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeLessThanOrEqualTo(String value) {
            addCriterion("front_tire_size <=", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeLike(String value) {
            addCriterion("front_tire_size like", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeNotLike(String value) {
            addCriterion("front_tire_size not like", value, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeIn(List<String> values) {
            addCriterion("front_tire_size in", values, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeNotIn(List<String> values) {
            addCriterion("front_tire_size not in", values, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeBetween(String value1, String value2) {
            addCriterion("front_tire_size between", value1, value2, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontTireSizeNotBetween(String value1, String value2) {
            addCriterion("front_tire_size not between", value1, value2, "frontTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeIsNull() {
            addCriterion("back_tire_size is null");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeIsNotNull() {
            addCriterion("back_tire_size is not null");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeEqualTo(String value) {
            addCriterion("back_tire_size =", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeNotEqualTo(String value) {
            addCriterion("back_tire_size <>", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeGreaterThan(String value) {
            addCriterion("back_tire_size >", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeGreaterThanOrEqualTo(String value) {
            addCriterion("back_tire_size >=", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeLessThan(String value) {
            addCriterion("back_tire_size <", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeLessThanOrEqualTo(String value) {
            addCriterion("back_tire_size <=", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeLike(String value) {
            addCriterion("back_tire_size like", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeNotLike(String value) {
            addCriterion("back_tire_size not like", value, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeIn(List<String> values) {
            addCriterion("back_tire_size in", values, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeNotIn(List<String> values) {
            addCriterion("back_tire_size not in", values, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeBetween(String value1, String value2) {
            addCriterion("back_tire_size between", value1, value2, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andBackTireSizeNotBetween(String value1, String value2) {
            addCriterion("back_tire_size not between", value1, value2, "backTireSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeIsNull() {
            addCriterion("front_boss_of_wheel_size is null");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeIsNotNull() {
            addCriterion("front_boss_of_wheel_size is not null");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeEqualTo(String value) {
            addCriterion("front_boss_of_wheel_size =", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeNotEqualTo(String value) {
            addCriterion("front_boss_of_wheel_size <>", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeGreaterThan(String value) {
            addCriterion("front_boss_of_wheel_size >", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeGreaterThanOrEqualTo(String value) {
            addCriterion("front_boss_of_wheel_size >=", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeLessThan(String value) {
            addCriterion("front_boss_of_wheel_size <", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeLessThanOrEqualTo(String value) {
            addCriterion("front_boss_of_wheel_size <=", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeLike(String value) {
            addCriterion("front_boss_of_wheel_size like", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeNotLike(String value) {
            addCriterion("front_boss_of_wheel_size not like", value, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeIn(List<String> values) {
            addCriterion("front_boss_of_wheel_size in", values, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeNotIn(List<String> values) {
            addCriterion("front_boss_of_wheel_size not in", values, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeBetween(String value1, String value2) {
            addCriterion("front_boss_of_wheel_size between", value1, value2, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andFrontBossOfWheelSizeNotBetween(String value1, String value2) {
            addCriterion("front_boss_of_wheel_size not between", value1, value2, "frontBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeIsNull() {
            addCriterion("back_boss_of_wheel_size is null");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeIsNotNull() {
            addCriterion("back_boss_of_wheel_size is not null");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeEqualTo(String value) {
            addCriterion("back_boss_of_wheel_size =", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeNotEqualTo(String value) {
            addCriterion("back_boss_of_wheel_size <>", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeGreaterThan(String value) {
            addCriterion("back_boss_of_wheel_size >", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeGreaterThanOrEqualTo(String value) {
            addCriterion("back_boss_of_wheel_size >=", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeLessThan(String value) {
            addCriterion("back_boss_of_wheel_size <", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeLessThanOrEqualTo(String value) {
            addCriterion("back_boss_of_wheel_size <=", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeLike(String value) {
            addCriterion("back_boss_of_wheel_size like", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeNotLike(String value) {
            addCriterion("back_boss_of_wheel_size not like", value, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeIn(List<String> values) {
            addCriterion("back_boss_of_wheel_size in", values, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeNotIn(List<String> values) {
            addCriterion("back_boss_of_wheel_size not in", values, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeBetween(String value1, String value2) {
            addCriterion("back_boss_of_wheel_size between", value1, value2, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andBackBossOfWheelSizeNotBetween(String value1, String value2) {
            addCriterion("back_boss_of_wheel_size not between", value1, value2, "backBossOfWheelSize");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialIsNull() {
            addCriterion("wheel_material is null");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialIsNotNull() {
            addCriterion("wheel_material is not null");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialEqualTo(String value) {
            addCriterion("wheel_material =", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialNotEqualTo(String value) {
            addCriterion("wheel_material <>", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialGreaterThan(String value) {
            addCriterion("wheel_material >", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("wheel_material >=", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialLessThan(String value) {
            addCriterion("wheel_material <", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialLessThanOrEqualTo(String value) {
            addCriterion("wheel_material <=", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialLike(String value) {
            addCriterion("wheel_material like", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialNotLike(String value) {
            addCriterion("wheel_material not like", value, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialIn(List<String> values) {
            addCriterion("wheel_material in", values, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialNotIn(List<String> values) {
            addCriterion("wheel_material not in", values, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialBetween(String value1, String value2) {
            addCriterion("wheel_material between", value1, value2, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andWheelMaterialNotBetween(String value1, String value2) {
            addCriterion("wheel_material not between", value1, value2, "wheelMaterial");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsIsNull() {
            addCriterion("spare_tire_specifications is null");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsIsNotNull() {
            addCriterion("spare_tire_specifications is not null");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsEqualTo(String value) {
            addCriterion("spare_tire_specifications =", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsNotEqualTo(String value) {
            addCriterion("spare_tire_specifications <>", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsGreaterThan(String value) {
            addCriterion("spare_tire_specifications >", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsGreaterThanOrEqualTo(String value) {
            addCriterion("spare_tire_specifications >=", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsLessThan(String value) {
            addCriterion("spare_tire_specifications <", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsLessThanOrEqualTo(String value) {
            addCriterion("spare_tire_specifications <=", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsLike(String value) {
            addCriterion("spare_tire_specifications like", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsNotLike(String value) {
            addCriterion("spare_tire_specifications not like", value, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsIn(List<String> values) {
            addCriterion("spare_tire_specifications in", values, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsNotIn(List<String> values) {
            addCriterion("spare_tire_specifications not in", values, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsBetween(String value1, String value2) {
            addCriterion("spare_tire_specifications between", value1, value2, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andSpareTireSpecificationsNotBetween(String value1, String value2) {
            addCriterion("spare_tire_specifications not between", value1, value2, "spareTireSpecifications");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightIsNull() {
            addCriterion("electric_skylight is null");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightIsNotNull() {
            addCriterion("electric_skylight is not null");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightEqualTo(String value) {
            addCriterion("electric_skylight =", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightNotEqualTo(String value) {
            addCriterion("electric_skylight <>", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightGreaterThan(String value) {
            addCriterion("electric_skylight >", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightGreaterThanOrEqualTo(String value) {
            addCriterion("electric_skylight >=", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightLessThan(String value) {
            addCriterion("electric_skylight <", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightLessThanOrEqualTo(String value) {
            addCriterion("electric_skylight <=", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightLike(String value) {
            addCriterion("electric_skylight like", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightNotLike(String value) {
            addCriterion("electric_skylight not like", value, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightIn(List<String> values) {
            addCriterion("electric_skylight in", values, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightNotIn(List<String> values) {
            addCriterion("electric_skylight not in", values, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightBetween(String value1, String value2) {
            addCriterion("electric_skylight between", value1, value2, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andElectricSkylightNotBetween(String value1, String value2) {
            addCriterion("electric_skylight not between", value1, value2, "electricSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightIsNull() {
            addCriterion("panoramic_skylight is null");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightIsNotNull() {
            addCriterion("panoramic_skylight is not null");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightEqualTo(String value) {
            addCriterion("panoramic_skylight =", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightNotEqualTo(String value) {
            addCriterion("panoramic_skylight <>", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightGreaterThan(String value) {
            addCriterion("panoramic_skylight >", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightGreaterThanOrEqualTo(String value) {
            addCriterion("panoramic_skylight >=", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightLessThan(String value) {
            addCriterion("panoramic_skylight <", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightLessThanOrEqualTo(String value) {
            addCriterion("panoramic_skylight <=", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightLike(String value) {
            addCriterion("panoramic_skylight like", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightNotLike(String value) {
            addCriterion("panoramic_skylight not like", value, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightIn(List<String> values) {
            addCriterion("panoramic_skylight in", values, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightNotIn(List<String> values) {
            addCriterion("panoramic_skylight not in", values, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightBetween(String value1, String value2) {
            addCriterion("panoramic_skylight between", value1, value2, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andPanoramicSkylightNotBetween(String value1, String value2) {
            addCriterion("panoramic_skylight not between", value1, value2, "panoramicSkylight");
            return (Criteria) this;
        }

        public Criteria andXenonLampIsNull() {
            addCriterion("xenon_lamp is null");
            return (Criteria) this;
        }

        public Criteria andXenonLampIsNotNull() {
            addCriterion("xenon_lamp is not null");
            return (Criteria) this;
        }

        public Criteria andXenonLampEqualTo(String value) {
            addCriterion("xenon_lamp =", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampNotEqualTo(String value) {
            addCriterion("xenon_lamp <>", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampGreaterThan(String value) {
            addCriterion("xenon_lamp >", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampGreaterThanOrEqualTo(String value) {
            addCriterion("xenon_lamp >=", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampLessThan(String value) {
            addCriterion("xenon_lamp <", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampLessThanOrEqualTo(String value) {
            addCriterion("xenon_lamp <=", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampLike(String value) {
            addCriterion("xenon_lamp like", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampNotLike(String value) {
            addCriterion("xenon_lamp not like", value, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampIn(List<String> values) {
            addCriterion("xenon_lamp in", values, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampNotIn(List<String> values) {
            addCriterion("xenon_lamp not in", values, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampBetween(String value1, String value2) {
            addCriterion("xenon_lamp between", value1, value2, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andXenonLampNotBetween(String value1, String value2) {
            addCriterion("xenon_lamp not between", value1, value2, "xenonLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampIsNull() {
            addCriterion("front_fog_lamp is null");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampIsNotNull() {
            addCriterion("front_fog_lamp is not null");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampEqualTo(String value) {
            addCriterion("front_fog_lamp =", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampNotEqualTo(String value) {
            addCriterion("front_fog_lamp <>", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampGreaterThan(String value) {
            addCriterion("front_fog_lamp >", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampGreaterThanOrEqualTo(String value) {
            addCriterion("front_fog_lamp >=", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampLessThan(String value) {
            addCriterion("front_fog_lamp <", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampLessThanOrEqualTo(String value) {
            addCriterion("front_fog_lamp <=", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampLike(String value) {
            addCriterion("front_fog_lamp like", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampNotLike(String value) {
            addCriterion("front_fog_lamp not like", value, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampIn(List<String> values) {
            addCriterion("front_fog_lamp in", values, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampNotIn(List<String> values) {
            addCriterion("front_fog_lamp not in", values, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampBetween(String value1, String value2) {
            addCriterion("front_fog_lamp between", value1, value2, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andFrontFogLampNotBetween(String value1, String value2) {
            addCriterion("front_fog_lamp not between", value1, value2, "frontFogLamp");
            return (Criteria) this;
        }

        public Criteria andRearWiperIsNull() {
            addCriterion("rear_wiper is null");
            return (Criteria) this;
        }

        public Criteria andRearWiperIsNotNull() {
            addCriterion("rear_wiper is not null");
            return (Criteria) this;
        }

        public Criteria andRearWiperEqualTo(String value) {
            addCriterion("rear_wiper =", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperNotEqualTo(String value) {
            addCriterion("rear_wiper <>", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperGreaterThan(String value) {
            addCriterion("rear_wiper >", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperGreaterThanOrEqualTo(String value) {
            addCriterion("rear_wiper >=", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperLessThan(String value) {
            addCriterion("rear_wiper <", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperLessThanOrEqualTo(String value) {
            addCriterion("rear_wiper <=", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperLike(String value) {
            addCriterion("rear_wiper like", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperNotLike(String value) {
            addCriterion("rear_wiper not like", value, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperIn(List<String> values) {
            addCriterion("rear_wiper in", values, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperNotIn(List<String> values) {
            addCriterion("rear_wiper not in", values, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperBetween(String value1, String value2) {
            addCriterion("rear_wiper between", value1, value2, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andRearWiperNotBetween(String value1, String value2) {
            addCriterion("rear_wiper not between", value1, value2, "rearWiper");
            return (Criteria) this;
        }

        public Criteria andAirConditionerIsNull() {
            addCriterion("air_conditioner is null");
            return (Criteria) this;
        }

        public Criteria andAirConditionerIsNotNull() {
            addCriterion("air_conditioner is not null");
            return (Criteria) this;
        }

        public Criteria andAirConditionerEqualTo(String value) {
            addCriterion("air_conditioner =", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerNotEqualTo(String value) {
            addCriterion("air_conditioner <>", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerGreaterThan(String value) {
            addCriterion("air_conditioner >", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerGreaterThanOrEqualTo(String value) {
            addCriterion("air_conditioner >=", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerLessThan(String value) {
            addCriterion("air_conditioner <", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerLessThanOrEqualTo(String value) {
            addCriterion("air_conditioner <=", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerLike(String value) {
            addCriterion("air_conditioner like", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerNotLike(String value) {
            addCriterion("air_conditioner not like", value, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerIn(List<String> values) {
            addCriterion("air_conditioner in", values, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerNotIn(List<String> values) {
            addCriterion("air_conditioner not in", values, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerBetween(String value1, String value2) {
            addCriterion("air_conditioner between", value1, value2, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAirConditionerNotBetween(String value1, String value2) {
            addCriterion("air_conditioner not between", value1, value2, "airConditioner");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningIsNull() {
            addCriterion("auto_air_conditioning is null");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningIsNotNull() {
            addCriterion("auto_air_conditioning is not null");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningEqualTo(String value) {
            addCriterion("auto_air_conditioning =", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningNotEqualTo(String value) {
            addCriterion("auto_air_conditioning <>", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningGreaterThan(String value) {
            addCriterion("auto_air_conditioning >", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningGreaterThanOrEqualTo(String value) {
            addCriterion("auto_air_conditioning >=", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningLessThan(String value) {
            addCriterion("auto_air_conditioning <", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningLessThanOrEqualTo(String value) {
            addCriterion("auto_air_conditioning <=", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningLike(String value) {
            addCriterion("auto_air_conditioning like", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningNotLike(String value) {
            addCriterion("auto_air_conditioning not like", value, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningIn(List<String> values) {
            addCriterion("auto_air_conditioning in", values, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningNotIn(List<String> values) {
            addCriterion("auto_air_conditioning not in", values, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningBetween(String value1, String value2) {
            addCriterion("auto_air_conditioning between", value1, value2, "autoAirConditioning");
            return (Criteria) this;
        }

        public Criteria andAutoAirConditioningNotBetween(String value1, String value2) {
            addCriterion("auto_air_conditioning not between", value1, value2, "autoAirConditioning");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}