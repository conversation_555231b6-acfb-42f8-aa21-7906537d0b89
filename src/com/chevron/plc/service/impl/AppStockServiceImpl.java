package com.chevron.plc.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.common.exception.auth.WxAuthException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chevron.plc.dao.InStockProductVoMapper;
import com.chevron.plc.dao.InStockVoMapper;
import com.chevron.plc.dao.OutStockProductVoMapper;
import com.chevron.plc.dao.OutStockVoMapper;
import com.chevron.plc.dao.StockDetailVoMapper;
import com.chevron.plc.model.InStockProductDetaiView;
import com.chevron.plc.model.InStockVo;
import com.chevron.plc.model.LogisticsStockVo;
import com.chevron.plc.model.OutStockProductDetaiView;
import com.chevron.plc.model.OutStockVo;
import com.chevron.plc.service.AppStockService;
import com.common.exception.WxPltException;
import com.common.util.StringUtils;

@Service
public class AppStockServiceImpl implements AppStockService {
	@Autowired
	private OutStockVoMapper outStockMapper;
	@Autowired
	private OutStockProductVoMapper outStockProductMapper;
	@Autowired
	private InStockVoMapper inStockMapper;
	@Autowired
	private InStockProductVoMapper inStockProductMapper;
	@Autowired
	private StockDetailVoMapper stockDetailVoMapper;
	
	public final static String SUCCESS_CODE = "success";
	public final static String FAIL_CODE = "fail";
	public final static String ERROR_CODE = "error";

	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_ERROR_MSG_KEY = "codeMsg";
	
	public final static Logger log = Logger.getLogger(AppStockServiceImpl.class);

	@Override
	public Map<String, Object> getStockInfoByStockNo(String stockNo) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(StringUtils.isBlank(stockNo)){
				throw new WxPltException("出库(或入库)单号为空");
			}else if(stockNo.length() == 11 && stockNo.startsWith("2")){
				//入库单
				map.put("type", 2);
				InStockVo instock = inStockMapper.queryInStockByStockInNo(stockNo);
				if(instock == null){
					throw new WxPltException("未找到入库单号" + stockNo + "对应的入库信息");
				}
				List<InStockProductDetaiView> productList = inStockProductMapper.selectInStockProductByStockInNo(stockNo);
				map.put("stock",instock);
				map.put("productList",productList);
			}else{
				//出库
				map.put("type", 1);
				OutStockVo outstock = outStockMapper.queryOutStockByStockNo(stockNo);
				if(outstock == null){
					throw new WxPltException("未找到出库单号" + stockNo + "对应的出库信息");
				}
				List<OutStockProductDetaiView> productList = outStockProductMapper.selectOutStockProductByStockOutNo(stockNo);
				map.put("stock",outstock);
				map.put("productList",productList);
			}
			map.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (WxPltException e) {
			log.error(e.getMessage());
			map.put(RESULT_CODE_KEY, ERROR_CODE);
			map.put(RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			log.error(e);
			map.put(RESULT_CODE_KEY, ERROR_CODE);
			map.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return map;
	}

	@Override
	public Map<String, Object> getStockDetailByCode(String code) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			List<LogisticsStockVo> detailList = stockDetailVoMapper.selectStockInfoByCode(code);
			if(detailList.size()>0) {
				resultMap.put("detailList", detailList);
				resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			} else {
				resultMap.put(RESULT_CODE_KEY,ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "没找到该物流码");
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY,WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}
}
