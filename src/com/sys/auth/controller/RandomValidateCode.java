package com.sys.auth.controller;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.util.Random;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.common.constants.Constants;
import com.redis.tockenMgnt.service.TokenMgntService;

@Controller
public class RandomValidateCode {
	public static final String RANDOMCODEKEY = "RANDOMVALIDATECODEKEY";//放到session中的key
	private Random random = new Random();
	private String randString = "23456789ABCDEFGHKMNPRSTUVWXYZ";//随机产生的字符串

	private int width = 82;//图片宽
	private int height = 26;//图片高
	private int lineSize = 40;//干扰线数量
	private int stringNum = 4;//随机产生字符数量
	
	@Resource
	private TokenMgntService tokenMgntServicelmpl;
	
	@ResponseBody
	@RequestMapping(value = "/checkValidateCode.action", method = RequestMethod.POST)
	public Object checkRandCode(@RequestParam("validateCode") String userInputCode,HttpServletRequest request, HttpServletResponse response) throws Exception {
        if ("false".equals(Constants.getSystemPropertyByCodeType(Constants.ENV_IS_PROD))){
        	return 1;
        }
        HttpSession session = request.getSession();
        /*String validateCode =  (String)session.getAttribute(RANDOMCODEKEY);*/
        String validateCode = tokenMgntServicelmpl.getValidataCodeBySessionId(session.getId());
        if(validateCode == null){
        	return 0;
        }
        if(validateCode.equalsIgnoreCase(userInputCode)){
        	return 1;
        }
        else return 0;
    }
	
	@ResponseBody
	@RequestMapping(value = "/RandomValidateCode.action", method = RequestMethod.GET)
	public void getRandcode(HttpServletResponse response) throws Exception {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = request.getSession();
        
        //BufferedImage类是具有缓冲区的Image类,Image类是用于描述图像信息的类
        BufferedImage image = new BufferedImage(width,height,BufferedImage.TYPE_INT_BGR);
        Graphics g = image.getGraphics();//产生Image对象的Graphics对象,改对象可以在图像上进行各种绘制操作
        g.fillRect(0, 0, width, height);
        g.setFont(new Font("Times New Roman",Font.ROMAN_BASELINE,18));
        g.setColor(getRandColor(20, 230));
        //绘制干扰线
        for(int i=0;i<=lineSize;i++){
            drowLine(g);
        }
        //绘制随机字符
        String randomString = "";
        for(int i=1;i<=stringNum;i++){
            randomString=drowString(g,randomString,i);
        }
        /* session.removeAttribute(RANDOMCODEKEY);
        session.setAttribute(RANDOMCODEKEY, randomString);*/
        tokenMgntServicelmpl.setSessionId2ValidataCode(session.getId(), randomString);
        //System.out.println(randomString);
        //System.out.println(randomString);
        session.setAttribute("ValidationCode", randomString);
        g.dispose();
        try {
            ImageIO.write(image, "JPEG", response.getOutputStream());//将内存中的图片通过流动形式输出到客户端
        } catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	/*
     * 获得字体
     */
    private Font getFont(){
        return new Font("Hei",Font.CENTER_BASELINE,26);//字体信息
    }
    /*
     * 获得颜色
     */
    private Color getRandColor(int fc,int bc){
        if(fc > 255)
            fc = 255;
        if(bc > 255)
            bc = 255;
        int r = fc + random.nextInt(bc-fc-16);
        int g = fc + random.nextInt(bc-fc-14);
        int b = fc + random.nextInt(bc-fc-18);
        return new Color(r,g,b);
    }
    /*
     * 绘制字符串
     */
    private String drowString(Graphics g,String randomString,int i){
        g.setFont(getFont());
        g.setColor(new Color(random.nextInt(30),random.nextInt(200),random.nextInt(121)));
        String rand = String.valueOf(getRandomString(random.nextInt(randString.length())));
        randomString +=rand;
        g.translate(random.nextInt(3), random.nextInt(3));
        g.drawString(rand, 15*i-10, 20);//后两个参数是x轴位置，y轴位置
        return randomString;
    }
    /*
     * 绘制干扰线
     */
    private void drowLine(Graphics g){
        int x = random.nextInt(width);
        int y = random.nextInt(height);
        int xl = random.nextInt(13);
        int yl = random.nextInt(15);
        g.drawLine(x, y, x+xl, y+yl);
    }
    /*
     * 获取随机的字符
     */
    public String getRandomString(int num){
        return String.valueOf(randString.charAt(num));
    }
		
}
