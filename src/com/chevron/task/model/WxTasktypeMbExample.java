package com.chevron.task.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxTasktypeMbExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public WxTasktypeMbExample() {
		oredCriteria = new ArrayList<Criteria>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<Criterion>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value,
				String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property
						+ " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1,
				Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property
						+ " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andTmbIdIsNull() {
			addCriterion("tmb_id is null");
			return (Criteria) this;
		}

		public Criteria andTmbIdIsNotNull() {
			addCriterion("tmb_id is not null");
			return (Criteria) this;
		}

		public Criteria andTmbIdEqualTo(Long value) {
			addCriterion("tmb_id =", value, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdNotEqualTo(Long value) {
			addCriterion("tmb_id <>", value, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdGreaterThan(Long value) {
			addCriterion("tmb_id >", value, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdGreaterThanOrEqualTo(Long value) {
			addCriterion("tmb_id >=", value, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdLessThan(Long value) {
			addCriterion("tmb_id <", value, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdLessThanOrEqualTo(Long value) {
			addCriterion("tmb_id <=", value, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdIn(List<Long> values) {
			addCriterion("tmb_id in", values, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdNotIn(List<Long> values) {
			addCriterion("tmb_id not in", values, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdBetween(Long value1, Long value2) {
			addCriterion("tmb_id between", value1, value2, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbIdNotBetween(Long value1, Long value2) {
			addCriterion("tmb_id not between", value1, value2, "tmbId");
			return (Criteria) this;
		}

		public Criteria andTmbNameIsNull() {
			addCriterion("tmb_name is null");
			return (Criteria) this;
		}

		public Criteria andTmbNameIsNotNull() {
			addCriterion("tmb_name is not null");
			return (Criteria) this;
		}

		public Criteria andTmbNameEqualTo(String value) {
			addCriterion("tmb_name =", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameNotEqualTo(String value) {
			addCriterion("tmb_name <>", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameGreaterThan(String value) {
			addCriterion("tmb_name >", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameGreaterThanOrEqualTo(String value) {
			addCriterion("tmb_name >=", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameLessThan(String value) {
			addCriterion("tmb_name <", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameLessThanOrEqualTo(String value) {
			addCriterion("tmb_name <=", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameLike(String value) {
			addCriterion("tmb_name like", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameNotLike(String value) {
			addCriterion("tmb_name not like", value, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameIn(List<String> values) {
			addCriterion("tmb_name in", values, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameNotIn(List<String> values) {
			addCriterion("tmb_name not in", values, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameBetween(String value1, String value2) {
			addCriterion("tmb_name between", value1, value2, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbNameNotBetween(String value1, String value2) {
			addCriterion("tmb_name not between", value1, value2, "tmbName");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeIsNull() {
			addCriterion("tmb_type_code is null");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeIsNotNull() {
			addCriterion("tmb_type_code is not null");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeEqualTo(String value) {
			addCriterion("tmb_type_code =", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeNotEqualTo(String value) {
			addCriterion("tmb_type_code <>", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeGreaterThan(String value) {
			addCriterion("tmb_type_code >", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeGreaterThanOrEqualTo(String value) {
			addCriterion("tmb_type_code >=", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeLessThan(String value) {
			addCriterion("tmb_type_code <", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeLessThanOrEqualTo(String value) {
			addCriterion("tmb_type_code <=", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeLike(String value) {
			addCriterion("tmb_type_code like", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeNotLike(String value) {
			addCriterion("tmb_type_code not like", value, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeIn(List<String> values) {
			addCriterion("tmb_type_code in", values, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeNotIn(List<String> values) {
			addCriterion("tmb_type_code not in", values, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeBetween(String value1, String value2) {
			addCriterion("tmb_type_code between", value1, value2, "tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbTypeCodeNotBetween(String value1, String value2) {
			addCriterion("tmb_type_code not between", value1, value2,
					"tmbTypeCode");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyIsNull() {
			addCriterion("tmb_classify is null");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyIsNotNull() {
			addCriterion("tmb_classify is not null");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyEqualTo(Integer value) {
			addCriterion("tmb_classify =", value, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyNotEqualTo(Integer value) {
			addCriterion("tmb_classify <>", value, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyGreaterThan(Integer value) {
			addCriterion("tmb_classify >", value, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyGreaterThanOrEqualTo(Integer value) {
			addCriterion("tmb_classify >=", value, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyLessThan(Integer value) {
			addCriterion("tmb_classify <", value, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyLessThanOrEqualTo(Integer value) {
			addCriterion("tmb_classify <=", value, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyIn(List<Integer> values) {
			addCriterion("tmb_classify in", values, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyNotIn(List<Integer> values) {
			addCriterion("tmb_classify not in", values, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyBetween(Integer value1, Integer value2) {
			addCriterion("tmb_classify between", value1, value2, "tmbClassify");
			return (Criteria) this;
		}

		public Criteria andTmbClassifyNotBetween(Integer value1, Integer value2) {
			addCriterion("tmb_classify not between", value1, value2,
					"tmbClassify");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeIsNull() {
			addCriterion("exec_user_type is null");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeIsNotNull() {
			addCriterion("exec_user_type is not null");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeEqualTo(Integer value) {
			addCriterion("exec_user_type =", value, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeNotEqualTo(Integer value) {
			addCriterion("exec_user_type <>", value, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeGreaterThan(Integer value) {
			addCriterion("exec_user_type >", value, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeGreaterThanOrEqualTo(Integer value) {
			addCriterion("exec_user_type >=", value, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeLessThan(Integer value) {
			addCriterion("exec_user_type <", value, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeLessThanOrEqualTo(Integer value) {
			addCriterion("exec_user_type <=", value, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeIn(List<Integer> values) {
			addCriterion("exec_user_type in", values, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeNotIn(List<Integer> values) {
			addCriterion("exec_user_type not in", values, "execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeBetween(Integer value1, Integer value2) {
			addCriterion("exec_user_type between", value1, value2,
					"execUserType");
			return (Criteria) this;
		}

		public Criteria andExecUserTypeNotBetween(Integer value1, Integer value2) {
			addCriterion("exec_user_type not between", value1, value2,
					"execUserType");
			return (Criteria) this;
		}

		public Criteria andCheckStatusIsNull() {
			addCriterion("check_status is null");
			return (Criteria) this;
		}

		public Criteria andCheckStatusIsNotNull() {
			addCriterion("check_status is not null");
			return (Criteria) this;
		}

		public Criteria andCheckStatusEqualTo(Integer value) {
			addCriterion("check_status =", value, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusNotEqualTo(Integer value) {
			addCriterion("check_status <>", value, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusGreaterThan(Integer value) {
			addCriterion("check_status >", value, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusGreaterThanOrEqualTo(Integer value) {
			addCriterion("check_status >=", value, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusLessThan(Integer value) {
			addCriterion("check_status <", value, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusLessThanOrEqualTo(Integer value) {
			addCriterion("check_status <=", value, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusIn(List<Integer> values) {
			addCriterion("check_status in", values, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusNotIn(List<Integer> values) {
			addCriterion("check_status not in", values, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusBetween(Integer value1, Integer value2) {
			addCriterion("check_status between", value1, value2, "checkStatus");
			return (Criteria) this;
		}

		public Criteria andCheckStatusNotBetween(Integer value1, Integer value2) {
			addCriterion("check_status not between", value1, value2,
					"checkStatus");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeIsNull() {
			addCriterion("inner_org_type is null");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeIsNotNull() {
			addCriterion("inner_org_type is not null");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeEqualTo(Integer value) {
			addCriterion("inner_org_type =", value, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeNotEqualTo(Integer value) {
			addCriterion("inner_org_type <>", value, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeGreaterThan(Integer value) {
			addCriterion("inner_org_type >", value, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeGreaterThanOrEqualTo(Integer value) {
			addCriterion("inner_org_type >=", value, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeLessThan(Integer value) {
			addCriterion("inner_org_type <", value, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeLessThanOrEqualTo(Integer value) {
			addCriterion("inner_org_type <=", value, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeIn(List<Integer> values) {
			addCriterion("inner_org_type in", values, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeNotIn(List<Integer> values) {
			addCriterion("inner_org_type not in", values, "innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeBetween(Integer value1, Integer value2) {
			addCriterion("inner_org_type between", value1, value2,
					"innerOrgType");
			return (Criteria) this;
		}

		public Criteria andInnerOrgTypeNotBetween(Integer value1, Integer value2) {
			addCriterion("inner_org_type not between", value1, value2,
					"innerOrgType");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusIsNull() {
			addCriterion("check_item_status is null");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusIsNotNull() {
			addCriterion("check_item_status is not null");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusEqualTo(Integer value) {
			addCriterion("check_item_status =", value, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusNotEqualTo(Integer value) {
			addCriterion("check_item_status <>", value, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusGreaterThan(Integer value) {
			addCriterion("check_item_status >", value, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusGreaterThanOrEqualTo(Integer value) {
			addCriterion("check_item_status >=", value, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusLessThan(Integer value) {
			addCriterion("check_item_status <", value, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusLessThanOrEqualTo(Integer value) {
			addCriterion("check_item_status <=", value, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusIn(List<Integer> values) {
			addCriterion("check_item_status in", values, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusNotIn(List<Integer> values) {
			addCriterion("check_item_status not in", values, "checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusBetween(Integer value1, Integer value2) {
			addCriterion("check_item_status between", value1, value2,
					"checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckItemStatusNotBetween(Integer value1,
				Integer value2) {
			addCriterion("check_item_status not between", value1, value2,
					"checkItemStatus");
			return (Criteria) this;
		}

		public Criteria andCheckIdIsNull() {
			addCriterion("check_id is null");
			return (Criteria) this;
		}

		public Criteria andCheckIdIsNotNull() {
			addCriterion("check_id is not null");
			return (Criteria) this;
		}

		public Criteria andCheckIdEqualTo(Long value) {
			addCriterion("check_id =", value, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdNotEqualTo(Long value) {
			addCriterion("check_id <>", value, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdGreaterThan(Long value) {
			addCriterion("check_id >", value, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdGreaterThanOrEqualTo(Long value) {
			addCriterion("check_id >=", value, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdLessThan(Long value) {
			addCriterion("check_id <", value, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdLessThanOrEqualTo(Long value) {
			addCriterion("check_id <=", value, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdIn(List<Long> values) {
			addCriterion("check_id in", values, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdNotIn(List<Long> values) {
			addCriterion("check_id not in", values, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdBetween(Long value1, Long value2) {
			addCriterion("check_id between", value1, value2, "checkId");
			return (Criteria) this;
		}

		public Criteria andCheckIdNotBetween(Long value1, Long value2) {
			addCriterion("check_id not between", value1, value2, "checkId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIsNull() {
			addCriterion("create_user is null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIsNotNull() {
			addCriterion("create_user is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUserEqualTo(Long value) {
			addCriterion("create_user =", value, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserNotEqualTo(Long value) {
			addCriterion("create_user <>", value, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserGreaterThan(Long value) {
			addCriterion("create_user >", value, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserGreaterThanOrEqualTo(Long value) {
			addCriterion("create_user >=", value, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserLessThan(Long value) {
			addCriterion("create_user <", value, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserLessThanOrEqualTo(Long value) {
			addCriterion("create_user <=", value, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserIn(List<Long> values) {
			addCriterion("create_user in", values, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserNotIn(List<Long> values) {
			addCriterion("create_user not in", values, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserBetween(Long value1, Long value2) {
			addCriterion("create_user between", value1, value2, "createUser");
			return (Criteria) this;
		}

		public Criteria andCreateUserNotBetween(Long value1, Long value2) {
			addCriterion("create_user not between", value1, value2,
					"createUser");
			return (Criteria) this;
		}

		public Criteria andXgSjIsNull() {
			addCriterion("xg_sj is null");
			return (Criteria) this;
		}

		public Criteria andXgSjIsNotNull() {
			addCriterion("xg_sj is not null");
			return (Criteria) this;
		}

		public Criteria andXgSjEqualTo(Date value) {
			addCriterion("xg_sj =", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjNotEqualTo(Date value) {
			addCriterion("xg_sj <>", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjGreaterThan(Date value) {
			addCriterion("xg_sj >", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjGreaterThanOrEqualTo(Date value) {
			addCriterion("xg_sj >=", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjLessThan(Date value) {
			addCriterion("xg_sj <", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjLessThanOrEqualTo(Date value) {
			addCriterion("xg_sj <=", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjIn(List<Date> values) {
			addCriterion("xg_sj in", values, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjNotIn(List<Date> values) {
			addCriterion("xg_sj not in", values, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjBetween(Date value1, Date value2) {
			addCriterion("xg_sj between", value1, value2, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjNotBetween(Date value1, Date value2) {
			addCriterion("xg_sj not between", value1, value2, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgUserIsNull() {
			addCriterion("xg_user is null");
			return (Criteria) this;
		}

		public Criteria andXgUserIsNotNull() {
			addCriterion("xg_user is not null");
			return (Criteria) this;
		}

		public Criteria andXgUserEqualTo(Long value) {
			addCriterion("xg_user =", value, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserNotEqualTo(Long value) {
			addCriterion("xg_user <>", value, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserGreaterThan(Long value) {
			addCriterion("xg_user >", value, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserGreaterThanOrEqualTo(Long value) {
			addCriterion("xg_user >=", value, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserLessThan(Long value) {
			addCriterion("xg_user <", value, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserLessThanOrEqualTo(Long value) {
			addCriterion("xg_user <=", value, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserIn(List<Long> values) {
			addCriterion("xg_user in", values, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserNotIn(List<Long> values) {
			addCriterion("xg_user not in", values, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserBetween(Long value1, Long value2) {
			addCriterion("xg_user between", value1, value2, "xgUser");
			return (Criteria) this;
		}

		public Criteria andXgUserNotBetween(Long value1, Long value2) {
			addCriterion("xg_user not between", value1, value2, "xgUser");
			return (Criteria) this;
		}

		public Criteria andStatusIsNull() {
			addCriterion("status is null");
			return (Criteria) this;
		}

		public Criteria andStatusIsNotNull() {
			addCriterion("status is not null");
			return (Criteria) this;
		}

		public Criteria andStatusEqualTo(Integer value) {
			addCriterion("status =", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotEqualTo(Integer value) {
			addCriterion("status <>", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusGreaterThan(Integer value) {
			addCriterion("status >", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
			addCriterion("status >=", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusLessThan(Integer value) {
			addCriterion("status <", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusLessThanOrEqualTo(Integer value) {
			addCriterion("status <=", value, "status");
			return (Criteria) this;
		}

		public Criteria andStatusIn(List<Integer> values) {
			addCriterion("status in", values, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotIn(List<Integer> values) {
			addCriterion("status not in", values, "status");
			return (Criteria) this;
		}

		public Criteria andStatusBetween(Integer value1, Integer value2) {
			addCriterion("status between", value1, value2, "status");
			return (Criteria) this;
		}

		public Criteria andStatusNotBetween(Integer value1, Integer value2) {
			addCriterion("status not between", value1, value2, "status");
			return (Criteria) this;
		}

		public Criteria andTenantIdIsNull() {
			addCriterion("tenant_id is null");
			return (Criteria) this;
		}

		public Criteria andTenantIdIsNotNull() {
			addCriterion("tenant_id is not null");
			return (Criteria) this;
		}

		public Criteria andTenantIdEqualTo(Long value) {
			addCriterion("tenant_id =", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdNotEqualTo(Long value) {
			addCriterion("tenant_id <>", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdGreaterThan(Long value) {
			addCriterion("tenant_id >", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
			addCriterion("tenant_id >=", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdLessThan(Long value) {
			addCriterion("tenant_id <", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdLessThanOrEqualTo(Long value) {
			addCriterion("tenant_id <=", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdIn(List<Long> values) {
			addCriterion("tenant_id in", values, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdNotIn(List<Long> values) {
			addCriterion("tenant_id not in", values, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdBetween(Long value1, Long value2) {
			addCriterion("tenant_id between", value1, value2, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdNotBetween(Long value1, Long value2) {
			addCriterion("tenant_id not between", value1, value2, "tenantId");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table wx_tasktype_mb
	 * @mbggenerated  Wed Jul 15 15:37:50 CST 2015
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue,
				String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wx_tasktype_mb
     *
     * @mbggenerated do_not_delete_during_merge Tue Jul 14 17:48:13 CST 2015
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }
}