package com.chevron.report.business.impl;

import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.util.Streams;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.chevron.report.business.MtdBizService;
import com.chevron.report.model.MtdEmailConfig;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.EmailSendUtils;
import com.common.util.StringUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.auth.model.WxTPropertiesExample;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.file.web.FileManager;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.LockerBizService;
import com.sys.utils.business.ReportViewBizService;
import com.sys.utils.model.AsynProcessStatus;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

@Service
public class MtdBizServiceImpl implements MtdBizService {
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	@Resource
	private JdbcTemplate wxJdbcTemplate;
	
	@Autowired
	private WxTPropertiesMapper wxTPropertiesMapper;
	
	private final static String EMAIL_CONFIG_TYPE_CODE = "mtd.email";
//	
//	@Autowired
//	private PartnerResponsibleVoMapper partnerResponsibleVoMapper;

	@Resource
	private DicService dicServiceImpl;
	
	@Resource
	private DicItemVoMapper dicItemVoMapper;
	
	@Autowired
	private LockerBizService lockerBizService;
	
	private final static String LOCKER_ID = "MTD.sendReport";
	
	private final static String LOCKER_NAME = "发送MTD报表";
	
	private final static int LOCK_EFFICTIVE_TIME = 600;//10分钟
	
	private final static String CAI_ALL = "ALL";
	
	public final static String MTD_FINAL_TEMPLATE = "mtd_report_email.ftl";
	
	public final static String MTD_NOT_FINAL_TEMPLATE = "mtd_report_email_not_final.ftl";
	public final static String MTD_FINAL_TEMPLATE_GQ = "mtd_report_email_gq.ftl";
	public final static String MTD_NOT_FINAL_TEMPLATE_GQ = "mtd_report_email_not_final_gq.ftl";
	
	private static Map<String, String> ftlTempMap = new HashMap<String, String>();
	private static Map<String, String> gQftlTempMap = new HashMap<String, String>();
	static {
//		ftlTempMap.put("xl/pivotTables/pivotTable2.xml", "mtd_ftl_pivotTable2.ftl");
//		ftlTempMap.put("xl/pivotTables/pivotTable3.xml", "mtd_ftl_pivotTable3.ftl");
//		ftlTempMap.put("xl/pivotTables/pivotTable4.xml", "mtd_ftl_pivotTable4.ftl");
//		ftlTempMap.put("xl/pivotTables/pivotTable5.xml", "mtd_ftl_pivotTable5.ftl");
//		ftlTempMap.put("xl/pivotTables/pivotTable6.xml", "mtd_ftl_pivotTable6.ftl");
//		ftlTempMap.put("xl/pivotTables/pivotTable7.xml", "mtd_ftl_pivotTable7.ftl");
//		ftlTempMap.put("xl/pivotCache/pivotCacheDefinition1.xml", "mtd_ftl_pivotCacheDefinition1.ftl");
//		ftlTempMap.put("xl/pivotCache/pivotCacheDefinition2.xml", "mtd_ftl_pivotCacheDefinition2.ftl");
		ftlTempMap.put("xl/tables/table1.xml", "mtd_ftl_table1.ftl");
		ftlTempMap.put("xl/sharedStrings.xml", "mtd_ftl_sharedStrings.ftl");
//		gQftlTempMap.put("xl/tables/table1.xml", "mtd_ftl_table1.ftl");
		gQftlTempMap.put("xl/sharedStrings.xml", "mtd_ftl_sharedStrings_gq.ftl");
	}
	
	private Logger logger = LoggerFactory
			.getLogger(MtdBizServiceImpl.class);

	public void sendMtdReport(AsynProcessStatus processStatus, String to, String cc, 
			String warningEmailHonorific, String warningEmailTo, String warningEmailCc,
			String versionNo, boolean lastReport, Long userId,String subject,String template,String comment,String bcc, boolean toCustomer) throws WxPltException {
//		if(!lockerBizService.lock(LOCKER_ID, LOCKER_NAME, LOCK_EFFICTIVE_TIME, versionNo, userId)) {
//			throw new WxPltException("重复操作");
//		}
		try {
			doSendMtdReport(processStatus, to, cc, warningEmailHonorific, warningEmailTo, warningEmailCc, lastReport, userId,subject,template,comment,bcc, toCustomer);
		} finally {
			lockerBizService.unlock(LOCKER_ID, versionNo, userId);
		}
	}
	
	protected void checkData(String warningEmailHonorific, String warningEmailTo, String warningEmailCc, Long userId,
			ServletContext servletContext) throws WxPltException {
		String warningView = (String) Constants.getSystemPropertyByCodeType("MTD.view.warning");
		if(StringUtils.isBlank(warningView)) {
			LogUtils.addLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.checkData", 
					"Log.warning", "未找到MTD数据告警视图配置");
			return;
		}
		StringBuilder sql = new StringBuilder("select e.error_table, e.error_content, e.error_details, e.error_res from ")
				.append(warningView).append(" e where e.error_res>0");
		
		final List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();
		wxJdbcTemplate.query(sql.toString(), (Object[])null, new RowCallbackHandler() {
			
			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				Map<String, Object> item = new HashMap<String, Object>();
				item.put("errorTable", arg0.getString("error_table"));
				item.put("errorContent", arg0.getString("error_content"));
				item.put("errorDetails", arg0.getString("error_details"));
				item.put("errorRes", arg0.getInt("error_res"));
				dataList.add(item);
			}
		});
		if(!dataList.isEmpty()) {
			//发送告警邮件
			String[] to = warningEmailTo.split(",");
			String[] cc = null;
			if(StringUtils.isNotBlank(warningEmailCc)) {
				cc = warningEmailCc.split(",");
			}
			Map<String, Object> dataRoot = new HashMap<String, Object>();
			dataRoot.put("warningEmailHonorific", warningEmailHonorific);
			dataRoot.put("dataList", dataList);
			
			try {
				if(!EmailSendUtils.sendEmailForListContent(
						servletContext,
						to,
						cc,
						"MTD报表数据告警",
						dataRoot,
						null,
						"mtd_warning_email.ftl", "雪佛龙中国",null)){
					throw new WxPltException("MTD报表数据告警邮件发送失败");
				}
				LogUtils.addInfoLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.checkData", "MTD报表数据告警邮件发送成功");
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
				LogUtils.addErrorLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.checkData",  "MTD报表数据告警邮件发送失败。", null);
			}
			throw new WxPltException("MTD报表数据有告警。告警邮件已发送。");
		}
	}
	
	protected void doSendMtdReport(AsynProcessStatus processStatus, String to, String cc, 
			String warningEmailHonorific, String warningEmailTo, String warningEmailCc,
			boolean lastReport, Long userId,String subject,String template,String comment,String bcc, boolean toCustomer) throws WxPltException {
		if(processStatus != null) {
			processStatus.setMessage("检查数据......");
			processStatus.save();
		}
		WebApplicationContext webApplicationContext = ContextLoader
				.getCurrentWebApplicationContext();
		ServletContext servletContext = webApplicationContext
				.getServletContext();
		if(StringUtils.isNotBlank(warningEmailTo)) {
			checkData(warningEmailHonorific, warningEmailTo, warningEmailCc, userId, servletContext);
		}else {
			LogUtils.addLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.doSendMtdReport", 
					"Log.warning", "没有告警接收邮箱");
		}
		if(processStatus != null) {
			processStatus.setMessage("获取报表日期......");
			processStatus.save();
		}
		//查询报表日期
//		String reportView = (String) Constants.getSystemPropertyByCodeType("MTD.reportView");
//		if(StringUtils.isBlank(reportView)) {
		String reportView = "PP_MID.dbo.[view_mtd_all_order_report]";
//		}
		StringBuilder sql = new StringBuilder(100);
		Object[] params = null;
		Date lastProcessDate = null;
		List<WxTProperties> list = null;
		if(!lastReport) {
			sql.append("select distinct mao.source_file_date, mao.processed_date from ").append(reportView).append(" mao");
			//查询实际报表日期
			WxTPropertiesExample example = new WxTPropertiesExample();
			example.createCriteria().andCodetypeEqualTo("MTD.lastProcessDate");
			list = wxTPropertiesMapper.selectByExample(example);
			if(!list.isEmpty()) {
				lastProcessDate = DateUtil.parseDate(list.get(0).getCode(), "yyyy-MM-dd");
				if(lastProcessDate != null) {
					sql.append(" where mao.processed_date>?");
					params = new Object[] {lastProcessDate};
				}
			}
			sql.append(" order by mao.processed_date, mao.source_file_date");
		}else {
			//查询最新报表日期
			sql.append("select top 1 mao.source_file_date, mao.processed_date from ").append(reportView).append(" mao order by mao.processed_date desc, mao.source_file_date desc");
		}
		final List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();
		wxJdbcTemplate.query(sql.toString(), params, new RowCallbackHandler() {
			
			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				Map<String, Object> item = new HashMap<String, Object>();
				item.put("source_file_date", arg0.getString("source_file_date"));
				item.put("processed_date", arg0.getDate("processed_date"));
				dataList.add(item);
			}
		});
		if(dataList.isEmpty()) {
			LogUtils.addLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.sendMtdReport", 
					"Log.warning", "未查询到新MTD报表日期");
			return;
		}
		if(processStatus != null) {
			processStatus.setMessage("加载Excel模板......");
			processStatus.save();
		}
		
		Configuration configuration = new Configuration();
		configuration.setDefaultEncoding("UTF-8");
		configuration.setServletContextForTemplateLoading(servletContext, "/WEB-INF/templates");
		Template sheet1 = null;
		Template sheet3 = null;
		Template sheet4 = null;
		Template sheet5 = null;
		Template sheet6 = null;
		try {
			sheet1 = configuration.getTemplate("mtd_summary_by_sale_team.ftl");
			sheet3 = configuration.getTemplate("mtd_sheet_oem_summary.ftl");
			sheet4 = configuration.getTemplate("mtd_sheet_order_detail.ftl");
			sheet5 = configuration.getTemplate("mtd_sheet_planning_comment.ftl");
			sheet6 = configuration.getTemplate("mtd_sheet_promotion_summary.ftl");
		} catch (IOException e1) {
			e1.printStackTrace();
			logger.error(e1.getMessage(), e1);
			throw new RuntimeException(e1);
		}
		File excelTemplate = new File(servletContext.getRealPath("/WEB-INF/templates/MTD Report TemplateV2.zip"));

		//GQ
		Template gqsheet1 = null;
		Template gqsheet3 = null;
		Template gqsheet4 = null;
		try {
			gqsheet1 = configuration.getTemplate("mtd_summary_by_sale_team_gq.ftl");
			gqsheet3 = configuration.getTemplate("mtd_sheet_order_detail_gq.ftl");
			gqsheet4 = configuration.getTemplate("mtd_sheet_planning_comment_gq.ftl");
		} catch (IOException e1) {
			e1.printStackTrace();
			logger.error(e1.getMessage(), e1);
			throw new RuntimeException(e1);
		}
		File gqExcelTemplate = new File(servletContext.getRealPath("/WEB-INF/templates/MTD Delivery report Gq.zip"));

		//发送报表
		Date currentProcessDate = null;
		try {
			for(Map<String, Object> item : dataList) {
				Date processDate = (Date) item.get("processed_date");
//				if(currentProcessDate != null && !currentProcessDate.equals(processDate)) {
//					sendMtdReportEmail(excels, responsibleVos, servletContext);
//					excels.clear();
//					currentProcessDate = processDate;
//				}else if(currentProcessDate == null) {
//					currentProcessDate = processDate;
//				}
//				excels.add(writeMtdExcel((String)item.get("source_file_date"), processDate, sheet1, sheet3, excelTemplate, servletContext));
				String reportDate = (String)item.get("source_file_date");
//				writeMtdExcel(excels, reportDate, processDate, sheet1, sheet3, sheet4, excelTemplate, servletContext);
				if(processStatus != null) {
					processStatus.setMessage("生成" + reportDate + "Excel报表......");
					processStatus.save();
				}
				sendMtdReportEmail(processStatus, reportDate, processDate, to, cc, userId, subject, template,
						comment, bcc, sheet1, sheet3, sheet4, sheet5, sheet6, excelTemplate,
						gqsheet1,gqsheet3,gqsheet4,gqExcelTemplate, servletContext, configuration, toCustomer);
				currentProcessDate = processDate;
			}
//			if(!excels.isEmpty()) {
//				sendMtdReportEmail(excels, responsibleVos, servletContext);
//				excels.clear();
//			}
//			LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.chevron.report.business.impl.MtdBizServiceImpl.sendMtdReport", "MTD报表发送成功");
		} catch (WxPltException e) {
			throw e;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			LogUtils.addErrorLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.sendMtdReport", "发送MTD报表失败。" 
					+ e.getMessage(), null);
			throw new RuntimeException(e);
		} 
		if(processStatus != null) {
			processStatus.setMessage("更新最后报表日期......");
			processStatus.save();
		}
		//更新最新报表发送日期
		if(lastReport) {
			//发送最新报表
			WxTPropertiesExample example = new WxTPropertiesExample();
			example.createCriteria().andCodetypeEqualTo("MTD.lastProcessDate");
			list = wxTPropertiesMapper.selectByExample(example);
		}
		WxTProperties newWxTProperties = new WxTProperties();
		newWxTProperties.setCode(DateUtil.getDateStr(currentProcessDate, "yyyy-MM-dd"));
		if(list.isEmpty()) {
			newWxTProperties.setMakeTime(DateUtil.getCurrentDate());
			newWxTProperties.setCodetype("MTD.lastProcessDate");
			newWxTProperties.setCodename("上一次发送MTD报表的处理日期");
			wxTPropertiesMapper.insertSelective(newWxTProperties);
		}else {
			newWxTProperties.setId(list.get(0).getId());
			newWxTProperties.setModifyTime(DateUtil.getCurrentDate());
			wxTPropertiesMapper.updateByPrimaryKeySelective(newWxTProperties);
		}
	}


	@Override
	public MtdEmailConfig getEmailConfig() throws WxPltException {
		MtdEmailConfig emailConfig = new MtdEmailConfig();
		Map<String, PropertyDescriptor> propertyMap = new HashMap<String, PropertyDescriptor>();
		try {
			for(PropertyDescriptor propertyDescriptor : Introspector.getBeanInfo(MtdEmailConfig.class).getPropertyDescriptors()) {
				propertyMap.put(propertyDescriptor.getName(), propertyDescriptor);
			}
		} catch (IntrospectionException e) {
			throw new RuntimeException(e);
		}
		Map<String, Object> dicMap = dicServiceImpl.getDicItemByDicTypeCode("mtd.email");
		if("success".equals(dicMap.get("result"))){
			@SuppressWarnings("unchecked")
			List<DicItemVo> list = (List<DicItemVo>) dicMap.get("data");
			if(list != null && !list.isEmpty()){
				for(DicItemVo vo : list){
					if(StringUtils.isBlank(vo.getDicItemDesc())) {
						continue;
					}
					PropertyDescriptor propertyDescriptor = propertyMap.get(vo.getDicItemCode());
					if(propertyDescriptor == null) {
						throw new RuntimeException("异常配置" + vo.getDicItemCode());
					}
					try {
						String value = (String)propertyDescriptor.getReadMethod().invoke(emailConfig);
						if(StringUtils.isBlank(value)) {
							value = vo.getDicItemDesc();
						}else {
							value += "," + vo.getDicItemDesc();
						}
						propertyDescriptor.getWriteMethod().invoke(emailConfig, value);
					} catch (Exception e) {
						throw new RuntimeException("获取MTD邮件配置失败。" + e.getMessage(), e);
					}
				}
			}
		}else{
			throw new WxPltException((String) dicMap.get("errorMsg"));
		}
		return emailConfig;
	}
	
	protected void sendMtdReportEmail(AsynProcessStatus processStatus, String reportDate, 
			Date processDate, String toEmails, String ccEmails, Long userId,String subject, String template, String comment,
			String bccEmails,Template sheet1, Template sheet3, Template sheet4, Template sheet5, Template sheet6, File excelTemplate,
									  Template sheet1Gq,Template sheet3Gq,Template sheet4Gq,File gqExcelTemplate,
			ServletContext servletContext, Configuration ftlConfiguration, boolean toCustomer) throws WxPltException, IOException, TemplateException {
		//bruce 2019/10/31 修改模板名
		if(StringUtils.isBlank(template)) {
			template = MTD_NOT_FINAL_TEMPLATE;
		}
	    if(StringUtils.isBlank(subject)) {
	    	if(MTD_FINAL_TEMPLATE.equals(template)) {
	    		subject = "(Final) ";
	    	}else {
	    		subject = "";
	    	}
	    	subject += "MTD Report " + reportDate;
	    }
		File folder = new File(FileManager.fileUploadPath + "MTD/" + reportDate);
		if (!folder.exists()) {
			folder.mkdirs();
		}
		Date rd = DateUtil.parseDate(reportDate, "yyyyMMdd");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		DateFormat df = new SimpleDateFormat("MMM",Locale.ENGLISH);
		rootMap.put("forecastCm1", "Forecast - " + df.format(rd));
//		rootMap.put("forecastCm", "Forecast–" + df.format(rd));
		rootMap.put("forecastCm2", "Forecast – " + df.format(rd));
//		rootMap.put("fcstCm", "FCST- " + df.format(rd));
//		rootMap.put("fcstCm1", "FCST - " + df.format(rd));
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(rd);
		calendar.add(Calendar.MONTH, -1);
		rootMap.put("forecastPm1", "Forecast - " + df.format(calendar.getTime()));
		rootMap.put("mtdPm1", "MTD - " + df.format(calendar.getTime()));
		calendar.add(Calendar.MONTH, 2);
		rootMap.put("forecastNm1", "Forecast - " + df.format(calendar.getTime()));
		rootMap.put("forecastNm2", "Forecast – " + df.format(calendar.getTime()));
		calendar.add(Calendar.MONTH, 1);
		rootMap.put("forecastNnm1", "Forecast - " + df.format(calendar.getTime()));
		calendar.add(Calendar.MONTH, 1);
		rootMap.put("forecastNnnm1", "Forecast - " + df.format(calendar.getTime()));
		Map<String, Object> sheet1Root = null;
		//发送给老板
		List<File> excels = new ArrayList<File>(5);
		sheet1Root = writeMtdExcel(processStatus, folder, excels, reportDate, processDate, CAI_ALL, sheet1, sheet3, sheet4, 
				sheet5, sheet6, excelTemplate, servletContext, ftlConfiguration, rootMap);
		sheet1Root.put("comment", comment);
		sendMtdEmail(processStatus, servletContext, toEmails, ccEmails, bccEmails, subject, sheet1Root,
				excels.toArray(new File[excels.size()]), template, userId, reportDate + "-" + CAI_ALL);
		//--------
		/*
		//test
		BufferedInputStream inputStream;
		BufferedOutputStream outputStream;
		inputStream = new BufferedInputStream(new FileInputStream(excels.toArray(new File[excels.size()])[0]));
		outputStream = new BufferedOutputStream(new FileOutputStream("C:\\XFL\\demoV4.xlsx"));
		Streams.copy(inputStream, outputStream, true);
		inputStream.close();
		outputStream.flush();
		outputStream.close();*/
		//--------
		//发送给销售
		ccEmails = null;
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "ReportSales", (Object[])null, "");
		for(int i = 0; i < dataList.size(); i++) {
			Map<String, String> item = dataList.get(i);
			String cai = item.get("rpt_level_cai");
			processStatus.setPrefixMessage("发送销售" + cai + "(" + i + "/" + dataList.size() + ")：");
			if(!toCustomer && StringUtils.isBlank(item.get("bcc"))) {
				continue;
			}
			if(toCustomer) {
				bccEmails = item.get("bcc");
				toEmails = item.get("email");
			}else {
				bccEmails = null;
				toEmails = item.get("bcc");
			}
			excels.clear();
			sheet1Root = writeMtdExcel(processStatus, folder, excels, reportDate, processDate, cai, sheet1, sheet3, sheet4, 
					sheet5, sheet6, excelTemplate, servletContext, ftlConfiguration, rootMap);
			sheet1Root.put("comment", comment);
			sendMtdEmail(processStatus, servletContext, toEmails, ccEmails, bccEmails, subject, sheet1Root,
					excels.toArray(new File[excels.size()]), template, userId, reportDate + "-" + cai);
		}

		//发送给高桥组
		Map<String, Object> sheet1RootGq = null;
		List<File> excelsGq = new ArrayList<File>(4);
		if(template.equals(MTD_NOT_FINAL_TEMPLATE)){
			template = MTD_NOT_FINAL_TEMPLATE_GQ;
		}else {
			template = MTD_FINAL_TEMPLATE_GQ;
		}
		ccEmails = null;
		List<Map<String, String>> dataListGq = reportViewBizService.queryForData("mtd", "ReportSalesGq", (Object[])null, "");
		for(int i = 0; i < dataListGq.size(); i++) {
			Map<String, String> item = dataListGq.get(i);
			String cai = item.get("cai");
			processStatus.setPrefixMessage("发送高桥" + cai + "(" + i + "/" + dataListGq.size() + ")：");
			if(!toCustomer && StringUtils.isBlank(item.get("bcc"))) {
				continue;
			}
			if(toCustomer) {
				bccEmails = item.get("bcc");
				toEmails = item.get("email");
			}else {
				bccEmails = null;
				toEmails = item.get("bcc");
			}
			excelsGq.clear();
			sheet1RootGq = writeMtdExcelGq(processStatus, folder, excelsGq, reportDate, processDate, cai, sheet1Gq, sheet3Gq, sheet4Gq,
					gqExcelTemplate, servletContext, ftlConfiguration, rootMap);
			sheet1RootGq.put("comment", comment);

			sendMtdEmail(processStatus, servletContext, toEmails, ccEmails, bccEmails, subject, sheet1RootGq,
					excelsGq.toArray(new File[excelsGq.size()]), template, userId, reportDate + "-" + cai);
			/*BufferedInputStream inputStream;
			BufferedOutputStream outputStream;
			inputStream = new BufferedInputStream(new FileInputStream(excelsGq.toArray(new File[excelsGq.size()])[0]));
			outputStream = new BufferedOutputStream(new FileOutputStream("C:\\XFL\\demoV3.xlsx"));
			Streams.copy(inputStream, outputStream, true);
			inputStream.close();
			outputStream.flush();
			outputStream.close();*/
		}

	}

	/*//发送高桥
	protected void sendMtdReportEmailGq(AsynProcessStatus processStatus, String reportDate,
										Date processDate, String toEmails, String ccEmails, Long userId,String subject, String template, String comment,
										String bccEmails,Template sheet1, Template sheet3, Template sheet4, File excelTemplate,
										ServletContext servletContext, Configuration ftlConfiguration, boolean toCustomer) throws WxPltException, IOException, TemplateException {

		if(StringUtils.isBlank(subject)) {
			if(MTD_FINAL_TEMPLATE.equals(template)) {
				subject = "(Final) ";
				template = MTD_FINAL_TEMPLATE_GQ;
			}else {
				subject = "";
			}
			subject += "MTD Report " + reportDate;
		}
		if(!MTD_FINAL_TEMPLATE_GQ.equals(template)){
			template = MTD_NOT_FINAL_TEMPLATE_GQ;
		}

		File folder = new File(FileManager.fileUploadPath + "MTD/" + reportDate);
		if (!folder.exists()) {
			folder.mkdirs();
		}
		Date rd = DateUtil.parseDate(reportDate, "yyyyMMdd");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		DateFormat df = new SimpleDateFormat("MMM",Locale.ENGLISH);
		rootMap.put("forecastCm1", "Forecast - " + df.format(rd));
		rootMap.put("forecastCm2", "Forecast – " + df.format(rd));
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(rd);
		calendar.add(Calendar.MONTH, -1);
		rootMap.put("forecastPm1", "Forecast - " + df.format(calendar.getTime()));
		rootMap.put("mtdPm1", "MTD - " + df.format(calendar.getTime()));
		calendar.add(Calendar.MONTH, 2);
		rootMap.put("forecastNm1", "Forecast - " + df.format(calendar.getTime()));
		rootMap.put("forecastNm2", "Forecast – " + df.format(calendar.getTime()));
		calendar.add(Calendar.MONTH, 1);
		rootMap.put("forecastNnm1", "Forecast - " + df.format(calendar.getTime()));
		calendar.add(Calendar.MONTH, 1);
		rootMap.put("forecastNnnm1", "Forecast - " + df.format(calendar.getTime()));
		Map<String, Object> sheet1Root = null;
		//发送给高桥组
		List<File> excels = new ArrayList<File>(4);
		ccEmails = null;
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "ReportSalesGq", (Object[])null, "");
		for(int i = 0; i < dataList.size(); i++) {
			Map<String, String> item = dataList.get(i);
			String cai = item.get("cai");
			processStatus.setPrefixMessage("发送高桥" + cai + "(" + i + "/" + dataList.size() + ")：");
			if(!toCustomer && StringUtils.isBlank(item.get("bcc"))) {
				continue;
			}
			if(toCustomer) {
				bccEmails = item.get("bcc");
				toEmails = item.get("email");
			}else {
				bccEmails = null;
				toEmails = item.get("bcc");
			}
			excels.clear();
			sheet1Root = writeMtdExcelGq(processStatus, folder, excels, reportDate, processDate, cai, sheet1, sheet3, sheet4,
					excelTemplate, servletContext, ftlConfiguration, rootMap);
			sheet1Root.put("comment", comment);

			sendMtdEmail(processStatus, servletContext, toEmails, ccEmails, bccEmails, subject, sheet1Root,
					excels.toArray(new File[excels.size()]), template, userId, reportDate + "-" + cai);

		*//*	BufferedInputStream inputStream;
			BufferedOutputStream outputStream;
			inputStream = new BufferedInputStream(new FileInputStream(excels.toArray(new File[excels.size()])[0]));
			outputStream = new BufferedOutputStream(new FileOutputStream("C:\\XFL\\demoV2.xlsx"));
			Streams.copy(inputStream, outputStream, true);
			inputStream.close();
			outputStream.flush();
			outputStream.close();*//*
		}

	}*/

	protected Map<String, Object> writeMtdExcelGq(AsynProcessStatus processStatus, File folder, List<File> excels, String reportDate, Date processDate,
												String cai, Template sheet1, Template sheet3, Template sheet4, File excelTemplate,
												ServletContext servletContext, Configuration ftlConfiguration, Map<String, Object> rootMap) throws IOException, WxPltException, TemplateException {
		//File excelFile = new File(folder, "MTD Delivery report-" + reportDate + (CAI_ALL.equals(cai) ? "" : "-" + cai) + ".xlsx");
		File excelFile = new File(folder, "MTD Delivery report-" + reportDate + ".xlsx");
		ZipOutputStream zipOutputStream = new ZipOutputStream(excelFile);
		Map<String, Object> sheet1Root = null;
		try {
			zipOutputStream.setEncoding("GBK");
			ZipFile zipFile = new ZipFile(excelTemplate, "GBK");
			try {
				Enumeration<?> entities = zipFile.getEntries();
				ZipEntry zipEntry = null;
				while(entities.hasMoreElements()) {
					zipEntry = (ZipEntry) entities.nextElement();
					if(zipEntry.isDirectory()) {
						zipOutputStream.putNextEntry(zipEntry);
					}else {
						String entryName = zipEntry.getName();
						if(ftlTempMap.containsKey(entryName)) {
							//延迟处理
							continue;
						}
						zipOutputStream.putNextEntry(new ZipEntry(entryName));
						if(entryName.endsWith("sheet1.xml")) {
							if(processStatus != null) {
								processStatus.setMessage("生成第一个Sheet......");
								processStatus.save();
							}
							sheet1Root = writeMtdSheet1Gq(sheet1, zipOutputStream, reportDate, processDate);
						}else if(entryName.endsWith("sheet3.xml")) {
							if(processStatus != null) {
								//老4，writeMtdSheet4
								processStatus.setMessage("生成第三个Sheet......");
								processStatus.save();
							}
							//gq 虚拟cai VIRTUALGQ
							cai = "VIRTUALGQ";
							writeMtdSheet4(sheet3, zipOutputStream, reportDate, processDate, cai);
						}else if(entryName.endsWith("sheet4.xml")) {
							if(processStatus != null) {
								//老5，writeMtdSheet5
								processStatus.setMessage("生成第四个Sheet......");
								processStatus.save();
							}
							writeMtdSheet5Gq(sheet4, zipOutputStream, reportDate);
						}else {
							InputStream in = zipFile.getInputStream(zipEntry);
							try {
								byte[] by = new byte[1024];
								int c;
								while((c=in.read(by))!=-1){
									zipOutputStream.write(by,0,c);
								}
							}finally {
								in.close();
							}
						}
					}
				}
				//处理延迟处理文件
				for(String entryName : gQftlTempMap.keySet()) {
					zipOutputStream.putNextEntry(new ZipEntry(entryName));
					ftlConfiguration.getTemplate(gQftlTempMap.get(entryName)).process(rootMap, new OutputStreamWriter(zipOutputStream, "UTF-8"));
				}
			} finally {
				zipFile.close();
			}
		} finally {
			zipOutputStream.close();
		}
		excels.add(excelFile);
		return sheet1Root;
	}

	protected void sendMtdEmail(AsynProcessStatus processStatus, ServletContext servletContext, String toEmails,
            String ccEmails,String bccEmails,String subject, Map<String, Object> sheet1Root, File[] attfiles,
            String template, Long userId, String reportKey) {
		if(processStatus != null) {
			processStatus.setMessage("发送邮件......");
			processStatus.save();
		}
		String[] to = toEmails.split(",");
		String[] cc = null;
		if(StringUtils.isNotBlank(ccEmails)) {
			cc = ccEmails.split(",");
		}
		String[] bcc = null;
		if(StringUtils.isNotBlank(bccEmails)) {
			bcc = bccEmails.split(",");
		}
		try {//TODO
			if(!EmailSendUtils.sendEmailForListContent(
					servletContext,
					to,
					cc,
					subject,
					sheet1Root,
					attfiles,
					template, "雪佛龙中国",bcc)){
				throw new WxPltException("MTD报表邮件发送失败");
			}
			LogUtils.addInfoLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.sendMtdEmail", reportKey + "MTD报表发送成功");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			LogUtils.addErrorLog(userId, "com.chevron.report.business.impl.MtdBizServiceImpl.sendMtdEmail",  reportKey + "发送MTD报表失败。", reportKey);
		}
	}
	
	protected Map<String, Object> writeMtdExcel(AsynProcessStatus processStatus, File folder, List<File> excels, String reportDate, Date processDate, 
			String cai, Template sheet1, Template sheet3, Template sheet4, Template sheet5, Template sheet6, File excelTemplate, 
			ServletContext servletContext, Configuration ftlConfiguration, Map<String, Object> rootMap) throws IOException, WxPltException, TemplateException {
		File excelFile = new File(folder, "MTD Delivery report-" + reportDate + (CAI_ALL.equals(cai) ? "" : "-" + cai) + ".xlsx");
		ZipOutputStream zipOutputStream = new ZipOutputStream(excelFile);
		Map<String, Object> sheet1Root = null;
		try {
			zipOutputStream.setEncoding("GBK");
			ZipFile zipFile = new ZipFile(excelTemplate, "GBK");
			try {
				Enumeration<?> entities = zipFile.getEntries();
				ZipEntry zipEntry = null;
				while(entities.hasMoreElements()) {
					zipEntry = (ZipEntry) entities.nextElement();
					if(zipEntry.isDirectory()) {
						zipOutputStream.putNextEntry(zipEntry);
					}else {
						String entryName = zipEntry.getName();
						if(ftlTempMap.containsKey(entryName)) {
							//延迟处理
							continue;
						}
						zipOutputStream.putNextEntry(new ZipEntry(entryName));
						if(entryName.endsWith("sheet1.xml")) {
								if(processStatus != null) {
									processStatus.setMessage("生成第一个Sheet......");
									processStatus.save();
								}
								sheet1Root = writeMtdSheet1(sheet1, zipOutputStream, reportDate, processDate);
						}else if(entryName.endsWith("sheet3.xml")) {
							if(processStatus != null) {
								processStatus.setMessage("生成第三个Sheet......");
								processStatus.save();
							}
							writeMtdSheet3(sheet3, zipOutputStream, reportDate, processDate);
						}else if(entryName.endsWith("sheet4.xml")) {
							if(processStatus != null) {
								processStatus.setMessage("生成第四个Sheet......");
								processStatus.save();
							}
							writeMtdSheet4(sheet4, zipOutputStream, reportDate, processDate, cai);
						}else if(entryName.endsWith("sheet5.xml")) {
							if(processStatus != null) {
								processStatus.setMessage("生成第五个Sheet......");
								processStatus.save();
							}
							writeMtdSheet5(sheet5, zipOutputStream, reportDate, processDate);
						}else if(entryName.endsWith("sheet6.xml")) {
							if(processStatus != null) {
								processStatus.setMessage("生成第六个Sheet......");
								processStatus.save();
							}
							writeMtdSheet6(sheet6, zipOutputStream, reportDate, processDate, rootMap);
						}else {
							InputStream in = zipFile.getInputStream(zipEntry);
							try {
								byte[] by = new byte[1024];
								int c;
								while((c=in.read(by))!=-1){
									zipOutputStream.write(by,0,c);
								}
							}finally {
								in.close();
							}
						}
					}
				}
				//处理延迟处理文件
				for(String entryName : ftlTempMap.keySet()) {
					zipOutputStream.putNextEntry(new ZipEntry(entryName));
					ftlConfiguration.getTemplate(ftlTempMap.get(entryName)).process(rootMap, new OutputStreamWriter(zipOutputStream, "UTF-8"));
				}
			} finally {
				zipFile.close();
			}
		} finally {
			zipOutputStream.close();
		}
		excels.add(excelFile);
		return sheet1Root;
	}
	
	protected Map<String, Object> writeMtdSheet1(Template sheet1, OutputStream outputStream, 
			String reportDate, Date processDate) throws WxPltException, UnsupportedEncodingException, TemplateException, IOException {
		String channel=null;
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "SummaryBySaleTeam ", new Object[] {reportDate, processDate,channel,channel}, "0");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		Map<String, Object> data = new HashMap<String, Object>();
		for(Map<String, String> item : dataList) {
			String rowNo = item.get("rno");
			for(String key : item.keySet()) {
				double value = Double.parseDouble(item.get(key));
				if(value > 0.0000001 || value < -0.0000001) {
					data.put("c" + rowNo + key, value);
				}else {
					data.put("c" + rowNo + key, 0);
				}
			}
		}
		data.put("c4d", DateUtil.getDateStr(DateUtil.parseDate(reportDate, "yyyyMMdd"), "yyyy-MM-dd"));
		rootMap.put("data", data);
		sheet1.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
		return rootMap;
	}

	protected Map<String, Object> writeMtdSheet1Gq(Template sheet1, OutputStream outputStream,
												 String reportDate, Date processDate) throws WxPltException, UnsupportedEncodingException, TemplateException, IOException {
		String channel="GQ";
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "SummaryBySaleTeam ", new Object[] {reportDate, processDate,channel,channel}, "0");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		Map<String, Object> data = new HashMap<String, Object>();
		for(Map<String, String> item : dataList) {
			String rowNo = item.get("rno");
			for(String key : item.keySet()) {
				double value = Double.parseDouble(item.get(key));
				if(value > 0.0000001 || value < -0.0000001) {
					data.put("c" + rowNo + key, value);
				}else {
					data.put("c" + rowNo + key, 0);
				}
			}
		}
		data.put("c4d", DateUtil.getDateStr(DateUtil.parseDate(reportDate, "yyyyMMdd"), "yyyy-MM-dd"));
		rootMap.put("data", data);
		sheet1.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
		return rootMap;
	}

	protected void writeMtdSheet3(Template sheet3, OutputStream outputStream, 
			String reportDate, Date processDate) throws WxPltException, UnsupportedEncodingException, TemplateException, IOException {
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "OEMSummary", new Object[] {reportDate, processDate}, "0");
		//计算汇总和类型转换
		Map<String, Double> totalMap = new HashMap<String, Double>();
		List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
		Map<String, Object> firstItem = null;
		for(int i = 0; i < dataList.size(); i++) {
			Map<String, String> item = dataList.get(i);
			Map<String, Object> newItem = new HashMap<String, Object>(item.size());
			for(String key : item.keySet()) {
				if(key.startsWith("sc")) {
					newItem.put(key, item.get(key));
					//字符串单元格 不汇总
					continue;
				}
				double value = Double.parseDouble(item.get(key));
				double newValue = 0.0;
				if(totalMap.get(key) != null) {
					newValue = totalMap.get(key);
				}
				if(value > 0.0000001 || value < -0.0000001) {
					newItem.put(key, value);
					newValue += value;
				}else {
					newItem.put(key, 0);
				}
				totalMap.put(key, newValue);
			}
			if(i == 0) {
				firstItem = newItem;
			}else {
				newDataList.add(newItem);
			}
		}
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		rootMap.put("dataList", newDataList);
		rootMap.put("firstItem", firstItem);
		rootMap.put("total", totalMap);
		rootMap.put("dataLength", dataList.size());
		sheet3.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
	}
	
	protected void writeMtdSheet4(Template sheet4, OutputStream outputStream, 
			String reportDate, Date processDate, String cai) throws WxPltException, UnsupportedEncodingException, TemplateException, IOException {
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "OrderDetail", new Object[] {reportDate, processDate, cai, cai, cai, cai, cai, cai}, "");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		rootMap.put("dataList", dataList);
		rootMap.put("dataLength", dataList.size());
		sheet4.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
	}

	protected void writeMtdSheet5(Template sheet5, OutputStream outputStream, 
			String reportDate, Date processDate) throws WxPltException, UnsupportedEncodingException, TemplateException, IOException {
		Date rd = DateUtil.parseDate(reportDate, "yyyyMMdd");
		String salesOffice = null;
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "PlanningComment", new Object[] {DateUtil.getDateStr(rd, "yyyyMM"),salesOffice,salesOffice}, "");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
//		DateFormat df = new SimpleDateFormat("MMM",Locale.ENGLISH);
//		rootMap.put("e1t", "Forecast - " + df.format(rd));
//		Calendar calendar = Calendar.getInstance();
//		calendar.setTime(rd);
//		calendar.add(Calendar.MONTH, 1);
//		rootMap.put("f1t", "Forecast - " + df.format(calendar.getTime()));
//		calendar.add(Calendar.MONTH, 1);
//		rootMap.put("g1t", "Forecast - " + df.format(calendar.getTime()));
//		calendar.add(Calendar.MONTH, 1);
//		rootMap.put("h1t", "Forecast - " + df.format(calendar.getTime()));
		rootMap.put("dataList", dataList);
		rootMap.put("dataLength", dataList.size());
		rootMap.put("c1a", "Report Date: " + DateUtil.getDateStr(rd, "yyyy-MM-dd") + "; Report Unit : Volume in Liter");
		sheet5.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
	}

	protected void writeMtdSheet5Gq(Template sheet5, OutputStream outputStream,
								  String reportDate) throws WxPltException, TemplateException, IOException {
		Date rd = DateUtil.parseDate(reportDate, "yyyyMMdd");
		String salesOffice = "GQ";
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd",
				"PlanningComment",
				new Object[] {DateUtil.getDateStr(rd, "yyyyMM"),
						salesOffice,
						salesOffice},
				"");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		rootMap.put("dataList", dataList);
		rootMap.put("dataLength", dataList.size());
		rootMap.put("c1a", "Report Date: " + DateUtil.getDateStr(rd, "yyyy-MM-dd") + "; Report Unit : Volume in Liter");
		sheet5.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
	}

	protected void writeMtdSheet6(Template sheet6, OutputStream outputStream, 
			String reportDate, Date processDate, Map<String, Object> globalRootMap) throws WxPltException, UnsupportedEncodingException, TemplateException, IOException {
		Date rd = DateUtil.parseDate(reportDate, "yyyyMMdd");
		List<Map<String, String>> dataList = reportViewBizService.queryForData("mtd", "PromotionSummary", new Object[] {DateUtil.getDateStr(rd, "yyyyMM")}, "");
		Map<String, Object> rootMap = new HashMap<String, Object>(5);
		rootMap.put("dataList", dataList);
		rootMap.put("dataLength", dataList.size());
		rootMap.put("c1c", "Report Date: " + DateUtil.getDateStr(rd, "yyyy-MM-dd") + "; Report Unit : Volume in Liter");
		sheet6.process(rootMap, new OutputStreamWriter(outputStream, "UTF-8"));
		globalRootMap.put("promotionSummarySize", dataList.isEmpty() ? 1 : dataList.size());
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void saveEmailConfig(MtdEmailConfig emailConfig) throws WxPltException {
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUser().getUserId();
		//删除老数据
		dicItemVoMapper.deleteByTypeCode(EMAIL_CONFIG_TYPE_CODE);
		List<DicItemVo> configs = new ArrayList<DicItemVo>();
		//保存是否自动发送给客户配置
		DicItemVo vo = new DicItemVo();
		vo.setCreateTime(now);
		vo.setCreator(userId);
		vo.setDicItemCode("autoToCustomer");
		vo.setDicTypeCode(EMAIL_CONFIG_TYPE_CODE);
		vo.setDicItemName("MTD报表是否自动发送给客户");
		vo.setDicItemDesc(emailConfig.getAutoToCustomer());
		vo.setStatus("1");
		vo.setSortNumb(0.0);
		configs.add(vo);
		//告警邮件尊称
		vo = new DicItemVo();
		vo.setCreateTime(now);
		vo.setCreator(userId);
		vo.setDicItemCode("warningEmailHonorific");
		vo.setDicTypeCode(EMAIL_CONFIG_TYPE_CODE);
		vo.setDicItemName("告警邮件尊称");
		vo.setDicItemDesc(emailConfig.getWarningEmailHonorific());
		vo.setStatus("1");
		vo.setSortNumb(0.0);
		configs.add(vo);
		//保存告警接收邮箱配置	
		separateEmailConfig(configs, emailConfig.getWarningEmailTo(), "warningEmailTo", 
				"告警接收邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		//保存告警抄送邮箱配置	
		separateEmailConfig(configs, emailConfig.getWarningEmailCc(), "warningEmailCc", 
				"告警抄送邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		//保存内部接收邮箱配置	
		separateEmailConfig(configs, emailConfig.getInternalEmailTo(), "internalEmailTo", 
				"内部接收邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		//保存内部抄送邮箱配置	
		separateEmailConfig(configs, emailConfig.getInternalEmailCc(), "internalEmailCc", 
				"内部抄送邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		//保存客户接收邮箱配置	
		separateEmailConfig(configs, emailConfig.getCustomerEmailTo(), "customerEmailTo", 
				"客户接收邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		//保存客户抄送邮箱配置	
		separateEmailConfig(configs, emailConfig.getCustomerEmailCc(), "customerEmailCc", 
				"客户抄送邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		
		//new
		//保存内部密送邮箱配置
		separateEmailConfig(configs, emailConfig.getInternalEmailBcc(), "internalEmailBcc", 
				"内部密送邮箱", now, userId, EMAIL_CONFIG_TYPE_CODE);
		
		dicItemVoMapper.insertBatch(configs);
	}
	
	protected void separateEmailConfig(List<DicItemVo> configs, String emails, String itemCode, 
			String dicItemName, Date now, Long userId, String dicTypeCode) {
		String[] es = emails.split(",");
		DicItemVo vo = null;
		int i = 0;
		StringBuilder sBuilder = new StringBuilder(200);
		for(String e : es) {
			if(StringUtils.isBlank(e)) {
				continue;
			}
			if((i % 6) == 0) {
				if(vo != null) {
					vo.setDicItemDesc(sBuilder.toString());
					configs.add(vo);
					sBuilder.setLength(0);
				}
				vo = new DicItemVo();
				vo.setCreateTime(now);
				vo.setCreator(userId);
				vo.setDicItemCode(itemCode);
				vo.setDicTypeCode(dicTypeCode);
				vo.setDicItemName(dicItemName);
				vo.setStatus("1");
				vo.setSortNumb(0.0);
				i = 1;
			}else {
				i++;
				sBuilder.append(",");
			}
			sBuilder.append(e);
		}
		if(vo != null) {
			vo.setDicItemDesc(sBuilder.toString());
			configs.add(vo);
		}
	}
}
