package com.chevron.b2b.dao;

import com.chevron.b2b.model.ScheduleDistributor;
import com.chevron.b2b.model.ScheduleDistributorExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 活动分配经销商-wx_t_schedule_distributor数据库操作对象
 * <AUTHOR>
 * @version 1.0 2019-01-09 22:26
 */
public interface ScheduleDistributorMapper {

 	/**
	 * 插入活动分配经销商对象(非空属性)
	 * @param record 被插入活动分配经销商对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(ScheduleDistributor record);

	/**
	 * 更新活动分配经销商对象
	 * @param record 被修改活动分配经销商对象
	 * @param example 过滤活动分配经销商对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") ScheduleDistributor record, @Param("example") ScheduleDistributorExample example);

	/**
	 * 删除活动分配经销商对象
	 * @param example 被删除活动分配经销商对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleDistributorExample example);
    
 	/**
	 * 查询满足条件的活动分配经销商对象数量
	 * @param example 过滤活动分配经销商对象条件
	 * @return 满足条件活动分配经销商对象数量
	 */
    int countByExample(ScheduleDistributorExample example);

	/**
	 * 单表查询活动分配经销商对象
	 * @param example 过滤活动分配经销商对象条件
	 * @return 满足条件活动分配经销商对象集合
	 */
    List<ScheduleDistributor> selectByExample(ScheduleDistributorExample example);

	/**
	 * 列表查询活动分配经销商对象
	 * @param params 查询条件
	 * @return 满足条件活动分配经销商对象集合
	 */
    List<ScheduleDistributor> queryByParams(Map<String, Object> params);

    /**
     * 批量插入活动分配经销商对象
     * @param records 插入活动分配经销商集合
     * @return 数据库操作影响记录条数
     */
	int insertBatch(@Param("records") List<ScheduleDistributor> records);
}
