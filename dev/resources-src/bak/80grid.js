;(function($){
	//单元格基础对象
	var BaseTableCell = {
			rowIndex: 0,
			colIndex: 0,
			colSpan: 1,
			rowSpan: 1,
			mergedBy: null,
			spanCols: null, //被合并的单元格列索引集合
			spanRows: null, //被合并单元格行ID集合
			addColSpan: function(offset, mergedColIndex){
				var spanCell = null;
				if(this.mergedBy == null){
					this.colSpan += offset;
					spanCell = this;
				}else if(this.rowIndex == this.mergedBy.rowIndex){
					this.mergedBy.colSpan += offset;
					spanCell = this.mergedBy;
				}
				if(mergedColIndex != undefined && spanCell){
					if(spanCell.spanCols == null){
						spanCell.spanCols = [mergedColIndex];
					}else{
						spanCell.spanCols.push(mergedColIndex);
					}
				}
				return this;
			},
			addRowSpan: function(offset, mergedRowId){
				var spanCell = null;
				if(this.mergedBy == null){
					this.rowSpan += offset;
					spanCell = this;
				}else if(this.colIndex == this.mergedBy.colIndex){
					this.mergedBy.rowSpan += offset;
					spanCell = this.mergedBy;
				}
				if(mergedRowId != undefined && spanCell){
					if(spanCell.spanRows == null){
						spanCell.spanRows = [mergedRowId];
					}else{
						spanCell.spanRows.push(mergedColIndex);
					}
				}
				return this;
			},
			getPhysicalCell: function(){
				return this.mergedBy || this;
			}
	};
	//构建表头
	var buildHeader = function(headers, columns, grid){
		var sheet = [], firstRow = {cls: grid.clazz.HEADER_ROW_CLS, cells: [], id: 'grid_header_row' + grid.counter++};
		sheet.push(firstRow);
		for(var i = 0; i < headers.length; i++){
			buildHeaderCell(headers[i], 0, firstRow.cells.length, firstRow.cells, sheet, columns, grid);
		}
		return sheet;
	};
	//构建表头单元格
	var buildHeaderCell = function(column, rowIndex, colIndex, cells, sheet, columns, grid){
		var cell = cells[cells.length] = $.extend({}, BaseTableCell, {
			rowIndex: rowIndex,
			colIndex: colIndex,
			rowId: sheet[rowIndex].id,
			title: column.title,
			valign: 'middle',
			tdCls: (column.tdCls ? column.tdCls + ' ' : '') + 'ui-grid-col' + colIndex,
			cls: grid.clazz.HEADER_CELL_CLS,
			id: 'ui_grid_cell' + grid.counter++
		});
		grid.headerElMap[cell.id] = cell;
		if(column.children && column.children.length > 0){
			//初始化新行
			var newRow, newRowIndex = rowIndex + 1;
			if(sheet.length == newRowIndex){
				//创建新行
				newRow = {cls: grid.clazz.HEADER_ROW_CLS, cells: [], id: 'grid_header_row' + grid.counter++};
				sheet.push(newRow);
				//更新前面列的rowSpan属性
				for(var i = 0; i < colIndex; i++){
					newRow.cells.push($.extend({}, BaseTableCell, {
						colIndex: i,
						rowIndex: rowIndex,
						mergedBy: sheet[rowIndex].cells[i].addRowSpan(1, newRow.id).getPhysicalCell()
					}));
				}
			}else{
				newRow = sheet[newRowIndex];
			}
			for(var i = 0; i < column.children.length; i++){
				if(i > 0){
					var newColIndex = newRow.cells.length;
					//创建新列，更新前一列的colSpan属性
					for(var j = 0; j < newRowIndex; j++){
						var c = sheet[j].cells[newColIndex - 1];
						sheet[j].cells.push($.extend({}, BaseTableCell, {
							rowIndex: j,
							colIndex: newColIndex,
							mergedBy: sheet[j].cells[newColIndex - 1].addColSpan(1, newColIndex).getPhysicalCell()
						}));
					}
				}
				buildHeaderCell(column.children[i], newRowIndex, newRow.cells.length, newRow.cells, sheet, columns, grid);
			}
		}else{
			//叶子column
			columns.push($.extend({}, grid.clazz.columnDefaults, column));
			if(column.endLock){
				grid.endLockIndex = colIndex;
			}
			if(column.summary && grid.summary){
				if(!column.property){
					throw '合计列' + column.title + '的property属性不能为空';
				}
				grid.hasSummary = true;
			}
		}
	};
	//构建部分sheet
	var buildPartSheet = function(sheet, startColIndex, endColIndex, partFlag, elMap){
		var partSheet = [];
		for(var i = 0; i < sheet.length; i++){
			var row = sheet[i], isMergedRow = true, cells = [];
			for(var j = startColIndex; j <= endColIndex; j++){
				//构建新行数据
				var cell = row.cells[j];
				if(cell.mergedBy == null){
					isMergedRow = false;
				}
				cells.push(cell);
			}
			if(isMergedRow){
				partSheet[partSheet.length - 1].mergeRows.push(row.id);
				//整行数据被合并
				for(var j = startColIndex; j <= endColIndex; j++){
					row.cells[j].addRowSpan(-1);
				}
			}else{
				var newRow = partSheet[partSheet.length] = {
						id: row.id,
						domId: row.id + partFlag, //rowId用来定位插入新数据
						cls: row.cls,
						cells: cells,
						mergeRows: []
					};
				elMap[newRow.domId] = newRow;
			}
		}
		return partSheet;
	};

	//渲染表内容
	var renderSheetBody = function(sheet, htmlBuffer, grid){
		for(var i = 0; i < sheet.length; i++){
			var row = sheet[i];
			htmlBuffer.push('<tr id="', row.domId ? row.domId : row.id, '" class="', row.cls, '">');
			for(var j = 0; j < row.cells.length; j += row.cells[j].colSpan){
				var cell = row.cells[j];
				htmlBuffer.push('<td align="center" valign="', cell.valign, '" id="', cell.id, '"');
				if(cell.colSpan > 1){
					htmlBuffer.push(' colSpan="', cell.colSpan, '"');
				}
				var tdCls = 'ui-grid-td ui-grid-td-colspan' + cell.colSpan + ' ' + cell.tdCls, cls = 'ui-grid-cell ' + cell.cls;
				htmlBuffer.push(' class="', tdCls, '"');
				if(cell.rowSpan > 1){
					htmlBuffer.push(' rowSpan="', cell.rowSpan, '"');
				}
				
				htmlBuffer.push('><div class="', cls, '">', cell.title, '</div></td>');
			}
			htmlBuffer.push('</tr>');
		}
	};
	//列锁定处理器
	var renderHandlers = {
			colLock: {
				lockFlag: '_locked',
				unlockFlag: '_unlocked',
				//渲染x轴滚动区
				renderXScroll: function(grid){
					grid.lockedXScrollEmpty = $('<div class="ui-grid-x-scroll-empty"></div>').appendTo(grid.xScrollContainer);
					grid.xScrollbar = UI.XScrollbar.init($('<div class="ui-grid-x-scroll-bar"></div>')
							.appendTo(grid.xScrollContainer)[0], {
						overflow: grid.xOverflow,
						scroll: function(scrollLeft){
							renderHandlers.colLock.xscroll(scrollLeft, grid);
						}
					});
				},
				//渲染表
				buildTableHtml: function(sheet, elMap, grid){
					//渲染锁定区域
					var h = ['<div class="ui-grid-locked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>'];
					//渲染表格宽度组
					for(var i = 0; i <= grid.endLockIndex; i++){
						h.push('<col/>');
					}
					h.push('</colgroup><tbody>');
					//拆锁定区域sheet
					var lockedSheet = buildPartSheet(sheet, 0, grid.endLockIndex, this.lockFlag, elMap);
					//渲染锁定区域表
					renderSheetBody(lockedSheet, h, grid);
					h.push('</tbody></table></div><div class="ui-grid-unlocked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>');
					//渲染表格宽度组
					for(var i = grid.endLockIndex + 1; i < grid.columns.length; i++){
						h.push('<col/>');
					}
					h.push('</colgroup><tbody>');
					//拆未锁定区域sheet
					var unlockedSheet = buildPartSheet(sheet, grid.endLockIndex + 1, grid.columns.length - 1, this.unlockFlag, elMap);
					//渲染未锁定区域表
					renderSheetBody(unlockedSheet, h, grid);
					h.push('</tbody></table></div>');
					return h;
				},
				emptySummary: function(grid){
					if(grid.sumRender == 'header'){
						grid.headerContainer.find('>.ui-grid-locked>table>tbody>.ui-grid-summary-row').remove();
					}else{
						grid.summaryContainer.empty();
					}
				},
				renderSummary: function(summarySheet, grid){
					//拆锁定区域sheet
					var lockedSheet = buildPartSheet(summarySheet, 0, grid.endLockIndex, this.lockFlag, grid.dataElMap);
					//拆未锁定区域sheet
					var unlockedSheet = buildPartSheet(summarySheet, grid.endLockIndex + 1, grid.columns.length - 1, this.unlockFlag, grid.dataElMap);
					if(grid.sumRender == 'header'){
						var h = [];
						//给合计行加样式
						for(var i = 0; i < summarySheet.length; i++){
							var row = summarySheet[i];
							if(row.cls){
								row.cls += ' ui-grid-summary-row';
							}
						}
						//渲染锁定区域表
						renderSheetBody(lockedSheet, h, grid);
						grid.headerContainer.find('>.ui-grid-locked>table>tbody').append(h.join(''));
						h = [];
						//渲染未锁定区域表
						renderSheetBody(unlockedSheet, h, grid);
						grid.headerContainer.find('>.ui-grid-unlocked>table>tbody').append(h.join(''));
					}else{
						//渲染锁定区域
						var h = ['<div class="ui-grid-locked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>'];
						//渲染表格宽度组
						for(var i = 0; i <= grid.endLockIndex; i++){
							h.push('<col/>');
						}
						h.push('</colgroup><tbody>');
						//渲染锁定区域表
						renderSheetBody(lockedSheet, h, grid);
						h.push('</tbody></table></div><div class="ui-grid-unlocked"><table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table"><colgroup>');
						//渲染表格宽度组
						for(var i = grid.endLockIndex + 1; i < grid.columns.length; i++){
							h.push('<col/>');
						}
						h.push('</colgroup><tbody>');
						//渲染未锁定区域表
						renderSheetBody(unlockedSheet, h, grid);
						h.push('</tbody></table></div>');
						grid.summaryContainer.append(h.join(''));
					}
				},
				fixResize: function(width, height, grid, scrollbarChanged, scrollbarWidth){
					if(grid.width == width){
						return;
					}
					if(!scrollbarChanged){
						grid.yScrollbar.setContentHeight(0).setHeight(50);
						scrollbarWidth = UI.getOuterWidth(grid.yScrollbar.element);
					}
					var allCells = [], gridWidth = width - scrollbarWidth;
					//设置列宽
					this._fixColWidth(gridWidth, allCells, grid);
					//设置分页区宽度
					if(grid.pagingContainer){
						UI.setWidth(grid.pagingContainer, gridWidth);
					}
					//设置行高
					this._fixRowHeight(height, allCells, grid);
					var newScrollbarWidth = UI.getOuterWidth(grid.yScrollbar.element);
					if(newScrollbarWidth != scrollbarWidth){
						this.fixResize(width, height, grid, true, newScrollbarWidth);
						return;
					}else if(scrollbarWidth > 1){
						UI.setWidth(grid.headerYScrollEmpty, scrollbarWidth);
						if(grid.summaryYScrollEmpty){
							UI.setWidth(grid.summaryYScrollEmpty, scrollbarWidth);
						}
						UI.setWidth(grid.xyScrollEmpty, scrollbarWidth);
//						UI.setWidth(grid.yScrollContainer, scrollbarWidth + 1);
					}
					UI.setWidth(grid.headerContainer, gridWidth);
					UI.setWidth(grid.bodyContainer, gridWidth);
					if(grid.summaryContainer){
						UI.setWidth(grid.summaryContainer, gridWidth);
					}
					UI.setWidth(grid.xScrollContainer, gridWidth);
					if(grid.pagingContainer){
						UI.setWidth(grid.pagingContainer, gridWidth);
					}
				},
				//x轴滚动
				xscroll: function(scrollLeft, grid){
					var left = -scrollLeft;
					grid.headerContainer.find('>.ui-grid-unlocked>table').css('left', left + 'px');
					grid.bodyContainer.find('>.ui-grid-unlocked>table').css('left', left + 'px');
					if(grid.summaryContainer){
						grid.summaryContainer.find('>.ui-grid-unlocked>table').css('left', left + 'px');
					}
				},
				//y轴滚动
				yscroll: function(scrollTop, grid){
					var top = -scrollTop;
					grid.bodyContainer.find('>.ui-grid-locked>table').css('top', top + 'px');
					grid.bodyContainer.find('>.ui-grid-unlocked>table').css('top', top + 'px');
				},
				//设置行高
				_fixRowHeight: function(height, allCells, grid){
					var rowHeightMap = {}, rowGroupMap = {};
					//初始化行高集合和行组集合
					for(var i = 0; i < allCells.length; i++){
						var cell = allCells[i];
						if(cell.spanRows){
							var rowGroup = {height: 0, totalHeight: cell.height, rowId: cell.rowId, rows: {}};
							for(var j = 0; j < cell.spanRows.length; j++){
								rowGroup.rows[cell.spanRows[j]] = 0;
							}
							rowGroupMap[cell.id] = rowGroup;
						}else{
							rowHeightMap[cell.rowId] = Math.max(rowHeightMap[cell.rowId] || 0, cell.height);
						}
					}
					//分配行组的每行高度
					this._distributeRowHeight(rowGroupMap, rowHeightMap);
					//设置每行高度
					var thiz = this;
					//表头标题区单元格高度设置
					grid.headerContainer.find('.ui-grid-header-row').each(function(){
						thiz._setRowHeight($(this), rowHeightMap, grid.headerElMap[this.id], grid);
					});
					//表头汇总区单元格高度设置
					grid.headerContainer.find('.ui-grid-data-row').each(function(){
						thiz._setRowHeight($(this), rowHeightMap, grid.dataElMap[this.id], grid);
					});
					//数据体单元格高度设置
					grid.bodyContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row').each(function(){
						thiz._setRowHeight($(this), rowHeightMap, grid.dataElMap[this.id], grid);
					});
					if(grid.summaryContainer){
						//汇总区单元格高度设置
						grid.summaryContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row').each(function(){
							thiz._setRowHeight($(this), rowHeightMap, grid.dataElMap[this.id], grid);
						});
					}
					//设置header区高度
					var h = UI.getOuterHeight(grid.headerContainer.find('>.ui-grid-locked')), contentViewHeight = height;
					UI.setHeight(grid.headerContainer, h);
					UI.setHeight(grid.headerYScrollEmpty, h);
					contentViewHeight -= h;
					//设置内容区高度
					var $bodyLocked = grid.bodyContainer.find('>.ui-grid-locked');
					if($bodyLocked.length > 0){
						h = UI.getOuterHeight($bodyLocked);
						UI.setHeight(grid.bodyContainer, h);
						grid.yScrollbar.setContentHeight(h);
					}else{
						grid.yScrollbar.setContentHeight(0);
					}
					//设置汇总区高度
					if(grid.summaryContainer){
						h = UI.getOuterHeight(grid.summaryContainer.find('>.ui-grid-locked'));
						UI.setHeight(grid.summaryContainer, h);
						UI.setHeight(grid.summaryYScrollEmpty, h);
						contentViewHeight -= h;
					}
					if(grid.pagingContainer){
						contentViewHeight -= UI.getOuterHeight(grid.pagingContainer);
					}
					contentViewHeight -= UI.getOuterHeight(grid.xScrollContainer);
					UI.setHeight(grid.bodyContainer, contentViewHeight);
					if($bodyLocked.length > 0){
						grid.bodyContainer.find('>.ui-grid-locked,>.ui-grid-unlocked').each(function(){
							UI.setHeight($(this), contentViewHeight);
						});
					}else{
						UI.setHeight(grid.bodyContainer.find('>.no-data-comment'), contentViewHeight);
					}
					grid.yScrollbar.setHeight(contentViewHeight).triggerScroll();
				},
				//设置行高
				_setRowHeight: function(rowElement, rowHeightMap, row, grid){
					var h = rowHeightMap[row.id] + grid._cellTotalPaddingY;
					if(row.mergeRows){
						for(var i = 0; i < row.mergeRows.length; i++){
							h += rowHeightMap[row.mergeRows[i]] ? (rowHeightMap[row.mergeRows[i]] + grid._cellTotalPaddingY) : 0;
						}
					}
					UI.setHeight(rowElement, h);
				},
				//分配行高
				_distributeRowHeight: function(rowGroupMap, rowHeightMap){
					//重置各行组行高
					this._resetRowGroupHeight(rowGroupMap, rowHeightMap);
					var done = true;
					for(var k in rowGroupMap){
						//将多余行高分配给单元格所在行
						rowHeightMap[rowGroupMap.rowId] += rowGroupMap[k].totalHeight - rowGroupMap[k]._cth;
						delete rowGroupMap[k];
						done = false;
						break;
					}
					if(!done){
						//重新分配行高
						this._distributeRowHeight(rowGroupMap, rowHeightMap);
					}
				},
				//重置行组合高度
				_resetRowGroupHeight: function(rowGroupMap, rowHeightMap){
					for(var k in rowGroupMap){
						var rowGroup = rowGroupMap[k], height = rowGroup.hegith = Math.max(rowGroup.height, rowHeightMap[rowGroup.rowId]);
						for(var l in rowGroup.rows){
							height += (rowHeightMap[l] = Math.max(rowHeightMap[l], rowHeightMap[l])) + grid._cellTotalPaddingY;
						}
						//当单元格高度不大于总行高，处理完成
						if(height >= rowGroup.totalHeight){
							delete rowGroupMap[k];
						}else{
							rowGroup._cth = height; //行组当前总行高
						}
					}
				},
				//设置列宽
				_fixColWidth: function(gridWidth, allCells, grid){
					//初始化表格最小宽度和最小固定宽度
					if(!grid._minWidth){
						grid._fixedWidth = 0; //表格固定列总宽度
						grid._unfixedWidth = 0; //表格非固定列总宽度
						grid._fixedWidthCols = 0; //表格固定列列数
						grid._unfixedWidthCols = 0; //表格非固定列列数
						grid._cellTotalPaddingX = grid.cellPaddingX * 2; //单元格X总padding
						grid._cellTotalPaddingY = grid.cellPaddingY * 2; //单元格Y总padding
						for(var i = 0; i < grid.columns.length; i++){
							var col = grid.columns[i];
							if(col.fixedWidth){
								grid._fixedWidth += col.width;
								grid._fixedWidthCols++;
							}else{
								grid._unfixedWidth += col.width;
								grid._unfixedWidthCols++;
							}
						}
						grid._fixedWidthPadding = grid._fixedWidthCols * grid._cellTotalPaddingX; //固定宽度列占用总padding
						grid._unfixedWidthPadding = grid._unfixedWidthCols * grid._cellTotalPaddingX; //非固定宽度列占用总padding
						grid._minWidth = grid._fixedWidth + grid._unfixedWidth + grid._fixedWidthPadding + grid._unfixedWidthPadding;
						grid._actualFixedWidth = grid._fixedWidth + grid._fixedWidthPadding; //表格固定列实际总宽度
					}
					var unfixedActualWidth, contentWidth;
					if(gridWidth >= grid._minWidth){
						//无需锁定列
						unfixedActualWidth = gridWidth - grid._actualFixedWidth;
						contentWidth = gridWidth;
					}else{
						//需锁定列
						unfixedActualWidth = grid._minWidth - grid._actualFixedWidth;
						contentWidth = grid._minWidth;
					}
					var lockedWidth = 0, unfixedWidthRate = unfixedActualWidth / grid._unfixedWidth;
					//锁定列
					for(var i = 0; i <= grid.endLockIndex; i++){
						var col = grid.columns[i];
						if(col.fixedWidth){
							lockedWidth += col.width + grid._cellTotalPaddingX;
						}else{
							col._actualWidth = unfixedWidthRate * col.width;
							lockedWidth += col._actualWidth;
						}
					}
					var uncompleteMap = {};
					//设置锁定区列宽
					this._setLockedAreaWidth(lockedWidth, grid);
					for(var i = 0; i <= grid.endLockIndex; i++){
						var col = grid.columns[i], widthByPercent, w;
						if(col.fixedWidth){
							w = col.width;
							widthByPercent = 100 * (w + grid._cellTotalPaddingX) / lockedWidth + '%';
						}else{
							w = col._actualWidth - grid._cellTotalPaddingX;
							widthByPercent = 100 * col._actualWidth / lockedWidth + '%';
						}
						this._setColWidth(i, widthByPercent, w, uncompleteMap, allCells, grid);
					}
					UI.setWidth(grid.xScrollContainer.find('.ui-grid-x-scroll-empty'), lockedWidth);
					//设置内容区非固定列宽度
					var unlockedWidth = contentWidth - lockedWidth, unlockedViewWidth = gridWidth - lockedWidth;
					this._setUnlockedAreaWidth(unlockedWidth, unlockedViewWidth, grid);
					uncompleteMap = {};
					for(var i = grid.endLockIndex + 1; i < grid.columns.length; i++){
						var col = grid.columns[i], widthByPercent, w;
						if(col.fixedWidth){
							w = col.width;
							widthByPercent = 100 * (w + grid._cellTotalPaddingX) / unlockedWidth + '%';
						}else{
							w = col._actualWidth - grid._cellTotalPaddingX;
							widthByPercent = 100 * col._actualWidth / unlockedWidth + '%';
						}
						this._setColWidth(i, widthByPercent, w, uncompleteMap, allCells, grid);
					}
					grid.xScrollbar.setWidth(unlockedViewWidth).setContentWidth(unlockedWidth).triggerScroll();
					var barHeight = UI.getOuterHeight(grid.xScrollbar.element);
					if(barHeight < 2){
						grid.xScrollContainer.height(0);
					}else{
						UI.setHeight(grid.xScrollContainer, barHeight);
						UI.setHeight(grid.xScrollContainer.find('.ui-grid-x-scroll-empty'), barHeight);
					}
				},
				_setUnlockedAreaWidth: function(contentWidth, viewWidth, grid){
					UI.setWidth(grid.headerContainer.find('.ui-grid-unlocked>table'), contentWidth);
					UI.setWidth(grid.headerContainer.find('.ui-grid-unlocked'), viewWidth);
					UI.setWidth(grid.bodyContainer.find('.ui-grid-unlocked>table'), contentWidth);
					UI.setWidth(grid.bodyContainer.find('.ui-grid-unlocked'), viewWidth);
					if(grid.summaryContainer){
						UI.setWidth(grid.summaryContainer.find('.ui-grid-unlocked>table'), contentWidth);
						UI.setWidth(grid.summaryContainer.find('.ui-grid-unlocked'), viewWidth);
					}
				},
				_setLockedAreaWidth: function(width, grid){
					UI.setWidth(grid.headerContainer.find('.ui-grid-locked>table'), width);
					UI.setWidth(grid.bodyContainer.find('>.ui-grid-locked>table'), width);
					if(grid.summaryContainer){
						UI.setWidth(grid.summaryContainer.find('>.ui-grid-locked>table'), width);
					}
				},
				_setColWidth: function(colIndex, widthByPercent, width, uncompleteMap, allCells, grid){
					var thiz = this;
					//处理未完成单元格宽度设置
					for(var k in uncompleteMap){
						var c = uncompleteMap[k];
						c.width += width + grid._cellTotalPaddingX;
						if(--c.leftCols == 0){
							//单元格宽度处理完成
							delete uncompleteMap[k];
							var $cell = $('#' + k).find('>.ui-grid-cell');
							UI.setWidth($cell, c.width);
//							UI.setHeight($cell, -1);
							c.cell.height = UI.getOuterHeight($cell);
							allCells.push(c.cell);
						}
					}
					$(grid.headerContainer.find('col')[colIndex]).css('width', widthByPercent);
					//表头标题区单元格宽度设置
					grid.headerContainer.find('.ui-grid-header-row .ui-grid-col' + colIndex).each(function(){
						thiz._setCellWidth($(this), width, uncompleteMap, grid.headerElMap[this.id], allCells, grid);
					});
					//表头汇总区单元格宽度设置
					grid.headerContainer.find('.ui-grid-data-row .ui-grid-col' + colIndex).each(function(){
						thiz._setCellWidth($(this), width, uncompleteMap, grid.dataElMap[this.id], allCells, grid);
					});
					//数据体宽度设置
					$(grid.bodyContainer.find('>div>.ui-grid-table>colgroup>col')[colIndex]).css('width', widthByPercent);
					//数据体单元格宽度设置
					grid.bodyContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row>.ui-grid-col' + colIndex).each(function(){
						thiz._setCellWidth($(this), width, uncompleteMap, grid.dataElMap[this.id], allCells, grid);
					});
					if(grid.summaryContainer){
						$(grid.summaryContainer.find('>.ui-grid-locked>.ui-grid-table>colgroup>col')[colIndex]).css('width', widthByPercent);
						//汇总区单元格宽度设置
						grid.summaryContainer.find('>div>.ui-grid-table>tbody>.ui-grid-data-row>.ui-grid-col' + colIndex).each(function(){
							thiz._setCellWidth($(this), width, uncompleteMap, grid.dataElMap[this.id], allCells, grid);
						});
					}
				},
				_setCellWidth: function(tdElement, width, uncompleteMap, cell, allCells, grid){
					//单元格有合并列
					if(cell.spanCols){
						uncompleteMap[cell.id] = {width: width, id: cell.id, leftCols: cell.spanCols.length, cell: cell};
					}else{
						var $cell = tdElement.find('>.ui-grid-cell');
						UI.setWidth($cell, width);
//						UI.setHeight($cell, -1);
						cell.height = UI.getOuterHeight($cell);
						allCells.push(cell);
					}
				}
			},
			colUnlock: {
				
			}
		};
	UI.Grid = UI.extend(UI.DomBase, {
		key: 'ui.grid',
		cssCls: 'ui-grid',
		counter: 1,
		DATA_CELL_CLS: 'ui-grid-data-cell', //数据单元格样式
		HEADER_CELL_CLS: 'ui-grid-header-cell', //表头单元格样式
		DATA_ROW_CLS: 'ui-grid-data-row', //数据行样式
		HEADER_ROW_CLS: 'ui-grid-header-row', //表头行样式
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom).addClass(this.cssCls), p = this.newInstance($d, opts);
			//初始化容器为panel
			p.panel = UI.Panel.init(dom, {
				fit: true,
				resize: function(){
					p.trigger('resize');
				}
			});
			p.columns = [];
			p.endLockIndex = -1;
			p.hasSummary = false; //表格是否有汇总
			//生成grid ID
			p.id = 'tree_grid' + this.counter++;
			//初始化表格的计数器，用以生成row和cell的ID
			p.counter = 1; 
			//初始化表格row和cell元素对象的ID映射表
			p.headerElMap = {};
			//构建表头
			var headerSheet = buildHeader(p.headers, p.columns, p);
			//初始化表格渲染处理器
			p.renderHandler = (p.endLockIndex > -1 && p.endLockIndex != p.columns.length - 1) ? 
					renderHandlers.colLock : renderHandlers.colUnlock;
			p.yScrollContainer = $('<div class="ui-grid-y-scroll"></div>').appendTo($d);
			//渲染表头
			p.headerContainer = $('<div class="ui-grid-header"></div>').appendTo($d);
			p.headerElement = $(p.renderHandler.buildTableHtml(headerSheet, p.headerElMap, p).join('')).appendTo(p.headerContainer);
			p.headerYScrollEmpty = $('<div class="ui-grid-y-scroll-empty"></div>').appendTo(p.yScrollContainer);
			//body
			p.bodyContainer = $('<div class="ui-grid-body"></div>').appendTo($d);
			p.yScrollbar = UI.YScrollbar.init($('<div class="ui-grid-y-scroll-bar ui-grid-y-scroll-' + p.yOverflow + 
					'"></div>').appendTo(p.yScrollContainer)[0], {
				overflow: p.yOverflow,
				scroll: function(scrollTop) {
					p.renderHandler.yscroll(scrollTop, p);
				}
			});
			//汇总
			if(p.hasSummary && p.sumRender == 'footer'){
				p.summaryContainer = $('<div class="ui-grid-summary"></div>').appendTo($d);
				p.summaryYScrollEmpty = $('<div class="ui-grid-y-scroll-empty"></div>').appendTo(p.yScrollContainer);
			}
			p.xScrollContainer = $('<div class="ui-grid-x-scroll"></div>').appendTo($d);
			p.renderHandler.renderXScroll(p);
			p.xyScrollEmpty = $('<div class="ui-grid-x-scroll-empty ui-grid-y-scroll-empty"></div>').appendTo(p.yScrollContainer);
			//初始化分页控件
			if(p.paging){
				p.pagingContainer = $('<div class="ui-grid-paging"></div>').appendTo($d);
				p.paging = UI.Paging.init(p === true ? null : p.paging);
				p.paging.bindGrid(p);
			}
			//初始化表格数据仓库
			if(!p.store){
				p.store = UI.Store.init(p);
			}
			p.store.bindGrid(p);
			if(p.store.autoLoad){
				p.store.load();
			}
			return p;
		},
		alignCls: {
			left: 'text-left',
			right: 'text-right',
			center: 'text-center'
		},
		buildDataSheet: function(records, grid){
			var dataSheet = [], len = records.length;
			for(var i = 0; i < len; i++){
				var cls = grid.clazz.DATA_ROW_CLS;
				if(grid.dataRowLoopCls && grid.dataRowLoopCls.length > 0){
					cls += (' ' + grid.dataRowLoopCls[i % grid.dataRowLoopCls.length]);
				}
				var record = records[i], clen = grid.columns.length, 
					row = {cls: cls, cells: [], id: 'grid_data' + grid.counter++, record: record};
				dataSheet.push(row);
				grid.dataElMap[row.id] = row;
				for(var j = 0; j < clen; j++){
					var col = grid.columns[j], cell = row.cells[j] = $.extend({}, BaseTableCell, {
						rowId: row.id,
						colIndex: j,
						rowIndex: i
					});
					if(col.rowMergeable && i > 0){
						//行可合并
						prerowCell = dataSheet[i - 1].cells[j];
						if(prerowCell.mergedBy && prerowCell.mergedBy == row.cells[j - 1].mergedBy){
							//单元格处于合并区内
							cell.mergedBy = prerowCell.mergedBy;
							continue;
						}else{
							var v = col.propertyProcessor(record, col.valueProperty);
							if(prerowCell.getPhysicalCell().colIndex == j && prerowCell.getPhysicalCell().value == v){
								//合并同一列
								cell.mergedBy = prerowCell.addRowSpan(1, row.id).getPhysicalCell();
								continue;
							}
							cell.value = v;
						}
					}
					cell.title = col.formatter(col.propertyProcessor(record, col.property), record, i, row.id);
					if(col.colMergeable && j != 0 && j != grid.endLockIndex + 1){
						//列可合并
						if(cell.title == grid.colMergeableText && (dataSheet[i].cells[j - 1].mergedBy == null 
								|| dataSheet[i].cells[j - 1].mergedBy.rowIndex == i)){
							cell.mergedBy = dataSheet[i].cells[j - 1].addColSpan(1, j).getPhysicalCell();
							continue;
						}
					}
					//未合并
					cell.id = 'grid_cell' + grid.counter++;
					cell.cls = grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[col.align];
					cell.tdCls = (col.tdCls ? col.tdCls + ' ' : '') + 'ui-grid-col' + j;
					cell.valign = 'middle';
					if(col.tdCls){
						cell.tdCls += (' ' + col.tdCls);
					}
					grid.dataElMap[cell.id] = cell;
				}
			}
			return dataSheet;
		},
		//构建汇总二维表
		buildSummarySheet: function(data, grid){
			var summarySheet = [], i = 0;
			if(data.pageSummary){
				summarySheet.push(this.buildSummaryRow(grid.pageSummaryTitle, data.pageSummary, i++, grid));
			}
			summarySheet.push(this.buildSummaryRow(grid.summaryTitle, data.summary, i++, grid));
			return summarySheet;
		},
		//构建汇总行
		buildSummaryRow: function(title, record, rowIndex, grid){
			var row = {id: 'grid_data' + grid.counter++, cls: grid.clazz.DATA_ROW_CLS, cells: []},
				firstCol = grid.columns[0],
				titleCell = $.extend({}, BaseTableCell, {
					title: title,
					colIndex: 0,
					tdCls: (firstCol.tdCls ? firstCol.tdCls + ' ' : '') + 'ui-grid-col0',
					cls: grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[firstCol.align],
					rowId: row.id,
					valign: 'middle',
					id: 'grid_cell' + grid.counter++,
					rowIndex: rowIndex
				});
			grid.dataElMap[row.id] = row;
			row.cells.push(titleCell);
			grid.dataElMap[titleCell.id] = titleCell;
			var i = 1, maxSpanCol = grid.endLockIndex >= 0 ? grid.endLockIndex : 100000;
			//处理标题合并单元格
			for(; i <= maxSpanCol && !grid.columns[i].summary; i++){
				row.cells.push($.extend({}, BaseTableCell, {
					colIndex: i,
					mergedBy: titleCell.addColSpan(1, i),
					rowIndex: rowIndex
				}));
			}
			//处理非合并部分合计单元格
			for(; i < grid.columns.length; i++){
				var col = grid.columns[i], cell = row.cells[row.cells.length] = $.extend({}, BaseTableCell, {
					title: col.summary ? col.formatter(record[col.property], record, rowIndex, row.id) : '',
					rowId: row.id,
					tdCls: (col.tdCls ? col.tdCls + ' ' : '') + 'ui-grid-col' + i,
					cls: grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[col.align],
					colIndex: i,
					valign: 'middle',
					id: 'grid_cell' + grid.counter++,
					rowIndex: rowIndex
				});
				grid.dataElMap[cell.id] = cell;
			}
			return row;
		},
		base: {
			beforeEvents: {
				resize: function(){
					var width = UI.getInnerWidth(this.element), height = this.panel.height;
					if(height > 0){
						height = UI.getInnerHeight(this.element);
						UI.setHeight(this.yScrollContainer, height);
					}
					this.renderHandler.fixResize(width, height, this);
				}
			},
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度
			 * @param height [必选]控件新高度
			 */
			resize: function(width, height){
				this.panel.resize(width, height);
			},
			//根据记录ID获取记录
			getRecordById: function(recordId){
				return this.dataElMap[recordId].record;
			},
			//渲染数据
			renderData: function(data){
				//初始化数据
				this.dataElMap = {};
				this.bodyContainer.empty();
				if(this.hasSummary){
					this.renderHandler.emptySummary(this);
				}
				//渲染数据
				if(!data.records || data.records.length == 0){
					this.bodyContainer.append('<div class="no-data-comment">' + this.noDataMsg + '</div>');
				}else{
					this.bodyContainer.append(this.renderHandler.buildTableHtml(this.clazz.buildDataSheet(data.records, this), this.dataElMap, this).join(''));
				}
				//渲染汇总
				if(data.summary){
					var summarySheet = this.clazz.buildSummarySheet(data, this);
					this.renderHandler.renderSummary(summarySheet, this);
				}
			},
			//刷新单元格内容
			refreshCell: function(recordId, propertyName){
				
			}
		},
		columnDefaults: {
			title: '', //列表头
			property: '', //列显示对应属性
			valueProperty: '', //列值对应属性(rowMergeable为true时有效)，默认为列显示对应属性
			endLock: false, //是否最后的锁定列(叶子列的最后一列为true列有效)
			formatter: function(value, record, index, recordId){return value ? value : (value === 0 ? '0' : '');}, //列显示文本格式化
			'export': true, //是否需要导出
			tdCls: '', //列td样式
			rowMergeable: false, //行是否可合并的。true-合并同一列相邻行valuePorperty相等的单元格。默认为false。
			colMergeable: false, //列是否可合并。true-同一行，合并显示文本为表格对象的colMergeableText值的单元格与同一行前一个单元格。默认为false
			align: 'left', //显示文本水平对齐方式
			nowrap: false, //强制不换行
			width: 20, //列宽(如果有列锁定且所有列的宽度总和大于总宽度时为列实际宽度，否则代表列宽占的比例)
			minWidth: 20, //最小列宽(固定宽度列或锁行时无效)
			fixedWidth: false, //列固定宽度,
			children: null, //子表头
			sortable: true, //列是否排序
			summary: false, //合计列。为true时，property属性不能为空
			propertyProcessor: function(record, propertyName){return record[propertyName];} //属性值处理器
		},
		defaults: {
			headers: null, //列定义数组
			paging: false, //是否分页
			store: null, //数据仓库实例
			autoLoad: true, //自动加载数据
			sortable: false, //表格是否排序总开关
			sumRender: 'footer', //合计行渲染位置。header-表头，footer-表尾
			summaryTitle: '总合计', //合计标题
			pageSummaryTitle: '本页合计', //本页合计标题
			showPageSummary: true, //是否显示分页汇总
			dataRowLoopCls: ['ui-grid-data-row-even', 'ui-grid-data-odd'], //数据行循环样式
			colMergeableText: '<merge/>', //列可合并特殊显示文本
			noDataMsg: '无数据', //没有数据显示文本
			cellPaddingX: 1.5, //单元格X轴方向间距
			cellPaddingY: 1.5, //单元格Y轴方向间距
			xOverflow: 'auto', //X轴滚动。scroll, auto
			yOverflow: 'scroll', //Y轴滚动。scroll, auto
			drill: function(record){} //下钻
		}
	});
})(jQuery);