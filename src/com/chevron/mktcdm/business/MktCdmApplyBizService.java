package com.chevron.mktcdm.business;

import com.common.exception.WxPltException;
import com.sys.workflow.utils.IFormBizService;
import com.chevron.cdm_mkt.model.pdf.MktPdfInfo;
import com.chevron.mktcdm.model.CdmWorkshopVo;
import com.chevron.mktcdm.model.MktCdmApply;
import com.chevron.mktcdm.model.MktCdmApplyExample;
import com.chevron.mktcdm.model.MktCdmApplyParams;
import com.chevron.mktcdm.model.QueryStoreParams;

import java.util.List;
import java.util.Map;

/**
 * Wx T Mkt Cdm Apply业务接口
 * <AUTHOR>
 * @version 1.0 2020-03-26 11:54
 */
public interface MktCdmApplyBizService  extends IFormBizService<MktCdmApply>{
	
	/**
	 * 保存Wx T Mkt Cdm Apply
	 * @param record 被插入Wx T Mkt Cdm Apply
	 * @throws WxPltException
	 */
	public void insert(MktCdmApply record) throws WxPltException;
	
	/**
	 * 修改Wx T Mkt Cdm Apply
	 * @param record 被修改Wx T Mkt Cdm Apply
	 * @throws WxPltException
	 */
	public void update(MktCdmApply record) throws WxPltException;
	
	/**
	 * 删除Wx T Mkt Cdm Apply
	 * @param ids 被删除Wx T Mkt Cdm Applyid集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的Wx T Mkt Cdm Apply
	 * @param example 被删除Wx T Mkt Cdm Apply满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(MktCdmApplyExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<MktCdmApply> queryByExample(MktCdmApplyExample example) throws WxPltException;
	
	/**
	 * 列表查询Wx T Mkt Cdm Apply对象
	 * @param params 查询条件
	 * @return 满足条件Wx T Mkt Cdm Apply对象集合
	 */
    public List<MktCdmApply> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 修改页面修改Wx T Mkt Cdm Apply
	 * @param record 被修改Wx T Mkt Cdm Apply
	 * @throws WxPltException
	 */
	public void updateForEditPage(MktCdmApply record) throws WxPltException;
	
	/**
	 * 获取指定主键的Wx T Mkt Cdm Apply对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public MktCdmApply getBean(Long id) throws WxPltException;	
	
	public void queryDraftForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException;
	public void queryTodoForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException;
	public void queryDoneForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException;
	public void queryAllApplyForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException;

	public List<MktCdmApply> queryDraftForPage(MktCdmApplyParams params) throws WxPltException;
	
	public List<MktCdmApply> queryTodoForPage(MktCdmApplyParams params) throws WxPltException;
	
	public List<MktCdmApply> queryDoneForPage(MktCdmApplyParams params) throws WxPltException;
	
	MktPdfInfo getPdfInfo(Long id) throws WxPltException;
	
	public List<MktCdmApply> queryAllApplyForPage(MktCdmApplyParams params) throws WxPltException;
	
	List<CdmWorkshopVo> queryStorePage(QueryStoreParams params)throws WxPltException;
	
	void queryStorePage(QueryStoreParams params, Map<String, Object> resultMap)throws WxPltException;
	
	Integer queryStoreApplyCount(Long storeId) throws WxPltException;
}
