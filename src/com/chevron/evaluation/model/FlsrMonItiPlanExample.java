package com.chevron.evaluation.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 月行程计划表单表查询条件
 * <AUTHOR>
 * @version 1.0 2019-01-17 10:53
 */
public class FlsrMonItiPlanExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public FlsrMonItiPlanExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProgramIdIsNull() {
            addCriterion("program_id is null");
            return (Criteria) this;
        }

        public Criteria andProgramIdIsNotNull() {
            addCriterion("program_id is not null");
            return (Criteria) this;
        }

        public Criteria andProgramIdEqualTo(Long value) {
            addCriterion("program_id =", value, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdNotEqualTo(Long value) {
            addCriterion("program_id <>", value, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdGreaterThan(Long value) {
            addCriterion("program_id >", value, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdGreaterThanOrEqualTo(Long value) {
            addCriterion("program_id >=", value, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdLessThan(Long value) {
            addCriterion("program_id <", value, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdLessThanOrEqualTo(Long value) {
            addCriterion("program_id <=", value, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdIn(List<Long> values) {
            addCriterion("program_id in", values, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdNotIn(List<Long> values) {
            addCriterion("program_id not in", values, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdBetween(Long value1, Long value2) {
            addCriterion("program_id between", value1, value2, "programId");
            return (Criteria) this;
        }

        public Criteria andProgramIdNotBetween(Long value1, Long value2) {
            addCriterion("program_id not between", value1, value2, "programId");
            return (Criteria) this;
        }

        public Criteria andSalesIdIsNull() {
            addCriterion("sales_id is null");
            return (Criteria) this;
        }

        public Criteria andSalesIdIsNotNull() {
            addCriterion("sales_id is not null");
            return (Criteria) this;
        }

        public Criteria andSalesIdEqualTo(Long value) {
            addCriterion("sales_id =", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotEqualTo(Long value) {
            addCriterion("sales_id <>", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdGreaterThan(Long value) {
            addCriterion("sales_id >", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sales_id >=", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdLessThan(Long value) {
            addCriterion("sales_id <", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdLessThanOrEqualTo(Long value) {
            addCriterion("sales_id <=", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdIn(List<Long> values) {
            addCriterion("sales_id in", values, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotIn(List<Long> values) {
            addCriterion("sales_id not in", values, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdBetween(Long value1, Long value2) {
            addCriterion("sales_id between", value1, value2, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotBetween(Long value1, Long value2) {
            addCriterion("sales_id not between", value1, value2, "salesId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNull() {
            addCriterion("supervisor_id is null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNotNull() {
            addCriterion("supervisor_id is not null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdEqualTo(Long value) {
            addCriterion("supervisor_id =", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotEqualTo(Long value) {
            addCriterion("supervisor_id <>", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThan(Long value) {
            addCriterion("supervisor_id >", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("supervisor_id >=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThan(Long value) {
            addCriterion("supervisor_id <", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThanOrEqualTo(Long value) {
            addCriterion("supervisor_id <=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIn(List<Long> values) {
            addCriterion("supervisor_id in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotIn(List<Long> values) {
            addCriterion("supervisor_id not in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdBetween(Long value1, Long value2) {
            addCriterion("supervisor_id between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotBetween(Long value1, Long value2) {
            addCriterion("supervisor_id not between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNull() {
            addCriterion("region_name is null");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNotNull() {
            addCriterion("region_name is not null");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualTo(String value) {
            addCriterion("region_name =", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualTo(String value) {
            addCriterion("region_name <>", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThan(String value) {
            addCriterion("region_name >", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("region_name >=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThan(String value) {
            addCriterion("region_name <", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualTo(String value) {
            addCriterion("region_name <=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIn(List<String> values) {
            addCriterion("region_name in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotIn(List<String> values) {
            addCriterion("region_name not in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameBetween(String value1, String value2) {
            addCriterion("region_name between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotBetween(String value1, String value2) {
            addCriterion("region_name not between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthIsNull() {
            addCriterion("volume_last_month is null");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthIsNotNull() {
            addCriterion("volume_last_month is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthEqualTo(Double value) {
            addCriterion("volume_last_month =", value, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthNotEqualTo(Double value) {
            addCriterion("volume_last_month <>", value, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthGreaterThan(Double value) {
            addCriterion("volume_last_month >", value, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthGreaterThanOrEqualTo(Double value) {
            addCriterion("volume_last_month >=", value, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthLessThan(Double value) {
            addCriterion("volume_last_month <", value, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthLessThanOrEqualTo(Double value) {
            addCriterion("volume_last_month <=", value, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthIn(List<Double> values) {
            addCriterion("volume_last_month in", values, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthNotIn(List<Double> values) {
            addCriterion("volume_last_month not in", values, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthBetween(Double value1, Double value2) {
            addCriterion("volume_last_month between", value1, value2, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeLastMonthNotBetween(Double value1, Double value2) {
            addCriterion("volume_last_month not between", value1, value2, "volumeLastMonth");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmIsNull() {
            addCriterion("volume_ytd_tm is null");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmIsNotNull() {
            addCriterion("volume_ytd_tm is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmEqualTo(Double value) {
            addCriterion("volume_ytd_tm =", value, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmNotEqualTo(Double value) {
            addCriterion("volume_ytd_tm <>", value, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmGreaterThan(Double value) {
            addCriterion("volume_ytd_tm >", value, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmGreaterThanOrEqualTo(Double value) {
            addCriterion("volume_ytd_tm >=", value, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmLessThan(Double value) {
            addCriterion("volume_ytd_tm <", value, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmLessThanOrEqualTo(Double value) {
            addCriterion("volume_ytd_tm <=", value, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmIn(List<Double> values) {
            addCriterion("volume_ytd_tm in", values, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmNotIn(List<Double> values) {
            addCriterion("volume_ytd_tm not in", values, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmBetween(Double value1, Double value2) {
            addCriterion("volume_ytd_tm between", value1, value2, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeYtdTmNotBetween(Double value1, Double value2) {
            addCriterion("volume_ytd_tm not between", value1, value2, "volumeYtdTm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmIsNull() {
            addCriterion("volume_forecast_nm is null");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmIsNotNull() {
            addCriterion("volume_forecast_nm is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmEqualTo(Double value) {
            addCriterion("volume_forecast_nm =", value, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmNotEqualTo(Double value) {
            addCriterion("volume_forecast_nm <>", value, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmGreaterThan(Double value) {
            addCriterion("volume_forecast_nm >", value, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmGreaterThanOrEqualTo(Double value) {
            addCriterion("volume_forecast_nm >=", value, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmLessThan(Double value) {
            addCriterion("volume_forecast_nm <", value, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmLessThanOrEqualTo(Double value) {
            addCriterion("volume_forecast_nm <=", value, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmIn(List<Double> values) {
            addCriterion("volume_forecast_nm in", values, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmNotIn(List<Double> values) {
            addCriterion("volume_forecast_nm not in", values, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmBetween(Double value1, Double value2) {
            addCriterion("volume_forecast_nm between", value1, value2, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andVolumeForecastNmNotBetween(Double value1, Double value2) {
            addCriterion("volume_forecast_nm not between", value1, value2, "volumeForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmIsNull() {
            addCriterion("cross_margin_lm is null");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmIsNotNull() {
            addCriterion("cross_margin_lm is not null");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmEqualTo(Double value) {
            addCriterion("cross_margin_lm =", value, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmNotEqualTo(Double value) {
            addCriterion("cross_margin_lm <>", value, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmGreaterThan(Double value) {
            addCriterion("cross_margin_lm >", value, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmGreaterThanOrEqualTo(Double value) {
            addCriterion("cross_margin_lm >=", value, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmLessThan(Double value) {
            addCriterion("cross_margin_lm <", value, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmLessThanOrEqualTo(Double value) {
            addCriterion("cross_margin_lm <=", value, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmIn(List<Double> values) {
            addCriterion("cross_margin_lm in", values, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmNotIn(List<Double> values) {
            addCriterion("cross_margin_lm not in", values, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmBetween(Double value1, Double value2) {
            addCriterion("cross_margin_lm between", value1, value2, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginLmNotBetween(Double value1, Double value2) {
            addCriterion("cross_margin_lm not between", value1, value2, "crossMarginLm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmIsNull() {
            addCriterion("cross_margin_ytd_tm is null");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmIsNotNull() {
            addCriterion("cross_margin_ytd_tm is not null");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmEqualTo(Double value) {
            addCriterion("cross_margin_ytd_tm =", value, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmNotEqualTo(Double value) {
            addCriterion("cross_margin_ytd_tm <>", value, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmGreaterThan(Double value) {
            addCriterion("cross_margin_ytd_tm >", value, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmGreaterThanOrEqualTo(Double value) {
            addCriterion("cross_margin_ytd_tm >=", value, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmLessThan(Double value) {
            addCriterion("cross_margin_ytd_tm <", value, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmLessThanOrEqualTo(Double value) {
            addCriterion("cross_margin_ytd_tm <=", value, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmIn(List<Double> values) {
            addCriterion("cross_margin_ytd_tm in", values, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmNotIn(List<Double> values) {
            addCriterion("cross_margin_ytd_tm not in", values, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmBetween(Double value1, Double value2) {
            addCriterion("cross_margin_ytd_tm between", value1, value2, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginYtdTmNotBetween(Double value1, Double value2) {
            addCriterion("cross_margin_ytd_tm not between", value1, value2, "crossMarginYtdTm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmIsNull() {
            addCriterion("cross_margin_forecast_nm is null");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmIsNotNull() {
            addCriterion("cross_margin_forecast_nm is not null");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmEqualTo(Double value) {
            addCriterion("cross_margin_forecast_nm =", value, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmNotEqualTo(Double value) {
            addCriterion("cross_margin_forecast_nm <>", value, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmGreaterThan(Double value) {
            addCriterion("cross_margin_forecast_nm >", value, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmGreaterThanOrEqualTo(Double value) {
            addCriterion("cross_margin_forecast_nm >=", value, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmLessThan(Double value) {
            addCriterion("cross_margin_forecast_nm <", value, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmLessThanOrEqualTo(Double value) {
            addCriterion("cross_margin_forecast_nm <=", value, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmIn(List<Double> values) {
            addCriterion("cross_margin_forecast_nm in", values, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmNotIn(List<Double> values) {
            addCriterion("cross_margin_forecast_nm not in", values, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmBetween(Double value1, Double value2) {
            addCriterion("cross_margin_forecast_nm between", value1, value2, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andCrossMarginForecastNmNotBetween(Double value1, Double value2) {
            addCriterion("cross_margin_forecast_nm not between", value1, value2, "crossMarginForecastNm");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysIsNull() {
            addCriterion("office_work_days is null");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysIsNotNull() {
            addCriterion("office_work_days is not null");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysEqualTo(Double value) {
            addCriterion("office_work_days =", value, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysNotEqualTo(Double value) {
            addCriterion("office_work_days <>", value, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysGreaterThan(Double value) {
            addCriterion("office_work_days >", value, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysGreaterThanOrEqualTo(Double value) {
            addCriterion("office_work_days >=", value, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysLessThan(Double value) {
            addCriterion("office_work_days <", value, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysLessThanOrEqualTo(Double value) {
            addCriterion("office_work_days <=", value, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysIn(List<Double> values) {
            addCriterion("office_work_days in", values, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysNotIn(List<Double> values) {
            addCriterion("office_work_days not in", values, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysBetween(Double value1, Double value2) {
            addCriterion("office_work_days between", value1, value2, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andOfficeWorkDaysNotBetween(Double value1, Double value2) {
            addCriterion("office_work_days not between", value1, value2, "officeWorkDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysIsNull() {
            addCriterion("account_visit_days is null");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysIsNotNull() {
            addCriterion("account_visit_days is not null");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysEqualTo(Double value) {
            addCriterion("account_visit_days =", value, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysNotEqualTo(Double value) {
            addCriterion("account_visit_days <>", value, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysGreaterThan(Double value) {
            addCriterion("account_visit_days >", value, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysGreaterThanOrEqualTo(Double value) {
            addCriterion("account_visit_days >=", value, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysLessThan(Double value) {
            addCriterion("account_visit_days <", value, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysLessThanOrEqualTo(Double value) {
            addCriterion("account_visit_days <=", value, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysIn(List<Double> values) {
            addCriterion("account_visit_days in", values, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysNotIn(List<Double> values) {
            addCriterion("account_visit_days not in", values, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysBetween(Double value1, Double value2) {
            addCriterion("account_visit_days between", value1, value2, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andAccountVisitDaysNotBetween(Double value1, Double value2) {
            addCriterion("account_visit_days not between", value1, value2, "accountVisitDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysIsNull() {
            addCriterion("training_meeting_days is null");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysIsNotNull() {
            addCriterion("training_meeting_days is not null");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysEqualTo(Double value) {
            addCriterion("training_meeting_days =", value, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysNotEqualTo(Double value) {
            addCriterion("training_meeting_days <>", value, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysGreaterThan(Double value) {
            addCriterion("training_meeting_days >", value, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysGreaterThanOrEqualTo(Double value) {
            addCriterion("training_meeting_days >=", value, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysLessThan(Double value) {
            addCriterion("training_meeting_days <", value, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysLessThanOrEqualTo(Double value) {
            addCriterion("training_meeting_days <=", value, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysIn(List<Double> values) {
            addCriterion("training_meeting_days in", values, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysNotIn(List<Double> values) {
            addCriterion("training_meeting_days not in", values, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysBetween(Double value1, Double value2) {
            addCriterion("training_meeting_days between", value1, value2, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andTrainingMeetingDaysNotBetween(Double value1, Double value2) {
            addCriterion("training_meeting_days not between", value1, value2, "trainingMeetingDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysIsNull() {
            addCriterion("others_days is null");
            return (Criteria) this;
        }

        public Criteria andOthersDaysIsNotNull() {
            addCriterion("others_days is not null");
            return (Criteria) this;
        }

        public Criteria andOthersDaysEqualTo(Double value) {
            addCriterion("others_days =", value, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysNotEqualTo(Double value) {
            addCriterion("others_days <>", value, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysGreaterThan(Double value) {
            addCriterion("others_days >", value, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysGreaterThanOrEqualTo(Double value) {
            addCriterion("others_days >=", value, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysLessThan(Double value) {
            addCriterion("others_days <", value, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysLessThanOrEqualTo(Double value) {
            addCriterion("others_days <=", value, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysIn(List<Double> values) {
            addCriterion("others_days in", values, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysNotIn(List<Double> values) {
            addCriterion("others_days not in", values, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysBetween(Double value1, Double value2) {
            addCriterion("others_days between", value1, value2, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersDaysNotBetween(Double value1, Double value2) {
            addCriterion("others_days not between", value1, value2, "othersDays");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsIsNull() {
            addCriterion("others_comments is null");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsIsNotNull() {
            addCriterion("others_comments is not null");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsEqualTo(String value) {
            addCriterion("others_comments =", value, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsNotEqualTo(String value) {
            addCriterion("others_comments <>", value, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsGreaterThan(String value) {
            addCriterion("others_comments >", value, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("others_comments >=", value, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsLessThan(String value) {
            addCriterion("others_comments <", value, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsLessThanOrEqualTo(String value) {
            addCriterion("others_comments <=", value, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsIn(List<String> values) {
            addCriterion("others_comments in", values, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsNotIn(List<String> values) {
            addCriterion("others_comments not in", values, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsBetween(String value1, String value2) {
            addCriterion("others_comments between", value1, value2, "othersComments");
            return (Criteria) this;
        }

        public Criteria andOthersCommentsNotBetween(String value1, String value2) {
            addCriterion("others_comments not between", value1, value2, "othersComments");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonIsNull() {
            addCriterion("activities_w1_mon is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonIsNotNull() {
            addCriterion("activities_w1_mon is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonEqualTo(String value) {
            addCriterion("activities_w1_mon =", value, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonNotEqualTo(String value) {
            addCriterion("activities_w1_mon <>", value, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonGreaterThan(String value) {
            addCriterion("activities_w1_mon >", value, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w1_mon >=", value, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonLessThan(String value) {
            addCriterion("activities_w1_mon <", value, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonLessThanOrEqualTo(String value) {
            addCriterion("activities_w1_mon <=", value, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonIn(List<String> values) {
            addCriterion("activities_w1_mon in", values, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonNotIn(List<String> values) {
            addCriterion("activities_w1_mon not in", values, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonBetween(String value1, String value2) {
            addCriterion("activities_w1_mon between", value1, value2, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1MonNotBetween(String value1, String value2) {
            addCriterion("activities_w1_mon not between", value1, value2, "activitiesW1Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueIsNull() {
            addCriterion("activities_w1_tue is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueIsNotNull() {
            addCriterion("activities_w1_tue is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueEqualTo(String value) {
            addCriterion("activities_w1_tue =", value, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueNotEqualTo(String value) {
            addCriterion("activities_w1_tue <>", value, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueGreaterThan(String value) {
            addCriterion("activities_w1_tue >", value, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w1_tue >=", value, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueLessThan(String value) {
            addCriterion("activities_w1_tue <", value, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueLessThanOrEqualTo(String value) {
            addCriterion("activities_w1_tue <=", value, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueIn(List<String> values) {
            addCriterion("activities_w1_tue in", values, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueNotIn(List<String> values) {
            addCriterion("activities_w1_tue not in", values, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueBetween(String value1, String value2) {
            addCriterion("activities_w1_tue between", value1, value2, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1TueNotBetween(String value1, String value2) {
            addCriterion("activities_w1_tue not between", value1, value2, "activitiesW1Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedIsNull() {
            addCriterion("activities_w1_wed is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedIsNotNull() {
            addCriterion("activities_w1_wed is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedEqualTo(String value) {
            addCriterion("activities_w1_wed =", value, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedNotEqualTo(String value) {
            addCriterion("activities_w1_wed <>", value, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedGreaterThan(String value) {
            addCriterion("activities_w1_wed >", value, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w1_wed >=", value, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedLessThan(String value) {
            addCriterion("activities_w1_wed <", value, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedLessThanOrEqualTo(String value) {
            addCriterion("activities_w1_wed <=", value, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedIn(List<String> values) {
            addCriterion("activities_w1_wed in", values, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedNotIn(List<String> values) {
            addCriterion("activities_w1_wed not in", values, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedBetween(String value1, String value2) {
            addCriterion("activities_w1_wed between", value1, value2, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WedNotBetween(String value1, String value2) {
            addCriterion("activities_w1_wed not between", value1, value2, "activitiesW1Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuIsNull() {
            addCriterion("activities_w1_thu is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuIsNotNull() {
            addCriterion("activities_w1_thu is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuEqualTo(String value) {
            addCriterion("activities_w1_thu =", value, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuNotEqualTo(String value) {
            addCriterion("activities_w1_thu <>", value, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuGreaterThan(String value) {
            addCriterion("activities_w1_thu >", value, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w1_thu >=", value, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuLessThan(String value) {
            addCriterion("activities_w1_thu <", value, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuLessThanOrEqualTo(String value) {
            addCriterion("activities_w1_thu <=", value, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuIn(List<String> values) {
            addCriterion("activities_w1_thu in", values, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuNotIn(List<String> values) {
            addCriterion("activities_w1_thu not in", values, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuBetween(String value1, String value2) {
            addCriterion("activities_w1_thu between", value1, value2, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1ThuNotBetween(String value1, String value2) {
            addCriterion("activities_w1_thu not between", value1, value2, "activitiesW1Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriIsNull() {
            addCriterion("activities_w1_fri is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriIsNotNull() {
            addCriterion("activities_w1_fri is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriEqualTo(String value) {
            addCriterion("activities_w1_fri =", value, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriNotEqualTo(String value) {
            addCriterion("activities_w1_fri <>", value, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriGreaterThan(String value) {
            addCriterion("activities_w1_fri >", value, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w1_fri >=", value, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriLessThan(String value) {
            addCriterion("activities_w1_fri <", value, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriLessThanOrEqualTo(String value) {
            addCriterion("activities_w1_fri <=", value, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriIn(List<String> values) {
            addCriterion("activities_w1_fri in", values, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriNotIn(List<String> values) {
            addCriterion("activities_w1_fri not in", values, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriBetween(String value1, String value2) {
            addCriterion("activities_w1_fri between", value1, value2, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1FriNotBetween(String value1, String value2) {
            addCriterion("activities_w1_fri not between", value1, value2, "activitiesW1Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendIsNull() {
            addCriterion("activities_w1_weekend is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendIsNotNull() {
            addCriterion("activities_w1_weekend is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendEqualTo(String value) {
            addCriterion("activities_w1_weekend =", value, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendNotEqualTo(String value) {
            addCriterion("activities_w1_weekend <>", value, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendGreaterThan(String value) {
            addCriterion("activities_w1_weekend >", value, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w1_weekend >=", value, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendLessThan(String value) {
            addCriterion("activities_w1_weekend <", value, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendLessThanOrEqualTo(String value) {
            addCriterion("activities_w1_weekend <=", value, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendIn(List<String> values) {
            addCriterion("activities_w1_weekend in", values, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendNotIn(List<String> values) {
            addCriterion("activities_w1_weekend not in", values, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendBetween(String value1, String value2) {
            addCriterion("activities_w1_weekend between", value1, value2, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW1WeekendNotBetween(String value1, String value2) {
            addCriterion("activities_w1_weekend not between", value1, value2, "activitiesW1Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonIsNull() {
            addCriterion("activities_w2_mon is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonIsNotNull() {
            addCriterion("activities_w2_mon is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonEqualTo(String value) {
            addCriterion("activities_w2_mon =", value, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonNotEqualTo(String value) {
            addCriterion("activities_w2_mon <>", value, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonGreaterThan(String value) {
            addCriterion("activities_w2_mon >", value, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w2_mon >=", value, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonLessThan(String value) {
            addCriterion("activities_w2_mon <", value, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonLessThanOrEqualTo(String value) {
            addCriterion("activities_w2_mon <=", value, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonIn(List<String> values) {
            addCriterion("activities_w2_mon in", values, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonNotIn(List<String> values) {
            addCriterion("activities_w2_mon not in", values, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonBetween(String value1, String value2) {
            addCriterion("activities_w2_mon between", value1, value2, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2MonNotBetween(String value1, String value2) {
            addCriterion("activities_w2_mon not between", value1, value2, "activitiesW2Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueIsNull() {
            addCriterion("activities_w2_tue is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueIsNotNull() {
            addCriterion("activities_w2_tue is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueEqualTo(String value) {
            addCriterion("activities_w2_tue =", value, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueNotEqualTo(String value) {
            addCriterion("activities_w2_tue <>", value, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueGreaterThan(String value) {
            addCriterion("activities_w2_tue >", value, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w2_tue >=", value, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueLessThan(String value) {
            addCriterion("activities_w2_tue <", value, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueLessThanOrEqualTo(String value) {
            addCriterion("activities_w2_tue <=", value, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueIn(List<String> values) {
            addCriterion("activities_w2_tue in", values, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueNotIn(List<String> values) {
            addCriterion("activities_w2_tue not in", values, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueBetween(String value1, String value2) {
            addCriterion("activities_w2_tue between", value1, value2, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2TueNotBetween(String value1, String value2) {
            addCriterion("activities_w2_tue not between", value1, value2, "activitiesW2Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedIsNull() {
            addCriterion("activities_w2_wed is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedIsNotNull() {
            addCriterion("activities_w2_wed is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedEqualTo(String value) {
            addCriterion("activities_w2_wed =", value, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedNotEqualTo(String value) {
            addCriterion("activities_w2_wed <>", value, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedGreaterThan(String value) {
            addCriterion("activities_w2_wed >", value, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w2_wed >=", value, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedLessThan(String value) {
            addCriterion("activities_w2_wed <", value, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedLessThanOrEqualTo(String value) {
            addCriterion("activities_w2_wed <=", value, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedIn(List<String> values) {
            addCriterion("activities_w2_wed in", values, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedNotIn(List<String> values) {
            addCriterion("activities_w2_wed not in", values, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedBetween(String value1, String value2) {
            addCriterion("activities_w2_wed between", value1, value2, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WedNotBetween(String value1, String value2) {
            addCriterion("activities_w2_wed not between", value1, value2, "activitiesW2Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuIsNull() {
            addCriterion("activities_w2_thu is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuIsNotNull() {
            addCriterion("activities_w2_thu is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuEqualTo(String value) {
            addCriterion("activities_w2_thu =", value, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuNotEqualTo(String value) {
            addCriterion("activities_w2_thu <>", value, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuGreaterThan(String value) {
            addCriterion("activities_w2_thu >", value, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w2_thu >=", value, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuLessThan(String value) {
            addCriterion("activities_w2_thu <", value, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuLessThanOrEqualTo(String value) {
            addCriterion("activities_w2_thu <=", value, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuIn(List<String> values) {
            addCriterion("activities_w2_thu in", values, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuNotIn(List<String> values) {
            addCriterion("activities_w2_thu not in", values, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuBetween(String value1, String value2) {
            addCriterion("activities_w2_thu between", value1, value2, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2ThuNotBetween(String value1, String value2) {
            addCriterion("activities_w2_thu not between", value1, value2, "activitiesW2Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriIsNull() {
            addCriterion("activities_w2_fri is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriIsNotNull() {
            addCriterion("activities_w2_fri is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriEqualTo(String value) {
            addCriterion("activities_w2_fri =", value, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriNotEqualTo(String value) {
            addCriterion("activities_w2_fri <>", value, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriGreaterThan(String value) {
            addCriterion("activities_w2_fri >", value, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w2_fri >=", value, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriLessThan(String value) {
            addCriterion("activities_w2_fri <", value, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriLessThanOrEqualTo(String value) {
            addCriterion("activities_w2_fri <=", value, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriIn(List<String> values) {
            addCriterion("activities_w2_fri in", values, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriNotIn(List<String> values) {
            addCriterion("activities_w2_fri not in", values, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriBetween(String value1, String value2) {
            addCriterion("activities_w2_fri between", value1, value2, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2FriNotBetween(String value1, String value2) {
            addCriterion("activities_w2_fri not between", value1, value2, "activitiesW2Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendIsNull() {
            addCriterion("activities_w2_weekend is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendIsNotNull() {
            addCriterion("activities_w2_weekend is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendEqualTo(String value) {
            addCriterion("activities_w2_weekend =", value, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendNotEqualTo(String value) {
            addCriterion("activities_w2_weekend <>", value, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendGreaterThan(String value) {
            addCriterion("activities_w2_weekend >", value, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w2_weekend >=", value, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendLessThan(String value) {
            addCriterion("activities_w2_weekend <", value, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendLessThanOrEqualTo(String value) {
            addCriterion("activities_w2_weekend <=", value, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendIn(List<String> values) {
            addCriterion("activities_w2_weekend in", values, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendNotIn(List<String> values) {
            addCriterion("activities_w2_weekend not in", values, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendBetween(String value1, String value2) {
            addCriterion("activities_w2_weekend between", value1, value2, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW2WeekendNotBetween(String value1, String value2) {
            addCriterion("activities_w2_weekend not between", value1, value2, "activitiesW2Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonIsNull() {
            addCriterion("activities_w3_mon is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonIsNotNull() {
            addCriterion("activities_w3_mon is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonEqualTo(String value) {
            addCriterion("activities_w3_mon =", value, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonNotEqualTo(String value) {
            addCriterion("activities_w3_mon <>", value, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonGreaterThan(String value) {
            addCriterion("activities_w3_mon >", value, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w3_mon >=", value, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonLessThan(String value) {
            addCriterion("activities_w3_mon <", value, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonLessThanOrEqualTo(String value) {
            addCriterion("activities_w3_mon <=", value, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonIn(List<String> values) {
            addCriterion("activities_w3_mon in", values, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonNotIn(List<String> values) {
            addCriterion("activities_w3_mon not in", values, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonBetween(String value1, String value2) {
            addCriterion("activities_w3_mon between", value1, value2, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3MonNotBetween(String value1, String value2) {
            addCriterion("activities_w3_mon not between", value1, value2, "activitiesW3Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueIsNull() {
            addCriterion("activities_w3_tue is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueIsNotNull() {
            addCriterion("activities_w3_tue is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueEqualTo(String value) {
            addCriterion("activities_w3_tue =", value, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueNotEqualTo(String value) {
            addCriterion("activities_w3_tue <>", value, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueGreaterThan(String value) {
            addCriterion("activities_w3_tue >", value, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w3_tue >=", value, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueLessThan(String value) {
            addCriterion("activities_w3_tue <", value, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueLessThanOrEqualTo(String value) {
            addCriterion("activities_w3_tue <=", value, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueIn(List<String> values) {
            addCriterion("activities_w3_tue in", values, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueNotIn(List<String> values) {
            addCriterion("activities_w3_tue not in", values, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueBetween(String value1, String value2) {
            addCriterion("activities_w3_tue between", value1, value2, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3TueNotBetween(String value1, String value2) {
            addCriterion("activities_w3_tue not between", value1, value2, "activitiesW3Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedIsNull() {
            addCriterion("activities_w3_wed is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedIsNotNull() {
            addCriterion("activities_w3_wed is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedEqualTo(String value) {
            addCriterion("activities_w3_wed =", value, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedNotEqualTo(String value) {
            addCriterion("activities_w3_wed <>", value, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedGreaterThan(String value) {
            addCriterion("activities_w3_wed >", value, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w3_wed >=", value, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedLessThan(String value) {
            addCriterion("activities_w3_wed <", value, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedLessThanOrEqualTo(String value) {
            addCriterion("activities_w3_wed <=", value, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedIn(List<String> values) {
            addCriterion("activities_w3_wed in", values, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedNotIn(List<String> values) {
            addCriterion("activities_w3_wed not in", values, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedBetween(String value1, String value2) {
            addCriterion("activities_w3_wed between", value1, value2, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WedNotBetween(String value1, String value2) {
            addCriterion("activities_w3_wed not between", value1, value2, "activitiesW3Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuIsNull() {
            addCriterion("activities_w3_thu is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuIsNotNull() {
            addCriterion("activities_w3_thu is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuEqualTo(String value) {
            addCriterion("activities_w3_thu =", value, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuNotEqualTo(String value) {
            addCriterion("activities_w3_thu <>", value, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuGreaterThan(String value) {
            addCriterion("activities_w3_thu >", value, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w3_thu >=", value, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuLessThan(String value) {
            addCriterion("activities_w3_thu <", value, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuLessThanOrEqualTo(String value) {
            addCriterion("activities_w3_thu <=", value, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuIn(List<String> values) {
            addCriterion("activities_w3_thu in", values, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuNotIn(List<String> values) {
            addCriterion("activities_w3_thu not in", values, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuBetween(String value1, String value2) {
            addCriterion("activities_w3_thu between", value1, value2, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3ThuNotBetween(String value1, String value2) {
            addCriterion("activities_w3_thu not between", value1, value2, "activitiesW3Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriIsNull() {
            addCriterion("activities_w3_fri is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriIsNotNull() {
            addCriterion("activities_w3_fri is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriEqualTo(String value) {
            addCriterion("activities_w3_fri =", value, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriNotEqualTo(String value) {
            addCriterion("activities_w3_fri <>", value, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriGreaterThan(String value) {
            addCriterion("activities_w3_fri >", value, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w3_fri >=", value, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriLessThan(String value) {
            addCriterion("activities_w3_fri <", value, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriLessThanOrEqualTo(String value) {
            addCriterion("activities_w3_fri <=", value, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriIn(List<String> values) {
            addCriterion("activities_w3_fri in", values, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriNotIn(List<String> values) {
            addCriterion("activities_w3_fri not in", values, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriBetween(String value1, String value2) {
            addCriterion("activities_w3_fri between", value1, value2, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3FriNotBetween(String value1, String value2) {
            addCriterion("activities_w3_fri not between", value1, value2, "activitiesW3Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendIsNull() {
            addCriterion("activities_w3_weekend is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendIsNotNull() {
            addCriterion("activities_w3_weekend is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendEqualTo(String value) {
            addCriterion("activities_w3_weekend =", value, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendNotEqualTo(String value) {
            addCriterion("activities_w3_weekend <>", value, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendGreaterThan(String value) {
            addCriterion("activities_w3_weekend >", value, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w3_weekend >=", value, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendLessThan(String value) {
            addCriterion("activities_w3_weekend <", value, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendLessThanOrEqualTo(String value) {
            addCriterion("activities_w3_weekend <=", value, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendIn(List<String> values) {
            addCriterion("activities_w3_weekend in", values, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendNotIn(List<String> values) {
            addCriterion("activities_w3_weekend not in", values, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendBetween(String value1, String value2) {
            addCriterion("activities_w3_weekend between", value1, value2, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW3WeekendNotBetween(String value1, String value2) {
            addCriterion("activities_w3_weekend not between", value1, value2, "activitiesW3Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonIsNull() {
            addCriterion("activities_w4_mon is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonIsNotNull() {
            addCriterion("activities_w4_mon is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonEqualTo(String value) {
            addCriterion("activities_w4_mon =", value, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonNotEqualTo(String value) {
            addCriterion("activities_w4_mon <>", value, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonGreaterThan(String value) {
            addCriterion("activities_w4_mon >", value, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w4_mon >=", value, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonLessThan(String value) {
            addCriterion("activities_w4_mon <", value, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonLessThanOrEqualTo(String value) {
            addCriterion("activities_w4_mon <=", value, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonIn(List<String> values) {
            addCriterion("activities_w4_mon in", values, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonNotIn(List<String> values) {
            addCriterion("activities_w4_mon not in", values, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonBetween(String value1, String value2) {
            addCriterion("activities_w4_mon between", value1, value2, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4MonNotBetween(String value1, String value2) {
            addCriterion("activities_w4_mon not between", value1, value2, "activitiesW4Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueIsNull() {
            addCriterion("activities_w4_tue is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueIsNotNull() {
            addCriterion("activities_w4_tue is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueEqualTo(String value) {
            addCriterion("activities_w4_tue =", value, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueNotEqualTo(String value) {
            addCriterion("activities_w4_tue <>", value, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueGreaterThan(String value) {
            addCriterion("activities_w4_tue >", value, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w4_tue >=", value, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueLessThan(String value) {
            addCriterion("activities_w4_tue <", value, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueLessThanOrEqualTo(String value) {
            addCriterion("activities_w4_tue <=", value, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueIn(List<String> values) {
            addCriterion("activities_w4_tue in", values, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueNotIn(List<String> values) {
            addCriterion("activities_w4_tue not in", values, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueBetween(String value1, String value2) {
            addCriterion("activities_w4_tue between", value1, value2, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4TueNotBetween(String value1, String value2) {
            addCriterion("activities_w4_tue not between", value1, value2, "activitiesW4Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedIsNull() {
            addCriterion("activities_w4_wed is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedIsNotNull() {
            addCriterion("activities_w4_wed is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedEqualTo(String value) {
            addCriterion("activities_w4_wed =", value, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedNotEqualTo(String value) {
            addCriterion("activities_w4_wed <>", value, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedGreaterThan(String value) {
            addCriterion("activities_w4_wed >", value, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w4_wed >=", value, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedLessThan(String value) {
            addCriterion("activities_w4_wed <", value, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedLessThanOrEqualTo(String value) {
            addCriterion("activities_w4_wed <=", value, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedIn(List<String> values) {
            addCriterion("activities_w4_wed in", values, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedNotIn(List<String> values) {
            addCriterion("activities_w4_wed not in", values, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedBetween(String value1, String value2) {
            addCriterion("activities_w4_wed between", value1, value2, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WedNotBetween(String value1, String value2) {
            addCriterion("activities_w4_wed not between", value1, value2, "activitiesW4Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuIsNull() {
            addCriterion("activities_w4_thu is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuIsNotNull() {
            addCriterion("activities_w4_thu is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuEqualTo(String value) {
            addCriterion("activities_w4_thu =", value, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuNotEqualTo(String value) {
            addCriterion("activities_w4_thu <>", value, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuGreaterThan(String value) {
            addCriterion("activities_w4_thu >", value, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w4_thu >=", value, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuLessThan(String value) {
            addCriterion("activities_w4_thu <", value, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuLessThanOrEqualTo(String value) {
            addCriterion("activities_w4_thu <=", value, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuIn(List<String> values) {
            addCriterion("activities_w4_thu in", values, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuNotIn(List<String> values) {
            addCriterion("activities_w4_thu not in", values, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuBetween(String value1, String value2) {
            addCriterion("activities_w4_thu between", value1, value2, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4ThuNotBetween(String value1, String value2) {
            addCriterion("activities_w4_thu not between", value1, value2, "activitiesW4Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriIsNull() {
            addCriterion("activities_w4_fri is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriIsNotNull() {
            addCriterion("activities_w4_fri is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriEqualTo(String value) {
            addCriterion("activities_w4_fri =", value, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriNotEqualTo(String value) {
            addCriterion("activities_w4_fri <>", value, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriGreaterThan(String value) {
            addCriterion("activities_w4_fri >", value, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w4_fri >=", value, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriLessThan(String value) {
            addCriterion("activities_w4_fri <", value, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriLessThanOrEqualTo(String value) {
            addCriterion("activities_w4_fri <=", value, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriIn(List<String> values) {
            addCriterion("activities_w4_fri in", values, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriNotIn(List<String> values) {
            addCriterion("activities_w4_fri not in", values, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriBetween(String value1, String value2) {
            addCriterion("activities_w4_fri between", value1, value2, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4FriNotBetween(String value1, String value2) {
            addCriterion("activities_w4_fri not between", value1, value2, "activitiesW4Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendIsNull() {
            addCriterion("activities_w4_weekend is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendIsNotNull() {
            addCriterion("activities_w4_weekend is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendEqualTo(String value) {
            addCriterion("activities_w4_weekend =", value, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendNotEqualTo(String value) {
            addCriterion("activities_w4_weekend <>", value, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendGreaterThan(String value) {
            addCriterion("activities_w4_weekend >", value, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w4_weekend >=", value, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendLessThan(String value) {
            addCriterion("activities_w4_weekend <", value, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendLessThanOrEqualTo(String value) {
            addCriterion("activities_w4_weekend <=", value, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendIn(List<String> values) {
            addCriterion("activities_w4_weekend in", values, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendNotIn(List<String> values) {
            addCriterion("activities_w4_weekend not in", values, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendBetween(String value1, String value2) {
            addCriterion("activities_w4_weekend between", value1, value2, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW4WeekendNotBetween(String value1, String value2) {
            addCriterion("activities_w4_weekend not between", value1, value2, "activitiesW4Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonIsNull() {
            addCriterion("activities_w5_mon is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonIsNotNull() {
            addCriterion("activities_w5_mon is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonEqualTo(String value) {
            addCriterion("activities_w5_mon =", value, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonNotEqualTo(String value) {
            addCriterion("activities_w5_mon <>", value, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonGreaterThan(String value) {
            addCriterion("activities_w5_mon >", value, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w5_mon >=", value, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonLessThan(String value) {
            addCriterion("activities_w5_mon <", value, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonLessThanOrEqualTo(String value) {
            addCriterion("activities_w5_mon <=", value, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonIn(List<String> values) {
            addCriterion("activities_w5_mon in", values, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonNotIn(List<String> values) {
            addCriterion("activities_w5_mon not in", values, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonBetween(String value1, String value2) {
            addCriterion("activities_w5_mon between", value1, value2, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5MonNotBetween(String value1, String value2) {
            addCriterion("activities_w5_mon not between", value1, value2, "activitiesW5Mon");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueIsNull() {
            addCriterion("activities_w5_tue is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueIsNotNull() {
            addCriterion("activities_w5_tue is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueEqualTo(String value) {
            addCriterion("activities_w5_tue =", value, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueNotEqualTo(String value) {
            addCriterion("activities_w5_tue <>", value, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueGreaterThan(String value) {
            addCriterion("activities_w5_tue >", value, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w5_tue >=", value, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueLessThan(String value) {
            addCriterion("activities_w5_tue <", value, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueLessThanOrEqualTo(String value) {
            addCriterion("activities_w5_tue <=", value, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueIn(List<String> values) {
            addCriterion("activities_w5_tue in", values, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueNotIn(List<String> values) {
            addCriterion("activities_w5_tue not in", values, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueBetween(String value1, String value2) {
            addCriterion("activities_w5_tue between", value1, value2, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5TueNotBetween(String value1, String value2) {
            addCriterion("activities_w5_tue not between", value1, value2, "activitiesW5Tue");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedIsNull() {
            addCriterion("activities_w5_wed is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedIsNotNull() {
            addCriterion("activities_w5_wed is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedEqualTo(String value) {
            addCriterion("activities_w5_wed =", value, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedNotEqualTo(String value) {
            addCriterion("activities_w5_wed <>", value, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedGreaterThan(String value) {
            addCriterion("activities_w5_wed >", value, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w5_wed >=", value, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedLessThan(String value) {
            addCriterion("activities_w5_wed <", value, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedLessThanOrEqualTo(String value) {
            addCriterion("activities_w5_wed <=", value, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedIn(List<String> values) {
            addCriterion("activities_w5_wed in", values, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedNotIn(List<String> values) {
            addCriterion("activities_w5_wed not in", values, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedBetween(String value1, String value2) {
            addCriterion("activities_w5_wed between", value1, value2, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WedNotBetween(String value1, String value2) {
            addCriterion("activities_w5_wed not between", value1, value2, "activitiesW5Wed");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuIsNull() {
            addCriterion("activities_w5_thu is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuIsNotNull() {
            addCriterion("activities_w5_thu is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuEqualTo(String value) {
            addCriterion("activities_w5_thu =", value, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuNotEqualTo(String value) {
            addCriterion("activities_w5_thu <>", value, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuGreaterThan(String value) {
            addCriterion("activities_w5_thu >", value, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w5_thu >=", value, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuLessThan(String value) {
            addCriterion("activities_w5_thu <", value, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuLessThanOrEqualTo(String value) {
            addCriterion("activities_w5_thu <=", value, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuIn(List<String> values) {
            addCriterion("activities_w5_thu in", values, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuNotIn(List<String> values) {
            addCriterion("activities_w5_thu not in", values, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuBetween(String value1, String value2) {
            addCriterion("activities_w5_thu between", value1, value2, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5ThuNotBetween(String value1, String value2) {
            addCriterion("activities_w5_thu not between", value1, value2, "activitiesW5Thu");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriIsNull() {
            addCriterion("activities_w5_fri is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriIsNotNull() {
            addCriterion("activities_w5_fri is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriEqualTo(String value) {
            addCriterion("activities_w5_fri =", value, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriNotEqualTo(String value) {
            addCriterion("activities_w5_fri <>", value, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriGreaterThan(String value) {
            addCriterion("activities_w5_fri >", value, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w5_fri >=", value, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriLessThan(String value) {
            addCriterion("activities_w5_fri <", value, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriLessThanOrEqualTo(String value) {
            addCriterion("activities_w5_fri <=", value, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriIn(List<String> values) {
            addCriterion("activities_w5_fri in", values, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriNotIn(List<String> values) {
            addCriterion("activities_w5_fri not in", values, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriBetween(String value1, String value2) {
            addCriterion("activities_w5_fri between", value1, value2, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5FriNotBetween(String value1, String value2) {
            addCriterion("activities_w5_fri not between", value1, value2, "activitiesW5Fri");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendIsNull() {
            addCriterion("activities_w5_weekend is null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendIsNotNull() {
            addCriterion("activities_w5_weekend is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendEqualTo(String value) {
            addCriterion("activities_w5_weekend =", value, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendNotEqualTo(String value) {
            addCriterion("activities_w5_weekend <>", value, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendGreaterThan(String value) {
            addCriterion("activities_w5_weekend >", value, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendGreaterThanOrEqualTo(String value) {
            addCriterion("activities_w5_weekend >=", value, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendLessThan(String value) {
            addCriterion("activities_w5_weekend <", value, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendLessThanOrEqualTo(String value) {
            addCriterion("activities_w5_weekend <=", value, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendIn(List<String> values) {
            addCriterion("activities_w5_weekend in", values, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendNotIn(List<String> values) {
            addCriterion("activities_w5_weekend not in", values, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendBetween(String value1, String value2) {
            addCriterion("activities_w5_weekend between", value1, value2, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andActivitiesW5WeekendNotBetween(String value1, String value2) {
            addCriterion("activities_w5_weekend not between", value1, value2, "activitiesW5Weekend");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNull() {
            addCriterion("submit_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNotNull() {
            addCriterion("submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeEqualTo(Date value) {
            addCriterion("submit_time =", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotEqualTo(Date value) {
            addCriterion("submit_time <>", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThan(Date value) {
            addCriterion("submit_time >", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("submit_time >=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThan(Date value) {
            addCriterion("submit_time <", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThanOrEqualTo(Date value) {
            addCriterion("submit_time <=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIn(List<Date> values) {
            addCriterion("submit_time in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotIn(List<Date> values) {
            addCriterion("submit_time not in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeBetween(Date value1, Date value2) {
            addCriterion("submit_time between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotBetween(Date value1, Date value2) {
            addCriterion("submit_time not between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNull() {
            addCriterion("review_time is null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNotNull() {
            addCriterion("review_time is not null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeEqualTo(Date value) {
            addCriterion("review_time =", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotEqualTo(Date value) {
            addCriterion("review_time <>", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThan(Date value) {
            addCriterion("review_time >", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("review_time >=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThan(Date value) {
            addCriterion("review_time <", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThanOrEqualTo(Date value) {
            addCriterion("review_time <=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIn(List<Date> values) {
            addCriterion("review_time in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotIn(List<Date> values) {
            addCriterion("review_time not in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeBetween(Date value1, Date value2) {
            addCriterion("review_time between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotBetween(Date value1, Date value2) {
            addCriterion("review_time not between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
