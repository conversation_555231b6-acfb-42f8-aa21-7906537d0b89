SELECT max(o.organization_name) "经销商名称", --u.login_name "登录名", u.ch_name "姓名", 
sum(pde.POINT_VALUE) as "发放积分", 
--sum(case when plog.CREATION_TIME<'2021-05-01' then 0 when bu.BUSINESS_TYPE_CODE='AWARD_DSR_POINT' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
--sum(pde.POINT_VALUE-pde.POINT_PAYED) "总剩余"
CONVERT(varchar(7), pde.TRANS_TIME, 23) "月份",
case when o.status=1 then 'Active' else 'Inactive' end "经销商状态"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        --left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        --LEFT JOIN wx_t_user u on u.user_id = pac.POINT_ACCOUNT_OWNER_ID
		left join wx_t_organization o on o.id=pac.POINT_ACCOUNT_OWNER_ID
        WHERE pde.point_type='CALTEX_POINT' and  bu.BUSINESS_TYPE_CODE='CALTEX_POINT_FROM_BI'
		and pde.TRANS_TIME>='2021-01-01'
		group by o.id,CONVERT(varchar(7), pde.TRANS_TIME, 23),o.status
		order by o.id,CONVERT(varchar(7), pde.TRANS_TIME, 23)
		--having sum(case when plog.CREATION_TIME<'2021-01-01' then 0 when bu.BUSINESS_TYPE_CODE='CDM_STOCK_POINT_FROM_BI' then pde.POINT_VALUE else 0 end)>0	
