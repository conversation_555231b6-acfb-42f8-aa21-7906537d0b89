package com.chevron.b2b.model;

import java.util.Date;

/**
 * 计划实体-wx_t_schedule_entity
 * <AUTHOR>
 * @version 1.0 2019-01-09 22:26
 */
public class ScheduleEntity {

	/**  ID  **/
    private Long id;

	/**  关联计划ID  **/
    private Long scheduleId;

	/**  任务开始日期  **/
    private Date startDate;

	/**  任务结束日期  **/
    private Date endDate;

	/**  创建用户ID  **/
    private Long createUserId;

	/**  创建时间  **/
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
