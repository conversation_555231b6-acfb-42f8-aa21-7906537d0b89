insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate());

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Dsr.kpi', 8192, 1, 'DSR绩效。8192 - 终端客户拜访tab权限', 1, getdate(), NULL, NULL);

