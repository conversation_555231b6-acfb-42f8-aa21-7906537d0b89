select *
--update m set m.message_receivers=m2.message_receivers
 from wx_message m left join wx_message m2 on m2.id=29422 where m.id=29671
 
 update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u join wx_t_organization o on o.id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where o.type=1 and (u.type is null or u.type != '1')
and u.user_id in (129892)
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=24953
 
  update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u 
where u.user_id in (129892)
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=32338

select  sent_time,(select count(1) from wx_push_message pm where pm.message_main_id=m.id ), * from wx_message m --left join wx_push_message pm on pm.message_main_id=m.id 
where m.status=0
order by id desc

select pm.*  into wx_push_message202109031
--delete pm
from wx_message m left join wx_push_message pm on pm.message_main_id=m.id
where m.id in (27671,
27670,27668)
 --order by id desc

update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u 
where (u.type is null or u.type != '1')
and u.status=1 and u.login_name in ('15665608788') FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=29084
