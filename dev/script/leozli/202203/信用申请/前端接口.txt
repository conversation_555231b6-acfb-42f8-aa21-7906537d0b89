1. 列表 POST JSON
/credit/app/list.do?fromPage=todo&fromRequestor=self&limit=20&start=0&field=updateTime&direction=DESC&appToken=afe963fc776e8bcfe244688a483f53903c90fea8
参数：
	fromPage：列表类型。todo/done/all
	fromRequestor: 按申请人分组。self/others
	workflowStatus: 流程状态（数据字典Credit.workflowStatus）

2. 暂存接口 POST JSON
/credit/app/save.do
    @ApiModelProperty("表单数据")
    private F form;
    @ApiModelProperty("提交备注信息")//提交/驳回/撤回 意见
    private String remark;
    @ApiModelProperty("执行人") //预留字段。这次不用管
    private Long executor;
    @ApiModelProperty("流程锁ID") //待办/已办列表进入传的参数
    private String workflowLockerId;
    @ApiModelProperty("版本号") //待办/已办列表进入传的参数
    private Long formVersionNo;
    @ApiModelProperty("驳回指定节点ID(前端指定驳回步骤)") //信用申请不用管
    private String nodeId;
    @ApiModelProperty("是否保存表单") //审批步骤能编辑的审批通过调用时传true
    private boolean saveForm = false;
返回接口：
	data: 更新后的form表单
	formVersionNo：更新后表单版本号。前端需要以此更新版本号
 
3. 申请提交/审批通过
/credit/app/submit.do
参数同2

 
4. 撤回
/credit/app/recall.do
参数同2

 
5. 驳回
/credit/app/reject.do
参数同2

6. 详情接口
/credit/app/detail.do
@ApiParam(name="id：表单ID", required=true) @RequestParam(value="id")Long id, 
@ApiParam(name="lockerId", value="流程锁ID", required=true) @RequestParam(value="lockerId")String lockerId, 
@ApiParam(name="fromPage", value="来源页面（todo/done/all）", required=true) @RequestParam(value="fromPage")String fromPage
返回：
	form: 表单对象
	lockerId：流程锁（流程参数）
	formVersionNo：表单版本（如果是待办和已办列表进入，切列表中的版本号和请求接口的版本号不一致，应提醒用户数据过期，并返回列表从新进入）
	nodeId：当前流程节点ID
	isRequestNode：当前节点是否是申请节点
	rejectable：当前是否可驳回
	recallable：当前是否可撤回
	submitable：是否可提交

7. 流程节点接口 POST FORM
/credit/app/getWorkflowStepInstances.do?processInstanceId=7965af09-13c7-11ed-af9d-0017fa00d799

8. 流程执行历史接口 POST FORM
/credit/app/getWorkflowStepHistory.do?processInstanceId=7965af09-13c7-11ed-af9d-0017fa00d799

9. reassign接口 POST FORM
/credit/app/reassign.do?processInstanceId=5e72109b-1486-11ed-8a86-0017fa00d799&taskId=5e96fd38-1486-11ed-8a86-0017fa00d799&oldExecutorId=122710&newExecutorId=130908

10. 催办提醒
/credit/app/notifyhandle.do?accepters=[流程图待办人UserId]&ccOperator=true&notifyConfigDicType=Credit.notifyHandleConfig&workflowKey=[requestNo]
@ApiParam(name="accepters", value="提醒用户ID集合", required=true)Long[] accepters,
@ApiParam(name="ccOperator", value="是否抄送操作人（登录用户）", required=false)Boolean ccOperator,
@ApiParam(name="workflowKey", value="流程键值", required=true)String workflowKey,
@ApiParam(name="notifyConfigDicType", value="提醒配置字典类型", required=false)String notifyConfigDicType

/credit/app/export.do?fromPage=done&fromRequestor=others&limit=20&start=0&field=updateTime&direction=DESC&appToken=afe963fc776e8bcfe244688a483f53903c90fea8

财务通知销售领导
/credit/app/notifysalesleader.do?id=[表单ID]

/credit/app/getInitForm.do?creditType=ANNUAL_CREDIT_REVIEW

/user/ctrldata.do?paging=false&userRoleName=Trainer
参数：
paging：是否分页。true-分页，false-不分页
limit: 记录条数。默认0。不分页且为0时，查询全部
userRoleName：角色名称

{"jsonrpc":"2.0","method":"dicService.getDicItemByDicTypeCode","params":["Credit.workflowStatus"],"id":2}
{"jsonrpc":"2.0","method":"userService.getLoginUser","params":[],"id":2}