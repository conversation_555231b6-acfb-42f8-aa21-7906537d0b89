package com.sys.auth.dao;

import com.sys.auth.model.WxTOwnerpermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WxTOwnerpermissionMapper {

    List<WxTOwnerpermission> selectPermissionByUserId(@Param("userId") Long userId);

    List<WxTOwnerpermission> selectPermissionByRoleId(@Param("roleId") Long roleId);

    List<WxTOwnerpermission> selectPermissionByOwnerId(@Param("ownerId") Long ownerId);
}
