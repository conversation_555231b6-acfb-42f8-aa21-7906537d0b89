--ALTER TABLE [wx_t_Main_Local_marketing] ALTER COLUMN distributor_id                              bigint                             null


/*update ws set biz_permission_weight=biz_permission_weight|1024
from wx_t_workflow_exe_list el
left join wx_t_workflow_step ws on el.step_id=ws.step_id
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP'
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER'
where ws.workflow_code='NON_LOCK_MKT' and ws.step_code='ABM_CONFIRM'*/

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('LocalMarketing.notifyByNewEmail', '窜货邮件提醒配置', '窜货邮件提醒配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('LocalMarketing.notifyByNewEmail', 'honorific', '邮件称谓', 'Dear <PERSON>,', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('LocalMarketing.notifyByNewEmail', 'message', '消息', '您有一条新的窜货处罚邮件待处理。', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('LocalMarketing.notifyByNewEmail', 'to', '接收人', '<EMAIL>', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('LocalMarketing.notifyByNewEmail', 'to', '接收人', '<EMAIL>', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('LocalMarketing.notifyByNewEmail', 'to', '接收人', '<EMAIL>', '1');
