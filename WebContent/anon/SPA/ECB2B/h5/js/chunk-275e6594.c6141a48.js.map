{"version": 3, "sources": ["webpack:///./src/views/user/Recharge.vue?850a", "webpack:///./src/views/user/Recharge.vue?e05c", "webpack:///src/views/user/Recharge.vue", "webpack:///./src/views/user/Recharge.vue?9293", "webpack:///./src/views/user/Recharge.vue?2bcc"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "_l", "item", "index", "key", "class", "active", "on", "$event", "navRecharges", "_v", "_s", "directives", "name", "rawName", "value", "expression", "attrs", "domProps", "target", "composing", "money", "userInfo", "brokerage_price", "now_money", "recharge", "staticRenderFns", "components", "props", "data", "navRecharge", "payType", "from", "computed", "mounted", "methods", "that", "price", "$dialog", "toast", "confirm", "mes", "title", "opts", "component"], "mappings": "kHAAA,yBAAyf,EAAG,G,oECA5f,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,MAAM,CAACE,YAAY,sCAAsCN,EAAIO,GAAIP,EAAe,aAAE,SAASQ,EAAKC,GAAO,OAAOL,EAAG,MAAM,CAACM,IAAID,EAAMH,YAAY,OAAOK,MAAMX,EAAIY,SAAWH,EAAQ,KAAO,GAAGI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAIe,aAAaN,MAAU,CAACT,EAAIgB,GAAG,IAAIhB,EAAIiB,GAAGT,GAAM,UAAS,GAAGJ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,OAAO,CAACJ,EAAIgB,GAAG,OAAOZ,EAAG,QAAQ,CAACc,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOrB,EAAS,MAAEsB,WAAW,UAAUC,MAAM,CAAC,KAAO,SAAS,YAAc,QAAQC,SAAS,CAAC,MAASxB,EAAS,OAAGa,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOW,OAAOC,YAAqB1B,EAAI2B,MAAMb,EAAOW,OAAOJ,aAAcrB,EAAIY,OAAiHR,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACN,EAAIgB,GAAG,aAAaZ,EAAG,OAAO,CAACJ,EAAIgB,GAAG,IAAIhB,EAAIiB,GAAGjB,EAAI4B,SAASC,iBAAmB,QAAzNzB,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACN,EAAIgB,GAAG,aAAaZ,EAAG,OAAO,CAACJ,EAAIgB,GAAG,IAAIhB,EAAIiB,GAAGjB,EAAI8B,WAAa,QAAgI1B,EAAG,MAAM,CAACE,YAAY,uBAAuBO,GAAG,CAAC,MAAQb,EAAI+B,WAAW,CAAC/B,EAAIgB,GAAG,IAAIhB,EAAIiB,GAAGjB,EAAIY,OAAS,OAAS,QAAQ,cACtoCoB,EAAkB,G,sGCsCtB,GACEb,KAAM,WACNc,WAAY,GACZC,MAAO,GACPC,KAAM,WACJ,MAAO,CACLC,YAAa,CAAC,OAAQ,QACtBxB,OAAQ,EACRyB,QAAS,CAAC,UACVC,KAAM,OAAZ,OAAY,GAAZ,oBACMX,MAAO,GACPG,UAAW,KAGfS,SAAU,OAAZ,OAAY,CAAZ,cACEC,QAAS,WACPvC,KAAK6B,UAAY7B,KAAK2B,SAASE,WAEjCW,QAAS,CACP1B,aAAc,SAAlB,GACMd,KAAKW,OAASH,GAEhBsB,SAAU,WAAd,WACA,OACA,qBACM,GAAIW,EAAK9B,OAAQ,CACf,GAAc,IAAV+B,EACF,OAAOD,EAAKE,QAAQC,MAAM,CAApC,mBACA,SACU,OAAOH,EAAKE,QAAQC,MAAM,CAApC,qBAEQ5C,KAAK2C,QAAQE,QAAQ,CACnBC,IAAK,kBACLC,MAAO,OACPC,KAAM,CAChB,CACY,IAAZ,KACY,OAAZ,EACY,SAAZ,WACc,OAAd,OAAc,CAAd,CAAgB,MAAhB,EAAgB,KAAhB,OAAgB,KAAhB,IACA,kBAUgB,OATA,EAAhB,yBACA,EACA,gCAEgB,EAAhB,wCACA,2BACA,GAEgB,EAAhB,SACA,iBAAkB,IAAlB,WAEA,mBACgB,EAAhB,eAAkB,IAAlB,aAIA,CACY,IAAZ,KACY,OAAZ,EACY,SAAZ,WACc,OAAd,iBAAgB,IAAhB,iBAKA,CACQ,GAAc,IAAVN,EACF,OAAOD,EAAKE,QAAQC,MAAM,CAApC,mBACA,SACU,OAAOH,EAAKE,QAAQC,MAAM,CAApC,qBAEQ,OAAR,OAAQ,CAAR,uBACA,kBACU,IAAV,SACA,oBACY,SAAZ,yBACY,EAAZ,iBACc,IAAd,OACc,KAAd,CACA,CACgB,IAAhB,MACgB,OAAhB,EACgB,SAAhB,WACkB,EAAlB,iBACoB,KAApB,oBAIA,CACgB,IAAhB,OACgB,OAAhB,EACgB,SAAhB,WACkB,EAAlB,iBACoB,KAApB,wBAOY,OAAZ,OAAY,CAAZ,QACA,oBACc,EAAd,yBACA,EACA,gCAEc,EAAd,eAAgB,IAAhB,YAEA,kBACc,EAAd,eAAgB,IAAhB,eAIA,mBACU,EAAV,eAAY,IAAZ,eC1JiW,I,wBCQ7VK,EAAY,eACd,EACAnD,EACAiC,GACA,EACA,KACA,WACA,MAIa,aAAAkB,E", "file": "h5/js/chunk-275e6594.c6141a48.js", "sourcesContent": ["import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Recharge.vue?vue&type=style&index=0&id=01e6b6da&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Recharge.vue?vue&type=style&index=0&id=01e6b6da&scoped=true&lang=css&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',{staticClass:\"recharge\"},[_c('div',{staticClass:\"nav acea-row row-around row-middle\"},_vm._l((_vm.navRecharge),function(item,index){return _c('div',{key:index,staticClass:\"item\",class:_vm.active === index ? 'on' : '',on:{\"click\":function($event){return _vm.navRecharges(index)}}},[_vm._v(\" \"+_vm._s(item)+\" \")])}),0),_c('div',{staticClass:\"info-wrapper\"},[_c('div',{staticClass:\"money\"},[_c('span',[_vm._v(\"￥\")]),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.money),expression:\"money\"}],attrs:{\"type\":\"number\",\"placeholder\":\"0.00\"},domProps:{\"value\":(_vm.money)},on:{\"input\":function($event){if($event.target.composing){ return; }_vm.money=$event.target.value}}})]),(!_vm.active)?_c('div',{staticClass:\"tips\"},[_vm._v(\" 提示：当前余额为\"),_c('span',[_vm._v(\"￥\"+_vm._s(_vm.now_money || 0))])]):_c('div',{staticClass:\"tips\"},[_vm._v(\" 提示：当前佣金为\"),_c('span',[_vm._v(\"￥\"+_vm._s(_vm.userInfo.brokerage_price || 0))])]),_c('div',{staticClass:\"pay-btn bg-color-red\",on:{\"click\":_vm.recharge}},[_vm._v(\" \"+_vm._s(_vm.active ? \"立即转入\" : \"立即充值\")+\" \")])])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <div class=\"recharge\">\n      <div class=\"nav acea-row row-around row-middle\">\n        <div\n          class=\"item\"\n          :class=\"active === index ? 'on' : ''\"\n          v-for=\"(item, index) in navRecharge\"\n          :key=\"index\"\n          @click=\"navRecharges(index)\"\n        >\n          {{ item }}\n        </div>\n      </div>\n      <div class=\"info-wrapper\">\n        <div class=\"money\">\n          <span>￥</span>\n          <input type=\"number\" placeholder=\"0.00\" v-model=\"money\" />\n        </div>\n        <div class=\"tips\" v-if=\"!active\">\n          提示：当前余额为<span>￥{{ now_money || 0 }}</span>\n        </div>\n        <div class=\"tips\" v-else>\n          提示：当前佣金为<span>￥{{ userInfo.brokerage_price || 0 }}</span>\n        </div>\n        <div class=\"pay-btn bg-color-red\" @click=\"recharge\">\n          {{ active ? \"立即转入\" : \"立即充值\" }}\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { mapGetters } from \"vuex\";\nimport { pay } from \"@libs/wechat\";\nimport { isWeixin } from \"@utils\";\nimport { rechargeWechat } from \"@api/user\";\nimport { add, sub } from \"@utils/bc\";\n\nexport default {\n  name: \"Recharge\",\n  components: {},\n  props: {},\n  data: function() {\n    return {\n      navRecharge: [\"账户充值\", \"佣金导入\"],\n      active: 0,\n      payType: [\"weixin\"],\n      from: isWeixin() ? \"weixin\" : \"weixinh5\",\n      money: \"\",\n      now_money: \"\"\n    };\n  },\n  computed: mapGetters([\"userInfo\"]),\n  mounted: function() {\n    this.now_money = this.userInfo.now_money;\n  },\n  methods: {\n    navRecharges: function(index) {\n      this.active = index;\n    },\n    recharge: function() {\n      let that = this,\n        price = Number(this.money);\n      if (that.active) {\n        if (price === 0) {\n          return that.$dialog.toast({ mes: \"请输入您要转入的金额\" });\n        } else if (price < 0.01) {\n          return that.$dialog.toast({ mes: \"转入金额不能低于0.01\" });\n        }\n        this.$dialog.confirm({\n          mes: \"转入余额无法在转出，请确认转入\",\n          title: \"转入余额\",\n          opts: [\n            {\n              txt: \"确认\",\n              color: false,\n              callback: () => {\n                rechargeWechat({ price: price, from: that.from, type: 1 })\n                  .then(res => {\n                    that.now_money = add(\n                      price,\n                      parseInt(that.userInfo.now_money)\n                    );\n                    that.userInfo.brokerage_price = sub(\n                      that.userInfo.brokerage_price,\n                      price\n                    );\n                    that.money = \"\";\n                    return that.$dialog.toast({ mes: res.msg });\n                  })\n                  .catch(res => {\n                    that.$dialog.toast({ mes: res.msg });\n                  });\n              }\n            },\n            {\n              txt: \"取消\",\n              color: false,\n              callback: () => {\n                return that.$dialog.toast({ mes: \"已取消\" });\n              }\n            }\n          ]\n        });\n      } else {\n        if (price === 0) {\n          return that.$dialog.toast({ mes: \"请输入您要充值的金额\" });\n        } else if (price < 0.01) {\n          return that.$dialog.toast({ mes: \"充值金额不能低于0.01\" });\n        }\n        rechargeWechat({ price: price, from: that.from })\n          .then(res => {\n            var data = res.data;\n            if (data.type == \"weixinh5\") {\n              location.replace(data.data.mweb_url);\n              this.$dialog.confirm({\n                mes: \"充值余额\",\n                opts: [\n                  {\n                    txt: \"已充值\",\n                    color: false,\n                    callback: () => {\n                      that.$router.replace({\n                        path: \"/user/account\"\n                      });\n                    }\n                  },\n                  {\n                    txt: \"查看余额\",\n                    color: false,\n                    callback: () => {\n                      that.$router.replace({\n                        path: \"/user/account\"\n                      });\n                    }\n                  }\n                ]\n              });\n            } else {\n              pay(data.data)\n                .finally(() => {\n                  that.now_money = add(\n                    price,\n                    parseInt(that.userInfo.now_money)\n                  );\n                  that.$dialog.toast({ mes: \"支付成功\" });\n                })\n                .catch(function() {\n                  that.$dialog.toast({ mes: \"支付失败\" });\n                });\n            }\n          })\n          .catch(res => {\n            that.$dialog.toast({ mes: res.msg });\n          });\n      }\n    }\n  }\n};\n</script>\n<style scoped>\n#iframe {\n  display: none;\n}\n.recharge {\n  width: 7.03rem;\n  padding: 0.5rem 0.63rem 0.45rem;\n  background-color: #fff;\n  margin: 0.2rem auto 0 auto;\n  border-radius: 0.1rem;\n}\n.recharge .nav {\n  height: 0.75rem;\n  line-height: 0.75rem;\n  padding: 0 1rem;\n}\n.recharge .nav .item {\n  font-size: 0.3rem;\n  color: #333;\n}\n.recharge .nav .item.on {\n  font-weight: bold;\n  border-bottom: 0.04rem solid #e83323;\n}\n.recharge .info-wrapper {\n  text-align: center;\n}\n.recharge .info-wrapper .money {\n  margin-top: 0.6rem;\n  padding-bottom: 0.2rem;\n  border-bottom: 1px dashed #ddd;\n}\n.recharge .info-wrapper .money span {\n  font-size: 0.56rem;\n  color: #333;\n  font-weight: bold;\n}\n.recharge .info-wrapper .money input {\n  display: inline-block;\n  width: 3rem;\n  font-size: 0.84rem;\n  text-align: center;\n  color: #282828;\n  font-weight: bold;\n  padding-right: 0.7rem;\n}\n.recharge .info-wrapper .money input::placeholder {\n  color: #ddd;\n}\n.recharge .info-wrapper .money input::-webkit-input-placeholder {\n  color: #ddd;\n}\n.recharge .info-wrapper .money input:-moz-placeholder {\n  color: #ddd;\n}\n.recharge .info-wrapper .money input::-moz-placeholder {\n  color: #ddd;\n}\n.recharge .info-wrapper .money input:-ms-input-placeholder {\n  color: #ddd;\n}\n.recharge .info-wrapper .tips {\n  font-size: 0.26rem;\n  color: #888;\n  margin: 0.25rem auto 0 auto;\n  line-height: 1.5;\n  padding: 0 0.3rem;\n}\n.recharge .info-wrapper .tips span {\n  color: #ef4a49;\n}\n.recharge .info-wrapper .pay-btn {\n  display: block;\n  width: 5.5rem;\n  height: 0.86rem;\n  margin: 0.5rem auto 0 auto;\n  line-height: 0.86rem;\n  text-align: center;\n  color: #fff;\n  border-radius: 0.5rem;\n  font-size: 0.3rem;\n  font-weight: bold;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Recharge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Recharge.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Recharge.vue?vue&type=template&id=01e6b6da&scoped=true&\"\nimport script from \"./Recharge.vue?vue&type=script&lang=js&\"\nexport * from \"./Recharge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Recharge.vue?vue&type=style&index=0&id=01e6b6da&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"01e6b6da\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}