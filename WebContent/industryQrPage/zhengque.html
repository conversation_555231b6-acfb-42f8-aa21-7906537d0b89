<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">
		<meta content="yes" name="apple-mobile-web-app-capable">
		<meta content="black" name="apple-mobile-web-app-status-bar-style">
		<meta content="telephone=no" name="format-detection">
		<meta content="email=no" name="format-detection">

		<title>查询正确</title>
		<link rel="stylesheet" href="css/base.css">
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<script src="js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/jquery-1.12.2.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/zhengque.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript">
			//引入该flexible.min.js
			! function(e, t) {
				function n() {
					var n = l.getBoundingClientRect().width;
					t = t || 540, n > t && (n = t);
					var i = 100 * n / e;
					r.innerHTML = "html{font-size:" + i + "px;}"
				}
				var i, d = document,
					o = window,
					l = d.documentElement,
					r = document.createElement("style");
				if (l.firstElementChild) l.firstElementChild.appendChild(r);
				else {
					var a = d.createElement("div");
					a.appendChild(r), d.write(a.innerHTML), a = null
				}
				n(), o.addEventListener("resize", function() {
					clearTimeout(i), i = setTimeout(n, 300)
				}, !1), o.addEventListener("pageshow", function(e) {
					e.persisted && (clearTimeout(i), i = setTimeout(n, 300))
				}, !1), "complete" === d.readyState ? d.body.style.fontSize = "16px" : d.addEventListener("DOMContentLoaded",
					function(e) {
						d.body.style.fontSize = "16px"
					}, !1)
			}(750, 750);
		</script>
	</head>


	<style>
	
		body{
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			background-color: #FFFFFF;
		}
		.swiper-container {
		width: 100%;
	}
	.swiper-container-horizontal>.swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction{
		bottom: 0rem;
	}
	.swiper-pagination-bullet {
		
		border-radius: .05rem;
		width: .3rem;
		height: .1rem;
		background-color: #FFFFFF;
	}
	
	.swiper-container img {
		width: 100%;
	}
	h2{
		font-size: .3rem;
		margin-bottom: .1rem;
	}
	h3{
		font-size: .22rem;
		margin-bottom: .2rem;
	}
	
	.jieguo{
		color: #0752A4;
		padding: .3rem .3rem .2rem .3rem;
		margin: .2rem;
		border: .02rem solid #0752a4;
		border-radius: .2rem;
      background-color:#e3ebf5;
		
	}
	.jieguo-info {
		display: flex;
	}
	
	.guige{
		font-size: .19rem;
		margin-bottom: .1rem;
		margin-top: .1rem;
		
	}
	.bianqian{
		margin-right: .2rem;
		display: inline-block;
		padding: .01rem .1rem; 
		background-color: #0752A4;
		color: #fff;
		float: left;
	}
	#productName{
		display: block;
	}

	#validateTime{
		
		font-size: .2rem;
		line-height: .2rem;
	}
	.clearfloat::after , .clearfloat::before{
		display: block;
		clear: both;
		content: "";
		visibility: hidden;
	
	}
	.clearfloat::after{
		clear: both;
	}
	.code{
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.bot{
		height: .89rem;
		width: 100%;
		overflow: hidden;
		position: fixed;
		bottom: 0px;
	}
	
</style>

	<body>
		<div class="swiper-container">
			<div class="swiper-wrapper">
				<div class="swiper-slide"><img src="img/swiper1.png"></div>
				<div class="swiper-slide"><img src="img/swiper2.png"></div>
			</div>
			<div class="swiper-pagination"></div>
		</div>
		<div class="jieguo">
			<div class="jieguo-info" style="display: flex;justify-content: space-between;">
				<div class="l" style="width: 75%;">
					<h2>查询结果</h2>
					<h3>感谢您购买雪佛龙润滑油正品</h3>
					<div class="guige clearfloat" style="font-size: .19rem;">
						<div class="bianqian">产品规格:</div><h4><label id="capacity">18L</label></h4>
					</div>
					<div class="guige clearfloat">
						<div class="bianqian">产品名称:</div><h4><label id="productName">雪佛龙®工程机械专用特级柴油机油
SAE 15W-40 
</label></h4>
					</div>
				</div>

				<div class="r" style="width: 20%;text-align: center;">
					<h2 class="bt2">数码正确</h2>
					<div class="jieguo-r">
						<img src="./img/dui.png" style="width: 1.41rem;height: 1.5rem;margin-top: .2rem;">
					</div>
				</div>
			</div>

			<div class="validateTime"><h5><label id="validateTime">首次查询时间：2019年1月12日/查询次数：1次</label><h5></div>
		</div>
		<div class='code' style="flex: 1;">

			<img src="img/code.gif" style="width:4.13rem;height: 4.16rem;">

			<img src="img/ts.png" style="width: 2.85rem;height: 1.28rem;">

		</div>
		<div style="height: .89rem;width: 100%;">
			
		</div>
		<div class="bot">
			<img src="img/bot.png" style="width: 100%;">
		</div>
	</body>
</html>
<script type="text/javascript">
	var mySwiper = new Swiper('.swiper-container', {
		autoplay: true, //可选选项，自动滑动
		pagination: {
			el: '.swiper-pagination',
		},
	})
</script>
