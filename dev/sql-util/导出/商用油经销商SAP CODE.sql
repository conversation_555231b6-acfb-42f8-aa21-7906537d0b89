select o.id, o.organization_name, pe.sap_code from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
where o.status=1 and o.type=1 and o.id!=9
and (exists (select 1 from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on ur.role_id=r.role_id where (u.type is null or u.type != '1') and u.org_id=o.id and r.sales_channel='C&I'
and u.status=1) or pe.partner_property='Delo Distributor')

order by o.id 