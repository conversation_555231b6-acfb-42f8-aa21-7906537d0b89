
--金富力促销积分
SELECT max(cs.sales_name_cn) "FLSR", max(cs.region) Region, max(o.organization_name) "经销商名称", /*p.partner_id "Partner ID",*/
sum(case when plog.CREATION_TIME<'2022-06-01' then 0 when bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' then pde.POINT_VALUE else 0 end) as "总发放", 
sum(case when plog.CREATION_TIME<'2022-06-01' then 0 when bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
sum(pde.POINT_VALUE-pde.POINT_PAYED) "总剩余"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        left join wx_t_promotion_point pp on pp.point_code = pde.point_type
		LEFT JOIN dbo.wx_t_organization o ON pac.POINT_ACCOUNT_OWNER_ID = o.id
		left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
		left join dw_customer_org_sales cs on cs.distributor_id=pe.distributor_id and cs.channel_weight&1>0
        WHERE pde.point_type='CDM_PROMOTION_POINT'
		group by o.id
		having sum(case when plog.CREATION_TIME<'2022-06-01' then 0 when bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' then pde.POINT_VALUE else 0 end)>0		
		order by o.id

--德乐促销积分
SELECT max(cs.sales_name_cn) "FLSR", max(cs.region) Region, max(o.organization_name) "经销商名称", /*p.partner_id "Partner ID",*/
sum(case when plog.CREATION_TIME<'2022-06-01' then 0 when bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' then pde.POINT_VALUE else 0 end) as "总发放", 
sum(case when plog.CREATION_TIME<'2022-06-01' then 0 when bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' then pde.POINT_VALUE else 0 end)-sum(pde.POINT_VALUE-pde.POINT_PAYED) "总兑换", 
sum(pde.POINT_VALUE-pde.POINT_PAYED) "总剩余"
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        left join wx_t_promotion_point pp on pp.point_code = pde.point_type
		LEFT JOIN dbo.wx_t_organization o ON pac.POINT_ACCOUNT_OWNER_ID = o.id
		left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
		left join dw_customer_org_sales cs on cs.distributor_id=pe.distributor_id and cs.channel_weight&2>0
        WHERE pde.point_type='PROMOTION_POINT'
		group by o.id
		having sum(case when plog.CREATION_TIME<'2022-06-01' then 0 when bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' then pde.POINT_VALUE else 0 end)>0		
		order by o.id

--金富力
SELECT cos1.region, cos1.sales_name_cn FLSR, cos1.customer_name_cn "经销商名称", sum(plog.MODIFIED_VALUE) as "总发放积分", --2022-06-06 18:51:17.550
(SELECT sum(plog1.MODIFIED_VALUE) as point
	 FROM wx_t_point_value_detail pde1
	 LEFT JOIN wx_t_point_value_detail_log plog1 ON plog1.POINT_VALUE_ID = pde1.ID
	LEFT JOIN wx_t_point_business bu1 ON plog1.BUSINESS_ID = bu1.ID 
	 WHERE bu1.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME'
	 AND pde.POINT_ACCOUNT_ID=pde1.POINT_ACCOUNT_ID and pde1.POINT_TYPE=pde.POINT_TYPE) "消费积分",
(select sum(pvd.POINT_VALUE) 
 FROM dbo.wx_t_point_value_detail pvd
  left join wx_t_point_business pb on pvd.business_id=pb.id
  where pb.BUSINESS_TYPE_CODE='CLEAR_POINT' and pvd.POINT_ACCOUNT_ID=pde.POINT_ACCOUNT_ID and pvd.POINT_TYPE=pde.POINT_TYPE
  and pb.creation_time>'2022-06-06') "清零积分",
(select sum(pde1.POINT_VALUE-pde1.POINT_PAYED) from wx_t_point_value_detail pde1 where pde1.POINT_ACCOUNT_ID=pde.POINT_ACCOUNT_ID and pde1.point_type=pde.POINT_TYPE) "总剩余"
--min(pde.creation_time)
	 FROM wx_t_point_value_detail pde 
	 LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
	 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
	 left join wx_t_partner_o2o_enterprise pe on pe.partner_id=pac.POINT_ACCOUNT_OWNER_ID
	 left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&1>0
	WHERE --bu.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' --and pde.creation_time between '2022-08-03' and '2022-09-06'
	 bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' and pde.POINT_TYPE='CDM_PROMOTION_POINT'
	 group by cos1.region, cos1.sales_name_cn, cos1.customer_name_cn,pde.POINT_ACCOUNT_ID,pde.POINT_TYPE
	 order by cos1.region, cos1.sales_name_cn, cos1.customer_name_cn
	 
--德乐
SELECT cos1.region, cos1.sales_name_cn FLSR, cos1.customer_name_cn "经销商名称", sum(plog.MODIFIED_VALUE) as "总发放积分", --2022-06-06 18:51:17.550
(SELECT sum(plog1.MODIFIED_VALUE) as point
	 FROM wx_t_point_value_detail pde1
	 LEFT JOIN wx_t_point_value_detail_log plog1 ON plog1.POINT_VALUE_ID = pde1.ID
	LEFT JOIN wx_t_point_business bu1 ON plog1.BUSINESS_ID = bu1.ID 
	 WHERE bu1.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME'
	 AND pde.POINT_ACCOUNT_ID=pde1.POINT_ACCOUNT_ID and pde1.POINT_TYPE=pde.POINT_TYPE) "消费积分",
(select sum(pvd.POINT_VALUE) 
 FROM dbo.wx_t_point_value_detail pvd
  left join wx_t_point_business pb on pvd.business_id=pb.id
  where pb.BUSINESS_TYPE_CODE='CLEAR_POINT' and pvd.POINT_ACCOUNT_ID=pde.POINT_ACCOUNT_ID and pvd.POINT_TYPE=pde.POINT_TYPE
  and pb.creation_time>'2022-06-06') "清零积分",
(select sum(pde1.POINT_VALUE-pde1.POINT_PAYED) from wx_t_point_value_detail pde1 where pde1.POINT_ACCOUNT_ID=pde.POINT_ACCOUNT_ID and pde1.point_type=pde.POINT_TYPE) "总剩余"
--min(pde.creation_time)
	 FROM wx_t_point_value_detail pde 
	 LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
	 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
	 left join wx_t_partner_o2o_enterprise pe on pe.partner_id=pac.POINT_ACCOUNT_OWNER_ID
	 left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&2>0
	WHERE --bu.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' --and pde.creation_time between '2022-08-03' and '2022-09-06'
	 bu.BUSINESS_TYPE_CODE='SELL_IN_PROMOTION_DELIVERY_V2' and pde.POINT_TYPE='PROMOTION_POINT'
	 group by cos1.region, cos1.sales_name_cn, cos1.customer_name_cn,pde.POINT_ACCOUNT_ID,pde.POINT_TYPE
	 order by cos1.region, cos1.sales_name_cn, cos1.customer_name_cn	 
	 
select sum(award_quantity) 
--update d set d.delivery_status=50
from wx_t_promotion_delivery_detail d where d.delivery_status=20 --and ext_property7!='1123' 
and create_time>'2022-06-01'
--order by id desc	 