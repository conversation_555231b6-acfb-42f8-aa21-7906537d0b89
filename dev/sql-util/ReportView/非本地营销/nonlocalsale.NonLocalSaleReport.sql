/*非本地营销-报表-经销商上报统计*/
select from_distributor_name,from_region,from_flsr,from_asm,channel_text,sum(report_times) report_times,non_local_punish,from_distributor_id,channel
from ( 
select isnull(s1.dic_item_name, cs1.customer_name_cn) from_distributor_name, 
isnull(cs1.region, s1.dic_item_name) from_region,
cs1.sales_name_cn from_flsr, cs1.supervisor_name_cn from_asm, diac1.dic_item_name channel_text,
1 report_times, 
(select (select dbo.fun_get_nonlocal_punish_desc(ir3.id) from wx_t_non_local_mkt_flow_form if1
			left join wx_t_non_local_mkt_flow_pro_res ir3 on ir3.flow_id = if1.id and ir3.setp_no = 'ABM_CONFIRM'
			where if1.from_distributor_id=rv.from_distributor_id 
			and ir3.channel & rv.channel>0 and ir3.kpi_result=0
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or if1.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or if1.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
			 FOR XML PATH,TYPE
 ).value('.','varchar(max)')) non_local_punish,rv.from_distributor_id,rv.channel
 from view_nonlocal_sale_report rv
		left join wx_t_dic_item s1 on cast(s1.dic_item_code as bigint) = rv.from_distributor_id and s1.dic_type_code = 'non_local_special_partner'
		left join dw_customer_org_sales cs1 on cs1.distributor_id = rv.from_distributor_id and cs1.channel_weight&rv.channel_weight>0
	  left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=rv.channel
	  left join [PP_MID].[dbo].[syn_dw_to_pp_product] p on rv.sku=p.product_code_SAP
		left join wx_t_region r1 on r1.id = rv.reporting_region_id
		left join wx_t_region r2 on r2.id = r1.parent_id
where (#VARCHAR,fromRegion# is null or #VARCHAR,fromRegion#='' or isnull(cs1.region, s1.dic_item_name)=#VARCHAR,fromRegion#)
and (#BIGINT,fromDistributorId# is null or rv.from_distributor_id=#BIGINT,fromDistributorId#)
and (#INTEGER,channel# is null or rv.channel&#INTEGER,channel#>0)
 and (#VARCHAR,t3Category# is null or #VARCHAR,t3Category#='' or p.[t3_category] like '%' + #VARCHAR,t3Category# + '%')
and (#INTEGER,permissionWeight#&3>0 or (#INTEGER,permissionWeight#&1024>0 and #BIGINT,fromDistributorId# is not null) or (#INTEGER,permissionWeight#&10>0 and exists (select 1 from dw_sales_role dp_001_sr1 where dp_001_sr1.[sales_cai]=cs1.sales_cai and dp_001_sr1.sales_cai_level like '%_'+#VARCHAR,cai#+'%')))
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or rv.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or rv.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
group by s1.dic_item_name,cs1.customer_name_cn,cs1.region,cs1.sales_name_cn,cs1.supervisor_name_cn,diac1.dic_item_name,rv.from_distributor_id,rv.channel,
convert(nvarchar(20),rv.req_time,112),rv.to_distributor_id,r1.id,r2.id,rv.report_type
) tt GROUP BY from_distributor_name,from_region,from_flsr,from_asm,channel_text,non_local_punish,from_distributor_id,channel