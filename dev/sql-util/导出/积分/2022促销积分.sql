--德乐促销积分
select pe.partner_id,d.promotion_id,p.promotion_name + 
(case when d.promotion_id=156 then '(2022-08-03至2022-09-06)' else '(2022-09-06至今)' end) "促销活动", 
cos1.region, cos1.sales_name_cn "FLSR", cos1.customer_name_cn "经销商", 
sum(d.award_quantity) "总发放", 
case when d.promotion_id=156 then 
	(SELECT -sum(cast(plog.MODIFIED_VALUE as decimal(12,2))) as point
	 FROM wx_t_point_value_detail pde 
	 LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
	 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
	WHERE bu.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' and pde.creation_time between '2022-08-03' and '2022-09-06'
	 AND pac.POINT_ACCOUNT_OWNER_ID=pe.partner_id and pde.POINT_TYPE='PROMOTION_POINT')
else 
	(SELECT -sum(cast(plog.MODIFIED_VALUE as decimal(12,2))) as point
	 FROM wx_t_point_value_detail pde 
	 LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
	 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID 
	 WHERE bu.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' and pde.creation_time>'2022-09-06'
	 AND pac.POINT_ACCOUNT_OWNER_ID=pe.partner_id and pde.POINT_TYPE='PROMOTION_POINT') end "消费积分",
	 (SELECT -sum(cast(plog.MODIFIED_VALUE as decimal(12,2))) as point
	 FROM wx_t_point_value_detail pde 
	 LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
	 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID 
	 WHERE bu.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' and pde.creation_time>'2022-08-03'
	 AND pac.POINT_ACCOUNT_OWNER_ID=pe.partner_id and pde.POINT_TYPE='PROMOTION_POINT') "2022-08-03至今消费"
from wx_t_promotion_delivery_detail d 
left join wx_t_sell_in_promotion p on p.id=d.promotion_id
left join dw_customer_org_sales cos1 on cos1.distributor_id=d.ext_property7 and cos1.channel_weight&2>0
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=d.ext_property7
where d.promotion_id in (156,158) and d.delivery_status=20
group by p.promotion_name,p.id, cos1.region, cos1.sales_name_cn, cos1.customer_name_cn,d.promotion_id,pe.partner_id
 --order by p.id;

--德乐促销积分消费明细
SELECT cos1.region, cos1.sales_name_cn "FLSR", cos1.customer_name_cn "经销商", plog.MODIFIED_VALUE point, bu.attribute1 "订单号",
plog.comments,pde.creation_time
	 FROM wx_t_point_value_detail pde 
	 LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
	 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID 
left join wx_t_partner_o2o_enterprise pe on pac.POINT_ACCOUNT_OWNER_ID=pe.partner_id
	 left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&2>0
	 WHERE bu.BUSINESS_TYPE_CODE='PLATFORM_POINT_CONSUME' 
	 and pde.POINT_TYPE='PROMOTION_POINT'
	 order by pde.creation_time