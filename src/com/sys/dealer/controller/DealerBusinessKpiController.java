package com.sys.dealer.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.common.constants.Constants;
import com.sys.dealer.dao.DealerBusinessCustomMapper;
import com.sys.dealer.dao.DealerBusinessKpiMapper;
import com.sys.dealer.model.DealerBusinessCustom;
import com.sys.dealer.model.DealerBusinessCustomExample;
import com.sys.dealer.model.DealerBusinessKpi;
import com.sys.dealer.model.DealerBusinessKpiExample;
import com.sys.dealer.service.DealerBusinessCustomService;

@RequestMapping("/dealerKpi")
@Controller
public class DealerBusinessKpiController {
	@Autowired
	private DealerBusinessKpiMapper dealerBusinessKpiMapper;
	
	@Autowired
	private DealerBusinessCustomMapper dealerBusinessCustomMapper;
	
	@Autowired
	private DealerBusinessCustomService dealerBusinessCustomService;
	
	@ResponseBody
	@RequestMapping("/getKpiByCustomId.do")
	public Map<String, Object> getKpiByCustomId(@RequestParam("partnerId")Long partnerId,@RequestParam("customId")Long customId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			DealerBusinessKpiExample example=new DealerBusinessKpiExample();
			example.createCriteria().andPartnerIdEqualTo(partnerId).andCustomIdEqualTo(customId);
			List<DealerBusinessKpi> kpiList=dealerBusinessKpiMapper.selectByExample(example);
			Map<String,Object> data=new HashMap<String,Object>();
			if(kpiList.size()>0) {
				data.put("kpiTarget", kpiList.get(0).getKpiTarget());
			}
			resultMap.put(Constants.RESULT_DATA_CODE, data);
			resultMap.put(Constants.RESULT_CODE_KEY,Constants.RESULT_SUCCESS);
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
			e.printStackTrace();
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/updateKpiTarget.do")
	public Map<String,Object> updateKpiTarget(@RequestParam("partnerId")Long partnerId,@RequestParam("customId")Long customId,@RequestParam("kpiTarget")Long kpiTarget){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//检查促销业务是否存在
		DealerBusinessCustomExample cusExample = new DealerBusinessCustomExample();
		cusExample.createCriteria().andIdEqualTo(customId);
		List<DealerBusinessCustom> cusList = dealerBusinessCustomMapper.selectByExample(cusExample);
		if(cusList.size()==0) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, "促销业务不存在");
			return resultMap;
		}
		DealerBusinessCustom custom=cusList.get(0);
		//检查是否有这个目标
		DealerBusinessKpiExample example=new DealerBusinessKpiExample();
		example.createCriteria().andPartnerIdEqualTo(partnerId).andCustomIdEqualTo(customId);
		List<DealerBusinessKpi> kpiList=dealerBusinessKpiMapper.selectByExample(example);
		if(kpiList.size()>0) {
			//有目标则修改
			DealerBusinessKpi kpi =kpiList.get(0);
			kpi.setKpiTarget(kpiTarget);
			DealerBusinessKpiExample updateExample=new DealerBusinessKpiExample();
			updateExample.createCriteria().andIdEqualTo(kpi.getId());
			dealerBusinessKpiMapper.updateByExampleSelective(kpi, updateExample);
		} else {
			//没有则新建
			DealerBusinessKpi kpi =new DealerBusinessKpi();
			kpi.setPartnerId(partnerId);
			kpi.setCustomId(customId);
			kpi.setKpiTarget(kpiTarget);
			kpi.setStartTime(custom.getStartDate());
			kpi.setEndTime(custom.getEndDate());
			dealerBusinessKpiMapper.insertSelective(kpi);
		}
		resultMap.put(Constants.RESULT_CODE_KEY,Constants.RESULT_SUCCESS);
		
		return resultMap;
	}
}
