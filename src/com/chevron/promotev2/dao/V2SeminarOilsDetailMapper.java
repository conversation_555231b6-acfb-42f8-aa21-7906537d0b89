package com.chevron.promotev2.dao;

import com.chevron.promotev2.model.V2SeminarOilsDetail;
import com.chevron.promotev2.model.V2SeminarOilsDetailExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 研讨会油料明细-wx_v2_seminar_oils_detail数据库操作对象
 * <AUTHOR>
 * @version 1.0 2020-01-10 14:59
 */
public interface V2SeminarOilsDetailMapper {

	/**
	 * 修改页面根据主键更新研讨会油料明细对象(修改页面修改属性)
	 * @param record 新研讨会油料明细对象
	 * @return 数据库操作影响记录条数
	 */
    int updateForEditPage(V2SeminarOilsDetail record);
    
	/**
	 * 根据主键更新研讨会油料明细对象(非空属性)
	 * @param record 新研讨会油料明细对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(V2SeminarOilsDetail record);

    /**
     * 根据主键删除研讨会油料明细对象
     * @param id 删除研讨会油料明细对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询研讨会油料明细对象
	 * @param id 研讨会油料明细对象主键值
	 * @return 研讨会油料明细对象结果
	 */
    V2SeminarOilsDetail selectByPrimaryKey(Long id);

 	/**
	 * 插入研讨会油料明细对象(非空属性)
	 * @param record 被插入研讨会油料明细对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(V2SeminarOilsDetail record);

	/**
	 * 更新研讨会油料明细对象
	 * @param record 被修改研讨会油料明细对象
	 * @param example 过滤研讨会油料明细对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") V2SeminarOilsDetail record, @Param("example") V2SeminarOilsDetailExample example);

	/**
	 * 删除研讨会油料明细对象
	 * @param example 被删除研讨会油料明细对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(V2SeminarOilsDetailExample example);
    
 	/**
	 * 查询满足条件的研讨会油料明细对象数量
	 * @param example 过滤研讨会油料明细对象条件
	 * @return 满足条件研讨会油料明细对象数量
	 */
    int countByExample(V2SeminarOilsDetailExample example);

	/**
	 * 单表查询研讨会油料明细对象
	 * @param example 过滤研讨会油料明细对象条件
	 * @return 满足条件研讨会油料明细对象集合
	 */
    List<V2SeminarOilsDetail> selectByExample(V2SeminarOilsDetailExample example);

	/**
	 * 列表查询研讨会油料明细对象
	 * @param params 查询条件
	 * @return 满足条件研讨会油料明细对象集合
	 */
    List<V2SeminarOilsDetail> queryByParams(Map<String, Object> params);
}
