;(function($){
	UI.XScrollbar = UI.extend(UI.DomBase, {
		key: 'ui.xscroll',
		cls: 'ui-x-scrollbar', //样式
		configEvents: ['scroll'],
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), o = this.newInstance($d, opts);
			o.contentEl = $('<div class="ui-scrollbar-holder"></div>').appendTo($d.addClass(this.cls).addClass(this.cls + '-' + o.overflow));
			$d.scroll(function(){
				o.triggerScroll();
			});
			return o;
		},
		base: {
			setWidth: function(width){
				UI.setWidth(this.element, width);
				return this;
			},
			setContentWidth: function(contentWidth){
				UI.setWidth(this.contentEl, contentWidth - 0.5);
				return this;
			},
			scrollLeft: function(scrollLeft){
				if(scrollLeft == undefined){
					return this.element.scrollLeft();
				}
				this.element.scrollLeft(scrollLeft);
			},
			triggerScroll: function(){
				this.trigger('scroll', this.element.scrollLeft());
			}
		},
		defaults: {
			scroll: null, //滚动事件
			overflow: 'auto' //scroll, auto
		}
	});
})(jQuery);