/*==============================================================*/
/* Module: 信用申请                                                 */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_credit_customer_info_final                       */
/*==============================================================*/
create table wx_t_credit_customer_info_final (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	bu                               nvarchar(30)                                   null,
	sold_to                          nvarchar(64)                                   null,
	sales_cai                        nvarchar(256)                                  null,
	primary key (id)
);
CREATE INDEX ix_cre_cus_info_final_sold_to ON wx_t_credit_customer_info_final (sold_to);
CREATE INDEX ix_cre_cus_inf_fin_sal_cai ON wx_t_credit_customer_info_final (sales_cai);

/*==============================================================*/
/* Table: wx_t_credit_app_request_form                          */
/*==============================================================*/
create table wx_t_credit_app_request_form (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	process_instance_id              nvarchar(64)                                   null,
	bu                               nvarchar(30)                               not null,
	workflow_status                  int                                        not null,
	customer_type                    nvarchar(30)                                   null,
	sold_to_code                     nvarchar(30)                                   null,
	payer_code                       nvarchar(30)                                   null,
	customer_name                    nvarchar(100)                                  null,
	direct_annual_sales_plan         decimal(20,6)                                  null,
	indirect_annual_sales_plan       decimal(20,6)                                  null,
	ext_flag                         int                                        not null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	flsr_id                          bigint                                         null,
	form_status                      int                                        not null,
	version_no                       bigint                                     not null,
	sales_channel                    nvarchar(50)                                   null,
	locker_id                        nvarchar(64)                                   null,
	request_no                       nvarchar(20)                               not null,
	apply_amount_usd                 decimal(20,6)                                  null,
	primary key (id)
);
ALTER TABLE wx_t_credit_app_request_form ADD CONSTRAINT uk_credit_app_request_form UNIQUE (request_no);
CREATE INDEX ix_cre_app_req_for_pro_ins_id ON wx_t_credit_app_request_form (process_instance_id);
CREATE INDEX ix_cre_app_req_for_sol_to_cod ON wx_t_credit_app_request_form (sold_to_code);

/*==============================================================*/
/* Table: wx_t_cre_app_cus_fin_info                             */
/*==============================================================*/
create table wx_t_cre_app_cus_fin_info (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	form_id                          bigint                                     not null,
	primary key (id)
);
ALTER TABLE wx_t_cre_app_cus_fin_info ADD CONSTRAINT uk_cre_app_cus_fin_info UNIQUE (form_id);

/*==============================================================*/
/* Table: wx_t_cre_app_req_from_att                             */
/*==============================================================*/
create table wx_t_cre_app_req_from_att (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	request_no                       nvarchar(20)                               not null,
	primary key (id)
);
CREATE INDEX ix_cre_app_req_fro_att_req_no ON wx_t_cre_app_req_from_att (request_no);
