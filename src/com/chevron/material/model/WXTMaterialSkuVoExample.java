package com.chevron.material.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WXTMaterialSkuVoExample {
    protected String orderByClause;

	protected boolean distinct;

	protected List<Criteria> oredCriteria;

	public WXTMaterialSkuVoExample() {
		oredCriteria = new ArrayList<Criteria>();
	}

	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	public String getOrderByClause() {
		return orderByClause;
	}

	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	public boolean isDistinct() {
		return distinct;
	}

	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<Criterion>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andMaterialIdIsNull() {
			addCriterion("MATERIAL_ID is null");
			return (Criteria) this;
		}

		public Criteria andMaterialIdIsNotNull() {
			addCriterion("MATERIAL_ID is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialIdEqualTo(Long value) {
			addCriterion("MATERIAL_ID =", value, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdNotEqualTo(Long value) {
			addCriterion("MATERIAL_ID <>", value, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdGreaterThan(Long value) {
			addCriterion("MATERIAL_ID >", value, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdGreaterThanOrEqualTo(Long value) {
			addCriterion("MATERIAL_ID >=", value, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdLessThan(Long value) {
			addCriterion("MATERIAL_ID <", value, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdLessThanOrEqualTo(Long value) {
			addCriterion("MATERIAL_ID <=", value, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdIn(List<Long> values) {
			addCriterion("MATERIAL_ID in", values, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdNotIn(List<Long> values) {
			addCriterion("MATERIAL_ID not in", values, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdBetween(Long value1, Long value2) {
			addCriterion("MATERIAL_ID between", value1, value2, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialIdNotBetween(Long value1, Long value2) {
			addCriterion("MATERIAL_ID not between", value1, value2, "materialId");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeIsNull() {
			addCriterion("MATERIAL_SKU_CODE is null");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeIsNotNull() {
			addCriterion("MATERIAL_SKU_CODE is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeEqualTo(String value) {
			addCriterion("MATERIAL_SKU_CODE =", value, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeNotEqualTo(String value) {
			addCriterion("MATERIAL_SKU_CODE <>", value, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeGreaterThan(String value) {
			addCriterion("MATERIAL_SKU_CODE >", value, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeGreaterThanOrEqualTo(String value) {
			addCriterion("MATERIAL_SKU_CODE >=", value, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeLessThan(String value) {
			addCriterion("MATERIAL_SKU_CODE <", value, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeLessThanOrEqualTo(String value) {
			addCriterion("MATERIAL_SKU_CODE <=", value, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeIn(List<String> values) {
			addCriterion("MATERIAL_SKU_CODE in", values, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeNotIn(List<String> values) {
			addCriterion("MATERIAL_SKU_CODE not in", values, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeBetween(String value1, String value2) {
			addCriterion("MATERIAL_SKU_CODE between", value1, value2, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuCodeNotBetween(String value1, String value2) {
			addCriterion("MATERIAL_SKU_CODE not between", value1, value2, "materialSkuCode");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorIsNull() {
			addCriterion("MATERIAL_SKU_PROP_COLOR is null");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorIsNotNull() {
			addCriterion("MATERIAL_SKU_PROP_COLOR is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_COLOR =", value, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorNotEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_COLOR <>", value, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorGreaterThan(String value) {
			addCriterion("MATERIAL_SKU_PROP_COLOR >", value, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorGreaterThanOrEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_COLOR >=", value, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorLessThan(String value) {
			addCriterion("MATERIAL_SKU_PROP_COLOR <", value, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorLessThanOrEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_COLOR <=", value, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorIn(List<String> values) {
			addCriterion("MATERIAL_SKU_PROP_COLOR in", values, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorNotIn(List<String> values) {
			addCriterion("MATERIAL_SKU_PROP_COLOR not in", values, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorBetween(String value1, String value2) {
			addCriterion("MATERIAL_SKU_PROP_COLOR between", value1, value2, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropColorNotBetween(String value1, String value2) {
			addCriterion("MATERIAL_SKU_PROP_COLOR not between", value1, value2, "materialSkuPropColor");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeIsNull() {
			addCriterion("MATERIAL_SKU_PROP_SIZE is null");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeIsNotNull() {
			addCriterion("MATERIAL_SKU_PROP_SIZE is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_SIZE =", value, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeNotEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_SIZE <>", value, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeGreaterThan(String value) {
			addCriterion("MATERIAL_SKU_PROP_SIZE >", value, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeGreaterThanOrEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_SIZE >=", value, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeLessThan(String value) {
			addCriterion("MATERIAL_SKU_PROP_SIZE <", value, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeLessThanOrEqualTo(String value) {
			addCriterion("MATERIAL_SKU_PROP_SIZE <=", value, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeIn(List<String> values) {
			addCriterion("MATERIAL_SKU_PROP_SIZE in", values, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeNotIn(List<String> values) {
			addCriterion("MATERIAL_SKU_PROP_SIZE not in", values, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeBetween(String value1, String value2) {
			addCriterion("MATERIAL_SKU_PROP_SIZE between", value1, value2, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andMaterialSkuPropSizeNotBetween(String value1, String value2) {
			addCriterion("MATERIAL_SKU_PROP_SIZE not between", value1, value2, "materialSkuPropSize");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagIsNull() {
			addCriterion("DELETE_FLAG is null");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagIsNotNull() {
			addCriterion("DELETE_FLAG is not null");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagEqualTo(Boolean value) {
			addCriterion("DELETE_FLAG =", value, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagNotEqualTo(Boolean value) {
			addCriterion("DELETE_FLAG <>", value, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagGreaterThan(Boolean value) {
			addCriterion("DELETE_FLAG >", value, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagGreaterThanOrEqualTo(Boolean value) {
			addCriterion("DELETE_FLAG >=", value, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagLessThan(Boolean value) {
			addCriterion("DELETE_FLAG <", value, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagLessThanOrEqualTo(Boolean value) {
			addCriterion("DELETE_FLAG <=", value, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagIn(List<Boolean> values) {
			addCriterion("DELETE_FLAG in", values, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagNotIn(List<Boolean> values) {
			addCriterion("DELETE_FLAG not in", values, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagBetween(Boolean value1, Boolean value2) {
			addCriterion("DELETE_FLAG between", value1, value2, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andDeleteFlagNotBetween(Boolean value1, Boolean value2) {
			addCriterion("DELETE_FLAG not between", value1, value2, "deleteFlag");
			return (Criteria) this;
		}

		public Criteria andAttribute1IsNull() {
			addCriterion("ATTRIBUTE1 is null");
			return (Criteria) this;
		}

		public Criteria andAttribute1IsNotNull() {
			addCriterion("ATTRIBUTE1 is not null");
			return (Criteria) this;
		}

		public Criteria andAttribute1EqualTo(String value) {
			addCriterion("ATTRIBUTE1 =", value, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1NotEqualTo(String value) {
			addCriterion("ATTRIBUTE1 <>", value, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1GreaterThan(String value) {
			addCriterion("ATTRIBUTE1 >", value, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
			addCriterion("ATTRIBUTE1 >=", value, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1LessThan(String value) {
			addCriterion("ATTRIBUTE1 <", value, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1LessThanOrEqualTo(String value) {
			addCriterion("ATTRIBUTE1 <=", value, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1In(List<String> values) {
			addCriterion("ATTRIBUTE1 in", values, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1NotIn(List<String> values) {
			addCriterion("ATTRIBUTE1 not in", values, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1Between(String value1, String value2) {
			addCriterion("ATTRIBUTE1 between", value1, value2, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute1NotBetween(String value1, String value2) {
			addCriterion("ATTRIBUTE1 not between", value1, value2, "attribute1");
			return (Criteria) this;
		}

		public Criteria andAttribute2IsNull() {
			addCriterion("ATTRIBUTE2 is null");
			return (Criteria) this;
		}

		public Criteria andAttribute2IsNotNull() {
			addCriterion("ATTRIBUTE2 is not null");
			return (Criteria) this;
		}

		public Criteria andAttribute2EqualTo(String value) {
			addCriterion("ATTRIBUTE2 =", value, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2NotEqualTo(String value) {
			addCriterion("ATTRIBUTE2 <>", value, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2GreaterThan(String value) {
			addCriterion("ATTRIBUTE2 >", value, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
			addCriterion("ATTRIBUTE2 >=", value, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2LessThan(String value) {
			addCriterion("ATTRIBUTE2 <", value, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2LessThanOrEqualTo(String value) {
			addCriterion("ATTRIBUTE2 <=", value, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2In(List<String> values) {
			addCriterion("ATTRIBUTE2 in", values, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2NotIn(List<String> values) {
			addCriterion("ATTRIBUTE2 not in", values, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2Between(String value1, String value2) {
			addCriterion("ATTRIBUTE2 between", value1, value2, "attribute2");
			return (Criteria) this;
		}

		public Criteria andAttribute2NotBetween(String value1, String value2) {
			addCriterion("ATTRIBUTE2 not between", value1, value2, "attribute2");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("CREATION_TIME is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("CREATION_TIME is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(Date value) {
			addCriterion("CREATION_TIME =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(Date value) {
			addCriterion("CREATION_TIME <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(Date value) {
			addCriterion("CREATION_TIME >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("CREATION_TIME >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(Date value) {
			addCriterion("CREATION_TIME <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(Date value) {
			addCriterion("CREATION_TIME <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<Date> values) {
			addCriterion("CREATION_TIME in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<Date> values) {
			addCriterion("CREATION_TIME not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(Date value1, Date value2) {
			addCriterion("CREATION_TIME between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(Date value1, Date value2) {
			addCriterion("CREATION_TIME not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreatedByIsNull() {
			addCriterion("CREATED_BY is null");
			return (Criteria) this;
		}

		public Criteria andCreatedByIsNotNull() {
			addCriterion("CREATED_BY is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedByEqualTo(Long value) {
			addCriterion("CREATED_BY =", value, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByNotEqualTo(Long value) {
			addCriterion("CREATED_BY <>", value, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByGreaterThan(Long value) {
			addCriterion("CREATED_BY >", value, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
			addCriterion("CREATED_BY >=", value, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByLessThan(Long value) {
			addCriterion("CREATED_BY <", value, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByLessThanOrEqualTo(Long value) {
			addCriterion("CREATED_BY <=", value, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByIn(List<Long> values) {
			addCriterion("CREATED_BY in", values, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByNotIn(List<Long> values) {
			addCriterion("CREATED_BY not in", values, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByBetween(Long value1, Long value2) {
			addCriterion("CREATED_BY between", value1, value2, "createdBy");
			return (Criteria) this;
		}

		public Criteria andCreatedByNotBetween(Long value1, Long value2) {
			addCriterion("CREATED_BY not between", value1, value2, "createdBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("LAST_UPDATE_TIME is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("LAST_UPDATE_TIME is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(Date value) {
			addCriterion("LAST_UPDATE_TIME =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(Date value) {
			addCriterion("LAST_UPDATE_TIME <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(Date value) {
			addCriterion("LAST_UPDATE_TIME >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("LAST_UPDATE_TIME >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(Date value) {
			addCriterion("LAST_UPDATE_TIME <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(Date value) {
			addCriterion("LAST_UPDATE_TIME <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<Date> values) {
			addCriterion("LAST_UPDATE_TIME in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<Date> values) {
			addCriterion("LAST_UPDATE_TIME not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(Date value1, Date value2) {
			addCriterion("LAST_UPDATE_TIME between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(Date value1, Date value2) {
			addCriterion("LAST_UPDATE_TIME not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByIsNull() {
			addCriterion("LAST_UPDATED_BY is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByIsNotNull() {
			addCriterion("LAST_UPDATED_BY is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByEqualTo(Long value) {
			addCriterion("LAST_UPDATED_BY =", value, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByNotEqualTo(Long value) {
			addCriterion("LAST_UPDATED_BY <>", value, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByGreaterThan(Long value) {
			addCriterion("LAST_UPDATED_BY >", value, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByGreaterThanOrEqualTo(Long value) {
			addCriterion("LAST_UPDATED_BY >=", value, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByLessThan(Long value) {
			addCriterion("LAST_UPDATED_BY <", value, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByLessThanOrEqualTo(Long value) {
			addCriterion("LAST_UPDATED_BY <=", value, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByIn(List<Long> values) {
			addCriterion("LAST_UPDATED_BY in", values, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByNotIn(List<Long> values) {
			addCriterion("LAST_UPDATED_BY not in", values, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByBetween(Long value1, Long value2) {
			addCriterion("LAST_UPDATED_BY between", value1, value2, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLastUpdatedByNotBetween(Long value1, Long value2) {
			addCriterion("LAST_UPDATED_BY not between", value1, value2, "lastUpdatedBy");
			return (Criteria) this;
		}

		public Criteria andLimitQtyIsNull() {
			addCriterion("LIMIT_QTY is null");
			return (Criteria) this;
		}

		public Criteria andLimitQtyIsNotNull() {
			addCriterion("LIMIT_QTY is not null");
			return (Criteria) this;
		}

		public Criteria andLimitQtyEqualTo(Long value) {
			addCriterion("LIMIT_QTY =", value, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyNotEqualTo(Long value) {
			addCriterion("LIMIT_QTY <>", value, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyGreaterThan(Long value) {
			addCriterion("LIMIT_QTY >", value, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyGreaterThanOrEqualTo(Long value) {
			addCriterion("LIMIT_QTY >=", value, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyLessThan(Long value) {
			addCriterion("LIMIT_QTY <", value, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyLessThanOrEqualTo(Long value) {
			addCriterion("LIMIT_QTY <=", value, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyIn(List<Long> values) {
			addCriterion("LIMIT_QTY in", values, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyNotIn(List<Long> values) {
			addCriterion("LIMIT_QTY not in", values, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyBetween(Long value1, Long value2) {
			addCriterion("LIMIT_QTY between", value1, value2, "limitQty");
			return (Criteria) this;
		}

		public Criteria andLimitQtyNotBetween(Long value1, Long value2) {
			addCriterion("LIMIT_QTY not between", value1, value2, "limitQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyIsNull() {
			addCriterion("TRADE_QTY is null");
			return (Criteria) this;
		}

		public Criteria andTradeQtyIsNotNull() {
			addCriterion("TRADE_QTY is not null");
			return (Criteria) this;
		}

		public Criteria andTradeQtyEqualTo(Long value) {
			addCriterion("TRADE_QTY =", value, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyNotEqualTo(Long value) {
			addCriterion("TRADE_QTY <>", value, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyGreaterThan(Long value) {
			addCriterion("TRADE_QTY >", value, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyGreaterThanOrEqualTo(Long value) {
			addCriterion("TRADE_QTY >=", value, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyLessThan(Long value) {
			addCriterion("TRADE_QTY <", value, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyLessThanOrEqualTo(Long value) {
			addCriterion("TRADE_QTY <=", value, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyIn(List<Long> values) {
			addCriterion("TRADE_QTY in", values, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyNotIn(List<Long> values) {
			addCriterion("TRADE_QTY not in", values, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyBetween(Long value1, Long value2) {
			addCriterion("TRADE_QTY between", value1, value2, "tradeQty");
			return (Criteria) this;
		}

		public Criteria andTradeQtyNotBetween(Long value1, Long value2) {
			addCriterion("TRADE_QTY not between", value1, value2, "tradeQty");
			return (Criteria) this;
		}
	}

	public static class Criteria extends GeneratedCriteria {
		protected Criteria() {
			super();
		}
	}

	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}
}