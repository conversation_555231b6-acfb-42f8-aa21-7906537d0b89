SELECT [organization_name] "经销商名称"
      ,store_name "仓库名称"
      ,[sku] SKU
      ,[product_name] "产品名称"
      ,[category_text] "产品分类"
      ,[product_line_text] "产品系列"
      ,[liters] "升数"
      ,CONVERT(varchar(100), [in_stock_time], 120) "入库时间"
  FROM [view_distributor_in_stock] v
  where (#NVARCHAR,i_store_id# is null or #NVARCHAR,i_store_id#='' or charindex(','+convert(varchar(20), v.[store_id]) + ',', #NVARCHAR,i_store_id#) > 0 )
and (#NVARCHAR,i_product_category# is null or #NVARCHAR,i_product_category#='' or charindex(','+v.[category] + ',', #NVARCHAR,i_product_category#) > 0 )
and (#NVARCHAR,i_product_line# is null or #NVARCHAR,i_product_line#='' or charindex(','+v.product_line + ',', #NVARCHAR,i_product_line#) > 0 )
and (#NVARCHAR,i_product_size# is null or #NVARCHAR,i_product_size#='' or charindex(','+v.capacity + ',', #NVARCHAR,i_product_size#) > 0 )
and (#NVARCHAR,i_product_specification# is null or #NVARCHAR,i_product_specification#='' or charindex(','+v.viscosity+ ',', #NVARCHAR,i_product_specification#) > 0 )
and (#NVARCHAR,i_product_code# is null or #NVARCHAR,i_product_code#='' or charindex(','+v.sku + ',', #NVARCHAR,i_product_code#) > 0 )
and (#NVARCHAR,i_region# is null or #NVARCHAR,i_region#='' or exists (select 1 from dw_customer_org_sales cos2 where cos2.distributor_id=v.distributor_id and charindex(','+cos2.region + ',', #NVARCHAR,i_region#) > 0 ))
and (v.[in_stock_time]>=#NVARCHAR,i_date_from# and v.[in_stock_time]<dateadd(day, 1, #NVARCHAR,i_date_to#))
and (1=1 or #INTEGER,i_permission_weight#&1>0 
	or (#INTEGER,i_permission_weight#&14>0 and exists (select 1 from dw_customer_org_sales cos1 where cos1.distributor_id=v.distributor_id and (cos1.supervisor_cai=#NVARCHAR,i_sals_cai# or cos1.sales_cai=#NVARCHAR,i_sals_cai#))
	or (#INTEGER,i_permission_weight#&16>0) and v.distributor_id=#BIGINT,i_distributor_id#))
