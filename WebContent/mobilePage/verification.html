<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>机油核销</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">

    <link rel="stylesheet" href="css/mui.min.css">
    <link rel="stylesheet" type="text/css" href="css/mui.picker.min.css"/>
    <link rel="stylesheet" href="css/verification.css">
    <link rel="stylesheet" href="css/loading.css">
    <script type="text/javascript" src="js/jquery-1.12.2.min.js"></script>
    <script language="javascript" type="text/javascript" src="js/jquery.json-2.4.min.js"></script>
    <script language="javascript" type="text/javascript" src="js/jquery.jsonrpcclient.js"></script>
    <script type="text/javascript" src="js/mui.min.js"></script>
    <script type="text/javascript" src="js/mui.picker.min.js"></script>
    <script src="js/loading.js"></script>
    <script type="text/javascript" src="js/verification.js"></script>
    <style>
        #detail {
            line-height: 42px;
        }
    </style>
</head>
<body>
<header class="mui-bar mui-bar-nav">
    <h1 class="mui-title">机油核销</h1>
</header>
<div class="mui-content">
    <div class="mui-content-padded">
        <!--<span class="time mui-text-center">请选择时间段</span>-->
        <button id='startTime' data-options='{"type":"date"}' class="time-btn btn mui-btn">选择起始日期</button>
        <span class="time mui-text-center">至</span>
        <button id='endTime' data-options='{"type":"date"}' class="time-btn btn mui-btn">选择截止日期</button>
        <button type="button" class="custom-button mui-btn mui-pull-right" onclick="query()">查询</button>
    </div>
    <ul class="mui-table-view">
        <li class="mui-table-view-cell" id="moreDetail">
            技师扫描升数
            <span id="detail" class="mui-pull-right"><span id="scanQuantity">X</span>升</span>
            <p class="mui-ellipsis">点击查看详情</p>
        </li>
        <li class="mui-table-view-cell">有效用户扫描瓶码次数<span class="mui-pull-right"><span id="validScanTimes">X</span>次</span>
        </li>
        <li class="mui-table-view-cell">无效用户扫描瓶码次数<span class="mui-pull-right"><span id="invalidScanTimes">X</span>次</span>
        </li>
        <li class="mui-table-view-cell">可核销升数<span class="mui-pull-right"><span
                id="validVerifyQuantity">X</span>升</span></li>
        <li class="mui-table-view-cell">已结算升数<span class="mui-pull-right"><span id="closedCapacity">X</span>升</span>
        </li>
    </ul>
    <h5 class="mui-content-padded" style="margin: 15px 10px;">*三个月内最多扫描2次,即为有效扫码客户,记为有效结算凭证</h5>
    <script>
        (function ($) {
            $.init();
            var btns = $('.time-btn');
            btns.each(function (i, btn) {
                btn.addEventListener('tap', function () {
                    var optionsJson = this.getAttribute('data-options') || '{}';
                    var options = JSON.parse(optionsJson);
                    var picker = new $.DtPicker(options);
                    picker.show(function (rs) {
                        btn.innerHTML = rs.text;
                        picker.dispose();
                    });
                }, false);
            });
        })(mui);
    </script>
</div>
</body>
</html>