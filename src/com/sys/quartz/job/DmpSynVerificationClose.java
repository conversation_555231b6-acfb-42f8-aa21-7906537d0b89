package com.sys.quartz.job;

import java.util.Calendar;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.pms.dao.DmpMapper;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;
import com.sys.log.business.LogBizService;
import com.sys.log.model.Log;

public class DmpSynVerificationClose {

	@Autowired
	private DmpMapper dmpMapper;
    
    @Autowired
    private LogBizService logBizService;
	
	public synchronized void execute(){
		final Log log = new Log();
		final Date now = DateUtil.getCurrentDate();
		try {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(now);
			calendar.add(Calendar.MONTH, -1);
			dmpMapper.synVerificationClose(DateUtil.getFirstDayOfMonth(calendar.getTime()));
		} catch (Exception e) {
			log.setExtProperty3("ERROR");
			log.setExtProperty4(e.getMessage());
		}
		log.setOperator(1l);
		log.setLogType("Dmp.sysLog");
		log.setExtProperty1("synVerificationClose");
		log.setCreateTime(DateUtil.getCurrentDate());
		log.setExtProperty2((DateUtil.getCurrentDate().getTime() - now.getTime()) + "");
		try {
			logBizService.insert(log);
		} catch (WxPltException e) {
			e.printStackTrace();
		}
	}
}
