$(function() {
	newhomepage_init.init();
    var thisYear = new Date().getFullYear();
    for (var j = 0; j < 2; j++) {//TODO
    	var i = thisYear - j;
        $('#year_c ul')
            .append($('<li class="'+(i === currentYear?'active':'')+'">'+i+'</li>').click(function() {
                $(this).siblings().removeClass('active');
                $(this).addClass('active');
                reloadPage({
                	year: $(this).text()
                });
            }));
    }
});
function reloadPage(params){
	var p1 = $.extend({}, currentParams);
	if(params){
		p1 = $.extend(p1, params);
	}
	if(p1.partnerName){
		p1.partnerName = encodeURIComponent(encodeURIComponent(p1.partnerName));
	}
	var u = null;
	for(var k in p1){
		if(u == null){
			u = common.ctx + 'newhomepage/jsp/asm/SpSalesThroughAnalysis.jsp?';
		}else {
			u += '&';
		}
		u += k + '=' + p1[k];
	}
	if(u == null){
		u = common.ctx + 'newhomepage/jsp/asm/SpSalesThroughAnalysis.jsp';
	}
	window.location = u;
}

var detailDealerInfoChart = null;
var workshopSkuDetailDialog = null;
function showWorkshopDetailDialog(workshopName,month) {
    if (workshopSkuDetailDialog == null) {
        BUI.use("bui/overlay", function (Overlay) {
            workshopSkuDetailDialog = new Overlay.Dialog({
                title: workshopName + "  " + newhomepage_init.getSelectedYear() + "年" + month + "月扫码出库详情",
                width: 800,
                height:660,
                mask: false,
                buttons:[],
                contentId:"workshopSkuDetailDialog"
            });
        });
    }
    workshopSkuDetailDialog.show();
}

function genShowSkuDetailHtml(value,item,month) {
    if(value!=null && value!=undefined){
        var opt= "<a href='javascript:void(0);' class='black_url' onclick='newhomepage_init.loadScanInfoByWorkshopAndMonthAndSku(" + item.workshopId + "," + "\"" + item.workShopName + "\"" + "," + "\"" + month + "\"" +")'>"+value+"</a>&nbsp;";
        return opt;
    } else {
        return value
    }
}

var taskMonthChart;
/**
 * 机构下拉框
 * @type {null}
 */
var organizationCtrl = null;
var gridStore=null;
var grid = null;
function refreshGrid() {
    if(gridStore == null){
        BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
            var Grid = Grid,
                Store = Data.Store;
            var columns = [
                { title: '序号', sortable: false, width: 50, renderer: function (value, item, index) { return index + 1; } },
                { title: 'SP名称', sortable: true, dataIndex: 'spName', width: '10%'},
                { title: '门店名称', sortable:true, dataIndex: 'workShopName', width: '15%' },
                { title: '新加入门店时间', dataIndex: 'createTime', width: '10%',renderer: function (value,item,index) {
                        var transTimeHtml = "N/A";
                        if(value){
                            transTimeHtml = common.formatDate(new Date(value), 'yyyy-MM-dd');
                        }
                        return transTimeHtml;
                    } },
                { title: '1月(L)', dataIndex: 'litersMonth1', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,1);
                    } },
                { title: '2月(L)', dataIndex: 'litersMonth2', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,2);
                    } },
                { title: '3月(L)', dataIndex: 'litersMonth3', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,3);
                    } },
                { title: '4月(L)', dataIndex: 'litersMonth4', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,4);
                    } },
                { title: '5月(L)', dataIndex: 'litersMonth5', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,5);
                    } },
                { title: '6月(L)', dataIndex: 'litersMonth6', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,6);
                    } },
                { title: '7月(L)', dataIndex: 'litersMonth7', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,7);
                    } },
                { title: '8月(L)', dataIndex: 'litersMonth8', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,8);
                    } },
                { title: '9月(L)', dataIndex: 'litersMonth9', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,9);
                    } },
                { title: '10月(L)', dataIndex: 'litersMonth10', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,10);
                    } },
                { title: '11月(L)', dataIndex: 'litersMonth11', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,11);
                    } },
                { title: '12月(L)', dataIndex: 'litersMonth12', width: '5%',renderer: function (value,item,index) {
                        return genShowSkuDetailHtml(value,item,12);
                    } }
            ];
            gridStore = new Store({
                url: '/myCdm/queryNewWorkshopScanInfo.do',
                pageSize: 10,
                params: genQueryListParam(),
                remoteSort: true,
                root: "data",
                totalProperty: "total",
                autoLoad: true,
                proxy: {
                    method: 'post'
                }
            }).on("beforeprocessload",function(e){
            });
            grid = new Grid.Grid({
                render: '#adjust_list_grid',
                columns: columns,
                store: gridStore,
                bbar: {
                    pagingBar: true
                }
            });
            grid.render();
            common.initGrid(grid, null, true);
        });
    } else {
        gridStore.load(genQueryListParam());
    }
}
function genQueryListParam() {
    var partnerId = $('#partnerId').val();
//    var year = newhomepage_init.getSelectedYear();
    // var month = newhomepage_init.getSelectedMonth();
    var params = {};
    params.start = 0;
    params.direction = "asc";
    params.field = "createTime";
    params.partnerId = partnerId;
    params.year = currentYear;
     params.month = currentMonth;
    return params;
}
OrgAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts = $.extend({placeholder: '支持名称或名称首字母搜索'}, opts);
        }else{
            opts = {placeholder: '支持名称或名称首字母搜索'};
        }
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
            	// debugger;
                if (!json || !json.resultLst) {
                    return false;
                }

                var len, data = {
                    value: []
                };
                len = json.resultLst.length;

                for (var i = 0; i < len; i++) {
                    data.value.push({
                        value: json.resultLst[i].id,
                        text: json.resultLst[i].organizationName
                    });
                }

                //字符串转化为 js 对象
                return data;
            }
        });
        return Ctrls.AutoSelect.init.call(this, 'myCdm/queryPartnersInfo.do', valueField, 'keyword', opts);
    }
});
var newhomepage_init = (function($) {

    var foo = new $.JsonRpcClient({
        ajaxUrl : '/wxPublicRpc.do'
    });

	var marketIviBar = null,
	
	iconNotUpClass = "fa fa-times-circle not-up-icon",
	iconRightClass = "fa fa-check-circle-o right-icon",
	iconWarmingClass= "fa fa-exclamation-circle warning-icon",
	
	rightTextClass = "up",
	notUpTextClass = "not-up",
	runningTextClass = "running",
	
	rightText = "已达标",
	notUpText = "未达标",
	runningText = "进行中",
	
	workshopTarget = 8,
		
	req = {}, 
	util = {},
	action = {},
	loader = {};
	
	req = {
		    getPartnerSellThroughInfoByDay: function(callback) {
                $.get(util.genPartnerQueryUrl("myCdm/queryPartnerSellThroughInfoByDay.do"), function(data){
                    if(data.code == 'success'){
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
			},
			getMonthlySellInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/monthlySellInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getProductMonthlySellInfo: function(callback) {
				$.get(common.ctx + "newhomepage/productMonthSellInfo.do?t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getQuarterlySellInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/quarterlySellInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getSellThrouthInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/sellThrouthInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
           getSellinStatsByMonthAndType: function (callback) {
            var param =   {
                "partnerId": $('#partnerId').val(),       //getPartnersInfo返回报文里面的id
                "year": newhomepage_init.getSelectedYear(),
                "salesChannel": "CDM",   // CDM或者CIO
                "month": newhomepage_init.getSelectedMonth()
            };
            foo.call('sellinStatsService.querySellinStatsByMonthAndType', [param],
                function (result) {
                    if (result.code == "success") {
                        $.isFunction(callback) && callback(result.resultLst);
                    } else {
                        common.alertMes('拉取数据失败', 'error');
                    }
                }, function (error) {
                    common.ajaxTimeout(error);
                });
        },
        getSellinStatsByMonth: function (callback) {
            var param =   {
                "partnerId": $('#partnerId').val(),       //getPartnersInfo返回报文里面的id
                "year": newhomepage_init.getSelectedYear(),
                "salesChannel": "CDM",   // CDM或者CIO
                "month": newhomepage_init.getSelectedMonth()
            };
            foo.call('sellinStatsService.querySellinStatsByMonth', [param],
                function (result) {
                    if (result.code == "success") {
                        $.isFunction(callback) && callback(result.resultLst);
                    } else {
                        common.alertMes('拉取数据失败', 'error');
                    }
                }, function (error) {
                    common.ajaxTimeout(error);
                });
        },
        getScanInfoByWorkshopAndMonthAndSku: function (callback,workshopId,workshopName,month) {
            var param =   {
                "workshopId": workshopId,       //getPartnersInfo返回报文里面的id
                "year": currentYear,
                "salesChannel": "CDM",   // CDM或者CIO
                "month": month
            };
            $.post(common.ctx + "myCdm/queryScanInfoByWorkshopAndMonthAndSku.do",param, function(data){
                if(data.code == 'success'){
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        }
	},
	util = {
			openMenu: function(url) {
				if(top.openMenuTab){
					top.openMenuTab(url);
				}else{
					window.open(url);
				}
			},
			openUrl: function(url, menuId, menuTitle) {
				if(top.openMenu){
					top.openMenu(url, menuId, menuTitle);
				}else{
					window.open(url);
				}
			},
			openPage: function(url, type) {
				window.location.href = url + "?type=" + type;
			},
			toThousands: function(n) {
				return parseFloat(n).toLocaleString() 
			},
		    genPartnerQueryUrl: function (urlPath) {
                var partnerId = $('#partnerId').val();
                var timestamp = new Date().getTime();
                var year = currentYear;//util.getSelectedYear() || new Date().getFullYear();
                var month = util.getSelectedMonth() || currentMonth;
                // var
                return common.ctx + urlPath + "?t=" + timestamp + "&partnerId="+partnerId + "&year=" + year + "&month=" + month;
            },
		    doSearch: function () {
                    // 获取pointType和orgId
                    var orgId = $('#partnerId').val();
                    if(!orgId){
                        common.alertMes("请选择一个机构再查询", 'error');
                        return;
                    }
		    		currentParams.partnerId = $('#partnerId').val();
		    		currentParams.partnerName = organizationCtrl.getText();
                	loader.initEcharts();
					loader.loadPartnerSellThroughInfoByDay();
                	refreshGrid();
            },
			getSelectedYear : function() {
				if ($('#year-mode .years-wrapper ul li.active').size() > 0 ) {
					return $('#year-mode .years-wrapper ul li.active').text();
				} else {
					return null;
				}
			},
			getSelectedMonth : function() {
				return $("#skuMonth").val();
			}
	},
	loader = {
            loadPartnerSellThroughInfoByDay: function(){
                req.getPartnerSellThroughInfoByDay(function (result) {
                    var screenWidth = $(window).width();
                    var barWidth = "10";
                    if (screenWidth < 1300 && result.x.length > 15) {
                        barWidth = 3;
                    }
                    var taskMonthOption = {
                        tooltip : {     //提示框，鼠标悬浮交互时的信息提示
                            trigger: 'axis',
                            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                                type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                            }
                        },
                        // title: {
                        //     text: '扫码出库情况',
                        //     left: '2%',
                        //     subtextStyle: {
                        //         color: '#333'
                        //     }
                        // },
                        legend: {
                            orient: 'horizontal',
                            left: 'center',
                            top: 0,
                            data: ['出库扫码量(L)'],
                            show:true
                        },
                        yAxis: [{
                            type: 'value',
							axisLabel:{
                                formatter: function(value,index){
                                    return value.toFixed(2).toLocaleString() + 'L';
                                }
                            }
                        }],
                        xAxis: [{
                            type: 'category',
                            data: result.x,
                            axisLabel:{
                                interval:0   // x轴的数据是否一定要全部显示
                            }
                        }],
                        series: [
                            {
                                name: "出库扫码量(L)",
                                data: result.y,
                                type: 'bar',
                                itemStyle: {
                                    normal: {
                                        color: 'rgb(0,166,157)',  //柱状图的颜色
                                        formatter: function(a,b,c){
                                            return util.toThousands(a.data.toFixed(2));
                                        }
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'inside',
                                        textStyle: {
                                            color: 'rgba(255, 255, 255, 1)'
                                        }
                                    }
                                }
                            }]
                    };
                    taskMonthChart.hideLoading();
                    taskMonthChart.setOption(taskMonthOption);
                    taskMonthChart.off('click');
                });
			},
            initSkuQuarterAndYear: function(){
                BUI.use('bui/select', function(Select) {
                    var nowMonth = currentMonth;
                    var nowQuarter = currentQuarter;
                    var monthItems = [];
                    var quarterItems = [];
                    for(var i = 1;i<=nowMonth;i++){
                        monthItems.push({
                            text : i + '月',
                            value : i
                        });
                    }

                    $('#skuMonth').val(nowMonth);
                    var monthCtrl = new Select.Select({
                        render : '#skuMonthC',
                        valueField : '#skuMonth',
                        width : '100%',
                        elStyle : {
                            'white-space' : 'nowrap'
                        },
                        items : monthItems
                    });
                    monthCtrl.render();
                    monthCtrl.on('change', function(ev) {
                        taskMonthChart.showLoading({
                            color: '#c23531',
                            maskColor: 'rgba(200, 200, 200, 0.5)',
                            text : '正在努力的读取数据中...'
                        });
                        loader.loadPartnerSellThroughInfoByDay();
                    });
                });
            },
            initEcharts: function() {
				if ($('#sellThroughInfoByDayChart').size()) {
					if(!taskMonthChart){
						taskMonthChart = echarts.init($('#sellThroughInfoByDayChart')[0]);
					}
					$(window).resize(taskMonthChart.resize);
					taskMonthChart.resize();
					taskMonthChart.showLoading({
						color: '#c23531',
						maskColor: 'rgba(200, 200, 200, 0.5)',
						text : '正在努力的读取数据中...'
					});
				}
                if ($('#detailDealerInfoChart').size()) {
                    if(!detailDealerInfoChart){
                        detailDealerInfoChart = echarts.init($('#detailDealerInfoChart')[0]);
                    }
                    $(window).resize(detailDealerInfoChart.resize);
                    detailDealerInfoChart.resize();
                    detailDealerInfoChart.showLoading({
                        color: '#c23531',
                        maskColor: 'rgba(200, 200, 200, 0.5)',
                        text : '正在努力的读取数据中...'
                    });
                }
            },
		    // SNScanInfoGrid相关
			loadSellThrouth: function() {
				req.getSellThrouthInfo(function(data) {
					//季度数据初始化
					var sellThrouthQuarterList = [];
					for(var q = 1; q <= 4; q++) {
						sellThrouthQuarterList.push({
							quarter: q,
							sellIn: 0,
							sellThrouth: 0,
							workshopCount: 0,
							targetBaseLine: 0,
							sellThrouthFlag: true,
						});
					}
					
					var snMonthTarget = 0;
					for(var i in data) {
						var month = data[i].month;
						var sellThrouth = data[i].sellThrouth;
						var sellIn = data[i].sellIn;
						var workshopCount = data[i].workshopCount;
						var targetBaseLine = data[i].targetBaseLine;
						//每月目标
						if(month == currentMonth) {
							snMonthTarget = (targetBaseLine * 0.5).toFixed(0);
						}
						//生成季度数据
						var quarterIndex = parseInt((month + 2) / 3) - 1;
						sellThrouthQuarterList[quarterIndex].sellIn += sellIn;
						sellThrouthQuarterList[quarterIndex].sellThrouth += sellThrouth;
						sellThrouthQuarterList[quarterIndex].workshopCount += workshopCount;
						sellThrouthQuarterList[quarterIndex].targetBaseLine += targetBaseLine;
						//月目标是否达标
						var $productMonthSellInSpan = $('<span></span>');
						var $productMonthSellInIcon = $('<i aria-hidden="true"></i>');
						if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
							$productMonthSellInIcon.addClass(iconRightClass);
						} else if (sellThrouth / (targetBaseLine * 0.5) < 1 && sellThrouth / (targetBaseLine * 0.5) > 0.75) {
							$productMonthSellInIcon.addClass(iconWarmingClass);
							if(month < currentMonth) {
								sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
							}
						} else {
							$productMonthSellInIcon.addClass(iconNotUpClass);
							if(month < currentMonth) {
								sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
							}
						}
						
						$productMonthSellInSpan.append($productMonthSellInIcon);
						$productMonthSellInSpan.append(util.toThousands(sellThrouth));
						
						$("#p-actual-m" + month).empty();
						$("#p-actual-m" + month).append($productMonthSellInSpan);
					}
					$("#sn-month-target").html(snMonthTarget + "&nbsp;&nbsp;升");
					
					//季度数据加载
					for(var qi in sellThrouthQuarterList) {
						var quarter = sellThrouthQuarterList[qi].quarter;
						var sellThrouth = sellThrouthQuarterList[qi].sellThrouth;
						var sellIn = sellThrouthQuarterList[qi].sellIn;
						var workshopCount = sellThrouthQuarterList[qi].workshopCount;
						var targetBaseLine = sellThrouthQuarterList[qi].targetBaseLine;
						var sellThrouthFlag = sellThrouthQuarterList[qi].sellThrouthFlag;
						//扫码季度达标
						$pTarget = $("#p-target-q" + quarter);
						if(!sellThrouthFlag) {
							$pTarget.addClass(notUpTextClass);
							$pTarget.html(notUpText);
						} else {
							if(quarter >= currentQuarter) {
								$pTarget.addClass(runningTextClass);
								$pTarget.html(runningText);
							} else {
								if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
									$pTarget.addClass(rightTextClass);
									$pTarget.html(rightText);
								} else if(sellThrouth / (targetBaseLine * 0.5) < 1) {
									$pTarget.addClass(notUpTextClass);
									$pTarget.html(notUpText);
								}
							}
						}
						
						//新维修门店
						var $storeCountSpan = $('<span></span>');
						var $storeCountIcon = $('<i aria-hidden="true"></i>');
						if(workshopCount >= workshopTarget) {
							$storeCountIcon.addClass(iconRightClass);
						} else if(workshopCount < workshopTarget && workshopCount >= 6) {
							$storeCountIcon.addClass(iconWarmingClass);
						} else {
							$storeCountIcon.addClass(iconNotUpClass);
						}
						
						$storeCountSpan.append($storeCountIcon);
						$storeCountSpan.append(util.toThousands(workshopCount));
						
						$("#s-actual-q" + quarter).empty();
						$("#s-actual-q" + quarter).append($storeCountSpan);
						//门店达标
						$sTraget = $("#s-target-q" + quarter);
						if(quarter >= currentQuarter) {
							$sTraget.addClass(runningTextClass);
							$sTraget.html(runningText);
						} else {
							if(workshopCount >= workshopTarget) {
								$sTraget.addClass(rightTextClass);
								$sTraget.html(rightText);
							} else {
								$sTraget.addClass(notUpTextClass);
								$sTraget.html(notUpText);
							}
						}
						
					}
				});
			},
		    // total-sales-target,对应"总销量目标"和季度的那些值
			loadQuarterlySales: function() {
				req.getQuarterlySellInfo(function(data) {
					var totalSalesTraget = 0;
					for(var i in data) {
						var quarter = data[i].quarter;
						var targetBaseLine = data[i].targetBaseline;
						var sellIn = data[i].sellIn;
						var monthAvgTarget = (targetBaseLine / 3).toFixed(0);
						//总目标销量
						totalSalesTraget += targetBaseLine;
						//进货量基数标准
						$("#target-q" + quarter).html(util.toThousands(targetBaseLine));
						//月平均进货目标=季度进货量基数标准/3
						for(var j = 0; j < 3; j++) {
							month = 3 * quarter - j;
							$("#target-m" + month).html(util.toThousands(monthAvgTarget));
							$("#target-m" + month).attr("title",util.toThousands(monthAvgTarget));
						}
						//季度实际完成进货量
						var sellInDiv = $('<div></div>');
						sellInDiv.html(util.toThousands(sellIn));
						if(sellIn / targetBaseLine < 0.8) {
							sellInDiv.addClass("not-up");
						} else if (sellIn / targetBaseLine < 1 && sellIn / targetBaseLine > 0.8) {
							sellInDiv.addClass("warning");
						} else {
							sellInDiv.addClass("up");
						}
						$("#actual-q" + quarter).html(sellInDiv);
                        $("#actual-q" + quarter).attr("title",util.toThousands(sellIn));
						//季度实际完成比例
						var percent = "";
						if(targetBaseLine != 0) {
							percent = (sellIn / targetBaseLine * 100).toFixed(0) + "%";
						}
						$("#percent-q" + quarter).html(percent);
					}
					$("#total-sales-target").html(totalSalesTraget + "&nbsp;&nbsp;升");
				});
			},
        	// totalPerformanceInfoGrid 月平均进货量
			loadMontlySales: function() {
				req.getMonthlySellInfo(function(data){
					for(var i in data) {
						var month = data[i].month;
						var sellIn = data[i].sellIn;
						$("#actual-m" + month).html(util.toThousands(sellIn));
						$("#actual-m" + month).attr("title",(util.toThousands(sellIn)));
					}
				});
			},
		    // marketingInfoGrid,每季度成功开发的新维修门店目标
			loadLocalGoal: function() {
				for(var i = 1; i <= 12; i++) {
					$goalSpan = $("#gola-m" + i);
					$goalSpan.empty();
//					if(i <= currentMonth) {
//						$SpanContent = $('<i class="fa fa-check-circle-o right-icon" aria-hidden="true"></i>');
//						$goalSpan.append($SpanContent);
//						$goalSpan.append('完成');
//					}
				}
			},
        initOrganizationSelect: function () {
        // 初始化，机构下拉框
            if(initPartnerName && initPartnerId){
                $('#partnerId').val(initPartnerId).attr('text', initPartnerName);
            }
			organizationCtrl = OrgAutoSelect.init('#partnerId');
		},
        initSearch: function () {
			$("#search").click(function () {
				util.doSearch();
            });
        },
        initYear: function() {
            var thisYear = new Date().getFullYear();
            for (var i = thisYear; i >= 2018; i--) {
                $('#year_c ul')
                    .append($('<li class="'+(i === thisYear?'active':'')+'">'+i+'</li>').click(function() {
                        $(this).siblings().removeClass('active');
                        $(this).addClass('active');
                        // var partnerId = $(".partner-list-wrapper ul li.active").attr("data-id");
                        // loader.initPerformanceTable(partnerId, $(this).html());
                        loader.refreshMonthList($(this).text());
                    }));
            }
            loader.refreshMonthList(thisYear);
        },
        refreshMonthList: function(year) {
            $('#year-mode .monthes-wrapper ul').empty();
            var now = new Date();
            var thisYear = now.getFullYear();
            var maxMonth = 12, thisMonth;
            if (parseInt(year) === thisYear) {
                maxMonth = thisMonth = now.getMonth() + 1;
            }
            // $('#year-mode .monthes-wrapper ul').append($('<li class="" >全年</li>').click(function() {
            //     $(this).siblings().removeClass('active');
            //     $(this).addClass('active');
            //     util.doSearch();
            // }));
            for (var i = 1; i <= maxMonth; i++) {
                $('#year-mode .monthes-wrapper ul')
                    .append($('<li data-month="'+i+'" class="'+(i === thisMonth?'active':'')+'">'+i+'</li>').click(function() {
                        $(this).siblings().removeClass('active');
                        $(this).addClass('active');
                        util.doSearch();
                    }));
            }
        },
        loadScanInfoByWorkshopAndMonthAndSku: function(workshopId,workshopName,month){
            showWorkshopDetailDialog(workshopName,month);
            req.getScanInfoByWorkshopAndMonthAndSku(function (result) {
                var productArray = [];
                var actualValueArray = [];
                if(result && result.length >0){
                    $.each(result,function (index,item) {
                        // monthArray.push(new Date(item.transTime).getMonth() + 1);
                        // var statList = item.statList;
                        actualValueArray.push(item.liters);
                        productArray.push(item.productName);
                    });
                }
                var chartOption = {
                    tooltip : {     //提示框，鼠标悬浮交互时的信息提示
                        trigger: 'axis',
                        axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                            type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                        }
                    },
                    grid: {
                        y2: 155
                    },
                    // title: {
                    //     text: '分sku销量情况',
                    //     left: '2%',
                    //     subtextStyle: {
                    //         color: '#0066b2'
                    //     }
                    // },
                    legend: {
                        data:['销量(L)'],
                        show:true
                    },
                    yAxis: [{
                        type: 'value',
                        axisLabel:{
                            formatter: function(value,index){
                                return value.toFixed(2).toLocaleString() + 'L';
                            }
                            // formatter:'{value} L',
                        }
                    }],
                    xAxis: [{
                        type: 'category',
                        data: productArray,
                        axisLabel:{
                            // interval:0,   // x轴的数据是否一定要全部显示
                            formatter:'{value}',
                            rotate:'45',
                            textStyle:{
                                fontSize:9
                            }
                        }
                    }],
                    series: [
                        {
                            name: '销量(L)',
                            data: actualValueArray,
                            type: 'bar',
                            barMaxWidth: '75',
                            itemStyle: {
                                normal: {
                                    color: 'rgb(0,102,178)',  //柱状图的颜色
                                    formatter: function(a,b,c){
                                        return util.toThousands(a.data.toFixed(2));
                                    }
                                }
                            },
                            label: {
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    textStyle: {
                                        color: 'rgba(255, 255, 255, 1)'
                                    }
                                }
                            }
                        }
                    ]
                };
                detailDealerInfoChart.hideLoading();
                detailDealerInfoChart.setOption(chartOption);
                detailDealerInfoChart.off('click');
            },workshopId,workshopName,month);
        },
	},
	action = {
	};
	
	return {
		init: function() {
		    loader.initSkuQuarterAndYear();
			loader.initOrganizationSelect();
            loader.initSearch();
            // loader.initYear();
            if(initPartnerId && initPartnerName) {
                util.doSearch(initPartnerId);
            }
			// loader.loadMarketIviInfo();
			// loader.loadMontlySales();
			// loader.loadSellThrouth();
			// loader.loadQuarterlySales();
            // loader.loadLocalGoal();
            // loader.loadSysMessage();
//			loader.loadRebateInfo();
//			loader.loadlastYearRebateInfo();
// 			loader.loadCdmPoints();
// 			loader.loadWorkshop();
// 			loader.loadSupportOrderPhone();
		},
		openUrl: function(url, menuId, menuTitle) {
			util.openUrl(url, menuId, menuTitle);
		},
		openMenu: function(url) {
			util.openMenu(url);
		},
		openPage: function(url, type) {
			util.openPage(url, type);
		},
        getSelectedYear: function() {
            var year = util.getSelectedYear() || new Date().getFullYear();
            return year;
        },
        getSelectedMonth: function () {
            var month = util.getSelectedMonth() || new Date().getMonth() + 1;
            return month;
        },
        loadScanInfoByWorkshopAndMonthAndSku:function (workshopId,workshopName,month) {
            loader.loadScanInfoByWorkshopAndMonthAndSku(workshopId,workshopName,month);
       }
	};
	
}($));