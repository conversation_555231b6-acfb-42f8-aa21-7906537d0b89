package com.common.util;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;

import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTUser;
import com.sys.permission.business.PermissionCache;
import com.sys.permission.model.ResPermissionType;
import com.sys.permission.model.WxResourceSql;
import com.sys.permission.model.WxSubjectResourcePermission;
import com.sys.permission.util.ResourceType;
import com.sys.permission.business.PredefineDataPermissionCache;

public class PermissionHelper {
	
	private static Logger log = LoggerFactory.getLogger(PermissionHelper.class);
	
	public static String assemblePermissionClause(String sqlId, String sql, Object params) {
		WxTUser user = null;
		//1. 初始化登录用户
		if(RequestContextHolder.getRequestAttributes() == null){
			//后台线程处理时，参数中带登录用户信息
			if(params instanceof IUnloginPermissionParams){
				user = ((IUnloginPermissionParams)params).getLoginUser();
			}
			if(user == null){
				//非登录处理
				return sql.replace(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER, "");
			}
		}else {
			user = ContextUtil.getCurUser();
		}
		if(user == null){
			return sql.replace(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER, "");
		}
		//2. 替换SQL中的登录用户信息
		sql = replaceVariableInExpression(sql, user);
		if(user.getUserId() == 1){
			//chevronadmin不走数据权限
			return sql.replace(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER, "");
		}
		//3. 初始化权限信息
		PermissionCache permissionCache = user.getPermissionCache();
		if(permissionCache == null){
			return sql.replace(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER, "");
		}
		IPermissionParams permissionParams = null;
		if(params != null && params instanceof IPermissionParams){
			permissionParams = (IPermissionParams)params;
			if(StringUtils.isNotBlank(permissionParams.getResourceId())){
				sqlId += ("-" + permissionParams.getResourceId());
			}
		}
		WxResourceSql resourceSql = permissionCache.getResourceSqlBySqlId(sqlId);
		if (resourceSql == null){
			//未配置数据权限，去掉SQL语句中的权限条件占位字符
			return sql.replace(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER, "");
		}
		//4. 初始化参数中权限设置
		ResourceType resourceType = ResourceType.geResourceTypeByCode(resourceSql.getResourceTypeCode());
		//5. 先根据用户id尝试获取具体的权限规则
		String permissionKey = new StringBuilder().append(WxSubjectResourcePermission.SUBJECT_TYPE_USER).append("-")
				.append(user.getUserId()).append("-").append(resourceSql.getResourceId()).toString();
		WxSubjectResourcePermission permission = permissionCache.getPermissionBySubjectKey(permissionKey);
		StringBuilder permissionExpBuilder = new StringBuilder();
		Set<String> existsPermissions = new HashSet<String>();
		String permissionExpression = permission == null ? null : PredefineDataPermissionCache.getInstance().getPermissionExp(permission.getPermissionTypeId()).getPermissionExpression(user, permissionParams);
		if (permission != null && StringUtils.isNotBlank(permissionExpression)){
			permissionExpBuilder.append(permissionExpression).append(" or ");
			existsPermissions.add(permission.getPermissionTypeId());
		}
		//6. 根据用户所属角色获取权限
		List<WxTRole> roleList = user.getRoleList();
		log.info("user: {}, role list size: {}", user.getUserId(), roleList.size());
		String permissionExp = getPermissionExpressionByRoleList(roleList, resourceSql.getResourceId(), permissionExpBuilder, 
				permissionCache, existsPermissions, user, resourceType, permissionParams);
		//7. 替换权限参数
		permissionExp = processPermissionExpressionParams(permissionExp, user, permissionParams, resourceType);
		if(sql.indexOf(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER) > -1){
			sql = sql.replace(WxSubjectResourcePermission.SQL_EXP_PLACEHOLDER, new StringBuilder(" and (").append(permissionExp).append(")"));
		}else{
			sql = new StringBuilder("select * from ( ").append(sql).append(" ) xx_permission_01 where ").append(permissionExp).toString();
		}
		log.info("found permission expression: {} for sqlId: {}, userId: {}", permissionExp, sqlId, user.getUserId());
		return sql;

	}
	
	public static String processPermissionExpressionParams(String permissionExpresion, WxTUser loginUser, 
			IPermissionParams params, ResourceType resourceType) {
		Map<String, String> paramConfigCondMap = buildParamConfigCondMap(loginUser, params, resourceType);
		boolean replaced;
		do {
			replaced = false;
			for(String paramName : paramConfigCondMap.keySet()) {
				if(permissionExpresion.contains(paramName)) {
					permissionExpresion = permissionExpresion.replace(paramName, paramConfigCondMap.get(paramName));
					replaced = true;
				}
			}
		}while(replaced);
		return permissionExpresion;
	}
	
	private static Map<String, String> buildParamConfigCondMap(WxTUser loginUser, 
			IPermissionParams params, ResourceType resourceType) {
		Map<String, String> paramConfigCondMap = new TreeMap<String, String>();
		paramConfigCondMap.put("${value.loginCai}", loginUser.getCai() == null ? "" : loginUser.getCai());
		paramConfigCondMap.put("${value.loginUserId}", loginUser.getUserId().toString());
		if(WxTUser.USER_MODEL_CHEVRON.equals(loginUser.getUserModel())){
			paramConfigCondMap.put("${010.cond.regionName.cos}", resourceType.getRegionNameCondForCos(params));
			paramConfigCondMap.put("${020.cond.regionName.prm}", resourceType.getRegionNameCondForPrm(params));
			paramConfigCondMap.put("${field.distributorId}", resourceType.getDistributorIdField(params));
			paramConfigCondMap.put("${field.partnerId}", resourceType.getPartnerIdField(params));
			if(params == null || params.isDataIncludeTestSp() != Boolean.FALSE) {
				paramConfigCondMap.put("${cond.excludeTestSp}", new StringBuilder(resourceType.getPartnerIdField(params)).append("!=9").toString());
			}else {
				paramConfigCondMap.put("${cond.excludeTestSp}", "1=1");
			}
			if(params == null || params.getPermissionChannelWeight() == null || (params.getPermissionChannelWeight() & 3) == 3) {
				paramConfigCondMap.put("${030.cond.channelWeight}", "");
				paramConfigCondMap.put("${030.cond.channelWeightFull}", "");
			}else {
				paramConfigCondMap.put("${030.cond.channelWeight}", "and dp_001_cos.channel_weight&" + params.getPermissionChannelWeight() + ">0");
				if((params.getPermissionChannelWeight() & 1) > 0) {
					paramConfigCondMap.put("${030.cond.channelWeightFull}", "and dp_001_cos.product_channel='Consumer'");
				}else if((params.getPermissionChannelWeight() & 2) > 0) {
					paramConfigCondMap.put("${030.cond.channelWeightFull}", "and dp_001_cos.product_channel='Commercial'");
				}
			}
			if(params == null || StringUtils.isBlank(params.getPermissionConfigFun())) {
				paramConfigCondMap.put("${cond.permissionConfigFun}", "");
			}else {
				paramConfigCondMap.put("${cond.permissionConfigFun}", "and dp_001_prm.fun_flag&" + params.getPermissionConfigFun() + ">0");
			}
		}else if(WxTUser.USER_MODEL_SP.equals(loginUser.getUserModel())) {
			paramConfigCondMap.put("${field.workshopId}",  resourceType.getWorkshopIdField(params));
		}else {
			//TODO 其他类型用户未处理
			throw new RuntimeException("用户类型" + loginUser.getUserModel() + "未处理数据权限");
		}
		return paramConfigCondMap;
	}

	private static String getPermissionExpressionByRoleList(List<WxTRole> roleList, Long resourceId, 
			StringBuilder permissionExpBuilder, PermissionCache permissionCache, Set<String> existsPermissions,
			WxTUser loginUser, ResourceType resourceType, IPermissionParams params) {
		for (WxTRole role : roleList) {
			String permissionKey = new StringBuilder().append(WxSubjectResourcePermission.SUBJECT_TYPE_ROLE).append("-")
					.append(role.getRoleId()).append("-").append(resourceId).toString();
			log.info("permissionKey(role): {}", permissionKey);
			WxSubjectResourcePermission permission = permissionCache.getPermissionBySubjectKey(permissionKey);
			if(permission == null) {
				continue;
			}
			ResPermissionType resPermissionType = PredefineDataPermissionCache.getInstance().getPermissionExp(permission.getPermissionTypeId());
			if(resPermissionType == null) {
				continue;
			}
			String sqlExpression = resPermissionType.getPermissionExpression(loginUser, params);
			if (!existsPermissions.contains(permission.getPermissionTypeId())
					&& StringUtils.isNotBlank(sqlExpression)){
				permissionExpBuilder.append(sqlExpression).append(" or ");
				existsPermissions.add(permission.getPermissionTypeId());
			}
		}
		if (permissionExpBuilder.indexOf("or") > 0){
			permissionExpBuilder.delete(permissionExpBuilder.lastIndexOf("or"), permissionExpBuilder.length());
			//添加合伙人过滤
			if(WxTUser.USER_MODEL_SP.equals(loginUser.getUserModel())){
				String filter = resourceType.getSpFilterSql(params, loginUser.getOrgId(), loginUser.getOrgType());
				if(filter != null){
					permissionExpBuilder.insert(0, " and (").insert(0, filter).append(")");
				}
			}
		}else{ //未找到任务权限记录,屏蔽所有数据查询
			log.warn("cound not find permission for resource: {}, will return no data", resourceId);
			permissionExpBuilder.append("1!=1");
		}
		return permissionExpBuilder.toString();
	}

	public static String replaceVariableInExpression(String permissionExp, WxTUser user) {
		String permissionExpWithReplacedValue = permissionExp.replace(WxSubjectResourcePermission.EXP_VAR_CUR_SP_ID, String.valueOf(user.getOrgId()))
			.replace(WxSubjectResourcePermission.EXP_VAR_CUR_USER_ID, String.valueOf(user.getUserId()))
			.replace(WxSubjectResourcePermission.EXP_VAR_CUR_USER_CAI, StringUtils.isNotBlank(user.getCai()) ? "'" + user.getCai() + "'" : "null")
			.replace(WxSubjectResourcePermission.LOGIN_SP_ID_HOLDER, String.valueOf(user.getOrgId()))
			.replace(WxSubjectResourcePermission.LOGIN_USER_ID, String.valueOf(user.getUserId()))
			.replace(WxSubjectResourcePermission.EXT_VAR_CUR_SALES_CHANNEL, StringUtils.isNotBlank(user.getSalesChannel()) ? "'" + user.getSalesChannel() + "'" : "null");
		//log.info("permissionExpWithReplacedValue: {}", permissionExpWithReplacedValue!=null?permissionExpWithReplacedValue.replaceAll("\\t|\\r|\\n", "").replaceAll("\\s{2,}"," "):"");
		return permissionExpWithReplacedValue;
	}
}
