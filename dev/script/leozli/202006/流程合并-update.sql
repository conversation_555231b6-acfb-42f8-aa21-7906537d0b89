
alter table wx_t_workflow_instance add flow_request_no                  nvarchar(64)                                   null;

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'WorkflowInstance.todoPage', 1, 1, '公共流程申请权限。1-乘用车店招申请', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'WorkflowInstance.todoPage', 2, 1, '公共流程申请权限。2-德乐店招申请', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'WorkflowInstance.todoPage', 4, 1, '公共流程申请权限。4-商用油油样申请', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'WorkflowInstance.todoPage', 8, 1, '公共流程申请权限。8-商用油资源申请', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Supplier'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Finance_Signage_Approver'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Signage_Supplier'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Finance_Signage_Approver'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Marketing_Region_Manager'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Marketing_Manager'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Marketing_Manager'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'WorkflowInstance.todoPage', 128, 1, '公共流程申请权限。128-商用油市场资源已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'WorkflowInstance.todoPage', 128, 1, '公共流程申请权限。128-商用油市场资源已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'WorkflowInstance.todoPage', 128, 1, '公共流程申请权限。128-商用油市场资源已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'WorkflowInstance.todoPage', 128, 1, '公共流程申请权限。128-商用油市场资源已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'WorkflowInstance.todoPage', 128, 1, '公共流程申请权限。128-商用油市场资源已办', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Finance_Signage_Approver'), 'WorkflowInstance.allPage', 1, 1, '市场资源申请列表查看权限。1-乘用车店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Finance_Signage_Observer'), 'WorkflowInstance.allPage', 1, 1, '市场资源申请列表查看权限。1-乘用车店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'WorkflowInstance.allPage', 1, 1, '市场资源申请列表查看权限。1-乘用车店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'WorkflowInstance.allPage', 1, 1, '市场资源申请列表查看权限。1-乘用车店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Finance_Signage_Approver'), 'WorkflowInstance.allPage', 2, 1, '市场资源申请列表查看权限。2-商用油店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Finance_Signage_Observer'), 'WorkflowInstance.allPage', 2, 1, '市场资源申请列表查看权限。2-商用油店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'WorkflowInstance.allPage', 2, 1, '市场资源申请列表查看权限。2-商用油店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Marketing_Region_Manager'), 'WorkflowInstance.allPage', 2, 1, '市场资源申请列表查看权限。2-商用油店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Marketing_Manager'), 'WorkflowInstance.allPage', 2, 1, '市场资源申请列表查看权限。2-商用油店招列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'WorkflowInstance.allPage', 4, 1, '市场资源申请列表查看权限。4-商用油油样申请列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Marketing_Region_Manager'), 'WorkflowInstance.allPage', 4, 1, '市场资源申请列表查看权限。4-商用油油样申请列表', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'WorkflowInstance.allPage', 8, 1, '市场资源申请列表查看权限。8-商用油资源包申请列表', 1, getdate());

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('WorkflowInstance.instanceApplyType', 'OIL_SAMPLE_APPLY', '商用油油样申请', '', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('WorkflowInstance.instanceApplyType', 'CIO_MKT_RESOURCE', '商用油资源包申请', '', '1');

update s set s.list_operate_name=null, accept_alias=null from wx_t_workflow_step s where workflow_code='CIO_MKT_RESOURCE' and step_code='ASM_APPROVE'

--select * 
update s set s.biz_permission_weight=s.biz_permission_weight|'2147483648'|'4294967296'
from wx_t_workflow_step s where workflow_code='CDM_APPLY'  --and biz_permission_weight&'2147483648'>0 and biz_permission_weight&'4294967296'>0
and step_code in ('ASM_APPROVE', 'ABM_APPROVE', 'MKT_S_APPROVE', 'MKT_M_APPROVE','ASM_CHECK_DIST_MATERIAL',
'MKT_S_CHECK_DIST_MATERIA',
'ASM_CHECK_DIST_MATERIAL_LOCAL','MKT_S_CHECK_DIST_MATERIA_LOCAL', 'ASM_APPROVE_SIGNAGE','ABM_APPROVE_SIGNAGE',
'MKT_S_APPROVE_SIGNAGE','MKT_M_APPROVE_SIGNAGE','MKT_M_CHECK_DIST_MATERIA',
'MKT_M_CHECK_DIST_MATERIA_LOCAL', 'TEAM_LEADER_APPROVE','TEAM_LEADER_CHECK_DIST_MATERIAL',
'TEAM_LEADER_CHECK_DIST_MATERIAL_LOCAL','TEAM_LEADER_APPROVE_SIGNAGE')

--select * 
update s set s.biz_permission_weight=s.biz_permission_weight|'2097152'
from wx_t_workflow_step s where workflow_code='CDM_APPLY'  --and biz_permission_weight&'2097152'>0 --and biz_permission_weight&'4294967296'>0
and step_code in ('FLSR_CHECK_DIST_MATERIAL_LOCAL',
'ASM_CHECK_DIST_MATERIAL_LOCAL','MKT_S_CHECK_DIST_MATERIA_LOCAL',
'MKT_M_CHECK_DIST_MATERIA_LOCAL',
'TEAM_LEADER_CHECK_DIST_MATERIAL_LOCAL')

update wx_t_workflow set def_sms_notify_temp='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='CDM_APPLY';
update wx_t_workflow set def_sms_notify_temp='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='CI_APPLY';

update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='CIO_MKT_RESOURCE' and step_code='ASM_APPROVE'
update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='CIO_MKT_RESOURCE' and step_code='MKT_APPROVE'
update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='OIL_SAMPLE_APPLY' and step_code='ASM_APPROVE'
update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='OIL_SAMPLE_APPLY' and step_code='MKT_M_APPROVE'
update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='OIL_SAMPLE_APPLY' and step_code='ABM_APPROVE'
update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='OIL_SAMPLE_APPLY' and step_code='TEAM_LEADER_APPROVE'
update wx_t_workflow_step set todo_sms_notify_template='您有新的待审批的市场资源活动申请，请及时审批!' where workflow_code='CIO_MKT_RESOURCE' and step_code='TEAM_LEADER_APPROVE'
