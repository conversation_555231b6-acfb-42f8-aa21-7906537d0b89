insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.productChannel2AuthChannelMap', '非本地营销产品渠道转授权区域渠道映射配置', '非本地营销产品渠道转授权区域渠道映射配置');
--delete d from wx_t_dic_item d where dic_type_code='NonLocalSales.productChannel2AuthChannelMap'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'CDM', '1', '乘用车', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'C&I', '2', '商用油', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2AuthChannelMap', 'OEM', '1', '乘用车', '1',30);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.sku2AuthChannelMap', '非本地营销产品转授权区域渠道映射配置', '非本地营销产品渠道转授权区域渠道映射配置');
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.sku2AuthChannelMap', 'CDM', '1', '乘用车', '1',10);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.productChannel2DisplayTextMap', '非本地营销产品渠道转显示渠道配置', '非本地营销产品渠道转显示渠道配置。备注内为转换后渠道，空表示不转换');
--delete d from wx_t_dic_item d where dic_type_code='NonLocalSales.productChannel2DisplayTextMap'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2DisplayTextMap', 'CDM', '乘用车', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2DisplayTextMap', 'C&I', '商用油', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.productChannel2DisplayTextMap', 'OEM', 'OEM', '', '1',30);

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSales.displayChannel', '非本地营销产品渠道显示配置', '非本地营销产品渠道显示配置。备注内为转换后渠道，空表示不转换');
--delete d from wx_t_dic_item d where dic_type_code='NonLocalSales.displayChannel'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.displayChannel', 'CDM', '乘用车', '', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.displayChannel', 'C&I', '商用油', '', '1',20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.displayChannel', 'OEM', 'OEM', '', '1',30);
--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSales.displayChannel', 'Others', '其他', '', '1',40);

delete v from wx_t_report_view v where v.package_name='nonLocalMkt' and v.view_name='QueryProductByTrackNo'

update mpi1 set mpi1.from_distributor_regions=(select (
		 SELECT ','+convert(varchar(20),rp.region_id) from wx_t_region_partner rp 
		left join wx_t_partner_o2o_enterprise pe1 on pe1.distributor_id=mpi1.from_distributor_id
		where rp.partner_id=pe1.partner_id and rp.channel_weight=pc1.auth_channel
		group by rp.region_id FOR XML PATH,TYPE).value('.','varchar(max)') + ','),
		mpi1.to_distributor_regions=(select (
		 SELECT ','+convert(varchar(20),rp.region_id) from wx_t_region_partner rp 
 		left join wx_t_main_local_marketing mlm1 on mlm1.id=mpi1.report_id
		left join wx_t_partner_o2o_enterprise pe1 on pe1.distributor_id=mlm1.distributor_id
		where rp.partner_id=pe1.partner_id and rp.channel_weight=pc1.auth_channel
		group by rp.region_id FOR XML PATH,TYPE).value('.','varchar(max)') + ',')
from wx_t_loc_mkt_product_info mpi1 
left join (select distinct ip1.sku, isnull(di1.dic_item_name, di2.dic_item_name) auth_channel,
case when di3.dic_item_desc is null or len(di3.dic_item_desc)=0 then ip1.product_channel else di3.dic_item_desc end display_channel 
from wx_t_product ip1
left join wx_t_dic_item di1 on di1.dic_type_code='NonLocalSales.sku2AuthChannelMap' and di1.dic_item_code=ip1.sku
left join wx_t_dic_item di2 on di2.dic_type_code='NonLocalSales.productChannel2AuthChannelMap' and di2.dic_item_code=ip1.product_channel
left join wx_t_dic_item di3 on di3.dic_type_code='productChannel2DisplayTextMap' and di3.dic_item_code=ip1.product_channel
) pc1 on pc1.sku=mpi1.sku
where mpi1.display_channel is null and from_distributor_id is not null;

update mpi1 set mpi1.display_channel=pc1.display_channel, mpi1.auth_channel=isnull(pc1.auth_channel, mpi1.channel)
from wx_t_loc_mkt_product_info mpi1 
left join (select distinct ip1.sku, isnull(di1.dic_item_name, di2.dic_item_name) auth_channel,
case when di3.dic_item_desc is null or len(di3.dic_item_desc)=0 then ip1.product_channel else di3.dic_item_desc end display_channel 
from wx_t_product ip1
left join wx_t_dic_item di1 on di1.dic_type_code='NonLocalSales.sku2AuthChannelMap' and di1.dic_item_code=ip1.sku
left join wx_t_dic_item di2 on di2.dic_type_code='NonLocalSales.productChannel2AuthChannelMap' and di2.dic_item_code=ip1.product_channel
left join wx_t_dic_item di3 on di3.dic_type_code='productChannel2DisplayTextMap' and di3.dic_item_code=ip1.product_channel
) pc1 on pc1.sku=mpi1.sku
where mpi1.display_channel is null 

update pr1 set pr1.attribute3=year(f1.req_time)
from wx_t_non_local_mkt_flow_pro_res pr1
left join wx_t_non_local_mkt_flow_form f1 on pr1.flow_id=f1.id 
where pr1.attribute3 is null

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSale.report.excelColumns', '非本地营销报表导出列配置', '非本地营销报表导出列配置');
--delete d from wx_t_dic_item d where dic_type_code='NonLocalSale.report.excelColumns'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', '非本地营销经销商名称', 'from_distributor_name', '{"width":40}', 1, '10');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', '区域', 'from_region', '', 1, '20');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', '影响渠道', 'channel_text', '', 1, '30');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', 'FLSR', 'from_flsr', '', 1, '40');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', 'ASM', 'from_asm', '', 1, '50');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', '累计被上报次数', 'report_times', '{"align":"RIGHT","dataType":"number"}', 1, '60');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.report.excelColumns', '累计被ABM确认处罚', 'non_local_punish', '{"width":80}', 1, '70');

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('NonLocalSale.UserReport.excelColumns', '非本地营销用户扫码验真导出列配置', '非本地营销用户扫码验真导出列配置');
--delete d from wx_t_dic_item d where dic_type_code='NonLocalSale.UserReport.excelColumns'
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码时间', 'report_time', '', 1, '10');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码经度', 'report_longitude', '{"dataType":"number"}', 1, '20');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码纬度', 'report_latitude', '{"dataType":"number"}', 1, '30');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码省', 'report_province', '', 1, '40');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码市', 'report_city', '', 1, '50');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码年', 'report_year', '{"width":10}', 1, '60');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码月', 'report_month', '{"width":10}', 1, '70');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '扫码日', 'report_day', '{"width":10}', 1, '80');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '产品名称', 'product_name', '{"width":40}', 1, '90');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '产品编号', 'sku', '{"width":15}', 1, '100');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '产品渠道', 'display_channel_text', '{"width":15}', 1, '110');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '产品所属经销商', 'from_distributor_name', '{"width":40}', 1, '120');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '产品所属大区', 'from_region', '{"width":15}', 1, '130');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '产品所属经销商授权区域', 'product_city', '{"width":40}', 1, '140');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '授权区域内扫码', 'local_sale_text', '', 1, '150');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '出库日期', 'out_date', '', 1, '160');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '生产日期', 'product_date', '', 1, '170');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '生产批号', 'product_batch', '', 1, '180');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '出库单号', 'out_order_no', '', 1, '190');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('NonLocalSale.UserReport.excelColumns', '物流码', 'logistics_code', '', 1, '200');
					
update d set d.dic_type_desc='非本地营销特殊合伙人。系统缓存，修改需重启'
from wx_t_dic_type d where dic_type_code='non_local_special_partner'

update ws set ws.step_name='非本地营销上报'
from wx_t_workflow_step ws where ws.workflow_code='NON_LOCK_MKT' and ws.step_code='REQUEST'

INSERT INTO [dbo].[wx_att_file]
           ([source_id]
           ,[file_name]
           ,[storage_name]
           ,[file_type]
           ,[source_type]
           ,[store_path]
           ,[create_time]
           ,[file_size]
           ,[upload_user]
           ,[att_status]
           ,[zt]
           ,[xg_sj]
           ,[xg_user]
           ,[tenant_id]
           ,[uuid]
           ,[passflag]
           ,[ext_flag]
           ,[ext_property1]
           ,[ext_property2]
           ,[ext_property3])
   select
           f.id
           ,a.file_name
           ,a.[storage_name]
           ,a.[file_type]
           ,61 [source_type]
           ,a.[store_path]
           ,a.[create_time]
           ,a.[file_size]
           ,a.[upload_user]
           ,a.[att_status]
           ,a.[zt]
           ,a.[xg_sj]
           ,a.[xg_user]
           ,a.[tenant_id]
           ,a.[uuid]+convert(varchar(20), f.id)
           ,a.[passflag]
           ,a.[ext_flag]
           ,a.[ext_property1]
           ,a.[ext_property2]
           ,a.[ext_property3]
from wx_att_file a 
left join wx_t_non_local_mkt_flow_form f on f.report_id=a.source_id
where a.source_type=53

select * into wx_t_non_local_mkt_emaile_info202207 from wx_t_non_local_mkt_emaile_info
update me1 set me1.warn_info=REPLACE(me1.warn_info, '2021年', '2022年')
from wx_t_non_local_mkt_emaile_info me1 where warn_year=2022 and charindex('2021年', me1.warn_info) > 0

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_GM'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate(), NULL, NULL);


INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_GM'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='cross_territory_email'), 'FLEEING.GOODS', 64, 1, '非本地营销。64-工作台权限', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_GM'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='cross_territory_email'), 'FLEEING.GOODS', 128, 1, '非本地营销。128-汇总tab权限', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_GM'), 'FLEEING.GOODS', 256, 1, '非本地营销。256-邮件发放tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'FLEEING.GOODS', 256, 1, '非本地营销。256-邮件发放tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'FLEEING.GOODS', 256, 1, '非本地营销。256-邮件发放tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'FLEEING.GOODS', 256, 1, '非本地营销。256-邮件发放tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='cross_territory_email'), 'FLEEING.GOODS', 256, 1, '非本地营销。256-邮件发放tab权限', 1, getdate(), NULL, NULL);

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_GM'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Channel_Manager'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Suppervisor'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='cross_territory_email'), 'FLEEING.GOODS', 512, 1, '非本地营销。512-用户扫码验真tab权限', 1, getdate(), NULL, NULL);

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'FLEEING.GOODS', 1024, 1, '非本地营销。1024-经销商权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'FLEEING.GOODS', 1024, 1, '非本地营销。1024-经销商权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'FLEEING.GOODS', 1024, 1, '非本地营销。1024-经销商权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'FLEEING.GOODS', 1024, 1, '非本地营销。1024-经销商权限', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Service_Partner_Admin'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'FLEEING.GOODS', 32, 1, '非本地营销。32-报表权限', 1, getdate());


insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Service_Partner_Manager' and m.menu_id=50373 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Service_Partner_Admin' and m.menu_id=50373 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='CDM_DIST' and m.menu_id=50373 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)
insert into wx_t_rolesource (source_id,role_id, rs_type, add_flag, xg_user,status,tenant_id) select distinct m.menu_id, r.role_id, '2', 1, 1,1,1 from wx_t_menu m left join wx_t_role r on 1=1 where r.ch_role_name='Caltex_Dealer' and m.menu_id=50373 and not exists (select 1 from wx_t_rolesource rs1 where rs1.role_id=r.role_id and rs1.source_id=m.menu_id)

INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Chevron_Cross_Territory_Admin'), 'FLEEING.GOODS', -1, 1, '非本地营销。-1-管理员', 1, getdate(), NULL, NULL);
