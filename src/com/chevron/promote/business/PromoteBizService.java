package com.chevron.promote.business;

import com.chevron.exportdata.Export;
import com.chevron.promote.dao.*;
import com.chevron.promote.model.*;
import com.chevron.promote.model.detail.*;
import com.chevron.promote.model.response.*;
import com.chevron.promote.service.IPromotePlanService;
import com.chevron.promote.service.impl.PromotePackageServiceImpl;
import com.common.util.*;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.file.model.WxAttFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ContextLoader;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Service
public class PromoteBizService extends BasePromote {

    private static Logger log = LoggerFactory.getLogger(PromotePackageServiceImpl.class);
    @Resource
    PromotePlanMapper planMapper;
    @Resource
    IPromotePlanService planService;
    @Resource
    PromoteApplicationBatchMapper applicationBatchMapper;
    @Resource
    PromoteDistributionMapper promoteDistributionMapper;
    @Resource
    WxTUserMapper userMapper;
    @Resource
    ApproveHistoryService approveHisService;
    @Resource
    PromoteXDOpenDetailMapper xdOpenDetailMapper;
    @Resource
    PromoteRoadshowActivityDetailMapper roadshowActivityDetailMapper;
    @Resource
    PromoteSeminarActivityDetailMapper promoteSeminarActivityDetailMapper;
    @Resource
    PromotePlanPacksDetailMapper planPacksDetailMapper;
    @Resource
    PromoteRegionalDetailMapper promoteRegionalDetailMapper;
    @Autowired
    DicService dicService;
    @Resource
    PromoteOrderMapper promoteOrderMapper;
    @Resource
    Export exportExcel;
    @Autowired
    private PromoteShopSignsDetailMapper promoteShopSignsDetailMapper;
    @Autowired
    private PromoteAdvertisementDetailMapper promoteAdvertisementDetailMapper;
    @Autowired
    private PromoteAgricultureDetailMapper promoteAgricultureDetailMapper;
    @Autowired
    private PromoteTryOilDetailMapper promoteTryOilDetailMapper;

    public static Map<String, Object> getStatus() throws Exception {
        Map<String, Object> reqMap = new HashMap<String, Object>();
        List<String> status = new ArrayList<String>();
        status.add(PromoteStatusEnum.STATUS_SAVE.getStatusCode());//暂存
        status.add(PromoteStatusEnum.STATUS_UNSAVE.getStatusCode());//未保存
        status.add(PromoteStatusEnum.STATUS_UNAPPROVED.getStatusCode());//反驳
        reqMap.put("status", status);
        return reqMap;
    }

    public static String doHandlDateEndStr(String dateEndStr) {
        String strs[] = dateEndStr.split("-");
        dateEndStr = DateUtil.getLastDateStrByYearMonth(Integer.parseInt(strs[0]), Integer.parseInt(strs[1]));
        return dateEndStr;
    }

    public CustomerRegionUser getCustomerRegionUserInfo() {
        log.info("PromoteBizService getCustomerRegionUserInfo");
        CustomerRegionUser customerRegionUser = null;
        try {
            customerRegionUser = planService.getCustomerRegionUserV2();
			/*if(BasePromote.PROMOTE_MARKETING.equals(customerRegionUser.getCustomerRoleName()))
			{
				return customerRegionUser;
			}*///delete by bo.liu 180704
            // 获取当前用户负责的区域
            String regionName = customerRegionUser.getRegionName();
            if (null == regionName || regionName.isEmpty()) {
                return customerRegionUser;
            }

            // 根据当前时间，查询时间在所属计划时间段中的计划，若存在多条，取最新(最近)的一条  （获取计划id）
            PromotePlan currentMarketingPlan = getMarketingPlanByRegion(regionName);
            if (BasePromote.PROMOTE_SALES.equals(customerRegionUser.getCustomerRoleName())) {
                //获取分配id，根据接收者是自己，发布时间是（当前时间段），状态为已提交
                PromoteDistribution sourcePromoteDistribution = getSourcePromoteDistributionBySales(customerRegionUser.getUserId(), currentMarketingPlan.getId());
                if (null != sourcePromoteDistribution) {
                    customerRegionUser.setSourceDistributionId(sourcePromoteDistribution.getId());
                }
            }
            // 根据当前时间，获取已经审批了的
            customerRegionUser.setSourcePlanId(currentMarketingPlan.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return customerRegionUser;

    }

    private PromoteDistribution getSourcePromoteDistributionBySales(Long userId, Long sourcePlanId) throws Exception {
        log.info("PromoteBizService getMarketingPlanByRegion");
        Map<String, Object> reqMap = new HashMap<String, Object>();
        reqMap.put("status", BasePromote.PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);//已审核
        reqMap.put("acceptUserId", userId);
        reqMap.put("sourcePlanId", sourcePlanId);
        List<PromoteDistribution> lstSourceDistribution = promoteDistributionMapper.getSourcePromoteDistributionBySales(reqMap);
        if (null == lstSourceDistribution || lstSourceDistribution.isEmpty()) {
            return null;
        }
        return lstSourceDistribution.get(0);
    }

    public PromotePlan getMarketingPlanByRegion(String regionName) throws Exception {
        log.info("PromoteBizService getMarketingPlanByRegion");
        Map<String, Object> reqMap = new HashMap<String, Object>();
        reqMap.put("nowdate", new Date());
        reqMap.put("regionName", regionName);
        List<PromotePlan> lstPlan = planMapper.getPromotePlanByRegion(reqMap);
        if (null == lstPlan || lstPlan.isEmpty()) {
            throw new Exception("拉取数据失败,marketing还没有分配计划");
        }
        return lstPlan.get(0);
    }

    public PromoteApplicationBatch insertPromoteApplicationBatch(String applyTitle, String status, String proKey, Long creatorId) throws Exception {
        PromoteApplicationBatch promoteApplicationBatch = new PromoteApplicationBatch();
        promoteApplicationBatch.setApproveStatus(status);
        if (BasePromote.PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status)) {
            promoteApplicationBatch.setApproveStatus(BasePromote.PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
            promoteApplicationBatch.setVersionFlag("2");//版本2
            promoteApplicationBatch.setCurrentStep(BasePromote.PROMOTE_SUPERVISOR);//第一次流向到小区经理
            doSndMessageForApprove(PROMOTE_SUPERVISOR, null, ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_SUPERVISOR_TIP + PROMOTE_ACTIVITY_APPROVE);//add by bo.liu 180712
            doSndMailForApprove(PROMOTE_SUPERVISOR, null, ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_SUPERVISOR_TIP + PROMOTE_ACTIVITY_APPROVE);
        } else if (BasePromote.PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS.equals(status)) {
            promoteApplicationBatch.setVersionFlag("1");//版本1
            promoteApplicationBatch.setCurrentStep(BasePromote.PROMOTE_SALES);//暂存流向销售  提交
        }
        promoteApplicationBatch.setBatchTitle(applyTitle);
        promoteApplicationBatch.setProkey(proKey);
        promoteApplicationBatch.setCreateTime(new Date());
        promoteApplicationBatch.setCreatorId(creatorId);
        applicationBatchMapper.insertSelective(promoteApplicationBatch);
        if (BasePromote.PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status)) {
            approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, promoteApplicationBatch.getBatchid(), status, "首次提交"
                    , "1.销售提交活动申请");
        }
        return promoteApplicationBatch;
    }

    public List<WxTUser> getChevronSuperManagerInfo(Long partnerId) {
        Map<String, Object> reqMap = new HashMap<String, Object>();
        reqMap.put("partnerId", partnerId);
        List<WxTUser> userInfoLst = userMapper.getChevronSuperManagerInfo(reqMap);
        return userInfoLst;
    }

    public ResponsePromoteXDOpenDetail getPromoteXDOpenDetail(Long activityId) throws Exception {
        List<ResponsePromoteXDOpenDetail> lst = xdOpenDetailMapper.getXDOpenActivityDetail(activityId);
        if (null == lst || lst.isEmpty()) {
            return null;
        }
        return lst.get(0);
    }

    public ResponsePromoteSeminarDetail getSeminarDetail(Long activityId) throws Exception {
        List<ResponsePromoteSeminarDetail> lst = promoteSeminarActivityDetailMapper.getSeminarActivityDetail(activityId);
        if (null == lst || lst.isEmpty()) {
            return null;
        }
        return lst.get(0);
    }

    public ResponsePromoteRoadShowDetail getRoadShowDetail(Long activityId) throws Exception {
        List<ResponsePromoteRoadShowDetail> lst = roadshowActivityDetailMapper.getRoadShowActivityDetail(activityId);
        if (null == lst || lst.isEmpty()) {
            return null;
        }
        return lst.get(0);
    }

    public ResponsePromoteShopSignsDetail getShopSignsDetail(Long activityId) {
        List<ResponsePromoteShopSignsDetail> lst = promoteShopSignsDetailMapper.getShopSignsActivityDetail(activityId);
        if (EmptyChecker.isEmpty(lst)) {
            return null;
        }
        return lst.get(0);
    }

    public ResponsePromoteAdvertisementDetail getAdvertisementDetail(Long activityId) {
        List<ResponsePromoteAdvertisementDetail> lst = promoteAdvertisementDetailMapper.getActivityDetail(activityId);
        if (EmptyChecker.isEmpty(lst)) {
            return null;
        }
        return lst.get(0);
    }

    public ResponsePromoteAgricultureDetail getAgricultureDetail(Long activityId) {
        List<ResponsePromoteAgricultureDetail> lst = promoteAgricultureDetailMapper.getActivityDetail(activityId);
        if (EmptyChecker.isEmpty(lst)) {
            return null;
        }
        return lst.get(0);
    }

    public ResponsePromoteTryOilDetail getTryOilDetail(Long activityId) {
        List<ResponsePromoteTryOilDetail> lst = promoteTryOilDetailMapper.getActivityDetail(activityId);
        if (EmptyChecker.isEmpty(lst)) {
            return null;
        }
        return lst.get(0);
    }

    public ResponseActivityPacksDetail getPacksDetailInfo(String filterPacks, Long sourcePlanId) throws Exception {

        Map<String, Object> reqMap = new HashMap<String, Object>();
        reqMap.put("AttSourceType", WxAttFile.SourceType_PromoteActivity);
        reqMap.put("sourcePlanId", sourcePlanId);
        if (null != filterPacks && !filterPacks.isEmpty()) {
            reqMap.put("filterPacks", filterPacks);
        }
        List<ResponsePacksDetail> lst = planPacksDetailMapper.queryActivityPacksDetailBySourcePlanId(reqMap);
        String lastPacksCode = "";
        String currentPacksCode = "";
        List<ResponsePacksDetail> tmpLst = null;
        Map<String, Object> tmpMap = new HashMap<String, Object>();
        ResponseActivityPacksDetail responseActivityPacks = new ResponseActivityPacksDetail();
        ResponsePacksDetail workClothes = null;//工作服
        for (ResponsePacksDetail tmp : lst) {
            currentPacksCode = tmp.getPacksCode();
            String flag = tmp.getMaterialsFlag();
            if (WORKCLOTHES_FLAG.equals(flag)) {
                workClothes = tmp;
                if (PROMOTE_ACTIVITY_RDLB.equals(currentPacksCode)) {
                    responseActivityPacks.setRdWorkClothes(WORKCLOTHES_TRUE);
                } else if (PROMOTE_ACTIVITY_GDLB.equals(currentPacksCode)) {
                    responseActivityPacks.setGdWorkClothes(WORKCLOTHES_TRUE);
                } else if (PROMOTE_ACTIVITY_XFZHDBCB.equals(currentPacksCode)) {
                    responseActivityPacks.setConsumerInteractionWorkClothes(WORKCLOTHES_TRUE);
                } else if (PROMOTE_ACTIVITY_LYWLB.equals(currentPacksCode)) {
                    responseActivityPacks.setRoadshowWorkClothes(WORKCLOTHES_TRUE);
                } else if (PROMOTE_ACTIVITY_TGHWLB.equals(currentPacksCode)) {
                    responseActivityPacks.setYthWorkClothes(WORKCLOTHES_TRUE);
                }
            }
            if (!lastPacksCode.equals(currentPacksCode)) {
                tmpLst = new ArrayList<ResponsePacksDetail>();

            }
            tmpLst.add(tmp);
            tmpMap.put(currentPacksCode, tmpLst);
            lastPacksCode = currentPacksCode;
        }
        //新店开业
        responseActivityPacks.setGdPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_GDLB));
        responseActivityPacks.setRdPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_RDLB));
        responseActivityPacks.setConsumerInteractionPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_XFZHDBCB));
        responseActivityPacks.setRoadshowPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_LYWLB));
        responseActivityPacks.setYthPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_TGHWLB));
        responseActivityPacks.setYzythPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_YZYTHLB));
        responseActivityPacks.setDzPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_DZLB));
        responseActivityPacks.setHwggPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_HWGGLB));
        responseActivityPacks.setNkPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_NKLB));
        responseActivityPacks.setSyPacksLst((List<ResponsePacksDetail>) tmpMap.get(PROMOTE_ACTIVITY_SYLB));

        //发现有一种包存在工作服，那么就去获取工作服的尺寸
        if (null != workClothes) {
            //1.获取工作服
            ResponseWorkClothes responseWorkClothes = new ResponseWorkClothes();
            responseWorkClothes.setMaterialCode(workClothes.getMaterialsName());
            responseWorkClothes.setMaterialDescrip(workClothes.getMaterialsSpecification());
            responseWorkClothes.setMaterialName(workClothes.getMaterialsName());
            responseWorkClothes.setMaterialId(workClothes.getMaterialsId());
            //responseWorkClothes.setMaterialPrice(workClothes.getMaterialsPrice());
            //responseWorkClothes.setMaterialTotalAmount(workClothes.getMaterialsTotalAmount());

            //2.从数据字段获取尺寸
            List<ResponseWorkClothesSize> lstRe = new ArrayList<ResponseWorkClothesSize>();
            Map<String, Object> promoteMarketingPower = dicService.getDicItemByDicTypeCode("promote.workclothes.size");
            List<DicItemVo> promoteMarketingItemlist = (ArrayList) promoteMarketingPower.get("data");
            for (DicItemVo dicItem : promoteMarketingItemlist) {
                ResponseWorkClothesSize re = new ResponseWorkClothesSize();
                re.setCode(dicItem.getDicItemCode());
                re.setName(dicItem.getDicItemName());
                lstRe.add(re);
            }
            responseWorkClothes.setLstWorkClothesSize(lstRe);
            responseActivityPacks.setResponseWorkClothes(responseWorkClothes);

        }
        return responseActivityPacks;
    }

    public WxTUser getCurrentSalesInfo(Long userId) {
        WxTUser tuser = userMapper.selectByPrimaryKey(userId);
        return tuser;
    }

    public PromoteXDOpenDetail getHasApplyXDOpenPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteXDOpenDetail xdOpenDetail = xdOpenDetailMapper.getHasApplyXDOpenPacks(reqMap);
        return xdOpenDetail;
    }

    public PromoteRoadshowActivityDetail getHasApplyRoadshowPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteRoadshowActivityDetail promoteRoadshowActivityDetail = roadshowActivityDetailMapper.getHasApplyRoadshowPacks(reqMap);
        return promoteRoadshowActivityDetail;
    }

    public PromoteSeminarActivityDetail getHasApplySeminarPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteSeminarActivityDetail promoteSeminarActivityDetail = promoteSeminarActivityDetailMapper.getHasApplyPacks(reqMap);
        return promoteSeminarActivityDetail;
    }

    public PromoteRegionalDetail getHasApplyPoints(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteRegionalDetail promoteRegionalDetail = promoteRegionalDetailMapper.getHasApplyPoints(reqMap);
        return promoteRegionalDetail;
    }

    public PromoteShopSignsDetail getHasApplyDzPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteShopSignsDetail detail = promoteShopSignsDetailMapper.getHasApplyPacks(reqMap);
        return detail;
    }

    public PromoteAdvertisementDetail getHasApplyHwggPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteAdvertisementDetail detail = promoteAdvertisementDetailMapper.getHasApplyPacks(reqMap);
        return detail;
    }

    public PromoteAgricultureDetail getHasApplyNkPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteAgricultureDetail detail = promoteAgricultureDetailMapper.getHasApplyPacks(reqMap);
        return detail;
    }

    public PromoteTryOilDetail getHasApplySyPacks(Map<String, Object> reqMap) throws Exception {
        reqMap.putAll(getStatus());
        PromoteTryOilDetail detail = promoteTryOilDetailMapper.getHasApplyPacks(reqMap);
        return detail;
    }

    public Double getActivityAmountLimit() throws Exception {
        Double activityAmount = BasePromote.PROMOTE_ACTIVITY_AMOUNT;
        Map<String, Object> activityAmountResult = dicService.getDicItemByDicTypeCode("promote.activity.amount.limit");
        if (null == activityAmountResult || activityAmountResult.isEmpty()) {
            return activityAmount;
        }
        List<DicItemVo> activityAmountlist = (ArrayList) activityAmountResult.get("data");
        for (DicItemVo dicItem : activityAmountlist) {
            if (dicItem.getDicItemCode().equals("activity.amount")) {
                activityAmount = Double.parseDouble(dicItem.getDicItemName());
                break;
            }
        }
        return activityAmount;
    }

    public void doSndMobile(List<WxTUser> lstUsers, String mobileMessageContent) {
        try {
            for (WxTUser tuser : lstUsers) {
                String mobile = tuser.getMobileTel();
                //add by bo.liu 180720
                String[] strs = getSndMessageDebug();
                if (null != strs) {
                    if ("true".equals(strs[0])) {
                        mobile = strs[1];
                    }
                }
                if (null != mobile && !mobile.isEmpty()) {
                    log.info("doSndMobile mobile:" + mobile);
                    SendMessageUtil.sndMessageToPhone(mobile, mobileMessageContent);
                } else {
                    log.info("doSndMobile is null,user:" + tuser.getLoginName());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("doSndMobile" + e.getLocalizedMessage());
        }

    }

    public void doSndMessageForApprove(String userType, Long userId, String bizType, String mobielMessageContent) {
        log.info("doSndMessageForApprove userType:{},userId:{},bizType:{},mobielMessageContent:{}", userType, userId, bizType, mobielMessageContent);
        try {
            CustomerRegionUser customerRegionUser = getCustomerRegionUserInfo();
            //根据当前用户的区域，获取当前用户的marketing
            String reginName = customerRegionUser.getRegionName();
            Map<String, Object> reqMap = new HashMap<String, Object>();
            reqMap.put("currentUserId", customerRegionUser.getUserId());
            String mobile = "";
            if (PROMOTE_MARKETING.equals(userType)) {
                /*reqMap.put("regionName", reginName);
                List<WxTUser> lstUser = userMapper.getMarketingMobile(reqMap);
                if (null == lstUser || lstUser.isEmpty()) {
                    log.info("doSndMessageForApprove PROMOTE_MARKETING user is null");
                    return;
                }
                if (null == lstUser.get(0)) {
                    log.info("doSndMessageForApprove PROMOTE_MARKETING user is null");
                    return;
                }
                mobile = lstUser.get(0).getMobileTel();*/

                //liyu
                String regionCode = null;
                if (StringUtils.equals("北区", reginName)) {
                    regionCode = "north";
                } else if (StringUtils.equals("南区", reginName)) {
                    regionCode = "south";
                } else {
                    regionCode = "global";
                }

                DicItemVo dicItemVo = dicService.getDictItem("promote.marketing.notification", regionCode);
                String loginName = dicItemVo != null ? dicItemVo.getDicItemName() : "";
                WxTUser user = userMapper.findUserByLoginName(loginName);
                if (null == user) {
                    log.info("doSndMessageForApprove PROMOTE_MARKETING user is null");
                    return;
                }
                mobile = user.getMobileTel();
            } else {
                if (ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION.equals(bizType)) {
                    mobile = doGetMobileForActivityApprove(userType, reqMap, userId);

                } else if (ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION.equals(bizType)) {
                    mobile = doGetMobileForDistributionApprove(userType, reqMap, userId);
                }
            }

            //add by bo.liu 180720
            String[] strs = getSndMessageDebug();
            if (null != strs) {
                if ("true".equals(strs[0])) {
                    mobile = strs[1];
                }
            }

            if (null == mobile || "".equals(mobile)) {
                log.info("doSndMessageForApprove mobile is null");
                return;
            }
            SendMessageUtil.sndMessageToPhone(mobile, mobielMessageContent);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("doSndMessageForApprove" + e.getLocalizedMessage());
        }
    }

    private String doGetMobileForDistributionApprove(String userType, Map<String, Object> reqMap, Long userId) {
        String mobile = "";
        if (PROMOTE_SUPERVISOR.equals(userType)) {
            if (null == userId) {
                log.info("doGetMobileForDistributionApprove PROMOTE_SUPERVISOR userId is null");
                return mobile;
            }
            WxTUser tuser = userMapper.selUserByUserid(userId);
            if (null == tuser) {
                log.info("doGetMobileForDistributionApprove PROMOTE_SUPERVISOR tuser is null");
                return mobile;
            }
            mobile = tuser.getMobileTel();

        }
        return mobile;
    }

    private String doGetMobileForActivityApprove(String userType,
                                                 Map<String, Object> reqMap, Long userId) {
        String mobile = "";
        if (PROMOTE_SALES.equals(userType)) {
            if (null == userId) {
                log.info("doGetMobileForActivityApprove PROMOTE_SALES userId is null");
                return mobile;
            }
            WxTUser tuser = userMapper.selUserByUserid(userId);
            if (null == tuser) {
                log.info("doGetMobileForActivityApprove PROMOTE_SALES tuser is null");
                return mobile;
            }
            mobile = tuser.getMobileTel();

        } else if (PROMOTE_SUPERVISOR.equals(userType))//小区经理
        {
            //获取当前用户的小区经理
			/*reqMap.put("currentUserCai", customerRegionUser.getCai());
			List<WxTUser> lstUser0 = userMapper.getSupervisorUser(reqMap);*/
            List<WxTUser> lstUser0 = userMapper.getChannelManagerOrSupervisorUser(reqMap);
            if (null == lstUser0 || lstUser0.isEmpty()) {
                log.info("doGetMobileForActivityApprove PROMOTE_SUPERVISOR user is null");
                return mobile;
            }
            mobile = lstUser0.get(0).getMobileTel();

        } else if (PROMOTE_CHANNELMANAGER.equals(userType)) {
            List<WxTUser> lstUser1 = userMapper.getChannelManagerOrSupervisorUser(reqMap);
            if (null == lstUser1 || lstUser1.isEmpty()) {
                log.info("doGetMobileForActivityApprove PROMOTE_CHANNELMANAGER user is null");
                return mobile;
            }
            mobile = lstUser1.get(0).getMobileTel();

        }
        return mobile;
    }

    public String[] getSndMessageDebug() throws Exception {
        String[] strs = new String[2];
        Map<String, Object> resultLst = dicService.getDicItemByDicTypeCode("promote.sndmessage.debug");
        List<DicItemVo> itemLst = (ArrayList) resultLst.get("data");
        for (DicItemVo dicItem : itemLst) {
            if (dicItem.getDicItemCode().equals("debug")) {
                strs[0] = dicItem.getDicItemName();
                continue;
            } else if (dicItem.getDicItemCode().equals("mobile")) {
                strs[1] = dicItem.getDicItemName();
                continue;
            }
        }
        return strs;
    }

    //TODO 发送审批的邮件
    public void doSndMailForApprove(String userType, Long userId, String bizType, String mobielMessageContent) {
        log.info("doSndMailForApprove userType:{},userId:{},bizType:{},mobielMessageContent:{}", userType, userId, bizType, mobielMessageContent);
        try {
            CustomerRegionUser customerRegionUser = getCustomerRegionUserInfo();
            //根据当前用户的区域，获取当前用户的marketing
            String reginName = customerRegionUser.getRegionName();
            Map<String, Object> reqMap = new HashMap<String, Object>();
            reqMap.put("currentUserId", customerRegionUser.getUserId());
            WxTUser user = new WxTUser();
            if (PROMOTE_MARKETING.equals(userType)) {
                /*reqMap.put("regionName", reginName);
                List<WxTUser> lstUser = userMapper.getMarketingMobile(reqMap);
                if (null == lstUser || lstUser.isEmpty()) {
                    log.info("doSndMailForApprove PROMOTE_MARKETING user is null");
                    return;
                }
                if (null == lstUser.get(0)) {
                    log.info("doSndMailForApprove PROMOTE_MARKETING user is null");
                    return;
                }
                user = lstUser.get(0);*/

                //liyu
                String regionCode = null;
                if (StringUtils.equals("北区", reginName)) {
                    regionCode = "north";
                } else if (StringUtils.equals("南区", reginName)) {
                    regionCode = "south";
                } else {
                    regionCode = "global";
                }

                DicItemVo dicItemVo = dicService.getDictItem("promote.marketing.notification", regionCode);
                String loginName = dicItemVo != null ? dicItemVo.getDicItemName() : "";
                user = userMapper.findUserByLoginName(loginName);
                if (null == user) {
                    log.info("doSndMessageForApprove PROMOTE_MARKETING user is null");
                    return;
                }
            } else {
                if (ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION.equals(bizType)) {
                    user = doGetMailForActivityApprove(userType, reqMap, userId);

                } else if (ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION.equals(bizType)) {
                    user = doGetMailForDistributionApprove(userType, reqMap, userId);
                }
            }

            if (EmptyChecker.isEmpty(user)) {
                log.info("doSndMailForApprove mobile is null");
                return;
            }

            String acceptEmail[] = new String[1];
            acceptEmail[0] = user.getEmail();

            String[] strs = getSndMailDebug();
            if (null != strs) {
                if ("true".equals(strs[0])) {
                    acceptEmail[0] = strs[1];
                }
            }

            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("userName", user.getChName());
            dataMap.put("content", mobielMessageContent);

            String subjectName = "德乐市场资源活动申请审核状态变更提醒";

            ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            EmailSendUtils.sendEmailForListContent(context, acceptEmail, null, subjectName, dataMap, null, PROMOTE_MAIL_TEMPLATE);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("doSndMailForApprove" + e.getLocalizedMessage());
        }
    }

    private WxTUser doGetMailForDistributionApprove(String userType, Map<String, Object> reqMap, Long userId) {
        WxTUser user = null;
        if (PROMOTE_SUPERVISOR.equals(userType)) {
            if (null == userId) {
                log.info("doGetMailForDistributionApprove PROMOTE_SUPERVISOR userId is null");
                return user;
            }
            WxTUser tuser = userMapper.selUserByUserid(userId);
            if (null == tuser) {
                log.info("doGetMailForDistributionApprove PROMOTE_SUPERVISOR tuser is null");
                return user;
            }
            user = tuser;
        }
        return user;
    }

    private WxTUser doGetMailForActivityApprove(String userType, Map<String, Object> reqMap, Long userId) {
        WxTUser user = null;
        if (PROMOTE_SALES.equals(userType)) {
            if (null == userId) {
                log.info("doGetMailForActivityApprove PROMOTE_SALES userId is null");
                return user;
            }
            WxTUser tuser = userMapper.selUserByUserid(userId);
            if (null == tuser) {
                log.info("doGetMailForActivityApprove PROMOTE_SALES tuser is null");
                return user;
            }
            user = tuser;

        } else if (PROMOTE_SUPERVISOR.equals(userType))//小区经理
        {
            //获取当前用户的小区经理
			/*reqMap.put("currentUserCai", customerRegionUser.getCai());
			List<WxTUser> lstUser0 = userMapper.getSupervisorUser(reqMap);*/
            List<WxTUser> lstUser0 = userMapper.getChannelManagerOrSupervisorUser(reqMap);
            if (null == lstUser0 || lstUser0.isEmpty()) {
                log.info("doGetMailForActivityApprove PROMOTE_SUPERVISOR user is null");
                return null;
            }
            user = lstUser0.get(0);

        } else if (PROMOTE_CHANNELMANAGER.equals(userType)) {
            List<WxTUser> lstUser1 = userMapper.getChannelManagerOrSupervisorUser(reqMap);
            if (null == lstUser1 || lstUser1.isEmpty()) {
                log.info("doGetMailForActivityApprove PROMOTE_CHANNELMANAGER user is null");
                return null;
            }
            user = lstUser1.get(0);

        }
        return user;
    }

    public String[] getSndMailDebug() throws Exception {
        String[] strs = new String[2];
        Map<String, Object> resultLst = dicService.getDicItemByDicTypeCode("promote.sndmessage.debug");
        List<DicItemVo> itemLst = (ArrayList) resultLst.get("data");
        for (DicItemVo dicItem : itemLst) {
            if (dicItem.getDicItemCode().equals("debug")) {
                strs[0] = dicItem.getDicItemName();
                continue;
            } else if (dicItem.getDicItemCode().equals("email")) {
                strs[1] = dicItem.getDicItemName();
                continue;
            }
        }
        return strs;
    }

    /**
     * 导出所有订单
     *
     * @param request
     * @param response
     * @return
     * <AUTHOR> 2018-9-5 上午11:19:47
     */
    public Map<String, Object> exportAllPromoteOrder(Map<String, Object> reqMap,
                                                     HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            //查询导出的订单数据，排序，并组装
            List<ExportPromoteOrder> lst = promoteOrderMapper.exportOrder(reqMap);
            lst = doHandelLst(lst);
            //导出数据
            exportExcel.exportPromoteOrderData(lst, "com.chevron.promote.model.ExportPromoteOrder", response, "地促订单报表", "地促订单", request);
            resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("exportAllPromoteOrder error:" + e.getLocalizedMessage());
            resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
            resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG + e.getMessage());
        }
        return resultMap;
    }

    private List<ExportPromoteOrder> doHandelLst(List<ExportPromoteOrder> lst) throws Exception {
        //获取发货方配置
        Map<String, String> senderMap = doGetSnderInfo();
        String lastOrderNo = "";
        String lastPacksName = "";
        int lastPackageNumber = 0;
        ExportPromoteOrder lastPromoteOrder = null;
        List<ExportPromoteOrder> lstNews = new ArrayList<ExportPromoteOrder>();
        boolean isOils = false;
        for (ExportPromoteOrder tmpOrder : lst) {
            String orderNo = tmpOrder.getOrderNo();
            String packageName = tmpOrder.getMateralPackageName();
            String productNo = tmpOrder.getProductNo();
            int currentPackageNumber = tmpOrder.getPackageNumber();
            if ((!lastOrderNo.equals(orderNo) || !lastPacksName.equals(packageName)) && currentPackageNumber != 0) {
                lastPackageNumber = currentPackageNumber;
            }
            tmpOrder.setPackageNumber(lastPackageNumber);
            if (productNo.startsWith(PRODUCT_START_M)) {
                tmpOrder.setSender(senderMap.get(PROMOTE_ORDER_SENDER_M));
            } else if (productNo.equals(DELO_WORK_CLOTHES)) {
                tmpOrder.setSender(senderMap.get(PROMOTE_ORDER_SENDER_DELOWORKCLOTHES));
            } else if (oilS.contains(productNo)) {
                isOils = true;
                tmpOrder.setSender(senderMap.get(PROMOTE_ORDER_SENDER_OIL));
            } else {
                tmpOrder.setSender(senderMap.get(PROMOTE_ORDER_SENDER_NORMAL));
            }
            lastOrderNo = orderNo;
            lastPacksName = packageName;
            if (!isOils) {
                if (null != lastPromoteOrder) {
                    //存在一种汽机油
                    lstNews.add(lastPromoteOrder);
                }
                lstNews.add(tmpOrder);
                lastPromoteOrder = null;
            } else {
                if (null == lastPromoteOrder) {
                    lastPromoteOrder = tmpOrder;
                } else {
                    //存在两种汽机油
                    lastPromoteOrder.setPackageNumber(lastPromoteOrder.getProductNumber() + tmpOrder.getProductNumber());
                    tmpOrder.setPackageNumber(lastPromoteOrder.getProductNumber() + tmpOrder.getProductNumber());
                    lstNews.add(lastPromoteOrder);
                    lstNews.add(tmpOrder);
                    lastPromoteOrder = null;
                }
                isOils = false;
            }

        }
        return lstNews;
    }

    private Map<String, String> doGetSnderInfo() throws Exception {
        Map<String, String> returnMap = new HashMap<String, String>();
        Map<String, Object> snderConfigInfo = dicService.getDicItemByDicTypeCode("promote.order.sender");
        List<DicItemVo> snderConfigInfoLst = (ArrayList) snderConfigInfo.get("data");
        for (DicItemVo dicItem : snderConfigInfoLst) {
            returnMap.put(dicItem.getDicItemCode(), dicItem.getDicItemName());
        }
        return returnMap;
    }

    public Map<String, Object> getQueryPromoteOrderMap(Map<String, Object> reqMap, String keyWord, String dateStartStr, String dateEndStr) {
        //合伙人超级管理
        WxTUser tuser = ContextUtil.getCurUser();
        log.info("getQueryPromoteOrderMap regionName:{}", tuser.getRegionName());
        String regionName = tuser.getRegionName();
        if (null != regionName && !regionName.isEmpty()) {
            reqMap.put("regionName", regionName);
        }
        reqMap.put("keyWord", keyWord);
        if (null != dateStartStr && !dateStartStr.isEmpty()) {
            reqMap.put("startTimeStr", dateStartStr + "-01");
        }
        if (null != dateEndStr && !dateEndStr.isEmpty()) {
            dateEndStr = doHandlDateEndStr(dateEndStr);
            reqMap.put("endTimeStr", dateEndStr);
        }

        return reqMap;
    }
}
