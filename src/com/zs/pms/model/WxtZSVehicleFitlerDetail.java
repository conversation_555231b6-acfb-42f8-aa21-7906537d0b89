package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleFitlerDetail {
    private Long id;

    private String tModelId;

    private String filterName;

    private String filterPartsNo;

    private String filterPurchasePrice;

    private String filterSalePrice;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getFilterName() {
        return filterName;
    }

    public void setFilterName(String filterName) {
        this.filterName = filterName == null ? null : filterName.trim();
    }

    public String getFilterPartsNo() {
        return filterPartsNo;
    }

    public void setFilterPartsNo(String filterPartsNo) {
        this.filterPartsNo = filterPartsNo == null ? null : filterPartsNo.trim();
    }

    public String getFilterPurchasePrice() {
        return filterPurchasePrice;
    }

    public void setFilterPurchasePrice(String filterPurchasePrice) {
        this.filterPurchasePrice = filterPurchasePrice == null ? null : filterPurchasePrice.trim();
    }

    public String getFilterSalePrice() {
        return filterSalePrice;
    }

    public void setFilterSalePrice(String filterSalePrice) {
        this.filterSalePrice = filterSalePrice == null ? null : filterSalePrice.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}