1. 分销商保存
/partnerController/save.do
{"partnerName":"分销商A","orgType":3,"extProperty1":56363,"regionPartnerVos":[{"regionId":440604}]}
orgType=3 分销商
extProperty1= 所属经销商
regionPartnerVos 负责区域
2. 分销商详情接口
{"jsonrpc":"2.0","method":"partnerServiceImpl.getPartnerInfoById","params":[56713],"id":2}
3. 分销商列表
/partnerController/retailer/data.do?limit=10&queryType=1&resourceId=retailer
extProperty1 所属经销商ID
partnerId 分销商ID
partnerName 分销商名称
4. 取消合作
{"jsonrpc":"2.0","method":"partnerServiceImpl.disableOrganization","params":[56474],"id":2}

5. 积分及下单礼品促销兑换 列表
返回：resultLst[i].pointInfo[0].retailerFlag 是否分销商标记 1-分销商
6. 积分划拨
/pointAdjust/movePoints.do?fromPartnerId=56363&fromPartnerName=fs&toPartnerId=56846&toPartnerName=fxsb&pointType=CDM_STOCK_POINT&points=10&comments=test
参数：
	fromPartnerId 被划拨经销商
	fromPartnerName 被划拨经销商名称
	toPartnerId 划拨进经销商ID
	toPartnerName 划拨进经销商名称
	pointType 划拨积分类型
	points 划拨积分数
	comments 备注（必填）