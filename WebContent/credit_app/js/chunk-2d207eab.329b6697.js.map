{"version": 3, "sources": ["webpack:///./src/views/credit/apply/cv/submit.vue?8bc0", "webpack:///src/views/credit/apply/cv/submit.vue", "webpack:///./src/views/credit/apply/cv/submit.vue?7d6d", "webpack:///./src/views/credit/apply/cv/submit.vue"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "title", "id", "staticRenderFns", "submitvue_type_script_lang_js_", "name", "components", "TitlePiece", "Basic", "cv", "Buttons", "_pieces_button", "Upload", "upload", "History", "review_history", "data", "$route", "query", "fromPage", "formVersionNo", "lockerId", "created", "_this", "params", "creditType", "payload", "$store", "dispatch", "then", "_ref", "_ref2", "Object", "slicedToArray", "status", "$router", "replace", "cv_submitvue_type_script_lang_js_", "component", "componentNormalizer", "__webpack_exports__"], "mappings": "uHAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAAA,EAAA,eAAmCE,MAAA,CAAOC,MAAA,4BAAmC,CAAAH,EAAA,WAAgBE,MAAA,CAAOE,GAAAR,EAAAQ,OAAa,GAAAJ,EAAA,SAAAA,EAAA,UAAAA,EAAA,gBAC1MK,EAAA,uFCiBAC,EAAA,CACAC,KAAA,yBACAC,WAAA,CACAC,WAAAN,EAAA,KACAO,MAAAC,EAAA,KACAC,QAAAC,EAAA,KACAC,OAAAC,EAAA,KACAC,QAAAC,EAAA,MAEAC,KATA,WAUA,OACAd,GAAAP,KAAAsB,OAAAC,MAAAhB,GACAiB,SAAAxB,KAAAsB,OAAAC,MAAAC,SACAC,cAAAzB,KAAAsB,OAAAC,MAAAE,cACAC,SAAA1B,KAAAsB,OAAAC,MAAAG,WAGAC,QAjBA,WAiBA,IAAAC,EAAA5B,KACA6B,EAAA,CAAAC,WAAA,cACA,GAAA9B,KAAAO,GAAA,CACA,IAAAwB,EAAA,CACAxB,GAAAP,KAAAO,GACAiB,SAAAxB,KAAAwB,SACAE,SAAA1B,KAAA0B,SAAA1B,KAAA0B,SAAA,IAEA1B,KAAAgC,OAAAC,SAAA,iBAAAF,QAEA/B,KAAAgC,OACAC,SAAA,mBAAAJ,GACAK,KAAA,SAAAC,GAAA,IAAAC,EAAAC,OAAAC,EAAA,KAAAD,CAAAF,EAAA,GAAAI,EAAAH,EAAA,GAAAf,EAAAe,EAAA,GACA,IAAAG,EAAA,SACAlB,OAAAd,KAAAqB,EAAArB,KACAqB,EAAArB,GAAAc,OAAAd,GACAqB,EAAAY,QAAAC,QAAA,wBAAAb,EAAArB,SCnD6XmC,EAAA,cCO7XC,EAAgBN,OAAAO,EAAA,KAAAP,CACdK,EACA5C,EACAU,GACF,EACA,KACA,KACA,MAIeqC,EAAA,WAAAF", "file": "js/chunk-2d207eab.329b6697.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('title-piece',{attrs:{\"title\":\"CV Request Form 特殊放单申请表\"}},[_c('buttons',{attrs:{\"id\":_vm.id}})],1),_c('basic'),_c('upload'),_c('history')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <title-piece title=\"CV Request Form 特殊放单申请表\">\r\n      <buttons :id=\"id\" />\r\n    </title-piece>\r\n    <basic />\r\n    <upload />\r\n    <history />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TitlePiece from '../_pieces/title'\r\nimport Basic from '../_pieces/basic/cv'\r\nimport Buttons from '../_pieces/button'\r\nimport Upload from '../_pieces/upload'\r\nimport History from '../_pieces/review-history'\r\n\r\nexport default {\r\n  name: 'credit-apply-cv-submit',\r\n  components: {\r\n    TitlePiece,\r\n    Basic,\r\n    Buttons,\r\n    Upload,\r\n    History\r\n  },\r\n  data() {\r\n    return {\r\n      id: this.$route.query.id,\r\n      fromPage: this.$route.query.fromPage,\r\n      formVersionNo: this.$route.query.formVersionNo,\r\n      lockerId: this.$route.query.lockerId\r\n    }\r\n  },\r\n  created() {\r\n    let params = { creditType: 'CV_REQUEST' }\r\n    if (this.id) {\r\n      const payload = {\r\n        id: this.id,\r\n        fromPage: this.fromPage,\r\n        lockerId: this.lockerId ? this.lockerId : ''\r\n      }\r\n      this.$store.dispatch('getCreditApply', payload)\r\n    } else {\r\n      this.$store\r\n        .dispatch('getDraftInitForm', params)\r\n        .then(([status, data]) => {\r\n          if (!status) return false\r\n          if (data.data.id && !this.id) {\r\n            this.id = data.data.id\r\n            this.$router.replace('/credit/cv/submit?id=' + this.id)\r\n          }\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n", "import mod from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./submit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./submit.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./submit.vue?vue&type=template&id=00ff4481&\"\nimport script from \"./submit.vue?vue&type=script&lang=js&\"\nexport * from \"./submit.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}