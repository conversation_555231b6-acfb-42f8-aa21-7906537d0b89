package com.chevron.poorder.business;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.dao.WorkshopPartnerVoMapper;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.model.WorkshopPartnerVo;
import com.chevron.poorder.model.ProductRespVo;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.util.ThrowableUtil;

@Service
public class PoOrderBizService {
	
	@Resource
	public WorkshopPartnerVoMapper workshopPartnerVoMapper;
	@Resource
	public ProductVoMapper productVoMapper;
	
	public final static int DEFAULT_PAGE_SIZE = 10;
	
	public Map<String,Object> getProductsByWorkshopAndPartner(Long workshopId,Long partnerId)throws Exception
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		if(null==workshopId || null==partnerId)
		{
			resultMap.put("code", MessageContants.PARAMS_NULL_CODE);
			resultMap.put("codeMsg", MessageContants.PARAMS_NULL_MSG);
			resultMap.put("lstProduct", null);
			return resultMap;
		}
		reqMap.put("workshopId", workshopId);
		reqMap.put("partnerId", partnerId);
		//1.验证门店与partner的关系是否匹配
		Map<String,Object> returnMap = validateWorkshopForPartner(reqMap);
		String retrunCode = (String)returnMap.get("code");
		if(!retrunCode.equals(MessageContants.SUCCESS_CODE))
		{
			resultMap = returnMap;
			resultMap.put("lstProduct", null);
			return resultMap;
		}
		
		//2.获取对应门店的产品信息
		reqMap.put("isCompeting", "0");//ADD BY BO.LIU 0825
		reqMap.put("isCollect", "1");//ADD BY BO.LIU 0825
		reqMap.put("partnerId", partnerId);
		List<ProductVo> lstTempProduct = productVoMapper.getProductsByParam(reqMap);//productVoMapper.seletctByWorkshopId(reqMap);
		if(null==lstTempProduct || lstTempProduct.isEmpty())
		{
			resultMap.put("code", MessageContants.POODER_WORKSHOP_PRODUCT_NOT_FOUNT_CODE);
			resultMap.put("codeMsg", MessageContants.POODER_WORKSHOP_PRODUCT_NOT_FOUNT_MSG);
			resultMap.put("lstProduct", null);
			return resultMap;
		}
		//3.重组返回的产品信息
		List<ProductRespVo> lstRespProduct = new ArrayList<ProductRespVo>();
		String downloadUrl =(String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+"/downloadProductLogo.do?sourceType=5&&attId=";//DOWNLOAD_URL
		for(ProductVo productVo:lstTempProduct)
		{
			ProductRespVo  productRespVo = new ProductRespVo();
			productRespVo.setCapacity(productVo.getCapacity());
			productRespVo.setOilType(productVo.getOilType());
			productRespVo.setProductSku(productVo.getSku());
			productRespVo.setProductName(productVo.getName());
			productRespVo.setProductIconUrl(downloadUrl+productVo.getIconId());
			if(productVo.getName().contains("金富力"))
			{
				productRespVo.setBrand("JFL");
				lstRespProduct.add(productRespVo);
				
			}else if(productVo.getName().contains("特劲"))
			{
				productRespVo.setBrand("TJ");
				lstRespProduct.add(productRespVo);
			}else if(productVo.getName().contains("美孚"))
			{
				productRespVo.setBrand("MF");
			}else if(productVo.getName().contains("嘉实多"))
			{
				productRespVo.setBrand("JSD");
			}else
			{
				productRespVo.setBrand("QT");
			}
			//lstRespProduct.add(productRespVo);
		}
		resultMap.put("code", MessageContants.SUCCESS_CODE);
		resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
		resultMap.put("lstProduct", lstRespProduct);
		
		return resultMap;
	}
	
	
	public Map<String,Object> validateWorkshopForPartner(Map<String,Object> reqMap)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<WorkshopPartnerVo> lstWorkshopPartnerVo =  workshopPartnerVoMapper.selectByMap(reqMap);
		if(null==lstWorkshopPartnerVo ||lstWorkshopPartnerVo.isEmpty())
		{
			resultMap.put("code", MessageContants.POODER_WORKSHOP_PARTNER_NOT_MATCH_CODE);
			resultMap.put("codeMsg", MessageContants.POODER_WORKSHOP_PARTNER_NOT_MATCH_MSG);
			return resultMap;
		}
		resultMap.put("code", MessageContants.SUCCESS_CODE);
		return resultMap;
	}
	
	
	public <T> List<T> paginateList(List<T> list, Integer pageNo, Integer pageSize) {
		if (pageSize == null){
			pageSize = DEFAULT_PAGE_SIZE;
		}
		
		int total = list.size();
		int indexFrom = (pageNo - 1) * pageSize;
		int indexTo = (pageNo * pageSize) - 1;
		if (indexFrom >= total){
			return Collections.emptyList();
		}
		if (indexTo >= total) {
			indexTo = total - 1;
		}
		return list.subList(indexFrom, indexTo + 1);
	}

}
