package com.sys.quartz.controller;

import com.common.exception.WxPltException;
import com.common.util.ResponseMap;
import com.sys.quartz.job.SystemWarningPlan;
import com.sys.utils.business.WarnConfigBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @author: chenBin
 * @date: 02/09/2020
 */
@Controller
@RequestMapping("/manualRunTask")
public class ManualRunTaskController {

    @Autowired
    private WarnConfigBizService warnConfigBizService;

    @RequestMapping("/systemWarningPlan.do")
    @ResponseBody
    public ResponseMap manualSystemWarningPlan(final Long id) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    warnConfigBizService.excuteWarnningTask(id);
                } catch (WxPltException e) {
                    e.printStackTrace();
                }
            }
        }).start();
        return new ResponseMap();
    }
}
