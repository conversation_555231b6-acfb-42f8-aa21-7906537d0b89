insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'rules.Chevron_System_Integration', '策略规则', '[{"type":"regular","config":".{15,}","msg":"密码长度至少为15个字符","funWeight":3},{"type":"optional","config":"4","msg":"密码组合必须含下列四种字符<br/>","funWeight":3,"options":[{"type":"regular","config":"[0-9]+","msg":"阿拉伯数字（0~9）"},{"type":"regular","config":"[A-Z]+","msg":"大写英文字母（A~Z）"},{"type":"regular","config":"[a-z]+","msg":"小写英文字母（a~z）"},{"type":"regular","config":"[^0-9a-zA-Z\u4E00-\u9FA5]+","msg":"非字母和数字的特殊字符（!,@,#,$,%,^,&,等）"}]},{"type":"norepeat","config":12,"msg":"不可重复使用前12次密码","funWeight":2}]', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'frozenLimitDays', '未登录冻结限制天数', '90', '1',10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('User.pwdPolicy', 'frozenLimitNotifyDays', '未登录冻结邮件提醒提前天数', '7', '1',10);

--update r set r.user_flag=r.user_flag|1024 from wx_t_role r where r.ch_role_name='Chevron_System_Integration'
INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'userPwdPolicyTask.sendFrozenNotifyEmail', N'WorkGroup', N'1', N'0 0 8 * * ?', N'每天早上8点执行', NULL, N'1', N'userPwdPolicyTask', N'sendFrozenNotifyEmail', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '发送用户账号已停用提醒邮件')
INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'userPwdPolicyTask.sendUserFreezeEmail', N'WorkGroup', N'1', N'0 0 8 * * ?', N'每天早上8点执行', NULL, N'1', N'userPwdPolicyTask', N'sendUserFreezeEmail', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '发送用户账号即将停用提醒邮件')
