	select cos1.region, cos1.supervisor_name_cn 'ASM'/*大区经理*/, cos1.sales_name_cn 'FLSR', pe1.distributor_id /*经销商ID（BI）*/, o1.id partner_id /*经销商ID（PP）*/, o1.organization_name /*经销商名称*/, od1.ext_property1 store_id, 
	w1.id workshop_id, w1.work_shop_name, p1.name product_name, osp1.sku, 
	isnull(isnull(di2.dic_item_code, di1.dic_item_code), 'OTHERS') product_line,
	isnull(isnull(di2.dic_item_name, di1.dic_item_name), '其他') product_line_text, 
	isnull(di3.dic_item_code, 'OTHERS') category, isnull(di3.dic_item_name, '其他') category_text, 
	convert(float, p1.capacity)*osp1.actual_out_count liters, os1.out_time out_stock_time,
	od1.creator, u1.ch_name creator_name,p1.pack, p1.capacity,p1.viscosity,s1.name store_name
	from wx_t_order od1 
	left join wx_t_out_stock os1 on os1.order_type='workshop' and os1.order_no=od1.order_no 
	left join wx_t_out_stock_product osp1 on osp1.stock_out_no=os1.stock_out_no
	left join wx_t_product p1 on p1.sku=osp1.sku
	left join wx_t_dic_item di1 on di1.dic_type_code='product.oilType' and di1.dic_item_code=p1.oil_type
	left join wx_t_dic_item di2 on di2.dic_type_code='product.productLine' and di2.dic_item_code=di1.dic_item_desc
	left join wx_t_dic_item di3 on di3.dic_type_code='product.category' and di3.dic_item_code=p1.category
	left join wx_t_work_shop w1 on w1.id=od1.work_shop_id
	left join wx_t_organization o1 on o1.id=w1.partner_id
	left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=o1.id
	left join wx_t_user u1 on u1.user_id=od1.creator
	left join wx_t_store2021 s1 on s1.id=od1.ext_property1
	left join dw_customer_org_sales cos1 on cos1.distributor_id=pe1.distributor_id and cos1.channel_weight&(case when w1.business_weight&6>0 then 2 else 1 end)>0
	where od1.source='OUT_STOCK' and os1.status='2'