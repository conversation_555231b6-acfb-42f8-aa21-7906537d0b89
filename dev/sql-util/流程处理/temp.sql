select distinct wi.flow_request_no, instance_apply_owner 
from wx_t_workflow_step_history wf_wsh1_1
left join wx_t_workflow_step_instance wf_wsi1_1 on wf_wsh1_1.step_instance_id=wf_wsi1_1.step_instance_id
left join wx_t_workflow_step ws on wf_wsi1_1.step_id=ws.step_id
left join wx_t_workflow_instance wi on wi.flow_instance_id=wf_wsi1_1.workflow_instance_id
where wf_wsh1_1.execute_time>='2020-11-01' and ws.step_code in ('PROVIDER_SUBMIT_PREVIEW_MATERIAL','PROVIDER_SUBMIT_DESIGN_MATERIAL','PROVIDER_SUBMIT_MATERIAL')