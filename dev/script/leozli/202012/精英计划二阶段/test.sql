--EBNL
select * from wx_t_v2_elite_collect_form

select * 
--update d set d.collect_id=6
--delete d
from wx_t_v2_elite_collect_form_det d where collect_id=6 and detail_id=323

select * 
--update f set f.form_status=30
from wx_t_v2_elite_fund_form f where id=323


安徽迈迪汽车部件有限公司 ${customer_name_cn}
180,334.53 ${ReimbursementQ1}
159,588.08 ${ReimbursementQ1Vat}
2020年度第一次折扣转让 ${subTitle}
2021 年 01 月 10 日 ${SignDate}

http://127.0.0.1/v2elitefundform/distributorfileexport.do?distributorId=56&channelWeight=1&applyYear=2020&applyPhase=q1q2
747
53
188
56


select f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0)) consumer_marketing_fund,
sum(- isnull(f.consumer_ivi_fund, 0) 
				- isnull(f.consumer_ivi_fund_adjust, 0)) consumer_ivi_fund,
SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)) commercial_marketing_fund,
sum( - isnull(f.commercial_ivi_fund, 0) 
				- isnull(f.commercial_ivi_fund_adjust, 0)) commercial_ivi_fund
from dw_mid_cal_elite_fund f --where f.remark is not null

group by f.distributor_id
order by f.distributor_id

select quarter,f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0)) consumer_marketing_fund,
sum(- isnull(f.consumer_ivi_fund, 0) 
				- isnull(f.consumer_ivi_fund_adjust, 0)) consumer_ivi_fund,
SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)) commercial_marketing_fund,
sum( - isnull(f.commercial_ivi_fund, 0) 
				- isnull(f.commercial_ivi_fund_adjust, 0)) commercial_ivi_fund
from (select * from dw_mid_cal_elite_fund union all select * from dw_mid_cal_elite_fund_offline) f --where f.remark is not null

group by quarter,f.distributor_id
order by quarter,f.distributor_id

select quarter,f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
SUM(isnull(-f.consumer_marketing_fund*1.13, 0) - isnull(f.consumer_marketing_fund_adjust*1.13, 0)) consumer_marketing_fund,
sum(- isnull(f.consumer_ivi_fund*1.13, 0) 
				- isnull(f.consumer_ivi_fund_adjust*1.13, 0)) consumer_ivi_fund,
SUM(isnull(-f.commercial_marketing_fund*1.13, 0) - isnull(f.commercial_marketing_fund_adjust*1.13, 0)) commercial_marketing_fund,
sum( - isnull(f.commercial_ivi_fund*1.13, 0) 
				- isnull(f.commercial_ivi_fund_adjust*1.13, 0)) commercial_ivi_fund
from PP_MID.dbo.[mid_cal_elite_fund] f --where f.remark is not null

group by quarter,f.distributor_id
order by quarter,f.distributor_id

select * from dw_mid_cal_elite_fund where distributor_id=403
select *  from PP_MID.dbo.[mid_cal_elite_fund]  where distributor_id=403


select quarter, f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
SUM(isnull(-f.consumer_marketing_fund, 0)*1.13 - isnull(f.consumer_marketing_fund_adjust, 0)*1.13 
- isnull(f.consumer_ivi_fund, 0)*1.13 
				- isnull(f.consumer_ivi_fund_adjust, 0)*1.13 - isnull(f.consumer_annual_reward, 0)*1.13) consumer_fund,
SUM(isnull(-f.commercial_marketing_fund, 0)*1.13 - isnull(f.commercial_marketing_fund_adjust, 0)*1.13  - isnull(f.commercial_ivi_fund, 0)*1.13 
				- isnull(f.commercial_ivi_fund_adjust, 0)*1.13) commercial_fund
from PP_MID.dbo.[mid_cal_elite_fund] f --where f.remark is not null
where quarter in ('Q3','Q4')
group by quarter,f.distributor_id 
order by quarter,f.distributor_id

select sum(new_entry+above_scan+flee_goods) from [wx_t_qbr_cdm_performance_adjust] a where a.quarter in (3,4)



update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u 
where  u.user_id in (122995,
123526,
123527,
123528,
129888,
129889,
129893,
129895,
129903,
129905,
129907,
129908,
129909,
129913,
129914,
129918,
129919,
129921,
130907,
130908,
131149,
131419,
131821,
131879,
132182,
132203,
132261,
132353,
65921,
122710,
129892,
129916,
132163,
57,
62,
63,
74,
76,
81,
123,
127,
146,
151,
157,
159,
163,
188,
193,
198,
203,
208,
211,
220,
222,
227,
230,
233,
238,
250,
251,
257,
269,
273,
275,
281,
284,
300,
306,
326,
328,
330,
333,
337,
369,
374,
384,
387,
395,
403,
404,
405,
414,
417,
422,
430,
433,
434,
462,
463,
492,
494,
496,
501,
503,
542,
553,
562,
572,
607,
619,
623,
643,
646,
654,
663,
670,
673,
677,
686,
688,
689,
705,
713,
717,
719,
724,
730,
741,
749,
750,
753,
765,
1049,
1063,
1070,
1078,
1087,
1090,
1093,
1096,
1099,
1100,
1115,
1123,
1127,
1128,
1147,
1159,
1160,
1163,
1164,
1172,
1177,
1183,
1193,
1206,
1216)
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=24953