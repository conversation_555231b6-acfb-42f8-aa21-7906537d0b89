package com.chevron.pms.business.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.importdata.ImportDataUtil;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterExample;
import com.chevron.pms.business.WorkshopBizService;
import com.chevron.pms.model.WorkShopVoExample;
import com.common.util.DateUtil;

@Component
public class WorkshopBizServiceImpl implements WorkshopBizService {

	@Autowired
	private WorkshopMasterMapper workshopMasterMapper;
	
	private static Logger logger = LoggerFactory.getLogger(WorkshopBizServiceImpl.class);
	
	@Override
	public List<WorkshopMaster> queryWorkshopListWithEmployeeExist(Long partnerId) {
		Map<String, Object> params = new HashMap<String, Object>(5);
		params.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
		params.put("existsEmployee", true);
		params.put("partnerId", partnerId);
		return workshopMasterMapper.querySimpleByParams(params);
	}

	@Override
	public List<WorkshopMaster> queryWorkshopListWithVerificationExist(Long partnerId) {
		return Collections.emptyList();
	}

	@Override
	public boolean checkWorkshopExists(Long workshopId, String workshopName) {
		WorkshopMaster existWorkshop = queryWorkshopByName(workshopName);
		if (existWorkshop != null){
			if (workshopId == null){
				return true;
			} else {
				if (existWorkshop.getId().longValue() != workshopId.longValue()){
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public WorkshopMaster queryWorkshopByName(String workshopName) {
		WorkshopMasterExample example = new WorkshopMasterExample();
		example.createCriteria().andWorkshopNameEqualTo(workshopName);
		List<WorkshopMaster> resultList = workshopMasterMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			return resultList.get(0);
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> importWorkshopActiveFee(Workbook wb) {
		logger.info("import workshop active fee data start=================================================");
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
		try {
			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(wb,
					WorkshopMaster.class, new String[]{null, null, "workShopName", "regionName", "workShopAddress"});
			if("dataInvalid".equals(repsMap.get("result"))){
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "门店激活数据解析异常：" + repsMap.get("errorResult"));
				return resultMap;
			}
			List<WorkshopMaster> voList = (List<WorkshopMaster>) repsMap.get("datalst");
			Set<String> importedWorkshops = new HashSet<String>();
//			for(int i = 0; i < voList.size(); i++){
//				WorkshopMaster vo = voList.get(i);
//				String regionName = vo.getDistName().trim();
//				String workshopName = vo.getWorkShopName().trim();
//				String address = vo.getWorkShopAddress().trim();
//				String key = regionName + "->" + address + "->" + workshopName;
//				if (importedWorkshops.contains(key)) {
//					throw new RuntimeException("The " + (i + 1) + "th record fail. The workshop that workshop name is " + workshopName + 
//							" and address is " + address + " has been imported in this file.");
//				}
//				importedWorkshops.add(key);
//				Map<String, Object> paramMap = new HashMap<String, Object>();
//				paramMap.put("regionNameEq", regionName);
//				paramMap.put("workshopNameEq", workshopName);
//				paramMap.put("workshopAddressEq", address);
//				paramMap.put("status", "3");
//				List<WorkshopMaster> matchedVos = workshopVoMapper.selectWorkshopByParams(paramMap);
//				if(matchedVos.isEmpty()){
//					throw new RuntimeException("The " + (i + 1) + "th record fail. There is no workshop that workshop name is " + workshopName + 
//							" and address is " + address);
//				}else if(matchedVos.size() > 1){
//					throw new RuntimeException("The " + (i + 1) + "th record fail.There is more than one workshop that workshop name is " 
//							+ workshopName + " and address is " + address);
//				}
//				WorkshopMaster newVo = new WorkshopMaster();
//				newVo.setId(matchedVos.get(0).getId());
//				newVo.setActiveFeeFlag(1);
//				newVo.setUpdateTime(DateUtil.getCurrentDate());
//				workshopVoMapper.updateByPrimaryKeySelective(newVo);
//			}
			logger.info("import workshop active fee data end=================================================");
		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			logger.error("import workshop active fee data fail: " + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "门店激活数据导入失败：" + e.getMessage());
		}
		return resultMap;
	}

}
