package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.pms.dao.OilVerificationMapper;
import com.chevron.pms.dao.PartnerResponsibleMainMapper;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.model.PartnerResponsibeParams;
import com.chevron.pms.model.PartnerResponsibleMain;
import com.chevron.pms.model.PartnerResponsibleMainExample;
import com.chevron.pms.model.PartnerResponsibleVo;
import com.chevron.pms.model.PartnerResponsibleVoExample;
import com.chevron.pms.service.PartnerResponsibleService;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MessageResourceUtil;
import com.sys.email.service.EmailSenderService;
import com.sys.organization.dao.OrganizationVoMapper;

@Service
public class PartnerResponsibleServiceImpl implements PartnerResponsibleService {
	@Resource
	PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	
	@Resource
	OilVerificationMapper oilVerificationMapper;

	@Autowired
	WorkshopMasterBizService workshopMasterBizService;

	@Autowired
	public EmailSenderService emailSenderService;
	
	@Resource
	private OrganizationVoMapper organizationVoMapper;
	
	@Resource
	public PartnerResponsibleMainMapper partnerResponsibleMainMapper;

	private Logger logger = LoggerFactory
			.getLogger(PartnerResponsibleServiceImpl.class);
	public final static String SUCCESS_CODE = "success";
	public final static String FAIL_CODE = "fail";
	public final static String ERROR_CODE = "error";

	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_CURRENT_ORDER_KEY = "currentOrder";

//	@Override
//	public void emailOilDayReport() {
//		logger.info("start email day report to responsible==============================================");
//		try {
//			// 查询扫码日报发送邮件配置
//			Map<String, Object> map = new HashMap<String, Object>();
//			map.put("funFlag", "sale_day_report");
//			List<PartnerResponsibleVo> userList = partnerResponsibleVoMapper
//					.queryPartnerResponsibleEmailInfo(map);
//			if (userList.isEmpty()) {
//				logger.warn("emailOilDayReport - no partnerResponsible");
//				return;
//			}
//			Date yesterday = DateUtil.addDays(DateUtil.getCurrentDate(), -1);
//			Map<Long, Map<String, Object>> userMap = new HashMap<Long, Map<String, Object>>();
//			Map<String, Object> paramMap = buildDayReportParams(userList,
//					yesterday, userMap);
//			Date startTime = DateUtil.parseDate(Calendar.getInstance().get(Calendar.YEAR) + "-01-01", "yyyy-MM-dd");
//			paramMap.put("startTime", startTime);
//			// 组装合伙人日报映射表
//			Map<Long, Map<String, Object>> reportMap = buildPartnerReportMap(oilVerificationMapper
//					.querySaleDayReport(paramMap));
//			// 组装合伙人激活门店日报映射表
//			Map<Long, Map<String, Object>> workshopReportMap = buildPartnerWorkshopReportMap(workShopVoMapper
//					.queryActiveSumReport(paramMap));
//			// 组装邮件日报数据
//			Map<Long, List<Map<String, Object>>> userSumReportMap = new HashMap<Long, List<Map<String, Object>>>();
//			Map<Long, Map<String, Object>> userTotalReportMap = new HashMap<Long, Map<String, Object>>();
//			Map<Long, List<Map<String, Object>>> userWorkshopSumReportMap = new HashMap<Long, List<Map<String, Object>>>();
//			Map<Long, Map<String, Object>> userWorkshopTotalReportMap = new HashMap<Long, Map<String, Object>>();
//
//			buildDaySumReport(userSumReportMap, userTotalReportMap,
//					userWorkshopSumReportMap, userWorkshopTotalReportMap,
//					userList, reportMap, workshopReportMap);
//			
//			//组装日报明细数据
//			Map<Long, List<OilVerification>> oilDetailReportMap = buildDayDetailReport(paramMap, userList);
//			Map<Long, List<WorkshopReportView>> wsDetailReportMap = buildWsDetailReport(paramMap, userList);
//			Map<Long, List<WorkshopReportView>> xdDetailReportMap = buildXdDetailReport(paramMap, userList);
//
//			// 发送邮件
//			sendDayReportEmail(userSumReportMap, userTotalReportMap,
//					userWorkshopSumReportMap, userWorkshopTotalReportMap,
//					yesterday, userMap,oilDetailReportMap, wsDetailReportMap, xdDetailReportMap);
//			logger.info("end email day report to responsible==============================================");
//			LogUtils.addInfoLog(1l, "PartnerResponsibleServiceImpl.emailOilDayReport", "销售日报已发送");
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(), e);
//			LogUtils.addErrorLog(1l, "PartnerResponsibleServiceImpl.emailOilDayReport", "发送销售日报失败。" + e.getMessage(), null);
//		}
//	}
//	
//	//构建每个用户核销日报明细数据
//	private Map<Long, List<OilVerification>> buildDayDetailReport(Map<String, Object> paramMap, List<PartnerResponsibleVo> userList){
//		Map<Long, List<OilVerification>> result = new HashMap<Long, List<OilVerification>>();
//		Map<Long, Set<Long>> userPartnerSetMap = new HashMap<Long, Set<Long>>();
//		// 查询报表数据
//		List<OilVerification> reportList = oilVerificationMapper.queryDetailForDailyReportEmail(paramMap);
//		Map<Long, List<OilVerification>> reportListMap = new HashMap<Long, List<OilVerification>>();
//		for(OilVerification temp : reportList){
//			List<OilVerification> list = reportListMap.get(temp.getPartnerId());
//			if(list == null){
//				list = new ArrayList<OilVerification>();
//				reportListMap.put(temp.getPartnerId(), list);
//			}
//			list.add(temp);
//		}
//		for(PartnerResponsibleVo vo : userList){
//			Set<Long> userPartnerSet = userPartnerSetMap.get(vo.getResponsiblePersonId());
//			List<OilVerification> list = result.get(vo.getResponsiblePersonId());
//			if(userPartnerSet == null){
//				userPartnerSet = new HashSet<Long>();
//				userPartnerSetMap.put(vo.getResponsiblePersonId(), userPartnerSet);
//				list = new ArrayList<OilVerification>();
//				result.put(vo.getResponsiblePersonId(), list);
//			}else if(userPartnerSet.contains(vo.getPartnerId())){
//				continue;
//			}
//			userPartnerSet.add(vo.getPartnerId());
//			if (reportListMap.containsKey(vo.getPartnerId())) {
//				list.addAll(reportListMap.get(vo.getPartnerId()));
//			}
//		}
//		return result;
//	}
//	
//	//构建每个用户门店激活明细数据
//	private Map<Long, List<WorkshopReportView>> buildWsDetailReport(Map<String, Object> paramMap, List<PartnerResponsibleVo> userList){
//		Map<Long, List<WorkshopReportView>> result = new HashMap<Long, List<WorkshopReportView>>();
//		Map<Long, Set<Long>> userPartnerSetMap = new HashMap<Long, Set<Long>>();
//		// 查询报表数据
//		List<WorkshopReportView> reportList = workShopVoMapper
//				.queryActiveDetialReport(paramMap);
//		Map<Long, List<WorkshopReportView>> reportListMap = new HashMap<Long, List<WorkshopReportView>>();
//		for(WorkshopReportView temp : reportList){
//			List<WorkshopReportView> list = reportListMap.get(temp.getPartnerId());
//			if(list == null){
//				list = new ArrayList<WorkshopReportView>();
//				reportListMap.put(temp.getPartnerId(), list);
//			}
//			list.add(temp);
//		}
//		for(PartnerResponsibleVo vo : userList){
//			Set<Long> userPartnerSet = userPartnerSetMap.get(vo.getResponsiblePersonId());
//			List<WorkshopReportView> list = result.get(vo.getResponsiblePersonId());
//			if(userPartnerSet == null){
//				userPartnerSet = new HashSet<Long>();
//				userPartnerSetMap.put(vo.getResponsiblePersonId(), userPartnerSet);
//				list = new ArrayList<WorkshopReportView>();
//				result.put(vo.getResponsiblePersonId(), list);
//			}else if(userPartnerSet.contains(vo.getPartnerId())){
//				continue;
//			}
//			userPartnerSet.add(vo.getPartnerId());
//			if (reportListMap.containsKey(vo.getPartnerId())) {
//				list.addAll(reportListMap.get(vo.getPartnerId()));
//			}
//		}
//		return result;
//	}
//	
//	//构建每个用户负责合伙人巡店明细数据
//	private Map<Long, List<WorkshopReportView>> buildXdDetailReport(Map<String, Object> paramMap, List<PartnerResponsibleVo> userList){
//		Map<Long, List<WorkshopReportView>> result = new HashMap<Long, List<WorkshopReportView>>();
//		Map<Long, Set<Long>> userPartnerSetMap = new HashMap<Long, Set<Long>>();
//		// 查询报表数据
//		List<WorkshopReportView> reportList = workShopVoMapper
//				.queryXdDetialReport(paramMap);
//		Map<Long, List<WorkshopReportView>> reportListMap = new HashMap<Long, List<WorkshopReportView>>();
//		for(WorkshopReportView temp : reportList){
//			List<WorkshopReportView> list = reportListMap.get(temp.getPartnerId());
//			if(list == null){
//				list = new ArrayList<WorkshopReportView>();
//				reportListMap.put(temp.getPartnerId(), list);
//			}
//			list.add(temp);
//		}
//		for(PartnerResponsibleVo vo : userList){
//			Set<Long> userPartnerSet = userPartnerSetMap.get(vo.getResponsiblePersonId());
//			List<WorkshopReportView> list = result.get(vo.getResponsiblePersonId());
//			if(userPartnerSet == null){
//				userPartnerSet = new HashSet<Long>();
//				userPartnerSetMap.put(vo.getResponsiblePersonId(), userPartnerSet);
//				list = new ArrayList<WorkshopReportView>();
//				result.put(vo.getResponsiblePersonId(), list);
//			}else if(userPartnerSet.contains(vo.getPartnerId())){
//				continue;
//			}
//			userPartnerSet.add(vo.getPartnerId());
//			if (reportListMap.containsKey(vo.getPartnerId())) {
//				list.addAll(reportListMap.get(vo.getPartnerId()));
//			}
//		}
//		return result;
//	}
//
//	private void buildDaySumReport(
//			Map<Long, List<Map<String, Object>>> userSumReportMap,
//			Map<Long, Map<String, Object>> userTotalReportMap,
//			Map<Long, List<Map<String, Object>>> userWorkshopSumReportMap,
//			Map<Long, Map<String, Object>> userWorkshopTotalReportMap,
//			List<PartnerResponsibleVo> userList,
//			Map<Long, Map<String, Object>> reportMap,
//			Map<Long, Map<String, Object>> workshopReportMap) {
//		for (PartnerResponsibleVo vo : userList) {
//			// 组装机油扫码报表
//			Map<String, Object> partnerReport = reportMap
//					.get(vo.getPartnerId());
//			if (partnerReport != null) {
//				List<Map<String, Object>> sumReport = userSumReportMap.get(vo
//						.getResponsiblePersonId());
//				Map<String, Object> totalReport;
//				if (sumReport == null) {
//					sumReport = new ArrayList<Map<String, Object>>();
//					userSumReportMap
//							.put(vo.getResponsiblePersonId(), sumReport);
//					totalReport = new HashMap<String, Object>(8);
//					totalReport.put("partnerName", "合计：");
//					totalReport.put("dayCapacity", 0);
//					totalReport.put("monthCapacity", 0);
//					totalReport.put("totalCapacity", 0);
//					totalReport.put("sellinDayCapacity", 0);
//					totalReport.put("sellinMonthCapacity", 0);
//					totalReport.put("sellinTotalCapacity", 0);
//					userTotalReportMap.put(vo.getResponsiblePersonId(),
//							totalReport);
//				} else {
//					totalReport = userTotalReportMap.get(vo
//							.getResponsiblePersonId());
//				}
//				totalReport.put("dayCapacity",
//						(Integer) totalReport.get("dayCapacity")
//								+ (Integer) partnerReport.get("dayCapacity"));
//				totalReport.put("monthCapacity",
//						(Integer) totalReport.get("monthCapacity")
//								+ (Integer) partnerReport.get("monthCapacity"));
//				totalReport.put("totalCapacity",
//						(Integer) totalReport.get("totalCapacity")
//								+ (Integer) partnerReport.get("totalCapacity"));
//				totalReport.put("sellinDayCapacity",
//						(Integer) totalReport.get("sellinDayCapacity")
//								+ (Integer) partnerReport.get("sellinDayCapacity"));
//				totalReport.put("sellinMonthCapacity",
//						(Integer) totalReport.get("sellinMonthCapacity")
//								+ (Integer) partnerReport.get("sellinMonthCapacity"));
//				totalReport.put("sellinTotalCapacity",
//						(Integer) totalReport.get("sellinTotalCapacity")
//								+ (Integer) partnerReport.get("sellinTotalCapacity"));
//				sumReport.add(partnerReport);
//			}
//			// 组装机油扫码报表
//			Map<String, Object> workshopPartnerReport = workshopReportMap
//					.get(vo.getPartnerId());
//			if (workshopPartnerReport != null) {
//				List<Map<String, Object>> sumReport = userWorkshopSumReportMap
//						.get(vo.getResponsiblePersonId());
//				Map<String, Object> totalReport;
//				if (sumReport == null) {
//					sumReport = new ArrayList<Map<String, Object>>();
//					userWorkshopSumReportMap.put(vo.getResponsiblePersonId(),
//							sumReport);
//					totalReport = new HashMap<String, Object>(8);
//					totalReport.put("partnerName", "合计：");
//					totalReport.put("dayCount", 0);
//					totalReport.put("monthCount", 0);
//					totalReport.put("totalCount", 0);
//					totalReport.put("dayXdCount", 0);
//					totalReport.put("monthXdCount", 0);
//					totalReport.put("totalXdCount", 0);
//					userWorkshopTotalReportMap.put(vo.getResponsiblePersonId(),
//							totalReport);
//				} else {
//					totalReport = userWorkshopTotalReportMap.get(vo
//							.getResponsiblePersonId());
//				}
//				totalReport.put(
//						"dayCount",
//						(Integer) totalReport.get("dayCount")
//								+ (Integer) workshopPartnerReport
//										.get("dayCount"));
//				totalReport.put(
//						"monthCount",
//						(Integer) totalReport.get("monthCount")
//								+ (Integer) workshopPartnerReport
//										.get("monthCount"));
//				totalReport.put(
//						"totalCount",
//						(Integer) totalReport.get("totalCount")
//								+ (Integer) workshopPartnerReport
//										.get("totalCount"));
//				totalReport.put(
//						"dayXdCount",
//						(Integer) totalReport.get("dayXdCount")
//								+ (Integer) workshopPartnerReport
//										.get("dayXdCount"));
//				totalReport.put(
//						"monthXdCount",
//						(Integer) totalReport.get("monthXdCount")
//								+ (Integer) workshopPartnerReport
//										.get("monthXdCount"));
//				totalReport.put(
//						"totalXdCount",
//						(Integer) totalReport.get("totalXdCount")
//								+ (Integer) workshopPartnerReport
//										.get("totalXdCount"));
//				sumReport.add(workshopPartnerReport);
//			}
//		}
//	}
//
//	private Map<Long, Map<String, Object>> buildPartnerReportMap(
//			List<OilVerificationReportView> views) {
//		Map<Long, Map<String, Object>> reportMap = new HashMap<Long, Map<String, Object>>();
//		for (OilVerificationReportView view : views) {
//			Map<String, Object> sumReport = reportMap.get(view.getPartnerId());
//			if (sumReport == null) {
//				sumReport = new HashMap<String, Object>(8);
//				sumReport.put("partnerName", view.getPartnerName());
//				sumReport.put("partnerId", view.getPartnerId());
//				sumReport.put("dayCapacity", 0);
//				sumReport.put("monthCapacity", 0);
//				sumReport.put("totalCapacity", 0);
//				sumReport.put("sellinDayCapacity", 0);
//				sumReport.put("sellinMonthCapacity", 0);
//				sumReport.put("sellinTotalCapacity", 0);
//				reportMap.put(view.getPartnerId(), sumReport);
//			}
//			switch (view.getReportType()) {
//			case 1:
//				sumReport.put("dayCapacity", view.getCapacity());
//				break;
//			case 2:
//				sumReport.put("monthCapacity", view.getCapacity());
//				break;
//			case 3:
//				sumReport.put("totalCapacity", view.getCapacity());
//				break;
//			case 4:
//				sumReport.put("sellinDayCapacity", view.getCapacity());
//				break;
//			case 5:
//				sumReport.put("sellinMonthCapacity", view.getCapacity());
//				break;
//			case 6:
//				sumReport.put("sellinTotalCapacity", view.getCapacity());
//				break;
//			default:
//				break;
//			}
//		}
//		return reportMap;
//	}
//
//	private Map<Long, Map<String, Object>> buildPartnerWorkshopReportMap(
//			List<WorkshopReportView> views) {
//		Map<Long, Map<String, Object>> reportMap = new HashMap<Long, Map<String, Object>>();
//		for (WorkshopReportView view : views) {
//			Map<String, Object> sumReport = reportMap.get(view.getPartnerId());
//			if (sumReport == null) {
//				sumReport = new HashMap<String, Object>(8);
//				sumReport.put("partnerName", view.getPartnerName());
//				sumReport.put("partnerId", view.getPartnerId());
//				sumReport.put("dayCount", 0);
//				sumReport.put("monthCount", 0);
//				sumReport.put("totalCount", 0);
//				sumReport.put("dayXdCount", 0);
//				sumReport.put("monthXdCount", 0);
//				sumReport.put("totalXdCount", 0);
//				reportMap.put(view.getPartnerId(), sumReport);
//			}
//			switch (view.getViewFlag()) {
//			case 2:
//				sumReport.put("dayCount", view.getCount());
//				break;
//			case 3:
//				sumReport.put("monthCount", view.getCount());
//				break;
//			case 1:
//				sumReport.put("totalCount", view.getCount());
//				break;
//			case 5:
//				sumReport.put("dayXdCount", view.getCount());
//				break;
//			case 6:
//				sumReport.put("monthXdCount", view.getCount());
//				break;
//			case 4:
//				sumReport.put("totalXdCount", view.getCount());
//				break;
//			default:
//				break;
//			}
//		}
//		return reportMap;
//	}
//
//	private Map<String, Object> buildDayReportParams(
//			List<PartnerResponsibleVo> userList, Date yesterday,
//			Map<Long, Map<String, Object>> userMap) {
//		Map<String, Object> paramMap = new HashMap<String, Object>();
//		// 日报查询合伙人条件
//		Set<Long> partnerIds = new HashSet<Long>();
//		for (PartnerResponsibleVo vo : userList) {
//			partnerIds.add(vo.getPartnerId());
//			if (!userMap.containsKey(vo.getResponsiblePersonId())) {
//				Map<String, Object> user = new HashMap<String, Object>(2);
//				user.put("id", vo.getResponsiblePersonId());
//				user.put("name", vo.getResponsiblePersonName());
//				user.put("email", vo.getResponsiblePersonEmail());
//				user.put("ccemail", vo.getDayReportCc());
//				userMap.put(vo.getResponsiblePersonId(), user);
//			}
//		}
//		StringBuilder sBuilder = null;
//		for (Long partnerId : partnerIds) {
//			if (sBuilder == null) {
//				sBuilder = new StringBuilder(500).append("(");
//			} else {
//				sBuilder.append(",");
//			}
//			sBuilder.append(partnerId);
//		}
//		paramMap.put("partnerInCondition", sBuilder.append(")").toString());
//		paramMap.put("dateFrom", DateUtil.getDateToZero(yesterday));
//		paramMap.put("dateTo", DateUtil.getDateToMax(yesterday));
//		paramMap.put("dateFrom1", DateUtil.getFirstDayOfMonth(yesterday));
//		paramMap.put("dateTo1", DateUtil.getLastDayOfMonth(yesterday));
//		paramMap.put("dateFrom2", DateUtil.getFirstDayOfYear(yesterday));
//		paramMap.put("dateTo2", DateUtil.getLastDayOfYear(yesterday));
//		//3个月明细数据限制
//		paramMap.put("dateFrom3", DateUtil.getDateToZero(DateUtil.addMonths(yesterday, -1)));
//		return paramMap;
//	}
//
//	private void sendDayReportEmail(
//			Map<Long, List<Map<String, Object>>> userSumReportMap,
//			Map<Long, Map<String, Object>> userTotalReportMap,
//			Map<Long, List<Map<String, Object>>> userWorkshopSumReportMap,
//			Map<Long, Map<String, Object>> userWorkshopTotalReportMap,
//			Date yesterday, Map<Long, Map<String, Object>> userMap,
//			Map<Long, List<OilVerification>> oilDetailReportMap,
//			Map<Long, List<WorkshopReportView>> wsDetailReportMap,
//			Map<Long, List<WorkshopReportView>> xdDetailReportMap) throws IOException {
//		WebApplicationContext webApplicationContext = ContextLoader
//				.getCurrentWebApplicationContext();
//		ServletContext servletContext = webApplicationContext
//				.getServletContext();
//
//		for (Map<String, Object> user : userMap.values()) {
//			List<File> attFiles = new ArrayList<File>(2);
//			Map<String, Object> dataMap = new HashMap<String, Object>();
//			dataMap.put("responsiblePersonName", user.get("name"));
//			dataMap.put("yesterdayDay",
//					new SimpleDateFormat("yyyy.M.d").format(yesterday));
//			dataMap.put("yesterdayMonth",
//					new SimpleDateFormat("yyyy.M").format(yesterday));
//			dataMap.put("currentYear", Calendar.getInstance().get(Calendar.YEAR) + "");
//
//			List<Map<String, Object>> sumReport = userSumReportMap.get(user
//					.get("id"));
//			if (sumReport != null) {
//				logger.info("email oil day report to responsible: export oil excel==============================================");
//				attFiles.add(OilVerificationBizService.createVerificationDetailExcel(oilDetailReportMap.get(user.get("id")), "合伙人核销扫码详情", servletContext));
//				sumReport.add(userTotalReportMap.get(user.get("id")));
//				dataMap.put("oilReportList", sumReport);
//			}
//			List<Map<String, Object>> sumWorkshopReport = userWorkshopSumReportMap
//					.get(user.get("id"));
//			if (sumWorkshopReport != null) {
//				logger.info("email oil day report to responsible: export workshop excel==============================================");
//				attFiles.add(createDayWorkshopReportExcel(wsDetailReportMap.get(user.get("id")), yesterday,
//						servletContext));
////				attFiles.add(createDayXdReportExcel(xdDetailReportMap.get(user.get("id")), yesterday,
////						servletContext));
//				sumWorkshopReport.add(userWorkshopTotalReportMap.get(user
//						.get("id")));
//				dataMap.put("workshopReportList", sumWorkshopReport);
//			}
//			String[] cc = null;
//			String ccemail = (String) user.get("ccemail");
//			if (StringUtils.isNotBlank(ccemail)) {
//				cc = ccemail.split(",");
//			}
//			try {
//				if(!EmailSendUtils.sendEmailForListContent(
//						servletContext,
//						new String[] { (String) user.get("email") },
//						cc,
//						"销售日报",
//						dataMap,
//						attFiles.isEmpty() ? null : attFiles
//								.toArray(new File[attFiles.size()]),
//						"oil_day_report.ftl")){
//					throw new WxPltException("邮件发送失败");
//				}
//			} catch (Exception e) {
//				logger.error(e.getMessage(), e);
//				LogUtils.addErrorLog(1l, "PartnerResponsibleServiceImpl.sendDayReportEmail", "发送日报失败。" + e.getMessage(), (String) user.get("email"));
//			}
//			// emailSenderService.sendEmail(servletContext, new
//			// String[]{vo.getResponsiblePersonEmail()}, cc,
//			// "合伙人扫码日报", sBuilder.toString(), , "oil_day_report.ftl");
//		}
//	}
//
//	private File createDayWorkshopReportExcel(List<WorkshopReportView> reportList,
//			Date yesterday, ServletContext servletContext) throws IOException {
//		// 导出excel
//		File excelFile = new File(
//				servletContext.getRealPath("/app/合伙人录店详情.xlsx"));
//		OutputStream outputStream = new FileOutputStream(excelFile);
//		List<ExportCol> exportCols = new ArrayList<ExportCol>();
//		ExportCol col = new ExportCol("partnerName", "合伙人名称", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("workshopName", "门店名称", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("provinceName", "所属省份", WorkshopReportView.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("cityName", "所属城市", WorkshopReportView.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("regionName", "所属区域", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("workshopAddress", "门店地址", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("type", "门店类型", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("reportTime", "激活时间", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		try {
//			PoiWriteExcel.exportLargeData(reportList, exportCols, outputStream, "Sheet1", true);
//		}catch (Exception e) {
//			logger.error("导出合伙人录店详情失败", e);
//		} finally {
//			outputStream.close();
//		}
//		return excelFile;
//	}
//
//	protected File createDayXdReportExcel(List<WorkshopReportView> reportList,
//			Date yesterday, ServletContext servletContext) throws IOException {
//		// 导出excel
//		File excelFile = new File(
//				servletContext.getRealPath("/app/合伙人巡店详情.xlsx"));
//		OutputStream outputStream = new FileOutputStream(excelFile);
//		List<ExportCol> exportCols = new ArrayList<ExportCol>();
//		ExportCol col = new ExportCol("partnerName", "合伙人名称", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("workshopName", "门店名称", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("provinceName", "所属省份", WorkshopReportView.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("cityName", "所属城市", WorkshopReportView.class);
//		col.setWidth(20);
//		exportCols.add(col);
//		col = new ExportCol("regionName", "所属区域", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("workshopAddress", "门店地址", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("type", "门店类型", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		col = new ExportCol("reportTime", "巡店完成时间", WorkshopReportView.class);
//		col.setWidth(30);
//		exportCols.add(col);
//		try {
//			PoiWriteExcel.exportLargeData(reportList, exportCols, outputStream, "Sheet1", true);
//		}catch (Exception e) {
//			logger.error("导出巡店详情失败", e);
//		} finally {
//			outputStream.close();
//		}
//		return excelFile;
//	}

	@Override
	public Map<String, Object> getAllPartnerResponsible(
			PartnerResponsibeParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		try {
		// 查询main表
			List<PartnerResponsibleMain> mainResultList = partnerResponsibleMainMapper.selectPartnerResponsibleVoByParams(params);
			//组装明细数据
			if(!mainResultList.isEmpty()){
				Map<Long, PartnerResponsibleMain> mainMap = new HashMap<Long, PartnerResponsibleMain>(mainResultList.size());
				List<Long> mainIds = new ArrayList<Long>();
				List<PartnerResponsibleMain> mains = new ArrayList<PartnerResponsibleMain>();
				for (PartnerResponsibleMain main : mainResultList) {
					mainMap.put(main.getId(), main);
					if(PartnerResponsibleMain.DATA_TYPE_TEMP_CONFIG.equals(main.getDataType())){
						mainIds.add(main.getId());
						if(main.getIncludeActualRel() == 1){
							mains.add(main);
						}
					}else{
						mains.add(main);
					}
				}
				if(!mainIds.isEmpty()){
					List<PartnerResponsibleVo> list = partnerResponsibleVoMapper.selectConfigPartnersByMainIds(mainIds);
					for(PartnerResponsibleVo vo : list){
						mainMap.get(vo.getResponsibleMainId()).getDetailList().add(vo);
					}
				}
				if(!mains.isEmpty()){
					List<PartnerResponsibleVo> list = partnerResponsibleVoMapper.selectActualResponsiblePartnersByMains(mains);
					for(PartnerResponsibleVo vo : list){
						mainMap.get(vo.getResponsibleMainId()).getActualResponsibleVos().add(vo);
					}
				}
			}
			resultMap.put("total", params.getTotalCount());
			resultMap.put(RESULT_LST_KEY, mainResultList);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);

		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("isError", true);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil
					.getMessage("system.unexpected_exception"));
		}

		// try {
		// List<PartnerResponsibleVo> resultList =
		// partnerResponsibleVoMapper.selectPartnerResponsibleVoByParams(params);
//		 resultMap.put(RESULT_LST_KEY, resultList);
//		 resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		// } catch (Exception e) {
		// e.printStackTrace();
		// resultMap.put("isError", true);
		// resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
		// resultMap.put(RESULT_ERROR_MSG_KEY,
		// MessageResourceUtil.getMessage("system.unexpected_exception"));
		// }

		return resultMap;
	}

	@Override
	public Map<String, Object> deletePartnerResponsible(long main_id) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			//删除main表中的数据
			partnerResponsibleMainMapper.deleteByPrimaryKey(main_id);

			//删除partnerResponsible表，根据main_id删除所有
			partnerResponsibleVoMapper.deleteByMainId(main_id);
			
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("isError", true);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil
					.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> saveInfo(
			PartnerResponsibleMain partnerResponsibleMain) {
		Map<String, Object> map = new HashMap<String, Object>();
		try{
			Long id = partnerResponsibleMain.getId();
			Long creator = ContextUtil.getCurUserId();
			if(null == id){
				partnerResponsibleMain.setCreateTime(DateUtil.getCurrentDate());
				partnerResponsibleMain.setCreator(creator);
				partnerResponsibleMainMapper.insertSelective(partnerResponsibleMain);
				id = partnerResponsibleMain.getId();
			} else {
				partnerResponsibleMain.setCreateTime(null);
				partnerResponsibleMain.setCreator(null);
				partnerResponsibleMain.setId(null);
				PartnerResponsibleMainExample example = new PartnerResponsibleMainExample();
				example.createCriteria().andIdEqualTo(id);
				partnerResponsibleMainMapper.updateByExampleSelective(partnerResponsibleMain, example);
				//删除原来的明细表
				PartnerResponsibleVoExample example1 = new PartnerResponsibleVoExample();
				example1.createCriteria().andResponsibleMainIdEqualTo(id);
				partnerResponsibleVoMapper.deleteByExample(example1);
			}
			//新增明细
			for(PartnerResponsibleVo vo : partnerResponsibleMain.getDetailList()){
				vo.setCreateTime(DateUtil.getCurrentDate());
				vo.setCreator(creator);
				vo.setResponsibleMainId(id);
				partnerResponsibleVoMapper.insertSelective(vo);
			}
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
			e.printStackTrace();
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}

//	@Override
//	public Map<String, Object> getSaleDailyReport(String mobile) {
//		Map<String, Object> map = new HashMap<String, Object>();
//		try{
//			// 查询扫码日报发送邮件配置
//			Map<String, Object> responsibleParams = new HashMap<String, Object>();
//			responsibleParams.put("funFlag", "sale_day_report");
//			responsibleParams.put("mobile", mobile);
//			List<Long> partnerIds = partnerResponsibleVoMapper
//					.querySaleReportPartners(responsibleParams);
//			if (partnerIds.isEmpty()) {
//				throw new WxPltException("该销售没有负责的合伙人");
//			}
//			Date yesterday = DateUtil.addDays(DateUtil.getCurrentDate(), -1);
//			Map<String, Object> paramMap = new HashMap<String, Object>();		
//			StringBuilder sBuilder = null;
//			for (Long partnerId : partnerIds) {
//				if (sBuilder == null) {
//					sBuilder = new StringBuilder(500).append("(");
//				} else {
//					sBuilder.append(",");
//				}
//				sBuilder.append(partnerId);
//			}
//			paramMap.put("partnerInCondition", sBuilder.append(")").toString());
//			paramMap.put("dateFrom", DateUtil.getDateToZero(yesterday));
//			paramMap.put("dateTo", DateUtil.getDateToMax(yesterday));
//			paramMap.put("dateFrom1", DateUtil.getFirstDayOfMonth(yesterday));
//			paramMap.put("dateTo1", DateUtil.getLastDayOfMonth(yesterday));
//			paramMap.put("dateFrom2", DateUtil.getFirstDayOfYear(yesterday));
//			paramMap.put("dateTo2", DateUtil.getLastDayOfYear(yesterday));
//			
//			OrganizationParam param = new OrganizationParam();
//			param.setResourceId("saleDailyReport");
//			Long partnerCount = organizationVoMapper.getPartnersCount(param);
//			map.put("partnerCount", partnerCount);
//			map.put("reportDate", yesterday);
//			map.put("verificationReport", buildVerificationReportMap(partnerCount, oilVerificationMapper.querySaleDailyReportForInte(paramMap)).values());
//			Map<String, Map<Long, Map<String, Object>>> allReports = buildTaskReportMap(partnerCount, workShopVoMapper.queryActiveSumReportForInte(paramMap));
//			for(String reportType : allReports.keySet()){
//				map.put(reportType, allReports.get(reportType).values());
//			}
//			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		} catch (WxPltException e) {
//			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
//		} catch (Exception e) {
//			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			//add by bo.liu 180111
//			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			logger.error(e.getMessage(), e);
//		}
//		return map;
//	}
//
//	/**
//	 * 重新组装核销报表数据
//	 * @param partnerCount 合伙人总数
//	 * @param verificationReportData 核销报表数据
//	 * @return
//	 */
//	private Map<Long, Map<String, Object>> buildVerificationReportMap(Long partnerCount, List<OilVerificationReportView> verificationReportData){
//		Map<Long, Map<String, Object>> verificationReportMap = new HashMap<Long, Map<String,Object>>();
//		for(OilVerificationReportView view : verificationReportData){
//			Map<String, Object> reportItem = verificationReportMap.get(view.getPartnerId());
//			if(reportItem == null){
//				reportItem = new HashMap<String, Object>();
//				reportItem.put("partnerName", view.getPartnerName());
//				reportItem.put("partnerId", view.getPartnerId());
//				reportItem.put("dayCount", 0l);
//				reportItem.put("dayRank", partnerCount);
//				reportItem.put("monthCount", 0l);
//				reportItem.put("monthRank", partnerCount);
//				reportItem.put("yearCount", 0l);
//				reportItem.put("yearRank", partnerCount);
//				verificationReportMap.put(view.getPartnerId(), reportItem);
//			}
//			switch (view.getReportType()) {
//			case 1:
//				reportItem.put("dayCount", view.getCapacity());
//				reportItem.put("dayRank", view.getRank());
//				break;
//			case 2:
//				reportItem.put("monthCount", view.getCapacity());
//				reportItem.put("monthRank", view.getRank());
//				break;
//			case 3:
//				reportItem.put("yearCount", view.getCapacity());
//				reportItem.put("yearRank", view.getRank());
//				break;
//			default:
//				break;
//			}
//		}
//		return verificationReportMap;
//	}
//
//	/**
//	 * 重新组装任务报表数据
//	 * @param partnerCount 合伙人总数
//	 * @param taskReportData 任务报表数据
//	 * @return
//	 */
//	private Map<String, Map<Long, Map<String, Object>>> buildTaskReportMap(Long partnerCount, List<WorkshopReportView> taskReportData){
//		Map<String, Map<Long, Map<String, Object>>> allReport = new HashMap<String, Map<Long,Map<String,Object>>>(6);
//		for(WorkshopReportView view : taskReportData){
//			Map<String, Object> reportItem = null;
//			switch (view.getViewFlag()) {
//			case 2:
//				reportItem = getReportItem("taskLDReport", view, allReport, partnerCount);
//				reportItem.put("dayCount", view.getCount());
//				reportItem.put("dayRank", view.getRank());
//				break;
//			case 3:
//				reportItem = getReportItem("taskLDReport", view, allReport, partnerCount);
//				reportItem.put("monthCount", view.getCount());
//				reportItem.put("monthRank", view.getRank());
//				break;
//			case 1:
//				reportItem = getReportItem("taskLDReport", view, allReport, partnerCount);
//				reportItem.put("yearCount", view.getCount());
//				reportItem.put("yearRank", view.getRank());
//				break;
//			case 5:
//				reportItem = getReportItem("taskXDReport", view, allReport, partnerCount);
//				reportItem.put("dayCount", view.getCount());
//				reportItem.put("dayRank", view.getRank());
//				break;
//			case 6:
//				reportItem = getReportItem("taskXDReport", view, allReport, partnerCount);
//				reportItem.put("monthCount", view.getCount());
//				reportItem.put("monthRank", view.getRank());
//				break;
//			case 4:
//				reportItem = getReportItem("taskXDReport", view, allReport, partnerCount);
//				reportItem.put("yearCount", view.getCount());
//				reportItem.put("yearRank", view.getRank());
//				break;
//			case 8:
//				reportItem = getReportItem("taskSDReport", view, allReport, partnerCount);
//				reportItem.put("dayCount", view.getCount());
//				reportItem.put("dayRank", view.getRank());
//				break;
//			case 9:
//				reportItem = getReportItem("taskSDReport", view, allReport, partnerCount);
//				reportItem.put("monthCount", view.getCount());
//				reportItem.put("monthRank", view.getRank());
//				break;
//			case 7:
//				reportItem = getReportItem("taskSDReport", view, allReport, partnerCount);
//				reportItem.put("yearCount", view.getCount());
//				reportItem.put("yearRank", view.getRank());
//				break;
//			case 11:
//				reportItem = getReportItem("sellinReport", view, allReport, partnerCount);
//				reportItem.put("dayCount", view.getCount());
//				reportItem.put("dayRank", view.getRank());
//				break;
//			case 12:
//				reportItem = getReportItem("sellinReport", view, allReport, partnerCount);
//				reportItem.put("monthCount", view.getCount());
//				reportItem.put("monthRank", view.getRank());
//				break;
//			case 10:
//				reportItem = getReportItem("sellinReport", view, allReport, partnerCount);
//				reportItem.put("yearCount", view.getCount());
//				reportItem.put("yearRank", view.getRank());
//				break;
//			default:
//				break;
//			}
//		}
//		return allReport;
//	}
//	
//	protected Map<String, Object> getReportItem(String reportType,WorkshopReportView view, 
//			Map<String, Map<Long, Map<String, Object>>> allReport, Long partnerCount){
//		Map<Long, Map<String, Object>> reportMap = allReport.get(reportType);
//		Map<String, Object> reportItem = null;
//		if(reportMap == null){
//			reportMap = new HashMap<Long, Map<String,Object>>();
//			allReport.put(reportType, reportMap);
//		}else {
//			reportItem = reportMap.get(view.getPartnerId());
//		}
//		if(reportItem == null){
//			reportItem = new HashMap<String, Object>(10);
//			reportItem.put("partnerName", view.getPartnerName());
//			reportItem.put("partnerId", view.getPartnerId());
//			reportItem.put("dayCount", 0);
//			reportItem.put("dayRank", partnerCount);
//			reportItem.put("monthCount", 0);
//			reportItem.put("monthRank", partnerCount);
//			reportItem.put("yearCount", 0);
//			reportItem.put("yearRank", partnerCount);
//			reportMap.put(view.getPartnerId(), reportItem);
//		}
//		return reportItem;
//	}
//
//	@Override
//	public Map<String, Object> getSaleDailyReportByYear(Long partnerId,
//			Date reportDate) {
//		Map<String, Object> map = new HashMap<String, Object>();
//		try{
//			// 查询扫码日报发送邮件配置
//			Map<String, Object> paramMap = new HashMap<String, Object>();
//			paramMap.put("partnerInCondition", "(" + partnerId + ")");
//			paramMap.put("dateFrom2", DateUtil.getFirstDayOfYear(reportDate));
//			paramMap.put("dateTo2", DateUtil.getLastDayOfYear(reportDate));
//			OrganizationParam param = new OrganizationParam();
//			param.setResourceId("saleDailyReport");
//			Long partnerCount = organizationVoMapper.getPartnersCount(param);
//			map.put("partnerCount", partnerCount);
//			map.put("partnerName", organizationVoMapper.selectByPrimaryKey(partnerId).getOrganizationName());
//			Calendar calendar = Calendar.getInstance();
//			calendar.setTime(reportDate);
//			Map<String, Map<String, Object>[]> allReports = buildDailyReportMapByYear(partnerCount, workShopVoMapper.querySaleDailyReportForInte(paramMap),
//					calendar.get(Calendar.MONTH) + 1, partnerId);
//			for(String reportType : allReports.keySet()){
//				map.put(reportType, allReports.get(reportType));
//			}
//			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		} catch (Exception e) {
//			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			//add by bo.liu 180111
//			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			logger.error(e.getMessage(), e);
//		}
//		return map;
//	}
//	
//	/**
//	 * 重新组装按年查询日报数据
//	 * @param partnerCount
//	 * @param taskReportData
//	 * @param endMonth
//	 * @param partnerId
//	 * @return
//	 */
//	@SuppressWarnings("unchecked")
//	private Map<String, Map<String, Object>[]> buildDailyReportMapByYear(Long partnerCount, List<WorkshopReportView> taskReportData, 
//			int endMonth, Long partnerId){
//		Map<String, Map<String, Object>[]> allReport = new HashMap<String, Map<String, Object>[]>(6);
//		String[] reportTypes = new String[]{"taskLDReport", "taskXDReport", "taskSDReport", "sellinReport", "verificationReport"};
//		for(String reportType : reportTypes){
//			Map<String, Object>[] reportItems = new Map[endMonth];
//			for(int i = 0; i < endMonth; i++){
//				Map<String, Object> reportItem = new HashMap<String, Object>(10);
//				reportItem.put("partnerId", partnerId);
//				reportItem.put("month", i + 1);
//				reportItem.put("monthCount", 0);
//				reportItem.put("monthRank", partnerCount);
//				reportItems[i] = reportItem;
//			}
//			allReport.put(reportType, reportItems);
//		}
//		for(WorkshopReportView view : taskReportData){
//			Map<String, Object> reportItem = allReport.get(reportTypes[view.getViewFlag()])[Integer.parseInt(view.getReportTime()) - 1];
//			reportItem.put("monthCount", view.getCount());
//			reportItem.put("monthRank", view.getRank());
//		}
//		return allReport;
//	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> saveCustomerSales(
			List<PartnerResponsibleMain> addedFuns, List<Long> deletedFuns) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			//删除数据
			if(deletedFuns != null && !deletedFuns.isEmpty()){
				PartnerResponsibleMainExample example = new PartnerResponsibleMainExample();
				example.createCriteria().andIdIn(deletedFuns);
				partnerResponsibleMainMapper.deleteByExample(example);
			}
			//插入数据
			if(addedFuns != null){
				Date now = DateUtil.getCurrentDate();
				Long userId = ContextUtil.getCurUserId();
				for(PartnerResponsibleMain item : addedFuns){
					item.setCreateTime(now);
					item.setCreator(userId);
					partnerResponsibleMainMapper.insertSelective(item);
				}
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error("save fail. " + e.getMessage(), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}
}
