package com.chevron.plc.controller;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.plc.model.StockCodeScanHistoryCondition;
import com.chevron.plc.model.StockDetailCondition;
import com.chevron.plc.service.StockCodeScanHistoryService;
import com.chevron.plc.service.StockDetailService;
import com.common.util.MessageResourceUtil;


@Controller
public class StockDetailController {
	
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";

	
	@Autowired
	private StockDetailService stockDetailService;
	
	private final static Logger log = Logger.getLogger(StockDetailController.class);
	
	@ResponseBody
	@RequestMapping(value="/stockInventory/queryForPages.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> queryForPages(StockDetailCondition praCondition){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			resultMap = stockDetailService.selectInventoryDetailNoByPage(praCondition);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

}
