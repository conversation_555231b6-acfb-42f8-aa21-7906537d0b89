var foo = new $.JsonRpcClient({
    ajaxUrl: '/wxPublicRpc.do'
});
$(document).ready(function () {
    initQueryCtrl();
    initGrid();

});
function initQueryCtrl() {
	var partnerCtrl = Ctrls.PartnerAutoSelect.init('#partnerId', {
		params: {resourceId: 'spInventory'},
		placeholder: '全部'
	});
}

var grid = null, store = null;
function initGrid() {
    BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
        var Grid = Grid,
            Store = Data.Store,
            columns = [{ title: '合伙人名称', dataIndex: 'partnerName', width: '30%', sortable: false},
               		{ title: '合伙人实时库存(L/瓶)', dataIndex: '', sortable: false, width: '30%', renderer: function(value, node){
            	var text = (node.capacity || 0) + 'L/' + (node.quantity || 0) + '瓶';
//        		if(node.capacity){
        			return "<a href='javascript: void(0);' onclick='viewItem(" + node.partnerId + ");'>" + text + "</a>";
//        		}
//            	return text;
            }},
            { title: '门店在途库存(L/瓶)', dataIndex: '', sortable: false, width: '30%', renderer: function(value, node){
            	var text = node.onWayCapacity + 'L/' + node.onWayQuantity + '瓶';
            	if(node.onWayQuantity == 0){
            		return text;
            	}
            	return '<a href="' + common.ctx + 'partnerorder/partnerinventory/partnerworkshoponwaystock.jsp?partnerId=' + node.partnerId + '&onwaycapacityBigerThanZero=true">' + text + '</a>';
            }}];
        var params = $.extend(buildParams(), initParams);
        params.start = initParams.start ? parseInt(initParams.start) : 0;
        store = new Store({
			url: '/partnerinventory/data.do',
			autoLoad:false,
			totalProperty : 'total',
			root: "resultLst",
			remoteSort: true,
			sortField: initParams.field ? initParams.field : 'partnerName',
			sortDirection: initParams.direction ? initParams.direction : 'ASC',
			pageSize:initParams.limit ? (initParams.limit - 0) : 10,
			params: params,
		    proxy : {
				method : 'post'
			}
        });

        grid = new Grid.Grid({
            render: '#partnerinventory_grid',
            columns: columns,
            loadMask: false,
            store: store,
            bbar: {
                pagingBar: true
            }
        });
        grid.render();
        store.set('start', params.start);
        common.initGrid(grid, null, true);
//        queryProductsByParam();
    });
}

//根据参数查询
function queryProductsByParam() {
	store.load(buildParams());
}

function buildParams(){
	return {
		partnerId: $('#partnerId').val(),
		partnerName: $('#partnerId_c input[type=text]').val(),
		start: 0
	};
}

//查看  参数是合伙人id 传到partnerinventoryDetail.jsp的是合伙人id
function viewItem(item) {
    window.location.href = "/partnerorder/partnerinventory/partnerinventoryDetail.jsp?partnerId=" + item;
}