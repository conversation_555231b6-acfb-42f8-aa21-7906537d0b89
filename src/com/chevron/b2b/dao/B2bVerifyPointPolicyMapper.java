package com.chevron.b2b.dao;

import com.chevron.b2b.model.B2bVerifyPointPolicy;
import com.chevron.b2b.model.B2bVerifyPointPolicyExample;
import com.chevron.b2b.model.B2bVerifyPointPolicyParams;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * B2B核销积分政策-wx_t_b2b_verify_point_policy数据库操作对象
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
public interface B2bVerifyPointPolicyMapper {

	/**
	 * 根据主键更新B2B核销积分政策对象(非空属性)
	 * @param record 新B2B核销积分政策对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(B2bVerifyPointPolicy record);

    /**
     * 根据主键删除B2B核销积分政策对象
     * @param id 删除B2B核销积分政策对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询B2B核销积分政策对象
	 * @param id B2B核销积分政策对象主键值
	 * @return B2B核销积分政策对象结果
	 */
    B2bVerifyPointPolicy selectByPrimaryKey(Long id);

 	/**
	 * 插入B2B核销积分政策对象(非空属性)
	 * @param record 被插入B2B核销积分政策对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(B2bVerifyPointPolicy record);

	/**
	 * 更新B2B核销积分政策对象
	 * @param record 被修改B2B核销积分政策对象
	 * @param example 过滤B2B核销积分政策对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") B2bVerifyPointPolicy record, @Param("example") B2bVerifyPointPolicyExample example);

	/**
	 * 删除B2B核销积分政策对象
	 * @param example 被删除B2B核销积分政策对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(B2bVerifyPointPolicyExample example);
    
 	/**
	 * 查询满足条件的B2B核销积分政策对象数量
	 * @param example 过滤B2B核销积分政策对象条件
	 * @return 满足条件B2B核销积分政策对象数量
	 */
    int countByExample(B2bVerifyPointPolicyExample example);

	/**
	 * 单表查询B2B核销积分政策对象
	 * @param example 过滤B2B核销积分政策对象条件
	 * @return 满足条件B2B核销积分政策对象集合
	 */
    List<B2bVerifyPointPolicy> selectByExample(B2bVerifyPointPolicyExample example);

//	/**
//	 * 列表查询B2B核销积分政策对象
//	 * @param params 查询条件
//	 * @return 满足条件B2B核销积分政策对象集合
//	 */
//    List<B2bVerifyPointPolicy> queryByParams(Map<String, Object> params);

	/**
	 * 分页查询B2B核销积分政策对象
	 * @param params 查询条件
	 * @return 满足条件B2B核销积分政策对象集合
	 */
    List<B2bVerifyPointPolicy> queryForPage(B2bVerifyPointPolicyParams params);
//    
//    /**
//         * 根据页面编辑，更新数据
//     */
//    int updateForEdit(B2bVerifyPointPolicy record);
    
    B2bVerifyPointPolicy getVerifyPolicy(Map<String, Object> params);
 }
