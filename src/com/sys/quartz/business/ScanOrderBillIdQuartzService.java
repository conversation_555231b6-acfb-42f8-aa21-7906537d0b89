package com.sys.quartz.business;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;

import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.WxtOrderYYFWSetsVoMapper;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.WxtOrderYYFWSetsVo;
import com.common.config.MyPropertyConfigurer;
import com.common.util.DateUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.email.service.EmailSenderService;
import com.web.ws.sf.SFRouteQueryService;

/**
 * 
 * @Author: bo.liu 2016-12-23 下午4:20:29
 * @Version: $Id$
 * @Desc:
 *        <p>
 *        </p>
 */
@Service
public class ScanOrderBillIdQuartzService {
	public final Logger logerr = Logger.getLogger(ScanOrderBillIdQuartzService.class);
	@Resource
	OrderVoMapper orderVoMapper;

	@Resource
	EmailSenderService emailSenderService;

	@Resource
	SFRouteQueryService sfRouteQueryService;

	@Resource
	WxTPropertiesMapper propertiesMapper;

	@Resource
	WxtOrderYYFWSetsVoMapper orderFWSetsMapper;

	public final static String QUERY_BY_BILLID_TYPE = "1";// 根据运单号
	public final static String QUERY_BY_ORDERID_TYPE = "2";// 根据订单号

	/**
	 * 处理查询运单号状态
	 * 
	 * <AUTHOR> 2016-12-23 下午4:23:56
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void doHandleOrderBillIdTask() {
		try {
			// 1.获取需要查询已发货的订单，状态为5的
			List<OrderVo> lstOrders = getNeedQueryBillIdForOrder();
			if (null == lstOrders || lstOrders.isEmpty()) {
				return;
			}
			List<String> lstBillIds = new ArrayList<String>();
			for (OrderVo orderVo : lstOrders) {
				String billId = orderVo.getBillId();
				if (null != billId && !billId.trim().isEmpty()) {
					lstBillIds.add(billId);
				}
			}
			logerr.info("ljc info---------------------------lstOrders:" + lstOrders.size());
			logerr.info("ljc info---------------------------lstBillIds:" + lstBillIds);
			// 2.调用顺丰接口获取，，已签收的订单信息
			List<String> returnBillIds = sfRouteQueryService.routeQueryFilterNotSign(lstBillIds, QUERY_BY_BILLID_TYPE);// 需要调用顺丰接口，暂时用此代替
			if (null == returnBillIds || returnBillIds.isEmpty()) {
				logerr.info("ljc warning---------------------------returnBillIds is null");
				return;
			}

			// 3.过滤出 ，已签单的订单
			List<OrderVo> lstHasQSOrders = new ArrayList<OrderVo>();
			for (String billId : returnBillIds) {
				for (OrderVo orderVo : lstOrders) {
					String tempBillId = orderVo.getBillId();
					if (billId.equals(tempBillId)) {
						if (null != orderVo.getServiceTime()) {
							Calendar cal = Calendar.getInstance();
							cal.setTime(orderVo.getServiceTime());
							orderVo.setOrderServiceTime(DateUtils.toDateStr(cal, 17));
							lstHasQSOrders.add(orderVo);
						}
					}
				}
			}

			// 4.通过1和2获取过滤出需要发送的订单信息，，此处发送邮件用单线程处理
			if (null == lstHasQSOrders || lstHasQSOrders.isEmpty()) {
				logerr.info("ljc warning---------------------------lstHasQSOrders is null");
				return;
			}
			// 对订单进行排序，按订单来源排序、分组
			Map<String, Object> orderMap = processOrder(lstHasQSOrders);
			Integer j = (Integer) orderMap.get("totalOrderType");
			// boolean isSendResult = false;
			List<OrderVo> lstOrder =null;
			for (int i = 1; i <= j; i++) {
				lstOrder = (List<OrderVo>) orderMap.get("o" + i);
				/*
				 * isSendResult = sendEmailByGetBillIdOrder(lstOrder); //5.发送成功后，需要更新订单状态为已发货
				 * if(isSendResult) { updateOrderStatus(lstOrder); }
				 */
				updateOrderStatus(lstOrder);
			}

		} catch (Exception ex) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			logerr.error("",ex);
		}
	}

	/**
	 * 获取需要查询已发货的订单信息
	 * 
	 * <AUTHOR> 2016-12-23 下午4:25:30
	 * @return
	 */
	public List<OrderVo> getNeedQueryBillIdForOrder() {
		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("orderStatus", OrderVo.ORDER_STATUS_5);
		reqMap.put("orderType", OrderVo.DDFW_ORDER_TYPE);

		List<OrderVo> lstOrders = orderVoMapper.getOrdersByStatus(reqMap);
		return lstOrders;
	}

	/**
	 * 把已签收的订单信息通过邮件的形式发送给大地保险
	 * 
	 * <AUTHOR> 2016-12-23 下午4:27:41
	 * @return
	 */
	public boolean sendEmailByGetBillIdOrder(List<OrderVo> lstOrders) {
		boolean isSendResult = true;
		// 邮件接收者
		String[] accepters = null;// new String[1];
		String fwaccepters = "";
		// 邮件抄送者
		String[] ccaccepters = null;
		// 实时从数据库中查询
		String ccaccepter = "";
		Map<String, Object> reqMap1 = new HashMap<String, Object>();
		reqMap1.put("sourceValue", lstOrders.get(0).getSource());
		WxtOrderYYFWSetsVo orderYYFWSets = orderFWSetsMapper.getWxtOrderYYFWSetsByMap(reqMap1);
		fwaccepters = orderYYFWSets.getEmailFWAccepter();
		ccaccepter = orderYYFWSets.getEmailFWCCAccepter();
		String emailSubject = orderYYFWSets.getSourceName() + "服务订单机油到店通知" + DateUtils.getCurrentDate("yyyyMMddHHmmss");
		if (null != orderYYFWSets.getSourceName() && orderYYFWSets.getSourceName().equals("合众")) {
			emailSubject = "大地服务订单机油到店通知（由Faywage）" + DateUtils.getCurrentDate("yyyyMMddHHmmss");
		}

		try {
			// 接收者
			accepters = fwaccepters.split(";");
			StringBuffer sb = new StringBuffer();
			for (int i = 0; i < accepters.length; i++) {
				if ("".equals(accepters[i])) {
					continue;
				}
				sb.append(accepters[i]);
				if (i != accepters.length - 1) {
					sb.append(";");
				}
			}
			accepters = sb.toString().split(";");
			// 抄送者
			ccaccepters = ccaccepter.split(";");
			StringBuffer sb1 = new StringBuffer();
			for (int i = 0; i < ccaccepters.length; i++) {
				if ("".equals(ccaccepters[i])) {
					continue;
				}
				sb1.append(ccaccepters[i]);
				if (i != ccaccepters.length - 1) {
					sb1.append(";");
				}
			}
			ccaccepters = sb1.toString().split(";");

			// quartz中使用此获取上下文
			ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
			isSendResult = emailSenderService.sendEmailForOrderQS(context, accepters, ccaccepters, emailSubject, null,
					lstOrders, null, MyPropertyConfigurer.getVal("mail.dd.fworder.ftl"));

			// add by bo.liu 1116 start 判断是否需要单独发送
			String fworderEmailByRegion = orderYYFWSets.getFworderEmailByRegion();
			if (null != fworderEmailByRegion && !fworderEmailByRegion.isEmpty()) {
				logerr.info(
						"ljc info 1116 start---------------------------fworderEmailByRegion:" + fworderEmailByRegion);
				// 组装配置区域对于的邮箱信息
				String[] fwemailInfos = fworderEmailByRegion.split(";");
				Map<String, Object> setEmailMap = new HashMap<String, Object>();
				for (String str : fwemailInfos) {
					// key:value
					String[] regionEmailInfos = str.split(":");
					setEmailMap.put(regionEmailInfos[0], regionEmailInfos[1]);
				}
				setEmailMap.put("emailsubject", emailSubject);
				sndFWOrderByRegion(lstOrders, setEmailMap);
				logerr.info("ljc info 1116 end---------------------------fworderEmailByRegion:" + fworderEmailByRegion
						+ "--fwemailInfos:" + fwemailInfos);
			}
			// end
		} catch (Exception e) {
			logerr.error("",e);
			isSendResult = false;
		}
		return isSendResult;
	}

	public Map<String, Object> processOrder(List<OrderVo> lstOrders) throws Exception {

		Collections.sort(lstOrders, new Comparator<OrderVo>() {
			public int compare(OrderVo o1, OrderVo o2) {
				return o1.getSource().compareTo(o2.getSource());
			}
		});
		Map<String, Object> orderMap = new HashMap<String, Object>();
		int j = 0;
		String lastOrderSource = "";
		List<OrderVo> tmpOrderLst = null;
		for (OrderVo orderTmp : lstOrders) {
			String orderSource = orderTmp.getSource();
			if (!lastOrderSource.equals(orderSource)) {
				tmpOrderLst = new ArrayList<OrderVo>();
				j = j + 1;

			}
			tmpOrderLst.add(orderTmp);
			orderMap.put("o" + j, tmpOrderLst);
			lastOrderSource = orderSource;
		}
		orderMap.put("totalOrderType", j);
		return orderMap;

	}

	/**
	 * 更新已签收订单信息
	 * 
	 * <AUTHOR> 2016-12-23 下午4:54:33
	 * @param lstOrders
	 */
	public void updateOrderStatus(List<OrderVo> lstOrders) {
		Map<String, Object> reqMap = new HashMap<String, Object>();
		List<OrderVo> updateOrderLst = new ArrayList<OrderVo>();
		for (OrderVo orderVo : lstOrders) {
			orderVo.setStatus(OrderVo.ORDER_STATUS_7);
			updateOrderLst.add(orderVo);

		}

		if (null == updateOrderLst || updateOrderLst.isEmpty()) {
			return;
		}

		// liyu: 分批次来执行,一次300条记录
		int pageSize = 300;
		int totalRows = updateOrderLst.size();
		if (totalRows > pageSize) {
			int pagecount = 0;
			int m = totalRows % pageSize;
			if (m > 0) {
				pagecount = totalRows / pageSize + 1;
			} else {
				pagecount = totalRows / pageSize;
			}

			List<OrderVo> subList = null;
			for (int i = 1; i <= pagecount; i++) {
				int fromIndex = (i - 1) * pageSize;
				int toIndex = 0;
				if (m > 0) {
					if (i == pagecount) { // 最后一页
						toIndex = totalRows;
					} else {
						toIndex = pageSize * (i);
					}
				} else {
					toIndex = pageSize * (i);
				}
				subList = updateOrderLst.subList(fromIndex, toIndex);
				reqMap.put("orderLst", subList);
				orderVoMapper.batchUpdateOrderLstByOrderNo(reqMap);
			}
		} else {
			reqMap.put("orderLst", updateOrderLst);
			orderVoMapper.batchUpdateOrderLstByOrderNo(reqMap);
		}
	}

	public void sndFWOrderByRegion(List<OrderVo> lstOrders, Map<String, Object> dataMap) {
		logerr.info("ljc info 1116 start---------------------------sndFWOrderByRegion");
		// 按区域排序
		Map<String, Object> orderMap = processOrderByRegion(lstOrders);
		Integer j = (Integer) orderMap.get("totalOrderRegion");
		List<OrderVo> tmpOrderLst = null;
		for (int i = 1; i <= j; i++) {
			tmpOrderLst = (List<OrderVo>) orderMap.get("o" + i);
			sndEmailForFWOrderByRegion(tmpOrderLst, dataMap);
		}
		logerr.info("ljc info 1116 end---------------------------sndFWOrderByRegion");
	}

	private void sndEmailForFWOrderByRegion(List<OrderVo> tmpOrderLst, Map<String, Object> dataMap) {
		logerr.info("ljc info 1116 start---------------------------sndEmailForFWOrderByRegion");
		// 获取订单的区域
		String orderRegion = tmpOrderLst.get(0).getRegionName();
		if (null == orderRegion || orderRegion.isEmpty()) {
			logerr.info(
					"ljc info 0322---------------------------sndEmailForFWOrderByRegion orderRegion：" + orderRegion);
			return;
		}
		// 组装配置区域对于的邮箱信息
		String emailInfos = (String) dataMap.get(orderRegion);
		String[] emails = emailInfos.split(",");

		// 接收者
		String[] acceptEmails = { emails[0] };
		int ccLength = 1;
		if (emails.length > 1) {
			ccLength = emails.length - 1;
		}
		// 抄送者
		String[] ccEmails = new String[ccLength];
		if (emails.length == 1) {
			ccEmails[0] = emails[0];
		} else {
			for (int j = 1; j < emails.length; j++) {
				ccEmails[j - 1] = emails[j];
			}
		}
		// 发送邮件
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		boolean isSuccess = emailSenderService.sendEmailForOrderQS(context, acceptEmails, ccEmails,
				(String) dataMap.get("emailsubject"), null, tmpOrderLst, null,
				MyPropertyConfigurer.getVal("mail.dd.fworder.ftl"));
		logerr.info("ljc info 1116 end---------------------------sndEmailForFWOrderByRegion,isSuccess:" + isSuccess);

	}

	public Map<String, Object> processOrderByRegion(List<OrderVo> lstOrders) {
		// 按区域排序
		Collections.sort(lstOrders, new Comparator<OrderVo>() {
			public int compare(OrderVo o1, OrderVo o2) {
				return o1.getRegionName().compareTo(o2.getRegionName());
			}
		});
		Map<String, Object> orderMap = new HashMap<String, Object>();
		int j = 0;
		String lastRegionName = "";
		List<OrderVo> tmpOrderLst = null;
		for (OrderVo orderTmp : lstOrders) {
			String orderRegionName = orderTmp.getRegionName();
			System.out.println("processOrderByRegion:" + orderRegionName + " " + lastRegionName);
			if (!lastRegionName.equals(orderRegionName)) {
				tmpOrderLst = new ArrayList<OrderVo>();
				j = j + 1;

			}
			tmpOrderLst.add(orderTmp);
			orderMap.put("o" + j, tmpOrderLst);
			lastRegionName = orderRegionName;
		}
		orderMap.put("totalOrderRegion", j);
		return orderMap;
	}
}
