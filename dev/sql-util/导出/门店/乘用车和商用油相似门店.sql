select x2.* from (
select *,
ROW_NUMBER() OVER (PARTITION BY x1.id ORDER BY x1.match_weight DESC, x1.id2 desc) AS rn from (
select wp1.partner_id, o1.organization_name, u.ch_name "DSR", w1.id, w1.work_shop_name "商用油门店", w1.work_shop_address, 
(case when w1.status='0' then '潜在' when w1.status='3' then '合作'
when w1.status='-10' then '待抽查' when w1.status='-3' then '不合格'
 when w1.status='-5' then '待完善' else w1.status end) "门店状态", w1.contact_person_tel, w1.create_time "录入时间",
w2.id id2, w2.work_shop_name "乘用车门店", w2.work_shop_address work_shop_address2, u2.ch_name "DSR2", 
(case when w2.status='0' then '潜在' when w2.status='3' then '合作'
when w2.status='-10' then '待抽查' when w2.status='-3' then '不合格'
 when w2.status='-5' then '待完善' when w2.status='1' then '已功店' else w2.status end) "门店状态2", w2.contact_person_tel contact_person_tel2, w2.create_time "录入时间2",
(case when w1.contact_person_tel is not null and len(w1.contact_person_tel)>6 and w1.contact_person_tel=w2.contact_person_tel then 30000 else 0 end) +
(case when w1.region_id=w2.region_id then 5000 else 0 end) +
(case when wp1.partner_id=wp2.partner_id then 1000 else 0 end) +
(case when charindex('号', w1.work_shop_address) > 0 and charindex(w1.work_shop_address, w2.work_shop_address) > 0 then 100 else 0 end) +
(case when charindex('号', w2.work_shop_address) > 0 and  charindex(w2.work_shop_address, w1.work_shop_address) > 0 then 100 else 0 end) +
(case when charindex(w1.work_shop_name, w2.work_shop_name) > 0 then 300 else 0 end) +
(case when charindex(w2.work_shop_name, w1.work_shop_name) > 0 then 300 else 0 end) match_weight
from wx_t_work_shop w1
left join wx_t_workshop_partner wp1 on wp1.workshop_id=w1.id
left join wx_t_organization o1 on o1.id=wp1.partner_id
left join wx_t_work_shop w2 on w2.from_source=1 and w2.delete_flag=0
left join wx_t_workshop_partner wp2 on wp2.workshop_id=w2.id
left join wx_t_user u on u.user_id=w1.excute_user_id
left join wx_t_user u2 on u2.user_id=w2.excute_user_id
where w1.from_source=2 and w1.delete_flag=0 and w1.id!=w2.id and w1.status='3' and w2.status='3'
) x1 where x1.match_weight>=6100
) x2 where x2.rn=1 and x2.partner_id!=9
order by x2.partner_id, x2.DSR