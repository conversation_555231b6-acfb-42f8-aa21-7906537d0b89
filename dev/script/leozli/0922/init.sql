insert into wx_t_excel_export_conf (file_name,order_no,file_name_postfix,export_sql,enable_flag) values 
('大地服务订单', 10, '${now.yyyyMMdd}','SELECT row_number() over(order by id desc) "序号", order_no "订单号", plate_number "车牌", service_time "预约服务时间", work_shop_name "服务门店", receive_user_name "收货人", receive_phone_no "收货人电话", region_name "区域", address "收货详细地址" FROM wx_t_order WHERE source=''DDBX'' AND order_type=''DA''',1);
insert into wx_t_excel_export_conf (file_name,order_no,file_name_postfix,export_sql,enable_flag) values 
('大地新保订单', 20, '${now.yyyyMMdd}','SELECT row_number() over(order by t_o.create_time desc, t_o.id desc) "序号", order_no "订单号", phone_no "电话号码", plate_number "车牌号", car_type "车型", region_name "区域", effective_time "生效时间", invalid_time "失效时间", oil_injection "加注机油升数",t_p.viscosity "机油规格",t_p_t.card_type "卡类型",t_p_t.buy_xb_times "次数", /*t_p_t.org_price,*/t_o.create_time "订单时间"   FROM wx_t_order t_o LEFT JOIN wx_t_preferential_type t_p_t ON t_p_t.type_code = t_o.type LEFT JOIN wx_t_order_line T_O_L ON t_o.id = T_O_L.order_id inner join wx_t_product t_p on t_p.id = T_O_L.product_id WHERE source=''DDBX'' AND order_type=''DP'' group by order_no, phone_no, plate_number, car_type, region_name, effective_time, invalid_time, oil_injection,t_p.viscosity,t_p_t.card_type,t_p_t.buy_xb_times, /*t_p_t.org_price,*/t_o.create_time, t_o.id',1);
insert into wx_t_excel_export_conf (file_name,order_no,file_name_postfix,export_sql,enable_flag) values 
('激活门店', 30, '${now.yyyyMMdd}', 'select row_number() over(order by l2.organization_name, t.create_time desc, t.id desc) "序号", l2.organization_name "合伙人名称", t.work_shop_name "门店名称", t3.region_name "所属省份", t2.region_name "所属城市", t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型", (select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status=''3'' and wss.workshop_id=t.id order by wss.create_time desc) "激活时间",(select u1.ch_name from wx_t_user u1 where u1.user_id=t.excute_user_id) "执行人", t.remark "备注", from wx_t_work_shop t left join wx_t_region t1 on t.region_id=t1.id left join wx_t_region t2 on t1.parent_id=t2.id left join wx_t_region t3 on t2.parent_id=t3.id join wx_t_workshop_partner l1 on l1.workshop_id=t.id and l1.relation_type=''trade'' join wx_t_organization l2 on l1.partner_id=l2.id where t.status=''3'' group by l2.organization_name, t.work_shop_name, t1.region_name, t.work_shop_address,t.type,t.id,t.create_time',1);
insert into wx_t_excel_export_conf (file_name,order_no,file_name_postfix,export_sql,enable_flag) values 
('合伙人巡店明细', 40, '${now.yyyyMMdd}','select row_number() over(order by l2.organization_name, t3.region_name, t2.region_name, t1.region_name, s.xg_sj desc, s.task_id desc) "序号", l2.organization_name "合伙人名称", t.work_shop_name "门店名称", t3.region_name "所属省份", t2.region_name "所属城市", t1.region_name "所属区域", t.work_shop_address "门店地址",t.type "门店类型", (select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status=''3'' and wss.workshop_id=t.id order by wss.create_time desc, s.task_id desc) "激活时间", s.xg_sj "巡店完成时间" from wx_t_work_shop t left join wx_t_region t1 on t.region_id=t1.id left join wx_t_region t2 on t1.parent_id=t2.id left join wx_t_region t3 on t2.parent_id=t3.id join wx_t_workshop_partner l1 on l1.workshop_id=t.id and l1.relation_type=''trade'' join wx_t_organization l2 on l1.partner_id=l2.id join wx_task_sub s on  s.org_id=t.id join wx_task_main m on m.task_main_id = s.task_main_id where convert(int, t.status)>-1 and s.task_status=4 and m.tmb_type_code=''TT_2_XD'' group by l2.organization_name, t.work_shop_name, t3.region_name, t2.region_name, t1.region_name, t.work_shop_address,t.type, t.id, s.xg_sj, s.task_id',1);
insert into wx_t_excel_export_conf (file_name,order_no,file_name_postfix,export_sql,enable_flag) values 
('门店扫码明细', 50, '${now.yyyyMMdd}','select row_number() over(order by l2.organization_name, a3.creation_time desc, a3.id desc) "序号", l2.organization_name "合伙人名称", ws.work_shop_name "门店名称", t1.region_name "所属区域", ws.work_shop_address "门店地址", a3.sku, p.name "产品名称", a3.capacity "扫码升数(L)", a3.creation_time "扫码时间" from wx_v_oil_verification a3 join wx_t_work_shop ws on a3.workshop_id=ws.id left join wx_t_region t1 on ws.region_id=t1.id left join wx_t_region t2 on t1.parent_id=t2.id left join wx_t_region t3 on t2.parent_id=t3.id join wx_t_workshop_partner l1 on l1.workshop_id=a3.workshop_id and l1.relation_type=''trade'' join wx_t_organization l2 on l1.partner_id=l2.id left join wx_t_product p on a3.sku=p.sku where  l1.partner_id not in (9) group by l2.organization_name, ws.work_shop_name, t1.region_name, ws.work_shop_address, a3.sku, p.name, a3.capacity, a3.creation_time, a3.id',1);
insert into wx_t_excel_export_conf (file_name,order_no,file_name_postfix,export_sql,enable_flag) values 
('扫码异常', 60, '${now.yyyyMMdd}','SELECT ext_property1 "异常扫码二维码",ext_property10 "sku",ext_property11 "产品名称",ext_property2 "异常扫码技师编号",ext_property7 "异常扫码合伙人",ext_property8 "异常扫码门店",ext_property9 "异常扫码技师",create_time "异常扫码时间",ext_property3 "已扫码合伙人",ext_property4 "已扫码门店",ext_property5 "已扫码技师",ext_property6 "已扫码时间"FROM wx_log where log_type=''exists_MechanicQrcode'' and ext_property7 != ''测试SP'' order by create_time desc',0);

