INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'mktQuartzJob', N'WorkGroup', N'1', N'0 0 9 * * ?', N'每天早上9点执行', NULL, N'1', N'mktQuartzJob', N'sendExpenseOverspendNotifyEmail', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '费用超支邮件提醒')
