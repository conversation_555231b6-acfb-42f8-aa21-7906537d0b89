package com.sys.permission.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ResPermissionTypeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ResPermissionTypeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPermissionTypeIdIsNull() {
            addCriterion("permission_type_id is null");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdIsNotNull() {
            addCriterion("permission_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdEqualTo(Long value) {
            addCriterion("permission_type_id =", value, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdNotEqualTo(Long value) {
            addCriterion("permission_type_id <>", value, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdGreaterThan(Long value) {
            addCriterion("permission_type_id >", value, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("permission_type_id >=", value, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdLessThan(Long value) {
            addCriterion("permission_type_id <", value, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("permission_type_id <=", value, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdIn(List<Long> values) {
            addCriterion("permission_type_id in", values, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdNotIn(List<Long> values) {
            addCriterion("permission_type_id not in", values, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdBetween(Long value1, Long value2) {
            addCriterion("permission_type_id between", value1, value2, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("permission_type_id not between", value1, value2, "permissionTypeId");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeIsNull() {
            addCriterion("permission_type_code is null");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeIsNotNull() {
            addCriterion("permission_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeEqualTo(String value) {
            addCriterion("permission_type_code =", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeNotEqualTo(String value) {
            addCriterion("permission_type_code <>", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeGreaterThan(String value) {
            addCriterion("permission_type_code >", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("permission_type_code >=", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeLessThan(String value) {
            addCriterion("permission_type_code <", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("permission_type_code <=", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeLike(String value) {
            addCriterion("permission_type_code like", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeNotLike(String value) {
            addCriterion("permission_type_code not like", value, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeIn(List<String> values) {
            addCriterion("permission_type_code in", values, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeNotIn(List<String> values) {
            addCriterion("permission_type_code not in", values, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeBetween(String value1, String value2) {
            addCriterion("permission_type_code between", value1, value2, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeCodeNotBetween(String value1, String value2) {
            addCriterion("permission_type_code not between", value1, value2, "permissionTypeCode");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescIsNull() {
            addCriterion("permission_type_desc is null");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescIsNotNull() {
            addCriterion("permission_type_desc is not null");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescEqualTo(String value) {
            addCriterion("permission_type_desc =", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescNotEqualTo(String value) {
            addCriterion("permission_type_desc <>", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescGreaterThan(String value) {
            addCriterion("permission_type_desc >", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescGreaterThanOrEqualTo(String value) {
            addCriterion("permission_type_desc >=", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescLessThan(String value) {
            addCriterion("permission_type_desc <", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescLessThanOrEqualTo(String value) {
            addCriterion("permission_type_desc <=", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescLike(String value) {
            addCriterion("permission_type_desc like", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescNotLike(String value) {
            addCriterion("permission_type_desc not like", value, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescIn(List<String> values) {
            addCriterion("permission_type_desc in", values, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescNotIn(List<String> values) {
            addCriterion("permission_type_desc not in", values, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescBetween(String value1, String value2) {
            addCriterion("permission_type_desc between", value1, value2, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andPermissionTypeDescNotBetween(String value1, String value2) {
            addCriterion("permission_type_desc not between", value1, value2, "permissionTypeDesc");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeIsNull() {
            addCriterion("resource_type_code is null");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeIsNotNull() {
            addCriterion("resource_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeEqualTo(String value) {
            addCriterion("resource_type_code =", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeNotEqualTo(String value) {
            addCriterion("resource_type_code <>", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeGreaterThan(String value) {
            addCriterion("resource_type_code >", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("resource_type_code >=", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeLessThan(String value) {
            addCriterion("resource_type_code <", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("resource_type_code <=", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeLike(String value) {
            addCriterion("resource_type_code like", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeNotLike(String value) {
            addCriterion("resource_type_code not like", value, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeIn(List<String> values) {
            addCriterion("resource_type_code in", values, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeNotIn(List<String> values) {
            addCriterion("resource_type_code not in", values, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeBetween(String value1, String value2) {
            addCriterion("resource_type_code between", value1, value2, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andResourceTypeCodeNotBetween(String value1, String value2) {
            addCriterion("resource_type_code not between", value1, value2, "resourceTypeCode");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(Date value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(Date value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(Date value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(Date value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(Date value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<Date> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<Date> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(Date value1, Date value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(Date value1, Date value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionIsNull() {
            addCriterion("permission_expression is null");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionIsNotNull() {
            addCriterion("permission_expression is not null");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionEqualTo(String value) {
            addCriterion("permission_expression =", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionNotEqualTo(String value) {
            addCriterion("permission_expression <>", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionGreaterThan(String value) {
            addCriterion("permission_expression >", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionGreaterThanOrEqualTo(String value) {
            addCriterion("permission_expression >=", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionLessThan(String value) {
            addCriterion("permission_expression <", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionLessThanOrEqualTo(String value) {
            addCriterion("permission_expression <=", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionLike(String value) {
            addCriterion("permission_expression like", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionNotLike(String value) {
            addCriterion("permission_expression not like", value, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionIn(List<String> values) {
            addCriterion("permission_expression in", values, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionNotIn(List<String> values) {
            addCriterion("permission_expression not in", values, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionBetween(String value1, String value2) {
            addCriterion("permission_expression between", value1, value2, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andPermissionExpressionNotBetween(String value1, String value2) {
            addCriterion("permission_expression not between", value1, value2, "permissionExpression");
            return (Criteria) this;
        }

        public Criteria andSequenceNoIsNull() {
            addCriterion("sequence_no is null");
            return (Criteria) this;
        }

        public Criteria andSequenceNoIsNotNull() {
            addCriterion("sequence_no is not null");
            return (Criteria) this;
        }

        public Criteria andSequenceNoEqualTo(Integer value) {
            addCriterion("sequence_no =", value, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoNotEqualTo(Integer value) {
            addCriterion("sequence_no <>", value, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoGreaterThan(Integer value) {
            addCriterion("sequence_no >", value, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("sequence_no >=", value, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoLessThan(Integer value) {
            addCriterion("sequence_no <", value, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoLessThanOrEqualTo(Integer value) {
            addCriterion("sequence_no <=", value, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoIn(List<Integer> values) {
            addCriterion("sequence_no in", values, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoNotIn(List<Integer> values) {
            addCriterion("sequence_no not in", values, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoBetween(Integer value1, Integer value2) {
            addCriterion("sequence_no between", value1, value2, "sequenceNo");
            return (Criteria) this;
        }

        public Criteria andSequenceNoNotBetween(Integer value1, Integer value2) {
            addCriterion("sequence_no not between", value1, value2, "sequenceNo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}