package com.chevron.pms.dao;

import com.chevron.pms.model.RegionVo;
import com.chevron.pms.model.RegionVoExample;
import com.chevron.pms.model.RegionVoTree;
import com.chevron.pms.model.TertiaryRegionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RegionVoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RegionVo record);

    int insertSelective(RegionVo record);

    List<RegionVo> selectByExample(RegionVoExample example);

    RegionVo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RegionVo record, @Param("example") RegionVoExample example);

    int updateByExample(@Param("record") RegionVo record, @Param("example") RegionVoExample example);

    int updateByPrimaryKeySelective(RegionVo record);

    int updateByPrimaryKey(RegionVo record);
    
    //add by bo.liu 0818 start
    //根据区域id获取对应的城市id，省市id
    RegionVo getRegionParentIdByRegionId(Long regionId);

    //end by bo.liu
    
    List<RegionVo> selChildrenByParentId(Long provId);
    
    List<RegionVo> selRegionByParams(Map<String, Object> params);
    
    List<RegionVo> selDistByParams(Map<String, Object> params);
  
    List<RegionVo> selectDistByProvinceId(Long provId);
    
    List<RegionVo> selectDistCityProvinceByParterId(Long partnerId);

    List<Map<String, String>>   selFullRegionByDist(Long distId);
    
    //读取根节点
    RegionVo selectByRegionCode(Map<String,Object> reqMap);
    // 读取除根节点以外的数据
    List<RegionVoTree> selectByForTreeNodes(Map<String,Object> reqMap);
    //导入数据
    int insertRegionSelective(RegionVoTree record);
    //检查是否存在子节点
    int selectCountAllChildRegion(Long regionId);
    //更新数据
    int updateRegion(RegionVoTree record);
  
    List<RegionVoTree> selectByRegionByParentId(Map<String,Object> reqMap);
    
    RegionVo selectByRegionCodenew(String regionCode);
    RegionVo selectByRegionCodenew2(String regionCode);
    
    List<RegionVo> getProvCityDistByRegionCode(String regionCode);
    
    List<RegionVo> selectDistsDetailByDistIds(@Param("distIdList")List<Long> distIdList);
    
    List<Long> getRegionIdsByRegionName(@Param("regionName")String regionName);
    
    List<String> getRegionIdsByParentRegionName(Map<String,Object> reqMap);
    
    List<String> getAllAreas();
    
    RegionVo getRegionNameByRegionId(Long regionId);

    Long getRegionIdByCityName(String cityName);

    List<Map<String,String>> getRegionByDistributorId(Long distributorId);

    List<TertiaryRegionVo> listRegionByIds(@Param("ids") List<Long> ids);
}