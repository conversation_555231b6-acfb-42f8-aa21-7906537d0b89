package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleDiscDetail {
    private Long id;

    private String tModelId;

    private String discName;

    private String discPartsNo;

    private String discPurchasePrice;

    private String discSalePrice;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getDiscName() {
        return discName;
    }

    public void setDiscName(String discName) {
        this.discName = discName == null ? null : discName.trim();
    }

    public String getDiscPartsNo() {
        return discPartsNo;
    }

    public void setDiscPartsNo(String discPartsNo) {
        this.discPartsNo = discPartsNo == null ? null : discPartsNo.trim();
    }

    public String getDiscPurchasePrice() {
        return discPurchasePrice;
    }

    public void setDiscPurchasePrice(String discPurchasePrice) {
        this.discPurchasePrice = discPurchasePrice == null ? null : discPurchasePrice.trim();
    }

    public String getDiscSalePrice() {
        return discSalePrice;
    }

    public void setDiscSalePrice(String discSalePrice) {
        this.discSalePrice = discSalePrice == null ? null : discSalePrice.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}