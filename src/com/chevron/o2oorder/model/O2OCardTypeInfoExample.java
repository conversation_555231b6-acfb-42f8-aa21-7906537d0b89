package com.chevron.o2oorder.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class O2OCardTypeInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public O2OCardTypeInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNull() {
            addCriterion("card_type is null");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNotNull() {
            addCriterion("card_type is not null");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualTo(String value) {
            addCriterion("card_type =", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualTo(String value) {
            addCriterion("card_type <>", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThan(String value) {
            addCriterion("card_type >", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualTo(String value) {
            addCriterion("card_type >=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThan(String value) {
            addCriterion("card_type <", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualTo(String value) {
            addCriterion("card_type <=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLike(String value) {
            addCriterion("card_type like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotLike(String value) {
            addCriterion("card_type not like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeIn(List<String> values) {
            addCriterion("card_type in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotIn(List<String> values) {
            addCriterion("card_type not in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeBetween(String value1, String value2) {
            addCriterion("card_type between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotBetween(String value1, String value2) {
            addCriterion("card_type not between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesIsNull() {
            addCriterion("card_service_times is null");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesIsNotNull() {
            addCriterion("card_service_times is not null");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesEqualTo(String value) {
            addCriterion("card_service_times =", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesNotEqualTo(String value) {
            addCriterion("card_service_times <>", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesGreaterThan(String value) {
            addCriterion("card_service_times >", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesGreaterThanOrEqualTo(String value) {
            addCriterion("card_service_times >=", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesLessThan(String value) {
            addCriterion("card_service_times <", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesLessThanOrEqualTo(String value) {
            addCriterion("card_service_times <=", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesLike(String value) {
            addCriterion("card_service_times like", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesNotLike(String value) {
            addCriterion("card_service_times not like", value, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesIn(List<String> values) {
            addCriterion("card_service_times in", values, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesNotIn(List<String> values) {
            addCriterion("card_service_times not in", values, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesBetween(String value1, String value2) {
            addCriterion("card_service_times between", value1, value2, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardServiceTimesNotBetween(String value1, String value2) {
            addCriterion("card_service_times not between", value1, value2, "cardServiceTimes");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceIsNull() {
            addCriterion("card_total_price is null");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceIsNotNull() {
            addCriterion("card_total_price is not null");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceEqualTo(String value) {
            addCriterion("card_total_price =", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceNotEqualTo(String value) {
            addCriterion("card_total_price <>", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceGreaterThan(String value) {
            addCriterion("card_total_price >", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceGreaterThanOrEqualTo(String value) {
            addCriterion("card_total_price >=", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceLessThan(String value) {
            addCriterion("card_total_price <", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceLessThanOrEqualTo(String value) {
            addCriterion("card_total_price <=", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceLike(String value) {
            addCriterion("card_total_price like", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceNotLike(String value) {
            addCriterion("card_total_price not like", value, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceIn(List<String> values) {
            addCriterion("card_total_price in", values, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceNotIn(List<String> values) {
            addCriterion("card_total_price not in", values, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceBetween(String value1, String value2) {
            addCriterion("card_total_price between", value1, value2, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardTotalPriceNotBetween(String value1, String value2) {
            addCriterion("card_total_price not between", value1, value2, "cardTotalPrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceIsNull() {
            addCriterion("card_sale_price is null");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceIsNotNull() {
            addCriterion("card_sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceEqualTo(String value) {
            addCriterion("card_sale_price =", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceNotEqualTo(String value) {
            addCriterion("card_sale_price <>", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceGreaterThan(String value) {
            addCriterion("card_sale_price >", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceGreaterThanOrEqualTo(String value) {
            addCriterion("card_sale_price >=", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceLessThan(String value) {
            addCriterion("card_sale_price <", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceLessThanOrEqualTo(String value) {
            addCriterion("card_sale_price <=", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceLike(String value) {
            addCriterion("card_sale_price like", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceNotLike(String value) {
            addCriterion("card_sale_price not like", value, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceIn(List<String> values) {
            addCriterion("card_sale_price in", values, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceNotIn(List<String> values) {
            addCriterion("card_sale_price not in", values, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceBetween(String value1, String value2) {
            addCriterion("card_sale_price between", value1, value2, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardSalePriceNotBetween(String value1, String value2) {
            addCriterion("card_sale_price not between", value1, value2, "cardSalePrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceIsNull() {
            addCriterion("card_discount_price is null");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceIsNotNull() {
            addCriterion("card_discount_price is not null");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceEqualTo(String value) {
            addCriterion("card_discount_price =", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceNotEqualTo(String value) {
            addCriterion("card_discount_price <>", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceGreaterThan(String value) {
            addCriterion("card_discount_price >", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceGreaterThanOrEqualTo(String value) {
            addCriterion("card_discount_price >=", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceLessThan(String value) {
            addCriterion("card_discount_price <", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceLessThanOrEqualTo(String value) {
            addCriterion("card_discount_price <=", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceLike(String value) {
            addCriterion("card_discount_price like", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceNotLike(String value) {
            addCriterion("card_discount_price not like", value, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceIn(List<String> values) {
            addCriterion("card_discount_price in", values, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceNotIn(List<String> values) {
            addCriterion("card_discount_price not in", values, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceBetween(String value1, String value2) {
            addCriterion("card_discount_price between", value1, value2, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardDiscountPriceNotBetween(String value1, String value2) {
            addCriterion("card_discount_price not between", value1, value2, "cardDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeIsNull() {
            addCriterion("card_createtime is null");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeIsNotNull() {
            addCriterion("card_createtime is not null");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeEqualTo(Date value) {
            addCriterion("card_createtime =", value, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeNotEqualTo(Date value) {
            addCriterion("card_createtime <>", value, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeGreaterThan(Date value) {
            addCriterion("card_createtime >", value, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("card_createtime >=", value, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeLessThan(Date value) {
            addCriterion("card_createtime <", value, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeLessThanOrEqualTo(Date value) {
            addCriterion("card_createtime <=", value, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeIn(List<Date> values) {
            addCriterion("card_createtime in", values, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeNotIn(List<Date> values) {
            addCriterion("card_createtime not in", values, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeBetween(Date value1, Date value2) {
            addCriterion("card_createtime between", value1, value2, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardCreatetimeNotBetween(Date value1, Date value2) {
            addCriterion("card_createtime not between", value1, value2, "cardCreatetime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeIsNull() {
            addCriterion("card_invalidtime is null");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeIsNotNull() {
            addCriterion("card_invalidtime is not null");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeEqualTo(Date value) {
            addCriterion("card_invalidtime =", value, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeNotEqualTo(Date value) {
            addCriterion("card_invalidtime <>", value, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeGreaterThan(Date value) {
            addCriterion("card_invalidtime >", value, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("card_invalidtime >=", value, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeLessThan(Date value) {
            addCriterion("card_invalidtime <", value, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeLessThanOrEqualTo(Date value) {
            addCriterion("card_invalidtime <=", value, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeIn(List<Date> values) {
            addCriterion("card_invalidtime in", values, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeNotIn(List<Date> values) {
            addCriterion("card_invalidtime not in", values, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeBetween(Date value1, Date value2) {
            addCriterion("card_invalidtime between", value1, value2, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardInvalidtimeNotBetween(Date value1, Date value2) {
            addCriterion("card_invalidtime not between", value1, value2, "cardInvalidtime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeIsNull() {
            addCriterion("card_effectivetime is null");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeIsNotNull() {
            addCriterion("card_effectivetime is not null");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeEqualTo(Date value) {
            addCriterion("card_effectivetime =", value, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeNotEqualTo(Date value) {
            addCriterion("card_effectivetime <>", value, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeGreaterThan(Date value) {
            addCriterion("card_effectivetime >", value, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("card_effectivetime >=", value, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeLessThan(Date value) {
            addCriterion("card_effectivetime <", value, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeLessThanOrEqualTo(Date value) {
            addCriterion("card_effectivetime <=", value, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeIn(List<Date> values) {
            addCriterion("card_effectivetime in", values, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeNotIn(List<Date> values) {
            addCriterion("card_effectivetime not in", values, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeBetween(Date value1, Date value2) {
            addCriterion("card_effectivetime between", value1, value2, "cardEffectivetime");
            return (Criteria) this;
        }

        public Criteria andCardEffectivetimeNotBetween(Date value1, Date value2) {
            addCriterion("card_effectivetime not between", value1, value2, "cardEffectivetime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}