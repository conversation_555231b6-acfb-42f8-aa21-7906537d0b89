package com.common.token.util;

import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TokenUtil {

	protected static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";
	protected static final String EXPIRE_PERIOD_MINUTES = "10080";
	protected static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
	public static final Logger logger = LoggerFactory.getLogger(TokenUtil.class);

	/**
	 * @param data
	 * @param expirePeriod
	 * @param appSource
	 * @return token
	 * @throws java.security.SignatureException
	 */
	public static String generateToken(String userId, String expirePeriod, String tokenType)
			throws java.security.SignatureException {
		String result;
		try {
			if (StringUtils.isEmpty(tokenType) || StringUtils.isEmpty(userId) || StringUtils.isEmpty(expirePeriod)) {
				throw new Exception("Empty tokenType or data or timeStamp for generateToken");
			}
			String key = "b4f4cc0d-49f9-4cb5-a3ff-a5efdbe88508"; //MyPropertyConfigurer.getVal("tokenTypeKey." + tokenType );
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.MINUTE, Integer.parseInt(expirePeriod));
			String dateStr = sdf.format(cal.getTime());
			result = calculateRFC2104HMAC(userId + dateStr, key) + "#TZ#" + dateStr;
		} catch (Exception e) {
			logger.error("Failed to generate HMAC");
			throw new SignatureException("Failed to generate Token : " + e.getMessage());
		}
		return result;
	}

	public static String generateToken(String userId, String tokenType) throws java.security.SignatureException {
		return generateToken(userId, EXPIRE_PERIOD_MINUTES, tokenType);
	}

	private static String generateTokenWithToken(String userId, String expirePeriod, String tokenType, String requestedToken)
			throws java.security.SignatureException {
		String result;
		try {
			if (StringUtils.isEmpty(tokenType) || StringUtils.isEmpty(userId) || StringUtils.isEmpty(expirePeriod)) {
				throw new Exception("Empty tokenType or data or timeStamp for generateToken");
			}
			String key = "b4f4cc0d-49f9-4cb5-a3ff-a5efdbe88508"; //MyPropertyConfigurer.getVal("tokenTypeKey." + tokenType );
			String requestedExpireDateTimeStampStr = getExpireDateTimeStampAsString(requestedToken);
			result = calculateRFC2104HMAC(userId + requestedExpireDateTimeStampStr, key) + "#TZ#" + requestedExpireDateTimeStampStr;
		} catch (Exception e) {
			logger.error("Failed to generate HMAC");
			throw new SignatureException("Failed to generate Token : " + e.getMessage());
		}
		return result;
	}

	/**
	 * Computes RFC 2104-compliant HMAC signature.
	 * @param data The data to be signed.
	 * @param sharedKey The signing key.
	 * @return The Base64-encoded RFC 2104-compliant HMAC signature.
	 * @throws java.security.SignatureException when signature generation fails
	 */
	protected static String calculateRFC2104HMAC(String data, String sharedKey) throws java.security.SignatureException {
		String result;
		try {

			// get an hmac_sha1 key from the raw key bytes
			SecretKeySpec signingKey = new SecretKeySpec(sharedKey.getBytes(), HMAC_SHA1_ALGORITHM);

			// get an hmac_sha1 Mac instance and initialize with the signing key
			Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
			mac.init(signingKey);

			// compute the hmac on input data bytes
			byte[] rawHmac = mac.doFinal(data.getBytes());

			// base64-encode the hmac
			result = Base64.encodeBase64String(rawHmac);

		} catch (Exception e) {
			logger.error("Failed to generate HMAC");
			throw new SignatureException("Failed to generate HMAC : " + e.getMessage());
		}
		return result;
	}
	/**
	 * @param token
	 * @return
	 * @throws java.security.SignatureException
	 */
	public static String getHMAC(String token)
			throws java.security.SignatureException {
		String result = "";
		try {
			result = token.substring(0, token.indexOf("#TZ#") - 1);
		} catch (Exception e) {
			logger.error("Failed to get token");
			throw new SignatureException("Failed to get Token : "
					+ e.getMessage());
		}
		return result;
	}

	/**
	 * @param token
	 * @return
	 * @throws java.security.SignatureException
	 */
	public static Date getExpireDateTimeStamp(String token)
			throws java.security.SignatureException {
		Date result = null;
		try {
			String dateStr = getExpireDateTimeStampAsString(token);
			result = sdf.parse(dateStr);
		} catch (Exception e) {
			logger.error("Failed to get Expire Date Time Stamp from Token");
			throw new SignatureException(
					"Failed to get Expire Date Time Stamp from Token : "
							+ e.getMessage());
		}
		return result;
	}

	/**
	 * @param token
	 * @return
	 * @throws java.security.SignatureException
	 */
	public static String getExpireDateTimeStampAsString(String token)
			throws java.security.SignatureException {
		String dateStr = null;
		try {
			// String encodedDateStr = token.substring(token.indexOf("#TZ#")+4);
			// String dateStr = new String(Base64.decodeBase64(encodedDateStr));
			dateStr = token.substring(token.indexOf("#TZ#") + 4);
		} catch (Exception e) {
			logger.error("Failed to get Expire Date Time Stamp from Token");
			throw new SignatureException(
					"Failed to get Expire Date Time Stamp from Token : "
							+ e.getMessage());
		}
		return dateStr;
	}

	/**
	 * @param requestedToken
	 * @param generatedToken
	 * @return
	 * @throws Exception
	 */
	public static boolean isValid(String userId, String expirePeriod, String tokenType,String token) throws Exception {
		boolean result = false;
		if (StringUtils.isEmpty(tokenType) || StringUtils.isEmpty(userId)
				|| StringUtils.isEmpty(expirePeriod) || StringUtils.isEmpty(token)) {
			throw new Exception("Empty tokenType or data or timeStamp for generateToken");
		}

		byte[] decodedToken = Base64.decodeBase64(token);
		String requestedToken = new String(decodedToken, "UTF-8");

		try {
			String generatedToken = generateTokenWithToken(userId, expirePeriod, tokenType, requestedToken);
			String requestedHMAC = getHMAC(requestedToken);
			Date requestedExpireDateTimeStamp = getExpireDateTimeStamp(requestedToken);
			String generatedHMAC = getHMAC(generatedToken);
			// Check if the HMAC code is matching or not
			if (!StringUtils.isEmpty(requestedToken)
					&& !StringUtils.isEmpty(generatedToken)
					&& requestedHMAC.equals(generatedHMAC)) {
				Date current = new Date();
				// Now check if this session is already expired or not
				// there is a possibility that HMAC code is matching but it is
				// already expired
				if (requestedExpireDateTimeStamp.after(current)
						|| requestedExpireDateTimeStamp.equals(current)) {
					return true;
				}
			}
		} catch (Exception e) {
			logger.error("Failed to compare two token");
		}
		return result;
	}

	public static boolean isValid(String userId, String tokenType,String token) throws Exception {
		return isValid(userId, EXPIRE_PERIOD_MINUTES, tokenType,token);
	}

	public static String generateTokenLink(String userId, String tokenType,
			String templateUrl, String targetUrl, boolean doDirectly, String... params ) {

		String token = null;
		try {
			token = generateToken(userId, EXPIRE_PERIOD_MINUTES, tokenType);
		} catch (SignatureException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String encodedToken = Base64.encodeBase64URLSafeString(token.getBytes());
		String encodedTemplateUrl = Base64.encodeBase64URLSafeString(templateUrl.getBytes());
		String encodedTargetUrl = Base64.encodeBase64URLSafeString(targetUrl.getBytes());

		StringUtils.join(params, ",");
		return "checkToken/" + userId + "/" +tokenType + "/" +encodedToken + "/autoLogin.do?url="
		+encodedTargetUrl + "&t=" + encodedTemplateUrl  + "&dd=" + doDirectly  + "&p=" + StringUtils.join(params, ",") ;
	}


	public static void main(String[] args) throws Exception {
		String token = generateToken("123", "1440","materialApproval");
		System.out.println(token);
		String encodedRequestedToken = Base64.encodeBase64URLSafeString(token.getBytes());
		System.out.println(encodedRequestedToken);
		byte[] decoded = Base64.decodeBase64(encodedRequestedToken);
	String requestedToken = new String(decoded, "UTF-8");
	System.out.println(requestedToken);
	System.out.println(isValid("123", "1440","materialApproval",token));
	System.out.println(generateTokenLink("1", "materialApprovalDirectly","/material/jsp/approval.jsp","/material/jsp/approval.jsp", true, "39,1499408637797" ));
	}
}
