--积分总控制Promotion.Point.Category，permissionCode-业务权限code。point.exchange.type.permission-权限。

insert into wx_t_dealer_business_fun (dealer_id, business_fun_code, business_custom_id)
select wp.partner_id,'CI_PROMOTION',-1 from wx_t_promotion_partner wp where promotion_id=140

select f.* --into wx_t_dealer_business_fun20210708c
--delete f
from wx_t_dealer_business_fun f
left join wx_t_organization o on o.id=f.dealer_id where business_fun_code='CDM_PROMOTION'

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select a.* from (
select u.user_id, 1 gu, (select r.role_id from wx_t_role r 
where r.ch_role_name='Chevron_CDM_Hero_Product_Admin') ri, 1 s from wx_t_user u left join wx_t_organization o on u.org_id=o.id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where exists (select 1 from wx_t_userrole ur left join wx_t_role r on ur.role_id=r.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer'))
and o.id in (56462,
56478,
56495)
) a where a.ri is not null


select * from wx_t_promotion_point

  select o.id, (select top 1 cos.sales_name_cn from wx_t_partner_o2o_enterprise pe left join dw_customer_org_sales cos on cos.distributor_id=pe.distributor_id where pe.partner_id=o.id) FLSR, o.organization_name "经销商名称", pp1.point_name "积分类型", sum(case when t.point>0 then t.point else 0 end) '收入',
  sum(case when t.point<0 then -t.point else 0 end) '支出', t.create_time "月份"  from (
              SELECT
            case when a.APPLICATION_TYPE='b2b' then (select w.partner_id from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id where we.code=a.APPLICATION_USER_ID) ELSE a.APPLICATION_ORG_ID END as partner_id,ISNULL(a.PROMOTION_TYPE, a.APPLICATION_TYPE) as point_type,
            -cast(pb.ATTRIBUTE1 as decimal(12,2)) AS point, MONTH(a.CREATION_TIME) as create_time
            FROM dbo.wx_t_material_application a
            LEFT JOIN wx_t_organization o ON o.id = a.APPLICATION_ORG_ID
            LEFT JOIN wx_t_point_business pb on a.APPLICATION_CODE = pb.RELATED_CODE
            LEFT JOIN wx_t_user u on u.user_id = a.CREATED_BY
            WHERE 1 = 1
            AND a.DELETE_FLAG = 0
            and pb.BUSINESS_STATUS = 'DONE'
            and pb.BUSINESS_TYPE_CODE not in ('ROLLBACK_POINT')
            AND (a.ATTRIBUTE1 is null or a.ATTRIBUTE1 != 'PROMOTION_EXCHANGE')
            AND a.CREATION_TIME>='2021-01-01'
    UNION ALL SELECT case when pac.POINT_ACCOUNT_TYPE='TC' then ((select w.partner_id from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id where we.code=pac.POINT_ACCOUNT_OWNER_CODE))
	when pac.POINT_ACCOUNT_TYPE='SPBD' then (select u2.org_id from wx_t_user u2 where u2.user_id=pac.POINT_ACCOUNT_OWNER_ID) else pac.POINT_ACCOUNT_OWNER_ID end as partner_id,pp.point_type as point_type,
        cast(plog.MODIFIED_VALUE as decimal(12,2)) as point, MONTH(plog.CREATION_TIME)  as create_time
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on u.user_id = pde.CREATED_BY
        WHERE plog.CREATION_TIME>='2021-01-01') t 
		left join wx_t_promotion_point pp1 on pp1.point_type = t.point_type and pp1.predefine_flag=1
		left join wx_t_organization o on o.id=t.partner_id
		where o.id!=9 and pp1.point_code='CONSUMER_HERO_PRODUCT_POINT'
		and not exists (select 1 from wx_t_user u left join wx_t_userrole ur on u.user_id=ur.user_id
		left join wx_t_role r on r.role_id=ur.role_id 
		where (u.type is null or u.type != 1) and u.status=1 and u.org_id=o.id and r.ch_role_name='Chevron_CDM_Hero_Product_Admin')
		group by o.organization_name,pp1.point_name,t.create_time,o.id
		order by t.create_time, pp1.point_name