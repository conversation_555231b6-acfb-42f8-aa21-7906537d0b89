package com.chevron.b2b.controller;

import com.chevron.b2b.business.ScheduleInstanceBizService;
import com.chevron.b2b.model.ScheduleInstanceParams;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.JsonResponse;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 计划实例Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2018-12-24 11:01
 */
@Controller
public class ScheduleInstanceController {
	@Autowired
	private ScheduleInstanceBizService scheduleInstanceBizService;
	
	private final static Logger log = Logger.getLogger(ScheduleInstanceController.class);

	/**
	 * 计划实例列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/scheduleinstance/data.do")
	public Map<String,Object> queryForPage(ScheduleInstanceParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			scheduleInstanceBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/anon/scheduleinstance/updateactivityagree.do",method={RequestMethod.GET,RequestMethod.POST})
	public Map<String,Object> updateActivityAgree(@RequestParam(value="activityId", required=false)Long activityId, 
			@RequestParam(value="userId", required=false)String userId, @RequestParam(value="enableFlag", required=false)Integer enableFlag){
		JsonResponse resultMap = new JsonResponse();
		if(activityId == null) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("未传入acitivityId参数");
			return resultMap;
		}
		if(StringUtils.isBlank(userId)) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("未传入userId参数");
			return resultMap;
		}
		try {
			if(new Integer(0).equals(enableFlag)) {
				scheduleInstanceBizService.cancelAgree(activityId, userId);
			}else {
				scheduleInstanceBizService.addAgree(activityId, userId);
			}
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);;
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
}
