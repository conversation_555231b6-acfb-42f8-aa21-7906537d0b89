package com.sys.organization.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.common.util.ContextUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVoTree;
/**
 * 
 * @Copyright: 本内容仅限于德勤公司内部使用，禁止转发. 
 * @Author: bo.liu  2016-9-6 下午8:00:22 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Controller
@RequestMapping("/organization")
public class OrganizationController {

	@Resource
	OrganizationVoMapper organizationVoMapper;
	
	
	@ResponseBody
	@RequestMapping("/getChildData.do")
	public List<OrganizationVoTree> getOrgLstByParentId(@RequestParam("id")String pid)
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("parentId", pid);
		WxTUser user = ContextUtil.getCurUser();
		if(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) && user.getUserId() != 1){
			reqMap.put("loginUser", user.getUserId());
			reqMap.put("userType", "chevron");
		}
		List<OrganizationVoTree> orgNodes = organizationVoMapper.selectByOrgByParentId(reqMap);
		List<OrganizationVoTree> resultNodes = new ArrayList<OrganizationVoTree>();
		for(OrganizationVoTree orgTree : orgNodes)
		{
			orgTree.setText(orgTree.getOrganizationName());
//			orgTree.setLeaf(false);
			orgTree.setExpanded(false);
			orgTree.setEsspflag(orgTree.getEsspflag());
			resultNodes.add(orgTree);
		}
		return resultNodes;
				
	}
	
	/**
	 * 获取所有合伙人
	 * <AUTHOR> 2016-11-25 下午5:07:02
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/ctrl/suggest.do")
	public Map<String, Object> getPartnersInfo(HttpServletRequest request) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		Map<String, Object> paramMap = new HashMap<String, Object>();
		try {
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) && user.getUserId() != 1){
				paramMap.put("loginUserId", user.getUserId());
				paramMap.put("userType", "chevron");
				paramMap.put("salesChannel", user.getSalesChannel());
				paramMap.put("loginCai", user.getCai());
				paramMap.put("orgLimit", user._isChevronLimitModel());
			}
			if(StringUtils.isNotBlank(request.getParameter("organizationName"))){
				paramMap.put("organizationName", request.getParameter("organizationName"));
			}
			map.put("data", organizationVoMapper.selectByOrgCodeForTreeNodes(paramMap));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}
}
