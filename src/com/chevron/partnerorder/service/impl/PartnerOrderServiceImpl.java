package com.chevron.partnerorder.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollectionUtil;
import com.chevron.partnerorder.model.*;
import com.chevron.pms.dao.PartnerResponsibleMainMapper;
import com.chevron.pms.model.PartnerResponsibleMain;
import com.lowagie.text.Utilities;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import com.chevron.core.conf.service.PartnerProductPriceService;
import com.chevron.core.conf.service.ProductPackageService;
import com.chevron.core.conf.service.PromotionalCampaignService;
import com.chevron.core.conf.service.SellInProductPriceService;
import com.chevron.elites.dao.DwCustomerRegionSalesSupervisorRelMapper;
import com.chevron.elites.model.DwCustomerRegionSalesSupervisorRel;
import com.chevron.exportdata.ExpAnnotation;
import com.chevron.exportdata.FieldComparator;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.partnerorder.dao.*;
import com.chevron.partnerorder.model.*;
import com.chevron.partnerorder.dao.*;
import com.chevron.partnerorder.model.*;
import com.chevron.partnerorder.dao.PartnerBillDetailMapper;
import com.chevron.partnerorder.dao.PartnerManageFeeDetailMapper;
import com.chevron.partnerorder.dao.PartnerOrderLineVoMapper;
import com.chevron.partnerorder.dao.PartnerOrderVoMapper;
import com.chevron.partnerorder.dao.WXTPartnerOrderAddressProductVoMapper;
import com.chevron.partnerorder.dao.WxTPartnerSaleConfigMapper;
import com.chevron.partnerorder.dao.WxTPartnerSaleConfigProductMapper;
import com.chevron.partnerorder.service.GetSapOrderService;
import com.chevron.partnerorder.service.PartnerOrderDeliveryService;
import com.chevron.partnerorder.service.PartnerOrderService;
import com.chevron.partnerorder.service.WxTPartnerSaleConfigService;
import com.chevron.plc.dao.InStockVoMapper;
import com.chevron.plc.dao.OutStockVoMapper;
import com.chevron.plc.model.InStockVo;
import com.chevron.plc.model.OutStockVo;
import com.chevron.plc.model.OutStockVoExample;
import com.chevron.plc.service.AppOutStockService;
import com.chevron.pmp.dao.ApprovalInfoVoMapper;
import com.chevron.pmp.model.ApprovalInfoVo;
import com.chevron.pms.dao.*;
import com.chevron.pms.model.*;
import com.chevron.pms.service.PartnerService;
import com.chevron.pms.service.WorkShopService;
import com.chevron.pms.service.WorkshopPartnerService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.token.util.TokenUtil;
import com.common.util.*;
import com.lowagie.text.pdf.BaseFont;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.DicItemVoExample;
import com.sys.dic.service.DicService;
import com.sys.email.service.EmailSenderService;
import com.sys.log.util.LogUtils;
import com.sys.master.business.ProductMasterBizService;
import com.sys.master.model.ProductMaster;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.PartnerView;
import com.sys.properties.service.SequenceTypes;
import com.sys.properties.service.WxTPropertiesService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.format.*;
import jxl.write.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.lang.Boolean;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class PartnerOrderServiceImpl implements PartnerOrderService {

	private Logger log = LoggerFactory.getLogger(PartnerOrderServiceImpl.class);

	@Autowired
	private WxRoleServiceImpl wxRoleService;
	
	@Autowired
	private EmailSenderService emailSenderService;
	
	@Autowired
	private OrganizationVoMapper organizationVoMapper;
	
	@Autowired
	private PartnerService partnerService;
	
	@Autowired
	private PartnerOrderVoMapper partnerOrderVoMapper;
	
	@Autowired
	private OrderVoMapper orderVoMapper;
	@Autowired
	private OrderLineVoMapper orderLinerVoMapper;
	
	@Autowired
	private WorkShopService workShopService;
	
	@Autowired
	private WorkshopPartnerService workshopPartnerService;
	
	@Autowired
	private PartnerAddressMapper partnerAddressMapper;
	
	@Autowired
	private PartnerOrderLineVoMapper partnerOrderLineVoMapper;
	
	@Autowired
	private WXTPartnerOrderAddressProductVoMapper addressProductMapper;
	
	@Autowired
	private ApprovalInfoVoMapper approvalInfoVoMapper;
	
	@Autowired
	private OutStockVoMapper outStockMapper;
	
	@Autowired
	private InStockVoMapper inStockMapper;
	
	@Autowired
	private AppOutStockService appOutStockService;
	
	@Resource
	private WxTPropertiesService propertiesServiceImpl;
	
	@Autowired
	private PartnerResponsibleVoMapper resonsibleMapper;
	
	@Autowired
	private ProductVoMapper productMapper;
	
	@Autowired
	private ProductMasterBizService productMasterBizService;
	
	@Autowired
	private WxTUserMapper userMapper;

	@Autowired
	private PartnerOrderDeliveryService partnerOrderDeliveryService;

	@Autowired
	private SellInProductPriceService sellInProductPriceService;
	
	@Autowired
	private DicService dicService;

	@Autowired
	private GetSapOrderService getSapOrderService;
	
	@Resource
	DicItemVoMapper dicItemVoMapper;
	
	@Resource
    PartnerO2OEnterpriseMapper partnerO2OEnterpriseMapper;
	
	@Autowired
	DwCustomerRegionSalesSupervisorRelMapper dcrssrMapper;
	
	@Autowired
	PromotionalCampaignService promotionalCampaignService;
	
	@Autowired
	WxTPartnerSaleConfigService partnerSaleConfigService;
	
	@Autowired
	private WxTPartnerSaleConfigMapper partnerSaleConfigMapper;
	
	@Autowired
	private WxTPartnerSaleConfigProductMapper partnerSaleConfigProductMapper;

	@Autowired
    private PartnerResponsibleMainMapper partnerResponsibleMainMapper;

	@Override
	public Map<String, Object> queryPartnerOrdersByConditions(
			PartnerOrderCondition orderCondition) {
		ResponseMap resultMap = new ResponseMap();
		List<PartnerOrderVo> lstOrders = new ArrayList<PartnerOrderVo>();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		try
		{
			if(null==orderCondition)
			{
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			if("false".equals(is_custommybatisintercepor))
			{
				orderCondition.setIsOpernCustomMybatisInterceptor("2");//关闭的
			}
			
			//合伙人
			String partnerId = orderCondition.getPartnerId();
			WxTUser currentUser = ContextUtil.getCurUser();
			Long mUserType = currentUser.getmUserTypes();
			Long mUserPartnerId = currentUser.getOrgId();
			if(mUserType==WxTUser.CHEVRON_USER_ROLE)
			{
				
				if(null == partnerId ||partnerId.equals("-999")||partnerId.length() == 0 )
				{
					mUserPartnerId = null;
				}else
				{
					mUserPartnerId = Long.parseLong(partnerId);
				}
			}else {
				orderCondition.setCreator(currentUser.getUserId().toString());
			}
				
			orderCondition.setmPartnerId(mUserPartnerId);
			//订单状态
			String orderStatus = orderCondition.getOrderStatus();
			if(null==orderStatus || orderStatus.trim().equals("-1"))
			{
				orderStatus = null;
			}
			orderCondition.setOrderStatus(orderStatus);
			
			//订单创建人（查看暂存订单）
			/*orderCondition.setCreator(ContextUtil.getCurUserId().toString());*/

            //检查权限是否指定经销商负责 用当前登录用户去找特殊配置
            HashMap<String, Object> mParams = new HashMap<String, Object>();
            mParams.put("userId", currentUser.getUserId());
            mParams.put("funFlag", "order_confirm");
			boolean isChevronIndustrialCSR = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_Industrial_CSR);
			orderCondition.setChevronIndustrialCsrFlag(isChevronIndustrialCSR);
			boolean isChevronIndustrialChannelManager = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_Industrial_Channel_Manager);
			orderCondition.setChevronIndustrialChannelManagerFlag(isChevronIndustrialChannelManager);
			boolean isChevronIndustrialSupervisor = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_Industrial_Supervisor);
			orderCondition.setChevronIndustrialSupervisorFlag(isChevronIndustrialSupervisor);

			boolean isChevronCIChannelManager = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_CI_Channel_Manager);
			orderCondition.setChevronCIChannelManagerFlag(isChevronCIChannelManager);
			boolean isChevronPriceAudit = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_PRICE_AUDIT);
			orderCondition.setChevronPriceAuditFlag(isChevronPriceAudit);


			boolean ************************ = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_Industrial_Sales);
			orderCondition.setChevronIndustrialSalesFlag(************************);

			if(isChevronIndustrialSupervisor || ************************) {
				orderCondition.setSalesCai(ContextUtil.getCurUser().getCai());
			}
            List<PartnerResponsibleMain> partnerResponsibleMains = partnerResponsibleMainMapper.selectByParams(mParams);
            if(CollectionUtil.isNotEmpty(partnerResponsibleMains) ||isChevronIndustrialCSR ||isChevronIndustrialChannelManager ||isChevronIndustrialSupervisor ||************************){
                orderCondition.setMainPermissionFlag(true);
                orderCondition.setCurUserId(currentUser.getUserId());
            }
			//1.查询数据
			Long totalRecord = 0L;
			lstOrders = partnerOrderVoMapper.getPartnerOrdersByCondition(orderCondition);
//			Map<String, Object> approverParam = new HashMap<String,Object>();
			/*approverParam.put("approverRoleName", Constants.CHEVRON_PRICE_AUDIT);
			approverParam.put("funFlag", "order_confirm");*/
			/*approverParam.put("partnerId", "9");*/
			/*List<PartnerResponsibleVo> approverList = resonsibleMapper.queryPartnerResponsible(approverParam);
			for(PartnerOrderVo partnerOrder : lstOrders){
				if(approverList != null && !approverList.isEmpty()){
					String chevronBdName = new String();
					for(PartnerResponsibleVo pr : approverList){
						if(pr.getPartnerId().equals(partnerOrder.getPartnerId())){
							chevronBdName = chevronBdName + " " +pr.getResponsiblePersonName();
						}
					}
					partnerOrder.setChevronBdName(chevronBdName);
				}
				
			}*/
			
//			if("false".equals(is_custommybatisintercepor))//没有开数据权限
//			{
//				totalRecord = partnerOrderVoMapper.countGetPartnerOrdersByCondition(orderCondition);
//			}else
//			{
				
				totalRecord = orderCondition.getTotalCount();
//			}
			resultMap.put(Constants.RESULT_LST_KEY, lstOrders);
			resultMap.put("totalRecord", totalRecord);
			WxTUser user = ContextUtil.getCurUser();
			// 登录用户权限检查（是否包含销售、价格审核、学佛龙下单员的权限）
			boolean isChevronBdRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_BD_CODE);
			boolean isChevronBdDiscountRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_BD_DISCOUNT_CODE);
			boolean isChevronPriceAuditRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_PRICE_AUDIT);
			boolean isChevronSapCsrRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_SAP_CSR);
			boolean isChevronSapModifyRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_SAP_MODIFY);
			boolean ************************* = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_Industrial_Dealer);
			boolean isChevronCiSuppervisor = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_CI_Suppervisor);
			boolean isChevronCdmSuppervisor = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_CDM_Suppervisor);
			boolean isChevronAdmin =  wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_Admin);

			resultMap.put("isChevronBdRole", isChevronBdRole);
			resultMap.put("isChevronBdDiscountRole", isChevronBdDiscountRole);
			resultMap.put("isChevronPriceAuditRole", isChevronPriceAuditRole);
			resultMap.put("isChevronSapCsrRole", isChevronSapCsrRole);
			resultMap.put("isChevronSapModifyRole", isChevronSapModifyRole);
			resultMap.put("*************************", *************************);
			resultMap.put("isChevronIndustrialCSR", isChevronIndustrialCSR);
			resultMap.put("************************", ************************);
			resultMap.put("isChevronIndustrialSupervisor", isChevronIndustrialSupervisor);
			resultMap.put("isChevronIndustrialChannelManager", isChevronIndustrialChannelManager);
			resultMap.put("isChevronCiSuppervisor", isChevronCiSuppervisor);
			resultMap.put("isChevronCdmSuppervisor", isChevronCdmSuppervisor);
			resultMap.put("isChevronAdmin", isChevronAdmin);
			//统计所有的升数
			/*orderCondition.setField("totalLiterCount");*/
			orderCondition.closeOrder();
			Double totalLiterCount = partnerOrderVoMapper.getPartnerOrdersTotalLiterCountByCondition(orderCondition);
			PartnerOrderVo sumPartnerOrder = new PartnerOrderVo();
			sumPartnerOrder.setTotalLiterCount(totalLiterCount);
			resultMap.put("summary", sumPartnerOrder);
//		}catch (WxPltException e) {
//			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//			resultMap.setErrorMsg(e.getMessage());
		}catch (Exception e) {
			log.error(e.getMessage(), e);
			resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	private String getOrderChineseStatusByStatusCode(String orderStatus){
		Map<String, Object> dicItemResult = dicService.getDicItemByParam("PARTNER_ORDER_STATUS","",orderStatus);
		List<DicItemVo> dicItemList = (List<DicItemVo>)dicItemResult.get("data");
		return dicItemList.get(0).getDicItemName();
	}
	
	

	@Override
	public Map<String, Object> getPartnerSaleConfig(Long partnerId) {		
		return partnerSaleConfigService.getPartnerSaleConfig(partnerId ,new Date());
	}

	/**
	 * 是否含有销售角色登录
	 */
	@Override
	public Map<String, Object> isIncludeRoleLogin() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 登录用户权限检查（是否包含销售、价格审核、学佛龙下单员的权限）
		boolean isChevronBdRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_BD_CODE);
		boolean isChevronBdDiscountRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_BD_DISCOUNT_CODE);
		boolean isChevronPriceAuditRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_PRICE_AUDIT);
		boolean isChevronSapCsrRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_SAP_CSR);
		boolean isChevronSapModifyRole = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.Chevron_SAP_MODIFY);
		resultMap.put("isChevronBdRole", isChevronBdRole);
		resultMap.put("isChevronBdDiscountRole", isChevronBdDiscountRole);
		resultMap.put("isChevronPriceAuditRole", isChevronPriceAuditRole);
		resultMap.put("isChevronSapCsrRole", isChevronSapCsrRole);
		resultMap.put("isChevronSapModifyRole", isChevronSapModifyRole);
		return resultMap;
	}
	
//	/**
//	 * 合伙人订单明细取得(用于订单重新提交页面使用)
//	 * 
//	 * @param partnerOrderId 合伙人订单ID
//	 * @return 合伙人订单明细
//	 */
//	@SuppressWarnings("unchecked")
//	@Override
//	public Map<String, Object> queryPartnerOrderDetail(String partnerOrderId) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		if (StringUtils.isEmpty(partnerOrderId)) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单不存在,请重新选择合伙人订单!");
//			return resultMap;
//		}
//		PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId);
//		if (partnerOrderVo == null) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单不存在!");
//			return resultMap;
//		}
//		List<PartnerOrderLineVo> partnerOrderDetailLst = partnerOrderLineVoMapper.selectByPartnerId(partnerOrderId);
//		
//		if (partnerOrderDetailLst == null || partnerOrderDetailLst.isEmpty()) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单明细不存在!");
//			return resultMap;
//		}
//		//找出订单中打包销售的打包详情
//		List<PartnerOrderLineVo> partnerOrderDetailLstPackage = new ArrayList<PartnerOrderLineVo>();
//		for(PartnerOrderLineVo pol : partnerOrderDetailLst){
//			if(pol.getPackageSell()!= null && pol.getPackageSell().equals("yes")){
//				partnerOrderDetailLstPackage.add(pol);
//			}
//		}
//		Set<Long> skuNotInPackage = new HashSet<Long>();
//		for(PartnerOrderLineVo polPackageDetail : partnerOrderDetailLstPackage){
//			for(PartnerOrderLineVo pol : partnerOrderDetailLst){
//				if(polPackageDetail.getSku().equals(pol.getSku()) && pol.getPackageSell() == null){
//					Integer carIneteger = pol.getActualAmount() + polPackageDetail.getActualAmount();
//					polPackageDetail.setAmount(carIneteger);
//					polPackageDetail.setActualAmount(carIneteger);
//					skuNotInPackage.add(pol.getId());
//				}
//			}
//		}
//		List<PartnerOrderLineVo> partnerOrderDetailLstWithNoPackage = new ArrayList<PartnerOrderLineVo>();
//		for(PartnerOrderLineVo pol : partnerOrderDetailLst){
//			if("package".equals(pol.getPackageunit()) || skuNotInPackage.contains(pol.getId())){
//				continue;
//			}else{
//				partnerOrderDetailLstWithNoPackage.add(pol);
//			}
//		}
//		
//		List<PartnerOrderLineVo> partnerOrderDetailLstForResubmit = new ArrayList<PartnerOrderLineVo>();
//		List<PartnerOrderLineVo> partnerOrderDetailLstForAll = new ArrayList<PartnerOrderLineVo>();
//		for(PartnerOrderLineVo pol : partnerOrderDetailLstWithNoPackage){
//			if(pol.getAmount() != null && pol.getAmount() > 0){
//				partnerOrderDetailLstForResubmit.add(pol);
//			}
//		}
//		partnerOrderDetailLstForAll = generatePartnerOrderlLinePackage(partnerOrderDetailLstWithNoPackage);
//		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		resultMap.put("orderLineVoLst", partnerOrderDetailLstForResubmit);
//		resultMap.put("partnerOrderDetailLstForAll", partnerOrderDetailLstForAll);
//		resultMap.put("partnerOrderVo", partnerOrderVo);
//		
//		//取得送货地址(适配最开始的单地址版本)
//		if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
//			if(partnerOrderVo.getAddressId() != null){
//				PartnerAddress address = new PartnerAddress();
//				address = partnerAddressMapper.getAddressDetailById(partnerOrderVo.getAddressId());		
//				resultMap.put("address", address);
//				resultMap.put("sapSaleNumber", partnerOrderVo.getSapSaleNumber());
//				List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(partnerOrderVo.getOrderNo());
//				List<InStockVo> instockList = inStockMapper.queryInStockByOrderNo(partnerOrderVo.getOrderNo());
//				if(outstockList != null && !outstockList.isEmpty()){
//					partnerOrderVo.setStockOutNo(outstockList.get(0).getStockOutNo());
//				}
//				if(instockList != null && !instockList.isEmpty()){
//					partnerOrderVo.setStockInNo(instockList.get(0).getStockInNo());
//				}
//			}else{
//				resultMap.put("address", null);
//			}
//		}
//		List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderId);
//		
//		Map<Long,Object> addressMap = new HashMap<Long,Object>();
//		
//		Set<Long> addressIdSet = new HashSet<Long>();
//		for(WXTPartnerOrderAddressProductView temp : addressProductList){
//			Map<String,Object> addressProductMap = new HashMap<String,Object>();
//			if(!addressIdSet.contains(temp.getAddressId())){
//				addressIdSet.add(temp.getAddressId());
//				addressProductMap.put("addressObj", temp);
//				if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
//					String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress() + " " + temp.getContactPerson()+ " " + temp.getMobile();					
//					addressProductMap.put("address", addressString);
//				}else{
//					String addressString = temp.getWorkshopAddress() + " " + temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone();
//					addressProductMap.put("address", addressString);
//				}
//				addressProductMap.put("addressId", temp.getAddressId());
//				addressProductMap.put("deliveryType", temp.getDeliveryType());
//				if(temp.getOutStockNo() != null){
//					addressProductMap.put("outStockNo", temp.getOutStockNo());
//				}else{
//					addressProductMap.put("outStockNo", "");
//				}
//				if(temp.getSapSaleNumber() != null){
//					addressProductMap.put("sapSaleNumber", temp.getSapSaleNumber());
//				}else{
//					addressProductMap.put("sapSaleNumber", "");
//				}
//				if(temp.getInStockNo() != null){
//					addressProductMap.put("inStockNo", temp.getInStockNo());
//				}else{
//					addressProductMap.put("inStockNo", "");
//				}
//				
//				if(temp.getDeliveryTime() != null){
//					addressProductMap.put("deliveryTime", DateUtil.getDateStr(temp.getDeliveryTime()));
//				}else{
//					addressProductMap.put("deliveryTime", "");
//				}
//				if(temp.getEstimatedArrivalTime() != null){
//					addressProductMap.put("estimatedArrivalTime", DateUtil.getDateStr(temp.getEstimatedArrivalTime()));
//				}else{
//					addressProductMap.put("estimatedArrivalTime", "");
//				}
//				if(temp.getOutStockOrgname() != null){
//					addressProductMap.put("outStockOrgname", temp.getOutStockOrgname());
//				}else{
//					addressProductMap.put("outStockOrgname", "");
//				}
//				if(temp.getUnits() != null && temp.getUnits().equals("box")){
//					temp.setUnits("箱");
//				}else if(temp.getUnits() != null && temp.getUnits().equals("barrel")){
//					temp.setUnits("桶");
//				}else{
//					temp.setUnits("瓶");
//				} 
//				
//				List<WXTPartnerOrderAddressProductView> addressProductTemp = new ArrayList<WXTPartnerOrderAddressProductView>();
//				addressProductTemp.add(temp);
//				addressProductMap.put("addressProduct", addressProductTemp);
//				addressMap.put(temp.getAddressId(), addressProductMap);
//			}else{
//				addressProductMap = (Map<String,Object>)addressMap.get(temp.getAddressId());
//				List<WXTPartnerOrderAddressProductView> addressProductTemp = (List<WXTPartnerOrderAddressProductView>)addressProductMap.get("addressProduct"); 
//				addressProductTemp.add(temp);
//				addressProductMap.put("addressProduct", addressProductTemp);
//				addressMap.put(temp.getAddressId(), addressProductMap);
//			}
//		}
//		List<Map<String,Object>> addProductResult =  new ArrayList<Map<String,Object>>();
//		for(Long addressid : addressIdSet){
//			addProductResult.add((Map<String,Object>)addressMap.get(addressid));
//		}
//		resultMap.put("addressProduct", addProductResult);
//		
//		// 取得审批历史信息
//		List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderVoMapper.getApprovalHistoryByOrderId(Long.valueOf(partnerOrderId));
//		for(PartnerOrderApprovalHistoryVo tempArrovalHistory : approvalList){
//			String stepName = chanslatePartnerOrderApprovalComment(tempArrovalHistory.getApprovalCurrentStep(),tempArrovalHistory.getApprovalFlowStep());
//			tempArrovalHistory.setApprovalStep(stepName);
//		}
//		resultMap.put("approvalList", approvalList);
//		return resultMap;
//	}
	
	/**
	 * 合伙人订单明细取得
	 * 
	 * @param partnerOrderId 合伙人订单ID，合伙人ID, 合伙人在订单没有sap下单之前不能看到相关价格
	 * @return 合伙人订单明细
	 */
	@Override
	public Map<String, Object> queryPartnerOrderDetailWithPower(String partnerOrderId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (StringUtils.isEmpty(partnerOrderId)) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单不存在,请重新选择合伙人订单!");
			return resultMap;
		}
		PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId);
		if (partnerOrderVo == null) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单不存在!");
			return resultMap;
		}
		List<PartnerOrderLineVo>  partnerOrderDetailLst = partnerOrderLineVoMapper.selectByOrderId(partnerOrderId);
		//如果订单还没导价格审核完成，合伙人不能看到相关价格
		Set<String> statusSet = new HashSet<String>();
		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_0);
		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_4);
		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_8);
		if(WxTUser.USER_MODEL_SP.equals(ContextUtil.getCurUser().getUserModel()) && 
				statusSet.contains(partnerOrderVo.getStatus())){ 
			for(PartnerOrderLineVo pol :  partnerOrderDetailLst ){
				pol.setPrice(null);
				pol.setLadderPrice(null);
				pol.setDiscountedPrice(null);
				pol.setTotalValue(null);
				pol.setDiscountedTotalValue(null);
				pol.setDiscountFee(null);
				pol.setDeductibleAmount(null);
				pol.setTotalValueAfterDeductible(null);
			}
		}
		
		if (partnerOrderDetailLst == null || partnerOrderDetailLst.isEmpty()) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单明细不存在!");
			return resultMap;
		}
		
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		resultMap.put("orderLineVoLst", partnerOrderDetailLst);
		resultMap.put("partnerOrderVo", partnerOrderVo);
		
		// 取得审批历史信息
		List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderVoMapper.getApprovalHistoryByOrderId(Long.valueOf(partnerOrderId));
//		for(PartnerOrderApprovalHistoryVo tempArrovalHistory : approvalList){
//			String stepName = chanslatePartnerOrderApprovalComment(tempArrovalHistory.getApprovalCurrentStep(),tempArrovalHistory.getApprovalFlowStep());
//			tempArrovalHistory.setApprovalStep(stepName);
//		}
		resultMap.put("approvalList", approvalList);
		
		if(partnerOrderVo.getVersionNo() < PartnerOrderVo.VERSION_NO202107) {
			buildProductAddressV0(partnerOrderVo, resultMap);
		}else {
			buildProductAddressV202107(partnerOrderVo, partnerOrderDetailLst, resultMap);
		}

		return resultMap;
	}
	private void buildProductAddressV202107(PartnerOrderVo partnerOrderVo, List<PartnerOrderLineVo> orderLineVos, Map<String, Object> resultMap) {
		Map<String,Object> addressDetailMap = new HashMap<String,Object>(5);
		StringBuilder addressString = new StringBuilder(500);
		if(StringUtils.isNotBlank(partnerOrderVo.getProvinceName())) {
			addressString.append(partnerOrderVo.getProvinceName()).append(" ");
		}
		if(StringUtils.isNotBlank(partnerOrderVo.getCityName())) {
			addressString.append(partnerOrderVo.getCityName()).append(" ");
		}
		if(StringUtils.isNotBlank(partnerOrderVo.getDistName())) {
			addressString.append(partnerOrderVo.getDistName()).append(" ");
		}
		
		addressString.append(StringUtils.nvl(partnerOrderVo.getAddress(), "")).append(StringUtils.nvl(partnerOrderVo.getContactPerson(), ""))
				.append(" ").append(StringUtils.nvl(partnerOrderVo.getContactPersonTel(), ""));			
		addressDetailMap.put("addressString", addressString.toString());
		PartnerOrderDeliveryView partnerOrderDelivery = new PartnerOrderDeliveryView();
		partnerOrderDelivery.setAddress(partnerOrderVo.getAddress());
		partnerOrderDelivery.setContactor(partnerOrderVo.getContactPerson());
		partnerOrderDelivery.setMobileTel(partnerOrderVo.getContactPersonTel());
		
		partnerOrderDelivery.setDeliveryNumber(partnerOrderVo.getDeliveryNumber());				
		partnerOrderDelivery.setDeliveryTime(DateUtil.getDateStr(partnerOrderVo.getDeliveryTime()));
		partnerOrderDelivery.setSapSaleNumber(partnerOrderVo.getSapSaleNumber());
		PartnerOrderDeliveryView[] partnerOrderDeliveryList = new PartnerOrderDeliveryView[] {partnerOrderDelivery};
		addressDetailMap.put("partnerOrderDeliveryList", partnerOrderDeliveryList);
		
		List<PartnerOrderDeliveryDetailView> partnerOrderDeliveryDetailList = new ArrayList<PartnerOrderDeliveryDetailView>(orderLineVos.size());
		for(PartnerOrderLineVo temp : orderLineVos) {
			PartnerOrderDeliveryDetailView partnerOrderDeliveryDetailView = new PartnerOrderDeliveryDetailView();
			partnerOrderDeliveryDetailView.setSku(temp.getSku());
			partnerOrderDeliveryDetailView.setProductName(temp.getProductName());
			partnerOrderDeliveryDetailView.setAssignedDeliveryCount(temp.getAmount());
			partnerOrderDeliveryDetailView.setUnits(temp.getUnitsText());
			
//			partnerOrderDeliveryDetailView.setActualDeliveryCount(temp.getActualAmount());
			partnerOrderDeliveryDetailList.add(partnerOrderDeliveryDetailView);
		}
		partnerOrderDelivery.setDeliveryDetaiList(partnerOrderDeliveryDetailList);
		resultMap.put("addressProduct", addressDetailMap);
	}
	
	private void buildProductAddressV0(PartnerOrderVo partnerOrderVo, Map<String, Object> resultMap) {
		
		//取得送货地址(适配最开始的单地址版本)
		Map<Long,Object> addressMap = new HashMap<Long,Object>();
		Set<Long> addressIdSet = new HashSet<Long>();
				
		if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
			Map<String,Object> addressDetailMap = new HashMap<String,Object>();
			if(partnerOrderVo.getAddressId() != null){
				addressIdSet.add(partnerOrderVo.getAddressId());
				
				PartnerAddress address = new PartnerAddress();
				address = partnerAddressMapper.getAddressDetailById(partnerOrderVo.getAddressId());	
				
				String addressString = address.getProvinceName() + " "+ address.getCityName() + " " + address.getDistName() + " " + address.getAddress() + " " + address.getContactPerson()+ " " + address.getMobile();					
				addressDetailMap.put("addressString", addressString);
				List<PartnerOrderDeliveryView> partnerOrderDeliveryList = new ArrayList<PartnerOrderDeliveryView>();
				PartnerOrderDeliveryView partnerOrderDelivery = new PartnerOrderDeliveryView();
				partnerOrderDelivery.setAddress(address.getAddress());
				partnerOrderDelivery.setContactor(address.getContactPerson());
				partnerOrderDelivery.setMobileTel(address.getMobile());
				
				partnerOrderDelivery.setDeliveryNumber(partnerOrderVo.getDeliveryNumber());				
				partnerOrderDelivery.setDeliveryTime(DateUtil.getDateStr(partnerOrderVo.getDeliveryTime()));
				partnerOrderDelivery.setSapSaleNumber(partnerOrderVo.getSapSaleNumber());
				
				List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(partnerOrderVo.getOrderNo());
				List<InStockVo> instockList = inStockMapper.queryInStockByOrderNo(partnerOrderVo.getOrderNo());	
				if(outstockList != null && !outstockList.isEmpty()){
					partnerOrderDelivery.setOutStockNo(outstockList.get(0).getStockOutNo());
					partnerOrderDelivery.setOutStockOrgname(outstockList.get(0).getStockFromOrgname());
				}
				if(instockList != null && !instockList.isEmpty()){
					partnerOrderDelivery.setInStockNo(instockList.get(0).getStockInNo());
				}
				partnerOrderDeliveryList.add(partnerOrderDelivery);
				addressDetailMap.put("partnerOrderDeliveryList", partnerOrderDeliveryList);
				
				addressMap.put(partnerOrderVo.getAddressId(), addressDetailMap);
			}
		}
		List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderVo.getId().toString());
		List<PartnerOrderDelivery> partnerOrderDeliveryVoList = partnerOrderDeliveryService.getParterOrderDeliveryByPartnerOrderNo(partnerOrderVo.getOrderNo());
		//统计多地址
		//一个地址一种发货方式  
		if(partnerOrderDeliveryVoList == null || partnerOrderDeliveryVoList.size() == 0){
			for(WXTPartnerOrderAddressProductView temp : addressProductList){
				
				if(!addressIdSet.contains(temp.getAddressId())){
					addressIdSet.add(temp.getAddressId());				
					Map<String,Object> addressDetailMap = new HashMap<String,Object>();
					if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
						String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress() + " " + temp.getContactPerson()+ " " + temp.getMobile();					
						addressDetailMap.put("addressString", addressString);
					}else{
						String addressString = temp.getWorkshopAddress() + " " + temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone();
						addressDetailMap.put("addressString", addressString);
					}	
					
					List<PartnerOrderDeliveryView> partnerOrderDeliveryList= new ArrayList<PartnerOrderDeliveryView>();
					PartnerOrderDeliveryView partnerOrderDelivery = new PartnerOrderDeliveryView();
					partnerOrderDelivery.setAddressId(temp.getAddressId());
					partnerOrderDelivery.setAddress(temp.getAddress());
					partnerOrderDelivery.setContactor(temp.getContactPerson());
					partnerOrderDelivery.setMobileTel(temp.getMobile());
					partnerOrderDelivery.setDeliveryType(temp.getDeliveryType());				
					partnerOrderDelivery.setDeliveryNumber(partnerOrderVo.getDeliveryNumber());	
					if(temp.getDeliveryTime() != null){
						partnerOrderDelivery.setDeliveryTime(DateUtil.getDateStr(temp.getDeliveryTime()));
					}
					partnerOrderDelivery.setSapSaleNumber(temp.getSapSaleNumber());
					if(temp.getEstimatedArrivalTime() != null){
						partnerOrderDelivery.setEstimatedArrivalTime(DateUtil.getDateStr(temp.getEstimatedArrivalTime()));
					}
					
					List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(partnerOrderVo.getOrderNo());
					List<InStockVo> instockList = inStockMapper.queryInStockByOrderNo(partnerOrderVo.getOrderNo());	
					if(outstockList != null && !outstockList.isEmpty()){
						partnerOrderDelivery.setOutStockNo(outstockList.get(0).getStockOutNo());
						partnerOrderDelivery.setOutStockOrgname(outstockList.get(0).getStockFromOrgname());
					}
					if(instockList != null && !instockList.isEmpty()){
						partnerOrderDelivery.setInStockNo(instockList.get(0).getStockInNo());
					}
					
					List<PartnerOrderDeliveryDetailView> partnerOrderDeliveryDetailList = new ArrayList<PartnerOrderDeliveryDetailView>();
					PartnerOrderDeliveryDetailView partnerOrderDeliveryDetailView = new PartnerOrderDeliveryDetailView();
					partnerOrderDeliveryDetailView.setSku(temp.getSku());
					partnerOrderDeliveryDetailView.setProductName(temp.getProductName());
					partnerOrderDeliveryDetailView.setAssignedDeliveryCount(temp.getAmount());
					
					if(temp.getUnits() != null && temp.getUnits().equals("box")){
						partnerOrderDeliveryDetailView.setUnits("箱");
					}else if(temp.getUnits() != null && temp.getUnits().equals("barrel")){
						partnerOrderDeliveryDetailView.setUnits("桶");
					}else{
						partnerOrderDeliveryDetailView.setUnits("瓶");
					}
					
					partnerOrderDeliveryDetailView.setActualDeliveryCount(temp.getActualAmount());
					partnerOrderDeliveryDetailList.add(partnerOrderDeliveryDetailView);
					partnerOrderDelivery.setDeliveryDetaiList(partnerOrderDeliveryDetailList);
					
					partnerOrderDeliveryList.add(partnerOrderDelivery);
					addressDetailMap.put("partnerOrderDeliveryList", partnerOrderDeliveryList);
					
					addressMap.put(temp.getAddressId(), addressDetailMap);
					
				}else{					
					Map<String,Object> addressDetailMap = (Map<String,Object>)addressMap.get(temp.getAddressId());
					List<PartnerOrderDeliveryView> partnerOrderDeliveryList = (List<PartnerOrderDeliveryView>)addressDetailMap.get("partnerOrderDeliveryList");
					PartnerOrderDeliveryView partnerOrderDelivery = partnerOrderDeliveryList.get(0);
					List<PartnerOrderDeliveryDetailView> partnerOrderDeliveryDetailList = partnerOrderDelivery.getDeliveryDetaiList();
					PartnerOrderDeliveryDetailView partnerOrderDeliveryDetailView = new PartnerOrderDeliveryDetailView();
					partnerOrderDeliveryDetailView.setSku(temp.getSku());
					partnerOrderDeliveryDetailView.setProductName(temp.getProductName());
					partnerOrderDeliveryDetailView.setAssignedDeliveryCount(temp.getAmount());
					
					if(temp.getUnits() != null && temp.getUnits().equals("box")){
						partnerOrderDeliveryDetailView.setUnits("箱");
					}else if(temp.getUnits() != null && temp.getUnits().equals("barrel")){
						partnerOrderDeliveryDetailView.setUnits("桶");
					}else{
						partnerOrderDeliveryDetailView.setUnits("瓶");
					}
					
					partnerOrderDeliveryDetailView.setActualDeliveryCount(temp.getActualAmount());
					partnerOrderDeliveryDetailList.add(partnerOrderDeliveryDetailView);
					partnerOrderDelivery.setDeliveryDetaiList(partnerOrderDeliveryDetailList);
					
					addressDetailMap.put("partnerOrderDeliveryList", partnerOrderDeliveryList);					
					addressMap.put(temp.getAddressId(), addressDetailMap);					
				}
			}
		}else{
			//一个地址多种发货方式
			for(WXTPartnerOrderAddressProductView temp : addressProductList){
				
				if(!addressIdSet.contains(temp.getAddressId())){
					addressIdSet.add(temp.getAddressId());				
					Map<String,Object> addressDetailMap = new HashMap<String,Object>();
					if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
						String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress() + " " + temp.getContactPerson()+ " " + temp.getMobile();					
						addressDetailMap.put("addressString", addressString);
					}else{
						String addressString = temp.getWorkshopAddress() + " " + temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone();
						addressDetailMap.put("addressString", addressString);
					}
					List<PartnerOrderDelivery> deliveryListForOneAddrees = new ArrayList<PartnerOrderDelivery>();
					List<PartnerOrderDeliveryView> partnerOrderDeliveryList = new ArrayList<PartnerOrderDeliveryView>();
					for(PartnerOrderDelivery deliveryTemp : partnerOrderDeliveryVoList){
						if(deliveryTemp.getAddressId().equals(temp.getAddressId())){
							deliveryListForOneAddrees.add(deliveryTemp);
							
							PartnerOrderDeliveryView deliveryView = new PartnerOrderDeliveryView();
							deliveryView.setAddressId(deliveryTemp.getAddressId());
							deliveryView.setDeliveryType(deliveryTemp.getDeliveryType());				
							deliveryView.setDeliveryNumber(deliveryTemp.getDeliveryNumber());
							if(deliveryTemp.getDeliveryTime() != null){
								deliveryView.setDeliveryTime(DateUtil.getDateStr(deliveryTemp.getDeliveryTime()));
							}
							deliveryView.setSapSaleNumber(deliveryTemp.getSapSaleNumber());
							if(deliveryTemp.getEstimatedArrivalTime() != null){
								deliveryView.setEstimatedArrivalTime(DateUtil.getDateStr(deliveryTemp.getEstimatedArrivalTime()));
							}
							deliveryView.setOutStockNo(deliveryTemp.getOutStockNo());
							deliveryView.setOutStockOrgname(deliveryTemp.getOutStockOrgname());
							deliveryView.setInStockNo(deliveryTemp.getInStockNo());
							
							List<PartnerOrderDeliveryDetailView> partnerOrderDeliveryDetailList = new ArrayList<PartnerOrderDeliveryDetailView>();						
							List<PartnerOrderDeliveryDetail> deliveryDetailList = deliveryTemp.getDeliveryDetaiList();
							for(PartnerOrderDeliveryDetail deliveryDetailTemp : deliveryDetailList){							
								PartnerOrderDeliveryDetailView partnerOrderDeliveryDetailView = new PartnerOrderDeliveryDetailView();
								partnerOrderDeliveryDetailView.setSku(deliveryDetailTemp.getSku());
								partnerOrderDeliveryDetailView.setProductName(deliveryDetailTemp.getProductName());
								partnerOrderDeliveryDetailView.setAssignedDeliveryCount(deliveryDetailTemp.getAssignedDeliveryCount());
								partnerOrderDeliveryDetailView.setActualDeliveryCount(deliveryDetailTemp.getActualDeliveryCount());
								
								if(deliveryDetailTemp.getUnits() != null && deliveryDetailTemp.getUnits().equals("box")){
									partnerOrderDeliveryDetailView.setUnits("箱");
								}else if(deliveryDetailTemp.getUnits() != null && deliveryDetailTemp.getUnits().equals("barrel")){
									partnerOrderDeliveryDetailView.setUnits("桶");
								}else{
									partnerOrderDeliveryDetailView.setUnits("瓶");
								}
								
								partnerOrderDeliveryDetailList.add(partnerOrderDeliveryDetailView);
								deliveryView.setDeliveryDetaiList(partnerOrderDeliveryDetailList);
							}
							partnerOrderDeliveryList.add(deliveryView);
						}
					}					
					addressDetailMap.put("partnerOrderDeliveryList", partnerOrderDeliveryList);					
					addressMap.put(temp.getAddressId(), addressDetailMap);				
				}
			}
		}
		List<Map<String,Object>> addProductResult =  new ArrayList<Map<String,Object>>();
		for(Long addressid : addressIdSet){
			addProductResult.add((Map<String,Object>)addressMap.get(addressid));
		}
		resultMap.put("addressProduct", addProductResult);
	}
	
//	@Override
//	public Map<String, Object> getPartnerOrderDetailForCaculatePrice(String partnerOrderId) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		if (StringUtils.isEmpty(partnerOrderId)) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单不存在,请重新选择合伙人订单!");
//			return resultMap;
//		}
//		PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId);
//		if (partnerOrderVo == null) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单不存在!");
//			return resultMap;
//		}
//		List<PartnerOrderLineVo> partnerOrderDetailLst = partnerOrderLineVoMapper.selectByPartnerId(partnerOrderId);
//		if (partnerOrderDetailLst == null || partnerOrderDetailLst.isEmpty()) {
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单明细不存在!");
//			return resultMap;
//		}
//		//自动计算价格，显示到列表
//		// 解析orderLines
//		BigDecimal totalProductPrice = new BigDecimal(0);
//		BigDecimal totalDiscountedProductPrice = new BigDecimal(0);
//		partnerOrderDetailLst = calculatePartnerOrderlLinePrice(partnerOrderId);
//		//根据促销活动组装订单详情
//		partnerOrderDetailLst = generatePartnerOrderlLinePackage(partnerOrderDetailLst);
//		
//		Set<String> statusSet = new HashSet<String>();
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_0);
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_4);
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_8);
//		if(statusSet.contains(partnerOrderVo.getStatus())){
//			for(PartnerOrderLineVo newOrderLineVo : partnerOrderDetailLst){
//				totalDiscountedProductPrice = newOrderLineVo.getDiscountedTotalValue().add(totalDiscountedProductPrice);
//			}
//		}
//		
//		totalDiscountedProductPrice = totalDiscountedProductPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
//		totalProductPrice = totalProductPrice.setScale(2,BigDecimal.ROUND_HALF_UP);
//		
//		partnerOrderVo.setTotalProductPrice(totalProductPrice);
//		partnerOrderVo.setTotalOrderPrice(totalProductPrice);
//		partnerOrderVo.setDiscountedTotalOrderPrice(totalDiscountedProductPrice);
//		
//		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		/*resultMap.put("isSingleSkuGreater2645", isSingleSkuGreater2645);*/
//		resultMap.put("orderLineVoLst", partnerOrderDetailLst);
//		//传回所有订单详情未包装信息
//		List<PartnerOrderLineVo> orderLineForDelivery = partnerOrderLineVoMapper.selectByPartnerId(partnerOrderId);
//		resultMap.put("orderLineForDelivery", orderLineForDelivery);
//		resultMap.put("partnerOrderVo", partnerOrderVo);
//		
//		//取得送货地址(适配最开始的单地址版本)
//		if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
//			if(partnerOrderVo.getAddressId() != null){
//				PartnerAddress address = new PartnerAddress();
//				address = partnerAddressMapper.getAddressDetailById(partnerOrderVo.getAddressId());		
//				resultMap.put("address", address);
//				resultMap.put("sapSaleNumber", partnerOrderVo.getSapSaleNumber());
//				List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(partnerOrderVo.getOrderNo());
//				List<InStockVo> instockList = inStockMapper.queryInStockByOrderNo(partnerOrderVo.getOrderNo());
//				if(outstockList != null && !outstockList.isEmpty()){
//					partnerOrderVo.setStockOutNo(outstockList.get(0).getStockOutNo());
//				}
//				if(instockList != null && !instockList.isEmpty()){
//					partnerOrderVo.setStockInNo(instockList.get(0).getStockInNo());
//				}
//			}else{
//				resultMap.put("address", null);
//			}
//		}
//		List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderId);
//		
//		Map<Long,Object> addressMap = new HashMap<Long,Object>();
//		
//		Set<Long> addressIdSet = new HashSet<Long>();
//		for(WXTPartnerOrderAddressProductView temp : addressProductList){
//			Map<String,Object> addressProductMap = new HashMap<String,Object>();
//			if(!addressIdSet.contains(temp.getAddressId())){
//				addressIdSet.add(temp.getAddressId());
//				addressProductMap.put("addressObj", temp);
//				if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
//					String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress() + " " + temp.getContactPerson()+ " " + temp.getMobile();					
//					addressProductMap.put("address", addressString);
//				}else{
//					String addressString = temp.getWorkshopAddress() + " " + temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone();
//					addressProductMap.put("address", addressString);
//				}
//				addressProductMap.put("addressId", temp.getAddressId());
//				addressProductMap.put("deliveryType", temp.getDeliveryType());
//				if(temp.getOutStockNo() != null){
//					addressProductMap.put("outStockNo", temp.getOutStockNo());
//				}else{
//					addressProductMap.put("outStockNo", "");
//				}
//				if(temp.getSapSaleNumber() != null){
//					addressProductMap.put("sapSaleNumber", temp.getSapSaleNumber());
//				}else{
//					addressProductMap.put("sapSaleNumber", "");
//				}
//				if(temp.getInStockNo() != null){
//					addressProductMap.put("inStockNo", temp.getInStockNo());
//				}else{
//					addressProductMap.put("inStockNo", "");
//				}
//				
//				if(temp.getDeliveryTime() != null){
//					addressProductMap.put("deliveryTime", DateUtil.getDateStr(temp.getDeliveryTime()));
//				}else{
//					addressProductMap.put("deliveryTime", "");
//				}
//				if(temp.getEstimatedArrivalTime() != null){
//					addressProductMap.put("estimatedArrivalTime", DateUtil.getDateStr(temp.getEstimatedArrivalTime()));
//				}else{
//					addressProductMap.put("estimatedArrivalTime", "");
//				}
//				if(temp.getOutStockOrgname() != null){
//					addressProductMap.put("outStockOrgname", temp.getOutStockOrgname());
//				}else{
//					addressProductMap.put("outStockOrgname", "");
//				}
//				
//				List<WXTPartnerOrderAddressProductView> addressProductTemp = new ArrayList<WXTPartnerOrderAddressProductView>();
//				addressProductTemp.add(temp);
//				addressProductMap.put("addressProduct", addressProductTemp);
//				addressMap.put(temp.getAddressId(), addressProductMap);
//			}else{
//				addressProductMap = (Map<String,Object>)addressMap.get(temp.getAddressId());
//				List<WXTPartnerOrderAddressProductView> addressProductTemp = (List<WXTPartnerOrderAddressProductView>)addressProductMap.get("addressProduct"); 
//				addressProductTemp.add(temp);
//				addressProductMap.put("addressProduct", addressProductTemp);
//				addressMap.put(temp.getAddressId(), addressProductMap);
//			}
//		}
//		List<Map<String,Object>> addProductResult =  new ArrayList<Map<String,Object>>();
//		for(Long addressid : addressIdSet){
//			addProductResult.add((Map<String,Object>)addressMap.get(addressid));
//		}
//		resultMap.put("addressProduct", addProductResult);
//		
//		// 取得审批历史信息
//		List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderVoMapper.getApprovalHistoryByOrderId(Long.valueOf(partnerOrderId));
//		for(PartnerOrderApprovalHistoryVo tempArrovalHistory : approvalList){
//			String stepName = chanslatePartnerOrderApprovalComment(tempArrovalHistory.getApprovalCurrentStep(),tempArrovalHistory.getApprovalFlowStep());
//			tempArrovalHistory.setApprovalStep(stepName);
//		}
//		resultMap.put("approvalList", approvalList);
//		
//		PartnerOrderPdfView  orderview =  generatePartnerOrderViewForPdfExcel(partnerOrderId ,true);
//		// 获得账单信息
//		resultMap.put("partnerBill", orderview.getPartnerBill());		
//		//获取阶梯价格表
//		resultMap.put("maxLadderCount", orderview.getMaxLadderCount());
//		resultMap.put("priceRule", orderview.getPriceRuleList());
//		//获取第几次审批
//		int submitTimes = 0;
//		for(PartnerOrderApprovalHistoryVo partnerOrderApprovalHistoryVo : approvalList){
//			if(partnerOrderApprovalHistoryVo.getApprovalFlowStep().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//				submitTimes = submitTimes + 1;
//			}
//		}
//		resultMap.put("submitTimes", submitTimes);
//		//获取审批附件
//		List<WxAttFile> attList = orderview.getAttList();
//		resultMap.put("attList", attList);
//		return resultMap;
//	}
	
//	private String chanslatePartnerOrderApprovalComment(String currentStep , String flowStep){
//		if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_0) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//			return "订单提交";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_0) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2)){
//			return "订单提交";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2)){
//			return "订单价格计算完成";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_4)){
//			return "订单驳回";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_3)){
//			return "订单价格审批完成";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//			return "订单价格审批驳回";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_3)){
//			return "订单审批提交";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_4)){
//			return "订单驳回";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_4) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//			return "订单重新提交";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_4) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2)){
//			return "订单重新提交";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_3) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//			return "价格审核被召回";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_3) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_5)){
//			return "订单下单完成";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_3) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_4)){
//			return "订单被召回";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_5) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//			return "价格下单后被召回";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_5) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_6)){
//			return "订单已出库";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_6) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_7)){
//			return "订单已收货";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_9) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_5)){
//			return "订单下单完成";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_3) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_9)){
//			return "订单SAP价格录入";
//		}else if(currentStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1) && flowStep.equals(PartnerOrderVo.PARTNER_ORDER_STATUS_8)){
//			return "订单撤回";
//		}
//			
//		return "";
//	}
	
	

	@Override
	public Map<String, Object> cancelOrder(Long partnerOrderId)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Date curDate = new Date();
		PartnerOrderVo order  = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId.toString());
		Integer currentStatus = Integer.valueOf(order.getStatus());
		if(order.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
			order.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_8);
			order.setUpdateTime(curDate);
			order.setDeductibleAmount(null);
			order.setTotalValueAfterDeductible(null);
			int count = partnerOrderVoMapper.updateByPrimaryKey(order);
			if (count != 1) {
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				return resultMap;
			}
		}else{
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "订单状态已改变，撤回失败!");
			return resultMap;
		}
				
		// 审核记录保存
		ApprovalInfoVo record = new ApprovalInfoVo();
		record.setCurrentStep(currentStatus.toString());
		record.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_8);
		record.setIsApproved(0);
		record.setOperateTime(curDate);
		record.setOperator(ContextUtil.getCurUserId());
		record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
		record.setSourceId(order.getId());
		approvalInfoVoMapper.insert(record);
		// 邮件发送 先判断是否是特殊配置  指定partner负责人按照指定发送，其他按照区域配置获取
		String regionName = getOrderRegionName(order);
		//工业油Industrial只发送给工业油CSR
		Boolean industrialOrderFlag = regionName.indexOf(Constants.SALES_CHANNEL_DIRECT_INDUSTRIAL) >= 0;
        List<PartnerResponsibleVo> approverList = this.getsendEmailTo(order.getPartnerId(),industrialOrderFlag);
        if(approverList != null && !approverList.isEmpty()){
			Set<String> approverEmailList = new HashSet<String>(approverList.size());
			StringBuilder approverName = null;
			Set<String> ccEmailList = new HashSet<String>();
			for(PartnerResponsibleVo approver : approverList){
				if(StringUtils.isBlank(approver.getResponsiblePersonEmail())) {
					continue;
				}
				approverEmailList.add(approver.getResponsiblePersonEmail());
				if(approverName == null) {
					approverName = new StringBuilder();
				}else {
					approverName.append(" & ");
				}
				approverName.append(approver.getResponsiblePersonName());
				approver.setPartnerId(order.getPartnerId());
				String ccs;
				try {
					ccs = approver.getDayReportCc(Constants.SALES_CHANNEL_INDIRECT);
				} catch (RuntimeException e) {
					log.error(e.getMessage(), e);
					throw (RuntimeException)e;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					throw new RuntimeException(e);
				}
				if(StringUtils.isNotBlank(ccs)) {
					for(String cc : ccs.split(",")) {
						if(StringUtils.isNotBlank(cc)) {
							ccEmailList.add(cc);
						}
					}
				}
			}
			String[] accepters = new String[approverEmailList.size()];
			accepters = approverEmailList.toArray(accepters);
			String[] accepterCCs = new String[ccEmailList.size()];
			accepterCCs = ccEmailList.toArray(accepterCCs);
			
			PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId.toString());	
			PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrderVo,true);
			String orderChinesStatus = getOrderChineseStatusByStatusCode(order.getStatus());
			String subjectName = orderChinesStatus + ",合伙人[" + order.getPartnerName() + "]采购订单:" + order.getOrderNo();
			Map<String,Object> contentMap = new HashMap<String,Object>();
			String acceptPersonName = "尊敬的" + approverName;
			String partnerName = "合伙人" + order.getPartnerName() + ",有订单"+ order.getOrderNo()+ "已自行撤销,订单详情如下：";
			contentMap.put("acceptPersonName", acceptPersonName);
			contentMap.put("partnerName", partnerName);
			contentMap.put("skuList", partnerOrderView.getOlEmailList());
			contentMap.put("partnerOrderVo", partnerOrderView);
			List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
			contentMap.put("approvalList", approvalList);
			
			String partnerProperty = partnerService.getPartnerProperty(order.getPartnerId());
			if("KA".equals(partnerProperty)){
				File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
				emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
						accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
			}else{
				File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
				emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
						accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_info.ftl");
			}
		}
		
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return resultMap;
	}

    /**
     * 指定partner负责人按照指定发送，其他按照区域配置获取
     * @param partnerId
     * @return
     */
	private List<PartnerResponsibleVo> getsendEmailTo(Long partnerId,Boolean industrialOrderFlag) {
		if (industrialOrderFlag) {
			List<PartnerResponsibleVo> approverList = resonsibleMapper.selectByChRoleName("Chevron_Industrial_CSR");
			for (PartnerResponsibleVo approver:approverList) {
				approver.setPartnerId(partnerId);
				approver.setDayReportCc("${flsr},${asm}");
			}
			return approverList;
		} else {
			Map<String, Object> approverParam = new HashMap<String,Object>();
			approverParam.put("partnerId", partnerId);
			approverParam.put("funFlag", "order_confirm");
			return resonsibleMapper.querySpecialPartnerResponsibleByParams(approverParam);
		}
    }

	private void insertApproveInfo(PartnerOrderVo partnerOrder, String currentStep, Integer isApproved, String approvalRemark, Date curDate) {
		ApprovalInfoVo record = new ApprovalInfoVo();
		record.setApprovalComment(approvalRemark);
		record.setCurrentStep(currentStep);
		record.setFlowStep(partnerOrder.getStatus());
		record.setIsApproved(isApproved);
		record.setOperateTime(curDate);
		record.setOperator(ContextUtil.getCurUserId());
		record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
		record.setSourceId(partnerOrder.getId());
		approvalInfoVoMapper.insert(record);
	}
	/**
	 * 价格录入
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> csrConfirm(PartnerOrderVo partnerOrder,PartnerOrderLineVo[] partnerOrderLines,String approvalRemark) throws Exception {
		String regionName = getOrderRegionName(partnerOrder);
		//工业油Industrial只发送给工业油CSR(注意大小写)
		Boolean industrialOrderFlag = regionName.indexOf(Constants.SALES_CHANNEL_DIRECT_INDUSTRIAL) >= 0;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Date curDate = new Date();
		partnerOrder.setUpdateTime(curDate);

		if(PartnerOrderVo.PARTNER_ORDER_STATUS_CSR_RECALL.equals(partnerOrder.getStatus())) {
			//价格确认撤回
			insertApproveInfo(partnerOrder, PartnerOrderVo.PARTNER_ORDER_STATUS_3, 1, approvalRemark, curDate);
			partnerOrderVoMapper.updateByPrimaryKey(partnerOrder);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			return resultMap;
		}
		if (partnerOrder == null || partnerOrderLines == null || partnerOrderLines.length == 0) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人订单数据错误!");
			return resultMap;
		}
		PartnerOrderVo partnerOrderExsited = partnerOrderVoMapper.selectByPrimaryKey(partnerOrder.getId().toString());
		// 订单明细更新
		Long indexNo = 0L;
		BigDecimal orderDiscountedTotalOrderValue = new BigDecimal(0);
		Double totalLiterCount = 0D;
		for (PartnerOrderLineVo partnerOrderLineVo : partnerOrderLines) {			
			indexNo = indexNo + 1;			
			if(PartnerOrderVo.PARTNER_ORDER_STATUS_4.equals(partnerOrder.getStatus())){
				partnerOrderLineVo.setLadderPrice(null);
				partnerOrderLineVo.setDiscountedPrice(null);
				partnerOrderLineVo.setDeductibleAmount(null);
				partnerOrderLineVo.setTotalValueAfterDeductible(null);
			}
			if(!PartnerOrderVo.PARTNER_ORDER_STATUS_4.equals(partnerOrder.getStatus())){
				orderDiscountedTotalOrderValue = orderDiscountedTotalOrderValue.add(partnerOrderLineVo.getDiscountedTotalValue());
			}
		}
		if(PartnerOrderVo.PARTNER_ORDER_STATUS_3.equals(partnerOrder.getStatus())){
			/*partnerOrder.setTotalLiterCount(totalLiterCount);*/
			partnerOrder.setDiscountedTotalOrderPrice(orderDiscountedTotalOrderValue);	
			
			BigDecimal deductibleAmount = BigDecimal.ZERO;//partnerOrder.getDeductibleAmount().setScale(2,BigDecimal.ROUND_HALF_UP);
			BigDecimal totalValueAfterDeductible = partnerOrder.getDiscountedTotalOrderPrice().subtract(deductibleAmount);
			partnerOrder.setDeductibleAmount(deductibleAmount);
			partnerOrder.setTotalValueAfterDeductible(totalValueAfterDeductible);
		}else{
			partnerOrder.setDeductibleAmount(null);
			partnerOrder.setTotalValueAfterDeductible(null);
		}
				
		int count = partnerOrderVoMapper.updateByPrimaryKey(partnerOrder);
		if (count != 1) {
			throw new Exception();
		}
		
		partnerOrder.setPartnerOrderDetailLst(Arrays.asList(partnerOrderLines));

		for(PartnerOrderLineVo partnerOrderLineVo : partnerOrderLines){
			PartnerOrderLineVoExample polExample = new PartnerOrderLineVoExample();
			polExample.createCriteria().andIdEqualTo(partnerOrderLineVo.getId());
			partnerOrderLineVoMapper.updateByExampleSelective(partnerOrderLineVo, polExample);
		}
		// 审核记录保存
		insertApproveInfo(partnerOrder, PartnerOrderVo.PARTNER_ORDER_STATUS_1, 
				PartnerOrderVo.PARTNER_ORDER_STATUS_4.equals(partnerOrder.getStatus()) ? 0 : 1, approvalRemark, curDate);
		
		if (PartnerOrderVo.PARTNER_ORDER_STATUS_3.equals(partnerOrder.getStatus())) {
			//给合伙人发送短信、邮件
			{
				//给订单的创建者发消息
				WxTUser partOrderCreater =  userMapper.selectByPrimaryKey(Long.valueOf(partnerOrder.getCreator()));
				
				String mobileTelephone = partOrderCreater.getMobileTel(); 
				if(!StringUtils.isEmpty(mobileTelephone)){
					String createTimeStr = DateUtil.getDateStr(partnerOrder.getCreateTime());
					String subjectName = "尊敬的雪佛龙合伙人，您" + createTimeStr + "日所下"+regionName+"订单：" + partnerOrder.getOrderNo() + "订单价格已经计算完成，请安排付款.";
					sendCaptcha2Phone(mobileTelephone,subjectName);
				}
				
				if(partOrderCreater != null && partOrderCreater.getEmail() != null){
					String[] accepters = new String[1];
					accepters[0] = partOrderCreater.getEmail();
					String saleName = partOrderCreater.getChName();
					String createTimeStr = DateUtil.getDateStr(partnerOrder.getCreateTime());
					PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrderExsited,true);
					String orderChinesStatus = getOrderChineseStatusByStatusCode(partnerOrder.getStatus());
					partnerOrderView.setIndustrialOrderFlag(industrialOrderFlag);
					if(industrialOrderFlag){
						partnerOrderView.setReceiveTypeTextShowStyle("style=\"display:none\"");
					}
					String subjectName ="订单已审批,合伙人[" + partnerOrder.getPartnerName() + "]采购"+regionName+"订单:" + partnerOrder.getOrderNo();
					Map<String,Object> contentMap = new HashMap<String,Object>();
					String acceptPersonName = "尊敬的" + saleName;
					String partnerName = "您" + createTimeStr + "日所下"+regionName+"订单：" + partnerOrder.getOrderNo() + "订单价格已经计算完成，请安排付款，订单详情如下：";
					contentMap.put("acceptPersonName", acceptPersonName);
					contentMap.put("partnerName", partnerName);
					contentMap.put("skuList", partnerOrderView.getOlEmailList());
					contentMap.put("partnerOrderVo", partnerOrderView);
					List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
					contentMap.put("approvalList", approvalList);
					
					String partnerProperty = partnerService.getPartnerProperty(partnerOrder.getPartnerId());
					if("KA".equals(partnerProperty)){
//						File[] attFiles = generateKapartnerOrderPDF(partnerOrder.getId(),true,HttpSessionGets.getSession().getServletContext());
						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
								accepters, null, subjectName, contentMap, null, "ka_partner_order_info.ftl");
					}else{
//						File[] attFiles = generatePartnerOrderPDF(partnerOrder.getId(),true,HttpSessionGets.getSession().getServletContext());
						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
								accepters, null, subjectName, contentMap, null, "partner_order_info.ftl");
					}
				}
			}
		} else if (PartnerOrderVo.PARTNER_ORDER_STATUS_4.equals(partnerOrder.getStatus())) {
			partnerOrder = partnerOrderVoMapper.selectByPrimaryKey(partnerOrder.getId().toString());
			String orderCreatorId = partnerOrder.getCreator();
			WxTUser creator = userMapper.selectByPrimaryKey(Long.valueOf(orderCreatorId));
			
			Map<String, Object> approverParam = new HashMap<String,Object>();
			approverParam.put("approverRoleName", Constants.CHEVRON_PRICE_AUDIT);
			approverParam.put("partnerId", partnerOrder.getPartnerId());
			approverParam.put("funFlag", "order_confirm");
			approverParam.put("salesChannelName", Constants.SALES_CHANNEL_CDM);
			List<PartnerResponsibleVo> approverList = resonsibleMapper.queryPartnerResponsible(approverParam);
			
			String[] accepterCCs = null;
			if(approverList != null && !approverList.isEmpty()){
				List<String> ccEmailList = new ArrayList<String>();
				for(PartnerResponsibleVo approver : approverList){
					if( !StringUtils.isNull(approver.getDayReportCc())){
						String[] ccEmailArray = approver.getDayReportCc().split(",");
						ccEmailList.addAll(Arrays.asList(ccEmailArray));
					}
				}
				accepterCCs = new String[ccEmailList.size()];
				accepterCCs = ccEmailList.toArray(accepterCCs);
			}
			
			if(creator != null && creator.getEmail() != null){
				String[] accepters = new String[1];
				accepters[0] = creator.getEmail();
				String saleName = creator.getChName();
				PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrderExsited,false);
				//5. 发送邮件及Excel附件
				//工业油:邮箱以及Excel内容不需要显示“收货模式”
				partnerOrderView.setIndustrialOrderFlag(industrialOrderFlag);
				if(industrialOrderFlag){
					partnerOrderView.setReceiveTypeTextShowStyle("style=\"display:none\"");
				}
				String orderChinesStatus = getOrderChineseStatusByStatusCode(partnerOrder.getStatus());
				String subjectName = orderChinesStatus + ",合伙人[" + partnerOrder.getPartnerName() + "]采购"+regionName+"订单:" + partnerOrder.getOrderNo();
				Map<String,Object> contentMap = new HashMap<String,Object>();
				String acceptPersonName = "尊敬的" + saleName;
				String partnerName = "合伙人" + partnerOrder.getPartnerName() + ",有"+regionName+"订单已驳回,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
				contentMap.put("acceptPersonName", acceptPersonName);
				contentMap.put("partnerName", partnerName);
				contentMap.put("skuList", partnerOrderView.getOlEmailList());
				contentMap.put("partnerOrderVo", partnerOrderView);
				List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
				contentMap.put("approvalList", approvalList);
				
				String partnerProperty = partnerService.getPartnerProperty(partnerOrderExsited.getPartnerId());

				if("KA".equals(partnerProperty)){				
					File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,false,HttpSessionGets.getSession().getServletContext());
					emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
							accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
				}else{
					File[] attFiles = generatePartnerOrderExcel(partnerOrderView,false,HttpSessionGets.getSession().getServletContext());
					emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
							accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_info.ftl");
					
				}
				
			}
		}
		
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return resultMap;
	}
	
	/**
	 * 价格录入后recall
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> csrConfirmRecall(Long partnerOrderId)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Date curDate = new Date();
		PartnerOrderVo order  = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId.toString());
		Integer currentStatus = Integer.valueOf(order.getStatus());
		//如果是订单状态是待出库  需要删除相关出库单、网管管家订单这些相关事务
		if(order.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_5)){
			 OutStockVoExample outStockExample = new OutStockVoExample(); 
			 OutStockVoExample.Criteria outStockCriteria = outStockExample.createCriteria();
			 outStockCriteria.andOrderNoEqualTo(order.getOrderNo());
			 outStockCriteria.andOrderTypeEqualTo(com.common.constants.Constants.STOCK_ORDER_TYPE_SP);
			 List<OutStockVo> outStockList = outStockMapper.selectByExample(outStockExample);
			 for(OutStockVo outstock : outStockList){
				 if(outstock.getStatus().equals(Constants.STOCK_OUT_STATUS_CHUKU)){
					 resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);				
					 resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "订单中已有商品已出库，不能召回!");
					 return resultMap;
				 }
			 }
			 List<OrderVo> wdgjOrderList = orderVoMapper.selectBySapOrderNo(order.getOrderNo());
			 for(OrderVo wdgjOrder : wdgjOrderList){
				 if((!wdgjOrder.getStatus().equals(OrderVo.ORDER_STATUS_4)) && (!wdgjOrder.getStatus().equals(OrderVo.ORDER_STATUS_6))){
					 resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);				
					 resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "订单中通过网店管家发货的订单发货，不能召回!");
					 return resultMap;
				 }
			 }
			 //删除相关出库单
			 for(OutStockVo outstock : outStockList){
				 Map<String, Object> outstockDeleteResult =  appOutStockService.deleteOutStock(outstock.getStockOutNo());
				 if(outstockDeleteResult.get(Constants.RESULT_CODE_KEY).toString().equals(Constants.ERROR_CODE)){
					 TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
					 resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);				
					 resultMap.put(Constants.RESULT_ERROR_MSG_KEY, outstockDeleteResult.get("codeMsg").toString());
					 return resultMap;
				 }
			 }
			 //更新网店管家发货的订单为已取消
			 for(OrderVo wdgjOrder : wdgjOrderList){
				 wdgjOrder.setStatus(OrderVo.ORDER_STATUS_6);
				 orderVoMapper.updateByPrimaryKey(wdgjOrder);
			 }	
		}
		
		order.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
		order.setUpdateTime(curDate);
		order.setDeductibleAmount(null);
		order.setTotalValueAfterDeductible(null);
		int count = partnerOrderVoMapper.updateByPrimaryKey(order);
		if (count != 1) {
			throw new Exception();
		}
		List<PartnerOrderLineVo> partnerOrderLines = partnerOrderLineVoMapper.selectByOrderId(partnerOrderId.toString());
		for(PartnerOrderLineVo orderLine : partnerOrderLines){
			orderLine.setDiscountedPrice(null);
			orderLine.setLadderPrice(null);
			orderLine.setDiscountedTotalValue(null);
			orderLine.setDiscountFee(null);
			orderLine.setDeductibleAmount(null);
			orderLine.setTotalValueAfterDeductible(null);
		}
		order.setPartnerOrderDetailLst(partnerOrderLines);
		int orderLineResut = partnerOrderLineVoMapper.updatePriceByPrimaryKey(order);
		if (orderLineResut != partnerOrderLines.size()) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);				
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "回滚订单商品价格失败");
		}
		//删除物流配送
		 List<PartnerOrderDelivery> partnerOrderDeliveryList = partnerOrderDeliveryService.getParterOrderDeliveryByPartnerOrderNo(order.getOrderNo());
		 Map<String, Object> deliveryDeleteResult = partnerOrderDeliveryService.deletePartnerOrderDeliveryList(partnerOrderDeliveryList);
		 if(deliveryDeleteResult.get(Constants.RESULT_CODE_KEY).toString().equals(Constants.ERROR_CODE)){
			 TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			 resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);				
			 resultMap.put(Constants.RESULT_ERROR_MSG_KEY, deliveryDeleteResult.get(Constants.RESULT_CODE_MSG_KEY).toString());
			 return resultMap;
		 }
		// 订单明细更新
		List<PartnerOrderLineVoEmail> olEmailList = new ArrayList<PartnerOrderLineVoEmail>();
		Long indexNo = 0L;
		for (PartnerOrderLineVo partnerOrderLineVo : partnerOrderLines) {
			
			indexNo = indexNo + 1;
			PartnerOrderLineVoEmail olEmail = new PartnerOrderLineVoEmail();
			olEmail.setSku(partnerOrderLineVo.getSku());
			olEmail.setSkuDesc(partnerOrderLineVo.getProductName());
			olEmail.setAmount(partnerOrderLineVo.getActualAmount().toString());
			if(partnerOrderLineVo.getFreeAmount() != null){
				olEmail.setFreeAmount(partnerOrderLineVo.getFreeAmount().toString());
			}
			olEmailList.add(olEmail);
			partnerOrderLineVo.setActualAmount(partnerOrderLineVo.getAmount());
			partnerOrderLineVo.setStatus("1");
		}
		
		order.setPartnerOrderDetailLst(partnerOrderLines);
		count = partnerOrderLineVoMapper.updatePriceByPrimaryKey(order);
		
		// 审核记录保存
		ApprovalInfoVo record = new ApprovalInfoVo();
		record.setCurrentStep(currentStatus.toString());
		record.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
		record.setIsApproved(0);
		record.setOperateTime(curDate);
		record.setOperator(ContextUtil.getCurUserId());
		record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
		record.setSourceId(order.getId());
		approvalInfoVoMapper.insert(record);
		if (count != partnerOrderLines.size()) {
			throw new Exception();
		} 
		
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return resultMap;
	}
	
	private Map<String,String> getProductSkuCapacityMap(){
		Map<String,String> result = new HashMap<String,String>();
		List<ProductVo> productList = productMapper.getProductsByParam(null);
		for(ProductVo  product: productList){
			if(!StringUtils.isNull(product.getCapacity()) && StringUtils.isNotEmpty(product.getCapacity())){
				result.put(product.getSku(), product.getCapacity());
			}
		}
		return result;
	}

	@Override
	public Map<String, Object> deletePartnerOrder(String partnerOrderId)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		try{
			PartnerOrderVo partnerOrderVo = new PartnerOrderVo();
			partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId);
			OutStockVoExample outStockExample = new OutStockVoExample();
			OutStockVoExample.Criteria outStockCriteria = outStockExample.createCriteria();
			outStockCriteria.andOrderNoEqualTo(partnerOrderVo.getOrderNo());
			List<OutStockVo> outStockVoList = outStockMapper.selectByExample(outStockExample);
			if(outStockVoList != null && (!outStockVoList.isEmpty())){
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "该订单已进入流程，不能删除！");
				return resultMap;
			}
			if( partnerOrderVo.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1) || partnerOrderVo.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2)  ){
				PartnerOrderVoExample partnerOrderVoExample = new PartnerOrderVoExample();				
				partnerOrderVoExample.createCriteria().andIdEqualTo(Long.valueOf(partnerOrderId));
				int deleteParnerOrderResult = partnerOrderVoMapper.deleteByExample(partnerOrderVoExample);
				
				PartnerOrderLineVoExample partnerOrderLineVoExample = new PartnerOrderLineVoExample();
				partnerOrderLineVoExample.createCriteria().andPartnerOrderIdEqualTo(Long.valueOf(partnerOrderId));
				int deleteParnerOrderLineResult = partnerOrderLineVoMapper.deleteByExample(partnerOrderLineVoExample);
				
				WXTPartnerOrderAddressProductVoExample addressProductExample = new WXTPartnerOrderAddressProductVoExample();
				addressProductExample.createCriteria().andPartnerOrderIdEqualTo(Long.valueOf(partnerOrderId));
				int deleteAddressProductResult = addressProductMapper.deleteByExample(addressProductExample);
				
				//partnerOrderWorkflowService.cancelProcessInstance(partnerOrderVo.getOrderNo());
			}else{
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "该订单已进入流程，不能删除！");
				return resultMap;
			}
		}catch(Exception e){
			log.error(e.getMessage());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
		
	}
	
	private  File[] generatePartnerOrderExcel(PartnerOrderPdfView partnerOrderView,boolean showPrice,ServletContext servletContext) {
		OutputStream outputStream = null;
		try {
			
			WritableWorkbook workbook = null;
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh_mm_ss");
			String orderTimeStr = df.format(partnerOrderView.getCreateTime());
			String filePath = servletContext.getRealPath(MyPropertyConfigurer
					.getVal("PARTNER_ORDER_EMAIL_TMP_DATA"))+File.separator  + partnerOrderView.getPartnerName() + "_" + partnerOrderView.getPartnerOrderNo() +".xls";
			File file = new File(
					filePath.trim());
			outputStream = new FileOutputStream(file);
	
			// 用于正文
			WritableFont NormalFont = new WritableFont(WritableFont.TAHOMA,
					11);
			WritableFont NormalFont2 = new WritableFont(WritableFont.TAHOMA,
					11);
			NormalFont.setColour(Colour.BLACK);
			NormalFont2.setColour(Colour.BLACK);
			WritableCellFormat wcf_center = new WritableCellFormat(
					NormalFont);
			wcf_center.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			wcf_center.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_center.setAlignment(Alignment.CENTRE);
			wcf_center.setWrap(true); // 是否换行

			WritableCellFormat wcf_left = new WritableCellFormat(
					NormalFont2);
			wcf_left.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			wcf_left.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_left.setAlignment(Alignment.LEFT);
			wcf_left.setWrap(true); // 是否换行
			
            WritableFont wf_title = new WritableFont(WritableFont.ARIAL, 11,  
                    WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE,  
                    jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色  
			WritableCellFormat wcf_title1 = new WritableCellFormat(wf_title); // 单元格定义  
	        wcf_title1.setAlignment(jxl.format.Alignment.CENTRE); // 设置对齐方式  
	        wcf_title1.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.NONE,jxl.format.Colour.BLACK);
			

			workbook = Workbook.createWorkbook(outputStream);
			//创建sheet0的数据
			workbook = createWorkbookSheet1ForPartnerOrder(workbook,partnerOrderView,showPrice,wcf_title1,wcf_center,wcf_left);
			
			workbook.write();
			workbook.close();
			outputStream.flush();
			outputStream.close();
		
			//附件集合
			File[] attfiles = new File[1];
			attfiles[0] = file;
			return attfiles;
			

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	private WritableWorkbook createWorkbookSheet1ForPartnerOrder(WritableWorkbook workBook,PartnerOrderPdfView partnerOrderView,boolean showPrice ,WritableCellFormat wcf_title,WritableCellFormat wcf_center,WritableCellFormat wcf_center2){
		
		//普通产品数据
		Class esHAContentClazz = PartnerOrderLineVoEmail.class;//内容部分
		// 类 声明的字段集合
		Field[] esHAContentFields = esHAContentClazz.getDeclaredFields();
		Arrays.sort(esHAContentFields, new FieldComparator());
		// 加了注解的 字段集合=需要导出的字段集合
		ArrayList<Field> annationContentFields = new ArrayList<Field>();

		
		try
		{
			WritableFont NormalFont = new WritableFont(WritableFont.TAHOMA,
					11);
			NormalFont.setColour(Colour.BLACK);
			WritableCellFormat wcf_right = new WritableCellFormat(
					NormalFont);
			wcf_right.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			wcf_right.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_right.setAlignment(Alignment.RIGHT);
			wcf_right.setWrap(true); // 是否换行
			
			WritableCellFormat wcf_no_border = new WritableCellFormat(
					NormalFont);
			wcf_no_border.setBorder(Border.ALL, BorderLineStyle.NONE,
					Colour.GRAY_80);
			wcf_no_border.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_no_border.setAlignment(Alignment.LEFT);
			wcf_no_border.setWrap(true); // 是否换行
			
			WritableSheet sheet = workBook.createSheet("productinfo", 0);	
			wcf_center.setAlignment(Alignment.LEFT);
			//中部内容
			for (int i = 0, j = 0; i < esHAContentFields.length; i++) {
				Field field = esHAContentFields[i];
				if (field.isAnnotationPresent(ExpAnnotation.class)) {
					annationContentFields.add(field);
					// 获取该字段的注解对象
					ExpAnnotation anno = field
							.getAnnotation(ExpAnnotation.class);
					sheet.setColumnView(j, anno.width());
					sheet.addCell(new Label(j, 0, anno.name(),
							wcf_center));
					j++;
				}
			}
			List<PartnerOrderLineVoEmail> olList = partnerOrderView.getOlEmailList();
			for (int k = 0;  k < olList.size(); k++) {
				// 数据bean
				Object esContentModel = olList.get(k);
				// 反射遍历字段
				for (int field_index = 0; field_index < annationContentFields
						.size(); field_index++) {
					try {
						Field tempField = annationContentFields
								.get(field_index);
						String methodName = "get"
								+ tempField.getName()
										.substring(0, 1)
										.toUpperCase()
								+ tempField.getName().substring(1);
						Method method = esContentModel.getClass()
								.getMethod(methodName);
						// 值/
						String tempvalue = method
								.invoke(esContentModel) == null ? ""
								: method.invoke(esContentModel)
										.toString();
						// 行号
						int c = k + 1;
						if(field_index > 3){
							sheet.addCell(new Label(field_index, c,
									tempvalue, wcf_right));
						}else{
							sheet.addCell(new Label(field_index, c,
								tempvalue, wcf_center2));
						}

					} catch (IllegalArgumentException e) {
						log.error(e.getMessage(), e);
					} catch (IllegalAccessException e) {
						log.error(e.getMessage(), e);
					} catch (InvocationTargetException e) {
						log.error(e.getMessage(), e);
					}
				}
			}
			//添加总金额：
			int rowIndex = olList.size() + 1;
			
			
			Map<Long,Map<String,Object>> addressMap = buildAddressMap(partnerOrderView.getPartnerOrderVo());
			String addresscontact = "";
				for(Long addressId : addressMap.keySet()){
					Map<String,Object> addressProductMap = addressMap.get(addressId);
					addresscontact =  addresscontact + (String)addressProductMap.get("address") + (String)addressProductMap.get("contact") + ";";
				}
			addresscontact = addresscontact.substring(0,addresscontact.length()-1);
			
			
			WritableFont totalFont = new WritableFont(WritableFont.TAHOMA,
					11);
			totalFont.setBoldStyle(WritableFont.BOLD);
			WritableCellFormat totalFormat = new WritableCellFormat(
					totalFont);
			totalFormat.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			totalFormat.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			totalFormat.setAlignment(Alignment.LEFT);
			totalFormat.setWrap(true);
			
			WritableCellFormat totalFormat_right = new WritableCellFormat(
					totalFont);
			totalFormat_right.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			totalFormat_right.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			totalFormat_right.setAlignment(Alignment.RIGHT);
			totalFormat_right.setWrap(true);
			sheet.addCell(new Label(0, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(1, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(2, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(3, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(4, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(5, rowIndex,
					partnerOrderView.getTotalLiterCount(), totalFormat_right));
			sheet.addCell(new Label(6, rowIndex,
					"", totalFormat));
			/*sheet.addCell(new Label(7, rowIndex,
					"", totalFormat));*/
			sheet.addCell(new Label(7, rowIndex,
					"总金额：", totalFormat));
			if(showPrice){
				sheet.addCell(new Label(8, rowIndex,
						partnerOrderView.getDiscountedTotalOrderPrice(), totalFormat_right));
				/*sheet.addCell(new Label(10, rowIndex,
						partnerOrderView.getDeductibleAmount(), totalFormat_right));
				sheet.addCell(new Label(11, rowIndex,
						partnerOrderView.getTotalValueAfterDeductible(), totalFormat_right));*/
			}else{
				sheet.addCell(new Label(8, rowIndex,
						"", totalFormat));
				/*sheet.addCell(new Label(10, rowIndex,
						"", totalFormat));
				sheet.addCell(new Label(11, rowIndex,
						"", totalFormat));*/
			}
			sheet.addCell(new Label(9, rowIndex,
					"", totalFormat));
			rowIndex++;
			
			sheet.addCell(new Label(0, ++rowIndex,
					"Sales Org：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					partnerOrderView.getSalesOrg(), wcf_no_border));
			sheet.addCell(new Label(0, ++rowIndex,
					"专用客户代码：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					partnerOrderView.getPartnerOrderVo().getShipToCode()/*.getSalesCode()-->.getPartnerOrderVo().getShipToCode()*/, wcf_no_border));
//			if(!StringUtils.isEmpty(partnerOrderView.getPurpose())){
//				sheet.addCell(new Label(0, ++rowIndex,
//						"订单类型：", wcf_no_border));
//				sheet.addCell(new Label(1, rowIndex,
//						partnerOrderView.getPurpose(), wcf_no_border));
//			}
//			if("1".equals(partnerOrderView.getIsQuotaRatio())){
//				sheet.addCell(new Label(0, ++rowIndex,
//						"一般Sales Org：", wcf_no_border));
//				sheet.addCell(new Label(1, rowIndex,
//						partnerOrderView.getQuotaRatioSalesOrg(), wcf_no_border));
//				sheet.addCell(new Label(0, ++rowIndex,
//						"一般专用客户代码：", wcf_no_border));
//				sheet.addCell(new Label(1, rowIndex,
//						partnerOrderView.getQuotaRatioSalesCode(), wcf_no_border));
//			}
			sheet.addCell(new Label(0, ++rowIndex,
					"经销商名称：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					partnerOrderView.getPartnerName(), wcf_no_border));
			sheet.addCell(new Label(0, ++rowIndex,
					"地址联系人：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					addresscontact, wcf_no_border));
			if(!partnerOrderView.getIndustrialOrderFlag()) {
				sheet.addCell(new Label(0, ++rowIndex,
						"收货模式：", wcf_no_border));
				sheet.addCell(new Label(1, rowIndex,
						StringUtils.nvl(partnerOrderView.getPartnerOrderVo().getReceiveTypeText(), ""), wcf_no_border));
			}
			
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
		
		return workBook;
	}
	
	private  File[] generateKapartnerOrderExcel(PartnerOrderPdfView partnerOrderView,boolean showPrice,ServletContext servletContext) {
		OutputStream outputStream = null;
		try {
			
			WritableWorkbook workbook = null;
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh_mm_ss");
			String orderTimeStr = df.format(partnerOrderView.getCreateTime());
			String filePath = servletContext.getRealPath(MyPropertyConfigurer
					.getVal("PARTNER_ORDER_EMAIL_TMP_DATA"))+File.separator  + partnerOrderView.getPartnerName() + "_" + partnerOrderView.getPartnerOrderNo() +".xls";
			File file = new File(
					filePath.trim());
			outputStream = new FileOutputStream(file);
	
			// 用于正文
			WritableFont NormalFont = new WritableFont(WritableFont.TAHOMA,
					11);
			WritableFont NormalFont2 = new WritableFont(WritableFont.TAHOMA,
					11);
			NormalFont.setColour(Colour.BLACK);
			NormalFont2.setColour(Colour.BLACK);
			WritableCellFormat wcf_center = new WritableCellFormat(
					NormalFont);
			wcf_center.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			wcf_center.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_center.setAlignment(Alignment.CENTRE);
			wcf_center.setWrap(true); // 是否换行

			WritableCellFormat wcf_left = new WritableCellFormat(
					NormalFont2);
			wcf_left.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			wcf_left.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_left.setAlignment(Alignment.LEFT);
			wcf_left.setWrap(true); // 是否换行
			
            WritableFont wf_title = new WritableFont(WritableFont.ARIAL, 11,  
                    WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE,  
                    jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色  
			WritableCellFormat wcf_title1 = new WritableCellFormat(wf_title); // 单元格定义  
	        wcf_title1.setAlignment(jxl.format.Alignment.CENTRE); // 设置对齐方式  
	        wcf_title1.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.NONE,jxl.format.Colour.BLACK);
			

			workbook = Workbook.createWorkbook(outputStream);
			//创建sheet0的数据
			workbook = createWorkbookSheetForKapartnerOrder(workbook,partnerOrderView,showPrice,wcf_title1,wcf_center,wcf_left);
			
			workbook.write();
			workbook.close();
			outputStream.flush();
			outputStream.close();
		
			//附件集合
			File[] attfiles = new File[1];
			attfiles[0] = file;
			return attfiles;
			

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	private WritableWorkbook createWorkbookSheetForKapartnerOrder(WritableWorkbook workBook,PartnerOrderPdfView partnerOrderView,boolean showPrice ,WritableCellFormat wcf_title,WritableCellFormat wcf_center,WritableCellFormat wcf_center2){
		
		//普通产品数据
		Class esHAContentClazz = KapartnerOrderLineVoEmail.class;//内容部分
		// 类 声明的字段集合
		Field[] esHAContentFields = esHAContentClazz.getDeclaredFields();
		Arrays.sort(esHAContentFields, new FieldComparator());
		// 加了注解的 字段集合=需要导出的字段集合
		ArrayList<Field> annationContentFields = new ArrayList<Field>();

		
		try
		{
			WritableFont NormalFont = new WritableFont(WritableFont.TAHOMA,
					11);
			NormalFont.setColour(Colour.BLACK);
			WritableCellFormat wcf_right = new WritableCellFormat(
					NormalFont);
			wcf_right.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			wcf_right.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_right.setAlignment(Alignment.RIGHT);
			wcf_right.setWrap(true); // 是否换行
			
			WritableCellFormat wcf_no_border = new WritableCellFormat(
					NormalFont);
			wcf_no_border.setBorder(Border.ALL, BorderLineStyle.NONE,
					Colour.GRAY_80);
			wcf_no_border.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			wcf_no_border.setAlignment(Alignment.LEFT);
			wcf_no_border.setWrap(true); // 是否换行
			
			WritableSheet sheet = workBook.createSheet("productinfo", 0);	
			wcf_center.setAlignment(Alignment.LEFT);
			//中部内容
			for (int i = 0, j = 0; i < esHAContentFields.length; i++) {
				Field field = esHAContentFields[i];
				if (field.isAnnotationPresent(ExpAnnotation.class)) {
					annationContentFields.add(field);
					// 获取该字段的注解对象
					ExpAnnotation anno = field
							.getAnnotation(ExpAnnotation.class);
					sheet.setColumnView(j, anno.width());
					sheet.addCell(new Label(j, 0, anno.name(),
							wcf_center));
					j++;
				}
			}
			List<PartnerOrderLineVoEmail> olList = partnerOrderView.getOlEmailList();
			for (int k = 0;  k < olList.size(); k++) {
				// 数据bean
				Object esContentModel = olList.get(k);
				// 反射遍历字段
				for (int field_index = 0; field_index < annationContentFields
						.size(); field_index++) {
					try {
						Field tempField = annationContentFields
								.get(field_index);
						String methodName = "get"
								+ tempField.getName()
										.substring(0, 1)
										.toUpperCase()
								+ tempField.getName().substring(1);
						Method method = esContentModel.getClass()
								.getMethod(methodName);
						// 值/
						String tempvalue = method
								.invoke(esContentModel) == null ? ""
								: method.invoke(esContentModel)
										.toString();
						// 行号
						int c = k + 1;
						if(field_index > 3){
							sheet.addCell(new Label(field_index, c,
									tempvalue, wcf_right));
						}else{
							sheet.addCell(new Label(field_index, c,
								tempvalue, wcf_center2));
						}

					} catch (IllegalArgumentException e) {
						log.error(e.getMessage(), e);
					} catch (IllegalAccessException e) {
						log.error(e.getMessage(), e);
					} catch (InvocationTargetException e) {
						log.error(e.getMessage(), e);
					}
				}
			}
			//添加总金额：
			int rowIndex = olList.size() + 1;
			
			//获取订单地址，如果是单一地址，则显示出来
			List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderView.getPartnerOrderId());
			
			Map<Long,Object> addressMap = new HashMap<Long,Object>();
			
			Set<Long> addressIdSet = new HashSet<Long>();
			for(WXTPartnerOrderAddressProductView temp : addressProductList){
				Map<String,Object> addressProductMap = new HashMap<String,Object>();
				if(!addressIdSet.contains(temp.getAddressId())){
					addressIdSet.add(temp.getAddressId());
					addressProductMap.put("addressObj", temp);
					if(partnerOrderView.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
						String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress();					
						addressProductMap.put("address", addressString);
						addressProductMap.put("contact", temp.getContactPerson()+ " " + temp.getMobile());
					}else{
						String addressString = temp.getWorkshopAddress();
						addressProductMap.put("address", addressString);
						addressProductMap.put("contact", temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone());
					}
					addressProductMap.put("addressId", temp.getAddressId());
					addressMap.put(temp.getAddressId(), addressProductMap);
				}
			}	
			String addresscontact = "";
			if(addressIdSet.size() > 0){
				for(Long addressId : addressIdSet){
					Map<String,Object> addressProductMap = (Map<String,Object>)addressMap.get(addressId);
					addresscontact =  addresscontact + (String)addressProductMap.get("address") + (String)addressProductMap.get("contact") + ";";
				}
			}
			addresscontact = addresscontact.substring(0,addresscontact.length()-1);
			
			
			WritableFont totalFont = new WritableFont(WritableFont.TAHOMA,
					11);
			totalFont.setBoldStyle(WritableFont.BOLD);
			WritableCellFormat totalFormat = new WritableCellFormat(
					totalFont);
			totalFormat.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			totalFormat.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			totalFormat.setAlignment(Alignment.LEFT);
			totalFormat.setWrap(true);
			
			WritableCellFormat totalFormat_right = new WritableCellFormat(
					totalFont);
			totalFormat_right.setBorder(Border.ALL, BorderLineStyle.THIN,
					Colour.GRAY_80);
			totalFormat_right.setVerticalAlignment(VerticalAlignment.CENTRE); // 垂直对齐
			totalFormat_right.setAlignment(Alignment.RIGHT);
			totalFormat_right.setWrap(true);
			sheet.addCell(new Label(0, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(1, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(2, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(3, rowIndex,
					"", totalFormat));
			sheet.addCell(new Label(4, rowIndex,
					partnerOrderView.getTotalLiterCount(), totalFormat_right));
			/*sheet.addCell(new Label(5, rowIndex,
					"", totalFormat));*/
			sheet.addCell(new Label(5, rowIndex,
					"总金额：", totalFormat));
			if(showPrice){
				sheet.addCell(new Label(6, rowIndex,
						partnerOrderView.getDiscountedTotalOrderPrice(), totalFormat_right));
			}else{
				sheet.addCell(new Label(6, rowIndex,
						"", totalFormat));
			}
			
			sheet.addCell(new Label(0, ++rowIndex,
					"专用客户代码：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					partnerOrderView.getPartnerOrderVo().getShipToCode()/*.getPartnerShipToCode()-->.getPartnerOrderVo().getShipToCode()*/, wcf_no_border));
			sheet.addCell(new Label(0, ++rowIndex,
					"经销商名称：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					partnerOrderView.getPartnerName(), wcf_no_border));
			sheet.addCell(new Label(0, ++rowIndex,
					"地址联系人：", wcf_no_border));
			sheet.addCell(new Label(1, rowIndex,
					addresscontact, wcf_no_border));
			
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
		
		return workBook;
	}
	
	@Override
	public Map<String,Object> createSPOrders(List<PartnerOrderVo> lstSPOrders)throws Exception 
	{
		//1.插入订单，并重组orderline
		//存放重组orderline(带有订单号的订单行记录)的集合
		List<PartnerOrderLineVo> newOrderLineVoLst = new ArrayList<PartnerOrderLineVo>();
		for(PartnerOrderVo  newOrderVo : lstSPOrders)
		{
			List<PartnerOrderLineVo> newOrderLineTemp = newOrderVo.getPartnerOrderDetailLst();
			partnerOrderVoMapper.insertSelective(newOrderVo);
			for(PartnerOrderLineVo newOrderLineVo:newOrderLineTemp)
			{
				newOrderLineVo.setPartnerOrderId(newOrderVo.getId());
				newOrderLineVoLst.add(newOrderLineVo);
			}
		}
		
		//2.批量插入订单行（产品）信息到订单上
		//进行分页
		ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(
				newOrderLineVoLst, 50);
		int toltalPage = newOrderLinePage.getTotalPages();
		for(int i=1;i<=toltalPage;i++)
		{
			@SuppressWarnings("unchecked")
			List<PartnerOrderLineVo> insertOrderLineLst  = newOrderLinePage.getObjects(i);
			partnerOrderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);
		}
		return null;
	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> createWdgjDeliveryRequest(PartnerOrderVo partnerOrder) throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		try{
//			// 合伙人订单状态修改
//			Date curDate = new Date();
//			partnerOrder.setUpdateTime(curDate);
//			partnerOrder.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_5);
//			int count = partnerOrderVoMapper.updateByPrimaryKey(partnerOrder);
//			if (count != 1) {
//				throw new Exception();
//			}
//	
//			// 订单明细状态更新
//			partnerOrderLineVoMapper.updateStatusByPOID(partnerOrder);
//			
//			partnerOrder = partnerOrderVoMapper.selectByPrimaryKey(partnerOrder.getId().toString());
//			
//			//生成多个网店管家订单
//			List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrder.getId().toString());
//			
//			Map<Long,Object> addressMap = new HashMap<Long,Object>();
//			
//			Set<Long> addressIdSet = new HashSet<Long>();
//			for(WXTPartnerOrderAddressProductView temp : addressProductList){
//				Map<String,Object> addressProductMap = new HashMap<String,Object>();
//				if(!addressIdSet.contains(temp.getAddressId())){
//					addressIdSet.add(temp.getAddressId());
//					String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress() + " " + temp.getMobile();
//					addressProductMap.put("address", addressString);
//					addressProductMap.put("addressId", temp.getAddressId());
//					List<WXTPartnerOrderAddressProductView> addressProductTemp = new ArrayList<WXTPartnerOrderAddressProductView>();
//					addressProductTemp.add(temp);
//					addressProductMap.put("addressProduct", addressProductTemp);
//					addressMap.put(temp.getAddressId(), addressProductMap);
//				}else{
//					addressProductMap = (Map<String,Object>)addressMap.get(temp.getAddressId());
//					List<WXTPartnerOrderAddressProductView> addressProductTemp = (List<WXTPartnerOrderAddressProductView>)addressProductMap.get("addressProduct"); 
//					addressProductTemp.add(temp);
//					addressProductMap.put("addressProduct", addressProductTemp);
//					addressMap.put(temp.getAddressId(), addressProductMap);
//				}
//			}
//			List<OrderVo> orderList = new ArrayList<OrderVo>();
//			for(Long addressId : addressIdSet){
//						
//				Map<Long,Object> addressResult = new HashMap<Long,Object>();
//				addressResult = (Map<Long,Object>)addressMap.get(addressId);
//				
//				//生成网店管家订单
//				OrderVo wdgjOrder = new OrderVo();
//				wdgjOrder.setSpAddressId(addressId);
//				if(partnerOrder.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
//					PartnerAddress partnerAddress = partnerAddressMapper.getAddressDetailById(addressId);
//					wdgjOrder.setBuyUserNo(Long.valueOf(partnerOrder.getCreator()));
//					wdgjOrder.setAddress(partnerAddress.getProvinceName() + partnerAddress.getCityName() + partnerAddress.getDistName() + partnerAddress.getAddress());
//					wdgjOrder.setReceiveUserName(partnerAddress.getContactPerson());
//					wdgjOrder.setReceivePhoneNo(partnerAddress.getMobile());
//					wdgjOrder.setPhoneNo(partnerAddress.getMobile());
//					wdgjOrder.setRegionName(partnerAddress.getProvinceName());
//				}else if(partnerOrder.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_STPAOD)){
//					wdgjOrder.setBuyUserNo(Long.valueOf(partnerOrder.getCreator()));
//					wdgjOrder.setAddress(addressResult.get("address").toString());
//					wdgjOrder.setReceiveUserName(addressResult.get("contactPerson").toString());
//					wdgjOrder.setReceivePhoneNo(addressResult.get("telephone").toString());
//					wdgjOrder.setPhoneNo(addressResult.get("telephone").toString());
//				}
//				wdgjOrder.setPoOrderPartnerId(partnerOrder.getPartnerId());
//				wdgjOrder.setSapOrderNo(partnerOrder.getOrderNo());
//				wdgjOrder.setCreateTime(new Date());
//				
//				List<OrderLineVo> orderLineLst = new ArrayList<OrderLineVo>();
//				
//				List<WXTPartnerOrderAddressProductView> addressProductTemp = (List<WXTPartnerOrderAddressProductView>)addressResult.get("addressProduct");
//				for(WXTPartnerOrderAddressProductView ol:  addressProductTemp){
//					OrderLineVo orderLine = new OrderLineVo();
//					orderLine.setProductId(ol.getProductId());
//					orderLine.setSku(ol.getSku());
//					orderLine.setProductName(ol.getProductName());
//					orderLine.setActualAmount(ol.getAmount());
//					orderLine.setAmount(ol.getAmount());
//					orderLine.setType(ol.getType());
//					orderLine.setCreateTime(new Date());
//					orderLineLst.add(orderLine);
//				}
//				wdgjOrder.setOrderLines(orderLineLst);	
//				orderList.add(wdgjOrder);
//				
//			}
//			Map<String,Object> resultWebThirdOrder =  webThirdOrderService.createFWOrderForSPID(orderList);
//			if(!resultWebThirdOrder.get("code").equals(MessageContants.SUCCESS_CODE)){
//				TransactionAspectSupport.currentTransactionStatus()
//				.setRollbackOnly();
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "下单失败！");
//			}
//		} catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus()
//			.setRollbackOnly();
//			log.error(e.getMessage(), e);
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
//		}
//		
//		return resultMap;
//	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> generatePartnerOrderByIds(String[] workshopOrderIds) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		//获取orderIds对应的所有的订单
		Map<String, Object> orderIdMap = new HashMap<String, Object>();
		orderIdMap.put("orderIds", new ArrayList<String>(Arrays.asList(workshopOrderIds)));
		List<OrderVo> workshopOrderList = orderVoMapper.getAllOrderByOrderIds(orderIdMap);
		List<OrderLineVo> workshopOrderLineList = orderLinerVoMapper.getAllOrderLinesByOrderIds(orderIdMap);
		
		WorkshopPartnerVo workshopPartnerVo = new WorkshopPartnerVo();
		//解析order
		Map<String,OrderVo> orderIdOrderMap = new HashMap<String,OrderVo>();
		for (OrderVo workshopOrderVo : workshopOrderList) {
			if(workshopOrderVo.getAddress() == null || workshopOrderVo.getAddress().equals("")){
				Map<String,Object> workshopMap = workShopService.getWorkShopAndAdressDetailById(workshopOrderVo.getWorkShopId());
				if(workshopMap.get("workshop") != null && workshopMap.get("address") != null){
					WorkshopMaster workshop = (WorkshopMaster)workshopMap.get("workshop");
					workshopOrderVo.setAddress((String)workshopMap.get("address") + workshop.getWorkShopAddress());
				}
			}
			orderIdOrderMap.put(workshopOrderVo.getId().toString(), workshopOrderVo);	
			if(workshopPartnerVo == null || workshopPartnerVo.getId() == null){
				workshopPartnerVo = workshopPartnerService.getSelectedPartnerByWorkshopId(Long.valueOf(workshopOrderVo.getWorkShopId())).get(0);
			}else{
				WorkshopPartnerVo workshopPartnerVoTemp = workshopPartnerService.getSelectedPartnerByWorkshopId(Long.valueOf(workshopOrderVo.getWorkShopId())).get(0);
				if(!workshopPartnerVo.getPartnerId().equals(workshopPartnerVoTemp.getPartnerId())){
					resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
					resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "待合并的订单只能属于同一个合伙人！");
					return resultMap;
				}
			}
			
		} 
		if(workshopPartnerVo == null){
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "待合并的订单没有找到对应合伙人！");
			return resultMap;
		}
		// 解析orderLines
		List<PartnerOrderLineVo> orderLines = new ArrayList<PartnerOrderLineVo>();
		List<WXTPartnerOrderAddressProductVo> addressProductList = new ArrayList<WXTPartnerOrderAddressProductVo>();
		Map<String,PartnerOrderLineVo> orderSkuLineMap = new HashMap<String,PartnerOrderLineVo>();
		for (OrderLineVo workshopOrderLineVo : workshopOrderLineList) { // 原始的
			if(orderSkuLineMap.containsKey(workshopOrderLineVo.getSku())){
				PartnerOrderLineVo partnerOrderLine = orderSkuLineMap.get(workshopOrderLineVo.getSku());
				partnerOrderLine.setAmount(partnerOrderLine.getActualAmount() + workshopOrderLineVo.getActualAmount());
				orderSkuLineMap.put(workshopOrderLineVo.getSku(), partnerOrderLine);
			}else{
				PartnerOrderLineVo partnerOrderLine = new PartnerOrderLineVo();
				partnerOrderLine.setProductId(workshopOrderLineVo.getProductId());
				partnerOrderLine.setSku(workshopOrderLineVo.getSku());
				partnerOrderLine.setProductName(workshopOrderLineVo.getProductName());
				partnerOrderLine.setAmount(workshopOrderLineVo.getActualAmount());
				partnerOrderLine.setActualAmount(workshopOrderLineVo.getActualAmount());
				partnerOrderLine.setUnits(workshopOrderLineVo.getUnits());
				partnerOrderLine.setType(workshopOrderLineVo.getType());
				partnerOrderLine.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
				orderSkuLineMap.put(workshopOrderLineVo.getSku(), partnerOrderLine);
			}
			
			WXTPartnerOrderAddressProductVo addressProductVo = new WXTPartnerOrderAddressProductVo();
			addressProductVo.setProductId(workshopOrderLineVo.getProductId());
			addressProductVo.setSku(workshopOrderLineVo.getSku());
			addressProductVo.setProductName(workshopOrderLineVo.getProductName());
			addressProductVo.setAmount(workshopOrderLineVo.getActualAmount());
			addressProductVo.setActualAmount(workshopOrderLineVo.getActualAmount());
			addressProductVo.setUnits(workshopOrderLineVo.getUnits());
			addressProductVo.setType(workshopOrderLineVo.getType());
			addressProductVo.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
			
			OrderVo tempOrder =  orderIdOrderMap.get(workshopOrderLineVo.getOrderId().toString());
			addressProductVo.setAddressId(workshopOrderLineVo.getOrderId());
			addressProductVo.setAddress(tempOrder.getAddress());
			addressProductVo.setContactPerson(tempOrder.getReceiveUserName());
			addressProductVo.setTelephone(tempOrder.getReceivePhoneNo());
			
			addressProductList.add(addressProductVo);
		}
		
		for(String sku: orderSkuLineMap.keySet()){
			PartnerOrderLineVo partnerOrderLine = orderSkuLineMap.get(sku); 
			orderLines.add(partnerOrderLine);
		}
		//新建partnerOrder
		PartnerOrderVo partnerOrder = new PartnerOrderVo();
		partnerOrder.setOrderType(OrderVo.PA_ORDER_TYPE);
		partnerOrder.setSource(PartnerOrderVo.PARTNER_ORDER_SOURCE_STPAOD);
		partnerOrder.setSourceId(PartnerOrderVo.PARTNER_ORDER_SOURCE_STPAOD + "1");
		
		
		//获取partner名称
		partnerOrder.setPartnerId(workshopPartnerVo.getPartnerId());
		if(workshopPartnerVo.getPartnerName() == null ){
			Map<String, Object> params = new HashMap<String, Object>();
	 		params.put("partnerId", workshopPartnerVo.getPartnerId());
	 		
	 		List<PartnerView> list = organizationVoMapper.selectPartnersByParams(params);
	 		if(list != null && !list.isEmpty()){
	 			partnerOrder.setPartnerName(list.get(0).getName());
	 		}
		}else{
			partnerOrder.setPartnerName(workshopPartnerVo.getPartnerName());
		}
		
		Map<String,Object> reqMap = new HashMap<String, Object>();
		reqMap.put("morderNoType", OrderVo.ORDER_CODE_SP_OWNER);
		
		String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.SP_SELF_ORDER,6,1);
		partnerOrder.setOrderNo(CommonUtil.generateOrderCode(6)+sequenceNo);
		partnerOrder.setTotalProductPrice(BigDecimal.valueOf(0));
		partnerOrder.setTotalOrderPrice(BigDecimal.valueOf(0));
		if(partnerOrder.getStatus() == null ){
			partnerOrder.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_2);//已确认
		}
		// 插入订单
		partnerOrder.setCreator(String.valueOf(ContextUtil.getCurUserId()));
		
		Date curDate = new Date();
		partnerOrder.setCreateTime(curDate);
		partnerOrder.setUpdateTime(curDate);
		partnerOrderVoMapper.insertSelective(partnerOrder);
		
		List<PartnerOrderLineVo> newOrderLineVoLst = new ArrayList<PartnerOrderLineVo>();// 要插入的
		Long indexNo = 0L;
		Double totalLiterCount = 0D;
		Map<String,String> skuCapacityMap = getProductSkuCapacityMap();
		for (PartnerOrderLineVo newOrderLineVo : orderLines) {  
			//统计订单机油合计升数
			String capacity = skuCapacityMap.get(newOrderLineVo.getSku());	
			if(!StringUtils.isEmpty(capacity)){
				totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getAmount());
			}
			
			newOrderLineVo.setPartnerOrderId(partnerOrder.getId());// 指定的订单id
			newOrderLineVo.setDiscountFee(null);
			newOrderLineVo.setTotalValue(null);
			newOrderLineVo.setDiscountedTotalValue(null);
			newOrderLineVo.setDiscountedPrice(null);
			newOrderLineVoLst.add(newOrderLineVo);
			
			indexNo = indexNo + 1;
		}
		partnerOrder.setTotalLiterCount(totalLiterCount);
		
		PartnerOrderVoExample orderExample = new PartnerOrderVoExample();
		PartnerOrderVoExample.Criteria orderCriteria = orderExample.createCriteria();
		orderCriteria.andIdEqualTo(partnerOrder.getId());
		partnerOrderVoMapper.updateByExampleSelective(partnerOrder, orderExample);
		
		// 5.批量插入orderline记录 //进行分页
		ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(newOrderLineVoLst, 40);
		int toltalPage = newOrderLinePage.getTotalPages();
		for (int i = 1; i <= toltalPage; i++) {

			@SuppressWarnings("unchecked")
			List<PartnerOrderLineVo> insertOrderLineLst = newOrderLinePage.getObjects(i);
			partnerOrderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);

		}
		
		List<WXTPartnerOrderAddressProductVo> newOrderAddressProductVoLst = new ArrayList<WXTPartnerOrderAddressProductVo>();// 要插入的
		
		for (WXTPartnerOrderAddressProductVo newOrderAddressProcutVo : addressProductList) {  // 原始的
			newOrderAddressProcutVo.setPartnerOrderId(partnerOrder.getId());// 指定的订单id
			newOrderAddressProcutVo.setDiscountFee(null);
			newOrderAddressProcutVo.setTotalValue(null);
			newOrderAddressProcutVo.setDiscountedTotalValue(null);
			newOrderAddressProcutVo.setDiscountedPrice(null);
			newOrderAddressProductVoLst.add(newOrderAddressProcutVo);
			
		}
		// 6.批量插入地址商品表记录
		ImportDataPageModelUtil newOrderAddressProductPage = new ImportDataPageModelUtil(newOrderAddressProductVoLst, 40);
		int addressProductToltalPage = newOrderAddressProductPage.getTotalPages();
		for (int i = 1; i <= addressProductToltalPage; i++) {

			@SuppressWarnings("unchecked")
			List<WXTPartnerOrderAddressProductVo> insertOrderAddressLst = newOrderAddressProductPage.getObjects(i);
			addressProductMapper.insertOrderAddressProductBatch(insertOrderAddressLst);

		}
		
		//7 更新门店订单状态
		orderIdMap.put("status", OrderVo.ORDER_STATUS_9);
		orderIdMap.put("remark", "合伙人订单单号：" + partnerOrder.getOrderNo());
		orderVoMapper.updateAllOrderStatusByOrderIds(orderIdMap);
		
		
		// 审核记录保存
		ApprovalInfoVo record = new ApprovalInfoVo();
		record.setCurrentStep(PartnerOrderVo.PARTNER_ORDER_STATUS_0);
		record.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_2);
		record.setIsApproved(1);
		record.setOperateTime(curDate);
		record.setOperator(ContextUtil.getCurUserId());
		record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
		record.setSourceId(partnerOrder.getId());
		approvalInfoVoMapper.insert(record);
		
		
		//邮件发送
		if(partnerOrder.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2)){
			// 邮件发送
			Map<String, Object> approverParam = new HashMap<String,Object>();
			approverParam.put("approverRoleName", Constants.CHEVRON_PRICE_AUDIT);
			approverParam.put("partnerId", partnerOrder.getPartnerId());
			approverParam.put("funFlag", "order_confirm");
			approverParam.put("salesChannelName", Constants.SALES_CHANNEL_CDM);
			List<PartnerResponsibleVo> approverList = resonsibleMapper.queryPartnerResponsible(approverParam);
			
			if(approverList != null && !approverList.isEmpty()){
				List<String> approverEmailList = new ArrayList<String>();
				List<String> approverNameList =  new ArrayList<String>();
				List<String> ccEmailList = new ArrayList<String>();
				List<String> approverIdList = new ArrayList<String>();
				for(PartnerResponsibleVo approver : approverList){
					if( !StringUtils.isNull(approver.getResponsiblePersonEmail()) &&  !StringUtils.isNull(approver.getResponsiblePersonName())){
						approverEmailList.add(approver.getResponsiblePersonEmail());
						approverNameList.add(approver.getResponsiblePersonName());							
					}
					if( !StringUtils.isNull(approver.getDayReportCc())){
						String[] ccEmailArray = approver.getDayReportCc().split(",");
						ccEmailList.addAll(Arrays.asList(ccEmailArray));
					}
					approverIdList.add(approver.getResponsiblePersonId().toString());
				}
				String[] accepters = new String[approverEmailList.size()];
				accepters = approverEmailList.toArray(accepters);
				String[] accepterCCs = new String[ccEmailList.size()];
				accepterCCs = ccEmailList.toArray(accepterCCs);

				String saleName = "";
				for(String name: approverNameList){
					if(saleName.equals("")){
						saleName = name;
					}else{
						saleName = saleName + "&" + name;
					}
				}
				PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrder.getId().toString());	
				PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrderVo,true);
				String orderChinesStatus = getOrderChineseStatusByStatusCode(partnerOrder.getStatus());
				String subjectName = orderChinesStatus + ",合伙人[" + partnerOrder.getPartnerName() + "]采购订单:" + partnerOrder.getOrderNo();
				Map<String,Object> contentMap = new HashMap<String,Object>();
				String acceptPersonName = "尊敬的" + saleName;
				String partnerName = "合伙人" + partnerOrder.getPartnerName() + ",有订单需要确认,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
				contentMap.put("acceptPersonName", acceptPersonName);
				contentMap.put("partnerName", partnerName);
				contentMap.put("skuList", partnerOrderView.getOlEmailList());
				contentMap.put("partnerOrderVo", partnerOrderView);
				List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
				contentMap.put("approvalList", approvalList);
				
				String partnerProperty = partnerService.getPartnerProperty(partnerOrder.getPartnerId());
				if("KA".equals(partnerProperty)){
					File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
					
					if (null != RequestContextHolder.getRequestAttributes()) {
						HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
						String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
						contentMap.put("approvalPageLink", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "orderAuditApproval",
								"/business/partnerorder/partnerOrderManage.jsp", "/business/partnerorder/priceAuditInit.jsp?partnerOrderId=" + partnerOrder.getId(), false,String.valueOf(curDate.getTime())));
						contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "orderAuditApprovalDirectly",
								"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
					}
					
					emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
							accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
				}else{
					File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
					
					if (null != RequestContextHolder.getRequestAttributes()) {
						HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
						String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
						contentMap.put("approvalPageLink", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "orderAuditApproval",
								"/business/partnerorder/partnerOrderManage.jsp", "/business/partnerorder/priceAuditInit.jsp?partnerOrderId=" + partnerOrder.getId(), false,String.valueOf(curDate.getTime())));
						contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "orderAuditApprovalDirectly",
								"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
					}
					
					emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
							accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_info.ftl");
				}
			}
		}
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		
		return resultMap;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> cancelSTPartnerOrder(String partnerOrderId)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		try{
			//修改原sell through订单状态
			WXTPartnerOrderAddressProductVoExample addressProductExample = new WXTPartnerOrderAddressProductVoExample();
			addressProductExample.createCriteria().andPartnerOrderIdEqualTo(Long.valueOf(partnerOrderId));
			List<WXTPartnerOrderAddressProductVo> addressProductList = addressProductMapper.selectByExample(addressProductExample);
			Set<String> workshopOrderIdSet = new HashSet<String>();
			for(WXTPartnerOrderAddressProductVo tempAddressProduct : addressProductList){
				workshopOrderIdSet.add(tempAddressProduct.getAddressId().toString());
			}
			List<String> workshopOrderIdList = new ArrayList<String>();
			workshopOrderIdList.addAll(workshopOrderIdSet);
			Map<String, Object> orderIdMap = new HashMap<String, Object>();
			orderIdMap.put("orderIds", new ArrayList<String>(workshopOrderIdList));
			orderIdMap.put("status", OrderVo.ORDER_STATUS_1);
			orderIdMap.put("remark", "已撤销合并");
			orderVoMapper.updateAllOrderStatusByOrderIds(orderIdMap);
			
			PartnerOrderVo partnerOrderVo = new PartnerOrderVo();
			partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId);
			if( partnerOrderVo.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1) || partnerOrderVo.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_2)  ){
				PartnerOrderVoExample partnerOrderVoExample = new PartnerOrderVoExample();				
				partnerOrderVoExample.createCriteria().andIdEqualTo(Long.valueOf(partnerOrderId));
				int deleteParnerOrderResult = partnerOrderVoMapper.deleteByExample(partnerOrderVoExample);
				
				PartnerOrderLineVoExample partnerOrderLineVoExample = new PartnerOrderLineVoExample();
				partnerOrderLineVoExample.createCriteria().andPartnerOrderIdEqualTo(Long.valueOf(partnerOrderId));
				int deleteParnerOrderLineResult = partnerOrderLineVoMapper.deleteByExample(partnerOrderLineVoExample);
				
				int deleteAddressProductResult = addressProductMapper.deleteByExample(addressProductExample);
				
			}else{
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "该订单已进入流程，不能删除！");
			}
		}catch(Exception e){
			log.error(e.getMessage());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> calculateTotalLiterCount() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		try{
			
			/*Map<String,String> skuCapacityMap = getProductSkuCapacityMap();*/
			
			PartnerOrderVoExample orderExample = new PartnerOrderVoExample();
			orderExample.createCriteria().andSourceEqualTo(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD);
			List<PartnerOrderVo> orderList = partnerOrderVoMapper.selectByExample(orderExample);
			if(orderList != null && !orderList.isEmpty()){
				for(PartnerOrderVo partnerOrder : orderList){
					/*if(partnerOrder.getTotalLiterCount() == null ){*/
					PartnerOrderLineVoExample orderLineVoExample = new PartnerOrderLineVoExample();
					orderLineVoExample.createCriteria().andPartnerOrderIdEqualTo(partnerOrder.getId());
					List<PartnerOrderLineVo> orderLines = partnerOrderLineVoMapper.selectByExample(orderLineVoExample);
					Double totalLiterCount = 0D;
					for (PartnerOrderLineVo newOrderLineVo : orderLines) {  
						if(newOrderLineVo.getActualAmount() == null){
							newOrderLineVo.setActualAmount(newOrderLineVo.getAmount()) ;
						}
						Integer amount = newOrderLineVo.getActualAmount();
						Integer freeAmount = newOrderLineVo.getFreeAmount() == null ? 0 :  newOrderLineVo.getFreeAmount();
						ProductVo productVo = productMapper.selectBySku(newOrderLineVo.getSku());
						if(productVo != null){
							if(newOrderLineVo.getUnits() != null && newOrderLineVo.getUnits().equals("box")){
								if(productVo.getBoxCapacity() != null){
									totalLiterCount = totalLiterCount + Double.valueOf(productVo.getBoxCapacity()) * (amount + freeAmount);
								}
							}else{
								if(!StringUtils.isEmpty(productVo.getCapacity())){
									totalLiterCount = totalLiterCount + Double.valueOf(productVo.getCapacity()) * (amount + freeAmount);
								}
							}
						}
					}
					if(!totalLiterCount.equals(partnerOrder.getTotalLiterCount())){
						partnerOrder.setTotalLiterCount(totalLiterCount);
						partnerOrderVoMapper.updateByPrimaryKey(partnerOrder);
					}
					/*}*/
				}
				
			}
		}catch(Exception e){
			log.error(e.getMessage());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> synchronizePartnerOrderStatus() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		try{
			//1. 找到全部待发货的订单
			PartnerOrderVoExample partnerOrderVoExample = new PartnerOrderVoExample();
			partnerOrderVoExample.createCriteria().andStatusEqualTo(PartnerOrderVo.PARTNER_ORDER_STATUS_5);
			List<PartnerOrderVo> todoDeliveredPartnerOrderList = partnerOrderVoMapper.selectByExample(partnerOrderVoExample);
			for(PartnerOrderVo partnerOrderTemp : todoDeliveredPartnerOrderList){				
				List<OrderVo> wdgjOrderList = orderVoMapper.selectBySapOrderNo(partnerOrderTemp.getOrderNo());
				if(wdgjOrderList != null && !wdgjOrderList.isEmpty()){
					boolean isDelivered = true;
					for(OrderVo wdgjOrderVo : wdgjOrderList){
						//存在没发货的情况，直接结束这个订单的状态同步
						if(!wdgjOrderVo.getStatus().equals(OrderVo.ORDER_STATUS_5)  && !wdgjOrderVo.getStatus().equals(OrderVo.ORDER_STATUS_7)){
							isDelivered = false;
							break;
						}
					}
					//判断订单是否已经出库完
					Map<String,Object> queryOutStockMap = new HashMap<String,Object>();
					queryOutStockMap.put("status", Constants.STOCK_OUT_STATUS_DAICHUKU);
					queryOutStockMap.put("orderNo", partnerOrderTemp.getOrderNo());
					Long daichukuCount = outStockMapper.countOutStockByParamsWithPage(queryOutStockMap);
					if(daichukuCount == 0 && isDelivered == true){
						partnerOrderTemp.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_6);
						partnerOrderVoMapper.updateByPrimaryKey(partnerOrderTemp);
						// 审核记录保存	
						ApprovalInfoVo approvalInfo = new ApprovalInfoVo();
						approvalInfo.setCurrentStep(PartnerOrderVo.PARTNER_ORDER_STATUS_5);
						approvalInfo.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_6);
						approvalInfo.setIsApproved(1);
						approvalInfo.setOperateTime(new Date());
						/*approvalInfo.setOperator(ContextUtil.getCurUserId());*/
						approvalInfo.setApprovalComment("网店管家发货");
						approvalInfo.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
						approvalInfo.setSourceId(partnerOrderTemp.getId());
						approvalInfoVoMapper.insert(approvalInfo);
					}					
				}
			}
			
			//1. 找到全部待收货的订单
			PartnerOrderVoExample partnerOrderToReceivedExample = new PartnerOrderVoExample();
			partnerOrderToReceivedExample.createCriteria().andStatusEqualTo(PartnerOrderVo.PARTNER_ORDER_STATUS_6);
			List<PartnerOrderVo> todoReceivedPartnerOrderList = partnerOrderVoMapper.selectByExample(partnerOrderToReceivedExample);
			for(PartnerOrderVo partnerOrderTemp : todoReceivedPartnerOrderList){
				List<OrderVo> wdgjOrderList = orderVoMapper.selectBySapOrderNo(partnerOrderTemp.getOrderNo());
				if(wdgjOrderList != null && !wdgjOrderList.isEmpty()){
					boolean isReceived = true;
					for(OrderVo wdgjOrderVo : wdgjOrderList){
						//存在收货的情况，直接结束这个订单的状态同步
						if(!wdgjOrderVo.getStatus().equals(OrderVo.ORDER_STATUS_7)){
							isReceived = false;
							break;
						}
					}
					//判断订单是否已经出库完
					Map<String,Object> queryOutStockMap = new HashMap<String,Object>();
					queryOutStockMap.put("status", Constants.STOCK_OUT_STATUS_DAICHUKU);
					queryOutStockMap.put("orderNo", partnerOrderTemp.getOrderNo());
					Long dairukuCount = outStockMapper.countOutStockByParamsWithPage(queryOutStockMap);
					if(dairukuCount == 0 && isReceived == true){
						partnerOrderTemp.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_7);
						partnerOrderVoMapper.updateByPrimaryKey(partnerOrderTemp);
						// 审核记录保存	javascript:void(0);
						ApprovalInfoVo approvalInfo = new ApprovalInfoVo();
						approvalInfo.setCurrentStep(PartnerOrderVo.PARTNER_ORDER_STATUS_6);
						approvalInfo.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_7);
						approvalInfo.setIsApproved(1);
						approvalInfo.setOperateTime(new Date());
						/*approvalInfo.setOperator(ContextUtil.getCurUserId());*/
						approvalInfo.setApprovalComment("网店管家发货，门店收货");
						approvalInfo.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
						approvalInfo.setSourceId(partnerOrderTemp.getId());
						approvalInfoVoMapper.insert(approvalInfo);
					}
					
				}
			}
		}catch(Exception e){
			log.error(e.getMessage());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "错误信息："+e.getMessage());
		}
		return resultMap;
	}
	
	private  File[] generatePartnerOrderPDF(Long partnerOrderId,boolean showPrice,ServletContext servletContext) {
		try {
			
			Map<String, Object> orderDetailMap = new HashMap<String, Object>();
			
			PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId.toString());
						
			PartnerOrderPdfView partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrderVo, showPrice);
			
			orderDetailMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			orderDetailMap.put("orderLineVoLst", partnerOrderView.getOlEmailList());
			orderDetailMap.put("partnerOrderVo", partnerOrderView);
			//付款信息
			List<DicItemVo> bankConfig = dicItemVoMapper.selectByCode("PartnerOrder.pdf.salesOrgBankAccount." + partnerOrderView.getSalesOrg());
			if(bankConfig.isEmpty()) {
				throw new WxPltException("未找到Sales Org" + partnerOrderView.getSalesOrg() + "配置的银行账户信息");
			}
			Map<String, String> bankInfo = new HashMap<String, String>(bankConfig.size());
			for(DicItemVo vo : bankConfig) {
				bankInfo.put(vo.getDicItemCode(), vo.getDicItemName());
			}
			orderDetailMap.put("bankInfo", bankInfo);
			orderDetailMap.put("currentDate", DateUtil.getDateStr(DateUtil.getCurrentDate(), "yyyy年M月d日"));
			
			//取得送货地址(适配最开始的单地址版本)
			if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
				if(partnerOrderVo.getAddressId() != null){
					PartnerAddress address = new PartnerAddress();
					address = partnerAddressMapper.getAddressDetailById(partnerOrderVo.getAddressId());		
					orderDetailMap.put("address", address);
					List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(partnerOrderVo.getOrderNo());
					List<InStockVo> instockList = inStockMapper.queryInStockByOrderNo(partnerOrderVo.getOrderNo());
					if(outstockList != null && !outstockList.isEmpty()){
						partnerOrderVo.setStockOutNo(outstockList.get(0).getStockOutNo());
					}
					if(instockList != null && !instockList.isEmpty()){
						partnerOrderVo.setStockInNo(instockList.get(0).getStockInNo());
					}
				}else{
					orderDetailMap.put("address", null);
				}
			}
			List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderId.toString());
			
			Map<Long,Object> addressMap = new HashMap<Long,Object>();
			
			Set<Long> addressIdSet = new HashSet<Long>();
			for(WXTPartnerOrderAddressProductView temp : addressProductList){
				Map<String,Object> addressProductMap = new HashMap<String,Object>();
				if(!addressIdSet.contains(temp.getAddressId())){
					addressIdSet.add(temp.getAddressId());
					if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
						String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress();					
						String contactor = temp.getContactPerson()+ " " + temp.getMobile();
						addressProductMap.put("address", addressString);
						addressProductMap.put("contactor", contactor);
						addressMap.put(temp.getAddressId(), addressProductMap);
					}else{
						String addressString = temp.getWorkshopAddress();
						String contactor = temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone();
						addressProductMap.put("address", addressString);
						addressProductMap.put("contactor", contactor);
						addressMap.put(temp.getAddressId(), addressProductMap);
					}
				}
			}
			List<Map<String,Object>> addProductResult =  new ArrayList<Map<String,Object>>();
			for(Long addressid : addressIdSet){
				addProductResult.add((Map<String,Object>)addressMap.get(addressid));
			}
			orderDetailMap.put("addressList", addProductResult);
			
			

			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh_mm_ss");
			String orderTimeStr = df.format(partnerOrderVo.getCreateTime());
			String filePath = servletContext.getRealPath(MyPropertyConfigurer
					.getVal("PARTNER_ORDER_EMAIL_TMP_DATA"))+File.separator  + partnerOrderVo.getPartnerName() + "_" + partnerOrderVo.getOrderNo() +".pdf";
			File file = new File(
					filePath.trim());
			OutputStream baos = new FileOutputStream(file);
	
			String basePath = servletContext.getRealPath("/");//绝对路径 
			String ftlName="partner_order_pdf.ftl";
			String fileName = partnerOrderVo.getPartnerName() + " " + partnerOrderVo.getOrderNo();
	        Configuration cfg = new Configuration();  
            cfg.setLocale(Locale.CHINA);  
            cfg.setEncoding(Locale.CHINA, "UTF-8");  
            //设置编码  
            cfg.setDefaultEncoding("UTF-8");
            //设置模板路径  
            cfg.setDirectoryForTemplateLoading(new File(basePath + "/WEB-INF/templates/"));  
            
            //获取模板  
            Template template = cfg.getTemplate(ftlName);  
            template.setEncoding("UTF-8");    
            Writer writer = new StringWriter();  
            //数据填充模板  
            template.process(orderDetailMap, writer);  
            String str = writer.toString();  
            //pdf生成	            	          
            ITextRenderer iTextRenderer = new ITextRenderer();
       	           
            //设置字体  其他字体需要添加字体库  
            ITextFontResolver fontResolver = iTextRenderer.getFontResolver();  
            fontResolver.addFont(basePath + "/WEB-INF/simsun.ttc", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);  
           /* iTextRenderer.setDocument(builder.parse(new ByteArrayInputStream(str.getBytes())),null);*/
            iTextRenderer.setDocumentFromString(str);  
            iTextRenderer.layout(); 
       
            //生成PDF  
            iTextRenderer.createPDF(baos);  
            baos.close();    
	            		
			//附件集合
			File[] attfiles = new File[1];
			
			attfiles[0] = file;
			return attfiles;
			

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
		
	}
	
	private  File[] generateKapartnerOrderPDF(Long partnerOrderId,boolean showPrice,ServletContext servletContext) {
		try {
			
			Map<String, Object> orderDetailMap = new HashMap<String, Object>();
			
			PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId.toString());
						
			PartnerOrderPdfView partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrderVo, showPrice);
			
			orderDetailMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			orderDetailMap.put("orderLineVoLst", partnerOrderView.getOlEmailList());
			orderDetailMap.put("partnerOrderVo", partnerOrderView);
			
			//取得送货地址(适配最开始的单地址版本)
			if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
				if(partnerOrderVo.getAddressId() != null){
					PartnerAddress address = new PartnerAddress();
					address = partnerAddressMapper.getAddressDetailById(partnerOrderVo.getAddressId());		
					orderDetailMap.put("address", address);
					List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(partnerOrderVo.getOrderNo());
					List<InStockVo> instockList = inStockMapper.queryInStockByOrderNo(partnerOrderVo.getOrderNo());
					if(outstockList != null && !outstockList.isEmpty()){
						partnerOrderVo.setStockOutNo(outstockList.get(0).getStockOutNo());
					}
					if(instockList != null && !instockList.isEmpty()){
						partnerOrderVo.setStockInNo(instockList.get(0).getStockInNo());
					}
				}else{
					orderDetailMap.put("address", null);
				}
			}
			List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderId.toString());
			
			Map<Long,Object> addressMap = new HashMap<Long,Object>();
			
			Set<Long> addressIdSet = new HashSet<Long>();
			for(WXTPartnerOrderAddressProductView temp : addressProductList){
				Map<String,Object> addressProductMap = new HashMap<String,Object>();
				if(!addressIdSet.contains(temp.getAddressId())){
					addressIdSet.add(temp.getAddressId());
					if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
						String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress();					
						String contactor = temp.getContactPerson()+ " " + temp.getMobile();
						addressProductMap.put("address", addressString);
						addressProductMap.put("contactor", contactor);
						addressMap.put(temp.getAddressId(), addressProductMap);
					}else{
						String addressString = temp.getWorkshopAddress();
						String contactor = temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone();
						addressProductMap.put("address", addressString);
						addressProductMap.put("contactor", contactor);
						addressMap.put(temp.getAddressId(), addressProductMap);
					}
				}
			}
			List<Map<String,Object>> addProductResult =  new ArrayList<Map<String,Object>>();
			for(Long addressid : addressIdSet){
				addProductResult.add((Map<String,Object>)addressMap.get(addressid));
			}
			orderDetailMap.put("addressList", addProductResult);
			
			

			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh_mm_ss");
			String orderTimeStr = df.format(partnerOrderVo.getCreateTime());
			String filePath = servletContext.getRealPath(MyPropertyConfigurer
					.getVal("PARTNER_ORDER_EMAIL_TMP_DATA"))+File.separator  + partnerOrderVo.getPartnerName() + "_" + partnerOrderVo.getOrderNo() +".pdf";
			File file = new File(
					filePath.trim());
			OutputStream baos = new FileOutputStream(file);
	
			String basePath = servletContext.getRealPath("/");//绝对路径 
			String ftlName="ka_partner_order_pdf.ftl";
			String fileName = partnerOrderVo.getPartnerName() + " " + partnerOrderVo.getOrderNo();
	        Configuration cfg = new Configuration();  
            cfg.setLocale(Locale.CHINA);  
            cfg.setEncoding(Locale.CHINA, "UTF-8");  
            //设置编码  
            cfg.setDefaultEncoding("UTF-8");
            //设置模板路径  
            cfg.setDirectoryForTemplateLoading(new File(basePath + "/WEB-INF/templates/"));  
            
            //获取模板  
            Template template = cfg.getTemplate(ftlName);  
            template.setEncoding("UTF-8");    
            Writer writer = new StringWriter();  
            //数据填充模板  
            template.process(orderDetailMap, writer);  
            String str = writer.toString();  
            //pdf生成	            	          
            ITextRenderer iTextRenderer = new ITextRenderer();
       	           
            //设置字体  其他字体需要添加字体库  
            ITextFontResolver fontResolver = iTextRenderer.getFontResolver();  
            fontResolver.addFont(basePath + "/WEB-INF/simsun.ttc", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);  
           /* iTextRenderer.setDocument(builder.parse(new ByteArrayInputStream(str.getBytes())),null);*/
            iTextRenderer.setDocumentFromString(str);  
            iTextRenderer.layout(); 
       
            //生成PDF  
            iTextRenderer.createPDF(baos);  
            baos.close();    
	            		
			//附件集合
			File[] attfiles = new File[1];
			
			attfiles[0] = file;
			return attfiles;
			

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
		
	}
	
//	private List<PartnerOrderLineVo>  calculatePartnerOrderlLinePrice(String partnerOrderId) {
//		
//		PartnerOrderVo partnerOrderVo = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId);	
//		List<PartnerOrderLineVo> partnerOrderDetailLst = partnerOrderLineVoMapper.selectByPartnerId(partnerOrderId);
//
//		//订单审核通过之前每一步都需要去计算有多少可以抵扣
//		Set<String> statusSet = new HashSet<String>();
//		/*statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_0);
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_4);
//		statusSet.add(PartnerOrderVo.PARTNER_ORDER_STATUS_8);*/
//		if(statusSet.contains(partnerOrderVo.getStatus())){
//			for(PartnerOrderLineVo newOrderLineVo : partnerOrderDetailLst){
//				if(newOrderLineVo.getPackageSell() == null){
//					if(newOrderLineVo.getUnits().equals(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE)){
//						newOrderLineVo.setLadderPrice(newOrderLineVo.getPrice());
//						newOrderLineVo.setDiscountedPrice(newOrderLineVo.getPrice());
//						
//						BigDecimal productPriceAcount = newOrderLineVo.getDiscountedPrice().multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//						newOrderLineVo.setDiscountedTotalValue(productPriceAcount);
//						newOrderLineVo.setTotalValue(productPriceAcount);
//					}else{
//						Map<String,Object> productPriceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerOrderVo.getPartnerId().toString(), newOrderLineVo.getSku(), 
//								Long.valueOf(newOrderLineVo.getActualAmount()), partnerOrderVo.getCreateTime(),false);
//						
//						ProductVo product = productMapper.selectBySku(newOrderLineVo.getSku());
//						if(newOrderLineVo.getPrice() == null){
//							if(product.getSalePrice() != null){
//								newOrderLineVo.setPrice(BigDecimal.valueOf(product.getSalePrice()));
//							}
//						}
//						if(newOrderLineVo.getDiscountedPrice() == null || newOrderLineVo.getLadderPrice() == null){
//							/*Map<String, Object> productPriceMap = productPriceService.getProductDiscountPriceByPartnerIdAndSku(partnerOrderVo.getPartnerId().toString(), newOrderLineVo.getSku(), Long.valueOf(newOrderLineVo.getAmount()));
//							*/
//							
//							if(productPriceMap.get("ladderPrice") != null){
//								BigDecimal ladderPrice = (BigDecimal)productPriceMap.get("ladderPrice");
//								newOrderLineVo.setLadderPrice(ladderPrice);
//								/*BigDecimal discountedPrice = (BigDecimal)productPriceMap.get("discountedPrice");
//								newOrderLineVo.setDiscountedPrice(discountedPrice);*/
//								newOrderLineVo.setDiscountedPrice(ladderPrice);
//							}
//							
//							if(newOrderLineVo.getDiscountedPrice() != null){
//								BigDecimal productPriceAcount = newOrderLineVo.getDiscountedPrice().multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//								newOrderLineVo.setDiscountedTotalValue(productPriceAcount);
//								newOrderLineVo.setTotalValue(productPriceAcount);
//							}else if(newOrderLineVo.getLadderPrice() != null){
//								BigDecimal productPriceAcount = newOrderLineVo.getLadderPrice().multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//								newOrderLineVo.setDiscountedTotalValue(productPriceAcount);
//								newOrderLineVo.setTotalValue(productPriceAcount);
//							}else if(newOrderLineVo.getPrice() != null){
//								BigDecimal productPriceAcount = newOrderLineVo.getPrice().multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//								newOrderLineVo.setDiscountedTotalValue(productPriceAcount);
//								newOrderLineVo.setTotalValue(productPriceAcount);
//							}else{
//								if(product.getSalePrice() != null){
//									BigDecimal productPriceAcount = BigDecimal.valueOf(product.getSalePrice()).multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//									newOrderLineVo.setPrice(BigDecimal.valueOf(product.getSalePrice()));
//									newOrderLineVo.setDiscountedTotalValue(productPriceAcount);
//									newOrderLineVo.setTotalValue(productPriceAcount);
//								}else{
//									newOrderLineVo.setPrice(BigDecimal.valueOf(0));
//									newOrderLineVo.setDiscountedTotalValue(BigDecimal.valueOf(0));
//									newOrderLineVo.setTotalValue(BigDecimal.valueOf(0));
//								}
//							}
//						}
//						
//					}					
//				}else if(newOrderLineVo.getPackageSell() != null && newOrderLineVo.getPackageSell().equals("yes")){
//					newOrderLineVo.setLadderPrice(BigDecimal.valueOf(0));
//					newOrderLineVo.setDiscountedPrice(BigDecimal.valueOf(0));
//					newOrderLineVo.setDiscountedTotalValue(BigDecimal.valueOf(0));
//					newOrderLineVo.setTotalValue(BigDecimal.valueOf(0));
//				}
//				
//			}
//			BigDecimal discountedTotalValue = BigDecimal.valueOf(0);
//			for(PartnerOrderLineVo newOrderLineVo : partnerOrderDetailLst){
//				if(newOrderLineVo.getPackageSell() == null){
//					if(newOrderLineVo.getActualAmount() != null && newOrderLineVo.getActualAmount() > 0){	
//						String deductibleConditon = null;
//						if(newOrderLineVo.getUnits().equals(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE)){
//							ProductPackage productPackage = productPackageService.getProductPackageById(Long.valueOf(newOrderLineVo.getPromotionSku()));
//							if(productPackage != null){
//								deductibleConditon = productPackage.getDeductible();
//							}							
//							BigDecimal discountedPrice = newOrderLineVo.getDiscountedPrice();
//							BigDecimal discountProductPriceTotalAmount = discountedPrice.multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//							newOrderLineVo.setDiscountedTotalValue(discountProductPriceTotalAmount);
//							if(PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE.equals(deductibleConditon) || deductibleConditon == null){
//								newOrderLineVo.setDeductible(PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE);
//								discountedTotalValue = discountedTotalValue.add(newOrderLineVo.getDiscountedTotalValue()); 
//							}
//						}else{
//							Map<String,Object> productPriceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerOrderVo.getPartnerId().toString(), newOrderLineVo.getSku(), 
//									Long.valueOf(newOrderLineVo.getActualAmount()), partnerOrderVo.getCreateTime(),false);
//							
//							deductibleConditon = (String)productPriceMap.get("deductible");
//							if(newOrderLineVo.getDiscountedPrice() != null){
//								BigDecimal discountedPrice = newOrderLineVo.getDiscountedPrice();
//								BigDecimal discountProductPriceTotalAmount = discountedPrice.multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//								newOrderLineVo.setDiscountedTotalValue(discountProductPriceTotalAmount);
//							}else if(newOrderLineVo.getLadderPrice() == null && newOrderLineVo.getPrice() != null){
//								BigDecimal discountedPrice = newOrderLineVo.getPrice();
//								newOrderLineVo.setDiscountedPrice(discountedPrice);
//								BigDecimal discountProductPriceTotalAmount = discountedPrice.multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//								newOrderLineVo.setDiscountedTotalValue(discountProductPriceTotalAmount);					
//							}else if(newOrderLineVo.getLadderPrice() != null){
//								BigDecimal discountedPrice = newOrderLineVo.getLadderPrice();
//								newOrderLineVo.setDiscountedPrice(discountedPrice);
//								BigDecimal discountProductPriceTotalAmount = discountedPrice.multiply(new BigDecimal(newOrderLineVo.getActualAmount()));
//								newOrderLineVo.setDiscountedTotalValue(discountProductPriceTotalAmount);						
//							}else{
//								newOrderLineVo.setDiscountedPrice(newOrderLineVo.getPrice());
//							}
//							String productProperty = productMapper.getProductPropertyBySku(newOrderLineVo.getSku());
//							if(Constants.PRODUCT_PROPERTY_SN_ABOVE.equals(productProperty)){
//								if(PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE.equals(deductibleConditon) || deductibleConditon == null){
//									newOrderLineVo.setDeductible(PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE);
//									discountedTotalValue = discountedTotalValue.add(newOrderLineVo.getDiscountedTotalValue()); 
//								}
//							}
//						}					
//					}
//				}else if(newOrderLineVo.getPackageSell() != null && newOrderLineVo.getPackageSell().equals("yes")){
//					newOrderLineVo.setLadderPrice(BigDecimal.valueOf(0));
//					newOrderLineVo.setDiscountedPrice(BigDecimal.valueOf(0));
//					newOrderLineVo.setDiscountedTotalValue(BigDecimal.valueOf(0));
//					newOrderLineVo.setTotalValue(BigDecimal.valueOf(0));
//				}
//			}		
//			/*PartnerBill partnerBill = partnerBillService.getPartnerBillInfoByPartnerId(partnerOrderVo.getPartnerId());
//			Map<String,Object> deductionAmountAvailMap = partnerDeductionService.getPartnerDeductionAmountAvail(partnerOrderVo.getPartnerId());
//			Double deductionAmountAvail = (Double)deductionAmountAvailMap.get("deductionAmountAvail");
//			BigDecimal maxOrderDeductibleAmount = discountedTotalValue.multiply(BigDecimal.valueOf(partnerBill.getDeductionRate()));
//			if(maxOrderDeductibleAmount.compareTo(BigDecimal.valueOf( deductionAmountAvail)) > 0){
//				maxOrderDeductibleAmount = BigDecimal.valueOf( deductionAmountAvail);
//			}
//			BigDecimal remainDeductibleAmount = new BigDecimal(maxOrderDeductibleAmount.doubleValue());
//			
//			int deductionProductCount = 0;
//			for(int i = 0 ; i < partnerOrderDetailLst.size();i++){	
//				PartnerOrderLineVo newOrderLineVo = partnerOrderDetailLst.get(i);
//				if(newOrderLineVo.getPackageSell() == null){
//					String deductibleConditon = null;
//					if(newOrderLineVo.getUnits().equals(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE)){
//						ProductPackage productPackage = productPackageService.getProductPackageById(Long.valueOf(newOrderLineVo.getPromotionSku()));
//						if(productPackage != null){
//							deductibleConditon = productPackage.getDeductible();
//						}
//						if(deductibleConditon == null || PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE.equals(deductibleConditon)){
//							deductionProductCount = deductionProductCount + 1;
//						}
//					}else{
//						Map<String,Object> productPriceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerOrderVo.getPartnerId().toString(), newOrderLineVo.getSku(), 
//								Long.valueOf(newOrderLineVo.getActualAmount()), partnerOrderVo.getCreateTime(),false);
//						
//						deductibleConditon = (String)productPriceMap.get("deductible");
//						String productProperty = productMapper.getProductPropertyBySku(newOrderLineVo.getSku());
//						if(Constants.PRODUCT_PROPERTY_SN_ABOVE.equals(productProperty)){
//							if(deductibleConditon == null || PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE.equals(deductibleConditon)){
//								deductionProductCount = deductionProductCount + 1;
//							}
//						}
//					}
//				}
//			}*/
//			
//			/*for(int i = 0 ; i < partnerOrderDetailLst.size();i++){	
//				PartnerOrderLineVo newOrderLineVo = partnerOrderDetailLst.get(i);
//				if(newOrderLineVo.getPackageSell() == null){
//					String deductibleConditon = null;
//					if(newOrderLineVo.getUnits().equals(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE)){
//						ProductPackage productPackage = productPackageService.getProductPackageById(Long.valueOf(newOrderLineVo.getPromotionSku()));
//						if(productPackage != null){
//							deductibleConditon = productPackage.getDeductible();
//						}
//						if(deductibleConditon == null || PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE.equals(deductibleConditon)){
//							if(discountedTotalValue.doubleValue() > 0){
//								deductionProductCount = deductionProductCount - 1;
//								if(deductionProductCount > 0){
//									BigDecimal orderLineLiterDeductibleAmount = newOrderLineVo.getDiscountedTotalValue().
//											multiply(maxOrderDeductibleAmount.divide(discountedTotalValue,10,BigDecimal.ROUND_HALF_UP));
//									orderLineLiterDeductibleAmount = orderLineLiterDeductibleAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
//									newOrderLineVo.setDeductibleAmount(orderLineLiterDeductibleAmount); 
//									BigDecimal totalValueAfterDeductible = newOrderLineVo.getDiscountedTotalValue().subtract(orderLineLiterDeductibleAmount);
//									newOrderLineVo.setTotalValueAfterDeductible(totalValueAfterDeductible.setScale(2,BigDecimal.ROUND_HALF_UP));
//									remainDeductibleAmount = remainDeductibleAmount.subtract(orderLineLiterDeductibleAmount);
//								}else{
//									BigDecimal orderLineLiterDeductibleAmount = remainDeductibleAmount;
//									newOrderLineVo.setDeductibleAmount(orderLineLiterDeductibleAmount.setScale(2,BigDecimal.ROUND_HALF_UP)); 
//									BigDecimal totalValueAfterDeductible = newOrderLineVo.getDiscountedTotalValue().subtract(orderLineLiterDeductibleAmount);
//									newOrderLineVo.setTotalValueAfterDeductible(totalValueAfterDeductible.setScale(2,BigDecimal.ROUND_HALF_UP));						
//								}
//							}else{
//								newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//								newOrderLineVo.setTotalValueAfterDeductible(newOrderLineVo.getDiscountedTotalValue());
//							}
//						}else{
//							newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//							newOrderLineVo.setTotalValueAfterDeductible(newOrderLineVo.getDiscountedTotalValue());
//						}
//					}else{
//						Map<String,Object> productPriceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerOrderVo.getPartnerId().toString(), newOrderLineVo.getSku(), 
//								Long.valueOf(newOrderLineVo.getActualAmount()), partnerOrderVo.getCreateTime(),false);
//						
//						deductibleConditon = (String)productPriceMap.get("deductible");
//						String productProperty = productMapper.getProductPropertyBySku(newOrderLineVo.getSku());
//						if(Constants.PRODUCT_PROPERTY_SN_ABOVE.equals(productProperty)){
//							if(deductibleConditon == null || PromotionalCampaign.PROMOTIONAL_DEDUCITIBLE_ENABLE.equals(deductibleConditon)){
//								if(discountedTotalValue.doubleValue() > 0){
//									deductionProductCount = deductionProductCount - 1;
//									if(deductionProductCount > 0){
//										BigDecimal orderLineLiterDeductibleAmount = newOrderLineVo.getDiscountedTotalValue().
//												multiply(maxOrderDeductibleAmount.divide(discountedTotalValue,10,BigDecimal.ROUND_HALF_UP));
//										orderLineLiterDeductibleAmount = orderLineLiterDeductibleAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
//										newOrderLineVo.setDeductibleAmount(orderLineLiterDeductibleAmount); 
//										BigDecimal totalValueAfterDeductible = newOrderLineVo.getDiscountedTotalValue().subtract(orderLineLiterDeductibleAmount);
//										newOrderLineVo.setTotalValueAfterDeductible(totalValueAfterDeductible.setScale(2,BigDecimal.ROUND_HALF_UP));
//										remainDeductibleAmount = remainDeductibleAmount.subtract(orderLineLiterDeductibleAmount);
//									}else{
//										BigDecimal orderLineLiterDeductibleAmount = remainDeductibleAmount;
//										newOrderLineVo.setDeductibleAmount(orderLineLiterDeductibleAmount.setScale(2,BigDecimal.ROUND_HALF_UP)); 
//										BigDecimal totalValueAfterDeductible = newOrderLineVo.getDiscountedTotalValue().subtract(orderLineLiterDeductibleAmount);
//										newOrderLineVo.setTotalValueAfterDeductible(totalValueAfterDeductible.setScale(2,BigDecimal.ROUND_HALF_UP));						
//									}
//								}else{
//									newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//									newOrderLineVo.setTotalValueAfterDeductible(newOrderLineVo.getDiscountedTotalValue());
//								}
//							}else{
//								newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//								newOrderLineVo.setTotalValueAfterDeductible(newOrderLineVo.getDiscountedTotalValue());
//							}
//						}else{
//							newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//							newOrderLineVo.setTotalValueAfterDeductible(newOrderLineVo.getDiscountedTotalValue());
//						}
//					}
//				}else if(newOrderLineVo.getPackageSell() != null && newOrderLineVo.getPackageSell().equals("yes")){
//					newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//					newOrderLineVo.setTotalValueAfterDeductible(BigDecimal.valueOf(0));
//				}
//			}*/
//		}
//		for(PartnerOrderLineVo newOrderLineVo : partnerOrderDetailLst){
//			if(!newOrderLineVo.getUnits().equals(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE)){
//				Double orderLineLiterCount = 0D;
//				String capacity = newOrderLineVo.getCapacity();
//				String boxCapacity = newOrderLineVo.getBoxCapacity();
//				Integer mount = newOrderLineVo.getAmount() == null ? 0 : newOrderLineVo.getAmount();
//				Integer freeAmount = newOrderLineVo.getFreeAmount() == null ? 0 : newOrderLineVo.getFreeAmount();
//				Integer totalMmount = mount + freeAmount;
//				if(!StringUtils.isEmpty(capacity) && (newOrderLineVo.getUnits() == null || newOrderLineVo.getUnits().equals("logistic")
//						|| newOrderLineVo.getUnits().equals("barrel"))){
//					orderLineLiterCount = orderLineLiterCount + Double.valueOf(capacity) * Integer.valueOf(totalMmount);
//				}else if(!StringUtils.isEmpty(boxCapacity) && (newOrderLineVo.getUnits() != null && newOrderLineVo.getUnits().equals("box"))){
//					orderLineLiterCount = orderLineLiterCount + Double.valueOf(boxCapacity) * Integer.valueOf(totalMmount);
//				}	
//				DecimalFormat df = new DecimalFormat("#.00");
//				orderLineLiterCount = Double.valueOf(df.format(orderLineLiterCount));
//				newOrderLineVo.setTotalLiterCount(orderLineLiterCount);
//			}else{
//				newOrderLineVo.setTotalLiterCount(0D);
//			}
//			if(newOrderLineVo.getPackageSell() != null && newOrderLineVo.getPackageSell().equals("yes")){
//				newOrderLineVo.setDiscountedPrice(BigDecimal.valueOf(0));
//				newOrderLineVo.setDiscountedTotalValue(BigDecimal.valueOf(0));
//				newOrderLineVo.setTotalValue(BigDecimal.valueOf(0));
//				newOrderLineVo.setDeductibleAmount(BigDecimal.valueOf(0));
//				newOrderLineVo.setTotalValueAfterDeductible(BigDecimal.valueOf(0));
//			}
//		}
//		return partnerOrderDetailLst;
//	}
	
//	private List<PartnerOrderLineVo>  generatePartnerOrderlLinePackage(List<PartnerOrderLineVo> orderLineList) {
//		List<PartnerOrderLineVo> orderLinePackageList = new ArrayList<PartnerOrderLineVo>();
//		
//		//区别数打包的与不打包销售的
//		List<PartnerOrderLineVo> orderLinePackageSellList = new ArrayList<PartnerOrderLineVo>();
//		List<PartnerOrderLineVo> orderLineNotPackageSellList = new ArrayList<PartnerOrderLineVo>();
//		for(PartnerOrderLineVo orderLine :orderLineList){
//			if(orderLine.getPackageSell() != null && orderLine.getPackageSell().equals("yes")){
//				orderLinePackageSellList.add(orderLine);
//			}else{
//				orderLineNotPackageSellList.add(orderLine);
//			}
//		}
//		
//		for(PartnerOrderLineVo orderLine :orderLineNotPackageSellList){
//			if(orderLine.getAmount() != null && orderLine.getActualAmount() > 0){
//				if(orderLine.getUnits().equals(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE)){
//					List<PartnerOrderLineVo> ppdList = new ArrayList<PartnerOrderLineVo>();				
//					for(PartnerOrderLineVo orderLinePackageSell :orderLinePackageSellList){
//						if(orderLinePackageSell.getPromotionSku().equals(orderLine.getSku())){
//							ppdList.add(orderLinePackageSell);
//						}
//					}
//					orderLine.setChildren(ppdList);
//				}
//				orderLinePackageList.add(orderLine);
//			}
//		}
//		for(PartnerOrderLineVo orderLine :orderLineNotPackageSellList){
//			if((orderLine.getAmount() == null || orderLine.getAmount() == 0) && orderLine.getFreeAmount() > 0){
//				if(orderLine.getUnits().equals("box") || orderLine.getUnits().equals("barrel")){
//					for(PartnerOrderLineVo orderLinePackage: orderLinePackageList){
//						if(orderLinePackage.getSku().equals(orderLine.getPromotionSku())){
//							List<PartnerOrderLineVo> ppdList = new ArrayList<PartnerOrderLineVo>();				
//							ProductVo product = productMapper.selectBySku(orderLine.getSku());
//							PartnerOrderLineVo ppdOl =  new PartnerOrderLineVo();
//							ppdOl.setSku(product.getSku());
//							ppdOl.setProductName(product.getName());
//							ppdOl.setProductId(product.getId());
//							ppdOl.setAmount(0);
//							ppdOl.setActualAmount(0);
//							ppdOl.setFreeAmount(orderLine.getFreeAmount());
//							Double totalLiterCount = orderLine.getFreeAmount() * product.getBoxCapacity();
//							ppdOl.setTotalLiterCount(totalLiterCount);
//							ppdOl.setUnits(orderLine.getUnits());
//							ppdOl.setPromotionTitle(orderLine.getPromotionTitle());
//							ppdOl.setTotalValue(BigDecimal.valueOf(0));
//							ppdOl.setDeductibleAmount(BigDecimal.valueOf(0));
//							ppdOl.setDiscountedTotalValue(BigDecimal.valueOf(0));
//							ppdOl.setTotalValueAfterDeductible(BigDecimal.valueOf(0));
//							ppdList.add(ppdOl);
//							orderLinePackage.setChildren(ppdList);
//						}
//					}
//				}
//			}
//		}		
//		return orderLinePackageList;
//	}
		
	private PartnerOrderPdfView  generatePartnerOrderViewForPdfExcel(PartnerOrderVo partnerOrderVo ,boolean showPrice) {
		
		PartnerOrderPdfView partnerOrderPdfView = new PartnerOrderPdfView();
		
		String baseUrl = (String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+"/";//"http://localhost:80/";//
		partnerOrderPdfView.setBaseUrl(baseUrl);
		
		partnerOrderPdfView.setPartnerOrderId(partnerOrderVo.getId().toString());
		partnerOrderPdfView.setPartnerName(partnerOrderVo.getPartnerName());
		partnerOrderPdfView.setPartnerOrderNo(partnerOrderVo.getOrderNo());
		partnerOrderPdfView.setPartnerOrderVo(partnerOrderVo);
		
		
		Map<String, Object> reqMap = new HashMap<String, Object>(2);
		reqMap.put("id", partnerOrderVo.getPartnerSaleConfigId());
		List<WxTPartnerSaleConfig> partnerSaleConfigList = partnerSaleConfigMapper.getPartnerSaleConfigByMap(reqMap);
		//优先使用partner master表里的shiptocode
		if(partnerSaleConfigList != null && !partnerSaleConfigList.isEmpty()){
			for(WxTPartnerSaleConfig psc : partnerSaleConfigList){
				if(psc.getQuotaType() == null ||psc.getQuotaType().isEmpty()){
					partnerOrderPdfView.setSalesOrg(psc.getSalesOrg());
					if("ship_to_code".equals(psc.getPriceLevel())
							&& !StringUtils.isEmpty(psc.getShipToCode())){						
						partnerOrderPdfView.setSalesCode(psc.getShipToCode());
					}else{
						partnerOrderPdfView.setSalesCode(psc.getSapCode());
					}
				}
			}			
		}else{
			partnerOrderPdfView.setSalesOrg(partnerOrderVo.getSalesOrg());
			partnerOrderPdfView.setSalesCode(partnerOrderVo.getSalesCode());
		}
		if(partnerOrderVo.getPartnerSaleConfigId() != null){
			WxTPartnerSaleConfigExample partnerSaleConfigExample = new WxTPartnerSaleConfigExample();
			partnerSaleConfigExample.createCriteria().andIdEqualTo(partnerOrderVo.getPartnerSaleConfigId());
			WxTPartnerSaleConfig partnerSaleConfig = partnerSaleConfigMapper.selectByExample(partnerSaleConfigExample).get(0);

			partnerOrderPdfView.setSalesOrg(partnerSaleConfig.getSalesOrg());
			if("ship_to_code".equals(partnerSaleConfig.getPriceLevel())){
				partnerOrderPdfView.setSalesCode(partnerSaleConfig.getShipToCode());
			}else{
				partnerOrderPdfView.setSalesCode(partnerSaleConfig.getSapCode());
			}
			partnerOrderPdfView.setPurpose(partnerSaleConfig.getPurpose());
			
			partnerOrderPdfView.setIsQuotaRatio(partnerSaleConfig.getIsQuotaRatio());
			partnerOrderPdfView.setQuotaRatioSalesOrg(partnerSaleConfig.getQuotaRatioSalesOrg());
			partnerOrderPdfView.setQuotaRatioSalesCode(partnerSaleConfig.getQuotaRatioSalesCode());
		}else{
			partnerOrderPdfView.setPurpose("");
		}
		/*partnerOrderPdfView.setSalesOrg(partnerOrderVo.getSalesOrg());
		partnerOrderPdfView.setSalesCode(partnerOrderVo.getSalesCode());*/
		partnerOrderPdfView.setSource(partnerOrderVo.getSource());
		partnerOrderPdfView.setCreateTime(partnerOrderVo.getCreateTime());
		
		List<PartnerOrderLineVo>  partnerOrderDetailLst =  partnerOrderLineVoMapper.selectByOrderId(partnerOrderVo.getId().toString());
		
		BigDecimal deductibleAmount = new BigDecimal(0);
		BigDecimal totalValueAfterDeductible = new BigDecimal(0);
		BigDecimal discountedTotalOrderPrice = new BigDecimal(0);
		Double totalLiterCount = 0D;
		List<PartnerOrderLineVoEmail> olEmailList = new ArrayList<PartnerOrderLineVoEmail>();
		for (PartnerOrderLineVo newOrderLineVo : partnerOrderDetailLst) {  // 原始的		
			PartnerOrderLineVoEmail olEmail = new PartnerOrderLineVoEmail();
			olEmail.setSku(newOrderLineVo.getSku());
			olEmail.setSkuDesc(newOrderLineVo.getProductName());
			
			//统计转换成箱桶数
			olEmail.setUnits(newOrderLineVo.getUnitsText());
			olEmail.setAmount(newOrderLineVo.getActualAmount().toString());
			if(newOrderLineVo.getFreeAmount() != null){
				olEmail.setFreeAmount(newOrderLineVo.getFreeAmount().toString());
			}
			if(showPrice){
				if(newOrderLineVo.getPrice() != null){
					olEmail.setPrice(newOrderLineVo.getPrice().toString());
				}
				if(newOrderLineVo.getDiscountedPrice() != null){
					olEmail.setDiscountedPrice(newOrderLineVo.getDiscountedPrice().toString());
				}
				if(newOrderLineVo.getDiscountedTotalValue() != null){
					discountedTotalOrderPrice = discountedTotalOrderPrice.add(newOrderLineVo.getDiscountedTotalValue());
					olEmail.setTotalValue(newOrderLineVo.getDiscountedTotalValue().toString());
				}
				if(newOrderLineVo.getDeductibleAmount() != null){
					deductibleAmount = deductibleAmount.add(newOrderLineVo.getDeductibleAmount());
					olEmail.setDeductibleAmount(newOrderLineVo.getDeductibleAmount().toString());
				}
				if(newOrderLineVo.getTotalValueAfterDeductible() != null){
					totalValueAfterDeductible = totalValueAfterDeductible.add(newOrderLineVo.getTotalValueAfterDeductible());
					olEmail.setTotalValueAfterDeductible(newOrderLineVo.getTotalValueAfterDeductible().toString());
				}
				
			}
			if(newOrderLineVo.getTotalLiterCount() != null){ 
				olEmail.setTotalLiterCount(newOrderLineVo.getTotalLiterCount().toString());
				totalLiterCount = totalLiterCount + newOrderLineVo.getTotalLiterCount();
			}else{
				if(!StringUtils.isNull(newOrderLineVo.getCapacity()) && StringUtils.isNotEmpty(newOrderLineVo.getCapacity())){
					Integer totalLiterCountOL = Integer.valueOf(newOrderLineVo.getCapacity()) * newOrderLineVo.getActualAmount();
					olEmail.setTotalLiterCount(totalLiterCountOL.toString());
					totalLiterCount = totalLiterCount + totalLiterCountOL;
				}
			}
			olEmail.setPromotionSku(newOrderLineVo.getPromotionSku());
			olEmail.setPromotionTitle(newOrderLineVo.getPromotionTitle());
			olEmail.setRemark(newOrderLineVo.getRemark());
			olEmailList.add(olEmail);
		}
//		//重新排序，赠品SKU请紧跟购买的SKU
//		List<PartnerOrderLineVoEmail> olEmailListSorted = new ArrayList<PartnerOrderLineVoEmail>();
		
		partnerOrderPdfView.setOlEmailList(olEmailList);
		if(showPrice){
			partnerOrderPdfView.setDeductibleAmount(deductibleAmount.toString());
			partnerOrderPdfView.setDiscountedTotalOrderPrice(discountedTotalOrderPrice.toString());
			partnerOrderPdfView.setTotalValueAfterDeductible(totalValueAfterDeductible.toString());
			
		}
		DecimalFormat df = new DecimalFormat("#.00");
		totalLiterCount = Double.valueOf(df.format(totalLiterCount));
		partnerOrderPdfView.setTotalLiterCount(totalLiterCount.toString());
		
		Map<Long,Map<String,Object>> addressMap = buildAddressMap(partnerOrderVo);
		String addresscontact = "";
		if(!addressMap.isEmpty()){
			for(Map<String,Object> addressProductMap : addressMap.values()){
				addresscontact =  addresscontact + (String)addressProductMap.get("address") + (String)addressProductMap.get("contact") + ";";
			}
		}
		if(addresscontact.length() > 1){
			addresscontact = addresscontact.substring(0,addresscontact.length()-1);
		}
		partnerOrderPdfView.setAddressContact(addresscontact);
		
//		PartnerView partner = organizationVoMapper.selectPartnerFullInfoByPartnerId(Long.valueOf(partnerOrderVo.getPartnerId()));
		
		// 取得审批历史信息
		List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderVoMapper.getApprovalHistoryByOrderId(partnerOrderVo.getId());
//		for(PartnerOrderApprovalHistoryVo tempArrovalHistory : approvalList){
//			String stepName = chanslatePartnerOrderApprovalComment(tempArrovalHistory.getApprovalCurrentStep(),tempArrovalHistory.getApprovalFlowStep());
//			tempArrovalHistory.setApprovalStep(stepName);
//		}
		partnerOrderPdfView.setApprovalList(approvalList);
		
		return partnerOrderPdfView;
	}
	
	private Map<Long,Map<String,Object>> buildAddressMap(PartnerOrderVo partnerOrderVo) {
		Map<Long,Map<String,Object>> addressMap = new HashMap<Long,Map<String,Object>>(5);
		
		if(partnerOrderVo.getVersionNo() < PartnerOrderVo.VERSION_NO202107) {
			//获取订单地址，如果是单一地址，则显示出来
			List<WXTPartnerOrderAddressProductView> addressProductList = addressProductMapper.selectByOrderId(partnerOrderVo.getId().toString());
//			Set<Long> addressIdSet = new HashSet<Long>();
			for(WXTPartnerOrderAddressProductView temp : addressProductList){
				Map<String,Object> addressProductMap = new HashMap<String,Object>();
				if(!addressMap.containsKey(temp.getAddressId())){
//					addressIdSet.add(temp.getAddressId());
					addressProductMap.put("addressObj", temp);
					if(partnerOrderVo.getSource().equals(PartnerOrderVo.PARTNER_ORDER_SOURCE_PAOD)){
						String addressString = temp.getProvinceName() + " "+ temp.getCityName() + " " + temp.getDistName() + " " + temp.getAddress();					
						addressProductMap.put("address", addressString);
						addressProductMap.put("contact", temp.getContactPerson()+ " " + temp.getMobile());
					}else{
						String addressString = temp.getWorkshopAddress();
						addressProductMap.put("address", addressString);
						addressProductMap.put("contact", temp.getWorkshopContactPerson() + " " + temp.getWorkshopTelephone());
					}
					addressProductMap.put("addressId", temp.getAddressId());
					addressMap.put(temp.getAddressId(), addressProductMap);
				}
			}	
		}else {
			Map<String,Object> addressProductMap = new HashMap<String,Object>(5);
			StringBuilder addressString = new StringBuilder();
			if(StringUtils.isNotBlank(partnerOrderVo.getProvinceName())) {
				addressString.append(partnerOrderVo.getProvinceName()).append(" ");
			}
			if(StringUtils.isNotBlank(partnerOrderVo.getCityName())) {
				addressString.append(partnerOrderVo.getCityName()).append(" ");
			}
			if(StringUtils.isNotBlank(partnerOrderVo.getDistName())) {
				addressString.append(partnerOrderVo.getDistName()).append(" ");
			}
			
			addressString.append(partnerOrderVo.getAddress());			
			addressProductMap.put("address", addressString.toString());
			addressProductMap.put("contact", partnerOrderVo.getContactPerson()+ " " + partnerOrderVo.getContactPersonTel());
			addressMap.put(-1l, addressProductMap);
		}
		return addressMap;
	}

	private void sendCaptcha2Phone(String phone,String captcha) throws Exception{
		SMSUtil.sendSms(phone, captcha, SMSUtil.APP_ID_PP);
	}

	@Override
	public Map<String, Object> sendEmailByStataus(String partnerOrderId) {		
		PartnerOrderVo partnerOrder  = partnerOrderVoMapper.selectByPrimaryKey(partnerOrderId.toString());	
		Date curDate = new Date();
		long diffDay = DateUtils.dateDiff("days", curDate, partnerOrder.getUpdateTime());
		String regionName = getOrderRegionName(partnerOrder);
		//工业油Industrial只发送给工业油CSR
		Boolean industrialOrderFlag = regionName.indexOf(Constants.SALES_CHANNEL_DIRECT_INDUSTRIAL) >= 0;
		if(diffDay >= 2){
			//订单待价格录入
			if(partnerOrder.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
				// 邮件发送
				List<PartnerResponsibleVo> approverList = this.getsendEmailTo(partnerOrder.getPartnerId(),industrialOrderFlag);
				String approverId = "";
				if(approverList != null && !approverList.isEmpty()){
					Set<String> approverEmailList = new HashSet<String>(approverList.size());
					StringBuilder approverName = null;
					Set<String> ccEmailList = new HashSet<String>();
					for(PartnerResponsibleVo approver : approverList){
						if(StringUtils.isBlank(approver.getResponsiblePersonEmail())) {
							continue;
						}
						approverId = approver.getResponsiblePersonId().toString();
						approverEmailList.add(approver.getResponsiblePersonEmail());
						if(approverName == null) {
							approverName = new StringBuilder();
						}else {
							approverName.append(" & ");
						}
						approverName.append(approver.getResponsiblePersonName());
						approver.setPartnerId(partnerOrder.getPartnerId());
						String ccs;
						try {
							ccs = approver.getDayReportCc(Constants.SALES_CHANNEL_INDIRECT);
						} catch (RuntimeException e) {
							log.error(e.getMessage(), e);
							throw (RuntimeException)e;
						} catch (Exception e) {
							log.error(e.getMessage(), e);
							throw new RuntimeException(e);
						}
						if(StringUtils.isNotBlank(ccs)) {
							for(String cc : ccs.split(",")) {
								if(StringUtils.isNotBlank(cc)) {
									ccEmailList.add(cc);
								}
							}
						}
					}
					String[] accepters = new String[approverEmailList.size()];
					accepters = approverEmailList.toArray(accepters);
					String[] accepterCCs = new String[ccEmailList.size()];
					accepterCCs = ccEmailList.toArray(accepterCCs);

					PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrder,true);
					String orderChinesStatus = getOrderChineseStatusByStatusCode(partnerOrder.getStatus());
					String subjectName = orderChinesStatus + ",合伙人[" + partnerOrder.getPartnerName() + "]采购"+regionName+"订单:" + partnerOrder.getOrderNo();
					Map<String,Object> contentMap = new HashMap<String,Object>();
					String acceptPersonName = "尊敬的" + approverName;
					String partnerName = "合伙人" + partnerOrder.getPartnerName() + ",有"+regionName+"订单需要确认价格,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
					contentMap.put("acceptPersonName", acceptPersonName);
					contentMap.put("partnerName", partnerName);
					contentMap.put("skuList", partnerOrderView.getOlEmailList());
					contentMap.put("partnerOrderVo", partnerOrderView);
					List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
					contentMap.put("approvalList", approvalList);
					
					String partnerProperty = partnerService.getPartnerProperty(partnerOrder.getPartnerId());
					if("KA".equals(partnerProperty)){
						File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
						
						if (null != RequestContextHolder.getRequestAttributes()) {
							HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
							String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
							contentMap.put("approvalPageLink", basePath + TokenUtil.generateTokenLink(approverId, "csrConfirmApproval",
									"/business/partnerorder/partnerOrderManage.jsp", "/business/partnerorder/csrConfirm.jsp?partnerOrderId=" + partnerOrder.getId(), false,String.valueOf(curDate.getTime())));
							contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmApprovalDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
							contentMap.put("rejectPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmRejectDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
						}
						
						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
								accepters, null, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
					}else{
						File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
						
						if (null != RequestContextHolder.getRequestAttributes()) {
							HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
							String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
							contentMap.put("approvalPageLink", basePath + TokenUtil.generateTokenLink(approverId, "csrConfirmApproval",
									"/business/partnerorder/partnerOrderManage.jsp", "/business/partnerorder/csrConfirm.jsp?partnerOrderId=" + partnerOrder.getId(), false,String.valueOf(curDate.getTime())));
							contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmApprovalDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
							contentMap.put("rejectPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmRejectDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
						}
						
						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
								accepters, null, subjectName, contentMap, attFiles, "partner_order_info.ftl");
					}
					
				}
			}
			//给订单审批者发邮件
			if (PartnerOrderVo.PARTNER_ORDER_STATUS_2.equals(partnerOrder.getStatus())) {
				//邮件发送			
				Map<String, Object> approverParam = new HashMap<String,Object>();
				approverParam.put("approverRoleName", Constants.CHEVRON_PRICE_AUDIT);
				approverParam.put("partnerId", partnerOrder.getPartnerId());
				approverParam.put("funFlag", "order_confirm");
				approverParam.put("salesChannelName", Constants.SALES_CHANNEL_CDM);
				List<PartnerResponsibleVo> approverList = resonsibleMapper.queryPartnerResponsible(approverParam);
				
				if(approverList != null && !approverList.isEmpty()){
					List<String> approverEmailList = new ArrayList<String>();
					List<String> ccEmailList = new ArrayList<String>();
					List<String> approverNameList =  new ArrayList<String>();
					List<String> approverIdList =  new ArrayList<String>();
					for(PartnerResponsibleVo approver : approverList){
						if( !StringUtils.isNull(approver.getResponsiblePersonEmail()) &&  !StringUtils.isNull(approver.getResponsiblePersonName())){
							approverEmailList.add(approver.getResponsiblePersonEmail());
							approverNameList.add(approver.getResponsiblePersonName());			
						}
						if( !StringUtils.isNull(approver.getDayReportCc())){
							String[] ccEmailArray = approver.getDayReportCc().split(",");
							ccEmailList.addAll(Arrays.asList(ccEmailArray));
						}
						approverIdList.add(approver.getResponsiblePersonId().toString());
					}
					String[] accepters = new String[approverEmailList.size()];
					String[] accepterCCs = new String[ccEmailList.size()];
					accepters = approverEmailList.toArray(accepters);
					accepterCCs = ccEmailList.toArray(accepterCCs);
					String saleName = "";
					for(String name: approverNameList){
						if(saleName.equals("")){
							saleName = name;
						}else{
							saleName = saleName + "&" + name;
						}
					}
					PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrder,true);
					String orderChinesStatus = getOrderChineseStatusByStatusCode(partnerOrder.getStatus());
					String subjectName = orderChinesStatus + ",合伙人[" + partnerOrder.getPartnerName() + "]采购"+regionName+"订单:" + partnerOrder.getOrderNo();
					Map<String,Object> contentMap = new HashMap<String,Object>();
					String acceptPersonName = "尊敬的" + saleName;
					String partnerName = "合伙人" + partnerOrder.getPartnerName() + ",有"+regionName+"订单已定价,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
					contentMap.put("acceptPersonName", acceptPersonName);
					contentMap.put("partnerName", partnerName);
					contentMap.put("skuList", partnerOrderView.getOlEmailList());
					contentMap.put("partnerOrderVo", partnerOrderView);
					List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
					contentMap.put("approvalList", approvalList);
					
					String partnerProperty = partnerService.getPartnerProperty(partnerOrder.getPartnerId());

					if("KA".equals(partnerProperty)){
						File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
						
						if (null != RequestContextHolder.getRequestAttributes()) {
							HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
							String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
							contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "poPriceAuditApprovalDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
							contentMap.put("rejectPageButton", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "poPriceAuditRejectDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
						}
						
						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
								accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_csr_mail.ftl");
					}else{
					
						File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
						
						if (null != RequestContextHolder.getRequestAttributes()) {
							HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
							String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
							contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "poPriceAuditApprovalDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
							contentMap.put("rejectPageButton", basePath + TokenUtil.generateTokenLink(approverIdList.get(0), "poPriceAuditRejectDirectly",
									"", "",true, String.valueOf(partnerOrder.getId()),String.valueOf(curDate.getTime())));
						}
						
						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
								accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_csr_mail.ftl");
					}
				}
			}
			// 给CSR发送下单邮件
			if (PartnerOrderVo.PARTNER_ORDER_STATUS_9.equals(partnerOrder.getStatus()) || PartnerOrderVo.PARTNER_ORDER_STATUS_3.equals(partnerOrder.getStatus())) {
				{
					List<PartnerResponsibleVo> approverList = this.getsendEmailTo(partnerOrder.getPartnerId(),industrialOrderFlag);
					if(approverList != null && !approverList.isEmpty()){
						Set<String> approverEmailList = new HashSet<String>(approverList.size());
						StringBuilder approverName = null;
						Set<String> ccEmailList = new HashSet<String>();
						for(PartnerResponsibleVo approver : approverList){
							if(StringUtils.isBlank(approver.getResponsiblePersonEmail())) {
								continue;
							}
							approverEmailList.add(approver.getResponsiblePersonEmail());
							if(approverName == null) {
								approverName = new StringBuilder();
							}else {
								approverName.append(" & ");
							}
							approverName.append(approver.getResponsiblePersonName());
							approver.setPartnerId(partnerOrder.getPartnerId());
							String ccs;
							try {
								ccs = approver.getDayReportCc(Constants.SALES_CHANNEL_CDM);
							} catch (RuntimeException e) {
								log.error(e.getMessage(), e);
								throw (RuntimeException)e;
							} catch (Exception e) {
								log.error(e.getMessage(), e);
								throw new RuntimeException(e);
							}
							if(StringUtils.isNotBlank(ccs)) {
								for(String cc : ccs.split(",")) {
									if(StringUtils.isNotBlank(cc)) {
										ccEmailList.add(cc);
									}
								}
							}
						}
						String[] accepters = new String[approverEmailList.size()];
						accepters = approverEmailList.toArray(accepters);
						String[] accepterCCs = new String[ccEmailList.size()];
						accepterCCs = ccEmailList.toArray(accepterCCs);
						
						PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(partnerOrder,true);
						String orderChinesStatus = getOrderChineseStatusByStatusCode(partnerOrder.getStatus());
						String subjectName = orderChinesStatus + ",合伙人[" + partnerOrder.getPartnerName() + "]采购"+regionName+"订单:" + partnerOrder.getOrderNo();
						Map<String,Object> contentMap = new HashMap<String,Object>();
						String acceptPersonName = "尊敬的" + approverName;
						String partnerName = "合伙人" + partnerOrder.getPartnerName() + "的"+regionName+"订单已经录入SAP系统，请及时处理，订单详情如下：";
						contentMap.put("acceptPersonName", acceptPersonName);
						contentMap.put("partnerName", partnerName);
						contentMap.put("skuList", partnerOrderView.getOlEmailList());
						contentMap.put("partnerOrderVo", partnerOrderView);
						List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
						contentMap.put("approvalList", approvalList);
						
						String partnerProperty = partnerService.getPartnerProperty(partnerOrder.getPartnerId());
						if("KA".equals(partnerProperty)){
							File[] attFiles = generateKapartnerOrderPDF(partnerOrder.getId(),true,HttpSessionGets.getSession().getServletContext());
							emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
									accepters, null, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
						}else{
							File[] attFiles = generatePartnerOrderPDF(partnerOrder.getId(),true,HttpSessionGets.getSession().getServletContext());
							emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
									accepters, null, subjectName, contentMap, attFiles, "partner_order_info.ftl");
						}				
					}
				}
			}
		}
		return null;
	}
	
	/**
	 * 下订单的时候根据各种活动生产订单详情
	 *
	 * @return
	 */
	/*private List<PartnerOrderLineVo>  generatePartnerOrderlLineByPromotionCampaign(Long partnerId,List<PartnerOrderLineVo> partnerOrderList) {
		
		List<PartnerOrderLineVo> orderLineVoListGenerated = new ArrayList<PartnerOrderLineVo>();
		
		Map<String,Integer> skuCountMap = new HashMap<String,Integer>();
		Map<String,PartnerOrderLineVo> skuOrderLineMap = new HashMap<String,PartnerOrderLineVo>();
		for(PartnerOrderLineVo orderLine : partnerOrderList){
			if(orderLine.getAmount() != null && orderLine.getAmount() > 0){
				skuCountMap.put(orderLine.getSku(), orderLine.getAmount());
			}
			skuOrderLineMap.put(orderLine.getSku(), orderLine);
		}		
		for(PartnerOrderLineVo orderLine : partnerOrderList){
			
			String sku = orderLine.getSku();
			//首先判断是否有打包活动
			Map<String,Object> packageInfoMap = productPackageService.getProductPackageValidBySku(sku, new Date());			
			if(packageInfoMap.get("packageExist") != null && ((String)packageInfoMap.get("packageExist")).equals("true")){
				ProductPackage productPackage =  (ProductPackage)packageInfoMap.get("productPackage");
				List<ProductPackageDetail> productPackageDetailList = (List<ProductPackageDetail>)packageInfoMap.get("productPackageDetailList");
				boolean packageBoolean = true;
				Map<String,Integer> packageSkuCountMap = new HashMap<String,Integer>();
				for(ProductPackageDetail packageDetail : productPackageDetailList){
					//订单列表中不包含礼包的sku 或者 订单的sku的数量小于礼包数量要求， 不满足
					if(!skuCountMap.containsKey(packageDetail.getProductSkuCode()) 
							|| skuCountMap.get(packageDetail.getProductSkuCode()) < packageDetail.getProductCount()){
						packageBoolean = false;
					}else{
						packageSkuCountMap.put(packageDetail.getProductSkuCode(), packageDetail.getProductCount());
					}					
				}
				if(packageBoolean == true){
					//计算礼包数量
					Integer packageCount = 0;
					for(String skuPackage : packageSkuCountMap.keySet()){
						Integer skuOrderCount = skuCountMap.get(skuPackage);
						Integer skuPackageCount = packageSkuCountMap.get(skuPackage);
						if(packageCount == 0){
							packageCount = skuOrderCount / skuPackageCount;
						}
						if(packageCount > (skuOrderCount / skuPackageCount)){
							packageCount = skuOrderCount / skuPackageCount;
						}
					}
					//生产礼包 
					PartnerOrderLineVo orderLinePackage = new PartnerOrderLineVo();
					orderLinePackage.setSku(productPackage.getPackageCode());
					orderLinePackage.setProductName(productPackage.getPackagePromotionTitle());
					orderLinePackage.setPrice(BigDecimal.valueOf(productPackage.getPackagePrice()));
					orderLinePackage.setLadderPrice(BigDecimal.valueOf(productPackage.getPackagePrice()));
					orderLinePackage.setAmount(packageCount);
					orderLinePackage.setActualAmount(packageCount);
					orderLinePackage.setPromotionTitle(productPackage.getPackagePromotionTitle());
					orderLinePackage.setUnits(Constants.PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE);
					orderLinePackage.setBoxCapacity(productPackage.getPackageCapacity().toString());
					orderLinePackage.setCapacity(productPackage.getPackageCapacity().toString());
					orderLinePackage.setPromotionSku(productPackage.getId().toString());
					
					orderLineVoListGenerated.add(orderLinePackage);
					//计算生产礼包扣除后的各种产品数量
					for(String skuPackage : packageSkuCountMap.keySet()){
						Integer skuPackageCount = packageSkuCountMap.get(skuPackage);
						Integer skuOrderCount = skuCountMap.get(skuPackage);
						skuOrderCount = skuOrderCount - skuPackageCount * packageCount;
						skuCountMap.put(skuPackage, skuOrderCount);
					}
					//记录打包中的产品的详情
					for(ProductPackageDetail packageDetail : productPackageDetailList){
						PartnerOrderLineVo orderLineDetail = new PartnerOrderLineVo();
						PartnerOrderLineVo orderLineDetailInCar = skuOrderLineMap.get(packageDetail.getProductSkuCode());	
						orderLineDetail.setProductId(orderLineDetailInCar.getProductId());
						orderLineDetail.setSku(orderLineDetailInCar.getSku());
						orderLineDetail.setProductName(orderLineDetailInCar.getProductName());						
						orderLineDetail.setPrice(orderLineDetailInCar.getPrice());
						orderLineDetail.setLadderPrice(orderLineDetailInCar.getLadderPrice());
						Integer amountinPackage = packageCount * packageDetail.getProductCount();
						orderLineDetail.setAmount(amountinPackage);
						orderLineDetail.setActualAmount(amountinPackage);
						orderLineDetail.setPromotionTitle(orderLinePackage.getPromotionTitle());
						orderLineDetail.setPromotionSku(productPackage.getPackageCode());
						orderLineDetail.setUnits(orderLineDetailInCar.getUnits());
						orderLineDetail.setBoxCapacity(orderLineDetailInCar.getCapacity());
						orderLineDetail.setCapacity(orderLineDetailInCar.getCapacity());
						orderLineDetail.setPackageSell("yes");
						orderLineVoListGenerated.add(orderLineDetail);
					}
					
				}else{
					continue;
				}
			}else{
				//处理买赠这种情况
				Map<String,Object> priceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerId.toString(), orderLine.getSku(), 
						Long.valueOf(orderLine.getAmount()), new Date(),false);
				if(priceMap != null && priceMap.get("giftCount") != null){
					String giftCount = (String)priceMap.get("giftCount");
					if(Integer.valueOf(giftCount) > 0){
						String giftSku = (String)priceMap.get("giftSku");
						String promotionTitle = (String)priceMap.get("promotionTitle");
						PartnerOrderLineVo orderLineFree = new PartnerOrderLineVo();
						ProductVo productFree = productMapper.selectBySku(giftSku);
						orderLineFree.setProductId(productFree.getId());
						orderLineFree.setSku(productFree.getSku());
						orderLineFree.setProductName(productFree.getName());
						
						orderLineFree.setPrice(BigDecimal.valueOf(productFree.getBoxPrice()));
						orderLineFree.setFreeAmount(Integer.valueOf(giftCount));
						if(orderLine.getUnits().equals("box")){
							orderLineFree.setCapacity(productFree.getBoxCapacity().toString());
						}else{
							orderLineFree.setCapacity(productFree.getCapacity());
						}					
						orderLineFree.setType(orderLine.getType());
						orderLineFree.setPromotionTitle(promotionTitle);
						orderLineFree.setUnits(orderLine.getUnits());
						if(sku.equals(giftSku)){
							Integer skuOrderCount = skuCountMap.get(sku);
							orderLineFree.setAmount(skuOrderCount);
							orderLineFree.setActualAmount(skuOrderCount);
							skuCountMap.put(sku, 0);
						}else{
							orderLineFree.setAmount(0);
							orderLineFree.setActualAmount(0);
							orderLineFree.setPromotionSku(sku);
						}
						orderLineVoListGenerated.add(orderLineFree);
					}
				}				
			}						
		}
		for(String sku : skuCountMap.keySet()){
			Integer skuOrderCount = skuCountMap.get(sku);
			if(skuOrderCount > 0){
				Map<String,Object> priceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerId.toString(), sku, 
						Long.valueOf(skuOrderCount), new Date(),false);
				BigDecimal ladderPrice = (BigDecimal)priceMap.get("ladderPrice");
				PartnerOrderLineVo orderLineFree = new PartnerOrderLineVo();
				ProductVo productFree = productMapper.selectBySku(sku);
				orderLineFree.setProductId(productFree.getId());
				orderLineFree.setSku(productFree.getSku());
				orderLineFree.setProductName(productFree.getName());
				orderLineFree.setAmount(skuOrderCount);
				orderLineFree.setActualAmount(skuOrderCount);
				orderLineFree.setPrice(BigDecimal.valueOf(productFree.getBoxPrice()));
				orderLineFree.setLadderPrice(ladderPrice);

				PartnerOrderLineVo orderLine = skuOrderLineMap.get(sku);
				if(orderLine.getUnits().equals("box")){
					orderLineFree.setCapacity(productFree.getBoxCapacity().toString());
				}else{
					orderLineFree.setCapacity(productFree.getCapacity());
				}
				
				orderLineFree.setType(productFree.getCategory());
				if(productFree.getBoxCapacity() == 200D){
					orderLineFree.setUnits("barrel");
				}else{
					orderLineFree.setUnits("box");
				}
				orderLineVoListGenerated.add(orderLineFree);
			}
		}
		
		return orderLineVoListGenerated;
		
	}*/
	
	protected Map<String, ProductMaster> buildOrderProductMap(List<PartnerOrderLineVo> orderLines) throws WxPltException {
		List<String> skus = new ArrayList<String>(orderLines.size());
		for(PartnerOrderLineVo orderLineVo : orderLines) {
			skus.add(orderLineVo.getSku());
		}
		Map<String, Object> params = new HashMap<String, Object>(2);
		params.put("skus", skus);
		Map<String, ProductMaster> productMap = new HashMap<String, ProductMaster>(orderLines.size());
		List<ProductMaster> productList = productMasterBizService.queryByParams(params);
		for(ProductMaster productMaster : productList) {
			productMap.put(productMaster.getSku(), productMaster);
		}
		return productMap;
	}
	
//	@Override
//	public Map<String,Object> getSapOrderUtil(String partnerId,Long partnerSaleConfigId, PartnerOrderLineVo[] orderLines){
//		ResponseMap map = new ResponseMap();
//		log.info("getSapOrderUtil: " + JsonUtil.writeValue(orderLines));
//		try {
////			Map<String, ProductMaster> productMap = buildOrderProductMap(orderLines);
////			List<PartnerOrderLineVo> orderLineList = new ArrayList<PartnerOrderLineVo>(orderLines.length);
////			Map<String,Integer> skuAmountMap = new HashMap<String,Integer>();
////			Map<String,String> sapSaleUnit = getSapSaleUnitMap();
////			for(PartnerOrderLineVo orderLine : orderLineList){
////				Integer amount = orderLine.getAmount();
////				skuAmountMap.put(orderLine.getSku(), amount);
////				String salesUnit = orderLine.getUnits();
////				if(salesUnit.equals("box")){
////					ProductVo product = productMapper.selectBySku(orderLine.getSku());
////					if(sapSaleUnit.get(product.getSku()) != null && sapSaleUnit.get(product.getSku()).equals("BO")){
////						Integer bottleQtyPerBox = product.getBottleQty();
////						Integer saleQty = bottleQtyPerBox * orderLine.getAmount();
////						orderLine.setAmount(saleQty);
////					}
////				}
////					
////			}
//			Map<String,Object> sapOrderLineMap = new HashMap<String,Object>();
//			List<PartnerOrderpProductView>  sapOrderLineViewList = new ArrayList<PartnerOrderpProductView>();
//			
//			if(partnerSaleConfigId != null){
//				WxTPartnerSaleConfigExample partnerSaleConfigExample = new WxTPartnerSaleConfigExample();
//				partnerSaleConfigExample.createCriteria().andIdEqualTo(partnerSaleConfigId);
//				WxTPartnerSaleConfig partnerSaleConfig = partnerSaleConfigMapper.selectByExample(partnerSaleConfigExample).get(0);
//				String salesOrg = partnerSaleConfig.getSalesOrg();
//				String saleCode = new String();
//				if(partnerSaleConfig.getPriceLevel().equals("sold_to_code")){
//					saleCode = partnerSaleConfig.getSapCode();
//				}else if(partnerSaleConfig.getPriceLevel().equals("ship_to_code")){
//					saleCode = partnerSaleConfig.getShipToCode();
//				}
//				sapOrderLineMap = getSapOrderService.getSapOrderUtil(salesOrg,saleCode,orderLineList);
//				resultMap.put("salesOrg", salesOrg);
//				resultMap.put("salesCode", saleCode);
//				
//				WxTPartnerSaleConfigProductExample partnerSaleConfigProductExample = new WxTPartnerSaleConfigProductExample();
//				partnerSaleConfigProductExample.createCriteria().andConfigIdEqualTo(partnerSaleConfigId);
//				List<WxTPartnerSaleConfigProduct> pscpList = partnerSaleConfigProductMapper.selectByExample(partnerSaleConfigProductExample);
//				
//				
//				resultMap.put("partnerSaleConfig", partnerSaleConfig);
//				resultMap.put("partnerSaleConfigProduct", pscpList);
//							
//			}else{
//				String sapCode = new String();
//				String salesOrg = "2051";
//				
//				DicItemVoExample dicItemVoExample = new DicItemVoExample();
//				dicItemVoExample.createCriteria().andDicItemCodeEqualTo("partnersaleconfig.onoff");
//				List<DicItemVo> dicItemList = dicItemVoMapper.selectByExample(dicItemVoExample);
//				//采用master表数据
//				if(dicItemList != null && dicItemList.get(0).getDicItemName().equals("off")){
//					Map<String, Object> reqMap = new HashMap<String, Object>();
//					reqMap.put("partnerId", partnerId);
//					List<WxTPartnerSaleConfig> partnerSaleConfigList = partnerSaleConfigMapper.getPartnerSaleConfigByMap(reqMap);
//					if(partnerSaleConfigList != null && !partnerSaleConfigList.isEmpty()){
//						for(WxTPartnerSaleConfig psc : partnerSaleConfigList){
//							if(psc.getQuotaType() == null || psc.getQuotaType().isEmpty()){
//								salesOrg = psc.getSalesOrg();
//								if("ship_to_code".equals(psc.getPriceLevel())){
//									sapCode = psc.getShipToCode();
//								}else{
//									sapCode = psc.getSapCode();
//								}
//							}
//						}
//						sapOrderLineMap = getSapOrderService.getSapOrderUtil(salesOrg,sapCode,orderLineList);
//						resultMap.put("salesOrg", salesOrg);
//						resultMap.put("salesCode", sapCode);
//					}else{
//						sapOrderLineMap = getSapOrderPrice2051(partnerId, orderLineList
//								,skuAmountMap,sapSaleUnit);
//						resultMap.put("salesOrg", sapOrderLineMap.get("salesOrg"));
//						resultMap.put("salesCode", sapOrderLineMap.get("salesCode"));
//					}
//				}else{			
//					sapOrderLineMap = getSapOrderPrice2051(partnerId, orderLineList
//							,skuAmountMap,sapSaleUnit);
//					resultMap.put("salesOrg", sapOrderLineMap.get("salesOrg"));
//					resultMap.put("salesCode", sapOrderLineMap.get("salesCode"));
//				}
//			}
//			
//			if(sapOrderLineMap.get(Constants.RESULT_CODE_KEY).equals(Constants.SUCCESS)){
//				sapOrderLineViewList = generateSapOrderLines(sapOrderLineMap);
//				
//			}else{
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, sapOrderLineMap.get(Constants.RESULT_ERROR_MSG_KEY));
//				resultMap.put("sapOrderLineList", sapOrderLineViewList);
//			}
//			partnerOrderDetailLst = generatePartnerOrderlLineBySapPromotionCampaign(partnerOrderDetailLst);
//			partnerOrderDetailLst = generatePartnerOrderlLinePackage(partnerOrderDetailLst);
//			
//			sapOrderLineViewList = generateSapPartnerOrderlLineByPromotionCampaign(partnerId,sapOrderLineViewList);
//			
//			
//			resultMap.put("sapOrderLineList", sapOrderLineViewList);
//			
//			partnerOrderDetailLst = generatePartnerOrderlLineByPromotionCampaign(partnerId,partnerOrderDetailLst);
//			if(partnerOrderDetailLst == null || partnerOrderDetailLst.isEmpty()){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "获取价格异常!");
//				return resultMap;
//			}
//			resultMap.put("orderLineDetailList", partnerOrderDetailLst);
//			
//			log.info("getSapOrderUtil success." );
//		} catch (WxPltException e) {
//			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//			map.setErrorMsg(e.getMessage());
//		} catch (Exception e) {
//			map.handleException(e, this, ContextUtil.getCurUserId(), 
//					"com.chevron.partnerorder.service.impl.PartnerOrderServiceImpl.getSapOrderUtil", JsonUtil.writeValue(orderLines));
//		}
//		return map;
//
//	}
	
	private WxTPartnerSaleConfig getPartnerSaleConfig(Long partnerSaleConfigId, String partnerId) throws WxPltException {
		Map<String, Object> reqMap = new HashMap<String, Object>(5);
		if(partnerSaleConfigId	!= null) {
			reqMap.put("id", partnerSaleConfigId);
		}else {
			throw new WxPltException("经销商下单配置不能未空");
//			reqMap.put("partnerId", partnerId);
		}
		reqMap.put("activeFlag", 1);
		List<WxTPartnerSaleConfig> partnerSaleConfigList = partnerSaleConfigMapper.getPartnerSaleConfigByMap(reqMap);
		if(partnerSaleConfigList.isEmpty()) {
			throw new WxPltException("未获取到经销商下单配置");
		}
		return partnerSaleConfigList.get(0);
	}
	
	private void checkQuota(WxTPartnerSaleConfig partnerSaleConfig, List<PartnerOrderLineVo> orderLines, Map<String, ProductMaster> productMap) throws WxPltException {
		//1. 非配额下单主数据，不需现在下单量
		if(!"1".equals(partnerSaleConfig.getIsQuotaRatio())) {
			return;
		}
		//2. 查询下单主数据的配额使用情况
		List<WxTPartnerSaleConfigProduct> saleConfigProducts = partnerSaleConfigProductMapper.queryRemainQuota(partnerSaleConfig.getId());
		if(saleConfigProducts.isEmpty()) {
			return;
		}
		//3. by sku构建配额使用情况mapping
		Map<String, WxTPartnerSaleConfigProduct> saleConfigProductMap = new HashMap<String, WxTPartnerSaleConfigProduct>(saleConfigProducts.size());
		for(WxTPartnerSaleConfigProduct item : saleConfigProducts) {
			saleConfigProductMap.put(item.getSku(), item);
		}
		//4. 检查下单产品是否超过配额
		for(PartnerOrderLineVo orderLineVo : orderLines) {
			if(!productMap.containsKey(orderLineVo.getSku())) {
				throw new WxPltException("产品" + orderLineVo.getSku() + "已下架。请联系雪佛龙销售！");
			}
			WxTPartnerSaleConfigProduct saleConfigProduct = saleConfigProductMap.get(orderLineVo.getSku());
			if(saleConfigProduct != null && (saleConfigProduct.getRemainQuota() - orderLineVo.getAmount() * productMap.get(orderLineVo.getSku()).getBoxCapacity().doubleValue()) < -0.000001) {
				throw new WxPltException("产品" + orderLineVo.getSku() + "剩余配额" + saleConfigProduct.getRemainQuota() + "小于下单升数。请联系雪佛龙销售！");
			}
		}
	}
	
	@SuppressWarnings("unchecked")
	public Map<String,Object> getSapOrderUtil(String partnerId,Long partnerSaleConfigId, List<PartnerOrderLineVo> orderLines){
		ResponseMap resultMap = new ResponseMap();
		try{

			boolean hasAorB=false;
			int acountB = 0;
			WxTUser curUser = ContextUtil.getCurUser();
			Iterator<PartnerOrderLineVo> iterator = orderLines.iterator();
			while (iterator.hasNext()) {
				PartnerOrderLineVo orderLineVo = iterator.next();
				if(orderLineVo.getSku().equalsIgnoreCase("503005LPB") || orderLineVo.getSku().equalsIgnoreCase("503007LPB")){
					acountB=acountB+orderLineVo.getAmount();
					hasAorB = true;
				}
				if(orderLineVo.getAmount()==null || orderLineVo.getAmount()==0){
					iterator.remove();
				}
			}

			//1. 获取下单主数据
			WxTPartnerSaleConfig partnerSaleConfig = getPartnerSaleConfig(partnerSaleConfigId, partnerId);
			String salesOrg = partnerSaleConfig.getSalesOrg();
			if(StringUtils.isEmpty(salesOrg)){
//				salesOrg = "2051";
				throw new WxPltException("未配置Sales Org。请联系雪佛龙销售！");
			}
			//获取需要校验的经销商 根据字典中配置的销售
			/* 2025-02-0-26 去除此条件，改为所有经销商都需大于500 (该字典配置作废)
			DicItemVoExample example = new DicItemVoExample();
			example.createCriteria().andDicTypeCodeEqualTo("partner_order_check_by_cai").andStatusEqualTo("1");
			List<DicItemVo> dicItemVos = dicItemVoMapper.selectByExample(example);
			List<String> cais = dicItemVos.stream().map(DicItemVo::getDicItemCode).collect(Collectors.toList());*/
			//所有经销商需校验•	503005LPB，503007LPB产品数量，
			/*•如果订单中出现上述两个产品（单独出现A(503005LPB)、单独出现B(503007LPB)或A、B同时出现），需要确保A产品和B产品的总数量大于或等于500，订单才能提交
			   --以销售同事来划分经销商，涉及销售为：白英杰、郑爱武、杨森、曾威宇、高登峰、蒋林、李伟德、张东浩、尚浩、李强、章园；现已改为所有经销商2025-02-26*/
			if(hasAorB & acountB<500){
				throw new WxPltException("订单中含有503005LPB产品或503007LPB产品，它们总数量(单个或之和)需大于或等于500才可下单，请重新修改后再试！");
			}

			//2. by sku构建下单产品的产品详细信息map
			Map<String, ProductMaster> productMap = buildOrderProductMap(orderLines);
			//3. 下单记录的配额检查
			checkQuota(partnerSaleConfig, orderLines, productMap);
			//4. 获取SAP订单价格
			String salesCode = null;
			if("ship_to_code".equals(partnerSaleConfig.getPriceLevel())){
				salesCode = partnerSaleConfig.getShipToCode();
			}else{
				salesCode = partnerSaleConfig.getSapCode();
			}
			Map<String,PartnerOrderLineVo> priceMap = getSapOrderService.getProductSapPriceMap(salesOrg, salesCode, orderLines, productMap, curUser.getLoginName());
			LogUtils.addInfoLog(ContextUtil.getCurUserId(),"com.chevron.partnerorder.service.impl.PartnerOrderServiceImpl.getSapOrderUtil1",JsonUtil.toJSONString(priceMap));
			log.info("priceMap json === "+JsonUtil.toJSONString(priceMap));
			//5. 补充订单中的价格和产品信息
			for(PartnerOrderLineVo orderLineVo : orderLines) {
				PartnerOrderLineVo priceProduct = priceMap.get(orderLineVo.getSku());
				if(priceProduct.getTotalValue() == null) {
					throw new WxPltException(orderLineVo.getSku() + "价格没有维护到SAP，请联系价格专员咨询");
				}
				ProductMaster productMaster = productMap.get(orderLineVo.getSku());
				orderLineVo.setActualAmount(priceProduct.getActualAmount());
				orderLineVo.setFreeAmount(priceProduct.getFreeAmount());
				orderLineVo.setTotalValue(priceProduct.getTotalValue());
				orderLineVo.setLadderPrice(priceProduct.getTotalValue().divide(new BigDecimal(priceProduct.getActualAmount()),2,BigDecimal.ROUND_HALF_UP));
				orderLineVo.setTotalLiterCount(productMaster.getBoxCapacity().multiply(new BigDecimal(priceProduct.getActualAmount() 
						+ (priceProduct.getFreeAmount() == null ? 0 : priceProduct.getFreeAmount()))).doubleValue());
				orderLineVo.setProductName(productMaster.getName());
				orderLineVo.setProductPhoto(productMaster.getIconId() == null ? null : productMaster.getIconId().toString());
				orderLineVo.setUnitsText(productMaster.getPackUnits());
			}
			//6. 买A赠B促销切B产品不在下单列表时，将赠送的B产品添加到下单列表中
			for(String sku : priceMap.keySet()) {
				if(!productMap.containsKey(sku)) {
					PartnerOrderLineVo priceProduct = priceMap.get(sku);
					PartnerOrderLineVo orderLineVo = new PartnerOrderLineVo();
					orderLineVo.setSku(sku);
					orderLineVo.setAmount(0);
					orderLineVo.setActualAmount(0);
					orderLineVo.setFreeAmount(priceProduct.getFreeAmount());
					orderLineVo.setTotalValue(BigDecimal.ZERO);
					orderLineVo.setLadderPrice(BigDecimal.ZERO);
					ProductMaster productMaster = productMasterBizService.getBeanBySku(sku);
					if(productMaster == null) {
						continue;
					}
					if(productMaster.getBoxCapacity() == null) {
						WeixinMessageUtil.weixinAlarmMessagePush("采购订单", "采购订单异常", "产品" + sku + "未配置每箱容量", productMaster.getName() + "(" + productMaster.getSku() + ")", "产品" + sku + "未配置每箱容量");
					}
					orderLineVo.setProductName(productMaster.getName());
					orderLineVo.setProductPhoto(productMaster.getIconId() == null ? null : productMaster.getIconId().toString());
					orderLineVo.setUnitsText(productMaster.getPackUnits());
					orderLineVo.setTotalLiterCount(productMaster.getBoxCapacity().multiply(new BigDecimal(priceProduct.getFreeAmount())).doubleValue());
					orderLines.add(orderLineVo);
				}
			}
			resultMap.put("salesOrg", salesOrg);
			resultMap.put("salesCode", salesCode);
					
			resultMap.put("orderLineDetailList", orderLines);
		}catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		}catch (Exception e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
			LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.partnerorder.service.impl.PartnerOrderServiceImpl.getSapOrderUtil", 
					"获取订单价格失败。" + e.getMessage(), partnerId + ";" + partnerSaleConfigId);
			log.error(e.getMessage() + "。" + partnerId + ";" + partnerSaleConfigId + ";" + JsonUtil.writeValue(orderLines), e);
		}
		return resultMap;
	}
	
	private List<PartnerOrderpProductView> generateSapOrderLines(Map<String,Object> sapOrderLineMap){
		List<PartnerOrderpProductView> sapOrderLineViewList = new ArrayList<PartnerOrderpProductView>();
		
		List<PartnerOrderLineVo> sapOrderLineList = (List<PartnerOrderLineVo>)sapOrderLineMap.get("partnerOrderLine");
		if(sapOrderLineList == null){
			return sapOrderLineViewList;
		}
		Map<String,String> itemSkuMap = new HashMap<String,String>();
		for(PartnerOrderLineVo sapOrderLine : sapOrderLineList){
			if(sapOrderLine.getTotalValue().compareTo(BigDecimal.valueOf(0)) != 0){
				itemSkuMap.put(sapOrderLine.getItmNumber(), sapOrderLine.getSku());
			}
		}
		
		for(PartnerOrderLineVo sapOrderLine : sapOrderLineList){	
			ProductVo product = productMapper.selectBySku(sapOrderLine.getSku());
			PartnerOrderpProductView sapOrderLineView  = new PartnerOrderpProductView(product);
			
			if(sapOrderLine.getTotalValue().compareTo(BigDecimal.valueOf(0)) == 0){
				itemSkuMap.put(sapOrderLine.getItmNumber(), sapOrderLine.getSku());
				sapOrderLineView.setPromotionTitle(itemSkuMap.get(sapOrderLine.getHgLvItem()));
				sapOrderLineView.setPromotionSku(itemSkuMap.get(sapOrderLine.getHgLvItem()));
			}
			
			sapOrderLineView.setTotalValue(sapOrderLine.getTotalValue());
			if(sapOrderLine.getUnits().equals("BO")){				
				Integer bottleQtyPerBox = product.getBottleQty();
				Integer boxQty = sapOrderLine.getAmount() / bottleQtyPerBox;
				BigDecimal totalValue = sapOrderLine.getTotalValue();
				sapOrderLineView.setTotalValue(totalValue);
				BigDecimal boxPirce = totalValue.divide(BigDecimal.valueOf(boxQty),2,BigDecimal.ROUND_HALF_UP);
				sapOrderLineView.setDiscountedPrice(boxPirce);
				sapOrderLineView.setAmount(boxQty);					
				sapOrderLineView.setUnits("box");
				Double boxCapacity = product.getBoxCapacity();
				Double capacityTotalAmount = boxCapacity * boxQty;
				sapOrderLineView.setCapacityTotalAmount(capacityTotalAmount);
			}else if(sapOrderLine.getUnits().equals("CA")){				
				Integer boxQty = sapOrderLine.getAmount();
				BigDecimal totalValue = sapOrderLine.getTotalValue();
				BigDecimal boxPirce = totalValue.divide(BigDecimal.valueOf(boxQty),2,BigDecimal.ROUND_HALF_UP);
				sapOrderLineView.setDiscountedPrice(boxPirce);
				sapOrderLineView.setAmount(boxQty);					
				sapOrderLineView.setUnits("box");
				Double boxCapacity = product.getBoxCapacity();
				Double capacityTotalAmount = boxCapacity * boxQty;
				sapOrderLineView.setCapacityTotalAmount(capacityTotalAmount);
			}else{
				BigDecimal totalValue = sapOrderLine.getTotalValue();
				Integer boxQty = sapOrderLine.getAmount();
				BigDecimal boxPirce = totalValue.divide(BigDecimal.valueOf(boxQty),2,BigDecimal.ROUND_HALF_UP);
				sapOrderLineView.setDiscountedPrice(boxPirce);
				sapOrderLineView.setUnits("barrel");
				sapOrderLineView.setAmount(boxQty);
				Double boxCapacity = product.getBoxCapacity();
				Double capacityTotalAmount = boxCapacity * boxQty;
				sapOrderLineView.setCapacityTotalAmount(capacityTotalAmount);
			}
			/*Map<String,Object> deductibleMap = sellInProductPriceService.getProductPriceRuleByPriceConfig(partnerId, product.getSku(), sapOrderLineView.getCount().longValue());*/
			sapOrderLineView.setDeductible("0");
			sapOrderLineViewList.add(sapOrderLineView);
		}
		return sapOrderLineViewList;
	}
	
	
//	private Map<String,Object> getSapOrderPrice2051(String partnerId, List<PartnerOrderLineVo> orderLineList
//			,Map<String,Integer> skuAmountMap,Map<String,String> sapSaleUnit){
//		Map<String,Object> sapOrderLineMap = new HashMap<String,Object>();
//		
//		Map<String, Object> _params = new HashMap<String, Object>();
//		_params.put("partnerId", partnerId);
//		List<PartnerView> partnerList = organizationVoMapper.selectPartnersByParams(_params);
//		
//		String codeFlag = getPartnerSappriceCodeflag(partnerId);
//		String sapCode = null;
//		if(!StringUtils.isEmpty(codeFlag) && codeFlag.equals(Constants.PARTNER_SAPPRICE_SHIPTOCODE_FLAG)){
//			sapCode	= partnerList.get(0).getShipToCode();
//		}else {
//			sapCode	= partnerList.get(0).getSapCode();
//		}
//		sapOrderLineMap = getSapOrderService.getSapOrderUtil("2051",sapCode,orderLineList);
//		sapOrderLineMap.put("salesOrg", "2051");
//		sapOrderLineMap.put("salesCode", sapCode);
//		
//		boolean isPriceExisted = false;
//		if(sapOrderLineMap.get(Constants.RESULT_CODE_KEY).equals(Constants.SUCCESS)){
//			List<PartnerOrderLineVo> sapOrderLineList = (List<PartnerOrderLineVo>)sapOrderLineMap.get("partnerOrderLine");
//			for(PartnerOrderLineVo sapOrderLine : sapOrderLineList){
//				if(sapOrderLine.getTotalValue().compareTo(BigDecimal.valueOf(0)) != 0){
//					isPriceExisted = true;
//				}
//			}
//		}
//		if(isPriceExisted == false){
//			String shipToCode = partnerList.get(0).getShipToCode();
//			sapOrderLineMap = getSapOrderService.getSapOrderUtil("2051",shipToCode,orderLineList);
//			sapOrderLineMap.put("salesOrg", "2051");
//			sapOrderLineMap.put("salesCode", shipToCode);
//		}
//		
//		boolean isUpdateSaleUnits = false;
//		if(sapOrderLineMap.get(Constants.RESULT_CODE_KEY).equals(Constants.SUCCESS)){
//			List<PartnerOrderLineVo> sapOrderLineList = (List<PartnerOrderLineVo>)sapOrderLineMap.get("partnerOrderLine");
//			isUpdateSaleUnits = getUpdateSaleUnits(sapOrderLineList);
//		}
//		if(isUpdateSaleUnits == true){
//			sapSaleUnit = getSapSaleUnitMap();
//			for(PartnerOrderLineVo orderLine : orderLineList){
//				orderLine.setAmount(skuAmountMap.get(orderLine.getSku()));
//				String salesUnit = orderLine.getUnits();
//				if(salesUnit.equals("box")){
//					ProductVo product = productMapper.selectBySku(orderLine.getSku());
//					if(sapSaleUnit.get(product.getSku()) != null && sapSaleUnit.get(product.getSku()).equals("BO")){
//						Integer bottleQtyPerBox = product.getBottleQty();
//						Integer saleQty = bottleQtyPerBox * orderLine.getAmount();
//						orderLine.setAmount(saleQty);
//					}
//				}						
//			}
//			
//			
//			sapCode = partnerList.get(0).getSapCode();
//			sapOrderLineMap = getSapOrderService.getSapOrderUtil("2051",sapCode,orderLineList);
//			sapOrderLineMap.put("salesOrg", "2051");
//			sapOrderLineMap.put("salesCode", sapCode);
//			
//			isPriceExisted = false;
//			if(sapOrderLineMap.get(Constants.RESULT_CODE_KEY).equals(Constants.SUCCESS)){
//				List<PartnerOrderLineVo> sapOrderLineList = (List<PartnerOrderLineVo>)sapOrderLineMap.get("partnerOrderLine");
//				for(PartnerOrderLineVo sapOrderLine : sapOrderLineList){
//					if(sapOrderLine.getTotalValue().compareTo(BigDecimal.valueOf(0)) != 0){
//						isPriceExisted = true;
//					}
//				}
//			}
//			if(isPriceExisted == false){
//				String shipToCode = partnerList.get(0).getShipToCode();
//				sapOrderLineMap = getSapOrderService.getSapOrderUtil("2051",shipToCode,orderLineList);	
//				sapOrderLineMap.put("salesOrg", "2051");
//				sapOrderLineMap.put("salesCode", shipToCode);
//			}
//		}
//		return sapOrderLineMap;
//	}
	
	private Map<String,String> getSapSaleUnitMap(){
		Map<String ,String> resultMap = new HashMap<String,String>();
		DicItemVoExample dicItemVoExample = new DicItemVoExample();
		dicItemVoExample.createCriteria().andDicTypeCodeEqualTo("sap_sale_unit");
		List<DicItemVo> dicItemList = dicItemVoMapper.selectByExample(dicItemVoExample);
		for(DicItemVo dicItem : dicItemList){
			resultMap.put(dicItem.getDicItemCode(), dicItem.getDicItemName());
		}
		return resultMap;
	}
	
	private String getPartnerSappriceCodeflag(String partnerId){
		String codeFlag = new String();
		DicItemVoExample dicItemVoExample = new DicItemVoExample();
		dicItemVoExample.createCriteria().andDicTypeCodeEqualTo(Constants.PARTNER_SAPPRICE_CODE_FLAG)
			.andDicItemCodeEqualTo(partnerId);
		List<DicItemVo> dicItemList = dicItemVoMapper.selectByExample(dicItemVoExample);
		if(dicItemList != null && !dicItemList.isEmpty()){
			codeFlag = dicItemList.get(0).getDicItemName();
		}
		return codeFlag;
	}
	
	private boolean getUpdateSaleUnits(List<PartnerOrderLineVo> sapOrderLineList){
		boolean isSalesUnitChanged = false;
		Map<String,String> sapSaleUnit = getSapSaleUnitMap();
		for(PartnerOrderLineVo sapOrderLine : sapOrderLineList){
			if(sapOrderLine.getTotalValue().compareTo(BigDecimal.valueOf(0)) != 0){
				if(!sapOrderLine.getUnits().equals(sapSaleUnit.get(sapOrderLine.getSku()))){
					isSalesUnitChanged = true;
					DicItemVoExample dicItemVoExample = new DicItemVoExample();
					dicItemVoExample.createCriteria().andDicTypeCodeEqualTo("sap_sale_unit")
						.andDicItemCodeEqualTo(sapOrderLine.getSku());
					List<DicItemVo> dicItemList = dicItemVoMapper.selectByExample(dicItemVoExample);
					if(dicItemList != null && !dicItemList.isEmpty()){
						DicItemVo dicItem = dicItemList.get(0);
						dicItem.setDicItemName(sapOrderLine.getUnits());
						dicItemVoMapper.updateByPrimaryKey(dicItem);
					}else{
						DicItemVo dicItem = new DicItemVo();
						dicItem.setDicTypeCode("sap_sale_unit");
						dicItem.setDicItemCode(sapOrderLine.getSku());
						dicItem.setDicItemName(sapOrderLine.getUnits());
						dicItemVoMapper.insertSelective(dicItem);
					}
				}
			}
		}
		return isSalesUnitChanged;
	}
	
	private List<PartnerOrderLineVo>  generatePartnerOrderlLineBySapPromotionCampaign(List<PartnerOrderLineVo> partnerOrderList) {
		List<PartnerOrderLineVo> orderlineList = new ArrayList<PartnerOrderLineVo>();
		List<PartnerOrderLineVo> orderlinePromotionList = new ArrayList<PartnerOrderLineVo>();
		Map<String,PartnerOrderLineVo> mainSkuLineMap = new HashMap<String,PartnerOrderLineVo>();
		for(PartnerOrderLineVo orderLineVo : partnerOrderList){
			if(orderLineVo.getLadderPrice().compareTo(BigDecimal.valueOf(0)) != 0){
				orderlineList.add(orderLineVo);
			}else{
				mainSkuLineMap.put(orderLineVo.getPromotionSku(), orderLineVo);
			}
		}
		for(PartnerOrderLineVo orderLineVo : orderlineList){
			if(mainSkuLineMap.containsKey(orderLineVo.getSku())){
				PartnerOrderLineVo promotionLine = mainSkuLineMap.get(orderLineVo.getSku());
				if(promotionLine.getSku().equals(orderLineVo.getSku())){
					orderLineVo.setFreeAmount(promotionLine.getAmount());
					orderLineVo.setPromotionSku(promotionLine.getSku());
					orderLineVo.setPromotionTitle(promotionLine.getPromotionTitle());
					mainSkuLineMap.remove(orderLineVo.getSku());
				}else{
					int freeAmount = promotionLine.getAmount();
					promotionLine.setFreeAmount(freeAmount);
					promotionLine.setAmount(0);
					promotionLine.setActualAmount(0);
					orderlinePromotionList.add(promotionLine);
				}				
			}
		}
		orderlineList.addAll(orderlinePromotionList);
		return orderlineList;
	}
	
	private List<PartnerOrderpProductView>  generateSapPartnerOrderlLineByPromotionCampaign(String partnerId, List<PartnerOrderpProductView> partnerOrderList) {
		List<PartnerOrderpProductView> orderlinePromotionList = new ArrayList<PartnerOrderpProductView>();
		
		Map<String,Integer> skuCountMap = new HashMap<String,Integer>();
		for(PartnerOrderpProductView orderLineVo : partnerOrderList){
			if(skuCountMap.get(orderLineVo.getSku()) != null){
				Integer count = skuCountMap.get(orderLineVo.getSku());
				Integer amount = orderLineVo.getCount() == null ? 0 : orderLineVo.getCount();
				count = count + amount;
				skuCountMap.put(orderLineVo.getSku(), count);
			}else{
				Integer count = 0;
				Integer amount = orderLineVo.getCount() == null ? 0 : orderLineVo.getCount();
				count = count + amount;
				skuCountMap.put(orderLineVo.getSku(), count);
			}
		}
		for(String sku : skuCountMap.keySet()){
			Map<String,Object> priceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerId, sku, 
					Long.valueOf(skuCountMap.get(sku)), new Date(),true);
			if(priceMap != null && priceMap.get("giftCount") != null){
				PartnerOrderLineVo orderLineGift = (PartnerOrderLineVo)priceMap.get("orderLineGift");
				ProductVo product = productMapper.selectBySku(orderLineGift.getSku());
				PartnerOrderpProductView orderProductGift = new PartnerOrderpProductView(product);
				orderProductGift.setCount(orderLineGift.getActualAmount());
				orderProductGift.setDiscountedPrice(orderLineGift.getLadderPrice());
				orderProductGift.setPromotionTitle(orderLineGift.getPromotionTitle());
				orderProductGift.setCapacityTotalAmount(orderLineGift.getTotalLiterCount());
				orderProductGift.setTotalValue(orderLineGift.getTotalValue());
				orderProductGift.setUnits(orderLineGift.getUnits());
				orderProductGift.setPromotionSku(orderLineGift.getPromotionSku());
				orderlinePromotionList.add(orderProductGift);
			}
		}
		/*for(PartnerOrderpProductView orderLineVo : partnerOrderList){
			if(orderLineVo.getDiscountedPrice().compareTo(BigDecimal.valueOf(0)) != 0){
				//处理买赠这种情况
				Map<String,Object> priceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerId, orderLineVo.getSku(), 
						Long.valueOf(orderLineVo.getCount()), new Date(),true);
				if(priceMap != null && priceMap.get("giftCount") != null){
					PartnerOrderLineVo orderLineGift = (PartnerOrderLineVo)priceMap.get("orderLineGift");
					ProductVo product = productMapper.selectBySku(orderLineGift.getSku());
					PartnerOrderpProductView orderProductGift = new PartnerOrderpProductView(product);
					orderProductGift.setCount(orderLineGift.getActualAmount());
					orderProductGift.setDiscountedPrice(orderLineGift.getLadderPrice());
					orderProductGift.setPromotionTitle(orderLineGift.getPromotionTitle());
					orderProductGift.setCapacityTotalAmount(orderLineGift.getTotalLiterCount());
					orderProductGift.setTotalValue(orderLineGift.getTotalValue());
					orderProductGift.setUnits(orderLineGift.getUnits());
					orderProductGift.setPromotionSku(orderLineGift.getPromotionSku());
					orderlinePromotionList.add(orderProductGift);
				}
			}
		}*/
		partnerOrderList.addAll(orderlinePromotionList);
		return partnerOrderList;
	}
	
	private List<PartnerOrderLineVo>  generatePartnerOrderlLineByPromotionCampaign(String partnerId, List<PartnerOrderLineVo> partnerOrderList) {
		List<PartnerOrderLineVo> orderlinePromotionList = new ArrayList<PartnerOrderLineVo>();
		Map<String,Integer> skuCountMap = new HashMap<String,Integer>();
		for(PartnerOrderLineVo orderLineVo : partnerOrderList){
			if(skuCountMap.get(orderLineVo.getSku()) != null){
				Integer count = skuCountMap.get(orderLineVo.getSku());
				Integer amount = orderLineVo.getAmount() == null ? 0 : orderLineVo.getAmount();
				Integer freeAmount = orderLineVo.getFreeAmount() == null ? 0 : orderLineVo.getFreeAmount();
				count = count + amount + freeAmount;
				skuCountMap.put(orderLineVo.getSku(), count);
			}else{
				Integer count = 0;
				Integer amount = orderLineVo.getAmount() == null ? 0 : orderLineVo.getAmount();
				Integer freeAmount = orderLineVo.getFreeAmount() == null ? 0 : orderLineVo.getFreeAmount();
				count = count + amount + freeAmount;
				skuCountMap.put(orderLineVo.getSku(), count);
			}
		}
		for(String sku : skuCountMap.keySet()){
			Map<String,Object> priceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerId, sku, 
					Long.valueOf(skuCountMap.get(sku)), new Date(),true);
			if(priceMap != null && priceMap.get("giftCount") != null){
				PartnerOrderLineVo orderLineGift = (PartnerOrderLineVo)priceMap.get("orderLineGift");
				orderlinePromotionList.add(orderLineGift);
			}
		}
		/*for(PartnerOrderLineVo orderLineVo : partnerOrderList){
			if(orderLineVo.getLadderPrice().compareTo(BigDecimal.valueOf(0)) != 0){
				//处理买赠这种情况
				Map<String,Object> priceMap = sellInProductPriceService.getProductPriceByPriceConfig(partnerId, orderLineVo.getSku(), 
						Long.valueOf(orderLineVo.getAmount()), new Date(),true);
				if(priceMap != null && priceMap.get("giftCount") != null){
					PartnerOrderLineVo orderLineGift = (PartnerOrderLineVo)priceMap.get("orderLineGift");
					orderlinePromotionList.add(orderLineGift);
				}
			}
		}*/
		partnerOrderList.addAll(orderlinePromotionList);
		return partnerOrderList;
	}
	
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> insertPartnerOrderMultiAddressSap(
//			PartnerOrderVo order, PartnerOrderLineVo[] orderLines,
//			WXTPartnerOrderAddressProductVo[] addressProducts) throws Exception {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try{
//			WxTUser curUser = ContextUtil.getCurUser();
//			if(curUser.getEmail() == null || StringUtils.isEmpty(curUser.getEmail())){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "当前用户没有保留邮件地址，无法收到订单相关信息，请更新邮件地址!");
//				return resultMap;
//			}
//			if(curUser.getMobileTel() == null || StringUtils.isEmpty(curUser.getMobileTel())){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "当前用户预留手机号码，无法收到订单相关信息，请更新手机号码!");
//				return resultMap;
//			}
//			/*PartnerBill partnerBill = partnerBillService.getPartnerBillInfoByPartnerId(order.getPartnerId());
//			if(partnerBill == null || partnerBill.getId() == null){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人没有初始化账单，请拨打4006686065确认!");
//				return resultMap;
//			}*/
//
//			for (PartnerOrderLineVo newOrderLineVo : orderLines) { // 原始的
//				if ("机油".equals(newOrderLineVo.getType())) {
//					newOrderLineVo.setType("JY");
//				} else if ("机油添加剂".equals(newOrderLineVo.getType())) {
//					newOrderLineVo.setType("YYY");
//				} else if ("礼盒".equals(newOrderLineVo.getType())) {
//					newOrderLineVo.setType("LH");
//				} else if ("其他".equals(newOrderLineVo.getType())) {
//					newOrderLineVo.setType("QT");
//				}
//				
//				newOrderLineVo.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已经确认
//			}
//			for (WXTPartnerOrderAddressProductVo newOrderAddreProductVo : addressProducts) { // 原始的
//				if ("机油".equals(newOrderAddreProductVo.getType())) {
//					newOrderAddreProductVo.setType("JY");
//				} else if ("机油添加剂".equals(newOrderAddreProductVo.getType())) {
//					newOrderAddreProductVo.setType("YYY");
//				} else if ("礼盒".equals(newOrderAddreProductVo.getType())) {
//					newOrderAddreProductVo.setType("LH");
//				} else if ("其他".equals(newOrderAddreProductVo.getType())) {
//					newOrderAddreProductVo.setType("QT");
//				}
//				newOrderAddreProductVo.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已经确认
//			}
//			//获取partner名称
//			Map<String, Object> params = new HashMap<String, Object>();
//	 		params.put("partnerId", order.getPartnerId());
//	 		
//	 		List<PartnerView> list = organizationVoMapper.selectPartnersByParams(params);
//	 	
//	 		if(list != null && !list.isEmpty()){
//	 			order.setPartnerName(list.get(0).getName());
//	 		}
//			
//			Map<String,Object> reqMap = new HashMap<String, Object>();
//			reqMap.put("morderNoType", OrderVo.ORDER_CODE_SP_OWNER);
//			String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.SP_SELF_ORDER,6,1);
//			order.setOrderNo(CommonUtil.generateOrderCode(6)+sequenceNo);
//			if(order.getStatus() == null ){
//				order.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已确认
//			}
//			// 插入订单
//			order.setOrderType(OrderVo.PA_ORDER_TYPE);
//			order.setSource(OrderVo.PA_ORDER_SOURCE);
//			order.setSourceId(OrderVo.PA_ORDER_SOURCE_ID);
//			order.setCreator(String.valueOf(ContextUtil.getCurUserId()));
//			
//			Date curDate = new Date();
//			order.setCreateTime(curDate);
//			order.setUpdateTime(curDate);
//			partnerOrderVoMapper.insertSelective(order);
//			
//			List<PartnerOrderLineVo> orderlineList = java.util.Arrays.asList(orderLines);
//			orderlineList = generatePartnerOrderlLineBySapPromotionCampaign(orderlineList);
//			Double totalLiterCount = 0D;
//			for (PartnerOrderLineVo newOrderLineVo : orderlineList) {  			
//				//统计订单机油合计升数
//				String capacity = newOrderLineVo.getCapacity();
//				if(!StringUtils.isEmpty(capacity) && (newOrderLineVo.getUnits() == null || newOrderLineVo.getUnits().equals("logistic"))){
//					totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getAmount());
//					if(newOrderLineVo.getFreeAmount() != null && newOrderLineVo.getFreeAmount() > 0){
//						totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getFreeAmount());
//					}
//				}else if(!StringUtils.isEmpty(capacity) && (newOrderLineVo.getUnits() == null || newOrderLineVo.getUnits().equals("box")
//						|| newOrderLineVo.getUnits().equals("barrel"))){
//					totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getAmount());
//					if(newOrderLineVo.getFreeAmount() != null && newOrderLineVo.getFreeAmount() > 0){
//						totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getFreeAmount());
//					}
//				}	
//				newOrderLineVo.setActualAmount(newOrderLineVo.getAmount());
//				newOrderLineVo.setDiscountedPrice(newOrderLineVo.getLadderPrice());
//				newOrderLineVo.setPartnerOrderId(order.getId());// 指定的订单id
//			}
//			order.setTotalLiterCount(totalLiterCount);
//
//			PartnerOrderVoExample orderExample = new PartnerOrderVoExample();
//			PartnerOrderVoExample.Criteria orderCriteria = orderExample.createCriteria();
//			orderCriteria.andIdEqualTo(order.getId());
//			partnerOrderVoMapper.updateByExampleSelective(order, orderExample);
//			
//			// 5.批量插入orderline记录 //进行分页
//			ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(orderlineList, 1);
//			int toltalPage = newOrderLinePage.getTotalPages();
//			for (int i = 1; i <= toltalPage; i++) {
//	
//				@SuppressWarnings("unchecked")
//				List<PartnerOrderLineVo> insertOrderLineLst = newOrderLinePage.getObjects(i);
//				partnerOrderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);
//	
//			}
//			
//			List<WXTPartnerOrderAddressProductVo> newOrderAddressProductVoLst = new ArrayList<WXTPartnerOrderAddressProductVo>();// 要插入的	
//			for (WXTPartnerOrderAddressProductVo newOrderAddressProcutVo : addressProducts) {  // 原始的
//				newOrderAddressProcutVo.setPartnerOrderId(order.getId());// 指定的订单id
//				newOrderAddressProcutVo.setDiscountFee(null);
//				newOrderAddressProcutVo.setTotalValue(null);
//				newOrderAddressProcutVo.setDiscountedTotalValue(null);
//				newOrderAddressProcutVo.setDiscountedPrice(null);
//				newOrderAddressProductVoLst.add(newOrderAddressProcutVo);
//				
//			}
//			// 6.批量插入地址商品表记录
//			ImportDataPageModelUtil newOrderAddressProductPage = new ImportDataPageModelUtil(newOrderAddressProductVoLst, 40);
//			int addressProductToltalPage = newOrderAddressProductPage.getTotalPages();
//			for (int i = 1; i <= addressProductToltalPage; i++) {
//	
//				@SuppressWarnings("unchecked")
//				List<WXTPartnerOrderAddressProductVo> insertOrderAddressLst = newOrderAddressProductPage.getObjects(i);
//				addressProductMapper.insertOrderAddressProductBatch(insertOrderAddressLst);
//	
//			}
//			
//			// 审核记录保存
//			ApprovalInfoVo record = new ApprovalInfoVo();
//			record.setCurrentStep(PartnerOrderVo.PARTNER_ORDER_STATUS_0);
//			record.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
//			record.setIsApproved(1);
//			record.setApprovalComment(order.getRemark());
//			record.setOperateTime(curDate);
//			record.setOperator(ContextUtil.getCurUserId());
//			record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
//			record.setSourceId(order.getId());
//			approvalInfoVoMapper.insert(record);
//			
//			//生成邮件发送的表格内容，需要自动计算价格
//			
//			if(order.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){//TODO
//				// 邮件发送
//                List<PartnerResponsibleVo> approverList = this.getsendEmailTo(order.getPartnerId());
//				if(approverList != null && !approverList.isEmpty()){
//					Set<String> approverEmailList = new HashSet<String>(approverList.size());
//					StringBuilder approverName = null;
//					Set<String> ccEmailList = new HashSet<String>();
//					for(PartnerResponsibleVo approver : approverList){
//						if(StringUtils.isBlank(approver.getResponsiblePersonEmail())) {
//							continue;
//						}
//						approverEmailList.add(approver.getResponsiblePersonEmail());
//						if(approverName == null) {
//							approverName = new StringBuilder();
//						}else {
//							approverName.append(" & ");
//						}
//						approverName.append(approver.getResponsiblePersonName());
//						approver.setPartnerId(order.getPartnerId());
//						String ccs;
//						final List<String> unfindCc = new ArrayList<String>(5);
//						try {
//							ccs = approver.getDayReportCc(Constants.SALES_CHANNEL_CDM, new ICallback<Object>() {
//
//								@Override
//								public void execute(Object source, Object... params) throws Exception {
//									unfindCc.add((String)params[0]);
//								}
//							});
//						} catch (RuntimeException e) {
//							log.error(e.getMessage(), e);
//							throw (RuntimeException)e;
//						} catch (Exception e) {
//							log.error(e.getMessage(), e);
//							throw new RuntimeException(e);
//						}
//						if(!unfindCc.isEmpty()) {
//							//微信告警未找到CC邮箱
//							WeixinMessageUtil.weixinAlarmMessagePush("采购订单", "采购订单", "下单告警", "未找到抄送邮箱", "订单【" + order.getOrderNo() + "】",
//									"经销商【" + order.getPartnerName() + "】下单未找到" + StringUtils.join(unfindCc, ",") + "邮箱");
//						}
//						if(StringUtils.isNotBlank(ccs)) {
//							for(String cc : ccs.split(",")) {
//								if(StringUtils.isNotBlank(cc)) {
//									ccEmailList.add(cc);
//								}
//							}
//						}
//					}
//					String[] accepters = new String[approverEmailList.size()];
//					accepters = approverEmailList.toArray(accepters);
//					String[] accepterCCs = new String[ccEmailList.size()];
//					accepterCCs = ccEmailList.toArray(accepterCCs);
//
//					order = partnerOrderVoMapper.selectByPrimaryKey(order.getId().toString());	
//					PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(order,true);
//					String orderChinesStatus = getOrderChineseStatusByStatusCode(order.getStatus());
//					String subjectName = orderChinesStatus + ",合伙人[" + order.getPartnerName() + "]采购订单:" + order.getOrderNo();
//					Map<String,Object> contentMap = new HashMap<String,Object>();
//					String acceptPersonName = "尊敬的" + approverName.toString();
//					String partnerName = "合伙人" + order.getPartnerName() + ",有订单需要确认价格,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
//					contentMap.put("acceptPersonName", acceptPersonName);
//					contentMap.put("partnerName", partnerName);
//					contentMap.put("skuList", partnerOrderView.getOlEmailList());
//					contentMap.put("partnerOrderVo", partnerOrderView);
//					List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
//					contentMap.put("approvalList", approvalList);
//					
//					String partnerProperty = partnerService.getPartnerProperty(order.getPartnerId());
//					if("KA".equals(partnerProperty)){
//						File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
//						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
//								accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
//					}else{
//						File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
//						emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
//								accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_info.ftl");
//					}
//				}else{
//					TransactionAspectSupport.currentTransactionStatus()
//					.setRollbackOnly();
//					resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
//					resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人没有配置价格录入人员，请联系销售BD确认!");
//					return resultMap;
//				}
//			}
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		}catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
//			log.error("PartnerOrderServiceImpl insertPartnerOrderMultiAddress e:{}", e.getLocalizedMessage());
//			throw e;
//		}
//		
//		return resultMap;
//	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertPartnerOrder(
			PartnerOrderVo order, List<PartnerOrderLineVo> orderLines) throws Exception {
		ResponseMap resultMap = new ResponseMap();
		try{
			WxTUser curUser = ContextUtil.getCurUser();
			//1. 验证下单人的邮箱和手机号信息
			if(curUser.getEmail() == null || StringUtils.isEmpty(curUser.getEmail())){
				throw new WxPltException("当前用户没有保留邮件地址，无法收到订单相关信息，请更新邮件地址!");
			}
			if(curUser.getMobileTel() == null || StringUtils.isEmpty(curUser.getMobileTel())){
				throw new WxPltException("当前用户预留手机号码，无法收到订单相关信息，请更新手机号码!");
			}
			//2. 非暂存订单（提交订单）验证下单主数据中的Paryment Term和详细地址信息（详细地址为空的数据可能是信息不完整下单主数据，需要完善）
			if(!PartnerOrderVo.PARTNER_ORDER_STATUS_0.equals(order.getStatus())) {
				WxTPartnerSaleConfig partnerSaleConfig = getPartnerSaleConfig(order.getPartnerSaleConfigId(), null);
				if(StringUtils.isBlank(partnerSaleConfig.getPaymentTerm())) {
					throw new WxPltException("下单配置中Paryment Term为空。请联系雪佛龙销售！");
				}
				if(StringUtils.isBlank(partnerSaleConfig.getAddress())) {
					throw new WxPltException("下单配置中详细地址为空。请联系雪佛龙销售！");
				}
				order.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已确认
			}

			//3. 保存订单信息
			Date curDate = new Date();
			order.setUpdateTime(curDate);
			order.setVersionNo(PartnerOrderVo.VERSION_NO202107);
			if(order.getId() != null) {
				PartnerOrderVoExample orderExample = new PartnerOrderVoExample();
				PartnerOrderVoExample.Criteria orderCriteria = orderExample.createCriteria();
				orderCriteria.andIdEqualTo(order.getId());
				partnerOrderVoMapper.updateByExampleSelective(order, orderExample);
				//  删除以前的orderLine
				PartnerOrderLineVoExample partnerOrderLineVoExample = new PartnerOrderLineVoExample();
				partnerOrderLineVoExample.createCriteria().andPartnerOrderIdEqualTo(order.getId());
				partnerOrderLineVoMapper.deleteByExample(partnerOrderLineVoExample);
			}else {
				String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.SP_SELF_ORDER,6,1);
				order.setOrderNo(CommonUtil.generateOrderCode(6)+sequenceNo);
				order.setOrderType(OrderVo.PA_ORDER_TYPE); //历史有销售订单转采购订单
				order.setSource(OrderVo.PA_ORDER_SOURCE);
				order.setSourceId(OrderVo.PA_ORDER_SOURCE_ID);
				order.setCreateTime(curDate);
				order.setCreator(curUser.getUserId().toString());
				partnerOrderVoMapper.insertSelective(order);
			}

			//4. 更新订单明细数据
			for (PartnerOrderLineVo newOrderLineVo : orderLines) { // 原始的
				newOrderLineVo.setStatus(order.getStatus());
				newOrderLineVo.setCreateTime(curDate);
				newOrderLineVo.setUpdateTime(curDate);
				newOrderLineVo.setPartnerOrderId(order.getId());
			}

			//5. 分批次批量插入orderline记录 //进行分页
			ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(orderLines, 80);
			int toltalPage = newOrderLinePage.getTotalPages();
			for (int i = 1; i <= toltalPage; i++) {

				@SuppressWarnings("unchecked")
				List<PartnerOrderLineVo> insertOrderLineLst = newOrderLinePage.getObjects(i);
				partnerOrderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);

			}
			resultMap.put("id", order.getId());
			//6. 暂存记录结束
			if(PartnerOrderVo.PARTNER_ORDER_STATUS_0.equals(order.getStatus())) {
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
				return resultMap;
			}

			//7. 审核记录保存
			ApprovalInfoVo record = new ApprovalInfoVo();
			record.setCurrentStep(PartnerOrderVo.PARTNER_ORDER_STATUS_0);
			record.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
			record.setIsApproved(1);
			record.setApprovalComment(order.getRemark());
			record.setOperateTime(curDate);
			record.setOperator(ContextUtil.getCurUserId());
			record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
			record.setSourceId(order.getId());
			approvalInfoVoMapper.insert(record);

			//8. 生成邮件发送的表格内容，需要自动计算价格
			if(order.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
				sendOrderEmail(order.getId());
			}
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		}catch (WxPltException e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.handleException(e, this, ContextUtil.getCurUserId(),
					"com.chevron.partnerorder.service.impl.PartnerOrderServiceImpl.insertPartnerOrder", JsonUtil.writeValue(order));
//			throw e;
		}
//
		return resultMap;
	}
	
	public Map<String, Object> sendOrderEmail(Long orderId) throws Exception {
		PartnerOrderVo order = partnerOrderVoMapper.selectByPrimaryKey(orderId.toString());
		String regionName = getOrderRegionName(order);
		//工业油Industrial只发送给工业油CSR(注意大小写)
		Boolean industrialOrderFlag = regionName.indexOf(Constants.SALES_CHANNEL_DIRECT_INDUSTRIAL) >= 0;

		//1. 获取经销商采购订单配置CSR信息
        List<PartnerResponsibleVo> approverList = this.getsendEmailTo(order.getPartnerId(),industrialOrderFlag);
		if(approverList != null && !approverList.isEmpty()){
			Set<String> approverEmailList = new HashSet<String>(approverList.size());
			StringBuilder approverName = null;
			Set<String> ccEmailList = new HashSet<String>();
			for(PartnerResponsibleVo approver : approverList){
				if(StringUtils.isBlank(approver.getResponsiblePersonEmail())) {
					continue;
				}
				//2.1 添加邮件接收CSR
				approverEmailList.add(approver.getResponsiblePersonEmail());
				if(approverName == null) {
					approverName = new StringBuilder();
				}else {
					approverName.append(" & ");
				}
				approverName.append(approver.getResponsiblePersonName());
				approver.setPartnerId(order.getPartnerId());
				String ccs;
				//2.2 添加CSR配置信息中的抄送用户（FLSR和ASM）
				final List<String> unfindCc = new ArrayList<String>(5);
				try {
					String salesChannel = Constants.SALES_CHANNEL_INDIRECT;
					if(industrialOrderFlag){
						salesChannel =  Constants.SALES_CHANNEL_DIRECT_INDUSTRIAL;
					}
					ccs = approver.getDayReportCc(salesChannel, new ICallback<Object>() {

						@Override
						public void execute(Object source, Object... params) throws Exception {
							unfindCc.add((String)params[0]);
						}
					});
				} catch (RuntimeException e) {
					log.error(e.getMessage(), e);
					throw (RuntimeException)e;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					throw new RuntimeException(e);
				}
				//2.3 抄送FLSR和ASM未找到时，微信告警
				if(!unfindCc.isEmpty()) {
					//微信告警未找到CC邮箱
					WeixinMessageUtil.weixinAlarmMessagePush("采购订单", "采购订单", "下单告警", "未找到抄送邮箱", "订单【" + order.getOrderNo() + "】",
							"经销商【" + order.getPartnerName() + "】下单未找到" + StringUtils.join(unfindCc, ",") + "邮箱");
				}
				if(StringUtils.isNotBlank(ccs)) {
					for(String cc : ccs.split(",")) {
						if(StringUtils.isNotBlank(cc)) {
							ccEmailList.add(cc);
						}
					}
				}
			}
			String[] accepters = new String[approverEmailList.size()];
			accepters = approverEmailList.toArray(accepters);
			String[] accepterCCs = new String[ccEmailList.size()];
			accepterCCs = ccEmailList.toArray(accepterCCs);

			//3. 组装Excel附件信息
			PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(order,true);
			//4. 组装邮件信息
			String orderChinesStatus = getOrderChineseStatusByStatusCode(order.getStatus());
			String subjectName = orderChinesStatus + ",合伙人[" + order.getPartnerName() + "]采购"+regionName+"订单:" + order.getOrderNo();
			Map<String,Object> contentMap = new HashMap<String,Object>();
			String acceptPersonName = "尊敬的" + approverName.toString();
			String partnerName = "合伙人" + order.getPartnerName() + ",有"+regionName+"订单需要确认价格,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
			contentMap.put("acceptPersonName", acceptPersonName);
			contentMap.put("partnerName", partnerName);
			contentMap.put("skuList", partnerOrderView.getOlEmailList());
			contentMap.put("partnerOrderVo", partnerOrderView);
			List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
			contentMap.put("approvalList", approvalList);
			int i = 0;
			for(PartnerOrderApprovalHistoryVo historyVo : approvalList) {
				if(PartnerOrderVo.PARTNER_ORDER_STATUS_1.equals(historyVo.getApprovalFlowStep())) {
					i++;
				}
			}
			subjectName += "(第" + i + "次提交)";
			//5. 发送邮件及Excel附件
				//工业油:邮箱以及Excel内容不需要显示“收货模式”
			partnerOrderView.setIndustrialOrderFlag(industrialOrderFlag);
			if(industrialOrderFlag){
				partnerOrderView.setReceiveTypeTextShowStyle("style=\"display:none\"");
			}
			String partnerProperty = partnerService.getPartnerProperty(order.getPartnerId());
			if("KA".equals(partnerProperty)){
				File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
				emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
						accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
			}else{
				File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
				emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
						accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_info.ftl");
			}
		}else{
			throw new WxPltException("合伙人没有配置价格录入人员，请联系销售BD确认!");
		}
		return null;
	}


	/**
	 *
	*    //获取partner信息补齐相关信息【工业油需求2023.12："Chevron China_PP Roll-out Planning for Industrial Oil V5.5.pptx"】
	 *     //备选方式：partnerSaleConfig/queryPartnerSaleConfig.do根据返回数组匹配订单【这个无法获取】
	 *    //备选方式：partnerSaleConfig/queryPartnerSaleConfig.do根据返回数组属性partnerId和订单partnerSaleConfigId匹配【这个无法OK】
	 * @param order
	 * @return
	 */
	private String getOrderRegionName(PartnerOrderVo order) {
		String regionName = "";
		PartnerSaleConfigConditions partnerSaleConfigConditions = new PartnerSaleConfigConditions();
//		start=0&limit=2&activeFlag=1&field=creationTime&direction=desc&queryField=&queryType=2&pageIndex=0&partnerName=&partnerId=56491&paging=false
		partnerSaleConfigConditions.setStart(0);
		partnerSaleConfigConditions.setLimit(2);
		partnerSaleConfigConditions.setActiveFlag(1);
		partnerSaleConfigConditions.setField("creationTime");
		partnerSaleConfigConditions.setDirection("desc");
		partnerSaleConfigConditions.setQueryType(2);
		partnerSaleConfigConditions.setPaging(false);
		partnerSaleConfigConditions.setIsOpernCustomMybatisInterceptor("1");

		Map<String,Object> mapPartnerList = partnerSaleConfigService.getPartnerSaleConfigByConditions(partnerSaleConfigConditions);
		List<WxTPartnerSaleConfig> wxTPartnerSaleConfigList = (List<WxTPartnerSaleConfig>) mapPartnerList.get(WxTPartnerSaleConfigServiceImpl.RESULT_LST_KEY);
		if(CollectionUtil.isNotEmpty(wxTPartnerSaleConfigList)) {
			List<WxTPartnerSaleConfig> findPartnerList = wxTPartnerSaleConfigList.stream().filter(x -> order.getPartnerSaleConfigId().equals(x.getId())).collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(findPartnerList)) {
				regionName = findPartnerList.get(0).getRegionName();
			}
		}
		return regionName;
	}

//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> resubmitPartnerOrderMultiAddressSap(PartnerOrderVo order, PartnerOrderLineVo[] orderLines,
//			WXTPartnerOrderAddressProductVo[] addressProducts)
//			throws Exception {
//		
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		
//		PartnerOrderVo orderExisted = partnerOrderVoMapper.selectByPrimaryKey(order.getId().toString());
//		
//		PartnerBill partnerBill = partnerBillService.getPartnerBillInfoByPartnerId(orderExisted.getPartnerId());
//		if(partnerBill == null || partnerBill.getId() == null){
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人没有初始化账单，请拨打4006686065确认!");
//			return resultMap;
//		}
//		// 解析orderLines
//		for (PartnerOrderLineVo newOrderLineVo : orderLines) { // 原始的
//			if ("机油".equals(newOrderLineVo.getType())) {
//				newOrderLineVo.setType("JY");
//			} else if ("机油添加剂".equals(newOrderLineVo.getType())) {
//				newOrderLineVo.setType("YYY");
//			} else if ("礼盒".equals(newOrderLineVo.getType())) {
//				newOrderLineVo.setType("LH");
//			} else if ("其他".equals(newOrderLineVo.getType())) {
//				newOrderLineVo.setType("QT");
//			}
//			
//			newOrderLineVo.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已经确认
//		}
//		for (WXTPartnerOrderAddressProductVo newOrderAddreProductVo : addressProducts) { // 原始的
//			if ("机油".equals(newOrderAddreProductVo.getType())) {
//				newOrderAddreProductVo.setType("JY");
//			} else if ("机油添加剂".equals(newOrderAddreProductVo.getType())) {
//				newOrderAddreProductVo.setType("YYY");
//			} else if ("礼盒".equals(newOrderAddreProductVo.getType())) {
//				newOrderAddreProductVo.setType("LH");
//			} else if ("其他".equals(newOrderAddreProductVo.getType())) {
//				newOrderAddreProductVo.setType("QT");
//			}
//			newOrderAddreProductVo.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已经确认
//		}
//		//获取partner名称
//		Date curDate = new Date();
//		
//		order.setStatus(PartnerOrderVo.PARTNER_ORDER_STATUS_1);//已确认
//		order.setUpdateTime(curDate);
//			
//		List<PartnerOrderLineVo> orderlineList = java.util.Arrays.asList(orderLines);
//		orderlineList = generatePartnerOrderlLineBySapPromotionCampaign(orderlineList);
//		Double totalLiterCount = 0D;
//		for (PartnerOrderLineVo newOrderLineVo : orderlineList) {  			
//			//统计订单机油合计升数
//			String capacity = newOrderLineVo.getCapacity();
//			if(!StringUtils.isEmpty(capacity) && (newOrderLineVo.getUnits() == null || newOrderLineVo.getUnits().equals("logistic"))){
//				totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getAmount());
//				if(newOrderLineVo.getFreeAmount() != null && newOrderLineVo.getFreeAmount() > 0){
//					totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getFreeAmount());
//				}
//			}else if(!StringUtils.isEmpty(capacity) && (newOrderLineVo.getUnits() == null || newOrderLineVo.getUnits().equals("box")
//					|| newOrderLineVo.getUnits().equals("barrel"))){
//				totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getAmount());
//				if(newOrderLineVo.getFreeAmount() != null && newOrderLineVo.getFreeAmount() > 0){
//					totalLiterCount = totalLiterCount + Double.valueOf(capacity) * Integer.valueOf(newOrderLineVo.getFreeAmount());
//				}
//			}	
//			newOrderLineVo.setActualAmount(newOrderLineVo.getAmount());
//			newOrderLineVo.setDiscountedPrice(newOrderLineVo.getLadderPrice());
//			newOrderLineVo.setPartnerOrderId(order.getId());// 指定的订单id
//		}
//		order.setTotalLiterCount(totalLiterCount);
//		
//		PartnerOrderVoExample orderExample = new PartnerOrderVoExample();
//		PartnerOrderVoExample.Criteria orderCriteria = orderExample.createCriteria();
//		orderCriteria.andIdEqualTo(order.getId());
//		partnerOrderVoMapper.updateByExampleSelective(order, orderExample);
//		order = partnerOrderVoMapper.selectByPrimaryKey(order.getId().toString());
//		
//		//  删除以前的orderLine
//		PartnerOrderLineVoExample partnerOrderLineVoExample = new PartnerOrderLineVoExample();
//		partnerOrderLineVoExample.createCriteria().andPartnerOrderIdEqualTo(order.getId());
//		partnerOrderLineVoMapper.deleteByExample(partnerOrderLineVoExample);
//		// 5.批量插入orderline记录 //进行分页
//		ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(orderlineList, 1);
//		int toltalPage = newOrderLinePage.getTotalPages();
//		for (int i = 1; i <= toltalPage; i++) {
//
//			@SuppressWarnings("unchecked")
//			List<PartnerOrderLineVo> insertOrderLineLst = newOrderLinePage.getObjects(i);
//			partnerOrderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);
//		}
//		
//		List<WXTPartnerOrderAddressProductVo> newOrderAddressProductVoLst = new ArrayList<WXTPartnerOrderAddressProductVo>();// 要插入的
//		
//		for (WXTPartnerOrderAddressProductVo newOrderAddressProcutVo : addressProducts) {  // 原始的
//			newOrderAddressProcutVo.setPartnerOrderId(order.getId());// 指定的订单id
//			newOrderAddressProcutVo.setDiscountFee(null);
//			newOrderAddressProcutVo.setTotalValue(null);
//			newOrderAddressProcutVo.setDiscountedTotalValue(null);
//			newOrderAddressProcutVo.setDiscountedPrice(null);
//			newOrderAddressProductVoLst.add(newOrderAddressProcutVo);
//			
//		}
//		// 删除以前的地址商品表记录
//		WXTPartnerOrderAddressProductVoExample addressProductVoExample = new WXTPartnerOrderAddressProductVoExample();
//		addressProductVoExample.createCriteria().andPartnerOrderIdEqualTo(order.getId());
//		addressProductMapper.deleteByExample(addressProductVoExample);
//		// 6.批量插入地址商品表记录
//		ImportDataPageModelUtil newOrderAddressProductPage = new ImportDataPageModelUtil(newOrderAddressProductVoLst, 40);
//		int addressProductToltalPage = newOrderAddressProductPage.getTotalPages();
//		for (int i = 1; i <= addressProductToltalPage; i++) {
//
//			@SuppressWarnings("unchecked")
//			List<WXTPartnerOrderAddressProductVo> insertOrderAddressLst = newOrderAddressProductPage.getObjects(i);
//			addressProductMapper.insertOrderAddressProductBatch(insertOrderAddressLst);
//
//		}
//		// 审核记录保存
//		ApprovalInfoVo record = new ApprovalInfoVo();
//		record.setCurrentStep(PartnerOrderVo.PARTNER_ORDER_STATUS_4);
//		record.setFlowStep(PartnerOrderVo.PARTNER_ORDER_STATUS_1);
//		record.setIsApproved(1);
//		record.setApprovalComment(order.getRemark());
//		record.setOperateTime(curDate);
//		record.setOperator(ContextUtil.getCurUserId());
//		record.setSourceFlag(ApprovalInfoVo.PARTNER_ORDER_SOURCE_FLAG);
//		record.setSourceId(order.getId());
//		approvalInfoVoMapper.insert(record);
//		
//		//邮件发送
//		if(order.getStatus().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//            List<PartnerResponsibleVo> approverList = this.getsendEmailTo(order.getPartnerId());
//            String approverId = "";
//			if(approverList != null && !approverList.isEmpty()){
//				Set<String> approverEmailList = new HashSet<String>(approverList.size());
//				StringBuilder approverName = null;
//				Set<String> ccEmailList = new HashSet<String>();
//				for(PartnerResponsibleVo approver : approverList){
//					if(StringUtils.isBlank(approver.getResponsiblePersonEmail())) {
//						continue;
//					}
//					approverId = approver.getResponsiblePersonId().toString();
//					approverEmailList.add(approver.getResponsiblePersonEmail());
//					if(approverName == null) {
//						approverName = new StringBuilder();
//					}else {
//						approverName.append(" & ");
//					}
//					approverName.append(approver.getResponsiblePersonName());
//					approver.setPartnerId(order.getPartnerId());
//					String ccs;
//					try {
//						ccs = approver.getDayReportCc(Constants.SALES_CHANNEL_CDM);
//					} catch (RuntimeException e) {
//						log.error(e.getMessage(), e);
//						throw (RuntimeException)e;
//					} catch (Exception e) {
//						log.error(e.getMessage(), e);
//						throw new RuntimeException(e);
//					}
//					if(StringUtils.isNotBlank(ccs)) {
//						for(String cc : ccs.split(",")) {
//							if(StringUtils.isNotBlank(cc)) {
//								ccEmailList.add(cc);
//							}
//						}
//					}
//				}
//				String[] accepters = new String[approverEmailList.size()];
//				accepters = approverEmailList.toArray(accepters);
//				String[] accepterCCs = new String[ccEmailList.size()];
//				accepterCCs = ccEmailList.toArray(accepterCCs);
//				
//				order = partnerOrderVoMapper.selectByPrimaryKey(order.getId().toString());	
//				PartnerOrderPdfView  partnerOrderView = generatePartnerOrderViewForPdfExcel(order,true);
//				List<PartnerOrderApprovalHistoryVo> approvalList = partnerOrderView.getApprovalList();
//				int submitTimes = 0;
//				for(PartnerOrderApprovalHistoryVo partnerOrderApprovalHistoryVo : approvalList){
//					if(partnerOrderApprovalHistoryVo.getApprovalFlowStep().equals(PartnerOrderVo.PARTNER_ORDER_STATUS_1)){
//						submitTimes = submitTimes + 1;
//					}
//				}
//				
//				String orderChinesStatus = getOrderChineseStatusByStatusCode(order.getStatus());
//				String subjectName = orderChinesStatus + ",合伙人[" + order.getPartnerName() + "]采购订单:" + order.getOrderNo() + " 第" + submitTimes + "次提交";
//				Map<String,Object> contentMap = new HashMap<String,Object>();
//				String acceptPersonName = "尊敬的" + approverName;
//				String partnerName = "合伙人" + order.getPartnerName() + ",有订单需要确认价格,您需要到雪佛龙合伙人后台做进一步处理,订单详情如下：";
//				contentMap.put("acceptPersonName", acceptPersonName);
//				contentMap.put("partnerName", partnerName);
//				contentMap.put("skuList", partnerOrderView.getOlEmailList());
//				contentMap.put("partnerOrderVo", partnerOrderView);
//				contentMap.put("approvalList",approvalList);
//				
//				String partnerProperty = partnerService.getPartnerProperty(order.getPartnerId());
//				if("KA".equals(partnerProperty)){
//					File[] attFiles = generateKapartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
//					
//					if (null != RequestContextHolder.getRequestAttributes()) {
//						HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
//						String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
//						contentMap.put("approvalPageLink", basePath + TokenUtil.generateTokenLink(approverId, "csrConfirmApproval",
//								"/business/partnerorder/partnerOrderManage.jsp", "/business/partnerorder/csrConfirm.jsp?partnerOrderId=" + order.getId(), false,String.valueOf(curDate.getTime())));
//						contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmApprovalDirectly",
//								"", "",true, String.valueOf(order.getId()),String.valueOf(curDate.getTime())));
//						contentMap.put("rejectPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmRejectDirectly",
//								"", "",true, String.valueOf(order.getId()),String.valueOf(curDate.getTime())));
//					}
//					
//					emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
//							accepters, accepterCCs, subjectName, contentMap, attFiles, "ka_partner_order_info.ftl");
//				}else{
//					File[] attFiles = generatePartnerOrderExcel(partnerOrderView,true,HttpSessionGets.getSession().getServletContext());
//					
//					if (null != RequestContextHolder.getRequestAttributes()) {
//						HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
//						String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
//						contentMap.put("approvalPageLink", basePath + TokenUtil.generateTokenLink(approverId, "csrConfirmApproval",
//								"/business/partnerorder/partnerOrderManage.jsp", "/business/partnerorder/csrConfirm.jsp?partnerOrderId=" + order.getId(), false,String.valueOf(curDate.getTime())));
//						contentMap.put("approvalPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmApprovalDirectly",
//								"", "",true, String.valueOf(order.getId()),String.valueOf(curDate.getTime())));
//						contentMap.put("rejectPageButton", basePath + TokenUtil.generateTokenLink(approverId, "poCsrConfirmRejectDirectly",
//								"", "",true, String.valueOf(order.getId()),String.valueOf(curDate.getTime())));
//					}
//					
//					emailSenderService.sendEmailForParterOrder(HttpSessionGets.getSession().getServletContext(),
//							accepters, accepterCCs, subjectName, contentMap, attFiles, "partner_order_info.ftl");
//				}
//			}else{
//				TransactionAspectSupport.currentTransactionStatus()
//				.setRollbackOnly();
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.FAIL);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "合伙人没有配置价格录入人员，请联系销售BD确认!");
//				return resultMap;
//			}
//		}
//		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		
//		return resultMap;
//	}
	
	private List<String> getCsrCCEmailListByPartnerId(Long partnerId){
		List<String> ccEmailList = new ArrayList<String>();
		PartnerO2OEnterpriseExample example = new PartnerO2OEnterpriseExample();
		example.createCriteria().andPartnerIdEqualTo(partnerId);
		List<PartnerO2OEnterprise> resultList = partnerO2OEnterpriseMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			Long distributorId = resultList.get(0).getDistributorId();
			Map<String,Object> praMap = new HashMap<String,Object>();
			praMap.put("distributorId", distributorId);
			praMap.put("salesChannelName", Constants.SALES_CHANNEL_CDM);
			List<DwCustomerRegionSalesSupervisorRel> dsrssrList =  dcrssrMapper.getCustomerInfoByPra(praMap);
			if(dsrssrList != null && dsrssrList.size() > 0){
				for(DwCustomerRegionSalesSupervisorRel dsrssr : dsrssrList){
					String saleCAI =  dsrssr.getSalesCai();
					List<WxTUser> saleUserList =  userMapper.selectUserByCai(saleCAI);
					if(saleUserList != null && saleUserList.size() > 0){
						WxTUser saleUser = saleUserList.get(0);
						if(saleUser.getStatus() == 1){
							ccEmailList.add(saleUser.getEmail());
						}
					}
					String saleManagerCAI  = dsrssr.getSuppervisorCai();
					List<WxTUser> saleManagerUserList =  userMapper.selectUserByCai(saleManagerCAI);
					if(saleManagerUserList != null && saleManagerUserList.size() > 0){
						WxTUser saleUser = saleManagerUserList.get(0);
						if(saleUser.getStatus() == 1){
							ccEmailList.add(saleUser.getEmail());
						}
					}
				}
			}
		}		
		return ccEmailList;
	}
	private Map<String,String> getCsrEmailMap(){
		Map<String,String> map = new HashMap<String,String>();
		DicItemVoExample dicItemExample = new DicItemVoExample();
		dicItemExample.createCriteria().andDicTypeCodeEqualTo("csr_userid_email");		
		List<DicItemVo> dicItemList = dicItemVoMapper.selectByExample(dicItemExample);
		if(dicItemList != null && !dicItemList.isEmpty()){
			for(DicItemVo dicItem : dicItemList){
				map.put(dicItem.getDicItemCode(), dicItem.getDicItemName());
			}			
		}
		return map;
	}
}
