package com.chevron.core.conf.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.core.conf.model.PartnerProductPriceViewQueryParams;
import com.chevron.core.conf.service.PartnerProductPriceService;
import com.common.util.MessageResourceUtil;

@Controller
@RequestMapping(value="/productprice")
public class PartnerProductPriceController {
	
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	
	@Resource
	PartnerProductPriceService partnerProductPriceImpl;
	
	private final static Logger log = Logger.getLogger(PartnerProductPriceController.class);

	/**
	 * 产品价格规则查询
	 */
	@ResponseBody
	@RequestMapping(value="/queryProductPrice.do", method = {RequestMethod.POST})
	public Map<String,Object> queryCGOrders(PartnerProductPriceViewQueryParams paraConditions,HttpServletRequest request)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			resultMap = partnerProductPriceImpl.selectPartnerProductPriceConf(paraConditions);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	

}
