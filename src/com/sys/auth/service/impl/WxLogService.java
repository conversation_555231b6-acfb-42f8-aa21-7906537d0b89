package com.sys.auth.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sys.auth.dao.WxTLogMapper;
import com.sys.auth.dao.WxTMenuMapper;
import com.sys.auth.dao.WxTOrgMapper;
import com.sys.auth.dao.WxTPlogMapper;
import com.sys.auth.dao.WxTRoleMapper;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.LogEntity;
import com.sys.auth.model.WxTLog;
import com.sys.auth.model.WxTMenu;
import com.sys.auth.model.WxTOrg;
import com.sys.auth.model.WxTPlog;
import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.WxLogServiceI;

@Service
public class WxLogService implements WxLogServiceI{

	@Resource
	WxTLogMapper wxTLogMapper;
	
	@Resource
	WxTPlogMapper wxTPlogMapper;
	
	@Resource
	WxTUserMapper wxTUserMapper;
	
	@Resource
	WxTRoleMapper wxTRoleMapper;
	
	@Resource
	WxTOrgMapper wxTOrgMapper;
	
	@Resource
	WxTMenuMapper wxTMenuMapper;
	
	public int crearLog(String logType,String sessionId,Long userId,Long tenantId){
		int n = 0 ;
		try {
			WxTLog log = new WxTLog();
			log.setLogType(Integer.parseInt(logType));
			log.setSessionId(sessionId);
			log.setStatus(1);
//			log.setTenantId(new Long(tenantId));
			log.setUserId(new Long(userId));
			log.setXgSj(new Date());
			n = wxTLogMapper.insertSelective(log);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return n;
	}
	
	public Map<String,Object> selLogByTime(String type,String timeStr) throws Exception{
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm");
			List<LogEntity> resultList = new ArrayList<LogEntity>();
			if("1".equals(type)){//登录日志
				WxTLog record = new WxTLog();
				record.setLogDescript(timeStr);
				List<WxTLog> logList = wxTLogMapper.selectByTime(record);
				if(logList.size() > 0){
					for(int i = 0 ; i < logList.size() ; i++){
						LogEntity loEntity = new LogEntity();
						WxTLog log = logList.get(i);
						String tempStr = "登出";
						if(1 == log.getLogType()){
							tempStr = "登录";
						}
						loEntity.setTime(sdf.format(log.getXgSj()));
						loEntity.setDescript("用户[<font color='blue'>" + log.getObjName() + "</font>]" + tempStr);
						resultList.add(loEntity);
					}
				}else{
					LogEntity loEntity = new LogEntity();
					loEntity.setTime(sdf.format(new Date()));
					loEntity.setDescript("暂无记录");
					resultList.add(loEntity);
				}
			}else{//权限操作日志
				WxTPlog record = new WxTPlog();
				record.setPlogDescript(timeStr);
				List<WxTPlog> plogList = wxTPlogMapper.selectByTime(record);
				if(plogList.size() > 0){
					for(int i = 0 ; i < plogList.size() ; i++){
						LogEntity loEntity = new LogEntity();
						WxTPlog log = plogList.get(i);
						String objType = "角色";
						String objName = "";//被操作对象名称
						//操作动作  - 授予 or 移除
						String perType = "授予";
						if(2 == log.getPlogPerType()){
							perType = "移除";
						}
						//判断操作对象
						if(1 == log.getPlogObjType()){//被操作对象为 用户
							objType = "用户";
							//查询用户名
							WxTUser objUser = wxTUserMapper.selectByPrimaryKey(log.getPlogObjId());
							objName = objUser.getChName();
						}else{//被操作对象为 角色
							WxTRole objRole = wxTRoleMapper.selectByPrimaryKey(log.getPlogObjId());
							objName = objRole.getChRoleName();
						}
						//判断被赋权资源类型:
						String sourceType = "";
						String sourceName = "";
						if(8 == log.getPlogSourceType()){
							WxTRole objRole = wxTRoleMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "通讯录角色";
							sourceName = objRole.getChRoleName();
						}else if(9 == log.getPlogSourceType()){
							WxTRole objRole = wxTRoleMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "菜单角色";
							sourceName = objRole.getChRoleName();
						}else if(10 == log.getPlogSourceType()){
							WxTRole objRole = wxTRoleMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "组织角色";
							sourceName = objRole.getChRoleName();
						}else if(11 == log.getPlogSourceType()){
							WxTRole objRole = wxTRoleMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "预置角色";
							sourceName = objRole.getChRoleName();
						}else if(1 == log.getPlogSourceType()){
							WxTOrg org = wxTOrgMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "通讯录资源";
							sourceName = org.getOrgName();
						}else if(2 == log.getPlogSourceType()){
							WxTMenu menu = wxTMenuMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "菜单资源";
							sourceName = menu.getMenuName();
						}else if(4 == log.getPlogSourceType()){
							WxTOrg org = wxTOrgMapper.selectByPrimaryKey(log.getPlogSourceid());
							sourceType = "组织资源";
							sourceName = org.getOrgName();
						}
						WxTUser czUser = wxTUserMapper.selectByPrimaryKey(log.getXgUser());
						String descript = "用户[<font color='blue'>" + czUser.getChName() + "</font>]" + perType + objType + 
										  "[<font color='blue'>" + objName + "</font>]" + sourceType + ":<font color='green'>" + sourceName + "</font>";
						loEntity.setTime(sdf.format(log.getXgSj()));
						loEntity.setDescript(descript);
						resultList.add(loEntity);
					}
				}else{
					LogEntity loEntity = new LogEntity();
					loEntity.setTime(sdf.format(new Date()));
					loEntity.setDescript("暂无记录");
					resultList.add(loEntity);
				}
			}
			map.put("code", "success");
			map.put("resultList", resultList);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}
}
