/*==============================================================*/
/* Module: BUDGET2021                                           */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_budget2021                                       */
/*==============================================================*/
create table wx_t_budget2021 (
	id                               bigint                    not null identity(true,1),
	budget_year                      int                                        not null,
	brand                            int                                        not null,
	budget_type                      int                                        not null,
	budget_partner_key               nvarchar(32)                                   null,
	budget_key                       nvarchar(32)                               not null,
	budget_titile                    nvarchar(64)                                   null,
	channel_weight                   int                                        not null,
	region_budget_value              decimal(20,2)                                  null,
	mkt_budget_value                 decimal(20,2)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_budget2021 ADD CONSTRAINT uk_budget2021 UNIQUE (budget_year,brand,budget_type,budget_key);

/*==============================================================*/
/* Table: wx_t_budget2021_log                                   */
/*==============================================================*/
create table wx_t_budget2021_log (
	id                               bigint                    not null identity(true,1),
	log_type                         int                                        not null,
	budget_year                      int                                        not null,
	budget_type                      int                                        not null,
	brand                            int                                        not null,
	budget_key                       nvarchar(32)                               not null,
	offset                           decimal(20,2)                              not null,
	final_value                      decimal(20,2)                              not null,
	value_type                       int                                            null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	comments                         nvarchar(1024)                                 null,
	primary key (id)
);
CREATE INDEX ix_budget2021_log_budget_key ON wx_t_budget2021_log (budget_key);

/*==============================================================*/
/* Table: wx_t_budget2021_expense_import                        */
/*==============================================================*/
create table wx_t_budget2021_expense_import (
	id                               bigint                    not null identity(true,1),
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	expense_year                     int                                        not null,
	expense_month                    int                                        not null,
	brand                            int                                        not null,
	region_name                      nvarchar(64)                               not null,
	distributor_id                   bigint                                     not null,
	sap_code                         nvarchar(64)                               not null,
	effective_flag                   tinyint                                    not null,
	delete_flag                      tinyint                                    not null,
	expense_main_code                int                                        not null,
	expense_code                     nvarchar(100)                              not null,
	pay_value                        decimal(20,6)                                  null,
	remark                           nvarchar(64)                                   null,
	input_user                       bigint                                         null,
	input_time                       datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_budget2021_expense_type                          */
/*==============================================================*/
create table wx_t_budget2021_expense_type (
	id                               bigint                    not null identity(true,1),
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	brand                            int                                        not null,
	expense_main_type                nvarchar(64)                               not null,
	expense_main_code                int                                        not null,
	expense_type                     nvarchar(100)                              not null,
	expense_code                     nvarchar(100)                              not null,
	if_workflow                      int                                            null,
	if_import                        int                                            null,
	delete_flag                      tinyint                                    not null,
	if_expense                       int                                            null,
	if_point                         int                                            null,
	if_system                        int                                            null,
	ext_property1                    nvarchar(64)                                   null,
	ext_property2                    nvarchar(64)                                   null,
	ext_property3                    nvarchar(64)                                   null,
	primary key (id)
);
ALTER TABLE wx_t_budget2021_expense_type ADD CONSTRAINT uk_budget2021_expense_type UNIQUE (expense_code);
