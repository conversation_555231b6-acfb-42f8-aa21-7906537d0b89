--非特劲生产数据
select year(pp.product_date) "生产年份", pp.product_id SKU, p.name "产品名称", count(1) "数量" 
from wx_t_oem_product_packaging_code pc 
left join wx_t_oem_product_packaging pp on pc.packaging_id=pp.id
left join wx_t_product p on p.sku=pp.product_id
where pp.product_date>='2020-01-01' and (pc.code1='000000000000' or pc.code1 like '10%') and len(pc.code1)=12
group by year(pp.product_date), pp.product_id, p.name order by year(pp.product_date)

--特劲数据
select year(b.finish_time) "QR生成年份", b.sku, b.product_name "产品名称", count(1) "数量"
from wx_t_qr_code_batch b join wx_t_qrcode_detail_untrace d on d.batch_id=b.batch_id
where b.finish_time>='2020-01-01' and b.batch_count>1000
group by year(b.finish_time), b.sku, b.product_name
order by year(b.finish_time)

select sku, name, case when p.category='TCP' then '是' else '否' end "特劲产品(后续产品无物流信息)", 
(select count(1) from wx_t_oem_delivery_product dp where dp.product_id=p.sku) "中商物流条数",
(select count(1) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "中商激活条数(生产批次数)",
(case when exists (select 1 from temp_qr_sku qs where qs.sku=p.sku) then '是' else '否' end) "生成QR"
,(case when p.pack/convert(float,p.capacity)>1 then '箱/瓶' else '桶' end) "单位",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.category' and di1.dic_item_code=p.category) "产品分类",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.oilType' and di1.dic_item_code=p.oil_type) "产品系列",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.capacity' and di1.dic_item_code=p.capacity) "产品容量",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.viscosity' and di1.dic_item_code=p.viscosity) "产品规格",
bp.product_channel
--distinct category, is_competing 
from wx_T_product p 
join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=p.sku
where (category is null or category!='QT') and p.status=1 --and pack>0 
and p.support_order=1 --and convert(float,p.capacity)>0
