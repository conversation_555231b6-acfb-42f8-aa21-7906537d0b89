<%@page import="com.common.util.StringUtils"%>
<%@page import="java.net.URLDecoder"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="com.common.util.ContextUtil" %>
<!DOCTYPE html>
<%
String salesChannel = request.getParameter("salesChannel");
if(StringUtils.isNotBlank(salesChannel)){
	salesChannel = URLDecoder.decode(salesChannel, "UTF-8");
}
%>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>兑换申请</title>
    <%@include file="/common/jsp/common.jsp" %>
    <link href="${ctx }common/hplus/css/animate.css" rel="stylesheet">
    <link href="${ctx }common/hplus/css/plugins/webuploader/webuploader.css" rel="stylesheet" type="text/css">
    <script src="${ctx }common/hplus/js/plugins/webuploader/webuploader.js" type="text/javascript"></script>
    <link href="${ctx }elites2/rebate/css/rebate-detail.css?v=${version}" rel="stylesheet">
    <link href="${ctx }elites2/rebate/css/rebateSave.css?v=${version}" rel="stylesheet">
    <link href="${ctx }elites2/rebate/css/file-img.css?v=${version}" rel="stylesheet">
    <script type="text/javascript" src="${ctx }elites2/rebate/js/formatMoney.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }elites2/rebate/js/rebate_common.js?v=${version}"></script>
    <script type="text/javascript" src="${ctx }elites2/rebate/js/rebateSave.js?v=${version}"></script>
    <script type="text/javascript">
        var rebateId = <%= request.getParameter("rebateId")!=null?request.getParameter("rebateId"):"null" %>;
        var actionType = "<%= request.getParameter("type")!=null?request.getParameter("type"):"" %>";
        var salesChannel = "<%= salesChannel %>";
    </script>
    <style>
        label.field-label{width: 220px;}
        #invoiceRemain,#iviRemain,#thisUsedFund,#excessInvoice{color:#003C99;font-weight: bold;margin-left:4px;margin-right:4px;}
        .x-field-error { position: relative;}
    </style>
</head>
<body class="gray-bg">
<div class="content-wrapper">
    <div class="content-panel header-panel">
        <div class="header-title" id="header-title">基金花费计划申请</div>
        <div class="header-btns">
            <button class="btn-back" id="saveBtn" onclick="save_widget.save()">保存</button>
            <button class="btn-create" id="submitBtn" onclick="save_widget.submit()">提交</button>
            <button class="btn-cancel" id="returnBtn" onclick="save_widget.returnListPage()">取消</button>
        </div>
    </div>

    <div class="content-panel tableDiv cols-2 hide">
        <div class="hide" id="totalfundGridDiv">
            <label class="tableLabel">已累计基金总额（含税）</label>
            <a id="totalToolTip" herf="#"
               title='代表该经销商名下截至当前通过购买雪佛龙相关产品所累计的雪佛龙基金的总金额（含税价值）。该价值由雪佛龙公司根据每一年度经销商“精英计划”所列明的规则（包括享受基金的产品、享受基金的进货价格、以及基金比率），定期核算并记录在案。每一年度经销商所累计之基金都有使用有效期，相关截止日期请参照当年度“精英计划”之规定。'>
                <i class="fa fa-question-circle fa-lg"></i>
            </a>
            <div class="fundTable" id="totalfundGrid"></div>
        </div>
        <div class="hide" id="usedfundGridDiv">
            <label class="tableLabel">已申报发票总额（含税）</label>
            <a id="declaredToolTip" herf="#"
               title='代表该经销商按照“精英计划”之规定，及时有效地使用“业务发展基金（BDF）”和“经销商营销基金（DMF）”后，（通过雪佛龙相关业务代表）所提交的证明材料（包括但不限于各种费用发票的复印件），经雪佛龙公司审核合格后，记录在案的总价值（含税价值）。雪佛龙鼓励经销商根据其自身发展需要和其雪佛龙授权销售区域内的市场需求， 先投入、后兑现；因此在一年中的特定时期，经销商累计的“已申报发票总额（含税）”可以大于其“已累计基金总额（含税）”，但最终雪佛龙将依据其全年进货量所累计的基金总额，通过开具“红字增值税专用发票”的形式，兑现相关费用。每一年度经销商所累计之基金都有最终兑现有效期，相关截止日期请参照当年度“精英计划”之规定。'>
                <i class="fa fa-question-circle fa-lg"></i>
            </a>
            <div class="fundTable" id="usedfundGrid"></div>
        </div>
        <div class="hide" id="applyfundGridDiv">
            <label class="tableLabel">已申报基金总额（含税）</label>
            <a id="applyFundToolTip" herf="#"
               title='已申报基金总额（含税），申报时填入的基金总金额'>
                <i class="fa fa-question-circle fa-lg"></i>
            </a>
            <div class="fundTable" id="applyfundGrid"></div>
        </div>
        <div class="hide" id="actualfundGridDiv">
            <label class="tableLabel">已兑现基金总额（含税）</label>
            <a id="actualFundToolTip" herf="#"
               title='按照每一年度的经销商“精英计划”，雪佛龙将分两次通过开具“红字增值税专用发票”的形式，兑现相关费用，具体兑现日期请参照当年度“精英计划”之规定。在（年中）第一次兑现流程结束之后，雪佛龙将记录已经开具“红字增值税专用发票”给该经销商的总价值（含税价值）。'>
                <i class="fa fa-question-circle fa-lg"></i>
            </a>
            <div class="fundTable" id="actualfundGrid"></div>
        </div>
        <div class="hide" id="fundGridDiv">
            <label class="tableLabel">剩余可用基金（含税）</label>
            <a id="surplusToolTip" herf="#"
               title='等于该经销商“已累计基金总额（含税）”，减去“已申报总额（含税）”。如果该项目下出现正数，代表该经销商通过进货所累计的基金总额仍有富余，经销商需加快投入并及时申报；如果出现负数，代表经销商“已经申报发票总额（含税）”（或加上“（年中）已兑现基金总额（含税）”）已经超过了其通过进货所累计的基金总额（含税）。全年结束，经销商能兑现的相关费用总额，不得超过其通过进货所累计的基金总额。'>
                <i class="fa fa-question-circle fa-lg"></i>
            </a>
            <div class="fundTable" id="fundGrid"></div>
        </div>
    </div>

    <div class="content-panel" id="dataDiv">

        <div class="hide" id="invoiceAmountDialog">
            <div class="content-panel">
                <div class="audit-group">
                    <label class="audit-label width-auto">发票金额（元）:</label>
                    <div class="control-group">
                        <input type="hidden" id="imgIdForAmount"/>
                        <input type="text" id="invoiceAmount" name="invoiceAmount" class="control-text bui-form-field"/>
                    </div>
                </div>
            </div>
        </div>

        <form id="saveRebateForm" class="form-horizontal" method="post">
            <div class="content-panel cols-2">
                <div class="field-group vertical-group">
                    <label class="field-label">
                        <span class="required-flag">*</span>请选择经销商:
                    </label>
                    <div class="control-group" id="orgListDiv">
                        <input type="hidden" id="orgList" name="organizationId" class="control-text bui-form-field"
                               data-rules="{required:true}"/>
                        <input type="hidden" id="orgName" name="organizationName" class="control-text bui-form-field"
                               >
                    </div>
                </div>

                <div class="field-group vertical-group">
                    <label class="field-label">
                        <span class="required-flag">*</span>基金类型:
                    </label>
                    <div class="control-group select-hidden" id="fundTypeDiv">
                        <input type="hidden" id="fundType" name="fundType" class="control-text bui-form-field"
                               data-rules="{required:true}" data-tip="{text:'请选择基金类型,iconCls:'icon-ok'}"/>
                    </div>
                </div>
                <div class="field-group vertical-group hide" id="fundOnwershipDiv">
                    <label class="field-label">
                        <span class="required-flag">*</span>基金归属:
                    </label>
                    <div class="control-group" id="fundOwnershipDiv">
                        <input disabled="disabled" readOnly type="text" id="fundOwnershipText"
                               class="control-text bui-form-field"/>
                        <input readOnly type="hidden" id="fundOwnership" name="fundOwnership"
                               class="control-text bui-form-field" data-rules="{required:true}"/>
                    </div>
                </div>
                <div class="field-group vertical-group">
                    <label class="field-label">
                        <span class="required-flag">*</span>申报项目:
                    </label>
                    <div class="control-group select-hidden" id="activityTypeDiv">
                        <input type="hidden" id="activityType" name="activityType" class="control-text bui-form-field"
                               data-rules="{required:true}" data-tip="{text:'请选择申报项目,iconCls:'icon-ok'}"/>
                    </div>
                </div>
                    <div class="field-group vertical-group" id="applyFundPlanGroup">
                        <label class="field-label">
                            <span class="required-flag">*</span>本次基金花费计划金额(元,含税):
                        </label>
                        <div class="control-group">
                            <input value=0 type="text" id="applyFundPlan" name="applyFundPlan"
                                   class="control-text bui-form-field"
                                   data-rules="{required:true, regexp:[/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,'不正确的金额格式(例：123.46)'], number:true, max:999999999.99,min:0.01}"
                                   data-tip="{text:'请填写基金花费计划金额',iconCls:'icon-ok'}"/>
                            <label id="applyFundPlanTip" style="color: #169bd5;"></label>
                        </div>
                    </div>
            </div>

            <div class="fund-div content-panel">

                <div class="field-group vertical-group hide" id="applyFundGroup" style="display: none">
                    <label class="field-label">
                        <span class="required-flag">*</span>本次申报金额(元,含税):
                    </label>
                    <div class="control-group">
                        <input value=0 type="text" id="applyFund" name="applyFund"
                               class="control-text bui-form-field"
                               data-rules="{required:true, regexp:[/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,'不正确的金额格式(例：123.46)'], number:true, max:999999999.99,min:0.01}"
                               data-tip="{text:'请填写申请金额',iconCls:'icon-ok'}"/>
                        <label id="applyFundTip" style="color: #169bd5;">申报金额必须小于等于发票累加金额. 预估本次申请可兑现金额<span id="thisUsedFund">0.0</span>元</label>
                    </div>
                </div>

                <div class="field-group vertical-group hide" id="useInvoiceAmountDiv">
                    <label class="field-label">
                        使用剩余发票金额(元,含税):
                    </label>
                    <div class="control-group">
                        <input value=0 type="text" id="useInvoiceAmount" name="useInvoiceAmount"
                               class="control-text bui-form-field"
                               data-rules="{required:true, regexp:[/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,'不正确的金额格式(例：123.46)'], number:true, max:999999999.99}"
                               data-tip="{text:'请填写使用发票金额',iconCls:'icon-ok'}"/>
                        <label id="useInvoiceAmountTip" style="color: #169bd5;">当前剩余<span id="invoiceRemain">0.0</span>元未使用发票金额</label>
                    </div>
                </div>
                <div class="field-group vertical-group hide" id="useIviFundDiv">
                    <label class="field-label">
                        使用奖励基金（IVI）金额(元,含税):
                    </label>
                    <div class="control-group">
                        <input value=0 type="text" id="useIviFund" name="useIviFund"
                               class="control-text bui-form-field"
                               data-rules="{required:true, regexp:[/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,'不正确的金额格式(例：123.46)'], number:true, max:999999999.99}"
                               data-tip="{text:'请填写奖励基金（IVI）金额',iconCls:'icon-ok'}"/>
                        <label id="useIviFundTip" style="color: #169bd5;">本次申报金额大于当前申报项目可用基金总额时，可输入奖励基金（IVI）来补足. 当前可使用不超过<span id="iviRemain">0.0</span>元的奖励基金（IVI）。</label>
                    </div>
                </div>

                <div class="hide" id="sumFundDiv" style="display: none">
                    <div class="field-group vertical-group">
                        <label class="field-label">
                            <span class="required-flag">*</span>本次发票累加金额(元):
                        </label>
                        <div class="control-group">
                            <input disabled="disabled" readonly type="text" id="invoiceAmountTotal"
                                   name="invoiceAmountTotal" class="control-text bui-form-field"
                                   data-rules="{required:true}"/>
                            <label style="color: #169bd5;">发票累加金额必须大于等于本次申报金额。预计当前发票超额<span id="excessInvoice">0.0</span>元，此部分可在下次申报时使用。</label>
                        </div>
                    </div>
                </div>

            </div>


            <div class="content-panel" id="hasRentInvoiceGroup" style="display: none">
                <div class="field-group vertical-group">
                    <label class="field-label">
                        <span class="required-flag">*</span>是否可提供租赁发票:
                    </label>
                    <div class="control-group">
                        <div id="hasRentInvoiceDiv">
                            <input type="hidden" id="hasRentInvoice" name="hasRentInvoice" class="control-text bui-form-field"
                                   data-rules="{required:true}" data-tip="{text:'请选择是否可提供租赁发票,iconCls:'icon-ok'}"/>
                        </div>
                        <label style="color: #169bd5;font-size:10px;">房屋租金大项：考虑到库房或者办公场地，通常出租方不提供正规发票，
                            如果经销商能够提供完整的房屋租赁资料复印件（包括合同和收据，其能清晰体现所覆盖的租房周期（年、月、日），<br/>
                            租金金额，相关方详细信息（包括租赁方和房东）如姓名、电话、地址等），可以选择不需要提供发票（可选项目），由FLSR审核通过。</label>
                    </div>
                </div>
            </div>


            <!--活动类型相关信息-->
            <div id="detail-div-2" class="activity-detail">
                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>活动主题:</label>
                    <div class="control-group">
                        <input type="text" id="activityTheme" name="activityTheme" class="control-text bui-form-field"
                               style="width: 430px;" data-rules="{required:true}"/>
                    </div>
                </div>

                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>预计参加的客户人数:</label>
                    <div class="control-group">
                        <input type="text" id="customerNumber" name="customerNumber"
                               class="control-text bui-form-field"
                               data-rules="{required:true, number:true, min:1}"/>
                    </div>
                </div>

                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>预计活动举办日期:</label>
                    <div class="control-group">
                        <input type="text" id="startDateActivity" name="startDateActivity" placeholder="开始时间"
                               class="control-text control-calendar" data-rules="{required:true}"
                               onFocus="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDateActivity\')}'});"/>
                        至
                        <input type="text" id="endDateActivity" name="endDateActivity" placeholder="结束时间"
                               class="control-text control-calendar" data-rules="{required:true}"
                               onFocus="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDateActivity\')}'});"/>
                    </div>
                </div>
            </div>

            <div id="detail-div-3" class="activity-detail">

                <div class="field-group vertical-group" id="billboardGroup">
                    <label class="field-label">
                        <span class="required-flag">*</span>门头类型:
                    </label>
                    <div class="control-group select-hidden" id="billboardTypeDiv">
                        <input type="hidden" id="billboardType" name="billboardType"
                               class="control-text bui-form-field" data-rules="{required:true}"/>
                    </div>
                </div>

                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>广告牌预计投放时间:</label>
                    <div class="control-group">
                        <input type="text" id="startDateBillboard" name="startDateBillboard" placeholder="开始时间"
                               class="control-text control-calendar"  data-rules="{required:true}"
                               onFocus="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDateBillboard\')}'});"/>
                        至
                        <input type="text" id="endDateBillboard" name="endDateBillboard" placeholder="结束时间"
                               class="control-text control-calendar" data-rules="{required:true}"
                               onFocus="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDateBillboard\')}'});"/>
                    </div>
                </div>

                <div class="">
                    <label class="textarea-field-label"><span class="required-flag">*</span>广告牌内容:</label>
                    <div class="textarea-box">
                        <textarea id="billboardContent" name="billboardContent"
                                  class="control-textarea control-text bui-form-field"
                                  data-rules="{required:true}"
                                  data-tip="{text:'请填写广告牌内容',iconCls:'icon-ok'}"/></textarea>
                    </div>
                </div>
            </div>

            <div id="detail-div-4" class="activity-detail">
                <div class="">
                    <label class="textarea-field-label"><span class="required-flag">*</span>预计购买设备名称、数量:</label>
                    <div class="textarea-box">
                        <textarea id="deviceDetail" name="deviceDetail"
                                  class="control-textarea control-text bui-form-field"
                                  data-rules="{required:true}"
                                  data-tip="{text:'请填写预计购买设备名称、数量',iconCls:'icon-ok'}"/></textarea>
                    </div>
                </div>
            </div>

            <div id="detail-div-5" class="activity-detail">
                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>促销形式:</label>
                    <div class="control-group" id="promoteTypeDiv">
                        <input type="hidden" id="promoteType" name="promoteType" class="control-text bui-form-field" data-rules="{required:true}"/>
                    </div>
                </div>

                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>促销日期:</label>
                    <div class="control-group">
                        <input type="text" id="startDatePromote" name="startDatePromote" placeholder="开始时间"
                               class="control-text control-calendar"  data-rules="{required:true}"
                               onFocus="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDatePromote\')}'});"/>
                        至
                        <input type="text" id="endDatePromote" name="endDatePromote" placeholder="结束时间"
                               class="control-text control-calendar"  data-rules="{required:true}"
                               onFocus="WdatePicker({readOnly:true, dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDatePromote\')}'});"/>
                    </div>
                </div>

                <div class="field-group vertical-group">
                    <label class="field-label"><span class="required-flag">*</span>预计促销期内出货量(L):</label>
                    <div class="control-group">
                        <input type="text" id="promoteNumber" name="promoteNumber" class="control-text bui-form-field"
                               data-rules="{required:true, number:true,regexp:[/(^[1-9]([0-9]+)?$)/,'请输入整数格式数字(例：123)']}"
                               data-tip="{text:'请填写预计促销期内出货量',iconCls:'icon-ok'}"/>
                    </div>
                </div>

                <div class="">
                    <label class="textarea-field-label"><span class="required-flag">*</span>促销产品:</label>
                    <div class="textarea-box">
                        <textarea id="promoteProduct" name="promoteProduct"
                                  class="control-textarea control-text bui-form-field"
                                  data-rules="{required:true}"
                                  data-tip="{text:'请填写促销产品',iconCls:'icon-ok'}"/></textarea>
                    </div>
                </div>
            </div>

            <div id="activityDescDiv" style="display: none">
                <label class="textarea-field-label">
                    <span class="required-flag">*</span><span id="activityDescLabel">申报项目说明:</span>
                </label>
                <div class="textarea-box">
                    <textarea id="activityDesc" name="activityDesc" class="control-textarea control-text bui-form-field"
                              data-rules="{required:true, maxlength:1000}"
                              data-tip="{text:'请填写申报项目说明',iconCls:'icon-ok'}"/></textarea>
                </div>
            </div>
            <!--活动类型相关信息-->


            <!--<div class="content-panel fileDiv requireFile hide" id="managerMailUploader">
                <label class="field-label">
                    <span class="required-flag">*</span>雪佛龙业务代表事前批准邮件：
                </label>
                <div class="control-group" style="display:block">
                    <div id="managerMailList" class="uploader-list">
                        <div class="uploadBtn" id="managerMailPicker"></div>
                    </div>
                </div>
            </div>-->

            <div class="content-panel fileDiv requireFile hide" id="customerInteractionDescDiv">
                <label class="field-label">
                    <span class="required-flag">*</span>客户互动效果说明:
                </label>
                <div class="textarea-box">
                    <textarea id="customerInteractionDesc" name="customerInteractionDesc" class="control-textarea control-text bui-form-field"
                              data-rules="{required:true,maxlength:500}" data-tip="{text:'请填写客户互动效果说明',iconCls:'icon-ok'}"/></textarea>
                </div>
            </div>

            <div class="content-panel fileDiv requireFile hide" id="scenePhotoUploader">
                <label class="field-label" style="width: 480px;">
                    <span class="required-flag">*</span>实物/会议现场照（提供的照片需要含有雪佛龙品牌元素和现场与客户互动的图片）:
                </label>
                <div class="control-group" style="display:block">
                    <div id="scenePhotoList" class="uploader-list">
                        <div class="uploadBtn" id="scenePhotoPicker"></div>
                    </div>
                </div>
            </div>

            <div class="content-panel fileDiv requireFile hide" id="invoiceUploader">
                <label class="field-label">
                    <span class="required-flag">*</span><span id="invoiceUploaderLabel">发票复印件</span>:
                </label>
                <div class="control-group" style="display:block">
                    <div id="invoiceList" class="uploader-list">
                        <div class="uploadBtn" id="invoicePicker"></div>
                    </div>
                    <label id="invoiceTip" style="color: #169bd5;display:none;">若无法提供租赁发票，此处可选择上传合同的扫描件，但填写金额必须与合同金额保持一致。</label>
                </div>
            </div>

            <div class="content-panel fileDiv requireFile hide" id="marketingMailUploader">
                <label class="field-label">
                    <span class="required-flag">*</span>市场部审核邮件:
                </label>
                <div class="control-group" style="display:block">
                    <div id="marketingMailList" class="uploader-list">
                        <div class="uploadBtn" id="marketingMailPicker"></div>
                    </div>
                </div>
            </div>

           <!-- <div class="content-panel fileDiv requireFile hide" id="customerInteractionUploader">
                <label class="field-label">
                    <span class="required-flag">*</span>顾客互动效果评估图片:
                </label>
                <div class="control-group" style="display:block">
                    <div id="customerInteractionList" class="uploader-list">
                        <div class="uploadBtn" id="customerInteractionPicker"></div>
                    </div>
                </div>
            </div>-->


            <div class="content-panel fileDiv requireFile hide" id="marketPlanUploader">
                <label class="field-label">
                    <span class="required-flag">*</span>经销商市场活动计划:
                </label>
                <div class="control-group" style="display:block">
                    <div id="marketPlanList" class="uploader-list">
                        <div class="uploadBtn" id="marketPlanPicker"></div>
                    </div>
                </div>
            </div>


            <div class="content-panel fileDiv requireFile hide" id="signInUploader">
                <label class="field-label">
                    <span class="required-flag">*</span>顾客签到表:
                </label>
                <div class="control-group" style="display:block">
                    <div id="signInList" class="uploader-list">
                        <div class="uploadBtn" id="signInPicker"></div>
                    </div>
                </div>
            </div>

            <div class="content-panel fileDiv requireFile hide" id="promotionReceiptUploader">
                <label class="field-label">
                    <span class="required-flag">*</span>地促客户签收单:
                </label>
                <div class="control-group" style="display:block">
                    <div id="promotionReceiptList" class="uploader-list">
                        <div class="uploadBtn" id="promotionReceiptPicker"></div>
                    </div>
                </div>
            </div>

            <div class="content-panel fileDiv requireFile hide" id="otherUploader">
                <label class="field-label">
                    其他证明:
                </label>
                <div class="control-group" style="display:block">
                    <div id="otherList" class="uploader-list">
                        <div class="uploadBtn" id="otherPicker"></div>
                    </div>
                </div>
            </div>

            <div class="">
                <label class="textarea-field-label">备注说明:</label>
                <div class="textarea-box">
                    <textarea id="descirption" name="descirption" class="control-textarea control-text bui-form-field"
                              data-rules="{maxlength:500}" data-tip="{text:'请填写申请备注',iconCls:'icon-ok'}"/></textarea>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
</html>