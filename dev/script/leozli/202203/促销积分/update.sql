--insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Promotion.Point.Category', 'cdm_promotion2', '金富力促销积分', '{  "commonPointCode":"CDM_PROMOTION_POINT2",  "type":"point",  "weight":528,  "channel":3,  "isSellIn":true,  "partnerPermission":false,  "permissionCode":"CDM_PROMOTION2",  "operationPermission":18  }', '1', 215);
--update wx_t_dic_item set dic_item_name='德乐促销积分' where dic_type_code='Promotion.Point.Category' and dic_item_code='promotion2'
--update wx_t_dic_item set dic_item_name='金富力促销积分' where dic_type_code='Promotion.Point.Category' and dic_item_code='cdm_promotion2'

--update wx_t_promotion_point set product_channel='Commercial',point_name='德乐促销积分' where point_code='PROMOTION_POINT2'
--INSERT INTO [dbo].[wx_t_promotion_point]            ([point_name]            ,[point_code]            ,[point_type]            ,[product_channel]            ,[predefine_flag]            ,[ext_flag]           ,[enable_flag]            ,[support_module_flag]           ,[delete_flag]           ,[create_user_id]            ,[create_time])      VALUES            ('金富力促销积分'           ,'CDM_PROMOTION_POINT2'            ,'cdm_promotion2'            ,'Consumer'           ,1            ,0            , 1           ,3            ,0            ,1           ,getdate())
--update wx_t_promotion_point set delete_flag=1 where  point_code in ('PROMOTION_POINT2','CDM_PROMOTION_POINT2')
update wx_t_promotion_point set delete_flag=1 where  point_code in ('PROMOTION_POINT2')

--com.chevron.interfaces.service.impl.MaterialPlatformServiceImpl
--com.chevron.point.dto.PointType

update d set d.status=0 
from wx_t_dic_item d where dic_type_code='Promotion.Point.Category' and dic_item_code='promotion2'
update d set d.sort_numb=9999970,d.dic_item_name='德乐促销积分',d.status=1,dic_item_desc='{  "commonPointCode":"PROMOTION_POINT",  "type":"point",  "weight":512,  "channel":2,  "isSellIn":true,  "partnerPermission":false,  "permissionCode":"CI_PROMOTION2",  "operationPermission":18  }'
from wx_t_dic_item d where dic_type_code='Promotion.Point.Category' and dic_item_code='promotion' and dic_item_name='商用油促销积分'
update d set d.sort_numb=9999950,d.dic_item_name='金富力促销积分',d.status=1,dic_item_desc='{  "commonPointCode":"CDM_PROMOTION_POINT",  "type":"point",  "weight":16,  "channel":1,  "isSellIn":true,  "partnerPermission":false,  "permissionCode":"CDM_PROMOTION2",  "operationPermission":18  }'
from wx_t_dic_item d where dic_type_code='Promotion.Point.Category' and dic_item_code='cdm_promotion' and dic_item_name='乘用车促销积分'
