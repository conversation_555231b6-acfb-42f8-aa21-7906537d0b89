package com.chevron.elites.constant;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

public class FileRequiredConstant {

	private static JSONObject fileRequiredJson = new JSONObject();
	
	static {
		//业务发展基金-业务发展基金
		List<String> busFundFileList = new ArrayList<String>();
		busFundFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		busFundFileList.add(RebateApplyFileType.INVOICE.getCode());
		busFundFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		busFundFileList.add(RebateApplyFileType.OTHER.getCode());
		fileRequiredJson.put(ActivityTypeEnum.BUSINESS_FUND.getCode(), busFundFileList);
		//业务发展基金-购置办公车辆
		List<String> officeCarFileList = new ArrayList<String>();
		officeCarFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		officeCarFileList.add(RebateApplyFileType.INVOICE.getCode());
		officeCarFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.OFFICE_CAR.getCode(), officeCarFileList);
		//业务发展基金-购置运输车辆
		List<String> transportCarFileList = new ArrayList<String>();
		transportCarFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		transportCarFileList.add(RebateApplyFileType.INVOICE.getCode());
		transportCarFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.TRANSPORT_CAR.getCode(), transportCarFileList);
		//业务发展基金-购买办公设备
		List<String> buyEquipmentFileList = new ArrayList<String>();
		buyEquipmentFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		buyEquipmentFileList.add(RebateApplyFileType.INVOICE.getCode());
		buyEquipmentFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.BUY_EQUIPMENT.getCode(), buyEquipmentFileList);
		//业务发展基金-租赁办公室
		List<String> rentOfficeFileList = new ArrayList<String>();
		rentOfficeFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		rentOfficeFileList.add(RebateApplyFileType.INVOICE.getCode());
		rentOfficeFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.RENT_OFFICE.getCode(), rentOfficeFileList);
		//业务发展基金-租赁库房
		List<String> rentStorageRoomFileList = new ArrayList<String>();
		rentStorageRoomFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		rentStorageRoomFileList.add(RebateApplyFileType.INVOICE.getCode());
		rentStorageRoomFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.RENT_STORAGE_ROOM.getCode(), rentStorageRoomFileList);
		//业务发展基金-差旅费（参加雪佛龙会议）
		List<String> conferenceFileList = new ArrayList<String>();
		conferenceFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		conferenceFileList.add(RebateApplyFileType.INVOICE.getCode());
		conferenceFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.CONFERENCE.getCode(), conferenceFileList);
		//业务发展基金-差旅费（参加学佛龙培训）
		List<String> trainFileList = new ArrayList<String>();
		trainFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		trainFileList.add(RebateApplyFileType.INVOICE.getCode());
		trainFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.TRAIN.getCode(), trainFileList);
		//业务发展基金-其他
		List<String> otherFileList = new ArrayList<String>();
		otherFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		otherFileList.add(RebateApplyFileType.INVOICE.getCode());
		otherFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.OTHER.getCode(), otherFileList);
		//市场营销基金-客户互动
		List<String> customerFileList = new ArrayList<String>();
		customerFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		customerFileList.add(RebateApplyFileType.INVOICE.getCode());
		customerFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		customerFileList.add(RebateApplyFileType.CUSTOMER_INTERACTION.getCode());
		customerFileList.add(RebateApplyFileType.SIGN_IN.getCode());
		fileRequiredJson.put(ActivityTypeEnum.CUSTOMER_INTERACTION.getCode(), customerFileList);
		//市场营销基金-门头、广告牌
		List<String> billboardFileList = new ArrayList<String>();
		billboardFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		billboardFileList.add(RebateApplyFileType.MARKETING_MAIL.getCode());
		billboardFileList.add(RebateApplyFileType.INVOICE.getCode());
		billboardFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.BILLBOARD_USED.getCode(), billboardFileList);
		//市场营销基金-小型设备
		List<String> equipmentFileList = new ArrayList<String>();
		equipmentFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		equipmentFileList.add(RebateApplyFileType.INVOICE.getCode());
		equipmentFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		fileRequiredJson.put(ActivityTypeEnum.SMALL_EQUIPMENT.getCode(), equipmentFileList);
		//市场营销基金-地促
		List<String> promotionFileList = new ArrayList<String>();
		promotionFileList.add(RebateApplyFileType.MANAGER_MAIL.getCode());
		promotionFileList.add(RebateApplyFileType.INVOICE.getCode());
		promotionFileList.add(RebateApplyFileType.SCENE_PHOTO.getCode());
		promotionFileList.add(RebateApplyFileType.MARKET_PLAN.getCode());
		promotionFileList.add(RebateApplyFileType.PROMOTION_RECEIPT.getCode());
		fileRequiredJson.put(ActivityTypeEnum.LOCAL_PROMOTION.getCode(), promotionFileList);
 	}
	
	@SuppressWarnings("unchecked")
	public static List<String> getActivityRequireFileList(String activityTypeCode) {
		if(fileRequiredJson.containsKey(activityTypeCode)) {
			return (List<String>) fileRequiredJson.get(activityTypeCode);
		} else {
			return null;
		}
	}
	
}
