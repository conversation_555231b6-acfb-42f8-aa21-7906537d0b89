select * 
--update c set c.sales_cai='KLTA_FLSR',c.from_source='PP'
from dw_access_control_customer_org_sales c where sales_cai='KLPA_FLSR'
select * --KLPA_FLSR
--update c set c.sales_cai='KLTA_FLSR',c.from_source='PP'
from dw_customer_org_sales c where sales_cai='KLPA_FLSR'

insert into dw_sales_role ([sales_cai]
      ,[sales_name_cn]
      ,[sales_role]
      ,[sales_cai_level]
	  , supervisor_cai
      ,[create_time]
      ,[update_time]
      ,[del_flag]
	  , from_source)
select 'KLTA_FLSR' [sales_cai]
      ,'卢可心' [sales_name_cn]
      ,[sales_role]
      ,'SKCL-SKCL-KLTA-KLTA_FLSR' [sales_cai_level]
	  , supervisor_cai
      ,getdate() [create_time]
      ,getdate() [update_time]
      ,[del_flag]
	  ,'PP' from_source 
from dw_sales_role where  supervisor_cai='KLTA' and sales_cai='DOQI'