select o1.organization_name "经销商名称",
	s1.name "仓库名称", 
	ivn2.product_key SKU, 
	p1.name "产品名称",
	isnull(di3.dic_item_name, '其他') "产品分类", 
	isnull(isnull(di2.dic_item_name, di1.dic_item_name), '其他') "产品系列", 
	(ivn2.original_quantity+ivn2.modify_quantity)*convert(float, p1.capacity) "升数"
from (
select ROW_NUMBER() OVER (PARTITION BY ivn1.id ORDER BY ivn1.inventory_time DESC) as rn, 
ivn1.* from (
select i1.id, i1.body_type, i1.body_key, i1.product_type, i1.product_key, 
il1.original_quantity, il1.modify_quantity, il1.create_time inventory_time
from wx_t_inventory_log2021 il1
left join wx_t_inventory2021 i1 on il1.inventory_id=i1.id
where i1.inventory_type='distributor' and il1.create_time<dateadd(day, 1, #NVARCHAR,i_date_to#)
union all 
select i1.id, i1.body_type, i1.body_key, i1.product_type, i1.product_key, 
il1.recall_original_quantity, -il1.modify_quantity, il1.update_time 
from wx_t_inventory_log2021 il1
left join wx_t_inventory2021 i1 on il1.inventory_id=i1.id
where i1.inventory_type='distributor' and il1.update_time<dateadd(day, 1, #NVARCHAR,i_date_to#) and il1.delete_flag=1) ivn1
) ivn2 
left join wx_t_store2021 s1 on s1.id=ivn2.body_key
join wx_t_product p1 on p1.sku=ivn2.product_key
left join wx_t_dic_item di1 on di1.dic_type_code='product.oilType' and di1.dic_item_code=p1.oil_type
left join wx_t_dic_item di2 on di2.dic_type_code='product.productLine' and di2.dic_item_code=di1.dic_item_desc
left join wx_t_dic_item di3 on di3.dic_type_code='product.category' and di3.dic_item_code=p1.category
left join wx_t_organization o1 on o1.id=s1.org_id
left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=o1.id
where ivn2.rn=1 
and (#NVARCHAR,i_store_id# is null or #NVARCHAR,i_store_id#='' or charindex(','+convert(varchar(20), ivn2.body_key) + ',', #NVARCHAR,i_store_id#) > 0 )
and (#NVARCHAR,i_product_category# is null or #NVARCHAR,i_product_category#='' or charindex(','+isnull(di3.dic_item_code, 'OTHERS') + ',', #NVARCHAR,i_product_category#) > 0 )
and (#NVARCHAR,i_product_line# is null or #NVARCHAR,i_product_line#='' or charindex(','+isnull(isnull(di2.dic_item_code, di1.dic_item_code), 'OTHERS') + ',', #NVARCHAR,i_product_line#) > 0 )
and (#NVARCHAR,i_product_size# is null or #NVARCHAR,i_product_size#='' or charindex(','+p1.capacity + ',', #NVARCHAR,i_product_size#) > 0 )
and (#NVARCHAR,i_product_specification# is null or #NVARCHAR,i_product_specification#='' or charindex(','+viscosity+ ',', #NVARCHAR,i_product_specification#) > 0 )
and (#NVARCHAR,i_product_code# is null or #NVARCHAR,i_product_code#='' or charindex(','+p1.sku + ',', #NVARCHAR,i_product_code#) > 0 )
and (#NVARCHAR,i_region# is null or #NVARCHAR,i_region#='' or exists (select 1 from dw_customer_org_sales cos2 where cos2.distributor_id=pe1.distributor_id and charindex(','+cos2.region + ',', #NVARCHAR,i_region#) > 0 ))
and (1=1 or #INTEGER,i_permission_weight#&1>0 
	or (#INTEGER,i_permission_weight#&14>0 and exists (select 1 from dw_customer_org_sales cos1 where cos1.distributor_id=pe1.distributor_id and (cos1.supervisor_cai=#NVARCHAR,i_sals_cai# or cos1.sales_cai=#NVARCHAR,i_sals_cai#))
	or (#INTEGER,i_permission_weight#&16>0) and pe1.distributor_id=#BIGINT,i_distributor_id#))
