--查询经销商账号积分
select sum(d.point_value - d.point_payed) "剩余积分", sum(d.point_payed) "已用积分", a.POINT_ACCOUNT_OWNER_ID,d.point_type from wx_t_point_account a
left join wx_t_point_value_detail d on d.point_account_id=a.id
where point_account_type='SP' and POINT_ACCOUNT_OWNER_ID in (56434,56424)
group by a.POINT_ACCOUNT_OWNER_ID, d.point_type
order by a.POINT_ACCOUNT_OWNER_ID

select * from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id 
where pe.distributor_id=426

select top 100 * from wx_t_log l left join wx_t_user u on u.user_id=l.user_id where u.user_id in (125380, 131679) 
order by log_id desc;

select * 
--update pe set pe.distributor_id=236 --426
from wx_t_partner_o2o_enterprise pe left join wx_t_organization o on pe.partner_id=o.id
where --pe.distributor_id in (56559,56560)56559,56560
o.id in (56559,56560)

select sum(d.point_value - d.point_payed) "剩余积分", sum(d.point_payed) "已用积分", a.POINT_ACCOUNT_OWNER_ID,d.point_type, 
max(d.CREATION_TIME) '最新导入时间', o.organization_name from wx_t_point_account a
left join wx_t_point_value_detail d on d.point_account_id=a.id
left join wx_t_organization o on o.id=a.POINT_ACCOUNT_OWNER_ID
where point_account_type='SP' and POINT_ACCOUNT_OWNER_ID in (56411,
56419,
56467,
56527)
group by a.POINT_ACCOUNT_OWNER_ID, d.point_type, o.organization_name
order by a.POINT_ACCOUNT_OWNER_ID