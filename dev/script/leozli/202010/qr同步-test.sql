2020-10-29

truncate table pp_mid.dbo.wx_t_oem_delivery_product
insert into pp_mid.dbo.wx_t_oem_delivery_product (code, product_id)
select pc.code1, p.product_id from wx_t_oem_product_packaging_code pc 
left join wx_t_oem_product_packaging p on pc.packaging_id=p.id where pc.creation_time>'2020-10-19' --and external_id is null order by id --desc

--truncate table pp_mid.dbo.wx_t_oem_delivery_product
insert into pp_mid.dbo.wx_t_oem_delivery_product (code, product_id)
select dp.code, dp.product_id from wx_t_oem_delivery_product dp 
where dp.creation_time > '2020-10-19' and len(dp.code)=12 and dp.codelevel=1
and not exists (select 1 from wx_t_oem_product_packaging_code c where c.code1=dp.code)

truncate table pp_mid.dbo.wx_t_oem_delivery_product_gq
insert into pp_mid.dbo.wx_t_oem_delivery_product_gq (code)
select dp.code from wx_t_oem_delivery_product dp 
left join wx_t_oem_delivery d on d.id=dp.delivery_id
where dp.creation_time > '2020-10-19' and len(dp.code)=12 and dp.codelevel=1 and d.recv_id='20205'
and not exists (select 1 from wx_t_oem_product_packaging_code c where c.code1=dp.code)
union all select pc.code1 from wx_t_oem_delivery_product dp 
left join wx_t_oem_delivery d on d.id=dp.delivery_id
join wx_t_oem_product_packaging_code pc on pc.code2=dp.code
where dp.creation_time > '2020-10-19' and dp.codelevel=2 and d.recv_id='20205'
and not exists (select 1 from wx_t_oem_product_packaging_code c where c.code1=dp.code)

select * 
--update cd set cd.sku=dp.product_id
from wx_t_oem_delivery_product dp join wx_t_qr_code_detail cd on cd.code_id=convert(bigint,dp.code)-2910000000
where cd.sku is null

select * 
--update cd set cd.sku=dp.product_id
from wx_t_oem_delivery_product_gq dp join wx_t_qr_code_detail cd on cd.code_id=convert(bigint,dp.code)-2910000000
where cd.qr_owner!='GQ'

select replace(dp.[recv_name], '''', '') recv_name, dp.recv_id, max(dp.creation_time) "最新同步时间",count(1) "数量" from wx_t_oem_delivery dp 
where not exists (select 1 from wx_t_organization o where o.status=1 and o.type=1 and dp.[recv_name] like '%' + o.organization_name + '%')
group by replace(dp.[recv_name], '''', ''), dp.recv_id
order by replace(dp.[recv_name], '''', '')

select replace(dp.[recv_name], '''', '') recv_name, dp.recv_id, max(dp.creation_time) "最新同步时间",count(1) "数量" from wx_t_oem_delivery dp 
where not exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] c where dp.[recv_name] like '%' + c.customer_name_cn + '%')
group by replace(dp.[recv_name], '''', ''), dp.recv_id
order by replace(dp.[recv_name], '''', '')