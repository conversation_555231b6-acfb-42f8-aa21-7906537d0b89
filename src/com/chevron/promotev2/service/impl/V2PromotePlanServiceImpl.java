package com.chevron.promotev2.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.promote.dao.PromotePlanBatchMapper;
import com.chevron.promote.dao.PromotePlanPacksDetailMapper;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.CustomerRegionUser;
import com.chevron.promote.model.PromoteLastYearSale;
import com.chevron.promote.model.PromotePlan;
import com.chevron.promote.model.PromotePlanBatch;
import com.chevron.promote.model.PromotePlanPacksDetail;
import com.chevron.promote.service.IPromotePlanService;
import com.chevron.promote.service.impl.PromotePackageServiceImpl;
import com.chevron.promotev2.dao.V2PromotePlanBatchMapper;
import com.chevron.promotev2.dao.V2PromotePlanMapper;
import com.chevron.promotev2.dao.V2PromotePlanPacksDetailMapper;
import com.chevron.promotev2.model.V2CustomerRegionUser;
import com.chevron.promotev2.model.V2PromotePlan;
import com.chevron.promotev2.model.V2PromotePlanBatch;
import com.chevron.promotev2.model.V2PromotePlanPacksDetail;
import com.chevron.promotev2.service.V2PromotePlanService;
import com.common.util.ContextUtil;
import com.common.util.DateUtils;
import com.sys.auth.model.WxTUser;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.file.model.WxAttFile;

@Service
public class V2PromotePlanServiceImpl extends BasePromote implements V2PromotePlanService{
	private static Logger log = LoggerFactory.getLogger(PromotePackageServiceImpl.class);
	private static final String LST = "Lst";
	//start 
	@Autowired
	DicService dicService;
	@Resource
	V2PromotePlanMapper planMapper;
	@Resource
	V2PromotePlanBatchMapper v2PromotePlanBatchMapper;
	@Resource
	V2PromotePlanPacksDetailMapper v2PromotePlanPacksDetailMapper;
	//end
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertOrUpdatePlanPacksDetail(
			List<V2PromotePlanPacksDetail> planPacksDetailLst,Long planbatchId) {
		log.info("PromotePlanServiceImpl insertOrUpdatePlanPacksDetail planPacksDetailLst：{},planbatchId:{}",planPacksDetailLst,planbatchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		if(null==planPacksDetailLst || planPacksDetailLst.isEmpty())
		{
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+PROMOTE_PARMAS_NULL);
			return resultMap;
		}
		try
		{
			//先判断批次是否为空
			if(null!=planbatchId)
			{
				//删掉原来计划礼包详情，
				v2PromotePlanPacksDetailMapper.deletePlanPacksByBatchId(planbatchId);
				
			}else
			{
				//先插入一个计划批次 、新增的计划
				planbatchId = doInsertPlanBatch();
			}
			log.info("insertPlanPacksDetail planbatchId:{}",planbatchId);
			//再录入对应批次中的礼包详情
			for(V2PromotePlanPacksDetail tmpPromotePlanPacksDetail:planPacksDetailLst)
			{
				tmpPromotePlanPacksDetail.setPlanBatchId(planbatchId);
			}
			//批量录入
			v2PromotePlanPacksDetailMapper.insertBatchPlanPacksDetails(planPacksDetailLst);
		
		
			//返回批次id
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put("planBatchId", planbatchId);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("insertPlanPacksDetail"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
			
		}
		return resultMap;
	}
	
	
	private Long doInsertPlanBatch()throws Exception {
		V2PromotePlanBatch planBatch = new V2PromotePlanBatch();
		planBatch.setBatchCode(DateUtils.getCurrentDate(DateUtils.DATA_TIME_PATTERN_NO_CHAR));
		Date date = new Date();
		planBatch.setBatchPlanTitle(DateUtils.getDateStr(date)+PROMOTE_PLAN_TITLE);
		planBatch.setBatchStatus(PROMOTE_PLAN_BATCH_UNSAVE_STATUS);
		planBatch.setCreateTime(date);
		planBatch.setPlanStartTime(date);
		planBatch.setPlanEndTime(DateUtils.addYears(date, 1));
		planBatch.setRemark("系统创建");
		v2PromotePlanBatchMapper.insertSelective(planBatch);
		return planBatch.getId();
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertPromotePlan(List<V2PromotePlan> promotePlanLst,String planStatus) {
		log.info("PromotePlanServiceImpl insertPromotePlan promotePlanLst：{},planStatus:{}",promotePlanLst,planStatus);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			WxTUser currentUser = ContextUtil.getCurUser();
			// 0.判断参数
			if(null==promotePlanLst || promotePlanLst.isEmpty())
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, PROMOTE_PARMAS_NULL);
				return resultMap;
			}
			// 1 先删掉已经有的
			if(null!=promotePlanLst.get(0).getPlanBatchId())
			{
				planMapper.deletePlanByBatchId(promotePlanLst.get(0).getPlanBatchId());
			}
			
			// 2.录入计划
			for(V2PromotePlan promotePlan: promotePlanLst)
			{
				promotePlan.setCreator(""+currentUser.getUserId());
				promotePlan.setCreateTime(new Date());
			}
			planMapper.insertBatchPlans(promotePlanLst);
			
			// 3.更新计划批次状态
			Long planBatchId = promotePlanLst.get(0).getPlanBatchId();
			V2PromotePlanBatch planBatch = new V2PromotePlanBatch();
			planBatch.setBatchStatus(planStatus);
			planBatch.setId(planBatchId);
			planBatch.setReleaseTime(new Date());
			v2PromotePlanBatchMapper.updateByPrimaryKeySelective(planBatch);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put("planBatchId", planBatchId);//可以用于回显
			
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("insertPromotePlan:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> queryPromotePlanBatchList(Long batchId) {
		log.info("PromotePlanServiceImpl queryPromotePlanBatchList batchId：{}",batchId);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("batchId", batchId);
		List<V2PromotePlanBatch> lst = v2PromotePlanBatchMapper.queryPromotePlanBatchList(reqMap);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		resultMap.put(RESULT_LST_KEY, lst);
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> queryPromotePlanListByBatchId(Long batchId) {
		log.info("PromotePlanServiceImpl queryPromotePlanListByBatchId batchId：{}",batchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			List<V2PromotePlan> lst =  planMapper.getPromotePlanLstByBatchId(batchId);
			if(null!=lst && !lst.isEmpty())
			{
				int totalSeminarPacksCount = 0;
				int totalRoadShowActivitiesMaterialsCount = 0;
				int totalRoadShowConsumerPacksCount = 0;
				int totalOpenShopPacksCount = 0;
				int totalOpenShopGdPacksCount = 0;
				int totalIntegralCount = 0;
				Double totalVenueMeal = 0.0;
				int totalSeminarPacksHighCount = 0;
				int totalStorePacksCount = 0;
				int totalAdvertPacksCount = 0;
				int totalAgriculturePacksCount = 0;
				int totalTryPacksCount = 0;
				for(V2PromotePlan promotePlan:lst)
				{
					totalSeminarPacksCount += promotePlan.getSeminarPacksCount();
					totalRoadShowActivitiesMaterialsCount += promotePlan.getRoadShowActivitiesMaterialsCount();
					totalRoadShowConsumerPacksCount += promotePlan.getRoadShowConsumerPacksCount();
					totalOpenShopPacksCount += promotePlan.getOpenShopPacksCount();
					totalOpenShopGdPacksCount += promotePlan.getOpenShopGdPacksCount();
					totalIntegralCount += promotePlan.getIntegralTotalCount();
					totalVenueMeal += promotePlan.getVenueMeal();
					totalSeminarPacksHighCount += promotePlan.getSeminarPacksHighCount();
					totalStorePacksCount += promotePlan.getStorePacksCount();
					totalAdvertPacksCount += promotePlan.getAdvertPacksCount();
					totalAgriculturePacksCount += promotePlan.getAgriculturePacksCount();
					totalTryPacksCount += promotePlan.getTryPacksCount();
				}
				//组装总量行
				V2PromotePlan totalPromotePlan = new V2PromotePlan();
				totalPromotePlan.setRegion("总量");
				totalPromotePlan.setSeminarPacksCount(totalSeminarPacksCount);
				totalPromotePlan.setRoadShowActivitiesMaterialsCount(totalRoadShowActivitiesMaterialsCount);
				totalPromotePlan.setRoadShowConsumerPacksCount(totalRoadShowConsumerPacksCount);
				totalPromotePlan.setOpenShopPacksCount(totalOpenShopPacksCount);
				totalPromotePlan.setOpenShopGdPacksCount(totalOpenShopGdPacksCount);
				totalPromotePlan.setIntegralTotalCount(totalIntegralCount);
				totalPromotePlan.setVenueMeal(totalVenueMeal);
				totalPromotePlan.setSeminarPacksHighCount(totalSeminarPacksHighCount);
				totalPromotePlan.setStorePacksCount(totalStorePacksCount);
				totalPromotePlan.setAdvertPacksCount(totalAdvertPacksCount);
				totalPromotePlan.setAgriculturePacksCount(totalAgriculturePacksCount);
				totalPromotePlan.setTryPacksCount(totalTryPacksCount);
				lst.add(totalPromotePlan);
			}
			resultMap.put(RESULT_LST_KEY, lst);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryPromotePlanListByBatchId:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryPlanPacksDetailByBatchId(Long batchId) {
		log.info("PromotePlanServiceImpl queryPlanPacksDetailByBatchId batchId：{}",batchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("planBatchId", batchId);
			reqMap.put("AttSourceType", WxAttFile.SourceType_PromoteActivity);
			List<V2PromotePlanPacksDetail> lst =  v2PromotePlanPacksDetailMapper.queryPlanPacksDetailByBatchId(reqMap);
			if(null==lst || lst.isEmpty())
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, PROMOTE_PARMAS_NULL);
				return resultMap;
			}
			resultMap = doHandlePlanPacksDetail(lst);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("queryPlanPacksDetailByBatchId:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	private Map<String, Object> doHandlePlanPacksDetail(
			List<V2PromotePlanPacksDetail> lst)throws Exception {
		log.info("PromotePlanServiceImpl doHandlePlanPacksDetail lst：{}",lst);
		Map<String, Object> resultMap = new HashMap<String,Object>();
		String lastPacksCode = "";
		List<V2PromotePlanPacksDetail> tmpLst = null;
		for(V2PromotePlanPacksDetail promotePlanPacksDetail:lst)
		{
			String packsCode = promotePlanPacksDetail.getPacksCode();
			if(!lastPacksCode.equals(packsCode))
			{
				tmpLst = new ArrayList<V2PromotePlanPacksDetail>();
			}
			tmpLst.add(promotePlanPacksDetail);
			resultMap.put(packsCode.toLowerCase()+LST, tmpLst);
			lastPacksCode = packsCode;
			
		}
		return resultMap;
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> updatePlanPacksDetail(List<V2PromotePlanPacksDetail> lst) {
		log.info("PromotePlanServiceImpl updatePlanPacksDetail lst：{}",lst);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		if(null==lst || lst.isEmpty())
		{
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, PROMOTE_PARMAS_NULL);
			return resultMap;
		}
		try
		{
			// 1.获取批次号，直接根据批次号删掉原来的礼包明细
			Long planbatchId = lst.get(0).getId();
			v2PromotePlanPacksDetailMapper.deletePlanPacksByBatchId(planbatchId);
			
			// 2.录入现在的礼包明细
			v2PromotePlanPacksDetailMapper.insertBatchPlanPacksDetails(lst);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("updatePlanPacksDetail:"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	@Override
	public Map<String, Object> getLastYearSales(Long batchId) {
		log.info("PromotePlanServiceImpl getLastYearSales batchId:{}",batchId);
		Map<String,Object> resultMap = new HashMap<String,Object>(2);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//reqMap.put("regionName", REGION_NAME);
		reqMap.put("regionType", REGION_TYPE);
		reqMap.put("queryStartDate", DateUtils.getDateStr(DateUtils.getFirstDayOfYear(DateUtils.addYears(DateUtils.getCurrentDate(),-1)), "yyyy-MM-dd"));
		reqMap.put("queryEndDate", DateUtils.getDateStr(DateUtils.getFirstDayOfYear(new Date()), "yyyy-MM-dd"));
		try
		{
			
			List<PromoteLastYearSale> lst =  planMapper.getLastYearSales(reqMap);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			if(null==lst || lst.isEmpty())
			{
				/*resultMap.put("regionSouth", 0);
				resultMap.put("regionNorth", 0);*/
				resultMap.put("lastYearSalesLiters", new PromoteLastYearSale());
				return resultMap;
			}
			
			log.info("PromotePlanServiceImpl getLastYearSales returnPromoteLastYearSale");
			PromoteLastYearSale returnPromoteLastYearSale = new PromoteLastYearSale();
			for(PromoteLastYearSale promoteLastYearSale : lst)
			{
				String region = promoteLastYearSale.getRegion();
				Double salesLiters = promoteLastYearSale.getSalesLiters();
				if(REGION_SOUTH.equals(region))
				{
					//resultMap.put("regionSouth", formatDouble(salesLiters));
					returnPromoteLastYearSale.setRegionSouth(formatDouble(salesLiters));
					returnPromoteLastYearSale.setRegionSouthStr(formatDoubleToStr(salesLiters));
				}
				else if(REGION_NORTH.equals(region))
				{
					//resultMap.put("regionNorth", formatDouble(salesLiters));
					returnPromoteLastYearSale.setRegionNorth(formatDouble(salesLiters));
					returnPromoteLastYearSale.setRegionNorthStr(formatDoubleToStr(salesLiters));
				}
			}
			double northPercent = formatDouble(
					(returnPromoteLastYearSale.getRegionNorth())
					/(returnPromoteLastYearSale.getRegionSouth()+returnPromoteLastYearSale.getRegionNorth())
					);
			returnPromoteLastYearSale.setNorthPercentage(northPercent);
			returnPromoteLastYearSale.setSouthPercentage(1-northPercent);
			
			List<PromotePlan> lstPlans = null;
			if(null!=batchId)
			{
				Map<String,Object> returnMap  = queryPromotePlanListByBatchId(batchId);
				lstPlans = (List<PromotePlan>) returnMap.get(RESULT_LST_KEY);
			}
			resultMap.put("lastYearSalesLiters", returnPromoteLastYearSale);
			resultMap.put("lstPlans", lstPlans);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getLastYearSales"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public V2CustomerRegionUser getCustomerRegionUserV2()throws Exception {
		log.info("PromotePlanServiceImpl getCustomerRegionUserV2");
		WxTUser currentUser = ContextUtil.getCurUser();
		V2CustomerRegionUser customerUser = new V2CustomerRegionUser();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("userId", currentUser.getUserId());
		reqMap.put("regionType", REGION_TYPE);
		customerUser = planMapper.getCustomerRegionUserInfo(reqMap);
		if(null==customerUser)
		{
			log.info("PromotePlanServiceImpl getCustomerRegionUserV2 customerUser is null");
			customerUser = new V2CustomerRegionUser();
		}
		customerUser.setUserId(currentUser.getUserId());
		customerUser.setLoginName(currentUser.getLoginName());
		customerUser.setChName(currentUser.getChName());
		String userCai = customerUser.getCai();
		log.info("PromotePlanServiceImpl getCustomerRegionUserV2 userCai：{},customerUser:{}",userCai,customerUser);
		if(null!=userCai && !userCai.isEmpty())
		{
			if(null!=customerUser.getChannelManagerCai() && !customerUser.getChannelManagerCai().isEmpty())//大区经理
			{
				customerUser.setCustomerRoleName(PROMOTE_CHANNELMANAGER);
				customerUser.setRegionName(customerUser.getChannelRegionName());
			}else if(null!=customerUser.getSupervisorCai() && !customerUser.getSupervisorCai().isEmpty())//小区经理
			{
				customerUser.setCustomerRoleName(PROMOTE_SUPERVISOR);
				customerUser.setRegionName(customerUser.getSupervisorRegionName());
			}else if(null!=customerUser.getSalesCai() && !customerUser.getSalesCai().isEmpty())//销售
			{
				customerUser.setCustomerRoleName(PROMOTE_SALES);
				customerUser.setRegionName(customerUser.getSalesRegionName());
			} else {
				//marketing
				Long userid = planMapper.getCustomerMarketingUser(reqMap);
				if(null!=userid)
				{
					customerUser.setCustomerRoleName(PROMOTE_MARKETING);
					customerUser.setRegionName(currentUser.getRegionName());
				}
			}
			
		}else
		{
			//经销商
			customerUser.setCustomerRoleName(PROMOTE_DEALER);
			customerUser.setRegionName(currentUser.getRegionName());//not use
		}
		//看对应的customerRoleName 是否配置了对应的审批字典中
		Map<String, Object> planDicResult =  dicService.getDicItemByDicTypeCode("promote.plan.approval");
		List<DicItemVo> planItemlist = (ArrayList)planDicResult.get("data");
		String planStr = "";
		for(DicItemVo dicItem : planItemlist){
			if(dicItem.getDicItemCode().equals("approvors")){
				planStr = dicItem.getDicItemName();
				continue;
			}
		}
		
		Map<String, Object> activityDicResult =  dicService.getDicItemByDicTypeCode("promote.activity.approval");
		List<DicItemVo> activityItemlist = (ArrayList)planDicResult.get("data");
		String activityStr = "";
		for(DicItemVo dicItem : planItemlist){
			if(dicItem.getDicItemCode().equals("approvors")){
				activityStr = dicItem.getDicItemName();
				continue;
			}
		}
		
		if(planStr.contains(customerUser.getCustomerRoleName()))
		{
			customerUser.setApprovalPlanPower(true);
		}
		
		
		if(activityStr.contains(customerUser.getCustomerRoleName()))
		{
			customerUser.setApprovalActivityPower(true);
		}
		return customerUser;
	}
	
	
	@Override
	public V2CustomerRegionUser getCustomerRegionUser() {
		log.info("PromotePlanServiceImpl getCustomerRegionUser");
		WxTUser currentUser = ContextUtil.getCurUser();
		V2CustomerRegionUser customerUser = new V2CustomerRegionUser();
		String userCai = customerUser.getCai();//not use currentUser.getCai();
		customerUser.setUserId(currentUser.getUserId());
		customerUser.setLoginName(currentUser.getLoginName());
		customerUser.setCai(userCai);
		customerUser.setChName(currentUser.getChName());
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("userId", currentUser.getUserId());
		String regionName = "";
		log.info("PromotePlanServiceImpl getCustomerRegionUser userCai：{},customerUser:{}",userCai,customerUser);
		if(null!=userCai && !userCai.isEmpty())
		{
			reqMap.put("regionName", REGION_NAME);
			//大区经理
			regionName = planMapper.getCustomerRegionChannelCai(reqMap);
			if(null!=regionName && !regionName.isEmpty())
			{
				customerUser.setRegionName(regionName);
				customerUser.setCustomerRoleName(PROMOTE_CHANNELMANAGER);
				
			}else
			{
				regionName = planMapper.getCustomerRegionSupervisorCai(reqMap);
				//小区经理
				if(null!=regionName && !regionName.isEmpty())
				{
					customerUser.setRegionName(regionName);
					customerUser.setCustomerRoleName(PROMOTE_SUPERVISOR);
				}else
				{
					//销售
					regionName = planMapper.getCustomerRegionSalesCai(reqMap);
					if(null!=regionName && !regionName.isEmpty())
					{
						customerUser.setRegionName(regionName);
						customerUser.setCustomerRoleName(PROMOTE_SALES);
					}
				}
			}
		}else
		{
			//marketing
			Long userid = planMapper.getCustomerMarketingUser(reqMap);
			if(null!=userid)
			{
				customerUser.setCustomerRoleName(PROMOTE_MARKETING);
			}else
			{	//经销商
				customerUser.setCustomerRoleName(PROMOTE_DEALER);
			}
		}
		//看对应的customerRoleName 是否配置了对应的审批字典中  not use
		/*Map<String, Object> planDicResult =  dicService.getDicItemByDicTypeCode("promote.plan.approval");
		List<DicItemVo> planItemlist = (ArrayList)planDicResult.get("data");
		String planStr = "";
		for(DicItemVo dicItem : planItemlist){
			if(dicItem.getDicItemCode().equals("approvors")){
				planStr = dicItem.getDicItemName();
				continue;
			}
		}
		
		Map<String, Object> activityDicResult =  dicService.getDicItemByDicTypeCode("promote.activity.approval");
		List<DicItemVo> activityItemlist = (ArrayList)activityDicResult.get("data");
		String activityStr = "";
		for(DicItemVo dicItem : planItemlist){
			if(dicItem.getDicItemCode().equals("approvors")){
				activityStr = dicItem.getDicItemName();
				continue;
			}
		}
		
		if(planStr.contains(customerUser.getCustomerRoleName()))
		{
			customerUser.setApprovalPlanPower(true);
		}
		
		
		if(activityStr.contains(customerUser.getCustomerRoleName()))
		{
			customerUser.setApprovalActivityPower(true);
		}*/
		return customerUser;
	}
}
