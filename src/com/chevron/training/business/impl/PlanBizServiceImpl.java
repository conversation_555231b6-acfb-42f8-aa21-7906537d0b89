package com.chevron.training.business.impl;

import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportExcel;
import com.chevron.exportdata.ICellStyleProcessor;
import com.chevron.importdata.ImportDataUtil;
import com.chevron.pms.service.impl.OrderDDBXServiceImpl;
import com.chevron.training.business.PlanBizService;
import com.chevron.training.business.ReportBizService;
import com.chevron.training.business.TrainingAssessmentBizService;
import com.chevron.training.business.TrainingSurveyBizService;
import com.chevron.training.dao.TrainingCurriculumMapper;
import com.chevron.training.dao.TrainingPlanMapper;
import com.chevron.training.model.*;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.google.common.collect.Lists;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.UserCtrlParams;
import com.sys.auth.model.WxTUser;
import com.sys.email.service.EmailSenderService;
import com.sys.push.model.Message;
import com.sys.push.service.MessagePushService;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.ContextLoader;

import javax.servlet.ServletContext;
import java.io.File;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.util.*;

/**
 * 培训安排业务接口实现类
 *
 * <AUTHOR>
 * @version 1.0 2020-10-26 11:03
 */
@Service
public class PlanBizServiceImpl implements PlanBizService {
    @Autowired
    private TrainingPlanMapper trainingPlanMapper;

    @Autowired
    private ExportExcel exportExcelService;

    @Autowired
    private OperationPermissionBizService operationPermissionBizService;

    @Autowired
    private EmailSenderService emailSenderService;

    @Autowired
    private WxTUserMapper userMapper;

    @Autowired
    private MessagePushService messagePushService;

    @Autowired
    private TrainingSurveyBizService trainingSurveyBizService;

    @Autowired
    private TrainingCurriculumMapper trainingCurriculumMapper;
    @Autowired
    private ReportBizService reportBizService;
    @Autowired
    private TrainingAssessmentBizService trainingAssessmentBizService;

    @Autowired
    private JdbcTemplate wxJdbcTemplate;

    private static final String MAIL = "%s—%s之间参加%s培训，";

    private static final String EMAIL = "%s—%s之间参加%s培训，";

    private static final String EMAIL_TITLE = "%s驻地培训通知";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insert(Plan record) throws WxPltException {
        record.setDeleteFlag(0);
        record.setCreateUserId(ContextUtil.getCurUserId());
        record.setCreateTime(DateUtil.getCurrentDate());
        Date startTime = record.getStartTime();
        if (startTime == null || record.getEndTime() == null) {
            throw new WxPltException("开始时间/结束时间不能为空");
        }
        if (record.getCurriculumId() == null || StringUtils.isBlank(record.getCurriculumName())) {
            throw new WxPltException("培训课程不能为空");
        }
        if (record.getTraningDays() == null) {
            try {
                int i = DateUtil.daysBetween(startTime, record.getEndTime());
                record.setTraningDays(i + 1);
            } catch (ParseException e) {
                throw new WxPltException(e);
            }
        }
        trainingPlanMapper.insertSelective(record);
        String month = DateUtil.getDateStr(new Date(), "yyyy-MM");
        String month1 = DateUtil.getDateStr(startTime, "yyyy-MM");
        if (StringUtils.equals(month, month1)) {
            notify(record);
        }
    }

    private void notify(Plan record) {
        Date endTime = record.getEndTime();
        String dateStr = DateUtil.getDateStr(record.getStartTime(), "yyyy-MM-dd");
        String dateStr1 = DateUtil.getDateStr(endTime, "yyyy-MM-dd");
        String curriculumName = record.getCurriculumName();
        String format = String.format(MAIL, dateStr, dateStr1, curriculumName);
        Map<String, Object> map = new HashMap<String, Object>();
        List<String> roleList = new ArrayList<String>();
        roleList.add("Caltex_Dealer");
        roleList.add("CDM_DIST");
        roleList.add("Service_Partner_Manager");
        map.put("distributorId", record.getDistributorId());
        map.put("roles", roleList);
        List<WxTUser> partnerAdmins = userMapper.getPartnerAdmins(map);
        Set<String> emails = new HashSet<String>();
        for (WxTUser user : partnerAdmins) {
            String email = user.getEmail();
            emails.add(email);
        }

        List<WxTUser> sales = userMapper.selectUserByCai(record.getSalesCai());
        String ccEmail = "";
        if (!CollectionUtils.isEmpty(sales)) {
            ccEmail = sales.get(0).getEmail();
        }
        // 邮件
        String content = String.format(EMAIL, dateStr, dateStr1, curriculumName);
        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        String emailTitle = String.format(EMAIL_TITLE, record.getDistributorName());
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("title", emailTitle);
        dataMap.put("content", content);
        emailSenderService.sendEmailForCommon(context,
                emails.toArray(new String[0]), new String[]{ccEmail},
                emailTitle, dataMap, null, "plan_notify_email.ftl");
        // 推送
        messagePushService.pushMessageOrPhoneMsg(Message.MESSAGE_TYPE_PERSONAL, "驻地培训通知",
                "请在" + format + "谢谢。", null, partnerAdmins);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void update(Plan record) throws WxPltException {
        record.setUpdateUserId(ContextUtil.getCurUserId());
        record.setUpdateTime(DateUtil.getCurrentDate());
        Plan plan = trainingPlanMapper.selectByPrimaryKey(record.getId());
        boolean equals = !record.getStartTime().equals(plan.getStartTime());
        boolean equals1 = !record.getEndTime().equals(plan.getEndTime());
        boolean equals2 = plan.getDistributorId() != null && plan.getDistributorId() > 0 
        		&& !plan.getDistributorId().equals(record.getDistributorId());
        if (equals2 || equals || equals1) {
            String month = DateUtil.getDateStr(new Date(), "yyyy-MM");
            String month1 = DateUtil.getDateStr(record.getStartTime(), "yyyy-MM");
            if (StringUtils.equals(month, month1)) {
                notify(record);
            }
        }
        if (record.getStartTime() != null && record.getEndTime() != null) {
            try {
                int i = DateUtil.daysBetween(record.getStartTime(), record.getEndTime());
                record.setTraningDays(i + 1);
            } catch (ParseException e) {
                throw new WxPltException(e);
            }
        }
        trainingPlanMapper.updateByPrimaryKeySelective(record);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delete(List<Long> ids) throws WxPltException {
        PlanExample example = new PlanExample();
        example.createCriteria().andIdIn(ids);
        trainingPlanMapper.deleteByExample(example);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteByExample(PlanExample example) throws WxPltException {
        trainingPlanMapper.deleteByExample(example);
    }

    @Override
    public List<Plan> queryByExample(PlanExample example) throws WxPltException {
        return trainingPlanMapper.selectByExample(example);
    }

    @Override
    public List<Plan> queryByParams(Map<String, Object> params) throws WxPltException {
        return trainingPlanMapper.queryByParams(params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateForEditPage(Plan record) throws WxPltException {
        record.setUpdateUserId(ContextUtil.getCurUserId());
        record.setUpdateTime(DateUtil.getCurrentDate());
        trainingPlanMapper.updateForEditPage(record);
    }

    @Override
    public Plan getBean(Long id) throws WxPltException {
        return trainingPlanMapper.selectByPrimaryKey(id);
    }

    @Override
    public void queryForPage(PlanParams params, Map<String, Object> resultMap) throws WxPltException {
        resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
        resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
    }

    @Override
    public List<Plan> queryForPage(PlanParams params) throws WxPltException {
        WxTUser curUser = ContextUtil.getCurUser();
        int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(),
                "Training.page.plan");
        if (permissionWeight > 0 && (permissionWeight & 1) > 0) {
            params.setUserId(curUser.getUserId());
        }
        List<Plan> plans = trainingPlanMapper.queryForPage(params);
        for (Plan plan : plans) {
            int month = getMonth(plan.getStartTime(), plan.getEndTime());
            plan.setMonth(month);
        }
        return plans;
    }

    @Override
    public List<TrainerHistory> getTrainerHistorySurvey(PlanParams params) throws WxPltException, Exception {
        params.setPaging(false);
        params.setField("id");
        List<Plan> plans = this.queryForPage(params);
        List<TrainerHistory> result = new ArrayList<TrainerHistory>();
        Map<Long, List<Plan>> map = new HashMap<Long, List<Plan>>();
        for (Plan plan : plans) {
            Long userId = plan.getUserId();
            List<Plan> list = map.get(userId);
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<Plan>();
            }
            list.add(plan);
            map.put(userId, list);
        }
        for (Long userId : map.keySet()) {
            TrainerHistory trainerHistory = new TrainerHistory();
            List<Plan> plans1 = map.get(userId);
            StringBuilder remark = new StringBuilder();
            trainerHistory.setTrainer(plans1.get(0).getTraningTeacher());
            int size = 0;
            for (Plan plan : plans1) {
                TrainingSurvey byPlanId = trainingSurveyBizService.getByPlanId(plan.getId());
                if (byPlanId != null) {
                    List<TrainingSurveyEvaluation> evaluations = byPlanId.getEvaluations();
                    for (TrainingSurveyEvaluation evaluation : evaluations) {
                        int sortNumb = evaluation.getSortNumb().intValue();
                        Method set = TrainerHistory.class.getDeclaredMethod("setConfig"
                                + sortNumb, String.class);
                        Method get = TrainerHistory.class.getMethod("getConfig"
                                + sortNumb);
                        Object getInvoke = get.invoke(trainerHistory);
                        String s = getInvoke == null ? "0" : (String) getInvoke;
                        int scope = evaluation.getScope() == null ? 0 : evaluation.getScope();
                        int i = scope + Integer.parseInt(s);
                        set.invoke(trainerHistory, String.valueOf(i));
                    }
                    Date submitTime = byPlanId.getSubmitTime();
                    remark.append(DateUtil.toDateStr(submitTime)).append("：").append(byPlanId.getProposal()).append("\r\n");
                    size = size + 1;
                }
            }
            handleScope(trainerHistory, size);
            trainerHistory.setRemark(remark.toString());
            result.add(trainerHistory);
        }
        return result;
    }

    @Override
    public List<ExportCol> exportCols() {
        // 导出列定义
        List<ExportCol> exportCols = new ArrayList<ExportCol>();
        ExportCol col = new ExportCol("trainer", "讲师", TrainerHistory.class);
        col.setWidth(40);
        exportCols.add(col);
        col = new ExportCol("totalScope", "总得分", TrainerHistory.class);
        col.setWidth(40);
        exportCols.add(col);
        col = new ExportCol("config1", "1、讲师准备工作充分", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config2", "2、讲师经验丰富、条理清晰、逻辑性强", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config3", "3、讲师语言流畅、清晰易懂", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config4", "4、学到了新的知识", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config5", "5、学到了新的技能", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config6", "6、学到了很多案例 ", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config7", "7、对我工作起了很大作用", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config8", "8、项目值得持续参与", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config9", "9、辅导气氛良好", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config10", "10、培训内容切合实际、便于应用", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config11", "11、培训内容深度适中、易于理解", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config12", "12、重点内容突出、主次分明", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config13", "13、培训时间控制合理 ", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config14", "14、充分调动学员积极性", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config15", "15、很好回答学员的提问", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config16", "16、符合学员的程度水平", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config17", "17、教学方法形式灵活多样", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config18", "18、表达方式生动、易于理解", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config19", "19、对工作有很大启发", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("config20", "20、值得我花时间投入", TrainerHistory.class);
        col.setWidth(40);
        col.setParentsPath("评价项目（评分标准为1-5分，每项最高分为5分）：");
        exportCols.add(col);
        col = new ExportCol("averageScore", "矩阵题平均分", TrainerHistory.class);
        col.setWidth(40);
        exportCols.add(col);
        col = new ExportCol("remark", "经销商的建议", TrainerHistory.class);
        col.setWidth(80);
        col.setCellStyleProcessor(new ICellStyleProcessor() {
            @Override
            public void process(CellStyle cellStyle, Workbook workbook) {
                cellStyle.setWrapText(true);
            }
        });
        exportCols.add(col);
        return exportCols;
    }

    private void handleScope(TrainerHistory trainerHistory, int size) {
        double v1 = Double.parseDouble(trainerHistory.getConfig1() == null ? "0" : trainerHistory.getConfig1());
        trainerHistory.setConfig1(String.valueOf(v1 == 0D ? 0D : (v1 / size)));
        double v2 = Double.parseDouble(trainerHistory.getConfig2() == null ? "0" : trainerHistory.getConfig2());
        trainerHistory.setConfig2(String.valueOf(v2 == 0D ? 0D : (v2 / size)));
        double v3 = Double.parseDouble(trainerHistory.getConfig3() == null ? "0" : trainerHistory.getConfig3());
        trainerHistory.setConfig3(String.valueOf(v3 == 0D ? 0D : (v3 / size)));
        double v4 = Double.parseDouble(trainerHistory.getConfig4() == null ? "0" : trainerHistory.getConfig4());
        trainerHistory.setConfig4(String.valueOf(v4 == 0D ? 0D : (v4 / size)));
        double v5 = Double.parseDouble(trainerHistory.getConfig5() == null ? "0" : trainerHistory.getConfig5());
        trainerHistory.setConfig5(String.valueOf(v5 == 0D ? 0D : (v5 / size)));
        double v6 = Double.parseDouble(trainerHistory.getConfig6() == null ? "0" : trainerHistory.getConfig6());
        trainerHistory.setConfig6(String.valueOf(v6 == 0D ? 0D : (v6 / size)));
        double v7 = Double.parseDouble(trainerHistory.getConfig7() == null ? "0" : trainerHistory.getConfig7());
        trainerHistory.setConfig7(String.valueOf(v7 == 0D ? 0D : (v7 / size)));
        double v8 = Double.parseDouble(trainerHistory.getConfig8() == null ? "0" : trainerHistory.getConfig8());
        trainerHistory.setConfig8(String.valueOf(v8 == 0D ? 0D : (v8 / size)));
        double v9 = Double.parseDouble(trainerHistory.getConfig9() == null ? "0" : trainerHistory.getConfig9());
        trainerHistory.setConfig9(String.valueOf(v9 == 0D ? 0D : (v9 / size)));
        double v10 = Double.parseDouble(trainerHistory.getConfig10() == null ? "0" : trainerHistory.getConfig10());
        trainerHistory.setConfig10(String.valueOf(v10 == 0D ? 0D : (v10 / size)));
        double v11 = Double.parseDouble(trainerHistory.getConfig11() == null ? "0" : trainerHistory.getConfig11());
        trainerHistory.setConfig11(String.valueOf(v11 == 0D ? 0D : (v11 / size)));
        double v12 = Double.parseDouble(trainerHistory.getConfig12() == null ? "0" : trainerHistory.getConfig12());
        trainerHistory.setConfig12(String.valueOf(v12 == 0D ? 0D : (v12 / size)));
        double v13 = Double.parseDouble(trainerHistory.getConfig13() == null ? "0" : trainerHistory.getConfig13());
        trainerHistory.setConfig13(String.valueOf(v13 == 0D ? 0D : (v13 / size)));
        double v14 = Double.parseDouble(trainerHistory.getConfig14() == null ? "0" : trainerHistory.getConfig14());
        trainerHistory.setConfig14(String.valueOf(v14 == 0D ? 0D : (v14 / size)));
        double v15 = Double.parseDouble(trainerHistory.getConfig15() == null ? "0" : trainerHistory.getConfig15());
        trainerHistory.setConfig15(String.valueOf(v15 == 0D ? 0D : (v15 / size)));
        double v16 = Double.parseDouble(trainerHistory.getConfig16() == null ? "0" : trainerHistory.getConfig16());
        trainerHistory.setConfig16(String.valueOf(v16 == 0D ? 0D : (v16 / size)));
        double v17 = Double.parseDouble(trainerHistory.getConfig17() == null ? "0" : trainerHistory.getConfig17());
        trainerHistory.setConfig17(String.valueOf(v17 == 0D ? 0D : (v17 / size)));
        double v18 = Double.parseDouble(trainerHistory.getConfig18() == null ? "0" : trainerHistory.getConfig18());
        trainerHistory.setConfig18(String.valueOf(v18 == 0D ? 0D : (v18 / size)));
        double v19 = Double.parseDouble(trainerHistory.getConfig19() == null ? "0" : trainerHistory.getConfig19());
        trainerHistory.setConfig19(String.valueOf(v19 == 0D ? 0D : (v19 / size)));
        double v20 = Double.parseDouble(trainerHistory.getConfig20() == null ? "0" : trainerHistory.getConfig20());
        trainerHistory.setConfig20(String.valueOf(v20 == 0D ? 0D : (v20 / size)));

        double total = v1 + v2 + v3 + v4 + v5 + v6 + v7 + v8 + v9 + v10
                + v11 + v12 + v13 + v14 + v15 + v16 + v17 + v18 + v19 + v20;
        trainerHistory.setTotalScope(total == 0d ? "0" : String.valueOf(total / size));
        trainerHistory.setAverageScore(total == 0d ? 0 : total / (20 * size));
    }

    /**
     * 计算月份，如果没有跨月则直接取当前月份，
     * 如果跨月，如果前一个月得天数大于等于后一个月，取前一个月，否则取后一个月
     *
     * @param startDate 培训开始时间
     * @param endDate   培训结束时间
     * @return
     * @throws WxPltException
     */
    private int getMonth(Date startDate, Date endDate) throws WxPltException {
        int startMonth = Integer.parseInt(DateUtil.getMonth(startDate));
        int endMonth = Integer.parseInt(DateUtil.getMonth(endDate));
        if (startMonth == endMonth) {
            return startMonth;
        }
        Date lastDayOfMonth = DateUtil.getLastDayOfMonth(startDate);
        Date firstDayOfMonth = DateUtil.getFirstDayOfMonth(endDate);
        try {
            int startDays = DateUtil.daysBetween(startDate, lastDayOfMonth);
            int endDays = DateUtil.daysBetween(firstDayOfMonth, endDate);
            if (startDays >= endDays) {
                return startMonth;
            } else {
                return endMonth;
            }
        } catch (ParseException e) {
            throw new WxPltException("日期格式错误");
        }
    }

    @Override
    public File exportPlan(PlanParams params) throws WxPltException {
        params.setPaging(false);
        params.setField("start_time");
        List<Plan> plans = this.queryForPage(params);
        List<ExportPlanVO> list = new ArrayList<ExportPlanVO>();
        for (Plan plan : plans) {
            ExportPlanVO transfer = transfer(plan);
            list.add(transfer);
        }
        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        //组装文件路径
        String fileRootStr = "training_plan_export" + File.separator;
        String fileRootPath = context.getRealPath(fileRootStr) + File.separator;
        File fileRootDir = new File(fileRootPath);
        if (!fileRootDir.exists() && !fileRootDir.isDirectory()) {
            fileRootDir.mkdir();
        }

        String fileName = "training_plan_detail_" + System.currentTimeMillis();
        String filePath = fileRootStr + fileName;
        String filePathDir = context.getRealPath(filePath) + File.separator;
        File file = new File(filePathDir);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }

        Map<String, Object> returnMap = exportExcelService.exportDataForCommonAndCreateFile(list, "com.chevron.training.model.ExportPlanVO",
                "training plan", filePath, fileName, context);
        String s = (String) returnMap.get(OrderDDBXServiceImpl.RESULT_CODE_KEY);
        if (StringUtils.equals(s, OrderDDBXServiceImpl.SUCCESS_CODE)) {
            File[] attfiles = (File[]) returnMap.get("attfiles");
            return attfiles[0];
        } else {
            String errorMsg = (String) returnMap.get(OrderDDBXServiceImpl.RESULT_ERROR_MSG_KEY);
            throw new WxPltException(errorMsg);
        }
    }

    private ExportPlanVO transfer(Plan plan) {
        ExportPlanVO exportPlanVO = new ExportPlanVO();
        exportPlanVO.setMonth(plan.getMonth());
        exportPlanVO.setDistributorName(plan.getDistributorName());
        exportPlanVO.setDistributorAddress(plan.getDistributorAddress());
        exportPlanVO.setDistributorConctor(plan.getDistributorConctor());
        exportPlanVO.setDays(plan.getTraningDays());
        exportPlanVO.setDistributorPhone(plan.getDistributorPhone());
        Date startTime = plan.getStartTime();
        Date endTime = plan.getEndTime();
        String start = DateUtil.getDateStr(startTime, "yyyy年MM月dd日");
        String end = DateUtil.getDateStr(endTime, "yyyy年MM月dd日");
        exportPlanVO.setTime(start + "-" + end);
        exportPlanVO.setAsmName(plan.getAsmName());
        List<WxTUser> wxTUsers = userMapper.selectUserByCai(plan.getAsmCai());
        if (!CollectionUtils.isEmpty(wxTUsers)) {
            exportPlanVO.setAsmPhone(wxTUsers.get(0).getMobileTel());
        }
        exportPlanVO.setSalesName(plan.getSalesName());
        exportPlanVO.setSalesPhone(plan.getSalesPhone());
        exportPlanVO.setTeacher(plan.getTraningTeacher());
        exportPlanVO.setTeacherPhone(plan.getTeacherPhone());
        exportPlanVO.setRemark(plan.getRemark());
        exportPlanVO.setName(plan.getCurriculumName());
        return exportPlanVO;
    }

    /**
     * @param workbook
     * @throws Exception
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> importData(Workbook workbook) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> map = ImportDataUtil.getImportDataByReflect1(workbook, ImportPlanData.class,
                TITLE);
        List<ImportPlanData> datalst = (List<ImportPlanData>) map.get("datalst");
        int datalstSize = datalst.size();
        List<Object> curriculumValid = CommonUtil.getColumnValuesFromDb(datalst, "curriculumName",
                "select id from wx_t_curriculum where name = ?");

        List<Object> teacherIds = CommonUtil.getColumnValuesFromDb(datalst, "teacherPhone",
                "select a.user_id from wx_t_user a\n" +
                        "    left join wx_t_userrole b on a.user_id=b.user_id\n" +
                        "    left join wx_t_role wtr on b.role_id = wtr.role_id\n" +
                        "where a.status=1  and wtr.ch_role_name='Trainer' and mobile_tel=?");

        List<Object> distributorNameValid = CommonUtil.getColumnValuesFromDb(datalst, "distributorName",
                "select t1.distributor_id from wx_t_organization t " +
                        "left join wx_t_partner_o2o_enterprise t1 on t.id = t1.partner_id " +
                        "where t.organization_name= ? and t.type = 1 and t.status = 1");

        List<Plan> needSends = new ArrayList<Plan>();
        List<Plan> plans = new ArrayList<Plan>();
        for (int i = 0; i < datalstSize; i++) {
            ImportPlanData data = datalst.get(i);
            Map<String, Object> temp = valid(curriculumValid, distributorNameValid, teacherIds, i, data);
            if (!temp.isEmpty()) {
                resultMap.put("fail", temp);
                StringBuilder sb = new StringBuilder();
                for (String s : temp.keySet()) {
                    sb.append(s).append(":").append(temp.get(s)).append(".");
                }
                throw new WxPltException("导入数据错误：" + sb.toString());
            } else {
                Plan plan = transfer(data, (Long) curriculumValid.get(i), (Long) distributorNameValid.get(i),
                        (Long) teacherIds.get(i));
                plans.add(plan);
                String now = DateUtil.getMonth(new Date());
                if (StringUtils.equals(DateUtil.getMonth(plan.getStartTime()), now)) {
                    needSends.add(plan);
                }
            }
        }
        if (!plans.isEmpty()) {
            int i = trainingPlanMapper.batchInsert(plans);
            resultMap.put("successNum", i);
        }
        if (!needSends.isEmpty()) {
            batchNotify(needSends);
        }
        return resultMap;
    }

    @Override
    public void send(Date date) throws WxPltException {
        PlanExample example = new PlanExample();
        String month = DateUtil.getMonth(date);
        String year = DateUtil.getYear(date);
        Date endTimeOfMonth = DateUtil.getEndTimeOfMonth(Integer.valueOf(year), Integer.valueOf(month));
        example.createCriteria().andStartTimeBetween(date, endTimeOfMonth);
        List<Plan> plans = trainingPlanMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(plans)) {
            batchNotify(plans);
        }

    }

    private void batchNotify(List<Plan> list) throws WxPltException {
        Map<Long, List<Plan>> map = new HashMap<Long, List<Plan>>();
        Map<String, List<Plan>> phoneMap = new HashMap<String, List<Plan>>();
        for (Plan plan : list) {
            Curriculum curriculum = trainingCurriculumMapper.selectByPrimaryKey(plan.getCurriculumId());
            plan.setCurriculumName(curriculum.getName());
            Long distributorId = plan.getDistributorId();
            if (distributorId != null) {
                List<Plan> plans = map.get(distributorId);
                if (CollectionUtils.isEmpty(plans)) {
                    map.put(distributorId, Lists.newArrayList(plan));
                } else {
                    plans.add(plan);
                    map.put(distributorId, plans);
                }
            } else {
                String distributorPhone = plan.getDistributorPhone();
                List<Plan> plans = phoneMap.get(distributorPhone);
                if (CollectionUtils.isEmpty(plans)) {
                    phoneMap.put(distributorPhone, Lists.newArrayList(plan));
                } else {
                    plans.add(plan);
                    phoneMap.put(distributorPhone, plans);
                }
            }
        }
        for (Long distributorId : map.keySet()) {
            List<Plan> plans = map.get(distributorId);
            String head = "请在";
            StringBuilder mailContext = new StringBuilder();
            for (Plan plan : plans) {
                //请在2021-01-05—2021-01-07（培训时间）之间参加XXX（课程名）培训，2021-01-10—2021-01-13（培训时间）之间参加XXX（课程名）培训，谢谢。
                String start = DateUtil.getDateStr(plan.getStartTime(), "yyyy-MM-dd");
                String endDate = DateUtil.getDateStr(plan.getEndTime(), "yyyy-MM-dd");
                String format = String.format(MAIL, start, endDate, plan.getCurriculumName());
                mailContext.append(format);
            }
            String end = "谢谢。";
            String sendMailContext = head + mailContext.toString() + end;
            Map<String, Object> dataMap = new HashMap<String, Object>();
            List<String> roleList = new ArrayList<String>();
            roleList.add("Caltex_Dealer");
            roleList.add("CDM_DIST");
            roleList.add("Service_Partner_Manager");
            dataMap.put("distributorId", distributorId);
            dataMap.put("roles", roleList);
            List<WxTUser> partnerAdmins = userMapper.getPartnerAdmins(dataMap);
            Set<String> emails = new HashSet<String>();
            for (WxTUser user : partnerAdmins) {
                String email = user.getEmail();
                emails.add(email);
            }

            List<WxTUser> sales = userMapper.selectUserByCai(plans.get(0).getSalesCai());
            String ccEmail = "";
            if (!CollectionUtils.isEmpty(sales)) {
                ccEmail = sales.get(0).getEmail();
            }
            Map<String, Object> emailMap = new HashMap<String, Object>();
            String emailTitle = String.format(EMAIL_TITLE, plans.get(0).getDistributorName());
            emailMap.put("title", emailTitle);
            emailMap.put("content", mailContext.toString());
            ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            // 发送邮件
            if (!CollectionUtils.isEmpty(emails)) {
                emailSenderService.sendEmailForCommon(context,
                        emails.toArray(new String[0]), new String[]{ccEmail},
                        emailTitle, emailMap, null, "plan_notify_email.ftl");
            }

            // 推送
            messagePushService.pushMessageOrPhoneMsg(Message.MESSAGE_TYPE_PERSONAL, "驻地培训通知",
                    sendMailContext, null, partnerAdmins);
        }

        for (String phone : phoneMap.keySet()) {
            List<Plan> plans = phoneMap.get(phone);
            String head = "请在";
            StringBuilder mailContext = new StringBuilder();
            for (Plan plan : plans) {
                //请在2021-01-05—2021-01-07（培训时间）之间参加XXX（课程名）培训，2021-01-10—2021-01-13（培训时间）之间参加XXX（课程名）培训，谢谢。
                String start = DateUtil.getDateStr(plan.getStartTime(), "yyyy-MM-dd");
                String endDate = DateUtil.getDateStr(plan.getEndTime(), "yyyy-MM-dd");
                String format = String.format(MAIL, start, endDate, plan.getCurriculumName());
                mailContext.append(format);
            }
            String end = "谢谢。";
            String sendMailContext = head + mailContext.toString() + end;
            // 只发送短信
            SMSUtil.sendSms(phone, sendMailContext, SMSUtil.APP_ID_PP);
        }
    }


    @Override
    public void notifyTeacher(Date date) throws WxPltException {
        Date endDate = DateUtil.addHours(date, -72);
        Date startDate = DateUtil.addHours(date, -(72 + 24));
        PlanExample example = new PlanExample();
        example.createCriteria().andEndTimeBetween(startDate, endDate).andDeleteFlagEqualTo(0);
        List<Plan> plans = trainingPlanMapper.selectByExample(example);
        Set<String> mobiles = new HashSet<String>();
        Set<String> emails = new HashSet<String>();
        for (Plan plan : plans) {
            Long id = plan.getId();
            Report report = reportBizService.queryByPlanId(id);
            List<TrainingAssessment> trainingAssessments = trainingAssessmentBizService.queryByPlanId(id);
            if (report == null && CollectionUtils.isEmpty(trainingAssessments)) {
                Long userId = plan.getUserId();
                WxTUser user = userMapper.selectByPrimaryKey(userId);
                String email = user.getEmail();
                String mobileTel = user.getMobileTel();
                mobiles.add(mobileTel);
                emails.add(email);
            }
        }
        for (String mobile : mobiles) {
            String mail = "您有待填写的驻地培训报告，请登陆雪佛龙合伙人平台填写，谢谢。";
            SMSUtil.sendSms(mobile, mail, SMSUtil.APP_ID_PP);
        }
        for (String email : emails) {
            ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            Map<String, Object> dataMap = new HashMap<String, Object>();
            emailSenderService.sendEmailForCommon(context,
                    new String[]{email}, null,
                    "驻地培训报告填写提醒", dataMap, null, "plan_teacher_email.ftl");
        }
    }

    @Override
    public void notifyDistributor(Date date) throws WxPltException {
        Date endDate = DateUtil.addDays(date, -3);
        Date startDate = DateUtil.addDays(date, -4);
        PlanExample example = new PlanExample();
        example.createCriteria().andEndTimeBetween(startDate, endDate).andDeleteFlagEqualTo(0);
        List<Plan> plans = trainingPlanMapper.selectByExample(example);
        List<WxTUser> sends = new ArrayList<WxTUser>();
        Set<String> emails = new HashSet<String>();
        for (Plan plan : plans) {
            TrainingSurvey byPlanId = trainingSurveyBizService.getByPlanId(plan.getId());
            if (byPlanId == null || byPlanId.getId() == null) {
                Long distributorId = plan.getDistributorId();
                if (distributorId != null) {
                    Map<String, Object> dataMap = new HashMap<String, Object>();
                    List<String> roleList = new ArrayList<String>();
                    roleList.add("Caltex_Dealer");
                    roleList.add("CDM_DIST");
                    roleList.add("Service_Partner_Manager");
                    dataMap.put("distributorId", distributorId);
                    dataMap.put("roles", roleList);
                    List<WxTUser> partnerAdmins = userMapper.getPartnerAdmins(dataMap);
                    sends.addAll(partnerAdmins);
                    for (WxTUser partnerAdmin : partnerAdmins) {
                        emails.add(partnerAdmin.getEmail());
                    }
                } else {
                    WxTUser wxTUser = new WxTUser();
                    wxTUser.setMobileTel(plan.getDistributorPhone());
                    sends.add(wxTUser);
                }
            }
        }
        String mail = "您有待填写的经销商反馈表，请登陆雪佛龙合伙人平台填写，谢谢。";
        messagePushService.pushMessageOrPhoneMsg(Message.MESSAGE_TYPE_PERSONAL, "驻地培训通知",
                mail, null, sends);
        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        Map<String, Object> dataMap = new HashMap<String, Object>();

        emailSenderService.sendEmailForCommon(context, emails.toArray(new String[0]), null,
                "经销商反馈表填写提醒", dataMap, null, "plan_disbutor_notify.ftl");
    }

    @Override
    public void notifySOP(Date date) throws WxPltException {
        Date endDate = DateUtil.addDays(date, -7);
        Date startDate = DateUtil.addDays(date, -8);
        PlanExample example = new PlanExample();
        example.createCriteria().andEndTimeBetween(startDate, endDate).andDeleteFlagEqualTo(0);
        List<Plan> plans = trainingPlanMapper.selectByExample(example);
        String email = (String) Constants.getSystemPropertyByCodeType("training.sop.notify.email.to");
        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        for (Plan plan : plans) {
            TrainingSurvey byPlanId = trainingSurveyBizService.getByPlanId(plan.getId());
            if (byPlanId == null || byPlanId.getId() == null) {
                Map<String, Object> dataMap = new HashMap<String, Object>();
                dataMap.put("distributor", plan.getDistributorName());
                dataMap.put("startDate", DateUtil.getDateStr(plan.getStartTime(), "yyyy/MM/dd"));
                dataMap.put("endDate", DateUtil.getDateStr(plan.getEndTime(), "yyyy/MM/dd"));
                emailSenderService.sendEmailForCommon(context, new String[]{email}, null,
                        "经销商反馈表填写提醒", dataMap, null, "plan_sop_notify.ftl");
            }
        }
    }


    private Plan transfer(ImportPlanData data, Long curriculumId, Long distributorId,
                          Long teacherId) throws ParseException, WxPltException {
        Plan plan = new Plan();
        plan.setCurriculumName(data.getCurriculumName());
        plan.setCurriculumId(curriculumId);
        plan.setStartTime(data.getStartDate());
        plan.setEndTime(data.getEndDate());
        Date startTime = plan.getStartTime();
        Date endTime = plan.getEndTime();
        int month = getMonth(startTime, endTime);
        String month1 = data.getMonth();
        if (!StringUtils.isEmpty(month1)) {
            int i = Integer.parseInt(month1);
            if (month != i) {
                throw new WxPltException("导入月份填写错误:" + i + "开始时间:"
                        + DateUtil.getDateStr(startTime) + ",结束时间:" + DateUtil.getDateStr(endTime));
            }
        } else {
            throw new WxPltException("月份不能为空");
        }
        int days = DateUtil.daysBetween(startTime, endTime) + 1;
        plan.setTraningDays(days);
        if (distributorId != null) {
            plan.setDistributorId(distributorId);
        }
        String distributorName = data.getDistributorName();
        String distributorConctor = data.getDistributorConctor();
        String distributorPhone = data.getDistributorPhone();
        if (StringUtils.isBlank(distributorName)) {
            throw new WxPltException("经销商不能为空");
        }
        if (StringUtils.isBlank(distributorConctor)) {
            throw new WxPltException("经销商联络人不能为空");
        }
        if (StringUtils.isBlank(distributorPhone)) {
            throw new WxPltException("经销商手机号不能为空");
        }
        plan.setDistributorName(distributorName);
        plan.setDistributorConctor(distributorConctor);
        plan.setDistributorPhone(distributorPhone);
        plan.setDistributorAddress(data.getDistributorAddress());
        if (distributorId != null) {
            List<WxTUser> asms = getAsm(distributorId);
            if (!CollectionUtils.isEmpty(asms)) {
                plan.setAsmCai(asms.get(0).getCai());
                plan.setAsmName(asms.get(0).getChName());
            }
            List<WxTUser> sales = getSales(distributorId);
            if (!CollectionUtils.isEmpty(sales)) {
                plan.setSalesCai(sales.get(0).getCai());
                plan.setSalesPhone(sales.get(0).getMobileTel());
                plan.setSalesName(sales.get(0).getChName());
            }
        } else {
            String asmName = data.getAsmName();
            String salesPhone = data.getSalesPhone();
            String salesName = data.getSalesName();
            String asmSql = "select distinct suppervisor_cai from view_customer_region_sales_channel where suppervisor_name= ?";
            String asmCai = wxJdbcTemplate.queryForObject(asmSql, String.class, asmName);
            if (StringUtils.isBlank(asmCai)) {
                throw new WxPltException("销售名称不存在:" + salesName);
            }
            plan.setAsmName(asmName);
            plan.setAsmCai(asmCai);

            String salesSql = "select distinct sales_cai from view_customer_region_sales_channel where sales_name= ?";
            String salesCai = wxJdbcTemplate.queryForObject(salesSql, String.class, salesName);
            if (StringUtils.isBlank(salesCai)) {
                throw new WxPltException("销售名称不存在:" + salesName);
            }
            plan.setSalesName(salesName);
            plan.setSalesCai(salesCai);
            plan.setSalesPhone(salesPhone);
        }
        plan.setTraningTeacher(data.getTraningTeacher());
        plan.setTeacherPhone(data.getTeacherPhone());
        plan.setRemark(data.getRemark());
        plan.setDeleteFlag(0);
        plan.setCreateUserId(ContextUtil.getCurUserId());
        plan.setCreateTime(DateUtil.getCurrentDate());
        plan.setUserId(teacherId);
        return plan;
    }

    private List<WxTUser> getAsm(Long distributorId) {
        UserCtrlParams params = new UserCtrlParams();
        params.setSpResource(true);
        params.setResourceId("training");
        params.setBu("Indirect");
        params.setDistributorId(distributorId);
        params.setPartnerIdField("wp.partner_id");
        params.setDistributorIdField("crs.distributor_id");
        params.setWorkshopIdField("w.id");
        params.setRegionNameField("crs.region");
        params.setDataIncludeTestSp(false);
        params.setPaging(false);
        params.setField("userId");
        return userMapper.selectAsmUsersForCtrlPage(params);
    }

    private List<WxTUser> getSales(Long distributorId) {
        UserCtrlParams params = new UserCtrlParams();
        params.setSpResource(true);
        params.setResourceId("training");
        params.setBu("Indirect");
        params.setDistributorId(distributorId);
        params.setPartnerIdField("wp.partner_id");
        params.setDistributorIdField("crs.distributor_id");
        params.setWorkshopIdField("w.id");
        params.setRegionNameField("crs.region");
        params.setDataIncludeTestSp(false);
        params.setPaging(false);
        params.setField("userId");
        return userMapper.selectFlsrUsersForCtrlPage(params);
    }

    private Map<String, Object> valid(List<Object> curriculumValid, List<Object> distributorNameValid,
                                      List<Object> teacherIds, int i, ImportPlanData data) {
        Map<String, Object> temp = new HashMap<String, Object>();
        if (curriculumValid.get(i) == null) {
            temp.put("第" + (i + 2) + "行,课程名称错误", data.getCurriculumName());
        }
        if (teacherIds.get(i) == null) {
            temp.put("第" + (i + 2) + "行,培训老师手机号错误", data.getTeacherPhone());
        } else {
            WxTUser user = userMapper.selectByPrimaryKey((Long) teacherIds.get(i));
            String chName = user.getChName();
            if (!StringUtils.equals(data.getTraningTeacher(), chName)) {
                temp.put("第" + (i + 2) + "行,培训老师姓名错误", data.getTraningTeacher());
            }
        }
        return temp;
    }


    private static final String[] TITLE = new String[]{"curriculumName", "month", "startDate", "endDate",
            "distributorName",
            "distributorAddress", "distributorConctor", "distributorPhone", "asmName", "salesName", "salesPhone"
            , "traningTeacher", "teacherPhone", "remark"};
}
