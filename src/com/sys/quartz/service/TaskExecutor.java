package com.sys.quartz.service;

import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sys.push.service.MessagePushService;
import com.sys.quartz.model.ScheduleJob;
import com.chevron.task.business.TaskService;
import com.sys.quartz.service.impl.JobTaskService;

@Component
public class TaskExecutor {
	public final Logger log = Logger.getLogger(this.getClass());
 
	@Autowired
	private JobTaskService taskService;
	@Autowired
	private MessagePushService messagePushService;
	
	@Resource
	TaskService wxTaskService ;
	public synchronized void execute(Long taskMainId) {
		log.debug("---TaskExecutor by ljc  0705 execute-----------");
		for (int i = 0; i < 1; i++) {
			log.debug(i+" run............. with  taskMainId:"+taskMainId+"........................." + (new Date()));
			try {
				//wxTaskService.createTaskByTaskMainId(taskMainId);
				wxTaskService.handleQuartzTaskAboutByMainTaskId(taskMainId);
			} catch (Exception e) {
				e.printStackTrace();
				log.error("周期性任务创建失败,taskMainId:"+taskMainId+";错误信息:"+e.getMessage());
			}
		}
	}

	public void stopExpiredJobs() throws SchedulerException {
		log.debug("----TaskExecutor by ljc stopExpiredJobs 0705-------");
		List<ScheduleJob> scheduleJobs = taskService.obtainRunningJobs();
		System.out.println("in the stopExpiredJobs method scheduleJobs size:"+scheduleJobs.size()+" "+ (new Date()));
		GregorianCalendar nowTime = new GregorianCalendar();
		GregorianCalendar endDate = new GregorianCalendar();
		for (ScheduleJob scheduleJob:scheduleJobs) {
			System.out.println("nowTime:"+nowTime.getTime());
			System.out.println("scheduleJob.getEndTime():"+scheduleJob.getEndTime());
			endDate.setTime(scheduleJob.getEndTime());
			if(nowTime.after(endDate)){
				taskService.changeStatus(scheduleJob.getJobId(), "stop");
			}
		}
	}
	
	public void pushMessage() throws SchedulerException {
		log.debug("-----TaskExecutor.pushMessage-------");
		try {
			messagePushService.sendMessageList();
		} catch (Exception e) {
			log.error(e.getMessage());
		}
	}
}
