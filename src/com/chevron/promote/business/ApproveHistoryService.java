package com.chevron.promote.business;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.common.util.ContextUtil;
import com.workflow.dao.ApproveHistoryMapper;
import com.workflow.model.ApproveHistory;
/**
 * 
 * @Author: bo.liu  2018-6-8 上午10:40:52 
 * @Version: $Id$
 * @Desc: <p>审批历史业务类</p>
 */
@Service
public class ApproveHistoryService {
	private static Logger log = LoggerFactory.getLogger(ApproveHistoryService.class);
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
    public static final String APPLY_STATUS_APPROVED_REMARK="";
    public static final String APPLY_STATUS_UNAPPROVED_REMARK="";
	/**
     * 审核中..
     */
    public static final String APPLY_STATUS_APPROVING = "2";
    /**
     * 审核通过
     */
    public static final String APPLY_STATUS_APPROVED= "3";
    /**
     * 审核不通过
     */
    public static final String APPLY_STATUS_UNAPPROVED= "4";

    /**
     * 流程定义的key
     */
    public static final String APPLY_PRO_KEY_PROMOTE_DISTRIBUTION = "promoteDistribution";
    
    /**
     * 流程定义的名字
     */
    public static final String APPLY_PRO_NAME_PROMOTE_DISTRIBUTION = "地促分配审批申请";
    /**
     * 流程定义的key
     */
    public static final String APPLY_PRO_KEY_PROMOTE_APPLICATION = "promoteApplication";
    
    /**
     * 流程定义的名字
     */
    public static final String APPLY_PRO_NAME_PROMOTE_APPLICATION = "地促活动审批申请";
	@Resource
	private ApproveHistoryMapper approveHistoryMapper;

	/**
	 *  插入审批历史
	 * <AUTHOR> 2018-6-8 上午10:24:15
	 * @param proKey  流程定义的key
	 * @param applyBatchId
	 * @param status
	 * @param approveRemark
	 * @param stepSequence
	 * @return
	 */
	public  boolean insertApproveHis(String proKey,Long applyBatchId,
			String status, String approveRemark,String stepSequence)
	{
		ApproveHistory approveHis = new ApproveHistory();
		approveHis.setOrderBatchId(applyBatchId);
		approveHis.setApprover(""+ContextUtil.getCurUser().getUserId());
		if(null==approveRemark||approveRemark.isEmpty())
		{
			if(status.equals(APPLY_STATUS_UNAPPROVED))
			{
				approveHis.setApproveRemark(APPLY_STATUS_UNAPPROVED_REMARK);
			}else
			{
				approveHis.setApproveRemark(APPLY_STATUS_APPROVED_REMARK);
			}
		}else
		{
			approveHis.setApproveRemark(approveRemark);
		}
		
		approveHis.setApproveStatus(status);
		approveHis.setApplyTempletType(proKey);
		approveHis.setApproveTime(new Date());
		approveHis.setStepSequence(stepSequence);
		approveHistoryMapper.insertSelective(approveHis);
		return true;
	}
	
	
	/**
	 * 获取审批历史
	 * <AUTHOR> 2018-6-8 上午10:25:13
	 * @param proKey
	 * @param applyBatchId
	 * @return
	 */
	public Map<String, Object> getApproveHisByBatchId(String proKey,Long applyBatchId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			reqMap.put("applyBatchId", applyBatchId);
			reqMap.put("applyTempletType", proKey);
			List<ApproveHistory> lstApproveHis = approveHistoryMapper.getApproveHistorys(reqMap);
			resultMap.put(RESULT_LST_KEY, lstApproveHis);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			log.info(e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, e.getLocalizedMessage());
		}
		return resultMap;
	}
}
