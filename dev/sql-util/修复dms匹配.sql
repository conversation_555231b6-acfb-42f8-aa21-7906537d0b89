select *, ROW_NUMBER() OVER (PARTITION BY a.dms_key ORDER BY a.weight DESC, a.task_count desc, a.create_time desc) AS rank from (
select (case when w.from_source = 2 then 1 else 0 end) +
+ (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then 8 else 0 end/*B2B店*/)
+ (case when exists (select 1   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.org_id=w.id) then 1 else 0 end/*陈列大比拼*/)
			 + (case when w.shop_recruitment=1 then 16 else 0 end/*店招店*/) + (case when w.join_location_plan=1 then 2 else 0 end/*定位店*/)
			  + (case when w.ext_flag&1024>0 then 1 else 0 end/*参与德乐400MGX陈列活动*/)
			  + (case when w.ext_flag&16>0 then 4 else 0 end/*导航店*/)
+(case when w.status = '3' then 1000 else 0 end) weight, w.id, w.work_shop_name, w.create_time, w.dms_key,
(select count(1)   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD', 'TT_2_XD_CAI')   and s.task_status = 4 and s.org_id=w.id) task_count from wx_t_work_shop w
 where w.delete_flag=0 and dms_key in () a order by dms_key