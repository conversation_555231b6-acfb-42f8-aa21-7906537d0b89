select sku,product_channel,tcp_flag, gq_flag, sum(a.validate_flag) validate_count, count(1) total from (
select cd.sku, case when exists (select 1 from temp_validate v where v.qr_code=cd.qr_code) then 1 else 0 end validate_flag,p.product_channel,
case when exists (select 1 from wx_t_qrcode_detail_untrace cd1 left join wx_t_qr_code_batch b1 on b1.batch_id=cd1.batch_id
where b1.sku=cd.sku) then 'TCP' else '' end tcp_flag, case when cd.qr_owner='GQ' then 'GQ' else '' end gq_flag
from wx_t_qr_code_detail cd left join wx_t_qr_code_batch b on b.batch_id=cd.batch_id
left join wx_t_product p on p.sku=cd.sku
where cd.sku is not null and b.creation_time>='2019-01-01'
union all select b.sku, case when exists (select 1 from temp_validate v where v.qr_code=cd.qr_code) then 1 else 0 end validate_flag,p.product_channel,
'TCP',''
from wx_t_qrcode_detail_untrace cd left join wx_t_qr_code_batch b on b.batch_id=cd.batch_id
left join wx_t_product p on p.sku=b.sku
where b.creation_time>='2019-01-01') a
group by a.sku,product_channel,tcp_flag, gq_flag
order by tcp_flag, gq_flag,product_channel

