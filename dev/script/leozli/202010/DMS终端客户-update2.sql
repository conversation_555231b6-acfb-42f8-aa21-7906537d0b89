update pp_dict20201112 set item_report_name=item_name

insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.customerStatus', '-100', '解除合作门店', '解除合作门店', 2, 1, 0, getdate(), getdate());

update d set d.item_report_name='项目工地',item_name='项目工地'
from pp_dict20201112 d where type_code='DistributorCustomer.customerType' and id=3

insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.customerType', '4', '工程机械', '工程机械', 4, 1, 0, getdate(), getdate());


update d set d.item_report_name='乘用车DSR业务'
from pp_dict20201112 d where type_code='DistributorCustomer.fromSource' and item_code='1'

update d set d.item_report_name='商用油DSR业务'
from pp_dict20201112 d where type_code='DistributorCustomer.fromSource' and item_code='2'

update d set d.item_report_name='工程机械客户',d.sort_numb=7
from pp_dict20201112 d where type_code='DistributorCustomer.fromSource' and item_code='4'
update d set d.item_report_name='工程机械DSR业务',item_name='工程机械DSR业务'
from pp_dict20201112 d where type_code='DistributorCustomer.fromSource' and item_code='4'

insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.fromSource', '8', '乘用车直播', '乘用车直播', 4, 1, 0, getdate(), getdate());
insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.fromSource', '16', '商用油直播', '商用油直播', 5, 1, 0, getdate(), getdate());
insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.fromSource', '32', '项目工地', '项目工地', 6, 1, 0, getdate(), getdate());
insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.fromSource', '64', '建筑渣土车队DSR业务', '建筑渣土车队DSR业务', 8, 1, 0, getdate(), getdate());

insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('SellThrough.orderStatus', '100', '已出库', '已出库', 100, 1, 0, getdate(), getdate());

update d set d.item_report_name='金富力'
from pp_dict20201112 d where type_code='DistributorCustomer.businessProperty' and item_code='1'

update d set d.item_report_name='德乐'
from pp_dict20201112 d where type_code='DistributorCustomer.businessProperty' and item_code='2'

insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.businessProperty', '4', '工程机械', '工程机械', 3, 1, 0, getdate(), getdate());
insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.businessProperty', '1073741824', '待收集', '-', 100, 1, 0, getdate(), getdate());

insert into pp_dict20201112 (type_code, item_code, item_name, item_report_name, sort_numb, enable_flag, delete_flag, create_time, update_time) values ('DistributorCustomer.customerLabel', '268435456', 'NA', 'NA', 100, 1, 0, getdate(), getdate());

update d set d.item_report_name='合作',item_name='合作'
from pp_dict20201112 d where type_code='DistributorCustomer.customerStatus' and item_code='3'
update d set d.item_report_name='解除合作',item_name='解除合作'
from pp_dict20201112 d where type_code='DistributorCustomer.customerStatus' and item_code='-100'