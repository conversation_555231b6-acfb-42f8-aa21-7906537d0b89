--销量 
EXEC dbo.pp_sp_distributor_performance_template @distributor_id = '251,273,1069',    -- nvarchar(100)
                                                    @sales_cai = NULL,         -- nvarchar(10)
                                                    @region = NULL,            -- nvarchar(100)
                                                    @year = 2020,                -- int
                                                    @end_month = NULL,           -- int
                                                    @customer_category = null, -- nvarchar(50)
                                                    @product_channel = N'consumer,commercial',   -- nvarchar(50)
                                                    @product_category = NULL,  -- nvarchar(50)
                                                    @product_code = NULL,      -- nvarchar(20)
                                                    @output_dim = N'distributor_id,customer_name_cn,product_channel',        -- nvarchar(1000)
                                                    @output_fact = N'act_liters,act_margin_rmb,act_margin_usd,bplan_target_liters,bplan_target_margin,operation_target_liters,operation_target_margin,baseline_target_liters'  ,       -- nvarchar(1000)
                                                    @order_field = NULL,        -- nvarchar(100)
             @by_distributor_flag = 0,
             @is_include_sub_distributor = 0, -- 0：not include sub_distributor ; 1:include sub_distributor ; default =1
             @is_output_union_sub_distributor = 1,-- 0：not include sub_distributor ; 1:include sub_distributor ; default =0
			 @tier_category=null --Premium

			 EXEC dbo.pp_sp_distributor_performance_template @distributor_id = null,    -- nvarchar(100)
                                                    @sales_cai = NULL,         -- nvarchar(10)
                                                    @region = NULL,            -- nvarchar(100)
                                                    @year = 2021,                -- int
                                                    @end_month = NULL,           -- int
                                                    @customer_category = N'consumer,commercial', -- nvarchar(50)
                                                    @product_channel = N'consumer,commercial',   -- nvarchar(50)
                                                    @product_category = NULL,  -- nvarchar(50)
                                                    @product_code = NULL,      -- nvarchar(20)
                                                    @output_dim = N'region,distributor_id,customer_name_cn,product_channel,product_name_cn',        -- nvarchar(1000)
                                                    @output_fact = N'act_liters',       -- nvarchar(1000)
                                                    @order_field = 'region,distributor_id',        -- nvarchar(100)
             @by_distributor_flag = 0,
             @is_include_sub_distributor = 1, -- 0：not include sub_distributor ; 1:include sub_distributor ; default =1
             @is_output_union_sub_distributor =0,-- 0：not include sub_distributor ; 1:include sub_distributor ; default =0
			 @tier_category=null --Premium		
			 
			 
EXEC pp_sp_distributor_expense @distributor_id = 728, -- nvarchar(100)
 @sales_cai = null, -- nvarchar(10)
 @region = null,-- nvarchar(10)
 @product_channel = 'commercial,consumer', -- int
 @expense_code = null,-- int
 @year = 2021, -- nvarchar(50)
 @output_fields = 8187, -- nvarchar(50)
 @order_by = null,-- nvarchar(50)	
 @brand_id=7
 
EXEC pp_sp_distributor_expense_with_permission @distributor_id = null, -- nvarchar(100)
 @sales_cai = null, -- nvarchar(10)
 @region = null,-- nvarchar(10)
 @product_channel = 'commercial,consumer', -- int
 @expense_code = null,-- int
 @year = 2021, -- nvarchar(50)
 @output_fields = 106491, -- nvarchar(50)
 @order_by = null,-- nvarchar(50)	
 @brand_id=7,
 @permission_weight=1	
 
EXEC dbo.pp_sp_distributor_performance_template_with_permission @distributor_id = '251,273,1069',    -- nvarchar(100)
                                                    @sales_cai = NULL,         -- nvarchar(10)
                                                    @region = NULL,            -- nvarchar(100)
                                                    @year = 2021,                -- int
                                                    @end_month = NULL,           -- int
                                                    @product_channel = N'consumer,commercial',   -- nvarchar(50)
                                                    @output_dim = N'distributor_id,customer_name_cn,product_channel',        -- nvarchar(1000)
                                                    @output_fact = N'act_liters,act_margin_rmb,act_margin_usd,bplan_target_liters,bplan_target_margin,operation_target_liters,operation_target_margin,baseline_target_liters'  ,       -- nvarchar(1000)
                                                    @order_field = NULL,        -- nvarchar(100)
			 @sales_channel='Indirect',
			 @tier_category=null,
			 @permission_weight=1,
			 @quarter=null,
			 @month_no=null 