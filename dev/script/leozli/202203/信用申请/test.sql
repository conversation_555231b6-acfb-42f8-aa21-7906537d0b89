<EMAIL>

/user/ctrldata.do?r=0.*****************&appToken=2480e4db7640fee344d94d683b84b99d0e27cfb7&paging=false&userRoleName=Chevron_BD,Chevron_Promote_Sales,Chevron_Industrial_Sales,Chevron_OEM_Sales,Chevron_CDM_Suppervisor,Chevron_Industrial_Supervisor,Chevron_OEM_Supervisor,Chevron_Industrial_Channel_Manager,Chevron_OEM_Channel_Manager
update p set VALUE_='5.18.0.0'
from ACT_GE_PROPERTY p where p.NAME_='schema.version'

ALTER TABLE [wx_t_credit_app_request_form] ALTER COLUMN workflow_status                  int                                        not null default(0)


TRUNCATE TABLE wx_t_credit_app_request_form;
TRUNCATE TABLE wx_t_credit_app_customer_finance_info;
TRUNCATE TABLE wx_t_credit_app_request_from_att;

update wx_t_user set password='f92274ff7c2f4914da79760b2e9ad872ceff0d20', salt='27B157064129F4E5',login_name='bpm', ext_flag=1 where login_name='bpm'

select * from wx_t_user $table.user$ where $table.user$.status=1 and ($table.user$.cai=(case when exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 where credit_bc1.[customer_code]='$soldTo$0')
 then (select top 1 credit_bcs1.supervisor_cai from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_bc1.distributor_id=credit_bcs1.distributor_id where credit_bc1.[customer_code]='8420839' and $table.user$.cai=credit_bcs1.supervisor_cai)
 else (select top 1 credit_c1.supervisor_cai from wx_t_credit_customer_info_final credit_c1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_bc1.distributor_id=credit_bcs1.distributor_id where credit_c1.sold_to='84208390') end))
--select * 
--update cc1 set cc1.sold_to='84208390' 
--from wx_t_credit_customer_info_final cc1 where cc1.status='1' and sold_to!=payer;
 
$table.user$.cai=(case when exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 where credit_bc1.[customer_code]='$soldTo$')
 then (select top 1 credit_bcs1.supervisor_cai from [PP_MID].[dbo].[syn_dw_to_pp_customer] credit_bc1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_bc1.distributor_id=credit_bcs1.distributor_id
 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region where credit_bc1.[customer_code]='$soldTo$' and credit_rs1.bu='$bu$')
 else (select top 1 credit_bcs1.supervisor_cai from wx_t_credit_customer_info_final credit_c1 left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on credit_c1.sales_cai=credit_bcs1.sales_cai where credit_c1.sold_to='$soldTo$' and credit_c1.bu='$bu$') end)
 
 SELECT  
                f.from_distributor_id distributor_id,--窜货经销商
				o.organization_name,
                CASE WHEN r3.channel = 1 THEN 'Consumer' 
                WHEN r3.channel = 2 THEN 'Commercial'
                ELSE 'Others'
                END product_channel, 
                r3.punish_flag, 
                r3.attribute7 quarter,
                CAST( r3.attribute8 AS float ) /100 rate_quarterly,
                CAST( r3.attribute6 AS float ) /100 rate_yearly 
                ,me.deliver_status
                ,me.warn_year warn_year
         FROM      wx_t_Main_Local_marketing m
         LEFT JOIN wx_t_non_local_mkt_flow_form f ON f.report_id  = m.id 
         LEFT JOIN wx_t_non_local_mkt_flow_pro_res r3 ON r3.flow_id = f.id  AND r3.setp_no  ='ABM_CONFIRM' 
         LEFT JOIN wx_t_non_local_mkt_emaile_info me ON me.flow_id=f.id
		 left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=f.from_distributor_id
		 left join wx_t_organization o on o.id=pe.partner_id
         WHERE r3.local_marketing_status = 1 
                        --AND me.deliver_status=2 
                        AND warn_year IN (2022) 
						and f.from_distributor_id=724
                        --AND punish_flag NOT IN (0,1,5)
                        --AND r3.attribute7 IS NOT NULL
$table.user$.user_id!=1 and exists (select 1 from wx_t_userrole credit_ur
left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Analyst' and credit_r.status=1)


$table.user$.user_id!=1 and exists (select 1 from wx_t_userrole credit_ur
left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_r.status=1)

1=(case when exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	then (case when exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='Local_Credit_Team_Lead' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 
		and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
		then 1 else 0 end)
	when exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Analyst' and credit_r.status=1)
	then 1 else 0 end)						

(exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='Local_Credit_Team_Lead' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 )
	or (exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate()))
	and exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Analyst' and credit_r.status=1)))	
	
	

select * from wx_t_user $table.user$ where $table.user$.status=1 and $table.user$.user_id!=1 and
(exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='Local_Credit_Team_Lead' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 )
	or (exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	and exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Analyst' and credit_r.status=1)))	

1=(case when exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	then (case when exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='Local_Credit_Team_Lead' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 
		and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
		then 1 else 0 end)
	when exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Analyst' and credit_r.status=1)
	then 1 else 0 end)
	
	
(exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='Local_Credit_Team_Lead' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 )
	or (exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	and exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Analyst' and credit_r.status=1)))	

	
1=(case when exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='China_Finance_Manager' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	then (case when exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='China_Finance_Manager' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 
		and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
		then 1 else 0 end)
	when exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_r.status=1)
	then 1 else 0 end)
	
(exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='China_Finance_Manager' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 )
	or (exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='China_Finance_Manager' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	and exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_r.status=1)))	
	
$table.user$.user_id!=1 and exists (select 1 from wx_t_userrole credit_ur
left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='China_Finance_Manager' and credit_r.status=1)

1=(case when exists (select 1 from wx_t_role credit_r
	left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
	left join wx_t_user credit_u on credit_u.user_id=credit_ur.user_id where credit_r.ch_role_name='China_Finance_Manager' and credit_u.status=1 and credit_u.user_id!=1 and credit_r.status=1 
	and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
	then (case when exists (select 1 from wx_t_role credit_r
		left join wx_t_userrole credit_ur on credit_r.role_id=credit_ur.role_id
		where credit_r.ch_role_name='China_Finance_Manager' and $table.user$.user_id=credit_ur.user_id and credit_r.status=1 
		and not exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=credit_ur.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time<=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate())))
		then 1 else 0 end)
	when exists (select 1 from wx_t_userrole credit_ur
	left join wx_t_role credit_r on credit_r.role_id=credit_ur.role_id where $table.user$.user_id=credit_ur.user_id and credit_r.ch_role_name='Local_Credit_Team_Lead' and credit_r.status=1)
	then 1 else 0 end)						

	
	
	
select
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '非本地营销经销商',
cs1.region "非本地营销大区",
 (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '销售所属省',
(select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '销售所属市',
ISNULL(dic1.dic_item_name,cs.customer_name_cn) as '被非本地营销影响经销商',
cs.region "被非本地营销影响大区",
m.report_name as '上报人',
case when f.to_flsr_confirm is null then '' when f.to_flsr_confirm = 0 then '否' else '是' end as '被非本地营销影响FLSR是否同意上报',
p.region_name as '实际上报省',
c.region_name as '实际上报市',
convert(nvarchar(20),m.create_time,20) as '上报日期',
year(m.create_time) as '上报年',
month(m.create_time) as '上报月',
day(m.create_time) as '上报日',
d1.dic_item_name as '上报来源',
ISNULL(p1.name,b.product_name) as '产品名称',
bp01.t3_category 'T3 category',
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "省",
        (select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "市",
d2.dic_item_name as '产品渠道',
b.scan_time "出库日期",b.product_time "生产日期",b.product_batch "生产批号",
        b.product_out_no "出库单号",b.logistics_code "物流码",
case when f.form_status = 50 then 'ABM已确认' 
when f.form_status = 35 then '不处理'
when f.from_asm_response = -1 then '未确认' 
when f.from_asm_response = 1 then '已接受' 
when f.from_asm_response = 0 then '待仲裁' 
else '-' end as '判定状态',
case when r3.local_marketing_status = 1 then '是' when r3.local_marketing_status = 0 then '否' else '-' end as '非本地营销（ABM）',
IIF(r3.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r3.punish_flag & 2 > 0 and r3.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r3.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r3.punish_flag & 32 > 0 and r3.attribute8 is not null, '扣除' + r3.attribute3 + '年第' + 
case when r3.attribute7='1' then '一' when r3.attribute7='2' then '二' 
when r3.attribute7='3' then '三'
when r3.attribute7='4' then '四' end + '季度的季度奖励的' + r3.attribute8 + '%;','')
+ IIF(r3.punish_flag & 16 > 0 and r3.attribute6 is not null, '扣除' + r3.attribute3 + '年的年终奖励的' + r3.attribute6 + '%;','')
+ IIF(r3.punish_flag & 4 > 0 and r3.other_reasons is not null,'其他（' + r3.other_reasons +' ）;','') as 'ABM处罚内容',
case when r3.kpi_result = 1 then '合格' when r3.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（ABM）',
r3.handle_info as 'ABM建议',
case when r3.attribute1='1' then '是' else '否' end "处罚文件已确认",
CONVERT(varchar(100), ei.deliver_time, 20) "处罚文件发放时间",
case when r2.local_marketing_status = 1 then '是' when r2.local_marketing_status = 0 then '否' else '-' end as '非本地营销（被非本地营销影响ASM）',
IIF(r2.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r2.punish_flag & 2 > 0 and r2.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r2.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r2.punish_flag & 32 > 0 and r2.attribute8 is not null, '扣除' + r2.attribute3 + '年第' + 
case when r2.attribute7='1' then '一' when r2.attribute7='2' then '二' 
when r2.attribute7='3' then '三'
when r2.attribute7='4' then '四' end + '季度的季度奖励的' + r2.attribute8 + '%;','')
+ IIF(r2.punish_flag & 16 > 0 and r2.attribute6 is not null, '扣除' + r2.attribute3 + '年的年终奖励的' + r2.attribute6 + '%;','')
+ IIF(r2.punish_flag & 4 > 0 and r2.other_reasons is not null,'其他（' + r2.other_reasons +' ）','') as '被非本地营销影响ASM处罚内容',
case when r2.kpi_result = 1 then '合格' when r2.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（被非本地营销影响ASM）',
r2.handle_info as '被非本地营销影响ASM建议',
case when r1.local_marketing_status = 1 then '是' when r1.local_marketing_status = 0 then '否' else '-' end as '非本地营销（非本地营销ASM）',
IIF(r1.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r1.punish_flag & 2 > 0 and r1.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r1.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r1.punish_flag & 32 > 0 and r1.attribute8 is not null, '扣除' + r1.attribute3 + '年第' + 
case when r1.attribute7='1' then '一' when r1.attribute7='2' then '二' 
when r1.attribute7='3' then '三'
when r1.attribute7='4' then '四' end + '季度的季度奖励的' + r1.attribute8 + '%;','')
+ IIF(r1.punish_flag & 16 > 0 and r1.attribute6 is not null, '扣除' + r1.attribute3 + '年的年终奖励的' + r1.attribute6 + '%;','')
+ IIF(r1.punish_flag & 4 > 0 and r1.other_reasons is not null,'其他（' + r1.other_reasons +' ）','') as '非本地营销ASM处罚内容',
case when r1.kpi_result = 1 then '合格' when r1.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（非本地营销ASM）',
r1.handle_info as '非本地营销ASM建议',
case when r0.local_marketing_status = 1 then '是' when r0.local_marketing_status = 0 then '否' else '-' end as '非本地营销（非本地营销FLSR）',
r0.handle_info as '非本地营销FLSR建议'
from wx_t_Main_Local_marketing m
left join wx_t_non_local_mkt_flow_form f on f.report_id  = m.id 
left join wx_t_loc_mkt_product_info b on f.id = b.flow_id
left join wx_t_non_local_mkt_flow_pro_res r0 on r0.flow_id = b.flow_id and r0.channel  = b.channel  and r0.setp_no  = 'FROM_FLSR_SUBMIT_SURVEY'
left join wx_t_non_local_mkt_flow_pro_res r1 on r1.flow_id = b.flow_id and r1.channel  = b.channel  and r1.setp_no in ('FROM_ASM_SUBMIT_SURVEY','GQ_OEM_HANDLER')
left join wx_t_non_local_mkt_flow_pro_res r2 on r2.flow_id = b.flow_id and r2.channel  = b.channel  and r2.setp_no  = 'TO_ASM_SUBMIT_FEEDBACK'
left join wx_t_non_local_mkt_flow_pro_res r3 on r3.flow_id = b.flow_id and r3.channel  = b.channel  and r3.setp_no  = 'ABM_CONFIRM'
left join dw_customer_org_sales cs on cs.distributor_id = f.to_distributor_id and cs.channel_weight&b.auth_channel>0
left join dw_customer_org_sales cs1 on cs1.distributor_id = f.from_distributor_id and cs1.channel_weight&b.auth_channel>0
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = f.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_dic_item dic1 on cast(dic1.dic_item_code as bigint) = f.to_distributor_id and dic1.dic_type_code = 'non_local_special_partner'
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id = f.from_distributor_id
left join wx_t_region c on c.id = m.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = m.reprot_type and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left JOIN PP_MID.dbo.bi_product bp01 ON bp01.product_code_SAP = b.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.display_channel and d2.dic_type_code  = 'NonLocalSales.displayChannel'
left join wx_t_non_local_mkt_emaile_info ei on ei.source_id=r3.id
where f.id is not null
and (#cai# is null or cs.supervisor_cai = #cai# or cs1.supervisor_cai = #cai#)
and (#oemFlag# is null or dic.dic_item_code is not null or dic1.dic_item_code is not null)
union all

select
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '非本地营销经销商',
cs1.region "非本地营销大区",
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '销售所属省',
(select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '销售所属市',
'' as '被非本地营销影响经销商',
'' "被非本地营销影响大区",
'用户扫码验真' as '上报人',
'' as '被非本地营销影响FLSR是否同意上报',
p.region_name as '实际上报省',
c.region_name as '实际上报市',
convert(nvarchar(20),b.report_time,20) as '上报日期',
year(b.report_time) as '上报年',
month(b.report_time) as '上报月',
day(b.report_time) as '上报日',
d1.dic_item_name as '上报来源',
p1.name as '产品名称',
bp01.t3_category 'T3 category',
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "省",
        (select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "市",
d2.dic_item_name as '产品渠道',
convert(nvarchar(20),b.out_date,23) "出库日期",
convert(nvarchar(20),b.product_date,23) "生产日期",b.product_batch "生产批号",
        b.out_order_no "出库单号",b.logistics_code "物流码",
'不处理' as '判定状态',
'-' as '非本地营销（ABM）',
'' as 'ABM处罚内容',
'-' as 'KPI合格（ABM）',
'' as 'ABM建议',
'-' "处罚文件已确认",
'' "处罚文件发放时间",
'-' as '非本地营销（被非本地营销影响ASM）',
'' as '被非本地营销影响ASM处罚内容',
'-' as 'KPI合格（被非本地营销影响ASM）',
'' as '被非本地营销影响ASM建议',
'-' as '非本地营销（非本地营销ASM）',
'' as '非本地营销ASM处罚内容',
'-' as 'KPI合格（非本地营销ASM）',
'' as '非本地营销ASM建议',
'-' as '非本地营销（非本地营销FLSR）',
'' as '非本地营销FLSR建议'
from wx_t_non_local_user_report b
left join dw_customer_org_sales cs1 on cs1.distributor_id = b.from_distributor_id and cs1.channel_weight&b.auth_channel>0
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = b.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_region c on c.id = b.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = '2' and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left JOIN PP_MID.dbo.bi_product bp01 ON bp01.product_code_SAP = b.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.display_channel and d2.dic_type_code  = 'NonLocalSales.displayChannel'
where b.ext_flag&2048>0
and not exists (select 1 from wx_t_loc_mkt_product_info mp1 where mp1.logistics_code=b.logistics_code)
and (#cai# is null or cs1.supervisor_cai = #cai#)
and (#oemFlag# is null or dic.dic_item_code is not null)



selectd1.dic_item_name as '上报来源',m.report_name as '上报人',
convert(nvarchar(20),m.create_time,20) as '上报日期',
p.region_name as '上报省',
c.region_name as '上报市',
ISNULL(p1.name,b.product_name) as '产品名称',
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "省",
        (select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "市",
b.sku SKU,
bp01.t3_category 'T3 category',
b.scan_time "出库日期",b.product_time "生产日期",b.product_batch "生产批号",
        b.product_out_no "出库单号",b.logistics_code "产品物流码",
d2.dic_item_name as '产品渠道',
ISNULL(dic1.dic_item_name,cs.customer_name_cn) as '被非本地营销影响经销商',
cs.region "被非本地营销影响大区",
diac1.dic_item_name "被非本地营销影响授权渠道",
 (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.to_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '被非本地营销影响授权省',
(select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.to_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '被非本地营销影响授权市',
cs.sales_name_cn "被非本地营销影响FLSR",
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '非本地营销经销商',
cs1.region "非本地营销大区",
diac1.dic_item_name "非本地营销影响授权渠道",
 (select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '非本地营销影响授权省',
(select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '非本地营销影响授权市',
cs1.sales_name_cn "非本地营销FLSR",

case when f.to_flsr_confirm is null then '' when f.to_flsr_confirm = 0 then '否' else '是' end as '被非本地营销影响FLSR是否同意上报',
case when f.form_status = 50 then 'ABM已确认' 
when f.form_status = 35 then '不处理'
when f.from_asm_response = -1 then '未确认' 
when f.from_asm_response = 1 then '已接受' 
when f.from_asm_response = 0 then '待仲裁' 
else '-' end as '判定状态',
case when r3.local_marketing_status = 1 then '是' when r3.local_marketing_status = 0 then '否' else '-' end as '非本地营销（ABM）',
IIF(r3.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r3.punish_flag & 2 > 0 and r3.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r3.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r3.punish_flag & 32 > 0 and r3.attribute8 is not null, '扣除' + r3.attribute3 + '年第' + 
case when r3.attribute7='1' then '一' when r3.attribute7='2' then '二' 
when r3.attribute7='3' then '三'
when r3.attribute7='4' then '四' end + '季度的季度奖励的' + r3.attribute8 + '%;','')
+ IIF(r3.punish_flag & 16 > 0 and r3.attribute6 is not null, '扣除' + r3.attribute3 + '年的年终奖励的' + r3.attribute6 + '%;','')
+ IIF(r3.punish_flag & 4 > 0 and r3.other_reasons is not null,'其他（' + r3.other_reasons +' ）;','') as 'ABM处罚内容',
case when r3.kpi_result = 1 then '合格' when r3.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（ABM）',
r3.handle_info as 'ABM建议',
case when r3.attribute1='1' then '是' else '否' end "处罚文件已确认",
CONVERT(varchar(100), ei.deliver_time, 20) "处罚文件发放时间",
case when r2.local_marketing_status = 1 then '是' when r2.local_marketing_status = 0 then '否' else '-' end as '非本地营销（被非本地营销影响ASM）',
IIF(r2.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r2.punish_flag & 2 > 0 and r2.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r2.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r2.punish_flag & 32 > 0 and r2.attribute8 is not null, '扣除' + r2.attribute3 + '年第' + 
case when r2.attribute7='1' then '一' when r2.attribute7='2' then '二' 
when r2.attribute7='3' then '三'
when r2.attribute7='4' then '四' end + '季度的季度奖励的' + r2.attribute8 + '%;','')
+ IIF(r2.punish_flag & 16 > 0 and r2.attribute6 is not null, '扣除' + r2.attribute3 + '年的年终奖励的' + r2.attribute6 + '%;','')
+ IIF(r2.punish_flag & 4 > 0 and r2.other_reasons is not null,'其他（' + r2.other_reasons +' ）','') as '被非本地营销影响ASM处罚内容',
case when r2.kpi_result = 1 then '合格' when r2.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（被非本地营销影响ASM）',
r2.handle_info as '被非本地营销影响ASM建议',
case when r1.local_marketing_status = 1 then '是' when r1.local_marketing_status = 0 then '否' else '-' end as '非本地营销（非本地营销ASM）',
IIF(r1.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(r1.punish_flag & 2 > 0 and r1.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,r1.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(r1.punish_flag & 32 > 0 and r1.attribute8 is not null, '扣除' + r1.attribute3 + '年第' + 
case when r1.attribute7='1' then '一' when r1.attribute7='2' then '二' 
when r1.attribute7='3' then '三'
when r1.attribute7='4' then '四' end + '季度的季度奖励的' + r1.attribute8 + '%;','')
+ IIF(r1.punish_flag & 16 > 0 and r1.attribute6 is not null, '扣除' + r1.attribute3 + '年的年终奖励的' + r1.attribute6 + '%;','')
+ IIF(r1.punish_flag & 4 > 0 and r1.other_reasons is not null,'其他（' + r1.other_reasons +' ）','') as '非本地营销ASM处罚内容',
case when r1.kpi_result = 1 then '合格' when r1.kpi_result = 0 then '不合格' else '-' end as 'KPI合格（非本地营销ASM）',
r1.handle_info as '非本地营销ASM建议',
case when r0.local_marketing_status = 1 then '是' when r0.local_marketing_status = 0 then '否' else '-' end as '非本地营销（非本地营销FLSR）',
r0.handle_info as '非本地营销FLSR建议'
from wx_t_Main_Local_marketing m
left join wx_t_non_local_mkt_flow_form f on f.report_id  = m.id 
left join wx_t_loc_mkt_product_info b on f.id = b.flow_id
left join wx_t_non_local_mkt_flow_pro_res r0 on r0.flow_id = b.flow_id and r0.channel  = b.channel  and r0.setp_no  = 'FROM_FLSR_SUBMIT_SURVEY'
left join wx_t_non_local_mkt_flow_pro_res r1 on r1.flow_id = b.flow_id and r1.channel  = b.channel  and r1.setp_no in ('FROM_ASM_SUBMIT_SURVEY','GQ_OEM_HANDLER')
left join wx_t_non_local_mkt_flow_pro_res r2 on r2.flow_id = b.flow_id and r2.channel  = b.channel  and r2.setp_no  = 'TO_ASM_SUBMIT_FEEDBACK'
left join wx_t_non_local_mkt_flow_pro_res r3 on r3.flow_id = b.flow_id and r3.channel  = b.channel  and r3.setp_no  = 'ABM_CONFIRM'
left join dw_customer_org_sales cs on cs.distributor_id = f.to_distributor_id and cs.channel_weight&b.auth_channel>0
left join dw_customer_org_sales cs1 on cs1.distributor_id = f.from_distributor_id and cs1.channel_weight&b.auth_channel>0
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = f.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_dic_item dic1 on cast(dic1.dic_item_code as bigint) = f.to_distributor_id and dic1.dic_type_code = 'non_local_special_partner'
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id = f.from_distributor_id
left join wx_t_region c on c.id = m.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = m.reprot_type and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left JOIN PP_MID.dbo.bi_product bp01 ON bp01.product_code_SAP = b.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.display_channel and d2.dic_type_code  = 'NonLocalSales.displayChannel'
left join wx_t_non_local_mkt_emaile_info ei on ei.source_id=r3.id
left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=b.auth_channel
where f.id is not null
and (#cai# is null or cs.supervisor_cai = #cai# or cs1.supervisor_cai = #cai#)
and (#oemFlag# is null or dic.dic_item_code is not null or dic1.dic_item_code is not null)
union all
select d1.dic_item_name as '上报来源','用户扫码验真' as '上报人',
convert(nvarchar(20),b.report_time,20) as '上报日期',
p.region_name as '上报省',
c.region_name as '上报市',
p1.name as '产品名称',
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "省",
        (select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as "市",
b.sku,
bp01.t3_category 'T3 category',
convert(nvarchar(20),b.out_date,23) "出库日期",
convert(nvarchar(20),b.product_date,23) "生产日期",b.product_batch "生产批号",
        b.out_order_no "出库单号",b.logistics_code "物流码",
d2.dic_item_name as '产品渠道',
'' as '被非本地营销影响经销商',
'' "被非本地营销影响大区",
'' "被非本地营销影响授权渠道",
'' "被非本地营销影响授权省",
'' "被非本地营销影响授权市",
'' "被非本地营销影响FLSR",
ISNULL(dic.dic_item_name,cs1.customer_name_cn) as '非本地营销经销商',
cs1.region "非本地营销大区",
diac1.dic_item_name "非本地营销影响授权渠道",
(select substring((
        SELECT ','+xx0.province_name  FROM (select distinct r3.region_name province_name from wx_t_region r1 
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '非本地营销影响授权省',
(select substring((
        SELECT ','+xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from  wx_t_region r1
        join wx_t_region r2 on r2.id=r1.parent_id
        join wx_t_region r3 on r3.id=r2.parent_id
        where CHARINDEX(',' + cast(r1.id as varchar) + ',',b.from_distributor_regions)>0) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) as '非本地营销影响授权市',
cs1.sales_name_cn "非本地营销FLSR",

'' as '被非本地营销影响FLSR是否同意上报',
'不处理' as '判定状态',
'-' as '非本地营销（ABM）',
'' as 'ABM处罚内容',
'-' as 'KPI合格（ABM）',
'' as 'ABM建议',
'-' "处罚文件已确认",
'' "处罚文件发放时间",
'-' as '非本地营销（被非本地营销影响ASM）',
'' as '被非本地营销影响ASM处罚内容',
'-' as 'KPI合格（被非本地营销影响ASM）',
'' as '被非本地营销影响ASM建议',
'-' as '非本地营销（非本地营销ASM）',
'' as '非本地营销ASM处罚内容',
'-' as 'KPI合格（非本地营销ASM）',
'' as '非本地营销ASM建议',
'-' as '非本地营销（非本地营销FLSR）',
'' as '非本地营销FLSR建议'
from wx_t_non_local_user_report b
left join dw_customer_org_sales cs1 on cs1.distributor_id = b.from_distributor_id and cs1.channel_weight&b.auth_channel>0
left join wx_t_dic_item dic on cast(dic.dic_item_code as bigint) = b.from_distributor_id and dic.dic_type_code = 'non_local_special_partner'
left join wx_t_region c on c.id = b.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item d1 on d1.dic_item_code = '2' and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_product p1 on b.sku = p1.sku
left JOIN PP_MID.dbo.bi_product bp01 ON bp01.product_code_SAP = b.sku
left join wx_t_dic_item d2 on d2.dic_item_code = b.display_channel and d2.dic_type_code  = 'NonLocalSales.displayChannel'
left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=b.auth_channel
where b.ext_flag&2048>0
and not exists (select 1 from wx_t_loc_mkt_product_info mp1 where mp1.logistics_code=b.logistics_code)
and (#cai# is null or cs1.supervisor_cai = #cai#)
and (#oemFlag# is null or dic.dic_item_code is not null)

select * from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
where module_code='Credit.apply' and r.ch_role_name='Local_Credit_Analyst' and permission_weight&1024>0

http://127.0.0.1:8080/utils/download.do?webpath=true&filePath=/template/credit/03+%E4%BB%98%E6%AC%BE%E6%89%BF%E8%AF%BA%E5%87%BD%EF%BC%88%E9%80%82%E7%94%A8%E4%BA%8E%E5%B9%B4%E5%BA%A6%E4%BF%A1%E7%94%A8%E9%A2%9D%E5%BA%A6%E7%94%B3%E8%AF%B7%EF%BC%89.docx
00 财务报表模板.xlsx
https://wwwstg.cvx-sh.com/downloadAttachmentFile.do?attId=508213&inline=true&appToken=d37888d85742c678fb7fcf4664c5e1c1f5f8850a
02 信用额度申请表.pdf
https://wwwstg.cvx-sh.com/downloadAttachmentFile.do?attId=508214&inline=true&appToken=d37888d85742c678fb7fcf4664c5e1c1f5f8850a

https://view.officeapps.live.com/op/view.aspx?src=https%3A%2F%2Fwwwstg.cvx-sh.com%2FdownloadAttachmentFile.do%3FattId%3D508213%26inline%3Dtrue%26appToken%3Dd37888d85742c678fb7fcf4664c5e1c1f5f8850a