package com.chevron.awardpolicy.controller;

import com.chevron.awardpolicy.business.OilVerAwardPolBizService;
import com.chevron.awardpolicy.model.OilVerAwardPol;
import com.chevron.awardpolicy.model.OilVerAwardPolParams;
import com.chevron.awardpolicy.model.OilVerPolDet;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 核销激励政策Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2017-04-11 17:36
 */
@Controller
@RequestMapping(value="/oilverawardpol")
public class OilVerAwardPolController {
	@Autowired
	private OilVerAwardPolBizService oilVerAwardPolBizService;
	
	private final static Logger log = Logger.getLogger(OilVerAwardPolController.class);
	
	/**
	 * 门店激励政策列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(OilVerAwardPolParams params)
		{
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			oilVerAwardPolBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}
	
	/**
	 * 门店激励政策列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@RequestMapping(value="/page.do")
	public String page(HttpServletRequest request) {
		try {
			request.setAttribute("policy", oilVerAwardPolBizService.getAvailablePolicy());
			return "forward:/business/awardpolicy/oilVerAwardPolPage.jsp";
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return "forward:/common/jsp/error.jsp";
	}
	
	/**
	 * 创建门店激励政策
	 * @param levels 激励等级
	 * @return 操作结果
	 */
	@ResponseBody
	@RequestMapping(value="/create.do")
	public Map<String,Object> create(@RequestParam("levels")String levels)
		{
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			String[] ls = levels.split(",");
			OilVerAwardPol policy = new OilVerAwardPol();
			List<OilVerPolDet> details = new ArrayList<OilVerPolDet>(ls.length);
			for(String level : ls){
				if(StringUtils.isNotBlank(level)){
					String[] lds = level.split("-");
					OilVerPolDet detail = new OilVerPolDet();
					if(lds.length == 1){
						detail.setStartCapacity(Integer.parseInt(lds[0]));
					}else {
						if(StringUtils.isNotBlank(lds[0])){
							detail.setStartCapacity(Integer.parseInt(lds[0]));
						}
						if(StringUtils.isNotBlank(lds[1])){
							detail.setEndCapacity(Integer.parseInt(lds[1]));
						}
					}
					details.add(detail);
				}
			}
			policy.setDetails(details);
			oilVerAwardPolBizService.insert(policy);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}

}
