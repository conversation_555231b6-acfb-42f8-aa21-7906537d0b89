--门店
select o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "门店名称", 
(case when w.status='0' then '潜在' when w.status='3' then '合作'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' when w.status='1' then '已功店' else w.status end) "门店状态",
 case when s.is_pass=1 then '合格' when s.is_pass=0 then '不合格' else '未抽查' end "任务状态",
  case when s.brand=1 then '金富力' when s.brand=2 then '德乐' when s.brand=4 then '工程机械' else convert(nvarchar(10), s.brand) end "品牌",
 s.submit_time "提交时间", s.xg_sj "完成时间"
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
left join wx_t_work_shop w on w.id=s.org_id
left join wx_t_workshop_partner wp on wp.workshop_id=s.org_id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=s.exec_user
where m.tmb_type_code in ('TT_2_7ne/')  and w.partner_id!=9 
and s.task_status = 4 --and s.xg_sj>='2020-04-01' and s.xg_sj<'2020-05-01'
--and w.from_source=1
order by o.id,s.exec_user, w.id

--车队及项目
select o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "客户名称", 
(case when w.status='0' then '潜在' when w.status='3' then '合作'
when w.status='-10' then '待抽查' when w.status='-3' then '不合格'
 when w.status='-5' then '待完善' when w.status='1' then '已功店' else w.status end) "客户状态",
 case when s.brand=1 then '金富力' when s.brand=2 then '德乐' when s.brand=4 then '工程机械' else convert(nvarchar(10), s.brand) end "品牌",
 case when s.is_pass=1 then '合格' when s.is_pass=0 then '不合格' else '未抽查' end "任务状态",
 s.submit_time "提交时间", s.xg_sj "完成时间"
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
left join wx_t_work_shop w on w.id=s.org_id
left join wx_t_workshop_partner wp on wp.workshop_id=s.org_id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=s.exec_user
where m.tmb_type_code in ('TT_2_7nf/')  and w.partner_id!=9 
and s.task_status = 4 --and s.xg_sj>='2020-04-01' and s.xg_sj<'2020-05-01'
--and w.from_source=1
order by o.id,s.exec_user, w.id