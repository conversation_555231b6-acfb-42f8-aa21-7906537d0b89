package com.sys.permission.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;


import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.json.JsonGenerator;
import com.sys.permission.dao.WxModuleMapper;
import com.sys.permission.dao.WxResourceMapper;
import com.sys.permission.dao.WxResourceSqlMapper;
import com.sys.permission.model.BaseDataPermissionTree;
import com.sys.permission.model.WxModule;
import com.sys.permission.model.WxResource;
import com.sys.permission.model.WxResourceSql;
import com.sys.permission.service.DataPermissionService;

@Service 
public class DataPermissionServiceImpl implements DataPermissionService {
	@Resource WxModuleMapper wxModuleMapper;
	private  Logger logger = LoggerFactory.getLogger(DataPermissionServiceImpl.class);
	@Resource WxResourceMapper wxResourceMapper;
	@Resource WxResourceSqlMapper wxResourceSqlMapper;
	
	/**
	 * 初始化数据资源权限页面，加载全部资源树
	 * 
	 */
	public Map<String, Object> findInitialDatapPermissionTree(){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			List<WxModule> dataList = wxModuleMapper.setAllModuleList();
			//List<WxResource> resourceList = wxResourceMapper.setAllResourceList();
			List<BaseDataPermissionTree> treeList = new ArrayList<BaseDataPermissionTree>();
			List<BaseDataPermissionTree> resultJsonList = null;
			if(null != dataList && dataList.size() >0){
				BaseDataPermissionTree tempObj = null;
				for(WxModule data :dataList){
					tempObj = new BaseDataPermissionTree();
					tempObj.setDataType(data.getDataType());
					tempObj.setPid(data.getParentModuleId());
					tempObj.setId(data.getModuleId());
					tempObj.setText(data.getModuleName());
					tempObj.setSequenceNo(data.getSequenceNo());
					tempObj.setResourceTypeCode(data.getResourceTypeCode());
					treeList.add(tempObj);
				}
			}

			resultJsonList = JsonGenerator.datalistToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			// TODO: handle exception
			map.put("code", "systemerror");
			e.printStackTrace();
			
		}
		return map;
	}

	/**
	 * 保存数据资源
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> saveResource(Long moduleId,String resourceName,int sequenceNo,String resourceTypeCode,String[] sqlIdsArraylist,Long resourceId,boolean isadd){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			WxResource resource = new WxResource();
			resource.setModuleId(moduleId);
			resource.setResourceName(resourceName);
			resource.setSequenceNo(sequenceNo);
			resource.setResourceTypeCode(resourceTypeCode);
			resource.setCreatedBy(ContextUtil.getCurUserId());
			resource.setCreationTime(DateUtil.getCurrentDate());
			if(isadd){
				wxResourceMapper.insertSelective(resource);
			}
			else {
				resource.setResourceId(resourceId);
				wxResourceMapper.updateByPrimaryKeySelective(resource);
			}
			if(sqlIdsArraylist != null){
				int sqlLength = sqlIdsArraylist.length;
				wxResourceSqlMapper.deleteByPrimaryKey(resourceId);
				for(int i=0;i<sqlLength;i++){
					WxResourceSql resourceSql = new WxResourceSql();
					resourceSql.setResourceId(resource.getResourceId());
					resourceSql.setSqlid(sqlIdsArraylist[i]);
					resourceSql.setCreatedBy(ContextUtil.getCurUserId());
					wxResourceSqlMapper.insert(resourceSql);
				}
				}
			map.put("code", "success");
			
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "error");
			map.put("errorMessage", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}
	
	/**
	 * 获得更新资源的值
	 */
	public Map<String, Object> getUpdataResource(long resourceId){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			List<WxResourceSql> resourceSql = wxResourceSqlMapper.selectByResourceId(resourceId);
			map.put("code", "success");
			map.put("resultData", resourceSql);
		} catch (Exception e) {
			// TODO: handle exception
			map.put("code", "systemerror");
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
			e.printStackTrace();
		}
		return map;
	}
	

	
	/**
	 * 删除数据
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> deleteData(Long id,int dataType){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			if(dataType == 1){
				wxModuleMapper.deleteByPrimaryKey(id);
			}else{
				List<WxResourceSql> sqlArrayList = null; 
				wxResourceMapper.deleteByPrimaryKey(id);
				sqlArrayList = wxResourceSqlMapper.selectByResourceId(id);
				if(sqlArrayList.size() > 0){
				wxResourceSqlMapper.deleteByPrimaryKey(id);
				}
			}
			map.put("code", "success");
		} catch (Exception e) {
			// TODO: handle exception
			map.put("code", "error");
			map.put("errorMessage", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}

		return map;
	}
	
	/**
	 * 增加模块资源
	 */
	public Map<String, Object> saveModule(Long pid,String moduleName,int sequenceNo,Long id,boolean isadd){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			Long createdBy = ContextUtil.getCurUserId();
			WxModule wxModule = new WxModule();
			if(pid != null){
				wxModule.setParentModuleId(pid);
			}
			if(createdBy != null){
				wxModule.setCreatedBy(createdBy);
			}			
			wxModule.setCreationTime(DateUtil.getCurrentDate());
			wxModule.setModuleName(moduleName);
			wxModule.setSequenceNo(sequenceNo);
			if(isadd){
				wxModule.setEnalbeFlag("Y");
				wxModuleMapper.insertSelective(wxModule);
			}else{
				wxModule.setModuleId(id);
				wxModuleMapper.updateByPrimaryKeySelective(wxModule);
			}
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "error");
			map.put("errorMessage", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
		}
		return map;
	}
	
}
