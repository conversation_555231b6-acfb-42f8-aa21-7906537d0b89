package com.common.util;

import java.io.OutputStream;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Vector;

import com.jcraft.jsch.SftpException;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;

public class SftpConnectionPool {

	/** 最大空闲数 */
	private int maxIdleCount;
	
	/** 最大总连接数 */
	private int maxTotalCount;
	
	private String host;// 服务器连接ip
	private String username;// 用户名
	private String password;// 密码
	private int port = 22;// 端口号
	
	private int connectionSize = 0;
	
	private Queue<SftpConnection> connectionQueue = null;
	
	/**
	 * @param maxIdleCount
	 * @param maxTotalCount
	 * @param host
	 * @param username
	 * @param password
	 * @param port
	 */
	public SftpConnectionPool(int maxIdleCount, int maxTotalCount, String host, String username, String password,
			int port) {
		super();
		this.maxIdleCount = maxIdleCount;
		this.maxTotalCount = maxTotalCount;
		this.host = host;
		this.username = username;
		this.password = password;
		this.port = port;
		connectionQueue = new LinkedList<SftpConnection>();
	}

	public synchronized SftpConnection getConnection() {
		if(connectionQueue.isEmpty()) {
			if(connectionSize < maxTotalCount) {
				//未达到最大连接数
				SftpConnectionWrapper connection = new SftpConnectionWrapper();
				connectionSize++;
				return connection;
			}else {
				try {
					wait();
				} catch (InterruptedException e) {
					e.printStackTrace();
					throw new RuntimeException("获取Sftp连接失败。" + e.getMessage(), e);
				}
				return getConnection();
			}
		}else {
			return connectionQueue.poll();
		}
	}
	
	protected class SftpConnectionWrapper extends SftpConnection {
		private SftpConnection connection;
		
		private boolean disabled = false;
		
		public SftpConnectionWrapper() {
			newConnection();
		}
		
		private SftpConnection newConnection() {
			if(connection != null) {
				final SftpConnection oldConnection = connection;
				LogUtils.addLog(new LogTask() {
					
					@Override
					public void execute() throws Exception {
						oldConnection.close();
					}
				});
			}
			connection = new SftpConnection(host, port, username, password);
			connection.connect();
			return connection;
		}

		@Override
		public void close() {
			synchronized (SftpConnectionPool.this) {
				if(disabled || connectionQueue.size() >= maxIdleCount) {
					connectionSize--;
					connection.close();
				}else {
					connectionQueue.add(this);
				}
				SftpConnectionPool.this.notifyAll();
			}
		}

		@Override
		public void connect() {
			//统一处理，只连接一次
		}

		@Override
		public List<String> batchDownLoadFile(String remotePath, String localPath, String fileFormat,
				String fileEndFormat, boolean del) {
			try {
				return connection.batchDownLoadFile(remotePath, localPath, fileFormat, fileEndFormat, del);
			} catch (RuntimeException e) {
				try {
					return newConnection().batchDownLoadFile(remotePath, localPath, fileFormat, fileEndFormat, del);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public boolean downloadFile(String remotePath, String remoteFileName, String localPath, String localFileName) {
			try {
				return connection.downloadFile(remotePath, remoteFileName, localPath, localFileName);
			} catch (RuntimeException e) {
				try {
					return newConnection().downloadFile(remotePath, remoteFileName, localPath, localFileName);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}
		
		public boolean downloadFile(String filePath, OutputStream outputStream) {
			if(!connection.downloadFile(filePath, outputStream)) {
				if(!newConnection().downloadFile(filePath, outputStream)) {
					disabled = true;
					return false;
				}
			}
			return true;
		}

		@Override
		public boolean uploadFile(String remotePath, String remoteFileName, String localPath, String localFileName) {
			try {
				return connection.uploadFile(remotePath, remoteFileName, localPath, localFileName);
			} catch (RuntimeException e) {
				try {
					return newConnection().uploadFile(remotePath, remoteFileName, localPath, localFileName);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public boolean bacthUploadFile(String remotePath, String localPath, boolean del) {
			try {
				return connection.bacthUploadFile(remotePath, localPath, del);
			} catch (RuntimeException e) {
				try {
					return newConnection().bacthUploadFile(remotePath, localPath, del);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public boolean deleteFile(String filePath) {
			try {
				return connection.deleteFile(filePath);
			} catch (RuntimeException e) {
				try {
					return newConnection().deleteFile(filePath);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public boolean createDir(String createpath) {
			try {
				return connection.createDir(createpath);
			} catch (RuntimeException e) {
				try {
					return newConnection().createDir(createpath);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public boolean isDirExist(String directory) {
			try {
				return connection.isDirExist(directory);
			} catch (RuntimeException e) {
				try {
					return newConnection().isDirExist(directory);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public void deleteSFTP(String directory, String deleteFile) {
			try {
				connection.deleteSFTP(directory, deleteFile);
			} catch (RuntimeException e) {
				try {
					newConnection().deleteSFTP(directory, deleteFile);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public void mkdirs(String path) {
			try {
				connection.mkdirs(path);
			} catch (RuntimeException e) {
				try {
					newConnection().mkdirs(path);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}

		@Override
		public Vector<?> listFiles(String directory) throws SftpException {
			try {
				return connection.listFiles(directory);
			} catch (RuntimeException e) {
				try {
					return newConnection().listFiles(directory);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			} catch (SftpException e) {
				try {
					return newConnection().listFiles(directory);
				} catch (RuntimeException e2) {
					disabled = true;
					throw e;
				}
			}
		}
		
		@Override
		protected void finalize() throws Throwable {
			close();
			super.finalize();
		}
	}
}
