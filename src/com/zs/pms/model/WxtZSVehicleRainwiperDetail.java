package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleRainwiperDetail {
    private Long id;

    private String tModelId;

    private String rainwiperName;

    private String rainwiperPartsNo;

    private String rainwiperType;

    private String rainwiperSize;

    private String rainwiperInterface;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getRainwiperName() {
        return rainwiperName;
    }

    public void setRainwiperName(String rainwiperName) {
        this.rainwiperName = rainwiperName == null ? null : rainwiperName.trim();
    }

    public String getRainwiperPartsNo() {
        return rainwiperPartsNo;
    }

    public void setRainwiperPartsNo(String rainwiperPartsNo) {
        this.rainwiperPartsNo = rainwiperPartsNo == null ? null : rainwiperPartsNo.trim();
    }

    public String getRainwiperType() {
        return rainwiperType;
    }

    public void setRainwiperType(String rainwiperType) {
        this.rainwiperType = rainwiperType == null ? null : rainwiperType.trim();
    }

    public String getRainwiperSize() {
        return rainwiperSize;
    }

    public void setRainwiperSize(String rainwiperSize) {
        this.rainwiperSize = rainwiperSize == null ? null : rainwiperSize.trim();
    }

    public String getRainwiperInterface() {
        return rainwiperInterface;
    }

    public void setRainwiperInterface(String rainwiperInterface) {
        this.rainwiperInterface = rainwiperInterface == null ? null : rainwiperInterface.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}