<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialApplicationDetailVoMapper">
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialApplicationDetailVo">
	<id column="APPLICATION_ID" jdbcType="BIGINT" property="applicationId" />
	<id column="MATERIAL_ID" jdbcType="BIGINT" property="materialId" />
	<id column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
	<result column="APPLICATION_QTY" jdbcType="BIGINT" property="applicationQty" />
	<result column="LOCKED_QTY" jdbcType="BIGINT" property="lockedQty" />
	<result column="COMMENTS" jdbcType="NVARCHAR" property="comments" />
	<result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
	<result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
	<result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
	<result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
	<result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
	<result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
	<result column="MATERIAL_PRICE" jdbcType="NUMERIC" property="materialPrice" />
	<result column="WAREHOUSE_ID" jdbcType="BIGINT" property="warehouseId" />
	<result column="WAREHOUSE_NAME" jdbcType="BIGINT" property="warehouseName" />

<!--  附带物料信息 -->
	<result column="MATERIAL_CODE" property="materialCode" jdbcType="NVARCHAR" />
	<result column="SMC_MATERIAL_CODE" property="smcMaterialCode" jdbcType="NVARCHAR" />
	<result column="MATERIAL_TYPE" property="materialType" jdbcType="NVARCHAR" />
	<result column="MATERIAL_TYPE_NAME" property="materialTypeName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_NAME" property="materialName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_DESC" property="materialDesc" jdbcType="NVARCHAR" />
	<result column="MATERIAL_IMAGES" property="materialImages" jdbcType="BIGINT" />
	<result column="MATERIAL_PRICE_NOW" property="materialPriceNow" jdbcType="NUMERIC" />
	<result column="MATERIAL_UNIT" property="materialUnit" jdbcType="NVARCHAR" />
	<result column="MATERIAL_SOURCE" property="materialSource" jdbcType="NVARCHAR" />
	<result column="MATERIAL_CATEGORY" property="materialCategory" jdbcType="NVARCHAR" />
	<result column="OVER_LIMIT" property="overLimit" jdbcType="BIT" />
	<result column="IS_LEFTOVER" property="isLeftover" jdbcType="BIT" />
	<result column="IS_PRESALE" property="isPresale" jdbcType="BIT" />
	<result column="SHOW_STOCK_QTY" property="showStockQty" jdbcType="BIT" />
	<result column="POINT_MODE" property="pointMode" jdbcType="BIT" />
	<result column="LIMIT_QTY_PER_ORDER" jdbcType="BIGINT" property="limitQtyPerOrder" />
	<result column="LIMIT_QTY_PER_YEAR" jdbcType="BIGINT" property="limitQtyPerYear" />
	<!-- 库存信息  -->
	<result column="STOCK_QTY" property="stockQty" jdbcType="NVARCHAR" />
	<result column="VIRTUAL_STOCK_QTY" property="virtualStockQty" jdbcType="NVARCHAR" />
	<result column="WAREHOUSE_OUT_ID" jdbcType="BIGINT" property="warehouseOutId" />

</resultMap>
<sql id="Example_Where_Clause">
	<where>
	<foreach collection="oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause">
	<where>
	<foreach collection="example.oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List">
	APPLICATION_ID, MATERIAL_ID, MATERIAL_SKU_CODE, APPLICATION_QTY, COMMENTS, DELETE_FLAG,
	ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY,
	MATERIAL_PRICE, WAREHOUSE_ID
</sql>
<select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVoExample" resultMap="BaseResultMap">
	select
	<if test="distinct">
	distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material_application_detail
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null">
	order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVoKey" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_material_application_detail
	where APPLICATION_ID = #{applicationId,jdbcType=BIGINT}
	and MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</select>
<delete id="deleteByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVoKey">
	delete from wx_t_material_application_detail
	where APPLICATION_ID = #{applicationId,jdbcType=BIGINT}
	and MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</delete>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVoExample">
	delete from wx_t_material_application_detail
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVo">
	insert into wx_t_material_application_detail (APPLICATION_ID, MATERIAL_ID, MATERIAL_SKU_CODE,
	APPLICATION_QTY, COMMENTS, DELETE_FLAG,
	ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME,
	CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY,
	MATERIAL_PRICE, WAREHOUSE_ID)
	values (#{applicationId,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{materialSkuCode,jdbcType=NVARCHAR},
	#{applicationQty,jdbcType=BIGINT}, #{comments,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT},
	#{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP},
	#{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT},
	#{materialPrice,jdbcType=NUMERIC}, #{warehouseId,jdbcType=BIGINT})
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVo">
	insert into wx_t_material_application_detail
	<trim prefix="(" suffix=")" suffixOverrides=",">
	<if test="applicationId != null">
		APPLICATION_ID,
	</if>
	<if test="materialId != null">
		MATERIAL_ID,
	</if>
	<if test="materialSkuCode != null">
		MATERIAL_SKU_CODE,
	</if>
	<if test="applicationQty != null">
		APPLICATION_QTY,
	</if>
	<if test="comments != null">
		COMMENTS,
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2,
	</if>
	<if test="creationTime != null">
		CREATION_TIME,
	</if>
	<if test="createdBy != null">
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY,
	</if>
	<if test="materialPrice != null">
		MATERIAL_PRICE,
	</if>
	<if test="warehouseId != null">
		WAREHOUSE_ID,
	</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
	<if test="applicationId != null">
		#{applicationId,jdbcType=BIGINT},
	</if>
	<if test="materialId != null">
		#{materialId,jdbcType=BIGINT},
	</if>
	<if test="materialSkuCode != null">
		#{materialSkuCode,jdbcType=NVARCHAR},
	</if>
	<if test="applicationQty != null">
		#{applicationQty,jdbcType=BIGINT},
	</if>
	<if test="comments != null">
		#{comments,jdbcType=NVARCHAR},
	</if>
	<if test="deleteFlag != null">
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		#{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null">
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="materialPrice != null">
		#{materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="warehouseId != null">
		#{warehouseId,jdbcType=BIGINT},
	</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map">
	update wx_t_material_application_detail
	<set>
	<if test="record.applicationId != null">
		APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
	</if>
	<if test="record.materialId != null">
		MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	</if>
	<if test="record.materialSkuCode != null">
		MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.applicationQty != null">
		APPLICATION_QTY = #{record.applicationQty,jdbcType=BIGINT},
	</if>
	<if test="record.comments != null">
		COMMENTS = #{record.comments,jdbcType=NVARCHAR},
	</if>
	<if test="record.deleteFlag != null">
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null">
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null">
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="record.creationTime != null">
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null">
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null">
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null">
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="record.materialPrice != null">
		MATERIAL_PRICE = #{record.materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="record.warehouseId != null">
		WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
	</if>
	</set>
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map">
	update wx_t_material_application_detail
	set APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
	MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	APPLICATION_QTY = #{record.applicationQty,jdbcType=BIGINT},
	COMMENTS = #{record.comments,jdbcType=NVARCHAR},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	MATERIAL_PRICE = #{record.materialPrice,jdbcType=NUMERIC},
	WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT}
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVo">
	update wx_t_material_application_detail
	<set>
	<if test="applicationQty != null">
		APPLICATION_QTY = #{applicationQty,jdbcType=BIGINT},
	</if>
	<if test="comments != null">
		COMMENTS = #{comments,jdbcType=NVARCHAR},
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null">
		CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		CREATED_BY = #{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="materialPrice != null">
		MATERIAL_PRICE = #{materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="warehouseId != null">
		WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT},
	</if>
	</set>
	where APPLICATION_ID = #{applicationId,jdbcType=BIGINT}
	and MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</update>
<update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialApplicationDetailVo">
	update wx_t_material_application_detail
	set APPLICATION_QTY = #{applicationQty,jdbcType=BIGINT},
	COMMENTS = #{comments,jdbcType=NVARCHAR},
	DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	MATERIAL_PRICE = #{materialPrice,jdbcType=NUMERIC},
	WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
	where APPLICATION_ID = #{applicationId,jdbcType=BIGINT}
	and MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</update>

<!-- 查询申请单行信息, 附带物料信息 -->
<select id="selectWithMaterialInfo" resultMap="BaseResultMap" parameterType="java.lang.Long" >
	select
	mad.APPLICATION_ID, mad.MATERIAL_ID, mad.MATERIAL_SKU_CODE, mad.APPLICATION_QTY,mad.APPLICATION_QTY LOCKED_QTY, mad.COMMENTS, mad.DELETE_FLAG, mad.ATTRIBUTE1,
	mad.ATTRIBUTE2, mad.CREATION_TIME, mad.CREATED_BY, mad.LAST_UPDATE_TIME, mad.LAST_UPDATED_BY,mad.MATERIAL_PRICE, mad.WAREHOUSE_ID, mw.WAREHOUSE_NAME,
	m.MATERIAL_CODE, m.SMC_MATERIAL_CODE, m.MATERIAL_TYPE, m.MATERIAL_NAME, m.MATERIAL_DESC, m.MATERIAL_SOURCE, m.MATERIAL_CATEGORY,m.OVER_LIMIT,
	m.IS_LEFTOVER,m.IS_PRESALE,m.SHOW_STOCK_QTY,m.POINT_MODE,
	MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path('')),
	m.MATERIAL_PRICE MATERIAL_PRICE_NOW, m.MATERIAL_UNIT,
	di.DIC_ITEM_NAME MATERIAL_TYPE_NAME,
	s.LIMIT_QTY LIMIT_QTY_PER_ORDER, s.TRADE_QTY LIMIT_QTY_PER_YEAR,
	STOCK_QTY = (select mi.STOCK_QTY from wx_t_material_inventory mi where mi.MATERIAL_ID=mad.MATERIAL_ID and mi.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE and mi.WAREHOUSE_ID = mad.WAREHOUSE_ID),
	VIRTUAL_STOCK_QTY = (select mi.VIRTUAL_STOCK_QTY from wx_t_material_inventory mi where mi.MATERIAL_ID=mad.MATERIAL_ID and mi.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE and mi.WAREHOUSE_ID = mad.WAREHOUSE_ID)
	from wx_t_material_application_detail mad
	left join wx_t_material m on m.ID = mad.MATERIAL_ID
	left join wx_t_material_sku s on s.MATERIAL_ID = mad.MATERIAL_ID and s.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE
	left join wx_t_material_warehouse mw on mad.warehouse_id = mw.id
	left join wx_t_dic_item di on di.DIC_TYPE_CODE = 'material.type' and di.DIC_ITEM_CODE = m.MATERIAL_TYPE
	left join wx_t_material_application ma1 on ma1.id=mad.APPLICATION_ID
	where mad.DELETE_FLAG = 0 and ma1.version_no=0
	and mad.APPLICATION_ID in (${applicationId})
	
	union all select
	mad.APPLICATION_ID, mad.MATERIAL_ID, mad.MATERIAL_SKU_CODE + '-' + (case when len(md1.material_color)>0 then md1.material_color else '默认' end) + '-' + (case when len(md1.material_size)>0 then md1.material_size else '均码' end) MATERIAL_SKU_CODE, 
	mad.APPLICATION_QTY,mad.APPLICATION_QTY LOCKED_QTY, mad.COMMENTS, mad.DELETE_FLAG, mad.ATTRIBUTE1,
	mad.ATTRIBUTE2, mad.CREATION_TIME, mad.CREATED_BY, mad.LAST_UPDATE_TIME, mad.LAST_UPDATED_BY,mad.MATERIAL_PRICE, mad.WAREHOUSE_ID, s1.supplier_name WAREHOUSE_NAME,
	m1.MATERIAL_CODE, null SMC_MATERIAL_CODE, md1.material_tag MATERIAL_TYPE, md1.material_name MATERIAL_NAME, md1.MATERIAL_DESC, 
	convert(nvarchar(20),m1.supplier_id) MATERIAL_SOURCE, null MATERIAL_CATEGORY,null OVER_LIMIT,
	0 IS_LEFTOVER,0 IS_PRESALE,null SHOW_STOCK_QTY,1 POINT_MODE,
	convert(nvarchar(100),(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = m1.id and af.SOURCE_TYPE = '58' order by af.create_time asc)) MATERIAL_IMAGES,
	pm1.point_value MATERIAL_PRICE_NOW, md1.material_unit MATERIAL_UNIT,
	(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) MATERIAL_TYPE_NAME,
	null LIMIT_QTY_PER_ORDER, null LIMIT_QTY_PER_YEAR, i1.current_quantity STOCK_QTY, i1.current_quantity VIRTUAL_STOCK_QTY
	from wx_t_material_application_detail mad
	left join wx_t_material_application ma1 on ma1.id=mad.APPLICATION_ID
	left join wx_t_point_material pm1 on pm1.id=mad.MATERIAL_ID
		  left join wx_t_material2021 m1 on pm1.material_id=m1.id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
	where mad.DELETE_FLAG = 0 and ma1.version_no>=202109
	and mad.APPLICATION_ID in (${applicationId})
</select>


	<!--   批量插入申请单行信息 -->
<insert id="insertBatch" parameterType="java.util.List">
	insert into wx_t_material_application_detail (
	APPLICATION_ID,
	MATERIAL_ID,
	MATERIAL_SKU_CODE,
	APPLICATION_QTY,
	COMMENTS,
	DELETE_FLAG,
	ATTRIBUTE1,
	ATTRIBUTE2,
	CREATION_TIME,
	CREATED_BY,
	LAST_UPDATE_TIME,
	LAST_UPDATED_BY,
	MATERIAL_PRICE,
	WAREHOUSE_ID)
	values
	<foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.applicationId,jdbcType=BIGINT},
			#{item.materialId,jdbcType=BIGINT},
			#{item.materialSkuCode,jdbcType=BIGINT},
			#{item.applicationQty,jdbcType=BIGINT},
			#{item.comments,jdbcType=NVARCHAR},
			#{item.deleteFlag,jdbcType=BIT},
			#{item.attribute1,jdbcType=NVARCHAR},
			#{item.attribute2,jdbcType=NVARCHAR},
			#{item.creationTime,jdbcType=TIMESTAMP},
			#{item.createdBy,jdbcType=BIGINT},
			#{item.lastUpdateTime,jdbcType=TIMESTAMP},
			#{item.lastUpdatedBy,jdbcType=BIGINT},
			#{item.materialPrice,jdbcType=NUMERIC},
			#{item.warehouseId,jdbcType=BIGINT}
		</trim>
	</foreach>
</insert>
<select id="countPendingQty4SKU" resultType="java.lang.Long"  parameterType="map" >
	SELECT SUM (d.APPLICATION_QTY)
	FROM wx_t_material_application_detail d
	LEFT JOIN wx_t_material_application a ON a.id = d.APPLICATION_ID
	WHERE a.application_status IN ('PENDING_ON_L1','PENDING_ON_L2','READY','APPROVED')
	AND d.MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	AND d.MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</select>
<select id="countPendingQty4Material" resultType="java.lang.Long"  parameterType="java.lang.Long" >
	SELECT SUM (d.APPLICATION_QTY)
	FROM wx_t_material_application_detail d
	LEFT JOIN wx_t_material_application a ON a.id = d.APPLICATION_ID
	WHERE a.application_status IN ('PENDING_ON_L1','PENDING_ON_L2','READY','APPROVED')
	AND d.MATERIAL_ID = #{materialId,jdbcType=BIGINT}
</select>

<select id="countShoppingCartQty" resultType="java.lang.Long"  parameterType="java.lang.Long" >
	SELECT SUM (d.APPLICATION_QTY)
	FROM wx_t_material_application_detail d
	LEFT JOIN wx_t_material_application a ON a.id = d.APPLICATION_ID
	WHERE a.application_status IN ('DRAFT')
	<if test="pointType != null">
	and a.application_type=#{pointType}
	</if>
	AND a.APPLICATION_ORG_ID = #{applicationOrgId,jdbcType=BIGINT}
</select>
<!--这个是按照目前最新的积分计算某个订单的totalPrice-->
<select id="calcAppTotalPrice" resultType="java.lang.Double"  parameterType="java.lang.Long" >
	SELECT SUM (d.APPLICATION_QTY * m.MATERIAL_PRICE)
	FROM wx_t_material_application_detail d
	LEFT JOIN wx_t_material m ON m.id = d.MATERIAL_ID
	WHERE 1=1
	AND d.APPLICATION_ID = #{applicationId,jdbcType=BIGINT}
</select>
<!--这个是按照目前最新的积分计算某个订单的totalPrice-->
<select id="calcAppHistoryTotalPrice" resultType="java.lang.Double"  parameterType="java.lang.Long" >
	SELECT SUM (d.APPLICATION_QTY * d.MATERIAL_PRICE)
	FROM wx_t_material_application_detail d
	WHERE 1=1
	AND d.APPLICATION_ID = #{applicationId,jdbcType=BIGINT}
</select>
<!--这个是查询历史的审批某种商品的个数的记录-->
<select id="selectHistoryTotal" resultType="java.math.BigDecimal"  parameterType="map" >
	SELECT SUM (d.APPLICATION_QTY)
	FROM wx_t_material_application_detail d
	LEFT JOIN wx_t_material_application ma
	ON ma.id = d.APPLICATION_ID
	WHERE 1=1
	AND ma.DELETE_FLAG != 1
	AND ma.APPLICATION_STATUS not in ('DRAFT', 'REJECTED', 'RETURNED')
	AND ma.APPLICATION_TIME &gt; DATEADD(YEAR,DATEDIFF(YEAR,0,GETDATE()),0)
	AND d.MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	<choose>
		<when test='applicationType == "b2b"'>
			AND ma.APPLICATION_USER_ID = #{userId,jdbcType=NVARCHAR}
		</when>
		<otherwise>
			AND ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
		</otherwise>
	</choose>
</select>
<!--这个是查询那个partnerId明细，状态是PENDING_4_URGENT，applicationType的订单的总和，算这个是历史价格的总和是对的-->
<select id="queryPendingOrderPointTotal" resultType="java.math.BigDecimal" parameterType="map">
	select sum(de.APPLICATION_QTY*MATERIAL_PRICE) as totalPrice 
	from dbo.wx_t_material_application_detail de
	left join dbo.wx_t_material_application ap on ap.ID=de.APPLICATION_ID
	where 1=1
	and ap.APPLICATION_STATUS='PENDING_4_URGENT'
	and ap.DELETE_FLAG=0
	and de.DELETE_FLAG=0
	and ap.APPLICATION_TYPE=#{applicationType}
	and ap.APPLICATION_ORG_ID=#{partnerId}
	<if test='applicationType == "promotion" and bizType == "CALTEX_POINT_FROM_PROMOTE"'>
		AND ap.BUSINESS_TYPE_CODE = 'CALTEX_POINT_FROM_PROMOTE'
	</if>
	<if test='applicationType == "promotion" and bizType != "CALTEX_POINT_FROM_PROMOTE"'>
		AND (ap.BUSINESS_TYPE_CODE != 'CALTEX_POINT_FROM_PROMOTE' OR ap.BUSINESS_TYPE_CODE IS NULL)
	</if>
	<if test='applicationType == "b2b"'>
       AND ap.BUSINESS_TYPE_CODE = #{bizType, jdbcType=NVARCHAR}
	</if>
</select>

<!-- 查询申请单行信息, 附带物料信息 -->
<select id="selectLinesByWarehouseOutId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
select
mad.APPLICATION_ID, mad.MATERIAL_ID, mad.MATERIAL_SKU_CODE, mad.APPLICATION_QTY,mad.APPLICATION_QTY LOCKED_QTY, mad.COMMENTS, mad.DELETE_FLAG, mad.ATTRIBUTE1,
mad.ATTRIBUTE2, mad.CREATION_TIME, mad.CREATED_BY, mad.LAST_UPDATE_TIME, mad.LAST_UPDATED_BY,mad.MATERIAL_PRICE, mad.WAREHOUSE_ID, mw.WAREHOUSE_NAME,
m.MATERIAL_CODE, m.SMC_MATERIAL_CODE, m.MATERIAL_TYPE, m.MATERIAL_NAME, m.MATERIAL_DESC, m.MATERIAL_SOURCE, m.MATERIAL_CATEGORY,m.OVER_LIMIT,
m.IS_LEFTOVER,m.IS_PRESALE,m.SHOW_STOCK_QTY,m.POINT_MODE,
MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path('')),
m.MATERIAL_PRICE MATERIAL_PRICE_NOW, m.MATERIAL_UNIT,
di.DIC_ITEM_NAME MATERIAL_TYPE_NAME,
s.LIMIT_QTY LIMIT_QTY_PER_ORDER, s.TRADE_QTY LIMIT_QTY_PER_YEAR,
STOCK_QTY = (select mi.STOCK_QTY from wx_t_material_inventory mi where mi.MATERIAL_ID=mad.MATERIAL_ID and mi.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE and mi.WAREHOUSE_ID = mad.WAREHOUSE_ID),
VIRTUAL_STOCK_QTY = (select mi.VIRTUAL_STOCK_QTY from wx_t_material_inventory mi where mi.MATERIAL_ID=mad.MATERIAL_ID and mi.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE and mi.WAREHOUSE_ID = mad.WAREHOUSE_ID)
,wod.WAREHOUSE_OUT_ID from wx_t_material_application_detail mad
	left join wx_t_material_application ma1 on ma1.id=mad.APPLICATION_ID
inner join wx_t_material_warehouse_out wo on wo.APPLICATION_ID  = mad.APPLICATION_ID
inner join (select distinct WAREHOUSE_OUT_ID,MATERIAL_ID from wx_t_material_warehouse_out_detail) wod on wo.ID  = wod.WAREHOUSE_OUT_ID and mad.MATERIAL_ID = wod.MATERIAL_ID
left join wx_t_material m on m.ID = mad.MATERIAL_ID
left join wx_t_material_sku s on s.MATERIAL_ID = mad.MATERIAL_ID and s.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE
left join wx_t_material_warehouse mw on mad.warehouse_id = mw.id
left join wx_t_dic_item di on di.DIC_TYPE_CODE = 'material.type' and di.DIC_ITEM_CODE = m.MATERIAL_TYPE
where mad.DELETE_FLAG = 0 and ma1.version_no=0
and wod.WAREHOUSE_OUT_ID in (${warehouseOutId})

union all
select
mad.APPLICATION_ID, mad.MATERIAL_ID, wod.MATERIAL_SKU_CODE MATERIAL_SKU_CODE, mad.APPLICATION_QTY,mad.APPLICATION_QTY LOCKED_QTY, mad.COMMENTS, mad.DELETE_FLAG, mad.ATTRIBUTE1,
mad.ATTRIBUTE2, mad.CREATION_TIME, mad.CREATED_BY, mad.LAST_UPDATE_TIME, mad.LAST_UPDATED_BY,mad.MATERIAL_PRICE, mad.WAREHOUSE_ID, s1.supplier_name WAREHOUSE_NAME,
m1.MATERIAL_CODE, null SMC_MATERIAL_CODE, md1.material_tag MATERIAL_TYPE, md1.material_name MATERIAL_NAME, md1.MATERIAL_DESC MATERIAL_DESC, convert(nvarchar(20),m1.supplier_id) MATERIAL_SOURCE, null MATERIAL_CATEGORY,null OVER_LIMIT,
0 IS_LEFTOVER,0 IS_PRESALE,null SHOW_STOCK_QTY,1 POINT_MODE,
	convert(nvarchar(100),(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = m1.id and af.SOURCE_TYPE = '58' order by af.create_time asc)) MATERIAL_IMAGES,
	pm1.point_value MATERIAL_PRICE_NOW, md1.material_unit MATERIAL_UNIT,
	(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) MATERIAL_TYPE_NAME,
	null LIMIT_QTY_PER_ORDER, null LIMIT_QTY_PER_YEAR, i1.current_quantity STOCK_QTY, i1.current_quantity VIRTUAL_STOCK_QTY
,wod.WAREHOUSE_OUT_ID from wx_t_material_application_detail mad
	left join wx_t_material_application ma1 on ma1.id=mad.APPLICATION_ID
inner join wx_t_material_warehouse_out wo on wo.APPLICATION_ID  = mad.APPLICATION_ID
inner join (select distinct WAREHOUSE_OUT_ID,MATERIAL_ID,MATERIAL_SKU_CODE from wx_t_material_warehouse_out_detail) wod on wo.ID  = wod.WAREHOUSE_OUT_ID and mad.MATERIAL_ID = wod.MATERIAL_ID
	left join wx_t_point_material pm1 on pm1.id=mad.MATERIAL_ID
		  left join wx_t_material2021 m1 on pm1.material_id=m1.id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
where mad.DELETE_FLAG = 0 and ma1.version_no>=202109
and wod.WAREHOUSE_OUT_ID in (${warehouseOutId})
</select>
</mapper>