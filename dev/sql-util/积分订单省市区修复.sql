select ADDRESS_DETAIL,* 
--update wo set wo.ADDRESS_REGION=440606
from wx_t_material_warehouse_out wo where wo.id=5813

SELECT * FROM ( SELECT TOP 20 nest_table3.* FROM ( SELECT row_number() OVER(ORDER BY creation_time DESC) AS rownumber, 
nest_table2.* FROM ( select wo.*, ADDRESS_REGION_NAME= (SELECT province.region_name + '-' + city.region_name +'-'+ dist.region_name 
FROM wx_t_region dist LEFT JOIN wx_t_region city ON dist.parent_id = city.id LEFT JOIN wx_t_region province ON city.parent_id = province.id 
WHERE dist.region_code = wo.ADDRESS_REGION), o.organization_name APPLICATION_ORG_NAME, u.CH_NAME APPLICATION_PERSON_NAME 
from wx_t_material_warehouse_out wo left join wx_t_user u on wo.APPLICATION_PERSON_ID = u.user_id LEFT JOIN wx_t_organization o 
on wo.APPLICATION_ORG_ID = o.id where wo.delete_flag=0 and wo.STATUS in ( 'PENDING_ON_OUTBOUND' ) and wo.ADDRESS_REGION is null and 
wo.application_type in ( 'dsr_cio' ) and ( wo.APPLICATION_NAME like '%'+''+'%' or wo.APPLICATION_CODE like '%'+''+'%' or u.CH_NAME like '%'+''+'%' )
 ) nest_table2 ) nest_table3 WHERE rownumber > 0 ) nest_table4 ORDER BY creation_time DESC
 
 select ma.*, o.organization_name APPLICATION_ORG_NAME, case when ma.APPLICATION_TYPE='b2b' then (select we.name from wx_t_workshop_employee we where we.code=ma.APPLICATION_USER_ID) else (select u.CH_NAME from wx_t_user u where ma.APPLICATION_PERSON_ID = u.user_id) end APPLICATION_PERSON_NAME, (select u.CH_NAME from wx_t_user u where ma.APPLICATION_PERSON_ID = u.user_id) as ACTUAL_APPLICATION_USER_NAME from wx_t_material_application ma LEFT JOIN wx_t_organization o on ma.APPLICATION_ORG_ID = o.id where ma.ID = '5005'
select ma.*, o.organization_name APPLICATION_ORG_NAME, case when ma.APPLICATION_TYPE='b2b' then (select we.name from wx_t_workshop_employee we where we.code=ma.APPLICATION_USER_ID) else (select u.CH_NAME from wx_t_user u where ma.APPLICATION_PERSON_ID = u.user_id) end APPLICATION_PERSON_NAME, (select u.CH_NAME from wx_t_user u where ma.APPLICATION_PERSON_ID = u.user_id) as ACTUAL_APPLICATION_USER_NAME 
--update ma set ma.ADDRESS_REGION=440606
from wx_t_material_application ma LEFT JOIN wx_t_organization o on ma.APPLICATION_ORG_ID = o.id where ma.ID = '5855'

select * from wx_t_material_shipping_address