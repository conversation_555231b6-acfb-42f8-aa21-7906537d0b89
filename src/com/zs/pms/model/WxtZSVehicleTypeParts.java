package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleTypeParts {
    private Long id;

    private String tModelId;

    private String partsNo;

    private String partsName;

    private String partsBrandName;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getPartsNo() {
        return partsNo;
    }

    public void setPartsNo(String partsNo) {
        this.partsNo = partsNo == null ? null : partsNo.trim();
    }

    public String getPartsName() {
        return partsName;
    }

    public void setPartsName(String partsName) {
        this.partsName = partsName == null ? null : partsName.trim();
    }

    public String getPartsBrandName() {
        return partsBrandName;
    }

    public void setPartsBrandName(String partsBrandName) {
        this.partsBrandName = partsBrandName == null ? null : partsBrandName.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}