package com.sys.umeng.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UmengEventDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public UmengEventDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUmodelNameIsNull() {
            addCriterion("umodel_name is null");
            return (Criteria) this;
        }

        public Criteria andUmodelNameIsNotNull() {
            addCriterion("umodel_name is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelNameEqualTo(String value) {
            addCriterion("umodel_name =", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameNotEqualTo(String value) {
            addCriterion("umodel_name <>", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameGreaterThan(String value) {
            addCriterion("umodel_name >", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameGreaterThanOrEqualTo(String value) {
            addCriterion("umodel_name >=", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameLessThan(String value) {
            addCriterion("umodel_name <", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameLessThanOrEqualTo(String value) {
            addCriterion("umodel_name <=", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameLike(String value) {
            addCriterion("umodel_name like", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameNotLike(String value) {
            addCriterion("umodel_name not like", value, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameIn(List<String> values) {
            addCriterion("umodel_name in", values, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameNotIn(List<String> values) {
            addCriterion("umodel_name not in", values, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameBetween(String value1, String value2) {
            addCriterion("umodel_name between", value1, value2, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelNameNotBetween(String value1, String value2) {
            addCriterion("umodel_name not between", value1, value2, "umodelName");
            return (Criteria) this;
        }

        public Criteria andUmodelEventIsNull() {
            addCriterion("umodel_event is null");
            return (Criteria) this;
        }

        public Criteria andUmodelEventIsNotNull() {
            addCriterion("umodel_event is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelEventEqualTo(String value) {
            addCriterion("umodel_event =", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventNotEqualTo(String value) {
            addCriterion("umodel_event <>", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventGreaterThan(String value) {
            addCriterion("umodel_event >", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventGreaterThanOrEqualTo(String value) {
            addCriterion("umodel_event >=", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventLessThan(String value) {
            addCriterion("umodel_event <", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventLessThanOrEqualTo(String value) {
            addCriterion("umodel_event <=", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventLike(String value) {
            addCriterion("umodel_event like", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventNotLike(String value) {
            addCriterion("umodel_event not like", value, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventIn(List<String> values) {
            addCriterion("umodel_event in", values, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventNotIn(List<String> values) {
            addCriterion("umodel_event not in", values, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventBetween(String value1, String value2) {
            addCriterion("umodel_event between", value1, value2, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelEventNotBetween(String value1, String value2) {
            addCriterion("umodel_event not between", value1, value2, "umodelEvent");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeIsNull() {
            addCriterion("umodel_oper_type is null");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeIsNotNull() {
            addCriterion("umodel_oper_type is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeEqualTo(String value) {
            addCriterion("umodel_oper_type =", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeNotEqualTo(String value) {
            addCriterion("umodel_oper_type <>", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeGreaterThan(String value) {
            addCriterion("umodel_oper_type >", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("umodel_oper_type >=", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeLessThan(String value) {
            addCriterion("umodel_oper_type <", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeLessThanOrEqualTo(String value) {
            addCriterion("umodel_oper_type <=", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeLike(String value) {
            addCriterion("umodel_oper_type like", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeNotLike(String value) {
            addCriterion("umodel_oper_type not like", value, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeIn(List<String> values) {
            addCriterion("umodel_oper_type in", values, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeNotIn(List<String> values) {
            addCriterion("umodel_oper_type not in", values, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeBetween(String value1, String value2) {
            addCriterion("umodel_oper_type between", value1, value2, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperTypeNotBetween(String value1, String value2) {
            addCriterion("umodel_oper_type not between", value1, value2, "umodelOperType");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameIsNull() {
            addCriterion("umodel_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameIsNotNull() {
            addCriterion("umodel_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameEqualTo(String value) {
            addCriterion("umodel_oper_name =", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameNotEqualTo(String value) {
            addCriterion("umodel_oper_name <>", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameGreaterThan(String value) {
            addCriterion("umodel_oper_name >", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("umodel_oper_name >=", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameLessThan(String value) {
            addCriterion("umodel_oper_name <", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameLessThanOrEqualTo(String value) {
            addCriterion("umodel_oper_name <=", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameLike(String value) {
            addCriterion("umodel_oper_name like", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameNotLike(String value) {
            addCriterion("umodel_oper_name not like", value, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameIn(List<String> values) {
            addCriterion("umodel_oper_name in", values, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameNotIn(List<String> values) {
            addCriterion("umodel_oper_name not in", values, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameBetween(String value1, String value2) {
            addCriterion("umodel_oper_name between", value1, value2, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperNameNotBetween(String value1, String value2) {
            addCriterion("umodel_oper_name not between", value1, value2, "umodelOperName");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdIsNull() {
            addCriterion("umodel_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdIsNotNull() {
            addCriterion("umodel_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdEqualTo(Long value) {
            addCriterion("umodel_oper_id =", value, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdNotEqualTo(Long value) {
            addCriterion("umodel_oper_id <>", value, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdGreaterThan(Long value) {
            addCriterion("umodel_oper_id >", value, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("umodel_oper_id >=", value, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdLessThan(Long value) {
            addCriterion("umodel_oper_id <", value, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdLessThanOrEqualTo(Long value) {
            addCriterion("umodel_oper_id <=", value, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdIn(List<Long> values) {
            addCriterion("umodel_oper_id in", values, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdNotIn(List<Long> values) {
            addCriterion("umodel_oper_id not in", values, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdBetween(Long value1, Long value2) {
            addCriterion("umodel_oper_id between", value1, value2, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelOperIdNotBetween(Long value1, Long value2) {
            addCriterion("umodel_oper_id not between", value1, value2, "umodelOperId");
            return (Criteria) this;
        }

        public Criteria andUmodelPageIsNull() {
            addCriterion("umodel_page is null");
            return (Criteria) this;
        }

        public Criteria andUmodelPageIsNotNull() {
            addCriterion("umodel_page is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelPageEqualTo(String value) {
            addCriterion("umodel_page =", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageNotEqualTo(String value) {
            addCriterion("umodel_page <>", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageGreaterThan(String value) {
            addCriterion("umodel_page >", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageGreaterThanOrEqualTo(String value) {
            addCriterion("umodel_page >=", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageLessThan(String value) {
            addCriterion("umodel_page <", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageLessThanOrEqualTo(String value) {
            addCriterion("umodel_page <=", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageLike(String value) {
            addCriterion("umodel_page like", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageNotLike(String value) {
            addCriterion("umodel_page not like", value, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageIn(List<String> values) {
            addCriterion("umodel_page in", values, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageNotIn(List<String> values) {
            addCriterion("umodel_page not in", values, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageBetween(String value1, String value2) {
            addCriterion("umodel_page between", value1, value2, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelPageNotBetween(String value1, String value2) {
            addCriterion("umodel_page not between", value1, value2, "umodelPage");
            return (Criteria) this;
        }

        public Criteria andUmodelDateIsNull() {
            addCriterion("umodel_date is null");
            return (Criteria) this;
        }

        public Criteria andUmodelDateIsNotNull() {
            addCriterion("umodel_date is not null");
            return (Criteria) this;
        }

        public Criteria andUmodelDateEqualTo(String value) {
            addCriterion("umodel_date =", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateNotEqualTo(String value) {
            addCriterion("umodel_date <>", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateGreaterThan(String value) {
            addCriterion("umodel_date >", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateGreaterThanOrEqualTo(String value) {
            addCriterion("umodel_date >=", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateLessThan(String value) {
            addCriterion("umodel_date <", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateLessThanOrEqualTo(String value) {
            addCriterion("umodel_date <=", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateLike(String value) {
            addCriterion("umodel_date like", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateNotLike(String value) {
            addCriterion("umodel_date not like", value, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateIn(List<String> values) {
            addCriterion("umodel_date in", values, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateNotIn(List<String> values) {
            addCriterion("umodel_date not in", values, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateBetween(String value1, String value2) {
            addCriterion("umodel_date between", value1, value2, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andUmodelDateNotBetween(String value1, String value2) {
            addCriterion("umodel_date not between", value1, value2, "umodelDate");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNull() {
            addCriterion("import_time is null");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNotNull() {
            addCriterion("import_time is not null");
            return (Criteria) this;
        }

        public Criteria andImportTimeEqualTo(Date value) {
            addCriterion("import_time =", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotEqualTo(Date value) {
            addCriterion("import_time <>", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThan(Date value) {
            addCriterion("import_time >", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("import_time >=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThan(Date value) {
            addCriterion("import_time <", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThanOrEqualTo(Date value) {
            addCriterion("import_time <=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeIn(List<Date> values) {
            addCriterion("import_time in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotIn(List<Date> values) {
            addCriterion("import_time not in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeBetween(Date value1, Date value2) {
            addCriterion("import_time between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotBetween(Date value1, Date value2) {
            addCriterion("import_time not between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}