package com.chevron.b2b.service;

import com.chevron.b2b.model.ScheduleInstance;
import com.chevron.b2b.model.ScheduleInstanceParams;
import com.common.util.JsonResponse;

import java.util.List;
import java.util.Map;

/**
 * 计划实例操作RPC接口
 * <AUTHOR>
 * @version 1.0 2018-12-20 17:16
 */
public interface ScheduleInstanceService {

	/**
	 * 保存计划实例信息
	 * @param record 计划实例对象
	 * @return 处理结果
	 */
	public Map<String, Object> save(ScheduleInstance record);
	
	/**
	 * 删除计划实例对象
	 * @param ids 被删除计划实例对象ID集合
	 * @return 处理结果
	 */
	public Map<String, Object> delete(List<Long> ids);
	
	public JsonResponse queryScheduleForePage(ScheduleInstanceParams params);
	
	public JsonResponse getScheduleInstance(Long id);
	
	public JsonResponse getScheduleInstanceForm(Long id);
	
	public JsonResponse saveScheduleInstanceForm(ScheduleInstance bean);
	
	public JsonResponse getApprovalInfo(Long id);
	
	public JsonResponse getScheduleShareDetail(Long id, String clientId);
	
	public JsonResponse getScheduleShareInfo(Long id, String clientId);
}
