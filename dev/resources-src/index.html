<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
    <meta charset="utf-8" http-equiv="content-type">
    <title>VisionIM首页</title>
    <meta name="Keywords" content="Vision,VisionIM">
	<meta name="Description" content="Vision,VisionIM">
	
    
	<link href="../common/css/common.css?v=20180703" rel="stylesheet">
<link href="../common/build-bui/css/bs3/bui.css?v=20161124" rel="stylesheet">
	<link href="ui.css" rel="stylesheet">
	<script language="javascript"  src="../common/js/jquery-1.9.1.min.js"></script>
	<script language="javascript"  src="../common/js/common.js"></script>
	<script type="text/javascript" src="../common/build-bui/bui-min.js?v=20180811"></script>
	<script language="javascript"  src="00base.js"></script>
	<script language="javascript"  src="25xscrollbar.js"></script>
	<script language="javascript"  src="26yscrollbar.js"></script>
	<script language="javascript"  src="30panel.js"></script>
	<script language="javascript"  src="40layout.js"></script>
	<script language="javascript"  src="45input.js"></script>
	<script language="javascript"  src="50store.js"></script>
	<script language="javascript"  src="55paging.js"></script>
	<script language="javascript"  src="80grid.js"></script>
	<script language="javascript"  src="85treegrid.js"></script>
	<script language="javascript"  src="result.js"></script>
	

<style>
.col-title {
    font-weight: bold;
}
#grid td.customer-separator {
	border-bottom-width: 2px;
}
</style>
<script type="text/javascript">
	alert(null != undefined);
function fixNumb(number){
	if(!number){
		return 0;
	}
	return Math.round(number * 1000000) / 1000000;
}
var firstNode = null, startNode = null;
function buildBinaryTree(node){
	if(node.value == pack){
		node.count++;
	}else if(node.pack > pack){
		var leftNode = node.left;
		if(!leftNode){
			node.left = {pack: pack, count: 1};
		}else{
			buildBinaryTree(leftNode, pack);
		}
	}
}
var isEdit = true;
$(document).ready(function(){
	if("success" == result.code){ 
		initGrid(result.startYear, result.endYear, result.startMonth, result.endMonth, result.list);
		$('.ui-layout').each(function(){
			UI.Layout.init(this, {
				minWidth: 850,
				minHeight: 300
			});
		});
		$(window).resize();
	}else{
		common.alertMes("获取修改对象失败。" + result.codeMsg, 'error');
	}
});
var isByPack = false, grid = null;
function initGrid(startYear, endYear, startMonth, endMonth, list){
	var headerChildren = [{
		title: '', 
		property: 'type', 
		fixedWidth: true,
		width: 100, 
		tdCls: 'col-title',
		formatter: function(value, record, index, recordId){
			if(value == 'valueLastYears'){
				return '去年同期';
			}else if(value == 'actualSellins'){
				return '实际销量';
			}else if(value == 'forecastingValues'){
				return '预测';
			}
			return '';
		},
		dataTdClsGetter: function(record){
			if(record.level == 2 && record.type == 'forecastingValues'){
				return 'customer-separator';
			}
			return null;
		}
	}];
	var currentYear = startYear, currentMonth = startMonth;
	for(var i = 0; i < 6; i++){
		if(currentMonth == 13){
			currentMonth = 1;
			currentYear += 1;
		}
		var propertyName = 'forecastingValues' + currentMonth;
		headerChildren.push({
			title: currentYear + '年' + currentMonth + '月', 
			property: propertyName, 
			fixedWidth: true,
			width: 100, 
			summary: true,
			m: currentMonth, 
			formatter: function(value, record, index, recordId){
				if(isByPack){
					return common.formatNumber(value, 0);
				}
				return common.formatNumber(parseFloat(value), 6);
			},
			propertyProcessor: function(record, propertyName){
				var value;
				if(record.level == 2){
					value = record.record[record.type][this.m - 1] || 0;
					if(record.type == 'forecastingValues' && record.record.fixedValues[this.m - 1] != null){
						value += record.record.fixedValues[this.m - 1];
					}
					if(isByPack){
						value = Math.round(value/record.pack);
					}
				}else if(record.level == 1){
					value = 0;
					for(var j = 0; j < record.children.length; j++){
						var r = record.children[j];
						if(r.type == 'forecastingValues'){
							value += this.propertyProcessor.call(this, r, propertyName) - 0;
						}
					}
				}else {
					value = record[propertyName];
				}
				return value;
			},
			dataTdClsGetter: function(record){
				if(record.level == 2 && record.type == 'forecastingValues'){
					return 'customer-separator';
				}
				return null;
			}
		});
		currentMonth++;
	}
	headerChildren.push({
		title: '', 
		property: 'type', 
		fixedWidth: true,
		width: 100, 
		tdCls: 'col-title',
		formatter: function(value, record, index, recordId){
			if(value == 'valueLastYears'){
				return '去年同期';
			}else if(value == 'actualSellins'){
				return '近12月平均';
			}else if(value == 'forecastingValues'){
				return isEdit ? '<a href="javascript: void(0);" onclick="">预测</a>' : '预测';
			}
			return '';
		},
		dataTdClsGetter: function(record){
			if(record.level == 2 && record.type == 'forecastingValues'){
				return 'customer-separator';
			}
			return null;
		}
	});
	for(var i = 0; i < 6; i++){
		if(currentMonth == 13){
			currentMonth = 1;
			currentYear += 1;
		}
		var propertyName = 'forecastingValues' + currentMonth;
		headerChildren.push({
			title: currentYear + '年' + currentMonth + '月', 
			property: propertyName, 
			fixedWidth: true,
			summary: true,
			width: 100, 
			m: currentMonth, 
			offsetMonth: i + 1,
			formatter: function(value, record, index, recordId){
				if(value === ''){
					return '';
				}
				if(isEdit && (!record.type || record.type == 'forecastingValues')){
					return '<input type="text" class="control-text" style="width: 76px;" value="' + parseFloat(value) + '" onchange=""/>';
				}
				return common.formatNumber(parseFloat(value), 6);
			},
			propertyProcessor: function(record, propertyName){
				var value;
				if(record.level == 2){
					if(record.record.forecastLeadingTime < this.offsetMonth){
						return '';
					}
					if(record.type == 'actualSellins'){
						value = record.record.avg12s[this.m - 1] || 0;
					}else if(record.type == 'forecastingValues'){
						if(record.record.forecastingValues[this.m - 1] == null){
							value = record.record.defForecastingValues[this.m - 1] || 0;
						}else{
							value = record.record.forecastingValues[this.m - 1];
							if(record.record.fixedValues[this.m - 1] != null){
								value += record.record.fixedValues[this.m - 1];
							}
						}
					}else{
						value = record.record[record.type][this.m - 1];
					}
					if(isByPack){
						value = Math.round(value/record.pack);
					}
				}else if(record.level == 1){
					if(record.forecastLeadingTime < this.offsetMonth){
						return '';
					}
					value = 0;
					for(var j = 0; j < record.children.length; j++){
						var r = record.children[j];
						if(r.type == 'forecastingValues'){
							value += this.propertyProcessor.call(this, r, propertyName);
						}
					}
				}else {
					value = record[propertyName];
				}
				return value;
			},
			dataTdClsGetter: function(record){
				if(record.level == 2 && record.type == 'forecastingValues'){
					return 'customer-separator';
				}
				return null;
			}
		});
		currentMonth++;
	}
	var headers = [{
		title: 'SKU', 
		property: 'sku', 
		rowMergeable: true, 
		width: 130, 
		propertyProcessor: function(record, propertyName){
			if(record.level == 2){
				return record.record.customerNameCn;
			}
			return record.sku;
		},
		dataTdClsGetter: function(record){
			if(record.level == 2){
				return 'customer-separator';
			}
			return null;
		} 
	},{
		title: '产品名称', 
		property: 'productName', 
		rowMergeable: true, 
		colMergeable: true, 
		width: 200, 
		formatter: function(value, record, index, recordId){
			if(record.level == 2){
				return '<merge/>';
			}
			return value;
		} 
	},{
		title: '包装单位', 
		property: 'pack', 
		rowMergeable: true, 
		colMergeable: true, 
		fixedWidth: true,
		align: 'right',
		width: 80, 
		formatter: function(value, record, index, recordId){
			if(record.level == 2){
				return '<merge/>';
			}
			return value;
		} 
	},{
		title: 'ABC', 
		property: 'gradeAbcText', 
		rowMergeable: true, 
		colMergeable: true, 
		fixedWidth: true,
		width: 60, 
		formatter: function(value, record, index, recordId){
			if(record.level == 2){
				return '<merge/>';
			}
			return value;
		} 
	},{
		title: '总量', 
		property: 'total', 
		valueProperty: 'sku',
		endLock: true, 
		rowMergeable: true, 
		fixedWidth: true,
		summary: true,
		align: 'right',
		width: 80, 
		formatter: function(value, record, index, recordId){
			if(isByPack){
				return common.formatNumber(value, 0);
			}
			return common.formatNumber(parseFloat(value), 6);
		},
		propertyProcessor: function(record, propertyName){
			if(propertyName == 'sku'){
				if(record.level == 2){
					return record.record.customerNameCn;
				}
				return record.sku;
			}
			var total = 0.0;
			for(var i = 1; i < 13; i++){
				var p = 'forecastingValues' + i,
					v = grid.getColByProperty(p).propertyProcessor(record.level == 2 ? record.forecastValueRecord : record, p);
				if(!isNaN(v)){
					total += v - 0;
				}
			}
			return total;
		},
		dataTdClsGetter: function(record){
			if(record.level == 2){
				return 'customer-separator';
			}
			return null;
		} 
	},{
		title: (startYear == endYear ? (startYear + '年') : (startYear + '年' + startMonth + '月-' + endYear + '年' + endMonth + '月')) + '预测量（<span><input type="hidden" id="packType" value="1"/></span>）', 
		children: headerChildren
	}];
	grid = UI.TreeGrid.init('#grid', {
		headers: headers,
		summary: true,
		fit: true,
		autoLoad: false,
		sumRender: 'header',
		summaryTitle: '产品总预测量',
		cellPaddingY: 5,
		data: buildTreeGridData(list),
		asynLoadChildren: true,
		childrenProperty: 'children1',
		nodeLoadingImg: 'image/loading.gif',
		loadChildren: function(record, rowId){
			var grid = this;
			setTimeout(function(){
				grid.appendChildren(rowId, record.children);
			}, 3000);
			return false;
		},
		paging: {
			pageSize: 10
		}
	});
	grid.store.load();
}

function buildTreeGridData(list){
	var currentSku = '', skuForecast = null, data = [];
	for(var i = 0; i < list.length; i++){
		var item = list[i];
		if(item.sku != currentSku){
			skuForecast = data[data.length] = {
				level: 1,
				sku: item.sku,
				productName: item.productName,
				pack: item.pack,
				gradeAbcText: item.gradeAbcText,
				notSummary: true,
				forecastLeadingTime: item.forecastLeadingTime,
				children: []
			};
			currentSku = item.sku;
		}
		var forecastValueRecord = {level: 2, expand: true, notSummary: true, pack: item.pack, type: 'forecastingValues', record: item };
		skuForecast.children.push({level: 2, expand: true, notSummary: true, pack: item.pack, type: 'valueLastYears', record: item, forecastValueRecord: forecastValueRecord }, 
				{level: 2, notSummary: true, expand: true, pack: item.pack, type: 'actualSellins', record: item }, 
				forecastValueRecord);
	}
	return data;
}
</script>

</head>
<body class="gray-bg">
	<div class="content-wrapper ui-layout">
    	<div class="content-panel header-panel ui-layout-north">
    		<div class="header-title">编辑消息</div>
    		<div class="header-btns">
					<button type="button" class="btn-submit" onclick="publish();">发布</button>
					<button type="button" class="btn-submit" onclick="saveTemp();">暂存</button>
					<button type="button" class="btn-back" onclick="window.location = '${ctx }sys/push/mainMessagePage.jsp?cacheParams=true&toItem=${data.id}'">返回</button>
    		</div>
    	</div>
		<div class="content-panel ui-layout-center">
			<div id="grid"></div>
		</div>
	</div>
</body>
</html>