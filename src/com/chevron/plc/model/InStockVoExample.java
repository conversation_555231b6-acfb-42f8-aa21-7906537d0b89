package com.chevron.plc.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InStockVoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public InStockVoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStockInNoIsNull() {
            addCriterion("stock_in_no is null");
            return (Criteria) this;
        }

        public Criteria andStockInNoIsNotNull() {
            addCriterion("stock_in_no is not null");
            return (Criteria) this;
        }

        public Criteria andStockInNoEqualTo(String value) {
            addCriterion("stock_in_no =", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoNotEqualTo(String value) {
            addCriterion("stock_in_no <>", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoGreaterThan(String value) {
            addCriterion("stock_in_no >", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoGreaterThanOrEqualTo(String value) {
            addCriterion("stock_in_no >=", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoLessThan(String value) {
            addCriterion("stock_in_no <", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoLessThanOrEqualTo(String value) {
            addCriterion("stock_in_no <=", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoLike(String value) {
            addCriterion("stock_in_no like", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoNotLike(String value) {
            addCriterion("stock_in_no not like", value, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoIn(List<String> values) {
            addCriterion("stock_in_no in", values, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoNotIn(List<String> values) {
            addCriterion("stock_in_no not in", values, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoBetween(String value1, String value2) {
            addCriterion("stock_in_no between", value1, value2, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockInNoNotBetween(String value1, String value2) {
            addCriterion("stock_in_no not between", value1, value2, "stockInNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoIsNull() {
            addCriterion("stock_out_no is null");
            return (Criteria) this;
        }

        public Criteria andStockOutNoIsNotNull() {
            addCriterion("stock_out_no is not null");
            return (Criteria) this;
        }

        public Criteria andStockOutNoEqualTo(String value) {
            addCriterion("stock_out_no =", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoNotEqualTo(String value) {
            addCriterion("stock_out_no <>", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoGreaterThan(String value) {
            addCriterion("stock_out_no >", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoGreaterThanOrEqualTo(String value) {
            addCriterion("stock_out_no >=", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoLessThan(String value) {
            addCriterion("stock_out_no <", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoLessThanOrEqualTo(String value) {
            addCriterion("stock_out_no <=", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoLike(String value) {
            addCriterion("stock_out_no like", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoNotLike(String value) {
            addCriterion("stock_out_no not like", value, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoIn(List<String> values) {
            addCriterion("stock_out_no in", values, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoNotIn(List<String> values) {
            addCriterion("stock_out_no not in", values, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoBetween(String value1, String value2) {
            addCriterion("stock_out_no between", value1, value2, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockOutNoNotBetween(String value1, String value2) {
            addCriterion("stock_out_no not between", value1, value2, "stockOutNo");
            return (Criteria) this;
        }

        public Criteria andStockFromIsNull() {
            addCriterion("stock_from is null");
            return (Criteria) this;
        }

        public Criteria andStockFromIsNotNull() {
            addCriterion("stock_from is not null");
            return (Criteria) this;
        }

        public Criteria andStockFromEqualTo(String value) {
            addCriterion("stock_from =", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromNotEqualTo(String value) {
            addCriterion("stock_from <>", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromGreaterThan(String value) {
            addCriterion("stock_from >", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromGreaterThanOrEqualTo(String value) {
            addCriterion("stock_from >=", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromLessThan(String value) {
            addCriterion("stock_from <", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromLessThanOrEqualTo(String value) {
            addCriterion("stock_from <=", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromLike(String value) {
            addCriterion("stock_from like", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromNotLike(String value) {
            addCriterion("stock_from not like", value, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromIn(List<String> values) {
            addCriterion("stock_from in", values, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromNotIn(List<String> values) {
            addCriterion("stock_from not in", values, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromBetween(String value1, String value2) {
            addCriterion("stock_from between", value1, value2, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromNotBetween(String value1, String value2) {
            addCriterion("stock_from not between", value1, value2, "stockFrom");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeIsNull() {
            addCriterion("stock_from_type is null");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeIsNotNull() {
            addCriterion("stock_from_type is not null");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeEqualTo(String value) {
            addCriterion("stock_from_type =", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeNotEqualTo(String value) {
            addCriterion("stock_from_type <>", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeGreaterThan(String value) {
            addCriterion("stock_from_type >", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stock_from_type >=", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeLessThan(String value) {
            addCriterion("stock_from_type <", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeLessThanOrEqualTo(String value) {
            addCriterion("stock_from_type <=", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeLike(String value) {
            addCriterion("stock_from_type like", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeNotLike(String value) {
            addCriterion("stock_from_type not like", value, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeIn(List<String> values) {
            addCriterion("stock_from_type in", values, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeNotIn(List<String> values) {
            addCriterion("stock_from_type not in", values, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeBetween(String value1, String value2) {
            addCriterion("stock_from_type between", value1, value2, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockFromTypeNotBetween(String value1, String value2) {
            addCriterion("stock_from_type not between", value1, value2, "stockFromType");
            return (Criteria) this;
        }

        public Criteria andStockToIsNull() {
            addCriterion("stock_to is null");
            return (Criteria) this;
        }

        public Criteria andStockToIsNotNull() {
            addCriterion("stock_to is not null");
            return (Criteria) this;
        }

        public Criteria andStockToEqualTo(String value) {
            addCriterion("stock_to =", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToNotEqualTo(String value) {
            addCriterion("stock_to <>", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToGreaterThan(String value) {
            addCriterion("stock_to >", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToGreaterThanOrEqualTo(String value) {
            addCriterion("stock_to >=", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToLessThan(String value) {
            addCriterion("stock_to <", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToLessThanOrEqualTo(String value) {
            addCriterion("stock_to <=", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToLike(String value) {
            addCriterion("stock_to like", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToNotLike(String value) {
            addCriterion("stock_to not like", value, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToIn(List<String> values) {
            addCriterion("stock_to in", values, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToNotIn(List<String> values) {
            addCriterion("stock_to not in", values, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToBetween(String value1, String value2) {
            addCriterion("stock_to between", value1, value2, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToNotBetween(String value1, String value2) {
            addCriterion("stock_to not between", value1, value2, "stockTo");
            return (Criteria) this;
        }

        public Criteria andStockToTypeIsNull() {
            addCriterion("stock_to_type is null");
            return (Criteria) this;
        }

        public Criteria andStockToTypeIsNotNull() {
            addCriterion("stock_to_type is not null");
            return (Criteria) this;
        }

        public Criteria andStockToTypeEqualTo(String value) {
            addCriterion("stock_to_type =", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeNotEqualTo(String value) {
            addCriterion("stock_to_type <>", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeGreaterThan(String value) {
            addCriterion("stock_to_type >", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stock_to_type >=", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeLessThan(String value) {
            addCriterion("stock_to_type <", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeLessThanOrEqualTo(String value) {
            addCriterion("stock_to_type <=", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeLike(String value) {
            addCriterion("stock_to_type like", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeNotLike(String value) {
            addCriterion("stock_to_type not like", value, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeIn(List<String> values) {
            addCriterion("stock_to_type in", values, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeNotIn(List<String> values) {
            addCriterion("stock_to_type not in", values, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeBetween(String value1, String value2) {
            addCriterion("stock_to_type between", value1, value2, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockToTypeNotBetween(String value1, String value2) {
            addCriterion("stock_to_type not between", value1, value2, "stockToType");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameIsNull() {
            addCriterion("stock_from_orgname is null");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameIsNotNull() {
            addCriterion("stock_from_orgname is not null");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameEqualTo(String value) {
            addCriterion("stock_from_orgname =", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameNotEqualTo(String value) {
            addCriterion("stock_from_orgname <>", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameGreaterThan(String value) {
            addCriterion("stock_from_orgname >", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameGreaterThanOrEqualTo(String value) {
            addCriterion("stock_from_orgname >=", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameLessThan(String value) {
            addCriterion("stock_from_orgname <", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameLessThanOrEqualTo(String value) {
            addCriterion("stock_from_orgname <=", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameLike(String value) {
            addCriterion("stock_from_orgname like", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameNotLike(String value) {
            addCriterion("stock_from_orgname not like", value, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameIn(List<String> values) {
            addCriterion("stock_from_orgname in", values, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameNotIn(List<String> values) {
            addCriterion("stock_from_orgname not in", values, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameBetween(String value1, String value2) {
            addCriterion("stock_from_orgname between", value1, value2, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromOrgnameNotBetween(String value1, String value2) {
            addCriterion("stock_from_orgname not between", value1, value2, "stockFromOrgname");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressIsNull() {
            addCriterion("stock_from_address is null");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressIsNotNull() {
            addCriterion("stock_from_address is not null");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressEqualTo(String value) {
            addCriterion("stock_from_address =", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressNotEqualTo(String value) {
            addCriterion("stock_from_address <>", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressGreaterThan(String value) {
            addCriterion("stock_from_address >", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressGreaterThanOrEqualTo(String value) {
            addCriterion("stock_from_address >=", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressLessThan(String value) {
            addCriterion("stock_from_address <", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressLessThanOrEqualTo(String value) {
            addCriterion("stock_from_address <=", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressLike(String value) {
            addCriterion("stock_from_address like", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressNotLike(String value) {
            addCriterion("stock_from_address not like", value, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressIn(List<String> values) {
            addCriterion("stock_from_address in", values, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressNotIn(List<String> values) {
            addCriterion("stock_from_address not in", values, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressBetween(String value1, String value2) {
            addCriterion("stock_from_address between", value1, value2, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromAddressNotBetween(String value1, String value2) {
            addCriterion("stock_from_address not between", value1, value2, "stockFromAddress");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneIsNull() {
            addCriterion("stock_from_phone is null");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneIsNotNull() {
            addCriterion("stock_from_phone is not null");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneEqualTo(String value) {
            addCriterion("stock_from_phone =", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneNotEqualTo(String value) {
            addCriterion("stock_from_phone <>", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneGreaterThan(String value) {
            addCriterion("stock_from_phone >", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("stock_from_phone >=", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneLessThan(String value) {
            addCriterion("stock_from_phone <", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneLessThanOrEqualTo(String value) {
            addCriterion("stock_from_phone <=", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneLike(String value) {
            addCriterion("stock_from_phone like", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneNotLike(String value) {
            addCriterion("stock_from_phone not like", value, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneIn(List<String> values) {
            addCriterion("stock_from_phone in", values, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneNotIn(List<String> values) {
            addCriterion("stock_from_phone not in", values, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneBetween(String value1, String value2) {
            addCriterion("stock_from_phone between", value1, value2, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockFromPhoneNotBetween(String value1, String value2) {
            addCriterion("stock_from_phone not between", value1, value2, "stockFromPhone");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameIsNull() {
            addCriterion("stock_to_orgname is null");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameIsNotNull() {
            addCriterion("stock_to_orgname is not null");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameEqualTo(String value) {
            addCriterion("stock_to_orgname =", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameNotEqualTo(String value) {
            addCriterion("stock_to_orgname <>", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameGreaterThan(String value) {
            addCriterion("stock_to_orgname >", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameGreaterThanOrEqualTo(String value) {
            addCriterion("stock_to_orgname >=", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameLessThan(String value) {
            addCriterion("stock_to_orgname <", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameLessThanOrEqualTo(String value) {
            addCriterion("stock_to_orgname <=", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameLike(String value) {
            addCriterion("stock_to_orgname like", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameNotLike(String value) {
            addCriterion("stock_to_orgname not like", value, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameIn(List<String> values) {
            addCriterion("stock_to_orgname in", values, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameNotIn(List<String> values) {
            addCriterion("stock_to_orgname not in", values, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameBetween(String value1, String value2) {
            addCriterion("stock_to_orgname between", value1, value2, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToOrgnameNotBetween(String value1, String value2) {
            addCriterion("stock_to_orgname not between", value1, value2, "stockToOrgname");
            return (Criteria) this;
        }

        public Criteria andStockToAddressIsNull() {
            addCriterion("stock_to_address is null");
            return (Criteria) this;
        }

        public Criteria andStockToAddressIsNotNull() {
            addCriterion("stock_to_address is not null");
            return (Criteria) this;
        }

        public Criteria andStockToAddressEqualTo(String value) {
            addCriterion("stock_to_address =", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressNotEqualTo(String value) {
            addCriterion("stock_to_address <>", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressGreaterThan(String value) {
            addCriterion("stock_to_address >", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressGreaterThanOrEqualTo(String value) {
            addCriterion("stock_to_address >=", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressLessThan(String value) {
            addCriterion("stock_to_address <", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressLessThanOrEqualTo(String value) {
            addCriterion("stock_to_address <=", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressLike(String value) {
            addCriterion("stock_to_address like", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressNotLike(String value) {
            addCriterion("stock_to_address not like", value, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressIn(List<String> values) {
            addCriterion("stock_to_address in", values, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressNotIn(List<String> values) {
            addCriterion("stock_to_address not in", values, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressBetween(String value1, String value2) {
            addCriterion("stock_to_address between", value1, value2, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToAddressNotBetween(String value1, String value2) {
            addCriterion("stock_to_address not between", value1, value2, "stockToAddress");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneIsNull() {
            addCriterion("stock_to_phone is null");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneIsNotNull() {
            addCriterion("stock_to_phone is not null");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneEqualTo(String value) {
            addCriterion("stock_to_phone =", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneNotEqualTo(String value) {
            addCriterion("stock_to_phone <>", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneGreaterThan(String value) {
            addCriterion("stock_to_phone >", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("stock_to_phone >=", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneLessThan(String value) {
            addCriterion("stock_to_phone <", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneLessThanOrEqualTo(String value) {
            addCriterion("stock_to_phone <=", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneLike(String value) {
            addCriterion("stock_to_phone like", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneNotLike(String value) {
            addCriterion("stock_to_phone not like", value, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneIn(List<String> values) {
            addCriterion("stock_to_phone in", values, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneNotIn(List<String> values) {
            addCriterion("stock_to_phone not in", values, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneBetween(String value1, String value2) {
            addCriterion("stock_to_phone between", value1, value2, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStockToPhoneNotBetween(String value1, String value2) {
            addCriterion("stock_to_phone not between", value1, value2, "stockToPhone");
            return (Criteria) this;
        }

        public Criteria andStorageLocationIsNull() {
            addCriterion("storage_location is null");
            return (Criteria) this;
        }

        public Criteria andStorageLocationIsNotNull() {
            addCriterion("storage_location is not null");
            return (Criteria) this;
        }

        public Criteria andStorageLocationEqualTo(String value) {
            addCriterion("storage_location =", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationNotEqualTo(String value) {
            addCriterion("storage_location <>", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationGreaterThan(String value) {
            addCriterion("storage_location >", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationGreaterThanOrEqualTo(String value) {
            addCriterion("storage_location >=", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationLessThan(String value) {
            addCriterion("storage_location <", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationLessThanOrEqualTo(String value) {
            addCriterion("storage_location <=", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationLike(String value) {
            addCriterion("storage_location like", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationNotLike(String value) {
            addCriterion("storage_location not like", value, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationIn(List<String> values) {
            addCriterion("storage_location in", values, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationNotIn(List<String> values) {
            addCriterion("storage_location not in", values, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationBetween(String value1, String value2) {
            addCriterion("storage_location between", value1, value2, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andStorageLocationNotBetween(String value1, String value2) {
            addCriterion("storage_location not between", value1, value2, "storageLocation");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeIsNull() {
            addCriterion("required_arrive_time is null");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeIsNotNull() {
            addCriterion("required_arrive_time is not null");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeEqualTo(Date value) {
            addCriterion("required_arrive_time =", value, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeNotEqualTo(Date value) {
            addCriterion("required_arrive_time <>", value, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeGreaterThan(Date value) {
            addCriterion("required_arrive_time >", value, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("required_arrive_time >=", value, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeLessThan(Date value) {
            addCriterion("required_arrive_time <", value, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeLessThanOrEqualTo(Date value) {
            addCriterion("required_arrive_time <=", value, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeIn(List<Date> values) {
            addCriterion("required_arrive_time in", values, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeNotIn(List<Date> values) {
            addCriterion("required_arrive_time not in", values, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeBetween(Date value1, Date value2) {
            addCriterion("required_arrive_time between", value1, value2, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andRequiredArriveTimeNotBetween(Date value1, Date value2) {
            addCriterion("required_arrive_time not between", value1, value2, "requiredArriveTime");
            return (Criteria) this;
        }

        public Criteria andTransportModeIsNull() {
            addCriterion("transport_mode is null");
            return (Criteria) this;
        }

        public Criteria andTransportModeIsNotNull() {
            addCriterion("transport_mode is not null");
            return (Criteria) this;
        }

        public Criteria andTransportModeEqualTo(String value) {
            addCriterion("transport_mode =", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeNotEqualTo(String value) {
            addCriterion("transport_mode <>", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeGreaterThan(String value) {
            addCriterion("transport_mode >", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeGreaterThanOrEqualTo(String value) {
            addCriterion("transport_mode >=", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeLessThan(String value) {
            addCriterion("transport_mode <", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeLessThanOrEqualTo(String value) {
            addCriterion("transport_mode <=", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeLike(String value) {
            addCriterion("transport_mode like", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeNotLike(String value) {
            addCriterion("transport_mode not like", value, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeIn(List<String> values) {
            addCriterion("transport_mode in", values, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeNotIn(List<String> values) {
            addCriterion("transport_mode not in", values, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeBetween(String value1, String value2) {
            addCriterion("transport_mode between", value1, value2, "transportMode");
            return (Criteria) this;
        }

        public Criteria andTransportModeNotBetween(String value1, String value2) {
            addCriterion("transport_mode not between", value1, value2, "transportMode");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsIsNull() {
            addCriterion("payment_terms is null");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsIsNotNull() {
            addCriterion("payment_terms is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsEqualTo(String value) {
            addCriterion("payment_terms =", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsNotEqualTo(String value) {
            addCriterion("payment_terms <>", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsGreaterThan(String value) {
            addCriterion("payment_terms >", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsGreaterThanOrEqualTo(String value) {
            addCriterion("payment_terms >=", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsLessThan(String value) {
            addCriterion("payment_terms <", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsLessThanOrEqualTo(String value) {
            addCriterion("payment_terms <=", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsLike(String value) {
            addCriterion("payment_terms like", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsNotLike(String value) {
            addCriterion("payment_terms not like", value, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsIn(List<String> values) {
            addCriterion("payment_terms in", values, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsNotIn(List<String> values) {
            addCriterion("payment_terms not in", values, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsBetween(String value1, String value2) {
            addCriterion("payment_terms between", value1, value2, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andPaymentTermsNotBetween(String value1, String value2) {
            addCriterion("payment_terms not between", value1, value2, "paymentTerms");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("order_type like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("order_type not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderDataIsNull() {
            addCriterion("order_data is null");
            return (Criteria) this;
        }

        public Criteria andOrderDataIsNotNull() {
            addCriterion("order_data is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDataEqualTo(Date value) {
            addCriterion("order_data =", value, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataNotEqualTo(Date value) {
            addCriterion("order_data <>", value, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataGreaterThan(Date value) {
            addCriterion("order_data >", value, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataGreaterThanOrEqualTo(Date value) {
            addCriterion("order_data >=", value, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataLessThan(Date value) {
            addCriterion("order_data <", value, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataLessThanOrEqualTo(Date value) {
            addCriterion("order_data <=", value, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataIn(List<Date> values) {
            addCriterion("order_data in", values, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataNotIn(List<Date> values) {
            addCriterion("order_data not in", values, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataBetween(Date value1, Date value2) {
            addCriterion("order_data between", value1, value2, "orderData");
            return (Criteria) this;
        }

        public Criteria andOrderDataNotBetween(Date value1, Date value2) {
            addCriterion("order_data not between", value1, value2, "orderData");
            return (Criteria) this;
        }

        public Criteria andFobDestinationIsNull() {
            addCriterion("fob_destination is null");
            return (Criteria) this;
        }

        public Criteria andFobDestinationIsNotNull() {
            addCriterion("fob_destination is not null");
            return (Criteria) this;
        }

        public Criteria andFobDestinationEqualTo(String value) {
            addCriterion("fob_destination =", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationNotEqualTo(String value) {
            addCriterion("fob_destination <>", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationGreaterThan(String value) {
            addCriterion("fob_destination >", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationGreaterThanOrEqualTo(String value) {
            addCriterion("fob_destination >=", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationLessThan(String value) {
            addCriterion("fob_destination <", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationLessThanOrEqualTo(String value) {
            addCriterion("fob_destination <=", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationLike(String value) {
            addCriterion("fob_destination like", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationNotLike(String value) {
            addCriterion("fob_destination not like", value, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationIn(List<String> values) {
            addCriterion("fob_destination in", values, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationNotIn(List<String> values) {
            addCriterion("fob_destination not in", values, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationBetween(String value1, String value2) {
            addCriterion("fob_destination between", value1, value2, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andFobDestinationNotBetween(String value1, String value2) {
            addCriterion("fob_destination not between", value1, value2, "fobDestination");
            return (Criteria) this;
        }

        public Criteria andSealNoIsNull() {
            addCriterion("seal_no is null");
            return (Criteria) this;
        }

        public Criteria andSealNoIsNotNull() {
            addCriterion("seal_no is not null");
            return (Criteria) this;
        }

        public Criteria andSealNoEqualTo(String value) {
            addCriterion("seal_no =", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoNotEqualTo(String value) {
            addCriterion("seal_no <>", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoGreaterThan(String value) {
            addCriterion("seal_no >", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoGreaterThanOrEqualTo(String value) {
            addCriterion("seal_no >=", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoLessThan(String value) {
            addCriterion("seal_no <", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoLessThanOrEqualTo(String value) {
            addCriterion("seal_no <=", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoLike(String value) {
            addCriterion("seal_no like", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoNotLike(String value) {
            addCriterion("seal_no not like", value, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoIn(List<String> values) {
            addCriterion("seal_no in", values, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoNotIn(List<String> values) {
            addCriterion("seal_no not in", values, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoBetween(String value1, String value2) {
            addCriterion("seal_no between", value1, value2, "sealNo");
            return (Criteria) this;
        }

        public Criteria andSealNoNotBetween(String value1, String value2) {
            addCriterion("seal_no not between", value1, value2, "sealNo");
            return (Criteria) this;
        }

        public Criteria andInTimeIsNull() {
            addCriterion("in_time is null");
            return (Criteria) this;
        }

        public Criteria andInTimeIsNotNull() {
            addCriterion("in_time is not null");
            return (Criteria) this;
        }

        public Criteria andInTimeEqualTo(Date value) {
            addCriterion("in_time =", value, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeNotEqualTo(Date value) {
            addCriterion("in_time <>", value, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeGreaterThan(Date value) {
            addCriterion("in_time >", value, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("in_time >=", value, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeLessThan(Date value) {
            addCriterion("in_time <", value, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeLessThanOrEqualTo(Date value) {
            addCriterion("in_time <=", value, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeIn(List<Date> values) {
            addCriterion("in_time in", values, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeNotIn(List<Date> values) {
            addCriterion("in_time not in", values, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeBetween(Date value1, Date value2) {
            addCriterion("in_time between", value1, value2, "inTime");
            return (Criteria) this;
        }

        public Criteria andInTimeNotBetween(Date value1, Date value2) {
            addCriterion("in_time not between", value1, value2, "inTime");
            return (Criteria) this;
        }

        public Criteria andScanGunNoIsNull() {
            addCriterion("scan_gun_no is null");
            return (Criteria) this;
        }

        public Criteria andScanGunNoIsNotNull() {
            addCriterion("scan_gun_no is not null");
            return (Criteria) this;
        }

        public Criteria andScanGunNoEqualTo(String value) {
            addCriterion("scan_gun_no =", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoNotEqualTo(String value) {
            addCriterion("scan_gun_no <>", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoGreaterThan(String value) {
            addCriterion("scan_gun_no >", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoGreaterThanOrEqualTo(String value) {
            addCriterion("scan_gun_no >=", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoLessThan(String value) {
            addCriterion("scan_gun_no <", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoLessThanOrEqualTo(String value) {
            addCriterion("scan_gun_no <=", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoLike(String value) {
            addCriterion("scan_gun_no like", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoNotLike(String value) {
            addCriterion("scan_gun_no not like", value, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoIn(List<String> values) {
            addCriterion("scan_gun_no in", values, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoNotIn(List<String> values) {
            addCriterion("scan_gun_no not in", values, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoBetween(String value1, String value2) {
            addCriterion("scan_gun_no between", value1, value2, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanGunNoNotBetween(String value1, String value2) {
            addCriterion("scan_gun_no not between", value1, value2, "scanGunNo");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdIsNull() {
            addCriterion("scan_person_id is null");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdIsNotNull() {
            addCriterion("scan_person_id is not null");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdEqualTo(String value) {
            addCriterion("scan_person_id =", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdNotEqualTo(String value) {
            addCriterion("scan_person_id <>", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdGreaterThan(String value) {
            addCriterion("scan_person_id >", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdGreaterThanOrEqualTo(String value) {
            addCriterion("scan_person_id >=", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdLessThan(String value) {
            addCriterion("scan_person_id <", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdLessThanOrEqualTo(String value) {
            addCriterion("scan_person_id <=", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdLike(String value) {
            addCriterion("scan_person_id like", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdNotLike(String value) {
            addCriterion("scan_person_id not like", value, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdIn(List<String> values) {
            addCriterion("scan_person_id in", values, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdNotIn(List<String> values) {
            addCriterion("scan_person_id not in", values, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdBetween(String value1, String value2) {
            addCriterion("scan_person_id between", value1, value2, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonIdNotBetween(String value1, String value2) {
            addCriterion("scan_person_id not between", value1, value2, "scanPersonId");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameIsNull() {
            addCriterion("scan_person_name is null");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameIsNotNull() {
            addCriterion("scan_person_name is not null");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameEqualTo(String value) {
            addCriterion("scan_person_name =", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameNotEqualTo(String value) {
            addCriterion("scan_person_name <>", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameGreaterThan(String value) {
            addCriterion("scan_person_name >", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameGreaterThanOrEqualTo(String value) {
            addCriterion("scan_person_name >=", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameLessThan(String value) {
            addCriterion("scan_person_name <", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameLessThanOrEqualTo(String value) {
            addCriterion("scan_person_name <=", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameLike(String value) {
            addCriterion("scan_person_name like", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameNotLike(String value) {
            addCriterion("scan_person_name not like", value, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameIn(List<String> values) {
            addCriterion("scan_person_name in", values, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameNotIn(List<String> values) {
            addCriterion("scan_person_name not in", values, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameBetween(String value1, String value2) {
            addCriterion("scan_person_name between", value1, value2, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andScanPersonNameNotBetween(String value1, String value2) {
            addCriterion("scan_person_name not between", value1, value2, "scanPersonName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("updater is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("updater is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(String value) {
            addCriterion("updater =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(String value) {
            addCriterion("updater <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(String value) {
            addCriterion("updater >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(String value) {
            addCriterion("updater >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(String value) {
            addCriterion("updater <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(String value) {
            addCriterion("updater <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLike(String value) {
            addCriterion("updater like", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotLike(String value) {
            addCriterion("updater not like", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<String> values) {
            addCriterion("updater in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<String> values) {
            addCriterion("updater not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(String value1, String value2) {
            addCriterion("updater between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(String value1, String value2) {
            addCriterion("updater not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}