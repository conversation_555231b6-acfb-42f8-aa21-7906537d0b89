package com.sys.properties.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.chevron.dsrkpi.dao.AwardRuleMapper;
import com.chevron.dsrkpi.model.AwardRule;
import com.chevron.dsrkpi.model.AwardRuleExample;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.auth.model.WxTPropertiesExample;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.dao.DicTypeVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.DicItemVoExample;
import com.sys.dic.model.DicTypeVo;
import com.sys.dic.model.DicTypeVoExample;
import com.sys.properties.service.SequenceTypes;
import com.sys.properties.service.WxTPropertiesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class WxTPropertiesServiceImpl implements WxTPropertiesService {

	@Resource
	private WxTPropertiesMapper propertiesMapper;

	@Resource
	private DicItemVoMapper dicItemVoMapper;

	@Resource
	private DicTypeVoMapper dicTypeVoMapper;

	@Autowired
	private AwardRuleMapper awardRuleMapper;

	@Override
	public Map<String, Object> getAllProperties() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTPropertiesExample example = new WxTPropertiesExample();

		List<WxTProperties> dataList = propertiesMapper
				.selectByExample(example);
		resultMap.put("data", dataList);
		resultMap.put("result", "success");
		return resultMap;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public Map<String, Object> createProperties(WxTProperties prop) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 补全数据
		prop.setMakeTime(new Date());
		prop.setOperator(ContextUtil.getCurUser().getLoginName());
		prop.setModifyOperator(prop.getOperator());
		prop.setModifyTime(prop.getMakeTime());

		try {
			propertiesMapper.insertSelective(prop);
			resultMap.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("result", "error");
			e.printStackTrace();
		}

		return resultMap;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public Map<String, Object> delPropertiesById(Long id) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		try {
			propertiesMapper.deleteByPrimaryKey(id);
			resultMap.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("result", "error");
			e.printStackTrace();
		}

		return resultMap;
	}

	@Override
	@Transactional(rollbackFor=Exception.class,propagation=Propagation.REQUIRED)
	public Map<String, Object> updateProperties(WxTProperties prop) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 补充数据
		prop.setModifyTime(new Date());
		prop.setModifyOperator(ContextUtil.getCurUser().getLoginName());

		try {
			propertiesMapper.updateByPrimaryKey(prop);
			resultMap.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("result", "error");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public Map<String, Object> delPropertiesByIds(Long[] ids) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		WxTPropertiesExample example = new WxTPropertiesExample();
		com.sys.auth.model.WxTPropertiesExample.Criteria criteria = example
				.createCriteria();
		// 使用in方式删除多条数据效率较高
		List<Long> idList = Arrays.asList(ids);
		criteria.andIdIn(idList);

		try {
			propertiesMapper.deleteByExample(example);
			resultMap.put("result", "success");
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("result", "error");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getPropertiesByParam(String code,
			String codename, String codetype) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTPropertiesExample example = new WxTPropertiesExample();
		com.sys.auth.model.WxTPropertiesExample.Criteria criteria = example
				.createCriteria();

		criteria.andCodeLike("%" + code + "%");
		criteria.andCodenameLike("%" + codename + "%");
		criteria.andCodetypeLike("%" + codetype + "%");

		List<WxTProperties> dataList=propertiesMapper.selectByExample(example);
		resultMap.put("data", dataList);
		resultMap.put("result", "success");

		return resultMap;
	}

	@Override
	public String getPropertiesCodeByCodeType(String codetype) {
		WxTPropertiesExample example = new WxTPropertiesExample();
		example.createCriteria().andCodetypeEqualTo(codetype);
		List<WxTProperties> res = propertiesMapper.selectByExample(example);
		String code = "";
		if(null != res && !res.isEmpty()) {
			code = res.get(0).getCode();
		}
		return code;
	}

    @Override
    public Map<String, Object> ciPerformanceProperties(List<WxTProperties> list) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
//        String[] propertiesCodes = {"mon_air_days", "rankNum", "aimDay", "aimTimes"};
        String[] propertiesCodes = {"aimDay"};
        //如果不传则返回数据
        if (CollectionUtil.isEmpty(list)) {
            WxTPropertiesExample example = new WxTPropertiesExample();
            example.createCriteria().andCodetypeIn(Arrays.asList(propertiesCodes));
            List<WxTProperties> res = propertiesMapper.selectByExample(example);
            resultMap.put("data", res);
            resultMap.put("result", "success");
            return resultMap;
        } else {
            String username = ContextUtil.getUsername();
            //如果传了数据过来就更新,只更新上面配置的值
            for (WxTProperties item : list) {
                if (ArrayUtil.contains(propertiesCodes, item.getCodetype())) {
                    item.setId(null);
                    item.setCodename(null);
                    item.setModifyTime(new Date());
                    item.setOperator(username);
                    WxTPropertiesExample example = new WxTPropertiesExample();
                    example.createCriteria().andCodetypeEqualTo(item.getCodetype());
                    propertiesMapper.updateByExampleSelective(item, example);
                    //如果修改的是德乐 门店拜访积分规则需要更新积分发放规则表
                    if(item.getCodetype().equals("aimDay")){
                        AwardRuleExample awardRuleExample = new AwardRuleExample();
                        awardRuleExample.createCriteria().andKpiCodeEqualTo("DAYS_OF_MONTH");
                        AwardRule awardRule = new AwardRule();
                        awardRule.setExtProperty1(item.getCode());
                        awardRuleMapper.updateByExampleSelective(awardRule,awardRuleExample);
                    }
                }
            }
            resultMap.put("result", "success");
            return resultMap;
        }
    }

    @Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public void transferPropertiesToDic() {
		WxTPropertiesExample example = new WxTPropertiesExample();

		List<WxTProperties> dataList = propertiesMapper
				.selectByExample(example);

		for(WxTProperties properties : dataList){
			String codeType = properties.getCodetype();
			DicTypeVoExample dicTypeExmp = new DicTypeVoExample();
			com.sys.dic.model.DicTypeVoExample.Criteria dicTypeCriteria = dicTypeExmp.createCriteria();
			dicTypeCriteria.andDicTypeCodeEqualTo(codeType);
			List<DicTypeVo> dicTypeList = dicTypeVoMapper.selectByExample(dicTypeExmp);
			if(dicTypeList == null || dicTypeList.isEmpty()){
				DicTypeVo dicType = new DicTypeVo();
				dicType.setCreateTime(new Date());
				dicType.setCreator(ContextUtil.getCurUserId());
				dicType.setDicTypeCode(codeType);
				dicTypeVoMapper.insertSelective(dicType);
			}

			DicItemVoExample dicItemExmp = new DicItemVoExample();
			com.sys.dic.model.DicItemVoExample.Criteria dicItemCriteria = dicItemExmp.createCriteria();
			dicItemCriteria.andDicTypeCodeEqualTo(codeType);
			dicItemCriteria.andDicItemCodeEqualTo(properties.getCode());
			List<DicItemVo> dicItemList = dicItemVoMapper.selectByExample(dicItemExmp);
			if(dicItemList == null || dicItemList.isEmpty()){
				DicItemVo dicItem = new DicItemVo();
				dicItem.setDicItemCode(properties.getCode());
				dicItem.setDicItemName(properties.getCodename());
				dicItem.setDicTypeCode(codeType);
				dicItem.setDicItemDesc(properties.getCodename());
				dicItem.setCreator(ContextUtil.getCurUserId());
				dicItem.setCreateTime(new Date());
				dicItem.setStatus("1");
				dicItemVoMapper.insertSelective(dicItem);
			}else{
				DicItemVo dicItem = dicItemList.get(0);
				dicItem.setDicItemCode(properties.getCode());
				dicItem.setDicItemName(properties.getCodename());
				dicItem.setDicTypeCode(codeType);
				dicItem.setDicItemDesc(properties.getCodename());
				dicItem.setCreator(ContextUtil.getCurUserId());
				dicItem.setStatus("1");
				dicItem.setUpdator(ContextUtil.getCurUserId());
				dicItem.setUpdateTime(new Date());
				dicItemVoMapper.updateByPrimaryKeySelective(dicItem);
			}
		}
	}


	public String getSequenceByType(SequenceTypes sequenceType,int sequenceLength,int zeroAddType)
	{
		Long sequenceNo = propertiesMapper.getSequenceByType(sequenceType.getValue());

		return CommonUtil.addZeroForStr(""+sequenceNo, sequenceLength, zeroAddType);
	}
}
