package com.sys.quartz.service.impl;

import java.lang.reflect.Method;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.apache.log4j.Logger;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;

import com.common.util.SpringUtils;
import com.common.util.StringUtils;
import com.sys.quartz.QuartzJobFactory;
import com.sys.quartz.QuartzJobFactoryDisallowConcurrentExecution;
import com.sys.quartz.dao.ScheduleJobMapper;
import com.sys.quartz.model.ScheduleJob;
import com.sys.quartz.service.JobTaskServiceI;


/**
 * 
 * @Description: 计划任务管理
 * <AUTHOR>
 */
@Service
public class JobTaskService implements JobTaskServiceI{
	public final Logger log = Logger.getLogger(this.getClass());
	@Autowired
	private SchedulerFactoryBean schedulerFactoryBean;
	@Autowired
	private ScheduleJobMapper scheduleJobMapper;

	/**
	 * 从数据库中取 区别于getAllJob
	 * 
	 * @return
	 */
	public List<ScheduleJob> getAllTask() {
		return scheduleJobMapper.getAll();
	}

	/**
	 * 添加到数据库中 区别于addJob
	 */
	public void addTask(ScheduleJob job) {
		job.setCreateTime(new Date());
		job.setUpdateTime(new Date());
		scheduleJobMapper.insertSelective(job);
	}

	/**
	 * 从数据库中查询job
	 */
	public ScheduleJob getTaskById(Long jobId) {
		return scheduleJobMapper.selectByPrimaryKey(jobId);
	}

	/**
	 * 更改任务状态
	 * 
	 * @throws SchedulerException
	 */
	public void changeStatus(Long jobId, String jobStatus) throws SchedulerException {
		ScheduleJob job = getTaskById(jobId);
		if (job == null) {
			return;
		}
		if (ScheduleJob.STATUS_NOT_RUNNING.equals(jobStatus)) {
			deleteJob(job);
			job.setJobStatus(ScheduleJob.STATUS_NOT_RUNNING);
		} else if (ScheduleJob.STATUS_RUNNING.equals(jobStatus)) {
			job.setJobStatus(ScheduleJob.STATUS_RUNNING);
			addJob(job);
		}
		scheduleJobMapper.updateByPrimaryKeySelective(job);
	}

	/**
	 * 更改任务 cron表达式
	 * 
	 * @throws SchedulerException
	 */
	public void updateCron(Long jobId, String cron) throws SchedulerException {
		ScheduleJob job = getTaskById(jobId);
		if (job == null) {
			return;
		}
		job.setCronExpression(cron);
		if (ScheduleJob.STATUS_RUNNING.equals(job.getJobStatus())) {
			updateJobCron(job);
		}
		scheduleJobMapper.updateByPrimaryKeySelective(job);

	}

	/**
	 * 添加任务
	 * 
	 * @param scheduleJob
	 * @throws SchedulerException
	 */
	public void addJob(ScheduleJob job) throws SchedulerException {
		if (job == null || !ScheduleJob.STATUS_RUNNING.equals(job.getJobStatus())) {
			return;
		}
		
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		log.debug("job: " + job.getJobName());
		TriggerKey triggerKey = TriggerKey.triggerKey(job.getJobName(), job.getJobGroup());

		CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
		log.debug("trigger: " + trigger);
		// 不存在，创建一个
		if (null == trigger) {
			Class clazz = ScheduleJob.CONCURRENT_IS.equals(job.getIsConcurrent()) ? QuartzJobFactory.class : QuartzJobFactoryDisallowConcurrentExecution.class;

			JobDetail jobDetail = JobBuilder.newJob(clazz).withIdentity(job.getJobName(), job.getJobGroup()).build();

			jobDetail.getJobDataMap().put("scheduleJob", job);

			CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
			
			//trigger = TriggerBuilder.newTrigger().withIdentity(job.getJobName(), job.getJobGroup()).withSchedule(scheduleBuilder).build();
			//不触发立即执行
			//等待下次Cron触发频率到达时刻开始按照Cron频率依次执行
			trigger = TriggerBuilder.newTrigger().withIdentity(job.getJobName(), job.getJobGroup()).withSchedule(scheduleBuilder.withMisfireHandlingInstructionDoNothing()).build();

			scheduler.scheduleJob(jobDetail, trigger);
//			log.debug("---JobTaskService by ljc 0705 addJob if----------------------------");
			
	  
		} else { 
			// Trigger已存在，那么更新相应的定时设置
			CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());

			// 按新的cronExpression表达式重新构建trigger
			//trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
			//不触发立即执行
			//等待下次Cron触发频率到达时刻开始按照Cron频率依次执行
			trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder.withMisfireHandlingInstructionDoNothing()).build();

			// 按新的trigger重新设置job执行
			scheduler.rescheduleJob(triggerKey, trigger);
//			log.debug("---JobTaskService by ljc 0705 addJob else----------------------------");
		}
		
	}

	@PostConstruct
	public void init() throws Exception {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　init-----------------");
		Scheduler scheduler = schedulerFactoryBean.getScheduler();

		// 这里获取任务信息数据
		List<ScheduleJob> jobList = scheduleJobMapper.getAll();
	
		for (ScheduleJob job : jobList) {
			addJob(job);
		}
	}

	/**
	 * 获取所有计划中的任务列表
	 * 
	 * @return
	 * @throws SchedulerException
	 */
	public List<ScheduleJob> getAllPlanJob() throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　getAllJob-----------------");
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
		Set<JobKey> jobKeys = scheduler.getJobKeys(matcher);
		List<ScheduleJob> jobList = new ArrayList<ScheduleJob>();
		for (JobKey jobKey : jobKeys) {
			List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
			for (Trigger trigger : triggers) {
				ScheduleJob job = new ScheduleJob();
				job.setJobName(jobKey.getName());
				job.setJobGroup(jobKey.getGroup());
				job.setDescription("触发器:" + trigger.getKey());
				Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
				job.setJobStatus(triggerState.name());
				if (trigger instanceof CronTrigger) {
					CronTrigger cronTrigger = (CronTrigger) trigger;
					String cronExpression = cronTrigger.getCronExpression();
					job.setCronExpression(cronExpression);
				}
				jobList.add(job);
			}
		}
		return jobList;
	}

	/**
	 * 所有正在运行的job  当前运行的
	 * 
	 * @return
	 * @throws SchedulerException
	 */
	public List<ScheduleJob> getRunningJob() throws SchedulerException {
		
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		List<JobExecutionContext> executingJobs = scheduler.getCurrentlyExecutingJobs();
		List<ScheduleJob> jobList = new ArrayList<ScheduleJob>(executingJobs.size());
		for (JobExecutionContext executingJob : executingJobs) {
			ScheduleJob job = new ScheduleJob();
			JobDetail jobDetail = executingJob.getJobDetail();
			JobKey jobKey = jobDetail.getKey();
			Trigger trigger = executingJob.getTrigger();
			job.setJobName(jobKey.getName());
			job.setJobGroup(jobKey.getGroup());
			job.setDescription("触发器:" + trigger.getKey());
			Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
			job.setJobStatus(triggerState.name());
			if (trigger instanceof CronTrigger) {
				CronTrigger cronTrigger = (CronTrigger) trigger;
				String cronExpression = cronTrigger.getCronExpression();
				job.setCronExpression(cronExpression);
			}
			jobList.add(job);
		}
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　getRunningJob-----------------:"+jobList.size()+"--"+jobList.get(0).getJobName());
		return jobList;
	}

	/**
	 * 暂停一个job
	 * 
	 * @param scheduleJob
	 * @throws SchedulerException
	 */
	public void pauseJob(ScheduleJob scheduleJob) throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　pauseJob-----------------"+scheduleJob.getJobName()+"---"+scheduleJob.getJobGroup());
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		JobKey jobKey = JobKey.jobKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
		scheduler.pauseJob(jobKey);
	}

	/**
	 * 恢复一个job
	 * 
	 * @param scheduleJob
	 * @throws SchedulerException
	 */
	public void resumeJob(ScheduleJob scheduleJob) throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　resumeJob-----------------");
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		JobKey jobKey = JobKey.jobKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
		scheduler.resumeJob(jobKey);
	}

	/**
	 * 删除一个job
	 * 
	 * @param scheduleJob
	 * @throws SchedulerException
	 */
	public void deleteJob(ScheduleJob scheduleJob) throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　deleteJob-----------------");
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		JobKey jobKey = JobKey.jobKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
		scheduler.deleteJob(jobKey);

	}

	/**
	 * 立即执行job
	 * 
	 * @param scheduleJob
	 * @throws SchedulerException
	 */
	public void runAJobNow(ScheduleJob scheduleJob) throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　runAJobNow-----------------");
		Scheduler scheduler = schedulerFactoryBean.getScheduler();
		JobKey jobKey = JobKey.jobKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
		scheduler.triggerJob(jobKey);
	}

	/**
	 * 更新job时间表达式
	 * 
	 * @param scheduleJob
	 * @throws SchedulerException
	 */
	public void updateJobCron(ScheduleJob scheduleJob) throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　updateJobCron-----------------");
		Scheduler scheduler = schedulerFactoryBean.getScheduler();

		TriggerKey triggerKey = TriggerKey.triggerKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());

		CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);

		CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());

		trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();

		scheduler.rescheduleJob(triggerKey, trigger);
	}

	/**
	 * 根据参数生成cron表达式
	 * 
	 * @param startTime,reptitionType,repetitionFrequency
	 */
	public String createCronExpression(Date startTime, String repetitionType, String repetitionFrequency){
		StringBuffer cronExpression = new StringBuffer();
		GregorianCalendar startDate = new GregorianCalendar();
		startDate.setTime(startTime);
		cronExpression.append("1 ");
//		cronExpression.append("20").append(" ");
		cronExpression.append("50").append(" ");//0点50分1秒开始
		cronExpression.append(startDate.get(GregorianCalendar.HOUR_OF_DAY)).append(" ");
		if("1".equals(repetitionType)){	
			cronExpression.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append("/")
			.append(repetitionFrequency).append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.MONTH)+1).append("/1")
			.append(" ");
			cronExpression.append("?").append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.YEAR)).append("/1");
		}else if("2".equals(repetitionType)){
			cronExpression.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append("/")
			.append(7*Integer.parseInt(repetitionFrequency)).append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.MONTH)+1).append("/1")
			.append(" ");
			cronExpression.append("?").append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.YEAR)).append("/1");
		}else if("3".equals(repetitionType)){
			cronExpression.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.MONTH)+1).append("/")
			.append(repetitionFrequency).append(" ");
			cronExpression.append("?").append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.YEAR)).append("/1");
		}else if("4".equals(repetitionType)){
			cronExpression.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.MONTH)+1).append(" ");
			cronExpression.append("?").append(" ");
			cronExpression.append(startDate.get(GregorianCalendar.YEAR)).append("/")
			.append(repetitionFrequency);
		}
		
//		if("1".equals(repetitionType)){	
//			return "1 */2 * * * ?";
//		}
		return cronExpression.toString();
	}
	//add by bo.liu 生成对应任务的描述
	public String cronExpressionDescript(Date startTime, String repetitionType, String repetitionFrequency){
		StringBuffer cronExpressionDescript = new StringBuffer();
		GregorianCalendar startDate = new GregorianCalendar();
		startDate.setTime(startTime);
		if("1".equals(repetitionType)){	
			cronExpressionDescript.append(startDate.get(GregorianCalendar.YEAR)).append("年");
			cronExpressionDescript.append(startDate.get(startDate.get(GregorianCalendar.MONTH)+1)).append("月");
			cronExpressionDescript.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append("日开始每隔")
			.append(repetitionFrequency).append("天");
		
		}else if("2".equals(repetitionType)){
			cronExpressionDescript.append(startDate.get(GregorianCalendar.YEAR)).append("年");
			cronExpressionDescript.append(startDate.get(startDate.get(GregorianCalendar.MONTH)+1)).append("月");
			cronExpressionDescript.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append("日开始每隔")
			.append(repetitionFrequency).append("星期");
			
		
		}else if("3".equals(repetitionType)){
			cronExpressionDescript.append(startDate.get(GregorianCalendar.YEAR)).append("年");
			cronExpressionDescript.append(startDate.get(startDate.get(GregorianCalendar.MONTH)+1)).append("月");
			cronExpressionDescript.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append("日开始每隔")
			.append(repetitionFrequency).append("月");
			
		}else if("4".equals(repetitionType)){
			cronExpressionDescript.append(startDate.get(GregorianCalendar.YEAR)).append("年");
			cronExpressionDescript.append(startDate.get(startDate.get(GregorianCalendar.MONTH)+1)).append("月");
			cronExpressionDescript.append(startDate.get(GregorianCalendar.DAY_OF_MONTH)).append("日开始每隔")
			.append(repetitionFrequency).append("年");
		}
		cronExpressionDescript.append("0点40分1秒重复执行1次");
		return cronExpressionDescript.toString();
	}
	
	
	public ScheduleJob createScheduleJob(Date startTime, Date endTime,
			String repetitionType, String repetitionFrequency, Long taskId, String jobStatus) throws ParseException{
		ScheduleJob scheduleJob = new ScheduleJob();
		scheduleJob.setDescription("new task");
		scheduleJob.setEndTime(endTime);
		scheduleJob.setIsConcurrent(ScheduleJob.CONCURRENT_IS);
		scheduleJob.setJobGroup("WorkGroup");
		scheduleJob.setJobName(StringUtils.getSeqNextval());
		scheduleJob.setJobStatus(jobStatus);
		scheduleJob.setMethodName("execute");
		scheduleJob.setRepetitionFrequency(repetitionFrequency);
		scheduleJob.setRepetitionType(repetitionType);
		scheduleJob.setSpringId("taskExecutor");
		scheduleJob.setStartTime(startTime);
		scheduleJob.setTaskId(taskId);
		return scheduleJob;
	}
	
	/**
	 * 从数据库中获得所有正在运行的job
	 * 
	 * @return
	 * @throws SchedulerException
	 */
	public List<ScheduleJob> obtainRunningJobs(){
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　obtainRunningJobs-----------------");
		return scheduleJobMapper.getRunningJobs();
	}
	
	public static void main(String[] args) {
		CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule("xxxxx");
	}

	@Override
	public Map addRepetitionTask(Date startTime, Date endTime,
			String repetitionType, String repetitionFrequency, Long taskId, String jobStatus,Long createuserId,String createUserName,String sourceTaskName)
			throws Exception {
		Map resMap = new HashMap();
		ScheduleJob scheduleJob = createScheduleJob(startTime,endTime,repetitionType,repetitionFrequency,taskId,jobStatus);
		try {
			String cronExpression = createCronExpression(scheduleJob.getStartTime(), 
					scheduleJob.getRepetitionType(), scheduleJob.getRepetitionFrequency());
			scheduleJob.setCronExpression(cronExpression);
			scheduleJob.setDescription(cronExpressionDescript(startTime, repetitionType, repetitionFrequency));
			scheduleJob.setCreateUserId(createuserId);
			scheduleJob.setCreateUserName(createUserName);
			scheduleJob.setSourceTaskName(sourceTaskName);
			CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());
		} catch (Exception e) {
			resMap.put("code","cronExpressError");
			return resMap;
		}
		Object obj = null;
		try {
			if (org.apache.commons.lang.StringUtils.isNotBlank(scheduleJob.getSpringId())) {
				obj = SpringUtils.getBean(scheduleJob.getSpringId());
			} else {
				Class clazz = Class.forName(scheduleJob.getBeanClass());
				obj = clazz.newInstance();
			}
		} catch (Exception e) {
			//nothing to do.........
		}
		if (obj == null) {
			resMap.put("code","cannotFindTargetObject");
			return resMap;
		} else {
			Class clazz = obj.getClass();
			Method method = null;
			try {
				method = clazz.getMethod(scheduleJob.getMethodName(), new Class[]{Long.class});
			} catch (Exception e) {
				// do nothing.....
			}
			if (method == null) {
				resMap.put("code","cannotFindTargetMethod");
				return resMap;
			}
		}
		try {
			addTask(scheduleJob);
			addJob(scheduleJob);
		} catch (Exception e) {
			e.printStackTrace();
			resMap.put("code","saveTaskFailed");
			return resMap;
		}

		resMap.put("code","success");
		return resMap;
	}
	
	/**
	 * 通过taskId物理删除重复任务
	 * 
	 * @throws SchedulerException
	 */
	public int deleteJobByTaskId(Long taskId) throws SchedulerException {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　deleteJobByTaskId-----------------");
		int colNum=0;
		if(taskId != null){
			colNum = scheduleJobMapper.deleteByTaskId(taskId);
		}
		return colNum;
	}
	
	/**
	 * 通过taskId从数据库中查询job
	 */
	public ScheduleJob getJobByTaskId(Long taskId) {
		log.debug("------JobTaskService　by ljc 0705 jobtaskservice　getJobByTaskId-----------------");
		return scheduleJobMapper.selectByTaskId(taskId);
	}
}
