1. 强制撤回 RPC
recallByRequest(MktSignageApply form, String remark, Long executor, Long versionNo)
{"id":2,"jsonrpc":"2.0","method":"mktSignageApplyService.recallByRequest","params":[{"id":"296"},"测试强制撤回",null,17]}

2. 终止 RPC
abortByStep(MktSignageApply form, String stepCode, String remark, Long executor, Long versionNo)
{"id":2,"jsonrpc":"2.0","method":"mktSignageApplyService.abortByStep","params":[{"id":"307"}, "ABORT", "测试终止",null,14]}
