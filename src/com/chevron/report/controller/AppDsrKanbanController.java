package com.chevron.report.controller;

import com.chevron.report.model.*;
import com.chevron.report.service.DsrKanbanService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(value="/kanban")
public class AppDsrKanbanController {

    private final static Logger log = LoggerFactory.getLogger(AppDsrKanbanController.class);

    @Autowired
    private DsrKanbanService dsrKanbanService;

    @ResponseBody
    @RequestMapping(value="/getWorkShopCount.do")
    public JsonResponse getWorkShopCount(){
        JsonResponse resultMap = new JsonResponse();

        try {
            dsrKanbanService.getWorkShopCount(resultMap);
            log.info("getWorkShopCount success." );
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询用户门店数量数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.getWorkShopCount", ContextUtil.getCurUser().getCai());
        }
        return resultMap;
    }


    @ResponseBody
    @RequestMapping(value="/sellThroughByMonth.do")
    public JsonResponse sellThroughSummary(){
        JsonResponse resultMap = new JsonResponse();

        try {
            dsrKanbanService.sellThroughByMonth(resultMap);
            log.info("sellThroughSummary success." );
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询用户扫码出库数量数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.sellThroughSummary", ContextUtil.getCurUser().getCai());
        }
        return resultMap;
    }
    @ResponseBody
    @RequestMapping(value="/sellThroughByStore.do")
    public JsonResponse sellThroughDetail(@RequestBody SellThroughByStoreVo vo){
        JsonResponse resultMap = new JsonResponse();

        try {
            dsrKanbanService.sellThroughByStore(vo ,resultMap);
            log.info("sellThroughDetail success." );
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询用户门扫码出库详情数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.sellThroughDetail", ContextUtil.getCurUser().getCai());
        }
        return resultMap;
    }
    @ResponseBody
    @RequestMapping(value="/sellThroughTendency.do")
    public JsonResponse sellThroughTendency(@RequestBody SellThroughByStoreVo vo) {
        // 查询用户扫码出库趋势数据，不受页面时间范围筛选条件控制
        vo.setStartDate(null);
        vo.setEndDate(null);

        JsonResponse resultMap = new JsonResponse();
        try {
            dsrKanbanService.sellThroughTendency(vo,resultMap);
            log.info("sellThroughTendency success." );
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询用户扫码出库趋势数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.sellThroughTendency", ContextUtil.getCurUser().getCai());
        }
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value="/queryCustomerRelationList.do")
    public JsonResponse queryCustomerRelationList(@RequestBody QueryCustomerVo vo){
        JsonResponse resultMap = new JsonResponse();

        try {
            dsrKanbanService.queryCustomerByConditions(vo,resultMap);
            log.info("queryCustomerRelationList success." );
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询用户客户信息数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.sellThroughTendency", ContextUtil.getCurUser().getCai());
        }
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value="/queryWorkshopInspectionsSummaryData.do", method = {RequestMethod.GET})
    public JsonResponse queryWorkshopInspectionsSummaryData() {
        JsonResponse resultMap = new JsonResponse();
        try {
            WorkshopInspectionsSummaryDataVo data = dsrKanbanService.queryWorkshopInspectionsSummaryData();
            resultMap.put("data", data);
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询门店打卡统计数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.queryWorkshopInspectionsSummaryData",
                    ContextUtil.getCurUserId()+"");
        }
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value="/queryCapScanSummaryData.do", method = {RequestMethod.GET})
    public JsonResponse queryCapScanSummaryData() {
        JsonResponse resultMap = new JsonResponse();
        try {
            CapScanSummaryDataVo data = dsrKanbanService.queryCapScanSummaryData();
            resultMap.put("data", data);
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询瓶盖扫码统计数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.queryCapScanSummaryData",
                    ContextUtil.getCurUserId()+"");
        }
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value="/queryCapScanTrendData.do", method = {RequestMethod.POST})
    public JsonResponse queryCapScanTrendData(@RequestBody CapScanTrendDataRequest req) {
        JsonResponse resultMap = new JsonResponse();
        try {
            List<CapScanMonthTrendDataVo> data = dsrKanbanService.queryCapScanTrendData(req);
            resultMap.put("data", data);
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询瓶盖扫码趋势数据异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.queryCapScanTrendData",
                    ContextUtil.getCurUserId()+"");
        }
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value="/queryCapScanDetailToWorkshop.do", method = {RequestMethod.POST})
    public JsonResponse queryCapScanDetailToWorkshop(@RequestBody CapScanDetailToWorkshopRequest req) {
        JsonResponse resultMap = new JsonResponse();
        try {
            List<CapScanDetailToWorkshopDataVo> data = dsrKanbanService.queryCapScanDetailToWorkshop(req);
            resultMap.put("data", data);
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-门店看板-查询瓶盖扫码明细异常，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.queryCapScanDetailToWorkshop",
                    ContextUtil.getCurUserId()+"");
        }
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value="/judgeUserRole.do", method = {RequestMethod.POST})
    public JsonResponse judgeUserRole() {
        JsonResponse resultMap = new JsonResponse();
        try {
            String userRole = dsrKanbanService.judgeUserRole();
            resultMap.put("data", userRole);
        } catch (WxPltException e) {
            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("DSR-查看用户登录角色，userId={}", ContextUtil.getCurUserId(), e);
            resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.report.controller.AppDsrKanbanController.judgeUserRole",
                    ContextUtil.getCurUserId()+"");
        }
        return resultMap;
    }

}