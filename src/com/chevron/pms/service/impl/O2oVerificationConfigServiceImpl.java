package com.chevron.pms.service.impl;

import com.chevron.pms.business.O2oVerificationConfigBizService;
import com.chevron.pms.model.O2oVerificationConfig;
import com.chevron.pms.service.O2oVerificationConfigService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * o2o核销配置操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2017-08-17 09:55
 */
@Service
public class O2oVerificationConfigServiceImpl implements O2oVerificationConfigService {
	@Autowired
	private O2oVerificationConfigBizService o2oVerificationConfigBizService;
	
	private final static Logger log = Logger.getLogger(O2oVerificationConfigServiceImpl.class);
	
	@Override
	public Map<String, Object> save(O2oVerificationConfig record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			o2oVerificationConfigBizService.update(record);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> delete(Long id) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			o2oVerificationConfigBizService.delete(id);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}

	@Override
	public Map<String, Object> query() {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put(Constants.RESULT_LST_KEY, o2oVerificationConfigBizService.queryForList(null));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
}
