SELECT 
	msa.req_no "申请编号",
	di2.dic_item_name "品牌",
	di.dic_item_name "申请类别", 
	case when msa.local_make='Y' then '14A申请' else '非14A申请' end "申请方式", 
	case when msa.local_make='N' then s.supplier_name
		else '' end "供应商", 
	msa.region "区域",
	msa.distributor_name "经销商",
	msa.store_name "客户名称",
	msa.budget_amount "申请金额",
	msa.company_code AS "CCN",
	msa.cost_center AS "成本中心",--wf_wi1.next_step_no,wf_wi1.flow_instance_id,
	CONVERT(varchar(100), wf_wi1.apply_time, 120) "申请时间",
	msa.req_user_name "申请销售",
	 tlw.execute_time "Team Leader审批时间",
	 tlw.actual_executor_name "Team Leader审批人",
	 asma.execute_time "ASM审批时间",
	 asma.actual_executor_name "ASM审批人",
	 abma.execute_time "ABM审批时间",
	 abma.actual_executor_name "ABM审批人",
	 rmkta.actual_executor_name "市场部区域经理审批批人",
	 rmkta.execute_time "市场部区域经理审批时间",
	 mmkta.execute_time "市场部经理审批时间",
	 mmkta.actual_executor_name "市场部经理审批人",
	/* psrma.execute_time "供应商上传预览图时间",
	 psrma.actual_executor_name "供应商上传预览图人",
	 frmkta.execute_time "FLSR和MKT区域经理审核设计时间",
	 frmkta.actual_executor_name "FLSR和MKT区域经理审核设计人",
	 psdma.execute_time "供应商上传最终设计图时间",
	 psdma.actual_executor_name "供应商上传最终设计图人",*/
	 dsma.execute_time "经销商完工确认时间",
	 dsma.actual_executor_name "经销商完工确认人",
	 /*psma.execute_time "供应商完工确认&提交报销材料时间",
	 psma.actual_executor_name "供应商完工确认&提交报销材料人",*/
	 fcdma.execute_time "FLSR完工审核时间",
	 fcdma.actual_executor_name "FLSR完工审核人",
	 rmcdm.execute_time "市场部区域经理完工审核时间",
	 rmcdm.actual_executor_name "市场部区域经理完工审核人",
	 mmfa.execute_time "市场部经理完工审核时间",
	 mmfa.actual_executor_name "市场部经理完工审核人",
	 fa.execute_time "FIN报销材料审核时间",
	 fa.actual_executor_name "FIN报销材料审核人"
FROM wx_t_mkt_signage_apply msa 
left join wx_t_supplier s on s.id=msa.supplier_id
left join wx_t_dic_item di on di.dic_type_code='signage.applyType' and di.dic_item_code=msa.sign_type
left join wx_t_dic_item di2 on di2.dic_type_code='ChevronBrand' and di2.dic_item_code=msa.brand
--left join view_customer_region_sales_channel crsc on msa.distributor_id=crsc.distributor_id and crsc.channel_weight&2>0
left join wx_t_workflow_instance wf_wi1 on msa.id=wf_wi1.form_key and wf_wi1.workflow_code='SIGNAGE_APPLY'
/*left join wx_t_user wf_wi1_u1 on wf_wi1_u1.user_id=wf_wi1.request_user
left join wx_t_workflow_step_history wf_wsh2 on wf_wsh2.history_id=(select max(wf_wsh2_1.history_id) from wx_t_workflow_step_history wf_wsh2_1
left join wx_t_workflow_step_instance wf_wsi2_1 on wf_wsh2_1.step_instance_id=wf_wsi2_1.step_instance_id
left join wx_t_workflow_step wf_ws2_1 on wf_ws2_1.step_id=wf_wsi2_1.step_id
	where wf_wi1.flow_instance_id=wf_wsi2_1.workflow_instance_id and wf_ws2_1.step_code=wf_wi1.current_step)
left join wx_t_user wf_u2 on wf_u2.user_id=wf_wsh2.executor
left join wx_t_workflow_step_instance wf_wsi2 on wf_wsh2.step_instance_id=wf_wsi2.step_instance_id
left join wx_t_workflow_step wf_ws2 on wf_ws2.step_id=wf_wsi2.step_id*/
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('TEAM_LEADER_APPROVE')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) tlw on wf_wi1.flow_instance_id=tlw.workflow_instance_id and tlw.step_no<wf_wi1.next_step_no and tlw.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('ASM_APPROVE')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) asma on wf_wi1.flow_instance_id=asma.workflow_instance_id and asma.step_no<wf_wi1.next_step_no and asma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('ABM_APPROVE')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) abma on wf_wi1.flow_instance_id=abma.workflow_instance_id and abma.step_no<wf_wi1.next_step_no and abma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('MKT_RM_APPROVE')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) rmkta on wf_wi1.flow_instance_id=rmkta.workflow_instance_id and rmkta.step_no<wf_wi1.next_step_no and rmkta.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('MKT_M_APPROVE')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) mmkta on wf_wi1.flow_instance_id=mmkta.workflow_instance_id and mmkta.step_no<wf_wi1.next_step_no and mmkta.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('PROVIDER_SUBMIT_PREVIEW_MATERIAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) psrma on wf_wi1.flow_instance_id=psrma.workflow_instance_id and psrma.step_no<wf_wi1.next_step_no and psrma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('FLSR_MKTRM_CHECK_MATERIAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) frmkta on wf_wi1.flow_instance_id=frmkta.workflow_instance_id and frmkta.step_no<wf_wi1.next_step_no and frmkta.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('PROVIDER_SUBMIT_DESIGN_MATERIAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) psdma on wf_wi1.flow_instance_id=psdma.workflow_instance_id and psdma.step_no<wf_wi1.next_step_no and psdma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('DIST_SUBMIT_MATERIAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) dsma on wf_wi1.flow_instance_id=dsma.workflow_instance_id and dsma.step_no<wf_wi1.next_step_no and dsma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('PROVIDER_SUBMIT_MATERIAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) psma on wf_wi1.flow_instance_id=psma.workflow_instance_id and psma.step_no<wf_wi1.next_step_no and psma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('FLSR_CHECK_DIST_MATERIAL','FLSR_CHECK_DIST_MATERIAL_LOCAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) fcdma on wf_wi1.flow_instance_id=fcdma.workflow_instance_id and fcdma.step_no<wf_wi1.next_step_no and fcdma.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('MKT_RM_CHECK_DIST_MATERIAL','MKT_RM_CHECK_DIST_MATERIAL_LOCAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) rmcdm on wf_wi1.flow_instance_id=rmcdm.workflow_instance_id and rmcdm.step_no<wf_wi1.next_step_no and rmcdm.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('MKT_M_FINAL_APPROVE','MKT_M_FINAL_APPROVE_LOCAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) mmfa on wf_wi1.flow_instance_id=mmfa.workflow_instance_id and mmfa.step_no<wf_wi1.next_step_no and mmfa.version_no=wf_wi1.step_version_no
left join (select wsi1.workflow_instance_id,max(u2.ch_name) actual_executor_name,max(CONVERT(varchar(100), wiel1.execute_time, 120)) execute_time, wsi1.step_no,wsi1.version_no
	from wx_t_workflow_step_instance wsi1
		 --left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_workflow_step_history wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where t1.step_code in ('FIN_APPROVE','FIN_APPROVE_LOCAL')
		 group by wsi1.workflow_instance_id, wsi1.step_no,wsi1.version_no ) fa on wf_wi1.flow_instance_id=fa.workflow_instance_id and fa.step_no<wf_wi1.next_step_no and fa.version_no=wf_wi1.step_version_no
WHERE msa.form_status>=10 --and msa.req_no='HDEO459120210619001'
order by wf_wi1.flow_instance_id desc