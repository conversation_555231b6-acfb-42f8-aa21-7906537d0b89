package com.chevron.pms.controller;

import java.lang.annotation.ElementType;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.pms.business.OilVerificationBizService;
import com.chevron.pms.dao.MechanicQrcodeMapper;
import com.chevron.pms.dao.OilVerificationMapper;
import com.chevron.pms.dao.VerificationBatchMapper;
import com.chevron.pms.dao.VerificationDetailMapper;
import com.chevron.pms.model.ChevronVerificationParams;
import com.chevron.pms.model.MechanicQrcodeParams;
import com.chevron.pms.model.OilVerification;
import com.chevron.pms.model.OilVerificationDetail;
import com.chevron.pms.model.OilVerificationParams;
import com.chevron.pms.model.OilVerificationSpParams;
import com.chevron.pms.model.OilVerificationSpReportExp;
import com.chevron.pms.model.OilVerificationView;
import com.chevron.pms.model.VerificationBatch;
import com.chevron.pms.model.VerificationBatchSpExp;
import com.chevron.pms.model.VerificationDetail;
import com.chevron.pms.model.WorkshopOilVerificationSummary;
import com.chevron.pms.model.WsOilVeriSumReport;
import com.chevron.pms.model.WsOilVeriSumSpChe;
import com.chevron.pms.model.WsVerifSumCheHistRealExp;
import com.chevron.pms.model.WsVerifSumSpHistRealExp;
import com.chevron.pms.service.OilVerificationService;
import com.chevron.pms.service.PartnerResponsibleService;
import com.chevron.exportdata.ExpTypeEnum;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportColAlign;
import com.chevron.exportdata.ExportExcel;
import com.chevron.exportdata.ExportUtil;
import com.common.constants.Constants;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;

@Controller
public class OilVerificationController {
	@Resource
	private OilVerificationBizService oilVerificationBizService;
	
	@Autowired
	PartnerResponsibleService partnerResponsibleService;
	
	@Resource
	private OilVerificationMapper oilVerificationMapper;
	
	@Autowired 
	OilVerificationService oilVerificationService;
	
	@Autowired
	private VerificationBatchMapper verificationBatchMapper;
	
	@Autowired
	private VerificationDetailMapper verificationDetailMapper;
	
	@Autowired
	private MechanicQrcodeMapper mechanicQrcodeMapper;

	private Logger logger = Logger.getLogger(OilVerificationController.class);
	
	@ResponseBody
	@RequestMapping("/oilverification/spverification/data.do")
	public Map<String, Object> spVerification(OilVerificationParams params){
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("rows", verificationDetailMapper.queryVerificationSumForPage(params));
			map.put("total", params.getTotalCount());
			params.closeOrder();
			VerificationDetail sum = verificationDetailMapper.queryVerificationSumForPageSum(params);
			if(sum == null){
				sum = new VerificationDetail();
			}
			map.put("summary", sum);
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
			map.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error("error",e);
		}
		return map;
	}
	
	@ResponseBody
	@RequestMapping("/oilverification/mechanicQrcode/data.do")
	public Map<String, Object> queryMechanicQrcodeData(MechanicQrcodeParams params){
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put(Constants.RESULT_LST_KEY, mechanicQrcodeMapper.queryForPage(params));
			map.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error("error",e);
		}
		return map;
	}
	
	@ResponseBody
	@RequestMapping("/oilverification/distributionMechanicQrcode/data.do")
	public Map<String, Object> queryDistributionMechanicQrcodeData(MechanicQrcodeParams params){
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put(Constants.RESULT_LST_KEY, mechanicQrcodeMapper.queryForDistributionPage(params));
			map.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error("error",e);
		}
		return map;
	}
	
	@RequestMapping(value = "/oilverification/export/chevronhistory.do")
	public String chevronVerificationHistory(@RequestParam("partnerId")Long partnerId, @RequestParam("workshopName")String workshopName, 
			@RequestParam("dateFrom")String dateFrom, @RequestParam("dateTo")String dateTo, HttpServletRequest request, HttpServletResponse response){
		ChevronVerificationParams params = new ChevronVerificationParams();
		params.closeOrder();
		try {
//			paramMap.put("workshopId", workshopId);
			WxTUser user = ContextUtil.getCurUser();
			if(!WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel())){
				params.setPartnerId(user.getOrgId());
			}
			params.setStartTimeString(dateFrom);
			params.setEndTimeString(dateTo);
			params.setWorkshopName(workshopName);
			
			List<VerificationBatch> batchList = verificationBatchMapper.queryChevronVerificationHistory(params);
			List<VerificationDetail> detailList = verificationDetailMapper.selectVerificationDetailForCHExport(params);
			Map<Long, List<VerificationDetail>> detailListMap = new HashMap<Long, List<VerificationDetail>>();
			for(VerificationDetail detail : detailList){
				List<VerificationDetail> list = detailListMap.get(detail.getBatchId());
				if(list == null){
					list = new ArrayList<VerificationDetail>();
					detailListMap.put(detail.getBatchId(), list);
				}
				list.add(detail);
			}
			if(batchList != null){
				VerificationBatch total = new VerificationBatch();
				total.setPartnerName("合计：");
				total.setTotalCapacity(0);
				total.setAmount(0.0);
				total.setMechanicAmount(0.0);
				total.setOwnerAmount(0.0);
				for(VerificationBatch batch : batchList){
					batch.setDetailList(detailListMap.get(batch.getId()));
					//计算合计
					if(batch.getTotalCapacity() != null){
						total.setTotalCapacity(total.getTotalCapacity() + batch.getTotalCapacity());
					}
					if(batch.getAmount() != null){
						total.setAmount(total.getAmount() + batch.getAmount());
					}
					if(batch.getMechanicAmount() != null){
						total.setMechanicAmount(total.getMechanicAmount() + batch.getMechanicAmount());
					}
					if(batch.getOwnerAmount() != null){
						total.setOwnerAmount(total.getOwnerAmount() + batch.getOwnerAmount());
					}
				}
				batchList.add(total);
			}
			ExportUtil.exportData(batchList, VerificationBatch.class.getName(), ExpTypeEnum.Excel, ElementType.METHOD, 
					"detailList", VerificationDetail.class, ElementType.METHOD, "结算升数详情", response, 
					"雪佛龙核销历史", "", request); //-不直接发红包
		} catch (Exception e) {
			logger.error(e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@RequestMapping(value = "/oilverification/export/chevronhistoryrealtime.do")
	public String chevronHistoryRealtime(@RequestParam("partnerId")String partnerId, @RequestParam("workshopName")String workshopName,
			@RequestParam("dateFrom")String dateFrom, @RequestParam("dateTo")String dateTo, 
			HttpServletRequest request, HttpServletResponse response){
		Date newDateFrom = StringUtils.isEmpty(dateFrom) ? DateUtil.getMinimumDate() : DateUtil.parseDate(dateFrom);
		Date newDateTo = StringUtils.isEmpty(dateTo) ? DateUtil.getMaximumDate() : DateUtil.addDays(DateUtil.parseDate(dateTo), 1);
		
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
//			paramMap.put("workshopId", workshopId);
			WxTUser user = ContextUtil.getCurUser();
			if(user.getmUserTypes() == 1){
				if(!"".equals(partnerId)){
					paramMap.put("partnerId", partnerId);
				}
			}else{
				paramMap.put("partnerId", user.getOrgId());
			}
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			
			if (!StringUtils.isNull(workshopName)){
				paramMap.put("workshopName", "%" + workshopName + "%");
			}
			
			List<WorkshopOilVerificationSummary> summaryList = oilVerificationBizService.queryOilVerificationSummaryListWithAwardRealtime(paramMap);
			List<WsVerifSumCheHistRealExp> newList = new ArrayList<WsVerifSumCheHistRealExp>();
			if (summaryList != null && !summaryList.isEmpty()){
				paramMap.put("workshopName", null);
				Iterator<WorkshopOilVerificationSummary> iter = summaryList.iterator();
				WorkshopOilVerificationSummary total = new WorkshopOilVerificationSummary();
				total.setPartnerName("合计：");
				while (iter.hasNext()){
					WorkshopOilVerificationSummary summary = iter.next();
					//技师扫码升数详情
					paramMap.put("workshopId", summary.getWorkshopId());
					List<OilVerification> detailList = oilVerificationBizService.queryOilVerificationListWithAwardRealtime(paramMap);
					if(detailList != null){
						List<OilVerification> newDetails = new ArrayList<OilVerification>();
						for(OilVerification verification : detailList){
							newDetails.add(new OilVerificationDetail(verification));
						}
						summary.setDetailList(newDetails);
					}
					total.setClosedCapacity(total.getClosedCapacity() + summary.getClosedCapacity());
					total.setCanVerifyCapacity(total.getCanVerifyCapacity() + summary.getCanVerifyCapacity());
					total.setVerifyAmount(total.getVerifyAmout() + summary.getVerifyAmout());
					total.setMechanicAmount(total.getMechanicAmout() + summary.getMechanicAmout());
					total.setOwnerAmount(total.getOwnerAmount() + summary.getOwnerAmount());
					
					newList.add(new WsVerifSumCheHistRealExp(summary));
				}
				newList.add(new WsVerifSumCheHistRealExp(total));
			}
			ExportUtil.exportData(newList, WsVerifSumCheHistRealExp.class.getName(), ExpTypeEnum.Excel, ElementType.METHOD, 
					"detailList", OilVerificationDetail.class, ElementType.METHOD, "结算升数详情", response, 
					"雪佛龙核销历史-直接发红包", "", request);
		} catch (Exception e) {
			logger.error(e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@RequestMapping(value = "/oilverification/export/sphistoryrealtime.do")
	public String spHistoryRealtime(@RequestParam("workshopName")String workshopName,
			@RequestParam("dateFrom")String dateFrom, @RequestParam("dateTo")String dateTo, 
			HttpServletRequest request, HttpServletResponse response){
		Date newDateFrom = StringUtils.isEmpty(dateFrom) ? DateUtil.getMinimumDate() : DateUtil.parseDate(dateFrom);
		Date newDateTo = StringUtils.isEmpty(dateTo) ? DateUtil.getMaximumDate() : DateUtil.addDays(DateUtil.parseDate(dateTo), 1);
		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
//			paramMap.put("workshopId", workshopId);
			paramMap.put("partnerId", ContextUtil.getCurUser().getOrgId());
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			
			if (!StringUtils.isNull(workshopName)){
				paramMap.put("workshopName", "%" + workshopName + "%");
			}
			
			List<WorkshopOilVerificationSummary> summaryList = oilVerificationBizService.queryOilVerificationSummaryListWithAwardRealtime(paramMap);
			List<WsVerifSumSpHistRealExp> newList = new ArrayList<WsVerifSumSpHistRealExp>();
			if (summaryList != null && !summaryList.isEmpty()){
				paramMap.put("workshopName", null);
				Iterator<WorkshopOilVerificationSummary> iter = summaryList.iterator();
				WorkshopOilVerificationSummary total = new WorkshopOilVerificationSummary();
				total.setWorkshopName("合计：");
				while (iter.hasNext()){
					WorkshopOilVerificationSummary summary = iter.next();
					//技师扫码升数详情
					paramMap.put("workshopId", summary.getWorkshopId());
					List<OilVerification> detailList = oilVerificationBizService.queryOilVerificationListWithAwardRealtime(paramMap);
					if(detailList != null){
						List<OilVerification> newDetails = new ArrayList<OilVerification>();
						for(OilVerification verification : detailList){
							newDetails.add(new OilVerificationDetail(verification));
						}
						summary.setDetailList(newDetails);
					}
					total.setClosedCapacity(total.getClosedCapacity() + summary.getClosedCapacity());
					total.setCanVerifyCapacity(total.getCanVerifyCapacity() + summary.getCanVerifyCapacity());
					total.setVerifyAmount(total.getVerifyAmout() + summary.getVerifyAmout());
					total.setMechanicAmount(total.getMechanicAmout() + summary.getMechanicAmout());
					total.setOwnerAmount(total.getOwnerAmount() + summary.getOwnerAmount());
					
					newList.add(new WsVerifSumSpHistRealExp(summary));
				}
				newList.add(new WsVerifSumSpHistRealExp(total));
			}
			ExportUtil.exportData(newList, WsVerifSumSpHistRealExp.class.getName(), ExpTypeEnum.Excel, ElementType.METHOD, 
					"detailList", OilVerificationDetail.class, ElementType.METHOD, "结算升数详情", response, 
					"合伙人核销历史-直接发红包", "", request);
		} catch (Exception e) {
			logger.error(e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@RequestMapping(value = "/oilverification/export/sphistory.do")
	public String spVerificationHistory(@RequestParam("workshopName")String workshopName, @RequestParam("partnerId")Long partnerId, 
			@RequestParam("dateFrom")String dateFrom, @RequestParam("dateTo")String dateTo, HttpServletRequest request, HttpServletResponse response){
		OilVerificationSpParams params = new OilVerificationSpParams();
		params.setPaging(false);
		params.setWorkshopName(workshopName);
		params.setPartnerId(partnerId);
		params.setDateFromStr(dateFrom);
		params.setDateToStr(dateTo);
		try {
//			paramMap.put("workshopId", workshopId);
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_SP.equals(user.getUserModel())){
				params.setPartnerId(user.getOrgId());
			}
			List<VerificationBatch> batchList = verificationBatchMapper.queryVerificationBatchForSp(params);
			List<VerificationBatch> newList = new ArrayList<VerificationBatch>(batchList.size());
			List<VerificationDetail> detailList = verificationDetailMapper.selectVerificationDetailForSPHExport(params);
			Map<Long, List<VerificationDetail>> detailListMap = new HashMap<Long, List<VerificationDetail>>();
			for(VerificationDetail detail : detailList){
				List<VerificationDetail> list = detailListMap.get(detail.getBatchId());
				if(list == null){
					list = new ArrayList<VerificationDetail>();
					detailListMap.put(detail.getBatchId(), list);
				}
				list.add(detail);
			}
			if(batchList != null){
				VerificationBatch total = new VerificationBatch();
				total.setWorkshopName("合计：");
				total.setTotalCapacity(0);
				total.setAmount(0.0);
				total.setMechanicAmount(0.0);
				total.setOwnerAmount(0.0);
				for(VerificationBatch batch : batchList){
					batch.setDetailList(detailListMap.get(batch.getId()));
					newList.add(new VerificationBatchSpExp(batch));
					if(batch.getTotalCapacity() != null){
						total.setTotalCapacity(total.getTotalCapacity() + batch.getTotalCapacity());
					}
					if(batch.getAmount() != null){
						total.setAmount(total.getAmount() + batch.getAmount());
					}
					if(batch.getMechanicAmount() != null){
						total.setMechanicAmount(total.getMechanicAmount() + batch.getMechanicAmount());
					}
					if(batch.getOwnerAmount() != null){
						total.setOwnerAmount(total.getOwnerAmount() + batch.getOwnerAmount());
					}
				}
				newList.add(new VerificationBatchSpExp(total));
			}
			ExportUtil.exportData(newList, VerificationBatchSpExp.class.getName(), ExpTypeEnum.Excel, ElementType.METHOD, 
					"detailList", VerificationDetail.class, ElementType.METHOD, "结算升数详情", response, 
					"合伙人核销历史", "", request); //-不直接发红包
		} catch (Exception e) {
			logger.error(e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@RequestMapping(value = "/oilverification/export/sp.do")
	public String spVerification(@RequestParam("partnerId")Long partnerId, @RequestParam("workshopName")String workshopName, 
	@RequestParam("isReport")Boolean isReport, @RequestParam("dateFrom")String dateFrom, 
			@RequestParam("dateTo")String dateTo, HttpServletRequest request, HttpServletResponse response){
		OilVerificationParams params = new OilVerificationParams();
		params.setPaging(false);
		params.setDateFromStr(dateFrom);
		params.setDateToStr(dateTo);
		params.setPartnerId(partnerId);
		params.setWorkshopName(workshopName);
		
		try {
//			paramMap.put("workshopId", workshopId);
			WxTUser user = ContextUtil.getCurUser();
			boolean isChevron = WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel());
			if(!isChevron){
				params.setPartnerId(user.getOrgId());
			}
			
			List<OilVerificationView> summaryList;
			if(isReport){
				summaryList = oilVerificationMapper.querySpOilVerificationReport(params);
			}else{
				summaryList = oilVerificationMapper.querySpOilVerification(params);
			}
			List<OilVerificationView> newList = null;
			if(isReport || isChevron){
				//雪佛龙导出带有合伙人列
				newList = new ArrayList<OilVerificationView>();
			}
			if (summaryList != null && !summaryList.isEmpty()){
				Map<String, List<OilVerification>> wsVeriDetailListMap = new HashMap<String, List<OilVerification>>();
				List<OilVerification> detailList = oilVerificationBizService.queryOilVerificationExpList(params, isReport ? "spreport" : "sp");
				for(OilVerification oilVerification : detailList){
					List<OilVerification> wsDetailList = wsVeriDetailListMap.get(oilVerification.getWorkshopId());
					if(wsDetailList == null){
						wsDetailList = new ArrayList<OilVerification>();
						wsVeriDetailListMap.put(oilVerification.getWorkshopId(), wsDetailList);
					}
					wsDetailList.add(new OilVerificationSpReportExp(oilVerification));
				}
				OilVerificationView total = new OilVerificationView();
				for (OilVerificationView summary : summaryList){
					if(isReport){
						newList.add(new WsOilVeriSumReport(summary));
					}else if(isChevron){
						newList.add(new WsOilVeriSumSpChe(summary));
					}
					//技师扫码升数详情
					summary.setDetailList(wsVeriDetailListMap.get(summary.getWorkshopId().toString()));
					total.setCapacity(total.getCapacity() + summary.getCapacity());
					total.setTotalReward(total.getTotalReward() + summary.getTotalReward());
					total.setMechanicReward(total.getMechanicReward() + summary.getMechanicReward());
					total.setOwnerReward(total.getOwnerReward() + summary.getOwnerReward());
					total.setSubmittedCapacity(total.getSubmittedCapacity() + summary.getSubmittedCapacity());
					total.setApprovedCapacity(total.getApprovedCapacity() + summary.getApprovedCapacity());

				}
				if(isReport || user.getmUserTypes() == 1){
					total.setPartnerName("合计：");
					if(isReport){
						newList.add(new WsOilVeriSumReport(total));
					}else{
						newList.add(new WsOilVeriSumSpChe(total));
					}
				}else{
					total.setWorkshopName("合计：");
					summaryList.add(total);
				}
			}
			String fileName = isReport ? "合伙人核销统计" : "合伙人核销";
			if(isReport || user.getmUserTypes() == 1){
				ExportUtil.exportData(newList, isReport ? WsOilVeriSumReport.class.getName() : WsOilVeriSumSpChe.class.getName(), 
						ExpTypeEnum.Excel, ElementType.METHOD, 
						"detailList", OilVerificationSpReportExp.class, ElementType.METHOD, "技师扫码升数详情", response, fileName, "", request);
			}else{
				ExportUtil.exportData(summaryList, OilVerificationView.class.getName(), ExpTypeEnum.Excel, ElementType.METHOD, 
						"detailList", OilVerificationSpReportExp.class, ElementType.METHOD, "技师扫码升数详情", response, fileName, "", request);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	@RequestMapping(value = "/oilverification/export/spverification.do")
	public String spVerificationExport(OilVerificationParams params, HttpServletRequest request, HttpServletResponse response){//
		try {
			boolean isChevron = WxTUser.USER_MODEL_CHEVRON.equals(ContextUtil.getCurUser().getUserModel());
			params.setPaging(false);
			List<VerificationDetail> summaryList = verificationDetailMapper.queryVerificationSumForPage(params);
			if (summaryList != null && !summaryList.isEmpty()){
				Map<Long, List<VerificationDetail>> wsVeriDetailListMap = new HashMap<Long, List<VerificationDetail>>();
				params.closeOrder();
				List<VerificationDetail> detailList = verificationDetailMapper.queryVerificationSumByMechanic(params);
				for(VerificationDetail detail : detailList){
					List<VerificationDetail> wsDetailList = wsVeriDetailListMap.get(detail.getWorkshopId());
					if(wsDetailList == null){
						wsDetailList = new ArrayList<VerificationDetail>();
						wsVeriDetailListMap.put(detail.getWorkshopId(), wsDetailList);
					}
					wsDetailList.add(detail);
				}
				VerificationDetail total = new VerificationDetail();
				total.setCapacity(0);
				total.setRedPackets(0.0);
				total.setMechanicAllocateAmount(0.0);
				total.setOwnerAllocateAmount(0.0);
				for (VerificationDetail summary : summaryList){
					//技师扫码升数详情
					summary.setVerificationDetails(wsVeriDetailListMap.get(summary.getWorkshopId()));
					total.setCapacity(total.getCapacity() + (summary.getCapacity() == null ? 0 : summary.getCapacity()));
					total.setVerificationAmount(total.getVerificationAmount() + summary.getVerificationAmount());
					total.setRedPackets(total.getRedPackets() + (summary.getRedPackets() == null ? 0.0 : summary.getRedPackets()));
					total.setMechanicAllocateAmount(total.getMechanicAllocateAmount() + (summary.getMechanicAllocateAmount() == null ? 0.0 : summary.getMechanicAllocateAmount()));
					total.setOwnerAllocateAmount(total.getOwnerAllocateAmount() + (summary.getOwnerAllocateAmount() == null ? 0.0 : summary.getOwnerAllocateAmount()));

				}
				if(isChevron){
					total.setPartnerName("合计：");
				}else{
					total.setWorkshopName("合计：");
				}
				summaryList.add(total);
			}
			spVerificationExportToExcel(summaryList, request, response, isChevron);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
	protected void spVerificationExportToExcel(List<VerificationDetail> summaryList, HttpServletRequest request, 
			HttpServletResponse response, boolean isChevron) throws Exception{
		List<ExportCol> exportCols = new ArrayList<ExportCol>();
		ExportCol col;
		if(isChevron){
			col = new ExportCol("partnerName", "合伙人名称", VerificationDetail.class);
			col.setWidth(30);
			exportCols.add(col);
		}
		col = new ExportCol("workshopName", "门店名称", VerificationDetail.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("capacity", "核销升数(L)", VerificationDetail.class);
		col.setWidth(20);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		col = new ExportCol("verificationAmountF", "结算金额(元)", VerificationDetail.class);
		col.setWidth(20);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		col = new ExportCol("redPacketsF", "技师实时激励金额(元)", VerificationDetail.class);
		col.setWidth(30);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		col = new ExportCol("mechanicAllocateAmountF", "技师非实时激励金额(元)", VerificationDetail.class);
		col.setWidth(30);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		col = new ExportCol("ownerAllocateAmountF", "店老板应得金额(元)", VerificationDetail.class);
		col.setWidth(30);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		List<ExportCol> detailCols = new ArrayList<ExportCol>();
		col = new ExportCol("mechanicName", "技师", VerificationDetail.class);
		col.setWidth(20);
		detailCols.add(col);
		col = new ExportCol("capacity", "扫码升数(L)", VerificationDetail.class);
		col.setWidth(20);
		col.setAlign(ExportColAlign.RIGHT);
		detailCols.add(col);
		col = new ExportCol("redPacketsF", "技师实时激励金额(元)", VerificationDetail.class);
		col.setWidth(30);
		col.setAlign(ExportColAlign.RIGHT);
		detailCols.add(col);
		col = new ExportCol("mechanicAllocateAmountF", "技师非实时激励金额(元)", VerificationDetail.class);
		col.setWidth(30);
		col.setAlign(ExportColAlign.RIGHT);
		detailCols.add(col);
		CommonUtil.setExportResponseHeader(request, response, "合伙人核销统计", "xls");
		ExportExcel.exportData(summaryList, exportCols, "verificationDetails", detailCols, "技师扫码详情", 
				response.getOutputStream(), "", true);
	}
	
//	@ResponseBody
//	@RequestMapping(value = "/oilverification/emailDayReport.do")
//	public Map<String, Object> emailDayReport(){
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try {
//			partnerResponsibleService.emailOilDayReport();
//			resultMap.put("code", "success");
//		} catch (Exception e) {
//			logger.error(e.getMessage(), e);
//			resultMap.put("code", "error");
//			resultMap.put("errorMsg", e.getMessage());			
//		}
//		return resultMap;
//	}
	
	@ResponseBody
	@RequestMapping(value = "/oilverification/scanDetailWeeklyEmail.do")
	public Map<String, Object> scanDetailWeeklyEmail(){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			oilVerificationBizService.sendScanDetailWeeklyReport();
			resultMap.put("code", "success");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			resultMap.put("code", "error");
			resultMap.put("errorMsg", e.getMessage());			
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/oilverification/scanDetailMonthEmail.do")
	public Map<String, Object> scanDetailMonthEmail(){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			oilVerificationBizService.sendLastMonthVerificationDetail();
			resultMap.put("code", "success");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			resultMap.put("code", "error");
			resultMap.put("errorMsg", e.getMessage());			
		}
		return resultMap;
	}
	@ResponseBody
	@RequestMapping(value ="/oilverification/queryChevronVerification.do")
	public Map<String, Object> queryChevronVerification(ChevronVerificationParams chevronVerificationParams){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap = oilVerificationService.queryVerification(chevronVerificationParams);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "error");
			resultMap.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/oilverification/queryChevronVerificationHistory.do")
	public Map<String, Object> queryChevronVerificationHistory(ChevronVerificationParams chevronVerificationParams){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap = oilVerificationService.queryVerificationBatchHistoryForChevron(chevronVerificationParams);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "error");
			resultMap.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));			
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/oilverification/queryVerificationBatchHistoryForSP.do")
	public Map<String, Object>queryVerificationBatchHistoryForSP(OilVerificationSpParams params){
		Map<String, Object> resultMap = new HashMap<String,Object>();
		resultMap = oilVerificationService.queryVerificationBatchHistoryForSP(params);
		return resultMap;
	}
}
