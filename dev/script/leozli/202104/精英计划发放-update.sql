ALTER TABLE [wx_t_qbr_cdm_performance_adjust] add flsr_comment                     nvarchar(4000)                                 null

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'EliteFund.award', 1024, 1, '精英计划基金发放。1024-FLSR意见编辑', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'EliteFund.award', 1024, 1, '精英计划基金发放。1024-FLSR意见编辑', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'EliteFund.award', 1024, 1, '精英计划基金发放。1024-FLSR意见编辑', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'EliteFund.award', 1024, 1, '精英计划基金发放。1024-FLSR意见编辑', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_BD'), 'EliteFund.award', 256, 1, '精英计划基金发放。256-上传附件', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'EliteFund.award', 256, 1, '精英计划基金发放。256-上传附件', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_OEM_Sales'), 'EliteFund.award', 256, 1, '精英计划基金发放。256-上传附件', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Industrial_Sales'), 'EliteFund.award', 256, 1, '精英计划基金发放。256-上传附件', 1, getdate());
