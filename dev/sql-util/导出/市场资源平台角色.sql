		select a.role_name "角色", a.ch_name "姓名", u1.login_name "登录名", u1.ch_name "用户名" from 
		wx_t_user u1 left join
		(
			select distinct di.dic_item_name role_name, di.sort_numb, u.ch_name,u.mobile_tel from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id left join wx_t_role r on r.role_id=ur.role_id
			left join wx_t_dic_item di on di.dic_type_code='MktResourcePlatform.roleMap' and di.dic_item_code=r.ch_role_name
			where u.status=1
			union all select distinct 'Distributor' role_name, 99910, u.ch_name sort_numb, u.mobile_tel from wx_t_user u left join wx_t_organization o on o.id=u.org_id where u.status=1 and (u.type is null or u.type!='1')
			and o.type=1
			union all select distinct 'Retailer' role_name, 99920, u.ch_name,u.mobile_tel  from wx_t_user u left join wx_t_organization o on o.id=u.org_id where u.status=1 
			and o.type=3
			union all select 'Mechanic' role_name, 99930, we.name,we.mobile from wx_t_workshop_employee we ) a on u1.mobile_tel=a.mobile_tel
			where u1.org_id=5 and u1.status=1 and len(u1.mobile_tel)>0 and a.role_name is not null
			order by a.sort_numb,u1.login_name 
			
