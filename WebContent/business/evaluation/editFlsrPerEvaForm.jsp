<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="title" value="${'FLSR Performance Evaluation Form FLSR绩效评估表' }"/>
<c:set var="isEdit" value="${param.opFlag ne 'view'}"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<title>${title }</title>
		<%@include file="/common/jsp/common.jsp"%>
		<script type="text/javascript" src="${ctx }business/evaluation/js/editFlsrPerEvaForm.js?v=${version}5"></script>
		<style type="text/css">
		.td-title {
			width: 180px;
		}
		.tr-row2 {
			height: 38px;
		}
		.themes-evaluation .plain-td {
			padding: 5px;
		}
		.themes-evaluation .ui-grid-unlocked .ui-grid-part-last-td {
			border-right: none;
		}
		.themes-evaluation .ui-grid-header td {
			background-color: #e7eaec;
			font-weight: bold;
		}
		.themes-evaluation .ui-grid-header .ui-grid-cell {
			line-height: 25px;
		}
		.themes-evaluation .ui-grid-cell {
			line-height: 18px;
		}
		div.ui-grid {
			border-left: 1px solid #dddddd;
			border-top: 1px solid #dddddd;
			border-right: 1px solid #dddddd;
		}
		.themes-evaluation th, .themes-evaluation .th {
			background-color: #e7eaec;
			font-weight: bold;
		}
		input[type="text"].readonly-text {
			border: none;
			height: 22px;
			width: calc(100% - 2px);
			box-shadow: none;
			font-weight: 600;
    		font-family: "微软雅黑"
		}
		.paragraph-header {
			font-weight: bold;
			font-size: 14px;
		}
		.text-right input {
			text-align: right;
		}
		.tr-row3 {
		}
		.td-control-container {
			width: 100%;
		}
		.td-control-container textarea {
			width: calc(100% - 5px);
		}
		.td-control-container input {
			width: calc(100% - 2px);
		}
		.td-control-container input {
			height: 26px;
		}
		.page-comment {
			color: #0b2d71;
		}
		.td-header {
			font-weight: bold;
		}
		.text-center input {
		    text-align: center;
		}
		#rulesDialog pre {
			font-family: "微软雅黑";
	    	white-space: pre-wrap;
		}
		</style>
		<script type="text/javascript">
		var isEdit = ${isEdit}, id="${param.id}", opRole = '${param.opRole}', evaluationMonth = '${param.evaluationMonth}', salesId = '${param.salesId}';
		</script>
	</head>
	<body class="gray-bg">
		<div class="content-wrapper" style="min-width: 960px;">
	    	<div class="content-panel header-panel ">
	    		<div class="header-title">${title }</div>
	    		<div class="header-btns">
	    		<c:choose>
	    			<c:when test="${isEdit}">
	    				<button type="button" class="btn-submit" onclick="save(20);">提交</button>
	    				<button type="button" class="btn-submit" onclick="save(10);">暂存</button>
	    			</c:when>
	    		</c:choose>
	    		<c:choose>
	    			<c:when test="${param.opRole eq 'sales' }">
						<button type="button" class="btn-back" id="returnListBtn" onclick="window.location = '${ctx }business/evaluation/flsrPerEvaFormPage.jsp?cacheParams=true&toItem=${param.id }';">返回</button>
	    			</c:when>
	    			<c:when test="${param.opRole eq 'supervisor' }">
	    				<!-- <c:if test="${param.status ge '20' }">
	    					<button type="button" id="rejectBtn" class="btn-submit" onclick="reject(10);">驳回</button>
	    				</c:if> -->
						<button type="button" class="btn-back" id="returnListBtn" onclick="window.location = '${ctx }business/evaluation/flsrAsmPage.jsp?cacheParams=true&formCode=FlsrPerEvaForm&toItem=${param.id }';">返回</button>
	    			</c:when>
	    			<c:otherwise>
						<button type="button" class="btn-back" id="returnListBtn" onclick="window.location = '${ctx }evaluationprogram/index.do?bu=${param.bu }&formCode=FlsrPerEvaForm&cacheParams=true&toItem=${param.id }';">返回</button>
	    			</c:otherwise>
	    		</c:choose>
	    		</div>
	    	</div>
	    	<form action="" id="editForm">
			<input type="hidden" name="id"/>
			<input type="hidden" name="salesId"/>
			<input type="hidden" name="supervisorId"/>
			<input type="hidden" name="programId"/>
			<div class="content-panel info-block themes-evaluation info-base">
	    		<a href="javascript:void(0)" style="float:right;margin-bottom: 5px;" onclick="showEvaluationRules();">评分规则</a>
			<table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table ui-grid" style="width: 100%;">
				<tbody>
					<tr class="ui-grid-data-row tr-row2">
						<th valign="middle" class="ui-grid-td plain-td text-left" style="width: 100px;"><div class="td-title">FLSR销售</div></th>
						<td valign="middle" class="ui-grid-td plain-td text-left" style="width: 45%;"><input type="text" name="salesName" readonly="readonly" class="readonly-text"/></td>
						<th valign="middle" class="ui-grid-td plain-td text-left" style="width: 100px;"><div class="td-title"><div class="excel-row">Channel&Region 渠道及区域</div><div class="excel-row"></div></div></th>
						<td valign="middle" class="ui-grid-td plain-td text-left" style="width: 55%;"><input type="text" name="regionName" readonly="readonly" class="readonly-text"/></td>
					</tr>
					<tr class="ui-grid-data-row tr-row2">
						<th valign="middle" class="ui-grid-td plain-td text-left">Manager经理</th>
						<td valign="middle" class="ui-grid-td plain-td text-left"><input type="text" name="supervisorName" readonly="readonly" class="readonly-text"/></td>
						<th valign="middle" class="ui-grid-td plain-td text-left">Month月份</th>
						<td valign="middle" class="ui-grid-td plain-td text-left"><input type="text" name="evaluationMonthText" readonly="readonly" class="readonly-text"/></td>
					</tr>
				</tbody>
			</table>
			</div>
			<div class="content-panel info-block themes-evaluation info-target">
			<table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table ui-grid" style="width: 100%;">
				<tbody>
					<tr class="ui-grid-data-row tr-row2">
						<th valign="middle" class="ui-grid-td plain-td text-left" style="width: 100px;"><div class="td-title">Metrics项目</div></th>
						<th valign="middle" class="ui-grid-td plain-td text-center" align="center" style="width: 28%;">
							<div class="td-header">
								<div class="excel-row">Target目标 YTD</div>
							</div>
						</th>
						<th valign="middle" class="ui-grid-td plain-td text-center" align="center" style="width: 18%;">
							<div class="td-header">
								<div class="excel-row">Weighted %权重</div>
							</div>
						</th>
						<th valign="middle" class="ui-grid-td plain-td text-center" align="center" style="width: 18%;">
							<div class="td-header">
								<div class="excel-row">Result完成YTD</div>
							</div>
						</th>
						<th valign="middle" class="ui-grid-td plain-td text-center" align="center" style="width: 18%;">
							<div class="td-header">
								<div class="excel-row">Score打分**</div>
							</div>
						</th>
						<th valign="middle" class="ui-grid-td plain-td text-center" align="center" style="width: 18%;">
							<div class="td-header">
								<div class="excel-row">Final Score分数</div>
							</div>
						</th>
					</tr>
					<tr class="ui-grid-data-row">
						<th valign="middle" class="ui-grid-td plain-td text-left">Volume销售量(KL)</th>
						<td valign="middle" class="ui-grid-td plain-td text-right"><div class="td-control-container"><input type="text" name="volumeTarget" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="volumeWeight"/><input type="text" name="volumeWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><input type="hidden" name="volumeResult"/><div class="td-control-container"><input type="text" name="volumeResultText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="volumeScore" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="volumeFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<th valign="middle" class="ui-grid-td plain-td text-left">Gross Margin毛利(KUSD)</th>
						<td valign="middle" class="ui-grid-td plain-td text-right"><div class="td-control-container"><input type="text" name="crossMarginTarget" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="crossMarginWeight"/><input type="text" name="crossMarginWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><input type="hidden" name="crossMarginResult"/><div class="td-control-container"><input type="text" name="crossMarginResultText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="crossMarginScore" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="crossMarginFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<th valign="middle" class="ui-grid-td plain-td text-left" rowspan="4">
							<div class="td-header">
								<div class="excel-row">Sales Reports</div>
								<div class="excel-row">销售报告</div>
							</div>
						</th>
						<td valign="middle" class="ui-grid-td plain-td text-left">
								<div class="excel-row">Number of accounts visits including potential/existing ones(per agreed)</div>
								<div class="excel-row">潜在/现有客户访问次数</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="accountsVisitsWeight"/><input type="text" name="accountsVisitsWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="accountsVisitsResult" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="accountsVisitsScore"${isEdit ? ' onchange="updateAccountsVisitsScore(this)";' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="accountsVisitsFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<td valign="middle" class="ui-grid-td plain-td text-left">
								<div class="excel-row">Issues, gaps / action plans ( be SMART)</div>
								<div class="excel-row">问题反馈/行动计划</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="issuesWeight"/><input type="text" name="issuesWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="issuesResult" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="issuesScore"${isEdit ? ' onchange="updateIssuesScore(this)";' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="issuesFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<td valign="middle" class="ui-grid-td plain-td text-left">
								<div class="excel-row">Number of new opportunities/follow up in SFDC</div>
								<div class="excel-row">新销售机会录入数目</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="sfdcWeight"/><input type="text" name="sfdcWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="sfdcResult" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="sfdcScore"${isEdit ? ' onchange="updateSfdcScore(this)";' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="sfdcFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<td valign="middle" class="ui-grid-td plain-td text-left">
								<div class="excel-row">Complete the reports on time and meet requirements</div>
								<div class="excel-row">及时高质量完成报告</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="completeQualityWeight"/><input type="text" name="completeQualityWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="completeQualityResult"${isEdit ? '' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="completeQualityScore"${isEdit ? ' onchange="updateCompleteQualityScore(this)";' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="completeQualityFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<th valign="middle" class="ui-grid-td plain-td text-left">
							<div class="td-header">
								<div class="excel-row">Attuite & behavior</div>
								<div class="excel-row">态度&行为</div>
							</div>
						</th>
						<td valign="middle" class="ui-grid-td plain-td text-left">
								<div class="excel-row"><span class="required-flag">*</span>具体评分标准请参见评分规则</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="hidden" name="attuiteBehaviorWeight"/><input type="text" name="attuiteBehaviorWeightText" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="attuiteBehaviorResult"${isEdit ? '' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="attuiteBehaviorScore"${isEdit ? ' onchange="updateAttuiteBehaviorScore(this)";' : ' disabled="disabled"' }/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="attuiteBehaviorFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<td valign="middle" class="ui-grid-td plain-td text-left" colspan="2">
							<div class="td-header">
								<div class="excel-row">Total总数</div>
							</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="totalWeight" readonly="readonly" class="readonly-text"/></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"></div></td>
						<td valign="middle" class="ui-grid-td plain-td text-center"><div class="td-control-container"><input type="text" name="totalFinalScore" readonly="readonly" class="readonly-text"/></div></td>
					</tr>
					<tr class="ui-grid-data-row">
						<td valign="middle" class="ui-grid-td plain-td text-left">
							<div class="td-header">
								<div class="excel-row">Comments</div>
							</div>
						</td>
						<td valign="middle" class="ui-grid-td plain-td text-left" colspan="5"><div class="td-control-container"><textarea name="comments" class="control-textarea"${isEdit ? '' : ' disabled="disabled"'}></textarea></div></td>
					</tr>
				</tbody>
			</table>
			</div>
			</form>
		</div>
		<div class="hide-important">
			<div id="rulesDialog">
			<div class="content-panel paragraph-header first-content-panel">
			Attuite & behavior 态度&行为 具体评分参考标准
(销售经理根据实际表现情况给FLSR评分）
			</div>
<pre>
1. Proactive & Seek Solutions积极主动 
2. All company required trainings (LMS)公司培训
3. LPO                                                  
4. Disciplinary /compliance 规章制度   
5. Distributor/territory  management 经销商管理                                            
6. SFDC                                                 
7. Coordination with functional teams 与支持部门合作                           
8. Customer relationships & service & timely response customer request客户关系服务及客户需求响应 
9. Quality of all required reports 报告的质量
</pre>			
			<div class="content-panel paragraph-header">
			项目打分分值范围
			</div>
<pre>
Volume销售量&Gross Margin毛利：0-120分，会有超额完成目标的情况Sales Reports销售报告&Attuite & behavior 态度&行为：0-100分	
</pre>
		</div>
		</div>
	</body>
</html>
