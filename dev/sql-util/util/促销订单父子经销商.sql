
			SELECT del.sales_order, del.distributor_id, xx1.ext_property7,MAX(p.promotion_name) AS promotion_name, xx1.promotion_id, MAX(p.update_time) AS u_time
				, MAX(del.post_year_month) AS post_year_month, del.distributor_id
				, (
					SELECT DISTINCT cos.customer_name_cn
					FROM PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos
					WHERE cos.distributor_id = del.distributor_id
				) AS customer_name, del.product_sku, MAX(p1.name) AS product_name
				, SUM(del.quantity) AS quantity, MAX(del.pack_units) AS pack_units
				, SUM(del.liters) AS liters, SUM(del.revenue_rmb) AS revenue_rmb
				, del.region, xx1.award_type, xx1.source_type, MAX(xx1.award_quantity) AS award_quantity
				, MAX(xx1.promotion_desc) AS promotion_desc, xx1.delivery_status, xx1.delivery_time
				, MAX(xx1.ext_property1) AS ext_property1, MAX(xx1.ext_property2) AS ext_property2
				, MAX(xx1.ext_property3) AS ext_property3, xx1.offer_key
			FROM (
				SELECT t1.promotion_id, t1.award_type, t1.source_type, SUM(t1.award_quantity) AS award_quantity
					, MAX(t1.promotion_desc) AS promotion_desc, t1.delivery_status AS deliveryStatus
					, MAX(t1.ext_property1) AS ext_property1, MAX(t1.ext_property2) AS ext_property2
					, MAX(t1.ext_property3) AS ext_property3, t1.offer_key, t1.ext_property7
					, t1.delivery_status, t1.delivery_time, t1.ext_property9
				FROM wx_t_promotion_delivery_detail t1
				WHERE 1 = 1
					AND t1.delivery_status = '10'
				GROUP BY t1.award_type, t1.promotion_id, t1.source_type, t1.delivery_status, t1.delivery_time, t1.offer_key, t1.ext_property7, t1.delivery_status, t1.ext_property9
			) xx1
				LEFT JOIN wx_t_sell_in_promotion_delivery del
				ON /*del.distributor_id = xx1.ext_property7
					AND*/ EXISTS (
						SELECT 1
						FROM wx_t_promotion_delivery_detail t1
							LEFT JOIN wx_t_delivery_order_rel dor ON t1.id = dor.delivery_detail_id
						WHERE t1.offer_key = xx1.offer_key
							AND t1.ext_property7 = xx1.ext_property7
							AND t1.ext_property9 = xx1.ext_property9
							AND del.delivery_id = dor.delivery_id
					)
				LEFT JOIN wx_t_product p1 ON p1.sku = del.product_sku
				LEFT JOIN wx_t_sell_in_promotion p ON p.id = xx1.promotion_id
			WHERE del.distributor_id!=xx1.ext_property7
			GROUP BY del.distributor_id, del.product_sku, xx1.award_type, xx1.promotion_id, xx1.source_type, del.region, xx1.delivery_status, xx1.delivery_time, xx1.offer_key
			,del.sales_order,del.distributor_id, xx1.ext_property7

			
--select * from wx_t_delivery_order_rel where delivery_detail_id=1724
select top 100 * 
--update d set d.distributor_id=1184
from wx_t_sell_in_promotion_delivery d where sales_order='0221805492'			