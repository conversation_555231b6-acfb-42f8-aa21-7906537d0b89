select * into wx_t_work_shop20210324 from wx_t_work_shop

select top 1000 * from wx_t_work_shop_old_2020 wo where wo.ext_flag&2048>0 and wo.ext_flag&1024>0 order by id desc;
select * from wx_log where log_type='Delo400ActivityLog' and ext_property4='2048' order by id desc;

select * 
--update w set w.ext_flag=w.ext_flag-2048
from wx_t_work_shop w left join wx_t_work_shop_old_2020 wo on w.id=wo.id
where w.ext_flag&2048=2048 and wo.ext_flag&2048=2048

select * 
--update w set w.ext_flag=w.ext_flag|2048
from wx_t_work_shop w where ext_property2 is not null and ext_flag&2048=0

select * 
--update w set w.ext_flag=w.ext_flag|2048
from wx_t_work_shop w left join wx_t_work_shop_old_2020 wo on w.id=wo.id
where w.ext_flag&2048=0 and wo.shop_recruitment=1 and wo.from_source=2
and w.delete_flag=0