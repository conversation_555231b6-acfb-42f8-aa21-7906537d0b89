;(function($){
	UI.Paging = UI.extend(UI.Base, {
		init: function(opts){
			var instance = this.newInstance(opts);
			instance.style = this.styles[instance.style];
			return instance;
		},
		base: {
			update: function(totalCount, pageNo, totalPage, grid){
				this.style.update(totalCount, pageNo, totalPage, grid);
			}
		},
		defaults: {
			pageSize: 10,
			pageNo: 1,
			style: 'default'
		},
		styles: {
			'default': {
				update: function(totalCount, pageNo, totalPage, grid){
					if(!grid._paging){
						var h = ['<div class="page-size-bar">每页显示<input class="page-size-value" type="hidden" value="',
						         grid.paging.pageSize, '"/><span class="page-size-select"></span>条记录</div><div class="ui-paging-bar ui-paging-bar-default">'];
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: 1});', {
							'class': 'ui-paging-first-btn ui-paging-btn',
							disabled: pageNo == 1
						});
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: grid.store.params.pageNo - 1});', {
							'class': 'ui-paging-pre-btn ui-paging-btn',
							disabled: pageNo == 1
						});
						h.push('<span class="ui-page-bar-separator"/><span class="ui-total-page">共<span class="ui-total-page-value">', totalPage, 
						'</span>页</span><span class="ui-input-page-no">第');
						UI.Int.create(h, {
							defValue: 1,
							value: pageNo,
							disabled: totalPage == 1,
							'class': 'ui-page-no-int',
							validator: {
								min: 1,
								max: totalPage
							}
						});
						h.push('页');
						UI.Button.create(h, '确定', "var $g = $(this).parents('." + UI.Grid.keyCls + 
								":first'), grid = UI.Grid.getInstance($g), pageNo = grid._paging.find('.ui-input-page-no>input').val();if(!isNaN(pageNo)){grid.store.load({pageNo: parseInt(pageNo)});}", {
							'class': 'ui-page-no-go-btn',
							disabled: totalPage == 1
						});
						h.push('</span><span class="ui-page-bar-separator"/>');
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: grid.store.params.pageNo + 1});', {
							'class': 'ui-paging-next-btn ui-paging-btn',
							disabled: pageNo == totalPage
						});
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.keyCls + ':first\'));grid.store.load({pageNo: grid._totalPage});', {
							'class': 'ui-paging-last-btn ui-paging-btn',
							disabled: pageNo == totalPage
						});
						h.push('<span class="ui-page-bar-separator"/><span class="ui-total-record">共<span class="ui-total-record-value">',
								totalCount, '</span>条记录</span></div>');
						grid._paging = $(h.join('')).appendTo(grid.pagingContainer);
						BUI.use('bui/select',function(Select){
							var items = ['10', '20', '25', '50', '100'];
							for(var i = 0; i < items.length; i++){
								if(grid.paging.pageSize < items[i] - 0){
									items.splice(i, 0, grid.paging.pageSize + '');
									break;
								}else if(grid.paging.pageSize == items[i] - 0){
									break;
								}
							}
							var ctrl = new Select.Select({
								render:grid._paging.find('.page-size-select'),
								valueField: grid._paging.find('.page-size-value'),
								elStyle: {'white-space': 'nowrap'},
								items: items
							});
							ctrl.render();
							ctrl.on('change', function(e){
								grid.paging.pageSize = e.value - 0;
								grid.store.load({pageNo: 1});
							});
						});
					}else{
						UI.Button.setDisabled(grid._paging.find('.ui-paging-first-btn'), pageNo == 1);
						UI.Button.setDisabled(grid._paging.find('.ui-paging-pre-btn'), pageNo == 1);
						UI.Button.setDisabled(grid._paging.find('.ui-page-no-go-btn'), totalPage == 1);
						UI.Button.setDisabled(grid._paging.find('.ui-paging-next-btn'), pageNo == totalPage);
						UI.Button.setDisabled(grid._paging.find('.ui-paging-last-btn'), pageNo == totalPage);
						var $int = grid._paging.find('.ui-page-no-int');
						UI.Int.setDisabled($int, totalPage == 1);
						UI.Int.setValue($int, pageNo);
						grid._paging.find('.ui-total-page-value').text(totalPage);
						grid._paging.find('.ui-total-record-value').text(totalCount);
					}
					grid._totalPage = totalPage;
				}
			}
		}
	});
})(jQuery);