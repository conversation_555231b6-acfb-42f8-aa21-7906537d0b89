package com.chevron.log.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.log.model.WxTImportLogParams;
import com.chevron.log.service.WxTImportLogService;
import com.chevron.pms.model.GenericPaginationQueryParams;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;

@Controller
public class WxTImportLogController {
	
	@Autowired
	private WxTImportLogService importLogService;

	private Logger log = LoggerFactory
			.getLogger(WxTImportLogController.class);

	private static final String ROWS = "rows";
	private static final String TOTAL = "total";
	private static final String RANK_TYPE = "direction";
	private static final String SORT_FILED = "field";
	private static final String LIMIT = "limit";
	private static final String START = "start";

	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";

	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	
	@ResponseBody
	@RequestMapping(value = "/log/queryImportLog.do", method = { RequestMethod.POST })
	public Map<String, Object> queryProductCode(WxTImportLogParams params) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			resultMap = importLogService.queryImportLogByConditionWithPagination(params);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

	private GenericPaginationQueryParams convertRequestParamToQueryParam(
			Map<String, String> requestParamMap) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		genericPagQueryParam
				.setStart(Long.parseLong(requestParamMap.get(START)));
		genericPagQueryParam.setLimit(Integer.parseInt(requestParamMap
				.get(LIMIT)));

		String orderByFiled = requestParamMap.get(SORT_FILED);
		String orderByRankType = requestParamMap.get(RANK_TYPE);
		String orderBy = StringUtils.camelToUnderline(orderByFiled) + " "
				+ orderByRankType;
		genericPagQueryParam.setOrderBy(orderBy);

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();
		
		String fileName = requestParamMap.get("fileName");
		if(null != fileName && !fileName.isEmpty()){
			queryParamsMap.put("fileName", fileName);
		}
		
		String importResult = requestParamMap.get("importResult");
		if(null != importResult && !importResult.isEmpty()){
			queryParamsMap.put("importResult", importResult);
		}
		

		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		return genericPagQueryParam;
	}

}
