select 
sum(report_times) report_times,
sum(case when report_type=1 then 1 else 0 end) app_scan_times,
sum(case when report_type=2 then 1 else 0 end) user_scan_times,
sum(case when report_type=3 then 1 else 0 end) admin_record_times,
sum(case when report_type=4 then 1 else 0 end) mechanic_scan_times,
out_date
from(
select 1 report_times,report_type,
CONVERT(nvarchar(20), rv.req_time, 23) out_date
 from view_nonlocal_sale_report rv
 left join wx_t_region r2 on r2.id=rv.reporting_region_id
 left join wx_t_region r3 on r3.id=r2.parent_id
	  left join [PP_MID].[dbo].[syn_dw_to_pp_product] p on rv.sku=p.product_code_SAP
where rv.from_distributor_id=#BIGINT,fromDistributorId#
and rv.channel&#INTEGER,channel#>0
 and (#VARCHAR,t3Category# is null or #VARCHAR,t3Category#='' or p.[t3_category] like '%' + #VARCHAR,t3Category# + '%')
and isnull(r3.id,-1)=#BIGINT,provinceId#
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or rv.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or rv.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
			group by rv.channel,convert(nvarchar(20),rv.req_time,23),rv.to_distributor_id,r2.id,r3.id,rv.report_type
) tt group by out_date