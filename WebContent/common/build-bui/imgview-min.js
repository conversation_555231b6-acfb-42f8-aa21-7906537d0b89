/**
 * @fileOverview imgview \u547d\u540d\u7a7a\u95f4\u5165\u53e3
 * @ignore
 */(function(){var e="bui/imgview";define(e,["bui/common",e+"/imgview",e+"/viewcontent",e+"/previewlist"],function(t){var n=t("bui/common"),r=n.namespace("ImgView");return n.mix(r,{ImgView:t(e+"/imgview"),ViewContent:t(e+"/viewcontent"),PreviewList:t(e+"/previewlist")}),r})})(),define("bui/imgview/imgview",["bui/common","bui/toolbar","bui/imgview/viewcontent","bui/imgview/previewlist"],function(e){var t=e("bui/common"),n=e("bui/imgview/viewcontent"),r=e("bui/imgview/previewlist"),i=e("bui/toolbar"),s=t.Component.Controller.extend({initializer:function(){var e=this,s=e.get("el"),o=e.get("children"),u=new n(t.mix(e.get("viewContent"),{elCls:"ui-img-view-content"})),a=new i.Bar({elCls:"ui-img-view-toolbar",children:e.get("commands"),itemTpl:'<a data-cmd="{cmd}" href="javascript:;">{text}</a>'}),f=new r({elCls:"ui-img-view-mini-wrap"});o.push(u),o.push(f),o.push(a),e.set("toolBar",a),e.set("viewContent",u),e.set("previewList",f)},renderUI:function(){var e=this,t=e.get("toolBar"),n=e.get("viewContent"),r=e.get("el");n.get("el").append('<a class="paintPrev J_paintPrev" href="#"></a><a class="paintNext J_paintNext" href="#"></a>'),r.addClass("img-view-controls-wrap")},bindUI:function(){this._bindToolbar(),this._bindSelectedChange(),this._bindPrevNext()},_bindPrevNext:function(){var e=this,t=e.get("viewContent"),n=t.get("el");n.hover(function(){n.find(".J_paintPrev,.J_paintNext").show()},function(){n.find(".J_paintPrev,.J_paintNext").hide()}),n.find(".J_paintPrev").on("click",function(t){t.preventDefault(),e.paintPrev()}),n.find(".J_paintNext").on("click",function(t){t.preventDefault(),e.paintNext()})},_bindToolbar:function(){var e=this,t=e.get("viewContent"),n=e.get("toolBar");n.on("click",function(e){var n=e.target,r=n.get("cmd");if(r=="viewImg"){var i=t.getSrc();$(e.domTarget).attr("href",i)}else r&&t[r]&&t[r]()})},_bindSelectedChange:function(){var e=this;e.get("previewList").set("selectedchange",function(t,n){var r=e.get("selectedchange"),i=e.get("viewContent"),s=e.get("imgList");s.length&&(i.set("imgSrc",s[n].src),e.set("imgNum",n),r(n,s[n].src))})},getViewContent:function(){return this.get("viewContent")},paintPrev:function(){var e=this,t=e.get("imgNum");t-1>=0&&e.set("imgNum",t-1)},paintNext:function(){var e=this,t=e.get("imgList"),n=e.get("imgNum");n+1<t.length&&e.set("imgNum",n+1)},reset:function(){this.getViewContent().reset()},getLength:function(){return this.get("imgList").length},getSrc:function(){return this.getViewContent().get("imgSrc")},_uiSetWidth:function(e){e=e||this.get("width");var t=this.get("sidebarWidth");this.get("viewContent").set("width",e-t),this.get("toolBar").set("width",e-t)},_uiSetHeight:function(e){e=e||this.get("height"),this.get("viewContent").set("height",e-this.get("toolbarHeight")),this.get("previewList").set("height",e)},_uiSetToolbarHeight:function(e){this.get("toolBar").set("height",e)},_uiSetSidebarWidth:function(e){this.get("previewList").set("width",e)},_uiSetImgNum:function(e){this.get("previewList").set("selected",e)},_uiSetImgList:function(e){var t=this,n=t.get("previewList");n.set("items",e)},destructor:function(){}},{ATTRS:{imgList:{value:[]},imgNum:{value:0},commands:{value:[{cmd:"fitToggle",text:"\u81ea\u52a8\u5927\u5c0f"},{cmd:"resume",text:"\u5b9e\u9645\u5927\u5c0f"},{cmd:"fit",text:"\u9002\u5408\u5927\u5c0f"},{cmd:"leftHand",text:"\u5de6\u65cb"},{cmd:"rightHand",text:"\u53f3\u65cb"},{cmd:"zoom",text:"\u653e\u5927"},{cmd:"micrify",text:"\u7f29\u5c0f"},{cmd:"viewImg",content:'<a href="#" target="_blank">\u67e5\u770b\u539f\u56fe</a>'}]},sidebarWidth:{value:100},toolbarHeight:{value:50},viewContent:{value:{rotateTime:300,scaleTime:300,zoomRate:1.5,micrifyRate:.66,overflowSize:100}},elCls:{value:"ui-img-view-wrap"},selectedchange:{value:function(){}}}});return t.mix(s,{ViewContent:n,PreviewList:r}),s}),define("bui/imgview/previewlist",["bui/common","bui/list"],function(e){var t=e("bui/common"),n=e("bui/list"),r=function(e,t){var n=e.width(),r=e.height(),i=new Image,s=jQuery("<img>");return i.onload=function(){var o=i.width,u=i.height;if(o/u>n/r){var a=n/o*u;s.css({width:n,height:a,marginTop:(r-a)/2})}else{var f=r/u*o;s.css({width:f,height:r,marginLeft:(n-f)/2})}s[0].src=t,e.html(s)},i.src=t,i};Array.indexOf||(Array.prototype.indexOf=function(e){for(var t=0;t<this.length;t++)if(this[t]==e)return t;return-1});var i=t.Component.Controller.extend({initializer:function(){var e=this,t=e.get("children"),r=new n.SimpleList({elCls:e.get("listCls"),itemTpl:e.get("itemTpl")});t.push(r),e.set("list",r)},renderUI:function(){var e=this,t=e.get("list"),n=e.get("alignType"),r=$(t.get("el")).find("ul"),i=e.get("el");e.set("ul",r),i.prepend('<a href="#" class="ui-previewlist-prev J_previewPrev"></a>'),i.append('<a href="#" class="ui-previewlist-next J_previewNext"></a>'),n=="width"&&(r.width(1e4),i.addClass("ui-preview-wrap-width")),r.addClass("clearfix"),e._uiSetHeight()},bindUI:function(){this._bindSelect(),this._bindScroll()},_bindSelect:function(){var e=this,t=e.get("list");t.on("selectedchange",function(t){var n=e.get("items"),r=n.indexOf(t.item),i=e.get("pageSize"),s=e.get("selectedchange");e.set("selected",r),e.setPage(parseInt(r/i)),s&&s(t,r)})},_bindScroll:function(){var e=this,t=e.get("el"),n=e.get("itemHeight");t.find(".J_previewNext").on("click",function(t){t.preventDefault(),e.goNext()}),t.find(".J_previewPrev").on("click",function(t){t.preventDefault(),e.goPrev()})},setPage:function(e,t){var n=this,e=typeof e=="number"?e:parseInt(n.get("selected")/n.get("pageSize")),r=n.get("maxPage"),i=!1;return e>=0&&e<r?i=!0:e>=r&&r!=0&&(e=r-1,i=!0),i&&n.set("pageNum",e),i},_uiSetPageNum:function(e){var t=this,n=t.get("ul"),r=t.get("scrollSize"),i=t.get("scrollTime"),s=t.get("alignType");s=="height"?n.clearQueue().animate({marginTop:-e*r},i):s=="width"&&n.clearQueue().animate({marginLeft:-e*r},i)},goPrev:function(){this.setPage(this.get("pageNum")-1)},goNext:function(){this.setPage(this.get("pageNum")+1)},getSelect:function(){return this.get("selected")},getLength:function(){return this.get("items").length||0},_uiSetWidth:function(e){e=e||this.get("width");var t=this,n=t.get("list"),r=t.get("alignType"),i=e-90;r=="width"&&(n.set("width",i),t.set("listWidth",i),t.__initPageInfo())},_uiSetHeight:function(e){e=e||this.get("height");var t=this,n=t.get("list"),r=t.get("alignType"),i=e-90;r=="height"&&(n.set("height",i),t.set("listHeight",i),t.__initPageInfo())},_uiSetSelected:function(e){var t=this,n=t.get("items"),r=t.get("pageSize"),i=t.get("list");n.length!=0&&(i.setSelected(n[e]),t.setPage(parseInt(e/r)))},_uiSetItems:function(e){var t=this,n=t.get("itemWidth"),i=t.get("itemHeight");t.get("list").setItems(e),t.get("el").find(".ui-preview-mini-wrap").each(function(t){$(this).width(n-8).height(i-8),r($(this),e[t].miniSrc)}),t.__initPageInfo(),t.get("_rendered")&&t._uiSetSelected(0),t.set("_rendered",!0)},__initPageInfo:function(){var e=this,t=e.get("alignType"),n=e.get("itemHeight"),r=e.get("itemWidth"),i=e.get("listHeight"),s=e.get("listWidth"),o=e.get("height");if(t=="width"){var u=parseInt(s/r),a=u*r,f=e.getLength(),l=f%u==0?f/u:parseInt(f/u)+1;e.set("pageSize",u),e.set("scrollSize",a),e.set("maxPage",l)}else if(t=="height"){var u=parseInt(i/n),a=u*n,f=e.getLength(),l=f%u==0?f/u:parseInt(f/u)+1;e.set("pageSize",u),e.set("scrollSize",a),e.set("maxPage",l)}},destructor:function(){}},{ATTRS:{elCls:{value:"ui-preview-wrap"},listCls:{value:"ui-preview-simple-list"},itemTpl:{value:'<li class="ui-preview-mini-wrap"></li>'},items:{value:[]},selectedchange:{value:function(e){}},selected:{value:0},pageNum:{value:0},itemWidth:{value:100},itemHeight:{value:60},controls:{value:!1},scrollTime:{value:300},alignType:{value:"height"},_rendered:{value:!1}}});return i}),define("bui/imgview/viewcontent",["bui/common","bui/graphic"],function(e){function r(e,t,n,r){var i={};return e/t>n/r?(i.height=n/e*t,i.width=n):(i.width=r/t*e,i.height=r),i.x=(n-i.width)/2,i.y=(r-i.height)/2,i}var t=e("bui/common"),n=e("bui/graphic"),i=t.Component.Controller.extend({initializer:function(){var e=this,n=new Image;e.set("targetImg",n),n.onload=function(){var i=n.width,s=n.height,o=e.get("width"),u=e.get("height"),a=r(i,s,o,u);e.get("imgObj").attr(t.mix(a,{src:n.src,transform:"r0"})),e.set("isPaint",!0),e.set("imgWidth",i),e.set("imgHeight",s),e.set("imgNowWidth",a.width),e.set("imgNowHeight",a.height),e.set("angle",0),e.set("imgSrc",n.src),e.set("isFit",!0)}},renderUI:function(){var e=this,t=e.get("imgSrc"),r=e.get("el"),i=new n.Canvas({render:r}),s=i.addShape("image",{});e.set("canvas",i),e.set("imgObj",s)},bindUI:function(){this._bindDrag()},clear:function(){var e=this,t=e.get("canvas");t.destroy(),e.set("isPaint",!1)},reset:function(){this.set("angle",0),this.rotate(0,0),this.scale("fit",0)},leftHand:function(e,t){var n=this,r=n.get("rotateTime"),e=e||"<>";n.rotate(-90,r,e,t)},rightHand:function(e,t){var n=this,r=n.get("rotateTime"),e=e||"<>";n.rotate(90,r,e,t)},rotate:function(e,t,n,r){var i=this,s=i.get("isPaint"),o=i.get("angle"),u=i.get("imgObj"),n=n||"<>";s&&(o+=e,u.attr({x:(i.get("width")-i.get("imgNowWidth"))/2,y:(i.get("height")-i.get("imgNowHeight"))/2}).stopAnimate().animate({transform:"r"+o},t,n,r),i.set("angle",o)),i.set("isFit",!1)},zoom:function(e,t){var n=this,r=n.get("zoomRate"),i=n.get("scaleTime"),e=e||"<>";n.scale(r,i,e,t)},micrify:function(e,t){var n=this,r=n.get("micrifyRate"),i=n.get("scaleTime"),e=e||"<>";n.scale(r,i,e,t)},resume:function(e,t){var n=this,r=n.get("scaleTime"),e=e||"<>";n.scale("resume",r,e,t)},fit:function(e,t){var n=this,r=n.get("scaleTime"),e=e||"<>";n.scale("fit",r,e,t)},fitToggle:function(){var e=this,t=e.get("isFit");t?e.resume():e.fit()},scale:function(e,t,n,i){var s=this,o=s.get("imgObj"),u=s.get("width"),a=s.get("height"),f=s.get("imgWidth"),l=s.get("imgHeight"),c=s.get("imgNowWidth"),h=s.get("imgNowHeight"),p=s.get("isPaint"),d=s.get("angle");if(p){var v=!1;if(e=="resume")c=f,h=l;else if(e=="fit"){if(Math.abs(d%180)==90){var m=r(l,f,u,a);c=m.height,h=m.width}else{var m=r(f,l,u,a);c=m.width,h=m.height}v=!0}else c*=e,h*=e;o.stopAnimate().animate({x:(u-c)/2,y:(a-h)/2,width:c,height:h,transform:"r"+d},t,n,i),s.set("imgNowWidth",c),s.set("imgNowHeight",h),s.set("isFit",v)}},getSrc:function(){return this.get("imgSrc")},_getOverflowSize:function(e,t,n,r,i,s,o,u){var a=this;return e.x+r<u-e.width?r=u-e.width-e.x:e.x+r>s-u&&(r=s-u-e.x),e.y+i<u-e.height?i=u-e.height-e.y:e.y+i>o-u&&(i=o-u-e.y),{x:t+r,y:n+i}},_bindDrag:function(){var e=this,n=e.get("imgObj"),r,i,s,o,u,a,f,l;n.drag(function(s,o){if(e.get("drag")){var c=e._getOverflowSize(l,u,a,s,o,r,i,f);n.attr(t.mix(c,{transform:n.attr("transform")}))}},function(){l=n.getBBox(),u=n.attr("x"),a=n.attr("y"),r=e.get("width"),i=e.get("height"),s=e.get("imgNowWidth"),o=e.get("imgNowHeight"),f=Math.min(s,o,e.get("overflowSize"))})},dragToggle:function(){this.get("drag")?this.set("drag",!1):this.set("drag",!0)},_uiSetWidth:function(e){this.get("canvas").setSize(e,this.get("height")||800),this.scale("fit",0)},_uiSetHeight:function(e){this.get("canvas").setSize(this.get("width")||600,e),this.scale("fit",0)},_uiSetImgSrc:function(e){this.get("targetImg").src=e,this.get("afterChanged")(e)},_uiSetDrag:function(e){e?this.get("imgObj").attr("cursor","move"):this.get("imgObj").attr("cursor","default")},destructor:function(){this.clear()}},{ATTRS:{imgSrc:{},isPaint:{value:!1},angle:{value:0},rotateTime:{value:300},scaleTime:{value:300},zoomRate:{value:1.5},micrifyRate:{value:2/3},overflowSize:{value:100},elCls:{value:"ui-view-content"},drag:{value:!0},afterChanged:{value:function(e){}}}});return i});
