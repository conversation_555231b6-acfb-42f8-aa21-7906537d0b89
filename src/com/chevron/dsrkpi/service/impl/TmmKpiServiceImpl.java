package com.chevron.dsrkpi.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chevron.dsrkpi.dao.TmmCaiKpiMapper;
import com.chevron.dsrkpi.model.TmmInfo;
import com.chevron.dsrkpi.model.TmmInfoDecVo;
import com.chevron.dsrkpi.model.TmmInfoVo;
import com.chevron.dsrkpi.service.TmmKpiService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;

import cn.hutool.core.collection.CollectionUtil;

@Service
public class TmmKpiServiceImpl implements TmmKpiService{
	
	@Autowired
	private TmmCaiKpiMapper tmmCaiKpiMapper;
	
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;

	@Override
	public JsonResponse queryTmmByCondition(String year) {
	    year = "2020";
		JsonResponse resMap = new JsonResponse();
		try {
				WxTUser curUser = ContextUtil.getCurUser();
				//获取当前用户的查看权限
				int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "CAI_KPI_INFO");
				if(1 == (permissionWeight&1)) {
					permissionWeight = 1;
				}else if(4 == (permissionWeight&4)) {
					permissionWeight = 4;
				}else {
					resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
					resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
					return resMap;
				}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}

		Map<String, Object>  params  = new HashMap<String, Object>();
		params.put("fromDate", year+"-01-01");
		params.put("toDate",  (Integer.valueOf(year)+1)+"-01-01");
		List<TmmInfo> tmmInfoList = tmmCaiKpiMapper.queryTmmByCondition(params);
		Map<String, TmmInfoVo> tmmInfoVoMap = new HashMap<String, TmmInfoVo>();
		Map<String, TmmInfoDecVo> tmmInfoDecMap = new HashMap<String, TmmInfoDecVo>();
		for (TmmInfo tmmInfo : tmmInfoList) {
			TmmInfoVo tmmInfoVo = tmmInfoVoMap.get(tmmInfo.getDsrId().toString());
			if(null == tmmInfoVo) {
				tmmInfoVo = new TmmInfoVo();
				tmmInfoVo.setChName(tmmInfo.getChName());
				tmmInfoVo.setDsrId(tmmInfo.getDsrId());
				tmmInfoVo.setCustomerNameCn(tmmInfo.getCustomerNameCn());
				tmmInfoVo.setDistributorId(tmmInfo.getDistributorId());
				tmmInfoVo.setChRegionName(tmmInfo.getChRegionName());
				tmmInfoVo.setRegionName(tmmInfo.getRegionName());
				
				TmmInfoDecVo tmmInfoDecVo = tmmInfoDecMap.get(tmmInfo.getDsrId()+"/"+DateUtil.getMonth(DateUtil.parseDate(tmmInfo.getEffictiveDate(), DateUtil.DATA_FORMAT_PATTERN_MONTH)));
				if(null == tmmInfoDecVo) {
					tmmInfoDecVo = new TmmInfoDecVo();
					tmmInfoDecVo.setDsrId(tmmInfo.getDsrId());
				}
				tmmInfoDecVo.setMon(Integer.valueOf(DateUtil.getMonth(DateUtil.parseDate(tmmInfo.getEffictiveDate(), DateUtil.DATA_FORMAT_PATTERN_MONTH))));
				if(TmmInfo.TMM_VISIT.equals(tmmInfo.getKpiCode())) {
					tmmInfoVo.setYtdTmmFactVisit(tmmInfo.getKpiValue());
					tmmInfoVo.setYtdTmmAimsVisist(tmmInfo.getTmmAims());
					tmmInfoDecVo.setTmmFactVisit(tmmInfo.getKpiValue());
					tmmInfoDecVo.setTmmAimsVisist(tmmInfo.getTmmAims());
				}
				if(TmmInfo.TMM_ENTRY.equals(tmmInfo.getKpiCode())) {
					tmmInfoVo.setYtdTmmFactEntry(tmmInfo.getKpiValue());
					tmmInfoDecVo.setTmmFactEntry(tmmInfo.getKpiValue());
				}
				if(TmmInfo.TMM_POTENTIAL_ENTRY.equals(tmmInfo.getKpiCode())) {
					tmmInfoVo.setYtdTmmFactPotentialEntry(tmmInfo.getKpiValue());
					tmmInfoDecVo.setTmmFactPotentialEntry(tmmInfo.getKpiValue());
				}
				if(null != tmmInfo.getTmmAims()) {
					tmmInfoDecMap.put(tmmInfo.getDsrId()+"/"+DateUtil.getMonth(DateUtil.parseDate(tmmInfo.getEffictiveDate(), DateUtil.DATA_FORMAT_PATTERN_MONTH)),tmmInfoDecVo);
				}
				tmmInfoVoMap.put(tmmInfo.getDsrId().toString(), tmmInfoVo);
				continue;
			}
			
			
			TmmInfoDecVo tmmInfoDecVo = tmmInfoDecMap.get(tmmInfo.getDsrId()+"/"+DateUtil.getMonth(DateUtil.parseDate(tmmInfo.getEffictiveDate(), DateUtil.DATA_FORMAT_PATTERN_MONTH)));
			if(null == tmmInfoDecVo) {
				tmmInfoDecVo = new TmmInfoDecVo();
				tmmInfoDecVo.setDsrId(tmmInfo.getDsrId());
			}
			
			tmmInfoDecVo.setMon(Integer.valueOf(DateUtil.getMonth(DateUtil.parseDate(tmmInfo.getEffictiveDate(), DateUtil.DATA_FORMAT_PATTERN_MONTH))));
			if(TmmInfo.TMM_VISIT.equals(tmmInfo.getKpiCode())) {
				Double ytdFactVisit =  tmmInfoVo.getYtdTmmFactVisit() == null ? 0 : tmmInfoVo.getYtdTmmFactVisit();
				Double ytdaimsVisit =  tmmInfoVo.getYtdTmmAimsVisist() == null ? 0 : tmmInfoVo.getYtdTmmAimsVisist();
				tmmInfoVo.setYtdTmmFactVisit(ytdFactVisit + (tmmInfo.getKpiValue() == null ? 0 : tmmInfo.getKpiValue()));
				tmmInfoVo.setYtdTmmAimsVisist(ytdaimsVisit + (tmmInfo.getTmmAims()  == null ? 0 : tmmInfo.getTmmAims()));
				tmmInfoDecVo.setTmmFactVisit(tmmInfo.getKpiValue());
				tmmInfoDecVo.setTmmAimsVisist(tmmInfo.getTmmAims());
			}
			if(TmmInfo.TMM_ENTRY.equals(tmmInfo.getKpiCode())) {
				Double ytdFactEntry = tmmInfoVo.getYtdTmmFactEntry() == null ? 0 : tmmInfoVo.getYtdTmmFactEntry();
				tmmInfoVo.setYtdTmmFactEntry(ytdFactEntry + (tmmInfo.getKpiValue() == null ? 0 : tmmInfo.getKpiValue()));
				tmmInfoDecVo.setTmmFactEntry(tmmInfo.getKpiValue());
			}
			if(TmmInfo.TMM_POTENTIAL_ENTRY.equals(tmmInfo.getKpiCode())) {
				Double ytdFactPotentialEntry  = tmmInfoVo.getYtdTmmFactPotentialEntry() == null ? 0 : tmmInfoVo.getYtdTmmFactPotentialEntry();
				tmmInfoVo.setYtdTmmFactPotentialEntry(ytdFactPotentialEntry + (tmmInfo.getKpiValue() == null ? 0 : tmmInfo.getKpiValue()));
				tmmInfoDecVo.setTmmFactPotentialEntry(tmmInfo.getKpiValue());
			}
			if(null != tmmInfo.getTmmAims()) {
				tmmInfoDecMap.put(tmmInfo.getDsrId()+"/"+DateUtil.getMonth(DateUtil.parseDate(tmmInfo.getEffictiveDate(), DateUtil.DATA_FORMAT_PATTERN_MONTH)),tmmInfoDecVo);
			}
			tmmInfoVoMap.put(tmmInfo.getDsrId().toString(), tmmInfoVo);
		}
		
		Map<Long, List<TmmInfoDecVo>> decMap = new HashMap<Long, List<TmmInfoDecVo>>(); 
		Collection<TmmInfoDecVo> TmmInfoDecVos = tmmInfoDecMap.values();
		
		for (TmmInfoDecVo tmmInfoDecVo : TmmInfoDecVos) {
			List<TmmInfoDecVo> declist = decMap.get(tmmInfoDecVo.getDsrId());
			if(CollectionUtil.isEmpty(declist)) {
				declist = new ArrayList<TmmInfoDecVo>();
			}
			declist.add(tmmInfoDecVo);
			decMap.put(tmmInfoDecVo.getDsrId(), declist);
		}
		
		Collection<TmmInfoVo> tmmInfoVos = tmmInfoVoMap.values();
		List<TmmInfoVo> resList = new ArrayList<TmmInfoVo>();
		for (TmmInfoVo tmmInfoVo2 : tmmInfoVos) {
			List<TmmInfoDecVo> decList = decMap.get(tmmInfoVo2.getDsrId());
			if(!CollectionUtil.isEmpty(decList)) {
				tmmInfoVo2.setTmmInfoDecList(decList);
			}
			resList.add(tmmInfoVo2);
		}
		resMap.setListResult(resList);
		return resMap;
	}
}
