<%@page import="com.common.util.StringUtils"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import= "com.common.util.ContextUtil"%>
<%@page import= "com.sys.auth.model.WxTUser"%>
<%@page import="com.sys.auth.model.WxTRole"%>
<%@page import="java.util.List"%>
<%@page import="java.util.Calendar"%>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.lang.Boolean" %>
<%@page import="com.sys.auth.controller.ConfigIndexPage"%>
<%
	int year = 0;
	int currentYear = 0;
	int currentMonth = 12;
	if(StringUtils.isNotBlank(request.getParameter("year"))){
		year = Integer.parseInt(request.getParameter("year"));
		currentYear = year + 1;
	}else{
		Calendar cal = Calendar.getInstance();
		currentYear = year = cal.get(Calendar.YEAR);
		currentMonth = cal.get(Calendar.MONTH) + 1;
	}
	int lastYear = year - 1;
    WxTUser curUser = ContextUtil.getCurUser();
    Long userId = curUser.getUserId();
    int isAsmAndAbove = 0;
    boolean isAsm = false;
    boolean isChannelManager = false;
    int roleWeight = 1;
    String roleType = "";
    String distributorId = null;
    String customerCategory = "Consumer";
    if (WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel())) {
        // 如果是chevron的user model

        List<WxTRole> roleList = curUser.getRoleList();
         for(WxTRole role : roleList) {
             boolean bChannelManager = role.getChRoleName().equals("Chevron_CDM_Channel_Manager");
             boolean bSuppervisorManager = role.getChRoleName().equals("Chevron_CDM_Suppervisor");
            if(bSuppervisorManager || bChannelManager) {
                if(bChannelManager) {
                    isChannelManager = true;
                    roleWeight = roleWeight | 4;
                }else if(bSuppervisorManager) {
                    isAsm = true;
                    roleWeight = roleWeight | 2;
                }
                isAsmAndAbove = 1;
            }
            if(role.getChRoleName().equals("Chevron_CDM_Suppervisor") || role.getChRoleName().equals("Chevron_C&I_Suppervisor")){
            	roleType = "asm";
            }else if(role.getChRoleName().equals("Chevron_BD") || role.getChRoleName().equals("Chevron_Promote_Sales")){
            	roleType = "flsr";
            }else {
            	roleType = "admin";
            }
        }
    }
    String higherReceivedDivClass = "higherReceivedDiv";
//    if(isAsmAndAbove == 1){
//        higherReceivedDivClass = "higherReceivedDiv";
//    }
//
//    WxTUser curUser = ContextUtil.getCurUser();
//    String userName = curUser.getChName();
//    Long userId = curUser.getUserId();
//    Long orgId = curUser.getOrgId();

//    List<WxTRole> roleList = curUser.getRoleList();
//
//    boolean isDistributor = false;
//    String userModel = curUser.getUserModel();
//    if(WxTUser.USER_MODEL_SP.equals(userModel)) {
//        isDistributor = true;
//    }
//
//    boolean isCdmDistributor = false;
//    for(WxTRole role : roleList) {
//        if(role.getChRoleName().equals("Service_Partner_Admin")
//                || role.getChRoleName().equals("Service_Partner_Manager")) {
//            isCdmDistributor = true;
//            break;
//        }
//    }
//
//    boolean isCioDistributor = false;
//    for(WxTRole role : roleList) {
//        if(role.getChRoleName().equals("Caltex_Dealer")) {
//            isCioDistributor = true;
//            break;
//        }
//    }
    boolean isMenuPermitted = false;
    isMenuPermitted = ConfigIndexPage.isMenuPermitted("/dms/CustomersaleReport.jsp", userId);
%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>我的金富力</title>
    <%@include file="/common/jsp/common.jsp" %>
    <link href="${ctx }newhomepage/css/myCdm.css?v=${version}10" rel="stylesheet">
    <link href="${ctx}newhomepage/tooltipster/css/tooltipster.bundle.css" rel="stylesheet">
    <script type="text/javascript" src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
    <script type="text/javascript">
        var currentYear = <%=year%>;
        var lastYear = <%=lastYear%>;
        var roleType = '<%=roleType%>';
        var customerCategory = '<%=customerCategory%>';
        var distributorId = <%=distributorId%>;
        var currentMonth = <%=currentMonth%>;
        var G_isAsmAndAbove = <%=isAsmAndAbove%>;
        var isMenuPermitted = <%=isMenuPermitted%>;
        var roleWeight = <%=roleWeight %>;
        var loginCai = '<%=curUser.getCai() %>';
    </script>
</head>
<c:set var="isAsmAndAbove" value="<%=isAsmAndAbove%>"/>
<c:set var="isChannelManager" value="<%=isChannelManager%>"/>
<c:set var="isAsm" value="<%=isAsm%>"/>
<c:set var="higherReceivedDivClass" value="<%=higherReceivedDivClass%>"/>
<body class="gray-bg">
<div class="not-top">
    <div class="row">
        <div class="block-col">
            <div class="content-box height-content">
                <div class="content-panel block-header">
                    <i class="fa fa-group"></i>
                    <c:if test="${isAsmAndAbove eq 0}" ><span>本区域内排名</span></c:if>
                    <c:if test="${isAsmAndAbove eq 1}" ><span>排名</span></c:if>
                </div>
                <div class="content-panel" id="rank_info">
                    <div id="cur_year" class="block-col rank_info-sub-div" style="height: 190px;">
                            <p class="year-title"><%=year%>年</p>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="cur-month-sales-rank">销量YTD 本月排名:<br/><span class="rank-value"></span></p>
                            <p class="pre-month-sales-rank">上月排名:<br/><span class="rank-value"></span></p>
                        </div>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="cur-month-gross-rank">毛利YTD 本月排名:<br/><span class="rank-value"></span></p>
                            <p class="pre-month-gross-rank">上月排名:<br/><span class="rank-value"></span></p>
                        </div>
                    </div>
                    <%--<div id="pre_year" class="block-col rank_info-sub-div" style="<%=((lastYear == 2018)?"display:none":"")%>">--%>
                    <div id="pre_year" class="block-col rank_info-sub-div" style="display:none">
                    <p class="year-title"><%=lastYear%>年</p>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="cur-month-sales-rank">销量YTD 本月排名:<span class="rank-value"></span></p>
                            <p class="cur-month-gross-rank">毛利YTD 本月排名: <span class="rank-value"></span></p>
                        </div>
                        <div class="sub-year-div" style="width: 50%;float: left;">
                            <p class="pre-month-sales-rank">上月排名:<span class="rank-value"></span></p>
                            <p class="pre-month-gross-rank">上月排名:<span class="rank-value"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box height-content">
                <div class="content-panel block-header">
                    <i class="fa fa-bar-chart"></i><span>Sales Target</span>
                    <%--<a class="more-btn" href="javascript: void(0);"--%>
                       <%--onclick="newhomepage_init.openUrl('${ctx }sys/push/personalMessagePage.jsp', 'mymessage', '我的消息')">更多...</a>--%>
                </div>
                <div class="content-panel" id="sales_target">
                    <div id="sell-in-info" class="block-col sales_target-pie-div" style="padding-right: 0;padding-left: 0;">
                    </div>
                    <div id="gross-info" class="block-col sales_target-pie-div"  style="padding-right: 0;padding-left: 0;">
                    </div>
                </div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box height-content" >
                <div class="content-panel block-header">
                    <i class="fa fa-commenting"></i><span>待办事项及快捷入口</span>
                    <%--<a class="more-btn" href="javascript: void(0);"--%>
                       <%--onclick="newhomepage_init.openUrl('${ctx }sys/push/personalMessagePage.jsp', 'mymessage', '我的消息')">更多...</a>--%>
                </div>
                <div class="content-panel" id="todo-div">
                    <div>
                         <ul class="todo-list" style="width: calc(50% - 35px); margin-right: 0;float: left;">
                             <%--cdm_mkt/jsp/mktAuditListPage.jsp?mid=50228 ?mid=60234--%>
                             <%--<li>门头申请待审批&nbsp;<span class="num-span" onclick="myCdmFunc.openMenu('${ctx}cdm_mkt/jsp/mktAuditListPage.jsp')">{{todoInfo.storeFrontTodoNum}}</span>&nbsp;条</li>--%>
                             <%--<li>研讨会申请待审批&nbsp;<span class="num-span" onclick="myCdmFunc.openMenu('${ctx}cdm_mkt/jsp/mktAuditListPage.jsp')">{{todoInfo.seminarTodoNum}}</span>&nbsp;条</li>--%>
                             <c:if test="${isAsmAndAbove eq 0}">
                             <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cdm/list')">乘用车店招研讨会申请</li>
                             <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyPlanTodoInfo}}</span>&nbsp;份销售月计划待提交</li>
                             <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}evaluationprogram/index.do')">{{todoInfo.monthlyReportInfo}}</span>&nbsp;份综合月报告待提交</li>
                                  <li><span class="num-span"
                                            onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.flsrExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核
                                  </li>
                             </c:if>
                             <c:if test="${isAsm}">
                    	         <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cdm/list')">乘用车店招研讨会审核</li>
                                 <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateListPage.jsp')">{{todoInfo.asmExpensePendingReviewCount}}</span>&nbsp;条精英计划基金花费申请待审核</li>
                                 <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.asmExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>
                             </c:if>
                              <c:if test="${isChannelManager}">
                              	<li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}SPA/resource-application/index.jsp#/cdm/list')">乘用车店招研讨会审核</li>
                                <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateAuditListPage.jsp')">{{todoInfo.cmExchangePendingReviewCount}}</span>&nbsp;条精英计划兑换待审核</li>
                              </c:if>
                              <li><span class="num-span" onclick="myCdmFunc.openMenu('${ctx}credit_app/index.jsp')">{{todoInfo.creditPendingCount}}</span>&nbsp;条&nbsp;信用申请待审批</li>
                         </ul>
                        <ul class="todo-list" style="width: calc(50% - 35px);margin-right: 0;float: left;">
                            <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}QBR/index.jsp#/qbr/review')">QBR</li>
                            <c:if test="${isAsmAndAbove eq 0}">
                            <%--<li style="cursor: pointer" onclick="myCdmFunc.openUrl('${ctx}cdm_mkt/jsp/mktApplyListPage.jsp?type=1', 'mktAuditListPage', '金富力资源申请')">
                                门头研讨会申请&nbsp;
                            </li>--%>
                            </c:if>
                            <c:if test="${isAsmAndAbove eq 1}">
                                <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}evaluationprogram/index.do')">销售绩效考核审核</li>
                            </c:if>
                            <li style="cursor: pointer;" class="dmsURL" onclick="myCdmFunc.openMenu('/dms/CustomersaleReport.jsp')">客户及销售业绩统计分析</li>
                            <c:if test="${isAsmAndAbove eq 0}">
                                <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}elites2/rebate/jsp/rebateListPage.jsp')">
                                    2019精英计划基金花费申请
                                </li>
                            </c:if>
                            <li style="cursor: pointer" onclick="myCdmFunc.openMenu('${ctx}credit_app/index.jsp')">
                                   	信用申请
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="not-top">
    <div class="row">
        <div class="block-col">
            <div class="content-box  ${higherReceivedDivClass}">
                <div class="content-panel block-header">
                    <i class="fa fa-star"></i>
                    <span><%=currentYear%>年所负责合伙人累计获取支持</span>
                </div>
                <div class="supports-received">
                    <div class="row">
                        <!--/cdm_rebate/jsp/fund/fundApplyListPage.jsp-->
                        <div id="rebate-div" class="block-col four-div" style="margin-right: 6px;height: 100px;">
                            <p><span class="text-span">基金金额</span></p>
                            <%--<p><span class="value-span" onclick="myCdmFunc.openUrl('${ctx}cdm_rebate/jsp/fund/fundApplyListPage.jsp', 'fundApplyListPage', '返利基金申请')"></span>--%>
                            <p><span class="value-span" onclick="return false;"></span>
                                <span>元</span></p>
                        </div>
                        <div id="cdm-stock-point-div" class="block-col four-div" style="margin-left: 6px;margin-right: 6px;height: 100px;">
                            <p><span class="text-span">进货积分</span></p>
                            <p><span class="value-span" onclick="myCdmFunc.openUrl('${ctx }material/jsp/cdmDistributorManagePage.jsp', 'dealerManagePage', '积分查看')">0</span>
                                <span>元</span>
                                <%--<c:if test="${isAsmAndAbove eq 1}">--%>
                                <%--<span>/</span><span class="value-span" onclick="myCdmFunc.openUrl('${ctx }material/jsp/cdmDistributorManagePage.jsp', 'dealerManagePage', '积分查看')">0</span>--%>
                                <%--<span>元</span>--%>
                                <%--</c:if>--%>
                            </p>
                        </div>
                        <div id="cdm-material-point-div" class="block-col four-div" style="margin-left: 6px;margin-right: 6px;height: 100px;">
                            <p><span class="text-span">物料积分</span></p>
                            <p><span class="value-span" onclick="myCdmFunc.openUrl('${ctx }material/jsp/cdmDistributorManagePage.jsp', 'dealerManagePage', '积分查看')">0</span>
                                <span>元</span>
                                <%--<c:if test="${isAsmAndAbove eq 1}">--%>
                                    <%--<span>/</span><span class="value-span" onclick="myCdmFunc.openUrl('${ctx }material/jsp/cdmDistributorManagePage.jsp', 'dealerManagePage', '积分查看')">0</span>--%>
                                    <%--<span>元</span>--%>
                                <%--</c:if>--%>
                            </p>
                        </div>
                        <div id="cdm-promotion-div" class="block-col four-div" style="margin-left: 6px; margin-right: 6px;height: 100px;">
                            <p><span class="text-span">促销支持</span></p>
                            <p><span class="value-span">0</span>
                                <span>元</span>
                                <%--<c:if test="${isAsmAndAbove eq 1}">--%>
                                <%--<span>/</span><span class="value-span" onclick="myCdmFunc.openUrl('${ctx }material/jsp/cdmDistributorManagePage.jsp', 'dealerManagePage', '积分查看')">0</span>--%>
                                <%--<span>元</span>--%>
                                <%--</c:if>--%>
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div id="new-workshop-div" class="block-col three-div" style="margin-right: 6px;">
                            <p><span class="text-span">店招</span></p>

                            <p><span class="value-span" onclick="myCdmFunc.openUrl('${ctx}cdm_mkt/jsp/mktAuditListPage.jsp', 'mktAuditListPage', '金富力资源申请')">0</span>
                            <%--<p><span class="value-span" onclick="openSalesPerfAnalysisPage()">0</span>--%>
                                <span >家</span>
                                <%--<c:if test="${isAsmAndAbove eq 1}">--%>
                                    <span>/</span><span class="value-span" onclick="myCdmFunc.openUrl('${ctx}cdm_mkt/jsp/mktAuditListPage.jsp', 'mktAuditListPage', '金富力资源申请')">0</span>
                                    <span>元</span>
                                <%--</c:if>--%>
                            </p>
                        </div>
                        <div id="meeting-subsidy-div" class="block-col three-div" style="margin-left: 6px;margin-right: 6px;">
                            <p><span class="text-span">会议补贴</span></p>
                            <%--<p><span class="value-span" onclick="myCdmFunc.openUrl('${ctx}cdm_rebate/jsp/rebate/rebateAuditListPage.jsp', 'rebateAuditListPage', '返利兑换审核')">0</span>--%>
                            <p>
                            <span class="value-span">0</span>
                            <span>场</span>
                            <%--<c:if test="${isAsmAndAbove eq 1}">--%>
                                <span>/</span><span class="value-span" onclick="myCdmFunc.openUrl('${ctx}cdm_mkt/jsp/mktAuditListPage.jsp', 'mktAuditListPage', '金富力资源申请')">0</span>
                                    <span>元</span>
                            <%--</c:if>--%>
                            </p>
                        </div>
                        <div id="training-subsidy-div" class="block-col three-div" style="margin-left: 6px;">
                            <p><span class="text-span">培训</span></p>
                            <%--<p><span class="value-span" onclick="myCdmFunc.openUrl('${ctx}cdm_rebate/jsp/rebate/rebateAuditListPage.jsp', 'rebateAuditListPage', '返利兑换审核')">0</span>--%>
                            <p>
                                <span class="value-span">0</span>
                                <span>天</span>
                                <%--<c:if test="${isAsmAndAbove eq 1}">--%>
                                    <span>/</span>
                                    <span class="value-span">0</span>
                                    <span>元</span>
                                <%--</c:if>--%>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box ${higherReceivedDivClass}">
                <div class="content-panel block-header">
                    <i class="fa fa-commenting"></i><span>系统公告</span>
                    <a class="more-btn" href="javascript: void(0);"
                       onclick="myCdmFunc.openUrl('${ctx }sys/push/personalMessagePage.jsp', 'mymessage', '我的消息')">更多...</a>
                </div>
                <div class="content-panel" id="sys_message"></div>
            </div>
        </div>
        <div class="block-col">
            <div class="content-box  ${higherReceivedDivClass}">
                <div class="content-panel block-header">
                    <i class="fa fa-question-circle"></i><span>系统支持</span>
                </div>
                <div class="content-panel support-item"><i class="fa fa-file-o"></i>
                    <a href="javascript: void(0);"
                       onclick="myCdmFunc.openUrl('${ctx }operationManual.jsp', 'documentDownload', '文档下载');"
                       style="font-weight: 700;">
                        <spring:message code="Click"/></a> <spring:message code="Open_the_document_support_page"/></div>
                <div class="content-panel support-item">
                    <i class="fa fa-phone"></i>技术服务热线（系统报错及操作）：
                    <a href="tel:************">************</a>
                </div>
                <div class="content-panel support-item">
                    <i class="fa fa-phone"></i>积分服务热线（礼品及订单咨询）：
                    <a href="tel:************">************</a>
                </div>
            </div>
        </div>
    </div>
</div>
<%--<div class="row not-top">--%>
    <%--<div id="perf_grid" class="content-panel" style="width: 100%; margin-left: 15px;margin-right: 15px;">--%>

    <%--</div>--%>
<%--</div>--%>
    <%--<div class="row">--%>
    <div class="content-panel" style="padding-left: 15px;padding-right: 15px;background: transparent;">
        <div id="perf_grid"  style="width: calc(100%);margin-bottom: 15px;">

        </div>
    </div>
    <%--</div>--%>
		<div class="subject-indexes-wrapper1">
			<ul>
				<li onclick="myCdmFunc.openMenu('/myCdm/index.do');" class='<%=currentYear == year ? "active" : "" %>'>
					<%=currentYear %> 我的金富力
				</li>
				<li onclick="myCdmFunc.openMenu('/myCdm/index.do', '/myCdm/index.do?year=<%=currentYear - 1 %>', '');" class='<%=currentYear - 1 == year ? "active" : "" %>'>
					<%=currentYear - 1 %> 我的金富力
				</li>
			</ul>
		</div>
</body>
<script type="text/javascript" src="${ctx}newhomepage/tooltipster/js/tooltipster.bundle.min.js"></script>
<script type="text/javascript" src="/common/js/vue.js"></script>
<script type="text/javascript" src="${ctx}newhomepage/js/myCdm.js?v=${version}10"></script>
</html>