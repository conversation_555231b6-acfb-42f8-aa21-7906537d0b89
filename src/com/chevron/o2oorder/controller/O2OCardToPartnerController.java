package com.chevron.o2oorder.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.o2oorder.model.O2OCardToPartnerParmas;
import com.chevron.o2oorder.service.O2OCardToPartnerService;
import com.common.base.BaseController;
import com.common.constants.MessageContants;
import com.common.util.MessageResourceUtil;


/**
 * 
 * @Author: bo.liu  2017-3-10 上午9:49:12 
 * @Version: $Id$
 * @Desc: <p>发放卡控制类</p>
 */
@Service
@RequestMapping(value="/o2ocardToPartnerController")
public class O2OCardToPartnerController extends BaseController {
	public static final Logger log = LoggerFactory.getLogger(O2OCardToPartnerController.class);
	@Resource
	O2OCardToPartnerService o2oCardToPartnerService;
	
	@ResponseBody
	@RequestMapping(value="/queryO2OCardToPartnerLst.do", method = {RequestMethod.POST})
    public Map<String,Object> queryO2OCardToPartner(O2OCardToPartnerParmas o2oCardToPartnerParams)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			
			resultMap = o2oCardToPartnerService.getO2OCardToPartnerLst(o2oCardToPartnerParams);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, MessageContants.EXCEPTION_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	
	

}
