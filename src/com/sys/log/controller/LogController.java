package com.sys.log.controller;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.log.business.LogBizService;
import com.sys.log.model.LogParams;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 日志Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2017-05-25 15:23
 */
@Controller
@RequestMapping(value="/log")
public class LogController {
	@Autowired
	private LogBizService logBizService;
	
	private final static Logger log = Logger.getLogger(LogController.class);
	
	/**
	 * 门店激励政策列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(LogParams params){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			logBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}
	
}
