package com.sys.utils.business;

import com.common.exception.WxPltException;
import com.sys.utils.model.WarnConfig;
import com.sys.utils.model.WarnConfigExample;
import com.sys.utils.model.WarnConfigParams;

import java.util.List;
import java.util.Map;

/**
 * 告警配置业务接口
 * <AUTHOR>
 * @version 1.0 2020-07-09 18:10
 */
public interface WarnConfigBizService {
	
	/**
	 * 保存告警配置
	 * @param record 被插入告警配置
	 * @throws WxPltException
	 */
	public void insert(WarnConfig record) throws WxPltException;
	
	/**
	 * 修改告警配置
	 * @param record 被修改告警配置
	 * @throws WxPltException
	 */
	public void update(WarnConfig record) throws WxPltException;
	
	/**
	 * 删除告警配置
	 * @param ids 被删除告警配置id集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的告警配置
	 * @param example 被删除告警配置满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(WarnConfigExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<WarnConfig> queryByExample(WarnConfigExample example) throws WxPltException;
	
	/**
	 * 列表查询告警配置对象
	 * @param params 查询条件
	 * @return 满足条件告警配置对象集合
	 */
    public List<WarnConfig> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 修改页面修改告警配置
	 * @param record 被修改告警配置
	 * @throws WxPltException
	 */
	public void updateForEditPage(WarnConfig record) throws WxPltException;
	
	/**
	 * 获取指定主键的告警配置对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public WarnConfig getBean(Long id) throws WxPltException;	

	/**
	 * 分页查询告警配置
	 * @param params 查询参数
	 * @param resultMap 结果对象
	 * @throws WxPltException
	 */
	public void queryForPage(WarnConfigParams params, Map<String, Object> resultMap) throws WxPltException;
	
	/**
	 * 分页查询告警配置
	 * @param params 查询参数
	 * @return 查询结果
	 * @throws WxPltException
	 */
	public List<WarnConfig> queryForPage(WarnConfigParams params) throws WxPltException;

    /**
     * 执行告警任务
     */
    public void excuteWarnningTask(Long id) throws WxPltException;
}
