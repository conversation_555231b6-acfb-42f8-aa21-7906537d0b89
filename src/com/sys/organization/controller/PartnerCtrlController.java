package com.sys.organization.controller;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import com.sys.organization.model.PartnerInfoVo;
import com.sys.organization.model.PartnerQueryParams;
import com.sys.organization.service.PartnerCtrlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @author: chenBin
 * @date: 06/07/2020
 */
@RequestMapping("/dealerCtrl")
@Controller
public class PartnerCtrlController {

    @Autowired
    private PartnerCtrlService partnerCtrlService;

    @RequestMapping("/findIndirectDealer.do")
    @ResponseBody
    public JsonResponse queryIndirect(@RequestBody PartnerQueryParams params) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<PartnerInfoVo> partnerInfoVos = partnerCtrlService.queryIndirectPartnerInfo(params);
            jsonResponse.setListResult(partnerInfoVos);
            jsonResponse.setTotalOfPaging(params.getTotalCount());
        } catch (WxPltException e) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getExpMsg());
        } catch (Exception e) {
            jsonResponse.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            jsonResponse.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
            e.printStackTrace();
        }
        return jsonResponse;
    }


    /**
     * 获取子经销商
     */
    @RequestMapping("/getChildPartners.do")
    @ResponseBody
    public JsonResponse getChildPartners(@RequestBody PartnerQueryParams params) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            params.setPaging(false);
            params.clearOrderField();
            List<PartnerInfoVo> partnerInfoVos = partnerCtrlService.getChildPartners(params);
            jsonResponse.setListResult(partnerInfoVos);
            jsonResponse.setTotalOfPaging(params.getTotalCount());
        } catch (WxPltException e) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg(e.getExpMsg());
        } catch (Exception e) {
            jsonResponse.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            jsonResponse.put(Constants.RESULT_CODE_MSG_KEY, "系统错误，查询失败");
            e.printStackTrace();
        }
        return jsonResponse;
    }




}
