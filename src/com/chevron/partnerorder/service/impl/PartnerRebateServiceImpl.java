package com.chevron.partnerorder.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import com.chevron.partnerorder.model.*;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.util.*;
import com.sys.email.service.EmailSenderService;
import com.sys.log.model.Log;
import com.sys.log.service.LogService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chevron.partnerorder.dao.DwCdmRebateSellInThroughMapper;
import com.chevron.partnerorder.dao.WXTPartnerRebateInfoApproveHistoryMapper;
import com.chevron.partnerorder.dao.WXTPartnerRebateInfoMapper;
import com.chevron.partnerorder.service.PartnerRebateService;
import com.chevron.pms.service.PartnerService;
import com.chevron.task.business.AppTaskBizService;
import com.chevron.task.model.WxTaskMain;
import com.chevron.task.model.WxTaskMainNew;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;

import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

@Service
public class PartnerRebateServiceImpl extends BasePartnerReabteInfo implements PartnerRebateService {
	private static Logger log = LoggerFactory.getLogger(PartnerRebateServiceImpl.class);
	public static final String MESAGE_TITLE= "合伙人扫码未达标提醒";
	public static final String MESAGE_CONTENT = "尊敬的合伙人%s，截止目前扫码率不超过50%，请知悉，谢谢！";
	public static final String SAVE_LOG_CONTENT = "mobile:%s partnername:%s 发送短信提醒";
	public static final String SAVE_EMAIL_LOG_CONTENT = "email:%s partnername:%s 发送邮件提醒";
	@Resource
	WXTPartnerRebateInfoMapper partnerRebateInfoMapper;
	
	@Resource
	WXTPartnerRebateInfoApproveHistoryMapper partnerRebateInfoApproveHistoryMapper;
	@Resource
	DwCdmRebateSellInThroughMapper dwCdmRebateSellInThroughMapper;
	@Resource
	PartnerService partnerService;
	
//	@Resource 
//	TaskService taskService;
	
	@Resource
	DicService dicService;

	@Resource
	LogService logService;
	
	@Autowired
	WxRoleServiceImpl wxRoleService;

	@Autowired
	WxTUserMapper userMapper;
	
	@Resource
	EmailSenderService emailSenderService;
	
	@Autowired
	private AppTaskBizService appTaskBizService;
	
	@Override
	public Map<String, Object> getPartnerRebateTaskDataInfo(
			PartnerRebateTaskConditions parmas)throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<WxTaskMainNew> lst = new ArrayList<WxTaskMainNew>();
		Long totalRecord = 0L;
		String year = parmas.getYear();
		String month = parmas.getMonth();
		Date taskFinishTimeS = null;
		Date taskFinishTimeE = null;
		
		if(month.equals("12"))
		{
			taskFinishTimeS = DateUtils.parseDate(year+"-"+month+"-01", DateUtils.DEFAULT_DATE_PATTERN);
			taskFinishTimeE = DateUtils.parseDate((Integer.parseInt(year)+1)+"-01"+"-01", DateUtils.DEFAULT_DATE_PATTERN);
		}else
		{
			taskFinishTimeS = DateUtils.parseDate(year+"-"+CommonUtil.addZeroForStr(month, 2, 1)+"-01", DateUtils.DEFAULT_DATE_PATTERN);
			taskFinishTimeE = DateUtils.parseDate(year+"-"+CommonUtil.addZeroForStr(""+(Integer.parseInt(month)+1),2,1)+"-01", DateUtils.DEFAULT_DATE_PATTERN);
		}
		parmas.setTaskFinishTimeS(taskFinishTimeS);
		parmas.setTaskFinishTimeE(taskFinishTimeE);
		lst = partnerRebateInfoMapper.getTaskByType(parmas);
		if(null==lst || lst.isEmpty())
		{
			resultMap.put(PartnerRebateTaskConditions.RESULTLST, new ArrayList<WxTaskMainNew>());
		}else
		{
			appTaskBizService.getTaskAttFile(WxTaskMain.TASK_TYPE_SD, lst);
			resultMap.put(PartnerRebateTaskConditions.RESULTLST, lst);
		}
		totalRecord = parmas.getTotalCount();
		resultMap.put(PartnerRebateTaskConditions.TOTALRECORD, totalRecord);
		return resultMap;
	}
	
	
	@Override
	public WXTPartnerRebateInfo statisticsPartnerRebateCommonData(
			Long partnerId, String year, String month)throws Exception {
		log.info("statisticsPartnerRebateCommonData partnerId:{},year:{},month:{}",partnerId,year,month);
		WXTPartnerRebateInfo partnerRebateInfo = null;
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("partnerId", partnerId);
		reqMap.put("year", year);
		reqMap.put("month", month);
		// 1.先从合伙人返利详情记录表中获取，看是否有对应的记录，有就直接返回
		partnerRebateInfo = partnerRebateInfoMapper.getPartnerRebateInfoByMap(reqMap);
		if(null!=partnerRebateInfo)
		{
			partnerRebateInfo.setEditFlag(EDIT_DISABLED);
			//获取当前所有已返利金额总和
			Map<String,Object> reqforTotalMap = new HashMap<String,Object>();
			reqforTotalMap.put("partnerId", partnerId);
			reqforTotalMap.put("yearStr", year);
			reqforTotalMap.put("monthStr", month);
			List<ResponseTotalRebateAmount> lst = partnerRebateInfoMapper.queryTotalRebateAmountByYearMonth(reqforTotalMap);
			if(lst != null && lst.size() > 0){
				partnerRebateInfo.setTotalRebateAmount(lst.get(0).getTotalAmount());
			}
			return partnerRebateInfo;
		}
		
		partnerRebateInfo = new WXTPartnerRebateInfo();
		partnerRebateInfo.setPartnerId(partnerId);
		partnerRebateInfo.setYearStr(year);
		partnerRebateInfo.setMonthStr(month);
		// 从数据字典中获取达标界值
		Map<String,Object> dataMap = doGetReachSettingData();
		dataMap.putAll(reqMap);
		
		// 2.若没有，就统计门店录入的信息
		partnerRebateInfo = doGetTaskPartInfo(partnerRebateInfo,dataMap);
		
		
		// 3.若没有，统计出库扫码考核信息
		partnerRebateInfo = doGetScanSalesPartInfo(partnerRebateInfo,dataMap);
		
		
		/*//TODO 4.若没有，统计窜货信息  不需要
		partnerRebateInfo = doGetFleeingGoodsPartInfo(partnerRebateInfo,dataMap);*/
		
		// 5.页面头部信息统计   合伙人返利  不需要设置
		// partnerRebateInfo = doGetPartnerRebatePartInfo(partnerRebateInfo,dataMap);
		
		return partnerRebateInfo;
	}


	private WXTPartnerRebateInfo doGetPartnerRebatePartInfo(
			WXTPartnerRebateInfo partnerRebateInfo, Map<String, Object> dataMap)throws Exception  {
		//test start
		/*partnerRebateInfo.setCurrentMonthRebateAmount(1200.0);//剩余的返利金额
		partnerRebateInfo.setActualRebateAmount(3500.0);//实际发生返利金额*/
		//test end
		//实际发生返利金额：
		/*String yearStr = (String) dataMap.get("year");
		String monthStr = (String) dataMap.get("month");
		int year = Integer.parseInt(yearStr);
		int month = Integer.parseInt(monthStr);
		String dayStr = "-01";
		if(12==month)
		{
			year+=1;
			yearStr = ""+year;
			monthStr = "-01";
		}else
		{
			month+=1;
			monthStr = CommonUtil.addZeroForStr(""+month, 2, 1);
			monthStr = "-"+monthStr;
		}
		String queryTime = yearStr+monthStr+dayStr;
		dataMap.put("queryTime", queryTime);
		
		
		Double partnerRebateAmount = partnerRebateInfoMapper.getActualRebateAmountByMap(dataMap)==null?0.0:partnerRebateInfoMapper.getActualRebateAmountByMap(dataMap);
		partnerRebateInfo.setActualRebateAmount(partnerRebateAmount);
		//剩余的返利金额
		Double currentMonthRebateAmount = partnerRebateInfoMapper.getPartnerBillTotalAmountByMap(dataMap);
		partnerRebateInfo.setCurrentMonthRebateAmount(currentMonthRebateAmount);*/
		String yearStr = (String) dataMap.get("year");
		String monthStr = (String) dataMap.get("month");
		int year = Integer.parseInt(yearStr);
		int month = Integer.parseInt(monthStr);
		String dayStr = "-01";
		if(12==month)
		{
			year = year + 1;
			yearStr = ""+year;
			monthStr = "-01";
		}else
		{
			month = month + 1;
			monthStr = CommonUtil.addZeroForStr(""+month, 2, 1);
			monthStr = "-"+monthStr;
		}
		String yearMonthDate = yearStr+monthStr+dayStr;
		Long partnerId = (Long)dataMap.get("partnerId");
		Map<String,Object> partnerInfo = partnerService.getPartnerAndSapCode(partnerId);
		String sapCode = (String)partnerInfo.get("sapCode");
		Map<String,Object> 	rebatePra = new HashMap<String,Object>();
		rebatePra.put("customerCode", sapCode);
		rebatePra.put("yearMonthDate", yearMonthDate);
		
		Float rebate = dwCdmRebateSellInThroughMapper.selectCdmRebateSummaryByPara(rebatePra)==null?0.0f:
			dwCdmRebateSellInThroughMapper.selectCdmRebateSummaryByPara(rebatePra);
		rebate = rebate * (-1);
		BigDecimal  rebateBigDecimal = new BigDecimal(rebate);  
		rebate =  rebateBigDecimal.setScale(2,  BigDecimal.ROUND_HALF_UP).floatValue(); 
		partnerRebateInfo.setActualRebateAmount(rebate.doubleValue());
		partnerRebateInfo.setCurrentMonthRebateAmount(rebate.doubleValue());
		
		return partnerRebateInfo;
	}


	private WXTPartnerRebateInfo doGetFleeingGoodsPartInfo(
			WXTPartnerRebateInfo partnerRebateInfo, Map<String, Object> dataMap)throws Exception {
		//获取限定值
		String fleeingGoodsReachRotalStr = (String) dataMap.get(REBATE_DATA_FLEEINGGOODS_NOT_REACH);
		int limitTotal = FLEEINGGOODS_REACH_NOT_TOTAL;
		if(null!=fleeingGoodsReachRotalStr && !fleeingGoodsReachRotalStr.isEmpty())
		{
			limitTotal = Integer.parseInt(fleeingGoodsReachRotalStr);
		}
		//test start
		//定义一个窜货的折扣率   用整数标识
		Double testHasFleeingGoods = 90.0;
		if(testHasFleeingGoods>=limitTotal)
		{
			partnerRebateInfo.setFleeingGoodsIsReach(IS_REACH_TRUE);
		}else
		{
			partnerRebateInfo.setFleeingGoodsIsReach(IS_REACH_FALSE);
		}
		//test end
		return partnerRebateInfo;
	}


	private WXTPartnerRebateInfo doGetScanSalesPartInfo(
			WXTPartnerRebateInfo partnerRebateInfo, Map<String, Object> dataMap)throws Exception {
		//获取限定值
		String scanSalesReachRotalStr = (String) dataMap.get(REBATE_DATA_SCANSALES_REACH);
		int limitTotal = SCANSALES_REACH_TOTAL;
		if(null!=scanSalesReachRotalStr && !scanSalesReachRotalStr.isEmpty())
		{
			limitTotal = Integer.parseInt(scanSalesReachRotalStr);
		}
		//test start
		/*partnerRebateInfo.setPurchaseLiters(3000L);
		partnerRebateInfo.setSalesLiters(2400L);*/
		/*Long instockLiters = 3000L;
		Long outstockLiters = 2400L;
		//test end 
		String yearStr = (String) dataMap.get("year");
		String monthStr = (String) dataMap.get("month");
		int month = Integer.parseInt(monthStr);
		String dayStr = "-01";
		String queryStartDate = "";
		String queryEndDate = "";
		if(12==month)
		{
			queryStartDate=yearStr+"-"+month+dayStr;
			queryEndDate = ""+(Integer.parseInt(yearStr)+1)+"-01"+dayStr;
		}else
		{
			queryStartDate = yearStr+"-"+CommonUtil.addZeroForStr(monthStr, 2, 1)+dayStr;
			queryEndDate = yearStr+"-"+CommonUtil.addZeroForStr(""+(Integer.parseInt(monthStr)+1),2,1)+dayStr;
		}
		dataMap.put("queryStartTime", queryStartDate);
		dataMap.put("queryEndTime", queryEndDate);
		//进货升数
		//instockLiters = partnerRebateInfoMapper.getPartnerInStockLitersByMap(dataMap)==null?0L:partnerRebateInfoMapper.getPartnerInStockLitersByMap(dataMap);
		//出库/扫码升数
		//outstockLiters = partnerRebateInfoMapper.getPartnerOutStockLitersByMap(dataMap)==null?0L:partnerRebateInfoMapper.getPartnerOutStockLitersByMap(dataMap);
		 * 
		 * */
		String yearStr = (String) dataMap.get("year");
		String monthStr = (String) dataMap.get("month");
		int year = Integer.parseInt(yearStr);
		int month = Integer.parseInt(monthStr);
		String dayStr = "-01";
		if(12==month)
		{
			year = year + 1;
			yearStr = ""+year;
			monthStr = "-01";
		}else
		{
			month = month + 1;
			monthStr = CommonUtil.addZeroForStr(""+month, 2, 1);
			monthStr = "-"+monthStr;
		}
		String yearMonthDate = yearStr+monthStr+dayStr;
		Long partnerId = (Long)dataMap.get("partnerId");
		Map<String,Object> partnerInfo = partnerService.getPartnerAndSapCode(partnerId);
		String sapCode = (String)partnerInfo.get("sapCode");
		Map<String,Object> 	rebatePra = new HashMap<String,Object>();
		rebatePra.put("customerCode", sapCode);
		rebatePra.put("yearMonthDate", yearMonthDate);
		
		Float sellIn = dwCdmRebateSellInThroughMapper.selectSellInSummaryByPara(rebatePra);
		/*Float sellThrough = dwCdmRebateSellInThroughMapper.selectSellThroughSummaryByPara(rebatePra);*/
		Map<String,Object> reqMap = new HashMap<String,Object>();
		getReqParmMap(reqMap,partnerId,Integer.parseInt((String) dataMap.get("year")),Integer.parseInt((String) dataMap.get("month")));
		// 统计总的sell-through的数据
		List<PartnerSalesInfo> partnerSalesInfo1 = partnerRebateInfoMapper.getPartnerSellThroughInfo(reqMap);
		Float sellThrough = 0F;
		if(partnerSalesInfo1 != null && partnerSalesInfo1.size() > 0){
			sellThrough = partnerSalesInfo1.get(0).getSellThrough();
		}
		 
		log.info("sellIn:"+sellIn+"================"+partnerRebateInfo);
		partnerRebateInfo.setPurchaseLiters(sellIn==null?0L:sellIn.longValue());
		partnerRebateInfo.setSalesLiters(sellThrough==null?0L:sellThrough.longValue());
		if(partnerRebateInfo.getPurchaseLiters() <= 0L)
		{
			partnerRebateInfo.setScanCodeRatio(100.0);
		}else
		{
			partnerRebateInfo.setScanCodeRatio(partnerRebateInfo.getSalesLiters()*100.0/partnerRebateInfo.getPurchaseLiters());
		}
		if(partnerRebateInfo.getScanCodeRatio()>=limitTotal)
		{
			partnerRebateInfo.setSalesIsReach(IS_REACH_TRUE);
		}else 
		{
			partnerRebateInfo.setSalesIsReach(IS_REACH_FALSE);
		}
		return partnerRebateInfo;
	}


	private Map<String, Object> doGetReachSettingData()throws Exception {
		Map<String, Object> dataMap =  dicService.getDicItemByDicTypeCode("partner.rebate.data");
		List<DicItemVo> dataItemlist = (ArrayList)dataMap.get("data");
		for(DicItemVo dicItem : dataItemlist){
			dataMap.put(dicItem.getDicItemCode(), dicItem.getDicItemName());
		}
		return dataMap;
	}


	private WXTPartnerRebateInfo doGetTaskPartInfo(
			WXTPartnerRebateInfo partnerRebateInfo,Map<String,Object> dataMap)throws Exception {
		PartnerRebateTaskConditions taskParams = new PartnerRebateTaskConditions();
		taskParams.setPartnerId(partnerRebateInfo.getPartnerId());
		taskParams.setYear(partnerRebateInfo.getYearStr());
		taskParams.setMonth(partnerRebateInfo.getMonthStr());
		Map<String,Object> returnMap = getPartnerRebateTaskDataInfo(taskParams);
		Long total = (Long) returnMap.get(PartnerRebateTaskConditions.TOTALRECORD);
		partnerRebateInfo.setActiveWorkshopNumber(Integer.valueOf(""+total));
		String workshopReachRotalStr = (String) dataMap.get(REBATE_DATA_WORKSHOP_REACH);
		int limitTotal = WORKSHOP_REACH_TOTAL;
		if(null!=workshopReachRotalStr && !workshopReachRotalStr.isEmpty())
		{
			limitTotal = Integer.parseInt(workshopReachRotalStr);
		}
		if(total>=limitTotal)
		{
			partnerRebateInfo.setWorkshopIsReach(IS_REACH_TRUE);
		}else 
		{
			partnerRebateInfo.setWorkshopIsReach(IS_REACH_FALSE);
		}
		return partnerRebateInfo;
	}
	
	@Override
	public Map<String, Object> submitPartnerRebateInfo(
			WXTPartnerRebateInfo rebateInfo) {
		log.info("statisticsPartnerRebateCommonData submitPartnerRebateInfo:{},",JsonUtil.writeValue(rebateInfo));
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Date currentDate = new Date();
			rebateInfo.setCreateTime(currentDate);
			rebateInfo.setUpdateTime(currentDate);
			rebateInfo.setWorkshopNotReachDiscountRatio(rebateInfo.getWorkshopNotReachDiscountRatio()*100);
			// 出库扫码考核
			rebateInfo.setScancodeNotReachDiscountRatio(rebateInfo.getScancodeNotReachDiscountRatio()*100);
			// 窜货记录
			rebateInfo.setFleeingGoodsDiscountRatio(rebateInfo.getFleeingGoodsDiscountRatio()*100);
			//实际应得返利折扣比例
			Double actualDeservedRebateDiscountRatio = 100-(rebateInfo.getWorkshopNotReachDiscountRatio()==null?0.0:rebateInfo.getWorkshopNotReachDiscountRatio());
			actualDeservedRebateDiscountRatio = actualDeservedRebateDiscountRatio-(rebateInfo.getScancodeNotReachDiscountRatio()==null?0.0:rebateInfo.getScancodeNotReachDiscountRatio());
			actualDeservedRebateDiscountRatio = actualDeservedRebateDiscountRatio-(rebateInfo.getFleeingGoodsDiscountRatio()==null?0.0:rebateInfo.getFleeingGoodsDiscountRatio());
			rebateInfo.setActualDeservedRebateDiscountRatio(actualDeservedRebateDiscountRatio);
			//实际应得返利金额
			Double actualDeservedRebateAmount = (actualDeservedRebateDiscountRatio*rebateInfo.getActualRebateAmount())/100;
			rebateInfo.setActualDeservedRebateAmount(actualDeservedRebateAmount);
			//总返利金额
			/*Double totalRebateAmount = rebateInfo.getCurrentMonthRebateAmount()+actualDeservedRebateAmount;
			rebateInfo.setTotalRebateAmount(totalRebateAmount);*/
			rebateInfo.setStatus("1"); 
			
			partnerRebateInfoMapper.insertSelective(rebateInfo);
			
			WXTPartnerRebateInfoApproveHistory reebateInfoApproveHistory = new WXTPartnerRebateInfoApproveHistory();
			reebateInfoApproveHistory.setPartnerId(rebateInfo.getPartnerId());
			reebateInfoApproveHistory.setYearStr(rebateInfo.getYearStr());
			reebateInfoApproveHistory.setMonthStr(rebateInfo.getMonthStr());
			reebateInfoApproveHistory.setCreateTime(rebateInfo.getCreateTime());
			reebateInfoApproveHistory.setActualRebateAmount(rebateInfo.getActualDeservedRebateAmount());
			reebateInfoApproveHistory.setActualDeservedRebateDiscountRatio(rebateInfo.getActualDeservedRebateDiscountRatio());
			reebateInfoApproveHistory.setActualDeservedRebateAmount(rebateInfo.getActualDeservedRebateAmount());
			
			reebateInfoApproveHistory.setCurrentMonthRebateAmount(rebateInfo.getCurrentMonthRebateAmount());
			reebateInfoApproveHistory.setTotalRebateAmount(rebateInfo.getTotalRebateAmount());
			reebateInfoApproveHistory.setWorkshopIsReach(rebateInfo.getWorkshopIsReach());
			reebateInfoApproveHistory.setWorkshopReachRatio(rebateInfo.getWorkshopReachRatio());
			reebateInfoApproveHistory.setActiveWorkshopNumber(rebateInfo.getActiveWorkshopNumber());
			reebateInfoApproveHistory.setWorkshopNotReachDiscountAmount(rebateInfo.getWorkshopNotReachDiscountAmount());
			reebateInfoApproveHistory.setWorkshopReachRatio(rebateInfo.getWorkshopReachRatio());
			
			reebateInfoApproveHistory.setSalesIsReach(rebateInfo.getSalesIsReach());
			reebateInfoApproveHistory.setScancodeReachRatio(rebateInfo.getScancodeReachRatio());
			reebateInfoApproveHistory.setPurchaseLiters(rebateInfo.getPurchaseLiters());
			reebateInfoApproveHistory.setSalesLiters(rebateInfo.getSalesLiters());
			reebateInfoApproveHistory.setScanCodeRatio(rebateInfo.getScanCodeRatio());
			reebateInfoApproveHistory.setScancodeNotReachDiscountAmount(rebateInfo.getScancodeNotReachDiscountAmount());
			reebateInfoApproveHistory.setScancodeNotReachDiscountRatio(rebateInfo.getScancodeNotReachDiscountRatio());
			
			reebateInfoApproveHistory.setFleeingGoodsIsReach(rebateInfo.getFleeingGoodsIsReach());
			reebateInfoApproveHistory.setFleeingGoodsRatio(rebateInfo.getFleeingGoodsRatio());
			reebateInfoApproveHistory.setFleeingGoodsDiscountAmount(rebateInfo.getFleeingGoodsDiscountAmount());
			reebateInfoApproveHistory.setFleeingGoodsDiscountRatio(rebateInfo.getFleeingGoodsDiscountRatio());
			reebateInfoApproveHistory.setFleeingGoodsRemark(rebateInfo.getFleeingGoodsRemark());
			
			reebateInfoApproveHistory.setStatus(rebateInfo.getStatus());
			reebateInfoApproveHistory.setCreator(ContextUtil.getCurUserId());
			
			partnerRebateInfoApproveHistoryMapper.insertSelective(reebateInfoApproveHistory);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}
	
	
	
	@Override
	public Map<String, Object> updatePartnerRebateInfo(
			WXTPartnerRebateInfo rebateInfo) {
		log.info("statisticsPartnerRebateCommonData submitPartnerRebateInfo:{},",JsonUtil.writeValue(rebateInfo));
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Date currentDate = new Date();
			rebateInfo.setUpdateTime(currentDate);
			rebateInfo.setWorkshopNotReachDiscountRatio(rebateInfo.getWorkshopNotReachDiscountRatio()*100);
			// 出库扫码考核
			rebateInfo.setScancodeNotReachDiscountRatio(rebateInfo.getScancodeNotReachDiscountRatio()*100);
			// 窜货记录
			rebateInfo.setFleeingGoodsDiscountRatio(rebateInfo.getFleeingGoodsDiscountRatio()*100);
			//实际应得返利折扣比例
			Double actualDeservedRebateDiscountRatio = 100-(rebateInfo.getWorkshopNotReachDiscountRatio()==null?0.0:rebateInfo.getWorkshopNotReachDiscountRatio());
			actualDeservedRebateDiscountRatio = actualDeservedRebateDiscountRatio-(rebateInfo.getScancodeNotReachDiscountRatio()==null?0.0:rebateInfo.getScancodeNotReachDiscountRatio());
			actualDeservedRebateDiscountRatio = actualDeservedRebateDiscountRatio-(rebateInfo.getFleeingGoodsDiscountRatio()==null?0.0:rebateInfo.getFleeingGoodsDiscountRatio());
			rebateInfo.setActualDeservedRebateDiscountRatio(actualDeservedRebateDiscountRatio);
			//实际应得返利金额
			Double actualDeservedRebateAmount = (actualDeservedRebateDiscountRatio*rebateInfo.getActualRebateAmount())/100;
			rebateInfo.setActualDeservedRebateAmount(actualDeservedRebateAmount);
			//总返利金额
			/*Double totalRebateAmount = rebateInfo.getCurrentMonthRebateAmount()+actualDeservedRebateAmount;
			rebateInfo.setTotalRebateAmount(totalRebateAmount);*/
			rebateInfo.setStatus("2"); 
			
			WXTPartnerRebateInfoExample  example =  new WXTPartnerRebateInfoExample(); 
			WXTPartnerRebateInfoExample.Criteria criteria = example.createCriteria();
			criteria.andPartnerIdEqualTo(rebateInfo.getPartnerId());
			criteria.andYearstrEqualTo(rebateInfo.getYearStr());
			criteria.andMonthstrEqualTo(rebateInfo.getMonthStr());
			List<WXTPartnerRebateInfo> rebateInfoList = partnerRebateInfoMapper.selectByExample(example);
			if(rebateInfoList == null || rebateInfoList.size() != 1){
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "返利情况异常！");
			}else{
				rebateInfo.setId(rebateInfoList.get(0).getId());
				partnerRebateInfoMapper.updateByPrimaryKeySelective(rebateInfo);
			}
			
			WXTPartnerRebateInfoApproveHistory reebateInfoApproveHistory = new WXTPartnerRebateInfoApproveHistory();
			reebateInfoApproveHistory.setPartnerId(rebateInfo.getPartnerId());
			reebateInfoApproveHistory.setYearStr(rebateInfo.getYearStr());
			reebateInfoApproveHistory.setMonthStr(rebateInfo.getMonthStr());
			reebateInfoApproveHistory.setCreateTime(rebateInfo.getUpdateTime());
			reebateInfoApproveHistory.setActualRebateAmount(rebateInfo.getActualDeservedRebateAmount());
			reebateInfoApproveHistory.setActualDeservedRebateDiscountRatio(rebateInfo.getActualDeservedRebateDiscountRatio());
			reebateInfoApproveHistory.setActualDeservedRebateAmount(rebateInfo.getActualDeservedRebateAmount());
			
			reebateInfoApproveHistory.setCurrentMonthRebateAmount(rebateInfo.getCurrentMonthRebateAmount());
			reebateInfoApproveHistory.setTotalRebateAmount(rebateInfo.getTotalRebateAmount());
			reebateInfoApproveHistory.setWorkshopIsReach(rebateInfo.getWorkshopIsReach());
			reebateInfoApproveHistory.setWorkshopReachRatio(rebateInfo.getWorkshopReachRatio());
			reebateInfoApproveHistory.setActiveWorkshopNumber(rebateInfo.getActiveWorkshopNumber());
			reebateInfoApproveHistory.setWorkshopNotReachDiscountAmount(rebateInfo.getWorkshopNotReachDiscountAmount());
			reebateInfoApproveHistory.setWorkshopReachRatio(rebateInfo.getWorkshopReachRatio());
			
			reebateInfoApproveHistory.setSalesIsReach(rebateInfo.getSalesIsReach());
			reebateInfoApproveHistory.setScancodeReachRatio(rebateInfo.getScancodeReachRatio());
			reebateInfoApproveHistory.setPurchaseLiters(rebateInfo.getPurchaseLiters());
			reebateInfoApproveHistory.setSalesLiters(rebateInfo.getSalesLiters());
			reebateInfoApproveHistory.setScanCodeRatio(rebateInfo.getScanCodeRatio());
			reebateInfoApproveHistory.setScancodeNotReachDiscountAmount(rebateInfo.getScancodeNotReachDiscountAmount());
			reebateInfoApproveHistory.setScancodeNotReachDiscountRatio(rebateInfo.getScancodeNotReachDiscountRatio());
			
			reebateInfoApproveHistory.setFleeingGoodsIsReach(rebateInfo.getFleeingGoodsIsReach());
			reebateInfoApproveHistory.setFleeingGoodsRatio(rebateInfo.getFleeingGoodsRatio());
			reebateInfoApproveHistory.setFleeingGoodsDiscountAmount(rebateInfo.getFleeingGoodsDiscountAmount());
			reebateInfoApproveHistory.setFleeingGoodsDiscountRatio(rebateInfo.getFleeingGoodsDiscountRatio());
			reebateInfoApproveHistory.setFleeingGoodsRemark(rebateInfo.getFleeingGoodsRemark());
			
			reebateInfoApproveHistory.setStatus(rebateInfo.getStatus());
			reebateInfoApproveHistory.setCreator(ContextUtil.getCurUserId());
			
			partnerRebateInfoApproveHistoryMapper.insertSelective(reebateInfoApproveHistory);
						
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}


	@Override
	public Map<String, Object> getPartnerRebateInfo(Long partnerId,
			String yearMonthStr) {
		log.info("PartnerRebateServiceImpl getPartnerRebateInfo partnerId:{},yearMonthStr:{}",partnerId,yearMonthStr);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			// 构造参数
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap = doCreateParamsMap(reqMap,partnerId,yearMonthStr);
			
			// 统计合伙人扫码/返利信息
			List<ResponsePartnerRebateInfo> lst = partnerRebateInfoMapper.queryPartnerRebateInfoByMap(reqMap);
			
			// 组装返回的lst
			doCreateResponsePartnerRebateInfoLst(lst,reqMap);
			log.info("PartnerRebateServiceImpl getPartnerRebateInfo lst:{}",JsonUtil.writeValue(lst));
			resultMap.put(RESULT_LST_KEY, lst);
			
			boolean isChevronChevronCdmChannelManager = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_PRICE_AUDIT);
			resultMap.put("isChevronChevronCdmChannelManager", isChevronChevronCdmChannelManager);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}
	
	@Override
	public Map<String, Object> getPartnerRebateInfoConfirmed(Long partnerId,
			String yearMonthStr) {
		log.info("PartnerRebateServiceImpl getPartnerRebateInfo partnerId:{},yearMonthStr:{}",partnerId,yearMonthStr);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			// 构造参数
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap = doCreateParamsMap(reqMap,partnerId,yearMonthStr);
			
			// 统计合伙人扫码/返利信息
			List<ResponsePartnerRebateInfo> lst = partnerRebateInfoMapper.queryPartnerRebateInfoConfirmedByMap(reqMap);

			// 组装返回的lst
			doCreateResponsePartnerRebateInfoLst(lst,reqMap);
			log.info("PartnerRebateServiceImpl getPartnerRebateInfo lst:{}",JsonUtil.writeValue(lst));
			resultMap.put(RESULT_LST_KEY, lst);
						
			
			boolean isChevronChevronCdmChannelManager = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_PRICE_AUDIT);
			resultMap.put("isChevronChevronCdmChannelManager", isChevronChevronCdmChannelManager);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}


	private List<ResponsePartnerRebateInfo> doCreateResponsePartnerRebateInfoLst(
			List<ResponsePartnerRebateInfo> lst,Map<String,Object> dataMap)throws Exception {
		log.info("PartnerRebateServiceImpl doCreateResponsePartnerRebateInfoLst lst:{}",lst);
		if(null==lst || lst.isEmpty())
		{
			return new ArrayList<ResponsePartnerRebateInfo>();
		}
		
		String scanSalesReachRotalStr = (String) dataMap.get(REBATE_DATA_SCANSALES_REACH);
		int limitTotal = SCANSALES_REACH_TOTAL;
		if(null!=scanSalesReachRotalStr && !scanSalesReachRotalStr.isEmpty())
		{
			limitTotal = Integer.parseInt(scanSalesReachRotalStr);
		}
		String yearMonthStr = (String) dataMap.get("yearMonthStr");
		
		for(ResponsePartnerRebateInfo respPartnerRebateInfo : lst)
		{
			String workshopIsReach = respPartnerRebateInfo.getWorkshopIsReach();
			respPartnerRebateInfo.setYearMonthStr(yearMonthStr);
			if(null!=workshopIsReach)//证明是已经确认了返利的情况
			{
				double currentRebateAmount = respPartnerRebateInfo.getCurrentRebateAmount()==null?0:respPartnerRebateInfo.getCurrentRebateAmount();
				double actualRebateAmount = respPartnerRebateInfo.getActualRebateAmount()==null?0:respPartnerRebateInfo.getActualRebateAmount();
				respPartnerRebateInfo.setEditFlag(EDIT_DISABLED);
				respPartnerRebateInfo.setDiscountRebateAmount(currentRebateAmount-actualRebateAmount);
			}else//未有确认返利，从dw表中获取
			{
				double rebateAmount = respPartnerRebateInfo.getCdmRebate()==null?0:respPartnerRebateInfo.getCdmRebate();
				respPartnerRebateInfo.setEditFlag(EDIT_ENABLED);
				respPartnerRebateInfo.setCurrentRebateAmount(rebateAmount);
				respPartnerRebateInfo.setActualRebateAmount(rebateAmount);
				respPartnerRebateInfo.setWorkshopIsReach(respPartnerRebateInfo.getIsWorkshopReach());
				respPartnerRebateInfo.setActiveWorkshopNumber(respPartnerRebateInfo.getWorkshopCount());
				respPartnerRebateInfo.setPurchaseLiters(respPartnerRebateInfo.getSellIn()==null?0L:respPartnerRebateInfo.getSellIn());
				respPartnerRebateInfo.setSalesLiters(respPartnerRebateInfo.getSellThrouth()==null?0L:respPartnerRebateInfo.getSellThrouth());
				if(respPartnerRebateInfo.getPurchaseLiters() <= 0L)
				{
					respPartnerRebateInfo.setScanCodeRatio(100.0);
				}else
				{
					respPartnerRebateInfo.setScanCodeRatio(Double.parseDouble(formatDouble(respPartnerRebateInfo.getSalesLiters()*100.0/respPartnerRebateInfo.getPurchaseLiters())));
				}
				if(respPartnerRebateInfo.getScanCodeRatio()>=limitTotal)
				{
					respPartnerRebateInfo.setSalesIsReach(IS_REACH_TRUE);
				}else 
				{
					respPartnerRebateInfo.setSalesIsReach(IS_REACH_FALSE);
				}
			}
		}
		return lst;
	}


	private Map<String, Object> doCreateParamsMap(Map<String, Object> reqMap,Long partnerId,String yearMonthStr)throws Exception {
		//从数据字典中获取
		reqMap = doGetReachSettingData();
		//门店录入数量合格指标
		String workshopReachRotalStr = (String) reqMap.get(REBATE_DATA_WORKSHOP_REACH);
		int limitTotal = WORKSHOP_REACH_TOTAL;
		if(null!=workshopReachRotalStr && !workshopReachRotalStr.isEmpty())
		{
			limitTotal = Integer.parseInt(workshopReachRotalStr);
		}
		reqMap.put("yearMonthStr", yearMonthStr);
		reqMap.put("partnerId", partnerId);
		reqMap.put("workshopReachCount", limitTotal);
		reqMap.put("queryStartDateStr", yearMonthStr+"-01");
		String yearMonth[] = yearMonthStr.split("-");
		String queryEndDateStr = "";
		if(yearMonth[1].equals("12"))
		{
			queryEndDateStr = (Integer.parseInt(yearMonth[0])+1)+"-01"+"-01";
		}else
		{
			queryEndDateStr = yearMonth[0]+"-"+CommonUtil.addZeroForStr(""+(Integer.parseInt(yearMonth[1])+1),2,1)+"-01";
		}
		reqMap.put("queryEndDateStr", queryEndDateStr);
		reqMap.put("dwQueryDateStr", queryEndDateStr);
		
		return reqMap;
	}
	
	
	@Override
	public Map<String, Object> getPartnerTotalRebateAmountInfo(Long partnerId) {
		log.info("PartnerRebateServiceImpl getPartnerTotalRebateAmountInfo partnerId:{}",partnerId);
		Map<String, Object>  resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("partnerId", partnerId);
			List<ResponseTotalRebateAmount> lst = partnerRebateInfoMapper.queryTotalRebateAmount(reqMap);
			log.info("PartnerRebateServiceImpl getPartnerTotalRebateAmountInfo lst:{}",JsonUtil.writeValue(lst));
			resultMap.put(RESULT_LST_KEY, lst);
			ResponseTotalRebateAmount summary = new ResponseTotalRebateAmount();
			Double totalAmount = summary.getTotalAmount();
			Double usedAmount = summary.getUsedAmount();
			Double applyFund = summary.getApplyFund();
			for(ResponseTotalRebateAmount tempRebate : lst){
				if(totalAmount == null){
					totalAmount = 0d;
				}
				totalAmount = totalAmount + tempRebate.getTotalAmount();
				if(usedAmount == null){
					usedAmount = 0d;
				}
				usedAmount = usedAmount + tempRebate.getUsedAmount();
				if(applyFund == null){
					applyFund = 0d;
				}
				applyFund = applyFund + tempRebate.getApplyFund();				
			}
			summary.setTotalAmount(totalAmount);
			summary.setUsedAmount(usedAmount);
			summary.setApplyFund(applyFund);
			resultMap.put("summary", summary);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}
	
	
	
	
	@Override
	public Map<String, Object> getPartnerTotalRebateAmountInfoByConditions(
			ResponseTotalRebateAmountConditions responseTotalRebateAmountConditions) {
		log.info("PartnerRebateServiceImpl getPartnerTotalRebateAmountInfoByConditions partnerId:{}",responseTotalRebateAmountConditions.getPartnerId());
		Map<String, Object>  resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("partnerId", responseTotalRebateAmountConditions.getPartnerId());
			
			Long totalRecord = 0L;
			WxTUser  tuser = ContextUtil.getCurUser();
			Long partnerId = tuser.getOrgId();
			String uSerType = tuser.getUserModel();
			if(!WxTUser.USER_MODEL_CHEVRON.equals(uSerType))
			{
				responseTotalRebateAmountConditions.setPartnerId(partnerId);
				reqMap.put("partnerId", partnerId);
			}
			
			List<ResponseTotalRebateAmount> lst = partnerRebateInfoMapper.queryTotalRebateAmount(reqMap);
			log.info("PartnerRebateServiceImpl getPartnerTotalRebateAmountInfo lst:{}",JsonUtil.writeValue(lst));
			/*resultMap.put(RESULT_LST_KEY, lst);*/
			ResponseTotalRebateAmount summary = new ResponseTotalRebateAmount();
			Double totalAmount = summary.getTotalAmount();
			Double usedAmount = summary.getUsedAmount();
			Double applyFund = summary.getApplyFund();
			for(ResponseTotalRebateAmount tempRebate : lst){
				if(totalAmount == null){
					totalAmount = 0d;
				}
				totalAmount = totalAmount + tempRebate.getTotalAmount();
				if(usedAmount == null){
					usedAmount = 0d;
				}
				usedAmount = usedAmount + tempRebate.getUsedAmount();
				if(applyFund == null){
					applyFund = 0d;
				}
				applyFund = applyFund + tempRebate.getApplyFund();				
			}
			summary.setTotalAmount(totalAmount);
			summary.setUsedAmount(usedAmount);
			summary.setApplyFund(applyFund);
			resultMap.put("summary", summary);
			
			
			List<ResponseTotalRebateAmount> list = partnerRebateInfoMapper.queryTotalRebateAmountByCondition(responseTotalRebateAmountConditions);
			resultMap.put(RESULT_LST_KEY, list);
			totalRecord = responseTotalRebateAmountConditions.getTotalCount();
			resultMap.put(PartnerRebateTaskConditions.TOTALRECORD, totalRecord);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}


	@Override
	public Map<String, Object> getPartnerTotalRebateAmountDetail(Long partnerId) {
		log.info("PartnerRebateServiceImpl getPartnerTotalRebateAmountDetail partnerId:{}",partnerId);
		Map<String, Object>  resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("partnerId", partnerId);
			List<ResponseTotalRebateAmountDetail> lst = partnerRebateInfoMapper.queryTotalRebateAmountDetail(reqMap);
			log.info("PartnerRebateServiceImpl getPartnerTotalRebateAmountDetail lst:{}",JsonUtil.writeValue(lst));
			resultMap.put(RESULT_LST_KEY, lst);
			boolean isChevronChevronCdmChannelManager = wxRoleService.isIncludeRole(ContextUtil.getCurUser().getUserId(), Constants.CHEVRON_PRICE_AUDIT);
			resultMap.put("isChevronChevronCdmChannelManager", isChevronChevronCdmChannelManager);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
			return resultMap;
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}
	
   public static String formatDouble(double d) {
        DecimalFormat df = new DecimalFormat("#.00");
        return df.format(d);
    }


	@Override
	public Map<String, Object> getPartnerSalesInfo(Long partnerId, int year, int month)throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		ResponsePartnerSalesInfoVo responsePartnerSalesInfoVo = new ResponsePartnerSalesInfoVo();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		getReqParmMap(reqMap,partnerId,year,month);
		// 1.统计总的sell-in的数据
		PartnerSalesInfo partnerSalesInfo = partnerRebateInfoMapper.getPartnerSellInInfo(reqMap);
		if(null!=partnerSalesInfo)
		{
			responsePartnerSalesInfoVo.setPurchaseLiters(partnerSalesInfo.getSellIn()==null?0:partnerSalesInfo.getSellIn().longValue());
		}
		// 2.统计总的sell-through的数据
		List<PartnerSalesInfo> partnerSalesInfo1 = partnerRebateInfoMapper.getPartnerSellThroughInfo(reqMap);
		if(null!=partnerSalesInfo1 && !partnerSalesInfo1.isEmpty())
		{
			responsePartnerSalesInfoVo.setSalesLiters(partnerSalesInfo1.get(0).getSellThrough()==null?0:partnerSalesInfo1.get(0).getSellThrough().longValue());
		}
		if(responsePartnerSalesInfoVo.getSalesLiters()>=responsePartnerSalesInfoVo.getPurchaseLiters() && responsePartnerSalesInfoVo.getSalesLiters()!=0)
		{
			responsePartnerSalesInfoVo.setScanCodeRatio(100d);
		}else
		{
			if(responsePartnerSalesInfoVo.getPurchaseLiters() <= 0 )
			{
				responsePartnerSalesInfoVo.setScanCodeRatio(100.0);
			}else {
				Float ff = (responsePartnerSalesInfoVo.getSalesLiters() * 100.0f) / responsePartnerSalesInfoVo.getPurchaseLiters();
				responsePartnerSalesInfoVo.setScanCodeRatio(Double.parseDouble(formatDouble(ff.doubleValue())));
			}
		}

		// 3.统计每一天的sell-through数据
		List<PartnerSalesInfo> lst = partnerRebateInfoMapper.getPartnerSellThroughInfoEveryDay(reqMap);

		// 4.构造x轴
		responsePartnerSalesInfoVo = generateXecharts(responsePartnerSalesInfoVo,year,month);


		// 5.组装y轴的数据
		responsePartnerSalesInfoVo = generateYValueEcaharts(responsePartnerSalesInfoVo,lst);
		resultMap.put("responsePartnerSalesInfo",responsePartnerSalesInfoVo);
		return resultMap;
	}

	private ResponsePartnerSalesInfoVo generateYValueEcaharts(ResponsePartnerSalesInfoVo responsePartnerSalesInfoVo, List<PartnerSalesInfo> lst)throws Exception {

		Map<Integer,Object> dataMap = new HashMap<Integer,Object>();
		for(PartnerSalesInfo  partnerSalesInfo: lst)
		{
			dataMap.put(partnerSalesInfo.getDay(),partnerSalesInfo.getSellThrough()==null?0:partnerSalesInfo.getSellThrough().longValue());
		}
		Object yvalues[] = new Object[responsePartnerSalesInfoVo.getXechart().length];
		for(int i=0;i<yvalues.length;i++)
		{
			yvalues[i] = dataMap.get(i+1)==null?0:dataMap.get(i+1);
		}
		responsePartnerSalesInfoVo.setYvalue(yvalues);
		return responsePartnerSalesInfoVo;
	}

	private ResponsePartnerSalesInfoVo generateXecharts(ResponsePartnerSalesInfoVo responsePartnerSalesInfoVo ,int year, int month)throws  Exception  {

		int selectedMonthDays = DateUtil.getDaysByYearMonth(year,month);
		//构造x轴的数据
		Object xechart[] = new Object[selectedMonthDays];
		for(int i=0;i<xechart.length;i++)
		{
			xechart[i] = i+1;
		}
		responsePartnerSalesInfoVo.setXechart(xechart);
		return responsePartnerSalesInfoVo;
	}

	/**
	 * 构造查询参数
	 * @param reqMap
	 * @param partnerId
	 * @param year
	 * @param month
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> getReqParmMap(Map<String,Object> reqMap,Long partnerId, int year, int month)throws  Exception
	{
		if(month==12)
		{
			reqMap.put("startTime", DateUtils.parseDate(year+"-"+month+"-01", DateUtils.DEFAULT_DATE_PATTERN));
			reqMap.put("endTime", DateUtils.parseDate((year+1)+"-01"+"-01", DateUtils.DEFAULT_DATE_PATTERN));
		}else
		{
			reqMap.put("startTime",DateUtils.parseDate(year+"-"+CommonUtil.addZeroForStr(""+month, 2, 1)+"-01", DateUtils.DEFAULT_DATE_PATTERN));
			reqMap.put("endTime",DateUtils.parseDate(year+"-"+CommonUtil.addZeroForStr(""+(month+1),2,1)+"-01", DateUtils.DEFAULT_DATE_PATTERN));
		}

		//Sell-In
		/*if(month==1)
		{
			reqMap.put("sellInStartTime", DateUtils.parseDate((year-1)+"-12"+"-01", DateUtils.DEFAULT_DATE_PATTERN));
			reqMap.put("sellInEndTime", DateUtils.parseDate(year+"-"+CommonUtil.addZeroForStr(""+(month),2,1)+"-01", DateUtils.DEFAULT_DATE_PATTERN));
		}else
		{
			reqMap.put("sellInStartTime", DateUtils.parseDate(year+"-"+CommonUtil.addZeroForStr(""+(month-1),2,1)+"-01", DateUtils.DEFAULT_DATE_PATTERN));
			reqMap.put("sellInEndTime", DateUtils.parseDate(year+"-"+month+"-01", DateUtils.DEFAULT_DATE_PATTERN));
		}*/
		if(month==12)
		{
			reqMap.put("sellInTime", (year + 1 )+"-01"+"-1");
		}else
		{
			reqMap.put("sellInTime", year+"-"+CommonUtil.addZeroForStr(""+(month+1),2,1) + "-1");
		}

		reqMap.put("partnerId",partnerId);
		return reqMap;
	}

	@Override
	public Map<String, Object> doSndMessageForPartnerSalesInfo(Long partnerId, int year, int month) throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		getReqParmMap(reqMap,partnerId,year,month);
		//1.获取上个月有sell-In的合伙人
		List<PartnerSalesInfoToMessageVo> lst =  partnerRebateInfoMapper.getPartnerSellInInfoForMessage(reqMap);
		if(null==lst || lst.isEmpty())
		{
			doSaveLog(ERROR_CODE,"查询到上个月所有的合伙人，都没有sell-in记录");
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY,"查询到上个月所有的合伙人，都没有sell-in记录");
			return resultMap;
		}

		List<Long> partnerIds = new ArrayList<Long>();
		for(PartnerSalesInfoToMessageVo tmp: lst)
		{
			partnerIds.add(tmp.getPartnerId());
		}
		reqMap.put("partnerIds",partnerIds);
		//2. Sell-through数据，根据合伙人id数组
		List<PartnerSalesInfo> lstSellThrough =  partnerRebateInfoMapper.getPartnerSellThroughInfo(reqMap);
		if(null==lstSellThrough || lstSellThrough.isEmpty())
		{

			doSaveLog(ERROR_CODE,"没有扫码出库信息，给所有合伙人发送消息");
			// 发送message
			doSndMessage(lst);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY,"没有扫码出库信息，给所有合伙人发送消息");
			return resultMap;
		}

		// 3.构造需要发送消息的  合伙人信息  并发送message
		doHandleSellInLstToSndMessage(lst,lstSellThrough);
		return resultMap;
	}

	private void doSaveLog(String code,String message) {
		Log log = new Log();
		log.setLogType("PartnerSalesInfoToMessageVo_"+code);
		log.setOperator(1L);
		log.setExtProperty1("chevron");
		log.setExtProperty2(message);
		log.setCreateTime(new Date());
		logService.save(log);

	}

	private void doHandleSellInLstToSndMessage(List<PartnerSalesInfoToMessageVo> lst, List<PartnerSalesInfo> lstSellThrough)throws Exception {
		Map<Long,Object> dataMap = new HashMap<Long,Object>();
		for(PartnerSalesInfo salesInfo: lstSellThrough)
		{
			dataMap.put(salesInfo.getPartnerId(),salesInfo.getSellThrough()==null?0.0d:salesInfo.getSellThrough().doubleValue());
		}

		List<PartnerSalesInfoToMessageVo> lst2 = new ArrayList<PartnerSalesInfoToMessageVo>();
		for(PartnerSalesInfoToMessageVo tmp: lst)
		{
			Double sellIn = tmp.getSellIn();
			Double sellThrough = dataMap.get(tmp.getPartnerId())==null?0.0d:(Double) dataMap.get(tmp.getPartnerId());
			if(sellThrough/sellIn <0.5)
			{
				lst2.add(tmp);
			}
		}
		//发送消息
		doSndMessage(lst2);
	}

	private void doSndMessage(List<PartnerSalesInfoToMessageVo> lst)throws Exception {
		for(PartnerSalesInfoToMessageVo tmp: lst)
		{
			// 发送邮件 需要保存日志
			doSndEmailMessage(tmp);
			// 发送短信 需要保存日志  单个日志保存
			doSndMobileMessage(tmp);
		}
	}

	private void doSndEmailMessage(PartnerSalesInfoToMessageVo tmp)throws Exception {

		if(null!=tmp.getEmail() && !tmp.getEmail().isEmpty()) {
			Map<String,Object> dataMap = new HashMap<String,Object>();
			String[] acceptEmails = new String[]{tmp.getEmail()};
			dataMap.put("acceptUserName",tmp.getPartnerName());
			dataMap.put("msgcontent",String.format(MESAGE_CONTENT,tmp.getPartnerName()));
			//发送邮件
			ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
			boolean isSuccess = emailSenderService.sendEmailForCommon(context,
					acceptEmails, null, MESAGE_TITLE, dataMap, null, MyPropertyConfigurer.getVal("mail.sp.sellthrough.notreach"));
			doSaveLog(SUCCESS_CODE,String.format(SAVE_EMAIL_LOG_CONTENT,tmp.getEmail(),tmp.getPartnerName()));
		}else
		{
			doSaveLog(ERROR_CODE,String.format(SAVE_EMAIL_LOG_CONTENT,tmp.getEmail(),tmp.getPartnerName()));
		}

	}

	private void doSndMobileMessage(PartnerSalesInfoToMessageVo tmp)throws Exception {
		if(null!=tmp.getMobile() && !tmp.getMobile().isEmpty()) {
			SendMessageUtil.sndMessageToPhone(tmp.getMobile(), String.format(MESAGE_CONTENT,tmp.getPartnerName()));
			doSaveLog(SUCCESS_CODE,String.format(SAVE_LOG_CONTENT,tmp.getMobile(),tmp.getPartnerName()));
		}else
		{
			doSaveLog(ERROR_CODE,String.format(SAVE_LOG_CONTENT,tmp.getMobile(),tmp.getPartnerName()));
		}

	}


	@Override
	public Map<String, Object> sendScanRatioUnreachEmailByMonth() throws Exception {
		Map<String,Object> dataMap = new HashMap<String,Object>();
		Date currentMonthFirstDay = DateUtil.getFirstDayOfMonth(new Date());
		String nextMonthFirstDay = DateUtil.getPerFirstDayOfMonth(new Date());
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("yearMonthDate", nextMonthFirstDay);
		paraMap.put("startTime", currentMonthFirstDay);
		List<PartnerSalesInfo> lstSellinSellThrough =  partnerRebateInfoMapper.statisticSellInSellThroughByMonth(paraMap);
		for(PartnerSalesInfo partnerSaleInfo : lstSellinSellThrough){
			Float sellIn = partnerSaleInfo.getSellIn();
			Float sellThough = partnerSaleInfo.getSellThrough();
			if(sellIn > 0){
				Float scanRatio = sellThough / sellIn;
				if(scanRatio < 0.5){
					Map<String,Object> contentMap = new HashMap<String,Object>();
					String partnerName = partnerSaleInfo.getPartnerName();
					contentMap.put("partnerName", partnerName);
					
					String sellInMonth = DateUtil.getLastMonth(new Date());
					DecimalFormat decimalFormat=new DecimalFormat("#.##");
					String sellInString = decimalFormat.format(sellIn);
					String sellInContent = sellInMonth + "月进货：" + sellInString + "L";
					contentMap.put("sellInContent", sellInContent);
					
					String currentDay = DateUtil.getCurrentDateString();
					contentMap.put("statisticsTime", currentDay);
					String sellThoughString = decimalFormat.format(sellThough);
					String monthStr = DateUtil.getDateStr(new Date(),DateUtil.DATA_FORMAT_PATTERN_MONTH);
					String sellThroughContent = monthStr + "月已扫码：" + sellThoughString + "L";
					contentMap.put("sellThroughContent", sellThroughContent);
					
					String scanRatioStr  = decimalFormat.format(scanRatio * 100) + "%";
					contentMap.put("scanRatio", scanRatioStr);
					
					List<WxTUser> userList =  userMapper.getPartnerUserByOrgIdAndRoleName(partnerSaleInfo.getPartnerId().toString(), Constants.SERVICE_PARTNER_MANAGER_CODE);
					Set<String> emailSet = new HashSet<String>();
					Set<String> mobileSet = new HashSet<String>();
					for(WxTUser user : userList){
						if(!StringUtils.isEmpty(user.getEmail())){
							if(!emailSet.contains(user.getEmail())){
								emailSet.add(user.getEmail());
							}
						}
						if(!StringUtils.isEmpty(user.getMobileTel())){
							if(!mobileSet.contains(user.getMobileTel())){
								mobileSet.add(user.getMobileTel());
							}
						}
					}
							
					String[] accepters = emailSet.toArray(new String[emailSet.size()]);
					String[] accepterCCs = {};
					String subjectName = DateUtils.getCurrentDate(DateUtils.DEFAULT_DATE_PATTERN)+ " 合伙人销售扫码率未达标提醒";
					
					
					WebApplicationContext webApplicationContext = ContextLoader
							.getCurrentWebApplicationContext();
					ServletContext servletContext = webApplicationContext
							.getServletContext();
					emailSenderService.sendEmailForCommon(servletContext,
							accepters, accepterCCs, subjectName, contentMap, null, "statistics_scanratio_noreach_warnning.ftl");
					
					String messageContent = "尊敬的" + partnerName+ "合伙人,截止" + currentDay + "," + sellInContent +"," 
							+ sellThroughContent + ",扫码率：" + scanRatioStr;
					for(String tele : mobileSet){
						sendCaptcha2Phone(tele,messageContent);
					}
							
					
					
				}
			}
		}
		return dataMap;
	}
	
	private void sendCaptcha2Phone(String phone,String captcha) throws Exception{
		
		SMSUtil.sendSms(phone, captcha, SMSUtil.APP_ID_PP);
	}

}
