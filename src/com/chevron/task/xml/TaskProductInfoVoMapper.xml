<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.task.dao.TaskProductInfoVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.task.model.TaskProductInfoVo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="main_task_id" property="mainTaskId" jdbcType="BIGINT" />
    <result column="sub_task_id" property="subTaskId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="name" property="name" jdbcType="NVARCHAR" />
    <result column="price" property="price" jdbcType="NUMERIC" />
    <result column="sale_price" property="salePrice" jdbcType="NUMERIC" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="replenish_quantity" property="replenishQuantity" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    
    <!-- add by bo.liu start -->
   	<result column="category" property="category" jdbcType="NVARCHAR" />
   	<result column="brand" property="brand" jdbcType="NVARCHAR" />
   	<result column="icon_id" property="iconId" jdbcType="BIGINT"/>
    <!-- end -->
    
    <result column="category_desc" property="categoryDesc" jdbcType="NVARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, main_task_id, sub_task_id, sku, name, price, sale_price, quantity, replenish_quantity, 
    create_time, update_time, creator, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.task.model.TaskProductInfoVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_task_product_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_task_product_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_task_product_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.chevron.task.model.TaskProductInfoVo" >
    insert into wx_task_product_info (id, main_task_id, sub_task_id, 
      sku, name, price, 
      sale_price, quantity, replenish_quantity, 
      create_time, update_time, creator, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{mainTaskId,jdbcType=BIGINT}, #{subTaskId,jdbcType=BIGINT}, 
      #{sku,jdbcType=NVARCHAR}, #{name,jdbcType=NVARCHAR}, #{price,jdbcType=NUMERIC}, 
      #{salePrice,jdbcType=NUMERIC}, #{quantity,jdbcType=INTEGER}, #{replenishQuantity,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=NVARCHAR}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.task.model.TaskProductInfoVo" >
    insert into wx_task_product_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="mainTaskId != null" >
        main_task_id,
      </if>
      <if test="subTaskId != null" >
        sub_task_id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="salePrice != null" >
        sale_price,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="replenishQuantity != null" >
        replenish_quantity,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mainTaskId != null" >
        #{mainTaskId,jdbcType=BIGINT},
      </if>
      <if test="subTaskId != null" >
        #{subTaskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="salePrice != null" >
        #{salePrice,jdbcType=NUMERIC},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="replenishQuantity != null" >
        #{replenishQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.task.model.TaskProductInfoVo" >
    update wx_task_product_info
    <set >
      <if test="mainTaskId != null" >
        main_task_id = #{mainTaskId,jdbcType=BIGINT},
      </if>
      <if test="subTaskId != null" >
        sub_task_id = #{subTaskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="salePrice != null" >
        sale_price = #{salePrice,jdbcType=NUMERIC},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="replenishQuantity != null" >
        replenish_quantity = #{replenishQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.task.model.TaskProductInfoVo" >
    update wx_task_product_info
    set main_task_id = #{mainTaskId,jdbcType=BIGINT},
      sub_task_id = #{subTaskId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=NVARCHAR},
      name = #{name,jdbcType=NVARCHAR},
      price = #{price,jdbcType=NUMERIC},
      sale_price = #{salePrice,jdbcType=NUMERIC},
      quantity = #{quantity,jdbcType=INTEGER},
      replenish_quantity = #{replenishQuantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  
  <!-- add by bo.liu start -->
   <insert id="insertTaskProductInfoBatch" parameterType="int">
  	INSERT INTO
    	wx_task_product_info
  	SELECT
		task_main.task_main_id[main_task_id],task_sub.task_id[sub_task_id],t_product.sku[sku],t_product.name[name],t_product.price[price]
		,t_product.sale_price[sale_price],t_inventory.quantity[quantity],0[replenish_quantity],getdate()[create_time],getdate()[update_time],task_main.create_user[create_user],t_product.remark
		FROM wx_task_sub task_sub
		INNER
		JOIN
		wx_task_main task_main
		ON task_sub.task_main_id = task_main.task_main_id
		LEFT 
		JOIN
		wx_t_product t_product
		ON 1 =1 
		left
		JOIN
		wx_t_inventory t_inventory
		ON
		t_inventory.workshop_id = task_sub.org_id AND t_product.sku = t_inventory.sku
	WHERE 
		task_main.task_main_id =  #{taskMainId,jdbcType=BIGINT}
		AND
		t_product.is_competing = 0
		AND
		t_product.is_collect = 1
  </insert>
  
  
   <!-- 任务实例表选项卡-竞品信息 -->
  <select id="getProductsInfoBySubTaskId" parameterType="map" resultMap="BaseResultMap">
   	SELECT
		t_product_info.*,t_product.category[category],t_product.brand[brand],
		(select att_file.att_id from wx_att_file att_file where att_file.source_id = t_product.id and source_type = '5') as icon_id,
	    (select di.dic_item_name from wx_t_dic_item di where di.dic_type_code = 'product.category' and di.dic_item_code = t_product.category) as category_desc
	FROM
		wx_task_product_info t_product_info
	LEFT
	JOIN
		wx_t_product t_product
	ON
		t_product_info.sku = t_product.sku
	WHERE
		t_product_info.sub_task_id = #{subTaskId,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteTaskProductInfoByMainTaskId" parameterType="java.lang.Long" >
    delete from wx_task_product_info
    where main_task_id = #{mainTaskId,jdbcType=BIGINT}
  </delete>
  
  <!-- end -->
  
  <delete id="deleteBySubTaskId" parameterType="string" >
    delete from wx_task_product_info
    where sub_task_id in (${_parameter})
  </delete>
  
  
  <!-- add by bo.liu 1102 获取需要录入到任务产品表中的产品信息 -->
  <select id="getNeedInsertTaskProducts" parameterType="map" resultMap="BaseResultMap">
     SELECT
        task_main.task_main_id[main_task_id],task_sub.task_id[sub_task_id],t_product.sku[sku],t_product.name[name],t_product.price[price]
        ,t_product.sale_price[sale_price],t_inventory.quantity[quantity],0[replenish_quantity],getdate()[create_time],getdate()[update_time],task_main.create_user[create_user],t_product.remark
        FROM wx_task_sub task_sub
        INNER
        JOIN
        wx_task_main task_main
        ON task_sub.task_main_id = task_main.task_main_id
        LEFT 
        JOIN
        wx_t_product t_product
        ON 1 =1 
        left
        JOIN
        wx_t_inventory t_inventory
        ON
        t_inventory.workshop_id = task_sub.org_id AND t_product.sku = t_inventory.sku
    WHERE 
        task_main.task_main_id =  #{taskMainId,jdbcType=BIGINT}
        AND
        t_product.is_competing = 0
        AND
        t_product.is_collect = 1
  </select>
  
  <!-- add by bo.liu 1102 插入到任务产品信息表中 -->
   <insert id="insertTaskProductsBatch" parameterType="java.util.List">
    insert into wx_task_product_info (main_task_id, sub_task_id, 
	      sku, name, price, 
	      sale_price, quantity, replenish_quantity, 
	      create_time, update_time, creator, 
	      remark)
    values 
        <foreach collection="list" index="index" item="item"
            separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
          #{item.mainTaskId,jdbcType=BIGINT}, #{item.subTaskId,jdbcType=BIGINT}, 
          #{item.sku,jdbcType=NVARCHAR}, #{item.name,jdbcType=NVARCHAR}, #{item.price,jdbcType=NUMERIC}, 
          #{item.salePrice,jdbcType=NUMERIC}, #{item.quantity,jdbcType=INTEGER}, #{item.replenishQuantity,jdbcType=INTEGER}, 
          #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=NVARCHAR}, 
          #{item.remark,jdbcType=NVARCHAR}
            </trim>
        </foreach>
    </insert>
</mapper>