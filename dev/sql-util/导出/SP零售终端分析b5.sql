select o.id, o.organization_name, 'i001' i, convert(nvarchar(20),a4.cou) v4, convert(nvarchar(20),a5.cou) v5,
convert(nvarchar(20),a6.cou) v6, convert(nvarchar(20),a7.cou) v7, convert(nvarchar(20),a8.cou) v8, convert(nvarchar(20),a9.cou) v9
, convert(nvarchar(20),a10.cou) v10 into #t001 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-05-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-05-01'))
group by wp.partner_id
) a4 on o.id=a4.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-06-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-06-01'))
group by wp.partner_id
) a5 on o.id=a5.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-07-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-07-01'))
group by wp.partner_id
) a6 on o.id=a6.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-08-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-08-01'))
group by wp.partner_id
) a7 on o.id=a7.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-09-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-09-01'))
group by wp.partner_id
) a8 on o.id=a8.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-10-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-10-01'))
group by wp.partner_id
) a9 on o.id=a9.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-11-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-11-01'))
group by wp.partner_id
) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'

select o.id, o.organization_name, 'i003' i, convert(nvarchar(20), a4.cou) v4, convert(nvarchar(20), a5.cou) v5,
convert(nvarchar(20), a6.cou) v6,convert(nvarchar(20), a7.cou) v7,convert(nvarchar(20), a8.cou) v8,convert(nvarchar(20), a9.cou) v9
,convert(nvarchar(20), a10.cou) v10 into #t003 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-05-01')
group by wp.partner_id
) a4 on o.id=a4.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) 
and wto.work_shop_id=w.id AND wto.create_time >= '2020-05-01' AND wto.create_time < '2020-06-01') group by wp.partner_id
) a5 on o.id=a5.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) 
and wto.work_shop_id=w.id AND wto.create_time >= '2020-06-01' AND wto.create_time < '2020-07-01') group by wp.partner_id
) a6 on o.id=a6.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) 
and wto.work_shop_id=w.id AND wto.create_time >= '2020-07-01' AND wto.create_time < '2020-08-01') group by wp.partner_id
) a7 on o.id=a7.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) 
and wto.work_shop_id=w.id AND wto.create_time >= '2020-08-01' AND wto.create_time < '2020-09-01') group by wp.partner_id
) a8 on o.id=a8.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) 
and wto.work_shop_id=w.id AND wto.create_time >= '2020-09-01' AND wto.create_time < '2020-10-01') group by wp.partner_id
) a9 on o.id=a9.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) 
and wto.work_shop_id=w.id AND wto.create_time >= '2020-10-01' AND wto.create_time < '2020-11-01') group by wp.partner_id
) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'

select o.id, o.organization_name, 'i007' i, convert(nvarchar(20), a4.cou) v4, convert(nvarchar(20), a5.cou) v5,
convert(nvarchar(20), a6.cou) v6, convert(nvarchar(20), a7.cou) v7, convert(nvarchar(20), a8.cou) v8, convert(nvarchar(20), a9.cou) v9
, convert(nvarchar(20), a10.cou) v10 into #t007 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-05-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-05-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-05-01'))
group by wp.partner_id
) a4 on o.id=a4.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-06-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-06-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-06-01'))
group by wp.partner_id
) a5 on o.id=a5.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-07-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-07-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-07-01'))
group by wp.partner_id
) a6 on o.id=a6.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-08-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-08-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-08-01'))
group by wp.partner_id
) a7 on o.id=a7.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-09-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-09-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-09-01'))
group by wp.partner_id
) a8 on o.id=a8.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-10-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-10-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-10-01'))
group by wp.partner_id
) a9 on o.id=a9.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-11-01') and
((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-11-01'
or exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-11-01'))
group by wp.partner_id
) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'

select o.id, o.organization_name, 'i008' i, convert(nvarchar(20), a4.cou) v4, convert(nvarchar(20), a5.cou) v5,
convert(nvarchar(20), a6.cou) v6, convert(nvarchar(20), a7.cou) v7, convert(nvarchar(20), a8.cou) v8, convert(nvarchar(20), a9.cou) v9
, convert(nvarchar(20), a10.cou) v10 into #t008 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-05-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id
AND wto.create_time >= '2020-04-01' AND wto.create_time < '2020-05-01')
group by wp.partner_id
) a4 on o.id=a4.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-06-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id 
AND wto.create_time >= '2020-05-01' AND wto.create_time < '2020-06-01') group by wp.partner_id
) a5 on o.id=a5.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-07-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id 
AND wto.create_time >= '2020-06-01' AND wto.create_time < '2020-07-01') group by wp.partner_id
) a6 on o.id=a6.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-08-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id 
AND wto.create_time >= '2020-07-01' AND wto.create_time < '2020-08-01') group by wp.partner_id
) a7 on o.id=a7.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-09-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id 
AND wto.create_time >= '2020-08-01' AND wto.create_time < '2020-09-01') group by wp.partner_id
) a8 on o.id=a8.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-10-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id 
AND wto.create_time >= '2020-09-01' AND wto.create_time < '2020-10-01') group by wp.partner_id
) a9 on o.id=a9.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and (w.shop_recruitment=1 or w.recruitment_amount>0)
and (w.shop_recruitment_update_time is null or w.shop_recruitment_update_time<'2020-11-01') and
exists (select 1 from wx_t_order wto where wto.status IN (10, 11) and wto.work_shop_id=w.id 
AND wto.create_time >= '2020-10-01' AND wto.create_time < '2020-11-01') group by wp.partner_id
) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'


select x1.id, x1.organization_name "经销商名称", x2.itext "零售终端项目", x1.v4 "4月", x1.v5 "5月",
x1.v6 "6月", x1.v7 "7月", x1.v8 "8月", x1.v9 "9月", x1.v10 "10月" from (
select * from #t001
union all 

select o.id, o.organization_name, 'i002' i, convert(nvarchar(20), a4.cou) v4, convert(nvarchar(20), a5.cou) v5,
convert(nvarchar(20), a6.cou) v6, convert(nvarchar(20), a7.cou) v7, convert(nvarchar(20), a8.cou) v8, convert(nvarchar(20), a9.cou) v9
, convert(nvarchar(20), a10.cou) v10 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-05-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-04-01'
group by wp.partner_id
) a4 on o.id=a4.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-06-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-05-01'
group by wp.partner_id
) a5 on o.id=a5.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-07-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-06-01'
group by wp.partner_id
) a6 on o.id=a6.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-08-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-07-01'
group by wp.partner_id
) a7 on o.id=a7.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-09-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-08-01'
group by wp.partner_id
) a8 on o.id=a8.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-10-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-09-01'
group by wp.partner_id
) a9 on o.id=a9.partner_id
left join (
select wp.partner_id, count(1) cou from wx_t_work_shop w 
left join wx_T_workshop_partner wp on w.id=wp.workshop_id
where w.delete_flag=0 and  w.from_source=1 and
(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')<'2020-11-01'
and (select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id and ws.workshop_with_status='3')>='2020-10-01'
group by wp.partner_id
) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'
union all 

select * from #t003
union all

select #t001.id, #t001.organization_name, 'i004' i, 
case when convert(float, #t001.v4)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v4)/convert(float, #t001.v4) * 100)) +'%' end v4,
case when convert(float, #t001.v5)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v5)/convert(float, #t001.v5) * 100)) +'%' end v5,
case when convert(float, #t001.v6)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v6)/convert(float, #t001.v6) * 100)) +'%' end v6,
case when convert(float, #t001.v7)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v7)/convert(float, #t001.v7) * 100)) +'%' end v7,   
case when convert(float, #t001.v8)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v8)/convert(float, #t001.v8) * 100)) +'%' end v8,   
case when convert(float, #t001.v9)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v9)/convert(float, #t001.v9) * 100)) +'%' end v9,   
case when convert(float, #t001.v10)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t003.v10)/convert(float, #t001.v10) * 100)) +'%' end v10     
from #t001 left join #t003 on #t001.id=#t003.id
union all 

select o.id, o.organization_name, 'i005' i, convert(nvarchar(20), a4.v) v4, convert(nvarchar(20), a5.v) v5,
convert(nvarchar(20), a6.v) v6,convert(nvarchar(20), a7.v) v7,convert(nvarchar(20), a8.v) v8,convert(nvarchar(20), a9.v) v9
,convert(nvarchar(20), a10.v) v10 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-04-01'
	AND wto.create_time < '2020-05-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a4 on o.id=a4.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-05-01'
	AND wto.create_time < '2020-06-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a5 on o.id=a5.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-06-01'
	AND wto.create_time < '2020-07-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a6 on o.id=a6.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-07-01'
	AND wto.create_time < '2020-08-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a7 on o.id=a7.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-08-01'
	AND wto.create_time < '2020-09-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a8 on o.id=a8.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-09-01'
	AND wto.create_time < '2020-10-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a9 on o.id=a9.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-10-01'
	AND wto.create_time < '2020-11-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'
union all

select o.id, o.organization_name, 'i006' i, convert(nvarchar(20), a4.v) v4, convert(nvarchar(20), a5.v) v5,
convert(nvarchar(20), a6.v) v6, convert(nvarchar(20), a7.v) v7, convert(nvarchar(20), a8.v) v8, convert(nvarchar(20), a9.v) v9
, convert(nvarchar(20), a10.v) v10 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-04-01'
	AND wto.create_time < '2020-05-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a4 on o.id=a4.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-05-01'
	AND wto.create_time < '2020-06-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a5 on o.id=a5.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-06-01'
	AND wto.create_time < '2020-07-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a6 on o.id=a6.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-07-01'
	AND wto.create_time < '2020-08-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a7 on o.id=a7.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-08-01'
	AND wto.create_time < '2020-09-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a8 on o.id=a8.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-09-01'
	AND wto.create_time < '2020-10-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a9 on o.id=a9.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-10-01'
	AND wto.create_time < '2020-11-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'
union all 

select * from #t007
union all 

select * from #t008
union all

select #t007.id, #t007.organization_name, 'i009' i, 
case when convert(float, #t007.v4)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v4)/convert(float, #t007.v4) * 100)) +'%' end v4,
case when convert(float, #t007.v5)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v5)/convert(float, #t007.v5) * 100)) +'%' end v5,
case when convert(float, #t007.v6)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v6)/convert(float, #t007.v6) * 100)) +'%' end v6,
case when convert(float, #t007.v7)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v7)/convert(float, #t007.v7) * 100)) +'%' end v7,   
case when convert(float, #t007.v8)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v8)/convert(float, #t007.v8) * 100)) +'%' end v8,   
case when convert(float, #t007.v9)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v9)/convert(float, #t007.v9) * 100)) +'%' end v9,   
case when convert(float, #t007.v10)=0 then null 
	else CONVERT(varchar(10),CONVERT(decimal(10, 2), convert(float, #t008.v10)/convert(float, #t007.v10) * 100)) +'%' end v10   
from #t007 left join #t008 on #t007.id=#t008.id
union all 

select o.id, o.organization_name, 'i010' i, convert(nvarchar(20), a4.v) v4, convert(nvarchar(20), a5.v) v5,
convert(nvarchar(20), a6.v) v6,convert(nvarchar(20), a7.v) v7,convert(nvarchar(20), a8.v) v8,convert(nvarchar(20), a9.v) v9
,convert(nvarchar(20), a10.v) v10 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-05-01') and
ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-04-01'
	AND wto.create_time < '2020-05-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a4 on o.id=a4.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-06-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-05-01'
	AND wto.create_time < '2020-06-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a5 on o.id=a5.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-07-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-06-01'
	AND wto.create_time < '2020-07-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a6 on o.id=a6.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-08-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-07-01'
	AND wto.create_time < '2020-08-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a7 on o.id=a7.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-09-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-08-01'
	AND wto.create_time < '2020-09-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a8 on o.id=a8.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-10-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-09-01'
	AND wto.create_time < '2020-10-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a9 on o.id=a9.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-11-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-10-01'
	AND wto.create_time < '2020-11-01'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'
union all

select o.id, o.organization_name, 'i011' i, convert(nvarchar(20), a4.v) v4, convert(nvarchar(20), a5.v) v5,
convert(nvarchar(20), a6.v) v6,convert(nvarchar(20), a7.v) v7,convert(nvarchar(20), a8.v) v8,convert(nvarchar(20), a9.v) v9
,convert(nvarchar(20), a10.v) v10 from wx_t_organization o
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0) 
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-05-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-04-01'
	AND wto.create_time < '2020-05-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a4 on o.id=a4.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-06-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-05-01'
	AND wto.create_time < '2020-06-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a5 on o.id=a5.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-07-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-06-01'
	AND wto.create_time < '2020-07-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a6 on o.id=a6.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-08-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-07-01'
	AND wto.create_time < '2020-08-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a7 on o.id=a7.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-09-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-08-01'
	AND wto.create_time < '2020-09-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a8 on o.id=a8.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-10-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-09-01'
	AND wto.create_time < '2020-10-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a9 on o.id=a9.partner_id
 left join (
 select h1.partner_id, AVG(h1.v) v from (
SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id, wp.partner_id
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11) AND (ws.shop_recruitment=1 or ws.recruitment_amount>0)
and (ws.shop_recruitment_update_time is null or ws.shop_recruitment_update_time<'2020-11-01') 
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-10-01'
	AND wto.create_time < '2020-11-01'
	and tp.product_property='SN Above'
GROUP BY wto.work_shop_id, wp.partner_id) h1 group by h1.partner_id) a10 on o.id=a10.partner_id
where o.status=1 and o.type=1 and o.id != 9
and pe.partner_property='NORMAL'
) x1
left join (select 'i001' I, 'PP系统门店总数' itext
union all select 'i002' I, '新建门店数' itext
union all select 'i003' I, '每月订货门店数' itext
union all select 'i004' I, '订货门店占比 VS 门店总数' itext
union all select 'i005' I, '每月订货门店平均销量' itext
union all select 'i006' I, '每月订货门店平均SN销量' itext
union all select 'i007' I, '金富力门头门店总数' itext
union all select 'i008' I, '每月订货金富力门头门店数' itext
union all select 'i009' I, '订货门头门店占比VS门头总数' itext
union all select 'i010' I, '每月订货金富力门头门店平均销量' itext
union all select 'i011' I, '每月订货金富力门头门店平均SN销量' itext	) x2 on x2.I=x1.i
order by x1.id, x2.I
