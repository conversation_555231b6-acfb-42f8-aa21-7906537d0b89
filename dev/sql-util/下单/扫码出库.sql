--select sum() from (
SELECT osp.actual_out_count * CONVERT(FLOAT, tp.capacity) AS liters,osp.actual_out_count 
       , wto.work_shop_id,ws.partner_id,wto.create_time,tp.sku,wto.order_no
FROM wx_t_order wto
       LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
          left join wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
       LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
       LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
       LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
       AND ws.delete_flag = 0 and ws.partner_id=56759 and wto.create_time>='2021-03-01'
	   --order by wto.order_no
--) 