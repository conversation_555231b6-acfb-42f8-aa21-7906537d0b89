/*非本地营销升数扣减上报提示数据*/
select distinct CONVERT(varchar(100), nff.req_time, 23) report_date,nr1.reprot_type report_type, 
d1.dic_item_name report_type_text, isnull(c.region_name, '未知') city_name, isnull(c.id, -1) city_id, 
isnull(p.region_name, '未知') province_name, isnull(p.id,-1) province_id, 
dbo.fun_get_nonlocal_punish_desc(ir3.id) punish_desc,
case when ir3.attribute1='1' then 1 else 0 end punish_flag,np1.product_batch,np1.sku,
nff.to_distributor_id,isnull(ISNULL(dic1.dic_item_name,cs.customer_name_cn), '-') to_distributor_name/*indirect经销商或OEM或GQ或扫码验真*/,
year(nff.req_time) report_year, year(eic.create_time) confirm_year, #VARCHAR,year# punish_year
from wx_t_non_local_mkt_flow_form nff 
left join wx_t_loc_mkt_product_info np1 on np1.flow_id=nff.id
left join wx_t_non_local_mkt_flow_pro_res ir3 on ir3.flow_id=nff.id and ir3.setp_no='ABM_CONFIRM' and ir3.channel=np1.auth_channel
left join wx_t_main_local_marketing nr1 on nr1.id=nff.report_id
left join wx_t_dic_item d1 on d1.dic_item_code = nr1.reprot_type and d1.dic_type_code  = 'Non.Loc.From.Source'
left join wx_t_region c on c.id = nr1.report_region_id
left join wx_t_region p on p.id = c.parent_id
left join wx_t_dic_item dic1 on cast(dic1.dic_item_code as bigint) = nff.to_distributor_id and dic1.dic_type_code = 'non_local_special_partner'
left join dw_customer_org_sales cs on cs.distributor_id = nff.to_distributor_id and cs.channel_weight&(case when np1.auth_channel&3=2 then 2 else 1 end)>0
left join wx_t_non_local_mkt_emaile_info eic on eic.source_id=ir3.id and not exists (select 1 from wx_t_non_local_mkt_emaile_info ei1 where ei1.source_id=ir3.id and ei1.id<eic.id)
where exists (select 1 from wx_t_non_local_mkt_flow_form nff1 where nff1.id=#BIGINT,flowId# and nff1.from_distributor_id=nff.from_distributor_id) 
and ((nff.req_time>=#VARCHAR,year# + '-01-01' and nff.req_time<DATEADD(year, 1, #VARCHAR,year# + '-01-01'))
	or (eic.create_time>=#VARCHAR,year# + '-01-01' and eic.create_time<DATEADD(year, 1, #VARCHAR,year# + '-01-01'))) 
and np1.auth_channel=#INTEGER,authChannel#
union all
select distinct CONVERT(varchar(100), t1.report_time, 23) report_date,2 report_type, 
(select d1.dic_item_name from wx_t_dic_item d1 where d1.dic_item_code = '2' and d1.dic_type_code  = 'Non.Loc.From.Source') report_type_text, 
isnull(c.region_name, '未知') city_name, isnull(c.id, -1) city_id, 
isnull(p.region_name, '未知') province_name, isnull(p.id,-1) province_id, '' punish_desc,
0 punish_flag,t1.product_batch,t1.sku,
null to_distributor_id,'-' to_distributor_name,
year(t1.report_time) report_year, null confirm_year, #VARCHAR,year# punish_year
 from wx_t_non_local_user_report t1
 left join dw_customer_org_sales dc002 on dc002.distributor_id=t1.from_distributor_id and dc002.channel_weight&t1.auth_channel>0
 left join wx_t_dic_item s1 on cast(s1.dic_item_code as bigint) = t1.from_distributor_id and s1.dic_type_code = 'non_local_special_partner'
left join wx_t_region c on c.id = t1.report_region_id
left join wx_t_region p on p.id = c.parent_id
 where t1.ext_flag&2048>0 and
 exists (select 1 from wx_t_non_local_mkt_flow_form nff1 where nff1.id=#BIGINT,flowId# and nff1.from_distributor_id=t1.from_distributor_id) 
and t1.report_time>=#VARCHAR,year# + '-01-01' 
and t1.report_time<DATEADD(year, 1, #VARCHAR,year# + '-01-01') 
and t1.auth_channel=#INTEGER,authChannel#
