package com.sys.auth.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.common.base.BaseController;
import com.common.util.HttpSender;
import com.sys.auth.dao.WxCompanyInfoMapper;
import com.sys.auth.model.WxCompanyInfo;
import com.sys.auth.service.WxUserServiceI;
import com.sys.auth.service.impl.WxCompanyInfoService;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.chevron.userselect.service.impl.OrgTreeService;

@Controller
public class CompanyRegister extends BaseController{
	@Resource
	WxCompanyInfoService wxCompanyInfoService;
	@Resource
	WxUserServiceI wxUserService;
	@Resource
	WxRoleServiceImpl wxRoleService;
	@Resource
	OrgTreeService orgTreeService;
	@Resource
	WxCompanyInfoMapper wxCompanyInfoMapper ;
	
	@ResponseBody
	@RequestMapping(value = "/companyRegister.do", method = RequestMethod.POST)
	public Object registerCompanyInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
		HttpSession session = request.getSession();
        String companyAccount = (String)request.getParameter("companyAccount");
        String companyName = (String)request.getParameter("companyName");
        String password = (String)request.getParameter("password");
        String mobile = (String) session.getAttribute("mobile");
        //插入企业信息记录
        WxCompanyInfo ci = wxCompanyInfoService.insert(mobile, companyAccount, companyName, null);
        //插入默认组织信息
        orgTreeService.createNewOrg(new Long(0), "总部", "总部", "HQ", "BRANCH", new Date(), null, ci.getTenantId());
        //插入用户角色信息
        Long roleId = wxRoleService.createCompanyAdmin(companyName, ci.getTenantId());
        //插入用户信息记录
        wxUserService.createWxTUser(companyAccount, companyAccount, companyName, null, "T", null, null, null, null, mobile, null, null, null, null, null, null, null, null, password, ci.getTenantId(), roleId,String.valueOf(roleId));
        request.setAttribute("password", password);
        request.setAttribute("loginname", companyAccount);
        return new ModelAndView("testExcelLoad");
    }
	
	@ResponseBody
	@RequestMapping(value = "/sendMSG.do", method = RequestMethod.POST)
	public Object sendMSG(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> returnMap = new HashMap<String, Object>();
		HttpSession session = request.getSession();
		String url = "http://222.73.117.158/msg/HttpBatchSendSM";// 应用地址
		//account和pswd需要询问
		String account = "jiekou-clcs-06";// 账号
		String pswd = "Txh520888";// 密码
        String last_identifying_time = (String)session.getAttribute("last_identifying_time");
        String identifying_code = (String)session.getAttribute("identifying_code");
		if(last_identifying_time == null || (new Date().getTime() - Long.parseLong(last_identifying_time)) > 180000){
			int temp = (int)(Math.random() * 1000000);
			identifying_code = (temp < 100000)?"0":"" + temp;
		}
		String mobile = (String)request.getParameter("mobile");// 手机号码，多个号码使用","分割
		String msg = "亲爱的用户，您的验证码是" + identifying_code + "，3分钟内有效。";// 短信内容
		boolean needstatus = true;// 是否需要状态报告，需要true，不需要false
		String product = null;// 产品ID
		String extno = null;// 扩展码
		
		if(wxCompanyInfoMapper.selCompanyByMobile(mobile).size() > 0){
			returnMap.put("status", "error");
			returnMap.put("error", "该手机已经注册");
			return returnMap;
		}
		
		try {
			String returnString = HttpSender.batchSend(url, account, pswd, mobile, msg, needstatus, product, extno);
			String status = returnString.split("\n")[0].split(",")[1];
			if(status.equals("0")){
				session.setAttribute("mobile", mobile);
				session.setAttribute("identifying_code", "" + identifying_code);
				session.setAttribute("last_identifying_time", "" + new Date().getTime());
			}
			returnMap.put("status", status);
			
//			System.out.println(returnString);
			// TODO 处理返回值,参见HTTP协议文档
		} catch (Exception e) {
			// TODO 处理异常
			e.printStackTrace();
		}
		return returnMap;
    }
	
	@ResponseBody
	@RequestMapping(value = "/checkAccount.do", method = RequestMethod.POST)
	public Object checkAccount(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		HttpSession session = request.getSession();
        String account = (String)request.getParameter("companyAccount");
        String mobile = (String)session.getAttribute("mobile");
        if(wxCompanyInfoMapper.selCompanyByAccount(account).size() == 0){
        	if(wxCompanyInfoMapper.selCompanyByMobile(mobile).size() == 0){
    			map.put("status", "success");
    		}
    		else {
    			map.put("status", "error");
    			map.put("error", "该手机已经被注册");
    		}
		}
		else{
			map.put("status", "error");
			map.put("error", "该帐号已经被注册");
		}
		return map;
	}
	
	@ResponseBody
	@RequestMapping(value = "/confirmMobile.do", method = RequestMethod.POST)
	public Object confirmMobile(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		HttpSession session = request.getSession();
        String mobile = (String)request.getParameter("mobile");
        String identifying_code = (String)request.getParameter("identifying_code");
        String last_identifying_time = session.getAttribute("last_identifying_time").toString();
		if(identifying_code.equals((String)session.getAttribute("identifying_code"))){
			if((new Date().getTime() - Long.parseLong(last_identifying_time) <= 180000)){
				if(mobile.equals((String)session.getAttribute("mobile"))){
					map.put("status", "success");
				}
				else{
					map.put("status", "error");
					map.put("error", "手机号错误");
				}
			}
			else{
				map.put("status", "error");
				map.put("error", "验证码已超时");
			}
		}else{
			map.put("status", "error");
			map.put("error", "验证码错误");
		}
		return map;
	}
	
}
