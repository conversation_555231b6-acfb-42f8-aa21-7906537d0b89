package com.common.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.servlet.http.HttpServletResponse;

import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileInputStream;
import jcifs.smb.SmbFileOutputStream;

import org.apache.commons.io.FileUtils;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.sys.file.web.FileManager;

public class FileUtil {

	private static Logger log = LoggerFactory.getLogger(FileUtil.class);
	public static final String FILE_SEPARATOR = System.getProperty("file.separator");

	/**
	 * path 以 / 结尾
	 * 
	 * @param
	 * @param path
	 * @param saveFileName
	 * @return
	 * @throws IOException
	 * <AUTHOR> 2014年11月27日 下午5:22:55
	 */
	public static boolean saveFile(MultipartFile infile, String path, String saveFileName) throws IOException {
		if (!path.endsWith(File.separator)) {
			path = path + File.separator;
		}

		File dir = new File(path);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		if (infile.getOriginalFilename().indexOf(".") == -1) {
			return false;
		}
		OutputStream f2 = new FileOutputStream(
				path + saveFileName + "." + infile.getOriginalFilename().split("\\.")[1]);
		FileCopyUtils.copy(infile.getInputStream(), f2);
		return true;
	}
	
	public static void download(HttpServletResponse response, String sourceType, String contentType, boolean inline, 
			String filePath, String fileName)
			throws IOException {
		InputStream inStream = null;
		OutputStream outStream = null;
		// 缓冲文件输入流
		BufferedInputStream bufferedInputStream = null;
		// 缓冲文件输出流
		BufferedOutputStream bufferedOutputStream = null;
		try {
			if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
				inStream = new FileInputStream(filePath);
			} else {
				SmbFile remoteFile = new SmbFile(filePath);
				remoteFile.connect();
				inStream = new SmbFileInputStream(remoteFile);
			}

			if (inStream != null) {
				// 设置输出的格式
				response.reset();
//				if(!inline && StringUtils.isNotBlank(contentType)) {
//					response.setContentType(contentType);
//				}else {
					if(inline && StringUtils.isNotBlank(contentType)) {
						response.setContentType(contentType);
					}else {
						response.setContentType("applicatoin/octet-stream");
					}
					response.setCharacterEncoding("UTF-8");
					String filename = new String(URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));
					// new String(fileName.getBytes("utf-8"),"ISO8859-1");
					response.addHeader("Content-Disposition", (inline ? "inline" : "attachment") + "; filename=\"" + filename + "\"");
//				}
				outStream = response.getOutputStream();
				// 循环取出流中的数据
				byte[] b = new byte[4096];
				int len;
				while ((len = inStream.read(b)) != -1) {
					outStream.write(b, 0, len);
				}
				/* inStream.close(); */
				/*
				 * //缓冲文件输入流 bufferedInputStream = new BufferedInputStream(inStream); //缓冲文件输出流
				 * bufferedOutputStream = new BufferedOutputStream(outStream);
				 * 
				 * int i = 0; //缓冲区的大小 byte[] buffer = new byte[512];
				 * 
				 * while(true) { if(bufferedInputStream.available() < 512) { while(i != -1) { i
				 * = bufferedInputStream.read(); bufferedOutputStream.write(i); } break; } else
				 * { //当文件的大小还大于512字节时 bufferedInputStream.read(buffer);
				 * bufferedOutputStream.write(buffer);
				 * 
				 * } }
				 * 
				 * //流的关闭 bufferedOutputStream.flush();//强制清除缓冲区的内容
				 */
			}

		} catch (IOException e) {
			log.error("处理下载文件时发生IO异常：{}", e.getMessage());
		} finally {
			/*
			 * if(bufferedInputStream != null){ bufferedInputStream.close(); }
			 * if(bufferedOutputStream != null){ bufferedOutputStream.close(); }
			 */
			if (inStream != null) {
				inStream.close();
			}
			if (outStream != null) {
				outStream.close();
			}
		}
	}

	/**
	 * 从本地下载文件。将文件放入response，返回给客户端浏览器
	 * 
	 * @param response 页面响应
	 * @param filePath 文件所在的绝对路径（包括文件名）本地硬盘上
	 * @param fileName 文件名称
	 * @throws IOException IO异常
	 */
	public static void download(HttpServletResponse response, String sourceType, boolean inline, String filePath, String fileName)
			throws IOException {
		download(response, sourceType, null, inline, filePath, fileName);
	}
	
	public static void download(HttpServletResponse response, String sourceType, String filePath, String fileName)
			throws IOException {
		download(response, sourceType, null, false, filePath, fileName);
	}
	
	public static void download(HttpServletResponse response, String sourceType, String contentType, 
			String filePath, String fileName) throws IOException {
		download(response, sourceType, contentType, true, filePath, fileName);
	}
	
	public static void downloadByThumbnail(HttpServletResponse response, String sourceType, String filePath, String fileName, int imgWidth)
			throws IOException {
		InputStream inStream = null;
		try {
			if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
				inStream = new FileInputStream(filePath);
			} else {
				SmbFile remoteFile = new SmbFile(filePath);
				remoteFile.connect();
				inStream = new SmbFileInputStream(remoteFile);
			}

			if (inStream != null) {
				// 设置输出的格式
				response.reset();
				response.setContentType("image/jpeg");
				response.setCharacterEncoding("UTF-8");
				String filename = new String(URLEncoder.encode(fileName, "UTF-8"));
				// new String(fileName.getBytes("utf-8"),"ISO8859-1");
//				response.addHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
				ThumbnailUtil thumbnailUtil = new ThumbnailUtil();
				thumbnailUtil.saveImageAsThumbnailJpg(inStream, response.getOutputStream(), imgWidth, imgWidth);
			}

		} catch (Exception e) {
			log.error("处理下载文件时发生IO异常", e);
		} finally {
			if (inStream != null) {
				inStream.close();
			}
		}
	}

	public static void showImgToBrowser(HttpServletResponse response, String sourceType, String filePath,
			String fileName) throws IOException {
		InputStream inStream = null;
		OutputStream outStream = null;
		// 缓冲文件输入流
		BufferedInputStream bufferedInputStream = null;
		// 缓冲文件输出流
		BufferedOutputStream bufferedOutputStream = null;
		try {
			if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
				inStream = new FileInputStream(filePath);
			} else {
				SmbFile remoteFile = new SmbFile(filePath);
				remoteFile.connect();
				inStream = new SmbFileInputStream(remoteFile);
			}

			if (inStream != null) {
				// 设置输出的格式
				response.reset();
				response.setContentType("image/jpeg");
				response.setCharacterEncoding("UTF-8");
				String filename = new String(URLEncoder.encode(fileName, "UTF-8"));
				outStream = response.getOutputStream();
				// 循环取出流中的数据
				byte[] b = new byte[4096];
				int len;
				while ((len = inStream.read(b)) != -1) {
					outStream.write(b, 0, len);
				}
			}

		} catch (IOException e) {
			log.error("处理下载文件时发生IO异常", e);
		} finally {
			if (inStream != null) {
				inStream.close();
			}
			if (outStream != null) {
				outStream.close();
			}
		}
	}

	public static void download(HttpServletResponse response, InputStream inputStream, String fileName)
			throws IOException {
		OutputStream outStream = null;
		try {
			if (inputStream != null) {
				// 设置输出的格式
				CommonUtil.setExportResponseHeader(null, response, fileName, null);
				outStream = response.getOutputStream();
				// 循环取出流中的数据
				byte[] b = new byte[4096];
				int len;
				while ((len = inputStream.read(b)) != -1) {
					outStream.write(b, 0, len);
				}
			}
		} catch (IOException e) {
			log.error("处理下载文件时发生IO异常", e);
		} finally {
			/*
			 * if(bufferedInputStream != null){ bufferedInputStream.close(); }
			 * if(bufferedOutputStream != null){ bufferedOutputStream.close(); }
			 */
			if (inputStream != null) {
				inputStream.close();
			}
			if (outStream != null) {
				outStream.close();
			}
		}
	}

	/**
	 * 从本地下载文件。返回流
	 * 
	 * @param filePath 文件所在的绝对路径（包括文件名）本地硬盘上
	 * @param fileName 文件名称
	 * @throws IOException IO异常
	 */
	public static InputStream download(String sourceType, String filePath, String fileName) throws IOException {
		InputStream inStream = null;
		try {
			if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
				inStream = new FileInputStream(filePath);
			} else {
				SmbFile remoteFile = new SmbFile(filePath);
				remoteFile.connect();
				inStream = new SmbFileInputStream(remoteFile);
			}

		} catch (IOException e) {
			log.error("处理下载文件时发生IO异常", e);
			throw new RuntimeException("处理下载文件时发生IO异常");
		}
		return inStream;
	}

	/**
	 * 获取文件的大小，单个文件不可能到T，所以暂不考虑这种情况。
	 * 
	 * @param fileSize
	 * @return
	 */
	public static String FormetFileSize(Long fileSize) {// 转换文件大小
		DecimalFormat df = new DecimalFormat("#.00");
		String fileSizeString = "";

		Long B = 1L; // 1024^0
		Long K = 1024L; // 1024^1
		Long M = 1048576L; // 1024^2
		Long G = 1073741824L; // 1024^3
		Long T = G * 1024;

		if (fileSize < K) {
			fileSizeString = df.format((double) fileSize / B) + "B";
		} else if (fileSize < M) {
			fileSizeString = df.format((double) fileSize / K) + "K";
		} else if (fileSize < G) {// 1024^3
			fileSizeString = df.format((double) fileSize / M) + "M";
		} else {
			fileSizeString = df.format((double) fileSize / G) + "G";
		}
		return fileSizeString;
	}

	/**
	 * 根据路径删除指定的目录或文件，无论存在与否
	 * 
	 * @param sPath 要删除的目录或文件
	 * @return 删除成功返回 true，否则返回 false。
	 */
	public static boolean deleteFolder(String sPath) {
		boolean flag = false;
		File file = new File(sPath);
		// 判断目录或文件是否存在
		if (!file.exists()) { // 不存在返回 false
			return flag;
		} else {
			// 判断是否为文件
			if (file.isFile()) { // 为文件时调用删除文件方法
				return FileUtil.deleteFile(sPath);
			} else { // 为目录时调用删除目录方法
				return FileUtil.deleteDirectory(sPath);
			}
		}
	}

	/**
	 * 删除单个文件
	 * 
	 * @param sPath 被删除文件的文件名
	 * @return 单个文件删除成功返回true，否则返回false
	 */
	public static boolean deleteFile(String sPath) {
		boolean flag = false;
		File file = new File(sPath);
		// 路径为文件且不为空则进行删除
		if (file.isFile() && file.exists()) {
			file.delete();
			flag = true;
		}
		return flag;
	}

	public static boolean deleteFileNew(String sPath, String filePath) {
		boolean flag = false;
		File file = new File(sPath);
		// 路径为文件且不为空则进行删除
		if (file.isFile() && file.exists()) {

			// 40X40缩略图
			String tmepFileName = ThumbnailUtil.THUMBNAIL_PREFIX + ThumbnailUtil.WIDTH_40 + file.getName();
			File tempFile = new File(filePath + tmepFileName);
			if (tempFile.isFile() && tempFile.exists()) {
				tempFile.delete();
			}

			// 200X200缩略图
//        	String tmepFileName2 = ThumbnailUtil.THUMBNAIL_PREFIX+ThumbnailUtil.WIDTH_200+file.getName();
//        	File tempFile2 = new File(filePath+tmepFileName2);
//        	if(tempFile2.isFile() && tempFile2.exists())
//        	{
//        		tempFile2.delete();
//        	}
			file.delete();
			flag = true;
		}
		return flag;
	}

	/**
	 * 删除单个文件
	 * 
	 * @param sPath 被删除文件的文件名
	 * @return 单个文件删除成功返回true，否则返回false
	 * @throws IOException
	 */
	public static boolean deleteRemoteFile(String remotePath) throws IOException {
		boolean flag = false;
		SmbFile file = new SmbFile(remotePath);
		file.connect();
		// 路径为文件且不为空则进行删除
		if (file.isFile() && file.exists()) {
			String tmepFileName = ThumbnailUtil.THUMBNAIL_PREFIX + ThumbnailUtil.WIDTH_40 + file.getName();
			File tempFile = new File(file.getParent() + System.getProperty("file.separator") + tmepFileName);
			if (tempFile.isFile() && tempFile.exists()) {
				tempFile.delete();
			}
			file.delete();
			flag = true;
		}
		return flag;
	}

	/**
	 * 删除目录（文件夹）以及目录下的文件
	 * 
	 * @param sPath 被删除目录的文件路径
	 * @return 目录删除成功返回true，否则返回false
	 */
	public static boolean deleteDirectory(String sPath) {
		// 如果sPath不以文件分隔符结尾，自动添加文件分隔符
		if (!sPath.endsWith(File.separator)) {
			sPath = sPath + File.separator;
		}
		File dirFile = new File(sPath);
		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!dirFile.exists() || !dirFile.isDirectory()) {
			return false;
		}
		boolean flag = true;
		// 删除文件夹下的所有文件(包括子目录)
		File[] files = dirFile.listFiles();
		for (int i = 0; i < files.length; i++) {
			// 删除子文件
			if (files[i].isFile()) {
				flag = FileUtil.deleteFile(files[i].getAbsolutePath());
				if (!flag)
					break;
			} // 删除子目录
			else {
				flag = FileUtil.deleteDirectory(files[i].getAbsolutePath());
				if (!flag)
					break;
			}
		}
		if (!flag)
			return false;
		// 删除当前目录
		if (dirFile.delete()) {
			return true;
		} else {
			return false;
		}
	}

	public static String getFolderName(int folderGrainSize) {
		GregorianCalendar today = new GregorianCalendar();
		DateFormat format = null;
		if (GregorianCalendar.YEAR == folderGrainSize) {
			format = new SimpleDateFormat("yyyy");
		} else if (GregorianCalendar.MONTH == folderGrainSize) {
			format = new SimpleDateFormat("yyyy-MM");
		} else if (GregorianCalendar.WEEK_OF_MONTH == folderGrainSize) {
			format = new SimpleDateFormat("yyyy-MM");
			return format.format(today.getTime()) + "-" + today.get(GregorianCalendar.WEEK_OF_MONTH);
		} else if (GregorianCalendar.DATE == folderGrainSize) {
			format = new SimpleDateFormat("yyyy-MM-dd");
		}
		return format.format(today.getTime());
	}

	public static void storeFile(String realPath, String storageName, InputStream fileInputStream, String sourceType,
			String fileType) throws Exception {
		storeFile(realPath, storageName, fileInputStream, sourceType, fileType, false);
	}

	public static void storeFile(String realPath, String storageName, InputStream fileInputStream, String sourceType,
			String fileType, boolean noThumbnail) throws Exception {
		if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
			File filePath = new File(realPath);
			if (!filePath.exists()) {
				filePath.mkdir();
			}
			File attFile = new File(filePath, storageName);
			if (!attFile.exists()) {
				FileUtils.copyInputStreamToFile(fileInputStream, attFile);
			}

			// add by bo.liu 05.08 生成缩略图,,前缀都是已thumbnail_
//			if(null!=fileType)  //如果反馈很慢，下次开启多线程进行处理
//			{
//				if(fileType.startsWith("image/"))
//				{
//					ThumbnailUtil thumbnailUtil = new ThumbnailUtil();
//					//生成40X40
//					File saveToFile = new File(filePath, ThumbnailUtil.THUMBNAIL_PREFIX+ThumbnailUtil.WIDTH_40+storageName);
//					thumbnailUtil.compressImageAsJpg(attFile, saveToFile, 40, 40);
//					//生成200X200
//					/*File saveToFile2 = new File(filePath, ThumbnailUtil.THUMBNAIL_PREFIX+ThumbnailUtil.WIDTH_200+storageName);
//					thumbnailUtil.compressImageAsJpg(attFile, saveToFile2, 200, 200);*/
//					
//				}
//			}
			// delete from by bo.liu 1116
			if (!noThumbnail && null != storageName && !storageName.isEmpty()) {
				System.out.println("FileUtil storeFile fileType:" + fileType);
				if (null != fileType && fileType.startsWith("image/")) {
					ThumbnailUtil thumbnailUtil = new ThumbnailUtil();
					Integer thumbnailWidth = FileManager.thumbnailWidthMap.get(sourceType);
					if (thumbnailWidth == null) {
						thumbnailWidth = 40;
					}
					try {
						// 生成40X40
						File saveToFile = new File(filePath,
								ThumbnailUtil.THUMBNAIL_PREFIX + ThumbnailUtil.WIDTH_40 + storageName);
						thumbnailUtil.compressImageAsJpg(attFile, saveToFile, thumbnailWidth, thumbnailWidth);
					} catch (Exception e) {
						log.warn("thumbnailUtil error. " + e.getMessage(), e);
					}
				}
			}
			// 生成缩略图end

		} else {
			InputStream in = null;
			OutputStream out = null;
			SmbFile remoteFilePath = new SmbFile(realPath);
			if (!remoteFilePath.exists()) {
				remoteFilePath.mkdir();
			}
			SmbFile remoteFile = new SmbFile(realPath + "/" + storageName);
			if (!remoteFile.exists()) {
				in = new BufferedInputStream(fileInputStream);
				out = new BufferedOutputStream(new SmbFileOutputStream(remoteFile));
				byte[] buffer = new byte[1024];
				while (in.read(buffer) != -1) {
					out.write(buffer);
					buffer = new byte[1024];
				}
			}
			if (out != null)
				out.close();
			if (in != null)
				in.close();
		}
	}

	public static void main(String[] args) {

		Long G = 1073741824L; // 1024^3
		Long T = G * 1024;

		System.out.println(T);
		System.out.println(FileUtil.FormetFileSize(G));
		System.out.println(FileUtil.FormetFileSize(T));
		System.out.println(".sdfsfs".indexOf("."));
		System.out.println("sdf.sfs".indexOf("."));
		System.out.println("sdfsfs.".indexOf("."));
		System.out.println("sdfsfs".indexOf("."));
		File f = new File("D:\\\\\\tt\\\\erro");
		f.mkdirs();
		String a = "aa";
		a += "b";
		System.out.println(a);

	}

	/**
	 * 根据文件内容进行下载
	 * 
	 * @param response
	 * @param fileName
	 * @param fileContent
	 * @throws IOException
	 */
	public static void download(HttpServletResponse response, String fileName, String fileContent) throws IOException {
		OutputStream outStream = null;
		try {
			// 设置输出的格式
			response.reset();
			response.setContentType("applicatoin/octet-stream");
			response.setCharacterEncoding("UTF-8");
			String filename = new String(fileName.getBytes("utf-8"), "ISO8859-1");
			response.addHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
			outStream = response.getOutputStream();
			outStream.write(fileContent.getBytes());
		} catch (IOException e) {
			log.error("处理下载文件时发生IO异常", e);
			throw new RuntimeException("处理下载文件时发生IO异常");
		} finally {
			if (outStream != null) {
				outStream.close();
			}
		}
	}

	/**
	 * 根据多文件内容进行下载(zip)
	 * 
	 * @param response
	 * @param zipFileName     压缩包文件名称
	 * @param fileNameList
	 * @param fileContentList
	 * @throws IOException
	 */
	public static void download(HttpServletResponse response, String zipFileName, Map<String, String> fileInfoMap)
			throws IOException {
		ZipOutputStream zos = null;
		try {
			// 设置输出的格式
			response.reset();
			response.setContentType("application/octet-stream;charset=UTF-8");
			response.setCharacterEncoding("UTF-8");
			/**
			 * 该方式在某些情况下无效，故更改为URL编码的方式 String filename = new
			 * String(zipFileName.getBytes("GBK"), "ISO-8859-1");
			 * response.addHeader("Content-Disposition", "attachment;filename=" + filename
			 * );
			 */
			response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
			zos = new ZipOutputStream(response.getOutputStream());
			for (Map.Entry<String, String> entry : fileInfoMap.entrySet()) {
				zos.putNextEntry(new ZipEntry(entry.getKey()));
				zos.write(entry.getValue().getBytes());
			}
		} catch (IOException e) {
			log.error("处理下载文件时发生IO异常", e);
			throw new RuntimeException("处理下载文件时发生IO异常");
		} finally {

			if (zos != null) {
				zos.close();
			}
		}
	}

	public static void createThumbnailImageByPath(String sPath, int width, int height) {
		// 如果sPath不以文件分隔符结尾，自动添加文件分隔符
		if (!sPath.endsWith(File.separator)) {
			sPath = sPath + File.separator;
		}
		File dirFile = new File(sPath);
		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!dirFile.exists() || !dirFile.isDirectory()) {
			System.out.println("-------------------createThumbnailImageByPath return:" + sPath);
			return;
		}
		try {
			List<String> fileNames = new ArrayList<String>();
			File[] files = dirFile.listFiles();
			for (File file : files) {
				if (file.isDirectory()) {
					// 这里将列出所有的文件夹
					createThumbnailImageByPath(file.getAbsolutePath(), width, height);
				} else {
					if ((file.getAbsolutePath().toLowerCase().endsWith(".jpg")
							|| file.getAbsolutePath().toUpperCase().endsWith(".JPG"))
							&& !file.getName().startsWith("thumbnail_")) {
						// System.out.println("parent==>" + file.getParent());
						System.out.println("createThumbnailImageByPath file==>" + file.getAbsolutePath());
						fileNames.add(file.getAbsolutePath());
					}
				}
			}
			ImportDataPageModelUtil allPage = new ImportDataPageModelUtil(fileNames, 50);
			int pageAccount = allPage.getTotalPages();
			for (int i = 1; i <= pageAccount; i++) {
				List<String> lstTmpPages = allPage.getObjects(i);
				for (String filePath : lstTmpPages) {

					ThumbnailUtil thumbnailUtil = new ThumbnailUtil();
					File sourceFile = new File(filePath);
					String partentPath = sourceFile.getParent();
					if (!partentPath.endsWith(File.separator)) {
						partentPath = partentPath + File.separator;
					}
					File saveToFile = new File(
							partentPath + ThumbnailUtil.THUMBNAIL_PREFIX + width + "_" + sourceFile.getName());
					if (!saveToFile.exists()) {
						thumbnailUtil.compressImageAsJpg(sourceFile, saveToFile, width, height);
					}
				}
				System.out.println(
						"---ljc-------------------createThumbnailImageByPath lstTmpPages.size():" + lstTmpPages.size());

			}

		} catch (Exception e) {
			log.error("", e);
			System.out.println("-------------------createThumbnailImageByPath exception:" + e);
		} finally {
			System.out.println("-------------------createThumbnailImageByPath success finish");
		}

	}

	public static TreeMap<File, LinkedList<File>> dirFiles = new TreeMap<File, LinkedList<File>>();

	static void getDirectoryFiles(File dir) {
		if (!dir.isDirectory()) {
			return;
		}
		LinkedList<File> files = new LinkedList<File>();
		File[] filesinDir = dir.listFiles();
		if (filesinDir.length > 0) {
			for (int i = 0; i < filesinDir.length; i++) {
				files.add(filesinDir[i]);
			}
		} else {
			dirFiles.put(dir, null);
			return;
		}
		dirFiles.put(dir, files);
		for (int i = 0; i < filesinDir.length; i++) {
			if (filesinDir[i].isDirectory()) {
				getDirectoryFiles(filesinDir[i]);
			}
		}

	}

	public static void createThumbnailImageByPathNew(String rootPath, String sPath, int width, int height,
			int errorSize) {
		dirFiles.clear();
		// 如果sPath不以文件分隔符结尾，自动添加文件分隔符
		if (!sPath.endsWith(File.separator)) {
			sPath = sPath + File.separator;
		}
		File dirFile = new File(sPath);
		// 如果dir对应的文件不存在，或者不是一个目录，则退出
		if (!dirFile.exists() || !dirFile.isDirectory()) {
			System.out.println("-------------------createThumbnailImageByPathNew return:" + sPath);
			return;
		}
		try {
			getDirectoryFiles(dirFile);
			List<String> fileNames = new ArrayList<String>();
			Iterator<File> iterator = dirFiles.keySet().iterator();
			while (iterator.hasNext()) {
				File dir = iterator.next();
				LinkedList<File> fileInDir = dirFiles.get(dir);
				if (fileInDir != null) {
					Iterator<File> it = fileInDir.iterator();
					while (it.hasNext()) {
						fileNames.add(it.next().getAbsolutePath());
					}
				}
			}

			List<String> newFiles = new ArrayList<String>();
			for (String fileName : fileNames) {
				File file = new File(fileName);
				if (!file.isDirectory()) {
					if ((file.getAbsolutePath().toLowerCase().endsWith(".jpg")
							|| file.getAbsolutePath().toUpperCase().endsWith(".JPG"))
							&& !file.getName().startsWith("thumbnail_")) {
						System.out.println("createThumbnailImageByPathNew file==>" + file.getAbsolutePath());
						newFiles.add(fileName);
					}
				}
			}
			ImportDataPageModelUtil allPage = new ImportDataPageModelUtil(newFiles, 50);
			int pageAccount = allPage.getTotalPages();
			int startPage = errorSize / 50 + 1;
			for (int i = startPage; i <= pageAccount; i++) {
				List<String> lstTmpPages = allPage.getObjects(i);
				for (String filePath : lstTmpPages) {
					ThumbnailUtil thumbnailUtil = new ThumbnailUtil();
					File sourceFile = new File(filePath);
					String partentPath = sourceFile.getParent();
					if (!partentPath.endsWith(File.separator)) {
						partentPath = partentPath + File.separator;
					}
					File saveToFile = new File(
							partentPath + ThumbnailUtil.THUMBNAIL_PREFIX + width + "_" + sourceFile.getName());
					if (!saveToFile.exists()) {
						thumbnailUtil.compressImageAsJpg(sourceFile, saveToFile, width, height);
					}
				}
				System.out.println("---ljc----createThumbnailImageByPathNew rootPath:" + rootPath + "  newFiles.size():"
						+ newFiles.size() + "---currentsize:" + i * 50);
			}

		} catch (Exception e) {
			log.error("", e);
			System.out.println("-------------------createThumbnailImageByPathNew exception:" + e);
		} finally {

			System.out.println("-------------------createThumbnailImageByPathNew success finish");
		}

	}

	/**
	 * 转换文件路径（不同的系统，如：linux,windows）
	 * 
	 * <AUTHOR> 2018-1-26 上午10:32:26
	 * @param path
	 * @return
	 */
	public static String getRealFilePath(String path) {
		return path.replace("/", FILE_SEPARATOR).replace("\\", FILE_SEPARATOR);
	}
}
