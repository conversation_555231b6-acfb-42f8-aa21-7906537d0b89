--drop table wx_t_non_local_user_report
create table wx_t_non_local_user_report (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	from_distributor_id              bigint                                         null,
	sku                              nvarchar(100)                                  null,
	report_region_id                 bigint                                         null,
	product_date                     datetime                                       null,
	out_date                         datetime                                       null,
	product_batch                    nvarchar(200)                                  null,
	out_order_no                     nvarchar(64)                                   null,
	logistics_code                   nvarchar(64)                                   null,
	report_latitude                  decimal(20,6)                                  null,
	report_longitude                 decimal(20,6)                                  null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	ext_flag                         int                                        not null,
	from_distributor_regions         nvarchar(max)                                  null,
	auth_channel                     int                                        not null,
	display_channel                  nvarchar(64)                                   null,
	report_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_non_local_user_report ADD CONSTRAINT uk_non_local_user_report UNIQUE (logistics_code);

ALTER TABLE [wx_t_loc_mkt_product_info] add from_distributor_regions         nvarchar(max)                                  null
ALTER TABLE [wx_t_loc_mkt_product_info] add to_distributor_regions           nvarchar(max)                                  null
ALTER TABLE [wx_t_loc_mkt_product_info] add auth_channel                     int                                        not null default(0)
ALTER TABLE [wx_t_loc_mkt_product_info] add display_channel                  nvarchar(64)                                   null

ALTER TABLE [wx_t_loc_mkt_product_info] ALTER COLUMN product_name                              nvarchar(256)                             not null

ALTER TABLE [wx_t_non_local_mkt_flow_pro_res] add auth_channel                     int                                        not null default(0)
ALTER TABLE [wx_t_non_local_mkt_flow_pro_res] add display_channel                  nvarchar(64)                                   null
