package com.chevron.pms.service.impl;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.b2b.business.MecVerPointBizService;
import com.chevron.b2b.dao.B2bVerifyPointPolicyMapper;
import com.chevron.b2b.model.B2bVerifyPointPolicy;
import com.chevron.b2b.model.MecVerPoint;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.business.impl.WorkshopDistributionRuleBizServiceImpl;
import com.chevron.pms.dao.MechanicQrcodeMapper;
import com.chevron.pms.dao.MechanicVerificationPayMapper;
import com.chevron.pms.dao.VerificationDetailMapper;
import com.chevron.pms.model.MechanicQrcode;
import com.chevron.pms.model.MechanicVerificationPay;
import com.chevron.pms.model.VerificationDetail;
import com.chevron.pms.model.WorkshopDistributionRule;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WsVerifOutStockParams;
import com.chevron.pms.service.InventoryService;
import com.chevron.pms.service.MechanicQrcodeService;
import com.chevron.pms.service.QrCodeService;
import com.chevron.point.business.PointBizService;
import com.chevron.point.model.B2BPointDetail;
import com.chevron.task.service.JobManageServiceI;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.log.util.LogUtils;
import com.sys.quartz.dao.ScheduleJobMapper;
import com.sys.quartz.service.impl.JobTaskService;

@Service
public class MechanicQrcodeServiceImpl implements MechanicQrcodeService {

	private static Logger log = LoggerFactory.getLogger(MechanicQrcodeServiceImpl.class);
	
//	@Autowired
//	private QrCodeService qrCodeService;
//	
//	@Autowired
//	private OilVerificationBizService oilVerificationBizService;
//	
//	@Autowired
//	private MechanicQrcodeBizService mechanicQrcodeBizService;
	
	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	@Autowired
	private MechanicQrcodeMapper mechanicQrcodeMapper;
	
	@Autowired
	private MechanicVerificationPayMapper mechanicVerificationPayMapper;
	
	@Autowired
	private WorkshopEmployeeBizService workshopEmployeeBizService;
	
	@Autowired
	private QrCodeService qrCodeServiceImpl;
	
	@Autowired
	private InventoryService inventoryService;
	
	@Autowired
	private WorkshopDistributionRuleBizServiceImpl workshopDistributionRuleBizServiceImpl;
	
	@Autowired
	private VerificationDetailMapper verificationDetailMapper;
	
	@Autowired
	ScheduleJobMapper scheduleJobMapper;
	
	@Resource
	JobManageServiceI jobManageService;
	
	@Resource
	JobTaskService jobService;
//	
//	@Autowired
//	private B2bVerifyPointPolicyBizService b2bVerifyPointPolicyBizService;
	
	@Autowired
	private B2bVerifyPointPolicyMapper b2bVerifyPointPolicyMapper;
	
	@Autowired
	private MecVerPointBizService mecVerPointBizService;
	
	@Autowired
	private PointBizService pointBizService;
//	
//	@Autowired
//	private MechanicVerificationPayBizService mechanicVerificationPayBizService;
	
	public final static String JOB_ID = "mechanicVerificationPayTask";
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> saveMechanicQrcode(final String mechanicCode, String qrCode) {
		log.info("mechanicCode: {}, qrCode: {}", mechanicCode, qrCode);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			//1, 验证输入参数
			validateInputData(mechanicCode, qrCode);
			//2. 验证技师编码
			final WorkshopEmployee employee = workshopEmployeeBizService.queryWorkshopEmployeeByCode(mechanicCode);
			if(employee == null){
				throw new WxPltException(MessageResourceUtil.getMessage("qr.invalid.mechanic"));
			}
//			if (qrCode.contains("\r")){
			qrCode = qrCode.replace("\r", "").replace("\n", "");
//		}
			String[] qrCodes1 = qrCode.split(",");
			//qrcode去重
			Set<String> qrCodes = new HashSet<String>();
			for(String qr : qrCodes1){
				qrCodes.add(qr);
			}
			//性能优化
			List<String> qrCodeList = new ArrayList<String>();
			StringBuilder sqlBuilder = null;
			Set<String> fleeingSet = new HashSet<String>(); //串货集合
			int i = 0;
			for(String qr : qrCodes){
				if(StringUtils.isNotBlank(qr)){
					qrCodeList.add(qr);
					if(i++ == 5){
						sqlBuilder.append(") union all select d.sku, d.qr_code, d.code_id from wx_t_qr_code_detail d  where d.qr_code in (?");
						i = 1;
					}else if(sqlBuilder == null){
						sqlBuilder = new StringBuilder("select d.sku, d.qr_code, d.code_id from wx_t_qr_code_detail d  where d.qr_code in (?");
					}else{
						sqlBuilder.append(",?");
					}
				}
			}
			final List<WsVerifOutStockParams> wsVerifOutStockParams = new ArrayList<WsVerifOutStockParams>();
			final List<VerificationDetail> verificationDetails = new ArrayList<VerificationDetail>();
			final List<VerificationDetail> o2oVerificationDetails = new ArrayList<VerificationDetail>();
			final Map<String, String> traceNoMap = new HashMap<String, String>(qrCodes.size());
			//1. 组装需要插入的技师扫码数据 
			final Long userId = ContextUtil.getCurUserId();
			final Date now = DateUtil.getCurrentDate();
			List<MechanicQrcode> scanData =  jdbcTemplate.query(sqlBuilder.append(")").toString(), 
					qrCodeList.toArray(), new RowMapper<MechanicQrcode>() {

						@Override
						public MechanicQrcode mapRow(ResultSet arg0, int arg1)
								throws SQLException {
							String traceNo = qrCodeServiceImpl.getLogisticsCode(arg0.getLong("code_id"));
							MechanicQrcode mechanicQrcode = new MechanicQrcode();
							mechanicQrcode.setQrCode(arg0.getString("qr_code"));
							mechanicQrcode.setMechanicCode(mechanicCode);
							mechanicQrcode.setSku(arg0.getString("sku"));
							mechanicQrcode.setTraceNo(traceNo);
							traceNoMap.put(mechanicQrcode.getQrCode(), traceNo);
//							mechanicQrcode.setCreatedBy(userId);
//							mechanicQrcode.setCreationTime(now);
//							mechanicQrcode.setWorkshopId(employee.getWorkshopId());
							//更新库存参数
							WsVerifOutStockParams params = new WsVerifOutStockParams();
							params.setLogisticsCode(traceNo);
							params.setSku(mechanicQrcode.getSku());
							params.setWorkshopId(employee.getWorkshopId());
							wsVerifOutStockParams.add(params);
							return mechanicQrcode;
						}});
			List<MechanicQrcode> insertMechanicQrcodes = new ArrayList<MechanicQrcode>();
			List<MechanicQrcode> updateMechanicQrcodes = new ArrayList<MechanicQrcode>();
			List<MechanicQrcode> mechanicQrcodes = mechanicQrcodeMapper.buildMechanicQrcodes(scanData);
			for(MechanicQrcode item : mechanicQrcodes){
				if(item.getId() < 0){
					//产品已核销
					throw new WxPltException("产品" + item.getQrCode() + "(" + traceNoMap.get(item.getQrCode()) + ")已核销");
				}
				if(item.getSku() == null){
					throw new WxPltException("产品" + item.getQrCode() + "(" + traceNoMap.get(item.getQrCode()) + ")不能核销");
				}
				item.setId(null);
				item.setMechanicCode(mechanicCode);
				item.setWorkshopId(employee.getWorkshopId());
				if(item.getUpdateFlag() == 1){
					item.setUpdateUser(userId);
					item.setUpdateTime(now);
					updateMechanicQrcodes.add(item);
				}else if(item.getQrcodePartnerId() == null){
					//非O2O码且可核销
					item.setScanType(MechanicQrcode.SCAN_TYPE_VERIFICATION);
					item.setCreatedBy(userId);
					item.setCreationTime(now);
					item.setEnableFlag(1);
					insertMechanicQrcodes.add(item);
					verificationDetails.add(buildVerificationDetail(item, false, userId, now));
				}else{
					item.setCreatedBy(userId);
					item.setCreationTime(now);
					insertMechanicQrcodes.add(item);
					
					Long qrcodePartnerId = item.getQrcodePartnerId() / 10;
					int flagBit = (int)(item.getQrcodePartnerId() - (qrcodePartnerId * 10));
					item.setQrcodePartnerId(qrcodePartnerId);
					if(flagBit == 1){
						//非O2O不可核销
						item.setEnableFlag(0);
						item.setScanType(MechanicQrcode.SCAN_TYPE_VERIFICATION);
						fleeingSet.add(item.getQrCode());
					}else{
						item.setScanType(MechanicQrcode.SCAN_TYPE_O2O);
						if(flagBit == 0){
							item.setEnableFlag(1);
							o2oVerificationDetails.add(buildVerificationDetail(item, true, userId, now));
						}else{
							item.setEnableFlag(0);
						}
					}
				}
			}
			//2. 批量插入技师扫码记录
			if(!insertMechanicQrcodes.isEmpty()){
				if(mechanicQrcodeMapper.insertBatch(insertMechanicQrcodes) < 1){
					throw new WxPltException("技师扫码插入失败");
				}
			}
			//3. 批量绑定出库即核销技师扫码记录
			if(!updateMechanicQrcodes.isEmpty()){
				if(mechanicQrcodeMapper.updateBatch(updateMechanicQrcodes) < 1){
					throw new WxPltException("出库即核销绑定技师扫码失败");
				}
				if(verificationDetailMapper.updateDistribution(updateMechanicQrcodes, null) < 1){
					throw new WxPltException("出库即核销金额分配失败");
				}
				//3.1 发红包
				WorkshopDistributionRule workshopDistributionRule = 
					workshopDistributionRuleBizServiceImpl.getWorkshopActiveRule(employee.getWorkshopId());
				if("Y".equals(workshopDistributionRule.getAwardRealtime())){
					List<MechanicVerificationPay> mechanicVerificationPays = mechanicVerificationPayMapper.buildMechanicVerificationPayList(updateMechanicQrcodes);
					if(!mechanicVerificationPays.isEmpty()){
						//3.1.1 批量插入发红包数据
						if(mechanicVerificationPayMapper.insertBatch(mechanicVerificationPays) < 1){
							throw new WxPltException("插入发红包数据失败");
						}
						triggerRedPacketPatyJobOnce();
					}
				}
			}
			//4. 插入核销申请数据TODO
			if(!o2oVerificationDetails.isEmpty()){
				//4.2 构建O2O核销数据
				verificationDetailMapper.insertBatchForO2oVerification(verificationDetailMapper.buildO2oVerificationDetails(o2oVerificationDetails));
			}
			if(!verificationDetails.isEmpty()){
				//4.3 批量插入核销申请数据
				verificationDetailMapper.insertBatchVerification(verificationDetails);
			}
			//5. 更新门店库存
			inventoryService.updateWsInventoryByVerificationList(wsVerifOutStockParams);
			
			resultMap.put("code", "success");
			if(!fleeingSet.isEmpty()){
				resultMap.put("codeMsg", "当前核销中包含串货产品");
			}
			log.info("request completed. mechanicCode: {}, qrCode: {}", mechanicCode, qrCode);
		} catch (WxPltException e){
			log.error("saveMechanicQrcode exception. " + e.getMessage(), e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} catch (Exception e) {
			log.error("saveMechanicQrcode exception. " + e.getMessage(), e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			if(e.getCause() != null){
				String msg = e.getCause().getMessage();
				int index = msg.indexOf("The duplicate key value is (");
				if(index >= 0){
					resultMap.put("codeMsg", "产品" + msg.substring(index + 28, msg.indexOf(")", index)) + "已核销");
				}
			}
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		
		return resultMap;
	}
	
	protected VerificationDetail buildVerificationDetail(MechanicQrcode mechanicQrcode, boolean isO2o,
			Long userId, Date now){
		VerificationDetail verificationDetail = new VerificationDetail();
		verificationDetail.setBatchId(-1l);
		verificationDetail.setCreatedBy(userId);
		verificationDetail.setCreationTime(now);
		verificationDetail.setQrCode(mechanicQrcode.getQrCode());
		verificationDetail.setSku(mechanicQrcode.getSku());
		verificationDetail.setWorkshopId(mechanicQrcode.getWorkshopId());
		verificationDetail.setMechanicCode(mechanicQrcode.getMechanicCode());
		if(isO2o){
			//O2O扫码
			verificationDetail.setO2oPartner(mechanicQrcode.getQrcodePartnerId());
		}
		return verificationDetail;
	}

	/**
	 * 手动触发消息推送一次
	 */
	private void triggerRedPacketPatyJobOnce(){
		
//		new Thread(){
//			public void run() {
//				//1. 临时关闭任务推送后台任务
//				ScheduleJobExample example = new ScheduleJobExample();
//				example.createCriteria().andSpringIdEqualTo(JOB_ID).andMethodNameEqualTo("execute");
//				List<ScheduleJob> jobs = scheduleJobMapper.selectByExample(example);
//				if(jobs != null && !jobs.isEmpty()){
//					try {
//						//任务立即启动
//						jobService.runAJobNow(jobs.get(0));
//					} catch (SchedulerException e) {
//						//任务未启动，直接发送红包
//						mechanicVerificationPayBizService.batchPay();
//						log.warn(e.getMessage(), e);
//					}
//				}else{
//					//未配置任务，直接发送红包
//					mechanicVerificationPayBizService.batchPay();
//				}
//			};
//		}.start();
	}

	private void validateInputData(String mechanicCode, String qrCode) throws WxPltException {
		if (StringUtils.isEmpty(mechanicCode) || StringUtils.isEmpty(qrCode)){
			throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
		}
	}

	@Override
	public Map<String, Object> getMechanicQrcodeByTraceNo(String traceNo) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			Map<String, Object> resultMap = qrCodeServiceImpl.getQrcode(traceNo);
			String qrcode = null;
			if(Constants.SUCCESS_CODE.equals(resultMap.get(Constants.RESULT_CODE_KEY))){
				qrcode = (String) resultMap.get("qrcode");
			}else{
				throw new WxPltException((String)resultMap.get("codeMsg"));
			}
			map.put("data", mechanicQrcodeMapper.getMechanicQrcode(qrcode));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getEnterpriseQrcodeUrl(String spId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(StringUtils.isBlank(spId)){
				throw new WxPltException("E001", "传入合伙人ID为空");
			}
			Long partnerId = null;
			try {
				partnerId = Long.parseLong(spId);
			} catch (Exception e) {
				throw new WxPltException("E002", "传入合伙人ID格式不对");
			}
			map.put("url", mechanicQrcodeMapper.getEnterpriseQrcodeUrl(partnerId, "Chevron"));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, e.getExpCode());
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public JsonResponse saveB2BMechanicQrcode(String userId, String qrcode,
			String salesChannel) {
		JsonResponse resultMap = new JsonResponse();
		Long uid = ContextUtil.getCurUserId();
		if(StringUtils.isBlank(qrcode)){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("瓶盖码不能为空");
			LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
					"瓶盖码不能为空",  userId + "," + qrcode + "," + salesChannel);
			return resultMap;
		}
		if(StringUtils.isBlank(userId)){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("技师编码不能为空");
			LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
					"技师编码不能为空",  userId + "," + qrcode + "," + salesChannel);
			return resultMap;
		}
		if(StringUtils.isBlank(salesChannel)){
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("Sales Channel不能为空");
			LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
					"Sales Channel不能为空",  userId + "," + qrcode + "," + salesChannel);
			return resultMap;
		}
		log.info("saveMechanic: " + userId + "," + qrcode + "," + salesChannel);
		try {
			String sku = qrCodeServiceImpl.getCapSkuByQrcode(qrcode);
			if(StringUtils.isBlank(sku)){
				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resultMap.setErrorMsg("瓶盖码无效");
				LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
						"瓶盖码无效",  userId + "," + qrcode + "," + salesChannel);
				return resultMap;
			}
			//查询技师信息
			WorkshopEmployee workshopEmployee = workshopEmployeeBizService.queryWorkshopEmployeeByCode(userId);
			
			if(workshopEmployee == null){
				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resultMap.setErrorMsg("技师不存在");
				LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
						"技师不存在", userId + "," + qrcode + "," + salesChannel);
				return resultMap;
			}
			//获取积分奖励规则
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("sku", sku);
//			paramsMap.put("enableFlag", 1);
			paramsMap.put("nowDate", DateUtil.getCurrentDate());
			paramsMap.put("partnerId", workshopEmployee.getPartnerId().toString());
			paramsMap.put("workshopId", workshopEmployee.getWorkshopId().toString());
			
//			List<B2bVerifyPointPolicy> list1 = b2bVerifyPointPolicyBizService.queryByParams(paramsMap);
			B2bVerifyPointPolicy policy = b2bVerifyPointPolicyMapper.getVerifyPolicy(paramsMap);
//			List<B2bVerifyPointPolicy> list=null;
			if(policy == null) {
				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resultMap.setErrorMsg("该产品不能扫码");
				LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
						"该产品不能扫码", userId + "," + qrcode + "," + salesChannel);
				return resultMap;
			}
			//查询技师信息
			//WorkshopEmployee workshopEmployee = workshopEmployeeBizService.queryWorkshopEmployeeByCode(userId);
//			if(workshopEmployee == null){
//				resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//				resultMap.setErrorMsg("技师不存在");
//				LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
//						"技师不存在", userId + "," + qrcode + "," + salesChannel);
//				return resultMap;
//			}
			doSaveB2BMechanicQrcode(uid, policy, workshopEmployee, userId, qrcode, salesChannel, sku);
			log.info("saveB2BMechanicQrcode success.");
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
			LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
					"积分发放失败。" + e.getMessage(),  userId + "," + qrcode + "," + salesChannel);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg("核销失败。请确认产品是否已核销");
			LogUtils.addErrorLog(uid, "com.chevron.pms.service.impl.MechanicQrcodeServiceImpl.saveB2BMechanicQrcode", 
					"核销失败。请确认产品是否未核销",  userId + "," + qrcode + "," + salesChannel);
		}
		return resultMap;
	}
	
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	protected void doSaveB2BMechanicQrcode(Long uid, B2bVerifyPointPolicy policy, WorkshopEmployee workshopEmployee, String userId, String qrcode,
			String salesChannel, String sku) throws Exception {
		BigDecimal b1 = new BigDecimal(policy.getAwardPoint()==null?"0":Double.toString(policy.getAwardPoint()));
		BigDecimal b2 = new BigDecimal(policy.getOrginalPoint()==null?"0":Double.toString(policy.getOrginalPoint()));
		Double point = b1.add(b2).doubleValue();
		//插入核销数据
		Date now = DateUtil.getCurrentDate();
		MechanicQrcode mechanicQrcode = new MechanicQrcode();
		mechanicQrcode.setCreatedBy(uid);
		mechanicQrcode.setCreationTime(now);
		mechanicQrcode.setMechanicCode(userId);
		mechanicQrcode.setSku(sku);
		mechanicQrcode.setQrCode(qrcode);
		mechanicQrcode.setWorkshopId(workshopEmployee.getWorkshopId());
		mechanicQrcodeMapper.insertSelective(mechanicQrcode);
		//插入技师核销积分数据
		MecVerPoint mecVerPoint = new MecVerPoint();
		mecVerPoint.setAwardPoint(point);
		mecVerPoint.setAwardPolicyId(policy.getId());
		mecVerPoint.setCreateTime(now);
		mecVerPoint.setCreateUserId(uid);
		mecVerPoint.setDistributorId(workshopEmployee.getPartnerId());
		mecVerPoint.setLiter(policy.getCapacity());
		mecVerPoint.setMechanicCode(userId);
		mecVerPoint.setQrcode(qrcode);
		mecVerPoint.setSalesChannel(salesChannel);
		mecVerPoint.setSku(sku);
		mecVerPoint.setStatus(1);
		mecVerPoint.setWorkshopId(workshopEmployee.getWorkshopId());
		mecVerPointBizService.insert(mecVerPoint);
		//发放积分
		List<B2BPointDetail> pointList = new ArrayList<B2BPointDetail>();
		B2BPointDetail b2bPointDetail = new B2BPointDetail();
		b2bPointDetail.setComments("【" + DateUtil.getDateStr(now, "yyyy-MM-dd") + "】扫码积分【" + point + "】");
		b2bPointDetail.setPoint(point);
		b2bPointDetail.setSalesChannel(salesChannel);
		b2bPointDetail.setTradeNo(mecVerPoint.getId().toString());
		b2bPointDetail.setEarnType("QR_CODE");
		b2bPointDetail.setUserId(userId);
		pointList.add(b2bPointDetail);
		try {
			pointBizService.importB2BPoint(pointList);
		} catch (WxPltException e) {
			log.error(e.getMessage(), e);
			throw e;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new WxPltException(e);
		}
	}

}
