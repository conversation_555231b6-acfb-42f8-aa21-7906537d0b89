insert into wx_t_workflow_step (workflow_code,step_code,step_name, list_operate_name, accept_alias,operation_permission_weight,biz_permission_weight,reject_to_step,reject_notify_type,reject_notify_template,reject_email_cc,todo_notify_type,todo_notify_template,todo_email_cc,pre_recall_notify_type,finish_rate_type,sort_numb,predefined_flag,remark,delete_flag,create_user_id,create_time) values ('SIGNAGE_APPLY','ABORT','终止申请','','',0,0,'',0,'','','0','','',0,0,1000,'0','',0,1,getdate())

update p set p.permission_weight=0
from wx_t_operation_permission p where p.module_code='Signage.apply' and p.permission_weight=256
--insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Promote_Sales'), 'Signage.apply', 256, 1, '店招权限。256-终止', 1, getdate());
