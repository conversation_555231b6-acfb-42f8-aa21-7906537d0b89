<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.master.dao.WorkshopEditRecordMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.master.model.WorkshopEditRecord">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="old_name" property="oldName" jdbcType="VARCHAR"/>
		<result column="new_name" property="newName" jdbcType="VARCHAR"/>
		<result column="old_dsr" property="oldDsr" jdbcType="BIGINT"/>
		<result column="old_dsr_text" property="oldDsrText" jdbcType="VARCHAR"/>
		<result column="new_dsr" property="newDsr" jdbcType="BIGINT"/>
		<result column="new_dsr_text" property="newDsrText" jdbcType="VARCHAR"/>
		<result column="old_contact_person" property="oldContactPerson" jdbcType="VARCHAR"/>
		<result column="old_contact_person_tel" property="oldContactPersonTel" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="workshop_id" property="workshopId" jdbcType="BIGINT"/>
		<result column="flsr" property="flsr" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="new_contact_person" property="newContactPerson" jdbcType="VARCHAR"/>
		<result column="new_contact_person_tel" property="newContactPersonTel" jdbcType="VARCHAR"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="approver" property="approver" jdbcType="BIGINT"/>
		<result column="approver_name" property="approverName" jdbcType="VARCHAR"/>
		<result column="organization_name" property="partnerName" jdbcType="VARCHAR"/>
		<result column="create_user" property="createUser" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,create_user_id,create_time,update_user_id,update_time,partner_id,old_name,new_name,old_dsr,new_dsr,
		old_contact_person,old_contact_person_tel,status,workshop_id,flsr,remark,delete_flag,new_contact_person,
		new_contact_person_tel,version,ext_property1,ext_property2,ext_property3,ext_property4,ext_property5,approver
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.master.model.WorkshopEditRecord">
		update wx_t_workshop_edit_record  set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.master.model.WorkshopEditRecord">
		update wx_t_workshop_edit_record 
		<set>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="oldName != null" >
				old_name = #{oldName,jdbcType=VARCHAR},
			</if>
			<if test="newName != null" >
				new_name = #{newName,jdbcType=VARCHAR},
			</if>
			<if test="oldDsr != null" >
				old_dsr = #{oldDsr,jdbcType=BIGINT},
			</if>
			<if test="newDsr != null" >
				new_dsr = #{newDsr,jdbcType=BIGINT},
			</if>
			<if test="oldContactPerson != null" >
				old_contact_person = #{oldContactPerson,jdbcType=VARCHAR},
			</if>
			<if test="oldContactPersonTel != null" >
				old_contact_person_tel = #{oldContactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="workshopId != null" >
				workshop_id = #{workshopId,jdbcType=BIGINT},
			</if>
			<if test="flsr != null" >
				flsr = #{flsr,jdbcType=VARCHAR},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="newContactPerson != null" >
				new_contact_person = #{newContactPerson,jdbcType=VARCHAR},
			</if>
			<if test="newContactPersonTel != null" >
				new_contact_person_tel = #{newContactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="version != null" >
				version = #{version,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="approver != null" >
				approver = #{approver,jdbcType=BIGINT},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.master.model.WorkshopEditRecordExample">
    	delete from wx_t_workshop_edit_record 
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.master.model.WorkshopEditRecord">
		insert into wx_t_workshop_edit_record
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="oldName != null">
				old_name,
			</if>
			<if test="newName != null">
				new_name,
			</if>
			<if test="oldDsr != null">
				old_dsr,
			</if>
			<if test="newDsr != null">
				new_dsr,
			</if>
			<if test="oldContactPerson != null">
				old_contact_person,
			</if>
			<if test="oldContactPersonTel != null">
				old_contact_person_tel,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="workshopId != null">
				workshop_id,
			</if>
			<if test="flsr != null">
				flsr,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="newContactPerson != null">
				new_contact_person,
			</if>
			<if test="newContactPersonTel != null">
				new_contact_person_tel,
			</if>
			<if test="version != null">
				version,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="approver != null">
				approver,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="oldName != null">
				#{oldName,jdbcType=VARCHAR},
			</if>
			<if test="newName != null">
				#{newName,jdbcType=VARCHAR},
			</if>
			<if test="oldDsr != null">
				#{oldDsr,jdbcType=BIGINT},
			</if>
			<if test="newDsr != null">
				#{newDsr,jdbcType=BIGINT},
			</if>
			<if test="oldContactPerson != null">
				#{oldContactPerson,jdbcType=VARCHAR},
			</if>
			<if test="oldContactPersonTel != null">
				#{oldContactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="workshopId != null">
				#{workshopId,jdbcType=BIGINT},
			</if>
			<if test="flsr != null">
				#{flsr,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="newContactPerson != null">
				#{newContactPerson,jdbcType=VARCHAR},
			</if>
			<if test="newContactPersonTel != null">
				#{newContactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="version != null">
				#{version,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="approver != null">
				#{approver,jdbcType=BIGINT},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_workshop_edit_record 
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.oldName != null">
				old_name = #{record.oldName,jdbcType=VARCHAR},
			</if>
			<if test="record.newName != null">
				new_name = #{record.newName,jdbcType=VARCHAR},
			</if>
			<if test="record.oldDsr != null">
				old_dsr = #{record.oldDsr,jdbcType=BIGINT},
			</if>
			<if test="record.newDsr != null">
				new_dsr = #{record.newDsr,jdbcType=BIGINT},
			</if>
			<if test="record.oldContactPerson != null">
				old_contact_person = #{record.oldContactPerson,jdbcType=VARCHAR},
			</if>
			<if test="record.oldContactPersonTel != null">
				old_contact_person_tel = #{record.oldContactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.workshopId != null">
				workshop_id = #{record.workshopId,jdbcType=BIGINT},
			</if>
			<if test="record.flsr != null">
				flsr = #{record.flsr,jdbcType=VARCHAR},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.newContactPerson != null">
				new_contact_person = #{record.newContactPerson,jdbcType=VARCHAR},
			</if>
			<if test="record.newContactPersonTel != null">
				new_contact_person_tel = #{record.newContactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="record.version != null">
				version = #{record.version,jdbcType=INTEGER},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty5 != null">
				ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="record.approver != null">
				approver = #{record.approver,jdbcType=BIGINT},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.master.model.WorkshopEditRecordExample">
		delete from wx_t_workshop_edit_record 
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.master.model.WorkshopEditRecordExample" resultType="int">
		select count(1) from wx_t_workshop_edit_record 
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopEditRecordExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_workshop_edit_record 
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopEditRecordExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_workshop_edit_record 
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.partner_id, t1.old_name,
			 t1.new_name, t1.old_dsr, t1.new_dsr, t1.old_contact_person, t1.old_contact_person_tel, t1.status, t1.workshop_id,
			 t1.flsr, t1.remark, t1.delete_flag, t1.new_contact_person, t1.new_contact_person_tel, t1.version, t1.ext_property1,
			 t1.ext_property2, t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.approver
		  from wx_t_workshop_edit_record  t1
		 where 1=1
	</select>

	<!--分页查询-->
    <select id="queryForPage" resultMap="BaseResultMap" resultType="com.chevron.master.model.WorkshopEditRecordParams">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.partner_id, t1.old_name,
		t1.new_name, t1.old_dsr, t1.new_dsr, t1.old_contact_person, t1.old_contact_person_tel, t1.status, t1.workshop_id,
		t1.flsr, t1.remark, t1.delete_flag, t1.new_contact_person, t1.new_contact_person_tel, t1.version, t1.ext_property1,
		t1.ext_property2, t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.approver,org.organization_name
		,u.ch_name as approver_name,u1.ch_name as create_user,u2.ch_name as update_user,
		u3.ch_name as old_dsr_text,u4.ch_name as new_dsr_text
		from wx_t_workshop_edit_record  t1
		left join wx_t_organization org on org.id = t1.partner_id
		left join wx_t_user u on u.user_id = t1.approver
		left join wx_t_user u1 on u1.user_id = t1.create_user_id
		left join wx_t_user u2 on u2.user_id = t1.update_user_id
		left join wx_t_user u3 on u3.user_id = t1.old_dsr
		left join wx_t_user u4 on u4.user_id = t1.new_dsr
		where t1.delete_flag != 1
		<if test="oldName != null and oldName != ''">
			and t1.old_name like '%' +  #{oldName} + '%'
		</if>
		<if test="newName != null and newName != ''">
			and t1.new_name like '%' +  #{new_name} + '%'
		</if>
		<if test="partnerId != null">
			and t1.partner_id = #{partnerId}
		</if>
		<if test="workshopId != null">
			and t1.workshop_id = #{workshopId}
		</if>
		<if test="status != null">
			and t1.status = #{status}
		</if>
		<if test="approver != null">
			and t1.approver = #{approver}
		</if>
		<if test="createUserId != null">
			and t1.create_user_id = #{createUserId}
		</if>

	</select>
	<select id="queryFlsrByPartner" resultType="map">
		select top 1 u.user_id as userId,u.ch_name as name, u.email
		from wx_t_partner_o2o_enterprise pe
		left join dw_customer_org_sales v on pe.distributor_id=v.distributor_id
		left JOIN wx_t_work_shop wtws on wtws.partner_id = pe.partner_id and wtws.business_weight = (
		case when v.product_channel='Consumer' then 1 when v.product_channel='Commercial' then 2 end)
		left join wx_t_user u on u.status=1 and u.cai=v.sales_cai
		where pe.partner_id=#{partnerId} and wtws.id=#{workshopId}
	</select>
</mapper>
