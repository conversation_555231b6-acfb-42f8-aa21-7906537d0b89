<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promotev2.dao.V2PromoteRegionalDetailMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promotev2.model.detail.V2PromoteRegionalDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="activity_workshop_number" property="activityWorkshopNumber" jdbcType="INTEGER" />
    <result column="main_subject" property="mainSubject" jdbcType="VARCHAR" />
    <result column="regional_promotion_details" property="regionalPromotionDetails" jdbcType="VARCHAR" />
    <result column="expecte_complete_sales_promotion" property="expecteCompleteSalesPromotion" jdbcType="INTEGER" />
    <result column="apply_promotion_support_points" property="applyPromotionSupportPoints" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, activity_id, activity_type, activity_workshop_number, main_subject, regional_promotion_details, 
    expecte_complete_sales_promotion, apply_promotion_support_points, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.V2PromoteRegionalDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_v2_promote_regional_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_v2_promote_regional_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_v2_promote_regional_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promotev2.model.V2PromoteRegionalDetailExample" >
    delete from wx_v2_promote_regional_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promotev2.model.detail.V2PromoteRegionalDetail" >
    insert into wx_v2_promote_regional_detail (id, activity_id, activity_type, 
      activity_workshop_number, main_subject, regional_promotion_details, 
      expecte_complete_sales_promotion, apply_promotion_support_points, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, #{activityType,jdbcType=VARCHAR}, 
      #{activityWorkshopNumber,jdbcType=INTEGER}, #{mainSubject,jdbcType=VARCHAR}, #{regionalPromotionDetails,jdbcType=VARCHAR}, 
      #{expecteCompleteSalesPromotion,jdbcType=INTEGER}, #{applyPromotionSupportPoints,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.promotev2.model.detail.V2PromoteRegionalDetail" >
    insert into wx_v2_promote_regional_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="activityId != null" >
        activity_id,
      </if>
      <if test="activityType != null" >
        activity_type,
      </if>
      <if test="activityWorkshopNumber != null" >
        activity_workshop_number,
      </if>
      <if test="mainSubject != null" >
        main_subject,
      </if>
      <if test="regionalPromotionDetails != null" >
        regional_promotion_details,
      </if>
      <if test="expecteCompleteSalesPromotion != null" >
        expecte_complete_sales_promotion,
      </if>
      <if test="applyPromotionSupportPoints != null" >
        apply_promotion_support_points,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null" >
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityType != null" >
        #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="activityWorkshopNumber != null" >
        #{activityWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="mainSubject != null" >
        #{mainSubject,jdbcType=VARCHAR},
      </if>
      <if test="regionalPromotionDetails != null" >
        #{regionalPromotionDetails,jdbcType=VARCHAR},
      </if>
      <if test="expecteCompleteSalesPromotion != null" >
        #{expecteCompleteSalesPromotion,jdbcType=INTEGER},
      </if>
      <if test="applyPromotionSupportPoints != null" >
        #{applyPromotionSupportPoints,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_v2_promote_regional_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.activityId != null" >
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.activityType != null" >
        activity_type = #{record.activityType,jdbcType=VARCHAR},
      </if>
      <if test="record.activityWorkshopNumber != null" >
        activity_workshop_number = #{record.activityWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="record.mainSubject != null" >
        main_subject = #{record.mainSubject,jdbcType=VARCHAR},
      </if>
      <if test="record.regionalPromotionDetails != null" >
        regional_promotion_details = #{record.regionalPromotionDetails,jdbcType=VARCHAR},
      </if>
      <if test="record.expecteCompleteSalesPromotion != null" >
        expecte_complete_sales_promotion = #{record.expecteCompleteSalesPromotion,jdbcType=INTEGER},
      </if>
      <if test="record.applyPromotionSupportPoints != null" >
        apply_promotion_support_points = #{record.applyPromotionSupportPoints,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_v2_promote_regional_detail
    set id = #{record.id,jdbcType=BIGINT},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      activity_type = #{record.activityType,jdbcType=VARCHAR},
      activity_workshop_number = #{record.activityWorkshopNumber,jdbcType=INTEGER},
      main_subject = #{record.mainSubject,jdbcType=VARCHAR},
      regional_promotion_details = #{record.regionalPromotionDetails,jdbcType=VARCHAR},
      expecte_complete_sales_promotion = #{record.expecteCompleteSalesPromotion,jdbcType=INTEGER},
      apply_promotion_support_points = #{record.applyPromotionSupportPoints,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promotev2.model.detail.V2PromoteRegionalDetail" >
    update wx_v2_promote_regional_detail
    <set >
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="activityWorkshopNumber != null" >
        activity_workshop_number = #{activityWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="mainSubject != null" >
        main_subject = #{mainSubject,jdbcType=VARCHAR},
      </if>
      <if test="regionalPromotionDetails != null" >
        regional_promotion_details = #{regionalPromotionDetails,jdbcType=VARCHAR},
      </if>
      <if test="expecteCompleteSalesPromotion != null" >
        expecte_complete_sales_promotion = #{expecteCompleteSalesPromotion,jdbcType=INTEGER},
      </if>
      <if test="applyPromotionSupportPoints != null" >
        apply_promotion_support_points = #{applyPromotionSupportPoints,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promotev2.model.detail.V2PromoteRegionalDetail" >
    update wx_v2_promote_regional_detail
    set activity_id = #{activityId,jdbcType=BIGINT},
      activity_type = #{activityType,jdbcType=VARCHAR},
      activity_workshop_number = #{activityWorkshopNumber,jdbcType=INTEGER},
      main_subject = #{mainSubject,jdbcType=VARCHAR},
      regional_promotion_details = #{regionalPromotionDetails,jdbcType=VARCHAR},
      expecte_complete_sales_promotion = #{expecteCompleteSalesPromotion,jdbcType=INTEGER},
      apply_promotion_support_points = #{applyPromotionSupportPoints,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- 活动明细 -->
  <resultMap id="RegionalBaseResultMap" type="com.chevron.promotev2.model.response.V2ResponsePromoteRegionalDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="activity_workshop_number" property="activityWorkshopNumber" jdbcType="INTEGER" />
    <result column="main_subject" property="mainSubject" jdbcType="VARCHAR" />
    <result column="regional_promotion_details" property="regionalPromotionDetails" jdbcType="VARCHAR" />
    <result column="expecte_complete_sales_promotion" property="expecteCompleteSalesPromotion" jdbcType="INTEGER" />
    <result column="apply_promotion_support_points" property="applyPromotionSupportPoints" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    
    <!-- 公共 -->
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="source_plan_id" property="sourcePlanId" jdbcType="BIGINT" />
    <result column="source_distribution_id" property="sourceDistributionId" jdbcType="BIGINT" />
    <result column="activity_subject" property="activitySubject" jdbcType="VARCHAR" />
    <result column="activity_start_time" property="activityStartTime" jdbcType="TIMESTAMP" />
    <result column="activity_end_time" property="activityEndTime" jdbcType="TIMESTAMP" />
    <result column="activity_address" property="activityAddress" jdbcType="VARCHAR" />
    <result column="activity_organizers" property="activityOrganizers" jdbcType="VARCHAR" />
    <result column="activity_organizers_id" property="activityOrganizersId" jdbcType="BIGINT" />
    <result column="activity_amount" property="activityAmount" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="contact_address" property="contactAddress" jdbcType="VARCHAR" />
    <result column="contact_person" property="contactPerson" jdbcType="VARCHAR" />
    <result column="contact_tel" property="contactTel" jdbcType="VARCHAR" />
  </resultMap>
  
  <select id="getRegionalActivityDetail" parameterType="long" resultMap="RegionalBaseResultMap">
    SELECT t_regional_detail.*,
    t_promote_activity.apply_batch_id,t_promote_activity.source_plan_id,
    t_promote_activity.source_distribution_id,t_promote_activity.activity_subject,
    t_promote_activity.activity_start_time,t_promote_activity.activity_end_time,
    t_promote_activity.activity_address,
    t_promote_activity.activity_organizers,t_promote_activity.activity_organizers_id,
    t_promote_activity.activity_amount,t_promote_activity.create_time,t_promote_activity.release_time,
    t_promote_activity.contact_address,t_promote_activity.contact_person,t_promote_activity.contact_tel,
    t_apply.creator_id,tt_user.ch_name creator
    FROM wx_v2_promote_activity t_promote_activity
    INNER JOIN wx_v2_promote_regional_detail t_regional_detail
    ON t_regional_detail.activity_id = t_promote_activity.id
    INNER JOIN wx_v2_promote_application_batch t_apply
    ON t_apply.batchid = t_promote_activity.apply_batch_id
    INNER JOIN wx_t_user tt_user
    ON tt_user.user_id = t_apply.creator_id
    WHERE t_regional_detail.activity_id = #{activityId}
  </select>
  
  <select id="getHasApplyPoints" parameterType="map" resultMap="BaseResultMap">
      SELECT sum(t_activity_detail.apply_promotion_support_points)apply_promotion_support_points
      FROM wx_v2_promote_activity t_activity
      INNER  JOIN wx_v2_promote_regional_detail t_activity_detail
      ON t_activity.id = t_activity_detail.activity_id
      INNER JOIN wx_v2_promote_application_batch t_apply
      ON t_apply.batchid = t_activity.apply_batch_id
      WHERE t_activity.source_plan_id = #{sourcePlanId}
      AND t_activity.creator_id = #{userId}
      <if test="status!=null">
        and
         t_apply.approve_status NOT IN 
        <foreach item="statu" index="index" collection="status" open="(" separator="," close=")">  
             #{statu}  
        </foreach> 
      </if>
  </select>
</mapper>