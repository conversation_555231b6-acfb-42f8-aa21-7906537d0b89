/*			select year(a.create_time), sum(l) l from (
			SELECT tt_order.id, tt_order.order_no, tt_order.order_type, tt_order.buy_user_no, tt_order.buy_user_name
				, tt_order.buyer_card_id, tt_order.invoice_info, tt_order.source, tt_order.source_id, tt_order.pay_type
				, pay_state, tt_order.total_product_price, tt_order.total_delivery_price, tt_order.total_order_price, tt_order.total_product_prefer_price
				, tt_order.total_delivery_prefer_price, tt_order.total_order_prefer_price, tt_order.total_order_pay_price, tt_order.total_order_need_pay_price, tt_order.work_shop_id
				, tt_order.work_shop_id AS workshop_id, tt_order.reference_order_no, tt_order.reference_order_type, tt_order.status, tt_order.create_time
				, tt_order.update_time, tt_order.creator, tt_order.remark, tt_order.plate_number, tt_order.address
				, tt_order.receive_user_name, tt_order.receive_phone_no, tt_order.effective_time, tt_order.type, tt_order.bill_id
				, tt_order.snd_type, tt_order.phone_no, tt_order.service_time, tt_order.invalid_time, tt_order.region_name
				, tt_order.is_export, tt_order.car_type, tt_order.preferential_type_price, tt_order.delivery_time, tt_order.oil_injection
				, tt_org.organization_name AS orgname, tt_workshop.work_shop_name AS t_order_workshopname,
				(select sum(convert(float,p.capacity)*ol.amount) from wx_t_order_line ol 
left join wx_t_product p on p.sku=ol.sku
where order_id=tt_order.id) l
				, (
					SELECT u.CH_NAME
					FROM wx_t_user u
					WHERE u.USER_ID = tt_order.creator
				) AS creator_display_name
				, (
					SELECT o.work_shop_name
					FROM wx_t_work_shop o
					WHERE o.id = tt_order.work_shop_id
				) AS work_shop_name
				, (
					SELECT p.dic_item_name
					FROM wx_t_dic_item p
					WHERE p.dic_type_code = 'order.status'
						AND p.dic_item_code = CASE 
							WHEN tt_order.status = 11
							AND tt_order.partner_confirm_type = 1 THEN 13
							ELSE tt_order.status
						END
				) AS status_meaning
				, (
					SELECT TOP 1 db_or.status
					FROM wx_t_db2b_order db_or
					WHERE 1 = 1
						AND charindex(db_or.order_no, tt_order.source) > 0
						AND db_or.delete_flag = 0
				) AS b2b_status
			FROM wx_t_order tt_order
				LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
				LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id
				LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
			WHERE 1 = 1
				AND tt_order.order_type != 'DA'
				AND tt_order.order_type != 'DP'
				AND tt_order.order_type != 'SPDA') a group by year(a.create_time)*/
			select a.order_no "订单编号", a.orgname "合伙人", a.work_shop_name "门店名称", a.status_meaning "订单状态",
			(select sum(convert(float,p.capacity)*ol.amount) from wx_t_order_line ol 
left join wx_t_product p on p.sku=ol.sku
where order_id=a.id) "升数",a.create_time "创建时间" from (
			SELECT tt_order.id, tt_order.order_no, tt_order.order_type, tt_order.buy_user_no, tt_order.buy_user_name
				, tt_order.buyer_card_id, tt_order.invoice_info, tt_order.source, tt_order.source_id, tt_order.pay_type
				, pay_state, tt_order.total_product_price, tt_order.total_delivery_price, tt_order.total_order_price, tt_order.total_product_prefer_price
				, tt_order.total_delivery_prefer_price, tt_order.total_order_prefer_price, tt_order.total_order_pay_price, tt_order.total_order_need_pay_price, tt_order.work_shop_id
				, tt_order.work_shop_id AS workshop_id, tt_order.reference_order_no, tt_order.reference_order_type, tt_order.status, tt_order.create_time
				, tt_order.update_time, tt_order.creator, tt_order.remark, tt_order.plate_number, tt_order.address
				, tt_order.receive_user_name, tt_order.receive_phone_no, tt_order.effective_time, tt_order.type, tt_order.bill_id
				, tt_order.snd_type, tt_order.phone_no, tt_order.service_time, tt_order.invalid_time, tt_order.region_name
				, tt_order.is_export, tt_order.car_type, tt_order.preferential_type_price, tt_order.delivery_time, tt_order.oil_injection
				, tt_org.organization_name AS orgname, tt_workshop.work_shop_name AS t_order_workshopname
				, (
					SELECT u.CH_NAME
					FROM wx_t_user u
					WHERE u.USER_ID = tt_order.creator
				) AS creator_display_name
				, (
					SELECT o.work_shop_name
					FROM wx_t_work_shop o
					WHERE o.id = tt_order.work_shop_id
				) AS work_shop_name
				, (
					SELECT p.dic_item_name
					FROM wx_t_dic_item p
					WHERE p.dic_type_code = 'order.status'
						AND p.dic_item_code = CASE 
							WHEN tt_order.status = 11
							AND tt_order.partner_confirm_type = 1 THEN 13
							ELSE tt_order.status
						END
				) AS status_meaning
				, (
					SELECT TOP 1 db_or.status
					FROM wx_t_db2b_order db_or
					WHERE 1 = 1
						AND charindex(db_or.order_no, tt_order.source) > 0
						AND db_or.delete_flag = 0
				) AS b2b_status
			FROM wx_t_order tt_order
				LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
				LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id
				LEFT JOIN wx_t_organization tt_org ON tt_par.partner_id = tt_org.id
			WHERE 1 = 1
				AND tt_order.order_type != 'DA'
				AND tt_order.order_type != 'DP'
				AND tt_order.order_type != 'SPDA') a order by a.orgname, a.create_time desc