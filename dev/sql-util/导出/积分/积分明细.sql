 select a.point, a.comments, a.create_time from ( SELECT a.APPLICATION_ORG_ID as partner_id,'' as point_code,'abp' as point_type,
 a.id AS source_id,a.APPLICATION_CODE AS source_code, 
 -cast(pb.ATTRIBUTE1 as decimal(12,2)) AS point, a.CREATION_TIME as create_time,
 '订单:' + a.APPLICATION_CODE AS comments, NULL AS promotion_name, 
 pb.BUSINESS_TYPE_CODE AS business_type, NULL AS promotion_id, pb.ID AS business_id, 
 '进货积分' AS point_type_text,u.ch_name AS created_by_user,NULL AS att_id 
 FROM dbo.wx_t_material_application a 
 LEFT JOIN wx_t_organization o ON o.id = a.APPLICATION_ORG_ID 
 LEFT JOIN wx_t_point_business pb on a.APPLICATION_CODE = pb.RELATED_CODE 
 LEFT JOIN wx_t_user u on u.user_id = a.CREATED_BY WHERE 1 = 1 AND a.DELETE_FLAG = 0 
 and pb.BUSINESS_STATUS = 'DONE' 
 and (pb.BUSINESS_TYPE_CODE is null or pb.BUSINESS_TYPE_CODE not in ('ROLLBACK_POINT'))
 AND (a.ATTRIBUTE1 is null or a.ATTRIBUTE1 != 'PROMOTION_EXCHANGE') 
 AND a.APPLICATION_USER_ID='M2020052608592444' AND (a.APPLICATION_TYPE = 'b2b' or a.promotion_type = 'b2b') 

 union all SELECT pac.POINT_ACCOUNT_OWNER_ID as partner_id,'' as point_code,'abp' as 
 point_type,bu.RELATED_ID as source_id,bu.RELATED_CODE AS source_code, 
 cast(plog.MODIFIED_VALUE as decimal(12,2)) as point, plog.CREATION_TIME as create_time, 
 plog.COMMENTS, case when bu.business_type_code in ('SELL_IN_PROMOTION_DELIVERY_V2',
 'SELL_IN_PROMOTION_DELIVERY') then bu.ATTRIBUTE1 else null end AS promotion_name, 
 bu.BUSINESS_TYPE_CODE AS business_type, 
 case when bu.business_type_code in ('SELL_IN_PROMOTION_DELIVERY_V2','SELL_IN_PROMOTION_DELIVERY') 
 then bu.RELATED_ID else null end AS promotion_id, bu.ID AS business_id,'进货积分' AS point_type_text,
 u.ch_name AS created_by_user,bu.ATT_ID AS att_id 
 FROM wx_t_point_value_detail pde LEFT JOIN wx_t_point_value_detail_log plog 
 ON plog.POINT_VALUE_ID = pde.ID LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID 
 LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID 
 left join wx_t_promotion_point pp on pp.point_code = pde.point_type 
 LEFT JOIN wx_t_user u on u.user_id = pde.CREATED_BY 
 WHERE 1 = 1 AND pp.point_type = 'b2b' --AND plog.MODIFIED_VALUE != 0 
 AND pac.POINT_ACCOUNT_OWNER_CODE='M2020052608592444') a order by a.create_time desc