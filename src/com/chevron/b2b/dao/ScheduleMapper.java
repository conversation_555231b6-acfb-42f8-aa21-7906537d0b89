package com.chevron.b2b.dao;

import com.chevron.b2b.model.Schedule;
import com.chevron.b2b.model.ScheduleExample;
import com.chevron.b2b.model.ScheduleParams;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 计划-wx_t_schedule数据库操作对象
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
public interface ScheduleMapper {

	/**
	 * 根据主键更新计划对象(非空属性)
	 * @param record 新计划对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(Schedule record);

    /**
     * 根据主键删除计划对象
     * @param id 删除计划对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询计划对象
	 * @param id 计划对象主键值
	 * @return 计划对象结果
	 */
    Schedule selectByPrimaryKey(Long id);

 	/**
	 * 插入计划对象(非空属性)
	 * @param record 被插入计划对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(Schedule record);

	/**
	 * 更新计划对象
	 * @param record 被修改计划对象
	 * @param example 过滤计划对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") Schedule record, @Param("example") ScheduleExample example);

	/**
	 * 删除计划对象
	 * @param example 被删除计划对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleExample example);
    
 	/**
	 * 查询满足条件的计划对象数量
	 * @param example 过滤计划对象条件
	 * @return 满足条件计划对象数量
	 */
    int countByExample(ScheduleExample example);

	/**
	 * 单表查询计划对象
	 * @param example 过滤计划对象条件
	 * @return 满足条件计划对象集合
	 */
    List<Schedule> selectByExample(ScheduleExample example);

	/**
	 * 列表查询计划对象
	 * @param params 查询条件
	 * @return 满足条件计划对象集合
	 */
    List<Schedule> queryByParams(Map<String, Object> params);

	/**
	 * 分页查询计划对象
	 * @param params 查询条件
	 * @return 满足条件计划对象集合
	 */
    List<Schedule> queryForPage(ScheduleParams params);
}
