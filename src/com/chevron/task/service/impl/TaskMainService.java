package com.chevron.task.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.chevron.exportdata.Export;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterExample;
import com.chevron.plc.dao.InStockProductVoMapper;
import com.chevron.pms.business.WorkshopStatusBizService;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.TaskExecTraceVoMapper;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.TaskExecTraceVo;
import com.chevron.point.business.PointBizService;
import com.chevron.task.business.TaskService;
import com.chevron.task.common.Base64ImgReplacedElementFactory;
import com.chevron.task.common.FleetPerProve;
import com.chevron.task.common.FleetPerStatus;
import com.chevron.task.controller.TaskServController;
import com.chevron.task.dao.TaskCompeteGoodsVoMapper;
import com.chevron.task.dao.TaskInstanceCheckVoMapper;
import com.chevron.task.dao.TaskProductInfoVoMapper;
import com.chevron.task.dao.WxTaskHasgdWorkshopMapper;
import com.chevron.task.dao.WxTaskMainMapper;
import com.chevron.task.dao.WxTaskMbMapper;
import com.chevron.task.dao.WxTaskSubMapper;
import com.chevron.task.model.AttfileCheckForXDConditions;
import com.chevron.task.model.ExportImgForActiveWorkshop;
import com.chevron.task.model.ExportMtImg;
import com.chevron.task.model.MainTaskQueryConditions;
import com.chevron.task.model.SubTaskQueryConditions;
import com.chevron.task.model.TaskCompeteGoodsVo;
import com.chevron.task.model.TaskHandleFleetProveVo;
import com.chevron.task.model.TaskInstanceCheckUpdateVo;
import com.chevron.task.model.TaskInstanceCheckVo;
import com.chevron.task.model.TaskInstanceCheckVoTree;
import com.chevron.task.model.TaskInstanceSubmitUpdate;
import com.chevron.task.model.TaskProductInfoVo;
import com.chevron.task.model.TaskTabPermissionInfo;
import com.chevron.task.model.WorkshopInstockProductResp;
import com.chevron.task.model.WxTaskHasgdWorkshop;
import com.chevron.task.model.WxTaskMain;
import com.chevron.task.model.WxTaskMainNew;
import com.chevron.task.model.WxTaskMb;
import com.chevron.task.model.WxTaskSub;
import com.chevron.task.service.AppTaskService;
import com.chevron.task.service.TaskMainServiceI;
import com.chevron.userselect.model.SelectedUser;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.EmailSendUtils;
import com.common.util.FileUtil;
import com.common.util.ImageWaterSetUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.SendMessageUtil;
import com.common.util.SpringUtils;
import com.common.util.StringUtils;
import com.common.util.ValueParser;
import com.lowagie.text.pdf.BaseFont;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.TreeNode;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.WxTParterRolePowerService;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.model.WxAttFile;
import com.sys.file.model.WxAttFileExample;
import com.sys.file.service.FileManagerServiceI;
import com.sys.file.web.FileManager;
import com.sys.log.model.Log;
import com.sys.log.service.LogService;
import com.sys.push.model.WxPushMessage;
import com.sys.push.service.MessagePushService;
import com.sys.quartz.business.CreateTaskQuartzService;
import com.sys.quartz.model.ScheduleJob;
import com.sys.quartz.model.WorkshopExcuteUserInfo;
import com.sys.quartz.service.impl.JobTaskService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 *
 * 提供任务创建 任务查询 任务办理的服务类
 * @Author: bo.liu  2016-11-15 下午2:31:36
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class TaskMainService implements TaskMainServiceI {

    @Resource
    private TaskService taskService;
    @Resource
    private WxTaskMbMapper mbMapper;
    @Resource
    private WxTaskMainMapper mainTaskMapper;
    @Resource
    private WxTUserMapper userMapper;
    @Resource
    private WorkshopMasterMapper workshopMasterMapper;
    @Resource
    private WxTaskSubMapper subtaskMapper;
    @Resource
    private TaskInstanceCheckVoMapper instanceCheckVoMapper;
    @Resource
    private WxAttFileMapper wxAttFileMapper;
    @Resource
    private TaskCompeteGoodsVoMapper taskCompeteGoodsVoMapper;
    @Resource
    private TaskProductInfoVoMapper taskProductInfoMapper;
    @Resource
    private JobTaskService jobTaskService;
    @Resource
    private TaskExecTraceVoMapper taskExecTraceMapper;

    @Resource
    WxTParterRolePowerService partnerRolePowerService;

    @Resource
    private OrderVoMapper orderVoMapper;

    @Resource
    private AppTaskService appTaskService;

    @Resource
    InStockProductVoMapper instockProductMapper;

    @Resource
    WxTaskHasgdWorkshopMapper hasgdWorkshopMapper;

    @Resource
    private JdbcTemplate wxJdbcTemplate;

    @Autowired
    PointBizService pointBizService;

//    @Autowired
//    private WorkshopEmployeeBizService workshopEmployeeBizService;

    @Resource
    WorkshopStatusBizService workshopStatusBizService;

    @Autowired
    private FileManagerServiceI fileManagerService;
    @Autowired
    private WorkshopMasterBizService workshopMasterBizService;
    
    @Autowired
    private WorkshopMasterBizService workshopEmployeeBizServiceImpl;

    private final static Logger log = Logger.getLogger(TaskMainService.class);

    public static Integer CREATE_TASK_SND_MSG = 1;
    public static Integer CREATE_TASK_NOT_SND_MSG= 0;
    public static String TASK_MSG_TITLE = "任务消息提示";
    public static String TASK_MSG_CONTENT_SD = "您有新的扫店任务需要处理";
    public static String TASK_MSG_CONTENT_GD = "您有新的攻店任务需要处理";
    public static String TASK_MSG_CONTENT_XD = "您有新的巡店任务需要处理";
    public static String TASK_MSG_CONTENT_SI = "您有新的门店入库任务需要处理";
    public static String TASK_MSG_CONTENT_TIME_XD = "您有系统创建的定时巡店任务需要处理";
    public static String TASK_MSG_CONTENT_TIME_YQ = "您有系统创建的延期任务需要处理";
    public static int TOTAL_WORKSHOP = 100;
    public static final String CDYJZM_DESIGN_PDF_FTL = "ci_cdyjzm_prove_design_pdf.ftl";
    public static final String GCJX_DESIGN_PDF_FTL = "ci_gcjx_prove_design_pdf.ftl";
    public static final String YJZM_DESIGN_PDF_FTL = "ci_yjzm_prove_design_pdf.ftl";
    public static final String CDYJZM_DESIGN_EMAIL_FTL = "ci_cdyjzm_approve_mail.ftl";
    public static final String GCJX_DESIGN_EMAIL_FTL = "ci_gcjx_approve_mail.ftl";
    public static final String YJZM_DESIGN_EMAIL_FTL = "ci_yjzm_approve_mail.ftl";
    public static final String CDYJZM_PROVE_HIGH_PDF = "ci_cdyjzm_prove_high_pdf.ftl";
    public static final String YJZM_PROVE_HIGH_PDF = "ci_yjzm_prove_high_pdf.ftl";
    public static final String CDYJZM_CC_DIC_TYPE = "FleetPerformanceEmailCC";
    public static final String GCJX_CC_DIC_TYPE = "GcjxPerformanceEmailCC";
    public static final String YJZM_CC_DIC_TYPE = "YjzmPerformanceEmailCC";

    @Resource
    MessagePushService msgPushService;

    @Resource
    CreateTaskQuartzService createTaskService;

    @Resource
    Export exportExcel;

    @Resource
    LogService logService;

    @Autowired
    private DicItemVoMapper dicItemVoMapper;

    // 查询所有模板
    public Map<String,Object> queryTemplate(String tmpName, int tmpRange, String taskTypeCode) {
        Map<String,Object> resMap = new HashMap<String,Object>();
        WxTUser curUser = ContextUtil.getCurUser();
        Long createUserId = curUser.getUserId();
        try {
            List<WxTaskMb> mbList = taskService.selectTemplate(createUserId,
                    tmpName, tmpRange, taskTypeCode);
            resMap.put("code", "success");
            resMap.put("codeMsg", "查询成功!");
            resMap.put("mbList", mbList);
        } catch (Exception ex) {
            resMap.put("code", "fail");
            resMap.put("codeMsg", "查询失败!错误信息:" + ex.getMessage());
            log.error("error",ex);
        }

        return resMap;
    }

    //add by bo.liu start==============================================================================================
    @Override
    public Map<String, Object> getCurrentMbByMtypeAndPartnerId(String mbType){
        Map<String, Object> resultMap = new HashMap<String,Object>();
        List<WxTaskMb> lstMb = new ArrayList<WxTaskMb>();
//		Map<String, Object> reqMap = new HashMap<String,Object>();
        try
        {	if(null!=mbType && !"".equals(mbType))
        {
//				reqMap.put("mbType", mbType);
            //reqMap.put("currentPartnerId", null);//前期先设置此值为null,,,即不用过滤
//				reqMap.put("currentPartnerId", null);//虚拟的tenantid 前期先设置此值为null,,,即不用过滤
            WxTUser user = ContextUtil.getCurUser();
            Long orgId = user.getOrgId();
            Long userType = WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l;
            lstMb = mbMapper.selectPublicMbByTmbTypeCode(mbType, orgId, userType);
//				lstMb = mbMapper.queryMbsByMbTypeAndPartnerId(reqMap);
            WxTaskMb defMb = mbMapper.selectDefTemplate(mbType, orgId, userType, null);
            if(defMb != null){
                resultMap.put("defMbId", defMb.getMbId());
            }
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
            resultMap.put("lstmb", lstMb);
        }else
        {
            //前台需要给出提示，模板类型是空
            resultMap.put("code", "mbtypeisnull");
            resultMap.put("codeMsg", "系统参数模板类型错误");
        }

        }catch(Exception ex)
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error",ex);
        }
        return resultMap;
    }

    //主任务列表需要
    public Map<String,Object> getMainTaskByTaskTypeCode(String taskTypeCode,MainTaskQueryConditions mainTaskQueryObj){
        Map<String, Object> resultMap = new HashMap<String,Object>();
        List<WxTaskMain> lstMainTask = new ArrayList<WxTaskMain>();
        Map<String, Object> reqMap = new HashMap<String,Object>();
        try
        {	if(null!=taskTypeCode && !"".equals(taskTypeCode))
        {

            reqMap.put("taskTypeCodeModel", 2);//所有任务类型，主任务界面上不显示门店信息
            if(null!=mainTaskQueryObj)//当查询条件数组不为null的时候需要解析数组各个值，
            {
                String taskName = mainTaskQueryObj.getTaskName();
                String taskMainStauts = mainTaskQueryObj.getTaskMainStatus();
                boolean isCreateUser = mainTaskQueryObj.isCreateUser();
                boolean isExcuteUser = mainTaskQueryObj.isExecUser();
                boolean isWatchUser = mainTaskQueryObj.isWatchUser();
                Date taskStartTime = mainTaskQueryObj.getTaskFinishTimeS();//预计开始时间
                Date taskEndTime = mainTaskQueryObj.getTaskFinishTimeE();//预计结束时间
                String taskExcuteUID = mainTaskQueryObj.getTaskExcuteUserName();//执行者ID modify by bo.liu 0828

                String workshopName = mainTaskQueryObj.getTaskWorkshopName();
                String workshopScale = mainTaskQueryObj.getTaskWorkshopScale();
                if(taskName!=null && !taskName.equals(""))
                {
                    reqMap.put("taskName", taskName);
                }
                if(taskMainStauts!=null && !taskMainStauts.equals(""))
                {
                    reqMap.put("taskMainStauts", taskMainStauts);
                }

                if(isCreateUser)
                {
                    reqMap.put("isCreateUser", isCreateUser);
                }

                else if(isExcuteUser)
                {
                    reqMap.put("isExcuteUser", isExcuteUser);
                }

                else if(isWatchUser)
                {
                    reqMap.put("isWatchUser", isWatchUser);
                }

                if(null!=taskStartTime)
                {
                    reqMap.put("taskStartTime", taskStartTime);
                }

                if(null!=taskEndTime)
                {
                    reqMap.put("taskEndTime", taskEndTime);
                }


                if(taskExcuteUID!=null && !taskExcuteUID.equals("") && !taskExcuteUID.equals("-1"))
                {
                    reqMap.put("taskExcuteUID", taskExcuteUID);
                }

                if(workshopName!=null && !workshopName.equals(""))
                {
                    reqMap.put("workshopName", workshopName);
                }

                if(workshopScale!=null && !workshopScale.equals(""))
                {
                    reqMap.put("workshopScale", workshopScale);
                }

                reqMap.put("taskEndTime", taskEndTime);
                Long currentUserId = ContextUtil.getCurUserId();
                reqMap.put("currentUserId", currentUserId);
            }

            reqMap.put("currentId", ContextUtil.getCurUserId());//登录的是创建人本人才能看
            reqMap.put("tenantId", ContextUtil.getCurUser().getmUserTypes());//虚拟的tenantid只用于判断是否雪佛龙或管理员  还是普通用户  1代表雪佛龙或管理员  0代表普通用户  modify by bo.liu 0809
            reqMap.put("orgId", ContextUtil.getCurUser().getOrgId());//add by bo.liu 0809
            reqMap.put("taskTypeCode", taskTypeCode);
            lstMainTask = mainTaskMapper.getMainTaskByTaskTypeOrParams(reqMap);

            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
            resultMap.put("resultlst", lstMainTask);
        }else
        {
            //前台需要给出提示,任务类型不能为
            resultMap.put("code", "tasktypeIsNull");
            resultMap.put("codeMsg", "参数传递错误");
        }

        }catch(Exception ex)
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error",ex);
        }
        return resultMap;
    }

    //查询执行人，，包含模糊查询
    @Override
    public Map<String, Object> findUserByPratnerIdAndName(String userNameOrLoginName){

        System.out.println("====ljc  findUserByPratnerIdAndName");
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> reqmap = new HashMap<String, Object>();
        List<Long> orgIdLst = new ArrayList<Long>();
        try {
            int total = 0;
            //Long tenantid = ContextUtil.getCurUser().getTenantId();//获取tenantid; 用于查询登录人对应的tenantid的所有用户
            WxTUser currentUser = ContextUtil.getCurUser();
            Long tenantid = currentUser.getmUserTypes();//虚拟的tenantid只用于判断是否雪佛龙或管理员  还是普通用户 1代表管理员或雪佛龙   0代表其他用户
            reqmap.put("userName", userNameOrLoginName);
            reqmap.put("userId", currentUser.getUserId());
            reqmap.put("tenantid", tenantid);
            reqmap.put("userNameFirstWord", userNameOrLoginName);
//			reqmap.put("userNameFirstWord", GetPinyinStringUtil.getPinYinHeadCharForSourceStr(userNameOrLoginName));
            if(tenantid==1)
            {
                reqmap.put("orgmodel", null);//所有

            }else
            {
                orgIdLst.add(ContextUtil.getCurUser().getOrgId());
                reqmap.put("orgmodel", "1");//单独一个
            }
            if(orgIdLst.size()==0)
            {
                orgIdLst.add(0L);
            }
            reqmap.put("orgIdLst", orgIdLst);
            List<WxTUser> listQuery = userMapper.seletUserByPratnerIdOrNameList(reqmap);
            //分组排序
            List<WxTUser> list = taskService.getUserLstGroupByUserId(listQuery);
            //分组排序

            List<SelectedUser> userList = new ArrayList<SelectedUser>();
            if (null != list && list.size() > 0) {
                for (WxTUser wxTUser : list) {
                    SelectedUser user = new SelectedUser();
                    user.setOrgId(wxTUser.getOrgId());
                    user.setUserId(wxTUser.getUserId());
                    user.setUserName(wxTUser.getChName());
                    user.setOrgName(wxTUser.getOrgName());
                    user.setBranchName(wxTUser.getBranchName());
                    user.setPostion(wxTUser.getPostionName());
                    user.setUserId(wxTUser.getUserId());
                    String chRoleNames = wxTUser.getChRoleName();
                    if(null!=chRoleNames && !chRoleNames.isEmpty())
                    {
                        user.setChRoleName((String)chRoleNames.subSequence(0, chRoleNames.length()-1));
                    }
                    userList.add(user);
                }
                total = list.size();
                map.put("resultData", userList);
            } else {
                map.put("resultData", null);
            }
            map.put("totalRecord", total);
            map.put("codeMsg", "操作成功");
            map.put("code", "success");
        } catch (Exception e) {
            map.put("code", "systemerror");
            map.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error",e);
        }
        return map;
    }

//    //查询门店，，，包含模糊查询
//    @Override
//    public Map<String, Object> getWorkShopsByTenIdAandWorkshopCodeOrName(
//            String workshopCodeOrName,String workshopStatus,String excuteUserId){
//        Map<String,Object> resultMap = new HashMap<String,Object>();
//        Map<String,Object> reqMap = new HashMap<String,Object>();
//        List<WorkshopMaster> workshoplst = new ArrayList<WorkshopMaster>();
//        try
//        {
//            Long tenantid = ContextUtil.getCurUser().getmUserTypes();//modify by bo.liu 0809    .getTenantId();//获取tenantid; 用于查询登录人对应的tenantid的相关门店
//            reqMap.put("tenantid", tenantid);//not use 可以不用了
//            reqMap.put("workshopCodeOrName", workshopCodeOrName);
//            reqMap.put("workshopStatus", workshopStatus);
//            if(excuteUserId==null || "".equals(excuteUserId))
//            {
//                excuteUserId = "0";
//            }
//            reqMap.put("excuteUserId", Long.parseLong(excuteUserId));
//            workshoplst = workShopMapper.getWorkShopListByNameOrCode_AndTenId(reqMap);
//            resultMap.put("workshoplst", workshoplst);
//            resultMap.put("code", "success");
//            resultMap.put("codeMsg", "操作成功");
//        }catch(Exception ex)
//        {
//            resultMap.put("code", "syserror");
//            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
//            log.error("error",ex);
//        }
//        return resultMap;
//    }
    @Override
    public Map<String, Object> getWorkShopsByPartnerIdAndOtherParams(String queryField,
                                                                     String workshopCodeOrName, String workshopStatus,
                                                                     String taskTypeCode,String workShopScale,String provParams,String cityParams,String distParams,String sdUserName){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        List<WorkshopMaster> workshoplst = new ArrayList<WorkshopMaster>();
        try
        {
        	WxTUser user = ContextUtil.getCurUser();
        	if(!WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel())) {
                reqMap.put("partnerId", user.getOrgId());
        	}

            if(null==workshopCodeOrName || workshopCodeOrName.trim().equals(""))
            {
                reqMap.put("workshopName", workshopCodeOrName);
            }else
            {
                reqMap.put("workshopName", workshopCodeOrName);
            }
            reqMap.put("status", workshopStatus);
            if(null==workShopScale || workShopScale.trim().equals(""))
            {
                reqMap.put("workShopScale", null);
            }else
            {
                reqMap.put("workShopScale", workShopScale);
            }
            if(provParams.equals("-1"))
            {
                reqMap.put("prov", null);
            }else
            {
                reqMap.put("prov", provParams);
            }
            if(cityParams.equals("-1"))
            {
                reqMap.put("city", null);
            }else
            {
                reqMap.put("city", cityParams);
            }

            if(distParams.equals("-1"))
            {
                reqMap.put("dist", null);
            }else
            {
                reqMap.put("dist", distParams);
            }

            reqMap.put("executeUserName", sdUserName);
            reqMap.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
            String bdWorkshopPowerTag = partnerRolePowerService.getBdWorkshopPower(ContextUtil.getCurUser().getOrgId());
            boolean isPartnerDB = user.isPartnerDB();
            //巡店时根据BD开关来判断是否查看所有门店:0 表示关：需要过滤
            if( "0".equals(bdWorkshopPowerTag) && "3".equals(workshopStatus) && isPartnerDB){
                reqMap.put("executeUserId", user.getUserId());
            }

            //添加关键字（合伙人，店名）
            if(null==queryField || queryField.trim().equals("")){
                reqMap.put("workshopName", null);
            }else{
                reqMap.put("workshopName", queryField);
            }

            log.info("reqMap: " + reqMap);
            workshoplst = workshopMasterBizService.querySimpleByParams(reqMap);
            resultMap.put("workshoplst", workshoplst);
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
        }catch(Exception ex)
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error",ex);
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getWorkShopsByPartnerIdAndParamsWithoutUserType(
            String workshopCodeOrName, String workshopStatus,String partnerId,String provParams,String cityParams,String distParams){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        List<WorkshopMaster> workshoplst = new ArrayList<WorkshopMaster>();
        try
        {
            if(partnerId != null && partnerId.equals("-1"))
            {
                reqMap.put("partnerId", null);
            }else
            {
                reqMap.put("partnerId", partnerId);
            }

            if(null==workshopCodeOrName || workshopCodeOrName.trim().equals(""))
            {
                reqMap.put("workshopName", workshopCodeOrName);
            }else
            {
                reqMap.put("workshopName", workshopCodeOrName);
            }
            reqMap.put("status", workshopStatus);

            if(provParams.equals("-1"))
            {
                reqMap.put("prov", null);
            }else
            {
                reqMap.put("prov", provParams);
            }
            if(cityParams.equals("-1"))
            {
                reqMap.put("city", null);
            }else
            {
                reqMap.put("city", cityParams);
            }

            if(distParams.equals("-1"))
            {
                reqMap.put("dist", null);
            }else
            {
                reqMap.put("dist", distParams);
            }
            reqMap.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
            log.info("reqMap: " + reqMap);
            workshoplst = workshopMasterBizService.querySimpleByParams(reqMap);
            resultMap.put("workshoplst", workshoplst);
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
        }catch(Exception ex)
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error",ex);
        }
        return resultMap;
    }


    //插入住任务--新增
    //by bo.liu 2016.07.01 事物回滚方式：
    //0.@Transactional(propagation=Propagation.REQUIRED)在方法中填写,
    //	throw new RuntimeException()在catch中抛出来;
    //	如果本来就要报错给上层那就抛出,用此种方式

    //1.@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class),
    //	TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); 在catch中
    //	让程序继续执行（不需要终止)用此种方式
    @Override
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public Map<String, Object> insertTask(WxTaskMain taskMain, String taskSteps, String attIds,Long startTimeLong, Long finishTimeLong,Integer sndMsgType){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        WxTUser currentUser =  ContextUtil.getCurUser();
        if(null==currentUser)
        {
            currentUser = new WxTUser();
            currentUser.setUserId(1L);
            currentUser.setChName("超级管理员");
        }
        String sourceTaskName = "任务";
        WxTUser texcute_user = null;
        String tasktype = taskMain.getTmbTypeCode();
        boolean isNullMdForLD = false;//add by bo.liu 是否空模板针对录店 0719
        List<Long> workshopLst = new ArrayList<Long>();
        try{
            Date startDate = null;
            Date finishDate = null;
            //1.解析数据
            if(null!=taskMain)
            {
                //add by bo.liu 1122 start
                if(null!=taskMain.getWorkshopIds() && !"".equals(taskMain.getWorkshopIds()))
                {
                    //门店对应的id
                    Long long2;
                    long workshop_id2;
                    String[] selectworkshops  = taskMain.getWorkshopIds().split(",");
                    for(int j=0;j<selectworkshops.length;j++)
                    {
                        long2 = new Long((long)Integer.parseInt(selectworkshops[j]));
                        workshop_id2 = (long)long2;
                        workshopLst.add(workshop_id2);
                    }
                    reqMap.put("workshopIds2",workshopLst);
                    if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD))
                    {
                        //判断是否已经录入了攻店任务
                        List<WxTaskHasgdWorkshop> lstHasGDWorkshops =  hasgdWorkshopMapper.selectByWorkshopIds(reqMap);
                        if(null!=lstHasGDWorkshops && !lstHasGDWorkshops.isEmpty())
                        {
                            resultMap.put("codeMsg", "此门店已经创建了攻店任务，不能再次创建");
                            resultMap.put("code", "syserror");
                            return resultMap;
                        }
                    }
                }
                //add by bo.liu 1122 end



                //add by bo.liu 20170316  执行人的所属机构（partnerid）
                texcute_user =  userMapper.selectByPrimaryKey(taskMain.getExcuteUserId());
                if(null==texcute_user)
                {
                    resultMap.put("codeMsg", "执行人不存在");
                    resultMap.put("code", "syserror");
                    return resultMap;
                }
                //任务名称
                String taskName = "";
                if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_XD))
                {
                    taskName = WxTaskMain.TASK_XD;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD))
                {
                    taskName = WxTaskMain.TASK_LD;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_SD))
                {
                    taskName = WxTaskMain.TASK_SD;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_SI))
                {
                    taskName = WxTaskMain.TASK_SI;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_CLZX)) {
                    taskName = WxTaskMain.TASK_CLZX;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_CDYJZM)) {
                    taskName = WxTaskMain.TASK_CDYJZM;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_XD_CAI)) {
                    taskName = WxTaskMain.TASK_XD_CAI;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_FLEET)) {
                    taskName = WxTaskMain.TASK_NEW_FLEET;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_FLEET_ZF)) {
                    taskName = WxTaskMain.TASK_NEW_FLEET_ZF;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_CDM_DXLQD)) {
                    taskName = WxTaskMain.TASK_CDM_DXLQD;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_GC_JX)) {
                    taskName = WxTaskMain.TASK_GC_JX;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_FEELING_GOODS)) {
                    taskName = WxTaskMain.TASK_FEELING_GOODS;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_PROJECT_CLIENT)) {
                    taskName = WxTaskMain.TASK_PROJECT_CLIENT;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_PROJECT_CLIENT_VISIT)) {
                    taskName = WxTaskMain.TASK_PROJECT_CLIENT_VISIT;
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_PROJECT_CLIENT_YJZM)) {
                    taskName = WxTaskMain.TASK_PROJECT_CLIENT_YJZM;
                }
                else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_PROJECT_FLEET)) {
                    taskName = WxTaskMain.TASK_PROJECT_FLEET;
                }
                else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_YJZM)) {
                    taskName = WxTaskMain.TASK_YJZM;
                }
                else {
                    //扩展任务
                    WxTaskMain.TaskInfo taskInfo = WxTaskMain.getAppTaskInfo(taskMain.getTmbTypeCode());
                    if(taskInfo != null) {
                        taskName = taskInfo.getTaskTypeName();
                    }
                }

                if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_SI))
                {
                    long nowtimeLong = System.currentTimeMillis();
                    //new日期对象
                    Date date = new Date(nowtimeLong);
                    //转换提日期输出格式
                    SimpleDateFormat dateFormat = new SimpleDateFormat("MMdd");
                    sourceTaskName =taskName+"-"+dateFormat.format(date);
                }else if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_XD))
                {
                    long nowtimeLong = System.currentTimeMillis();
                    //new日期对象
                    Date date = new Date(nowtimeLong);
//					if(null!=startTimeLong && sndMsgType==CREATE_TASK_NOT_SND_MSG)//ADD BY bo.liu 0720
//					{
//						date = new Date(startTimeLong);
//					}
                    //转换提日期输出格式
                    SimpleDateFormat dateFormat = new SimpleDateFormat("MMdd HH:mm");
                    sourceTaskName =taskMain.getExecuteUserName()+"-"+ taskName+"-"+dateFormat.format(date);
                }
                else
                {
                    sourceTaskName =taskMain.getExecuteUserName()+"-"+ taskName+"-"+DateUtils.getCurrentTimeForTaskName();
                }
                taskMain.setTaskName(sourceTaskName);
                taskMain.setCreateUser(currentUser.getUserId());
                taskMain.setCreateUserName(currentUser.getChName());
                taskMain.setCreateTime(new Date());
                taskMain.setTaskStatus(WxTaskMain.MAIN_TASK_STATUS_DCL);//任务状态 '0已删除，1待处理，2 处理中，3待验收，4办结',
                taskMain.setCreateSource(1);//任务创建来源 1手工创建  2 任务调度自动创建'
                taskMain.setTaskCreateUser(currentUser.getUserId());
                taskMain.setStatus(1);//'0已删除，1正常，'
                taskMain.setTenantId(texcute_user.getOrgId());//modify by bo.liu 0818 currentUser.getTenantId()
                if (startTimeLong != null) {
                    startDate = DateUtils.long2date(startTimeLong);
                    taskMain.setTaskStartTime(startDate);
                }
                if (finishTimeLong != null) {
                    finishDate = DateUtils.long2date(finishTimeLong);
                    taskMain.setTaskFinishTime(finishDate);
                }

                if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD))
                {
                    //其实针对录店而言
                    taskMain.setTaskStartTime(new Date());
                    taskMain.setTaskFinishTime(DateUtils.addCurrentDateByNYear(System.currentTimeMillis(), 1));
                }

            }
            //2.判断是否存在创建任务的权限
            if (WxTaskMain.FrequencyType_More.compareTo(taskMain.getFrequencyType()) != 0) {//单次
                //计算完成天数
                int finishDay = DateUtil.daysBetween(startDate, finishDate);
                taskMain.setFinishDay(finishDay+1);
            }

            //3.插入主任务数据
            int task_main_id = mainTaskMapper.insertSelective(taskMain);
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");

            // 如果是周期性任务 则插入quartz信息
            if (WxTaskMain.FrequencyType_More.compareTo(taskMain.getFrequencyType()) == 0) {// 是多次任务
                if (startTimeLong != null && finishTimeLong != null) {
                    String jobStatus = ScheduleJob.STATUS_RUNNING;
                    // 如果主任务为暂存 则置调度为不运行 否则 则置为运行
                    if (taskMain.getPublishStatus() == WxTaskMain.Publish_Temp) {
                        jobStatus = ScheduleJob.STATUS_NOT_RUNNING;
                    }
                    jobTaskService.addRepetitionTask(startDate, finishDate,
                            taskMain.getRepetitionType(),
                            taskMain.getRepetitionFrequency(),
                            taskMain.getTaskMainId(), jobStatus,currentUser.getUserId(),currentUser.getChName(),sourceTaskName);
                }
            }

            if(task_main_id>0)
            {
                //主任务id
                long main_task_id;
                main_task_id = taskMain.getTaskMainId();//mainTaskMapper.getMainIds(); modify by bo.liu 0816

                //门店对应的id
                Long long3;
                long workshop_id;
                String[] stepValues = null;
                if((null==taskSteps || taskSteps.isEmpty()) && taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD))//mod by bo.liu 0719
                {
                    isNullMdForLD = true;
                    log.info("---TASK_TYPE_LD stepValues is null 1--");
                }else
                {
                    stepValues = taskSteps.split(",");
                }
                //4.0批量更新附件数据表（知道附件的sourceid对应的主任务id）
                if(!attIds.equals("") && !attIds.equals(","))
                {
                    attIds = attIds.substring(0, attIds.length()-1);
                    String attids[] = attIds.split(",");
                    if(attids.length>0)
                    {
                        Long[] attIDS = new Long[attids.length];
                        for(int i=0; i<attIDS.length;i++)
                        {
                            attIDS[i] = new Long((long)Integer.parseInt(attids[i]));
                        }
                        reqMap.put("taskMainId", main_task_id);
                        reqMap.put("attIds", attIDS);
                        wxAttFileMapper.updateWxAttFilesSetTaskMainId(reqMap);
                    }
                }


                //4.1插入wx_taskmb_step  即主任务关联步骤
                int reusltCode = 0;
                if(!isNullMdForLD)
                {
                    reusltCode = taskService.insertBatchTaskSteps(main_task_id, stepValues);
                }
                if(reusltCode>0 || isNullMdForLD)//mod by bo.liu 0719 (reusltCode>0)
                {
                    //巡店，录店的时候需要插入子任务，，，竞平，库存信息等
                    //5.插入成功后插入子任务数据
                    List<WxTaskSub> taskSublst = new ArrayList<WxTaskSub>();
                    //add by bo.liu 20170316  执行人的所属机构（partnerid）
					/*WxTUser texcute_user =  userMapper.selectByPrimaryKey(taskMain.getExcuteUserId());
					if(null==texcute_user)
					{
						resultMap.put("codeMsg", "执行人不存在");
						resultMap.put("code", "syserror");
						return resultMap;
					}*///delete by bo.liu 20170329
                    {
                        if(null!=taskMain.getWorkshopIds() && !"".equals(taskMain.getWorkshopIds()))
                        {
                            String[] selectworkshops  = taskMain.getWorkshopIds().split(",");
                            if(selectworkshops.length>TOTAL_WORKSHOP)
                            {
                                throw new Exception("最多只能选择门店个数："+TOTAL_WORKSHOP);
                            }

                            for(int j=0;j<selectworkshops.length;j++)
                            {
                                long3 = new Long((long)Integer.parseInt(selectworkshops[j]));
                                workshop_id = (long)long3;
                                WxTaskSub taskSub = new WxTaskSub();
                                taskSub.setAttribute3(null == taskMain.getShopOrFleetType()?null:taskMain.getShopOrFleetType().toString());
                                taskSub.setTaskMainId(main_task_id);
                                taskSub.setTaskStartTime(taskMain.getTaskStartTime());
                                taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
                                taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
                                taskSub.setExecUser(taskMain.getExcuteUserId());
                                taskSub.setOrgId(workshop_id);//门店id
                                taskSub.setTaskRemark(taskMain.getTaskDescription());
                                taskSub.setRecfityCount(0);//整改次数，默认是0
                                taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
                                taskSub.setTenantId(texcute_user.getOrgId());//by bo.liu 0818 currentUser.getTenantId()
                                taskSub.setXgSj(new Date());
                                taskSub.setInStockNo(taskMain.getInStockNo());
                                taskSub.setOutStockNo(taskMain.getOutStockNo());
                                taskSub.setOrderNo(taskMain.getOrderNo());
                                taskSub.setBrand(taskMain.getBrand());
                                taskSublst.add(taskSub);
                            }
                        }else if(null!=taskMain.getWorkShopId() && !"".equals(taskMain.getWorkShopId()))
                        {
                            WxTaskSub taskSub = new WxTaskSub();
                            taskSub.setAttribute3(null == taskMain.getShopOrFleetType()?null:taskMain.getShopOrFleetType().toString());
                            taskSub.setTaskMainId(main_task_id);
                            taskSub.setTaskStartTime(taskMain.getTaskStartTime());
                            taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
                            taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
                            taskSub.setExecUser(taskMain.getExcuteUserId());
                            taskSub.setOrgId(taskMain.getWorkShopId());//门店的id
                            taskSub.setTaskRemark(taskMain.getTaskDescription());
                            taskSub.setRecfityCount(0);//整改次数，默认是0
                            taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
                            taskSub.setTenantId(texcute_user.getOrgId());//by bo.liu 0818 currentUser.getTenantId()
                            taskSub.setXgSj(new Date());
                            taskSub.setInStockNo(taskMain.getInStockNo());
                            taskSub.setOutStockNo(taskMain.getOutStockNo());
                            taskSub.setOrderNo(taskMain.getOrderNo());
                            taskSub.setBrand(taskMain.getBrand());
                            taskSublst.add(taskSub);
                        }else if(null!=taskMain.getFleetIds() && !"".equals(taskMain.getFleetIds()))
                        {
                            //子任务批量org_id插入车队id
                            String[] selectFleetIds  = taskMain.getFleetIds().split(",");
                            if(selectFleetIds.length>TOTAL_WORKSHOP)
                            {
                                throw new Exception("最多只能选择车队个数："+TOTAL_WORKSHOP);
                            }

                            for(int j=0;j<selectFleetIds.length;j++)
                            {
                                long fleet_id = new Long((long)Integer.parseInt(selectFleetIds[j]));
                                WxTaskSub taskSub = new WxTaskSub();
                                taskSub.setAttribute3(null == taskMain.getShopOrFleetType()?null:taskMain.getShopOrFleetType().toString());
                                taskSub.setTaskMainId(main_task_id);
                                taskSub.setTaskStartTime(taskMain.getTaskStartTime());
                                taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
                                taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
                                taskSub.setExecUser(taskMain.getExcuteUserId());
                                taskSub.setOrgId(fleet_id);//车队id
                                taskSub.setTaskRemark(taskMain.getTaskDescription());
                                taskSub.setRecfityCount(0);//整改次数，默认是0
                                taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
                                taskSub.setTenantId(texcute_user.getOrgId());//by bo.liu 0818 currentUser.getTenantId()
                                taskSub.setXgSj(new Date());
                                taskSub.setInStockNo(taskMain.getInStockNo());
                                taskSub.setOutStockNo(taskMain.getOutStockNo());
                                taskSub.setOrderNo(taskMain.getOrderNo());
                                taskSub.setBrand(taskMain.getBrand());
                                taskSublst.add(taskSub);
                            }
                        }else if(null!=taskMain.getFleetId() && !"".equals(taskMain.getFleetId())) {
                            //子任务orgId插入车队的信息Id
                            WxTaskSub taskSub = new WxTaskSub();
                            taskSub.setAttribute3(null == taskMain.getShopOrFleetType()?null:taskMain.getShopOrFleetType().toString());
                            taskSub.setTaskMainId(main_task_id);
                            taskSub.setTaskStartTime(taskMain.getTaskStartTime());
                            taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
                            taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
                            taskSub.setExecUser(taskMain.getExcuteUserId());
                            taskSub.setOrgId(taskMain.getFleetId());//门店的id
                            taskSub.setTaskRemark(taskMain.getTaskDescription());
                            taskSub.setRecfityCount(0);//整改次数，默认是0
                            taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
                            taskSub.setTenantId(texcute_user.getOrgId());//by bo.liu 0818 currentUser.getTenantId()
                            taskSub.setXgSj(new Date());
                            taskSub.setInStockNo(taskMain.getInStockNo());
                            taskSub.setOutStockNo(taskMain.getOutStockNo());
                            taskSub.setOrderNo(taskMain.getOrderNo());
                            taskSub.setBrand(taskMain.getBrand());
                            taskSublst.add(taskSub);
                        }else {
                            WxTaskSub taskSub = new WxTaskSub();
                            taskSub.setAttribute3(null == taskMain.getShopOrFleetType()?null:taskMain.getShopOrFleetType().toString());
                            taskSub.setTaskMainId(main_task_id);
                            taskSub.setTaskStartTime(taskMain.getTaskStartTime());
                            taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
                            taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
                            taskSub.setExecUser(taskMain.getExcuteUserId());
                            taskSub.setOrgId(0L);//门店的id
                            taskSub.setTaskRemark(taskMain.getTaskDescription());
                            taskSub.setRecfityCount(0);//整改次数，默认是0
                            taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
                            taskSub.setTenantId(texcute_user.getOrgId());//by bo.liu 0818 currentUser.getTenantId()
                            taskSub.setXgSj(new Date());
                            taskSub.setInStockNo(taskMain.getInStockNo());
                            taskSub.setOutStockNo(taskMain.getOutStockNo());
                            taskSub.setOrderNo(taskMain.getOrderNo());
                            taskSub.setBrand(taskMain.getBrand());
                            taskSublst.add(taskSub);
                        }
                    }


                    //end
                    int insertResultCode = subtaskMapper.insertWxTaskSubBatch(taskSublst);
                    if(insertResultCode<0)
                    {
                        resultMap.put("codeMsg", "插子任务失败");
                        resultMap.put("code", "syserror");
                    }else{
                        //插入任务实例
                        Map<String,Object> taskAboutInsertMap = new HashMap<String, Object>();
                        taskAboutInsertMap.put("taskMainId", main_task_id);

                        if(isNullMdForLD)//add by bo.liu 0719
                        {
                            log.info("---TASK_TYPE_LD stepValues is null 2--");
                        }else
                        {
                            int insertInstanceCode = instanceCheckVoMapper.insertSubTaskInstance(taskAboutInsertMap);
                            if(insertInstanceCode<0)
                            {
                                resultMap.put("codeMsg", "插子任务实例失败");
                                resultMap.put("code", "syserror");
                            }
                        }

                        //巡店,通用任务情况下,需要检查是否录入竞品/库存订单信息
                        if(!taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD) && !taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_SD))
                        //不是录店和扫店的情况[巡店任务类型和通用任务类型]if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_XD))
                        {
                            //插入任务对应竞品（如果有竞品收集）
                            taskService.getStepContainCompeteGoodsOrProductsInfo(stepValues);
                            if(TaskService.isContainCompeteGoods)
                            {
                                //录入竞品表中，mod by bo.liu 1102
                                taskService.insertCompeteGoodsBatch(taskAboutInsertMap);
								/* int competeResultCode = taskCompeteGoodsVoMapper.insertTaskCompeteCoodsBatch(taskAboutInsertMap);
								 if(competeResultCode<0)
								{
									resultMap.put("codeMsg", "插子任务——竞品信息失败");
									resultMap.put("code", "syserror");
									return resultMap;
								}*/
                            }

                            //插入任务对应库存（如果有库存收集）
                            if(TaskService.isContainProductInfo)
                            {
                                //录入产品信息  mod by bo.liu 1102
                                taskService.insertTaskProductInfoBatch(taskAboutInsertMap);
								/*int insertTaskProductInfoResultCode = taskProductInfoMapper.insertTaskProductInfoBatch(taskAboutInsertMap);
								if(insertTaskProductInfoResultCode<0)
								{
									resultMap.put("codeMsg", "插入任务——库存订单信息失败");
									resultMap.put("code", "syserror");
									return resultMap;
								}*/
                            }
                        }

                        //add by 0811 start
                        {
                            if(null!=taskMain.getWorkshopIds() && !"".equals(taskMain.getWorkshopIds()))
                            {
								/*List<Long> workshopLst = new ArrayList<Long>();
								String[] selectworkshops  = taskMain.getWorkshopIds().split(",");
								for(int j=0;j<selectworkshops.length;j++)
								{
									long3 = new Long((long)Integer.parseInt(selectworkshops[j]));
									workshop_id = (long)long3;
									workshopLst.add(workshop_id);
								}*///delete by bo.liu 1122
                                reqMap.put("workshopIds",workshopLst);

//                                //add by bo.liu 1207 只有针对攻店任务的时候，指定门店的执行人，同时门店状不发生改变
//                                if(taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD))
//                                {
//                                    reqMap.put("excuteUser", taskMain.getExcuteUserId());
//                                    reqMap.put("status",null);
//                                    reqMap.put("mnowTime", new Date());//更新门店时间
//                                    //批量更新
//                                    workShopMapper.updateWorkShopsById(reqMap);
//
//                                    //需要往wx_v_hasgd_workshop插入对应的门店id,,记录已经创建了攻店任务
////									hasgdWorkshopMapper.insertBatch(reqMap);
//
//                                }

                            }

                        }
                    }

                }else
                {
                    resultMap.put("codeMsg", "插入任务步骤关联失败");
                    resultMap.put("code", "syserror");
                }


            }else
            {
                resultMap.put("codeMsg", "插入主任务失败");
                resultMap.put("code", "syserror");
            }

            //推送消息
            if(sndMsgType==CREATE_TASK_SND_MSG)
            {

                String sndMsg = "";
                if(tasktype.equals(WxTaskMain.TASK_TYPE_XD))
                {
                    sndMsg = TASK_MSG_CONTENT_XD;
                }else if(tasktype.equals(WxTaskMain.TASK_TYPE_LD))
                {
                    sndMsg = TASK_MSG_CONTENT_GD;
                }else if(tasktype.equals(WxTaskMain.TASK_TYPE_SD))
                {
                    sndMsg = TASK_MSG_CONTENT_SD;
                }else if(tasktype.equals(WxTaskMain.TASK_TYPE_SI))
                {
                    sndMsg = TASK_MSG_CONTENT_SI;
                }
                List<Long> userLst = new ArrayList<Long>();
                /*userLst.add(taskMain.getExcuteUserId());*/
                //查找门店最新执行人
                String workshopIds = taskMain.getWorkshopIds();
                Map<String, Object> params = new HashMap<String, Object>(5);
                params.put("ids", workshopIds.split(","));
                List<WorkshopMaster> list = workshopMasterBizService.querySimpleByParams(params);
                for(WorkshopMaster workshop : list){
                    if(workshop.getExcuteUserId() != null){
                        userLst.add(workshop.getExcuteUserId());
                    }
                }

                log.info("---insert task pushMessage start--");
                msgPushService.pushMessage(WxPushMessage.MSG_TYPE_PERSONAL,TASK_MSG_TITLE, sndMsg,null, userLst);
                log.info("---insert task pushMessage end--");
            }


        }catch(Exception ex)
        {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception")+"错误信息:"+ex.getMessage());
            resultMap.put("code", "syserror");
            log.error("error",ex);
        }

        return resultMap;
    }

    //获取子任务相关信息
    @Override
    public Map<String, Object> getSubTaskListInfo(String taskMainId,String mtaskMainTypecode,
                                                  String workShopNameOrId, SubTaskQueryConditions subTaskMultiParamsQuery){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        try
        {
            //0.获取对应主任务相关信息，用于页面显示
            WxTaskMain mainTaskObj = mainTaskMapper.getMainTaskById(Long.parseLong(taskMainId));

            //1.查询子任务列表信息
            reqMap.put("taskMainId",taskMainId);
            reqMap.put("workShopNameOrId", workShopNameOrId);
            reqMap.put("subTaskMultiParamsQuery", subTaskMultiParamsQuery);
            List<WxTaskSub> taskSublst = subtaskMapper.getWxTaskSubList(reqMap);

            //2.针对扫店获取门头照信息
            if(mtaskMainTypecode.equals(WxTaskMain.TASK_TYPE_SD))
            {
                //获取门头照/名片照/其他照片  检查项对应的 sourceid
                List<TaskInstanceCheckVo> taskInstanceChecks= instanceCheckVoMapper.getSubTaskStepCheckInstanceForSDNew(reqMap);//instanceCheckVoMapper.getSubTaskStepCheckInstanceForSD(reqMap);

                //获取对应sourceid 且类型sourcetype为‘3’的所有附件（图片）
                List<Long> taskInstanceIdlst = new ArrayList<Long>();//门头照附件
                List<Long> businessCardsInstanceIdlst = new ArrayList<Long>();
                List<Long> otherPhotosInstanceIdlst = new ArrayList<Long>();
                for(TaskInstanceCheckVo taskCheckVo : taskInstanceChecks)
                {
                    if(taskCheckVo.getCheckCode().equals(WxTaskSub.WORKSHOP_PHOTO_CHECK_CODE))
                    {
                        taskInstanceIdlst.add(taskCheckVo.getId());
                    }else if(taskCheckVo.getCheckCode().equals(WxTaskSub.BUSINESS_PHOTO_CHECK_CODE))
                    {
                        businessCardsInstanceIdlst.add(taskCheckVo.getId());
                    }else if(taskCheckVo.getCheckCode().equals(WxTaskSub.OTHDERS_PHOTO_CHECK_CODE))
                    {
                        otherPhotosInstanceIdlst.add(taskCheckVo.getId());
                    }
                }


                reqMap.put("sourceType",WxAttFile.SourceType_Check);

                //获取门头照
                if(taskInstanceIdlst.size()>0)
                {
                    //taskInstanceIdlst = null;

                    reqMap.put("instanceIdlst", taskInstanceIdlst);
                    List<WxAttFile> wxAttFileLst = wxAttFileMapper.getWxAttFiles(reqMap);
                    for(WxTaskSub taskSub :  taskSublst)
                    {
                        //delete by bo.liu 1013
                        //由于扫店只有一条任务实例或子任务
						/*if(null!=wxAttFileLst && wxAttFileLst.size()>0)
						{
							taskSub.setMattFileList(wxAttFileLst);
						}*/

                        //add by bo.liu 1013
                        List<WxAttFile> tempwxAttFileLst = new ArrayList<WxAttFile>();
                        for(WxAttFile wxAttFile:wxAttFileLst)
                        {
                            if(taskSub.getTaskId().equals(wxAttFile.getSubTaskId()))
                            {
                                tempwxAttFileLst.add(wxAttFile);
                            }
                        }
                        taskSub.setMattFileList(tempwxAttFileLst);
                    }
                }else
                {
                    for(WxTaskSub taskSub :  taskSublst)
                    {

                        taskSub.setMattFileList(new ArrayList<WxAttFile>());
                    }
                }

                //获取名片照
                if(businessCardsInstanceIdlst.size()>0)
                {
                    reqMap.put("instanceIdlst", businessCardsInstanceIdlst);
                    List<WxAttFile> wxBusinessCardsAttFileLst = wxAttFileMapper.getWxAttFiles(reqMap);
                    for(WxTaskSub taskSub :  taskSublst)
                    {
                        //delete by bo.liu 1013
                        //由于扫店只有一条任务实例或子任务
						/*if(null!=wxBusinessCardsAttFileLst && wxBusinessCardsAttFileLst.size()>0)
						{
							taskSub.setmBusinessCardsFileList(wxBusinessCardsAttFileLst);
						}*/
                        //add by bo.liu 1013
                        List<WxAttFile> tempwxAttFileLst = new ArrayList<WxAttFile>();
                        for(WxAttFile wxAttFile:wxBusinessCardsAttFileLst)
                        {
                            if(taskSub.getTaskId().equals(wxAttFile.getSubTaskId()))
                            {
                                tempwxAttFileLst.add(wxAttFile);
                            }
                        }
                        taskSub.setmBusinessCardsFileList(tempwxAttFileLst);

                    }
                }else
                {
                    for(WxTaskSub taskSub :  taskSublst)
                    {

                        taskSub.setmBusinessCardsFileList(new ArrayList<WxAttFile>());
                    }
                }

                //获取其他照片
                if(otherPhotosInstanceIdlst.size()>0)
                {
                    //otherPhotosInstanceIdlst = null;
                    reqMap.put("instanceIdlst", otherPhotosInstanceIdlst);
                    List<WxAttFile> wxOtherPhotosAttFileLst = wxAttFileMapper.getWxAttFiles(reqMap);
                    for(WxTaskSub taskSub :  taskSublst)
                    {
                        //delete by bo.liu 1013
                        //由于扫店只有一条任务实例或子任务
						/*if(null!=wxOtherPhotosAttFileLst && wxOtherPhotosAttFileLst.size()>0)
						{
							taskSub.setmOtherPhotosFileList(wxOtherPhotosAttFileLst);
						}*/

                        //add by bo.liu 1013
                        List<WxAttFile> tempwxAttFileLst = new ArrayList<WxAttFile>();
                        for(WxAttFile wxAttFile:wxOtherPhotosAttFileLst)
                        {
                            if(taskSub.getTaskId().equals(wxAttFile.getSubTaskId()))
                            {
                                tempwxAttFileLst.add(wxAttFile);
                            }
                        }
                        taskSub.setmOtherPhotosFileList(tempwxAttFileLst);
                    }
                }else
                {
                    for(WxTaskSub taskSub :  taskSublst)
                    {

                        taskSub.setmOtherPhotosFileList(new ArrayList<WxAttFile>());
                    }
                }
            }

            //3.用于前台使用
            resultMap.put("taskSublst", taskSublst);
            resultMap.put("codeMsg", "操作成功");
            resultMap.put("code", "success");
            if(taskSublst.size()>0 && (mtaskMainTypecode.equals(WxTaskMain.TASK_TYPE_SD) || mtaskMainTypecode.equals(WxTaskMain.TASK_TYPE_LD)))
            {
                mainTaskObj.setWorkShopId(taskSublst.get(0).getWorkShopId());
                mainTaskObj.setWorkShopName(taskSublst.get(0).getWorkShopName());
                mainTaskObj.setWorkShopStatus(taskSublst.get(0).getWorkshopStatus());
                mainTaskObj.setWorkshopExcuteUserId(taskSublst.get(0).getWorkshopExcuteUserId());
            }
            resultMap.put("taskMainObj", mainTaskObj);

        }catch(Exception ex)
        {
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "error");
            log.error("error",ex);
        }

        return resultMap;
    }

    //获取子任务---步骤检查项信息(从任务实例中获取)
    @Override
    public Map<String, Object> getSubTaskStepCheckInfoByMainIdAndSubId(String taskMainId, String taskSubId){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        try{

            reqMap.put("taskMainId", taskMainId);
            reqMap.put("taskSubId", taskSubId);
            //List<StepCheckVo> lstStepCheck =   instanceCheckVoMapper.getSubTaskStepCheckInstance(reqMap);
            //关联查询获取，，当前任务对应的实例，从表wx_task_instance_check表中获取
            List<TaskInstanceCheckVo> lstStepCheck =  instanceCheckVoMapper.getSubTaskStepCheckInstance(reqMap);

            //根据检查项ID获取检查项对应的图片信息（从wx_att_file表中），然后加载到对应的检查项上

            //0.新增过滤条件（从instance中获取合法的 检查项ID,,用于到wx_att_file表中去查找是否存在对应的图片信息）
            List<Long> taskInstanceIdlst = new ArrayList<Long>();
            for(TaskInstanceCheckVo taskInstanceCheckVo:lstStepCheck)
            {
                if(taskInstanceCheckVo.getPhotoType()==1)//如果要求上传图片就添加到过滤中，
                {
                    taskInstanceIdlst.add(taskInstanceCheckVo.getId());
                }
            }
            //1.从wx_att_file表中获取，符合条件的检查项id对应的---图片列表
            List<WxAttFile> wxAttFileLst = null;
            if(taskInstanceIdlst.isEmpty())
            {
                wxAttFileLst = Collections.emptyList();
            }else {
                reqMap.put("instanceIdlst", taskInstanceIdlst);
                reqMap.put("sourceType",WxAttFile.SourceType_Check);
                wxAttFileLst = wxAttFileMapper.getWxAttFiles(reqMap);
            }
            //2.与对应的任务检查实例集合进行一一对比，将对应的附件指定给对应的所属检查项的对象上
            List<TaskInstanceCheckVo> lstStepCheckNew = new ArrayList<TaskInstanceCheckVo>(); //构建新的检查项实例列表

            List<WxAttFile> setAttfilesToInstanceCheckVoLst = new ArrayList<WxAttFile>();
            for(TaskInstanceCheckVo newTaskInstanceCheckVo:lstStepCheck)
            {
                for(WxAttFile attFile:wxAttFileLst)
                {
                    if(newTaskInstanceCheckVo.getPhotoType() == 1)
                    {
                        if(newTaskInstanceCheckVo.getId().equals(attFile.getSourceId()))
                        {
                            setAttfilesToInstanceCheckVoLst.add(attFile);
                        }
                    }
                }

                if(newTaskInstanceCheckVo.getPhotoType() == 1)
                {
                    newTaskInstanceCheckVo.setAttFileList(setAttfilesToInstanceCheckVoLst);
                }
                lstStepCheckNew.add(newTaskInstanceCheckVo);
                setAttfilesToInstanceCheckVoLst = new ArrayList<WxAttFile>();
            }


            List<TaskInstanceCheckVoTree> lstStepTree = new ArrayList<TaskInstanceCheckVoTree>();//放置的tree对象的集合
            TaskInstanceCheckVoTree instanceCheckTree = null;//新的tree对象
            List<TreeNode> currentStepTree = null;//1个数对象对应N个实例对象（一个步骤对应N个检查项，，用于存放相同步骤的实例对象）
            Long lastTaskStepId = 0L;//控制->原始的步骤检查项
            Long currentTaskStepId = 0L;
            Long lastTreeLstId = 0L;//控制->新实例对象

            String lastStepName = "";
            String currentStepName = "";

            //分组归类{步骤:ss;检查项:[,,,]}
            Collections.sort(lstStepCheckNew, new Comparator<TaskInstanceCheckVo>() {
                public int compare(TaskInstanceCheckVo o1, TaskInstanceCheckVo o2) {
                    return o1.getStepId().compareTo(o2.getStepId());
                }
            });
            for(TaskInstanceCheckVo taskInstanceCheckVo:lstStepCheckNew)//包含了附件信息 lstStepCheck
            {
                taskInstanceCheckVo.setText(taskInstanceCheckVo.getCheckName());//设置，用于UI上显示
                currentTaskStepId = taskInstanceCheckVo.getStepId();
                String stepName = taskInstanceCheckVo.getStepName();
                currentStepName = stepName;
                if(!lastStepName.equals(currentStepName))
                {
                    instanceCheckTree = new TaskInstanceCheckVoTree();//不等的时候新的实例对象
                    currentStepTree = new ArrayList<TreeNode>();
                    instanceCheckTree.setStepId(currentTaskStepId);
                    instanceCheckTree.setText(taskInstanceCheckVo.getStepName());//设置用于UI上显示
                    instanceCheckTree.setStepName(taskInstanceCheckVo.getStepName());
                }

                currentStepTree.add(taskInstanceCheckVo);
//				lastTaskStepId = currentTaskStepId ;
                instanceCheckTree.setChildren(currentStepTree);

                if(!lastStepName.equals(currentStepName))
                {
                    lstStepTree.add(instanceCheckTree);
                }
                lastTaskStepId = currentTaskStepId ;
                lastStepName = currentStepName;
                //lastTreeLstId = currentTaskStepId;
            }
            //判断是否是车队业绩证明，如果是需要单独处理一下数据

            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
            resultMap.put("lstStepCheck", lstStepTree);

        }catch(Exception ex)
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", "系统出错");
            log.error("error",ex);
        }
        return resultMap;
    }


    //子任务详情后，抽查，，更新任务实例，主任务，子任务状态--更新
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    public Map<String,Object> updateSubTaskInstanceAndTaskMainOrSubTaskStatus(TaskInstanceSubmitUpdate taskSubInstances,String mainTaskId ,String subTaskId,int checkCount)
    {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        WxTUser currentUser =  ContextUtil.getCurUser();
        boolean isTongGuo = true;
        resultMap.put("code", "success");
        resultMap.put("codeMsg", "操作成功");
        try
        {
            List<TaskInstanceCheckUpdateVo> lstTaskInstanceVo = taskSubInstances.getLstTaskInstanceCheckVo();
            for(TaskInstanceCheckUpdateVo taskInstanceVo : lstTaskInstanceVo)
            {
                if(taskInstanceVo.getCheckValue().equals(TaskInstanceCheckVo.INSTANCE_CHECK_BTG))
                {
                    isTongGuo = false;
                    break;
                }
            }
            if(null!=lstTaskInstanceVo && lstTaskInstanceVo.size()>0)
            {	//0.更新任务实例表中的数据,状态？
                reqMap.put("lstUpdateTaskInstance", lstTaskInstanceVo);
                reqMap.put("xgUser", currentUser.getUserId());
                int records = instanceCheckVoMapper.updateSubTaskInstanceBatch(reqMap);
                if(records>0)
                {
                    //1.通过与否，都要更新子任务状态为不通过,,，，且抽查次数需要更新，，
                    reqMap.put("checkCount", checkCount+1);
                    if(!isTongGuo)
                    {
                        reqMap.put("taskstatus", WxTaskSub.SUB_TAST_STATUS_DZG);//不通过，待整改
                    }else
                    {
                        reqMap.put("taskstatus", WxTaskSub.SUB_TASK_STATUS_BJ);//通过，办结
                    }
                    reqMap.put("subTaskId", subTaskId);
                    reqMap.put("checktask", "1");
                    int subtaskresult = subtaskMapper.updateWxTaskSub(reqMap);
                    if(subtaskresult>0)
                    {
                        //2.更新主任务
                        reqMap.put("mainTaskId", mainTaskId);
                        int mainresult = 0;
                        if(!isTongGuo)//如果不通过
                        {
                            reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_DZG);//不通过，直接待整改
                            mainresult = mainTaskMapper.updateWxTaskMainStatus(reqMap);
                        }else//如果通过，需要查询主任务下其他子任务是否通过或者办结，，，
                        //如果其他子任务为非办结，即修改为待整改，如果其他已经办结，修改任务主任务为办结
                        {
                            //0.先查询对应主任务，其他子任务是否办结？
                            boolean isContainNot_BJ_SubTask = false;//存在是否没有办结的任务
                            List<WxTaskSub> lstSubTask = subtaskMapper.getWxTaskSubByTaskMainId(reqMap);
                            for(WxTaskSub subTask:lstSubTask)
                            {
                                if(!subTask.getTaskStatus().equals(WxTaskMain.MAIN_TASK_STATUS_BJ))
                                {
                                    isContainNot_BJ_SubTask = true;//存在没有办结
                                    break;
                                }
                            }

                            if(isContainNot_BJ_SubTask)//存在没有办结的子任务
                            {
                                reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_DZG);//更改为待整改
                            }else
                            {
                                reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_BJ);//办结
                            }
                            mainresult = mainTaskMapper.updateWxTaskMainStatus(reqMap);
                        }

                        if(mainresult<0)
                        {
                            resultMap.put("codeMsg", "更新主任务状态失败");
                            resultMap.put("code", "syserror");
                        }

                    }else
                    {
                        resultMap.put("codeMsg", "更新子任务状态失败");
                        resultMap.put("code", "syserror");
                    }

                }else
                {
                    resultMap.put("codeMsg", "更新检查项失败");
                    resultMap.put("code", "syserror");
                }
            }

        }catch(Exception ex)
        {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");

            log.error("error",ex);

        }

        return resultMap;
    }


    //获取子任务对应的  竞品信息
    @Override
    public Map<String, Object> getTaskCompeteGoodsBySubTaskId(String taskSubId){
        Map<String,Object> resultMap = new HashMap<String, Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {	if(null!=taskSubId && !taskSubId.equals(""))
        {
            reqMap.put("subTaskId", taskSubId);
            List<TaskCompeteGoodsVo> taskCompeteGoodsLst = taskCompeteGoodsVoMapper.getCompeteGoodsBySubTaskId(reqMap);
            resultMap.put("competeGoodsLst", taskCompeteGoodsLst);
        }else
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", "参数传递错误");
        }

        }catch(Exception ex)
        {
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",ex);
        }
        return resultMap;
    }

    //获取子任务对应的 库存/订单信息
    @Override
    public Map<String, Object> getTaskProductInfoBySubTaskId(String taskSubId){
        Map<String,Object> resultMap = new HashMap<String, Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {	if(null!=taskSubId && !taskSubId.equals(""))
        {
            reqMap.put("subTaskId", taskSubId);
            List<TaskProductInfoVo> taskProductInfoLst = taskProductInfoMapper.getProductsInfoBySubTaskId(reqMap);
            resultMap.put("taskProductInfoLst", taskProductInfoLst);
        }else
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", "参数传递错误");
        }

        }catch(Exception ex)
        {
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",ex);
        }
        return resultMap;
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public Map<String, Object> updateSubTaskStatus(int taskIsPass,
                                                   String checkResult, String mainTaskId, String subTaskId,
                                                   int checkCount, String mtaskTypeCode, String mworkshopId) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> reqMap = new HashMap<String, Object>();

        try {
            WxTUser currentUser = ContextUtil.getCurUser();
            reqMap.put("taskIsPass", taskIsPass);
            reqMap.put("checkResult", checkResult);
            reqMap.put("xgUser", currentUser.getUserId());
            reqMap.put("checkCount", checkCount + 1);
            reqMap.put("subTaskId", subTaskId);
            reqMap.put("checktask", "1");
            reqMap.put("xgSj", 1);


            WxTaskMain taskMain = mainTaskMapper.selectByPrimaryKey(Long.valueOf(mainTaskId));
            WxTaskSub taskSub = subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId));

            //处理抽查 不合格场景
            if (taskIsPass == 0) {
                //1.德乐车队业绩证明特殊逻辑处理
                if (WxTaskMain.TASK_TYPE_CDYJZM.equals(taskMain.getTmbTypeCode())) {
                    reqMap.put("checkEvaluation", 3);
                }

                //2.修改子任务状态为 不通过，待整改
                reqMap.put("taskstatus", WxTaskSub.SUB_TAST_STATUS_DZG);

            }

            //处理抽查 合格场景
            if (taskIsPass == 1) {

                //德乐车队业绩证明特殊逻辑处理
                if (WxTaskMain.TASK_TYPE_CDYJZM.equals(taskMain.getTmbTypeCode())) {
                    reqMap.put("checkEvaluation", 3);
                }

                //修改子任务状态为通过
                reqMap.put("taskstatus", WxTaskSub.SUB_TASK_STATUS_BJ);

                if ((WxTaskMain.TASK_NEW_WORKSHOP.equals(taskMain.getTmbTypeCode()) || WxTaskMain.TASK_NEW_FLEET_PROJECT.equals(taskMain.getTmbTypeCode()))) {
//                    //更改门店状态为合格状态
//                    WorkshopMaster mWorkshopMaster = new WorkshopMaster();
//                    mWorkshopMaster.setId(taskSub.getOrgId());
//                    mWorkshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS3);//合格
//                    mWorkshopMaster.setActivationTime(new Date());
//                    workshopMasterBizService.update(mWorkshopMaster);

                    //记录客户MKT抽查合格过渡状态
                    workshopStatusBizService.insertWhenNotExists(taskSub.getOrgId(), WorkshopMaster.WORKSHOP_STATUS_MKT_CHECKED.toString(),
                            Long.valueOf(subTaskId), DateUtil.getCurrentDate());
                    
//                    if(WxTaskMain.TASK_NEW_WORKSHOP.equals(taskMain.getTmbTypeCode()) 
//                    		&& taskSub.getBrand().equals(Constants.BRAND_CDM) 
//                    		&& "Y".equals(Constants.getSystemPropertyByCodeType("Task.synB2bMechnicOn"))) {
//                         //注册店员/店主
//                        makeUpEmployeeInfo(mWorkshopMaster.getId(), taskMain.getTenantId(),taskSub.getExecUser());
//                    }
                }

            }

            //处理抽查 待完善场景
            if (taskIsPass == 2) {
                //修改子任务状态为待完善
                reqMap.put("taskstatus", WxTaskSub.SUB_TAST_STATUS_DWS);//不通过，待完善
            }

            //公共逻辑修改子任务状态
            subtaskMapper.updateWxTaskSub(reqMap);

            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
        } catch (Exception ex) {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error", ex);
        }
        return resultMap;
    }


    //抽查子任务检查项，，，，更新子任务，主任务是否通过----更新
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    @Override
    public Map<String, Object> updateTaskMainOrSubTaskStatus(int taskIsPass,
                                                             String checkResult, String mainTaskId, String subTaskId,
                                                             int checkCount,String mtaskTypeCode,String mworkshopId){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        WxTUser currentUser =  ContextUtil.getCurUser();
        boolean isTongGuo = true;
        resultMap.put("code", "success");
        resultMap.put("codeMsg", "操作成功");
        try
        {
            switch (taskIsPass) {
                case 1:
                    isTongGuo = true;
                    break;
                case 0:
                    isTongGuo = false;
                    break;
                default:
                    break;
            }

            reqMap.put("taskIsPass", taskIsPass);
            reqMap.put("checkResult", checkResult);
            reqMap.put("xgUser", currentUser.getUserId());
            //1.通过与否，都要更新子任务状态为不通过,,，，且抽查次数需要更新，，
            reqMap.put("checkCount", checkCount+1);
            WxTaskMain selectByMbId = mainTaskMapper.selectByPrimaryKey(Long.valueOf(mainTaskId));
            if((WxTaskMain.TASK_TYPE_CLZX.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode())) && taskIsPass ==2) {
                reqMap.put("taskstatus", WxTaskSub.SUB_TAST_STATUS_DWS);//不通过，待完善
            }else {
                if(!isTongGuo)
                {
                    //德乐车队业绩逻辑处理
                    if(WxTaskMain.TASK_TYPE_CDYJZM.equals(selectByMbId.getTmbTypeCode())){
                        reqMap.put("checkEvaluation", 3);

                    }
                    reqMap.put("taskstatus", WxTaskSub.SUB_TAST_STATUS_DZG);//不通过，待整改
                }else
                {
                    reqMap.put("taskstatus", WxTaskSub.SUB_TASK_STATUS_BJ);//通过，办结
                    //德乐车队业绩逻辑处理
                    if(WxTaskMain.TASK_TYPE_CDYJZM.equals(selectByMbId.getTmbTypeCode())){
                        reqMap.put("checkEvaluation", 3);
                    }
                }
            }

            reqMap.put("subTaskId", subTaskId);
            reqMap.put("checktask", "1");
		/*	//车队业绩证明，门店录入 都需要更改子任务的修改时间
			if(WxTaskMain.TASK_TYPE_CDYJZM.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_CLZX.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode())){
				reqMap.put("xgSj", 1);
			}*/
            reqMap.put("xgSj", 1);
            int subtaskresult = subtaskMapper.updateWxTaskSub(reqMap);
            if(subtaskresult>0)
            {
                //2.更新主任务
                reqMap.put("mainTaskId", mainTaskId);
                int mainresult = 0;

                //审核为待完善特殊处理
                if(taskIsPass ==2) {
                   // if(WxTaskMain.TASK_TYPE_CLZX.equals(selectByMbId.getTmbTypeCode())) {
                        //德乐门店录入审核为待完善
//                        WorkshopMaster mWorkshopMaster = new WorkshopMaster();
//                        mWorkshopMaster.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
//                        mWorkshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS2_CLZX);//待完善
//                        workshopMasterBizService.update(mWorkshopMaster);
                /*    }else if(WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode())) {
                        //车队录入审核为待完善
                        FleetInfo fleetInfo = new FleetInfo();
                        fleetInfo.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                        fleetInfo.setStatus(FleetInfo.FLEET_STATUS_REFINE);
                        fleetInfoMapper.updateByPrimaryKeySelective(fleetInfo);
                    }*/
                    reqMap.put("maintaskstatus", WxTaskMain.SUB_TAST_STATUS_DWS);//不通过，直接待整改
                    mainresult = mainTaskMapper.updateWxTaskMainStatus(reqMap);
                }else {
                    if(!isTongGuo)//如果不通过
                    {
                        //针对德乐门店录入/金富力门店录入任务抽查，更新门店状态
//                        if(WxTaskMain.TASK_TYPE_CLZX.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_SD.equals(selectByMbId.getTmbTypeCode()) ||WxTaskMain.TASK_TYPE_PROJECT_CLIENT.equals(selectByMbId.getTmbTypeCode())
//                        		|| WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_PROJECT_FLEET.equals(selectByMbId.getTmbTypeCode()))
//                        {
//                            WorkshopMaster mWorkshopMaster = new WorkshopMaster();
//                            mWorkshopMaster.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
//                            mWorkshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS3_CLZX);// 不通过，门店状态为待整改
//                            workshopMasterBizService.update(mWorkshopMaster);;
//                        }
                     /*   //德乐车队录入任务审核不通过，更新车队状态
                        if(WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode())) {
                            FleetInfo fleetInfo = new FleetInfo();
                            fleetInfo.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                            fleetInfo.setStatus(FleetInfo.FLEET_STATUS_NOT_PASS);
                            fleetInfoMapper.updateByPrimaryKeySelective(fleetInfo);
                     
                        //工程机械车队录入任务审核不通过，更新车队状态
                        if(WxTaskMain.TASK_TYPE_PROJECT_FLEET.equals(selectByMbId.getTmbTypeCode())) {
                        	ConMacFleet conMacFleet = new ConMacFleet();
                            conMacFleet.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                            conMacFleet.setUpdateTime(new Date());
                            conMacFleet.setUpdateUserId(ContextUtil.getCurUserId());
                            conMacFleet.setStatus(ConMacFleet.FLEET_STATUS03);
                            conMacFleetMapper.updateByPrimaryKeySelective(conMacFleet);
                        }
                           }*/
                        
                      /*  //工程机械客户录入任务审核不通过，更新客户状态
                        if(WxTaskMain.TASK_TYPE_PROJECT_CLIENT.equals(selectByMbId.getTmbTypeCode())) {
                        	ProjectClient projectClient = new ProjectClient();
                        	
                        	ConMacFleet conMacFleet = new ConMacFleet();
                        	projectClient.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                        	projectClient.setUpdateTime(new Date());
                        	projectClient.setUpdateUserId(ContextUtil.getCurUserId());
                        	projectClient.setStatus(ProjectClient.PROJECTCLIENT_STATUS03);
                        	projectClientMapper.updateByPrimaryKeySelective(projectClient);
                        }*/
                        
                        reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_DZG);//不通过，直接待整改
                        mainresult = mainTaskMapper.updateWxTaskMainStatus(reqMap);
                    }else//如果通过，需要查询主任务下其他子任务是否通过或者办结，，，
                    //如果其他子任务为非办结，即修改为待整改，如果其他已经办结，修改任务主任务为办结
                    {
                        //0.先查询对应主任务，其他子任务是否办结？
                        boolean isContainNot_BJ_SubTask = false;//存在是否没有办结的任务
                        List<WxTaskSub> lstSubTask = subtaskMapper.getWxTaskSubByTaskMainId(reqMap);
                        for(WxTaskSub subTask:lstSubTask)
                        {
                            if(!subTask.getTaskStatus().equals(WxTaskMain.MAIN_TASK_STATUS_BJ))
                            {
                                isContainNot_BJ_SubTask = true;//存在没有办结
                                break;
                            }
                        }

                        if(isContainNot_BJ_SubTask)//存在没有办结的子任务
                        {
                            reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_DZG);//更改为待整改
                        }else
                        {
                            reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_BJ);//办结
                        }
                        mainresult = mainTaskMapper.updateWxTaskMainStatus(reqMap);

                        //优秀业绩证明发放积分
                        if(WxTaskMain.TASK_TYPE_YJZM.equals(selectByMbId.getTmbTypeCode())){
                            if(!isContainNot_BJ_SubTask){
                                WxTaskSub wxTaskSub = lstSubTask.get(0);
                                if(wxTaskSub != null && FleetPerProve.HIGH.getCode().equals(wxTaskSub.getAttribute1())){
                                    pointBizService.importDsrPointAndExpense(DateUtil.getCurrentDate(),wxTaskSub.getExecUser(),wxTaskSub.getBrand(),currentUser);
                                }
                            }
                        }

                        //针对德乐门店录入/金富力门店录入任务抽查，更新门店状态
//                        if(WxTaskMain.TASK_TYPE_CLZX.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_SD.equals(selectByMbId.getTmbTypeCode()) ||WxTaskMain.TASK_TYPE_PROJECT_CLIENT.equals(selectByMbId.getTmbTypeCode())
//                        		|| WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode()) || WxTaskMain.TASK_TYPE_PROJECT_FLEET.equals(selectByMbId.getTmbTypeCode()))
//                        {
//                            WorkshopMaster mWorkshopMaster = new WorkshopMaster();
//                            mWorkshopMaster.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
//                            mWorkshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS3);//合格
//                            mWorkshopMaster.setActivationTime(new Date());
//                            workshopMasterBizService.update(mWorkshopMaster);
//                            if(WxTaskMain.TASK_TYPE_SD.equals(selectByMbId.getTmbTypeCode())) {
//                                Long orgId = selectByMbId.getTenantId(); //创建人的orgId
//                                 //注册店员/店主
//                                //TODO 上线需要放开
//                                makeUpEmployeeInfo(mWorkshopMaster.getId(), orgId,selectByMbId.getTaskCreateUser());
//                            }
//                            //更改门店激活状态
//                            workshopStatusBizService.insertWhenNotExists(mWorkshopMaster.getId(), WorkshopStatusVo.STATUS_ACTIVE,Long.valueOf(subTaskId),selectByMbId.getTaskFinishTime());
//                           
//                        }
                       /* //德乐车队录入任务审核通过，更新车队状态
                        if(WxTaskMain.TASK_TYPE_FLEET.equals(selectByMbId.getTmbTypeCode())) {
                            FleetInfo fleetInfo = new FleetInfo();
                            fleetInfo.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                            fleetInfo.setStatus(FleetInfo.FLEET_STATUS_PASS); //合格
                            fleetInfoMapper.updateByPrimaryKeySelective(fleetInfo);
                            //更改车队激活时间
                            FleetStatusVo record = new FleetStatusVo();
                            record.setFleetId(fleetInfo.getId());
                            record.setCreateTime(new Date());
                            record.setFleetWithStatus(FleetStatusVo.STATUS_ACTIVE);
                            record.setSubtaskId(Long.valueOf(subTaskId));
                            fleetInfoMapper.insertStatusWhenNotExists(record);
                        }
                        
                      //工程机械车队录入任务审核不通过，更新车队状态
                        if(WxTaskMain.TASK_TYPE_PROJECT_FLEET.equals(selectByMbId.getTmbTypeCode())) {
                        	ConMacFleet conMacFleet = new ConMacFleet();
                            conMacFleet.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                            conMacFleet.setUpdateTime(new Date());
                            conMacFleet.setUpdateUserId(ContextUtil.getCurUserId());
                            conMacFleet.setStatus(ConMacFleet.FLEET_STATUS3);
                            conMacFleetMapper.updateByPrimaryKeySelective(conMacFleet);
                        }
                        */
                  /*      //工程机械客户录入任务审核通过，更新客户状态
                        if(WxTaskMain.TASK_TYPE_PROJECT_CLIENT.equals(selectByMbId.getTmbTypeCode())) {
                        	ProjectClient projectClient = new ProjectClient();
                        	
                        	ConMacFleet conMacFleet = new ConMacFleet();
                        	projectClient.setId(subtaskMapper.selectByPrimaryKey(Long.valueOf(subTaskId)).getOrgId());
                        	projectClient.setUpdateTime(new Date());
                        	projectClient.setUpdateUserId(ContextUtil.getCurUserId());
                        	projectClient.setStatus(ProjectClient.PROJECTCLIENT_STATUS3);
                        	projectClientMapper.updateByPrimaryKeySelective(projectClient);
                        }*/
                    }
                }

                if(mainresult<0)
                {
                    resultMap.put("codeMsg", "更新主任务状态失败");
                    resultMap.put("code", "syserror");
                }

			/*	if(null!=mtaskTypeCode)
				{
					//针对扫店任务抽查，更新门店状态
					if(mtaskTypeCode.equals(WxTaskMain.TASK_TYPE_SD))
					{
						if(!isTongGuo)
						{
							WorkshopMaster mWorkshopMaster = new WorkshopMaster();
							mWorkshopMaster.setId(Long.parseLong(mworkshopId));
							mWorkshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS5);//待整改
							workShopMapper.updateByPrimaryKeySelective(mWorkshopMaster);
						}else
						{
							WorkshopMaster mWorkshopMaster = new WorkshopMaster();
							mWorkshopMaster.setId(Long.parseLong(mworkshopId));
							mWorkshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS0);//待整改
							workShopMapper.updateByPrimaryKeySelective(mWorkshopMaster);
						}
					}
				}*/
            }else
            {
                resultMap.put("codeMsg", "更新子任务状态失败");
                resultMap.put("code", "syserror");
            }

        }catch(Exception ex)
        {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");

            log.error("error",ex);
        }
        return resultMap;
    }

    @Override
    public JsonResponse saveTaskSub(WxTaskSub record) {
        JsonResponse map = new JsonResponse();
        log.info("saveTaskSub: " + JsonUtil.writeValue(record));
        try {
            doSaveTaskSub(record);
            log.info("saveTaskSub success." );
        } catch (Exception e) {
            map.handleException(e, this, ContextUtil.getCurUserId(),
                    "com.chevron.task.service.impl.TaskMainService.saveTaskSub", JsonUtil.writeValue(record));
        }
        return map;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    protected void doSaveTaskSub(WxTaskSub record) throws Exception {
        if(record.getCheckEvaluation() != null && record.getCheckEvaluation() >= 6) {
            //抽查合格，更新门店
            final WorkshopMaster newWorkshop = new WorkshopMaster();
            final Map<String, PropertyDescriptor> propertyMap = new HashMap<String, PropertyDescriptor>();
            for(PropertyDescriptor propertyDescriptor :
                    Introspector.getBeanInfo(WorkshopMaster.class).getPropertyDescriptors()) {
                propertyMap.put(propertyDescriptor.getName(), propertyDescriptor);
            }
            StringBuilder sql = new StringBuilder(10000).append("select tic1.check_value, tc1.property_name from wx_task_sub ts1")
                    .append(" join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id")
                    .append(" left join wx_task_check tc1 on tic1.check_id=tc1.check_id")
                    .append(" where ts1.task_id=? and tc1.property_name is not null and tc1.property_name!=''")
                    .append(" union all select convert(nvarchar(20), ts1.org_id), 'id' from wx_task_sub ts1 where ts1.task_id=?");
            wxJdbcTemplate.query(sql.toString(), new Object[] {record.getTaskId(), record.getTaskId()}, new RowCallbackHandler() {

                @Override
                public void processRow(ResultSet arg0) throws SQLException {
                    String propertyName = arg0.getString("property_name");
                    String value = arg0.getString("check_value");
                    PropertyDescriptor propertyDescriptor = propertyMap.get(propertyName);
                    if(propertyDescriptor == null) {
                        throw new RuntimeException("门店属性" + propertyName + "不存在");
                    }
                    try {
                        propertyDescriptor.getWriteMethod().invoke(newWorkshop, ValueParser.parseValue(value, propertyDescriptor.getPropertyType()));
                    } catch (Exception e) {
                        throw new RuntimeException("设置门店属性" + propertyName + "失败", e);
                    }
                }
            });
            workshopMasterBizService.update(newWorkshop);
        }
        subtaskMapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    @Override
    public Map<String, Object> deleteBatchTaskByMainTaskId(String mainTaskId){
        Map<String,Object> reqMap = new HashMap<String,Object>();
        Map<String,Object> resultMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {
            //0.删除主任务
            Long mMainTaskId = Long.parseLong(mainTaskId);
            mainTaskMapper.deleteByPrimaryKey(mMainTaskId);
            //1.删除对应的所有子任务
            subtaskMapper.deleteSubTaskByMainTaskId(mMainTaskId);
            //2.删除对应任务实例
            //获取任务主任务对应的实例id
            List<Long> instanceIds = instanceCheckVoMapper.getTaskInstanceIdsByMainTaskId(mMainTaskId);
            //删除任务实例
            instanceCheckVoMapper.deleteInstanceByMainTaskId(mMainTaskId);
            //3.删除对应任务产品|库存信息
            taskProductInfoMapper.deleteTaskProductInfoByMainTaskId(mMainTaskId);
            //4.删除对应任务竞品信息
            taskCompeteGoodsVoMapper.deleteCompeteGoodsByMainTaskId(mMainTaskId);
            //5.删除对应附件信息（数据库表中）
            instanceIds.add(mMainTaskId);
            reqMap.put("instanceIds", instanceIds);
            wxAttFileMapper.delteBatchWxAttFilesBySourceIds(reqMap);

        }catch(Exception ex)
        {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");

            log.error("error",ex);
        }
        return reqMap;
    }


    @Override
    public Map<String, Object> getAccessWorkshopForSD(String subTaskId,
                                                      String workshopId){
        Map<String,Object> reqMap = new HashMap<String,Object>();
        Map<String,Object> resultMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {
            reqMap.put("subTaskId", subTaskId);
            reqMap.put("workshopId", workshopId);
            List<TaskExecTraceVo> lstExecTrace = taskExecTraceMapper.getTaskExecTraceVoList(reqMap);
            resultMap.put("lstExecTrace", lstExecTrace);

        }catch (Exception e) {
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",e);
        }
        return resultMap;
    }

    @Override
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    public Map<String, Object> releaseWorkshopForSDBySPMOrExcuteUser(
            String mainTaskId){
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {
            reqMap.put("mainTaskId", mainTaskId);
            reqMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_QX);//取消
            //0.重置主任务状态为取消
            mainTaskMapper.updateWxTaskMainStatus(reqMap);
            //1.重置子任务状态为取消
            reqMap.put("taskstatus", WxTaskMain.MAIN_TASK_STATUS_QX);//取消
            subtaskMapper.updateBatchWxTaskSubByMainId(reqMap);
            //2.释放门店执行者
            //获取要被释放的门店
            List<WxTaskSub> subTaskLst = subtaskMapper.getWxTaskSubByTaskMainId(reqMap);
            List<Long> workshopIds = new ArrayList<Long>();
            for(WxTaskSub taskSub:subTaskLst)
            {

                if(taskSub.getOrgId()!=null && !taskSub.getOrgId().equals(""))
                {
                    workshopIds.add(taskSub.getOrgId());
                }
            }
            //批量更新门店
            if(workshopIds.size()>0)
            {
            	WorkshopMaster record = new WorkshopMaster();
            	record.setStatus(WorkshopMaster.WORKSHOP_STATUS0);
            	record.setUpdateTime(DateUtil.getCurrentDate());
            	record.setUpdateUserId(ContextUtil.getCurUserId());
            	WorkshopMasterExample example = new WorkshopMasterExample();
            	example.createCriteria().andIdIn(workshopIds);
            	workshopMasterMapper.updateByExampleSelective(record, example);
            }

        }catch(Exception ex)
        {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");

            log.error("error",ex);
        }
        return resultMap;
    }



    //主任务列表需要
    public Map<String,Object> getMainTaskByTaskTypeCodeNew(String taskTypeCode,MainTaskQueryConditions mainTaskQueryObj,int start,int limit){
        Map<String, Object> resultMap = new HashMap<String,Object>();
        List<WxTaskMain> lstMainTask = new ArrayList<WxTaskMain>();
        Map<String, Object> reqMap = new HashMap<String,Object>();
        try
        {	if(null!=taskTypeCode && !"".equals(taskTypeCode))
        {
            reqMap.put("taskTypeCodeModel", 2);//所有任务类型，主任务界面上不显示门店信息
            if(null!=mainTaskQueryObj)//当查询条件数组不为null的时候需要解析数组各个值，
            {
                String taskName = mainTaskQueryObj.getTaskName();
                String taskMainStauts = mainTaskQueryObj.getTaskMainStatus();
                boolean isCreateUser = mainTaskQueryObj.isCreateUser();
                boolean isExcuteUser = mainTaskQueryObj.isExecUser();
                boolean isWatchUser = mainTaskQueryObj.isWatchUser();
                Date taskStartTime = mainTaskQueryObj.getTaskFinishTimeS();//预计开始时间
                Date taskEndTime = mainTaskQueryObj.getTaskFinishTimeE();//预计结束时间
                String taskExcuteUID = mainTaskQueryObj.getTaskExcuteUserName();//执行者ID modify by bo.liu 0828

                String workshopName = mainTaskQueryObj.getTaskWorkshopName();
                String workshopScale = mainTaskQueryObj.getTaskWorkshopScale();
                if(taskName!=null && !taskName.equals(""))
                {
                    reqMap.put("taskName", taskName);
                }
                if(taskMainStauts!=null && !taskMainStauts.equals(""))
                {
                    reqMap.put("taskMainStauts", taskMainStauts);
                }

                if(isCreateUser)
                {
                    reqMap.put("isCreateUser", isCreateUser);
                }

                else if(isExcuteUser)
                {
                    reqMap.put("isExcuteUser", isExcuteUser);
                }

                else if(isWatchUser)
                {
                    reqMap.put("isWatchUser", isWatchUser);
                }

                if(null!=taskStartTime)
                {
                    reqMap.put("taskStartTime", taskStartTime);
                }

                if(null!=taskEndTime)
                {
                    reqMap.put("taskEndTime", taskEndTime);
                }


                if(taskExcuteUID!=null && !taskExcuteUID.equals("") && !taskExcuteUID.equals("-1"))
                {
                    reqMap.put("taskExcuteUID", taskExcuteUID);
                }

                if(workshopName!=null && !workshopName.equals(""))
                {
                    reqMap.put("workshopName", workshopName);
                }

                if(workshopScale!=null && !workshopScale.equals(""))
                {
                    reqMap.put("workshopScale", workshopScale);
                }

                reqMap.put("taskEndTime", taskEndTime);
                Long currentUserId = ContextUtil.getCurUserId();
                reqMap.put("currentUserId", currentUserId);


            }

            reqMap.put("currentId", ContextUtil.getCurUserId());//登录的是创建人本人才能看
            reqMap.put("tenantId", ContextUtil.getCurUser().getmUserTypes());//虚拟的tenantid只用于判断是否雪佛龙或管理员  还是普通用户  1代表雪佛龙或管理员  0代表普通用户  modify by bo.liu 0809
            reqMap.put("orgId", ContextUtil.getCurUser().getOrgId());//add by bo.liu 0809
            reqMap.put("taskTypeCode", taskTypeCode);
            if(null == mainTaskQueryObj)
            {
                reqMap.put("order", new MainTaskQueryConditions().getOrder());
            }else
            {
                reqMap.put("order", mainTaskQueryObj.getOrder());
            }
            reqMap.put("start", start);
            reqMap.put("limit", limit);
            lstMainTask = mainTaskMapper.getMainTaskByTaskTypeOrParams(reqMap);
            int totalRecord =  mainTaskMapper.countMainTaskByTaskTypeOrParams(reqMap);
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
            resultMap.put("resultlst", lstMainTask);
            resultMap.put("totalRecord", totalRecord);
        }else
        {
            //前台需要给出提示,任务类型不能为
            resultMap.put("code", "tasktypeIsNull");
            resultMap.put("codeMsg", "参数传递错误");
        }

        }catch(Exception ex)
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("error",ex);
        }
        return resultMap;
    }



    @Override
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public Map<String, Object> createTaskForWorkshopInstockConfirm(
            String inStockNo, String outStockNo, String orderNo) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> reqMap = new HashMap<String,Object>();
        try
        {
            //0.根据订单号查询，对应的门店id，再根据门店id获取门店名称，门店执行人,门店名称
            if(null==orderNo || orderNo.isEmpty() || null==inStockNo||inStockNo.isEmpty()|| null==outStockNo||outStockNo.isEmpty())
            {
                resultMap.put("codeMsg", "创建任务失败，传入的参数存在null");
                resultMap.put("code", "syserror");
                return resultMap;
            }
            OrderVo orderVo = orderVoMapper.selectByOrderNo(orderNo);
            if(null==orderVo)
            {
                resultMap.put("codeMsg", "创建任务失败，没有对应的订单来源");
                resultMap.put("code", "syserror");
                return resultMap;
            }
            Long workshopId = orderVo.getWorkShopId();
            if(null==workshopId)
            {
                resultMap.put("codeMsg", "创建任务失败，对应订单的门店id为null");
                resultMap.put("code", "syserror");
                return resultMap;
            }
            //根据workshop id 获取门店信息
            reqMap.put("workshopId", workshopId);
            WorkshopMaster workShopVo = workshopMasterBizService.getBean(workshopId);
            Long userId = workShopVo.getExcuteUserId();
            if(null==userId)
            {
                resultMap.put("codeMsg", "创建任务失败，没找到对应门店的执行人");
                resultMap.put("code", "syserror");
                return resultMap;
            }
            WxTUser wtuser = userMapper.selectByPrimaryKey(userId);

            //1.根据typecode，获取模板id
            reqMap.put("mbType", WxTaskMain.TASK_TYPE_SI);
            List<WxTaskMb>  lstMbs = mbMapper.queryMbsByMbTypeAndPartnerId(reqMap);
            if(null==lstMbs || lstMbs.isEmpty())
            {
                resultMap.put("codeMsg", "创建任务失败，没有找到对应的模板信息");
                resultMap.put("code", "syserror");
                return resultMap;
            }
            Long taskTemplateId = lstMbs.get(0).getMbId();
            //2.获取指定的步骤
            String steps = appTaskService.getTaskSteps(taskTemplateId, WxTaskMain.TASK_TYPE_SI);

            //3.组装数据
            WxTaskMain wxTask = new WxTaskMain();
            wxTask.setInStockNo(inStockNo);
            wxTask.setOutStockNo(outStockNo);
            wxTask.setOrderNo(orderNo);
            wxTask.setExcuteUserId(workShopVo.getExcuteUserId());
            wxTask.setExecuteUserName(wtuser.getChName());
            wxTask.setFrequencyType(1);
            wxTask.setPublishStatus(1);
            wxTask.setTaskDescription("门店入库确认任务");
            wxTask.setTaskFinishTime(DateUtil.getCurrentDate());
            wxTask.setTaskMbId(taskTemplateId);
            wxTask.setTaskPriority("1");
            wxTask.setTaskStartTime(DateUtil.getCurrentDate());
            wxTask.setTaskSteps(steps);
            wxTask.setTmbTypeCode(WxTaskMain.TASK_TYPE_SI);
            wxTask.setWorkshopIds(""+workshopId);
            wxTask.setWorkShopName(workShopVo.getWorkShopName());
            //插入任务
            insertTask(wxTask, steps, "", wxTask.getTaskStartTime().getTime(), wxTask.getTaskFinishTime().getTime(),CREATE_TASK_SND_MSG);


        }catch (Exception e) {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",e);
            return resultMap;
        }
        resultMap.put("codeMsg", "操作成功");
        resultMap.put("code", "success");
        return resultMap;
    }



    @Override
    public Map<String, Object> getTaskInstockProductsByInstockNo(
            String instockNo) {
        Map<String,Object> resultMap = new HashMap<String, Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {	if(null!=instockNo && !instockNo.equals(""))
        {
            reqMap.put("inStockNo", instockNo);
            List<WorkshopInstockProductResp> taskWorkshopInstockProductList = instockProductMapper.selectInStockProductByCondition(reqMap);
            resultMap.put("taskInstockProductInfoLst", taskWorkshopInstockProductList);
        }else
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", "参数传递错误");
        }

        }catch(Exception ex)
        {
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",ex);
        }
        return resultMap;
    }


//    @Override
//    public Map<String, Object> createXDTaskForActiveWorkshopByExcuteUserId(
//            Long excuteUserId,List<Long> workshopIdLst) {
//        Map<String,Object> reqMap = new HashMap<String,Object>();
//        Map<String,Object> resultMap = new HashMap<String,Object>();
//        try
//        {
//            //0.获取门店列表，条件：状态是3，且执行人不为null的门店，关联查询对应执行人的组织id
//            reqMap.put("excuteUserId", excuteUserId);
//            reqMap.put("workshopIdLst", workshopIdLst);
//            List<WorkshopMaster> lstWorkshop = workShopMapper.getWorkshopForCreateXDTask(reqMap);
//            //1.组装门店，执行人数据，用于创建任务
//            List<WorkshopExcuteUserInfo> lstWorkshopExcuteUserInfo = new ArrayList<WorkshopExcuteUserInfo>();
//            WorkshopExcuteUserInfo workshopExcuteUserInfo = null;//新的WorkshopExcuteUserInfo对象
//            List<String> currentUserWorkshops = null;//1个数对象对应N个实例对象（一个用户对应N个门店，，用于存放相同执行人的门店）
//            Long lastExcuteUserId = 0L;//控制->原始的用户执行人id
//            Long currentExuteUserId = 0L;
//            Long lastNewExcuteUserId = 0L;//控制->新实例对象
//            System.out.println("------------------------createXDTaskForActiveWorkshopByExcuteUserId:"+lstWorkshop.size());
//
//            //分组归类{用户:ss;门店:[,,,]}
//            for(WorkshopMaster workShopVo:lstWorkshop)
//            {
//                currentExuteUserId = workShopVo.getExcuteUserId();
//                Long workshopIds = workShopVo.getId();
//                if(!currentExuteUserId.equals(lastExcuteUserId))
//                {
//                    workshopExcuteUserInfo = new WorkshopExcuteUserInfo();//不等的时候新的实例对象
//                    currentUserWorkshops = new ArrayList<String>();
//                    workshopExcuteUserInfo.setUserId(currentExuteUserId);
//                    workshopExcuteUserInfo.setPartnerId(workShopVo.getExcuteUserOrgId());
//                    workshopExcuteUserInfo.setUserName(workShopVo.getExcutorName());
//                }
//
//                currentUserWorkshops.add(""+workshopIds);
//                lastExcuteUserId = currentExuteUserId;
//                workshopExcuteUserInfo.setWorkshopIds(currentUserWorkshops);
//
//                if(!currentExuteUserId.equals(lastNewExcuteUserId))
//                {
//                    lstWorkshopExcuteUserInfo.add(workshopExcuteUserInfo);
//                }
//                lastNewExcuteUserId = currentExuteUserId;
//            }
//            resultMap = createBatchTaskByManual(lstWorkshopExcuteUserInfo,WxTaskMain.TASK_TYPE_XD);
//			/*//2.根据typecode，获取模板id
//			reqMap.put("mbType", WxTaskMain.TASK_TYPE_XD);
//			List<WxTaskMb>  lstMbs = mbMapper.queryMbsByMbTypeAndPartnerId(reqMap);
//			if(null==lstMbs || lstMbs.isEmpty())
//			{
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "not found taskmb");
//				return resultMap;
//			}
//			Long taskTemplateId = lstMbs.get(0).getMbId();
//			//3.获取指定的步骤,并构建任务
//			String steps = appTaskService.getTaskSteps(taskTemplateId, WxTaskMain.TASK_TYPE_XD);
//			List<WxTaskMain> lstTaskMain = new ArrayList<WxTaskMain>();
//			for(WorkshopExcuteUserInfo workshopExcuteUserInfo2:  lstWorkshopExcuteUserInfo)
//			{
//				List<String> lstworkshopIDS = workshopExcuteUserInfo2.getWorkshopIds();
//				ImportDataPageModelUtil workshoppage = new ImportDataPageModelUtil(
//						lstworkshopIDS, 70);
//				int workshop_account = workshoppage.getTotalPages();
//				for (int i = 1; i <= workshop_account; i++) {
//					WxTaskMain wxTask = new WxTaskMain();
//					wxTask.setExcuteUserId(workshopExcuteUserInfo2.getUserId());
//					wxTask.setExecuteUserName(workshopExcuteUserInfo2.getUserName());
//					wxTask.setFrequencyType(1);
//					wxTask.setPublishStatus(1);
//					wxTask.setTaskDescription("定时创建巡店任务");
//					wxTask.setTaskStartTime(new Date());
//					wxTask.setTaskFinishTime(DateUtil.addDays(new Date(), 10));
//					wxTask.setTaskMbId(taskTemplateId);
//					wxTask.setTaskPriority("1");
//					wxTask.setTaskSteps(steps);
//					wxTask.setTmbTypeCode(WxTaskMain.TASK_TYPE_XD);
//					List<String> tempWorkshopIds = workshoppage.getObjects(i);
//					String workshopIDS = "";
//					for(String workshopID:tempWorkshopIds)
//					{
//						workshopIDS+=workshopID+",";
//					}
//					wxTask.setWorkshopIds(workshopIDS.substring(0, workshopIDS.length()-1));
//					lstTaskMain.add(wxTask);
//				}
//			}
//
//			//录入任务
//			for(WxTaskMain wxTask:lstTaskMain)
//			{
//				insertTask(wxTask, steps, "", wxTask.getTaskStartTime().getTime(), wxTask.getTaskFinishTime().getTime());
//			}
//			*/
//
//        }catch(Exception ex)
//        {
//            TransactionAspectSupport.currentTransactionStatus()
//                    .setRollbackOnly();
//            ex.printStackTrace();
//            resultMap.put("code", "syserror");
//            resultMap.put("codeMsg", "ex:"+ex.getLocalizedMessage());
//            return resultMap;
//        }
//        resultMap.put("code", "success");
//        log.debug("------------------------------by ljc createXDTaskForActiveWorkshopByExcuteUserId end---------------------------");
//        return resultMap;
//    }



    @Override
    public Map<String, Object> releaseWorkshopForSDBySPMOrExcuteUserAndSubTaskId(
            String subTaskId,String workshopId) {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        try
        {
            //1.重置子任务状态为取消
            reqMap.put("subTaskId", subTaskId);
            reqMap.put("taskstatus", WxTaskMain.MAIN_TASK_STATUS_QX);//取消
            subtaskMapper.updateWxTaskSubByTaskId(reqMap);
            //2.释放门店执行者

            List<Long> workshopIds = new ArrayList<Long>();
            if(null!=workshopId)
            {
                workshopIds.add(Long.parseLong(workshopId));
            }

            //批量更新门店
            if(workshopIds.size()>0)
            {
            	WorkshopMaster record = new WorkshopMaster();
            	record.setStatus(WorkshopMaster.WORKSHOP_STATUS0);
            	record.setUpdateTime(DateUtil.getCurrentDate());
            	record.setUpdateUserId(ContextUtil.getCurUserId());
            	WorkshopMasterExample example = new WorkshopMasterExample();
            	example.createCriteria().andIdIn(workshopIds);
            	workshopMasterMapper.updateByExampleSelective(record, example);


                //同时在wx_v_hasgd_workshop表中删除掉已经创建的门店信息
                hasgdWorkshopMapper.deleteByWorkshopIds(reqMap);

            }

        }catch(Exception ex)
        {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");

            log.error("error",ex);
        }
        return resultMap;
    }


    @Override
    public Map<String, Object> createBatchTaskForGD(
            List<WxTaskMainNew> selectedTaskForGDLst) {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        try
        {
            //0.过滤掉待整改的
            List<WxTaskMainNew> selectedTaskForGDLstNew = new ArrayList<WxTaskMainNew>();
            for(WxTaskMainNew taskMainNew:selectedTaskForGDLst)
            {
                if(null!=taskMainNew.getTaskNewStatus()&&taskMainNew.getTaskNewStatus().equals("8"))
                {
                    selectedTaskForGDLstNew.add(taskMainNew);
                }
            }


            //1.组装门店，执行人数据，用于创建任务
            List<WorkshopExcuteUserInfo> lstWorkshopExcuteUserInfo = new ArrayList<WorkshopExcuteUserInfo>();
            WorkshopExcuteUserInfo workshopExcuteUserInfo = null;//新的WorkshopExcuteUserInfo对象
            List<String> currentUserWorkshops = null;//1个数对象对应N个实例对象（一个用户对应N个门店，，用于存放相同执行人的门店）
            Long lastExcuteUserId = 0L;//控制->原始的用户执行人id
            Long currentExuteUserId = 0L;
            Long lastNewExcuteUserId = 0L;//控制->新实例对象
            System.out.println("------------------------createBatchTaskForGD:"+selectedTaskForGDLst.size());

            //分组归类{用户:ss;门店:[,,,]}
            for(WxTaskMainNew taskMainNew:selectedTaskForGDLstNew)
            {
                currentExuteUserId = taskMainNew.getExcuteUserId();
                Long workshopIds = taskMainNew.getWorkShopId();
                if(!currentExuteUserId.equals(lastExcuteUserId))
                {
                    workshopExcuteUserInfo = new WorkshopExcuteUserInfo();//不等的时候新的实例对象
                    currentUserWorkshops = new ArrayList<String>();
                    workshopExcuteUserInfo.setUserId(currentExuteUserId);
                    workshopExcuteUserInfo.setPartnerId(taskMainNew.getPartnerId());
                    workshopExcuteUserInfo.setUserName(taskMainNew.getExecuteUserName());
                }

                currentUserWorkshops.add(""+workshopIds);
                lastExcuteUserId = currentExuteUserId;
                workshopExcuteUserInfo.setWorkshopIds(currentUserWorkshops);

                if(!currentExuteUserId.equals(lastNewExcuteUserId))
                {
                    lstWorkshopExcuteUserInfo.add(workshopExcuteUserInfo);
                }
                lastNewExcuteUserId = currentExuteUserId;
            }

            resultMap = createBatchTaskByManual(lstWorkshopExcuteUserInfo,WxTaskMain.TASK_TYPE_LD);
			/*
			//2.根据typecode，获取模板id 获取攻店的模板id
			reqMap.put("mbType", WxTaskMain.TASK_TYPE_LD);
			List<WxTaskMb>  lstMbs = mbMapper.queryMbsByMbTypeAndPartnerId(reqMap);
			if(null==lstMbs || lstMbs.isEmpty())
			{
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "not found taskmb");
				return resultMap;
			}
			Long taskTemplateId = lstMbs.get(0).getMbId();
			//3.获取指定的步骤,并构建任务
			String steps = appTaskService.getTaskSteps(taskTemplateId, WxTaskMain.TASK_TYPE_LD);
			List<WxTaskMain> lstTaskMain = new ArrayList<WxTaskMain>();
			for(WorkshopExcuteUserInfo workshopExcuteUserInfo2:  lstWorkshopExcuteUserInfo)
			{
				List<String> lstworkshopIDS = workshopExcuteUserInfo2.getWorkshopIds();
				ImportDataPageModelUtil workshoppage = new ImportDataPageModelUtil(
						lstworkshopIDS, 50);
				int workshop_account = workshoppage.getTotalPages();
				for (int i = 1; i <= workshop_account; i++) {
					WxTaskMain wxTask = new WxTaskMain();
					wxTask.setExcuteUserId(workshopExcuteUserInfo2.getUserId());
					wxTask.setExecuteUserName(workshopExcuteUserInfo2.getUserName());
					wxTask.setFrequencyType(1);
					wxTask.setPublishStatus(1);
					wxTask.setTaskDescription("批量发布攻店任务");
					wxTask.setTaskStartTime(new Date());
					wxTask.setTaskFinishTime(DateUtil.addDays(new Date(), 1));
					wxTask.setTaskMbId(taskTemplateId);
					wxTask.setTaskPriority("1");
					wxTask.setTaskSteps(steps);
					wxTask.setTmbTypeCode(WxTaskMain.TASK_TYPE_LD);
					List<String> tempWorkshopIds = workshoppage.getObjects(i);
					String workshopIDS = "";
					for(String workshopID:tempWorkshopIds)
					{
						workshopIDS+=workshopID+",";
					}
					wxTask.setWorkshopIds(workshopIDS.substring(0, workshopIDS.length()-1));
					lstTaskMain.add(wxTask);
				}
			}

			//发布任务
			for(WxTaskMain wxTask:lstTaskMain)
			{
				insertTask(wxTask, steps, "", wxTask.getTaskStartTime().getTime(), wxTask.getTaskFinishTime().getTime());
			}

    		*/
        }catch (Exception e) {
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",e);
            return resultMap;
        }
        resultMap.put("code", "success");
        return resultMap;
    }

    public Map<String, Object> createBatchTaskByManual(List<WorkshopExcuteUserInfo> lstWorkshopExcuteUserInfo,String taskType)
    {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        List<Long> userIds = new ArrayList<Long>();
        //1.根据typecode，获取模板id 获取攻店的模板id
        reqMap.put("mbType", taskType);
        List<WxTaskMb>  lstMbs = mbMapper.queryMbsByMbTypeAndPartnerId(reqMap);
        if(null==lstMbs || lstMbs.isEmpty())
        {
            resultMap.put("code", "syserror");
            resultMap.put("codeMsg", "not found taskmb");
            return resultMap;
        }
        Long taskTemplateId = lstMbs.get(0).getMbId();
        //2.获取指定的步骤,并构建任务
        String steps = appTaskService.getTaskSteps(taskTemplateId, taskType);
        List<WxTaskMain> lstTaskMain = new ArrayList<WxTaskMain>();
        for(WorkshopExcuteUserInfo workshopExcuteUserInfo2:  lstWorkshopExcuteUserInfo)
        {
            List<String> lstworkshopIDS = workshopExcuteUserInfo2.getWorkshopIds();
            ImportDataPageModelUtil workshoppage = new ImportDataPageModelUtil(
                    lstworkshopIDS, TOTAL_WORKSHOP);
            int workshop_account = workshoppage.getTotalPages();
            for (int i = 1; i <= workshop_account; i++) {
                WxTaskMain wxTask = new WxTaskMain();
                wxTask.setExcuteUserId(workshopExcuteUserInfo2.getUserId());
                wxTask.setExecuteUserName(workshopExcuteUserInfo2.getUserName());
                wxTask.setFrequencyType(1);
                wxTask.setPublishStatus(1);
                wxTask.setTaskStartTime(new Date());
                if(taskType.equals(WxTaskMain.TASK_TYPE_LD))
                {
                    wxTask.setTaskDescription("批量发布攻店任务");
                    wxTask.setTaskFinishTime(new Date());
                }else if(taskType.equals(WxTaskMain.TASK_TYPE_XD))
                {
                    wxTask.setTaskDescription("定时创建巡店任务");
                    wxTask.setTaskFinishTime(DateUtil.addDays(new Date(), 10));
                }
                wxTask.setTaskMbId(taskTemplateId);
                wxTask.setTaskPriority("1");
                wxTask.setTaskSteps(steps);
                wxTask.setTmbTypeCode(WxTaskMain.TASK_TYPE_LD);
                List<String> tempWorkshopIds = workshoppage.getObjects(i);
                String workshopIDS = "";
                for(String workshopID:tempWorkshopIds)
                {
                    workshopIDS+=workshopID+",";
                }
                wxTask.setWorkshopIds(workshopIDS.substring(0, workshopIDS.length()-1));
                lstTaskMain.add(wxTask);
                Long excuteUserId = wxTask.getExcuteUserId();
                if(!userIds.contains(excuteUserId))
                {
                    userIds.add(excuteUserId);
                }
            }
        }

        //发布任务
        for(WxTaskMain wxTask:lstTaskMain)
        {
            resultMap = insertTask(wxTask, steps, "", wxTask.getTaskStartTime().getTime(), wxTask.getTaskFinishTime().getTime(),CREATE_TASK_NOT_SND_MSG);
        }
        //这里批量推送创建任务消息，，需要去重 05.31
        log.info("---createBatchTaskByManual pushMessage start--");
        msgPushService.pushMessage(WxPushMessage.MSG_TYPE_PERSONAL, TASK_MSG_TITLE, TASK_MSG_CONTENT_TIME_XD, null, userIds);
        log.info("---createBatchTaskByManual pushMessage end--");
        //resultMap.put("code", "success");
        return resultMap;

    }

    @Override
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public Map<String, Object> checkAttFileForXD(
            AttfileCheckForXDConditions attfileCheckForXDConditions) {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        try
        {
            //0.获取打回的图片id,和所有的图片id（，过滤出合格的图片id）
            resultMap = modifyAttFlag(attfileCheckForXDConditions);
            String code = (String) resultMap.get("code");
            if(null!=code && !code.equals("success"))
            {
                throw new Exception("更新附件对应的是否通过标识失败");
            }
            //1.针对不合格的图片，，查询或组装图片对应的路径，，然后生成不合格的水印图片
            List<Long> badAttIds = (ArrayList<Long>) resultMap.get("badAttIds");
            if(null==badAttIds || badAttIds.isEmpty())//不存在不合格的照片，，全部审核通过
            {
                resultMap.put("code", "success");
                return resultMap;
            }
            reqMap.put("attIds", badAttIds);
            List<WxAttFile> lstAttFile = wxAttFileMapper.getWxAttFilesByAttId(reqMap);
            resultMap = createWaterImage(lstAttFile);
            code = (String) resultMap.get("code");
            if(null!=code && !code.equals("success"))
            {
                throw new Exception("生成图片水印失败");
            }

            //2.查询不合格图片的taskid列表
            List<Long> sourceIds = new ArrayList<Long>();
            for(WxAttFile wxAtt:lstAttFile)
            {
                Long sourceId = wxAtt.getSourceId();
                if(null!=sourceId)
                {
                    if(!sourceIds.contains(sourceId))
                    {
                        sourceIds.add(sourceId);
                    }
                }
            }

            if(null==sourceIds||sourceIds.isEmpty())
            {
                resultMap.put("code", "success");
                return resultMap;
            }
            //根据source id，任务实例id获取对应的任务id和主任务id
            reqMap.put("sourceIds", sourceIds);
            List<TaskInstanceCheckVo> taskInstanceLst =  instanceCheckVoMapper.getTaskStepCheckInstanceByIds(reqMap);
            List<Long> taskIds = new ArrayList<Long>();
            List<Long> taskMainIds = new ArrayList<Long>();
            for(TaskInstanceCheckVo taskInstanceCheck:taskInstanceLst)
            {
                Long taskId = taskInstanceCheck.getSubTaskId();
                Long taskMainId = taskInstanceCheck.getMainTaskId();
                if(null!=taskId)
                {
                    if(!taskIds.contains(taskId))
                    {
                        taskIds.add(taskId);
                    }
                }

                if(null!=taskMainId)
                {
                    if(!taskMainIds.contains(taskMainId))
                    {
                        taskMainIds.add(taskMainId);
                    }
                }

            }
            //3.更新不合格的子任务列表为待整改
            reqMap.put("taskStatus", WxTaskSub.SUB_TAST_STATUS_DZG);
            reqMap.put("taskIds", taskIds);
            subtaskMapper.updateSubTaskStatusByTaskIds(reqMap);

            //4.更新主任务列表为待整改
            reqMap.put("taskMainIds", taskMainIds);
            mainTaskMapper.updateMainTaskStatusByTaskMainIds(reqMap);
            resultMap.put("code", "success");
        }catch (Exception e) {
            //手动触发回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
            resultMap.put("code", "syserror");
            log.error("error",e);
        }
        return resultMap;
    }

    /**
     * 审核图片，并置标志
     * <AUTHOR> 2017-5-16 上午11:24:19
     * @param attfileCheckForXDConditions
     * @return
     * @throws Exception
     */
    public Map<String,Object> modifyAttFlag(AttfileCheckForXDConditions attfileCheckForXDConditions)
    {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        Long[] invalidAttIds = attfileCheckForXDConditions.getInvalidAttIds();
        List<Long> badAttIds = new ArrayList<Long>();
        List<Long> goodAttIds = new ArrayList<Long>();
        List<Long> allAttIds = new ArrayList<Long>();
        try
        {
            resultMap.put("code", "success");
            for(Long allAtt: attfileCheckForXDConditions.getAllAttIds())
            {
                allAttIds.add(allAtt);
            }
            if(null!=invalidAttIds && invalidAttIds.length>0)
            {
                for(Long invalidAtt : invalidAttIds)
                {
                    badAttIds.add(invalidAtt);
                }
                for (Iterator<Long> it = badAttIds.iterator();it.hasNext();){
                    Long removeId= it.next().longValue();
                    for (Iterator<Long> it2 = allAttIds.iterator();it2.hasNext();){
                        Long allId =it2.next().longValue();
                        if(allId.equals(removeId) || allId.equals(removeId) )
                        {
                            it2.remove();
                        }
                    }
                }
            }
            goodAttIds = allAttIds;
            //1.更新图片对应id的标志，，，打回的，，合格的
            if(null!=badAttIds && !badAttIds.isEmpty())
            {
                reqMap.put("passflag", WxAttFile.PASSFLAGS[0]);
                reqMap.put("attIds", badAttIds);
                wxAttFileMapper.modifyAttFilesPassFlagByAttIds(reqMap);  //不需要
            }

            if(null!=goodAttIds && !goodAttIds.isEmpty())
            {
                reqMap.put("passflag", WxAttFile.PASSFLAGS[1]);
                reqMap.put("attIds", goodAttIds);
                wxAttFileMapper.modifyAttFilesPassFlagByAttIds(reqMap);  //不需要
            }
            resultMap.put("badAttIds", badAttIds);
        }catch (Exception e) {
            resultMap.put("code", "syserror");
            log.error("error",e);
        }
        return resultMap;

    }


    /**
     * 生成水印图片
     * <AUTHOR> 2017-5-16 上午11:19:36
     * @throws Exception
     */
    public Map<String,Object> createWaterImage(List<WxAttFile> lstAttFile)
    {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        try
        {
            resultMap.put("code", "success");
            if(null==lstAttFile || lstAttFile.isEmpty())
            {
                return resultMap;
            }
            String fileSep = System.getProperty("file.separator");
            String realPath = "";
            String storageName = "", filePath = "";
            for(WxAttFile att:lstAttFile)
            {
                realPath = FileManager.fileUploadPath + att.getStorePath();
                storageName = att.getStorageName();
                filePath = realPath + fileSep + storageName;
                ImageWaterSetUtil.makeWaterImage(filePath, filePath, Color.RED,"不合格");
            }
        }catch (Exception e) {
            resultMap.put("code", "syserror");
            System.out.println("-------------createWaterImage:");
            log.error("error",e);
        }
        return resultMap;
    }
    /**
     * 修复缩略图
     */
    @Override
    public Map<String, Object> createThumbnailImage(final String storePath,final int width,final int height) {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        try
        {
            resultMap.put("code", "success");
            final String fileSep = System.getProperty("file.separator");

            new Thread(new Runnable() {

                @Override
                public void run() {
                    String realPath = "";
                    realPath = FileManager.fileUploadPath + storePath+fileSep;
                    FileUtil.createThumbnailImageByPath(realPath,width,height);

                }
            }).start();



        }catch (Exception e) {
            resultMap.put("code", "syserror");
            System.out.println("-------------createThumbnailImage:");
            log.error("error",e);
        }
        return resultMap;
    }


    @Override
    public Map<String, Object> createThumbnailImageNew(String[] storePaths,
                                                       int width, int height,int errorSize) {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        try
        {
            resultMap.put("code", "success");
            final String fileSep = System.getProperty("file.separator");
            System.out.println("-------------createThumbnailImageNew started");
            for(String storePath:storePaths)
            {
                System.out.println("-------------createThumbnailImageNew storePath:"+storePath);
                String realPath = "";
                realPath = FileManager.fileUploadPath + storePath+fileSep;
                FileUtil.createThumbnailImageByPathNew(storePath,realPath,width,height,errorSize);
            }

        }catch (Exception e) {
            resultMap.put("code", "syserror");
            System.out.println("-------------createThumbnailImage:");
            log.error("error",e);
        }
        System.out.println("-------------createThumbnailImageNew finished");
        return resultMap;
    }

    @Override
    public Map<String, Object> repairTaskQuartz(Date date) {
        createTaskService.doHandleCreateTask(date);
        Map<String,Object> resultMap = new HashMap<String,Object>();
        resultMap.put("code", "success");
        return resultMap;
    }


    @Override
    public Map<String, Object> exportImgToPdf(String activeWorkShopStartTime,
                                              String activeWorkshopEndTime, String taskFinishStartTime,
                                              String taskFinishEndTime, String partnerId,String isworkshopIdFlag)throws Exception {
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        reqMap.put("activeWorkShopStartTime", activeWorkShopStartTime);
        reqMap.put("activeWorkshopEndTime", activeWorkshopEndTime);
        reqMap.put("taskFinishStartTime", taskFinishStartTime);
        reqMap.put("taskFinishEndTime", taskFinishEndTime);
        reqMap.put("partnerId", partnerId);
        reqMap.put("isworkshopIdFlag", isworkshopIdFlag);
        List<ExportImgForActiveWorkshop> lstExportAtts = mainTaskMapper.exportActiveWorkshopImg(reqMap);
        String fileSep = System.getProperty("file.separator");
        String realPath = FileManager.fileUploadPath;
        String  filePath = "";
        List<String> lstFileNames = null;
        //按合伙人分组排序。。。或者用hashMap;
        List<String> lstPartners = new ArrayList<String>();
        Map<String,Object> partnerImgMap = new HashMap<String,Object>();
        for(ExportImgForActiveWorkshop taskForXDAttFile:lstExportAtts)
        {
            String partnerName = taskForXDAttFile.getOrganizationName().trim();
            if(!lstPartners.contains(partnerName))
            {
                lstPartners.add(partnerName);
                lstFileNames = new ArrayList<String>();
            }
            filePath = realPath+taskForXDAttFile.getStoragePath()+fileSep+taskForXDAttFile.getStorageName();
            lstFileNames.add(filePath);
            partnerImgMap.put(partnerName, lstFileNames);
        }

        //按合伙人导出对应的图片，生成pdf文件
        List<String> lstdownpdfNames = new ArrayList<String>();//用于下载的文件名，后续可能使用
        for(int i=0;i<lstPartners.size();i++)
        {
            String partnername = lstPartners.get(i);
            String pdfName = FileManager.fileUploadPath+partnername+"_"+DateUtil.getCurrentDate("yyyy-MM-dd")+".pdf";
            String downpdfName = partnername+"_"+DateUtil.getCurrentDate("yyyy-MM-dd")+".pdf";
            List<String> imgFileNames = (List<String>) partnerImgMap.get(partnername);
            lstdownpdfNames.add(downpdfName);
            if(null==imgFileNames||imgFileNames.isEmpty())
            {
                resultMap.put("warning", "没有要导出的图片");
                resultMap.put("partnername", partnername);
                log.info("没有找到符合条件导出的图片,合伙人："+partnername);
                continue;
            }
            resultMap.put("lstdownpdfNames", lstdownpdfNames);
            resultMap.put("code", "success");
            TaskServController.createPdf(pdfName,imgFileNames);
        }
        return resultMap;
    }



    @Override
    public Map<String, Object> exportImgToPdf(String activeWorkShopStartTime,
                                              String activeWorkshopEndTime, String taskFinishStartTime,
                                              String taskFinishEndTime, String partnerId,
                                              String isworkshopIdFlag, String attCreatTime1, String attCreatTime2)
            throws Exception {
        //0.获取所有的
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        reqMap.put("activeWorkShopStartTime", activeWorkShopStartTime);
        reqMap.put("activeWorkshopEndTime", activeWorkshopEndTime);
        reqMap.put("taskFinishStartTime", taskFinishStartTime);
        reqMap.put("taskFinishEndTime", taskFinishEndTime);
        reqMap.put("partnerId", partnerId);
        reqMap.put("isworkshopIdFlag", isworkshopIdFlag);
        List<ExportImgForActiveWorkshop> lstExportAtts = mainTaskMapper.exportActiveWorkshopImg(reqMap);
        log.info("all-----lstExportAtts:"+lstExportAtts.size());


        //1.分组<attCreatTime1  (04-07)  计算个数（未有水印）
        Date tmpattCreatTime1 = DateUtils.parseDate(attCreatTime1 ,0);
        Date tmpattCreatTime2 = DateUtils.parseDate(attCreatTime2 ,0);

        long creatTime1 = tmpattCreatTime1.getTime();
        long creatTime2 = tmpattCreatTime2.getTime();

        List<ExportImgForActiveWorkshop> lstExportAttsStep1 = new ArrayList<ExportImgForActiveWorkshop>();
        //2.分组>=04.07  <04-13   且为ios的  计算个数（未有水印）
        List<ExportImgForActiveWorkshop> lstExportAttsStep2 = new ArrayList<ExportImgForActiveWorkshop>();
        //3.分组>=04.07  <04-13   且为android的  计算个数（有水印）
        List<ExportImgForActiveWorkshop> lstExportAttsStep3 = new ArrayList<ExportImgForActiveWorkshop>();
        //4.分组>=04-13   计算个数（有水印）
        List<ExportImgForActiveWorkshop> lstExportAttsStep4 = new ArrayList<ExportImgForActiveWorkshop>();
        for(ExportImgForActiveWorkshop tempW:lstExportAtts)
        {
            String tmpfileName = tempW.getFileName();
            Long attTmpCreateTime = tempW.getAttCreatTime().getTime();
            if(creatTime1-attTmpCreateTime>0)
            {
                lstExportAttsStep1.add(tempW);
            }

            if(attTmpCreateTime-creatTime1>=0 && creatTime2-attTmpCreateTime>0 && tmpfileName.startsWith("ios"))
            {
                lstExportAttsStep2.add(tempW);
            }
            else if(attTmpCreateTime-creatTime1>=0 && creatTime2-attTmpCreateTime>0)
            {
                lstExportAttsStep3.add(tempW);
            }

            if(attTmpCreateTime-creatTime2>=0)
            {
                lstExportAttsStep4.add(tempW);
            }

        }
        log.info("exportImgToPdf-----lstExportAttsStep1:"+lstExportAttsStep1.size());
        log.info("exportImgToPdf-----lstExportAttsStep2:"+lstExportAttsStep2.size());
        log.info("exportImgToPdf-----lstExportAttsStep3:"+lstExportAttsStep3.size());
        log.info("exportImgToPdf-----lstExportAttsStep4:"+lstExportAttsStep4.size());


        List<ExportImgForActiveWorkshop> needCreatWarterImgLst = new ArrayList<ExportImgForActiveWorkshop>();
        needCreatWarterImgLst.addAll(lstExportAttsStep1);
        needCreatWarterImgLst.addAll(lstExportAttsStep2);

        List<ExportImgForActiveWorkshop> notNeedCreatWarterImgLst = new ArrayList<ExportImgForActiveWorkshop>();
        notNeedCreatWarterImgLst.addAll(lstExportAttsStep3);
        notNeedCreatWarterImgLst.addAll(lstExportAttsStep4);



        //生成水印
        String fileSep = System.getProperty("file.separator");
        String realPath = FileManager.fileUploadPath;
        //List<ExportImgForActiveWorkshop> tempneedCreatWarterImgLst = new ArrayList<ExportImgForActiveWorkshop>();
        for(ExportImgForActiveWorkshop needWarter : needCreatWarterImgLst)
        {
            String sourceFileName = realPath+needWarter.getStoragePath()+fileSep+needWarter.getStorageName();
            if(!needWarter.getStoragePath().contains(needWarter.getOrganizationName()))
            {
                needWarter.setStoragePath("new_img"+fileSep+needWarter.getStoragePath()+fileSep+needWarter.getOrganizationName());
            }else
            {
                needWarter.setStoragePath("new_img"+fileSep+needWarter.getStoragePath());
            }
            String targetFileName = realPath+needWarter.getStoragePath()+fileSep+needWarter.getStorageName();
            log.info("-----sourceFileName:"+sourceFileName);
            log.info("targetFileName-----:"+targetFileName);
            ImageWaterSetUtil.makeWaterImageNew(sourceFileName, targetFileName, Color.WHITE, needWarter.getWorkShopName());
        }

        List<ExportImgForActiveWorkshop> allExportAtt = new ArrayList<ExportImgForActiveWorkshop>();
        allExportAtt.addAll(needCreatWarterImgLst);
        allExportAtt.addAll(notNeedCreatWarterImgLst);

        Collections.sort(allExportAtt, new Comparator<ExportImgForActiveWorkshop>() {
            public int compare(ExportImgForActiveWorkshop o1, ExportImgForActiveWorkshop o2) {
                return o1.getOrganizationName().trim().compareTo(o2.getOrganizationName().trim());
            }
        });


        //3/4/5步骤生成pdf文件
        String  filePath = "";
        List<String> lstFileNames = null;
        //按合伙人分组排序。。。或者用hashMap;
        List<String> lstPartners = new ArrayList<String>();
        Map<String,Object> partnerImgMap = new HashMap<String,Object>();
        for(ExportImgForActiveWorkshop taskForXDAttFile:allExportAtt)
        {
            String partnerName = taskForXDAttFile.getOrganizationName().trim();
            if(!lstPartners.contains(partnerName))
            {
                lstPartners.add(partnerName);
                lstFileNames = new ArrayList<String>();
            }
            filePath = realPath+taskForXDAttFile.getStoragePath()+fileSep+taskForXDAttFile.getStorageName();
            lstFileNames.add(filePath);
            partnerImgMap.put(partnerName, lstFileNames);
        }
        //按合伙人导出对应的图片，生成pdf文件
        List<String> lstdownpdfNames = new ArrayList<String>();//用于下载的文件名，后续可能使用
        for(int i=0;i<lstPartners.size();i++)
        {
            String partnername = lstPartners.get(i);
            String pdfName = FileManager.fileUploadPath+partnername+"_"+DateUtil.getCurrentDate("yyyy-MM-dd")+".pdf";
            String downpdfName = partnername+"_"+DateUtil.getCurrentDate("yyyy-MM-dd")+".pdf";
            List<String> imgFileNames = (List<String>) partnerImgMap.get(partnername);
            lstdownpdfNames.add(downpdfName);
            if(null==imgFileNames||imgFileNames.isEmpty())
            {
                resultMap.put("warning", "没有要导出的图片");
                resultMap.put("partnername", partnername);
                log.info("没有找到符合条件导出的图片,合伙人："+partnername);
                continue;
            }
            resultMap.put("lstdownpdfNames", lstdownpdfNames);
            resultMap.put("code", "success");
            TaskServController.createPdf(pdfName,imgFileNames);
        }
        return resultMap;
    }
    //add by bo.liu end=================================================================================================



    @Override
    public Map<String, Object> exportImgToPdf(Long partnerId, String startTime,
                                              String endTime, Integer xdCount)  {
        log.info("exportImgToPdf mtImg partnerId:"+partnerId+" startTime:"+ startTime +" endTime:"+endTime+" xdCount:"+xdCount);
        Map<String,Object> resultMap = new HashMap<String,Object>();
        Map<String,Object> reqMap = new HashMap<String,Object>();
        try
        {
            reqMap.put("partnerId", partnerId);
            reqMap.put("startTime", startTime);
            reqMap.put("endTime", endTime);
            reqMap.put("cishu", xdCount);
            // 0.查询数据,,组装数据
            List<ExportMtImg> lst = mainTaskMapper.exportMtImgPdf(reqMap);
            if(null==lst || lst.isEmpty())
            {
                throw new Exception("exportImgToPdf lst is null or empty!!!!");
            }
            Map<Integer,Object> exportDataMap = doGenerateExportMtImgData(lst);

            // 1.生产excel文件
            WebApplicationContext webApplicationContext = ContextLoader
                    .getCurrentWebApplicationContext();
            ServletContext servletContext = webApplicationContext
                    .getServletContext();
            String fileName = lst.get(0).getOrgName()+startTime+"_"+endTime;
            boolean excelFlag = exportExcel.exportWorkshopImgData(exportDataMap, "com.chevron.task.model.ExportMtImg",fileName,servletContext);


            // 2.组装数据，生产水印
            doCreateWaterImg(lst);


            // 3.生产pdf文件
            doCreatePdf(exportDataMap,fileName);
            resultMap.put("code", "success");
        }catch (Exception e) {
            e.printStackTrace();
            resultMap.put("code", "error");
            resultMap.put("msg", e.getMessage()+"\n"+e.getLocalizedMessage());
        }
        return resultMap;
    }

    private void doCreatePdf(Map<Integer, Object> exportDataMap,String fileName)throws Exception {
        String fileSep = System.getProperty("file.separator");
        String realPath = FileManager.fileUploadPath;
        for(Map.Entry<Integer, Object> entry: exportDataMap.entrySet())
        {
            String pdfName = realPath;
            List<ExportMtImg> tmpLst = (List<ExportMtImg>) entry.getValue();
            String partFileName = tmpLst.get(0).getDataMonth();
            String partnerNmae =  tmpLst.get(0).getOrgName();
            List<String> imgFileNames = new ArrayList<String>();
            for(ExportMtImg tmpImg: tmpLst)
            {
                imgFileNames.add(realPath+"new_img"+fileSep+tmpImg.getMtImgName());
            }
            pdfName = pdfName+partnerNmae+"_"+partFileName+"月.pdf";
            TaskServController.createPdf(pdfName,imgFileNames);
        }


    }

    private void doCreateWaterImg(List<ExportMtImg> lst)throws Exception {
        String fileSep = System.getProperty("file.separator");
        String realPath = FileManager.fileUploadPath;
        for(ExportMtImg needWarter : lst)
        {
            String sourceFileName = realPath+needWarter.getMtImgPath()+fileSep+needWarter.getMtImgName();
            String targetFileName = realPath+"new_img"+fileSep+needWarter.getMtImgName();
            log.info("-----sourceFileName:"+sourceFileName);
            log.info("targetFileName-----:"+targetFileName);
            ImageWaterSetUtil.makeWaterImageNew(sourceFileName, targetFileName, Color.WHITE, needWarter.getWorkshopName());
        }

    }

    private Map<Integer, Object> doGenerateExportMtImgData(
            List<ExportMtImg> lst)throws Exception {
        Map<Integer,Object> dataMap = new HashMap<Integer,Object>();
        String lastDmonth = "";
        String currentDMontd = "";
        int i = -1;
        List<ExportMtImg> tmpExportMtImgLst = null;
        for(ExportMtImg tmpExport: lst)
        {
            currentDMontd = tmpExport.getDataMonth();
            if(!lastDmonth.equals(currentDMontd))
            {
                tmpExportMtImgLst = new ArrayList<ExportMtImg>();
                i+=1;
            }
            tmpExportMtImgLst.add(tmpExport);
            dataMap.put(i, tmpExportMtImgLst);
            lastDmonth = currentDMontd;
        }

        return dataMap;
    }

    @Override
    public Map<String, Object> exportImgToPdf(List<Long> lstPartnerIds, String startTime, String endTime, Integer xdCount) {
        log.info("exportImgToPdf lst:{}"+lstPartnerIds+"  startTime:{}"+startTime+" endTime:"+endTime+" xdCount:"+xdCount);
        Map<String,Object> resultMap = new HashMap<String,Object>();
        try
        {
            if(null==lstPartnerIds || lstPartnerIds.isEmpty())
            {
                throw new Exception("合伙人列表不能为空");
            }
            for(Long partnerId: lstPartnerIds)
            {
                resultMap = exportImgToPdf(partnerId,startTime,endTime,xdCount);
            }
            Log logInfo = new Log();
            logInfo.setOperator(1L);
            logInfo.setLogType("export_mt_photo");
            logInfo.setCreateTime(new Date());
            logInfo.setExtProperty1("xiaoliu");
            logService.save(logInfo);


        }catch (Exception ex)
        {
            ex.printStackTrace();
            resultMap.put("code", "error");
            resultMap.put("msg", ex.getMessage()+"\n"+ex.getLocalizedMessage());
        }
        return resultMap;
    }

    @Override
    @Transactional
    public JsonResponse updateTaskStepInfo(List<TaskInstanceCheckVo> setpList) {
        JsonResponse resMap = new JsonResponse();
        if(CollectionUtil.isEmpty(setpList)) {
            resMap.put(JsonResponse.KEY_ERROR_MSG, "更新的检查项为空！");
        }
        for (TaskInstanceCheckVo taskInstanceCheckVo : setpList) {
            if(null == taskInstanceCheckVo.getId()) {
                resMap.put(JsonResponse.KEY_ERROR_MSG, "更新的检查项id不能为空！");
                break;
            }
            if(null == instanceCheckVoMapper.selectByPrimaryKey(taskInstanceCheckVo.getId())) {
                resMap.put(JsonResponse.KEY_ERROR_MSG, "系统不存在id为"+taskInstanceCheckVo.getId()+"检查项！");
                break;
            }
            taskInstanceCheckVo.setXgSj(new Date());
            taskInstanceCheckVo.setXgUser(ContextUtil.getCurUserId());
            instanceCheckVoMapper.updateByPrimaryKeySelective(taskInstanceCheckVo);
        }
        return resMap;
    }

    @Override
    public Map<String, Object> handleForCdyjzm(TaskHandleFleetProveVo vo) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //手动开启事务
        DataSourceTransactionManager transactionManager = SpringUtils.getBean(DataSourceTransactionManager.class);
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = transactionManager.getTransaction(def);
        WxTUser currentUser = ContextUtil.getCurUser();
        try {
            //更新子任务表相关数据
            FleetPerProve fleetPerProve = FleetPerProve.getByCode(vo.getRankValue());
            //如果评价为不合格代表本次流程驳回
            if (FleetPerProve.UNQUALIFIED.equals(fleetPerProve)) {
                log.debug("评价为不合格，走原本的不合格的处理...");
                Map<String, Object> map = this.updateTaskMainOrSubTaskStatus(0, vo.getCheckResult(), vo.getMainTaskId().toString(), vo.getSubTaskId().toString(), vo.getCheckCount(), null, null);
                if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_YJZM)){
                    WxTaskSub subTask = new WxTaskSub();
                    subTask.setAttribute1(fleetPerProve.getCode());
                    subTask.setTaskId(vo.getSubTaskId());
                    subTask.setXgUser(currentUser.getUserId());
                    subTask.setXgUserName(currentUser.getChName());
//                    subTask.setCheckResult(vo.getCheckResult());
//                    subTask.setXgSj(new Date());
                    subtaskMapper.updateByPrimaryKeySelective(subTask);
                }
                transactionManager.commit(status);
                return map;
            }
//            WxTUser currentUser = ContextUtil.getCurUser();
            WxTaskSub wxTaskSub = subtaskMapper.selectByPrimaryKey(vo.getSubTaskId());
            if ((FleetPerProve.HIGH.equals(fleetPerProve) || FleetPerProve.ORDINARY.equals(fleetPerProve)) && StringUtils.isBlank(wxTaskSub.getAttribute1())) {
                WxTaskSub subTask = new WxTaskSub();
                subTask.setAttribute1(fleetPerProve.getCode());
                subTask.setTaskId(vo.getSubTaskId());
                subTask.setXgUser(currentUser.getUserId());
                subTask.setXgUserName(currentUser.getChName());
                subTask.setCheckResult(vo.getCheckResult());
                subTask.setXgSj(new Date());
                subtaskMapper.updateByPrimaryKeySelective(subTask);
            }
            WxTaskMain wxTaskMain = mainTaskMapper.selectByPrimaryKey(vo.getMainTaskId());
            WxTUser wxTUser = userMapper.selectByPrimaryKey(wxTaskMain.getCreateUser());
            if (FleetPerProve.ORDINARY.equals(fleetPerProve)) {
                log.debug("生成PDF模板并发送邮件...");
                //需要生成PDF模板并发送邮件将模板文件写入到数据库中
                String storageName = vo.getFleetName() == null ? "" : vo.getFleetName() + "业绩证明设计文件" + DateUtil.getCurrentDate(DateUtil.DATA_TIME_PATTERN_NO_CHAR) + ".pdf";
                Map<String, Object> data = this.getCdyzjzApplyInfo(vo, wxTaskMain);
                String storePath = FileUtil.getFolderName(GregorianCalendar.MONTH) + "/" + ContextUtil.getCurUser().getOrgName();
                String realPath = FileManager.getFileRealPath(WxAttFile.SOURCETYPE_TASK_YJZM_PDF, storePath);
//                String designFtl =  vo.getTaskType().equals(WxTaskMain.TASK_TYPE_PROJECT_CLIENT_YJZM)?GCJX_DESIGN_PDF_FTL:CDYJZM_DESIGN_PDF_FTL;
                //2021-05-12
                String designFtl = "";
                if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_PROJECT_CLIENT_YJZM)){
                    designFtl = GCJX_DESIGN_PDF_FTL;
                }else if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_CDYJZM)){
                    designFtl = CDYJZM_DESIGN_PDF_FTL;
                }else if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_YJZM)){
                    designFtl = YJZM_DESIGN_PDF_FTL;
                }
                File file = this.generatePdf(realPath, storageName, designFtl, data);
                String fileType = "application/pdf";
                Long fileSize = file.length();
                // 更新附件信息表
                fileManagerService.addAttFile(vo.getSubTaskId(), storageName, storageName, fileType,
                        WxAttFile.SOURCETYPE_TASK_YJZM_PDF, storePath, fileSize, ContextUtil.getCurUserId(), "1");
                this.sendDesignDocument(vo, wxTUser);
                WxTaskSub updateSubPdfFiles = new WxTaskSub();
                updateSubPdfFiles.setAttribute2(FleetPerStatus.DESIGNUPLOAD.getCode());
                updateSubPdfFiles.setTaskId(vo.getSubTaskId());
                subtaskMapper.updateByPrimaryKeySelective(updateSubPdfFiles);
            } else if (Boolean.TRUE.equals(vo.getSendPdf())) { //先将市场部上传的pdf文件存到数据库，然后发送邮件
                log.debug("市场部已经上传设计文件，开始发送邮件...");
                this.sendDesignDocument(vo, wxTUser);
                WxTaskSub updateSubPdfFiles = new WxTaskSub();
                updateSubPdfFiles.setAttribute2(FleetPerStatus.DESIGNUPLOAD.getCode());
                updateSubPdfFiles.setTaskId(vo.getSubTaskId());
                subtaskMapper.updateByPrimaryKeySelective(updateSubPdfFiles);
            }
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
            transactionManager.commit(status);
        } catch (Exception ex) {
            transactionManager.rollback(status);
            ex.printStackTrace();
            resultMap.put("code", "error");
            resultMap.put("msg", ex.getMessage() + "\n" + ex.getLocalizedMessage());
        }
        return resultMap;
    }

    /**
     * 提交盖章之后pdf文件
     *
     * @param subTaskId
     * @param reCommit
     * @return
     */
    @Override
    public Map<String, Object> commitFleetPerformance(Long subTaskId, Boolean reCommit) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        WxTaskSub wxTaskSub = new WxTaskSub();
        wxTaskSub.setTaskId(subTaskId);
        WxAttFileExample fileExample = new WxAttFileExample();
        fileExample.createCriteria().andSourceIdEqualTo(subTaskId).andSourceTypeEqualTo(WxAttFile.SOURCETYPE_TASK_YJZM_FILING);
        int count = wxAttFileMapper.countByExample(fileExample);
        //如果手机端删除了所有的附件，那么这里需要回退到市场部已经上传设计文件
        if (count > 0) {
            wxTaskSub.setAttribute2(FleetPerStatus.STAMP.getCode());
        } else {
            wxTaskSub.setAttribute2(FleetPerStatus.DESIGNUPLOAD.getCode());
        }
        wxTaskSub.setXgSj(new Date());
        wxTaskSub.setXgUser(ContextUtil.getCurUser().getUserId());
        wxTaskSub.setXgUserName(ContextUtil.getCurUser().getChName());
        try {
            if (reCommit != null && reCommit) {
                wxTaskSub.setIsPass("-1");
                wxTaskSub.setCheckEvaluation(-1);
                wxTaskSub.setTaskStatus("4");
            }
            subtaskMapper.updateByPrimaryKeySelective(wxTaskSub);
            resultMap.put("code", "success");
            resultMap.put("codeMsg", "操作成功");
        } catch (Exception ex) {
            ex.printStackTrace();
            resultMap.put("code", "error");
            resultMap.put("msg", ex.getMessage() + "\n" + ex.getLocalizedMessage());
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> exportFleetPerHignPDF(Long subTaskId) {
        log.debug("导出优质车队业绩证明模板...");
        try {
	        WxTaskSub wxTaskSub = subtaskMapper.selectByPrimaryKey(subTaskId);
	        if (wxTaskSub == null) {
	            return null;
	        }
	        WxTaskMain wxTaskMain = mainTaskMapper.selectByPrimaryKey(wxTaskSub.getTaskMainId());
	       // WxTUser wxTUser = userMapper.selectByPrimaryKey(wxTaskMain.getCreateUser());
	        WorkshopMaster fleetInfo= workshopEmployeeBizServiceImpl.getBean(wxTaskSub.getOrgId());
	        TaskHandleFleetProveVo vo = new TaskHandleFleetProveVo();
	        if (fleetInfo != null) {
	            vo.setFleetName(fleetInfo.getWorkshopName());
	        }
	        vo.setSubTaskId(subTaskId);
	        Map<String, Object> data = this.getCdyzjzApplyInfo(vo, wxTaskMain);
	        return data;
        } catch (WxPltException e) {
			e.printStackTrace();
			return null;
		}
    }

    private Map<String, Object> getCdyzjzApplyInfo(TaskHandleFleetProveVo vo,WxTaskMain wxTaskMain) {
        HashMap<String, Object> data = new HashMap<String, Object>();
        data.put("fleetName", vo.getFleetName());
        data.put("rootPath", FileManager.fileUploadPath);
        //获取所有检查项
        HashMap<String, Object> reqMap = new HashMap<String, Object>();
        reqMap.put("taskMainId", wxTaskMain.getTaskMainId());
        reqMap.put("taskSubId", vo.getSubTaskId());
        //关联查询获取，，当前任务对应的实例，从表wx_task_instance_check表中获取
        List<TaskInstanceCheckVo> lstStepCheck = instanceCheckVoMapper.getSubTaskStepCheckInstance(reqMap);
        for (TaskInstanceCheckVo taskInstanceCheckVo : lstStepCheck) {
            String checkValue = taskInstanceCheckVo.getCheckValue();
            checkValue = checkValue == null ? "" : checkValue;
            String checkName = taskInstanceCheckVo.getCheckName();
            if(checkName.contains("(")){
                checkName = checkName.substring(0, checkName.indexOf("("));
                taskInstanceCheckVo.setCheckName(checkName);
            }

            taskInstanceCheckVo.setCheckValue(ArrayUtil.join(StrUtil.split(checkValue, 56), "<br/>"));

            String remark = taskInstanceCheckVo.getRemark();
            remark = remark == null ? "" : remark;
            if("使用雪佛龙产品型号及换油周期".equals(taskInstanceCheckVo.getCheckName()) || "之前使用竞品品牌,型号及换油周期".equals(taskInstanceCheckVo.getCheckName())){
                taskInstanceCheckVo.setRemark(ArrayUtil.join(StrUtil.split(remark, 26), "<br/>"));
            }else{
                taskInstanceCheckVo.setRemark(ArrayUtil.join(StrUtil.split(remark, 56), "<br/>"));
            }

            //
//            if (taskInstanceCheckVo.getPhotoType() == 1){
//                String trimStr = StringUtils.trimStr(taskInstanceCheckVo.getCheckName(), "请拍摄");
//                checkName = StringUtils.trimStr(trimStr, "照片");
//                taskInstanceCheckVo.setCheckName(checkName);
//            }
            ArrayList<Long> list = new ArrayList<Long>();
            if (taskInstanceCheckVo.getPhotoType() == 1){
                list.add(taskInstanceCheckVo.getId());
            }
            reqMap.clear();
            reqMap.put("instanceIdlst", list);
            reqMap.put("sourceType", WxAttFile.SourceType_Check);
            if (CollectionUtil.isNotEmpty(list)) {
                //放入获取文件的根路径
                List<WxAttFile> wxAttFiles = wxAttFileMapper.getWxAttFiles(reqMap);
                if(wxAttFiles.size() >= 2){
                    List<WxAttFile> wxAttFiles1 = wxAttFiles.subList(0, 2);
                    taskInstanceCheckVo.setAttFileList(wxAttFiles1);
                }else {
                    taskInstanceCheckVo.setAttFileList(wxAttFiles);
                }
            }
        }
        data.put("checks", lstStepCheck);
        data.put("applyDate", DateUtil.getDateStr(wxTaskMain.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        data.put("evaluate", FleetPerProve.getNameByCode(vo.getRankValue()));
        data.put("now", DateUtil.getDateStr(new Date(), "yyyy-MM-dd"));
        //获取业绩证明图片
        reqMap.clear();
        ArrayList<Long> list = new ArrayList<Long>();
        for (TaskInstanceCheckVo taskInstanceCheckVo : lstStepCheck) {
            if (taskInstanceCheckVo.getPhotoType() == 1)//如果要求上传图片就添加到过滤中，
            {
                list.add(taskInstanceCheckVo.getId());
            }
        }
        reqMap.put("instanceIdlst", list);
        reqMap.put("sourceType", WxAttFile.SourceType_Check);
        if (CollectionUtil.isNotEmpty(list)) {
            //放入获取文件的根路径
            data.put("rootPath", FileManager.fileUploadPath);
            data.put("images", wxAttFileMapper.getWxAttFiles(reqMap));
        }

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes()).getRequest();
        String baseUrl = request.getSession().getServletContext().getRealPath("/images/logo3.png");
        data.put("baseUrl", baseUrl);

        data.put("logoUrl", request.getSession().getServletContext().getRealPath("/WEB-INF/templates/images/logo.png"));
        data.put("titleUrl", request.getSession().getServletContext().getRealPath("/WEB-INF/templates/images/title.png"));
        return data;
    }

    private void sendDesignDocument(TaskHandleFleetProveVo vo, WxTUser wxTUser) throws Exception {
        WxAttFileExample fileExample = new WxAttFileExample();
        fileExample.createCriteria().andSourceIdEqualTo(vo.getSubTaskId()).andSourceTypeEqualTo(WxAttFile.SOURCETYPE_TASK_YJZM_PDF);
        List<WxAttFile> wxAttFiles = wxAttFileMapper.selectByExample(fileExample);
        if (CollectionUtil.isEmpty(wxAttFiles)) {
            throw new WxPltException("没有找到设计文档。");
        }
        WebApplicationContext webApplicationContext = ContextLoader
                .getCurrentWebApplicationContext();
        ServletContext servletContext = webApplicationContext
                .getServletContext();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("fleetName", vo.getFleetName() == null ? "" : vo.getFleetName());
        List<File> fileList = new ArrayList<File>();
        for (WxAttFile wxAttFile : wxAttFiles) {
            String path = FileManager.fileUploadPath + wxAttFile.getStorePath() + File.separator + wxAttFile.getStorageName();
            File file = new File(path);
            if (file.exists()) {
                fileList.add(file);
            }
        }
        String rank = FleetPerProve.getNameByCode(vo.getRankValue());
        dataMap.put("rank", rank);
        String subjectName = "雪佛龙业绩证明提醒";
        String ccDicType = CDYJZM_CC_DIC_TYPE;
        String emailFtl = CDYJZM_DESIGN_EMAIL_FTL;
        if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_CDYJZM)){
            subjectName = "雪佛龙德乐车队业绩证明提醒";
            ccDicType = CDYJZM_CC_DIC_TYPE;
            emailFtl = CDYJZM_DESIGN_EMAIL_FTL;
        }else if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_PROJECT_CLIENT_YJZM)){
            subjectName = "雪佛龙工程机械客户业绩证明提醒";
            ccDicType = GCJX_CC_DIC_TYPE;
            emailFtl = GCJX_DESIGN_EMAIL_FTL;
        }else if(vo.getTaskType().equals(WxTaskMain.TASK_TYPE_YJZM)){
            subjectName = "雪佛龙业绩证明提醒";
            ccDicType = YJZM_CC_DIC_TYPE;
            emailFtl = YJZM_DESIGN_EMAIL_FTL;
        }
        List<DicItemVo> dicItemVos = dicItemVoMapper.selectByCode(ccDicType);
        HashSet<String> ccList = new HashSet<String>();
        if (CollectionUtil.isNotEmpty(dicItemVos)) {
            for (DicItemVo dicItemVo : dicItemVos) {
                ccList.add(dicItemVo.getDicItemName());
            }
        }
        log.debug("开始发送短信和邮件,电话号码: " + wxTUser.getMobileTel() + " 邮件：" + wxTUser.getEmail() + " 抄送列表：" + CollectionUtil.join(ccList, ";"));
        if (StringUtils.isNotBlank(wxTUser.getMobileTel())) {
            SendMessageUtil.sndMessageToPhone(wxTUser.getMobileTel(), vo.getFleetName() + " 的" + rank + "业绩证明设计文件已上传，请到雪佛龙合伙人APP查看。");
        }

        if (StringUtils.isNotBlank(wxTUser.getEmail())) {
            EmailSendUtils.sendEmailForListContent(servletContext, new String[]{wxTUser.getEmail()}, ccList.toArray(new String[ccList.size()]), subjectName, dataMap,
                    fileList.isEmpty() ? null : fileList.toArray(new File[fileList.size()]), emailFtl);
        } else {
            throw new WxPltException(wxTUser.getChName() + " 没有设置邮箱。");
        }
    }

    private File generatePdf(String targetPath, String pdfFileName, String ftlName, Map<String, Object> root) throws Exception {
        String basePath = ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("/");//绝对路径
        Configuration cfg = new Configuration();
        OutputStream baos = null;
        try {
            cfg.setLocale(Locale.CHINA);
            cfg.setEncoding(Locale.CHINA, "UTF-8");
            //设置编码
            cfg.setDefaultEncoding("UTF-8");
            //设置模板路径
            cfg.setDirectoryForTemplateLoading(new File(basePath + "/WEB-INF/templates/"));
            root.put("basePath", basePath);
            //获取模板
            Template template = cfg.getTemplate(ftlName);
            template.setEncoding("UTF-8");
            Writer writer = new StringWriter();
            //数据填充模板
            template.process(root, writer);
            String str = writer.toString();
            log.info(str);
            //pdf生成设置图片转换
            ITextRenderer iTextRenderer = new ITextRenderer();
            iTextRenderer.getSharedContext().setReplacedElementFactory(new Base64ImgReplacedElementFactory());
            iTextRenderer.getSharedContext().getTextRenderer().setSmoothingThreshold(0);
            iTextRenderer.setDocumentFromString(str);
            //设置字体  其他字体需要添加字体库
            ITextFontResolver fontResolver = iTextRenderer.getFontResolver();
            fontResolver.addFont(basePath + "/WEB-INF/simsun.ttc", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            fontResolver.addFont(basePath + "/WEB-INF/simhei.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            String uploadFilePath = FileManager.fileUploadPath;
            iTextRenderer.getSharedContext().setBaseURL("file:" + uploadFilePath);
            iTextRenderer.layout();
            //生成PDF
            if (!cn.hutool.core.io.FileUtil.exist(targetPath)) {
                cn.hutool.core.io.FileUtil.mkdir(targetPath);
            }
            baos = new FileOutputStream(targetPath + "/" + pdfFileName);
            iTextRenderer.createPDF(baos);
            return new File(targetPath + "/" + pdfFileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new WxPltException("普通车队业绩证明生成设计文件失败。");
        } finally {
            IoUtil.flush(baos);
            IoUtil.close(baos);
        }
    }

//	@Override
//	public Map<String, Object> getSubTaskListInfoByParamsType(String taskMainId,String mtaskMainTypecode,String paramsType) {
//		 Map<String,Object> resultMap = new HashMap<String,Object>();
//	        Map<String,Object> reqMap = new HashMap<String,Object>();
//	        try
//	        {
//	            //0.获取对应主任务相关信息，用于页面显示
//	            WxTaskMain mainTaskObj = mainTaskMapper.getMainTaskById(Long.valueOf(taskMainId));
//
//	            //1.查询子任务列表信息
//	            reqMap.put("taskMainId",taskMainId);
//	            List<WxTaskSub> taskSublst = null;
//	            //查询车队子任务列表信息
//	            if(TaskSubParams.PARAMS_TYPE_FLEET.equals(paramsType)) {
//	            	reqMap.put("fleetName", mainTaskObj.getFleetName());
//	            	 taskSublst = subtaskMapper.getWxTaskSubListByFleet(reqMap);
//	            	 mainTaskObj.setFleetAddress(taskSublst.get(0).getFleetAddress());
//	            	 mainTaskObj.setFleetArea(taskSublst.get(0).getFleetArea());
//	            	 mainTaskObj.setFleetId(taskSublst.get(0).getFleetId());
//	            	 mainTaskObj.setFleetName(taskSublst.get(0).getFleetName());
//	            	 mainTaskObj.setFleetType(taskSublst.get(0).getFleetName());
//	            }
//				if(TaskSubParams.PARAMS_TYPE_MACHINERY.equals(paramsType)) {
//					reqMap.put("projectName", mainTaskObj.getProjectName());    
//					  taskSublst = subtaskMapper.getWxTaskSubListByMachinery(reqMap);
//					  mainTaskObj.setMachineryId(taskSublst.get(0).getMachineryId());
//					  mainTaskObj.setProjectAddress(taskSublst.get(0).getProjectAddress());
//					  mainTaskObj.setProjectName(taskSublst.get(0).getProjectName());
//				}
//	    
//
//	            //3.用于前台使用
//	            resultMap.put("taskSublst", taskSublst);
//	            resultMap.put("codeMsg", "操作成功");
//	            resultMap.put("code", "success");
//	            resultMap.put("taskMainObj", mainTaskObj);
//
//	        }catch(Exception ex)
//	        {
//	            resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
//	            resultMap.put("code", "error");
//	            log.error("error",ex);
//	        }
//
//	        return resultMap;
//	}

    @Override
    public List<TaskTabPermissionInfo> newCustomerTabInfo(String type) {
        List<TaskTabPermissionInfo> list = new ArrayList<TaskTabPermissionInfo>();
        List<DicItemVo> permissions = dicItemVoMapper.selectByCode(type);
        for (DicItemVo permission : permissions) {
            TaskTabPermissionInfo item = new TaskTabPermissionInfo();
            item.setTabName(permission.getDicItemCode());
            item.setTabUrl(permission.getDicItemName());
            if(permission.getSortNumb() != null){
                item.setId(permission.getSortNumb().intValue());
            }
            if(StringUtils.isNotBlank(permission.getDicItemDesc())) {
                String[] split = permission.getDicItemDesc().split(",");
                item.setRoles(Arrays.asList(split));
            }
            list.add(item);
        }
        return list;
    }
}
