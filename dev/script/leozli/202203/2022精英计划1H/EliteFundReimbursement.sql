select f1.distributor_id as org_id,crss.customer_name_cn customer_name_cn,ecc1.customer_name_en, crss.sales_name_cn sales_name, 
'润滑油产品' product_name, f1.product_channel, f1.product_channel sales_channel,
case when #elitePhase#='q1q2' then 'Q1-Q2' else 'Q3-Q4' end quarter,'13%' rate,
isnull(di.dic_item_name,ecc1.company_code) company_code, f1.customer_code,
convert(varchar(10),round(isnull(convert(float,di.dic_item_desc),1) * 100,2)) + '%' proportion,
f1.fund_value*isnull(convert(float,di.dic_item_desc),1) fund_value, 
round(f1.fund_value*isnull(convert(float,di.dic_item_desc),1)/1.13,2) fund_value_rated, di1.dic_item_name company_name,
(case when #year#='2021' and #elitePhase#='q1q2' then 'v1/' else 'v2/' end) 
	+ 'elite_fund_Tax_Invoicing_Rebate_' + isnull(di.dic_item_name,ecc1.company_code) 
	+ (case when #year#='2021' and #elitePhase#='q1q2' then '' when exists (select 1 from [PP_MID].dbo.mid_partner_sale_config psc where psc.distributor_id=f1.distributor_id and psc.payment_term='credit' and psc.date_to>=getdate() and psc.date_from<=getdate()) then '_credit' else '_cash' end)
	+ '.ftl' temp_file,
crss.customer_name_cn + '_' 
	+ (case when isnull(di.dic_item_name,ecc1.company_code)='4400' then '北京总公司' else di1.dic_item_name end)
	+ '_' + f1.product_channel + '_' + isnull(di.dic_item_name,ecc1.company_code) 
	+  (case when #year#='2021' and #elitePhase#='q1q2' then '' when exists (select 1 from [PP_MID].dbo.mid_partner_sale_config psc where psc.distributor_id=f1.distributor_id and psc.payment_term='credit' and psc.date_to>=getdate() and psc.date_from<=getdate()) then ' Credit' else ' Cash' end) file_name
from (
	select f.distributor_id, 
	SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0) - isnull(f.consumer_ivi_fund, 0) 
					- isnull(f.consumer_ivi_fund_adjust, 0) - isnull(f.consumer_annual_reward, 0)) fund_value, 'Consumer' product_channel, 1 channel_weight, f.customer_code
	from dw_mid_cal_elite_fund f where f.year=#year# and #elitePhase#=(case when f.quarter in ('Q1','Q2') then 'q1q2' else 'q3q4' end)
	group by f.distributor_id, f.customer_code
	union all 
	select f.distributor_id, SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)  - isnull(f.commercial_ivi_fund, 0) 
					- isnull(f.commercial_ivi_fund_adjust, 0) - isnull(f.[commercial_annual_reward], 0)) fund_value, 'Commercial' product_channel, 2 channel_weight, f.customer_code
	from dw_mid_cal_elite_fund f where f.year=#year# and #elitePhase#=(case when f.quarter in ('Q1','Q2') then 'q1q2' else 'q3q4' end)
	group by f.distributor_id, f.customer_code
		) f1
left join dw_customer_org_sales crss on f1.distributor_id = crss.distributor_id and f1.channel_weight&crss.channel_weight>0
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=f1.distributor_id
left join wx_t_dic_item di on di.dic_type_code='EliteFund2021.' + #VARCHAR,year# + '.' + #elitePhase# +'.otherCompany' and di.dic_item_code=pe.partner_id
left join (select distributor_id,customer_name_cn,customer_name_en,company_code 
from PP_MID.dbo.syn_dw_to_pp_customer group by distributor_id,customer_name_cn,customer_name_en,company_code) ecc1 on ecc1.distributor_id=f1.distributor_id
left join wx_t_dic_item di1 on di1.dic_type_code='elites.office.detail' and di1.dic_item_code=isnull(di.dic_item_name,ecc1.company_code)
where f1.fund_value != 0 
and (#VARCHAR,distributorIds# is null or CHARINDEX(',' + convert(varchar(20),f1.distributor_id) + ',', ',' + #VARCHAR,distributorIds# + ',') > 0)
and (#productChannel# is null or f1.product_channel=#productChannel#)
order by f1.distributor_id,isnull(di.dic_item_name,ecc1.company_code), f1.customer_code