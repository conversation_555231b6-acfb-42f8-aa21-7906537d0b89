--select top 100 * 
--delete l
--from wx_log l where log_type='fix_product_capacity'
insert into wx_log (operator, log_type,ext_property1,ext_property2,ext_property3,ext_property4,create_time)
select 1,'fix_product_capacity',p.sku, p.name,p.capacity,vtp.value_after_transform,getdate()
from wx_t_product p 
join wx_t_value_transform_map vtp on p.sku like '%' + vtp.value_before_transform and vtp.transform_type='ProductUnitMapping'
where (p.capacity is null or p.capacity='') and p.status=1
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

update p set p.capacity=vtp.value_after_transform
from wx_t_product p 
join wx_t_value_transform_map vtp on p.sku like '%' + vtp.value_before_transform and vtp.transform_type='ProductUnitMapping'
where (p.capacity is null or p.capacity='') and p.status=1
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

select p.capacity,* 
--update p set p.capacity=(select vtp.value_after_transform from wx_t_value_transform_map vtp where p.sku like '%' + vtp.value_before_transform and vtp.transform_type='ProductUnitMapping')
from wx_log l left join wx_t_product p on p.sku=l.ext_property1 
where log_type='fix_product_capacity'


select pack,* 
--update p set p.status=0
from wx_t_product p 
where (p.capacity is null or p.capacity='') and 
p.status=1
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)
--and p.sku like '%FNE'
--and p.sku like '%LMB'
--and p.sku like '%HRB'
--and p.sku like '%HRK'
--and p.sku like '%LPK'CX Hav Formula 10W30 (4x4LP ML3)
--and p.sku like '%DNB'540881FNE


insert into wx_t_in_stock (stock_in_no, stock_to,stock_to_type,order_no,order_type,in_time,status,create_time,creator,ext_property1,
ext_property2) 
select po.id,po.partner_id,'sp',po.order_no, 'SP202111','2021-09-21','2',getdate(),1,'4','418267364'
from wx_t_partner_order po where po.status='3' and po.order_no='P07210812003646'

insert into wx_t_in_stock_product (stock_in_no, sku, expect_in_count, actual_in_count)
select po.id,pl.sku,isnull(pl.free_amount,0)+isnull(pl.actual_amount,0),isnull(pl.actual_amount,0)
from wx_t_partner_order po 
left join wx_t_partner_order_line pl on pl.partner_order_id=po.id
where po.status='3' and po.order_no='P07210812003646' and isnull(pl.actual_amount,0)>0

update po set po.status='7'
from wx_t_partner_order po where po.status='3' and po.order_no='P07210812003646'


SELECT psc.id,org.organization_name AS partner_name, org.id partner_id, psc.payment_term, psc.ship_to_code, psc.sap_code, psc.address,psc.date_from,psc.date_to
				FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					JOIN wx_t_organization org ON org.id = poe.partner_id
					where len(psc.address)>0 and org.status=1 --and psc.distributor_id=208 
					order by psc.distributor_id

SELECT psc.id,org.organization_name AS partner_name, org.id partner_id, psc.payment_term, psc.ship_to_code, psc.sap_code, psc.address,psc.date_from,psc.date_to,
s1.name store_name
				FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					JOIN wx_t_organization org ON org.id = poe.partner_id
					left join wx_t_store2021 s1 on s1.id=psc.store_id
					where len(psc.address)>0 and org.status=1 and psc.[creation_time]>='2021-12-08'
					
select distinct bsi.[sales_reference_doc] sales_order, bso.region,isnull(bc.[distributor_id_pp],bc.[distributor_id]) distributor_id,
				bsi.[customer_code] sold_to_code,bsi.[product_code_SAP] product_sku, p.name,
				di.dic_item_name pack_units,
				convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0)) quantity, 
				round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0) * bp.unit liters,
				bsi.revenue_rmb, bsi.[base_trans_sell_in_id], bsi.[trans_time] post_year_month,bsi.[pricing_date]
				from[PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi 
				left join [PP_MID].[dbo].[syn_dw_to_pp_customer] bc on bc.[customer_code]=bsi.[customer_code]
				left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] bso on bsi.org_hier_id=bso.org_hier_id
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
				left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
				left  join wx_t_product p on p.sku=bsi.[product_code_SAP]
		where bsi.[trans_time]>='2021-08-01'and bsi.liters>0
		and convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0))>0

		
select bsi.[sales_reference_doc] order_no, pe.distributor_id, pe.partner_id,
				max(bsi.trans_time) post_year_month, max(bsi.pricing_date) pricing_date,
				max(o.organization_name) partner_name,
				sum(round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0) * bp.unit) total_liter_count
				from[PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi 
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
				join wx_t_partner_o2o_enterprise pe on pe.distributor_id=bsi.distributor_id
				join wx_t_organization o on o.id=pe.partner_id
		where bsi.[trans_time]>='2021-08-01'and bsi.liters>0
		and convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - 
		bsi.[below_standard_price_liters_business])/bp.unit, 0))>0
		group by bsi.[sales_reference_doc], pe.distributor_id, pe.partner_id
		
select o.organization_name "经销商名称", sc.ship_to_code, sc.sap_code sold_to_code, po.order_no "订单编号",pl.sku sku, p.name "产品名称",--bp.[pack_type] sales_unit,
pl.actual_amount "下单数量", pl.free_amount "赠送数量", pl.total_value "总价",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=1) "提交时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=3) "确认时间",
(select p.dic_item_name from wx_t_dic_item p where p.dic_type_code = 'PARTNER_ORDER_STATUS' and p.dic_item_code = po.status) as "状态"
				from wx_t_partner_order po 
				left join wx_t_partner_order_line pl on pl.partner_order_id=po.id
                --left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=pl.sku
				--left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
				left join wx_t_product p on p.sku=pl.sku
				left join wx_t_organization o on o.id=po.partner_id
				left join [PP_MID].[dbo].[mid_partner_sale_config] sc on po.partner_sale_config_id=sc.id
		where po.status not in (0,4) and po.create_time>'2021-01-01' --and po.order_no='P07211008005253'
		order by po.id desc

select sku, name, case when p.category='TCP' then '是' else '否' end "特劲产品(后续产品无物流信息)", 
(select count(1) from wx_t_oem_delivery_product dp where dp.product_id=p.sku) "中商物流条数",
(select count(1) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "中商激活条数(生产批次数)",
(case when exists (select 1 from temp_qr_sku qs where qs.sku=p.sku) then '是' else '否' end) "生成QR"
--distinct category, is_competing 
from wx_T_product p where (category is null or category!='QT') and status=1 and pack>0
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

select * 
--update s set s.order_type='DISTRIBUTOR_DELIVERY_ORDER', s.ext_property3='P07210812003646'
from wx_t_in_stock s where s.order_no='P07210812003646'
update s set s.order_no=421870402,s.ext_property2='222087745'
from wx_t_in_stock s where s.id=28736--28737
update s set s.order_no=421892921,s.ext_property2='222112306'
from wx_t_in_stock s where s.id=28737

select sku, name, case when p.category='TCP' then '是' else '否' end "特劲产品(后续产品无物流信息)", 
(select count(1) from wx_t_oem_delivery_product dp where dp.product_id=p.sku) "中商物流条数",
(select count(1) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "中商激活条数(生产批次数)",
(case when exists (select 1 from temp_qr_sku qs where qs.sku=p.sku) then '是' else '否' end) "生成QR"
,(case when p.pack/convert(float,p.capacity)>1 then '箱/瓶' else '桶' end) "单位"
--distinct category, is_competing 
from wx_T_product p where (category is null or category!='QT') and status=1 --and pack>0 
and p.support_order=1 --and convert(float,p.capacity)>0
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

select sku, name, case when p.category='TCP' then '是' else '否' end "特劲产品(后续产品无物流信息)", 
(select count(1) from wx_t_oem_delivery_product dp where dp.product_id=p.sku) "中商物流条数",
(select count(1) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "中商激活条数(生产批次数)",
(select min(pp.product_date) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "最早生产日期",
(select max(pp.product_date) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "最新生产日期",
(case when exists (select 1 from temp_qr_sku qs where qs.sku=p.sku) then '是' else '否' end) "生成QR"
,(case when p.pack/convert(float,p.capacity)>1 then '箱/瓶' else '桶' end) "单位"
--distinct category, is_competing 
from wx_T_product p where (category is null or category!='QT') and status=1 --and pack>0 
--and p.support_order=1 --and convert(float,p.capacity)>0
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)
and p.sku in ('503311DNK','503307LMB','503011NJB')


select sku, name, case when p.category='TCP' then '是' else '否' end "特劲产品(后续产品无物流信息)", 
(select count(1) from wx_t_oem_delivery_product dp where dp.product_id=p.sku) "中商物流条数",
(select count(1) from wx_t_oem_product_packaging pp where pp.product_id=p.sku) "中商激活条数(生产批次数)",
(case when exists (select 1 from temp_qr_sku qs where qs.sku=p.sku) then '是' else '否' end) "生成QR"
,(case when p.pack/convert(float,p.capacity)>1 then '箱/瓶' else '桶' end) "单位",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.category' and di1.dic_item_code=p.category) "产品分类",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.oilType' and di1.dic_item_code=p.oil_type) "产品系列",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.capacity' and di1.dic_item_code=p.capacity) "产品容量",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.viscosity' and di1.dic_item_code=p.viscosity) "产品规格"
--distinct category, is_competing 
from wx_T_product p where (category is null or category!='QT') and status=1 --and pack>0 
and p.support_order=1 --and convert(float,p.capacity)>0
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

SELECT CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_promotion_delivery_detail dd1
				LEFT JOIN wx_t_delivery_order_rel r1 ON dd1.id = r1.delivery_detail_id
				LEFT JOIN wx_t_sell_in_promotion_delivery pd1 ON pd1.delivery_id = r1.delivery_id
			WHERE dd.id = dd1.id
				AND pd1.delivery_id < pd.delivery_id
		) THEN 0
		ELSE dd.award_quantity
	END AS point, pd.*
	, (
		SELECT DISTINCT p.name
		FROM wx_t_product p
		WHERE p.sku = pd.product_sku
	) AS product_name
FROM wx_t_promotion_delivery_detail dd
	LEFT JOIN wx_t_delivery_order_rel r ON dd.id = r.delivery_detail_id
	LEFT JOIN wx_t_sell_in_promotion_delivery pd ON pd.delivery_id = r.delivery_id
WHERE 1 = 1
	AND dd.id = '502'
	
select capacity,pack,* 
--update p set p.sku=capacity
from wx_t_product p where --sku like '%LMB' and 
convert(float,capacity)=pack and status=1

select * from wx_t_oem_product_packaging p left join wx_t_oem_product_packaging_code pc on p.id=pc.packaging_id where p.product_id='560888ICE' order by p.creation_time

select p.name,* 
--update l set l.product_count=4
from wx_t_out_stock_line l left join wx_t_product p on p.sku=l.sku where --product_count=4 and 
p.name like '%6X4%' --and p.

select osl.* from wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_line osl ON os.stock_out_no = osl.stock_out_no
			where YEAR(wto.create_time)>=2021 and wto.work_shop_id=143753 and month(wto.create_time)=6 and osl.sku='503048LPB'
select osl.* from wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_product osl ON os.stock_out_no = osl.stock_out_no
			where YEAR(wto.create_time)>=2021 and wto.work_shop_id=143753 and month(wto.create_time)=6 and osl.sku='503048LPB'
			
select a1.liters, a2.liters,* from 
(SELECT sum(osp.actual_out_count * CONVERT(FLOAT, tp.capacity)) AS liters
				   , wto.work_shop_id
				   , tp.sku
				   , MONTH(wto.create_time) [month]
			FROM wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
			LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
			LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
			JOIN PP_MID.dbo.bi_product bp ON bp.product_code_SAP = tp.sku
			WHERE wto.status IN (10, 11)
		    AND ws.delete_flag = 0
			AND bp.product_channel = 'Consumer'
			AND YEAR(wto.create_time)>=2021
			group by wto.work_shop_id
				   , tp.sku
				   , MONTH(wto.create_time)) a2 left join 
(
SELECT sum(osl.product_count * CONVERT(FLOAT, tp.capacity)) AS liters
				   , tp.sku
				   ,ws.id
				   , MONTH(wto.create_time) [month]
			FROM wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_line osl ON os.stock_out_no = osl.stock_out_no
			LEFT JOIN wx_t_product tp ON osl.sku = tp.sku
			LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
			JOIN PP_MID.dbo.bi_product bp ON bp.product_code_SAP = tp.sku
			WHERE wto.status IN (10, 11,13)
		    AND ws.delete_flag = 0
			AND bp.tier_category = 'Premium'
			AND bp.product_channel = 'Consumer'
			AND YEAR(wto.create_time)>=2021 
			group by ws.id,tp.sku,MONTH(wto.create_time) ) a1 on a1.sku=a2.sku and a1.id=a2.work_shop_id and a1.month=a2.month
			where a2.liters>a1.liters
			order by a1.month			
			
select top 100 p.name,i.product_key, round(p.pack/convert(float,p.capacity),0),l.* 
--update l set l.ext_property5=l.modify_quantity*round(p.pack/convert(float,p.capacity),0)-l.modify_quantity,l.log_desc=REPLACE(l.log_desc,'【' + convert(varchar(20),l.modify_quantity) + '】','【' + convert(varchar(20),l.modify_quantity*round(p.pack/convert(float,p.capacity),0)) + '】')
from wx_t_inventory_log2021 l left join wx_t_inventory2021 i on i.id=l.inventory_id
left join wx_t_product p on p.sku=i.product_key
where l.business_type='INSTOCK' and round(p.pack/convert(float,p.capacity),0)>1
and l.delete_flag=0
 order by l.id desc;

 update l set l.modify_quantity=l.modify_quantity*round(p.pack/convert(float,p.capacity),0)
from wx_t_inventory_log2021 l left join wx_t_inventory2021 i on i.id=l.inventory_id
left join wx_t_product p on p.sku=i.product_key
where l.business_type='INSTOCK' and round(p.pack/convert(float,p.capacity),0)>1
and l.delete_flag=0

select i.current_quantity, isnull((select sum(convert(int, l.ext_property5)) from wx_t_inventory_log2021 l where l.inventory_id=i.id and l.delete_flag=0),0), 
p.name,i.product_key, round(p.pack/convert(float,p.capacity),0),i.* 
--update i set i.current_quantity=i.current_quantity + isnull((select sum(convert(int, l.ext_property5)) from wx_t_inventory_log2021 l where l.inventory_id=i.id),0)
from wx_t_inventory2021 i
left join wx_t_product p on p.sku=i.product_key
 where i.inventory_type='distributor'
and round(p.pack/convert(float,p.capacity),0)>1

select l1.original_quantity+l1.modify_quantity,i1.current_quantity, * 
--update l1 set original_quantity=i1.current_quantity-l1.modify_quantity
from wx_t_inventory_log2021 l1 left join wx_t_inventory2021 i1 on i1.id=l1.inventory_id
where i1.inventory_type='distributor' and l1.delete_flag=0 and not exists (select 1 from wx_t_inventory_log2021 l2 
where  l2.inventory_id=l1.inventory_id and l2.create_time>l1.create_time)
and l1.original_quantity+l1.modify_quantity!=i1.current_quantity


select a1.liters, a2.liters,o.organization_name,* from 
(SELECT sum(osp.actual_out_count * CONVERT(FLOAT, tp.capacity)) AS liters
				   , wto.work_shop_id
				   , tp.sku
				   , MONTH(wto.create_time) [month]
			FROM wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
			LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
			LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
			JOIN PP_MID.dbo.bi_product bp ON bp.product_code_SAP = tp.sku
			WHERE wto.status IN (10, 11)
		    AND ws.delete_flag = 0
			AND bp.product_channel = 'Consumer'
			AND YEAR(wto.create_time)>=2021
			group by wto.work_shop_id
				   , tp.sku
				   , MONTH(wto.create_time)) a2 left join 
(
SELECT sum(osl.product_count * CONVERT(FLOAT, tp.capacity)) AS liters
				   , tp.sku
				   ,ws.id
				   , MONTH(wto.create_time) [month]
			FROM wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_line osl ON os.stock_out_no = osl.stock_out_no
			LEFT JOIN wx_t_product tp ON osl.sku = tp.sku
			LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
			JOIN PP_MID.dbo.bi_product bp ON bp.product_code_SAP = tp.sku
			WHERE wto.status IN (10, 11,13)
		    AND ws.delete_flag = 0
			AND bp.tier_category = 'Premium'
			AND bp.product_channel = 'Consumer'
			AND YEAR(wto.create_time)>=2021 
			group by ws.id,tp.sku,MONTH(wto.create_time) ) a1 on a1.sku=a2.sku and a1.id=a2.work_shop_id and a1.month=a2.month
	left join wx_t_workshop_partner wp on wp.workshop_id=a1.id
	left join wx_t_organization o on o.id=wp.partner_id
			where a2.liters>a1.liters
			order by a1.month	
			
select tp.name, tp.capacity,osp.* 
--update osp set osp.product_count=4+4
from wx_t_order wto
			JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
			JOIN wx_t_out_stock_line osp ON os.stock_out_no = osp.stock_out_no
			LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
			where YEAR(wto.create_time)>=2021 and wto.work_shop_id=182123 and osp.sku='500186LPK' and month(wto.create_time)=4
			and osp.id=323330
			
			
select * 
--update p set p.capacity='0.1'
from wx_t_product p where p.sku='510731OZL'


select pack, box_capacity, capacity,* from wx_t_product p where convert(float,p.capacity)>0 and (p.pack/convert(float,p.capacity)-round(p.pack/convert(float,p.capacity),0))!=0

select * 
--update p set p.ext_flag=p.ext_flag|8
from wx_t_product p where p.sku in (select d.dic_item_code from wx_t_dic_item d where d.dic_type_code='sap.bundle.sku.config')

select p.name,os.stock_from_orgname, w.work_shop_name, os.scan_person_name,os.out_time,od.order_no,* from wx_t_out_stock_line osl left join wx_t_out_stock os on os.stock_out_no=osl.stock_out_no 
left join wx_t_product p on p.sku=osl.sku
left join wx_t_order od on od.order_no=os.order_no
left join wx_t_work_shop w on w.id=od.work_shop_id
where --os.order_no='05211210011250' and 
osl.code in ('102945304659','102945304730','102945304679','102945304687')


select top 100 * from wx_t_out_stock_line osl
left JOIN wx_t_out_stock os  ON os.stock_out_no = osl.stock_out_no
left JOIN wx_t_order wto ON wto.order_no = os.order_no AND os.order_type = 'workshop' order by osl.id desc;

select code1, count(1) --into wx_t_oem_product_packaging_code20211231 
from wx_t_oem_product_packaging_code pcn 
where code1<>'000000000000'
group by code1 
having count(1)>1

--select 
delete a --into [wx_t_qbr_cdm_performance_adjust20211231] 
from [wx_t_qbr_cdm_performance_adjust] a left join wx_t_qbr_cdm_per_adj_log al on a.id=al.adjust_id
where a.quarter=3  and a.year=2021