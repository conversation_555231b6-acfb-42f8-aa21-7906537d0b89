package com.chevron.pms.service;

import java.util.Map;

import com.common.util.JsonResponse;

public interface MechanicQrcodeService {
	/**
	 * 保存技师扫码记录
	 * @param mechanicCode
	 * @param qrCode
	 * @return
	 */
	public Map<String, Object> saveMechanicQrcode(String mechanicCode, String qrCode);
	
	/**
	 * 查询物流码对应扫码记录
	 * @param qrCode
	 * @return
	 */
	public Map<String, Object> getMechanicQrcodeByTraceNo(String traceNo);
	
	/**
	 * 获取关注企业号二维码Url
	 * @param spId 合伙人ID
	 * @return
	 */
	public Map<String, Object> getEnterpriseQrcodeUrl(String spId);
	
	public JsonResponse saveB2BMechanicQrcode(String userId, String qrcode, String salesChannel);
}
