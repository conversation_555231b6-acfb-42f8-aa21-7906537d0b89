create FUNCTION [dbo].[fun_get_nonlocal_quarter_punish_desc] 
(   
@punish_value nvarchar(4000),
@punish_year int
)  
RETURNS nvarchar(4000)  
AS  
BEGIN  
	DECLARE @start_idx int
	DECLARE @idx int
	DECLARE @desc nvarchar(4000) 
	DECLARE @quarter nvarchar(10) 
	SET @start_idx=1 
	SET @desc=''
	SET @idx=CHARINDEX(',', @punish_value, @start_idx)
	WHILE @idx>@start_idx
	BEGIN
		SET @desc=@desc + '扣除' + convert(varchar(20), @punish_year) + '年'
		SET @quarter=SUBSTRING(@punish_value, @start_idx, @idx - @start_idx)
		IF @quarter='1'
			SET @desc=@desc + '第一'
		ELSE IF @quarter='2'
			SET @desc=@desc + '第二'
		ELSE IF @quarter='3'
			SET @desc=@desc + '第三'
		ELSE IF @quarter='4'
			SET @desc=@desc + '第四'
		
		SET @desc=@desc + '季度的季度奖励的'
		SET @start_idx = @idx + 1
		SET @idx=CHARINDEX(';', @punish_value, @start_idx)
		IF @idx < 1
			SET @idx = 4000
		SET @desc=@desc + SUBSTRING(@punish_value, @start_idx, @idx - @start_idx) + '%；'
		SET @start_idx = @idx + 1
		SET @idx=CHARINDEX(',', @punish_value, @start_idx)
	END 
   RETURN @desc  
END  

--select dbo.[fun_get_nonlocal_quarter_punish_desc]('1,5;2,10;', 2022)