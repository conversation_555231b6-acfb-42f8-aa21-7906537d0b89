package com.sys.log.util;

import java.lang.reflect.Method;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingDeque;

import org.apache.log4j.Logger;

import com.common.util.DateUtils;
import com.common.util.SpringUtils;
import com.sys.log.business.LogBizService;
import com.sys.log.model.Log;

public class LogUtils {
	private static LogUtils instance = new LogUtils();
	
	private BlockingQueue<LogTask> taskQueue = new LinkedBlockingDeque<LogTask>();
	
	private LogBizService logBizService = SpringUtils.getBean("logBizServiceImpl"); 
	
	private static Logger logger = Logger.getLogger(LogUtils.class);

	private LogUtils(){
		new Thread(){
			@Override
			public void run() {
				do {
					while(!taskQueue.isEmpty()){
						//取出最先插入日志任务
						LogTask task = taskQueue.poll();
						try {
							task.execute();
						} catch (Exception e) {
							logger.error("执行插入日志失败。" + e.getMessage(), e);
						}
					}
					synchronized (instance) {
						if(taskQueue.isEmpty()){
							try {
								instance.wait();
							} catch (InterruptedException e) {
								logger.error("记录日志线程休眠失败", e);
							}
						}
					}
				} while (true);
			}
		}.start();
	}
	
	/**
	 * 添加日志任务
	 * @param logTask
	 */
	public static void addLog(LogTask logTask){
		synchronized (instance) {
			instance.taskQueue.add(logTask);
			instance.notifyAll();
		}
	}
	
	/**
	 * 添加系统错误日志
	 * @param operator 操作员
	 * @param method 出错方法
	 * @param errorMsg 错误信息
	 * @param params 方法参数
	 */
	public static void addErrorLog(Long operator, String method, String errorMsg, String params){
		final Log errorLog = new Log();
		errorLog.setCreateTime(DateUtils.getCurrentDate());
		errorLog.setOperator(operator);
		errorLog.setLogType("Log.error");
		errorLog.setExtProperty1(errorMsg);
		errorLog.setExtProperty2(method);
		errorLog.setExtProperty3(params);
		addLog(new LogTask() {
			
			@Override
			public void execute() throws Exception {
				instance.logBizService.insert(errorLog);
			}
		});
	}
	
	/**
	 * 添加INFO级系统日志
	 * @param operator 操作员
	 * @param method 调用方法名
	 * @param infoMsg INFO信息
	 */
	public static void addInfoLog(Long operator, String method, String infoMsg){
		final Log errorLog = new Log();
		errorLog.setCreateTime(DateUtils.getCurrentDate());
		errorLog.setOperator(operator);
		errorLog.setLogType("Log.info");
		errorLog.setExtProperty1(infoMsg);
		errorLog.setExtProperty2(method);
		addLog(new LogTask() {
			
			@Override
			public void execute() throws Exception {
				instance.logBizService.insert(errorLog);
			}
		});
	}
	
	/**
	 * 添加日志
	 * @param operator 操作员
	 * @param method 调用方法
	 * @param logType 日志类型 error info warning等
	 * @param message 日志消息
	 * @param extMsgs 扩展消息
	 */
	public static void addLog(Long operator, String method, String logType, String message, String... extMsgs){
		final Log errorLog = new Log();
		errorLog.setCreateTime(DateUtils.getCurrentDate());
		errorLog.setOperator(operator);
		errorLog.setLogType("Log." + logType);
		errorLog.setExtProperty1(message);
		errorLog.setExtProperty2(method);
		if(extMsgs != null){
			int i = 3;
			if(extMsgs.length > 10){
				throw new RuntimeException("LogUtils.addLog方法最多支持10个扩展消息");
			}
			for(String msg : extMsgs){
				try {
					Method m = Log.class.getMethod("setExtProperty" + i++, String.class);
					m.invoke(errorLog, msg);
				} catch (Exception e) {
					logger.error("addLog fail. " + e.getMessage(), e);
				}
			}
		}
		addLog(new LogTask() {
			
			@Override
			public void execute() throws Exception {
				instance.logBizService.insert(errorLog);
			}
		});
	}
}
