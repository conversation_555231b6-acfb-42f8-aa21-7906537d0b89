package com.sys.auth.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

import com.sys.auth.service.*;
import net.sf.json.JSONArray;

import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springside.modules.security.utils.Cryptos;
import org.springside.modules.utils.Encodes;

import com.chevron.forecasting.business.ForecastCacheUtil;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopEmployeeExample;
import com.chevron.promote.model.CustomerRegionUser;
import com.chevron.promote.model.PromoteRoleEnum;
import com.chevron.thirdorder.model.MessageVo;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.exception.WxPltException;
import com.common.exception.auth.WxAuthException;
import com.common.token.util.TokenUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.EmailSendUtils;
import com.common.util.JsonResponse;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.SMSUtil;
import com.common.util.SaltUtil;
import com.common.util.StringUtils;
import com.redis.tockenMgnt.service.TokenMgntService;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.business.UserBizService;
import com.sys.auth.dao.WxTLoginErrorLogMapper;
import com.sys.auth.dao.WxTMenuMapper;
import com.sys.auth.dao.WxTOrgMapper;
import com.sys.auth.dao.WxTRoleMapper;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.dao.WxTUserbaseMapper;
import com.sys.auth.dao.WxTUserroleMapper;
import com.sys.auth.model.MenuItem;
import com.sys.auth.model.WxRoleMenu;
import com.sys.auth.model.WxSimpleUser;
import com.sys.auth.model.WxTLoginErrorLog;
import com.sys.auth.model.WxTMenu;
import com.sys.auth.model.WxTOrg;
import com.sys.auth.model.WxTOrgExample;
import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTRolesource;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserExample;
import com.sys.auth.model.WxTUserbase;
import com.sys.auth.model.WxTUserrole;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVo;
import com.sys.permission.business.PermissionCache;

@Service
public class UserService implements UserServiceI {

	/**
	 * 用户类型: 1-表示门店用户，orgid保存门店ID
	 */
	private static final String USER_TYPE_WS = "1";
	/**
	 * 资源类型, 2-菜单资源
	 */
	private static final int RESOURCE_TYPE_MENU = 2;
	
	private static final String PASSWORD_ERROR_WARNING_MESSAGE = "剩余尝试次数:";
	
	private static final String PASSWORD_ERROR_LOCKED_MESSAGE = "账号已锁定。点击“忘记密码？”重置后可解锁";

	private static Logger log = LoggerFactory.getLogger(UserService.class);

	@Resource
	WxTUserMapper wxTUserMapper;
	@Resource
	WxTUserbaseMapper wxTUserbaseMapper;
	@Resource
	WxTOrgMapper wxTOrgMapper;
	@Resource
	WxTMenuMapper wxTMenuMapper;
	@Resource
	public WxTRoleMapper wxTRoleMapper;
	@Resource
	public WxTUserroleMapper wxTUserroleMapper;
	@Resource
	public UserRoleService userRoleService;
	@Resource
	private OrganizationVoMapper organizationVoMapper;
	@Resource
	private WxUserRoleService wxUserRoleServiceImpl;
	@Resource
	WxRoleServiceI wxRoleServiceImpl;
	@Resource
	WorkshopEmployeeMapper workshopEmployeeMapper;
	@Resource
	public WxTParterRolePowerService wxTParterRolePowerService;
	@Resource
	private TokenMgntService tokenMgntServicelmpl;
	@Autowired
	DicService dicService;
	@Autowired
	private ForecastCacheUtil forecastCacheUtil;
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	@Autowired
	private WxTLoginErrorLogMapper wxTLoginErrorLogMapper;
	@Autowired
	private UserBizService userBizService;
	@Autowired
	protected WxLogServiceI wxLogService;
	@Autowired
	private WxPermissionService wxPermissionService;
	
	private static Set<String> appAllowRoles = new HashSet<String>();
	
	static {
		appAllowRoles.add(Constants.SERVICE_PARTNER_ADMIN_CODE);
		appAllowRoles.add(Constants.SERVICE_PARTNER_MANAGER_CODE);
		appAllowRoles.add(Constants.SERVICE_PARTNER_BD_CODE);
		appAllowRoles.add("Caltex_Dealer");
		appAllowRoles.add("Caltex_BD");
		appAllowRoles.add("Industrial_DSR");
		appAllowRoles.add(Constants.ZWY_ADMIN_CODE);
	}

	/**
	 * 根据用户登录名 查询用户数据
	 *
	 * @param loginName
	 * @return WxTUser
	 */
	@Override
	public WxTUser findUserByLoginName(String loginName) {
		WxTUser user = null;
		try {
			user = wxTUserMapper.findUserByLoginName(loginName);
		} catch (Exception ex) {
			log.error("findUserByLoginName exception", ex);
		}
		return user;
	}

	/**
	 * 登录方法(Web Page端)，当用户不存在返回空 当密码错误 将user对象userId置空
	 * @param loginName
	 * @param password
	 * @return WxTUser
	 * @throws Exception
	 */
	public WxTUser userLogin(String loginName, String password) {
		WxTUser tUser = null;
		try {
			tUser = userLogin4App(loginName, password, false);
		} catch (WxAuthException e) {
			if(e.getExpCode() != null && e.getExpCode().startsWith(WxAuthException.PWD_WARNING_PREFIX)) {
				//先存session，在com.sys.auth.controller.ConfigIndexPage.loadIndexPage再转到request中
				HttpServletRequest request = ContextUtil.getHttpRequest();
				request.getSession().setAttribute("loginCode", e.getExpCode());
				request.getSession().setAttribute("loginMsg", e.getExpMsg());
				tUser =  findUserByLoginName(loginName);
			}else {
				throw e;
			}
		}
		// 加载用户权限菜单项
		loadMenuInfo(tUser);
		return tUser;
//		WxTUser tUser = findUserByLoginName(loginName);
//		if (tUser != null) {
//			// 验证密码
//			boolean isValidPwd = false;
//			try {
//				isValidPwd = validatePasswd(password, tUser, true);
//			} catch (WxAuthException e) {
//				if(e.getExpCode() != null && e.getExpCode().startsWith(WxAuthException.PWD_WARNING_PREFIX)) {
//					//先存session，在com.sys.auth.controller.ConfigIndexPage.loadIndexPage再转到request中
//					HttpServletRequest request = ContextUtil.getHttpRequest();
//					request.getSession().setAttribute("loginCode", e.getExpCode());
//					request.getSession().setAttribute("loginMsg", e.getExpMsg());
//					isValidPwd = true;
//				}else {
//					throw e;
//				}
//			}
//			if (isValidPwd) {
//				//验证用户是否被锁定
//				if("F".equals(tUser.getAllowLogin())) {
//					tUser.setUserId(null);
//					tUser = null;
//					log.error("user locked");
//					throw WxAuthException.createAuthObj(WxAuthException.USER_LOCKED, null);
//				}
//				// 验证通过
//				tUser = updateUserAndPush2Context(tUser);
//				// 加载用户权限菜单项
//				loadMenuInfo(tUser);
//				//重置密码错误登陆历史
//				wxTLoginErrorLogMapper.resetStatus(tUser.getUserId());
//				// 存入tokenUser
//				String token = ContextUtil.createToken(loginName, password);
//				ContextUtil.putTokenUser(token, tUser);
//			} else {
//				//多次输入密码错误锁定逻辑
//				Integer remainNum = lockUserForPwdError(tUser.getUserId());
//				String message = PASSWORD_ERROR_WARNING_MESSAGE + remainNum;
//				if(remainNum == 0) {
//					message = PASSWORD_ERROR_LOCKED_MESSAGE;
//				}
//				// 密码错误 将userId置空, 抛出异常
//				tUser.setUserId(null);
//				tUser = null;
//				log.error("Password validation failed");
//				throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_PwdExp, message);
//			}
//			
//		} else {
//			// 本地用户不存在, 抛出异常
//			log.error("No local user found.");
//			throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_UserNot, null);
//		}
//		return tUser;
	}
	
	/**
	 * 登录方法(APP端)，当用户不存在返回空 当密码错误 将user对象userId置空
	 * @param loginName
	 * @param password
	 * @return WxTUser
	 * @throws Exception
	 * <AUTHOR>
	 */
	private WxTUser userLogin4App(String loginName, String password, boolean nonePwd) {
		//1. 获取登录用户信息
		WxTUser tUser = findUserByLoginName(loginName);
		if (tUser != null) {

			if(!"T".equals(tUser.getAllowLogin())){
				throw WxAuthException.createAuthObj(WxAuthException.USER_LOCKED, null);
			}
			//2. 验证用户密码
			boolean isValidPwd = nonePwd;
			if(!nonePwd) {
				try {
					//2.1 验证密码，包含密码策略验证
					isValidPwd = validatePasswd(password, tUser, true);
				} catch (WxAuthException e) {
					//2.2 针对PC登录后需要告警的信息放到SESSION，重定向后再转到request中
					if(e.getExpCode() != null && e.getExpCode().startsWith(WxAuthException.PWD_WARNING_PREFIX)) {
						//先存session，在com.sys.auth.controller.ConfigIndexPage.loadIndexPage再转到request中
						HttpServletRequest request = ContextUtil.getHttpRequest();
						request.getSession().setAttribute("loginCode", e.getExpCode());
						request.getSession().setAttribute("loginMsg", e.getExpMsg());
						isValidPwd = true;
					}else {
						throw e;
					}
				}
				
			}
			if (isValidPwd) {
				//3. 密码验证通过
				//3.1 初始化用户登录上下文信息
				tUser = updateUserAndPush2Context(tUser);
				//3.2 重置密码错误登陆历史
				wxTLoginErrorLogMapper.resetStatus(tUser.getUserId());
				//3.3 创建token，用于app或第三方（如PC端调用office接口在线预览world，Excel等）临时访问系统
				final String sid =(String) SecurityUtils.getSubject().getSession().getId();
				String token = ContextUtil.createToken(tUser.getLoginName(), password);
				ContextUtil.putTokenUser(token, tUser);
				/*tokenMgntServicelmpl.setToken2LoginName(token, loginName);*/
				ContextUtil.addUserToLoginList(tUser, sid, token); 
				
				//3.4 .记录登录日志
				final WxTUser user = tUser;
				LogUtils.addLog(new LogTask() {
					
					@Override
					public void execute() throws Exception {
						wxLogService.crearLog("1", sid,  user.getUserId(), user.getTenantId());
					}
				});
			} else {
				//4. 密码错误
				//4.1 多次输入密码错误锁定逻辑
				Integer remainNum = lockUserForPwdError(tUser.getUserId());
				String message = PASSWORD_ERROR_WARNING_MESSAGE + remainNum;
				if(remainNum == 0) {
					message = PASSWORD_ERROR_LOCKED_MESSAGE;
				}
				//4.2 密码错误 将userId置空, 抛出异常
				tUser.setUserId(null);
				tUser = null;
				log.error("Password validation failed");
				throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_PwdExp, message);
			}
		} else {
			// 本地用户不存在, 抛出异常
			log.error("No local user found.");
			throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_UserNot, null);
		}
		return tUser;
	}

	/**
	 * 登录方法，用作后台Token登陆
	 * @param userId
	 * @return WxTUser
	 * <AUTHOR>
	 */
	public WxTUser userLogin4Token(String userId, String tokenType, String token) {
		WxTUser tUser = wxTUserMapper.selUserByUserid(Long.valueOf(userId));
		if (tUser != null) {
			// 验证密码
			boolean isValid = false;
			try {
				isValid = TokenUtil.isValid(userId, tokenType, token);
			} catch (Exception e) {
				log.error("Token validation exception", e);
				throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_PwdExp, null);
			}

			if (isValid) {
				// 验证通过
				tUser = updateUserAndPush2Context(tUser);
				// 加载用户权限菜单项
				loadMenuInfo(tUser);
				ContextUtil.putSSOTokenUser(token, tUser);
			} else {
				// 密码错误 将userId置空, 抛出异常
				tUser.setUserId(null);
				tUser = null;
				log.error("Token validation failed");
				throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_PwdExp, null);
			}
		} else {
			// 本地用户不存在
			log.error("No local user found.");
			throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_UserNot, null);
		}
		return tUser;
	}

	/**
	 * 加载各项用户信息(用户类型, 角色列表, 门店powerFlag, 用户权限菜单项等)
	 * @param user WxTUser
	 * @return WxTUser
	 * <AUTHOR>
	 */
	private WxTUser updateUserAndPush2Context(final WxTUser user) {
		if (null == user) {
			return user;
		}

		// 1. 加载用户类型
		if (USER_TYPE_WS.equals(user.getType())) {
			// 用户类型: 1-表示门店用户，orgid保存门店ID
			user.setUserModel(WxTUser.USER_MODEL_WS);
		} else {
			user.setUserModel(user.getOrgType() == 0 ? WxTUser.USER_MODEL_CHEVRON : WxTUser.USER_MODEL_SP);
//			OrganizationVo organizationVo = organizationVoMapper.selectByPrimaryKey(user.getOrgId());
//			if (OrganizationVo.ORG_PARTNER_TYPE == organizationVo.getType()) {
//				user.setUserModel(WxTUser.USER_MODEL_SP);
//			} else {
//				user.setUserModel(WxTUser.USER_MODEL_CHEVRON);
//			}
		}

		// 2. 加载角色列表
		user.setRoleList(wxRoleServiceImpl.getCurUserRoles(user.getUserId()));
		List<WxTRole> roleList = user.getRoleList();
		// 角色去重
		Set<String> existsRoles = new HashSet<String>();
		if (roleList != null) {
			boolean salesChannelInited = user.getSalesChannel() != null;
			for (WxTRole role : roleList) {
				existsRoles.add(role.getChRoleName());
				//初始化用户包含渠道
				int includeChannel = Constants.getChannelWeight(role.getSalesChannel());
				if(includeChannel > 0) {
					user.setIncludeChannels(user.getIncludeChannels() | includeChannel);
				}
				//初始化用户查看销售权限等级
				if(role.getSalesLevel() != null) {
					user.setSalesLevel(user.getSalesLevel() | role.getSalesLevel());
				}
				//初始化用户标记
				if(role.getUserFlag() != null) {
					user.setUserFlag(user.getUserFlag() | role.getUserFlag());
				}
				// 按角色处理chevron的channel
				if(user.getUserId() != 1 && !salesChannelInited){
					if(StringUtils.isNotBlank(role.getSalesChannel())){
						if(StringUtils.isBlank(user.getSalesChannel())){
							user.setSalesChannel(role.getSalesChannel());
						}else if(!user.getSalesChannel().equals(role.getSalesChannel())){
							user.setSalesChannel(null);
							salesChannelInited = true;
						}
					}
				}
			}
		}
		//缓存销售预测
		forecastCacheUtil.cacheInBackground(user);
		boolean isChevronManager = existsRoles.contains(Constants.CHEVRON_MANAGER_CODE);
		boolean isAdminUser = existsRoles.contains(Constants.ADMIN_CODE);
		boolean isWorkshopManager = existsRoles.contains(Constants.WORKSHOP_MANAGER_CODE);
		boolean isServicePartnerAdmin = existsRoles.contains(Constants.SERVICE_PARTNER_ADMIN_CODE);
		boolean isPartnerDB = existsRoles.contains(Constants.SERVICE_PARTNER_BD_CODE);
		boolean isChevonBD = existsRoles.contains(Constants.CHEVRON_BD_CODE);
		boolean isChevonDiscount = existsRoles.contains(Constants.Chevron_BD_DISCOUNT_CODE);
		boolean isChevonPriceAudit = existsRoles.contains(Constants.CHEVRON_PRICE_AUDIT);
		boolean isChevonSapCsr = existsRoles.contains(Constants.CHEVRON_SAP_CSR);

		// 3.加载用户类型
		if (isChevronManager || isAdminUser || isChevonBD || isChevonPriceAudit || isChevonSapCsr || isChevonDiscount) {
			// 雪佛龙
			user.setmUserTypes(WxTUser.CHEVRON_USER_ROLE);
		} else if (isWorkshopManager) {
			// 店长
			user.setmUserTypes(WxTUser.WORKSHOP_USER_ROLE);
		} else if (isServicePartnerAdmin) {
			// SP Admin角色用户
			user.setmUserTypes(WxTUser.SERVICE_PARTNER_ADMIN_USER_ROLE);
		} else {
			// 普通用户 包括partner
			user.setmUserTypes(WxTUser.OTHER_USER_ROLE);
		}

		// 初始化用户数据权限缓存
		if(user.getUserId() != 1){
			//chevronadmin用户操作不处理权限
			final PermissionCache permissionCache = new PermissionCache();
			user.setPermissionCache(permissionCache);
			permissionCache.init(user.getUserId());
//			new Thread(){
//				public void run() {
//					
//				};
//			}.start();
		}

		// 加载功能权限
		List<Long> ownerIdList = new ArrayList<>();
		ownerIdList.add(user.getUserId());
		if (!CollectionUtils.isEmpty(roleList)) {
			List<Long> roleIdList = roleList.stream().map(x -> x.getRoleId()).distinct().collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(roleIdList)) {
				ownerIdList.addAll(roleIdList);
			}
		}
		List<String> permissionKey = wxPermissionService.selectPermissionKeyByOwnerId(ownerIdList);
		if (!CollectionUtils.isEmpty(permissionKey)) {
			user.setPermissionKeyList(permissionKey);
		}

		// 是否系统管理员角色  add comments by Ervin 2017.7.5
		user.setAdmin(isAdminUser);
		
		user.setSystemIntegrationUser(existsRoles.contains(Constants.ROLE_SYSTEM_INTEGRATION));

		// 雪佛龙管理员
		user.setChevron_Manager(isChevronManager);

		// 历史原因, 以下两个属性皆表示是否合伙人销售角色 add comments by Ervin 2017.7.5
		user.setPartnerDB(isPartnerDB);
		user.setService_Partner_BD(isPartnerDB);

		user.setService_Partner_Manager(existsRoles.contains(Constants.SERVICE_PARTNER_MANAGER_CODE));
		user.setService_Partner_CSR(existsRoles.contains(Constants.SERVICE_PARTNER_CSR_CODE));
		user.setService_Partner_CLERK(existsRoles.contains(Constants.SERVICE_PARTNER_CLERK));

		user.setWorkshop_Manager(isWorkshopManager);
		user.setWorkshop_Mechanic(existsRoles.contains(Constants.WORKSHOP_MECHANIC_CODE));

		// 4. 加载门店powerFlag
		String bdWorkshopPowerTag = wxTParterRolePowerService.getBdWorkshopPower(user.getOrgId());
		user.setBdWorkshopPower(bdWorkshopPowerTag);
		// 5.暂时不把web 登录的User放入appToken By Ervin 2017.7.5
		// ContextUtil.putTokenUser(ContextUtil.APP_TOKEN_KEYWORD, user);
		return user;
	}

	/**
	 * 加载用户权限菜单项, 目前仅PC端页面需要加载, APP端不需要加载
	 * @param user
	 * @throws WxAuthException
	 * <AUTHOR>
	 */
	public void loadMenuInfo(WxTUser user) throws WxAuthException {

		List<WxTMenu> userMenuList = loadUserMenuList(user.getUserId(), user.getTenantId());
		if (userMenuList == null || userMenuList.size() == 0) {
			WxAuthException authExp = WxAuthException.createAuthObj(WxAuthException.role_Exception, null);
			log.error("加载用户权限菜单失败 load menu failed, 该用户对应权限菜单为空 ");
			throw authExp;
		}

		String userMenuJson = convert2UserMenuJson(userMenuList);
		user.setMenuListJson(userMenuJson);
		user.setMenuList(userMenuList);
	}

	/**
	 * 获取权限菜单列表
	 * @param rsType Resource Type 资源类型
	 * @param userId 用户ID
	 * @param tenantId tenant Id
	 * @return List<WxTMenu>
	 * <AUTHOR>
	 */
	private List<WxTMenu> loadUserMenuList(Long userId, Long tenantId) {
		WxTRolesource rs = new WxTRolesource();
		rs.setRsType(RESOURCE_TYPE_MENU);
		rs.setXgUser(userId);
		rs.setTenantId(tenantId);

		List<WxTMenu> wxTMenulist = wxTMenuMapper.selMenuByPermissions(rs);
		return wxTMenulist;
	}

	/**
	 * 创建权限菜单树形结构JSON, 为空则返回null
	 * @param wxTMenulist 权限菜单列表
	 * @return JSON String
	 * <AUTHOR>
	 */
	private String convert2UserMenuJson(List<WxTMenu> wxTMenulist) {

		if (null == wxTMenulist || wxTMenulist.isEmpty()) {
			log.error("There is no wxTMenulist, retur null.");
			return null;
		}

		Map<Long, MenuItem> menuMap = new TreeMap<Long, MenuItem>();
		for (WxTMenu wxTMenu : wxTMenulist) {
			MenuItem menuItem = new MenuItem();
			menuItem.setMenuId(String.valueOf(wxTMenu.getMenuId()));
			menuItem.setTpl("<a href='{href}'><em class='mid_" + wxTMenu.getMenuId() + "'>{text}</em></a>");
			menuItem.setText(wxTMenu.getMenuName());
			menuItem.setHref(wxTMenu.getMenuUrl());
			menuItem.setPid(wxTMenu.getMenuPid());
			menuItem.setSort(wxTMenu.getSort());
			menuMap.put(wxTMenu.getMenuId(), menuItem);
		}

		List<MenuItem> menuItemList = new ArrayList<MenuItem>();
		for (MenuItem menuItem : menuMap.values()) {
			if (menuItem.getPid() == 0) {
				menuItemList.add(menuItem);
			} else {
				MenuItem parentItem = menuMap.get(menuItem.getPid());
				if (parentItem != null) {
					if (parentItem.getItems() != null) {
						parentItem.getItems().add(menuItem);
					} else {
						List<MenuItem> items = new ArrayList<MenuItem>();
						items.add(menuItem);
						parentItem.setItems(items);
					}
				}
			}
		}

		if (menuItemList.isEmpty()) {
			log.error("There is no menu, retur null.");
			return null;
		}

		JSONArray menuListJson = JSONArray.fromObject(menuItemList);
		log.debug("menuListJson:" + menuListJson.toString());
		return menuListJson.toString();
	}

	@Override
	public Map<String, Object> registerSubSystemForUser(String subSystemName) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser tUser = ContextUtil.getCurUser();
		String token = ContextUtil.registerUserForSubSystem(subSystemName, tUser);
		resultMap.put("code", "success");
		resultMap.put("token", token);
		return resultMap;
	}

	/**
	 * 验证并获取用户信息服务, 供移动端调用接口<br/>
	 * @param loginName 登录名
	 * @param password 密码
	 * @return Map 返回数据JSON
	 * @throws Exception 异常
	 */
	@Override
	public Map<String, Object> validateUser(String loginName, String password) throws Exception {
		WxTUser tUser = null;
		Map<String, Object> resMap = new HashMap<String, Object>();

		try {
			tUser = userLogin4App(loginName, password, false);

			if (tUser == null) {
				resMap.put("code", WxAuthException.Local_Exp_UserNot);
				resMap.put("codeMsg", WxAuthException.Local_Exp_UserNot_msg);
				resMap.put("user", null);
				resMap.put("msgVo", new MessageVo<Object>(MessageContants.ACCOUNT_ERROR_CODE, MessageContants.ACCOUNT_ERROR_MSG));
				return resMap;
			}
			if (tUser.getUserId() == null) {// 密码正确，如果不正确 userId为空
				resMap.put("code", WxAuthException.Local_Exp_PwdExp);
				resMap.put("codeMsg", WxAuthException.Local_Exp_PwdExp_msg);
				resMap.put("user", null);
				resMap.put("msgVo", new MessageVo<Object>(MessageContants.ACCOUNT_PASSWD_ERROR_CODE,
						MessageContants.ACCOUNT_PASSWD_ERROR_MSG));
				return resMap;
			}
			// 创建token
			tUser.setPassword("");
			tUser.setSalt("");
			boolean isSpAdmin = false;
			for (WxTRole role : tUser.getRoleList()) {
				if (Constants.SERVICE_PARTNER_ADMIN_CODE.equals(role.getChRoleName())
						|| Constants.SERVICE_PARTNER_MANAGER_CODE.equals(role.getChRoleName())) {
					isSpAdmin = true;
				}
				if (Constants.SERVICE_PARTNER_BD_CODE.equals(role.getChRoleName())) {
					tUser.setPartnerDB(true);
				}
			}

//			// 存入tokenUser
//			String token = ContextUtil.createToken(loginName, password);
//			ContextUtil.putTokenUser(token, tUser);
//			/*tokenMgntServicelmpl.setToken2LoginName(token, loginName);*/
//			ContextUtil.addUserToLoginList(tUser, null, token);

			// add by bo.liu 0525 获取partner区域信息省份过滤
			// if("1".equals(tUser.getType())){
			// tUser.setUserModel(WxTUser.USER_MODEL_WS);
			// }else{
			// OrganizationVo organizationVo =
			// organizationVoMapper.selectByPrimaryKey(tUser.getOrgId());
			// if(OrganizationVo.ORG_PARTNER_TYPE == organizationVo.getType()){
			// tUser.setUserModel(WxTUser.USER_MODEL_SP);
			// }else{
			// tUser.setUserModel(WxTUser.USER_MODEL_CHEVRON);
			// }
			// }
			// add by bo.liu 0525

			resMap.put("code", "success");
			resMap.put("token", tUser.getToken());
			resMap.put("user", tUser);
			resMap.put("isSpAdmin", isSpAdmin);
			// add by bo.liu 1017 供大地保险返回接口使用
			resMap.put("msgVo", new MessageVo<Object>(MessageContants.SUCCESS_CODE, MessageContants.SUCCESS_MSG));
		} catch (Exception ex) {
			log.error("validate user exception", ex);
			resMap.put("user", null);
			if (ex instanceof WxAuthException) {
				WxAuthException authExp = (WxAuthException) ex;
				if (WxAuthException.LDAP_Exception.equals(authExp.getExpCode())) {
					// ldap异常
					resMap.put("code", WxAuthException.LDAP_Exception);
					resMap.put("codeMsg", WxAuthException.LDAP_Exception_msg);

					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(MessageContants.LDAP_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.LDAP_ERROR_MSG);
					resMap.put("msgVo", msgVo);
				} else if (WxAuthException.Local_Exp_PwdExp.equals(authExp.getExpCode())) {
					// 本地密码错误
					resMap.put("code", WxAuthException.Local_Exp_PwdExp);
					resMap.put("codeMsg", WxAuthException.Local_Exp_PwdExp_msg);
					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(MessageContants.ACCOUNT_PASSWD_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.ACCOUNT_PASSWD_ERROR_MSG);
					resMap.put("msgVo", msgVo);

				} else if (WxAuthException.Local_Exp_UserNot.equals(authExp.getExpCode())) {
					// 本地用户不存在
					resMap.put("code", WxAuthException.Local_Exp_UserNot);
					resMap.put("codeMsg", WxAuthException.Local_Exp_UserNot_msg);
					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(MessageContants.ACCOUNT_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.ACCOUNT_ERROR_MSG);
					resMap.put("msgVo", msgVo);
				}
			} else {
				// 处理系统错误
				resMap.put("code", WxAuthException.System_Exception);
				resMap.put("codeMsg", WxAuthException.System_Exception_msg);
				MessageVo<Object> msgVo = new MessageVo<Object>();
				msgVo.setCode(MessageContants.EXCEPTION_CODE);
				msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG);
				resMap.put("msgVo", msgVo);

			}
		}
		return resMap;
	}

	/**
	 * 验证用户(新), AAP端用作登录验证和获取用信息服务接口
	 * <AUTHOR> 2017-2-16 上午10:44:42
	 * @param loginName 登录名
	 * @param password 密码
	 * @param appType 取值1,2,3 1代表partner app 2代表扫码枪app 3代表技师核销app 2和3针对android的版本
	 * @return Map
	 * e.g.: {<br/>
	 * "code": "success",<br/>
	 * "token": "e07b54b3c4ab182312bbb6ea0e7dd80f3684f556",<br/>
	 * "user": {},<br/>
	 * "userMenuList": []<br/>
	 * }<br/>
	 * @throws Exception
	 */
	@Override
	public Map<String, Object> validateUserNew(String loginName, String password, String appType) throws Exception {
		WxTUser tUser = null;
		Map<String, Object> resMap = new HashMap<String, Object>();

		try {
			tUser = userLogin4App(loginName, password, false);

			if (tUser == null) {
				resMap.put("code", WxAuthException.Local_Exp_UserNot);
				resMap.put("codeMsg", WxAuthException.Local_Exp_UserNot_msg);
				resMap.put("user", null);
				resMap.put("msgVo",
						new MessageVo<Object>(MessageContants.ACCOUNT_ERROR_CODE, MessageContants.ACCOUNT_ERROR_MSG));
				return resMap;
			}
			if (tUser.getUserId() == null) {// 密码正确，如果不正确 userId为空
				resMap.put("code", WxAuthException.Local_Exp_PwdExp);
				resMap.put("codeMsg", WxAuthException.Local_Exp_PwdExp_msg);
				resMap.put("user", null);
				resMap.put("msgVo", new MessageVo<Object>(MessageContants.ACCOUNT_PASSWD_ERROR_CODE,
						MessageContants.ACCOUNT_PASSWD_ERROR_MSG));
				return resMap;
			}
			if (USER_TYPE_WS.equals(tUser.getType())) {
				//门店账号要在技师表中
				Map<String, Object> reqMap = new HashMap<String, Object>();
				reqMap.put("mobile", tUser.getMobileTel());
				WorkshopEmployee workshopEmployee = workshopEmployeeMapper.selectByMapParams(reqMap);
				if (null == workshopEmployee) {
					resMap.put("code", WxAuthException.USER_IS_NOT_WORKSHOPEMPLOYEE);
					resMap.put("codeMsg", WxAuthException.USER_IS_NOT_WORKSHOPEMPLOYEE_MSG);
					resMap.put("user", null);
					resMap.put("msgVo", new MessageVo<Object>(MessageContants.USER_ISNOT_MATCHUSER_ERROR_CODE,
							MessageContants.USER_ISNOT_MATCHUSER_ERROR_MSG));
					return resMap;
				}else {
					tUser.setAppRight(true);
					resMap.put("workshopEmployee", workshopEmployee);
				}

			} /*else if (Constants.SCANCODE_APP_TYPE.equals(appType)) {
				// 扫码枪app登录
				if (null != workshopEmployee) {
					resMap.put("code", WxAuthException.role_Exception);
					resMap.put("codeMsg", WxAuthException.role_Exception_msg);
					resMap.put("user", tUser);
					resMap.put("msgVo", new MessageVo<Object>(MessageContants.ROLE_ISNOT_MATCH_ERROR_CODE,
							MessageContants.ROLE_ISNOT_MATCH_ERROR_MSG));
					return resMap;
				}
			}

			// 合伙人登录，或技师登录都给予weChatCode
			if (null != workshopEmployee) {
				tUser.setWechatCode(workshopEmployee.getCode());
			}*/

			tUser.setPassword("");
			tUser.setSalt("");
			boolean isSpAdmin = false;
			int appPermissionWeight = operationPermissionBizService.getPermissionWeight(tUser.getUserId(), "App");
			for (WxTRole role : tUser.getRoleList()) {
				if (Constants.SERVICE_PARTNER_ADMIN_CODE.equals(role.getChRoleName())
						|| Constants.SERVICE_PARTNER_MANAGER_CODE.equals(role.getChRoleName())) {
					isSpAdmin = true;
				}
				if (Constants.SERVICE_PARTNER_BD_CODE.equals(role.getChRoleName())) {
					tUser.setPartnerDB(true);
				}

				// add by bo.liu 2017.02.16 start
				if (Constants.WORKSHOP_MECHANIC_CODE.equals(role.getChRoleName())
						|| Constants.WORKSHOP_MANAGER_CODE.equals(role.getChRoleName())) {
					tUser.setWorkshop_Mechanic(true);
				}

				if (Constants.ZWY_ADMIN_CODE.equals(role.getChRoleName())) {
					tUser.setZWY_Admin(true);
				}

				if (Constants.SERVICE_PARTNER_CLERK.equals(role.getChRoleName())) {
					tUser.setService_Partner_CLERK(true);
				}
				if(!tUser.isAppRight() && (appAllowRoles.contains(role.getChRoleName()) || appPermissionWeight > 0)) {
					tUser.setAppRight(true);
				}

				// end
			}

			if (Constants.MECHANIC_APP_TYPE.equals(appType)) {
				// 技师
				if (!tUser.isWorkshop_Mechanic()) {
					resMap.put("code", WxAuthException.role_Exception);
					resMap.put("codeMsg", WxAuthException.role_Exception_msg);
					resMap.put("user", tUser);
					resMap.put("msgVo", new MessageVo<Object>(MessageContants.ROLE_ISNOT_MATCH_ERROR_CODE,
							MessageContants.ROLE_ISNOT_MATCH_ERROR_MSG));
					return resMap;
				}
			} else if (Constants.SCANCODE_APP_TYPE.equals(appType)) {
				// 扫码枪,针对合伙人出入库远/中外运管理员
				if (!(tUser.isZWY_Admin() || tUser.isService_Partner_CLERK())) {
					resMap.put("code", WxAuthException.role_Exception);
					resMap.put("codeMsg", WxAuthException.role_Exception_msg);
					resMap.put("user", tUser);
					resMap.put("msgVo", new MessageVo<Object>(MessageContants.ROLE_ISNOT_MATCH_ERROR_CODE,
							MessageContants.ROLE_ISNOT_MATCH_ERROR_MSG));
					return resMap;
				}
			} else if(!tUser.isAppRight()) {
				// 合伙人app
				resMap.put("code", WxAuthException.role_Exception);
				resMap.put("codeMsg", WxAuthException.role_Exception_msg);
				resMap.put("user", tUser);
				resMap.put("msgVo", new MessageVo<Object>(MessageContants.ROLE_ISNOT_MATCH_ERROR_CODE,
						MessageContants.ROLE_ISNOT_MATCH_ERROR_MSG));
				return resMap;
			}

//			// 创建token存入app tokenUser map
//			String token = ContextUtil.createToken(loginName, password);
//			ContextUtil.putTokenUser(token, tUser);
//			/*tokenMgntServicelmpl.setToken2LoginName(token, loginName);*/
//			ContextUtil.addUserToLoginList(tUser, null, token);

			// add by bo.liu 0525 获取partner区域信息省份过滤
			if (USER_TYPE_WS.equals(tUser.getType())) {
				tUser.setUserModel(WxTUser.USER_MODEL_WS);
			} else {
				tUser.setUserModel(tUser.getOrgType() == 0 ? WxTUser.USER_MODEL_CHEVRON : WxTUser.USER_MODEL_SP);
//				OrganizationVo organizationVo = organizationVoMapper.selectByPrimaryKey(tUser.getOrgId());
//				if (OrganizationVo.ORG_PARTNER_TYPE == organizationVo.getType()) {
//					tUser.setUserModel(WxTUser.USER_MODEL_SP);
//				} else {
//					tUser.setUserModel(WxTUser.USER_MODEL_CHEVRON);
//				}
			}
			// add by bo.liu 0525

			resMap.put("code", "success");
			resMap.put("token", tUser.getToken());
			resMap.put("user", tUser);
			resMap.put("isSpAdmin", isSpAdmin);
			resMap.put("appPermissionWeight", appPermissionWeight);
			// add by bo.liu 1017 供大地保险返回接口使用
			resMap.put("msgVo", new MessageVo<Object>(MessageContants.SUCCESS_CODE, MessageContants.SUCCESS_MSG));
			//返回告警信息
			HttpServletRequest request = ContextUtil.getHttpRequest();
			resMap.put("warningCode", request.getSession().getAttribute("loginCode"));
			resMap.put("warningMsg", request.getSession().getAttribute("loginMsg"));
			//该用户是否KA
			resMap.put("isKa",wxTUserMapper.countKaByOrgId(tUser.getOrgId())>0);
		} catch (Exception ex) {
			log.error("validate user exception", ex);
			resMap.put("user", null);
			if (ex instanceof WxAuthException) {
				WxAuthException authExp = (WxAuthException) ex;
				if (WxAuthException.LDAP_Exception.equals(authExp.getExpCode())) {
					// ldap异常
					resMap.put("code", WxAuthException.LDAP_Exception);
					resMap.put("codeMsg", WxAuthException.LDAP_Exception_msg);

					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(MessageContants.LDAP_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.LDAP_ERROR_MSG);
					resMap.put("msgVo", msgVo);
				} else if (WxAuthException.Local_Exp_PwdExp.equals(authExp.getExpCode())) {
					// 本地密码错误
					resMap.put("code", WxAuthException.Local_Exp_PwdExp);
					resMap.put("codeMsg", authExp.getExpMsg());
					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(MessageContants.ACCOUNT_PASSWD_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.ACCOUNT_PASSWD_ERROR_MSG);
					resMap.put("msgVo", msgVo);

				} else if (WxAuthException.Local_Exp_UserNot.equals(authExp.getExpCode())) {
					// 本地用户不存在
					resMap.put("code", WxAuthException.Local_Exp_UserNot);
					resMap.put("codeMsg", WxAuthException.Local_Exp_UserNot_msg);
					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(MessageContants.ACCOUNT_ERROR_CODE);
					msgVo.setCodeMsg(MessageContants.ACCOUNT_ERROR_MSG);
					resMap.put("msgVo", msgVo);
				} else {
					// 处理系统错误
					resMap.put("code", authExp.getExpCode());
					resMap.put("codeMsg", authExp.getExpMsg());
					resMap.put("attr1", authExp.getAttr1());
					MessageVo<Object> msgVo = new MessageVo<Object>();
					msgVo.setCode(authExp.getExpCode());
					if(WxAuthException.PWD_EXPIRED.equals(authExp.getExpCode())) {
						msgVo.setCodeMsg("密码已过期，不可登录，请重置密码");
					}else if(WxAuthException.PWD_ILLEGAL_FIRST.equals(authExp.getExpCode())) {
						msgVo.setCodeMsg(new StringBuilder("根据雪佛龙全球IT安全政策要求，合伙人平台已升级密码策略。您的密码不符合当前系统要求，已于")
								.append(authExp.getAttr1()).append(" 23:59自动失效，不可登录，请重置密码").toString());
					}else {
						msgVo.setCodeMsg(authExp.getExpMsg());
					}
					resMap.put("msgVo", msgVo);
				}
			} else {
				// 处理系统错误
				resMap.put("code", WxAuthException.System_Exception);
				resMap.put("codeMsg", WxAuthException.System_Exception_msg);
				MessageVo<Object> msgVo = new MessageVo<Object>();
				msgVo.setCode(MessageContants.EXCEPTION_CODE);
				msgVo.setCodeMsg(MessageContants.EXCEPTION_MSG);
				resMap.put("msgVo", msgVo);

			}
		}
		return resMap;
	}

	@Override
	public List<WxRoleMenu> getUserMenuList(Long userId) {
		WxTUserbase record = new WxTUserbase();
		record.setUserId(userId);
		return wxTUserbaseMapper.selectAllUserMenu(record);
	}

	/**
	 * 根据类型返回当前app版本信息
	 * @param appType "android" / "ios"
	 * @return Map
	 * @throws Exception
	 */
	@Override
	public Map<String, Object> searchAppInfo(String appType) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		try {
			String version = "";
			String changeLog = "";
			String downloadUrl = "";
			if (Constants.appType_az.equals(appType)) {
				version = (String) Constants.getSystemPropertyByCodeType(Constants.android_version);
				changeLog = (String) Constants.getSystemPropertyByCodeType(Constants.android_changeLog);
			} else if (Constants.appType_ios.equals(appType)) {
				version = (String) Constants.getSystemPropertyByCodeType(Constants.ios_version);
				changeLog = (String) Constants.getSystemPropertyByCodeType(Constants.ios_changeLog);
			}
			downloadUrl = (String) Constants.getSystemPropertyByCodeType(Constants.app_downloadUrl);

			resMap.put("code", "success");
			resMap.put("version", version);
			resMap.put("changeLog", changeLog);
			resMap.put("downloadUrl", downloadUrl);

		} catch (Exception ex) {// 处理系统错误
			resMap.put("code", "systemerror");
			resMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception")+"错误信息:"+ex.getMessage());
		}
		return resMap;
	}

	/**
	 * 查询通讯录信息 返回所有人员信息
	 */
	@Override
	public Map<String, Object> searchAddressList() throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		if (null == curUser) {
			resMap.put("code", "tokenfail");
		}

		try {
			WxTUserExample example = new WxTUserExample();
			example.setCurUserId(curUser.getUserId());
			// 未删除的用户
			example.createCriteria().andStatusEqualTo(1).andIsValidEqualTo("1");
			List<WxSimpleUser> userList = wxTUserMapper.selectAddressList(example);
			resMap.put("code", "success");
			resMap.put("addressList", userList);

		} catch (Exception ex) {
			ex.printStackTrace();
			resMap.put("code", "systemerror");
			resMap.put("codeMsg", ex.getMessage());
		}
		return resMap;
	}

	/**
	 * 单独查询用户详细信息
	 */
	@Override
	public Map<String, Object> searchUserInfo(String userId, String token) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		if (null == curUser) {
			resMap.put("code", "tokenfail");
		}
		try {
			Long lUserId = Long.valueOf(userId);
			WxTUser userInfo = wxTUserMapper.selectByPrimaryKey(lUserId);
			resMap.put("code", "success");
			if (userInfo != null) {
				userInfo.setPassword("");
				userInfo.setSalt("");
			}
			resMap.put("userInfo", userInfo);

		} catch (Exception ex) {
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	/**
	 * 验证密码
	 * @param inputPwd 输入的密码
	 * @param storedPwd 存储的密码
	 * @param salt 密码盐
	 * @return true/fasle
	 */
	private boolean validatePasswd(String inputPwd, WxTUser user, boolean checkPolicy) throws WxAuthException {
		boolean isValid = false;
//		try {
		if((user.getExtFlag() & WxTUser.EXT_FLAG_PWD_EXPIRED) > 0) {
			throw WxAuthException.createAuthObj(WxAuthException.PWD_EXPIRED, "密码已过期，不可登录。请点击“忘记密码?”按钮，按照系统提示进行密码重置，重置成功后方可登录");
		}
		if((user.getExtFlag() & WxTUser.EXT_FLAG_USER_FROZEN) > 0) {
//			throw WxAuthException.createAuthObj(WxAuthException.USER_FROZEN, "账号长期未使用，已被停用。点击“忘记密码？”重置后可激活");
			throw WxAuthException.createAuthObj(WxAuthException.USER_FROZEN, getUserFrozenMsg(user));
		}
			String encryptPwd = userBizService.toPasswd(inputPwd, user.getSalt());
			isValid = StringUtils.equals(encryptPwd, user.getPassword());
			if(isValid && checkPolicy && "Y".equals(Constants.getSystemPropertyByCodeType("User.pwdPolicy.TurnOn")) 
					&& ((user.getExtFlag() & WxTUser.EXT_FLAG_LOGIN_NO_CHECK_PWD_RULE) == 0 || (user.getExtFlag() & WxTUser.EXT_FLAG_PWD_TOBE_EXPIRE) > 0)) {
				if((user.getExtFlag() & WxTUser.EXT_FLAG_PWD_TOBE_EXPIRE) > 0) {
					WxAuthException ex = WxAuthException.createAuthObj(WxAuthException.PWD_WARNING_EXPIRE, new StringBuilder("您的密码将于")
							.append(user.getExtProperty1()).append("天后过期，请在过期前修改密码，过期后将无法使用旧密码登录系统").toString());
					ex.setAttr1(user.getExtProperty1());
					throw ex;
				}
				Map<String, String> pwdPolicyConfig = userBizService.getPwdPolicyConfig();
				try {
					userBizService.checkPwdPolicy(user.getUserId(), inputPwd, encryptPwd, JSONArray.fromObject(userBizService.getUserPwdRules(user, pwdPolicyConfig)), 
							UserBizService.PWD_FUN_FLAG_LOGIN, false);
				} catch (WxPltException e) {
//					LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.sys.auth.service.impl.UserService.validatePasswd", e.getMessage(), inputPwd);
					//缓冲期
					String deadDay = pwdPolicyConfig.get("deadDay");
					if(StringUtils.isNotBlank(deadDay)) {
						Date dd = DateUtil.parseDate(deadDay, "yyyy-MM-dd");
						long leftDay = DateUtil.dateDiff("days", dd, DateUtil.getCurrentDate());
						if(leftDay >= -1) {
							WxAuthException ex = WxAuthException.createAuthObj(WxAuthException.PWD_WARNING_ILLEGAL, new StringBuilder("根据雪佛龙全球安全政策要求，合伙人平台已升级密码策略。您的密码不符合当前系统要求，将于")
									.append(deadDay).append(" 23:59自动失效，失效后将无法使用旧密码登录系统，请修改密码").toString());
							ex.setAttr1(deadDay);
							throw ex;
						}else {
							WxAuthException ex = WxAuthException.createAuthObj(WxAuthException.PWD_ILLEGAL_FIRST, new StringBuilder("根据雪佛龙全球安全政策要求，合伙人平台已升级密码策略。您的密码不符合当前系统要求，已于")
									.append(deadDay).append(" 23:59自动失效，不可登录。请点击“忘记密码?”按钮，按照系统提示进行密码重置，重置成功后方可登录").toString());
							ex.setAttr1(deadDay);
							throw ex;
						}
					}
					throw WxAuthException.createAuthObj(WxAuthException.PWD_ILLEGAL, e.getMessage());
				}
			}
//		} catch (Exception ex) {
//			log.error("checkPassword exception", ex);
//			isValid = false;
//		}
		return isValid;
	}

//	/**
//	 * 明文密码加盐加密, 再Hex转码成字符串
//	 * @param inputPasswd 明文密码
//	 * @param salt 盐
//	 * @return 加密字符串密码
//	 */
//	private static String toPasswd(String inputPasswd, byte[] salt) {
//		byte[] hashPassword = Digests.sha1(inputPasswd.getBytes(), salt, Constants.HASH_INTERATIONS);
//		return Encodes.encodeHex(hashPassword);
//	}

	@Override
	public Map<String, Object> editUserInfo(Long userId, String chName, String address, String mobileTel, Long birthday,
			String password, String token) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		WxTUser curUser = ContextUtil.getCurUser();
		if (null == curUser) {
			resMap.put("code", "tokenfail");
		}
		try {
			WxTUser tUser = new WxTUser();
			tUser.setUserId(curUser.getUserId());
			tUser.setChName(chName);
			tUser.setAddress(address);
			tUser.setMobileTel(mobileTel);
			if (null != birthday) {
				tUser.setBirthday(new Date(birthday));
			} else {
				tUser.setBirthday(null);
			}
			password = "anta.1qa2ws";
			if (null != password) {
				WxTUser tempUser = wxTUserMapper.selectByPrimaryKey(curUser.getUserId());
				tempUser.setSalt(SaltUtil.getSalt());
				tUser.setPassword(userBizService.toPasswd(password, tempUser.getSalt()));
			}
			wxTUserMapper.updateByPrimaryKeySelective(tUser);
			resMap.put("code", "success");
		} catch (Exception ex) {
			ex.printStackTrace();
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	@Override
	public Map<String, Object> findUserByOrgPermissions(String userId, String userName, int start, int pageSize) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		try {
			if (userName != null) {
				userName = userName.trim();
			}
			WxTUser tUser = new WxTUser();
			tUser.setUserId(new Long(userId));
			tUser.setChName(userName);
			int totalRecord = wxTUserMapper.countUserList(tUser);
			List<WxTUser> list = wxTUserMapper.findUserByOrgPermissionsByPage(new Long(userId), userName,
					(start - 1) * pageSize, pageSize);
			if (list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					list.get(i)
							.setDescription("<a href=\"javascript:;\" onclick=\"selUserPerInfo('"
									+ list.get(i).getUserId() + "','" + list.get(i).getChName()
									+ "')\">查看权限</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
									+ "<a href=\"javascript:;\" onclick=\"givePermissions('" + list.get(i).getUserId()
									+ "','" + list.get(i).getChName() + "')\">授权操作</a>");
				}
				resMap.put("code", "success");
				resMap.put("resultList", list);
				resMap.put("totalRecord", totalRecord);
			} else {
				resMap.put("code", "notData");
			}
		} catch (Exception ex) {
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	@Override
	public Map<String, Object> updateUserDeviceId(String deviceId, String deviceType, Integer receiveMsg) {
		Map<String, Object> resMap = new HashMap<String, Object>();
		try {
			Long userId = ContextUtil.getCurUserId();
			WxTUser tUser = new WxTUser();
			tUser.setUserId(userId);
			tUser.setDeviceId(deviceId);
			tUser.setDeviceType(deviceType);
			tUser.setReceiveMsg(receiveMsg);
			wxTUserMapper.updateByPrimaryKeySelective(tUser);
			resMap.put("code", "success");
		} catch (Exception ex) {
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	/**
	 * 查询完整的组织树 通讯录时使用
	 */
	@Override
	public Map<String, Object> findTxlAllOrgTree() throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		try {
			// 加载所有组织信息
			WxTOrgExample orgExa = new WxTOrgExample();
			orgExa.createCriteria().andOrgRootIdEqualTo("1").andStatusEqualTo(1).andTenantIdEqualTo(Long.valueOf(1))
					.andOrgTypeNotEqualTo("POSTION");
			List<WxTOrg> orgList = wxTOrgMapper.selectByExample(orgExa);
			resMap.put("code", "success");
			resMap.put("orgList", orgList);

		} catch (Exception ex) {
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	/**
	 * 查询完整的组织树 通讯录时使用
	 */
	@Override
	public Map<String, Object> findInitialOrgTree() throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		try {
			// 加载所有组织信息
			List<WxTOrg> orgList = wxTOrgMapper.selOrgTreeByAll();
			resMap.put("code", "success");

			resMap.put("orgList", orgList);

		} catch (Exception ex) {
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	/**
	 * 修改用户密码
	 *
	 * @param username
	 * @param oldPassword
	 * @param newPassword
	 * @return
	 */
	@Override
	public Map<String, Object> changeUserPassword(String username, String oldPassword, String newPassword) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (StringUtils.isNull(username) || StringUtils.isNull(oldPassword) || StringUtils.isNull(newPassword)) {
				throw new WxPltException("illegal parameter");
			}

			WxTUser tUser = findUserByLoginName(username);
			if (tUser == null) {
				throw new WxPltException("username or password error");
			}

			boolean oldPasswordOk = validatePasswd(oldPassword, tUser, false);
			if (oldPasswordOk) {
//				String newEncryptPassword = userBizService.toPasswd(newPassword, tUser.getSalt());
//				tUser.setPassword(newEncryptPassword);
//				wxTUserMapper.updateByPrimaryKeySelective(tUser);
				userBizService.updateUserPassword(tUser, newPassword, resultMap);
				//修改密码后，重置登录状态
				HttpServletRequest request = ContextUtil.getHttpRequest();
				request.getSession().setAttribute("loginCode", null);
				request.getSession().setAttribute("loginMsg", null);
			} else {
				throw new WxPltException("old password is not correct");
			}
			resultMap.put("code", "success");
		} catch (WxPltException e) {
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		}

		return resultMap;
	}

//	@Override
//	public Map<String, Object> resetUserPassword(String username, String newPassword) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try {
//			WxTUser tUser = findUserByLoginName(username);
//			if (tUser == null) {
//				throw new WxPltException("user not exist");
//			}
//
//			String newEncryptPassword = toPasswd(newPassword, Encodes.decodeHex(tUser.getSalt()));
//			tUser.setPassword(newEncryptPassword);
//			wxTUserMapper.updateByPrimaryKeySelective(tUser);
//			resultMap.put("code", "success");
//		} catch (WxPltException e) {
//			resultMap.put("code", "error");
//			resultMap.put("codeMsg", e.getExpMsg());
//		}
//
//		return resultMap;
//	}

	@Override
	public Map<String, Object> findUserByOrgPermissionsNew(String userId, String userName, Long workshopId, int start, int pageSize)
			throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		try {
			if (userName != null) {
				userName = userName.trim();
			}
			Date d1 = new Date();

			// add by bo.liu 0826
			WxTUser currentUser = ContextUtil.getCurUser();
			Long orgId = currentUser.getOrgId();// partnerId
			int userType = 1;// 代表雪佛龙用户
			if (currentUser.getmUserTypes() == WxTUser.OTHER_USER_ROLE)// 普通用户
																		// 和spm
			{
				userType = 0;
			} else if (currentUser.getmUserTypes() == WxTUser.SERVICE_PARTNER_ADMIN_USER_ROLE) // spm
																								// admin特列
			{
				userType = 3;
			}

			reqMap.put("userId", currentUser.getUserId());
			if (workshopId != null) {
				reqMap.put("orgType", "1");
				reqMap.put("orgId", workshopId);
			} else {
				reqMap.put("userType", userType);
				reqMap.put("orgId", orgId);
			}
			reqMap.put("userName", userName);
			reqMap.put("start", start);
			reqMap.put("pageSize", pageSize);
			reqMap.put("serviceManager", Constants.SERVICE_PARTNER_MANAGER_CODE);
			reqMap.put("serviceAdmin", Constants.SERVICE_PARTNER_ADMIN_CODE);
			reqMap.put("chevronManager", Constants.CHEVRON_MANAGER_CODE);
			int totalRecord = wxTUserMapper.countUserByOrgPermissionsByPageNew(reqMap);
			List<WxTUser> list = wxTUserMapper.findUserByOrgPermissionsByPageNew(reqMap);

			Date d2 = new Date();
			log.info("cost: {}", d2.getTime() - d1.getTime());
			for (int i = 0; i < list.size(); i++) {

				WxTUser user = list.get(i);
				List<WxTUserrole> userRoleList = wxUserRoleServiceImpl.selectUserRoleByUserId(user.getUserId());
				StringBuilder roleNames = new StringBuilder();
				if (!userRoleList.isEmpty()) {
					List<Long> roleIds = new ArrayList<Long>();
					for (int j = 0; j < userRoleList.size(); j++) {
						WxTUserrole wtur = userRoleList.get(j);
						roleIds.add(wtur.getRoleId());
					}
					List<WxTRole> roleList = wxRoleServiceImpl.batchSelectRoleByRoleIds(roleIds);
					for (int k = 0; k < roleList.size(); k++) {
						WxTRole wtr = roleList.get(k);
						if (k == 0) {
							roleNames.append(wtr.getRoleDescript());
						} else {
							roleNames.append(",").append(wtr.getRoleDescript());
						}
					}
				}
				user.setChRoleName(roleNames.toString());
				StringBuffer sb = new StringBuffer();
				sb.append("<a href=\"javascript:;\" onclick=\"givePermissions('");
				sb.append(user.getUserId());
				sb.append("','");
				sb.append(user.getChName());
				sb.append("')\">授权操作</a>");
				user.setDescription(sb.toString());
			}
			resMap.put("code", "success");
			resMap.put("resultList", list);
			resMap.put("totalRecord", totalRecord);
		} catch (Exception ex) {
			resMap.put("code", "systemerror");
		}
		return resMap;
	}

	@Override
	public boolean isUserAdmin(Long userId) {
		WxTRole trole = wxTRoleMapper.selectRoleByChRoleName(Constants.ADMIN_CODE);
		long roleId = trole.getRoleId();
		WxTUserrole userRole = wxTUserroleMapper.selectUserRoleByUserIdAndRoleId(userId, roleId);
		if (null != userRole) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public int getCurrentUserType() {
		WxTUser currentUser = ContextUtil.getCurUser();
		Long organizationId = currentUser.getOrgId();
		OrganizationVo organization = organizationVoMapper.selectByPrimaryKey(organizationId);
		return organization.getType();
	}

	@Override
	public WxTUser getUserById(Long userId) {
		return wxTUserMapper.selectByPrimaryKey(userId);
	}

	/**
	 * TODO 待优化, 只需一条SQl更新密码即可 <br/>
	 * e.g.: update wx_t_user set password = ? where user_id= ? and password = ?<br/>
	 * add comments by Ervin 2017.7.6<br/>
	 */
	@Override
	public Map<String, Object> updateUserPassword(Long userId, String newPassword) {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			WxTUser user = userBizService.updateUserPassword(userId, newPassword, resultMap);
			//密码更新成功，发送邮件
			if(StringUtils.isNotBlank(user.getEmail())) {
				WebApplicationContext webApplicationContext = ContextLoader
						.getCurrentWebApplicationContext();
				ServletContext servletContext = webApplicationContext
						.getServletContext();
				Map<String, Object> rootMap = new HashMap<String, Object>();
				String subject = "合伙人平台密码重置提醒";
				String emailTemplate = "user/pwd_reset_notify_email.ftl";
				
				rootMap.put("user", user);
				rootMap.put("newPassword", newPassword);
				if(!EmailSendUtils.sendEmailForListContent(
						servletContext, new String[] {user.getEmail()}, 
						null, subject, rootMap, null, emailTemplate)){
					throw new RuntimeException("邮件发送失败。" + user.getLoginName());
				}
			}

			resultMap.put("result", "success");
		} catch (WxPltException e) {
			resultMap.put("result", "error");
			resultMap.put("error", e.getExpCode());
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getExpMsg());
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("error", "updateError");
			log.error(e.getMessage(), e);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> checkUserResetFlag(String username) {
		Map<String,Object> resultMap=new HashMap<String,Object>();
		WxTUser user=wxTUserMapper.findUserByLoginName(username);
		if((user.getExtFlag() & WxTUser.EXT_FLAG_USER_FROZEN) > 0) {
			//账号已停用
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, getUserFrozenMsg(user));
			resultMap.put(Constants.RESULT_CODE_KEY, "FROZEN");
		}else if((user.getResetFlag()!=null)&&(user.getResetFlag()==1)) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "首次登陆请重置密码");
		} else {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		}
		return resultMap;
	}
	
	private String getUserFrozenMsg(WxTUser user) {
		if((user.getUserFlag() & WxTRole.USER_FLAG_DISTRIBUTOR_BOSS) > 0) {
			//经销商老板
			return WxAuthException.USER_FROZEN_MSG_DISTRIBUTOR_ADMIN;
		}else if(user.getOrgType() == 0) {
			//雪佛龙用户
			return WxAuthException.USER_FROZEN_MSG_CHEVRON;
		}else {
			//DSR
			return WxAuthException.USER_FROZEN_MSG_DSR;
		}
	}

	@Override
	public WxTUser getUserByCai(String cai) {
		List<WxTUser> userList=wxTUserMapper.selectUserByCai(cai);
		if(userList.size()>0) {
			return userList.get(0);
		}
		return null;
	}

	@Override
	public Long decryptUserId(String encryptedUserId) {
		String aesKey = (String) Constants.getSystemPropertyByCodeType(Constants.DMS_ENCRYPT_KEY);
		String decryptResult = Cryptos.aesDecrypt(Encodes.decodeHex(encryptedUserId), Arrays.copyOf(aesKey.getBytes(), 16));
		return Long.parseLong(decryptResult.substring(0, decryptResult.indexOf("-")));
	}

	@Override
	public String encryptUserId(Long userId){
		String aesKey = (String) Constants.getSystemPropertyByCodeType(Constants.DMS_ENCRYPT_KEY);
		String currentDateTime = "1";//System.currentTimeMillis() + "";
		String tokenSource = userId + "-" + currentDateTime;
		String token = Encodes.encodeHex(Cryptos.aesEncrypt(tokenSource.getBytes(), Arrays.copyOf(aesKey.getBytes(), 16)));
		return token;
	}
	
	@Override
	public CustomerRegionUser getCustomerRegionUser()throws Exception {
		log.info("PromotePlanServiceImpl getCustomerRegionUserV2");
		WxTUser currentUser = ContextUtil.getCurUser();
		CustomerRegionUser customerUser = new CustomerRegionUser();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("userId", currentUser.getUserId());
		reqMap.put("regionType", Constants.REGION_TYPE);
		customerUser = wxTUserMapper.getCustomerRegionUserInfo(reqMap);
		if(null==customerUser)
		{
			log.info("PromotePlanServiceImpl getCustomerRegionUserV2 customerUser is null");
			customerUser = new CustomerRegionUser();
		}
		customerUser.setUserId(currentUser.getUserId());
		customerUser.setLoginName(currentUser.getLoginName());
		customerUser.setChName(currentUser.getChName());
		String userCai = customerUser.getCai();
		log.info("PromotePlanServiceImpl getCustomerRegionUserV2 userCai：{},customerUser:{}",userCai,customerUser);
		if(null!=userCai && !userCai.isEmpty())
		{
			if(null!=customerUser.getChannelManagerCai() && !customerUser.getChannelManagerCai().isEmpty())//大区经理
			{
				customerUser.setCustomerRoleName(Constants.PROMOTE_CHANNELMANAGER);
				customerUser.setRegionName(customerUser.getChannelRegionName());
			}else if(null!=customerUser.getSupervisorCai() && !customerUser.getSupervisorCai().isEmpty())//小区经理
			{
				customerUser.setCustomerRoleName(Constants.PROMOTE_SUPERVISOR);
				customerUser.setRegionName(customerUser.getSupervisorRegionName());
			}else if(null!=customerUser.getSalesCai() && !customerUser.getSalesCai().isEmpty())//销售
			{
				customerUser.setCustomerRoleName(Constants.PROMOTE_SALES);
				customerUser.setRegionName(customerUser.getSalesRegionName());
			} else {
				List<WxTRole> roleLst = currentUser.getRoleList();
				 for(WxTRole role: roleLst)
				 {
					 if(role.getChRoleName().equals(PromoteRoleEnum.CAI_ADMIN.getRoleCode()))//c&i管理员
					 {
						 customerUser.setCustomerRoleName(Constants.PROMOTE_CAI_CHEVRON_MANAGER);
					 }else if(role.getChRoleName().equals(PromoteRoleEnum.CAI_MARKETING.getRoleCode()))//c&i marketing
					 {
						customerUser.setCustomerRoleName(Constants.PROMOTE_MARKETING);
						//针对marketing： 看当前登录是否有配置审批的权限
						//看是否有审批的权限
						Map<String, Object> promoteMarketingPower =  dicService.getDicItemByDicTypeCode("promote.marketing.power");
						List<DicItemVo> promoteMarketingItemlist = (ArrayList)promoteMarketingPower.get("data");
						for(DicItemVo dicItem : promoteMarketingItemlist){
							if(dicItem.getDicItemCode().equals(currentUser.getLoginName())){
								customerUser.setApproveFlag(dicItem.getDicItemName());
								break;
							}
						}
					 }
				 }
			}
			
		}else
		{	
			 List<WxTRole> roleLst = currentUser.getRoleList();
			 for(WxTRole role: roleLst)
			 {
				 if(role.getChRoleName().equals(PromoteRoleEnum.CAI_DEALER.getRoleCode()))//c&i经销商
				 {
					 customerUser.setCustomerRoleName(Constants.PROMOTE_DEALER);
				 }else if(role.getChRoleName().equals(PromoteRoleEnum.CAI_ADMIN.getRoleCode()))//c&i管理员
				 {
					 customerUser.setCustomerRoleName(Constants.PROMOTE_CAI_CHEVRON_MANAGER);
				 }else if(role.getChRoleName().equals(PromoteRoleEnum.CAI_MARKETING.getRoleCode()))//c&i marketing
				 {
					customerUser.setCustomerRoleName(Constants.PROMOTE_MARKETING);
					//针对marketing： 看当前登录是否有配置审批的权限
					//看是否有审批的权限
					Map<String, Object> promoteMarketingPower =  dicService.getDicItemByDicTypeCode("promote.marketing.power");
					List<DicItemVo> promoteMarketingItemlist = (ArrayList)promoteMarketingPower.get("data");
					for(DicItemVo dicItem : promoteMarketingItemlist){
						if(dicItem.getDicItemCode().equals(currentUser.getLoginName())){
							customerUser.setApproveFlag(dicItem.getDicItemName());
							break;
						}
					}
				 }
			 }
			
		}
		return customerUser;
	}

	@Override
	public Map<String, Object> logAppError(String function,
			String errorMsg, String extMsg1, String extMsg2, String extMsg3,
			String extMsg4, String extMsg5) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			LogUtils.addLog(ContextUtil.getCurUserId(), function, "error", errorMsg, extMsg1, extMsg2, extMsg3, extMsg4, extMsg5);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}
	
	/**
	 * 密码多次输入错误锁定用户，返回剩余尝试次数
	 * @param userId
	 */
	private Integer lockUserForPwdError(Long userId) {
		int pwdErrorNum = wxTLoginErrorLogMapper.getLoginErrorNum(userId);
		//获取密码错误锁定用户次数
		Integer lockNum = null;
		Map<String, Object> resultLst =  dicService.getDicItemByDicTypeCode("password.error.config");
		@SuppressWarnings("unchecked")
		List<DicItemVo> itemLst = (ArrayList<DicItemVo>) resultLst.get("data");
		for(DicItemVo dicItem : itemLst){
			if("lock.user.items".equals(dicItem.getDicItemCode())) {
				lockNum = Integer.parseInt(dicItem.getDicItemName());
			}
		}
		//新增登陆失败记录
		WxTLoginErrorLog errorLog = new WxTLoginErrorLog();
		errorLog.setUserId(userId);
		errorLog.setLoginTime(new Date());
		errorLog.setStatus(1);
		wxTLoginErrorLogMapper.insert(errorLog);
		//剩余尝试次数
		Integer remainNum = 0;
		if(pwdErrorNum + 1 >= lockNum) {
			//锁定用户
			WxTUser updateData = new WxTUser();
			updateData.setUserId(userId);
			updateData.setAllowLogin("F");
			wxTUserMapper.updateByPrimaryKeySelective(updateData);
		} else {
			remainNum = lockNum - (pwdErrorNum + 1);
		}
		return remainNum;
	}
	
	@Override
	public JsonResponse remoteUserLogin(String loginName, String password){
		log.info("remoteUserLogin: " + loginName);
		WxTUser tUser = null;
		JsonResponse resMap = new JsonResponse();
		try {
			tUser = userLogin4App(loginName, password, false);

			if (tUser == null || tUser.getUserId() == null) {// 密码正确，如果不正确 userId为空
				resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resMap.setErrorMsg(MessageContants.ACCOUNT_PASSWD_ERROR_MSG);
				return resMap;
			}
//			// 创建token
//			tUser.setPassword("");
//			tUser.setSalt("");

			// 存入tokenUser
//			String token = ContextUtil.createToken(loginName, password);
//			ContextUtil.putTokenUser(token, tUser);
//			/*tokenMgntServicelmpl.setToken2LoginName(token, loginName);*/
//			ContextUtil.addUserToLoginList(tUser, null, token);

			resMap.put("token", tUser.getToken());
			log.info("remoteUserLogin success: " + loginName);
		} catch (WxAuthException ex) {
			resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resMap.setErrorMsg(ex.getExpMsg());
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.remoteUserLogin", loginName);
		}
		return resMap;
	}
	
	@Override
	public JsonResponse remoteUserLoginBySign(String loginName, String client, String sign){
		log.info("remoteUserLoginBySign: " + loginName);
		WxTUser tUser = null;
		JsonResponse resMap = new JsonResponse();
		try {
			String configSign = (String)Constants.getSystemPropertyByCodeType("LoginSign." + client);
			if(StringUtils.isBlank(configSign)) {
				throw new WxAuthException(client + "登录已停用");
			}
			if(!configSign.equals(sign)) {
				throw new WxAuthException(client + "登录签名不匹配");
			}
			LogUtils.addInfoLog(1l, "com.sys.auth.service.impl.UserService.remoteUserLoginBySign", loginName + "," + client + "," + sign);
			tUser = userLogin4App(loginName, null, true);

			if (tUser == null || tUser.getUserId() == null) {// 密码正确，如果不正确 userId为空
				resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
				resMap.setErrorMsg(MessageContants.ACCOUNT_PASSWD_ERROR_MSG);
				return resMap;
			}
//			// 创建token
//			tUser.setPassword("");
//			tUser.setSalt("");

//			// 存入tokenUser
//			String token = ContextUtil.createToken(loginName, "");
//			ContextUtil.putTokenUser(token, tUser);
//			/*tokenMgntServicelmpl.setToken2LoginName(token, loginName);*/
//			ContextUtil.addUserToLoginList(tUser, null, token);

			resMap.put("token", tUser.getToken());
			log.info("remoteUserLoginBySign success: " + loginName + "," + client + "," + sign);
		} catch (WxAuthException ex) {
			resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resMap.setErrorMsg(ex.getExpMsg());
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.remoteUserLoginBySign", loginName + "," + client + "," + sign);
		}
		return resMap;
	}
	
	@Override
	public Map<String, Object> validateToken(String token) {
        log.info("token: {}", token);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
        	String isLogin = "false";
	        if(!StringUtils.isNull(token)){
	        	//根据token中获取用户对象
	        	WxTUser user =  ContextUtil.createTokenUser(token);
	        	log.info("tokenUser: {}", user);
	        	if(user != null){
	        		isLogin = "true";
	        		resultMap.put("username", user.getLoginName());
	        	}
	        } 
	        resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
	        resultMap.put("isLogin", isLogin);
        } catch (Exception ex) {
        	resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
        	resultMap.put(Constants.RESULT_CODE_MSG_KEY, ex.getMessage());
		}
        return resultMap;
	}

	@Override
	public JsonResponse getLoginUser() {
		log.info("getLoginUser start.");
		JsonResponse resMap = new JsonResponse();
		try {
			WxTUser user = ContextUtil.getCurUser();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("userModel", user.getUserModel());
			data.put("userId", user.getUserId());
			data.put("includeChannels", user.getIncludeChannels());
			data.put("chName", user.getChName());
			data.put("distributorId", user.getDistributorId());
			data.put("orgId", user.getOrgId());
			data.put("orgName", user.getOrgName());
			data.put("partnerFlag", user.getPartnerFlag());
			data.put("userFlag", user.getUserFlag());
			data.put("cai", user.getCai());
			data.put("token", user.getToken());
			resMap.setDataResult(data);
			log.info("getLoginUser success.");
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.getLoginUser", null);
		}
		return resMap;
	}
	@Override
	public JsonResponse sendPhoneCaptcha(String mobile){
		log.info("sendPhoneCaptcha: " + mobile);
		JsonResponse resMap = new JsonResponse();
		try {
			Random random = new Random();
			int capInt;
			do {
				capInt=Math.abs(random.nextInt())%9999;
			}while(capInt<1000);
			String captcha=String.valueOf(capInt);
			log.info("验证码："+captcha);
			tokenMgntServicelmpl.setUserProperties(1l, "PhoneCaptcha." + mobile, captcha);
			SMSUtil.sendSms(mobile, "您的验证码是："+captcha+"，十分钟内有效", SMSUtil.APP_ID_PP);

			log.info("sendPhoneCaptcha success.");
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.sendPhoneCaptcha", mobile);
		}
		return resMap;
	}
	
	@Override
	public JsonResponse bindWorkshopEmplyeeToWechat(String mobile, String openId, String captcha){
		log.info("bindWorkshopEmplyeeToWechat: " + mobile);
		JsonResponse resMap = new JsonResponse();
		try {
			WorkshopEmployeeExample example = new WorkshopEmployeeExample();
			example.createCriteria().andMobileEqualTo(mobile).andEnableFlagEqualTo(1);
			List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
			if(list.isEmpty()) {
				resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resMap.put(Constants.RESULT_ERROR_MSG_KEY, "技师不存在");
				return resMap;
				//throw new WxPltException("技师不存在");
			}
			if(StringUtils.isBlank(captcha)) {
				resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resMap.put(Constants.RESULT_ERROR_MSG_KEY, "验证码不能为空");
				return resMap;
				//throw new WxPltException("验证码不能为空");
			}
			if(!captcha.equals(tokenMgntServicelmpl.getUserProperties(1l, "PhoneCaptcha." + mobile))) {
				resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resMap.put(Constants.RESULT_ERROR_MSG_KEY, "验证码失效。请重试");
				return resMap;
				//throw new WxPltException("验证码失效。请重试");
			}
			WorkshopEmployee workshopEmployee = new WorkshopEmployee();
			workshopEmployee.setWechatOpenId(openId);
			workshopEmployee.setId(list.get(0).getId());
			workshopEmployeeMapper.updateByPrimaryKeySelective(workshopEmployee);
			resMap.put("token", generateB2bToken());

			log.info("bindWorkshopEmplyeeToWechat success.");
		} catch (WxPltException e) {
			resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.bindWorkshopEmplyeeToWechat", mobile + "," + openId + "," + captcha);
		}
		return resMap;
	}
	
	@Override
	public JsonResponse boundCheck(String openId){
		log.info("boundCheck: " + openId);
		JsonResponse resMap = new JsonResponse();
		try {
			WorkshopEmployeeExample example = new WorkshopEmployeeExample();
			example.createCriteria().andWechatOpenIdEqualTo(openId).andEnableFlagEqualTo(1);
			List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
			if(list.isEmpty()) {
				resMap.put("boundFlag", 0);
				return resMap;
			}
			resMap.put("boundFlag", 1);
			resMap.put("openId", openId);
			resMap.put("token", generateB2bToken());
			resMap.put("mobile", list.get(0).getMobile());
			log.info("boundCheck success.");
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.boundCheck", openId);
		}
		return resMap;
	}
	
	protected String generateB2bToken() throws WxPltException {
		String loginName = (String)Constants.getSystemPropertyByCodeType("PPB2bUser.userName");
		String password = (String)Constants.getSystemPropertyByCodeType("PPB2bUser.pwd");
		return createToken(loginName, password);
//		WxTUser tUser = userLogin4App(loginName, password, false);
//
//		if (tUser == null || tUser.getUserId() == null) {// 密码正确，如果不正确 userId为空
//			throw new WxPltException("系统账号已停用");
//		}
////		// 创建token
////		tUser.setPassword("");
////		tUser.setSalt("");
//
////		// 存入tokenUser
////		String token = ContextUtil.createToken(loginName, password);
////		ContextUtil.putTokenUser(token, tUser);
////		/*tokenMgntServicelmpl.setToken2LoginName(token, loginName);*/
////		ContextUtil.addUserToLoginList(tUser, null, token);
//		return tUser.getToken();
	}
	
	public JsonResponse b2bBoundCheck(String b2bOpenId) {
		log.info("b2bBoundCheck: " + b2bOpenId);
		JsonResponse resMap = new JsonResponse();
		try {
			WorkshopEmployeeExample example = new WorkshopEmployeeExample();
			example.createCriteria().andB2bOpenIdEqualTo(b2bOpenId).andEnableFlagEqualTo(1);
			List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
			if(list.isEmpty()) {
				resMap.put("boundFlag", 0);
				return resMap;
			}
			resMap.put("boundFlag", 1);
			resMap.put("openId", b2bOpenId);
			resMap.put("token", generateB2bToken());
			resMap.put("mobile", list.get(0).getMobile());
			log.info("boundCheck success.");
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.boundCheck", b2bOpenId);
		}
		return resMap;
	}
	
	public JsonResponse bindWorkshopEmplyeeToB2B(String mobile, String b2bOpenId, String captcha) {
		log.info("bindWorkshopEmplyeeToB2B: " + mobile);
		JsonResponse resMap = new JsonResponse();
		try {
			WorkshopEmployeeExample example = new WorkshopEmployeeExample();
			example.createCriteria().andMobileEqualTo(mobile).andEnableFlagEqualTo(1);
			List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
			if(list.isEmpty()) {
				resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resMap.put(Constants.RESULT_ERROR_MSG_KEY, "技师不存在");
				return resMap;
			}
			if(StringUtils.isBlank(captcha)) {
				resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resMap.put(Constants.RESULT_ERROR_MSG_KEY, "验证码不能为空");
				return resMap;
			}
			if(!captcha.equals(tokenMgntServicelmpl.getUserProperties(1l, "PhoneCaptcha." + mobile))) {
				resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resMap.put(Constants.RESULT_ERROR_MSG_KEY, "验证码失效。请重试");
				return resMap;
			}
			WorkshopEmployee workshopEmployee = new WorkshopEmployee();
			workshopEmployee.setB2bOpenId(b2bOpenId);
			workshopEmployee.setId(list.get(0).getId());
			workshopEmployeeMapper.updateByPrimaryKeySelective(workshopEmployee);
			resMap.put("token", generateB2bToken());

			log.info("bindWorkshopEmplyeeToB2B success.");
		} catch (WxPltException e) {
			resMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.bindWorkshopEmplyeeToB2B", mobile + "," + b2bOpenId + "," + captcha);
		}
		return resMap;
	}
	
	public JsonResponse loginByMechanic(String mobile, String captcha) {
		log.info("loginByMechanic: " + mobile);
		ResponseMap resMap = new ResponseMap();
		try {
			if(StringUtils.isBlank(captcha)) {
				throw new WxPltException("验证码不能为空");
			}
			if(!captcha.equals(tokenMgntServicelmpl.getUserProperties(1l, "PhoneCaptcha." + mobile))) {
				throw new WxPltException("验证码失效。请重试");
			}
			WorkshopEmployeeExample example = new WorkshopEmployeeExample();
			example.createCriteria().andMobileEqualTo(mobile).andEnableFlagEqualTo(1);
			List<WorkshopEmployee> list = workshopEmployeeMapper.selectByExample(example);
			if(list.isEmpty()) {
				throw new WxAuthException("技师不存在");
			}
			resMap.put("token", createToken((String)Constants.getSystemPropertyByCodeType("MechanicApp.loginName"), 
					(String)Constants.getSystemPropertyByCodeType("MechanicApp.pwd")));
			resMap.put("workshopEmployee", list.get(0));
			log.info("loginByMechanic success.");
		} catch (WxPltException e) {
			resMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resMap.setErrorMsg(e.getMessage());
		} catch (Exception ex) {
			resMap.handleException(ex, this, 1l, "com.sys.auth.service.impl.UserService.loginByMechanic", mobile + "," + captcha);
		}
		return resMap;
	}
	
	protected String createToken(String loginName, String password) throws WxPltException {
		WxTUser tUser = userLogin4App(loginName, password, false);

		if (tUser == null || tUser.getUserId() == null) {// 密码正确，如果不正确 userId为空
			throw new WxPltException("系统账号已停用");
		}
//		// 创建token
//		tUser.setPassword("");
//		tUser.setSalt("");
//
//		// 存入tokenUser
//		String token = ContextUtil.createToken(loginName, password);
//		ContextUtil.putTokenUser(token, tUser);
//		ContextUtil.addUserToLoginList(tUser, null, token);
		return tUser.getToken();
	}


	/**
	 * 模拟登录方法(Web Page端)，用于监测系统是否可正常登录
	 *
	 * @param loginName
	 * @throws Exception
	 */
	public void checkUserLogin(String loginName) {
		WxTUser tUser;
		try {
			tUser = checkUserLogin4App(loginName);
		} catch (WxAuthException e) {
			if (e.getExpCode() != null && e.getExpCode().startsWith(WxAuthException.PWD_WARNING_PREFIX)) {
				tUser = findUserByLoginName(loginName);
			} else {
				throw e;
			}
		}
		// 加载用户权限菜单项
		loadMenuInfo(tUser);

	}

	/**
	 * 模拟登录方法(APP端)，监测pp系统是否正常登录
	 * @param loginName
	 * @param password
	 * @return WxTUser
	 * @throws Exception
	 * <AUTHOR>
	 */
	private WxTUser checkUserLogin4App(String loginName) {
		//1. 获取登录用户信息
		WxTUser tUser = findUserByLoginName(loginName);

		if (tUser != null) {
			try {
				//2.1 验证密码，包含密码策略验证
//				if(!validatePasswd(password, tUser, false)){
//					throw new WxAuthException("密码错误");
//				}
				//生成token
				//ContextUtil.createToken(tUser.getLoginName(), password);
				//3.1 初始化用户登录上下文信息
				tUser = updateUserAndPush2Context(tUser);
			} catch (WxAuthException e) {
				throw e;
			}
		} else {
			// 本地用户不存在, 抛出异常
			throw WxAuthException.createAuthObj(WxAuthException.Local_Exp_UserNot, null);
		}
		return tUser;
	}
}
