/*==============================================================*/
/* Module: INVENTORY2021                                        */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_inventory2021                                    */
/*==============================================================*/
create table wx_t_inventory2021 (
	id                               bigint                            not null identity,
	body_type                        nvarchar(30)                               not null,
	body_key                         bigint                                     not null,
	product_type                     nvarchar(30)                               not null,
	product_key                      nvarchar(30)                               not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	version_no                       bigint                                     not null,
	current_quantity                 int                                            null,
	inventory_type                   nvarchar(64)                                   null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	delete_flag                      bigint                                     not null,
	primary key (id)
);
ALTER TABLE wx_t_inventory2021 ADD CONSTRAINT uk_inventory2021 UNIQUE (body_type,body_key,product_type,product_key,delete_flag);
CREATE INDEX ix_inventory2021_KCZT ON wx_t_inventory2021 (body_type,body_key);
CREATE INDEX ix_inventory2021_KCCP ON wx_t_inventory2021 (product_type,product_key);

/*==============================================================*/
/* Table: wx_t_inventory_log2021                                */
/*==============================================================*/
create table wx_t_inventory_log2021 (
	id                               bigint                            not null identity,
	business_type                    nvarchar(30)                               not null,
	business_key                     nvarchar(30)                               not null,
	inventory_id                     bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	original_quantity                int                                        not null,
	modify_quantity                  int                                        not null,
	log_desc                         nvarchar(256)                              not null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	delete_flag                      tinyint                                    not null,
	primary key (id)
);
CREATE INDEX ix_inventory_log2021_KCBGYW ON wx_t_inventory_log2021 (business_type,business_key);

/*==============================================================*/
/* Table: wx_t_store2021                                        */
/*==============================================================*/
create table wx_t_store2021 (
	id                               bigint                            not null identity,
	org_type                         nvarchar(30)                               not null,
	org_id                           bigint                                     not null,
	region_id                        bigint                                         null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	name                             nvarchar(256)                                  null,
	store_code                       nvarchar(30)                                   null,
	store_type                       nvarchar(64)                                   null,
	comments                         nvarchar(1024)                                 null,
	address                          nvarchar(256)                                  null,
	contact_person                   nvarchar(100)                                  null,
	contact_person_tel               nvarchar(100)                                  null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	delete_flag                      tinyint                                    not null,
	primary key (id)
);
CREATE INDEX ix_store2021_CKJG ON wx_t_store2021 (org_type,org_id);

/*==============================================================*/
/* Table: wx_t_inventory_check2021                              */
/*==============================================================*/
create table wx_t_inventory_check2021 (
	id                               bigint                            not null identity,
	body_type                        nvarchar(30)                               not null,
	body_key                         nvarchar(30)                               not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	comments                         nvarchar(1024)                                 null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(512)                                  null,
	delete_flag                      tinyint                                    not null,
	primary key (id)
);
CREATE INDEX ix_inventory_check2021_PKZT ON wx_t_inventory_check2021 (body_type,body_key);

/*==============================================================*/
/* Foreign Key(only index): ix_inv_log_inv                      */
/*==============================================================*/
CREATE INDEX ix_inv_log_inv ON wx_t_inventory_log2021 (inventory_id);

/*==============================================================*/
/* Foreign Key(only index): ix_store2021_region                 */
/*==============================================================*/
CREATE INDEX ix_store2021_region ON wx_t_store2021 (region_id);
