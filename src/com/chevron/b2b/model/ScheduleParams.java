package com.chevron.b2b.model;

import com.chevron.pms.model.BaseParams;
import com.common.util.DateUtil;
import com.common.util.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * 计划列表查询参数
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
public class ScheduleParams extends BaseParams {

	/**  活动名称  **/
    private String scheduleName;

	/**  活动类型  **/
    private String scheduleType;

	/** 起始日期条件字符串 */
	private String publishDateFromStr;
	
	/** 截止日期条件字符串 */
	private String publishDateToStr;
	
	private String executeUserType;
	
    private Integer status;
    
    private String salesChannel;

	/**
	 * 获取活动名称条件
	 * @return 活动名称条件
	 */
    public String getScheduleName() {
        return scheduleName;
    }
    
	/**
	 * 设置活动名称条件
	 * @param scheduleName 活动名称条件
	 */
    public void setScheduleName(String scheduleName) {
        this.scheduleName = scheduleName;
    }

	/**
	 * 获取活动类型条件
	 * @return 活动类型条件
	 */
    public String getScheduleType() {
        return scheduleType;
    }
    
	/**
	 * 设置活动类型条件
	 * @param scheduleType 活动类型条件
	 */
    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

	/**
	 * 获取起始日期条件字符串
	 * @return 起始日期条件字符串
	 */
	public String getPublishDateFromStr() {
		return publishDateFromStr;
	}

	/**
	 * 获取截止日期条件字符串
	 * @return 截止日期条件字符串
	 */
	public String getPublishDateToStr() {
		return publishDateToStr;
	}

	/**
	 * 设置起始日期条件字符串
	 * @param publishDateFromStr 起始日期条件字符串
	 */
	public void setPublishDateFromStr(String publishDateFromStr) {
		this.publishDateFromStr = publishDateFromStr;
	}

	/**
	 * 设置截止日期条件字符串
	 * @param publishDateFromStr 截止日期条件字符串
	 */
	public void setPublishDateToStr(String publishDateToStr) {
		this.publishDateToStr = publishDateToStr;
	}

	/**
	 * 获取起始日期条件
	 * @return 起始日期条件
	 */
	public Date getPublishDateFrom() {
		if(StringUtils.isEmpty(publishDateFromStr)){
			return null;
		}
		return DateUtil.parseDate(publishDateFromStr);
	}

	/**
	 * 获取截止日期条件
	 * @return 截止日期条件
	 */
	public Date getPublishDateTo() {
		if(StringUtils.isEmpty(publishDateToStr)){
			return null;
		}
		return DateUtil.addDays(DateUtil.parseDate(publishDateToStr), 1);
	}

	@Override
	protected Map<String, String> getFieldMap() {
		return null;
	}

	public String getExecuteUserType() {
		return executeUserType;
	}

	public void setExecuteUserType(String executeUserType) {
		this.executeUserType = executeUserType;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}
}
