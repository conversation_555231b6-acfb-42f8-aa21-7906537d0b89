package com.chevron.promote.model;

import java.util.ArrayList;
import java.util.List;

public class ResponseActivityPacksDetail {

	//新店开业活动
	/**
	 * 入店礼包
	 */
	private List<ResponsePacksDetail> rdPacksLst = new ArrayList<ResponsePacksDetail>();
	/**
	 * 是否有入店工作服
	 */
	private String rdWorkClothes =  BasePromote.WORKCLOTHES_FALSE;
	/**
	 * 攻店礼包
	 */
	private List<ResponsePacksDetail> gdPacksLst = new ArrayList<ResponsePacksDetail>();
	/**
	 * 是否有攻店工作服
	 */
	private String gdWorkClothes =  BasePromote.WORKCLOTHES_FALSE;
	
	//路演
	/**
	 * 路演礼包
	 */
	private List<ResponsePacksDetail> roadshowPacksLst = new ArrayList<ResponsePacksDetail>();
	/**
	 * 是否有路演礼包工作服
	 */
	private String roadshowWorkClothes =  BasePromote.WORKCLOTHES_FALSE;
	/**
	 * 消费者互动礼包
	 */
	private List<ResponsePacksDetail> consumerInteractionPacksLst = new ArrayList<ResponsePacksDetail>();
	/**
	 * 是否有消费者互动礼包工作服
	 */
	private String consumerInteractionWorkClothes =  BasePromote.WORKCLOTHES_FALSE;
	
	//研讨会
	private List<ResponsePacksDetail> ythPacksLst = new ArrayList<ResponsePacksDetail>();
	/**
	 * 是否有研讨会工作服
	 */
	private String ythWorkClothes = BasePromote.WORKCLOTHES_FALSE;
	
	private List<ResponsePacksDetail> yzythPacksLst = new ArrayList<ResponsePacksDetail>();
	
	private List<ResponsePacksDetail> dzPacksLst = new ArrayList<ResponsePacksDetail>();
	
	private List<ResponsePacksDetail> hwggPacksLst = new ArrayList<ResponsePacksDetail>();
	
	private List<ResponsePacksDetail> nkPacksLst = new ArrayList<ResponsePacksDetail>();
	
	private List<ResponsePacksDetail> syPacksLst = new ArrayList<ResponsePacksDetail>();
	

	private ResponseWorkClothes responseWorkClothes;
	public List<ResponsePacksDetail> getRdPacksLst() {
		return rdPacksLst;
	}

	public void setRdPacksLst(List<ResponsePacksDetail> rdPacksLst) {
		this.rdPacksLst = rdPacksLst;
	}

	public List<ResponsePacksDetail> getGdPacksLst() {
		return gdPacksLst;
	}

	public void setGdPacksLst(List<ResponsePacksDetail> gdPacksLst) {
		this.gdPacksLst = gdPacksLst;
	}

	public List<ResponsePacksDetail> getRoadshowPacksLst() {
		return roadshowPacksLst;
	}

	public void setRoadshowPacksLst(List<ResponsePacksDetail> roadshowPacksLst) {
		this.roadshowPacksLst = roadshowPacksLst;
	}

	public List<ResponsePacksDetail> getConsumerInteractionPacksLst() {
		return consumerInteractionPacksLst;
	}

	public void setConsumerInteractionPacksLst(
			List<ResponsePacksDetail> consumerInteractionPacksLst) {
		this.consumerInteractionPacksLst = consumerInteractionPacksLst;
	}

	public List<ResponsePacksDetail> getYthPacksLst() {
		return ythPacksLst;
	}

	public void setYthPacksLst(List<ResponsePacksDetail> ythPacksLst) {
		this.ythPacksLst = ythPacksLst;
	}

	public String getRdWorkClothes() {
		return rdWorkClothes;
	}

	public void setRdWorkClothes(String rdWorkClothes) {
		this.rdWorkClothes = rdWorkClothes;
	}

	public String getGdWorkClothes() {
		return gdWorkClothes;
	}

	public void setGdWorkClothes(String gdWorkClothes) {
		this.gdWorkClothes = gdWorkClothes;
	}

	public String getRoadshowWorkClothes() {
		return roadshowWorkClothes;
	}

	public void setRoadshowWorkClothes(String roadshowWorkClothes) {
		this.roadshowWorkClothes = roadshowWorkClothes;
	}

	public String getConsumerInteractionWorkClothes() {
		return consumerInteractionWorkClothes;
	}

	public void setConsumerInteractionWorkClothes(
			String consumerInteractionWorkClothes) {
		this.consumerInteractionWorkClothes = consumerInteractionWorkClothes;
	}

	public String getYthWorkClothes() {
		return ythWorkClothes;
	}

	public void setYthWorkClothes(String ythWorkClothes) {
		this.ythWorkClothes = ythWorkClothes;
	}

	public ResponseWorkClothes getResponseWorkClothes() {
		return responseWorkClothes;
	}

	public void setResponseWorkClothes(ResponseWorkClothes responseWorkClothes) {
		this.responseWorkClothes = responseWorkClothes;
	}

	public List<ResponsePacksDetail> getYzythPacksLst() {
		return yzythPacksLst;
	}

	public void setYzythPacksLst(List<ResponsePacksDetail> yzythPacksLst) {
		this.yzythPacksLst = yzythPacksLst;
	}

	public List<ResponsePacksDetail> getDzPacksLst() {
		return dzPacksLst;
	}

	public void setDzPacksLst(List<ResponsePacksDetail> dzPacksLst) {
		this.dzPacksLst = dzPacksLst;
	}

	public List<ResponsePacksDetail> getHwggPacksLst() {
		return hwggPacksLst;
	}

	public void setHwggPacksLst(List<ResponsePacksDetail> hwggPacksLst) {
		this.hwggPacksLst = hwggPacksLst;
	}

	public List<ResponsePacksDetail> getNkPacksLst() {
		return nkPacksLst;
	}

	public void setNkPacksLst(List<ResponsePacksDetail> nkPacksLst) {
		this.nkPacksLst = nkPacksLst;
	}

	public List<ResponsePacksDetail> getSyPacksLst() {
		return syPacksLst;
	}

	public void setSyPacksLst(List<ResponsePacksDetail> syPacksLst) {
		this.syPacksLst = syPacksLst;
	}
}
