package com.common.util;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDataFormatter;
import org.apache.poi.ss.usermodel.Cell;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串处理函数工具包
 *
 * <p>
 * Title: StringUtils
 * </p>
 * <p>
 * Description: -
 * </p>
 * <p>
 * Copyright: Copyright (c) 2008
 * </p>
 * <p>
 * Company:
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class StringUtils extends org.apache.commons.lang.StringUtils{
	public StringUtils() {
	}
	private static String nullStr = "";
	private static int PRECISION = 2;
	private static String[] chrChineseUnit = {"分","角","元","拾","佰","仟","万","拾","佰","仟","亿","拾","佰","仟","兆","拾","佰","仟"};
	private static String[] chrChineseNumber = {"零","壹","贰","叁","肆","伍","陆","柒","捌","玖"};
    public final static String[] QUARTER_TEXT = {"一", "二", "三", "四"};
	private static String chrChineseFull = "整";
	private static String chrChineseNegative = "负";
	private static char[] digits = {
		'0' , '1' , '2' , '3' , '4' , '5' ,
		'6' , '7' , '8' , '9' , 'a' , 'b' ,
		'c' , 'd' , 'e' , 'f' , 'g' , 'h' ,
		'i' , 'j' , 'k' , 'l' , 'm' , 'n' ,
		'o' , 'p' , 'q' , 'r' , 's' , 't' ,
		'u' , 'v' , 'w' , 'x' , 'y' , 'z' ,
		'A' , 'B' , 'C' , 'D' , 'E' , 'F' ,
		'G' , 'H' , 'I' , 'J' , 'K' , 'L' ,
		'M' , 'N' , 'O' , 'P' , 'Q' , 'R' ,
		'S' , 'T' , 'U' , 'V' , 'W' , 'X' ,
		'Y' , 'Z' , '@' , '*'
		};

	/**
	 * 长整形数据转64进制字符串
	 * @param number 需要转的数字
	 * @return 64进制字符串
	 */
	public static String to64(long number){
		char[] buf = new char[12];
		int charPos = 12;
		int radix = 1 << 6;
			long mask = radix - 1;
			do {
				buf[--charPos] = digits[(int)(number & mask)];
				number >>>= 6;
			} while (number != 0);
		return new String(buf, charPos, (12 - charPos));
	}

	/**
	 * Description 将数字金额转换为中文金额
	 *
	 * @param
	 * <p>
	 * BigDecimal bigdMoneyNumber 转换前的数字金额
	 * </P>
	 * @return String 调用：myToChineseCurrency("101.89")="壹佰零壹圆捌角玖分" myToChineseCurrency("100.89")="壹佰零捌角玖分" myToChineseCurrency("100")="壹佰圆整"
	 */

	public static String DoNumberCurrencyToChineseCurrency(BigDecimal bigdMoneyNumber) {
		// 中文金额缓存
		StringBuffer sb = new StringBuffer();
		// 获取符号
		int signum = bigdMoneyNumber.signum();
//        System.out.println("signum=" + signum);
		if (signum == 0) {
			return "零元整";
		}
//        System.out.println(bigdMoneyNumber.scale());

		// 转换金额为long,精确到分
		long number = bigdMoneyNumber.movePointRight(PRECISION).setScale(0,
				BigDecimal.ROUND_HALF_UP).abs().longValue();

		long scale = number % 100;

		int numUnit = 0;
		int numIndex = 0;
		// 遇零标志
		boolean getZero = false;
//        while((scale = scale % 10) == 0){
//            numIndex ++;
//            number = number / 10;
//            getZero = true;
//        }
		if (scale == 0) {
			numIndex = 2;
			number = number / 100;
			getZero = true;
		}
		if (scale != 0 && scale % 10 == 0) {
			numIndex = 1;
			number = number / 10;
			getZero = true;
		}
		int zeroSize = 0;
		while (number > 0) {
			numUnit = (int) (number % 10);
			if (numUnit > 0) {
				// 非零处理
				if(numIndex==9 && zeroSize>=3){
				sb.insert(0,chrChineseUnit[6]);
				}
				if(numIndex==13 && zeroSize>=3){
				sb.insert(0,chrChineseUnit[10]);
				}
				sb.insert(0, chrChineseUnit[numIndex]);
				sb.insert(0, chrChineseNumber[numUnit]);
				getZero = false;
				zeroSize = 0;
			} else {
				// 为零处理
				zeroSize++;
				if (!getZero)
					sb.insert(0, chrChineseNumber[numUnit]);

				if (numIndex == 2) {
					if (number > 0) {
						sb.insert(0, chrChineseUnit[numIndex]);
					}
				} else if ((numIndex - 2) % 4 == 0) {
					if (number % 1000 > 0) {
						sb.insert(0, chrChineseUnit[numIndex]);
					}
				}
				getZero = true;
			}

			// 自除10
			number = number / 10;
			numIndex++;

		}

		// 负数追加首字 负

		if (signum == -1) {
			sb.insert(0, chrChineseNegative);
		}
		// 整数追加尾字 整

		if (scale == 0) {
			sb.append(chrChineseFull);
		}

		return sb.toString();
	}


	/**
	 * 字符串的字符集类型转换
	 *
	 * @param src
	 *            需要转换的字符串
	 * @param fromCharSet
	 *            字符串当前的字符集类型，如"iso-8859-1","GBK"等
	 * @param toCharSet
	 *            目标字符集类型，如"iso-8859-1","GBK"等
	 * @return 转换后的字符串,失败返回null
	 */
	public static String charSetConvert(String src, String fromCharSet,
			String toCharSet) {
		if (src == null)
			return src;
		try {
			return new String(src.getBytes(fromCharSet), toCharSet);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 将iso8859的字符集转换成UTF-8字符集
	 *
	 * @param src
	 *            iso8859字符串
	 * @return 转化后的字符串,失败返回null
	 */
	public static String isoToUTF8(String src) {

//		return charSetConvert(src, "iso-8859-1", "UTF-8");
		return src;
	}

	/**
	 *
	 * @将iso8859的字符集转换成UTF-8字符集
	 * @param src
	 *            iso8859字符串,force 强制转换标志
	 * @return 转化后的字符串,失败返回null
	 */
	public static String isoToUTF8(String src, boolean force) {

		if (force) {
			return charSetConvert(src, "iso-8859-1", "UTF-8");
		} else {
			return isoToUTF8(src);
		}
	}

	/**
	 * 将iso8859的字符集转换成GBK字符集
	 *
	 * @param src
	 *            iso8859字符串
	 * @return 转化后的字符串,失败返回null
	 */
	public static String isoToGBK(String src) {
		return charSetConvert(src, "iso-8859-1", "GBK");
	}

	/**
	 * 将GBK的字符集转换成iso8859字符集
	 *
	 * @param src
	 *            GBK字符串
	 * @return 转化后的字符串,失败返回null
	 */
	public static String gbkToISO(String src) {
		return charSetConvert(src, "GBK", "iso-8859-1");
	}

	/**
	 * 字符替换函数 <br>
	 * 比如 String a = "ccddaa"; replace(a,"dd","xx") 将返回 ccxxaa
	 *
	 * @param str
	 *            需要替换的原始字符串
	 * @param pattern
	 *            需要被替换掉的字符串
	 * @param replace
	 *            希望被替换成的字符串
	 * @return 返回替换后的字符串
	 */
	public static String replace(String str, String pattern, String replace) {
		int s = 0;
		int e = 0;
		StringBuffer result = new StringBuffer();
		while ((e = str.indexOf(pattern, s)) >= 0) {
			result.append(str.substring(s, e));
			result.append(replace);
			s = e + pattern.length();
		}
		result.append(str.substring(s));
		return result.toString();
	}

	/**
	 * 重复一个字符串 n 次，比如 abcabcabc
	 *
	 * @param str
	 *            需要重复的字符串
	 * @param repeat
	 *            重复的次数
	 * @return 重复后生成的字符串
	 */
	public static String repeat(String str, int repeat) {
		StringBuffer buffer = new StringBuffer(repeat * str.length());
		for (int i = 0; i < repeat; i++) {
			buffer.append(str);
		}
		return buffer.toString();
	}

	/**
	 * 获得一个字符串的最左边的n个字符，如果长度len的长度大于字符串的总长度，返回字符串本身
	 *
	 * @param str
	 *            原始的字符串
	 * @param len
	 *            左边的长度
	 * @return 最左边的字符
	 */
	public static String left(String str, int len) {
		if (len < 0) {
			throw new IllegalArgumentException("Requested String length " + len
					+ " is less than zero");
		}
		if ((str == null) || (str.length() <= len)) {
			return str;
		} else {
			return str.substring(0, len);
		}
	}

	/**
	 * 获得一个字符串的最右边的n个字符，如果长度len的长度大于字符串的总长度，返回字符串本身
	 *
	 * @param str
	 *            原始的字符串
	 * @param len
	 *            右边的长度
	 * @return 最右边的字符
	 */
	public static String right(String str, int len) {
		if (len < 0) {
			throw new IllegalArgumentException("Requested String length " + len
					+ " is less than zero");
		}
		if ((str == null) || (str.length() <= len)) {
			return str;
		} else {
			return str.substring(str.length() - len);
		}
	}

	/**
	 * 将给定的字符串格式化为定长字符串, 原始字符串长度超过给定长度的,按照给定长度从左到右截取 如果原始字符串小于给定长度,
	 * 则按照给定字符在左端补足空位
	 *
	 * @param src
	 *            原始字符串
	 * @param s2
	 *            补充用字符,
	 * @param length
	 *            格式化后长度
	 * @return 格式化后字符串
	 */
	public static String formatString(String src, char s2, int length) {
		String retValue = src;
		if (src == null || length <= 0) {
			return null;
		}

		if (src.length() > length) {
			retValue = src.substring(0, length);
		}

		for (int i = 0; i < length - src.length(); i++) {
			retValue = s2 + retValue;
		}

		return retValue;
	}

	/**
	 * 将一个浮点数转换为人民币的显示格式：￥##,###.##
	 *
	 * @param value
	 *            浮点数
	 * @return 人民币格式显示的数字
	 */
	public static String toRMB(double value) {
		NumberFormat nf = NumberFormat.getCurrencyInstance(Locale.CHINA);
		return nf.format(value);
	}

	/**
	 * 默认保留小数点后两位，将一个浮点数转换为定长小数位的小数 ######.##
	 *
	 * @param value
	 *            浮点数
	 * @return 定长小数位的小数
	 */
	public static String toCurrencyWithoutComma(double value) {
		String retValue = toCurrency(value);
		retValue = retValue.replaceAll(",", "");
		return retValue;
	}

	/**
	 * 默认保留小数点后两位，将一个浮点数转换为货币的显示格式：##,###.##
	 *
	 * @param value
	 *            浮点数
	 * @return 货币格式显示的数字
	 */
	public static String toCurrency(double value) {
		return toCurrency(value, 2);
	}

	/**
	 * 根据指定的小数位数，将一个浮点数转换为货币的显示格式
	 *
	 * @param value
	 *            浮点数
	 * @param decimalDigits
	 *            小数点后保留小数位数
	 * @return 货币格式显示的数字 <br>
	 *         <br>
	 *         例： toCurrency(123456.789,5) 将返回 "123,456.78900"
	 */
	public static String toCurrency(double value, int decimalDigits) {
		String format = "#,##0." + repeat("0", decimalDigits);
		NumberFormat nf = new DecimalFormat(format);
		return nf.format(value);

	}

	/**
	 * 将一个字符串格式化为给定的长度，过长的话按照给定的长度从字符串左边截取，反之以给定的 字符在字符串左边补足空余位 <br>
	 * 比如： <br>
	 * prefixStr("abc",'0',5) 将返回 00aaa <br>
	 * prefixStr("abc",'0',2) 将返回 ab
	 *
	 * @param source
	 *            原始字符串
	 * @param profix
	 *            补足空余位时使用的字符串
	 * @param length
	 *            格式化后字符串长度
	 * @return 返回格式化后的字符串,异常返回null
	 */
	public static String prefixStr(String source, char profix, int length) {
		String strRet = source;
		if (source == null) {
			return strRet;
		}
		if (source.length() >= length) {
			strRet = source.substring(0, length);
		}

		if (source.length() < length) {
			for (int i = 0; i < length - source.length(); i++) {
				strRet = "" + profix + strRet;
			}
		}

		return strRet;
	}

	/**
	 * 格式化字符串,将字符串trim()后返回. 如果字符串为null,则返回长度为零的字符串("")
	 *
	 * @param value
	 *            被格式化字符串
	 * @return 格式化后的字符串
	 */
	public static String stringTrim(String value) {
		if (value == null)
			return "";
		return value.trim();
	}

	/**
	 * 格式化字符串,将字符串trim()后返回. 如果字符串为null,则返回长度为零的字符串("")
	 *
	 * @param value
	 *            被格式化字符串
	 * @return 格式化后的字符串
	 */
	public static String stringNullRep(String value,String strReps) {
		if (value == null)
			return strReps;
		if("".equals(value.trim()))
			return strReps;
		return value.trim();
	}

	/**
	 * 格式化字符串,将字符串trim()后返回. 如果字符串为null,则返回长度为零的字符串("")
	 *
	 * @param value
	 *            被格式化字符串
	 * @return 格式化后的字符串
	 */
	public static String strObjTrim(Object value) {
		if (value == null)
			return "";
		return value.toString().trim();
	}

	/**
	 * 将一个字符串格式化为给定的长度，过长的话按照给定的长度从字符串左边截取，反之以给定的 字符在字符串右边补足空余位 <br>
	 * 比如： <br>
	 * suffixStr("abc",'0',5) 将返回 aaa00 <br>
	 * suffixStr("abc",'0',2) 将返回 ab
	 *
	 * @param source
	 *            原始字符串
	 * @param profix
	 *            补足空余位时使用的字符串
	 * @param length
	 *            格式化后字符串长度
	 * @return 返回格式化后的字符串,异常返回null
	 */
	public static String suffixStr(String source, char suffix, int length) {
		String strRet = source;
		if (source == null) {
			return strRet;
		}
		if (source.length() >= length) {
			strRet = source.substring(0, length);
		}

		if (source.length() < length) {
			for (int i = 0; i < length - source.length(); i++) {
				strRet += suffix;
			}
		}
		return strRet;
	}

	/**
	 * 根据分割符sp，将str分割成多个字符串，并将它们放入一个ArrayList并返回，其规则是最后的 字符串最后add到ArrayList中
	 *
	 * @param str
	 *            被分割的字符串
	 * @param sp
	 *            分割符字符串
	 * @return 封装好的ArrayList
	 * <AUTHOR> ： 友情提供
	 */
	public static List<String> convertStrToArrayList(String str, String sp) {
		List<String> al = new ArrayList<String>();
		if (str == null) {
			return al;
		}
		String strArr[] = str.split(sp);
		for (int i = 0; i < strArr.length; i++) {
			al.add(strArr[i]);
		}

		return al;
	}

	/**
	 * 将数字字符串转换为人民币大写
	 *
	 * @param value
	 *            金额人民币数字字符串
	 * @return 转换后的人民币大写字符串
	 * <AUTHOR> : 友情提供
	 */
	public static String changeToBig(String value) {

		if (null == value || "".equals(value.trim()))
			return "零";
		// add by leipan
		@SuppressWarnings("unused")
		int len = value.length();

		@SuppressWarnings("unused")
		String strCheck, strArr, strFen, strDW, strNum, strBig, strNow;
		double d = 0;
		try {
			d = Double.parseDouble(value);
		} catch (Exception e) {
			return "数据" + value + "非法！";
		}

		strCheck = value + ".";
		int dot = strCheck.indexOf(".");
		if (dot > 12) {
			return "数据" + value + "过大，无法处理！";
		}

		try {
			int i = 0;
			strBig = "";
			strDW = "";
			strNum = "";

			// long intFen = (long)(d*100); //原来的处理方法
			/**
			 * 增加了对double转换为long时的精度， 例如:(long)(208123.42 * 100) = 20812341
			 * 解决了该问题 add 庞学亮
			 */
			BigDecimal big = new BigDecimal(d);
			big = big.multiply(new BigDecimal(100)).setScale(2, 4);
			long intFen = big.longValue();

			strFen = String.valueOf(intFen);
			int lenIntFen = strFen.length();
			while (lenIntFen != 0) {
				i++;
				switch (i) {
				case 1:
					strDW = "分";
					break;
				case 2:
					strDW = "角";
					break;
				case 3:
					strDW = "元";
					break;
				case 4:
					strDW = "拾";
					break;
				case 5:
					strDW = "佰";
					break;
				case 6:
					strDW = "仟";
					break;
				case 7:
					strDW = "万";
					break;
				case 8:
					strDW = "拾";
					break;
				case 9:
					strDW = "佰";
					break;
				case 10:
					strDW = "仟";
					break;
				case 11:
					strDW = "亿";
					break;
				case 12:
					strDW = "拾";
					break;
				case 13:
					strDW = "佰";
					break;
				case 14:
					strDW = "仟";
					break;
				}
				switch (strFen.charAt(lenIntFen - 1)) { // 选择数字

				case '1':
					strNum = "壹";
					break;
				case '2':
					strNum = "贰";
					break;
				case '3':
					strNum = "叁";
					break;
				case '4':
					strNum = "肆";
					break;
				case '5':
					strNum = "伍";
					break;
				case '6':
					strNum = "陆";
					break;
				case '7':
					strNum = "柒";
					break;
				case '8':
					strNum = "捌";
					break;
				case '9':
					strNum = "玖";
					break;
				case '0':
					strNum = "零";
					break;
				}
				// 处理特殊情况
				strNow = strBig;
				// 分为零时的情况
				if ((i == 1) && (strFen.charAt(lenIntFen - 1) == '0'))
					strBig = "整";
				// 角为零时的情况
				else if ((i == 2) && (strFen.charAt(lenIntFen - 1) == '0')) { // 角分同时为零时的情况
					if (!strBig.equals("整"))
						strBig = "零" + strBig;
				}
				// 元为零的情况
				else if ((i == 3) && (strFen.charAt(lenIntFen - 1) == '0'))
					strBig = "元" + strBig;
				// 拾－仟中一位为零且其前一位（元以上）不为零的情况时补零
				else if ((i < 7) && (i > 3)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (!("" + strNow.charAt(0)).equals("零"))
						&& (!("" + strNow.charAt(0)).equals("元")))
					strBig = "零" + strBig;
				// 拾－仟中一位为零且其前一位（元以上）也为零的情况时跨过
				else if ((i < 7) && (i > 3)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("零"))) {
				}
				// 拾－仟中一位为零且其前一位是元且为零的情况时跨过
				else if ((i < 7) && (i > 3)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("元"))) {
				}
				// 当万为零时必须补上万字
				// modified by leipan
				else if ((i == 7) && (strFen.charAt(lenIntFen - 1) == '0'))
					strBig = "万" + strBig;
				// 拾万－仟万中一位为零且其前一位（万以上）不为零的情况时补零
				else if ((i < 11) && (i > 7)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (!("" + strNow.charAt(0)).equals("零"))
						&& (!("" + strNow.charAt(0)).equals("万")))
					strBig = "零" + strBig;
				// 拾万－仟万中一位为零且其前一位（万以上）也为零的情况时跨过
				else if ((i < 11) && (i > 7)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("万"))) {
				}
				// 拾万－仟万中一位为零且其前一位为万位且为零的情况时跨过
				else if ((i < 11) && (i > 7)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("零"))) {
				}
				// 万位为零且存在仟位和十万以上时，在万仟间补零
				else if ((i < 11) && (i > 8)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("万"))
						&& (("" + strNow.charAt(2)).equals("仟")))
					strBig = strNum + strDW + "万零"
							+ strBig.substring(1, strBig.length());
				// 单独处理亿位
				else if (i == 11) {
					// 亿位为零且万全为零存在仟位时，去掉万补为零
					if ((strFen.charAt(lenIntFen - 1) == '0')
							&& (("" + strNow.charAt(0)).equals("万"))
							&& (("" + strNow.charAt(2)).equals("仟")))
						strBig = "亿" + "零"
								+ strBig.substring(1, strBig.length());
					// 亿位为零且万全为零不存在仟位时，去掉万
					else if ((strFen.charAt(lenIntFen - 1) == '0')
							&& (("" + strNow.charAt(0)).equals("万"))
							&& (("" + strNow.charAt(2)).equals("仟")))
						strBig = "亿" + strBig.substring(1, strBig.length());
					// 亿位不为零且万全为零存在仟位时，去掉万补为零
					else if ((("" + strNow.charAt(0)).equals("万"))
							&& (("" + strNow.charAt(2)).equals("仟")))
						strBig = strNum + strDW + "零"
								+ strBig.substring(1, strBig.length());
					// 亿位不为零且万全为零不存在仟位时，去掉万
					else if ((("" + strNow.charAt(0)).equals("万"))
							&& (("" + strNow.charAt(2)).equals("仟")))
						strBig = strNum + strDW
								+ strBig.substring(1, strBig.length());
					// 其他正常情况
					else {
						if (("" + strBig.charAt(0)).equals("万")) {
							strBig = strNum + strDW
									+ strBig.substring(1, strBig.length())
									+ " ";
						} else {
							strBig = strNum + strDW + strBig;
						}
					}

				} // 拾亿－仟亿中一位为零且其前一位（亿以上）不为零的情况时补零
				else if ((i < 15) && (i > 11)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("零"))
						&& (!("" + strNow.charAt(0)).equals("亿")))
					strBig = "零" + strBig;
				// 拾亿－仟亿中一位为零且其前一位（亿以上）也为零的情况时跨过
				else if ((i < 15) && (i > 11)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("亿"))) {
				}
				// 拾亿－仟亿中一位为零且其前一位为亿位且为零的情况时跨过
				else if ((i < 15) && (i > 11)
						&& (strFen.charAt(lenIntFen - 1) == '0')
						&& (("" + strNow.charAt(0)).equals("零"))) {
				}
				// 亿位为零且不存在仟万位和十亿以上时去掉上次写入的零
				else if ((i < 15) && (i > 11)
						&& (strFen.charAt(lenIntFen - 1) != '0')
						&& (("" + strNow.charAt(0)).equals("零"))
						&& (("" + strNow.charAt(1)).equals("亿"))
						&& (!("" + strNow.charAt(3)).equals("仟")))
					strBig = strNum + strDW
							+ strBig.substring(1, strBig.length());
				// 亿位为零且存在仟万位和十亿以上时，在亿仟万间补零
				else if ((i < 15) && (i > 11)
						&& (strFen.charAt(lenIntFen - 1) != '0')
						&& (("" + strNow.charAt(0)).equals("零"))
						&& (("" + strNow.charAt(1)).equals("亿"))
						&& (("" + strNow.charAt(3)).equals("仟")))
					strBig = strNum + strDW + "亿零"
							+ strBig.substring(2, strBig.length());
				else
					strBig = strNum + strDW + strBig;
				strFen = strFen.substring(0, lenIntFen - 1);
				lenIntFen--;
			}
			strBig = isoToUTF8(strBig);
			return strBig;
		} catch (Exception e) {
			return "";
		}
	}

	// add by kfr for date
	public static String changeToBigdate(String value) {

		if (null == value || "".equals(value.trim())) {
			return " ";
		}

		String strDW, strNum, strNum1;

		strDW = "";
		strNum = "";
		strNum1 = "";

		for (int i = 0; i < value.length(); i++) {
			if (value.length() == 2) {
				if (i == 0) {
					switch (value.substring(0, 1).charAt(i)) {
					case '1':
						strNum = "十";
						break;
					case '2':
						strNum = "二十";
						break;
					case '3':
						strNum = "三十";
						break;
					case '0':
						strNum = " ";
						break;

					}
					switch (value.charAt(1)) { // 选择数字
					case '1':
						strNum1 = "一";
						break;
					case '2':
						strNum1 = "二";
						break;
					case '3':
						strNum1 = "三";
						break;
					case '4':
						strNum1 = "四";
						break;
					case '5':
						strNum1 = "五";
						break;
					case '6':
						strNum1 = "六";
						break;
					case '7':
						strNum1 = "七";
						break;
					case '8':
						strNum1 = "八";
						break;
					case '9':
						strNum1 = "九";
						break;
					case '0':
						strNum1 = " ";
						break;
					}

					strDW = strNum + strNum1;
				}
			}

			else {
				if (i == 0 && value.startsWith("0")) {
					strNum = "";
				} else {
					switch (value.charAt(i)) { // 选择数字
					case '1':
						strNum = "一";
						break;
					case '2':
						strNum = "二";
						break;
					case '3':
						strNum = "三";
						break;
					case '4':
						strNum = "四";
						break;
					case '5':
						strNum = "五";
						break;
					case '6':
						strNum = "六";
						break;
					case '7':
						strNum = "七";
						break;
					case '8':
						strNum = "八";
						break;
					case '9':
						strNum = "九";
						break;
					case '0':
						strNum = "零";
						break;
					}
				}
				strDW = strDW + strNum;
			}
		}

		return strDW;
	}
	/**
	 *
	 * @name   根据数据库的列字段名将其按Vo定义格式转换为VO中属性定义
	 * @        convertDbColToMapKey
	 * @Description 相关说明
	 * @time    2008-2-3下午03:24:32
	 * @param dbCol 数据库的字段
	 * @return String BO定义格式属性
	 * @throws  Exception 异常:
	 * <AUTHOR> leipan
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 */
	public static String convertDbColToMapKey(String dbCol) {
		String mapKey = "";
		String str = dbCol.toLowerCase();
		String[] ss = str.split("_");
		StringBuffer key =new StringBuffer();
		if(ss.length>1){
		key.append(ss[0]);
			for(int j=1;j<ss.length;j++){
				key.append(ss[j].substring(0,1).toLowerCase()).append(ss[j].substring(1));
			}
		}else{
			key.append(str);
		}
		mapKey = key.toString();
		return mapKey;
	}
	/**
	 * 转换字符串
	 * 为字符串转换为Integer、Double、Long等对象去空格，如果为null或者“”转换为“0”返回
	 * trim4Number <br>
	 * @param str
	 * @return String
	 */
	public static String trim4Number(String str) {
		str = stringTrim(str);
		if("".equals(str)) {
			return "0";
		}
		return str;
	}
	/**
	 *
	 *@name    String 转换成 BigDecimal
	 *@Description 相关说明
	 *@Time    创建时间:Apr 25, 20084:35:05 PM
	 *@param s
	 *@return
	 *@throws Exception
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 * 冯静衠
	 */
	public static BigDecimal String2BigDecimal(String s)  {
		if (null!=s ) {
			try{
			return new BigDecimal(s);
			}catch(Exception ex ){
				ex.printStackTrace();
			}
		}

		return null;
	}
	/**
	 *
	 *@name BigDecimal    转换成  String
	 *@Description 相关说明
	 *@Time    创建时间:Apr 25, 20084:35:05 PM
	 *@param s
	 *@return
	 *@throws Exception
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 * 冯静衠
	 */
	public static String BigDecimal2String(BigDecimal b) {
		return BigDecimal2String(b,2);
	}
	/**
	 *
	 *@name BigDecimal    转换成  String
	 *@Description 相关说明
	 *@Time    创建时间:Apr 25, 20084:35:05 PM
	 *@param s
	 *@return
	 *@throws Exception
	 * @history 修订历史（历次修订内容、修订人、修订时间等）
	 * 冯静衠
	 */
	public static String BigDecimal2String(BigDecimal b,int scale) {
		if (b == null) {
			return null;
		} else {
			return b.setScale(scale,BigDecimal.ROUND_HALF_UP).toString();
		}
	}


	/**
	 * 转换URL，将形如opencx.action?flash1=xx?flash2=xx?flash3=xx
	 * 的URL转换成 opencx.action?flash1=xx&flash2=xx&flash3=xx 形式的URL
	 * @param targetUrl 需要转换的URL
	 * @return String 转换后的URL
	 */
	public static String convertURL(String targetUrl)
	{
		//如果目标URL为空或者不包括?号。直接将原URL返回
		if (org.apache.commons.lang.StringUtils.isEmpty(targetUrl) || targetUrl.indexOf("?") < 0)
		{
			return targetUrl;
		}

		int index = targetUrl.indexOf("?") + 1;
		String first = targetUrl.substring(0, index);
		String last = targetUrl.substring(index);
		last = last.replace("?", "&");

		return first + last;


	}



	/**
	 * 字符串分割
	 * @param oldString 需要分割的字符串
	 * @param splitstr 分隔符
	 * @return 返回分割后的数组
	 */
	public static String[] splitString(String oldString,String splitstr){
		//定义返回对象
		String[] newString = null;
		//截取对象成数组
		newString = oldString.split(splitstr);
		//返回对象
		return newString;
	}


	/**
	 * 字符串分割
	 * @param oldString 需要分割的字符串
	 * @param splitstr 分隔符
	 * @return 返回分割后的数组
	 */
	public static List<String> splitStringToList(String oldString,String splitstr){
		//定义返回对象
		String[] newString = null;
		//截取对象成数组
		newString = oldString.split(splitstr);
		//数组转成list
		List<String> list = new ArrayList<String>(Arrays.asList(newString));
		//返回对象
		return list;
	}

	/**
	 * 字符串截取
	 * @param oldString 需要截取的字符串
	 * @param beginIndex 起始位置
	 * @param endIndex 结束位置
	 * @param splitstr 分割字符串
	 * @param substrtype 截取类型：begin:只截取前段部分;beginend:截图区间部分，由用户定义;beginlen:截图区间部分，从开始位置直接截取到末尾
	 * @return 返回截取后的字符串
	 */
	public static String substrString(String oldString,int beginIndex,int endIndex,String substrtype){
		//定义返回对象
		String newString = null;
		//只截取前段部分
		if("begin".equals(substrtype)){
			//截取
			newString = oldString.substring(beginIndex);
		}
		//截图区间部分，由用户定义
		else if("beginend".equals(substrtype)){
			//截取
			newString = oldString.substring(beginIndex, endIndex);
		}
		//截图区间部分，从开始位置直接截取到末尾
		else if("beginlen".equals(substrtype)){
			//截取
			newString = oldString.substring(beginIndex, oldString.length());
		}
		//返回截取对象
		return newString;
	}

	/**
	 * 获取截取字符串所在位置
	 * @param oldString 需要截取的字符串
	 * @param splitstr 截取字符
	 * @param isEnd 是否从后往前截取，true:代表从后往前截取，false：从前往后截取
	 * @return 返回截取字符串所在位置
	 */
	public static int indexofString(String oldString,String splitstr,boolean isEnd){
		//定义返回变量
		int splitnum = 0;
		//判断是否从后往前截取，true:代表从后往前截取，false：从前往后截取
		if(isEnd){
			//从后往前截取
			splitnum = oldString.lastIndexOf(splitstr);
		}else{
			//从前往后截取
			splitnum = oldString.indexOf(splitstr);
		}
		//返回截取位置
		return splitnum;
	}

	/**
	 * list比较类
	 * @param list1 list1对象
	 * @param list2 list2对象
	 * @return
	 */
	public static List<String> list1ComparisonList2(List<String> list1,List<String> list2){
//		List<String> newlist = new ArrayList<String>();
		List<String> newlist = new ArrayList<String>(Arrays.asList(new String[list1.size()]));
		//对象赋值
		Collections.copy(newlist, list1);
//		newlist = newlist.c;
		//进行比对
		newlist.removeAll(list2);
		//返回对象
		return newlist;
	}
	public static boolean isNull(String str){
		str = StringUtils.stringTrim(str);
		if(nullStr.equals(str)){
			return true;
		}
		return false;
	}
	/**
	 * //不考虑元素顺序是否相同 时的LIST比较相同的部分
	 * @param list1 对象1
	 * @param list2 对象2
	 * @return 比较出相同部分对象
	 */
	public static List<String> getSameElementList(List<String> list1,List<String> list2){
		List<String> sameElementList = new ArrayList<String>();
		for(String tem : list1){
			if(list2.contains(tem)){//包含tem元素
				if(sameElementList == null){
				sameElementList = new ArrayList<String>();
				}
				sameElementList.add(tem);
			}
		}
		return sameElementList ;
	}
	public  static String getSeqNextval(){
		Random random = new Random();
		return System.currentTimeMillis()+""+random.nextInt(999999);
	}

	public static String getFileSuffixName(String fileName){
		return fileName.substring(fileName.lastIndexOf(".")+1);
	}
	/**
	 * 将Unicode字符串转换成中文
	 * @param ori
	 * @return
	 */
	public static String convertUnicode(String ori){
		char aChar;
		int len = ori.length();
		StringBuffer outBuffer = new StringBuffer(len);
		for (int x = 0; x < len;) {
			aChar = ori.charAt(x++);
			if (aChar == '\\') {
				aChar = ori.charAt(x++);
				if (aChar == 'u') {
					// Read the xxxx
					int value = 0;
					for (int i = 0; i < 4; i++) {
						aChar = ori.charAt(x++);
						switch (aChar) {
						case '0':
						case '1':
						case '2':
						case '3':
						case '4':
						case '5':
						case '6':
						case '7':
						case '8':
						case '9':
							value = (value << 4) + aChar - '0';
							break;
						case 'a':
						case 'b':
						case 'c':
						case 'd':
						case 'e':
						case 'f':
							value = (value << 4) + 10 + aChar - 'a';
							break;
						case 'A':
						case 'B':
						case 'C':
						case 'D':
						case 'E':
						case 'F':
							value = (value << 4) + 10 + aChar - 'A';
							break;
						default:
							throw new IllegalArgumentException(
									"Malformed   \\uxxxx   encoding.");
						}
					}
					outBuffer.append((char) value);
				} else {
					if (aChar == 't')
						aChar = '\t';
					else if (aChar == 'r')
						aChar = '\r';
					else if (aChar == 'n')
						aChar = '\n';
					else if (aChar == 'f')
						aChar = '\f';
					outBuffer.append(aChar);
				}
			} else
				outBuffer.append(aChar);

		}
		return outBuffer.toString();
	}

	public static String convertStreamToString(InputStream is) throws Exception {
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		StringBuilder sb = new StringBuilder();

		String line = null;
		try {
			while ((line = reader.readLine()) != null) {
				sb.append(line);
			}
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return sb.toString();
	}

	public static final char UNDERLINE = '_';

	public static String camelToUnderline(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if (Character.isUpperCase(c)) {
				sb.append(UNDERLINE);
				sb.append(Character.toLowerCase(c));
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	/**
	 * 带有下划线的字符串转为驼峰,字符串支持小写及大写
	 * @param param
	 * @return
	 */
	public static String underlineToCamel(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if (c == UNDERLINE) {
				if (++i < len) {
					sb.append(Character.toUpperCase(param.charAt(i)));
				}
			} else {
				sb.append(Character.toLowerCase(c));
			}
		}
		return sb.toString();
	}

	/**
	 *
	 * 去掉字符串的第一个和最后一个字符并去掉空格。
	 *
	 * @param resource
	 * @return 截取后的字符串
	 */
	public static String cutFirstAndLastChar(String resource)
	{
		if (StringUtils.isEmpty(resource))
		{
			return resource;
		}

		return resource.trim().substring(1, resource.length() - 1);
	}

	/**
	 * toString 工具方法
	 *
	 * @param entity
	 *            需要toString的实体类
	 * @param fieldName
	 *            不需要toString的实体属性名
	 * @return 实体对象String
	 */
	@SuppressWarnings("unchecked")
	public static String toString(Object entity, String... fieldNames)
	{
		Class cls = entity.getClass();
		StringBuffer buffer = new StringBuffer(cls.getSimpleName() + ":[");

		try
		{
			// 不获取私有方法
			Method[] methods = cls.getMethods();
			// 得到所有field
			Field[] fields = cls.getDeclaredFields();

			List<Field> fieldList = new ArrayList<Field>();
			for (Field field : fields)
			{
				fieldList.add(field);
			}

			//如果存在不需要打印的属性
			if (null != fieldNames && 0 != fieldNames.length)
			{
				//删除不需要打印的属性
				for(Field field : fieldList)
				{
					for(String fieldName : fieldNames)
					{
						if (fieldName.equals(field.getName()))
						{
							fieldList.remove(field);
						}
					}
				}
			}

			for (Method method : methods)
			{
				String mn = method.getName();
				for (Field field : fieldList)
				{
					String name = field.getName().toString();
					if (mn.equalsIgnoreCase("get" + name))
					{
						Object obj = method.invoke(entity);
						String value = null == obj ? null : obj.toString();

						buffer.append(name + ":" + value + ", ");
					}
				}
			}

			buffer.deleteCharAt(buffer.length()-2).append("]");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

		return buffer.toString();
	}

	public static String getCellStringValue(Cell cell){
		String str = "";
		try{
			if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC){
				str = (long)cell.getNumericCellValue() + "";
			}else if(cell.getCellType() == Cell.CELL_TYPE_STRING){
				str = cell.getStringCellValue();
			}else
			{
				str = cell.getStringCellValue();
			}
		}catch(Exception e){
			str = "";
		}
		return str;
	}

	public static String getCellStringValue2(Cell cell){
		String str = "";
		try{
			if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC){
				str = (double)cell.getNumericCellValue() + "";
			}else if(cell.getCellType() == Cell.CELL_TYPE_STRING){
				str = cell.getStringCellValue();
			}else
			{
				str = cell.getStringCellValue();
			}
		}catch(Exception e){
			str = "";
		}
		return str;
	}
	public static String getCellStringValue3(Cell cell){
		String str = "";
		try{
			if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC){
				str = cell.getStringCellValue();
			}else if(cell.getCellType() == Cell.CELL_TYPE_STRING){
				str = cell.getStringCellValue();
			}else
			{
				str = cell.getStringCellValue();
			}
		}catch(Exception e){
			str = "";
		}
		return str;
	}

	public static String getCellStringValueForDouble(Cell cell)
	{
		String str = "";
		switch (cell.getCellType()) {

		case HSSFCell.CELL_TYPE_STRING:
			str = cell.getStringCellValue();
		break;

		case HSSFCell.CELL_TYPE_FORMULA:
			str = cell.getCellFormula();
		break;

		case HSSFCell.CELL_TYPE_NUMERIC:
			HSSFDataFormatter dataFormatter = new HSSFDataFormatter();
			String cellFormatted = dataFormatter.formatCellValue(cell);
			str = cellFormatted;
			break;

		case HSSFCell.CELL_TYPE_ERROR:
			str= cell.getStringCellValue();
		break;

		}
		return str;
	}

	public static boolean checkStringNoChinese(String str){
		int len = str.length();
		for(int i=0;i<len;i++){
			int c = str.charAt(i);
			if(c > 127){
				return false;
			}
		}
		return true;
	}

	/**
	 * 存放国标一级汉字不同读音的起始区位码
	 */
	static final int[] secPosValueList = { 1601, 1637, 1833, 2078, 2274, 2302,
			2433, 2594, 2787, 3106, 3212, 3472, 3635, 3722, 3730, 3858, 4027,
			4086, 4390, 4558, 4684, 4925, 5249, 5600 };

	/**
	 * 存放国标一级汉字不同读音的起始区位码对应读音
	 */
	static final char[] firstLetter = { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h',
			'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'w', 'x',
			'y', 'z' };

	/**
	 * 去除字符串中的空格、回车、换行符、制表符
	 *
	 * @param str
	 *            源字符串
	 * @return 处理后的字符串
	 */
	public static String replaceBlank(String str) {
		Pattern p = Pattern.compile("\\s*|\t|\r|\n");
		Matcher m = p.matcher(str);
		String after = m.replaceAll("");
		return after;
	}

	/**
	 * 得到c在s中的出现的索引列表
	 *
	 * @param s
	 *            原字符串
	 * @param c
	 *            子字符串
	 * @return c在s中出现的索引列表
	 */
	public static List<String> getIndexList(String s, String c) {
		int x = s.indexOf(c);
		int replaceLenght = 0;
		List<String> list = new ArrayList<String>();
		while (x != -1) {
			list.add(x + "");
			s = s.replaceFirst(c, "");
			replaceLenght = replaceLenght + c.length();
			x = s.indexOf(c);
			if (x != -1) {
				x = s.indexOf(c) + replaceLenght;
			}
		}
		return list;
	}

	/**
	 * 判断是否为数字（包括小数和整数）
	 *
	 * @param str
	 *            要判断的字符串
	 * @return true/flase（是/否）
	 */
	public static boolean isNumeric(String str) {
		Pattern pattern = Pattern.compile("-?[0-9]*\\.?[0-9]*");
		Matcher isNum = pattern.matcher(str);
		if (!isNum.matches()) {
			return false;
		}
		return true;
	}

	/**
	 * 获取一个字符串的拼音码
	 *
	 * @param oriStr
	 *            要操作的字符串
	 * @return 拼音码
	 */
	public static String getFirstLetter(String oriStr) {
		String str = oriStr.toLowerCase();
		StringBuffer buffer = new StringBuffer();
		char ch;
		char[] temp;
		for (int i = 0; i < str.length(); i++) {
			// 依次处理str中每个字符
			ch = str.charAt(i);
			temp = new char[] { ch };
			byte[] uniCode = new String(temp).getBytes();
			if (uniCode[0] < 128 && uniCode[0] > 0) {
				// 非汉字
				buffer.append(temp);
			} else {
				buffer.append(convert(uniCode));
			}
		}
		return buffer.toString();
	}

	/**
	 * 获取一个汉字的拼音首字母
	 *
	 * @param bytes
	 *            要操作的字符串
	 * @return 拼音首字母
	 */
	public static char convert(byte[] bytes) {
		char result = '-';
		int secPosValue = 0;
		int i;
		for (i = 0; i < bytes.length; i++) {
			bytes[i] -= 160;
		}
		secPosValue = bytes[0] * 100 + bytes[1];
		for (i = 0; i < 23; i++) {
			if (secPosValue >= secPosValueList[i]
					&& secPosValue < secPosValueList[i + 1]) {
				result = firstLetter[i];
				break;
			}
		}
		return result;
	}

	/**
	 * 比较两个字符串的大小,按拼音顺序
	 *
	 * @param str1
	 *            要操作的字符串
	 * @param str2
	 *            要操作的字符串
	 * @return -1:表示str1<str2 ; 1:表示str1>str2 ;0:表示str1=str2
	 */
	public static int compareString(String str1, String str2) {
		int m = str1.length();
		int n = str2.length();
		for (int i = 0; i < m; i++) {
			if (i < n) {
				if (str1.charAt(i) > str2.charAt(i)) {
					return 1;
				} else if (str1.charAt(i) == str2.charAt(i)) {
					if (m == n && i + 1 == m) {
						return 0;
					} else {
						continue;
					}
				} else {
					return -1;
				}
			} else {
				return 1;
			}
		}
		return -1;
	}

	/**
	 * 替换字符串
	 *
	 * @param resource
	 *            要操作的字符串
	 * @param target
	 *            要替换的目标子串
	 * @param result
	 *            用来替换目标子串的字符串
	 * @return 替换后的字符串
	 */
	public static String replaceAllStr(String resource, String target,
			String result) {
		resource = resource.replaceAll(target, result);
		return resource;
	}

	/**
	 * 将object 转为 string value并去空格 若object为null返回空字串
	 *
	 * @param value
	 * @return 转换后的字符串
	 */
	public static String getString(Object value) {
		if (value == null) {
			return "";
		}
		return String.valueOf(value).trim();
	}

	/**
	 * 将字符串转为整形值
	 *
	 * @param value
	 * @return 转换后的int数字
	 */
	public static int parseStringToInt(String value) {
		try {
			if (value == null || value.trim().equals("")) {
				return 0;
			}
			return Integer.parseInt(value);
		} catch (Exception ex) {
			return 0;
		}
	}

	/**
	 * 根据分割符','，将输入字符串转换为String数组
	 *
	 * @param value
	 * @return
	 */

	public static String arrayToCSV(String[] value) {
		return arrayToDelimited(value, ",", true, true);
	}

	/**
	 * 根据分割符，将输入字符串转换为String数组,以','分割
	 *
	 * @param value
	 * @return
	 */

	public static String arrayToDelimited(Object[] value) {
		return arrayToDelimited(value, ",");
	}

	/**
	 * 根据分割符，将输入字符串转换为String数组
	 *
	 * @param value
	 * @param delimiter
	 * @return
	 */

	public static String arrayToDelimited(Object[] value, String delimiter) {
		return arrayToDelimited(value, delimiter, false, false, false);
	}

	/**
	 * 根据分割符，将输入字符串转换为String数组
	 *
	 * @param value
	 * @param delimiter
	 * @return
	 */

	public static String arrayToDelimited(String[] value, String delimiter) {
		return arrayToDelimited(value, delimiter, true, true);
	}

	/**
	 * 根据分割符，将输入字符串转换为String数组
	 *
	 * @param value
	 * @param delimiter
	 * @param prepend
	 * @param append
	 * @return
	 */
	public static String arrayToDelimited(String[] value, String delimiter,
			boolean prepend, boolean append) {
		return arrayToDelimited(value, delimiter, prepend, append, false);
	}

	/**
	 * 根据分割符，将输入字符串转换为String数组
	 *
	 * @param value
	 * @param delimiter
	 * @param prepend
	 * @param append
	 * @param eliminateDuplicates
	 * @return
	 */
	public static String arrayToDelimited(Object[] value, String delimiter,
			boolean prepend, boolean append, boolean eliminateDuplicates) {
		if (delimiter == null)
			delimiter = ",";
		String retVal = null;
		if (value != null) {
			StringBuffer buff = new StringBuffer();
			int length = value.length;
			if (length > 0) {
				if (prepend)
					buff.append(delimiter);
				boolean isDuplicateValue = false;
				buff.append(delimiter); // Always make sure the buff starts with
				// a delimiter for duplicate checking
				for (int i = 0; i < length; i++) {
					isDuplicateValue = (eliminateDuplicates ? (buff
							.indexOf(delimiter + value[i] + delimiter) != -1)
							: false);
					if (!isDuplicateValue) {
						buff.append(value[i]);
						if (i < length - 1)
							buff.append(delimiter);
					}
				}
				buff.deleteCharAt(0); // remove the delimiter added for checking
				// duplicates
				// If the last value is a duplicate value, remove the delimiter
				// added to the end of the string
				if (isDuplicateValue) {
					buff.deleteCharAt(buff.length() - 1);
				}
				if (append)
					buff.append(delimiter);
			}
			retVal = buff.toString();
		}
		return retVal;
	}

	/**
	 * 根据数组返回String
	 *
	 * @param array
	 *            数组
	 * @return
	 */
	public static String transformStringByArray(String array[]) {
		StringBuffer buff = new StringBuffer();
		if (array != null && array.length > 0) {
			for (int i = 0; i < array.length; i++) {
				if (i == 0) {
					buff.append(array[i]);
				} else {
					buff.append(",");
					buff.append(array[i]);
				}
			}
		}
		return buff.toString();
	}

	/**
	 * 根据对象返回String
	 *
	 * @param obj
	 * @return
	 * @throws InvocationTargetException
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 */
	public static String transformStringObject(Object obj) throws Exception {
		StringBuffer buff = new StringBuffer();
		Class cls = obj.getClass();
		Field[] fields = cls.getDeclaredFields();
		for (int i = 0; i < fields.length; i++) {
			Field field = fields[i];
			String fieldName = field.getName();
			String firstLetter = fieldName.substring(0, 1).toUpperCase();
			String getMethodName = "get" + firstLetter + fieldName.substring(1);
			Method getMethod = cls.getMethod(getMethodName, new Class[] {});
			Object value = getMethod.invoke(obj, new Object[] {});
			buff.append(fieldName);
			buff.append(":");
			if (value instanceof Object[]) {
				buff.append(transformStringByArray((String[]) value));
			} else {
				buff.append(value);
			}
			buff.append("\n");
		}
		return buff.toString();
	}

	/**
	 * ajax传来中文都会 encodeURI ，这里进行解码操作
	 * @param str
	 * @return
	 * <AUTHOR>
	 * @time 2014年6月18日 下午4:10:14
	 */
	public static String decode(String str){
		if(isBlank(str)){
			return "";
		}
		String s = new String();
		try {
			s = URLDecoder.decode(str,"utf-8");
		} catch (UnsupportedEncodingException e) {

			e.printStackTrace();
		}
		return s;
	}

	/**
	 *
	 * <AUTHOR> 2016-9-7 下午3:02:32
	 * @param number  生成几位数的随机数
	 * @return
	 */
	public static String generateRandomNumber(int number) {
		Random random = new Random();
		String result = "";
		for (int i = 0; i < number; i++) {
			result += random.nextInt(10);
		}
		return result;
	}

	/**
	 * 类似于oracle数据库中的nvl函数
	 * @param source
	 * @param target
	 * @return
	 */
	public static String nvl(String source, String target){
		if (source == null || "".equals(source.trim())){
			return target;
		} else {
			return source;
		}
	}

	/**
	 * 除去String字符串里面指定的字符串
	 * @param str
	 * @param indexStr
	 * @return
	 */
	public static String trimStr(String str, String indexStr){
		if(str == null){
			return null;
		}
		StringBuilder newStr = new StringBuilder(str);
		if(newStr.indexOf(indexStr) == 0){
			newStr = new StringBuilder(newStr.substring(indexStr.length()));

		}else if(newStr.indexOf(indexStr) == newStr.length() - indexStr.length()){
			newStr = new StringBuilder(newStr.substring(0,newStr.lastIndexOf(indexStr)));

		}else if(newStr.indexOf(indexStr) < (newStr.length() - indexStr.length())){
			newStr =  new StringBuilder(newStr.substring(0,newStr.indexOf(indexStr))+newStr.substring(newStr.indexOf(indexStr)+indexStr.length(),newStr.length()));

		}
		return newStr.toString();
	}
	
	/**
	 * 打印SQL
	 * @param sql
	 * @param params
	 * @return
	 */
	public static String printSql(String sql, Object[] params) {
		if(params != null) {
			for(Object param : params) {
				try {
					sql = sql.replaceFirst("\\?", param == null ? "null" : ("'" + param.toString() + "'"));
				} catch (Exception e) {
				}
			}
		}
		return sql;
	}
}
