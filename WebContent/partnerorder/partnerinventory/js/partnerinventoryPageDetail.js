var grid, store, data;
//创建模糊查询列表框
$(document).ready(function(){
	bindBackspaceUrl('partnerorder/partnerinventory/partnerinventoryPage.jsp?cacheParams=true');
	queryPartnerInventory();
	initProductDictItem();
	
	$('#gKeyWord').bind('input propertychange', function() {  
		queryPartnerInventory();
	});
	$('#grid').on('keypress', function(e){
		if(e.keyCode == 13 && $(e.target).hasClass('inventory-input')){
			var $input = $('.inventory-input');
			$($input[$input.index(e.target) + 1]).focus();
		}
	});
});
function initProductDictItem() {
	common.rpcClient.call('productServiceImpl.queryProductDictItem',[],
			function (result) {
				if (result.code = "success") {
					var viscosityList = result.viscosity;
					var viscosityHtml = '';
					for(var i = 0; i < viscosityList.length; i++){
						var viscosity = viscosityList[i];
						viscosityHtml = viscosityHtml +'<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="viscosity" value="' + 
						viscosity.dicItemCode + '">' + viscosity.dicItemName + '</label>';
					}
					$("#viscosityDiv").html(viscosityHtml);
					
					var oiltypeList = result.oiltype;
					var oiltypeHtml = '';
					for(var i = 0; i < oiltypeList.length; i++){
						var oiltype = oiltypeList[i];
						oiltypeHtml = oiltypeHtml + '<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="oiltype" value="' + 
						oiltype.dicItemCode + '">' + oiltype.dicItemName + '</label>';
					}
					$("#oiltypeDiv").html(oiltypeHtml);
					
					var capacityList = result.capacity;
					var capacityHtml = '';
					for(var i = 0; i < capacityList.length; i++){
						var capacity = capacityList[i];
						capacityHtml = capacityHtml + '<label class="control-checkbox" style="margin-right:15px;">'+
						'<input type="checkbox" class="control-checkbox" style="vertical-align:middle;" name="capacity" value="' + 
						capacity.dicItemCode + '">' + capacity.dicItemName + '</label>';
					}
					$("#capacityDiv").html(capacityHtml);
					$("input[type='checkbox']").click(function() {  
						queryPartnerInventory();
					});
				} else {
					common.alertMes('加载地址失败','error');
				}
			},function(error){
				common.ajaxTimeout(error);
			});
}

/*function initGrid(){
	BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '序号', dataIndex: '', width: '60px',sortable: false, renderer: function(value, node, index){
				        	return index + 1;
				        }},
						{ title: 'SKU', dataIndex: 'sku', width: '30%' },
						{ title: '产品名称', dataIndex: 'productName', width: '70%' },
						{ title: '产品类别', dataIndex: 'category', width: '15%' },
						{ title: '粘度', dataIndex: 'viscosity', width: '8%' },
						{ title: '容量', dataIndex: 'capacity', width: '100px', elCls: 'text-right', renderer: function(value, item, index){
							if(value){
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },						
//						{ title: '零售价格(元)', dataIndex: 'salePrice', elCls: 'text-right', width: '35%'},
						{ title: '规格', dataIndex: 'units', elCls: 'text-right', width: '100px'},
						{
							title : '库存',
							dataIndex : 'quantity',
							elCls: 'text-right',
							width : '100px',
							sortable: false,
							renderer : function(value,obj,index){
								if(value){
									return '<input style="width:50px;">'+value+'</input>';
								}
								value = value || 0;
								return '<input type="number" name="points" min="1" max="10000" style="width:55px;" ' + 
								'value="'+value+'" class="text-right" onchange="updateInventoryCount(this, ' + index + ')"></input>';
				        	}
						}],
					store = new Store({
						data: [],
						autoLoad: true
					});

					grid = new Grid.Grid({
						render: '#grid',
						columns: columns,
						loadMask: false, // 加载数据时显示屏蔽层
						store: store,
//						itemStatusFields : {
//			    			selected : 'checked',
//			    			disabled : 'disable'
//			            },
						bbar: { width:'100%'}
					});	
					grid.render();
					common.gridAutoResize(grid);
					LoadMask.show();
					common.rpcClient.call('partnerInventoryService.queryInventoryForDetailPage', [{partnerId: partnerId}],
							function (result) {
						LoadMask.hide();
						if(result.success){
							data = result.data;
							if(data.length > 0){
								$('#headerTitle').text('合伙人库存管理 - ' + data[0].partnerName);
							}
							queryPartnerInventory();
						}else{
							data = [];
							grid.set('items', []);
							common.alertMes(result.errorMsg, 'error');
						}
						if(data.length == 0){
							$('#updateSubmitBtn').hide();
						}else{
							$('#updateSubmitBtn').show();
						}
					}, function (error) {
						LoadMask.hide();
						common.ajaxTimeout(error);
					});
			});

}*/
function clearSelectionPra() {
	$("input[type='checkbox']").attr("checked", false);
	queryPartnerInventory();
}
function queryPartnerInventory(){
	var productConditionQuery = new Object();
	
	var viscosityList = $("[name = viscosity]:checkbox")/*.attr("checked")*/;
	var viscositySelectList = new Array();	
	for(var i = 0 ; i < viscosityList.length ; i++){
		var viscosityCheck = viscosityList[i];
		if($(viscosityCheck).prop("checked")){
			viscositySelectList.push($(viscosityCheck).val());
		};
	}
	productConditionQuery.viscosityList = viscositySelectList;
	
	var oiltypeList = $("[name = oiltype]:checkbox")/*.attr("checked")*/;
	var oiltypeSelectList = new Array();	
	for(var i = 0 ; i < oiltypeList.length ; i++){
		var oiltypeCheck = oiltypeList[i];
		if($(oiltypeCheck).prop("checked")){
			oiltypeSelectList.push($(oiltypeCheck).val());
		};
	}
	productConditionQuery.oilTypeList = oiltypeSelectList;
	
	var capacityList = $("[name = capacity]:checkbox")/*.attr("checked")*/;
	var capacitySelectList = new Array();	
	for(var i = 0 ; i < capacityList.length ; i++){
		var capacityCheck = capacityList[i];
		if($(capacityCheck).prop("checked")){
			capacitySelectList.push($(capacityCheck).val());
		};
	}
	productConditionQuery.capacityList = capacitySelectList;
	productConditionQuery.partnerId = partnerId;
	
	listToJsonProductsByParam(productConditionQuery);
}
var productSelected = [];
var productSelectedBef = [];
function listToJsonProductsByParam( productConditionQuery) {
	LoadMask.show();
	common.rpcClient.call('partnerInventoryService.queryInventoryForDetailPage', [productConditionQuery],
		function (result) {
			if ("syserror" == result.code) {
				$("#grid").html('系统错误..');
			} else {
				$("#grid").html("");
				
				if(result.success){
					data = result.data;
					if(data.length > 0){
						$('#headerTitle').text('合伙人库存管理 - ' + data[0].partnerName);
					}
					productSelected = result.data;
					
					if (productSelectedBef.length == 0) {
						productSelectedBef = productSelected;
					}
					// 刷新临时保存数据列表
					else {
						for (var k = 0; k < productSelected.length; k++) {
							for (var i = 0; i < productSelectedBef.length; i++) {
								if (productSelected[k].sku == productSelectedBef[i].sku) {
									productSelected[k].quantity = productSelectedBef[i].quantity;
								}
							}
						}
					}
					
					listProductSelected(productSelected);
				}else{
					data = [];
					grid.set('items', []);
					common.alertMes(result.errorMsg, 'error');
				}
				if(data.length == 0){
					$('#updateSubmitBtn').hide();
				}else{
					$('#updateSubmitBtn').show();
				}
				LoadMask.hide();
			}
		}, function (error) {
			loadMask.hide();
			common.ajaxTimeout(error);
		});
}
function listProductSelected(selections) {
	if (selections == null) {
		$("#grid").html('没有选择任何产品');
	} else {
		$("#grid").html("");
		var tableData = selections;
		BUI.use(['bui/grid', 'bui/data'],
			function (Grid, Data) {
				var Grid = Grid,
					Store = Data.Store,
					columns = [
						{ title: '序号', dataIndex: '', width: '65px',sortable: false, renderer: function(value, node, index){
				        	return index + 1;
				        }},{ title: '产品编号', dataIndex: 'sku', width: '20%' },
						{ title: '产品信息', dataIndex: 'productName', width: '45%', renderer: function(value, item, index){
				        	var text = value;
				        	if(item.productPhoto){
				        		text = '<div class="photo-icon"><img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=5&attId=' + 
				        				item.productPhoto + '"/></div><span>' + value + '</span>';
				        	}
				        	return text;
						}},
						{ title: '规格', dataIndex: 'viscosity', width: '8%' },
						{ title: '升/瓶', dataIndex: 'capacity', width: '80px', elCls: 'text-right', renderer: function(value, item, index){
							if(value){
								if(value.indexOf('L') < 0){
									return value + 'L';
								}
								return value;
							}
							return "";
						} },
						{
							title : '库存',
							dataIndex : 'quantity',
							elCls: 'text-right',
							width : '100px',
							sortable: false,
							renderer : function(value,obj,index){
								/*if(value){
									return '<input style="width:50px;">'+value+'</input>';
								}*/
								value = value || 0;
								return '<input id="'+obj.sku+'"  min="1" max="10000" onfocus="this.select();" style="width:60px;height:25px;font-size:18px;padding-right:15px;" ' + 
								'value="'+value+'" class="text-right inventory-input" onchange="setProductInventoryCount(\''+obj+'\',\''+obj.sku+'\')"></input>';
				        	}
						}],
					data = tableData; // 显示的数据
					store = new Store({
						data: data,
						autoLoad: true
					}),

					editing = new Grid.Plugins.CellEditing(
							{
								triggerSelected : false //触发编辑的时候不选中行
							}),
					grid = new Grid.Grid({
						render: '#grid',
						columns: columns,
						/*width : '870px',*/
						loadMask: true, // 加载数据时显示屏蔽层
						store: store,
						/*height:350,*/
						itemStatusFields : {
			    			selected : 'checked',
			    			disabled : 'disable'
			            },
						plugins: [/*BUI.Grid.Plugins.CheckSelection, */editing,Grid.Plugins.AutoFit],
						bbar: { width:'100%'}
					});	
					grid.render();
			});
	}
}

function updateInventoryCount(el, index){
	var item = grid.getItemAt(index);
	
	var inventoryTemp =  el.value;
	if(!common.checkInt(inventoryTemp)){
		common.alertMes("库存数量只能是大于0的整数！", 'error');
		$(this).val("");
		return;
	}
	
	item.quantity = el.value;
}

function setProductInventoryCount(obj, value) {
	for (var i = 0; i < productSelected.length; i++) {
		var tempProduct = productSelected[i];
		if (tempProduct.sku == value) {
			var inventoryTemp =  $("#" + value + "").val();
			if(!common.checkInt(inventoryTemp)){
				common.alertMes("库存数量只能是大于0的整数！", 'error');
				$("#"+value+"").val(tempProduct.quantity);
				return;
			}
			
			tempProduct.quantity = $("#" + value + "").val();
			break;
		}
	}
	// 修改数量时，保存至临时列表中
	for (var i = 0; i < productSelectedBef.length; i++) {
		var tempProduct = productSelectedBef[i];
		if (tempProduct.sku == value) {
			tempProduct.quantity = $("#" + value + "").val();
			break;
		}
	}
}

function updateInventory(){
	var data = grid.get('items');
	for(var i = 0; i < data.length; i++){
		data[i].partnerId = partnerId;
	}
	common.rpcClient.call("partnerInventoryService.updateInventory", [data],
	        function (result) {
	            LoadMask.hide();
	            if (result.success) {
	            	common.alertMes("库存更新成功！", "success");
	            	window.history.go(-1);
	            } else{
	                common.alertMes(result.errorMsg, "error");
	            }
	        }, function (error) {
	            common.ajaxTimeout(error);
	            LoadMask.hide();
	        });
}