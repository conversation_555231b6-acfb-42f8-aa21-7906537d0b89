select top 1 CONVERT(varchar(100), p.product_date, 23) product_date, CONVERT(varchar(100), dp.scantime, 23) delivery_time, p1.name product_name, p.product_id sku,
p.product_batch, d.order_id do from wx_t_oem_product_packaging_code pc 
left join wx_t_oem_product_packaging p on p.id=pc.packaging_id
left join wx_t_oem_delivery_product dp on (dp.codelevel=1 and dp.code = pc.code1) or (dp.codelevel=2 and dp.code=pc.code2)
left join wx_t_oem_delivery d on d.id=dp.delivery_id
left join wx_t_product p1 on p1.sku=p.product_id
where pc.code1 = #code# or pc.code2=#code#
order by dp.scantime desc