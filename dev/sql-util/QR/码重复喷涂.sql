SET   IDENTITY_INSERT [wx_t_oem_delivery_product] on;
insert into [wx_t_oem_delivery_product] ([id]
      ,[delivery_id]
      ,[product_id]
      ,[code]
      ,[codelevel]
      ,[scantime]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[external_id]
      ,[ext_property1]
      ,[ext_property2]
      ,[ext_property3]
      ,[ext_property4]
      ,[ext_property5])
SELECT [id]
      ,[delivery_id]
      ,[product_id]
      ,[code]
      ,[codelevel]
      ,[scantime]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[external_id]
      ,[ext_property1]
      ,[ext_property2]
      ,[ext_property3]
      ,[ext_property4]
      ,[ext_property5]
  FROM [pmpdb01].[dbo].[wx_t_oem_delivery_product20220216] dp 
  where exists (select 1 from wx_t_oem_delivery20220216 d where dp.delivery_id=d.id and d.order_id in ('422078540',
'422086282',
'422098557',
'422099141',
'422099214',
'422101598',
'422103044',
'500630782'))
SET   IDENTITY_INSERT [wx_t_oem_delivery_product] off;


/****** Script for SelectTopNRows command from SSMS  ******/
SET   IDENTITY_INSERT [wx_t_oem_delivery] on;
insert into [wx_t_oem_delivery] ([id]
      ,[order_id]
      ,[recv_id]
      ,[recv_name]
      ,[send_id]
      ,[send_name]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[file_name]
      ,[external_id]
      ,[recv_distributor_id])
SELECT [id]
      ,[order_id]
      ,[recv_id]
      ,[recv_name]
      ,[send_id]
      ,[send_name]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[file_name]
      ,[external_id]
      ,[recv_distributor_id]
  FROM [pmpdb01].[dbo].wx_t_oem_delivery20220216 where order_id in ('422078540',
'422086282',
'422098557',
'422099141',
'422099214',
'422101598',
'422103044',
'500630782')
SET   IDENTITY_INSERT [wx_t_oem_delivery] off;
