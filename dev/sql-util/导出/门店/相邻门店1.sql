--乘用车200米内门店
select o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "门店名称", 
(case when w.status='0' then '潜在' when w.status='3' then '合作' else w.status end) "门店状态",
(select substring((select ','+cast(a.k as varchar(90)) from (select dbo.fun_cal_distance(w.longitude, w.latitude, t1.longitude, t1.latitude) dist,
t1.work_shop_name + '(' + convert(nvarchar(20),t1.id) + ')' k
from wx_t_work_shop t1 where t1.status in ('0', '3') and t1.delete_flag=0 and t1.from_source=1 and t1.longitude>0 and t1.latitude>0 and t1.id != w.id) a 
	where a.dist <= 200 and a.dist >= -200  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) "邻近门店"  from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=w.excute_user_id
where w.from_source=1 and w.status in ('0', '3') and w.delete_flag=0 and o.id!=9
and exists (select 1 from (select dbo.fun_cal_distance(w.longitude, w.latitude, t1.longitude, t1.latitude) dist 
from wx_t_work_shop t1 where t1.status in ('0', '3') and t1.delete_flag=0 and t1.from_source=1 and t1.longitude>0 and t1.latitude>0 and t1.id != w.id) a 
	where a.dist <= 200 and a.dist >= -200)
order by o.id, w.excute_user_id

--商用油500米内门店
select o.organization_name "经销商名称", u.ch_name "DSR", w.id, w.work_shop_name "门店名称", 
/*(case when w.status='0' then '潜在' when w.status='3' then '合作' else w.status end) "门店状态", */
case when w.shop_recruitment=1 then '是' else '否' end "店招店",
case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then '是' else '否' end "B2B店",
case when w.dms_key is null or w.dms_key='' then '否' else '是' end "DMS店",
(select count(1) from (select dbo.fun_cal_distance(w.longitude, w.latitude, t1.longitude, t1.latitude) dist,
t1.work_shop_name + '(' + convert(nvarchar(20),t1.id) + ')' k
from wx_t_work_shop t1 where t1.status in ('3') and t1.delete_flag=0 and t1.from_source=2 and t1.longitude>0 and t1.latitude>0 and t1.id != w.id) a 
	where a.dist <= 500 and a.dist >= -500) "邻近门店数",
(select substring((select ','+cast(a.k as varchar(90)) from (select dbo.fun_cal_distance(w.longitude, w.latitude, t1.longitude, t1.latitude) dist,
t1.work_shop_name + '(' + convert(nvarchar(20),t1.id) + ')' k
from wx_t_work_shop t1 where t1.status in ('3') and t1.delete_flag=0 and t1.from_source=2 and t1.longitude>0 and t1.latitude>0 and t1.id != w.id) a 
	where a.dist <= 500 and a.dist >= -500  FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) "邻近门店"  from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=w.excute_user_id
where w.from_source=2 and w.status in ( '3') and w.delete_flag=0 and o.id!=9
and exists (select 1 from (select dbo.fun_cal_distance(w.longitude, w.latitude, t1.longitude, t1.latitude) dist 
from wx_t_work_shop t1 where t1.status in ('3') and t1.delete_flag=0 and t1.from_source=2 and t1.longitude>0 and t1.latitude>0 and t1.id != w.id) a 
	where a.dist <= 500 and a.dist >= -500)
order by o.id, w.excute_user_id