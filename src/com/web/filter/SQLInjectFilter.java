
package com.web.filter;

import java.io.CharConversionException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;

import com.common.util.ContextUtil;
import com.sys.auth.model.WxTUser;

public class SQLInjectFilter implements Filter {
	
	public void destroy() {
		// TODO Auto-generated method stub
	}

		public static String BFH_STR ="%";

		@Override
		public void doFilter(ServletRequest servletrequest, ServletResponse servletresponse,
				FilterChain filterchain) throws IOException, ServletException {
			// TODO Auto-generated method stub
			//此处判断 rpc请求 并返回错误json格式
			HttpServletRequest request = (HttpServletRequest) servletrequest;
			HttpServletResponse response = (HttpServletResponse) servletresponse;
			String url  =request.getRequestURL().toString();
	        String token = request.getParameter(ContextUtil.APP_TOKEN_KEYWORD);
	        WxTUser user =  ContextUtil.createTokenUser(token);
	        if(user==null){
	        	response.setCharacterEncoding("utf-8");
				response.setContentType("application/json");
				String str ="{\"jsonrpc\":\"2.0\",\"id\":1,\"result\":{\"code\":\"tokenfail\"}}";
				this.write(str, response);
	        }else{
	        	filterchain.doFilter(servletrequest, servletresponse);
	        }
		}

		@Override
		public void init(FilterConfig arg0) throws ServletException {
			// TODO Auto-generated method stub
			
		}
		
		/**
		 * 向客户端写入内容
		 * 
		 * @param data
		 *            需要写入的数据
		 */
		public void write(String data, HttpServletResponse response) {
			if (StringUtils.isEmpty(data)) {
				return;
			}
			Writer writer = null;
			try {
				writer = response.getWriter();
				writer.write(data);
				writer.flush();

			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				try {
					writer.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
}
			