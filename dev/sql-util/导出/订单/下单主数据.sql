			SELECT *
			FROM (
				SELECT psc.*, org.organization_name AS partner_name, org.id AS partner_id
				FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					LEFT JOIN wx_t_organization org ON org.id = poe.partner_id
				--WHERE org.status = 1
			) v
			
SELECT psc.id,org.organization_name AS partner_name, org.id partner_id, psc.payment_term, psc.sap_code sold_to_code, psc.ship_to_code, psc.address,
psc.contact_person, psc.contact_person_tel,psc.date_from,psc.date_to
				,case when psc.date_from<getdate() and psc.date_to>getdate() then 'Active' else 'Inactive' end "状态"
				 FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					JOIN wx_t_organization org ON org.id = poe.partner_id
					where --len(psc.address)>0 and 
					org.status=1 --and psc.distributor_id=208 
					order by psc.distributor_id
					
SELECT psc.id,org.organization_name AS partner_name, org.id partner_id, sales_org,psc.payment_term, psc.sap_code sold_to_code, psc.ship_to_code, psc.address,
psc.contact_person, psc.contact_person_tel,psc.date_from,psc.date_to
				,case when psc.date_from<getdate() and psc.date_to>getdate() then 'Active' else 'Inactive' end "状态"
				 FROM [PP_MID].dbo.mid_partner_sale_config psc
					LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
					JOIN wx_t_organization org ON org.id = poe.partner_id
					where --len(psc.address)>0 and 
					org.status=1 --and psc.distributor_id=208 
					order by psc.distributor_id					