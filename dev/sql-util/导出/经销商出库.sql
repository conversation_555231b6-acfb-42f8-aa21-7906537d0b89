SELECT YEAR(os.out_time) [year]
       , MONTH(os.out_time) [month]
       ,ws.partner_id,o.organization_name "经销商名称"
          ,sum(osp.actual_out_count * CONVERT(FLOAT, tp.capacity)) AS liters
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK' and os.status='2'
                    group by YEAR(os.out_time),MONTH(os.out_time),ws.partner_id, o.organization_name

                    
SELECT YEAR(os.out_time) [year]
       , MONTH(os.out_time) [month]
       ,ws.partner_id,o.organization_name "经销商名称"
          ,sum(osp.product_count * CONVERT(FLOAT, tp.capacity)) AS liters
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_line osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK' and os.status='2'
                    group by YEAR(os.out_time),MONTH(os.out_time),ws.partner_id, o.organization_name
                    
                    
                    
                    
                    
SELECT o.organization_name "经销商名称",
	tp.name "产品名称", tp.sku, osp.actual_out_count "数量(瓶/桶)",
          osp.actual_out_count * CONVERT(FLOAT, tp.capacity) AS liters, os.out_time "出库时间"
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK' and os.status='2'
                  order by  os.out_time desc

                    
SELECT max(o.organization_name) "经销商名称",
	max(tp.name) "产品名称", tp.sku, sum(osp.product_count) "数量(瓶/桶)",
          sum(osp.product_count * CONVERT(FLOAT, tp.capacity)) AS liters, min(os.out_time) "出库时间"
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_line osp ON os.stock_out_no = osp.stock_out_no
            JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK' and os.status='2'
		   group by os.stock_out_no, tp.sku
                  order by  min(os.out_time) desc
                    
SELECT o.organization_name "经销商名称",
	tp.name "产品名称", tp.sku, osp.actual_out_count "数量(瓶/桶)",
          osp.actual_out_count * CONVERT(FLOAT, tp.capacity) AS liters, os.create_time "提交时间", os.out_time "出库时间",
		  case when os.status='2' then '已出库' else '未确认送达' end "状态"
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK'
                  order by  os.create_time desc
                    
                    
SELECT max(o.organization_name) "经销商名称",
	max(tp.name) "产品名称", tp.sku, sum(osp.product_count) "数量(瓶/桶)",
          sum(osp.product_count * CONVERT(FLOAT, tp.capacity)) AS liters,min(os.create_time) "提交时间", min(os.out_time) "出库时间",
		  case when os.status='2' then '已出库' else '未确认送达' end "状态"
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_line osp ON os.stock_out_no = osp.stock_out_no
            JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK'
		   group by os.stock_out_no, tp.sku,os.status
                  order by  min(os.create_time) desc
                   
                  
SELECT o.organization_name "经销商名称",
	tp.name "产品名称", tp.sku, osp.actual_out_count "数量(瓶/桶)",
          osp.actual_out_count * CONVERT(FLOAT, tp.capacity) AS liters, os.create_time "提交时间", os.out_time "出库时间",
		  case when os.status='2' then '已出库' else '未确认送达' end "状态"
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK'
                  order by  os.create_time desc

SELECT o.organization_name "经销商名称", ws.customer_code "门店编码", ws.work_shop_name "门店名称",
         sum( osp.actual_out_count * CONVERT(FLOAT, tp.capacity) )AS liters, 
		  case when os.status='2' then '已出库' else '未确认送达' end "状态"
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
			left join wx_t_organization o on o.id=ws.partner_id
           WHERE wto.source='OUT_STOCK' and os.out_time>='2022-01-01'
                  group by o.organization_name,ws.id,ws.customer_code,ws.work_shop_name,os.status
				  order by o.organization_name,ws.work_shop_name

				  