(function(e){function t(t){for(var n,a,c=t[0],u=t[1],s=t[2],d=0,p=[];d<c.length;d++)a=c[d],i[a]&&p.push(i[a][0]),i[a]=0;for(n in u)Object.prototype.hasOwnProperty.call(u,n)&&(e[n]=u[n]);l&&l(t);while(p.length)p.shift()();return o.push.apply(o,s||[]),r()}function r(){for(var e,t=0;t<o.length;t++){for(var r=o[t],n=!0,a=1;a<r.length;a++){var c=r[a];0!==i[c]&&(n=!1)}n&&(o.splice(t--,1),e=u(u.s=r[0]))}return e}var n={},a={app:0},i={app:0},o=[];function c(e){return u.p+"js/"+({}[e]||e)+"."+{"chunk-89aefca4":"5fe2dc31","chunk-24391f54":"b311f51b","chunk-73ebcd26":"9f36ed0c","chunk-29a3ff40":"f8e88de3","chunk-1317a172":"94ab5924","chunk-4707e672":"7fe0b484","chunk-0c61e046":"59e62f65","chunk-0c5fbb7c":"ef948e3d","chunk-c5afaca4":"ba85ba3d","chunk-2d0baaa9":"a1bf85a4","chunk-2d207eab":"fab8a388","chunk-ee81b820":"ec46dd39"}[e]+".js"}function u(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,u),r.l=!0,r.exports}u.e=function(e){var t=[],r={"chunk-89aefca4":1,"chunk-c5afaca4":1,"chunk-ee81b820":1};a[e]?t.push(a[e]):0!==a[e]&&r[e]&&t.push(a[e]=new Promise(function(t,r){for(var n="css/"+({}[e]||e)+"."+{"chunk-89aefca4":"af639ac9","chunk-24391f54":"31d6cfe0","chunk-73ebcd26":"31d6cfe0","chunk-29a3ff40":"31d6cfe0","chunk-1317a172":"31d6cfe0","chunk-4707e672":"31d6cfe0","chunk-0c61e046":"31d6cfe0","chunk-0c5fbb7c":"31d6cfe0","chunk-c5afaca4":"8a9fa684","chunk-2d0baaa9":"31d6cfe0","chunk-2d207eab":"31d6cfe0","chunk-ee81b820":"29547143"}[e]+".css",i=u.p+n,o=document.getElementsByTagName("link"),c=0;c<o.length;c++){var s=o[c],d=s.getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(d===n||d===i))return t()}var p=document.getElementsByTagName("style");for(c=0;c<p.length;c++){s=p[c],d=s.getAttribute("data-href");if(d===n||d===i)return t()}var l=document.createElement("link");l.rel="stylesheet",l.type="text/css",l.onload=t,l.onerror=function(t){var n=t&&t.target&&t.target.src||i,o=new Error("Loading CSS chunk "+e+" failed.\n("+n+")");o.code="CSS_CHUNK_LOAD_FAILED",o.request=n,delete a[e],l.parentNode.removeChild(l),r(o)},l.href=i;var f=document.getElementsByTagName("head")[0];f.appendChild(l)}).then(function(){a[e]=0}));var n=i[e];if(0!==n)if(n)t.push(n[2]);else{var o=new Promise(function(t,r){n=i[e]=[t,r]});t.push(n[2]=o);var s,d=document.createElement("script");d.charset="utf-8",d.timeout=120,u.nc&&d.setAttribute("nonce",u.nc),d.src=c(e),s=function(t){d.onerror=d.onload=null,clearTimeout(p);var r=i[e];if(0!==r){if(r){var n=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src,o=new Error("Loading chunk "+e+" failed.\n("+n+": "+a+")");o.type=n,o.request=a,r[1](o)}i[e]=void 0}};var p=setTimeout(function(){s({type:"timeout",target:d})},12e4);d.onerror=d.onload=s,document.head.appendChild(d)}return Promise.all(t)},u.m=e,u.c=n,u.d=function(e,t,r){u.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},u.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.t=function(e,t){if(1&t&&(e=u(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(u.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)u.d(r,n,function(t){return e[t]}.bind(null,n));return r},u.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return u.d(t,"a",t),t},u.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},u.p="",u.oe=function(e){throw console.error(e),e};var s=window["webpackJsonp"]=window["webpackJsonp"]||[],d=s.push.bind(s);s.push=t,s=s.slice();for(var p=0;p<s.length;p++)t(s[p]);var l=d;o.push([0,"chunk-vendors"]),r()})({0:function(e,t,r){e.exports=r("56d7")},"2ef7":function(e,t,r){"use strict";var n=r("2b0e"),a=r("8c4f"),i=[{path:"/",redirect:"/credit/list"},{path:"/credit/annual/submit",component:function(e){return Promise.all([r.e("chunk-89aefca4"),r.e("chunk-29a3ff40"),r.e("chunk-1317a172")]).then(function(){var t=[r("3fa5")];e.apply(null,t)}.bind(this)).catch(r.oe)}},{path:"/credit/annual/review",component:function(e){return Promise.all([r.e("chunk-89aefca4"),r.e("chunk-4707e672"),r.e("chunk-29a3ff40"),r.e("chunk-0c5fbb7c")]).then(function(){var t=[r("8271")];e.apply(null,t)}.bind(this)).catch(r.oe)}},{path:"/credit/temp/submit",component:function(e){return Promise.all([r.e("chunk-89aefca4"),r.e("chunk-24391f54"),r.e("chunk-73ebcd26")]).then(function(){var t=[r("0111")];e.apply(null,t)}.bind(this)).catch(r.oe)}},{path:"/credit/temp/review",component:function(e){return Promise.all([r.e("chunk-89aefca4"),r.e("chunk-4707e672"),r.e("chunk-24391f54"),r.e("chunk-0c61e046")]).then(function(){var t=[r("4e42")];e.apply(null,t)}.bind(this)).catch(r.oe)}},{path:"/credit/cv/submit",component:function(e){return Promise.all([r.e("chunk-89aefca4"),r.e("chunk-c5afaca4"),r.e("chunk-2d207eab")]).then(function(){var t=[r("a325")];e.apply(null,t)}.bind(this)).catch(r.oe)}},{path:"/credit/cv/review",component:function(e){return Promise.all([r.e("chunk-89aefca4"),r.e("chunk-4707e672"),r.e("chunk-c5afaca4"),r.e("chunk-2d0baaa9")]).then(function(){var t=[r("37d3")];e.apply(null,t)}.bind(this)).catch(r.oe)}},{path:"/credit/list",component:function(e){return r.e("chunk-ee81b820").then(function(){var t=[r("0411")];e.apply(null,t)}.bind(this)).catch(r.oe)},meta:{keepAlive:!0}}],o=i,c=[o],u=[].concat.apply([],c),s=u;n["default"].use(a["a"]);var d=new a["a"]({mode:"hash",routes:s});t["a"]=d},"56d7":function(e,t,r){"use strict";r.r(t);r("cadf"),r("551c"),r("f751"),r("097d");var n=r("2b0e"),a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{attrs:{id:"app"}},[r("keep-alive",[e.$route.meta.keepAlive&&e.loadedPermission?r("router-view"):e._e()],1),!e.$route.meta.keepAlive&&e.loadedPermission?r("router-view"):e._e()],1)},i=[],o=(r("96cf"),r("3b8d")),c={name:"app",data:function(){return{loadedPermission:!1}},created:function(){var e=Object(o["a"])(regeneratorRuntime.mark(function e(){var t=this;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return this.$store.dispatch("getUserInfo"),e.next=3,this.$store.dispatch("getCreditPermissions").then(function(){t.loadedPermission=!0});case 3:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()},u=c,s=(r("5c0b"),r("2877")),d=Object(s["a"])(u,a,i,!1,null,null,null),p=d.exports,l=r("6d9e"),f=r("2ef7"),m=(r("9e1f"),r("450d"),r("6ed5")),b=r.n(m),y=(r("46a1"),r("e5f2")),h=r.n(y),v=(r("be4f"),r("896a")),T=r.n(v),g=(r("fe07"),r("6ac5")),A=r.n(g),R=(r("3c52"),r("0d7b")),I=r.n(R),E=(r("0c67"),r("299c")),O=r.n(E),C=(r("9c49"),r("6640")),S=r.n(C),k=(r("d2ac"),r("95b0")),w=r.n(k),P=(r("672e"),r("101e")),q=r.n(P),j=(r("b0ee"),r("d180")),_=r.n(j),L=(r("a7cc"),r("df33")),N=r.n(L),x=(r("a335"),r("c0bb")),U=r.n(x),D=(r("f225"),r("89a9")),F=r.n(D),B=(r("e612"),r("dd87")),W=r.n(B),M=(r("075a"),r("72aa")),V=r.n(M),Y=(r("5466"),r("ecdf")),H=r.n(Y),G=(r("38a0"),r("ad41")),$=r.n(G),Q=(r("eca7"),r("3787")),K=r.n(Q),J=(r("425f"),r("4105")),Z=r.n(J),z=(r("1951"),r("eedf")),X=r.n(z),ee=(r("826b"),r("c263")),te=r.n(ee),re=(r("6611"),r("e772")),ne=r.n(re),ae=(r("1f1a"),r("4e4b")),ie=r.n(ae),oe=(r("10cb"),r("f3ad")),ce=r.n(oe),ue=(r("fd71"),r("a447")),se=r.n(ue),de=(r("f4f9"),r("c2cc")),pe=r.n(de),le=(r("7a0f"),r("0f6c")),fe=r.n(le);n["default"].use(fe.a),n["default"].use(pe.a),n["default"].use(se.a),n["default"].use(ce.a),n["default"].use(ie.a),n["default"].use(ne.a),n["default"].use(te.a),n["default"].use(X.a),n["default"].use(Z.a),n["default"].use(K.a),n["default"].use($.a),n["default"].use(H.a),n["default"].use(V.a),n["default"].use(W.a),n["default"].use(F.a),n["default"].use(U.a),n["default"].use(N.a),n["default"].use(_.a),n["default"].use(q.a),n["default"].use(w.a),n["default"].use(S.a),n["default"].use(O.a),n["default"].use(I.a),n["default"].use(A.a),n["default"].use(T.a),n["default"].prototype.$notify=h.a,n["default"].prototype.$alert=b.a.alert,n["default"].prototype.$confirm=b.a.confirm;r("3b2b"),r("a481");function me(e,t){var r={"M+":e.getMonth()+1,"D+":e.getDate(),"h+":e.getHours()%12===0?12:e.getHours()%12,"H+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()},n={0:"/u65e5",1:"/u4e00",2:"/u4e8c",3:"/u4e09",4:"/u56db",5:"/u4e94",6:"/u516d"};for(var a in/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),/(E+)/.test(t)&&(t=t.replace(RegExp.$1,(RegExp.$1.length>1?RegExp.$1.length>2?"/u661f/u671f":"/u5468":"")+n[e.getDay()+""])),r)new RegExp("("+a+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?r[a]:("00"+r[a]).substr((""+r[a]).length)));return t}n["default"].filter("formatDate",function(e,t){return"Invalid Date"!==e?me(e,t):""}),new n["default"]({store:l["a"],router:f["a"],render:function(e){return e(p)}}).$mount("#app")},"5c0b":function(e,t,r){"use strict";var n=r("5e27"),a=r.n(n);a.a},"5e27":function(e,t,r){},6529:function(e,t,r){"use strict";var n=r("d225"),a=r("b0b4"),i=r("a1bd"),o=function(){function e(){Object(n["a"])(this,e)}return Object(a["a"])(e,[{key:"getCreditListForTodo",value:function(e){return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryTodoList",params:[{start:10*(e.page-1),limit:10,queryType:e.queryType,queryField:e.queryField,dateStart:e.dateStart,dateEnd:e.dateEnd,aiRequestedBy:e.aiRequestedBy,cbiCustomerId:e.cbiCustomerId,partnerName:e.partnerName,creditAppTypes:e.creditAppTypes,status:e.status}]}})}},{key:"getCreditListForDone",value:function(e){return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryAllList",params:[{start:10*(e.page-1),limit:10,queryType:e.queryType,queryField:e.queryField,dateStart:e.dateStart,dateEnd:e.dateEnd,aiRequestedBy:e.aiRequestedBy,cbiCustomerId:e.cbiCustomerId,partnerName:e.partnerName,creditAppTypes:e.creditAppTypes,status:e.status}]}})}},{key:"getCreditListForDraft",value:function(e){return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryDraftList",params:[{start:10*(e.page-1),limit:10,searchWord:e.queryField}]}})}},{key:"getReviewHistory",value:function(e){return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryApprovalHistory",params:[{applicationId:e.id}]}})}},{key:"getCreditStatusOptions",value:function(){return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:2,jsonrpc:"2.0",method:"dicService.getDicItemByDicTypeCode",params:["Credit.workflowStatus"]}})}},{key:"getCreditList",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/list.do",contentType:"json",params:{},data:{start:10*(e.page-1),limit:10,queryType:e.queryType||"",queryField:e.queryField||"",dateStart:e.dateStart||"",dateEnd:e.dateEnd||"",aiRequestedBy:e.aiRequestedBy||"",cbiCustomerId:e.cbiCustomerId||"",partnerName:e.partnerName||"",creditAppTypes:e.creditAppTypes||null,workflowStatus:e.workflowStatus||"",fromPage:e.fromPage||"",fromRequestor:e.fromRequestor||"",requestNo:e.requestNo||"",direction:"DESC",field:"updateTime"}})}},{key:"downloadList",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/export.do",contentType:"json",responseType:"blob",params:{},data:{start:10*(e.page-1),limit:10,queryType:e.queryType||"",queryField:e.queryField||"",dateStart:e.dateStart||"",dateEnd:e.dateEnd||"",aiRequestedBy:e.aiRequestedBy||"",cbiCustomerId:e.cbiCustomerId||"",partnerName:e.partnerName||"",creditAppTypes:e.creditAppTypes||null,workflowStatus:e.workflowStatus||"",fromPage:e.fromPage||"",fromRequestor:e.fromRequestor||"",direction:"DESC",field:"updateTime"}})}}]),e}();t["a"]=new o},"662e":function(e,t,r){"use strict";r("7f7f");var n=r("d225"),a=r("b0b4"),i=r("a1bd"),o=function(){function e(){Object(n["a"])(this,e)}return Object(a["a"])(e,[{key:"getRequestedPersonByName",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"user/ctrldata.do",contentType:"form",params:{},data:e})}},{key:"getRequestedPersonById",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditCommonService.getApplicationRequestedInformation",params:[e.id]}})}},{key:"getCustomerById",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditCommonService.getCustomerBasicInformation",params:[e.id,e.name]}})}},{key:"getCustomerListById",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditCommonService.getCustomerCodeList",params:[e.id]}})}},{key:"getCreditCsr",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditCommonService.getCreditCsr",params:[e.name]}})}},{key:"getDraftInitForm",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"credit/app/getInitForm.do",contentType:"form",params:{},data:{creditType:e.creditType}})}},{key:"getReviewProcess",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryWorkflowNodes",params:[{applicationId:e.id}]}})}},{key:"getWorkflowStepInstance",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/getWorkflowStepInstances.do",contentType:"form",params:{},data:e})}},{key:"getWorkflowStepHistory",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/getWorkflowStepHistory.do",contentType:"form",params:{},data:e})}},{key:"saveApply",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/save.do",contentType:"json",params:{},data:e})}},{key:"submitApply",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/submit.do",contentType:"json",params:{},data:e})}},{key:"getCreditApply",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"credit/app/detail.do",contentType:"form",params:{},data:e})}},{key:"rejectApply",value:function(e){return Object(i["a"])({method:"post",path:"credit/app/reject.do",contentType:"json",params:{},data:e})}},{key:"calcFinanceInfo",value:function(e){return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.calcCustomerFinanceInfo",params:[e]}})}},{key:"recallApply",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"credit/app/recall.do",contentType:"json",params:{},data:e})}},{key:"releaseOrder",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.updateReleaseOrderStatusById",params:[""+e.id]}})}},{key:"reassign",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"credit/app/reassign.do",contentType:"form",params:{},data:e})}},{key:"notifyApplyHandle",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"credit/app/notifyhandle.do",contentType:"form",params:{},data:e})}},{key:"notifySalesLeader",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i["a"])({method:"post",path:"credit/app/notifysalesleader.do",contentType:"form",params:{},data:e})}}]),e}();t["a"]=new o},"6d9e":function(e,t,r){"use strict";var n=r("2b0e"),a=r("2f62"),i=(r("be4f"),r("450d"),r("896a")),o=r.n(i),c=r("768b"),u=(r("96cf"),r("3b8d")),s=(r("28a5"),r("6b54"),r("7514"),{id:"",curTaskId:"",processInstanceId:"",processStatus:"",creditType:"",requestNo:"",currency:"",aiPreparedBy:"",aiPreparedByName:"",aiRegionId:"",aiRegionName:"",aiRequestDate:"",aiRequestedBy:"",aiTelephone:"",aiSalesTeam:"",aiSalesTeamArray:[],cbiCreditCsr:"",cbiCustomerList:[],cbiCustomerId:"",cbiCustomerName:"",customerType:"",soldToCode:"",payerCode:"",customerName:"",cbiProvinceId:"",cbiProvinceList:[],cbiRequestedTempCreditLimit:"",cbiRequestedTempPaymentTerm:"",cbiExpireDate:"",cbiRequestedCvOrderNo:"",cbiRequestedCvOrderNoArray:[{id:Date.now(),value:""}],cbiCooperationYearsWithCvx:"",cbiCooperationYearsWithCvxList:[],cbiYearN1TotalSales:"",cbiDateEstablishment:"",directAnnualSalesPlan:"",indirectAnnualSalesPlan:"",cbiCommentsFromBu:"",cbiCreditLimitOfYearN1:"",cbiPaymentTermOfYearN1:"",cbiRequestedCreditLimitCurrentYear:"",applyAmountUsd:"",cbiRequestedPaymentTermOfCurrentYear:"",cbiFinancialStatementsAttId:"",cbiFinancialStatementsAttUrl:"",cbiApplicationFormAttId:"",cbiBusinessLicenseAttId:"",cbiPaymentCommitmentAttId:"",uploadOrderFileAttId:"",cbiCashDepositWithAmount:"",cbiCashDepositWithAmountUploadScancopyId:"",cbiCashDepositWithAmountUploadScancopyUrl:"",cbiCashDepositWithAmountValidDate:"",cbiThe3rdPartyGuaranteeWithAmount:"",cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId:"",cbiThe3rdPartyGuaranteeWithAmountUploadScancopyUrl:"",cbiThe3rdPartyGuaranteeWithAmountValidDate:"",cbiBankGuaranteeWithAmount:"",cbiBankGuaranteeWithAmountUploadScancopyId:"",cbiBankGuaranteeWithAmountUploadScancopyUrl:"",cbiBankGuaranteeWithAmountValidDate:"",cbiPersonalGuaranteeWithAmount:"",cbiPersonalGuaranteeWithAmountUploadScancopyId:"",cbiPersonalGuaranteeWithAmountUploadScancopyUrl:"",cbiPersonalGuaranteeWithAmountValidDate:"",creditDollarRate:"",cfiInfo:{cfiConfirmedCreditLimitOfCurrentYear:"",cfiConfirmedPaymentTermOfCurrentYear:"",cfiConfirmedTempCreditLimit:"",cfiConfirmedTempPaymentTerm:"",cfiConfirmedExpiredDate:"",cfiAccountReceivableTrunover:"",cfiAfterTaxProfitRatio:"",cfiApDays:"",cfiAssetTurnover:"",cfiAssetTurnoverNetSalesToTotalAssets:"",cfiCalculatedCreditLimitPerCreditPolicy:"",cfiCashFlowCoverage:"",cfiCommentsFromCredit:"",cfiCreditIndex:"",cfiCreditLimitEstimatedValue:"",cfiCurrentExposure:"",cfiCurrentLiabilityToEquity:"",cfiCurrentRatio:"",cfiCvAmount:"",cfiDailySales:"",cfiDaysInAccountsReceivable:"",cfiDaysInInventory:"",cfiDsoInChevronChina:"",cfiEquity:"",cfiEquityRatio:"",cfiEstimatedValue:"",cfiInventoryTurnover:"",cfiLiablitiesAssets:"",cfiLongTermLiabilityTotalAssetsRatio:"",cfiNetWorkingCapitalCycle:"",cfiPayHistoryWithChevron:"",cfiProfitMargin:"",cfiQuickRatio:"",cfiRecAddTempCreditLimit:"",cfiRecCreditLimitOfCurrentYear:"",cfiRecCreditPaymentTerm:"",cfiRecCreditPaymentTermList:[],cfiRecTempPaymentTerm:"",cfiReturnOnEquity:"",cfiSaleCurrentAssets:"",othersAttId:"",cfiUploadArtAttId:"",cfiReleaseOrderAttId:"",cfiUploadInvestigationReportAttId:"",cfiScreenshotOfCurrentExposureAttId:"",cfiScreenshotOfCurrentExposureAttUrl:"",cfiTangibleNetWorth:"",cfiTangibleNetWorthRatioG32:"",cfiTotalScore:"",cfiWorkingAssets:"",cfiWorkingCapital:"",cfiYearN1PaymentRecord:""}}),d=r("662e"),p=r("6529");function l(e,t){var r="";return t.find(function(t){if("required"===t.type){if("undefined"===typeof e||null===e||""===e)return r=t.message||"* is required, please complete it",!0}else if("notEqualZero"===t.type&&0===e)return r=t.message||"* is required, please complete it",!0}),r?[!1,r]:[!0,e]}var f=function(e,t){for(var r in t){var n=!0,a="";if("[object Object]"===Object.prototype.toString.call(t[r]))for(var i in t[r]){var o=l(e[r][i],t[r][i]),u=Object(c["a"])(o,2);if(n=u[0],a=u[1],!n)return[!1,a]}else{var s=l(e[r],t[r]),d=Object(c["a"])(s,2);n=d[0],a=d[1]}if(!n)return[!1,a]}return[!0,e]},m={creditType:[{type:"required"}],requestNo:[{type:"required"}],aiPreparedBy:[{type:"required"}],aiRequestedBy:[{type:"required"}],aiTelephone:[{type:"required"}],aiSalesTeam:[{type:"required"}],cbiCustomerId:[{type:"required"}],cbiProvinceId:[{type:"required"}],cbiCooperationYearsWithCvx:[{type:"required"}],cbiYearN1TotalSales:[{type:"required"}],cbiDateEstablishment:[{type:"required"}],directAnnualSalesPlan:[{type:"required"}],indirectAnnualSalesPlan:[{type:"required"}],cbiApplicationFormAttId:[{type:"notEqualZero",message:"Application Form 信用额度申请表，必须上传附件"}],cbiBusinessLicenseAttId:[{type:"notEqualZero",message:"Business License 营业执照，必须上传附件"}],cbiFinancialStatementsAttId:[{type:"notEqualZero",message:"Financial Statements 财务报表上传，必须上传附件"}],cbiRequestedCreditLimitCurrentYear:[{type:"required"}],cbiRequestedPaymentTermOfCurrentYear:[{type:"required"}]},b=function(e){var t=f(e,m),r=Object(c["a"])(t,2),n=r[0],a=r[1];return n?[!0,a]:[!1,a]},y={creditType:[{type:"required"}],requestNo:[{type:"required"}],aiPreparedBy:[{type:"required"}],aiRequestedBy:[{type:"required"}],aiTelephone:[{type:"required"}],aiSalesTeam:[{type:"required"}],cbiCustomerId:[{type:"required"}],cbiRequestedTempCreditLimit:[{type:"required"}],cbiRequestedTempPaymentTerm:[{type:"required"}],cbiExpireDate:[{type:"required"}]},h=function(e){var t=f(e,y),r=Object(c["a"])(t,2),n=r[0],a=r[1];return n?[!0,a]:[!1,a]},v={creditType:[{type:"required"}],requestNo:[{type:"required"}],aiPreparedBy:[{type:"required"}],aiRequestedBy:[{type:"required"}],aiTelephone:[{type:"required"}],aiSalesTeam:[{type:"required"}],cbiCustomerId:[{type:"required"}],cbiRequestedCvOrderNo:[{type:"required"}]},T=function(e){var t=f(e,v),r=Object(c["a"])(t,2),n=r[0],a=r[1];return n?[!0,a]:[!1,a]},g=function(e){return"ANNUAL_CREDIT_REVIEW"===e.creditType?b(e):"TEMP_CREDIT_REQUEST"===e.creditType?h(e):"CV_REQUEST"===e.creditType?T(e):[!0]},A={id:[{type:"required"}],creditType:[{type:"required"}],requestNo:[{type:"required"}],aiPreparedBy:[{type:"required"}],aiRequestedBy:[{type:"required"}],aiTelephone:[{type:"required"}],aiSalesTeam:[{type:"required"}],cbiCustomerId:[{type:"required"}],cbiProvinceId:[{type:"required"}],cbiCooperationYearsWithCvx:[{type:"required"}],cbiYearN1TotalSales:[{type:"required"}],cbiDateEstablishment:[{type:"required"}],directAnnualSalesPlan:[{type:"required"}],indirectAnnualSalesPlan:[{type:"required"}],cbiRequestedCreditLimitCurrentYear:[{type:"required"}],cbiRequestedPaymentTermOfCurrentYear:[{type:"required"}]},R=function(e){var t=f(e,A),r=Object(c["a"])(t,2),n=r[0],a=r[1];return n?[!0,a]:[!1,a]},I={id:[{type:"required"}],creditType:[{type:"required"}],requestNo:[{type:"required"}],aiPreparedBy:[{type:"required"}],aiRequestedBy:[{type:"required"}],aiTelephone:[{type:"required"}],aiSalesTeam:[{type:"required"}],cbiCustomerId:[{type:"required"}],cbiRequestedTempCreditLimit:[{type:"required"}],cbiRequestedTempPaymentTerm:[{type:"required"}],cbiExpireDate:[{type:"required"}]},E=function(e){var t=f(e,I),r=Object(c["a"])(t,2),n=r[0],a=r[1];return n?[!0,a]:[!1,a]},O={id:[{type:"required"}],creditType:[{type:"required"}],requestNo:[{type:"required"}],aiPreparedBy:[{type:"required"}],aiRequestedBy:[{type:"required"}],aiTelephone:[{type:"required"}],aiSalesTeam:[{type:"required"}],cbiCustomerId:[{type:"required"}],cbiRequestedCvOrderNo:[{type:"required"}]},C=function(e){var t=f(e,O),r=Object(c["a"])(t,2),n=r[0],a=r[1];return n?[!0,a]:[!1,a]},S=function(e){return"ANNUAL_CREDIT_REVIEW"===e.creditType?R(e):"TEMP_CREDIT_REQUEST"===e.creditType?E(e):"CV_REQUEST"===e.creditType?C(e):[!0]};function k(e,t){for(var r in"[object Object]"!==Object.prototype.toString.call(e)&&(e={}),t){var n=t[r];"[object Object]"===Object.prototype.toString.call(n)?k(e[r],t[r]):"[object Array]"===Object.prototype.toString.call(n)?e[r]=[].concat(t[r]):e[r]=t[r]}}var w=k;function P(e){return["cfiScreenshotOfCurrentExposureAttId","othersAttId","cfiUploadArtAttId","cfiReleaseOrderAttId","cfiUploadInvestigationReportAttId"].indexOf(e)>-1}function q(e){var t={aiSalesTeamArray:void 0,cbiCustomerList:void 0,cbiProvinceList:void 0,cbiCooperationYearsWithCvxList:void 0,cfiRecCreditPaymentTermList:void 0,aiRegionName:void 0,cbiApplicationFormAttId:void 0,cbiBusinessLicenseAttId:void 0,cbiPaymentCommitmentAttId:void 0,cbiRequestedCvOrderNoArray:void 0,uploadOrderFileAttId:void 0},r=Object.assign({},j.form,t);return r.cfiInfo&&(r.cfiInfo.cfiReleaseOrderAttId=void 0,r.cfiInfo.cfiUploadInvestigationReportAttId=void 0,r.cfiInfo.cfiScreenshotOfCurrentExposureAttId=void 0,r.cfiInfo.cfiScreenshotOfCurrentExposureAttUrl=void 0,r.cfiInfo.cfiRecCreditPaymentTermList=void 0),r}var j={form:Object.assign({},s),formVersionNo:void 0,isRequestNode:void 0,lockerId:"",nodeId:"",recallable:void 0,rejectable:void 0,submitable:void 0,notifyHandleable:void 0,paymentTermList:[],workflowSteps:[],reviewHistory:[],maxAmountUsd:1e6},_={moneyMasked:function(){return{decimal:".",thousands:",",prefix:"",suffix:"",precision:2,masked:!1}},applyForm:function(e){return e.form},cfiInfo:function(e){return e.form.cfiInfo},canSubmit:function(e,t){return e.submitable},canReject:function(e){return e.rejectable},canReview:function(e,t){return!!e.form.curTaskId&&e.form.aiPreparedBy!==t.userId},canEditApply:function(e,t){return t.canEditCredit||t.canEditComfirmedCredit||t.isApplyNotInProcess},canEditCredit:function(e,t){return t.canSubmit&&!t.isApplyNotInProcess&&t.isSalesManager},canEditComfirmedCredit:function(e,t){return t.canSubmit&&!t.isApplyNotInProcess&&t.isCredit},canRecall:function(e){return e.recallable},canNotify:function(){return j.notifyHandleable},formApplyVersionNo:function(e){return e.formVersionNo},isApplyRequestNode:function(e){return e.isRequestNode},applyLockerId:function(e){return e.lockerId},applyNodeId:function(e){return e.nodeId},isApplyNotInProcess:function(e){return"undefined"===typeof e.isRequestNode||e.isRequestNode},paymentTermListOptions:function(e){return e.paymentTermList},isCredit:function(e){return["FL1","FL2","FL3"].indexOf(e.nodeId)>-1},isLocalCredit:function(e){return"localCredit"===e.nodeId},isSalesManager:function(e){return["SL1","SL2","SL3"].indexOf(e.nodeId)>-1},isAnnualApply:function(e){return"ANNUAL_CREDIT_REVIEW"===e.form.creditType},isTempApply:function(e){return"TEMP_CREDIT_REQUEST"===e.form.creditType},isCVApply:function(e){return"CV_REQUEST"===e.form.creditType},cvRequestOrderArray:function(e){return e.form.cbiRequestedCvOrderNoArray},currentFlowExcutors:function(e){var t=e.workflowSteps.find(function(e){return!e.finished&&e.executors});return t?t.executors:[]},currentExcutorTaskId:function(e){var t=e.workflowSteps.find(function(e){return!e.finished&&e.executors});return t?t.taskId:""},isCVAndApplyInProcess:function(e,t){return t.isCVApply&&!t.isApplyNotInProcess},isApplyProcessFinished:function(e){return e.form.workflowStatus>=100},applyWorkFlowSteps:function(e){return e.workflowSteps},maxUsd:function(e){return e.maxAmountUsd},processInstanceId:function(e){return e.form.processInstanceId}},L={UPDATE_APPLY_FORM:function(e,t){w(e.form,t)},CLEAR_APPLY_FORM:function(e){e.form=Object.assign({},s)},UPDATE_UPLOAD_FILE_NUMBER:function(e,t){if(e.form.cbiBankGuaranteeWithAmountUploadScancopyId=0,e.form.cbiCashDepositWithAmountUploadScancopyId=0,e.form.cbiApplicationFormAttId=0,e.form.cbiBusinessLicenseAttId=0,e.form.cbiFinancialStatementsAttId=0,e.form.cbiPaymentCommitmentAttId=0,e.form.cbiPersonalGuaranteeWithAmountUploadScancopyId=0,e.form.cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId=0,e.form.cfiInfo.othersAttId=0,e.form.cfiInfo.cfiScreenshotOfCurrentExposureAttId=0,e.form.cfiInfo.cfiUploadArtAttId=0,e.form.cfiInfo.cfiReleaseOrderAttId=0,e.form.cfiInfo.cfiUploadInvestigationReportAttId=0,!t.attCountInfo)return[];t.attCountInfo.map(function(t){P(t.attColumnName)?e.form.cfiInfo[t.attColumnName]=t.attCount:e.form[t.attColumnName]=t.attCount})},ADD_FILES_NUMBER:function(e,t){P(t)?e.form.cfiInfo[t]++:e.form[t]++},SUBTRACT_FILES_NUMBER:function(e,t){P(t)?e.form.cfiInfo[t]--:e.form[t]--},SET_FORM_VERSION_NO:function(e,t){e.formVersionNo=t},SET_IS_REQUEST_NODE:function(e,t){e.isRequestNode=t},SET_LOCKER_ID:function(e,t){e.lockerId=t},SET_NODE_ID:function(e,t){e.nodeId=t},SET_RECALLABLE:function(e,t){e.recallable=t},SET_REJECTABLE:function(e,t){e.rejectable=t},SET_SUBMITABLE:function(e,t){e.submitable=t},SET_NOTIFY_HANDLEABLE:function(e,t){e.notifyHandleable=t},RESET_APPLY_STATE:function(e){e.form=Object.assign({},s),e.formVersionNo=void 0,e.isRequestNode=void 0,e.lockerId="",e.nodeId="",e.recallable=void 0,e.rejectable=void 0,e.submitable=void 0,e.paymentTermList=[],e.reviewHistory=[],e.workflowSteps=[]},SET_PAYMENT_TERM_LIST:function(e,t){e.paymentTermList=t},SET_CV_REQUEST_ORDER_ARRAY:function(e,t){"[object Array]"===Object.prototype.toString.call(t)?w(e.form,{cbiRequestedCvOrderNo:t.map(function(e){return e.value}).join(",")}):w(e.form,{cbiRequestedCvOrderNoArray:t?t.split(",").map(function(e){return{id:Date.now(),value:e}}):[{id:Date.now(),value:""}]})},SET_WORK_FLOW_STEPS:function(e,t){e.workflowSteps=t}},N={getDraftInitForm:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.commit,n("RESET_APPLY_STATE"),e.next=4,d["a"].getDraftInitForm(r);case 4:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],o&&(u.data&&!u.data.cfiInfo&&(u.data.cfiInfo={id:null,cfiYearN1PaymentRecord:null,cfiPayHistoryWithChevron:null,cfiDsoInChevronChina:null,cfiQuickRatio:null,cfiCurrentRatio:null,cfiDailySales:null,cfiNetWorkingCapitalCycle:null,cfiCashFlowCoverage:null,cfiTangibleNetWorthRatioG32:null,cfiApDays:null,cfiTangibleNetWorth:null,cfiCurrentLiabilityToEquity:null,cfiLongTermLiabilityTotalAssetsRatio:null,cfiLiablitiesAssets:null,cfiEquityRatio:null,cfiInventoryTurnover:null,cfiDaysInInventory:null,cfiAccountReceivableTrunover:null,cfiDaysInAccountsReceivable:null,cfiSaleCurrentAssets:null,cfiAssetTurnover:null,cfiProfitMargin:null,cfiAfterTaxProfitRatio:null,cfiReturnOnEquity:null,cfiAssetTurnoverNetSalesToTotalAssets:null,cfiWorkingCapital:null,cfiEquity:null,cfiWorkingAssets:null,cfiEstimatedValue:null,cfiCreditIndex:null,cfiCreditLimitEstimatedValue:null,cfiCalculatedCreditLimitPerCreditPolicy:null,cfiCurrentExposure:null,cfiCvAmount:null,cfiScreenshotOfCurrentExposureAttId:null,othersAttId:null,cfiRecCreditLimitOfCurrentYear:null,cfiRecCreditPaymentTerm:null,cfiRecAddTempCreditLimit:null,cfiRecTempPaymentTerm:null,cfiTotalScore:null,createTime:null,updateTime:null,cfiCommentsFromCredit:null,cfiConfirmedCreditLimitOfCurrentYear:null,cfiConfirmedPaymentTermOfCurrentYear:null,cfiConfirmedTempCreditLimit:null,cfiConfirmedTempPaymentTerm:null,cfiConfirmedExpiredDate:null,cfiUploadArtAttId:null,cfiUploadInvestigationReportAttId:null}),n("SET_CV_REQUEST_ORDER_ARRAY",u.data&&u.data.cbiRequestedCvOrderNo),n("UPDATE_APPLY_FORM",u.data),n("UPDATE_UPLOAD_FILE_NUMBER",u.data)),e.abrupt("return",[o,u]);case 10:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),getCreditApply:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u,s,p,l,f,m,b,y,h,v;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.commit,n("RESET_APPLY_STATE"),e.next=4,d["a"].getCreditApply(r);case 4:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],s=u.form,p=u.formVersionNo,l=u.isRequestNode,f=u.lockerId,m=u.nodeId,b=u.recallable,y=u.rejectable,h=u.submitable,v=u.notifyHandleable,o&&(n("SET_CV_REQUEST_ORDER_ARRAY",s&&s.cbiRequestedCvOrderNo),n("UPDATE_APPLY_FORM",s),n("UPDATE_UPLOAD_FILE_NUMBER",s),n("SET_FORM_VERSION_NO",p),n("SET_IS_REQUEST_NODE",l),n("SET_LOCKER_ID",f),n("SET_NODE_ID",m),n("SET_RECALLABLE",b),n("SET_REJECTABLE",y),n("SET_SUBMITABLE",h),n("SET_NOTIFY_HANDLEABLE",v)),e.abrupt("return",[o,u]);case 11:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),getReviewProcess:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return t.state,e.next=3,d["a"].getReviewProcess(r);case 3:return n=e.sent,a=Object(c["a"])(n,2),i=a[0],o=a[1],e.abrupt("return",[i,o]);case 8:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),getWorkflowStepInstance:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return t.state,n=t.commit,e.next=3,d["a"].getWorkflowStepInstance(r);case 3:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],n("SET_WORK_FLOW_STEPS",u.resultLst),e.abrupt("return",[o,u]);case 9:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),getReviewHistory:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.state,e.next=3,p["a"].getReviewHistory(r);case 3:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],o&&(n.reviewHistory=u.result.resultLst),e.abrupt("return",[o,u]);case 9:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),getWorkflowStepHistory:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.state,e.next=3,d["a"].getWorkflowStepHistory(r);case 3:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],o&&(n.reviewHistory=u.resultLst),e.abrupt("return",[o,u]);case 9:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),saveApply:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u,s,p,l;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.state,a=t.commit,i=q(n.form,r),e.next=4,d["a"].saveApply({form:i,workflowLockerId:n.lockerId,formVersionNo:n.formVersionNo,nodeId:n.nodeId,saveForm:!0});case 4:return o=e.sent,u=Object(c["a"])(o,2),s=u[0],p=u[1],s&&(l=p.formVersionNo,a("UPDATE_APPLY_FORM",p.data),a("SET_FORM_VERSION_NO",l)),e.abrupt("return",[s,p]);case 10:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),releaseOrder:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u,s;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.state,a=Object.assign(n.form,r),e.next=4,d["a"].releaseOrder(a);case 4:return i=e.sent,o=Object(c["a"])(i,2),u=o[0],s=o[1],e.abrupt("return",[u,s]);case 9:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),submitApply:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u,s,p,l,f;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.state,a=g(q(n.form)),i=Object(c["a"])(a,2),o=i[0],u=i[1],o){e.next=4;break}return e.abrupt("return",[!1,u]);case 4:return e.next=6,d["a"].submitApply({form:u,workflowLockerId:n.lockerId,formVersionNo:n.formVersionNo,nodeId:n.nodeId,saveForm:!0,remark:r?r.comment:""});case 6:return s=e.sent,p=Object(c["a"])(s,2),l=p[0],f=p[1],e.abrupt("return",l?[l,f]:[l,f.errorMsg]);case 11:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),recallApply:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.state,e.next=3,d["a"].recallApply({form:q(r.form),workflowLockerId:r.lockerId,formVersionNo:r.formVersionNo,nodeId:r.nodeId});case 3:return n=e.sent,a=Object(c["a"])(n,2),i=a[0],o=a[1],e.abrupt("return",[i,o]);case 8:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}(),rejectApply:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u,s,p,l,f;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.state,a=S(q(n.form)),i=Object(c["a"])(a,2),o=i[0],u=i[1],o){e.next=4;break}return e.abrupt("return",[!1,u]);case 4:return e.next=6,d["a"].rejectApply({form:u,workflowLockerId:n.lockerId,formVersionNo:n.formVersionNo,nodeId:n.nodeId,saveForm:!0,remark:r?r.comment:""});case 6:return s=e.sent,p=Object(c["a"])(s,2),l=p[0],f=p[1],e.abrupt("return",[l,f]);case 11:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}(),calcFinanceInfo:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,u,s,p,l,f,m,b,y,h;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.state,a=t.commit,i=o.a.service({lock:!0,fullscreen:!0,background:"RGBA(0,0,0,0.5)",text:"calculating"}),u=!1,s=1e3,p=(new Date).getTime(),setTimeout(function(){u&&i.close()},s),l=Object.assign(n.form,{processInfo:r}),e.next=9,d["a"].calcFinanceInfo(l);case 9:return f=e.sent,m=Object(c["a"])(f,2),b=m[0],y=m[1],b&&a("UPDATE_APPLY_FORM",y.result&&y.result.data),h=(new Date).getTime(),h-p<s?u=!0:i.close(),e.abrupt("return",[b,y]);case 17:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}()},x={state:j,mutations:L,actions:N,getters:_},U=r("d225"),D=r("b0b4"),F=r("a1bd"),B=function(){function e(){Object(U["a"])(this,e)}return Object(D["a"])(e,[{key:"getUserInfo",value:function(){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditCommonService.getApplicationPreparedInformation",params:[]}})}},{key:"getLoginUser",value:function(){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:2,jsonrpc:"2.0",method:"userService.getLoginUser",params:[]}})}}]),e}(),W=new B,M={user:{roleList:[],preparedbyUserId:"",preparedBy:""},loginToken:""},V={userInfo:function(e){return e.user||{}},userToken:function(){var e="";return e},userId:function(){return M.user.preparedbyUserId},userName:function(){return M.user.preparedBy},currentLoginToken:function(){return M.loginToken},isAdmin:function(e){return e.user&&1===e.user.preparedbyUserId}},Y={UPDATE_USER_INFO:function(e,t){e.user=t},SET_LOGIN_USER_TOKEN:function(e,t){e.loginToken=t}},H={getUserInfo:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.commit,e.next=3,W.getUserInfo();case 3:return n=e.sent,a=Object(c["a"])(n,2),i=a[0],o=a[1],i&&r("UPDATE_USER_INFO",o.result),e.abrupt("return",[i,o]);case 9:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}(),getLoginUser:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o,u,s;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.commit,e.next=3,W.getLoginUser();case 3:return n=e.sent,a=Object(c["a"])(n,2),i=a[0],o=a[1],i&&o.result&&(u=o.result.data,s=u.token,r("SET_LOGIN_USER_TOKEN",s)),e.abrupt("return",[i,o]);case 9:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}()},G={state:M,mutations:Y,actions:H,getters:V},$=(r("7f7f"),function(){function e(){Object(U["a"])(this,e)}return Object(D["a"])(e,[{key:"getUploadFileList",value:function(e){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryAttListByAttColumnName",params:[{requestNo:e.requestNo,attColumnName:e.name}]}})}},{key:"deleteUploadFileList",value:function(e){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.deleteAttById",params:[{id:e.id}]}})}}]),e}()),Q=new $,K={files:[],fileName:"",visible:!1,disabled:!1},J={showUploadDialog:function(e){return e.visible},uploadFileList:function(e){return e.files},uploadFileName:function(e){return e.fileName},allowUploadFile:function(e){return!e.disabled}},Z={UPDATE_UPLOAD_DIALOG_VISIBLE:function(e,t){e.visible=t},UPDATE_UPLOAD_FILE_NAME:function(e,t){e.fileName=t},DISABLED_UPLOAD_BUTTON:function(e,t){e.disabled=t},RESET_UPLOAD_FILE:function(e,t){e.files=t},DELETE_UPLOAD_FILE:function(e,t){e.files=e.files.filter(function(e){return e.id!==t.id})},UPDATE_UPLOAD_FILE:function(e,t){t.map(function(t){var r=e.files.find(function(e){return e.id===t.id});r>=0?e.files[r]=t:e.files.unshift(t)})}},z={getUploadFileList:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o,u,s;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.state,n=t.getters,a=t.commit,e.next=3,Q.getUploadFileList({requestNo:n.applyForm.requestNo,name:r.fileName});case 3:return i=e.sent,o=Object(c["a"])(i,2),u=o[0],s=o[1],u&&a("RESET_UPLOAD_FILE",s.result.resultLst),e.abrupt("return",[u,s]);case 9:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}(),deleteUploadFile:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t,r){var n,a,i,o,u,s;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=t.state,a=t.commit,e.next=3,Q.deleteUploadFileList({id:r.id});case 3:return i=e.sent,o=Object(c["a"])(i,2),u=o[0],s=o[1],u&&(a("DELETE_UPLOAD_FILE",r),a("SUBTRACT_FILES_NUMBER",n.fileName)),e.abrupt("return",[u,s]);case 9:case"end":return e.stop()}},e)}));function t(t,r){return e.apply(this,arguments)}return t}()},X={state:K,mutations:Z,actions:z,getters:J},ee=function(){function e(){Object(U["a"])(this,e)}return Object(D["a"])(e,[{key:"getAbsentInfo",value:function(e){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.queryAbsentInfo",params:[e]}})}},{key:"updateAbsentInfo",value:function(e){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.saveAbsentInfo",params:[e]}})}},{key:"deleteAbsentInfo",value:function(e){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",data:{id:1,jsonrpc:"2.0",method:"creditApplicationService.deleteAbsentInfo",params:[e]}})}}]),e}(),te=new ee,re={id:"",startTime:"",endTime:"",disabled:!1},ne={absentDate:function(e){return[e.startTime,e.endTime]},absentId:function(e){return e.id},absenting:function(e){var t=(new Date).getTime(),r=new Date(e.startTime).getTime(),n=new Date(e.endTime).getTime();return t>r&&t<n}},ae={RESET_ABSENT:function(e,t){t=t||{},e.id=t.id,e.startTime=t.startTime,e.endTime=t.endTime},UPDATE_ABSENT_DATE:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.startTime=t[0],e.endTime=t[1]}},ie={getAbsentInfo:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o,u;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.getters,n=t.commit,e.next=3,te.getAbsentInfo({userId:r.userId});case 3:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],o&&n("RESET_ABSENT",u.result.data),e.abrupt("return",[o,u]);case 9:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}(),updateAbsentInfo:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o,u,s;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.state,n=t.getters,a=t.commit,e.next=3,te.updateAbsentInfo({id:r.id,userId:n.userId,startTime:r.startTime,endTime:r.endTime});case 3:return i=e.sent,o=Object(c["a"])(i,2),u=o[0],s=o[1],u&&a("RESET_ABSENT",s.result.data),e.abrupt("return",[u,s]);case 9:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}(),deleteAbsentInfo:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o,u;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.state,n=t.commit,e.next=3,te.deleteAbsentInfo({id:r.id});case 3:return a=e.sent,i=Object(c["a"])(a,2),o=i[0],u=i[1],o&&(n("UPDATE_ABSENT_DATE"),n("RESET_ABSENT",{})),e.abrupt("return",[o,u]);case 9:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}()},oe={state:re,mutations:ae,actions:ie,getters:ne},ce=function(){function e(){Object(U["a"])(this,e)}return Object(D["a"])(e,[{key:"getPermissionWeight",value:function(){return Object(F["a"])({method:"post",path:"wxPublicRpc.do",contentType:"json",data:{id:2,jsonrpc:"2.0",method:"operationPermissionService.getOperationPermissionByUser",params:[null,"Credit.apply"]}})}}]),e}(),ue=new ce,se={permissionWeight:0},de={canSubmitAnnualCredit:function(e){return(1&e.permissionWeight)>0},canSubmitTempCredit:function(e){return(2&e.permissionWeight)>0},canSubmitCVCredit:function(e){return(4&e.permissionWeight)>0},canViewMyAppliedTab:function(e){return(8&e.permissionWeight)>0},canViewMyApprovalTab:function(e){return(16&e.permissionWeight)>0},canViewAllTab:function(e){return(32&e.permissionWeight)>0},canOnlyViewApproval:function(e,t){return!t.canViewMyAppliedTab&&t.canViewMyApprovalTab},canReassign:function(e,t){return(64&e.permissionWeight)>0&&!t.isApplyProcessFinished},isApplyAgency:function(e){return(128&e.permissionWeight)>0},canDownloadList:function(e){return(256&e.permissionWeight)>0},isCreditTeamRole:function(){return(512&se.permissionWeight)>0},canAbsent:function(e){return(1024&e.permissionWeight)>0},canNotifySalesManager:function(e,t){return(2048&e.permissionWeight)>0&&t.isApplyProcessFinished}},pe={SET_PERMISSION_WEIGHT:function(e,t){e.permissionWeight=t}},le={getCreditPermissions:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var r,n,a,i,o;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return r=t.commit,e.next=3,ue.getPermissionWeight();case 3:n=e.sent,a=Object(c["a"])(n,2),i=a[0],o=a[1],i&&r("SET_PERMISSION_WEIGHT",o.result.weight);case 8:case"end":return e.stop()}},e)}));function t(t){return e.apply(this,arguments)}return t}()},fe={state:se,mutations:pe,actions:le,getters:de},me={page:"todo",requestor:""},be={fromPage:function(e){return e.page},fromRequestor:function(e){return e.requestor}},ye={SET_FROM_PAGE:function(e,t){e.page=t},SET_FROM_REQUESTOR:function(e,t){e.requestor=t}},he={state:me,mutations:ye,getters:be},ve={apply:x,user:G,upload:X,absent:oe,permission:fe,list:he};n["default"].use(a["a"]);t["a"]=new a["a"].Store({modules:ve})},a1bd:function(e,t,r){"use strict";var n=r("fcf0").default;t["a"]=n},fcf0:function(e,t,r){"use strict";r.r(t);r("6b54");var n=r("768b"),a=r("bc3a"),i=r.n(a),o=r("6d9e"),c=(r("96cf"),r("3b8d")),u=(r("46a1"),r("450d"),r("e5f2")),s=r.n(u),d=Object({NODE_ENV:"production",BASE_URL:""}).VUE_APP_ROOT_API,p=2e4,l={time:0,notify:null};function f(e){var t=(new Date).getTime();t-l.time>5e3&&(s.a.error(e),l.time=t)}function m(){o["a"].getters.env.app?(H.$removePrefs(function(){},"user_info"),H.$clearStorage(),H.$openWin("login_head","../login/login_head.html"),H.$toast("Login information has expired, please log in again")):top&&(top.location="/logout.do")}var b=function(){var e=Object(c["a"])(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:if(!(t.data.indexOf('action="login.do"')>-1)){e.next=2;break}return e.abrupt("return",m());case 2:return e.abrupt("return",[!1]);case 3:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),y=function(){var e=Object(c["a"])(regeneratorRuntime.mark(function e(t){var r;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.data,r){e.next=5;break}"Request failed with status code 502"===t.message?f({title:"An error has occurred",duration:5e3,position:"bottom-right",message:"The server failed to respond, please contact the manager"}):f({title:"An error has occurred",duration:5e3,position:"bottom-right",message:"Network exception or server response failed, please try again later"}),e.next=18;break;case 5:if(!r.error||0===r.error.code){e.next=9;break}f({title:"An error has occurred",duration:5e3,position:"bottom-right",message:"Network exception or server response failed, please try again later"}),e.next=18;break;case 9:if(!r.result||"success"===r.result.code){e.next=17;break}if("invalidToken"!==r.result.code){e.next=14;break}return e.abrupt("return",m());case 14:f({title:"An error has occurred",duration:5e3,position:"bottom-right",message:r.result.errorMsg||"Network exception or server response failed, please try again later"});case 15:e.next=18;break;case 17:r.errorMsg&&f({title:"An error has occurred",duration:5e3,position:"bottom-right",message:r.errorMsg});case 18:return e.abrupt("return",[!1]);case 19:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}();function h(e,t){var r=e.data;return e.headers["content-type"].indexOf("text/html")>-1?b(e).then(function(r){var a=Object(n["a"])(r,1),i=a[0];i||t([!1,e.data])}):r.result&&"success"!==r.result.code||r.error&&0!==r.error.code||r.code&&"success"!==r.code?y(e).then(function(r){var a=Object(n["a"])(r,1),i=a[0];i||t([!1,e.data])}):void t([!0,e.data])}function v(e){var t=e.method,r=void 0===t?"get":t,a=e.path,c=e.params,u=void 0===c?null:c,s=e.data,d=void 0===s?null:s,p=e.responseType,l=e.contentType;return new Promise(function(e){try{u="get"===r?d:u,o["a"].getters.userToken&&(u=Object.assign({appToken:o["a"].getters.userToken},u));var t="";"json"===l?t="application/json; charset=utf-8":"form"===l&&(t="application/x-www-form-urlencoded; charset=utf-8");var c={method:r,url:"/"+a,params:u,data:/put|post|patch/.test(r)?d:""};t&&(c.headers={"Content-Type":t,Accept:"*/*"}),p&&(c.responseType=p),"form"===l&&(c.transformRequest=[function(e){var t="";for(var r in e)t+=encodeURIComponent(r)+"="+("[object Array]"===Object.prototype.toString.call(e[r])?encodeURIComponent(JSON.stringify(e[r])):encodeURIComponent(e[r]))+"&";return t}]),i()(c).then(function(t){h(t,e)}).catch(function(t){return y(t).then(function(r){var a=Object(n["a"])(r,1),i=a[0];i||e([!1,t.data])})})}catch(s){e([!1,s])}})}i.a.defaults.baseURL=d,i.a.defaults.headers.common["Content-Type"]="application/json; charset=utf-8",i.a.defaults.timeout=p,i.a.defaults.withCredentials=!0;t["default"]=v}});
//# sourceMappingURL=app.e564eb76.js.map