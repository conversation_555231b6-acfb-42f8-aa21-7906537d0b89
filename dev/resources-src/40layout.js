;(function($){
	/**
	 * 布局器。高度和宽度均不支持自适应。分north, south, west, east, center 5类区域。<br/>
	 * 其中center区有且仅有一个，其他的可有多个，并且north和south区不能滚动，east和west区的宽度固定
	 */
	UI.Layout = UI.extend(UI.DomBase, {
		key: 'ui.layout',
		NORTH_CLS: 'ui-layout-north',
		SOUTH_CLS: 'ui-layout-south',
		CENTER_CLS: 'ui-layout-center',
		WEST_CLS: 'ui-layout-west',
		EAST_CLS: 'ui-layout-east',
		configEvents: ['beforeresize', 'onresize'],
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), p = this.newInstance($d, opts);
			if(p.east && p.east.length == 0){
				p.east = null;
			}
			if(p.west && p.west.length == 0){
				p.west = null;
			}
			if(p.north && p.north.length == 0){
				p.north = null;
			}
			if(p.south && p.south.length == 0){
				p.south = null;
			}
			if(p.center.length != 1){
				throw 'UI.Layout初始化失败。' + (p.center.length == 0 ? 'Layout无center区块' : ('Layout有' + p.center.length + "个区块"));
			}
			if(p.east || p.west){
				if(p.north || p.south){
					p._$centerWrapper = $('<div class="ui-layout-middle-wrapper"></div>').insertBefore(p.center.first());
					if(p.west){
						p._$centerWrapper.append(p.west.css({'float':'left'}));
					}
					p._$centerWrapper.append(p.center.css({'float':'left'}));
					if(p.east){
						p._$centerWrapper.append(p.east.css({'float':'left'}));
					}
					p._$centerWrapper.append('<div style="clear: both;"/>');
				}
			}
			//初始化north区
			if(p.north){
				p.north.each(function(){
					var $d = $(this), instance = UI.Panel.init(this, {
						scrollType: 'none',
						resize: function(){
							this._lheight = this.getHeight();
							p.trigger('resize');
						}
					});
				});
			}
			//初始化south区
			if(p.south){
				p.south.each(function(){
					var $d = $(this), instance = UI.Panel.init(this, {
						scrollType: 'none',
						resize: function(){
							this._lheight = instance.getHeight();
							p.trigger('resize');
						}
					});
				});
			}
			//初始化west区
			if(p.west){
				p.west.each(function(){
					var instance = UI.Panel.init(this);
					instance._lwidth = instance.getWidth();
				});
			}
			//初始化east区
			if(p.east){
				p.east.each(function(){
					var instance = UI.Panel.init(this);
					instance._lwidth = instance.getWidth();
				});
			}
			//初始化center区
			p.center.each(function(){
				var instance = UI.Panel.init(this);
			});
			//初始化scroll
			if(p.scrollType != 'none'){
//				p.scroll = UI.Scrollable.init(dom);
			}
			//添加自适应功能
			if(p.fit){
				UI.addFitAbility(p, p.scroll ? p.scroll.wrapper : $d, function(width, height){
					p.resize(width, height);
				});
			}
			return p;
		},
		createInitInstance: function($d){
			return $.extend({}, this.defaults, {
				north: $d.find('>.' + this.NORTH_CLS),
				south: $d.find('>.' + this.SOUTH_CLS),
				center: $d.find('>.' + this.CENTER_CLS),
				west: $d.find('>.' + this.WEST_CLS),
				east: $d.find('>.' + this.EAST_CLS)
			});
		},
		//调整滚动状态
		_fixScroll: function(props){
			if(props.scroll && !props._fixScrollClose){
				props.scroll.trigger('onresize');
			}
		},
		base: {
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度。undefined表示控件宽度不变
			 * @param height [可选]控件新高度。不填表示高度不变
			 */
			resize: function(width, height){
				//1. 修正参数
				if(width !== undefined && width < this.minWidth){
					width = this.minWidth;
				}
				if(height !== undefined && height < this.minHeight){
					height = this.minHeight;
				}
				//2. 判断尺寸是否有变化
				if(this._sizeInited && (this.width == width || width === undefined) 
						&& (this.height == height || height === undefined)){
					return;
				}
				//3. 准备工作
				this.trigger('beforeresize', width, height);
				this.suspend('resize'); //暂停resize的触发事件，防止变动尺寸中触发resize事件
				var thiz = this;

				//4. 重新设置宽度
				if(!this._sizeInited || (width !== undefined && width != this.width)){
					this.width = width;
					//4.1 设置整体宽度
					this.setWidth(this.width);
					this._contentWidth = UI.getInnerWidth(this.element)
					//4.2 重置north和south区宽度
					if(this.north){
						this.north.each(function(){
							UI.Panel.getInstance($(this)).resize(thiz._contentWidth);
						});
					}
					if(this.south){
						this.south.each(function(){
							UI.Panel.getInstance($(this)).resize(thiz._contentWidth);
						});
					}
				}
				//5. 更新高度
				if(height !== undefined){
					this.height = height;
				}
//				this.center.each(function(){
//					UI.Panel.getInstance($(this)).resize(centerWidth);
//				});
				//6. 触发resize事件
				this._fixScrollClose = true; //关闭滚动条的调整，需要手动调整尺寸
				this.start('resize');
				this.trigger('resize');
				//7. 更新scroll尺寸
				if(this.scroll){
					this.scroll.resize(instance.width, instance.height);
				}
				this._fixScrollClose = false;
				this._sizeInited = true;
			},
			beforeEvents: {
				resize: function(){
					var height = this.height, centerWidth = this._contentWidth, thiz = this;
					//1. 计算center面板height
					if(height > 0){
						if(height < this.minHeight){
							this.height = this.minHeight;
						}else{
							this.height = height;
						}
						this.setHeight(this.height - 0.5);
						height = UI.getInnerHeight(this.element); 
						if(this.north){
							this.north.each(function(){
								var $d = $(this), instance = UI.Panel.getInstance($d);
								if(!instance._lheight){
									instance._lheight = instance.getHeight();
								}
								height -= instance._lheight;
							});
						}
						if(this.south){
							this.south.each(function(){
								var $d = $(this), instance = UI.Panel.getInstance($d);
								if(!instance._lheight){
									instance._lheight = instance.getHeight();
								}
								height -= instance._lheight;
							});
						}
						//重置west区高度
						if(this.west){
							this.west.each(function(){
								UI.Panel.getInstance($(this)).resize(undefined, height);
							});
						}
						//重置east区高度
						if(this.east){
							this.east.each(function(){
								UI.Panel.getInstance($(this)).resize(undefined, height);
							});
						}
					}
					//2. 计算center面板width
					if(this.east){
						this.east.each(function(){
							var $d = $(this), ins = UI.Panel.getInstance($d);
							if(!ins._lwidth){
								ins._lwidth = ins.getWidth();
							}
							centerWidth -= ins._lwidth;
						});
					}
					if(this.west){
						this.west.each(function(){
							var $d = $(this), ins = UI.Panel.getInstance($d);
							if(!ins._lwidth){
								ins._lwidth = ins.getWidth();
							}
							centerWidth -= ins._lwidth;
						});
					}
					//3. 重置center区尺寸
					this.center.each(function(){
						UI.Panel.getInstance($(this)).resize(centerWidth, height);
					});
				}
			},
			afterEvents: {
				resize: function(){
					//调整layout滚动状态
					UI.Layout._fixScroll(this);
				}
			},
			/**
			 * 获取元素高度
			 * @return {number} 返回元素高度
			 */
			getHeight: function(){
				if(this.scroll){
					return UI.getOunterHeight(this.scroll.wrapper);
				}else{
					return UI.getOunterHeight(this.element);
				}
			},
			/**
			 * 获取元素宽度
			 * @return {number} 返回元素宽度
			 */
			getWidth: function($d){
				if(this.scroll){
					return UI.getOunterWidth(this.scroll.wrapper);
				}else{
					return UI.getOunterWidth(this.element);
				}
			},
			/**
			 * 设置面板宽度
			 * @param width 面板新宽度
			 */
			setWidth: function(width){
				if(this.scroll){
					return UI.setWidth(this.scroll.wrapper, width);
				}else{
					return UI.setWidth(this.element, width);
				}
			},
			/**
			 * 设置面板高度
			 * @param width 面板新高度
			 */
			setHeight: function(height){
				if(this.scroll){
					return UI.setHeight(this.scroll.wrapper, height);
				}else{
					return UI.setHeight(this.element, height);
				}
			}
		},
		//默认属性
		defaults: {
			fit: true,
			north: null,
			south: null,
			west: null,
			east: null,
			center: null,
			minWidth: 0, //确保north和south区不出现滚动条
			minHeight: 0, //确保north和south区不出现滚动条
			srollType: 'none'
		}
	});
})(jQuery);