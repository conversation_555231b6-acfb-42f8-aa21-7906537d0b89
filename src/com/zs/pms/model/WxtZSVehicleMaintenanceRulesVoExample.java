package com.zs.pms.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxtZSVehicleMaintenanceRulesVoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WxtZSVehicleMaintenanceRulesVoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNull() {
            addCriterion("t_model_id is null");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNotNull() {
            addCriterion("t_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andTModelIdEqualTo(String value) {
            addCriterion("t_model_id =", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotEqualTo(String value) {
            addCriterion("t_model_id <>", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThan(String value) {
            addCriterion("t_model_id >", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_id >=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThan(String value) {
            addCriterion("t_model_id <", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThanOrEqualTo(String value) {
            addCriterion("t_model_id <=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLike(String value) {
            addCriterion("t_model_id like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotLike(String value) {
            addCriterion("t_model_id not like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdIn(List<String> values) {
            addCriterion("t_model_id in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotIn(List<String> values) {
            addCriterion("t_model_id not in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdBetween(String value1, String value2) {
            addCriterion("t_model_id between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotBetween(String value1, String value2) {
            addCriterion("t_model_id not between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelNameIsNull() {
            addCriterion("t_model_name is null");
            return (Criteria) this;
        }

        public Criteria andTModelNameIsNotNull() {
            addCriterion("t_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andTModelNameEqualTo(String value) {
            addCriterion("t_model_name =", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotEqualTo(String value) {
            addCriterion("t_model_name <>", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameGreaterThan(String value) {
            addCriterion("t_model_name >", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_name >=", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameLessThan(String value) {
            addCriterion("t_model_name <", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameLessThanOrEqualTo(String value) {
            addCriterion("t_model_name <=", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameLike(String value) {
            addCriterion("t_model_name like", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotLike(String value) {
            addCriterion("t_model_name not like", value, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameIn(List<String> values) {
            addCriterion("t_model_name in", values, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotIn(List<String> values) {
            addCriterion("t_model_name not in", values, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameBetween(String value1, String value2) {
            addCriterion("t_model_name between", value1, value2, "tModelName");
            return (Criteria) this;
        }

        public Criteria andTModelNameNotBetween(String value1, String value2) {
            addCriterion("t_model_name not between", value1, value2, "tModelName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("brand_name is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("brand_name is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("brand_name =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("brand_name <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("brand_name >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("brand_name >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("brand_name <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("brand_name <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("brand_name like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("brand_name not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("brand_name in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("brand_name not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("brand_name between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("brand_name not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNull() {
            addCriterion("factory_name is null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIsNotNull() {
            addCriterion("factory_name is not null");
            return (Criteria) this;
        }

        public Criteria andFactoryNameEqualTo(String value) {
            addCriterion("factory_name =", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotEqualTo(String value) {
            addCriterion("factory_name <>", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThan(String value) {
            addCriterion("factory_name >", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("factory_name >=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThan(String value) {
            addCriterion("factory_name <", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLessThanOrEqualTo(String value) {
            addCriterion("factory_name <=", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameLike(String value) {
            addCriterion("factory_name like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotLike(String value) {
            addCriterion("factory_name not like", value, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameIn(List<String> values) {
            addCriterion("factory_name in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotIn(List<String> values) {
            addCriterion("factory_name not in", values, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameBetween(String value1, String value2) {
            addCriterion("factory_name between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andFactoryNameNotBetween(String value1, String value2) {
            addCriterion("factory_name not between", value1, value2, "factoryName");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(String value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(String value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(String value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(String value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLike(String value) {
            addCriterion("vehicle_type like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotLike(String value) {
            addCriterion("vehicle_type not like", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<String> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<String> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(String value1, String value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(String value1, String value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIsNull() {
            addCriterion("vehicle_series is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIsNotNull() {
            addCriterion("vehicle_series is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesEqualTo(String value) {
            addCriterion("vehicle_series =", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotEqualTo(String value) {
            addCriterion("vehicle_series <>", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesGreaterThan(String value) {
            addCriterion("vehicle_series >", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_series >=", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLessThan(String value) {
            addCriterion("vehicle_series <", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLessThanOrEqualTo(String value) {
            addCriterion("vehicle_series <=", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesLike(String value) {
            addCriterion("vehicle_series like", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotLike(String value) {
            addCriterion("vehicle_series not like", value, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesIn(List<String> values) {
            addCriterion("vehicle_series in", values, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotIn(List<String> values) {
            addCriterion("vehicle_series not in", values, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesBetween(String value1, String value2) {
            addCriterion("vehicle_series between", value1, value2, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andVehicleSeriesNotBetween(String value1, String value2) {
            addCriterion("vehicle_series not between", value1, value2, "vehicleSeries");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLIsNull() {
            addCriterion("engine_displacement_l is null");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLIsNotNull() {
            addCriterion("engine_displacement_l is not null");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLEqualTo(String value) {
            addCriterion("engine_displacement_l =", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotEqualTo(String value) {
            addCriterion("engine_displacement_l <>", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLGreaterThan(String value) {
            addCriterion("engine_displacement_l >", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLGreaterThanOrEqualTo(String value) {
            addCriterion("engine_displacement_l >=", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLLessThan(String value) {
            addCriterion("engine_displacement_l <", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLLessThanOrEqualTo(String value) {
            addCriterion("engine_displacement_l <=", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLLike(String value) {
            addCriterion("engine_displacement_l like", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotLike(String value) {
            addCriterion("engine_displacement_l not like", value, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLIn(List<String> values) {
            addCriterion("engine_displacement_l in", values, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotIn(List<String> values) {
            addCriterion("engine_displacement_l not in", values, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLBetween(String value1, String value2) {
            addCriterion("engine_displacement_l between", value1, value2, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineDisplacementLNotBetween(String value1, String value2) {
            addCriterion("engine_displacement_l not between", value1, value2, "engineDisplacementL");
            return (Criteria) this;
        }

        public Criteria andEngineIsNull() {
            addCriterion("engine is null");
            return (Criteria) this;
        }

        public Criteria andEngineIsNotNull() {
            addCriterion("engine is not null");
            return (Criteria) this;
        }

        public Criteria andEngineEqualTo(String value) {
            addCriterion("engine =", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotEqualTo(String value) {
            addCriterion("engine <>", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineGreaterThan(String value) {
            addCriterion("engine >", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineGreaterThanOrEqualTo(String value) {
            addCriterion("engine >=", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineLessThan(String value) {
            addCriterion("engine <", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineLessThanOrEqualTo(String value) {
            addCriterion("engine <=", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineLike(String value) {
            addCriterion("engine like", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotLike(String value) {
            addCriterion("engine not like", value, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineIn(List<String> values) {
            addCriterion("engine in", values, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotIn(List<String> values) {
            addCriterion("engine not in", values, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineBetween(String value1, String value2) {
            addCriterion("engine between", value1, value2, "engine");
            return (Criteria) this;
        }

        public Criteria andEngineNotBetween(String value1, String value2) {
            addCriterion("engine not between", value1, value2, "engine");
            return (Criteria) this;
        }

        public Criteria andPartsNameIsNull() {
            addCriterion("parts_name is null");
            return (Criteria) this;
        }

        public Criteria andPartsNameIsNotNull() {
            addCriterion("parts_name is not null");
            return (Criteria) this;
        }

        public Criteria andPartsNameEqualTo(String value) {
            addCriterion("parts_name =", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameNotEqualTo(String value) {
            addCriterion("parts_name <>", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameGreaterThan(String value) {
            addCriterion("parts_name >", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameGreaterThanOrEqualTo(String value) {
            addCriterion("parts_name >=", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameLessThan(String value) {
            addCriterion("parts_name <", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameLessThanOrEqualTo(String value) {
            addCriterion("parts_name <=", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameLike(String value) {
            addCriterion("parts_name like", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameNotLike(String value) {
            addCriterion("parts_name not like", value, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameIn(List<String> values) {
            addCriterion("parts_name in", values, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameNotIn(List<String> values) {
            addCriterion("parts_name not in", values, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameBetween(String value1, String value2) {
            addCriterion("parts_name between", value1, value2, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsNameNotBetween(String value1, String value2) {
            addCriterion("parts_name not between", value1, value2, "partsName");
            return (Criteria) this;
        }

        public Criteria andPartsTypeIsNull() {
            addCriterion("parts_type is null");
            return (Criteria) this;
        }

        public Criteria andPartsTypeIsNotNull() {
            addCriterion("parts_type is not null");
            return (Criteria) this;
        }

        public Criteria andPartsTypeEqualTo(String value) {
            addCriterion("parts_type =", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeNotEqualTo(String value) {
            addCriterion("parts_type <>", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeGreaterThan(String value) {
            addCriterion("parts_type >", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("parts_type >=", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeLessThan(String value) {
            addCriterion("parts_type <", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeLessThanOrEqualTo(String value) {
            addCriterion("parts_type <=", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeLike(String value) {
            addCriterion("parts_type like", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeNotLike(String value) {
            addCriterion("parts_type not like", value, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeIn(List<String> values) {
            addCriterion("parts_type in", values, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeNotIn(List<String> values) {
            addCriterion("parts_type not in", values, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeBetween(String value1, String value2) {
            addCriterion("parts_type between", value1, value2, "partsType");
            return (Criteria) this;
        }

        public Criteria andPartsTypeNotBetween(String value1, String value2) {
            addCriterion("parts_type not between", value1, value2, "partsType");
            return (Criteria) this;
        }

        public Criteria andMileIsNull() {
            addCriterion("mile is null");
            return (Criteria) this;
        }

        public Criteria andMileIsNotNull() {
            addCriterion("mile is not null");
            return (Criteria) this;
        }

        public Criteria andMileEqualTo(Long value) {
            addCriterion("mile =", value, "mile");
            return (Criteria) this;
        }

        public Criteria andMileNotEqualTo(Long value) {
            addCriterion("mile <>", value, "mile");
            return (Criteria) this;
        }

        public Criteria andMileGreaterThan(Long value) {
            addCriterion("mile >", value, "mile");
            return (Criteria) this;
        }

        public Criteria andMileGreaterThanOrEqualTo(Long value) {
            addCriterion("mile >=", value, "mile");
            return (Criteria) this;
        }

        public Criteria andMileLessThan(Long value) {
            addCriterion("mile <", value, "mile");
            return (Criteria) this;
        }

        public Criteria andMileLessThanOrEqualTo(Long value) {
            addCriterion("mile <=", value, "mile");
            return (Criteria) this;
        }

        public Criteria andMileIn(List<Long> values) {
            addCriterion("mile in", values, "mile");
            return (Criteria) this;
        }

        public Criteria andMileNotIn(List<Long> values) {
            addCriterion("mile not in", values, "mile");
            return (Criteria) this;
        }

        public Criteria andMileBetween(Long value1, Long value2) {
            addCriterion("mile between", value1, value2, "mile");
            return (Criteria) this;
        }

        public Criteria andMileNotBetween(Long value1, Long value2) {
            addCriterion("mile not between", value1, value2, "mile");
            return (Criteria) this;
        }

        public Criteria andMonthIsNull() {
            addCriterion("month is null");
            return (Criteria) this;
        }

        public Criteria andMonthIsNotNull() {
            addCriterion("month is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEqualTo(Long value) {
            addCriterion("month =", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotEqualTo(Long value) {
            addCriterion("month <>", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThan(Long value) {
            addCriterion("month >", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanOrEqualTo(Long value) {
            addCriterion("month >=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThan(Long value) {
            addCriterion("month <", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThanOrEqualTo(Long value) {
            addCriterion("month <=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthIn(List<Long> values) {
            addCriterion("month in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotIn(List<Long> values) {
            addCriterion("month not in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthBetween(Long value1, Long value2) {
            addCriterion("month between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotBetween(Long value1, Long value2) {
            addCriterion("month not between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIsNull() {
            addCriterion("working_hour is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIsNotNull() {
            addCriterion("working_hour is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourEqualTo(String value) {
            addCriterion("working_hour =", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotEqualTo(String value) {
            addCriterion("working_hour <>", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourGreaterThan(String value) {
            addCriterion("working_hour >", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourGreaterThanOrEqualTo(String value) {
            addCriterion("working_hour >=", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourLessThan(String value) {
            addCriterion("working_hour <", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourLessThanOrEqualTo(String value) {
            addCriterion("working_hour <=", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourLike(String value) {
            addCriterion("working_hour like", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotLike(String value) {
            addCriterion("working_hour not like", value, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourIn(List<String> values) {
            addCriterion("working_hour in", values, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotIn(List<String> values) {
            addCriterion("working_hour not in", values, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourBetween(String value1, String value2) {
            addCriterion("working_hour between", value1, value2, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourNotBetween(String value1, String value2) {
            addCriterion("working_hour not between", value1, value2, "workingHour");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceIsNull() {
            addCriterion("working_hour_price is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceIsNotNull() {
            addCriterion("working_hour_price is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceEqualTo(String value) {
            addCriterion("working_hour_price =", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceNotEqualTo(String value) {
            addCriterion("working_hour_price <>", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceGreaterThan(String value) {
            addCriterion("working_hour_price >", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceGreaterThanOrEqualTo(String value) {
            addCriterion("working_hour_price >=", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceLessThan(String value) {
            addCriterion("working_hour_price <", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceLessThanOrEqualTo(String value) {
            addCriterion("working_hour_price <=", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceLike(String value) {
            addCriterion("working_hour_price like", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceNotLike(String value) {
            addCriterion("working_hour_price not like", value, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceIn(List<String> values) {
            addCriterion("working_hour_price in", values, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceNotIn(List<String> values) {
            addCriterion("working_hour_price not in", values, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceBetween(String value1, String value2) {
            addCriterion("working_hour_price between", value1, value2, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourPriceNotBetween(String value1, String value2) {
            addCriterion("working_hour_price not between", value1, value2, "workingHourPrice");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueIsNull() {
            addCriterion("working_hour_total_value is null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueIsNotNull() {
            addCriterion("working_hour_total_value is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueEqualTo(String value) {
            addCriterion("working_hour_total_value =", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueNotEqualTo(String value) {
            addCriterion("working_hour_total_value <>", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueGreaterThan(String value) {
            addCriterion("working_hour_total_value >", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueGreaterThanOrEqualTo(String value) {
            addCriterion("working_hour_total_value >=", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueLessThan(String value) {
            addCriterion("working_hour_total_value <", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueLessThanOrEqualTo(String value) {
            addCriterion("working_hour_total_value <=", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueLike(String value) {
            addCriterion("working_hour_total_value like", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueNotLike(String value) {
            addCriterion("working_hour_total_value not like", value, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueIn(List<String> values) {
            addCriterion("working_hour_total_value in", values, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueNotIn(List<String> values) {
            addCriterion("working_hour_total_value not in", values, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueBetween(String value1, String value2) {
            addCriterion("working_hour_total_value between", value1, value2, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andWorkingHourTotalValueNotBetween(String value1, String value2) {
            addCriterion("working_hour_total_value not between", value1, value2, "workingHourTotalValue");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNull() {
            addCriterion("import_time is null");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNotNull() {
            addCriterion("import_time is not null");
            return (Criteria) this;
        }

        public Criteria andImportTimeEqualTo(Date value) {
            addCriterion("import_time =", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotEqualTo(Date value) {
            addCriterion("import_time <>", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThan(Date value) {
            addCriterion("import_time >", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("import_time >=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThan(Date value) {
            addCriterion("import_time <", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThanOrEqualTo(Date value) {
            addCriterion("import_time <=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeIn(List<Date> values) {
            addCriterion("import_time in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotIn(List<Date> values) {
            addCriterion("import_time not in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeBetween(Date value1, Date value2) {
            addCriterion("import_time between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotBetween(Date value1, Date value2) {
            addCriterion("import_time not between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}