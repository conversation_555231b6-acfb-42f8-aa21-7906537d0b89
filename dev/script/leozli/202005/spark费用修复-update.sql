--金富力新店开业礼包修复
/*SELECT
	poe.distributor_id,
	pde.POINT_VALUE,
	pde.CREATION_TIME,
  'cdm_newstore_open' application_type,
	pde.spark_amount,
	pde.online_amount,
	case when st.spark_budget>0 then pde.POINT_VALUE*250 else 0 end spark_amount,
	case when st.spark_budget>0 then 0 else pde.POINT_VALUE*250 end online_amount*/
	update pde set pde.spark_amount=(case when st.spark_budget>0 then pde.POINT_VALUE*250 else 0 end), pde.online_amount=(case when st.spark_budget>0 then 0 else pde.POINT_VALUE*250 end)
FROM wx_t_point_value_detail pde
	LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID 
	left join wx_t_partner_o2o_enterprise poe on pac.POINT_ACCOUNT_OWNER_ID = poe.partner_id
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=poe.distributor_id and st.sales_channel='Consumer'
WHERE pde.POINT_VALUE!= 0  
	AND pde.POINT_TYPE = 'CDM_STORE_OPEN_POINT'
	
--商用油油样申请
/*SELECT poe.distributor_id, wi.apply_time, total_amount, online_amount, spark_amount,
case when st.spark_budget>0 then total_amount else 0 end spark_amount,
	case when st.spark_budget>0 then 0 else total_amount end online_amount*/
	update osa set osa.spark_amount=0, osa.online_amount=total_amount
FROM wx_t_oil_sample_apply osa
	LEFT JOIN wx_t_partner_o2o_enterprise poe ON osa.customerId = poe.partner_id
	LEFT JOIN wx_t_workflow_instance wi ON osa.id = wi.form_key
WHERE osa.form_status >= 10
	AND wi.workflow_code = 'OIL_SAMPLE_APPLY'
	
--德乐店招结算金额
/*select *,
	case when st.spark_budget>0 then ca.settlement_amount else 0 end spark_amount,
	case when st.spark_budget>0 then 0 else ca.settlement_amount end online_amount*/
	update ca set ca.settlement_spark_amount=(case when st.spark_budget>0 then ca.settlement_amount else 0 end), ca.settlement_online_amount=(case when st.spark_budget>0 then 0 else ca.settlement_amount end)
from wx_t_mkt_ci_apply ca
left join wx_t_workflow_instance wi on ca.id=wi.form_key and wi.workflow_code='CI_APPLY' 
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=ca.distributor_id and st.sales_channel='Commercial'
where ca.form_status>=10 and ca.settlement_amount>0	
	
--德乐店招预算金额
	update ca set ca.quote_spark_amount=(case when st.spark_budget>0 then ca.budget_amount else 0 end), ca.quote_online_amount=(case when st.spark_budget>0 then 0 else ca.budget_amount end)
from wx_t_mkt_ci_apply ca
left join wx_t_workflow_instance wi on ca.id=wi.form_key and wi.workflow_code='CI_APPLY' 
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=ca.distributor_id and st.sales_channel='Commercial'
where ca.form_status>=10 and ca.budget_amount>0

--金富力店招结算金额
/*select *,
	case when st.spark_budget>0 then ca.settlement_amount else 0 end spark_amount,
	case when st.spark_budget>0 then 0 else ca.settlement_amount end online_amount*/
	update ca set ca.settlement_spark_amount=(case when st.spark_budget>0 then ca.settlement_amount else 0 end), ca.settlement_online_amount=(case when st.spark_budget>0 then 0 else ca.settlement_amount end)
from wx_t_mkt_cdm_apply ca
left join wx_t_workflow_instance wi on ca.id=wi.form_key and wi.workflow_code='CDM_APPLY' 
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=ca.distributor_id and st.sales_channel='Consumer'
where ca.form_status>=10 and ca.settlement_amount>0	
	
--金富力店招预算金额
	update ca set ca.quote_spark_amount=(case when st.spark_budget>0 then ca.quote_amount else 0 end), ca.quote_online_amount=(case when st.spark_budget>0 then 0 else ca.quote_amount end)
from wx_t_mkt_cdm_apply ca
left join wx_t_workflow_instance wi on ca.id=wi.form_key and wi.workflow_code='CDM_APPLY' 
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=ca.distributor_id and st.sales_channel='Consumer'
where ca.form_status>=10 and ca.quote_amount>0

--商用油新店开业礼包
/*SELECT 'CIO_New_Store_Package' AS expense_code
		, convert(varchar(6), t_activity.submit_time, 112) AS month
		, pe.distributor_id,
		t_new_store.total_expense AS total_amount,
		t_new_store.online_expense AS online_amount,
		t_new_store.sp_expense AS spark_amount,
		rel.region_name AS region_name*/
		update t_new_store set t_new_store.online_expense=t_new_store.total_expense,t_new_store.sp_expense=0
		from wx_v2_promote_activity t_activity
		INNER JOIN wx_v2_promote_xd_open_detail t_new_store ON t_new_store.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
		INNER JOIN view_customer_region_sales_channel rel ON pe.distributor_id = rel.distributor_id and rel.channel_weight & 2 >0
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=pe.distributor_id and st.sales_channel='Commercial'
	WHERE 1 = 1
		AND t_activity.status in (1,2)
		AND t_activity.activity_type = 'XDKYLB'
		AND t_activity.submit_time >= '2020-01-01'
--商用油路演消费者互动包
/*SELECT 'CIO_Customer_Package' AS expense_code
		, convert(varchar(6), t_activity.submit_time, 112) AS month
		, pe.distributor_id, 
		t_roadshow.total_expense AS total_amount,
		t_roadshow.online_expense AS online_amount,
		t_roadshow.sp_expense AS spark_amount,
		rel.region_name AS region_name*/
		update t_roadshow set t_roadshow.online_expense=t_roadshow.total_expense,t_roadshow.sp_expense=0
		from wx_v2_promote_activity t_activity 
		INNER JOIN wx_v2_promote_roadshow_activity_detail t_roadshow ON t_roadshow.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
		INNER JOIN view_customer_region_sales_channel rel ON pe.distributor_id = rel.distributor_id and rel.channel_weight & 2 >0
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=pe.distributor_id and st.sales_channel='Commercial'
	WHERE 1 = 1
		And t_activity.status in (1,2)
		AND t_activity.activity_type = 'LYXFLB'
		AND t_activity.submit_time >= '2020-01-01'
--商用油车队研讨会
/*SELECT 'CIO_Team_Workshop_Package' AS expense_code
		, convert(varchar(6), t_activity.submit_time, 112) AS month
		, pe.distributor_id, 
		t_seminar.total_fleet_expense AS total_amount,
		t_seminar.online_fleet_expense AS online_amount,
		t_seminar.sp_fleet_expense AS spark_amount,
		rel.region_name AS region_name*/
		update t_seminar set t_seminar.online_fleet_expense=(case when st.spark_budget>0 then 0 else t_seminar.total_fleet_expense end),t_seminar.sp_fleet_expense=(case when st.spark_budget>0 then t_seminar.total_fleet_expense else 0 end)
       from wx_v2_promote_activity t_activity
		INNER JOIN wx_v2_promote_seminar_activity_detail t_seminar ON t_seminar.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
		INNER JOIN view_customer_region_sales_channel rel ON pe.distributor_id = rel.distributor_id and rel.channel_weight & 2 >0
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=pe.distributor_id and st.sales_channel='Commercial'
	WHERE 1 = 1
		AND t_activity.status in(1,2)
		AND t_activity.activity_type = 'YTH'
		AND t_activity.submit_time >= '2020-01-01'
--商用油普通研讨会
/*SELECT 'CIO_Seminar_Package' AS expense_code
		, convert(varchar(6), t_activity.submit_time, 112) AS month
		, pe.distributor_id,
		t_seminar.total_general_expense AS total_amount,
		t_seminar.online_general_expense AS online_amount,
		t_seminar.sp_general_expense AS spark_amount,
		rel.region_name AS region_name*/
		update t_seminar set t_seminar.online_general_expense=(case when st.spark_budget>0 then 0 else t_seminar.total_general_expense end),t_seminar.sp_general_expense=(case when st.spark_budget>0 then t_seminar.total_general_expense else 0 end)
		from wx_v2_promote_activity t_activity 
		INNER JOIN wx_v2_promote_seminar_activity_detail t_seminar ON t_seminar.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
		INNER JOIN view_customer_region_sales_channel rel ON pe.distributor_id = rel.distributor_id and rel.channel_weight & 2 >0
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=pe.distributor_id and st.sales_channel='Commercial'
	WHERE 1 = 1
		AND t_activity.status in (1,2)
		AND t_activity.activity_type = 'YTH'
		AND t_activity.submit_time >= '2020-01-01'
--商用油优质研讨会
/*SELECT 'CIO_Seminar_Quality_Package' AS expense_code
		, convert(varchar(6), t_activity.submit_time, 112) AS month
		, pe.distributor_id,
		t_seminar.total_high_expense AS total_amount,
		t_seminar.online_high_expense AS online_amount,
		t_seminar.sp_high_expense AS spark_amount,
		rel.region_name AS region_name*/
		update t_seminar set t_seminar.online_high_expense=(case when st.spark_budget>0 then 0 else t_seminar.total_high_expense end),t_seminar.sp_high_expense=(case when st.spark_budget>0 then t_seminar.total_high_expense else 0 end)
		from wx_v2_promote_activity t_activity 
		INNER JOIN wx_v2_promote_seminar_activity_detail t_seminar ON t_seminar.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
		INNER JOIN view_customer_region_sales_channel rel ON pe.distributor_id = rel.distributor_id and rel.channel_weight & 2 >0
	left join (select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) spark_budget, t.distributor_id, t.sales_channel
		from wx_t_mkt_dealer_quarter t
		group by t.distributor_id, t.sales_channel) st on st.distributor_id=pe.distributor_id and st.sales_channel='Commercial'
	WHERE 1 = 1
		AND t_activity.status in(1,2)
		AND t_activity.activity_type = 'YTH'
		AND t_activity.submit_time >= '2020-01-01'

--费用看板-金富力店招申请看板
update s set s.biz_permission_weight=s.biz_permission_weight|'**********'
from wx_t_workflow_step s where workflow_code='CDM_APPLY' 
and step_code not in ('START','DIST_SUBMIT_MATERIAL','PROVIDER_SUBMIT_MATERIAL','FIN_APPROVE','END','DIST_SUBMIT_MATERIAL_SEMINARS','FIN_APPROVE_SEMINARS','FIN_APPROVE_LOCAL')

--费用看板-德乐店招申请看板
--select * 
update s set s.biz_permission_weight=s.biz_permission_weight|'**********'
from wx_t_workflow_step s where workflow_code='CI_APPLY' 
and step_code not in ('START','PROVIDER_SUBMIT_PREVIEW_MATERIAL','PROVIDER_SUBMIT_DESIGN_MATERIAL','DIST_SUBMIT_MATERIAL','END','FIN_APPROVE')
