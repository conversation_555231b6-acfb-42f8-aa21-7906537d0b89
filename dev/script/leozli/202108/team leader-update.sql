update [dw_sales_role] set [sales_cai_level]='SKCL-SKCL-LIZN-WXJY-WDLI',[supervisor_cai]='WXJY',[sales_role]='FLSR', [from_source]='PP' where sales_cai='WDLI'
update [dw_sales_role] set [sales_cai_level]='SKCL-SKCL-PZEN-YLEY-LMJD',[supervisor_cai]='YLEY',[sales_role]='FLSR', [from_source]='PP' where sales_cai='LMJD'
update [dw_sales_role] set [sales_cai_level]='SKCL-SKCL-PZEN-YLEY-ZWJL',[supervisor_cai]='YLEY',[sales_role]='FLSR', [from_source]='PP' where sales_cai='ZWJL'

delete r from dw_sales_role r where sales_cai in ('YOPE','PXUZ')

select * 
--delete r
from [PP_MID].[dbo].[syn_dw_to_pp_sales_role] r where r.sales_cai_level like 'SKCL-SKCL-YUAZ%' --where sales_cai='WDLI'
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_sales_role] r1 where r1.sales_cai_level like 'SKCL-SKCL-LIZN%' and r1.sales_cai=r.sales_cai)
and sales_cai !='NA'
--and sales_cai in ('OPCX', 'WDLI')