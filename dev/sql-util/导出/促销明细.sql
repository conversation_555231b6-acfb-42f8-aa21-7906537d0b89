select max(p.promotion_name) "促销活动",max(o.organization_name) "经销商名称", max(pd.ext_property1) "赠品", sum(pd.award_quantity) "数量" 

from wx_t_promotion_delivery_detail pd 

left join wx_t_sell_in_promotion p on p.id=pd.promotion_id
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=pd.ext_property7
left join wx_t_organization o on pe.partner_id=o.id
group by p.id, o.id, pd.ext_property2
order by p.id, o.id, pd.ext_property2