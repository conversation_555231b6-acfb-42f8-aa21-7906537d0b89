select * into wx_t_point_value_detail_log20211111
--delete l
from wx_t_point_value_detail_log l 
where exists (select 1 from wx_t_point_value_detail d
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID  where pb.id=41594
 and l.POINT_VALUE_ID=d.id)

select d.* into wx_t_point_value_detail20211111
--delete d
from wx_t_point_value_detail d 
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID where pb.id=41594

select top 1000 * into wx_t_point_business20211111
--delete pb
 from dbo.wx_t_point_business pb where pb.id=41594
 
 select top 100 * into wx_t_budget2021_expense_import20211111
--delete d
 from wx_t_budget2021_expense_import d where input_time>='2021-11-11 22:32' order by id desc;