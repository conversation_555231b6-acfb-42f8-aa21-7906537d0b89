/*非本地营销-销量扣减-经销商销量明细数据*/
select distinct case when dlt.quarter=-1 then -1 else alt.quarter end quarter, alt.sku,bp1.t3_category, alt.product_batch, 
case when dlt.quarter=-1 then 0 else alt.liters end liters, isnull(dlt.liters, 0) deduction_liters, dlt.effect_flag,
p01.name product_name from (
select vs1.delivery_quarter 'quarter',vs1.sku,sum(vs1.liters) liters, vs1.product_batch
from wx_t_non_local_mkt_flow_form nff 
left join view_zs_sell_in vs1 on vs1.recv_distributor_id=nff.from_distributor_id
left join wx_t_product p1 on p1.sku=vs1.sku
where nff.id=#BIGINT,flowId# /*上报年份YTD销量*/and vs1.delivery_month>=#VARCHAR,year#+'01' 
and vs1.delivery_month<convert(varchar(10),(#INTEGER,year# + 1))+'01'
and p1.pack!=200/*排除200L产品*/
/*上报产品相同t3 category*/
and exists (select 1 from PP_MID.dbo.syn_dw_to_pp_product bp01 
	where bp01.product_code_SAP = vs1.sku 
	and bp01.t3_category in (select bp02.t3_category 
		from PP_MID.dbo.syn_dw_to_pp_product bp02 
			where exists (select 1 from wx_t_loc_mkt_product_info npi where npi.flow_id=nff.id and npi.auth_channel=#INTEGER,authChannel# and npi.sku=bp02.product_code_SAP)))
group by vs1.delivery_quarter,vs1.sku,vs1.product_batch
) alt 
left join (select nld.sku, nld.quarter, sum(nld.liters) liters,nld.effect_flag
from wx_t_non_local_liters_deduction nld 
left join wx_t_non_local_mkt_flow_pro_res npr on nld.punish_id=npr.id
left join wx_t_non_local_mkt_flow_form nff on nff.id=npr.flow_id
left join wx_t_non_local_mkt_flow_form nff2 on nff.from_distributor_id=nff2.from_distributor_id and nff.id!=nff2.id /*排除当前处罚相关扣减*/
where nff2.id=#BIGINT,flowId# and npr.attribute3=#VARCHAR,year# and nld.status=1 and nld.delete_flag=0
group by nld.sku, nld.quarter,nld.effect_flag) dlt on alt.sku=dlt.sku and (alt.quarter=dlt.quarter or dlt.quarter=-1)
left join PP_MID.dbo.syn_dw_to_pp_product bp1 on bp1.product_code_SAP=alt.sku
left join wx_t_product p01 on p01.sku=alt.sku
