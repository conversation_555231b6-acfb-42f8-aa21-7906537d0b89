$(function() {
	newhomepage_init.init();
        var thisYear = new Date().getFullYear();
        for (var j = 0; j < 2; j++) {//TODO
        	var i = thisYear - j;
            $('#year_c ul')
                .append($('<li class="'+(i === currentYear?'active':'')+'">'+i+'</li>').click(function() {
                    $(this).siblings().removeClass('active');
                    $(this).addClass('active');
                    reloadPage({
                    	year: $(this).text()
                    });
                }));
        }

});
function reloadPage(params){
	var p1 = $.extend({}, currentParams);
	if(params){
		p1 = $.extend(p1, params);
	}
	if(p1.partnerName){
		p1.partnerName = encodeURIComponent(encodeURIComponent(p1.partnerName));
	}
	var u = null;
	for(var k in p1){
		if(u == null){
			u = common.ctx + 'newhomepage/jsp/asm/SpSalesPerfAnalysis.jsp?';
		}else {
			u += '&';
		}
		u += k + '=' + p1[k];
	}
	if(u == null){
		u = common.ctx + 'newhomepage/jsp/asm/SpSalesPerfAnalysis.jsp';
	}
	window.location = u;
}

/**
 * 机构下拉框
 * @type {null}
 */
var organizationCtrl = null;
OrgAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts = $.extend({placeholder: '支持名称或名称首字母搜索'}, opts);
        }else{
            opts = {placeholder: '支持名称或名称首字母搜索'};
        }
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
            	// debugger;
                if (!json || !json.resultLst) {
                    return false;
                }

                var len, data = {
                    value: []
                };
                len = json.resultLst.length;

                for (var i = 0; i < len; i++) {
                    data.value.push({
                        value: json.resultLst[i].id,
                        text: json.resultLst[i].organizationName
                    });
                }

                //字符串转化为 js 对象
                return data;
            }
        });
        return Ctrls.AutoSelect.init.call(this, 'myCdm/queryPartnersInfo.do', valueField, 'keyword', opts);
    }
});
var newhomepage_init = (function($) {
	
	var marketIviBar = null,
	
	iconNotUpClass = "fa fa-times-circle not-up-icon",
	iconRightClass = "fa fa-check-circle-o right-icon",
	iconWarmingClass= "fa fa-exclamation-circle warning-icon",
	
	rightTextClass = "up",
	notUpTextClass = "not-up",
	runningTextClass = "running",
	
	rightText = "已达标",
	notUpText = "未达标",
	runningText = "进行中",
	
	workshopTarget = 8,
		
	req = {}, 
	util = {},
	action = {},
	loader = {};
	
	req = {
			getMonthlySellInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/monthlySellInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getProductMonthlySellInfo: function(callback) {
				$.get(common.ctx + "newhomepage/productMonthSellInfo.do?t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
        	getLocalSalesResult: function(callback) {
                var partnerId = $('#partnerId').val();
                $.get(common.ctx + "newhomepage/getLocalSalesResultByPartnerId.do?year=" + currentYear + "&t=" + new Date().getTime() + "&partnerId="+partnerId, function(data){
    				if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getQuarterlySellInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/quarterlySellInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getSellThrouthInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/sellThrouthInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			}
	},
	util = {
			openMenu: function(url) {
				if(top.openMenuTab){
					top.openMenuTab(url);
				}else{
					window.open(url);
				}
			},
			openUrl: function(url, menuId, menuTitle) {
				if(top.openMenu){
					top.openMenu(url, menuId, menuTitle);
				}else{
					window.open(url);
				}
			},
			openPage: function(url, type) {
				window.location.href = url + "?type=" + type;
			},
			toThousands: function(n) {
				return parseFloat(n).toLocaleString() 
			},
		    genPartnerQueryUrl: function (urlPath) {
                var partnerId = $('#partnerId').val();
                var timestamp = new Date().getTime();
                return common.ctx + urlPath + "?year=" + currentYear + "&month=" + currentMonth + "&t=" + timestamp + "&partnerId="+partnerId+"&productChannel=Consumer";
            },
		    doSearch: function () {
                    // 获取pointType和orgId
                    var orgId = $('#partnerId').val();
                    if(!orgId){
                        common.alertMes("请选择一个机构再查询", 'error');
                        return;
                    }
		    		currentParams.partnerId = $('#partnerId').val();
		    		currentParams.partnerName = organizationCtrl.getText();

					loader.loadMontlySales();
					loader.loadSellThrouth();
					loader.loadQuarterlySales();
					loader.loadLocalGoal();
            }
	},
	loader = {
		    // SNScanInfoGrid相关
			loadSellThrouth: function() {
                req.getSellThrouthInfo(function(data) {
                    //季度数据初始化
                    var sellThrouthQuarterList = [];
                    for(var q = 1; q <= 4; q++) {
                        sellThrouthQuarterList.push({
                            quarter: q,
                            sellIn: 0,
                            sellThrouth: 0,
                            workshopCount: 0,
                            targetBaseLine: 0,
                            sellThrouthFlag: true,
                        });
                    }

                    var snMonthTarget = 0;
                    for(var i in data) {
                        var month = data[i].month;
                        var sellThrouth = data[i].sellThrouth;
                        var sellIn = data[i].sellIn;
                        var workshopCount = data[i].workshopCount;
                        var targetBaseLine = data[i].targetBaseLine;
                        //每月目标
                        
                        snMonthTarget += targetBaseLine;
//                        if(month == currentMonth) {
//                            snMonthTarget = ((targetBaseLine*12/14) * 0.5).toFixed(0);
//                        }
                        //生成季度数据
                        var quarterIndex = parseInt((month + 2) / 3) - 1;
                        sellThrouthQuarterList[quarterIndex].sellIn += sellIn;
                        sellThrouthQuarterList[quarterIndex].sellThrouth += sellThrouth;
                        sellThrouthQuarterList[quarterIndex].workshopCount += workshopCount;
                        sellThrouthQuarterList[quarterIndex].targetBaseLine += targetBaseLine;
                        //月目标是否达标
                        var $productMonthSellInSpan = $('<span></span>');
                        var $productMonthSellInIcon = $('<i aria-hidden="true"></i>');

                        // var monthSellThrouthFlag = false;
                        // if()
                        // if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
                        // 	$productMonthSellInIcon.addClass(iconRightClass);
                        // } else if (sellThrouth / (targetBaseLine * 0.5) < 1 && sellThrouth / (targetBaseLine * 0.5) > 0.75) {
                        // 	$productMonthSellInIcon.addClass(iconWarmingClass);
                        // 	if(month < currentMonth) {
                        // 		sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
                        // 	}
                        // } else {
                        // 	$productMonthSellInIcon.addClass(iconNotUpClass);
                        // 	if(month < currentMonth) {
                        // 		sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
                        // 	}
                        // }

                        $productMonthSellInSpan.append($productMonthSellInIcon);
                        $productMonthSellInSpan.append(util.toThousands(sellThrouth));

                        $("#p-actual-m" + month).empty();
                        $("#p-actual-m" + month).append($productMonthSellInSpan);
                    }
                    for(var i in data) {
						var month = data[i].month;
						var sellThrouth = data[i].sellThrouth;
						//现在“每月SN级以上小包装产品出货扫码目标”的达标计算变成了月份了
                        var mTarget = $("#p-target-m" + month);
                        if (month >= currentMonth && nowYear == currentYear) {
                            mTarget.addClass(runningTextClass);
                            mTarget.html(runningText);
                        } else {
                            var actualTarget = snMonthTarget/28;
                            if (sellThrouth / actualTarget >= 1) {
                                mTarget.addClass(rightTextClass);
                                mTarget.html(rightText);
                            } else if (sellThrouth / actualTarget < 1) {
                                mTarget.addClass(notUpTextClass);
                                mTarget.html(notUpText);
                            }
                        }
					}
                    $("#sn-month-target").html(util.toThousands((snMonthTarget/28).toFixed(0)) + "&nbsp;&nbsp;升");

                    //季度数据加载
                    for(var qi in sellThrouthQuarterList) {
                        var quarter = sellThrouthQuarterList[qi].quarter;
                        // var sellThrouth = sellThrouthQuarterList[qi].sellThrouth;
                        // var sellIn = sellThrouthQuarterList[qi].sellIn;
                        var workshopCount = sellThrouthQuarterList[qi].workshopCount;
                        // var targetBaseLine = sellThrouthQuarterList[qi].targetBaseLine;
                        // var sellThrouthFlag = sellThrouthQuarterList[qi].sellThrouthFlag;
                        // //扫码季度达标
                        // $pTarget = $("#p-target-q" + quarter);
                        // if(!sellThrouthFlag) {
                        // 	$pTarget.addClass(notUpTextClass);
                        // 	$pTarget.html(notUpText);
                        // } else {
                        // 	if(quarter >= currentQuarter) {
                        // 		$pTarget.addClass(runningTextClass);
                        // 		$pTarget.html(runningText);
                        // 	} else {
                        // 		if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
                        // 			$pTarget.addClass(rightTextClass);
                        // 			$pTarget.html(rightText);
                        // 		} else if(sellThrouth / (targetBaseLine * 0.5) < 1) {
                        // 			$pTarget.addClass(notUpTextClass);
                        // 			$pTarget.html(notUpText);
                        // 		}
                        // 	}
                        // }

                        //新维修门店
                        var $storeCountSpan = $('<span></span>');
                        var $storeCountIcon = $('<i aria-hidden="true"></i>');
                        if(workshopCount >= workshopTarget) {
                            $storeCountIcon.addClass(iconRightClass);
                        } else if(workshopCount < workshopTarget && workshopCount >= 6) {
                            $storeCountIcon.addClass(iconWarmingClass);
                        } else {
                            $storeCountIcon.addClass(iconNotUpClass);
                        }

                        $storeCountSpan.append($storeCountIcon);
                        $storeCountSpan.append(util.toThousands(workshopCount));

                        $("#s-actual-q" + quarter).empty();
                        $("#s-actual-q" + quarter).append($storeCountSpan);
                        //门店达标
                        $sTraget = $("#s-target-q" + quarter);
                        $sTraget.removeClass(rightTextClass);
                        $sTraget.removeClass(notUpTextClass);
                        $sTraget.removeClass(runningTextClass);
                        if(quarter >= currentQuarter && nowYear == currentYear) {
                            $sTraget.addClass(runningTextClass);
                            $sTraget.html(runningText);
                        } else {
                            if(workshopCount >= workshopTarget) {
                                $sTraget.addClass(rightTextClass);
                                $sTraget.html(rightText);
                            } else {
                                $sTraget.addClass(notUpTextClass);
                                $sTraget.html(notUpText);
                            }
                        }

                    }
                });
			},
		    // total-sales-target,对应"总销量目标"和季度的那些值
			loadQuarterlySales: function() {
				req.getQuarterlySellInfo(function(data) {
					var totalSalesTraget = 0;
					for(var i in data) {
						var quarter = data[i].quarter;
						var targetBaseLine = data[i].targetBaseline;
						var sellIn = data[i].sellIn;
						var monthAvgTarget = (targetBaseLine / 3).toFixed(0);
						//总目标销量
						totalSalesTraget += targetBaseLine;
						//进货量基数标准
						$("#target-q" + quarter).html(util.toThousands(targetBaseLine));
						//月平均进货目标=季度进货量基数标准/3
						for(var j = 0; j < 3; j++) {
							month = 3 * quarter - j;
							$("#target-m" + month).html(util.toThousands(monthAvgTarget));
							$("#target-m" + month).attr("title",util.toThousands(monthAvgTarget));
						}
						//季度实际完成进货量
						var sellInDiv = $('<div></div>');
						sellInDiv.html(util.toThousands(sellIn));
						if(sellIn / targetBaseLine < 0.8) {
							sellInDiv.addClass("not-up");
						} else if (sellIn / targetBaseLine < 1 && sellIn / targetBaseLine > 0.8) {
							sellInDiv.addClass("warning");
						} else {
							sellInDiv.addClass("up");
						}
						$("#actual-q" + quarter).html(sellInDiv);
                        $("#actual-q" + quarter).attr("title",util.toThousands(sellIn));
						//季度实际完成比例
						var percent = "";
						if(targetBaseLine != 0) {
							percent = (sellIn / targetBaseLine * 100).toFixed(0) + "%";
						}
						$("#percent-q" + quarter).html(percent);
					}
					$("#total-sales-target").html(totalSalesTraget + "&nbsp;&nbsp;升");
				});
			},
        	// totalPerformanceInfoGrid 月平均进货量
			loadMontlySales: function() {
				req.getMonthlySellInfo(function(data){
					for(var i in data) {
						var month = data[i].month;
						var sellIn = data[i].sellIn;
						$("#actual-m" + month).html(util.toThousands(sellIn));
						$("#actual-m" + month).attr("title",(util.toThousands(sellIn)));
					}
				});
			},
		    // marketingInfoGrid,每季度成功开发的新维修门店目标
			loadLocalGoal: function() {
                req.getLocalSalesResult(function(data) {
                    for(var i = 1; i <= 4; i++) {
                        $goalSpan = $("#goal-q" + i);
                        $goalSpan.empty();
                        for(var index = 0; index < 4; index++) {
                            if(data[index].quarter == i) {
                                if(i < currentQuarter || nowYear > currentYear) {
                                    if(data[index].salesResult == '1') {
                                        $SpanContent = $('<i class="fa fa-check-circle-o right-icon" aria-hidden="true"></i>');
                                        $goalSpan.append($SpanContent);
                                        $goalSpan.append('完成');
                                    } else {
                                        $SpanContent = $('<span><i class="fa fa-times-circle not-up-icon" aria-hidden="true"></i></span>');
                                        $goalSpan.append($SpanContent);
                                        $goalSpan.append('未达标');
                                    }
                                } else {
                                    $SpanContent = $('<span></span>');
                                    $goalSpan.append($SpanContent);
                                    $goalSpan.append('进行中');
                                }
                            }
                        }
                    }
                });
			},
        initOrganizationSelect: function () {
        // 初始化，机构下拉框
            if(initPartnerName && initPartnerId){
                $('#partnerId').val(initPartnerId).attr('text', initPartnerName);
            }
			organizationCtrl = OrgAutoSelect.init('#partnerId');
		},
        initSearch: function () {
			$("#search").click(function () {
				util.doSearch();
            });
        }
	},
	action = {
	};
	
	return {
		init: function() {
			loader.initOrganizationSelect();
            loader.initSearch();
            if(initPartnerId && initPartnerName) {
                util.doSearch();
            }
			// loader.loadMarketIviInfo();
			// loader.loadMontlySales();
			// loader.loadSellThrouth();
			// loader.loadQuarterlySales();
            // loader.loadLocalGoal();
            // loader.loadSysMessage();
//			loader.loadRebateInfo();
//			loader.loadlastYearRebateInfo();
// 			loader.loadCdmPoints();
// 			loader.loadWorkshop();
// 			loader.loadSupportOrderPhone();
		},
		openUrl: function(url, menuId, menuTitle) {
			util.openUrl(url, menuId, menuTitle);
		},
		openMenu: function(url) {
			util.openMenu(url);
		},
		openPage: function(url, type) {
			util.openPage(url, type);
		}
	};
	
}($));