<%@page import="com.sys.utils.business.PrivacyStatusBizService"%>
<%@page import="com.sys.utils.model.PrivacyStatus"%>
<%@page import="com.sys.auth.business.OperationPermissionBizService"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
	String title = "新增门店";
	String workshopId = request.getParameter("workshopId");
	boolean isEdit = !"view".equals(request.getParameter("opFlag"));
	if(workshopId != null){
		title = "修改门店";
	}
	if(!isEdit){
		title = "门店详情";
	}
	WxTUser user = ContextUtil.getCurUser();
	DicItemVoMapper dicTypeVoMapper = (DicItemVoMapper)SpringUtils.getBean("dicItemVoMapper");
	String sourceJson = JSONArray.fromObject(dicTypeVoMapper.selectByCode("workshop.source")).toString();
	String signedTypeJson = JSONArray.fromObject(dicTypeVoMapper.selectByCode("workshop.signedtype")).toString();
	String typeJson = JSONArray.fromObject(dicTypeVoMapper.selectByCode("workshop.type")).toString();
	String scaleJson = JSONArray.fromObject(dicTypeVoMapper.selectByCode("workshop.scale")).toString();
	int privacyStatus = 0;
	if(isEdit){
		PrivacyStatus status = SpringUtils.getBean(PrivacyStatusBizService.class).getPrivacyStatus(user.getUserId(), "Workshop.editPage");
		if(status != null){
			privacyStatus = status.getStatus();
		}
	}
%>
<%@page import="com.sys.auth.model.WxTUser"%>
<%@page import="com.common.util.ContextUtil"%>
<%@page import="com.common.util.SpringUtils"%>
<%@page import="net.sf.json.JSONArray"%>
<%@page import="com.sys.dic.dao.DicItemVoMapper"%>
<!DOCTYPE html>
<html>
<c:set var="isEdit" value="<%=isEdit %>"></c:set>
<c:set var="isSetRule" value='${param.opFlag eq "setrule"}'></c:set>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title><%=title %></title>
 <%@include file="/common/jsp/common.jsp"%>

<!-- iframe里面页面布局 -->
<link href="${ctx }common/ion.rangeSlider-2.1.5/css/ion.rangeSlider.css" rel="stylesheet">
<link href="${ctx }common/ion.rangeSlider-2.1.5/css/ion.rangeSlider.skinModern.css" rel="stylesheet">
<link href="${ctx }business/workshop/css/amap-select.css?v=${version}" rel="stylesheet">
  
<script type="text/javascript" src="${ctx }common/ion.rangeSlider-2.1.5/js/ion-rangeSlider/ion.rangeSlider.min.js"></script>
<script type='text/javascript' src="${ctx }business/workshop/js/editWorkshop.js?v=${version}1"></script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=99a81c9a92e84f722b56587dd9e64d2c&plugin=AMap.PlaceSearch"></script>
<style type="text/css">
.view-form .required-flag {
	display: none;
}
.info-block {
	overflow: hidden;
	height: 0;
	position: relative;
}
.show-block .info-block {
	height: auto;
	padding-bottom: 15px;
}
.info-block-header {
	border: 1px solid #dbdcdd;
    color: #666666;
    cursor: pointer;
    font-size: 16px;
    height: 50px;
    line-height: 50px;
    padding: 0 16px 0 20px;
    margin-bottom: 15px;
}
.show-block .info-block-header {
	color: #333;
}
.info-block-header .icon {
    background: url("../../common/images/xiala.png") no-repeat scroll 0 0;
    float: right;
    height: 6px;
    margin-top: 22px;
    width: 14px;
}
.reward-contract-temp {
	margin: 0 0 0 350px;
    position: absolute;
    line-height: 30px;
}
.units {
	margin-left: 5px;
}
#sp_reward_div {
	position: absolute;
	margin-top: -30px;
}
#ws_reward_div {
	position: absolute;
	margin-top: -30px;
}
#sp_all_reward_div {
    left: 50%;
    position: absolute;
    margin-top: -20px;
}
#ws_all_reward_div {
    left: 50%;
    position: absolute;
    margin-top: -20px;
}
#perLiterReward_div {
    margin-top: -30px;
    position: absolute;
    right: 0;
}
#mech_reward_div {
    margin-top: -30px;
    position: absolute;
    right: 0;
}
#sp_reward_flag_div {
    margin-top: 45px;
    position: absolute;
    color: rgb(32, 180, 38);
}
#ws_reward_flag_div {
    margin-top: 45px;
    position: absolute;
    color: rgb(32, 180, 38);
}
#ws_all_reward_flag_div {
    margin-top: 45px;
    position: absolute;
    right: 0;
}
#mech_reward_flag_div {
    margin-top: 45px;
    position: absolute;
    right: 0;
}
#wsDistributionGroup input[type=text] {
	display: inline-block;
	padding: 2px 6px;
}
#noWsDistribution {
	color: #666;
	line-height: 30px;
}
#wsDistributionGroup input[type=text] {
	height: 18px;
}
input.control-text {
	width: 180px;
}
div.bui-select .bui-select-input {
	width: 164px;
}
#baseInfoBlock .cols-2 > .col-group, #baseInfoBlock .cols-2 > .field-group, #baseInfoBlock .cols-1 > .field-group {
	float: left;
}
#baseInfoBlock .field-group {
	height: 30px;
}
.bar-block-separator {
	background-color: #dbdcdd;
    display: none;
    height: 1px;
    left: -50px;
    margin-bottom: 15px;
    padding: 0 50px;
    position: relative;
    width: 100%;
}
.show-wsallocation .bar-block-separator {
	display: block;
}
div label.field-label {
	width: 150px;
}
.no-sp-verification-rule {
	line-height: 30px;
}
#file_img_cover .close {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 31px;
    height: 31px;
    font-size: 0;
    color: #fff;
    background: url(/material/imges/close-icon.png) no-repeat;
    opacity: 0.5;
    filter: alpha(opacity=50)\9;
    -webkit-transition: opacity 0.2s;
    transition: opacity 0.2s;
    z-index: 99;
}
#file_img_cover .close:hover{
    background: url(/material/imges/close-icon.png) no-repeat;
    opacity: 1;
	filter: alpha(opacity=100)\9;
	color: #fff;
}
.large-image-wrapper {
	background: #000;
	position:absolute;
}
.large-image-wrapper-left {
	float:left;
}
.large-image-wrapper-right {
	float:left;
	width:300px;
}
.large-image-title{
	padding: 10px 10px 10px;
	color: #fff;
	font-weight: 600;
	overflow: hidden;
	text-overflow: ellipsis;
	background: #333;
	white-space: nowrap;
}
.large-image-desc {
	color: #333;
	padding: 0px 10px 0px;
	background: #222020b8;
	color: #d7cfcf;
	margin: 10px 0px;
    /* position: absolute;
    bottom: 36px; */
	height: auto;
	max-height:200px;
    overflow: hidden;
    z-index: 100000;
}
#file_img_larger {
	z-index : 9998;
	display:block;
	min-width: 400px;
	/* margin:0px auto; */
	cursor:zoom-out;
	}

#file_img_cover {
	left: 0px;
    top: 0px;
    position: fixed;
    z-index: 9996;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.46);
}
		.edit-form .field-limit {
			display: none;
		}
.bui-dialog .bui-stdmod-body {
    position: relative;
}
.field-comments {
	color: #999;
    font-size: 10px;
}
		.edit-form .field-limit {
			display: none;
		}
</style>
<script type='text/javascript'>
var workshopId = '<%=workshopId == null ? "" : workshopId%>', isEdit = <%=isEdit%>, 
	status = '${param.status}', subTaskId = '${param.subTaskId}',
	userType = <%=user.getmUserTypes() == null ? "-1" : user.getmUserTypes().toString()%>,
	loginOrgId = <%=user.getOrgId()%>, userId = <%=user.getUserId() %>;
	var sourceData = <%=sourceJson%>;
	var signedTypeData = <%=signedTypeJson%>;
	var typeData = <%=typeJson %>;
	var scaleData = <%=scaleJson %>;
	var orgType = "1", orgId='<%=workshopId == null ? "" : workshopId%>', orgName='';
	var loginOrgType = '<%=user.getType() %>';
	var permissionWeight = <%=SpringUtils.getBean(OperationPermissionBizService.class).getPermissionWeight(user.getUserId(), "Workshop.editPage") %>;
	var privacyStatus = <%=privacyStatus%>;
	var excuteUserId = '${param.excuteUserId}';
</script>
</head>

<body class="gray-bg">
	<div class="content-wrapper">
    	<div class="content-panel header-panel">
    		<div class="header-title"><%=title %></div>
    		<div class="header-btns">
				<%if(isEdit){ %>
					<button type="button" class="btn-submit" onclick="save();">提交</button>
				<%} %>
				<%if(workshopId != null && !isEdit){ %>
					<button type="button" class="btn-update" onclick="window.location = '${ctx }business/workshop/editWorkshop.jsp?workshopId=<%=workshopId %>';">修改</button>
				<%} %>
					<button type="button" class="btn-back" onclick="window.location = '${ctx }business/workshop/workshopPage.jsp?cacheParams=true&toItem=<%=workshopId %>'">返回</button>
    		</div>
    	</div>

<div class="content-panel">
	<%if(!isEdit){ %>
	<div id="tab"></div>
	<div id="tab_content">
	<div class="content-panel"><%} %>
	<form class='form-horizontal edit-form${isEdit ? "" : "  view-form" }' action="" id="editForm">
						<input name="extFlag" type="hidden"/>
<div class='${isSetRule ? "" : "show-block"}'>
	<div class="info-block-header" onclick="showBlock(this);">门店基本属性<i class="icon"></i></div>
	<div class="info-block" id="baseInfoBlock">
		<div class="cols-2">
			<div class="col-group">
				<div class="field-group">
					<label class="field-label"><span class="required-flag">*</span>门店名称：</label>
					<div class="control-group">
						<input id="workshopName" name="workShopName" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{required:true}" >
					</div>
				</div>
				<div class="field-group">
					<label class="field-label"><span class="required-flag">*</span>门店所在省：</label>
					<div class="control-group" id="prov_c">
						<input id="prov" name="provinceId" type="hidden" value="-1"/>
					</div>
				</div>
				<div class="field-group">
					<label class="field-label"><span class="required-flag">*</span>门店所在市：</label>
					<div class="control-group" id="city_c">
						<input id="city" name="cityId" type="hidden" value="-1"/>
					</div>
				</div>
				<div class="field-group">
					<label class="field-label"><span class="required-flag">*</span>门店所在区：</label>
					<div class="control-group" id="region_c">
						<input id="regionId" name="regionId" type="hidden" value="-1"/>
					</div>
				</div>
				<div class="field-group">
					<label class="field-label"><span class="required-flag">*</span>街道地址(精确到门牌)：</label>
					<div class="control-group">
						<input id="workShopAddress" name="workShopAddress" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{required:true}">
					</div>
				</div>
			</div>
			<div class="field-group height-auto" style="">
				<label class="field-label">门头照：</label>
				<div class="control-group" style="text-align: center;">
					<img id="photoImg" src="${ctx }images/wu.jpg" width="" height="182px"/>
					<div style="margin: 20px auto 0 auto;" id="uploadPhoto_c" class="hide"></div>
					<input type="hidden" name="photoId" id="photoId" />
				</div>
			</div>
			<div class="field-group"<%=(workshopId == null || user.getUserId() == 1) ? "" : " style=\"display: none;\"" %>>
				<label class="field-label">门店经度：</label>
				<div class="control-group">
					<input id="longitude" name="longitude" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group"<%=(workshopId == null || user.getUserId() == 1) ? "" : " style=\"display: none;\"" %>>
				<label class="field-label">门店纬度：</label>
				<div class="control-group">
					<input id=latitude name="latitude" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group hide-important" id="locationFun">
				<label class="field-label"><span class="required-flag">*</span>门店定位确认：</label>
				<div class="control-group">
					<button type="button" class="btn-submit" onclick="editLocation();"><i class="fa fa-map-marker"></i></button>
				</div>
			</div>
			<div class="field-group field-limit field-source1">
				<label class="field-label">店主姓名：</label>
				<div class="control-group">
					<input id="workshopOwner" name="workshopOwner" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			<div class="field-group field-limit field-source1">
				<label class="field-label">店主手机号：</label>
				<div class="control-group">
					<input id="workshopOwnerMobile" name="workshopOwnerMobile" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			<div class="field-group field-limit field-source1">
				<label class="field-label">门店电话：</label>
				<div class="control-group">
					<input id="workshopTel" name="workshopTel" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店联系人：</label>
				<div class="control-group">
					<input id=contactPerson name="contactPerson" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label"><span class="required-flag">*</span>门店联系电话：</label>
					<div class="control-group">
					<input id=contactPersonTel name="contactPersonTel" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true,maxlength:11}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">是否连锁：</label>
				<div class="control-group">
					<input type="hidden" id="isMultiple" name="isMultiple"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店类型：</label>
				<div class="control-group" id="type_c">
					<input type="hidden" id="type" name="type" value="composite"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店店面数：</label>
				<div class="control-group" id="scale_c">
					<input type="hidden" id="scale" name="scale" value="1-3"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店面积：</label>
				<div class="control-group">
					<input id=area name="area" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店工位数：</label>
				<div class="control-group">
					<input id=seatsNum name="seatsNum" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">门店员工数：</label>
				<div class="control-group">
					<input id=employeesNum name="employeesNum" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">地沟数 ：</label>
				<div class="control-group">
					<input id=trenchNumb name="trenchNumb" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">四柱举升机 ：</label>
				<div class="control-group">
					<input id=fourColumnElevatorNumb name="fourColumnElevatorNumb" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">两柱举升机 ：</label>
				<div class="control-group">
					<input id=twoColumnElevatorNumb name="twoColumnElevatorNumb" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">是否店招：</label>
				<div class="control-group">
					<input type="hidden" id="shopRecruitment" name="shopRecruitment"/>
				</div>
			</div>
			<div class="field-group hide-important recruitment-property">
				<label class="field-label">店招金额：</label>
				<div class="control-group">
					<input type="text" class="control-text" id="recruitmentAmount" name="recruitmentAmount"/>
				</div>
			</div>
			<div class="field-group hide-important recruitment-property">
				<label class="field-label">是否已确认：</label>
				<div class="control-group">
					<input type="hidden" id="recruitmentConfirmed" name="recruitmentConfirmed"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label"><span class="required-flag field-limit field-wlabel1">*</span>雪佛龙养护大咖汇计划：</label>
				<div class="control-group">
					<input type="hidden" id="joinShopGiftPlan" name="joinShopGiftPlan"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label"><span class="required-flag field-limit field-wlabel1">*</span>参与门店定位计划：</label>
				<div class="control-group">
					<input type="hidden" id="joinLocationPlan" name="joinLocationPlan"/>
				</div>
			</div>
			<div class="field-group" id="partnersField">
				<label class="field-label">其他设备：</label>
				<div class="control-group">
					<input id="otherEquipment" name="otherEquipment" type="hidden" class="control-text" value=""/>
				</div>
			</div>
			<div class="field-group" id="partnersField">
				<label class="field-label">其他设置：</label>
				<div class="control-group">
					<input id="otherCollocation" name="otherCollocation" type="hidden" class="control-text" value=""/>
				</div>
			</div>
			<div class="field-group" style="display: none;">
				<label class="field-label">激活时间：</label>
				<div class="control-group">
					<input id="activeTime" name="activeTime" type="text" disabled="disabled" class="control-text">
				</div>
			</div>
		</div>
		<div class="cols-1">
			<div class="field-group height-auto">
				<label class="field-label">合同：</label>
				<div class="control-group col-sm-9" id="uploadContract_c">
				</div>
			</div>
		</div>
		<div class="clear"></div>
	</div>
</div>
<div class='${isSetRule ? "show-block" :  ""}'>
	<div class="info-block-header" onclick="showBlock(this);">门店业务属性<i class="icon"></i></div>
	<div class="info-block">
		<div class="cols-1">		
			<div class="field-group">
				<label class="field-label">门店核销服务费分配：</label>
				<div class="control-group col-sm-9">
					<div id="noWsDistribution" class="no-sp-verification-rule">不能操作。您需要先创建该门店合作合伙人的销售规则！</div>
					<div id="wsDistributionGroup" class="has-sp-verification-rule hide">
						<div style="">
							<input type="checkbox" value="Y" id="awardRealtime" name="awardRealtime"${isEdit ? "" : "  disabled=\"disabled\"" }/> <label for="awardRealtime">实时激励</label>
						</div>
						<div id="perLiterReward_c" style="display: none;position: relative;clear: both;padding: 30px 0;">
							<div id="sp_reward_div"><input id="spReward" name="spReward" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } style="width: 30px" onchange="var v = this.value.trim();if(v == '' || isNaN(v)){v = 0;}else{ v = v - 0; if(v > verificationRule.perLiterReward){v = verificationRule.perLiterReward;}}updateSpDistritution(v, verificationRule.perLiterReward - v, true);"><span class="units">元</span></div>
							<div id="sp_all_reward_div"><div style="left: -50%;position: relative;">合伙人和门店服务费共<span id="spAllReward" class="total-quantity">0</span>元</div></div>
							<div id="perLiterReward_div"><input id="perLiterReward" name="perLiterReward" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } style="width: 30px" value="0" onchange="var v = this.value.trim();if(v == '' || isNaN(v)){v = 0;}else{v = v - 0; if(v > verificationRule.perLiterReward){v = verificationRule.perLiterReward;}}updateSpDistritution(verificationRule.perLiterReward - v, v, true);"><span class="units">元</span></div>
							<div id="sp_reward_flag_div">合伙人<span class="reward-comment" id="spRewardComment"></span></div>
							<div id="ws_all_reward_flag_div">门店<span class="reward-comment" id="wsAllRewardComment"></span></div>
							<div id=""><input type="text" id="perLiterReward_bar"/></div>
						</div>
						<div class="bar-block-separator"></div>
						<div id="wsAllocation_c" style="display: none;clear: both;padding: 30px 0;">
							<div id="ws_reward_div"><input id="ownerReward" name="ownerReward" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } style="width: 30px" value="0" onchange="var v = this.value.trim(), ws = $('#perLiterReward').val().trim() - 0;if(v == '' || isNaN(v)){v = 0;}else{v = v - 0; if(v > ws){v = ws;}}updateWsDistritution(v, ws - v, ws);"><span class="units">元</span></div>
							<div id="ws_all_reward_div"><div style="left: -50%;position: relative;">店老板和技师服务费共<span id="wsAllReward" class="total-quantity">0</span>元</div></div>
							<div id="mech_reward_div"><input id="mechanicReward" name="mechanicReward" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } style="width: 30px" onchange="var v = this.value.trim(), ws = $('#perLiterReward').val().trim() - 0;if(v == '' || isNaN(v)){v = 0;}else{v = v - 0; if(v > ws){v = ws;}}updateWsDistritution(ws - v, v, ws);"><span class="units">元</span></div>
							<div id="ws_reward_flag_div">店老板<span class="reward-comment" id="wsRewardComment"></span></div>
							<div id="mech_reward_flag_div">技师<span class="reward-comment" id="mechRewardComment"></span></div>
							<div id=""><input type="text" id="wsAllocation_bar"/></div>
						</div>
					</div>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店核销服务费优先分配：</label>
				<div class="control-group col-sm-9">
					<div class="no-sp-verification-rule">不能操作。您需要先创建该门店合作合伙人的销售规则！</div>
					<div class="has-sp-verification-rule hide">
			    		<div id="skuDistributionGrid"></div>
			    		<c:if test="${isEdit}">
			    		<div class="content-panel"><button type="button" class="btn-create" onclick="addSkuDistribution()">添加分配规则</button></div>
			    		</c:if>
					</div>
				</div>
			</div>
			<div class="field-group">
			<%if(isEdit){ %>
				<div class="reward-contract-temp"><a href="#" target="_blank">模板下载</a></div>
			<%} %>
				<label class="field-label">服务费合同：</label>
				<div class="control-group col-sm-9" id="rewardContract_c">
				</div>
			</div>
		</div>
		<div class="cols-2">
			<div class="field-group">
				<label class="field-label">签约类型：</label>
				<div class="control-group" id="signedType_c">
					<input type="hidden" value="交易类型" id="signedType" name="signedType"/>
				</div>
			</div>
			<div class="field-group" id="partnersField">
				<label class="field-label"><span class="required-flag">*</span>门店合作合伙人：</label>
				<div class="control-group" id="partners_c">
					<input id="partnerIds" name="partnerIds" type="hidden" class="control-text" value=""/>
				</div>
			</div>
			<div class="field-group">
					<input id="excuteUserId" name="excuteUserId" type="hidden" class="control-text" value=""/>
				<label class="field-label">门店执行人：</label>
				<div class="control-group" id="excuteUserId_c">
				</div>
			</div>
			<!-- <div class="field-group">
				<label class="field-label">门店对接人：</label>
				<div class="control-group">
					<input id=buttInChargePerson name="buttInChargePerson" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">门店对接人电话：</label>
				<div class="control-group">
					<input id=buttInChargePersonTel name="buttInChargePersonTel" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div> -->
			<div class="field-group">
				<label class="field-label">门店来源：</label>
				<div class="control-group" id="source_c">
					<input type="hidden" id="source" name="source" value="excel导入"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">运营年限 ：</label>
				<div class="control-group">
					<input id="operationYear" name="operationYear" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">营业时间：</label>
				<div class="control-group">
					<input type="hidden" id="operationPeriods" name="operationPeriods"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">节假日是否休息：</label>
				<div class="control-group">
					<input type="hidden" id="isHolidayClose" name="isHolidayClose"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">收款方式：</label>
				<div class="control-group">
					<input type="hidden" id="paymentType" name="paymentType"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">有无兴趣提高数字化管理：</label>
				<div class="control-group">
					<input type="hidden" id="digitalManagementIntention" name="digitalManagementIntention"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">有无形象提升需求：</label>
				<div class="control-group">
					<input type="hidden" id="levelPromotionIntention" name="levelPromotionIntention"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">服务类型：</label>
				<div class="control-group">
					<input type="hidden" id="serviceScope" name="serviceScope"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">门店授权经销商：</label>
				<div class="control-group">
					<input type="hidden" id="authProductAgency" name="authProductAgency"/>
				</div>
			</div>
			<!-- add by bo.liu 180724 start  -->
			<div class="field-group" id="monthlyOilSalesVolume_div" style="display: none;">
                <label class="field-label">该门店月机油销量升数：</label>
                <div class="control-group">
                    <input id=monthlyOilSalesVolume name="monthlyOilSalesVolume" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }><span class="units">L</span>
                </div>
            </div>
            <div class="field-group" id="monthlyChevronOilSalesVolume_div" style="display: none;">
                <label class="field-label">预计月销售雪佛龙机油升数：</label>
                <div class="control-group">
                    <input id=monthlyChevronOilSalesVolume name="monthlyChevronOilSalesVolume" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }><span class="units">L</span>
                </div>
            </div>
             <!-- add by bo.liu 180724 end  -->
			<div class="field-group">
				<label class="field-label">销售哪种粘度的机油较多：</label>
				<div class="control-group">
					<input type="hidden" id="saleMajorOilViscosity" name="saleMajorOilViscosity"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">销售哪种类型的油比较多：</label>
				<div class="control-group">
					<input type="hidden" id="saleMajorOilType" name="saleMajorOilType"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">销售的主要机油品牌：</label>
				<div class="control-group">
					<input type="hidden" id="saleMajorOilBrand" name="saleMajorOilBrand"/>
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">每月保养车辆数(台)：</label>
				<div class="control-group">
					<input id=carMaintainNumb name="carMaintainNumb" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">周边小区数量：</label>
				<div class="control-group">
					<input id=nearbyVillageNumb name="nearbyVillageNumb" type="hidden" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">引流方式：</label>
				<div class="control-group">
					<input id=spreadWay name="spreadWay" type="hidden" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">有无营销概念：</label>
				<div class="control-group">
					<input id=hasMarketingConcept name="hasMarketingConcept" type="hidden" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">有无仓库：</label>
				<div class="control-group">
					<input id=hasWarehouse name="hasWarehouse" type="hidden" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group">
				<label class="field-label">管理成熟度：</label>
				<div class="control-group">
					<input id=managementMaturity name="managementMaturity" type="hidden" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true}">
				</div>
			</div>
			<div class="field-group" id="activeFeeFlag_div" style="display: none;">
				<label class="field-label">激活费：</label>
				<div class="control-group">
					<input id=activeFeeFlag name="activeFeeFlag" type="text" class="control-text" disabled="disabled">
				</div>
			</div>
			<div class="field-group" id="managementFeeFlag_div" style="display: none;">
				<label class="field-label">管理费：</label>
				<div class="control-group">
					<input id=managementFeeFlag name="managementFeeFlag" type="text" class="control-text"  disabled="disabled"><span class="units">次</span>
				</div>
			</div>
		</div>
	</div>
</div>
<div>
	<div class="info-block-header" onclick="showBlock(this);">门店资质属性<i class="icon"></i></div>
	<div class="info-block">
		<div class="cols-2">		
			<!-- 合伙人 编码   名字
			<div class="field-group">
				<label class="field-label"><span class="required-flag">*</span>Partner：</label>
				<div class="control-group">
					<input id="partnerId" name="partnerId" type="hidden">
					<input id="partnerName" name="partnerName" type="text" class="control-text" class="" disabled="true">
				</div>
			</div>
			 -->
			<!-- BD 编码   名字 	-->
				<!-- <div class="field-group">
				<label class="field-label"><span class="required-flag">*</span>执行人：</label>
				<div class="control-group">
					<select class="input-normal" id="excuteUserId" name="excuteUserId"> 
	             	</select>
				</div>
			</div> -->
			
			<div class="field-group">
				<label class="field-label">银行开户名称：</label>
				<div class="control-group">
					<input id=bankAcountName name="bankAcountName" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">银行开户账号：</label>
					<div class="control-group">
					<input id=bankAcount name="bankAcount" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">银行开户行：</label>
				<div class="control-group">
					<input id=bank name="bank" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">信用等级：</label>
				<div class="control-group">
					<input id=creditRating name="creditRating" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<!-- <div class="field-group">
				<label class="field-label">服务范围：</label>
				<div class="control-group">
					<input id=serviceScope name="serviceScope" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div> -->
			
			<!-- 
			<div class="field-group">
				<label class="field-label">营业执照编码：</label>
				<div class="control-group">
					<input id=businessLicenseCode name="businessLicenseCode" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">预约服务电话：</label>
				<div class="control-group">
					<input id=reserveServiceTel name="reserveServiceTel" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true,maxlength:11}">
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">商家详情：</label>
				<div class="control-group">
					<input id=businessDetail name="businessDetail" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
			
			<div class="field-group">
				<label class="field-label">收货负责人：</label>
				<div class="control-group">
					<input id=harvestPerson name="harvestPerson" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" }>
				</div>
			</div>
				
			<div class="field-group">
				<label class="field-label">收货负责人电话：</label>
				<div class="control-group">
					<input id=harvestPersonTel name="harvestPersonTel" type="text" class="control-text"${isEdit ? "" : "  disabled=\"disabled\"" } data-rules="{number:true,maxlength:11}">
				</div>
			</div>
			 -->
			<%if(workshopId == null){ %>
			 <div class="field-group">
				<label class="field-label">门店状态：</label>
				<div class="control-group" style="padding-top: 5px;">
					<label class="radio-label"><input type="radio" name="status" id="status_0" value="0" class="control-radio" checked="checked"/>已扫店</label>
					<label class="radio-label"><input type="radio" name="status" id="status_3" value="3" style="margin-left: 15px;" class="control-radio"/>已激活</label>
				</div>
			</div>
				<%} else { %>
	<!-- 		<div class="field-group height-auto">
				<label class="field-label">技师：</label>
				<div class="col-sm-9" id="mechanicGrid">
				</div>
			</div> -->
			<%} %>
		</div>
		<div class="cols-1">
			<div class="field-group">
				<label class="field-label">备注：</label>
				<div class="control-group col-sm-8">
					<textarea class="control-textarea"${isEdit ? "" : "  disabled=\"disabled\"" } id="remark" name="remark"></textarea>
				</div>
			</div>
		</div>
	</div>
</div>
   	</form>
    	<%if(!isEdit){ %>
    	</div>
    	<div class="content-panel">
    		<div id="task_list"></div>
    	</div>
    	<div class="content-panel">
    		<div id="mechanic_list"></div>
    	</div>
    	<div class="content-panel">
    		<div id="manager_list">
    		<!-- <div class='form-horizontal' id="shopManagerInfo">
		<div class="field-group">
			<label class="field-label">姓名：</label>
			<div class="control-group">
				<input name="chName" type="text" class="control-text" disabled="disabled">
			</div>
		</div>
		
		<div class="field-group">
			<label class="field-label">登录名：</label>
			<div class="control-group">
				<input name="loginName" type="text" class="control-text" disabled="disabled">
			</div>
		</div>
 		<div class="field-group">
			<label class="field-label">性别：</label>
			<div class="col-sm-6" style="margin-top: 2px;">
				<input id="sex_m" type="radio" value="M" name="sex">
				<label style="margin-right: 15px;" for="sex_m">男</label>
				<input id="sex_f" type="radio" value="F" name="sex">
				<label for="sex_f">女</label>
			</div>
		</div>
		
		<div class="field-group">
			<label class="field-label">生日：</label>
			<div class="control-group">
				<input name="birthday" type="text" class="control-text" disabled="disabled">
			</div>
		</div>
 		<div class="field-group">
			<label class="field-label">手机号码：</label>
			<div class="control-group">
				<input name="mobile" type="text" class="control-text" disabled="disabled">
			</div>
		</div>
		
		<div class="field-group">
			<label class="field-label">电子邮箱：</label>
			<div class="control-group">
				<input name="email" type="text" class="control-text" disabled="disabled">
			</div>
		</div>
 		<div class="field-group">
			<label class="field-label">通讯地址：</label>
			<div class="control-group">
				<input name="address" type="text" class="control-text" disabled="disabled">
			</div>
		</div>
		</div>
		<div id="shopManagerResult">
		</div> -->
    		</div>
    	</div>
    	<!-- 
    	<div class="content-panel">
    		<div class="query-panel">
    			<div class="field-group" style="width: 390px;">
    				<label class="field-label width-auto">搜索：</label>
    				<div class="control-group">
 <input type="text" name='mUserNameOraccount' id="mUserNameOraccount" placeholder="用户名或登录账号查询" style="width: 320px;" class="control-text" /> 
    				</div>
    			</div>
	 			<div class="query-btns">
					<div class="query-btn field-label">
						<button onclick="loadUserList()" class="btn-query">查询</button>
					</div>
				</div>
				<div class="header-btns" style="padding: 10px 0;">
			<button id="createUserButton" type="button" class="btn-create">创建新用户</button>
				</div>
    		</div>
    		<div id="user_grid" class="content-panel"></div>
    		< %@include file="/sys/org/userEdit.jsp"%>
    	</div> -->
    	<!-- 
    	<div class="content-panel">
    		<div id="workshop_verification">
    			<iframe src="${ctx }business/workshopverification/workshopVerification.jsp?workshopId=<%=workshopId %>&&embed=true" frameborder="0" width="100%" height="450px"></iframe>
    		</div>
    	</div>
    	<div class="content-panel">
    		<div id="order_list"></div>
    	</div>
    	<div class="content-panel">
    		<div id="inventory_list"></div>
    	</div> -->
     	</div>
    	<%} %>
	</div>
    	<!-- 添加门店结束 -->
    	<div class="clear"></div>
    	</div>
    <div style="display:none;">
    	<div id="mapDialog" style="height:500px;">
  <div id="amap-container"></div>
  <div id="amap-panel">
    <div class="amap-panel-search">
      <input class="amap-panel-search-input" type="text" placeholder="按关键字搜索门店"/>
    </div>
    <div class="amap-panel-content">
      <div class="amap-panel-body" style="display: none;">
        <div class="amap-panel-list" id="amap-panel-list"></div>
        <div class="amap-panel-buttons">
          <button class="amap-panel-buttons-address">选用地址</button>
          <button class="amap-panel-buttons-position">选用坐标</button>
          <button class="amap-panel-buttons-all">选用地址和坐标</button>
        </div>
      </div>
      <div class="amap-panel-footer">
        确认已录入系统的坐标和地址无误
      </div>
    </div>
  </div>

    	</div>
	    <div id="editDialog">
	    	<div id="skuDistributionForm" class="cols-1">
	    		<input type="hidden" name="ruleId"/>
	     		<div class="field-group">
	     			<label class="field-label"><span class="required-flag">*</span>产品名称：</label>
	     			<div class="control-group">
	     				<input type="hidden" name="sku" class="control-text"/>
	     			</div>
	     		</div>
	     		<div class="field-group">
	     			<label class="field-label"><span class="required-flag">*</span>每升分配给店老板金额：</label>
	     			<div class="control-group">
	     				<input name="ownerReward" type="text" class="control-text" onchange="if(validateAmount(this, '核销每升服务费分配给店老板金额')){fixDistribution([$(this),$('#skuDistributionForm input[name=spReward]'),$('#skuDistributionForm input[name=mechanicReward]')]);}"
									value="0"><span class="units" style="margin-left:8px;">元</span>
	     			</div>
	     		</div>
	     		<div class="field-group">
	     			<label class="field-label"><span class="required-flag">*</span>每升分配给技师金额：</label>
	     			<div class="control-group">
	     				<input name="mechanicReward" type="text" class="control-text" onchange="if(validateAmount(this, '核销每升服务费分配给技师金额')){fixDistribution([$(this),$('#skuDistributionForm input[name=spReward]'),$('#skuDistributionForm input[name=ownerReward]')]);}"
									value="0"><span class="units" style="margin-left:8px;">元</span>
	     			</div>
	     		</div>
	     		<div class="field-group">
	     			<label class="field-label"><span class="required-flag">*</span>每升分配给合伙人金额：</label>
	     			<div class="control-group">
	     				<input name="spReward" type="text" class="control-text" onchange="if(validateAmount(this, '核销每升服务费分配给合伙人金额')){fixDistribution([$(this),$('#skuDistributionForm input[name=ownerReward]'),$('#skuDistributionForm input[name=mechanicReward]')]);}"
									value="0"><span class="units" style="margin-left:8px;">元</span>
	     			</div>
	     		</div>
	    	</div>  
	    </div>
    </div>
<script type="text/javascript" src="${ctx }business/workshop/js/amap-select.js?v=${version}"></script>
 </body>
</html>   		
    		