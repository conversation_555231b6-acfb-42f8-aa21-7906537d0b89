select o.id "Partner Id", o.organization_name "经销商名称", pe.sap_code "SAP Number", --pe.distributor_id,
case when o.status=1 then 'Active' else 'Inactive' end "状态", o.update_time "最新更新时间" from wx_t_organization o 
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
where --o.status=1 and 
o.type=1 and o.id!=9
and len(pe.sap_code)>0
and exists (select 1 from dw_customer_org_sales cos1 join wx_t_dic_item di1 on di1.dic_type_code='Region.Indirect' and di1.dic_item_code=cos1.region
where cos1.distributor_id=pe.distributor_id and cos1.sales_cai!='NA')
order by o.id 