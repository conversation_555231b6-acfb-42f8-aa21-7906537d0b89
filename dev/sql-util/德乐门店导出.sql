select o.organization_name "经销商名称",u.ch_name "DSR.",w.id "门店ID", w.work_shop_name "门店名称", 
r3.region_name "省份", r2.region_name "城市", r1.region_name "区县",w.work_shop_address "地址",
w.contact_person "门店联系人", w.contact_person_tel "门店联系电话",w.delo_type "门店类型",
w.volume_month "月销量", w.sale_major_delo_product "主要销售产品",
CASE 
		WHEN s.task_status = 3 THEN '不合格'
		WHEN s.is_pass = 1 THEN '合格'
		ELSE '未抽查'
	END AS "状态", s.xg_sj "提交时间"
from wx_t_work_shop w 
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
left join wx_task_sub s on s.org_id=w.id
left join wx_task_main tm on tm.task_main_id =s.task_main_id
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on s.exec_user=u.user_id
where w.from_source=2 and w.delete_flag=0
and o.id!=9 and tm.tmb_type_code = 'TT_2_CLZX'
order by o.id, w.id