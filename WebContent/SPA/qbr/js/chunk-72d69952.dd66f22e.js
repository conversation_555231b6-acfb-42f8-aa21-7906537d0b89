(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72d69952"],{"0a98":function(e,t,r){"use strict";r("0c4a")},"0c4a":function(e,t,r){},"190e":function(e,t,r){},"1cd3":function(e,t,r){"use strict";r("d487")},"1d2b3":function(e,t,r){},"1f20":function(e,t,r){"use strict";r("6de3")},"1fa8":function(e,t,r){var a=r("cb7c");e.exports=function(e,t,r,o){try{return o?t(a(r)[0],r[1]):t(r)}catch(l){var n=e["return"];throw void 0!==n&&a(n.call(e)),l}}},"27ee":function(e,t,r){var a=r("23c6"),o=r("2b4c")("iterator"),n=r("84f2");e.exports=r("8378").getIteratorMethod=function(e){if(void 0!=e)return e[o]||e["@@iterator"]||n[a(e)]}},"2f21":function(e,t,r){"use strict";var a=r("79e5");e.exports=function(e,t){return!!e&&a((function(){t?e.call(null,(function(){}),1):e.call(null)}))}},"33a4":function(e,t,r){var a=r("84f2"),o=r("2b4c")("iterator"),n=Array.prototype;e.exports=function(e){return void 0!==e&&(a.Array===e||n[o]===e)}},3591:function(e,t,r){"use strict";r("9fdd")},4954:function(e,t,r){},"4a59":function(e,t,r){var a=r("9b43"),o=r("1fa8"),n=r("33a4"),l=r("cb7c"),i=r("9def"),s=r("27ee"),c={},u={};t=e.exports=function(e,t,r,h,d){var p,y,g,f,m=d?function(){return e}:s(e),b=a(r,h,t?2:1),S=0;if("function"!=typeof m)throw TypeError(e+" is not iterable!");if(n(m)){for(p=i(e.length);p>S;S++)if(f=t?b(l(y=e[S])[0],y[1]):b(e[S]),f===c||f===u)return f}else for(g=m.call(e);!(y=g.next()).done;)if(f=o(g,b,y.value,t),f===c||f===u)return f};t.BREAK=c,t.RETURN=u},"4f7f":function(e,t,r){"use strict";var a=r("c26b"),o=r("b39a"),n="Set";e.exports=r("e0b8")(n,(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return a.def(o(this,n),e=0===e?0:e,e)}},a)},"55dd":function(e,t,r){"use strict";var a=r("5ca1"),o=r("d8e8"),n=r("4bf8"),l=r("79e5"),i=[].sort,s=[1,2,3];a(a.P+a.F*(l((function(){s.sort(void 0)}))||!l((function(){s.sort(null)}))||!r("2f21")(i)),"Array",{sort:function(e){return void 0===e?i.call(n(this)):i.call(n(this),o(e))}})},"59d8":function(e,t,r){},"5a39":function(e,t,r){"use strict";r("7221")},"5ac8":function(e,t,r){"use strict";r("7ee6")},"5cc5":function(e,t,r){var a=r("2b4c")("iterator"),o=!1;try{var n=[7][a]();n["return"]=function(){o=!0},Array.from(n,(function(){throw 2}))}catch(l){}e.exports=function(e,t){if(!t&&!o)return!1;var r=!1;try{var n=[7],i=n[a]();i.next=function(){return{done:r=!0}},n[a]=function(){return i},e(n)}catch(l){}return r}},"5df3":function(e,t,r){"use strict";var a=r("02f4")(!0);r("01f9")(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=a(t,r),this._i+=e.length,{value:e,done:!1})}))},"627c":function(e,t,r){var a=r("6d8b"),o=r("3eba"),n=r("2306"),l=r("f934"),i=l.getLayoutRect,s=r("eda2"),c=s.windowOpen;o.extendComponentModel({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),o.extendComponentView({type:"title",render:function(e,t,r){if(this.group.removeAll(),e.get("show")){var o=this.group,l=e.getModel("textStyle"),s=e.getModel("subtextStyle"),u=e.get("textAlign"),h=a.retrieve2(e.get("textBaseline"),e.get("textVerticalAlign")),d=new n.Text({style:n.setTextStyle({},l,{text:e.get("text"),textFill:l.getTextColor()},{disableBox:!0}),z2:10}),p=d.getBoundingRect(),y=e.get("subtext"),g=new n.Text({style:n.setTextStyle({},s,{text:y,textFill:s.getTextColor(),y:p.height+e.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),f=e.get("link"),m=e.get("sublink"),b=e.get("triggerEvent",!0);d.silent=!f&&!b,g.silent=!m&&!b,f&&d.on("click",(function(){c(f,"_"+e.get("target"))})),m&&g.on("click",(function(){c(f,"_"+e.get("subtarget"))})),d.eventData=g.eventData=b?{componentType:"title",componentIndex:e.componentIndex}:null,o.add(d),y&&o.add(g);var S=o.getBoundingRect(),P=e.getBoxLayoutParams();P.width=S.width,P.height=S.height;var C=i(P,{width:r.getWidth(),height:r.getHeight()},e.get("padding"));u||(u=e.get("left")||e.get("right"),"middle"===u&&(u="center"),"right"===u?C.x+=C.width:"center"===u&&(C.x+=C.width/2)),h||(h=e.get("top")||e.get("bottom"),"center"===h&&(h="middle"),"bottom"===h?C.y+=C.height:"middle"===h&&(C.y+=C.height/2),h=h||"top"),o.attr("position",[C.x,C.y]);var v={textAlign:u,textVerticalAlign:h};d.setStyle(v),g.setStyle(v),S=o.getBoundingRect();var T=C.margin,_=e.getItemStyle(["color","opacity"]);_.fill=e.get("backgroundColor");var w=new n.Rect({shape:{x:S.x-T[3],y:S.y-T[0],width:S.width+T[1]+T[3],height:S.height+T[0]+T[2],r:e.get("borderRadius")},style:_,subPixelOptimize:!0,silent:!0});o.add(w)}}})},6410:function(e,t,r){},"662d":function(e,t,r){},"67ab":function(e,t,r){var a=r("ca5a")("meta"),o=r("d3f4"),n=r("69a8"),l=r("86cc").f,i=0,s=Object.isExtensible||function(){return!0},c=!r("79e5")((function(){return s(Object.preventExtensions({}))})),u=function(e){l(e,a,{value:{i:"O"+ ++i,w:{}}})},h=function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!n(e,a)){if(!s(e))return"F";if(!t)return"E";u(e)}return e[a].i},d=function(e,t){if(!n(e,a)){if(!s(e))return!0;if(!t)return!1;u(e)}return e[a].w},p=function(e){return c&&y.NEED&&s(e)&&!n(e,a)&&u(e),e},y=e.exports={KEY:a,NEED:!1,fastKey:h,getWeak:d,onFreeze:p}},"6a2c":function(e,t,r){"use strict";r("8f8b")},"6de3":function(e,t,r){},7221:function(e,t,r){},"759c":function(e,t,r){"use strict";r("bebd")},"789e":function(e,t,r){"use strict";r("9d2d")},"7ee6":function(e,t,r){},"867e":function(e,t,r){"use strict";r("f27f")},"8f8b":function(e,t,r){},"9b54":function(e,t,r){"use strict";r("4954")},"9d2d":function(e,t,r){},"9fdd":function(e,t,r){},a1af:function(e,t,r){},a322:function(e,t,r){"use strict";r("1d2b3")},aaac:function(e,t,r){"use strict";r("190e")},abdd:function(e,t,r){},ad54:function(e,t,r){"use strict";r("662d")},b2d8:function(e,t,r){},b39a:function(e,t,r){var a=r("d3f4");e.exports=function(e,t){if(!a(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},bd72:function(e,t,r){"use strict";r("b2d8")},bebd:function(e,t,r){},c26b:function(e,t,r){"use strict";var a=r("86cc").f,o=r("2aeb"),n=r("dcbc"),l=r("9b43"),i=r("f605"),s=r("4a59"),c=r("01f9"),u=r("d53b"),h=r("7a56"),d=r("9e1e"),p=r("67ab").fastKey,y=r("b39a"),g=d?"_s":"size",f=function(e,t){var r,a=p(t);if("F"!==a)return e._i[a];for(r=e._f;r;r=r.n)if(r.k==t)return r};e.exports={getConstructor:function(e,t,r,c){var u=e((function(e,a){i(e,u,t,"_i"),e._t=t,e._i=o(null),e._f=void 0,e._l=void 0,e[g]=0,void 0!=a&&s(a,r,e[c],e)}));return n(u.prototype,{clear:function(){for(var e=y(this,t),r=e._i,a=e._f;a;a=a.n)a.r=!0,a.p&&(a.p=a.p.n=void 0),delete r[a.i];e._f=e._l=void 0,e[g]=0},delete:function(e){var r=y(this,t),a=f(r,e);if(a){var o=a.n,n=a.p;delete r._i[a.i],a.r=!0,n&&(n.n=o),o&&(o.p=n),r._f==a&&(r._f=o),r._l==a&&(r._l=n),r[g]--}return!!a},forEach:function(e){y(this,t);var r,a=l(e,arguments.length>1?arguments[1]:void 0,3);while(r=r?r.n:this._f){a(r.v,r.k,this);while(r&&r.r)r=r.p}},has:function(e){return!!f(y(this,t),e)}}),d&&a(u.prototype,"size",{get:function(){return y(this,t)[g]}}),u},def:function(e,t,r){var a,o,n=f(e,t);return n?n.v=r:(e._l=n={i:o=p(t,!0),k:t,v:r,p:a=e._l,n:void 0,r:!1},e._f||(e._f=n),a&&(a.n=n),e[g]++,"F"!==o&&(e._i[o]=n)),e},getEntry:f,setStrong:function(e,t,r){c(e,t,(function(e,r){this._t=y(e,t),this._k=r,this._l=void 0}),(function(){var e=this,t=e._k,r=e._l;while(r&&r.r)r=r.p;return e._t&&(e._l=r=r?r.n:e._t._f)?u(0,"keys"==t?r.k:"values"==t?r.v:[r.k,r.v]):(e._t=void 0,u(1))}),r?"entries":"values",!r,!0),h(t)}}},d487:function(e,t,r){},d95c:function(e,t,r){"use strict";r("a1af")},dcbc:function(e,t,r){var a=r("2aba");e.exports=function(e,t,r){for(var o in t)a(e,o,t[o],r);return e}},dd94:function(e,t,r){"use strict";r("59d8")},e08c:function(e,t,r){"use strict";r("6410")},e0b8:function(e,t,r){"use strict";var a=r("7726"),o=r("5ca1"),n=r("2aba"),l=r("dcbc"),i=r("67ab"),s=r("4a59"),c=r("f605"),u=r("d3f4"),h=r("79e5"),d=r("5cc5"),p=r("7f20"),y=r("5dbc");e.exports=function(e,t,r,g,f,m){var b=a[e],S=b,P=f?"set":"add",C=S&&S.prototype,v={},T=function(e){var t=C[e];n(C,e,"delete"==e||"has"==e?function(e){return!(m&&!u(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return m&&!u(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,r){return t.call(this,0===e?0:e,r),this})};if("function"==typeof S&&(m||C.forEach&&!h((function(){(new S).entries().next()})))){var _=new S,w=_[P](m?{}:-0,1)!=_,Y=h((function(){_.has(1)})),O=d((function(e){new S(e)})),x=!m&&h((function(){var e=new S,t=5;while(t--)e[P](t,t);return!e.has(-0)}));O||(S=t((function(t,r){c(t,S,e);var a=y(new b,t,S);return void 0!=r&&s(r,f,a[P],a),a})),S.prototype=C,C.constructor=S),(Y||x)&&(T("delete"),T("has"),f&&T("get")),(x||w)&&T(P),m&&C.clear&&delete C.clear}else S=g.getConstructor(t,e,f,P),l(S.prototype,r),i.NEED=!0;return p(S,e),v[e]=S,o(o.G+o.W+o.F*(S!=b),v),m||g.setStrong(S,e,f),S}},e1c1:function(e,t,r){"use strict";r("abdd")},edde:function(e,t,r){"use strict";r("efd8")},efd8:function(e,t,r){},f27f:function(e,t,r){},f605:function(e,t){e.exports=function(e,t,r,a){if(!(e instanceof t)||void 0!==a&&a in e)throw TypeError(r+": incorrect invocation!");return e}},f7c3:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tabs",{attrs:{type:"card"},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[r("el-tab-pane",{attrs:{label:"经销商数据分析",name:"first",lazy:!0}},[r("dealer")],1),r("el-tab-pane",{attrs:{label:"客户数据分析",name:"second",lazy:!0}},[r("store")],1),r("el-tab-pane",{attrs:{label:"产品数据分析",name:"third",lazy:!0}},[r("product")],1)],1)],1)},o=[],n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"dealer_container"},[r("div",{staticClass:"dealer_content_purchase_sales"},[r("div",{staticClass:"dealer_content_purchase_sales_content"},[r("purchaseSales",{attrs:{arrayPurchaseSales:e.arrayPurchaseSales}})],1)]),r("div",{staticClass:"dealer_content_purchase"},[r("div",{staticClass:"dealer_content_purchase_title"},[r("span",[e._v("进货量")]),r("el-tooltip",{attrs:{effect:"dark",content:"数据来源于SAP",placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1),r("div",{staticClass:"dealer_content_purchase_bar"},[r("div",{staticClass:"dealer_content_purchase_bar_year"},[r("purchaseYear",{attrs:{arrayPurchaseYear:e.arrayPurchaseYear}})],1),e._m(0),r("div",{staticClass:"dealer_content_purchase_bar_product"},[r("purchaseProduct",{attrs:{arrayPurchaseProduct:e.arrayPurchaseProduct}})],1)])]),r("div",{staticClass:"dealer_content_sales"},[r("div",{staticClass:"dealer_content_sales_title"},[e._v("\n            销量\n        ")]),r("div",{staticClass:"dealer_content_sales_bar"},[r("div",{staticClass:"dealer_content_sales_bar_year"},[r("salesYear",{attrs:{arraySalesYear:e.arraySalesYear}})],1),e._m(1),r("div",{staticClass:"dealer_content_sales_bar_product"},[r("salesProduct",{attrs:{arraySalesProduct:e.arraySalesProduct}})],1)])])])},l=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"dealer_content_purchase_bar_middle"},[r("div",{staticClass:"dealer_content_purchase_bar_middle_line"})])},function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"dealer_content_sales_bar_middle"},[r("div",{staticClass:"dealer_content_sales_bar_middle_line"})])}],i=(r("8e6e"),r("456d"),r("a481"),r("ac6a"),r("55dd"),r("768b")),s=(r("96cf"),r("3b8d")),c=r("bd86"),u=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"purchase_sales_content_top"},[r("div",{staticClass:"purchase_sales_content_top_title"},[e._v("\n            进销存数据汇总 (单位: 升)\n           "),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1),r("el-button",{staticClass:"purchase_sales_content_top_detail",attrs:{loading:e.viewDetailLoading,size:"mini"},on:{click:e.viewDetailClick}},[e._v("\n            查看详情\n        ")])],1),e.arrayFilterCriteria.length>0?r("div",{staticClass:"purchase_sales_content_filter"},[r("div",{staticClass:"purchase_sales_content_filter_title"},[e._v("\n            筛选条件:\n        ")]),r("div",{staticClass:"purchase_sales_content_filter_content"},e._l(e.arrayFilterCriteria,(function(t,a){return r("div",{key:a,staticClass:"purchase_sales_content_filter_list"},[r("div",{staticClass:"purchase_sales_content_filter_list_item"},[r("span",[e._v(e._s(t))])])])})),0)]):e._e(),r("div",{staticStyle:{height:"250px",position:"relative"}},[r("chartBar",{attrs:{width:e.chartBarWidth,height:"250px",option:e.optionPurchaseSales}}),r("div",{staticStyle:{width:"8px",height:"2px",position:"absolute",top:"20px",left:"calc(50% + 27px)",background:"#878787"}})],1),r("el-dialog",{attrs:{title:"进销存数据汇总 (单位: 升)",visible:e.dialogTableVisible,width:"60%"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"渠道:","label-width":"40px"}},[r("el-select",{staticStyle:{width:"130px"},attrs:{multiple:"",clearable:"",size:e.queryChannelName.length<2?"small":"",placeholder:"请选择"},on:{change:e.channelChange},model:{value:e.queryChannelName,callback:function(t){e.queryChannelName=t},expression:"queryChannelName"}},e._l(e.optionChannelNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"产品分类:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{clearable:"",multiple:"",size:e.queryProductCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.productCategoryChange},model:{value:e.queryProductCategory,callback:function(t){e.queryProductCategory=t},expression:"queryProductCategory"}},e._l(e.optionProductCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品名称:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.dialogTableData,border:"",stripe:"","show-summary":"","summary-method":e.getSummaries,size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"yearMonth",label:"月份"}}),r("el-table-column",{attrs:{prop:"sellIn",label:"采购",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellIn))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThrough",label:"销售",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThrough))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"stockQuantity",label:"库存",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.stockQuantity))+"\n                ")]}}])})],1)],1)],1)},h=[],d=(r("5df3"),r("4f7f"),r("75fc")),p=(r("7f7f"),function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{ref:"chartBar",style:"width: "+e.width+"; height: "+e.height+";"})}),y=[],g=r("3eba"),f=r.n(g),m=(r("94b1"),r("627c"),r("d28f"),r("007d"),{name:"componentChartBar",props:{width:{type:String,default:"100%"},height:{type:String,default:"100%"},option:{type:Object}},data:function(){return{myChartBar:null}},mounted:function(){this.myChartBar=f.a.init(this.$refs.chartBar),this.myChartBar.setOption(this.option)},beforeDestroy:function(){this.myChartBar&&(this.myChartBar.dispose(),this.myChartBar=null)},watch:{option:{deep:!0,immediate:!0,handler:function(e,t){this.myChartBar&&(e?this.myChartBar.setOption(e):this.myChartBar.setOption(t))}},width:function(e){this.myChartBar.resize({width:e})},height:function(e){this.myChartBar.resize({height:e})}}}),b=m,S=(r("e1c1"),r("2877")),P=Object(S["a"])(b,p,y,!1,null,"14ec1d46",null),C=P.exports,v=r("2f62"),T=r("fd5e");function _(e){for(var t=[],r=0;r<14;r++)t.push("");e.forEach((function(e){e.indexOf("德乐高端")>=0?t[0]="德乐高端":e.indexOf("德乐中端")>=0?t[1]="德乐中端":e.indexOf("德乐低端")>=0?t[2]="德乐低端":e.indexOf("德乐其它")>=0?t[3]="德乐其它":e.indexOf("液压油 Mid Tier")>=0?t[4]="液压油 Mid Tier":e.indexOf("液压油 Low Tier")>=0?t[5]="液压油 Low Tier":e.indexOf("液压油其它")>=0?t[6]="液压油其它":e.indexOf("商用油其它")>=0?t[7]="商用油其它":e.indexOf("金富力高端")>=0?t[8]="金富力高端":e.indexOf("金富力低端")>=0?t[9]="金富力低端":e.indexOf("金富力其它")>=0?t[10]="金富力其它":e.indexOf("防冻液")>=0?t[11]="防冻液":e.indexOf("TCP添加剂")>=0?t[12]="TCP添加剂":e.indexOf("乘用车其它")>=0?t[13]="乘用车其它":t.push(e)}));var a=t.filter((function(e){return e.length>0}));return a}function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var O={components:{chartBar:C},props:{arrayPurchaseSales:{type:Array}},data:function(){return{chartBarWidth:"100%",arrayPurchaseSalesCategory:[],arrayPurchaseSalesSellIn:[],arrayPurchaseSalesSellThrough:[],arrayPurchaseSalesStockQuantity:[],arrayPurchaseSalesCategoryTemp:[],arrayPurchaseSalesSellInTemp:[],arrayPurchaseSalesSellThroughTemp:[],arrayPurchaseSalesStockQuantityTemp:[],viewDetailLoading:!1,dialogTableVisible:!1,dialogTableData:[],arrayPurchaseSalesTotal:[],arrayPurchaseSalesShow:[],arrayPurchaseYearMonth:[],optionChannelNames:[],queryChannelName:[],optionProductCategory:[],queryProductCategory:[],optionProductNames:[],queryProductName:"",arrayFilterCriteria:[]}},computed:Y(Y({},Object(v["b"])(["dealerInfo","dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"},optionPurchaseSales:function(){var e=this;return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){var r='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#878787;"></span>';return t[0].name+"<br/>"+t[1].marker+t[1].seriesName+": "+e.$options.filters.toThousand(t[1].value)+"<br/>"+t[2].marker+t[2].seriesName+": "+e.$options.filters.toThousand(t[2].value)+"<br/>"+r+t[0].seriesName+": "+e.$options.filters.toThousand(t[0].value)}},legend:{data:["采购","销售","库存"],top:10,itemWidth:6,itemHeight:6,textStyle:{color:"#282828",fontSize:12}},grid:{top:40,left:80,right:30,bottom:40},xAxis:{type:"category",data:this.arrayPurchaseSalesCategory,axisLine:{lineStyle:{color:"#D9D9D9"}},axisTick:{show:!1},axisLabel:{fontSize:10,color:"#282828"}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828"},splitLine:{lineStyle:{color:"#ECECEC"}}},series:[{name:"库存",type:"bar",data:this.arrayPurchaseSalesStockQuantity,itemStyle:{color:"rgba(0,0,0,0)"},barWidth:20,label:{show:!0,position:[10,-2],fontSize:2,color:"#282828",backgroundColor:"#878787",lineHeight:2,formatter:function(){return"               "}}},{name:"采购",type:"bar",data:this.arrayPurchaseSalesSellIn,itemStyle:{color:"#B0C7EA"},barWidth:20},{name:"销售",type:"bar",data:this.arrayPurchaseSalesSellThrough,itemStyle:{color:"#FD7702"},barGap:"-75%",barWidth:10}]}}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.chartBarWidth=.8*(t-72)+"px"}))},watch:{arrayPurchaseSales:{deep:!0,handler:function(e){var t=this;e&&(this.arrayPurchaseSalesCategory.splice(0,this.arrayPurchaseSalesCategory.length),this.arrayPurchaseSalesSellIn.splice(0,this.arrayPurchaseSalesSellIn.length),this.arrayPurchaseSalesSellThrough.splice(0,this.arrayPurchaseSalesSellThrough.length),this.arrayPurchaseSalesStockQuantity.splice(0,this.arrayPurchaseSalesStockQuantity.length),this.arrayPurchaseSalesCategoryTemp.splice(0,this.arrayPurchaseSalesCategoryTemp.length),this.arrayPurchaseSalesSellInTemp.splice(0,this.arrayPurchaseSalesSellInTemp.length),this.arrayPurchaseSalesSellThroughTemp.splice(0,this.arrayPurchaseSalesSellThroughTemp.length),this.arrayPurchaseSalesStockQuantityTemp.splice(0,this.arrayPurchaseSalesStockQuantityTemp.length),e.forEach((function(e){t.arrayPurchaseSalesCategory.push(e.yearMonth),t.arrayPurchaseSalesSellIn.push(Math.round(e.sellIn)),t.arrayPurchaseSalesSellThrough.push(Math.round(e.sellThrough)),t.arrayPurchaseSalesStockQuantity.push(Math.round(e.stockQuantity)),t.arrayPurchaseSalesCategoryTemp.push(e.yearMonth),t.arrayPurchaseSalesSellInTemp.push(Math.round(e.sellIn)),t.arrayPurchaseSalesSellThroughTemp.push(Math.round(e.sellThrough)),t.arrayPurchaseSalesStockQuantityTemp.push(Math.round(e.stockQuantity))})),this.arrayPurchaseSalesTotal=[])}}},methods:{viewDetailClick:function(){this.arrayPurchaseSalesTotal.length>0?this.dialogTableVisible=!0:this.getDmsDealerPurchaseSalesDetailInfo()},getDmsDealerPurchaseSalesDetailInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.viewDetailLoading=!0,e.next=3,T["a"].getDmsDealerPurchaseSalesDetailInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear});case 3:t=e.sent,r=Object(i["a"])(t,2),a=r[0],o=r[1],a&&this.formateDealerPurchaseSalesDetailInfo(o.data),this.viewDetailLoading=!1,this.dialogTableVisible=!0;case 10:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),formateDealerPurchaseSalesDetailInfo:function(e){this.arrayPurchaseSalesTotal=e.sellInDecList.sort((function(e,t){return e.month-t.month})),this.arrayPurchaseSalesShow=this.arrayPurchaseSalesTotal,this.formatePurchaseFilterChannel(),this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.queryChannelName=[],this.queryProductCategory=[],this.queryProductName="",this.formatDialogTableData(),this.formatPurchaseYearMonth(),this.formatFilterCriteria()},formatePurchaseFilterChannel:function(){var e=this;this.optionChannelNames=[];var t=[];this.arrayPurchaseSalesShow.forEach((function(e){null!==e.productChannel&&""!==e.productChannel&&t.push(e.productChannel)}));var r=Object(d["a"])(new Set(t));r.forEach((function(t){var r="其他";"Consumer"===t?r="乘用车":"Commercial"===t?r="商用油":"Chevron Others"===t&&(r="雪佛龙其它"),e.optionChannelNames.push({value:t,label:r})}))},formatePurchaseFilterProductCategory:function(){this.optionProductCategory=[];var e=[];this.arrayPurchaseSalesShow.forEach((function(t){null!==t.productCategory&&""!==t.productCategory&&e.push(t.productCategory)})),this.optionProductCategory=Object(d["a"])(new Set(e)),this.optionProductCategory=_(this.optionProductCategory)},formatePurchaseFilterProductName:function(){this.optionProductNames=[];var e=[];this.arrayPurchaseSalesShow.forEach((function(t){null!==t.productNameCn&&""!==t.productNameCn&&e.push(t.productNameCn)})),this.optionProductNames=Object(d["a"])(new Set(e))},formatPurchaseYearMonth:function(){this.arrayPurchaseYearMonth=[];var e=[];this.arrayPurchaseSalesShow.forEach((function(t){null!==t.month&&""!==t.month&&e.push(t.month)})),this.arrayPurchaseYearMonth=Object(d["a"])(new Set(e))},purchaseFilterChange:function(){var e=this;if(this.arrayPurchaseSalesShow=this.arrayPurchaseSalesTotal,this.queryChannelName.length>0){var t=[];this.queryChannelName.forEach((function(r){var a=e.arrayPurchaseSalesShow.filter((function(e){return e.productChannel===r}));t=t.concat(a)})),this.arrayPurchaseSalesShow=t}if(this.queryProductCategory.length>0){var r=[];this.queryProductCategory.forEach((function(t){var a=e.arrayPurchaseSalesShow.filter((function(e){return e.productCategory===t}));r=r.concat(a)})),this.arrayPurchaseSalesShow=r}if(this.queryProductName.length>0){var a=[];a=this.arrayPurchaseSalesShow.filter((function(t){return t.productNameCn===e.queryProductName})),this.arrayPurchaseSalesShow=a}},channelChange:function(){this.purchaseFilterChange(),0===this.queryChannelName.length&&this.formatePurchaseFilterChannel(),this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatDialogTableData(),this.formatFilterCriteria(),this.formatChartBarInfo()},productCategoryChange:function(){this.purchaseFilterChange(),this.formatePurchaseFilterChannel(),0===this.queryProductCategory.length&&this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatDialogTableData(),this.formatFilterCriteria(),this.formatChartBarInfo()},productNameChange:function(){this.purchaseFilterChange(),this.formatePurchaseFilterChannel(),this.formatePurchaseFilterProductCategory(),0===this.queryProductName.length&&this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatDialogTableData(),this.formatFilterCriteria(),this.formatChartBarInfo()},formatFilterCriteria:function(){if(this.arrayFilterCriteria=[],this.queryChannelName.length>0){var e=[];this.queryChannelName.forEach((function(t){var r="其他";"Consumer"===t?r="乘用车":"Commercial"===t?r="商用油":"Chevron Others"===t&&(r="雪佛龙其它"),e.push(r)})),this.arrayFilterCriteria.push("渠道: "+e.join(", "))}this.queryProductCategory.length>0&&this.arrayFilterCriteria.push("产品分类: "+this.queryProductCategory.join(", ")),this.queryProductName.length>0&&this.arrayFilterCriteria.push("产品名称: "+this.queryProductName)},formatDialogTableData:function(){var e=this;this.dialogTableData=[];var t=[],r=[];this.arrayPurchaseSalesShow.forEach((function(e){null!==e.month&&""!==e.month&&r.push(e.month)})),t=Object(d["a"])(new Set(r)),t.forEach((function(t){var r=e.arrayPurchaseSalesShow.filter((function(e){return e.month===t})),a=0,o=0,n=0;r.forEach((function(e){a+=e.sellInLY,o+=e.sellThroughLY,n+=e.stockQuantityY})),e.dialogTableData.push({yearMonth:t.substring(0,4)+"-"+t.substring(4),sellIn:Math.round(a),sellThrough:Math.round(o),stockQuantity:Math.round(n)})}))},formatChartBarInfo:function(){var e=this;if(0===this.queryChannelName.length&&0===this.queryProductCategory.length&&0===this.queryProductName.length){this.arrayPurchaseSalesCategory.splice(0,this.arrayPurchaseSalesCategory.length),this.arrayPurchaseSalesSellIn.splice(0,this.arrayPurchaseSalesSellIn.length),this.arrayPurchaseSalesSellThrough.splice(0,this.arrayPurchaseSalesSellThrough.length),this.arrayPurchaseSalesStockQuantity.splice(0,this.arrayPurchaseSalesStockQuantity.length);for(var t=0;t<this.arrayPurchaseSalesCategoryTemp.length;t++)this.arrayPurchaseSalesCategory.push(this.arrayPurchaseSalesCategoryTemp[t]),this.arrayPurchaseSalesSellIn.push(Math.round(this.arrayPurchaseSalesSellInTemp[t])),this.arrayPurchaseSalesSellThrough.push(Math.round(this.arrayPurchaseSalesSellThroughTemp[t])),this.arrayPurchaseSalesStockQuantity.push(Math.round(this.arrayPurchaseSalesStockQuantityTemp[t]))}else this.arrayPurchaseSalesCategory.splice(0,this.arrayPurchaseSalesCategory.length),this.arrayPurchaseSalesSellIn.splice(0,this.arrayPurchaseSalesSellIn.length),this.arrayPurchaseSalesSellThrough.splice(0,this.arrayPurchaseSalesSellThrough.length),this.arrayPurchaseSalesStockQuantity.splice(0,this.arrayPurchaseSalesStockQuantity.length),this.arrayPurchaseYearMonth.forEach((function(t){var r=e.arrayPurchaseSalesShow.filter((function(e){return e.month===t})),a=0,o=0,n=0;r.forEach((function(e){a+=e.sellInLY,o+=e.sellThroughLY,n+=e.stockQuantityY})),e.arrayPurchaseSalesCategory.push(t.substring(0,4)+"-"+t.substring(4)),e.arrayPurchaseSalesSellIn.push(Math.round(a)),e.arrayPurchaseSalesSellThrough.push(Math.round(o)),e.arrayPurchaseSalesStockQuantity.push(Math.round(n))}))},getSummaries:function(){for(var e=[],t=0;t<4;t++)e[t]=0===t?"合计":t<1?"":0;return this.arrayPurchaseSalesShow.forEach((function(t){e[1]+=t.sellInLY,e[2]+=t.sellThroughLY,e[3]+=t.stockQuantityY})),e[1]=this.$options.filters.toThousand(Math.round(e[1])),e[2]=this.$options.filters.toThousand(Math.round(e[2])),e[3]=this.$options.filters.toThousand(Math.round(e[3])),e}}},x=O,N=(r("1f20"),Object(S["a"])(x,u,h,!1,null,"cd43af4a",null)),D=N.exports,k=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"purchase_year_content_top"},[r("div",{staticClass:"purchase_year_content_top_title"},[e._v("\n            "+e._s(e.dealerYear)+" VS "+e._s(e.dealerYear-1)+"  (单位: 升)\n            "),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1),r("el-button",{staticClass:"purchase_year_content_top_detail",attrs:{loading:e.viewDetailLoading,size:"mini"},on:{click:e.viewDetailClick}},[e._v("\n            查看详情\n        ")])],1),e.arrayFilterCriteria.length>0?r("div",{staticClass:"purchase_year_content_filter"},[r("div",{staticClass:"purchase_year_content_filter_title"},[e._v("\n            筛选条件:\n        ")]),r("div",{staticClass:"purchase_year_content_filter_content"},e._l(e.arrayFilterCriteria,(function(t,a){return r("div",{key:a,staticClass:"purchase_year_content_filter_list"},[r("div",{staticClass:"purchase_year_content_filter_list_item"},[r("span",[e._v(e._s(t))])])])})),0)]):e._e(),r("div",{staticStyle:{height:"250px",position:"relative"}},[r("chartBar",{attrs:{width:e.chartBarWidth,height:"250px",option:e.optionPurchaseYear}}),r("div",{staticStyle:{width:"100%",height:"20px",position:"absolute",top:"10px",display:"flex","justify-content":"center"}},[r("div",{staticStyle:{"align-self":"center",width:"6px",height:"6px",background:"#8AAAE1"}}),r("div",{staticStyle:{"align-self":"center","margin-left":"5px","font-size":"12px",color:"#282828"}},[e._v("\n                "+e._s(e.dealerYear)+"年进货量\n            ")]),r("div",{staticStyle:{"align-self":"center","margin-left":"20px",width:"6px",height:"6px",background:"#FFAE7B"}}),r("div",{staticStyle:{"align-self":"center","margin-left":"5px","font-size":"12px",color:"#282828"}},[e._v("\n                "+e._s(e.dealerYear-1)+"年进货量\n            ")])])],1),r("el-dialog",{attrs:{title:"进货量 "+e.dealerYear+" VS "+(e.dealerYear-1)+" (单位: 升)",visible:e.dialogTableVisible,width:"60%"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"渠道:","label-width":"40px"}},[r("el-select",{staticStyle:{width:"130px"},attrs:{multiple:"",clearable:"",size:e.queryChannelName.length<2?"small":"",placeholder:"请选择"},on:{change:e.channelChange},model:{value:e.queryChannelName,callback:function(t){e.queryChannelName=t},expression:"queryChannelName"}},e._l(e.optionChannelNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"产品分类:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{clearable:"",multiple:"",size:e.queryProductCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.productCategoryChange},model:{value:e.queryProductCategory,callback:function(t){e.queryProductCategory=t},expression:"queryProductCategory"}},e._l(e.optionProductCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品名称:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayPurchaseYearShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"","show-summary":"","summary-method":e.getSummaries,size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"month",label:"月份",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(t.row.month.substring(4,5).replace("0","")+t.row.month.substring(5)+"月")+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"productChannel",label:"渠道",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s("Commercial"===t.row.productChannel?"商用油":"Consumer"===t.row.productChannel?"乘用车":"Chevron Others"===t.row.productChannel?"雪佛龙其它":"其它")+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"productCategory",label:"产品分类",width:"100"}}),r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellInLY",label:e.dealerYear+"年进货量",width:"90",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellInLY)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellinLY1",label:e.dealerYear-1+"年进货量",width:"90",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellinLY1)))+"\n                ")]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayPurchaseYearShow.length},on:{"current-change":e.currentChange}})],1)],1)},F=[];function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var I={components:{chartBar:C},props:{arrayPurchaseYear:{type:Array}},data:function(){return{chartBarWidth:"100%",arrayPurchaseYearCategory:[],arrayPurchaseYearSellIn:[],arrayPurchaseYearSellInPrev:[],arrayPurchaseYearCategoryTemp:[],arrayPurchaseYearSellInTemp:[],arrayPurchaseYearSellInPrevTemp:[],viewDetailLoading:!1,dialogTableVisible:!1,arrayPurchaseYearTotal:[],arrayPurchaseYearShow:[],currentPage:1,pageSize:10,arrayPurchaseYearMonth:[],optionChannelNames:[],queryChannelName:[],optionProductCategory:[],queryProductCategory:[],optionProductNames:[],queryProductName:"",arrayFilterCriteria:[]}},computed:q(q({},Object(v["b"])(["dealerInfo","dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+(new Date).getMonth()+"月";return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"年"+e+"的数"}return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"全年的数"},optionPurchaseYear:function(){var e=this,t=this.dealerYear+"年进货量",r=this.dealerYear-1+"年进货量";return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(a){return a[0].name+"<br/>"+a[0].marker+t+": "+e.$options.filters.toThousand(a[0].value)+"<br/>"+a[1].marker+r+": "+e.$options.filters.toThousand(a[1].value)}},grid:{top:40,left:70,right:30,bottom:40},xAxis:{type:"category",data:this.arrayPurchaseYearCategory,axisLine:{lineStyle:{color:"#D9D9D9"}},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828",rotate:45}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828"},splitLine:{lineStyle:{color:"#ECECEC"}}},series:[{name:"当年进货量",type:"bar",data:this.arrayPurchaseYearSellIn,itemStyle:{color:"#8AAAE1"},barWidth:20,z:5},{name:"去年进货量",type:"bar",data:this.arrayPurchaseYearSellInPrev,itemStyle:{color:"#FFAE7B"},barGap:"-75%",barWidth:10,z:10}]}}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.chartBarWidth=(t-92)/2+"px"}))},watch:{arrayPurchaseYear:{deep:!0,handler:function(e){var t=this;e&&(this.arrayPurchaseYearCategory.splice(0,this.arrayPurchaseYearCategory.length),this.arrayPurchaseYearSellIn.splice(0,this.arrayPurchaseYearSellIn.length),this.arrayPurchaseYearSellInPrev.splice(0,this.arrayPurchaseYearSellInPrev.length),this.arrayPurchaseYearCategoryTemp.splice(0,this.arrayPurchaseYearCategoryTemp.length),this.arrayPurchaseYearSellInTemp.splice(0,this.arrayPurchaseYearSellInTemp.length),this.arrayPurchaseYearSellInPrevTemp.splice(0,this.arrayPurchaseYearSellInPrevTemp.length),e.forEach((function(e){t.arrayPurchaseYearCategory.push(e.month),t.arrayPurchaseYearSellIn.push(Math.round(e.sellIn)),t.arrayPurchaseYearSellInPrev.push(Math.round(e.sellInPrev)),t.arrayPurchaseYearCategoryTemp.push(e.month),t.arrayPurchaseYearSellInTemp.push(Math.round(e.sellIn)),t.arrayPurchaseYearSellInPrevTemp.push(Math.round(e.sellInPrev))})),this.arrayPurchaseYearTotal=[])}}},methods:{viewDetailClick:function(){this.arrayPurchaseYearTotal.length>0?this.dialogTableVisible=!0:this.getDmsDealerPurchaseDetailInfo()},getDmsDealerPurchaseDetailInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o,n,l=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.viewDetailLoading=!0,t=function(){if(l.dealerYear==(new Date).getFullYear()){var e=(new Date).getMonth();return e<10?l.dealerYear+"0"+e||1:l.dealerYear+""+e}return l.dealerYear+"12"},e.next=4,T["a"].getDmsDealerPurchaseDetailInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear,endMonth:t()});case 4:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&this.formateDealerPurchaseDetailInfo(n.data),this.viewDetailLoading=!1,this.dialogTableVisible=!0;case 11:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),formateDealerPurchaseDetailInfo:function(e){this.arrayPurchaseYearTotal=e.sellInDecList.sort((function(e,t){return e.month===t.month?e.productChannel-t.productChannel:e.month-t.month})),this.arrayPurchaseYearShow=this.arrayPurchaseYearTotal,this.formatePurchaseFilterChannel(),this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.queryChannelName=[],this.queryProductCategory=[],this.queryProductName="",this.formatPurchaseYearMonth(),this.formatFilterCriteria()},formatePurchaseFilterChannel:function(){var e=this;this.optionChannelNames=[];var t=[];this.arrayPurchaseYearShow.forEach((function(e){null!==e.productChannel&&""!==e.productChannel&&t.push(e.productChannel)}));var r=Object(d["a"])(new Set(t));r.forEach((function(t){var r="其他";"Consumer"===t?r="乘用车":"Commercial"===t?r="商用油":"Chevron Others"===t&&(r="雪佛龙其它"),e.optionChannelNames.push({value:t,label:r})}))},formatePurchaseFilterProductCategory:function(){this.optionProductCategory=[];var e=[];this.arrayPurchaseYearShow.forEach((function(t){null!==t.productCategory&&""!==t.productCategory&&e.push(t.productCategory)})),this.optionProductCategory=Object(d["a"])(new Set(e)),this.optionProductCategory=_(this.optionProductCategory)},formatePurchaseFilterProductName:function(){this.optionProductNames=[];var e=[];this.arrayPurchaseYearShow.forEach((function(t){null!==t.productNameCn&&""!==t.productNameCn&&e.push(t.productNameCn)})),this.optionProductNames=Object(d["a"])(new Set(e))},formatPurchaseYearMonth:function(){this.arrayPurchaseYearMonth=[];var e=[];this.arrayPurchaseYearTotal.forEach((function(t){null!==t.month&&""!==t.month&&e.push(t.month)})),this.arrayPurchaseYearMonth=Object(d["a"])(new Set(e))},purchaseFilterChange:function(){var e=this;if(this.arrayPurchaseYearShow=this.arrayPurchaseYearTotal,this.queryChannelName.length>0){var t=[];this.queryChannelName.forEach((function(r){var a=e.arrayPurchaseYearShow.filter((function(e){return e.productChannel===r}));t=t.concat(a)})),this.arrayPurchaseYearShow=t}if(this.queryProductCategory.length>0){var r=[];this.queryProductCategory.forEach((function(t){var a=e.arrayPurchaseYearShow.filter((function(e){return e.productCategory===t}));r=r.concat(a)})),this.arrayPurchaseYearShow=r}if(this.queryProductName.length>0){var a=[];a=this.arrayPurchaseYearShow.filter((function(t){return t.productNameCn===e.queryProductName})),this.arrayPurchaseYearShow=a}},channelChange:function(){this.purchaseFilterChange(),0===this.queryChannelName.length&&this.formatePurchaseFilterChannel(),this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productCategoryChange:function(){this.purchaseFilterChange(),this.formatePurchaseFilterChannel(),0===this.queryProductCategory.length&&this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productNameChange:function(){this.purchaseFilterChange(),this.formatePurchaseFilterChannel(),this.formatePurchaseFilterProductCategory(),0===this.queryProductName.length&&this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},formatFilterCriteria:function(){if(this.arrayFilterCriteria=[],this.queryChannelName.length>0){var e=[];this.queryChannelName.forEach((function(t){var r="其他";"Consumer"===t?r="乘用车":"Commercial"===t?r="商用油":"Chevron Others"===t&&(r="雪佛龙其它"),e.push(r)})),this.arrayFilterCriteria.push("渠道: "+e.join(", "))}this.queryProductCategory.length>0&&this.arrayFilterCriteria.push("产品分类: "+this.queryProductCategory.join(", ")),this.queryProductName.length>0&&this.arrayFilterCriteria.push("产品名称: "+this.queryProductName)},formatChartBarInfo:function(){var e=this;if(0===this.queryChannelName.length&&0===this.queryProductCategory.length&&0===this.queryProductName.length){this.arrayPurchaseYearCategory.splice(0,this.arrayPurchaseYearCategory.length),this.arrayPurchaseYearSellIn.splice(0,this.arrayPurchaseYearSellIn.length),this.arrayPurchaseYearSellInPrev.splice(0,this.arrayPurchaseYearSellInPrev.length);for(var t=0;t<this.arrayPurchaseYearCategoryTemp.length;t++)this.arrayPurchaseYearCategory.push(this.arrayPurchaseYearCategoryTemp[t]),this.arrayPurchaseYearSellIn.push(Math.round(this.arrayPurchaseYearSellInTemp[t])),this.arrayPurchaseYearSellInPrev.push(Math.round(this.arrayPurchaseYearSellInPrevTemp[t]))}else this.arrayPurchaseYearCategory.splice(0,this.arrayPurchaseYearCategory.length),this.arrayPurchaseYearSellIn.splice(0,this.arrayPurchaseYearSellIn.length),this.arrayPurchaseYearSellInPrev.splice(0,this.arrayPurchaseYearSellInPrev.length),this.arrayPurchaseYearMonth.forEach((function(t){var r=e.arrayPurchaseYearShow.filter((function(e){return e.month===t})),a=0,o=0;r.forEach((function(e){a+=e.sellInLY,o+=e.sellinLY1})),e.arrayPurchaseYearCategory.push(t.substring(4,5).replace("0","")+t.substring(5)+"月"),e.arrayPurchaseYearSellIn.push(Math.round(a)),e.arrayPurchaseYearSellInPrev.push(Math.round(o))}))},currentChange:function(e){this.currentPage=e},getSummaries:function(){for(var e=[],t=0;t<6;t++)e[t]=0===t?"合计":t<4?"":0;return this.arrayPurchaseYearShow.forEach((function(t){e[4]+=t.sellInLY,e[5]+=t.sellinLY1})),e[4]=this.$options.filters.toThousand(Math.round(e[4])),e[5]=this.$options.filters.toThousand(Math.round(e[5])),e}}},L=I,E=(r("aaac"),Object(S["a"])(L,k,F,!1,null,"1ca23520",null)),M=E.exports,z=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"purchase_product_content_top"},[r("div",{staticClass:"purchase_product_content_top_title"},[e._v("\n            "+e._s(e.dealerYear)+" 乘用车 VS 商用油 (单位: 升)\n            "),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1),r("el-button",{staticClass:"purchase_product_content_top_detail",attrs:{loading:e.viewDetailLoading,size:"mini"},on:{click:e.viewDetailClick}},[e._v("\n            查看详情\n        ")])],1),e.arrayFilterCriteria.length>0?r("div",{staticClass:"purchase_product_content_filter"},[r("div",{staticClass:"purchase_product_content_filter_title"},[e._v("\n            筛选条件:\n        ")]),r("div",{staticClass:"purchase_product_content_filter_content"},e._l(e.arrayFilterCriteria,(function(t,a){return r("div",{key:a,staticClass:"purchase_product_content_filter_list"},[r("div",{staticClass:"purchase_product_content_filter_list_item"},[r("span",[e._v(e._s(t))])])])})),0)]):e._e(),r("chartBar",{attrs:{width:e.chartBarWidth,height:"250px",option:e.optionPurchaseProduct}}),r("el-dialog",{attrs:{title:"进货量 "+e.dealerYear+" 乘用车 VS 商用油 (单位: 升)",visible:e.dialogTableVisible,width:"60%"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"产品分类:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{clearable:"",multiple:"",size:e.queryProductCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.productCategoryChange},model:{value:e.queryProductCategory,callback:function(t){e.queryProductCategory=t},expression:"queryProductCategory"}},e._l(e.optionProductCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品名称:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayPurchaseProductShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"","show-summary":"","summary-method":e.getSummaries,size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"yearMonth",label:"月份",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(t.row.month.substring(0,4)+"-"+t.row.month.substring(4))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"productCategory",label:"产品分类",width:"100"}}),r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellInLYConsume",label:"乘用车",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellInLYConsume)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellInLYCommercial",label:"商用油",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellInLYCommercial)))+"\n                ")]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayPurchaseProductShow.length},on:{"current-change":e.currentChange}})],1)],1)},W=[];function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var V={components:{chartBar:C},props:{arrayPurchaseProduct:{type:Array}},data:function(){return{chartBarWidth:"100%",arrayPurchaseProductCategory:[],arrayPurchaseProductConsumer:[],arrayPurchaseProductCommercial:[],arrayPurchaseProductCategoryTemp:[],arrayPurchaseProductConsumerTemp:[],arrayPurchaseProductCommercialTemp:[],viewDetailLoading:!1,dialogTableVisible:!1,arrayPurchaseProductTotal:[],arrayPurchaseProductShow:[],currentPage:1,pageSize:10,arrayPurchaseYearMonth:[],optionProductCategory:[],queryProductCategory:[],optionProductNames:[],queryProductName:"",arrayFilterCriteria:[]}},computed:A(A({},Object(v["b"])(["dealerInfo","dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"},optionPurchaseProduct:function(){var e=this;return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){return t[0].name+"<br/>"+t[0].marker+t[0].seriesName+": "+e.$options.filters.toThousand(t[0].value)+"<br/>"+t[1].marker+t[1].seriesName+": "+e.$options.filters.toThousand(t[1].value)}},legend:{data:["乘用车","商用油"],top:10,itemWidth:6,itemHeight:6,textStyle:{color:"#282828",fontSize:12}},grid:{top:40,left:70,right:30,bottom:50},xAxis:{type:"category",data:this.arrayPurchaseProductCategory,axisLine:{lineStyle:{color:"#D9D9D9"}},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828",rotate:45}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828"},splitLine:{lineStyle:{color:"#ECECEC"}}},series:[{name:"乘用车",type:"bar",data:this.arrayPurchaseProductConsumer,itemStyle:{color:"#FD7702"},barGap:"10%",barWidth:10},{name:"商用油",type:"bar",data:this.arrayPurchaseProductCommercial,itemStyle:{color:"#3870CA"},barGap:"10%",barWidth:10}]}}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.chartBarWidth=(t-92)/2+"px"}))},watch:{arrayPurchaseProduct:{deep:!0,handler:function(e){var t=this;e&&(this.arrayPurchaseProductCategory.splice(0,this.arrayPurchaseProductCategory.length),this.arrayPurchaseProductConsumer.splice(0,this.arrayPurchaseProductConsumer.length),this.arrayPurchaseProductCommercial.splice(0,this.arrayPurchaseProductCommercial.length),this.arrayPurchaseProductCategoryTemp.splice(0,this.arrayPurchaseProductCategoryTemp.length),this.arrayPurchaseProductConsumerTemp.splice(0,this.arrayPurchaseProductConsumerTemp.length),this.arrayPurchaseProductCommercialTemp.splice(0,this.arrayPurchaseProductCommercialTemp.length),e.forEach((function(e){t.arrayPurchaseProductCategory.push(e.yearMonth),t.arrayPurchaseProductConsumer.push(Math.round(e.consumer)),t.arrayPurchaseProductCommercial.push(Math.round(e.commercial)),t.arrayPurchaseProductCategoryTemp.push(e.yearMonth),t.arrayPurchaseProductConsumerTemp.push(Math.round(e.consumer)),t.arrayPurchaseProductCommercialTemp.push(Math.round(e.commercial))})),this.arrayPurchaseProductTotal=[])}}},methods:{viewDetailClick:function(){this.arrayPurchaseProductTotal.length>0?this.dialogTableVisible=!0:this.getDmsDealerPurchaseDetailInfo()},getDmsDealerPurchaseDetailInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.viewDetailLoading=!0,e.next=3,T["a"].getDmsDealerPurchaseDetailInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear});case 3:t=e.sent,r=Object(i["a"])(t,2),a=r[0],o=r[1],a&&this.formateDealerPurchaseDetailInfo(o.data),this.viewDetailLoading=!1,this.dialogTableVisible=!0;case 10:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),formateDealerPurchaseDetailInfo:function(e){this.arrayPurchaseProductTotal=e.sellInByCategoryList.sort((function(e,t){return e.month-t.month})),this.arrayPurchaseProductShow=this.arrayPurchaseProductTotal,this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.queryProductCategory=[],this.queryProductName="",this.formatPurchaseYearMonth(),this.formatFilterCriteria()},formatePurchaseFilterProductCategory:function(){this.optionProductCategory=[];var e=[];this.arrayPurchaseProductShow.forEach((function(t){null!==t.productCategory&&""!==t.productCategory&&e.push(t.productCategory)})),this.optionProductCategory=Object(d["a"])(new Set(e)),this.optionProductCategory=_(this.optionProductCategory)},formatePurchaseFilterProductName:function(){this.optionProductNames=[];var e=[];this.arrayPurchaseProductShow.forEach((function(t){null!==t.productNameCn&&""!==t.productNameCn&&e.push(t.productNameCn)})),this.optionProductNames=Object(d["a"])(new Set(e))},formatPurchaseYearMonth:function(){this.arrayPurchaseYearMonth=[];var e=[];this.arrayPurchaseProductTotal.forEach((function(t){null!==t.month&&""!==t.month&&e.push(t.month)})),this.arrayPurchaseYearMonth=Object(d["a"])(new Set(e))},purchaseFilterChange:function(){var e=this;if(this.arrayPurchaseProductShow=this.arrayPurchaseProductTotal,this.queryProductCategory.length>0){var t=[];this.queryProductCategory.forEach((function(r){var a=e.arrayPurchaseProductShow.filter((function(e){return e.productCategory===r}));t=t.concat(a)})),this.arrayPurchaseProductShow=t}if(this.queryProductName.length>0){var r=[];r=this.arrayPurchaseProductShow.filter((function(t){return t.productNameCn===e.queryProductName})),this.arrayPurchaseProductShow=r}},productCategoryChange:function(){this.purchaseFilterChange(),0===this.queryProductCategory.length&&this.formatePurchaseFilterProductCategory(),this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productNameChange:function(){this.purchaseFilterChange(),this.formatePurchaseFilterProductCategory(),0===this.queryProductName.length&&this.formatePurchaseFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},formatFilterCriteria:function(){this.arrayFilterCriteria=[],this.queryProductCategory.length>0&&this.arrayFilterCriteria.push("产品分类: "+this.queryProductCategory.join(", ")),this.queryProductName.length>0&&this.arrayFilterCriteria.push("产品名称: "+this.queryProductName)},formatChartBarInfo:function(){var e=this;if(0===this.queryProductCategory.length&&0===this.queryProductName.length){this.arrayPurchaseProductCategory.splice(0,this.arrayPurchaseProductCategory.length),this.arrayPurchaseProductConsumer.splice(0,this.arrayPurchaseProductConsumer.length),this.arrayPurchaseProductCommercial.splice(0,this.arrayPurchaseProductCommercial.length);for(var t=0;t<this.arrayPurchaseProductCategoryTemp.length;t++)this.arrayPurchaseProductCategory.push(this.arrayPurchaseProductCategoryTemp[t]),this.arrayPurchaseProductConsumer.push(Math.round(this.arrayPurchaseProductConsumerTemp[t])),this.arrayPurchaseProductCommercial.push(Math.round(this.arrayPurchaseProductCommercialTemp[t]))}else this.arrayPurchaseProductCategory.splice(0,this.arrayPurchaseProductCategory.length),this.arrayPurchaseProductConsumer.splice(0,this.arrayPurchaseProductConsumer.length),this.arrayPurchaseProductCommercial.splice(0,this.arrayPurchaseProductCommercial.length),this.arrayPurchaseYearMonth.forEach((function(t){var r=e.arrayPurchaseProductShow.filter((function(e){return e.month===t})),a=0,o=0;r.forEach((function(e){a+=e.sellInLYConsume,o+=e.sellInLYCommercial})),e.arrayPurchaseProductCategory.push(t.substring(0,4)+"-"+t.substring(4)),e.arrayPurchaseProductConsumer.push(Math.round(a)),e.arrayPurchaseProductCommercial.push(Math.round(o))}))},currentChange:function(e){this.currentPage=e},getSummaries:function(){for(var e=[],t=0;t<5;t++)e[t]=0===t?"合计":t<3?"":0;return this.arrayPurchaseProductShow.forEach((function(t){e[3]+=t.sellInLYConsume,e[4]+=t.sellInLYCommercial})),e[3]=this.$options.filters.toThousand(Math.round(e[3])),e[4]=this.$options.filters.toThousand(Math.round(e[4])),e}}},Q=V,$=(r("e08c"),Object(S["a"])(Q,z,W,!1,null,"64571e82",null)),R=$.exports,H=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"sales_year_content_top"},[r("div",{staticClass:"sales_year_content_top_title"},[e._v("\n            "+e._s(e.dealerYear)+" VS "+e._s(e.dealerYear-1)+" (单位: 升)\n            "),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1),r("el-button",{staticClass:"sales_year_content_top_detail",attrs:{loading:e.viewDetailLoading,size:"mini"},on:{click:e.viewDetailClick}},[e._v("\n            查看详情\n        ")])],1),e.arrayFilterCriteria.length>0?r("div",{staticClass:"sales_year_content_filter"},[r("div",{staticClass:"sales_year_content_filter_title"},[e._v("\n            筛选条件:\n        ")]),r("div",{staticClass:"sales_year_content_filter_content"},e._l(e.arrayFilterCriteria,(function(t,a){return r("div",{key:a,staticClass:"sales_year_content_filter_list"},[r("div",{staticClass:"sales_year_content_filter_list_item"},[r("span",[e._v(e._s(t))])])])})),0)]):e._e(),r("div",{staticStyle:{height:"250px",position:"relative"}},[r("chartBar",{attrs:{width:e.chartBarWidth,height:"250px",option:e.optionSalesYear}}),r("div",{staticStyle:{width:"100%",height:"20px",position:"absolute",top:"10px",display:"flex","justify-content":"center"}},[r("div",{staticStyle:{"align-self":"center",width:"6px",height:"6px",background:"#8AAAE1"}}),r("div",{staticStyle:{"align-self":"center","margin-left":"5px","font-size":"12px",color:"#282828"}},[e._v("\n                "+e._s(e.dealerYear)+"年销量\n            ")]),r("div",{staticStyle:{"align-self":"center","margin-left":"20px",width:"6px",height:"6px",background:"#FFAE7B"}}),r("div",{staticStyle:{"align-self":"center","margin-left":"5px","font-size":"12px",color:"#282828"}},[e._v("\n                "+e._s(e.dealerYear-1)+"年销量\n            ")])])],1),r("el-dialog",{attrs:{title:"销量 "+e.dealerYear+" VS "+(e.dealerYear-1)+" (单位: 升)",visible:e.dialogTableVisible,width:"80%"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"渠道:","label-width":"40px"}},[r("el-select",{staticStyle:{width:"130px"},attrs:{clearable:"",multiple:"",size:e.queryChannelName.length<2?"small":"",placeholder:"请选择"},on:{change:e.channelChange},model:{value:e.queryChannelName,callback:function(t){e.queryChannelName=t},expression:"queryChannelName"}},e._l(e.optionChannelNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"客户业务分类:","label-width":"90px"}},[r("el-select",{staticStyle:{width:"325px"},attrs:{clearable:"",multiple:"",size:e.queryStoreCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.storeCategoryChange},model:{value:e.queryStoreCategory,callback:function(t){e.queryStoreCategory=t},expression:"queryStoreCategory"}},e._l(e.optionStoreCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"DMS客户名称:","label-width":"90px"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.storeNameChange},model:{value:e.queryStoreName,callback:function(t){e.queryStoreName=t},expression:"queryStoreName"}},e._l(e.optionStoreNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品分类:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{clearable:"",multiple:"",size:e.queryProductCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.productCategoryChange},model:{value:e.queryProductCategory,callback:function(t){e.queryProductCategory=t},expression:"queryProductCategory"}},e._l(e.optionProductCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品名称:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arraySalesYearShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"","show-summary":"","summary-method":e.getSummaries,size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"month",label:"月份",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(t.row.month.substring(4,5).replace("0","")+t.row.month.substring(5)+"月")+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"productChannel",label:"渠道",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s("Commercial"===t.row.productChannel?"商用油":"Consumer"===t.row.productChannel?"乘用车":"Chevron Others"===t.row.productChannel?"雪佛龙其它":"其它")+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"categoryName",label:"客户业务分类",width:"155"}}),r("el-table-column",{attrs:{prop:"dmsWorkshopName",label:"DMS客户名称",width:"155"}}),r("el-table-column",{attrs:{prop:"productCategory",label:"产品分类",width:"100"}}),r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:e.dealerYear+"年销量",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLY)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLY1",label:e.dealerYear-1+"年销量",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLY1)))+"\n                ")]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arraySalesYearShow.length},on:{"current-change":e.currentChange}})],1)],1)},U=[];function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var K={components:{chartBar:C},props:{arraySalesYear:{type:Array}},data:function(){return{chartBarWidth:"100%",arraySalesYearCategory:[],arraySalesYearSellThrough:[],arraySalesYearSellThroughPrev:[],arraySalesYearCategoryTemp:[],arraySalesYearSellThroughTemp:[],arraySalesYearSellThroughPrevTemp:[],viewDetailLoading:!1,dialogTableVisible:!1,arraySalesYearTotal:[],arraySalesYearShow:[],currentPage:1,pageSize:10,arraySalesYearMonth:[],optionChannelNames:[],queryChannelName:[],optionStoreCategory:[],queryStoreCategory:[],optionStoreNames:[],queryStoreName:"",optionProductCategory:[],queryProductCategory:[],optionProductNames:[],queryProductName:"",arrayFilterCriteria:[]}},computed:J(J({},Object(v["b"])(["dealerInfo","dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+(new Date).getMonth()+"月";return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"年"+e+"的数"}return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"全年的数"},optionSalesYear:function(){var e=this.dealerYear+"年销量",t=this.dealerYear-1+"年销量",r=this;return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(a){return a[0].name+"<br/>"+a[0].marker+e+": "+r.$options.filters.toThousand(a[0].value)+"<br/>"+a[1].marker+t+": "+r.$options.filters.toThousand(a[1].value)}},grid:{top:40,left:70,right:30,bottom:40},xAxis:{type:"category",data:this.arraySalesYearCategory,axisLine:{lineStyle:{color:"#D9D9D9"}},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828",rotate:45}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828"},splitLine:{lineStyle:{color:"#ECECEC"}}},series:[{name:"当年销量",type:"bar",data:this.arraySalesYearSellThrough,itemStyle:{color:"#8AAAE1"},barWidth:20,z:5},{name:"去年销量",type:"bar",data:this.arraySalesYearSellThroughPrev,itemStyle:{color:"#FFAE7B"},barGap:"-75%",barWidth:10,z:10}]}}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.chartBarWidth=(t-92)/2+"px"}))},watch:{arraySalesYear:{deep:!0,handler:function(e){var t=this;e&&(this.arraySalesYearCategory.splice(0,this.arraySalesYearCategory.length),this.arraySalesYearSellThrough.splice(0,this.arraySalesYearSellThrough.length),this.arraySalesYearSellThroughPrev.splice(0,this.arraySalesYearSellThroughPrev.length),this.arraySalesYearCategoryTemp.splice(0,this.arraySalesYearCategoryTemp.length),this.arraySalesYearSellThroughTemp.splice(0,this.arraySalesYearSellThroughTemp.length),this.arraySalesYearSellThroughPrevTemp.splice(0,this.arraySalesYearSellThroughPrevTemp.length),e.forEach((function(e){t.arraySalesYearCategory.push(e.month),t.arraySalesYearSellThrough.push(Math.round(e.sellThrough)),t.arraySalesYearSellThroughPrev.push(Math.round(e.sellThroughPrev)),t.arraySalesYearCategoryTemp.push(e.month),t.arraySalesYearSellThroughTemp.push(Math.round(e.sellThrough)),t.arraySalesYearSellThroughPrevTemp.push(Math.round(e.sellThroughPrev))})),this.arraySalesYearTotal=[])}}},methods:{viewDetailClick:function(){this.arraySalesYearTotal.length>0?this.dialogTableVisible=!0:this.getDmsDealerSalesDetailInfo()},getDmsDealerSalesDetailInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o,n,l=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.viewDetailLoading=!0,t=function(){if(l.dealerYear==(new Date).getFullYear()){var e=(new Date).getMonth();return e<10?l.dealerYear+"0"+e||1:l.dealerYear+""+e}return l.dealerYear+"12"},e.next=4,T["a"].getDmsDealerSalesDetailInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear,endMonth:t()});case 4:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&this.formateDealerSalesDetailInfo(n.data),this.viewDetailLoading=!1,this.dialogTableVisible=!0;case 11:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),formateDealerSalesDetailInfo:function(e){this.arraySalesYearTotal=e.throughDecList.sort((function(e,t){return e.month-t.month})),this.arraySalesYearShow=this.arraySalesYearTotal,this.formateSalesFilterChannel(),this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.queryChannelName=[],this.queryStoreCategory=[],this.queryStoreName="",this.queryProductCategory=[],this.queryProductName="",this.formatSalesYearMonth(),this.formatFilterCriteria()},formateSalesFilterChannel:function(){var e=this;this.optionChannelNames=[];var t=[];this.arraySalesYearShow.forEach((function(e){null!==e.productChannel&&""!==e.productChannel&&t.push(e.productChannel)}));var r=Object(d["a"])(new Set(t));r.forEach((function(t){var r="其他";"Consumer"===t?r="乘用车":"Commercial"===t?r="商用油":"Chevron Others"===t&&(r="雪佛龙其它"),e.optionChannelNames.push({value:t,label:r})}))},formateSalesFilterStoreCategory:function(){this.optionStoreCategory=[];var e=[];this.arraySalesYearShow.forEach((function(t){null!==t.categoryName&&""!==t.categoryName&&e.push(t.categoryName)})),this.optionStoreCategory=Object(d["a"])(new Set(e))},formateSalesFilterStoreName:function(){this.optionStoreNames=[];var e=[];this.arraySalesYearShow.forEach((function(t){null!==t.dmsWorkshopName&&""!==t.dmsWorkshopName&&e.push(t.dmsWorkshopName)})),this.optionStoreNames=Object(d["a"])(new Set(e))},formateSalesFilterProductCategory:function(){this.optionProductCategory=[];var e=[];this.arraySalesYearShow.forEach((function(t){null!==t.productCategory&&""!==t.productCategory&&e.push(t.productCategory)})),this.optionProductCategory=Object(d["a"])(new Set(e)),this.optionProductCategory=_(this.optionProductCategory)},formateSalesFilterProductName:function(){this.optionProductNames=[];var e=[];this.arraySalesYearShow.forEach((function(t){null!==t.productNameCn&&""!==t.productNameCn&&e.push(t.productNameCn)})),this.optionProductNames=Object(d["a"])(new Set(e))},formatSalesYearMonth:function(){this.arraySalesYearMonth=[];var e=[];this.arraySalesYearTotal.forEach((function(t){null!==t.month&&""!==t.month&&e.push(t.month)})),this.arraySalesYearMonth=Object(d["a"])(new Set(e))},salesFilterChange:function(){var e=this;if(this.arraySalesYearShow=this.arraySalesYearTotal,this.queryChannelName.length>0){var t=[];this.queryChannelName.forEach((function(r){var a=e.arraySalesYearShow.filter((function(e){return e.productChannel===r}));t=t.concat(a)})),this.arraySalesYearShow=t}if(this.queryStoreCategory.length>0){var r=[];this.queryStoreCategory.forEach((function(t){var a=e.arraySalesYearShow.filter((function(e){return e.categoryName===t}));r=r.concat(a)})),this.arraySalesYearShow=r}if(this.queryStoreName.length>0){var a=[];a=this.arraySalesYearShow.filter((function(t){return t.dmsWorkshopName===e.queryStoreName})),this.arraySalesYearShow=a}if(this.queryProductCategory.length>0){var o=[];this.queryProductCategory.forEach((function(t){var r=e.arraySalesYearShow.filter((function(e){return e.productCategory===t}));o=o.concat(r)})),this.arraySalesYearShow=o}if(this.queryProductName.length>0){var n=[];n=this.arraySalesYearShow.filter((function(t){return t.productNameCn===e.queryProductName})),this.arraySalesYearShow=n}},channelChange:function(){this.salesFilterChange(),0===this.queryChannelName.length&&this.formateSalesFilterChannel(),this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},storeCategoryChange:function(){this.salesFilterChange(),this.formateSalesFilterChannel(),0===this.queryStoreCategory.length&&this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},storeNameChange:function(){this.salesFilterChange(),this.formateSalesFilterChannel(),this.formateSalesFilterStoreCategory(),0===this.queryStoreName.length&&this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productCategoryChange:function(){this.salesFilterChange(),this.formateSalesFilterChannel(),this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),0===this.queryProductCategory.length&&this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productNameChange:function(){this.salesFilterChange(),this.formateSalesFilterChannel(),this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),0===this.queryProductName.length&&this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},formatFilterCriteria:function(){if(this.arrayFilterCriteria=[],this.queryChannelName.length>0){var e=[];this.queryChannelName.forEach((function(t){var r="其他";"Consumer"===t?r="乘用车":"Commercial"===t?r="商用油":"Chevron Others"===t&&(r="雪佛龙其它"),e.push(r)})),this.arrayFilterCriteria.push("渠道: "+e.join(", "))}this.queryStoreCategory.length>0&&this.arrayFilterCriteria.push("客户业务分类: "+this.queryStoreCategory.join(", ")),this.queryStoreName.length>0&&this.arrayFilterCriteria.push("DMS客户名称: "+this.queryStoreName),this.queryProductCategory.length>0&&this.arrayFilterCriteria.push("产品分类: "+this.queryProductCategory.join(", ")),this.queryProductName.length>0&&this.arrayFilterCriteria.push("产品名称: "+this.queryProductName)},formatChartBarInfo:function(){var e=this;if(0===this.queryChannelName.length&&0===this.queryStoreCategory.length&&0===this.queryStoreName.length&&0===this.queryProductCategory.length&&0===this.queryProductName.length){this.arraySalesYearCategory.splice(0,this.arraySalesYearCategory.length),this.arraySalesYearSellThrough.splice(0,this.arraySalesYearSellThrough.length),this.arraySalesYearSellThroughPrev.splice(0,this.arraySalesYearSellThroughPrev.length);for(var t=0;t<this.arraySalesYearCategoryTemp.length;t++)this.arraySalesYearCategory.push(this.arraySalesYearCategoryTemp[t]),this.arraySalesYearSellThrough.push(Math.round(this.arraySalesYearSellThroughTemp[t])),this.arraySalesYearSellThroughPrev.push(Math.round(this.arraySalesYearSellThroughPrevTemp[t]))}else this.arraySalesYearCategory.splice(0,this.arraySalesYearCategory.length),this.arraySalesYearSellThrough.splice(0,this.arraySalesYearSellThrough.length),this.arraySalesYearSellThroughPrev.splice(0,this.arraySalesYearSellThroughPrev.length),this.arraySalesYearMonth.forEach((function(t){var r=e.arraySalesYearShow.filter((function(e){return e.month===t})),a=0,o=0;r.forEach((function(e){a+=e.sellThroughLY,o+=e.sellThroughLY1})),e.arraySalesYearCategory.push(t.substring(4,5).replace("0","")+t.substring(5)+"月"),e.arraySalesYearSellThrough.push(Math.round(a)),e.arraySalesYearSellThroughPrev.push(Math.round(o))}))},currentChange:function(e){this.currentPage=e},getSummaries:function(){for(var e=[],t=0;t<8;t++)e[t]=0===t?"合计":t<6?"":0;return this.arraySalesYearShow.forEach((function(t){e[6]+=t.sellThroughLY,e[7]+=t.sellThroughLY1})),e[6]=this.$options.filters.toThousand(Math.round(e[6])),e[7]=this.$options.filters.toThousand(Math.round(e[7])),e}}},X=K,Z=(r("5ac8"),Object(S["a"])(X,H,U,!1,null,"505e990a",null)),ee=Z.exports,te=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"sales_product_content_top"},[r("div",{staticClass:"sales_product_content_top_title"},[e._v("\n            "+e._s(e.dealerYear)+" 乘用车 VS 商用油 VS 雪佛龙其它 (单位: 升)\n            "),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1),r("el-button",{staticClass:"sales_product_content_top_detail",attrs:{loading:e.viewDetailLoading,size:"mini"},on:{click:e.viewDetailClick}},[e._v("\n            查看详情\n        ")])],1),e.arrayFilterCriteria.length>0?r("div",{staticClass:"sales_product_content_filter"},[r("div",{staticClass:"sales_product_content_filter_title"},[e._v("\n            筛选条件:\n        ")]),r("div",{staticClass:"sales_product_content_filter_content"},e._l(e.arrayFilterCriteria,(function(t,a){return r("div",{key:a,staticClass:"sales_product_content_filter_list"},[r("div",{staticClass:"sales_product_content_filter_list_item"},[r("span",[e._v(e._s(t))])])])})),0)]):e._e(),r("chartBar",{attrs:{width:e.chartBarWidth,height:"250px",option:e.optionSalesProduct}}),r("el-dialog",{attrs:{title:"销量 "+e.dealerYear+" 乘用车 VS 商用油 VS 雪佛龙其它 (单位: 升)",visible:e.dialogTableVisible,width:"80%"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"客户业务分类:","label-width":"90px"}},[r("el-select",{staticStyle:{width:"325px"},attrs:{clearable:"",multiple:"",size:e.queryStoreCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.storeCategoryChange},model:{value:e.queryStoreCategory,callback:function(t){e.queryStoreCategory=t},expression:"queryStoreCategory"}},e._l(e.optionStoreCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"DMS客户名称:","label-width":"90px"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.storeNameChange},model:{value:e.queryStoreName,callback:function(t){e.queryStoreName=t},expression:"queryStoreName"}},e._l(e.optionStoreNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品分类:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{clearable:"",multiple:"",size:e.queryProductCategory.length<2?"small":"",placeholder:"请选择"},on:{change:e.productCategoryChange},model:{value:e.queryProductCategory,callback:function(t){e.queryProductCategory=t},expression:"queryProductCategory"}},e._l(e.optionProductCategory,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"产品名称:","label-width":"65px"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",size:"small",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arraySalesProductShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"","show-summary":"","summary-method":e.getSummaries,size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"month",label:"月份",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(t.row.month.substring(0,4)+"-"+t.row.month.substring(4))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"categoryName",label:"客户业务分类",width:"155"}}),r("el-table-column",{attrs:{prop:"dmsWorkshopName",label:"DMS客户名称",width:"155"}}),r("el-table-column",{attrs:{prop:"productCategory",label:"产品分类",width:"100"}}),r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLYConsume",label:"乘用车",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLYConsume)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLYCommercial",label:"商用油",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLYCommercial)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLYChevronOthers",label:"雪佛龙其它",width:"80",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLYChevronOthers)))+"\n                ")]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arraySalesProductShow.length},on:{"current-change":e.currentChange}})],1)],1)},re=[];function ae(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function oe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ae(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var ne={components:{chartBar:C},props:{arraySalesProduct:{type:Array}},data:function(){return{chartBarWidth:"100%",arraySalesProductCategory:[],arraySalesProductConsumer:[],arraySalesProductCommercial:[],arraySalesProductChevronOther:[],arraySalesProductCategoryTemp:[],arraySalesProductConsumerTemp:[],arraySalesProductCommercialTemp:[],arraySalesProductChevronOtherTemp:[],viewDetailLoading:!1,dialogTableVisible:!1,arraySalesProductTotal:[],arraySalesProductShow:[],currentPage:1,pageSize:10,arraySalesYearMonth:[],optionStoreCategory:[],queryStoreCategory:[],optionStoreNames:[],queryStoreName:"",optionProductCategory:[],queryProductCategory:[],optionProductNames:[],queryProductName:"",arrayFilterCriteria:[]}},computed:oe(oe({},Object(v["b"])(["dealerInfo","dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"},optionSalesProduct:function(){var e=this;return{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){return t[0].name+"<br/>"+t[0].marker+t[0].seriesName+": "+e.$options.filters.toThousand(t[0].value)+"<br/>"+t[1].marker+t[1].seriesName+": "+e.$options.filters.toThousand(t[1].value)+"<br/>"+t[2].marker+t[2].seriesName+": "+e.$options.filters.toThousand(t[2].value)}},legend:{data:["乘用车","商用油","雪佛龙其它"],top:10,itemWidth:6,itemHeight:6,textStyle:{color:"#282828",fontSize:12}},grid:{top:40,left:70,right:30,bottom:50},xAxis:{type:"category",data:this.arraySalesProductCategory,axisLine:{lineStyle:{color:"#D9D9D9"}},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828",rotate:45}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{fontSize:12,color:"#282828"},splitLine:{lineStyle:{color:"#ECECEC"}}},series:[{name:"乘用车",type:"bar",data:this.arraySalesProductConsumer,itemStyle:{color:"#FD7702"},barGap:"10%",barWidth:10},{name:"商用油",type:"bar",data:this.arraySalesProductCommercial,itemStyle:{color:"#3870CA"},barGap:"10%",barWidth:10},{name:"雪佛龙其它",type:"bar",data:this.arraySalesProductChevronOther,itemStyle:{color:"#769230"},barGap:"10%",barWidth:10}]}}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.chartBarWidth=(t-92)/2+"px"}))},watch:{arraySalesProduct:{deep:!0,handler:function(e){var t=this;e&&(this.arraySalesProductCategory.splice(0,this.arraySalesProductCategory.length),this.arraySalesProductConsumer.splice(0,this.arraySalesProductConsumer.length),this.arraySalesProductCommercial.splice(0,this.arraySalesProductCommercial.length),this.arraySalesProductChevronOther.splice(0,this.arraySalesProductChevronOther.length),this.arraySalesProductCategoryTemp.splice(0,this.arraySalesProductCategoryTemp.length),this.arraySalesProductConsumerTemp.splice(0,this.arraySalesProductConsumerTemp.length),this.arraySalesProductCommercialTemp.splice(0,this.arraySalesProductCommercialTemp.length),this.arraySalesProductChevronOtherTemp.splice(0,this.arraySalesProductChevronOtherTemp.length),e.forEach((function(e){t.arraySalesProductCategory.push(e.yearMonth),t.arraySalesProductConsumer.push(Math.round(e.consumer)),t.arraySalesProductCommercial.push(Math.round(e.commercial)),t.arraySalesProductChevronOther.push(Math.round(e.chevronOther)),t.arraySalesProductCategoryTemp.push(e.yearMonth),t.arraySalesProductConsumerTemp.push(Math.round(e.consumer)),t.arraySalesProductCommercialTemp.push(Math.round(e.commercial)),t.arraySalesProductChevronOtherTemp.push(Math.round(e.chevronOther))})),this.arraySalesProductTotal=[])}}},methods:{viewDetailClick:function(){this.arraySalesProductTotal.length>0?this.dialogTableVisible=!0:this.getDmsDealerSalesDetailInfo()},getDmsDealerSalesDetailInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.viewDetailLoading=!0,e.next=3,T["a"].getDmsDealerSalesDetailInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear});case 3:t=e.sent,r=Object(i["a"])(t,2),a=r[0],o=r[1],a&&this.formateDealerDetailInfo(o.data),this.viewDetailLoading=!1,this.dialogTableVisible=!0;case 10:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),formateDealerDetailInfo:function(e){this.arraySalesProductTotal=e.throughByCategoryDecList.sort((function(e,t){return e.month-t.month})),this.arraySalesProductShow=this.arraySalesProductTotal,this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.queryStoreCategory=[],this.queryStoreName="",this.queryProductCategory=[],this.queryProductName="",this.formatSalesYearMonth(),this.formatFilterCriteria()},formateSalesFilterStoreCategory:function(){this.optionStoreCategory=[];var e=[];this.arraySalesProductShow.forEach((function(t){null!==t.categoryName&&""!==t.categoryName&&e.push(t.categoryName)})),this.optionStoreCategory=Object(d["a"])(new Set(e))},formateSalesFilterStoreName:function(){this.optionStoreNames=[];var e=[];this.arraySalesProductShow.forEach((function(t){null!==t.dmsWorkshopName&&""!==t.dmsWorkshopName&&e.push(t.dmsWorkshopName)})),this.optionStoreNames=Object(d["a"])(new Set(e))},formateSalesFilterProductCategory:function(){this.optionProductCategory=[];var e=[];this.arraySalesProductShow.forEach((function(t){null!==t.productCategory&&""!==t.productCategory&&e.push(t.productCategory)})),this.optionProductCategory=Object(d["a"])(new Set(e)),this.optionProductCategory=_(this.optionProductCategory)},formateSalesFilterProductName:function(){this.optionProductNames=[];var e=[];this.arraySalesProductShow.forEach((function(t){null!==t.productNameCn&&""!==t.productNameCn&&e.push(t.productNameCn)})),this.optionProductNames=Object(d["a"])(new Set(e))},formatSalesYearMonth:function(){this.arraySalesYearMonth=[];var e=[];this.arraySalesProductTotal.forEach((function(t){null!==t.month&&""!==t.month&&e.push(t.month)})),this.arraySalesYearMonth=Object(d["a"])(new Set(e))},salesFilterChange:function(){var e=this;if(this.arraySalesProductShow=this.arraySalesProductTotal,this.queryStoreCategory.length>0){var t=[];this.queryStoreCategory.forEach((function(r){var a=e.arraySalesProductShow.filter((function(e){return e.categoryName===r}));t=t.concat(a)})),this.arraySalesProductShow=t}if(this.queryStoreName.length>0){var r=[];r=this.arraySalesProductShow.filter((function(t){return t.dmsWorkshopName===e.queryStoreName})),this.arraySalesProductShow=r}if(this.queryProductCategory.length>0){var a=[];this.queryProductCategory.forEach((function(t){var r=e.arraySalesProductShow.filter((function(e){return e.productCategory===t}));a=a.concat(r)})),this.arraySalesProductShow=a}if(this.queryProductName.length>0){var o=[];o=this.arraySalesProductShow.filter((function(t){return t.productNameCn===e.queryProductName})),this.arraySalesProductShow=o}},storeCategoryChange:function(){this.salesFilterChange(),0===this.queryStoreCategory.length&&this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},storeNameChange:function(){this.salesFilterChange(),this.formateSalesFilterStoreCategory(),0===this.queryStoreName.length&&this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productCategoryChange:function(){this.salesFilterChange(),this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),0===this.queryProductCategory.length&&this.formateSalesFilterProductCategory(),this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},productNameChange:function(){this.salesFilterChange(),this.formateSalesFilterStoreCategory(),this.formateSalesFilterStoreName(),this.formateSalesFilterProductCategory(),0===this.queryProductName.length&&this.formateSalesFilterProductName(),this.currentPage=1,this.formatFilterCriteria(),this.formatChartBarInfo()},formatFilterCriteria:function(){this.arrayFilterCriteria=[],this.queryStoreCategory.length>0&&this.arrayFilterCriteria.push("客户业务分类: "+this.queryStoreCategory.join(", ")),this.queryStoreName.length>0&&this.arrayFilterCriteria.push("DMS客户名称: "+this.queryStoreName),this.queryProductCategory.length>0&&this.arrayFilterCriteria.push("产品分类: "+this.queryProductCategory.join(", ")),this.queryProductName.length>0&&this.arrayFilterCriteria.push("产品名称: "+this.queryProductName)},formatChartBarInfo:function(){var e=this;if(0===this.queryStoreCategory.length&&0===this.queryStoreName.length&&0===this.queryProductCategory.length&&0===this.queryProductName.length){this.arraySalesProductCategory.splice(0,this.arraySalesProductCategory.length),this.arraySalesProductConsumer.splice(0,this.arraySalesProductConsumer.length),this.arraySalesProductCommercial.splice(0,this.arraySalesProductCommercial.length),this.arraySalesProductChevronOther.splice(0,this.arraySalesProductChevronOther.length);for(var t=0;t<this.arraySalesProductCategoryTemp.length;t++)this.arraySalesProductCategory.push(this.arraySalesProductCategoryTemp[t]),this.arraySalesProductConsumer.push(Math.round(this.arraySalesProductConsumerTemp[t])),this.arraySalesProductCommercial.push(Math.round(this.arraySalesProductCommercialTemp[t])),this.arraySalesProductChevronOther.push(Math.round(this.arraySalesProductChevronOtherTemp[t]))}else this.arraySalesProductCategory.splice(0,this.arraySalesProductCategory.length),this.arraySalesProductConsumer.splice(0,this.arraySalesProductConsumer.length),this.arraySalesProductCommercial.splice(0,this.arraySalesProductCommercial.length),this.arraySalesProductChevronOther.splice(0,this.arraySalesProductChevronOther.length),this.arraySalesYearMonth.forEach((function(t){var r=e.arraySalesProductShow.filter((function(e){return e.month===t})),a=0,o=0,n=0;r.forEach((function(e){a+=e.sellThroughLYConsume,o+=e.sellThroughLYCommercial,n+=e.sellThroughLYChevronOthers})),e.arraySalesProductCategory.push(t.substring(0,4)+"-"+t.substring(4)),e.arraySalesProductConsumer.push(Math.round(a)),e.arraySalesProductCommercial.push(Math.round(o)),e.arraySalesProductChevronOther.push(Math.round(n))}))},currentChange:function(e){this.currentPage=e},getSummaries:function(){for(var e=[],t=0;t<8;t++)e[t]=0===t?"合计":t<5?"":0;return this.arraySalesProductShow.forEach((function(t){e[5]+=t.sellThroughLYConsume,e[6]+=t.sellThroughLYCommercial,e[7]+=t.sellThroughLYChevronOthers})),e[5]=this.$options.filters.toThousand(Math.round(e[5])),e[6]=this.$options.filters.toThousand(Math.round(e[6])),e[7]=this.$options.filters.toThousand(Math.round(e[7])),e}}},le=ne,ie=(r("edde"),Object(S["a"])(le,te,re,!1,null,"628d06fd",null)),se=ie.exports;function ce(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function ue(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ce(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ce(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var he={components:{purchaseSales:D,purchaseYear:M,purchaseProduct:R,salesYear:ee,salesProduct:se},computed:ue({},Object(v["b"])(["dealerInfo","dealerYear"])),data:function(){return{loading:!1,arrayPurchaseSales:[],arrayPurchaseYear:[],arrayPurchaseProduct:[],arraySalesYear:[],arraySalesProduct:[],arraySalesFilterStore:[],arraySalesFilterProduct:[]}},mounted:function(){this.getDmsDealerInfo()},watch:{dealerInfo:function(e){e&&this.getDmsDealerInfo()},dealerYear:function(e){e&&this.dealerInfo&&this.getDmsDealerInfo()}},methods:{getDmsDealerInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loading=!0,e.next=3,T["a"].getDmsDealerInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear});case 3:t=e.sent,r=Object(i["a"])(t,2),a=r[0],o=r[1],a&&this.formateDealerInfo(o.data),this.loading=!1;case 9:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),formateDealerInfo:function(e){var t=e.inAndThroughAndQuantityList.sort((function(e,t){return e.month-t.month})),r=[],a=[],o=[];t.forEach((function(e){var t=e.month.substring(0,4)+"-"+e.month.substring(4),n=e.month.substring(4,5).replace("0","")+e.month.substring(5)+"月";r.push({yearMonth:t,sellIn:Math.round(e.sellInLY),sellThrough:Math.round(e.sellThroughLY),stockQuantity:Math.round(e.stockQuantityY)}),a.push({month:n,sellIn:Math.round(e.sellInLY),sellInPrev:Math.round(e.sellinLY1)}),o.push({month:n,sellThrough:Math.round(e.sellThroughLY),sellThroughPrev:Math.round(e.sellThroughLY1)})})),this.arrayPurchaseSales=r,this.arrayPurchaseYear=a.filter((function(e){return e.month!=(new Date).getMonth()+1+"月"})),this.arraySalesYear=o.filter((function(e){return e.month!=(new Date).getMonth()+1+"月"}));var n=e.sellInByCategoryList.sort((function(e,t){return e.month-t.month})),l=[],i=[];n.forEach((function(e){var t=e.month.substring(0,4)+"-"+e.month.substring(4);l.push({yearMonth:t,consumer:Math.round(e.sellInLYConsume),commercial:Math.round(e.sellInLYCommercial),chevronOther:Math.round(e.sellInLYChevronOthers)}),i.push({yearMonth:t,consumer:Math.round(e.sellThroughLYConsume),commercial:Math.round(e.sellThroughLYCommercial),chevronOther:Math.round(e.sellThroughLYChevronOthers)})})),this.arrayPurchaseProduct=l,this.arraySalesProduct=i}}},de=he,pe=(r("6a2c"),Object(S["a"])(de,n,l,!1,null,"4a04a029",null)),ye=pe.exports,ge=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"store_container"},[r("div",{staticClass:"store_content_screen"},[r("storeScreen",{attrs:{dicStoreScreenInfo:e.dicStoreScreenInfo},on:{storeScreenQuery:e.storeScreenQuery}})],1),r("div",{staticClass:"store_content_category"},[r("div",{staticClass:"store_content_category_title"},[e._v("\n            1. 客户业务分类\n        ")]),r("div",{staticClass:"store_content_category_table"},[r("div",{staticClass:"store_content_category_table_through"},[r("sellThrough",{attrs:{arraySellThrough:e.arraySellThrough}})],1),r("div",{staticClass:"store_content_category_table_compare"},[r("sellCompare",{attrs:{arraySellCompare:e.arraySellCompare}})],1)])]),r("div",{staticClass:"store_content_total"},[r("div",{staticClass:"store_content_total_title"},[e._v("\n            2. 最终客户销售数据\n        ")]),r("div",{staticClass:"store_content_total_table"},[r("div",{staticClass:"store_content_total_table_Top"},[r("totalTopTen",{attrs:{arrayTotalTopTen:e.arrayTotalTopTen}})],1),r("div",{staticClass:"store_content_total_table_compare"},[r("totalCompare",{attrs:{arrayTotalCompare:e.arrayTotalCompare}})],1)])])])},fe=[],me=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{"padding-top":"10px"}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"渠道:","label-width":"40px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{multiple:"",clearable:"",size:e.querySalesChannels.length<2?"small":"",placeholder:"请选择"},on:{change:e.salesChannelChange},model:{value:e.querySalesChannels,callback:function(t){e.querySalesChannels=t},expression:"querySalesChannels"}},e._l(e.optionSalesChannels,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"产品分类:","label-width":"70px"}},[r("el-select",{staticStyle:{width:"290px"},attrs:{multiple:"",clearable:"",size:e.queryProductTypes.length<3?"small":"",placeholder:"请选择"},model:{value:e.queryProductTypes,callback:function(t){e.queryProductTypes=t},expression:"queryProductTypes"}},e._l(e.optionProductTypes,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-tooltip",{attrs:{effect:"light",placement:"bottom"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("el-table",{attrs:{data:e.tableData,border:"",stripe:"",size:"mini","span-method":e.arraySpanMethod,"header-cell-style":{background:"#F5F7FA",height:"35px",fontWeight:700},"cell-style":{height:"25px"}}},[r("el-table-column",{attrs:{prop:"channel",label:"渠道",align:"center",width:"50"}}),r("el-table-column",{attrs:{prop:"categoryT2",label:"一级分类",align:"center",width:"60"}}),r("el-table-column",{attrs:{prop:"categoryT3",label:"二级分类",align:"left",width:"400"}}),r("el-table-column",{attrs:{prop:"product",label:"二级分类名称",align:"center",width:"95"}})],1)],1),r("i",{staticClass:"el-icon-info",staticStyle:{"margin-top":"10px","margin-left":"-6px"}})]),r("el-form-item",{attrs:{label:" ","label-width":"20px"}},[r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.queryStoreInfoClick}},[e._v("查询")])],1)],1)],1)},be=[],Se={props:{dicStoreScreenInfo:{type:Object}},data:function(){return{optionSalesChannels:[],querySalesChannels:[],optionProductTypes:[],queryProductTypes:[],dicProductTypes:{},tableData:[{channel:"商用油",categoryT2:"柴机油",categoryT3:"C-E CI-4，D-E CI-4，D-E CJ-4，D-E CK-4，D-E NG - 400，D-E Others",product:"德乐高端"},{channel:"商用油",categoryT2:"柴机油",categoryT3:"D-E CH-4，D-E NG - Gold",product:"德乐中端"},{channel:"商用油",categoryT2:"柴机油",categoryT3:"C-E CH-4，D-E CF-4",product:"德乐低端"},{channel:"商用油",categoryT2:"柴机油",categoryT3:"Acton Power EO，D-E CD，D-E CI-4 Plus，Gas Engine Oil，OEM-Others，Others，Others，Private - LiFu",product:"德乐其它"},{channel:"商用油",categoryT2:"液压油",categoryT3:"Mid Tier",product:"液压油 Mid Tier"},{channel:"商用油",categoryT2:"液压油",categoryT3:"Low Tier",product:"液压油 Low Tier"},{channel:"商用油",categoryT2:"液压油",categoryT3:"High Tier，Ashless，OEM-Borche，OEM-HLT，OEM-JIDD，OEM-Jinke，OEM-Keda，OEM-Mingsheng，OEM-Xingdi，Premium Tier，Private - Enric，Private - LiFu，Private - LiFuLong，Wind Power",product:"液压油其它"},{channel:"商用油",categoryT2:"其它",categoryT3:"属于商用油的其它分类都叫“商用油其它”",product:"其它"},{channel:"乘用车",categoryT2:"汽机油",categoryT3:"H-E SN & Above，H-E SN & Above FS - Premium，H-E SN & Above FS - Value，H-E SN & Above SB",product:"金富力高端"},{channel:"乘用车",categoryT2:"汽机油",categoryT3:"H-E SG，H-E SJ，H-E SL，H-E SM",product:"金富力低端"},{channel:"乘用车",categoryT2:"汽机油",categoryT3:"OEM-BAIC，OEM-Others",product:"金富力其它"},{channel:"乘用车",categoryT2:"防冻液",categoryT3:"D-C ELC 55/45，H-C XLC 50/50，H-C XLC 55/45，H-C XLC Conc",product:"防冻液"},{channel:"乘用车",categoryT2:"添加剂",categoryT3:"TCP-100ml，TCP-295ml，TCP-355ml，TCP-470ml",product:"TCP添加剂"},{channel:"乘用车",categoryT2:"其它",categoryT3:"属于乘用车的其它分类都叫“乘用车其它”",product:"其它"}]}},watch:{dicStoreScreenInfo:{deep:!0,handler:function(e){var t=this;if(e){this.optionSalesChannels.splice(0,this.optionSalesChannels.length);var r=e.productChannels.filter((function(e){return null!==e&&""!==e}));r.forEach((function(e){var r="其他";"Commercial"===e?r="商用油":"Chevron Others"===e?r="雪佛龙其它":"Consumer"===e&&(r="乘用车"),t.optionSalesChannels.push({value:e,label:r})})),this.querySalesChannels=[],this.dicProductTypes=e.productCategory,this.formateProductTypes()}}}},methods:{formateProductTypes:function(){var e=this,t=[];this.querySalesChannels.length>0?this.querySalesChannels.forEach((function(r){t=t.concat(e.dicProductTypes[r])})):this.optionSalesChannels.forEach((function(r){t=t.concat(e.dicProductTypes[r.value])})),this.optionProductTypes.splice(0,this.optionProductTypes.length),t.forEach((function(t){for(var r in t)e.optionProductTypes.push({value:r,label:t[r]})})),this.queryProductTypes=[]},salesChannelChange:function(){this.formateProductTypes()},queryStoreInfoClick:function(){var e={};this.querySalesChannels.length>0&&(e={productChannel:this.querySalesChannels.join(",")});var t={};this.queryProductTypes.length>0&&(t={productCategory:this.queryProductTypes.join(",")}),this.$emit("storeScreenQuery",Object.assign({},e,t))},arraySpanMethod:function(e){var t=e.rowIndex,r=e.columnIndex;return 0===r?0===t?[8,1]:8===t?[6,1]:[0,0]:1===r?0===t?[4,1]:4===t||8===t?[3,1]:11===t||12===t?[1,1]:[0,0]:2===r?7===t||13===t?[1,3]:[1,1]:7===t||13===t?[0,0]:[1,1]}}},Pe=Se,Ce=(r("5a39"),Object(S["a"])(Pe,me,be,!1,null,null,null)),ve=Ce.exports,Te=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"sell_through_content_top"},[r("div",{staticClass:"sell_through_content_top_title"},[e._v("\n            "+e._s(e.dealerYear)+" 销售 (单位: 升)\n            "),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1)]),r("div",{staticClass:"sell_through_content_table"},[r("el-table",{style:"width: "+e.tableWidth+";",attrs:{data:e.arraySellThroughTotal,border:"",stripe:"","show-summary":"","summary-method":e.getSummaries,size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"categoryName",label:"客户业务分类"}}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:"销售",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLY)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLScale",label:"%",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("progressBar",{attrs:{dicProgress:{width:"100px",height:"18px",colorLeft:"#92D050",colorRight:"#E1EFD9",percentView:String(e.row.sellThroughLScale<=0?0:e.row.sellThroughLScale/e.row.maxSellThroughScale*100)+"%",percentLable:String(e.row.sellThroughLScale)+"%"}}})]}}])}),r("el-table-column",{attrs:{prop:"workshopQty",label:"客户数量",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.workshopQty)))+"\n                ")]}}])})],1)],1)])},_e=[],we=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{style:"width: "+e.viewWidth+"; height: "+e.viewHeight+"; position: relative;"},[r("div",{style:"width: "+e.progressPercentView+"; height: 100%; background-image: linear-gradient(to right, "+e.colorLeft+", "+e.colorRight+");"}),r("span",{style:"font-size: 10px; line-height: "+e.viewHeight+"; position: absolute; right: 0px; top: 0px;"},[e._v("\n        "+e._s(e.progressPercentLable)+"\n    ")])])},Ye=[],Oe={name:"componentProgressBar",props:{dicProgress:{type:Object}},data:function(){return{viewWidth:"100px",viewHeight:"12px",colorLeft:"#588ECA",colorRight:"#D5E2F2",progressPercentView:"0%",progressPercentLable:"0%"}},watch:{dicProgress:{deep:!0,immediate:!0,handler:function(e){e&&(this.viewWidth=e.width||"100px",this.viewHeight=e.height||"12px",this.colorLeft=e.colorLeft||"#588ECA",this.colorRight=e.colorRight||"#D5E2F2",this.progressPercentView=e.percentView||"0%",this.progressPercentLable=e.percentLable||"0%")}}}},xe=Oe,Ne=Object(S["a"])(xe,we,Ye,!1,null,"0a7a5b2d",null),De=Ne.exports;function ke(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Fe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ke(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ke(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var je={components:{progressBar:De},props:{arraySellThrough:{type:Array}},data:function(){return{tableWidth:"100%",dialogTableVisible:!1,arraySellThroughTotal:[],optionCategoryName:[],queryCategoryName:""}},computed:Fe(Fe({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.tableWidth=(t-122)/2+"px"}))},watch:{arraySellThrough:{deep:!0,handler:function(e){var t=this;if(e){var r=[];e.forEach((function(e){r.push((100*e.sellThroughLScale).toFixed(2))}));var a=Math.max.apply(null,r);this.arraySellThroughTotal.splice(0,this.arraySellThroughTotal.length),this.optionCategoryName.splice(0,this.optionCategoryName.length),e.forEach((function(e){t.arraySellThroughTotal.push({categoryName:e.categoryName,sellThroughLY:e.sellThroughLY,sellThroughLScale:(100*e.sellThroughLScale).toFixed(2),workshopQty:e.workshopQty,maxSellThroughScale:a}),null!==e.categoryName&&""!==e.categoryName&&t.optionCategoryName.push(e.categoryName)})),this.queryCategoryName=""}}}},methods:{getSummaries:function(){for(var e=[],t=0;t<4;t++)e[t]=0===t?"合计":2===t?"100%":0;return this.arraySellThroughTotal.forEach((function(t){e[1]+=t.sellThroughLY,e[3]+=t.workshopQty})),e[1]=this.$options.filters.toThousand(Math.round(e[1])),e[3]=this.$options.filters.toThousand(Math.round(e[3])),e}}},qe=je,Ie=(r("ad54"),Object(S["a"])(qe,Te,_e,!1,null,"e901f85a",null)),Le=Ie.exports,Ee=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"sell_compare_content_top"},[r("div",{staticClass:"sell_compare_content_top_title"},[r("span",[e._v("\n                "+e._s(e.dealerYear)+" VS "+e._s(e.dealerYear-1)+" (单位: 升)\n            ")]),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1)]),r("div",{staticClass:"sell_compare_content_table"},[r("el-table",{style:"width: "+e.tableWidth+";",attrs:{data:e.arraySellCompareTotal,border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":e.tableLastCellStyle}},[r("el-table-column",{attrs:{prop:"categoryName",label:"客户业务分类"}}),r("el-table-column",{attrs:{prop:"sellThroughLY1",label:""+(e.dealerYear-1),align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLY1)))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:""+e.dealerYear,align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(Math.round(t.row.sellThroughLY)))+"\n                ")]}}])}),r("el-table-column",{attrs:{label:e.dealerYear+" VS "+(e.dealerYear-1),align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[r("i",{class:e.resutlIcon(t.row.sellThroughLY,t.row.sellThroughLY1),style:t.row.sellThroughLY>=t.row.sellThroughLY1?"align-self: center; margin-left: 15px; font-size: 12px; font-weight: bold; color: #53A68F":"align-self: center; margin-left: 15px; font-size: 12px; font-weight: bold; color: #E74A20"}),r("span",{staticStyle:{"align-self":"center"}},[e._v("\n                            "+e._s(e._f("toThousand")(Math.abs(Math.round(t.row.sellThroughLY-t.row.sellThroughLY1))))+"\n                        ")])])]}}])})],1)],1)])},Me=[];function ze(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function We(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Be={props:{arraySellCompare:{type:Array}},data:function(){return{tableWidth:"100%",dialogTableVisible:!1,arraySellCompareTotal:[],optionCategoryName:[],queryCategoryName:""}},computed:We(We({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+(new Date).getMonth()+"月";return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"年"+e+"的数"}return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"全年的数"}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.tableWidth=(t-122)/2+"px"}))},watch:{arraySellCompare:{deep:!0,handler:function(e){var t=this;if(e){this.arraySellCompareTotal.splice(0,this.arraySellCompareTotal.length),this.optionCategoryName.splice(0,this.optionCategoryName.length);var r=0,a=0;e.forEach((function(e){t.arraySellCompareTotal.push({categoryName:e.categoryName,sellThroughLY:e.sellThroughLY,sellThroughLY1:e.sellThroughLY1}),null!==e.categoryName&&""!==e.categoryName&&t.optionCategoryName.push(e.categoryName),r+=e.sellThroughLY,a+=e.sellThroughLY1})),this.arraySellCompareTotal.length>0&&this.arraySellCompareTotal.push({categoryName:"合计",sellThroughLY:r,sellThroughLY1:a}),this.queryCategoryName=""}}}},methods:{resutlIcon:function(e,t){return e>t?"el-icon-top":e<t?"el-icon-bottom":"el-icon-minus"},tableLastCellStyle:function(e){var t=e.rowIndex;return t===this.arraySellCompareTotal.length-1?{background:"#F6F7FA",fontWeight:700,height:"30px"}:{height:"30px"}}}},Ae=Be,Ve=(r("867e"),Object(S["a"])(Ae,Ee,Me,!1,null,"29ebdb6b",null)),Qe=Ve.exports,$e=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"total_ten_content_top"},[r("div",{staticClass:"total_ten_content_top_title"},[r("div",{staticClass:"total_ten_content_top_title_desc"},[e._v("\n                销售数据汇总 (单位: 升) \n            ")]),r("div",{staticClass:"total_ten_content_top_title_filter"},[r("el-form",{staticStyle:{"margin-top":"10px"},attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"Top:","label-width":"32px"}},[r("el-select",{staticStyle:{width:"50px"},attrs:{placeholder:"请选择"},model:{value:e.queryTopN,callback:function(t){e.queryTopN=t},expression:"queryTopN"}},e._l(e.optionTopN,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1)],1),r("div",{staticClass:"total_ten_content_top_title_desc"},[r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1)]),r("el-button",{staticClass:"total_ten_content_top_detail",attrs:{size:"mini"},on:{click:function(t){e.dialogTableVisible=!0}}},[e._v("\n            查看详情\n        ")])],1),r("div",{staticClass:"total_ten_content_table"},[r("el-table",{staticStyle:{width:"100%",position:"absolute"},attrs:{data:e.arrayTotalTopTenTotal.slice(0,e.queryTopN),border:"",stripe:"",size:"mini","max-height":"345","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"dmsWorkshopName",label:"DMS客户名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:"销售",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLScale",label:"%",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("progressBar",{attrs:{dicProgress:{width:"100px",height:"18px",colorLeft:"#92D050",colorRight:"#E1EFD9",percentView:String(e.row.sellThroughLScale<=0?0:e.row.sellThroughLScale/e.row.maxSellThroughScale*100)+"%",percentLable:String(e.row.sellThroughLScale)+"%"}}})]}}])})],1)],1),r("el-dialog",{attrs:{title:"销售数据汇总 (单位: 升)",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"DMS客户名称:"}},[r("el-select",{staticStyle:{width:"180px"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.storeNameChange},model:{value:e.queryWorkShopName,callback:function(t){e.queryWorkShopName=t},expression:"queryWorkShopName"}},e._l(e.optionWorkShopNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayTotalTopTenShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"dmsWorkshopName",label:"DMS客户名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:"销售",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLScale",label:"%",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("progressBar",{attrs:{dicProgress:{width:"100px",height:"18px",colorLeft:"#92D050",colorRight:"#E1EFD9",percentView:String(e.row.sellThroughLScale<=0?0:e.row.sellThroughLScale/e.row.maxSellThroughScale*100)+"%",percentLable:String(e.row.sellThroughLScale)+"%"}}})]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayTotalTopTenShow.length},on:{"current-change":e.currentChange}})],1)],1)},Re=[];function He(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Ue(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?He(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):He(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Ge={components:{progressBar:De},props:{arrayTotalTopTen:{type:Array}},data:function(){return{dialogTableVisible:!1,arrayTotalTopTenTotal:[],arrayTotalTopTenShow:[],currentPage:1,pageSize:10,optionTopN:[10,20,30,40,50],queryTopN:10,optionWorkShopNames:[],queryWorkShopName:""}},computed:Ue(Ue({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"}}),watch:{arrayTotalTopTen:{deep:!0,handler:function(e){var t=this;if(e){var r=[];e.forEach((function(e){r.push((100*e.sellThroughLScale).toFixed(2))}));var a=Math.max.apply(null,r);this.arrayTotalTopTenTotal.splice(0,this.arrayTotalTopTenTotal.length),this.optionWorkShopNames.splice(0,this.optionWorkShopNames.length),e.forEach((function(e){t.arrayTotalTopTenTotal.push({dmsWorkshopName:e.dmsWorkshopName,sellThroughLY:Math.round(e.sellThroughLY),sellThroughLScale:(100*e.sellThroughLScale).toFixed(2),maxSellThroughScale:a}),null!==e.dmsWorkshopName&&""!==e.dmsWorkshopName&&t.optionWorkShopNames.push(e.dmsWorkshopName)})),this.arrayTotalTopTenShow=this.arrayTotalTopTenTotal,this.queryWorkShopName=""}}}},methods:{storeNameChange:function(){var e=this;this.queryWorkShopName.length>0?(this.arrayTotalTopTenShow=this.arrayTotalTopTenTotal.filter((function(t){return t.dmsWorkshopName===e.queryWorkShopName})),this.currentPage=1):(this.arrayTotalTopTenShow=this.arrayTotalTopTenTotal,this.currentPage=1)},currentChange:function(e){this.currentPage=e}}},Je=Ge,Ke=(r("1cd3"),Object(S["a"])(Je,$e,Re,!1,null,"42fa5452",null)),Xe=Ke.exports,Ze=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"total_compare_content_top"},[r("div",{staticClass:"total_compare_content_top_title"},[r("div",{staticClass:"total_compare_content_top_title_desc"},[e._v("\n                "+e._s(e.dealerYear)+" VS "+e._s(e.dealerYear-1)+" (单位: 升)\n            ")]),r("div",{staticClass:"total_compare_content_top_title_filter"},[r("el-form",{staticStyle:{"margin-top":"10px"},attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"Top:","label-width":"32px"}},[r("el-select",{staticStyle:{width:"50px"},attrs:{placeholder:"请选择"},model:{value:e.queryTopN,callback:function(t){e.queryTopN=t},expression:"queryTopN"}},e._l(e.optionTopN,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1)],1),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"align-self":"center","margin-left":"1px"}})])],1),r("el-button",{staticClass:"total_compare_content_top_detail",attrs:{size:"mini"},on:{click:function(t){e.dialogTableVisible=!0}}},[e._v("\n            查看详情\n        ")])],1),r("div",{staticClass:"total_compare_content_table"},[r("el-table",{staticStyle:{width:"100%",position:"absolute"},attrs:{data:e.arrayTotalCompareTotal.slice(0,e.queryTopN),border:"",stripe:"",size:"mini","max-height":"345","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"dmsWorkshopName",label:"DMS客户名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY1",label:""+(e.dealerYear-1),align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY1))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:""+e.dealerYear,align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{label:e.dealerYear+" VS "+(e.dealerYear-1),align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[r("i",{class:e.resutlIcon(t.row.sellThroughLY,t.row.sellThroughLY1),style:t.row.sellThroughLY>=t.row.sellThroughLY1?"align-self: center; margin-left: 15px; font-size: 12px; font-weight: bold; color: #53A68F":"align-self: center; margin-left: 15px; font-size: 12px; font-weight: bold; color: #E74A20"}),r("span",{staticStyle:{"align-self":"center"}},[e._v("\n                            "+e._s(e._f("toThousand")(Math.abs(t.row.sellThroughLY-t.row.sellThroughLY1)))+"\n                        ")])])]}}])})],1)],1),r("el-dialog",{attrs:{title:e.dealerYear+" VS "+(e.dealerYear-1)+" (单位: 升)",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"DMS客户名称:"}},[r("el-select",{staticStyle:{width:"180px"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.storeNameChange},model:{value:e.queryWorkShopName,callback:function(t){e.queryWorkShopName=t},expression:"queryWorkShopName"}},e._l(e.optionWorkShopNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayTotalCompareShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"dmsWorkshopName",label:"DMS客户名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY1",label:""+(e.dealerYear-1),align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY1))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:""+e.dealerYear,align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{label:e.dealerYear+" VS "+(e.dealerYear-1),align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[r("i",{class:e.resutlIcon(t.row.sellThroughLY,t.row.sellThroughLY1),style:t.row.sellThroughLY>=t.row.sellThroughLY1?"align-self: center; margin-left: 15px; font-size: 12px; font-weight: bold; color: #53A68F":"align-self: center; margin-left: 15px; font-size: 12px; font-weight: bold; color: #E74A20"}),r("span",{staticStyle:{"align-self":"center"}},[e._v("\n                            "+e._s(e._f("toThousand")(Math.abs(t.row.sellThroughLY-t.row.sellThroughLY1)))+"\n                        ")])])]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayTotalCompareShow.length},on:{"current-change":e.currentChange}})],1)],1)},et=[];function tt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function rt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tt(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var at={props:{arrayTotalCompare:{type:Array}},data:function(){return{dialogTableVisible:!1,arrayTotalCompareTotal:[],arrayTotalCompareShow:[],currentPage:1,pageSize:10,optionTopN:[10,20,30,40,50],queryTopN:10,optionWorkShopNames:[],queryWorkShopName:""}},computed:rt(rt({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+(new Date).getMonth()+"月";return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"年"+e+"的数"}return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"全年的数"}}),mounted:function(){var e=this;window.addEventListener("resize",(function(){var t=document.documentElement.clientWidth<1300?1300:document.documentElement.clientWidth;e.tableWidth=(t-152)/2+"px"}))},watch:{arrayTotalCompare:{deep:!0,handler:function(e){var t=this;e&&(this.arrayTotalCompareTotal.splice(0,this.arrayTotalCompareTotal.length),this.optionWorkShopNames.splice(0,this.optionWorkShopNames.length),e.forEach((function(e){t.arrayTotalCompareTotal.push({dmsWorkshopName:e.dmsWorkshopName,sellThroughLY:Math.round(e.sellThroughLY),sellThroughLY1:Math.round(e.sellThroughLY1)}),null!==e.dmsWorkshopName&&""!==e.dmsWorkshopName&&t.optionWorkShopNames.push(e.dmsWorkshopName)})),this.arrayTotalCompareShow=this.arrayTotalCompareTotal,this.queryWorkShopName="")}}},methods:{storeNameChange:function(){var e=this;this.queryWorkShopName.length>0?(this.arrayTotalCompareShow=this.arrayTotalCompareTotal.filter((function(t){return t.dmsWorkshopName===e.queryWorkShopName})),this.currentPage=1):(this.arrayTotalCompareShow=this.arrayTotalCompareTotal,this.currentPage=1)},resutlIcon:function(e,t){return e>t?"el-icon-top":e<t?"el-icon-bottom":"el-icon-minus"},currentChange:function(e){this.currentPage=e}}},ot=at,nt=(r("dd94"),Object(S["a"])(ot,Ze,et,!1,null,"109ee411",null)),lt=nt.exports;function it(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function st(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?it(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):it(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var ct={components:{storeScreen:ve,sellThrough:Le,sellCompare:Qe,totalTopTen:Xe,totalCompare:lt},computed:st({},Object(v["b"])(["dealerInfo","dealerYear"])),data:function(){return{loading:!1,dicStoreScreenInfo:{},arraySellThrough:[],arraySellCompare:[],arrayTotalTopTen:[],arrayTotalCompare:[]}},mounted:function(){this.getDmsStoreScreenInfo(),this.getDmsStoreCategoryInfo({}),this.getDmsStoreTotalInfo({})},watch:{dealerInfo:function(e){e&&(this.getDmsStoreScreenInfo(),this.getDmsStoreCategoryInfo({}),this.getDmsStoreTotalInfo({}))},dealerYear:function(e){e&&this.dealerInfo&&(this.getDmsStoreScreenInfo(),this.getDmsStoreCategoryInfo({}),this.getDmsStoreTotalInfo({}))}},methods:{getDmsStoreScreenInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,T["a"].getDmsStoreScreenInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear});case 2:t=e.sent,r=Object(i["a"])(t,2),a=r[0],o=r[1],a&&(this.dicStoreScreenInfo=o.data);case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getDmsStoreCategoryInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var r,a,o,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,T["a"].getDmsStoreCategoryInfo(Object.assign({},{distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear},t));case 2:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&(this.arraySellThrough=n.resultLst.sort((function(e,t){return t.sellThroughLY-e.sellThroughLY})),this.arraySellCompare=n.lastMonth.sort((function(e,t){return t.sellThroughLY-e.sellThroughLY})));case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getDmsStoreTotalInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var r,a,o,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loading=!0,e.next=3,T["a"].getDmsStoreTotalInfo(Object.assign({},{distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear},t));case 3:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&(this.arrayTotalTopTen=n.resultLst.sort((function(e,t){return t.sellThroughLY-e.sellThroughLY})),this.arrayTotalCompare=n.lastMonth.sort((function(e,t){return t.sellThroughLY-e.sellThroughLY}))),this.loading=!1;case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),storeScreenQuery:function(e){this.getDmsStoreCategoryInfo(e),this.getDmsStoreTotalInfo(e)}}},ut=ct,ht=(r("759c"),Object(S["a"])(ut,ge,fe,!1,null,"7d765854",null)),dt=ht.exports,pt=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"product_container"},[r("div",{staticClass:"product_content_screen"},[r("productScreen",{attrs:{dicProductScreenInfo:e.dicProductScreenInfo},on:{productScreenQuery:e.productScreenQuery}})],1),r("div",{staticClass:"product_content_annual"},[r("div",{staticClass:"product_content_annual_title"},[e._v("\n            1. 年度数据汇总\n        ")]),r("div",{staticClass:"product_content_annual_table"},[r("div",{staticClass:"product_content_annual_ten"},[r("annualTopTen",{attrs:{arrayAnnualTopTen:e.arrayAnnualTopTen}})],1),r("div",{staticClass:"product_content_annual_compare"},[r("annualCompare",{attrs:{arrayAnnualCompare:e.arrayAnnualCompare}})],1)])]),r("div",{staticClass:"product_content_unsalable_stock"},[r("div",{staticClass:"product_content_unsalable"},[r("div",{staticClass:"product_content_unsalable_table"},[r("unsalable",{attrs:{arrayProductUnsalable:e.arrayProductUnsalable}})],1)]),r("div",{staticClass:"product_content_stock"},[r("div",{staticClass:"product_content_stock_table"},[r("stockAlert",{attrs:{arrayProductStock:e.arrayProductStock}})],1)])])])},yt=[],gt=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{"padding-top":"10px"}},[r("el-form",{attrs:{inline:!0}},[r("el-form-item",{attrs:{label:"渠道:","label-width":"40px"}},[r("el-select",{staticStyle:{width:"155px"},attrs:{multiple:"",clearable:"",size:e.querySalesChannels.length<2?"small":"",placeholder:"请选择"},on:{change:e.salesChannelChange},model:{value:e.querySalesChannels,callback:function(t){e.querySalesChannels=t},expression:"querySalesChannels"}},e._l(e.optionSalesChannels,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"产品分类:","label-width":"70px"}},[r("el-select",{staticStyle:{width:"290px"},attrs:{multiple:"",clearable:"",size:e.queryProductTypes.length<3?"small":"",placeholder:"请选择"},model:{value:e.queryProductTypes,callback:function(t){e.queryProductTypes=t},expression:"queryProductTypes"}},e._l(e.optionProductTypes,(function(e,t){return r("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-tooltip",{attrs:{effect:"light",placement:"bottom"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("el-table",{attrs:{data:e.tableData,border:"",stripe:"",size:"mini","span-method":e.arraySpanMethod,"header-cell-style":{background:"#F5F7FA",height:"35px",fontWeight:700},"cell-style":{height:"25px"}}},[r("el-table-column",{attrs:{prop:"channel",label:"渠道",align:"center",width:"50"}}),r("el-table-column",{attrs:{prop:"categoryT2",label:"一级分类",align:"center",width:"60"}}),r("el-table-column",{attrs:{prop:"categoryT3",label:"二级分类",align:"left",width:"400"}}),r("el-table-column",{attrs:{prop:"product",label:"二级分类名称",align:"center",width:"95"}})],1)],1),r("i",{staticClass:"el-icon-info",staticStyle:{"margin-top":"10px","margin-left":"-6px"}})]),r("el-form-item",{attrs:{label:" ","label-width":"20px"}},[r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.queryProductInfoClick}},[e._v("查询")])],1)],1)],1)},ft=[],mt={props:{dicProductScreenInfo:{type:Object}},data:function(){return{optionSalesChannels:[],querySalesChannels:[],optionProductTypes:[],queryProductTypes:[],dicProductTypes:{},tableData:[{channel:"商用油",categoryT2:"柴机油",categoryT3:"C-E CI-4，D-E CI-4，D-E CJ-4，D-E CK-4，D-E NG - 400，D-E Others",product:"德乐高端"},{channel:"商用油",categoryT2:"柴机油",categoryT3:"D-E CH-4，D-E NG - Gold",product:"德乐中端"},{channel:"商用油",categoryT2:"柴机油",categoryT3:"C-E CH-4，D-E CF-4",product:"德乐低端"},{channel:"商用油",categoryT2:"柴机油",categoryT3:"Acton Power EO，D-E CD，D-E CI-4 Plus，Gas Engine Oil，OEM-Others，Others，Others，Private - LiFu",product:"德乐其它"},{channel:"商用油",categoryT2:"液压油",categoryT3:"Mid Tier",product:"液压油 Mid Tier"},{channel:"商用油",categoryT2:"液压油",categoryT3:"Low Tier",product:"液压油 Low Tier"},{channel:"商用油",categoryT2:"液压油",categoryT3:"High Tier，Ashless，OEM-Borche，OEM-HLT，OEM-JIDD，OEM-Jinke，OEM-Keda，OEM-Mingsheng，OEM-Xingdi，Premium Tier，Private - Enric，Private - LiFu，Private - LiFuLong，Wind Power",product:"液压油其它"},{channel:"商用油",categoryT2:"其它",categoryT3:"属于商用油的其它分类都叫“商用油其它”",product:"其它"},{channel:"乘用车",categoryT2:"汽机油",categoryT3:"H-E SN & Above，H-E SN & Above FS - Premium，H-E SN & Above FS - Value，H-E SN & Above SB",product:"金富力高端"},{channel:"乘用车",categoryT2:"汽机油",categoryT3:"H-E SG，H-E SJ，H-E SL，H-E SM",product:"金富力低端"},{channel:"乘用车",categoryT2:"汽机油",categoryT3:"OEM-BAIC，OEM-Others",product:"金富力其它"},{channel:"乘用车",categoryT2:"防冻液",categoryT3:"D-C ELC 55/45，H-C XLC 50/50，H-C XLC 55/45，H-C XLC Conc",product:"防冻液"},{channel:"乘用车",categoryT2:"添加剂",categoryT3:"TCP-100ml，TCP-295ml，TCP-355ml，TCP-470ml",product:"TCP添加剂"},{channel:"乘用车",categoryT2:"其它",categoryT3:"属于乘用车的其它分类都叫“乘用车其它”",product:"其它"}]}},watch:{dicProductScreenInfo:{deep:!0,handler:function(e){var t=this;if(e){this.optionSalesChannels.splice(0,this.optionSalesChannels.length);var r=e.productChannels.filter((function(e){return null!==e&&""!==e}));r.forEach((function(e){var r="其他";"Commercial"===e?r="商用油":"Chevron Others"===e?r="雪佛龙其它":"Consumer"===e&&(r="乘用车"),t.optionSalesChannels.push({value:e,label:r})})),this.querySalesChannels=[],this.dicProductTypes=e.productCategory,this.formateProductTypes()}}}},methods:{formateProductTypes:function(){var e=this,t=[];this.querySalesChannels.length>0?this.querySalesChannels.forEach((function(r){t=t.concat(e.dicProductTypes[r])})):this.optionSalesChannels.forEach((function(r){t=t.concat(e.dicProductTypes[r.value])})),this.optionProductTypes.splice(0,this.optionProductTypes.length),t.forEach((function(t){for(var r in t)e.optionProductTypes.push({value:r,label:t[r]})})),this.queryProductTypes=[]},salesChannelChange:function(){this.formateProductTypes()},queryProductInfoClick:function(){var e={};this.querySalesChannels.length>0&&(e={productChannel:this.querySalesChannels.join(",")});var t={};this.queryProductTypes.length>0&&(t={productCategory:this.queryProductTypes.join(",")}),this.$emit("productScreenQuery",Object.assign({},e,t))},arraySpanMethod:function(e){var t=e.rowIndex,r=e.columnIndex;return 0===r?0===t?[8,1]:8===t?[6,1]:[0,0]:1===r?0===t?[4,1]:4===t||8===t?[3,1]:11===t||12===t?[1,1]:[0,0]:2===r?7===t||13===t?[1,3]:[1,1]:7===t||13===t?[0,0]:[1,1]}}},bt=mt,St=(r("bd72"),Object(S["a"])(bt,gt,ft,!1,null,null,null)),Pt=St.exports,Ct=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"annual_ten_content_top"},[r("div",{staticClass:"annual_ten_content_top_title"},[r("div",{staticClass:"annual_ten_content_top_title_desc"},[e._v("\n                产品销售数据汇总 (单位: 升)\n            ")]),r("div",{staticClass:"annual_ten_content_top_title_filter"},[r("el-form",{staticStyle:{"margin-top":"10px"},attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"Top:","label-width":"32px"}},[r("el-select",{staticStyle:{width:"50px"},attrs:{placeholder:"请选择"},model:{value:e.queryTopN,callback:function(t){e.queryTopN=t},expression:"queryTopN"}},e._l(e.optionTopN,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1)],1),r("div",{staticClass:"annual_ten_content_top_title_filter"},[r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px"}})])],1)]),r("el-button",{staticClass:"annual_ten_content_top_detail",attrs:{size:"mini"},on:{click:function(t){e.dialogTableVisible=!0}}},[e._v("\n            查看详情\n        ")])],1),r("div",{staticClass:"annual_ten_content_table"},[r("el-table",{staticStyle:{width:"100%",position:"absolute"},attrs:{data:e.arrayProductAnnualTotal.slice(0,e.queryTopN),border:"",stripe:"",size:"mini","max-height":"345","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:"销售",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLScale",label:"%",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("progressBar",{attrs:{dicProgress:{width:"100px",height:"18px",colorLeft:"#92D050",colorRight:"#E1EFD9",percentView:String(e.row.sellThroughLScale<=0?0:e.row.sellThroughLScale/e.row.maxSellThroughScale*100)+"%",percentLable:String(e.row.sellThroughLScale)+"%"}}})]}}])})],1)],1),r("el-dialog",{attrs:{title:"产品销售数据汇总 (单位: 升)",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"产品名称:"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayProductAnnualShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:"销售",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLScale",label:"%",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("progressBar",{attrs:{dicProgress:{width:"100px",height:"18px",colorLeft:"#92D050",colorRight:"#E1EFD9",percentView:String(e.row.sellThroughLScale<=0?0:e.row.sellThroughLScale/e.row.maxSellThroughScale*100)+"%",percentLable:String(e.row.sellThroughLScale)+"%"}}})]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayProductAnnualShow.length},on:{"current-change":e.currentChange}})],1)],1)},vt=[];function Tt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function _t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Tt(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var wt={components:{progressBar:De},props:{arrayAnnualTopTen:{type:Array}},computed:_t(_t({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"}}),data:function(){return{dialogTableVisible:!1,arrayProductAnnualTotal:[],arrayProductAnnualShow:[],currentPage:1,pageSize:10,optionTopN:[10,20,30,40,50],queryTopN:10,optionProductNames:[],queryProductName:""}},watch:{arrayAnnualTopTen:{deep:!0,handler:function(e){var t=this;if(e){var r=[];e.forEach((function(e){r.push((100*e.sellThroughLScale).toFixed(2))}));var a=Math.max.apply(null,r);this.arrayProductAnnualTotal.splice(0,this.arrayProductAnnualTotal.length),this.optionProductNames.splice(0,this.optionProductNames.length),e.forEach((function(e){t.arrayProductAnnualTotal.push({productNameCn:e.productNameCn,sellThroughLY:Math.round(e.sellThroughLY),sellThroughLScale:(100*e.sellThroughLScale).toFixed(2),maxSellThroughScale:a}),null!==e.productNameCn&&""!==e.productNameCn&&t.optionProductNames.push(e.productNameCn)})),this.arrayProductAnnualShow=this.arrayProductAnnualTotal,this.queryProductName=""}}}},methods:{productNameChange:function(){var e=this;this.queryProductName.length>0?(this.arrayProductAnnualShow=this.arrayProductAnnualTotal.filter((function(t){return t.productNameCn===e.queryProductName})),this.currentPage=1):(this.arrayProductAnnualShow=this.arrayProductAnnualTotal,this.currentPage=1)},currentChange:function(e){this.currentPage=e}}},Yt=wt,Ot=(r("a322"),Object(S["a"])(Yt,Ct,vt,!1,null,"ce1e2bf6",null)),xt=Ot.exports,Nt=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"annual_compare_content_top"},[r("div",{staticClass:"annual_compare_content_top_title"},[r("div",{staticClass:"annual_compare_content_top_title_desc"},[e._v("\n                "+e._s(e.dealerYear)+" VS "+e._s(e.dealerYear-1)+" (单位: 升)\n            ")]),r("div",{staticClass:"annual_compare_content_top_title_filter"},[r("el-form",{staticStyle:{"margin-top":"10px"},attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"Top:","label-width":"32px"}},[r("el-select",{staticStyle:{width:"50px"},attrs:{placeholder:"请选择"},model:{value:e.queryTopN,callback:function(t){e.queryTopN=t},expression:"queryTopN"}},e._l(e.optionTopN,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1)],1),r("el-tooltip",{attrs:{effect:"dark",content:e.toolTipContent,placement:"right"}},[r("i",{staticClass:"el-icon-info",staticStyle:{"align-self":"center","margin-left":"1px"}})])],1),r("el-button",{staticClass:"annual_compare_content_top_detail",attrs:{size:"mini"},on:{click:function(t){e.dialogTableVisible=!0}}},[e._v("\n            查看详情\n        ")])],1),r("div",{staticClass:"annual_compare_content_table"},[r("el-table",{staticStyle:{width:"100%",position:"absolute"},attrs:{data:e.arrayProductCompareTotal.slice(0,e.queryTopN),border:"",stripe:"",size:"mini","max-height":"345","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY1",label:""+(e.dealerYear-1),align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY1))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:""+e.dealerYear,align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{label:e.dealerYear+" VS "+(e.dealerYear-1),align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[r("i",{class:e.resutlIcon(t.row.sellThroughLY,t.row.sellThroughLY1),style:t.row.sellThroughLY>=t.row.sellThroughLY1?"align-self: center; margin-left: 10px; font-size: 12px; font-weight: bold; color: #53A68F":"align-self: center; margin-left: 10px; font-size: 12px; font-weight: bold; color: #E74A20"}),r("span",{staticStyle:{"align-self":"center"}},[e._v("\n                            "+e._s(e._f("toThousand")(Math.abs(t.row.sellThroughLY-t.row.sellThroughLY1)))+"\n                        ")])])]}}])})],1)],1),r("el-dialog",{attrs:{title:e.dealerYear+" VS "+(e.dealerYear-1)+" (单位: 升)",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"产品名称:"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayProductCompareShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"sellThroughLY1",label:""+(e.dealerYear-1),align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY1))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"sellThroughLY",label:""+e.dealerYear,align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.sellThroughLY))+"\n                ")]}}])}),r("el-table-column",{attrs:{label:e.dealerYear+" VS "+(e.dealerYear-1),align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[r("i",{class:e.resutlIcon(t.row.sellThroughLY,t.row.sellThroughLY1),style:t.row.sellThroughLY>=t.row.sellThroughLY1?"align-self: center; margin-left: 10px; font-size: 12px; font-weight: bold; color: #53A68F":"align-self: center; margin-left: 10px; font-size: 12px; font-weight: bold; color: #E74A20"}),r("span",{staticStyle:{"align-self":"center"}},[e._v("\n                            "+e._s(e._f("toThousand")(Math.abs(t.row.sellThroughLY-t.row.sellThroughLY1)))+"\n                        ")])])]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayProductCompareShow.length},on:{"current-change":e.currentChange}})],1)],1)},Dt=[];function kt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Ft(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?kt(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var jt={props:{arrayAnnualCompare:{type:Array}},data:function(){return{dialogTableVisible:!1,arrayProductCompareTotal:[],arrayProductCompareShow:[],currentPage:1,pageSize:10,optionTopN:[10,20,30,40,50],queryTopN:10,optionProductNames:[],queryProductName:""}},computed:Ft(Ft({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+(new Date).getMonth()+"月";return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"年"+e+"的数"}return"同期比较"+(this.dealerYear-1)+"和"+this.dealerYear+"全年的数"}}),watch:{arrayAnnualCompare:{deep:!0,handler:function(e){var t=this;e&&(this.arrayProductCompareTotal.splice(0,this.arrayProductCompareTotal.length),this.optionProductNames.splice(0,this.optionProductNames.length),e.forEach((function(e){t.arrayProductCompareTotal.push({productNameCn:e.productNameCn,sellThroughLY:Math.round(e.sellThroughLY),sellThroughLY1:Math.round(e.sellThroughLY1)}),null!==e.productNameCn&&""!==e.productNameCn&&t.optionProductNames.push(e.productNameCn)})),this.arrayProductCompareShow=this.arrayProductCompareTotal,this.queryProductName="")}}},methods:{productNameChange:function(){var e=this;this.queryProductName.length>0?(this.arrayProductCompareShow=this.arrayProductCompareTotal.filter((function(t){return t.productNameCn===e.queryProductName})),this.currentPage=1):(this.arrayProductCompareShow=this.arrayProductCompareTotal,this.currentPage=1)},resutlIcon:function(e,t){return e>t?"el-icon-top":e<t?"el-icon-bottom":"el-icon-minus"},currentChange:function(e){this.currentPage=e}}},qt=jt,It=(r("3591"),Object(S["a"])(qt,Nt,Dt,!1,null,"4e1a9f0c",null)),Lt=It.exports,Et=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"product_content_unsalable_title"},[r("div",{staticClass:"product_content_unsalable_title_desc"},[e._v("\n            2. 滞销产品列表 (单位: 升)\n        ")]),r("div",{staticClass:"product_content_unsalable_title_filter"},[r("el-form",{staticStyle:{"margin-top":"10px"},attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"Top:","label-width":"35px"}},[r("el-select",{staticStyle:{width:"50px"},attrs:{placeholder:"请选择"},model:{value:e.queryTopN,callback:function(t){e.queryTopN=t},expression:"queryTopN"}},e._l(e.optionTopN,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1)],1),r("el-tooltip",{attrs:{effect:"dark",placement:"right"}},[r("div",{attrs:{slot:"content"},slot:"content"},[e._v("\n                "+e._s(e.toolTipContent)+"\n                "),r("br"),e._v("\n                无销量天数分别为: 30天、60天、90天\n                "),r("br"),e._v("\n                滞销品明细中, 库存数量为当前库存量\n            ")]),r("i",{staticClass:"el-icon-info",staticStyle:{"align-self":"center","margin-left":"1px"}})])],1),r("div",{staticClass:"unsalable_content_top"},[r("div",{staticClass:"unsalable_content_top_filter"},[r("el-radio-group",{attrs:{size:"mini"},on:{change:e.unsalableDayChange},model:{value:e.unsalableDay,callback:function(t){e.unsalableDay=t},expression:"unsalableDay"}},[r("el-radio-button",{attrs:{label:30}},[e._v("30天")]),r("el-radio-button",{attrs:{label:60}},[e._v("60天")]),r("el-radio-button",{attrs:{label:90}},[e._v("90天")])],1)],1),r("el-button",{staticClass:"unsalable_content_top_detail",attrs:{size:"mini"},on:{click:function(t){e.dialogTableVisible=!0}}},[e._v("\n            查看详情\n        ")])],1),r("div",{staticClass:"unsalable_content_table"},[r("el-table",{staticStyle:{width:"100%",position:"absolute"},attrs:{data:e.arrayProductUnsalableShowTop.slice(0,e.queryTopN),border:"",stripe:"",size:"mini","max-height":"345","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"stockQuantityY",label:"当前库存",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.stockQuantityY))+"\n                ")]}}])})],1)],1),r("el-dialog",{attrs:{title:"滞销产品列表",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"产品名称:"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayProductUnsalableShowDetail.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"stockQuantityY",label:"当前库存",align:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.stockQuantityY))+"\n                ")]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayProductUnsalableShowDetail.length},on:{"current-change":e.currentChange}})],1)],1)},Mt=[];function zt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Wt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?zt(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Bt={props:{arrayProductUnsalable:{type:Array}},computed:Wt(Wt({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计"+this.dealerYear+"全年的数"}}),data:function(){return{unsalableDay:30,dialogTableVisible:!1,arrayProductUnsalableTotal:[],arrayProductUnsalableShowTop:[],arrayProductUnsalableShowDetail:[],currentPage:1,pageSize:10,optionTopN:[10,20,30,40,50],queryTopN:10,optionProductNames:[],queryProductName:""}},watch:{arrayProductUnsalable:{deep:!0,handler:function(e){var t=this;this.arrayProductUnsalableTotal.splice(0,this.arrayProductUnsalableTotal.length),this.optionProductNames.splice(0,this.optionProductNames.length),e.forEach((function(e){t.arrayProductUnsalableTotal.push({productNameCn:e.productNameCn,stockQuantityY:Math.round(e.stockQuantityY),stagnatingSales:e.stagnatingSales}),null!==e.productNameCn&&""!==e.productNameCn&&t.optionProductNames.push(e.productNameCn)})),this.unsalableDay=30,this.arrayProductUnsalableShowTop=this.arrayProductUnsalableTotal.filter((function(e){return e.stagnatingSales>=t.unsalableDay})),this.arrayProductUnsalableShowDetail=this.arrayProductUnsalableTotal,this.queryProductName=""}}},methods:{unsalableDayChange:function(e){this.arrayProductUnsalableShowTop=this.arrayProductUnsalableTotal.filter((function(t){return t.stagnatingSales>=e}))},productNameChange:function(){var e=this;this.queryProductName.length>0?(this.arrayProductUnsalableShowDetail=this.arrayProductUnsalableTotal.filter((function(t){return t.productNameCn===e.queryProductName})),this.currentPage=1):(this.arrayProductUnsalableShowDetail=this.arrayProductUnsalableTotal,this.currentPage=1)},currentChange:function(e){this.currentPage=e}}},At=Bt,Vt=(r("d95c"),Object(S["a"])(At,Et,Mt,!1,null,"174866d2",null)),Qt=Vt.exports,$t=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("div",{staticClass:"product_content_stock_title"},[r("div",{staticClass:"product_content_stock_title_desc"},[e._v("\n            3. 库存预警 (单位: 升)\n        ")]),r("div",{staticClass:"product_content_stock_title_filter"},[r("el-form",{staticStyle:{"margin-top":"10px"},attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"Top:","label-width":"35px"}},[r("el-select",{staticStyle:{width:"50px"},attrs:{placeholder:"请选择"},model:{value:e.queryTopN,callback:function(t){e.queryTopN=t},expression:"queryTopN"}},e._l(e.optionTopN,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1)],1),r("el-tooltip",{attrs:{effect:"dark",placement:"right"}},[r("div",{attrs:{slot:"content"},slot:"content"},[e._v("\n                "+e._s(e.toolTipContent)+"\n                "),r("br"),e._v("\n                可销售天数=当前库存/过去60天日平均销量\n            ")]),r("i",{staticClass:"el-icon-info",staticStyle:{"align-self":"center","margin-left":"1px"}})])],1),r("div",{staticClass:"stock_content_top"},[r("div",{staticClass:"stock_content_top_title"}),r("el-button",{staticClass:"stock_content_top_detail",attrs:{size:"mini"},on:{click:function(t){e.dialogTableVisible=!0}}},[e._v("\n            查看详情\n        ")])],1),r("div",{staticClass:"stock_content_table"},[r("el-table",{staticStyle:{width:"100%",position:"absolute"},attrs:{data:e.arrayProductStockTotal.slice(0,e.queryTopN),border:"",stripe:"",size:"mini","max-height":"345","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"stockQuantityY",label:"当前库存",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.stockQuantityY))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"avgSellThroughL60",label:"过去60天日平均销量",align:"right",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.avgSellThroughL60))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"salesDay",label:"可销售天数",align:"right",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.salesDay))+"\n                ")]}}])})],1)],1),r("el-dialog",{attrs:{title:"库存预警",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[r("el-form",{attrs:{inline:!0,size:"small"}},[r("el-form-item",{attrs:{label:"产品名称:"}},[r("el-select",{staticStyle:{width:"315px"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.productNameChange},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}},e._l(e.optionProductNames,(function(e,t){return r("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1)],1),r("el-table",{attrs:{data:e.arrayProductStockShow.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),border:"",stripe:"",size:"mini","header-cell-style":{background:"#277BB9",height:"35px",fontWeight:700,color:"#FFFFFF"},"cell-style":{height:"30px"}}},[r("el-table-column",{attrs:{prop:"productNameCn",label:"产品名称"}}),r("el-table-column",{attrs:{prop:"stockQuantityY",label:"当前库存",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.stockQuantityY))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"avgSellThroughL60",label:"过去60天日平均销量",align:"right",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.avgSellThroughL60))+"\n                ")]}}])}),r("el-table-column",{attrs:{prop:"salesDay",label:"可销售天数",align:"right",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(e._f("toThousand")(t.row.salesDay))+"\n                ")]}}])})],1),r("el-pagination",{staticStyle:{"margin-top":"10px","text-align":"center"},attrs:{small:"",layout:"prev, pager, next","current-page":e.currentPage,"page-size":e.pageSize,total:e.arrayProductStockShow.length},on:{"current-change":e.currentChange}})],1)],1)},Rt=[];function Ht(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Ut(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ht(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ht(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Gt={props:{arrayProductStock:{type:Array}},data:function(){return{dialogTableVisible:!1,arrayProductStockTotal:[],arrayProductStockShow:[],currentPage:1,pageSize:10,optionTopN:[10,20,30,40,50],queryTopN:10,optionProductNames:[],queryProductName:""}},computed:Ut(Ut({},Object(v["b"])(["dealerYear"])),{},{toolTipContent:function(){if(this.dealerYear===(new Date).getFullYear()){var e=0===(new Date).getMonth()?"1月":"1-"+((new Date).getMonth()+1)+"月";return"YTD累计".concat(e,"数")}return"YTD累计".concat(this.dealerYear,"全年的数")}}),watch:{arrayProductStock:{deep:!0,handler:function(e){var t=this;this.arrayProductStockTotal.splice(0,this.arrayProductStockTotal.length),this.optionProductNames.splice(0,this.optionProductNames.length),e.forEach((function(e){t.arrayProductStockTotal.push({productNameCn:e.productNameCn,stockQuantityY:Math.round(e.stockQuantityY),avgSellThroughL60:e.avgSellThroughL60.toFixed(2),salesDay:Math.round(e.salesDay)}),null!==e.productNameCn&&""!==e.productNameCn&&t.optionProductNames.push(e.productNameCn)})),this.arrayProductStockShow=this.arrayProductStockTotal,this.queryProductName=""}}},methods:{productNameChange:function(){var e=this;this.queryProductName.length>0?(this.arrayProductStockShow=this.arrayProductStockTotal.filter((function(t){return t.productNameCn===e.queryProductName})),this.currentPage=1):(this.arrayProductStockShow=this.arrayProductStockTotal,this.currentPage=1)},currentChange:function(e){this.currentPage=e}}},Jt=Gt,Kt=(r("9b54"),Object(S["a"])(Jt,$t,Rt,!1,null,"ecf77628",null)),Xt=Kt.exports;function Zt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function er(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Zt(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var tr={components:{productScreen:Pt,annualTopTen:xt,annualCompare:Lt,unsalable:Qt,stockAlert:Xt},computed:er({},Object(v["b"])(["dealerInfo","dealerYear"])),data:function(){return{loading:!1,dicProductScreenInfo:{},arrayAnnualTopTen:[],arrayAnnualCompare:[],arrayProductUnsalable:[],arrayProductStock:[]}},mounted:function(){this.getDmsProductScreenInfo(),this.getDmsProductAnnualInfo({}),this.getDmsProductUnsalableInfo({}),this.getDmsProductStockInfo({})},watch:{dealerInfo:function(e){e&&(this.getDmsProductScreenInfo(),this.getDmsProductAnnualInfo({}),this.getDmsProductUnsalableInfo({}),this.getDmsProductStockInfo({}))},dealerYear:function(e){e&&this.dealerInfo&&(this.getDmsProductScreenInfo(),this.getDmsProductAnnualInfo({}),this.getDmsProductUnsalableInfo({}),this.getDmsProductStockInfo({}))}},methods:{getDmsProductScreenInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,T["a"].getDmsProductScreenInfo({distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear});case 2:t=e.sent,r=Object(i["a"])(t,2),a=r[0],o=r[1],a&&(this.dicProductScreenInfo=o.data);case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getDmsProductAnnualInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var r,a,o,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loading=!0,e.next=3,T["a"].getDmsProductAnnualInfo(Object.assign({},{distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear},t));case 3:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&(this.arrayAnnualTopTen=n.resultLst.sort((function(e,t){return t.sellThroughLY-e.sellThroughLY})),this.arrayAnnualCompare=n.lastMonth.sort((function(e,t){return t.sellThroughLY-e.sellThroughLY}))),this.loading=!1;case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getDmsProductUnsalableInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var r,a,o,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,T["a"].getDmsProductUnsalableInfo(Object.assign({},{distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear},t));case 2:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&(this.arrayProductUnsalable=n.resultLst.sort((function(e,t){return t.stockQuantityY-e.stockQuantityY})));case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getDmsProductStockInfo:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var r,a,o,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,T["a"].getDmsProductStockInfo(Object.assign({},{distributorId:this.dealerInfo.distributorId,channelWeight:this.dealerInfo.channelWeight,year:this.dealerYear},t));case 2:r=e.sent,a=Object(i["a"])(r,2),o=a[0],n=a[1],o&&(this.arrayProductStock=n.resultLst.sort((function(e,t){return e.salesDay-t.salesDay})));case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),productScreenQuery:function(e){this.getDmsProductAnnualInfo(e),this.getDmsProductUnsalableInfo(e),this.getDmsProductStockInfo(e)}}},rr=tr,ar=(r("0a98"),Object(S["a"])(rr,pt,yt,!1,null,"4e16e4d8",null)),or=ar.exports,nr={components:{dealer:ye,store:dt,product:or},data:function(){return{activeName:"first"}}},lr=nr,ir=(r("789e"),Object(S["a"])(lr,a,o,!1,null,"1646051e",null));t["default"]=ir.exports},fd5e:function(e,t,r){"use strict";var a=r("d225"),o=r("b0b4"),n=r("4d20"),l=(r("5a0c"),function(){function e(){Object(a["a"])(this,e)}return Object(o["a"])(e,[{key:"getFundDeclaredItem",value:function(){return Object(n["a"])({method:"post",path:"wxPublicRpc.do",data:{jsonrpc:"2.0",method:"v2EliteFundFormService.getAllApplyProjects",params:[],id:1}})}},{key:"getFundDeclared",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(n["a"])({method:"post",path:"reportview/report/data.do",params:{packageName:"qbr",viewName:"EliteApplyDetail",year:e.year,distributorId:e.distributorId}})}},{key:"getFundTotal",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(n["a"])({method:"post",path:"reportview/report/data.do",params:{packageName:"qbr",viewName:"EarnEliteFund",year:e.year,distributorId:e.distributorId}})}},{key:"getFundTotalSummary",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(n["a"])({method:"post",path:"reportview/report/data.do",params:{packageName:"qbr",viewName:"UsedEliteFund",year:e.year,distributorId:e.distributorId}})}},{key:"getDmsDealerInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/getDateAnalyList.do",contentType:"json",params:e})}},{key:"getDmsDealerPurchaseSalesDetailInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/getDateAnalyListSellAllDes.do",contentType:"json",params:e})}},{key:"getDmsDealerPurchaseDetailInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/getDateAnalyListSellInDesc.do",contentType:"json",params:e})}},{key:"getDmsDealerSalesDetailInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/getDateAnalyListThroughDesc.do",contentType:"json",params:e})}},{key:"getDmsStoreScreenInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/qbrDmsSelectDatas.do",contentType:"json",data:e})}},{key:"getDmsStoreCategoryInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/qbrDmsPerformanceDatasByCategory.do",contentType:"json",data:e})}},{key:"getDmsStoreTotalInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/qbrDmsPerformanceDatasByWorkShopName.do",contentType:"json",data:e})}},{key:"getDmsProductScreenInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/qbrDmsSelectDatas.do",contentType:"json",data:e})}},{key:"getDmsProductAnnualInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/productsSaleInfo.do",contentType:"json",data:e})}},{key:"getDmsProductUnsalableInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/listOfUnmarketableProducts.do",contentType:"json",data:e})}},{key:"getDmsProductStockInfo",value:function(e){return Object(n["a"])({method:"post",path:"qbr/dms/inventoryWarning.do",contentType:"json",data:e})}}])}());t["a"]=new l}}]);