package com.chevron.b2b.model;

import java.util.Date;
import java.util.List;

/**
 * 计划-wx_t_schedule
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
public class Schedule {

	/**  ID  **/
    private Long id;

	/**  活动名称  **/
    private String scheduleName;

	/**  图标图片ID  **/
    private Long iconId;

	/**  横幅图片ID  **/
    private Long bannerId;

	/**  活动类型(activity-活动，task-任务)  **/
    private String scheduleType;

	/**  活动类型显示文本  **/
    private String scheduleTypeText;

	/**  状态(0-暂存，5-已发布)  **/
    private Integer status;

	/**  参与用户类型(Mechanic-技师，Owner-店长)  **/
    private String executeUserType;

	/**  奖励积分  **/
    private Double awardPoint;

	/**  后续步骤(二进制第一位1表示后续需要审批步骤)  **/
    private Integer leftSteps;

	/**  开始时间  **/
    private Date startTime;

	/**  截止时间  **/
    private Date endTime;

	/**  描述  **/
    private String scheduleDesc;

	/**  发布时间  **/
    private Date publishTime;

	/**  备注  **/
    private String remark;

	/**  销售渠道  **/
    private String salesChannel;

	/**  扩展属性1  **/
    private String extAttr1;

	/**  扩展属性2  **/
    private String extAttr2;

	/**  扩展属性3  **/
    private String extAttr3;

	/**  删除标记(1-删除，0-未删除)  **/
    private Integer deleteFlag;

	/**  创建用户ID  **/
    private Long createUserId;

	/**  创建时间  **/
    private Date createTime;

	/**  最后修改用户ID  **/
    private Long updateUserId;

	/**  最后修改时间  **/
    private Date updateTime;

	/**  活动时长  **/
    private Integer limitTime;

	/**  分享标记(1-分享,2-不分享)  **/
    private Integer shareFlag;

	/**  表单背景(分享活动有)  **/
    private Long formBg;

	/**  操作背景  **/
    private Long actionBg;

	/**  可变积分  **/
    private Integer fixedPointFlag;

	/**  最大积分(可变积分时有效)  **/
    private Double maxPoint;

	/**  关联任务ID  **/
    private Long jobId;
    
    private List<ScheduleWorkshop> assignedWorkshops;
    
    private List<ScheduleDistributor> assignedDistributors;
    
    private List<ScheduleItem> scheduleItems;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getScheduleName() {
        return scheduleName;
    }

    public void setScheduleName(String scheduleName) {
        this.scheduleName = scheduleName;
    }

    public Long getIconId() {
        return iconId;
    }

    public void setIconId(Long iconId) {
        this.iconId = iconId;
    }

    public Long getBannerId() {
        return bannerId;
    }

    public void setBannerId(Long bannerId) {
        this.bannerId = bannerId;
    }

    public String getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getScheduleTypeText() {
        return scheduleTypeText;
    }

    public void setScheduleTypeText(String scheduleTypeText) {
        this.scheduleTypeText = scheduleTypeText;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getExecuteUserType() {
        return executeUserType;
    }

    public void setExecuteUserType(String executeUserType) {
        this.executeUserType = executeUserType;
    }

    public Double getAwardPoint() {
        return awardPoint;
    }

    public void setAwardPoint(Double awardPoint) {
        this.awardPoint = awardPoint;
    }

    public Integer getLeftSteps() {
        return leftSteps;
    }

    public void setLeftSteps(Integer leftSteps) {
        this.leftSteps = leftSteps;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getScheduleDesc() {
        return scheduleDesc;
    }

    public void setScheduleDesc(String scheduleDesc) {
        this.scheduleDesc = scheduleDesc;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSalesChannel() {
        return salesChannel;
    }

    public void setSalesChannel(String salesChannel) {
        this.salesChannel = salesChannel;
    }

    public String getExtAttr1() {
        return extAttr1;
    }

    public void setExtAttr1(String extAttr1) {
        this.extAttr1 = extAttr1;
    }

    public String getExtAttr2() {
        return extAttr2;
    }

    public void setExtAttr2(String extAttr2) {
        this.extAttr2 = extAttr2;
    }

    public String getExtAttr3() {
        return extAttr3;
    }

    public void setExtAttr3(String extAttr3) {
        this.extAttr3 = extAttr3;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public List<ScheduleItem> getScheduleItems() {
		return scheduleItems;
	}

	public void setScheduleItems(List<ScheduleItem> scheduleItems) {
		this.scheduleItems = scheduleItems;
	}

	public Integer getLimitTime() {
		return limitTime;
	}

	public void setLimitTime(Integer limitTime) {
		this.limitTime = limitTime;
	}

	public Integer getShareFlag() {
		return shareFlag;
	}

	public void setShareFlag(Integer shareFlag) {
		this.shareFlag = shareFlag;
	}

	public Long getFormBg() {
		return formBg;
	}

	public void setFormBg(Long formBg) {
		this.formBg = formBg;
	}

	public Long getActionBg() {
		return actionBg;
	}

	public void setActionBg(Long actionBg) {
		this.actionBg = actionBg;
	}

	public Integer getFixedPointFlag() {
		return fixedPointFlag;
	}

	public void setFixedPointFlag(Integer fixedPointFlag) {
		this.fixedPointFlag = fixedPointFlag;
	}

	public Double getMaxPoint() {
		return maxPoint;
	}

	public void setMaxPoint(Double maxPoint) {
		this.maxPoint = maxPoint;
	}

	public Long getJobId() {
		return jobId;
	}

	public void setJobId(Long jobId) {
		this.jobId = jobId;
	}

	public List<ScheduleWorkshop> getAssignedWorkshops() {
		return assignedWorkshops;
	}

	public void setAssignedWorkshops(List<ScheduleWorkshop> assignedWorkshops) {
		this.assignedWorkshops = assignedWorkshops;
	}

	public List<ScheduleDistributor> getAssignedDistributors() {
		return assignedDistributors;
	}

	public void setAssignedDistributors(List<ScheduleDistributor> assignedDistributors) {
		this.assignedDistributors = assignedDistributors;
	}
}
