UPDATE  wx_t_user SET email='<EMAIL>' 
from wx_t_user u
WHERE email='<EMAIL>'
select *
--UPDATE m SET day_report_cc = '<EMAIL>' 
from wx_t_partner_responsible_main m
WHERE day_report_cc LIKE '%<EMAIL>%';

select *
--update t set dic_item_name='<EMAIL>' 
from wx_t_dic_item t where dic_item_name like '%<EMAIL>%'

select *
--update t set dic_item_desc='<EMAIL>' 
from wx_t_dic_item t where dic_item_desc like '%<EMAIL>%'

select *
--update t set dic_item_code='<EMAIL>' 
from wx_t_dic_item t where dic_item_code like '%<EMAIL>%'

select *
--update t set code='<EMAIL>' 
from wx_t_properties t where code like '%<EMAIL>%'

select *
--update t set codename='<EMAIL>' 
from wx_t_properties t where codename like '%<EMAIL>%'

select * 
--update u set u.mobile_tel='13996694185'
from wx_t_user u where mobile_tel='13512105976'
