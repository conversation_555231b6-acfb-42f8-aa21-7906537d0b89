package com.common.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
 
/**
 * 拼音工具类
 * 
 * <AUTHOR>
 */
public class PinYinUtil {

	/**
     * 将字符串中的中文转化为拼音,其他字符不变
     * 
     * @param inputString
     * @return
     */
    public static String getPingYin(String inputString) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
 
        char[] input = inputString.trim().toCharArray();
        String output = "";
 
        try {
            for (int i = 0; i < input.length; i++) {
                if (java.lang.Character.toString(input[i]).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(input[i], format);
                    output += temp[0];
                } else
                    output += java.lang.Character.toString(input[i]);
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }
        return output;
    }
    
    /**
     * 获取第一个汉字的首字母
     * @param inputString
     * @return
     */
    public static String getFirstLetter(String inputString) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
 
        char[] input = inputString.trim().toCharArray();
        String output = "";
        try {
           
                if (java.lang.Character.toString(input[0]).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(input[0], format);
                    output += temp[0];
                } else
                    output += java.lang.Character.toString(input[0]);
           
        } catch (Exception e) {
           // e.printStackTrace();
        	return inputString;//针对特殊字符不能转拼音的
        }
        return output.substring(0, 1);
    }
    
    /**  
     * 获取每个汉字的拼音首字母，英文字符不变  
     * @param chinese 汉字串  
     * @return 汉语拼音首字母  
     */  
    public static String getFirstSpell(String chinese) {   
            StringBuffer pybf = new StringBuffer();   
            try {  
	            char[] arr = chinese.toCharArray();   
	            HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();   
	            defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);   
	            defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);   
	            for (int i = 0; i < arr.length; i++) {   
	                    if (arr[i] > 128) {   
	                            
	                                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat);   
	                                    if (temp != null) {   
	                                            pybf.append(temp[0].charAt(0));   
	                                    }   
	                            
	                    } else {   
	                            pybf.append(arr[i]);   
	                    }   
	            }   
             } catch (Exception e) {   
                //e.printStackTrace();  
            	 return chinese;//针对特殊字符不能转拼音的
             }   
            return pybf.toString().replaceAll("\\W", "").trim();   
    }   
    /**  
     * 获取汉字串拼音，英文字符不变  
     * @param chinese 汉字串  
     * @return 汉语拼音  
     */  
    public static String getFullSpell(String chinese) {   
            StringBuffer pybf = new StringBuffer(); 
            try {   
	            char[] arr = chinese.toCharArray();   
	            HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();   
	            defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);   
	            defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);   
	            for (int i = 0; i < arr.length; i++) {   
	                    if (arr[i] > 128) {   
	                            pybf.append(PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat)[0]);   
	                    } else {   
	                            pybf.append(arr[i]);   
	                    }   
	            }  
            } catch (Exception e) {  
            	//e.printStackTrace();   
            	return chinese;//针对特殊字符不能转拼音的 by bo.liu 0901
            	
            }
            return pybf.toString();   
    }  
    
}  