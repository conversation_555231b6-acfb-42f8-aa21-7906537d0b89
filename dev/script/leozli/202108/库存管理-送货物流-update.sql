--物流商-LogisticsProvider 

update wx_t_role set user_flag=user_flag|512 where ch_role_name in ('LogisticsProvider')

insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Caltex_Dealer'), (select r.role_id from wx_t_role r where r.ch_role_name='LogisticsProvider'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='CDM_DIST'), (select r.role_id from wx_t_role r where r.ch_role_name='LogisticsProvider'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Service_Partner_Admin'), (select r.role_id from wx_t_role r where r.ch_role_name='LogisticsProvider'), 1, getdate());
insert into wx_t_role_manage_role (f_manager_role_id, f_manage_role_id,f_create_user_id,f_create_time) values ((select r.role_id from wx_t_role r where r.ch_role_name='Service_Partner_Manager'), (select r.role_id from wx_t_role r where r.ch_role_name='LogisticsProvider'), 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='LogisticsProvider'), 'App', 256, 1, 'APP权限。256-经销商物流商', 1, getdate());
insert into wx_t_resource_sql (sqlid, resource_id,creation_time,created_by) values ('com.sys.auth.dao.WxTUserMapper.seletPartnerUsersForCtrlPage-distributor-inventory',(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='库存管理' and m.module_name='订单管理'),getdate(),1)
insert into wx_t_subject_res_permission (subject_type, subject_id, resource_id,permission_type_id,creation_time,created_by) values ('ROLE',(select r.role_id from wx_t_role r where r.ch_role_name='LogisticsProvider'),(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='门店订单' and m.module_name='采购订单'),(select permission_type_id from wx_t_res_permission_type where permission_type_desc='合伙人下所有门店' and resource_type_code='WS'),getdate(),1)

update o set o.status='6'
from wx_t_order o where o.status='0' and o.source='OUT_STOCK'

update os set os.out_time=os.create_time
from wx_t_out_stock os
left JOIN wx_t_order wto ON wto.order_no = os.order_no AND os.order_type = 'workshop' 
where wto.source='OUT_STOCK' and os.status='2' and os.out_time is null

update wto set wto.ext_property7=wto.creator
from wx_t_out_stock os
left JOIN wx_t_order wto ON wto.order_no = os.order_no AND os.order_type = 'workshop' 
where wto.source='OUT_STOCK' and (wto.ext_property7 is null or len(wto.ext_property7)=0)

update w set w.business_weight=1
 from wx_t_work_shop w where w.customer_type&48>0 and w.business_weight=0