package com.sys.report.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.common.util.FileUtil;
import com.common.util.StringUtils;
import com.sys.report.service.impl.ReportService;

/**
 * report下载
 * 
 * <AUTHOR>
 * @create June 29, 2015
 */
@Controller
public class ReportController {
	
	/*@Resource
	private ReportService reportService;
	
	@RequestMapping(value = "/downloadExportReport.do")
	public void downloadExportReport(@RequestParam("fileName") String fileName,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		if(!StringUtils.isNull(fileName)){
			String filePath = ReportService.reportFilePath;
			String filePathName =filePath+fileName;
			FileUtil.download(response, "5", filePathName, fileName);
			//FileUtil.deleteFile(filePath);
		}		
		
	}
	
	
	@RequestMapping(value = "/exportAllReport.do")
	public void exportAllReport(@RequestParam("taskMainIdStr") String taskMainIdStr,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		Map map =(Map)reportService.generateAllReport(taskMainIdStr);
		String returnCode = (String)map.get("code");
		if (returnCode.equals("success")) {
			String filePath = (String) map.get("resultFilePath");
			String fileName = (String) map.get("resultFileName");
			FileUtil.download(response, "4", filePath, fileName);
			FileUtil.deleteFile(filePath);
		}

	}	
	@RequestMapping(value = "/downloadallExportReport.do")
	public void downloadallExportReport(@RequestParam("taskMainIdStr") String taskMainIdStr,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		Map map =(Map)reportService.generateAllReport(taskMainIdStr);
		String returnCode = (String)map.get("code");
		if (returnCode.equals("success")) {
			String filePath = (String) map.get("resultFilePath");
			String fileName = (String) map.get("resultFileName");
			FileUtil.download(response, "4", filePath, fileName);
			FileUtil.deleteFile(filePath);
		}

	}*/
	
}
