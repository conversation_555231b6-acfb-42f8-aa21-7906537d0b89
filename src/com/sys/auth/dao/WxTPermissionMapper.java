package com.sys.auth.dao;

import com.sys.auth.model.WxTPermission;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WxTPermissionMapper {

    List<WxTPermission> selectPermissionById(@Param("permissionId") Long permissionId);
    List<WxTPermission> selectAllPermission();

    List<WxTPermission> selectPermissionByOwnerId(@Param("ownerList") List<Long> ownerList);

    @MapKey("permissionKey")
    List<Map<String, String>> selectPermissionKeyByOwnerId(@Param("ownerList") List<Long> ownerList);
}
