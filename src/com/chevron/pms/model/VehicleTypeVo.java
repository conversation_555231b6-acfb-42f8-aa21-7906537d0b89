package com.chevron.pms.model;

public class VehicleTypeVo {
  
	private String remark;//备注

    private String levelId;//

    private String factory;//厂家

    private String brand;//品牌

    private String vehicleSeries;//车系

    private String vehicleType;//车型

    private String salesName;//销售名称

    private Integer modelYear;//年款

    private String emissionStandard;//排放标准

    private String vehicleCategory;//车辆类型

    private Integer salesYear;//上市年份

    private Integer salesMonth;//上市月份

    private Integer productYear;//生产年份

    private Integer stopProductionYear;//停产年份

    private String stopProductionStatus;//停产状态

    private String country;//国别

    private String vehicleKinds;//国产合资

    private String cylinderVolume;

    private String displacement;

    private String airIntakeForm;

    private String fuelType;

    private String fuelLabel;

    private String maximumHorsepower;

    private String maximumPower;

    private String cylinderArrangement;

    private String numberOfCylinders;

    private String valvesPerCylinder;

    private String gearboxType;

    private String gearboxDesc;

    private String numberOfGears;

    private String frontBrakeType;

    private String backBrakeType;

    private String assistType;

    private String enginePosition;

    private String drivingMode;

    private String wheelBase;

    private String doorNumber;

    private String numberOfSeats;

    private String frontTireSize;

    private String backTireSize;

    private String frontBossOfWheelSize;

    private String backBossOfWheelSize;

    private String wheelMaterial;

    private String spareTireSpecifications;

    private String electricSkylight;

    private String panoramicSkylight;

    private String xenonLamp;

    private String frontFogLamp;

    private String rearWiper;

    private String airConditioner;

    private String autoAirConditioning;
    
  
    
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLevelId() {
        return levelId;
    }

    public void setLevelId(String levelId) {
        this.levelId = levelId == null ? null : levelId.trim();
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory == null ? null : factory.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getVehicleSeries() {
        return vehicleSeries;
    }

    public void setVehicleSeries(String vehicleSeries) {
        this.vehicleSeries = vehicleSeries == null ? null : vehicleSeries.trim();
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType == null ? null : vehicleType.trim();
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName == null ? null : salesName.trim();
    }

    public Integer getModelYear() {
        return modelYear;
    }

    public void setModelYear(Integer modelYear) {
        this.modelYear = modelYear;
    }

    public String getEmissionStandard() {
        return emissionStandard;
    }

    public void setEmissionStandard(String emissionStandard) {
        this.emissionStandard = emissionStandard == null ? null : emissionStandard.trim();
    }

    public String getVehicleCategory() {
        return vehicleCategory;
    }

    public void setVehicleCategory(String vehicleCategory) {
        this.vehicleCategory = vehicleCategory == null ? null : vehicleCategory.trim();
    }

    public Integer getSalesYear() {
        return salesYear;
    }

    public void setSalesYear(Integer salesYear) {
        this.salesYear = salesYear;
    }

    public Integer getSalesMonth() {
        return salesMonth;
    }

    public void setSalesMonth(Integer salesMonth) {
        this.salesMonth = salesMonth;
    }

    public Integer getProductYear() {
        return productYear;
    }

    public void setProductYear(Integer productYear) {
        this.productYear = productYear;
    }

    public Integer getStopProductionYear() {
        return stopProductionYear;
    }

    public void setStopProductionYear(Integer stopProductionYear) {
        this.stopProductionYear = stopProductionYear;
    }

    public String getStopProductionStatus() {
        return stopProductionStatus;
    }

    public void setStopProductionStatus(String stopProductionStatus) {
        this.stopProductionStatus = stopProductionStatus == null ? null : stopProductionStatus.trim();
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country == null ? null : country.trim();
    }

    public String getVehicleKinds() {
        return vehicleKinds;
    }

    public void setVehicleKinds(String vehicleKinds) {
        this.vehicleKinds = vehicleKinds == null ? null : vehicleKinds.trim();
    }

    public String getCylinderVolume() {
        return cylinderVolume;
    }

    public void setCylinderVolume(String cylinderVolume) {
        this.cylinderVolume = cylinderVolume == null ? null : cylinderVolume.trim();
    }

    public String getDisplacement() {
        return displacement;
    }

    public void setDisplacement(String displacement) {
        this.displacement = displacement == null ? null : displacement.trim();
    }

    public String getAirIntakeForm() {
        return airIntakeForm;
    }

    public void setAirIntakeForm(String airIntakeForm) {
        this.airIntakeForm = airIntakeForm == null ? null : airIntakeForm.trim();
    }

    public String getFuelType() {
        return fuelType;
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType == null ? null : fuelType.trim();
    }

    public String getFuelLabel() {
        return fuelLabel;
    }

    public void setFuelLabel(String fuelLabel) {
        this.fuelLabel = fuelLabel == null ? null : fuelLabel.trim();
    }

    public String getMaximumHorsepower() {
        return maximumHorsepower;
    }

    public void setMaximumHorsepower(String maximumHorsepower) {
        this.maximumHorsepower = maximumHorsepower == null ? null : maximumHorsepower.trim();
    }

    public String getMaximumPower() {
        return maximumPower;
    }

    public void setMaximumPower(String maximumPower) {
        this.maximumPower = maximumPower == null ? null : maximumPower.trim();
    }

    public String getCylinderArrangement() {
        return cylinderArrangement;
    }

    public void setCylinderArrangement(String cylinderArrangement) {
        this.cylinderArrangement = cylinderArrangement == null ? null : cylinderArrangement.trim();
    }

    public String getNumberOfCylinders() {
        return numberOfCylinders;
    }

    public void setNumberOfCylinders(String numberOfCylinders) {
        this.numberOfCylinders = numberOfCylinders == null ? null : numberOfCylinders.trim();
    }

    public String getValvesPerCylinder() {
        return valvesPerCylinder;
    }

    public void setValvesPerCylinder(String valvesPerCylinder) {
        this.valvesPerCylinder = valvesPerCylinder == null ? null : valvesPerCylinder.trim();
    }

    public String getGearboxType() {
        return gearboxType;
    }

    public void setGearboxType(String gearboxType) {
        this.gearboxType = gearboxType == null ? null : gearboxType.trim();
    }

    public String getGearboxDesc() {
        return gearboxDesc;
    }

    public void setGearboxDesc(String gearboxDesc) {
        this.gearboxDesc = gearboxDesc == null ? null : gearboxDesc.trim();
    }

    public String getNumberOfGears() {
        return numberOfGears;
    }

    public void setNumberOfGears(String numberOfGears) {
        this.numberOfGears = numberOfGears == null ? null : numberOfGears.trim();
    }

    public String getFrontBrakeType() {
        return frontBrakeType;
    }

    public void setFrontBrakeType(String frontBrakeType) {
        this.frontBrakeType = frontBrakeType == null ? null : frontBrakeType.trim();
    }

    public String getBackBrakeType() {
        return backBrakeType;
    }

    public void setBackBrakeType(String backBrakeType) {
        this.backBrakeType = backBrakeType == null ? null : backBrakeType.trim();
    }

    public String getAssistType() {
        return assistType;
    }

    public void setAssistType(String assistType) {
        this.assistType = assistType == null ? null : assistType.trim();
    }

    public String getEnginePosition() {
        return enginePosition;
    }

    public void setEnginePosition(String enginePosition) {
        this.enginePosition = enginePosition == null ? null : enginePosition.trim();
    }

    public String getDrivingMode() {
        return drivingMode;
    }

    public void setDrivingMode(String drivingMode) {
        this.drivingMode = drivingMode == null ? null : drivingMode.trim();
    }

    public String getWheelBase() {
        return wheelBase;
    }

    public void setWheelBase(String wheelBase) {
        this.wheelBase = wheelBase == null ? null : wheelBase.trim();
    }

    public String getDoorNumber() {
        return doorNumber;
    }

    public void setDoorNumber(String doorNumber) {
        this.doorNumber = doorNumber == null ? null : doorNumber.trim();
    }

    public String getNumberOfSeats() {
        return numberOfSeats;
    }

    public void setNumberOfSeats(String numberOfSeats) {
        this.numberOfSeats = numberOfSeats == null ? null : numberOfSeats.trim();
    }

    public String getFrontTireSize() {
        return frontTireSize;
    }

    public void setFrontTireSize(String frontTireSize) {
        this.frontTireSize = frontTireSize == null ? null : frontTireSize.trim();
    }

    public String getBackTireSize() {
        return backTireSize;
    }

    public void setBackTireSize(String backTireSize) {
        this.backTireSize = backTireSize == null ? null : backTireSize.trim();
    }

    public String getFrontBossOfWheelSize() {
        return frontBossOfWheelSize;
    }

    public void setFrontBossOfWheelSize(String frontBossOfWheelSize) {
        this.frontBossOfWheelSize = frontBossOfWheelSize == null ? null : frontBossOfWheelSize.trim();
    }

    public String getBackBossOfWheelSize() {
        return backBossOfWheelSize;
    }

    public void setBackBossOfWheelSize(String backBossOfWheelSize) {
        this.backBossOfWheelSize = backBossOfWheelSize == null ? null : backBossOfWheelSize.trim();
    }

    public String getWheelMaterial() {
        return wheelMaterial;
    }

    public void setWheelMaterial(String wheelMaterial) {
        this.wheelMaterial = wheelMaterial == null ? null : wheelMaterial.trim();
    }

    public String getSpareTireSpecifications() {
        return spareTireSpecifications;
    }

    public void setSpareTireSpecifications(String spareTireSpecifications) {
        this.spareTireSpecifications = spareTireSpecifications == null ? null : spareTireSpecifications.trim();
    }

    public String getElectricSkylight() {
        return electricSkylight;
    }

    public void setElectricSkylight(String electricSkylight) {
        this.electricSkylight = electricSkylight == null ? null : electricSkylight.trim();
    }

    public String getPanoramicSkylight() {
        return panoramicSkylight;
    }

    public void setPanoramicSkylight(String panoramicSkylight) {
        this.panoramicSkylight = panoramicSkylight == null ? null : panoramicSkylight.trim();
    }

    public String getXenonLamp() {
        return xenonLamp;
    }

    public void setXenonLamp(String xenonLamp) {
        this.xenonLamp = xenonLamp == null ? null : xenonLamp.trim();
    }

    public String getFrontFogLamp() {
        return frontFogLamp;
    }

    public void setFrontFogLamp(String frontFogLamp) {
        this.frontFogLamp = frontFogLamp == null ? null : frontFogLamp.trim();
    }

    public String getRearWiper() {
        return rearWiper;
    }

    public void setRearWiper(String rearWiper) {
        this.rearWiper = rearWiper == null ? null : rearWiper.trim();
    }

    public String getAirConditioner() {
        return airConditioner;
    }

    public void setAirConditioner(String airConditioner) {
        this.airConditioner = airConditioner == null ? null : airConditioner.trim();
    }

    public String getAutoAirConditioning() {
        return autoAirConditioning;
    }

    public void setAutoAirConditioning(String autoAirConditioning) {
        this.autoAirConditioning = autoAirConditioning == null ? null : autoAirConditioning.trim();
    }

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Override
	public String toString() {
		return "VehicleTypeVo [remark=" + remark + ", levelId=" + levelId + ", factory=" + factory + ", brand=" + brand
				+ ", vehicleSeries=" + vehicleSeries + ", vehicleType=" + vehicleType + ", salesName=" + salesName
				+ ", modelYear=" + modelYear + ", emissionStandard=" + emissionStandard + ", vehicleCategory="
				+ vehicleCategory + ", salesYear=" + salesYear + ", salesMonth=" + salesMonth + ", productYear="
				+ productYear + ", stopProductionYear=" + stopProductionYear + ", stopProductionStatus="
				+ stopProductionStatus + ", country=" + country + ", vehicleKinds=" + vehicleKinds + ", cylinderVolume="
				+ cylinderVolume + ", displacement=" + displacement + ", airIntakeForm=" + airIntakeForm + ", fuelType="
				+ fuelType + ", fuelLabel=" + fuelLabel + ", maximumHorsepower=" + maximumHorsepower + ", maximumPower="
				+ maximumPower + ", cylinderArrangement=" + cylinderArrangement + ", numberOfCylinders="
				+ numberOfCylinders + ", valvesPerCylinder=" + valvesPerCylinder + ", gearboxType=" + gearboxType
				+ ", gearboxDesc=" + gearboxDesc + ", numberOfGears=" + numberOfGears + ", frontBrakeType="
				+ frontBrakeType + ", backBrakeType=" + backBrakeType + ", assistType=" + assistType
				+ ", enginePosition=" + enginePosition + ", drivingMode=" + drivingMode + ", wheelBase=" + wheelBase
				+ ", doorNumber=" + doorNumber + ", numberOfSeats=" + numberOfSeats + ", frontTireSize=" + frontTireSize
				+ ", backTireSize=" + backTireSize + ", frontBossOfWheelSize=" + frontBossOfWheelSize
				+ ", backBossOfWheelSize=" + backBossOfWheelSize + ", wheelMaterial=" + wheelMaterial
				+ ", spareTireSpecifications=" + spareTireSpecifications + ", electricSkylight=" + electricSkylight
				+ ", panoramicSkylight=" + panoramicSkylight + ", xenonLamp=" + xenonLamp + ", frontFogLamp="
				+ frontFogLamp + ", rearWiper=" + rearWiper + ", airConditioner=" + airConditioner
				+ ", autoAirConditioning=" + autoAirConditioning + ", id=" + id + "]";
	}


    
    
}