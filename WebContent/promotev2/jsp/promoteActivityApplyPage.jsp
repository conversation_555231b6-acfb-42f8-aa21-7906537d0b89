<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.sys.organization.dao.OrganizationVoMapper"%>
<% String promoteUserType=request.getParameter("promoteUserType");%>
<% String pageType=request.getParameter("pageType");%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <% if(pageType!=null&&pageType.equals("apply")){ %>
    <title>市场资源活动申请New</title>
    <%} else if(pageType!=null&&pageType.equals("feedback")){%>
    <title>市场资源活动反馈</title>
    <%} else if(pageType!=null&&pageType.equals("approve")){%>
    <title>市场资源活动审批New</title>
    <% }%>
    <%@include file="/common/jsp/common.jsp"%>
    <link rel="stylesheet" href="/common/css/ListInfo.css">
<!--     <link href="/promote/css/createPromotePlanPage.css" rel="stylesheet"> -->
    <style>
    	#pacNum{
    		display:none;
    	}
    	#pacNum header{
    		font-size:16px;
    		margin-left:10px;
    	}
    	#pacNum .box{
    		border: 1px solid #ccc;
		    padding: 0px;
		    margin-top: 10px;
    	}
        .box-info {
            padding: 5px 0px;
            margin:3px 10px;
            height: 53px;
            
        }
    	.yth-box-info {
        	padding: 0px 0px;
            margin:0px 10px;
            height: 45px;
        }
        .box-body {
            margin-top: 5px;
            padding-left: 20px;
        }
        .box-header {
            color: #666;
            font-size: 12px;
            font-weight: 600;
            padding-left: 20px;
        }
    
        .progress-wrapper {
            display: inline-block;
            width: 80%;
            vertical-align: bottom;
            margin-top: 0px;
        }
    
        .total_amount {
            font-size: 14px;
            font-weight: 600;
            color: #777;
        }
        .cols-5 > .field-group {
        	width: 19.7%;
        	display: inline-block;
        }
        .bui-grid-table .bui-grid-hd-title {
        	line-height: 12px;
        }
    </style>
<script type="text/javascript">
    var promoteUserType='<%=promoteUserType==null?"null":promoteUserType%>';
    var pageType='<%=pageType==null?"null":pageType%>';
</script>

    <script src="/promotev2/js/promoteActivityApplyPage.js?v=${version}" type="text/javascript"></script>
</head>
<body class="gray-bg">
    <div class="content-wrapper">
        <%-- <div class="content-panel">
            <div class="flow-steps" style="text-align: center;">
                <ol class="num4" style="display: inline-block;">
                <% if(pageType!=null&&pageType.equals("apply")){ %>
                    <c:set var="step" value="${1}" />
                <%} else {%>
                    <c:set var="step" value="${2}" />
                <%} %>
					<li class='first ${step eq 1 ? "current" : (step eq 2 ? " current-prev" : "")}'>1.市场资源活动申请</li>
					<li class='${step eq 2 ? "current" : (step eq 3 ? "current-prev" : "")}' style="margin-left:-4px;">2.审批</li>
					<li class='last ${step eq 3 ? "current" : (step eq 4 ? "current-prev" : "")}' style="margin-left:-4px;">3.市场资源活动反馈</li>
				</ol>
            </div>
        </div> --%>
        <div class="content-panel header-panel">
            <% if(pageType!=null&&pageType.equals("apply")){ %>
            <div class="header-title">市场资源活动申请</div>
            <%} else if(pageType!=null&&pageType.equals("approve")){%>
            <div class="header-title">市场资源活动审批</div>
            <%} else if(pageType!=null&&pageType.equals("feedback")){%>
            <div class="header-title">市场资源活动反馈</div>
            <%}%>
            <!-- <div class="header-btns">
                <button class="btn-cancel" onclick="save()">暂存</button>
                <button class="btn-submit" onclick="submit()">提交</button>
                 <button class="btn-back" onclick="window.history.go(-1);">返回</button>
            </div> -->
        </div>
        <div class="info-panel cols-5" id="pacNum">
            <div class="newshop field-group" style="width:20%!important">
                <div class="box">
                    <header>德乐新店开业</header>
                    <div class="box-info newshop" id="rdlb">
                        <div class="box-header">
                            新店礼包
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div>
            <!--         <div class="box-info newshop" id="gdlb">
                        <div class="box-header">
                            新店礼包(优质)
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="roadshow field-group"  style="width:20%!important">
                <div class="box">
                    <header>德乐路演</header>
                    <div class="box-info roadshow" id="lywlb">
                        <div class="box-header">
                            路演物料包
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div>
          <!--           <div class="box-info roadshow" id="xfzhdbcb">
                        <div class="box-header">
                            消费者互动补充包
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="seminar field-group"  style="width:59%!important">
                <div class="box">
                    <header>研讨会</header>
                    <div class="box-info seminar" id="ptythlb" style="width:33%!important;float:left">
                        <div class="box-header">
                             研讨会(普通)
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div>
                    <div class="box-info seminar" id="yzythlb" style="width:33%!important;float:left">
                        <div class="box-header">
                            研讨会(优质)
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div>
                            <div class="box-info seminar" id="cdythlb" style="width:33%!important">
                        <div class="box-header">
                          车队研讨会礼包
                        </div>
                        <div class="box-body">
                            <div class="progress-wrapper">
                                <div class="progress" style="margin-bottom:0px">
                                    <div style="width: 0%;" class="bar">可用0</div>
                                </div>
                            </div>
                            <label class="total_amount">0</label>
                        </div>
                    </div>
                    
                </div>
            </div>
           
        </div>
        <div class="content-panel" id="tabs"></div>
        <% if(pageType!=null&&pageType.equals("apply")){%>
        <div class="content-panel tools-panel">
            <button type="button" class="btn-submit" onclick='window.location.href="/promotev2/jsp/save/promoteActivitySave.jsp";'>新建</button>
        </div>
        <%}%>
        <div id="tab-content-wrapper">
            <div data-type="daichuli">
                <div class="content-panel query-panel">
                    <div class="field-group key-search">
                        <label class="field-label width-auto">搜索：</label>
                        <div class="control-group">
                            <input type="text" data-name="keyword" value="" placeholder="请输入关键字" class="control-text">
                        </div>
                    </div>
                    <div class="query-btns">
                        <div class="query-btn field-label">
                            <button class="btn-query" data-type="daichuli" onclick="query('daichuli');">查询</button>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <ul>
                        <li>
                            <div class="row-fluid list-header-panel list-header">
                                <div class="span1">序号</div>
                                <div class="span2">活动类型</div>
                                <div class="span5">标题</div>
                                <div class="span5">申请经销商</div>
                                <div class="span2">状态</div>
                                <div class="span4">活动时间</div>
                                <div class="span2">创建时间</div>
                                <div class="list-info-toobar span3">操作</div>
                            </div>
                        </li>
                    </ul>
                    <ul class="applyList">
                    </ul>
                </div>
            </div>
            <div data-type="daishenpi">
                <div class="content-panel query-panel">
                    <div class="field-group key-search">
                        <label class="field-label width-auto">搜索：</label>
                        <div class="control-group">
                            <input type="text" data-name="keyword" value="" placeholder="请输入关键字" class="control-text">
                        </div>
                    </div>
                    <div class="query-btns">
                        <div class="query-btn field-label">
                            <button class="btn-query" data-type="daishenpi" onclick="query('daishenpi');">查询</button>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <ul>
                        <li>
                            <div class="row-fluid list-header-panel list-header">
                                    <div class="span1">序号</div>
                                    <div class="span2">活动类型</div>
                                    <div class="span5">标题</div>
	                                <div class="span5">申请经销商</div>
	                                <div class="span2">状态</div>
	                                <div class="span4">活动时间</div>
	                                <div class="span2">创建时间</div>
                                <div class="list-info-toobar span3">操作</div>
                            </div>
                        </li>
                    </ul>
                    <ul class="applyList">
                    </ul>
                </div>
            </div>
            <div data-type="yishenpi">
                <div class="content-panel query-panel">
                    <div class="field-group key-search">
                        <label class="field-label width-auto">搜索：</label>
                        <div class="control-group">
                            <input type="text" data-name="keyword" value="" placeholder="请输入关键字" class="control-text">
                        </div>
                    </div>
                    <div class="query-btns">
                        <div class="query-btn field-label">
                            <button class="btn-query" data-type="yishenpi" onclick="query('yishenpi');">查询</button>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <ul>
                        <li>
                            <div class="row-fluid list-header-panel list-header">
                                <div class="span1">序号</div>
                                <div class="span2">活动类型</div>
                                <div class="span5">标题</div>
                                <div class="span5">申请经销商</div>
                                <div class="span2">状态</div>
                                <div class="span4">活动时间</div>
                                 <div class="span2">创建时间</div>
                                <div class="list-info-toobar span3">操作</div>
                            </div>
                        </li>
                    </ul>
                    <ul class="applyList">
                    </ul>
                </div>
            </div>
        </div>       
    </div>
    <div class="hide" id="approveDialog">
        <div class="content-panel">
            <div class="field-group">
                <label class="field-label width-auto">理由：</label>
                <div class="control-group">
                    <textarea placeholder="请输入理由" id="approveReason" class="control-text" style="width:350px;height:100px;"></textarea>
                </div>
            </div>
        </div>
    </div>
</body>
</html>