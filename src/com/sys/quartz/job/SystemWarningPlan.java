package com.sys.quartz.job;

import com.sys.log.util.LogUtils;
import com.sys.utils.business.WarnConfigBizService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class SystemWarningPlan {

    @Autowired
    private WarnConfigBizService warnConfigBizService;

    private static Logger log = LoggerFactory.getLogger(SystemWarningPlan.class);

    public synchronized void execute() {
        log.info("SystemWarningPlan job start....");
        LogUtils.addInfoLog(1l, "SystemWarningPlan.excute", "开始执行系统告警：" + new Date());
        try {
            warnConfigBizService.excuteWarnningTask(null);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.addErrorLog(1l, "SystemWarningPlan.excute", "系统告警检查失败。" + e.getMessage(), null);
        }
        log.info("SystemWarningPlan job finish....");
        LogUtils.addInfoLog(1l, "SystemWarningPlan.excute", "系统告警检查结束：" + new Date());
    }
}
