select o.organization_name Org, u.ch_name Name, u.login_name "Login Name", 
(select substring((select ','+r.role_descript from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000))"Role", pm.menu_name + '/' + m.menu_name "Function List"  from wx_t_user u 
inner join wx_t_organization o on o.id=u.org_id
left join wx_t_menu m on m.status=1 and (exists (select 1 from 
		wx_t_userbase um where um.rs_type='2' and um.user_id=u.user_id and um.source_id=m.menu_id)
	or exists (select 1 from wx_t_userrole ur left join wx_t_rolesource rs on ur.role_id=rs.role_id where ur.user_id=u.user_id
		and rs.rs_type='2' and rs.source_id=m.menu_id))
and (o.type!=1/*非合伙人用户*/ or
		not exists (select 1 from wx_t_business_fun_resource bfr1 join wx_t_business_fun bf1 on bfr1.business_fun_code=bf1.business_fun_code and bf1.enable_flag=1 where bfr1.resource_type=2 and bfr1.resource_id=m.menu_id) /*非合伙人业务菜单*/ or
		exists (select 1 from wx_t_dealer_business_fun dbf1
		join wx_t_business_fun bf1 on bf1.business_fun_code=dbf1.business_fun_code and bf1.enable_flag=1
		join wx_t_business_fun_resource bfr1 on bfr1.business_fun_code=bf1.business_fun_code
		where dbf1.dealer_id=o.id
		and bfr1.resource_type=2 and bfr1.resource_id=m.menu_id and (bf1.business_fun_type is null or bf1.business_fun_type!='CUSTOM' 
		 or exists (select 1 from wx_t_dealer_business_custom dbc1 where dbc1.id=dbf1.business_custom_id and dbc1.enable_flag=1 
		 and (dbc1.start_date is null or dbc1.start_date<=getdate()) and (dbc1.end_date is null or dbc1.end_date >=getdate())))))
left join wx_t_menu pm on m.menu_pid=pm.menu_id
where u.status=1 and (u.type is null or u.type!='1')
and pm.menu_id is not null and u.user_id != 1 and o.type in (0, 1)
order by o.type,o.id, u.user_id, pm.sort, m.sort

select u.ch_name "用户名", u.login_name "登陆名", u.email "邮箱", o.organization_name "所属机构",
(select top 1 l.xg_sj from wx_t_log l where l.user_id=u.user_id order by l.log_id desc) "最近登录时间", 
(select substring((select ','+r.role_descript from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000))"已分配角色" 
 from wx_t_user u 
inner join wx_t_organization o on o.id=u.org_id
where u.status=1 --and (u.type is null or u.type!='1')
and u.user_id != 1 and o.type in (0, 1,3)
order by o.type,o.id, u.user_id

select u.ch_name "用户名", u.login_name "登陆名", u.email "邮箱", u.mobile_tel "手机号", o.organization_name "所属机构",
(select top 1 l.xg_sj from wx_t_log l where l.user_id=u.user_id order by l.log_id desc) "最近登录时间", 
(select substring((select ','+r.role_descript from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000))"已分配角色" , u.allow_login
 from wx_t_user u 
inner join wx_t_organization o on o.id=u.org_id
where u.status=1 --and (u.type is null or u.type!='1')
and u.user_id != 1 and o.type in (0, 1,3)
order by o.type,o.id, u.user_id

select u.ch_name "User Account", u.login_name "User Name", o.organization_name "Department", u.email "Email", 
(select substring((select ','+r.role_descript from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000))"Role Assigned" ,
(select top 1 l.xg_sj from wx_t_log l where l.user_id=u.user_id order by l.log_id desc) "Last Login"
 from wx_t_user u 
inner join wx_t_organization o on o.id=u.org_id
where u.status=1 --and (u.type is null or u.type!='1')
and u.user_id != 1 and o.type in (0, 1,3)
order by o.type,o.id, u.user_id