INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'经销商负责FLSR(全量关系)', N'exists (select 1 from dw_customer_org_sales xx_crss001
 where xx_crss001.distributor_id=$distributorId$ and xx_crss001.sales_cai=$table.user$.cai and (case when xx_crss001.product_channel=''Consumer'' then 1 when xx_crss001.product_channel=''Commercial'' then 2 else 12 end) & $channelWeight$ > 0)
', CAST(2.1500000000 AS Decimal(20, 10)), N'', N'必须重写getChannelWeightForTodoUser提供单据的channel weight信息。Direct-12', 1, 0, 1, CAST(N'2020-12-29T14:06:36.283' AS DateTime), 1, CAST(N'2020-12-29T15:07:07.107' AS DateTime))
INSERT [dbo].[wx_t_workflow_pre_exe_group] ([group_name], [condition_sql], [sort_numb], [group_desc], [remark], [enable_flag], [delete_flag], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'经销商负责ASM(全量关系)', N'exists (select 1 from dw_customer_org_sales xx_crss001
 where xx_crss001.distributor_id=$distributorId$ and xx_crss001.supervisor_cai=$table.user$.cai and (case when xx_crss001.product_channel=''Consumer'' then 1 when xx_crss001.product_channel=''Commercial'' then 2 else 12 end) & $channelWeight$ > 0)
', CAST(2.2500000000 AS Decimal(20, 10)), N'', N' 必须重写getChannelWeightForTodoUser提供单据的channel weight信息。Direct-12', 1, 0, 1, CAST(N'2020-12-29T14:16:22.687' AS DateTime), 1, CAST(N'2020-12-29T15:07:21.530' AS DateTime))


update el set el.source_key=(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责ASM(全量关系)'),el.remark='经销商负责ASM(全量关系)' 
from wx_t_workflow_exe_list el left join wx_t_workflow_step ws on el.step_id=ws.step_id 
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP' 
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER' where ws.workflow_code='NON_LOCK_MKT' 
and eg.group_name='经销商负责ASM' and ws.step_code='FROM_ASM_SUBMIT_SURVEY'

update el set el.source_key=(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责ASM(全量关系)'),el.remark='经销商负责ASM(全量关系)' 
from wx_t_workflow_exe_list el left join wx_t_workflow_step ws on el.step_id=ws.step_id 
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP' 
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER' where ws.workflow_code='NON_LOCK_MKT' 
and eg.group_name='经销商负责ASM' and ws.step_code='TO_ASM_SUBMIT_FEEDBACK'

update el set el.source_key=(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责ASM(全量关系)'),el.remark='经销商负责ASM(全量关系)' 
from wx_t_workflow_exe_list el left join wx_t_workflow_step ws on el.step_id=ws.step_id 
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP' 
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER' where ws.workflow_code='NON_LOCK_MKT' 
and eg.group_name='经销商负责ASM' and ws.step_code='FROM_ASM_RESPONSE'

update el set el.source_key=(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责FLSR(全量关系)'),el.remark='经销商负责FLSR(全量关系)' 
from wx_t_workflow_exe_list el left join wx_t_workflow_step ws on el.step_id=ws.step_id 
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP' 
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER' where ws.workflow_code='NON_LOCK_MKT' 
and eg.group_name='经销商负责FLSR' and ws.step_code='TO_FLSR_CONFIRM'

update el set el.source_key=(select id from wx_t_workflow_pre_exe_group where group_name='经销商负责FLSR(全量关系)'),el.remark='经销商负责FLSR(全量关系)' 
from wx_t_workflow_exe_list el left join wx_t_workflow_step ws on el.step_id=ws.step_id 
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP' 
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER' where ws.workflow_code='NON_LOCK_MKT' 
and eg.group_name='经销商负责FLSR' and ws.step_code='FROM_FLSR_SUBMIT_SURVEY'
