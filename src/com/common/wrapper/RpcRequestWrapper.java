package com.common.wrapper;


import com.alibaba.fastjson.JSONObject;
import com.common.util.JsonUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Rpc请求包装器，用于存储请求body数据，以解决请求输入流只能读取一次的问题
 */
public class RpcRequestWrapper extends HttpServletRequestWrapper {
    private Map<String, String[]> parameterMap; // 所有参数的Map集合
    private HttpServletRequest request;

    private String bodyString;
    private static Logger logger = LoggerFactory.getLogger(RpcRequestWrapper.class);
    public RpcRequestWrapper(HttpServletRequest request) {
        super(request);
        parameterMap = request.getParameterMap();
        if (request != null && request.getContentType() != null && !request.getContentType().contains("multipart/form-data")) {
            try {
                ServletInputStream stream = request.getInputStream();
                bodyString = IOUtils.toString(stream, StandardCharsets.UTF_8.name());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.request = request;
    }

    /* 获取所有参数名
     *
     * @return 返回所有参数名
     */
    @Override
    public Enumeration<String> getParameterNames() {
        ArrayList<String> list = new ArrayList<String>(parameterMap.keySet());
        return Collections.enumeration(list);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if(request != null && request.getContentType() != null && request.getContentType().contains("multipart/form-data")) {
            return super.getInputStream();
        }
        String bizBindMsg = "";
        ServletInputStream stream = null;
//        try {
//            stream = request.getInputStream();
//            bizBindMsg = IOUtils.toString(stream, StandardCharsets.UTF_8.name());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        bizBindMsg = bodyString;
        try {
            //如果不是JSON格式的，则直接跳过
            if(JsonUtil.isJSON(bizBindMsg)){
                bizBindMsg = URLDecoder.decode(bizBindMsg.replaceAll("%","%25").replaceAll("\\+","%2B"), StandardCharsets.UTF_8.name());
                bizBindMsg = stripXSS(bizBindMsg, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        /**
         * 将解密后的明文串放到buffer数组中
         */
        byte[] buffer = null;
        try {
            buffer = bizBindMsg.getBytes(StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        final ByteArrayInputStream bais = new ByteArrayInputStream(buffer);
        return new ServletInputStream() {

            @Override
            public int read() throws IOException {
                return bais.read();
            }
        };
    }

    @Override
    public String getParameter(String parameter) {
        String[] value = parameterMap.get(parameter);
        if (value != null){
//            System.err.println("参数名："+parameter+",原始值为:"+value[0]+",替换后为："+stripXSS(value[0]));
            return stripXSS(value[0], false);
        }
        return null;
    }

    /**
     * 获取attribute,特殊字符过滤
     */
    @Override
    public Object getAttribute(String parameter) {
        Object value = super.getAttribute(parameter);
        if (value != null && value instanceof String) {
            Object afterValue = stripXSS((String) value, false);
//            System.err.println("参数名33："+parameter+"的原始值为"+value+",替换后为："+afterValue);
            return afterValue;
        }else{
            return value;
        }

    }




    @Override
    public String getHeader(String name) {
        String value = super.getHeader(name);
//        logger.info("\nHttp header|原始value:{},替换后:{}",value,stripXSS(value));
        return stripXSS(value, false);
    }

    /**
     * 获取指定参数名的所有值的数组，如：checkbox的所有数据
     * 接收数组变量 ，如checkobx类型
     */
    @Override
    public String[] getParameterValues(String name) {
        return parameterMap.get(name);
    }
    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> newMap = new HashMap<String, String[]>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            newMap.put(entry.getKey(), new String[]{stripXSS(entry.getValue()[0], true)});
        }
        return newMap;
    }
    public void setParameterMap(Map<String, String[]> parameterMap) {
        this.parameterMap = parameterMap;
    }

    private String stripXSS(String value, boolean printVal) {
        if (!com.common.util.StringUtils.isNull(value)) {
            if (printVal) {
                logger.warn("原始value={}",value);
            }
//            String regEx = "[`\'+！‘”“’？]|\n|\r|\t";
//            Pattern scriptPattern = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
//            value = scriptPattern.matcher(value).replaceAll("");
//            logger.warn("替换后value={}",value);

            Pattern scriptPattern = Pattern.compile("<style>(.*?)</style>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("</style>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("<script>(.*?)</script>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("src[\r\n]*=[\r\n]*\\\'(.*?)\\\'",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("src[\r\n]*=[\r\n]*\\\"(.*?)\\\"",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("</script>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("<script(.*?)>",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("eval\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("expression\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("onload(.*?)=",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("alert\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("window.location(.*?)=",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("location(.*?)=",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("unescape\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("execscript\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("msgbox\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("confirm\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("prompt\\((.*?)\\)",
                    Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("document.*?\\)");
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile(".?href=.*?[>;]");
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("netsparker\\((.*?)\\)", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");

            scriptPattern = Pattern.compile("\"><[^>]+>", Pattern.CASE_INSENSITIVE);
            value = scriptPattern.matcher(value).replaceAll("");
        }
        return value;
    }

    /**
     * 关键词校验
     * @param str
     * @return
     */
    protected static boolean sqlValidate(String str) {
        // 统一转为小写
        str = str.toLowerCase();
        // 过滤掉的sql关键字，可以手动添加
        String badStr = "'|and|exec|execute|insert|select|delete|update|count|drop|*|%|chr|mid|master|truncate|" +
                "char|declare|sitename|net user|xp_cmdshell|;|or|-|+|,|like'|and|exec|execute|insert|create|drop|" +
                "table|from|grant|use|group_concat|column_name|" +
                "information_schema.columns|table_schema|union|where|select|delete|update|order|by|count|*|" +
                "chr|mid|master|truncate|char|declare|or|;|-|--|+|,|like|//|/|%|#";
        String[] badStrs = badStr.split("\\|");
        for (int i = 0; i < badStrs.length; i++) {
            if (str.indexOf(badStrs[i]) >= 0) {
                return true;
            }
        }
        return false;
    }

    public String getBody() {
        return bodyString;
    }

    public JSONObject getBodyJson() {
        JSONObject builderJson = null;
        if (org.apache.commons.lang3.StringUtils.isBlank(bodyString)) {
            return null;
        }
        builderJson = JSONObject.parseObject(bodyString);
        return builderJson;
    }


}