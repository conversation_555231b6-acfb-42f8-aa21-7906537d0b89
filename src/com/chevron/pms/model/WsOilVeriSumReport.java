package com.chevron.pms.model;

import java.util.List;

import com.chevron.exportdata.ExpAnnotation;
import com.chevron.exportdata.ExpAnnotation.Align;

/**
 * 合伙人核销统计导出包装类
 * <AUTHOR>
 *
 */
public class WsOilVeriSumReport extends WsOilVeriSumSpChe {

	public WsOilVeriSumReport(OilVerificationView verificationSummary) {
		super(verificationSummary);
	}
	@ExpAnnotation(id=20, name="总扫码升数(L)", width=25, align=Align.RIGHT)
	@Override
	public double getCapacity() {
		return super.getCapacity();
	}

	@ExpAnnotation(id=32, name="已申请未结算升数(L)", width=25, align=Align.RIGHT)
	@Override
	public double getSubmittedCapacity() {
		return super.getSubmittedCapacity();
	}

	@ExpAnnotation(id=34, name="已结算升数(L)", width=25, align=Align.RIGHT)
	@Override
	public double getApprovedCapacity() {
		return super.getApprovedCapacity();
	}
	@Override
	public List<OilVerification> getDetailList() {
		return super.getDetailList();
	}
	@Override
	public double getMechanicPerLiterReward() {
		return super.getMechanicPerLiterReward();
	}
	@Override
	public double getMechanicReward() {
		return super.getMechanicReward();
	}
	@Override
	public String getMechanicRewardF() {
		return super.getMechanicRewardF();
	}
	@Override
	public double getOwnerPerLiterReward() {
		return super.getOwnerPerLiterReward();
	}
	@Override
	public double getOwnerReward() {
		return super.getOwnerReward();
	}
	@Override
	public String getOwnerRewardF() {
		return super.getOwnerRewardF();
	}
	@Override
	public Long getPartnerId() {
		return super.getPartnerId();
	}
	@Override
	public Long getSpDistributionRuleId() {
		return super.getSpDistributionRuleId();
	}
	@Override
	public double getSpPerLiterReward() {
		return super.getSpPerLiterReward();
	}
	@Override
	public double getSpReward() {
		return super.getSpReward();
	}
	@Override
	public String getSpRewardF() {
		return super.getSpRewardF();
	}
	@Override
	public double getTotalReward() {
		return super.getTotalReward();
	}
	@Override
	public String getTotalRewardF() {
		return super.getTotalRewardF();
	}
	
	@ExpAnnotation(id=30, name="可结算升数(L)", width=25, align=Align.RIGHT)
	@Override
	public double getUnsubmitCapacity() {
		return super.getUnsubmitCapacity();
	}
	@Override
	public Long getWorkshopId() {
		return super.getWorkshopId();
	}
	@Override
	public Long getWsDistributionRuleId() {
		return super.getWsDistributionRuleId();
	}
	@Override
	public void setApprovedCapacity(double approvedCapacity) {
		super.setApprovedCapacity(approvedCapacity);
	}
	@Override
	public void setCapacity(double capacity) {
		super.setCapacity(capacity);
	}
	@Override
	public void setDetailList(List<OilVerification> detailList) {
		super.setDetailList(detailList);
	}
	@Override
	public void setMechanicPerLiterReward(double mechanicPerLiterReward) {
		super.setMechanicPerLiterReward(mechanicPerLiterReward);
	}
	@Override
	public void setMechanicReward(double mechanicReward) {
		super.setMechanicReward(mechanicReward);
	}
	@Override
	public void setOwnerPerLiterReward(double ownerPerLiterReward) {
		super.setOwnerPerLiterReward(ownerPerLiterReward);
	}
	@Override
	public void setOwnerReward(double ownerReward) {
		super.setOwnerReward(ownerReward);
	}
	@Override
	public void setPartnerId(Long partnerId) {
		super.setPartnerId(partnerId);
	}
	@Override
	public void setPartnerName(String partnerName) {
		super.setPartnerName(partnerName);
	}
	@Override
	public void setSpDistributionRuleId(Long spDistributionRuleId) {
		super.setSpDistributionRuleId(spDistributionRuleId);
	}
	@Override
	public void setSpPerLiterReward(double spPerLiterReward) {
		super.setSpPerLiterReward(spPerLiterReward);
	}
	@Override
	public void setSubmittedCapacity(double submittedCapacity) {
		super.setSubmittedCapacity(submittedCapacity);
	}
	@Override
	public void setTotalReward(double totalReward) {
		super.setTotalReward(totalReward);
	}
	@Override
	public void setWorkshopId(Long workshopId) {
		super.setWorkshopId(workshopId);
	}
	@Override
	public void setWorkshopName(String workshopName) {
		super.setWorkshopName(workshopName);
	}
	@Override
	public void setWsDistributionRuleId(Long wsDistributionRuleId) {
		super.setWsDistributionRuleId(wsDistributionRuleId);
	}
}
