package com.chevron.dsrkpi.controller;

import com.chevron.dsrkpi.business.CdmKpiInfoBizService;
import com.chevron.dsrkpi.business.DsrKpiBizService;
import com.chevron.dsrkpi.dao.DsrKpiMapper;
import com.chevron.dsrkpi.model.DsrKpiParams;
import com.chevron.dsrkpi.model.excel.PartnerAllData;
import com.chevron.dsrkpi.model.excel.RowData;
import com.chevron.dsrkpi.service.DsrKpiService;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportColAlign;
import com.chevron.exportdata.ICellStyleProcessor;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.chevron.kpi.dao.KPIInfoMapper;
import com.chevron.kpi.model.SellInVO;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.utils.business.ReportViewBizService;

import cn.hutool.core.collection.CollectionUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.OutputStreamWriter;
import java.net.URLEncoder;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * 经销商销售绩效Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2019-12-24 09:35
 */
@Controller
@RequestMapping(value="/dsrkpi")
public class DsrKpiController {

	@Autowired
	private DsrKpiService dsrKpiService;
	
	@Autowired
	private DicItemVoMapper dicItemVoMapper;
	
	@Autowired
	private DsrKpiBizService dsrKpiBizService;
	
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;

	@Autowired
    private CdmKpiInfoBizService cdmKpiInfoBizService;
	
	@Resource
	KPIInfoMapper kPIInfoMapper;
	
	@Autowired
	private DsrKpiMapper dsrKpiMapper;
	
    private static IPropertyHelper volumeHelper = new IPropertyHelper() {
		
		@SuppressWarnings("unchecked")
		@Override
		public Object getProperty(String propertyName, Object bean)
				throws Exception {
			return Math.round((Double)((Map)bean).get(propertyName));
		}
	};
	
	private final static Logger log = Logger.getLogger(DsrKpiController.class);

	/**
	 * 经销商销售绩效列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public JsonResponse queryForPage(DsrKpiParams params, HttpServletRequest request){
		JsonResponse resultMap = new JsonResponse();
		log.info("queryForPage: " + JsonUtil.writeValue(params));

		try {
			dsrKpiBizService.queryForPage(params, resultMap);
			log.info("queryForPage success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.controller.DsrKpiController.queryForPage", JsonUtil.writeValue(params));
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/totalPoint.do" , method = { RequestMethod.POST})
	public JsonResponse totalPoint(@RequestBody DsrKpiParams params, HttpServletRequest request){
		/* JsonResponse resultMap = new JsonResponse(); */
		log.info("totalPoint: " + JsonUtil.writeValue(params));
		return dsrKpiService.selectPointTotal(params);
	}
	
	@ResponseBody
	@RequestMapping(value="/queryPermission.do")
	public Integer queryPermission(HttpServletRequest request){
		return dsrKpiService.queryPermission();
		
	}
	
	@ResponseBody
	@RequestMapping(value="/personalPointDetail.do" , method = { RequestMethod.POST})
	public JsonResponse personalPointDetail(@RequestBody DsrKpiParams params, HttpServletRequest request){		
		JsonResponse resultMap = new JsonResponse();
		log.info("queryForPage: " + JsonUtil.writeValue(params));

		try {
			dsrKpiBizService.personalPointDetail(params, resultMap);
			log.info("queryForPage success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.controller.DsrKpiController.queryForPage", JsonUtil.writeValue(params));
		}
		return resultMap;
	}
	
	
	
	
	
	@ResponseBody
	@RequestMapping(value="/personalPoint.do" , method = { RequestMethod.POST})
	public JsonResponse personalPoint(@RequestBody DsrKpiParams params, HttpServletRequest request){		

		log.info("personalPoint: " + JsonUtil.writeValue(params));
		//dsrKpiService.selectPointPersonal(params);
		return dsrKpiService.selectPointPersonal(params);
	}
	
	
	protected Map<String, Object> buildRequestParams(HttpServletRequest request){
		Map<String, Object> paramMap = new HashMap<String, Object>();
		Enumeration<String> names = request.getParameterNames();
		while(names.hasMoreElements()) {
			String name = names.nextElement();
			paramMap.put(name, request.getParameter(name));
		}
		return paramMap;
	}
    
	//通过form表单传参数	
	@RequestMapping(value = "/excel/export.do")
	public String exportByExcel(@RequestParam("packageName")String packageName, @RequestParam("viewName")String viewName, @RequestParam("fileName")String fileName,
			 @RequestParam(value="columnInfoDictKey", required=false) String columnInfoDictKey, 
			 @RequestParam("regionName")String regionName, @RequestParam("asmName")String asmName,
			 @RequestParam("organizationName")String organizationName, @RequestParam("saleName")String saleName, @RequestParam("fromDate")String fromDate,@RequestParam("toDate")String toDate,		 
			HttpServletRequest request, HttpServletResponse response){
		
		//初始化参数
		int permissionWeight;
		WxTUser curUser = ContextUtil.getCurUser();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		//组装数据
		try {
			permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getOrgId(), "CAI_KPI_INFO");
			paramMap.put("permissionWeight", permissionWeight);
			paramMap.put("orgCai",  StringUtils.isEmpty(curUser.getCai())? "***" : curUser.getCai());
			paramMap.put("regionName", StringUtils.isEmpty(regionName)?"%%":regionName);
			paramMap.put("asmName", StringUtils.isEmpty(asmName)?"%%":asmName);
			paramMap.put("organizationName", StringUtils.isEmpty(organizationName)?"%%":organizationName);
			paramMap.put("saleName", StringUtils.isEmpty(saleName)?"%%":saleName);
			SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DEFAULT_DATE_PATTERN);
			Calendar d = Calendar.getInstance();
			paramMap.put("fromDate", StringUtils.isEmpty(fromDate)?sdf.format(new java.util.Date(1979)):fromDate);
			paramMap.put("toDate", StringUtils.isEmpty(toDate)?sdf.format(new java.util.Date(d.get(Calendar.YEAR) + 1)):toDate);
			
		} catch (WxPltException e1) {
			e1.printStackTrace();
		}
		
		
		try {
			final Map<String, DicItemVo> columnInfoMap = new HashMap<String, DicItemVo>();
			if(StringUtils.isNotBlank(columnInfoDictKey)) {
				List<DicItemVo> itemVos = dicItemVoMapper.selectByCode(columnInfoDictKey);
				if(!itemVos.isEmpty()) {
					for(DicItemVo itemVo : itemVos) {
						columnInfoMap.put(itemVo.getDicItemCode(), itemVo);
					}
				}
			}
			final List<Map<String, String>> dataList = new ArrayList<Map<String,String>>();
			final List<ExportCol> exportCols = new ArrayList<ExportCol>();
//			reportViewBizService.loadData(packageName, viewName, paramMap, new RowCallbackHandler() {
			reportViewBizService.loadData(request.getParameter("packageName").toString(), request.getParameter("viewName").toString(), paramMap, new RowCallbackHandler() {

				@Override
				public void processRow(ResultSet arg0) throws SQLException {
					//初始化表头
					ResultSetMetaData metaData = arg0.getMetaData();
					ExportCol col = null;
					String columnName = null;
					for(int i = 1; i <= metaData.getColumnCount(); i++){
						columnName = metaData.getColumnName(i);
						col = new ExportCol(columnName, columnName);
						col.setPropertyHelper(IPropertyHelper.mapHelper);
						DicItemVo columnInfo = columnInfoMap.get(columnName);
						if(columnInfo == null) {
							col.setWidth(30);
						}else {
							col.setWidth(Integer.parseInt(columnInfo.getDicItemName()));
							if(StringUtils.isNotBlank(columnInfo.getDicItemDesc())) {
								final JSONObject config = JSONObject.fromObject(columnInfo.getDicItemDesc());
								col.setDataType(config.getString("type"));
								if("right".equalsIgnoreCase(config.getString("align"))) {
									col.setAlign(ExportColAlign.RIGHT);
								}
								if(StringUtils.isNotBlank(config.getString("pattern"))) {
									col.setCellStyleProcessor(new ICellStyleProcessor() {
										
										@Override
										public void process(CellStyle cellStyle, Workbook workbook) {
											DataFormat df = workbook.createDataFormat();
											cellStyle.setDataFormat(df.getFormat(config.getString("pattern")));
										}
									});
								}
							}
						}
						exportCols.add(col);
					}
					//加载数据
					Map<String, String> item = null;
					do{
						item = new HashMap<String, String>(metaData.getColumnCount());
						for(int i = 1; i <= metaData.getColumnCount(); i++){
							item.put(metaData.getColumnName(i), arg0.getString(i));
						}					
						dataList.add(item);
					}while(arg0.next());
				}
			});
			CommonUtil.setExportResponseHeader(request, response, fileName, "xlsx");
			PoiWriteExcel.exportLargeData(dataList, exportCols, response.getOutputStream(), "Sheet1", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	
    @ResponseBody
	@RequestMapping(value = "/sendEmail.do", method = { RequestMethod.POST,  RequestMethod.GET})
	public JsonResponse sendEmail(final String date)
    {
    	JsonResponse map = new JsonResponse();
    	new Thread(){
			public void run() {
				dsrKpiService.sendEmail(date);
			};
		}.start();
		return map;
   }

   /*
	@RequestMapping(value = "/excel/export11.do")
	public String exportByExcel11(HttpServletRequest request, HttpServletResponse response){
		
		try {
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("region_name", "区域");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
//			exportCol = new ExportCol("channel_manager_name", "渠道经理");
//			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
//			exportCols.add(exportCol);
			exportCol = new ExportCol("suppervisor_name", "大区经理");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("sales_name", "销售");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("organization_name", "经销商名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			exportCol = new ExportCol("dsr_name", "DSR");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol.setWidth(20);
			exportCol = new ExportCol("pointsDouble", "获得总积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			exportCol = new ExportCol("residueDouble", "剩余积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);		
			exportCols.add(exportCol);
			
			Map<String, TreeMap<String, IExportCol>> kpiColsMap = new HashMap<String, TreeMap<String, IExportCol>>();
			Map<String, Set<String>> existsColMap = new HashMap<String, Set<String>>();
			
			Map<String, Object> params = reportViewBizService.buildRequestParams(request);
			for (Map.Entry<String, Object> entry : params.entrySet()) {
				  //System.out.println("Key = " + entry.getKey() + ", Value = " + entry.getValue());
				  if(entry.getValue() != null && StringUtils.isEmpty(entry.getValue().toString())) {
					  params.put(entry.getKey(), null);
				  }
				}
			WxTUser user = ContextUtil.getCurUser();
			
			params.put("loginUserId", user.getUserId());
			int permissionWeight = operationPermissionBizService.getPermissionWeight(user.getUserId(), "CAI_KPI_INFO");
			if(permissionWeight == 16) {
		    	//如果是经销商老板，则权限设置为32
		    	String userModelString = user.getUserModel();
		    	if(WxTUser.USER_MODEL_SP.equals(userModelString)) {
		    		permissionWeight = 32;
		        }
			}
			if(StringUtils.isEmpty(user.getCai())) {
				params.put("loginCai", "XXX");
				params.put("partnerId",user.getOrgId());
			}else {
				params.put("loginCai", user.getCai());
				params.put("partnerId","999");
			}
			
			params.put("permissionWeight", permissionWeight);
			List<Map<String, Object>> dataList = reportViewBizService.loadData("dsrkpi", "DsrPointExport", params);
			List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
			List<String> kpiCodes = new ArrayList<String>();
			Map<String, Object> currentDsr = null;
			String currentKpiCode = null;
			
			String currentMonth = null;
			for(Map<String, Object> data : dataList) {
				if(currentKpiCode == null || !currentKpiCode.equals(data.get("kpi_code"))) {
					currentKpiCode = (String)data.get("kpi_code");
					if(currentKpiCode == null) {
						continue;
					}
					if(!kpiColsMap.containsKey(currentKpiCode)) {
						kpiColsMap.put(currentKpiCode, new TreeMap<String, IExportCol>());
						existsColMap.put(currentKpiCode, new HashSet<String>());
						kpiCodes.add(currentKpiCode);
					}
				}
				currentMonth = DateUtil.getDateStr((Date)data.get("effictive_date"), "yyyy年M月");
				String monthForSort = DateUtil.getDateStr((Date)data.get("effictive_date"), "yyyy年M月");

				if(!existsColMap.get(currentKpiCode).contains(monthForSort)) {
					exportCol = new ExportCol(currentKpiCode + "/" + monthForSort, currentMonth);
					exportCol.setParentsPath((String) data.get("kpi_name"));
					exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
					exportCol.setDataType("number");
					exportCol.setWidth(20);
					kpiColsMap.get(currentKpiCode).put(monthForSort, exportCol);
					existsColMap.get(currentKpiCode).add(monthForSort);
				}
				if(currentDsr == null || !currentDsr.get("dsr_id").equals(data.get("dsr_id"))) {
					if(currentDsr != null && (Double)currentDsr.get("pointsDouble") > 0) {
						newDataList.add(currentDsr);
					}
					currentDsr = data;
					data.put("pointsDouble", CommonUtil.toDouble((Number)data.get("award_point"), 0.0));
					data.put("residueDouble", (Number)data.get("left_point"));
				}else {
					currentDsr.put("pointsDouble", (Double)currentDsr.get("pointsDouble") + CommonUtil.toDouble((Number)data.get("award_point"), 0.0));
				}
				if(currentKpiCode.equals("TOP_RANK")) {
					currentDsr.put(currentKpiCode + "/" + currentMonth, data.get("ext_property1"));
				}else {
					currentDsr.put(currentKpiCode + "/" + currentMonth, data.get("kpi_value"));
				}
			}
			if(currentDsr != null && (Double)currentDsr.get("pointsDouble") > 0) {
				newDataList.add(currentDsr);
			}
			for(String kpiCode: kpiCodes) {
				exportCols.addAll(kpiColsMap.get(kpiCode).values());
			}
			CommonUtil.setExportResponseHeader(request, response, "DSR积分详情" + DateUtil.getCurrentDate("yyyyMMdd"), "xlsx");
//			response.reset();
//			response.setContentType("application/vnd.ms-excel;charset=GBK");
//			response.addHeader("Content-Disposition", "attachment; filename=\"Customer Sales Complete Detail.xlsx\"");
			PoiWriteExcel.exportLargeData(newDataList, exportCols, response.getOutputStream(), "sheet1", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
	*/


   @RequestMapping(value = "/excel/exportKpi.do")
	public String exportByExportKpi(HttpServletRequest request, HttpServletResponse response){
		try {
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("region_name", "区域");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			/*exportCol = new ExportCol("channel_manager_name", "渠道经理");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);*/
			exportCol = new ExportCol("suppervisor_name", "大区经理");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("sales_name", "销售");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("organization_name", "经销商名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			exportCol = new ExportCol("dsr_name", "DSR");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol.setWidth(20);
			exportCol = new ExportCol("ytdSellIn", "YTD销量");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataFormat("#,##0.00#");
			exportCol.setDataType("number");
			exportCols.add(exportCol);
			exportCol.setWidth(20);
			exportCol = new ExportCol("pointsDouble", "获得总积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataFormat("#,##0#");
			exportCol.setDataType("number");
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			exportCol = new ExportCol("residueDouble", "剩余积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataFormat("#,##0#");
			exportCol.setDataType("number");
			exportCol.setWidth(20);		
			exportCols.add(exportCol);
			
			Map<String, TreeMap<String, IExportCol>> kpiColsMap = new HashMap<String, TreeMap<String, IExportCol>>();
			Map<String, Set<String>> existsColMap = new HashMap<String, Set<String>>();
			
			Map<String, Object> params = reportViewBizService.buildRequestParams(request);
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_SP.equals(user.getUserModel())) {
				params.put("partnerId", user.getOrgId());
			}
			int permissionWeight =  operationPermissionBizService.getPermissionWeight(user.getUserId(), "CAI_KPI_INFO");
			params.put("loginCai", user.getCai());
			params.put("loginUserId", user.getUserId());
			params.put("permissionWeight",permissionWeight);
			//String  toDate = params.get("toDate").toString();
			String  toDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
			params.put("toDate", new SimpleDateFormat("yyyy-MM-dd").format(DateUtil.addDays(DateUtil.parseDate(toDate,"yyyy-MM-dd"), 1)));
			List<Map<String, Object>> dataList = reportViewBizService.loadData("dsrkpi", "DsrPointsExport", params);
			
			//查询YTD销量
			String roleType; //ytd销量类型
			String cai; 
			String orgCai = user.getCai();
			//判断是否有权限
			try {
				//获取当前用户的查看权限
				if(1 == (permissionWeight&1)) {
					permissionWeight = 1;
					roleType = "admin";
					cai = null;
				}else if(4 == (permissionWeight&4)) {
					permissionWeight = 4;
					roleType = "channel manager";
					cai = orgCai;
				}else if(8 == (permissionWeight&8)) {
					permissionWeight = 8;
					roleType = "asm";
					cai = orgCai;
				}else if(16 == (permissionWeight&16)) {
					String userModel = user.getUserModel();
					if(WxTUser.USER_MODEL_SP.equals(userModel)) {						
						permissionWeight = 32; //销售老板赋有fdsr的权限，且特殊处理
						roleType = "admin";
						cai = null;
					}else {
						permissionWeight = 16;
						roleType = "flsr";
						cai = orgCai;
					}
				}else {
					return null;
				}
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
			
			String productChannel = "Commercial";
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("year", new SimpleDateFormat("yyyy").format(new Date()));
			map.put("productChannel", productChannel);
			map.put("roleType", roleType);
			map.put("cai", cai);
			List<SellInVO> sellInList = kPIInfoMapper.getSellInList(map);
			Map<Long, Object> sellInMap = new HashMap<Long, Object>();
			for (SellInVO sellInVO : sellInList) {
				sellInMap.put(sellInVO.getDistributorId(), sellInVO);
			}
			
			List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
			List<String> kpiCodes = new ArrayList<String>();
			Map<String, Object> currentDsr = null;
			String currentKpiCode = null;
			String currentMonth = null;
			for(Map<String, Object> data : dataList) {
				
				if(currentKpiCode == null || !currentKpiCode.equals(data.get("kpi_code"))) {
					currentKpiCode = (String)data.get("kpi_code");
					if(currentKpiCode == null) {
						continue;
					}
					if(!kpiColsMap.containsKey(currentKpiCode)) {
						kpiColsMap.put(currentKpiCode, new TreeMap<String, IExportCol>());
						existsColMap.put(currentKpiCode, new HashSet<String>());
						kpiCodes.add(currentKpiCode);
					}
				}
				
				currentMonth = DateUtil.getDateStr( DateUtil.parseDate(data.get("effictive_date").toString(),"yyyy-MM-dd"), "yyyy年M月");
				String monthForSort = DateUtil.getDateStr(DateUtil.parseDate(data.get("effictive_date").toString(),"yyyy-MM-dd"), "yyyy年M月");

				if(!existsColMap.get(currentKpiCode).contains(monthForSort)) {
					exportCol = new ExportCol(currentKpiCode + "/" + monthForSort, currentMonth);
					exportCol.setParentsPath((String) data.get("kpi_name"));
					exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
					exportCol.setDataType("number");
					exportCol.setWidth(20);
					kpiColsMap.get(currentKpiCode).put(monthForSort,exportCol);
					existsColMap.get(currentKpiCode).add(monthForSort);
				}
				if(currentDsr == null || !currentDsr.get("dsr_id").equals(data.get("dsr_id"))) {
					currentDsr = data;
					
					SellInVO sellInVO = (SellInVO)sellInMap.get(data.get("distributor_id"));
					if(sellInVO != null) {
						NumberFormat nf = NumberFormat.getNumberInstance();
						nf.setMaximumFractionDigits(2);
						data.put("ytdSellIn", nf.format(sellInVO.getActLiters()/1000));
					}
					if(data.get("left_point") != null) {
						data.put("residueDouble",(Number)data.get("left_point") );
					}
					if(data.get("all_point") != null) {
						data.put("pointsDouble", (Number)data.get("all_point"));
					}
					newDataList.add(currentDsr);
				}
//				if(currentKpiCode.equals("TOP_RANK")) {
//					currentDsr.put(currentKpiCode + "/" + currentMonth, data.get("ext_property1"));
//				}else {
//					currentDsr.put(currentKpiCode + "/" + currentMonth, data.get("kpi_value"));
//				}
				currentDsr.put(currentKpiCode + "/" + currentMonth, data.get("kpi_value"));
			}
			for(String kpiCode: kpiCodes) {
				exportCols.addAll(kpiColsMap.get(kpiCode).values());
			}
			CommonUtil.setExportResponseHeader(request, response, "DSR绩效详情" + DateUtil.getCurrentDate("yyyyMMdd"), "xlsx");
//			response.reset();
//			response.setContentType("application/vnd.ms-excel;charset=GBK");
//			response.addHeader("Content-Disposition", "attachment; filename=\""+DateUtil.getDateStr(new Date(),DateUtil.ACCURACY_PATTERN_SECOND)+ "SR积分获得详情.xlsx\"");
			PoiWriteExcel.exportLargeData(newDataList, exportCols, response.getOutputStream(), "sheet1", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

	//http://localhost/dsrkpi/exportBonusDetail.do?provideTime=2020-04
    @RequestMapping("/exportBonusDetail.do")
    public String exportBonusDetail(String provideTime, HttpServletRequest request, HttpServletResponse response) {
        try {
            Date date = DateUtil.parseDate(provideTime, "yyyy-MM");
            List<PartnerAllData> partnerAllDatas = dsrKpiService.exportBonusDetail(provideTime);
            HashMap<String, Object> root = new HashMap<String, Object>();
            root.put("data", partnerAllDatas);
            root.put("now", new Date());
            root.put("month", DateUtil.getMonth(date));
            Configuration configuration = new Configuration();
            configuration.setDefaultEncoding("UTF-8");
            configuration.setServletContextForTemplateLoading(request.getServletContext(),"/WEB-INF/templates/");
            Template template = configuration.getTemplate("/performance/cdm_dsr_performance.ftl");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(provideTime+"金富力绩效统计", "UTF-8") + ".xls");
            template.process(root, new OutputStreamWriter(response.getOutputStream(), "UTF-8"));
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
            return "forward:/common/jsp/downloadError.jsp";
        }
    }

    //http://localhost/dsrkpi/exportBonusDetailByYear.do?kpiYear=2020
    @RequestMapping("/exportBonusDetailByYear.do")
    public String exportBonusDetailByYear(String kpiYear, HttpServletRequest request, HttpServletResponse response) {
        try {
            kpiYear= "2020";
            List<PartnerAllData> partnerAllDatas = dsrKpiService.exportTotalBonusDetailByKpiYear(kpiYear);
            HashSet<String> months = new HashSet<String>();
            HashSet<Long> patnerIds = new HashSet<Long>();
            ArrayList<RowData> partners = new ArrayList<RowData>();
            ArrayList<RowData> dsrs = new ArrayList<RowData>();
            HashMap<Long,Long> dsrPartnerMap = new HashMap<Long,Long>();
            for (PartnerAllData partnerAllData : partnerAllDatas) {
                patnerIds.add(partnerAllData.getParentId());
                months.add(partnerAllData.getMonth());
                partners.add(partnerAllData.getPartnerData());
                dsrs.addAll(partnerAllData.getDsrDatas());
            }
            for (RowData dsr : dsrs) {
                dsrPartnerMap.put(dsr.getDsrId(), dsr.getPartnerId());
            }
            ArrayList<RowData> dsrPartners = new ArrayList<RowData>();
            for (Long key : dsrPartnerMap.keySet()) {
                RowData rowData = new RowData();
                rowData.setPartnerId(dsrPartnerMap.get(key));
                rowData.setDsrId(key);
                dsrPartners.add(rowData);
            }
            ArrayList<RowData> specialData = new ArrayList<RowData>();
            HashMap<Long, HashSet<String>> dsrMonthMap = new HashMap<Long, HashSet<String>>();
            for (RowData dsr : dsrs) {
                HashSet<String> set = dsrMonthMap.get(dsr.getDsrId());
                if(set == null){
                    set = new HashSet<String>();
                }
                set.add(dsr.getMonth());
                dsrMonthMap.put(dsr.getDsrId(), set);
            }
            for (Long dsrId : dsrMonthMap.keySet()) {
                HashSet<String> set = dsrMonthMap.get(dsrId);
                Collection<String> disjunction = CollectionUtil.disjunction(months, set);
                RowData dsrRowData = null;
                for (RowData dsr : dsrs) {
                    if(dsr.getDsrId().equals(dsrId)){
                        dsrRowData = dsr;
                        break;
                    }
                }
                for (String month : disjunction) {
                    RowData rowData = new RowData();
                    rowData.setMonth(month);
                    rowData.setPartnerId(dsrRowData.getPartnerId());
                    rowData.setDsrId(dsrId);
                    rowData.setGainTotalBonus(dsrRowData.getGainTotalBonus());
                    rowData.setDsrName(dsrRowData.getDsrName());
                    rowData.setPartnerName(dsrRowData.getPartnerName());
                    //该属性需要去经销商数据中获取
                    for (RowData dsr : dsrs) {
                        if(dsr.getMonth().equals(month) && dsr.getPartnerId().equals(rowData.getPartnerId())){
                            rowData.setPreQuarterPass(dsr.getPreQuarterPass());
                        }
                    }
                    specialData.add(rowData);
                }
            }
            dsrs.addAll(specialData);
            HashMap<String, Object> root = new HashMap<String, Object>();
            CollectionUtil.sort(months, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    return o1.compareTo(o2);
                }
            });
            root.put("months", months);
            root.put("patnerIds", patnerIds);
            root.put("dsrPartners", dsrPartners);
            root.put("partners", partners);
            root.put("dsrs", dsrs);
            Configuration configuration = new Configuration();
            configuration.setDefaultEncoding("UTF-8");
            configuration.setServletContextForTemplateLoading(request.getServletContext(),"/WEB-INF/templates/");
            Template template = configuration.getTemplate("/performance/cdm_dsr_year_performance.ftl");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(kpiYear+"金富力绩效统计", "UTF-8") + ".xls");
            template.process(root, new OutputStreamWriter(response.getOutputStream(), "UTF-8"));
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
            return "forward:/common/jsp/downloadError.jsp";
        }
    }

    /*
    @SuppressWarnings("unchecked")
	@RequestMapping(value = "/excel/exportPrePonits.do")
	public String exportPrePonits(HttpServletRequest request, HttpServletResponse response){
		try {
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("regionNameCh", "区域");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("suppervisorName", "大区经理");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("salesName", "销售");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("organizationName", "经销商名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("dsrName", "DSR");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			
			
			exportCol = new ExportCol("pointsDouble", "YTD总积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("residueDouble", "YTD剩余积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);		
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("rePointsDouble", "本次计算发放总积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(30);		
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("entryPoints", "新增客户获得积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("visitPoints", "有效拜访天数获得积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(30);		
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("proofPoints", "车队业绩证明获得积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(30);		
			exportCols.add(exportCol);
	
			Map<String, TreeMap<String, IExportCol>> kpiColsMap = new HashMap<String, TreeMap<String, IExportCol>>();
			Map<String, Set<String>> existsColMap = new HashMap<String, Set<String>>();
			List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
			Map<String, DsrPointsExport> prePointsMap = new HashMap<String, DsrPointsExport>();
			Map<String, Object> current = new HashMap<String, Object>();
			Map<Long, Long> dsrIdMap = new HashMap<Long, Long>();
			Map<Long, Long> partnerIdMap = new HashMap<Long, Long>();
			Map<String, Object> params = reportViewBizService.buildRequestParams(request);
			String date = (String)params.get("date");
			//获取当月实时发放积分数
			List<DsrKpi> calculateBonus = dsrKpiBizService.calculateBonus(date);
			for (DsrKpi dsrKpi : calculateBonus) {
				if(null == dsrIdMap.get(dsrKpi.getDsrId())) {
					dsrIdMap.put(dsrKpi.getDsrId(), dsrKpi.getDsrId());
				}
				if(null == partnerIdMap.get(dsrKpi.getPartnerId())) {
					partnerIdMap.put(dsrKpi.getPartnerId(), dsrKpi.getPartnerId());
				}
				DsrPointsExport dsrPointsExport = prePointsMap.get(dsrKpi.getPartnerId() +"/"+dsrKpi.getDsrId());
				if(null == dsrPointsExport) {
					dsrPointsExport = new DsrPointsExport();
					dsrPointsExport.setDsrId(dsrKpi.getDsrId());
					dsrPointsExport.setPartnerId(dsrKpi.getPartnerId());
				}
				if("TOP_RANK".equals(dsrKpi.getKpiCode())) {
					dsrPointsExport.setEntryPoints(dsrKpi.getAwardPoint() == null ? 0 : dsrKpi.getAwardPoint());
				}
				if("AMOUNT_OF_CERTIFICATE".equals(dsrKpi.getKpiCode())) {
					dsrPointsExport.setProofPoints(dsrKpi.getAwardPoint()  == null ? 0 : dsrKpi.getAwardPoint());	
				}
				if("DAYS_OF_MONTH".equals(dsrKpi.getKpiCode())) {
					dsrPointsExport.setVisitPoints(dsrKpi.getAwardPoint()  == null ? 0 : dsrKpi.getAwardPoint());
				}
				if(null != dsrKpi.getAwardPoint()) {
					dsrPointsExport.setRePointsDouble((dsrPointsExport.getRePointsDouble() == null ? 0: dsrPointsExport.getRePointsDouble())+ dsrKpi.getAwardPoint());
				}
				prePointsMap.put(dsrKpi.getPartnerId() +"/"+dsrKpi.getDsrId(), dsrPointsExport);
			}
			
			Map<Long, PointTotal> PointTotalMap = new HashMap<Long, PointTotal>();
			Map<Long, PointTotal> customerInfoMap = new HashMap<Long, PointTotal>();
			//查询dsrIds的全部积分/剩余积分
			List<Long> dsrIdList = new ArrayList<Long>(dsrIdMap.values()) ;
			
			List<PointTotal> selectPointDouble  = null;
			List<PointTotal> selectCurInfoByOrdIds = null;
			if(null != dsrIdList && !CollectionUtil.isEmpty(dsrIdList)) {
				selectPointDouble = dsrKpiMapper.selectPointDouble(dsrIdList);
				selectCurInfoByOrdIds = dsrKpiMapper.selectCurInfoByOrdIds(dsrIdList);
			}
			
			for (PointTotal pointTotal : selectCurInfoByOrdIds) {
				if(null == customerInfoMap.get(pointTotal.getDsrId())) {
					customerInfoMap.put(pointTotal.getDsrId(), pointTotal);
				}
			}
			
			for (PointTotal pointTotal : selectPointDouble) {
				if(null == PointTotalMap.get(pointTotal.getDsrId())) {
					PointTotalMap.put(pointTotal.getDsrId(), pointTotal);
				}
			}
			
			//查询门店信息
			List<Long> partnerIdList = new ArrayList<Long>(partnerIdMap.values());
			List<PointTotal> selectCurDouble = null;
			if(null != partnerIdList && !CollectionUtil.isEmpty(partnerIdList)) {
				selectCurDouble = dsrKpiMapper.selectCurDouble(partnerIdList);
			}
			
			for (PointTotal pointTotal : selectCurDouble) {
				if(null == PointTotalMap.get(pointTotal.getPartnerId())) {
					PointTotalMap.put(pointTotal.getPartnerId(), pointTotal);
				}
			}
			
			List<DsrPointsExport> dsrPointsExportList =  new ArrayList<DsrPointsExport>(prePointsMap.values());
			
			for (DsrPointsExport dsrPointsExport : dsrPointsExportList) {
				Map<String, Object> pointMap = (Map<String, Object>) current.get(dsrPointsExport.getPartnerId()+"/"+dsrPointsExport.getDsrId());
				if(null == pointMap) {
					pointMap = new HashMap<String, Object>();
					pointMap.put("visitPoints", dsrPointsExport.getVisitPoints());
					pointMap.put("entryPoints", dsrPointsExport.getEntryPoints());
					pointMap.put("proofPoints", dsrPointsExport.getProofPoints());
					pointMap.put("rePointsDouble", dsrPointsExport.getRePointsDouble());
				}
				
				//组装dsr信息
				PointTotal dsrTotal = PointTotalMap.get(dsrPointsExport.getDsrId());
				if(null != dsrTotal) {
					pointMap.put("pointsDouble", dsrTotal.getPointsTotalDouble()+dsrPointsExport.getRePointsDouble());
					pointMap.put("residueDouble", dsrTotal.getResidueTotalDouble()+dsrPointsExport.getRePointsDouble());
					pointMap.put("dsrName", dsrTotal.getChName());
					dsrPointsExport.setPointsDouble(dsrTotal.getPointsTotalDouble());
					dsrPointsExport.setResidueDouble(dsrTotal.getResidueTotalDouble());
				}else {
					pointMap.put("pointsDouble", dsrPointsExport.getRePointsDouble());
					pointMap.put("residueDouble",dsrPointsExport.getRePointsDouble());
					pointMap.put("dsrName", customerInfoMap.get(dsrPointsExport.getDsrId()).getChName());
				}
				//组装经销商信息
				PointTotal curTotal = PointTotalMap.get(dsrPointsExport.getPartnerId());
				if(null != curTotal) {
					pointMap.put("regionNameCh", curTotal.getRegionNameCh());
					pointMap.put("suppervisorName", curTotal.getAsmName());
					pointMap.put("organizationName",curTotal.getOrganizationName());
					pointMap.put("salesName", curTotal.getCmName());
					dsrPointsExport.setRegionNameCh(curTotal.getRegionNameCh());
					dsrPointsExport.setSuppervisorName(curTotal.getAsmName());
					dsrPointsExport.setSalesName(curTotal.getSalesName());
					dsrPointsExport.setOrganizationName(curTotal.getOrganizationName());
				}else {
					pointMap.put("regionNameCh", customerInfoMap.get(dsrPointsExport.getDsrId()).getRegionNameCh());
					pointMap.put("suppervisorName", customerInfoMap.get(dsrPointsExport.getDsrId()).getAsmName());
					pointMap.put("organizationName",customerInfoMap.get(dsrPointsExport.getDsrId()).getOrganizationName());
					pointMap.put("salesName", customerInfoMap.get(dsrPointsExport.getDsrId()).getCmName());
				}
				current.put(dsrPointsExport.getPartnerId()+"/"+dsrPointsExport.getDsrId(), pointMap);
			}
			
			newDataList.addAll((Collection)current.values());
			
			CommonUtil.setExportResponseHeader(request, response, "DSR发放积分详情" + DateUtil.getCurrentDate("yyyyMMdd"), "xlsx");
//			response.reset();
//			response.setContentType("application/vnd.ms-excel;charset=GBK");
//			response.addHeader("Content-Disposition", "attachment; filename=\""+DateUtil.getDateStr(new Date(),DateUtil.ACCURACY_PATTERN_SECOND)+ "SR积分获得详情.xlsx\"");
			PoiWriteExcel.exportLargeData(newDataList, exportCols, response.getOutputStream(), "sheet1", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
    */
}
