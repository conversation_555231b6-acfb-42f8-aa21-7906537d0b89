package com.chevron.kpi.controller;

import com.chevron.dsrkpi.business.DsrKpiBizService;
import com.chevron.dsrkpi.service.DsrKpiService;
import com.chevron.kpi.dao.KPIConfigMapper;
import com.chevron.kpi.dao.KPIInfoMapper;
import com.chevron.kpi.model.KPIConfig;
import com.chevron.kpi.model.KPIConfigCtrlParams;
import com.chevron.kpi.model.KPIConfigParams;
import com.chevron.kpi.model.KPIInfoListVo;
import com.chevron.kpi.service.KPIService;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.common.util.ResponseStatus;
import com.sys.auth.model.WxTUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/kpi")
public class KPIController {
	public static final Logger log = LoggerFactory.getLogger(KPIController.class);

	@Autowired
	private KPIConfigMapper kpiConfigMapper;

	@Autowired
	private KPIService kPIService;
	
	@Autowired
	private KPIInfoMapper kPIInfoMapper;
	
	@Autowired
	private DsrKpiBizService dsrKpiBizService;

    @Autowired
	private DsrKpiService dsrKpiService;

	@ResponseBody
	@RequestMapping(value = "/getConfigList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getlist(KPIConfigParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", kpiConfigMapper.selectConfig4Pagination(params));
		resultMap.put("total", params.getTotalCount());
		resultMap.put("code", "success");
		resultMap.put("errorMsg", "成功");
		return resultMap;
	}

	@ResponseBody
	@RequestMapping("/config/selectsuggest.do")
	public Map<String, Object> selectSuggest(KPIConfigCtrlParams params) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		List<Map<String, Object>> value = new ArrayList<Map<String, Object>>();
		map.put("value", value);
		WxTUser user = ContextUtil.getCurUser();
		if(params == null){
			params = new KPIConfigCtrlParams();
			return map;
		}
		if(!WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel())){
			params.setPartnerId(user.getOrgId());
		}
		params.setUserId(user.getUserId());
		List<KPIConfig> list = kpiConfigMapper.selectConfigForCtrl(params);
		for (KPIConfig config : list) {
			Map<String, Object> item = new HashMap<String, Object>(2);
			item.put("text", config.getConfigName());
			item.put("value", config.getId());
			value.add(item);
		}
		return map;
	}

	/**
	 * 是否为合伙人用户
	 * @param user 系统用户对象
	 * @return true/false
	 */
//    private boolean isPartnerUser(WxTUser user) {
//        return user!= null && WxTUser.USER_MODEL_SP.equals(user.getUserModel());
//    }
	
	/**
	 * PC端德乐KPI报表饼图查询
	 * @param date
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryKPIInfo.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public JsonResponse queryKPIInfo(String date) {
        JsonResponse map = new JsonResponse();
        //兑换积分
        try {
            return kPIService.queryKPIInfo(null,null,date);
        } catch (Exception e) {
            map.setReponseStatus(ResponseStatus.WARNING);
            map.setErrorMsg(e.getMessage());
            e.printStackTrace();
        }
        return map;
	}
	
	/**
	 * PC端德乐KPI报表list查询
	 * @param vo
	 * @param date
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryKPIInfoList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public JsonResponse queryKPIInfoList(@RequestBody KPIInfoListVo vo) {
        JsonResponse map = new JsonResponse();
        //兑换积分
        try {
            return kPIService.queryKPIInfoList(vo,null,vo.getDate());
        } catch (Exception e) {
            map.setReponseStatus(ResponseStatus.WARNING);
            map.setErrorMsg(e.getMessage());
            e.printStackTrace();
        }
        return map;
	}
	
	/**
	 * PC端德乐KPI dsr信息查询
	 * @param orgCai
	 * @param date
	 * @param page
	 * @param pageSize
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/queryKPIDSRInfoBySalesCai.do", method = { RequestMethod.POST,  RequestMethod.GET})
	public JsonResponse queryKPIDSRInfoBySalesCai(Integer partnerId,String orgCai,String date,Integer page,Integer pageSize) {
        JsonResponse map = new JsonResponse();
        //兑换积分
        try {
            return kPIService.queryKPIDSRInfoBySalesCai(partnerId,orgCai,date,page,pageSize);
        } catch (Exception e) {
            map.setReponseStatus(ResponseStatus.WARNING);
            map.setErrorMsg(e.getMessage());
            e.printStackTrace();
        }
		return map;
	}

	
	@ResponseBody
	@RequestMapping(value = "/getRegionList.do", method = { RequestMethod.POST,  RequestMethod.GET})
	public JsonResponse getRegionList() {
		List<String> regionList = kPIInfoMapper.getRegionList();
		JsonResponse map = new JsonResponse();
		map.setListResult(regionList);
		return map;
	}
	
	@ResponseBody
	@RequestMapping(value = "/pointsAndMail.do", method = { RequestMethod.POST,  RequestMethod.GET})
	public JsonResponse pointsAndMail(String date) {
		JsonResponse map = new JsonResponse();
		//兑换积分
        try {
            date = DateUtil.getDateStr(new SimpleDateFormat("yyyy-MM-dd").parse(date), "yyyy-MM-01");
            dsrKpiBizService.distributePoints(date);
            //手动发放微调积分
            dsrKpiBizService.sendPersonalPoint(date);
        } catch (Exception e) {
            map.setReponseStatus(ResponseStatus.WARNING);
            map.setErrorMsg(e.getMessage());
            e.printStackTrace();
        }
        final String emailDate = new SimpleDateFormat("yyyy-MM").format(DateUtil.parseDate(date,"yyyy-MM-dd"));
		//发送邮件
    	new Thread(){
			public void run() {
				dsrKpiService.sendEmail(emailDate);
			};
		}.start();
		return map;
	}


}
