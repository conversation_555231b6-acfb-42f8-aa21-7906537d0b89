package com.zs.pms.model;

import java.util.Date;

public class WxtZSVehicleGearoilDetail {
    private Long id;

    private String tModelId;

    private String oilName;

    private String oilPartsNo;

    private String waveBoxNo;

    private String waveBoxOilNo;

    private String oilConsumption;

    private String oilTotalPrice;

    private String oilType;

    private String oilViscosity;

    private String oilGrade;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String gettModelId() {
        return tModelId;
    }

    public void settModelId(String tModelId) {
        this.tModelId = tModelId == null ? null : tModelId.trim();
    }

    public String getOilName() {
        return oilName;
    }

    public void setOilName(String oilName) {
        this.oilName = oilName == null ? null : oilName.trim();
    }

    public String getOilPartsNo() {
        return oilPartsNo;
    }

    public void setOilPartsNo(String oilPartsNo) {
        this.oilPartsNo = oilPartsNo == null ? null : oilPartsNo.trim();
    }

    public String getWaveBoxNo() {
        return waveBoxNo;
    }

    public void setWaveBoxNo(String waveBoxNo) {
        this.waveBoxNo = waveBoxNo == null ? null : waveBoxNo.trim();
    }

    public String getWaveBoxOilNo() {
        return waveBoxOilNo;
    }

    public void setWaveBoxOilNo(String waveBoxOilNo) {
        this.waveBoxOilNo = waveBoxOilNo == null ? null : waveBoxOilNo.trim();
    }

    public String getOilConsumption() {
        return oilConsumption;
    }

    public void setOilConsumption(String oilConsumption) {
        this.oilConsumption = oilConsumption == null ? null : oilConsumption.trim();
    }

    public String getOilTotalPrice() {
        return oilTotalPrice;
    }

    public void setOilTotalPrice(String oilTotalPrice) {
        this.oilTotalPrice = oilTotalPrice == null ? null : oilTotalPrice.trim();
    }

    public String getOilType() {
        return oilType;
    }

    public void setOilType(String oilType) {
        this.oilType = oilType == null ? null : oilType.trim();
    }

    public String getOilViscosity() {
        return oilViscosity;
    }

    public void setOilViscosity(String oilViscosity) {
        this.oilViscosity = oilViscosity == null ? null : oilViscosity.trim();
    }

    public String getOilGrade() {
        return oilGrade;
    }

    public void setOilGrade(String oilGrade) {
        this.oilGrade = oilGrade == null ? null : oilGrade.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}