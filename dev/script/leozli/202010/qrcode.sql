/*==============================================================*/
/* Module: QRCODE                                               */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_qrcode_short_url_pool                            */
/*==============================================================*/
create table wx_t_qrcode_short_url_pool (
	id                               bigint                            not null identity,
	qrcode                           nvarchar(64)                               not null,
	url                              nvarchar(256)                              not null,
	short_url                        nvarchar(128)                              not null,
	lock_flag                        nvarchar(64)                                   null,
	create_time                      datetime                                       null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_qrcode_short_url_pool ADD CONSTRAINT uk_qrcode_short_url_pool UNIQUE (qrcode);
CREATE INDEX ix_qrcode_short_url_pool_url ON wx_t_qrcode_short_url_pool (url);

/*==============================================================*/
/* Table: wx_t_cap_track_no_rel                                 */
/*==============================================================*/
create table wx_t_cap_track_no_rel (
	cap_code_id                      bigint                                     not null,
	track_code_id                    bigint                                     not null,
	primary key (cap_code_id, track_code_id)
);
