<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.kpi.dao.KPIConfigItemMapper" >
<resultMap id="BaseResultMap" type="com.chevron.kpi.model.KPIConfigItem" >
	<id column="ID" property="id" jdbcType="BIGINT" />
	<result column="CONFIG_ID" property="configId" jdbcType="BIGINT" />
	<result column="KPI_TYPE_ID" property="kpiTypeId" jdbcType="BIGINT" />
	<result column="KPI_TYPE_CODE" property="kpiTypeCode" jdbcType="NVARCHAR" />
	<result column="KPI_TYPE_NAME" property="kpiTypeName" jdbcType="NVARCHAR" />
	<result column="KPI_TYPE_DESC" property="kpiTypeDesc" jdbcType="NVARCHAR" />
	<result column="KPI_TYPE_UNIT" property="kpiTypeUnit" jdbcType="NVARCHAR" />
	<result column="USE_LOOP" property="useLoop" jdbcType="BIT" />
	<result column="GOOD_VALUE" property="goodValue" jdbcType="NUMERIC" />
	<result column="BETTER_VALUE" property="betterValue" jdbcType="NUMERIC" />
	<result column="BEST_VALUE" property="bestValue" jdbcType="NUMERIC" />
	<result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
	<result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
	<result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
	<result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
	<result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
	<result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
	<result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
</resultMap>
<sql id="Example_Where_Clause" >
	<where >
	<foreach collection="oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause" >
	<where >
	<foreach collection="example.oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List" >
	ID, CONFIG_ID, KPI_TYPE_ID, USE_LOOP, GOOD_VALUE, BETTER_VALUE, BEST_VALUE, DELETE_FLAG,
	ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
</sql>
<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.kpi.model.KPIConfigItemExample" >
	select
	<if test="distinct" >
	distinct
	</if>
	'true' as QUERYID,
	ci.ID, ci.CONFIG_ID, ci.KPI_TYPE_ID,t.CODE KPI_TYPE_CODE, t.NAME KPI_TYPE_NAME,t.DESCRIPTION KPI_TYPE_DESC, t.UNIT KPI_TYPE_UNIT, ci.USE_LOOP, ci.GOOD_VALUE, ci.BETTER_VALUE, ci.BEST_VALUE, ci.DELETE_FLAG,
	ci.ATTRIBUTE1, ci.ATTRIBUTE2, ci.CREATION_TIME, ci.CREATED_BY, ci.LAST_UPDATE_TIME, ci.LAST_UPDATED_BY
	from wx_t_kpi_config_item ci
	left join wx_t_kpi_type t
	on t.ID = ci.KPI_TYPE_ID
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null" >
	order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
	select
	<include refid="Base_Column_List" />
	from wx_t_kpi_config_item
	where ID = #{id,jdbcType=BIGINT}
</select>
<delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
	delete from wx_t_kpi_config_item
	where ID = #{id,jdbcType=BIGINT}
</delete>
<delete id="deleteByExample" parameterType="com.chevron.kpi.model.KPIConfigItemExample" >
	delete from wx_t_kpi_config_item
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.kpi.model.KPIConfigItem" >
	insert into wx_t_kpi_config_item (ID, CONFIG_ID, KPI_TYPE_ID,
	USE_LOOP, GOOD_VALUE, BETTER_VALUE,
	BEST_VALUE, DELETE_FLAG, ATTRIBUTE1,
	ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY)
	values (#{id,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, #{kpiTypeId,jdbcType=BIGINT},
	#{useLoop,jdbcType=BIT}, #{goodValue,jdbcType=NUMERIC}, #{betterValue,jdbcType=NUMERIC},
	#{bestValue,jdbcType=NUMERIC}, #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR},
	#{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT},
	#{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT})
</insert>
<insert id="insertSelective" parameterType="com.chevron.kpi.model.KPIConfigItem" >
	insert into wx_t_kpi_config_item
	<trim prefix="(" suffix=")" suffixOverrides="," >
	<if test="id != null" >
		ID,
	</if>
	<if test="configId != null" >
		CONFIG_ID,
	</if>
	<if test="kpiTypeId != null" >
		KPI_TYPE_ID,
	</if>
	<if test="useLoop != null" >
		USE_LOOP,
	</if>
	<if test="goodValue != null" >
		GOOD_VALUE,
	</if>
	<if test="betterValue != null" >
		BETTER_VALUE,
	</if>
	<if test="bestValue != null" >
		BEST_VALUE,
	</if>
	<if test="deleteFlag != null" >
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null" >
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null" >
		ATTRIBUTE2,
	</if>
	<if test="creationTime != null" >
		CREATION_TIME,
	</if>
	<if test="createdBy != null" >
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null" >
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null" >
		LAST_UPDATED_BY,
	</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides="," >
	<if test="id != null" >
		#{id,jdbcType=BIGINT},
	</if>
	<if test="configId != null" >
		#{configId,jdbcType=BIGINT},
	</if>
	<if test="kpiTypeId != null" >
		#{kpiTypeId,jdbcType=BIGINT},
	</if>
	<if test="useLoop != null" >
		#{useLoop,jdbcType=BIT},
	</if>
	<if test="goodValue != null" >
		#{goodValue,jdbcType=NUMERIC},
	</if>
	<if test="betterValue != null" >
		#{betterValue,jdbcType=NUMERIC},
	</if>
	<if test="bestValue != null" >
		#{bestValue,jdbcType=NUMERIC},
	</if>
	<if test="deleteFlag != null" >
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null" >
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null" >
		#{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null" >
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null" >
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null" >
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null" >
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map" >
	update wx_t_kpi_config_item
	<set >
	<if test="record.id != null" >
		ID = #{record.id,jdbcType=BIGINT},
	</if>
	<if test="record.configId != null" >
		CONFIG_ID = #{record.configId,jdbcType=BIGINT},
	</if>
	<if test="record.kpiTypeId != null" >
		KPI_TYPE_ID = #{record.kpiTypeId,jdbcType=BIGINT},
	</if>
	<if test="record.useLoop != null" >
		USE_LOOP = #{record.useLoop,jdbcType=BIT},
	</if>
	<if test="record.goodValue != null" >
		GOOD_VALUE = #{record.goodValue,jdbcType=NUMERIC},
	</if>
	<if test="record.betterValue != null" >
		BETTER_VALUE = #{record.betterValue,jdbcType=NUMERIC},
	</if>
	<if test="record.bestValue != null" >
		BEST_VALUE = #{record.bestValue,jdbcType=NUMERIC},
	</if>
	<if test="record.deleteFlag != null" >
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null" >
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null" >
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="record.creationTime != null" >
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null" >
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null" >
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null" >
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</set>
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map" >
	update wx_t_kpi_config_item
	set ID = #{record.id,jdbcType=BIGINT},
	CONFIG_ID = #{record.configId,jdbcType=BIGINT},
	KPI_TYPE_ID = #{record.kpiTypeId,jdbcType=BIGINT},
	USE_LOOP = #{record.useLoop,jdbcType=BIT},
	GOOD_VALUE = #{record.goodValue,jdbcType=NUMERIC},
	BETTER_VALUE = #{record.betterValue,jdbcType=NUMERIC},
	BEST_VALUE = #{record.bestValue,jdbcType=NUMERIC},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.chevron.kpi.model.KPIConfigItem" >
	update wx_t_kpi_config_item
	<set >
	<if test="configId != null" >
		CONFIG_ID = #{configId,jdbcType=BIGINT},
	</if>
	<if test="kpiTypeId != null" >
		KPI_TYPE_ID = #{kpiTypeId,jdbcType=BIGINT},
	</if>
	<if test="useLoop != null" >
		USE_LOOP = #{useLoop,jdbcType=BIT},
	</if>
	<if test="goodValue != null" >
		GOOD_VALUE = #{goodValue,jdbcType=NUMERIC},
	</if>
	<if test="betterValue != null" >
		BETTER_VALUE = #{betterValue,jdbcType=NUMERIC},
	</if>
	<if test="bestValue != null" >
		BEST_VALUE = #{bestValue,jdbcType=NUMERIC},
	</if>
	<if test="deleteFlag != null" >
		DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null" >
		ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null" >
		ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null" >
		CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null" >
		CREATED_BY = #{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null" >
		LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null" >
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</set>
	where ID = #{id,jdbcType=BIGINT}
</update>
<update id="updateByPrimaryKey" parameterType="com.chevron.kpi.model.KPIConfigItem" >
	update wx_t_kpi_config_item
	set CONFIG_ID = #{configId,jdbcType=BIGINT},
	KPI_TYPE_ID = #{kpiTypeId,jdbcType=BIGINT},
	USE_LOOP = #{useLoop,jdbcType=BIT},
	GOOD_VALUE = #{goodValue,jdbcType=NUMERIC},
	BETTER_VALUE = #{betterValue,jdbcType=NUMERIC},
	BEST_VALUE = #{bestValue,jdbcType=NUMERIC},
	DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
	where ID = #{id,jdbcType=BIGINT}
</update>
<!--   批量插入考核指标信息 -->
<insert id="insertBatch" parameterType="java.util.List">
	insert into wx_t_kpi_config_item (CONFIG_ID, KPI_TYPE_ID,
	USE_LOOP, GOOD_VALUE, BETTER_VALUE,
	BEST_VALUE, DELETE_FLAG, ATTRIBUTE1,
	ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY)
	values
	<foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.configId,jdbcType=BIGINT},
			#{item.kpiTypeId,jdbcType=BIGINT},
			#{item.useLoop,jdbcType=BIT},
			#{item.goodValue,jdbcType=NUMERIC},
			#{item.betterValue,jdbcType=NUMERIC},
			#{item.bestValue,jdbcType=NUMERIC},
			#{item.deleteFlag,jdbcType=BIT},
			#{item.attribute1,jdbcType=NVARCHAR},
			#{item.attribute2,jdbcType=NVARCHAR},
			#{item.creationTime,jdbcType=TIMESTAMP},
			#{item.createdBy,jdbcType=BIGINT},
			#{item.lastUpdateTime,jdbcType=TIMESTAMP},
			#{item.lastUpdatedBy,jdbcType=BIGINT}
		</trim>
	</foreach>
</insert>
</mapper>