select * from wx_t_vendor_code c
left join (
SELECT distinct [distributor_id], customer_name_cn
      ,[company_code]
  FROM [PP_MID].[dbo].[syn_dw_to_pp_customer] where [company_code] is not null and [company_code]!='NA'
  ) a  on c.distributor_id=a.distributor_id where c.ccn!=a.[company_code]
  
  
 insert into wx_t_vendor_code (distributor_id, vendor, ccn, sold_code,create_user_id, create_time)
 select pe.distributor_id, '',  
 4591,
 pe.sap_code, 1, getdate()
 from wx_t_partner_o2o_enterprise pe 
 where pe.distributor_id=1220
 and not exists (select 1 from wx_t_vendor_code v1 where v1.distributor_id=pe.distributor_id)
 
 SELECT distinct [distributor_id], customer_name_cn
      ,[company_code]
	  --update c set c.company_code='4400'
  FROM [PP_MID].[dbo].[syn_dw_to_pp_customer] c where distributor_id=1372
  
  
 select * from wx_t_vendor_code v1 where v1.distributor_id=1220