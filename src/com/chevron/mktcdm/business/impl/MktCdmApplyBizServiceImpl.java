package com.chevron.mktcdm.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chevron.cdm_mkt.model.pdf.MktPdfInfo;
import com.chevron.cdm_mkt.model.zip.ZipFile;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.mktcdm.business.MktCdmApplyBizService;
import com.chevron.mktcdm.common.CDMConstants;
import com.chevron.mktcdm.common.CDMSignTypeEnum;
import com.chevron.mktcdm.dao.MktCdmApplyMapper;
import com.chevron.mktcdm.model.CdmWorkshopVo;
import com.chevron.mktcdm.model.MktCdmApply;
import com.chevron.mktcdm.model.MktCdmApply.FormStatus;
import com.chevron.mktcdm.model.MktCdmApplyExample;
import com.chevron.mktcdm.model.MktCdmApplyParams;
import com.chevron.mktcdm.model.QueryStoreParams;
import com.chevron.mktci.model.MktCiApply;
import com.chevron.pms.dao.PartnerO2OEnterpriseMapper;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.model.PartnerO2OEnterprise;
import com.chevron.pms.model.PartnerResponsibleVo;
import com.chevron.report.business.ExpenseBizService;
import com.chevron.report.util.ExpenseDetail;
import com.chevron.report.util.ExpenseItem;
import com.chevron.vendorcode.business.VendorCodeBizService;
import com.chevron.vendorcode.model.VendorCode;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.EmailSendUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.file.model.WxAttFile;
import com.sys.file.web.FileManager;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.PartnerView;
import com.sys.utils.dao.SupplierMapper;
import com.sys.utils.model.Supplier;
import com.sys.workflow.model.WorInsExeList;
import com.sys.workflow.model.WorkflowInstance;
import com.sys.workflow.model.WorkflowStep;
import com.sys.workflow.model.WorkflowStepHistory;
import com.sys.workflow.model.WorkflowStepInstance;
import com.sys.workflow.utils.BaseFormBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Wx T Mkt Cdm Apply业务接口实现类
 * <AUTHOR>
 * @version 1.0 2020-03-26 11:54
 */
@Service
public class MktCdmApplyBizServiceImpl extends BaseFormBizService<MktCdmApply> implements MktCdmApplyBizService {
	
	
	@Autowired
	private MktCdmApplyMapper mktCdmApplyMapper;
	
	@Autowired
	DicService dicService;
	@Autowired
	VendorCodeBizService vendorCodeBizService;
	@Autowired
	OperationPermissionBizService operationPermissionBizService;
	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;
	@Autowired
    private SupplierMapper supplierMapper;
	@Resource
	private DicItemVoMapper dicItemVoMapper;
	@Resource
	private PartnerO2OEnterpriseMapper partnerO2OEnterpriseMapper;
	@Resource
	private OrganizationVoMapper organizationVoMapper;
	@Resource
	private PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	@Autowired
    private ExpenseBizService expenseBizService;
	
	@Autowired
	WxTUserMapper wxTUserMapper;
	
	private Logger LOGGER = Logger.getLogger(MktCdmApplyBizServiceImpl.class);

	private static final String CDM_TEMPLATE_LOCAL = "CDM/cdm_store_front_pdf.ftl";

    private static final String CDM_SEMINAR_PDF = "CDM/cdm_seminar_pdf.ftl";

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(MktCdmApply record) throws WxPltException {
		if (record.getStoreId() != null) {
			int count = mktCdmApplyMapper.queryStoreApplying(record.getStoreId());
			if (count > 0) {
				throw new WxPltException("当前门店有正在申请的流程,请审批完成后再申请.");
			}
		}
		if(record.getFormStatus() == null) {
			record.setFormStatus(MktCiApply.FormStatus.FLSR_UNSUBMITED);
		}
		record.setReqUserId(ContextUtil.getCurUserId());
		record.setUpdateUserId(ContextUtil.getCurUserId());
		record.setReqUserName(ContextUtil.getCurUser().getChName());
		record.setCreateTime(DateUtil.getCurrentDate());
		record.setUpdateTime(DateUtil.getCurrentDate());
		if(StringUtils.isBlank(record.getReqNo())) {
			record.setReqNo("TEMP" + System.currentTimeMillis());
		}
		if(record.getFormStatus() == null) {
			record.setFormStatus(MktCdmApply.FormStatus.FLSR_UNSUBMIT);
		}
		mktCdmApplyMapper.insertSelective(record);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(MktCdmApply record) throws WxPltException {
		mktCdmApplyMapper.updateByPrimaryKeySelective(record);
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		MktCdmApplyExample example = new MktCdmApplyExample();
		example.createCriteria().andIdIn(ids).andReqUserIdEqualTo(ContextUtil.getCurUserId())
			.andFormStatusEqualTo(MktCdmApply.FormStatus.FLSR_UNSUBMIT);
		mktCdmApplyMapper.deleteByExample(example);
		//删除流程数据
		deleteWorkflowInstance(ids, ContextUtil.getCurUserId(), DateUtil.getCurrentDate());
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(MktCdmApplyExample example) throws WxPltException {
		mktCdmApplyMapper.deleteByExample(example);
	}

	@Override
	public List<MktCdmApply> queryByExample(MktCdmApplyExample example) throws WxPltException {
		return mktCdmApplyMapper.selectByExample(example);
	}

	@Override
    public List<MktCdmApply> queryByParams(Map<String, Object> params) throws WxPltException {
    	return mktCdmApplyMapper.queryByParams(params);
    }

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateForEditPage(MktCdmApply record) throws WxPltException {
		mktCdmApplyMapper.updateForEditPage(record);
	}
	
	@Override
	public MktCdmApply getBean(Long id) throws WxPltException{
		return mktCdmApplyMapper.selectByPrimaryKey(id);
	}

	@Override
	public Object getFormKey(MktCdmApply form) throws WxPltException {
		return form.getId();
	}

	@Override
	public String getWorkflowCode() throws WxPltException {
		return CDMConstants.CDM_FLOW_CODE;
	}

	@Override
	public MktCdmApply getWorkflowForm(Object formKey, String stepCode, Long executor) throws WxPltException {
		return mktCdmApplyMapper.getWorkflowForm((Long)formKey, stepCode, executor, 
				getWorkflowCode(), "t1.id", "");
	}

	@Override
	public MktCdmApply getWorkflowFormByKey(Object formKey) throws WxPltException {
		// TODO Auto-generated method stub
		return getBean(Long.parseLong(String.valueOf(formKey)));
	}

	
	@Override
	protected void processBeforeSubmit(MktCdmApply form, WorkflowInstance workflowInstance,
			List<WorkflowStepInstance> stepInstances, List<WxTUser> todoUsers, List<WorkflowStepHistory> histories,
			List<WorInsExeList> insExeLists) throws WxPltException {
		// 设置company_code及申请编号
		super.processBeforeSubmit(form, workflowInstance, stepInstances, todoUsers, histories, insExeLists);
		form.setFormStatus(MktCdmApply.FormStatus.FLSR_SUBMIT);
		form.setReqTime(DateUtil.getCurrentDate());
		form.setUpdateTime(DateUtil.getCurrentDate());
        CDMSignTypeEnum type = CDMSignTypeEnum.getSignType(form.getSignType());
        if(type == null){
            throw new WxPltException("请选择申请类型");
        }
        ExpenseItem item = null;
        switch (type) {
            case STORE_FRONT:
                item = ExpenseItem.CONSUMER_SIGNAGE;
                break;
            case SEMINAR:
                item = ExpenseItem.CONSUMER_SEMINARS;
                break;
            case STORE_OTHERS:
                item = ExpenseItem.CONSUMER_OTHERS_ONLINE;
                break;
        }
        ExpenseDetail expenseDetail = expenseBizService.allocateExpense(item,Integer.parseInt(DateUtil.getYear(form.getReqTime())), form.getDistributorId(), form.getQuoteAmount(), null);
        form.setQuoteSparkAmount(expenseDetail.getSparkAmount());
        form.setQuoteOnlineAmount(expenseDetail.getOnlineAmount());
	}
	
	@Override
	protected String getFlowInstanceApplyType(MktCdmApply form, WorkflowInstance flowInstance) throws WxPltException {
		return "CDM_APPLY_" + form.getSignType();
	}
	
	@Override
	protected boolean processAfterAbort(MktCdmApply form, WorkflowInstance flowInstance,
			List<WorkflowStepInstance> allStepInstances) {
		super.processAfterAbort(form, flowInstance, allStepInstances);
		//流程终止
		form.setFormStatus(MktCdmApply.FormStatus.ABORT);
		return true;
	}
	
	@Override
	protected boolean processBeforeReject(MktCdmApply form, WorkflowInstance workflowInstance,
			List<WorkflowStepInstance> allStepInstances, WorkflowStepInstance currentStep,
			WorkflowStepInstance rejectToStep, List<WxTUser> todoUsers, List<WorkflowStepHistory> histories,
			List<WorInsExeList> insExeLists) throws WxPltException {
		boolean flag = super.processBeforeReject(form, workflowInstance, allStepInstances, currentStep, rejectToStep, todoUsers,
				histories, insExeLists);
		//流程状态从价格确认返回提交
		if(form.getFormStatus() >= FormStatus.WORKFLOW_FINISH) {
			for(WorkflowStepInstance stepInstance : allStepInstances) {
				if("Y".equalsIgnoreCase(stepInstance.getWorkflowStep().getExtProperty2())){
					if(!stepInstance.isBefore(rejectToStep)) {
						form.setFormStatus(FormStatus.FLSR_SUBMIT);
						flag = true;
					}
					break;
				}
			}
		}
		if(WorkflowStep.STEP_CODE_REQUEST.equals(rejectToStep.getWorkflowStep().getStepCode())) {
			form.setFormStatus(FormStatus.FLSR_UNSUBMIT);
			flag = true;
		}
		return flag;
	}
	
	@Override
	protected boolean processBeforeRecall(MktCdmApply form, WorkflowInstance workflowInstance,
			List<WorkflowStepInstance> allStepInstances, WorkflowStepInstance currentStep,
			WorkflowStepInstance recallFromStep, List<WxTUser> todoUsers, List<WorkflowStepHistory> histories)
			throws WxPltException {
		boolean flag = super.processBeforeRecall(form, workflowInstance, allStepInstances, currentStep, recallFromStep, todoUsers,
				histories);
		WorkflowStepInstance finishStep = null;
		WorkflowStepInstance finStep = null;
		WorkflowStepInstance costConfirmStep = null;
		for(WorkflowStepInstance stepInstance : allStepInstances) {
			if("Y".equalsIgnoreCase(stepInstance.getWorkflowStep().getExtProperty1())) {
				finishStep = stepInstance;
			}
			if("Y".equalsIgnoreCase(stepInstance.getWorkflowStep().getExtProperty2())) {
				costConfirmStep = stepInstance;
			}
			if("Y".equalsIgnoreCase(stepInstance.getWorkflowStep().getExtProperty3())) {
				finStep = stepInstance;
			}
		}
		//完成步骤撤回
		if((finishStep == null && WorkflowStep.STEP_CODE_END.equals(recallFromStep.getWorkflowStep().getStepCode()) || finishStep == currentStep)){
			form.setFormStatus(finStep != null ? FormStatus.TO_FIN : (costConfirmStep != null ? FormStatus.COST_CONFORMED : FormStatus.FLSR_SUBMIT));
			flag = true;
		}
		//已到财务
		if(finStep == recallFromStep) {
			form.setFormStatus(costConfirmStep != null ? FormStatus.COST_CONFORMED : FormStatus.FLSR_SUBMIT);
			//待办不能导出
			workflowInstance.setInstanceExtProperty5("N");
			flag = true;
		}
		//确认价格步骤撤回
		if(costConfirmStep == currentStep) {
			form.setFormStatus(FormStatus.FLSR_SUBMIT);
			flag = true;
		}
		if(WorkflowStep.STEP_CODE_REQUEST.equals(currentStep.getWorkflowStep().getStepCode())) {
			form.setFormStatus(FormStatus.FLSR_UNSUBMIT);
			flag = true;
		}
		return flag;
	}

	@Override
	protected boolean processBeforeAccept(MktCdmApply form, MktCdmApply oldForm, WorkflowInstance workflowInstance,
			WorkflowStepInstance currentStep, WorkflowStepInstance nextStep,
			List<WorkflowStepInstance> subsequenceStepInstances, List<WxTUser> todoUsers,
			List<WorkflowStepHistory> histories, List<WorInsExeList> insExeLists) throws WxPltException {
		// TODO Auto-generated method stub
		boolean flag = super.processBeforeAccept(form, oldForm, workflowInstance, currentStep, nextStep, subsequenceStepInstances,
				todoUsers, histories, insExeLists);
		//FLSR提交
		if(WorkflowStep.STEP_CODE_REQUEST.equals(currentStep.getWorkflowStep().getStepCode())) {
			form.setFormStatus(FormStatus.FLSR_SUBMIT);
			flag = true;
		}
		//完工确认
		if("Y".equalsIgnoreCase(currentStep.getWorkflowStep().getExtProperty2())) {
			form.setFormStatus(FormStatus.COST_CONFORMED);
            CDMSignTypeEnum type = CDMSignTypeEnum.getSignType(oldForm.getSignType());
            ExpenseItem item = null;
            switch (type) {
                case STORE_FRONT:
                    item = ExpenseItem.CONSUMER_SIGNAGE;
                    break;
                case SEMINAR:
                    item = ExpenseItem.CONSUMER_SEMINARS;
                    break;
                case STORE_OTHERS:
                    item = ExpenseItem.CONSUMER_OTHERS_ONLINE;
                    break;
            }
            ExpenseDetail oldExpense = new ExpenseDetail();
            oldExpense.setSparkAmount(oldForm.getQuoteSparkAmount() == null ? 0d : oldForm.getQuoteSparkAmount());
            oldExpense.setOnlineAmount(oldForm.getQuoteOnlineAmount() == null ? 0d : oldForm.getQuoteOnlineAmount());
            double applyAmount = NumberUtil.add(oldForm.getQuoteSparkAmount(), oldForm.getQuoteOnlineAmount());
            double actualAmount = form.getSettlementAmount() == null ? 0 : form.getSettlementAmount();
            ExpenseDetail expenseDetail = null;
            if (Math.abs(applyAmount - actualAmount) <= 0.0000001) {
                expenseDetail = oldExpense;
            } else if (actualAmount < applyAmount) {
                expenseDetail = ExpenseItem.actualLtApplayReDistribute(oldExpense, actualAmount);
            } else {
                expenseDetail = expenseBizService.allocateExpense(item,
                        Integer.parseInt(DateUtil.getYear(oldForm.getReqTime())), oldForm.getDistributorId(), form.getSettlementAmount(), oldExpense);
            }
            form.setSettlementSparkAmount(expenseDetail.getSparkAmount());
            form.setSettlementOnlineAmount(expenseDetail.getOnlineAmount());
			flag = true;
		}
//		if (Objects.equals(stepCode, CDMConstants.Step.MKT_S_CHECK_DIST_MATERIA)) {
//			form.setFormStatus(MktCdmApply.FormStatus.MKT_S_CHECK_DIST);
//			return true;
//		}
//		if (Objects.equals(stepCode, CDMConstants.Step.FIN_APPROVE)) {
//			form.setFormStatus(MktCdmApply.FormStatus.FIN_APPROVE);
//			return true;
//		}
		//已到财务
		if("Y".equalsIgnoreCase(nextStep.getWorkflowStep().getExtProperty3())) {
			form.setFormStatus(FormStatus.TO_FIN);
			//后续待办能导出PDF
			workflowInstance.setInstanceExtProperty5("Y");
			flag = true;
		}
		//步骤结束，更新门店标记
		if("Y".equalsIgnoreCase(currentStep.getWorkflowStep().getExtProperty1()) || WorkflowStep.STEP_CODE_END.equals(nextStep.getWorkflowStep().getStepCode())) {
			if(oldForm.getFormStatus() < MktCdmApply.FormStatus.WORKFLOW_FINISH) {
				//更新门店店招标记
				if(CDMSignTypeEnum.STORE_FRONT.getCode().equals(oldForm.getSignType()) || CDMSignTypeEnum.STORE_IN_STORE.getCode().equals(oldForm.getSignType())) {
					Date now = DateUtil.getCurrentDate();
					WorkshopMaster workshopMaster = new WorkshopMaster();
					workshopMaster.setId(oldForm.getStoreId());
					//TODO 店招相关待处理
					/*workshopMaster.setShopRecruitment(1);
					workshopMaster.setShopRecruitmentUpdateTime(now);*/
					workshopMaster.setNewExtFlag(1<<12);
					workshopMaster.setExtProperty1(DateUtil.getCurrentDate().getTime() + "");
					workshopMasterBizService.update(workshopMaster);
				}
				//店招
				form.setFormStatus(MktCdmApply.FormStatus.WORKFLOW_FINISH);
				flag = true;
			}
		}
		
		return flag;
	}

	@Override
	public void doSubmit(MktCdmApply form, WorkflowInstance workflowInstance, Long executor, Long actualExecutor)
			throws WxPltException {
		//提交设置流程步骤
		VendorCode vendorCode = vendorCodeBizService.getVendorCodeByDistributorId(form.getDistributorId());
		if (vendorCode == null) {
			throw new WxPltException("未维护该经销商的Company Code");
		}
		form.setCompanyCode(vendorCode.getCcn());
		if(StringUtils.isBlank(form.getReqNo()) || form.getReqNo().startsWith("TEMP")) {
			form.setReqNo(CommonUtil.generateReqNo("PCMO" + form.getCompanyCode()));
		}
		submit(form, workflowInstance, executor, executor, actualExecutor, getStepCodes(form, executor));
		
	}
	
	@Override
	protected boolean processBeforeEnd(MktCdmApply form, MktCdmApply oldForm, WorkflowInstance workflowInstance,
			WorkflowStepInstance currentStep) throws WxPltException {
		boolean flag = super.processBeforeEnd(form, oldForm, workflowInstance, currentStep);
		if(oldForm.getFormStatus() < MktCdmApply.FormStatus.WORKFLOW_FINISH) {
			//更新门店店招标记
			if(CDMSignTypeEnum.STORE_FRONT.getCode().equals(oldForm.getSignType()) || CDMSignTypeEnum.STORE_IN_STORE.getCode().equals(oldForm.getSignType())) {
				Date now = DateUtil.getCurrentDate();
				WorkshopMaster workshopMaster = new WorkshopMaster();
				workshopMaster.setId(oldForm.getStoreId());
				//TODO 店招相关待处理
				/*workshopMaster.setShopRecruitment(1);
				workshopMaster.setShopRecruitmentUpdateTime(now);*/
				workshopMaster.setNewExtFlag(1<<12);
				workshopMaster.setExtProperty1(DateUtil.getCurrentDate().getTime() + "");
				workshopMasterBizService.update(workshopMaster);
			}
			//店招
			form.setFormStatus(MktCdmApply.FormStatus.WORKFLOW_FINISH);
			flag = true;
		}
		return flag;
	}
	
	private boolean greaterLimit(Double amount, Map<String, DicItemVo> flowConfig) {
		DicItemVo itemVo = flowConfig.get(CDMConstants.QUOTE_LIMIT);
		if (itemVo == null) {
			return false;
		}
		Double limitDouble = Double.parseDouble(itemVo.getDicItemName());
		return amount.compareTo(limitDouble) >= 0;
	}
	
	private Map<String, DicItemVo> getFlowConfig(){
		List<DicItemVo> itemVos = dicItemVoMapper.selectByCode(CDMConstants.CDM_FLOW_CONFIG);
		Map<String, DicItemVo> result = new HashMap<String, DicItemVo>(itemVos.size());
		for(DicItemVo itemVo : itemVos) {
			result.put(itemVo.getDicItemCode(), itemVo);
		}
		return result;
	}
	
	private String[] getStepCodes(MktCdmApply form, Long executor) throws WxPltException {
		CDMSignTypeEnum typeEnum = CDMSignTypeEnum.getSignType(form.getSignType());
		List<String> list = new ArrayList<String>();
		switch (typeEnum) {
		case STORE_FRONT:
			storeFlow(list, form, executor);
			break;
		case STORE_IN_STORE:
			storeFlow(list, form, executor);
			break;
		case SEMINAR:
			meetingFlow(list, form, executor);
			break;
		case STORE_OTHERS:
			list.add(CDMConstants.Step.ASM_APPROVE);
			list.add(CDMConstants.Step.ABM_APPROVE);
			list.add(CDMConstants.Step.MKT_M_APPROVE);
			break;

		default:
			throw new WxPltException("申请类型不匹配");
		}
		return list.toArray(new String[list.size()]);
	}
	
	/**
	 * 店招(店中店)流程
	 * @param list
	 * @param form
	 * @throws WxPltException
	 */
	private void storeFlow(List<String> list, MktCdmApply form, Long executor)throws WxPltException {
		boolean flag = greaterLimit(form.getQuoteAmount(), getFlowConfig());
//		if (CDMConstants.LOCAL_Y.equals(form.getLocalMake()) && flag) {
//			throw new WxPltException("当地制作预算须小于2W");
//		} 
		Map<String, Object> params = new HashMap<String, Object>(2);
		params.put("salesId", executor);
		boolean hasTeamLeader = (wxTUserMapper.getChevronUserFlag(params) & 1) > 0;
		if(hasTeamLeader) {
			list.add(CDMConstants.Step.TEAM_LEADER_APPROVE_SIGNAGE);
		}
		list.add(CDMConstants.Step.ASM_APPROVE_SIGNAGE);
		if (flag) {
			list.add(CDMConstants.Step.ABM_APPROVE_SIGNAGE);
		}
		list.add(CDMConstants.Step.MKT_S_APPROVE_SIGNAGE);
		list.add(CDMConstants.Step.MKT_M_APPROVE_SIGNAGE);
		//当地制作为经销商 非当地制作为供应商
		if (CDMConstants.LOCAL_Y.equals(form.getLocalMake())) {
			list.add(CDMConstants.Step.DIST_SUBMIT_MATERIAL);
			list.add(CDMConstants.Step.FLSR_CHECK_DIST_MATERIAL_LOCAL);
			if(hasTeamLeader) {
				list.add(CDMConstants.Step.TEAM_LEADER_CHECK_DIST_MATERIAL_LOCAL);
			}
			list.add(CDMConstants.Step.ASM_CHECK_DIST_MATERIAL_LOCAL);
			list.add(CDMConstants.Step.MKT_S_CHECK_DIST_MATERIA_LOCAL);
			list.add(CDMConstants.Step.MKT_M_CHECK_DIST_MATERIA_LOCAL);
			list.add(CDMConstants.Step.FIN_APPROVE_LOCAL);
		} else {
			list.add(CDMConstants.Step.PROVIDER_SUBMIT_MATERIAL);
			list.add(CDMConstants.Step.FLSR_CHECK_DIST_MATERIAL);
			if(hasTeamLeader) {
				list.add(CDMConstants.Step.TEAM_LEADER_CHECK_DIST_MATERIAL);
			}
			list.add(CDMConstants.Step.ASM_CHECK_DIST_MATERIAL);
			list.add(CDMConstants.Step.MKT_S_CHECK_DIST_MATERIA);
			list.add(CDMConstants.Step.MKT_M_CHECK_DIST_MATERIA);
			list.add(CDMConstants.Step.FIN_APPROVE);
		}

	}
	
	private void meetingFlow(List<String> list, final MktCdmApply form, Long executor) {
		Map<String, Object> params = new HashMap<String, Object>(2);
		params.put("salesId", executor);
		boolean hasTeamLeader = (wxTUserMapper.getChevronUserFlag(params) & 1) > 0;
		if(hasTeamLeader) {
			list.add(CDMConstants.Step.TEAM_LEADER_APPROVE);
		}
		list.add(CDMConstants.Step.ASM_APPROVE);
		Map<String, DicItemVo> flowConfig = getFlowConfig();
		boolean flag = greaterLimit(form.getQuoteAmount(), flowConfig);
		if (flag) {
			list.add(CDMConstants.Step.ABM_APPROVE);
		}
		list.add(CDMConstants.Step.MKT_S_APPROVE);
		list.add(CDMConstants.Step.MKT_M_APPROVE);
		//当地制作需要经销商提交材料并审核
		if (CDMConstants.LOCAL_Y.equals(form.getLocalMake())) {
			list.add(CDMConstants.Step.DIST_SUBMIT_MATERIAL_SEMINARS);
			list.add(CDMConstants.Step.FLSR_CHECK_DIST_MATERIAL_SEMINARS);
			if(hasTeamLeader) {
				list.add(CDMConstants.Step.TEAM_LEADER_CHECK_DIST_MATERIAL_SEMINARS);
			}
			list.add(CDMConstants.Step.ASM_CHECK_DIST_MATERIAL_SEMINARS);
			list.add(CDMConstants.Step.MKT_S_CHECK_DIST_MATERIA_SEMINARS);
			list.add(CDMConstants.Step.MKT_M_CHECK_DIST_MATERIA_SEMINARS);
			list.add(CDMConstants.Step.FIN_APPROVE_SEMINARS);
		}
		//大型研讨会，发送发放标准研讨会礼包提醒邮件
		DicItemVo bigSeminarsConfig = flowConfig.get(CDMConstants.QUOTE_BIG_SEMINARS);
		if(bigSeminarsConfig != null && form.getQuoteAmount() > Double.parseDouble(bigSeminarsConfig.getDicItemName())) {
			final Long operator = ContextUtil.getCurUserId();
			LogUtils.addLog(new LogTask() {
				
				@Override
				public void execute() throws Exception {
					try {
						Map<String, Object> paramsMap = new HashMap<String, Object>(3);
						paramsMap.put("distributorId", form.getDistributorId());
						List<PartnerView> list = organizationVoMapper.selectPartnersByParams(paramsMap);
						if(list.size() > 1) {
							throw new WxPltException("找到多个匹配经销商" + form.getDistributorName() + 
									"(" + form.getDistributorId() + ")");
						}
						PartnerView partnerView = list.get(0);
						if(partnerView.getExtFlag() != null && (partnerView.getExtFlag() & 1) > 0) {
							//已发送过标准研讨会礼包
							return;
						}
						paramsMap = new HashMap<String, Object>(3);
						paramsMap.put("funFlag", "email_consumer_standard_seminars_package");
						paramsMap.put("distributorId", form.getDistributorId());
						List<PartnerResponsibleVo> partnerResponsibleVos = partnerResponsibleVoMapper.queryPartnerResponsibleByParams(paramsMap);
						if(partnerResponsibleVos.isEmpty()) {
							throw new WxPltException("经销商" + form.getDistributorName() + "(" + form.getDistributorId() + ")未找到");
						}
						Set<String> toEmailList = new HashSet<String>(partnerResponsibleVos.size());
						StringBuilder toName = null;
						Set<String> ccEmailList = new HashSet<String>();
						Map<String, Object> contentMap = new HashMap<String, Object>();
						for(PartnerResponsibleVo vo : partnerResponsibleVos){
							if(StringUtils.isBlank(vo.getResponsiblePersonEmail())) {
								continue;
							}
							toEmailList.add(vo.getResponsiblePersonEmail());
							if(toName == null) {
								toName = new StringBuilder();
							}else {
								toName.append(" & ");
							}
							toName.append(vo.getResponsiblePersonName());
							vo.setPartnerId(partnerView.getId());
							String ccs = vo.getDayReportCc(Constants.SALES_CHANNEL_CDM);
							if(StringUtils.isNotBlank(ccs)) {
								for(String cc : ccs.split(",")) {
									if(StringUtils.isNotBlank(cc)) {
										ccEmailList.add(cc);
									}
								}
							}
						}
						if(toEmailList.isEmpty()) {
							throw new WxPltException("经销商" + form.getDistributorName() + "(" + form.getDistributorId() + ")未找到发送邮箱");
						}
						contentMap.put("toName", toName.toString());
						contentMap.put("distributorName", partnerView.getName());
						contentMap.put("form", form);
						
						WebApplicationContext webApplicationContext = ContextLoader
								.getCurrentWebApplicationContext();
						ServletContext servletContext = webApplicationContext
								.getServletContext();
						if(!EmailSendUtils.sendEmailForListContent(
								servletContext,
								toEmailList.toArray(new String[toEmailList.size()]),
								ccEmailList.isEmpty() ? null : ccEmailList.toArray(new String[ccEmailList.size()]),
								"第一次申请大型研讨会发送礼包通知提醒", contentMap, null, "CDM/big_seminars_package_notify.ftl")){
							throw new WxPltException("经销商" + form.getDistributorName() + "(" + form.getDistributorId() + ")邮件发送失败");
						}
						LogUtils.addInfoLog(operator, "sendStandardSeminarsPackage", "经销商" + form.getDistributorName() + "(" + form.getDistributorId() + ")邮件发送成功");
						//更新经销商标准研讨会礼包已发送标记
						PartnerO2OEnterprise partnerO2OEnterprise = new PartnerO2OEnterprise();
						partnerO2OEnterprise.setPartnerId(partnerView.getId());
						partnerO2OEnterprise.setNewExtFlag(1);
						partnerO2OEnterprise.setLastUpdatedBy(operator);
						partnerO2OEnterprise.setLastUpdateTime(DateUtil.getCurrentDate());
						if(partnerO2OEnterpriseMapper.updateByPartnerIdSelective(partnerO2OEnterprise) < 0) {
							throw new WxPltException("经销商" + form.getDistributorName() + "(" + form.getDistributorId() + ")更新已发送标记失败");
						}
						
					} catch (WxPltException e) {
						LogUtils.addErrorLog(operator, "sendStandardSeminarsPackage", e.getMessage(), null);
					} catch (Exception e) {
						LogUtils.addErrorLog(operator, "sendStandardSeminarsPackage", e.getMessage(), null);
						LOGGER.error(e.getMessage(), e);
					}
				}
			});
			
		}
		
	}
	
	
	@Override
	protected String processExecutorGroupSql(String conditionSql, MktCdmApply form) throws WxPltException {
		// TODO Auto-generated method stub
		if (form.getSupplierId() != null) {
			conditionSql = conditionSql.replace("$supplierId$", String.valueOf(form.getSupplierId()));
		}
		if (StringUtils.isNotBlank(form.getCompanyCode())) {
			conditionSql = conditionSql.replace("$officeCode$", form.getCompanyCode());
		}

		return super.processExecutorGroupSql(conditionSql, form);
	}

	@Override
	protected String getPartnerIdForTodoUser(MktCdmApply form) throws WxPltException {
		return getPartnerIdByDistributorId(form.getDistributorId());
	}

	@Override
	protected String getDistributorIdForTodoUser(MktCdmApply form,WorkflowStepInstance stepInstance) throws WxPltException {
		return form.getDistributorId().toString();
	}

	@Override
	protected String getSalesChannelForTodoUser(MktCdmApply form) throws WxPltException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void queryDraftForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryDraftForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public void queryTodoForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryTodoForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		
	}

	@Override
	public void queryDoneForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryDoneForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		
	}

	@Override
	public void queryAllApplyForPage(MktCdmApplyParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, queryAllApplyForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		
	}

	@Override
	public List<MktCdmApply> queryDraftForPage(MktCdmApplyParams params) throws WxPltException {
		params.setActualExecutor(ContextUtil.getCurUserId());
		if(params.getExecutor() == null) {
			params.setExecutor(params.getActualExecutor());
		}
		return mktCdmApplyMapper.queryDraftForPage(params);
	}

	@Override
	public List<MktCdmApply> queryTodoForPage(MktCdmApplyParams params) throws WxPltException {
		params.setActualExecutor(ContextUtil.getCurUserId());
		if(params.getExecutor() == null) {
			params.setExecutor(params.getActualExecutor());
		}
		List<MktCdmApply> list = mktCdmApplyMapper.queryTodoForPage(params);
		exportPermission(list);
		return list;
	}

	@Override
	public List<MktCdmApply> queryDoneForPage(MktCdmApplyParams params) throws WxPltException {
		params.setActualExecutor(ContextUtil.getCurUserId());
		if(params.getExecutor() == null) {
			params.setExecutor(params.getActualExecutor());
		}
		List<MktCdmApply> list = mktCdmApplyMapper.queryDoneForPage(params);
		exportPermission(list);
		return list;
	}

	

	@Override
	public List<MktCdmApply> queryAllApplyForPage(MktCdmApplyParams params) throws WxPltException {
		List<MktCdmApply> list = mktCdmApplyMapper.queryAllApplyForPage(params);
		exportPermission(list);
		return list;
	}	
	
	private void exportPermission(List<MktCdmApply> list)  throws WxPltException{
		if (CollectionUtil.isEmpty(list)) {
			return;
		}
		int permission = operationPermissionBizService.getPermissionWeight(ContextUtil.getCurUserId(), "Signage.CDM.apply");
		if ((permission & 2) <= 0) {
			return;
		}
		for (MktCdmApply apply : list) {
			if (apply.getFormStatus() >= FormStatus.TO_FIN) {
				apply.setExportPdf("Y");
			}
		}
	}

	@Override
	public List<CdmWorkshopVo> queryStorePage(QueryStoreParams params)throws WxPltException {
		return mktCdmApplyMapper.queryCdmStorePage(params);
		
	}

	@Override
	public void queryStorePage(QueryStoreParams params, Map<String, Object> resultMap) throws WxPltException{
		resultMap.put(Constants.RESULT_LST_KEY, queryStorePage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		
	}

	@Override
	public Integer queryStoreApplyCount(Long storeId) throws WxPltException {
		return mktCdmApplyMapper.queryStoreApplyCount(storeId);
	}
	@Override
	public MktPdfInfo getPdfInfo(Long id) throws WxPltException {
        MktPdfInfo mktPdfInfo = new MktPdfInfo();
        MktCdmApplyExample example = new MktCdmApplyExample();
        example.createCriteria()
                .andIdEqualTo(id)
                .andFormStatusIn(Arrays.asList(MktCdmApply.FormStatus.WORKFLOW_FINISH,MktCdmApply.FormStatus.TO_FIN));
        List<MktCdmApply> list = mktCdmApplyMapper.selectByExample(example);

        if (CollectionUtil.isEmpty(list)) {
            throw new WxPltException("未查询到店招信息或未完成审核");
        }
        MktCdmApply apply = list.get(0);
        Dict paramsMap = new Dict();
        if(apply.getSupplierId()!=null){
            Supplier supplier = supplierMapper.selectByPrimaryKey(apply.getSupplierId());
            if(supplier!=null){
                paramsMap.put("supplierName", supplier.getSupplierName());
            }
        }
        CDMSignTypeEnum signType = CDMSignTypeEnum.getSignType(apply.getSignType());
        paramsMap.put("localMake", apply.getLocalMake());
        paramsMap.put("reqNo", apply.getReqNo());
        paramsMap.put("distributorName", apply.getDistributorName());
        paramsMap.put("reqUserName", apply.getReqUserName());
        paramsMap.put("reqTime", DateUtil.getDateStr(apply.getReqTime(), DateUtil.DEFAULT_PATTERN));
        paramsMap.put("signType", signType == null ? "" : signType.getName());
        paramsMap.put("signTypeCode", apply.getSignType());
        VendorCode vendorCode = vendorCodeBizService.getVendorCodeByDistributorId(apply.getDistributorId());
        paramsMap.put("vendorCode", vendorCode.getVendor());
        paramsMap.put("ccn", vendorCode.getCcn());
        paramsMap.put("storeName", apply.getStoreName());
        JSONObject storeJson = JSONObject.parseObject(apply.getApplyBaseInfo());
        paramsMap.put("storeAddress", storeJson.getString("storeProvince") + storeJson.getString("storeCity") + storeJson.getString("storeAddress"));
        paramsMap.put("storeContactPerson", storeJson.getString("storeContacts"));
        paramsMap.put("storeContactPhone", storeJson.getString("storeContact"));
        paramsMap.put("province", storeJson.getString("storeProvince"));
        paramsMap.put("city", storeJson.getString("storeCity"));
        paramsMap.put("district", storeJson.getString(""));
        paramsMap.put("storeFlagship", storeJson.getString("storeFlagship"));
        paramsMap.put("storeType", storeJson.getString("storeType"));
        paramsMap.put("signboardLocalProduction", "Y".equals(apply.getLocalMake())?"是":"否");
        String signboardHeight = storeJson.getString("signboardHeight");
        String signboardWidth = storeJson.getString("signboardWidth");
        paramsMap.put("boardLength", signboardHeight);
        paramsMap.put("boardWidth", signboardWidth);
        if(StringUtils.isNotBlank(signboardHeight) && StringUtils.isNotBlank(signboardWidth) ){
            paramsMap.put("boardArea", NumberUtil.div(signboardHeight,signboardWidth,1).toString());
        }
        paramsMap.put("otherApplyReason", storeJson.getString("otherApplyReason"));
        paramsMap.put("completeTime", DateUtil.getDateStr(apply.getCompleteTime(), DateUtil.DEFAULT_PATTERN));
        paramsMap.put("quoteAmount",apply.getQuoteAmount());
        paramsMap.put("settlementAmount",apply.getSettlementAmount());
        paramsMap.put("signboardExplanation", storeJson.getString("storeJson"));
        paramsMap.put("supplierContact", storeJson.getString("otherSupplierConcacts"));
        paramsMap.put("supplierContactPhone", storeJson.getString("otherSupplierConcact"));
        JSONArray conferenceEstimatedDate = storeJson.getJSONArray("conferenceEstimatedDate");
        if(conferenceEstimatedDate!=null && conferenceEstimatedDate.size() > 0){
            Date startDate = DateUtil.parseDate(conferenceEstimatedDate.getString(0).replace("Z", " UTC"), "yyyy-MM-dd'T'HH:mm:ss.SSS Z");
            paramsMap.put("meetingStartTime",DateUtil.getDateStr(startDate, DateUtil.DEFAULT_DATE_PATTERN));
        }
        if(conferenceEstimatedDate!=null && conferenceEstimatedDate.size() > 1){
            Date endDate = DateUtil.parseDate(conferenceEstimatedDate.getString(1).replace("Z", " UTC"), "yyyy-MM-dd'T'HH:mm:ss.SSS Z");
            paramsMap.put("meetingEndTime",DateUtil.getDateStr(endDate, DateUtil.DEFAULT_DATE_PATTERN));
        }
        paramsMap.put("conferenceNumberOfPeople",storeJson.getString("conferenceNumberOfPeople"));
        paramsMap.put("conferencePlace",storeJson.getString("conferencePlace"));
        paramsMap.put("conferenceEstimatedPurchaseVolume",storeJson.getString("conferenceEstimatedPurchaseVolume"));
        paramsMap.put("conferenceEstimatedSNPurchaseVolume",storeJson.getString("conferenceEstimatedSNPurchaseVolume"));
        paramsMap.put("conferenceActualPurchaseVolume",storeJson.getString("conferenceActualPurchaseVolume"));

        paramsMap.put("isCarQr", "N");
        paramsMap.put("isOrigCar", "N");
        paramsMap.put("isOrigSign", "N");
        paramsMap.put("isQuote", "N");
        paramsMap.put("isCostDetail", "N");
        paramsMap.put("isPreview", "N");
        paramsMap.put("isDesign", "N");
        paramsMap.put("isDesignDes", "N");
        paramsMap.put("isApplyFormFiles", "N");
        paramsMap.put("isInvoiceFiles", "N");
        paramsMap.put("isConfirmProofFiles", "N");
        paramsMap.put("isTripleAgreementFiles", "N");
        paramsMap.put("isPaymentProofFiles", "N");
        paramsMap.put("isCompletionAttFiles", "N");
        paramsMap.put("isCarAdQuali", "N");
        paramsMap.put("isInvoiceConfirm", "N");
        paramsMap.put("paymentApply", "N");
        paramsMap.put("ARIBAOrder", "N");
        paramsMap.put("isMeetingFlowFiles", "N");
        paramsMap.put("isMeetingLocalFiles", "N");
        paramsMap.put("isMeetingSignFiles", "N");
        if (StringUtils.isNotBlank(apply.getApplyAttFiles())) {
            JSONObject applyFilesJsonObject = JSONObject.parseObject(apply.getApplyAttFiles());
            JSONArray origArray = applyFilesJsonObject.getJSONArray("attOriginalSignboard");
            if (origArray != null && origArray.size() > 0) {
                paramsMap.put("isOrigSign", "Y");
            }

            JSONArray origCar = applyFilesJsonObject.getJSONArray("attAppliedVehicle");
            if (origCar != null && origCar.size() > 0) {
                paramsMap.put("isOrigCar", "Y");
            }

            JSONArray carQr = applyFilesJsonObject.getJSONArray("attCarQrcode");
            if (carQr != null && carQr.size() > 0) {
                paramsMap.put("isCarQr", "Y");
            }

            JSONArray applyFormFiles = applyFilesJsonObject.getJSONArray("applyFormFiles");
            if (applyFormFiles != null && applyFormFiles.size() > 0) {
                paramsMap.put("isApplyFormFiles", "Y");
            }

            JSONArray completionAttFiles = applyFilesJsonObject.getJSONArray("completionAttFiles");
            if (completionAttFiles != null && completionAttFiles.size() > 0) {
                paramsMap.put("isCompletionAttFiles", "Y");
            }


            JSONArray attAribaPaymentApplication = applyFilesJsonObject.getJSONArray("attAribaPaymentApplication");
            if (completionAttFiles != null && attAribaPaymentApplication.size() > 0) {
                paramsMap.put("paymentApply", "Y");
            }

            if (StringUtils.isNotBlank(storeJson.getString("designDes"))) {
                paramsMap.put("isDesignDes", "Y");
            }

            JSONArray attPurchaseProof = applyFilesJsonObject.getJSONArray("attPurchaseProof");
            if (attPurchaseProof != null && attPurchaseProof.size() > 0) {
                paramsMap.put("isPaymentProofFiles", "Y");
            }

            JSONArray attSeminarPhotos = applyFilesJsonObject.getJSONArray("attSeminarPhotos");
            if (attSeminarPhotos != null && attSeminarPhotos.size() > 0) {
                paramsMap.put("isMeetingLocalFiles", "Y");
            }
            JSONArray attSeminarProcess = applyFilesJsonObject.getJSONArray("attSeminarProcess");
            if (attSeminarProcess != null && attSeminarProcess.size() > 0) {
                paramsMap.put("isMeetingFlowFiles", "Y");
            }
            JSONArray attSeminarRegistrationForm = applyFilesJsonObject.getJSONArray("attSeminarRegistrationForm");
            if (attSeminarRegistrationForm != null && attSeminarRegistrationForm.size() > 0) {
                paramsMap.put("isMeetingSignFiles", "Y");
            }

        }


        if (StringUtils.isNotBlank(apply.getQuoteAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getQuoteAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isQuote", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getSealQuoteAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getSealQuoteAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isCostDetail", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getInvoiceFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getInvoiceFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isInvoiceFiles", "Y");
            }
        }
        if (StringUtils.isNotBlank(apply.getCompleteConfirmFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getCompleteConfirmFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isConfirmProofFiles", "Y");
            }
        }
        if (StringUtils.isNotBlank(apply.getTripleAgreementFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getTripleAgreementFiles());
            if (jsonArray != null && jsonArray.size() > 0) {

                paramsMap.put("isTripleAgreementFiles", "Y");
            }
        }
        if (StringUtils.isNotBlank(apply.getPaymentFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getPaymentFiles());
            if (jsonArray != null && jsonArray.size() > 0) {

                paramsMap.put("isPaymentProofFiles", "Y");
            }
        }
        if (StringUtils.isNotBlank(apply.getInvoiceCheckFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getInvoiceCheckFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isInvoiceConfirm", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getAribaOrderFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getAribaOrderFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("ARIBAOrder", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getDesignAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getDesignAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isDesign", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getMeetingSignFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getMeetingSignFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isMeetingSignFiles", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getMeetingFlowFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getMeetingFlowFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isMeetingFlowFiles", "Y");
            }
        }

        if (StringUtils.isNotBlank(apply.getMeetingLocalFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getMeetingLocalFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                paramsMap.put("isMeetingLocalFiles", "Y");
            }
        }

        List<WorkflowStepHistory> historyList = getLatestStepHistoryByStep(String.valueOf(id));
        if (CollectionUtil.isNotEmpty(historyList)) {

            paramsMap.put("historyList", historyList);
        }
        mktPdfInfo.setParams(paramsMap);
        if(CDMSignTypeEnum.SEMINAR.equals(signType)){
            mktPdfInfo.setTemplateName(CDM_SEMINAR_PDF);
        }else{
            mktPdfInfo.setTemplateName(CDM_TEMPLATE_LOCAL);
        }

        mktPdfInfo.setExportFileName("批复证明_" + DateUtil.toDateStrNew(new Date(), 9));
        mktPdfInfo.setZipFileList(getAttFileIdList(apply));
        return mktPdfInfo;
    }


    private List<ZipFile> getAttFileIdList(MktCdmApply apply) {
        List<ZipFile> fileList = new ArrayList<ZipFile>();
        if (StringUtils.isNotBlank(apply.getApplyAttFiles()) && apply.getApplyAttFiles().startsWith("{")) {
            JSONObject json = JSONObject.parseObject(apply.getApplyAttFiles());
            getAttIdByKey(json, fileList, "attOriginalSignboard");
            getAttIdByKey(json, fileList, "attAppliedVehicle");
            getAttIdByKey(json, fileList, "attCarQrcode");
            getAttIdByKey(json, fileList, "applyFormFiles");
            getAttIdByKey(json, fileList, "completionAttFiles");
            getAttIdByKey(json, fileList, "attAribaPaymentApplication");
            getAttIdByKey(json, fileList, "attSeminarPhotos");
            getAttIdByKey(json, fileList, "attSeminarProcess");
            getAttIdByKey(json, fileList, "attSeminarRegistrationForm");
            getAttIdByKey(json, fileList, "attPurchaseProof");
        }


        if (StringUtils.isNotBlank(apply.getQuoteAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getQuoteAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }

        if (StringUtils.isNotBlank(apply.getSealQuoteAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getSealQuoteAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }

        if (StringUtils.isNotBlank(apply.getDesignAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getDesignAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }

        if (StringUtils.isNotBlank(apply.getInvoiceFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getInvoiceFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }
        if (StringUtils.isNotBlank(apply.getCompleteConfirmFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getCompleteConfirmFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }
        if (StringUtils.isNotBlank(apply.getTripleAgreementFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getTripleAgreementFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }
        if (StringUtils.isNotBlank(apply.getPaymentFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getPaymentFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }
        if (StringUtils.isNotBlank(apply.getInvoiceCheckFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getInvoiceCheckFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }

        if (StringUtils.isNotBlank(apply.getDesignAttFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getDesignAttFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }

        if (StringUtils.isNotBlank(apply.getAribaOrderFiles())) {
            JSONArray jsonArray = JSONArray.parseArray(apply.getAribaOrderFiles());
            if (jsonArray != null && jsonArray.size() > 0) {
                fileList.addAll(getAttFiles(jsonArray));
            }
        }

        return fileList;
    }


    private void getAttIdByKey(JSONObject oriJson, List<ZipFile> attFileList, String key) {
        if (oriJson.containsKey(key)) {
            JSONArray origArray = oriJson.getJSONArray(key);
            attFileList.addAll(getAttFiles(origArray));
        }
    }

    private List<ZipFile> getAttFiles(JSONArray array) {
        List<ZipFile> fileList = new ArrayList<ZipFile>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);
                if (json != null && json.containsKey("storePath") && json.containsKey("storageName")) {
                    ZipFile file = new ZipFile();
                    file.setFileDir(FileManager.fileUploadPath + FileManager.storePathFolderMap.get(WxAttFile.SourceType_WORKFLOW_ATT) + json.getString("storePath")
                            + File.separator + json.getString("storageName"));
                    file.setFileName(json.getString("name"));
                    fileList.add(file);
                }
            }
        }
        return fileList;
    }

	@Override
	protected String getRequestNo(MktCdmApply form, WorkflowInstance flowInstance) throws WxPltException {
		return form.getReqNo();
	}

	@Override
	protected void buildApplyOwnerInfoBeforeSubmit(MktCdmApply form, WorkflowInstance flowInstance)
			throws WxPltException {
		buildDistributorInfoBeforeSubmit(flowInstance, form.getDistributorId() == null ? null : form.getDistributorId().toString(), form.getDistributorName());
	}
}
