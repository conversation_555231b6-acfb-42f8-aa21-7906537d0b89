页面参数：
executor: 被代理人用户ID

1.获取列表页面权限 RPC
operationPermissionService.getOperationPermissionByUser(Long userId, String moduleCode)
参数：
	userId: 被代理人用户ID/null
	moduleCode：EliteFundV2.apply
返回：
	weight：1-提交申请权限。页面中包含草稿选项卡、创建申请按钮
			2-BS区域Excel汇总导出
			4-下载发票通知文件

2. 获取申请步骤信息（包含申请页面操作权限和字段权限）RPC
v2EliteFundFormService.getRequestStep()
返回：
	data：WorkflowStep

3. 临时保存表单 RPC
v2EliteFundFormService.save(V2EliteFundForm record, Long executor)

4. 提交/通过 RPC
v2EliteFundFormService.accept(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数：
	form:V2EliteFundForm。非可编辑页面传{id:xxx}只需传ID
	remark:通过备注。可编辑页面没有备注传null
	executor:被代理人
	versionNo：流程版本号。申请页面传null。其他form.workflowInstance.versionNo
	
5. 获取表单明细 RPC
v2EliteFundFormService.detail(Long id, String stepCode, Long executor)
参数：
	id: 表单ID
	stepCode: 流程当前步骤编码
	executor：被代理人用户ID
返回：
	currentStep：WorkflowStep
	form: V2EliteFundForm
	form.workflowInstance.versionNo：版本号
	form.workflowInstance.acceptFlag：通过按钮标记。1-有权限，0-无权限
	form.workflowInstance.rejectFlag：驳回按钮标记。1-有权限，0-无权限
	form.workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	form.workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）


6. 待办列表 POST
/v2elitefundform/tododata.do?limit=10&start=0&field=executeTime&direction=desc&executor=129915
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].workflowInstance.latestStepHistory.actionName 状态。resultLst[0].workflowInstance.latestStepHistory为null时显示草稿
	resultLst[0].workflowInstance.acceptFlag：通过按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.rejectFlag：驳回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）

7. 已办列表 POST
/v2elitefundform/donedata.do?limit=10&start=0&field=executeTime&direction=desc&executor=122995
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）
	
8. 全部列表 POST
/v2elitefundform/alldata.do?limit=10&start=0&field=executeTime&direction=desc
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）

9. 流程办理历史 RPC
v2EliteFundFormService.getWorkflowStepHistory(String id)
参数：
	id：表单ID
返回：
	data：List<WorkflowStepHistory>
	data[0].workflowStep.stepName：步骤名称
	data[0].executorName：执行人姓名
	data[0].executor：执行人ID
	data[0].actualExecutorName：代办人姓名（actualExecutor不为空且不等于executor时有效）
	data[0].actualExecutor：代办人ID（actualExecutor不为空且不等于executor时有效）
	data[0].approveStatusText：办理状态
	data[0].executeTime：执行时间
	data[0].approveRemark：审批备注

10. 流程图 RPC
v2EliteFundFormService.getWorkflowStepInstances(String id)
参数：
	id：表单ID
返回：
	data：List<WorkflowStepInstance>
	data[0].workflowStep.stepName：步骤名称
	data[0].worInsExeList：List<WorInsExeList> 办理人列表
	data[0].worInsExeList[0].executor：执行人ID
	data[0].worInsExeList[0].executorName：执行人姓名
	data[0].worInsExeList[0].actualExecutorName：代办人姓名（actualExecutor不为空且不等于executor时有效）
	data[0].worInsExeList[0].actualExecutor：代办人ID（actualExecutor不为空且不等于executor时有效）

11. 驳回 RPC
v2EliteFundFormService.reject(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数：
	form:表单对象。只需包含表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

12. 撤回 RPC
v2EliteFundFormService.recall(V2EliteFundForm form, String remark, Long executor, Long versionNo)
参数：
	id:表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

13. 删除草稿
v2EliteFundFormService.delete(List<Long> ids)
参数：
	ids:要删除表单ID集合
