
insert into wx_t_business_region_config (business_name, sales_channel,enable_flag, create_user_id,create_time,ext_property1) values ('PERFORMANCE_CHANNEL','Industrial',1,1,getdate(),'Direct');
insert into wx_t_business_region_config (business_name, sales_channel,enable_flag, create_user_id,create_time,ext_property1) values ('PERFORMANCE_CHANNEL','OEM',1,1,getdate(),'Direct');

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ChannelWieghtMapping','Indirect','3',1,getdate());

update wx_t_performance_program set salesChannel=3

INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202002', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202003', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202004', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202005', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202006', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202007', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202008', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202009', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, null, NULL, NULL)
INSERT [dbo].[wx_t_performance_program] ([bu], [salesChannel], [evaluation_month], [program_type], [ext1], [ext2], [ext3], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (N'Direct', N'12', N'202010', N'FLSR_Performance_Evaluation_Form', NULL, NULL, NULL, NULL, null, NULL, NULL)

--select * 
update d set d.dic_item_desc=r.sales_channel_name
from wx_t_dic_item d
left join dw_region_sales_channel_rel r on d.dic_item_code=r.region_name
where dic_type_code='Region.Direct'