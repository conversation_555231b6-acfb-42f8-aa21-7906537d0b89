package com.chevron.elites2.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chevron.elites.dao.ManagerUserMapper;
import com.chevron.elites.model.vo.ManagerVo;
import com.chevron.elites2.base.BaseResp;
import com.chevron.elites2.base.ResultCode;
import com.chevron.elites2.constant.*;
import com.chevron.elites2.dao.*;
import com.chevron.elites2.dto.request.RebateAuditPageRequest;
import com.chevron.elites2.dto.response.PageResponse;
import com.chevron.elites2.model.*;
import com.chevron.elites2.model.fund.RebateFund;
import com.chevron.elites2.model.fund.RebateFundDetail;
import com.chevron.elites2.model.vo.DwFundSumVo;
import com.chevron.elites2.model.vo.FundVo;
import com.chevron.elites2.model.vo.RebateAuditVo;
import com.chevron.elites2.model.vo.UsedFundVo;
import com.chevron.elites2.service.RebateApply2Service;
import com.chevron.elites2.service.RebateAudit2Service;
import com.chevron.elites2.service.RebateFund2Service;
import com.chevron.elites2.service.RebateNotice2Service;
import com.chevron.elites2.util.RebateUtil;
import com.common.util.ContextUtil;
import com.common.util.EmptyChecker;
import com.sys.auth.model.WxTUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class RebateAudit2ServiceImpl implements RebateAudit2Service {

    private static DecimalFormat percentTextFormat = new DecimalFormat("0.00%");
    private static DecimalFormat moneyTextFormat = new DecimalFormat("0.00");

    @Autowired
    private WXTRebateAudit2Mapper rebateAuditDao;

    @Autowired
    private WXTRebateRelated2Mapper rebateRelatedDao;

    @Autowired
    private WXTRebateAuditHistory2Mapper rebateAuditHistoryDao;

    @Autowired
    private WxTRebateApply2Mapper rebateApplyDao;

    @Autowired
    private OrgRebate2Mapper orgRebateDao;

    @Autowired
    private ManagerUserMapper userDao;

    @Autowired
    private RebateApply2Service rebateApply2Service;

    @Autowired
    private RebateFund2Service rebateFund2Service;

    @Autowired
    private RebateNotice2Service rebateNotice2Service;

    @Autowired
    private OrgFund2Mapper orgFund2Mapper;

    @Autowired
    private WxTRebateFundLogs2Mapper wxTRebateFundLogs2Mapper;

    @Autowired
    private WxTRebateFundLogMapper wxTRebateFundLogMapper;

    @Autowired
    private WxTRebateInvoiceLogs2Mapper rebateInvoiceLogs2Dao;


    @Autowired
    private DwPpCustomerFundQuarterNewMapper dwPpCustomerFundQuarterNewMapper;

    @Autowired
    private DwPpCustomerCompanyFundQuarterMapper dwPpCustomerCompanyFundQuarterMapper;


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public BaseResp submitToChannelManager(List<Long> rebateIdList) {
        BaseResp baseResp;
        WxTUser user = ContextUtil.getCurUser();
        if (CollectionUtil.isEmpty(rebateIdList)) {
            return new BaseResp("-2", "未选择需要处理的数据项!");
        }

        //检查CDM,CIO不能混合提交
        WxTRebateApply2 rebateApplyOne = rebateApplyDao.selectByPrimaryKey(rebateIdList.get(0));
        String channel = rebateApplyOne.getFundOwnership();
        boolean checkChanel = true;
        for (Long rebateId : rebateIdList) {
            WxTRebateApply2 rebateApply = rebateApplyDao.selectByPrimaryKey(rebateId);
            if (channel != null && !channel.equalsIgnoreCase(rebateApply.getFundOwnership())) {
                checkChanel = false;
                break;
            }
        }
        if (!checkChanel) {
            return new BaseResp("-3", "不同渠道的申请不允许混合汇总提交!");
        }

        // 批量更改审核状态
        baseResp = rebateApply2Service.submitRebateList(rebateIdList, RebateApplyStatusEnum.BS_SUMMARY.getCode());
        if (!ResultCode.SUCCESS.getCode().equals(baseResp.getCode())) {
            return new BaseResp("-1", "");
        }

        // 生成新的审核汇总
        WXTRebateAudit rebateAudit = new WXTRebateAudit();
        rebateAudit.setCreateTime(new Date());
        rebateAudit.setCreateByName(user != null ? user.getChName() : null);
        rebateAudit.setCreateBy(ContextUtil.getCurUserId());
        rebateAudit.setStatus(RebateAuditStatusEnum.BS_SUMMARY.getCode());
        rebateAudit.setIsSummary(IsSummaryEnum.ACTIVE_DATA.getCode());
        rebateAudit.setChannel(channel);

        String region = getRegionByOrgId(rebateApplyOne.getOrganizationId(), rebateApplyOne.getFundOwnership());
        rebateAudit.setCioRegion(region);
        BigDecimal totalFund = BigDecimal.ZERO;
        for (Long rebateId : rebateIdList) {
            WxTRebateApply2 rebateApply = rebateApplyDao.selectByPrimaryKey(rebateId);
            totalFund = totalFund.add(rebateApply.getApplyFund());
            rebateApply.setStatus(RebateApplyStatusEnum.BS_SUMMARY.getCode());
            rebateApplyDao.updateByPrimaryKeySelective(rebateApply);
        }
        rebateAudit.setTotalFund(totalFund);
        rebateAuditDao.insertSelective(rebateAudit);


        // 关联记录
        Long auditId = rebateAudit.getId();
        for (Long rebateId : rebateIdList) {
            WXTRebateRelated rebateRelated = new WXTRebateRelated();
            rebateRelated.setAuditId(auditId);
            rebateRelated.setRebateId(rebateId);
            rebateRelatedDao.insertSelective(rebateRelated);
        }

        // 生成审核历史
        baseResp = saveAuditHistory(auditId, RebateAuditStatusEnum.BS_SUMMARY.getCode(), null);

        // 自动审核通过
        audit(auditId, RebateAuditStatusEnum.BSM_PASS.getCode(), "自动审核通过");

        // 发送提醒邮件
        //List<ManagerVo> userList = new ArrayList<ManagerVo>();
        //userList.add(userDao.getChannelManger(channel, region));
        /*String cioRegion = orgRebateDao.getRegionNameByOrgId(rebateApplyOne.getOrganizationId(),
                ChannelRegionEnum.CIO.getCode());
        String cdmRegion = orgRebateDao.getRegionNameByOrgId(rebateApplyOne.getOrganizationId(),
                ChannelRegionEnum.CDM.getCode());
        userList.add(userDao.getChannelManger(ChannelRegionEnum.CIO.getCode(), cioRegion));
        if (!EmptyChecker.isEmpty(cdmRegion)) {
            userList.add(userDao.getChannelManger(ChannelRegionEnum.CDM.getCode(), cdmRegion));
        }*/
        //rebateNotice2Service.sendRemindEmailForAudit(userList, auditId, RebateAuditStatusEnum.BS_SUMMARY.getCode(), true);
        // 发送提醒短信
        //rebateNotice2Service.sendRemindSmgForAudit(userList, auditId, RebateAuditStatusEnum.BS_SUMMARY.getCode(), true);
        return baseResp;
    }

    @Override
    public PageResponse page(RebateAuditPageRequest pageRequest) {
        PageResponse pageResponse = new PageResponse();
        WxTUser user = ContextUtil.getCurUser();
        pageRequest.setUserId(ContextUtil.getCurUserId());
        pageRequest.setUserChannel(user != null ? user.getSalesChannel() : null);
        List<WXTRebateAudit> rebateAuditList = rebateAuditDao.getRebateAuditByPage(pageRequest);
        Long totalCount = pageRequest.getTotalCount();

        List<RebateAuditVo> voList = new ArrayList<RebateAuditVo>();
        for (WXTRebateAudit rebateAudit : rebateAuditList) {
            RebateAuditVo vo = new RebateAuditVo();
            vo.setId(rebateAudit.getId());
            vo.setStatus(rebateAudit.getStatus());
            vo.setCreateTime(rebateAudit.getCreateTime());
            vo.setCreateByName(rebateAudit.getCreateByName());
            vo.setTotalFundStr(moneyTextFormat.format(rebateAudit.getTotalFund()));
            vo.setChannel(rebateAudit.getChannel());
            voList.add(vo);
        }

        pageResponse.setData(voList);
        pageResponse.setTotalCount(totalCount);
        return pageResponse;
    }

    @Override
    public BaseResp detail(Long auditId) {
        BaseResp baseResp = new BaseResp();
        RebateAuditVo vo = new RebateAuditVo();

        List<Long> orgIdList = new ArrayList<Long>();
        String roleCode = (String) rebateApply2Service.getRebateRoleCode(null).getData();
        if (RebateRoleCode.BS_NORTH.getPermissionCode().equals(roleCode)) {
            orgIdList = orgRebateDao.getOrgIdByRegionName(ChannelRegionEnum.CIO_NORTH.getCode());
            orgIdList.addAll(orgRebateDao.getOrgIdByRegionName(ChannelRegionEnum.CIO_NENW.getCode()));
        } else if (RebateRoleCode.BS_SOUTH.getPermissionCode().equals(roleCode)) {
            orgIdList = orgRebateDao.getOrgIdByRegionName(ChannelRegionEnum.CIO_SOUTH.getCode());
        } else if (RebateRoleCode.BS_SW.getPermissionCode().equals(roleCode)) {
            orgIdList = orgRebateDao.getOrgIdByRegionName(ChannelRegionEnum.CIO_SW.getCode());
        } else if (RebateRoleCode.CDM_CHANNEL.getPermissionCode().equals(roleCode)) {
            String userChannel = ContextUtil.getCurUser().getSalesChannel();
            if (ChannelRegionEnum.CIO.getCode().equals(userChannel)) {
                orgIdList = orgRebateDao.getOrgIdByChannelManagerCai(ContextUtil.getCurUser().getCai());
            } else {
                orgIdList = dwPpCustomerFundQuarterNewMapper.getCustomerDistributorIdList();
            }
        } else {
            orgIdList = dwPpCustomerFundQuarterNewMapper.getCustomerDistributorIdList();
        }

        vo.setFundList(getFundDetail(auditId, orgIdList));
        baseResp.setData(vo);
        return baseResp;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public BaseResp audit(Long auditId, String auditStatus, String opinion) {
        BaseResp baseResp = new BaseResp();
        // 保存审核记录
        saveAuditHistory(auditId, auditStatus, opinion);

        // 记录审核流水
        saveRebateHistroy(auditId, auditStatus, opinion);

        // 子数据流程记录
        if (Integer.parseInt(RebateAuditStatusEnum.BSM_SUMMARY.getCode()) < Integer.parseInt(auditStatus)) {
            saveSubHistroy(auditId, auditStatus, opinion);
        }

        // 判断审核流程-需要当前流程所有角色审核完成才进入下一步
        Boolean nextStepFlag = false;
        List<ManagerVo> userList = new ArrayList<ManagerVo>();
        List<WXTRebateRelated> rebateRelatedList = rebateRelatedDao.getListByAuditId(auditId);
        WxTRebateApply2 rebateApplyOne = rebateApplyDao.selectByPrimaryKey(rebateRelatedList.get(0).getRebateId());
        String channel = rebateApplyOne.getFundOwnership();

        /*String cioRegion = orgRebateDao.getRegionNameByOrgId(rebateApplyOne.getOrganizationId(),
                ChannelRegionEnum.CIO.getCode());
        String cdmRegion = orgRebateDao.getRegionNameByOrgId(rebateApplyOne.getOrganizationId(),
                ChannelRegionEnum.CDM.getCode());*/
        /*if (RebateAuditStatusEnum.CHANNEL_PASS.getCode().equals(auditStatus)) {
            userList.add(userDao.getChannelManger(ChannelRegionEnum.CIO.getCode(), cioRegion));
            if (!EmptyChecker.isEmpty(cdmRegion)) {
                userList.add(userDao.getChannelManger(ChannelRegionEnum.CDM.getCode(), cdmRegion));
            }
            nextStepFlag = validAllUserPass(userList, auditId);
        } else */
        if (RebateAuditStatusEnum.ABM_PASS.getCode().equals(auditStatus)) { //最终BU审批
            if (StrUtil.equals(channel, "CDM")) {
                userList.add(userDao.getBuManager(ChannelRegionEnum.CDM.getCode()));
            }
            if (StrUtil.equalsAny(channel, "CIO", "C&I")) {
                userList.add(userDao.getBuManager(ChannelRegionEnum.CIO.getCode()));
            }
            nextStepFlag = validAllUserPass(userList, auditId);
        } else {
            nextStepFlag = true;
        }
        // 发送邮件
        Boolean sendFlag = getSendFlag(auditStatus, nextStepFlag);
        List<ManagerVo> sendUserList = getSendUserList(auditStatus, rebateApplyOne.getOrganizationId(), channel);
        rebateNotice2Service.sendRemindEmailForAudit(sendUserList, auditId, auditStatus, sendFlag);
        // 发送短信
        rebateNotice2Service.sendRemindSmgForAudit(sendUserList, auditId, auditStatus, 3, sendFlag);

        // 更改汇总信息当前状态
        if (nextStepFlag) {
            WXTRebateAudit updateData = new WXTRebateAudit();
            Long userId = ContextUtil.getCurUserId();
            Date current = new Date();
            updateData.setId(auditId);
            updateData.setUpdateBy(userId);
            updateData.setUpdateTime(current);
            updateData.setStatus(auditStatus);
            rebateAuditDao.updateByPrimaryKeySelective(updateData);

            if (RebateAuditStatusEnum.ABM_PASS.getCode().equals(auditStatus)) { //最终BU审批
                List<WXTRebateRelated> relatedList = rebateRelatedDao.getListByAuditId(auditId);
                Example example = new Example(WxTRebateFundLogs2.class);
                for (WXTRebateRelated related : relatedList) {
                    WxTRebateApply2 apply = new WxTRebateApply2();
                    apply.setId(related.getRebateId());
                    apply.setProcess(1);
                    rebateApplyDao.updateByPrimaryKeySelective(apply);

                    example.createCriteria().andEqualTo("applyId", related.getRebateId());
                    WxTRebateFundLogs2 log = wxTRebateFundLogs2Mapper.selectOneByExample(example);
                    example.clear();
                    log.setStatus(Integer.parseInt(RebateAuditStatusEnum.ABM_PASS.getCode()));
                    wxTRebateFundLogs2Mapper.updateByPrimaryKeySelective(log);
                }
            }

            // 审核不通过需要重置审核历史的状态
            if (/*RebateAuditStatusEnum.CHANNEL_REJECT.getCode().equals(auditStatus)
                    || */RebateAuditStatusEnum.BSM_REJECT.getCode().equals(auditStatus)
                    || RebateAuditStatusEnum.SD_REJECT.getCode().equals(auditStatus)
                    || RebateAuditStatusEnum.ABM_REJECT.getCode().equals(auditStatus)) {
                rebateAuditHistoryDao.updateHistoryStatus(auditId, YesOrNoEnum.NO.getCode());
            }

            // 对于SDO Manager审核不通过需要拆分数据
            if (RebateAuditStatusEnum.SD_REJECT.getCode().equals(auditStatus)) {
                splitSummary(auditId, opinion);
            }
            // 子数据流程变更
            if (Integer.parseInt(auditStatus) > Integer.parseInt(RebateAuditStatusEnum.BSM_SUMMARY.getCode())) {
                updateSubHistory(auditId, auditStatus, opinion);
            }
            // 更改申请金额最终状态
            if (RebateAuditStatusEnum.ABM_PASS.getCode().equals(auditStatus)) {
                //发送最终附件邮件
//				rebateNotice2Service.sendEmailToSalesWithFinalExcel(auditId);
            }
        }
        return baseResp;
    }

    /**
     * 拆分汇总信息
     *
     * @param summaryId
     * @param opinion
     */
    private void splitSummary(Long summaryId, String opinion) {
        // 汇总数据设置为无效数据
        WXTRebateAudit summaryData = new WXTRebateAudit();
        summaryData.setId(summaryId);
        summaryData.setIsSummary(IsSummaryEnum.INVALID_DATA.getCode());
        rebateAuditDao.updateByPrimaryKeySelective(summaryData);
        // 拆分数据
        List<WXTRebateAudit> auditList = rebateAuditDao.getSummaryAudit(summaryId);//已被汇总的
        for (WXTRebateAudit audit : auditList) {
            WXTRebateAudit updateAudit = new WXTRebateAudit();
            updateAudit.setId(audit.getId());
            updateAudit.setIsSummary(IsSummaryEnum.ACTIVE_DATA.getCode());
            rebateAuditDao.updateByPrimaryKeySelective(updateAudit);
        }
    }

    /**
     * 子数据流程变更
     *
     * @param summaryId
     * @param auditStatus
     * @param opinion
     */
    private void updateSubHistory(Long summaryId, String auditStatus, String opinion) {
        List<WXTRebateAudit> auditList = rebateAuditDao.getSummaryAudit(summaryId);
        for (WXTRebateAudit audit : auditList) {
            WXTRebateAudit updateAudit = new WXTRebateAudit();
            Long auditId = audit.getId();
            updateAudit.setId(audit.getId());
            updateAudit.setStatus(auditStatus);
            rebateAuditDao.updateByPrimaryKeySelective(updateAudit);
            rebateAuditHistoryDao.updateHistoryStatus(auditId, YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 子数据历史记录
     *
     * @param summaryId
     * @param auditStatus
     * @param opinion
     */
    private void saveSubHistroy(Long summaryId, String auditStatus, String opinion) {
        List<WXTRebateAudit> auditList = rebateAuditDao.getSummaryAudit(summaryId);
        for (WXTRebateAudit audit : auditList) {
            // 保存审核记录
            saveAuditHistory(audit.getId(), auditStatus, opinion);
        }
    }

    /**
     * 是否发送短信和邮件
     *
     * @param auditStatus
     * @param nextStepFlag
     * @return
     */
    private Boolean getSendFlag(String auditStatus, Boolean nextStepFlag) {
        Boolean sendFlag = false;
        if (RebateAuditStatusEnum.BSM_PASS.getCode().equals(auditStatus)) {
            if (nextStepFlag) {
                sendFlag = true;
            }
        } else if (RebateAuditStatusEnum.ABM_PASS.getCode().equals(auditStatus)) {
            if (nextStepFlag) {
                sendFlag = true;
            }
        } else if (RebateAuditStatusEnum.SD_PASS.getCode().equals(auditStatus)) {
            sendFlag = true;
        }
        return sendFlag;
    }

    /**
     * 获取发送邮件和短信的用户
     *
     * @param auditStatus
     * @param orgId
     * @return
     */
    private List<ManagerVo> getSendUserList(String auditStatus, Long orgId, String channel) {
        List<ManagerVo> userList = new ArrayList<ManagerVo>();

        if (RebateAuditStatusEnum.BSM_PASS.getCode().equals(auditStatus)) {
            userList.add(userDao.getBsManager());
        } else if (RebateAuditStatusEnum.SD_PASS.getCode().equals(auditStatus)) {
            if (StrUtil.equals(channel, "CDM")) {
                userList.add(userDao.getBuManager(ChannelRegionEnum.CDM.getCode()));
            }
            if (StrUtil.equalsAny(channel, "CIO", "C&I")) {
                userList.add(userDao.getBuManager(ChannelRegionEnum.CIO.getCode()));
            }
        } else if (RebateAuditStatusEnum.ABM_PASS.getCode().equals(auditStatus)) {
            //String region = getRegionByOrgId(orgId, channel);
            //userList.add(userDao.getBsByRegion(region));
        }

        return userList;
    }

    @Override
    public BaseResp getHistoryList(Long auditId) {
        BaseResp baseResp = new BaseResp();
        List<WXTRebateAuditHistory> historyList = rebateAuditHistoryDao.getHistoryList(auditId);
        baseResp.setData(historyList);
        return baseResp;
    }

    private UsedFundVo getUsedFund(WxTRebateApply2 rebateApply, RebateFund fund) {
        if (rebateApply == null || fund == null) {
            return null;
        }
        UsedFundVo result = new UsedFundVo();
        String channel = rebateApply.getFundOwnership();
        BigDecimal useIviFund = rebateApply.getUseIviFund();
        BigDecimal applyFund = rebateApply.getApplyFund();
        BigDecimal invoiceAmount = rebateApply.getInvoiceAmountTotal();
        BigDecimal redundantInvoice = BigDecimal.ZERO;
        BigDecimal acturalApplyInvoice = BigDecimal.ZERO; //本次真实提交可用的发票
        if (rebateApply.getUseInvoiceAmount() == null) {
            rebateApply.setUseInvoiceAmount(BigDecimal.ZERO);
        }
        //申请金额与发票取小值，多余发票记录下来下次报销用
        if (applyFund.compareTo(invoiceAmount) >= 0) {
            acturalApplyInvoice = invoiceAmount;
        } else {
            redundantInvoice = invoiceAmount.subtract(applyFund);
            acturalApplyInvoice = applyFund;
        }
        BigDecimal invoiceApply = RebateUtil.formatDecimal(acturalApplyInvoice.add(rebateApply.getUseInvoiceAmount()));//本次真实提交可用的发票+上次提交的发票
        if (StrUtil.equalsAny(channel, "CIO", "C&I")) {
            BigDecimal cioBdEarn = fund.getCioBd().getEarnTotal();
            BigDecimal cioMktEarn = fund.getCioMkt().getEarnTotal();
            BigDecimal cioIviEarn = fund.getCioIvi().getEarnTotal();
            BigDecimal cioBdUsed = fund.getCioBd().getUsedFund();
            BigDecimal cioMktUsed = fund.getCioMkt().getUsedFund();
            BigDecimal cioIviUsed = fund.getCioIvi().getUsedFund();
            BigDecimal cioIviBalance = RebateUtil.formatDecimal(cioIviEarn)
                    .subtract(RebateUtil.formatDecimal(cioIviUsed));
            if (StrUtil.equals("1", rebateApply.getFundType())) { //BD
                BigDecimal cioBdBalance = RebateUtil.formatDecimal(cioBdEarn)
                        .subtract(RebateUtil.formatDecimal(cioBdUsed));
                result.setRemainIviFund(cioIviBalance);

                //确保不超额使用
                if (useIviFund.compareTo(BigDecimal.ZERO) > 0 && useIviFund.compareTo(cioIviBalance) > 0) {
                    useIviFund = cioIviBalance;
                }

                if (cioBdBalance.compareTo(invoiceApply) >= 0) { //基金余额>=申请额度
                    result.setUsedFund(invoiceApply);//直接全兑现
                    result.setUsedFundBd(invoiceApply);
                    result.setUsedFundIvi(BigDecimal.ZERO);//没有使用
                    result.setRemainFundBd(RebateUtil.formatDecimal(cioBdBalance).subtract(invoiceApply));
                    result.setRemainFundIvi(cioIviBalance);//保持不变
                    result.setRemainInvoice(BigDecimal.ZERO);//发票全生效
                } else { //基金额度<申请额度,发票多
                    if (useIviFund.compareTo(BigDecimal.ZERO) > 0) { //使用IVI
                        if (cioBdBalance.add(useIviFund).compareTo(invoiceApply) >= 0) { //基金更多
                            result.setUsedFund(invoiceApply); //发票全部兑现
                            result.setUsedFundBd(cioBdBalance); //全部用完余额
                            result.setUsedFundIvi(invoiceApply.subtract(cioBdBalance)); //使用部分IVI的钱
                            result.setRemainInvoice(BigDecimal.ZERO);
                            result.setRemainFundBd(BigDecimal.ZERO);
                            result.setRemainFundIvi(RebateUtil.formatDecimal(useIviFund).add(cioBdBalance).subtract(invoiceApply)); //?
                        } else { //发票多余
                            result.setUsedFund(cioBdBalance.add(useIviFund));
                            result.setUsedFundBd(cioBdBalance);//全部用完余额
                            result.setUsedFundIvi(useIviFund);
                            result.setRemainInvoice(invoiceApply.subtract(cioBdBalance).subtract(useIviFund));//多余需要记录下来
                            result.setRemainFundBd(BigDecimal.ZERO);
                            result.setRemainFundIvi(cioIviBalance.subtract(useIviFund));
                        }
                    } else { //不使用IVI
                        result.setUsedFund(cioBdBalance);
                        result.setUsedFundBd(cioBdBalance);
                        result.setUsedFundIvi(BigDecimal.ZERO);
                        result.setRemainFundIvi(cioIviBalance); //保持不变
                        result.setRemainInvoice(invoiceApply.subtract(cioBdBalance));//多余发票
                        result.setRemainFundBd(BigDecimal.ZERO); //全部使用了
                    }
                }
            } else if (StrUtil.equals("2", rebateApply.getFundType())) {//MKT
                BigDecimal cioMktBalance = RebateUtil.formatDecimal(cioMktEarn.subtract(cioMktUsed));
                result.setRemainIviFund(cioIviBalance);
                //确保不超额使用
                if (useIviFund.compareTo(BigDecimal.ZERO) > 0 && useIviFund.compareTo(cioIviBalance) > 0) {
                    useIviFund = cioIviBalance;
                }
                if (invoiceApply.compareTo(cioMktBalance) > 0) { //申请额度>MKT余额
                    if (useIviFund.compareTo(BigDecimal.ZERO) > 0) { //使用IVI
                        if (cioMktBalance.add(useIviFund).compareTo(invoiceApply) >= 0) { //基金更多
                            result.setUsedFund(invoiceApply);
                            result.setUsedFundMkt(cioMktBalance);
                            result.setUsedFundMkt(cioMktBalance); //全部用完余额
                            result.setUsedFundIvi(invoiceApply.subtract(cioMktBalance)); //使用部分IVI的钱
                            result.setRemainInvoice(BigDecimal.ZERO);
                            result.setRemainFundMkt(BigDecimal.ZERO);
                            result.setRemainFundMkt(BigDecimal.ZERO);
                            result.setRemainFundIvi(useIviFund.add(cioMktBalance).subtract(invoiceApply));
                        } else { //发票多余
                            result.setUsedFund(cioMktBalance.add(useIviFund));
                            result.setUsedFundMkt(cioMktBalance);//全部用完余额
                            result.setUsedFundIvi(useIviFund);
                            result.setRemainFundMkt(BigDecimal.ZERO);
                            result.setRemainFundIvi(cioIviBalance.subtract(useIviFund));
                            result.setRemainInvoice(invoiceApply.subtract(cioMktBalance).subtract(useIviFund));//需要记录下来
                        }
                    } else { //不使用IVI
                        result.setUsedFund(cioMktBalance);//共使用的金额
                        result.setUsedFundMkt(cioMktBalance);
                        result.setUsedFundIvi(BigDecimal.ZERO);
                        result.setRemainFundIvi(cioIviBalance); //保持不变
                        result.setRemainFundMkt(BigDecimal.ZERO);//全部使用了
                        result.setRemainInvoice(invoiceApply.subtract(cioMktBalance));//多余发票
                    }
                } else {
                    result.setUsedFund(invoiceApply);
                    result.setUsedFundMkt(invoiceApply);
                    result.setUsedFundIvi(BigDecimal.ZERO);//没有使用
                    result.setRemainFundMkt(cioMktBalance.subtract(invoiceApply));
                    result.setRemainFundIvi(cioIviBalance);//保持不变
                    result.setRemainInvoice(BigDecimal.ZERO);//发票全生效
                }
            }
        }
        if (StrUtil.equals(channel, "CDM")) {
            BigDecimal cdmMktEarn = fund.getCdmMkt().getEarnTotal();
            BigDecimal cdmIviEarn = fund.getCdmIvi().getEarnTotal();
            BigDecimal cdmMktUsed = fund.getCdmMkt().getUsedFund();
            BigDecimal cdmIviUsed = fund.getCdmIvi().getUsedFund();
            BigDecimal cdmMktBalance = RebateUtil.formatDecimal(cdmMktEarn)
                    .subtract(RebateUtil.formatDecimal(cdmMktUsed));
            BigDecimal cdmIviBalance = RebateUtil.formatDecimal(cdmIviEarn)
                    .subtract(RebateUtil.formatDecimal(cdmIviUsed));
            result.setRemainIviFund(cdmIviBalance);
            //确保不超额使用
            if (useIviFund.compareTo(BigDecimal.ZERO) > 0 && useIviFund.compareTo(cdmIviBalance) > 0) {
                useIviFund = cdmIviBalance;
            }
            if (invoiceApply.compareTo(cdmMktBalance) > 0) { //申请额度>MKT余额,即发票多
                if (useIviFund.compareTo(BigDecimal.ZERO) > 0) { //使用IVI
                    if (cdmMktBalance.add(useIviFund).compareTo(invoiceApply) >= 0) { //基金更多
                        result.setUsedFund(invoiceApply);
                        result.setUsedFundMkt(cdmMktBalance); //全部用完余额
                        result.setUsedFundIvi(invoiceApply.subtract(cdmMktBalance)); //使用部分IVI的钱
                        result.setRemainInvoice(BigDecimal.ZERO);
                        result.setRemainFundMkt(BigDecimal.ZERO);
                        result.setRemainFundIvi(useIviFund.add(cdmMktBalance).subtract(invoiceApply));
                    } else { //发票多余
                        result.setUsedFund(cdmMktBalance.add(useIviFund));
                        result.setUsedFundMkt(cdmMktBalance);//全部用完余额
                        result.setUsedFundIvi(useIviFund);
                        result.setRemainFundMkt(BigDecimal.ZERO);
                        result.setRemainFundIvi(cdmIviBalance.subtract(useIviFund));
                        result.setRemainInvoice(invoiceApply.subtract(cdmMktBalance).subtract(useIviFund));//需要记录下来
                    }
                } else { //不使用IVI
                    //发票多余
                    result.setUsedFund(cdmMktBalance);
                    result.setUsedFundMkt(invoiceApply);
                    result.setUsedFundIvi(BigDecimal.ZERO);//没有使用
                    result.setRemainFundIvi(cdmIviBalance);//保持不变
                    result.setRemainInvoice(invoiceApply.subtract(cdmMktBalance));
                    result.setRemainFundMkt(BigDecimal.ZERO);
                }
            } else {
                result.setUsedFund(invoiceApply);
                result.setUsedFundMkt(invoiceApply);
                result.setUsedFundIvi(BigDecimal.ZERO);//没有使用
                result.setRemainFundMkt(cdmMktBalance.subtract(invoiceApply));
                result.setRemainFundIvi(cdmIviBalance);
                result.setRemainInvoice(BigDecimal.ZERO);
            }
        }
        result.setRemainInvoice(result.getRemainInvoice().add(redundantInvoice));

        return result;
    }

    @Override
    public void caculateOneFund(WxTRebateApply2 rebateApply, String handleType) {
        /*example.createCriteria().andEqualTo("applyId", applyId);
            WxTRebateFundLogs2 recordOne = wxTRebateFundLogs2Mapper.selectOneByExample(example);
            if (recordOne != null) continue;*/
        //WxTRebateApply2 rebateApply = rebateApplyMap.get(applyId);
        Long userId = ContextUtil.getCurUserId();
        List<Long> excludeId = new ArrayList<Long>();
        excludeId.add(rebateApply.getId());
        Long orgId = rebateApply.getOrganizationId();
        RebateFund fund = rebateFund2Service.getFundForOrg(orgId, null, 2, excludeId);

        BigDecimal cioBdEarn = fund.getCioBd().getEarnTotal();
        BigDecimal cioMktEarn = fund.getCioMkt().getEarnTotal();
        BigDecimal cioIviEarn = fund.getCioIvi().getEarnTotal();
        BigDecimal cdmMktEarn = fund.getCdmMkt().getEarnTotal();
        BigDecimal cdmIviEarn = fund.getCdmIvi().getEarnTotal();
        BigDecimal total = RebateUtil.formatDecimal(cioBdEarn)
                .add(RebateUtil.formatDecimal(cioMktEarn))
                .add(RebateUtil.formatDecimal(cdmMktEarn))
                .add(RebateUtil.formatDecimal(cioIviEarn))
                .add(RebateUtil.formatDecimal(cdmIviEarn));

        //已兑现金额
        BigDecimal cioBdUsed = fund.getCioBd().getUsedFund();
        BigDecimal cioMktUsed = fund.getCioMkt().getUsedFund();
        BigDecimal cioIviUsed = fund.getCioIvi().getUsedFund();
        BigDecimal cdmMktUsed = fund.getCdmMkt().getUsedFund();
        BigDecimal cdmIviUsed = fund.getCdmIvi().getUsedFund();
        BigDecimal usedFund = RebateUtil.formatDecimal(cioBdUsed)
                .add(RebateUtil.formatDecimal(cioMktUsed))
                .add(RebateUtil.formatDecimal(cdmMktUsed));

        //剩余金额
        BigDecimal cioBdBalance = RebateUtil.formatDecimal(cioBdEarn)
                .subtract(RebateUtil.formatDecimal(cioBdUsed));
        BigDecimal cioMktBalance = RebateUtil.formatDecimal(cioMktEarn)
                .subtract(RebateUtil.formatDecimal(cioMktUsed));
        BigDecimal cioIviBalance = RebateUtil.formatDecimal(cioIviEarn)
                .subtract(RebateUtil.formatDecimal(cioIviUsed));
        BigDecimal cdmMktBalance = RebateUtil.formatDecimal(cdmMktEarn)
                .subtract(RebateUtil.formatDecimal(cdmMktUsed));
        BigDecimal cdmIviBalance = RebateUtil.formatDecimal(cdmIviEarn)
                .subtract(RebateUtil.formatDecimal(cdmIviUsed));
        BigDecimal balance = RebateUtil.formatDecimal(cioBdBalance)
                .add(RebateUtil.formatDecimal(cioMktBalance))
                .add(RebateUtil.formatDecimal(cdmMktBalance));

        //List<WXTRebateRelated> rebateRelatedList = rebateRelatedDao.getListByAuditId(auditId);
        //for (WXTRebateRelated rebateRelated : rebateRelatedList) {
        //Long applyId = rebateRelated.getRebateId();
        String channel = rebateApply.getFundOwnership();
        rebateApply.setTotalFund(total);
        UsedFundVo thisUsedFund = getUsedFund(rebateApply, fund);
        BigDecimal thisApplyUsed = thisUsedFund.getUsedFund();
        rebateApply.setInvoiceRedundant(thisUsedFund.getRemainInvoice());
        rebateApply.setUsedFund(thisApplyUsed);
        if (StrUtil.equals(channel, "CDM")) {
            if (rebateApply.getFundType().equals("1")) {//BD
                rebateApply.setTotalFundCdm(RebateUtil.formatDecimal(fund.getCdmBd().getEarnTotal()));
                rebateApply.setUsedFundCdm(thisUsedFund.getUsedFundBd());
                rebateApply.setRemainFundCdm(thisUsedFund.getRemainFundBd());//本期剩余
                rebateApply.setRemainFund(BigDecimal.ZERO); //上期总结余
            }
            if (rebateApply.getFundType().equals("2")) {//MKT
                rebateApply.setTotalFundCdm(RebateUtil.formatDecimal(cdmMktEarn));
                rebateApply.setUsedFundCdm(thisUsedFund.getUsedFundMkt());
                rebateApply.setRemainFundCdm(thisUsedFund.getRemainFundMkt());//本期剩余
                rebateApply.setRemainFund(cdmMktBalance); //上期总结余
            }
            rebateApply.setTotalFundIvi(cdmIviEarn);
            rebateApply.setRemainFundIvi(thisUsedFund.getRemainFundIvi());
            rebateApply.setUsedFundIvi(thisUsedFund.getUsedFundIvi());
        }
        if (StrUtil.equalsAny(channel, "CIO", "C&I")) {
                /*rebateApply.setTotalFundCio(RebateUtil.formatDecimal(cioBdEarn)
                        .add(RebateUtil.formatDecimal(cioMktEarn)).add(RebateUtil.formatDecimal(cioIviEarn)));*/

                /*BigDecimal cioRemain = RebateUtil.formatDecimal(cioBdBalance)
                        .add(RebateUtil.formatDecimal(cioMktBalance));
                rebateApply.setRemainFund(cioRemain); //上期总结余*/

            if (rebateApply.getFundType().equals("1")) { //BD
                rebateApply.setTotalFundCio(fund.getCioBd().getEarnTotal());
                rebateApply.setUsedFundCio(thisUsedFund.getUsedFundBd());
                rebateApply.setRemainFundCio(thisUsedFund.getRemainFundBd());//本期剩余
                rebateApply.setRemainFund(cioBdBalance); //上期总结余
            }
            if (rebateApply.getFundType().equals("2")) {//MKT
                rebateApply.setTotalFundCio(fund.getCioMkt().getEarnTotal());
                rebateApply.setUsedFundCio(thisUsedFund.getUsedFundMkt());
                rebateApply.setRemainFundCio(thisUsedFund.getRemainFundMkt());//本期剩余
                rebateApply.setRemainFund(cioMktBalance); //上期总结余
            }
            rebateApply.setTotalFundIvi(cioIviEarn);
            rebateApply.setUsedFundIvi(thisUsedFund.getUsedFundIvi());
            rebateApply.setRemainFundIvi(thisUsedFund.getRemainFundIvi());//本期结余
            rebateApply.setRemainIviFund(thisUsedFund.getRemainIviFund());//上期结余
        }
        rebateApply.setProcess(0); //试算
        rebateApplyDao.updateByPrimaryKeySelective(rebateApply);
        if (StrUtil.equals(handleType, "save")) {
            //记录发票
            WxTRebateInvoiceLogs2 invoiceLogItem = new WxTRebateInvoiceLogs2();
            invoiceLogItem.setCreatedAt(new Date());
            invoiceLogItem.setCreatedBy(userId);
            invoiceLogItem.setUserId(rebateApply.getCreateBy());
            invoiceLogItem.setRebateApplyId(rebateApply.getId());
            invoiceLogItem.setActivityType(rebateApply.getActivityType());
            invoiceLogItem.setChannel(rebateApply.getFundOwnership());
            invoiceLogItem.setOrgId(orgId);
            invoiceLogItem.setStatus(1);
            invoiceLogItem.setInvoiceAmount(rebateApply.getInvoiceAmountTotal());//本次提交的发票
            invoiceLogItem.setInvoiceUsed(rebateApply.getUsedFund());//本次使用的发票
            invoiceLogItem.setInvoiceRedundant(RebateUtil.getNullAsZero(thisUsedFund.getRemainInvoice()));//多余的发票
            invoiceLogItem.setBalance(RebateUtil.getNullAsZero(rebateApply.getUseInvoiceAmount()));//本次使用池中的发票
            rebateInvoiceLogs2Dao.insertSelective(invoiceLogItem);
        }
        doFundLog(rebateApply, excludeId);
    }

    @Override
    public BaseResp caculateFund(List<Long> relateList, String handleType) {
        BaseResp result = new BaseResp();
        List<WxTRebateApply2> rebateList;
        if (CollectionUtil.isNotEmpty(relateList)) {
            rebateList = rebateApplyDao.getRebateApplyByIds(relateList);
        } else {
            rebateList = rebateApplyDao.getRebateApplyByRelated();
        }
        Map<Long, WxTRebateApply2> rebateApplyMap = new HashMap<Long, WxTRebateApply2>();
        for (WxTRebateApply2 rebateApply : rebateList) {
            rebateApplyMap.put(rebateApply.getId(), rebateApply);
        }

        for (Long applyId : rebateApplyMap.keySet()) {
            WxTRebateApply2 rebateApply = rebateApplyMap.get(applyId);
            caculateOneFund(rebateApply, handleType);
        }
        return result;
    }

    private List<Long> submitToSDOManagerByChannel(List<WXTRebateAudit> auditList) {
        if (CollectionUtil.isEmpty(auditList)) {
            return null;
        }
        BigDecimal summaryTotalFund = BigDecimal.ZERO;
        String channel = null;
        for (WXTRebateAudit rebateAudit : auditList) {
            summaryTotalFund = summaryTotalFund.add(rebateAudit.getTotalFund());
            channel = rebateAudit.getChannel();
        }

        // 新建汇总信息
        WXTRebateAudit summary = new WXTRebateAudit();
        summary.setStatus(RebateAuditStatusEnum.BSM_SUMMARY.getCode());
        summary.setTotalFund(summaryTotalFund);
        summary.setCreateTime(new Date());
        summary.setCreateBy(ContextUtil.getCurUserId());
        summary.setCreateByName(ContextUtil.getCurUser().getChName());
        summary.setIsSummary(IsSummaryEnum.SUMMARY_DATA.getCode());
        summary.setChannel(channel);
        rebateAuditDao.insertSelective(summary);
        Long summaryId = summary.getId();

        // 新建流程信息
        saveAuditHistory(summaryId, RebateAuditStatusEnum.BSM_SUMMARY.getCode(), null);

        // 更新子数据
        List<Long> relateList = new ArrayList<Long>();
        for (WXTRebateAudit rebateAudit : auditList) {
            Long auditId = rebateAudit.getId();
            WXTRebateAudit updateData = new WXTRebateAudit();
            updateData.setIsSummary(IsSummaryEnum.BE_SUMMED.getCode());
            updateData.setSummaryId(summaryId);
            updateData.setId(auditId);
            rebateAuditDao.updateByPrimaryKeySelective(updateData);

            // 审核
            audit(auditId, RebateAuditStatusEnum.BSM_SUMMARY.getCode(), null);

            // 获取关联申请的记录
            List<WXTRebateRelated> rebateRelatedList = rebateRelatedDao.getListByAuditId(auditId);
            for (WXTRebateRelated rebateRelated : rebateRelatedList) {
                relateList.add(rebateRelated.getRebateId()); //第一次汇总时的id,二级
            }
        }
        // 更新关联数据
        for (Long rebateId : relateList) {
            WXTRebateRelated rebateRelated = new WXTRebateRelated();
            rebateRelated.setAuditId(summaryId);
            rebateRelated.setRebateId(rebateId);//apply_id
            rebateRelatedDao.insertSelective(rebateRelated);
        }

        // 发送邮件
        List<ManagerVo> userList = new ArrayList<ManagerVo>();
        userList.add(userDao.getSDOManager());
        rebateNotice2Service.sendRemindEmailForAudit(userList, summaryId, RebateAuditStatusEnum.BSM_SUMMARY.getCode(), true);
        // 发送提醒短信
        rebateNotice2Service.sendRemindSmgForAudit(userList, summaryId, RebateAuditStatusEnum.BSM_SUMMARY.getCode(), 3, true);
        return relateList;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public BaseResp submitToSDOManager(List<Long> auditIdList) {
        BaseResp baseResp = new BaseResp();
        List<WXTRebateAudit> cdmAuditList = new ArrayList<WXTRebateAudit>();
        List<WXTRebateAudit> cioAuditList = new ArrayList<WXTRebateAudit>();

        for (Long auditId : auditIdList) {
            WXTRebateAudit rebateAudit = rebateAuditDao.selectByPrimaryKey(auditId);

            if (rebateAudit != null && StrUtil.equals(rebateAudit.getChannel(), "CDM")) {
                cdmAuditList.add(rebateAudit);
            } else {
                cioAuditList.add(rebateAudit);
            }
        }

        List<Long> relateList = new ArrayList<Long>();
        List<Long> cdmRelateList = submitToSDOManagerByChannel(cdmAuditList);
        List<Long> cioRelateList = submitToSDOManagerByChannel(cioAuditList);
        if (CollectionUtil.isNotEmpty(cdmRelateList)) {
            for (Long auditId : cdmRelateList) {
                relateList.add(auditId);
            }
        }
        if (CollectionUtil.isNotEmpty(cioRelateList)) {
            for (Long auditId : cioRelateList) {
                relateList.add(auditId);
            }
        }

        //liyu: 算各项所得
        caculateFund(relateList, "save");

        return baseResp;
    }

    /**
     * 生成审核历史
     *
     * @param auditId
     * @param step
     * @param opinion
     * @return
     */
    private BaseResp saveAuditHistory(Long auditId, String step, String opinion) {
        BaseResp baseResp = new BaseResp();
        WXTRebateAuditHistory rebateAuditHistory = new WXTRebateAuditHistory();
        rebateAuditHistory.setAuditId(auditId);
        rebateAuditHistory.setOperator(ContextUtil.getCurUserId());
        rebateAuditHistory.setOperatorName(ContextUtil.getCurUser().getChName());
        rebateAuditHistory.setOperationTime(new Date());
        rebateAuditHistory.setStep(step);
        rebateAuditHistory.setStepName(RebateAuditStatusEnum.getNameByCode(step));
        rebateAuditHistory.setStatus(YesOrNoEnum.YES.getCode());
        if (!EmptyChecker.isEmpty(opinion)) {
            rebateAuditHistory.setOpinion(opinion);
        }
        rebateAuditHistoryDao.insertSelective(rebateAuditHistory);
        return baseResp;
    }

    /**
     * 审核与申请状态转换
     *
     * @param auditId
     * @param auditStatus
     * @param opinion
     * @return
     */
    private BaseResp saveRebateHistroy(Long auditId, String auditStatus, String opinion) {
        BaseResp baseResp = new BaseResp();
        String status = null;
        /*if (RebateAuditStatusEnum.CHANNEL_PASS.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.CHANNEL_PASS.getCode();
        }
        if (RebateAuditStatusEnum.CHANNEL_REJECT.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.CHANNEL_REJECT.getCode();
        }*/
        if (RebateAuditStatusEnum.BSM_PASS.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.BSM_PASS.getCode();
        } else if (RebateAuditStatusEnum.BSM_REJECT.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.BSM_REJECT.getCode();
        } else if (RebateAuditStatusEnum.BSM_SUMMARY.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.BSM_SUMMARY.getCode();
        } else if (RebateAuditStatusEnum.SD_PASS.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.SD_PASS.getCode();
        } else if (RebateAuditStatusEnum.SD_REJECT.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.SD_REJECT.getCode();
        } else if (RebateAuditStatusEnum.ABM_PASS.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.BU_PASS.getCode();
        } else if (RebateAuditStatusEnum.ABM_REJECT.getCode().equals(auditStatus)) {
            status = RebateApplyStatusEnum.BU_REJECT.getCode();
        }
        List<WXTRebateRelated> rebateRelatedList = rebateRelatedDao.getListByAuditId(auditId);
        for (WXTRebateRelated rebateRelated : rebateRelatedList) {
            rebateApply2Service.audit(rebateRelated.getRebateId(), "1", opinion, null);
        }
        return baseResp;
    }

    /**
     * 生成基金表格
     *
     * @param auditId
     * @return
     */
    private List<FundVo> getFundDetail(Long auditId, List<Long> orgIdList) {
        List<FundVo> voList = new ArrayList<FundVo>();
        RebateFund fundSummary = new RebateFund();
        RebateFundDetail cioBdSummary = new RebateFundDetail();
        RebateFundDetail cioMktSummary = new RebateFundDetail();
        RebateFundDetail cioIviSummary = new RebateFundDetail();
        RebateFundDetail cdmBdSummary = new RebateFundDetail();
        RebateFundDetail cdmMktSummary = new RebateFundDetail();
        RebateFundDetail cdmIviSummary = new RebateFundDetail();
        //for (Long orgId : orgIdList) {
        RebateFund fund = rebateFund2Service.getFundForOrg(null, orgIdList, 2, null);
        dealWithSummary(fund.getCioBd(), cioBdSummary);
        dealWithSummary(fund.getCioMkt(), cioMktSummary);
        dealWithSummary(fund.getCioIvi(), cioIviSummary);
        dealWithSummary(fund.getCdmBd(), cdmBdSummary);
        dealWithSummary(fund.getCdmMkt(), cdmMktSummary);
        dealWithSummary(fund.getCdmIvi(), cdmIviSummary);
        //}
        fundSummary.setCioBd(cioBdSummary);
        fundSummary.setCioMkt(cioMktSummary);
        fundSummary.setCioIvi(cioIviSummary);
        fundSummary.setCdmBd(cdmBdSummary);
        fundSummary.setCdmMkt(cdmMktSummary);
        fundSummary.setCdmIvi(cdmIviSummary);
        // 获取今年的总金额
        for (int i = 0; i < 5; i++) {
            FundVo vo = generateFundVo(i, fundSummary);
            voList.add(vo);
        }
        return voList;
    }

    private void dealWithSummary(RebateFundDetail unit, RebateFundDetail summary) {
        BigDecimal earnTotal = summary.getEarnTotal() == null ? BigDecimal.ZERO : summary.getEarnTotal();
        BigDecimal earnTotal1 = unit.getEarnTotal() == null ? BigDecimal.ZERO : unit.getEarnTotal();
        summary.setEarnTotal(earnTotal.add(earnTotal1));

        BigDecimal apply = summary.getApplyFund() == null ? BigDecimal.ZERO : summary.getApplyFund();
        BigDecimal apply1 = unit.getApplyFund() == null ? BigDecimal.ZERO : unit.getApplyFund();
        summary.setApplyFund(apply.add(apply1));

        BigDecimal used = summary.getUsedFund() == null ? BigDecimal.ZERO : summary.getUsedFund();
        BigDecimal used1 = unit.getUsedFund() == null ? BigDecimal.ZERO : unit.getUsedFund();
        summary.setUsedFund(used.add(used1));
    }

    /**
     * 生成基金表格行数据
     *
     * @param index
     * @param fund
     * @return
     */
    private FundVo generateFundVo(int index, RebateFund fund) {
        FundVo vo = new FundVo();
        //累计金额
        BigDecimal bdFundCIO = fund.getCioBd().getEarnTotal();
        BigDecimal marketingFundCIO = fund.getCioMkt().getEarnTotal();
        BigDecimal iviFundCIO = fund.getCioIvi().getEarnTotal();
        BigDecimal bdFundCDM = fund.getCdmBd().getEarnTotal();
        BigDecimal marketingFundCDM = fund.getCdmMkt().getEarnTotal();
        BigDecimal iviFundCDM = fund.getCdmIvi().getEarnTotal();
        //已兑现金额
        BigDecimal bdFundCIOUsed = fund.getCioBd().getUsedFund();
        BigDecimal marketingFundCIOUsed = fund.getCioMkt().getUsedFund();
        BigDecimal iviFundCIOUsed = fund.getCioIvi().getUsedFund();
        BigDecimal bdFundCDMUsed = fund.getCdmBd().getUsedFund();
        BigDecimal marketingFundCDMUsed = fund.getCdmMkt().getUsedFund();
        BigDecimal iviFundCDMUsed = fund.getCdmIvi().getUsedFund();
        //已申报发票
        BigDecimal bdFundCIOInvoice = fund.getCioBd().getApplyFund();
        BigDecimal marketingFundCIOInvoice = fund.getCioMkt().getApplyFund();
        BigDecimal iviFundCIOInvoice = fund.getCioIvi().getApplyFund();
        BigDecimal bdFundCDMInvoice = fund.getCdmBd().getApplyFund();
        BigDecimal marketingFundCDMInvoice = fund.getCdmMkt().getApplyFund();
        BigDecimal iviFundCDMInvoice = fund.getCdmIvi().getApplyFund();
        //剩余金额=累计-已兑现
        BigDecimal bdFundCIOBalance = bdFundCIO.subtract(bdFundCIOUsed);
        BigDecimal marketingFundCIOBalance = marketingFundCIO.subtract(marketingFundCIOUsed);
        BigDecimal iviFundCIOBalance = iviFundCIO.subtract(iviFundCIOUsed);
        BigDecimal bdFundCDMBalance = bdFundCDM.subtract(bdFundCDMUsed);
        BigDecimal marketingFundCDMBalance = marketingFundCDM.subtract(marketingFundCDMUsed);
        BigDecimal iviFundCDMBalance = iviFundCDM.subtract(iviFundCDMUsed);

        if (index == 0) {// Earned Fund
            vo.setLineName("Earned Fund");
            vo.setBdFundCDMStr(moneyTextFormat.format(bdFundCDM));
            vo.setMarketingFundCDMStr(moneyTextFormat.format(marketingFundCDM));
            vo.setIviFundCDMStr(moneyTextFormat.format(iviFundCDM));
            vo.setFundCDMTotalStr(moneyTextFormat.format(marketingFundCDM.add(bdFundCDM).add(iviFundCDM)));

            vo.setBdFundCIOStr(moneyTextFormat.format(bdFundCIO));
            vo.setMarketingFundCIOStr(moneyTextFormat.format(marketingFundCIO));
            vo.setIviFundCIOStr(moneyTextFormat.format(iviFundCIO));
            vo.setFundCIOTotalStr(moneyTextFormat.format(bdFundCIO.add(marketingFundCIO).add(iviFundCIO)));

            BigDecimal toltalFund = bdFundCDM.add(marketingFundCDM).add(bdFundCIO).add(marketingFundCIO).add(iviFundCDM).add(iviFundCIO);
            vo.setTotalFundStr(moneyTextFormat.format(toltalFund));
        } else if (index == 1) {// Reimbursement
            vo.setLineName("Reimbursement");
            vo.setBdFundCDMStr(moneyTextFormat.format(bdFundCDMUsed));
            vo.setMarketingFundCDMStr(moneyTextFormat.format(marketingFundCDMUsed));
            vo.setIviFundCDMStr(moneyTextFormat.format(iviFundCDMUsed));
            vo.setFundCDMTotalStr(moneyTextFormat.format(marketingFundCDMUsed.add(bdFundCDMUsed).add(iviFundCDMUsed)));

            vo.setBdFundCIOStr(moneyTextFormat.format(bdFundCIOUsed));
            vo.setMarketingFundCIOStr(moneyTextFormat.format(marketingFundCIOUsed));
            vo.setIviFundCIOStr(moneyTextFormat.format(iviFundCIOUsed));
            vo.setFundCIOTotalStr(moneyTextFormat.format(bdFundCIOUsed.add(marketingFundCIOUsed).add(iviFundCIOUsed)));
            BigDecimal toltalFund = bdFundCDMUsed.add(marketingFundCDMUsed).add(bdFundCIOUsed)
                    .add(marketingFundCIOUsed).add(iviFundCDMUsed).add(iviFundCIOUsed);
            vo.setTotalFundStr(moneyTextFormat.format(toltalFund));
        } else if (index == 2) { //Invoice
            vo.setLineName("Invoice");
            vo.setBdFundCDMStr(moneyTextFormat.format(bdFundCDMInvoice));
            vo.setMarketingFundCDMStr(moneyTextFormat.format(marketingFundCDMInvoice));
            vo.setIviFundCDMStr(moneyTextFormat.format(iviFundCDMInvoice));
            vo.setFundCDMTotalStr(moneyTextFormat.format(marketingFundCDMInvoice.add(bdFundCDMInvoice).add(iviFundCDMInvoice)));
            vo.setBdFundCIOStr(moneyTextFormat.format(bdFundCIOInvoice));
            vo.setMarketingFundCIOStr(moneyTextFormat.format(marketingFundCIOInvoice));
            vo.setIviFundCIOStr(moneyTextFormat.format(iviFundCIOInvoice));
            vo.setFundCIOTotalStr(moneyTextFormat.format(bdFundCIOInvoice.add(marketingFundCIOInvoice).add(iviFundCIOInvoice)));
            BigDecimal toltalFund = bdFundCDMInvoice.add(marketingFundCDMInvoice).add(bdFundCIOInvoice)
                    .add(marketingFundCIOInvoice).add(iviFundCDMInvoice).add(bdFundCDMInvoice).add(iviFundCIOInvoice);
            vo.setTotalFundStr(moneyTextFormat.format(toltalFund));
        } else if (index == 3) {// Fund Balance
            vo.setLineName("Fund Balance");
            vo.setBdFundCDMStr(moneyTextFormat.format(bdFundCDMBalance));
            vo.setMarketingFundCDMStr(moneyTextFormat.format(marketingFundCDMBalance));
            vo.setIviFundCDMStr(moneyTextFormat.format(iviFundCDMBalance));
            vo.setFundCDMTotalStr(moneyTextFormat.format(bdFundCDMBalance.add(marketingFundCDMBalance).add(iviFundCDMBalance)));

            vo.setBdFundCIOStr(moneyTextFormat.format(bdFundCIOBalance));
            vo.setMarketingFundCIOStr(moneyTextFormat.format(marketingFundCIOBalance));
            vo.setIviFundCIOStr(moneyTextFormat.format(iviFundCIOBalance));
            vo.setFundCIOTotalStr(moneyTextFormat.format(bdFundCIOBalance.add(marketingFundCIOBalance).add(iviFundCIOBalance)));
            BigDecimal toltalFund = bdFundCDMBalance.add(marketingFundCDMBalance).add(bdFundCIOBalance)
                    .add(marketingFundCIOBalance).add(iviFundCDMBalance).add(iviFundCIOBalance);
            vo.setTotalFundStr(moneyTextFormat.format(toltalFund));
        } else if (index == 4) {// Reimbursement Vs. Earned Fund %
            vo.setLineName("Reimbursement Vs. Earned Fund %");
            BigDecimal bdCDMPre = null;
            if (bdFundCDM.compareTo(BigDecimal.ZERO) > 0) {
                bdCDMPre = RebateUtil.div(bdFundCDMUsed, bdFundCDM, 6);
            }
            vo.setBdFundCDMStr(bdCDMPre != null ? percentTextFormat.format(bdCDMPre) : "");

            BigDecimal mktCDMPre = null;
            if (marketingFundCDM.compareTo(BigDecimal.ZERO) > 0) {
                mktCDMPre = RebateUtil.div(marketingFundCDMUsed, marketingFundCDM, 6);
            }
            vo.setMarketingFundCDMStr(mktCDMPre != null ? percentTextFormat.format(mktCDMPre) : "");

            BigDecimal iviCDMPre = null;
            if (iviFundCDM.compareTo(BigDecimal.ZERO) > 0) {
                iviCDMPre = RebateUtil.div(iviFundCDMUsed, iviFundCDM, 6);
            }
            vo.setIviFundCDMStr(iviCDMPre != null ? percentTextFormat.format(iviCDMPre) : "");

            BigDecimal cdmTotalPre = null;
            if (marketingFundCDM.compareTo(BigDecimal.ZERO) > 0) {
                cdmTotalPre = RebateUtil.div(bdFundCDMUsed.add(marketingFundCDMUsed).add(iviFundCDMUsed), marketingFundCDM.add(bdFundCDM).add(iviFundCDM), 6);
            }
            vo.setFundCDMTotalStr(cdmTotalPre != null ? percentTextFormat.format(cdmTotalPre) : "");

            BigDecimal bdCIOPre = null;
            if (bdFundCIO.compareTo(BigDecimal.ZERO) > 0) {
                bdCIOPre = RebateUtil.div(bdFundCIOUsed, bdFundCIO, 6);
            }
            vo.setBdFundCIOStr(bdCIOPre != null ? percentTextFormat.format(bdCIOPre) : "");

            BigDecimal mktCIOPre = null;
            if (marketingFundCIO.compareTo(BigDecimal.ZERO) >= 0) {
                mktCIOPre = RebateUtil.div(marketingFundCIOUsed, marketingFundCIO, 6);
            }
            vo.setMarketingFundCIOStr(percentTextFormat.format(mktCIOPre));

            BigDecimal iviCIOPre = null;
            if (iviFundCIO.compareTo(BigDecimal.ZERO) >= 0) {
                iviCIOPre = RebateUtil.div(iviFundCIOUsed, iviFundCIO, 6);
            }
            vo.setIviFundCIOStr(percentTextFormat.format(iviCIOPre));

            BigDecimal cioTotalPre = null;
            BigDecimal tmp1 = bdFundCIO.add(marketingFundCIO).add(iviFundCIO);
            if (tmp1.compareTo(BigDecimal.ZERO) >= 0) {
                cioTotalPre = RebateUtil.div(bdFundCIOUsed.add(marketingFundCIOUsed).add(iviFundCIOUsed), tmp1, 6);
            }
            vo.setFundCIOTotalStr(cioTotalPre != null ? percentTextFormat.format(cioTotalPre) : "");

            BigDecimal totalPre = null;
            BigDecimal total = bdFundCDM.add(marketingFundCDM).add(iviFundCDM).add(bdFundCIO).add(marketingFundCIO).add(iviFundCIO);
            if (total.compareTo(BigDecimal.ZERO) >= 0) {
                BigDecimal usedTotal = bdFundCDMUsed.add(marketingFundCDMUsed).add(iviFundCDMUsed).add(bdFundCIOUsed).add(marketingFundCIOUsed).add(iviFundCIOUsed);
                totalPre = RebateUtil.div(usedTotal, total, 6);
            }
            vo.setTotalFundStr(totalPre != null ? percentTextFormat.format(totalPre) : "");
        }
        return vo;
    }

    /**
     * 验证是否用户都审核通过
     *
     * @param userList
     * @param auditId
     * @return
     */
    private Boolean validAllUserPass(List<ManagerVo> userList, Long auditId) {
        for (ManagerVo user : userList) {
            WXTRebateAuditHistory history = rebateAuditHistoryDao.getHistoryByUserId(auditId, user.getManagerUserId(),
                    YesOrNoEnum.YES.getCode());
            if (EmptyChecker.isEmpty(history)) {
                return false;
            }
        }
        return true;
    }

    private String getRegionByOrgId(Long orgId, String channel) {
        String region = null;
        if (StrUtil.equalsAny(channel, "CIO", "C&I")) {
            region = orgRebateDao.getRegionNameByOrgId(orgId, ChannelRegionEnum.CIO.getCode());
        }
        if (StrUtil.equals(channel, "CDM")) {
            region = orgRebateDao.getRegionNameByOrgId(orgId, ChannelRegionEnum.CDM.getCode());
        }
        return region;
    }


    private void doFundLog(WxTRebateApply2 rebateApply, List<Long> excludeId) {
        WxTRebateFundLogs2 record = new WxTRebateFundLogs2();
        record.setCreatedAt(new Date());
        record.setCreatedBy(ContextUtil.getCurUserId());
        record.setApplyId(rebateApply.getId());
        record.setOrgId(rebateApply.getOrganizationId());
        record.setOrganizationName(rebateApply.getOrganizationName());
        record.setFundType(Integer.parseInt(rebateApply.getFundType()));
        record.setChannel(rebateApply.getFundOwnership());
        record.setActivityType(Integer.parseInt(rebateApply.getActivityType()));
        record.setInvoiceAmount(rebateApply.getInvoiceAmountTotal());
        record.setApplyFund(rebateApply.getApplyFund());
        record.setUsedFund(rebateApply.getUsedFund());
        record.setUseInvoiceAmount(rebateApply.getUseInvoiceAmount());
        record.setUseIviFund(rebateApply.getUseIviFund());
        String channel = rebateApply.getFundOwnership();

        if (rebateApply.getInvoiceAmountTotal().compareTo(rebateApply.getApplyFund()) >= 0) {
            record.setApplyFundActual(rebateApply.getApplyFund());
        } else {
            record.setApplyFundActual(rebateApply.getInvoiceAmountTotal());
        }

        /*Example example = new Example(DwPpCustomerFundQuarterNew.class);
        example.createCriteria().andEqualTo("year", ElitesConstant.THIS_YEAR)
                .andEqualTo("distributorId", rebateApply.getOrganizationId());
        List<DwPpCustomerFundQuarterNew> list = dwPpCustomerFundQuarterNewMapper.selectByExample(example);*/

        //DwFundSumVo orgEarnFund = orgFund2Mapper.getEarnFund(rebateApply.getOrganizationId(), null, ElitesConstant.THIS_YEAR, null);

        List<Long> orgIdList = new ArrayList<Long>();
        orgIdList.add(rebateApply.getOrganizationId());
        List<DwFundSumVo> orgEarnFund = dwPpCustomerFundQuarterNewMapper.getEarnFundByOrgQuarter(orgIdList, ElitesConstant.THIS_YEAR, null);

        //当前累计
        Map<String, DwFundSumVo> orgEarnFundMap = new HashMap<String, DwFundSumVo>();
        for (DwFundSumVo dwFundSumVo : orgEarnFund) {
            orgEarnFundMap.put(dwFundSumVo.getQuarter(), dwFundSumVo);
        }
        DwFundSumVo dwFundSumVo1 = orgEarnFundMap.get("Q1");
        DwFundSumVo dwFundSumVo2 = orgEarnFundMap.get("Q2");
        DwFundSumVo dwFundSumVo3 = orgEarnFundMap.get("Q3");
        DwFundSumVo dwFundSumVo4 = orgEarnFundMap.get("Q4");
        if (StrUtil.equals(channel, "CDM")) {
            record.setQ1BdFund(dwFundSumVo1 != null ? RebateUtil.formatDecimal(dwFundSumVo1.getBdFundCDM()) : BigDecimal.ZERO);
            record.setQ1MktFund(dwFundSumVo1 != null ? RebateUtil.formatDecimal(dwFundSumVo1.getMktFundCDM()) : BigDecimal.ZERO);
            record.setQ1IviFund(dwFundSumVo1 != null ? RebateUtil.formatDecimal(dwFundSumVo1.getIviFundCDM()) : BigDecimal.ZERO);
            record.setQ2BdFund(dwFundSumVo2 != null ? RebateUtil.formatDecimal(dwFundSumVo2.getBdFundCDM()) : BigDecimal.ZERO);
            record.setQ2MktFund(dwFundSumVo2 != null ? RebateUtil.formatDecimal(dwFundSumVo2.getMktFundCDM()) : BigDecimal.ZERO);
            record.setQ2IviFund(dwFundSumVo2 != null ? RebateUtil.formatDecimal(dwFundSumVo2.getIviFundCDM()) : BigDecimal.ZERO);
//            record.setQ3q4BdFund(dwFundSumVo3 != null ? RebateUtil.formatDecimal((dwFundSumVo3.getBdFundCDM().add(dwFundSumVo4.getBdFundCDM()))) :
//                    BigDecimal.ZERO);
//            record.setQ3q4MktFund(dwFundSumVo3 != null ? RebateUtil.formatDecimal((dwFundSumVo3.getMktFundCDM().add(dwFundSumVo4.getMktFundCDM()))) :
//                    BigDecimal.ZERO);
//            record.setQ3q4IviFund(dwFundSumVo3 != null ? RebateUtil.formatDecimal((dwFundSumVo3.getIviFundCDM().add(dwFundSumVo4.getIviFundCDM()))) :
//                    BigDecimal.ZERO);
        } else if (StrUtil.equalsAny(channel, "CIO", "C&I")) {
            record.setQ1BdFund(dwFundSumVo1 != null ? RebateUtil.formatDecimal(dwFundSumVo1.getBdFundCIO()) : BigDecimal.ZERO);
            record.setQ1MktFund(dwFundSumVo1 != null ? RebateUtil.formatDecimal(dwFundSumVo1.getMktFundCIO()) : BigDecimal.ZERO);
            record.setQ1IviFund(dwFundSumVo1 != null ? RebateUtil.formatDecimal(dwFundSumVo1.getIviFundCIO()) : BigDecimal.ZERO);
            record.setQ2BdFund(dwFundSumVo2 != null ? RebateUtil.formatDecimal(dwFundSumVo2.getBdFundCIO()) : BigDecimal.ZERO);
            record.setQ2MktFund(dwFundSumVo2 != null ? RebateUtil.formatDecimal(dwFundSumVo2.getMktFundCIO()) : BigDecimal.ZERO);
            record.setQ2IviFund(dwFundSumVo2 != null ? RebateUtil.formatDecimal(dwFundSumVo2.getIviFundCIO()) : BigDecimal.ZERO);
//            record.setQ3q4BdFund(dwFundSumVo3 != null ? RebateUtil.formatDecimal((dwFundSumVo3.getBdFundCIO().add(dwFundSumVo4.getBdFundCIO()))) : BigDecimal.ZERO);
//            record.setQ3q4MktFund(dwFundSumVo3 != null ? RebateUtil.formatDecimal((dwFundSumVo3.getMktFundCIO().add(dwFundSumVo4.getMktFundCIO()))) : BigDecimal.ZERO);
//            record.setQ3q4IviFund(dwFundSumVo3 != null ? RebateUtil.formatDecimal((dwFundSumVo3.getIviFundCIO().add(dwFundSumVo4.getIviFundCIO()))) : BigDecimal.ZERO);
        }
        BigDecimal totalFund = BigDecimal.ZERO;
        if (dwFundSumVo1 != null) {
            totalFund = totalFund.add(dwFundSumVo1.sumTotal(channel, rebateApply.getFundType()));
        }
        if (dwFundSumVo2 != null) {
            totalFund = totalFund.add(dwFundSumVo2.sumTotal(channel, rebateApply.getFundType()));
        }
        if (dwFundSumVo3 != null) {
            totalFund = totalFund.add(dwFundSumVo3.sumTotal(channel, rebateApply.getFundType()));
        }
        if (dwFundSumVo4 != null) {
            totalFund = totalFund.add(dwFundSumVo4.sumTotal(channel, rebateApply.getFundType()));
        }
        record.setTotalFund(totalFund != null ? totalFund : BigDecimal.ZERO);

        //之前共使用
        WxTRebateFundLogs2 fundLog = wxTRebateFundLogs2Mapper.getUsedFund(rebateApply.getOrganizationId(), channel, excludeId, null, null, null);

        //上期剩余
        //RebateFund fund = rebateFund2Service.getFundForOrg(rebateApply.getOrganizationId(), null, 2);
        record.setRemainFund(rebateApply.getRemainFund());

        record.setQ1BdFundRemain(record.getQ1BdFund() != null ? RebateUtil.formatDecimal(record.getQ1BdFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ1BdFundUsed())) : BigDecimal.ZERO);
        record.setQ1MktFundRemain(record.getQ1MktFund() != null ? RebateUtil.formatDecimal(record.getQ1MktFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ1MktFundUsed())) : BigDecimal.ZERO);
        record.setQ1IviFundRemain(record.getQ1IviFund() != null ? RebateUtil.formatDecimal(record.getQ1IviFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ1IviFundUsed())) : BigDecimal.ZERO);
        record.setQ2BdFundRemain(record.getQ2BdFund() != null ? RebateUtil.formatDecimal(record.getQ2BdFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ2BdFundUsed())) : BigDecimal.ZERO);
        record.setQ2MktFundRemain(record.getQ2MktFund() != null ? RebateUtil.formatDecimal(record.getQ2MktFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ2MktFundUsed())) : BigDecimal.ZERO);
        record.setQ2IviFundRemain(record.getQ2IviFund() != null ? RebateUtil.formatDecimal(record.getQ2IviFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ2IviFundUsed())) : BigDecimal.ZERO);
        record.setQ3q4BdFundRemain(record.getQ3q4BdFund() != null ? RebateUtil.formatDecimal(record.getQ3q4BdFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ3q4BdFundUsed())) : BigDecimal.ZERO);
        record.setQ3q4MktFundRemain(record.getQ3q4MktFund() != null ? RebateUtil.formatDecimal(record.getQ3q4MktFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ3q4MktFundUsed())) : BigDecimal.ZERO);
        record.setQ3q4IviFundRemain(record.getQ3q4IviFund() != null ? RebateUtil.formatDecimal(record.getQ3q4IviFund()).subtract(RebateUtil.formatDecimal(fundLog.getQ3q4IviFundUsed())) : BigDecimal.ZERO);

        //本期使用
        record.setUsedFund(rebateApply.getUsedFund());
        record.setUsedFundIvi(rebateApply.getUsedFundIvi());

        //本次使用的Fund
        BigDecimal thisUsedFund = BigDecimal.ZERO;
        if (StrUtil.equalsIgnoreCase(channel, "CDM")) {
            thisUsedFund = rebateApply.getUsedFundCdm();
        } else if (StrUtil.equalsAnyIgnoreCase(channel, "CIO", "C&I")) {
            thisUsedFund = rebateApply.getUsedFundCio();
        }

        if (StrUtil.equals(rebateApply.getFundType(), "1")) {
            BigDecimal q1Remain = record.getQ1BdFundRemain();
            BigDecimal q1q2Remain = record.getQ1BdFundRemain().add(record.getQ2BdFundRemain());
            BigDecimal totalRemain = q1q2Remain.add(record.getQ3q4BdFundRemain());
            BigDecimal q1BdFundUsed = BigDecimal.ZERO;
            BigDecimal q2BdFundUsed = BigDecimal.ZERO;
            BigDecimal q3q4BdFundUsed = BigDecimal.ZERO;
            if (thisUsedFund.compareTo(q1Remain) <= 0) {
                q1BdFundUsed = thisUsedFund; //<=Q1
            } else {
                if (thisUsedFund.compareTo(q1q2Remain) <= 0) { //Q1<=x<=Q1+Q2
                    q1BdFundUsed = q1Remain;
                    q2BdFundUsed = RebateUtil.formatDecimal(thisUsedFund.subtract(q1Remain));
                } else {
                    if (thisUsedFund.compareTo(totalRemain) <= 0) {// Q1+Q2<=x<=Q1~Q4
                        q1BdFundUsed = q1Remain;
                        q2BdFundUsed = record.getQ2BdFundRemain();
                        q3q4BdFundUsed = RebateUtil.formatDecimal(thisUsedFund.subtract(q1Remain).subtract(q2BdFundUsed));
                    } else {
                        q1BdFundUsed = q1Remain;
                        q2BdFundUsed = record.getQ2MktFundRemain();
                        q3q4BdFundUsed = record.getQ3q4BdFundRemain();
                    }
                }
            }
            record.setQ1BdFundUsed(q1BdFundUsed);
            record.setQ2BdFundUsed(q2BdFundUsed);
            record.setQ3q4BdFundUsed(q3q4BdFundUsed);
        }

        if (StrUtil.equals(rebateApply.getFundType(), "2")) {
            BigDecimal q1Remain = record.getQ1MktFundRemain();
            BigDecimal q1q2Remain = record.getQ1MktFundRemain().add(record.getQ2MktFundRemain());
            BigDecimal totalRemain = q1q2Remain.add(record.getQ3q4MktFundRemain());
            BigDecimal q1MktFundUsed = BigDecimal.ZERO;
            BigDecimal q2MktFundUsed = BigDecimal.ZERO;
            BigDecimal q3q4MktFundUsed = BigDecimal.ZERO;
            if (thisUsedFund.compareTo(q1Remain) <= 0) {
                q1MktFundUsed = thisUsedFund; //<=Q1
            } else {
                if (thisUsedFund.compareTo(q1q2Remain) <= 0) { //Q1<=x<=Q1+Q2
                    q1MktFundUsed = q1Remain;
                    q2MktFundUsed = RebateUtil.formatDecimal(thisUsedFund.subtract(q1Remain));
                } else {
                    if (thisUsedFund.compareTo(totalRemain) <= 0) {// Q1+Q2<=x<=Q1~Q4
                        q1MktFundUsed = q1Remain;
                        q2MktFundUsed = record.getQ2MktFundRemain();
                        q3q4MktFundUsed = RebateUtil.formatDecimal(thisUsedFund.subtract(q1Remain).subtract(q2MktFundUsed));
                    } else {
                        q1MktFundUsed = q1Remain;
                        q2MktFundUsed = record.getQ2MktFundRemain();
                        q3q4MktFundUsed = record.getQ3q4MktFundRemain();
                    }
                }
            }
            record.setQ1MktFundUsed(q1MktFundUsed);
            record.setQ2MktFundUsed(q2MktFundUsed);
            record.setQ3q4MktFundUsed(q3q4MktFundUsed);
        }

        BigDecimal thisUsedIviFund = rebateApply.getUsedFundIvi();
        BigDecimal q1Remain = record.getQ1IviFundRemain();
        BigDecimal q1q2Remain = record.getQ1IviFundRemain().add(record.getQ2IviFundRemain());
        BigDecimal totalRemain = q1q2Remain.add(record.getQ3q4IviFundRemain());
        BigDecimal q1IviFundUsed = BigDecimal.ZERO;
        BigDecimal q2IviFundUsed = BigDecimal.ZERO;
        BigDecimal q3q4IviFundUsed = BigDecimal.ZERO;
        if (thisUsedIviFund.compareTo(q1Remain) <= 0) {
            q1IviFundUsed = thisUsedIviFund; //<=Q1
        } else {
            if (thisUsedIviFund.compareTo(q1q2Remain) <= 0) { //Q1<=x<=Q1+Q2
                q1IviFundUsed = q1Remain;
                q2IviFundUsed = RebateUtil.formatDecimal(thisUsedIviFund.subtract(q1Remain));
            } else {
                if (thisUsedIviFund.compareTo(totalRemain) <= 0) {// Q1+Q2<=x<=Q1~Q4
                    q1IviFundUsed = q1Remain;
                    q2IviFundUsed = record.getQ2IviFundRemain();
                    q3q4IviFundUsed = RebateUtil.formatDecimal(thisUsedIviFund.subtract(q1Remain).subtract(q2IviFundUsed));
                }
            }
        }

        record.setQ1IviFundUsed(q1IviFundUsed);
        record.setQ2IviFundUsed(q2IviFundUsed);
        record.setQ3q4IviFundUsed(q3q4IviFundUsed);

        //多余发票额度
        record.setInvoiceRedunant(rebateApply.getInvoiceRedundant());

        //按CompanyCode扣除的计算
        //saveCompanyCodeFungLog(rebateApply, record);

        Example example = new Example(WxTRebateFundLogs2.class);
        example.createCriteria().andEqualTo("applyId", rebateApply.getId());
        WxTRebateFundLogs2 recordOne = wxTRebateFundLogs2Mapper.selectOneByExample(example);
        if (recordOne != null) {
            record.setId(recordOne.getId());
            wxTRebateFundLogs2Mapper.updateByPrimaryKeySelective(record);
        } else {
            wxTRebateFundLogs2Mapper.insertSelective(record);
        }
    }

    /**
     * 按CompanyCode扣除的计算
     *
     * @param rebateApply
     * @param record
     */
    private void saveCompanyCodeFungLog(WxTRebateApply2 rebateApply, WxTRebateFundLogs2 record) {
        String channel = rebateApply.getFundOwnership();

        //本次使用的Fund

        BigDecimal q1FundUsed = BigDecimal.ZERO;
        BigDecimal q234FundUsed = BigDecimal.ZERO;
        BigDecimal q1IviFundUsed = record.getQ1IviFundUsed();
        BigDecimal q234IviFundUsed = record.getQ2IviFundUsed().add(record.getQ3q4IviFundUsed());

        BigDecimal thisUsedFund = BigDecimal.ZERO;
        /*if (StrUtil.equalsIgnoreCase(channel, "CDM")) {
            thisUsedFund = rebateApply.getUsedFundCdm();
        } else if (StrUtil.equalsAnyIgnoreCase(channel, "CIO", "C&I")) {
            thisUsedFund = rebateApply.getUsedFundCio();
        }*/

        Map<String, DwPpCustomerCompanyFundQuarter> companyFundListQ1 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(rebateApply.getOrganizationId(), channel,
                CollUtil.newArrayList("Q1"));
        Map<String, DwPpCustomerCompanyFundQuarter> companyFundListQ234 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(rebateApply.getOrganizationId(), channel,
                CollUtil.newArrayList("Q2", "Q3", "Q4"));

        for (String companyCode : companyFundListQ1.keySet()) {
            DwPpCustomerCompanyFundQuarter fundQ1 = companyFundListQ1.get(companyCode);
            DwPpCustomerCompanyFundQuarter fundQ234 = companyFundListQ234.get(companyCode);
            WxTRebateFundLog rebateFundLog = new WxTRebateFundLog();
            rebateFundLog.setCreatedBy(ContextUtil.getCurUserId());
            rebateFundLog.setCreatedTime(new Date());
            rebateFundLog.setApplyId(rebateApply.getId());
            rebateFundLog.setOrgId(rebateApply.getOrganizationId());
            rebateFundLog.setOrgName(rebateApply.getOrganizationName());
            rebateFundLog.setFundType(Integer.parseInt(rebateApply.getFundType()));
            rebateFundLog.setChannel(channel);
            rebateFundLog.setCompanyCode(companyCode);

            if (rebateFundLog.getFundType() == 1) { //BD
                q1FundUsed = record.getQ1BdFundUsed();
                q234FundUsed = record.getQ2BdFundUsed().add(record.getQ3q4BdFundUsed());
                rebateFundLog.setUsedFundQ1(q1FundUsed.multiply(fundQ1.getBdFundRate()));
                rebateFundLog.setUsedFundQ234(q234FundUsed.multiply(fundQ1.getBdFundRate()));
            } else if (rebateFundLog.getFundType() == 2) { //MKT
                q1FundUsed = record.getQ1MktFundUsed();
                q234FundUsed = record.getQ2MktFundUsed().add(record.getQ3q4MktFundUsed());
                rebateFundLog.setUsedFundQ1(q1FundUsed.multiply(fundQ1.getMarketingFundRate()));
                rebateFundLog.setUsedFundQ234(q234FundUsed.multiply(fundQ1.getMarketingFundRate()));
            }


            rebateFundLog.setUsedFundIviQ1(q1IviFundUsed.multiply(fundQ1.getIviFundRate()));
            rebateFundLog.setUsedFundIviQ234(q234IviFundUsed.multiply(fundQ1.getIviFundRate()));
            rebateFundLog.setUsedFundIvi(rebateFundLog.getUsedFundIviQ1().add(rebateFundLog.getUsedFundIviQ234()));

            thisUsedFund = rebateFundLog.getUsedFundQ1().add(rebateFundLog.getUsedFundQ234()).add(rebateFundLog.getUsedFundIvi());
            rebateFundLog.setUsedFund(thisUsedFund);
            wxTRebateFundLogMapper.insertSelective(rebateFundLog);
        }

    }

}
