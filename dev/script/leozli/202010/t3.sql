SELECT sum([Amount]) sell_amount
      ,sum([Quantity]) sell_through
	  ,convert(nvar<PERSON>r(10), st.[Year]) + '年' year
	  ,st.[Month] month
  FROM [dw_dms_Data_Interface_Sell_Through] st
  left join wx_t_product p on st.[Standardgoodscode]=p.sku
  where st.[Standardgoodscode]  is not null
  and [distributorCode] + '/' + [customerCode] = #dmsKey#
  and (#qbr_category# is null or case when p.product_channel='CDM' then '金富力' when p.product_channel='C&I' then '德乐' else '其它' end=#qbr_category#)
  and (#channel_category# is null or 
		case when #category_channel#='CDM' then isnull(p.cdm_category, 'UNKOWN') 
			when #category_channel#='CI' then isnull(p.ci_category, 'UNKOWN') end=#channel_category#)
  and (#sku# is null or st.[Standardgoodscode]=#sku#)
  group by st.[Month], st.[Year]