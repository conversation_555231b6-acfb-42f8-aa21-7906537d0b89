package com.chevron.dwz.business.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;

import org.springframework.stereotype.Service;

import com.chevron.dwz.business.BaiduDwzBizService;
import com.chevron.dwz.model.BaiduDwz;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.google.gson.Gson;

@Service
public class BaiduDwzServiceBizImpl implements BaiduDwzBizService {
	
    final static String KEY_CREATE_API = "BaiduDwz.create";
    final static String KEY_QEURY_API = "BaiduDwz.query";
    final static String KEY_TOKEN = "BaiduDwz.token";
    /**
     * 创建短网址
     *
     * @param longUrl
     *            长网址：即原网址
     *        termOfValidity
     *            有效期：默认值为long-term
     * @return  成功：短网址
     *          失败：返回空字符串
     * @throws WxPltException 
     */
    @Override
    public synchronized String createShortUrl(String longUrl, String termOfValidity) throws WxPltException {
        String params = "{\"Url\":\""+ longUrl + "\",\"TermOfValidity\":\""+ termOfValidity + "\"}";

        BufferedReader reader = null;
        try {
            // 创建连接
            URL url = new URL((String)Constants.getSystemPropertyByCodeType(KEY_CREATE_API));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式
            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.setRequestProperty("Token", (String)Constants.getSystemPropertyByCodeType(KEY_TOKEN)); // 设置发送数据的格式");

            // 发起请求
            connection.connect();
            OutputStreamWriter out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            out.append(params);
            out.flush();
            out.close();

            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }
            reader.close();

            // 抽取生成短网址
            BaiduDwz urlResponse = new Gson().fromJson(res, BaiduDwz.class);
            if (urlResponse.getCode() == 0) {
                return urlResponse.getShortUrl();
            } else {
                throw new WxPltException(urlResponse.getErrMsg());
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("生成百度短链接失败", e);
        }
    }

    
    /**
     * 还原长网址短网址
     *
     * @param shortUrl 短网址
     * @return  成功：长网址
     *
     */
    @Override
    public synchronized String queryLongUrl(String shortUrl) throws WxPltException {
        String params = "{\"shortUrl\":\""+ shortUrl + "\"}";

        BufferedReader reader = null;
        try {
            // 创建连接
            URL url = new URL((String)Constants.getSystemPropertyByCodeType(KEY_QEURY_API));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式
            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.setRequestProperty("Token", (String)Constants.getSystemPropertyByCodeType(KEY_TOKEN)); // 设置发送数据的格式");

            // 发起请求
            connection.connect();
            OutputStreamWriter out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            out.append(params);
            out.flush();
            out.close();

            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }
            reader.close();

            // 抽取生成长网址
            BaiduDwz urlResponse = new Gson().fromJson(res, BaiduDwz.class);
            if (urlResponse.getCode() == 0) {
                return urlResponse.getLongUrl();
            } else {
                throw new WxPltException(urlResponse.getErrMsg());
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("百度短链接还原长网址失败", e);
        }
    }
}
