package com.chevron.pms.business;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.chevron.master.dao.WorkshopMasterMapper;
import com.common.exception.auth.WxAuthException;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.WorkshopEmpSummary;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopEmployeeExample;
import com.chevron.pms.model.WorkshopEmployeeParams;
import com.chevron.point.business.PointBizService;
import com.chevron.point.dto.EarnType;
import com.chevron.point.model.B2BPointDetail;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.HttpSender;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.EtoBizService;

import net.sf.json.JSONObject;

import javax.annotation.Resource;

@Service
public class WorkshopEmployeeBizService {
	
	private static Logger log = LoggerFactory.getLogger(WorkshopEmployeeBizService.class);
	public static final String EMPLOYEE_TYPE_MECHANIC = "Mechanic";
	public static final String EMPLOYEE_TYPE_OWNER = "Owner";
	
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_TOTALRECORD_KEY = "totalRecord";
	
//	@Autowired
//	private WorkShopService workshopService;
	
	@Autowired
	private WorkshopEmployeeMapper workshopEmployeeMapper;
	
	@Autowired
	private WxTUserMapper wxTUserMapper;
	
//	@Autowired
//	private UserServiceI userService;
	
//	@Autowired
//	private PartnerService partnerService;
//	
//	@Autowired
//	private WorkShopVoMapper workShopVoMapper;
	
	@Resource
	private WorkshopMasterMapper workshopMasterMapper;
    @Autowired
    private WorkshopMasterBizService workshopMasterBizService;
	
	@Autowired
	private PointBizService pointBizService;
	
	@Autowired
	private WxTPropertiesMapper wxTPropertiesMapper;
	
	@Autowired
	private DicItemVoMapper dicItemVoMapper;
	
	@Autowired
	private EtoBizService etoBizService;


//	private void registerMechanicInfoToO2O(WorkshopEmployee workshopEmployee) throws WxPltException{
//		try {
//			String o2oRegisterMechanicUrl = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC);
//			Map<String, String> postData = getO2ORegisterPostData(workshopEmployee);
//			Map<String, String> httpHeader = new HashMap<String, String>();
//			httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
//			String responseContent = HttpSender.postJSON(o2oRegisterMechanicUrl, postData, httpHeader);
//			log.info("responseContent: " + responseContent);
//			if (responseContent == null){
//				throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
//			}
//			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
//			String errCode = responseJsonObj.getString("errcode");
//			String errMsg = responseJsonObj.getString("errmsg");
//			if("60104".equals(errCode) && errMsg.startsWith("mobile existed: ")){
//				//企业号已存在，更新技师编码
//				WorkshopEmployee newEmployee = new WorkshopEmployee();
//				newEmployee.setCode(errMsg.substring(16));
//				newEmployee.setId(workshopEmployee.getId());
//				workshopEmployeeMapper.updateByPrimaryKeySelective(newEmployee);
//				workshopEmployee.setCode(newEmployee.getCode());
//				//调用企业号修改接口
////				updateRegisterMechanicInfo(workshopEmployee);
//			}else if (!"0".equals(errCode)){
//				throw new WxPltException(errMsg);
//			}
//		} catch (WxPltException e){
//			throw e;
//		} catch (Exception e) {
//			log.error("register mechanic to o2o exception", e);
//			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
//		}
//	}

//	private Map<String, String> getO2ORegisterPostData(WorkshopEmployee workshopEmployee) {
//		String key = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC_KEY);
//		log.info("key: " + key);
//		String userid = workshopEmployee.getCode();
//		String name = workshopEmployee.getName();
//		String cellphone = workshopEmployee.getMobile();
//		String wechatid = workshopEmployee.getWechatAccount();
//		if (StringUtils.isNull(wechatid)){
//		    wechatid = "";
//		}
//		String wsid = workshopEmployee.getWorkshopId().toString();
//		String wsname = workshopEmployee.getWorkshopName();
//		WxTUser user = ContextUtil.getCurUser();
//		String bd = user.getChName();
//		String sp = user.getOrgName();
//		String spid = workshopEmployee.getPartnerId().toString();
//		String position = workshopEmployee.getEmployeeType();
//		//技师类型转换
//		if("Y".equalsIgnoreCase((String)Constants.getSystemPropertyByCodeType("WorkshopEmployee.synO2O.employeeTypeMapOn"))) {
//			position = transferEmployeeType(position);
//		}
//		String departmentIds = partnerService.getO2ODepartmentIdsByPartnerId(workshopEmployee.getPartnerId(), workshopEmployee.getEmployeeType());
//		StringBuilder tokenStr = new StringBuilder();
//		tokenStr.append(userid).append(name).append(cellphone).append(wechatid).append(wsid)
//				.append(wsname).append(sp).append(spid).append(bd).append(position).append(departmentIds);
//		if(workshopEmployee.getOldPartnerId() != null){
//			tokenStr.append(workshopEmployee.getOldPartnerId());
//		}
//		tokenStr.append(key);
//		log.info("tokenStr: {}", tokenStr.toString());
//		String token = DigestUtils.md5Hex(tokenStr.toString());
//		Map<String, String> postData = new HashMap<String, String>();
//		postData.put("userid", userid);
//		postData.put("name", name);
//		postData.put("cellphone", cellphone);
//		postData.put("wechatid", wechatid);
//		postData.put("wsid", wsid);
//		postData.put("wsname", wsname);
//		postData.put("bd", bd);
//		postData.put("sp", sp);
//		postData.put("spid", spid);
//		if(workshopEmployee.getOldPartnerId() != null){
//			postData.put("oldspid", workshopEmployee.getOldPartnerId().toString());
//		}
//		postData.put("position", position);
//		postData.put("token", token);
//		if (!StringUtils.isEmpty(departmentIds)){
//			postData.put("department", departmentIds);
//		}
//		return postData;
//	}
	
	public JSONObject synMechanicInfoToScrm(WorkshopEmployee workshopEmployee, WxTUser user) throws WxPltException{
		try {
			//判断所属门店是否合作门店，否，不同步
			WorkshopMaster shop = workshopMasterMapper.selectByPrimaryKey(workshopEmployee.getWorkshopId());
			if(!shop.getStatus().equals(WorkshopMaster.WORKSHOP_STATUS3)){
				LogUtils.addInfoLog(ContextUtil.getCurUserId(), "com.chevron.pms.business.WorkshopEmployeeBizService.synMechanicInfoToScrm",
						workshopEmployee.getCode() + "," + workshopEmployee.getMobile() + ",非合作门店，不同步技师!");
				return null;
			}
			String o2oRegisterMechanicUrl = (String) Constants.getSystemPropertyByCodeType(Constants.B2B_SCRM_SYN_MECHANIC_URL);
			Map<String, Object> postData = getScrmPostData(workshopEmployee, user);
			Map<String, String> httpHeader = new HashMap<String, String>();
			httpHeader.put("Content-Type", "application/json;charset=utf-8");
			String responseContent = HttpSender.postJSON(o2oRegisterMechanicUrl, postData, httpHeader);
			log.info("responseContent: " + responseContent);
			if (responseContent == null){
				log.error("技师同步接口请求失败。url: {}, data: {}", o2oRegisterMechanicUrl, JsonUtil.writeValue(postData));
				throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
			}
			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
			boolean success = responseJsonObj.getBoolean("success");
			/*if("60104".equals(errCode) && errMsg.startsWith("mobile existed: ")){
				//企业号已存在，更新技师编码
				WorkshopEmployee newEmployee = new WorkshopEmployee();
				newEmployee.setCode(errMsg.substring(16));
				newEmployee.setId(workshopEmployee.getId());
				workshopEmployeeMapper.updateByPrimaryKeySelective(newEmployee);
				workshopEmployee.setCode(newEmployee.getCode());
				//调用企业号修改接口
//				updateRegisterMechanicInfo(workshopEmployee);
			}else */if (!success){
				String errMsg = responseJsonObj.getString("errorMsg");
//				String errorCode = responseJsonObj.getString("errorCode");
				LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.pms.business.WorkshopEmployeeBizService.synMechanicInfoToScrm", 
						responseContent, workshopEmployee.getMobile());
				throw new WxPltException("B2B_SCRM平台: "+errMsg);
			}
			return responseJsonObj;
		} catch (WxPltException e){
			throw e;
		} catch (Exception e) {
			log.error("register mechanic to o2o exception", e);
			LogUtils.addErrorLog(ContextUtil.getCurUser().getUserId(), "com.chevron.pms.business.WorkshopEmployeeBizService.synMechanicInfoToScrm", 
					e.getMessage(), workshopEmployee.getMobile());
			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
		}
	}
	
	public String transferEmployeeType(String employeeType) {
		Map<String, String> employeeTypeMap = new HashMap<String, String>();
		List<DicItemVo> list = dicItemVoMapper.selectByCode("WorkshopEmployee.employeeType");
		if(!list.isEmpty()) {
			for(DicItemVo vo : list) {
				employeeTypeMap.put(vo.getDicItemCode(), vo.getDicItemDesc());
			}
		}
		String newValue = employeeTypeMap.get(employeeType);
		if(StringUtils.isBlank(newValue)) {
			return employeeType;
		}
		return newValue;
	}

	private Map<String, Object> getScrmPostData(WorkshopEmployee workshopEmployee, WxTUser user) throws WxPltException {
		String key = (String) Constants.getSystemPropertyByCodeType(Constants.B2B_SCRM_ACCESS_SECRET_KEY);
		log.info("key: " + key);
		String userid = workshopEmployee.getCode();
		String name = workshopEmployee.getName();
		String cellphone = workshopEmployee.getMobile();
//		String wechatid = workshopEmployee.getWechatAccount();
//		if (StringUtils.isNull(wechatid)){
//		    wechatid = "";
//		}
		String wsid = workshopEmployee.getWorkshopId().toString();
		String wsname = workshopEmployee.getWorkshopName();
		
		String bd = user.getChName();
		String sp = user.getOrgName();
		String spid = user.getOrgId().toString();
		
		
		String position = workshopEmployee.getEmployeeType();
		//技师类型转换
		if("Y".equalsIgnoreCase((String)Constants.getSystemPropertyByCodeType("WorkshopEmployee.synScrm.employeeTypeMapOn"))) {
			position = transferEmployeeType(position);
		}
		position = "Owner".equals(position) ? "0" : "1";
		String timestamp = DateUtil.getCurrentDate("yyyy-MM-dd HH:mm:ss");
//		String departmentIds = partnerService.getO2ODepartmentIdsByPartnerId(workshopEmployee.getPartnerId(), workshopEmployee.getEmployeeType());
//		if(workshopEmployee.getOldPartnerId() != null){
//			tokenStr.append(workshopEmployee.getOldPartnerId());
//		}
		Map<String, Object> postData = new TreeMap<String, Object>();
		postData.put("userid", userid);
		postData.put("name", name);
		postData.put("cellphone", cellphone);
//		postData.put("wechatid", wechatid);
		postData.put("wsid", wsid);
		postData.put("wsname", wsname);
		Map<String, Object> params = new HashMap<String, Object>(2);
		params.put("id", workshopEmployee.getWorkshopId());
		WorkshopMaster workshop = workshopMasterBizService.querySimpleByParams(params).get(0);
		postData.put("province", workshop.getProvinceName());
		postData.put("city", workshop.getCityName());
		String wstype = "0";
		if((workshop.getBusinessWeight() & 3) == 3) {
			wstype = "2";
		}else if((workshop.getBusinessWeight() & 2)>0) {
			wstype = "1";
		}
		postData.put("wstype", wstype);
		postData.put("bd", bd);
		postData.put("sp", sp);
		postData.put("spid", spid);
//		if(workshopEmployee.getOldPartnerId() != null){
//			postData.put("oldspid", workshopEmployee.getOldPartnerId().toString());
//		}
		postData.put("position", position);
		postData.put("timestamp", timestamp);
//		if (!StringUtils.isEmpty(departmentIds)){
//			postData.put("department", departmentIds);
//		}
		StringBuilder tokenStr = new StringBuilder();
		for(String k : postData.keySet()){
			tokenStr.append(k).append("=").append(postData.get(k)).append("&");
		}
		tokenStr.append("access_secret=").append(key);
//		tokenStr.append(userid).append(name).append(cellphone)//.append(wechatid)
//			.append(wsid).append(wsname).append(spid).append(sp).append(bd).append(position);//.append(departmentIds);
//		tokenStr.append(key);
		log.info("tokenStr: {}", tokenStr.toString());
		String token = DigestUtils.md5Hex(tokenStr.toString());
		postData.put("token", token);
		return postData;
	}
	public Map<String, Object> saveEmployeeListByUser(List<WorkshopEmployee> employeeList, Long workshopId, String workshopName,Long orgId) throws WxPltException {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (employeeList == null){
			return resultMap;
		}
	/*	if (employeeList.size() != 1){
			throw new WxPltException(MessageResourceUtil.getMessage("task.workshop_employee.submit_one_record"));
		}*/
	
		for (WorkshopEmployee employee : employeeList) {
			if(employee.getVersionNo() != null && employee.getVersionNo() == 1) {
				
			}
		}
		return resultMap;
	}
	
	public void createEmployeeByWorkshop(Long workshopId) throws WxPltException {
        WorkshopMaster newWorkShop = workshopMasterBizService.getBean(workshopId);
        if(newWorkShop.getExcuteUserId() == null) {
        	throw new WxPltException("客户“" + newWorkShop.getWorkShopName() + "”没有执行人");
        }
        /* 都放开 都创建 2023-08-11
        if(!WorkshopMaster.CUSTOMER_TYPE1.equals(newWorkShop.getCustomerType()) || newWorkShop.getBusinessWeight() == null
        		|| newWorkShop.getBusinessWeight() != Constants.BRAND_CDM) {
        	//非金富力门店不创建技师
        	return;
        }*/
        //创建员工集合
        boolean created = false;
        WxTUser user = wxTUserMapper.selUserByUseridNew(newWorkShop.getExcuteUserId());
		Map<String, Object> resultMap = new HashMap<String, Object>();
    	//店主
    	if( StringUtils.isNotBlank(newWorkShop.getExtProperty5()) 
    			&& (StringUtils.isNotBlank(newWorkShop.getExtProperty4()) || newWorkShop.getExtProperty5().equals(newWorkShop.getContactPersonTel()))) {
    		 WorkshopEmployee workshopEmployer = new WorkshopEmployee();
             workshopEmployer.setName(StringUtils.isNotBlank(newWorkShop.getExtProperty4()) ? newWorkShop.getExtProperty4() : newWorkShop.getContactPerson());
             workshopEmployer.setMobile(newWorkShop.getExtProperty5());
             workshopEmployer.setEmployeeType("Boss");
             workshopEmployer.setVersionNo(1);
             workshopEmployer.setCreator(newWorkShop.getExcuteUserId());
             workshopEmployer.setIsHasProduct(1);
             saveScrmEmployee(workshopEmployer, workshopId, newWorkShop.getWorkShopName(), resultMap, user,newWorkShop.getPartnerId());
             created = true;
    	} 
        //店员
    	if(!newWorkShop.getContactPerson().equals(newWorkShop.getExtProperty5()) 
    			|| !created) {
    		 WorkshopEmployee workshopEmploy = new WorkshopEmployee();
             workshopEmploy.setName(newWorkShop.getContactPerson());
             workshopEmploy.setMobile(newWorkShop.getContactPersonTel());
             if(!created) {
                 workshopEmploy.setEmployeeType("Owner");
            	 workshopEmploy.setIsHasProduct(1);
             }else {
            	 workshopEmploy.setEmployeeType("Mechanic");
             }
             workshopEmploy.setVersionNo(1);
             workshopEmploy.setCreator(newWorkShop.getExcuteUserId());
             saveScrmEmployee(workshopEmploy, workshopId, newWorkShop.getWorkShopName(), resultMap, user,newWorkShop.getPartnerId());
    	}
	}
	
	public Map<String, Object> saveEmployeeList(List<WorkshopEmployee> employeeList, Long workshopId, String workshopName) throws WxPltException {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (employeeList == null){
			return resultMap;
		}
		
		if (employeeList.size() != 1){
			throw new WxPltException(MessageResourceUtil.getMessage("task.workshop_employee.submit_one_record"));
		}
		WxTUser user = ContextUtil.getCurUser();
		for (WorkshopEmployee employee : employeeList) {
			if(employee.getVersionNo() != null && employee.getVersionNo() == 1) {
				saveScrmEmployee(employee, workshopId, workshopName, resultMap, user,user.getOrgId());
				continue;
			}
		}
		return resultMap;
	}
	
	protected void saveScrmEmployee(WorkshopEmployee employee, Long workshopId, String workshopName, Map<String, Object> resultMap, WxTUser user,Long orgId) throws WxPltException {
		employee.setVersionNo(1);
		employee.setWorkshopId(workshopId);
		employee.setWorkshopName(workshopName);
		employee.setPartnerId(orgId);
		if(employee.getEnableFlag() == null) {
			//新增技师更新状态
			employee.setEnableFlag(1);
		}
		boolean update = false;
		WorkshopEmployee existEmployee= checkEmployeeExist(employee);
		if (existEmployee == null){
			
			employee.setCreationTime(DateUtil.getCurrentDate());
			if(employee.getCreator() == null) {
				employee.setCreator(ContextUtil.getCurUserId());
			}
			employee.setCode(CommonUtil.generateCode("M"));
			workshopEmployeeMapper.insertSelective(employee);
			
			resultMap.put("create", true);
			
		} else {
			employee.setUpdateTime(DateUtil.getCurrentDate());
			employee.setUpdator(user.getUserId());
			employee.setId(existEmployee.getId());
			if(!orgId.equals(existEmployee.getPartnerId())){
				employee.setOldPartnerId(existEmployee.getPartnerId());
			}
			
			workshopEmployeeMapper.updateByPrimaryKeySelective(employee);
			employee.setCode(existEmployee.getCode());
			resultMap.put("update", true);
			update = true;
		}
		// 由ETO同步至大咖汇，所以此处不再同步大咖汇，仅同步ETO (配置 WorkshopEmployee.synB2bMechnicOn 为 N)
		/*if("Y".equals(Constants.getSystemPropertyByCodeType("WorkshopEmployee.synB2bMechnicOn"))) {
			synMechanicInfoToScrm(employee, user);
		}*/
		//奖励注册积分
		if(existEmployee == null || existEmployee.getVersionNo() != 1) {
			awardB2bPointsByRegister(employee);
		}
		//同步门店直播技师
		try{
			etoBizService.synWorkshopEmployee(employee, update, false);
		}catch (WxPltException e){
			if(e.getMessage().contains("尚未查询到用户信息")){
				etoBizService.synWorkshopEmployee(employee, false, false);
			}
		}
	}

//	private void checkEmployeeInfoValid(WorkshopEmployee employee) throws WxPltException {
//		//1, check employee's mobile phone has regist as system account
//		WxTUser tUser = userService.findUserByLoginName(employee.getMobile());
//		if (tUser != null){
//			throw new WxPltException(MessageResourceUtil.getMessage("task.workshop_employee.exist_phone_account"));
//		}
//		
//	}

//	private void updateRegisterMechanicInfo(WorkshopEmployee workshopEmployee) throws WxPltException {
//		try {
//			String o2oUpdateRegisterMechanicUrl = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_UPDATE_REGISTER_MECHANIC);
//			Map<String, String> postData = getO2ORegisterPostData(workshopEmployee);
//			Map<String, String> httpHeader = new HashMap<String, String>();
//			httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
//			String responseContent = HttpSender.postJSON(o2oUpdateRegisterMechanicUrl, postData, httpHeader);
//			log.info("responseContent: " + responseContent);
//			if (responseContent == null){
//				throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
//			}
//			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
//			String errCode = responseJsonObj.getString("errcode");
//			String errMsg = responseJsonObj.getString("errmsg");
//			if (!"0".equals(errCode)){
//				throw new WxPltException(errMsg);
//			}
//			
//		} catch (WxPltException e){
//			throw e;
//		} catch (Exception e) {
//			log.error("update register mechanic to o2o exception", e);
//			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
//		}
//	}
	
	public WorkshopEmployee checkEmployeeExist(WorkshopEmployee workshopEmployee) {
		WorkshopEmployeeExample example = new WorkshopEmployeeExample();
		example.createCriteria().andMobileEqualTo(workshopEmployee.getMobile());
		List<WorkshopEmployee> employeeList = workshopEmployeeMapper.selectByExample(example);
		if (employeeList != null && employeeList.size() > 0){
			return employeeList.get(0);
		}
		return null;
	}
	
	/**
	 * 查询门店员工统计数据
	 * @param partnerId  required
	 * @param workshopId optional
	 * @param dateFrom   required
	 * @param dateTo     required
	 * @return
	 */
	public List<WorkshopEmpSummary> queryWorkshopEmpSummaryList(Long partnerId, Long workshopId, Date dateFrom, Date dateTo){
		List<WorkshopEmployee> empList = queryWorkshopEmpList(partnerId, workshopId, null, dateFrom, dateTo);
		if (empList == null){
			return Collections.emptyList();
		}
		
		Map<Long, WorkshopEmpSummary> summaryMap = new HashMap<Long, WorkshopEmpSummary>();
		for (WorkshopEmployee emp : empList){
			if (!summaryMap.containsKey(emp.getWorkshopId())){
				summaryMap.put(emp.getWorkshopId(), new WorkshopEmpSummary(emp.getWorkshopId(), emp.getWorkshopName()));
			}
			WorkshopEmpSummary summary = summaryMap.get(emp.getWorkshopId());
			if (EMPLOYEE_TYPE_OWNER.equals(emp.getEmployeeType())){
				summary.setHasOwner(true);
			} else if (EMPLOYEE_TYPE_MECHANIC.equals(emp.getEmployeeType())){
				summary.setMechanicCount(summary.getMechanicCount() + 1);
			}
		}
		
		return new ArrayList<WorkshopEmpSummary>(summaryMap.values());
	}
	
	/**
	 * 根据条件查询门店员工列表
	 * @param partnerId   optional
	 * @param workshopId  required
	 * @param dateFrom    required
	 * @param dateTo      required
	 * @return
	 */
	public List<WorkshopEmployee> queryWorkshopEmpList(Long partnerId, Long workshopId, String workshopName, Date dateFrom, Date dateTo){
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("partnerId", partnerId);
		paramMap.put("workshopId", workshopId);
		paramMap.put("workshopName", workshopName);
		paramMap.put("dateFrom", dateFrom);
		paramMap.put("dateTo", dateTo);
		return workshopEmployeeMapper.selectMechanicBySpAndWorkshop(paramMap);
	}
	
	/**
	 * 根据员工编码获取门店员工
	 * @param code
	 * @return
	 */
	public WorkshopEmployee queryWorkshopEmployeeByCode(String code){
//		WorkshopEmployeeExample example = new WorkshopEmployeeExample();
//		example.createCriteria().andCodeEqualTo(code);
//		List<WorkshopEmployee> empList = workshopEmployeeMapper.selectByExample(example);
//		if (empList != null && !empList.isEmpty()){
//			return empList.get(0);
//		}
		return workshopEmployeeMapper.selectByCode(code);
	}
	
	/**
	 * 兼容数据权限控制的分页处理
	 * <AUTHOR> 2016-12-2 上午10:33:03
	 * @param workshopEmployeeParams
	 * @return
	 */
	public  Map<String, Object>  queryWorkshopEmpListNew(WorkshopEmployeeParams workshopEmployeeParams)
	{
		 Map<String, Object> resultMap = new HashMap<String,Object>();
		 try
		 {
			 if(null==workshopEmployeeParams)
			 {
					resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
					resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
					return resultMap;
			 }
			 
			 String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			 if("false".equals(is_custommybatisintercepor))
			 {
				 workshopEmployeeParams.setIsOpernCustomMybatisInterceptor("2");//关闭的
			 }
			 
			 //查询数据
			//1.查询数据
			Long totalRecord = 0L;
			List<WorkshopEmployee> lstEmployee = workshopEmployeeMapper.selectMechanicBySpAndWorkshopNew(workshopEmployeeParams);
			if("false".equals(is_custommybatisintercepor))//没有开数据权限
			{
				totalRecord = workshopEmployeeMapper.countSelectMechanicBySpAndWorkshopNew(workshopEmployeeParams);
			}else
			{
				
				totalRecord = workshopEmployeeParams.getTotalCount();
			}
			resultMap.put(RESULT_LST_KEY, lstEmployee);
			resultMap.put(RESULT_TOTALRECORD_KEY, totalRecord);
		 }catch (Exception e) {
				log.error(e.getMessage(),e);
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		 }
		 return resultMap;
	}
	
	
	public void test_registerMechanicInfoToO2O(){
		try {
			String o2oRegisterMechanicUrl = "http://scrmservicestg.techronworks.com.cn:8024/api/common/registeroffline";//(String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC);
			String key = "123456";//(String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC_KEY);
			log.info("key: " + key);
			String random = (System.currentTimeMillis() % 1000000000 )+ "";
			StringBuilder tokenStr = new StringBuilder();
			tokenStr.append("t_" + random).append("测试").append("12" + random).append("").append("96295")
					.append("测试门店").append("滴滴养车").append("9").append("孙探微").append("Mechanic").append("15").append(key);
			log.info("tokenStr: {}", tokenStr.toString());
			String token = DigestUtils.md5Hex(tokenStr.toString());
			Map<String, Object> postData = new HashMap<String, Object>();
			postData.put("userid", "t_" + random);
			postData.put("name", "测试"); 
			postData.put("cellphone", "12" + random);
			postData.put("wechatid", "");
			postData.put("wsid", "96295");
			postData.put("wsname", "测试门店");
			postData.put("bd", "孙探微");
			postData.put("sp", "滴滴养车");
			postData.put("spid", "9");
			postData.put("position", "Mechanic");
			postData.put("department", "15");
			postData.put("token", token);
//			String departmentIds = partnerService.getO2ODepartmentIdsByPartnerId(12964l, "Mechanic");
//			if (!StringUtils.isEmpty(departmentIds)){
//				postData.put("department", departmentIds);
//			}
			Map<String, String> httpHeader = new HashMap<String, String>();
//			httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
			String responseContent = HttpSender.postJSON(o2oRegisterMechanicUrl, postData, httpHeader);
			log.info("responseContent: " + responseContent);
			if (responseContent == null){
				log.error("test_registerMechanicInfoToO2O===========================response null");
			}
			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
			String errCode = responseJsonObj.getString("errcode");
			String errMsg = responseJsonObj.getString("errmsg");
			if (!"0".equals(errCode)){
				log.error("test_registerMechanicInfoToO2O====================================error:" + errMsg);
			}
		} catch (Exception e) {
			log.error("test_registerMechanicInfoToO2O======================================exception: " + e.getMessage(), e);
		}
	}
	
	public void awardB2bPointsByRegister(WorkshopEmployee workshopEmployee) {
		Map<String, Object> reqMap = new HashMap<String, Object>(1);
		reqMap.put("codetype", "B2b.awardPoints.register");
		WxTProperties properties = wxTPropertiesMapper.selectByMap(reqMap);
		if(properties == null || StringUtils.isBlank(properties.getCode())) {
			return;
		}
		//发放积分验证
		int validate = workshopEmployeeMapper.validateRegisterAward(workshopEmployee);
		if(validate > 0) {
			//技师不能发积分
			return;
		}
		double points = Double.parseDouble(properties.getCode());
		Date now = DateUtil.getCurrentDate();
		List<B2BPointDetail> pointList = new ArrayList<B2BPointDetail>();
		B2BPointDetail b2bPointDetail = new B2BPointDetail();
		b2bPointDetail.setComments("【" + DateUtil.getDateStr(now, "yyyy-MM-dd") + "】注册奖励积分【" + points + "】");
		b2bPointDetail.setPoint(points);
		b2bPointDetail.setSalesChannel("CDM");
		b2bPointDetail.setTradeNo(workshopEmployee.getCode());
		b2bPointDetail.setEarnType(EarnType.MECHANIC_REGISTER.getValue());
		b2bPointDetail.setUserId(workshopEmployee.getCode());
		pointList.add(b2bPointDetail);
		try {
			pointBizService.importB2BPoint(pointList);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("奖励注册积分失败：" + e.getMessage(), e);
			LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.pms.business.WorkshopEmployeeBizService.awardB2bPointsByRegister", 
					"奖励注册积分失败：" + e.getMessage(), workshopEmployee.getCode() + "," + points);
		}

	}
}
