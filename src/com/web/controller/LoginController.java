package com.web.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.common.util.ContextUtil;
import com.common.util.HttpSessionGets;
import com.common.util.MobileUtils;
import com.common.util.StringUtils;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * LoginController负责打开登录页面(GET请求)和登录出错页面(POST请求)，
 * 
 * 真正登录的POST请求由Filter完成,
 * 
 * <AUTHOR>
 */
@Controller
public class LoginController {

	private static Logger log = LoggerFactory.getLogger(LoginController.class);

	@RequestMapping(value = "/login.do", method = { RequestMethod.GET, RequestMethod.HEAD })
	public String login(@RequestParam(value = "locale", required = false) String locale, HttpServletRequest request,
			HttpServletResponse response) {
		HttpSessionGets.setLocale(request, locale);
		if (SecurityUtils.getSubject().isAuthenticated() && !"pwd".equals(request.getParameter("flag"))) {
			return "redirect:/";
		} else {
			String ua = request.getHeader("User-Agent");
			if (MobileUtils.checkAgentIsMobile(ua)) {
				return "mobileLogin";
			} else {
				return "login";
			}
		}
	}

	// 帐号密码输入有误
	@RequestMapping(value = "/login.do", method = RequestMethod.POST)
	public String fail(
			@RequestParam(value = FormAuthenticationFilter.DEFAULT_USERNAME_PARAM, required = false) String userName,
			Model model, HttpServletRequest request) {
		if (StringUtils.isEmpty(userName)) {
			return "login";
		}
		String token = request.getParameter(ContextUtil.APP_TOKEN_KEYWORD);

		if (!StringUtils.isNull(token)) {
			return "rpcLoginError";
		}
		if (SecurityUtils.getSubject().isAuthenticated() && !"pwd".equals(request.getParameter("flag"))) {
			log.info("authenticated....");
			return "redirect:/";
		}

		model.addAttribute(FormAuthenticationFilter.DEFAULT_USERNAME_PARAM, userName);
		return "login";
	}

	@ResponseBody
	@RequestMapping(value = "/unauthorized.do", method = { RequestMethod.GET, RequestMethod.HEAD }, produces = "text/plain;charset=utf-8")
	public String unauthorized() {
		return "对不起， 您没有权限！您可以拨打电话4006686065联系管理员！";
	}
}
