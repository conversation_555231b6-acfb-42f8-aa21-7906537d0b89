select sales_cai, sales_name_cn, customer_name_cn, 
case when channel_weight&3=3 then 'ALL' when channel_weight&1>0 then '乘用车' when channel_weight&2>0 then '商用油' end product_channel 
from dw_access_control_customer_org_sales s 
where s.region in (select d.dic_item_code from wx_t_dic_item d where d.dic_type_code='Region.Indirect')
order by sales_cai

select region, supervisor_cai, supervisor_name_cn, sales_cai, sales_name_cn, customer_name_cn, product_channel 
from dw_customer_org_sales s 
where s.region in (select d.dic_item_code from wx_t_dic_item d where d.dic_type_code='Region.Indirect')
and s.sales_cai not in ('NA')
order by region,sales_cai,s.distributor_id


select sc1.bu, sc1.bu_manager_cai, sc1.bu_manager_name, sc1.sales_channel_name, sc1.channel_manager_cai, sc1.channel_manager_name, 
region, supervisor_cai, supervisor_name_cn, sales_cai, sales_name_cn, product_channel, s.distributor_id,c1.customer_code, s.customer_name_cn
--,s.from_source
--delete s
from dw_customer_org_sales s 
left join dw_region_sales_channel_rel sc1 on s.region=sc1.region_name
left join [PP_MID].[dbo].[syn_dw_to_pp_customer] c1 on c1.distributor_id_pp=s.distributor_id
--where s.region in (select d.dic_item_code from wx_t_dic_item d where d.dic_type_code='Region.Indirect') and 
--s.sales_cai not in ('NA') and 
--c1.customer_code is not null
order by sc1.bu desc, sc1.sales_channel_name, region,sales_cai,c1.distributor_id