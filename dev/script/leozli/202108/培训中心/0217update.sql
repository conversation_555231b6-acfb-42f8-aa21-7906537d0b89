
delete wx_t_operation_permission where module_code='TrainingCenter.pc'
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.1.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.1.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.1.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.1.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.2.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.2.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.2.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.2.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.2.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.2.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.2.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.2.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.2.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.2.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.2.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.2.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.3.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.3.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.3.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'TrainingCenter.3.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.3.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.3.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.3.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'TrainingCenter.3.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.3.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.3.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.3.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.3.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.4.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.4.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.4.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.4.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.5.pc', 1, 1, '培训中心-PC端：1-文件上传', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.5.pc', 2, 1, '培训中心-PC端：2-文件删除', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.5.pc', 4, 1, '培训中心-PC端：4-文件重命名', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'), 'TrainingCenter.5.pc', 8, 1, '培训中心-PC端：8-文件移动端可见设置', 1, getdate());

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, (select role_id from wx_t_role where ch_role_name='Chevron_Training_Center_Admin'),1
from wx_t_user u where u.status=1 and u.login_name in ('KCCH')

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, (select role_id from wx_t_role where ch_role_name='Chevron_Sales_Support_Team'),1
from wx_t_user u where u.status=1 and u.login_name in ('Vicky',
'DGKH',
'CSHA',
'EBNL',
'LQGK',
'BCAE',
'QIOY',
'LIFJ',
'KCCH')

insert into wx_t_homepagev2_entry (url, label,name,path,icon,permission_weight,usageFlag,channel_weight,ext_flag) 
values ('','培训中心', '培训中心','Resources','item-pending-cdm',1279,2, 3,0)
--home_page_v2_todo

select * 
--update e set e.permission_weight=224
from wx_t_homepagev2_entry e where name='培训中心'