<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter"%>
<%@ page import="org.apache.shiro.authc.LockedAccountException "%>
<%@ page import="com.ass.shiro.exp.WxAuthException "%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/"/>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	
	/*获取登录信息,此处无须判断是否登录，因为在url访问全县中已经限制*/
	//Subject subject = SecurityUtils.getSubject();		
	//ShiroUser user = (ShiroUser)subject.getPrincipal();
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title>注册</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<link href="css/style.css" rel="stylesheet" type="text/css" media="all" />
<link href="css/login.css" rel="stylesheet" />
<link href="css/dpl.css" rel="stylesheet">
<link href="css/bui.css" rel="stylesheet">
<!-- 
<link href="css/dpl.css" rel="stylesheet" />
<link href="css/bui.css" rel="stylesheet" /> -->
<link rel= "shortcut icon " href= "${ctx }images/favicon.ico">
<script type="text/javascript" src="${ctx }common/js/jquery-1.9.1.min.js"></script>
<script language="javascript" type="text/javascript" src="${ctx }common/js/jquery.json-2.4.min.js"></script>
<script language="javascript" type="text/javascript" src="${ctx }common/js/jquery.jsonrpcclient.js"></script>
<script type="text/javascript" src="${ctx }common/js/qrcode.js"></script>
<script type="text/javascript" src="${ctx }common/js/jquery.qrcode.js"></script>
<script type="text/javascript" src="${ctx }common/js/sea.js"></script>
<script type="text/javascript" src="${ctx }common/js/config.js"></script>
<script type="text/javascript" src="${ctx }common/js/common.js"></script>
<script type="text/javascript" src="${ctx }common/js/login.js"></script>

<style type="text/css">
.wxLogTitle {
	color: #FAF6F6 !important;
	font-size: 21px !important;
	padding-top: 20px;
}
.wxLogTitleImg {
	margin-top: 0px !important;
	margin-bottom: 0px !important;
}
#validateCode:-ms-input-placeholder{
	color:#ff0
}
.form-horizontal{
	margin : 50px auto;
}
.control-group{
	width : 500px;
	padding-left:100px;
	margin : auto auto;
}
.registerButton{
	width : 80px;
	margin : auto auto;
}
</style>
<script>
	var foo = new $.JsonRpcClient({
		ajaxUrl : '/wxPublicRpc.do'
	});
    BUI.use('bui/form',function(Form){
    	Form.Rules.add({
    		name : 'confirmpassword',  //规则名称
	msg : '两次密码输入不一致',//默认显示的错误信息
	validator : function(value,baseValue,formatMsg){ //验证函数，验证值、基准值、格式化后的错误信息
		if(baseValue == 1){
		return;
			var password = $("input[name='confrimPassword']").val();
			if(password != "" && password !== value){
				return formatMsg;
			}
		}
		else{
			var password = $("input[name='password']").val();
			if(password !== value){
				return formatMsg;
			}
		}
		
	}
    	});
    	new Form.Form({
    		srcNode : '#form'
    	}).render();
	});
 	
	function tosubmit(){
		$.ajax({
			url: "checkAccount.do",
			data:{companyAccount:$("input[name='companyAccount']").val()},
			type: 'POST',
			async: false,
			success: function(data){// 成功返回回调函数
				if(data.status == "success"){
					$("#form").submit();
				}
				else{
					alert(data.error);
				}
			}
		});
	}
</script>
</head>
<body>
	<div class="header">
        <div class="content">
            <a href="/" class="InlineBlock Left"><img class="mingdaoLogo InlineBlock"  src=" ../images/logo.png"></a>
            <div class="wxLogTitle">安踏零售精细化管理平台</div>
            <div class="Clear"></div>
        </div>
    </div>
 <div class="main">
 	<div class="form-horizontal">
 	<form action="/companyRegister.do" method="POST" id="form">
	 	<div class="control-group">
	 		<label class="control-label">公司帐号：</label>
	 		<div class="controls"><input type="text" name="companyAccount" data-rules="{required:true}" data-messages="{required:'请确认公司帐号'}"/></div>
	 	</div>
	 	<div class="control-group">
	 		<label class="control-label">公司名称：</label>
	 		<div class="controls"><input type="text" name="companyName" data-rules="{required:true}" data-messages="{required:'请确认公司名称'}"/></div>
	 	</div>
	 	<div class="control-group">
	 		<label class="control-label">密码：</label>
	 		<div class="controls"><input type="password" name="password" class="input-normal control-text" data-rules="{required:true,confirmpassword:1}" data-messages="{required:'请确认密码'}"></div>
	 	</div>
	 	<div class="control-group">
	 		<label class="control-label">确认密码：</label>
	 		<div class="controls"><input type="password" name="confrimPassword" data-rules="{required:true,confirmpassword:2}" data-messages="{required:'请确认密码'}"/></div>
	 	</div>
	 	<div class="registerButton">
	 		<input type="button" value="立即注册" class="button button-primary" onclick="tosubmit()">
	 	</div>
 	</form>
 	</div>
 </div>
</body>
</html>