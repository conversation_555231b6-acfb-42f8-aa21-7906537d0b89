package com.sys.quartz.job;

import com.chevron.master.service.GiftPackageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * @Description 门店开客礼包创建任务 每10分钟执行一次
 * <AUTHOR> Ma
 * @Date 16:30 04/01/2024
 **/
public class CreateWorkShopGiftPackageTask {

    private static Logger log = LoggerFactory.getLogger(CreateWorkShopGiftPackageTask.class);

    @Resource
    private GiftPackageService giftPackageService;

    public synchronized void execute(){
        log.info("createWorkShopGiftPackageTask job start----");
        giftPackageService.createWorkShopGiftPackage();
        log.info("createWorkShopGiftPackageTask job end----");
    }

}
