insert into dw_product (material_code ,material_en_desc,material_zh_desc ,creation_date,
status ,block_date,unblock_date,base_un ,unit,package_unit ,
abc_class ,forecast_leading_time,product_channel ,
qbr_product_channel ,sector ,effect_time,t1_category ,
t2_category ,t3_category ,brand ,hydraulic_ago_ci ,
hydraulic_trans_ago_commercial ,create_time,update_time,sync_status)
select sku, name_en,name, creation_date,p.master_status,block_date,unblock_date,base_un, units,package_type, grade_abc, forecast_leading_time, product_channel,
qbr_product_channel, product_sector, effect_time, t1_category, t2_category, t3_category,brand, hyd_tra_ago_ind_com, hyd_tra_ago_ind_com, getdate(),update_time, status
from wx_t_product p
where not exists (select 1 from dw_product dp where dp.material_code=p.sku) --and (p.category is null or p.category!='QT') 
and p.create_time is null
and len(name_en)>0
--and status=1
and p.sku not in (SELECT dic_item_code FROM wx_t_dic_item WHERE dic_type_code = 'product.nosync')