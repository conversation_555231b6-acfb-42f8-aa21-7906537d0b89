--经销商
select o.organization_name "经销商名称", create_time "创建时间", case when o.status=1 then 'Active' else 'Inactive' end "状态"
  from wx_t_organization o where o.type=1 and o.create_time is not null
  
--门店
select o.organization_name "经销商名称", w.work_shop_name "门店名称", w.create_time "创建时间", w.activation_time "激活时间", 
case when w.status in (0, 1,-3) then '潜在' when w.status=3 then '合作' else w.status end "门店状态", 
case when w.delete_flag=0 then 'Active' else 'Inactive' end "状态"
 from wx_t_work_shop w left join wx_t_organization o on o.id=w.partner_id

--用户
select u.login_name, o.organization_name "机构名称", 
case when o.type=0 then '雪佛龙' when o.type=1 then '经销商' end "机构类型",
u.xz_time "创建时间", 
case when o.status=1 then 'Active' else 'Inactive' end "状态"
  from wx_t_user u join wx_t_organization o on o.id=u.org_id where o.type in (0, 1)
  
--sell in
select o.organization_name "经销商名称", po.order_no "订单编号", 
sum((isnull(pl.actual_amount,0)+isnull(pl.free_amount,0))*p.pack) "总升数",
po.create_time "创建时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=1) "提交时间",
				(select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=3) "确认时间",
(select p.dic_item_name from wx_t_dic_item p where p.dic_type_code = 'PARTNER_ORDER_STATUS' and p.dic_item_code = po.status) as "状态"
				from wx_t_partner_order po 
				left join wx_t_partner_order_line pl on pl.partner_order_id=po.id
				left join wx_t_product p on p.sku=pl.sku
				left join wx_t_organization o on o.id=po.partner_id
				left join [PP_MID].[dbo].[mid_partner_sale_config] sc on po.partner_sale_config_id=sc.id
		where po.status not in (0,4)
		group by po.id, po.order_no,o.organization_name,po.create_time,po.status
		order by po.id desc	
--sell through 
		
--技师
select o.organization_name, w.work_shop_name, we.name, we.creation_time
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
left join wx_t_organization o on o.id=w.partner_id
where we.version_no=1 and o.id !=9
order by we.creation_time desc;

