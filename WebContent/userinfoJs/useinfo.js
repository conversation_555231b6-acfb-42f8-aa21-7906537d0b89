
var foo = new $.JsonRpcClient({
	ajaxUrl :'/wxPublicRpc.do'
});
var selectUserDialog;
var gird;
var seelectUserObj;
var mUserId;
BUI.use('bui/overlay',function(Overlay){
	selectUserDialog  = new Overlay.Dialog({
      title:_lang.ChangePwd,
      contentId:'userinfo12',
	   success:function () {
		   modifyUserPassword();
	   }
	   });
});

function updateUserInfo(){
	 initPwdFun(getUserInfo);
}

//初始化密码验证功能
var pwdChangeHelper = null;
function initPwdFun(callback){
	if(pwdChangeHelper == null){
		pwdChangeHelper = new PwdChangeHelper($('#pwd_rule'), $("input[name='newPassword']"));
		pwdChangeHelper.requestRuleConfig(callback);
	}else{
		pwdChangeHelper.clearStatus();
		callback();
	}
}

//获取用户信息
function getUserInfo()
{
	var mUserId = $("#userId").val()
	//0.调用service去获取用户信息
	foo.call('wxUserService.getWxTUserInfoByUseridNew',[mUserId],function(result){
		//1.返回结果
		if(null==result.resultData)
		{
			common.alertMes("该用户不存在", "error");
			return;
		}
		//2.根据返回结果，填充数据
		$("input[name='loginName']").val(result.resultData.loginName);
		$("input[name='userNo']").val(result.resultData.userNo);
		$("input[name='chName']").val(result.resultData.chName);
		$("input[name='password']").val("");
		$("input[name='newPassword']").val("");
		$("input[name='confirmPassword']").val("");
		$("span[name='chName']").html("");
		$("input[name='address']").val(result.resultData.address);
		$("input[name='email']").val(result.resultData.email);
		$("input[name='mobile']").val(result.resultData.mobileTel);
	});
	//3.显示弹出框
	selectUserDialog.show();
	//4.隐藏下拉框
	if($("a.dropdown-toggle").attr("aria-expanded")=="true"){
		$("div.dropdown").removeClass('open');
	}
}
//更改密码
function modifyUserPassword()
{
	if(pwdChangeHelper.checkPwd(true) === false){
		$("input[name='newPassword']").focus();
		common.alertMes("新密码不符合规则", "error");
		return;
	}
	//0.判断新旧密码
	if($("input[name='newPassword']").val() == $("input[name='confirmPassword']").val()){
		    			password = $("input[name='newPassword']").val();
		    		}else{
		    			common.alertMes("两次密码不一致", "error");
		    			//alert("两次密码不一致");
		    			//window.location.href="${ctx }logout.do";
		    			return;
		    		}
//	//判断新密码的长度不能小于6位
//		if(pwdLength<6){
//		common.alertMes("密码不能小于6位", "error");
//		return;
//	}  		
	var name = $("input[name='loginName']").val();
	var password = $("input[name='password']").val();
	var newPassword = $("input[name='newPassword']").val();	
	
	//1.合格后，提交数据，访问service方法进行密码修改
	foo.call("userService.changeUserPassword",[name, password, newPassword],function(result){
	//2.返回结果，隐藏加载框，弹出提示框，最后关闭修改密码对话框
	if(result.code == "success"){
			common.alertMes("修改密码成功", "success");
			 //window.location.href="${ctx }logout.jsp";
			selectUserDialog.hide();
		}else{
			pwdChangeHelper.refreshSaveError(result.rules);
			common.alertMes("操作失败:"+result.codeMsg, "error");
		}
	});
}
//显示提示信息
$(document).ready(function(){
	if(remainderMessages && remainderMessages.length > 0){
		showRemainderDialog();
	}
});
var currentRemainderIndex = 0;
function showRemainderDialog(){
	BUI.use('bui/overlay',function(Overlay){
		var dialog = new Overlay.Dialog({
			title: "提示消息",
			bodyStyle: {'padding':0},
			bodyContent:$('<div id="remainder_message_dialog" style="width: 1000px;height: 500px;"><iframe width="100%" height="100%" frameborder="0"></iframe></div>'),
			buttons: [{ text:'上一条', elCls : 'btn-prev btn-success hide', handler : function(){ 
					currentRemainderIndex--; 
					if(currentRemainderIndex == 0){
						this.get('el').find('.btn-prev').addClass('hide');
					}
					this.get('el').find('.btn-next').removeClass('hide');
					this.get('el').find('.btn-close').addClass('hide');
					$('#closeRemainderCheckbox').prop('checked', remainderMessages[currentRemainderIndex]._closeRemainder == true);
					showRemainderMessage();
				}}
				,{ text:'下 一条', elCls : 'btn-next btn-success hide', handler : function(){
					currentRemainderIndex++; 
					if(currentRemainderIndex == remainderMessages.length - 1){
						this.get('el').find('.btn-next').addClass('hide');
						this.get('el').find('.btn-close').removeClass('hide');
					}
					this.get('el').find('.btn-prev').removeClass('hide');
					$('#closeRemainderCheckbox').prop('checked', remainderMessages[currentRemainderIndex]._closeRemainder == true);
					showRemainderMessage();
				}}
				,{ text:'关闭', elCls : 'btn-close hide', handler : function(){ this.close(); }}]
		});
		dialog.show();
		if(remainderMessages.length > 1){
			$('#remainder_message_dialog').parents('.bui-dialog:first').find('.btn-next').removeClass('hide');
		}else{
			$('#remainder_message_dialog').parents('.bui-dialog:first').find('.btn-close').removeClass('hide');
		}
		$('#remainder_message_dialog').parents('.bui-dialog:first').find('.bui-stdmod-footer').prepend('<label class="checkbox-label" style="margin-right: 10px;"><input class="control-checkbox" type="checkbox" id="closeRemainderCheckbox" onclick="updateRemainderCloseFlag(this);"/>不再提示</label>');
		showRemainderMessage();
	});
}
function showRemainderMessage(){
	var messageId = remainderMessages[currentRemainderIndex].messageId, updateStatus = !remainderMessages[currentRemainderIndex]._updatedStatus;
	remainderMessages[currentRemainderIndex]._updatedStatus = true;
//	LoadMask.show();
	$('#remainder_message_dialog iframe').attr('src', common.ctx + 'messagepush/view.do?messageId=' + messageId).load(function () {
		//关闭loading提示
//		LoadMask.hide();
		if(updateStatus){
			common.rpcClient.call('messagePushService.updateMessageStatusToRead', [messageId],
					function (result) {
			}, function (error) {
				common.ajaxTimeout(error);
			});
		}
	});
}
function updateRemainderCloseFlag(el){
	remainderMessages[currentRemainderIndex]._closeRemainder = el.checked;
	common.rpcClient.call('messagePushService.updateMessage', [{messageId: remainderMessages[currentRemainderIndex].messageId,reminderCloseFlag: el.checked ? 1 : 0}],
			function (result) {
	}, function (error) {
		common.ajaxTimeout(error);
	});
}