insert into wx_t_budget2021_expense_type ([create_time],[brand]
      ,[expense_main_type]
      ,[expense_main_code]
      ,[expense_type]
      ,[expense_code]
      ,[if_workflow]
      ,[if_import]
      ,[delete_flag])
select getdate(),[brand]
      ,'大区费用ASM' [expense_main_type]
      ,8 [expense_main_code]
      ,[expense_type]
      ,'ASM_' + [expense_code]
      ,0 [if_workflow]
      ,1 [if_import]
      ,[delete_flag] from wx_t_budget2021_expense_type where expense_main_code=1 and delete_flag=0

insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('ExpenseMainType', '8', '大区费用ASM', '', '1',1.5);
      
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'ChevronHomePage', 1, 1, '首页报表。1-全部', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'ChevronHomePage', 1, 1, '首页报表。1-全部', 1, getdate());
