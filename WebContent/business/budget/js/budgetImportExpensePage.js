var importUtil = null;
$(document).ready(function () {
	importUtil = ImportUtil.init( '数据导入','budget/importExpense.do', 'app/' + bu + ' Budget Actual Expense.xlsx', bu + ' Budget Actual Expense.xlsx', 
			{success: function(result, importDialog){
				ProgressBar.init(result.progressStatus, function(code, progressStatus){
					LoadMask.hide();
					if(code == 'success'){
						importDialog.close();
						query();
					}else {
						common.alertMes(progressStatus.message, 'error');
					}
				});
				return false
			}});
   // initQueryCtrl();
    initGrid();
    //初始化回车查询功能
    $('.query-panel input[type=text]').keydown(function(e){
		if(e.keyCode==13){
			$(this).parents('.query-panel:first').find('.btn-query').click();
		}
	});
});

//初始化查询控件
/*function initQueryCtrl() {
	//初始化合伙人查询控件
	var partnerCtrl = Ctrls.PartnerAutoSelect.init('#distributorId', {
		params: {resourceId: 'budgetImportExpense'},
		placeholder: '全部'
	});
}
*/
//初始化表格
var grid = null, gridStore = null;
function initGrid() {
	var distributWidth = 160;
	var expeseCodeWidth = 200;
	if(bu=="Consumer"){
		distributWidth = "50%";
		expeseCodeWidth = 120;
	}
    BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
        var columns = [{ title: '序号', dataIndex: '', width: 50, sortable: false, renderer: function(value, item, index){return index + 1;}},
                       { title: '月份', dataIndex: 'expenseMonth', width: 80, sortable: true, renderer: function (value, item, index){
        					return common.formatDate(new Date(value), 'yyyy-MM');
                       }},
//                       { title: '经销商编码', dataIndex: 'soldToCode', width: 100, sortable: true },
                       { title: '经销商名称', dataIndex: 'distributorName', width: distributWidth, sortable: true }];
        //配置费用项
        for(var i = 0; i < expenseItems.length; i++){
        	var item = expenseItems[i];
        	columns.push({ title: item.expenseTitle, dataIndex: 'expenseValue' + i, width: expeseCodeWidth,elCls: 'text-right', sortable: true, renderer: function (value, item, index){
        		if(value == null){
        			return '-';
        		}
				return common.formatMoney(value, 2);
            }});
        }
        //columns.push({ title: '批次号', dataIndex: 'batchNo', width: 45, sortable: true },
        columns.push({ title: '操作', dataIndex: 'id', width: 60, sortable: false, renderer: function(value, obj, index){
               			   var str = '';
               			   if(obj.effectiveFlag != 1){
        	        		   str += '<a href="javascript:;" onclick="deleteBean(' + value + ')">删除</a>';
               			   }else{
               				   str = '-';
               			   }
               			   return str;
               		   }});
        var params = buildParams();
        params.bu = bu;
        gridStore = new Data.Store({
			url: common.ctx + 'budgetimportexpense/data.do',
			autoLoad:false,
			totalProperty : 'total',
			root: 'resultLst',
			remoteSort: true,
			sortField: 'id',
			sortDirection: 'DESC',
			pageSize: 10,
			params: params,
		    proxy : {
				method : 'post'
			}
        });
        grid = new Grid.Grid({
            render: '#grid',
            columns: columns,
            loadMask: false,
            store: gridStore,
            itemStatusFields : { //设置数据跟状态的对应关系
//                selected : 'selected',
                disabled : 'effectiveFlag'
              },
            plugins: [Grid.Plugins.CheckSelection,Grid.Plugins.ColumnChecked],
            bbar: {
                pagingBar: true
            }
        });
        grid.render();
        common.initGrid(grid, null, true);
    });
}

//根据参数查询
function query() {
	gridStore.load(buildParams());
}

//构建当前查询条件
function buildParams(){
	if ($("#queryPanel").hasClass("query-adv")) {
		return {
			//expenseMonth: $('#expenseMonth').val(),
			dateFromStr: $('#dateFrom').val(),
			dateToStr: $('#dateTo').val(),
			distributorName: $('#distributorName').val(),
			soldToCode: $('#soldToCode').val(),
			batchNo: $('#batchNo').val(),
			queryField: null,
			queryType: 1,
			start: 0
		};
	} else {
		return {
			expenseMonth: null,
			distributorName: null,
			soldToCode: null,
			batchNo: null,
			queryField: $('#gKeyWord').val(),
			queryType: 2,
			start: 0
		};
	}
}

//删除单个对象
function deleteBean(id){
	common.confirmMes('确定要删除该记录吗？', function () {
		doDelete([id]);
	}, 'question');
}

//批量删除对象
function delBatch(){
	var items = grid.getItemsByStatus("selected");
	if(items.length == 0){
		common.alertMes("请先选择需要批量删除的导入费用", 'error');
		return;
	}
	
	var ids = [];
	for(var i = 0; i < items.length; i++){
		ids.push(items[i].id);
	}
	common.confirmMes('确定要删除所选导入费用吗?',function(){
		 doDelete(ids);
	 },'question');
}

//执行对象集合删除
function doDelete(ids){
	LoadMask.show();
	common.rpcClient.call("budgetImportExpenseService.delete", [ids], function (result) {
		LoadMask.hide();
		if (result.code == "success") {
			common.alertMes("删除成功！", "success");
			query();
		}else {
			common.alertMes("删除失败。" + result.errorMsg, "error");
		}
	}, function (error) {
		LoadMask.hide();
		common.ajaxTimeout(error);
	});
}

ImportUtil={
	init: function(title, url, templateFile, templateFileName, opts){
		var instance = $.extend({title: title, url: url, templateFile: templateFile, templateFileName: templateFileName, _clazz: this}, this.defaults);
		if(opts){
			instance = $.extend(instance, opts);
		}
		return $.extend(instance, this._base);
	},
	_base: {
		open: function(){
			if(!this._importDialog){
				this._importDialog = this._clazz.createDialog(this);
			}
			this._importDialog.show();
		},
		close: function(){
			
		}
	},
	createDialog: function(instance){
		var dialog = null, bch = ['<div class="field-group defineclass" style="padding-bottom:0px;">',
		  				'		<label class="field-label defineclass" style="vertical-align:baseline;">步骤1：</label>',
						'		<div class="control-group defineclass">',
						'			<span><i class="fa fa-info-circle"></i>模板下载</span>',
						'		</div>',
						'	</div>',
						'<div class="field-group defineclass" style="padding-top:0px;">',
						'		<label class="field-label"></label>',
						'		<div class="control-group">',
						'			<div><a href="', common.ctx, instance.templateFile,'" class="excel">模版下载(',instance.templateFileName,')</a></div>',
						'		</div>',
						'	</div>',
						'<div class="defineclass" style="border-top:1px dashed #e7eaec; color:#ffffff; background-color:#ffffff; height:1px; margin:20px 0;"></div>',
						'<div class="field-group defineclass" style="padding-bottom:0px;">',
		  				'		<label class="field-label defineclass" style="vertical-align:baseline;">步骤2：</label>',
						'		<div class="control-group defineclass">',
						'			<span><i class="fa fa-info-circle"></i>选择月份</span>',
						'		</div>',
						'	</div>',
						'<div class="field-group defineclass" style="padding-bottom:0px;">',
						'<label class="field-label"><span class="required-flag">*</span>费用月份：</label>',
						'<div class="control-group">',
						'	<input type="text" id="date" name="date" placeholder="月份" class="control-text control-calendar" onFocus="WdatePicker({dateFmt:\'yyyy-MM\'});" />',
						'</div>',
						'</div>',
						'	<div class="defineclass" style="border-top:1px dashed #e7eaec; color:#ffffff; background-color:#ffffff; height:1px; margin:20px 0;"></div>',
						'	<div class="field-group defineclass" style="padding-bottom:0px;">',
						'		<label class="field-label" style="vertical-align:baseline;">步骤3：</label>',
						'		<div class="control-group">',
						'				<span><i class="fa fa-info-circle"></i>整理数据并导入</span>',
						'		</div>',
						'	</div>',
						'	<div class="field-group" style="padding-top:0px;">',
						'		<label class="field-label"></label>',
						'		<div class="control-group">',
						'			<form name="importForm">',
						'				<input type="file" name="myfiles" accept="',instance.accept,'">',
						'				<input type="hidden" name="expenseMonth" id="expenseMonth"/>',
						'				<input type="hidden" name="bu" value="', bu, '"/>',
						'			</form>',
						'			<span style="width: 80px important; display: inline; color: white; float: left; margin: 12px 0px 0px 6px;"></span>',
						'		</div>',
						'	</div>'];
		instance._importBody = $(bch.join(''));
		var opts = {
				title: instance.title,
				width: 600,
//				height: 300,
				bodyContent: instance._importBody,
				buttons: [{
					text: '导入',
					elCls: 'button btn-create defineclassbtn',
					handler: function () {
						instance._clazz.importData(instance);
					}
				},
				{
					text: "取消",
					elCls: 'button btn-cancel',
					handler: function () {
						this.close();
					}
				}]
			};
		if(instance.dialogOpts){
			opts = $.extend(opts, instance.dialogOpts);
		}
		BUI.use('bui/overlay', function (Overlay) {
			dialog = new Overlay.Dialog(opts);
			dialog.render();
		});
		return dialog;
	},
	importData: function(instance){
		$("input[name=expenseMonth]").val($("input[name=date]").val())
		if($("input[name=expenseMonth]").val()== ""){
			common.alertMes("请选择月份", 'error');
			return;
		}
		if(instance._importBody.find("input[name=myfiles]")[0].value == ""){
			common.alertMes("请选择要上传的文件", 'error');
			return;
		}
		var formData = new FormData(instance._importBody.find('form')[0]);
		$.ajax({
	        url: common.ctx + instance.url,
	        type: 'POST',
	        data: formData,
	        async: true,
	        cache: false,
	        contentType: false,
	        processData: false,
	        beforeSend: function () {
	       	 	LoadMask.show();
	        },
	        success: function (returndata) {
	        	switch(returndata.code){
	            	case "success":
            			if(instance.success(returndata, instance._importDialog) === false){
            				return;
            			}
            			instance._importDialog.close();
        	        	LoadMask.hide();
            			if(returndata.successMsg){
	            			common.alertMes(returndata.successMsg, 'success');
	            		}else{
	            			common.alertMes("操作成功！", 'success');
	            		}
	            		break;
	            	case "warning":
	    	        	LoadMask.hide();
	            		common.alertMes(returndata.warning, 'warning');
	            		break;
	            	case "error":
	    	        	LoadMask.hide();
	                    common.alertMes(returndata.errorMsg, 'error');
	            		break;
	            	case "syserror":
	    	        	LoadMask.hide();
	            		common.alertMes(returndata.codeMsg, 'error');
	            		break;
	        	}
	        },
	        error: function (returndata) {
	        	LoadMask.hide();
	       	 	common.alertMes("访问接口socket连接超时", 'error');
	        }
	   });
	},
	defaults: {
		accept: '.xls,.xlsx',
		success: function(){}
	}
}
