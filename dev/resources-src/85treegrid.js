;(function($){
	var buildPartSheetByRow = function(sheet, startRow, endRow){
//		var newSheet = [];
//		for(var i = startRow; i < endRow; i++){
//			newSheet.push(sheet[i]);
//		}
		return sheet.slice(startRow, endRow);
	};
	UI.TreeGrid = UI.extend(UI.Grid, {
		idPrefix: 'ui_tree_grid',
		COLLAPASE_ITEM_CLS: 'ui-tree-grid-collapse-item',
		afterInit: function(grid){
			grid.element.addClass('ui-tree-grid');
			grid.titleColIndex = -1;
			for(var i = 0; i < grid.columns.length; i++){
				if(grid.columns[i].titleCol){
					grid.titleColIndex = i;
				}
			}
			if(grid.titleColIndex <0){
				grid.titleColIndex = 0;
			}
			var titleCol = grid.columns[0], formatter = titleCol.formatter, thiz = this;
			titleCol.formatter = function(value, record, index, rowId){
				var title = formatter.apply(this, arguments), row = grid.dataElMap[rowId];
				return thiz.buildTitleColumnTitle(title, row.level, row, grid);
			};
			if(grid.nodeLoadingImg){
				UI.preloadImg(grid.nodeLoadingImg);
			}
		},
		buildTitleColumnTitle: function(title, level, row, grid){
			if(!level){
				return title;
			}
			var children = row.record[grid.childrenProperty], statusFlag = 'collapse';
			if((row.record[grid.expandProperty] || !grid.asynLoadChildren) && (!children || children.length == 0)){
				statusFlag = 'leaf';
			}else if(row.record[grid.expandProperty]){
				statusFlag = 'expand';
				row.expand = true;
			}
			return this.buildTreeNodeTitle(title, level, statusFlag, row.id, grid);
		},
		buildTreeNodeTitle: function(title, level, statusFlag, rowId, grid){
			var h = ['<div class="ui-tree-grid-node ui-tree-grid-node-', statusFlag, '" nodestatus="', statusFlag, '"'];
			if(level > 1){
				h.push(' style="padding-left: ', (level - 1) * grid.indent, 'px;"');
			}
			h.push('>');
			h.push('<span class="ui-tree-grid-node-icon" onmouseover="var $el=$(this).parent();$el.addClass(\'ui-tree-grid-node-\' + $el.attr(\'nodestatus\') + \'-hover\');"',
					' onmouseout="var $el=$(this).parent();$el.removeClass(\'ui-tree-grid-node-\' + $el.attr(\'nodestatus\') + \'-hover\');""');
			if(statusFlag != 'leaf'){
				h.push(' onclick="var $el = $(this).parent(), grid = UI.TreeGrid.getInstance($el.parents(\'.', this.cls, 
						':first\'));if($el.attr(\'nodestatus\') == \'collapse\'){grid.expand(\'', rowId, 
						'\');}else{grid.collapse(\'', rowId, '\');}event.cancelBubble = true;event.stopPropagation();"');
			}
			h.push('></span>');
			h.push(title, '</div>');
			return h.join('');
		},
		buildDataSheet: function(records, grid, parentRow, level, dataSheet, collapse){
			dataSheet = dataSheet || [];
			level = level || 1;
			for(var i = 0; i < records.length; i++){
				var cls = grid.clazz.DATA_ROW_CLS + ' ' + grid.levelClsPrefix + level, rowHide = false;
				if(collapse){
					cls += (' ' + grid.clazz.COLLAPASE_ITEM_CLS);
					rowHide = true;
				}
				var record = records[i], clen = grid.columns.length, 
					row = {cls: cls, hide: rowHide, cells: [], id: grid.id + '_data' + grid.counter++, record: record, level: level}, 
					children = record[grid.childrenProperty], rowIndex = dataSheet.length;
				this.bindDataRowEvents(row, grid);
				dataSheet.push(row);
				if(parentRow){
					if(!parentRow.children){
						parentRow.children = [];
					}
					parentRow.children.push(row.id);
				}
				grid.dataElMap[row.id] = row;
				grid.dataRows.push(row);
				//树title列处理
				if((record[grid.expandProperty] || !grid.asynLoadChildren) && (!children || children.length == 0)){
					row.statusFlag = 'leaf';
				}else if(record[grid.expandProperty]){
					row.statusFlag = 'expand';
					row.expand = true;
				}
				for(var j = 0; j < clen; j++){
					var col = grid.columns[j], cell = row.cells[j] = $.extend({}, grid.clazz.cellDefaults, {
						rowId: row.id,
						colIndex: j,
						rowIndex: rowIndex
					});
					if(col.rowMergeable && ((record[grid.expandProperty] || !grid.asynLoadChildren) && (!children || children.length == 0))){
						//非叶子节点记录不可合并
						if(i > 0){
							//行可合并
							prerowCell = dataSheet[rowIndex - 1].cells[j];
							if(j > 0 && prerowCell.mergedBy && prerowCell.mergedBy == row.cells[j - 1].mergedBy){
								//单元格处于合并区内
								cell.mergedBy = prerowCell.mergedBy;
								continue;
							}else{
								if(col.valueProperty){
									cell.value = col.propertyProcessor(record, col.valueProperty);
								}else{
									cell.value = col.propertyProcessor(record, col.property);
								}
								if(prerowCell.getPhysicalCell().colIndex == j && prerowCell.getPhysicalCell().value == cell.value){
									//合并同一列
									cell.mergedBy = prerowCell.addRowSpan(1, row.id).getPhysicalCell();
									continue;
								}
							}
						}else{
							if(col.valueProperty){
								cell.value = col.propertyProcessor(record, col.valueProperty);
							}else{
								cell.value = col.propertyProcessor(record, col.property);
							}
						}
					}
					cell.title = col.formatter((!col.valueProperty && cell.value != undefined) ? 
							cell.value : col.propertyProcessor(record, col.property), record, i, row.id);
					if(col.colMergeable && j != 0 && j != grid.endLockIndex + 1){
						//列可合并
						if(cell.title == grid.colMergeableText && (dataSheet[rowIndex].cells[j - 1].mergedBy == null 
								|| dataSheet[rowIndex].cells[j - 1].mergedBy.rowIndex == rowIndex)){
							cell.mergedBy = dataSheet[rowIndex].cells[j - 1].addColSpan(1, j).getPhysicalCell();
							continue;
						}
					}
					//未合并
					cell.id = grid.id + '_cell' + grid.counter++;
					cell.cls = grid.clazz.DATA_CELL_CLS + ' ' + grid.clazz.alignCls[col.align];
					cell.tdCls = (col.tdCls ? col.tdCls + ' ' : '') + 'ui-grid-col' + j;
					if(col.dataTdClsGetter){
						var dataTdCls = col.dataTdClsGetter(record);
						if(dataTdCls){
							cell.tdCls += ' ' + dataTdCls;
						}
					}
					cell.valign = 'middle';
					grid.dataElMap[cell.id] = cell;
					if(j == grid.titleColIndex){
						row.nodeCellId = cell.id;
					}
				}
				if(children && children.length > 0){
					var startRow = dataSheet.length;
					this.buildDataSheet(children, grid, row, level + 1, dataSheet, collapse || !record[grid.expandProperty]);
					row.childrenSheet = buildPartSheetByRow(dataSheet, startRow, dataSheet.length);
				}
			}
			return dataSheet;
		},
		//绑定数据行事件
		bindDataRowEvents: function(row, grid){
			if(grid.dataRowEvents){
				row.events = {};
				for(var k in grid.dataRowEvents){
					row.events[k] = 'var grid = UI.TreeGrid.getInstance($(this).parents(\'.' 
						+ grid.clazz.cls + ':first\')); grid.dataRowEvents[\'' + k + '\'].call(grid, \'' + row.id + '\')';
				}
			}
		},
		base: {
			collapse: function(rowId, childrenCacadeUpdate){
				var row = this.dataElMap[rowId];
				if((!childrenCacadeUpdate && !row.expand) || (childrenCacadeUpdate && !row.expand)){
					//当操作触发节点的状态是已折叠的或子节点级联折叠，但是子节点是未展开的，不处理
					return;
				}
				if(!childrenCacadeUpdate){
					this.beforecollapse(row);
					row.expand = false;
					//设置操作触发节点状态
					$('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-expand')
						.removeClass('ui-tree-grid-node-expand-hover')
						.addClass('ui-tree-grid-node-collapse').attr('nodestatus', 'collapse');
					row.record[this.expandProperty] = false;
				}
				for(var i = 0, l = (row.childrenEndIndex ? row.childrenEndIndex : row.children.length); i < l; i++){
					//折叠子节点
					var childId = row.children[i];
					this.dataElMap[childId].hide = true;
					this.renderHandler.addRowClass(childId, this.clazz.COLLAPASE_ITEM_CLS);
					this.collapse(childId, true);
				}
				if(row.moreRowId){
					this.renderHandler.addRowClass(row.moreRowId, this.clazz.COLLAPASE_ITEM_CLS);
				}
				if(!childrenCacadeUpdate){
					this.trigger('resize');
					this.aftercollapse(row);
				}
			},
			expand: function(rowId, childrenCacadeUpdate){
				var row = this.dataElMap[rowId];
				if((!childrenCacadeUpdate && row.expand) || (childrenCacadeUpdate && !row.expand)){
					//当操作触发节点的状态是已展开的或子节点级联展开，但是子节点是未展开的，不处理
					return;
				}
				if(!childrenCacadeUpdate){
					this.beforeexpand(row);
					//设置操作触发节点状态
					row.expand = true;
					var $treeNode = $('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-collapse')
						.removeClass('ui-tree-grid-node-collapse-hover')
						.addClass('ui-tree-grid-node-expand').attr('nodestatus', 'expand');
					row.record[this.expandProperty] = true;
					if((!row.children || row.children.length == 0) && this.asynLoadChildren){
						//异步加载子节点
						if(this.nodeLoadingImg){
							$treeNode.addClass('ui-tree-grid-node-loading');
							if(!row.$loading){
								row.$loading = $('<img class="loading-img" src="' + this.nodeLoadingImg + '"/>').appendTo($treeNode.find('.ui-tree-grid-node-icon'));
							}
						}
						var children = this.loadChildren(row.record, rowId);
						if(children){
							this.appendChildren(rowId, children);
						}else if(children !== false && this.nodeLoadingImg){
							$treeNode.removeClass('ui-tree-grid-node-loading');
						}
						return;
					}
				}
				for(var i = 0, l = (row.childrenEndIndex ? row.childrenEndIndex : row.children.length); i < l; i++){
					//展开子节点
					var childId = row.children[i];
					this.dataElMap[childId].hide = false;
					this.renderHandler.removeRowClass(childId, this.clazz.COLLAPASE_ITEM_CLS);
					this.expand(childId, true);
				}
				if(row.moreRowId){
					this.renderHandler.removeRowClass(row.moreRowId, this.clazz.COLLAPASE_ITEM_CLS);
				}
				if(!childrenCacadeUpdate){
					this.trigger('resize');
					this.afterexpand(row);
				}
			},
			appendChildren: function(rowId, children){
				var row = this.dataElMap[rowId], 
					dataSheet = this.clazz.buildDataSheet(children, this, row, row.level + 1, dataSheet, false);
				if(dataSheet.length == 0){
					if(this.nodeLoadingImg){
						$('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-loading');
					}
					return;
				}
				row.childrenSheet = dataSheet;
				if(this.maxAppendChildren > 0 && dataSheet.length > this.maxAppendChildren){
					row.childrenEndIndex = this.maxAppendChildren;
					row.lastChildRow = dataSheet[this.maxAppendChildren - 1];
					row.moreRowId = grid.id + '_more' + grid.counter++;
					this.renderHandler.insertRowAfter(row, buildPartSheetByRow(dataSheet, 0, this.maxAppendChildren), this);
					this.renderHandler.insertBlankRowAfter(row.lastChildRow, row.moreRowId,
							this.clazz.buildTreeNodeTitle(this.buildMoreNodesTitle(row), row.level + 1, 'leaf', row.moreRowId, grid), this);
					for(var i = row.childrenEndIndex, l = dataSheet.length; i < l; i++){
						dataSheet[i].notAvailable = true;
					}
				}else{
					this.renderHandler.insertRowAfter(row, dataSheet, this);
				}
				row.record[this.childrenProperty] = children;
				this.trigger('resize');
				this.afterexpand(row);
				if(this.nodeLoadingImg){
					$('#' + row.nodeCellId + ' .ui-tree-grid-node').removeClass('ui-tree-grid-node-loading');
				}
			},
			insertChildrenAfter: function(parentRowId, index, children){
				var parentRow = this.dataElMap[parentRowId], beforeRow = null, 
				oldChildren = parentRow.record[this.childrenProperty] = (parentRow.record[this.childrenProperty] || []), 
				dataSheet = this.clazz.buildDataSheet(children, this, parentRow, parentRow.level + 1, dataSheet, false);
				parentRow.childrenSheet = parentRow.childrenSheet || [];
				//初始化前一行和新children和datasheet数据
				if(index < 0){
					beforeRow = parentRow;
					index = 0;
				}else{
					beforeRow = parentRow.childrenSheet[index];
					index++;
				}
				//插入新child数据
				for(var i = 0, l = children.length; i < l; i++){
					var r = dataSheet[i];
					r.notAvailable = beforeRow.notAvailable;
					oldChildren.splice(index, 0, children[i]);
					parentRow.childrenSheet.splice(index++, 0, r);
				}
				//如果前一行不在更多行展示，那么新插入行展示
				if(!beforeRow.notAvailable){
					this.renderHandler.insertRowAfter(beforeRow, dataSheet, this);
					if(parentRow.moreRowId){
						parentRow.childrenEndIndex += children.length;
					}
				}
				this.trigger('resize');
			},
			moreNodes: function(parentRowId){
				var parentRow = this.dataElMap[parentRowId], csl = parentRow.childrenSheet.length;
				if(parentRow.childrenEndIndex >= csl){
					//已经是最后数据
					return;
				}
				this.beforemorenodes(parentRow);
				var startIndex = parentRow.childrenEndIndex;
				parentRow.childrenEndIndex += this.maxAppendChildren;
				if(parentRow.childrenEndIndex > csl){
					parentRow.childrenEndIndex = csl;
					this.renderHandler.removeRow(parentRow.moreRowId, this);
					parentRow.moreRowId = null;
				}
				for(var i = startIndex; i < parentRow.childrenEndIndex; i++){
					parentRow.childrenSheet[i].notAvailable = false;
				}
				this.renderHandler.insertRowAfter(parentRow.lastChildRow, buildPartSheetByRow(parentRow.childrenSheet, startIndex, parentRow.childrenEndIndex), this);
				parentRow.lastChildRow = parentRow.childrenSheet[parentRow.childrenEndIndex  - 1];
				this.trigger('resize');
				this.aftermorenodes(parentRow);
			}
		},
		columnDefaults: {
			title: '', //列表头
			property: '', //列显示对应属性
			valueProperty: '', //列值对应属性(rowMergeable为true时有效)，默认为列显示对应属性
			endLock: false, //是否最后的锁定列(叶子列的最后一列为true列有效)
			formatter: function(value, record, index, rowId){return value ? value : (value === 0 ? '0' : '');}, //列显示文本格式化
			'export': true, //是否需要导出
			tdCls: '', //列td样式
			rowMergeable: false, //行是否可合并的。true-合并同一列相邻行valuePorperty相等的单元格。默认为false。
			colMergeable: false, //列是否可合并。true-同一行，合并显示文本为表格对象的colMergeableText值的单元格与同一行前一个单元格。默认为false
			align: 'left', //显示文本水平对齐方式
			nowrap: false, //强制不换行
			width: 20, //列宽(如果有列锁定且所有列的宽度总和大于总宽度时为列实际宽度，否则代表列宽占的比例)
			minWidth: 20, //最小列宽(固定宽度列或锁行时无效)
			fixedWidth: false, //列固定宽度,
			children: null, //子表头
			sortable: true, //列是否排序
			summary: false, //合计列。为true时，property属性不能为空
			propertyProcessor: function(record, propertyName){return record[propertyName];}, //属性值处理器
			titleCol: false //树标题列，第一个标题列有效。默认第一列
		},
		defaults: {
			headers: null, //列定义数组
			store: null, //数据仓库实例
			autoLoad: true, //自动加载数据
			indent: 20, //第一列标题缩进
			sumRender: 'footer', //合计行渲染位置。header-表头，footer-表尾
			summaryTitle: '总合计', //合计标题
			summary: false, //汇总总开关
			colMergeableText: '<merge/>', //列可合并特殊显示文本
			noDataMsg: '无数据', //没有数据显示文本
			cellPaddingX: 1.5, //单元格X轴方向间距
			cellPaddingY: 1.5, //单元格Y轴方向间距
			xOverflow: 'auto', //X轴滚动。scroll, auto
			yOverflow: 'scroll', //Y轴滚动。scroll, auto
			childrenProperty: 'children', //下级数据属性
			expandProperty: 'expand', //记录展开下级数据属性
			levelClsPrefix: 'ui-tree-grid-record-level', //记录层级样式前缀
			asynLoadChildren: false, //是否异步加载数据
			loadChildren: function(record, rowId){}, //异步加载
			nodeLoadingImg: null, //节点加载图片
			beforecollapse: function(row){}, //折叠前操作
			aftercollapse: function(row){}, //折叠后操作
			beforeexpand: function(row){}, //展开前操作
			afterexpand: function(row){}, //展开后操作
			maxAppendChildren: -1, //最大附加子节点数
			buildMoreNodesTitle: function(parentRow){
				return '<a href="javascript: void(0);" onclick="var $el = $(this).parent(), grid = UI.TreeGrid.getInstance($el.parents(\'.' 
				+ this.clazz.cls + ':first\')); grid.moreNodes(\'' + parentRow.id + '\');">更多......</a>';
			},
			beforemorenodes: function(parentRow){},
			aftermorenodes: function(parentRow){},
			drill: function(record){} //下钻
		}
	});
})(jQuery);