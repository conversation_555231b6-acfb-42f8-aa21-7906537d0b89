package com.chevron.elitefundv2.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chevron.elitefundv2.dao.V2EliteFundFormMapper;
import com.chevron.elitefundv2.model.V2EliteFundForm;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.SpringUtils;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.PartnerView;

/**
 * 基金类型
 * <AUTHOR>
 *
 */
public enum FundType {
	CONSUMER_MKT_FUND("consumer_mkt_fund", "乘用车市场营销基金", 1, 1, 1, true) {
		@Override
		public String getOver25PErrorMessage() {
			return "运营费用（含购置车辆，办公设备，库存租赁，其他雪佛龙支持的运营）的上限为经销商累计的“营销基金+IVI+全年超额奖励”总额的25%";
		}
	},
	COMMERCIAL_MKT_FUND("commerical_mkt_fund", "商用油市场营销基金", 10, 2, 1, true) {
		@Override
		public String getOver25PErrorMessage() {
			return "运营费用（含购置车辆，办公设备，库存租赁，其他雪佛龙支持的运营）的上限为经销商累计的“营销基金+IVI”总额的25%";
		}
	},
	COMMERCIAL_SPARK("commerical_spark", "商用油星火计划", 10, 4, 0, false) {
		@Override
		protected void processBy25P(V2EliteFundForm form) throws WxPltException {
		}

		@Override
		public String getOver25PErrorMessage() {
			return null;
		}
	};
	
	private String typeCode;
	
	private String text;
	
	private int channelWeight;
	
	private int fundWeight;
	
	private int funFlag;
	
	private boolean enable;
	
	public final int FUN_FLAG_25P = 1;
	
	private static Map<String, FundType> fundTypeMap = new HashMap<String, FundType>(values().length);
	
	static {
		for(FundType type : values()) {
			fundTypeMap.put(type.getTypeCode(), type);
		}
	}

	
	/**
	 * @param typeCode
	 * @param text
	 * @param channelWeight
	 * @param fundWeight
	 * @param funFlag
	 */
	private FundType(String typeCode, String text, int channelWeight, int fundWeight, int funFlag, boolean enable) {
		this.typeCode = typeCode;
		this.text = text;
		this.channelWeight = channelWeight;
		this.fundWeight = fundWeight;
		this.funFlag = funFlag;
		this.enable = enable;
	}

	public static FundType getFundTypeByCode(String typeCode) {
		return fundTypeMap.get(typeCode);
	}
	
	public abstract String getOver25PErrorMessage();
	
	protected void processBy25P(V2EliteFundForm form) throws WxPltException {
		Map<String, Object> params = new HashMap<String, Object>(3);
		params.put("fundType", getTypeCode());
		List<String> projects = new ArrayList<String>();
		for(ApplyProject project : ApplyProject.values()) {
			if((project.getFunFlag() & getFundWeight()) > 0) {
				projects.add(project.getProjectCode());
			}
		}
		params.put("projects", projects);
		params.put("applyAmount", form.getApplyAmount());
		params.put("distributorId", form.getDistributorId());
		params.put("applyYear", Integer.parseInt((String)Constants.getSystemPropertyByCodeType("EliteFund.applyYear")));
		int result = SpringUtils.getBean(V2EliteFundFormMapper.class).validateForm(params);
		if((result & 1) > 0) {
			StringBuilder sBuilder = null;
			for(ApplyProject project : ApplyProject.values()) {
				if((project.getFunFlag() & getFundWeight()) > 0) {
					if(sBuilder == null) {
						sBuilder = new StringBuilder();
					}else{
						sBuilder.append(",");
					}
					sBuilder.append(project.getText());
				}
			}

			throw new WxPltException(getOver25PErrorMessage());
		}
	}
	
	public void processBeforeSubmit(V2EliteFundForm form) throws WxPltException {
		Map<String, Object> paramsMap = new HashMap<String, Object>(3);
		paramsMap.put("distributorId", form.getDistributorId());
		List<PartnerView> list = SpringUtils.getBean(OrganizationVoMapper.class).selectPartnersByParams(paramsMap);
		if(list.size() > 1) {
			throw new WxPltException("找到多个匹配经销商" + form.getDistributorName() + 
					"(" + form.getDistributorId() + ")");
		}
		PartnerView partnerView = list.get(0);
		form.setDistributorName(partnerView.getName());
		form.setChannelWeight(partnerView.getChannelWeight() & form.getChannelWeight());
		if((funFlag & 1) > 0) {
			ApplyProject project = ApplyProject.getApplyProjectByCode(form.getApplyProject());
			if((project.getFunFlag() & getFundWeight()) > 0) {
				processBy25P(form);
			}
		}
	}

	public String getTypeCode() {
		return typeCode;
	}

	public String getText() {
		return text;
	}

	public int getChannelWeight() {
		return channelWeight;
	}

	public int getFundWeight() {
		return fundWeight;
	}

	public boolean isEnable() {
		return enable;
	}
	
}
