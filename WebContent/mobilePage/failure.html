<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,height=device-height, initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<meta name="format-detection" content="telephone=yes"/>
		<title>查询有误</title>
	</head>
	<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
	<script src="js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
	<script src="js/jquery-1.12.2.min.js" type="text/javascript" charset="utf-8"></script>
	<script src="js/cuowu.js" type="text/javascript" charset="utf-8"></script>
	<body>
		<div class="neirong">
			<div class="swiper-container">
				<div class="swiper-wrapper">
					<div class="swiper-slide"><img src="img/banner1.jpg"></div>
					<div class="swiper-slide"><img src="img/swiper2.jpg"></div>
					<div class="swiper-slide"><img src="img/swiper2.png"></div>
				</div>
				<div class="swiper-pagination"></div>
			</div>
			<div id="mid">
			<div class="jieguo">
				<div class="jieguo-l">
					<p>
						<h2>查询结果</h2>
					</p>
					<p class="info1"><label id="errorMsg"></label></p>

					<p class="cw-info2">更多详情</p>
					<p>请致电 <b><a href="tel:************">************</a></b></p>
				</div>
				<div class="jieguo-r">
					<p>
						<h2>数码有误</h2>
					</p>
					<img src="img/cuo.png">
				</div>
				
				</div>

				<div class="btn">
<div><a href="#"><img src="img/logo.jpg"/></a></div>
				
			</div>

			</div>
			
			</div>
			
		</div>
	</body>
	<link rel="stylesheet" type="text/css" href="css/index.css" />
	<style>
	body{background-color:#FFF;}
	#mid{justify-content:flex-start;}
	.btn{
	justify-content:center;
	}
	.btn img{
		width:121px;
		text-align:center;
	}
	.btn div img{
		width:121px;
	}
	.jieguo {
		margin:20px 5px 30px 5px;
	}
	</style>
	<script type="text/javascript">
		var mySwiper = new Swiper('.swiper-container', {
			autoplay: true, //可选选项，自动滑动
			pagination: {
				el: '.swiper-pagination',
			},
		});
		(function($){
		    $.getUrlParam = function(name)
		    {
		        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		        var r = window.location.search.substr(1).match(reg);
		        if (r!=null) return unescape(r[2]); return null;
		    }
		})(jQuery);
		$(document).ready(function () {
			var errorMsg = $.getUrlParam('errorMsg');
			errorMsg = decodeURIComponent(errorMsg);
			$("#errorMsg").html(errorMsg);
		});
	</script>
</html>
