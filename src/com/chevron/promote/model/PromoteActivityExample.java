package com.chevron.promote.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PromoteActivityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PromoteActivityExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdIsNull() {
            addCriterion("apply_batch_id is null");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdIsNotNull() {
            addCriterion("apply_batch_id is not null");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdEqualTo(Integer value) {
            addCriterion("apply_batch_id =", value, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdNotEqualTo(Integer value) {
            addCriterion("apply_batch_id <>", value, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdGreaterThan(Integer value) {
            addCriterion("apply_batch_id >", value, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_batch_id >=", value, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdLessThan(Integer value) {
            addCriterion("apply_batch_id <", value, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdLessThanOrEqualTo(Integer value) {
            addCriterion("apply_batch_id <=", value, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdIn(List<Integer> values) {
            addCriterion("apply_batch_id in", values, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdNotIn(List<Integer> values) {
            addCriterion("apply_batch_id not in", values, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdBetween(Integer value1, Integer value2) {
            addCriterion("apply_batch_id between", value1, value2, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andApplyBatchIdNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_batch_id not between", value1, value2, "applyBatchId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdIsNull() {
            addCriterion("source_plan_id is null");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdIsNotNull() {
            addCriterion("source_plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdEqualTo(Long value) {
            addCriterion("source_plan_id =", value, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdNotEqualTo(Long value) {
            addCriterion("source_plan_id <>", value, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdGreaterThan(Long value) {
            addCriterion("source_plan_id >", value, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("source_plan_id >=", value, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdLessThan(Long value) {
            addCriterion("source_plan_id <", value, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdLessThanOrEqualTo(Long value) {
            addCriterion("source_plan_id <=", value, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdIn(List<Long> values) {
            addCriterion("source_plan_id in", values, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdNotIn(List<Long> values) {
            addCriterion("source_plan_id not in", values, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdBetween(Long value1, Long value2) {
            addCriterion("source_plan_id between", value1, value2, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourcePlanIdNotBetween(Long value1, Long value2) {
            addCriterion("source_plan_id not between", value1, value2, "sourcePlanId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdIsNull() {
            addCriterion("source_distribution_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdIsNotNull() {
            addCriterion("source_distribution_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdEqualTo(Long value) {
            addCriterion("source_distribution_id =", value, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdNotEqualTo(Long value) {
            addCriterion("source_distribution_id <>", value, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdGreaterThan(Long value) {
            addCriterion("source_distribution_id >", value, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("source_distribution_id >=", value, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdLessThan(Long value) {
            addCriterion("source_distribution_id <", value, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdLessThanOrEqualTo(Long value) {
            addCriterion("source_distribution_id <=", value, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdIn(List<Long> values) {
            addCriterion("source_distribution_id in", values, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdNotIn(List<Long> values) {
            addCriterion("source_distribution_id not in", values, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdBetween(Long value1, Long value2) {
            addCriterion("source_distribution_id between", value1, value2, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andSourceDistributionIdNotBetween(Long value1, Long value2) {
            addCriterion("source_distribution_id not between", value1, value2, "sourceDistributionId");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectIsNull() {
            addCriterion("activity_subject is null");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectIsNotNull() {
            addCriterion("activity_subject is not null");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectEqualTo(String value) {
            addCriterion("activity_subject =", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectNotEqualTo(String value) {
            addCriterion("activity_subject <>", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectGreaterThan(String value) {
            addCriterion("activity_subject >", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectGreaterThanOrEqualTo(String value) {
            addCriterion("activity_subject >=", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectLessThan(String value) {
            addCriterion("activity_subject <", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectLessThanOrEqualTo(String value) {
            addCriterion("activity_subject <=", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectLike(String value) {
            addCriterion("activity_subject like", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectNotLike(String value) {
            addCriterion("activity_subject not like", value, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectIn(List<String> values) {
            addCriterion("activity_subject in", values, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectNotIn(List<String> values) {
            addCriterion("activity_subject not in", values, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectBetween(String value1, String value2) {
            addCriterion("activity_subject between", value1, value2, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivitySubjectNotBetween(String value1, String value2) {
            addCriterion("activity_subject not between", value1, value2, "activitySubject");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeIsNull() {
            addCriterion("activity_start_time is null");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeIsNotNull() {
            addCriterion("activity_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeEqualTo(Date value) {
            addCriterion("activity_start_time =", value, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeNotEqualTo(Date value) {
            addCriterion("activity_start_time <>", value, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeGreaterThan(Date value) {
            addCriterion("activity_start_time >", value, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("activity_start_time >=", value, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLessThan(Date value) {
            addCriterion("activity_start_time <", value, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("activity_start_time <=", value, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeIn(List<Date> values) {
            addCriterion("activity_start_time in", values, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeNotIn(List<Date> values) {
            addCriterion("activity_start_time not in", values, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeBetween(Date value1, Date value2) {
            addCriterion("activity_start_time between", value1, value2, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("activity_start_time not between", value1, value2, "activityStartTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeIsNull() {
            addCriterion("activity_end_time is null");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeIsNotNull() {
            addCriterion("activity_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeEqualTo(Date value) {
            addCriterion("activity_end_time =", value, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeNotEqualTo(Date value) {
            addCriterion("activity_end_time <>", value, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeGreaterThan(Date value) {
            addCriterion("activity_end_time >", value, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("activity_end_time >=", value, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLessThan(Date value) {
            addCriterion("activity_end_time <", value, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("activity_end_time <=", value, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeIn(List<Date> values) {
            addCriterion("activity_end_time in", values, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeNotIn(List<Date> values) {
            addCriterion("activity_end_time not in", values, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeBetween(Date value1, Date value2) {
            addCriterion("activity_end_time between", value1, value2, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("activity_end_time not between", value1, value2, "activityEndTime");
            return (Criteria) this;
        }

        public Criteria andActivityAddressIsNull() {
            addCriterion("activity_address is null");
            return (Criteria) this;
        }

        public Criteria andActivityAddressIsNotNull() {
            addCriterion("activity_address is not null");
            return (Criteria) this;
        }

        public Criteria andActivityAddressEqualTo(String value) {
            addCriterion("activity_address =", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressNotEqualTo(String value) {
            addCriterion("activity_address <>", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressGreaterThan(String value) {
            addCriterion("activity_address >", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressGreaterThanOrEqualTo(String value) {
            addCriterion("activity_address >=", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressLessThan(String value) {
            addCriterion("activity_address <", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressLessThanOrEqualTo(String value) {
            addCriterion("activity_address <=", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressLike(String value) {
            addCriterion("activity_address like", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressNotLike(String value) {
            addCriterion("activity_address not like", value, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressIn(List<String> values) {
            addCriterion("activity_address in", values, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressNotIn(List<String> values) {
            addCriterion("activity_address not in", values, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressBetween(String value1, String value2) {
            addCriterion("activity_address between", value1, value2, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityAddressNotBetween(String value1, String value2) {
            addCriterion("activity_address not between", value1, value2, "activityAddress");
            return (Criteria) this;
        }

        public Criteria andActivityTypeIsNull() {
            addCriterion("activity_type is null");
            return (Criteria) this;
        }

        public Criteria andActivityTypeIsNotNull() {
            addCriterion("activity_type is not null");
            return (Criteria) this;
        }

        public Criteria andActivityTypeEqualTo(String value) {
            addCriterion("activity_type =", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotEqualTo(String value) {
            addCriterion("activity_type <>", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeGreaterThan(String value) {
            addCriterion("activity_type >", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_type >=", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeLessThan(String value) {
            addCriterion("activity_type <", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeLessThanOrEqualTo(String value) {
            addCriterion("activity_type <=", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeLike(String value) {
            addCriterion("activity_type like", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotLike(String value) {
            addCriterion("activity_type not like", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeIn(List<String> values) {
            addCriterion("activity_type in", values, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotIn(List<String> values) {
            addCriterion("activity_type not in", values, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeBetween(String value1, String value2) {
            addCriterion("activity_type between", value1, value2, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotBetween(String value1, String value2) {
            addCriterion("activity_type not between", value1, value2, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIsNull() {
            addCriterion("activity_organizers is null");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIsNotNull() {
            addCriterion("activity_organizers is not null");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersEqualTo(String value) {
            addCriterion("activity_organizers =", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersNotEqualTo(String value) {
            addCriterion("activity_organizers <>", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersGreaterThan(String value) {
            addCriterion("activity_organizers >", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersGreaterThanOrEqualTo(String value) {
            addCriterion("activity_organizers >=", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersLessThan(String value) {
            addCriterion("activity_organizers <", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersLessThanOrEqualTo(String value) {
            addCriterion("activity_organizers <=", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersLike(String value) {
            addCriterion("activity_organizers like", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersNotLike(String value) {
            addCriterion("activity_organizers not like", value, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIn(List<String> values) {
            addCriterion("activity_organizers in", values, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersNotIn(List<String> values) {
            addCriterion("activity_organizers not in", values, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersBetween(String value1, String value2) {
            addCriterion("activity_organizers between", value1, value2, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersNotBetween(String value1, String value2) {
            addCriterion("activity_organizers not between", value1, value2, "activityOrganizers");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdIsNull() {
            addCriterion("activity_organizers_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdIsNotNull() {
            addCriterion("activity_organizers_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdEqualTo(Long value) {
            addCriterion("activity_organizers_id =", value, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdNotEqualTo(Long value) {
            addCriterion("activity_organizers_id <>", value, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdGreaterThan(Long value) {
            addCriterion("activity_organizers_id >", value, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdGreaterThanOrEqualTo(Long value) {
            addCriterion("activity_organizers_id >=", value, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdLessThan(Long value) {
            addCriterion("activity_organizers_id <", value, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdLessThanOrEqualTo(Long value) {
            addCriterion("activity_organizers_id <=", value, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdIn(List<Long> values) {
            addCriterion("activity_organizers_id in", values, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdNotIn(List<Long> values) {
            addCriterion("activity_organizers_id not in", values, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdBetween(Long value1, Long value2) {
            addCriterion("activity_organizers_id between", value1, value2, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityOrganizersIdNotBetween(Long value1, Long value2) {
            addCriterion("activity_organizers_id not between", value1, value2, "activityOrganizersId");
            return (Criteria) this;
        }

        public Criteria andActivityAmountIsNull() {
            addCriterion("activity_amount is null");
            return (Criteria) this;
        }

        public Criteria andActivityAmountIsNotNull() {
            addCriterion("activity_amount is not null");
            return (Criteria) this;
        }

        public Criteria andActivityAmountEqualTo(BigDecimal value) {
            addCriterion("activity_amount =", value, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountNotEqualTo(BigDecimal value) {
            addCriterion("activity_amount <>", value, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountGreaterThan(BigDecimal value) {
            addCriterion("activity_amount >", value, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("activity_amount >=", value, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountLessThan(BigDecimal value) {
            addCriterion("activity_amount <", value, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("activity_amount <=", value, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountIn(List<BigDecimal> values) {
            addCriterion("activity_amount in", values, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountNotIn(List<BigDecimal> values) {
            addCriterion("activity_amount not in", values, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("activity_amount between", value1, value2, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andActivityAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("activity_amount not between", value1, value2, "activityAmount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}