<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.b2b.dao.ScheduleInstanceMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.b2b.model.ScheduleInstance">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="schedule_id" property="scheduleId" jdbcType="BIGINT"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="workshop_id" property="workshopId" jdbcType="BIGINT"/>
		<result column="executor_type" property="executorType" jdbcType="VARCHAR"/>
		<result column="executor_key" property="executorKey" jdbcType="VARCHAR"/>
		<result column="executor_name" property="executorName" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="status_text" property="statusText" jdbcType="VARCHAR"/>
		<result column="submit_time" property="submitTime" jdbcType="TIMESTAMP"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		
		<result column="schedule_name" property="scheduleName" jdbcType="VARCHAR"/>
		<result column="icon_id" property="iconId" jdbcType="BIGINT"/>
		<result column="banner_id" property="bannerId" jdbcType="BIGINT"/>
		<result column="schedule_type" property="scheduleType" jdbcType="VARCHAR"/>
		<result column="award_point" property="awardPoint" jdbcType="NUMERIC"/>
		<result column="start_time" property="startTime" jdbcType="DATE"/>
		<result column="end_time" property="endTime" jdbcType="DATE"/>
		<result column="schedule_desc" property="scheduleDesc" jdbcType="VARCHAR"/>
		<result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
		<result column="left_steps" property="leftSteps" jdbcType="INTEGER"/>
		<result column="schedule_type_text" property="scheduleTypeText" jdbcType="VARCHAR"/>
		<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
		<result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
		<result column="workshop_name" property="workshopName" jdbcType="VARCHAR"/>
		<result column="share_flag" property="shareFlag" jdbcType="INTEGER"/>
		<result column="fixed_point_flag" property="fixedPointFlag" jdbcType="INTEGER"/>
		<result column="max_point" property="maxPoint" jdbcType="NUMERIC"/>
		<result column="agree_count" property="agreeCount" jdbcType="BIGINT"/>
		<result column="client_agree_flag" property="clientAgreeFlag" jdbcType="INTEGER"/>
		<result column="form_bg" property="formBg" jdbcType="BIGINT"/>
		<result column="action_bg" property="actionBg" jdbcType="BIGINT"/>
		<result column="agree_numb" property="agreeNumb" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,schedule_id,distributor_id,workshop_id,executor_type,executor_key,executor_name,status,submit_time,delete_flag,
		create_user_id,create_time,update_user_id,update_time,remark
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.b2b.model.ScheduleInstance">
		update wx_t_schedule_instance
		<set>
			<if test="scheduleId != null" >
				schedule_id = #{scheduleId,jdbcType=BIGINT},
			</if>
			<if test="distributorId != null" >
				distributor_id = #{distributorId,jdbcType=BIGINT},
			</if>
			<if test="workshopId != null" >
				workshop_id = #{workshopId,jdbcType=BIGINT},
			</if>
			<if test="executorType != null" >
				executor_type = #{executorType,jdbcType=VARCHAR},
			</if>
			<if test="executorKey != null" >
				executor_key = #{executorKey,jdbcType=VARCHAR},
			</if>
			<if test="executorName != null" >
				executor_name = #{executorName,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="submitTime != null" >
				submit_time = #{submitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<choose>
				<when test="remark == '-1'">
				remark = null,
				</when>
				<when test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
				</when>
			</choose>
		</set>
		where id = #{id,jdbcType=BIGINT} and status&lt;100
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.b2b.model.ScheduleInstanceExample">
    	delete from wx_t_schedule_instance
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleInstanceExample">
    	select
		t1.id, t1.schedule_id, t1.distributor_id, t1.workshop_id, t1.executor_type, t1.executor_key, t1.executor_name,
			 t1.status, t1.submit_time, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.remark,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ScheduleInstance.status' and di1.dic_item_code=t1.status) status_text,
		s.schedule_name, s.icon_id, s.banner_id, s.schedule_type, s.award_point, s.start_time, s.end_time, s.publish_time, s.schedule_desc, 
		s.share_flag,s.fixed_point_flag,s.max_point
,(select count(1) from wx_t_activity_agree aa where aa.activity_id=t1.id) agree_numb
		  from wx_t_schedule_instance t1
		  join wx_t_schedule_entity se on t1.schedule_id=se.id
		  join wx_t_schedule s on s.id=se.schedule_id
		where t1.id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.b2b.model.ScheduleInstance" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_schedule_instance
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="scheduleId != null">
				schedule_id,
			</if>
			<if test="distributorId != null">
				distributor_id,
			</if>
			<if test="workshopId != null">
				workshop_id,
			</if>
			<if test="executorType != null">
				executor_type,
			</if>
			<if test="executorKey != null">
				executor_key,
			</if>
			<if test="executorName != null">
				executor_name,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="submitTime != null">
				submit_time,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="remark != null">
				remark,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="scheduleId != null">
				#{scheduleId,jdbcType=BIGINT},
			</if>
			<if test="distributorId != null">
				#{distributorId,jdbcType=BIGINT},
			</if>
			<if test="workshopId != null">
				#{workshopId,jdbcType=BIGINT},
			</if>
			<if test="executorType != null">
				#{executorType,jdbcType=VARCHAR},
			</if>
			<if test="executorKey != null">
				#{executorKey,jdbcType=VARCHAR},
			</if>
			<if test="executorName != null">
				#{executorName,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="submitTime != null">
				#{submitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_schedule_instance
		<set>
			<if test="record.scheduleId != null">
				schedule_id = #{record.scheduleId,jdbcType=BIGINT},
			</if>
			<if test="record.distributorId != null">
				distributor_id = #{record.distributorId,jdbcType=BIGINT},
			</if>
			<if test="record.workshopId != null">
				workshop_id = #{record.workshopId,jdbcType=BIGINT},
			</if>
			<if test="record.executorType != null">
				executor_type = #{record.executorType,jdbcType=VARCHAR},
			</if>
			<if test="record.executorKey != null">
				executor_key = #{record.executorKey,jdbcType=VARCHAR},
			</if>
			<if test="record.executorName != null">
				executor_name = #{record.executorName,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.submitTime != null">
				submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.b2b.model.ScheduleInstanceExample">
		delete from wx_t_schedule_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.b2b.model.ScheduleInstanceExample">
		select count(1) from wx_t_schedule_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleInstanceExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_schedule_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.schedule_id, t1.distributor_id, t1.workshop_id, t1.executor_type, t1.executor_key, t1.executor_name,
			 t1.status, t1.submit_time, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,
<!-- 		 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ScheduleInstance.status' and di1.dic_item_code=t1.status) status_text, -->	
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Schedule.scheduleType' and di1.dic_item_code=s.schedule_type) schedule_type_text,
			 s.award_point, s.left_steps, s.schedule_type, s.sales_channel, s.share_flag,s.fixed_point_flag,s.max_point
			 <if test="includeAgreeNo">
			 ,(select count(1) from wx_t_activity_agree aa where aa.activity_id=t1.id) agree_numb
			 </if>
		  from wx_t_schedule_instance t1
		  join wx_t_schedule_entity se on se.id=t1.schedule_id
		  left join wx_t_schedule s on s.id=se.schedule_id
		 where 1=1
		<choose>
			<when test="id != null">
			and t1.id = #{id, jdbcType=BIGINT}
			</when>
			<otherwise>
				<if test="distributorId != null">
					and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
				</if>
				<if test="workshopId != null">
					and t1.workshop_id = #{workshopId, jdbcType=BIGINT}
				</if>
				<if test="executorName != null and executorName != ''">
					and t1.executor_name like '%' + #{executorName, jdbcType=VARCHAR} + '%'
				</if>
				<if test="status != null">
					and t1.status = #{status, jdbcType=INTEGER}
				</if>
				<if test="dateFrom != null">
					and t1.submit_time &gt;= #{dateFrom, jdbcType=DATE}
				</if>
				<if test="dateTo != null">
					and t1.submit_time &lt; #{dateTo, jdbcType=DATE}
				</if>
			</otherwise>
		</choose>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleInstanceParams">
		select t1.id, t1.schedule_id, t1.distributor_id, t1.workshop_id, t1.executor_type, t1.executor_key, t1.executor_name,
			 t1.status, t1.submit_time,  t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ScheduleInstance.status' and di1.dic_item_code=t1.status) status_text,
			 s.schedule_name, s.icon_id, s.banner_id, s.schedule_type, s.award_point, s.start_time, s.end_time, se.create_time publish_time, s.left_steps, 
			 s.share_flag,s.fixed_point_flag,s.max_point,
			 o.organization_name distributor_name, w.work_shop_name workshop_name, t1.remark
			 <if test="includeAgreeNumb">
			 ,(select count(1) from wx_t_activity_agree aa where aa.activity_id=t1.id) agree_numb
			 </if>
		  from wx_t_schedule_instance t1
		  join wx_t_schedule_entity se on se.id=t1.schedule_id
		  join wx_t_schedule s on s.id=se.schedule_id
		  left join wx_t_organization o on o.id=t1.distributor_id
		  left join wx_t_work_shop w on w.id=t1.workshop_id
		 where t1.delete_flag=0 and (t1.status>=15 or (s.delete_flag=0 and DATEADD(day, 1, se.end_date)>getdate()))
		<if test="distributorId != null">
			and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
		</if>
		<if test="workshopId != null">
			and t1.workshop_id = #{workshopId, jdbcType=BIGINT}
		</if>
		<if test="scheduleId != null">
			and se.schedule_id = #{scheduleId, jdbcType=BIGINT}
		</if>
		<if test="executorName != null and executorName != ''">
			and t1.executor_name like '%' + #{executorName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="userId != null and userId != ''">
			and t1.executor_type='mechanic' and t1.executor_key = #{userId, jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and t1.status = #{status, jdbcType=INTEGER}
		</if>
		<if test="statusArray != null">
			and t1.status in 
	        <foreach collection="statusArray" item="listItem" open="(" close=")" separator="," >
	          ${listItem}
	        </foreach>
		</if>
		<if test="startStatus != null">
			and t1.status >= #{startStatus, jdbcType=INTEGER}
		</if>
		<if test="endStatus != null">
			and t1.status &lt;= #{endStatus, jdbcType=INTEGER}
		</if>
		<if test="salesChannel != null and salesChannel != ''">
			and s.sales_channel = #{salesChannel, jdbcType=INTEGER}
		</if>
		<if test="scheduleType != null and scheduleType != ''">
			and s.schedule_type = #{scheduleType, jdbcType=INTEGER}
		</if>
		<if test="submitDateFrom != null">
			and t1.submit_time &gt;= #{submitDateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="submitDateTo != null">
			and t1.submit_time &lt; #{submitDateTo, jdbcType=TIMESTAMP}
		</if>
		<if test="publishDateFrom != null">
			and se.create_time &gt;= #{publishDateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="publishDateTo != null">
			and se.create_time &lt; #{publishDateTo, jdbcType=TIMESTAMP}
		</if>
		<if test="startDateFrom != null">
			and se.start_date &gt;= #{startDateFrom, jdbcType=DATE}
		</if>
		<if test="startDateTo != null">
			and se.start_date &lt; #{startDateTo, jdbcType=DATE}
		</if>
		<if test="endDateFrom != null">
			and se.end_date &gt;= #{endDateFrom, jdbcType=DATE}
		</if>
		<if test="endDateTo != null">
			and se.end_date &lt; #{endDateTo, jdbcType=DATE}
		</if>
		<if test="queryField != null and queryField != ''">
			and (o.organization_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or w.work_shop_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.executor_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
		union all
		select 1000000000000000 + we.id * 10000000 + se.id as id, se.id schedule_id, wp.partner_id distributor_id, 
		we.workshop_id, 'mechanic' executor_type, we.code executor_key, we.name executor_name,
			 0 status, null submit_time, null create_user_id, null create_time, null update_user_id, se.create_time update_time,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ScheduleInstance.status' and di1.dic_item_code=0) status_text,
			 s.schedule_name, s.icon_id, s.banner_id, s.schedule_type, s.award_point, se.start_date start_time, se.end_date end_time, 
			 se.create_time publish_time, s.left_steps, s.share_flag,s.fixed_point_flag,s.max_point,
			 o.organization_name distributor_name, w.work_shop_name workshop_name, null remark
			 <if test="includeAgreeNumb">
			 ,null agree_numb
			 </if>
		  from wx_t_schedule_entity se
		  join wx_t_schedule s on s.id=se.schedule_id
		  left join wx_t_workshop_employee we on we.version_no=1 and we.enable_flag=1
		left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id
		  left join wx_t_organization o on o.id=wp.partner_id
		  left join wx_t_work_shop w on w.id=we.workshop_id
		 where DATEADD(day, 1, se.end_date)>getdate() and not exists (select 1 from wx_t_schedule_instance t1 where t1.schedule_id=se.id and t1.executor_type='mechanic' 
		 	and t1.executor_key=we.code)
		 	and (s.execute_user_type = '-1' or s.execute_user_type=we.employee_type)
		 	and ((not exists (select 1 from wx_t_schedule_workshop sw where sw.schedule_id=s.id) 
		 			and not exists (select 1 from wx_t_schedule_distributor sd where sd.schedule_id=s.id))
		 		or exists (select 1 from wx_t_schedule_workshop sw where sw.schedule_id=s.id and sw.workshop_id=we.workshop_id)
		 		or exists (select 1 from wx_t_schedule_distributor sd where sd.schedule_id=s.id and sd.distributor_id=wp.partner_id))
		<if test="distributorId != null">
			and wp.partner_id = #{distributorId, jdbcType=BIGINT}
		</if>
		<if test="workshopId != null">
			and we.workshop_id = #{workshopId, jdbcType=BIGINT}
		</if>
		<if test="scheduleId != null">
			and se.schedule_id = #{scheduleId, jdbcType=BIGINT}
		</if>
		<if test="executorName != null and executorName != ''">
			and we.name like '%' + #{executorName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="userId != null and userId != ''">
			and we.code = #{userId, jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and 0 = #{status, jdbcType=INTEGER}
		</if>
		<if test="statusArray != null">
			and 0 in 
	        <foreach collection="statusArray" item="listItem" open="(" close=")" separator="," >
	          ${listItem}
	        </foreach>
		</if>
		<if test="startStatus != null">
			and 0 >= #{startStatus, jdbcType=INTEGER}
		</if>
		<if test="endStatus != null">
			and 0 &lt;= #{endStatus, jdbcType=INTEGER}
		</if>
		<if test="salesChannel != null and salesChannel != ''">
			and s.sales_channel = #{salesChannel, jdbcType=INTEGER}
		</if>
		<if test="scheduleType != null and scheduleType != ''">
			and s.schedule_type = #{scheduleType, jdbcType=INTEGER}
		</if>
		<if test="submitDateFrom != null">
			and 1!=1
		</if>
		<if test="submitDateTo != null">
			and 1!=1
		</if>
		<if test="publishDateFrom != null">
			and se.create_time &gt;= #{publishDateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="publishDateTo != null">
			and se.create_time &lt; #{publishDateTo, jdbcType=TIMESTAMP}
		</if>
		<if test="startDateFrom != null">
			and se.start_date &gt;= #{startDateFrom, jdbcType=DATE}
		</if>
		<if test="startDateTo != null">
			and se.start_date &lt; #{startDateTo, jdbcType=DATE}
		</if>
		<if test="endDateFrom != null">
			and se.end_date &gt;= #{endDateFrom, jdbcType=DATE}
		</if>
		<if test="endDateTo != null">
			and se.end_date &lt; #{endDateTo, jdbcType=DATE}
		</if>
		<if test="queryField != null and queryField != ''">
			and (o.organization_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or w.work_shop_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or we.name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
	</select>
	<!--  -->
	<select id="getBeanByUks" resultMap="BaseResultMap" parameterType="map">
    	select
		t1.id, t1.schedule_id, t1.distributor_id, t1.workshop_id, t1.executor_type, t1.executor_key, t1.executor_name,
			 t1.status, t1.submit_time, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.remark,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ScheduleInstance.status' and di1.dic_item_code=t1.status) status_text,
		s.schedule_name, s.icon_id, s.banner_id, s.schedule_type, s.award_point, s.start_time, s.end_time, s.publish_time, s.schedule_desc, 
		s.share_flag,s.fixed_point_flag,s.max_point
		  from wx_t_schedule_instance t1
		  join wx_t_schedule_entity se on t1.schedule_id=se.id
		  join wx_t_schedule s on s.id=se.schedule_id
		where 1=1
		<if test="scheduleEntityId != null">
		and se.id=#{scheduleEntityId, jdbcType=BIGINT}
		</if>
		<if test="mechanicId != null">
		and t1.executor_type='mechanic' and exists (select 1 from wx_t_workshop_employee we where we.id=#{mechanicId, jdbcType=BIGINT} and we.code=t1.executor_key)
		</if>
	</select>
	
	<insert id="insertBatchWhenPublish" parameterType="map">
		insert into wx_t_schedule_instance (schedule_id, distributor_id, workshop_id, executor_type, executor_key, executor_name, status, delete_flag) 
		select #{scheduleId, jdbcType=BIGINT}, wp.partner_id, we.workshop_id, 'mechanic', we.code, we.name, 0, 0 
		from wx_t_workshop_employee we
		left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id
		where we.version_no=1 and we.enable_flag=1
		<if test="executeUserType != null">
		and we.employee_type=#{executeUserType, jdbcType=VARCHAR}
		</if>
      			<if test="assignDistributors != null or assignWorkshops != null">
      			and (1!= 1 
      				<if test="assignDistributors != null">
      				or wp.partner_id in 
	                  <foreach collection="assignDistributors" item="listItem" open="(" close=")" separator="," >
	                    #{listItem}
	                  </foreach>
      				</if>
      				<if test="assignWorkshops != null">
      				or we.workshop_id in 
	                  <foreach collection="assignWorkshops" item="listItem" open="(" close=")" separator="," >
	                    #{listItem}
	                  </foreach>
      				</if>
      			)
      			</if>
	</insert>
	<insert id="insertByGetBean" parameterType="com.chevron.b2b.model.ScheduleInstance" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_schedule_instance (schedule_id, distributor_id, workshop_id, executor_type, executor_key, executor_name, status, delete_flag) 
		select #{scheduleId, jdbcType=BIGINT}, wp.partner_id, we.workshop_id, 'mechanic', we.code, we.name, 0, 0 
		from wx_t_workshop_employee we
		left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id
		where we.id=#{mechanicId, jdbcType=BIGINT}
	</insert>
		<select id="selectShareInfo" resultMap="BaseResultMap" parameterType="map">
    	select
		(select count(1) from wx_t_activity_agree aa where aa.activity_id=t1.id) agree_count
		<if test="clientId != null">
		, case when exists (select 1 from wx_t_activity_agree aa where aa.activity_id=t1.id and aa.user_id=#{clientId}) then 1 else 0 end client_agree_flag
		</if>
		  from wx_t_schedule_instance t1
		where t1.id = #{id,jdbcType=BIGINT}
	</select>
		<select id="selectShareDetail" resultMap="BaseResultMap" parameterType="map">
    	select
		t1.id, t1.schedule_id, t1.distributor_id, t1.workshop_id, t1.executor_type, t1.executor_key, t1.executor_name,
			 t1.status, t1.submit_time, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.remark,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ScheduleInstance.status' and di1.dic_item_code=t1.status) status_text,
		s.schedule_name, s.icon_id, s.banner_id, s.schedule_type, s.award_point, s.start_time, s.end_time, s.publish_time, s.schedule_desc, 
		s.share_flag,s.fixed_point_flag,s.max_point, s.form_bg, s.action_bg, 
		(select count(1) from wx_t_activity_agree aa where aa.activity_id=t1.id) agree_count
		<if test="clientId != null">
		, case when exists (select 1 from wx_t_activity_agree aa where aa.activity_id=t1.id and aa.user_id=#{clientId}) then 1 else 0 end client_agree_flag
		</if>
		  from wx_t_schedule_instance t1
		  join wx_t_schedule_entity se on t1.schedule_id=se.id
		  join wx_t_schedule s on s.id=se.schedule_id
		where t1.id = #{id,jdbcType=BIGINT}
	</select>
</mapper>
