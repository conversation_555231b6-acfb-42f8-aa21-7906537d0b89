package com.chevron.pms.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.common.exception.auth.WxAuthException;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.common.constants.Constants;
import com.chevron.log.model.WxTImportLog;
import com.chevron.log.service.WxTImportLogService;
import com.chevron.pms.model.GenericPaginationQueryParams;
import com.chevron.pms.model.OemDelivery;
import com.chevron.pms.service.OemDeliveryService;
import com.chevron.pms.service.QrQrCodeService;
import com.common.util.ServletUtils;
import com.common.util.StringUtils;
import com.sys.quartz.service.OemDeliveryImportJobService;
import com.sys.quartz.service.OemImportService;

@Controller
public class OemDeliveryController {
	
	@Autowired
	private OemDeliveryService odService;
	
	@Autowired
	private OemDeliveryImportJobService odiService;
	
	@Autowired
	private OemImportService importService;
	
	@Autowired
	private WxTImportLogService logService;

	private Logger log = LoggerFactory
			.getLogger(OemDeliveryController.class);

	private static final String ROWS = "rows";
	private static final String TOTAL = "total";
	private static final String RANK_TYPE = "direction";
	private static final String SORT_FILED = "field";
	private static final String LIMIT = "limit";
	private static final String START = "start";

	private final static String SUCCESS_CODE = "success";
	private final static String ERROR_CODE = "error";

	private final static String RESULT_CODE_KEY = "code";
	private final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	
	@ResponseBody
	@RequestMapping("/oem/importdeliveryByFtp.do")
	public List<Map<String, Object>> importdeliveryAll() {
		List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
		try {
			odiService.doImportJob();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultList;
	}
	
	@ResponseBody
	@RequestMapping("/oem/importdelivery.do")
	public Object importdelivery(@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try {
			CommonsMultipartFile cf = (CommonsMultipartFile) myfiles[0];
			DiskFileItem fi = (DiskFileItem) cf.getFileItem();

			File readfile = fi.getStoreLocation();
			String fileName = myfiles[0].getOriginalFilename();
			Map<String, Object> importReult = importService
					.importOemDelivery(fileName,readfile);
			return importReult;
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/oem/queryDelivery.do", method = { RequestMethod.POST })
	public Map<String, Object> queryDelivery(HttpServletRequest request,
			HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try {
			// 1, collect request params
			Map<String, String> requestParamMap = ServletUtils
					.collectParamValues(request);
			log.info("requestParamMap: {}", requestParamMap);

			// 2, convert request param to generic pagination query param
			GenericPaginationQueryParams genericPagQeryParam = convertRequestParamToQueryParam(requestParamMap);

			// 3, query data from db
			long odCount = odService
					.countDeliveryByConditionWithPagination(genericPagQeryParam);
			List<OemDelivery> odList = odService.queryDeliveryByConditionWithPagination(genericPagQeryParam);

			// 4, generate return data
			resultMap.put(TOTAL, odCount);
			resultMap.put(ROWS, odList);
		} catch (Exception e) {
			log.error(e.getMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}

	private GenericPaginationQueryParams convertRequestParamToQueryParam(
			Map<String, String> requestParamMap) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		genericPagQueryParam
				.setStart(Long.parseLong(requestParamMap.get(START)));
		genericPagQueryParam.setLimit(Integer.parseInt(requestParamMap
				.get(LIMIT)));

		String orderByFiled = requestParamMap.get(SORT_FILED);
		String orderByRankType = requestParamMap.get(RANK_TYPE);
		String orderBy = StringUtils.camelToUnderline(orderByFiled) + " "
				+ orderByRankType;
		genericPagQueryParam.setOrderBy(orderBy);

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();
		
		String orderId = requestParamMap.get("orderId");
		if(null != orderId && !orderId.isEmpty()){
			queryParamsMap.put("orderId", orderId);
		}
		
		String recvName = requestParamMap.get("recvName");
		if(null != recvName && !recvName.isEmpty()){
			queryParamsMap.put("recvName", recvName);
		}
		
		String sendName = requestParamMap.get("sendName");
		if(null != sendName && !sendName.isEmpty()){
			queryParamsMap.put("sendName", sendName);
		}

		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		return genericPagQueryParam;
	}

}
