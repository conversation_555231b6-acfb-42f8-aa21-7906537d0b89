package com.sys.umeng.service.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.common.exception.auth.WxAuthException;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.common.config.MyPropertyConfigurer;
import com.common.util.CommonUtil;
import com.common.util.DateUtil;
import com.common.util.XxlsUtils;
import com.sys.umeng.dao.UmengEventDetailMapper;
import com.sys.umeng.dao.UmengMobileportalDetailMapper;
import com.sys.umeng.model.ImportUmengEventData;
import com.sys.umeng.model.ImportUmengMobileportalData;
import com.sys.umeng.model.UmengEventDataAccessUserInfo;
import com.sys.umeng.model.UmengEventDetail;
import com.sys.umeng.model.UmengMobileportalDetail;
import com.sys.umeng.service.UmengDataService;


@Service
public class UmengDataServiceImpl implements UmengDataService{
	public final Logger logerr = Logger.getLogger(this.getClass());
	
	@Resource
	UmengEventDetailMapper umengEventDetailMapper;
	@Resource
	UmengMobileportalDetailMapper umengMobileportalDetailMapper;
	
	@Override
	public Map<String, Object> importUmengEventFromExcel(DiskFileItem dsiItem,
			int sheetIndex, Object obj, String[] models) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<ImportUmengEventData> importUmengEventDataLst = new ArrayList<ImportUmengEventData>();
		try {
			    CommonsMultipartFile cf = new CommonsMultipartFile(dsiItem);
			    InputStream in = cf.getInputStream();
			 	System.out.println("importUmengEventFromExcel:"+in);
			 	XxlsUtils xxls = new XxlsUtils();
			 	xxls.processOneSheet(in, sheetIndex,obj,models);
			 	resultMap.put("dataList", xxls.getList());
			
		} catch (Exception ex) {
				ex.printStackTrace();
				logerr.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
				return resultMap;

		}
		logerr.info("msg:ljc importUmengEventFromExcel====================导入成功,sheetIndex:"+sheetIndex);
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量,sheetIndex"+sheetIndex +":"+ importUmengEventDataLst.size()+",");
		return resultMap;
	}
	
	
	
	@Override
	public Map<String, Object> importUmengMobileportalFromExcel(
			DiskFileItem dsiItem, int sheetIndex, Object obj, String[] models) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<ImportUmengMobileportalData> importUmengMobileportalDataLst = new ArrayList<ImportUmengMobileportalData>();
		try {
			    CommonsMultipartFile cf = new CommonsMultipartFile(dsiItem);
			    InputStream in = cf.getInputStream();
			 	System.out.println("importUmengMobileportalFromExcel:"+in);
			 	XxlsUtils xxls = new XxlsUtils();
			 	xxls.processOneSheet(in, sheetIndex,obj,models);
			 	resultMap.put("dataList", xxls.getList());
			
		} catch (Exception ex) {
				ex.printStackTrace();
				logerr.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
				return resultMap;

		}
		logerr.info("msg:ljc importUmengMobileportalFromExcel====================导入成功,sheetIndex:"+sheetIndex);
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量,sheetIndex"+sheetIndex +":"+ importUmengMobileportalDataLst.size()+",");
		return resultMap;
	}
	
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertUmengEvent(List<ImportUmengEventData> list) {
		Map<String, Object> resultMap = new HashMap<String,Object>();
		try
		{
			//检查
			if(null==list || list.isEmpty())
			{
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "目标数据表信息为空，不能进行导入");
				return resultMap;
			}
			
			//0.组装数据
			//获取日期数据
			String date = list.get(2).getEventDateTime().substring(8, 18);
			List<UmengEventDetail> umengEventDetailLst = new ArrayList<UmengEventDetail>();
			for(int i=4; i<list.size()-1;i++)
			{
				
				ImportUmengEventData tmpEventData = list.get(i);
				UmengEventDetail umengEventDetail = new UmengEventDetail();
				umengEventDetail.setUmodelDate(date+" "+tmpEventData.getEventDateTime());
				umengEventDetail.setImportTime(new Date());
				String tmpStr = tmpEventData.getEventName().replace("+", ";");
				String[] eventNames = tmpStr.split(";");
				if(null==eventNames || eventNames.length<4)
				{
					continue;
				}
				//模块
				umengEventDetail.setUmodelName(eventNames[0]);
				//操作的事件
				umengEventDetail.setUmodelEvent(eventNames[1]);
				//操作者类型
				umengEventDetail.setUmodelOperType(eventNames[2]);
				umengEventDetail.setUmodelOperName(eventNames[3]);
				umengEventDetail.setUmodelOperId(Long.parseLong(tmpEventData.getEventValue()));
				umengEventDetail.setUmodelPage(tmpEventData.getEventPage());
				umengEventDetail.setUmodelDateTime(DateUtil.parseDate(umengEventDetail.getUmodelDate(), 0));
				umengEventDetailLst.add(umengEventDetail);
			}
			//1.插入数据
			if(null!=umengEventDetailLst && !umengEventDetailLst.isEmpty())
			{
				//需要分页插入
				ImportDataPageModelUtil umengEventDetailLstPage = new ImportDataPageModelUtil(
						umengEventDetailLst, 120);
				int toltalPage = umengEventDetailLstPage.getTotalPages();
				for(int i=1;i<=toltalPage;i++)
				{
					@SuppressWarnings("unchecked")
					List<UmengEventDetail> tempumengEventDetailLst  = umengEventDetailLstPage.getObjects(i);
					umengEventDetailMapper.insertBatch(tempumengEventDetailLst);
				}
				
				
			}
			resultMap.put("code", "success");
		}catch (Exception e) {
			logerr.error("insertUmengEvent Exception:"+e.getLocalizedMessage());
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}
	
	
	
	@SuppressWarnings("deprecation")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertUmengMobileportal(
			List<ImportUmengMobileportalData> list) {
		Map<String, Object> resultMap = new HashMap<String,Object>();
		try
		{
			//检查
			if(null==list || list.isEmpty())
			{
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "目标数据表信息为空，不能进行导入");
				return resultMap;
			}
			
			//0.组装数据
			//获取第一行第一个字段是，数据来源
			String dataSource = list.get(0).getDateTime();
			List<UmengMobileportalDetail> umengMobileportalDetailLst = new ArrayList<UmengMobileportalDetail>();
			for(int i=2; i<list.size();i++)
			{
				ImportUmengMobileportalData tmpMobileportalData = list.get(i);
				UmengMobileportalDetail umengMobileportalDetail = new UmengMobileportalDetail();
				umengMobileportalDetail.setActiveUser(Integer.parseInt(tmpMobileportalData.getActiveUser()));
				umengMobileportalDetail.setActiveUserProportion(tmpMobileportalData.getActiveUserProportion());
				umengMobileportalDetail.setDataSource(dataSource);
				umengMobileportalDetail.setImportTime(new Date());
				//原始时间
				Date createDate = DateUtil.parseDate(tmpMobileportalData.getDateTime());
				umengMobileportalDetail.setDataTime(createDate);
				umengMobileportalDetail.setDataDate(tmpMobileportalData.getDateTime());
				umengMobileportalDetail.setDataDateMonth(Integer.parseInt(DateUtil.getMonth(createDate)));
				umengMobileportalDetail.setDataDateDay(Integer.parseInt(DateUtil.getDay(createDate)));
				umengMobileportalDetailLst.add(umengMobileportalDetail);
			}
			//1.插入数据
			if(null!=umengMobileportalDetailLst && !umengMobileportalDetailLst.isEmpty())
			{
				//需要分页插入
				ImportDataPageModelUtil umengMobileportalDetailLstPage = new ImportDataPageModelUtil(
						umengMobileportalDetailLst, 120);
				int toltalPage = umengMobileportalDetailLstPage.getTotalPages();
				for(int i=1;i<=toltalPage;i++)
				{
					@SuppressWarnings("unchecked")
					List<UmengMobileportalDetail> tempumengMobileportalDetailLst  = umengMobileportalDetailLstPage.getObjects(i);
					umengMobileportalDetailMapper.insertBatch(tempumengMobileportalDetailLst);
				}
				
			}
			resultMap.put("code", "success");
		}catch (Exception e) {
			logerr.error("insertUmengMobileportal Exception:"+e.getLocalizedMessage());
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> umengEventDataUserAccess(String modelName,
			Integer year, Integer month, String selectData) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("modelName", modelName);
		reqMap.put("year", year);
		if(null==month)//按年
		{
			selectData = selectData.substring(0, selectData.length()-1);
			reqMap.put("month", CommonUtil.addZeroForStr(selectData, 2, 1));
		}else//按月
		{
			reqMap.put("month", CommonUtil.addZeroForStr(""+month, 2, 1));
			reqMap.put("selectData",  CommonUtil.addZeroForStr(""+selectData, 2, 1));
		}
		List<UmengEventDataAccessUserInfo> lst = umengEventDetailMapper.countUmengEventDataUserAccess(reqMap);
		resultMap.put("code", "success");
		resultMap.put("lst", lst);
		return resultMap;
	}

}
