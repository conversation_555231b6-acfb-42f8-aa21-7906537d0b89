select x.sales_cai, x.sales_channel_name, '<EMAIL> ' email, x.ch_name, x.not_sales from (
select a.sales_cai, a.sales_channel_name + ' ' sales_channel_name, u.email, u.ch_name, 0 not_sales from (
select distinct crss.sales_cai, rsc.sales_channel_name from dw_customer_region_sales_supervisor_rel crss
left join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name 
where crss.sales_cai is not null
union all select distinct crss.suppervisor_cai, rsc.sales_channel_name from dw_customer_region_sales_supervisor_rel crss
left join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name 
where crss.suppervisor_cai is not null
union all select distinct rsc.channel_manager_cai, rsc.sales_channel_name from dw_region_sales_channel_rel rsc
where rsc.channel_manager_cai is not null and rsc.channel_manager_cai !='') a
left join wx_t_user u on u.cai=a.sales_cai and u.status=1
where a.sales_cai not in ('WMPL', 'JNBJ')
union all
select u.cai, case when b.rn=1 then b.bu_name else null end bu_name, u.email, u.ch_name, 1 not_sales from (
select a.user_id, max(a.bu_name) bu_name, count(1) rn from (
select ur.user_id, 'CIO ' bu_name from wx_t_userrole ur
left join wx_t_role r on ur.role_id=r.role_id
where r.ch_role_name = 'Report_Center_CIO_Manager' and user_id!=1
union all select ur.user_id, 'CDM ' bu_name from wx_t_userrole ur
left join wx_t_role r on ur.role_id=r.role_id
where r.ch_role_name = 'Report_Center_CDM_Manager' and user_id!=1) a
group by a.user_id) b
left join wx_t_user u on u.user_id=b.user_id
where u.cai is not null) x