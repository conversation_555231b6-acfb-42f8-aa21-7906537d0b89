
-- ----------------------------
-- Table structure for bpm_buss_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[bpm_buss_config]') AND type IN ('U'))
	DROP TABLE [dbo].[bpm_buss_config]
GO

CREATE TABLE [dbo].[bpm_buss_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [node_id] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [reject_status] varchar(16) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [recall_status] varchar(16) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [process_def_key] varchar(255) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [create_time] datetime  NULL,
  [update_time] datetime  NULL
)
GO

ALTER TABLE [dbo].[bpm_buss_config] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of [bpm_buss_config]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[bpm_buss_config] ON
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'1', N'SL1', N'true', N'true', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'2', N'SL2', N'true', N'true', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'3', N'SL3', N'true', N'true', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'4', N'FL1', N'true', N'true', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'5', N'FL2', N'true', N'true', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'6', N'FL3', N'true', N'true', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'7', N'SL1', N'true', N'true', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'8', N'SL2', N'true', N'true', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'9', N'SL3', N'true', N'true', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'10', N'FL1', N'true', N'true', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'11', N'FL2', N'true', N'true', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'12', N'FL3', N'true', N'true', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'13', N'SL1', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'14', N'SL2', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'15', N'SL3', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'16', N'FL1', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'17', N'FL2', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'18', N'FL3', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'19', N'localCredit', N'true', N'true', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'20', N'request', N'false', N'false', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'21', N'request', N'false', N'false', N'temp_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'22', N'request', N'false', N'false', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'23', N'adjustValue', N'false', N'false', N'cv_credit_request', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'24', N'adjustValue', N'false', N'false', N'annual_credit_review', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_buss_config] ([id], [node_id], [reject_status], [recall_status], [process_def_key], [create_time], [update_time]) VALUES (N'25', N'adjustValue', N'false', N'false', N'temp_credit_request', NULL, NULL)
GO

SET IDENTITY_INSERT [dbo].[bpm_buss_config] OFF
GO


-- ----------------------------
-- Primary Key structure for table bpm_buss_config
-- ----------------------------
ALTER TABLE [dbo].[bpm_buss_config] ADD CONSTRAINT [bpm_buss_config_pk] PRIMARY KEY NONCLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO



-- ----------------------------
-- Table structure for bpm_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[bpm_log]') AND type IN ('U'))
	DROP TABLE [dbo].[bpm_log]
GO

CREATE TABLE [dbo].[bpm_log] (
  [id] bigint  IDENTITY(1,1) NOT NULL,
  [process_ins_id] varchar(50) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [task_id] varchar(50) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [step_name] varchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [executor] varchar(34) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [actual_executor] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [type] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [create_time] datetime  NULL,
  [update_time] datetime  NULL,
  [comments] varchar(512) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [node_id] varchar(32) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[bpm_log] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增id',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程实例id',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'process_ins_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务id',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'task_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'步骤名称',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'step_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'执行人',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'executor'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实际执行人',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'actual_executor'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作状态',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'create_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'update_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log',
'COLUMN', N'comments'
GO

EXEC sp_addextendedproperty
'MS_Description', N'bpm日志信息表',
'SCHEMA', N'dbo',
'TABLE', N'bpm_log'
GO


-- ----------------------------
-- Primary Key structure for table bpm_log
-- ----------------------------
ALTER TABLE [dbo].[bpm_log] ADD CONSTRAINT [bpm_log_pk] PRIMARY KEY NONCLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


/*
 Navicat Premium Data Transfer

 Source Server         : **********
 Source Server Type    : SQL Server
 Source Server Version : 13004435
 Source Host           : **********:1433
 Source Catalog        : workflow0721
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 13004435
 File Encoding         : 65001

 Date: 22/08/2022 14:34:37
*/


-- ----------------------------
-- Table structure for bpm_node_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[bpm_node_config]') AND type IN ('U'))
	DROP TABLE [dbo].[bpm_node_config]
GO

CREATE TABLE [dbo].[bpm_node_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [first_condition] varchar(255) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [second_condition] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [third_condition] varchar(255) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [value] varchar(255) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [create_time] datetime  NULL,
  [update_time] datetime  NULL
)
GO

ALTER TABLE [dbo].[bpm_node_config] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of [bpm_node_config]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[bpm_node_config] ON
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'1', N'Direct_OEM', N'150000', N'cv_credit_request', N'request,localCredit,SL1,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'2', N'Direct_OEM', N'250000', N'cv_credit_request', N'request,localCredit,SL1,SL2,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'3', N'Direct_Industrial,Indirect_Indirect', N'80000', N'cv_credit_request', N'request,localCredit,SL1,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'4', N'Direct_Industrial,Indirect_Indirect', N'250000', N'cv_credit_request', N'request,localCredit,SL1,SL2,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'5', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'300000', N'cv_credit_request', N'request,localCredit,SL1,SL2,FL1,FL2', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'6', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'500000', N'cv_credit_request', N'request,localCredit,SL1,SL2,SL3,FL1,FL2', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'7', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'1000000', N'cv_credit_request', N'request,localCredit,SL1,SL2,SL3,FL1,FL2,FL3', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'8', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'1000000', N'temp_credit_request', N'request,SL1,SL2,SL3,FL1,FL2,FL3', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'9', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'500000', N'temp_credit_request', N'request,SL1,SL2,SL3,FL1,FL2', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'10', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'300000', N'temp_credit_request', N'request,SL1,SL2,FL1,FL2', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'11', N'Direct_Industrial,Indirect_Indirect', N'250000', N'temp_credit_request', N'request,SL1,SL2,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'12', N'Direct_Industrial,Indirect_Indirect', N'80000', N'temp_credit_request', N'request,SL1,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'13', N'Direct_OEM', N'250000', N'temp_credit_request', N'request,SL1,SL2,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'14', N'Direct_OEM', N'150000', N'temp_credit_request', N'request,SL1,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'15', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'1000000', N'annual_credit_review', N'request,SL1,SL2,SL3,FL1,FL2,FL3', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'16', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'500000', N'annual_credit_review', N'request,SL1,SL2,SL3,FL1,FL2', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'17', N'Direct_OEM,Direct_Industrial,Indirect_Indirect', N'300000', N'annual_credit_review', N'request,SL1,SL2,FL1,FL2', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'18', N'Direct_Industrial,Indirect_Indirect', N'250000', N'annual_credit_review', N'request,SL1,SL2,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'19', N'Direct_Industrial,Indirect_Indirect', N'80000', N'annual_credit_review', N'request,SL1,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'20', N'Direct_OEM', N'250000', N'annual_credit_review', N'request,SL1,SL2,FL1', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_node_config] ([id], [first_condition], [second_condition], [third_condition], [value], [create_time], [update_time]) VALUES (N'21', N'Direct_OEM', N'150000', N'annual_credit_review', N'request,SL1,FL1', NULL, NULL)
GO

SET IDENTITY_INSERT [dbo].[bpm_node_config] OFF
GO


-- ----------------------------
-- Primary Key structure for table bpm_node_config
-- ----------------------------
ALTER TABLE [dbo].[bpm_node_config] ADD CONSTRAINT [bpm_node_config_pk] PRIMARY KEY NONCLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

/*
 Navicat Premium Data Transfer

 Source Server         : **********
 Source Server Type    : SQL Server
 Source Server Version : 13004435
 Source Host           : **********:1433
 Source Catalog        : workflow0721
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 13004435
 File Encoding         : 65001

 Date: 22/08/2022 14:34:50
*/


-- ----------------------------
-- Table structure for bpm_variables_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[bpm_variables_config]') AND type IN ('U'))
	DROP TABLE [dbo].[bpm_variables_config]
GO

CREATE TABLE [dbo].[bpm_variables_config] (
  [id] bigint  IDENTITY(1,1) NOT NULL,
  [node_id] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [data_type] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [name] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [value] varchar(64) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [process_def_Id] varchar(64) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [version] int  NULL,
  [process_def_key] varchar(32) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [create_time] datetime  NULL,
  [update_time] datetime  NULL,
  [remark] varchar(256) COLLATE Chinese_PRC_90_CI_AS  NULL,
  [task_data_type] varchar(50) COLLATE Chinese_PRC_90_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[bpm_variables_config] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增id',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务节点ID',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'node_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'配置类型类型，暂时只有用户任务(USERTASK)。',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'data_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变量名',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变量值',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'value'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程定义ID',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'process_def_Id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程定义版本',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'version'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程定义KEY',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'process_def_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N' 创建时间',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'create_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'update_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'描述',
'SCHEMA', N'dbo',
'TABLE', N'bpm_variables_config',
'COLUMN', N'remark'
GO


-- ----------------------------
-- Records of [bpm_variables_config]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[bpm_variables_config] ON
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'1', NULL, N'PROCESS', N'cfg_dep1', N'Direct_OEM', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:09:39.140', N'2022-07-26 17:09:39.140', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'2', NULL, N'PROCESS', N'cfg_dep1', N'Direct_OEM', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:09:39.163', N'2022-07-26 17:09:39.163', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'3', N'request', N'USERTASK', N'TITLE', N'发起请求', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.017', N'2022-07-26 14:37:34.017', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'4', N'SL1', N'USERTASK', N'TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.037', N'2022-07-26 14:37:34.037', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'5', N'SL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Area Sales Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.063', N'2022-07-26 14:37:34.063', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'6', N'SL2', N'USERTASK', N'TITLE', N'BU Sales Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.087', N'2022-07-26 14:37:34.087', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'7', N'SL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Sales Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.107', N'2022-07-26 14:37:34.107', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'8', N'SL3', N'USERTASK', N'TITLE', N'ABM Sales Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.127', N'2022-07-26 14:37:34.127', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'9', N'SL3', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit ABM Sales Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.150', N'2022-07-26 14:37:34.150', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'10', N'FL1', N'USERTASK', N'TITLE', N'Local Credit Analyst Approval/Local Credit Team Lead Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.173', N'2022-07-26 14:37:34.173', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'11', N'FL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Analyst,Local Credit Team Lead', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.213', N'2022-07-26 14:37:34.213', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'12', N'FL2', N'USERTASK', N'TITLE', N'Local Credit Team Lead Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.240', N'2022-07-26 14:37:34.240', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'13', N'FL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Team Lead', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.263', N'2022-07-26 14:37:34.263', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'14', N'FL3', N'USERTASK', N'TITLE', N'China Finance Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.287', N'2022-07-26 14:37:34.287', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'15', N'FL3', N'USERTASK', N'CANDDIDATE_GROUP', N'China Finance Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 14:37:34.303', N'2022-07-26 14:37:34.303', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'16', NULL, N'PROCESS', N'cfg_dep1', N'Direct_OEM', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:09:39.183', N'2022-07-26 17:09:39.183', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'17', NULL, N'PROCESS', N'cfg_dep2', N'Indirect_Indirect', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:09:39.207', N'2022-07-26 17:09:39.207', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'18', NULL, N'PROCESS', N'cfg_dep2', N'Indirect_Indirect', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:09:39.227', N'2022-07-26 17:09:39.227', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'19', NULL, N'PROCESS', N'cfg_dep2', N'Indirect_Indirect', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:09:39.247', N'2022-07-26 17:09:39.247', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'20', NULL, N'PROCESS', N'cfg_dep3', N'Direct_Industrial', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:09:39.267', N'2022-07-26 17:09:39.267', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'21', NULL, N'PROCESS', N'cfg_dep3', N'Direct_Industrial', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:09:39.287', N'2022-07-26 17:09:39.287', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'22', NULL, N'PROCESS', N'cfg_dep3', N'Direct_Industrial', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:09:39.310', N'2022-07-26 17:09:39.310', NULL, NULL)
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'23', NULL, N'PROCESS', N'cfg_range4', N'300000', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:10:22.363', N'2022-07-26 17:10:22.363', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'24', NULL, N'PROCESS', N'cfg_range4', N'300000', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:10:22.387', N'2022-07-26 17:10:22.387', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'25', NULL, N'PROCESS', N'cfg_range4', N'300000', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:10:22.407', N'2022-07-26 17:10:22.407', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'26', NULL, N'PROCESS', N'cfg_range2', N'80000', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:10:22.427', N'2022-07-26 17:10:22.427', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'27', NULL, N'PROCESS', N'cfg_range2', N'80000', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:10:22.447', N'2022-07-26 17:10:22.447', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'28', NULL, N'PROCESS', N'cfg_range2', N'80000', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:10:22.477', N'2022-07-26 17:10:22.477', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'29', NULL, N'PROCESS', N'cfg_range3', N'250000', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:10:22.497', N'2022-07-26 17:10:22.497', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'30', NULL, N'PROCESS', N'cfg_range3', N'250000', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:10:22.517', N'2022-07-26 17:10:22.517', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'31', NULL, N'PROCESS', N'cfg_range3', N'250000', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:10:22.540', N'2022-07-26 17:10:22.540', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'32', NULL, N'PROCESS', N'cfg_range1', N'150000', NULL, NULL, N'cv_credit_request', N'2022-07-26 17:10:22.570', N'2022-07-26 17:10:22.570', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'33', NULL, N'PROCESS', N'cfg_range1', N'150000', NULL, NULL, N'temp_credit_request', N'2022-07-26 17:10:22.590', N'2022-07-26 17:10:22.590', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'34', NULL, N'PROCESS', N'cfg_range1', N'150000', NULL, NULL, N'annual_credit_review', N'2022-07-26 17:10:22.610', N'2022-07-26 17:10:22.610', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'35', N'adjustValue', N'USERTASK', N'TITLE', N'adjustValue', NULL, NULL, N'cv_credit_request', N'2022-07-29 16:14:42.733', N'2022-07-29 16:14:42.733', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'36', N'adjustValue', N'USERTASK', N'TITLE', N'adjustValue', NULL, NULL, N'temp_credit_request', N'2022-07-29 16:14:42.760', N'2022-07-29 16:14:42.760', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'37', N'adjustValue', N'USERTASK', N'TITLE', N'adjustValue', NULL, NULL, N'annual_credit_review', N'2022-07-29 16:14:42.780', N'2022-07-29 16:14:42.780', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'38', N'request', N'USERTASK', N'TITLE', N'发起请求', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.620', N'2022-07-26 14:39:30.620', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'39', N'SL1', N'USERTASK', N'TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.640', N'2022-07-26 14:39:30.640', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'40', N'SL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Area Sales Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.657', N'2022-07-26 14:39:30.657', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'41', N'SL2', N'USERTASK', N'TITLE', N'BU Sales Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.673', N'2022-07-26 14:39:30.673', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'42', N'SL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Sales Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.693', N'2022-07-26 14:39:30.693', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'43', N'SL3', N'USERTASK', N'TITLE', N'ABM Sales Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.710', N'2022-07-26 14:39:30.710', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'44', N'SL3', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit ABM Sales Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.730', N'2022-07-26 14:39:30.730', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'45', N'FL1', N'USERTASK', N'TITLE', N'Local Credit Analyst Approval/Local Credit Team Lead Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.747', N'2022-07-26 14:39:30.747', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'46', N'FL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Analyst,Local Credit Team Lead', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.773', N'2022-07-26 14:39:30.773', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'47', N'FL2', N'USERTASK', N'TITLE', N'Local Credit Team Lead Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.793', N'2022-07-26 14:39:30.793', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'48', N'FL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Team Lead', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.810', N'2022-07-26 14:39:30.810', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'49', N'FL3', N'USERTASK', N'TITLE', N'China Finance Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.827', N'2022-07-26 14:39:30.827', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'50', N'FL3', N'USERTASK', N'CANDDIDATE_GROUP', N'China Finance Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 14:39:30.847', N'2022-07-26 14:39:30.847', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'51', N'request', N'USERTASK', N'TITLE', N'发起请求', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.613', N'2022-07-26 14:43:29.613', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'52', N'localCredit', N'USERTASK', N'TITLE', N'Local Credit Analyst Approval,Local Credit Team Lead Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.630', N'2022-07-26 14:43:29.630', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'53', N'localCredit', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Analyst,Local Credit Team Lead', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.650', N'2022-07-26 14:43:29.650', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'54', N'SL1', N'USERTASK', N'Direct_TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.673', N'2022-07-26 14:43:29.673', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'55', N'SL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Area Sales Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.697', N'2022-07-26 14:43:29.697', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'56', N'SL2', N'USERTASK', N'TITLE', N'BU Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.713', N'2022-07-26 14:43:29.713', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'57', N'SL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Sales Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.730', N'2022-07-26 14:43:29.730', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'58', N'SL3', N'USERTASK', N'TITLE', N'ABM Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.753', N'2022-07-26 14:43:29.753', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'59', N'SL3', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit ABM Sales Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.783', N'2022-07-26 14:43:29.783', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'60', N'FL1', N'USERTASK', N'TITLE', N'Local Credit Analyst Approval/Local Credit Team Lead Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.807', N'2022-07-26 14:43:29.807', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'61', N'FL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Analyst,Local Credit Team Lead', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.830', N'2022-07-26 14:43:29.830', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'62', N'FL2', N'USERTASK', N'TITLE', N'Local Credit Team Lead Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.853', N'2022-07-26 14:43:29.853', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'63', N'FL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Local Credit Team Lead', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.873', N'2022-07-26 14:43:29.873', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'64', N'FL3', N'USERTASK', N'TITLE', N'China Finance Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.897', N'2022-07-26 14:43:29.897', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'65', N'FL3', N'USERTASK', N'CANDDIDATE_GROUP', N'China Finance Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.917', N'2022-07-26 14:43:29.917', NULL, N'Direct,Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'66', N'SL1', N'USERTASK', N'TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 16:45:50.663', N'2022-07-26 16:45:50.663', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'67', N'SL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Area Sales Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 16:45:50.687', N'2022-07-26 16:45:50.687', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'68', N'SL2', N'USERTASK', N'TITLE', N'BU Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 16:45:50.703', N'2022-07-26 16:45:50.703', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'69', N'SL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Sales Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 16:45:50.723', N'2022-07-26 16:45:50.723', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'70', N'SL3', N'USERTASK', N'TITLE', N'ABM Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 16:45:50.740', N'2022-07-26 16:45:50.740', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'71', N'SL3', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit ABM Sales Manager', NULL, NULL, N'cv_credit_request', N'2022-07-26 16:45:50.760', N'2022-07-26 16:45:50.760', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'72', N'SL1', N'USERTASK', N'TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 16:46:30.457', N'2022-07-26 16:46:30.457', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'73', N'SL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Area Sales Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 16:46:30.477', N'2022-07-26 16:46:30.477', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'74', N'SL2', N'USERTASK', N'TITLE', N'BU Sales Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 16:46:30.497', N'2022-07-26 16:46:30.497', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'75', N'SL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Sales Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 16:46:30.517', N'2022-07-26 16:46:30.517', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'76', N'SL3', N'USERTASK', N'TITLE', N'ABM Sales Manager Approval', NULL, NULL, N'temp_credit_request', N'2022-07-26 16:46:30.533', N'2022-07-26 16:46:30.533', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'77', N'SL3', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit ABM Sales Manager', NULL, NULL, N'temp_credit_request', N'2022-07-26 16:46:30.553', N'2022-07-26 16:46:30.553', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'78', N'SL1', N'USERTASK', N'TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 16:46:54.167', N'2022-07-26 16:46:54.167', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'79', N'SL1', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Area Sales Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 16:46:54.190', N'2022-07-26 16:46:54.190', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'80', N'SL2', N'USERTASK', N'TITLE', N'BU Sales Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 16:46:54.207', N'2022-07-26 16:46:54.207', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'81', N'SL2', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit BU Sales Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 16:46:54.227', N'2022-07-26 16:46:54.227', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'82', N'SL3', N'USERTASK', N'TITLE', N'ABM Sales Manager Approval', NULL, NULL, N'annual_credit_review', N'2022-07-26 16:46:54.250', N'2022-07-26 16:46:54.250', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'83', N'SL3', N'USERTASK', N'CANDDIDATE_GROUP', N'Credit ABM Sales Manager', NULL, NULL, N'annual_credit_review', N'2022-07-26 16:46:54.280', N'2022-07-26 16:46:54.280', NULL, N'Indirect')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'87', NULL, N'PROCESS', N'cfg_range5', N'500000', NULL, NULL, N'annual_credit_review', N'2022-08-09 11:58:17.000', N'2022-08-09 11:58:15.000', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'88', NULL, N'PROCESS', N'formBizService', N'creditAppRequestFormBizServiceImpl', NULL, NULL, N'annual_credit_review', N'2022-08-10 13:51:50.093', N'2022-08-10 13:51:50.093', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'89', NULL, N'PROCESS', N'todoEmailTemplate', N'credit/credit_app_info_annual.ftl', NULL, NULL, N'annual_credit_review', N'2022-08-10 13:51:50.180', N'2022-08-10 13:51:50.180', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'90', NULL, N'PROCESS', N'rejectEmailTemplate', N'credit/credit_app_info_annual.ftl', NULL, NULL, N'annual_credit_review', N'2022-08-10 13:51:50.253', N'2022-08-10 13:51:50.253', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'91', NULL, N'PROCESS', N'endEmailTemplate', N'credit/credit_app_info_annual.ftl', NULL, NULL, N'annual_credit_review', N'2022-08-10 13:51:50.327', N'2022-08-10 13:51:50.327', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'104', N'SL1', N'USERTASK', N'TITLE', N'BU Area Sales Manager Approval', NULL, NULL, N'cv_credit_request', N'2022-07-26 14:43:29.713', N'2022-07-26 14:43:29.713', NULL, N'Direct')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'92', NULL, N'PROCESS', N'formBizService', N'creditAppRequestFormBizServiceImpl', NULL, NULL, N'temp_credit_request', N'2022-08-10 13:57:19.670', N'2022-08-10 13:57:19.670', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'93', NULL, N'PROCESS', N'todoEmailTemplate', N'credit/credit_app_info_temp.ftl', NULL, NULL, N'temp_credit_request', N'2022-08-10 13:57:19.760', N'2022-08-10 13:57:19.760', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'94', NULL, N'PROCESS', N'rejectEmailTemplate', N'credit/credit_app_info_temp.ftl', NULL, NULL, N'temp_credit_request', N'2022-08-10 13:57:19.833', N'2022-08-10 13:57:19.833', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'95', NULL, N'PROCESS', N'endEmailTemplate', N'credit/credit_app_info_temp.ftl', NULL, NULL, N'temp_credit_request', N'2022-08-10 13:57:19.907', N'2022-08-10 13:57:19.907', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'96', NULL, N'PROCESS', N'formBizService', N'creditAppRequestFormBizServiceImpl', NULL, NULL, N'cv_credit_request', N'2022-08-10 13:59:52.703', N'2022-08-10 13:59:52.703', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'97', NULL, N'PROCESS', N'todoEmailTemplate', N'credit/credit_app_info_cv.ftl', NULL, NULL, N'cv_credit_request', N'2022-08-10 13:59:52.797', N'2022-08-10 13:59:52.797', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'98', NULL, N'PROCESS', N'rejectEmailTemplate', N'credit/credit_app_info_cv.ftl', NULL, NULL, N'cv_credit_request', N'2022-08-10 13:59:52.867', N'2022-08-10 13:59:52.867', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'99', NULL, N'PROCESS', N'endEmailTemplate', N'credit/credit_app_info_cv.ftl', NULL, NULL, N'cv_credit_request', N'2022-08-10 13:59:52.940', N'2022-08-10 13:59:52.940', NULL, N'EMAIL')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'100', NULL, N'PROCESS', N'cfg_range5', N'500000', NULL, NULL, N'cv_credit_request', N'2022-08-09 11:58:17.000', N'2022-08-09 11:58:15.000', NULL, N'MONEY')
GO

INSERT INTO [dbo].[bpm_variables_config] ([id], [node_id], [data_type], [name], [value], [process_def_Id], [version], [process_def_key], [create_time], [update_time], [remark], [task_data_type]) VALUES (N'103', NULL, N'PROCESS', N'cfg_range5', N'500000', NULL, NULL, N'temp_credit_request', N'2022-08-09 11:58:17.000', N'2022-08-09 11:58:15.000', NULL, N'MONEY')
GO

SET IDENTITY_INSERT [dbo].[bpm_variables_config] OFF
GO


-- ----------------------------
-- Primary Key structure for table bpm_variables_config
-- ----------------------------
ALTER TABLE [dbo].[bpm_variables_config] ADD CONSTRAINT [bpm_variables_config_pk] PRIMARY KEY NONCLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

