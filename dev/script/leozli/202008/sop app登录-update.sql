insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Specialist'), 'App', 16, 1, 'APP首页权限。16-SOP', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Manager'), 'App', 16, 1, 'APP首页权限。16-SOP', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_SOP_Leader'), 'App', 16, 1, 'APP首页权限。16-SOP', 1, getdate());
