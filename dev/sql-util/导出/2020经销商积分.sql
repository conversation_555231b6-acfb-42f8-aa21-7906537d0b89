--收入
	SELECT
		o.id partner_id, max(o.organization_name) organization_name,
		case when a.application_type='promotion' then '商用油促销积分' 
			when a.application_type='caltex' then '商用油进货积分'
			when a.application_type='cdm_stock' then '乘用车进货积分'
			when a.application_type='cdm_material' then '乘用车物料积分'
			when a.application_type='cdm_promotion' then '乘用车促销积分'
			when a.application_type='cdm_red_bag' then '乘用车红包'
			when a.application_type='cdm_store_open' then '乘用车新店开业礼包兑换币'
		else a.application_type end "积分类型",				
		SUM ( a.POINT_VALUE ) value
	FROM
		(
		SELECT
			pac.POINT_ACCOUNT_OWNER_ID,
			pde.POINT_VALUE,
			pde.CREATION_TIME,
		CASE
				
				WHEN pde.POINT_TYPE = 'CALTEX_POINT' THEN
				'caltex' 
				WHEN pde.POINT_TYPE = 'PROMOTION_POINT' THEN
				'promotion' 
				WHEN pde.POINT_TYPE = 'CDM_STOCK_POINT' THEN
				'cdm_stock' 
				WHEN pde.POINT_TYPE = 'CDM_MATERIAL_POINT' THEN
				'cdm_material' 
				WHEN pde.POINT_TYPE = 'CDM_PROMOTION_POINT' THEN
				'cdm_promotion' 
				WHEN pde.POINT_TYPE = 'CDM_RED_BAG_POINT' THEN
				'cdm_red_bag' 
				WHEN pde.POINT_TYPE = 'CDM_STORE_OPEN_POINT' THEN
				'cdm_store_open' 
			END AS application_type,
			plog.COMMENTS,
			1 AS status,
			NULL AS id
		FROM
			wx_t_point_value_detail pde
			LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
			LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID 
			left join wx_t_point_business pb on pde.BUSINESS_ID=pb.id
		WHERE
			1 = 1 
			AND pde.POINT_VALUE!= 0 
			and (pde.POINT_TYPE != 'CDM_RED_BAG_POINT' or pb.BUSINESS_TYPE_CODE in ('POINT_IMPORT', 'ADJUST_POINT'))
			AND pde.creation_time>='2020-01-01'
		 --and pac.POINT_ACCOUNT_OWNER_ID=56640
		) a left join wx_t_organization o on o.id= a.POINT_ACCOUNT_OWNER_ID
	GROUP BY
	application_type, o.id
		order by application_type, o.id
		
		
--支出
	SELECT
		o.id partner_id, max(o.organization_name) organization_name,
		case when a.application_type='promotion' then '商用油促销积分' 
			when a.application_type='caltex' then '商用油进货积分'
			when a.application_type='cdm_stock' then '乘用车进货积分'
			when a.application_type='cdm_material' then '乘用车物料积分'
			when a.application_type='cdm_promotion' then '乘用车促销积分'
			when a.application_type='cdm_red_bag' then '乘用车红包'
			when a.application_type='cdm_store_open' then '乘用车新店开业礼包兑换币'
		else a.application_type end "积分类型",		 
		SUM ( a.total_price ) value
	FROM
		(
		SELECT
			a.APPLICATION_ORG_ID,
			SUM ( de.MATERIAL_PRICE* de.APPLICATION_QTY ) AS total_price,
			a.CREATION_TIME,
			a.APPLICATION_TYPE,
			'订单:' + a.APPLICATION_CODE AS comments,
			0 AS status,
			a.id AS id 
		FROM
			dbo.wx_t_material_application a
			LEFT JOIN dbo.wx_t_organization o ON o.id = a.APPLICATION_ORG_ID
			LEFT JOIN dbo.wx_t_material_application_detail de ON de.APPLICATION_ID = a.ID 
		WHERE
			1 = 1 
			AND a.DELETE_FLAG = 0 
			AND a.APPLICATION_TYPE IS NOT NULL
			AND a.APPLICATION_STATUS NOT IN ( 'REJECTED', 'DRAFT', 'PENDING_4_URGENT' ) 
			AND a.creation_time >='2020-01-01'
		GROUP BY
			a.APPLICATION_CODE,
			a.APPLICATION_TYPE,
			a.APPLICATION_STATUS,
			a.CREATION_TIME,
			a.LAST_UPDATE_TIME,
			a.APPLICATION_ORG_ID,
			a.APPLICATION_PERSON_ID,
			a.id 
		) a 
		left join wx_t_organization o on o.id= a.APPLICATION_ORG_ID --where o.id=56640
	GROUP BY
		application_type, o.id
		order by application_type, o.id

		