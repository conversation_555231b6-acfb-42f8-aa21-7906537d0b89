package com.chevron.haorder.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.chevron.haorder.model.HaOrderApplyBatchVo;
import com.chevron.haorder.model.HaOrderConditions;
import com.chevron.haorder.model.importvo.ImportHavolinePrices;
import com.chevron.haorder.model.importvo.ImportHavolineWarehouse;
import com.chevron.haorder.model.importvo.ImportPriceList;
import com.chevron.haorder.model.importvo.ImportTcp;
import com.chevron.haorder.service.IHaOrderService;
import com.chevron.haorder.service.IHaOrderWorkFlowService;
import com.chevron.pms.model.OrderDDBXCondition;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.sys.auth.model.WxTUser;
import com.sys.file.web.FileManager;

@Controller
@RequestMapping(value = "/haorderServ")
public class HaOrderController {
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	private static Logger log = LoggerFactory.getLogger(FileManager.class);
	@Resource
	private IHaOrderService haOrderServiceImpl;
	
	@Resource
	private IHaOrderWorkFlowService haOrderWorkFlowServiceImpl;
	
	public static final String TCP_SHEET_MODE[] = new String[]{"wareHouse","amount","totalPrice","confirmAmount","amountnetvat","vat","totalamount","refundamount","refundvat","netunitprice","totalnetunitprice" };
	public static final int TCP_SHEET_INDEX = 1;
	
	public static final String HAVOLINEPRICES_SHEET_MODE[] = new String[]{"category","sku","workShopname","amount","totalprice","nettotalprice1","totalprice1","netunitprice","totalnetunitprice"};
	public static final int HAVOLINEPRICES_SHEET_INDEX = 2;
	
	public static final String HAVOLINEWAREHOUSE_SHEET_MODE[] = new String[]{"wareHouse","category","sku","amount","actAmount" };
	public static final int HAVOLINEWAREHOUSE_SHEET_INDEX = 3;
	
	public static final String PRICELIST_SHEET_MODE[] = new String[]{"sku","price","amount","units","time1","time2"};
	public static final int PRICELIST_SHEET_INDEX = 8;
	
	public static final String  LSTIMPORTTCPS = "lstImportTcps";
	public static final String  LSTIMPORTHAVOLINEPRICES = "lstImportHavolinePrices";
	public static final String  LSTIMPORTHAVOLINEWAREHOUSES = "lstImportHavolineWarehouses";
	public static final String  LSTIMPORTPRICES = "lstImportPrices";
	
	@ResponseBody
	@RequestMapping(value = "/importHAOrderBatchData.do", method = RequestMethod.POST)
	public Object importHAOrderBatchDataFromExcel(
			@RequestParam("myfiles") MultipartFile[] myfiles,
			HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			CommonsMultipartFile cf = (CommonsMultipartFile) myfiles[0];
			DiskFileItem fi = (DiskFileItem) cf.getFileItem();
			log.info("msg:ljc 0823=======================================HA订单 导入开始:"+fi.getInputStream());
			
			/**
			 * TCP sheet
			 */
			Map<String,Object> tcpMap = haOrderServiceImpl.importHAOrderBatchDataFromExcel(fi,TCP_SHEET_INDEX,new ImportTcp(),TCP_SHEET_MODE);
			if(!"success".equals((String)tcpMap.get("code")))
			{
				throw new Exception((String)tcpMap.get("codeMsg"));
			}
		    List<ImportTcp> lstImportTcps =  (List<ImportTcp>) tcpMap.get("dataList");
		    
		    
		    /**
		     * havolineprices sheet
		     */
		    Map<String,Object> havolinePriceMap = haOrderServiceImpl.importHAOrderBatchDataFromExcel(fi,HAVOLINEPRICES_SHEET_INDEX,new ImportHavolinePrices(),HAVOLINEPRICES_SHEET_MODE);
		    if(!"success".equals((String)havolinePriceMap.get("code")))
			{
				throw new Exception((String)havolinePriceMap.get("codeMsg"));
			}
		    List<ImportHavolinePrices> lstImportHavolinePrices =  (List<ImportHavolinePrices>) havolinePriceMap.get("dataList");
		    
			
		    /**
		     * havolineWarehouse sheet
		     */
		    Map<String,Object> havolineWarehouseMap = haOrderServiceImpl.importHAOrderBatchDataFromExcel(fi,HAVOLINEWAREHOUSE_SHEET_INDEX,new ImportHavolineWarehouse(),HAVOLINEWAREHOUSE_SHEET_MODE);
		    if(!"success".equals((String)havolineWarehouseMap.get("code")))
			{
				throw new Exception((String)havolineWarehouseMap.get("codeMsg"));
			}
		    List<ImportHavolineWarehouse> lstImportHavolineWarehouses =  (List<ImportHavolineWarehouse>) havolineWarehouseMap.get("dataList");
		    
		    
		    /**
		     * priceList sheet
		     */
		    Map<String,Object> priceListMap =  	haOrderServiceImpl.importHAOrderBatchDataFromExcel(fi,PRICELIST_SHEET_INDEX,new ImportPriceList(),PRICELIST_SHEET_MODE);
		    if(!"success".equals((String)priceListMap.get("code")))
			{
				throw new Exception((String)priceListMap.get("codeMsg"));
			}
		    List<ImportPriceList> lstImportPrices =  (List<ImportPriceList>) priceListMap.get("dataList");
		    
		    /**
		     * 录入数据库，发送邮件审批
		     */
		    reqMap.put(LSTIMPORTTCPS, lstImportTcps);
		    reqMap.put(LSTIMPORTHAVOLINEPRICES, lstImportHavolinePrices);
		    reqMap.put(LSTIMPORTHAVOLINEWAREHOUSES, lstImportHavolineWarehouses);
		    reqMap.put(LSTIMPORTPRICES, lstImportPrices);
		    resultMap =haOrderServiceImpl.generateHAOrdersByMap(reqMap);
		    log.info("msg:ljc 0823=======================================HA订单 导入完成");
			
		}catch(Exception ex)
		{
			ex.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入订单异常:"+ex.getLocalizedMessage());
			return resultMap;
		}
		return resultMap;

	}
	
	/**
	 * 查询我的待办任务列表
	 * <AUTHOR> 2017-8-24 上午11:44:46
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/myTaskList.do", method = RequestMethod.POST)
	public Object myTaskList(HttpServletRequest request) throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			String start = request.getParameter("start");
			String limit = request.getParameter("limit");
			String batchId = request.getParameter("batchId");
			log.info("myTaskList---start:"+start+"----limit:"+limit+"---batchId:"+batchId);
			WxTUser currentUser = ContextUtil.getCurUser();
		    resultMap = haOrderWorkFlowServiceImpl.queryHAOrderWorkFlowTasks(currentUser.getLoginName(), HaOrderApplyBatchVo.APPLY_PRO_KEY,batchId);
			
		}catch(Exception ex)
		{
			ex.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			return resultMap;
		}
		return resultMap;

	}
	
	
	
	/**
	 * HA订单查询
	 * <AUTHOR> 2017-10-24 上午9:57:11
	 * @param orderConditions
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/queryHAOrders.do", method = {RequestMethod.POST})
    public Map<String,Object> queryHAOrders(HaOrderConditions orderConditions)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			resultMap = haOrderServiceImpl.queryHAOrdersByConditions(orderConditions);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	
}
