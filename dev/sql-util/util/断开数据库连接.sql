

SELECT * FROM
　　[Master].[dbo].[SYSPROCESSES] WHERE [DBID] IN ( SELECT 
   　　[DBID]
　　FROM 
   　　[Master].[dbo].[SYSDATABASES]
　　WHERE 
  　　 NAME='pmpdb01' --and hostname='PP-APP04'
　　)
--EXEC KillPpPendingSp 
--DBCC INPUTBUFFER (93)
--kill 93
use master  
go
ALTER proc KillSpByDbName(@dbname varchar(20), @hostname varchar(20))   
as   
begin   
declare @sql nvarchar(500),@temp varchar(1000)
declare @spid int   
set @sql='declare getspid cursor for   
select spid from sysprocesses where dbid=db_id('''+@dbname+''') and hostname=''' +@hostname+ ''''   
exec (@sql)   
open getspid   
fetch next from getspid into @spid   
while @@fetch_status<>-1   
begin   
  set @temp='kill '+rtrim(@spid)
  exec(@temp)
fetch next from getspid into @spid   
end   
close getspid   
deallocate getspid   
end   

use master  
go
create proc KillPpPendingSp   
as   
begin   
declare @sql nvarchar(500),@temp varchar(1000)
declare @spid int   
set @sql='declare getspid cursor for   
select spid from sysprocesses where dbid=db_id(''pmpdb01'') and hostname in (''PP-APP03'',''PP-APP04'')'   
exec (@sql)   
open getspid   
fetch next from getspid into @spid   
while @@fetch_status<>-1   
begin   
  set @temp='kill '+rtrim(@spid)
  exec(@temp)
fetch next from getspid into @spid   
end   
close getspid   
deallocate getspid   
end   



SELECT creation_time N'语句编译时间'
,last_execution_time N'上次执行时间'
,total_physical_reads N'物理读取总次数'
,total_logical_reads/execution_count N'每次逻辑读次数'
,total_logical_reads N'逻辑读取总次数'
,total_logical_writes N'逻辑写入总次数'
,execution_count N'执行次数'
,total_worker_time/1000 N'所用的CPU总时间ms'
,total_elapsed_time/1000 N'总花费时间ms'
,(total_elapsed_time / execution_count)/1000 N'平均时间ms'
,SUBSTRING(st.text, (qs.statement_start_offset/2) + 1,
((CASE statement_end_offset
WHEN -1 THEN DATALENGTH(st.text)
ELSE qs.statement_end_offset END
- qs.statement_start_offset)/2) + 1) N'执行语句'
FROM sys.dm_exec_query_stats AS qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
where SUBSTRING(st.text, (qs.statement_start_offset/2) + 1,
((CASE statement_end_offset
WHEN -1 THEN DATALENGTH(st.text)
ELSE qs.statement_end_offset END
- qs.statement_start_offset)/2) + 1) not like '%fetch%'
ORDER BY total_elapsed_time / execution_count DESC;

https://learn.microsoft.com/zh-CN/troubleshoot/sql/database-engine/performance/troubleshoot-slow-running-queries

SELECT TOP 20 s.session_id,
           r.status,
           r.cpu_time,
           r.logical_reads,
           r.reads,
           r.writes,
           r.total_elapsed_time / (1000 * 60) 'Elaps M',
           SUBSTRING(st.TEXT, (r.statement_start_offset / 2) + 1,
           ((CASE r.statement_end_offset
                WHEN -1 THEN DATALENGTH(st.TEXT)
                ELSE r.statement_end_offset
            END - r.statement_start_offset) / 2) + 1) AS statement_text,
           COALESCE(QUOTENAME(DB_NAME(st.dbid)) + N'.' + QUOTENAME(OBJECT_SCHEMA_NAME(st.objectid, st.dbid)) 
           + N'.' + QUOTENAME(OBJECT_NAME(st.objectid, st.dbid)), '') AS command_text,
           r.command,
           s.login_name,
           s.host_name,
           s.program_name,
           s.last_request_end_time,
           s.login_time,
           r.open_transaction_count
FROM sys.dm_exec_sessions AS s
JOIN sys.dm_exec_requests AS r ON r.session_id = s.session_id CROSS APPLY sys.Dm_exec_sql_text(r.sql_handle) AS st
WHERE r.session_id != @@SPID
ORDER BY r.cpu_time DESC
