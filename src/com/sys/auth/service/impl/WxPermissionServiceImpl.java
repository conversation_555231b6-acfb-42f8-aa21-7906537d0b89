package com.sys.auth.service.impl;

import com.sys.auth.dao.WxTPermissionMapper;
import com.sys.auth.model.WxTPermission;
import com.sys.auth.service.WxPermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class WxPermissionServiceImpl implements WxPermissionService {

    private static Logger log = LoggerFactory.getLogger(WxPermissionServiceImpl.class);

    @Resource
    private WxTPermissionMapper permissionMapper;

    @Override
    public List<WxTPermission> selectPermissionByOwnerId(List<Long> ownerList) {
        return permissionMapper.selectPermissionByOwnerId(ownerList);
    }

    @Override
    public List<String> selectPermissionKeyByOwnerId(List<Long> ownerList) {
        List<String> permissionKeyList = new ArrayList<>();
        List<Map<String, String>> permissionListMap = permissionMapper.selectPermissionKeyByOwnerId(ownerList);
        for (Map<String, String> map : permissionListMap) {
            permissionKeyList.add(map.get("permissionKey"));
        }
        return permissionKeyList;
    }

    @Override
    public List<String> selectAllPermissionKey() {
        List<WxTPermission> wxTPermissionList = permissionMapper.selectAllPermission();
        if(CollectionUtils.isEmpty(wxTPermissionList)) {
            return new ArrayList<>();
        }
        return wxTPermissionList.stream().map(WxTPermission::getPermissionKey).collect(Collectors.toList());
    }
}
