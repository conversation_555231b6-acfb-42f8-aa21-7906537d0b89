package com.sys.quartz.business;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.common.util.DateUtil;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.push.model.WxPushMessage;
import com.sys.push.service.MessagePushService;
import com.chevron.task.business.TaskService;
import com.chevron.task.dao.TaskCompeteGoodsVoMapper;
import com.chevron.task.dao.TaskInstanceCheckVoMapper;
import com.chevron.task.dao.TaskProductInfoVoMapper;
import com.chevron.task.dao.WxTaskMainMapper;
import com.chevron.task.dao.WxTaskSubMapper;
import com.chevron.task.model.WxTaskMain;
import com.chevron.task.model.WxTaskSub;
import com.chevron.task.service.impl.TaskMainService;

/**
 * 
 * @Copyright: 本内容仅限于德勤公司内部使用，禁止转发.
 * @Author: bo.liu 2016-7-12 下午7:18:57
 * @Version: $Id$
 * @Desc: <p>
 *        </p>
 */

@Service
public class DelayTaskQuartzService{
	public final Logger log = Logger.getLogger(this.getClass());
	@Resource
	WxTaskMainMapper mwxTaskMianMapper;
	@Resource
	TaskService mtaskService;
	@Resource
	private WxAttFileMapper mattFileMapper;
	@Resource
	private TaskCompeteGoodsVoMapper mtaskCompeteGoodsVoMapper;
	@Resource
	private TaskProductInfoVoMapper mtaskProductInfoMapper;
	@Resource
	private TaskInstanceCheckVoMapper minstanceCheckVoMapper;
	@Resource
	private WxTaskSubMapper mtaskSubMapper;
	
	@Resource
	MessagePushService msgPushService;
	@SuppressWarnings("static-access")
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public  void doHandleDealyTask() {
		log.debug("------------------------------doHandleDealyTask by ljc 处理延时任务开始执行---------------------------");
		List<WxTaskMain> delayTaskMainLst = mwxTaskMianMapper.getDelayMainTask();
		Map<String,Object> reqMap = new HashMap<String, Object>();
		String taskSteps = "";//步骤检查项
		Long sourceMainId = 0L;//从哪个主任务源来的
		List<Long> userIds = new ArrayList<Long>();
		try {
				for (WxTaskMain delayTaskMain : delayTaskMainLst) {
				//0.插入主任务[开始时间，结束时间，完成天数，创建时间，修改时间，执行频率修改为单次]
					WxTaskMain taskMain = delayTaskMain;
					//获取源任务id
					sourceMainId = delayTaskMain.getTaskMainId();
					//重新生成一个对象
					taskMain.setTaskMainId(null);
					// 指定新任务的来源任务ID
					taskMain.setSourceTaskPid(sourceMainId);
					// 定时任务自动创建
					taskMain.setCreateSource(WxTaskMain.Task_createSource_Scheduler);
					String taskSourceName = taskMain.getTaskName().replaceAll("_已延期", "").trim()+"_已延期";
					String taskName = taskSourceName 
										+ DateUtil.getCurrentDate(DateUtil.DEFAULT_DATE_PATTERN);
					// 设置新任务的名字
					taskMain.setTaskName(taskName);
					
					// 计算任务结束时间 根据任务持续天数 和任务开始时间来计算 首先获取当天零时时间
					Date startTime = DateUtil.getDateToZero(new Date());
					Date finishTime = taskMain.getTaskFinishTime();
					Integer finishDay = taskMain.getFinishDay();
					if (finishDay == null) {
						finishDay = 7;//默认完成天数为一个星期
					}
					finishTime = DateUtil.addDays(startTime, finishDay);
					// 设置任务开始和结束时间
					taskMain.setTaskStartTime(startTime);
					taskMain.setTaskFinishTime(finishTime);
					// 自动创建任务执行频率都是单次
					taskMain.setFrequencyType(WxTaskMain.FrequencyType_Single);
					// 自动创建任务为已发布
					taskMain.setPublishStatus(WxTaskMain.Publish_Release);
					// 修改任务状态为提交待处理
					taskMain.setTaskStatus(WxTaskMain.TaskMainStatus_Tj);
					
					taskSteps = taskMain.getTaskSteps();//获取任务的步骤检查项信息
					mwxTaskMianMapper.insertSelective(taskMain);
					
					
				//1.插入步骤检查项关联
					String[] stepValues=null;
					if(null!=taskSteps && !taskSteps.equals(""))
					{
						stepValues = taskSteps.split(",");
					}
					long main_task_id;//新任务即目标任务id
					main_task_id = mwxTaskMianMapper.getMainIds();
					log.debug("================================doHandleDealyTask by ljc main_task_id:"+main_task_id);
					if(null!=taskSteps && !taskSteps.equals(""))
					{
						System.out.println("====================hello ljc======================");
						mtaskService.insertBatchTaskSteps(main_task_id, stepValues);
					}
					
					
				//2.插入附件相关信息
					reqMap.put("newTaskMainId", main_task_id);//目标任务id
					reqMap.put("sourceTaskMainId", sourceMainId);//源任务id
					mattFileMapper.insertWxAttFilesByQuartzByMainIdAndSouceId(reqMap);
				
				//3.插入子任务信息
					//先获取源始任务对应的门店信息
					reqMap.put("mainTaskId", sourceMainId);//源任务id
					List<WxTaskSub> oldsubLst = mtaskSubMapper.getWxTaskSubByTaskMainId(reqMap);//源子任务
					
					List<WxTaskSub> taskSublst = new ArrayList<WxTaskSub>();//新子任务
					for(int j=0;j<oldsubLst.size();j++)
					{
						Long workshop_id = oldsubLst.get(j).getOrgId();
						WxTaskSub taskSub = new WxTaskSub();
						taskSub.setTaskMainId(main_task_id);
						taskSub.setTaskStartTime(taskMain.getTaskStartTime());
						taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
						taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
						taskSub.setExecUser(taskMain.getExcuteUserId());
						taskSub.setOrgId(workshop_id);
						taskSub.setTaskRemark(taskMain.getTaskDescription());
						taskSub.setRecfityCount(0);//整改次数，默认是0
						taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
						if(null!=taskMain.getTenantId())
						{
							taskSub.setTenantId(taskMain.getTenantId());//modify by bo.liu 0818 taskMain.getTenantId()
						}
						
						taskSublst.add(taskSub);
					}
					if(taskSublst.size()==0)//如果没有门店《扫店任务情况》
					{
						WxTaskSub taskSub = new WxTaskSub();
						taskSub.setTaskMainId(main_task_id);//目标任务即新任务id
						taskSub.setTaskStartTime(taskMain.getTaskStartTime());
						taskSub.setTaskFinishTime(taskMain.getTaskFinishTime());
						taskSub.setTaskStatus(WxTaskSub.SUB_TAST_STATUS_DCL);//'0未生效，1生效待处理，2 处理中，3，待整改  4办结，
						taskSub.setExecUser(taskMain.getExcuteUserId());
						taskSub.setOrgId(0L);//设置门店不存在
						taskSub.setTaskRemark(taskMain.getTaskDescription());
						taskSub.setRecfityCount(0);//整改次数，默认是0
						taskSub.setStatus(1);//'0已撤回，1正常提交，2 暂存',
						if(null!=taskMain.getTenantId())
						{
							taskSub.setTenantId(taskMain.getTenantId());
						}
						taskSublst.add(taskSub);
					}
					mtaskSubMapper.insertWxTaskSubBatch(taskSublst);
					Long excuteUserId = taskMain.getExcuteUserId();
					if(!userIds.contains(excuteUserId))
					{
						userIds.add(excuteUserId);
					}
					
					
					
				//4.插入任务实例
					Map<String,Object> taskAboutInsertMap = new HashMap<String, Object>();
					taskAboutInsertMap.put("taskMainId", main_task_id);
					minstanceCheckVoMapper.insertSubTaskInstance(taskAboutInsertMap);
					
				
				//巡店情况下,需要检查是否录入竞品/库存订单信息
				if(!taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_LD) && !taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_SD))//modify by bo.liu 0718
					//不是录店和扫店的情况下[巡店和通用任务类型的情况]taskMain.getTmbTypeCode().equals(WxTaskMain.TASK_TYPE_XD)
				{
					//5.插入竞品信息 //插入任务对应竞品（如果有竞品收集）
					mtaskService.getStepContainCompeteGoodsOrProductsInfo(stepValues);
					if(mtaskService.isContainCompeteGoods)
					{
						mtaskCompeteGoodsVoMapper.insertTaskCompeteCoodsBatch(taskAboutInsertMap);
					}
					
				
					//6.插入库存/订单信息 //插入任务对应库存（如果有库存收集）
					if(mtaskService.isContainProductInfo)
					{
						mtaskProductInfoMapper.insertTaskProductInfoBatch(taskAboutInsertMap);
					}
				}
				
				
				//7.//需要对延期的任务进行状态更新，[设置一个延期次数1次？？？需要给以前延期的任务设置一个延期次数为1]
				Map<String,Object> updateOldTaskMap = new HashMap<String,Object>();
				updateOldTaskMap.put("maintaskstatus", WxTaskMain.MAIN_TASK_STATUS_YQ);
				updateOldTaskMap.put("xgUser", null);
				updateOldTaskMap.put("mainTaskId", sourceMainId);
				mwxTaskMianMapper.updateWxTaskMainStatus(updateOldTaskMap);
				//需要批量更新子任务状态为延期
				updateOldTaskMap.put("taskstatus", WxTaskSub.SUB_TASK_STATUS_YQ);
				mtaskSubMapper.updateBatchWxTaskSubByMainId(updateOldTaskMap);
			 }
			//需要推送消息......05.31
			/*
			log.info("---doHandleDealyTask pushMessage start--");
			msgPushService.pushMessage(WxPushMessage.MSG_TYPE_PERSONAL, TaskMainService.TASK_MSG_TITLE, TaskMainService.TASK_MSG_CONTENT_TIME_YQ, userIds);
			log.info("---doHandleDealyTask pushMessage end--");	*/
				
				
		} catch (Exception ex) {
			
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			ex.printStackTrace();
			log.info("延期任务创建失败，源任务id:" + sourceMainId + ",异常信息:"
					+ ex.getMessage());
		}

		log.debug("------------------------------doHandleDealyTask by ljc 处理延时任务执行结束---------------------------");
	}
}
