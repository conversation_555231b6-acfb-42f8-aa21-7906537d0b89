SELECT [workflow_code]
      ,[workflow_name]
      ,[handle_bean]
      ,[page_themes]
      ,[workflow_version]
      ,[end_notify]
      ,[end_notify_template]
      ,[end_email_cc]
      ,[def_todo_email_temp]
      ,[def_reject_notify_template]
      ,[sms_notify_template]
      ,[def_sms_notify_temp]
      ,[flow_status]
      ,[delete_flag]
      ,[create_user_id]
      ,[create_time]
      ,[update_user_id]
      ,[update_time]
      into wx_t_workflow
  FROM [pmpdb01].[dbo].[wx_t_workflow] where workflow_code in ('NON_LOCK_MKT')
  
  SELECT [workflow_code]
      ,[step_code]
      ,[step_name]
      ,[step_icon]
      ,[list_operate_name]
      ,[accept_alias]
      ,[operation_permission_weight]
      ,[biz_permission_weight]
      ,[reject_to_step]
      ,[reject_notify_type]
      ,[reject_email_cc]
      ,[reject_notify_template]
      ,[todo_notify_type]
      ,[todo_email_cc]
      ,[todo_notify_template]
      ,[pre_recall_notify_type]
      ,[pre_recall_email_cc]
      ,[pre_recall_email_temp]
      ,[finish_rate_type]
      ,[finish_rate]
      ,[ext_property1]
      ,[ext_property2]
      ,[ext_property3]
      ,[ext_property4]
      ,[ext_property5]
      ,[ext_property6]
      ,[ext_property7]
      ,[ext_property8]
      ,[ext_property9]
      ,[sort_numb]
      ,[reject_sms_notify_template]
      ,[todo_sms_notify_template]
      ,[pre_recall_sms_temp]
      ,[remark]
      ,[predefined_flag]
      ,[delete_flag]
      ,[create_user_id]
      ,[create_time]
      ,[history_step_name]
      into wx_t_workflow_step
  FROM [pmpdb01].[dbo].[wx_t_workflow_step] where workflow_code in ('NON_LOCK_MKT')
  
  /*SELECT [group_name]
      ,[condition_sql]
      ,[sort_numb]
      ,[group_desc]
      ,[remark]
      ,[enable_flag]
      ,[delete_flag]
      ,[create_user_id]
      ,[create_time]
      ,[update_user_id]
      ,[update_time]
      --into PP_MID.DBO.wx_t_workflow_pre_exe_group
  FROM [wx_t_workflow_pre_exe_group] where [id] > 25*/