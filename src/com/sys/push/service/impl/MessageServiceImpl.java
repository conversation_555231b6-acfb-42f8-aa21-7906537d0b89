package com.sys.push.service.impl;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.push.business.MessageBizService;
import com.sys.push.model.Message;
import com.sys.push.service.MessageService;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消息操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2017-06-02 15:38
 */
@Service
public class MessageServiceImpl implements MessageService {
	@Autowired
	private MessageBizService MessageBizService;
	
	private final static Logger log = Logger.getLogger(MessageServiceImpl.class);
	
	@Override
	public Map<String, Object> save(Message record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				MessageBizService.insert(record);
			}else{
				MessageBizService.update(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> delete(Long id) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			MessageBizService.delete(id);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
}
