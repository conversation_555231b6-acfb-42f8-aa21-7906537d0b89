--乘用车
select o.organization_name "经销商名称", (select distinct region_name from view_customer_region_sales_channel v where v.distributor_id=pe.distributor_id and v.sales_channel_name='Indirect') region,
u.ch_name "姓名", u.login_name "登录名", u.mobile_tel "手机号", 
(select substring((select ','+r.role_descript from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000))"角色" from wx_t_user u 
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where u.status=1 and 
exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id and r.ch_role_name in ('Service_Partner_BD', 'Service_Partner_Manager', 'Service_Partner_Admin'))
and o.id!=9 and o.type=1 and o.status=1
order by o.id,u.user_id

--商用油
select o.organization_name "经销商名称", (select distinct region_name from view_customer_region_sales_channel v where v.distributor_id=pe.distributor_id and v.sales_channel_name='Indirect') region,
u.ch_name "姓名", u.login_name "登录名", u.mobile_tel "手机号", 
(select substring((select ','+r.role_descript from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000))"角色" from wx_t_user u 
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where u.status=1 and 
exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id 
	where u.user_id=ur.user_id and r.ch_role_name in ('Caltex_Dealer', 'Caltex_BD'))
and o.id!=9 and o.type=1 and o.status=1
order by o.id,u.user_id