select sc.[ship_to_code],scp.sku, p.name "产品名称", scp.quota "配额", 
convert(float, scp.quota) - isnull((select sum(pol.actual_amount*p.box_capacity) from wx_t_partner_order po left join wx_t_partner_order_line pol on po.id=pol.partner_order_id
left join wx_t_product p on p.sku=pol.sku
where po.partner_sale_config_id=sc.id and pol.sku=scp.sku and (select max(a.operate_time) from wx_approval_info a where a.source_flag=2 and a.source_id=po.id and a.flow_step=1 and po.status not in (0,4)) between sc.date_from and sc.date_to), 0) 
"剩余配额"
from wx_t_partner_sale_config_product scp 
left join [PP_MID].[dbo].[mid_partner_sale_config] sc on scp.config_id=sc.id
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=sc.distributor_id
left join wx_t_product p on p.sku=scp.sku
 where scp.config_id=503 and scp.quota is not null and scp.quota!=''
