package com.sys.dic.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DicItemVoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DicItemVoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeIsNull() {
            addCriterion("dic_type_code is null");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeIsNotNull() {
            addCriterion("dic_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeEqualTo(String value) {
            addCriterion("dic_type_code =", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeNotEqualTo(String value) {
            addCriterion("dic_type_code <>", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeGreaterThan(String value) {
            addCriterion("dic_type_code >", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("dic_type_code >=", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeLessThan(String value) {
            addCriterion("dic_type_code <", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("dic_type_code <=", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeLike(String value) {
            addCriterion("dic_type_code like", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeNotLike(String value) {
            addCriterion("dic_type_code not like", value, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeIn(List<String> values) {
            addCriterion("dic_type_code in", values, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeNotIn(List<String> values) {
            addCriterion("dic_type_code not in", values, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeBetween(String value1, String value2) {
            addCriterion("dic_type_code between", value1, value2, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicTypeCodeNotBetween(String value1, String value2) {
            addCriterion("dic_type_code not between", value1, value2, "dicTypeCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeIsNull() {
            addCriterion("dic_item_code is null");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeIsNotNull() {
            addCriterion("dic_item_code is not null");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeEqualTo(String value) {
            addCriterion("dic_item_code =", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeNotEqualTo(String value) {
            addCriterion("dic_item_code <>", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeGreaterThan(String value) {
            addCriterion("dic_item_code >", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("dic_item_code >=", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeLessThan(String value) {
            addCriterion("dic_item_code <", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeLessThanOrEqualTo(String value) {
            addCriterion("dic_item_code <=", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeLike(String value) {
            addCriterion("dic_item_code like", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeNotLike(String value) {
            addCriterion("dic_item_code not like", value, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeIn(List<String> values) {
            addCriterion("dic_item_code in", values, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeNotIn(List<String> values) {
            addCriterion("dic_item_code not in", values, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeBetween(String value1, String value2) {
            addCriterion("dic_item_code between", value1, value2, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemCodeNotBetween(String value1, String value2) {
            addCriterion("dic_item_code not between", value1, value2, "dicItemCode");
            return (Criteria) this;
        }

        public Criteria andDicItemNameIsNull() {
            addCriterion("dic_item_name is null");
            return (Criteria) this;
        }

        public Criteria andDicItemNameIsNotNull() {
            addCriterion("dic_item_name is not null");
            return (Criteria) this;
        }

        public Criteria andDicItemNameEqualTo(String value) {
            addCriterion("dic_item_name =", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameNotEqualTo(String value) {
            addCriterion("dic_item_name <>", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameGreaterThan(String value) {
            addCriterion("dic_item_name >", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("dic_item_name >=", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameLessThan(String value) {
            addCriterion("dic_item_name <", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameLessThanOrEqualTo(String value) {
            addCriterion("dic_item_name <=", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameLike(String value) {
            addCriterion("dic_item_name like", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameNotLike(String value) {
            addCriterion("dic_item_name not like", value, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameIn(List<String> values) {
            addCriterion("dic_item_name in", values, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameNotIn(List<String> values) {
            addCriterion("dic_item_name not in", values, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameBetween(String value1, String value2) {
            addCriterion("dic_item_name between", value1, value2, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemNameNotBetween(String value1, String value2) {
            addCriterion("dic_item_name not between", value1, value2, "dicItemName");
            return (Criteria) this;
        }

        public Criteria andDicItemDescIsNull() {
            addCriterion("dic_item_desc is null");
            return (Criteria) this;
        }

        public Criteria andDicItemDescIsNotNull() {
            addCriterion("dic_item_desc is not null");
            return (Criteria) this;
        }

        public Criteria andDicItemDescEqualTo(String value) {
            addCriterion("dic_item_desc =", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescNotEqualTo(String value) {
            addCriterion("dic_item_desc <>", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescGreaterThan(String value) {
            addCriterion("dic_item_desc >", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescGreaterThanOrEqualTo(String value) {
            addCriterion("dic_item_desc >=", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescLessThan(String value) {
            addCriterion("dic_item_desc <", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescLessThanOrEqualTo(String value) {
            addCriterion("dic_item_desc <=", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescLike(String value) {
            addCriterion("dic_item_desc like", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescNotLike(String value) {
            addCriterion("dic_item_desc not like", value, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescIn(List<String> values) {
            addCriterion("dic_item_desc in", values, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescNotIn(List<String> values) {
            addCriterion("dic_item_desc not in", values, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescBetween(String value1, String value2) {
            addCriterion("dic_item_desc between", value1, value2, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andDicItemDescNotBetween(String value1, String value2) {
            addCriterion("dic_item_desc not between", value1, value2, "dicItemDesc");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Long value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Long value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Long value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Long value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Long value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Long value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Long> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Long> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Long value1, Long value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Long value1, Long value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNull() {
            addCriterion("updator is null");
            return (Criteria) this;
        }

        public Criteria andUpdatorIsNotNull() {
            addCriterion("updator is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatorEqualTo(Long value) {
            addCriterion("updator =", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotEqualTo(Long value) {
            addCriterion("updator <>", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThan(Long value) {
            addCriterion("updator >", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorGreaterThanOrEqualTo(Long value) {
            addCriterion("updator >=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThan(Long value) {
            addCriterion("updator <", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorLessThanOrEqualTo(Long value) {
            addCriterion("updator <=", value, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorIn(List<Long> values) {
            addCriterion("updator in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotIn(List<Long> values) {
            addCriterion("updator not in", values, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorBetween(Long value1, Long value2) {
            addCriterion("updator between", value1, value2, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdatorNotBetween(Long value1, Long value2) {
            addCriterion("updator not between", value1, value2, "updator");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}