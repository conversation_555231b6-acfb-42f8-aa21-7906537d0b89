update pcn set pcn.cap_code=(select top 1 pco.cap_code from wx_t_oem_product_packaging_code pco where pco.code1=pcn.code1 and len(pco.cap_code)>1)
from wx_t_oem_product_packaging_code pcn
where pcn.code1<>'000000000000' and (pcn.cap_code is null or len(pcn.cap_code)=0)
and not exists (select 1 from wx_t_oem_product_packaging_code pco where pco.code1=pcn.code1 and pco.id>pcn.id)
and exists (select 1 from wx_t_oem_product_packaging_code pco where pco.code1=pcn.code1 and len(pco.cap_code)>1)

delete pco
from wx_t_oem_product_packaging_code pco
where pco.code1<>'000000000000'
and exists (select 1 from wx_t_oem_product_packaging_code pcn where pco.code1=pcn.code1 and pco.id<pcn.id)

select code1, count(1) --into wx_t_oem_product_packaging_code20211231 
from wx_t_oem_product_packaging_code pcn 
where code1<>'000000000000'
group by code1 
having count(1)>1
