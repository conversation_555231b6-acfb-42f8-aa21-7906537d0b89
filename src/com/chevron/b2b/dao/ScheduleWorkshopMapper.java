package com.chevron.b2b.dao;

import com.chevron.b2b.model.ScheduleWorkshop;
import com.chevron.b2b.model.ScheduleWorkshopExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 活动分配门店-wx_t_schedule_workshop数据库操作对象
 * <AUTHOR>
 * @version 1.0 2019-01-09 22:26
 */
public interface ScheduleWorkshopMapper {

 	/**
	 * 插入活动分配门店对象(非空属性)
	 * @param record 被插入活动分配门店对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(ScheduleWorkshop record);

	/**
	 * 更新活动分配门店对象
	 * @param record 被修改活动分配门店对象
	 * @param example 过滤活动分配门店对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") ScheduleWorkshop record, @Param("example") ScheduleWorkshopExample example);

	/**
	 * 删除活动分配门店对象
	 * @param example 被删除活动分配门店对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleWorkshopExample example);
    
 	/**
	 * 查询满足条件的活动分配门店对象数量
	 * @param example 过滤活动分配门店对象条件
	 * @return 满足条件活动分配门店对象数量
	 */
    int countByExample(ScheduleWorkshopExample example);

	/**
	 * 单表查询活动分配门店对象
	 * @param example 过滤活动分配门店对象条件
	 * @return 满足条件活动分配门店对象集合
	 */
    List<ScheduleWorkshop> selectByExample(ScheduleWorkshopExample example);

	/**
	 * 列表查询活动分配门店对象
	 * @param params 查询条件
	 * @return 满足条件活动分配门店对象集合
	 */
    List<ScheduleWorkshop> queryByParams(Map<String, Object> params);

    /**
     * 批量插入活动分配门店对象
     * @param records 插入活动分配门店集合
     * @return 数据库操作影响记录条数
     */
	int insertBatch(@Param("records") List<ScheduleWorkshop> records);
}
