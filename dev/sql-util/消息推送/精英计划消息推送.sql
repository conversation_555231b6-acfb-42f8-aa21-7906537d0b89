--查看精英计划经销商老板账。排查经销商老板没有账号的情况
="union all select (select count(1) from wx_t_user u "&
"left join wx_t_organization o on o.id=u.org_id "&
"left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id "&
"where  u.status=1 and exists (select 1 from wx_t_userrole ur left join wx_t_role r on ur.role_id=r.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')) "&
"and pe.distributor_id="&D2&")"

--经销商distributor id
=D2&","

--经销商老板
select distinct o.organization_name, u.user_id, u.login_name, u.ch_name, u.mobile_tel from wx_t_user u 
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where u.status=1 and  exists (select 1 from wx_t_userrole ur left join wx_t_role r on ur.role_id=r.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer'))
and pe.distributor_id in ()

--FLS
select distinct u.user_id, u.login_name, u.ch_name, u.mobile_tel from dw_customer_org_sales cos1 
left join wx_t_user u on u.status=1 and u.cai=cos1.sales_cai
where cos1.distributor_id in ()

--ASM
select distinct u.user_id, u.login_name, u.ch_name, u.mobile_tel from dw_customer_org_sales cos1 
left join wx_t_user u on u.status=1 and u.cai=cos1.supervisor_cai
where cos1.distributor_id in ()

--ABM
select distinct u.user_id, u.login_name, u.ch_name, u.mobile_tel from wx_t_user u where u.status=1 and u.login_name='cyana'

--经销商管理员
select u.user_id from wx_t_user u
left join wx_t_userrole ur on u.user_id = ur.user_id
left join wx_t_role r on ur.role_id = r.role_id
where 
u.status = 1 and u.ext_flag!=8 and r.ch_role_name in ('CDM_DIST','Service_Partner_Manager','Caltex_Dealer')
and u.org_id in () ;

--接收人USER ID
=B2&","

--添加接收用户到消息
update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u 
where u.user_id in ()
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=33089