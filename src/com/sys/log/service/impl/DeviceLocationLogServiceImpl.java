package com.sys.log.service.impl;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.log.business.DeviceLocationLogBizService;
import com.sys.log.model.DeviceLocationLog;
import com.sys.log.service.DeviceLocationLogService;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 设备位置日志操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2017-06-08 09:29
 */
@Service
public class DeviceLocationLogServiceImpl implements DeviceLocationLogService {
	@Autowired
	private DeviceLocationLogBizService deviceLocationLogBizService;
	
	private final static Logger log = Logger.getLogger(DeviceLocationLogServiceImpl.class);
	
	@Override
	public Map<String, Object> save(DeviceLocationLog record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				deviceLocationLogBizService.insert(record);
			}else{
				deviceLocationLogBizService.update(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> delete(Long id) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			deviceLocationLogBizService.delete(id);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return map;
	}
	
}
