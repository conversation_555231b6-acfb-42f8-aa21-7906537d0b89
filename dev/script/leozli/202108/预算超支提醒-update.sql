insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('Budget2021.overspendNotify', '预算超支提醒配置', '预算超支提醒配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Budget2021.overspendNotify', 'honorific', 'Dear,', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('Budget2021.overspendNotify', 'to', '接收人', '<EMAIL>', '1', 10);

INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'mktQuartzJob', N'WorkGroup', N'1', N'0 0 9 * * ?', N'每天早上9点执行', NULL, N'1', N'mktQuartzJob', N'sendExpenseOverspendNotifyEmail', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '费用超支邮件提醒')
