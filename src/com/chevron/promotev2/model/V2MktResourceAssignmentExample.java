package com.chevron.promotev2.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 资源包分配单表查询条件
 * <AUTHOR>
 * @version 1.0 2020-02-11 11:18
 */
public class V2MktResourceAssignmentExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public V2MktResourceAssignmentExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPackageCodeIsNull() {
            addCriterion("package_code is null");
            return (Criteria) this;
        }

        public Criteria andPackageCodeIsNotNull() {
            addCriterion("package_code is not null");
            return (Criteria) this;
        }

        public Criteria andPackageCodeEqualTo(String value) {
            addCriterion("package_code =", value, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeNotEqualTo(String value) {
            addCriterion("package_code <>", value, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeGreaterThan(String value) {
            addCriterion("package_code >", value, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeGreaterThanOrEqualTo(String value) {
            addCriterion("package_code >=", value, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeLessThan(String value) {
            addCriterion("package_code <", value, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeLessThanOrEqualTo(String value) {
            addCriterion("package_code <=", value, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeIn(List<String> values) {
            addCriterion("package_code in", values, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeNotIn(List<String> values) {
            addCriterion("package_code not in", values, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeBetween(String value1, String value2) {
            addCriterion("package_code between", value1, value2, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageCodeNotBetween(String value1, String value2) {
            addCriterion("package_code not between", value1, value2, "packageCode");
            return (Criteria) this;
        }

        public Criteria andPackageNameIsNull() {
            addCriterion("package_name is null");
            return (Criteria) this;
        }

        public Criteria andPackageNameIsNotNull() {
            addCriterion("package_name is not null");
            return (Criteria) this;
        }

        public Criteria andPackageNameEqualTo(String value) {
            addCriterion("package_name =", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotEqualTo(String value) {
            addCriterion("package_name <>", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameGreaterThan(String value) {
            addCriterion("package_name >", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameGreaterThanOrEqualTo(String value) {
            addCriterion("package_name >=", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameLessThan(String value) {
            addCriterion("package_name <", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameLessThanOrEqualTo(String value) {
            addCriterion("package_name <=", value, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameIn(List<String> values) {
            addCriterion("package_name in", values, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotIn(List<String> values) {
            addCriterion("package_name not in", values, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameBetween(String value1, String value2) {
            addCriterion("package_name between", value1, value2, "packageName");
            return (Criteria) this;
        }

        public Criteria andPackageNameNotBetween(String value1, String value2) {
            addCriterion("package_name not between", value1, value2, "packageName");
            return (Criteria) this;
        }

        public Criteria andFocusAmountIsNull() {
            addCriterion("focus_amount is null");
            return (Criteria) this;
        }

        public Criteria andFocusAmountIsNotNull() {
            addCriterion("focus_amount is not null");
            return (Criteria) this;
        }

        public Criteria andFocusAmountEqualTo(Integer value) {
            addCriterion("focus_amount =", value, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountNotEqualTo(Integer value) {
            addCriterion("focus_amount <>", value, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountGreaterThan(Integer value) {
            addCriterion("focus_amount >", value, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("focus_amount >=", value, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountLessThan(Integer value) {
            addCriterion("focus_amount <", value, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountLessThanOrEqualTo(Integer value) {
            addCriterion("focus_amount <=", value, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountIn(List<Integer> values) {
            addCriterion("focus_amount in", values, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountNotIn(List<Integer> values) {
            addCriterion("focus_amount not in", values, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountBetween(Integer value1, Integer value2) {
            addCriterion("focus_amount between", value1, value2, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andFocusAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("focus_amount not between", value1, value2, "focusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountIsNull() {
            addCriterion("non_focus_amount is null");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountIsNotNull() {
            addCriterion("non_focus_amount is not null");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountEqualTo(Integer value) {
            addCriterion("non_focus_amount =", value, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountNotEqualTo(Integer value) {
            addCriterion("non_focus_amount <>", value, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountGreaterThan(Integer value) {
            addCriterion("non_focus_amount >", value, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("non_focus_amount >=", value, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountLessThan(Integer value) {
            addCriterion("non_focus_amount <", value, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountLessThanOrEqualTo(Integer value) {
            addCriterion("non_focus_amount <=", value, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountIn(List<Integer> values) {
            addCriterion("non_focus_amount in", values, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountNotIn(List<Integer> values) {
            addCriterion("non_focus_amount not in", values, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountBetween(Integer value1, Integer value2) {
            addCriterion("non_focus_amount between", value1, value2, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andNonFocusAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("non_focus_amount not between", value1, value2, "nonFocusAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountIsNull() {
            addCriterion("all_amount is null");
            return (Criteria) this;
        }

        public Criteria andAllAmountIsNotNull() {
            addCriterion("all_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAllAmountEqualTo(Integer value) {
            addCriterion("all_amount =", value, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountNotEqualTo(Integer value) {
            addCriterion("all_amount <>", value, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountGreaterThan(Integer value) {
            addCriterion("all_amount >", value, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("all_amount >=", value, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountLessThan(Integer value) {
            addCriterion("all_amount <", value, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountLessThanOrEqualTo(Integer value) {
            addCriterion("all_amount <=", value, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountIn(List<Integer> values) {
            addCriterion("all_amount in", values, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountNotIn(List<Integer> values) {
            addCriterion("all_amount not in", values, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountBetween(Integer value1, Integer value2) {
            addCriterion("all_amount between", value1, value2, "allAmount");
            return (Criteria) this;
        }

        public Criteria andAllAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("all_amount not between", value1, value2, "allAmount");
            return (Criteria) this;
        }

        public Criteria andOperateCaiIsNull() {
            addCriterion("operate_cai is null");
            return (Criteria) this;
        }

        public Criteria andOperateCaiIsNotNull() {
            addCriterion("operate_cai is not null");
            return (Criteria) this;
        }

        public Criteria andOperateCaiEqualTo(String value) {
            addCriterion("operate_cai =", value, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiNotEqualTo(String value) {
            addCriterion("operate_cai <>", value, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiGreaterThan(String value) {
            addCriterion("operate_cai >", value, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiGreaterThanOrEqualTo(String value) {
            addCriterion("operate_cai >=", value, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiLessThan(String value) {
            addCriterion("operate_cai <", value, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiLessThanOrEqualTo(String value) {
            addCriterion("operate_cai <=", value, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiIn(List<String> values) {
            addCriterion("operate_cai in", values, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiNotIn(List<String> values) {
            addCriterion("operate_cai not in", values, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiBetween(String value1, String value2) {
            addCriterion("operate_cai between", value1, value2, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateCaiNotBetween(String value1, String value2) {
            addCriterion("operate_cai not between", value1, value2, "operateCai");
            return (Criteria) this;
        }

        public Criteria andOperateNameIsNull() {
            addCriterion("operate_name is null");
            return (Criteria) this;
        }

        public Criteria andOperateNameIsNotNull() {
            addCriterion("operate_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperateNameEqualTo(String value) {
            addCriterion("operate_name =", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotEqualTo(String value) {
            addCriterion("operate_name <>", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameGreaterThan(String value) {
            addCriterion("operate_name >", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameGreaterThanOrEqualTo(String value) {
            addCriterion("operate_name >=", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameLessThan(String value) {
            addCriterion("operate_name <", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameLessThanOrEqualTo(String value) {
            addCriterion("operate_name <=", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameIn(List<String> values) {
            addCriterion("operate_name in", values, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotIn(List<String> values) {
            addCriterion("operate_name not in", values, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameBetween(String value1, String value2) {
            addCriterion("operate_name between", value1, value2, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotBetween(String value1, String value2) {
            addCriterion("operate_name not between", value1, value2, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateLevelIsNull() {
            addCriterion("operate_level is null");
            return (Criteria) this;
        }

        public Criteria andOperateLevelIsNotNull() {
            addCriterion("operate_level is not null");
            return (Criteria) this;
        }

        public Criteria andOperateLevelEqualTo(String value) {
            addCriterion("operate_level =", value, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelNotEqualTo(String value) {
            addCriterion("operate_level <>", value, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelGreaterThan(String value) {
            addCriterion("operate_level >", value, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelGreaterThanOrEqualTo(String value) {
            addCriterion("operate_level >=", value, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelLessThan(String value) {
            addCriterion("operate_level <", value, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelLessThanOrEqualTo(String value) {
            addCriterion("operate_level <=", value, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelIn(List<String> values) {
            addCriterion("operate_level in", values, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelNotIn(List<String> values) {
            addCriterion("operate_level not in", values, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelBetween(String value1, String value2) {
            addCriterion("operate_level between", value1, value2, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andOperateLevelNotBetween(String value1, String value2) {
            addCriterion("operate_level not between", value1, value2, "operateLevel");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
