package com.sys.auth.service.impl;

import com.sys.auth.dao.WxTOwnerpermissionMapper;
import com.sys.auth.model.WxTOwnerpermission;
import com.sys.auth.service.WxOwnerPermissionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WxOwnerPermissionServiceImpl implements WxOwnerPermissionService {

    @Resource
    private WxTOwnerpermissionMapper wxTOwnerpermissionMapper;

    @Override
    public List<WxTOwnerpermission> selectOwnerPermissionByUserId(Long userId) {
        return wxTOwnerpermissionMapper.selectPermissionByUserId(userId);
    }

    @Override
    public List<WxTOwnerpermission> selectOwnerPermissionByRoleId(Long roleId) {
        return wxTOwnerpermissionMapper.selectPermissionByRoleId(roleId);
    }

    @Override
    public List<WxTOwnerpermission> selectOwnerPermissionByOwnerId(Long ownerId) {
        return wxTOwnerpermissionMapper.selectPermissionByOwnerId(ownerId);
    }
}
