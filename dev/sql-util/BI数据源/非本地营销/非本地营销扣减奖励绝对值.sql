select nld.id/*顺延ID*/, nld.punish_sku/*顺延扣减产品编码*/, 
nff.from_distributor_id/*处罚经销商*/,npr.channel/*扣减渠道，1-金富力，2-德乐*/,npr.attribute3/*扣减年份*/,
isnull((select max(ol1.update_time) from wx_t_operation_log ol1 where ol1.business_key=nld.id and ol1.business_type='non_local_delay_deduction' and ol1.ext_property3='abm_submit'),nld.update_time) effective_time/*生效时间*/,
nld.abm_punish_flag/*16-扣除年终奖励,32-扣除季度奖励,64-扣除进货量（实际值从升数扣减表取）*/,
case when nld.abm_punish_flag&32>0 then nld.abm_punish_quarter_value else null end abm_punish_quarter_value/*ABM扣除季奖[quarter,deductBonus（奖励绝对值）;quarter,deductBonus]*/,
case when nld.abm_punish_flag&16>0 then nld.abm_punish_annual_value else null end abm_punish_annual_value/*Abm扣除年终奖绝对值*/,
dbo.[fun_get_non_local_flow_no](npr.flow_id) flow_no/*流程ID（一个流程ID可有多个顺延扣减）*/
from wx_t_non_local_delay_deduction nld 
left join wx_t_non_local_mkt_flow_pro_res npr on nld.punish_id=npr.id
left join wx_t_non_local_mkt_flow_form nff on nff.id=npr.flow_id
where nld.delete_flag=0/*未删除*/ and nld.delay_status=40/*顺延状态。10-待执行，20-执行中，25-变更中，30-已完成，40-已变更，50-已终止，60-已失效*/
and nld.abm_punish_flag&48>0/*有季奖或年奖绝对值扣减*/