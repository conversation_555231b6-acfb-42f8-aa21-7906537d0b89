/** */
package com.common.exception;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * 
 *
 */
public class WxPltException extends Exception {

	/** */
	private static final long serialVersionUID = 1L;
	/**
	 * 异常代码
	 */
	private String expCode = "";
	/**
	 * 异常信息
	 */
	private String expMsg = "";
	/**
	 * 原生异常信息
	 */
	private Exception exception = null;

	public Exception getException() {
		return exception;
	}

	public void setException(Exception exception) {
		this.exception = exception;
	}

	/**
	 * @return the expMsg get 异常信息
	 */
	public String getExpMsgValue() {

		return expMsg;
	}

	/**
	 * @return the expMsg get 异常信息
	 */
	public String getExpMsg() {
		return expMsg;
	}

	/**
	 * @param expMsg the expMsg to set set 异常信息
	 */
	public WxPltException setExpMsg(String expMsg) {
		this.expMsg = expMsg;
		return this;
	}

	/**
	 * @param expCode the expCode to set set 异常代码
	 */
	public WxPltException setExpCode(String expCode) {
		this.expCode = expCode;

		return this;
	}

	/**
	 * @return the expCode get 异常代码
	 */
	public String getExpCode() {
		return expCode;
	}

	/** */
	public WxPltException() {
	}

	/** */
	public WxPltException(Exception exception) {
		this.exception = exception;
	}

	/**
	 * @param message
	 */
	public WxPltException(String message) {
		super(message);
		setExpMsg(message);
	}

	public WxPltException(String expCode, String expMsg) {
		super(expMsg);
		this.expCode = expCode;
		this.expMsg = expMsg;
	}

	/**
	 * @param cause
	 */
	public WxPltException(Throwable cause) {
		super(cause);
	}

	/**
	 * @param message
	 * @param cause
	 */
	public WxPltException(String message, Throwable cause) {
		super(message, cause);
	}

	public String getPrintStackTrace() {
		try {
			StringWriter sw = new StringWriter();
			PrintWriter pw = new PrintWriter(sw);
			if (getException() != null) {
				this.getException().printStackTrace(pw);
				return "\r\n" + sw.toString() + "\r\n";
			}
			return "";

		} catch (Exception e) {
			return "获取异常信息失败！";
		}
	}

	public void PrintStackTrace() {
		if (getException() != null) {
			this.getException().printStackTrace();
		}
	}

}
