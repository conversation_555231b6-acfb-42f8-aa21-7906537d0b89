select * from dw_mid_cal_elite_fund where distributor_id=284

select *
into [dw_mid_cal_elite_fund]  
from PP_MID.dbo.[mid_cal_elite_fund] f

truncate table [dw_mid_cal_elite_fund]
insert into [dw_mid_cal_elite_fund] ([year], [quarter], [distributor_id], [customer_name_cn], [region], [total_fund], [commercial_bd_fund], [commercial_marketing_fund], [commercial_ivi_fund], [commercial_bd_fund_adjust], [commercial_marketing_fund_adjust], [commercial_ivi_fund_adjust], [consumer_bd_fund], [consumer_marketing_fund], [consumer_ivi_fund], [consumer_bd_fund_adjust], [consumer_marketing_fund_adjust], [consumer_ivi_fund_adjust], [consumer_annual_reward], [create_time]) 
select [year], [quarter], [distributor_id], [customer_name_cn], [region], [total_fund], [commercial_bd_fund], [commercial_marketing_fund], [commercial_ivi_fund], [commercial_bd_fund_adjust], [commercial_marketing_fund_adjust], [commercial_ivi_fund_adjust], [consumer_bd_fund], [consumer_marketing_fund], [consumer_ivi_fund], [consumer_bd_fund_adjust], [consumer_marketing_fund_adjust], [consumer_ivi_fund_adjust], [consumer_annual_reward], [create_time]
from PP_MID.dbo.[mid_cal_elite_fund] f

--select * 
update  f set f.[total_fund] = [total_fund]*1.13,
f.[commercial_bd_fund] = [commercial_bd_fund]*1.13,
[commercial_marketing_fund]=[commercial_marketing_fund]*1.13,
[commercial_ivi_fund]=[commercial_ivi_fund]*1.13,
[commercial_bd_fund_adjust]=[commercial_bd_fund_adjust]*1.13,
[commercial_marketing_fund_adjust]=[commercial_marketing_fund_adjust]*1.13,
[commercial_ivi_fund_adjust]=[commercial_ivi_fund_adjust]*1.13,
[consumer_bd_fund]=[consumer_bd_fund]*1.13,
[consumer_marketing_fund]=[consumer_marketing_fund]*1.13,
[consumer_ivi_fund]=[consumer_ivi_fund]*1.13,
[consumer_bd_fund_adjust]=[consumer_bd_fund_adjust]*1.13,
[consumer_marketing_fund_adjust]=[consumer_marketing_fund_adjust]*1.13,
[consumer_ivi_fund_adjust]=[consumer_ivi_fund_adjust]*1.13,
[consumer_annual_reward]=[consumer_annual_reward]*1.13
from [dw_mid_cal_elite_fund] f --where f.remark is not null

select [total_fund]*1.13,
[commercial_bd_fund]*1.13,
[commercial_marketing_fund]*1.13,
[commercial_ivi_fund]*1.13,
[commercial_bd_fund_adjust]*1.13,
[commercial_marketing_fund_adjust]*1.13,
[commercial_ivi_fund_adjust]*1.13,
[consumer_bd_fund]*1.13,
[consumer_marketing_fund]*1.13,
[consumer_ivi_fund]*1.13,
[consumer_bd_fund_adjust]*1.13,
[consumer_marketing_fund_adjust]*1.13,
[consumer_ivi_fund_adjust]*1.13,
[consumer_annual_reward]*1.13
into [dw_mid_cal_elite_fund]  
from PP_MID.dbo.[mid_cal_elite_fund] f

insert into [dw_mid_cal_elite_fund] ([year], [quarter], [distributor_id], [customer_name_cn], [region], [total_fund], [commercial_bd_fund], [commercial_marketing_fund], [commercial_ivi_fund], [commercial_bd_fund_adjust], [commercial_marketing_fund_adjust], [commercial_ivi_fund_adjust], [consumer_bd_fund], [consumer_marketing_fund], [consumer_ivi_fund], [consumer_bd_fund_adjust], [consumer_marketing_fund_adjust], [consumer_ivi_fund_adjust], [consumer_annual_reward], [create_time],[remark]) 
select [year], [quarter], [distributor_id], [customer_name_cn], [region], [total_fund], [commercial_bd_fund], [commercial_marketing_fund], [commercial_ivi_fund], [commercial_bd_fund_adjust], [commercial_marketing_fund_adjust], [commercial_ivi_fund_adjust], [consumer_bd_fund], [consumer_marketing_fund], [consumer_ivi_fund], [consumer_bd_fund_adjust], [consumer_marketing_fund_adjust], [consumer_ivi_fund_adjust], [consumer_annual_reward], [create_time],[remark]
from PP_MID.dbo.[mid_cal_elite_fund] f where f.remark is not null

select f.quarter, f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
sum([commercial_marketing_fund]+commercial_marketing_fund_adjust) [commercial_marketing_fund],
sum([commercial_ivi_fund] + commercial_ivi_fund_adjust) [commercial_ivi_fund],
sum([consumer_marketing_fund]+consumer_marketing_fund_adjust) [consumer_marketing_fund],
sum([consumer_ivi_fund]+[consumer_ivi_fund_adjust]) [consumer_ivi_fund],
sum([consumer_annual_reward]) [consumer_annual_reward]
from dw_mid_cal_elite_fund f --where f.remark is not null
group by f.distributor_id,f.quarter
order by f.quarter


select * from (
select a.quarter, a.distributor_id, a.customer_name_cn, 
b.commercial_marketing_fund-a.commercial_marketing_fund commercial_marketing_fund,
b.commercial_ivi_fund-a.commercial_ivi_fund commercial_ivi_fund,
b.consumer_marketing_fund-a.consumer_marketing_fund consumer_marketing_fund, 
b.consumer_ivi_fund-a.consumer_ivi_fund consumer_ivi_fund from (
select f.quarter, f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
sum([commercial_marketing_fund]+commercial_marketing_fund_adjust) [commercial_marketing_fund],
sum([commercial_ivi_fund] + commercial_ivi_fund_adjust) [commercial_ivi_fund],
sum([consumer_marketing_fund]+consumer_marketing_fund_adjust) [consumer_marketing_fund],
sum([consumer_ivi_fund]+[consumer_ivi_fund_adjust]) [consumer_ivi_fund],
sum([consumer_annual_reward]) [consumer_annual_reward]
from dw_mid_cal_elite_fund f --where f.remark is not null
group by f.distributor_id,f.quarter) a 
left join (select f.quarter, f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
sum([commercial_marketing_fund]+commercial_marketing_fund_adjust) [commercial_marketing_fund],
sum([commercial_ivi_fund] + commercial_ivi_fund_adjust) [commercial_ivi_fund],
sum([consumer_marketing_fund]+consumer_marketing_fund_adjust) [consumer_marketing_fund],
sum([consumer_ivi_fund]+[consumer_ivi_fund_adjust]) [consumer_ivi_fund],
sum([consumer_annual_reward]) [consumer_annual_reward]
from pmpdb0724.dbo.dw_mid_cal_elite_fund f --where f.remark is not null
group by f.distributor_id,f.quarter) b on a.distributor_id=b.distributor_id and a.quarter=b.quarter
union all
select f.quarter, f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
sum([commercial_marketing_fund]+commercial_marketing_fund_adjust) [commercial_marketing_fund],
sum([commercial_ivi_fund] + commercial_ivi_fund_adjust) [commercial_ivi_fund],
sum([consumer_marketing_fund]+consumer_marketing_fund_adjust) [consumer_marketing_fund],
sum([consumer_ivi_fund]+[consumer_ivi_fund_adjust]) [consumer_ivi_fund]
from pmpdb0724.dbo.dw_mid_cal_elite_fund f where not exists (select 1 from dw_mid_cal_elite_fund f1 where f1.distributor_id=f.distributor_id
and f.quarter=f1.quarter)
group by f.distributor_id,f.quarter) x
order by x.quarter

update  f set f.[total_fund] = round([total_fund],2),
f.[commercial_bd_fund] = round([commercial_bd_fund],2),
[commercial_marketing_fund]=round([commercial_marketing_fund],2),
[commercial_ivi_fund]=round([commercial_ivi_fund],2),
[commercial_bd_fund_adjust]=round([commercial_bd_fund_adjust],2),
[commercial_marketing_fund_adjust]=round([commercial_marketing_fund_adjust],2),
[commercial_ivi_fund_adjust]=round([commercial_ivi_fund_adjust],2),
[consumer_bd_fund]=round([consumer_bd_fund],2),
[consumer_marketing_fund]=round([consumer_marketing_fund],2),
[consumer_ivi_fund]=round([consumer_ivi_fund],2),
[consumer_bd_fund_adjust]=round([consumer_bd_fund_adjust],2),
[consumer_marketing_fund_adjust]=round([consumer_marketing_fund_adjust],2),
[consumer_ivi_fund_adjust]=round([consumer_ivi_fund_adjust],2),
[consumer_annual_reward]=round([consumer_annual_reward],2)
from [dw_mid_cal_elite_fund] f --where f.remark is not null

insert into [dw_mid_cal_elite_fund] ([year]
      ,[quarter]
      ,[distributor_id]
      ,[customer_name_cn]
      ,[region]
      ,[total_fund]
      ,[commercial_bd_fund]
      ,[commercial_marketing_fund]
      ,[commercial_ivi_fund]
      ,[commercial_bd_fund_adjust]
      ,[commercial_marketing_fund_adjust]
      ,[commercial_ivi_fund_adjust]
      ,[consumer_bd_fund]
      ,[consumer_marketing_fund]
      ,[consumer_ivi_fund]
      ,[consumer_bd_fund_adjust]
      ,[consumer_marketing_fund_adjust]
      ,[consumer_ivi_fund_adjust]
      ,[consumer_annual_reward]
      ,[create_time])
select  [year], 'Q3' [quarter]
      ,[distributor_id]
      ,[customer_name_cn]
      ,[region]
      ,round([total_fund]*1.13,2)
      ,round([commercial_bd_fund]*1.13,2)
      ,round([commercial_marketing_fund]*1.13,2)
      ,round([commercial_ivi_fund]*1.13,2)
      ,round([commercial_bd_fund_adjust]*1.13,2)
      ,round([commercial_marketing_fund_adjust]*1.13,2)
      ,round([commercial_ivi_fund_adjust]*1.13,2)
      ,round([consumer_bd_fund]*1.13,2)
      ,round([consumer_marketing_fund]*1.13,2)
      ,round([consumer_ivi_fund]*1.13,2)
      ,round([consumer_bd_fund_adjust]*1.13,2)
      ,round([consumer_marketing_fund_adjust]*1.13,2)
      ,round([consumer_ivi_fund_adjust]*1.13,2)
      ,round([consumer_annual_reward]*1.13,2)
      ,[create_time]  
      from PP_MID.dbo.[mid_cal_elite_fund] where [quarter]='Q3'
