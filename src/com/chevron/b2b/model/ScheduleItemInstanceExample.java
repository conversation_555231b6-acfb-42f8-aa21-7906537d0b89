package com.chevron.b2b.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 计划事项实例单表查询条件
 * <AUTHOR>
 * @version 1.0 2018-12-18 15:32
 */
public class ScheduleItemInstanceExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ScheduleItemInstanceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdIsNull() {
            addCriterion("schedule_item_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdIsNotNull() {
            addCriterion("schedule_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdEqualTo(Long value) {
            addCriterion("schedule_item_id =", value, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdNotEqualTo(Long value) {
            addCriterion("schedule_item_id <>", value, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdGreaterThan(Long value) {
            addCriterion("schedule_item_id >", value, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_item_id >=", value, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdLessThan(Long value) {
            addCriterion("schedule_item_id <", value, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_item_id <=", value, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdIn(List<Long> values) {
            addCriterion("schedule_item_id in", values, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdNotIn(List<Long> values) {
            addCriterion("schedule_item_id not in", values, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdBetween(Long value1, Long value2) {
            addCriterion("schedule_item_id between", value1, value2, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleItemIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_item_id not between", value1, value2, "scheduleItemId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdIsNull() {
            addCriterion("schedule_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdIsNotNull() {
            addCriterion("schedule_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdEqualTo(Long value) {
            addCriterion("schedule_instance_id =", value, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdNotEqualTo(Long value) {
            addCriterion("schedule_instance_id <>", value, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdGreaterThan(Long value) {
            addCriterion("schedule_instance_id >", value, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_instance_id >=", value, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdLessThan(Long value) {
            addCriterion("schedule_instance_id <", value, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_instance_id <=", value, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdIn(List<Long> values) {
            addCriterion("schedule_instance_id in", values, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdNotIn(List<Long> values) {
            addCriterion("schedule_instance_id not in", values, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdBetween(Long value1, Long value2) {
            addCriterion("schedule_instance_id between", value1, value2, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andScheduleInstanceIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_instance_id not between", value1, value2, "scheduleInstanceId");
            return (Criteria) this;
        }

        public Criteria andInputValueIsNull() {
            addCriterion("input_value is null");
            return (Criteria) this;
        }

        public Criteria andInputValueIsNotNull() {
            addCriterion("input_value is not null");
            return (Criteria) this;
        }

        public Criteria andInputValueEqualTo(String value) {
            addCriterion("input_value =", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotEqualTo(String value) {
            addCriterion("input_value <>", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueGreaterThan(String value) {
            addCriterion("input_value >", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueGreaterThanOrEqualTo(String value) {
            addCriterion("input_value >=", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLessThan(String value) {
            addCriterion("input_value <", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueLessThanOrEqualTo(String value) {
            addCriterion("input_value <=", value, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueIn(List<String> values) {
            addCriterion("input_value in", values, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotIn(List<String> values) {
            addCriterion("input_value not in", values, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueBetween(String value1, String value2) {
            addCriterion("input_value between", value1, value2, "inputValue");
            return (Criteria) this;
        }

        public Criteria andInputValueNotBetween(String value1, String value2) {
            addCriterion("input_value not between", value1, value2, "inputValue");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
