package com.common.util;

import freemarker.template.Configuration;
import freemarker.template.Template;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.StringWriter;
import java.util.Locale;
import java.util.Map;
import javax.servlet.ServletContext;

import com.common.config.MyPropertyConfigurer;
/**
 * 
 * @Author: bo.liu  2016-12-6 上午11:11:56 
 * @Version: $Id$
 * @Desc: <p></p>
 */
public class EmailHtmlRender {
	private static Configuration freemarkerCfg = null;
	private static final String ftldir = MyPropertyConfigurer.getVal("mail.templates.addr");
	private static final String datetime_format = MyPropertyConfigurer.getVal("mail.datetime.format");
	private static final String charset = MyPropertyConfigurer.getVal("mail.charset");

	public static EmailHtmlRender getInstance(ServletContext sc) {
		return new EmailHtmlRender(sc);
	}

	public EmailHtmlRender(ServletContext sc) {
		if (freemarkerCfg == null) {
			freemarkerCfg = new Configuration();
			freemarkerCfg.setServletContextForTemplateLoading(sc,
					ftldir);
			freemarkerCfg.setEncoding(Locale.getDefault(), charset);
			try {
				freemarkerCfg.setSetting("datetime_format", datetime_format);
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
	}

	public static void createHTML(Map<String, Object> data,
			String templatePath, String htmlPath) {
		try {
			Template template = freemarkerCfg
					.getTemplate(templatePath, charset);
			template.setEncoding(charset);

			File htmlFile = new File(htmlPath);
			BufferedWriter out = new BufferedWriter(new OutputStreamWriter(
					new FileOutputStream(htmlFile), charset));

			template.process(data, out);
			out.flush();
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static String crateHtmlString(Map<String, Object> data,
			String templatePath) {
		String emialContent = null;
		try {
			Template template = freemarkerCfg
					.getTemplate(templatePath, charset);
			template.setEncoding(charset);

			StringWriter stringWriter = new StringWriter();
			BufferedWriter bufferedWriter = new BufferedWriter(stringWriter);
			template.process(data, bufferedWriter);
			bufferedWriter.flush();
			emialContent = stringWriter.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return emialContent;
	}
}
