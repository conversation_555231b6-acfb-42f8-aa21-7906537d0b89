package com.chevron.task.dao;

import com.chevron.pms.model.TaskParams;
import com.chevron.task.model.SubtaskParams;
import com.chevron.task.model.app.*;
import com.sys.auth.model.WxTUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface AppTaskDao {

	public List<SubTask> querySubTask(Map<String, Object> paramMap);

	public List<SubTask> querySubTaskForPage(SubtaskParams params);
	
	public List<SubTask> queryPendingVisitSubTaskForPage(SubtaskParams params);
	
	public String queryWorkShopLastTaskFeedback(@Param("workShopId")Long workShopId, @Param("subTaskId")Long subTaskId);

	public String queryWorkShopLastTaskCheckResult(@Param("workShopId")Long workShopId, @Param("tmbTypeCode") String tmbTypeCode);
	
	public SubTask querySubTaskBySubTaskId(@Param("subTaskId")Long subTaskId);
	
	
	List<MyXDTask> queryMyXDTaskList(Map<String,Object> paramMap);
	Long countMyXDTaskList(Map<String,Object> paramMap);
	
	
	List<MySDTask> queryMySDTaskList(Map<String,Object> paramMap);
	Long countMySDTaskList(Map<String,Object> paramMap);
	
	List<MyGDTask> queryMyGDTaskList(Map<String,Object> paramMap);
	Long countMyGDTaskList(Map<String,Object> paramMap);
	
	List<MySITask> queryMySITaskList(Map<String,Object> paramMap);
	Long countMySITaskList(Map<String,Object> paramMap);

	public List<SubTask> querySubTaskByParams(Map<String, Object> paramMap);
	
	List<MySDTask> queryForPage(TaskParams params);
	
	List<MySDTask> queryShopOrFleetForPage(TaskParams params);
	
	//有效拜访门店的具体日期
	List<String> getEffectiveVisitingDays(Map<String,Object> paramMap);
	
	//当月有效拜访门数量店查询
	Long getVisitingDaysNum(Map<String,Object> paramMap);
	
	//新门店客户数量查询
	List<TaskKPI> getNewWorkShopNum(Map<String,Object> paramMap);
	
	//业绩证明查询
	TaskKPI getProofOfPerformance(Map<String,Object> paramMap);
	
	//分页查询门店/车队信息
	List<ShopOrFleetParam> getShopOrFleetParamByPage(ShopOrFleetParam param);

	long totalShopOrFleetParamForPage(ShopOrFleetParam param);

	List<String> getAvailableRoutes(@Param("loginName") String loginName);

	List<WxTUser> getAsmUser(Map<String,Object> paramMap);
	
	String getRegionName(Long userId);
}
