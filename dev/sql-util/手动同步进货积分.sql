select top 1000 * from wx_t_point_pending_record order by id desc;
select *
--update l set l.COMMENTS='BI系统积分计算 01-03月'
from wx_t_point_value_detail_log l 
where exists (select 1 from wx_t_point_value_detail d
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID  where pb.id=6191
 and l.POINT_VALUE_ID=d.id)
 
 select *
--update d set d.COMMENTS='BI系统积分计算 01-03月'
from wx_t_point_value_detail d 
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID where pb.id=6191;