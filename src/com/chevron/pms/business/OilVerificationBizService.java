package com.chevron.pms.business;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportColAlign;
import com.chevron.exportdata.PoiWriteExcel;
import com.chevron.importdata.ImportDataUtil;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterExample;
import com.chevron.pms.dao.MechanicQrcodeMapper;
import com.chevron.pms.dao.OilVerificationMapper;
import com.chevron.pms.dao.VerificationBatchMapper;
import com.chevron.pms.dao.VerificationDetailMapper;
import com.chevron.pms.dao.VerificationDetailUserMapper;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.MechanicQrcode;
import com.chevron.pms.model.MechanicQrcodeExample;
import com.chevron.pms.model.MechanicVerificationPay;
import com.chevron.pms.model.OilVerification;
import com.chevron.pms.model.OilVerificationImportVo;
import com.chevron.pms.model.OilVerificationParams;
import com.chevron.pms.model.OilVerificationReportView;
import com.chevron.pms.model.VerificationBatch;
import com.chevron.pms.model.VerificationDetail;
import com.chevron.pms.model.VerificationDetailUser;
import com.chevron.pms.model.VerificationRule;
import com.chevron.pms.model.WorkShopVoExample;
import com.chevron.pms.model.WorkshopDistributionRule;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopEmployeeExample;
import com.chevron.pms.model.WorkshopOilVerificationSummary;
import com.chevron.pms.model.WsVerifOutStockParams;
import com.chevron.pms.service.InventoryService;
import com.chevron.pms.service.QrCodeService;
import com.chevron.pms.service.WorkshopPartnerService;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.EmailSendUtils;
import com.common.util.MessageResourceUtil;
import com.common.util.PermissionParamsMap;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogUtils;

@Component
public class OilVerificationBizService {

	private static final String P_DATE_TO = "dateTo";

	private static final String P_DATE_FROM = "dateFrom";

	private static final String P_WORKSHOP_ID = "workshopId";

	@Autowired
	private OilVerificationMapper oilVerificationMapper;

	@Autowired
	private VerificationBatchMapper verificationBatchMapper;

	@Autowired
	private VerificationDetailMapper verificationDetailMapper;

	@Autowired
	private VerificationDetailUserMapper verificationDetailUserMapper;

	@Autowired
	private WorkshopEmployeeMapper workshopEmployeeMapper;

	@Autowired
	private MechanicQrcodeMapper mechanicQrcodeMapper;

	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;

	@Autowired
	private VerificationRuleBizService verificationRuleBizService;

	@Autowired
	private WorkshopPartnerService workshopPartnerService;

	@Autowired
	private WorkshopEmployeeBizService workshopEmployeeBizService;

	@Autowired
	private WorkshopDistributionRuleBizService workshopDistributionRuleBizService;

	@Autowired
	private MechanicVerificationPayBizService mechanicVerificationPayBizService;

	@Autowired
	private MechanicQrcodeBizService mechanicQrcodeBizService;

	@Autowired
	private QrCodeService qrCodeService;

	@Autowired
	private InventoryService inventoryService;

	@Resource
	private DicService dicServiceImpl;

	private static Logger logger = LoggerFactory.getLogger(OilVerificationBizService.class);

	public WorkshopOilVerificationSummary getOilVerificationSummary(Long workshopId, Date dateFrom, Date dateTo){
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);

		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put(P_WORKSHOP_ID, String.valueOf(workshopId));
		paramMap.put(P_DATE_FROM, newDateFrom);
		paramMap.put(P_DATE_TO, newDateTo);

		List<WorkshopOilVerificationSummary> summaryList = queryOilVerificationSummaryList(paramMap);
		WorkshopOilVerificationSummary summary = new WorkshopOilVerificationSummary();
		if (summaryList != null && summaryList.size() == 1){
			summary = summaryList.get(0);
		}
		return summary;
	}
	
	/**
	 * 发送上一个月核销明细
	 */
	@SuppressWarnings("unchecked")
	public void sendLastMonthVerificationDetail(){
		try {
			//获取邮件发送配置
			String[] to = null;
			String[] cc = null;
			String honorific = null;
			Map<String, Object> dicMap = dicServiceImpl.getDicItemByDicTypeCode("task.monthemail.scandetial");
			if("success".equals(dicMap.get("result"))){
				List<DicItemVo> list = (List<DicItemVo>) dicMap.get("data");
				if(list != null && !list.isEmpty()){
					for(DicItemVo vo : list){
						if("to".equals(vo.getDicItemCode())){
							to = vo.getDicItemDesc().split(",");
							honorific = vo.getDicItemName();
						}else if("cc".equals(vo.getDicItemCode())){
							cc = vo.getDicItemDesc().split(",");
						}
					}
				}
				if(to == null){
					throw new WxPltException("数据字典中未配置接收人信息");
				}
			}else{
				throw new WxPltException((String) dicMap.get("errorMsg"));
			}

			WebApplicationContext webApplicationContext = ContextLoader
					.getCurrentWebApplicationContext();
			ServletContext servletContext = webApplicationContext
					.getServletContext();
			Map<String, Object> params = new HashMap<String, Object>();
			Date today = DateUtil.getDateToZero(DateUtil.getCurrentDate());
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(today);
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			params.put("dateTo3", calendar.getTime());
			calendar.add(Calendar.MONTH, -1);
			Date fromDate = calendar.getTime();
			params.put("dateFrom3", fromDate);
			File excelFile = OilVerificationBizService.createVerificationDetailExcel(oilVerificationMapper.queryDetailForDailyReportEmail(params), 
					DateUtil.getDateStr(fromDate, "yyyy年M月") + "扫码明细", servletContext);
			//发送周报邮件
			Map<String, Object> dataMap = new HashMap<String, Object>();
			dataMap.put("honorific", honorific);
			EmailSendUtils.sendEmailForListContent(servletContext, to, cc, "上一个月扫码明细", dataMap,
					new File[]{excelFile}, "scan_detail_month_email.ftl");
			logger.info("sendLastMonthVerificationDetail end=============================");
			LogUtils.addInfoLog(1l, "OilVerificationBizService.sendLastMonthVerificationDetail", "上一月扫码明细发送成功");
		} catch (Exception e) {
			logger.error("sendLastMonthVerificationDetail error. " + e.getMessage(), e);
			LogUtils.addErrorLog(1l, "OilVerificationBizService.sendLastMonthVerificationDetail", "上一月扫码明细发送失败。" + e.getMessage(), null);
		}

	}

	public List<WorkshopOilVerificationSummary> queryOilVerificationSummaryList(Map<String, Object> paramMap){
		paramMap.put("orderBy", "creation_time desc");
		List<OilVerification> verificationList = oilVerificationMapper.queryOilVerificationList(paramMap);
		Map<Long, WorkshopOilVerificationSummary> summaryMap = new HashMap<Long, WorkshopOilVerificationSummary>();
		if (verificationList == null){
			return Collections.emptyList();
		}

		for (OilVerification verification : verificationList){
			Long workshopId = Long.valueOf(verification.getWorkshopId());
			if (!summaryMap.containsKey(workshopId)){
				WorkshopOilVerificationSummary summary = new WorkshopOilVerificationSummary(workshopId, verification.getWorkshopName());
//				Long partnerId = workshopPartnerService.getTradePartnerIdByWorkshopId(workshopId);
//				if(partnerId == null){
//					logger.error("未找到" + verification.getQrCode() + "核销记录的门店ID" + verification.getWorkshopId() + "对应的SP");
//					continue;
//				}
//				summary.setPartnerName(organizationService.selectByPrimaryKey(partnerId).getOrganizationName());
//				VerificationRule verificationRule = verificationRuleBizService.getVerificationRuleByPartnerId(partnerId);
//				WorkshopDistributionRule distributionRule = workshopDistributionRuleBizService.queryDistributionRuleByWorkshopId(workshopId);
//				summary.setRule(verificationRule);
//				summary.setDistributionRule(distributionRule);
				summary.setPartnerName(verification.getOrganizationName());
				if(verification.getVerificationRuleId() != null){
					VerificationRule verificationRule = new VerificationRule();
					verificationRule.setId(verification.getVerificationRuleId());
					verificationRule.setPerLiterReward(verification.getPerLiterReward());
					verificationRule.setPartnerId(verification.getPartnerId());
					verificationRule.setRuleType(VerificationRule.RULE_TYPE_LITER);
					summary.setRule(verificationRule);
				}
				if(verification.getDistributionRuleId() != null){
					WorkshopDistributionRule distributionRule = new WorkshopDistributionRule();
					distributionRule.setId(verification.getDistributionRuleId());
					distributionRule.setOwnerReward(verification.getOwnerPerLiterReward());
					distributionRule.setMechanicReward(verification.getMechanicPerLiterReward());
					summary.setDistributionRule(distributionRule);
				}
				summaryMap.put(workshopId, summary);
			}
			WorkshopOilVerificationSummary summary = summaryMap.get(workshopId);
//			//没有设置分配规则不进行统计
//			if (summary.getDistributionRule() == null){
//				continue;
//			}
			//已核销升数
			summary.setClosedCapacity(summary.getClosedCapacity() + verification.getVerifiedCapacity());

			//技师扫码
			summary.setMechanicScannedCapacity(summary.getMechanicScannedCapacity() + verification.getCapacity());
			if (VerificationBatch.STATUS_SUBMIT.equals(verification.getStatus())){
				summary.setSubmitCapacity(summary.getSubmitCapacity() + verification.getCapacity());
			}else if (VerificationBatch.STATUS_APPROVED.equals(verification.getStatus())){
				summary.setApprovedCapacity(summary.getApprovedCapacity() + verification.getCapacity());
			}

		}

		return new ArrayList<WorkshopOilVerificationSummary>(summaryMap.values());
	}

	public List<WorkshopOilVerificationSummary> queryOilVerificationSummaryListWithAwardRealtime(Map<String, Object> paramMap){
		List<OilVerification> verificationList = oilVerificationMapper.queryOilVerificationListWithAwardRealtime(paramMap);
		Map<Long, WorkshopOilVerificationSummary> summaryMap = new HashMap<Long, WorkshopOilVerificationSummary>();
		if (verificationList == null){
			return Collections.emptyList();
		}

		for (OilVerification verification : verificationList){
			Long workshopId = Long.valueOf(verification.getWorkshopId());
			if (!summaryMap.containsKey(workshopId)){
				WorkshopOilVerificationSummary summary = new WorkshopOilVerificationSummary(workshopId, verification.getWorkshopName());
				summary.setPartnerName(verification.getPartnerName());
				summaryMap.put(workshopId, summary);
			}

			WorkshopOilVerificationSummary summary = summaryMap.get(workshopId);

			//已核销升数
			summary.setClosedCapacity(summary.getClosedCapacity() + verification.getVerifiedCapacity());
			//技师扫码
			summary.setMechanicScannedCapacity(summary.getMechanicScannedCapacity() + verification.getCapacity());
			//核销总金额
			summary.setVerifyAmount(summary.getVerifyAmout() + verification.getTotalAmount());
			//店主总金额
			summary.setOwnerAmount(summary.getOwnerAmount() + verification.getOwnerAmount());
			//技师总金额
			summary.setMechanicAmount(summary.getMechanicAmout() + verification.getMechanicAmount());
		}

		return new ArrayList<WorkshopOilVerificationSummary>(summaryMap.values());
	}

	public List<OilVerification> queryOilVerificationList(Map<String, Object> paramMap) {
		paramMap.put("orderBy", "creation_time desc");
		return oilVerificationMapper.queryOilVerificationList(paramMap);
	}

	public List<OilVerification> queryOilVerificationExpList(OilVerificationParams paramMap, String operation) {
		if("spreport".equals(operation)){
			return oilVerificationMapper.querySpOilVerificationReportExpList(paramMap);
		}
		return oilVerificationMapper.querySpOilVerificationExpList(paramMap);
	}

	public List<OilVerification> queryOilVerificationListWithAwardRealtime(Map<String, Object> paramMap) {
		return oilVerificationMapper.queryOilVerificationListWithAwardRealtime(paramMap);
	}

	public List<OilVerificationReportView> queryOilVerificationReport(Map<String, Object> paramMap){
		paramMap.put("orderBy", "create_date");
		return oilVerificationMapper.queryOilVerificationReport(paramMap);
	}

	public List<OilVerificationReportView> queryOilVerificationReportByYear(Map<String, Object> paramMap){
		paramMap.put("orderBy", "create_date");
		return oilVerificationMapper.queryOilVerificationReportByYear(paramMap);
	}

	/**
	 * 获取机油核销总升数, 针对不同数据权限进行不同范围的统计
	 * @param paramMap PermissionParamsMap
	 * @return 机油核销总升数
	 */
	public Long queryOilVerificationSumReport(PermissionParamsMap<String, Object> paramMap){
		return oilVerificationMapper.queryOilVerificationSumReport(paramMap);
	}

	public List<OilVerificationReportView> queryOilVerificationGridReport(Map<String, Object> paramMap){
		paramMap.put("orderBy", "work_shop_name");
		return oilVerificationMapper.queryOilVerificationGridReport(paramMap);
	}

	/**
	 * 对指定日期范围内的门店扫码记录进行核销
	 * @param workshopId
	 * @param dateFrom
	 * @param dateTo
	 * @return
	 * @throws WxPltException
	 */
	public void verifyWorkshop(Long workshopId, Date dateFrom, Date dateTo) throws WxPltException{
		WorkshopOilVerificationSummary summary = getOilVerificationSummary(workshopId, dateFrom, dateTo);

		if (summary.getCanVerifyCapacity() == 0){
			throw new WxPltException(MessageResourceUtil.getMessage("sp.oil.verification.capacityEqZero", new String[]{summary.getWorkshopName()}));
		}

		//获取指定日期范围内该门店下所有待核销数据
		List<OilVerification> verificationList = queryUnVerificationList(workshopId, dateFrom, dateTo);

		//根据用户扫描的数据进行核销,分解到所有的带核销记录中,生成核销详情
		List<VerificationDetail> verificationDetailList = decomposeMechanicVerificationList(verificationList, summary);

		saveVerificationDetails(verificationDetailList, summary);

	}

	/**
	 * 保存核销明细
	 * @param verificationDetailList
	 * @return
	 */
	private void saveVerificationDetails(List<VerificationDetail> verificationDetailList,
			WorkshopOilVerificationSummary summary){

		double amount = summary.getVerifyAmout();
		VerificationBatch verificationBatch = new VerificationBatch();
		verificationBatch.setWorkshopId(verificationDetailList.get(0).getWorkshopId());
		verificationBatch.setPartnerId(summary.getRule().getPartnerId());
		verificationBatch.setRuleId(summary.getRule().getId());
		verificationBatch.setDistributionRuleId(summary.getDistributionRule().getId());
		verificationBatch.setStatus(VerificationBatch.STATUS_SUBMIT);
		verificationBatch.setAmount(amount);
		verificationBatch.setOwnerAmount(summary.getOwnerAmount());
		verificationBatch.setMechanicAmount(summary.getMechanicAmout());
		saveVerificationBatch(verificationBatch);

		for (VerificationDetail verificationDetail : verificationDetailList) {
			verificationDetail.setBatchId(verificationBatch.getId());
			saveVerificationDetail(verificationDetail);
		}
	}

	/**
	 * 对指定日期范围内的门店扫码记录进行核销(不考虑用户扫码情况)
	 * @param workshopId
	 * @param dateFrom
	 * @param dateTo
	 * @return
	 * @throws WxPltException
	 */
	public void verifyWorkshopRegardlessUser(Long workshopId, Date dateFrom, Date dateTo) throws WxPltException{
		Long partnerId = workshopPartnerService.getTradePartnerIdByWorkshopId(workshopId);
		VerificationRule verificationRule = verificationRuleBizService.getVerificationRuleByPartnerId(partnerId);
		logger.info("verificationRule: {}", verificationRule);

		WorkshopOilVerificationSummary summary = getOilVerificationSummary(workshopId, dateFrom, dateTo);

		if (summary.getCanVerifyCapacity() == 0){
			throw new WxPltException(MessageResourceUtil.getMessage("sp.oil.verification.capacityEqZero", new String[]{summary.getWorkshopName()}));
		}

		//获取指定日期范围内该门店下所有待核销数据
		List<OilVerification> verificationList = queryUnVerificationList(workshopId, dateFrom, dateTo);

		//回传微信接口需要核销完全的数据
		List<Map<String, Object>> verifyCompletedInfos = new ArrayList<Map<String,Object>>();

		//根据用户扫描的数据进行核销,分解到所有的带核销记录中,生成核销详情
		List<VerificationDetail> verificationDetailList = decomposeMechanicVerificationListRegardlessUser(verificationList, verificationRule, summary, verifyCompletedInfos);

		saveVerificationDetailsRegardlessUser(verificationDetailList, verificationRule, summary);
	}

	public List<VerificationDetail> decomposeMechanicVerificationListRegardlessUser(List<OilVerification> verificationList, VerificationRule verificationRule,
			WorkshopOilVerificationSummary summary, List<Map<String, Object>> verifyCompletedInfos) {
		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		if (VerificationRule.RULE_TYPE_ORDER.equals(verificationRule.getRuleType())) {
			verificationDetailList = decomposeMechanicVerificationListByOrderRuleRegardlessUser(verificationList, summary, verifyCompletedInfos);
		}
		return verificationDetailList;
	}

	/**
	 * 保存核销明细
	 * @param verificationDetailList
	 * @return
	 */
	private void saveVerificationDetailsRegardlessUser(List<VerificationDetail> verificationDetailList, VerificationRule verificationRule,
			WorkshopOilVerificationSummary summary){

		double amount = summary.getVerifyAmout();
		VerificationBatch verificationBatch = new VerificationBatch();
		verificationBatch.setWorkshopId(verificationDetailList.get(0).getWorkshopId());
		verificationBatch.setPartnerId(verificationRule.getPartnerId());
		verificationBatch.setRuleId(verificationRule.getId());
		verificationBatch.setStatus(VerificationBatch.STATUS_SUBMIT);
		verificationBatch.setAmount(amount);
		saveVerificationBatch(verificationBatch);

		for (VerificationDetail verificationDetail : verificationDetailList) {
			verificationDetail.setBatchId(verificationBatch.getId());
			saveVerificationDetail(verificationDetail);
		}
	}

	private List<VerificationDetail> decomposeMechanicVerificationListByOrderRuleRegardlessUser(List<OilVerification> verificationList,
			WorkshopOilVerificationSummary summary, List<Map<String, Object>> verifyCompletedInfos) {
		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		int totalCapacityForVerify = summary.getCanVerifyCapacity();
		int totalCapacityUnVerify = 0;
		//将所有待核销记录按照技师进行分组,并计算每个技师应该核销的比例、升数
		Map<String, MechanicVerification> mechanicVerificationMap = new HashMap<String, MechanicVerification>();

		for (OilVerification oilVerification : verificationList) {
			String mechanicCode = oilVerification.getMechanicCode();
			MechanicVerification mechanicVerification = mechanicVerificationMap.get(mechanicCode);
			if (mechanicVerification == null){
				mechanicVerification = new MechanicVerification(mechanicCode);
				mechanicVerificationMap.put(mechanicCode, mechanicVerification);
				mechanicVerification.workshopId = oilVerification.getWorkshopId();
			}
			if (mechanicVerification.getVerificationList() == null){
				mechanicVerification.setVerificationList(new ArrayList<OilVerification>());
			}
			List<OilVerification> mechanicVerificationList = mechanicVerification.getVerificationList();
			mechanicVerificationList.add(oilVerification);
			mechanicVerification.setTotalShouldVerified(mechanicVerification.getTotalShouldVerified() + oilVerification.getUnVerifiedCapacity());
			totalCapacityUnVerify = totalCapacityUnVerify + oilVerification.getUnVerifiedCapacity();
		}
		List<MechanicVerification> mechanicVerificationList = new ArrayList<MechanicVerification>(mechanicVerificationMap.values());

		//对技师核销记录按照应核销量进行排序（从小到大）
		Collections.sort(mechanicVerificationList, new Comparator<MechanicVerification>(){

			@Override
			public int compare(MechanicVerification o1, MechanicVerification o2) {
				return (o1.getTotalShouldVerified() < o2.getTotalShouldVerified()) ? -1 : 1;
			}

		});

		int totalActualVerified = 0;
		for (int i = 0; i < mechanicVerificationList.size(); i++){
			MechanicVerification mechanicVerification = mechanicVerificationList.get(i);
			mechanicVerification.setVerifyPercent((double)mechanicVerification.getTotalShouldVerified() / (double)totalCapacityUnVerify);

			double mechanicActualVerified = mechanicVerification.getVerifyPercent() * totalCapacityForVerify;
			int minMechanicActualVerified = (int) Math.floor(mechanicActualVerified);

			if (i < mechanicVerificationList.size() - 1) {
				if (minMechanicActualVerified == 0 || minMechanicActualVerified > mechanicVerification.getTotalShouldVerified()){ //按比例分成,取整数部分后,如果等于0或者大于应核销数量,则设为应核销数量
					mechanicVerification.setTotalActualVerified(mechanicVerification.getTotalShouldVerified());
				} else {
					mechanicVerification.setTotalActualVerified(minMechanicActualVerified);
				}
				totalActualVerified = totalActualVerified + mechanicVerification.getTotalActualVerified();
			} else {
				int leftVerified = totalCapacityForVerify - totalActualVerified;
				if (leftVerified <= mechanicVerification.getTotalShouldVerified()){
					mechanicVerification.setTotalActualVerified(leftVerified);
				} else {
					logger.error("verification exception when calculate last mechanic");
				}
			}

			//微信回传信息
			Map<String, Object> verifyCompletedInfo = new HashMap<String, Object>(3);
			verifyCompletedInfo.put("wsid", mechanicVerification.workshopId);
			verifyCompletedInfo.put("wmid", mechanicVerification.getMechanicCode());

			//根据实际要核销的数量对扫码记录进行分解
			verificationDetailList.addAll(generateVerificationDetail1(mechanicVerification, verifyCompletedInfo));

			//收集需要回传的qrcode数据
			if(verifyCompletedInfo.containsKey("qrlist")){
				verifyCompletedInfos.add(verifyCompletedInfo);
			}
		}

		return verificationDetailList;
	}
	@SuppressWarnings("unchecked")
	private List<VerificationDetail> generateVerificationDetail1(MechanicVerification mechanicVerification, Map<String, Object> verifyCompletedInfo) {
		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		List<OilVerification> oilVerificationList = mechanicVerification.getVerificationList();
		//按待核销升数进行升序排序
		Collections.sort(oilVerificationList, new Comparator<OilVerification>(){

			@Override
			public int compare(OilVerification o1, OilVerification o2) {
				return (o1.getUnVerifiedCapacity() < o2.getUnVerifiedCapacity()) ? -1 : 1;
			}

		});

		//微信回传信息
		List<String> verifyCompletedQrcodes = (List<String>) verifyCompletedInfo.get("qrlist");
		if(verifyCompletedQrcodes == null){
			verifyCompletedQrcodes = new ArrayList<String>();
			verifyCompletedInfo.put("qrlist", verifyCompletedQrcodes);
		}

		int totalLeftActualVerified = mechanicVerification.getTotalActualVerified();
		for (int i = 0; i < oilVerificationList.size(); i++) {
			if (totalLeftActualVerified == 0){ //已核销完
				break;
			}
			OilVerification oilVerification = oilVerificationList.get(i);
			int verifiedCapacity;

			if (oilVerification.getUnVerifiedCapacity() < totalLeftActualVerified){
				verifiedCapacity = oilVerification.getUnVerifiedCapacity();
				//qrcode完全核销
				verifyCompletedQrcodes.add(oilVerification.getQrCode());
			} else {
				verifiedCapacity = totalLeftActualVerified;
			}
			totalLeftActualVerified = totalLeftActualVerified - verifiedCapacity;
			VerificationDetail detail = new VerificationDetail(oilVerification, verifiedCapacity);
			verificationDetailList.add(detail);
		}
		return verificationDetailList;
	}

	private void saveVerificationBatch(VerificationBatch verificationBatch) {
		verificationBatch.setCreatedBy(ContextUtil.getCurUserId());
		verificationBatch.setLastUpdatedBy(ContextUtil.getCurUserId());
		verificationBatchMapper.insertSelective(verificationBatch);
	}

	private void saveVerificationDetail(VerificationDetail verificationDetail) {
		verificationDetail.setCreatedBy(ContextUtil.getCurUserId());
		verificationDetail.setLastUpdatedBy(ContextUtil.getCurUserId());
		verificationDetailMapper.insertSelective(verificationDetail);
	}

	public List<VerificationDetail> decomposeMechanicVerificationList(List<OilVerification> verificationList,
			WorkshopOilVerificationSummary summary) {
		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		if (VerificationRule.RULE_TYPE_ORDER.equals(summary.getRule().getRuleType())) {
			verificationDetailList = decomposeMechanicVerificationListByOrderRule(verificationList, summary);
		}
		return verificationDetailList;
	}

	private List<VerificationDetail> decomposeMechanicVerificationListByOrderRule(List<OilVerification> verificationList,
			WorkshopOilVerificationSummary summary) {
		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		int totalCapacityForVerify = summary.getCanVerifyCapacity();
		int totalCapacityUnVerify = 0;
		//将所有待核销记录按照技师进行分组,并计算每个技师应该核销的比例、升数
		Map<String, MechanicVerification> mechanicVerificationMap = new HashMap<String, MechanicVerification>();

		for (OilVerification oilVerification : verificationList) {
			String mechanicCode = oilVerification.getMechanicCode();
			if (!mechanicVerificationMap.containsKey(mechanicCode)){
				mechanicVerificationMap.put(mechanicCode, new MechanicVerification(mechanicCode));
			}
			MechanicVerification mechanicVerification = mechanicVerificationMap.get(mechanicCode);
			if (mechanicVerification.getVerificationList() == null){
				mechanicVerification.setVerificationList(new ArrayList<OilVerification>());
			}
			List<OilVerification> mechanicVerificationList = mechanicVerification.getVerificationList();
			mechanicVerificationList.add(oilVerification);
			mechanicVerification.setTotalShouldVerified(mechanicVerification.getTotalShouldVerified() + oilVerification.getUnVerifiedCapacity());
			totalCapacityUnVerify = totalCapacityUnVerify + oilVerification.getUnVerifiedCapacity();
		}
		List<MechanicVerification> mechanicVerificationList = new ArrayList<MechanicVerification>(mechanicVerificationMap.values());

		//对技师核销记录按照应核销量进行排序（从小到大）
		Collections.sort(mechanicVerificationList, new Comparator<MechanicVerification>(){

			@Override
			public int compare(MechanicVerification o1, MechanicVerification o2) {
				return (o1.getTotalShouldVerified() < o2.getTotalShouldVerified()) ? -1 : 1;
			}

		});

		int totalActualVerified = 0;
		for (int i = 0; i < mechanicVerificationList.size(); i++){
			MechanicVerification mechanicVerification = mechanicVerificationList.get(i);
			mechanicVerification.setVerifyPercent((double)mechanicVerification.getTotalShouldVerified() / (double)totalCapacityUnVerify);

			double mechanicActualVerified = mechanicVerification.getVerifyPercent() * totalCapacityForVerify;
			int minMechanicActualVerified = (int) Math.floor(mechanicActualVerified);

			if (i < mechanicVerificationList.size() - 1) {
				if (minMechanicActualVerified == 0 || minMechanicActualVerified > mechanicVerification.getTotalShouldVerified()){ //按比例分成,取整数部分后,如果等于0或者大于应核销数量,则设为应核销数量
					mechanicVerification.setTotalActualVerified(mechanicVerification.getTotalShouldVerified());
				} else {
					mechanicVerification.setTotalActualVerified(minMechanicActualVerified);
				}
				totalActualVerified = totalActualVerified + mechanicVerification.getTotalActualVerified();
			} else {
				int leftVerified = totalCapacityForVerify - totalActualVerified;
				if (leftVerified <= mechanicVerification.getTotalShouldVerified()){
					mechanicVerification.setTotalActualVerified(leftVerified);
				} else {
					logger.error("verification exception when calculate last mechanic");
				}
			}
			//根据实际要核销的数量对扫码记录进行分解
			verificationDetailList.addAll(generateVerificationDetail(mechanicVerification));
		}

		return verificationDetailList;
	}

	private List<VerificationDetail> generateVerificationDetail(MechanicVerification mechanicVerification) {
		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		List<OilVerification> oilVerificationList = mechanicVerification.getVerificationList();
		//按待核销升数进行升序排序
		Collections.sort(oilVerificationList, new Comparator<OilVerification>(){

			@Override
			public int compare(OilVerification o1, OilVerification o2) {
				return (o1.getUnVerifiedCapacity() < o2.getUnVerifiedCapacity()) ? -1 : 1;
			}

		});

		int totalLeftActualVerified = mechanicVerification.getTotalActualVerified();
		for (int i = 0; i < oilVerificationList.size(); i++) {
			if (totalLeftActualVerified == 0){ //已核销完
				break;
			}
			OilVerification oilVerification = oilVerificationList.get(i);
			int verifiedCapacity;

			if (oilVerification.getUnVerifiedCapacity() < totalLeftActualVerified){
				verifiedCapacity = oilVerification.getUnVerifiedCapacity();
			} else {
				verifiedCapacity = totalLeftActualVerified;
			}
			totalLeftActualVerified = totalLeftActualVerified - verifiedCapacity;
			VerificationDetail detail = new VerificationDetail(oilVerification, verifiedCapacity);
			verificationDetailList.add(detail);
		}
		return verificationDetailList;
	}

	private List<OilVerification> queryUnVerificationList(Long workshopId, Date dateFrom, Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put(P_WORKSHOP_ID, String.valueOf(workshopId));
		paramMap.put(P_DATE_FROM, newDateFrom);
		paramMap.put(P_DATE_TO, newDateTo);
		paramMap.put("orderBy", "creation_time desc");
		List<OilVerification> verificationList = oilVerificationMapper.queryOilVerificationList(paramMap);
		if (verificationList != null){
			Iterator<OilVerification> iter = verificationList.iterator();
			while (iter.hasNext()){
				OilVerification oilVerification = iter.next();
				if (oilVerification.isFullyVerified()){
					iter.remove();
				}
			}
		}
		return verificationList;
	}

	class MechanicVerification{
		private String mechanicCode;
		private int totalShouldVerified = 0;
		private double verifyPercent;
		private int totalActualVerified = 0;
		private List<OilVerification> verificationList;
		private String workshopId;
		public MechanicVerification(String mechanicCode) {
			this.mechanicCode = mechanicCode;
		}
		public String getMechanicCode() {
			return mechanicCode;
		}
		public void setMechanicCode(String mechanicCode) {
			this.mechanicCode = mechanicCode;
		}
		public int getTotalShouldVerified() {
			return totalShouldVerified;
		}
		public void setTotalShouldVerified(int totalShouldVerified) {
			this.totalShouldVerified = totalShouldVerified;
		}
		public double getVerifyPercent() {
			return verifyPercent;
		}
		public void setVerifyPercent(double verifyPercent) {
			this.verifyPercent = verifyPercent;
		}
		public int getTotalActualVerified() {
			return totalActualVerified;
		}
		public void setTotalActualVerified(int totalActualVerified) {
			this.totalActualVerified = totalActualVerified;
		}
		public List<OilVerification> getVerificationList() {
			return verificationList;
		}
		public void setVerificationList(List<OilVerification> verificationList) {
			this.verificationList = verificationList;
		}
	}

	public void confirmVerification(Long[] batchIds) {
		for (Long batchId : batchIds){
			VerificationBatch batch = new VerificationBatch();
			batch.setId(batchId);
			batch.setStatus(VerificationBatch.STATUS_APPROVED);
			batch.setApproveTime(DateUtil.getCurrentDate());
			verificationBatchMapper.updateByPrimaryKeySelective(batch);
		}
	}

	public List<VerificationBatch> queryVerificationBatch(Map<String, Object> paramMap, String operation) {
		return verificationBatchMapper.queryVerificationBatch(paramMap);
	}

	public List<VerificationDetail> queryVerificationDetail(Long batchId) {
		return verificationDetailMapper.selectVerificationDetailByBatchId(batchId);
	}

	public List<VerificationDetailUser> queryVerificationDetailUser(Long batchId){
		return verificationDetailUserMapper.selectVerificationDetailUserList(batchId);
	}

	/**
	 * 核算技师扫码记录(实时核算)
	 * @param mechanicCode
	 * @param qrCode
	 * @param capacity
	 */
	public void verifyMechanicQrcode(String mechanicCode, String qrCode, int capacity){
		WorkshopEmployee emp = workshopEmployeeBizService.queryWorkshopEmployeeByCode(mechanicCode);
		if (emp == null){
			logger.warn("can not get workshop employee by mechanicCode:{}", mechanicCode);
			return;
		}
		WorkshopDistributionRule distributionRule = workshopDistributionRuleBizService.queryDistributionRuleByWorkshopId(emp.getWorkshopId());
		if (distributionRule == null){
			logger.warn("can not get workshop distribution rule by workshopId: {}", emp.getWorkshopId());
			return;
		}

		if (!"Y".equals(distributionRule.getAwardRealtime())){
			logger.warn("workshop {} current distribution rule award realtime {}", emp.getWorkshopName(), distributionRule.getAwardRealtime());
			return;
		}

		VerificationRule verificationRule = verificationRuleBizService.getVerificationRuleByPartnerId(distributionRule.getPartnerId());
		if (verificationRule == null){
			logger.warn("can not get partner verification rule by partnerId: {}", distributionRule.getPartnerId());
			return;
		}
		double ownerAmount = capacity * distributionRule.getOwnerReward().doubleValue();
		double mechanicAmount = capacity * distributionRule.getMechanicReward().doubleValue();

		MechanicVerificationPay pay = new MechanicVerificationPay();
		pay.setQrCode(qrCode);
		pay.setCapacity(capacity);
		pay.setMechanicCode(mechanicCode);
		pay.setDistributionRuleId(distributionRule.getId());
		pay.setVerificationRuleId(verificationRule.getId());
		pay.setOwnerAmount(BigDecimal.valueOf(ownerAmount));
		pay.setMechanicAmount(BigDecimal.valueOf(mechanicAmount));
		mechanicVerificationPayBizService.saveMechanicVerificationPay(pay);
	}


	/**
	 * 对指定日期范围内的门店扫码记录进行核销（按照每个技师扫码记录独立核销，不存在平均、结余的情况）
	 * @param workshopId
	 * @param dateFrom
	 * @param dateTo
	 * @return
	 * @throws WxPltException
	 */
	public void verifyWorkshopByIndividualMechanicQrcode(Long workshopId, Date dateFrom, Date dateTo) throws WxPltException {
		WorkshopOilVerificationSummary summary = getOilVerificationSummary(workshopId, dateFrom, dateTo);

		if (summary.getCanVerifyCapacity() == 0){
			throw new WxPltException(MessageResourceUtil.getMessage("sp.oil.verification.capacityEqZero", new String[]{summary.getWorkshopName()}));
		}

		VerificationRule verificationRule = summary.getRule();
		logger.info("verificationRule: {}", verificationRule);

		if (VerificationRule.RULE_TYPE_LITER.equals(verificationRule.getRuleType())){
			//获取指定日期范围内该门店下所有待核销数据
			List<OilVerification> verificationList = queryUnVerificationList(workshopId, dateFrom, dateTo);

			List<VerificationDetail> detailList = convertUnVerficationListToDetailList(verificationList);

			saveVerificationDetails(detailList, summary);
		}
	}

	/**
	 * 将未核销的扫码记录转换为核销明细
	 * @param verificationList
	 * @return
	 */
	private List<VerificationDetail> convertUnVerficationListToDetailList(List<OilVerification> verificationList) {
		if (verificationList == null || verificationList.isEmpty()){
			return Collections.emptyList();
		}

		List<VerificationDetail> verificationDetailList = new ArrayList<VerificationDetail>();
		for (OilVerification tempOilVerification : verificationList) {

			VerificationDetail detail = new VerificationDetail(tempOilVerification, tempOilVerification.getCapacity());
			verificationDetailList.add(detail);
		}

		return verificationDetailList;
	}

	public Map<String, Object> queryVerificationInfoByMechanic(String mechanicCode, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		long scannedTotalCapacity = mechanicQrcodeBizService.getMechanicQrcodeTotalCapacity(mechanicCode, dateFrom, dateTo);
		Long verifiedTotalCapacity = oilVerificationMapper.getVerifiedTotalCapacityByMechanic(mechanicCode, dateFrom, dateTo);

		resultMap.put("scannedTotalCapacity", scannedTotalCapacity);
		resultMap.put("verifiedTotalCapacity", verifiedTotalCapacity == null ? 0 : verifiedTotalCapacity.longValue());
		return resultMap;
	}

	public List<OilVerificationReportView> querySpKpiReportByYear(Map<String, Object> paramMap){
		paramMap.put("orderBy", "report_time");
		return oilVerificationMapper.querySpKpiReportByYear(paramMap);
	}

	public List<OilVerificationReportView> querySpKpiReportByDay(Map<String, Object> paramMap){
		paramMap.put("orderBy", "report_time");
		return oilVerificationMapper.querySpKpiReportByDay(paramMap);
	}

	public List<OilVerificationReportView> querySpKpiReportByMonth(Map<String, Object> paramMap){
		paramMap.put("orderBy", "report_time");
		return oilVerificationMapper.querySpKpiReportByMonth(paramMap);
	}

	public List<OilVerificationReportView> querySpKpiReportByEmpMonth(Map<String, Object> paramMap){
		paramMap.put("orderBy", "report_time");
		return oilVerificationMapper.querySpKpiReportByEmpMonth(paramMap);
	}

	public List<OilVerificationReportView> querySpKpiReportBySp(Map<String, Object> paramMap){
		paramMap.put("orderBy", "partner_id");
		return oilVerificationMapper.querySpKpiReportBySp(paramMap);
	}

	public List<OilVerificationReportView> querySpKpiReportByWs(Map<String, Object> paramMap){
		paramMap.put("orderBy", "workshop_id");
		return oilVerificationMapper.querySpKpiReportByWs(paramMap);
	}

	public List<OilVerificationReportView> querySpKpiReportByEmp(Map<String, Object> paramMap){
		paramMap.put("orderBy", "emp_id");
		return oilVerificationMapper.querySpKpiReportByEmp(paramMap);
	}

	public List<OilVerificationReportView> queryIndexTDReport(Map<String, Object> paramMap){
		paramMap.put("orderBy", "partner_id");
		return oilVerificationMapper.queryIndexTDReport(paramMap);
	}

	@SuppressWarnings("unchecked")
	public Map<String,Object> importOilByTraceCode(final Workbook wb, final HttpServletRequest request){
		logger.info("\u5f00\u59cb\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165=================================================");
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
		try {
			final Map<String, Object> progressStatus = new HashMap<String, Object>();
			progressStatus.put("status", "progressing");
			progressStatus.put("progressKey", "importOilByTraceCodeProgress");
			progressStatus.put("desc", "正在解析...");
			new Thread(){
				public void run() {
					try {
						Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect(wb,
								new OilVerificationImportVo(), new String[]{"mobile", "logisticsCode"});
						if("dataInvalid".equals(repsMap.get("result"))){
							progressStatus.put("status", "error");
							progressStatus.put("desc", "\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u6570\u636e\u89e3\u6790\u5f02\u5e38: " + repsMap.get("errorResult"));
							return;
						}
						progressStatus.put("desc", "正在导入...");
						mechanicQrcodeBizService.importDataByTraceCode((List<OilVerificationImportVo>) repsMap.get("datalst"), progressStatus);
						logger.info("\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u7ed3\u675f=================================================");
						progressStatus.put("status", "success");
						progressStatus.put("desc", "导入成功");
					}catch (WxPltException e) {
						logger.error(e.getMessage());
						progressStatus.put("status", "error");
						progressStatus.put("desc", e.getMessage());
					} catch (Exception e) {
						e.printStackTrace();
						logger.error("\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u5931\u8d25: " + e.getMessage());
						progressStatus.put("status", "error");
						progressStatus.put("desc", "\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u5931\u8d25: " + e.getMessage());
					}
				};
			}.start();
			request.getSession().setAttribute("importOilByTraceCodeProgress", progressStatus);
			resultMap.put("progressStatus", progressStatus);
			resultMap.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u5931\u8d25: " + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "\u7269\u6d41\u7801\u6838\u9500\u5bfc\u5165\u5931\u8d25: " + e.getMessage());
		}
		return resultMap;
	}

	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String,Object> importOilVerification(Workbook wb){
		logger.info("import oil scan data start=================================================");
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
		try {
			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect(wb,
					new OilVerificationImportVo(), new String[]{"workshopName", "scanLink", "mobile", "scanTime"});
			if("dataInvalid".equals(repsMap.get("result"))){
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "扫码数据解析异常：" + repsMap.get("errorResult"));
				return resultMap;
			}
			List<OilVerificationImportVo> voList = (List<OilVerificationImportVo>) repsMap.get("datalst");
			for(int i = 0; i < voList.size(); i++){
				OilVerificationImportVo vo = voList.get(i);
				String qrcode = vo.getScanLink().substring(vo.getScanLink().lastIndexOf("id=") + 3);
				Map<String, Object> product = qrCodeService.getQrCodeProductInfo(qrcode);
				if(product == null){
					throw new RuntimeException("第" + i + "条扫码数据对应的QRCode(" + qrcode + ")未找到对应的产品信息");
				}
				String sku = (String) product.get("sku");
				//验证qrcode是否已经扫码
				MechanicQrcodeExample example = new MechanicQrcodeExample();
				example.createCriteria().andQrCodeEqualTo(qrcode);
				List<MechanicQrcode> list = mechanicQrcodeMapper.selectByExample(example);
				if(!list.isEmpty()){
					throw new RuntimeException("第" + i + "条扫码数据对应的QRCode(" + qrcode + ")已扫描");
				}
				//获取技师信息
				WorkshopEmployee employee = null;
				WorkshopEmployeeExample workshopEmployeeExample = new WorkshopEmployeeExample();
				workshopEmployeeExample.createCriteria().andMobileEqualTo(vo.getMobile());
				List<WorkshopEmployee> employees = workshopEmployeeMapper.selectByExample(workshopEmployeeExample);
				if(employees.isEmpty()){
					throw new RuntimeException("第" + i + "条扫码数据对应的扫码手机(" + vo.getMobile() + ")未找到对应的技师");
				}else if(employees.size() > 1){
					throw new RuntimeException("第" + i + "条扫码数据对应的扫码手机(" + vo.getMobile() + ")对应多个技师");
				}
				employee = employees.get(0);
				//获取门店ID
				WorkshopMasterExample workShopVoExample = new WorkshopMasterExample();
				workShopVoExample.createCriteria().andWorkshopNameEqualTo(vo.getWorkshopName()).andStatusEqualTo(3);
				List<WorkshopMaster> workShopVos = workshopMasterBizService.queryByExample(workShopVoExample);
				Long workshopId = null;
				if(workShopVos.size() == 1){
					workshopId = workShopVos.get(0).getId();
				}else{
					workshopId = employee.getWorkshopId();
				}
				//扫码时间
				Date scanTime = DateUtil.parseDate(vo.getScanTime(), "yyyy-MM-dd HH:mm");
//				//插入用户扫码记录
//				UserQrcode userQrcode = new UserQrcode();
//				userQrcode.setCreationTime(scanTime);
//				userQrcode.setQrCode(qrcode);
//				userQrcode.setSku(sku);
//				userQrcodeMapper.insertSelective(userQrcode);
				//插入用户扫码记录
				MechanicQrcode mechanicQrcode = new MechanicQrcode();
				mechanicQrcode.setCreatedBy(1l);
				mechanicQrcode.setCreationTime(scanTime);
				mechanicQrcode.setMechanicCode(employee.getCode());
				mechanicQrcode.setQrCode(qrcode);
				mechanicQrcode.setSku(sku);
				mechanicQrcode.setWorkshopId(workshopId);
				mechanicQrcodeMapper.insertSelective(mechanicQrcode);
				//更新门店库存
				List<WsVerifOutStockParams> wsVerifOutStockParams = new ArrayList<WsVerifOutStockParams>();
				WsVerifOutStockParams params = new WsVerifOutStockParams();
				params.setSku(mechanicQrcode.getSku());
				params.setWorkshopId(mechanicQrcode.getWorkshopId());
				wsVerifOutStockParams.add(params);
				inventoryService.updateWsInventoryByVerificationList(wsVerifOutStockParams);
			}
			logger.info("import oil scan data end=================================================");
		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			logger.error("导入扫码数据失败,异常信息:" + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "扫码数据导入失败：" + e.getMessage());
		}
		return resultMap;
	}

	/**
	 * 发送扫码明细周报
	 */
	@SuppressWarnings("unchecked")
	public void sendScanDetailWeeklyReport(){
		logger.info("sendScanDetailWeeklyReport start=============================");
		try {
			//获取邮件发送配置
			String[] to = null;
			String[] cc = null;
			String honorific = null;
			Map<String, Object> dicMap = dicServiceImpl.getDicItemByDicTypeCode("task.weekemail.scandetial");
			if("success".equals(dicMap.get("result"))){
				List<DicItemVo> list = (List<DicItemVo>) dicMap.get("data");
				if(list != null && !list.isEmpty()){
					for(DicItemVo vo : list){
						if("to".equals(vo.getDicItemCode())){
							to = vo.getDicItemDesc().split(",");
							honorific = vo.getDicItemName();
						}else if("cc".equals(vo.getDicItemCode())){
							cc = vo.getDicItemDesc().split(",");
						}
					}
				}
				if(to == null){
					throw new WxPltException("数据字典中未配置接收人信息");
				}
			}else{
				throw new WxPltException(MessageResourceUtil.getMessage("system.unexpected_exception"));
			}
			WebApplicationContext webApplicationContext = ContextLoader
					.getCurrentWebApplicationContext();
			ServletContext servletContext = webApplicationContext
					.getServletContext();

			//发送周报邮件
			Map<String, Object> dataMap = new HashMap<String, Object>();
			dataMap.put("honorific", honorific);
//			exportWeeklyExcel(servletContext);
			EmailSendUtils.sendEmailForListContent(servletContext, to, cc, "扫码明细", dataMap,
					new File[]{exportWeeklyExcel(servletContext)}, "scan_detail_weekly_email.ftl");
			logger.info("sendScanDetailWeeklyReport end=============================");
			LogUtils.addInfoLog(1l, "OilVerificationBizService.sendScanDetailWeeklyReport", "周报发送成功");
		} catch (Exception e) {
			logger.error("sendScanDetailWeeklyReport error", e);
			LogUtils.addErrorLog(1l, "OilVerificationBizService.sendScanDetailWeeklyReport", "周报发送失败。" + e.getMessage(), null);
		}
	}

	//导出excel
	private File exportWeeklyExcel(ServletContext servletContext) throws Exception{
		Map<String, Object> paramMap = new HashMap<String, Object>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		Date dateTo = (Date) dateFormat.parseObject(dateFormat.format(calendar.getTime()));
		//3个月内扫码数据
		paramMap.put("dateFrom", DateUtils.addMonths(dateTo, -3));
		paramMap.put("dateTo", dateTo);
		List<OilVerification> dataList = oilVerificationMapper.queryScanDetailForEmail(paramMap);
		List<ExportCol> exportCols = new ArrayList<ExportCol>();
		ExportCol col = new ExportCol("partnerName", "合伙人名称", OilVerification.class);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("workshopName", "门店名称", OilVerification.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("regionName", "所属区域", OilVerification.class);
		col.setWidth(20);
		exportCols.add(col);
		col = new ExportCol("workshopAddress", "门店地址", OilVerification.class);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("sku", "SKU", OilVerification.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("productName", "产品名称", OilVerification.class);
		col.setWidth(45);
		exportCols.add(col);
		col = new ExportCol("capacity", "扫码升数(L)", OilVerification.class);
		col.setWidth(15);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		col = new ExportCol("creationTimeF", "扫码时间", OilVerification.class);
		col.setWidth(30);
		exportCols.add(col);
		//写入excel
		calendar.add(Calendar.DAY_OF_YEAR, -1);
		File excelFile = new File(
				servletContext.getRealPath("/app/扫码明细(至" + dateFormat.format(calendar.getTime()) + ").xlsx"));
		OutputStream outputStream = new FileOutputStream(excelFile);
		try {
			PoiWriteExcel.exportLargeData(dataList, exportCols, outputStream, "Sheet1", false);
		} finally {
			outputStream.close();
		}
		return excelFile;
	}

	/**
	 * 创建核销明细EXCEL文件
	 * @param dataList 核销明细数据
	 * @param fileName Excel文件名称
	 * @param servletContext Servlet上下文
	 * @return 创建的文件
	 * @throws IOException
	 */
	public static File createVerificationDetailExcel(List<OilVerification> dataList,
			String fileName, ServletContext servletContext) throws IOException {
		// 导出列定义
		List<ExportCol> exportCols = new ArrayList<ExportCol>();
		ExportCol col = new ExportCol("partnerName", "合伙人名称", OilVerification.class);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("workshopName", "门店名称", OilVerification.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("regionName", "所属区域", OilVerification.class);
		col.setWidth(20);
		exportCols.add(col);
		col = new ExportCol("workshopAddress", "门店地址", OilVerification.class);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("sku", "SKU", OilVerification.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("productName", "产品名称", OilVerification.class);
		col.setWidth(45);
		exportCols.add(col);
		col = new ExportCol("capacity", "扫码升数(L)", OilVerification.class);
		col.setWidth(15);
		col.setAlign(ExportColAlign.RIGHT);
		exportCols.add(col);
		col = new ExportCol("creationTimeF", "扫码时间", OilVerification.class);
		col.setWidth(30);
		exportCols.add(col);
		//写入excel
		File excelFile = new File(
				servletContext.getRealPath("/app/" + fileName + ".xlsx"));
		OutputStream outputStream = new FileOutputStream(excelFile);
		try {
			PoiWriteExcel.exportLargeData(dataList, exportCols, outputStream, "Sheet1", false);
		}catch (Exception e) {
			LogUtils.addErrorLog(1l, "OilVerificationBizService.createVerificationDetailExcel", "创建扫码明细Excel文件" + fileName + "失败", fileName);
		} finally {
			outputStream.close();
		}
		return excelFile;
	}
}
