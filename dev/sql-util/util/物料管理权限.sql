select u.ch_name, u.login_name,*
--delete ur
from wx_t_userrole ur 
left join wx_t_role r on ur.role_id=r.role_id
left join wx_t_user u on ur.user_id=u.user_id
where r.ch_role_name in ('Chevron_Material_Admin') and ur.user_id!=1

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, r.role_id,1
from wx_t_user u 
left join wx_t_role r on 1=1
where (u.type is null or u.type!='1') and u.status=1 and r.ch_role_name in ('Chevron_Material_Admin')
and u.login_name in ('13570236050',
'13570965620',
'13662315485',
'13500032285')
and not exists (select 1 from wx_t_userrole ur1 where ur1.user_id=u.user_id and ur1.role_id=r.role_id)

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, r.role_id,1
from wx_t_user u 
left join wx_t_role r on 1=1
where (u.type is null or u.type!='1') and u.status=1 and r.ch_role_name in ('Chevron_Material_Supplier')
and u.login_name in ('13632429835',
'13003427822')
and not exists (select 1 from wx_t_userrole ur1 where ur1.user_id=u.user_id and ur1.role_id=r.role_id)