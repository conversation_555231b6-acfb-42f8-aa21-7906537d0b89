insert into dw_access_control_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[customer_category]
      ,[customer_type]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[control_type]
      ,[control_type_name]
      ,[product_channel]
      ,[channel_weight]
      ,[from_source])
 select a.* from (
	  SELECT cs.[distributor_id]
      ,cs.[customer_name_cn]
      ,cs.[org_hier_id]
      ,'NC - 绩效' [region]
      ,cs.[sales_cai]
      ,cs.[sales_name_cn]
      ,cs.[customer_category]
      ,cs.[customer_type]
      ,cs.[create_time]
      ,cs.[update_time]
      ,cs.[del_flag]
      ,cs.[control_type]
      ,cs.[control_type_name]
      ,cs.[product_channel]
	  ,case when so.bu='Indirect' then (case when cs.[customer_category]='Consumer' 
			then 1 when cs.[customer_category]='Commercial' then 2 else 0 end) 
			when so.sales_channel='OEM' then 4 
			when so.sales_channel='Industrial' then 8
			else null end channel_weight
	  ,'PP' from_source
  FROM [PP_MID].[dbo].[syn_dw_to_pp_access_control_customer_org_sales] cs
  left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on cs.org_hier_id=so.org_hier_id
 ) a
  where not exists (select 1 from dw_access_control_customer_org_sales dac1 where dac1.from_source='PP' and a.region=dac1.region 
	and a.distributor_id=dac1.distributor_id and (a.channel_weight is null or a.channel_weight & dac1.channel_weight>0))
	and a.distributor_id=284
	