package com.chevron.promote.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.promote.business.ApproveHistoryService;
import com.chevron.promote.business.PromoteBizService;
import com.chevron.promote.dao.PromoteApplicationBatchMapper;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.PromoteApplicationBatch;
import com.chevron.promote.service.IPromoteDistributionWorkFlowService;
@Service
public class PromoteDistributionWorkFlowServiceImpl extends BasePromote implements IPromoteDistributionWorkFlowService{
	private static Logger log = LoggerFactory.getLogger(PromoteDistributionWorkFlowServiceImpl.class);
	@Resource
	ApproveHistoryService approveHisService;
	@Resource
	PromoteApplicationBatchMapper promoteApplyBatchMapper;
	@Resource
	PromoteBizService promoteBizService;
	
	@Override
	public Map<String, Object> queryWorkFlowTasks(String loginName,
			String pKey, String batchId) throws Exception {
		// TODO 后面有必要迁移到此，查询待审批的
		
		// 根据不同的用户，获取不同的审批列表
		return null;
	}
	
	@Override
	public Map<String, Object> approvePromoteDistribution(String applyBatchId) {
		// TODO v2
		return null;
	}
	
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> approvePromoteDistribution(Long applyBatchId,
			String status, String approveRemark, String version) {
		log.info("approvePromoteDistribution applyBatchId:{},status:{},approveRemark:{},version:{}",applyBatchId,status,approveRemark,version);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//1.根据批次号，版本号，定义类型判断是否有审批过
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("bachId", applyBatchId);
			reqMap.put("processKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION);
			reqMap.put("versionFlag", version);
			List<PromoteApplicationBatch> lst = promoteApplyBatchMapper.checkPromoteApplyHasApprovedByMap(reqMap);
			if(null!=lst && !lst.isEmpty())
			{
				throw new Exception("已经被审核，不能进行重复审核");
			}

			//2.在录入审批历史
			String stepDesc = ".Marketing审批";
			approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, applyBatchId, status, approveRemark,version+stepDesc);
			PromoteApplicationBatch promoteApply = new PromoteApplicationBatch();
			promoteApply.setBatchid(applyBatchId);
			promoteApply.setVersionFlag(""+(Integer.parseInt(version)+1));
			
			//3.如果是不通过，需要更新批次状态为驳回
			if(ApproveHistoryService.APPLY_STATUS_UNAPPROVED.equals(status))
			{
				promoteApply.setApproveStatus(status);
				promoteApply.setCurrentStep(PROMOTE_SUPERVISOR);//回到小区经理，让他重新修改，然后再提交
				//获取创建此活动的用户id
				PromoteApplicationBatch tmpPromoteApply = promoteApplyBatchMapper.selectByPrimaryKey(applyBatchId);
				promoteBizService.doSndMessageForApprove(PROMOTE_SUPERVISOR,tmpPromoteApply.getCreatorId(),ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, PROMOTE_SUPERVISOR_TIP+PROMOTE_DISTRIBUTION_UNAPPROVED);
				promoteBizService.doSndMailForApprove(PROMOTE_SUPERVISOR,tmpPromoteApply.getCreatorId(),ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, PROMOTE_SUPERVISOR_TIP+PROMOTE_DISTRIBUTION_UNAPPROVED);

			}else
			{
				// TODO 判断是否存在下一级审批
				//.....
				boolean isNextApprove = false;
				if(!isNextApprove)
				{
					//如果不存在下一级审批
					promoteApply.setApproveStatus(status);
				}else
				{
					//存在下一级审批
				}
			}
			//最后都需要更新版本号
			promoteApplyBatchMapper.updateByPrimaryKeySelective(promoteApply);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("approvePromoteDistribution"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryPromoteDistributionByBatchId(Long batchId) {
		return null;
	}
	
	
	@Override
	public Map<String, Object> queryPromoteDistributionApprovalHistory(
			Long batchId) {
		log.info("queryPromoteDistributionApprovalHistory batchId:{}",batchId);
		Map<String,Object> returnMap = approveHisService.getApproveHisByBatchId(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, batchId);
		return returnMap;
	}
	
}
