$(function() {
	datachartpage_init.init();
});

var datachartpage_init = (function($){
	
	var salesBar = null,
	salesPie = null,
	
	req = {
			getMonthlySellInfoDetail: function(callback) {
				$.get(common.ctx + "newhomepage/monthlySellInfoDetail.do", function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getQuarterlySellInfoDetail: function(callback) {
				$.get(common.ctx + "newhomepage/quarterlySellInfoDetail.do", function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getSellInfoDetail: function(productTypeCode, callback) {
				$.get(common.ctx + "newhomepage/sellInfoDetail.do", {yearType: type, productTypeCode: productTypeCode}, function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			}
	},
	util = {},
	loader = {
			loadSalesBarMonthly: function() {
				req.getMonthlySellInfoDetail(function(data){
					var xDataValue = [];
					var yDataValue = [];
					var salesPieLoadFlag = false;
					for(var i = 0; i < data.length; i++) {
						//X轴标题
						xDataValue.push(data[i].productType);
						//Y轴数据
						yDataValue.push({
							code: data[i].productTypeCode,
							name: data[i].productType,
							value: data[i].sellIn
						});
						//数据加载
						if(data[i].productTypeCode == 'HC' && data[i].sellIn > 0) {
							salesPieLoadFlag = true;
						}
					}
					
					salesBar = echarts.init($('#sales-bar')[0]);
					$(window).resize(salesBar.resize);
					
					var salesBarOption = {
						color:['#009dd9'],
					    title: {
					        text: '月度进货完成率对比',
					        left: 'center'
					    },
					    tooltip: {
					        trigger: 'axis',
					        axisPointer: {
					            type: 'cross',
					            crossStyle: {
					                color: '#999'
					            }
					        }
					    },
					    legend: {
					        data:['月实际进货量'],
					        left: 'center',
					        bottom: 10,
					    },
					    xAxis: [{
					            type: 'category',
					            data: xDataValue,
					            axisPointer: {
					                type: 'shadow'
					            }
				        }],
					    yAxis: [{
					            type: 'value',
					            name: '月实际进货量',
					            min: 0,
					            axisLabel: {
					                formatter: '{value} L'
					            }
				        }],
					    series: [{
					            name:'月实际进货量',
					            type:'bar',
					            barWidth: '20%',
					            label: {
					                normal: {
					                    color: '#000000',
					                    show: true,
					                    position: 'top'
					                }
					            },
					            data:yDataValue
				        }]
					};
					
					salesBar.setOption(salesBarOption);
					
					salesBar.on('click', function(params) {
						req.getSellInfoDetail(params.data.code, function(data){
							loader.loadSalesPie(data, params.data.name);
						});
					});
					
					if(salesPieLoadFlag) {
						req.getSellInfoDetail('HC', function(data){
							loader.loadSalesPie(data, '全合成');
						});
					}
				});
			},
			loadSalesBarQuarterly: function() {
				req.getQuarterlySellInfoDetail(function(data){
					var xDataValue = [];
					var yDataValue = [];
					var salesPieLoadFlag = false;
					for(var i = 0; i < data.length; i++) {
						xDataValue.push(data[i].productType);
						yDataValue.push({
							code: data[i].productTypeCode,
							name: data[i].productType,
							value: data[i].sellIn
						});
						//数据加载
						if(data[i].productTypeCode == 'HC' && data[i].sellIn > 0) {
							salesPieLoadFlag = true;
						}
					}
					
					salesBar = echarts.init($('#sales-bar')[0]);
					$(window).resize(salesBar.resize);
					
					var salesBarOption = {
						color:['#009dd9'],
					    title: {
					        text: '季度进货完成率对比',
					        left: 'center'
					    },
					    tooltip: {
					        trigger: 'axis',
					        axisPointer: {
					            type: 'cross',
					            crossStyle: {
					                color: '#999'
					            }
					        }
					    },
					    legend: {
					        data:['季度实际进货量'],
					        left: 'center',
					        bottom: 10,
					    },
					    xAxis: [{
					            type: 'category',
					            data: xDataValue,
					            axisPointer: {
					                type: 'shadow'
					            }
				        }],
					    yAxis: [{
					            type: 'value',
					            name: '季度实际进货量',
					            min: 0,
					            axisLabel: {
					                formatter: '{value} L'
					            }
				        }],
					    series: [{
					            name:'季度实际进货量',
					            type:'bar',
					            barWidth: '20%',
					            label: {
					                normal: {
					                    color: '#000000',
					                    show: true,
					                    position: 'top'
					                }
					            },
					            data:yDataValue
				        }]
					};
					
					salesBar.setOption(salesBarOption);
					
					salesBar.on('click', function(params) {
						req.getSellInfoDetail(params.data.code, function(data){
							loader.loadSalesPie(data, params.data.name);
						});
					});
					
					if(salesPieLoadFlag) {
						req.getSellInfoDetail('HC', function(data){
							loader.loadSalesPie(data, '全合成');
						});
					}
				});
			},
			loadSalesPie: function(productData, productType) {
				var viewData = [];
				for(var i = 0; i < productData.length; i++) {
					var viscosity = null;
					if(productData[i].viscosity == '') {
						viscosity = '其他';
					} else {
						viscosity = productData[i].viscosity;
					}
					
					viewData.push({
						name: viscosity,
						value: productData[i].sellIn.toFixed(0)
					});
				}
				
				
				if(salesPie == null) {
					salesPie = echarts.init($('#sales-pie')[0]);
					$(window).resize(salesPie.resize);
				}
				
				salesPieOption = {
					title : {
				        text: productType + '产品进货量明细(L)',
				        x:'center'
				    },
				    tooltip : {
				        trigger: 'item',
				        formatter: "{b} : {c} ({d}%)"
				    },
				    color : [ '#DEB948', '#43B5AD', '#578EBE', '#8674A6', '#E21836'],
				    series : [{
				            name: 'productType',
				            type: 'pie',
				            radius : '55%',
				            center: ['50%', '45%'],
				            label: {
		                    	normal: {
		                    		formatter: ['{b}',
			                                    '{c}',
			                                    '{d}%'].join('\n'),
		                    	}
		                    },
				            data: viewData,
				            itemStyle: {
				                emphasis: {
				                    shadowBlur: 10,
				                    shadowOffsetX: 0,
				                    shadowColor: 'rgba(0, 0, 0, 0.5)'
				                }
				            }
				        }
				    ]	
				};
				
				salesPie.setOption(salesPieOption);
			}
	};
	
	return {
		init: function() {
			if(type == "monthly") {
				loader.loadSalesBarMonthly();
			} else if(type == "quarterly") {
				loader.loadSalesBarQuarterly();
			}
		},
		returnPage: function () {
			window.history.go(-1);
		},
	};
}($));