--CDM陈列大比拼&店招DMS匹配信息
select o.organization_name "经销商名称", t1.work_shop_name "门店名称",
case when t1.dms_key is not null and t1.dms_key!='' then '已匹配' 
when exists (select top 1 dms_key
from (select dw.[Customer_Code] work_shop_code, dw.[Customer_Name] work_shop_name, 
dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person, dw.[Customer_Tel] contact_person_tel,
 dw.DSR_Name execute_user_name, dw.[Distributor_Code] + '/' + dw.[Customer_Code] dms_key, 
 (SELECT sum(st.[SELL THROUGH L]) sell_through FROM [dw_dms_sell_through] st where st.[QBR CATEGORY] is not null 
 and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code] and 
 SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume, (case when (t1.workshop_owner_mobile is not null and 
 len(t1.workshop_owner_mobile)>6 and charindex(t1.workshop_owner_mobile,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0) 
 or (t1.contact_person_tel is not null and len(t1.contact_person_tel)>6 and charindex(t1.contact_person_tel,dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end) + (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and 
 (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, t1.workshop_owner_mobile)>0 or charindex(dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS, t1.contact_person_tel)>0) then 3000 else 0 end) + (case when charindex(t1.work_shop_address, 
 dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end) + (case when charindex('号', t1.work_shop_address) > 0 
 then 100 else 0 end) + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, t1.work_shop_address) > 0 then 100 else 0 end) + 
 (case when len(t1.work_shop_name)>1 and charindex(t1.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end) + 
 (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, t1.work_shop_name) > 0 then 1000 else 0 end) + 
 (case when t1.shop_recruitment=1 then 8 else 0 end) + (case when t1.join_location_plan=1 then 4 else 0 end) + (case when exists 
 (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 2 else 0 end) + (case when t1.from_source=2 then 
 1 else 0 end) + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight from dw_dms_workshop dw left join 
 wx_t_partner_o2o_enterprise pe on dw.distributor_id=pe.distributor_id left join wx_t_workshop_partner wp on wp.partner_id=pe.partner_id 
 where wp.workshop_id=t1.id) a_0 where a_0.match_weight>299 order by a_0.match_weight desc) then '建议匹配'
 else '未匹配' end "匹配状态", case when x.type & 3 > 0 then '是' else '否' end "店招店", case when x.type & 4 > 0 then '是' else '否' end "参与陈列之星", x.asm_audit_time2 "店招提交时间",
 x.status "陈列之星抽查状态", x.submit_time "陈列之星提交时间" from (select max(xx1.asm_audit_time2) asm_audit_time2,
 max(partner_id) partner_id, max(expense_value) expense_value, max(field_value) field_value, sum(type) type, max(status) status,
 max(submit_time) submit_time from (
select ma.asm_audit_time2, eop.partner_id, isnull(ma.price,0) expense_value, 
mf.field_value, 1 type, null status, null submit_time, 1 rn
from view_mkt_apply ma
left join wx_t_mkt_field mf on ma.id=mf.mkt_apply_id and mf.field_name='workShopId'
 INNER JOIN wx_t_partner_o2o_enterprise eop ON eop.distributor_id = ma.partner_id
	 where ma.mkt_type in ('STORE_FRONT')
	 and ma.asm_audit_status2=1
union all SELECT isnull(w.shop_recruitment_update_time, '2019-04-30') month,
wp.partner_id,recruitment_amount expense_value, w.id, 2, null, null, 1 rn
FROM wx_t_work_shop w
 INNER JOIN wx_t_workshop_partner wp ON w.id = wp.workshop_id
WHERE recruitment_amount IS NOT NULL AND recruitment_amount>0  
union all select
null, m.tenant_id, null expense_value, 
s.org_id, 4 type, CASE 
		WHEN s.task_status = 3 THEN '不合格'
		WHEN s.check_evaluation > 0 THEN '合格'
		ELSE '未抽查'
	END AS status, s.xg_sj submit_time,
ROW_NUMBER() OVER (PARTITION BY m.tenant_id, s.org_id ORDER BY s.task_status desc, isnull(s.check_evaluation, -1) desc, s.xg_sj DESC) AS rn
FROM wx_task_main m
	LEFT JOIN wx_task_sub s ON m.task_main_id = s.task_main_id
WHERE m.tmb_type_code = 'TT_2_CDMCLZX'
	AND s.task_status IN ('3', '4')
	and m.tenant_id !=9) xx1
	where xx1.rn=1
	group by xx1.field_value
) x 
left join wx_t_organization o on o.id=x.partner_id
LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
left join wx_t_work_shop t1 on x.field_value=t1.id
--where exists (select 1 from dw_dms_workshop dw where dw.distributor_id=pe.distributor_id)
--group by x.field_value
order by o.id, t1.id
--having count(1)>1

--DMS门店信息
select dw.Distributor_Name "经销商名称", dw.Distributor_Code "经销商编码", count(1) "门店数" from [Data_Interface_Customer_Info] dw group by dw.Distributor_Name, dw.Distributor_Code

--陈列之星DMS信息
select o.organization_name "经销商名称", t1.work_shop_name "门店名称",
case when t1.dms_key is not null and t1.dms_key!='' then '已匹配' 
when exists (select top 1 dms_key
from (select dw.[Customer_Code] work_shop_code, dw.[Customer_Name] work_shop_name, 
dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person, dw.[Customer_Tel] contact_person_tel,
 dw.DSR_Name execute_user_name, dw.[Distributor_Code] + '/' + dw.[Customer_Code] dms_key, 
 (SELECT sum(st.[SELL THROUGH L]) sell_through FROM [dw_dms_sell_through] st where st.[QBR CATEGORY] is not null 
 and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code] and 
 SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume, (case when (t1.workshop_owner_mobile is not null and 
 len(t1.workshop_owner_mobile)>6 and charindex(t1.workshop_owner_mobile,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0) 
 or (t1.contact_person_tel is not null and len(t1.contact_person_tel)>6 and charindex(t1.contact_person_tel,dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end) + (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and 
 (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, t1.workshop_owner_mobile)>0 or charindex(dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS, t1.contact_person_tel)>0) then 3000 else 0 end) + (case when charindex(t1.work_shop_address, 
 dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end) + (case when charindex('号', t1.work_shop_address) > 0 
 then 100 else 0 end) + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, t1.work_shop_address) > 0 then 100 else 0 end) + 
 (case when len(t1.work_shop_name)>1 and charindex(t1.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end) + 
 (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, t1.work_shop_name) > 0 then 1000 else 0 end) + 
 (case when t1.shop_recruitment=1 then 8 else 0 end) + (case when t1.join_location_plan=1 then 4 else 0 end) + (case when exists 
 (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 2 else 0 end) + (case when t1.from_source=2 then 
 1 else 0 end) + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight from dw_dms_workshop dw left join 
 wx_t_partner_o2o_enterprise pe on dw.distributor_id=pe.distributor_id left join wx_t_workshop_partner wp on wp.partner_id=pe.partner_id 
 where wp.workshop_id=t1.id) a_0 where a_0.match_weight>299 order by a_0.match_weight desc) then '建议匹配'
 else '未匹配' end "匹配状态",
CASE 
		WHEN s.task_status = 3 THEN '不合格'
		WHEN s.is_pass = 1 THEN '合格'
		ELSE '未抽查'
	END AS "状态", s.xg_sj "提交时间"
FROM wx_task_main m
	LEFT JOIN wx_task_sub s ON m.task_main_id = s.task_main_id
	LEFT JOIN wx_t_work_shop t1 ON s.org_id = t1.id
	LEFT JOIN wx_t_organization o ON o.id = m.tenant_id
	LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
	/*LEFT JOIN dw_customer_region_sales_supervisor_rel crss
	ON crss.distributor_id = pe.distributor_id
		AND crss.region_name LIKE 'CDM%'*/
WHERE m.tmb_type_code = 'TT_2_CLZX'
	AND s.task_status IN ('3', '4')
	and o.id !=9
	and exists (select 1 from dw_dms_workshop dw where dw.distributor_id=pe.distributor_id)
--group by o.id, o.organization_name, CONVERT(varchar(7), s.xg_sj, 23)
order by o.id,t1.id

--CDM陈列之星DMS信息
select o.organization_name "经销商名称", t1.work_shop_name "门店名称", 
case when t1.dms_key is not null and t1.dms_key!='' then '已匹配' 
when exists (select top 1 dms_key
from (select dw.[Customer_Code] work_shop_code, dw.[Customer_Name] work_shop_name, 
dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person, dw.[Customer_Tel] contact_person_tel,
 dw.DSR_Name execute_user_name, dw.[Distributor_Code] + '/' + dw.[Customer_Code] dms_key, 
 (SELECT sum(st.[SELL THROUGH L]) sell_through FROM [dw_dms_sell_through] st where st.[QBR CATEGORY] is not null 
 and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code] and 
 SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume, (case when (t1.workshop_owner_mobile is not null and 
 len(t1.workshop_owner_mobile)>6 and charindex(t1.workshop_owner_mobile,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0) 
 or (t1.contact_person_tel is not null and len(t1.contact_person_tel)>6 and charindex(t1.contact_person_tel,dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end) + (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and 
 (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, t1.workshop_owner_mobile)>0 or charindex(dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS, t1.contact_person_tel)>0) then 3000 else 0 end) + (case when charindex(t1.work_shop_address, 
 dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end) + (case when charindex('号', t1.work_shop_address) > 0 
 then 100 else 0 end) + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, t1.work_shop_address) > 0 then 100 else 0 end) + 
 (case when len(t1.work_shop_name)>1 and charindex(t1.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end) + 
 (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, t1.work_shop_name) > 0 then 1000 else 0 end) + 
 (case when t1.shop_recruitment=1 then 8 else 0 end) + (case when t1.join_location_plan=1 then 4 else 0 end) + (case when exists 
 (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 2 else 0 end) + (case when t1.from_source=2 then 
 1 else 0 end) + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight from dw_dms_workshop dw left join 
 wx_t_partner_o2o_enterprise pe on dw.distributor_id=pe.distributor_id left join wx_t_workshop_partner wp on wp.partner_id=pe.partner_id 
 where wp.workshop_id=t1.id) a_0 where a_0.match_weight>299 order by a_0.match_weight desc) then '建议匹配'
 else '未匹配' end "匹配状态",
 x.status "状态", x.xg_sj "提交时间"
 from (select
 CASE 
		WHEN s.task_status = 3 THEN '不合格'
		WHEN s.check_evaluation > 0 THEN '合格'
		ELSE '未抽查'
	END AS status,
s.xg_sj, s.org_id, m.tenant_id,
ROW_NUMBER() OVER (PARTITION BY m.tenant_id, s.org_id ORDER BY s.task_status desc, isnull(s.check_evaluation, -1) desc, s.xg_sj DESC) AS rn
FROM wx_task_main m
	LEFT JOIN wx_task_sub s ON m.task_main_id = s.task_main_id
WHERE m.tmb_type_code = 'TT_2_CDMCLZX'
	AND s.task_status IN ('3', '4')
	and m.tenant_id !=9
	) x
	LEFT JOIN wx_t_work_shop t1 ON x.org_id = t1.id
	LEFT JOIN wx_t_organization o ON o.id = x.tenant_id
	LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
	/*LEFT JOIN dw_customer_region_sales_supervisor_rel crss
	ON crss.distributor_id = pe.distributor_id
		AND crss.region_name LIKE 'CDM%'*/
	where x.rn=1 and exists (select 1 from dw_dms_workshop dw where dw.distributor_id=pe.distributor_id)
--group by o.id, o.organization_name, t1.id, t1.work_shop_name
order by o.id, t1.id

--经销商门店DMS信息
select o.organization_name "经销商名称", t1.work_shop_name "门店名称", t1.work_shop_address "门店地址",
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.fromSource' and di1.dic_item_code=t1.from_source) "门店来源",
(case when t1.status='0' then '潜在门店' when t1.status='1' then '已功店' when t1.status='3' then '合作门店' else '' end) "门店状态",
case when t1.dms_key is not null and t1.dms_key!='' then '已匹配' 
when exists (select top 1 dms_key
from (select dw.[Customer_Code] work_shop_code, dw.[Customer_Name] work_shop_name, 
dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person, dw.[Customer_Tel] contact_person_tel,
 dw.DSR_Name execute_user_name, dw.[Distributor_Code] + '/' + dw.[Customer_Code] dms_key, 
 (SELECT sum(st.[SELL THROUGH L]) sell_through FROM [dw_dms_sell_through] st where st.[QBR CATEGORY] is not null 
 and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code] and 
 SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume, (case when (t1.workshop_owner_mobile is not null and 
 len(t1.workshop_owner_mobile)>6 and charindex(t1.workshop_owner_mobile,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0) 
 or (t1.contact_person_tel is not null and len(t1.contact_person_tel)>6 and charindex(t1.contact_person_tel,dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end) + (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and 
 (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, t1.workshop_owner_mobile)>0 or charindex(dw.[Customer_Tel] 
 collate Chinese_PRC_CI_AS, t1.contact_person_tel)>0) then 3000 else 0 end) + (case when charindex(t1.work_shop_address, 
 dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end) + (case when charindex('号', t1.work_shop_address) > 0 
 then 100 else 0 end) + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, t1.work_shop_address) > 0 then 100 else 0 end) + 
 (case when len(t1.work_shop_name)>1 and charindex(t1.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end) + 
 (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, t1.work_shop_name) > 0 then 1000 else 0 end) + 
 (case when t1.shop_recruitment=1 then 8 else 0 end) + (case when t1.join_location_plan=1 then 4 else 0 end) + (case when exists 
 (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 2 else 0 end) + (case when t1.from_source=2 then 
 1 else 0 end) + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight from dw_dms_workshop dw left join 
 wx_t_partner_o2o_enterprise pe on dw.distributor_id=pe.distributor_id left join wx_t_workshop_partner wp on wp.partner_id=pe.partner_id 
 where wp.workshop_id=t1.id) a_0 where a_0.match_weight>299 order by a_0.match_weight desc) then '建议匹配'
 else '未匹配' end "匹配状态", (select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=t1.id order by wss.create_time desc) "录店时间" from wx_t_work_shop t1
 left join wx_t_workshop_partner wp on wp.workshop_id=t1.id 
left join wx_t_organization o on o.id=wp.partner_id
LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
where o.id <> 9 and--exists (select 1 from dw_dms_workshop dw where dw.distributor_id=pe.distributor_id)
t1.delete_flag=0 and convert(int, t1.status)>=0
--and t1.from_source=1 and t1.status='3'
--group by x.field_value
order by o.id, t1.id