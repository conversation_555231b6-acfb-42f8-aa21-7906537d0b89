package com.chevron.pmp.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sys.auth.dao.WxTOrgMapper;
import com.chevron.userselect.service.impl.OrgTreeService;
import com.chevron.pmp.service.BaseDataService;
import com.chevron.pms.service.RegionService;

@Service
public class BaseDataServiceImpl implements BaseDataService {
	@Resource
	private WxTOrgMapper wxTOrgMapper;
	
	@Resource
	OrgTreeService orgTreeService;
	
	@Resource
	RegionService regionServiceImpl;
	
	@Override
	public Map<String, Object> findAllProvinces() {
		
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", regionServiceImpl.selChildrenByParentId(1l));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> findCities(Long provId) {
		return regionServiceImpl.findCities(provId);
	}

	@Override
	public Map<String, Object> findDistricts(Long cityId) {
		return regionServiceImpl.findDistricts(cityId);
	}

	@Override
	public Map<String, Object> findDistrictsByProvinceId(Long provinceId) {
		return regionServiceImpl.findDistrictsByProvinceId(provinceId);
	}

	@Override
	public Map<String, Object> findFullRegionByDist(Long distId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", wxTOrgMapper.selFullRegionByDist(distId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

}
