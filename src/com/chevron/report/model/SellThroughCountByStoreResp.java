package com.chevron.report.model;

import com.wordnik.swagger.annotations.ApiModel;
import com.wordnik.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel
public class SellThroughCountByStoreResp {
    @ApiModelProperty(value = "premiumCount",name = "金盾升数")
    private BigDecimal premiumCount;

    @ApiModelProperty(value = "valueCount",name = "银盾升数")
    private BigDecimal valueCount;

    @ApiModelProperty(value = "ecoCount",name = "星盾升数")
    private BigDecimal ecoCount;

    @ApiModelProperty(value = "entryCount",name = "蓝盾升数")
    private BigDecimal entryCount;

    @ApiModelProperty(value = "otherCount",name = "其他全合成类型升数")
    private BigDecimal otherCount;

    @ApiModelProperty(value = "saleMonth",name = "销售月份")
    private Integer saleMonth;

    @ApiModelProperty(value = "yearMonth",name = "销售年月")
    private String yearMonth;

    @ApiModelProperty(value = "endCustomerCode",name = "门店编码")
    private String endCustomerCode;

    @ApiModelProperty(value = "t3Category",name = "产品类型")
    private String t3Category;

    @ApiModelProperty(value = "workShopName",name = "门店名称")
    private String workShopName;

    @ApiModelProperty(value = "countMonth",name = "单项类型升数汇总")
    private BigDecimal categoryCount;

    @ApiModelProperty(value = "productChannel",name = "单项类型升数汇总")
    private String productChannel;

    @ApiModelProperty(value = "consumerTotal",name = "乘用车合计")
    private BigDecimal consumerCount;

    @ApiModelProperty(value = "commercialTotal",name = "商用油合计")
    private BigDecimal commercialCount;

    @ApiModelProperty(value = "totalCount",name = "总计")
    private BigDecimal totalCount;

    @ApiModelProperty(value = "qhcCount",name = "全合成总计")
    private BigDecimal qhcCount;

    @ApiModelProperty(value = "monthCount",name = "月度总计")
    private BigDecimal monthCount;

    public BigDecimal getPremiumCount() {
        return premiumCount;
    }

    public void setPremiumCount(BigDecimal premiumCount) {
        this.premiumCount = premiumCount;
    }

    public BigDecimal getValueCount() {
        return valueCount;
    }

    public void setValueCount(BigDecimal valueCount) {
        this.valueCount = valueCount;
    }

    public BigDecimal getEcoCount() {
        return ecoCount;
    }

    public void setEcoCount(BigDecimal ecoCount) {
        this.ecoCount = ecoCount;
    }

    public BigDecimal getEntryCount() {
        return entryCount;
    }

    public void setEntryCount(BigDecimal entryCount) {
        this.entryCount = entryCount;
    }

    public BigDecimal getOtherCount() {
        return otherCount;
    }

    public void setOtherCount(BigDecimal otherCount) {
        this.otherCount = otherCount;
    }

    public Integer getSaleMonth() {
        return saleMonth;
    }

    public void setSaleMonth(Integer saleMonth) {
        this.saleMonth = saleMonth;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getEndCustomerCode() {
        return endCustomerCode;
    }

    public void setEndCustomerCode(String endCustomerCode) {
        this.endCustomerCode = endCustomerCode;
    }

    public String getT3Category() {
        return t3Category;
    }

    public void setT3Category(String t3Category) {
        this.t3Category = t3Category;
    }

    public String getWorkShopName() {
        return workShopName;
    }

    public void setWorkShopName(String workShopName) {
        this.workShopName = workShopName;
    }

    public BigDecimal getCategoryCount() {
        return categoryCount;
    }

    public void setCategoryCount(BigDecimal categoryCount) {
        this.categoryCount = categoryCount;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public BigDecimal getConsumerCount() {
        return consumerCount;
    }

    public void setConsumerCount(BigDecimal consumerCount) {
        this.consumerCount = consumerCount;
    }

    public BigDecimal getCommercialCount() {
        return commercialCount;
    }

    public void setCommercialCount(BigDecimal commercialCount) {
        this.commercialCount = commercialCount;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getQhcCount() {
        return qhcCount;
    }

    public void setQhcCount(BigDecimal qhcCount) {
        this.qhcCount = qhcCount;
    }

    public BigDecimal getMonthCount() {
        return monthCount;
    }

    public void setMonthCount(BigDecimal monthCount) {
        this.monthCount = monthCount;
    }
}
