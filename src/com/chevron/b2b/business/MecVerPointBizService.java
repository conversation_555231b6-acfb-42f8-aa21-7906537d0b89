package com.chevron.b2b.business;

import com.chevron.b2b.model.MecVerPoint;
import com.chevron.b2b.model.MecVerPointExample;
import com.chevron.b2b.model.MecVerPointParams;
import com.common.exception.WxPltException;

import java.util.List;
import java.util.Map;

/**
 * 技师核销积分业务接口
 * <AUTHOR>
 * @version 1.0 2018-11-29 15:46
 */
public interface MecVerPointBizService {
	
	/**
	 * 保存技师核销积分
	 * @param record 被插入技师核销积分
	 * @throws WxPltException
	 */
	public void insert(MecVerPoint record) throws WxPltException;
	
	/**
	 * 修改技师核销积分
	 * @param record 被修改技师核销积分
	 * @throws WxPltException
	 */
	public void update(MecVerPoint record) throws WxPltException;
	
	/**
	 * 删除技师核销积分
	 * @param ids 被删除技师核销积分id集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的技师核销积分
	 * @param example 被删除技师核销积分满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(MecVerPointExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<MecVerPoint> queryByExample(MecVerPointExample example) throws WxPltException;
	
	/**
	 * 列表查询技师核销积分对象
	 * @param params 查询条件
	 * @return 满足条件技师核销积分对象集合
	 */
    public List<MecVerPoint> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 获取指定主键的技师核销积分对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public MecVerPoint getBean(Long id) throws WxPltException;	

	/**
	 * 分页查询技师核销积分
	 * @param params 查询参数
	 * @param resultMap 结果对象
	 * @throws WxPltException
	 */
	public void queryForPage(MecVerPointParams params, Map<String, Object> resultMap) throws WxPltException;
	
	public void queryForFreezeDetailPage(MecVerPointParams params, Map<String, Object> resultMap) throws WxPltException;
	
	public void queryForUnfreezeDetailPage(MecVerPointParams params, Map<String, Object> resultMap) throws WxPltException;
	
	public void unfreeze(MecVerPointParams params) throws WxPltException;
	
    public List<MecVerPoint> queryForMechanicByParams(Map<String, Object> params) throws WxPltException;
    
    public double getMechanicFreezedVerifyPoints(String mechanicCode, String salesChannel) throws WxPltException;
    
    /**
     * 瓶盖扫码分页
     * @param params
     * @param resultMap
     * @throws WxPltException
     */
    public void queryCapForPage(MecVerPointParams params, Map<String, Object> resultMap) throws WxPltException;
}
