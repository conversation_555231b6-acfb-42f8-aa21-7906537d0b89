select o.organization_name, w.work_shop_name, dw.customer_name
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
where w.status='3' and w.dms_key is not null and w.delete_flag=0
order by o.id, w.work_shop_name

--未找到匹配DMS门店20201123
select o.organization_name "经销商名称", w.customer_name "PP门店名称",w.dms_key, SUBSTRING(w.dms_key, 1, CHARINDEX('/',w.dms_key) - 1) dms_distributor_code, 
SUBSTRING(w.dms_key, CHARINDEX('/',w.dms_key) + 1, len(w.dms_key) - CHARINDEX('/',w.dms_key)) dms_customer_code
from PP_MID.[dbo].[pp_distributor_customer20201112] w
left join wx_t_organization o on o.id=w.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null
order by o.id, w.customer_name


select x.* from (
select o.organization_name "经销商名称", w.customer_name "PP门店名称",w.dms_key, SUBSTRING(w.dms_key, 1, CHARINDEX('/',w.dms_key) - 1) dms_distributor_code, 
SUBSTRING(w.dms_key, CHARINDEX('/',w.dms_key) + 1, len(w.dms_key) - CHARINDEX('/',w.dms_key)) dms_customer_code
from PP_MID.[dbo].[pp_distributor_customer20201112] w
left join wx_t_organization o on o.id=w.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null
--order by o.id, w.customer_name
) x 
left join dw_dms_workshop_merge dwm on dwm.DistributorCode collate Chinese_PRC_CI_AS=x.dms_distributor_code and dwm.customercode collate Chinese_PRC_CI_AS=x.dms_customer_code
left join dw_dms_workshop  dw1 on dw1.[Distributor_Code]=dwm.DistributorCode collate Chinese_PRC_CI_AS and dw1.[Customer_Code]=dwm.proc_customercode collate Chinese_PRC_CI_AS

where dw1.Distributor_Code is null

select o.organization_name "经销商名称", w.work_shop_name "PP门店名称",w.dms_key, SUBSTRING(w.dms_key, 1, CHARINDEX('/',w.dms_key) - 1) dms_distributor_code, 
SUBSTRING(w.dms_key, CHARINDEX('/',w.dms_key) + 1, len(w.dms_key) - CHARINDEX('/',w.dms_key)) dms_customer_code,
dwm.DistributorCode + '/' + dwm.proc_customercode
--update w set w.dms_key=dwm.DistributorCode + '/' + dwm.proc_customercode
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
left join dw_dms_workshop_merge dwm on w.dms_key=dwm.DistributorCode + '/' + dwm.customercode
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null and dwm.DistributorCode is not null

select o.organization_name "经销商名称", w.customer_name "PP门店名称",w.dms_key, SUBSTRING(w.dms_key, 1, CHARINDEX('/',w.dms_key) - 1) dms_distributor_code, 
SUBSTRING(w.dms_key, CHARINDEX('/',w.dms_key) + 1, len(w.dms_key) - CHARINDEX('/',w.dms_key)) dms_customer_code,
dwm.DistributorCode + '/' + dwm.proc_customercode
--update w set w.dms_key=dwm.DistributorCode + '/' + dwm.proc_customercode,w.dms_customer_code=dwm.proc_customercode,w.dms_distributor_code=dwm.DistributorCode
from PP_MID.[dbo].[pp_distributor_customer20201112] w
left join wx_t_organization o on o.id=w.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
left join dw_dms_workshop_merge dwm on w.dms_key=dwm.DistributorCode + '/' + dwm.customercode
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null and dwm.DistributorCode is not null

select o.organization_name "经销商名称", w.work_shop_name "PP门店名称",w.dms_key, SUBSTRING(w.dms_key, 1, CHARINDEX('/',w.dms_key) - 1) dms_distributor_code, 
SUBSTRING(w.dms_key, CHARINDEX('/',w.dms_key) + 1, len(w.dms_key) - CHARINDEX('/',w.dms_key)) dms_customer_code,
dwm.DistributorCode + '/' + dwm.proc_customercode
--update w set w.dms_key=null
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
left join dw_dms_workshop_merge dwm on w.dms_key=dwm.DistributorCode + '/' + dwm.customercode
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null and dwm.DistributorCode is null

/* 同步DMS KEY */
insert into wx_log (operator,log_type,ext_property1,ext_property2,ext_property3,ext_property4,ext_property5, create_time)
select 1, 'update_dms_key','dms_workshop_merged', w.id,w.dms_key, 
dwm.DistributorCode, dwm.proc_customercode,getdate()
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
left join PP_MID.[dbo].[Data_Interface_Customer_Merge] dwm on w.dms_key=dwm.DistributorCode + '/' + dwm.customercode
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null and dwm.DistributorCode is not null
and not exists (select 1 from wx_t_work_shop w1 where w1.dms_key=dwm.DistributorCode + '/' + dwm.proc_customercode and w1.delete_flag=0)

update w set w.dms_key=dwm.DistributorCode + '/' + dwm.proc_customercode
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
left join PP_MID.[dbo].[Data_Interface_Customer_Merge] dwm on w.dms_key=dwm.DistributorCode + '/' + dwm.customercode
where  w.dms_key is not null and w.delete_flag=0
and dw.[Customer_Code] is null and dwm.DistributorCode is not null
and not exists (select 1 from wx_t_work_shop w1 where w1.dms_key=dwm.DistributorCode + '/' + dwm.proc_customercode and w1.delete_flag=0)

insert into wx_log (operator,log_type,ext_property1,ext_property2,ext_property3, create_time)
select 1, 'update_dms_key','dms_key_repetition', w.id,w.dms_key, getdate()
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
where  w.dms_key is not null and w.delete_flag=0
and exists (select 1 from wx_t_work_shop w1 where w1.dms_key=w.dms_key and w1.id<w.id and w1.delete_flag=0)

update w set w.dms_key=null
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
where  w.dms_key is not null and w.delete_flag=0
and exists (select 1 from wx_t_work_shop w1 where w1.dms_key=w.dms_key and w1.id<w.id and w1.delete_flag=0)

insert into wx_log (operator,log_type,ext_property1,ext_property2,ext_property3, create_time)
select 1, 'update_dms_key','dms_workshop_not_exists', w.id,w.dms_key, getdate()
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
where  w.dms_key is not null and w.delete_flag=0 and dw.[Customer_Code] is null

update w set w.dms_key=null
from wx_t_work_shop w
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_organization o on o.id=wp.partner_id
left join dw_dms_workshop  dw on w.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code]
where  w.dms_key is not null and w.delete_flag=0 and dw.[Customer_Code] is null