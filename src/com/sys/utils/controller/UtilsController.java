package com.sys.utils.controller;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.sys.file.web.FileManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.EmailSendUtils;
import com.common.util.FileUtil;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.WeixinMessageUtil;
import com.redis.tockenMgnt.service.TokenMgntService;
import com.sys.auth.business.UserBizService;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserExample;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.BaiDuBizService;
import com.sys.utils.business.EtoBizService;
import com.sys.utils.business.LockerBizService;
import com.sys.utils.model.AsynProcessStatus;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;

import net.sf.json.JSONObject;

@Controller
@Api(value = "通用接口Controller", tags = "通用接口Controller")
public class UtilsController {
	
	private static Logger log = LoggerFactory.getLogger(UtilsController.class);
	
	@Autowired
	private TokenMgntService tokenMgntService;
	
	@Autowired
	private DicItemVoMapper dicItemVoMapper;
	
	@Autowired
	private BaiDuBizService baiDuBizService;
	
	@Autowired
	private EtoBizService etoBizService;
	
	@Autowired
	private WxTUserMapper wxTUserMapper;
	
	@Autowired
	private LockerBizService lockerBizService;
	
	@Autowired
	private UserBizService userBizService;
	
	private final static String LOCKER_ID = "UtilsController.sendEmailByUser";
	
	private final static String LOCKER_NAME = "发送邮件";
	
	private final static int LOCK_EFFICTIVE_TIME = 600;//10分钟
	
	@ResponseBody
    @ApiOperation(value="获取api ticket接口",  httpMethod="POST", notes="获取api ticket接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/utils/sendemailbyuser.do")
	public ResponseMap sendEmailByUser (@RequestParam("userIds")String userIds, @RequestParam(value="ccs", required=false)String ccs, 
			@RequestParam(value="atts", required=false)String atts, @RequestParam("emailTemplate")String emailTemplate, 
			@RequestParam("subject")String subject, @RequestParam(value="single", required=false)Boolean single,
			HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("sendEmailByUser start. ");
		Long loginUser = ContextUtil.getCurUserId();
		try {
			if(!lockerBizService.lock(LOCKER_ID, LOCKER_NAME, LOCK_EFFICTIVE_TIME, loginUser.toString(), loginUser)) {
				throw new WxPltException("重复操作");
			}
			try {
				doSendEmailByUser(userIds, ccs, atts, emailTemplate, subject, single, loginUser, request);
			} finally {
				lockerBizService.unlock(LOCKER_ID, loginUser.toString(), loginUser);
			}
			LogUtils.addInfoLog(loginUser, "com.sys.utils.controller.UtilsController.sendEmailByUser", "邮件发送成功。" + userIds);
			log.info("sendEmailByUser success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			LogUtils.addErrorLog(loginUser, "com.sys.utils.controller.UtilsController.sendEmailByUser", e.getMessage(), userIds);
			resultMap.handleException(e, this, loginUser, 
					"com.sys.utils.controller.UtilsController.sendEmailByUser", null);
		}
		return resultMap;
	}
	
	private void doSendEmailByUser(String userIds, String ccs, String atts, String emailTemplate, 
			String subject, Boolean single, Long loginUser, HttpServletRequest request) throws WxPltException {
		//抄送
		Set<String> ccSet = new HashSet<String>();
		if(StringUtils.isNotBlank(ccs)) {
			for(String cc : ccs.split(",")) {
				if(StringUtils.isNotBlank(cc)) {
					ccSet.add(cc);
				}
			}
		}
		String[] ccArray = ccSet.isEmpty() ? null : ccSet.toArray(new String[ccSet.size()]);
		//附件
		Set<File> attSet = new HashSet<File>();
		if(StringUtils.isNotBlank(atts)) {
			for(String att : atts.split(",")) {
				if(StringUtils.isNotBlank(att)) {
					attSet.add(new File(att));
				}
			}
		}
		File[] attArray = attSet.isEmpty() ? null : attSet.toArray(new File[attSet.size()]);
		//接收人
		List<Long> uids = new ArrayList<Long>();
		for(String id : userIds.split(",")) {
			if(StringUtils.isNotBlank(id)) {
				uids.add(Long.parseLong(id));
			}
		}
		WxTUserExample example = new WxTUserExample();
		example.createCriteria().andUserIdIn(uids);
		List<WxTUser> toUsers = wxTUserMapper.selectByExample(example);
		
		WebApplicationContext webApplicationContext = ContextLoader
				.getCurrentWebApplicationContext();
		ServletContext servletContext = webApplicationContext
				.getServletContext();
		Map<String, Object> rootMap = CommonUtil.buildRequestParamsByRequest(request);
		rootMap.put("subject", subject);
		Set<String> tos = new HashSet<String>(toUsers.size());
		for(WxTUser user : toUsers) {
			if(StringUtils.isNotBlank(user.getEmail())) {
				if(Boolean.TRUE.equals(single)) {
					tos.add(user.getEmail());
				}else {
					rootMap.put("user", user);
					if(!EmailSendUtils.sendEmailForListContent(
							servletContext, new String[] {user.getEmail()}, 
							ccArray, subject, rootMap, attArray, emailTemplate)){
						LogUtils.addErrorLog(loginUser, "com.sys.utils.controller.UtilsController.sendEmailByUser", "邮件发送失败。" + 
							user.getLoginName() + "(" + user.getEmail() + ")", user.getUserId().toString());
					}else {
						LogUtils.addInfoLog(loginUser, "com.sys.utils.controller.UtilsController.sendEmailByUser", "已发送邮件：" + user.getLoginName());
					}
				}
			}
		}
		if(Boolean.TRUE.equals(single)) {
			if(tos.isEmpty()) {
				throw new WxPltException("未查询到收件人信息");
			}
			if(!EmailSendUtils.sendEmailForListContent(
					servletContext, tos.toArray(new String[tos.size()]), 
					ccArray, subject, rootMap, attArray, emailTemplate)){
				throw new RuntimeException("邮件发送失败。" + JsonUtil.writeValue(tos));
			}
		}
	}
	
	@ResponseBody
    @ApiOperation(value="获取api ticket接口",  httpMethod="POST", notes="获取api ticket接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/anon/utils/getpwdrulesconfig.do")
	public ResponseMap getPwdRulesConfig (){
		ResponseMap resultMap = new ResponseMap();
		log.info("getPwdRulesConfig start. ");
		try {
			resultMap.put("rules", userBizService.getUserPwdRules(ContextUtil.getCurUser(), userBizService.getPwdPolicyConfig()));
			log.info("getPwdRulesConfig success." );
//		} catch (WxPltException e) {
//			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.controller.UtilsController.getPwdRulesConfig", null);
		}
		return resultMap;
	}
	
	@ResponseBody
    @ApiOperation(value="获取api ticket接口",  httpMethod="POST", notes="获取api ticket接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/anon/utils/getapiticket.do")
	public ResponseMap getApiTicket (){
		ResponseMap resultMap = new ResponseMap();
		log.info("getApiTicket start. ");
		try {
			resultMap.put("appId", etoBizService.getAppId());
			resultMap.put("apiTicket", etoBizService.getApiTicket());
			resultMap.put("wechatAppId", Constants.getSystemPropertyByCodeType("Qrcode.Eto.wechatAppId"));
			log.info("getApiTicket success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.controller.WechatController.getApiTicket", null);
		}
		return resultMap;
	}
	
	@RequestMapping(value = "/utils/download.do")
	public String download(@RequestParam("filePath")String filePath, @RequestParam(value="fileName",required=false)String fileName,
			@RequestParam(value="deleteFile",required=false)boolean deleteFile, 
			@RequestParam(value="webpath",required=false)boolean webpath, 
			HttpServletRequest request, HttpServletResponse response){
		if (true) {
			request.setAttribute("errorMsg", "该模版下载功能已迁移至BPM");
			return "forward:/common/jsp/downloadError.jsp";
		}

		try {
			File downloadFile = null;
			log.info("filePath={},FileManager.fileUploadPath={}",filePath,FileManager.fileUploadPath);
			if(webpath&&(filePath.contains("template")||filePath.contains("app"))) {
				WebApplicationContext webApplicationContext = ContextLoader
						.getCurrentWebApplicationContext();
				ServletContext servletContext = webApplicationContext
						.getServletContext();

				downloadFile = new File(servletContext.getRealPath(filePath));
				if(StringUtils.isBlank(fileName)){
					fileName = downloadFile.getName();
				}
				FileUtil.download(response, new FileInputStream(downloadFile), fileName);
			}else if(filePath.contains(FileManager.fileUploadPath)){
				downloadFile = new File(filePath);
				if(StringUtils.isBlank(fileName)){
					fileName = downloadFile.getName();
				}
				FileUtil.download(response, new FileInputStream(downloadFile), fileName);
				if(deleteFile){
					Files.delete(downloadFile.toPath());
				}
			}else{
//				request.setAttribute("errorMsg", filePath+",此路径无法识别");
//				return "forward:/common/jsp/downloadError.jsp";
				downloadFile = new File(filePath);
				FileUtil.download(response, new FileInputStream(downloadFile), fileName);
			}
		} catch (IOException e) {
			log.error("文件" + filePath + "下载失败。" + e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

	@ResponseBody
	@RequestMapping(value="/utils/getprocessstatus.do")
	public Map<String,Object> getProcessStatus(@RequestParam(value="key", required=false)String key,
			HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			if(StringUtils.isBlank(key)){
				throw new WxPltException("处理状态键值为空");
			}
			resultMap.put(AsynProcessStatus.RESULT_KEY, tokenMgntService.getUserProperties(ContextUtil.getCurUser().getUserId(), key));
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/anon/pushwechatmessage.do")
	public String pushWeChatMessage(HttpServletRequest request){
		StringBuilder params = new StringBuilder();
		try {
			BufferedReader br = request.getReader();
			String str;
			while((str = br.readLine()) != null){
			         params.append(str);
			}
			JSONObject paramsJson = JSONObject.fromObject(params.toString());
			WeixinMessageUtil.weixinAlarmMessagePush(paramsJson.getString("eventId"), paramsJson.getString("alarmName"), 
					paramsJson.getString("alarmType"), paramsJson.getString("entityName"), paramsJson.getString("alarmContent"), 
					paramsJson.getString("alarmSummary"));
			LogUtils.addInfoLog(1l, "com.sys.utils.controller.UtilsController.pushWeChatMessage", params.toString());
			return "success";
		} catch (Exception e) {
			LogUtils.addErrorLog(1l, "com.sys.utils.controller.UtilsController.pushWeChatMessage", e.getMessage(), params.toString());
			log.error(e.getMessage(), e);
		}
		return "error";
	}

	@ResponseBody
	@RequestMapping(value="/utils/issuefeedback.do")
	public ResponseMap issueFeedback(@RequestParam(value="issueDesc", required=false)String issueDesc,
			@RequestParam(value="dicConfigCode", required=true) String dicConfigCode, HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("issueFeedback: " + issueDesc);
		try {
			List<DicItemVo> list = dicItemVoMapper.selectByCode(dicConfigCode);
			Map<String, Object> rootMap = new HashMap<String, Object>();
			Set<String> tos = new HashSet<String>();
			Set<String> ccs = new HashSet<String>();
			for(DicItemVo item : list) {
				if(StringUtils.isNotBlank(item.getDicItemDesc())) {
					if("to".equalsIgnoreCase(item.getDicItemCode())) {
						String[] ts = item.getDicItemDesc().split(",");
						for(String to : ts) {
							if(StringUtils.isNotBlank(to)) {
								tos.add(to);
							}
						}
					}else if("cc".equalsIgnoreCase(item.getDicItemCode())) {
						String[] cs = item.getDicItemDesc().split(",");
						for(String cc : cs) {
							if(StringUtils.isNotBlank(cc)) {
								ccs.add(cc);
							}
						}
					}
				}
				rootMap.put(item.getDicItemCode(), item.getDicItemDesc());
			}
			if(tos.isEmpty()) {
				throw new WxPltException("未配置接收邮箱");
			}
			if(!rootMap.containsKey("issueDesc")) {
				rootMap.put("issueDesc", issueDesc);
			}
			rootMap.put("feedbackUser", ContextUtil.getCurUser().getChName());
			WebApplicationContext webApplicationContext = ContextLoader
					.getCurrentWebApplicationContext();
			ServletContext servletContext = webApplicationContext
					.getServletContext();
			EmailSendUtils.sendEmailForListContent(servletContext, tos.toArray(new String[tos.size()]), 
					ccs.isEmpty() ? null : ccs.toArray(new String[ccs.size()]), "问题反馈", rootMap,
					null, "issue_feedback_mail.ftl");

			log.info("issueFeedback success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.controller.UtilsController.issueFeedback", issueDesc);
		}
		return resultMap;
	}

	@ResponseBody
    @ApiOperation(value="查询坐标地址接口",  httpMethod="POST", notes="查询坐标地址接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/utils/getaddressbycoordinate.do")
	public ResponseMap getAddressByCoordinate (@ApiParam(name="longitude", value="longitude", required=true) @RequestParam("longitude") Double longitude, 
			@ApiParam(name="latitude", value="latitude", required=true) @RequestParam("latitude") Double latitude, 
			@ApiParam(name="coordtype", value="坐标系类型。bd09ll（百度经纬度坐标）、gcj02ll（国测局经纬度坐标）、wgs84ll（ GPS经纬度）", required=true) @RequestParam("coordtype") String coordtype){
		ResponseMap resultMap = new ResponseMap();
		log.info("getAddressByCoordinate: " + longitude + "," + latitude + "," + coordtype);
		try {
			resultMap.setDataResult(baiDuBizService.queryAddress(longitude, latitude, coordtype));
			log.info("getAddressByCoordinate success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.utils.controller.UtilsController.getAddressByCoordinate", longitude + "," + latitude + "," + coordtype);
		}
		return resultMap;
	}
	
}
