package com.chevron.qbr.util;

import com.chevron.qbr.model.BaseTextRecordItem;
import com.chevron.qbr.model.StatsQueryParam;
import com.chevron.qbr.model.TextRecordQueryParam;
import com.common.constants.Constants;
import org.apache.commons.lang3.StringUtils;
import com.chevron.qbr.model.WXTQbrBusinessTargetVo;

import java.math.BigDecimal;
import java.util.*;

public class QbrUtil {
    public static final String CDM_SALES_CHANNEL = "CDM";
    public static final String CIO_SALES_CHANNEL = "CIO";
    public static final String ATT_PATH = "/downloadAttachmentFile.do?attId=%d";
    private static final String QBR_CIO_SALES_CHANNEL= "CIO";
    private static final Integer[] QUARTER_END_MONTH = new Integer[]{
      3,6,9,12
    };
    private static final String SELLIN_TYPE_OTHERS = "OTHERS";
    private static final String SELLIN_CDM_TYPE_SN_BELOW = "SN_BELOW";
    private static final String SELLIN_CDM_TYPE_SN_ABOVE = "SN_ABOVE";


    /**
     * INSERT INTO pmpdb01_stage.dbo.dw_pp_customer_fund_quarter (Customer_Grade) VALUES ('02-Gold');
     * INSERT INTO pmpdb01_stage.dbo.dw_pp_customer_fund_quarter (Customer_Grade) VALUES ('04-Others');
     * INSERT INTO pmpdb01_stage.dbo.dw_pp_customer_fund_quarter (Customer_Grade) VALUES ('03-Silver');
     * INSERT INTO pmpdb01_stage.dbo.dw_pp_customer_fund_quarter (Customer_Grade) VALUES ('01-Platinum');
     */
    public static final String CUSTOMER_GRADE_01 = "01-Club_Platinum";
    public static final String CUSTOMER_GRADE_02 = "02-Club_Gold";
    public static final String CUSTOMER_GRADE_03 = "03-Common Gold";
    public static final String CUSTOMER_GRADE_04 = "04-Silver";

    // CDM渠道相关的productType
    public static final Map<String,String> CDM_SELLIN_PRODUCT_TYPES = new LinkedHashMap<String, String>() {{
        put("SN & Above", SELLIN_CDM_TYPE_SN_ABOVE);
        put("SM & Below", SELLIN_CDM_TYPE_SN_BELOW);
        put("Others", SELLIN_TYPE_OTHERS);
    }};

    /**
     * 液压
     */
    private static final String SELLIN_CIO_TYPE_HYDRAULIC = "HYDRAULIC";
    /**
     * 柴机油
     */
    private static final String SELLIN_CIO_TYPE_DIESEL = "DIESEL";
    // CIO渠道相关的productType
    public static final Map<String,String> CIO_SELLIN_PRODUCT_TYPES = new LinkedHashMap<String, String>() {{
        put("液压油", SELLIN_CIO_TYPE_HYDRAULIC);
        put("柴机油", SELLIN_CIO_TYPE_DIESEL);
        put("Others", SELLIN_TYPE_OTHERS);
    }};


    public static final Map<String,String> CIO_INVENTORY_PRODUCT_TYPES = new LinkedHashMap<String, String>() {{
        put("液压油", SELLIN_CIO_TYPE_HYDRAULIC);
        put("柴机油", SELLIN_CIO_TYPE_DIESEL);
        put("Others", SELLIN_TYPE_OTHERS);
    }};

    public static final Map<String, String > SELL_THROUGH_CUSTOMER_COUNT_TYPES = new LinkedHashMap<String, String>(){
        {
//            德乐维修点（Delo Workshop）
//            金富力维修点（Havoline Workshop）
//            其他客户（Others）
//            汽、柴混合型修理厂
//                NULL
//            零售（Retailer）
//            通用工业客户（General Manufacturing）
//            工程机械车队（Construction Fleet）
//            二级批发商（Sub-distributors）
//            商用车队（Commercial Fleet- Truck/Bus/Taxi）

//            private BigDecimal dieselCustomer;
//
//            private BigDecimal dieselTrade;
//
//            private BigDecimal bizFleetCustomer;
//
//            private BigDecimal bizFleetTrade;
//
//            private BigDecimal industryFleetCustomer;
//
//            private BigDecimal industryFleetTrade;
//
//            private BigDecimal generalCustomer;
//
//            private BigDecimal generalTrade;
//
//            private BigDecimal otherCustomer;
//
//            private BigDecimal otherTrade;
//
//            private BigDecimal dealer2Customer;
//
//            private BigDecimal dealer2Trade;

            put("德乐维修点（Delo Workshop）", "dieselCustomer");
            put("商用车队（Commercial Fleet- Truck/Bus/Taxi）","bizFleetCustomer");
            put("工程机械车队（Construction Fleet）", "industryFleetCustomer");
            put("通用工业客户（General Manufacturing）", "generalCustomer");
            put("其他客户（Others）", "otherCustomer");
            put("二级批发商（Sub-distributors）", "dealer2Customer");
        }
    };

    public static final Map<String, String > SELL_THROUGH_TRADE_COUNT_TYPES = new LinkedHashMap<String, String>(){
        {
//            德乐维修点（Delo Workshop）
//            金富力维修点（Havoline Workshop）
//            其他客户（Others）
//            汽、柴混合型修理厂
//                NULL
//            零售（Retailer）
//            通用工业客户（General Manufacturing）
//            工程机械车队（Construction Fleet）
//            二级批发商（Sub-distributors）
//            商用车队（Commercial Fleet- Truck/Bus/Taxi）

//            private BigDecimal dieselCustomer;
//
//            private BigDecimal dieselTrade;
//
//            private BigDecimal bizFleetCustomer;
//
//            private BigDecimal bizFleetTrade;
//
//            private BigDecimal industryFleetCustomer;
//
//            private BigDecimal industryFleetTrade;
//
//            private BigDecimal generalCustomer;
//
//            private BigDecimal generalTrade;
//
//            private BigDecimal otherCustomer;
//
//            private BigDecimal otherTrade;
//
//            private BigDecimal dealer2Customer;
//
//            private BigDecimal dealer2Trade;

            put("德乐维修点（Delo Workshop）", "dieselTrade");
            put("商用车队（Commercial Fleet- Truck/Bus/Taxi）","bizFleetTrade");
            put("工程机械车队（Construction Fleet）", "industryFleetTrade");
            put("通用工业客户（General Manufacturing）", "generalTrade");
            put("其他客户（Others）", "otherTrade");
            put("二级批发商（Sub-distributors）", "dealer2Trade");
        }
    };

    /**
     * 根据产品的类型来统计
     * SN以上，SAN / 液压油，柴机油 的那个传这个
     */
    public static final String PRODUCT_TYPE = "PRODUCT_TYPE";
    /**
     * 根据产品的sku来统计
     *  ——超级金德乐/金德乐/银德乐。。。那个table传这个
     */
    public static final String PRODUCT_SKU = "PRODUCT_SKU";

    /**
     * 对于salesChannel的适配
     * @return
     */
    public static void salesChannelAdaptation(StatsQueryParam param){
        // CIO的sales channel转成C&I
        if(param!=null && QBR_CIO_SALES_CHANNEL.equals(param.getSalesChannel())){
            param.setSalesChannel(Constants.SALES_CHANNEL_CAI);
        }
    }

    /**
     * 对于salesChannel的适配endQuarter
     * @return
     */
    public static String queryTimeAdaptation(StatsQueryParam param,boolean endDateMonthEnd){
        String result = null;
        if(param.getYear() == null){
            result = "year不能为空";
        } else {
            Calendar calc = Calendar.getInstance();
            calc.set(Calendar.HOUR_OF_DAY,0);
            calc.set(Calendar.MINUTE,0);
            calc.set(Calendar.SECOND,0);
            calc.set(Calendar.MILLISECOND,0);

            Integer quarter = param.getQuarter();
            Integer month = param.getMonth();
            if(quarter == null && month == null){
                result = "quarter或则month不能为空";
            } else {
                if(quarter != null){
                    if(quarter < 1 || quarter > 4){
                        quarter = 4;
                        param.setQuarter(quarter);
                    }
                    int beginYear = param.getYear();
//                    int endYear = quarter==4 ? (param.getYear() + 1):param.getYear();
                    int beginMonth = 0;
//                    int endMonth = quarter==4 ? 0: QUARTER_END_MONTH[quarter - 1];
                    int endMonth = QUARTER_END_MONTH[quarter - 1] - 1;
                    calc.set(Calendar.YEAR,beginYear);

//                    calc.set(Calendar.YEAR,endYear);
                    if(endDateMonthEnd){
                        calc.set(Calendar.MONTH,endMonth + 1);
                        calc.set(Calendar.DATE,1);
                        calc.add(Calendar.SECOND,-1);
                    } else {
                        calc.set(Calendar.MONTH,endMonth);
                        calc.set(Calendar.DATE,1);
                    }
                    param.setEndDate(calc.getTime());

                    calc.set(Calendar.HOUR_OF_DAY,0);
                    calc.set(Calendar.MINUTE,0);
                    calc.set(Calendar.SECOND,0);
                    calc.set(Calendar.MONTH,beginMonth);
                    calc.set(Calendar.DATE,1);
                    calc.add(Calendar.SECOND,-1);
                    param.setStartDate(calc.getTime());
                } else {
                    if(month < 1 || month > 12){
                        month = 12;
                    }
                    int beginYear = param.getYear();
//                    int endYear = month==12 ? (param.getYear() + 1):param.getYear();
                    int beginMonth = 0;
                    int endMonth = month - 1;
                    calc.set(Calendar.YEAR,beginYear);
                    calc.set(Calendar.MONTH,beginMonth);
                    calc.set(Calendar.DATE,1);
                    param.setStartDate(calc.getTime());

//                    calc.set(Calendar.YEAR,endYear);
                    calc.set(Calendar.MONTH,endMonth);
                    calc.set(Calendar.DATE,1);
//                    calc.add(Calendar.SECOND,-1);
                    param.setEndDate(calc.getTime());
                }
            }


        }
        return result;
    }
    /**
     * 对于salesChannel的适配
     * @return
     */
    public static String queryTimeAdaptation(StatsQueryParam param){
        return queryTimeAdaptation(param,false);
    }

    /**
     * 对于salesChannel的适配
     * @return
     */
    public static String queryTimeAdaptationOneQuarter(StatsQueryParam param,boolean endDateMonthEnd){
        String result = null;
        if(param.getYear() == null){
            result = "year不能为空";
        } else {
            Calendar calc = Calendar.getInstance();
            calc.set(Calendar.HOUR_OF_DAY,0);
            calc.set(Calendar.MINUTE,0);
            calc.set(Calendar.SECOND,0);
            calc.set(Calendar.MILLISECOND,0);

            Integer quarter = param.getQuarter();
            Integer month = param.getMonth();
            if(quarter == null && month == null){
                result = "quarter或则month不能为空";
            } else {
                if(quarter != null){
                    if(quarter < 1 || quarter > 4){
                        quarter = 1;
                        param.setQuarter(quarter);
                    }
                    int beginYear = param.getYear();
//                    int endYear = quarter==4 ? (param.getYear() + 1):param.getYear();
//                    int endMonth = quarter==4 ? 0: QUARTER_END_MONTH[quarter - 1];
                    int endMonth = QUARTER_END_MONTH[quarter - 1] - 1;
                    int beginMonth = (quarter -1)*3 + 1;
                    calc.set(Calendar.YEAR,beginYear);

//                    calc.set(Calendar.YEAR,endYear);
                    if(endDateMonthEnd){
                        calc.set(Calendar.MONTH,endMonth + 1);
                        calc.set(Calendar.DATE,1);
                        calc.add(Calendar.SECOND,-1);
                    } else {
                        calc.set(Calendar.MONTH,endMonth);
                        calc.set(Calendar.DATE,1);
                    }
                    param.setEndDate(calc.getTime());

                    calc.set(Calendar.HOUR_OF_DAY,0);
                    calc.set(Calendar.MINUTE,0);
                    calc.set(Calendar.SECOND,0);
                    calc.set(Calendar.MONTH,beginMonth - 1);
                    calc.set(Calendar.DATE,1);
                    calc.add(Calendar.SECOND,-1);
                    param.setStartDate(calc.getTime());
                } else {
                    if(month < 1 || month > 12){
                        month = 12;
                    }
                    int beginYear = param.getYear();
//                    int endYear = month==12 ? (param.getYear() + 1):param.getYear();
                    int beginMonth = month - 1;
                    int endMonth = month - 1;
                    calc.set(Calendar.YEAR,beginYear);
                    calc.set(Calendar.MONTH,beginMonth);
                    calc.set(Calendar.DATE,1);
                    param.setStartDate(calc.getTime());

//                    calc.set(Calendar.YEAR,endYear);
                    calc.set(Calendar.MONTH,endMonth);
                    calc.set(Calendar.DATE,1);
//                    calc.add(Calendar.SECOND,-1);
                    param.setEndDate(calc.getTime());
                }
            }


        }
        return result;
    }


    /**
     * 对于salesChannel的适配
     * @return
     */
    public static String isValidGatherType(StatsQueryParam param){
        String gatherType = param.getQueryType();
        String result = null;
       if(StringUtils.isBlank(gatherType) || (!PRODUCT_TYPE.equals(gatherType) && !PRODUCT_SKU.equals(gatherType))){
            result = "PRODUCT_TYPE请传合适的值";
        }
        return result;
    }

    /**
     * 对于salesChannel的适配
     * @return
     */
    public static String isValidSalesChannel(StatsQueryParam param){
        String salesChannel = param.getSalesChannel();
        String result = null;
        if(StringUtils.isBlank(salesChannel)){
            result = "salesChannel请传合适的值";
        }
        return result;
    }

    public static List<String> convertToPath(List<Long> attIds){
        List<String> paths = null;
        if(attIds!=null && attIds.size() >0){
            paths = new ArrayList<String>();
            for(Long attId:attIds){
                paths.add(String.format(ATT_PATH,attId));
            }
        }
        return paths;
    }

    /**
     * 对YTD的适配——year to date
     * @return
     */
    public static String queryQTDAdaptation(StatsQueryParam param){
        String result = null;
        Calendar calc = Calendar.getInstance();
        // 如果传过来的参数就是今年，那么就是从今天起---》往前退役年
        if(param.getYear()==null || param.getYear() == calc.get(Calendar.YEAR)){
            calc.set(Calendar.HOUR_OF_DAY,23);
            calc.set(Calendar.MINUTE,59);
            calc.set(Calendar.SECOND,59);
            param.setEndDate(calc.getTime());
            calc.set(Calendar.MONTH,0);
            calc.set(Calendar.DAY_OF_MONTH,1);
            calc.set(Calendar.HOUR_OF_DAY,0);
            calc.set(Calendar.MINUTE,0);
            calc.set(Calendar.SECOND,0);
            param.setStartDate(calc.getTime());
        } else {
            calc.add(Calendar.YEAR,-1);
            calc.set(Calendar.MONTH,0);
            calc.set(Calendar.DAY_OF_MONTH,1);

            calc.set(Calendar.HOUR_OF_DAY,0);
            calc.set(Calendar.MINUTE,0);
            calc.set(Calendar.SECOND,0);

            param.setStartDate(calc.getTime());
            calc.set(Calendar.MONTH,11);
            calc.set(Calendar.DAY_OF_MONTH,31);

            calc.set(Calendar.HOUR_OF_DAY,23);
            calc.set(Calendar.MINUTE,59);
            calc.set(Calendar.SECOND,59);
            param.setEndDate(calc.getTime());
        }
        return result;
    }


    /**
     * TextRecord query的参数的check
     * @param param
     * @return
     */
    public static String checkTextRecordQueryParam(TextRecordQueryParam param) {
        String checkResult = null;
        if(param.getPartnerId() == null
                || param.getQuarter() == null
                || param.getYear()==null){
            checkResult = "partnerId、year、quarter不能传空值";
        }
        if(StringUtils.isNotBlank(checkResult)){
            return checkResult;
        }
        if(param.getQuarter() > 4 || param.getQuarter() <1){
            checkResult = "quarter只能传1~4之内的值";
        }
        return checkResult;
    }

    /**
     * TextRecord refresh的参数的check
     * @param baseTextRecordItem
     * @return
     */
    public static String checkTextRecordRefreshParam(BaseTextRecordItem baseTextRecordItem) {
        String checkResult = null;
        if(baseTextRecordItem.getPartnerId() == null
                || baseTextRecordItem.getQuarter() == null
                || baseTextRecordItem.getYear()==null){
            checkResult = "partnerId、year、quarter不能传空值";
        }
        if(!StringUtils.isBlank(checkResult)){
            return checkResult;
        }
        if(baseTextRecordItem.getQuarter() > 4 || baseTextRecordItem.getQuarter() <1){
            checkResult = "quarter只能传1~4之内的值";
        }
        return checkResult;
    }

    public static Double round2Decimal(Double num){
        Double result = num;
        if(num !=null){
            result = Math.round(num*100)/100.0;
        }
        return result;
    }

    /**
     * 把string转换为BigDecimal,没有的就返回0
     * @param oriValue
     * @return
     */
    public static BigDecimal convertToDecimal(String oriValue){
        BigDecimal convertedValue;
        try{
            convertedValue  = BigDecimal.valueOf(Double.valueOf(oriValue));
        } catch (Exception e){
            convertedValue = BigDecimal.valueOf(0);
        }
        return convertedValue;
    }

    /**
     * 把salesChannelOri变回CIO
     * @param salesChannelOri
     * @return
     */
    public static String revertToCIO(String salesChannelOri){
        if("C&I".equals(salesChannelOri)){
            return CIO_SALES_CHANNEL;
        }else {
            return salesChannelOri;
        }
    }
}
