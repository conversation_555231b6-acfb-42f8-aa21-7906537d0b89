package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;

import com.chevron.interfaces.business.MaterialPlatformBizService;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterExample;
import com.chevron.material.util.DealerPermissUtil;
import com.chevron.point.business.PointAccountBizService;
import com.chevron.point.dic.PointAccountType;
import com.chevron.point.model.WXTPointAccountVo;
import com.chevron.point.model.WXTPointAccountVoExample;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.pms.business.VerificationRuleBizService;
import com.chevron.pms.business.WorkshopDistributionRuleBizService;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.dao.PartnerO2ODepartmentMapper;
import com.chevron.pms.dao.PartnerO2OEnterpriseMapper;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.dao.RegionPartnerVoMapper;
import com.chevron.pms.model.PartnerCtrlParam;
import com.chevron.pms.model.PartnerO2ODepartment;
import com.chevron.pms.model.PartnerO2ODepartmentExample;
import com.chevron.pms.model.PartnerO2OEnterprise;
import com.chevron.pms.model.PartnerO2OEnterpriseExample;
import com.chevron.pms.model.PartnerParams;
import com.chevron.pms.model.RegionPartnerVo;
import com.chevron.pms.model.RegionVo;
import com.chevron.pms.model.VerificationRule;
import com.chevron.pms.model.WorkshopDistributionRule;
import com.chevron.pms.model.wechat.Department;
import com.chevron.pms.service.PartnerService;
import com.chevron.pms.service.RegionPartnerService;
import com.chevron.pms.service.RegionService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.GetPinyinStringUtil;
import com.common.util.HttpSender;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserExample;
import com.sys.dealer.dao.DealerBusinessFunMapper;
import com.sys.dealer.dao.DealerProductPermissionMapper;
import com.sys.dealer.model.DealerBusinessFun;
import com.sys.dealer.model.DealerBusinessFunExample;
import com.sys.dealer.model.DealerProductPermission;
import com.sys.dealer.model.DealerProductPermissionExample;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.model.WxAttFile;
import com.sys.file.model.WxAttFileExample;
import com.sys.log.business.LogBizService;
import com.sys.log.model.Log;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVo;
import com.sys.organization.model.OrganizationVoExample;
import com.sys.organization.model.PartnerView;
import com.sys.organization.model.OrganizationVoExample.Criteria;
import com.sys.utils.business.EtoBizService;

@Service
public class PartnerServiceImpl implements PartnerService  {

    private Logger logger = Logger.getLogger(PartnerServiceImpl.class);
   
    
    @Resource
    RegionPartnerService regionPartnerServiceImpl;
    
    @Resource
    OrganizationVoMapper organizationVoMapper;
    
    @Resource
    RegionService regionServiceImpl;
    
    @Resource
    PartnerO2OEnterpriseMapper partnerO2OEnterpriseMapper;
    
    @Resource
    PartnerO2ODepartmentMapper partnerO2ODepartmentMapper;
    
    @Resource
    VerificationRuleBizService verificationRuleBizServiceImpl;
    
    @Resource
    WorkshopDistributionRuleBizService workshopDistributionRuleBizServiceImpl;
    
    @Resource
	private WxAttFileMapper wxAttFileMapper;
	
	@Autowired
	private DealerBusinessFunMapper dealerBusinessFunMapper;
	
	@Autowired
	private ProductVoMapper productVoMapper;
	
	@Autowired
	private RegionPartnerVoMapper regionPartnerVoMapper;
	
	@Resource
	private WxTUserMapper wxTUserMapper;
	
	@Resource
	private WorkshopMasterMapper workshopMasterMapper;
	
	@Autowired
	private DealerProductPermissionMapper dealerProductPermissionMapper;
    
    @Autowired
    private LogBizService logBizService;

	@Autowired
	private PointAccountBizService pointAccountBizService;

	@Autowired
	private DicService dicService;

	@Autowired
    private DicItemVoMapper dicItemVoMapper;
	
	@Autowired
	private EtoBizService etoBizService;
	
	@Autowired
	private MaterialPlatformBizService materialPlatformBizService;
   
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_TOTAL_RECORD =  "totalRecord";
	
    @Override
    public Map<String,String> removePartner(long partnerId) throws Exception {
        Map<String,String> map = new HashMap<String,String>();
        organizationVoMapper.deleteByPrimaryKey(partnerId);
        map.put("resultData", "success");
        return map;
    }

    public Map<String,OrganizationVo> getPartner(long partnerId) throws Exception {
        Map<String,OrganizationVo> map = new HashMap<String,OrganizationVo>();
        organizationVoMapper.selectByPrimaryKey(partnerId);
        return map;
    }
        
    public OrganizationVo getPartnerByCode(String partnerCode) throws Exception {
        return organizationVoMapper.selectPartnerByCode(partnerCode);
    }
	
	@Override
	public OrganizationVo getPartnerById(long id) throws Exception {
		return organizationVoMapper.selectByPrimaryKey(id);
	}
//    
//    @Override
//    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
//    public Map<String,String> createPartner(String partnerName,String orgIds,String orgName,
//			String enterpriseCode, String enterpriseName, Double perLiterReward) throws Exception {
//        Map<String,String> map = new HashMap<String,String>();
//        String [] orgIdArr = orgIds.split(",");
//	    OrganizationVo organizationVo = new OrganizationVo(); 
//	    organizationVo.setOrganizationName(partnerName);
//	    organizationVo.setSort(10);
//	    organizationVo.setStatus(1);
//	    organizationVo.setType(1);
//	    organizationVo.setCreateTime(new Date());
//	    organizationVo.setUpdateTime(new Date());
//	    OrganizationVoExample example =new OrganizationVoExample();
//    	com.sys.organization.model.OrganizationVoExample.Criteria criteria = example.createCriteria();
//    	criteria.andOrganizationNameEqualTo(Constants.ORGANIZATION_PARTNER_NAME);
//		List<OrganizationVo> list  = organizationVoMapper.selectByExample(example);
//		if(list.size()>0){
//			OrganizationVo ovo = list.get(0);
//			organizationVo.setParentId(ovo.getId());
//			String code  = CommonUtil.generateCode("SP");
//			String newCode = ovo.getOrganizationCode()+code;
//			organizationVo.setOrganizationCode(newCode);
//		    try {
//			    organizationVoMapper.insertSelective(organizationVo);
//				long partnerId = organizationVo.getId();
//				List<RegionPartnerVo> rpvList = new ArrayList<RegionPartnerVo>();
//				
//				for(int i=0;i<orgIdArr.length;i++){
//					RegionPartnerVo regionPartnerVo = new RegionPartnerVo();
//					long regionId = Long.valueOf(orgIdArr[i]);
//					regionPartnerVo.setRegionId(regionId);
//					regionPartnerVo.setPartnerId(partnerId);
//					regionPartnerVo.setCreateTime(new Date());
//					regionPartnerVo.setUpdateTime(new Date());
//					rpvList.add(regionPartnerVo);
//				}
//				regionPartnerServiceImpl.batchInsert(rpvList);
//				//新建企业号
//		        if(enterpriseCode != null){
//		        	PartnerO2OEnterprise enterprise = new PartnerO2OEnterprise();
//		        	enterprise.setCreatedBy(ContextUtil.getCurUserId());
//		        	enterprise.setCreationTime(new Date());
//		        	enterprise.setEnterpriseCode(enterpriseCode);
//		        	enterprise.setEnterpriseName(enterpriseName);
//		        	enterprise.setPartnerId(partnerId);
//		        	partnerO2OEnterpriseMapper.insertSelective(enterprise);
//		        }
//		        //新建核销规则
//		        if(perLiterReward != null){
//		        	VerificationRule newRule = new VerificationRule();
//		        	newRule.setPartnerId(partnerId);
//		        	newRule.setPerLiterReward(perLiterReward);
//		        	newRule.setRuleType(VerificationRule.RULE_TYPE_LITER);
//		        	verificationRuleBizServiceImpl.saveVerificationRule(newRule);
//		        }
//				map.put("resultData", "success");
//			} catch (Exception e) {
//				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
//				map.put("resultData", "fail");
//				map.put("resultMsg", e.getMessage());
//				e.printStackTrace();
//			}
//		}else{
//			map.put("resultData", "fail");
//			map.put("resultMsg", "未找到Partner父节点,请检查Partner常量配置是否与组织名字一致");
//		}
//        return map;
//    }
	
	private void insertRegionPartner(String regionIds, int channelWeight, Long partnerId, Date now) throws WxPltException {
		if(StringUtils.isBlank(regionIds)) {
			return;
		}
        String [] orgIdArr = regionIds.split(",");
		List<RegionPartnerVo> rpvList = new ArrayList<RegionPartnerVo>(orgIdArr.length);
		
		for(int i=0;i<orgIdArr.length;i++){
			RegionPartnerVo regionPartnerVo = new RegionPartnerVo();
			Long regionId = Long.valueOf(orgIdArr[i]);
			regionPartnerVo.setRegionId(regionId);
			regionPartnerVo.setPartnerId(partnerId);
			regionPartnerVo.setCreateTime(now);
			regionPartnerVo.setUpdateTime(now);
			regionPartnerVo.setChannelWeight(channelWeight);
			rpvList.add(regionPartnerVo);
		}
		regionPartnerServiceImpl.batchInsert(rpvList);
	}
	
    @Override
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public Map<String,String> createPartnerWithContact(String partnerName, String orgIds, String commercialOrgIds, 
    		PartnerO2OEnterprise partnerExtProperty, 
			List<DealerBusinessFun> selectedFuns, List<DealerProductPermission> selectedProducts, 
    		Double perLiterReward, WorkshopDistributionRule workshopDistributionRule,
    		String attIds,String[] owerPowerList, String[] mechPowerList, WorkshopDistributionRule[] skuDistributionRules,String remark) throws Exception {
        Map<String,String> map = new HashMap<String,String>();
        //验证合伙人是否已存在
        Map<String, Object> params = new HashMap<String, Object>(5);
        params.put("existCheck", true);
        params.put("partnerName", partnerName);
        if(partnerExtProperty != null){
        	if(StringUtils.isNotBlank(partnerExtProperty.getSapCode())){
        		params.put("sapCode", partnerExtProperty.getSapCode());
        	}
        	if(partnerExtProperty.getDistributorId() != null){
                params.put("distributorId", partnerExtProperty.getDistributorId());
        	}
        }
        List<PartnerView> existPartners = organizationVoMapper.selectPartnersByParams(params);
        if(existPartners != null && !existPartners.isEmpty()){
        	PartnerView existPartner = existPartners.get(0);
        	if(partnerName.equals(existPartner.getName())){
				map.put("resultData", "fail");
				map.put("resultMsg", "合伙人名称已存在");
				return map;
        	}else if(partnerExtProperty.getSapCode() != null && partnerExtProperty.getSapCode().equals(existPartner.getSapCode())){
				map.put("resultData", "fail");
				map.put("resultMsg", "SAP编码已存在");
				return map;
        	}else {//if(partnerExtProperty.getDistributorId() != null && partnerExtProperty.getDistributorId().equals(existPartner.getDistributorId())){
				map.put("resultData", "fail");
				map.put("resultMsg", "经销商ID已存在");
				return map;
        	}
        }
        
        Date now = DateUtil.getCurrentDate();

        Map<String, Object> reqMap = new HashMap<String,Object>();
	    OrganizationVo organizationVo = new OrganizationVo(); 
	    organizationVo.setOrganizationName(partnerName);
	    organizationVo.setSort(10);
	    organizationVo.setStatus(1);
	    organizationVo.setType(1);
	    organizationVo.setCreateTime(now);
	    organizationVo.setUpdateTime(now);
	    organizationVo.setRemark(remark);
	    OrganizationVoExample example =new OrganizationVoExample();
    	com.sys.organization.model.OrganizationVoExample.Criteria criteria = example.createCriteria();
    	criteria.andOrganizationNameEqualTo(Constants.ORGANIZATION_PARTNER_NAME);
		List<OrganizationVo> list  = organizationVoMapper.selectByExample(example);
		if(list.size()>0){
			OrganizationVo ovo = list.get(0);
			organizationVo.setParentId(ovo.getId());
			String code  = CommonUtil.generateCode("SP");
			String newCode = ovo.getOrganizationCode()+code;
			organizationVo.setOrganizationCode(newCode);
		    try {
			    organizationVoMapper.insertSelective(organizationVo);
				Long partnerId = organizationVo.getId();
				
		        insertRegionPartner(orgIds, Constants.CHANNEL_WEIGHT_CDM, partnerId, now);
		        insertRegionPartner(commercialOrgIds, Constants.CHANNEL_WEIGHT_CI, partnerId, now);
				//新建企业号
		        if(partnerExtProperty != null){
		        	partnerExtProperty.setCreatedBy(ContextUtil.getCurUserId());
		        	partnerExtProperty.setCreationTime(now);
		        	partnerExtProperty.setPartnerId(partnerId);
		        	partnerO2OEnterpriseMapper.insertSelective(partnerExtProperty);
		        }
    	        //插入新业务权限
    	        if(selectedFuns != null && !selectedFuns.isEmpty()){
    	        	for(DealerBusinessFun item : selectedFuns){
    	        		item.setDealerId(partnerId);
    	        	}
    	        	dealerBusinessFunMapper.insertBatch(selectedFuns);
    	        }
    	        //插入新产品权限
    	        if(selectedProducts != null && !selectedProducts.isEmpty()){
    	        	for(DealerProductPermission permission : selectedProducts){
    	        		permission.setDealerId(partnerId);
    	        	}
    	        	dealerProductPermissionMapper.insertBatch(selectedProducts);
    	        }
		        if(workshopDistributionRule != null){
			        //新建核销规则
			        if(perLiterReward != null){
			        	VerificationRule newRule = new VerificationRule();
			        	newRule.setPartnerId(partnerId);
			        	newRule.setPerLiterReward(perLiterReward);
			        	newRule.setRuleType(VerificationRule.RULE_TYPE_LITER);
			        	verificationRuleBizServiceImpl.saveVerificationRule(newRule);
			        }
			        //新建核销默认分配规则
			        workshopDistributionRule.setPartnerId(partnerId);
			        workshopDistributionRule.setWorkshopId(-1l);
			        workshopDistributionRuleBizServiceImpl.saveDistributionRule(workshopDistributionRule);
			        //新建核销优先分配规则
			        if(skuDistributionRules != null && skuDistributionRules.length > 0){
			        	for(WorkshopDistributionRule skuRule : skuDistributionRules){
			        		skuRule.setWorkshopId(-1l);
			        		skuRule.setPartnerId(partnerId);
			        		workshopDistributionRuleBizServiceImpl.saveDistributionRule(skuRule);
			        	}
			        }
		        }
		        //更新核销合同ID
		      //4.0批量更新附件数据表（知道附件的sourceid对应的主任务id）
				if(!attIds.equals("") && !attIds.equals(","))
				{
					attIds = attIds.substring(0, attIds.length()-1);
					String attids[] = attIds.split(",");
					if(attids.length>0)
					{
						Long[] attIDS = new Long[attids.length];
						for(int i=0; i<attIDS.length;i++)
						{
							attIDS[i] = new Long((long)Integer.parseInt(attids[i]));
						}
						reqMap.put("sourceId", partnerId);
						reqMap.put("attIds", attIDS);
						wxAttFileMapper.updateWxAttFilesSetSourceId(reqMap);
					}
				}
//				//保存企业号功能维护
//    	        savePartnerDepartments(partnerId,mechPowerList,owerPowerList);
//    	        //新建企业号SP
//    	        updateO2oSp(partnerId, partnerName);
				
				map.put("resultData", "success");
		    }catch (WxPltException e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
				map.put("resultData", "fail");
				map.put("resultMsg", e.getExpMsg());
			} catch (Exception e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
				map.put("resultData", "fail");
				map.put("resultMsg", e.getMessage());
				e.printStackTrace();
			}
		}else{
			map.put("resultData", "fail");
			map.put("resultMsg", "未找到Partner父节点,请检查Partner常量配置是否与组织名字一致");
		}
        return map;
    }
    
    protected void updateO2oSp(Long spId, String spName) throws WxPltException{
		try {
			String serviceUrl = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_UPDATE_SP);
			String key = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC_KEY);
			String token = DigestUtils.md5Hex(spId + spName + key);
			Map<String, Object> postData = new HashMap<String, Object>();
			postData.put("token", token);
			postData.put("spid", spId.toString()); 
			postData.put("name", spName);
			String responseContent = HttpSender.postJSON(serviceUrl, postData, null);
			logger.info("responseContent: " + responseContent);
			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
			if (!"0".equals(responseJsonObj.getString("errcode"))){
				throw new WxPltException(responseJsonObj.getString("errmsg"));
			}
		} catch (WxPltException e){
			throw e;
		} catch (Exception e) {
			logger.error("register mechanic to o2o exception", e);
			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
		}
    }
//    
//    @Override
//    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
//    public Map<String,String> updatePartner(String partnerId,String partnerCode,String partnerName,String orgIds,String orgName,
//			String enterpriseCode, String enterpriseName, Double perLiterReward) throws Exception {
//        Map<String,String> map = new HashMap<String,String>();
//       
//        long partnerIdl = Long.valueOf(partnerId);
//        OrganizationVo organizationVo = organizationVoMapper.selectByPrimaryKey(partnerIdl);
//        organizationVo.setOrganizationName(partnerName);
//        organizationVo.setUpdateTime(new Date());
//        
//        String [] orgIdArr = orgIds.split(",");
//        List<RegionPartnerVo> newRpvList = new ArrayList<RegionPartnerVo>();
//        for(int i=0;i<orgIdArr.length;i++){
//			RegionPartnerVo regionPartnerVo = new RegionPartnerVo();
//			long regionId = Long.valueOf(orgIdArr[i]);
//			regionPartnerVo.setRegionId(regionId);
//			regionPartnerVo.setPartnerId(partnerIdl);
//			regionPartnerVo.setUpdateTime(new Date());
//			newRpvList.add(regionPartnerVo);
//		}
//        Long userId = ContextUtil.getCurUserId();
//        try {
//			organizationVoMapper.updateByPrimaryKeySelective(organizationVo);
//			regionPartnerServiceImpl.deleteByPartnerId(partnerIdl);
//			regionPartnerServiceImpl.batchInsert(newRpvList);
//			//更新企业号
//			partnerO2OEnterpriseMapper.deleteByPartnerId(partnerIdl);
//	        if(enterpriseCode != null){
//	        	PartnerO2OEnterprise enterprise = new PartnerO2OEnterprise();
//	        	enterprise.setCreatedBy(userId);
//	        	enterprise.setCreationTime(new Date());
//	        	enterprise.setEnterpriseCode(enterpriseCode);
//	        	enterprise.setEnterpriseName(enterpriseName);
//	        	enterprise.setPartnerId(partnerIdl);
//	        	partnerO2OEnterpriseMapper.insertSelective(enterprise);
//	        }
//	        //更新核销规则
//	        if(perLiterReward != null){
//		        VerificationRule oldRule = verificationRuleBizServiceImpl.getVerificationRuleByPartnerId(partnerIdl);
//		        if(oldRule == null || !perLiterReward.equals(oldRule.getPerLiterReward() != null)){
//		        	VerificationRule newRule = new VerificationRule();
//		        	newRule.setPartnerId(partnerIdl);
//		        	newRule.setPerLiterReward(perLiterReward);
//		        	newRule.setRuleType(VerificationRule.RULE_TYPE_LITER);
//		        	if(oldRule != null){
//		        		newRule.setId(oldRule.getId());
//		        	}
//		        	verificationRuleBizServiceImpl.saveVerificationRule(newRule);
//		        }
//	        }
//			
//			map.put("resultData", "success");
//		} catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			map.put("resultData", "fail");
//			e.printStackTrace();
//		}
//        return map;
//    }


    @Override
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, String> updatePartnerWithEnterprisePower(
            String partnerId, String partnerCode, String partnerName,
            String orgIds, String commercialOrgIds, PartnerO2OEnterprise partnerExtProperty,
            List<DealerBusinessFun> selectedFuns, List<DealerProductPermission> selectedProducts,
            Double perLiterReward, WorkshopDistributionRule workshopDistributionRule, String[] owerPowerList,
            String[] mechPowerList, String ruleForAllWs, String skuRuleForAllWs,
            WorkshopDistributionRule[] skuDistributionRules, List<Long> deletedSkuDistributionRules, String remark) throws Exception {
            Map<String,String> map = new HashMap<String,String>();

            Long partnerIdl = Long.valueOf(partnerId);
            //验证合伙人是否已存在
            Map<String, Object> params = new HashMap<String, Object>(5);
            params.put("existCheck", true);
            params.put("partnerName", partnerName);
            params.put("partnerId", partnerIdl);
            if(partnerExtProperty != null){
            	if(StringUtils.isNotBlank(partnerExtProperty.getSapCode())){
            		params.put("sapCode", partnerExtProperty.getSapCode());
            	}
            	if(partnerExtProperty.getDistributorId() != null){
                    params.put("distributorId", partnerExtProperty.getDistributorId());
            	}
            }
            List<PartnerView> existPartners = organizationVoMapper.selectPartnersByParams(params);
            if(existPartners != null && !existPartners.isEmpty()){
            	PartnerView existPartner = existPartners.get(0);
            	if(partnerName.equals(existPartner.getName())){
    				map.put("resultData", "fail");
    				map.put("resultMsg", "合伙人名称已存在");
    				return map;
            	}else if(partnerExtProperty.getSapCode() != null && partnerExtProperty.getSapCode().equals(existPartner.getSapCode())){
    				map.put("resultData", "fail");
    				map.put("resultMsg", "SAP编码已存在");
    				return map;
            	}else {//if(partnerExtProperty.getDistributorId() != null && partnerExtProperty.getDistributorId().equals(existPartner.getDistributorId())){
    				map.put("resultData", "fail");
    				map.put("resultMsg", "经销商ID已存在");
    				return map;
            	}
            }
            OrganizationVo organizationVo = organizationVoMapper.selectByPrimaryKey(partnerIdl);
            organizationVo.setOrganizationName(partnerName);
            organizationVo.setUpdateTime(new Date());
            organizationVo.setRemark(remark);
            Long userId = ContextUtil.getCurUserId();
        	Date now = new Date();
            try {
    			organizationVoMapper.updateByPrimaryKeySelective(organizationVo);
    			regionPartnerServiceImpl.deleteByPartnerId(partnerIdl);
		        insertRegionPartner(orgIds, Constants.CHANNEL_WEIGHT_CDM, partnerIdl, now);
		        insertRegionPartner(commercialOrgIds, Constants.CHANNEL_WEIGHT_CI, partnerIdl, now);
				//同步授权区域，避免分销商授权区域扩散
				regionPartnerVoMapper.synRetailerRegion(RegionPartnerVoMapper.SYN_TYPE_DISTRIBUTOR, partnerIdl);
    			//更新或插入合伙人扩展属性
//    			partnerO2OEnterpriseMapper.deleteByPartnerId(partnerIdl);
    	        if(partnerExtProperty != null){
    	        	partnerExtProperty.setLastUpdateTime(now);
    	        	partnerExtProperty.setLastUpdatedBy(userId);
    	        	partnerExtProperty.setPartnerId(partnerIdl);
    	        	if(partnerO2OEnterpriseMapper.updateByPartnerIdSelective(partnerExtProperty) < 1){
        	        	partnerExtProperty.setCreatedBy(userId);
        	        	partnerExtProperty.setCreationTime(now);
        	        	partnerO2OEnterpriseMapper.insertSelective(partnerExtProperty);
    	        	}
    	        }
    	        //删除原业务权限
    	        DealerBusinessFunExample dealerBusinessFunExample = new DealerBusinessFunExample();
    	        dealerBusinessFunExample.createCriteria().andDealerIdEqualTo(partnerIdl).andBusinessCustomIdEqualTo(-1l);
    	        dealerBusinessFunMapper.deleteByExample(dealerBusinessFunExample);
    	        //插入新业务权限
    	        if(selectedFuns != null && !selectedFuns.isEmpty()){
    	        	dealerBusinessFunMapper.insertBatch(selectedFuns);
    	        }
    	        //删除原产品权限
    	        DealerProductPermissionExample dealerProductPermissionExample = new DealerProductPermissionExample();
    	        dealerProductPermissionExample.createCriteria().andDealerIdEqualTo(partnerIdl);
    	        dealerProductPermissionMapper.deleteByExample(dealerProductPermissionExample);
    	        //插入新产品权限
    	        if(selectedProducts != null && !selectedProducts.isEmpty()){
    	        	dealerProductPermissionMapper.insertBatch(selectedProducts);
    	        }
    	        //默认分配规则
    	        if(workshopDistributionRule != null){
			        workshopDistributionRule.setPartnerId(partnerIdl);
			        workshopDistributionRule.setWorkshopId(-1l);
			        workshopDistributionRuleBizServiceImpl.saveDistributionRule(workshopDistributionRule);
			        if("1".equals(ruleForAllWs)){
			        	workshopDistributionRuleBizServiceImpl.resetRuleByPartnerDefRule(partnerIdl);
			        }
			        if("1".equals(skuRuleForAllWs)){
			        	workshopDistributionRuleBizServiceImpl.resetSkuRuleByPartnerDefRule(partnerIdl);
			        }
	    	        //更新核销规则
	    	        if(perLiterReward != null){
	    		        VerificationRule oldRule = verificationRuleBizServiceImpl.getVerificationRuleByPartnerId(partnerIdl);
	    		        if(oldRule == null || !perLiterReward.equals(oldRule.getPerLiterReward() != null)){
	    		        	VerificationRule newRule = new VerificationRule();
	    		        	newRule.setPartnerId(partnerIdl);
	    		        	newRule.setPerLiterReward(perLiterReward);
	    		        	newRule.setRuleType(VerificationRule.RULE_TYPE_LITER);
	    		        	if(oldRule != null){
	    		        		newRule.setId(oldRule.getId());
	    		        	}
	    		        	verificationRuleBizServiceImpl.saveVerificationRule(newRule);
	    		        }
	    	        }
	    	        //更新优先核销分配规则
	    	        if(skuDistributionRules != null){
	    	        	for(WorkshopDistributionRule skuRule : skuDistributionRules){
	    	        		skuRule.setWorkshopId(-1l);
	    	        		skuRule.setPartnerId(partnerIdl);
	    	        		workshopDistributionRuleBizServiceImpl.saveDistributionRule(skuRule);
	    	        	}
	    	        }
	    	        //删除优先核销分配规则
	    	        if(deletedSkuDistributionRules != null && !deletedSkuDistributionRules.isEmpty()){
	    	        	workshopDistributionRuleBizServiceImpl.deleteRule(deletedSkuDistributionRules);
	    	        }
    	        }
    	        PartnerView partnerView = new PartnerView();
    	        partnerView.setId(partnerIdl);
    	        etoBizService.synPartner(partnerView);
    	        materialPlatformBizService.notifyPartnerUpdate(partnerIdl);
//    	        //保存企业号功能维护
//    	        savePartnerDepartments(partnerIdl,mechPowerList,owerPowerList);
//    	        //新建企业号SP
//    	        updateO2oSp(partnerIdl, partnerName);

    			map.put("resultData", "success");
            } catch (WxPltException e) {
    			map.put("resultData", "fail");
    			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    			map.put("resultMsg", e.getExpMsg());
    			e.printStackTrace();
    		} catch (Exception e) {
    			map.put("resultData", "fail");
    			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    			//add by bo.liu 180111
    			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
    			map.put("resultMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
    			e.printStackTrace();
    		}
        return map;
	}

	@Override
    public Map<String,Object> getPartnerInfoById(Long partnerId){
    	Map<String,Object> map = new HashMap<String,Object>();
 		Map<String, Object> params = new HashMap<String, Object>();
 		params.put("partnerId", partnerId);
 		try {
 			List<PartnerView> list = organizationVoMapper.selectPartnersByParams(params);
 			if(list.size() == 1){
                PartnerView partnerView = list.get(0);
                //获取字典表拆分
                ArrayList<String> types = new ArrayList<String>();
                List<DicItemVo> dicItemVos = dicItemVoMapper.selectByCode(Constants.PARTNER_PROPERTY_LABEL);
                for (DicItemVo dicItemVo : dicItemVos) {
                    if(partnerView.getPartnerPropertyLabel() != null
                            && (partnerView.getPartnerPropertyLabel() & Integer.valueOf(dicItemVo.getDicItemCode()))> 0){
                        types.add(dicItemVo.getDicItemCode());
                    }
                }
                if(CollectionUtil.isNotEmpty(types)){
                    partnerView.setPartnerPropertyText(CollectionUtil.join(types, ","));
                }
                //VerificationRule rule = verificationRuleBizServiceImpl.getVerificationRuleByPartnerId(partnerId);
 				/*List<RegionPartnerVo> relations  =  regionPartnerServiceImpl.getRegionPartnerByPartnerId(partnerId);
 				List<Long> regionIdList  = new ArrayList<Long>();
 				List<RegionVo> regionList = new ArrayList<RegionVo>();
 				for(int i=0;i<relations.size();i++){
 					RegionPartnerVo rpv = relations.get(i);
 					regionIdList.add(rpv.getRegionId());
 				}
 				if(regionIdList.size()>0){
 					regionList = regionServiceImpl.getRegionByRegionIdIncludeCityAndProvince(regionIdList);
 				}*/
 				List<RegionVo> regionList = regionServiceImpl.selectDistCityProvinceByParterId(partnerId);
 				map.put("regionList", regionList);
 	 	 		map.put("resultData", partnerView);
 	 	 		params = new HashMap<String, Object>();
 	 	 		params.put("dealerId", partnerId);
 	 	 		
 	 	 		List<RegionVo> citys = organizationVoMapper.getCitysSelectedByPartnerId(partnerId);
 	 	 		map.put("cityList", citys);
 	 	 		
 				//业务功能权限
 				DealerBusinessFunExample dealerBusinessFunExample = new DealerBusinessFunExample();
 				dealerBusinessFunExample.createCriteria().andDealerIdEqualTo(partnerId).andBusinessCustomIdEqualTo(-1l);
 				map.put("selectedBusinessFuns", dealerBusinessFunMapper.selectByExample(dealerBusinessFunExample));
 				//产品权限
 				Map<String, Object> reqMap = new HashMap<String, Object>();
 				StringBuilder sBuilder = new StringBuilder(1000)
 						.append("exists (select 1 from wx_t_dealer_product_permission dpp0 where dpp0.dealer_id=")
 						.append(partnerId).append(" and dpp0.product_sku=p.sku)");
 				reqMap.put("constraintSql", sBuilder.toString());
 				map.put("products", productVoMapper.getProductsByParam(reqMap));

 	 	 		//获取激励合同列表
 	 	 		WxAttFileExample example = new WxAttFileExample();
 	 	 		com.sys.file.model.WxAttFileExample.Criteria criteria = example.createCriteria();
 	 	    	criteria.andSourceIdEqualTo(partnerId);
 	 	    	criteria.andSourceTypeEqualTo(WxAttFile.SourceType_VerificationRewardContract);
 	 			List<WxAttFile> attachementList  = wxAttFileMapper.selectByExample(example);
 	 			map.put("attachementList", attachementList);
 	 			//Map<String,Object> departments = queryDepartmentsByPartnerId(partnerId);
 	 			//map.put("departments", departments);
 	 	 		//map.put("rule", rule);
 	 	 		//map.put("skuDistributions", workshopDistributionRuleBizServiceImpl.querySkuDistributionRules(partnerId));
 	 	 		map.put("code", "success");
 			}else{
 				map.put("code", "fail");
 			}
 	 	} catch (Exception e) {
 	 		map.put("code", "fail");
 	 		e.printStackTrace();
 	 	}
 	 	return map;
    }
    
    @Override
	public Map<String, Object> findPartners(Long regionId) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			PartnerCtrlParam param = new PartnerCtrlParam();
			param.setRegionId(regionId);
			List<OrganizationVo> ovList = organizationVoMapper.selPartners(param);
			map.put("data", ovList);
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}
    
    /**
     * 通过区域id 查找区域对应的合伙人（机构）列表
     * @param regionId
     * @return
     */
    	@Override
    	public Map<String, Object> getPartnerListByRegionId(String regionId){
    		if(null!=regionId){
    			return findPartners(Long.parseLong(regionId));
    		}else{
        		Map<String, Object> map = new HashMap<String, Object>(2);
        		List<OrganizationVo> partnerVoList = new ArrayList<OrganizationVo>();
    			WxTUser user  = ContextUtil.getCurUser();
    			long isChevronManager = user.getmUserTypes();
    			if(1==isChevronManager){
    				OrganizationVoExample example = new OrganizationVoExample();
        			com.sys.organization.model.OrganizationVoExample.Criteria criteria = example.createCriteria();
        			criteria.andTypeEqualTo(1);//合伙人
        			partnerVoList = organizationVoMapper.selectByExample(example);
    			}                           
    			map.put("data", partnerVoList);
    			map.put("success", true);
        		return map;
    		}
    	}
    	
//    	 @Override
//	 /**
//	     * 通过合伙人id 查找对应的区域列表
//	     * @param regionId
//	     * @return
//	     */
//	    public Map<String,Object> getOrganizationInfoById(String organizationId){
//	    	 Map<String,Object> map = new HashMap<String,Object>();
//	    	 Long id  = Long.valueOf(organizationId);
//			try {
//				 OrganizationVo organizationVo = organizationVoMapper.selectByPrimaryKey(id);
//				 List<RegionPartnerVo> list  =  regionPartnerServiceImpl.getRegionPartnerByPartnerId(id);
//				 List<Long> regionIdList  = new ArrayList<Long>();
//				 List<RegionVo> regionList = new ArrayList<RegionVo>();
//				 for(int i=0;i<list.size();i++){
//					 RegionPartnerVo rpv = list.get(i);
//					 regionIdList.add(rpv.getRegionId());
//				 }
//				 if(regionIdList.size()>0){
//					 regionList = regionServiceImpl.getRegionByRegionIdIncludeCityAndProvince(regionIdList);
//				 }
//				 map.put("organization", organizationVo);
//				 map.put("regionList", regionList);
//		    	 map.put("code", "success");
//			} catch (Exception e) {
//				map.put("resultData", "fail");
//				e.printStackTrace();
//			}
//	    	return map;
//	    }
    	 
	@Override
    public Map<String,String> removeOrganization(long partnerId) throws Exception {
        Map<String,String> map = new HashMap<String,String>();
        organizationVoMapper.deleteByPrimaryKey(partnerId);
        regionPartnerServiceImpl.deleteByPartnerId(partnerId);
        partnerO2OEnterpriseMapper.deleteByPartnerId(partnerId);
        map.put("resultData", "success");
        return map;
    }
	
	@Override
	public Map<String, Object> disableOrganization(long partnerId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			final Date now = DateUtil.getCurrentDate();
			final Long userId = ContextUtil.getCurUserId();
			Map<String, Object> workshopParams = new HashMap<String, Object>();
			workshopParams.put("partnerId", partnerId);
//			workshopParams.put("excludeStatus", WorkShopVo.WORKSHOP_STATUS_DELETE);
			final List<WorkshopMaster> deletedWorkshops = workshopMasterMapper.queryByParams(workshopParams);
			//假删除门店
			int i = 1;
			int BATCH_SIZE = 300;
			List<Long> workshopIds = new ArrayList<Long>(BATCH_SIZE);
			WorkshopMaster newWorkShopVo = new WorkshopMaster();
//			newWorkShopVo.setStatus(WorkShopVo.WORKSHOP_STATUS_DELETE);
			newWorkShopVo.setDeleteFlag(1);
			newWorkShopVo.setUpdateTime(now);
			for(WorkshopMaster workShopVo : deletedWorkshops){
				if(i++ % BATCH_SIZE == 0){
					WorkshopMasterExample workShopVoExample = new WorkshopMasterExample();
					workShopVoExample.createCriteria().andIdIn(workshopIds);
					workshopMasterMapper.updateByExampleSelective(newWorkShopVo, workShopVoExample);
					workshopIds.clear();
				}
				workshopIds.add(workShopVo.getId());
			}
			if(!workshopIds.isEmpty()){
				WorkshopMasterExample workShopVoExample = new WorkshopMasterExample();
				workShopVoExample.createCriteria().andIdIn(workshopIds);
				workshopMasterMapper.updateByExampleSelective(newWorkShopVo, workShopVoExample);
			}
			WxTUserExample userExample = new WxTUserExample();
			userExample.createCriteria().andTypeIsNull().andOrgIdEqualTo(partnerId).andStatusEqualTo(1);
			userExample.or().andTypeNotEqualTo("1").andOrgIdEqualTo(partnerId).andStatusEqualTo(1);
			//获取要删除用户以记录删除日志
			final List<WxTUser> deletedUsers = wxTUserMapper.selectByExample(userExample);
			//假删除用户
			WxTUser user = new WxTUser();
			user.setStatus(0);
			user.setXgTime(now);
			user.setXgUser(userId.toString());
			wxTUserMapper.updateByExampleSelective(user, userExample);
			//记录删除日志
			LogUtils.addLog(new LogTask() {
				
				@Override
				public void execute() throws Exception {
					//记录门店删除日志，记录门店删除前状态
					for(WorkshopMaster vo : deletedWorkshops){
						Log log = new Log();
						log.setCreateTime(now);
						log.setLogType("WorkshopDelete");
						log.setOperator(userId);
						log.setExtProperty1(vo.getId().toString());
						log.setExtProperty2(vo.getStatus().toString());
						logBizService.insert(log);
					}
					//记录用删除日志
					for(WxTUser user : deletedUsers){
						Log log = new Log();
						log.setCreateTime(now);
						log.setLogType("UserDelete");
						log.setOperator(userId);
						log.setExtProperty1(user.getUserId().toString());
						logBizService.insert(log);
					}
				}
			});
			//假删除合伙人
			OrganizationVo newPartner = new OrganizationVo();
			newPartner.setStatus(OrganizationVo.ORG_STOP_STATUS);
			newPartner.setId(partnerId);
			newPartner.setUpdateTime(now);
			newPartner.setUpdator(userId);
			organizationVoMapper.updateByPrimaryKeySelective(newPartner);
			materialPlatformBizService.notifyPartnerUpdate(partnerId);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return map;
	}
	@Override
	public Map<String, Object> getPartnerLstByPartnerName(String partnerName){
		 Map<String,Object> map = new HashMap<String,Object>();
		try {
			 OrganizationVoExample example = new OrganizationVoExample();
			 Criteria criteria = example.createCriteria();
			  criteria.andOrganizationNameLike(partnerName);
			 List<OrganizationVo> list  =   organizationVoMapper.selectByExample(example);
			 logger.debug("---getPartnerLstByPartnerName:"+list.size());
			 map.put("data", list);
	    	 map.put("code", "success");
		} catch (Exception e) {
			map.put("resultData", "fail");
			e.printStackTrace();
		}
    	return map;
	}

	@Override
	public Map<String, Object> getPartnersByCtrl(PartnerCtrlParam param) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		if(param == null){
			param = new PartnerCtrlParam();
		}
		try {
			map.put("data", organizationVoMapper.selPartners(param));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> getAllWechatEnterprise() {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			String key = (String) Constants.getSystemPropertyByCodeType(Constants.WECHAT_GET_ALL_ENTERPRISE_KEY);
			String token = DigestUtils.md5Hex(key);
			Map<String, String> postData = new HashMap<String, String>();
			postData.put("token", token);
			String serviceUrl = (String) Constants.getSystemPropertyByCodeType(Constants.WECHAT_GET_ALL_ENTERPRISE_URL);
			logger.info("=============================调用获取微信企业号接口====================================");
			logger.info("url: " + serviceUrl);
			logger.info("token: " + token);
			String responseContent = HttpSender.post(serviceUrl, postData, null, null);
			logger.info("responseContent: " + responseContent);
			map.put("data", responseContent);
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> queryPartners(Long regionId, String partnerName) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		Map<String, Object> params = new HashMap<String, Object>();
		if(partnerName != null && !"".equals(partnerName.trim())){
			params.put("partnerName", "%" + partnerName + "%");
		}
		params.put("regionId", regionId);
		try {
			WxTUser user = ContextUtil.getCurUser();
			params.put("loginUserType", WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l);
			params.put("loginUserOrg", user.getOrgId());
			map.put("data", organizationVoMapper.selectPartnersByParams(params));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> getWechatDepartments(Long partnerId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			String serviceUrl = (String) Constants.getSystemPropertyByCodeType("o2o_service.getDepartments.url");
			String key = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC_KEY);
			String token = DigestUtils.md5Hex(partnerId + "0" + key);
			Map<String, Object> postData = new HashMap<String, Object>();
			postData.put("token", token);
			postData.put("spid", partnerId.toString()); 
			logger.info("spid: " + partnerId + ", key: " + key + ", token: " + token);
			String responseContent = HttpSender.postJSON(serviceUrl, postData, null);
			logger.info("responseContent: " + responseContent);
			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
			List<Department> departmentList = new ArrayList<Department>();
			if (responseJsonObj.getInt("errcode") == 0){
				JSONArray departmentsArray = responseJsonObj.getJSONArray("department");
				for (int i = 0; i < departmentsArray.size(); i++){
					JSONObject departmentObj = departmentsArray.getJSONObject(i);
					Department department = new Department();
					department.setId(departmentObj.getString("id"));
					department.setName(departmentObj.getString("name"));
					departmentList.add(department);
				}
				map.put("mechanicDepartments", getO2ODepartmentIdsByPartnerId(partnerId, WorkshopEmployeeBizService.EMPLOYEE_TYPE_MECHANIC));
				map.put("ownerDepartments", getO2ODepartmentIdsByPartnerId(partnerId, WorkshopEmployeeBizService.EMPLOYEE_TYPE_OWNER));
			} else {
				logger.error("getWechatDepartments error :" + responseJsonObj.getString("errmsg"));
			}
			map.put("data", departmentList);
			map.put("success", true);
		} catch (Exception e) {
			logger.error(e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public String getEnterpriseCodeByPartnerId(Long partnerId) {
		PartnerO2OEnterpriseExample example = new PartnerO2OEnterpriseExample();
		example.createCriteria().andPartnerIdEqualTo(partnerId);
		List<PartnerO2OEnterprise> resultList = partnerO2OEnterpriseMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			return StringUtils.stringNullRep(resultList.get(0).getEnterpriseCode(), "") ;
		}
		return "";
	}

	@Override
	public Long getDistributorIdByPartnerId(Long partnerId) {
		PartnerO2OEnterpriseExample example = new PartnerO2OEnterpriseExample();
		example.createCriteria().andPartnerIdEqualTo(partnerId);
		List<PartnerO2OEnterprise> resultList = partnerO2OEnterpriseMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			return resultList.get(0).getDistributorId();
		}
		return null;
	}

	@Override
	public String getO2ODepartmentIdsByPartnerId(Long partnerId, String type) {
		PartnerO2ODepartmentExample example = new PartnerO2ODepartmentExample();
		example.createCriteria().andPartnerIdEqualTo(partnerId).andTypeEqualTo(type);
		List<PartnerO2ODepartment> departmentList = partnerO2ODepartmentMapper.selectByExample(example);
		if (departmentList != null && !departmentList.isEmpty()){
			return StringUtils.stringNullRep(departmentList.get(0).getDepartmentIds(), "");
		}
		return "";
	}
	
	
	@Override
	public Map<String, Object> queryPartnersNew(PartnerParams params) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<PartnerView> lstPartners = new ArrayList<PartnerView>();
		if(null==params)
		{
			map.put("success", false);
			return map;
		}
		String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
		if("false".equals(is_custommybatisintercepor))
		{
			params.setIsOpernCustomMybatisInterceptor("2");
		}
		
		String partnerName = params.getPartnerName();
		if(partnerName != null && !"".equals(partnerName.trim())){
			params.setPartnerName("%" + partnerName + "%");
			params.setFirtPyForQueryField(GetPinyinStringUtil.getPinYinHeadCharForSourceStr(partnerName.trim()));
		}
		String queryField = params.getQueryField();
		if(queryField != null && !"".equals(queryField.trim()))
		{
			params.setQueryField("%" + queryField + "%");
			params.setFirtPyForQueryField(GetPinyinStringUtil.getPinYinHeadCharForSourceStr(queryField.trim()));
		}
		try {
			WxTUser user = ContextUtil.getCurUser();
			params.setLoginUserOrg(user.getOrgId());
			params.setLoginUserType(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l);
			
			
			Long totalRecord = 0L;
			lstPartners = organizationVoMapper.selectPartnersByParamsNew(params);
			if("false".equals(is_custommybatisintercepor))//没有开数据权限
			{
				totalRecord = organizationVoMapper.countPartnersByParamsNew(params);
			}else
			{
				
				totalRecord = params.getTotalCount();
			}
			if(params.isIncludeAreas() && lstPartners != null && !lstPartners.isEmpty()){
				Map<Long, PartnerView> partnerMap = new HashMap<Long, PartnerView>(lstPartners.size());
				List<Long> partnerIds = new ArrayList<Long>(lstPartners.size());
				for(PartnerView partnerView : lstPartners){
					partnerIds.add(partnerView.getId());
					partnerMap.put(partnerView.getId(), partnerView);
				}
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put("partnerIds", partnerIds);
				List<RegionPartnerVo> regionPartnerVos = regionPartnerVoMapper.queryPartnerAreas(paramMap);
				if(regionPartnerVos != null && !regionPartnerVos.isEmpty()){
					for(RegionPartnerVo regionPartnerVo : regionPartnerVos){
						PartnerView partner = partnerMap.get(regionPartnerVo.getPartnerId());
						if(partner.getAreas() == null){
							partner.setAreas(new ArrayList<String>(5));
						}
						partner.getAreas().add(regionPartnerVo.getAreaName());
					}
				}
			}
			//转换合伙人属性
            List<DicItemVo> dicItemVos = dicItemVoMapper.selectByCode(Constants.PARTNER_PROPERTY_LABEL);
            for (PartnerView lstPartner : lstPartners) {
                lstPartner.setPartnerPropertyText("");
                Integer partnerPropertyLabel = lstPartner.getPartnerPropertyLabel();
                if(partnerPropertyLabel != null && partnerPropertyLabel > 0){
                    HashSet<String> lable = new HashSet<String>();
                    for (DicItemVo dicItemVo : dicItemVos) {
                        if((partnerPropertyLabel & Integer.valueOf(dicItemVo.getDicItemCode())) > 0){
                            lable.add(dicItemVo.getDicItemName());
                        }
                    }
                    lstPartner.setPartnerPropertyText(CollectionUtil.join(lable, ","));
                }
            }
            map.put(RESULT_LST_KEY, lstPartners);
			map.put(RESULT_TOTAL_RECORD, totalRecord);
			map.put("success", true);
		} catch (Exception e) {
			logger.error(e);
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> queryDepartmentsByPartnerId(Long partnerId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			String mechanicDepartmentIdStr = getO2ODepartmentIdsByPartnerId(partnerId, WorkshopEmployeeBizService.EMPLOYEE_TYPE_MECHANIC);
			String ownerDepartmentIdStr = getO2ODepartmentIdsByPartnerId(partnerId, WorkshopEmployeeBizService.EMPLOYEE_TYPE_OWNER);
			String[] mechanicDepartmentIds = mechanicDepartmentIdStr.split(",");
			String[] ownerDepartmentIds = ownerDepartmentIdStr.split(",");
			resultMap.put("success", true);
			resultMap.put("mechanicDeptIds", mechanicDepartmentIds);
			resultMap.put("ownerDeptIds", ownerDepartmentIds);
		} catch (Exception e) {
			logger.error("queryDepartmentsByPartnerId exception", e);
			resultMap.put("success", false);
		}
		return resultMap;
		
	}

	@Override
	 @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> savePartnerDepartments(Long partnerId, String[] mechanicDepartmentIds,
			String[] ownerDepartmentIds) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			partnerO2ODepartmentMapper.deleteByPartnerId(partnerId);
			
			String mechanicDepartmentIdStr = StringUtils.arrayToDelimited(mechanicDepartmentIds);
			insertPartnerO2ODepartment(partnerId, mechanicDepartmentIdStr, WorkshopEmployeeBizService.EMPLOYEE_TYPE_MECHANIC);
			
			String ownerDepartmentIdStr = StringUtils.arrayToDelimited(ownerDepartmentIds);
			insertPartnerO2ODepartment(partnerId, ownerDepartmentIdStr, WorkshopEmployeeBizService.EMPLOYEE_TYPE_OWNER);
			resultMap.put("success", true);
		} catch (Exception e) {
			logger.error("savePartnerDepartments exception", e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("success", false);
		}
		return resultMap;
	}

	private void insertPartnerO2ODepartment(Long partnerId, String departmentIds, String type) {
		PartnerO2ODepartment dept = new PartnerO2ODepartment();
		dept.setCreatedBy(ContextUtil.getCurUserId());
		dept.setCreationTime(DateUtil.getCurrentDate());
		dept.setDepartmentIds(departmentIds);
		dept.setPartnerId(partnerId);
		dept.setType(type);
		partnerO2ODepartmentMapper.insertSelective(dept);
	}
//
//	@Override
//	public Map<String, Object> findAllProvinces() {
//		Map<String, Object> map = new HashMap<String, Object>(2);
//		try {
//			WxTUser currUser  = ContextUtil.getCurUser();
//			if(WxTUser.USER_MODEL_CHEVRON.equals(currUser.getUserModel())){
//				map.put("data", regionServiceImpl.selChildrenByParentId(1l));
//			}else{
//				long orgId  = currUser.getOrgId();
//				List<Long> provIds  = regionPartnerServiceImpl.getProvIdsByPartnerId(orgId);
//				List<RegionVo> provinceList = new ArrayList<RegionVo>();
//				if(null != provIds && !provIds.isEmpty()){
//				    provinceList.addAll(regionServiceImpl.getRegionByRegionId(provIds));
//				}
//				map.put("data", provinceList);
//			}
//			map.put("success", true);
//		} catch (Exception e) {
//			e.printStackTrace();
//			map.put("success", false);
//		}
//		return map;
//	}

	@Override
	public String getDealerBusinessCode(Long dealerId) {
		//业务功能权限
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("dealerId", dealerId);
		StringBuilder stringBuilder = new StringBuilder();
		for(DealerBusinessFun item : dealerBusinessFunMapper.queryActiveFunCodesByParams(paramMap)){
			stringBuilder.append("<").append(item.getBusinessFunCode()).append(">");
			//附加功能编码...
		}
		return stringBuilder.toString();
	}

	@Override
	public String getPartnerProperty(Long partnerId) {
		PartnerView partner = organizationVoMapper.selectPartnerFullInfoByPartnerId(Long.valueOf(partnerId));		
		return partner.getPartnerProperty();
	}

	@Override
	public Map<String,Object> getPartnerAndSapCode(Long partnerId) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("partner", organizationVoMapper.selectByPrimaryKey(partnerId));
		String sapCode = new String();
		PartnerO2OEnterpriseExample example = new PartnerO2OEnterpriseExample();
		example.createCriteria().andPartnerIdEqualTo(partnerId);
		List<PartnerO2OEnterprise> resultList = partnerO2OEnterpriseMapper.selectByExample(example);
		if (resultList != null && !resultList.isEmpty()){
			sapCode =  StringUtils.stringNullRep(resultList.get(0).getSapCode(), "") ;
		}
		map.put("sapCode", sapCode);
		map.put("pointPermission",DealerPermissUtil.getCurUserDealerPermissCode(dicService));
		try {
			WXTPointAccountVoExample acExample = new WXTPointAccountVoExample();
			acExample.createCriteria().andPointAccountOwnerIdEqualTo(partnerId)
					.andPointAccountTypeEqualTo(PointAccountType.SP.name()).andDeleteFlagEqualTo(false);
			List<WXTPointAccountVo> accountVos = pointAccountBizService.queryByExample(acExample);
			if(accountVos!=null && !accountVos.isEmpty()) {
				map.put("pointAccount", accountVos.get(0));
			}
		} catch (Exception e){
			logger.error("getPartnerAndSapCode里面调用pointAccountBizService.queryByExample partnerId" + partnerId + ",error=" + e.getLocalizedMessage());
			map.put("pointAccount", null);
		}


		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void save(PartnerView partnerView) throws WxPltException {
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();
		OrganizationVo organizationVo = partnerView.toOrganizationVo();
		organizationVo.setUpdateTime(now);
		organizationVo.setUpdator(userId);
		PartnerO2OEnterprise partnerO2OEnterprise = organizationVo.getPartnerO2OEnterprise();
		partnerO2OEnterprise.setLastUpdatedBy(userId);
		partnerO2OEnterprise.setLastUpdateTime(now);
		List<RegionPartnerVo> regionPartnerVos = partnerView.getRegionPartnerVos();
		if(organizationVo.getId() == null) {
			organizationVo.setCreateTime(now);
			organizationVo.setCreator(userId);
			organizationVo.setStatus(OrganizationVo.ORG_START_STATUS);
			organizationVoMapper.insertSelective(organizationVo);
			partnerO2OEnterprise.setPartnerId(organizationVo.getId());
			partnerO2OEnterprise.setCreatedBy(userId);
			partnerO2OEnterprise.setCreationTime(now);
			partnerO2OEnterpriseMapper.insertSelective(partnerO2OEnterprise);
//			//同步授权区域
//			regionPartnerVoMapper.synRetailerRegion(RegionPartnerVoMapper.SYN_TYPE_RETAILER, organizationVo.getId());
		}else {
			organizationVoMapper.updateByPrimaryKeySelective(organizationVo);
			partnerO2OEnterpriseMapper.updateByPartnerIdSelective(partnerO2OEnterprise);
			if(regionPartnerVos != null) {
				regionPartnerServiceImpl.deleteByPartnerId(organizationVo.getId());
			}
		}
		if(regionPartnerVos != null) {
			for(RegionPartnerVo regionPartnerVo : regionPartnerVos) {
				regionPartnerVo.setPartnerId(organizationVo.getId());
			}
		}
		if(regionPartnerVos != null && !regionPartnerVos.isEmpty()) {
			regionPartnerServiceImpl.batchInsert(regionPartnerVos);
		}
	}

	@Override
	public PartnerView getPartnerViewByorgId(Long partnerId) {
		return organizationVoMapper.selectPartnerFullInfoByPartnerId(Long.valueOf(partnerId));
	}


	@Override
	public Map<String,String> queryDistributorIds(Long partnerId,Long userId) {
			logger.info("入参partnerId，userId分别=="+partnerId+" , "+userId);
		return organizationVoMapper.queryDistributorIds(String.valueOf(partnerId),String.valueOf(userId));
	}
}
