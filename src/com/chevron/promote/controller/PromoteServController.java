package com.chevron.promote.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.promote.business.PromoteBizService;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.PromoteActivityFeedback;
import com.chevron.promote.model.ResponseActivityInfoMobile;
import com.chevron.promote.service.IPromoteApplicationService;
import com.chevron.thirdorder.model.Result;
import com.chevron.thirdorder.model.ResultEnum;
import com.chevron.thirdorder.model.ResultUtil;
import com.common.util.ContextUtil;
import com.sys.auth.model.WxTUser;

/**
 * 
 * @Author: bo.liu  2018-6-14 下午3:24:40 
 * @Version: $Id$
 * @Desc: <p>地促控制类</p>
 */
@Controller
@RequestMapping(value = "/promoteServCtrl")
public class PromoteServController extends BasePromote{
	private static Logger log = LoggerFactory.getLogger(PromoteServController.class);
	@Resource
	IPromoteApplicationService promoteApplyService;
	@Resource
	PromoteBizService promoteBizService;
	
	/**
	 * 获取活动信息
	 * <AUTHOR> 2018-6-28 下午8:17:33
	 * @param activityUUid
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getActivityInfoMobile.do", method = RequestMethod.GET, produces="application/json")
	public Result getActivityType(@RequestParam("activityUUid") String activityUUid) {
		ResponseActivityInfoMobile responseActivityInfoMobile = null;
		try {
			log.info("getActivityInfoMobile:", activityUUid);
			if(null==activityUUid || activityUUid.isEmpty())
			{
				ResultUtil.error(ResultEnum.PARAM_IS_NULL.getCode(), ResultEnum.PARAM_IS_NULL.getMsg(), null);
			}
			responseActivityInfoMobile = promoteApplyService.getPromoteActivityInfoMobile(activityUUid);
			log.info("getActivityInfoMobile responseActivityInfoMobile:", responseActivityInfoMobile.toString());
		} catch (Exception e) {
			e.printStackTrace();
			log.error("error", e);
			return ResultUtil.error(ResultEnum.SYS_ERROR.getCode(), ResultEnum.SYS_ERROR.getMsg()+e.getLocalizedMessage(), null);
		}
		return ResultUtil.success(ResultEnum.SUCCESS_OPER.getCode(), ResultEnum.SUCCESS_OPER.getMsg(), responseActivityInfoMobile);
   }
	
	
	/**
	 * 提交活动反馈，供手机端页面
	 * <AUTHOR> 2018-6-27 下午5:44:28
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/submitActivityFeedBack.do", method = RequestMethod.POST, produces="application/json")
	public Result submitActivityFeedBack(@RequestBody PromoteActivityFeedback activityFeedBack) {
		try {
			log.info("submitActivityFeedBack:", activityFeedBack);
			if(null==activityFeedBack)
			{
				ResultUtil.error(ResultEnum.PARAM_IS_NULL.getCode(), ResultEnum.PARAM_IS_NULL.getMsg(), null);
			}
			Map<String,Object> resultMap = promoteApplyService.submitActivityFeedBack(activityFeedBack);
			String code = (String) resultMap.get(RESULT_CODE_KEY);
			if(ERROR_CODE.equals(code))
			{
				throw new Exception((String) resultMap.get(RESULT_ERROR_MSG_KEY));
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("error", e);
			return ResultUtil.error(ResultEnum.SYS_ERROR.getCode(), ResultEnum.SYS_ERROR.getMsg()+e.getLocalizedMessage(), null);
		}
		return ResultUtil.success();
   }
	
	@RequestMapping(value = "/exportAllPromoteOrder.do")
	public Map<String,Object> exportAllPromoteOrder (@RequestParam("keyWord") String keyWord,
			@RequestParam("dateStartStr") String dateStartStr,@RequestParam("dateEndStr") String dateEndStr,
			HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 导出全部
		Map<String, Object> reqMap = new HashMap<String,Object>();
		reqMap = promoteBizService.getQueryPromoteOrderMap(reqMap,keyWord,dateStartStr,dateEndStr);
		resultMap = promoteBizService.exportAllPromoteOrder(reqMap,request, response);
		return resultMap;
	}

	
	
}
