begin 
	set xact_abort on
	declare @start_time datetime /*同步开始时间*/
	declare @end_time datetime/*同步截止时间*/
	declare @syn_time datetime/*同步时间*/
	set @start_time='2017-09-21 02:49:59.080'
	set @end_time='2017-09-21 23:43:15.860'
	set @syn_time='2017-09-27 21:39:22.033'
/*同步门店修改数据*/
update sw set sw.region_id=isnull(ws.region_id, -1), sw.workshop_name=ws.work_shop_name,sw.workshop_address=ws.work_shop_address,
sw.workshop_status=(case when ws.status in ('0', '1', '3') then ws.status else '-101' end),
sw.is_enable=(case when ws.status in ('0', '1', '3') then 1 else 0 end),sw.workshop_category='OTHER',
sw.discover_time=isnull((select top 1 ts1.xg_sj FROM wx_task_main task_main join wx_task_sub ts1 on ts1.task_main_id=task_main.task_main_id where ts1.org_id=ws.id and task_main.tmb_type_code='TT_2_SD' and task_main.task_status=4), ws.create_time),
sw.active_time=(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=ws.id and not exists (select 1 from wx_t_workshop_status wss1 where wss1.workshop_id=wss.workshop_id and wss.create_time>wss.create_time)),
sw.syn_update_time=@syn_time
from [chemid].[CHE_MID].dbo.syn_data_workshop sw join wx_t_work_shop ws on sw.workshop_id=ws.id 
where ws.update_time>@start_time and ws.update_time<=@end_time
/*同步新建门店数据*/
insert into [chemid].[CHE_MID].dbo.syn_data_workshop (workshop_id,partner_id,region_id,workshop_name,workshop_address,discover_time,active_time,workshop_status,is_enable,workshop_category,syn_create_time)
select ws.id,(select top 1 wp.partner_id from wx_t_workshop_partner wp where wp.workshop_id=ws.id order by (case when wp.relation_type='trade' then 0 else 1 end), wp.create_time desc), 
isnull(ws.region_id, -1),ws.work_shop_name,ws.work_shop_address,
isnull((select top 1 ts1.xg_sj FROM wx_task_main task_main join wx_task_sub ts1 on ts1.task_main_id=task_main.task_main_id where ts1.org_id=ws.id and task_main.tmb_type_code='TT_2_SD' and task_main.task_status=4), ws.create_time),
(select top 1 wss.create_time from wx_t_workshop_status wss where wss.workshop_with_status='3' and wss.workshop_id=ws.id and not exists (select 1 from wx_t_workshop_status wss1 where wss1.workshop_id=wss.workshop_id and wss.create_time>wss.create_time)),
case when ws.status in ('0', '1', '3') then ws.status else '-101' end, case when ws.status in ('0', '1', '3') then 1 else 0 end,'OTHER',@syn_time
from wx_t_work_shop ws where (@start_time is null or ws.create_time > @start_time) and ws.create_time <= @end_time
and not exists (select 1 from [chemid].[CHE_MID].dbo.syn_data_workshop sw where sw.workshop_id=ws.id)
/*修复大地保险门店合伙人*/
update [chemid].[CHE_MID].dbo.syn_data_workshop set partner_id=279 where partner_id is null and is_enable=0
end