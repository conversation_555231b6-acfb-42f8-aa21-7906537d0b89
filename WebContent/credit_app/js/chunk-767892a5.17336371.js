(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-767892a5"],{1627:function(e,t,r){},"419f":function(e,t,r){"use strict";var a=r("1627"),n=r.n(a);n.a},"434a":function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form-item",{staticClass:"is-required",attrs:{label:"Current Payment Term 现有信用账期 : ","label-width":e.labelWidth}},[r("el-input",{attrs:{size:"small",disabled:"",placeholder:""},model:{value:e.applyForm.cbiPaymentTermOfYearN1,callback:function(t){e.$set(e.applyForm,"cbiPaymentTermOfYearN1",t)},expression:"applyForm.cbiPaymentTermOfYearN1"}})],1)},n=[],l=r("cebc"),i=r("2f62"),s={name:"credit-apply-cbiCurrentPaymentTerm",computed:Object(l["a"])({},Object(i["b"])(["applyForm","isApplyNotInProcess"]),{labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"},value:{get:function(){return this.applyForm.cbiPaymentTermOfYearN1}}}),watch:{value:function(e){/^cash in advance$/i.test(e)&&"CV_REQUEST"===this.applyForm.creditType&&this.isApplyNotInProcess&&this.applyForm.cbiFinancialStatementsAttId<=0&&this.$alert("现金客户要求最新财报 Financial statements is required when current payment term equals cash in advance","Prompt",{confirmButtonText:"Confirm"})}}},c=s,o=r("2877"),u=Object(o["a"])(c,a,n,!1,null,null,null);t["a"]=u.exports},c6a3:function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"cvBasic",staticClass:"form",attrs:{model:e.applyForm,rules:e.rules}},[r("header-piece"),r("div",{staticClass:"form-title"},[e._v("Application Information 申请人信息")]),r("application"),r("div",{staticClass:"form-title"},[e._v("Customer Basic Information 客户基础信息")]),r("customer-basic"),r("div",{staticClass:"form-title"},[e._v("Contract Information 财务相关信息")]),r("other"),r("upload")],1)},n=[],l=r("cebc"),i=r("4ade"),s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-row",[r("el-col",{attrs:{span:8}},[r("prepared-by")],1),r("el-col",{attrs:{span:8}},[r("region")],1),r("el-col",{attrs:{span:8}},[r("request-date")],1),r("el-col",{attrs:{span:8}},[r("requested-by")],1),r("el-col",{attrs:{span:8}},[r("telephone")],1),r("el-col",{attrs:{span:8}},[r("sales-team")],1),r("el-col",{attrs:{span:8}},[r("credit-csr")],1)],1)},c=[],o=r("3998"),u=r("6981"),p=r("6599"),d=r("e688"),m=r("22bf"),b=r("ff04"),f=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form-item",{attrs:{label:"CSR 抄送员 : ","label-width":"280px"}},[r("el-select",{attrs:{placeholder:"",multiple:"","multiple-limit":2,disabled:e.disabled,size:"small"},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)},h=[],y=r("768b"),v=(r("96cf"),r("3b8d")),O=(r("28a5"),r("2f62")),g=r("662e"),C={name:"credit-apply-cbiCreditCsr",data:function(){return{options:[]}},computed:Object(l["a"])({},Object(O["b"])(["applyForm","canEditApply"]),{aiRequestedBy:function(){return this.applyForm.aiRequestedBy},disabled:function(){return!this.canEditApply||!this.applyForm.aiRequestedBy},value:{get:function(){var e=this.applyForm.cbiCreditCsr||"";return e?e.split(","):[]},set:function(e){this.$store.commit("UPDATE_APPLY_FORM",{cbiCreditCsr:e.join(",")})}}}),watch:{aiRequestedBy:function(){this.getList()}},created:function(){this.getList()},methods:{getList:function(){var e=Object(v["a"])(regeneratorRuntime.mark(function e(){var t,r,a,n;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:if(!(this.options.length>0)){e.next=2;break}return e.abrupt("return",!1);case 2:if(this.applyForm.aiRequestedBy){e.next=4;break}return e.abrupt("return",!1);case 4:return e.next=6,g["a"].getCreditCsr({name:this.applyForm.aiRequestedBy});case 6:if(t=e.sent,r=Object(y["a"])(t,2),a=r[0],n=r[1],a){e.next=12;break}return e.abrupt("return",!1);case 12:if(n.result){e.next=14;break}return e.abrupt("return",!1);case 14:this.options=n.result.data.map(function(e){return{value:e.loginName,label:e.userName}});case 15:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()}},_=C,A=r("2877"),E=Object(A["a"])(_,f,h,!1,null,null,null),R=E.exports,w={name:"credit-apply-application",components:{PreparedBy:o["a"],Region:u["a"],RequestDate:p["a"],RequestedBy:d["a"],Telephone:m["a"],SalesTeam:b["a"],CreditCsr:R}},F=w,q=Object(A["a"])(F,s,c,!1,null,null,null),T=q.exports,j=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-row",[r("el-col",{attrs:{span:12}},[r("customer-id")],1),r("el-col",{attrs:{span:12}},[r("customer-name")],1),r("el-col",{attrs:{span:12}},[r("current-credit-limit")],1),r("el-col",{attrs:{span:12}},[r("current-payment-term")],1),r("el-col",{attrs:{span:12}},[r("requested-cv-order-no")],1),r("el-col",{attrs:{span:12}},[r("upload-order-file")],1)],1)},x=[],P=r("f149"),$=r("cb85"),B=r("d119"),I=r("434a"),D=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",e._l(e.cvRequestOrderArray,function(t,a){return r("el-form-item",{key:t.id,attrs:{label:"Requested CV order No. 申请释放的订单号 : ","label-width":"280px",prop:"cbiRequestedCvOrderNoArray."+a+".value",rules:{required:!0,message:"",trigger:"blur"}}},[r("el-input",{staticClass:"cvOrderNoInput",attrs:{size:"small",disabled:e.disabled,placeholder:""},model:{value:t.value,callback:function(r){e.$set(t,"value",r)},expression:"order.value"}},[e.disabled?e._e():r("template",{slot:"append"},[r("i",{staticClass:"icon-class el-icon-circle-plus-outline",on:{click:function(t){return e.handleAddNewOrder()}}}),a>0?r("i",{staticClass:"icon-class el-icon-remove-outline",on:{click:function(t){return e.handleDeleteOrder(a)}}}):e._e()])],2)],1)}),1)},k=[],N={name:"credit-apply-cbiRequestedCvOrderNo",data:function(){return{orderArray:[]}},computed:Object(l["a"])({},Object(O["b"])(["applyForm","canEditApply","cvRequestOrderArray"]),{disabled:function(){return!this.canEditApply}}),watch:{cvRequestOrderArray:{handler:function(e){console.log(e),this.SET_CV_REQUEST_ORDER_ARRAY(e)},deep:!0}},methods:Object(l["a"])({},Object(O["c"])(["SET_CV_REQUEST_ORDER_ARRAY"]),{handleAddNewOrder:function(){this.cvRequestOrderArray.push({id:Date.now(),value:""})},handleDeleteOrder:function(e){this.cvRequestOrderArray.splice(e,1)}})},S=N,U=(r("419f"),Object(A["a"])(S,D,k,!1,null,"4b187966",null)),L=U.exports,W=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form-item",{attrs:{label:"Upload Order上传订单 : ","label-width":"280px",prop:"uploadOrderFileAttId"}},[r("template",{slot:"label"},[r("el-tooltip",{staticClass:"form-item-label-tooltip",attrs:{content:"请上传该客户的订单",placement:"top-start"}},[r("i",{staticClass:"el-icon-question"})]),e._v("\n    Upload Order上传订单 :\n  ")],1),r("div",{staticStyle:{display:"flex","align-items":"center"}},[r("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.showUploadDialog}},[e._v("\n      UPLOAD\n    ")]),r("span",{staticStyle:{color:"#666","margin-left":"10px"}},[e._v("\n      "+e._s(e.applyForm.uploadOrderFileAttId)+" Files\n    ")])],1)],2)},V=[],Y={name:"credit-apply-upload-order",computed:Object(l["a"])({},Object(O["b"])(["applyForm","canEditApply","canEditCredit"]),{disabled:function(){return!(this.canEditApply||this.canEditCredit)}}),methods:{showUploadDialog:function(){this.$store.commit("UPDATE_UPLOAD_DIALOG_VISIBLE",!0),this.$store.commit("UPDATE_UPLOAD_FILE_NAME","uploadOrderFileAttId"),this.$store.commit("DISABLED_UPLOAD_BUTTON",this.disabled)}}},G=Y,z=Object(A["a"])(G,W,V,!1,null,null,null),Q=z.exports,M={name:"credit-apply-customerBasic",components:{CustomerId:P["a"],CustomerName:$["a"],CurrentCreditLimit:B["a"],CurrentPaymentTerm:I["a"],RequestedCvOrderNo:L,UploadOrderFile:Q}},J=M,H=Object(A["a"])(J,j,x,!1,null,null,null),K=H.exports,X=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-row",[r("el-col",{attrs:{span:12}},[r("comments-from-bu")],1)],1)},Z=[],ee=r("495a"),te={name:"credit-apply-application",components:{CommentsFromBu:ee["a"]}},re=te,ae=Object(A["a"])(re,X,Z,!1,null,null,null),ne=ae.exports,le=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-row",[r("el-col",{attrs:{span:12}},[r("financial-statements")],1),r("el-col",{attrs:{span:12}},[r("payment-commitment")],1),r("el-col",{attrs:{span:24}},[r("cash-deposit-with-amount")],1),r("el-col",{attrs:{span:24}},[r("the3rd-party-guarantee-with-amount")],1),r("el-col",{attrs:{span:24}},[r("bank-guarantee-with-amount")],1),r("el-col",{attrs:{span:24}},[r("personal-guarantee-with-amount")],1)],1)},ie=[],se=r("9178"),ce=r("03f2"),oe=r("bd08"),ue=r("be6d"),pe=r("f093"),de=r("cb99"),me={name:"credit-apply-application",components:{FinancialStatements:se["a"],PaymentCommitment:ce["a"],CashDepositWithAmount:oe["a"],The3rdPartyGuaranteeWithAmount:ue["a"],BankGuaranteeWithAmount:pe["a"],PersonalGuaranteeWithAmount:de["a"]}},be=me,fe=Object(A["a"])(be,le,ie,!1,null,null,null),he=fe.exports,ye=r("65e9"),ve=/((^[1-9]\d*)|^0)(\.\d{2}){0,1}$/,Oe={aiRequestedBy:[{required:!0,message:"",trigger:"blur"}],aiTelephone:[{required:!0,message:"",trigger:"blur"}],aiSalesTeam:[{required:!0,message:"",trigger:"blur"}],cbiCustomerId:[{required:!0,message:"",trigger:"blur"}],cbiRequestedCvOrderNo:[{required:!0,message:"",trigger:"blur"}],uploadOrderFileAttId:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,r){0===t?r(new Error("")):r()},message:"",trigger:"blur"}],cbiCashDepositWithAmount:[{validator:function(e,t,r){t?ve.test(Object(ye["b"])(t))?r():r(new Error("")):r()}}],cbiThe3rdPartyGuaranteeWithAmount:[{validator:function(e,t,r){t?ve.test(Object(ye["b"])(t))?r():r(new Error("")):r()}}],cbiBankGuaranteeWithAmount:[{validator:function(e,t,r){t?ve.test(Object(ye["b"])(t))?r():r(new Error("")):r()}}],cbiPersonalGuaranteeWithAmount:[{validator:function(e,t,r){t?ve.test(Object(ye["b"])(t))?r():r(new Error("")):r()}}]},ge=r("e681"),Ce={name:"credit-apply-basic-cv",components:{HeaderPiece:i["a"],Application:T,CustomerBasic:K,Other:ne,Upload:he},data:function(){return{rules:Oe}},computed:Object(l["a"])({},Object(O["b"])(["applyForm"])),created:function(){var e=this;ge["a"].$on("cvBasicValidate",function(t){e.$refs["cvBasic"].validate(t)}),ge["a"].$on("cvCustomerSelectChange",function(){console.log(e.applyForm.customerType),"CIA"===e.applyForm.customerType?e.rules=Object.assign({},e.rules,{cbiFinancialStatementsAttId:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,r){0===t?r(new Error("")):r()},message:"",trigger:"blur"}],cbiPaymentCommitmentAttId:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,r){0===t?r(new Error("")):r()},message:"",trigger:"blur"}]}):e.rules=Object.assign({},e.rules,{cbiFinancialStatementsAttId:void 0,cbiPaymentCommitmentAttId:void 0}),e.$nextTick(function(){e.$refs.cvBasic&&e.$refs.cvBasic.clearValidate()})})},destroyed:function(){ge["a"].$off("cvBasicValidate")}},_e=Ce,Ae=Object(A["a"])(_e,a,n,!1,null,null,null);t["a"]=Ae.exports},d119:function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form-item",{staticClass:"is-required",attrs:{label:"Current Credit limit 现有信用额度 : ","label-width":e.labelWidth}},[r("el-input",{attrs:{size:"small",disabled:"",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},n=[],l=r("cebc"),i=r("2f62"),s=r("65e9"),c={name:"credit-apply-cbiCurrentCreditLimit",computed:Object(l["a"])({},Object(i["b"])(["applyForm"]),{value:{get:function(){return Object(s["d"])(this.applyForm.cbiCreditLimitOfYearN1)}},labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"}})},o=c,u=r("2877"),p=Object(u["a"])(o,a,n,!1,null,null,null);t["a"]=p.exports}}]);
//# sourceMappingURL=chunk-767892a5.17336371.js.map