package com.chevron.material.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.Response;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.chevron.material.model.MaterialInventoryDTO;
import com.common.exception.WxPltException;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.sys.organization.model.PartnerView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.chevron.material.dao.WXTMaterialApplicationDetailVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationVoMapper;
import com.chevron.material.dao.WXTMaterialImageVoMapper;
import com.chevron.material.dao.WXTMaterialInventoryLogVoMapper;
import com.chevron.material.dao.WXTMaterialInventoryVoMapper;
import com.chevron.material.dao.WXTMaterialSkuVoMapper;
import com.chevron.material.dao.WXTMaterialVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseVoMapper;
import com.chevron.material.model.ApplicationHistory;
import com.chevron.material.model.ApplicationHistoryDetail;
import com.chevron.material.model.ApplicationHistoryDetailMonthly;
import com.chevron.material.model.MaterialAppHisParams;
import com.chevron.material.model.MaterialParams;
import com.chevron.material.model.MaterialSource;
import com.chevron.material.model.WXTMaterialApplicationDetailVo;
import com.chevron.material.model.WXTMaterialApplicationVo;
import com.chevron.material.model.WXTMaterialInventoryLogVo;
import com.chevron.material.model.WXTMaterialInventoryVo;
import com.chevron.material.model.WXTMaterialInventoryVoExample;
import com.chevron.material.model.WXTMaterialInventoryVoKey;
import com.chevron.material.model.WXTMaterialSkuVo;
import com.chevron.material.model.WXTMaterialSkuVoExample;
import com.chevron.material.model.WXTMaterialVo;
import com.chevron.material.model.WXTMaterialWarehouseVo;
import com.chevron.material.model.WXTMaterialWarehouseVoExample;
import com.chevron.material.service.MaterialService;
import com.chevron.material.service.SmartCommService;
import com.chevron.material.util.DealerPermissUtil;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTUser;
import com.sys.dic.service.DicService;
import com.sys.file.model.WxAttFile;
import com.sys.file.web.FileManager;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.properties.service.WxTPropertiesService;

@Controller
@RequestMapping(value = "/material")
public class MaterialManageController {
	public static final Logger log = LoggerFactory.getLogger(MaterialManageController.class);

	@Autowired
	private WXTMaterialVoMapper wxtMaterialVoMapper;

	@Resource
	private WxTPropertiesService wxTPropertiesServiceImpl;

	@Autowired
	private WXTMaterialInventoryVoMapper wxtMaterialInventoryVoMapper;

	@Autowired
	WXTMaterialInventoryLogVoMapper wxtMaterialInventoryLogVoMapper;

	@Autowired
	WXTMaterialImageVoMapper wxtMaterialImageVoMapper;

	@Autowired
	WXTMaterialApplicationDetailVoMapper applicationDetailVoMapper;

	@Autowired
	WXTMaterialApplicationVoMapper applicationVoMapper;

	@Autowired
	SmartCommService smartCommService;

	@Autowired
	MaterialService materialService;

	@Autowired
	FileManager fileManager;

	@Autowired
	WXTMaterialSkuVoMapper wxtMaterialSkuVoMapper;

	@Autowired
	private WXTMaterialApplicationVoMapper wxtMaterialApplicationVoMapper;

	@Autowired
	private WXTMaterialApplicationDetailVoMapper wxtMaterialApplicationDetailVoMapper;

	@Autowired
	private WXTMaterialWarehouseVoMapper wxtMaterialWarehouseVoMapper;

	@Resource
	OrganizationVoMapper organizationVoMapper;

	@Autowired
	private DicService dicService;

	@ResponseBody
	@RequestMapping(value = "/uploadImage.do", method = RequestMethod.POST)
	public Map<String, Object> uploadMaterialImage(@RequestParam("materialImage") MultipartFile materialImage, HttpServletRequest request) throws Exception {

		MultipartFile[] myfiles = new MultipartFile[] { materialImage };
		Map<String, Object> resMap = fileManager.handleUploadRequest("12", myfiles, request);
		if(null != resMap && resMap.containsKey("code") && "success".equals(resMap.get("code"))) {
			if (resMap.containsKey("attachmentFileList") && null != resMap.get("attachmentFileList")) {
				List<WxAttFile> attachmentFileList = (List<WxAttFile>)resMap.get("attachmentFileList");
				resMap.put("url", "/downloadAttachmentFile.do?sourceType=12&attId=" + attachmentFileList.get(0).getAttId() );
				resMap.put("name", attachmentFileList.get(0).getFileName());
			}

		}
		return resMap;

	}

	@ResponseBody
	@RequestMapping(value = "/getSkuList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getSkuList(@RequestParam("materialId") Long materialId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> data = new HashMap<String, Object>();
		if (null == materialId) {
			resultMap.put("code", "error");
			resultMap.put("msg", "获取SKU列表失败, 物料ID为空.");
			log.error("获取SKU列表失败, 物料ID为空.");
			return resultMap;
		}
		WXTMaterialSkuVoExample example = new WXTMaterialSkuVoExample();
		example.createCriteria().andMaterialIdEqualTo(materialId).andDeleteFlagEqualTo(false);
		List<WXTMaterialSkuVo> skuList = wxtMaterialSkuVoMapper.selectByExample(example);

		data.put("skuList", skuList);
		resultMap.put("code", "success");
		resultMap.put("msg", "成功");
		resultMap.put("data", data);
		return resultMap;
	}
	/**
	 * 稽核礼享网物料库存
	 * @param params
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/auditStock.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> auditStock(@RequestParam("materialId") Long materialId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<Map<String, Object>> resultList = new ArrayList<Map<String,Object>>();
		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		if (null == materialId) {
			resultMap.put("code", "error");
			resultMap.put("msg", "稽核失败, 物料ID为空.");
			log.error("稽核库存失败, 物料ID为空.");
			return resultMap;
		}

		WXTMaterialVo materialVo = wxtMaterialVoMapper.selectByPrimaryKey(materialId);
		if (StringUtils.isEmpty(materialVo.getSmcMaterialCode())) {
			resultMap.put("code", "error");
			resultMap.put("msg", "稽核库存失败, 该物料未同步至礼享网, 请重新同步.");
			log.error("稽核库存失败, 该物料未同步至礼享网.");
			return resultMap;
		}

		if (!MaterialSource.isFromEnjoygifts(materialVo.getMaterialSource())) {
			resultMap.put("code", "error");
			resultMap.put("msg", "稽核库存失败, 该物料非礼享网物料，不能稽核.");
			log.error("稽核库存失败, 该物料非礼享网物料，不能稽核.");
			return resultMap;
		}

		WXTMaterialSkuVoExample skuExample = new WXTMaterialSkuVoExample();
		skuExample.createCriteria().andMaterialIdEqualTo(materialId);
		List<WXTMaterialSkuVo> skuList = wxtMaterialSkuVoMapper.selectByExample(skuExample);
		if (null == skuList || skuList.isEmpty()) {
			resultMap.put("code", "error");
			resultMap.put("msg", "稽核库存异常, 该物料没有SKU, 请配置SKU.");
			log.error("稽核库存异常, 该物料没有SKU.");
			return resultMap;
		}
		WXTMaterialWarehouseVoExample we = new WXTMaterialWarehouseVoExample();
		we.createCriteria().andMaterialSourceEqualTo(MaterialSource.ENJOYGIFTS.getValue());
		List<WXTMaterialWarehouseVo> warehouse = wxtMaterialWarehouseVoMapper.selectByExample(we);
		if (null == warehouse || warehouse.isEmpty()) {
			resultMap.put("code", "error");
			resultMap.put("msg", "稽核库存异常, 未配置礼享网仓库.");
			log.error("稽核库存异常, 未配置礼享网仓库.");
			return resultMap;
		}

		if (warehouse.size() > 1) {
			resultMap.put("code", "error");
			resultMap.put("msg", "稽核库存异常, 礼享网仓库配置重复.");
			log.error("稽核库存异常, 礼享网仓库配置重复.");
			return resultMap;
		}

		Long warehouseId = warehouse.get(0).getId();

		// 稽核每个SKU的库存
		boolean areAllOk = true;
		Long stockQtyTotal= 0L;
		Long virtualStockQtyTotal = 0L;
		Long pendingQtyTotal = 0L;
		Integer smcInventoryTotal = 0;
		for (WXTMaterialSkuVo sku : skuList) {
			Map<String, Object> data = new HashMap<String, Object>();
			Integer smcInventory = smartCommService.getInventory(materialVo.getSmcMaterialCode(), sku.getMaterialSkuCode());
			if (null == smcInventory) {
				resultMap.put("code", "error");
				resultMap.put("msg", "稽核库存异常, 未从礼享网查询到正常库存数据.");
				log.error("稽核库存异常, 未从礼享网查询到正常库存数据.");
				return resultMap;
			}
			smcInventoryTotal = smcInventoryTotal + smcInventory;
			data.put("smcInventory", smcInventory);

			Long stockQty= 0L;
			Long virtualStockQty = 0L;

			WXTMaterialInventoryVoKey inventoryKey = new WXTMaterialInventoryVoKey();
			inventoryKey.setMaterialId(materialId);
			inventoryKey.setMaterialSkuCode(sku.getMaterialSkuCode());
			inventoryKey.setWarehouseId(warehouseId);
			WXTMaterialInventoryVo inventoryVo = wxtMaterialInventoryVoMapper.selectByPrimaryKey(inventoryKey);
			Long pendingQty = applicationDetailVoMapper.countPendingQty4SKU(materialId, sku.getMaterialSkuCode());
			pendingQty = pendingQty==null?0L:pendingQty;
			if (null == inventoryVo || null == inventoryVo.getStockQty() || null == inventoryVo.getVirtualStockQty()) {
				log.error("稽核库存异常, 数据库查询不到该物料库存数据");
				resultMap.put("msg", "稽核库存异常, 数据库查询不到该物料库存数据");
			} else {
				stockQty = inventoryVo.getStockQty() == null ? 0L : inventoryVo.getStockQty();
				virtualStockQty = inventoryVo.getVirtualStockQty() == null ? 0L : inventoryVo.getVirtualStockQty();
			}

			stockQtyTotal = stockQtyTotal + stockQty;
			virtualStockQtyTotal = virtualStockQtyTotal + virtualStockQty;
			pendingQtyTotal = pendingQtyTotal + pendingQty;
			data.put("stockQty", stockQty);
			data.put("virtualStockQty", virtualStockQty);
			data.put("pendingQty", pendingQty);
			data.put("materialSkuCode", sku.getMaterialSkuCode());
			boolean isOk = stockQty.equals(smcInventory.longValue()) && (pendingQty + virtualStockQty == stockQty);
			data.put("isOk", isOk);
			if (!isOk) {
				areAllOk = false;
				WXTMaterialInventoryVo inventoryRecord = new WXTMaterialInventoryVo();
				inventoryRecord.setStockQty(smcInventory.longValue());
				inventoryRecord.setVirtualStockQty(smcInventory.longValue() - pendingQty);
				inventoryRecord.setMaterialId(materialId);
				inventoryRecord.setMaterialSkuCode(sku.getMaterialSkuCode());
				inventoryRecord.setWarehouseId(warehouseId);
				inventoryRecord.setLastUpdatedBy(curUserId);
				inventoryRecord.setLastUpdateTime(now);
				int inventoryRes = wxtMaterialInventoryVoMapper.updateByPrimaryKeySelective(inventoryRecord);
				if (1 == inventoryRes) {
					WXTMaterialInventoryLogVo inventoryLogVo = new WXTMaterialInventoryLogVo();
					inventoryLogVo.setChangeQty(smcInventory.longValue() - stockQty);
					inventoryLogVo.setMaterialId(materialId);
					inventoryLogVo.setMaterialSkuCode(sku.getMaterialSkuCode());
					inventoryLogVo.setWarehouseId(warehouseId);
					inventoryLogVo.setStockType("REAL");
					inventoryLogVo.setDeleteFlag(false);
					inventoryLogVo.setLastUpdatedBy(curUserId);
					inventoryLogVo.setLastUpdateTime(now);
					inventoryLogVo.setCreatedBy(curUserId);
					inventoryLogVo.setCreationTime(now);
					inventoryLogVo.setAttribute1("前台库存稽核");
					inventoryLogVo.setAttribute2("" + stockQty + "->" + smcInventory + "");
					wxtMaterialInventoryLogVoMapper.insertSelective(inventoryLogVo);
					WXTMaterialInventoryLogVo inventoryLogVo2 = new WXTMaterialInventoryLogVo();
					inventoryLogVo2.setChangeQty(smcInventory.longValue() - pendingQty - virtualStockQty);
					inventoryLogVo2.setMaterialId(materialId);
					inventoryLogVo2.setMaterialSkuCode(sku.getMaterialSkuCode());
					inventoryLogVo2.setWarehouseId(warehouseId);
					inventoryLogVo2.setStockType("VIRTUAL");
					inventoryLogVo2.setDeleteFlag(false);
					inventoryLogVo2.setLastUpdatedBy(curUserId);
					inventoryLogVo2.setLastUpdateTime(now);
					inventoryLogVo2.setCreatedBy(curUserId);
					inventoryLogVo2.setCreationTime(now);
					inventoryLogVo2.setAttribute1("前台库存稽核");
					inventoryLogVo2.setAttribute2("" + virtualStockQty + "->" + (smcInventory.longValue() - pendingQty) + "");
					wxtMaterialInventoryLogVoMapper.insertSelective(inventoryLogVo2);
				}
			}
			resultList.add(data);
		}
		resultMap.put("code", "success");
		resultMap.put("msg", "成功");
		resultMap.put("data", resultList);
		resultMap.put("stockQtyTotal", stockQtyTotal);
		resultMap.put("virtualStockQtyTotal", virtualStockQtyTotal);
		resultMap.put("pendingQtyTotal", pendingQtyTotal);
		resultMap.put("smcInventoryTotal", smcInventoryTotal);

		resultMap.put("isOk", areAllOk);
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/enable.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> enable(@RequestParam("materialId") Long materialId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		if (null == materialId) {
			resultMap.put("code", "error");
			resultMap.put("msg", "开放物料申请失败, 物料ID为空.");
			log.error("开放物料失败, 物料ID为空.");
			return resultMap;
		}

		WXTMaterialVo record = new WXTMaterialVo();
		record.setId(materialId);
		record.setLastUpdatedBy(curUserId);
		record.setAttribute1("ENABLE");
		record.setLastUpdateTime(now);
		int res = wxtMaterialVoMapper.updateByPrimaryKeySelective(record);

		if (0 == res) {
			resultMap.put("code", "error");
			resultMap.put("msg", "开放物料申请失败, 该物料不存在.");
			log.error("开放物料失败, 查询不到该物料, ID: {}", materialId);
			return resultMap;
		}

		if (0 < res) {
			resultMap.put("code", "success");
			resultMap.put("msg", "开放物料申请成功.");
		}

		if (0 > res) {
			resultMap.put("code", "success");
			resultMap.put("msg", "开放物料申请失败.");
			log.error("开放物料申请失败, 异常发生, ID: {}", materialId);
		}

		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/updateStatus.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> updateStatus(@RequestParam("materialId") Long materialId, @RequestParam("status") Integer status) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		if (null == materialId) {
			resultMap.put("code", "error");
			resultMap.put("msg", "更新物料状态失败, 物料ID为空.");
			log.error("更新物料状态失败, 物料ID为空.");
			return resultMap;
		}

		WXTMaterialVo record = new WXTMaterialVo();
		record.setId(materialId);
		record.setLastUpdatedBy(curUserId);
		record.setSmcStatus(status);
		record.setLastUpdateTime(now);
		int res = wxtMaterialVoMapper.updateByPrimaryKeySelective(record);

		if (0 == res) {
			resultMap.put("code", "error");
			resultMap.put("msg", "更新物料状态失败, 该物料不存在.");
			log.error("更新物料状态失败, 查询不到该物料, ID: {}", materialId);
			return resultMap;
		}

		if (0 < res) {
			resultMap.put("code", "success");
			resultMap.put("msg", "更新物料状态成功.");
		}

		if (0 > res) {
			resultMap.put("code", "success");
			resultMap.put("msg", "更新物料状态失败.");
			log.error("更新物料状态失败, 异常发生, ID: {}", materialId);
		}

		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/disable.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> disable(@RequestParam("materialId") Long materialId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		if (null == materialId) {
			resultMap.put("code", "error");
			resultMap.put("msg", "关闭物料申请失败, 物料ID为空.");
			log.error("关闭物料申请失败, 物料ID为空.");
			return resultMap;
		}

		WXTMaterialVo record = new WXTMaterialVo();
		record.setId(materialId);
		record.setLastUpdatedBy(curUserId);
		record.setAttribute1("DISABLE");
		record.setLastUpdateTime(now);
		int res = wxtMaterialVoMapper.updateByPrimaryKeySelective(record);

		if (0 == res) {
			resultMap.put("code", "error");
			resultMap.put("msg", "关闭物料申请失败, 该物料不存在.");
			log.error("关闭物料申请失败, 查询不到该物料, ID: {}", materialId);
			return resultMap;
		}

		if (0 < res) {
			resultMap.put("code", "success");
			resultMap.put("msg", "关闭物料申请成功.");
		}

		if (0 > res) {
			resultMap.put("code", "success");
			resultMap.put("msg", "关闭物料申请失败.");
			log.error("关闭物料申请失败, 异常发生, ID: {}", materialId);
		}

		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/delete.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> delete(@RequestParam("materialId") Long materialId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		if (null == materialId) {
			resultMap.put("code", "error");
			resultMap.put("msg", "删除失败, 物料ID为空.");
			log.error("删除物料失败, 物料ID为空.");
			return resultMap;
		}

		WXTMaterialVo materialVo = wxtMaterialVoMapper.selectByPrimaryKey(materialId);

		if (null == materialVo) {
			resultMap.put("code", "error");
			resultMap.put("msg", "删除失败, 该物料不存在.");
			log.error("删除物料失败, 查询不到该物料, ID: {}", materialId);
			return resultMap;
		}

		// 判断是否有库存
		WXTMaterialInventoryVoExample inventoryEx = new WXTMaterialInventoryVoExample();
		inventoryEx.createCriteria()
		.andMaterialIdEqualTo(materialId)
		.andStockQtyNotEqualTo(0L)
		.andVirtualStockQtyNotEqualTo(0L);
		List<WXTMaterialInventoryVo> inventory = wxtMaterialInventoryVoMapper.selectByExample(inventoryEx);

		if(null != inventory && !inventory.isEmpty()) {
			resultMap.put("code", "error");
			resultMap.put("msg", "删除失败, 该物料仍有库存, 不能删除.");
			log.error("删除物料失败, 该物料仍有库存, 不能删除, ID: {}", materialId);
			return resultMap;
		}

		// 请求SMC删除物料
		Boolean isSMCSusscess = true;
		if (!StringUtils.isEmpty(materialVo.getSmcMaterialCode())
				&& MaterialSource.isFromEnjoygifts(materialVo.getMaterialSource())) {
			isSMCSusscess = smartCommService.deleteItem(materialVo.getSmcMaterialCode());
		}

		// 逻辑删除本地物料
		if (isSMCSusscess) {
			WXTMaterialVo record = new WXTMaterialVo();
			record.setId(materialId);
			record.setDeleteFlag(true);
			record.setLastUpdatedBy(curUserId);
			record.setLastUpdateTime(now);
			int res = wxtMaterialVoMapper.updateByPrimaryKeySelective(record);
			if (res == 1) {
				resultMap.put("code", "success");
				resultMap.put("msg", "删除成功.");
			} else {
				resultMap.put("code", "error");
				resultMap.put("msg", "删除失败, 系统异常.");
			}
		} else {
			resultMap.put("code", "error");
			resultMap.put("msg", "删除失败, 礼享网该物料仍有库存, 请同步库存后再试.");
		}
		return resultMap;
	}

	/**
	 * 获取物料信息列表, 分页
	 * @param params
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getlist(MaterialParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
        WxTUser curUser = ContextUtil.getCurUser();
        if(1==curUser.getUserId()){
            params.setChevronAdmin(true);
        }
		//获取用户的角色和积分操作权限
		int permiss=DealerPermissUtil.getCurUserDealerPermissCode(dicService);
		resultMap.put("pointPermission", permiss);
        List<WXTMaterialVo> wxtMaterialVos = wxtMaterialVoMapper.selectMaterial4Pagination(params);
        //查询不可见的经销商基本信息
        HashSet<Long> partnerIds = new HashSet<Long>();
        for (WXTMaterialVo wxtMaterialVo : wxtMaterialVos) {
            if(wxtMaterialVo.getMaterialType().equals(Constants.VIRTUAL_MATERIAL_TYPE)){
                wxtMaterialVo.setVirtualGiftCode(wxtMaterialVo.getAttribute5());
                wxtMaterialVo.setVirtualGiftBusiness(wxtMaterialVo.getAttribute2());
                wxtMaterialVo.setVirtualGiftSupplier(wxtMaterialVo.getAttribute3());
                wxtMaterialVo.setActualAmount(wxtMaterialVo.getAttribute4()==null?null:new Double(wxtMaterialVo.getAttribute4()));
            }
            ArrayList<PartnerView> partnerViews = new ArrayList<PartnerView>();
            if(StringUtils.isNotBlank(wxtMaterialVo.getInvisibleDealer())){
                for (String id : wxtMaterialVo.getInvisibleDealer().split("\\|")) {
                    if(StringUtils.isBlank(id)){
                        continue;
                    }
                    PartnerView partnerView = new PartnerView();
                    partnerView.setId(Long.valueOf(id));
                    partnerIds.add(Long.valueOf(id));
                    partnerViews.add(partnerView);
                }
            }
            wxtMaterialVo.setVisiblePartners(partnerViews);
        }
        if(CollectionUtil.isNotEmpty(partnerIds)){
            List<PartnerView> partnerViewByIds = organizationVoMapper.getPartnerViewByIds(new ArrayList<Long>(partnerIds));
            for (WXTMaterialVo wxtMaterialVo : wxtMaterialVos) {
                for (PartnerView visiblePartner : wxtMaterialVo.getVisiblePartners()) {
                    for (PartnerView partnerView : partnerViewByIds) {
                        if (visiblePartner.getId().equals(partnerView.getId())) {
                            visiblePartner.setPartnerName(partnerView.getPartnerName());
                            break;
                        }
                    }
                }
            }
        }
        resultMap.put("rows",wxtMaterialVos);
		resultMap.put("total", params.getTotalCount());
		resultMap.put("code", "success");
//		resultMap.put("pointPermission", permissMap);
		resultMap.put("errorMsg", "成功");
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value="/getNotEnoughInventoryList.do",method= {RequestMethod.POST,RequestMethod.GET})
	public Map<String,Object> getNotEnoughInventoryList(@RequestParam("pointType")String pointType){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		//获取仓库信息
		List<WxTRole> roleList=ContextUtil.getCurUser().getRoleList();
		String warehouse=null;
		for(WxTRole role:roleList) {
			if(role.getChRoleName().equals("Caltex_Warehouse_Admin_TJ")) {
				warehouse="caltexWarehouseTJ";
			} else if(role.getChRoleName().equals("Caltex_Warehouse_Admin_GZ")) {
				warehouse="caltexWarehouseGZ";
			}
		}
		resultMap.put("rows", wxtMaterialVoMapper.selectNotEnoughMaterialInventory(10,true,warehouse,pointType));
//		resultMap.put("total", params.getTotalCount());
		resultMap.put("code", "success");
		resultMap.put("errorMsg", "成功");
		return resultMap;
	}

    /**
     * 获取可供挑选的物料列表, 分页
     * 雪佛龙金富力和特劲的SP用户：申请上限=真实库存*进货升数的百分比，若合伙人分区域单独申请，则平均到申请上限到各个区域
     * 加德士和德勒的经销商用户：使用积分兑换物料
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/goodsList.do", method = { RequestMethod.POST,  RequestMethod.GET })
    public Map<String, Object> goodsList(MaterialParams params) {
        return materialService.goodsList(params);
    }

    /**
     *
     */
    @ResponseBody
    @RequestMapping(value = "/materialCtrl.do", method = { RequestMethod.POST,  RequestMethod.GET })
    public ResponseMap materialCtrl(MaterialParams params) {
        return materialService.materialCtrl(params);
    }

    /**
     * 获取促销礼品兑换商品列表
     */
    @ResponseBody
    @RequestMapping(value = "/promotionGiftList.do", method = { RequestMethod.POST,  RequestMethod.GET })
    public Map<String, Object> promotionGiftList(MaterialParams params) {
        return materialService.promotionGiftList(params);
    }

    /**
     * 获取促销礼品兑换商品列表
     */
    @ResponseBody
    @RequestMapping(value = "/couponGiftList.do", method = { RequestMethod.POST,  RequestMethod.GET })
    public ResponseMap couponGiftList(MaterialParams params) {
        try {
            return materialService.couponGiftList(params);
        } catch (Exception e) {
            e.printStackTrace();
            ResponseMap responseMap = new ResponseMap();
            responseMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            responseMap.setErrorMsg("系统错误，查询失败。");
            return responseMap;
        }
    }

	/**
	 * 获取可供挑选的物料列表, 分页
	 * 雪佛龙金富力和特劲的SP用户：申请上限=真实库存*进货升数的百分比，若合伙人分区域单独申请，则平均到申请上限到各个区域
	 * 加德士和德勒的经销商用户：使用积分兑换物料
	 * @param params
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getPickingList.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getPickingList(MaterialParams params) {
		return materialService.getPickingList(params);
	}

	private boolean isPointOrder( Long applicationId) {
		WXTMaterialApplicationVo app = wxtMaterialApplicationVoMapper.selectByPrimaryKey(applicationId);
		 String applicationType = null;
		 if(app!=null){
			 applicationType = app.getApplicationType();
		 }
		return "caltex".equals(applicationType) || "promotion".equals(applicationType)
				|| "cdm_stock".equals(applicationType) || "cdm_material".equals(applicationType);
	}

	/**
	 * 获取申请单行，暂存和驳回时， 申请者编辑重新挑选物料，根据合伙人订单量重新计算了库存上限
	 * @param params
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getApplicationline4Edit.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> getApplicationline4Edit(@RequestParam("applicationId") Long applicationId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<WXTMaterialApplicationDetailVo> list = null;
		try {
			list = wxtMaterialApplicationDetailVoMapper.selectWithMaterialInfo(applicationId);
		} catch (Exception e) {
			resultMap.put("applicationLines", null);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请行详情获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			return resultMap;
		}

		if (!isPointOrder(applicationId)) {
			// 非积分订单，计算申请上限
			setFakeVirtualStock4AppLine(list);
		}
		resultMap.put("applicationLines", list);
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "申请行详情获取成功");
		return resultMap;
	}

	private List<WXTMaterialApplicationDetailVo> setFakeVirtualStock4AppLine(List<WXTMaterialApplicationDetailVo> list) {
		if (list == null || list.isEmpty()) {
			return list;
		}

		WxTUser user = ContextUtil.getCurUser();
		Long partnerId = user.getOrgId();
		java.math.BigDecimal scale = getVirtualStockScale(partnerId);

		for (WXTMaterialApplicationDetailVo vo : list) {
			String virtualStockQty = vo.getVirtualStockQty();
			Double vStock = Double.valueOf(virtualStockQty);
			if (!StringUtils.isNumeric(virtualStockQty)) {
				continue;
			}

			BigDecimal fakeStock = new BigDecimal(0);
			fakeStock = scale.multiply(new BigDecimal(vStock)).divide(new BigDecimal(100D));
			vo.setVirtualStockQty(String.valueOf(fakeStock.longValue()));
		}
		return list;
	}

	private List<WXTMaterialVo> setFakeVirtualStock(List<WXTMaterialVo> list) {

		if (list == null || list.isEmpty()) {
			return list;
		}

		WxTUser user = ContextUtil.getCurUser();
		Long partnerId = user.getOrgId();
		java.math.BigDecimal scale = getVirtualStockScale(partnerId);

		for (WXTMaterialVo vo : list) {
			String virtualStockQty = vo.getVirtualStockQty();
			Double vStock = Double.valueOf(virtualStockQty);
			if (!StringUtils.isNumeric(virtualStockQty)) {
				continue;
			}

			BigDecimal fakeStock = new BigDecimal(0);
			fakeStock = scale.multiply(new BigDecimal(vStock)).divide(new BigDecimal(100D));
			vo.setVirtualStockQty(String.valueOf(fakeStock.longValue()));
		}
		return list;
	}

	private BigDecimal getVirtualStockScale (Long partnerId) {

		// SP分区域申请
		boolean isRegionDevided = false;
		Long regionNumber = wxtMaterialApplicationVoMapper.countSPRegionNumber(partnerId);
		if (null != regionNumber && regionNumber.intValue() > 0) {
			isRegionDevided = true;
		}

		Map<String, Object> resMap = applicationVoMapper.selectSellInSacle(partnerId);
		if(null == resMap || !resMap.containsKey("isPartner")) {
			return new BigDecimal(100D);
		}
		Integer isPartner = (Integer)resMap.get("isPartner");
		if (isPartner ==null || isPartner < 1) {
			return new BigDecimal(100D);
		}
		java.math.BigDecimal scale = (java.math.BigDecimal)resMap.get("scale");
		Integer diffMonth =  (Integer)resMap.get("diffMonth");
		Integer diffDay =  (Integer)resMap.get("diffDay");
		java.math.BigDecimal total =  (java.math.BigDecimal)resMap.get("total");

		if (diffMonth < 1 || (diffMonth == 1 && diffDay< 1)) {
			scale = new BigDecimal(30);
		} else {
			if (null == scale || scale.equals(new BigDecimal(0)) || null == total || total.compareTo(BigDecimal.ZERO) == 0) {
				scale = new BigDecimal(10);
			} else {
				scale = scale.multiply(new BigDecimal(0.85d));
			}
		}
		if (scale.compareTo(new BigDecimal(10)) < 0){
			scale = new BigDecimal(10);
		}

		if (scale.compareTo(new BigDecimal(60)) > 0){
			scale = new BigDecimal(60);
		}

		// SP分区域申请， 比例均分
		if (isRegionDevided) {
			scale =scale.divide(new BigDecimal(regionNumber), 2, RoundingMode.HALF_UP);
		}

		return scale;
	}

	/**
	 * 查看某个合伙人的物料申请历史(针对同一个合伙人,不同的多种物料)
	 * @param params
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/getHistory.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getSPApplicationHistory(MaterialAppHisParams params) throws Exception {

		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<ApplicationHistory> appHisList = null;
		WxTUser curUser = ContextUtil.getCurUser();
		if (isPartnerUser(curUser)) {
			params.setPartnerId(curUser.getOrgId());
		}
		try{
			appHisList = wxtMaterialVoMapper.selectApplicationHistory(params);
		} catch(Exception e) {
			resultMap.put("rows", null);
			resultMap.put("total", 0L);
			resultMap.put("code", "error");
			resultMap.put("errorMsg", "物料申请历史获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}

		resultMap.put("rows", appHisList);
		resultMap.put("total", params.getTotalCount());
		resultMap.put("code", "success");
		resultMap.put("errorMsg", "物料申请历史获取成功");
		return resultMap;

	}

	/**
	 * 查看物料的申请历史(针对同一个物料,不同的合伙人)
	 * @param materialId
	 * @param partnerId
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/getHistoryDetail.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getSPApplicationHistoryDetail(MaterialAppHisParams params) throws Exception {

		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<ApplicationHistoryDetail> appHisDetailList = null;
		WxTUser curUser = ContextUtil.getCurUser();
		if (isPartnerUser(curUser)) {
			params.setPartnerId(curUser.getOrgId());
		} else {
			params.setPartnerId(null);
		}

		try{
			appHisDetailList = wxtMaterialVoMapper.selectApplicationHistoryDetail(params);
		} catch(Exception e) {
			resultMap.put("rows", null);
			resultMap.put("total", 0L);
			resultMap.put("code", "error");
			resultMap.put("errorMsg", "物料申请历史获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}

		resultMap.put("rows", appHisDetailList);
		resultMap.put("total", params.getTotalCount());
		resultMap.put("code", "success");
		resultMap.put("errorMsg", "物料申请历史获取成功");
		return resultMap;

	}

	/**
	 * 获取物料申请历史, 按月
	 * @param materialId
	 * @param partnerId
	 * @param year
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/getHistoryDetailMonthly.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getSPApplicationHistoryDetailMonthly(
			@RequestParam(value = "materialId", required = false)  Long materialId,
			@RequestParam(value = "partnerId", required = false) Long partnerId,
			@RequestParam(value = "year", required = false)  Long year,
			@RequestParam(value = "type", required = false)  String type,
			@RequestParam(value = "materialSkuCode", required = false)  String materialSkuCode) throws Exception {

		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (null == materialId || null == year) {
			resultMap.put("rows", null);
			resultMap.put("total", 0L);
			resultMap.put("code", "error");
			resultMap.put("errorMsg", "物料申请历史获取失败, 参数不足: materialId:" + materialId + ", year:" + year);
			return resultMap;
		}

		if (StringUtils.isEmpty(type)) {
			type = "RECEIVED";
		}

		List<ApplicationHistoryDetailMonthly> appHisDetailList = null;
		WxTUser curUser = ContextUtil.getCurUser();
		if (isPartnerUser(curUser)) {
			partnerId = curUser.getOrgId();
		} else {
			partnerId = null;
		}

		try {
			appHisDetailList = wxtMaterialVoMapper.selectApplicationHistoryDetailMonthly(materialId, partnerId, year, type, materialSkuCode);
		} catch (Exception e) {
			resultMap.put("rows", null);
			resultMap.put("total", 0L);
			resultMap.put("code", "error");
			resultMap.put("errorMsg", "物料申请历史获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}

		List<Map<String, Object>> resList = new ArrayList<Map<String,Object>>();
		Map<Long, List<ApplicationHistoryDetailMonthly>> filter = new HashMap<Long, List<ApplicationHistoryDetailMonthly>>();
		if(null != appHisDetailList && !appHisDetailList.isEmpty()) {
			for (ApplicationHistoryDetailMonthly m : appHisDetailList) {
				Long orgId = m.getApplicationOrgId();
				List<ApplicationHistoryDetailMonthly> voList = null;
				if (filter.containsKey(orgId)) {
					voList = filter.get(orgId);
				} else {
					voList = new ArrayList<ApplicationHistoryDetailMonthly>();
					filter.put(orgId, voList);
				}
				voList.add(m);
			}

			for (Long key : filter.keySet()) {
				List<ApplicationHistoryDetailMonthly> list = filter.get(key);
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("applicationOrgId", key);
				for (ApplicationHistoryDetailMonthly m : list) {
					map.put("applicationOrgName", m.getApplicationOrgName());
					map.put(m.getApplicationMonth().substring(5, 7), m.getApplicationQty());
				}
				resList.add(map);
			}
		}

		resultMap.put("data", resList);
		resultMap.put("success", true);
		resultMap.put("errorMsg", "物料申请历史获取成功");
		return resultMap;
	}

	/**
	 * 统计已申请物料的申请年份信息
	 * @param materialId
	 * @param partnerId
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/getHistoryDetailYears.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getSPApplicationHistoryDetailYears(
			@RequestParam(value = "materialId", required = false) Long materialId,
			@RequestParam(value = "partnerId", required = false) Long partnerId,
			@RequestParam(value = "type", required = false) String type,
			@RequestParam(value = "materialSkuCode", required = false)  String materialSkuCode) throws Exception {

		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<Long> years = null;
		WxTUser curUser = ContextUtil.getCurUser();
		if (isPartnerUser(curUser)) {
			partnerId = curUser.getOrgId();
		} else {
			partnerId = null;
		}
		if (StringUtils.isEmpty(type)) {
			type = "RECEIVED";
		}
		try {
			years = wxtMaterialVoMapper.selectApplicationHistoryDetailYear(materialId, partnerId, type, materialSkuCode);
		} catch (Exception e) {
			resultMap.put("years", null);
			resultMap.put("success", false);
			resultMap.put("errorMsg", "物料申请历史年份列表获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}
		resultMap.put("data", years);
		resultMap.put("success", true);
		resultMap.put("errorMsg", "物料申请历史年份列表获取成功");

		return resultMap;
	}

	/**
	 * 获取SKU库存信息
	 * @param materialId
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/getSkuInventory.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getSkuInventory(
			@RequestParam(value = "materialId", required = false) Long materialId) throws Exception {

		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<Map<String, Object>> list = null;
		try {
			list = wxtMaterialInventoryVoMapper.selectSkuInventory4Edit(materialId);
		} catch (Exception e) {
			resultMap.put("data", null);
			resultMap.put("success", false);
			resultMap.put("msg", "SKU库存信息获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}
		resultMap.put("data", list);
		resultMap.put("success", true);
		resultMap.put("msg", "SKU库存信息获取成功");

		return resultMap;
	}

	/**
	 * 添加库存
	 * @param materialId
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/addSkuInventory.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> addSkuInventory(
			@RequestParam(value = "materialCode", required = true) String materialCode,
			@RequestParam(value = "materialSkuCode", required = true) String materialSkuCode,
			@RequestParam(value = "stockQty", required = true) Long stockQty,
			@RequestParam(value = "changeQty", required = true) Long changeQty,
			@RequestParam(value = "warehouseId", required = true) Long warehouseId) throws Exception {

		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// 如果changeQty是负数，则调用removeSkuInventory
			if(changeQty <0){
				return removeSkuInventory(materialCode,materialSkuCode,stockQty,changeQty,warehouseId);
			}
//			SMCUpdateInventoryDto dto = new SMCUpdateInventoryDto();
//			dto.setUpdateType("2");
//			dto.setUpdateTime(now);
//			dto.setComments("管理员手动入库");
//			SMCUpdateInventoryDetailDto detail = new SMCUpdateInventoryDetailDto();
//			detail.setChangeQty(changeQty);
//			detail.setMaterialCode(materialCode);
//			detail.setMaterialSkuCode(materialSkuCode);
//			detail.setOriginalQty(stockQty);
//			detail.setStockQty(stockQty+changeQty);
//			detail.setWarehouseId(warehouseId);
//
//			dto.setDetailList(new SMCUpdateInventoryDetailDto[] {detail});

//			Map<String, Object> updateInventory = materialService.updateInventory(dto );
			Map<String, Object> updateInventory = materialService.updateInventoryInternal(materialCode, materialSkuCode, warehouseId, changeQty, stockQty);
			resultMap.put("success", Constants.SUCCESS.equals(updateInventory.get("code")));
			resultMap.put("msg", updateInventory.get("codeMsg"));
		} catch (Exception e) {
			resultMap.put("success", false);
			resultMap.put("msg", "后台异常， 请联系IT支持");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}

		return resultMap;
	}

	/**
	 *
	 * @param materialCode 这个material的code
	 * @param materialSkuCode 这个material的skuCode
	 * @param stockQty 当前的数量，或者叫originalQty
	 * @param changeQty 要remove掉的数量
	 * @param warehouseId 对应的仓库id
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/removeSkuInventory.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> removeSkuInventory(
			@RequestParam(value = "materialCode", required = true) String materialCode,
			@RequestParam(value = "materialSkuCode", required = true) String materialSkuCode,
			@RequestParam(value = "stockQty", required = true) Long stockQty,
			@RequestParam(value = "changeQty", required = true) Long changeQty,
			@RequestParam(value = "warehouseId", required = true) Long warehouseId) throws Exception {

		Long curUserId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// remove库存之前先判断是否“扣减的库存大于剩余的库存”
			WXTMaterialInventoryVoExample queryMaterialInfoExample = new WXTMaterialInventoryVoExample();
			queryMaterialInfoExample.createCriteria()
					.andMaterialSkuCodeEqualTo(materialSkuCode)
					.andWarehouseIdEqualTo(warehouseId);

			List<WXTMaterialInventoryVo> materialInventoryVos = wxtMaterialInventoryVoMapper.selectByExample(queryMaterialInfoExample);
			WXTMaterialInventoryVo curMaterialInventory = null;
            if(materialInventoryVos!=null && materialInventoryVos.size() > 0){
				curMaterialInventory = materialInventoryVos.get(0);
			}
			// 查询不到对应的库存信息直接报错
            if(curMaterialInventory == null){
            	throw new Exception("查询不到对应的库存信息:"+materialSkuCode);
			}

			if(curMaterialInventory.getVirtualStockQty() < Math.abs(changeQty) || curMaterialInventory.getStockQty() < Math.abs(changeQty)){
				throw new Exception("扣减的量大于剩余的库存");
			}
			Map<String, Object> updateInventory = materialService.updateInventoryInternal(materialCode, materialSkuCode, warehouseId, changeQty, stockQty);
			resultMap.put("success", Constants.SUCCESS.equals(updateInventory.get("code")));
			resultMap.put("msg", updateInventory.get("codeMsg"));
		} catch (Exception e) {
			resultMap.put("success", false);
			resultMap.put("msg", e.getLocalizedMessage());
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}

		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value = "/getMaterialInfo.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> getMaterialInfo(@RequestParam(value = "materialId", required = false) Long materialId) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WXTMaterialVo material = null;
		try {
			material = wxtMaterialVoMapper.selectByPrimaryKey(materialId);
		} catch (Exception e) {
			resultMap.put("data", null);
			resultMap.put("success", false);
			resultMap.put("msg", "物料信息获取失败");
			resultMap.put(Constants.RESULT_ERROR, "程序异常:" + e.getMessage());
			log.error("程序异常:" , e.getMessage(), e);
			return resultMap;
		}
		resultMap.put("data", material);
		resultMap.put("success", true);
		resultMap.put("msg", "物料信息获取成功");

		return resultMap;
	}

	/**
	 * 是否为合伙人用户
	 * @param user 系统用户对象
	 * @return true/false
	 */
	private boolean isPartnerUser(WxTUser user) {
		return user!= null && WxTUser.USER_MODEL_SP.equals(user.getUserModel());
	}


    @ResponseBody
    @RequestMapping(value = "/createCoupon.do", method = {RequestMethod.POST})
    public ResponseMap createCoupon(@RequestBody WXTMaterialVo params) {
        ResponseMap resultMap = new ResponseMap();
        WXTMaterialVo material = null;
        try {
            material = materialService.createCoupon(params);
        } catch (WxPltException e) {
            resultMap.put("success", false);
            resultMap.put("msg", e.getMessage());
            resultMap.put(Constants.RESULT_ERROR, e.getMessage());
            log.error("程序异常:", e.getMessage(), e);
            return resultMap;
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("msg", "系统异常，请联系管理员");
            resultMap.put(Constants.RESULT_ERROR, "系统异常，请联系管理员");
            log.error("程序异常:", e.getMessage(), e);
            return resultMap;
        }
        resultMap.put("data", material);
        resultMap.put("success", true);
        resultMap.put("msg", "物料信息获取成功");
        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value = "/getCouponById.do", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMap createCoupon(@RequestParam("couponId") Long couponId) {
        ResponseMap resultMap = new ResponseMap();
        WXTMaterialVo material = null;
        try {
            material = materialService.getCouponById(couponId);
        } catch (WxPltException e) {
            resultMap.put("success", false);
            resultMap.put("msg", e.getMessage());
            resultMap.put(Constants.RESULT_ERROR, e.getMessage());
            log.error("程序异常:", e.getMessage(), e);
            return resultMap;
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("msg", "系统异常，请联系管理员");
            resultMap.put(Constants.RESULT_ERROR, "系统异常，请联系管理员");
            log.error("程序异常:", e.getMessage(), e);
            return resultMap;
        }
        resultMap.put("data", material);
        resultMap.put("success", true);
        resultMap.put("msg", "获取优惠券成功");
        return resultMap;
    }
}
