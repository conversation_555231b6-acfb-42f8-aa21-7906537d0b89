package com.sys.quartz.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.performance.service.VisitPlanService;
import com.sys.log.util.LogUtils;

import java.util.Date;

public class SendEmailForVisitPlanWarning {

	@Autowired
	private VisitPlanService visitPlanService;
	
	private static Logger log = LoggerFactory.getLogger(SendEmailForVisitPlanWarning.class);
	
	public synchronized void doSendEmailForRenamWorkShop()
	{
		log.info("SendEmailForVisitPlanWarning job start....");
		LogUtils.addInfoLog(1l, "VisitPlanService.sendVisitPlanWarningEmail", "开始发送flsr拜访计划预警邮件："+new Date());
		try {
			visitPlanService.sendVisitPlanWarningEmail();
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.addErrorLog(1l, "VisitPlanService.sendVisitPlanWarningEmail", "发送flsr拜访计划预警失败。" + e.getMessage(), null);
		}
		log.info("sendVisitPlanWarningEmail job finish....");
		LogUtils.addInfoLog(1l, "VisitPlanService.sendVisitPlanWarningEmail", "发送flsr拜访计划预警邮件结束："+new Date());
	}
}
