--乘用车促销礼品
select cs.sales_name_cn "FLSR", cs.region, o.organization_name "经销商名称", p.partner_id "Partner ID", p.point_code "积分类型", 
pp.point_name "积分名称",p.available_quantity "剩余数量",p.material_code "SPU",(select top 1 md1.material_name from wx_t_material2021_dictionary md1 where md1.spu=p.material_code) "礼品名称"
from wx_t_promotion_gift_pool p
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cs on cs.distributor_id=pe.distributor_id and cs.channel_weight&1>0
where p.application_type='cdm_promotion' and available_quantity>0 and p.material_id<0
order by cs.sales_name_cn

--商用油促销礼品
select cos1.region Region, cos1.sales_name_cn "FLSR", o.organization_name "经销商名称", p.partner_id "Partner ID", p.point_code "积分类型", 
pp.point_name "积分名称",p.available_quantity "剩余数量",p.material_code "SPU",(select top 1 md1.material_name from wx_t_material2021_dictionary md1 where md1.spu=p.material_code) "礼品名称"
from wx_t_promotion_gift_pool p
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
left join dw_customer_org_sales cos1 on cos1.distributor_id=en.distributor_id and cos1.channel_weight&2>0
where p.application_type='promotion' and available_quantity>0 and p.material_id<0
order by cos1.sales_name_cn


select cs.sales_name_cn "FLSR", cs.region, o.organization_name "经销商名称", p.partner_id "Partner ID", p.point_code "积分类型", 
pp.point_name "积分名称",pl.modify_quantity "清零数量",p.material_code "SPU",(select top 1 md1.material_name from wx_t_material2021_dictionary md1 where md1.spu=p.material_code) "礼品名称"
from wx_t_promotion_gift_modify_log pl
left join wx_t_promotion_gift_pool p on pl.pool_id=p.id
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cs on cs.distributor_id=pe.distributor_id and cs.channel_weight&1>0
where p.application_type='cdm_promotion' --and available_quantity>0 
and p.material_id<0 and pl.business_key like 'CLEAR_20220505_%'
order by cs.region,cs.sales_name_cn


select cos1.region Region, cos1.sales_name_cn "FLSR", o.organization_name "经销商名称", p.partner_id "Partner ID", p.point_code "积分类型", 
pp.point_name "积分名称",pl.modify_quantity "清零数量",p.material_code "SPU",(select top 1 md1.material_name from wx_t_material2021_dictionary md1 where md1.spu=p.material_code) "礼品名称"
from wx_t_promotion_gift_modify_log pl
left join wx_t_promotion_gift_pool p on pl.pool_id=p.id
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
left join dw_customer_org_sales cos1 on cos1.distributor_id=en.distributor_id and cos1.channel_weight&2>0
where p.application_type='promotion' --and available_quantity>0 
and p.material_id<0 and pl.business_key like 'CLEAR_20220505_%'
order by cos1.region,cos1.sales_name_cn

--乘用车促销礼品
select cs.sales_name_cn "FLSR", cs.region, o.organization_name "经销商名称", p.partner_id "Partner ID", p.point_code "积分类型", 
pp.point_name "积分名称",p.material_code "SPU",
(select top 1 md1.material_name from wx_t_material2021_dictionary md1 where md1.spu=p.material_code) "礼品名称",
sum(case when pl.business_type='SELL_IN_PROMOTION_DELIVERY_V2' then pl.modify_quantity else 0 end) "总发放",
sum(case when pl.business_type='exchange' then pl.modify_quantity else 0 end) "总兑换",
sum(case when pl.business_type='CLEAR' then pl.modify_quantity else 0 end) "总清零"
from wx_t_promotion_gift_pool p
left join wx_t_promotion_gift_modify_log pl on pl.pool_id=p.id
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cs on cs.distributor_id=pe.distributor_id and cs.channel_weight&1>0
where p.application_type='cdm_promotion' and pl.create_time>='2022-01-01' and p.material_id<0 --available_quantity>0 and p.material_id<0
group by cs.sales_name_cn, cs.region, o.organization_name, p.partner_id, p.point_code, 
pp.point_name,p.material_code
order by cs.region, cs.sales_name_cn,p.partner_id

--商用油促销礼品
select cs.sales_name_cn "FLSR", cs.region, o.organization_name "经销商名称", p.partner_id "Partner ID", p.point_code "积分类型", 
pp.point_name "积分名称",p.material_code "SPU",
(select top 1 md1.material_name from wx_t_material2021_dictionary md1 where md1.spu=p.material_code) "礼品名称",
sum(case when pl.business_type='SELL_IN_PROMOTION_DELIVERY_V2' then pl.modify_quantity else 0 end) "总发放",
sum(case when pl.business_type='exchange' then pl.modify_quantity else 0 end) "总兑换",
sum(case when pl.business_type='CLEAR' then pl.modify_quantity else 0 end) "总清零"
from wx_t_promotion_gift_pool p
left join wx_t_promotion_gift_modify_log pl on pl.pool_id=p.id
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
left join dw_customer_org_sales cs on cs.distributor_id=pe.distributor_id and cs.channel_weight&2>0
where p.application_type='promotion' and pl.create_time>='2022-01-01' and p.material_id<0 --available_quantity>0 and p.material_id<0
group by cs.sales_name_cn, cs.region, o.organization_name, p.partner_id, p.point_code, 
pp.point_name,p.material_code
order by cs.region, cs.sales_name_cn,p.partner_id

select sum(award_quantity) 
--*
--update d set d.delivery_status=50
from wx_t_promotion_delivery_detail d where d.delivery_status=20 --and ext_property7!='1123' 
and create_time<'2022-06-01' and create_time>='2022-01-10'
