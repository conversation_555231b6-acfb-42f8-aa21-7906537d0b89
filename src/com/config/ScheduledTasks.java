package com.config;

import cn.emay.ResultModel;
import cn.emay.eucp.inter.http.v1.dto.request.SmsSingleRequest;
import cn.emay.util.AES;
import cn.emay.util.GZIPUtils;
import cn.emay.util.JsonHelper;
import cn.emay.util.http.*;
import com.chevron.master.util.GiftPackageConstants;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.auth.service.impl.UserService;
import com.sys.utils.business.impl.EtoBizServiceImpl;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

@Component
public class ScheduledTasks {

    private static Logger log = LoggerFactory.getLogger(ScheduledTasks.class);
    @Resource
    UserService userService;

    @Resource
    EtoBizServiceImpl etoBizService;

    @Scheduled(cron = "0 0/2 * * * ?")
    public void checkPPIsOKByLoginTask() {
        log.info("checkPPIsOKByLoginTask job start....");
        RedisTemplate<Serializable, Serializable> redisTemplate = SpringUtils.getBean(RedisTemplate.class);
        String key = "scheduledTasksLockKey";
        //获取到redis分布式锁
        if(lock(redisTemplate,key)) {
            log.info("checkPPIsOKByLoginTask job 获取到分布式锁 start");
            //环境
            String env = MyPropertyConfigurer.getVal("pp.db.env");
            String mobileStr = (String) Constants.getSystemPropertyByCodeType(Constants.SYSTEM_CHECK_LOGIN_MOBILE);
            String[] to = null;
            String openidstr = (String) Constants.getSystemPropertyByCodeType(Constants.SYSTEM_CHECK_LOGIN_OPENID);
            String checkLongName = (String) Constants.getSystemPropertyByCodeType(Constants.SYSTEM_CHECK_LOGIN_KEY);
            String emails = (String) Constants.getSystemPropertyByCodeType(Constants.SYSTEM_CHECK_LOGIN_EMAIL);
            //查询用户接口，验正数据库连接是否正常
            try {
                if(StringUtils.isEmpty(emails)){
                    to = new String[]{"<EMAIL>"};
                }else {
                    to = emails.split(",");
                }
                if(StringUtils.isEmpty(checkLongName)){
                    checkLongName = "test_user";
                }
                userService.checkUserLogin(checkLongName);
            } catch (Exception ex) {
                String v = (String) redisTemplate.opsForValue().get("CheckPPIsOKByLoginTask");
                if(StringUtils.isEmpty(v)){
                    redisTemplate.delete("CheckPPIsOKByLoginTask");
                    v = "penologist";
                    redisTemplate.opsForValue().set("CheckPPIsOKByLoginTask",v);
                    //redis 设置时间间隔,30分钟频次
                    redisTemplate.expire("CheckPPIsOKByLoginTask",60 * 30,  TimeUnit.SECONDS);

                    //开始执行告警推送操作
                    log.info("PP系统无法登录告警推送 start....");

                    String content = env+": PP系统登录异常    \n" +
                            "    \n" +
                            "        Dear All，\n" +
                            "    \n" +
                            "    \n" +
                            "        告警描述：PP系统登录异常。\n" +
                            "    \n" +
                            "    \n" +
                            "        告警明细：PP系统监测账号登录异常,请查看数据库是否正常!\n" +
                            "    \n" +
                            "    \n" +
                            "    \n" +
                            "    \n" +
                            "          注意：这封邮件是系统自动发送的，请不要直接回复！";

                    //微信通知,无日志记录
                    if (!StringUtils.isEmpty(openidstr)) {
                        try {
                            String[] openids = openidstr.split(",");
                            String ackey = etoBizService.getAckey(false);
                            for (String openid : openids) {
                                try {
                                    pushEtoWeixinAlarm("系统自定义告警", env+": PP系统登录异常",
                                            "系统自定义告警", "PP系统登录异常",
                                            "PP系统登录异常，请检查数据库连接!", "",
                                            openid, ackey, null);
                                } catch (WxPltException ee) {
                                    ee.PrintStackTrace();
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    try{
                        //邮件通知，无日志记录
                        EmailSender.sendWithoutSaveLog(to, null, env+": PP系统登录异常", content, null, "text/plain", EmailSender.DEFAULT_SENDER_NICK, null);
                        //发送短信
                        if(!StringUtils.isEmpty(mobileStr)){
                            for (String mobile : mobileStr.split(",")) {
                                log.info("PP系统监测账号登录异常 doSendMobile mobile:" + mobile);
                                //有日志
                                //SendMessageUtil.sndMessageToPhone(mobile, "PP系统监测账号登录异常,请检查!");
                                //无日志
                                sendSms(mobile, env+": PP系统监测账号登录异常,请检查!");
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
                ex.printStackTrace();
                log.info("PP系统无法登录告警推送 end....");
            }/*finally {
                //释放锁
                unlock(redisTemplate,key);
            }*/
        }else {
            log.info("checkPPIsOKByLoginTask job 未获取到分布式锁....");
        }
        log.info("checkPPIsOKByLoginTask job end....");
        //避免意外死锁
        unlock(redisTemplate,key);
    }
    private synchronized void pushEtoWeixinAlarm (String eventId, String alarmName, String alarmType, String
            entityName,
                                                  String alarmContent, String alarmSummary,
                                                  String userOpenId, String ackey, Long messageId) throws
            WxPltException {
        String url = (String) Constants.getSystemPropertyByCodeType("Eto.wechatMessage.url");
        if (url.indexOf("?") < 0) {
            url += "?";
        } else {
            url += "&";
        }
        url += "ackey=" + ackey;
        Map<String, Object> dataMap = new TreeMap<String, Object>();

        Map<String, String> firstMap = new HashMap<String, String>();
        firstMap.put("value", eventId);
        firstMap.put("color", "#173177");

        Map<String, String> keyword1Map = new HashMap<String, String>();
        keyword1Map.put("value", alarmContent);
        keyword1Map.put("color", "#173177");

        Map<String, String> keyword2Map = new HashMap<String, String>();
        keyword2Map.put("value", alarmType);
        keyword2Map.put("color", "#173177");

        Map<String, String> keyword3Map = new HashMap<String, String>();
        keyword3Map.put("value", entityName);
        keyword3Map.put("color", "#173177");

        Map<String, String> keyword4Map = new HashMap<String, String>();
        keyword4Map.put("value", alarmSummary);
        keyword4Map.put("color", "#173177");

        Map<String, String> keyword5Map = new HashMap<String, String>();
        keyword5Map.put("value", DateUtil.getDateTimeStr(new Date()));
        keyword5Map.put("color", "#173177");

        dataMap.put("first", firstMap);
        dataMap.put("keyword1", keyword1Map);
        dataMap.put("keyword2", keyword2Map);
        dataMap.put("keyword3", keyword3Map);
        dataMap.put("keyword4", keyword4Map);
        dataMap.put("keyword5", keyword5Map);

        Map<String, Object> postPra = new HashMap<String, Object>(5);

        postPra.put("data", dataMap);
        postPra.put("touser", userOpenId);
        postPra.put("template_id", Constants.getSystemPropertyByCodeType("Eto.wechatMessage.templateId"));
        postPra.put("url", Constants.getSystemPropertyByCodeType("app.host") + "/anon/mobile/warning.html?id=" + messageId);


        Map<String, String> httpHeader = new HashMap<String, String>();
        httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        log.info(JsonUtil.writeValue(postPra));
        String responseContent = HttpSender.postJSON(url, postPra, httpHeader);
        log.info("responseContent: " + responseContent);
        if (responseContent == null) {
            log.error("推送微信消息失败。url: " + url);
            throw new RuntimeException("推送微信消息失败。返回值为空");
        }
        JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
        if (!responseJsonObj.containsKey("errcode")) {
            log.error("推送微信消息失败。url: " + url);
            throw new RuntimeException("推送微信消息失败。" + responseContent);
        }
        int errcode = responseJsonObj.getInt("errcode");
        if (errcode != 0) {
            log.error("推送微信消息失败。url: " + url + ", response: " + responseContent);
            throw new WxPltException(errcode + "", responseJsonObj.getString("errmsg"));
        }
    }

    private void sendSms(final String mobile, String content) throws WxPltException {
        if(StringUtils.isBlank(mobile)) {
            throw new WxPltException("发送短信手机号不能为空");
        }
        SMSUtil simpleSMSUtil = new SMSUtil();
        if(simpleSMSUtil.getAPPIDS().size()==0) {
            simpleSMSUtil.initMessageProperty();
        }
        content = simpleSMSUtil.getMESSAGE_SIGNATURE() + content;
        //发送短信
        for(String ai : simpleSMSUtil.getAPPIDS().keySet()){
            if(ai==null) {
                throw new RuntimeException("短信密钥为空");
            }
            String secretKey = simpleSMSUtil.getAPPIDS().get(ai);
            SmsSingleRequest pamars = new SmsSingleRequest();
            pamars.setContent(content);
            pamars.setCustomSmsId(mobile);
            pamars.setExtendedCode(simpleSMSUtil.getEXTEND_CODE());
            pamars.setMobile(mobile);
            long currentTime = System.currentTimeMillis();
            ResultModel result = request(ai,secretKey,SMSUtil.ALGORITHM,pamars, "http://" + simpleSMSUtil.getHOST() + "/inter/sendSingleSMS",SMSUtil.IS_GZIP);
            if(!"SUCCESS".equals(result.getCode())){
                throw new RuntimeException("短信发送失败");
            }
        }
    }

    private ResultModel request(String appId, String secretKey, String algorithm, Object content, String url,final boolean isGzip) {
        Map<String, String> headers = new HashMap<String, String>();
        EmayHttpRequestBytes request = null;
        try {
            headers.put("appId", appId);
            String requestJson = JsonHelper.toJsonString(content);
            byte[] bytes = requestJson.getBytes("UTF-8");
            if (isGzip) {
                headers.put("gzip", "on");
                bytes = GZIPUtils.compress(bytes);
            }
            byte[] parambytes = AES.encrypt(bytes, secretKey.getBytes("UTF-8"), algorithm);
            request = new EmayHttpRequestBytes(url, "UTF-8", "POST", headers, null, parambytes);
        } catch (Exception e) {
            log.error("", e);
        }
        EmayHttpClient client = new EmayHttpClient();
        String code = null;
        String result = null;
        try {
            EmayHttpResponseBytes res = client.service(request, new EmayHttpResponseBytesPraser());
            if (res.getResultCode().equals(EmayHttpResultCode.SUCCESS)) {
                if (res.getHttpCode() == 200) {
                    code = res.getHeaders().get("result");
                    if (code.equals("SUCCESS")) {
                        byte[] data = res.getResultBytes();
                        data = AES.decrypt(data, secretKey.getBytes("UTF-8"), algorithm);
                        if (isGzip) {
                            data = GZIPUtils.decompress(data);
                        }
                        result = new String(data, "UTF-8");
                    }
                } else {
                    log.error("请求接口异常,请求码:" + res.getHttpCode());
                }
            } else {
                log.error("请求接口网络异常:" + res.getResultCode().getCode());
            }
        } catch (Exception e) {
            log.error("解析失败", e);
        }
        log.debug("result:" + result);
        ResultModel re = new ResultModel(code, result);
        return re;
    }


    private boolean lock(RedisTemplate redisTemplate,String key) {
        if (redisTemplate.opsForValue().setIfAbsent(key, "locked")) {
            redisTemplate.expire(key,3000,  TimeUnit.MILLISECONDS);
            return true;
        }
        return false;
    }

    private void unlock(RedisTemplate redisTemplate,String key) {
        redisTemplate.delete(key);
    }

}
