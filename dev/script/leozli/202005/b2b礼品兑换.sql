alter table wx_t_material add attribute3 nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL;
alter table wx_t_material add attribute4 nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL;
alter table wx_t_material add attribute5 nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL;

ALTER TABLE wx_t_material_application ALTER COLUMN ATTRIBUTE1 nvarchar(2000) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE wx_t_material_application ALTER COLUMN ATTRIBUTE2 nvarchar(2000) COLLATE Chinese_PRC_CI_AS NULL;

--虚拟商品供应商
INSERT INTO wx_t_dic_type (dic_type_code,dic_type_name,dic_type_desc,creator,create_time,updator,update_time) VALUES
('b2b.virtual.gift.supplier','B2B虚拟商品供应商','B2B虚拟商品供应商',NULL,NULL,NULL,NULL);

INSERT INTO wx_t_dic_item (dic_type_code,dic_item_code,dic_item_name,dic_item_desc,status,creator,create_time,updator,update_time,sort_numb) VALUES
('b2b.virtual.gift.supplier','Tencent','腾讯','腾讯','1',1,GETDATE(),1,GETDATE(),NULL)
;
INSERT INTO wx_t_dic_item (dic_type_code,dic_item_code,dic_item_name,dic_item_desc,status,creator,create_time,updator,update_time,sort_numb) VALUES
('b2b.virtual.gift.supplier','iQIYI','爱奇艺','爱奇艺','1',1,GETDATE(),1,GETDATE(),NULL)
;
INSERT INTO wx_t_dic_item (dic_type_code,dic_item_code,dic_item_name,dic_item_desc,status,creator,create_time,updator,update_time,sort_numb) VALUES
('b2b.virtual.gift.supplier','YOUKU','优酷土豆','优酷土豆','1',1,GETDATE(),1,GETDATE(),NULL)
;
INSERT INTO wx_t_dic_item (dic_type_code,dic_item_code,dic_item_name,dic_item_desc,status,creator,create_time,updator,update_time,sort_numb) VALUES
('b2b.virtual.gift.supplier','Mango','芒果','芒果','1',1,GETDATE(),1,GETDATE(),NULL);

INSERT INTO wx_t_dic_item (dic_type_code,dic_item_code,dic_item_name,dic_item_desc,status,creator,create_time,updator,update_time,sort_numb) VALUES
('material.type','XN','虚拟礼品','虚拟礼品','1',1,GETDATE(),1,GETDATE(),NULL);

INSERT INTO wx_t_properties (codetype,code,codename,make_time,operator,modify_time,modify_operator) VALUES
('virtual.gift.exchange.time.up','3','b2b积分兑换虚拟商品一天上限次数',NULL,NULL,NULL,NULL);

INSERT INTO wx_t_properties (codetype,code,codename,make_time,operator,modify_time,modify_operator) VALUES
('virtual.gift.exchange.point.up','300','b2b积分兑换虚拟商品一天上限积分数',NULL,NULL,NULL,NULL);
