select * from view_customer_region_sales_channel  where distributor_id=1191

select v.region_name, dpp.product_sku, (select substring((
 SELECT ','+cast(o1.organization_name as varchar(90)) FROM wx_t_dealer_product_permission dpp1 
 left join wx_t_organization o1 on dpp1.dealer_id=o1.id
left join wx_t_partner_o2o_enterprise pe1 on dpp1.dealer_id=pe1.partner_id
left join PP_MID.dbo.syn_dw_to_pp_customer c1 on pe1.sap_code=c1.customer_code
left join view_customer_region_sales_channel v1 on c1.distributor_id=v1.distributor_id
 where dpp1.product_sku=dpp.product_sku and v1.region_name=v.region_name FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) distirbutor_name,count(1) c from wx_t_dealer_product_permission dpp 
left join wx_t_partner_o2o_enterprise pe on dpp.dealer_id=pe.partner_id
left join PP_MID.dbo.syn_dw_to_pp_customer c on pe.sap_code=c.customer_code
left join view_customer_region_sales_channel v on c.distributor_id=v.distributor_id
left join wx_t_product p on dpp.product_sku=p.sku
where v.sales_channel_name='Indirect' and p.support_order=1 and v.region_name not in ('EC', 'NC - 绩效')
group by v.region_name, dpp.product_sku
having count(1) < 10
order by v.region_name

insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from 
(select * from wx_t_product ip1 where exists (select 1 from wx_t_dealer_product_permission dpp 
left join wx_t_partner_o2o_enterprise pe on dpp.dealer_id=pe.partner_id
left join PP_MID.dbo.syn_dw_to_pp_customer c on pe.sap_code=c.customer_code
left join view_customer_region_sales_channel v on c.distributor_id=v.distributor_id
where v.region_name in ('NE') and ip1.support_order=1 and dpp.product_sku=ip1.sku)) p, wx_t_organization o
where --p.sku in('500175LPK','503033DNK','503033DNK') and 
--p.sku not in('500268NJK') and
o.type=1 and o.status=1 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)
and o.id=56664

insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from 
(select * from wx_t_product ip1 where exists (select 1 from wx_t_dealer_product_permission dpp 
left join wx_t_partner_o2o_enterprise pe on dpp.dealer_id=pe.partner_id
left join PP_MID.dbo.syn_dw_to_pp_customer c on pe.sap_code=c.customer_code
left join view_customer_region_sales_channel v on c.distributor_id=v.distributor_id
where v.region_name in ('NE') and ip1.support_order=1 and dpp.product_sku=ip1.sku)) p, wx_t_organization o
where --p.sku in('500175LPK','503033DNK','503033DNK') and 
p.sku not in('500268NJK','500268LPK') and
o.type=1 and o.status=1 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)
--and o.id=56664
and exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.partner_id=o.id and pe.distributor_id=1191)

insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from 
wx_t_product p, wx_t_organization o
where p.sku in('503048LPB',   '500175NJB',  '500185LPB',   '500186NJB', '500186LPB') and p.support_order=1 and
--p.sku not in('500268NJK','500268LPK') and
o.type=1 and o.status=1 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)
--and o.id=56664
--and exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.partner_id=o.id and pe.distributor_id=1191)
order by o.id

--insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.id from 
wx_t_product p, wx_t_organization o
where p.sku in('503330DNB') and p.support_order=1 and
--p.sku not in('500268NJK','500268LPK') and
o.type=1 and o.status=1 and o.id!=9 and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.id) --and o.id not in (56308)
--and o.id=56664
and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=o.id and rp1.channel_weight&2>0)
order by o.id

update p set p.support_order=1 from wx_t_product p 
where p.sku in('503340HRB',  '503349HRB',   '503351HRB')

insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, o.dealer_id from 
wx_t_product p left join wx_t_dealer_product_permission o on o.product_sku='503311DNK'
where p.sku in('503333HRB',   '503340HRB',  '503349HRB',   '503351HRB', '505501HRB') and p.support_order=1 and
--p.sku not in('500268NJK','500268LPK') and
not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=o.dealer_id) --and o.id not in (56308)
--and o.id=56664
--and exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.partner_id=o.id and pe.distributor_id=1191)
--order by o.id


------------给sales_org=2051的进销商开放权限
insert into wx_t_dealer_product_permission (product_sku, dealer_id)
select distinct p.sku, org.id from 
wx_t_product p, 
[PP_MID].dbo.mid_partner_sale_config psc 
LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id 
left join wx_t_organization org on org.id = poe.partner_id 
WHERE org.status=1 and org.type = 1 and org.id!=9
and psc.sales_org = '2051'
and not exists (select 1 from wx_t_dealer_product_permission dpp where dpp.product_sku=p.sku and dpp.dealer_id=org.id)
and p.sku in('500722NJL',   '500694NJL',  '500723NJL',   '500720NJL') and p.support_order=1