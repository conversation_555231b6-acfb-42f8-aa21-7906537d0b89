select quarter, f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0) - isnull(f.consumer_ivi_fund, 0) 
				- isnull(f.consumer_ivi_fund_adjust, 0) - isnull(f.consumer_annual_reward, 0)) consumer_fund,
SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)  - isnull(f.commercial_ivi_fund, 0) 
				- isnull(f.commercial_ivi_fund_adjust, 0)) commercial_fund
from dw_mid_cal_elite_fund f --where f.remark is not null

group by quarter,f.distributor_id
order by quarter,f.distributor_id

select quarter,
SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0) - isnull(f.consumer_ivi_fund, 0) 
				- isnull(f.consumer_ivi_fund_adjust, 0) - isnull(f.consumer_annual_reward, 0)) consumer_fund,
SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)  - isnull(f.commercial_ivi_fund, 0) 
				- isnull(f.commercial_ivi_fund_adjust, 0) - isnull(f.commercial_annual_reward, 0)) commercial_fund, 
f.product_channel, max(u.ch_name) "发放人", CONVERT(varchar(16), f.create_time, 20) "发放时间"
from dw_mid_cal_elite_fund f --where f.remark is not null
left join wx_t_user u on u.login_name='cyana'
where f.year=2021 and f.quarter in ('Q3','Q4') and f.distributor_id=208
group by quarter,CONVERT(varchar(16), f.create_time, 20),f.product_channel
order by CONVERT(varchar(16), f.create_time, 20),f.product_channel