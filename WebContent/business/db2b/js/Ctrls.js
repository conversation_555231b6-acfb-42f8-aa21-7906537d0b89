Ctrls = {
    rpcClient: common.rpcClient,
    List: {
        method: '',
        singleton: false,
        getParam: function(instance, param){
            if(param && instance.param){
                if (typeof param !== "object") {
                    instance.param = param;
                } else {
                    instance.param = $.extend(instance.param, param);
                }
            }else if(param){
                instance.param = param;
            }
            if(instance.param === undefined){
                return [];
            }
            return [instance.param];
        },
        getData: function(result, items){
            return items;
        },
        init: function(valueField, container, opts){
            opts = $.extend({clazz: this}, this.defaults, opts);
            opts._valueField = valueField;
            var thiz = this;
            BUI.use('bui/select',function(Select){
                var items = [];
                if(!opts.autoLoad && opts.canEmpty){
                    items.push({value: opts.emptyValue, text: opts.emptyText});
                }
                var ctrlOpts = {
                        render:container,
                        valueField:valueField,
                        elStyle: {'white-space': 'nowrap'},
                        multipleSelect:false,
                        items:items
                    };
                if(opts.ctrlOpts){
                    ctrlOpts = $.extend(ctrlOpts, opts.ctrlOpts);
                }
                opts.ctrl = new Select.Select(ctrlOpts);
                opts.ctrl.render();
                if(opts.events){
                    for(var k in opts.events){
                        opts.ctrl.on(k, opts.events[k]);
                    }
                }
                opts = $.extend(opts, thiz._base);
                if(opts.autoLoad){
                	setTimeout(function(){
                        thiz.load(opts, opts.param);
                	}, 10);
                }
            });
            return opts;
        },
        empty: function(instance){
            $(instance._valueField).val(instance.canEmpty ? instance.emptyValue : '');
            var items = [];
            if(instance.canEmpty){
                items.push({value: instance.emptyValue, text: instance.emptyText});
            }
            instance.ctrl.set('items', items);
        },
        load: function(instance, param){
            var thiz = this;
            Ctrls.rpcClient.call(this.method,this.getParam(instance, param),
                    function(result) {
                        var items = [];
                        if(instance.canEmpty){
                            items.push({value: instance.emptyValue, text: instance.emptyText});
                        }
                        instance.ctrl.set('items', thiz.getData(result, items));
                        instance.ctrl.setSelectedValue($(instance._valueField).val());

                        // 允许空值, 且值字段初始值为空,默认值=emptyValue
                        if(instance.canEmpty && !$(instance._valueField).val()){
                            instance.ctrl.setSelectedValue(instance.emptyValue);
                        }

                        // 不允许空值, 且值字段初始值为空,默认值=items[defaultItemIndex]
                        if(!instance.delaySetValue && !instance.canEmpty && !$(instance._valueField).val()/*!instance.ctrl.getSelectedValue()*/){
                            var defaultItemIndex = instance.defaultItemIndex ? instance.defaultItemIndex : 0;
                            if(items&& items[defaultItemIndex]) {
                                instance.ctrl.setSelectedValue(items[defaultItemIndex].value);
                            }
                        }
                        if(instance.afterLoaded){
                        	instance.afterLoaded();
                        }
                },  function(error) {
                    common.ajaxTimeout(error);
                });
        },
        defaults: {
            canEmpty: true,
            emptyValue: '-999',
            emptyText: '全部',
            defaultItemIndex: 0,
            autoLoad: true,
            ctrlOpts: null,
            events: null,
            param: undefined
        },
        _base: {
            setValue: function(value){
                this.ctrl.setSelectedValue(value);
            },
            getText: function(){
            	return this.ctrl.getSelectedText();
            },
            empty: function(){
                this.clazz.empty(this);
            },
            load: function(param){
                this.clazz.load(this, param);
            },
            disable: function(){
                this.ctrl.disable();
            },
            enable: function(){
                this.ctrl.enable();
            },
            setItems: function(items) {
            	var value = $(this._valueField).val();
                this.ctrl.set('items', items);
                if(value){
                	this.setValue(value);
                }
            }
        }
    },
    SimpleSelect: {
        init: function(valueField, items, opts){
            opts = $.extend({}, this.defaults, opts);
            opts._valueField = valueField;
            var thiz = this;
            BUI.use('bui/select',function(Select){
                var ctrlOpts = {
                        render:$(valueField).parent(),
                        valueField:valueField,
                        elStyle: {'white-space': 'nowrap'},
                        multipleSelect:false,
                        items:items
                    };
                if(opts.ctrlOpts){
                    ctrlOpts = $.extend(ctrlOpts, opts.ctrlOpts);
                }
                opts.ctrl = new Select.Select(ctrlOpts);
                opts.ctrl.render();
                if(opts.events){
                    for(var k in opts.events){
                        opts.ctrl.on(k, opts.events[k]);
                    }
                }
            });
            $.extend(opts, this._base);
            return opts;
        },
        _base: {
            setValue: function(value){
                this.ctrl.setSelectedValue(value);
            },
            disable: function(){
                this.ctrl.disable();
            },
            enable: function(){
                this.ctrl.enable();
            },
            setItems: function(items) {
            	var value = $(this._valueField).val();
                this.ctrl.set('items', items);
                if(value){
                	this.setValue(value);
                }
            }
        },
        defaults: {
            ctrlOpts: null,
            events: null
        }
    },
//    Suggest: {
//        method: '',
//        getParam: function(instance, param){
//            if(param){
//                instance.param = $.extend(instance.param, param);
//            }
//            return instance.param === undefined ? [] : [instance.param];
//        },
//        init: function(valueField, container, opts){
//            if(valueField){
//                opts = $.extend({}, this.defaults, opts);
//                opts.valueField = valueField;
//            }
//            var ctrlOpts = {
//                    getDataMethod: 'data',
//                    idField: opts.idField,
//                    keyField: opts.keyField,
//                    effectiveFields: [opts.keyField],
//                    searchFields: [opts.keyField],
//                    listHoverStyle: 'background-color: #f5f5f5;color: #262626;',
//                    data: {value: []},
//                    showBtn: false
//            };
//            if(opts.ctrlOpts){
//                ctrlOpts = $.extend(ctrlOpts, opts.ctrlOpts);
//            }
//            opts.input = $(['<div class="bui-select input-group" style="white-space: nowrap;" tabindex="0" hidefocus="true" aria-pressed="false">',
//                            '<input class="bui-select-input control-text" type="text"', opts.emptyText ? (' placeholder="' + opts.emptyText + '"') : '',
//                            '><span class="input-group-btn" style="position: relative;">',
//                            '<button type="button" class="btn btn-white dropdown-toggle x-icon x-icon-normal" data-toggle="dropdown" style="height: 32px;">',
//                            '<i class="icon icon-caret icon-caret-down"></i>',
//                            '</button>',
//                            '<ul class="dropdown-menu dropdown-menu-right" role="menu"></ul></span>',
//                            '</div>'].join('')).appendTo($(container)).find('.bui-select-input');
//            opts.input.bsSuggest(ctrlOpts);
//            if(opts.input.val() == ''){
//                opts.input.parent().addClass('empty-btn-hide');
//            }
//            $('<span class="empty-btn"></span>').insertAfter(opts.input.addClass('control-auto')).click(function(){
//                opts.input.val('');
//                opts.input.parent().addClass('empty-btn-hide');
//            });
//            if(opts.events){
//                for(var k in opts.events){
//                    opts.input.on(k, opts.events[k]);
//                }
//            }
//            opts._value = $(opts.valueField).val();
//            opts.input.on('onSetSelectValue', function(e, item){
//                $(opts.valueField).val(item.id);
//                $(opts.valueField).trigger('change');
//            });
//            opts.input.on('onUnsetSelectValue', function(){
//                $(opts.valueField).val('');
//                $(opts.valueField).trigger('change');
//            });
//            opts.input.bind('change', function(){
//                if(this.value == ''){
//                    $(opts.valueField).val('');
//                }else if($(opts.valueField).val() == ''){
//                    this.value = '';
//                    opts.input.css('background', 'rgba(255, 255, 255, 0.1) none repeat scroll 0% 0%');
//                    opts.input.parent().addClass('empty-btn-hide');
//                }
////                if(opts.onchange && $(opts.valueField).val() != opts._value){
////                    opts._value = $(opts.valueField).val();
////                    opts.onchange(opts._value);
////                }
//            });
//            opts.input.blur(function(){
//                if(opts.onchange && $(opts.valueField).val() != opts._value){
//                    opts._value = $(opts.valueField).val();
//                    opts.onchange(opts._value);
//                }
//            }).on('keyup', function(){
//                if(this.value == ''){
//                    opts.input.parent().addClass('empty-btn-hide');
//                }else{
//                    opts.input.parent().removeClass('empty-btn-hide');
//                }
//            }).focus(function(){
//                if(this.value != ''){
//                    opts.input.parent().removeClass('empty-btn-hide');
//                }
//            });
//            if(opts.autoLoad){
//                this.load(opts);
//            }
//            $.extend(opts, this._base);
//            if(ctrlOpts.disabled){
//                opts.disable();
//            }
//            return opts;
//        },
//        empty: function(instance){
//            instance.input.data("bsSuggest").options.data.value = [];
//            instance.input.val('');
//            $(instance.valueField).val('');
//            if('' != instance._value){
//                instance._value = '';
//                instance.onchange(instance._value);
//            }
//        },
//        load: function(instance, param){
//            var thiz = this;
//            Ctrls.rpcClient.call(this.method,this.getParam(instance, param),
//                    function(result) {
//                        var items = [];
//                        instance.input.data("bsSuggest").options.data.value = thiz.getData(result, items);
//                        instance.setValue($(instance.valueField).val())
//                },  function(error) {
//                    common.ajaxTimeout(error);
//                });
//        },
//        getParam: function(instance, param){
//            if(param){
//                instance.param = $.extend(instance.param, param);
//            }
//            return [param];
//        },
//        getData: function(result, items){
//            return items;
//        },
//        _base: {
//            empty: function(){
//                this.input.data("bsSuggest").options.data.value = [];
//                this.input.val('');
//                $(this.valueField).val('');
//                if('' != this._value){
//                    this._value = '';
//                    this.onchange(this._value);
//                }
//            },
//            setValueText: function(value, text){
//                this.input.val(text);
//                $(this.valueField).val(value);
//                if(value != this._value){
//                    this._value = value;
//                    this.onchange(this._value);
//                }
//            },
//            setValue: function(value){
//                var data = this.input.data("bsSuggest").options.data.value, v = '', t = '';
//                for(var i = 0; i < data.length; i++){
//                    if(data[i][this.idField] == value){
//                        v = value;
//                        t = data[i][this.keyField];
//                        break;
//                    }
//                }
//                this.input.val(t);
//                $(this.valueField).val(v);
//                if(v != this._value){
//                    this._value = v;
//                    this.onchange(this._value);
//                }
//            },
//            disable: function(){
//                this.input.bsSuggest('disable');
//            },
//            enable: function(){
//                this.input.bsSuggest('enable');
//            }
//        },
//        defaults: {
//            idField: 'value',
//            keyField: 'text',
//            autoLoad: true,
//            ctrlOpts: null,
//            events: null,
//            param: null
//        }
//    },
    AutoComplete: {
        init: function(url, field, parameterName, params, opts){
            url = common.ctx + url;
            var instance = {url: url, ctrl: field, params: params};
            var wrapper = field.wrap('<div class="input-group"></div>').parent().append('<div class="input-group-btn" style="position: relative;"><ul class="dropdown-menu dropdown-menu-right" role="menu"></ul></div>');
            if(field.val() == ''){
                field.parent().addClass('empty-btn-hide');
            }
            $('<span class="empty-btn"></span>').insertAfter(field.addClass('control-auto')).click(function(){
                field.val('');
                field.parent().addClass('empty-btn-hide');
            });

            url = this._getURL(url, params);
            if(opts){
                opts = $.extend({url: url, parameterName: parameterName}, this._defaults, opts);
            }else{
                opts = $.extend({url: url, parameterName: parameterName}, this._defaults);
            }
            field.bsSuggest(opts);
            field.on('keyup', function(){
                if(this.value == ''){
                    field.parent().addClass('empty-btn-hide');
                }else{
                    field.parent().removeClass('empty-btn-hide');
                }
            }).focus(function(){
                if(this.value != ''){
                    field.parent().removeClass('empty-btn-hide');
                }
            });
            instance = $.extend(instance, this._base);
            return instance;
        },
        _getURL: function(url, params){
            if(params){
                var ps = null;
                for(var k in params){
                    if(!params[k]){
                        continue;
                    }
                    if(ps == null){
                        ps = k;
                    }else{
                        ps = ps + "&" + k;
                    }
                    ps = ps + '=' + params[k];
                }
                if(ps != null){
                    if(url.indexOf('?') < 0){
                        url = url + '?' + ps;
                    }else{
                        url = url + '&' + ps;
                    }
                }
            }
            return url;
        },
        _defaults: {
            getDataMethod: 'url',
            idField: 'value',
            keyField: 'text',
            effectiveFields: ['text'],
            searchFields: ['text'],
            showBtn: false,
            popNotEmpty: true,
            listStyle: {
                "padding-top": 0,
                "max-height": "375px",
                "max-width": "300px",
                overflow: "auto",
                width: "auto",
                transition: "0.5s",
                "-webkit-transition": "0.5s",
                "-moz-transition": "0.5s",
                "-o-transition": "0.5s"
            },
            listHoverStyle: 'background-color: #f5f5f5;color: #262626;',
            inputWarnColor: 'rgba(255,255,255,0.1)'
        },
        _base: {
            refresh: function(params){
                if(this.params && params){
                    $.extend(this.params, params);
                }else if(params){
                    this.params = params;
                }
                this.ctrl.val('').parent().addClass('empty-btn-hide');
                this.ctrl.data("bsSuggest").options.url = Ctrls.AutoComplete._getURL(this.url, this.params);
            }
        }
    },
    AutoSelect: {
        init: function(url, valueField, parameterName, opts){
            valueField = $(valueField);
            var placeholder = opts ? opts.placeholder : null, text = valueField.attr('text') || '', inputCls = opts ? opts.inputCls : null;
            if(inputCls){
                inputCls = ' ' + inputCls;
            }else{
                inputCls = '';
            }
            var field = $('<div class="input-group" style="white-space: nowrap;"><input type="text"' + (placeholder ? ' placeholder="' + placeholder + '"' : '') +
                    ' class="control-text control-auto' + inputCls + '" value="' + text +
                    '"><div class="input-group-btn" style="position: relative;"><ul class="dropdown-menu dropdown-menu-right" role="menu"></ul></div></div>')
                    .insertAfter(valueField).find('>input');
            if(!text){
                field.parent().addClass('empty-btn-hide');
            }
            url = common.ctx + url;
            var instance = {url: url, ctrl: field, valueField: valueField, params: opts ? opts.params : null, onchange: function(){
                if(field.val() == ''){

                }
                if(opts && opts.onchange){
                    opts.onchange.apply(instance, arguments);
                }
            }};
            $('<span class="empty-btn"></span>').insertAfter(field).click(function(){
                instance.empty();
            });

            url = this._getURL(url, opts ? opts.params : null);
            var o;
            if(opts && opts.ctrlOpts){
                o = $.extend({url: url, parameterName: parameterName}, this.defaults, opts.ctrlOpts);
            }else{
                o = $.extend({url: url, parameterName: parameterName}, this.defaults);
            }
            field.bsSuggest(o);
            field.on('onSetSelectValue', function(e, item){
            	
            	var pMap = opts.instance._nodeMap;
            	
                valueField.val(item.id);
                valueField.trigger('change');
            });
            field.on('onUnsetSelectValue', function(){
                valueField.val('');
                valueField.trigger('change');
            });
            field.on('keyup', function(){
                if(this.value == ''){
                    field.parent().addClass('empty-btn-hide');
                }else{
                    field.parent().removeClass('empty-btn-hide');
                }
            });
            field.bind('change', function(){
                if(this.value == ''){
                    valueField.val('');
                }else if(valueField.val() == ''){
                    this.value = '';
                    field.parent().addClass('empty-btn-hide');
                    field.css('background', 'rgba(255, 255, 255, 0.1) none repeat scroll 0% 0%');
                }
            });
            field.blur(function(){
                if(opts && opts.onchange && valueField.val() != instance._value){
                    instance._value = valueField.val();
                    opts.onchange(instance._value);
                }
            }).focus(function(){
                if(this.value != ''){
                    field.parent().removeClass('empty-btn-hide');
                }
            });
            instance = $.extend(instance, this._base);
            if(o.disabled){
                instance.disable();
            }
            //控件初始化回显
            instance._value = valueField.val();
            if(instance._value){
                field.val(valueField.attr('text'));
            }
            return instance;
        },
        _getURL: function(url, params){
            if(params){
                var ps = null;
                for(var k in params){
                    if(!params[k]){
                        continue;
                    }
                    if(ps == null){
                        ps = k;
                    }else{
                        ps = ps + "&" + k;
                    }
                    ps = ps + '=' + params[k];
                }
                if(ps != null){
                    if(url.indexOf('?') < 0){
                        url = url + '?' + ps;
                    }else{
                        url = url + '&' + ps;
                    }
                }
            }
            return url;
        },
        defaults: {
            getDataMethod: 'url',
            idField: 'value',
            keyField: 'text',
            valueField: 'sku',
            effectiveFields: ['text'],
            popNotEmpty: true,
            searchFields: ['text'],
            showBtn: false,
            listHoverStyle: 'background-color: #f5f5f5;color: #262626;'
        },
        _base: {
            refresh: function(params, byDelay){
                if(this.params && params){
                    $.extend(this.params, params);
                }else if(params){
                    this.params = params;
                }
//                if(!byDelay){
//                	var self = this;
//                	//通过延长触发事件，解决控件频繁刷新
//                	if(self._refreshTimer){
//                		clearTimeout(self._refreshTimer);
//                	}
//                	self._refreshTimer = setTimeout(function(){
//                		self.refresh(null, true);
//                	}, 100);
//                }
                this.setValueText('', '');
                this.ctrl.data("bsSuggest").options.url = Ctrls.AutoComplete._getURL(this.url, this.params);
            },
            empty: function(){
                this.setValueText('', '');
            },
            setValueText: function(value, text){
                if(text == ''){
                    this.ctrl.parent().addClass('empty-btn-hide');
                }
                this.ctrl.val(text);
                this.valueField.val(value);
                if(this.onchange && value != this._value){
                    this._value = value;
                    this.onchange(this._value);
                }
            },
            getText: function(){
            	return this.ctrl.val();
            },
            disable: function(){
                this.ctrl.parent().addClass('empty-btn-hide');
                this.ctrl.bsSuggest('disable');
            },
            enable: function(){
                if(this.ctrl.val() != ''){
                    this.ctrl.parent().removeClass('empty-btn-hide');
                }
                this.ctrl.bsSuggest('enable');
            }
        }
    },
    ImportUtil: {
    	init: function(title, url, templateFile, templateFileName, opts){
    		var instance = $.extend({title: title, url: url, templateFile: templateFile, templateFileName: templateFileName, _clazz: this}, this.defaults);
    		if(opts){
    			instance = $.extend(instance, opts);
    		}
    		return $.extend(instance, this._base);
    	},
    	_base: {
    		open: function(){
    			if(!this._importDialog){
    				this._importDialog = this._clazz.createDialog(this);
    			}
    			this._importDialog.show();
    		},
    		close: function(){
    			
    		}
    	},
    	createDialog: function(instance){
    		var dialog = null, bch = ['<div class="field-group defineclass" style="padding-bottom:0px;">',
    		  				'		<label class="field-label defineclass" style="vertical-align:baseline;">步骤1：</label>',
    						'		<div class="control-group defineclass">',
    						'			<span><i class="fa fa-info-circle"></i>模板下载</span>',
    						'		</div>',
    						'	</div>',
    						'	<div class="field-group defineclass" style="padding-top:0px;">',
    						'		<label class="field-label"></label>',
    						'		<div class="control-group">',
    						'			<div><a href="', common.ctx, instance.templateFile,'" class="excel">模版下载(',instance.templateFileName,')</a></div>',
    						'		</div>',
    						'	</div>',
    						'	<div class="defineclass" style="border-top:1px dashed #e7eaec; color:#ffffff; background-color:#ffffff; height:1px; margin:20px 0;"></div>',
    						'	<div class="field-group defineclass" style="padding-bottom:0px;">',
    						'		<label class="field-label" style="vertical-align:baseline;">步骤2：</label>',
    						'		<div class="control-group">',
    						'				<span><i class="fa fa-info-circle"></i>整理数据并导入</span>',
    						'		</div>',
    						'	</div>',
    						'	<div class="field-group" style="padding-top:0px;">',
    						'		<label class="field-label"></label>',
    						'		<div class="control-group">',
    						'			<form name="importForm">',
    						'				<input type="file" name="myfiles" accept="',instance.accept,'">',
    						'			</form>',
    						'			<span style="width: 80px important; display: inline; color: white; float: left; margin: 12px 0px 0px 6px;"></span>',
    						'		</div>',
    						'	</div>'];
    		instance._importBody = $(bch.join(''));
    		var opts = {
    				title: instance.title,
    				width: 600,
//    				height: 300,
    				bodyContent: instance._importBody,
    				buttons: [{
    					text: '导入',
    					elCls: 'button btn-create defineclassbtn',
    					handler: function () {
    						instance._clazz.importData(instance);
    					}
    				},
    				{
    					text: "取消",
    					elCls: 'button btn-cancel',
    					handler: function () {
    						this.close();
    					}
    				}]
    			};
    		if(instance.dialogOpts){
    			opts = $.extend(opts, instance.dialogOpts);
    		}
    		BUI.use('bui/overlay', function (Overlay) {
    			dialog = new Overlay.Dialog(opts);
    			dialog.render();
    		});
    		return dialog;
    	},
    	importData: function(instance){
    		if(instance._importBody.find("input[name=myfiles]")[0].value == ""){
    			common.alertMes("请选择要上传的文件", 'error');
    			return;
    		}
    		var formData = new FormData(instance._importBody.find('form')[0]);
    		$.ajax({
    	        url: common.ctx + instance.url,
    	        type: 'POST',
    	        data: formData,
    	        async: true,
    	        cache: false,
    	        contentType: false,
    	        processData: false,
    	        beforeSend: function () {
    	       	 	LoadMask.show();
    	        },
    	        success: function (returndata) {
    	        	switch(returndata.code){
    	            	case "success":
	            			if(instance.success(returndata, instance._importDialog) === false){
	            				return;
	            			}
	            			instance._importDialog.close();
	        	        	LoadMask.hide();
	            			if(returndata.successMsg){
    	            			common.alertMes(returndata.successMsg, 'success');
    	            		}else{
    	            			common.alertMes("操作成功！", 'success');
    	            		}
    	            		break;
    	            	case "warning":
    	    	        	LoadMask.hide();
    	            		common.alertMes(returndata.warning, 'warning');
    	            		break;
    	            	case "error":
    	    	        	LoadMask.hide();
    	                    common.alertMes(returndata.errorMsg, 'error');
    	            		break;
    	            	case "syserror":
    	    	        	LoadMask.hide();
    	            		common.alertMes(returndata.codeMsg, 'error');
    	            		break;
    	        	}
    	        },
    	        error: function (returndata) {
    	        	LoadMask.hide();
    	       	 	common.alertMes("访问接口socket连接超时", 'error');
    	        }
    	   });
    	},
    	defaults: {
    		accept: '.xls,.xlsx,.txt',
    		success: function(){}
    	}
    }
};

Ctrls.Partner = $.extend({}, Ctrls.List, {
    method: 'partnerServiceImpl.getPartnersByCtrl',
    getData: function(result, items){
        if(result.success){
            for(var i = 0; i < result.data.length; i++){
                items.push({text: result.data[i].organizationName, value: result.data[i].id});
            }
        }else{
            common.alertMes("加载合伙人失败", 'error');
        }
        return items;
    }
});

Ctrls.Workshop = $.extend({}, Ctrls.List, {
    method: 'workShopServiceImpl.getActiveWorkshopsByPartner',
    getParam: function(instance, param){
        if(param){
            instance.param = $.extend(instance.param, param);
        }
        return [instance.param ? instance.param.partnerId : null];
    },
    getData: function(result, items){
        if(result.success){
            for(var i = 0; i < result.data.length; i++){
                items.push({text: result.data[i].workShopName, value: result.data[i].id});
            }
        }else{
            common.alertMes("加载门店失败", 'error');
        }
        return items;
    }
});

Ctrls.WorkshopAuto = $.extend({}, Ctrls.AutoComplete, {
    init: function(field, params, opts){
        return Ctrls.AutoComplete.init.call(this, 'workshop/suggest.do', field, 'workshopName', params, opts);
    }
});

Ctrls.ProductAuto = $.extend({}, Ctrls.AutoComplete, {
    init: function(field, params, opts){
        return Ctrls.AutoComplete.init.call(this, 'product/suggest.do', field, 'searchText', params, opts);
    }
});

Ctrls.ProductAutoSelect = $.extend({}, Ctrls.AutoSelect, {
	init: function(field, opts){
		if(opts){
			opts.placeholder = '支持名称、首字母或SKU搜索';
		}else{
			opts = {placeholder: '支持名称、首字母或SKU搜索'};
		}
		opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
        		opts.instance._nodeMap = {};
                if (!json || !json.value) {
                    return false;
                }

                var len = json.value.length;

                for (var i = 0; i < len; i++) {
                    opts.instance._nodeMap[json.value[i].value + ''] = json.value[i].node;
//                    productMap[json.value[i].value] = json.value[i].node;
                }

                //字符串转化为 js 对象
                return json;
            }
        });
        opts.instance = Ctrls.AutoSelect.init.call(this, 'db2bproduct/Db2bProduct/suggest1.do', field, 'productName', opts);
//        opts.instance = Ctrls.AutoSelect.init.call(this, 'db2bproduct/Db2bProduct/suggest1.do', valueField, 'searchText', opts);
//		return Ctrls.AutoSelect.init.call(this, 'db2bproduct/Db2bProduct/suggest1.do', field, 'searchText', opts);
        opts.instance.getWorkshop = function(wsId){
        	return this._nodeMap[wsId + ''];
        }
        var ins = opts.instance;
        return opts.instance;
//		return Ctrls.AutoSelect.init.call(this, 'productmaster/suggest.do', field, 'searchText', opts);
	},
	defaults: {
		getDataMethod: 'url',
		effectiveFields: ['field4', 'field1', 'field0'],
        effectiveFieldsAlias: {
            'field4': "SKU",
            'field1': "中文名",
            'field0': '创建日期'
        },
        listStyle: {
            "padding-top": 0,
            "max-height": "375px",
            "max-width": "500px",
            overflow: "auto",
            width: "auto",
            transition: "0.5s",
            "-webkit-transition": "0.5s",
            "-moz-transition": "0.5s",
            "-o-transition": "0.5s"
        },
		idField: 'field4',
		keyField: 'field1',
		popNotEmpty: true,
//		searchFields: ['text', 'sku'],
		showHeader: true
	}
});

Ctrls.ProductAutoComplete = $.extend({}, Ctrls.AutoComplete, {//TODO
	init: function(field, opts){
		return Ctrls.AutoComplete.init.call(this, 'productmaster/suggest.do', field, 'searchText', opts);
	},
	_defaults: {
		getDataMethod: 'url',
		effectiveFields: ['field4', 'field3', 'field1', 'field0'],
        effectiveFieldsAlias: {
            'field4': "SKU",
            'field1': "中文名",
            'field3': "英文名",
            'field0': '创建日期'
        },
        listStyle: {
            "padding-top": 0,
            "max-height": "375px",
            "max-width": "800px",
            overflow: "auto",
            width: "auto",
            transition: "0.5s",
            "-webkit-transition": "0.5s",
            "-moz-transition": "0.5s",
            "-o-transition": "0.5s"
        },
		idField: 'field3',
		keyField: 'field3',
		popNotEmpty: true,
//		searchFields: ['text', 'sku'],
		showHeader: true,
        listHoverStyle: 'background-color: #f5f5f5;color: #262626;',
        inputWarnColor: 'rgba(255,255,255,0.1)'
	}
});

Ctrls.WorkshopAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts.params = $.extend({limit: 20}, opts.params);
        }else{
            opts = {params: {limit: 20}};
        }
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
        		opts.instance._nodeMap = {};
                if (!json || !json.value) {
                    return false;
                }

                var len = json.value.length;

                for (var i = 0; i < len; i++) {
                    opts.instance._nodeMap[json.value[i].value + ''] = json.value[i].node;
                }

                //字符串转化为 js 对象
                return json;
            }
        });
        opts.instance = Ctrls.AutoSelect.init.call(this, 'db2bproduct/Db2bProduct/suggest1.do', valueField, 'productName', opts);
        opts.instance.getWorkshop = function(wsId){
        	return this._nodeMap[wsId + ''];
        }
        return opts.instance;
    }
});
Ctrls.WorkshopSuggest = $.extend({}, Ctrls.List, {
    method: 'workShopServiceImpl.getActiveWorkshopsByPartner',
    getParam: function(instance, param){
        if(param){
            instance.param = $.extend(instance.param, param);
        }
        return [instance.param ? instance.param.partnerId : null];
    },
    init: function(valueField, container, opts){
        if(valueField){
            opts = $.extend({}, this.defaults, opts);
            opts.valueField = valueField;
        }
        var ctrlOpts = {
                getDataMethod: 'data',
                idField: 'value',
                keyField: 'text',
                effectiveFields: ['text'],
                searchFields: ['text'],
                listHoverStyle: 'background-color: #f5f5f5;color: #262626;',
                data: {value: []}
        };
        if(opts.ctrlOpts){
            ctrlOpts = $.extend(ctrlOpts, opts.ctrlOpts);
        }
        opts.input = $(['<div class="bui-select input-group" style="white-space: nowrap; width: 100%;" tabindex="0" hidefocus="true" aria-pressed="false">',
                        '<input class="bui-select-input" type="text"><span class="input-group-btn" style="position: relative;">',
                        '<button type="button" class="btn btn-white dropdown-toggle x-icon x-icon-normal" data-toggle="dropdown" style="height: 32px;">',
                        '<i class="icon icon-caret icon-caret-down"></i>',
                        '</button>',
                        '<ul class="dropdown-menu dropdown-menu-right" role="menu"></ul></span>',
                        '</div>'].join('')).appendTo($(container)).find('.bui-select-input');
        opts.input.bsSuggest(ctrlOpts);
        if(opts.events){
            for(var k in opts.events){
                opts.input.on(k, opts.events[k]);
            }
        }
        opts.input.on('onSetSelectValue', function(e, item){
            $(opts.valueField).val(item.id);
            $(opts.valueField).trigger('change');
        });
        opts.input.on('onUnsetSelectValue', function(){
            $(opts.valueField).val('');
            $(opts.valueField).trigger('change');
        });
        opts.input.bind('keyup', function(){
            if(this.value == ''){
                $(opts.valueField).val('');
                $(opts.valueField).trigger('change');
            }
        });
        if(opts.autoLoad){
            this.load(opts);
        }
        return opts;
    },
    empty: function(instance){
        instance.input.data("bsSuggest").options.data.value = [];
        instance.input.val('');
        $(instance.valueField).val('');
        $(opts.valueField).trigger('change');
    },
    load: function(instance, param){
        var thiz = this;
        instance.input.val('');
        $(instance.valueField).val('');
        $(opts.valueField).trigger('change');
        Ctrls.rpcClient.call(this.method,this.getParam(instance, param),
                function(result) {
                    var items = [];
                    instance.input.data("bsSuggest").options.data.value = thiz.getData(result, items);
            },  function(error) {
                common.ajaxTimeout(error);
            });
    },
    getData: function(result, items){
        if(result.success){
            for(var i = 0; i < result.data.length; i++){
                items.push({text: result.data[i].workShopName, value: result.data[i].id});
            }
        }else{
            common.alertMes("加载门店失败", 'error');
        }
        return items;
    }
});
//Ctrls.PartnerSuggest = $.extend({}, Ctrls.Suggest, {
//    method: 'partnerServiceImpl.getPartnersByCtrl',
//    getData: function(result, items){
//        if(result.success){
//            for(var i = 0; i < result.data.length; i++){
//                items.push({text: result.data[i].organizationName, value: result.data[i].id});
//            }
//        }else{
//            common.alertMes("加载合伙人失败", 'error');
//        }
//        return items;
//    },
//    init: function(valueField, container, opts){
//        opts = $.extend({param: null}, opts);
//        return Ctrls.Suggest.init.call(this, valueField, container, opts);
//    }
//});

//默认只有有门店的合伙人。如果和门店没有关系，那么需要传入参数spResource为true
Ctrls.PartnerAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts.params = $.extend({limit: 20}, opts.params);
        }else{
            opts = {params: {limit: 20}};
        }
        opts = $.extend({placeholder: '支持名称或名称首字母搜索'}, opts);
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
                if (!json || !json.data) {
                    return false;
                }

                var len, data = {
                        value: []
                    };
                len = json.data.length;

                for (var i = 0; i < len; i++) {
                    data.value.push({
                        value: json.data[i].id,
                        text: json.data[i].organizationName
                    });
                }

                //字符串转化为 js 对象
                return data;
            }
        });
        return Ctrls.AutoSelect.init.call(this, 'workshop/getPartnersInfo.do', valueField, 'partnerName', opts);
    }
});
Ctrls.UserAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts.params = $.extend({limit: 20}, opts.params);
        }else{
            opts = {params: {limit: 20}};
        }
        return Ctrls.AutoSelect.init.call(this, 'user/suggest.do', valueField, 'userName', opts);
    }
});
Ctrls.UserAutoNew = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts.params = $.extend({limit: 20,paging: 'false'}, opts.params);
        }else{
            opts = {params: {limit: 20,paging: 'false'}};
        }
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
                if (!json || !json.resultLst) {
                    return false;
                }

                //字符串转化为 js 对象
                return {
                    value: json.resultLst
                };
            }
        });
        return Ctrls.AutoSelect.init.call(this, 'user/ctrldata.do', valueField, 'keyWord', opts);
    },
	defaults: {
		getDataMethod: 'url',
		idField: 'userId',
		keyField: 'chName',
		effectiveFields: ['loginName','chName'],
        effectiveFieldsAlias: {
    		loginName: "登录名",
    		chName: "姓名"
        },
		popNotEmpty: true,
		searchFields: ['vipDesc'],
		showBtn: false,
		listHoverStyle: 'background-color: #f5f5f5;color: #262626;',
		showHeader: true
	}
});
Ctrls.PartnerUserAuto = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts.params = $.extend({limit: 20,paging: 'false'}, opts.params);
        }else{
            opts = {params: {limit: 20,paging: 'false'}};
        }
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
                if (!json || !json.resultLst) {
                    return false;
                }

                //字符串转化为 js 对象
                return {
                    value: json.resultLst
                };
            }
        });
        return Ctrls.AutoSelect.init.call(this, 'partneruser/ctrldata.do', valueField, 'keyWord', opts);
    },
	defaults: {
		getDataMethod: 'url',
		idField: 'userId',
		keyField: 'chName',
		effectiveFields: ['loginName','chName'],
        effectiveFieldsAlias: {
    		loginName: "登录名",
    		chName: "姓名"
        },
		popNotEmpty: true,
//		searchFields: ['vipDesc'],
		showBtn: false,
		listHoverStyle: 'background-color: #f5f5f5;color: #262626;',
		showHeader: true
	}
});
Ctrls.Province = $.extend({}, Ctrls.List, {
    method: 'regionServiceImpl.findFilteredRegionInfo',
    getParam: function(instance, param){
        return [-1];
    },
    getData: function(result, items){
        if(result.code == 'success'){
            for(var i = 0; i < result.regionList.length; i++){
                items.push({text: result.regionList[i].regionName, value: result.regionList[i].id});
            }
            return items;
        }else{
            common.alertMes("加载省份失败", 'error');
        }
    }
});
Ctrls.City = $.extend({}, Ctrls.List, {
    method: 'regionServiceImpl.findFilteredRegionInfo',
    getParam: function(instance, param){
        return [param.provinceId];
    },
    load: function(instance, param){
    	if(!param || !param.provinceId || param.provinceId == instance.emptyValue){
    		instance.empty();
    		instance.setValue(instance.emptyValue);
    	}else{
    		Ctrls.List.load.apply(this, arguments);
    	}
    },
    getData: function(result, items){
        if(result.code == 'success'){
            for(var i = 0; i < result.regionList.length; i++){
                items.push({text: result.regionList[i].regionName, value: result.regionList[i].id});
            }
            return items;
        }else{
            common.alertMes("加载城市失败", 'error');
        }
    }
});
Ctrls.District = $.extend({}, Ctrls.List, {
    method: 'regionServiceImpl.findFilteredRegionInfo',
    getParam: function(instance, param){
        return [param.cityId];
    },
    load: function(instance, param){
    	if(!param || !param.cityId || param.cityId == instance.emptyValue){
    		instance.empty();
    		instance.setValue(instance.emptyValue);
    	}else{
    		Ctrls.List.load.apply(this, arguments);
    	}
    },
    getData: function(result, items){
        if(result.code == 'success'){
            for(var i = 0; i < result.regionList.length; i++){
                items.push({text: result.regionList[i].regionName, value: result.regionList[i].id});
            }
            return items;
        }else{
            common.alertMes("加载区县失败", 'error');
        }
    }
});
Ctrls.DicItem = $.extend({}, Ctrls.List, {
	method: 'dicService.getDicItemByDicTypeCode',
	getParam: function (instance, param) {
		return [instance.dicTypeCode];
	},
	getData: function (result, items) {
		if (result.result == 'success') {
			for (var i = 0; i < result.data.length; i++) {
				items.push({ text: result.data[i].dicItemName, value: result.data[i].dicItemCode });
			}
			return items;
		}
		common.alertMes("加载数据字典失败", 'error');
		return items;
	},
	init: function(valueField, dicTypeCode, opts){
        if(opts){
            opts.dicTypeCode = dicTypeCode;
        }else{
            opts = {dicTypeCode: dicTypeCode};
        }
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});

Ctrls.Area = $.extend({}, Ctrls.List, {
	method: 'regionServiceImpl.getAllAreas',
	getParam: function (instance, param) {
		return [];
	},
	getData: function (result, items) {
		if (result.code == 'success') {
			for (var i = 0; i < result.resultLst.length; i++) {
				items.push({ text: result.resultLst[i], value: result.resultLst[i] });
			}
			return items;
		}
		common.alertMes("加载片区数据失败", 'error');
		return items;
	},
	init: function(valueField, opts){
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});

Ctrls.SalesChannel = $.extend({}, Ctrls.List, {
	method: 'masterDataService.findSalesChannel',
	getParam: function (instance, param) {
		return [param];
	},
	getData: function (result, items) {
		if (result.code == 'success') {
			for (var i = 0; i < result.resultLst.length; i++) {
				items.push({ text: result.resultLst[i].salesChannelName, value: result.resultLst[i].salesChannelName });
			}
			return items;
		}
		common.alertMes("加载Sales Channel数据失败", 'error');
		return items;
	},
	init: function(valueField, opts){
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});

Ctrls.BusinessRegion = $.extend({}, Ctrls.List, {
	method: 'masterDataService.findRegion',
	getParam: function (instance, param) {
		return [param];
	},
	getData: function (result, items) {
		if (result.code == 'success') {
			for (var i = 0; i < result.resultLst.length; i++) {
				items.push({ text: result.resultLst[i].regionName, value: result.resultLst[i].regionName });
			}
			return items;
		}
		common.alertMes("加载Region数据失败", 'error');
		return items;
	},
	init: function(valueField, opts){
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});

Ctrls.Role = $.extend({}, Ctrls.List, {
	method: 'wxRoleService.getAvailableRoles',
	getParam: function (instance, param) {
		return [param ? param.roleType : null];
	},
	getData: function (result, items) {
		if (result.code == 'success') {
			for (var i = 0; i < result.resultLst.length; i++) {
				items.push({ text: result.resultLst[i].roleDescript, value: result.resultLst[i].roleId });
			}
			return items;
		}
		common.alertMes("加载角色数据失败", 'error');
		return items;
	},
	init: function(valueField, opts){
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});
Ctrls.MenuAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
	    if(opts){
	        opts.params = $.extend({limit: 20,paging: 'false'}, opts.params);
	    }else{
	        opts = {params: {limit: 20,paging: 'false'}};
	    }
	    opts.ctrlOpts = $.extend(opts.ctrlOpts, {
	        processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
	            if (!json || !json.resultLst) {
	                return false;
	            }
	
	            var len, data = {
	                    value: []
	                };
	            len = json.resultLst.length;
	
	            for (var i = 0; i < len; i++) {
	                data.value.push({
	                    value: json.resultLst[i].menuId,
	                    text: json.resultLst[i].menuName
	                });
	            }
	
	            //字符串转化为 js 对象
	            return data;
	        }
	    });
        return Ctrls.AutoSelect.init.call(this, 'menu/ctrldata.do', valueField, 'menuName', opts);
    }
});
Ctrls.InputCtrl = $.extend({}, Ctrls.List, {
	method: 'inputCtrlService.getAvailableCtrls',
	getParam: function (instance, param) {
		return [];
	},
	getData: function (result, items) {
		if (result.code == 'success') {
			for (var i = 0; i < result.resultLst.length; i++) {
				items.push({ text: result.resultLst[i].ctrlTitle, value: result.resultLst[i].id });
			}
			return items;
		}
		common.alertMes("加载控件数据失败", 'error');
		return items;
	},
	init: function(valueField, opts){
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});

// Ctrls.PointType = $.extend({}, Ctrls.AutoSelect, {
//     method: 'pointTaskService.getPointType',
//     getParam: function(instance, param){
//         return [null];
//     },
//     getData: function(result, items){
//         if(result.code == 'success'){
//             for(var i = 0; i < result.resultLst.length; i++){
//                 items.push({text: result.resultLst[i].text, value: result.resultLst[i].value});
//             }
//             return items;
//         }else{
//             common.alertMes("积分类型加载失败", 'error');
//         }
//     },
//     // init: function(valueField, opts){
//     //     return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
//     // }
// });


Ctrls.PointType = $.extend({}, Ctrls.List, {
    method: 'pointTaskService.getPointType',
    getParam: function (instance, param) {
        return null;
    },
    getData: function (result, items) {
        if (result.code == 'success') {
            for (var i = 0; i < result.resultLst.length; i++) {
                items.push({text: result.resultLst[i].text, value: result.resultLst[i].value});
            }
            return items;
        }
        common.alertMes("加载数据字典失败", 'error');
        return items;
    },
    init: function(valueField, dicTypeCode, opts){
        // if(opts){
        //     opts.dicTypeCode = dicTypeCode;
        // }else{
        //     opts = {dicTypeCode: dicTypeCode};
        // }
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});

//值验证器
Ctrls.ValueValidator = $.extend({}, Ctrls.List, {
	method: 'valueValidatorService.getValueValidators',
	getParam: function (instance, param) {
		return [];
	},
	getData: function (result, items) {
		if (result.code == 'success') {
			for(var i = 0; i < result.list.length; i++){
				items.push({value:result.list[i].id, text:result.list[i].validatorName})
			}
			return items;
		}
		common.alertMes("加载值验证器数据失败", 'error');
		return items;
	},
	init: function(valueField, opts){
        return Ctrls.List.init.call(this, valueField, $(valueField).parent(), opts);
    }
});