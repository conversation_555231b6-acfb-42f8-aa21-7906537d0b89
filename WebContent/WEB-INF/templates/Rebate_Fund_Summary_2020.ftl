<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Author><PERSON>, <PERSON></Author>
  <LastAuthor><PERSON></LastAuthor>
  <Created>2019-06-10T08:59:11Z</Created>
  <LastSaved>2020-06-03T02:47:20Z</LastSaved>
  <Version>16.00</Version>
 </DocumentProperties>
 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
  <AllowPNG/>
 </OfficeDocumentSettings>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12212</WindowHeight>
  <WindowWidth>20382</WindowWidth>
  <WindowTopX>-111</WindowTopX>
  <WindowTopY>-111</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
  </Style>
  <Style ss:ID="m546836048">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546836068">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833096">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833116">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833156">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833176">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833196">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833216">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546833236">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834408">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#9999FF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834428">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#9999FF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834448">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#9999FF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834468">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#9999FF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834488">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834508">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834528">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834548">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#D9E1F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834568">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#D9E1F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834588">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#D9E1F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834608">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#D9E1F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834628">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834648">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834668">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834688">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m546834708">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s57">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Arial" x:Family="Swiss"/>
   <Interior/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s58">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCCCFF" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s59">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#9999FF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s60">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s61">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFF99" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s62">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#9999FF" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s63">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s64">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_ * #,##0.00_ ;_ * \-#,##0.00_ ;_ * &quot;-&quot;_ ;_ @_ "/>
  </Style>
  <Style ss:ID="s67">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s68">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s79">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s80">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s82">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s86">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#D9E1F2" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s87">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#D9E1F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s89">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s90">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#CCFFCC" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s92">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"
     ss:Color="#333333"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
   <NumberFormat/>
  </Style>
  <Style ss:ID="s93">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Tahoma" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#C6E0B4" ss:Pattern="Solid"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Fund Reimbursement Summary">
  <Table ss:ExpandedColumnCount="100" ss:ExpandedRowCount="3000" x:FullColumns="1"
   x:FullRows="1" ss:StyleID="s57" ss:DefaultColumnWidth="49.153846153846153"
   ss:DefaultRowHeight="13.153846153846153">
   <Column ss:StyleID="s57" ss:Width="221.53846153846155"/>
   <Column ss:StyleID="s57" ss:Width="110.76923076923077" ss:Span="65"/>
   <Column ss:Index="68" ss:StyleID="s57" ss:Width="221.53846153846155"/>
   <Row ss:AutoFitHeight="0" ss:Height="13.846153846153847">
    <Cell ss:StyleID="s58"><Data ss:Type="String">Rebate Summary (RMB)</Data></Cell>
    <Cell ss:Index="8" ss:MergeAcross="10" ss:StyleID="m546833156"><Data ss:Type="String">Earn Fund Without VAT</Data></Cell>
    <Cell ss:MergeAcross="10" ss:StyleID="s67"><Data ss:Type="String">Earn Fund With VAT</Data></Cell>
    <Cell ss:MergeAcross="21" ss:StyleID="m546834528"><Data ss:Type="String">Reimbursement With VAT</Data></Cell>
    <Cell ss:MergeAcross="7" ss:StyleID="m546834568"><Data ss:Type="String">Non-reimbursed Balance Without VAT</Data></Cell>
    <Cell ss:MergeAcross="7" ss:StyleID="m546834408"><Data ss:Type="String">Non-reimbursed Balance With VAT</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="13.846153846153847">
    <Cell ss:Index="8" ss:MergeAcross="2" ss:StyleID="m546833176"><Data ss:Type="String">Commerical </Data></Cell>
    <Cell ss:MergeAcross="3" ss:StyleID="m546833196"><Data ss:Type="String">Consumer</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546833216"><Data ss:Type="String">Industrial</Data></Cell>
    <Cell ss:StyleID="s80"></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546833096"><Data ss:Type="String">Commerical </Data></Cell>
    <Cell ss:MergeAcross="3" ss:StyleID="m546833116"><Data ss:Type="String">Consumer</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546833236"><Data ss:Type="String">Industrial</Data></Cell>
    <Cell ss:StyleID="s68"></Cell>
    <Cell ss:MergeAcross="5" ss:StyleID="m546834628"><Data ss:Type="String">Commerical </Data></Cell>
    <Cell ss:MergeAcross="8" ss:StyleID="m546834648"><Data ss:Type="String">Consumer</Data></Cell>
    <Cell ss:MergeAcross="5" ss:StyleID="m546834668"><Data ss:Type="String">Industrial</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String"></Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="m546834588"><Data ss:Type="String">Commercial</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546834608"><Data ss:Type="String">Consumer</Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="m546834548"><Data ss:Type="String">Industrial </Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String"></Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="m546834428"><Data ss:Type="String">Commercial</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546834448"><Data ss:Type="String">Consumer</Data></Cell>
    <Cell ss:MergeAcross="1" ss:StyleID="m546834468"><Data ss:Type="String">Industrial </Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String"></Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="13.846153846153847">
    <Cell ss:Index="8" ss:StyleID="s79"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s79"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s93"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s80"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s79"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s79"><Data ss:Type="String">Annual Bonus</Data></Cell>
    <Cell ss:StyleID="s93"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s80"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s79"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s93"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s80"><Data ss:Type="String">Total Fund </Data></Cell>
    <Cell ss:StyleID="s67"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s67"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s90"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s68"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s67"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s67"><Data ss:Type="String">Annual Bonus</Data></Cell>
    <Cell ss:StyleID="s90"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s68"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s67"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s90"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s68"><Data ss:Type="String">Total Fund </Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546834688"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546834708"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546836048"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546836068"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546836068"><Data ss:Type="String">Annual Bonus</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546834488"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:MergeAcross="2" ss:StyleID="m546834508"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String"></Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">Annual Bonus</Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s87"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Non-reimbursed Balance</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">Annual Bonus</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">MKT Fund</Data></Cell>
    <Cell ss:StyleID="s59"><Data ss:Type="String">IVI Fund</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Non-reimbursed Balance</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="27.519230769230766">
    <Cell ss:StyleID="s58"><Data ss:Type="String">客户名称(中文)</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Commerical region</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Commerical sales</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Consumer region</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Consumer sales</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Industrial region</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Industrial sales</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s92"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s92"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s92"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s82"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s89"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s89"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s89"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s60"><Data ss:Type="String">Q1-Q4</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">1st Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">2nd Reimbursement</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s61"><Data ss:Type="String">Reimbursement Amount</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s86"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">Total</Data></Cell>
    <Cell ss:StyleID="s58"><Data ss:Type="String">Remark</Data></Cell>
   </Row>
      <#list dataLists as data>
           <#assign
           cdm_mkt_balance = data.consumer_marketing_fund - data.cdm_mkt_1st - data.cdm_mkt_2st
           cdm_ivi_balance = data.consumer_ivi_fund - data.cdm_ivi_1st - data.cdm_ivi_2st
           ci_mkt_balance = data.commercial_marketing_fund - data.ci_mkt_1st -  data.ci_mkt_2st
           ci_ivi_balance = data.commercial_ivi_fund - data.ci_ivi_1st -  data.ci_ivi_2st
           ind_mkt_balance = data.industrial_marketing_fund - data.industrial_mkt_1st - data.industrial_mkt_2st
           ind_ivi_balance = data.industrial_ivi_fund - data.industrial_ivi_1st - data.industrial_ivi_2st
           cdm_bouns_balance = data.consumer_annual_reward - data.cdm_bonus_1st -  data.cdm_bonus_2st
           commercial_with_vat = data.commercial_marketing_fund + data.commercial_ivi_fund
           consumer_with_vat = data.consumer_marketing_fund + data.consumer_marketing_fund + data.consumer_annual_reward
           industrial_with_vat = data.industrial_marketing_fund + data.industrial_ivi_fund
           total_with_vat = commercial_with_vat + consumer_with_vat + industrial_with_vat
           commercial_without_vat = commercial_with_vat/data.rate
           consumer_without_vat = consumer_with_vat/data.rate
           industrial_without_vat = industrial_with_vat/data.rate
           total_without_vat = commercial_without_vat + consumer_without_vat + industrial_without_vat
           ci_mkt_year = data.ci_mkt_1st + data.ci_mkt_2st
           ci_ivi_year =  data.ci_ivi_1st + data.ci_ivi_2st
           cdm_mkt_year=  data.cdm_mkt_1st + data.cdm_mkt_2st
           cdm_ivi_year=  data.cdm_ivi_1st + data.cdm_ivi_2st
           cdm_bonus_year= data.cdm_bonus_1st + data.cdm_bonus_2st
           industrial_mkt_year = data.industrial_mkt_1st + data.industrial_mkt_2st
           industrial_ivi_year = data.industrial_ivi_1st + data.industrial_ivi_2st
           balance_with_vat = cdm_mkt_balance + cdm_ivi_balance + ci_mkt_balance + ci_ivi_balance + ind_mkt_balance + ind_ivi_balance + cdm_bouns_balance
           balance_without_vat =  balance_with_vat/data.rate
           >
          <Row ss:AutoFitHeight="0" ss:Height="13.846153846153847">
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.customer_name_cn!}</Data></Cell>
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.ci_region!}</Data></Cell>
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.ci_sales!}</Data></Cell>
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.cdm_region!}</Data></Cell>
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.cdm_sales!}</Data></Cell>
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.industrial_region!}</Data></Cell>
              <Cell ss:StyleID="s63"><Data ss:Type="String">${data.industrial_sales!}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.commercial_marketing_fund/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.commercial_ivi_fund/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${commercial_without_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.consumer_marketing_fund/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.consumer_ivi_fund/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.consumer_annual_reward/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-3]:RC[-1])"><Data ss:Type="Number">${consumer_without_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_marketing_fund/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_ivi_fund/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${industrial_without_vat}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=RC[-8]+RC[-4]+RC[-1]"><Data ss:Type="Number">${total_without_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.commercial_marketing_fund}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.commercial_ivi_fund}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${commercial_with_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.consumer_marketing_fund}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.consumer_ivi_fund}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.consumer_annual_reward}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-3]:RC[-1])"><Data ss:Type="Number">${consumer_with_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_marketing_fund}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_ivi_fund}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${industrial_with_vat}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=RC[-8]+RC[-4]+RC[-1]"><Data ss:Type="Number">${total_with_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.ci_mkt_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.ci_mkt_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${ci_mkt_year}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.ci_ivi_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.ci_ivi_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${ci_ivi_year}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.cdm_mkt_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.cdm_mkt_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${cdm_mkt_year}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.cdm_ivi_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.cdm_ivi_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${cdm_ivi_year}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.cdm_bonus_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.cdm_bonus_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${cdm_bonus_year}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_mkt_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_mkt_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${industrial_mkt_year}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_ivi_1st}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${data.industrial_ivi_2st}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-2]:RC[-1])"><Data ss:Type="Number">${industrial_ivi_year}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=RC[-19]+RC[-16]+RC[-13]+RC[-10]+RC[-7]+RC[-4]+RC[-1]"></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ci_mkt_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ci_ivi_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${cdm_mkt_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${cdm_ivi_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${cdm_bouns_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ind_mkt_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ind_ivi_balance/data.rate}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-7]:RC[-1])"><Data ss:Type="Number">${balance_without_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ci_mkt_balance}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ci_ivi_balance}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${cdm_mkt_balance}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${cdm_ivi_balance}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${cdm_bouns_balance}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ind_mkt_balance}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="Number">${ind_ivi_balance}</Data></Cell>
              <Cell ss:StyleID="s64" ss:Formula="=SUM(RC[-7]:RC[-1])"><Data ss:Type="Number">${balance_with_vat}</Data></Cell>
              <Cell ss:StyleID="s64"><Data ss:Type="String"></Data></Cell>
          </Row>
      </#list>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <Unsynced/>
   <Print>
    <FitHeight>0</FitHeight>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <HorizontalResolution>300</HorizontalResolution>
    <VerticalResolution>300</VerticalResolution>
   </Print>
   <Selected/>
   <FreezePanes/>
   <FrozenNoSplit/>
   <SplitHorizontal>4</SplitHorizontal>
   <TopRowBottomPane>4</TopRowBottomPane>
   <SplitVertical>1</SplitVertical>
   <LeftColumnRightPane>10</LeftColumnRightPane>
   <ActivePane>0</ActivePane>
   <Panes>
    <Pane>
     <Number>3</Number>
    </Pane>
    <Pane>
     <Number>1</Number>
     <ActiveCol>1</ActiveCol>
    </Pane>
    <Pane>
     <Number>2</Number>
    </Pane>
    <Pane>
     <Number>0</Number>
     <ActiveCol>16</ActiveCol>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>
