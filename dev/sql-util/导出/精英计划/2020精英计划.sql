select a.*, x.consumer_apply, x.commerical_apply from (
select f.distributor_id, max([customer_name_cn]) [customer_name_cn], 
SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0) - isnull(f.consumer_ivi_fund, 0) 
				- isnull(f.consumer_ivi_fund_adjust, 0) - isnull(f.consumer_annual_reward, 0)) consumer_fund,
SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)  - isnull(f.commercial_ivi_fund, 0) 
				- isnull(f.commercial_ivi_fund_adjust, 0)) commercial_fund
from dw_mid_cal_elite_fund f 
group by f.distributor_id) a
left join (select f1.distributor_id, sum(case when fund_type='consumer_mkt_fund' then f1.apply_amount else 0 end) consumer_apply,
sum(case when fund_type='commerical_mkt_fund' then f1.apply_amount else 0 end) commerical_apply
	from wx_t_v2_elite_fund_form f1
	where f1.form_status >= 10 and f1.delete_flag=0 
	group by f1.distributor_id) x on a.distributor_id=x.distributor_id
order by a.distributor_id