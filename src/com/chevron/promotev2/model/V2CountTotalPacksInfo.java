package com.chevron.promotev2.model;

/**
 * 统计总的礼包数量
 * <AUTHOR>
 *
 */
public class V2CountTotalPacksInfo {

	 //路演消费者互动包总数
	 private Integer roadShowConsumerPacksTotalCount=0;
	 
	 //新店礼包总数
	 private Integer newStorePacksTotalCount = 0;
	 
	 //车队研讨会包总数
	 private Integer carYthPacksTotalCount = 0;
	 
     //优质研讨会包总数
	 private Integer yzYthPacksTotalCount = 0;
	 
	 //普通研讨会包总数
	 private Integer ptYthPacksTotalCount = 0;

	public Integer getRoadShowConsumerPacksTotalCount() {
		return roadShowConsumerPacksTotalCount;
	}

	public void setRoadShowConsumerPacksTotalCount(Integer roadShowConsumerPacksTotalCount) {
		this.roadShowConsumerPacksTotalCount = roadShowConsumerPacksTotalCount;
	}

	public Integer getNewStorePacksTotalCount() {
		return newStorePacksTotalCount;
	}

	public void setNewStorePacksTotalCount(Integer newStorePacksTotalCount) {
		this.newStorePacksTotalCount = newStorePacksTotalCount;
	}

	public Integer getCarYthPacksTotalCount() {
		return carYthPacksTotalCount;
	}

	public void setCarYthPacksTotalCount(Integer carYthPacksTotalCount) {
		this.carYthPacksTotalCount = carYthPacksTotalCount;
	}

	public Integer getYzYthPacksTotalCount() {
		return yzYthPacksTotalCount;
	}

	public void setYzYthPacksTotalCount(Integer yzYthPacksTotalCount) {
		this.yzYthPacksTotalCount = yzYthPacksTotalCount;
	}

	public Integer getPtYthPacksTotalCount() {
		return ptYthPacksTotalCount;
	}

	public void setPtYthPacksTotalCount(Integer ptYthPacksTotalCount) {
		this.ptYthPacksTotalCount = ptYthPacksTotalCount;
	}
	 
	 
	
}
