delete wiel1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='2' and wsi1.step_no>2

update wsi1 set wsi1.enable_flag=0
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='2' and wsi1.step_no>2 and t1.step_code!='END'
		 
		 
update wi set status=30 --4, 20
from wx_t_workflow_instance wi where wi.workflow_code='NON_LOCK_MKT' and wi.form_key='2'	

update f set f.form_status=35
from wx_t_non_local_mkt_flow_form f where id=2;

update r set r.reprot_status=1
from wx_t_non_local_mkt_flow_form f left join wx_t_main_local_marketing r on r.id=f.report_id where f.id=2

333
------------------------
delete wiel1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='1' and wsi1.step_no>2

update wsi1 set wsi1.enable_flag=0
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='1' and wsi1.step_no>2 and t1.step_code!='END'
		 
		 
update wi set status=30 --4, 20
from wx_t_workflow_instance wi where wi.workflow_code='NON_LOCK_MKT' and wi.form_key='1'	

update f set f.form_status=35
from wx_t_non_local_mkt_flow_form f where id=1;

update r set r.reprot_status=1
from wx_t_non_local_mkt_flow_form f left join wx_t_main_local_marketing r on r.id=f.report_id where f.id=1

---------------------------------
delete wiel1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='21' and wsi1.step_no>5
		 
update wiel1 set wiel1.execute_status=10
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id and wiel1.execute_status=30
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='21' and wsi1.step_no=5
		 
update wi set next_step_no=5,status=10 --4, 20
from wx_t_workflow_instance wi where wi.workflow_code='NON_LOCK_MKT' and wi.form_key='21'

update f set f.form_status=15
from wx_t_non_local_mkt_flow_form f where id=21;

update wsi1 set wsi1.enable_flag=1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=0 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='21' and wsi1.step_no>5
		 
		 
-----------------------------------------------------
delete wiel1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='20' and wsi1.step_no>5
		 
update wiel1 set wiel1.execute_status=10
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id and wiel1.execute_status=30
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='20' and wsi1.step_no=5
		 
update wi set next_step_no=5,status=10 --4, 20
from wx_t_workflow_instance wi where wi.workflow_code='NON_LOCK_MKT' and wi.form_key='20'

update f set f.form_status=15
from wx_t_non_local_mkt_flow_form f where id=20;
