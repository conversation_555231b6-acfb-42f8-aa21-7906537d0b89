/budget2021/negativeexpense.do?appToken=776fef939558c31a7d21c97286de7ccdd0dea068&year=2021
{
    "__version": "1.1",
    "code": "success",
    "resultLst": [
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "金富力 - 大区费用 - East & CC - 李强", //费用定位
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -94331.26687200007, //剩余预算
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "金富力 - 大区费用 - NC - 于伟新",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -10048.0,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "金富力 - 大区费用 - NW & SW - 许鹏",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -101774.28,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "德乐 - 大区费用 - NC - 仝军军",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -4064.7599999999948,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "德乐 - 大区费用 - NW & SW - 许鹏",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -24788.96,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "德乐 - 大区费用 - NW & SW - 白英杰",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -839.16,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "德乐 - 大区费用 - SC - 林孟军",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -1001.890000000014,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "工程机械 - 大区费用 - NW & SW - 许鹏",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -16000.0,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        },
        {
            "start": 0,
            "limit": 0,
            "field": null,
            "totalCount": 0,
            "direction": "ASC",
            "queryType": 2,
            "queryField": null,
            "firtPyForQueryField": null,
            "isOpernCustomMybatisInterceptor": "1",
            "id": null,
            "budgetYear": null,
            "brand": null,
            "budgetType": null,
            "budgetPartnerKey": null,
            "budgetKey": null,
            "budgetTitile": "金富力 - 市场费用 - NE",
            "channelWeight": null,
            "regionBudgetValue": null,
            "mktPayValue": null,
            "regionPayValue": null,
            "mktBudgetValue": null,
            "createUserId": null,
            "createTime": null,
            "updateUserId": null,
            "updateTime": null,
            "budgetFrom": null,
            "salesCai": null,
            "itemBudgetValue": null,
            "itemPayValue": null,
            "remainBudget": -56811.83999999997,
            "order": " ASC",
            "paging": true,
            "orderBy": ""
        }
    ]
}