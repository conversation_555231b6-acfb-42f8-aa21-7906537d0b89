--select pe.distributor_id,* 
update od set od.recv_distributor_id=pe.distributor_id
from wx_t_oem_delivery od 
left join wx_T_organization o on od.recv_name like '%' + o.organization_name + '%' and o.type=1
left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id
where distributor_id is not null

select bir.customer_name_cn,* 
FROM wx_t_oem_product_packaging_code oppc
	LEFT JOIN wx_t_oem_product_packaging opp ON opp.id = oppc.packaging_id
	LEFT JOIN wx_t_oem_delivery_product dp
	ON (dp.codelevel = 1
			AND oppc.code1 = dp.code)
		OR (dp.codelevel = 2
			AND oppc.code2 = dp.code)
	LEFT JOIN wx_t_oem_delivery d ON d.id = dp.delivery_id
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir on bir.distributor_id=d.recv_distributor_id
	where oppc.code1='102926120139'
	
select * from wx_t_workshop_employee we left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id
where wp.partner_id=	

select bir.customer_name_cn,* 
FROM wx_t_oem_delivery_product dp
	LEFT JOIN wx_t_oem_delivery d ON d.id = dp.delivery_id
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir on bir.distributor_id=d.recv_distributor_id
	where dp.code='000052010888237507062282'
	
{
  "jsonrpc": "2.0",
  "method": "qrCodeService.getB2BVerifyInfoByCap",
  "params": ["M201908292206362480","ejbegn8hzsst48j3","CDM"],
  "id": 1
}

http://127.0.0.1/anon/jsonp/getWechatTempFollowUrlForQrCode.do?qrcode=ucecbpeyisnrbi429p9vbbu2afer&longitude=106.513585&latitude=29.552049&callback=?

select o.organization_name, 
(select substring((
 SELECT ','+xx0.province_name + ' ' + xx0.region_name FROM (select distinct r3.region_name province_name, r2.region_name from wx_t_region_partner rp 
join wx_t_region r1 on r1.id=rp.region_id
join wx_t_region r2 on r2.id=r1.parent_id
join wx_t_region r3 on r3.id=r2.parent_id
where rp.partner_id=o.id) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000)) "分配城市"
from wx_t_organization o where o.type=1 and o.status=1 and o.id!=9
and o.id in (select o.id from view_customer_region_sales_channel v
left join wx_t_partner_o2o_enterprise pe on v.distributor_id=pe.distributor_id
left join wx_t_organization o on o.id=pe.partner_id where sales_cai='IYTE')

select * 
--update we set we.workshop_id=131350, workshop_name='上海合创贸易'
from wx_t_workshop_employee we where we.name ='小慧慧'--18696723217	
[门店]1<<0-坐标确认,1-MKT已维护,2-金富力用户,4-网站门店导航,5-德乐店招户外广告,6-德乐店招车身广告,7-新店礼包申请门店,8-经销商老板门店确认,9-经销商老板审核发放积分,10-参与德乐400MGX陈列活动,14-黑金店,30-不奖励技师注册积分