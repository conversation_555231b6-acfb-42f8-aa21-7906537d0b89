package com.chevron.credit.service.impl;

import com.chevron.credit.business.CreditAppRequestFormBizService;
import com.chevron.credit.constants.CreditConstants;
import com.chevron.credit.controller.CreditFinancialReportController;
import com.chevron.credit.dao.*;
import com.chevron.credit.model.*;
import com.chevron.credit.service.CreditApplicationService;
import com.chevron.credit.service.CreditCommonService;
import com.chevron.credit.service.CreditFinancialReportService;
import com.chevron.credit.service.CreditRangeService;
import com.chevron.credit.util.CreditApplicationUtil;
import com.chevron.credit.util.CreditFinancialReportUtil;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.dic.service.DicService;
import com.sys.file.model.WxAttFile;
import com.sys.file.service.FileManagerServiceI;
import com.sys.file.web.FileManager;
import com.sys.log.util.LogUtils;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;

@Service
public class CreditApplicationServiceImpl implements CreditApplicationService {
    private Logger logger = LoggerFactory.getLogger(CreditApplicationServiceImpl.class);

    @Autowired
    WxTCreditAppRequestFormVoMapper wxTCreditAppRequestFormVoMapper;

    @Autowired
    WxTCreditAppCustomerFinanceInfoVoMapper wxTCreditAppCustomerFinanceInfoVoMapper;

    @Autowired
    CreditRangeService creditRangeService;

    @Autowired
    CreditFinancialReportService creditFinancialReportService;

    @Autowired
    ActRuTaskVoMapper actRuTaskVoMapper;

    @Resource
    private FileManagerServiceI fileManagerService;

    @Autowired
    WxTCreditAppRequestFromAttVoMapper wxTCreditAppRequestFromAttVoMapper;

    @Autowired
    WxTUserMapper wxTUserMapper;

    @Autowired
    WxTCreditAppUserAbsentInfoVoMapper wxTCreditAppUserAbsentInfoVoMapper;

    @Autowired
    CreditCommonService creditCommonService;


    @Resource
    private DicService dicService;
    
    @Autowired
    private CreditAppRequestFormBizService creditAppRequestFormBizService;

//    /**
//     * 根据type获取一份草稿的applicationForm
//     * @param creditAppQueryPara
//     * @return
//     */
//    @Override
//    @Transactional
//    public JsonResponse getDraftApplicationForm(CreditAppQueryPara creditAppQueryPara) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//            String creditAppType = creditAppQueryPara.getCreditAppType();
//            if (creditAppType == null) {
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("creditType不能为空");
//                return jsonResponse;
//
//            }
//            CreditAppType creditAppTypeEnum = CreditAppType.getCreditAppTypeByValue(creditAppType);
//            if (creditAppTypeEnum == null) {
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("creditType不是正确的值");
//                return jsonResponse;
//            }
//            // 查询到当前用户的userId
////            WxTUser user = ContextUtil.getCurUserId();
//            // 然后根据creditAppType来查询那一条草稿的信息
////            Map<String, Object> queryMap = new HashMap<String, Object>();
////            queryMap.put("aiPreparedBy", user.getUserId());
////            queryMap.put("creditType", creditAppType);
////            WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.selectDraftAppFormByUserIdAndType(queryMap);
//            // 如果现在没有就可以新建一个
//            WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo;
//            if(creditAppQueryPara.getApplicationId() == null){
//                Date currentDate = DateUtil.getCurrentDate();
//
//                // 新建一个空的cfi的
//                WxTCreditAppCustomerFinanceInfoVo customerFinanceInfoVo = new WxTCreditAppCustomerFinanceInfoVo();
//                customerFinanceInfoVo.setCreateTime(currentDate);
//                customerFinanceInfoVo.setUpdateTime(currentDate);
//                wxTCreditAppCustomerFinanceInfoVoMapper.insertSelective(customerFinanceInfoVo);
//
//                // 新建一个空的appRequestForm
//                wxTCreditAppRequestFormVo = new WxTCreditAppRequestFormVo();
//                // 设置creditAppType 和 aiPreparedBy
//                wxTCreditAppRequestFormVo.setCreditType(creditAppType);
//                wxTCreditAppRequestFormVo.setAiPreparedBy(ContextUtil.getCurUserId());
//                // 获得一个processNo
//                wxTCreditAppRequestFormVo.setRequestNo(CreditApplicationUtil.getNextProcessNo());
//                wxTCreditAppRequestFormVo.setDeleteFlag(0);
//                // 设置和cfi的对应关系
//                wxTCreditAppRequestFormVo.setCfiInfo(customerFinanceInfoVo);
//                wxTCreditAppRequestFormVo.setCfiInfoId(customerFinanceInfoVo.getId());
//                wxTCreditAppRequestFormVo.setCreateTime(currentDate);
//                wxTCreditAppRequestFormVo.setUpdateTime(currentDate);
//                // 增加设置AiRequestDate
//                wxTCreditAppRequestFormVo.setAiRequestDate(currentDate);
//                // 增加记录processStatus
//                wxTCreditAppRequestFormVo.setProcessStatus(ProcessStatusType.DRAFT.getValue());
//                wxTCreditAppRequestFormVoMapper.insertSelective(wxTCreditAppRequestFormVo);
//                wxTCreditAppRequestFormVo = getApplicationDetailById(wxTCreditAppRequestFormVo.getId());
//            } else {
//                wxTCreditAppRequestFormVo =  getApplicationDetailById(creditAppQueryPara.getApplicationId());
//            }
//            // 然后返回一份草稿
//            jsonResponse.setDataResult(wxTCreditAppRequestFormVo);
//        } catch (Exception e) {
//            jsonResponse.setErrorMsg("getDraftApplicationForm failed:" + e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }




//    /**
//     * 只是暂存信息
//     * @param wxTCreditApplicationRequestForm
//     * @return
//     */
//    @Override
//    @Transactional
//    public JsonResponse saveApplication(WxTCreditAppRequestFormVo wxTCreditApplicationRequestForm) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//
//            // 如果已经启动了流程就不用
////            if(wxTCreditApplicationRequestForm.getProcessInstanceId() != null){
////                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
////                jsonResponse.setErrorMsg("已经启动流程了，不能再保存");
////                return jsonResponse;
////            }
//            CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditApplicationRequestForm.getCreditType());
//            if(creditAppType == null){
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("creditType不是正确的值");
//                return jsonResponse;
//            }
//
//            Date currentDate = DateUtil.getCurrentDate();
//
//            // 如果没有id就是insert，有id就是update
//            // 查询到当前用户的userId
//            WxTUser user = ContextUtil.getCurUser();
//            // 然后根据creditAppType来查询那一条草稿的信息
////            Map<String, Object> queryMap = new HashMap<String, Object>();
////            queryMap.put("aiPreparedBy", user.getUserId());
////            queryMap.put("creditType", creditAppType);
//            WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo;
//            // 如果现在没有就可以新建一个
//            if(wxTCreditApplicationRequestForm.getId() == null){
//                // 新建一个空的cfi的
//                WxTCreditAppCustomerFinanceInfoVo customerFinanceInfoVo = new WxTCreditAppCustomerFinanceInfoVo();
//                customerFinanceInfoVo.setCreateTime(currentDate);
//                customerFinanceInfoVo.setUpdateTime(currentDate);
//                wxTCreditAppCustomerFinanceInfoVoMapper.insertSelective(customerFinanceInfoVo);
//
//                // 新建一个空的appRequestForm
//                wxTCreditAppRequestFormVo = new WxTCreditAppRequestFormVo();
//                // 设置creditAppType 和 aiPreparedBy
//                wxTCreditAppRequestFormVo.setCreditType(creditAppType.getValue());
//                wxTCreditAppRequestFormVo.setAiPreparedBy(user.getUserId());
//                // 获得一个processNo
//                wxTCreditAppRequestFormVo.setRequestNo(CreditApplicationUtil.getNextProcessNo());
//                wxTCreditAppRequestFormVo.setDeleteFlag(0);
//                // 设置和cfi的对应关系
//                wxTCreditAppRequestFormVo.setCfiInfo(customerFinanceInfoVo);
//                wxTCreditAppRequestFormVo.setCfiInfoId(customerFinanceInfoVo.getId());
//                wxTCreditAppRequestFormVo.setCreateTime(currentDate);
//                wxTCreditAppRequestFormVo.setUpdateTime(currentDate);
//                // 增加设置AiRequestDate
//                wxTCreditAppRequestFormVo.setAiRequestDate(currentDate);
//                wxTCreditAppRequestFormVoMapper.insertSelective(wxTCreditAppRequestFormVo);
//            } else {
//                wxTCreditApplicationRequestForm.setUpdateTime(currentDate);
//                wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(wxTCreditApplicationRequestForm);
//                WxTCreditAppCustomerFinanceInfoVo cfiInfo = wxTCreditApplicationRequestForm.getCfiInfo();
//                if(cfiInfo != null) {
//                    wxTCreditAppCustomerFinanceInfoVoMapper.updateByPrimaryKeySelective(cfiInfo);
//                }
//                wxTCreditAppRequestFormVo = getApplicationDetailById(wxTCreditApplicationRequestForm.getId());
//            }
//            // 然后返回一份草稿
//            jsonResponse.setDataResult(wxTCreditAppRequestFormVo);
//        } catch (Exception e){
//            jsonResponse.setErrorMsg("saveApplication failed:" + e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }


//    /**
//     * 根据applicationId获得application的详细信息
//     * @param applicationId
//     * @return
//     * @throws Exception
//     */
//    private WxTCreditAppRequestFormVo getApplicationDetailById(Long applicationId) throws Exception{
//        WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.selectInfoByPrimaryKey(applicationId);
//        if(wxTCreditAppRequestFormVo != null){
//            WxTCreditAppCustomerFinanceInfoVo customerFinanceInfoVo = null;
//            if(wxTCreditAppRequestFormVo.getCfiInfoId() != null){
//                customerFinanceInfoVo = wxTCreditAppCustomerFinanceInfoVoMapper.selectByPrimaryKey(wxTCreditAppRequestFormVo.getCfiInfoId());
//            }
//            wxTCreditAppRequestFormVo.setCfiInfo(customerFinanceInfoVo);
//            // curTaskId在查询detail的信息里面体现
////            String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
////            if(processInstanceId != null){
////
////                WxTUser user = ContextUtil.getCurUser();
////                String salesChannel = user.getSalesChannel();
////                boolean isCDM = "CDM".equals(salesChannel);
////                // 如果是chevron的user model
////                String cai = user.getCai();
////                boolean isSupervisorAndAbove = false;
////                boolean isSales = false;
////                String checkRoleForSupervisorAndAbove = "";
////                if(StringUtils.isNotBlank(cai)){
////                    Map<String, Object> userInfoParams = new HashMap<String, Object>();
////                    userInfoParams.put("cai", cai);
////                    // 查询当前用户是哪些职务的人,这个是从销售关系的那两个表里面查询出来的
////                    WxTUser chevronUserInfo = wxTUserMapper.getChevronUserInfo(userInfoParams);
////                    if (chevronUserInfo != null) {
////                        if ("BuManager".equals(chevronUserInfo.getUserModel())) {
////                            isSupervisorAndAbove = true;
////                            checkRoleForSupervisorAndAbove = isCDM?"Chevron_Manager":"Chevron_C&I_Admin";
////                        } else if ("ChannelManager".equals(chevronUserInfo.getUserModel())) {
////                            isSupervisorAndAbove = true;
////                            checkRoleForSupervisorAndAbove = isCDM?"Chevron_CDM_Channel_Manager":"Chevron_C&I_Channel_Manager";
////                        } else if ("Supervisor".equals(chevronUserInfo.getUserModel())) {
////                            isSupervisorAndAbove = true;
////                            checkRoleForSupervisorAndAbove = isCDM?"Chevron_CDM_Suppervisor":"Chevron_C&I_Suppervisor";
////                        } else if ("Sales".equals(chevronUserInfo.getUserModel())) {
////                            isSales = true;
////                        }
////                    }
////                }
////                List<String> roleList;
////                if (StringUtils.isNotBlank(checkRoleForSupervisorAndAbove)) {
////                    roleList = new ArrayList<String>();
////                    roleList.add(checkRoleForSupervisorAndAbove);
////                    List<String> userRoleList = getRoleStrList(user);
////                    if(userRoleList.contains("L3_CDM_ABM_Sales_Manager")){
////                    	roleList.add("L3_CDM_ABM_Sales_Manager");
////                    }
////                    if(userRoleList.contains("L3_C&I_ABM_Sales_Manager")){
////                    	roleList.add("L3_C&I_ABM_Sales_Manager");
////                    }
////                } else {
////                    roleList = getRoleStrList(user);
////                }
////
////                List<ActRuTaskVo> unsignedTasks = getActRuTaskVos(wxTCreditAppRequestFormVo.getCreditType(), processInstanceId,roleList);
////                // 获取一下当前app的preparedBy的cai对应的 salesTime的列表，再根据当前的任务
////                if(unsignedTasks!=null && unsignedTasks.size() >0){
////                    wxTCreditAppRequestFormVo.setCurTaskId(unsignedTasks.get(0).getId());
////                }
////            }
//            // 这个是这个app form的所有的count的info
//            wxTCreditAppRequestFormVo.setAttCountInfo(wxTCreditAppRequestFromAttVoMapper.queryAttCount(wxTCreditAppRequestFormVo.getRequestNo()));
////            if(StringUtils.isBlank(wxTCreditAppRequestFormVo.getProcessInstanceId())){
////                wxTCreditAppRequestFormVo.setCreditDollarRate(creditCommonService.getCreditDollarRate());
////            }
//        } else {
//            throw new Exception("No wxTCreditAppRequestFormVo found");
//        }
//        return wxTCreditAppRequestFormVo;
//    }

//    /**
//     * 根据当前用户的groupList和processInstanceId获取下一个待办的列表
//     * @param creditType
//     * @param processInstanceId
//     * @return
//     */
//    @Override
//    public List<ActRuTaskVo> getActRuTaskVos(String creditType, String processInstanceId,List<String> groupIdList) {
//        List<String> processKeys = new ArrayList<String>();
//        processKeys.add(CreditAppType.valueOf(creditType).getProcessKey());
//        Map<String,Object> queryTodoMap = new HashMap<String, Object>();
//        queryTodoMap.put("processKeys",processKeys);
//        queryTodoMap.put("processInstanceId",processInstanceId);
//        queryTodoMap.put("groupIdList",groupIdList);
//        return actRuTaskVoMapper.queryTodoTaskByGroupIds(queryTodoMap);
//    }

//    private Task getNextRuTaskVo(String creditType, String processInstanceId,List<String> groupIdList) {
//        Task nextTask = null;
//        List<ActRuTaskVo> actRuTaskVos = getActRuTaskVos(creditType,processInstanceId,groupIdList);
//        if(actRuTaskVos!=null && actRuTaskVos.size() >0){
//            ActRuTaskVo actRuTaskVo = actRuTaskVos.get(0);
//             nextTask = taskService.createTaskQuery().taskId(actRuTaskVo.getId()).singleResult();
//        }
//        return nextTask;
//    }

//    @Override
//    @Transactional
//    public boolean postCompleteHandler(Long applicationId,String comment) {
//        Boolean handled = false;
////        List<Long> backupUserIds = new ArrayList<Long>();
//        try {
//            WxTUser curUser = ContextUtil.getCurUser();
//            // step1、先判断当前的用户拥有角色是否需要判断absent
////            String roleListStr = getRoleListStr(curUser);
////            boolean needCheckAbsent = false;
////            for(String checkRole:CreditApplicationUtil.NEED_CHECK_ABSENT_ROLES) {
////                if(roleListStr.contains(checkRole + ",")) {
////                    needCheckAbsent = true;
////                }
////            }
////            if(!needCheckAbsent) {
////                return handled;
////            }
//            WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = getApplicationDetailById(applicationId);
//            ProcessInfo processInfo = new ProcessInfo();
//            processInfo.setApprove(true);
//            processInfo.setComment("auto");
//            wxTCreditAppRequestFormVo.setProcessInfo(processInfo);
//            Task nextTask = getNextRuTaskVo(wxTCreditAppRequestFormVo.getCreditType(),wxTCreditAppRequestFormVo.getProcessInstanceId(),null);
//            // --------------------------
//            // 处理absent,获得下一个节点的信息
////            boolean isAbsentHandled = false;
//            Map<String, Object> completeTaskVariables;
//            Map<String, Long> approvalTeamMap = getApprovalTeamIdsMap(wxTCreditAppRequestFormVo);
//            Map<String, Double> refValueMap = queryCreditLimitRefValueMap();
//            String aiRequestedByCai = wxTCreditAppRequestFormVo.getAiRequestedBy();
//            String[] frontBrackets = wxTCreditAppRequestFormVo.getAiRequestedBy().split("\\(");
//            if (frontBrackets.length >= 2) {
//                String[] endBrackets = frontBrackets[frontBrackets.length - 1].split("\\)");
//                if (endBrackets.length > 0) {
//                    aiRequestedByCai = endBrackets[0];
//                }
//            }
//            String salesChannel = wxTCreditAppRequestFormVoMapper.querySalesChannelByUserId(aiRequestedByCai);
//            boolean isCDM = "CDM".equals(salesChannel);
//            CreditAppType creditAppType = CreditAppType.valueOf(wxTCreditAppRequestFormVo.getCreditType());
//
//            // 如果根据需要检查的role，得到下一个task是有需要check的Task
////            List<ActRuTaskVo> nextTasks = getActRuTaskVos(wxTCreditAppRequestFormVo.getCreditType(),
////                    wxTCreditAppRequestFormVo.getProcessInstanceId(), Arrays.asList(CreditApplicationUtil.NEED_CHECK_ABSENT_ROLES));
//            // 获得一个roleListStr的string，来判断用户是否包含某个role
//            boolean curIsCreditTeamLead = userIsCreditTeamLead(curUser);
//            boolean curIsChinaFinanceManager = userIsChinaFinanceManager(curUser);
//            if(nextTask!=null) {
//                String nextTaskName = nextTask.getName();
//               if( (curIsCreditTeamLead && CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK.equals(nextTaskName))
//                       || (curIsChinaFinanceManager && CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK.equals(nextTaskName))) {
//                    boolean auto = isAuto(curUser, nextTaskName, approvalTeamMap);
//                    // 如果本人也是inCharge的，auto就是true
//                    completeTaskVariables = getCompleteTaskVarMap(wxTCreditAppRequestFormVo, creditAppType, isCDM, refValueMap.get(nextTaskName), auto, comment);
//
//                    // 如果审批了还需要"Local_Credit_Team_Lead"审批的，那就直接审批通过，等待"Local_Credit_Team_Lead"继续审批
//                    taskService.claim(nextTask.getId(), String.valueOf(curUser.getUserId()));
//                    // 然后再complete
//                    taskService.setVariablesLocal(nextTask.getId(), completeTaskVariables);
//                    taskService.complete(nextTask.getId(), completeTaskVariables);
//                }
//            }
//
//            // 然后处理absent的事情
//            nextTask = getNextRuTaskVo(wxTCreditAppRequestFormVo.getCreditType(),
//                    wxTCreditAppRequestFormVo.getProcessInstanceId(),
//                    Arrays.asList(CreditApplicationUtil.NEED_CHECK_ABSENT_ROLES));
//            if (nextTask!=null && StringUtils.isNotBlank(nextTask.getName())) {
////                ActRuTaskVo nextTask = nextTasks.get(0);
//                Double finalRequestCreditLimit = wxTCreditAppRequestFormVo.getFinalRequestCreditLimit(creditAppType, getCreditDollarRate(wxTCreditAppRequestFormVo));
//                String taskName = nextTask.getName();
//                List<WxTCreditAppUserAbsentInfoVo> absentInfoVos = getAbsentInfoByTaskName(taskName);
//
//                // 这里计算一下下一个任务是不是auto
//                boolean auto = isAuto(curUser, taskName, approvalTeamMap);
//
//                // 找到这一步需要的userId
//                if (absentInfoVos != null && absentInfoVos.size() > 0) {
//                    // 对于是absent的userId + nextTaskName来判断做相应的操作
//                    Long autoUserId = curIsCreditTeamLead? curUser.getUserId(): absentInfoVos.get(0).getUserId();
//                    String nextTaskName = nextTask.getName();
//                    if (CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK.equals(nextTaskName)) {
//                        // 这一步的话，如果> refValue，则这一步是自动的approved
//                        if (finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK)) {
//                            // 如果本人也是inCharge的，auto就是true
//                            completeTaskVariables = getCompleteTaskVarMap(wxTCreditAppRequestFormVo, creditAppType, isCDM, refValueMap.get(taskName), auto, "auto");
//
//                            // 如果审批了还需要"Local_Credit_Team_Lead"审批的，那就直接审批通过，等待"Local_Credit_Team_Lead"继续审批
//                            taskService.claim(nextTask.getId(), String.valueOf(autoUserId));
//                            // 然后再complete
//                            taskService.setVariablesLocal(nextTask.getId(), completeTaskVariables);
//                            taskService.complete(nextTask.getId(), completeTaskVariables);
//                        }
//                    } else if (CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK.equals(nextTaskName)) {
////                            isAbsentHandled = true;
//                        // 如果是只需要Local_Credit_Team_Lead审批的，
//                        // 审批完就结束的那种那就 把"China_Finance_Manager"加入到CandidateGroup
//                        if (finalRequestCreditLimit <= refValueMap.get(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK) && !curIsCreditTeamLead) {
////                            （这个的处理已经改到回调函数了）
////                            taskService.addCandidateGroup(nextTask.getId(), "China_Finance_Manager");
////                            backupUserIds.add(approvalTeamMap.get(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK));
//                        } else {
//                            // 如果本人也是inCharge的，auto就是true
//                            completeTaskVariables = getCompleteTaskVarMap(wxTCreditAppRequestFormVo, creditAppType, isCDM, refValueMap.get(taskName), auto, "auto");
//
//                            // 如果审批了还需要"Local_Credit_Team_Lead"审批的，那就直接审批通过，等待"Local_Credit_Team_Lead"继续审批
//                            taskService.claim(nextTask.getId(), String.valueOf(autoUserId));
//                            // 然后再complete
//                            taskService.setVariablesLocal(nextTask.getId(), completeTaskVariables);
//                            taskService.complete(nextTask.getId(), completeTaskVariables);
//                        }
//                    } else if (CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK.equals(nextTaskName)) {
//                        //如果下一步absent的步骤是CHINA_FINANCE_MANAGER,当前的操作人是Local_Credit_Team_Lead，则
//                        if (curIsCreditTeamLead) {
//                            // 如果本人就是creditTeamLeader
//                            completeTaskVariables = getCompleteTaskVarMap(wxTCreditAppRequestFormVo, creditAppType, isCDM, refValueMap.get(taskName), auto, "auto");
//
//                            // 如果审批了还需要"Local_Credit_Team_Lead"审批的，那就直接审批通过，执行人也是记录China_Finance_Manager
//                            taskService.claim(nextTask.getId(),String.valueOf(approvalTeamMap.get(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK)));
//                            // 然后再complete
//                            taskService.setVariablesLocal(nextTask.getId(), completeTaskVariables);
//                            taskService.complete(nextTask.getId(), completeTaskVariables);
//                        }
//                        else {
//                            // 本人不是的话，就是把Local_Credit_Team_Lead加入到candidateGroup里面（这个的处理已经改到回调函数了）
////                            taskService.addCandidateGroup(nextTask.getId(), "Local_Credit_Team_Lead");
////                            backupUserIds.add(approvalTeamMap.get(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK));
//                        }
//                    } else if (CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK.equals(nextTaskName)) {
//                        // 如果是只需要AP_CREDIT_TEAM_GRACE审批的，把AP_Credit_Team_backup作为backup来审批（这个的处理已经改到回调函数了）
////                        taskService.addCandidateGroup(nextTask.getId(), "AP_Credit_Team_backup");
////                        backupUserIds.add(approvalTeamMap.get(CreditApplicationUtil.AP_CREDIT_TEAM_GRACE_BACKUP));
//                    }
//                }
//            }
////            }
//            handled = true;
//            // --------------absent处理完成--------------
//        } catch (Exception e) {
//            logger.error("postCompleteHandler failed", e);
//
//        }
//
//        return handled;
//    }
//
//    /**
//     * 查询这个任务对于的role，然后查询absentInfoVos
//     * @param taskName 任务的名称
//     * @return
//     */
//    @Override
//    public List<WxTCreditAppUserAbsentInfoVo> getAbsentInfoByTaskName(String taskName) {
//        // 查询这个任务对于的role，然后查询absentInfoVos
//        String needCheckAbsentRoleChRoleName = CreditApplicationUtil.NEED_CHECK_ABSENT_ROLES_AND_STEPS.get(taskName);
//        Map<String, Object> queryAbsentInfoMap = new HashMap<String, Object>();
//        queryAbsentInfoMap.put("roleName", needCheckAbsentRoleChRoleName);
//        Calendar calendar = Calendar.getInstance();
//        calendar.set(Calendar.MILLISECOND, 0);
//        calendar.set(Calendar.SECOND, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        Date currentDate = calendar.getTime();
//        queryAbsentInfoMap.put("currentDate", currentDate);
//        List<WxTCreditAppUserAbsentInfoVo> absentInfoVoList = wxTCreditAppUserAbsentInfoVoMapper.queryAbsentInfoByRole(queryAbsentInfoMap);
//        return absentInfoVoList;
//    }
//
//    private boolean userIsCreditTeamLead(WxTUser curUser) {
//        String roleListStr = getRoleListStr(curUser);
//
//        // 如果是Local_Credit_Team_Lead并且下一个节点是LOCAL_CREDIT_TEAM_LEADER，先complete
//        return roleListStr.indexOf("Local_Credit_Team_Lead,") != -1;
//    }
//
//    private String getRoleListStr(WxTUser curUser) {
//        StringBuffer roleListSb = new StringBuffer("");
//        List<WxTRole> wxTRoleList = curUser.getRoleList();
//        for (WxTRole wxTRole : wxTRoleList) {
//            roleListSb.append(wxTRole.getChRoleName());
//            roleListSb.append(",");
//        }
//        return roleListSb.toString();
//    }

//    private boolean userIsChinaFinanceManager(WxTUser curUser) {
//        String roleListStr = getRoleListStr(curUser);
//
//        // 如果是Local_Credit_Team_Lead并且下一个节点是LOCAL_CREDIT_TEAM_LEADER，先complete
//        return roleListStr.indexOf("China_Finance_Manager,") != -1;
//    }

//    /**
//     * 点击进入列表之后查询的这个application的detail的信息
//     * @param creditAppQueryPara
//     * @return
//     */
//    @Override
//    public JsonResponse getApplicationDetailById(CreditAppQueryPara creditAppQueryPara) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try{
//            if(creditAppQueryPara.getApplicationId() == null){
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("applicationId不能为空");
//                return jsonResponse;
//            }
//            WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = getApplicationDetailById(creditAppQueryPara.getApplicationId());
//            jsonResponse.setDataResult(wxTCreditAppRequestFormVo);
//        } catch (Exception e){
//            jsonResponse.setErrorMsg("getApplicationDetailById failed:" + e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//
//        return jsonResponse;
//    }
//
//    /**
//     * 根据不同的
//     * @param wxTCreditAppRequestFormVo
//     * @return
//     */
//    @Override
//    @Transactional
//    public JsonResponse startWorkflow(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//            if(wxTCreditAppRequestFormVo.getProcessInstanceId() != null){
//                ProcessInfo processInfo = new ProcessInfo();
//                processInfo.setApprove(true);
//                wxTCreditAppRequestFormVo.setProcessInfo(processInfo);
//                jsonResponse = completeWorkflowNode(wxTCreditAppRequestFormVo);
//                return jsonResponse;
//            }
//            if(wxTCreditAppRequestFormVo.getCreditType() == null){
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("creditType不能为空");
//                return jsonResponse;
//            }
//
//            // 如果id为空的话直接save一下
//            if(wxTCreditAppRequestFormVo.getId() == null){
//                JsonResponse saveResult = saveApplication(wxTCreditAppRequestFormVo);
//                if(!ResponseStatus.SUCCESS.getCode().equals(saveResult.get(JsonResponse.KEY_CODE))){
//                    return jsonResponse;
//                }
//                wxTCreditAppRequestFormVo = (WxTCreditAppRequestFormVo) saveResult.get(JsonResponse.KEY_DATA);
//            }
//
//            // 根据前端传过来的creditType获得processKey
//            CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditAppRequestFormVo.getCreditType());
//            if(creditAppType == CreditAppType.ANNUAL_CREDIT_REVIEW){
//                if(wxTCreditAppRequestFormVo.getCbiRequestedCreditLimitCurrentYear() == null){
//                    jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                    jsonResponse.setErrorMsg("ANNUAL_CREDIT_REVIEW的请求cbiRequestedCreditLimitCurrentYear不能为空");
//                    return jsonResponse;
//                }
//            } else if(creditAppType == CreditAppType.TEMP_CREDIT_REQUEST){
//                if(wxTCreditAppRequestFormVo.getCbiRequestedTempCreditLimit() == null){
//                    jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                    jsonResponse.setErrorMsg("TEMP_CREDIT_REQUEST的请求cbiRequestedTempCreditLimit不能为空");
//                    return jsonResponse;
//                }
//            }
//
//            // 如果是CV的
//            if(creditAppType == CreditAppType.CV_REQUEST) {
//                // 如果没有financial statement的文件里面没有excel
//                if(!checkHasFsExcelAtt(wxTCreditAppRequestFormVo) && "Cash in Advance".equalsIgnoreCase(wxTCreditAppRequestFormVo.getCbiPaymentTermOfYearN1())){
//                    jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                    /*jsonResponse.setErrorMsg("CV_credit_request的请求,如果前一年的payment term还是Cash in Advance,则需要上传一个Financial Statements的Excel类型的附件");
//                    */
//                    jsonResponse.setErrorMsg("需要上传一个Financial Statements的Excel类型的附件");                  
//                    return jsonResponse;
//                }
//            }
//
//            String processKey = creditAppType.getProcessKey();
//            // 启动流程时设置业务Key
//            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processKey,
//                    String.valueOf(wxTCreditAppRequestFormVo.getId()), null);
//            // 获得这个processInstance的Id，放到wxTCreditAppRequestFormVo里面，更新保存一下
//            String processDefinitionId = processInstance.getId();
//            // 这个时候就是running的状态
//            wxTCreditAppRequestFormVo.setProcessStatus(ProcessStatusType.RUNNING.getValue());
//            wxTCreditAppRequestFormVo.setProcessInstanceId(processDefinitionId);
//            wxTCreditAppRequestFormVo.setCreditDollarRate(creditCommonService.getCreditDollarRate());
//            wxTCreditAppRequestFormVo.setUpdateTime(DateUtil.getCurrentDate());
//            wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo);
//            // 查询到这个这个process的任务然后，claim + complete
//            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//            taskService.claim(task.getId(),String.valueOf(wxTCreditAppRequestFormVo.getAiPreparedBy()));
//            Map<String, Object> taskVariables = new HashMap<String, Object>();
//            taskVariables.put("comment",wxTCreditAppRequestFormVo.getProcessInfo()!=null?wxTCreditAppRequestFormVo.getProcessInfo().getComment():null);
//            taskVariables.put("approve",true);
//            taskVariables.put("creditLimit",wxTCreditAppRequestFormVo.getFinalRequestCreditLimit(creditAppType,getCreditDollarRate(wxTCreditAppRequestFormVo)));
//            taskService.setVariablesLocal(task.getId(),taskVariables);
//            taskService.complete(task.getId(), taskVariables);
//
//            final WxTCreditAppRequestFormVo latestWxTCreditAppRequestFormVo = getApplicationDetailById(wxTCreditAppRequestFormVo.getId());
//            jsonResponse.setDataResult(latestWxTCreditAppRequestFormVo);
//
//            new Thread(new Runnable() {
//
//                @Override
//                public void run() {
//                    sendMail(latestWxTCreditAppRequestFormVo,true, CreditApplicationUtil.SUBMIT_REQUEST);
//                }
//
//            }).start();
//        } catch (Exception e) {
//            e.printStackTrace();
//            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//            jsonResponse.setErrorMsg("startWorkflow failed:" + e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }

//    /**
//     *
//     *@User   :Test
//     *@date   :2014-6-27 上午09:38:36
//     *@return :Set
//     *@userFor :获得任务中的办理候选人
//     */
//    private Set getTaskCandidate(String taskId) {
//        Set users = new HashSet();
//        List identityLinkList = taskService.getIdentityLinksForTask(taskId);
//        if (identityLinkList != null && identityLinkList.size() > 0) {
//            for (Iterator iterator = identityLinkList.iterator(); iterator
//                    .hasNext();) {
//                IdentityLink identityLink = (IdentityLink) iterator.next();
//                if (identityLink.getUserId() != null) {
//                    User user = getUser(identityLink.getUserId());
//                    if (user != null)
//                        users.add(user);
//                }
//                if (identityLink.getGroupId() != null) {
//                    // 根据组获得对应人员
//                    List userList = identityService.createUserQuery()
//                            .memberOfGroup(identityLink.getGroupId()).list();
//                    if (userList != null && userList.size() > 0)
//                        users.addAll(userList);
//                }
//            }
//
//        }
//        return users;
//    }
//
//    private User getUser(String userId) {
//        User user = (User) identityService.createUserQuery().userId(userId)
//                .singleResult();
//        return user;
//    }
//
//    /**
//     * 获取下一个用户任务信息
//     * @param  taskId     任务Id信息
//     * @return  下一个用户任务用户组信息
//     * @throws Exception
//     */
//    public TaskDefinition getNextTaskInfo(String taskId) throws Exception {
//        ProcessDefinitionEntity processDefinitionEntity = null;
//        String id = null;
//        TaskDefinition task = null;
//        //获取流程实例Id信息
//        String processInstanceId = taskService.createTaskQuery().taskId(taskId).singleResult().getProcessInstanceId();
//        //获取流程发布Id信息
//        String definitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();
//        processDefinitionEntity = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
//                .getDeployedProcessDefinition(definitionId);
//        ExecutionEntity execution = (ExecutionEntity) runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
//        //当前流程节点Id信息
//        String activitiId = execution.getActivityId();
//        //获取流程所有节点信息
//        List<ActivityImpl> activitiList = processDefinitionEntity.getActivities();
//
//        //遍历所有节点信息
//        for (ActivityImpl activityImpl : activitiList) {
//            id = activityImpl.getId();
//            if (activitiId.equals(id)) {
//                //获取下一个节点信息
//                task = nextTaskDefinition(activityImpl, activityImpl.getId(), null, processInstanceId);
//                break;
//            }
//        }
//        return task;
//    }
//
//    /**
//     * 下一个任务节点信息,
//     *
//     * 如果下一个节点为用户任务则直接返回,
//     *
//     * 如果下一个节点为排他网关, 获取排他网关Id信息, 根据排他网关Id信息和execution获取流程实例排他网关Id为key的变量值,
//     * 根据变量值分别执行排他网关后线路中的el表达式, 并找到el表达式通过的线路后的用户任务
//     * @param  activityImpl     流程节点信息
//     * @param activityId             当前流程节点Id信息
//     * @param elString               排他网关顺序流线段判断条件
//     * @param processInstanceId      流程实例Id信息
//     * @return
//     */
//    private TaskDefinition nextTaskDefinition(ActivityImpl activityImpl, String activityId, String elString, String processInstanceId){
//
//        PvmActivity ac = null;
//
//        Object s = null;
//
//        // 如果遍历节点为用户任务并且节点不是当前节点信息
//        if ("userTask".equals(activityImpl.getProperty("type")) && !activityId.equals(activityImpl.getId())) {
//            // 获取该节点下一个节点信息
//            TaskDefinition taskDefinition = ((UserTaskActivityBehavior) activityImpl.getActivityBehavior())
//                    .getTaskDefinition();
//            return taskDefinition;
//        } else {
//            // 获取节点所有流向线路信息
//            List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
//            List<PvmTransition> outTransitionsTemp = null;
//            for (PvmTransition tr : outTransitions) {
//                ac = tr.getDestination(); // 获取线路的终点节点
//                // 如果流向线路为排他网关
//                if ("exclusiveGateway".equals(ac.getProperty("type"))) {
//                    outTransitionsTemp = ac.getOutgoingTransitions();
//
//                    // 如果网关路线判断条件为空信息
//                    if (StringUtils.isEmpty(elString)) {
//                        // 获取流程启动时设置的网关判断条件信息
//                        elString = getGatewayCondition(ac.getId(), processInstanceId);
//                    }
//
//                    // 如果排他网关只有一条线路信息
//                    if (outTransitionsTemp.size() == 1) {
//                        return nextTaskDefinition((ActivityImpl) outTransitionsTemp.get(0).getDestination(), activityId,
//                                elString, processInstanceId);
//                    } else if (outTransitionsTemp.size() > 1) { // 如果排他网关有多条线路信息
//                        for (PvmTransition tr1 : outTransitionsTemp) {
//                            s = tr1.getProperty("conditionText"); // 获取排他网关线路判断条件信息
//                            // 判断el表达式是否成立
//                            if (isCondition(ac.getId(), StringUtils.trim(s.toString()), elString)) {
//                                return nextTaskDefinition((ActivityImpl) tr1.getDestination(), activityId, elString,
//                                        processInstanceId);
//                            }
//                        }
//                    }
//                } else if ("userTask".equals(ac.getProperty("type"))) {
//                    return ((UserTaskActivityBehavior) ((ActivityImpl) ac).getActivityBehavior()).getTaskDefinition();
//                } else {
//                }
//            }
//            return null;
//        }
//    }
//
//    /**
//     * 查询流程启动时设置排他网关判断条件信息
//     * @param  gatewayId          排他网关Id信息, 流程启动时设置网关路线判断条件key为网关Id信息
//     * @param  processInstanceId  流程实例Id信息
//     * @return
//     */
//    public String getGatewayCondition(String gatewayId, String processInstanceId) {
//        Execution execution = runtimeService.createExecutionQuery().processInstanceId(processInstanceId).singleResult();
//        Object object= runtimeService.getVariable(execution.getId(), gatewayId);
//        return object==null? "":object.toString();
//    }
//
//    /**
//     * 根据key和value判断el表达式是否通过信息
//     * @param  key    el表达式key信息
//     * @param  el     el表达式信息
//     * @param  value  el表达式传入值信息
//     * @return
//     */
//    public boolean isCondition(String key, String el, String value) {
//        ExpressionFactory factory = new ExpressionFactoryImpl();
//        SimpleContext context = new SimpleContext();
//        context.setVariable(key, factory.createValueExpression(value, String.class));
//        ValueExpression e = factory.createValueExpression(context, el, boolean.class);
//        return (Boolean) e.getValue(context);
//    }

//    /**
//     * 完成这个节点
//     * @param wxTCreditAppRequestFormVo form的内容
//     * @return
//     */
//    @Override
//    @Transactional
//    public JsonResponse completeWorkflowNode(final WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//            if(wxTCreditAppRequestFormVo.getCreditType() == null){
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("creditType不能为空");
//                return jsonResponse;
//            }
//            if(wxTCreditAppRequestFormVo.getId() == null){
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("id不能为空");
//                return jsonResponse;
//            }
//            if(wxTCreditAppRequestFormVo.getCurTaskId() == null) {
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("curTaskId不能为空");
//                return jsonResponse;
//            }
//            if(wxTCreditAppRequestFormVo.getProcessInfo() == null || wxTCreditAppRequestFormVo.getProcessInfo().getApprove() == null) {
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("processInfo不能为空，并且approve一定要有值");
//                return jsonResponse;
//            }
//            CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditAppRequestFormVo.getCreditType());
//            if(creditAppType == CreditAppType.ANNUAL_CREDIT_REVIEW){
//                if(wxTCreditAppRequestFormVo.getCbiRequestedCreditLimitCurrentYear() == null){
//                    jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                    jsonResponse.setErrorMsg("ANNUAL_CREDIT_REVIEW的请求cbiRequestedCreditLimitCurrentYear不能为空");
//                    return jsonResponse;
//                }
//            } else if(creditAppType == CreditAppType.TEMP_CREDIT_REQUEST){
//                if(wxTCreditAppRequestFormVo.getCbiRequestedTempCreditLimit() == null){
//                    jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                    jsonResponse.setErrorMsg("TEMP_CREDIT_REQUEST的请求cbiRequestedTempCreditLimit不能为空");
//                    return jsonResponse;
//                }
//            }
//
//            WxTUser user = ContextUtil.getCurUser();
//            // 增加查询当前的用户是creditTeamLead
//            boolean curIsCreditTeamLead = userIsCreditTeamLead(user);
//            WxTCreditAppRequestFormVo oriWxTCreditAppRequestFormVo = getApplicationDetailById(wxTCreditAppRequestFormVo.getId());
//            String aiRequestedByCai = oriWxTCreditAppRequestFormVo.getAiRequestedBy();
//            String[] frontBrackets = oriWxTCreditAppRequestFormVo.getAiRequestedBy().split("\\(");
//            if(frontBrackets.length >=2){
//                String[] endBrackets = frontBrackets[frontBrackets.length-1].split("\\)");
//                if(endBrackets.length > 0){
//                    aiRequestedByCai = endBrackets[0];
//                }
//            }
//            String salesChannel = wxTCreditAppRequestFormVoMapper.querySalesChannelByUserId(aiRequestedByCai);
//            boolean isCDM = "CDM".equals(salesChannel);
//            boolean approveParam = wxTCreditAppRequestFormVo.getProcessInfo().getApprove();
//            Long idParam = wxTCreditAppRequestFormVo.getId();
//            // 获取一下当前任务的id,根据名称找到对应的refValue值,如果不存在也就是不用比较的
//            ActRuTaskVo curActRuTaskVo = actRuTaskVoMapper.selectByPrimaryKey(wxTCreditAppRequestFormVo.getCurTaskId());
//            final String curTaskName = curActRuTaskVo.getName();
//            Map<String,Double> refValueMap = queryCreditLimitRefValueMap();
//            Double refValue = refValueMap.get(curTaskName);
//
//            Map<String, Long> approvalTeamMap = getApprovalTeamIdsMap(wxTCreditAppRequestFormVo);
//
//            // 修改form里面的updateTime的值的更新时间
//            wxTCreditAppRequestFormVo.setUpdateTime(DateUtil.getCurrentDate());
//            wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo);
//            if(wxTCreditAppRequestFormVo.getCfiInfo() != null && wxTCreditAppRequestFormVo.getCfiInfo().getId() != null){
//                wxTCreditAppRequestFormVo.getCfiInfo().setUpdateTime(DateUtil.getCurrentDate());
//                wxTCreditAppCustomerFinanceInfoVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo.getCfiInfo());
//            }
////            String processKey = creditAppType.getProcessKey();
//            // 先claim这一条
//            // 如果是LOCAL_CREDIT_ANALYST_CHECK_TASK,需要特殊处理一下PCA-135
//            String exactExecutorId = String.valueOf(approvalTeamMap.get(curTaskName));
//            boolean auto = isAuto(user, curTaskName, approvalTeamMap);
//
//            // 对于AP_CREDIT_TEAM_GRACE_TASK的要特殊处理一下
//            if(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK.equals(curTaskName) ){
//                exactExecutorId =  String.valueOf(ContextUtil.getCurUserId());
//                auto = false;
//            } 
//            else if(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK.equals(curTaskName))
//            {
//                if(curIsCreditTeamLead){
//                    exactExecutorId = String.valueOf(user.getUserId());
//                    auto = false;
//                }
//            }
//            taskService.claim(wxTCreditAppRequestFormVo.getCurTaskId(), exactExecutorId);
//
//            // 这里计算一下auto,当前用户的id和应该审批的那个用户的id不相同的时候才是auto
//            Long inChargeUserId;
//            // 如果本人也是inCharge的，auto就是true
//            Map<String, Object> completeTaskVariables = getCompleteTaskVarMap(wxTCreditAppRequestFormVo, creditAppType, isCDM, refValue, auto, wxTCreditAppRequestFormVo.getProcessInfo().getComment());
//            // 然后再complete
//            taskService.setVariablesLocal(wxTCreditAppRequestFormVo.getCurTaskId(),completeTaskVariables);
//            taskService.complete(wxTCreditAppRequestFormVo.getCurTaskId(),completeTaskVariables);
//
////            List<Long> backupUserIdList = null;
//            if(wxTCreditAppRequestFormVo.getProcessInfo().getApprove()){
//                postCompleteHandler(wxTCreditAppRequestFormVo.getId(), wxTCreditAppRequestFormVo.getProcessInfo().getComment());
//            }
////            final List<Long> backupUserIds = backupUserIdList;
//
//
//            // 到最后了还需要查询一下这个process是否流程已经走完，然后更新一下状态
//            List<ActRuTaskVo> unsignedTasks = getActRuTaskVos(wxTCreditAppRequestFormVo.getCreditType(),wxTCreditAppRequestFormVo.getProcessInstanceId(),null);
//            //通过对比任务节点和历史节点 如果任务节点和历史节点是一个  则认为已经完成
//            CreditListQueryParam historyParam = new CreditListQueryParam();
//            historyParam.setApplicationId(wxTCreditAppRequestFormVo.getId());
//            List<FormApprovalInfo> approvelHistory = (List<FormApprovalInfo>)queryApprovalHistory(historyParam).get("resultLst");
//            CreditAppQueryPara creditAppQueryPara = new CreditAppQueryPara();
//            creditAppQueryPara.setApplicationId(wxTCreditAppRequestFormVo.getId());
//            List<WorkflowNode> workflowNodeList = (List<WorkflowNode>)queryWorkflowNodes(creditAppQueryPara).get("resultLst");
//            
//            
//            
//            WxTCreditAppRequestFormVo updateStatusVo = new WxTCreditAppRequestFormVo();
//            updateStatusVo.setId(idParam);
//            /*if(unsignedTasks != null && unsignedTasks.size() >0 ){
//                String nextTaskName = unsignedTasks.get(0).getName();
//                // 如果后续的任务，并且这一节是approved
//                if(approveParam && !nextTaskName.equals(CreditApplicationUtil.ADJUST_VALUE)) {
//                    updateStatusVo.setProcessStatus(ProcessStatusType.RUNNING.getValue());
//                } else {
//                    updateStatusVo.setProcessStatus(ProcessStatusType.REJECTING.getValue());
//                }
//            } else {
//                if(approveParam){
//                    updateStatusVo.setProcessStatus(ProcessStatusType.APPROVED.getValue());
//                } else {
//                    updateStatusVo.setProcessStatus(ProcessStatusType.REJECTED.getValue());
//                }
//            }*/
//            
//            if(approvelHistory != null && approvelHistory.size() > 0 
//            		&& workflowNodeList != null && !workflowNodeList.isEmpty()){
//            	FormApprovalInfo latestedApprove = approvelHistory.get(0);
//            	if(StringUtils.isEmpty(latestedApprove.getExecutionId())){
//            		latestedApprove = approvelHistory.get(1);
//            	}
//            	int workflowNodeListSize = workflowNodeList.size();
//            	WorkflowNode lastNode = workflowNodeList.get(workflowNodeListSize -1);
//            	if(latestedApprove.getStepName().equals(lastNode.getNodeName())){
//            		 if(approveParam){
//            			//CV poppy/rachel是第二步就还是running  
//            			 if(wxTCreditAppRequestFormVo.getCreditType().equals("CV_REQUEST")
//            					 && latestedApprove.getStepName().equals(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK)
//            					 && unsignedTasks != null && unsignedTasks.size() >0 ){
//            				 updateStatusVo.setProcessStatus(ProcessStatusType.RUNNING.getValue());
//            			 }else{
//                         updateStatusVo.setProcessStatus(ProcessStatusType.APPROVED.getValue());
//            			 }
//                     } else {
//                         updateStatusVo.setProcessStatus(ProcessStatusType.REJECTED.getValue());
//                     }
//            	}else{
//            		if(approveParam) {
//                        updateStatusVo.setProcessStatus(ProcessStatusType.RUNNING.getValue());
//                    } else {
//                        updateStatusVo.setProcessStatus(ProcessStatusType.REJECTING.getValue());
//                    }
//            	}
//            }else{
//            	if(approveParam) {
//                    updateStatusVo.setProcessStatus(ProcessStatusType.RUNNING.getValue());
//                } else {
//                    updateStatusVo.setProcessStatus(ProcessStatusType.REJECTING.getValue());
//                }
//            }
//            // 更新一下状态
//            updateStatusVo.setUpdateTime(DateUtil.getCurrentDate());
//            wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(updateStatusVo);
//
//            final WxTCreditAppRequestFormVo latestWxTCreditAppRequestFormVo = getApplicationDetailById(wxTCreditAppRequestFormVo.getId());
//            jsonResponse.setDataResult(latestWxTCreditAppRequestFormVo);
//
//            new Thread(new Runnable() {
//                @Override
//                public void run() {
//                    sendMail(latestWxTCreditAppRequestFormVo,wxTCreditAppRequestFormVo.getProcessInfo().getApprove(), curTaskName);
//                }
//            }).start();
//        } catch (Exception e){
//            e.printStackTrace();
//            jsonResponse.setErrorMsg(e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//
////        List<IdentityLink> idLinks = taskService.getIdentityLinksForTask(processKey);
//        return jsonResponse;
//    }
//
//    private boolean isAuto(WxTUser user, String curTaskName, Map<String, Long> approvalTeamMap) {
//        Long inChargeUserId = approvalTeamMap.get(curTaskName);
//        boolean auto = false;
//        if(inChargeUserId!=null && !inChargeUserId.equals(user.getUserId())){
//            auto = true;
//        }
//        return auto;
//    }
//
//    private Map<String, Object> getCompleteTaskVarMap(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo, CreditAppType creditAppType, boolean isCDM, Double refValue, boolean auto,String comment) {
//        Map<String, Object> taskVariables = new HashMap<String, Object>();
//        taskVariables.put("comment",auto?"":comment);
//        taskVariables.put("approve",wxTCreditAppRequestFormVo.getProcessInfo().getApprove());
//        taskVariables.put("creditLimit",wxTCreditAppRequestFormVo.getFinalRequestCreditLimit(creditAppType,getCreditDollarRate(wxTCreditAppRequestFormVo)));
//        taskVariables.put("isCdm",isCDM);
//        taskVariables.put("refValue",refValue);
//        taskVariables.put("auto",auto);
//        taskVariables.put("actualExecutorId", ContextUtil.getCurUserId());
//        return taskVariables;
//    }

//    /**
//     * 如果是CV的
//     * @return 返回是否有问题
//     */
//    private boolean checkHasFsExcelAtt(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo) {
//        boolean foundExcelFile = false;
//        CreditListQueryParam creditListQueryParam = new CreditListQueryParam();
//        creditListQueryParam.setApplicationId(wxTCreditAppRequestFormVo.getId());
//        creditListQueryParam.setAttColumnName("cbiFinancialStatementsAttId");
//        JsonResponse jsonResponse = queryAttListByAttColumnName(creditListQueryParam);
//        if (ResponseStatus.SUCCESS.getCode().equals(jsonResponse.get(JsonResponse.KEY_CODE))) {
//            List<CreditAppAttVo> creditAppAttVoList = (List<CreditAppAttVo>) jsonResponse.get(JsonResponse.KEY_LIST);
//            if (creditAppAttVoList != null && creditAppAttVoList.size() > 0) {
//                for (CreditAppAttVo creditAppAttVo : creditAppAttVoList) {
//                    String fileName = creditAppAttVo.getFileName();
//                    if (fileName.endsWith(CreditFinancialReportController.SUFFIX_XLS)
//                            || fileName.endsWith(CreditFinancialReportController.SUFFIX_XLSX)
//                            || fileName.endsWith(CreditFinancialReportController.SUFFIX_XLSM)) {
//                        foundExcelFile = true;
//                        break;
//                    }
//                }
//            }
//        }
//        return foundExcelFile;
//    }

//    private Map<String,Double> queryCreditLimitRefValueMap(){
//        Map<String,Object> dicMap = dicService.getDicItemByDicTypeCode("credit.limit.refValue");
//        List<DicItemVo> itemList = (List<DicItemVo>) dicMap.get("data");
//        Map<String,Double> refValueMap = new HashMap<String, Double>();
//        for(DicItemVo wxTCreditDicItem:itemList) {
//            String key = wxTCreditDicItem.getDicItemCode().replace("_"," ");
//            Double value = Double.parseDouble(wxTCreditDicItem.getDicItemName());
//            refValueMap.put(key,value);
//        }
//        return refValueMap;
//    }

//    /**
//     * 查询待办时间的列表
//     * @return
//     */
//    @Override
//    public JsonResponse queryTodoList(CreditListQueryParam param) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//        	if(param.getLimit() == 0){
//        		param.setLimit(10);
//        	}
//        	
//            List<String> processKeys = CreditApplicationUtil.getProcessKeys();
//            List<WxTCreditAppRequestFormVo> results = new ArrayList<WxTCreditAppRequestFormVo>();
//            List<Task> tasks = new ArrayList<Task>();
//
//            WxTUser user = ContextUtil.getCurUser();
//            String salesChannel = user.getSalesChannel();
//            boolean isCDM = "CDM".equals(salesChannel);
//            // 如果是chevron的user model
//            String cai = user.getCai();
//            boolean isSupervisorAndAbove = false;
//            boolean isSales = false;
//            String checkRoleForSupervisorAndAbove = "";
//            if(StringUtils.isNotBlank(cai)){
//                Map<String, Object> userInfoParams = new HashMap<String, Object>();
//                userInfoParams.put("cai", cai);
//                // 查询当前用户是哪些职务的人,这个是从销售关系的那两个表里面查询出来的
//                WxTUser chevronUserInfo = wxTUserMapper.getChevronUserInfo(userInfoParams);
//                if (chevronUserInfo != null) {
//                    if ("BuManager".equals(chevronUserInfo.getUserModel())) {
//                        isSupervisorAndAbove = true;
//                        checkRoleForSupervisorAndAbove = isCDM?"Chevron_Manager":"Chevron_C&I_Admin";
//                    } else if ("ChannelManager".equals(chevronUserInfo.getUserModel())) {
//                        isSupervisorAndAbove = true;
//                        checkRoleForSupervisorAndAbove = isCDM?"Chevron_CDM_Channel_Manager":"Chevron_C&I_Channel_Manager";
//                    } else if ("Supervisor".equals(chevronUserInfo.getUserModel())) {
//                        isSupervisorAndAbove = true;
//                        checkRoleForSupervisorAndAbove = isCDM?"Chevron_CDM_Suppervisor":"Chevron_C&I_Suppervisor";
//                    } else if ("Sales".equals(chevronUserInfo.getUserModel())) {
//                        isSales = true;
//                    }
//                }
//            }
//            List<Task> unsignedTasks = getTodoTasks(processKeys, user, checkRoleForSupervisorAndAbove);
//            List<Task> filteredTasks = new ArrayList<Task>();
//
//            // 过滤一下unsignedTasks
//            if(unsignedTasks!=null && unsignedTasks.size() >0){
//                List<String> adjustProcessInstanceIds = new ArrayList<String>();
//                for(Task task:unsignedTasks) {
//                    if(CreditApplicationUtil.ADJUST_VALUE.equals(task.getName())){
//                        String processInstanceId = task.getProcessInstanceId();
//                        adjustProcessInstanceIds.add(processInstanceId);
//                    }
//                }
//                StringBuffer myAdjustProcIdsSB = new StringBuffer("");
//                if(adjustProcessInstanceIds.size() >0){
//                    WxTCreditAppRequestFormVoExample example = new WxTCreditAppRequestFormVoExample();
//                    example.createCriteria().andAiPreparedByEqualTo(user.getUserId()).andDeleteFlagNotEqualTo(1).andProcessInstanceIdIn(adjustProcessInstanceIds);
//                    List<WxTCreditAppRequestFormVo> myAdjustWxTCreditAppRequestFormVoList = wxTCreditAppRequestFormVoMapper.selectByExample(example);
//                    if(myAdjustWxTCreditAppRequestFormVoList != null && myAdjustWxTCreditAppRequestFormVoList.size()>0){
//                        for(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo:myAdjustWxTCreditAppRequestFormVoList){
//                            myAdjustProcIdsSB.append(wxTCreditAppRequestFormVo.getProcessInstanceId());
//                            myAdjustProcIdsSB.append(",");
//                        }
//                    }
//                }
//
////                if(myAdjustProcIdsSB.toString().length()>0){
//                    for(Task task:unsignedTasks) {
//                        String myAdjustProcIds = myAdjustProcIdsSB.toString();
//                        if(!CreditApplicationUtil.ADJUST_VALUE.equals(task.getName()) || (CreditApplicationUtil.ADJUST_VALUE.equals(task.getName()) && myAdjustProcIds.contains(task.getProcessInstanceId() + ","))){
//                           filteredTasks.add(task);
//                        }
//                    }
////                } else {
////                    filteredTasks = unsignedTasks;
////                }
//
////                filteredTasks
//            }
//
//            // 根据当前人的ID查询那些claim了的，暂时来说不需要
////        List<Task> todoList = taskService.createTaskQuery().processDefinitionKeyIn(processKeys).taskCandidateOrAssigned(String.valueOf(userId)).list();
//            tasks.addAll(filteredTasks);
//
//
//
//            Set<String> processInstanceIdList = new HashSet<String>();
//            for (Task task : tasks) {
////                processInstanceIdList = new HashSet<String>();
//                String processInstanceId = task.getProcessInstanceId();
//                processInstanceIdList.add(processInstanceId);
//            }
//            if(processInstanceIdList.isEmpty()){
//            	jsonResponse.setTotalOfPaging(0);
//                jsonResponse.setListResult(results);
//                return jsonResponse;
//            }
//
//            List<Long> businessKeyList = new ArrayList<Long>();
//            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIdList).list();
//            for(ProcessInstance processInstance:processInstanceList){
//                if(processInstance.getBusinessKey() != null && !"null".equals(processInstance.getBusinessKey() )){
//                    businessKeyList.add(Long.valueOf(processInstance.getBusinessKey()));
//                }
//            }
//            param.setSupervisorAndAbove(isSupervisorAndAbove);
//            param.setSales(isSales);
//            param.setCai(cai);
//            param.setIdList(businessKeyList);
////            Date dateEnd = param.getDateEnd();
////            if (dateEnd != null) {
////                Calendar endCal = Calendar.getInstance();
////                endCal.setTime(dateEnd);
////                endCal.set(Calendar.MILLISECOND, 0);
////                endCal.set(Calendar.HOUR_OF_DAY, 23);
////                endCal.set(Calendar.MINUTE, 59);
////                endCal.set(Calendar.SECOND, 59);
////                dateEnd = endCal.getTime();
////                param.setDateEnd(dateEnd);
////            }
//            // 需要根据当前用户的salesChannel 和 销售关系来判断
//            results = wxTCreditAppRequestFormVoMapper.getCreditAppRequestFormsByCondition(param);
////            businessKeyList, searchWord, limit, size
////            WxTCreditAppRequestFormVo creditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.search(businessKeyList);
//
//
//            // 根据流程的业务ID查询实体并关联
////            for (Task task : tasks) {
////                String processInstanceId = task.getProcessInstanceId();
////
////                // 根据processInstanceId获取到processInstance
////                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceId).singleResult();
////                String businessKey = processInstance.getBusinessKey();
////                if("null".equals(businessKey) || StringUtils.isEmpty(businessKey)){
////                    continue;
////                }
////                WxTCreditAppRequestFormVo creditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.selectInfoByPrimaryKeyIn(Long.valueOf(businessKey));
////                creditAppRequestFormVo.setCurTaskId(task.getId());
////                results.add(creditAppRequestFormVo);
////            }
//            // TODO 增加查询那些已经草稿状态的task的列表
////            WxTCreditAppRequestFormVoExample creditAppRequestFormVoExample = new WxTCreditAppRequestFormVoExample();
////            creditAppRequestFormVoExample.createCriteria().andDeleteFlagNotEqualTo(1).andAiPreparedByEqualTo(user.getUserId());
////            List<WxTCreditAppRequestFormVo> draftTasks = wxTCreditAppRequestFormVoMapper.selectByExample(creditAppRequestFormVoExample);
////            if(draftTasks!= null){
////                results.addAll(draftTasks);
////            }
//            jsonResponse.setTotalOfPaging(param.getTotalCount());
//            jsonResponse.setListResult(results);
//        }catch (Exception e){
//            jsonResponse.setErrorMsg(e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }
//
//    private List<Task> getTodoTasks(List<String> processKeys, WxTUser user,String checkRoleForSupervisorAndAbove) {
//        List<Task> unsignedTasks;
//        TaskQuery taskQuery = taskService.createTaskQuery();
//        List<String> roleList;
//        if (user.getUserId() != 1L) {
//            if(StringUtils.isNotBlank(checkRoleForSupervisorAndAbove)){
//                roleList = new ArrayList<String>();
//                roleList.add(checkRoleForSupervisorAndAbove);
//                List<String> userRoleList = getRoleStrList(user);
//                if(userRoleList.contains("L3_CDM_ABM_Sales_Manager")){
//                	roleList.add("L3_CDM_ABM_Sales_Manager");
//                }
//                if(userRoleList.contains("L3_C&I_ABM_Sales_Manager")){
//                	roleList.add("L3_C&I_ABM_Sales_Manager");
//                }
//            } else {
//                roleList = getRoleStrList(user);
//                boolean isGM = false;
//                // 增加对roleList的过滤,目前就是GM的就查询GM的todo
//                if(roleList !=null && roleList.size() >0){
//                    for(String role:roleList) {
//                        if("GM".equals(role)) {
//                            isGM = true;
//                            break;
//                        }
//                    }
//                }
//                if(isGM) {
//                    roleList.clear();
//                    roleList.add("GM");
//                }
//            }
//            taskQuery = taskQuery.processDefinitionKeyIn(processKeys).taskCandidateGroupIn(roleList);
////            if(processInstanceId!=null){
////                taskQuery = taskQuery.processInstanceId(processInstanceId);
////            }
//            unsignedTasks = taskQuery.list();
//        } else {
//            taskQuery = taskQuery.processDefinitionKeyIn(processKeys);
////            if(processInstanceId!=null){
////                taskQuery = taskQuery.processInstanceId(processInstanceId);
////            }
//            unsignedTasks = taskQuery.list();
//        }
//        return unsignedTasks;
//    }
//
//    private List<String> getRoleStrList(WxTUser user) {
//        List<String> roleStrList = null;
//        List<WxTRole> wxTRoles = user.getRoleList();
//        if (wxTRoles != null && wxTRoles.size() > 0) {
//            roleStrList = new ArrayList<String>();
//            for (WxTRole wxTRole : wxTRoles) {
//                roleStrList.add(wxTRole.getChRoleName());
//            }
//        }
//        return roleStrList;
//    }

//    /**
//     * 查询当前登录人所有参与过的列表
//     * @return
//     */
//    @Override
//    public JsonResponse queryAllList(CreditListQueryParam param) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//        	if(param.getLimit() == 0){
//        		param.setLimit(10);
//        	}
//            List<String> processKeys = CreditApplicationUtil.getProcessKeys();
//            WxTUser user = ContextUtil.getCurUser();
//            String roleListStr = getRoleListStr(user);
//            // 这里是保证Local_Credit_Analyst，Local_Credit_Team_Lead，China_Finance_Manager，AP_Credit_Team这几个角色，
//            // 能狗查询到自己角色一定看到的内容，就是那些(auto)的被别人帮着审批的，这几个角色的还是可以查询到
//            List<String> extendedTaskNames = new ArrayList<String>();
//            if(roleListStr.contains("Local_Credit_Analyst,")) {
//                extendedTaskNames.add(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK);
//                extendedTaskNames.add(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK);
//            } else if(roleListStr.contains("Local_Credit_Team_Lead,")) {
//                extendedTaskNames.add(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK);
//                extendedTaskNames.add(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK);
//            } else if (roleListStr.contains("China_Finance_Manager,")) {
//                extendedTaskNames.add(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK);
//            } else if (roleListStr.contains("AP_Credit_Team,")) {
//                extendedTaskNames.add(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK);
//            }
//            HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
//            List<HistoricTaskInstance> taskInstanceList;
//            if(user.getUserId() == 1L){
//                taskInstanceList = historicTaskInstanceQuery
//                        .processDefinitionKeyIn(processKeys)
//                        .list();
//            } else {
//                if(extendedTaskNames!=null && extendedTaskNames.size() > 0){
////                    taskInstanceList = historicTaskInstanceQuery
////                            .processDefinitionKeyIn(processKeys)
////                            .taskAssignee(String.valueOf(user.getUserId()))
////                            .taskVariableValueEquals("actualExecutorId",user.getUserId())
////                            .taskNameIn(extendedTaskNames)
////                            .list();
//
//                    taskInstanceList = historicTaskInstanceQuery.finished()
//                            .processDefinitionKeyIn(processKeys)
//                            .or().taskNameIn(extendedTaskNames)
//                            .taskAssignee(String.valueOf(user.getUserId()))
//                            .taskVariableValueEquals("actualExecutorId",user.getUserId()).endOr()
//                            .list();
//                } else {
//                    taskInstanceList = historicTaskInstanceQuery
//                            .processDefinitionKeyIn(processKeys)
//                            .taskAssignee(String.valueOf(user.getUserId()))
//                            .list();
//                }
//            }
//
//            List<WxTCreditAppRequestFormVo> creditAppRequestFormVos = new ArrayList<WxTCreditAppRequestFormVo>();
//            jsonResponse.setTotalOfPaging(0);
//            jsonResponse.setListResult(creditAppRequestFormVos);
//            if(taskInstanceList!=null && taskInstanceList.size() > 0){
//                Set<String> processInstanceIdSet = new HashSet<String>();
//                for(HistoricTaskInstance historicTaskInstance:taskInstanceList){
//                    processInstanceIdSet.add(historicTaskInstance.getProcessInstanceId());
//                }
//                HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
//                /*List<HistoricProcessInstance> processInstances = historicProcessInstanceQuery.processDefinitionKeyIn(processKeys).processInstanceIds(processInstanceIdSet).listPage(start,limit);
//                if(processInstances!=null && processInstances.size() >0 ){
//                    for(HistoricProcessInstance processInstance:processInstances){
//                        if(processInstance.getBusinessKey() != null && !"null".equals(processInstance.getBusinessKey())){
//                            creditAppRequestFormVos.add(wxTCreditAppRequestFormVoMapper.selectInfoByPrimaryKey(Long.valueOf(processInstance.getBusinessKey())));
//                        }
//                    }
//                    jsonResponse.setTotalOfPaging(historicProcessInstanceQuery.count());
//                    jsonResponse.setListResult(creditAppRequestFormVos);
//                }*/
//                List<HistoricProcessInstance> processInstances = historicProcessInstanceQuery.processDefinitionKeyIn(processKeys).processInstanceIds(processInstanceIdSet).list();
//                List<Long> businessKeyList = new ArrayList<Long>();
//                if(processInstances!=null && processInstances.size() >0 ){
//	                for(HistoricProcessInstance processInstance:processInstances){
//	                	if(StringUtils.isNotEmpty(processInstance.getBusinessKey()) && !"null".equals(processInstance.getBusinessKey())){
//	                		businessKeyList.add(Long.valueOf(processInstance.getBusinessKey()));
//	                	}
//	                }
//                }
//                param.setIdList(businessKeyList);
////                Date dateEnd = param.getDateEnd();
////                if (dateEnd != null) {
////                    Calendar endCal = Calendar.getInstance();
////                    endCal.setTime(dateEnd);
////                    endCal.set(Calendar.MILLISECOND, 0);
////                    endCal.set(Calendar.HOUR_OF_DAY, 23);
////                    endCal.set(Calendar.MINUTE, 59);
////                    endCal.set(Calendar.SECOND, 59);
////                    dateEnd = endCal.getTime();
////                    param.setDateEnd(dateEnd);
////                }
//                List<WxTCreditAppRequestFormVo> results = wxTCreditAppRequestFormVoMapper.getCreditAppRequestFormsByCondition(param);
//                jsonResponse.setTotalOfPaging(param.getTotalCount());
//                jsonResponse.setListResult(results);
//            }
//        } catch (Exception e){
//            e.printStackTrace();
//            jsonResponse.setErrorMsg(e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }

//    @Override
//    public JsonResponse queryMyList(CreditListQueryParam param) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//            List<String> creditAppTypes = CreditApplicationUtil.getCreditAppTypes();
//            WxTUser user = ContextUtil.getCurUser();
//
//            if(com.common.util.StringUtils.isBlank(param.getField())){
//                param.setField("createTime");
//                param.setDirection("DESC");
//            }
//            param.setCreditAppTypes(creditAppTypes);
//            param.setUserId(user.getUserId());
//            List<WxTCreditAppRequestFormVo> creditAppRequestFormVos = wxTCreditAppRequestFormVoMapper.selectMyListByUserId(param);
//
//            jsonResponse.setTotalOfPaging(param.getTotalCount());
//            jsonResponse.setListResult(creditAppRequestFormVos);
//        } catch (Exception e) {
//            jsonResponse.setErrorMsg(e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }
//
//    @Override
//    public JsonResponse queryDraftList(CreditListQueryParam param) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try {
//            List<String> creditAppTypes = CreditApplicationUtil.getCreditAppTypes();
//            WxTUser user = ContextUtil.getCurUser();
//
//            if(com.common.util.StringUtils.isBlank(param.getField())){
//                param.setField("createTime");
//                param.setDirection("DESC");
//            }
//            param.setCreditAppTypes(creditAppTypes);
//            param.setUserId(user.getUserId());
//            param.setStatus("draft");
//            if(StringUtils.isEmpty(param.getSearchWord())){
//                param.setSearchWord(null);
//            }
//            List<WxTCreditAppRequestFormVo> creditAppRequestFormVos = wxTCreditAppRequestFormVoMapper.selectMyListByUserId(param);
//
//            jsonResponse.setTotalOfPaging(param.getTotalCount());
//            jsonResponse.setListResult(creditAppRequestFormVos);
//        } catch (Exception e) {
//            jsonResponse.setErrorMsg(e.getLocalizedMessage());
//            logger.error(e.getLocalizedMessage());
//        }
//        return jsonResponse;
//    }

    @Override
    @Transactional
    public JsonResponse calcCustomerFinanceInfo(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
//            if(wxTCreditAppRequestFormVo.getId() == null){
//            	throw new WxPltException("wxTCreditAppRequestFormVo需要id");
//            }

            CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditAppRequestFormVo.getCreditType());
            if(creditAppType == null){
            	throw new WxPltException("creditType不是正确的值");
            }

            Date currentDate = DateUtil.getCurrentDate();
            CreditScoringParam creditScoringParam = creditRangeService.queryCreditScoringParam();
            String requestNo = wxTCreditAppRequestFormVo.getRequestNo();


            // 先获取一下老的form的值
            WxTCreditAppRequestFormVo oriWxTCreditAppRequestFormVo = creditAppRequestFormBizService.getWorkflowFormByKey(wxTCreditAppRequestFormVo.getId());


            /**
             * 计算现在数据库里面记录的cbiDateEstablishment，cbiCooperationYearsWithCvx，payHistoryWithChevron
             */
            CreditNonFinancialRatios oriCreditNonFinancialRatios = new CreditNonFinancialRatios(oriWxTCreditAppRequestFormVo);
            /**
             * 从用户传过来的的cbiDateEstablishment，cbiCooperationYearsWithCvx，payHistoryWithChevron
             * 根据计算yearsInBusiness
             */
            CreditNonFinancialRatios formCreditNonFinancialRatios = new CreditNonFinancialRatios(wxTCreditAppRequestFormVo);

            /**
             * 这个本地默认数据字典里面有个0，如果excel里面有，以excel为准
             */
            Double bouncedChecks = creditScoringParam.getBouncedChecks();

            // 修改form里面的updateTime的值的更新时间
            wxTCreditAppRequestFormVo.setUpdateTime(DateUtil.getCurrentDate());
            wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo);
            if(wxTCreditAppRequestFormVo.getCfiInfo() != null && wxTCreditAppRequestFormVo.getCfiInfo().getId() != null){
                wxTCreditAppRequestFormVo.getCfiInfo().setUpdateTime(DateUtil.getCurrentDate());
                wxTCreditAppCustomerFinanceInfoVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo.getCfiInfo());
            }

            /**
             * 获得最近的那个excel来作为Financial report，来计算
             */
            CreditListQueryParam attListQueryParam = new CreditListQueryParam();
            attListQueryParam.setPaging(false);
            attListQueryParam.setAttColumnName("cbiFinancialStatementsAttId");
            attListQueryParam.setApplicationId(wxTCreditAppRequestFormVo.getId());
            attListQueryParam.setDirection("desc");
            attListQueryParam.setField("createTime");
            List<CreditAppAttVo> creditAppAttVoList = wxTCreditAppRequestFromAttVoMapper.queryAttListByAttColumnName(attListQueryParam);
            Long financialStatementsAttId = null;
            if(creditAppAttVoList != null && creditAppAttVoList.size() >0){
                for(CreditAppAttVo creditAppAttVo : creditAppAttVoList){
                    String fileName = creditAppAttVo.getFileName();
                    if(fileName.endsWith(CreditFinancialReportController.SUFFIX_XLS)
                            || fileName.endsWith(CreditFinancialReportController.SUFFIX_XLSX)
                            ||  fileName.endsWith(CreditFinancialReportController.SUFFIX_XLSM)){
                        financialStatementsAttId = creditAppAttVo.getAttId();
                        break;
                    }
                }
            }

            // 没有找到Excel后缀的文件的时候就表示，查询失败了
            if(financialStatementsAttId == null){
                throw new WxPltException("没有找到合适的附件用来计算");
            }

            // 找到对应financialStatementsAttId的文件，得到excel的workbook
            WxAttFile att = fileManagerService.searchAttByKey(String.valueOf(financialStatementsAttId));
            String filePath = FileManager.getFileRealPath(att.getSourceType(), att.getStorePath()) + System.getProperty("file.separator")
                    + att.getStorageName();
            File attFile = new File(filePath);
            FileInputStream fileInputStream = new FileInputStream(filePath);
            Workbook wb = null;
            if (attFile.getPath().endsWith(CreditFinancialReportController.SUFFIX_XLS)) {
                wb = new HSSFWorkbook(new POIFSFileSystem(fileInputStream));
            } else if (attFile.getName().endsWith(CreditFinancialReportController.SUFFIX_XLSX) || attFile.getName().endsWith(CreditFinancialReportController.SUFFIX_XLSM)) {
                wb = new XSSFWorkbook(fileInputStream);
            }

            /**
             * 对于genereateFinancialReportRatio这个函数需要的那些参数，excel里面也是有值得，需要更新一下
             */
            Map<String,Object> nonFinancialRatiosMap = CreditFinancialReportUtil.getNonFinancialRatios(wb);
            CreditNonFinancialRatios excelCreditNonFinancialRatios = new CreditNonFinancialRatios(nonFinancialRatiosMap);
            if(nonFinancialRatiosMap.get(CreditConstants.bouncedChecks) != null) {
                bouncedChecks = (Double)nonFinancialRatiosMap.get(CreditConstants.bouncedChecks);
            }

            CreditNonFinancialRatios creditNonFinancialRatios = CreditApplicationUtil.getCreditNonFinancialRatios(oriCreditNonFinancialRatios,
                    formCreditNonFinancialRatios, excelCreditNonFinancialRatios);

            /**
             * 参数准备好了开始check一下了
             */
            if(creditNonFinancialRatios.getYearsInBusiness() == null){
            	LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.credit.service.impl.CreditApplicationServiceImpl.calcCustomerFinanceInfo", 
            			"需要excel中填写或者报文中有yearsInBusiness或者cbiDateEstablishment", wxTCreditAppRequestFormVo.getRequestNo());
                throw new WxPltException("上传的Excel模板有误");
            }

            if(creditNonFinancialRatios.getYearsWithChevron() == null){
            	LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.credit.service.impl.CreditApplicationServiceImpl.calcCustomerFinanceInfo", 
            			"需要excel中填写或者报文中有cbiCooperationYearsWithCvx", wxTCreditAppRequestFormVo.getRequestNo());
                throw new WxPltException("上传的Excel模板有误");
            }

            if(creditNonFinancialRatios.getPayHistoryWithChevron() == null){
            	LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.credit.service.impl.CreditApplicationServiceImpl.calcCustomerFinanceInfo", 
            			"需要excel中填写或者报文中有cfiInfo.cfiPayHistoryWithChevron", wxTCreditAppRequestFormVo.getRequestNo());
                throw new WxPltException("上传的Excel模板有误");
            }

            // 先保存一下form
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, -1 * creditNonFinancialRatios.getYearsInBusiness().intValue());
            wxTCreditAppRequestFormVo.setCbiDateEstablishment(calendar.getTime());
            // 需要设置回wxTCreditAppRequestFormVo
            wxTCreditAppRequestFormVo.setCbiCooperationYearsWithCvx(creditNonFinancialRatios.getYearsWithChevron().intValue());
            wxTCreditAppRequestFormVo.setUpdateTime(currentDate);
            wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo);

            /**
             * 参数准备好了，调用开始计算
             */
            Map<String,Object> reportRatioMap = creditFinancialReportService.genereateFinancialReportRatio(requestNo, creditNonFinancialRatios.getYearsInBusiness(),
                    creditNonFinancialRatios.getYearsWithChevron(), creditNonFinancialRatios.getPayHistoryWithChevron(), bouncedChecks, wb);

            WxTCreditAppCustomerFinanceInfoVo wxTCreditAppCustomerFinanceInfoVo = new WxTCreditAppCustomerFinanceInfoVo();
            WxTCreditAppCustomerFinanceInfoVo cfiInfo = wxTCreditAppRequestFormVo.getCfiInfo();
            // 先是把用户这次传过来得credit 填写得那些值给设置了
            // 下面的这几个是这个3钟类型都有的属性
            wxTCreditAppCustomerFinanceInfoVo.setCfiYearN1PaymentRecord(cfiInfo.getCfiYearN1PaymentRecord());
            // 这个也使用新的计算的
            wxTCreditAppCustomerFinanceInfoVo.setCfiPayHistoryWithChevron(creditNonFinancialRatios.getPayHistoryWithChevron());
            wxTCreditAppCustomerFinanceInfoVo.setCfiDsoInChevronChina(cfiInfo.getCfiDsoInChevronChina());
            // 下面的是各个type需要更新的
            if(creditAppType == CreditAppType.ANNUAL_CREDIT_REVIEW){
                wxTCreditAppCustomerFinanceInfoVo.setCfiRecCreditLimitOfCurrentYear(cfiInfo.getCfiRecCreditLimitOfCurrentYear());
                wxTCreditAppCustomerFinanceInfoVo.setCfiRecCreditPaymentTerm(cfiInfo.getCfiRecCreditPaymentTerm());
            } else if(creditAppType == CreditAppType.TEMP_CREDIT_REQUEST) {
                wxTCreditAppCustomerFinanceInfoVo.setCfiRecAddTempCreditLimit(cfiInfo.getCfiRecAddTempCreditLimit());
                wxTCreditAppCustomerFinanceInfoVo.setCfiRecTempPaymentTerm(cfiInfo.getCfiRecTempPaymentTerm());
            } else if(creditAppType == CreditAppType.CV_REQUEST) {
                wxTCreditAppCustomerFinanceInfoVo.setCfiCurrentExposure(cfiInfo.getCfiCurrentExposure());
                wxTCreditAppCustomerFinanceInfoVo.setCfiCvAmount(cfiInfo.getCfiCvAmount());
            }


//            } else {
                // CV的这几个值有点不一样

//            }


            /**
             * 根据reportRatioMap，构造WxTCreditAppCustomerFinanceInfoVo
             */
            WxTCreditRatio ratio = (WxTCreditRatio)reportRatioMap.get("ratio");
            FinancialRatioView ratioView = (FinancialRatioView)reportRatioMap.get("ratioView");
            FinancialBalanceSheetView balanceView = (FinancialBalanceSheetView)reportRatioMap.get("balanceView");

            wxTCreditAppCustomerFinanceInfoVo.setCfiQuickRatio(ratio.getQuickRatio());
            wxTCreditAppCustomerFinanceInfoVo.setCfiCurrentRatio(ratio.getCurrentRatio());
            wxTCreditAppCustomerFinanceInfoVo.setCfiDailySales(ratioView.getDailySales());
            wxTCreditAppCustomerFinanceInfoVo.setCfiNetWorkingCapitalCycle(ratioView.getNetWorkingCapitalCycle());
            wxTCreditAppCustomerFinanceInfoVo.setCfiCashFlowCoverage(ratioView.getCashFlowCoverage());
            /**
             * 注意  setCfiTangibleNetWorthRatioG32 和 setCfiTangibleNetWorthRatio 的差别
             */
            wxTCreditAppCustomerFinanceInfoVo.setCfiTangibleNetWorthRatioG32(ratioView.getTdmitNetWorth());
            wxTCreditAppCustomerFinanceInfoVo.setCfiApDays(ratioView.getApDays());
            /**
             * 注意  setCfiTangibleNetWorthRatioG32 和 setCfiTangibleNetWorthRatio 的差别
             */
            wxTCreditAppCustomerFinanceInfoVo.setCfiTangibleNetWorth(ratioView.getTangibleNetWorth());
            wxTCreditAppCustomerFinanceInfoVo.setCfiCurrentLiabilityToEquity(ratio.getCurrentLiabilityToEquity());
            wxTCreditAppCustomerFinanceInfoVo.setCfiLongTermLiabilityTotalAssetsRatio(ratio.getLongTermLiablityTotalAssetsRatio());
            wxTCreditAppCustomerFinanceInfoVo.setCfiLiablitiesAssets(ratio.getLiablitiesAssets());
            wxTCreditAppCustomerFinanceInfoVo.setCfiEquityRatio(ratio.getEquityRatio());
            wxTCreditAppCustomerFinanceInfoVo.setCfiInventoryTurnover(ratio.getInventoryTurnover());
            wxTCreditAppCustomerFinanceInfoVo.setCfiDaysInInventory(ratio.getDaysInInventory());
            wxTCreditAppCustomerFinanceInfoVo.setCfiAccountReceivableTrunover(ratio.getAccountReceivableTrunover());
            wxTCreditAppCustomerFinanceInfoVo.setCfiDaysInAccountsReceivable(ratio.getDaysInAccountsReceivable());
            wxTCreditAppCustomerFinanceInfoVo.setCfiSaleCurrentAssets(ratio.getSaleCurrentAssets());
            wxTCreditAppCustomerFinanceInfoVo.setCfiAssetTurnover(ratio.getAssetTurnover());
            wxTCreditAppCustomerFinanceInfoVo.setCfiProfitMargin(ratio.getProfitMargin());
            wxTCreditAppCustomerFinanceInfoVo.setCfiAfterTaxProfitRatio(ratio.getAfterTaxProfitRatio());
            wxTCreditAppCustomerFinanceInfoVo.setCfiReturnOnEquity(ratio.getReturnOnEquity());
            wxTCreditAppCustomerFinanceInfoVo.setCfiAssetTurnoverNetSalesToTotalAssets(ratio.getNetSalesToTotalAssets());
            wxTCreditAppCustomerFinanceInfoVo.setCfiWorkingCapital(ratio.getWorkingCapital());
            wxTCreditAppCustomerFinanceInfoVo.setCfiEquity(ratio.getEquity());
            wxTCreditAppCustomerFinanceInfoVo.setCfiWorkingAssets(ratio.getWorkingAssets());
            wxTCreditAppCustomerFinanceInfoVo.setCfiEstimatedValue(ratio.getEstimatedValue());
            wxTCreditAppCustomerFinanceInfoVo.setCfiCreditIndex(ratio.getCreditIndex());
            wxTCreditAppCustomerFinanceInfoVo.setCfiCreditLimitEstimatedValue(ratio.getCreditLimitEstimatedValue());
            // 这个是来自'='Credit Scoring'!C40需要计算的
            wxTCreditAppCustomerFinanceInfoVo.setCfiCalculatedCreditLimitPerCreditPolicy((balanceView.getTotalAssets() - balanceView.getTotalLiabilities()) * 0.05);
            /**
             * total积分是加起来得
             */
            Double score1 = (Double) reportRatioMap.get("score1");
            Double score2 = (Double) reportRatioMap.get("score2");
            wxTCreditAppCustomerFinanceInfoVo.setCfiTotalScore(score1 + score2);

            // 开始保存cfiInfo
            // 并且准备toUpdateWxTCreditAppRequestFormVo
//            WxTCreditAppRequestFormVo toUpdateWxTCreditAppRequestFormVo = new WxTCreditAppRequestFormVo();
            wxTCreditAppCustomerFinanceInfoVo.setFormId(wxTCreditAppRequestFormVo.getId());
//            if(wxTCreditAppRequestFormVo.getCfiInfoId() != null){
                wxTCreditAppCustomerFinanceInfoVo.setUpdateTime(currentDate);
                wxTCreditAppCustomerFinanceInfoVo.setId(wxTCreditAppRequestFormVo.getCfiInfoId());
                wxTCreditAppCustomerFinanceInfoVoMapper.updateByFormIdSelective(wxTCreditAppCustomerFinanceInfoVo);
//            } else {
//                wxTCreditAppCustomerFinanceInfoVo.setCreateTime(currentDate);
//                wxTCreditAppCustomerFinanceInfoVo.setUpdateTime(currentDate);
//                wxTCreditAppCustomerFinanceInfoVoMapper.insertSelective(wxTCreditAppCustomerFinanceInfoVo);
//                wxTCreditAppRequestFormVo.setCfiInfoId(wxTCreditAppCustomerFinanceInfoVo.getId());
//                wxTCreditAppRequestFormVo.setUpdateTime(currentDate);
//                wxTCreditAppRequestFormVoMapper.updateByPrimaryKeySelective(wxTCreditAppRequestFormVo);
//            }

            jsonResponse.setDataResult(creditAppRequestFormBizService.getWorkflowFormByKey(wxTCreditAppRequestFormVo.getId()));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            int permissionWeight = 0;
			try {
				permissionWeight = creditAppRequestFormBizService.getPermissionWeight();
			} catch (WxPltException e1) {
	            logger.error(e.getMessage(), e);
			}
            if((permissionWeight & CreditAppRequestFormBizService.PERMISSION_WEIGHT_CAL_FIN_ATT) == 0) {
            	return jsonResponse;
            }
            if(e instanceof WxPltException) {
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                jsonResponse.setErrorMsg(e.getMessage());
            }else {
            	jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            	LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.credit.service.impl.CreditApplicationServiceImpl.calcCustomerFinanceInfo", e.getMessage(), wxTCreditAppRequestFormVo.getRequestNo());
            }
        }
        return jsonResponse;
    }

//    @Override
//    public JsonResponse queryApprovalHistory(CreditListQueryParam param) {
//        JsonResponse jsonResponse = new JsonResponse();
//        try{
//            if(param.getApplicationId() == null){
//                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//                jsonResponse.setErrorMsg("查询的时候application不能为空");
//                return jsonResponse;
//            }
//            // 根据applicationId查询到对应form的processInstanceId
//            WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.selectByPrimaryKey(param.getApplicationId());
//            
//            Map<String,Double> refValueMap = queryCreditLimitRefValueMap();
//            CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditAppRequestFormVo.getCreditType());
//           String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
//
//           // 查询这个process的所有肯恶搞经历的activities
//            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(creditAppType.getProcessKey()).latestVersion().singleResult();
//            ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
//                    .getDeployedProcessDefinition(processDefinition.getId());
//            List<ActivityImpl> activities = processDefinitionEntity.getActivities();
//            Map<String,String>  approvalTeamMap = new HashMap<String, String>();
//            String salesChannel = "";
//            // 查询整个流程对应的那些人的id  根据 requestedBy的那个查询到这个人对应的那些 sales相关 + credit team的那些人的值
//            if(StringUtils.isNotBlank(wxTCreditAppRequestFormVo.getAiRequestedBy())){
//                String aiRequestedByCai = wxTCreditAppRequestFormVo.getAiRequestedBy();
//                Long aiPreparedBy = wxTCreditAppRequestFormVo.getAiPreparedBy();
//                String[] frontBrackets = wxTCreditAppRequestFormVo.getAiRequestedBy().split("\\(");
//                if(frontBrackets.length >=2){
//                    String[] endBrackets = frontBrackets[frontBrackets.length-1].split("\\)");
//                    if(endBrackets.length > 0){
//                        aiRequestedByCai = endBrackets[0];
//                    }
//                }
//
//                salesChannel = wxTCreditAppRequestFormVoMapper.querySalesChannelByUserId(aiRequestedByCai);
//
//                // 获取任务和执行人的chName的对应的map
//                approvalTeamMap = getApprovalTeamNameMap(aiRequestedByCai, aiPreparedBy);
//            }           
//            
//            if (wxTCreditAppRequestFormVo != null && wxTCreditAppRequestFormVo.getProcessInstanceId() != null) {
//                Map<String,Object> queryApprovalHistoryParam = new HashMap<String, Object>();
//                queryApprovalHistoryParam.put("processInstanceId",wxTCreditAppRequestFormVo.getProcessInstanceId());
//                List<FormApprovalInfo> formApprovalInfos = wxTCreditAppRequestFormVoMapper.queryApprovalHistory(queryApprovalHistoryParam);
//                if(formApprovalInfos != null && formApprovalInfos.size() > 0) {
//                    for(FormApprovalInfo formApprovalInfo:formApprovalInfos) {
//                        if(formApprovalInfo.getStepName().equals(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK)){
//                            formApprovalInfo.setStepName(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK);
//                        }
//                        String userNameInApprovalTeamMap = approvalTeamMap.get(formApprovalInfo.getStepName());
//                        
//                      //这种情况存在于被recall后的recall到上一步的节点
//                        if(formApprovalInfo.getExecutionName() != null && !userNameInApprovalTeamMap.equals(formApprovalInfo.getExecutionName())){                       	
//                        	if(formApprovalInfo.getExecutionName().contains("auto") && formApprovalInfo.getExecutionName().contains(userNameInApprovalTeamMap)){
//                        		continue;
//                        	}else{
//                        		//CV 存在Local Credit Analyst 这个步骤中 Rachel和POPPY都可以审批同一个节点的情况
//                        		if(wxTCreditAppRequestFormVo.getCreditType().equals(CreditAppType.CV_REQUEST.getValue())){
//                        			if(formApprovalInfo.getStepName().equals(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK)){
//                        				if(formApprovalInfo.getExecutionName().contains("Rachel")
//                        						|| formApprovalInfo.getExecutionName().contains("Poppy")){
//                        					if(formApprovalInfo.getApprove() == null){
//                                            	formApprovalInfo.setApprove(Long.valueOf(1));
//                        					}                       					
//                        				}else{
//                        					if(formApprovalInfo.getApprove() == null){
//                                				formApprovalInfo.setComments(formApprovalInfo.getExecutionName() + " recall");
//                                				formApprovalInfo.setApprove(Long.valueOf(2));
//                                			}
//                        				}
//                        				
//                        			}else{
//                        				if(formApprovalInfo.getApprove() == null){
//                            				formApprovalInfo.setComments(formApprovalInfo.getExecutionName() + " recall");
//                            				formApprovalInfo.setApprove(Long.valueOf(2));
//                            			}
//                        			}
//                        			
//                        		}else{                      		
//                        			if(formApprovalInfo.getApprove() == null){
//                        				formApprovalInfo.setComments(formApprovalInfo.getExecutionName() + " recall");
//                        				formApprovalInfo.setApprove(Long.valueOf(2));
//                        			}
//                        		}
//                        	}
//                        }
//                        
//                        //这种情况存在于被recall删除后历史人物提交下一步的节点
//                        if(formApprovalInfo.getExecutionName() != null && userNameInApprovalTeamMap.equals(formApprovalInfo.getExecutionName())){
//                        	if(formApprovalInfo.getApprove() == null)
//                        	formApprovalInfo.setApprove(Long.valueOf(1));
//                        }
//                        
//                    }
//                }
//                jsonResponse.setListResult(formApprovalInfos);
//            }
//        } catch (Exception e) {
//            logger.debug(e.getLocalizedMessage());
//            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//            jsonResponse.setErrorMsg(e.getLocalizedMessage());
//        }
//
//        return jsonResponse;
//    }
//    
//    private List<FormApprovalInfo> getApprovalHistoryByProcessId(String applicationId){
//
//    	 // 根据applicationId查询到对应form的processInstanceId
//        WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.selectByPrimaryKey(Long.valueOf(applicationId));
//
//        Map<String,Double> refValueMap = queryCreditLimitRefValueMap();
//        CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditAppRequestFormVo.getCreditType());
//        String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
//
//        // 查询这个process的所有肯恶搞经历的activities
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(creditAppType.getProcessKey()).latestVersion().singleResult();
//        ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
//                .getDeployedProcessDefinition(processDefinition.getId());
//        List<ActivityImpl> activities = processDefinitionEntity.getActivities();
//        Map<String,String>  approvalTeamMap = new HashMap<String, String>();
//        String salesChannel = "";
//        // 查询整个流程对应的那些人的id  根据 requestedBy的那个查询到这个人对应的那些 sales相关 + credit team的那些人的值
//        if(StringUtils.isNotBlank(wxTCreditAppRequestFormVo.getAiRequestedBy())){
//            String aiRequestedByCai = wxTCreditAppRequestFormVo.getAiRequestedBy();
//            Long aiPreparedBy = wxTCreditAppRequestFormVo.getAiPreparedBy();
//            String[] frontBrackets = wxTCreditAppRequestFormVo.getAiRequestedBy().split("\\(");
//            if(frontBrackets.length >=2){
//                String[] endBrackets = frontBrackets[frontBrackets.length-1].split("\\)");
//                if(endBrackets.length > 0){
//                    aiRequestedByCai = endBrackets[0];
//                }
//            }
//
//            salesChannel = wxTCreditAppRequestFormVoMapper.querySalesChannelByUserId(aiRequestedByCai);
//
//            // 获取任务和执行人的chName的对应的map
//            approvalTeamMap = getApprovalTeamNameMap(aiRequestedByCai, aiPreparedBy);
//        }
//        List<FormApprovalInfo> formApprovalInfos = null;
//        if (wxTCreditAppRequestFormVo != null && wxTCreditAppRequestFormVo.getProcessInstanceId() != null) {
//            Map<String,Object> queryApprovalHistoryParam = new HashMap<String, Object>();
//            queryApprovalHistoryParam.put("processInstanceId",wxTCreditAppRequestFormVo.getProcessInstanceId());
//            formApprovalInfos = wxTCreditAppRequestFormVoMapper.queryApprovalHistory(queryApprovalHistoryParam);
//            if(formApprovalInfos != null && formApprovalInfos.size() > 0) {
//                for(FormApprovalInfo formApprovalInfo:formApprovalInfos) {
//                    if(formApprovalInfo.getStepName().equals(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK)){
//                        formApprovalInfo.setStepName(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK);
//                        break;
//                    }
//                    String userNameInApprovalTeamMap = approvalTeamMap.get(formApprovalInfo.getStepName());
//
//                  //这种情况存在于被recall后的recall到上一步的节点
//                    if(formApprovalInfo.getExecutionName() != null && !userNameInApprovalTeamMap.equals(formApprovalInfo.getExecutionName())){
//                    	if(formApprovalInfo.getExecutionName().contains("auto") && formApprovalInfo.getExecutionName().contains(userNameInApprovalTeamMap)){
//                    		continue;
//                    	}else{
//                    		formApprovalInfo.setComments(formApprovalInfo.getExecutionName() + " recall");
//                    		formApprovalInfo.setApprove(Long.valueOf(2));
//                    	}
//                    }
//
//                    //这种情况存在于被recall删除后历史人物提交下一步的节点
//                    if(formApprovalInfo.getExecutionName() != null && userNameInApprovalTeamMap.equals(formApprovalInfo.getExecutionName())){
//                    	if(formApprovalInfo.getApprove() == null)
//                    	formApprovalInfo.setApprove(Long.valueOf(1));
//                    }
//
//                }
//            }
//        }
//        return formApprovalInfos;
//
//    }

    @Override
    public JsonResponse queryAttListByAttColumnName(CreditListQueryParam creditListQueryParam) {
        JsonResponse jsonResponse = new JsonResponse();
        try{
            creditListQueryParam.setPaging(false);
            creditListQueryParam.setDirection("desc");
            creditListQueryParam.setField("createTime");
            List<CreditAppAttVo>  creditAppAttVoList = wxTCreditAppRequestFromAttVoMapper.queryAttListByAttColumnName(creditListQueryParam);
            jsonResponse.setListResult(creditAppAttVoList);
        } catch (Exception e){
            e.printStackTrace();
            logger.error(e.getLocalizedMessage());
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            jsonResponse.setErrorMsg("attColumnName 或者 formId 不能为空");
        }
        return jsonResponse;
    }

    @Override
    public JsonResponse addToAttList(WxTCreditAppRequestFromAttVo wxTCreditAppRequestFromAttVo) throws Exception {
        JsonResponse jsonResponse = new JsonResponse();
        int result = wxTCreditAppRequestFromAttVoMapper.insertSelective(wxTCreditAppRequestFromAttVo);
        if(result <=0 ){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("执行失败");
        }
        Long id = wxTCreditAppRequestFromAttVo.getId();
        CreditListQueryParam param = new CreditListQueryParam();
        param.setPaging(false);
//        param.setApplicationId(wxTCreditAppRequestFromAttVo.getApplicationId());
        param.setRequestNo(wxTCreditAppRequestFromAttVo.getRequestNo());
        param.setDirection("desc");
        param.setField("createTime");
        param.setAttColumnName(wxTCreditAppRequestFromAttVo.getAttColumnName());
        jsonResponse.setDataResult(wxTCreditAppRequestFromAttVoMapper.queryAttListByAttColumnName(param));
        return jsonResponse;
    }

    @Override
    public JsonResponse deleteAttById(WxTCreditAppRequestFromAttVo wxTCreditAppRequestFromAttVo) {
        JsonResponse jsonResponse = new JsonResponse();
        int result = wxTCreditAppRequestFromAttVoMapper.deleteById(wxTCreditAppRequestFromAttVo);
        if(result <=0 ){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("执行失败");
        }
        return jsonResponse;
    }

//    @Override
//    public JsonResponse queryHighlightNodes(WxTCreditAppRequestFromAttVo wxTCreditAppRequestFromAttVo) {
//        JsonResponse jsonResponse = new JsonResponse();
//
//        // 根据application的id查询processInstanceId
//        Long applicationId = wxTCreditAppRequestFromAttVo.getId();
//        WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = wxTCreditAppRequestFormVoMapper.selectInfoByPrimaryKey(applicationId);
//        if (wxTCreditAppRequestFormVo != null && wxTCreditAppRequestFormVo.getProcessInstanceId() != null) {
//            String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
//            // 查询这个processInstanceId对应的processInstance
//            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
////            if(processInstance != null && processInstance.getProcessDefinitionId()!=null){
////                String processDefinitionId = processInstance.getProcessDefinitionId();
//            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
////                ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) repositoryService.createProcessDefinitionQuery().processDefinitionId(processDefinitionId).singleResult();
////                List<String> highLightedFlows = getHighLightedFlows(processDefinition, processInstance.getId());
//
//            for (String activityId : activeActivityIds) {
//
//                // 查询处于活动状态的任务
////                Task task = taskService.createTaskQuery().taskDefinitionKey(activityId).executionId(execution.getId()).singleResult();
//            }
//            jsonResponse.setListResult(activeActivityIds);
////            }
//        }
//        return jsonResponse;
//    }
//
//    /**
//     * 根据creditAppQueryPara里面的applicationId查询
//     * @param creditAppQueryPara
//     * @return
//     */
//    @Override
//    public JsonResponse queryWorkflowNodes(CreditAppQueryPara creditAppQueryPara) throws Exception {
//        JsonResponse jsonResponse = new JsonResponse();
//        // 根据applicationId，查询这个application的信息
//        Long applicationId = creditAppQueryPara.getApplicationId();
//        WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = getApplicationDetailById(applicationId);
//        if(wxTCreditAppRequestFormVo != null && wxTCreditAppRequestFormVo.getCreditType() != null){
//            Map<String,Double> refValueMap = queryCreditLimitRefValueMap();
//            CreditAppType creditAppType = CreditAppType.getCreditAppTypeByValue(wxTCreditAppRequestFormVo.getCreditType());
//           String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
//
//           // 查询这个process的所有肯恶搞经历的activities
//            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(creditAppType.getProcessKey()).latestVersion().singleResult();
//            ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
//                    .getDeployedProcessDefinition(processDefinition.getId());
//            List<ActivityImpl> activities = processDefinitionEntity.getActivities();
//
//            Map<String,String>  approvalTeamMap = new HashMap<String, String>();
//            String salesChannel = "";
//            // 查询整个流程对应的那些人的id  根据 requestedBy的那个查询到这个人对应的那些 sales相关 + credit team的那些人的值
//            if(StringUtils.isNotBlank(wxTCreditAppRequestFormVo.getAiRequestedBy())){
//                String aiRequestedByCai = wxTCreditAppRequestFormVo.getAiRequestedBy();
//                Long aiPreparedBy = wxTCreditAppRequestFormVo.getAiPreparedBy();
//                String[] frontBrackets = wxTCreditAppRequestFormVo.getAiRequestedBy().split("\\(");
//                if(frontBrackets.length >=2){
//                    String[] endBrackets = frontBrackets[frontBrackets.length-1].split("\\)");
//                    if(endBrackets.length > 0){
//                        aiRequestedByCai = endBrackets[0];
//                    }
//                }
//
//                salesChannel = wxTCreditAppRequestFormVoMapper.querySalesChannelByUserId(aiRequestedByCai);
//
//                // 获取任务和执行人的chName的对应的map
//                approvalTeamMap = getApprovalTeamNameMap(aiRequestedByCai, aiPreparedBy);
//            }
//
//            // 获取finalRequestCreditLimit
//            Double finalRequestCreditLimit = wxTCreditAppRequestFormVo.getFinalRequestCreditLimit(creditAppType, getCreditDollarRate(wxTCreditAppRequestFormVo));
//            if(StringUtils.isNotBlank(processInstanceId)){
//                Map<String,Object> queryApprovalHistoryParam = new HashMap<String, Object>();
//                // 查询审批历史
//                queryApprovalHistoryParam.put("processInstanceId",wxTCreditAppRequestFormVo.getProcessInstanceId());
//                queryApprovalHistoryParam.put("order", "ASC");
//                List<FormApprovalInfo> formApprovalInfos = wxTCreditAppRequestFormVoMapper.queryApprovalHistory(queryApprovalHistoryParam);
//
//                // 查询历史的activity里面的userTask
////                List<HistoricActivityInstance> historicActivityInstances = historyService
////                        .createHistoricActivityInstanceQuery()
////                        .processInstanceId(processInstanceId)
////                        .activityType("userTask")
////                        .orderByHistoricActivityInstanceStartTime().asc().list();
//                List<WorkflowNode> workflowNodeList = new ArrayList<WorkflowNode>();
//
////                List<Long> userIdList = new ArrayList<Long>();
////                Map<String,WxTUser> userIdMap = new HashMap<String, WxTUser>();
//                Map<String,WorkflowNode> historicActivityMap = new HashMap<String, WorkflowNode>();
//                int beginPosition = 0;
//                if(formApprovalInfos!=null && formApprovalInfos.size() >0){
//                    // 先找到最后的那个"adjust value"，后面的才是真的historicActivity
//                    for(int i=0;i<formApprovalInfos.size();i++){
//                        FormApprovalInfo formApprovalInfo = formApprovalInfos.get(i);
//                        if(CreditApplicationUtil.ADJUST_VALUE.equals(formApprovalInfo.getStepName())){
//                            beginPosition = i;
//                        }
//                    }
//                    for(int j=beginPosition;j<formApprovalInfos.size();j++){
//                        FormApprovalInfo formApprovalInfo = formApprovalInfos.get(j);
//                        WorkflowNode workflowNode = new WorkflowNode();
////                        String assignee = formApprovalInfo.getExecutionName();
//                        workflowNode.setAssignee(formApprovalInfo.getExecutionId());
//                        workflowNode.setUserName(formApprovalInfo.getExecutionName());
////                        if(assignee!=null){
////                            userIdList.add(Long.parseLong(assignee));
////                        }
//                        workflowNode.setFinished(formApprovalInfo.getEndTime() != null);
//                        Boolean approved = null;
//                        if(formApprovalInfo.getApprove() != null){
//                            approved = formApprovalInfo.getApprove() == 1?true:false;
//                        }
//                        workflowNode.setApproved(approved);
//                        boolean bAuto = (formApprovalInfo.getAuto()!=null&& formApprovalInfo.getAuto()==1)?true:false;
//                        workflowNode.setAuto(bAuto);
//                        // adjust value的被算作重新开始
//                        String activityName = CreditApplicationUtil.ADJUST_VALUE.equals(formApprovalInfo.getStepName())?CreditApplicationUtil.SUBMIT_REQUEST:formApprovalInfo.getStepName();
//                        workflowNode.setNodeName(activityName);
////                        if(historicActivityMap.get(workflowNode.getNodeName()) != null){
//////                            historicActivityMap.put(workflowNode.getNodeName() + "%j",workflowNode);
////                            historicActivityMap.put(workflowNode.getNodeName(),workflowNode);
////                        } else {
//                        historicActivityMap.put(workflowNode.getNodeName(), workflowNode);
////                        }
//                    }
//                }
//
//                // 查询那些assignee对应的userName
////                if(userIdList.size() >0){
////                    WxTUserExample wxTUserExample = new WxTUserExample();
////                    wxTUserExample.createCriteria().andUserIdIn(userIdList);
////                    List<WxTUser> userList = wxTUserMapper.selectByExample(wxTUserExample);
////                    if(userIdList != null && userIdList.size() >0){
////                        for(WxTUser wxTUser:userList){
////                            userIdMap.put(String.valueOf(wxTUser.getUserId()), wxTUser);
////                        }
////                    }
////                }
//
//               // 然后遍历activities，老的和没有执行的分开填写
//                for(ActivityImpl activity:activities){
//                    String name = (String)activity.getProperties().get("name");
//                    String type = (String)activity.getProperties().get("type");
//                    if("userTask".equals(type) && !CreditApplicationUtil.ADJUST_VALUE.equals(name)){
////                        WorkflowNode historicActivityNode = historicActivityMap.get(name.replace("%j",""));
//                        WorkflowNode historicActivityNode = historicActivityMap.get(name);
//                        if(historicActivityNode !=null){
//                            String userName = historicActivityNode.getUserName();
//                            String nodeName = historicActivityNode.getNodeName();
//                            
//                            
//                            String userNameInApprovalTeamMap = approvalTeamMap.get(historicActivityNode.getNodeName());
//                            //审批人和系统里默认的审批人不一致的时候（被recall产生的历史节点）
//                            if(!userNameInApprovalTeamMap.equals(userName)){
//                            	if(StringUtils.isNotEmpty(userName) && userName.contains("auto") && userName.contains(userNameInApprovalTeamMap)){
//                            		if(userName == null || historicActivityNode.isAuto()){
//    	                                userName = approvalTeamMap.get(nodeName);
//    	                            }
//    	                            if(historicActivityNode.isAuto()){
//    	                                // 如果是auto的应该是那个应该出现在那里的用户
//    	                                userName += "(auto)";
//    	                            }
//    	                            historicActivityNode.setUserName(userName);
//    	                            workflowNodeList.add(historicActivityNode);
//                            	} else if(StringUtils.isNotEmpty(userName)  && !userName.equals(userNameInApprovalTeamMap)){
//                                    historicActivityNode.setUserName(userNameInApprovalTeamMap);
//                                    workflowNodeList.add(historicActivityNode);
//                                }else{
//                                    WorkflowNode workflowNode = new WorkflowNode();
//                                    workflowNode.setAssignee(null);
//                                    workflowNode.setFinished(false);
//                                    workflowNode.setNodeName(name);
//                                    workflowNode.setUserName(approvalTeamMap.get(workflowNode.getNodeName()));
//                                    if(allowAdd(workflowNode,finalRequestCreditLimit,creditAppType,refValueMap,salesChannel)) {
//                                        workflowNodeList.add(workflowNode);
//                                    }
//                                }
//                            }else{	                            
//	                            if(userName == null || historicActivityNode.isAuto()){
//	                                userName = approvalTeamMap.get(nodeName);
//	                            }
//	                            if(historicActivityNode.isAuto()){
//	                                // 如果是auto的应该是那个应该出现在那里的用户
//	                                userName += "(auto)";
//	                            }
//	//                            if(historicActivityNode.getUserName() != null){
//	//                                historicActivityNode.setUserName(historicActivityNode.getUserName());
//	//                            } else {
//	                            historicActivityNode.setUserName(userName);
//	//                            }
//	                            workflowNodeList.add(historicActivityNode);
//                            }
//                        } else {
//                            WorkflowNode workflowNode = new WorkflowNode();
//                            workflowNode.setAssignee(null);
//                            workflowNode.setFinished(false);
//                            workflowNode.setNodeName(name);
//                            workflowNode.setUserName(approvalTeamMap.get(workflowNode.getNodeName()));
//                            if(allowAdd(workflowNode,finalRequestCreditLimit,creditAppType,refValueMap,salesChannel)) {
//                                workflowNodeList.add(workflowNode);
//                            }
//                        }
//                    }
//                }
//
//                // 查询还没有进行的activity
////                List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
//
//                // 对GM做一下特殊处理
//                int gmIndex = -1;
//                int analystIndex = -1;
//                WorkflowNode gmWorkflowNode = null;
//                WorkflowNode analystWorkflowNode = null;
//                for(int i = 0;i<workflowNodeList.size();i++){
//                    WorkflowNode workflowNode = workflowNodeList.get(i);
//                    if(CreditApplicationUtil.GM_TASK.equals(workflowNode.getNodeName())){
//                        gmIndex = i;
//                        gmWorkflowNode = workflowNode;
//                    } else if(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK.equals(workflowNode.getNodeName())){
//                        analystIndex = i;
//                        analystWorkflowNode = workflowNode;
//                    }
//                }
//                if(gmIndex != -1 && analystIndex!=-1 && gmIndex > analystIndex){
//                    for(int i = gmIndex;i>= analystIndex;i--){
//                        if(i == analystIndex) {
//                            workflowNodeList.set(i,gmWorkflowNode);
//                        } else  {
//                            WorkflowNode workflowNode = workflowNodeList.get(i - 1);
//                            workflowNodeList.set(i,workflowNode);
//                        }
//                    }
//                }
//
//                int checkIndex = -1;
//                int l1Index = -1;
//                WorkflowNode checkWorkflowNode = null;
//                WorkflowNode l1WorkflowNode = null;
//                for(int i = 0;i<workflowNodeList.size();i++){
//                    WorkflowNode workflowNode = workflowNodeList.get(i);
//                    if(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK.equals(workflowNode.getNodeName())){
//                        checkIndex = i;
//                        checkWorkflowNode = workflowNode;
//                    } else if(CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK.equals(workflowNode.getNodeName())){
//                        l1Index = i;
//                        l1WorkflowNode = workflowNode;
//                    }
//                }
//                if(checkIndex != -1 && l1Index!=-1 && checkIndex > l1Index){
//                    for(int i = checkIndex;i>= l1Index;i--){
//                        if(i == l1Index) {
//                            workflowNodeList.set(i,checkWorkflowNode);
//                        } else  {
//                            WorkflowNode workflowNode = workflowNodeList.get(i - 1);
//                            workflowNodeList.set(i,workflowNode);
//                        }
//                    }
//                }
//
//                jsonResponse.setListResult(workflowNodeList);
//            } else {
//                List<WorkflowNode> workflowNodeList = new ArrayList<WorkflowNode>();
//
//                for(ActivityImpl activity:activities){
//                    String name = (String)activity.getProperties().get("name");
//                    String type = (String)activity.getProperties().get("type");
//                    if("userTask".equals(type) && !CreditApplicationUtil.ADJUST_VALUE.equals(name)){
//                        WorkflowNode workflowNode = new WorkflowNode();
//                        workflowNode.setAssignee(null);
//                        workflowNode.setFinished(false);
//                        workflowNode.setNodeName(name);
//                        workflowNode.setUserName(approvalTeamMap.get(workflowNode.getNodeName()));
//                        if(allowAdd(workflowNode,finalRequestCreditLimit,creditAppType, refValueMap,salesChannel)) {
//                            workflowNodeList.add(workflowNode);
//                        }
//                    }
//                }
//                jsonResponse.setListResult(workflowNodeList);
//            }
//            // 如果是cv的credit application，需要把第二步的那个check去掉
//            if(creditAppType == CreditAppType.CV_REQUEST){
//                List<WorkflowNode> workflowNodeList = (List<WorkflowNode>) jsonResponse.get(JsonResponse.KEY_LIST);
//                if(workflowNodeList != null && workflowNodeList.size() >0){
//                    for (WorkflowNode workflowNode:workflowNodeList) {
//                        if(workflowNode.getNodeName().equals(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK)){
//                            workflowNode.setNodeName(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK);
//                            break;
//                        }
//                    }
//                }
//            }
//        }
//
//        return jsonResponse;
//    }

//    /**
//     * approvalTeam的NameMap
//     * @param aiRequestedByCai
//     * @param aiPreparedBy
//     * @return
//     */
//    private Map<String, String> getApprovalTeamNameMap(String aiRequestedByCai, Long aiPreparedBy) {
//        Map<String, String> approvalTeamMap = new HashMap<String, String>();
//        List<ApprovalTeamVo> approvalTeamByCais = wxTCreditAppRequestFormVoMapper.queryApprovalTeamByCai(aiRequestedByCai,aiPreparedBy);
//        if(approvalTeamByCais != null && approvalTeamByCais.size() >0){
//            ApprovalTeamVo approvalTeamByCai = approvalTeamByCais.get(0);
//            approvalTeamMap.put(CreditApplicationUtil.SUBMIT_REQUEST,approvalTeamByCai.getAiPreparedByName());
//            approvalTeamMap.put(CreditApplicationUtil.ADJUST_VALUE,approvalTeamByCai.getAiPreparedByName());
//            approvalTeamMap.put(CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK,approvalTeamByCai.getSupervisorMName());
//            approvalTeamMap.put(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK,approvalTeamByCai.getChannelMName());
//            approvalTeamMap.put(CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK,approvalTeamByCai.getBuMName());
//
//            approvalTeamMap.put(CreditApplicationUtil.GM_TASK,approvalTeamByCai.getGm());
//
//            approvalTeamMap.put(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK,approvalTeamByCai.getLocalCreditAnalyst());
//            approvalTeamMap.put(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK,approvalTeamByCai.getLocalCreditAnalyst());
//            approvalTeamMap.put(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK,approvalTeamByCai.getLocalCreditTeamLead());
//            approvalTeamMap.put(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK,approvalTeamByCai.getChinaFinanceManager());
//            approvalTeamMap.put(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK,approvalTeamByCai.getApCreditTeam());
//            approvalTeamMap.put(CreditApplicationUtil.AP_CREDIT_TEAM_DAVID_TASK,approvalTeamByCai.getApCreditTeam2());
//
//        }
//        return  approvalTeamMap;
//    }
//
//    private Double getCreditDollarRate(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo) {
//        return wxTCreditAppRequestFormVo.getCreditDollarRate() == null?creditCommonService.getCreditDollarRate():wxTCreditAppRequestFormVo.getCreditDollarRate();
//    }

    @Override
    @Transactional
    public JsonResponse saveAbsentInfo(WxTCreditAppUserAbsentInfoVo wxTCreditAppUserAbsentInfoVo) {
        JsonResponse jsonResponse = new JsonResponse();
        if(wxTCreditAppUserAbsentInfoVo.getUserId() == null){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            jsonResponse.setErrorMsg("userId 不能为空");
            return jsonResponse;
        }
        if(wxTCreditAppUserAbsentInfoVo.getStartTime() == null || wxTCreditAppUserAbsentInfoVo.getEndTime() == null){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
            jsonResponse.setErrorMsg("startTime 或者 endTime 不能为空");
            return jsonResponse;
        }

        try {
            Date currentDate = DateUtil.getCurrentDate();
            // null的需要insert，其他的需要update
            if(wxTCreditAppUserAbsentInfoVo.getId() == null){
                wxTCreditAppUserAbsentInfoVo.setCreateTime(currentDate);
                wxTCreditAppUserAbsentInfoVo.setUpdateTime(currentDate);
                wxTCreditAppUserAbsentInfoVo.setDeleteFlag(0);
                wxTCreditAppUserAbsentInfoVoMapper.insertSelective(wxTCreditAppUserAbsentInfoVo);
            } else {
                wxTCreditAppUserAbsentInfoVo.setUpdateTime(currentDate);
                wxTCreditAppUserAbsentInfoVoMapper.updateByPrimaryKeySelective(wxTCreditAppUserAbsentInfoVo);
            }
            jsonResponse.setDataResult(wxTCreditAppUserAbsentInfoVoMapper.selectByPrimaryKey(wxTCreditAppUserAbsentInfoVo.getId()));
        } catch (Exception e){
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("updateAbsentInfo failed:" + e.getLocalizedMessage());
            logger.error(e.getLocalizedMessage());
        }
        return jsonResponse;
    }

    @Override
    @Transactional
    public JsonResponse deleteAbsentInfo(WxTCreditAppUserAbsentInfoVo wxTCreditAppUserAbsentInfoVo) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            if (wxTCreditAppUserAbsentInfoVo.getId() == null) {
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                jsonResponse.setErrorMsg("id 不能为空");
                return jsonResponse;
            }
            // 删除这个userId下面的所有absent信息
            WxTCreditAppUserAbsentInfoVo toDeleteVo = new WxTCreditAppUserAbsentInfoVo();
            toDeleteVo.setUpdateTime(DateUtil.getCurrentDate());
            toDeleteVo.setDeleteFlag(1);
            // 查询到这条记录对应的user的id，一起删除掉
            WxTCreditAppUserAbsentInfoVo absentInfoVoResult = wxTCreditAppUserAbsentInfoVoMapper.selectByPrimaryKey(wxTCreditAppUserAbsentInfoVo.getId());
            WxTCreditAppUserAbsentInfoVoExample deleteExample = new WxTCreditAppUserAbsentInfoVoExample();
            deleteExample.createCriteria().andUserIdEqualTo(absentInfoVoResult.getUserId()).andDeleteFlagNotEqualTo(1);
            wxTCreditAppUserAbsentInfoVoMapper.updateByExampleSelective(toDeleteVo,deleteExample);
        } catch (Exception e) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("queryAbsentInfo failed:" + e.getLocalizedMessage());
            logger.error(e.getLocalizedMessage(), e);
        }
        return jsonResponse;
    }


    @Override
    public JsonResponse queryAbsentInfo(WxTCreditAppUserAbsentInfoVo wxTCreditAppUserAbsentInfoVo) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            if (wxTCreditAppUserAbsentInfoVo.getUserId() == null) {
                jsonResponse.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
                jsonResponse.setErrorMsg("userId 不能为空");
                return jsonResponse;
            }
            Map<String, Object> queryAbsentInfoMap = new HashMap<String, Object>();
            queryAbsentInfoMap.put("userId", wxTCreditAppUserAbsentInfoVo.getUserId());
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MILLISECOND, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            Date currentDate = calendar.getTime();
            queryAbsentInfoMap.put("currentDate", currentDate);
            WxTCreditAppUserAbsentInfoVo absentInfoVoResult = wxTCreditAppUserAbsentInfoVoMapper.queryAbsentInfo(queryAbsentInfoMap);
            if (absentInfoVoResult != null) {
                jsonResponse.setDataResult(absentInfoVoResult);
            } else {
                jsonResponse.setDataResult(null);
            }
        } catch (Exception e) {
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("queryAbsentInfo failed:" + e.getLocalizedMessage());
            logger.error(e.getLocalizedMessage(), e);
        }

        return jsonResponse;
    }

    private boolean allowAdd(WorkflowNode workflowNode,Double finalRequestCreditLimit, CreditAppType creditAppType,Map<String,Double> refValueMap,String salesChannel){
        boolean isAllow = false;
        if(finalRequestCreditLimit != null){
            String nodeName = workflowNode.getNodeName();
            if(creditAppType == CreditAppType.CV_REQUEST && CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK.equals(nodeName)){
                isAllow = true;
            } else if(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK.equals(nodeName)){
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK)){
                    isAllow = true;
                }
            } else if(CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK.equals(nodeName)) {
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK)){
                    isAllow = true;
                }
            }  else if(CreditApplicationUtil.GM_TASK.equals(nodeName) && "CDM".equals(salesChannel)) {
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK)){
                    isAllow = true;
                }
            } else if(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK.equals(nodeName)){
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK)){
                    isAllow = true;
                }
            } else if(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK.equals(nodeName)){
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK)){
                    isAllow = true;
                }
            } else if(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK.equals(nodeName)) {
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK)){
                    isAllow = true;
                }
            } else if(CreditApplicationUtil.AP_CREDIT_TEAM_DAVID_TASK.equals(nodeName)) {
                if(finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK)){
                    isAllow = true;
                }
            } else {
                if(CreditApplicationUtil.GM_TASK.equals(nodeName)){
                    if("CDM".equals(salesChannel) ){
                        isAllow = true;
                    }
                } else {
                    isAllow = true;
                }
            }
        } else {
            String nodeName = workflowNode.getNodeName();
            if(CreditApplicationUtil.GM_TASK.equals(nodeName)){
                if("CDM".equals(salesChannel) ){
                    isAllow = true;
                }
            } else {
                isAllow = true;
            }
        }

        return isAllow;
    }
//
//    private List<String> getHighLightedFlows(ProcessDefinitionEntity processDefinition, String processInstanceId) {
//        List<String> highLightedFlows = new ArrayList<String>();
//        // 根据processInstanceId查询，历史的historic的activity的instance，
//        // 里面有还没有被审批的节点
//        List<HistoricActivityInstance> historicActivityInstances = historyService
//                .createHistoricActivityInstanceQuery()
//                .processInstanceId(processInstanceId)
//                .orderByHistoricActivityInstanceStartTime().asc().list();
//
//        // 这里是historicActivity对应的id的列表
//        List<String> historicActivityInstanceIdList = new ArrayList<String>();
//        for (HistoricActivityInstance hai : historicActivityInstances) {
//            historicActivityInstanceIdList.add(hai.getActivityId());
//        }
//
//        // 根据processInstanceId查询，剩余的还没有执行的activity
//        List<String> highLightedActivities = runtimeService.getActiveActivityIds(processInstanceId);
//        historicActivityInstanceIdList.addAll(highLightedActivities);
//
//        //  遍历所有这个processInstance下面的所有activity
//        //  activities and their sequence-flows
//        for (ActivityImpl activity : processDefinition.getActivities()) {
//            int index = historicActivityInstanceIdList.indexOf(activity.getId());
//
//            if (index >= 0 && index + 1 < historicActivityInstanceIdList.size()) {
//                // 这个是获取这个activity的链接线
//                List<PvmTransition> pvmTransitionList = activity
//                        .getOutgoingTransitions();
//                // 遍历这些连接线
//                for (PvmTransition pvmTransition : pvmTransitionList) {
//                    // 获得这个连接线的目的地destination的id
//                    String destinationFlowId = pvmTransition.getDestination().getId();
//                    if (destinationFlowId.equals(historicActivityInstanceIdList.get(index + 1))) {
//                        highLightedFlows.add(pvmTransition.getId());
//                    }
//                }
//            }
//        }
//        return highLightedFlows;
//    }




//	@Override
//	public  List<CreditAppRequestDetailView> getCreditAppRequestFullInfoByCondition(
//			CreditListQueryParam params) {
//		
//		List<CreditAppRequestDetailView> results = new ArrayList<CreditAppRequestDetailView>();
//		try {
//			 results = wxTCreditAppRequestFormVoMapper.getCreditAppRequestFullInfoByCondition(params);
//		} catch (Exception e) {
//			e.printStackTrace();
//            
//        }
//
//		
//		return results;
//	}

//    /**
//     *
//     * @param wxTCreditAppRequestFormVo
//     * @param approve
//     * @param preTaskName
//     */
//    private void sendMail(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo,Boolean approve,String preTaskName){
//        ServletContext servletContext = ContextLoader.getCurrentWebApplicationContext().getServletContext();
//        // 查询下一个任务节点的名称
//        String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
//        List<ActRuTaskVo> unsignedTasks = getActRuTaskVos(wxTCreditAppRequestFormVo.getCreditType(),processInstanceId,null);
//
//        CreditAppType creditAppType = CreditAppType.valueOf(wxTCreditAppRequestFormVo.getCreditType());
//        Map<String, Long> approvalTeamIdsMap = getApprovalTeamIdsMap(wxTCreditAppRequestFormVo);
//        // cc用户的email的列表
//        
////        if(backupUserIds!=null && backupUserIds.size() >0){
////            WxTUserExample wxTUserExample = new WxTUserExample();
////            wxTUserExample.createCriteria().andUserIdIn(backupUserIds);
////            List<WxTUser> backupUsers = wxTUserMapper.selectByExample(wxTUserExample);
////            if(backupUserIds!=null && backupUserIds.size() >0){
////                for(WxTUser backupUser:backupUsers){
////                    if(StringUtils.isNotBlank(backupUser.getEmail())){
////                        ccUsersMails.add(backupUser.getEmail());
////                    }
////                }
////            }
////        }
//        if (unsignedTasks != null && unsignedTasks.size() > 0) {
//        	List<String> ccUsersMails = new ArrayList<String>();
//            ActRuTaskVo nextTask = unsignedTasks.get(0);
//            String nextTaskId = nextTask.getId();
//            String nextTaskName = nextTask.getName();
//            List<String> toUsersMails = new ArrayList<String>();
//
//            if(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK.equals(nextTaskName) ||
//                    CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK.equals(nextTaskName) ||
//                    CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK.equals(nextTaskName) ){
//                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(nextTaskId);
//                if(identityLinks!=null && identityLinks.size() >0){
//                    for(IdentityLink identityLink:identityLinks) {
//                        if(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK.equals(nextTaskName) && identityLink.getGroupId().equals("China_Finance_Manager")){
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK);
//                        } else  if(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK.equals(nextTaskName)  && identityLink.getGroupId().equals("Local_Credit_Team_Lead")){
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK);
//                        }  else  if(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK.equals(nextTaskName)  && identityLink.getGroupId().equals("AP_Credit_Team_backup")){
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.AP_CREDIT_TEAM_GRACE_BACKUP);
//                        }
//                    }
//                }
//            }
//
//            Long toUserId = approvalTeamIdsMap.get(nextTaskName);
//            WxTUser toUser = wxTUserMapper.selectByPrimaryKey(toUserId);
//            String toUserEmail = toUser.getEmail();
//
//            String subjectName = String.format("Customer " + wxTCreditAppRequestFormVo.getCbiCustomerId() + " - Credit Approval Notification 信用申请通知提醒");
//            Map<String, Object> emailInfoMap = getMailInfoMap(wxTCreditAppRequestFormVo, toUser.getChName(), creditAppType, approve);
//
//            if (StringUtils.isNotBlank(toUserEmail)) {
//                toUsersMails.add(toUserEmail);
//                // 如果是local credit analyst 和 local credit team leader共享的任务,poppy和Rachel都应该收到email
//                if (CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK.equals(nextTaskName) || CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK.equals(nextTaskName)) {
//                    addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK);
//                }
//                if (approve) {
//                    // 如果是approve的，会根据下一个任务的情况来增加ccUsersMails              	              	
//                    // approve的话，就是直接往下一级
//                    EmailSendUtils.sendEmailForListContent(servletContext,
//                            toUsersMails.toArray(new String[toUsersMails.size()]),
//                            ccUsersMails.toArray(new String[ccUsersMails.size()]), subjectName,
//                            emailInfoMap, null, creditAppType.getMailTemplate());
//                } else {
//                    // 如果是打回的
//                    toUser = wxTUserMapper.selectByPrimaryKey(wxTCreditAppRequestFormVo.getAiPreparedBy());
//                    toUserEmail = toUser.getEmail();
//                    toUsersMails = new ArrayList<String>();
//                    // level1 在credit及之后如果被打回的话，一定会有
//                    if(!CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK.equals(preTaskName) && !CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK.equals(preTaskName)
//                         && !CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK.equals(preTaskName) && !CreditApplicationUtil.GM_TASK.equals(preTaskName)){
//                        addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK);
//                    }
//                    Double finalRequestCreditLimit = wxTCreditAppRequestFormVo.getFinalRequestCreditLimit(creditAppType, getCreditDollarRate(wxTCreditAppRequestFormVo));
//                    Map<String, Double> refValueMap = queryCreditLimitRefValueMap();
//                    if (CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK.equals(preTaskName)) {
//                        // 判断l2是否加入
//                        if (finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK)
//                                && finalRequestCreditLimit <= refValueMap.get(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK)) {
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK);
//                        }
//                    } else if(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK.equals(preTaskName)){
//                        // 判断l3是否加入
//                        // 判断l2是否加入
//                        if (finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK)) {
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK);
//                        }
//                    } else if(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK.equals(preTaskName)){
//                        // 判断l3是否加入
//                        // 判断l2是否加入
//                        if (finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK)) {
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK);
//                        }
//                    } else if(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK.equals(preTaskName)){
//                        // 判断l3是否加入
//                        // 判断l2是否加入
//                        if (finalRequestCreditLimit > refValueMap.get(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK)) {
//                            addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK);
//                        }
//                        addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK);
//                        addEmailByRole(approvalTeamIdsMap, ccUsersMails, CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK);
//                    }
//                    emailInfoMap = getMailInfoMap(wxTCreditAppRequestFormVo, toUser.getChName(), creditAppType, approve);
//                    if (StringUtils.isNotBlank(toUserEmail)) {
//                        toUsersMails.add(toUserEmail);                  
//                        subjectName = String.format("Customer " + wxTCreditAppRequestFormVo.getCbiCustomerId() + " - Credit Approval Notification 信用申请通知提醒");
//                        EmailSendUtils.sendEmailForListContent(servletContext,
//                                toUsersMails.toArray(new String[toUsersMails.size()]),
//                                ccUsersMails.toArray(new String[ccUsersMails.size()]), subjectName,
//                                emailInfoMap, null, creditAppType.getMailTemplate());
//                    }
//
//                }
//            }
//        }else{
//        	if(wxTCreditAppRequestFormVo.getProcessStatus().equals(ProcessStatusType.APPROVED.getValue())){
//        		//发动邮件
//            	Map<String, Object> abmAboveMap = creditCommonService.getCreditABMAboveWorkFlowNodes();
//            	
//            	CreditListQueryParam param = new CreditListQueryParam();
//            	param.setApplicationId(wxTCreditAppRequestFormVo.getId());
//            	JsonResponse jr =  queryApprovalHistory(param);
//            	List<FormApprovalInfo> formApprovalInfos = (List<FormApprovalInfo>)jr.get("resultLst");
//            	//获取审批者
//            	Set<String> userIdSet = new HashSet<String>();
//            	for(FormApprovalInfo formApprovalInfo : formApprovalInfos){
//            		if(abmAboveMap.containsKey(formApprovalInfo.getStepName())
//            				|| abmAboveMap.containsKey(formApprovalInfo.getExecutionId())){
//            			continue;
//            		}else{
//            			if(StringUtils.isNotEmpty(formApprovalInfo.getExecutionId())){
//            				userIdSet.add(formApprovalInfo.getExecutionId());
//            			}
//            		}
//            	}
//            	//添加CSR的userId
//            	String csrUserId = wxTCreditAppRequestFormVo.getCbiCreditCsr();
//            	if(!StringUtils.isEmpty(csrUserId)){
//                	String[] csrUserIdArray = csrUserId.split(",");
//                	List<String> csrUserIdArrayList = Arrays.asList(csrUserIdArray);
//                	for(String csruserId : csrUserIdArrayList){
//                		userIdSet.add(csruserId);
//                	}
//            	}
//            	//添加申请的销售
//            	String aiRequestBy = wxTCreditAppRequestFormVo.getAiRequestedBy();
//            	Map<String, Object> userInfoMap = creditCommonService.getApplicationRequestedInformation(aiRequestBy);
//            	userIdSet.add(String.valueOf((Long)userInfoMap.get("userId")));
//            	
//            	String subjectName = String.format("Customer " + wxTCreditAppRequestFormVo.getCbiCustomerId() + " - Credit Approval Notification 信用申请通知提醒");
//                
//            	//获取审核者的邮箱
//            	Map<String, Object> emailMap = creditCommonService.getCreditReleaserEmail();
//            	
//            	
//            	List<String>  toUsersMails = (List<String>)emailMap.get("emailList");
//            	List<String>  ccUsersMails = new ArrayList<String>();
//            	WxTUser toUser = wxTUserMapper.selectByPrimaryKey(wxTCreditAppRequestFormVo.getAiPreparedBy());
//            	
//            	for(String userId : userIdSet){
//            		WxTUser ccUser = wxTUserMapper.selectByPrimaryKey(Long.valueOf(userId));
//            		if(!StringUtils.isEmpty(ccUser.getEmail()) && !toUsersMails.contains(ccUser.getEmail())){
//            			toUsersMails.add(ccUser.getEmail());
//            		}
//            	}
//            	
//            	/*CreditAppType creditAppType = CreditAppType.valueOf(wxTCreditAppRequestFormVo.getCreditType());*/
//            	Map<String, Object> emailInfoMap = getMailInfoMap(wxTCreditAppRequestFormVo, toUser.getChName(), creditAppType, true);
//            	emailInfoMap.put("release", false);
//            	emailInfoMap.put("tobeRelease", true);
//            	/*emailInfoMap.put("toUser",emailMap.get("userName"));*/
//            	emailInfoMap.put("toUser","all");
//            	
//            	// 如果是approve的，会根据下一个任务的情况来增加ccUsersMails
//                // approve的话，就是直接往下一级
//            	/*ServletContext servletContext = ContextLoader.getCurrentWebApplicationContext().getServletContext();*/
//                EmailSendUtils.sendEmailForListContent(servletContext,
//                        toUsersMails.toArray(new String[toUsersMails.size()]),
//                        ccUsersMails.toArray(new String[ccUsersMails.size()]), subjectName,
//                        emailInfoMap, null, creditAppType.getMailTemplate());
//        		
//        	}
//        }
        // 查询team的内容c
        // 如果是approve的
        // 如果是被驳回的
//        ServletContext servletContext = ContextLoader.gtCurrentWebApplicationContext().getServletContext();
//        // approve是false的话，根据processInstanceId查询历史
//        // approve是true的话，根据processInstanceId查询未来的
//        Task task = taskService.createTaskQuery().taskId("1").singleResult();
//        task.get
//        boolean isSendback = passType == SENDBACK_PASS_TYPE;
//
//        String subjectName = organName + "Q" + quarter + "季度的QBR" + (isSendback?"被驳回":"待审批");
//        if(nextAction!=null && nextAction.indexOf("审核流程已经走完") != -1) {
//            subjectName = organName + "Q" + quarter + "季度的QBR" + "审核流程已经走完";
//        }
//        Map<String, Object> emailInfoMap = new HashMap<String, Object>();
//        emailInfoMap.put("toUserName",toUserName);
//        emailInfoMap.put("curAction",curAction);
//        emailInfoMap.put("nextAction",nextAction);
//        emailInfoMap.put("organizationName",organName);
//        emailInfoMap.put("quarter", quarter);
//        EmailSendUtils.sendEmailForListContent(servletContext, emailList.toArray(new String[emailList.size()]), null, subjectName,
//                emailInfoMap, null, "access_info_status_change.ftl");
//    }
//
//    private void addEmailByRole(Map<String, Long> approvalTeamMap, List<String> ccUsersMails, String taskName) {
//        WxTUser creditAnalystUser = wxTUserMapper.selectByPrimaryKey(approvalTeamMap.get(taskName));
//        if (StringUtils.isNotBlank(creditAnalystUser.getEmail())) {
//            ccUsersMails.add(creditAnalystUser.getEmail());
//        }
//    }
//
//    private Map<String,Long> getApprovalTeamIdsMap(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo){
//        Map<String,Long>  approvalTeamMap = new HashMap<String, Long>();
//        if(StringUtils.isNotBlank(wxTCreditAppRequestFormVo.getAiRequestedBy())){
//            String aiRequestedByCai = wxTCreditAppRequestFormVo.getAiRequestedBy();
//            Long aiPreparedBy = wxTCreditAppRequestFormVo.getAiPreparedBy();
//            String[] frontBrackets = wxTCreditAppRequestFormVo.getAiRequestedBy().split("\\(");
//            if(frontBrackets.length >=2){
//                String[] endBrackets = frontBrackets[frontBrackets.length-1].split("\\)");
//                if(endBrackets.length > 0){
//                    aiRequestedByCai = endBrackets[0];
//                }
//            }
//
//            List<ApprovalTeamIdVo> approvalTeamByCais = wxTCreditAppRequestFormVoMapper.queryApprovalTeamIdsByCai(aiRequestedByCai,aiPreparedBy);
//            if(approvalTeamByCais != null && approvalTeamByCais.size() >0){
//                ApprovalTeamIdVo approvalTeamByCai = approvalTeamByCais.get(0);
//                approvalTeamMap.put(CreditApplicationUtil.SUBMIT_REQUEST,approvalTeamByCai.getAiPreparedById());
//                approvalTeamMap.put(CreditApplicationUtil.ADJUST_VALUE,approvalTeamByCai.getAiPreparedById());
//                approvalTeamMap.put(CreditApplicationUtil.L1_BU_AREA_SALES_MANAGER_TASK,approvalTeamByCai.getSupervisorId());
//                approvalTeamMap.put(CreditApplicationUtil.L2_BU_SALES_MANAGER_TASK,approvalTeamByCai.getChannelMId());
//                approvalTeamMap.put(CreditApplicationUtil.L3_ABM_SALES_MANAGER_TASK,approvalTeamByCai.getBuId());
//
//                approvalTeamMap.put(CreditApplicationUtil.GM_TASK,approvalTeamByCai.getGmId());
//
//                approvalTeamMap.put(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_CHECK_TASK,approvalTeamByCai.getLocalCreditAnalystId());
//                approvalTeamMap.put(CreditApplicationUtil.LOCAL_CREDIT_ANALYST_TASK,approvalTeamByCai.getLocalCreditAnalystId());
//                approvalTeamMap.put(CreditApplicationUtil.LOCAL_CREDIT_TEAM_LEADER_TASK,approvalTeamByCai.getLocalCreditTeamLeadId());
//                approvalTeamMap.put(CreditApplicationUtil.CHINA_FINANCE_MANAGER_TASK,approvalTeamByCai.getChinaFinanceManagerId());
//                approvalTeamMap.put(CreditApplicationUtil.AP_CREDIT_TEAM_APRIL_TASK,approvalTeamByCai.getApCreditTeamId());
//                approvalTeamMap.put(CreditApplicationUtil.AP_CREDIT_TEAM_GRACE_BACKUP,approvalTeamByCai.getApCreditTeamBackupId());
//                approvalTeamMap.put(CreditApplicationUtil.AP_CREDIT_TEAM_DAVID_TASK,approvalTeamByCai.getApCreditTeam2Id());
//            }
//        }
//        return approvalTeamMap;
//    }
//
//
//    private Map<String, Object> getMailInfoMap(WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo,String toUser, CreditAppType creditAppType, Boolean approve) {
//        Map<String, Object> mailInfoMap = new HashMap<String, Object>();
//        mailInfoMap.put("approve",approve);
//        mailInfoMap.put("release", false);
//        mailInfoMap.put("tobeRelease", false);
//        mailInfoMap.put("createTime", DateUtils.getDateStr(wxTCreditAppRequestFormVo.getCreateTime(),"yyyy/MM/dd"));
//        mailInfoMap.put("aiRequestedBy",wxTCreditAppRequestFormVo.getAiPreparedByName());
//        mailInfoMap.put("regionName",wxTCreditAppRequestFormVo.getAiSalesTeam());
//        mailInfoMap.put("currency","CHINA CNY");
//        mailInfoMap.put("sapCode",wxTCreditAppRequestFormVo.getCbiCustomerId());
//        mailInfoMap.put("toUser",toUser);
//        if (creditAppType == CreditAppType.ANNUAL_CREDIT_REVIEW) {
//        	mailInfoMap.put("cfiConfirmedPaymentTermOfCurrentYear", 
//        			wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedPaymentTermOfCurrentYear() ==null?"N/A":wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedPaymentTermOfCurrentYear());
//            mailInfoMap.put("cfiConfirmedCreditLimitOfCurrentYear",
//            		wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedCreditLimitOfCurrentYear() ==null?"N/A":wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedCreditLimitOfCurrentYear());
//        } else if (creditAppType == CreditAppType.TEMP_CREDIT_REQUEST) {
//        	mailInfoMap.put("cfiConfirmedTempPaymentTerm", 
//        			wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedTempPaymentTerm() 
//        				== null ?"N/A":wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedTempPaymentTerm());
//        	
//        	mailInfoMap.put("cfiConfirmedTempCreditLimit",
//        			wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedTempCreditLimit() 
//    				== null ?"N/A":wxTCreditAppRequestFormVo.getCfiInfo().getCfiConfirmedTempCreditLimit());
//        } else if (creditAppType == CreditAppType.CV_REQUEST) {
//            mailInfoMap.put("cbiRequestedCvOrderNo",wxTCreditAppRequestFormVo.getCbiRequestedCvOrderNo() == null ? "N/A":wxTCreditAppRequestFormVo.getCbiRequestedCvOrderNo());
//            mailInfoMap.put("cfiCvAmount",wxTCreditAppRequestFormVo.getCfiInfo().getCfiCvAmount() == null ? "N/A":wxTCreditAppRequestFormVo.getCfiInfo().getCfiCvAmount());
//        }
//        return mailInfoMap;
//    }
    
//	@Autowired
//	private ManagementService managerService;
//	
//	/**
//	 * 撤回流程
//	 * @throws Exception 
//	 * @throws NumberFormatException 
//	 */
//	@Override
//	public Map<String, Object> recall(String applicationId, String assignee) throws NumberFormatException, Exception {
//		
//		Map<String,Object> resultMap = new HashMap<String,Object>();
//		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		
//		WxTCreditAppRequestFormVo wxTCreditAppRequestFormVo = getApplicationDetailById(Long.valueOf(applicationId));
//		
//		String processInstanceId = wxTCreditAppRequestFormVo.getProcessInstanceId();
//		List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).
//				taskAssignee(assignee).orderByTaskCreateTime().desc().list();
//		if(historicTaskInstanceList == null || historicTaskInstanceList.isEmpty()){
//			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "此流程当前用户没有已处理流程节点");
//			return resultMap;
//		}
//		HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(0);
//
//		/*List<HistoricTaskInstance> historicTaskInstanceAllList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByTaskCreateTime().desc().list();
//		HistoricTaskInstance historicTaskInstanceLatested = historicTaskInstanceAllList.get(1);
//		HistoricTaskInstance historicTaskInstanceSeconedLatested = historicTaskInstanceAllList.get(2);*/
//
//		/*List<FormApprovalInfo> formApprovalInfos = getApprovalHistoryByProcessId(applicationId);
//		FormApprovalInfo approval1 = formApprovalInfos.get(1);
//		FormApprovalInfo approval2 = formApprovalInfos.get(2);
//		//最新审批是自动审批,判断最新第2个审批的执行人是否是当前登录人
//		if(approval1.getExecutionName().contains("auto")){
//			//当前登录人是最新审批人
//			if(approval1.getExecutionId().equals(assignee)){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "此流程已被你当前自动审批，不能Recall");
//				return resultMap;
//			}
//			//当前登录人不是最新审批者
//			else{
//				//最新第二审批者也不是当前登录人
//				if(!approval2.getExecutionId().equals(assignee)){
//					resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//					resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "此流程已被其他人处理");
//					return resultMap;
//				}
//			}
//		}
//		//最新审批是不是自动审批
//		else{
//			//如果最新审批是recall，不能再次recall
//			if(approval1.getApprove() == 2){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "此流程当前处于Recall状态，不能重复Recall");
//				return resultMap;
//			}//如果最新审批是reject，不能再次recall
//			if(approval1.getApprove() == null || approval1.getApprove() == 0){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "此流程当前处于Reject状态，不能Recall");
//				return resultMap;
//			}
//			//最新审批是当前登录人
//			if(!approval1.getExecutionId().equals(assignee)){
//				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "此流程已被其他人处理");
//				return resultMap;
//			}
//		}
//		*/
//		// 取得流程实例
//		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
//				.processInstanceId(historicTaskInstance.getProcessInstanceId()).singleResult();
//		System.out.println(processInstance.getId());
//		Map<String, Object> variables = runtimeService.getVariables(historicTaskInstance.getExecutionId());
//		System.out.println(variables);
//		// 取得流程定义
//		ProcessDefinitionEntity definitionEntity = (ProcessDefinitionEntity) repositoryService
//				.getProcessDefinition(historicTaskInstance.getProcessDefinitionId());
//		System.out.println(definitionEntity);
//		// 取得上一步活动
//		
//		Map<String, Double> refValueMap = queryCreditLimitRefValueMap();
//		ProcessInfo processInfo = new ProcessInfo();
//		processInfo.setApprove(false);
//		processInfo.setComment("recall");
//		wxTCreditAppRequestFormVo.setProcessInfo(processInfo);
//		CreditAppType creditAppType = CreditAppType.valueOf(wxTCreditAppRequestFormVo.getCreditType());
//		/*Map<String,Object> taskVariables = getCompleteTaskVarMap(wxTCreditAppRequestFormVo,
//				creditAppType,
//				true,
//				refValueMap.get(historicTaskInstance.getName()),
//				false,
//				"recall");*/
//		
//		Map<String, Object> completeTaskVariables = new HashMap<String, Object>();
//		completeTaskVariables.put("comment","recall");
//		completeTaskVariables.put("approve","false");
//		completeTaskVariables.put("creditLimit",wxTCreditAppRequestFormVo.getFinalRequestCreditLimit(creditAppType,getCreditDollarRate(wxTCreditAppRequestFormVo)));
//		completeTaskVariables.put("isCdm",true);
//		completeTaskVariables.put("refValue",refValueMap.get(historicTaskInstance.getName()));
//		completeTaskVariables.put("auto",false);
//		completeTaskVariables.put("actualExecutorId", ContextUtil.getCurUserId());
//		
//		Task newestTask = taskService.createTaskQuery().processInstanceId(processInstanceId)
//				.orderByTaskCreateTime().desc().singleResult();
//		
//		ActivityImpl hisActivity = definitionEntity.findActivity(historicTaskInstance.getTaskDefinitionKey());
//		System.out.println(hisActivity);
//		
//		List<PvmTransition> currTransitionList = hisActivity.getOutgoingTransitions();
//		System.out.println(currTransitionList);
//		
//		boolean contailGateway = true;
//		for (PvmTransition currTransition : currTransitionList) {
//			PvmActivity currActivity = currTransition.getDestination();
//			List<Task> currTasks = taskService.createTaskQuery().processInstanceId(processInstance.getId())
//					.taskDefinitionKey(currActivity.getId()).list();
//			if(currTasks != null && !currTasks.isEmpty()){
//				contailGateway = false;
//			}
//			for (Task currTask : currTasks) {
//				if(currTask.getId().equals(historicTaskInstance.getId())){
//					continue;
//				}
//				ArrayList<PvmTransition> oriPvmTransitionList = new ArrayList<PvmTransition>();
//				List<PvmTransition> pvmTransitionList = currActivity.getOutgoingTransitions();
//				System.out.println(pvmTransitionList);
//				for (PvmTransition pvmTransition : pvmTransitionList) {
//					oriPvmTransitionList.add(pvmTransition);
//				}
//				System.out.println(oriPvmTransitionList);
//				pvmTransitionList.clear();
//				// 建立新方向
//				ActivityImpl nextActivityImpl = ((ProcessDefinitionImpl) definitionEntity)
//						.findActivity(currActivity.getId());
//				TransitionImpl newTransition = nextActivityImpl.createOutgoingTransition();
//				newTransition.setDestination(hisActivity);
//				taskService.claim(currTask.getId(), assignee);
//				taskService.setVariablesLocal(currTask.getId(), completeTaskVariables);
//				taskService.complete(currTask.getId(), completeTaskVariables);
//				historyService.deleteHistoricTaskInstance(currTask.getId());
//
//				// 恢复方向
//				hisActivity.getIncomingTransitions().remove(newTransition);
//				List<PvmTransition> pvmTList = currActivity.getOutgoingTransitions();
//				pvmTList.clear();
//				for (PvmTransition pvmTransition : oriPvmTransitionList) {
//					pvmTransitionList.add(pvmTransition);
//				}
//				System.out.println(pvmTransitionList);
//			}
//		}
//		if(contailGateway){
//			Set<String> taskIdFinished = new HashSet<String>();
//			
//			
//			ActivityImpl newhisActivity = definitionEntity.findActivity(newestTask.getTaskDefinitionKey());
//			System.out.println(newhisActivity);
//			
//			List<PvmTransition> inCommoningTransitionList = newhisActivity.getIncomingTransitions();
//			System.out.println(inCommoningTransitionList);
//			
//			for (PvmTransition currTransition : inCommoningTransitionList) {
//				PvmActivity currActivity = currTransition.getDestination();
//				List<Task> currTasks = taskService.createTaskQuery().processInstanceId(processInstance.getId())
//						.taskDefinitionKey(currActivity.getId()).list();				
//				for (Task currTask : currTasks) {
//					if(currTask.getId().equals(historicTaskInstance.getId())){
//						continue;
//					}
//					if(taskIdFinished.contains(currTask.getId())){
//						continue;
//					}else{
//						taskIdFinished.add(currTask.getId());
//					}
//					ArrayList<PvmTransition> oriPvmTransitionList = new ArrayList<PvmTransition>();
//					List<PvmTransition> pvmTransitionList = currActivity.getOutgoingTransitions();
//					System.out.println(pvmTransitionList);
//					for (PvmTransition pvmTransition : pvmTransitionList) {
//						oriPvmTransitionList.add(pvmTransition);
//					}
//					System.out.println(oriPvmTransitionList);
//					pvmTransitionList.clear();
//					// 建立新方向
//					ActivityImpl nextActivityImpl = ((ProcessDefinitionImpl) definitionEntity)
//							.findActivity(currActivity.getId());
//					TransitionImpl newTransition = nextActivityImpl.createOutgoingTransition();
//					newTransition.setDestination(hisActivity);
//					taskService.claim(currTask.getId(), assignee);
//					taskService.setVariablesLocal(currTask.getId(), completeTaskVariables);
//					taskService.complete(currTask.getId(), completeTaskVariables);
//					historyService.deleteHistoricTaskInstance(currTask.getId());
//
//					// 恢复方向
//					hisActivity.getIncomingTransitions().remove(newTransition);
//					List<PvmTransition> pvmTList = currActivity.getOutgoingTransitions();
//					pvmTList.clear();
//					for (PvmTransition pvmTransition : oriPvmTransitionList) {
//						pvmTransitionList.add(pvmTransition);
//					}
//					System.out.println(pvmTransitionList);
//				}
//			}
//		}		
//		historyService.deleteHistoricTaskInstance(historicTaskInstance.getId());
//		
//		return resultMap;
//	}

	@Override
	public Map<String, Object> updateReleaseOrderStatusById(Long applicationId) throws Exception {
		ResponseMap resultMap = new ResponseMap();
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
        logger.info("updateReleaseOrderStatusById: " + applicationId);
        try {
            creditAppRequestFormBizService.updateReleaseOrderStatusById(applicationId);
            logger.info("updateReleaseOrderStatusById success.");
        } catch (WxPltException e) {
        	resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
        	resultMap.setErrorMsg(e.getMessage());
        } catch (Exception e) {
        	resultMap.handleException(e, this, ContextUtil.getCurUserId(),
                    getClass().getName() + ".updateReleaseOrderStatusById", applicationId.toString());
        }
        
        
		return resultMap;
	}   
}
