select o.organization_name "经销商名称", u.ch_name "DSR", w.id "门店ID", w.work_shop_name "门店名称", 
--(case when w.status='0' then '潜在' when w.status='3' then '合作' else w.status end) "门店状态", 
w.create_time "创建时间"  from wx_t_work_shop w
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_organization o on o.id=wp.partner_id
left join wx_t_user u on u.user_id=w.excute_user_id
where w.from_source=1 --and w.status in ('3') 
and w.delete_flag=0 and o.id!=9
order by o.id, w.excute_user_id