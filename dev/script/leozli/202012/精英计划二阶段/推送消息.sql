update m set m.message_receivers=(select '[' + substring((
 SELECT ','+('{"userId":'+cast(u.user_id as varchar(90)) + ',"chName":"' + u.ch_name + '"}')  from wx_t_user u join wx_t_organization o on o.id=u.org_id
left join wx_t_userrole ur on u.user_id=ur.user_id
left join wx_t_role r on r.role_id=ur.role_id
where o.type=1 and (u.type is null or u.type != '1')
and u.user_id in (129892,
122710,
129916,
122710,
129916,
122710,
129916,
129916,
129916,
65921,
122710,
129892,
129892,
129892,
65921,
132163,
122710,
65921,
132163,
129916,
122710,
65921,
129916,
122710,
129916,
132163,
129916,
129892,
65921,
122710,
129892,
129916,
132163,
123555,
128918,
123580,
125430,
125522,
125402,
125400,
127058,
128850,
125409,
123549,
127057,
132273,
123567,
125358,
125379,
128910,
123568,
126748,
125360,
125354,
125434,
127288,
125367,
128913,
125377,
123569,
130789,
126574,
123488,
125020,
126749,
123559,
127095,
125404,
126540,
125351,
123770,
123578,
125329,
125439,
123552,
125431,
126822,
124513,
126461,
125389,
125390,
125436,
123551,
125366,
124511,
125353,
125437,
125407,
123562,
125398,
123764,
128919,
127549,
121050,
125356,
123572,
65773,
125412,
123489,
127389,
126755,
126560,
126818,
124976,
125403,
127055,
123490,
125375,
125426,
125433,
125350,
124514,
123573,
125392,
125397,
76467,
123574,
130805,
125419,
125415,
125408,
123548,
131459,
131281,
131096,
131644,
131444,
131678,
131479,
131680,
131589,
131682,
131777,
131787,
131836,
131835,
131934,
131925,
131926,
131963,
131984,
132123,
132111,
132149,
132201,
132283,
132323)
and u.status=1 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000) + ']') from wx_message m where m.id=24953