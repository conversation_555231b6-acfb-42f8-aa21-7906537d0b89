select  v.customer_name_cn "经销商名称", w.work_shop_name "门店名称", we.name "技师", pd.creation_time "扫码时间", pd.POINT_VALUE "获取积分"
from wx_t_point_value_detail pd left join wx_t_point_value_detail_log dl on dl.POINT_VALUE_ID=pd.id /*where 1=1 and
id in (select t.id from wx_t_point_value_detail t LEFT JOIN dbo.wx_t_point_business pb ON t.BUSINESS_ID = pb.ID 
where 1=1 and '2020-06-11' <= t.CREATION_TIME and pb.EARN_TYPE='QR_CODE' )*/
LEFT JOIN dbo.wx_t_point_business pb ON pd.BUSINESS_ID = pb.ID 
left join wx_t_point_account pc on pc.id=pd.point_account_id and pc.POINT_ACCOUNT_TYPE='TC'
left join wx_t_workshop_employee we on pc.POINT_ACCOUNT_OWNER_CODE=we.code
left join wx_t_workshop_partner wp on wp.workshop_id=we.workshop_id left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join view_customer_region_sales_channel v on v.distributor_id=pe.distributor_id 
left join wx_t_work_shop w on w.id=we.workshop_id
where v.region_name='SC' and pc.id=pd.POINT_ACCOUNT_ID and pd.CREATION_TIME>='2020-06-11' and pb.EARN_TYPE='QR_CODE' and pd.POINT_VALUE=32
order by wp.partner_id, w.id, pd.CREATION_TIME