package com.chevron.b2b.dao;

import com.chevron.b2b.model.ScheduleEntity;
import com.chevron.b2b.model.ScheduleEntityExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 计划实体-wx_t_schedule_entity数据库操作对象
 * <AUTHOR>
 * @version 1.0 2019-01-09 22:26
 */
public interface ScheduleEntityMapper {

	/**
	 * 根据主键更新计划实体对象(非空属性)
	 * @param record 新计划实体对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(ScheduleEntity record);

    /**
     * 根据主键删除计划实体对象
     * @param id 删除计划实体对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询计划实体对象
	 * @param id 计划实体对象主键值
	 * @return 计划实体对象结果
	 */
    ScheduleEntity selectByPrimaryKey(Long id);

 	/**
	 * 插入计划实体对象(非空属性)
	 * @param record 被插入计划实体对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(ScheduleEntity record);

	/**
	 * 更新计划实体对象
	 * @param record 被修改计划实体对象
	 * @param example 过滤计划实体对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") ScheduleEntity record, @Param("example") ScheduleEntityExample example);

	/**
	 * 删除计划实体对象
	 * @param example 被删除计划实体对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleEntityExample example);
    
 	/**
	 * 查询满足条件的计划实体对象数量
	 * @param example 过滤计划实体对象条件
	 * @return 满足条件计划实体对象数量
	 */
    int countByExample(ScheduleEntityExample example);

	/**
	 * 单表查询计划实体对象
	 * @param example 过滤计划实体对象条件
	 * @return 满足条件计划实体对象集合
	 */
    List<ScheduleEntity> selectByExample(ScheduleEntityExample example);

	/**
	 * 列表查询计划实体对象
	 * @param params 查询条件
	 * @return 满足条件计划实体对象集合
	 */
    List<ScheduleEntity> queryByParams(Map<String, Object> params);

    /**
     * 批量插入计划实体对象
     * @param records 插入计划实体集合
     * @return 数据库操作影响记录条数
     */
	int insertBatch(@Param("records") List<ScheduleEntity> records);
}
