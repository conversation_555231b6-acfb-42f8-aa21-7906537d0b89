select distinct pior1.code, po1.create_time from wx_t_partner_inventory_out_record pior1 
join wx_t_partner_order_address_product ap1 on ap1.out_stock_no=pior1.stock_out_no 
join wx_t_partner_order po1 on po1.id=ap1.partner_order_id 
where po1.partner_id=8 order by po1.create_time desc

select sum(pol1.amount * convert(int, p.capacity)), p.sku, p.oil_type, p.viscosity, p.capacity 
from wx_t_partner_order po1 join wx_t_partner_order_line pol1 on pol1.partner_order_id=po1.id
join wx_t_product p on p.sku=pol1.sku
where po1.create_time>='2018-01-01' and po1.create_time<'2018-02-01' and po1.partner_id=8
group by p.sku, p.oil_type, p.viscosity, p.capacity
order by p.oil_type, p.viscosity

select distinct pior1.code, po1.create_time from wx_t_partner_inventory_out_record pior1 
join wx_t_partner_order_address_product ap1 on ap1.out_stock_no=pior1.stock_out_no 
join wx_t_partner_order po1 on po1.id=ap1.partner_order_id
where po1.partner_id=8 and po1.create_time>='2018-01-01' and po1.create_time<'2018-02-01'
and pior1.code_type='logistic' 
order by po1.create_time desc

update cb set cb.creation_time=null from wx_t_qr_code_batch cb where finish_time<'2018-04-18 16:17' and finish_time>'2018-04-18 16:09'