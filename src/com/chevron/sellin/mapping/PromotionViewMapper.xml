<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.sellin.dao.PromotionViewMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.chevron.sellin.model.PartnerPromotionVo">
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
        <result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
        <result column="promotion_name" property="promotionName" jdbcType="VARCHAR"/>
        <result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
        <result column="point_type" property="pointType" jdbcType="VARCHAR"/>
        <result column="point_code" property="pointCode" jdbcType="VARCHAR"/>
        <result column="point_name" property="pointName" jdbcType="VARCHAR"/>
        <result column="balance" property="balance" jdbcType="NUMERIC"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="promotion_flag" property="promotionFlag"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="predefine_flag" property="predefineFlag"/>
        <result column="coupon_count" property="couponCount"/>
        <result column="retailer_flag" property="retailerFlag"/>
    </resultMap>

    <select id="getPromotionPartners" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.PartnerPromotionParams">
        select * from
            (select
            o.id as partner_id,
            t4.region,
            case when exists (select 1 from wx_t_promotion_delivery_detail e inner join wx_t_partner_o2o_enterprise e2 on cast(e.ext_property7 as bigint) = e2.distributor_id
            where e2.partner_id = o.id) then 1 else 0 end as promotion_flag
            from wx_t_point_account t1
            inner join  wx_t_organization o on t1.POINT_ACCOUNT_OWNER_ID  = o.id
            inner join wx_t_partner_o2o_enterprise t3 on t3.partner_id = (case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id = o.id) else o.id end)
            inner join (select distributor_id,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null group by distributor_id,region) t4
            on t4.distributor_id = t3.distributor_id
            where o.status=1
            and exists (select 1 from wx_t_point_value_detail t2 where t1.id = t2.POINT_ACCOUNT_ID and t2.DELETE_FLAG = 0)
            and t1.POINT_ACCOUNT_TYPE = 'SP'
            <if test="partnerId!=null">
                and o.id = #{partnerId}
            </if>
            group by o.id,t4.region
            union
            select o.partner_id,
            t4.region,
            case when exists (select 1 from wx_t_promotion_delivery_detail e
            inner join wx_t_partner_o2o_enterprise e2 on cast(e.ext_property7 as bigint) = e2.distributor_id
            where e2.partner_id = o.partner_id) then 1 else 0 end as promotion_flag
            from wx_t_promotion_gift_pool o
            inner join wx_t_partner_o2o_enterprise t3 on t3.partner_id = o.partner_id
            inner join (select distributor_id,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null group by distributor_id,region) t4
            on t4.distributor_id = t3.distributor_id
            where 1 = 1
            <if test="partnerId!=null">
                and o.partner_id = #{partnerId}
            </if>
            group by o.partner_id,t4.region
        ) temp
        where 1 = 1
        $Permission_Clause$
    </select>

    <select id="getPromotionPartnerInfo" resultMap="BaseResultMap">
        select 'point' as [type],t4.id as partner_id,
        <if test="params.byRetailer == null or params.byRetailer == false">
        (case when t4.type=3 then r1o1.organization_name + ' - ' else '' end) + 
        </if>
        t4.organization_name as partner_name,
        sum(t3.POINT_VALUE-t3.POINT_PAYED) as balance,t5.point_type,
        case when t4.type=3 then 1 else 0 end retailer_flag from
        wx_t_point_account t1
        inner join wx_t_partner_o2o_enterprise t2 on t1.POINT_ACCOUNT_OWNER_ID = t2.partner_id
        left join wx_t_organization t4 on t2.partner_id  = t4.id
        left join wx_t_organization r1o1 on t4.type=3 and r1o1.id=t2.ext_property1
        left join wx_t_point_value_detail t3 on t1.id = t3.POINT_ACCOUNT_ID
        inner join wx_t_promotion_point t5 on t5.point_code = t3.POINT_TYPE
        where t1.DELETE_FLAG != 'true'
        and t1.POINT_ACCOUNT_TYPE = 'SP'
        and t3.DELETE_flag = 0
        <if test="params.partnerId!=null">
            and t2.partner_id = #{params.partnerId}
        </if>
        <choose>
        	<when test="params.partnerIds!=null and params.partnerIds.size > 0">
            and t2.partner_id in
            <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
                #{listItem}
            </foreach>
        	</when>
        	<otherwise>
        	and 1!=1
        	</otherwise>
        </choose>
        /*and EXISTS (select 1 from wx_t_promotion_delivery_detail e1 where t2.distributor_id = cast(e1.ext_property7 as bigint))*/
        group by t4.id,t4.organization_name,t5.point_type,t4.type,r1o1.organization_name
        union all
        select 'gift' as [type],t1.partner_id,
        <if test="params.byRetailer == null or params.byRetailer == false">
        (case when t2.type=3 then r1o1.organization_name + ' - ' else '' end) + 
        </if>
        t2.organization_name as partner_name, sum(available_quantity) as balance, t1.application_type as point_type,
        case when t2.type=3 then 1 else 0 end retailer_flag from wx_t_promotion_gift_pool t1
        inner join wx_t_organization t2 on t1.partner_id = t2.id
        left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=t2.id
        left join wx_t_organization r1o1 on t2.type=3 and r1o1.id=pe1.ext_property1
        where t2.status = 1
        <if test="params.partnerId!=null">
            and t1.partner_id = #{params.partnerId}
        </if>
        <choose>
        	<when test="params.partnerIds!=null and params.partnerIds.size > 0">
            and t1.partner_id in
            <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
                #{listItem}
            </foreach>
        	</when>
        	<otherwise>
        	and 1!=1
        	</otherwise>
        </choose>
        group by t1.partner_id,t2.organization_name ,t1.application_type,t2.type,r1o1.organization_name
        <if test="params.brand > 0">
        union all
        select 'point' as [type],pe.partner_id,v.customer_name_cn as partner_name,
        case when b.brand=1 then b.region_budget_value-#{params.consumerExpense} else b.region_budget_value-#{params.commercialExpense} end as balance,
        case when b.brand=1 then 'CONSUMER_BUDGET' else 'COMMERCIAL_BUDGET' end as point_type,
        0 retailer_flag 
        from dw_access_control_customer_org_sales v 
        left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=v.distributor_id
        left join wx_t_organization o on o.id=pe.partner_id
        left join wx_t_budget2021 b on b.budget_key=v.sales_cai and b.budget_year=#{params.year} and b.budget_type=3 and v.channel_weight&amp;b.channel_weight>0
        where v.sales_cai=#{params.salesCai} and b.brand&amp;#{params.brand}>0 and o.status=1
        <if test="params.partnerId!=null">
            and pe.partner_id = #{params.partnerId}
        </if>
        <!-- <if test="params.partnerIds!=null and params.partnerIds.size > 0">
            and pe.partner_id in
            <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
                #{listItem}
            </foreach>
        </if> -->
        </if>
    </select>

    <select id="queryPartnerPromotionPointBalance" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.PartnerPromotionParams">
        select t1.promotion_id,t2.promotion_name,t1.type,t1.partner_id,t6.point_type,t6.point_code,t6.predefine_flag,
        sum(case when t4.id is not null then t4.POINT_VALUE-t4.POINT_PAYED when t5.id is not null then t5.available_quantity else 0 end) as balance,
        <!-- sum(case when t5.id is not null and t7.MATERIAL_TYPE = 'COUPON' then t5.available_quantity else 0 end) --> 0 as coupon_count
        from
        (select promotion_id,
        case when award_type = 1 then 'gift' when award_type = 2 then 'point' else '' end as [type] ,
        case when award_type = 2 then wtpp.ext_property2 when wtpp.award_type = 1 then wtpp.ext_property3 else '' end as point_code,
        t2.partner_id
        from wx_t_promotion_delivery_detail wtpp
        inner join wx_t_partner_o2o_enterprise t2 on wtpp.ext_property7 = t2.distributor_id
        where t2.partner_id = #{partnerId} and wtpp.delivery_status=20
        group by promotion_id,
        case when award_type = 1 then 'gift' when award_type = 2 then 'point' else '' end,
        case when award_type = 2 then wtpp.ext_property2 when wtpp.award_type = 1 then wtpp.ext_property3 else '' end
        ,t2.partner_id
        ) t1
        inner join wx_t_sell_in_promotion t2 on t1.promotion_id = t2.id
        left join wx_t_point_account t3 on t3.POINT_ACCOUNT_OWNER_ID = t1.partner_id and t3.DELETE_FLAG != 'true' and t3.POINT_ACCOUNT_TYPE = 'SP' and t1.type = 'point'
        left join wx_t_point_value_detail t4 on t3.id = t4.POINT_ACCOUNT_ID and t1.point_code = t4.POINT_TYPE
        left join wx_t_promotion_gift_pool t5 on t5.point_code =  t1.point_code and t5.partner_id = t1.partner_id  and t1.type = 'gift'
        <!-- left join wx_t_material t7 on t7.id = t5.material_id -->
        left join wx_t_promotion_point t6 on t6.point_code =t1.point_code
        <where>
            <if test = "type!=null and type!=''">
                and t1.type = #{type}
            </if>
            <if test = "pointType!=null and pointType!=''">
                and t6.point_type = #{pointType}
            </if>
        </where>
        GROUP by t1.promotion_id,t2.promotion_name,t1.type,t1.partner_id,t6.point_type,t6.point_code,t6.predefine_flag
    </select>

    <select id="queryCommonPointBalance" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.PartnerPromotionParams">
        select t3.point_name as promotion_name,'point' as type,t1.POINT_ACCOUNT_OWNER_ID as partner_id,t3.point_type,t3.point_code,t3.point_name
        ,sum(t2.POINT_VALUE-t2.POINT_PAYED) as balance
        from wx_t_point_account t1
        left join wx_t_point_value_detail t2 on t1.id = t2.POINT_ACCOUNT_ID
        left join wx_t_promotion_point t3 on t3.point_code = t2.POINT_TYPE
        where t1.DELETE_FLAG != 'true'
        and t1.POINT_ACCOUNT_TYPE = 'SP'
        and t2.DELETE_FLAG = 0
        and t1.POINT_ACCOUNT_OWNER_ID = #{partnerId}
        and t2.point_type = #{pointType}
        group by t1.POINT_ACCOUNT_OWNER_ID,t3.point_type,t3.point_code,t3.point_name
    </select>

    <!-- 结果集映射 -->
    <resultMap id="PromotionPointChangeDTO" type="com.chevron.sellin.model.PromotionPointChangeDTO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
        <result column="point_type" property="pointType" jdbcType="VARCHAR"/>
        <result column="point_code" property="pointCode" jdbcType="VARCHAR"/>
        <result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
        <result column="promotion_name" property="promotionName" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="BIGINT"/>
        <result column="source_code" property="sourceCode" jdbcType="VARCHAR"/>
        <result column="point" property="point" jdbcType="NUMERIC"/>
        <result column="comments" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="business_id" property="businessId" jdbcType="BIGINT"/>
        <result column="point_type_text" property="pointTypeText" jdbcType="VARCHAR"/>
        <result column="created_by_user" property="createdByUser" jdbcType="VARCHAR"/>
        <result column="att_id" property="attId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryPromotionPointChangeDetail" resultMap="PromotionPointChangeDTO">
        select * from (
            <if test="incomeFlag == null or !incomeFlag">
                    SELECT
                    a.APPLICATION_ORG_ID as partner_id,#{pointCode} as point_code,#{pointType} as point_type,a.id AS source_id,a.APPLICATION_CODE AS source_code,
                    -cast(pb.ATTRIBUTE1 as decimal(12,2)) AS point, a.CREATION_TIME as create_time,'订单:' + a.APPLICATION_CODE AS comments,
                    NULL AS promotion_name, pb.BUSINESS_TYPE_CODE AS business_type, NULL AS promotion_id, pb.ID AS business_id,
                    #{pointName} AS point_type_text,u.ch_name AS created_by_user,NULL AS att_id
                FROM dbo.wx_t_material_application a
                        LEFT JOIN wx_t_organization o ON o.id = a.APPLICATION_ORG_ID
                        LEFT JOIN wx_t_point_business pb on a.APPLICATION_CODE = pb.RELATED_CODE
                        LEFT JOIN wx_t_user u on u.user_id = a.CREATED_BY
                    WHERE 1 = 1
                        AND a.DELETE_FLAG = 0
                        and pb.BUSINESS_STATUS = 'DONE'
                        and pb.BUSINESS_TYPE_CODE not in ('ROLLBACK_POINT')
                        AND (a.ATTRIBUTE1 is null or a.ATTRIBUTE1 != 'PROMOTION_EXCHANGE')
                        AND a.APPLICATION_ORG_ID = #{partnerId}
                        <choose>
                            <when test="pointCode != null and pointCode != ''">
                                and a.APPLICATION_TYPE = #{applicationType}
                            </when>
                            <when test="(pointCode == null or pointCode == '') and pointType != null">
                                AND (a.APPLICATION_TYPE = #{applicationType} or a.promotion_type = #{pointType})
                            </when>
                        </choose>
                    union all
            </if>
            SELECT pac.POINT_ACCOUNT_OWNER_ID as partner_id,#{pointCode} as point_code,#{pointType} as point_type,bu.RELATED_ID as source_id,bu.RELATED_CODE AS source_code,
            cast(plog.MODIFIED_VALUE as decimal(12,2)) as point, plog.CREATION_TIME  as create_time, plog.COMMENTS,
            case when bu.business_type_code in ('SELL_IN_PROMOTION_DELIVERY_V2','SELL_IN_PROMOTION_DELIVERY') then bu.ATTRIBUTE1 else null end AS promotion_name, bu.BUSINESS_TYPE_CODE AS business_type,
            case when bu.business_type_code in ('SELL_IN_PROMOTION_DELIVERY_V2','SELL_IN_PROMOTION_DELIVERY') then bu.RELATED_ID else null end AS promotion_id,
            bu.ID AS business_id,#{pointName} AS point_type_text,u.ch_name AS created_by_user,bu.ATT_ID AS att_id
            FROM wx_t_point_value_detail pde
            LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
            LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
            LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
            left join wx_t_promotion_point pp on pp.point_code = pde.point_type
            LEFT JOIN wx_t_user u on u.user_id = pde.CREATED_BY
            WHERE 1 = 1
            <if test="pointCode != null and pointCode != ''">
                AND pp.point_code = #{pointCode}
            </if>
            <if test="pointType != null and pointType != ''">
                AND pp.point_type = #{pointType}
            </if>
            AND plog.MODIFIED_VALUE != 0
            AND pac.POINT_ACCOUNT_OWNER_ID = #{partnerId}
            <if test = "incomeFlag != null and incomeFlag">
                and plog.MODIFIED_VALUE > 0
            </if>
            <if test = "incomeFlag != null and !incomeFlag">
                and plog.MODIFIED_VALUE <![CDATA[ < ]]> 0
            </if>
        )  t
        <where>
            <if test="startDate != null and startDate != null">
                and create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != null">
                and create_time <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="queryPromotionGiftChangeDetail" resultMap="PromotionPointChangeDTO">
        select * from (
        <if test="incomeFlag == null or !incomeFlag">
            select max(id) id,partner_id,point_code,point_type,sum(point)
            point,comments,business_type,source_id,source_code,promotion_id,promotion_name,max(create_time) create_time from
            (
            select
            t1.id,t2.partner_id, t3.point_code , t3.point_type ,
            case
            when t1.modify_type = 'ModifyTypeReduce' then -t1.modify_quantity
            else modify_quantity
            end as point,
            case
            when t1.business_key is not null then '订单:' + t1.ext_property1
            else t1.comments
            end as comments,
            t1.business_type, t1.business_key as source_id, null as source_code,
            NULL as promotion_id,
            NULL as promotion_name,
            t1.create_time
            from
            wx_t_promotion_gift_modify_log t1
            inner join wx_t_promotion_gift_pool t2 on
            t2.id = t1.pool_id
            inner join wx_t_promotion_point t3 on
            t3.point_code = t2.point_code
            <where>
                and t1.business_type = 'exchange'
                <if test = "partnerId != null">
                    and t2.partner_id = #{partnerId}
                </if>
                <if test = "pointCode != null and pointCode!=''">
                    and t3.point_code = #{pointCode}
                </if>
                <if test = "pointType != null and pointType!=''">
                    and t3.point_type = #{pointType}
                </if>
            </where>
            ) temp group by partner_id,point_code,point_type,comments,business_type,source_id,source_code,promotion_id,promotion_name
        </if>
        <if test="incomeFlag == null">
            union all
        </if>
        <if test="incomeFlag == null or incomeFlag == true">
            select
            t1.id,t2.partner_id, t3.point_code , t3.point_type ,
            case
            when t1.modify_type = 'ModifyTypeReduce' then -t1.modify_quantity
            else modify_quantity
            end as point,
            t1.comments, t1.business_type, t1.business_key as source_id, null as source_code,
            case
            when t1.business_type in ('SELL_IN_PROMOTION_DELIVERY', 'SELL_IN_PROMOTION_DELIVERY_V2') then t1.business_key
            else NULL
            end AS promotion_id, t1.ext_property2 promotion_name, t1.create_time
            from
            wx_t_promotion_gift_modify_log t1
            inner join wx_t_promotion_gift_pool t2 on
            t2.id = t1.pool_id
            inner join wx_t_promotion_point t3 on
            t3.point_code = t2.point_code
            <where>
                and t1.business_type != 'exchange'
                <if test = "partnerId != null">
                    and t2.partner_id = #{partnerId}
                </if>
                <if test = "pointCode != null and pointCode!=''">
                    and t3.point_code = #{pointCode}
                </if>
                <if test = "pointType != null and pointType!=''">
                    and t3.point_type = #{pointType}
                </if>
            </where>
        </if>
        )  t
        <where>
            <if test="startDate != null and startDate != null">
                and create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != null">
                and create_time <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
    </select>

    <resultMap id="CouPonsResultMap" type="com.chevron.sellin.model.CouPonInfo">
        <id column="id" property="id"/>
        <result column="material_name" property="name"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="expiry_date" property="expiryDate"/>
        <result column="choose_quantity" property="chooseQuantity"/>
        <result column="available_quantity" property="availableQuantity"/>
        <result column="applied_options" property="appliedOptions"/>
        <result column="choose_info" property="chooseInfo"/>
        <collection property="options" ofType="com.chevron.material.model.CouponOptio">
            <id column="option_id" property="id"/>
            <result column="id" property="couponId"/>
            <result column="option_name" property="optionName"/>
        </collection>
    </resultMap>

    <select id="getPartnerCouPons" resultMap="CouPonsResultMap">
        select t1.id, t1.MATERIAL_NAME, t2.id as option_id, t2.option_name,t.available_quantity,
               t1.attribute4 as effective_date,t1.attribute5 as expiry_date,t1.attribute3 as choose_quantity,
               t3.dic_item_name as choose_info
        from wx_t_promotion_gift_pool t
        inner join wx_t_material t1 on t.material_id = t1.ID
        left join wx_t_coupon_option t2 on t2.coupon_id = t1.ID
        left join wx_t_dic_item t3 on t3.dic_type_code = 'coupon.options' and t3.dic_item_code = t1.ATTRIBUTE2
        where t1.material_type = 'COUPON'
        <if test="partnerId != null">
            and t.partner_id = #{partnerId}
        </if>
        <if test="pointCode != null and pointCode != '' ">
            and t.point_code = #{pointCode}
        </if>
    </select>


    <resultMap id="CouPonsLogResultMap" type="com.chevron.sellin.model.CouPonInfo">
        <id column="log_id"></id>
        <result column="id" property="id"/>
        <result column="material_name" property="name"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="expiry_date" property="expiryDate"/>
        <result column="choose_quantity" property="chooseQuantity"/>
        <result column="available_quantity" property="availableQuantity"/>
        <result column="applied_options" property="appliedOptions"/>
        <result column="choose_info" property="chooseInfo"/>
        <collection property="options" ofType="com.chevron.material.model.CouponOptio">
            <id column="option_id" property="id"/>
            <result column="id" property="couponId"/>
            <result column="option_name" property="optionName"/>
        </collection>
    </resultMap>

    <select id="getAppliedCouPons" resultMap="CouPonsLogResultMap">
        select l.id as log_id, t1.id, t1.MATERIAL_NAME, t2.id as option_id, t2.option_name,t.available_quantity,
        t1.attribute4 as effective_date,t1.attribute5 as expiry_date,t1.attribute3 as choose_quantity,t3.dic_item_name as choose_info,
        l.ext_property2 as applied_options
        from
        wx_t_promotion_gift_modify_log l inner join
        wx_t_promotion_gift_pool t on l.pool_id = t.id
        inner join wx_t_material t1 on t.material_id = t1.ID
        left join wx_t_coupon_option t2 on t2.coupon_id = t1.ID
        left join wx_t_dic_item t3 on t3.dic_type_code = 'coupon.options' and t3.dic_item_code = t1.ATTRIBUTE2
        where t1.material_type = 'COUPON'
          and l.business_type = 'exchange'
          and t.partner_id = #{partnerId}
          and t.point_code = #{pointCode}
    </select>

    <resultMap id="PromotionPointBalanceBaseResultMap" type="com.chevron.sellin.model.PromotionPointBalanceVo">
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="point_name" property="pointName" jdbcType="VARCHAR"/>
        <result column="point_type" property="pointType" jdbcType="VARCHAR"/>
        <result column="point_code" property="pointCode" jdbcType="VARCHAR"/>
        <result column="organization_name" property="organizationName" jdbcType="VARCHAR"/>
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
        <result column="status" property="status"/>
        <result column="balance" property="balance" jdbcType="NUMERIC"/>
    </resultMap>

    <select id="queryPromotionPointBalance" resultMap="PromotionPointBalanceBaseResultMap" parameterType="com.chevron.sellin.model.PromotionPointBalanceParams">
        select 'point' as [type],*
        from
        (select
            pp.point_name,
            pp.point_type,
            pp.point_code,
            o.organization_name,
            o.id as partner_id,
            o.status,
            sum(pvd.POINT_VALUE-pvd.POINT_PAYED) as balance
        FROM dbo.wx_t_point_value_detail pvd
        LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
        LEFT JOIN dbo.wx_t_organization o ON pa.POINT_ACCOUNT_OWNER_ID = o.id
        LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
        left join wx_t_promotion_point pp on pp.point_code=pvd.POINT_TYPE
        where 1=1
        <if test="pointType != null and pointType != '' ">
            and pp.point_type=#{pointType}
        </if>
        and o.id = #{partnerId}
        group by pp.point_name, pp.point_type, pp.point_code, o.organization_name, o.status, o.id
        ) a
        where 1=1
        order by a.organization_name
    </select>
</mapper>
