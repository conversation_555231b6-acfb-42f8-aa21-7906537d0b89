package com.chevron.localmarketing.business;

import com.chevron.localmarketing.model.NonLocalUserReport;
import com.chevron.localmarketing.model.NonLocalUserReportExample;
import com.chevron.localmarketing.model.NonLocalUserReportParams;
import com.common.exception.WxPltException;

import java.util.List;
import java.util.Map;

/**
 * 用户验真上报业务接口
 * <AUTHOR>
 * @version 1.0 2022-06-29 10:36
 */
public interface NonLocalUserReportBizService {
	
	/**
	 * 保存用户验真上报
	 * @param record 被插入用户验真上报
	 * @throws WxPltException
	 */
	public void insert(NonLocalUserReport record) throws WxPltException;
	
	/**
	 * 修改用户验真上报
	 * @param record 被修改用户验真上报
	 * @throws WxPltException
	 */
	public void update(NonLocalUserReport record) throws WxPltException;
	
	/**
	 * 删除用户验真上报
	 * @param ids 被删除用户验真上报id集合
	 * @throws WxPltException
	 */
	public void delete(List<Long> ids) throws WxPltException;
	
	/**
	 * 删除满足条件的用户验真上报
	 * @param example 被删除用户验真上报满足条件
	 * @throws WxPltException
	 */
	public void deleteByExample(NonLocalUserReportExample example) throws WxPltException;
	
	/**
	 * 列表查询
	 * @param example 查询条件
	 * @throws WxPltException
	 */
	public List<NonLocalUserReport> queryByExample(NonLocalUserReportExample example) throws WxPltException;
	
	/**
	 * 列表查询用户验真上报对象
	 * @param params 查询条件
	 * @return 满足条件用户验真上报对象集合
	 */
    public List<NonLocalUserReport> queryByParams(Map<String, Object> params) throws WxPltException;

	/**
	 * 修改页面修改用户验真上报
	 * @param record 被修改用户验真上报
	 * @throws WxPltException
	 */
	public void updateForEditPage(NonLocalUserReport record) throws WxPltException;
	
	/**
	 * 获取指定主键的用户验真上报对象
	 * @param id 主键值
	 * @throws WxPltException
	 */
	public NonLocalUserReport getBean(Long id) throws WxPltException;	

	/**
	 * 分页查询用户验真上报
	 * @param params 查询参数
	 * @param resultMap 结果对象
	 * @throws WxPltException
	 */
	public void queryForPage(NonLocalUserReportParams params, Map<String, Object> resultMap) throws WxPltException;
	
	/**
	 * 分页查询用户验真上报
	 * @param params 查询参数
	 * @return 查询结果
	 * @throws WxPltException
	 */
	public List<NonLocalUserReport> queryForPage(NonLocalUserReportParams params) throws WxPltException;
}
