<%@page import="com.common.util.StringUtils"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import= "com.common.util.ContextUtil"%>
<%@page import= "com.sys.auth.model.WxTUser"%>
<%@page import="com.sys.auth.model.WxTRole"%>
<%@page import="java.util.List"%>
<%@page import="java.util.Calendar"%>
<%@page import="java.net.URLDecoder"%>
<%
    String initPartnerIdStr = request.getParameter("partnerId");
    Long initPartnerId = null;
    String initPartnerName = null;
    if(initPartnerIdStr != null){
        try{
            initPartnerId = Long.valueOf(initPartnerIdStr);
            initPartnerName = request.getParameter("partnerName") == null ? null : "'" + URLDecoder.decode(request.getParameter("partnerName"),"UTF-8") + "'";
        }catch (Exception e){

        }
    }
	int year = 0;
	int currentMonth = 12;
	Calendar cal = Calendar.getInstance();
	if(StringUtils.isNotBlank(request.getParameter("year"))){
		year = Integer.parseInt(request.getParameter("year"));
		if(year == cal.get(Calendar.YEAR)){
			currentMonth = cal.get(Calendar.MONTH) + 1;
		}
	}else{
		year = cal.get(Calendar.YEAR);
		currentMonth = cal.get(Calendar.MONTH) + 1;
	}
	int lastYear = year - 1;

    WxTUser curUser = ContextUtil.getCurUser();
    String userName = curUser.getChName();
    Long userId = curUser.getUserId();
    Long orgId = curUser.getOrgId();

    List<WxTRole> roleList = curUser.getRoleList();

    boolean isDistributor = false;
    String userModel = curUser.getUserModel();
    if(WxTUser.USER_MODEL_SP.equals(userModel)) {
        isDistributor = true;
    }

    boolean isCdmDistributor = false;
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Service_Partner_Admin")
                || role.getChRoleName().equals("Service_Partner_Manager")) {
            isCdmDistributor = true;
            break;
        }
    }

    boolean isCioDistributor = false;
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Caltex_Dealer")) {
            isCioDistributor = true;
            break;
        }
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>合伙人sell through数据分析</title>
    <%@include file="/common/jsp/common.jsp"%>
    <link href="${ctx }newhomepage/css/SpSalesThroughAnalysis.css?v=${version}" rel="stylesheet">
    <link href="${ctx }newhomepage/css/SpSellInInfoAnalysis.css?v=${version}" rel="stylesheet">
    <script type="text/javascript" src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
    <script type="text/javascript" src="${ctx }newhomepage/js/SpSalesThroughAnalysis.js?v=${version}7"></script>
    <script type="text/javascript">
        var orgId = <%=orgId%>;
        var currentYear = <%=year%>;
        var lastYear = <%=lastYear%>;
        var currentMonth = <%=currentMonth %>;
        var currentQuarter = parseInt((currentMonth + 2) / 3);
        var initPartnerName = decodeURIComponent(<%=initPartnerName%>);
        var initPartnerId = <%=initPartnerId%>;
        var currentParams = {
        		partnerId: initPartnerId,
        		partnerName: initPartnerName
        };
    </script>
    <style type="text/css">
        .control-text.control-auto {
            height: 30px;
            width: 180px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="not-top">
    <div  id="detailDiv" class="content-wrapper">
        <!--标题栏-->
        <div class="content-panel header-panel">
            <div class="header-title" id="titleDiv">合伙人sell through数据分析</div>
        </div>
        <!--查询条件-->
        <div class="content-panel query-panel">
                         <div class="field-group" style="width:250px;">
                            <label class="search-label">年份：</label>
                            <div class="control-group years-wrapper" id="year_c"><ul></ul></div>
                        </div>
            <div class="field-group" style="width: 280px;">
                <label class="field-label width-auto search-label" style="vertical-align: middle">经销商：</label>
                <div class="control-group">
                    <input type="hidden" name='partnerId' id="partnerId"/>
                </div>
                <%--<div class="query-btns" style="width: auto;">--%>
                    <div class="query-btn field-label" style="width: auto; vertical-align: middle;margin-left: 10px;">
                        <button id="search" class="btn-query">查询</button>
                    </div>
                <%--</div>--%>
            </div>
            <%--<div class="row" id="year-mode" style="margin-right: 0px;margin-left:0px;margin-top: 0;">--%>
                <%--<div class="col-sm-12">--%>
                    <%--<div class="query-panel">--%>
                        <%--<div class="field-group" style="width:60%">--%>
                            <%--<label class="search-label">年份：</label>--%>
                            <%--<div class="control-group years-wrapper" id="year_c"><ul></ul></div>--%>
                        <%--</div>--%>
                    <%--</div>--%>
                    <%--&lt;%&ndash;<div class="field-group" style="width:100%">&ndash;%&gt;--%>
                        <%--&lt;%&ndash;<label class="search-label">月份：</label>&ndash;%&gt;--%>
                        <%--&lt;%&ndash;<div class="control-group monthes-wrapper" id="month_c"><ul></ul></div>&ndash;%&gt;--%>
                    <%--&lt;%&ndash;</div>&ndash;%&gt;--%>
                <%--</div>--%>
            <%--</div>--%>
    <div class="content-box" style="max-height: none;text-align: center;">
        <div style="max-width: 1000px;margin: auto; display: none;">
            <h2>录入新店扫码分析</h2>
            <div class="grid-content-box high-content-box" style="margin-right: 15px;">
                <div class="month-mode-wrapper">
                    <div class="content-panel echarts-block">
                        <div class="e-tables" id="newShopScanTable">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="margin: auto;min-height: 540px">
            <h2>录入新店扫码分析</h2>
            <div class="grid-content-box high-content-box" style="margin-right: 15px;">
                <div class="month-mode-wrapper">
                    <div class="content-panel echarts-block">
                        <div class="e-tables" id="newShopScanInfoTable">
                            <div class="content-panel" id="adjust_list_grid">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="max-width: 90%;margin: auto;">
            <h2>出库扫码分析</h2>
            <div class="query-panel" id="skuMonthDiv">
                <div class="field-group">
                    <label class="field-label">月份：</label>
                    <div class="control-group" id="skuMonthC">
                        <input id="skuMonth" name="skuMonth"
                               class="input-small control-text" type="hidden"
                               value=""/>
                    </div>
                </div>
            </div>
            <div class="grid-content-box high-content-box" style="margin-right: 15px;">
                    <div class="content-panel echarts-block">
                        <div class="echarts" id="sellThroughInfoByDayChart"></div>
                    </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
<!-- address detail dialog -->
<div style="display:none;">
    <div id="workshopSkuDetailDialog">
        <div class="content-panel row-fluid" id="detailDealerInfoChart"  style="width: 770px;height: 570px;margin-top: 0;">
        </div>
    </div>
</div>
</body>
</html>