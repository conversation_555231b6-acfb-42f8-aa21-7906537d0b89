select * from wx_t_dealer_business_fun where business_custom_id=1
insert into wx_t_dealer_business_fun (dealer_id, business_fun_code, business_custom_id)
select pe.partner_id, 'PROMOTION_POINT', 1
--select cs.* 
from view_customer_region_sales_channel cs left join wx_t_partner_o2o_enterprise pe on cs.distributor_id=pe.distributor_id
where pe.partner_id not in (
select dealer_id from wx_t_dealer_business_fun where business_custom_id=1)
and cs.channel_weight&2>0
order by cs.sales_channel_name, suppervisor_cai, sales_cai