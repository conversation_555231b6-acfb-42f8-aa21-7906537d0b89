package com.chevron.b2b.business.impl;

import com.chevron.b2b.business.ScheduleBizService;
import com.chevron.b2b.dao.ScheduleDistributorMapper;
import com.chevron.b2b.dao.ScheduleEntityMapper;
import com.chevron.b2b.dao.ScheduleInstanceMapper;
import com.chevron.b2b.dao.ScheduleItemInstanceMapper;
import com.chevron.b2b.dao.ScheduleItemMapper;
import com.chevron.b2b.dao.ScheduleMapper;
import com.chevron.b2b.dao.ScheduleWorkshopMapper;
import com.chevron.b2b.model.Schedule;
import com.chevron.b2b.model.ScheduleDistributor;
import com.chevron.b2b.model.ScheduleDistributorExample;
import com.chevron.b2b.model.ScheduleEntity;
import com.chevron.b2b.model.ScheduleExample;
import com.chevron.b2b.model.ScheduleItem;
import com.chevron.b2b.model.ScheduleItemExample;
import com.chevron.b2b.model.ScheduleParams;
import com.chevron.b2b.model.ScheduleWorkshop;
import com.chevron.b2b.model.ScheduleWorkshopExample;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 计划业务接口实现类
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
@Service
public class ScheduleBizServiceImpl implements ScheduleBizService {
	@Autowired
	private ScheduleMapper scheduleMapper;
	
	@Resource
	private ScheduleItemMapper scheduleItemMapper;
	
	@Resource
	private ScheduleInstanceMapper scheduleInstanceMapper;
	
	@Resource
	private ScheduleItemInstanceMapper scheduleItemInstanceMapper;
	
	@Resource
	private ScheduleDistributorMapper scheduleDistributorMapper;
	
	@Resource
	private ScheduleWorkshopMapper scheduleWorkshopMapper;
	
	@Resource
	private ScheduleEntityMapper scheduleEntityMapper;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void insert(Schedule record) throws WxPltException {
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();
		record.setDeleteFlag(0);
		record.setCreateUserId(userId);
		record.setCreateTime(now);
		record.setUpdateUserId(userId);
		record.setUpdateTime(now);
		scheduleMapper.insertSelective(record);
		//插入计划事项
		if(record.getScheduleItems() != null && !record.getScheduleItems().isEmpty()) {
			for(ScheduleItem item : record.getScheduleItems()) {
				item.setScheduleId(record.getId());
			}
			scheduleItemMapper.insertBatch(record.getScheduleItems());
		}
		//插入分配经销商
		if(record.getAssignedDistributors() != null && !record.getAssignedDistributors().isEmpty()) {
			for(ScheduleDistributor scheduleDistributor : record.getAssignedDistributors()) {
				scheduleDistributor.setScheduleId(record.getId());
			}
			scheduleDistributorMapper.insertBatch(record.getAssignedDistributors());
		}
		//插入分配门店
		if(record.getAssignedWorkshops() != null && !record.getAssignedWorkshops().isEmpty()) {
			for(ScheduleWorkshop scheduleWorkshop : record.getAssignedWorkshops()) {
				scheduleWorkshop.setScheduleId(record.getId());
			}
			scheduleWorkshopMapper.insertBatch(record.getAssignedWorkshops());
		}
		//发布任务
		if(record.getStatus() == 5) {
			publishSchedule(record, now, userId);
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(Schedule record) throws WxPltException {
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();
		record.setUpdateUserId(userId);
		record.setUpdateTime(now);
		if(record.getStatus() != null && record.getStatus() == 5) {
			record.setPublishTime(now);
		}
		scheduleMapper.updateByPrimaryKeySelective(record);
		if(record.getStatus() == null) {
			//非修改页面操作
			return;
		}
		//删除原计划事项
		ScheduleItemExample scheduleItemExample = new ScheduleItemExample();
		scheduleItemExample.createCriteria().andScheduleIdEqualTo(record.getId());
		scheduleItemMapper.deleteByExample(scheduleItemExample);
		//更新计划事项
		if(record.getScheduleItems() != null && !record.getScheduleItems().isEmpty()) {
			for(ScheduleItem item : record.getScheduleItems()) {
				item.setScheduleId(record.getId());
			}
			scheduleItemMapper.insertBatch(record.getScheduleItems());
		}
		//删除原分配经销商
		ScheduleDistributorExample scheduleDistributorExample = new ScheduleDistributorExample();
		scheduleDistributorExample.createCriteria().andScheduleIdEqualTo(record.getId());
		scheduleDistributorMapper.deleteByExample(scheduleDistributorExample);
		//插入分配经销商
		if(record.getAssignedDistributors() != null && !record.getAssignedDistributors().isEmpty()) {
			for(ScheduleDistributor scheduleDistributor : record.getAssignedDistributors()) {
				scheduleDistributor.setScheduleId(record.getId());
			}
			scheduleDistributorMapper.insertBatch(record.getAssignedDistributors());
		}
		//删除原已分配门店
		ScheduleWorkshopExample scheduleWorkshopExample = new ScheduleWorkshopExample();
		scheduleWorkshopExample.createCriteria().andScheduleIdEqualTo(record.getId());
		scheduleWorkshopMapper.deleteByExample(scheduleWorkshopExample);
		//插入分配门店
		if(record.getAssignedWorkshops() != null && !record.getAssignedWorkshops().isEmpty()) {
			for(ScheduleWorkshop scheduleWorkshop : record.getAssignedWorkshops()) {
				scheduleWorkshop.setScheduleId(record.getId());
			}
			scheduleWorkshopMapper.insertBatch(record.getAssignedWorkshops());
		}
		//发布任务
		if(record.getStatus() == 5) {
			publishSchedule(record, now, userId);
		}
	}

	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void delete(List<Long> ids) throws WxPltException {
		ScheduleExample example = new ScheduleExample();
		example.createCriteria().andIdIn(ids);
		scheduleMapper.deleteByExample(example);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void deleteByExample(ScheduleExample example) throws WxPltException {
		scheduleMapper.deleteByExample(example);
	}

	@Override
	public List<Schedule> queryByExample(ScheduleExample example) throws WxPltException {
		return scheduleMapper.selectByExample(example);
	}

	@Override
    public List<Schedule> queryByParams(Map<String, Object> params) throws WxPltException {
    	return scheduleMapper.queryByParams(params);
    }

	@Override
	public Schedule getBean(Long id) throws WxPltException{
		return scheduleMapper.selectByPrimaryKey(id);
	}	

	@Override
	public void queryForPage(ScheduleParams params, Map<String, Object> resultMap) throws WxPltException {
		resultMap.put(Constants.RESULT_LST_KEY, scheduleMapper.queryForPage(params));
		resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
	}

	@Override
	public void publishSchedule(Schedule record, List<Long> assignDistributors, List<Long> assignWorkshops)
			throws WxPltException {
		Map<String, Object> params = new HashMap<String, Object>();
		if(assignDistributors != null && !assignDistributors.isEmpty()) {
			params.put("assignDistributors", assignDistributors);
		}
		if(assignWorkshops != null && !assignWorkshops.isEmpty()) {
			params.put("assignWorkshops", assignWorkshops);
		}
		params.put("enableFlag", 1);
		params.put("versionNo", 1);
		params.put("scheduleId", record.getId());
		params.put("createUserId", ContextUtil.getCurUserId());
		params.put("createTime", DateUtil.getCurrentDate());
		if(!"-1".equals(record.getExecuteUserType())) {
			params.put("executeUserType", record.getExecuteUserType());
		}
		//插入计划实例
		scheduleInstanceMapper.insertBatchWhenPublish(params);
		//插入计划事项实例
		scheduleItemInstanceMapper.insertBatchWhenPublish(params);
	}

	@Override
	public void publishSchedule(Schedule record, Date publishTime, Long publishUser) throws WxPltException {
		ScheduleEntity scheduleEntity = new ScheduleEntity();
		scheduleEntity.setCreateTime(publishTime);
		scheduleEntity.setCreateUserId(publishUser);
		scheduleEntity.setScheduleId(record.getId());
		scheduleEntity.setStartDate(record.getStartTime());
		scheduleEntity.setEndDate(record.getEndTime());
		scheduleEntityMapper.insertSelective(scheduleEntity);
	}
}
