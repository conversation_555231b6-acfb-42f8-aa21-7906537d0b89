package com.chevron.credit.model;

import java.util.ArrayList;
import java.util.List;

public class WxTCreditRatioExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WxTCreditRatioExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRequestNoIsNull() {
            addCriterion("request_no is null");
            return (Criteria) this;
        }

        public Criteria andRequestNoIsNotNull() {
            addCriterion("request_no is not null");
            return (Criteria) this;
        }

        public Criteria andRequestNoEqualTo(String value) {
            addCriterion("request_no =", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotEqualTo(String value) {
            addCriterion("request_no <>", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThan(String value) {
            addCriterion("request_no >", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanOrEqualTo(String value) {
            addCriterion("request_no >=", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThan(String value) {
            addCriterion("request_no <", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanOrEqualTo(String value) {
            addCriterion("request_no <=", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLike(String value) {
            addCriterion("request_no like", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotLike(String value) {
            addCriterion("request_no not like", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoIn(List<String> values) {
            addCriterion("request_no in", values, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotIn(List<String> values) {
            addCriterion("request_no not in", values, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoBetween(String value1, String value2) {
            addCriterion("request_no between", value1, value2, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotBetween(String value1, String value2) {
            addCriterion("request_no not between", value1, value2, "requestNo");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(String value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(String value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(String value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(String value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(String value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(String value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLike(String value) {
            addCriterion("customer_id like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotLike(String value) {
            addCriterion("customer_id not like", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<String> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<String> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(String value1, String value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(String value1, String value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameIsNull() {
            addCriterion("custmoer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameIsNotNull() {
            addCriterion("custmoer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameEqualTo(String value) {
            addCriterion("custmoer_name =", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameNotEqualTo(String value) {
            addCriterion("custmoer_name <>", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameGreaterThan(String value) {
            addCriterion("custmoer_name >", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameGreaterThanOrEqualTo(String value) {
            addCriterion("custmoer_name >=", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameLessThan(String value) {
            addCriterion("custmoer_name <", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameLessThanOrEqualTo(String value) {
            addCriterion("custmoer_name <=", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameLike(String value) {
            addCriterion("custmoer_name like", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameNotLike(String value) {
            addCriterion("custmoer_name not like", value, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameIn(List<String> values) {
            addCriterion("custmoer_name in", values, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameNotIn(List<String> values) {
            addCriterion("custmoer_name not in", values, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameBetween(String value1, String value2) {
            addCriterion("custmoer_name between", value1, value2, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andCustmoerNameNotBetween(String value1, String value2) {
            addCriterion("custmoer_name not between", value1, value2, "custmoerName");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("year is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("year is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(String value) {
            addCriterion("year =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(String value) {
            addCriterion("year <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(String value) {
            addCriterion("year >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(String value) {
            addCriterion("year >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThan(String value) {
            addCriterion("year <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(String value) {
            addCriterion("year <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLike(String value) {
            addCriterion("year like", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotLike(String value) {
            addCriterion("year not like", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearIn(List<String> values) {
            addCriterion("year in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<String> values) {
            addCriterion("year not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(String value1, String value2) {
            addCriterion("year between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(String value1, String value2) {
            addCriterion("year not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andQuickRatioIsNull() {
            addCriterion("quick_ratio is null");
            return (Criteria) this;
        }

        public Criteria andQuickRatioIsNotNull() {
            addCriterion("quick_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andQuickRatioEqualTo(Double value) {
            addCriterion("quick_ratio =", value, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioNotEqualTo(Double value) {
            addCriterion("quick_ratio <>", value, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioGreaterThan(Double value) {
            addCriterion("quick_ratio >", value, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("quick_ratio >=", value, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioLessThan(Double value) {
            addCriterion("quick_ratio <", value, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioLessThanOrEqualTo(Double value) {
            addCriterion("quick_ratio <=", value, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioIn(List<Double> values) {
            addCriterion("quick_ratio in", values, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioNotIn(List<Double> values) {
            addCriterion("quick_ratio not in", values, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioBetween(Double value1, Double value2) {
            addCriterion("quick_ratio between", value1, value2, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andQuickRatioNotBetween(Double value1, Double value2) {
            addCriterion("quick_ratio not between", value1, value2, "quickRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioIsNull() {
            addCriterion("current_ratio is null");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioIsNotNull() {
            addCriterion("current_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioEqualTo(Double value) {
            addCriterion("current_ratio =", value, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioNotEqualTo(Double value) {
            addCriterion("current_ratio <>", value, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioGreaterThan(Double value) {
            addCriterion("current_ratio >", value, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("current_ratio >=", value, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioLessThan(Double value) {
            addCriterion("current_ratio <", value, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioLessThanOrEqualTo(Double value) {
            addCriterion("current_ratio <=", value, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioIn(List<Double> values) {
            addCriterion("current_ratio in", values, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioNotIn(List<Double> values) {
            addCriterion("current_ratio not in", values, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioBetween(Double value1, Double value2) {
            addCriterion("current_ratio between", value1, value2, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentRatioNotBetween(Double value1, Double value2) {
            addCriterion("current_ratio not between", value1, value2, "currentRatio");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityIsNull() {
            addCriterion("current_liability_to_equity is null");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityIsNotNull() {
            addCriterion("current_liability_to_equity is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityEqualTo(Double value) {
            addCriterion("current_liability_to_equity =", value, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityNotEqualTo(Double value) {
            addCriterion("current_liability_to_equity <>", value, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityGreaterThan(Double value) {
            addCriterion("current_liability_to_equity >", value, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityGreaterThanOrEqualTo(Double value) {
            addCriterion("current_liability_to_equity >=", value, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityLessThan(Double value) {
            addCriterion("current_liability_to_equity <", value, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityLessThanOrEqualTo(Double value) {
            addCriterion("current_liability_to_equity <=", value, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityIn(List<Double> values) {
            addCriterion("current_liability_to_equity in", values, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityNotIn(List<Double> values) {
            addCriterion("current_liability_to_equity not in", values, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityBetween(Double value1, Double value2) {
            addCriterion("current_liability_to_equity between", value1, value2, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andCurrentLiabilityToEquityNotBetween(Double value1, Double value2) {
            addCriterion("current_liability_to_equity not between", value1, value2, "currentLiabilityToEquity");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioIsNull() {
            addCriterion("long_term_liablity_total_assets_ratio is null");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioIsNotNull() {
            addCriterion("long_term_liablity_total_assets_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioEqualTo(Double value) {
            addCriterion("long_term_liablity_total_assets_ratio =", value, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioNotEqualTo(Double value) {
            addCriterion("long_term_liablity_total_assets_ratio <>", value, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioGreaterThan(Double value) {
            addCriterion("long_term_liablity_total_assets_ratio >", value, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("long_term_liablity_total_assets_ratio >=", value, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioLessThan(Double value) {
            addCriterion("long_term_liablity_total_assets_ratio <", value, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioLessThanOrEqualTo(Double value) {
            addCriterion("long_term_liablity_total_assets_ratio <=", value, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioIn(List<Double> values) {
            addCriterion("long_term_liablity_total_assets_ratio in", values, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioNotIn(List<Double> values) {
            addCriterion("long_term_liablity_total_assets_ratio not in", values, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioBetween(Double value1, Double value2) {
            addCriterion("long_term_liablity_total_assets_ratio between", value1, value2, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLongTermLiablityTotalAssetsRatioNotBetween(Double value1, Double value2) {
            addCriterion("long_term_liablity_total_assets_ratio not between", value1, value2, "longTermLiablityTotalAssetsRatio");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsIsNull() {
            addCriterion("liablities_assets is null");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsIsNotNull() {
            addCriterion("liablities_assets is not null");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsEqualTo(Double value) {
            addCriterion("liablities_assets =", value, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsNotEqualTo(Double value) {
            addCriterion("liablities_assets <>", value, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsGreaterThan(Double value) {
            addCriterion("liablities_assets >", value, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsGreaterThanOrEqualTo(Double value) {
            addCriterion("liablities_assets >=", value, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsLessThan(Double value) {
            addCriterion("liablities_assets <", value, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsLessThanOrEqualTo(Double value) {
            addCriterion("liablities_assets <=", value, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsIn(List<Double> values) {
            addCriterion("liablities_assets in", values, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsNotIn(List<Double> values) {
            addCriterion("liablities_assets not in", values, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsBetween(Double value1, Double value2) {
            addCriterion("liablities_assets between", value1, value2, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andLiablitiesAssetsNotBetween(Double value1, Double value2) {
            addCriterion("liablities_assets not between", value1, value2, "liablitiesAssets");
            return (Criteria) this;
        }

        public Criteria andEquityRatioIsNull() {
            addCriterion("equity_ratio is null");
            return (Criteria) this;
        }

        public Criteria andEquityRatioIsNotNull() {
            addCriterion("equity_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andEquityRatioEqualTo(Double value) {
            addCriterion("equity_ratio =", value, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioNotEqualTo(Double value) {
            addCriterion("equity_ratio <>", value, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioGreaterThan(Double value) {
            addCriterion("equity_ratio >", value, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("equity_ratio >=", value, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioLessThan(Double value) {
            addCriterion("equity_ratio <", value, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioLessThanOrEqualTo(Double value) {
            addCriterion("equity_ratio <=", value, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioIn(List<Double> values) {
            addCriterion("equity_ratio in", values, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioNotIn(List<Double> values) {
            addCriterion("equity_ratio not in", values, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioBetween(Double value1, Double value2) {
            addCriterion("equity_ratio between", value1, value2, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andEquityRatioNotBetween(Double value1, Double value2) {
            addCriterion("equity_ratio not between", value1, value2, "equityRatio");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverIsNull() {
            addCriterion("inventory_turnover is null");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverIsNotNull() {
            addCriterion("inventory_turnover is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverEqualTo(Double value) {
            addCriterion("inventory_turnover =", value, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverNotEqualTo(Double value) {
            addCriterion("inventory_turnover <>", value, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverGreaterThan(Double value) {
            addCriterion("inventory_turnover >", value, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverGreaterThanOrEqualTo(Double value) {
            addCriterion("inventory_turnover >=", value, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverLessThan(Double value) {
            addCriterion("inventory_turnover <", value, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverLessThanOrEqualTo(Double value) {
            addCriterion("inventory_turnover <=", value, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverIn(List<Double> values) {
            addCriterion("inventory_turnover in", values, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverNotIn(List<Double> values) {
            addCriterion("inventory_turnover not in", values, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverBetween(Double value1, Double value2) {
            addCriterion("inventory_turnover between", value1, value2, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andInventoryTurnoverNotBetween(Double value1, Double value2) {
            addCriterion("inventory_turnover not between", value1, value2, "inventoryTurnover");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryIsNull() {
            addCriterion("days_in_inventory is null");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryIsNotNull() {
            addCriterion("days_in_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryEqualTo(Double value) {
            addCriterion("days_in_inventory =", value, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryNotEqualTo(Double value) {
            addCriterion("days_in_inventory <>", value, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryGreaterThan(Double value) {
            addCriterion("days_in_inventory >", value, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryGreaterThanOrEqualTo(Double value) {
            addCriterion("days_in_inventory >=", value, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryLessThan(Double value) {
            addCriterion("days_in_inventory <", value, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryLessThanOrEqualTo(Double value) {
            addCriterion("days_in_inventory <=", value, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryIn(List<Double> values) {
            addCriterion("days_in_inventory in", values, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryNotIn(List<Double> values) {
            addCriterion("days_in_inventory not in", values, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryBetween(Double value1, Double value2) {
            addCriterion("days_in_inventory between", value1, value2, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andDaysInInventoryNotBetween(Double value1, Double value2) {
            addCriterion("days_in_inventory not between", value1, value2, "daysInInventory");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverIsNull() {
            addCriterion("account_receivable_trunover is null");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverIsNotNull() {
            addCriterion("account_receivable_trunover is not null");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverEqualTo(Double value) {
            addCriterion("account_receivable_trunover =", value, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverNotEqualTo(Double value) {
            addCriterion("account_receivable_trunover <>", value, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverGreaterThan(Double value) {
            addCriterion("account_receivable_trunover >", value, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverGreaterThanOrEqualTo(Double value) {
            addCriterion("account_receivable_trunover >=", value, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverLessThan(Double value) {
            addCriterion("account_receivable_trunover <", value, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverLessThanOrEqualTo(Double value) {
            addCriterion("account_receivable_trunover <=", value, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverIn(List<Double> values) {
            addCriterion("account_receivable_trunover in", values, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverNotIn(List<Double> values) {
            addCriterion("account_receivable_trunover not in", values, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverBetween(Double value1, Double value2) {
            addCriterion("account_receivable_trunover between", value1, value2, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andAccountReceivableTrunoverNotBetween(Double value1, Double value2) {
            addCriterion("account_receivable_trunover not between", value1, value2, "accountReceivableTrunover");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableIsNull() {
            addCriterion("days_in_accounts_receivable is null");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableIsNotNull() {
            addCriterion("days_in_accounts_receivable is not null");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableEqualTo(Double value) {
            addCriterion("days_in_accounts_receivable =", value, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableNotEqualTo(Double value) {
            addCriterion("days_in_accounts_receivable <>", value, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableGreaterThan(Double value) {
            addCriterion("days_in_accounts_receivable >", value, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableGreaterThanOrEqualTo(Double value) {
            addCriterion("days_in_accounts_receivable >=", value, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableLessThan(Double value) {
            addCriterion("days_in_accounts_receivable <", value, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableLessThanOrEqualTo(Double value) {
            addCriterion("days_in_accounts_receivable <=", value, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableIn(List<Double> values) {
            addCriterion("days_in_accounts_receivable in", values, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableNotIn(List<Double> values) {
            addCriterion("days_in_accounts_receivable not in", values, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableBetween(Double value1, Double value2) {
            addCriterion("days_in_accounts_receivable between", value1, value2, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andDaysInAccountsReceivableNotBetween(Double value1, Double value2) {
            addCriterion("days_in_accounts_receivable not between", value1, value2, "daysInAccountsReceivable");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsIsNull() {
            addCriterion("sale_current_assets is null");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsIsNotNull() {
            addCriterion("sale_current_assets is not null");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsEqualTo(Double value) {
            addCriterion("sale_current_assets =", value, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsNotEqualTo(Double value) {
            addCriterion("sale_current_assets <>", value, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsGreaterThan(Double value) {
            addCriterion("sale_current_assets >", value, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsGreaterThanOrEqualTo(Double value) {
            addCriterion("sale_current_assets >=", value, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsLessThan(Double value) {
            addCriterion("sale_current_assets <", value, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsLessThanOrEqualTo(Double value) {
            addCriterion("sale_current_assets <=", value, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsIn(List<Double> values) {
            addCriterion("sale_current_assets in", values, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsNotIn(List<Double> values) {
            addCriterion("sale_current_assets not in", values, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsBetween(Double value1, Double value2) {
            addCriterion("sale_current_assets between", value1, value2, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andSaleCurrentAssetsNotBetween(Double value1, Double value2) {
            addCriterion("sale_current_assets not between", value1, value2, "saleCurrentAssets");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverIsNull() {
            addCriterion("asset_turnover is null");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverIsNotNull() {
            addCriterion("asset_turnover is not null");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverEqualTo(Double value) {
            addCriterion("asset_turnover =", value, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverNotEqualTo(Double value) {
            addCriterion("asset_turnover <>", value, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverGreaterThan(Double value) {
            addCriterion("asset_turnover >", value, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverGreaterThanOrEqualTo(Double value) {
            addCriterion("asset_turnover >=", value, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverLessThan(Double value) {
            addCriterion("asset_turnover <", value, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverLessThanOrEqualTo(Double value) {
            addCriterion("asset_turnover <=", value, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverIn(List<Double> values) {
            addCriterion("asset_turnover in", values, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverNotIn(List<Double> values) {
            addCriterion("asset_turnover not in", values, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverBetween(Double value1, Double value2) {
            addCriterion("asset_turnover between", value1, value2, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andAssetTurnoverNotBetween(Double value1, Double value2) {
            addCriterion("asset_turnover not between", value1, value2, "assetTurnover");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIsNull() {
            addCriterion("profit_margin is null");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIsNotNull() {
            addCriterion("profit_margin is not null");
            return (Criteria) this;
        }

        public Criteria andProfitMarginEqualTo(Double value) {
            addCriterion("profit_margin =", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotEqualTo(Double value) {
            addCriterion("profit_margin <>", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginGreaterThan(Double value) {
            addCriterion("profit_margin >", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("profit_margin >=", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginLessThan(Double value) {
            addCriterion("profit_margin <", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginLessThanOrEqualTo(Double value) {
            addCriterion("profit_margin <=", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIn(List<Double> values) {
            addCriterion("profit_margin in", values, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotIn(List<Double> values) {
            addCriterion("profit_margin not in", values, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginBetween(Double value1, Double value2) {
            addCriterion("profit_margin between", value1, value2, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotBetween(Double value1, Double value2) {
            addCriterion("profit_margin not between", value1, value2, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioIsNull() {
            addCriterion("after_tax_profit_ratio is null");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioIsNotNull() {
            addCriterion("after_tax_profit_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioEqualTo(Double value) {
            addCriterion("after_tax_profit_ratio =", value, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioNotEqualTo(Double value) {
            addCriterion("after_tax_profit_ratio <>", value, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioGreaterThan(Double value) {
            addCriterion("after_tax_profit_ratio >", value, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("after_tax_profit_ratio >=", value, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioLessThan(Double value) {
            addCriterion("after_tax_profit_ratio <", value, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioLessThanOrEqualTo(Double value) {
            addCriterion("after_tax_profit_ratio <=", value, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioIn(List<Double> values) {
            addCriterion("after_tax_profit_ratio in", values, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioNotIn(List<Double> values) {
            addCriterion("after_tax_profit_ratio not in", values, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioBetween(Double value1, Double value2) {
            addCriterion("after_tax_profit_ratio between", value1, value2, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andAfterTaxProfitRatioNotBetween(Double value1, Double value2) {
            addCriterion("after_tax_profit_ratio not between", value1, value2, "afterTaxProfitRatio");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityIsNull() {
            addCriterion("return_on_equity is null");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityIsNotNull() {
            addCriterion("return_on_equity is not null");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityEqualTo(Double value) {
            addCriterion("return_on_equity =", value, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityNotEqualTo(Double value) {
            addCriterion("return_on_equity <>", value, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityGreaterThan(Double value) {
            addCriterion("return_on_equity >", value, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityGreaterThanOrEqualTo(Double value) {
            addCriterion("return_on_equity >=", value, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityLessThan(Double value) {
            addCriterion("return_on_equity <", value, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityLessThanOrEqualTo(Double value) {
            addCriterion("return_on_equity <=", value, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityIn(List<Double> values) {
            addCriterion("return_on_equity in", values, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityNotIn(List<Double> values) {
            addCriterion("return_on_equity not in", values, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityBetween(Double value1, Double value2) {
            addCriterion("return_on_equity between", value1, value2, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andReturnOnEquityNotBetween(Double value1, Double value2) {
            addCriterion("return_on_equity not between", value1, value2, "returnOnEquity");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsIsNull() {
            addCriterion("net_sales_to_total_assets is null");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsIsNotNull() {
            addCriterion("net_sales_to_total_assets is not null");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsEqualTo(Double value) {
            addCriterion("net_sales_to_total_assets =", value, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsNotEqualTo(Double value) {
            addCriterion("net_sales_to_total_assets <>", value, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsGreaterThan(Double value) {
            addCriterion("net_sales_to_total_assets >", value, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsGreaterThanOrEqualTo(Double value) {
            addCriterion("net_sales_to_total_assets >=", value, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsLessThan(Double value) {
            addCriterion("net_sales_to_total_assets <", value, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsLessThanOrEqualTo(Double value) {
            addCriterion("net_sales_to_total_assets <=", value, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsIn(List<Double> values) {
            addCriterion("net_sales_to_total_assets in", values, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsNotIn(List<Double> values) {
            addCriterion("net_sales_to_total_assets not in", values, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsBetween(Double value1, Double value2) {
            addCriterion("net_sales_to_total_assets between", value1, value2, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andNetSalesToTotalAssetsNotBetween(Double value1, Double value2) {
            addCriterion("net_sales_to_total_assets not between", value1, value2, "netSalesToTotalAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalIsNull() {
            addCriterion("working_capital is null");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalIsNotNull() {
            addCriterion("working_capital is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalEqualTo(Double value) {
            addCriterion("working_capital =", value, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalNotEqualTo(Double value) {
            addCriterion("working_capital <>", value, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalGreaterThan(Double value) {
            addCriterion("working_capital >", value, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalGreaterThanOrEqualTo(Double value) {
            addCriterion("working_capital >=", value, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalLessThan(Double value) {
            addCriterion("working_capital <", value, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalLessThanOrEqualTo(Double value) {
            addCriterion("working_capital <=", value, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalIn(List<Double> values) {
            addCriterion("working_capital in", values, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalNotIn(List<Double> values) {
            addCriterion("working_capital not in", values, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalBetween(Double value1, Double value2) {
            addCriterion("working_capital between", value1, value2, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andWorkingCapitalNotBetween(Double value1, Double value2) {
            addCriterion("working_capital not between", value1, value2, "workingCapital");
            return (Criteria) this;
        }

        public Criteria andEquityIsNull() {
            addCriterion("equity is null");
            return (Criteria) this;
        }

        public Criteria andEquityIsNotNull() {
            addCriterion("equity is not null");
            return (Criteria) this;
        }

        public Criteria andEquityEqualTo(Double value) {
            addCriterion("equity =", value, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityNotEqualTo(Double value) {
            addCriterion("equity <>", value, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityGreaterThan(Double value) {
            addCriterion("equity >", value, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityGreaterThanOrEqualTo(Double value) {
            addCriterion("equity >=", value, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityLessThan(Double value) {
            addCriterion("equity <", value, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityLessThanOrEqualTo(Double value) {
            addCriterion("equity <=", value, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityIn(List<Double> values) {
            addCriterion("equity in", values, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityNotIn(List<Double> values) {
            addCriterion("equity not in", values, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityBetween(Double value1, Double value2) {
            addCriterion("equity between", value1, value2, "equity");
            return (Criteria) this;
        }

        public Criteria andEquityNotBetween(Double value1, Double value2) {
            addCriterion("equity not between", value1, value2, "equity");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsIsNull() {
            addCriterion("working_assets is null");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsIsNotNull() {
            addCriterion("working_assets is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsEqualTo(Double value) {
            addCriterion("working_assets =", value, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsNotEqualTo(Double value) {
            addCriterion("working_assets <>", value, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsGreaterThan(Double value) {
            addCriterion("working_assets >", value, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsGreaterThanOrEqualTo(Double value) {
            addCriterion("working_assets >=", value, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsLessThan(Double value) {
            addCriterion("working_assets <", value, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsLessThanOrEqualTo(Double value) {
            addCriterion("working_assets <=", value, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsIn(List<Double> values) {
            addCriterion("working_assets in", values, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsNotIn(List<Double> values) {
            addCriterion("working_assets not in", values, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsBetween(Double value1, Double value2) {
            addCriterion("working_assets between", value1, value2, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andWorkingAssetsNotBetween(Double value1, Double value2) {
            addCriterion("working_assets not between", value1, value2, "workingAssets");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueIsNull() {
            addCriterion("estimated_value is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueIsNotNull() {
            addCriterion("estimated_value is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueEqualTo(Double value) {
            addCriterion("estimated_value =", value, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueNotEqualTo(Double value) {
            addCriterion("estimated_value <>", value, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueGreaterThan(Double value) {
            addCriterion("estimated_value >", value, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueGreaterThanOrEqualTo(Double value) {
            addCriterion("estimated_value >=", value, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueLessThan(Double value) {
            addCriterion("estimated_value <", value, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueLessThanOrEqualTo(Double value) {
            addCriterion("estimated_value <=", value, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueIn(List<Double> values) {
            addCriterion("estimated_value in", values, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueNotIn(List<Double> values) {
            addCriterion("estimated_value not in", values, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueBetween(Double value1, Double value2) {
            addCriterion("estimated_value between", value1, value2, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andEstimatedValueNotBetween(Double value1, Double value2) {
            addCriterion("estimated_value not between", value1, value2, "estimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditIndexIsNull() {
            addCriterion("credit_index is null");
            return (Criteria) this;
        }

        public Criteria andCreditIndexIsNotNull() {
            addCriterion("credit_index is not null");
            return (Criteria) this;
        }

        public Criteria andCreditIndexEqualTo(Double value) {
            addCriterion("credit_index =", value, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexNotEqualTo(Double value) {
            addCriterion("credit_index <>", value, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexGreaterThan(Double value) {
            addCriterion("credit_index >", value, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexGreaterThanOrEqualTo(Double value) {
            addCriterion("credit_index >=", value, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexLessThan(Double value) {
            addCriterion("credit_index <", value, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexLessThanOrEqualTo(Double value) {
            addCriterion("credit_index <=", value, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexIn(List<Double> values) {
            addCriterion("credit_index in", values, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexNotIn(List<Double> values) {
            addCriterion("credit_index not in", values, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexBetween(Double value1, Double value2) {
            addCriterion("credit_index between", value1, value2, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditIndexNotBetween(Double value1, Double value2) {
            addCriterion("credit_index not between", value1, value2, "creditIndex");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueIsNull() {
            addCriterion("credit_limit_estimated_value is null");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueIsNotNull() {
            addCriterion("credit_limit_estimated_value is not null");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueEqualTo(Double value) {
            addCriterion("credit_limit_estimated_value =", value, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueNotEqualTo(Double value) {
            addCriterion("credit_limit_estimated_value <>", value, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueGreaterThan(Double value) {
            addCriterion("credit_limit_estimated_value >", value, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueGreaterThanOrEqualTo(Double value) {
            addCriterion("credit_limit_estimated_value >=", value, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueLessThan(Double value) {
            addCriterion("credit_limit_estimated_value <", value, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueLessThanOrEqualTo(Double value) {
            addCriterion("credit_limit_estimated_value <=", value, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueIn(List<Double> values) {
            addCriterion("credit_limit_estimated_value in", values, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueNotIn(List<Double> values) {
            addCriterion("credit_limit_estimated_value not in", values, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueBetween(Double value1, Double value2) {
            addCriterion("credit_limit_estimated_value between", value1, value2, "creditLimitEstimatedValue");
            return (Criteria) this;
        }

        public Criteria andCreditLimitEstimatedValueNotBetween(Double value1, Double value2) {
            addCriterion("credit_limit_estimated_value not between", value1, value2, "creditLimitEstimatedValue");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}