package com.sys.quartz.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.quartz.CronScheduleBuilder;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sys.quartz.model.ScheduleJob;
import com.sys.quartz.service.impl.JobTaskService;


@Controller
@RequestMapping("/task")
public class JobTaskController {
	
	public final Logger log = Logger.getLogger(this.getClass());
	@Autowired
	private JobTaskService taskService;

	@RequestMapping("taskList")
	public String taskList(HttpServletRequest request) {
		List<ScheduleJob> taskList = taskService.getAllTask();
		request.setAttribute("taskList", taskList);
		return "view/taskList";
	}

	/*@RequestMapping("add")
	@ResponseBody
	public Map add(HttpServletRequest request,@RequestParam("startTime") String startTime, 
			@RequestParam("endTime") String endTime,@RequestParam("repetitionType") String repetitionType,
			@RequestParam("repetitionFrequency") String repetitionFrequency,@RequestParam("taskId") String taskId,@RequestParam("jobStatus") String jobStatus) throws ParseException {
		Map resMap = new HashMap();
		ScheduleJob scheduleJob = taskService.createScheduleJob(startTime,endTime,repetitionType,repetitionFrequency,taskId,jobStatus);
		try {
			String cronExpression = taskService.createCronExpression(scheduleJob.getStartTime(), 
					scheduleJob.getRepetitionType(), scheduleJob.getRepetitionFrequency());
			scheduleJob.setCronExpression(cronExpression);
			CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());
		} catch (Exception e) {
			resMap.put("code","cronExpressError");
			return resMap;
		}
		Object obj = null;
		try {
			if (StringUtils.isNotBlank(scheduleJob.getSpringId())) {
				obj = SpringUtils.getBean(scheduleJob.getSpringId());
			} else {
				Class clazz = Class.forName(scheduleJob.getBeanClass());
				obj = clazz.newInstance();
			}
		} catch (Exception e) {
			// do nothing.........
		}
		if (obj == null) {
			resMap.put("code","cannotFindTargetObject");
			return resMap;
		} else {
			Class clazz = obj.getClass();
			Method method = null;
			try {
				method = clazz.getMethod(scheduleJob.getMethodName(), new Class[]{Long.class});
			} catch (Exception e) {
				// do nothing.....
			}
			if (method == null) {
				resMap.put("code","cannotFindTargetMethod");
				return resMap;
			}
		}
		try {
			taskService.addTask(scheduleJob);
			taskService.addJob(scheduleJob);
		} catch (Exception e) {
			e.printStackTrace();
			resMap.put("code","saveTaskFailed");
			return resMap;
		}

		resMap.put("code","success");
		return resMap;
	}*/

	@RequestMapping("changeJobStatus.do")
	@ResponseBody
	public Map changeJobStatus(HttpServletRequest request, Long jobId, String cmd) {
		Map resMap = new HashMap();
		try {
			taskService.changeStatus(jobId, cmd);
		} catch (SchedulerException e) {
			log.error(e.getMessage(), e);
			resMap.put("code","任务状态改变失败！");
			return resMap;
		}
		resMap.put("code","success");
		return resMap;
	}

	@RequestMapping("updateCron.do")
	@ResponseBody
	public Map updateCron(HttpServletRequest request, Long jobId, String cron) {
		Map resMap = new HashMap();
		try {
			CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cron);
		} catch (Exception e) {
			resMap.put("code","cron表达式有误，不能被解析！");
			return resMap;
		}
		try {
			taskService.updateCron(jobId, cron);
		} catch (SchedulerException e) {
			resMap.put("code","cron更新失败！");
			return resMap;
		}
		resMap.put("code","success");
		return resMap;
	}
}
