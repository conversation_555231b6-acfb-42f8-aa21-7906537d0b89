<%@page import="com.common.util.StringUtils"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import= "com.common.util.ContextUtil"%>
<%@page import= "com.sys.auth.model.WxTUser"%>
<%@page import="com.sys.auth.model.WxTRole"%>
<%@page import="java.util.List"%>
<%@page import="java.util.Calendar"%>
<%@page import="java.net.URLDecoder"%>
<%
//    request.setCharacterEncoding("UTF-8");
    String initPartnerIdStr = request.getParameter("partnerId");
    Long initPartnerId = null;
    String initPartnerName = null;
    if(initPartnerIdStr != null){
        try{
            initPartnerId = Long.valueOf(initPartnerIdStr);
            initPartnerName = request.getParameter("partnerName") == null ? null : "'" + URLDecoder.decode(request.getParameter("partnerName"),"UTF-8") + "'";
        }catch (Exception e){

        }
    }
	int year = 0;
	int currentMonth = 12;
	Calendar cal = Calendar.getInstance();
	if(StringUtils.isNotBlank(request.getParameter("year"))){
		year = Integer.parseInt(request.getParameter("year"));
		if(year == cal.get(Calendar.YEAR)){
			currentMonth = cal.get(Calendar.MONTH) + 1;
		}
	}else{
		year = cal.get(Calendar.YEAR);
		currentMonth = cal.get(Calendar.MONTH) + 1;
	}
	int lastYear = year - 1;

    WxTUser curUser = ContextUtil.getCurUser();
    String userName = curUser.getChName();
    Long userId = curUser.getUserId();
    Long orgId = curUser.getOrgId();

    List<WxTRole> roleList = curUser.getRoleList();
    String roleType = "";
    String customerCategory = "Consumer";
    boolean isDistributor = false;
    String userModel = curUser.getUserModel();
    if(WxTUser.USER_MODEL_SP.equals(userModel)) {
        isDistributor = true;
    }

    boolean isCdmDistributor = false;
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Service_Partner_Admin")
                || role.getChRoleName().equals("Service_Partner_Manager")) {
            isCdmDistributor = true;
            break;
        }
        
        
    }

    boolean isCioDistributor = false;
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Caltex_Dealer")) {
            isCioDistributor = true;
            break;
        }
    }
    
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Chevron_CDM_Suppervisor") || role.getChRoleName().equals("Chevron_C&I_Suppervisor")){
        	roleType = "asm";
        }else if(role.getChRoleName().equals("Chevron_BD") || role.getChRoleName().equals("Chevron_Promote_Sales")){
        	roleType = "flsr";
        }else{
        	roleType = "admin";
        }
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>合伙人sell in分析</title>
    <%@include file="/common/jsp/common.jsp"%>
    <link href="${ctx }newhomepage/css/SpSellInInfoAnalysis.css?v=${version}" rel="stylesheet">
    <script type="text/javascript" src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
    <script type="text/javascript" src="${ctx }newhomepage/js/SpSellInInfoAnalysis.js?v=${version}8"></script>
    <script type="text/javascript">
        var orgId = <%=orgId%>;
        var currentYear = <%=year%>;
        var lastYear = <%=lastYear%>;
        var currentMonth = <%=currentMonth %>;
        var currentQuarter = parseInt((currentMonth + 2) / 3);
        var initPartnerName = decodeURIComponent(<%=initPartnerName%>);
        var initPartnerId = <%=initPartnerId%>;
        var currentParams = {
        		partnerId: initPartnerId,
        		partnerName: initPartnerName,
        		groupByType: '${param.groupByType}'
        };
        var roleType = '<%=roleType%>';
        var customerCategory = '<%=customerCategory%>';
        var loginCai = '<%=curUser.getCai() %>';
    </script>
    <style type="text/css">
        .control-text.control-auto {
            height: 30px;
            width: 180px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="not-top">
    <div  id="detailDiv" class="content-wrapper" style="min-height: 1400px;">
        <!--标题栏-->
        <div class="content-panel header-panel">
            <div class="header-title" id="titleDiv">合伙人sell in分析</div>
        </div>
        <!--查询条件-->
        <div class="content-panel query-panel">
            <div class="field-group" style="width: 280px;">
                <label class="field-label width-auto search-label" style="vertical-align: middle">经销商：</label>
                <div class="control-group">
                    <input type="hidden" name='partnerId' id="partnerId"/>
                </div>
                <%--<div class="query-btns" style="width: auto;">--%>
                <div class="query-btn field-label" style="width: auto; vertical-align: middle;margin-left: 10px;">
                    <button id="search" class="btn-query">查询</button>
                </div>
                <p class="no-partner-explain-text">不选择经销商，销售数据是显示您所负责经销商数据的汇总</p>
                <%--</div>--%>
            </div>
            <div class="row" id="year-mode" style="margin-right: 0px;margin-left:0px;margin-top: 0;">
                <div class="col-sm-12">
                        <div class="field-group" style="width:250px;">
                            <label class="search-label">年份：</label>
                            <div class="control-group years-wrapper" id="year_c"><ul></ul></div>
                        </div>
                    <%--<div class="field-group" style="width:100%">--%>
                        <%--<label class="search-label">月份：</label>--%>
                        <%--<div class="control-group monthes-wrapper" id="month_c"><ul></ul></div>--%>
                    <%--</div>--%>
                    <div class="field-group" style="width:500px">
                        <label class="search-label">统计方式：</label>
                        <div class="control-group monthes-wrapper" id="group_by_type_c"><ul></ul></div>
                    </div>
                </div>
            </div>
    <div class="content-box" style="max-height: none;text-align: center">
        <div style="margin: auto;">
            <div class="row">
                <!-- <p id="sell_in_YTD" class="ytd-info">销量(YTD)已经完成<span id="sell_in_value" class="emphasize">L</span></p> -->
                <!-- <p id="gross_info_YTD" class="ytd-info">毛利(YTD)已经完成<span id="gross_info_value" class="emphasize">CNY</span></p> -->
                  <p id="sell_in_YTD" class="ytd-info">销量(YTD)已经完成<span id="sell_in_value" class="emphasize">L</span>,&nbsp;完成目标(YTD)的<span id="sell_in_percent" class="emphasize">%</span></p>
                  <p id="gross_info_YTD" class="ytd-info">毛利(YTD)已经完成<span id="gross_info_value" class="emphasize">CNY</span>,&nbsp;完成目标(YTD)的<span id="gross_info_percent" class="emphasize">%</span></p>
            </div>
            <h2>销售详情分析</h2>
            <div class="row">
                <div class="grid-content-box high-content-box" style="margin-right: 8px;float: left;width: calc(50% - 10px);">
                    <div class="month-mode-wrapper">
                        <div class="content-panel echarts-block">
                            <div class="e-tables" id="totalSellinStatsByMonthChart">

                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-content-box high-content-box" style="margin-left: 8px;float: left;width: calc(50% - 10px);">
                    <div class="month-mode-wrapper">
                        <div class="content-panel echarts-block">
                            <div class="e-tables" id="sellinStatsByMonthAndTypeChart">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="sku-info-div">
            <h2>销售详情分析(按sku统计)</h2>
            <div class="grid-content-box high-content-box" style="margin-right: 15px;">
                <div class="row" style="margin-right: 0px;margin-left:0px;margin-top: 0; text-align: center">
                    <div class="col-sm-12">
                        <div class="query-panel" id="skuQuarterDiv">
                            <div class="field-group">
                                <label class="field-label">季度：</label>
                                <div class="control-group" id="skuQuarterC">
                                    <input id="skuQuarter" name="skuQuarter"
                                           class="input-small control-text" type="hidden"
                                           value=""/>
                                </div>
                            </div>
                        </div>
                        <div class="query-panel" id="skuMonthDiv" style="display: none;">
                            <div class="field-group">
                                <label class="field-label">月份：</label>
                                <div class="control-group" id="skuMonthC">
                                    <input id="skuMonth" name="skuMonth"
                                           class="input-small control-text" type="hidden"
                                           value=""/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-panel echarts-block">
                    <div class="echarts" style="height:550px" id="sellinStatsByMonthAndSkuChart"></div>
                </div>
            </div>
        </div>
    </div>
</div>
    </div>
</div>
</body>
</html>