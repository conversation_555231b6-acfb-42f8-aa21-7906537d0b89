INSERT INTO wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc, creator, create_time, updator, update_time)
VALUES ('qbr.dms.product.channel', 'QBR-DMS-产品渠道', 'QBR-DMS-产品渠道', NULL, NULL, NULL, NULL)
     , ('qbr.dms.product.category', 'QBR-DMS-产品分类', 'QBR-DMS-产品分类', NULL, NULL, NULL, NULL);

INSERT INTO wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, creator, create_time,
                           updator, update_time, sort_numb)
VALUES ('qbr.dms.product.channel', 'Chevron Others', '雪佛龙其它', '雪佛龙其它', '1', 1, '2020-06-14 20:55:52.977', 1,
        '2020-06-14 20:55:52.977', NULL)
     , ('qbr.dms.product.channel', 'Commercial', '商用油', '商用油', '1', 1, '2020-06-14 20:55:52.960', 1,
        '2020-06-14 20:55:52.960', NULL)
     , ('qbr.dms.product.channel', 'Consumer', '乘用车', '乘用车', '1', 1, '2020-06-14 20:55:52.960', 1,
        '2020-06-14 20:55:52.960', NULL);

INSERT INTO wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, creator, create_time,
                           updator, update_time, sort_numb)
VALUES ('qbr.dms.product.category', 'Consumer Others', '乘用车其它', '乘用车其它', '1', 1, '2020-06-02 14:43:00.717', 1,
        '2020-06-02 14:43:00.717', 30.0000000000)
     , ('qbr.dms.product.category', 'Coolant', '防冻液', '防冻液', '1', 1, '2020-06-02 14:43:00.733', 1,
        '2020-06-02 14:43:00.733', 20.0000000000)
     , ('qbr.dms.product.category', 'Fuel Additive', 'TCP添加剂', 'TCP添加剂', '1', 1, '2020-06-02 14:43:00.733', 1,
        '2020-06-02 14:43:00.733', 25.0000000000)
     , ('qbr.dms.product.category', 'PCMO Others', '金富力其它', '金富力其它', '1', 1, '2020-06-02 14:43:00.733', 1,
        '2020-06-02 14:43:00.733', 15.0000000000)
     , ('qbr.dms.product.category', 'SM & Below', '金富力低端', '金富力低端', '1', 1, '2020-06-02 14:43:00.750', 1,
        '2020-06-02 14:43:00.750', 10.0000000000)
     , ('qbr.dms.product.category', 'SN & Above', '金富力高端', '金富力高端', '1', 1, '2020-06-02 14:43:00.750', 1,
        '2020-06-02 14:43:00.750', 5.0000000000)
     , ('qbr.dms.product.category', 'Commercial Others', '商用油其它', '商用油其它', '1', 1, '2020-06-02 14:43:00.750', 1,
        '2020-06-02 14:43:00.750', 70.0000000000)
     , ('qbr.dms.product.category', 'HDMO Others', '德乐其它', '德乐其它', '1', 1, '2020-06-02 14:43:00.763', 1,
        '2020-06-02 14:43:00.763', 50.0000000000)
     , ('qbr.dms.product.category', 'HDMO Low', '德乐低端', '德乐低端', '1', 1, '2020-06-02 14:43:00.763', 1,
        '2020-06-02 14:43:00.763', 45.0000000000)
     , ('qbr.dms.product.category', 'HDMO High', '德乐高端', '德乐高端', '1', 1, '2020-06-02 14:43:00.780', 1,
        '2020-06-02 14:43:00.780', 35.0000000000);
INSERT INTO wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, creator, create_time,
                           updator, update_time, sort_numb)
VALUES ('qbr.dms.product.category', 'Hydraulic Oil Others', '液压油其它', '液压油其它', '1', 1, '2020-06-02 14:43:00.780', 1,
        '2020-06-02 14:43:00.780', 65.0000000000)
     , ('qbr.dms.product.category', 'Hydraulic Oil Low Tier', '液压油 Low Tier', '液压油 Low Tier', '1', 1,
        '2020-06-02 14:43:00.780', 1, '2020-06-02 14:43:00.780', 60.0000000000)
     , ('qbr.dms.product.category', 'Hydraulic Oil Mid Tier', '液压油 Mid Tier', '液压油 Mid Tier', '1', 1,
        '2020-06-02 14:43:00.797', 1, '2020-06-02 14:43:00.797', 55.0000000000)
     , ('qbr.dms.product.category', 'Chevron Others', '雪佛龙其他', '雪佛龙其他', '1', 1, '2020-06-02 17:48:54.773', 1,
        '2020-06-02 17:48:54.773', 75.0000000000)
     , ('qbr.dms.product.category', 'HDMO Middle', '德乐中端', '德乐中端', '1', 1, '2020-06-02 17:48:54.790', 1,
        '2020-06-02 17:48:54.790', 40.0000000000);