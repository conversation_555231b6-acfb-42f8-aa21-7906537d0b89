SELECT v.region "Region", v.sales_name_cn "FLSR", o.organization_name AS "所属经销商",
cu.ch_name "创建人",
CASE 
		WHEN we.employee_type = 'Owner' THEN '店长'
		ELSE '技师'
	END AS "会员属性（店长/技师）", we.name AS "姓名", we.mobile AS "手机号", w.work_shop_name AS "门店名称"
	, isnull(we.update_time, we.creation_time) AS "注册绑定时间", we.creation_time "创建时间", p.qrcode_point AS "瓶盖积分"
	, p.activity_point AS "活动积分", p.task_point AS "任务积分", p.register_point AS "注册奖励积分", p.total_import_point AS "积分合计"
FROM wx_t_workshop_employee we
	LEFT JOIN wx_t_work_shop w ON w.id = we.workshop_id
	left join wx_t_user u on w.excute_user_id=u.user_id
	LEFT JOIN wx_t_workshop_partner wp ON wp.workshop_id = we.workshop_id
	LEFT JOIN wx_t_organization o ON wp.partner_id = o.id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
	left join wx_t_user cu on cu.user_id=we.creator
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales v on v.distributor_id=pe.distributor_id and v.product_channel='Consumer'
	LEFT JOIN (
		SELECT SUM(pvd.POINT_VALUE) AS total_import_point, SUM(CASE 
				WHEN pb.EARN_TYPE = 'QR_CODE' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS qrcode_point
			, SUM(CASE 
				WHEN pb.EARN_TYPE = 'MISSION_REWARD' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS task_point, SUM(CASE 
				WHEN pb.EARN_TYPE = 'ACTIVITY_REWARD' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS activity_point
			, SUM(CASE 
				WHEN pb.EARN_TYPE = 'MECHANIC_REGISTER' THEN pvd.POINT_VALUE
				ELSE 0
			END) AS register_point, em.code, em.name
			, em.workshop_id, em.workshop_name
			, pvd.SUB_TYPE
		FROM dbo.wx_t_point_value_detail pvd
			LEFT JOIN dbo.wx_t_point_business pb ON pb.id = pvd.BUSINESS_ID
			LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
			LEFT JOIN dbo.wx_t_workshop_employee em ON em.code = pa.POINT_ACCOUNT_OWNER_CODE
			LEFT JOIN dbo.wx_t_work_shop shop ON shop.id = em.workshop_id
		WHERE 1 = 1
			AND pvd.POINT_TYPE = 'B2B_POINT'
			AND em.code IS NOT NULL
			AND pb.BUSINESS_TYPE_CODE = 'POINT_IMPORT'
		GROUP BY em.code, em.workshop_id, em.workshop_name, em.name, pvd.SUB_TYPE
	) p
	ON we.code = p.code
WHERE we.version_no = 1 --and we.creator=131536 
and w.partner_id!=9
ORDER BY v.region,v.sales_cai,o.id,u.user_id, w.id, we.employee_type DESC