{"version": 3, "sources": ["webpack:///./node_modules/vant/es/tab/index.js", "webpack:///./node_modules/vant/es/utils/dom/raf.js", "webpack:///./node_modules/vant/es/utils/router.js", "webpack:///./node_modules/vant/es/tabs/utils.js", "webpack:///./node_modules/vant/es/utils/dom/style.js", "webpack:///./node_modules/vant/es/utils/dom/event.js", "webpack:///./node_modules/vant/es/mixins/bind-event.js", "webpack:///./node_modules/vant/es/utils/constant.js", "webpack:///./node_modules/vant/es/utils/dom/scroll.js", "webpack:///./node_modules/vant/es/tabs/Title.js", "webpack:///./node_modules/vant/es/mixins/touch.js", "webpack:///./node_modules/vant/es/tabs/Content.js", "webpack:///./node_modules/vant/es/sticky/index.js", "webpack:///./node_modules/vant/es/tabs/index.js", "webpack:///./node_modules/vant/es/mixins/relation.js", "webpack:///./node_modules/vant/es/utils/index.js", "webpack:///./node_modules/@babel/runtime/helpers/esm/extends.js", "webpack:///./node_modules/vant/es/utils/create/bem.js", "webpack:///./node_modules/vant/es/utils/format/string.js", "webpack:///./node_modules/vant/es/mixins/slots.js", "webpack:///./node_modules/vant/es/utils/create/component.js", "webpack:///./node_modules/vant/es/utils/deep-assign.js", "webpack:///./node_modules/vant/es/locale/lang/zh-CN.js", "webpack:///./node_modules/vant/es/locale/index.js", "webpack:///./node_modules/vant/es/utils/create/i18n.js", "webpack:///./node_modules/vant/es/utils/create/index.js", "webpack:///./node_modules/vant/es/utils/validate/number.js", "webpack:///./node_modules/vant/es/utils/format/unit.js"], "names": ["_createNamespace", "createComponent", "bem", "mixins", "props", "name", "Number", "String", "title", "titleStyle", "disabled", "Boolean", "data", "inited", "computed", "computedName", "this", "index", "isActive", "parent", "currentName", "watch", "setLine", "render", "h", "slots", "shouldRender", "lazy<PERSON>ender", "Content", "animated", "inactive", "value", "prev", "Date", "now", "fallback", "fn", "curr", "ms", "Math", "max", "id", "setTimeout", "root", "global", "window", "iRaf", "requestAnimationFrame", "cancelAnimationFrame", "clearTimeout", "raf", "call", "route", "router", "config", "to", "url", "replace", "promise", "catch", "err", "location", "href", "routeProps", "Object", "scrollLeftTo", "el", "duration", "count", "from", "scrollLeft", "frames", "round", "animate", "isHidden", "element", "getComputedStyle", "display", "offsetParent", "supportsPassive", "opts", "defineProperty", "get", "addEventListener", "e", "on", "target", "event", "handler", "passive", "capture", "off", "removeEventListener", "BindEventMixin", "bind", "binded", "unbind", "mounted", "activated", "deactivated", "<PERSON><PERSON><PERSON><PERSON>", "BORDER", "BORDER_TOP_BOTTOM", "overflowScrollReg", "getScrollEventTarget", "rootParent", "node", "tagName", "nodeType", "_window$getComputedSt", "overflowY", "test", "_window$getComputedSt2", "parentNode", "htmlOverflowY", "getScrollTop", "scrollTop", "pageYOffset", "setScrollTop", "scrollTo", "scrollX", "getRootScrollTop", "document", "documentElement", "body", "setRootScrollTop", "getElementTop", "getBoundingClientRect", "top", "create", "type", "color", "ellipsis", "scrollable", "activeColor", "inactiveColor", "swipe<PERSON><PERSON><PERSON><PERSON>", "style", "isCard", "borderColor", "backgroundColor", "titleColor", "flexBasis", "methods", "onClick", "$emit", "arguments", "active", "complete", "MIN_DISTANCE", "getDirection", "x", "y", "TouchMixin", "extend", "direction", "touchStart", "resetTouchStatus", "startX", "touches", "clientX", "startY", "clientY", "touchMove", "touch", "deltaX", "deltaY", "offsetX", "abs", "offsetY", "MIN_SWIPE_DISTANCE", "swipeable", "currentIndex", "transform", "transitionDuration", "listeners", "touchstart", "touchmove", "touchend", "onTouchEnd", "touchcancel", "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "$createElement", "scroller", "$el", "onScroll", "zIndex", "container", "offsetTop", "default", "fixed", "height", "_this", "offsetHeight", "topToPageTop", "emitScrollEvent", "isFixed", "bottomToPageTop", "distanceToBottom", "resize", "model", "prop", "sticky", "background", "lineWidth", "lineHeight", "titleActiveColor", "titleInactiveColor", "border", "position", "lineStyle", "children", "length", "navStyle", "activeTab", "setCurrentIndexByName", "$nextTick", "scrollIntoView", "stickyFixed", "ceil", "onShow", "_this2", "_this3", "shouldAnimate", "titles", "$refs", "width", "offsetWidth", "left", "offsetLeft", "borderRadius", "matched", "filter", "tab", "defaultIndex", "setCurrentIndex", "findAvailableTab", "shouldEmitChange", "diff", "_this$children$index", "immediate", "nav", "params", "_ref", "_this4", "Nav", "map", "item", "Title", "$router", "Wrap", "flattenVNodes", "vnodes", "result", "traverse", "for<PERSON>ach", "vnode", "push", "ChildrenMixin", "_parent", "options", "_inject", "_computed", "indexKey", "inject", "disableBindRelation", "bindRelation", "indexOf", "concat", "sort", "a", "b", "$vnode", "ParentMixin", "provide", "isServer", "prototype", "$isServer", "isDef", "undefined", "isObj", "object", "path", "keys", "split", "key", "_extends", "assign", "i", "source", "hasOwnProperty", "apply", "ELEMENT", "MODS", "join", "symbol", "mods", "Array", "isArray", "ret", "createBEM", "camelizeRE", "camelize", "str", "_", "c", "toUpperCase", "SlotsMixin", "$slots", "$scopedSlots", "scopedSlot", "install", "<PERSON><PERSON>", "component", "unifySlots", "context", "scopedSlots", "transformFunctionComponent", "pure", "functional", "sfc", "<PERSON><PERSON><PERSON>", "val", "deepAssign", "tel", "save", "confirm", "cancel", "delete", "loading", "telEmpty", "nameEmpty", "confirmDelete", "telInvalid", "vanContactCard", "addText", "vanContactList", "vanPagination", "next", "vanPullRefresh", "pulling", "loosing", "vanSubmitBar", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valid", "unlimited", "discount", "_discount", "condition", "_condition", "vanCouponCell", "tips", "_count", "vanCouponList", "empty", "exchange", "close", "enable", "placeholder", "vanAddressEdit", "area", "postal", "areaEmpty", "addressEmpty", "postalEmpty", "defaultAddress", "telPlaceholder", "namePlaceholder", "areaPlaceholder", "vanAddressEditDetail", "vanAddressList", "add", "proto", "defineReactive", "util", "messages", "$vantMessages", "$vantLang", "use", "lang", "_this$add", "createI18N", "prefix", "message", "locale", "_len", "args", "_key", "createNamespace", "isNumber", "addUnit"], "mappings": "kHAAA,gEAKIA,EAAmB,eAAgB,OACnCC,EAAkBD,EAAiB,GACnCE,EAAMF,EAAiB,GAEZ,OAAAC,EAAA,CACbE,OAAQ,CAAC,eAAc,YACvBC,MAAO,eAAS,GAAI,OAAY,CAC9BC,KAAM,CAACC,OAAQC,QACfC,MAAOD,OACPE,WAAY,KACZC,SAAUC,UAEZC,KAAM,WACJ,MAAO,CACLC,QAAQ,IAGZC,SAAU,CACRC,aAAc,WACZ,OAAO,eAAMC,KAAKX,MAAQW,KAAKX,KAAOW,KAAKC,OAE7CC,SAAU,WACR,OAAOF,KAAKD,eAAiBC,KAAKG,OAAOC,cAG7CC,MAAO,CAEL,sBAAuB,WACrBL,KAAKH,OAASG,KAAKH,QAAUG,KAAKE,UAEpCV,MAAO,WACLQ,KAAKG,OAAOG,YAGhBC,OAAQ,SAAgBC,GACtB,IAAIC,EAAQT,KAAKS,MACbP,EAAWF,KAAKE,SAChBQ,EAAeV,KAAKH,SAAWG,KAAKG,OAAOQ,WAC3CC,EAAUF,EAAeD,IAAUD,IAEvC,OAAIR,KAAKG,OAAOU,SACPL,EAAE,MAAO,CACd,MAAS,CACP,KAAQ,WACR,eAAgBN,GAElB,MAAShB,EAAI,eAAgB,CAC3B4B,UAAWZ,KAEZ,CAACM,EAAE,MAAO,CACX,MAAStB,EAAI,SACZ,CAAC0B,MAGCJ,EAAE,MAAO,CACd,WAAc,CAAC,CACbnB,KAAM,OACN0B,MAAOb,IAET,MAAS,CACP,KAAQ,YAEV,MAAShB,EAAI,SACZ,CAAC0B,Q,mCCpER,8DAIII,EAAOC,KAAKC,MAGhB,SAASC,EAASC,GAChB,IAAIC,EAAOJ,KAAKC,MACZI,EAAKC,KAAKC,IAAI,EAAG,IAAMH,EAAOL,IAC9BS,EAAKC,WAAWN,EAAIE,GAExB,OADAN,EAAOK,EAAOC,EACPG,EAKT,IAAIE,EAAO,OAAWC,EAASC,OAG3BC,EAAOH,EAAKI,uBAAyBZ,EAG3BQ,EAAKK,sBAAwBL,EAAKM,aACzC,SAASC,EAAId,GAClB,OAAOU,EAAKK,KAAKR,EAAMP,M,0DCtBlB,SAASgB,EAAMC,EAAQC,GAC5B,IAAIC,EAAKD,EAAOC,GACZC,EAAMF,EAAOE,IACbC,EAAUH,EAAOG,QAErB,GAAIF,GAAMF,EAAQ,CAChB,IAAIK,EAAUL,EAAOI,EAAU,UAAY,QAAQF,GAG/CG,GAAWA,EAAQC,OACrBD,EAAQC,OAAM,SAAUC,GAEtB,GAAIA,GAAoB,yBAAbA,EAAIvD,KACb,MAAMuD,UAIHJ,IACTC,EAAUI,SAASJ,QAAQD,GAAOK,SAASC,KAAON,GArBtD,oEA2BO,IAAIO,EAAa,CACtBP,IAAKjD,OACLkD,QAAS9C,QACT4C,GAAI,CAAChD,OAAQyD,U,wFC7BR,SAASC,EAAaC,EAAIX,EAAIY,GACnC,IAAIC,EAAQ,EACRC,EAAOH,EAAGI,WACVC,EAAsB,IAAbJ,EAAiB,EAAI5B,KAAKiC,MAAiB,IAAXL,EAAkB,IAE/D,SAASM,IACPP,EAAGI,aAAef,EAAKc,GAAQE,IAEzBH,EAAQG,GACZ,OAAArB,EAAA,MAAIuB,GAIRA,I,gBCdK,SAASC,EAASC,GACvB,MAAoD,SAA7C9B,OAAO+B,iBAAiBD,GAASE,SAA+C,OAAzBF,EAAQG,a,gBCC7DC,GAAkB,EAE7B,IAAK,OACH,IACE,IAAIC,EAAO,GACXhB,OAAOiB,eAAeD,EAAM,UAAW,CAErCE,IAAK,WAEHH,GAAkB,KAGtBlC,OAAOsC,iBAAiB,eAAgB,KAAMH,GAC9C,MAAOI,IAGJ,SAASC,EAAGC,EAAQC,EAAOC,EAASC,QACzB,IAAZA,IACFA,GAAU,GAGP,QACHH,EAAOH,iBAAiBI,EAAOC,IAAST,GAAkB,CACxDW,SAAS,EACTD,QAASA,IAIR,SAASE,EAAIL,EAAQC,EAAOC,GAC5B,QACHF,EAAOM,oBAAoBL,EAAOC,GC5B/B,SAASK,EAAeL,GAC7B,SAASM,IACF9E,KAAK+E,SACRP,EAAQrC,KAAKnC,KAAMqE,GAAI,GACvBrE,KAAK+E,QAAS,GAIlB,SAASC,IACHhF,KAAK+E,SACPP,EAAQrC,KAAKnC,KAAM2E,GAAK,GACxB3E,KAAK+E,QAAS,GAIlB,MAAO,CACLE,QAASH,EACTI,UAAWJ,EACXK,YAAaH,EACbI,cAAeJ,GCtBZ,IAOIK,EAAS,eAMTC,EAAoBD,EAAS,eCXpCE,EAAoB,eACjB,SAASC,EAAqB7B,EAAS8B,QACzB,IAAfA,IACFA,EAAa5D,QAGf,IAAI6D,EAAO/B,EAEX,MAAO+B,GAAyB,SAAjBA,EAAKC,SAAwC,IAAlBD,EAAKE,UAAkBF,IAASD,EAAY,CACpF,IAAII,EAAwBhE,OAAO+B,iBAAiB8B,GAChDI,EAAYD,EAAsBC,UAEtC,GAAIP,EAAkBQ,KAAKD,GAAY,CACrC,GAAqB,SAAjBJ,EAAKC,QACP,OAAOD,EAIT,IAAIM,EAAyBnE,OAAO+B,iBAAiB8B,EAAKO,YACtDC,EAAgBF,EAAuBF,UAE3C,GAAIP,EAAkBQ,KAAKG,GACzB,OAAOR,EAIXA,EAAOA,EAAKO,WAGd,OAAOR,EAEF,SAASU,EAAaxC,GAC3B,MAAO,cAAeA,EAAUA,EAAQyC,UAAYzC,EAAQ0C,YAEvD,SAASC,EAAa3C,EAAS5C,GACpC,cAAe4C,EAAUA,EAAQyC,UAAYrF,EAAQ4C,EAAQ4C,SAAS5C,EAAQ6C,QAASzF,GAElF,SAAS0F,IACd,OAAO5E,OAAOwE,aAAeK,SAASC,gBAAgBP,WAAaM,SAASE,KAAKR,WAAa,EAEzF,SAASS,EAAiB9F,GAC/BuF,EAAazE,OAAQd,GACrBuF,EAAaI,SAASE,KAAM7F,GAGvB,SAAS+F,EAAcnD,GAC5B,OAAQA,IAAY9B,OAAS,EAAI8B,EAAQoD,wBAAwBC,KAAOP,IC/C1E,IAAIzH,EAAmB,OAAAiI,EAAA,MAAgB,OACnChI,EAAkBD,EAAiB,GACnCE,EAAMF,EAAiB,GAEZ,EAAAC,EAAA,CACbG,MAAO,CACL8H,KAAM3H,OACN4H,MAAO5H,OACPC,MAAOD,OACPW,SAAUP,QACVyH,SAAUzH,QACVD,SAAUC,QACV0H,WAAY1H,QACZ2H,YAAa/H,OACbgI,cAAehI,OACfiI,eAAgBlI,QAElBQ,SAAU,CACR2H,MAAO,WACL,IAAIA,EAAQ,GACRN,EAAQnH,KAAKmH,MACbjH,EAAWF,KAAKE,SAChBwH,EAAuB,SAAd1H,KAAKkH,KAEdC,GAASO,IACXD,EAAME,YAAcR,EAEfnH,KAAKN,WACJQ,EACFuH,EAAMG,gBAAkBT,EAExBM,EAAMN,MAAQA,IAKpB,IAAIU,EAAa3H,EAAWF,KAAKsH,YAActH,KAAKuH,cAUpD,OARIM,IACFJ,EAAMN,MAAQU,GAGZ7H,KAAKqH,YAAcrH,KAAKoH,WAC1BK,EAAMK,UAAY,GAAK9H,KAAKwH,eAAiB,KAGxCC,IAGXM,QAAS,CACPC,QAAS,WACPhI,KAAKiI,MAAM,WAGf1H,OAAQ,WACN,IAAIC,EAAI0H,UAAU,GAClB,OAAO1H,EAAE,MAAO,CACd,MAAS,CACP,KAAQ,MACR,gBAAiBR,KAAKE,UAExB,MAAShB,EAAI,CACXiJ,OAAQnI,KAAKE,SACbR,SAAUM,KAAKN,SACf0I,UAAWpI,KAAKoH,WAElB,MAASpH,KAAKyH,MACd,GAAM,CACJ,MAASzH,KAAKgI,UAEf,CAACxH,EAAE,OAAQ,CACZ,MAAS,CACP,eAAgBR,KAAKoH,WAEtB,CAACpH,KAAKS,SAAWT,KAAKR,a,wBC3EzB6I,EAAe,GAEnB,SAASC,EAAaC,EAAGC,GACvB,OAAID,EAAIC,GAAKD,EAAIF,EACR,aAGLG,EAAID,GAAKC,EAAIH,EACR,WAGF,GAGF,IAAII,EAAa,aAAIC,OAAO,CACjC9I,KAAM,WACJ,MAAO,CACL+I,UAAW,KAGfZ,QAAS,CACPa,WAAY,SAAoBrE,GAC9BvE,KAAK6I,mBACL7I,KAAK8I,OAASvE,EAAMwE,QAAQ,GAAGC,QAC/BhJ,KAAKiJ,OAAS1E,EAAMwE,QAAQ,GAAGG,SAEjCC,UAAW,SAAmB5E,GAC5B,IAAI6E,EAAQ7E,EAAMwE,QAAQ,GAC1B/I,KAAKqJ,OAASD,EAAMJ,QAAUhJ,KAAK8I,OACnC9I,KAAKsJ,OAASF,EAAMF,QAAUlJ,KAAKiJ,OACnCjJ,KAAKuJ,QAAUhI,KAAKiI,IAAIxJ,KAAKqJ,QAC7BrJ,KAAKyJ,QAAUlI,KAAKiI,IAAIxJ,KAAKsJ,QAC7BtJ,KAAK2I,UAAY3I,KAAK2I,WAAaL,EAAatI,KAAKuJ,QAASvJ,KAAKyJ,UAErEZ,iBAAkB,WAChB7I,KAAK2I,UAAY,GACjB3I,KAAKqJ,OAAS,EACdrJ,KAAKsJ,OAAS,EACdtJ,KAAKuJ,QAAU,EACfvJ,KAAKyJ,QAAU,MCpCjB,EAAmB,OAAAxC,EAAA,MAAgB,QACnC,EAAkB,EAAiB,GACnC,EAAM,EAAiB,GAEvByC,EAAqB,GACV,IAAgB,CAC7BvK,OAAQ,CAACsJ,GACTrJ,MAAO,CACLgE,MAAO9D,OACP6D,SAAU7D,OACVuB,SAAUlB,QACVgK,UAAWhK,QACXiK,aAActK,QAEhBQ,SAAU,CACR2H,MAAO,WACL,GAAIzH,KAAKa,SACP,MAAO,CACLgJ,UAAW,gBAAkB,EAAI7J,KAAK4J,aAAe,IAAM,WAC3DE,mBAAoB9J,KAAKmD,SAAW,MAI1C4G,UAAW,WACT,GAAI/J,KAAK2J,UACP,MAAO,CACLK,WAAYhK,KAAK4I,WACjBqB,UAAWjK,KAAKmJ,UAChBe,SAAUlK,KAAKmK,WACfC,YAAapK,KAAKmK,cAK1BpC,QAAS,CAEPoC,WAAY,WACV,IAAIxB,EAAY3I,KAAK2I,UACjBU,EAASrJ,KAAKqJ,OACdO,EAAe5J,KAAK4J,aAGN,eAAdjB,GAA8B3I,KAAKuJ,SAAWG,IAE5CL,EAAS,GAAsB,IAAjBO,EAChB5J,KAAKiI,MAAM,SAAU2B,EAAe,GAC3BP,EAAS,GAAKO,IAAiB5J,KAAKoD,MAAQ,GACrDpD,KAAKiI,MAAM,SAAU2B,EAAe,KAI1CS,YAAa,WACX,IAAI7J,EAAIR,KAAKsK,eAEb,OAAItK,KAAKa,SACAL,EAAE,MAAO,CACd,MAAS,EAAI,SACb,MAASR,KAAKyH,OACb,CAACzH,KAAKS,UAGJT,KAAKS,UAGhBF,OAAQ,WACN,IAAIC,EAAI0H,UAAU,GAClB,OAAO1H,EAAE,MAAO,CACd,MAAS,EAAI,UAAW,CACtBK,SAAUb,KAAKa,WAEjB,GAAM,eAAS,GAAIb,KAAK+J,YACvB,CAAC/J,KAAKqK,mBCvET,EAAmB,OAAApD,EAAA,MAAgB,UACnC,EAAkB,EAAiB,GACnC,EAAM,EAAiB,GAEZ,IAAgB,CAC7B9H,OAAQ,CAAC0F,GAAe,SAAUC,GAC3B9E,KAAKuK,WACRvK,KAAKuK,SAAW/E,EAAqBxF,KAAKwK,MAG5C1F,EAAK9E,KAAKuK,SAAU,SAAUvK,KAAKyK,UAAU,GAC7CzK,KAAKyK,eAEPrL,MAAO,CACLsL,OAAQpL,OACRqL,UAAW,KACXC,UAAW,CACT1D,KAAM5H,OACNuL,QAAS,IAGbjL,KAAM,WACJ,MAAO,CACLkL,OAAO,EACPC,OAAQ,EACRlB,UAAW,IAGf/J,SAAU,CACR2H,MAAO,WACL,GAAKzH,KAAK8K,MAAV,CAIA,IAAIrD,EAAQ,GAcZ,OAZI,eAAMzH,KAAK0K,UACbjD,EAAMiD,OAAS1K,KAAK0K,QAGlB1K,KAAK4K,WAAa5K,KAAK8K,QACzBrD,EAAMT,IAAMhH,KAAK4K,UAAY,MAG3B5K,KAAK6J,YACPpC,EAAMoC,UAAY,kBAAoB7J,KAAK6J,UAAY,UAGlDpC,KAGXM,QAAS,CACP0C,SAAU,WACR,IAAIO,EAAQhL,KAEZA,KAAK+K,OAAS/K,KAAKwK,IAAIS,aACvB,IAAIN,EAAY3K,KAAK2K,UACjBC,EAAY5K,KAAK4K,UACjBxE,EAAYD,EAAatE,QACzBqJ,EAAepE,EAAc9G,KAAKwK,KAElCW,EAAkB,WACpBH,EAAM/C,MAAM,SAAU,CACpB7B,UAAWA,EACXgF,QAASJ,EAAMF,SAKnB,GAAIH,EAAW,CACb,IAAIU,EAAkBH,EAAeP,EAAUM,aAE/C,GAAI7E,EAAYwE,EAAY5K,KAAK+K,OAASM,EAAiB,CACzD,IAAIC,EAAmBtL,KAAK+K,OAAS3E,EAAYiF,EAUjD,OARIC,EAAmBtL,KAAK+K,QAC1B/K,KAAK8K,OAAQ,EACb9K,KAAK6J,YAAcyB,EAAmBV,IAEtC5K,KAAK8K,OAAQ,OAGfK,KAKA/E,EAAYwE,EAAYM,GAC1BlL,KAAK8K,OAAQ,EACb9K,KAAK6J,UAAY,GAEjB7J,KAAK8K,OAAQ,EAGfK,MAGJ5K,OAAQ,WACN,IAAIC,EAAI0H,UAAU,GACd4C,EAAQ9K,KAAK8K,MACbrD,EAAQ,CACVsD,OAAQD,EAAQ9K,KAAK+K,OAAS,KAAO,MAEvC,OAAOvK,EAAE,MAAO,CACd,MAASiH,GACR,CAACjH,EAAE,MAAO,CACX,MAAS,EAAI,CACXsK,MAAOA,IAET,MAAS9K,KAAKyH,OACb,CAACzH,KAAKS,eCtGT,EAAmB,OAAAwG,EAAA,MAAgB,QACnC,EAAkB,EAAiB,GACnC,EAAM,EAAiB,GAEZ,SAAgB,CAC7B9H,OAAQ,CAAC,eAAY,WAAY0F,GAAe,SAAUC,GACxDA,EAAKjD,OAAQ,SAAU7B,KAAKuL,QAAQ,OAEtCC,MAAO,CACLC,KAAM,UAERrM,MAAO,CACL+H,MAAO5H,OACPmM,OAAQ/L,QACRkB,SAAUlB,QACVgK,UAAWhK,QACXgM,WAAYpM,OACZqM,UAAW,CAACtM,OAAQC,QACpBsM,WAAY,CAACvM,OAAQC,QACrBuM,iBAAkBvM,OAClBwM,mBAAoBxM,OACpB2H,KAAM,CACJA,KAAM3H,OACNsL,QAAS,QAEX1C,OAAQ,CACNjB,KAAM,CAAC5H,OAAQC,QACfsL,QAAS,GAEXmB,OAAQ,CACN9E,KAAMvH,QACNkL,SAAS,GAEXzD,SAAU,CACRF,KAAMvH,QACNkL,SAAS,GAEX1H,SAAU,CACR+D,KAAM5H,OACNuL,QAAS,IAEXD,UAAW,CACT1D,KAAM5H,OACNuL,QAAS,GAEXlK,WAAY,CACVuG,KAAMvH,QACNkL,SAAS,GAEXrD,eAAgB,CACdN,KAAM5H,OACNuL,QAAS,IAGbjL,KAAM,WACJ,MAAO,CACLqM,SAAU,GACVrC,aAAc,KACdsC,UAAW,CACTtE,gBAAiB5H,KAAKmH,SAI5BrH,SAAU,CAERuH,WAAY,WACV,OAAOrH,KAAKmM,SAASC,OAASpM,KAAKwH,iBAAmBxH,KAAKoH,UAE7DiF,SAAU,WACR,MAAO,CACL1E,YAAa3H,KAAKmH,MAClBwE,WAAY3L,KAAK2L,aAGrBvL,YAAa,WACX,IAAIkM,EAAYtM,KAAKmM,SAASnM,KAAK4J,cAEnC,GAAI0C,EACF,OAAOA,EAAUvM,eAIvBM,MAAO,CACL8G,MAAO,UACPgB,OAAQ,SAAgB9I,GAClBA,IAASW,KAAKI,aAChBJ,KAAKuM,sBAAsBlN,IAG/B8M,SAAU,WACR,IAAInB,EAAQhL,KAEZA,KAAKuM,sBAAsBvM,KAAKI,aAAeJ,KAAKmI,QACpDnI,KAAKM,UACLN,KAAKwM,WAAU,WACbxB,EAAMyB,gBAAe,OAGzB7C,aAAc,WACZ5J,KAAKyM,iBACLzM,KAAKM,UAEDN,KAAK0M,aACP7F,EAAiBtF,KAAKoL,KAAK7F,EAAc9G,KAAKwK,KAAOxK,KAAK4K,cAIhE3F,QAAS,WACPjF,KAAK4M,UAEP1H,UAAW,WACTlF,KAAK4M,SACL5M,KAAKM,WAEPyH,QAAS,CAEPwD,OAAQ,WACNvL,KAAKM,WAEPsM,OAAQ,WACN,IAAIC,EAAS7M,KAEbA,KAAKwM,WAAU,WACbK,EAAOhN,QAAS,EAEhBgN,EAAOJ,gBAAe,OAI1BnM,QAAS,WACP,IAAIwM,EAAS9M,KAET+M,EAAgB/M,KAAKH,OACzBG,KAAKwM,WAAU,WACb,IAAIQ,EAASF,EAAOG,MAAMD,OAE1B,GAAKA,GAAWA,EAAOF,EAAOlD,eAAiC,SAAhBkD,EAAO5F,OAAmBxD,EAASoJ,EAAOtC,KAAzF,CAIA,IAAIhL,EAAQwN,EAAOF,EAAOlD,cAAcY,IACpCoB,EAAYkB,EAAOlB,UACnBC,EAAaiB,EAAOjB,WACpBqB,EAAQ,eAAMtB,GAAaA,EAAYpM,EAAM2N,YAAc,EAC3DC,EAAO5N,EAAM6N,WAAa7N,EAAM2N,YAAc,EAC9CjB,EAAY,CACdgB,MAAO,eAAQA,GACftF,gBAAiBkF,EAAO3F,MACxB0C,UAAW,cAAgBuD,EAAO,wBAOpC,GAJIL,IACFb,EAAUpC,mBAAqBgD,EAAO3J,SAAW,KAG/C,eAAM0I,GAAa,CACrB,IAAId,EAAS,eAAQc,GACrBK,EAAUnB,OAASA,EACnBmB,EAAUoB,aAAevC,EAG3B+B,EAAOZ,UAAYA,OAIvBK,sBAAuB,SAA+BlN,GACpD,IAAIkO,EAAUvN,KAAKmM,SAASqB,QAAO,SAAUC,GAC3C,OAAOA,EAAI1N,eAAiBV,KAE1BqO,GAAgB1N,KAAKmM,SAAS,IAAM,IAAIlM,OAAS,EACrDD,KAAK2N,gBAAgBJ,EAAQnB,OAASmB,EAAQ,GAAGtN,MAAQyN,IAE3DC,gBAAiB,SAAyB/D,GAGxC,GAFAA,EAAe5J,KAAK4N,iBAAiBhE,GAEjC,eAAMA,IAAiBA,IAAiB5J,KAAK4J,aAAc,CAC7D,IAAIiE,EAAyC,OAAtB7N,KAAK4J,aAC5B5J,KAAK4J,aAAeA,EACpB5J,KAAKiI,MAAM,QAASjI,KAAKI,aAErByN,GACF7N,KAAKiI,MAAM,SAAUjI,KAAKI,YAAaJ,KAAKmM,SAASvC,GAAcpK,SAIzEoO,iBAAkB,SAA0B3N,GAC1C,IAAI6N,EAAO7N,EAAQD,KAAK4J,cAAgB,EAAI,EAE5C,MAAO3J,GAAS,GAAKA,EAAQD,KAAKmM,SAASC,OAAQ,CACjD,IAAKpM,KAAKmM,SAASlM,GAAOP,SACxB,OAAOO,EAGTA,GAAS6N,IAIb9F,QAAS,SAAiB/H,GACxB,IAAI8N,EAAuB/N,KAAKmM,SAASlM,GACrCT,EAAQuO,EAAqBvO,MAC7BE,EAAWqO,EAAqBrO,SAChCK,EAAegO,EAAqBhO,aAEpCL,EACFM,KAAKiI,MAAM,WAAYlI,EAAcP,IAErCQ,KAAK2N,gBAAgB1N,GACrBD,KAAKiI,MAAM,QAASlI,EAAcP,KAItCiN,eAAgB,SAAwBuB,GACtC,IAAIhB,EAAShN,KAAKiN,MAAMD,OAExB,GAAKhN,KAAKqH,YAAe2F,GAAWA,EAAOhN,KAAK4J,cAAhD,CAIA,IAAIqE,EAAMjO,KAAKiN,MAAMgB,IACjBzO,EAAQwN,EAAOhN,KAAK4J,cAAcY,IAClCjI,EAAK/C,EAAM6N,YAAcY,EAAId,YAAc3N,EAAM2N,aAAe,EACpElK,EAAagL,EAAK1L,EAAIyL,EAAY,EAAIhO,KAAKmD,YAE7CsH,SAAU,SAAkByD,GAC1BlO,KAAK0M,YAAcwB,EAAO9C,QAC1BpL,KAAKiI,MAAM,SAAUiG,KAGzB3N,OAAQ,WACN,IACI4N,EADAC,EAASpO,KAGTQ,EAAI0H,UAAU,GACdhB,EAAOlH,KAAKkH,KACZE,EAAWpH,KAAKoH,SAChBvG,EAAWb,KAAKa,SAChBwG,EAAarH,KAAKqH,WAClBgH,EAAMrO,KAAKmM,SAASmC,KAAI,SAAUC,EAAMtO,GAC1C,OAAOO,EAAEgO,EAAO,CACd,IAAO,SACP,UAAY,EACZ,MAAS,CACP,KAAQtH,EACR,MAASqH,EAAK/O,MACd,MAAS4O,EAAOjH,MAChB,SAAYlH,IAAUmO,EAAOxE,aAC7B,SAAYxC,EACZ,SAAYmH,EAAK7O,SACjB,WAAc2H,EACd,YAAe+G,EAAOtC,iBACtB,cAAiBsC,EAAOrC,mBACxB,eAAkBqC,EAAO5G,gBAE3B,MAAS+G,EAAK9O,WACd,YAAe,CACboL,QAAS,WACP,OAAO0D,EAAK9N,MAAM,WAGtB,GAAM,CACJ,MAAS,WACP2N,EAAOpG,QAAQ/H,GAEf,eAAMsO,EAAKE,QAASF,UAKxBG,EAAOlO,EAAE,MAAO,CAClB,IAAO,OACP,MAAS,CAAC,EAAI,OAAQ,CACpB6G,WAAYA,KACT8G,EAAO,GAAIA,EAAK7I,GAA8B,SAAT4B,GAAmBlH,KAAKgM,OAAQmC,KACzE,CAAC3N,EAAE,MAAO,CACX,IAAO,MACP,MAAS,CACP,KAAQ,WAEV,MAAS,EAAI,MAAO,CAAC0G,IACrB,MAASlH,KAAKqM,UACb,CAACrM,KAAKS,MAAM,YAAa4N,EAAc,SAATnH,GAAmB1G,EAAE,MAAO,CAC3D,MAAS,EAAI,QACb,MAASR,KAAKkM,YACZlM,KAAKS,MAAM,iBACf,OAAOD,EAAE,MAAO,CACd,MAAS,EAAI,CAAC0G,KACb,CAAClH,KAAK0L,OAASlL,EAAE,EAAQ,CAC1B,MAAS,CACP,UAAaR,KAAKwK,IAClB,UAAaxK,KAAK4K,WAEpB,GAAM,CACJ,OAAU5K,KAAKyK,WAEhB,CAACiE,IAASA,EAAMlO,EAAEI,EAAS,CAC5B,MAAS,CACP,MAASZ,KAAKmM,SAASC,OACvB,SAAYvL,EACZ,SAAYb,KAAKmD,SACjB,UAAanD,KAAK2J,UAClB,aAAgB3J,KAAK4J,cAEvB,GAAM,CACJ,OAAU5J,KAAK2N,kBAEhB,CAAC3N,KAAKS,gB,kCC7Tb,oFAEA,SAASkO,EAAcC,GACrB,IAAIC,EAAS,GAEb,SAASC,EAASF,GAChBA,EAAOG,SAAQ,SAAUC,GACvBH,EAAOI,KAAKD,GAERA,EAAM7C,UACR2C,EAASE,EAAM7C,aAMrB,OADA2C,EAASF,GACFC,EAGF,SAASK,EAAcC,EAASC,GACrC,IAAIC,EAASC,OAEG,IAAZF,IACFA,EAAU,IAGZ,IAAIG,EAAWH,EAAQG,UAAY,QACnC,OAAO,aAAI7G,OAAO,CAChB8G,QAASH,EAAU,GAAIA,EAAQF,GAAW,CACxCtE,QAAS,MACRwE,GACHvP,UAAWwP,EAAY,CACrBnP,OAAQ,WACN,OAAIH,KAAKyP,oBACA,KAGFzP,KAAKmP,KAEbG,EAAUC,GAAY,WAEvB,OADAvP,KAAK0P,eACE1P,KAAKG,OAAOgM,SAASwD,QAAQ3P,OACnCsP,GACHrK,QAAS,WACPjF,KAAK0P,gBAEPtK,cAAe,WACb,IAAI4F,EAAQhL,KAERA,KAAKG,SACPH,KAAKG,OAAOgM,SAAWnM,KAAKG,OAAOgM,SAASqB,QAAO,SAAUe,GAC3D,OAAOA,IAASvD,OAItBjD,QAAS,CACP2H,aAAc,WACZ,GAAK1P,KAAKG,SAAkD,IAAxCH,KAAKG,OAAOgM,SAASwD,QAAQ3P,MAAjD,CAIA,IAAImM,EAAW,GAAGyD,OAAO5P,KAAKG,OAAOgM,SAAU,CAACnM,OAC5C4O,EAASD,EAAc3O,KAAKG,OAAOM,SACvC0L,EAAS0D,MAAK,SAAUC,EAAGC,GACzB,OAAOnB,EAAOe,QAAQG,EAAEE,QAAUpB,EAAOe,QAAQI,EAAEC,WAErDhQ,KAAKG,OAAOgM,SAAWA,OAKxB,SAAS8D,EAAY9P,GAC1B,MAAO,CACL+P,QAAS,WACP,IAAI/B,EAEJ,OAAOA,EAAO,GAAIA,EAAKhO,GAAUH,KAAMmO,GAEzCvO,KAAM,WACJ,MAAO,CACLuM,SAAU,Q,kCChFlB,wJAGWgE,EAAW,aAAIC,UAAUC,UAE7B,SAASC,EAAMvP,GACpB,YAAiBwP,IAAVxP,GAAiC,OAAVA,EAEzB,SAASyP,EAAMjI,GACpB,IAAIrB,SAAcqB,EAClB,OAAa,OAANA,IAAwB,WAATrB,GAA8B,aAATA,GAEtC,SAAShD,EAAIuM,EAAQC,GAC1B,IAAIC,EAAOD,EAAKE,MAAM,KAClB/B,EAAS4B,EAIb,OAHAE,EAAK5B,SAAQ,SAAU8B,GACrBhC,EAASyB,EAAMzB,EAAOgC,IAAQhC,EAAOgC,GAAO,MAEvChC,I,8EClBM,SAASiC,IAetB,OAdAA,EAAW9N,OAAO+N,QAAU,SAAUzM,GACpC,IAAK,IAAI0M,EAAI,EAAGA,EAAI9I,UAAUkE,OAAQ4E,IAAK,CACzC,IAAIC,EAAS/I,UAAU8I,GAEvB,IAAK,IAAIH,KAAOI,EACVjO,OAAOoN,UAAUc,eAAe/O,KAAK8O,EAAQJ,KAC/CvM,EAAOuM,GAAOI,EAAOJ,IAK3B,OAAOvM,GAGFwM,EAASK,MAAMnR,KAAMkI,a,kCCP9B,IAAIkJ,EAAU,KACVC,EAAO,KAEX,SAASC,EAAKjS,EAAM6D,EAAIqO,GACtB,OAAOrO,EAAK7D,EAAOkS,EAASrO,EAAK7D,EAGnC,SAAS,EAAOA,EAAMmS,GACpB,GAAoB,kBAATA,EACT,OAAOF,EAAKjS,EAAMmS,EAAMH,GAG1B,GAAII,MAAMC,QAAQF,GAChB,OAAOA,EAAKlD,KAAI,SAAUC,GACxB,OAAO,EAAOlP,EAAMkP,MAIxB,IAAIoD,EAAM,GAQV,OANIH,GACFxO,OAAO2N,KAAKa,GAAMzC,SAAQ,SAAU8B,GAClCc,EAAItS,EAAOgS,EAAOR,GAAOW,EAAKX,MAI3Bc,EAGF,SAASC,EAAUvS,GACxB,OAAO,SAAU6D,EAAIsO,GAOnB,OANItO,GAAoB,kBAAPA,IACfsO,EAAOtO,EACPA,EAAK,IAGPA,EAAKoO,EAAKjS,EAAM6D,EAAIkO,GACbI,EAAO,CAACtO,EAAI,EAAOA,EAAIsO,IAAStO,GC7C3C,IAAI2O,EAAa,SACV,SAASC,EAASC,GACvB,OAAOA,EAAItP,QAAQoP,GAAY,SAAUG,EAAGC,GAC1C,OAAOA,EAAEC,iB,gBCEFC,EAAa,aAAIzJ,OAAO,CACjCX,QAAS,CACPtH,MAAO,SAAepB,EAAMD,QACb,IAATC,IACFA,EAAO,WAGT,IAAI+S,EAASpS,KAAKoS,OACdC,EAAerS,KAAKqS,aACpBC,EAAaD,EAAahT,GAE9B,OAAIiT,EACKA,EAAWlT,GAGbgT,EAAO/S,OCZpB,SAASkT,EAAQC,GACf,IAAInT,EAAOW,KAAKX,KAChBmT,EAAIC,UAAUpT,EAAMW,MACpBwS,EAAIC,UAAUX,EAAS,IAAMzS,GAAOW,MAI/B,SAAS0S,EAAWC,GAEzB,IAAIC,EAAcD,EAAQC,aAAeD,EAAQ/S,KAAKgT,aAAe,GACjEnS,EAAQkS,EAAQlS,QAQpB,OAPAuC,OAAO2N,KAAKlQ,GAAOsO,SAAQ,SAAU8B,GAC9B+B,EAAY/B,KACf+B,EAAY/B,GAAO,WACjB,OAAOpQ,EAAMoQ,QAIZ+B,EAGT,SAASC,EAA2BC,GAClC,MAAO,CACLC,YAAY,EACZ3T,MAAO0T,EAAK1T,MACZoM,MAAOsH,EAAKtH,MACZjL,OAAQ,SAAgBC,EAAGmS,GACzB,OAAOG,EAAKtS,EAAGmS,EAAQvT,MAAOsT,EAAWC,GAAUA,KAKlD,SAAS1T,EAAgBI,GAC9B,OAAO,SAAU2T,GAYf,MAXmB,oBAARA,IACTA,EAAMH,EAA2BG,IAG9BA,EAAID,aACPC,EAAI7T,OAAS6T,EAAI7T,QAAU,GAC3B6T,EAAI7T,OAAO8P,KAAKkD,IAGlBa,EAAI3T,KAAOA,EACX2T,EAAIT,QAAUA,EACPS,G,gBCpDP,EAAiBhQ,OAAOoN,UAAUc,eAEtC,SAAS+B,EAAU1Q,EAAIc,EAAMwN,GAC3B,IAAIqC,EAAM7P,EAAKwN,GAEV,eAAMqC,KAIN,EAAe/Q,KAAKI,EAAIsO,IAAS,eAAMqC,IAAuB,oBAARA,EAIzD3Q,EAAGsO,GAAOsC,EAAWnQ,OAAOT,EAAGsO,IAAOxN,EAAKwN,IAH3CtO,EAAGsO,GAAOqC,GAOP,SAASC,EAAW5Q,EAAIc,GAI7B,OAHAL,OAAO2N,KAAKtN,GAAM0L,SAAQ,SAAU8B,GAClCoC,EAAU1Q,EAAIc,EAAMwN,MAEftO,ECtBM,OACblD,KAAM,KACN+T,IAAK,KACLC,KAAM,KACNC,QAAS,KACTC,OAAQ,KACRC,OAAQ,KACRpL,SAAU,KACVqL,QAAS,SACTC,SAAU,QACVC,UAAW,QACXC,cAAe,SACfC,WAAY,WACZC,eAAgB,CACdC,QAAS,SAEXC,eAAgB,CACdD,QAAS,SAEXE,cAAe,CACbjT,KAAM,MACNkT,KAAM,OAERC,eAAgB,CACdC,QAAS,YACTC,QAAS,aAEXC,aAAc,CACZC,MAAO,OAETC,UAAW,CACTC,MAAO,MACPC,UAAW,QACXC,SAAU,SAAkBC,GAC1B,OAAOA,EAAY,KAErBC,UAAW,SAAmBC,GAC5B,MAAO,IAAWA,EAAa,QAGnCC,cAAe,CACbvV,MAAO,MACPwV,KAAM,OACN5R,MAAO,SAAe6R,GACpB,OAAOA,EAAS,QAGpBC,cAAe,CACbC,MAAO,QACPC,SAAU,KACVC,MAAO,QACPC,OAAQ,SACR5V,SAAU,UACV6V,YAAa,UAEfC,eAAgB,CACdC,KAAM,KACNC,OAAQ,OACRC,UAAW,QACXC,aAAc,UACdC,YAAa,YACbC,eAAgB,WAChBC,eAAgB,SAChBC,gBAAiB,QACjBC,gBAAiB,eAEnBC,qBAAsB,CACpB3B,MAAO,OACPgB,YAAa,iBAEfY,eAAgB,CACdC,IAAK,SCpELC,EAAQ,aAAIjG,UACZkG,EAAiB,aAAIC,KAAKD,eAC9BA,EAAeD,EAAO,YAAa,SACnCC,EAAeD,EAAO,gBAAiB,CACrC,QAAS,IAEI,OACbG,SAAU,WACR,OAAOH,EAAMI,cAAcJ,EAAMK,YAEnCC,IAAK,SAAaC,EAAMJ,GACtB,IAAIK,EAEJR,EAAMK,UAAYE,EAClB5W,KAAKoW,KAAKS,EAAY,GAAIA,EAAUD,GAAQJ,EAAUK,KAExDT,IAAK,SAAaI,QACC,IAAbA,IACFA,EAAW,IAGbrD,EAAWkD,EAAMI,cAAeD,KCrB7B,SAASM,EAAWzX,GACzB,IAAI0X,EAASjF,EAASzS,GAAQ,IAC9B,OAAO,SAAUqR,GAGf,IAFA,IAAIsG,EAAU,eAAIC,EAAOT,WAAYO,EAASrG,IAAS,eAAIuG,EAAOT,WAAY9F,GAErEwG,EAAOhP,UAAUkE,OAAQ+K,EAAO,IAAI1F,MAAMyF,EAAO,EAAIA,EAAO,EAAI,GAAIE,EAAO,EAAGA,EAAOF,EAAME,IAClGD,EAAKC,EAAO,GAAKlP,UAAUkP,GAG7B,MAA0B,oBAAZJ,EAAyBA,EAAQ7F,WAAM,EAAQgG,GAAQH,GCTlE,SAASK,EAAgBhY,GAE9B,OADAA,EAAO,OAASA,EACT,CAACJ,EAAgBI,GAAOuS,EAAUvS,GAAOyX,EAAWzX,IAL7D,mC,kDCAO,SAASiY,EAASvW,GACvB,MAAO,gBAAgBgF,KAAKhF,GCCvB,SAASwW,EAAQxW,GACtB,GAAK,eAAMA,GAKX,OADAA,EAAQxB,OAAOwB,GACRuW,EAASvW,GAASA,EAAQ,KAAOA,EAR1C", "file": "h5/js/chunk-ee81c0da.b4875923.js", "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { isDef, createNamespace } from '../utils';\nimport { ChildrenMixin } from '../mixins/relation';\nimport { routeProps } from '../utils/router';\n\nvar _createNamespace = createNamespace('tab'),\n    createComponent = _createNamespace[0],\n    bem = _createNamespace[1];\n\nexport default createComponent({\n  mixins: [ChildrenMixin('vanTabs')],\n  props: _extends({}, routeProps, {\n    name: [Number, String],\n    title: String,\n    titleStyle: null,\n    disabled: Boolean\n  }),\n  data: function data() {\n    return {\n      inited: false\n    };\n  },\n  computed: {\n    computedName: function computedName() {\n      return isDef(this.name) ? this.name : this.index;\n    },\n    isActive: function isActive() {\n      return this.computedName === this.parent.currentName;\n    }\n  },\n  watch: {\n    // eslint-disable-next-line object-shorthand\n    'parent.currentIndex': function parentCurrentIndex() {\n      this.inited = this.inited || this.isActive;\n    },\n    title: function title() {\n      this.parent.setLine();\n    }\n  },\n  render: function render(h) {\n    var slots = this.slots,\n        isActive = this.isActive;\n    var shouldRender = this.inited || !this.parent.lazyRender;\n    var Content = shouldRender ? slots() : h();\n\n    if (this.parent.animated) {\n      return h(\"div\", {\n        \"attrs\": {\n          \"role\": \"tabpanel\",\n          \"aria-hidden\": !isActive\n        },\n        \"class\": bem('pane-wrapper', {\n          inactive: !isActive\n        })\n      }, [h(\"div\", {\n        \"class\": bem('pane')\n      }, [Content])]);\n    }\n\n    return h(\"div\", {\n      \"directives\": [{\n        name: \"show\",\n        value: isActive\n      }],\n      \"attrs\": {\n        \"role\": \"tabpanel\"\n      },\n      \"class\": bem('pane')\n    }, [Content]);\n  }\n});", "/**\n * requestAnimationFrame polyfill\n */\nimport { isServer } from '..';\nvar prev = Date.now();\n/* istanbul ignore next */\n\nfunction fallback(fn) {\n  var curr = Date.now();\n  var ms = Math.max(0, 16 - (curr - prev));\n  var id = setTimeout(fn, ms);\n  prev = curr + ms;\n  return id;\n}\n/* istanbul ignore next */\n\n\nvar root = isServer ? global : window;\n/* istanbul ignore next */\n\nvar iRaf = root.requestAnimationFrame || fallback;\n/* istanbul ignore next */\n\nvar iCancel = root.cancelAnimationFrame || root.clearTimeout;\nexport function raf(fn) {\n  return iRaf.call(root, fn);\n} // double raf for animation\n\nexport function doubleRaf(fn) {\n  raf(function () {\n    raf(fn);\n  });\n}\nexport function cancelRaf(id) {\n  iCancel.call(root, id);\n}", "/**\n * Vue Router support\n */\nexport function route(router, config) {\n  var to = config.to,\n      url = config.url,\n      replace = config.replace;\n\n  if (to && router) {\n    var promise = router[replace ? 'replace' : 'push'](to);\n    /* istanbul ignore else */\n\n    if (promise && promise.catch) {\n      promise.catch(function (err) {\n        /* istanbul ignore if */\n        if (err && err.name !== 'NavigationDuplicated') {\n          throw err;\n        }\n      });\n    }\n  } else if (url) {\n    replace ? location.replace(url) : location.href = url;\n  }\n}\nexport function functionalRoute(context) {\n  route(context.parent && context.parent.$router, context.props);\n}\nexport var routeProps = {\n  url: String,\n  replace: Boolean,\n  to: [String, Object]\n};", "import { raf } from '../utils/dom/raf';\nexport function scrollLeftTo(el, to, duration) {\n  var count = 0;\n  var from = el.scrollLeft;\n  var frames = duration === 0 ? 1 : Math.round(duration * 1000 / 16);\n\n  function animate() {\n    el.scrollLeft += (to - from) / frames;\n\n    if (++count < frames) {\n      raf(animate);\n    }\n  }\n\n  animate();\n}", "export function isH<PERSON>den(element) {\n  return window.getComputedStyle(element).display === 'none' || element.offsetParent === null;\n}", "import { isServer } from '..';\n// eslint-disable-next-line import/no-mutable-exports\nexport var supportsPassive = false;\n\nif (!isServer) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', {\n      // eslint-disable-next-line getter-return\n      get: function get() {\n        /* istanbul ignore next */\n        supportsPassive = true;\n      }\n    });\n    window.addEventListener('test-passive', null, opts); // eslint-disable-next-line no-empty\n  } catch (e) {}\n}\n\nexport function on(target, event, handler, passive) {\n  if (passive === void 0) {\n    passive = false;\n  }\n\n  if (!isServer) {\n    target.addEventListener(event, handler, supportsPassive ? {\n      capture: false,\n      passive: passive\n    } : false);\n  }\n}\nexport function off(target, event, handler) {\n  if (!isServer) {\n    target.removeEventListener(event, handler);\n  }\n}\nexport function stopPropagation(event) {\n  event.stopPropagation();\n}\nexport function preventDefault(event, isStopPropagation) {\n  /* istanbul ignore else */\n  if (typeof event.cancelable !== 'boolean' || event.cancelable) {\n    event.preventDefault();\n  }\n\n  if (isStopPropagation) {\n    stopPropagation(event);\n  }\n}", "/**\n * Bind event when mounted or activated\n */\nimport { on, off } from '../utils/dom/event';\nexport function BindEventMixin(handler) {\n  function bind() {\n    if (!this.binded) {\n      handler.call(this, on, true);\n      this.binded = true;\n    }\n  }\n\n  function unbind() {\n    if (this.binded) {\n      handler.call(this, off, false);\n      this.binded = false;\n    }\n  }\n\n  return {\n    mounted: bind,\n    activated: bind,\n    deactivated: unbind,\n    beforeDestroy: unbind\n  };\n}", "// color\nexport var RED = '#ee0a24';\nexport var BLUE = '#1989fa';\nexport var GREEN = '#07c160';\nexport var WHITE = '#fff';\nexport var GRAY = '#c9c9c9';\nexport var GRAY_DARK = '#969799'; // border\n\nexport var BORDER = 'van-hairline';\nexport var BORDER_TOP = BORDER + \"--top\";\nexport var BORDER_LEFT = BORDER + \"--left\";\nexport var BORDER_RIGHT = BORDER + \"--right\";\nexport var BORDER_BOTTOM = BORDER + \"--bottom\";\nexport var BORDER_SURROUND = BORDER + \"--surround\";\nexport var BORDER_TOP_BOTTOM = BORDER + \"--top-bottom\";\nexport var BORDER_UNSET_TOP_BOTTOM = BORDER + \"-unset--top-bottom\";", "// get nearest scroll element\n// http://w3help.org/zh-cn/causes/SD9013\n// http://stackoverflow.com/questions/17016740/onscroll-function-is-not-working-for-chrome\nvar overflowScrollReg = /scroll|auto/i;\nexport function getScrollEventTarget(element, rootParent) {\n  if (rootParent === void 0) {\n    rootParent = window;\n  }\n\n  var node = element;\n\n  while (node && node.tagName !== 'HTML' && node.nodeType === 1 && node !== rootParent) {\n    var _window$getComputedSt = window.getComputedStyle(node),\n        overflowY = _window$getComputedSt.overflowY;\n\n    if (overflowScrollReg.test(overflowY)) {\n      if (node.tagName !== 'BODY') {\n        return node;\n      } // see: https://github.com/youzan/vant/issues/3823\n\n\n      var _window$getComputedSt2 = window.getComputedStyle(node.parentNode),\n          htmlOverflowY = _window$getComputedSt2.overflowY;\n\n      if (overflowScrollReg.test(htmlOverflowY)) {\n        return node;\n      }\n    }\n\n    node = node.parentNode;\n  }\n\n  return rootParent;\n}\nexport function getScrollTop(element) {\n  return 'scrollTop' in element ? element.scrollTop : element.pageYOffset;\n}\nexport function setScrollTop(element, value) {\n  'scrollTop' in element ? element.scrollTop = value : element.scrollTo(element.scrollX, value);\n}\nexport function getRootScrollTop() {\n  return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n}\nexport function setRootScrollTop(value) {\n  setScrollTop(window, value);\n  setScrollTop(document.body, value);\n} // get distance from element top to page top\n\nexport function getElementTop(element) {\n  return (element === window ? 0 : element.getBoundingClientRect().top) + getRootScrollTop();\n}\nexport function getVisibleHeight(element) {\n  return element === window ? element.innerHeight : element.getBoundingClientRect().height;\n}", "import { createNamespace } from '../utils';\n\nvar _createNamespace = createNamespace('tab'),\n    createComponent = _createNamespace[0],\n    bem = _createNamespace[1];\n\nexport default createComponent({\n  props: {\n    type: String,\n    color: String,\n    title: String,\n    isActive: <PERSON><PERSON><PERSON>,\n    ellipsis: <PERSON><PERSON><PERSON>,\n    disabled: <PERSON><PERSON><PERSON>,\n    scrollable: <PERSON><PERSON><PERSON>,\n    activeColor: String,\n    inactiveColor: String,\n    swipeThreshold: Number\n  },\n  computed: {\n    style: function style() {\n      var style = {};\n      var color = this.color,\n          isActive = this.isActive;\n      var isCard = this.type === 'card'; // card theme color\n\n      if (color && isCard) {\n        style.borderColor = color;\n\n        if (!this.disabled) {\n          if (isActive) {\n            style.backgroundColor = color;\n          } else {\n            style.color = color;\n          }\n        }\n      }\n\n      var titleColor = isActive ? this.activeColor : this.inactiveColor;\n\n      if (titleColor) {\n        style.color = titleColor;\n      }\n\n      if (this.scrollable && this.ellipsis) {\n        style.flexBasis = 88 / this.swipeThreshold + \"%\";\n      }\n\n      return style;\n    }\n  },\n  methods: {\n    onClick: function onClick() {\n      this.$emit('click');\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    return h(\"div\", {\n      \"attrs\": {\n        \"role\": \"tab\",\n        \"aria-selected\": this.isActive\n      },\n      \"class\": bem({\n        active: this.isActive,\n        disabled: this.disabled,\n        complete: !this.ellipsis\n      }),\n      \"style\": this.style,\n      \"on\": {\n        \"click\": this.onClick\n      }\n    }, [h(\"span\", {\n      \"class\": {\n        'van-ellipsis': this.ellipsis\n      }\n    }, [this.slots() || this.title])]);\n  }\n});", "import Vue from 'vue';\nvar MIN_DISTANCE = 10;\n\nfunction getDirection(x, y) {\n  if (x > y && x > MIN_DISTANCE) {\n    return 'horizontal';\n  }\n\n  if (y > x && y > MIN_DISTANCE) {\n    return 'vertical';\n  }\n\n  return '';\n}\n\nexport var TouchMixin = Vue.extend({\n  data: function data() {\n    return {\n      direction: ''\n    };\n  },\n  methods: {\n    touchStart: function touchStart(event) {\n      this.resetTouchStatus();\n      this.startX = event.touches[0].clientX;\n      this.startY = event.touches[0].clientY;\n    },\n    touchMove: function touchMove(event) {\n      var touch = event.touches[0];\n      this.deltaX = touch.clientX - this.startX;\n      this.deltaY = touch.clientY - this.startY;\n      this.offsetX = Math.abs(this.deltaX);\n      this.offsetY = Math.abs(this.deltaY);\n      this.direction = this.direction || getDirection(this.offsetX, this.offsetY);\n    },\n    resetTouchStatus: function resetTouchStatus() {\n      this.direction = '';\n      this.deltaX = 0;\n      this.deltaY = 0;\n      this.offsetX = 0;\n      this.offsetY = 0;\n    }\n  }\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createNamespace } from '../utils';\nimport { TouchMixin } from '../mixins/touch';\n\nvar _createNamespace = createNamespace('tabs'),\n    createComponent = _createNamespace[0],\n    bem = _createNamespace[1];\n\nvar MIN_SWIPE_DISTANCE = 50;\nexport default createComponent({\n  mixins: [TouchMixin],\n  props: {\n    count: Number,\n    duration: Number,\n    animated: Boolean,\n    swipeable: Boolean,\n    currentIndex: Number\n  },\n  computed: {\n    style: function style() {\n      if (this.animated) {\n        return {\n          transform: \"translate3d(\" + -1 * this.currentIndex * 100 + \"%, 0, 0)\",\n          transitionDuration: this.duration + \"s\"\n        };\n      }\n    },\n    listeners: function listeners() {\n      if (this.swipeable) {\n        return {\n          touchstart: this.touchStart,\n          touchmove: this.touchMove,\n          touchend: this.onTouchEnd,\n          touchcancel: this.onTouchEnd\n        };\n      }\n    }\n  },\n  methods: {\n    // watch swipe touch end\n    onTouchEnd: function onTouchEnd() {\n      var direction = this.direction,\n          deltaX = this.deltaX,\n          currentIndex = this.currentIndex;\n      /* istanbul ignore else */\n\n      if (direction === 'horizontal' && this.offsetX >= MIN_SWIPE_DISTANCE) {\n        /* istanbul ignore else */\n        if (deltaX > 0 && currentIndex !== 0) {\n          this.$emit('change', currentIndex - 1);\n        } else if (deltaX < 0 && currentIndex !== this.count - 1) {\n          this.$emit('change', currentIndex + 1);\n        }\n      }\n    },\n    genChildren: function genChildren() {\n      var h = this.$createElement;\n\n      if (this.animated) {\n        return h(\"div\", {\n          \"class\": bem('track'),\n          \"style\": this.style\n        }, [this.slots()]);\n      }\n\n      return this.slots();\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    return h(\"div\", {\n      \"class\": bem('content', {\n        animated: this.animated\n      }),\n      \"on\": _extends({}, this.listeners)\n    }, [this.genChildren()]);\n  }\n});", "import { createNamespace, isDef } from '../utils';\nimport { BindEventMixin } from '../mixins/bind-event';\nimport { getScrollTop, getElementTop, getScrollEventTarget } from '../utils/dom/scroll';\n\nvar _createNamespace = createNamespace('sticky'),\n    createComponent = _createNamespace[0],\n    bem = _createNamespace[1];\n\nexport default createComponent({\n  mixins: [BindEventMixin(function (bind) {\n    if (!this.scroller) {\n      this.scroller = getScrollEventTarget(this.$el);\n    }\n\n    bind(this.scroller, 'scroll', this.onScroll, true);\n    this.onScroll();\n  })],\n  props: {\n    zIndex: Number,\n    container: null,\n    offsetTop: {\n      type: Number,\n      default: 0\n    }\n  },\n  data: function data() {\n    return {\n      fixed: false,\n      height: 0,\n      transform: 0\n    };\n  },\n  computed: {\n    style: function style() {\n      if (!this.fixed) {\n        return;\n      }\n\n      var style = {};\n\n      if (isDef(this.zIndex)) {\n        style.zIndex = this.zIndex;\n      }\n\n      if (this.offsetTop && this.fixed) {\n        style.top = this.offsetTop + \"px\";\n      }\n\n      if (this.transform) {\n        style.transform = \"translate3d(0, \" + this.transform + \"px, 0)\";\n      }\n\n      return style;\n    }\n  },\n  methods: {\n    onScroll: function onScroll() {\n      var _this = this;\n\n      this.height = this.$el.offsetHeight;\n      var container = this.container,\n          offsetTop = this.offsetTop;\n      var scrollTop = getScrollTop(window);\n      var topToPageTop = getElementTop(this.$el);\n\n      var emitScrollEvent = function emitScrollEvent() {\n        _this.$emit('scroll', {\n          scrollTop: scrollTop,\n          isFixed: _this.fixed\n        });\n      }; // The sticky component should be kept inside the container element\n\n\n      if (container) {\n        var bottomToPageTop = topToPageTop + container.offsetHeight;\n\n        if (scrollTop + offsetTop + this.height > bottomToPageTop) {\n          var distanceToBottom = this.height + scrollTop - bottomToPageTop;\n\n          if (distanceToBottom < this.height) {\n            this.fixed = true;\n            this.transform = -(distanceToBottom + offsetTop);\n          } else {\n            this.fixed = false;\n          }\n\n          emitScrollEvent();\n          return;\n        }\n      }\n\n      if (scrollTop + offsetTop > topToPageTop) {\n        this.fixed = true;\n        this.transform = 0;\n      } else {\n        this.fixed = false;\n      }\n\n      emitScrollEvent();\n    }\n  },\n  render: function render() {\n    var h = arguments[0];\n    var fixed = this.fixed;\n    var style = {\n      height: fixed ? this.height + \"px\" : null\n    };\n    return h(\"div\", {\n      \"style\": style\n    }, [h(\"div\", {\n      \"class\": bem({\n        fixed: fixed\n      }),\n      \"style\": this.style\n    }, [this.slots()])]);\n  }\n});", "import { createNamespace, isDef, addUnit } from '../utils';\nimport { scrollLeftTo } from './utils';\nimport { route } from '../utils/router';\nimport { isHidden } from '../utils/dom/style';\nimport { ParentMixin } from '../mixins/relation';\nimport { BindEventMixin } from '../mixins/bind-event';\nimport { BORDER_TOP_BOTTOM } from '../utils/constant';\nimport { setRootScrollTop, getElementTop } from '../utils/dom/scroll';\nimport Title from './Title';\nimport Content from './Content';\nimport Sticky from '../sticky';\n\nvar _createNamespace = createNamespace('tabs'),\n    createComponent = _createNamespace[0],\n    bem = _createNamespace[1];\n\nexport default createComponent({\n  mixins: [ParentMixin('vanTabs'), BindEventMixin(function (bind) {\n    bind(window, 'resize', this.resize, true);\n  })],\n  model: {\n    prop: 'active'\n  },\n  props: {\n    color: String,\n    sticky: Boolean,\n    animated: Boolean,\n    swipeable: Boolean,\n    background: String,\n    lineWidth: [Number, String],\n    lineHeight: [Number, String],\n    titleActiveColor: String,\n    titleInactiveColor: String,\n    type: {\n      type: String,\n      default: 'line'\n    },\n    active: {\n      type: [Number, String],\n      default: 0\n    },\n    border: {\n      type: Boolean,\n      default: true\n    },\n    ellipsis: {\n      type: Boolean,\n      default: true\n    },\n    duration: {\n      type: Number,\n      default: 0.3\n    },\n    offsetTop: {\n      type: Number,\n      default: 0\n    },\n    lazyRender: {\n      type: Boolean,\n      default: true\n    },\n    swipeThreshold: {\n      type: Number,\n      default: 4\n    }\n  },\n  data: function data() {\n    return {\n      position: '',\n      currentIndex: null,\n      lineStyle: {\n        backgroundColor: this.color\n      }\n    };\n  },\n  computed: {\n    // whether the nav is scrollable\n    scrollable: function scrollable() {\n      return this.children.length > this.swipeThreshold || !this.ellipsis;\n    },\n    navStyle: function navStyle() {\n      return {\n        borderColor: this.color,\n        background: this.background\n      };\n    },\n    currentName: function currentName() {\n      var activeTab = this.children[this.currentIndex];\n\n      if (activeTab) {\n        return activeTab.computedName;\n      }\n    }\n  },\n  watch: {\n    color: 'setLine',\n    active: function active(name) {\n      if (name !== this.currentName) {\n        this.setCurrentIndexByName(name);\n      }\n    },\n    children: function children() {\n      var _this = this;\n\n      this.setCurrentIndexByName(this.currentName || this.active);\n      this.setLine();\n      this.$nextTick(function () {\n        _this.scrollIntoView(true);\n      });\n    },\n    currentIndex: function currentIndex() {\n      this.scrollIntoView();\n      this.setLine(); // scroll to correct position\n\n      if (this.stickyFixed) {\n        setRootScrollTop(Math.ceil(getElementTop(this.$el) - this.offsetTop));\n      }\n    }\n  },\n  mounted: function mounted() {\n    this.onShow();\n  },\n  activated: function activated() {\n    this.onShow();\n    this.setLine();\n  },\n  methods: {\n    // @exposed-api\n    resize: function resize() {\n      this.setLine();\n    },\n    onShow: function onShow() {\n      var _this2 = this;\n\n      this.$nextTick(function () {\n        _this2.inited = true;\n\n        _this2.scrollIntoView(true);\n      });\n    },\n    // update nav bar style\n    setLine: function setLine() {\n      var _this3 = this;\n\n      var shouldAnimate = this.inited;\n      this.$nextTick(function () {\n        var titles = _this3.$refs.titles;\n\n        if (!titles || !titles[_this3.currentIndex] || _this3.type !== 'line' || isHidden(_this3.$el)) {\n          return;\n        }\n\n        var title = titles[_this3.currentIndex].$el;\n        var lineWidth = _this3.lineWidth,\n            lineHeight = _this3.lineHeight;\n        var width = isDef(lineWidth) ? lineWidth : title.offsetWidth / 2;\n        var left = title.offsetLeft + title.offsetWidth / 2;\n        var lineStyle = {\n          width: addUnit(width),\n          backgroundColor: _this3.color,\n          transform: \"translateX(\" + left + \"px) translateX(-50%)\"\n        };\n\n        if (shouldAnimate) {\n          lineStyle.transitionDuration = _this3.duration + \"s\";\n        }\n\n        if (isDef(lineHeight)) {\n          var height = addUnit(lineHeight);\n          lineStyle.height = height;\n          lineStyle.borderRadius = height;\n        }\n\n        _this3.lineStyle = lineStyle;\n      });\n    },\n    // correct the index of active tab\n    setCurrentIndexByName: function setCurrentIndexByName(name) {\n      var matched = this.children.filter(function (tab) {\n        return tab.computedName === name;\n      });\n      var defaultIndex = (this.children[0] || {}).index || 0;\n      this.setCurrentIndex(matched.length ? matched[0].index : defaultIndex);\n    },\n    setCurrentIndex: function setCurrentIndex(currentIndex) {\n      currentIndex = this.findAvailableTab(currentIndex);\n\n      if (isDef(currentIndex) && currentIndex !== this.currentIndex) {\n        var shouldEmitChange = this.currentIndex !== null;\n        this.currentIndex = currentIndex;\n        this.$emit('input', this.currentName);\n\n        if (shouldEmitChange) {\n          this.$emit('change', this.currentName, this.children[currentIndex].title);\n        }\n      }\n    },\n    findAvailableTab: function findAvailableTab(index) {\n      var diff = index < this.currentIndex ? -1 : 1;\n\n      while (index >= 0 && index < this.children.length) {\n        if (!this.children[index].disabled) {\n          return index;\n        }\n\n        index += diff;\n      }\n    },\n    // emit event when clicked\n    onClick: function onClick(index) {\n      var _this$children$index = this.children[index],\n          title = _this$children$index.title,\n          disabled = _this$children$index.disabled,\n          computedName = _this$children$index.computedName;\n\n      if (disabled) {\n        this.$emit('disabled', computedName, title);\n      } else {\n        this.setCurrentIndex(index);\n        this.$emit('click', computedName, title);\n      }\n    },\n    // scroll active tab into view\n    scrollIntoView: function scrollIntoView(immediate) {\n      var titles = this.$refs.titles;\n\n      if (!this.scrollable || !titles || !titles[this.currentIndex]) {\n        return;\n      }\n\n      var nav = this.$refs.nav;\n      var title = titles[this.currentIndex].$el;\n      var to = title.offsetLeft - (nav.offsetWidth - title.offsetWidth) / 2;\n      scrollLeftTo(nav, to, immediate ? 0 : this.duration);\n    },\n    onScroll: function onScroll(params) {\n      this.stickyFixed = params.isFixed;\n      this.$emit('scroll', params);\n    }\n  },\n  render: function render() {\n    var _this4 = this,\n        _ref;\n\n    var h = arguments[0];\n    var type = this.type,\n        ellipsis = this.ellipsis,\n        animated = this.animated,\n        scrollable = this.scrollable;\n    var Nav = this.children.map(function (item, index) {\n      return h(Title, {\n        \"ref\": \"titles\",\n        \"refInFor\": true,\n        \"attrs\": {\n          \"type\": type,\n          \"title\": item.title,\n          \"color\": _this4.color,\n          \"isActive\": index === _this4.currentIndex,\n          \"ellipsis\": ellipsis,\n          \"disabled\": item.disabled,\n          \"scrollable\": scrollable,\n          \"activeColor\": _this4.titleActiveColor,\n          \"inactiveColor\": _this4.titleInactiveColor,\n          \"swipeThreshold\": _this4.swipeThreshold\n        },\n        \"style\": item.titleStyle,\n        \"scopedSlots\": {\n          default: function _default() {\n            return item.slots('title');\n          }\n        },\n        \"on\": {\n          \"click\": function click() {\n            _this4.onClick(index);\n\n            route(item.$router, item);\n          }\n        }\n      });\n    });\n    var Wrap = h(\"div\", {\n      \"ref\": \"wrap\",\n      \"class\": [bem('wrap', {\n        scrollable: scrollable\n      }), (_ref = {}, _ref[BORDER_TOP_BOTTOM] = type === 'line' && this.border, _ref)]\n    }, [h(\"div\", {\n      \"ref\": \"nav\",\n      \"attrs\": {\n        \"role\": \"tablist\"\n      },\n      \"class\": bem('nav', [type]),\n      \"style\": this.navStyle\n    }, [this.slots('nav-left'), Nav, type === 'line' && h(\"div\", {\n      \"class\": bem('line'),\n      \"style\": this.lineStyle\n    }), this.slots('nav-right')])]);\n    return h(\"div\", {\n      \"class\": bem([type])\n    }, [this.sticky ? h(Sticky, {\n      \"attrs\": {\n        \"container\": this.$el,\n        \"offsetTop\": this.offsetTop\n      },\n      \"on\": {\n        \"scroll\": this.onScroll\n      }\n    }, [Wrap]) : Wrap, h(Content, {\n      \"attrs\": {\n        \"count\": this.children.length,\n        \"animated\": animated,\n        \"duration\": this.duration,\n        \"swipeable\": this.swipeable,\n        \"currentIndex\": this.currentIndex\n      },\n      \"on\": {\n        \"change\": this.setCurrentIndex\n      }\n    }, [this.slots()])]);\n  }\n});", "import Vue from 'vue';\n\nfunction flattenVNodes(vnodes) {\n  var result = [];\n\n  function traverse(vnodes) {\n    vnodes.forEach(function (vnode) {\n      result.push(vnode);\n\n      if (vnode.children) {\n        traverse(vnode.children);\n      }\n    });\n  }\n\n  traverse(vnodes);\n  return result;\n}\n\nexport function ChildrenMixin(_parent, options) {\n  var _inject, _computed;\n\n  if (options === void 0) {\n    options = {};\n  }\n\n  var indexKey = options.indexKey || 'index';\n  return Vue.extend({\n    inject: (_inject = {}, _inject[_parent] = {\n      default: null\n    }, _inject),\n    computed: (_computed = {\n      parent: function parent() {\n        if (this.disableBindRelation) {\n          return null;\n        }\n\n        return this[_parent];\n      }\n    }, _computed[indexKey] = function () {\n      this.bindRelation();\n      return this.parent.children.indexOf(this);\n    }, _computed),\n    mounted: function mounted() {\n      this.bindRelation();\n    },\n    beforeDestroy: function beforeDestroy() {\n      var _this = this;\n\n      if (this.parent) {\n        this.parent.children = this.parent.children.filter(function (item) {\n          return item !== _this;\n        });\n      }\n    },\n    methods: {\n      bindRelation: function bindRelation() {\n        if (!this.parent || this.parent.children.indexOf(this) !== -1) {\n          return;\n        }\n\n        var children = [].concat(this.parent.children, [this]);\n        var vnodes = flattenVNodes(this.parent.slots());\n        children.sort(function (a, b) {\n          return vnodes.indexOf(a.$vnode) - vnodes.indexOf(b.$vnode);\n        });\n        this.parent.children = children;\n      }\n    }\n  });\n}\nexport function ParentMixin(parent) {\n  return {\n    provide: function provide() {\n      var _ref;\n\n      return _ref = {}, _ref[parent] = this, _ref;\n    },\n    data: function data() {\n      return {\n        children: []\n      };\n    }\n  };\n}", "import Vue from 'vue';\nexport { createNamespace } from './create';\nexport { addUnit } from './format/unit';\nexport var isServer = Vue.prototype.$isServer;\nexport function noop() {}\nexport function isDef(value) {\n  return value !== undefined && value !== null;\n}\nexport function isObj(x) {\n  var type = typeof x;\n  return x !== null && (type === 'object' || type === 'function');\n}\nexport function get(object, path) {\n  var keys = path.split('.');\n  var result = object;\n  keys.forEach(function (key) {\n    result = isDef(result[key]) ? result[key] : '';\n  });\n  return result;\n}", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "/**\n * bem helper\n * b() // 'button'\n * b('text') // 'button__text'\n * b({ disabled }) // 'button button--disabled'\n * b('text', { disabled }) // 'button__text button__text--disabled'\n * b(['disabled', 'primary']) // 'button button--disabled button--primary'\n */\nvar ELEMENT = '__';\nvar MODS = '--';\n\nfunction join(name, el, symbol) {\n  return el ? name + symbol + el : name;\n}\n\nfunction prefix(name, mods) {\n  if (typeof mods === 'string') {\n    return join(name, mods, MODS);\n  }\n\n  if (Array.isArray(mods)) {\n    return mods.map(function (item) {\n      return prefix(name, item);\n    });\n  }\n\n  var ret = {};\n\n  if (mods) {\n    Object.keys(mods).forEach(function (key) {\n      ret[name + MODS + key] = mods[key];\n    });\n  }\n\n  return ret;\n}\n\nexport function createBEM(name) {\n  return function (el, mods) {\n    if (el && typeof el !== 'string') {\n      mods = el;\n      el = '';\n    }\n\n    el = join(name, el, ELEMENT);\n    return mods ? [el, prefix(el, mods)] : el;\n  };\n}", "var camelizeRE = /-(\\w)/g;\nexport function camelize(str) {\n  return str.replace(camelizeRE, function (_, c) {\n    return c.toUpperCase();\n  });\n}\nexport function padZero(num, targetLength) {\n  if (targetLength === void 0) {\n    targetLength = 2;\n  }\n\n  var str = num + '';\n\n  while (str.length < targetLength) {\n    str = '0' + str;\n  }\n\n  return str;\n}", "/**\n * Use scopedSlots in Vue 2.6+\n * downgrade to slots in lower version\n */\nimport Vue from 'vue';\nexport var SlotsMixin = Vue.extend({\n  methods: {\n    slots: function slots(name, props) {\n      if (name === void 0) {\n        name = 'default';\n      }\n\n      var $slots = this.$slots,\n          $scopedSlots = this.$scopedSlots;\n      var scopedSlot = $scopedSlots[name];\n\n      if (scopedSlot) {\n        return scopedSlot(props);\n      }\n\n      return $slots[name];\n    }\n  }\n});", "/**\n * Create a basic component with common options\n */\nimport '../../locale';\nimport { camelize } from '../format/string';\nimport { SlotsMixin } from '../../mixins/slots';\nimport Vue from 'vue';\n\nfunction install(Vue) {\n  var name = this.name;\n  Vue.component(name, this);\n  Vue.component(camelize(\"-\" + name), this);\n} // unify slots & scopedSlots\n\n\nexport function unifySlots(context) {\n  // use data.scopedSlots in lower Vue version\n  var scopedSlots = context.scopedSlots || context.data.scopedSlots || {};\n  var slots = context.slots();\n  Object.keys(slots).forEach(function (key) {\n    if (!scopedSlots[key]) {\n      scopedSlots[key] = function () {\n        return slots[key];\n      };\n    }\n  });\n  return scopedSlots;\n} // should be removed after Vue 3\n\nfunction transformFunctionComponent(pure) {\n  return {\n    functional: true,\n    props: pure.props,\n    model: pure.model,\n    render: function render(h, context) {\n      return pure(h, context.props, unifySlots(context), context);\n    }\n  };\n}\n\nexport function createComponent(name) {\n  return function (sfc) {\n    if (typeof sfc === 'function') {\n      sfc = transformFunctionComponent(sfc);\n    }\n\n    if (!sfc.functional) {\n      sfc.mixins = sfc.mixins || [];\n      sfc.mixins.push(SlotsMixin);\n    }\n\n    sfc.name = name;\n    sfc.install = install;\n    return sfc;\n  };\n}", "import { isDef, isObj } from '.';\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction assignKey(to, from, key) {\n  var val = from[key];\n\n  if (!isDef(val)) {\n    return;\n  }\n\n  if (!hasOwnProperty.call(to, key) || !isObj(val) || typeof val === 'function') {\n    to[key] = val;\n  } else {\n    // eslint-disable-next-line no-use-before-define\n    to[key] = deepAssign(Object(to[key]), from[key]);\n  }\n}\n\nexport function deepAssign(to, from) {\n  Object.keys(from).forEach(function (key) {\n    assignKey(to, from, key);\n  });\n  return to;\n}", "export default {\n  name: '姓名',\n  tel: '电话',\n  save: '保存',\n  confirm: '确认',\n  cancel: '取消',\n  delete: '删除',\n  complete: '完成',\n  loading: '加载中...',\n  telEmpty: '请填写电话',\n  nameEmpty: '请填写姓名',\n  confirmDelete: '确定要删除么',\n  telInvalid: '请填写正确的电话',\n  vanContactCard: {\n    addText: '添加联系人'\n  },\n  vanContactList: {\n    addText: '新建联系人'\n  },\n  vanPagination: {\n    prev: '上一页',\n    next: '下一页'\n  },\n  vanPullRefresh: {\n    pulling: '下拉即可刷新...',\n    loosing: '释放即可刷新...'\n  },\n  vanSubmitBar: {\n    label: '合计：'\n  },\n  vanCoupon: {\n    valid: '有效期',\n    unlimited: '无使用门槛',\n    discount: function discount(_discount) {\n      return _discount + \"\\u6298\";\n    },\n    condition: function condition(_condition) {\n      return \"\\u6EE1\" + _condition + \"\\u5143\\u53EF\\u7528\";\n    }\n  },\n  vanCouponCell: {\n    title: '优惠券',\n    tips: '使用优惠',\n    count: function count(_count) {\n      return _count + \"\\u5F20\\u53EF\\u7528\";\n    }\n  },\n  vanCouponList: {\n    empty: '暂无优惠券',\n    exchange: '兑换',\n    close: '不使用优惠',\n    enable: '可使用优惠券',\n    disabled: '不可使用优惠券',\n    placeholder: '请输入优惠码'\n  },\n  vanAddressEdit: {\n    area: '地区',\n    postal: '邮政编码',\n    areaEmpty: '请选择地区',\n    addressEmpty: '请填写详细地址',\n    postalEmpty: '邮政编码格式不正确',\n    defaultAddress: '设为默认收货地址',\n    telPlaceholder: '收货人手机号',\n    namePlaceholder: '收货人姓名',\n    areaPlaceholder: '选择省 / 市 / 区'\n  },\n  vanAddressEditDetail: {\n    label: '详细地址',\n    placeholder: '街道门牌、楼层房间号等信息'\n  },\n  vanAddressList: {\n    add: '新增地址'\n  }\n};", "import Vue from 'vue';\nimport { deepAssign } from '../utils/deep-assign';\nimport defaultMessages from './lang/zh-CN';\nvar proto = Vue.prototype;\nvar defineReactive = Vue.util.defineReactive;\ndefineReactive(proto, '$vantLang', 'zh-CN');\ndefineReactive(proto, '$vantMessages', {\n  'zh-CN': defaultMessages\n});\nexport default {\n  messages: function messages() {\n    return proto.$vantMessages[proto.$vantLang];\n  },\n  use: function use(lang, messages) {\n    var _this$add;\n\n    proto.$vantLang = lang;\n    this.add((_this$add = {}, _this$add[lang] = messages, _this$add));\n  },\n  add: function add(messages) {\n    if (messages === void 0) {\n      messages = {};\n    }\n\n    deepAssign(proto.$vantMessages, messages);\n  }\n};", "import { get } from '..';\nimport { camelize } from '../format/string';\nimport locale from '../../locale';\nexport function createI18N(name) {\n  var prefix = camelize(name) + '.';\n  return function (path) {\n    var message = get(locale.messages(), prefix + path) || get(locale.messages(), path);\n\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return typeof message === 'function' ? message.apply(void 0, args) : message;\n  };\n}", "import { createBEM } from './bem';\nimport { createComponent } from './component';\nimport { createI18N } from './i18n';\nexport function createNamespace(name) {\n  name = 'van-' + name;\n  return [createComponent(name), createBEM(name), createI18N(name)];\n}", "export function isNumber(value) {\n  return /^\\d+(\\.\\d+)?$/.test(value);\n}\nexport function isNaN(value) {\n  if (Number.isNaN) {\n    return Number.isNaN(value);\n  } // eslint-disable-next-line no-self-compare\n\n\n  return value !== value;\n}", "import { isDef } from '..';\nimport { isNumber } from '../validate/number';\nexport function addUnit(value) {\n  if (!isDef(value)) {\n    return undefined;\n  }\n\n  value = String(value);\n  return isNumber(value) ? value + \"px\" : value;\n}"], "sourceRoot": ""}