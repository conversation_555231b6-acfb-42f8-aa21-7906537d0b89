select o.organization_name, p.partner_id, p.point_code, pp.point_name,p.available_quantity,p.material_id,
m.material_code, m.material_name,o.status 
from wx_t_promotion_gift_pool p
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
left join wx_t_material m on m.id=p.material_id
where p.application_type='cdm_promotion' and available_quantity>0
order by o.organization_name

select o.organization_name, p.partner_id, p.point_code, pp.point_name,p.available_quantity,p.material_id,
m.material_code, m.material_name,o.status 
from wx_t_promotion_gift_pool p
left join wx_t_promotion_point pp on pp.point_code=p.point_code
LEFT JOIN dbo.wx_t_organization o ON p.partner_id = o.id
left join wx_t_material m on m.id=p.material_id
where p.application_type='promotion' and available_quantity>0
order by o.organization_name