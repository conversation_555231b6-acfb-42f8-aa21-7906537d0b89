select dh.dsr_id,t6.*, a.total_liters, a.hero_liters,sales_volume_actual_mtd, hero_product_sales_volume_actual_mtd
from dbo.wx_t_v2_dsr_sc_grant_history dh 
left join wx_t_user u1 on u1.user_id=dh.dsr_id
left join wx_t_organization o1 on o1.id=dh.partner_id
left join temp_dsr_liters6 t6 on t6.dsr=u1.ch_name and t6.partner_name=o1.organization_name--dh.dsr_id=(select user_id from wx_t_user u left join wx_t_organization o on u.org_id=o.id where u.ch_name=t6.dsr and o.organization_name=t6.partner_name and u.status=1)
left join (select o.creator, SUM(tt_order_line.amount*CONVERT(decimal(9,2), product.capacity)) total_liters, sum(case when product.ext_flag&2>0 then tt_order_line.amount*CONVERT(decimal(9,2), product.capacity) else 0 end) hero_liters from wx_t_order o
left join wx_t_order_line tt_order_line on o.id=tt_order_line.order_id
LEFT JOIN wx_t_product product ON tt_order_line.sku = product.sku
where o.status=11 and partner_confirm_time<'2021-07-01' and partner_confirm_time>='2021-06-01'
group by o.creator) a on a.creator=dh.dsr_id
where grant_month=6
--and (t6.hero_liters>dh.hero_product_sales_volume_actual_mtd or t6.total_liters>dh.sales_volume_actual_mtd)
and t6.dsr in ('曹其何',
'王挺',
'谢明智',
'郭隆',
'张力军',
'蔡炎彬',
'黄金玉',
'赖欣才',
'林伟松',
'苏鸿斌')


SELECT case when pac.POINT_ACCOUNT_TYPE='TC' then ((select w.partner_id from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id where we.code=pac.POINT_ACCOUNT_OWNER_CODE))
	when pac.POINT_ACCOUNT_TYPE='SPBD' then (select u2.org_id from wx_t_user u2 where u2.user_id=pac.POINT_ACCOUNT_OWNER_ID) else pac.POINT_ACCOUNT_OWNER_ID end as partner_id,pp.point_type as point_type,
        cast(plog.MODIFIED_VALUE as decimal(12,2)) as point, MONTH(plog.CREATION_TIME)  as create_time, plog.*
        FROM wx_t_point_value_detail pde
        LEFT JOIN wx_t_point_value_detail_log plog ON plog.POINT_VALUE_ID = pde.ID
        LEFT JOIN wx_t_point_business bu ON plog.BUSINESS_ID = bu.ID
        LEFT JOIN wx_t_point_account pac ON pac.id = pde.POINT_ACCOUNT_ID
        left join wx_t_promotion_point pp on pp.point_code = pde.point_type
        LEFT JOIN wx_t_user u on u.user_id = pde.CREATED_BY
        WHERE plog.CREATION_TIME>='2021-07-01' and pp.point_code='DSR_CIO_POINT'
		order by bu.id
		
select o.* 
--update o set o.partner_confirm_type=1
from wx_t_order o
left join wx_t_order_line tt_order_line on o.id=tt_order_line.order_id
LEFT JOIN wx_t_product product ON tt_order_line.sku = product.sku
where o.status=11 --and partner_confirm_time<'2021-07-01' 
and partner_confirm_time>='2021-07-01'
and o.partner_confirm_type is null /*and o.creator in (124411,
123746,
124613,
124914,
124562,
123738,
131742,
131975,
132544,
132545)*/		
