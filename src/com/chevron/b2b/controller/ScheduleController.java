package com.chevron.b2b.controller;

import com.chevron.b2b.business.ScheduleBizService;
import com.chevron.b2b.model.ScheduleParams;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 计划Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
@Controller
@RequestMapping(value="/schedule")
public class ScheduleController {
	@Autowired
	private ScheduleBizService scheduleBizService;
	
	private final static Logger log = Logger.getLogger(ScheduleController.class);

	/**
	 * 计划列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public Map<String,Object> queryForPage(ScheduleParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);

		try {
			scheduleBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			request.getSession().setAttribute("schedule_params", params);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("queryForPage fail. " + e.getMessage(), e);
		}
		return resultMap;
	}
}
