<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.WorkshopDistributionRuleMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.WorkshopDistributionRule" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
    <result column="per_liter_reward" property="perLiterReward" jdbcType="NUMERIC" />
    <result column="owner_reward" property="ownerReward" jdbcType="NUMERIC" />
    <result column="mechanic_reward" property="mechanicReward" jdbcType="NUMERIC" />
    <result column="award_realtime" property="awardRealtime" jdbcType="VARCHAR" />
    <result column="enable_flag" property="enableFlag" jdbcType="VARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
    <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="BIGINT" />
    
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="sp_verification_rule_id" property="spVerificationRuleId" jdbcType="BIGINT" />
    <result column="sp_verification_amount" property="spVerificationAmount" jdbcType="NUMERIC" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, sku, workshop_id, per_liter_reward, owner_reward, mechanic_reward, award_realtime, 
    enable_flag, creation_time, created_by, last_update_time, last_updated_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.WorkshopDistributionRuleExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_ws_distribution_rule
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_ws_distribution_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_ws_distribution_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.WorkshopDistributionRule" >
    insert into wx_t_ws_distribution_rule (id, partner_id, workshop_id, 
      per_liter_reward, owner_reward, mechanic_reward, 
      award_realtime, enable_flag, creation_time, 
      created_by, last_update_time, last_updated_by
      )
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{workshopId,jdbcType=BIGINT}, 
      #{perLiterReward,jdbcType=NUMERIC}, #{ownerReward,jdbcType=NUMERIC}, #{mechanicReward,jdbcType=NUMERIC}, 
      #{awardRealtime,jdbcType=VARCHAR}, #{enableFlag,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.WorkshopDistributionRule" >
    insert into wx_t_ws_distribution_rule
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="workshopId != null" >
        workshop_id,
      </if>
      <if test="perLiterReward != null" >
        per_liter_reward,
      </if>
      <if test="ownerReward != null" >
        owner_reward,
      </if>
      <if test="mechanicReward != null" >
        mechanic_reward,
      </if>
      <if test="awardRealtime != null" >
        award_realtime,
      </if>
      <if test="enableFlag != null" >
        enable_flag,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="lastUpdateTime != null" >
        last_update_time,
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="perLiterReward != null" >
        #{perLiterReward,jdbcType=NUMERIC},
      </if>
      <if test="ownerReward != null" >
        #{ownerReward,jdbcType=NUMERIC},
      </if>
      <if test="mechanicReward != null" >
        #{mechanicReward,jdbcType=NUMERIC},
      </if>
      <if test="awardRealtime != null" >
        #{awardRealtime,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null" >
        #{enableFlag,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.WorkshopDistributionRule" >
    update wx_t_ws_distribution_rule
    <set >
      <if test="partnerId != null" >
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="workshopId != null" >
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="perLiterReward != null" >
        per_liter_reward = #{perLiterReward,jdbcType=NUMERIC},
      </if>
      <if test="ownerReward != null" >
        owner_reward = #{ownerReward,jdbcType=NUMERIC},
      </if>
      <if test="mechanicReward != null" >
        mechanic_reward = #{mechanicReward,jdbcType=NUMERIC},
      </if>
      <if test="awardRealtime != null" >
        award_realtime = #{awardRealtime,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null" >
        enable_flag = #{enableFlag,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_ws_distribution_rule
    <set >
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=NVARCHAR},
      </if>
      <if test="record.workshopId != null" >
        workshop_id = #{record.workshopId,jdbcType=BIGINT},
      </if>
      <if test="record.perLiterReward != null" >
        per_liter_reward = #{record.perLiterReward,jdbcType=NUMERIC},
      </if>
      <if test="record.ownerReward != null" >
        owner_reward = #{record.ownerReward,jdbcType=NUMERIC},
      </if>
      <if test="record.mechanicReward != null" >
        mechanic_reward = #{record.mechanicReward,jdbcType=NUMERIC},
      </if>
      <if test="record.awardRealtime != null" >
        award_realtime = #{record.awardRealtime,jdbcType=VARCHAR},
      </if>
      <if test="record.enableFlag != null" >
        enable_flag = #{record.enableFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.creationTime != null" >
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.WorkshopDistributionRule" >
    update wx_t_ws_distribution_rule
    set partner_id = #{partnerId,jdbcType=BIGINT},
      workshop_id = #{workshopId,jdbcType=BIGINT},
      per_liter_reward = #{perLiterReward,jdbcType=NUMERIC},
      owner_reward = #{ownerReward,jdbcType=NUMERIC},
      mechanic_reward = #{mechanicReward,jdbcType=NUMERIC},
      award_realtime = #{awardRealtime,jdbcType=VARCHAR},
      enable_flag = #{enableFlag,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getMaxPerLiterRewardByPartnerId" parameterType="long" resultType="double">
  	select max(t.per_liter_reward) from wx_t_ws_distribution_rule t where t.partner_id = #{partnerId} and enable_flag = 'Y'
  </select>
  <select id="selectWorkshopActiveRule" resultMap="BaseResultMap" parameterType="map" >
  select top 1 dr00.*, vr.per_liter_reward sp_verification_amount, vr.id sp_verification_rule_id from(
select dr1.id, dr1.award_realtime, dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.per_liter_reward, dr1.workshop_id 
from wx_t_ws_distribution_rule dr1 where dr1.workshop_id=#{workshopId,jdbcType=BIGINT} and dr1.enable_flag='Y'
union all
select dr1.id, (select top 1 dr0.award_realtime from wx_t_ws_distribution_rule dr0 where dr0.workshop_id=#{workshopId,jdbcType=BIGINT} order by dr0.creation_time desc) award_realtime, 
dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.per_liter_reward, -1
from wx_t_ws_distribution_rule dr1 where dr1.workshop_id=-1 and dr1.enable_flag='Y' and dr1.sku is null 
and exists (select 1 from wx_t_workshop_partner wp0 where wp0.workshop_id=#{workshopId,jdbcType=BIGINT} and wp0.partner_id=dr1.partner_id and wp0.relation_type='trade')) dr00
left join wx_t_verification_rule vr on vr.partner_id=dr00.partner_id and vr.enable_flag='Y'
  </select>
  <select id="selectSkuDistributionRules" resultMap="BaseResultMap" parameterType="map" >
select dr.award_realtime, dr.id, dr.mechanic_reward, dr.owner_reward, dr.partner_id, dr.workshop_id, dr.sku, p.name product_name
from wx_t_ws_distribution_rule dr join wx_t_product p on p.sku=dr.sku where dr.enable_flag='Y'
<choose>
	<when test="workshopId != null and workshopId != -1">
		and dr.workshop_id = #{workshopId,jdbcType=BIGINT}
	</when>
	<otherwise>
		and dr.partner_id=#{partnerId,jdbcType=BIGINT} and dr.workshop_id = -1
	</otherwise>
</choose>
  </select>
</mapper>