package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.common.util.ResponseMap;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.pms.dao.RegionVoMapper;
import com.chevron.pms.model.RegionVo;
import com.chevron.pms.model.RegionVoExample;
import com.chevron.pms.model.RegionVoExample.Criteria;
import com.chevron.pms.model.RegionVoTree;
import com.chevron.pms.service.RegionService;
import com.chevron.userselect.service.impl.OrgTreeService;
import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ThrowableUtil;
import com.common.util.json.JsonGenerator;
import com.sys.auth.model.WxTUser;

@Service
public class RegionServiceImpl implements RegionService {
	private static Log log = LogFactory.getLog(RegionServiceImpl.class);
	@Resource
	RegionVoMapper regionVoMapper;

	@Resource
	OrgTreeService orgTreeService;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.chevron.pms.service.regionService#save(com.chevron.pms.model.RegionVo
	 * )
	 */
	@Override
	public Map<String, String> insert(RegionVo regionVo) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		regionVo.setCreateTime(new Date());
		regionVo.setUpdateTime(new Date());
		regionVoMapper.insert(regionVo);
		map.put("resultData", "success");
		return map;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.chevron.pms.service.regionService#delete(com.chevron.pms.model.RegionVo
	 * )
	 */
	@Override
	public Map<String, String> delete(RegionVo regionVo) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		regionVoMapper.deleteByPrimaryKey(regionVo.getId());
		map.put("resultData", "success");
		return map;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.chevron.pms.service.regionService#update(com.chevron.pms.model.RegionVo
	 * )
	 */
	@Override
	public Map<String, String> update(RegionVo regionVo) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		regionVo.setUpdateTime(new Date());
		regionVoMapper.updateByPrimaryKey(regionVo);
		map.put("resultData", "success");
		return map;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.chevron.pms.service.regionService#getregions()
	 */
	@Override
	public Map<String, Object> getRegionById(String id) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		RegionVo regionVo = regionVoMapper.selectByPrimaryKey(Long.valueOf(id));
		map.put("result", "success");
		map.put("data", regionVo);
		return map;
	}

	@Override
	public Map<String, Object> getRegionByCode(String regionCode)
			throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		RegionVoExample example = new RegionVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andRegionCodeEqualTo(regionCode);
		List<RegionVo> regionList = regionVoMapper.selectByExample(example);
		map.put("resultData", regionList);
		return map;
	}

	// /* (non-Javadoc)
	// * @see com.chevron.pms.service.regionService#getregions()
	// */
	// @Override
	// public Map<String, ArrayList<RegionVo>> getRegions() throws Exception {
	// Map<String,ArrayList<RegionVo>> map = new
	// HashMap<String,ArrayList<RegionVo>>();
	// regionMapper.getRegions();
	// return map;
	// }

//	@Override
//	public Map<String, Object> findAllProvinces() {
//
//		Map<String, Object> map = new HashMap<String, Object>(2);
//		try {
//			WxTUser currUser = ContextUtil.getCurUser();
//			long isChevron = currUser.getmUserTypes();
//			if (1 == isChevron) {
//				map.put("data", regionVoMapper.selChildrenByParentId(1l));
//			} else {
//				long orgId = currUser.getOrgId();
//				List<Long> regionIdList = regionPartnerServiceImpl
//						.getRegionIdByPartnerId(orgId);
//				List<RegionVo> provinceList = new ArrayList<RegionVo>();
//				if (null != regionIdList && regionIdList.size() > 0) {
//					List<RegionVo> regionList = getRegionByRegionIdIncludeCityAndProvince(regionIdList);
//					List<Long> provinceIdList = new ArrayList<Long>();
//					if (null != regionList && regionList.size() > 0) {
//						for (int i = 0; i < regionList.size(); i++) {
//							RegionVo wto = regionList.get(i);
//							long provinceId = wto.getProvinceId();
//							provinceIdList.add(provinceId);
//						}
//						provinceList
//								.addAll(getRegionByRegionId(provinceIdList));
//					}
//				}
//				map.put("data", provinceList);
//			}
//			map.put("success", true);
//		} catch (Exception e) {
//			e.printStackTrace();
//			map.put("success", false);
//		}
//		return map;
//	}

	@Override
	public Map<String, Object> findCities(Long provId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", regionVoMapper.selChildrenByParentId(provId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> findDistricts(Long cityId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", regionVoMapper.selChildrenByParentId(cityId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}
		

	@Override
	public Map<String, Object> findDistrictsByProvinceId(Long provinceId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", regionVoMapper.selectDistByProvinceId(provinceId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> findFullRegionByDist(Long distId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", regionVoMapper.selFullRegionByDist(distId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> findRegionInfo(Long id) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (-1 == id) {
			id = 1L;// 如果传入-1 说明是查询省份，则将id设置为1。
		}
		map.put("regionList", regionVoMapper.selChildrenByParentId(id));
		map.put("code", "success");
		return map;
	}

	@Override
	public Map<String, Object> findFilteredRegionInfo(Long id) {
		return findFilteredRegionInfoWithChannel(id, null);
	}

	@Override
	public Map<String, Object> findFilteredRegionInfoWithBrand(Long id, Integer brand){
		return findFilteredRegionInfoWithChannel(id, brand == null ? null : Constants.getChannelWeightByBrand(brand));
	}
	@Override
	public Map<String, Object> findFilteredRegionInfoWithChannel(Long id, Integer channelWeight){
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			if (id == null || id.equals(-1l)) {
				id = 1L;// 如果传入-1 说明是查询省份，则将id设置为1。
			}
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("parentId", id);
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_SP.equals(user.getUserModel())){
				params.put("partnerId", user.getOrgId());
				params.put("loginUserId", user.getUserId());
			}
			if(channelWeight != null) {
				params.put("channelWeight", channelWeight);
			}
			params.put("orderBy", "create_time");
			map.put("regionList", regionVoMapper.selRegionByParams(params));
			map.put("code", "success");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			map.put("code", "error");
			map.put("errorMsg", e.getMessage());
		}
		return map;
	}
	@Override
	public Map<String, Object> findSpRegionInfo(Long id, Long partnerId) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			if (-1 == id) {
				id = 1L;// 如果传入-1 说明是查询省份，则将id设置为1。
			}
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("parentId", id);
			params.put("partnerId", partnerId);
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_SP.equals(user.getUserModel())){
				params.put("loginUserId", user.getUserId());
			}
			params.put("orderBy", "create_time");
			map.put("regionList", regionVoMapper.selRegionByParams(params));
			map.put("data", map.get("regionList"));
			map.put("success", true);
			map.put("code", "success");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			map.put("code", "error");
			map.put("success", false);
			map.put("errorMsg", e.getMessage());
		}
		return map;
	}
	@Override
	public Map<String, Object> findSpDistrictsByProvinceId(Long provinceId, Long partnerId) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("provinceId", provinceId);
			params.put("partnerId", partnerId);
			params.put("orderBy", "create_time");
			map.put("regionList", regionVoMapper.selDistByParams(params));
			map.put("code", "success");
			map.put("success", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			map.put("code", "error");
			map.put("success", false);
			map.put("errorMsg", e.getMessage());
		}
		return map;
	}
	@Override
	public RegionVo getRegionParentIdByRegionId(Long regionId) {
		return regionVoMapper.getRegionParentIdByRegionId(regionId);
	}

	@Override
	public List<RegionVo> getProvCityDistByRegionId(Long distId) {
		List<RegionVo> list = new ArrayList<RegionVo>();
		list = regionVoMapper.getProvCityDistByRegionCode(distId.toString());
		
		/*RegionVo distVo = regionVoMapper.selectByPrimaryKey(distId);
		Long cityId = distVo.getParentId();
		RegionVo cityVo = regionVoMapper.selectByPrimaryKey(cityId);
		Long provId = cityVo.getParentId();
		RegionVo provVo = regionVoMapper.selectByPrimaryKey(provId);

		list.add(distVo);
		list.add(cityVo);
		list.add(provVo);*/
		return list;
	};

	@Override
	public List<RegionVo> getRegionByRegionIdIncludeCityAndProvince(
			List<Long> regionIdList) {
		List<RegionVo> regionList = new ArrayList<RegionVo>();
		RegionVoExample example = new RegionVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andIdIn(regionIdList);
		example.setOrderByClause("region_code");
		/*regionList.addAll(regionVoMapper.selectByExample(example));
		for (int i = 0; i < regionList.size(); i++) {
			RegionVo region = regionList.get(i);
			long pid = region.getParentId();
			RegionVo city = regionVoMapper.selectByPrimaryKey(pid);
			region.setCityId(city.getId());
			region.setCityName(city.getRegionName());
			RegionVo province = regionVoMapper.selectByPrimaryKey(city
					.getParentId());
			region.setProvinceId(province.getId());
			region.setProvinceName(province.getRegionName());
		}*/
		regionList.addAll(regionVoMapper.selectDistsDetailByDistIds(regionIdList));
		return regionList;
	}

	@Override
	public List<RegionVo> getRegionByRegionId(List<Long> regionIdList) {
		List<RegionVo> regionList = new ArrayList<RegionVo>();
		/*RegionVoExample example = new RegionVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andIdIn(regionIdList);*/
		regionList.addAll(regionVoMapper.selectDistsDetailByDistIds(regionIdList));
		return regionList;
	}

	@Override
	public Object selChildrenByParentId(Long parentId) {
		List<RegionVo> regionList = new ArrayList<RegionVo>();
		RegionVoExample example = new RegionVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andParentIdEqualTo(parentId);
		regionList.addAll(regionVoMapper.selectByExample(example));
		return regionList;
	}

	/**
	 * 初始化页面加载全部菜单 add by --ken.zhang
	 */
	@Override
	public Map<String, Object> getRegionList() {
		log.info("--------------------getRegionList start:"
				+ System.currentTimeMillis());
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		resultMap.put("code", "success");
		try {

			// 新添加代码
			reqMap.put("orgCode", RegionVo.ROOT_REGION_CODE);
			// RegionVo region = regionVoMapper.selectByRegionCode(reqMap); //
			// 读取根节点
			List<RegionVoTree> regionNodes = regionVoMapper
					.selectByForTreeNodes(reqMap);
			List<RegionVoTree> children = JsonGenerator.listToTree(regionNodes);
			resultMap.put("resultData", children);

		} catch (Exception ex) {
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		log.info("--------------------getRegionList end:"
				+ System.currentTimeMillis());
		return resultMap;
	}

	/**
	 * 新增按钮，添加数据 add by --ken.zhang
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertRegion(RegionVoTree regionVo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		if (regionVo == null) {
			// 如果Region为空，则直接返回
			resultMap.put("code", "isNullInsertObj");
			return resultMap;
		}
		// 如果pid为空，则添加为0，为根目录
		if (regionVo.getPid() == null) {
			regionVo.setPid(0L);
		}
		resultMap.put("code", "success");
		try {
			regionVoMapper.insertRegionSelective(regionVo);// 已经自动向menu中添加了id
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("code", "syserror");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> deleteData(String id) {
		Long idLong = Long.parseLong(id);

		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 先通过id检查是否有子目录，有子目录则不准删除（没有子目录的根目录也可以删除）
		if (regionVoMapper.selectCountAllChildRegion(idLong) != 0) {
			resultMap.put("success", false);
			return resultMap;
		}

		// 删除该条数据
		try {
			regionVoMapper.deleteByPrimaryKey(idLong);
			resultMap.put("success", true);
		} catch (Exception e) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("success", false);
			e.printStackTrace();
		}
		return resultMap;
	}
  // 更新数据
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> updateSelectRegion(RegionVoTree regionVo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		resultMap.put("code", "success");

		try {
			if (regionVo != null) {
				regionVoMapper.updateRegion(regionVo);
			} else {
				resultMap.put("code", "isNullUpdateObj");
			}
		} catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			resultMap.put("code", "syserror");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getRegionListTree() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		//WxTUser wxUser = ContextUtil.getCurUser();
		resultMap.put("code", "success");
		try {  //检查是否存在根节点，如果不存在，则进行插入操作.
			reqMap.put("orgCode", RegionVo.ROOT_REGION_CODE);
			RegionVo region = regionVoMapper.selectByRegionCode(reqMap);
			if (null == region) {
				region = new RegionVo();
				region.setRegionCode(RegionVo.ROOT_REGION_CODE);
				region.setRegionName(RegionVo.ROOT_REGION_NAME);
				region.setParentId(0L);
				region.setRegionType(RegionVo.REGION_TYPE_CITY);
				region.setSort(0);
				//region.setCreator(wxUser.getUserId());
				regionVoMapper.insertSelective(region);
			}
			reqMap.put("parentId", region.getId());
			List<RegionVoTree> regionNodes = regionVoMapper.selectByRegionByParentId(reqMap);
			List<RegionVoTree> resultNodes = new ArrayList<RegionVoTree>();
			for(RegionVoTree regionTree : regionNodes)
			{
				regionTree.setText(regionTree.getRegionName());
				regionTree.setLeaf(false);
				regionTree.setExpanded(false);
				resultNodes.add(regionTree);
			}
			List<RegionVoTree> list = new ArrayList<RegionVoTree>();
			RegionVoTree root = new RegionVoTree();
			root.setId(region.getId());
			root.setText(region.getRegionName());
			root.setRegionCode(region.getRegionCode());
			root.setRegionName(region.getRegionName());
			root.setSort(region.getSort());
			root.setRegionType(region.getRegionType());
			root.setStatus(region.getStatus());
			root.setChildren(resultNodes);
			root.setLevel(1);
			list.add(root);
			resultMap.put("resultJsonList", list);
		} catch(Exception ex) {
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public List<RegionVo> selectDistCityProvinceByParterId(Long partnerId) {
		List<RegionVo> list = new ArrayList<RegionVo>();
		list = regionVoMapper.selectDistCityProvinceByParterId(partnerId);
		return list;
	}
	
	
	@Override
	public Map<String,Object> getContainsWorkshopsRegionNamesByRegionName(
			String regionName) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		resultMap = getContainsWorkshopsRegionNamesByRegionName(regionName,null);
		return resultMap;
	}
	
	
	
	@Override
	public Map<String, Object> getContainsWorkshopsRegionNamesByRegionName(
			String regionName, Long partnerId) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
		//0.校验参数
		if(null==regionName || regionName.isEmpty())
		{
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "区域名称不能为空");
			return resultMap;
		}
		
		//1.模糊查询区域名称，获取区域列表第1个区域id
		List<Long> tmpRegionLst = regionVoMapper.getRegionIdsByRegionName(regionName);
		if(null==tmpRegionLst || tmpRegionLst.isEmpty())
		{
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "没有找到匹配的区域信息");
			return resultMap;
		}
		
		//2.根据第一个区域id,获取所有包含自己的子区域id 列表
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("partnerId", partnerId);
		reqMap.put("regionId", tmpRegionLst.get(0));
		reqMap.put("existsWorkshop", true);
		List<String> lstAllRegionNames = regionVoMapper.getRegionIdsByParentRegionName(reqMap);
		
//		//3.根据区域列表，到门店列表中去查询，存在区域列表中的门店，对应的区域名称，，，如果超过2000分页查询
//		ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
//				tmpRegionLst2, 2000);
//		int toltalPage = newPage.getTotalPages();
//		//定义返回的区域lst
//		List<String> lstAllRegionNames = new ArrayList<String>();
//		for (int i = 1; i <= toltalPage; i++) {
//			List<Long> regionCodes = newPage.getObjects(i);
//			List<String> regionNames = workShopMapper.getContainsWorkshopsRegionNamesByRegionCodes(regionCodes);
//			lstAllRegionNames.addAll(regionNames);
//			/*if(lstRegionNames.size()>=10)
//			{
//				break;
//			}*/
//		}
		resultMap.put("code", "success");
		List<String> lstRegionNames = new ArrayList<String>();
		if(null!=lstAllRegionNames && !lstAllRegionNames.isEmpty())
		{
			if(lstAllRegionNames.size()>10)
			{
				for(int i=0;i<10;i++)
				{
					lstRegionNames.add(lstAllRegionNames.get(i));
				}
			}else
			{
				lstRegionNames.addAll(lstAllRegionNames);
			}
				
		}
		resultMap.put("lstRegionNames", lstRegionNames);
			return resultMap;
		}catch (Exception e) {
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "exception:"+ThrowableUtil.getStackTrace(e));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getAllAreas() {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put(Constants.RESULT_LST_KEY, regionVoMapper.getAllAreas());
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
		}
		return map;
	}

    @Override
    public ResponseMap getRegionByDistributorId(Long distributorId) {
        List<Map<String, String>> regionByDistributorId = regionVoMapper.getRegionByDistributorId(distributorId);
        if(regionByDistributorId.size() == 0){
            return new ResponseMap();
        }
        ResponseMap responseMap = new ResponseMap();
        HashSet<String> dists = new HashSet<String>();
        HashSet<String> citys = new HashSet<String>();
        HashSet<String> provinces = new HashSet<String>();
        for (Map<String, String> map : regionByDistributorId) {
            dists.add(map.get("dist"));
            citys.add(map.get("city"));
            provinces.add(map.get("province"));
        }
        responseMap.put("dist",dists);
        responseMap.put("city",citys);
        responseMap.put("province",provinces);
        return responseMap;
    }
}
