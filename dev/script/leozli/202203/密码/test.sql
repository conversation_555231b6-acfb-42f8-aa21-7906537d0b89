select l.* from wx_t_pwd_change_log l 
left join wx_t_user u on u.user_id=l.user_id where u.login_name='leoz<PERSON>'

select * from wx_t_user where login_name='leozli' and status=1
--update wx_t_user set password='f92274ff7c2f4914da79760b2e9ad872ceff0d20', salt='27B157064129F4E5' where login_name='leozli' and status=1
update wx_t_user set password='f92274ff7c2f4914da79760b2e9ad872ceff0d20', salt='27B157064129F4E5',allow_login='T' where org_id=9--login_name='leozli' and status=1


select l.*, u.ext_flag
--delete l 
--update l set create_time='2022-08-26 23:00'
from wx_t_pwd_change_log l 
left join wx_t_user u on u.user_id=l.user_id where u.login_name='leozli'
--and l.id<25

select l.*, u.ext_flag
--update u set u.ext_flag=0
--delete l 
--update l set create_time='2021-11-30 23:00'
--update l set create_time='2022-09-10 23:00'
from wx_t_pwd_change_log l 
left join wx_t_user u on u.user_id=l.user_id where u.login_name='cs-dl'
--and l.id<25

select datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate()),
isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), (select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1))
 from wx_t_user u where 1=1 and u.reset_flag=0 and u.status=1  and u.login_name='cs-400'
 
 select isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), (select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1)) 
- datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate()),
isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), (select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1)),
datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate()),
(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='expireNotifyDay' and di1.status=1)
--update u SET xg_time = '2022-11-25 16:35:56', xg_user = '1', ext_flag = ext_flag|'4' 
from wx_t_user u where 1=1 and u.reset_flag=0 and u.status=1 --and u.ext_flag&6=0 
and u.login_name='cs-dl'
 

http://127.0.0.1:8080/utils/sendemailbyuser.do?emailTemplate=user/pwd_transitional_notify_email.ftl&subject=合伙人平台密码策略升级已完成 - 密码修改提醒&deadDay=2022-12-31&effectiveDay=365&userIds=65921,65927,122710
131974,132167,132314,132347,132455,132718,132769,133014,133030,131797,131798,131800,131801,131802,131804,131805,131881,131882,131888,132348,127836,131792,131793,131997,131998,132000,132627,132734,132972,122710,123527,123528,127453,129888,129889,129890,129892,129893,129895,129903,129905,129907,129908,129909,129913,129914,129916,129918,129919,129921,130907,130908,131094,131149,131419,131821,132052,132163,132182,132203,132261,132353,132454,132670,132705,132791,132936,132996,129894,129896,129898,129900,129901,129902,129904,129906,129911,129915,129917,130690,130842,130843,130844,130845,130846,130847,130848,130849,130925,131210,131214,131217,131225,131226,131740,131803,132090,129922,130971,131012,131228,131273,132222,132259,132349,132370,125443,125444,126713,126940,126941,129613,131221,131409,131410,131412,131909,131910,132456,132457,132458,132710,132711,132712,132713,132892,132893,132299,123258,123290,122277,131784,131809,131886,76480,76567,122928,130831,121050,121058,121060,121061,121066,121160,122410,129210,130686,131442,131518,131519,131791,123561,123917,124632,132114,132546,123566,123946,123488,123778,123788,123790,124536,124537,131840,131869,132354,123555,132742,123800,123801,123802,123803,123490,123835,123836,123837,123840,123567,123610,123615,126805,131415,132024,123568,123625,123681,123720,131083,131167,131917,123569,123607,123611,123805,123810,123811,123812,123813,123828,131287,131544,131959,132665,123769,123770,123771,123617,123758,123759,123906,123951,123988,124532,124611,123573,131414,132365,132479,123623,131605,123562,123746,124916,123580,123780,123784,123785,132827,132828,124228,124247,124249,132078
132499,123549,124404,124405,124412,124562,124613,125132,126608,130617,123552,127108,127760,129757,132526,123551,123743,127485,131841,124101,124103,131960,132041,129421,124510,131259,131260,125223,125224,125225,125228,127738,124514,124515,126125,132281,124895,124896,132291,124976,130662,125020,125097,125102,125329,125758,125760,131924,125350,131363,131367,131904,131905,132076,132880,125351,131667,131937,132308,125353,125354,125355,125358,131553,131555,125359,125360,131672,125361,125366,131600,131658,132130,132478,125367,131657,132062,125368,125372,125375,125377,132339,125378,125379,131573,125382,125383,127542,125386,125387,125388,125389,131213,131216,131218,131876,131890,131928,132200,125392,126176,125395,125397,125398,125400,131571,131572,125403,125404,131579,131581,132157,125407,125408,125409,132581,125410,125411,125412,125414,131558,125415,132070,127288,131661,131663,125419,127286,125423,125425,125426,125427,125429,125430,125431,131468,131469,131617,131690,131954,131979,133008,125433,131498,125434,125437,125439,125440,131608,131609,125441,127292,132786,132922,126457,126461,126462,126463,126464,131593,126540,131742,131812,132671,126574,127226,127227,127228,126560,126748,126750,126751,126752,126822,131664,132939,127290,126755,126756,126757,126818,127722,127723,128574,131785,132231,132278,127055,132565,127057,127095,127549,131733,131734,131735,131736,127845,127952,128850,132512,128910,128913,128918,132220,133012,128919,130789,130805,131087,131655,131677,132055,132056,132656,131096,131236,131237,131459,131461,131479,131695,131873,131901,131679,131682,131589,131632,131631,131635,132113,131636,132334,131673,131680
131968,131969,131970,131971,132335,131681,132209,132462,131693,131698,131829,131831,131992,131777,131787,131854,131856,131857,131859,131860,131861,131862,131911,131972,132141,132243,132634,132636,132638,131835,131836,131878,131923,132074,131925,132018,131934,131963,131982,131983,132144,132111,132515,132789,132112,132123,132142,132616,132177,132709,132272,132554,132240,132468,132506,132508,132239,132560,132273,132283,132303,132304,132306,132309,132310,132343,132285,132307,132355,132356,132403,132323,132361,132385,132615,132684,132378,132379,132408,132380,132520,132647,132655,132392,132388,132464,132398,132504,132528,132397,132399,132409,132407,132406,132405,132689,132461,132481,132556,132555,132686,132557,132602,132603,132608,132624,132625,132644,132692,132623,132629,132637,132667,132676,132732,132673,132681,132688,132700,132704,132851,132719,132708,132707,132715,132962,132963,132724,132725,132727,132735,132737,132741,132754,132755,132766,132767,132768,132772,132784,132787,132835,132785,132793,132825,132826,132852,132849,132873,132861,132901,133026,132904,132907,132918,132920,132930,132948,132949,132950,132964,132965,132951,132971,132974,132977,132982,132981,132991,132997,133002,133025,133027,132899,132599