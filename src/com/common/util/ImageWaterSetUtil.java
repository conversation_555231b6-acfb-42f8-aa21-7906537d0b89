package com.common.util;
import java.awt.*;  
import java.awt.image.*;  
import java.io.*;  

import javax.imageio.ImageIO;
import javax.swing.*;  
import com.sun.image.codec.jpeg.*; 
import com.sun.org.apache.xpath.internal.FoundIndex;
public class ImageWaterSetUtil {
	public static int linesize =10;
	/** *//** 
     * 给图片添加水印 
     *  
     * @param filePath 
     *            需要添加水印的图片的路径 
     * @param markContent 
     *            水印的文字 
     * @param markContentColor 
     *            水印文字的颜色 
     * @param qualNum 
     *            图片质量 
     * @return 
     */  
    public static boolean createMark(String filePath, String markContent,  
            Color markContentColor, float qualNum) {  
        ImageIcon imgIcon = new ImageIcon(filePath);  
        Image theImg = imgIcon.getImage();  
        int width = theImg.getWidth(null);  
        int height = theImg.getHeight(null);  
        BufferedImage bimage = new BufferedImage(width, height,  
                BufferedImage.TYPE_INT_RGB);  
        Graphics2D g = bimage.createGraphics();  
        g.setColor(markContentColor);  
        g.setBackground(Color.white);  
        g.drawImage(theImg, 0, 0, null);  
        g.drawString(markContent, width / 2, height / 2); // 添加水印的文字和设置水印文字出现的内容  
        g.dispose();  
        try {  
            FileOutputStream out = new FileOutputStream(filePath);  
            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);  
            JPEGEncodeParam param = encoder.getDefaultJPEGEncodeParam(bimage);  
            param.setQuality(qualNum, true);  
            encoder.encode(bimage, param);  
            out.close();  
        } catch (Exception e) {  
            return false;  
        }  
        return true;  
    }  
    
    
    /** *//** 
     * 把图片印刷到图片上 
     *  
     * @param pressImg -- 
     *            水印文件 
     * @param targetImg -- 
     *            目标文件 
     * @param x 
     *            --x坐标 
     * @param y 
     *            --y坐标 
     */  
    public final static void pressImage(String pressImg, String targetImg,  
            int x, int y) {  
        try {  
            //目标文件  
            File _file = new File(targetImg);  
            Image src = ImageIO.read(_file);  
            int wideth = src.getWidth(null);  
            int height = src.getHeight(null);  
            BufferedImage image = new BufferedImage(wideth, height,  
                    BufferedImage.TYPE_INT_RGB);  
            Graphics g = image.createGraphics();  
            g.drawImage(src, 0, 0, wideth, height, null);  
            //水印文件  
            File _filebiao = new File(pressImg);  
            Image src_biao = ImageIO.read(_filebiao);  
            int wideth_biao = src_biao.getWidth(null);  
            int height_biao = src_biao.getHeight(null);  
            g.drawImage(src_biao, (wideth - wideth_biao) / 2,  
                    (height - height_biao) / 2, wideth_biao, height_biao, null);  
            //水印文件结束  
            g.dispose();  
            FileOutputStream out = new FileOutputStream(targetImg);  
            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);  
            encoder.encode(image);  
            out.close();  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
    }  
    /** *//** 
     * 打印文字水印图片 
     *  
     * @param pressText 
     *            --文字 
     * @param targetImg -- 
     *            目标图片 
     * @param fontName -- 
     *            字体名 
     * @param fontStyle -- 
     *            字体样式 
     * @param color -- 
     *            字体颜色 
     * @param fontSize -- 
     *            字体大小 
     * @param x -- 
     *            偏移量 
     * @param y 
     */  
    public static void pressText(String pressText, String targetImg,  
            String fontName, int fontStyle, int color, int fontSize, int x,  
            int y) {  
        try {  
            File _file = new File(targetImg);  
            Image src = ImageIO.read(_file);  
            int wideth = src.getWidth(null);  
            int height = src.getHeight(null);  
            BufferedImage image = new BufferedImage(wideth, height,  
                    BufferedImage.TYPE_INT_RGB);  
            Graphics g = image.createGraphics();  
            g.drawImage(src, 0, 0, wideth, height, null);  
            // String s="www.qhd.com.cn";  
            g.setColor(Color.WHITE);  
            g.setFont(new Font(fontName, fontStyle, fontSize));  
            g.drawString(pressText, wideth - fontSize - wideth/2, height - fontSize  
                    / 2 - height/2);  
            g.dispose();  
            FileOutputStream out = new FileOutputStream(targetImg);  
            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);  
            encoder.encode(image);  
            out.close();  
        } catch (Exception e) {  
            System.out.println(e);  
        }  
    }  
    
    
    
    
    /** 
     * 图片添加水印 
     * @param srcImgPath 需要添加水印的图片的路径 
     * @param outImgPath 添加水印后图片输出路径 
     * @param markContentColor 水印文字的颜色 
     * @param waterMarkContent 水印的文字 
     */  
    public static void makeWaterImage(String srcImgPath, String outImgPath, Color markContentColor, String waterMarkContent) {  
        try {  
            // 读取原图片信息  
            File srcImgFile = new File(srcImgPath);  
            Image srcImg = ImageIO.read(srcImgFile);  
            int srcImgWidth = srcImg.getWidth(null);  
            int srcImgHeight = srcImg.getHeight(null);  
            // 加水印  
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);  
            Graphics2D g = bufImg.createGraphics();  
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);  
            //Font font = new Font("Courier New", Font.PLAIN, 12);  
            Font font = new Font("宋体", Font.BOLD, 50);    
            g.setColor(markContentColor); //根据图片的背景设置水印颜色  
              
            g.setFont(font);  
            int x = srcImgWidth - getWatermarkLength(waterMarkContent, g) - 3;  
            int y = srcImgHeight - 50;  
            //int x = (srcImgWidth - getWatermarkLength(watermarkStr, g)) / 2;  
            //int y = srcImgHeight / 2;  
            g.drawString(waterMarkContent, x, y);  
            g.dispose();  
            // 输出图片  
            File outImg= new File(outImgPath);
            System.out.println(outImgPath);
            outImg.createNewFile();
            FileOutputStream outImgStream = new FileOutputStream(outImgPath);  
            ImageIO.write(bufImg, "jpg", outImgStream);  
            outImgStream.flush();  
            outImgStream.close();  
        } catch (IOException e) {  
            e.printStackTrace();  
        }  
    }  
    
    
    public static void makeWaterImageNew(String srcImgPath, String outImgPath, Color markContentColor, String waterMarkContent) {  
        try {  
            // 读取原图片信息  
            File srcImgFile = new File(srcImgPath);  
            Image srcImg = ImageIO.read(srcImgFile);  
            int srcImgWidth = srcImg.getWidth(null);  
            int srcImgHeight = srcImg.getHeight(null);  
            // 加水印  
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);  
            Graphics2D g = bufImg.createGraphics();  
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);  
            int fontlen = waterMarkContent.length();
            int x = (srcImgWidth - fontlen) / 4;  //图片显示的坐标x
            int y = srcImgHeight / 2;  			  //图片显示的坐标y
            
            int fontsize = 50;//字体大小
            int flag =10;//一行显示的单词个数
            if(srcImgWidth>1000 && srcImgWidth<=2000)
            {
            	fontsize = 100;
            	x = x-2*fontsize-20;
            	linesize = 8;
            	flag =8;//一行显示的单词个数
            }else if(srcImgWidth>2000)
            {
            	fontsize = 300;
            	x = x-2*fontsize-20;
            	linesize = 9;
            	flag = 9;
            }else
            {
            	x = x-2*fontsize-20;
            	//if(srcImgWidth<=750)
            	{
            		linesize = 10;
            		flag = 10;
            	}
            }
            Font font = new Font("宋体", Font.BOLD, fontsize);    
            g.setColor(markContentColor); //根据图片的背景设置水印颜色  
            g.setFont(font);  
            //分页
            int line  = 0;
            if ((fontlen % linesize) == 0) { 
            	line = fontlen / linesize; 
            } else { 
            	line  = fontlen/linesize +1;
            } 
            
            String tmpStr = "";
            int ctrli = 0;
            y = y-fontsize*(line/4+1);
            for(int i=0;i<line;i++)
            {
            	if(linesize>=fontlen)
            	{
            		linesize = fontlen;
            	}
            	tmpStr = waterMarkContent.substring(ctrli, linesize);
            	//System.out.println("tmpStr:"+tmpStr);
            	g.drawString(tmpStr, x, y);
            	ctrli = linesize;
            	linesize = ctrli+flag;
            	y+=fontsize;
            }
            g.dispose();  
            // 输出图片  
            File outImg= new File(outImgPath);
            File filePath = new File(outImg.getParent());
			if (!filePath.exists()) {
				filePath.mkdirs();//创建多级目录
			}
            outImg.createNewFile();
            FileOutputStream outImgStream = new FileOutputStream(outImgPath);  
            ImageIO.write(bufImg, "jpg", outImgStream);  
            outImgStream.flush();  
            outImgStream.close();  
        } catch (IOException e) {  
            e.printStackTrace();  
        }  
    }  
      

	public static int getCharLen(char c,
	 Graphics2D g) {
	return g.getFontMetrics(g.getFont()).charWidth(c);
	}
    
    /** 
     * 获取水印文字总长度 
     * @param waterMarkContent 水印的文字 
     * @param g 
     * @return 水印文字总长度 
     */  
    public static int getWatermarkLength(String waterMarkContent, Graphics2D g) {  
        return g.getFontMetrics(g.getFont()).charsWidth(waterMarkContent.toCharArray(), 0, waterMarkContent.length());  
    }  
    
	
      
    public static void main(String[] args) {
    	try {
    		//makeWaterImageNew("C:/data/chevron/uploadfile/img/1491024665753920562.jpg","C:\\data\\chevron\\uploadfile\\img_new\\new1\\new2\\1491024665753920562.jpg",Color.WHITE,"上海涵丹汽车维修服务有限公司y");
    	} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
