
1. 操作配置
	actionType: 操作类型 GET/POST/RPC/POP_MENU
	actionName: 操作名称
	actionCode: 操作编码。需要特殊处理操作编码
	actionTheme: 操作样式。CREATE/ACCEPT/RECALL/VIEW/DELETE/DOWNLOAD
	ajax: 是否ajax操作（不刷新页面）
	successMsg：操作成功后的提示语。为空不提示
	responseType：操作成功后相应类型。NONE-不响应，RELOAD-刷新列表
	config: 操作配置
		(GET: {url:''})
		(POST: {url:'',contentType:"FORM/JSON",params:{}})
		(RPC: {method:'',params:[]})
		(POP_MENU: {menus:[Action]})
		(DOWNLOAD: {url:'',contentType:"FORM/JSON",async:false,method:'POST/GET',params:{}})
		
2. 申请类型 数据字典 
编码：WorkflowInstance.instanceApplyType

3. 获取待办页面操作 RPC
workflowInstanceService.getPcTodoPageActions(Long executor) 
返回：
	resultLst：操作ACTION集合	

4. 待办列表 POST
/workflowinstance/tododata.do?limit=10&start=0&field=executeTime&direction=desc&executor=129915
参数：
	requestUserName: 申请人姓名模糊查询
	instanceApplyType：申请类型。数据字典WorkflowInstance.instanceApplyType
	instanceApplyOwner：经销商名称模糊查询
	applyDateFromStr：申请开始日期yyyy-MM-dd
	applyDateToStr：申请截止日期yyyy-MM-dd
	executor：被代理人用户ID
返回：
	resultLst：WorkflowInstance集合
	resultLst[0].currentStep 当前办理步骤
	resultLst[0].listOperateName 列表中审批操作名称
	resultLst[0].flowRequestNo 申请编号
	resultLst[0].createTime 创建时间
	resultLst[0].applyTime 申请时间
	resultLst[0].requestUserName 申请人
	resultLst[0].instanceApplyOwner 经销商
	resultLst[0].instanceApplyTypeText 申请类型
	resultLst[0].appListActions 列表操作
	resultLst[0].acceptFlag：通过按钮标记。1-有权限，0-无权限
	resultLst[0].latestStepHistory.actionName 状态。resultLst[0].workflowInstance.latestStepHistory为null时显示草稿


5. 获取已办页面操作 RPC
workflowInstanceService.getPcDonePageActions(Long executor) 
返回：
	resultLst：操作ACTION集合	

6. 获取全部列表页面操作 RPC
workflowInstanceService.getPcAllPageActions(Long executor) 
返回：
	resultLst：操作ACTION集合		
	
7. 已办页面中转 重定向到用户有权限的已办页面
/workflowinstance/done/index.do
参数：
	page：流程类型编码。调整到指定流程页面
	executor：
	
8. 全部列表页面中转 重定向到用户有权限的全部列表页面
/workflowinstance/all/index.do
参数：
	page：流程类型编码。调整到指定流程页面
	executor：	