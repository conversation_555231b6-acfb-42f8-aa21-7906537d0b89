select * from (
select sum (modify_liters) l, body_key, product_key from (
select i1.id, i1.body_type, i1.body_key, i1.product_type, i1.product_key, 
il1.original_quantity, il1.modify_quantity*convert(float, p1.capacity) modify_liters, il1.create_time inventory_time
from wx_t_inventory_log2021 il1
left join wx_t_inventory2021 i1 on il1.inventory_id=i1.id
join wx_t_product p1 on p1.sku=i1.product_key
where i1.inventory_type='distributor' 
union all 
select i1.id, i1.body_type, i1.body_key, i1.product_type, i1.product_key, 
il1.recall_original_quantity, -il1.modify_quantity*convert(float, p1.capacity) modify_liters, il1.update_time 
from wx_t_inventory_log2021 il1
left join wx_t_inventory2021 i1 on il1.inventory_id=i1.id
join wx_t_product p1 on p1.sku=i1.product_key
where i1.inventory_type='distributor' --and il1.update_time<getdate() 
and il1.delete_flag=1
) a group by body_key, product_key) b
left join (select sum(current_quantity*convert(float, p1.capacity)) l, body_key, product_key from wx_t_inventory2021 ivn2 join wx_t_product p1 on p1.sku=ivn2.product_key where body_type='store' group by body_key,product_key) c
on b.body_key=c.body_key and b.product_key=c.product_key
where (b.l-c.l>0.0000001 or b.l-c.l<-0.0000001 )
--and b.body_key=97
--3161577
--21844958.66
--21846193.35
--select sum(current_quantity*convert(float, p1.capacity)) from wx_t_inventory2021 ivn2 join wx_t_product p1 on p1.sku=ivn2.product_key where body_type='store'


select sum (modify_liters) l from (
select i1.id, i1.body_type, i1.body_key, i1.product_type, i1.product_key, 
il1.original_quantity, il1.modify_quantity*convert(float, p1.capacity) modify_liters, il1.create_time inventory_time
from wx_t_inventory_log2021 il1
left join wx_t_inventory2021 i1 on il1.inventory_id=i1.id
join wx_t_product p1 on p1.sku=i1.product_key
where i1.inventory_type='distributor' 
union all 
select i1.id, i1.body_type, i1.body_key, i1.product_type, i1.product_key, 
il1.recall_original_quantity, -il1.modify_quantity*convert(float, p1.capacity) modify_liters, il1.update_time 
from wx_t_inventory_log2021 il1
left join wx_t_inventory2021 i1 on il1.inventory_id=i1.id
join wx_t_product p1 on p1.sku=i1.product_key
where i1.inventory_type='distributor' --and il1.update_time<getdate() 
and il1.delete_flag=1
) a