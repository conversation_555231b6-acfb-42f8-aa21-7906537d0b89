(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-49b8e18d"],{"386d":function(e,t,r){"use strict";var a=r("cb7c"),s=r("83a1"),o=r("5f1b");r("214f")("search",1,function(e,t,r,n){return[function(r){var a=e(this),s=void 0==r?void 0:r[t];return void 0!==s?s.call(r,a):new RegExp(r)[t](String(a))},function(e){var t=n(r,e,this);if(t.done)return t.value;var i=a(e),l=String(this),c=i.lastIndex;s(c,0)||(i.lastIndex=0);var p=o(i,l);return s(i.lastIndex,c)||(i.lastIndex=c),null===p?-1:p.index}]})},"4a65":function(e,t,r){"use strict";var a=r("a463"),s=r.n(a);s.a},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"8eda":function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{position:"relative",padding:"20px"}},[r("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.activeNameInner,callback:function(t){e.activeNameInner=t},expression:"activeNameInner"}},[e.showVisitplan?r("el-tab-pane",{attrs:{label:"拜访计划",name:"plan"}},[e._t("plan")],2):e._e(),e.showReport?r("el-tab-pane",{attrs:{label:"绩效报告",name:"report"}},[e._t("report")],2):e._e(),e.showVisitrecord?r("el-tab-pane",{attrs:{label:"拜访记录",name:"record"}},[e._t("record")],2):e._e()],1)],1)},s=[],o=(r("8e6e"),r("ac6a"),r("456d"),r("7f7f"),r("bd86")),n=r("2f62");function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(r,!0).forEach(function(t){Object(o["a"])(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(r).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var c={props:["activeName"],computed:l({activeNameInner:{get:function(){return this.activeName},set:function(e){this.$emit("update:activeName",e)}}},Object(n["c"])({showReport:function(e){return e.list.showReport},showVisitplan:function(e){return e.list.showVisitplan},showVisitrecord:function(e){return e.list.showVisitrecord}})),methods:{handleClick:function(e){console.log(e.name),"plan"===e.name?this.$router.push("/visitplan/list"):"report"===e.name?this.$router.push("/report"):"record"===e.name&&this.$router.push("/visitrecord/list")}}},p=c,u=r("2877"),h=Object(u["a"])(p,a,s,!1,null,null,null);t["a"]=h.exports},"8f69":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("tabs",{attrs:{activeName:"report"}},[r("div",{attrs:{slot:"report"},slot:"report"},[r("el-form",[r("el-row",[r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"按年份查看报告:"}},[r("el-select",{attrs:{placeholder:""},on:{change:function(t){return e.searchChange("year")}},model:{value:e.search.year,callback:function(t){e.$set(e.search,"year",t)},expression:"search.year"}},e._l(e.years,function(e){return r("el-option",{key:e.label,attrs:{label:e.name,value:e.label}})}),1)],1)],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"按季度查看报告:"}},[r("el-select",{attrs:{placeholder:""},on:{change:function(t){return e.searchChange("quarter")}},model:{value:e.search.quarter,callback:function(t){e.$set(e.search,"quarter",t)},expression:"search.quarter"}},e._l(e.quarters,function(e){return r("el-option",{key:e.label,attrs:{label:e.name,value:e.label}})}),1)],1)],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"按月份查看报告:"}},[r("el-select",{attrs:{placeholder:""},on:{change:function(t){return e.searchChange("month")}},model:{value:e.search.month,callback:function(t){e.$set(e.search,"month",t)},expression:"search.month"}},e._l(e.months,function(e){return r("el-option",{key:e.label,attrs:{label:e.name,value:e.label}})}),1)],1)],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{label:"区域:"}},[r("el-select",{attrs:{placeholder:"",multiple:"",clearable:""},on:{change:function(t){return e.searchChange("region")}},model:{value:e.search.region,callback:function(t){e.$set(e.search,"region",t)},expression:"search.region"}},e._l(e.regionList,function(e){return r("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1)],1)],1),r("el-row",[r("div",{staticStyle:{"margin-bottom":"10px"}},[r("el-button",{staticStyle:{"margin-top":"2.5px"},attrs:{type:"primary",size:"mini"},on:{click:e.getPerformanceListByCondition}},[e._v("查询")]),r("el-button",{staticStyle:{"margin-top":"2.5px"},attrs:{type:"primary",size:"mini"},on:{click:e.downloadReport}},[e._v("下载报告")])],1)])],1),r("el-row",[r("el-col",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{span:24}},[r("el-table",{ref:"table",staticStyle:{width:"100%","font-size":"12px"},attrs:{data:e.sourceList,"empty-text":"没有数据",height:e.windowHeight}},e._l(e.tableTitle,function(t,a){return r("el-table-column",{key:a,attrs:{label:t.label,width:t.width,fixed:t.fixed,prop:t.prop,sortable:t.sortBy,"render-header":e.renderheader,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[-1!==e.filterNums.indexOf(t.prop)?r("span",[e._v(e._s(e.toThousands(a.row[t.prop],t.prop,a.row.salesNameCn)))]):"evalMonthVos"===t.prop?r("span",[a.row.evalMonthVos&&a.row.evalMonthVos.length>0?e._l(a.row.evalMonthVos,function(t,a){return r("span",{key:a,staticStyle:{"margin-right":"8px"}},[1===t.isReport?r("el-link",{attrs:{type:"primary"},on:{click:function(r){return e.toReport(t.link)}}},[e._v(e._s(t.label))]):e._e()],1)}):e._e()],2):"asmCommentVos"===t.prop?r("el-popover",{attrs:{placement:"top-start",width:"450",trigger:"hover"}},[r("div",{staticStyle:{"max-height":"200px","overflow-y":"scroll"}},[a.row.asmCommentVos&&a.row.asmCommentVos.length>0?r("div",e._l(a.row.asmCommentVos,function(t,a){return r("div",{key:a,staticStyle:{"padding-right":"10px"}},[t.asmComment?[r("el-row",[r("el-col",{attrs:{span:16}},[r("span",{staticClass:"bold"},[e._v("拜访客户：")]),e._v(e._s(t.customerName))]),r("el-col",{attrs:{span:8}},[r("span",{staticClass:"bold"},[e._v("离开时间：")]),e._v(e._s(e.dayjs(t.leaveTime).format("YYYY-MM-DD")))])],1),r("el-row",[r("el-col",[r("span",{staticClass:"bold"},[e._v("评价内容：")]),e._v(e._s(t.asmComment))])],1),r("div",{staticStyle:{"border-top":"1px solid #F1F1F1",margin:"10px 0"}})]:e._e()],2)}),0):e._e()]),r("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(a.row.asmCommentVosStr))])]):"salesVisitPlanVos"===t.prop?r("el-popover",{attrs:{placement:"top-start",width:"450",trigger:"hover"}},[r("div",{staticStyle:{"max-height":"200px","overflow-y":"scroll"}},[a.row.salesVisitPlanVos&&a.row.salesVisitPlanVos.length>0?r("div",e._l(a.row.salesVisitPlanVos,function(t,a){return r("div",{key:a,staticStyle:{"padding-right":"10px"}},[t.visitReport?[r("el-row",[r("el-col",{attrs:{span:16}},[r("span",{staticClass:"bold"},[e._v("拜访客户：")]),e._v(e._s(t.customerName))]),r("el-col",{attrs:{span:8}},[r("span",{staticClass:"bold"},[e._v("离开时间：")]),e._v(e._s(e.dayjs(t.leaveTime).format("YYYY-MM-DD")))])],1),r("el-row",[r("el-col",[r("span",{staticClass:"bold"},[e._v("拜访报告：")]),e._v(e._s(t.visitReport))])],1),r("div",{staticStyle:{"border-top":"1px solid #F1F1F1",margin:"10px 0"}})]:e._e()],2)}),0):e._e()]),r("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(a.row.salesVisitPlanVosStr))])]):"visitIsFinishFlag"===t.prop?r("span",[e._v(e._s(a.row[t.prop]||1===a.row[t.prop]?"是":"否"))]):"completeQualityScore"===t.prop?r("span",{staticClass:"score"},[r("el-input",{attrs:{type:"number",disabled:0===a.row.editableFlag},on:{blur:function(t){return e.saveScore(a.$index)}},model:{value:a.row[t.prop],callback:function(r){e.$set(a.row,t.prop,r)},expression:"scope.row[item.prop]"}})],1):["supervisorGrade","regionMultipleRank","channelAchieveRank"].indexOf(t.prop)>-1?r("span",[e._v(e._s(a.row[t.prop]?a.row[t.prop]:"-"))]):r("span",[e._v(e._s(a.row[t.prop]))])]}}],null,!0)})}),1)],1)],1)],1)])},s=[],o=(r("8e6e"),r("456d"),r("a481"),r("6b54"),r("ac6a"),r("768b")),n=(r("96cf"),r("3b8d")),i=(r("386d"),r("28a5"),r("bd86")),l=r("8eda"),c=r("3fbc"),p={months:[{name:"1月",label:1,active:!1},{name:"2月",label:2,active:!1},{name:"3月",label:3,active:!1},{name:"4月",label:4,active:!1},{name:"5月",label:5,active:!1},{name:"6月",label:6,active:!1},{name:"7月",label:7,active:!1},{name:"8月",label:8,active:!1},{name:"9月",label:9,active:!1},{name:"10月",label:10,active:!1},{name:"11月",label:11,active:!1},{name:"12月",label:12,active:!1}],quarters:[{name:"Q1",label:1,active:!1},{name:"Q2",label:2,active:!1},{name:"Q3",label:3,active:!1},{name:"Q4",label:4,active:!1}],monthsTableTitle:[{label:"销售代表",prop:"salesNameCn",width:"80px",fixed:!0,sortBy:!1},{label:"YTD销量实际/(KL)",prop:"ytdActualVolume",width:"100px",fixed:!0,sortBy:!0},{label:"YTD销量目标/(KL)",prop:"ytdVolumeTarget",width:"100px",fixed:!0,sortBy:!0},{label:"YTD差距/(KL)",prop:"ytdVolumeGap",width:"80px",fixed:!0,sortBy:!0},{label:"YTD毛利实际/(KUSD)",prop:"ytdActualMarginUsd",width:"100px",fixed:!0,sortBy:!0},{label:"YTD毛利目标/(KUSD)",prop:"ytdMarginTargetUsd",width:"110px",fixed:!0,sortBy:!0},{label:"YTD差距/(KUSD)",prop:"ytdMarginGap",width:"110px",fixed:!0,sortBy:!0},{label:"绩效报告",prop:"evalMonthVos",width:"80px",fixed:!0,sortBy:!1},{label:"YTD区域内/销量排名",prop:"regionVolumeRank",width:"100px",fixed:!1,sortBy:!0},{label:"YTD渠道内/销量排名",prop:"channelVolumeRank",width:"100px",fixed:!1,sortBy:!0},{label:"YTD区域内/毛利排名",prop:"regionMarginRank",width:"100px",fixed:!1,sortBy:!0},{label:"YTD渠道内/毛利排名",prop:"channelMarginRank",width:"100px",fixed:!1,sortBy:!0},{label:"YTD区域内/总分排名",prop:"regionMultipleRank",width:"100px",fixed:!1,sortBy:!0},{label:"YTD渠道内/总分排名",prop:"channelAchieveRank",width:"100px",fixed:!1,sortBy:!0},{label:"大区经理/打分",prop:"supervisorGrade",width:"100px",fixed:!1,sortBy:!1},{label:"当月客户拜访/天数",prop:"visitDayCurrentMonth",width:"100px",fixed:!1,sortBy:!1},{label:"当月工作/天数",prop:"workDayCurrentMonth",width:"110px",fixed:!1,sortBy:!1},{label:"拜访报告/是否完成",prop:"visitIsFinishFlag",width:"100px",fixed:!1,sortBy:!1},{label:"拜访报告/(点击查看全部)",prop:"salesVisitPlanVos",width:"110px",fixed:!1,sortBy:!1},{label:"拜访报告/打分",prop:"completeQualityScore",width:"110px",fixed:!1,sortBy:!1},{label:"大区经理/(点击查看全部)",prop:"asmCommentVos",width:"110px",fixed:!1,sortBy:!1}],quarterTableTitle:[{label:"销售代表",prop:"salesNameCn",width:"80px",fixed:!0,sortBy:!1},{label:"QTD销量实际/(KL)",prop:"ytdActualVolume",width:"100px",fixed:!0,sortBy:!0},{label:"QTD销量目标/(KL)",prop:"ytdVolumeTarget",width:"100px",fixed:!0,sortBy:!0},{label:"QTD差距/(KL)",prop:"ytdVolumeGap",width:"80px",fixed:!0,sortBy:!0},{label:"QTD毛利实际/(KUSD)",prop:"ytdActualMarginUsd",width:"100px",fixed:!0,sortBy:!0},{label:"QTD毛利目标/(KUSD)",prop:"ytdMarginTargetUsd",width:"110px",fixed:!0,sortBy:!0},{label:"QTD差距/(KUSD)",prop:"ytdMarginGap",width:"110px",fixed:!0,sortBy:!0},{label:"绩效报告",prop:"evalMonthVos",width:"130px",fixed:!0,sortBy:!1},{label:"QTD区域内/销量排名",prop:"regionVolumeRank",width:"100px",fixed:!1,sortBy:!0},{label:"QTD渠道内/销量排名",prop:"channelVolumeRank",width:"100px",fixed:!1,sortBy:!0},{label:"QTD区域内/毛利排名",prop:"regionMarginRank",width:"100px",fixed:!1,sortBy:!0},{label:"QTD渠道内/毛利排名",prop:"channelMarginRank",width:"100px",fixed:!1,sortBy:!0},{label:"QTD区域内/总分排名",prop:"regionMultipleRank",width:"100px",fixed:!1,sortBy:!0},{label:"QTD渠道内/总分排名",prop:"channelAchieveRank",width:"100px",fixed:!1,sortBy:!0},{label:"大区经理/打分",prop:"supervisorGrade",width:"100px",fixed:!1,sortBy:!1},{label:"当季客户拜访/天数",prop:"visitDayCurrentMonth",width:"100px",fixed:!1,sortBy:!1},{label:"当季工作/天数",prop:"workDayCurrentMonth",width:"110px",fixed:!1,sortBy:!1},{label:"拜访报告/是否完成",prop:"visitIsFinishFlag",width:"100px",fixed:!1,sortBy:!1},{label:"拜访报告/(点击查看全部)",prop:"salesVisitPlanVos",width:"110px",fixed:!1,sortBy:!1},{label:"拜访报告/打分",prop:"completeQualityScore",width:"110px",fixed:!1,sortBy:!1},{label:"大区经理评语/(点击查看全部)",prop:"asmCommentVos",width:"110px",fixed:!1,sortBy:!1}],filterNums:["regionVolumeRank","ytdMarginGap","ytdActualVolume","ytdVolumeTarget","ytdVolumeGap","ytdActualMarginUsd","ytdMarginTargetUsd"]},u=r("0328"),h=r("4d20"),d=r("5c96");function m(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.loading=d["Loading"].service({lock:!0,text:"正在处理下载数据"}),this.options=R.clone(e),this.attributes=t,this.key="",this.filePath="",this.fileName="",this.run(),[!0]}m.prototype={run:function(){var e=Object(n["a"])(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getFileKey();case 2:return e.next=4,this.checkProcess();case 4:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),updateLoading:function(e){this.loading.text=e},closeLoading:function(){this.loading.close()},getFileKey:function(){var e=Object(n["a"])(regeneratorRuntime.mark(function e(){var t,r,a,s;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(h["a"])(this.options);case 2:return t=e.sent,r=Object(o["a"])(t,2),a=r[0],s=r[1],a&&(this.key=s.progressStatus.key,this.updateLoading(s.progressStatus.message)),e.abrupt("return",[a,s]);case 8:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),checkProcess:function(){var e=Object(n["a"])(regeneratorRuntime.mark(function e(){var t,r,a,s,n=this;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(h["a"])({method:"get",path:"utils/getprocessstatus.do",contentType:"json",data:{key:this.key,random:math.random()}});case 2:if(t=e.sent,r=Object(o["a"])(t,2),a=r[0],s=r[1],!a||!s.progressStatus||"success"!==s.progressStatus.status){e.next=13;break}return this.filePath=s.progressStatus.attrs.filePath||this.attributes.filePath,this.fileName=s.progressStatus.attrs.fileName||this.attributes.fileName,this.downloadFile(),e.abrupt("return",[!0]);case 13:if(!a||!s.progressStatus||"error"!==s.progressStatus.status){e.next=19;break}return this.closeLoading(),d["Notification"].error({title:"错误提示",message:s.progressStatus.message}),e.abrupt("return",[!1]);case 19:s.progressStatus&&s.progressStatus.message&&this.updateLoading(s.progressStatus.message);case 20:return setTimeout(function(){n.checkProcess()},5e3),e.abrupt("return",[!1]);case 22:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),downloadFile:function(){Object(u["a"])({path:"/utils/download.do",data:{filePath:this.filePath,fileName:this.fileName,deleteFile:!0},options:{target:"_self"}}),this.closeLoading()}};var f=function(e,t){return new m(e,t)},b=r("5a0c"),g=r.n(b);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(r,!0).forEach(function(t){Object(i["a"])(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(r).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w={components:{tabs:l["a"]},data:function(){var e=new Date(Date.now()).getMonth()+1,t=g()().year();return y({},p,{activeName:"report",loading:!1,windowHeight:window.screen.height-360,search:{year:t,quarter:null,month:e,region:[]},sourceList:[],regionList:[],tableTitle:p.monthsTableTitle,canToReport:!1,years:[{name:"".concat(t,"年"),label:t,active:!0},{name:"".concat(t-1,"年"),label:t-1,active:!0},{name:"".concat(t-2,"年"),label:t-2,active:!0}]})},created:function(){this.getPerformanceListByCondition()},methods:{dayjs:g.a,renderheader:function(e,t){var r=t.column,a=[e("span",{},r.label.split("/")[0]),e("br"),e("span",{},r.label.split("/")[1])];return"salesVisitPlanVos"!==r.property&&"asmCommentVos"!==r.property||(a=[e("span",{},r.label.split("/")[0]),e("br"),e("span",{style:"color: red;font-size:10px"},r.label.split("/")[1])]),e("span",{},a)},toReport:function(e){this.canToReport||top.openMenu(e,e,"绩效评估表")},downloadReport:function(){var e=this.search.year,t=this.search.month||0,r=this.search.quarter||0,a=this.search.region.length>0?this.search.region.join(","):"";return f({method:"post",path:"salesPerEvalForm/excel/export.do",contentType:"json",params:{salesChannel:"Indirect",year:e,month:t,quarter:r,region:a}})},getPerformanceListByCondition:function(){var e=Object(n["a"])(regeneratorRuntime.mark(function e(){var t,r,a,s,n=this;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return this.sourceList=[],this.loading=!0,this.tableTitle=this.search.month?this.monthsTableTitle:this.quarterTableTitle,e.next=5,c["a"].getPerformanceListByCondition([y({},this.search,{region:this.search.region.length>0?this.search.region.join(","):""})]);case 5:t=e.sent,r=Object(o["a"])(t,2),a=r[0],s=r[1],a&&s.result.resultLst.length>0&&(this.sourceList=s.result.resultLst,this.sourceList.forEach(function(e,t){e.reportLinks&&n.$set(n.sourceList,t,[])}),this.sourceList.forEach(function(e,t){e.regionName&&-1===n.regionList.indexOf(e.regionName)&&n.regionList.push(e.regionName),e.evalMonthVos.length>0&&e.evalMonthVos.forEach(function(t,r){var a=1===t.editableFlag?"edit":"view",s=g()(t.month,"YYYYMM").month()+1,o="isEditFlag=".concat(a,"&id=").concat(t.id,"&month=").concat(t.month,"&salesCai=").concat(e.salesCai),i="".concat(window.location.origin,"/business/evaluation/editPerformanceFlsrPerEvaForm.jsp?")+o;n.$set(e.evalMonthVos,r,y({},e.evalMonthVos[r],{link:i,label:s?s+"月":""}))});var r="";e.salesVisitPlanVos&&e.salesVisitPlanVos.length>0&&e.salesVisitPlanVos.forEach(function(e){e.visitReport&&(r+=e.visitReport+"  ")});var a="";e.asmCommentVos&&e.asmCommentVos.length>0&&e.asmCommentVos.forEach(function(e){e.asmComment&&(a+=e.asmComment+"  ")}),n.$set(n.sourceList,t,y({},n.sourceList[t],{salesVisitPlanVosStr:r,asmCommentVosStr:a}))}),this.search.region.length>0&&this.filterByRegion()),this.loading=!1;case 11:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),saveScore:function(){var e=Object(n["a"])(regeneratorRuntime.mark(function e(t){var r,a,s,n,i=this;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:if(this.sourceList[t].completeQualityScore){e.next=2;break}return e.abrupt("return");case 2:if(!(this.sourceList[t].completeQualityScore<0||this.sourceList[t].completeQualityScore>100)){e.next=5;break}return this.$notify.error({message:"拜访报告打分只能输入0~100的数字"}),e.abrupt("return");case 5:return this.canToReport=!0,e.next=8,c["a"].saveScoreByPlan([y({},this.sourceList[t],{status:0,bu:"Indirect",month:this.sourceList[t].evalMonthVos[0].month,id:this.sourceList[t].evalMonthVos[0].id})]);case 8:r=e.sent,a=Object(o["a"])(r,2),s=a[0],n=a[1],s&&(this.$set(this.sourceList,t,y({},this.sourceList[t],{evalMonthVos:[y({},this.sourceList[t].evalMonthVos[0],{id:n.result.id})]})),this.sourceList[t].evalMonthVos.forEach(function(e,r){var a=1===e.editableFlag?"edit":"view",s=g()(e.month,"YYYYMM").month()+1,o="isEditFlag=".concat(a,"&id=").concat(e.id,"&month=").concat(e.month,"&salesCai=").concat(i.sourceList[t].salesCai),n="".concat(window.location.origin,"/business/evaluation/editPerformanceFlsrPerEvaForm.jsp?")+o;i.$set(i.sourceList[t].evalMonthVos,r,y({},i.sourceList[t].evalMonthVos[r],{link:n,label:s?s+"月":""}))})),this.canToReport=!1;case 14:case"end":return e.stop()}},e,this)}));function t(t){return e.apply(this,arguments)}return t}(),filterByRegion:function(){var e=this;this.sourceList=this.sourceList.filter(function(t){return-1!==e.search.region.indexOf(t.regionName)})},toThousands:function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(0===e)return 0;if(e&&!isNaN(e)){var t="";return t=e.toString().indexOf(".")>-1?e.toString().substr(0,e.toString().indexOf(".")+3).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g,"$1,"):e.toString().replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g,"$1,"),t}},searchChange:function(e){switch(e){case"year":this.search=y({},this.search,{month:""});break;case"quarter":this.search=y({},this.search,{month:""});break;case"month":this.search=y({},this.search,{quarter:""});break;case"region":break;default:break}}}},x=w,V=(r("4a65"),r("2877")),k=Object(V["a"])(x,a,s,!1,null,null,null);t["default"]=k.exports},a463:function(e,t,r){}}]);