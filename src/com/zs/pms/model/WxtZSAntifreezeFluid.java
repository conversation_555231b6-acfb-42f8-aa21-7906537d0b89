package com.zs.pms.model;

import java.util.Date;

public class WxtZSAntifreezeFluid {
    private Long id;

    private String antifreezeFluidId;

    private String brandName;

    private String model;

    private String freezePoint;

    private String bolingPoint;

    private String color;

    private String capacity;

    private Date importTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAntifreezeFluidId() {
        return antifreezeFluidId;
    }

    public void setAntifreezeFluidId(String antifreezeFluidId) {
        this.antifreezeFluidId = antifreezeFluidId == null ? null : antifreezeFluidId.trim();
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName == null ? null : brandName.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getFreezePoint() {
        return freezePoint;
    }

    public void setFreezePoint(String freezePoint) {
        this.freezePoint = freezePoint == null ? null : freezePoint.trim();
    }

    public String getBolingPoint() {
        return bolingPoint;
    }

    public void setBolingPoint(String bolingPoint) {
        this.bolingPoint = bolingPoint == null ? null : bolingPoint.trim();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    public String getCapacity() {
        return capacity;
    }

    public void setCapacity(String capacity) {
        this.capacity = capacity == null ? null : capacity.trim();
    }

    public Date getImportTime() {
        return importTime;
    }

    public void setImportTime(Date importTime) {
        this.importTime = importTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}