package com.chevron.expense.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.expense.model.ExpenseSpecificCondition;
import com.chevron.expense.model.ExpenseStatisticCondition;
import com.chevron.expense.service.ExpenseSpecificService;
import com.chevron.expense.service.ExpenseStatisticService;
import com.common.util.MessageResourceUtil;

@Controller
@RequestMapping(value="/partnerexpense")
public class ExpenseStatisticController {
	
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	
	@Resource
	private ExpenseStatisticService expenseStatisticServiceImpl;
	
	private final static Logger log = Logger.getLogger(ExpenseSpecificController.class);
	
	/**
	 * 合伙人费用查询
	 */
	@ResponseBody
	@RequestMapping(value="/queryPartnerExpenseStatistic.do", method = {RequestMethod.POST})
	public Map<String,Object> queryPartnerExpenseStatistic(ExpenseStatisticCondition expenseConditions,HttpServletRequest request)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			resultMap = expenseStatisticServiceImpl.getPartnerExpenseStatisticPagenationByCondition(expenseConditions);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
}
