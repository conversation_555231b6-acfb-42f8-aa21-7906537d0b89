package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.chevron.master.model.WorkshopMasterExample;
import com.sys.utils.business.EtoBizService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springside.modules.security.utils.Digests;
import org.springside.modules.utils.Encodes;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterParams;
import com.chevron.pms.business.VerificationRuleBizService;
import com.chevron.pms.business.WorkshopDistributionRuleBizService;
import com.chevron.pms.business.WorkshopStatusBizService;
import com.chevron.pms.dao.InventoryVoMapper;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.InventoryQueryParams;
import com.chevron.pms.model.RegionVo;
import com.chevron.pms.model.VerificationRule;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopForExternal;
import com.chevron.pms.service.ChevronCommonService;
import com.chevron.pms.service.RegionService;
import com.chevron.pms.service.WorkShopService;
import com.chevron.pms.service.WorkshopPartnerService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.SaltUtil;
import com.common.util.StringUtils;
import com.common.util.ThrowableUtil;
import com.spatial4j.core.context.SpatialContext;
import com.spatial4j.core.distance.DistanceUtils;
import com.spatial4j.core.shape.Rectangle;
import com.sys.auth.business.RoleBizService;
import com.sys.auth.model.WxTMenu;
import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserbase;
import com.sys.auth.model.WxTUserrole;
import com.sys.auth.service.WxDataService;
import com.sys.auth.service.WxMenuService;
import com.sys.auth.service.WxRoleSourceService;
import com.sys.auth.service.WxTParterRolePowerService;
import com.sys.auth.service.WxUserBaseService;
import com.sys.auth.service.WxUserRoleService;
import com.sys.auth.service.WxUserServiceI;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.organization.model.OrganizationVo;
import com.sys.organization.service.OrganizationService;

@Service
public class WorkShopServiceImpl  implements WorkShopService  {
	private static final int DEFAULT_PAGE_SIZE = 10;

	private static Logger log = LoggerFactory.getLogger(WorkShopServiceImpl.class);
	//@Resource
	//ChevronCommonServiceImpl chevronCommonServiceImpl; delete by bo.liu

//	@Resource
//	public WorkshopMasterMapper workshopMasterMapper;
	
	@Autowired
	public WorkshopMasterBizService workshopMasterBizService;

	@Resource
	WxUserServiceI wxUserService;//modify by bo.liu WxUserService

	@Resource
	WxDataService wxDataServiceImpl;//modify by bo.liu WxDataServiceImpl

	@Resource
	WorkshopPartnerService workshopPartnerServiceImpl;//modify by bo.liu WorkshopPartnerServiceImpl

	@Resource
	OrganizationService organizationServiceImpl;

	@Resource
	WorkshopEmployeeMapper workshopEmployeeMapper;

	@Resource
	RoleBizService roleBizService;

	@Resource
	WxMenuService wxMenuServiceImpl;

	@Resource
	WxUserRoleService wxUserRoleServiceImpl;

	@Resource
	WxRoleSourceService wxRoleSourceServiceImpl;

	@Resource
	WxUserBaseService wxUserBaseServiceImpl;
//
//	@Resource
//	private WorkshopBizService workshopBizService;

	@Resource
	ChevronCommonService chevronCommonServiceImpl;

	@Resource
	RegionService  regionServiceImpl;

	@Resource
	WxAttFileMapper wxAttFileMapper;

	@Resource
	VerificationRuleBizService verificationRuleBizServiceImpl;

	@Resource
	WorkshopDistributionRuleBizService workshopDistributionRuleBizServiceImpl;

	@Resource
	WorkshopStatusBizService workshopStatusBizService;
	@Resource
	InventoryVoMapper inventoryVoMapper;

	@Resource
	WxTParterRolePowerService partnerRolePowerService;
	@Resource
	private DicService dicService;

	@Resource
	private EtoBizService etoBizService;
	
//	public static final String[] WORKSHOP = new String[] { "workShopName",
//		"workShopAddress", "regionName", "longitude", "latitude", "area",
//		"seatsNum", "employeesNum", "serviceScope", "businessLicenseCode",
//		"businessLicenseExpiryDate", "reserveServiceTel", "contactPerson",
//		"contactPersonTel", "harvestPerson", "harvestPersonTel",
//		"buttInChargePerson", "buttInChargePersonTel", "bankAcountName",
//		"bankAcount", "bank", "creditRating", "type", "status", "remark",
//		"workShopCode", "source", "regionId","partnerName","partnerId" };

//	@Override
//	public Map<String,Object> insert(WorkshopMaster workShopVo) throws Exception {
//		Map<String,Object> map = new HashMap<String,Object>();
//		try
//		{
//			workShopVo.setCreateTime(new Date());
//			workShopVo.setUpdateTime(new Date());
//			workShopVo.setCreator(""+ContextUtil.getCurUser().getUserId());//创建人账号
//			if(null == workShopVo.getWorkShopCode() || "".equals(workShopVo.getWorkShopCode())){
//				workShopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//			}
//			List<WorkshopMaster> workShopVoList = new ArrayList<WorkshopMaster>();
//			workShopVoList.add(workShopVo);
//			map  = this.validateData(workShopVoList);
//			//0.先检查是否已经添加
//			if("fail".equals(map.get("code"))){
//				return map;
//			} else {	//1.插入
//				workShopVoMapper.insertSelective(workShopVo);
//
//				List<WorkshopMaster> wsList = new ArrayList<WorkshopMaster>();
//				wsList.add(workShopVo);
//			}
//			map.put("code", "success");
//		}catch(Exception ex){
//			map.put("code", "systemerror");
//			ex.printStackTrace();
//		}
//		return map;
//	}
//
//	@Override
//	public Map<String,Object> batchInsert(List<WorkshopMaster> workShopVoList) throws Exception {
//		Map<String, Object> map = new HashMap<String,Object>();
//		try
//		{
//			map = validateData(workShopVoList);
//			if("fail".equals(map.get("code"))){
//				return map;
//			}else{
//				workShopVoMapper.batchInsert(workShopVoList);
//				map.put("code", "success");
//			}
//		}catch(Exception ex){
//			map.put("code", "systemerror");
//			ex.printStackTrace();
//		}
//		return map;
//	}

//	/**
//	 * 校验门店必填项，和编码、名字是否重复
//	 * @param workShopVoList
//	 * @return
//	 */
//	private Map<String, Object> validateData(List<WorkshopMaster> workShopVoList) {
//		Map<String,Object> resultMap = new HashMap<String,Object>();
//		for(int i=0;i<workShopVoList.size();i++){
//			WorkshopMaster workShopVo  = workShopVoList.get(i);
//			Map<String,Object> reqMap = new HashMap<String,Object>();
//			reqMap.put("workShopCode", workShopVo.getWorkShopCode());
//			reqMap.put("workShopName", workShopVo.getWorkShopName());
//			List<WorkshopMaster>  wsvList = workShopVoMapper.getWorkShopListByNameOrCode(reqMap);
//			if(wsvList.size()>0){
//				resultMap.put("code", "fail");
//				resultMap.put("msg", "门店编码或名称已存在!"+workShopVo.getWorkShopCode()+"-"+workShopVo.getWorkShopName());
//				break;
//			}
//		}
//		resultMap.put("code", "success");
//		return resultMap;
//	}

//	@Override
//	public Map<String,String> delete(WorkshopMaster workShopVo) throws Exception {
//		Map<String,String> map = new HashMap<String,String>();
//		workShopVoMapper.deleteByWorkShopcode(workShopVo.getWorkShopCode());
//		map.put("resultData", "success");
//		return map;
//	}

//	@Override
//	public Map<String,String> update(WorkshopMaster workShopVo) throws Exception {
//		Map<String,String> map = new HashMap<String,String>();
//		try{
//			workShopVo.setUpdateTime(new Date());
//			workShopVoMapper.updateByPrimaryKeySelective(workShopVo);
//			map.put("code", "success");
//		}catch(Exception ex){
//			map.put("code", "systemerror");
//			 ex.printStackTrace();
//		}
//		return map;
//	}


//	@Override
//	public Map<String,Workshopmas> getWorkShop(String workShopCode) throws Exception {
//		Map<String,WorkshopMaster> map = new HashMap<String,WorkshopMaster>();
//		workShopVoMapper.selectByWorkShopcode(workShopCode);
//		return map;
//	}

	@Override
	public Map<String,WorkshopMaster> getWorkShopById(Long id) throws Exception {
		Map<String,WorkshopMaster> map = new HashMap<String,WorkshopMaster>();
		map.put("workshop", workshopMasterBizService.getBean(id));
		return map;
	}

	@Override
	public Map<String,Object> getWorkShopAndAdressDetailById(Long id) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();

		WorkshopMaster workshop = workshopMasterBizService.getBean(id);
		map.put("workshop", workshop);
		Long regionId = workshop.getRegionId();
		if(regionId != null){
			List<RegionVo> regionList =  regionServiceImpl.getProvCityDistByRegionId(regionId);
			String address = new String("");
			for(RegionVo region: regionList){
				if("P".equals(region.getRegionType())){
					address = address + region.getRegionName();
				}
			}
			for(RegionVo region: regionList){
				if("C".equals(region.getRegionType())){
					address = address + region.getRegionName();
				}
			}
			for(RegionVo region: regionList){
				if("D".equals(region.getRegionType())){
					address = address + region.getRegionName();
				}
			}

			map.put("address", address);
		}else{
			map.put("address", "");
		}

		return map;
	}

//	@Override
//	public Map<String, Object> getWorkShopByParamers(String partnerId)
//			throws Exception {
//		Map<String, Object> map = new HashMap<String, Object>();
//		try{
//			List<WorkshopMaster> workShopList = new ArrayList<WorkshopMaster>();
//			OrganizationVo organizationVo = null;
//			List<WxTUser>  executeUsersList = new ArrayList<WxTUser>();
//			List<WxTData> sourceList = new ArrayList<WxTData>();
//			List<WxTData> signedTypeList =new ArrayList<WxTData>();
//			List<WxTData> statusList = new ArrayList<WxTData>();
//
//			sourceList.addAll(wxDataServiceImpl.selectByDataCode(WORKSHOP_SOURCE_DATA_CODE_VALUE));
//			signedTypeList.addAll(wxDataServiceImpl.selectByDataCode(SIGNED_TYPE_DATA_CODE_VALUE));
//			statusList.addAll(wxDataServiceImpl.selectByDataCode(STATUS_CODE_VALUE));
//
//			WxTUser tuser = ContextUtil.getCurUser();
//			boolean isChervon = tuser.getmUserTypes() == 1l;//chevronCommonServiceImpl.isIncludeRole(tuser.getUserId(), Contants.CHEVRON_MANAGER_CODE);
//			WorkShopVoExample example = new WorkShopVoExample();
//			Criteria criteria = example.createCriteria();
//			if(null!=partnerId){
//				long id  = Long.valueOf(partnerId);
//				List<Long> workshopIdList  = new ArrayList<Long>();
//				List<WorkshopPartnerVo> workshopPartnerList = workshopPartnerServiceImpl.getWorkShopPartnerByPartnerId(id);
//				for(int i = 0; i<workshopPartnerList.size(); i++){
//					WorkshopPartnerVo wpv  = workshopPartnerList.get(i);
//					workshopIdList.add(wpv.getWorkshopId());
//					workShopVoMapper.selectByExample(example);
//				}
//				if(workshopIdList.size()>0){
//					criteria.andIdIn(workshopIdList);
//					workShopList.addAll(workShopVoMapper.selectByExample(example));
//				}
//
//				organizationVo = organizationServiceImpl.selectByPrimaryKey(id);
//
//				executeUsersList = wxUserService.findUserByOrgIdAndRoleName(partnerId,Constants.SERVICE_PARTNER_BD_CODE);
//			}else{
//				organizationVo = new OrganizationVo();
//				if(isChervon){
//					workShopList.addAll(workShopVoMapper.selectByExample(example));
//				}else{
//					long orgId  = tuser.getOrgId();
//					workShopList.addAll(workShopVoMapper.selectByPartnerId(orgId));
//				}
//			}
//			map.put("resultData", workShopList);
//			map.put("organizationVo", organizationVo);
//			map.put("bdUserList", executeUsersList);
//			map.put("sourceList", sourceList);
//			map.put("signedTypeList", signedTypeList);
//			map.put("statusList", statusList);
//			map.put("code", "success");
//		}catch (Exception e) {
//			e.printStackTrace();
//			map.put("code", "systemerror");
//		}
//		return map;
//	}

//
//	@Override
//	public Map<String, Object> deleteByWorkShopCode(String workShopCode)
//			throws Exception {
//		Map<String,Object> map = new HashMap<String,Object>();
//		try{
//			workShopVoMapper.deleteByWorkShopcode(workShopCode);
//			map.put("code", "success");
//		}catch(Exception ex)
//		{
//			map.put("code", "systemerror");
//			ex.printStackTrace();
//		}
//
//		return map;
//	}

//	@Override
//	public Map<String,String> deleteById(List<Long> ids) throws Exception {
//		Map<String,String> map = new HashMap<String,String>();
//		try {
//			WorkShopVoExample example = new WorkShopVoExample();
//			example.createCriteria().andIdIn(ids);
//			//删除验证
//			int validateResult = workShopVoMapper.validateDelete(example);
//			if((validateResult & 1) == 1){
//				//门店有扫码记录，不能删除
//				map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//				map.put(Constants.RESULT_ERROR_MSG_KEY, "被删除门店有扫码记录，不能删除");
//				return map;
//			}
//			WorkshopMaster record = new WorkshopMaster();
//			record.setStatus(WorkshopMaster.WORKSHOP_STATUS_DELETE);
//			record.setUpdateTime(new Date());
//			workShopVoMapper.updateByExampleSelective(record, example);
//			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
//		} catch (Exception e) {
//			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
//			//add by bo.liu 180111
//			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
//			log.error("deleteById error. " + e.getMessage(), e);
//		}
//		return map;
//	}

//	/**
//	 * 新增workshop
//	 * @param workshopName
//	 * @param parnterId
//	 * @param parnterName
//	 * @param workShopAddress
//	 * @param longitude
//	 * @param latitude
//	 * @param area
//	 * @param seatsNum
//	 * @param employeesNum
//	 * @param serviceScope
//	 * @param businessLicenseCode
//	 * @param reserveServiceTel
//	 * @param contactPerson
//	 * @param contactPersonTel
//	 * @param businessDetail
//	 * @param harvestPerson
//	 * @param harvestPersonTel
//	 * @param buttInChargePerson
//	 * @param buttInChargePersonTel
//	 * @param bankAcountName
//	 * @param bankAcount
//	 * @param bank
//	 * @param creditRating
//	 * @param regionId
//	 * @param regionName
//	 * @param remark
//	 * @param status
//	 * @return
//	 * @throws Exception
//	 */
//public Map<String,String> insertWorkshop(String workshopName,String regionId,String regionName,String excuteUserId,String workShopAddress,String longitude,String latitude,String area,String seatsNum,String employeesNum
//			,String serviceScope,String businessLicenseCode,String reserveServiceTel,String contactPerson,String contactPersonTel,String source,String signedType,String businessDetail,String harvestPerson,String harvestPersonTel,
//			String buttInChargePerson,String buttInChargePersonTel,String bankAcountName,String bankAcount,String bank,String creditRating,String remark,String status) throws Exception
//{
//	Map<String,String> map = new HashMap<String,String>();
//	try
//	{
//		WorkshopMaster workShopVo = new WorkshopMaster();
//		workShopVo.setWorkShopAddress(workShopAddress);
//		workShopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//		workShopVo.setWorkShopName(workshopName);
//		workShopVo.setRegionId(Long.valueOf(regionId));
//		workShopVo.setRegionName(regionName);
//		if(null != excuteUserId && !"".equals(excuteUserId)){
//			workShopVo.setExcuteUserId(Long.valueOf(excuteUserId));
//		}
//		if(null!= latitude){
//			 workShopVo.setLatitude(Double.valueOf(latitude));
//		}
//		if(null!=longitude){
//			 workShopVo.setLongitude(Double.valueOf(longitude));
//		}
//
//		workShopVo.setArea(area);
//		if(null==seatsNum || "".equals(seatsNum)){
//			  workShopVo.setSeatsNum(0);
//		}else{
//			workShopVo.setSeatsNum(Integer.parseInt(seatsNum));
//		}
//		if(null==employeesNum || "".equals(employeesNum)){
//			workShopVo.setEmployeesNum(0);
//		}else{
//			workShopVo.setEmployeesNum(Integer.parseInt(employeesNum));
//		}
//		workShopVo.setServiceScope(serviceScope);
//		workShopVo.setBusinessLicenseCode(businessLicenseCode);
//		workShopVo.setReserveServiceTel(reserveServiceTel);
//		workShopVo.setContactPerson(contactPerson);
//		workShopVo.setContactPersonTel(contactPersonTel);
//		workShopVo.setSource(source);
//		workShopVo.setSignedType(signedType);
//		workShopVo.setBusinessDetail(businessDetail);
//		workShopVo.setHarvestPerson(harvestPersonTel);
//		workShopVo.setHarvestPersonTel(harvestPersonTel);
//		workShopVo.setButtInChargePerson(buttInChargePerson);
//		workShopVo.setButtInChargePersonTel(buttInChargePersonTel);
//		workShopVo.setBank(bank);
//		workShopVo.setBankAcount(bankAcount);
//		workShopVo.setBankAcountName(bankAcountName);
//		workShopVo.setCreditRating(creditRating);
//		workShopVo.setRemark(remark);
//		workShopVo.setStatus(status);
//		String mCurrentDate = DateUtils.getCurrentDateString();
//		workShopVo.setCreateTime(DateUtils.stringToDate(mCurrentDate));
//		workShopVo.setUpdateTime(DateUtils.stringToDate(mCurrentDate));
//		workShopVo.setCreator(""+ContextUtil.getCurUser().getUserId());//创建人账号
//		//0.先检查是否已经添加
//		if(null!=workShopVoMapper.selectByWorkShopcode(workShopVo.getWorkShopCode()))
//		{
//			map.put("code", "isexit");
//		}else{   //1.插入
//			workShopVoMapper.insertSelective(workShopVo);
//		}
//		map.put("code", "success");
//	}catch(Exception ex)
//	{
//		map.put("code", "systemerror");
//		ex.printStackTrace();
//	}
//	return map;
//}
//
//public Map<String,String> updateWorkshop(String workshopCode,String workshopName,String regionId,
//		String regionName,String excuteUserId,String workShopAddress,String longitude,String latitude,String area,
//		String seatsNum,String employeesNum,String serviceScope,String businessLicenseCode,
//		String reserveServiceTel,String contactPerson,String contactPersonTel,String source,String signedType,String businessDetail,
//		String harvestPerson,String harvestPersonTel,String buttInChargePerson,String buttInChargePersonTel,
//		String bankAcountName,String bankAcount,String bank,String creditRating,String remark,String status) throws Exception
//{
//
//	Map<String,String> map = new HashMap<String,String>();
//	try
//	{
//		WorkshopMaster workShopVo = new WorkshopMaster();
//		workShopVo.setWorkShopAddress(workShopAddress);
//		workShopVo.setWorkShopCode(workshopCode);
//		workShopVo.setWorkShopName(workshopName);
//		workShopVo.setRegionId(Long.valueOf(regionId));
//		workShopVo.setRegionName(regionName);
//		if(!(null==excuteUserId || "".equals(excuteUserId))){
//			workShopVo.setExcuteUserId(Long.valueOf(excuteUserId));
//		}
//		if(!(null==latitude || "".equals(latitude))){
//			workShopVo.setLatitude(Double.valueOf(latitude));
//		}
//		if(!(null==longitude || "".equals(longitude))){
//			 workShopVo.setLongitude(Double.valueOf(longitude));
//		}
//		workShopVo.setArea(area);
//		if(null==seatsNum || "".equals(seatsNum))
//		{
//			  workShopVo.setSeatsNum(0);
//		}else{
//			workShopVo.setSeatsNum(Integer.parseInt(seatsNum));
//		}
//		if(null==employeesNum || "".equals(employeesNum))
//		{
//			workShopVo.setEmployeesNum(0);
//		}else
//		{
//			workShopVo.setEmployeesNum(Integer.parseInt(employeesNum));
//		}
//		workShopVo.setServiceScope(serviceScope);
//		workShopVo.setBusinessLicenseCode(businessLicenseCode);
//		workShopVo.setReserveServiceTel(reserveServiceTel);
//		workShopVo.setContactPerson(contactPerson);
//		workShopVo.setContactPersonTel(contactPersonTel);
//		workShopVo.setSource(source);
//		workShopVo.setSignedType(signedType);
//		workShopVo.setBusinessDetail(businessDetail);
//		workShopVo.setHarvestPerson(harvestPersonTel);
//		workShopVo.setHarvestPersonTel(harvestPersonTel);
//		workShopVo.setButtInChargePerson(buttInChargePerson);
//		workShopVo.setButtInChargePersonTel(buttInChargePersonTel);
//		workShopVo.setBank(bank);
//		workShopVo.setBankAcount(bankAcount);
//		workShopVo.setBankAcountName(bankAcountName);
//		workShopVo.setCreditRating(creditRating);
//		workShopVo.setRemark(remark);
//		workShopVo.setStatus(status);
//		String mCurrentDate = DateUtils.getCurrentDateString();
//		workShopVo.setUpdateTime(DateUtils.stringToDate(mCurrentDate));
//		workShopVo.setCreator(""+ContextUtil.getCurUser().getUserId());//创建人账号
//		workShopVoMapper.updateByWorkShopcode(workShopVo);
//
//		map.put("code", "success");
//	}catch(Exception ex)
//	{
//		map.put("code", "systemerror");
//		ex.printStackTrace();
//	}
//	return map;
//}
//
////by bo.liu end
//
//@Override
//public Map<String,Object> importWorkshopBat(Workbook wb){
//	Map<String,Object> map = new HashMap<String,Object>();
////	Long currenyUserId = ContextUtil.getCurUser().getTenantId();
//	Long currenyUserId = ContextUtil.getCurUserId();
//	Sheet sheet = wb.getSheetAt(0);
//	int rowNum = sheet.getLastRowNum();
//	boolean bool = validateData(map, sheet);
//	if(!bool){
//		return map;
//	}
//	List<WorkshopMaster> voList = new ArrayList<WorkshopMaster>();
//	List<Map<String,Object>> errorList = new ArrayList<Map<String,Object>>();
//	for(int i=1; i<=rowNum; i++){
//		try {
//			WorkshopMaster workShopVo = new WorkshopMaster();
//			Row row = sheet.getRow(i);
//			//TODO:对每一个录入数据做格式检查
//			String workShopCode = CommonUtil.generateCode("WS");
//			workShopVo.setWorkShopCode(workShopCode);
//			String workShopName = StringUtils.getCellStringValue(row.getCell(0));
//			workShopVo.setWorkShopName(workShopName);
//			String workShopAddress = StringUtils.getCellStringValue(row.getCell(1));
//			workShopVo.setWorkShopAddress(workShopAddress);
//			String region = StringUtils.getCellStringValue(row.getCell(2));
//			String[] arr = region.split("-");
//			workShopVo.setRegionId(Long.valueOf(arr[1]));
//			workShopVo.setRegionName(arr[0]);
//
//			String longtitude = StringUtils.getCellStringValue(row.getCell(5));
//			workShopVo.setLongitude(Double.valueOf("".equals(longtitude) ? "0":longtitude));
//			String latitude = StringUtils.getCellStringValue(row.getCell(6));
//			workShopVo.setLatitude(Double.valueOf("".equals(latitude)? "0":latitude));
//			String area = StringUtils.getCellStringValue(row.getCell(7));
//			workShopVo.setArea(area);
//			String seatsNum = StringUtils.getCellStringValue(row.getCell(8));
//			workShopVo.setSeatsNum(Integer.valueOf("".equals(seatsNum)? "0":seatsNum));
//			String employeesNum = StringUtils.getCellStringValue(row.getCell(9));
//			workShopVo.setEmployeesNum(Integer.valueOf("".equals(employeesNum) ? "0":employeesNum));
//			String serviceScope = StringUtils.getCellStringValue(row.getCell(10));
//			workShopVo.setServiceScope(serviceScope);
//			String businessLicenseCode = StringUtils.getCellStringValue(row.getCell(11));
//			workShopVo.setBusinessLicenseCode(businessLicenseCode);
//			String businessLicenseExpiryDate = StringUtils.getCellStringValue(row.getCell(12));
//			if(!"".equals(businessLicenseExpiryDate)){
//				SimpleDateFormat sdf = new SimpleDateFormat("YYYY-MM-dd");
//				workShopVo.setBusinessLicenseExpiryDate(sdf.parse(businessLicenseExpiryDate));
//			}
//
//			String reserveServiceTel = StringUtils.getCellStringValue(row.getCell(13));
//			workShopVo.setReserveServiceTel(reserveServiceTel);
//			String contactPerson = StringUtils.getCellStringValue(row.getCell(14));
//			workShopVo.setContactPerson(contactPerson);
//			String contactPersonTel = StringUtils.getCellStringValue(row.getCell(15));
//			workShopVo.setContactPersonTel(contactPersonTel);
//			String harvestPerson = StringUtils.getCellStringValue(row.getCell(16));
//			workShopVo.setHarvestPerson(harvestPerson);
//			String harvestPersonTel = StringUtils.getCellStringValue(row.getCell(17));
//			workShopVo.setHarvestPersonTel(harvestPersonTel);
//			String buttInChargePerson = StringUtils.getCellStringValue(row.getCell(18));
//			workShopVo.setButtInChargePerson(buttInChargePerson);
//			String buttInChargePersonTel = StringUtils.getCellStringValue(row.getCell(19));
//			workShopVo.setButtInChargePersonTel(buttInChargePersonTel);
//			String bankAcountName = StringUtils.getCellStringValue(row.getCell(20));
//			workShopVo.setBankAcountName(bankAcountName);
//			String bankAcount = StringUtils.getCellStringValue(row.getCell(21));
//			workShopVo.setBankAcount(bankAcount);
//			String bank = StringUtils.getCellStringValue(row.getCell(22));
//			workShopVo.setBank(bank);
//			String creditRating = StringUtils.getCellStringValue(row.getCell(23));
//			workShopVo.setCreditRating(creditRating);
//			String type   = StringUtils.getCellStringValue(row.getCell(24));
//			workShopVo.setType(type);
////			String status = StringUtil.getCellStringValue(row.getCell(25));
//			workShopVo.setStatus("3");//默认导入的门店信息为已激活状态
//			workShopVo.setSource("excel导入");
//			String remark = StringUtils.getCellStringValue(row.getCell(26));
//			workShopVo.setRemark(remark);
//			workShopVo.setCreateTime(new Date());
//			workShopVo.setUpdateTime(new Date());
//			workShopVo.setCreator(String.valueOf(currenyUserId));
//
//			//TODO 考虑如何关联
////			WorkshopPartnerVo wpv = new WorkshopPartnerVo();
////			wpv.setPartnerId(partnerId);
////			wpv.setPartnerName(partnerName);
////			wpv.setWorkshopId(workshopId);
////			wpv.setWorkshopName(workshopName);
////			String parnterCode = StringUtil.getCellStringValue(row.getCell(3));
////			workShopVo.setParnterCode(parnterCode);
////			String parnterName = StringUtil.getCellStringValue(row.getCell(4));
////			workShopVo.setParnterName(parnterName);
//
//			voList.add(workShopVo);
//		}catch(Exception e){
//			e.printStackTrace();
//			Map<String,Object> error = new HashMap<String,Object>();
//			error.put("error", "未知错误");
//			error.put("line", i);
//			errorList.add(error);
//		}
//	}
//	if(voList.size()>0){
//		for(int i =0;i<voList.size();i++){
//			workShopVoMapper.insert(voList.get(i));
//		}
//		map.put("code", "success");
//		map.put("codeMsg", "导入成功,数量："+voList.size());
//		return map;
//	}
//	return map;
//}
//
///**
// * 校验excel数据合法性
// * @param map
// * @param sheet
// * @return
// */
//private boolean validateData(Map<String, Object> map, Sheet sheet) {
//	int rowNum = sheet.getLastRowNum();
//	Row row = sheet.getRow(0);
//	System.out.println(row.getLastCellNum());
//	System.out.println(StringUtils.getCellStringValue(row.getCell(2)).indexOf("门店地址")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(3)).indexOf("区域编码")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(4)).indexOf("区域名称")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(5)).indexOf("合伙人编码")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(6)).indexOf("合伙人名称")==-1);
//	System.out.println(StringUtils.getCellStringValue(row.getCell(7)).indexOf("经度")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(8)).indexOf("纬度")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(9)).indexOf("面积")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(10)).indexOf("工位数")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(11)).indexOf("员工数")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(12)).indexOf("服务范围")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(13)).indexOf("营业执照编码")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(14)).indexOf("营业执照有效期")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(15)).indexOf("预约服务电话")==-1);
//	System.out.println(StringUtils.getCellStringValue(row.getCell(16)).indexOf("主要联系人")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(17)).indexOf("主要联系人电话")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(18)).indexOf("收货负责人")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(19)).indexOf("收货负责人电话")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(20)).indexOf("对接负责人")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(21)).indexOf("对接负责人联系电话")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(22)).indexOf("银行开户名称") ==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(23)).indexOf("银行开户账号")==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(24)).indexOf("银行开户行") ==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(25)).indexOf("信用等级") ==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(26)).indexOf("门店类型") ==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(27)).indexOf("门店状态") ==-1 );
//	System.out.println(StringUtils.getCellStringValue(row.getCell(28)).indexOf("备注")==-1);
//
//	if(rowNum <= 1){
//		map.put("code", "warning");
//		map.put("warning", "没有数据");
//		return false;
//	}else if(row.getLastCellNum() != 27){
//		map.put("code", "warning");
//		map.put("warning", "表格格式错误");
//		return false;
//	}else if(
//		StringUtils.getCellStringValue(row.getCell(0)).indexOf("门店名称")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(1)).indexOf("门店地址")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(2)).indexOf("区域名称")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(3)).indexOf("合伙人编码")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(4)).indexOf("合伙人名称")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(5)).indexOf("经度")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(6)).indexOf("纬度")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(7)).indexOf("面积")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(8)).indexOf("工位数")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(9)).indexOf("员工数")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(10)).indexOf("服务范围")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(11)).indexOf("营业执照编码")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(12)).indexOf("营业执照有效期")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(13)).indexOf("预约服务电话")==-1||
//		StringUtils.getCellStringValue(row.getCell(14)).indexOf("主要联系人")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(15)).indexOf("主要联系人电话")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(16)).indexOf("收货负责人")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(17)).indexOf("收货负责人电话")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(18)).indexOf("对接负责人")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(19)).indexOf("对接负责人联系电话")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(20)).indexOf("银行开户名称") ==-1 ||
//		StringUtils.getCellStringValue(row.getCell(21)).indexOf("银行开户账号")==-1 ||
//		StringUtils.getCellStringValue(row.getCell(22)).indexOf("银行开户行") ==-1 ||
//		StringUtils.getCellStringValue(row.getCell(23)).indexOf("信用等级") ==-1 ||
//		StringUtils.getCellStringValue(row.getCell(24)).indexOf("门店类型") ==-1 ||
//		StringUtils.getCellStringValue(row.getCell(25)).indexOf("门店状态") ==-1 ||
//		StringUtils.getCellStringValue(row.getCell(26)).indexOf("备注")==-1){
//
//		map.put("code", "warning");
//		map.put("warning", "表格格式错误");
//		return false;
//	}
//	return true;
//}

//
//	@Override
//	public Map<String, Object> queryWorkshopByOrders(String orderType) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		Map<String, Object> paramMap = new HashMap<String, Object>();
//		paramMap.put("orderType", orderType);
//		List<WorkshopMaster> workshopList = workShopVoMapper.selectWorkshopByOrders(paramMap);
//		resultMap.put("success", true);
//		resultMap.put("data", workshopList);
//		return resultMap;
//	}


	@Override
	public Map<String, Object> queryWorkshopListByLocation(final Double longitude, final Double latitude, Double radius, Integer pageNo, Integer pageSize) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (longitude == null || latitude == null || radius == null) {
				throw new WxPltException("illegal parameter");
			}

			if (radius > 200){
				throw new WxPltException("radius is too large");
			}
			Map<String, Double> location = getLocationMap(longitude, latitude, radius);
			Map<String, Object> params = new HashMap<String, Object>(5);
			params.put("location", location);

			//不需要根据当前用户的权限进行过滤
			List<WorkshopMaster> workshopList = workshopMasterBizService.querySimpleByParams(params);

			// 对结果进一步处理,剔除不满足条件的数据
			List<WorkshopForExternal> workshopExternalList = eliminateUnMatchedWorkshop(workshopList, longitude, latitude, radius);

			//结果排序
			Collections.sort(workshopExternalList, new Comparator<WorkshopForExternal>(){

				@Override
				public int compare(WorkshopForExternal o1, WorkshopForExternal o2) {
					return (o1.getDistance() < o2.getDistance()) ? -1 : 1;
				}

			});

			//分页
			if (pageNo != null && pageNo.intValue() > 0){
				workshopExternalList = paginateList(workshopExternalList, pageNo, pageSize);
			}

			resultMap.put("code", "success");
			resultMap.put("data", workshopExternalList);
		} catch (WxPltException e) {
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("query workshop list by location exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
		}
		return resultMap;
	}

	private <T> List<T> paginateList(List<T> list, Integer pageNo, Integer pageSize) {
		if (pageSize == null){
			pageSize = DEFAULT_PAGE_SIZE;
		}

		int total = list.size();
		int indexFrom = (pageNo - 1) * pageSize;
		int indexTo = (pageNo * pageSize) - 1;
		if (indexFrom >= total){
			return Collections.emptyList();
		}
		if (indexTo >= total) {
			indexTo = total - 1;
		}
		return list.subList(indexFrom, indexTo + 1);
	}


	private List<WorkshopForExternal> eliminateUnMatchedWorkshop(List<WorkshopMaster> workshopList, Double longitude, Double latitude,
			Double radius) {
		List<WorkshopForExternal> workshopExternalList = new ArrayList<WorkshopForExternal>();
		if (workshopList != null && workshopList.size() > 0){
			String downloadUrl = (String)Constants.getSystemPropertyByCodeType(Constants.DOWNLOAD_URL);
			log.info("downloadUrl: {}", downloadUrl);
			Iterator<WorkshopMaster> workshopIter = workshopList.iterator();
			while (workshopIter.hasNext()){
				WorkshopMaster workshop = workshopIter.next();
				double distance = getDistanceBetweenLocationAndWorkshop(longitude, latitude, workshop);
				log.info("distance: {}", distance);
				if (distance > radius.doubleValue()){
					workshopIter.remove();
				} else {
					WorkshopForExternal workshopExternal = new WorkshopForExternal(workshop, distance, downloadUrl + workshop.getPhotoId());
					workshopExternalList.add(workshopExternal);
				}

			}
		}
		return workshopExternalList;
	}

	private double getDistanceBetweenLocationAndWorkshop(Double longitude, Double latitude, WorkshopMaster workshop) {
		SpatialContext geo = SpatialContext.GEO;
		double distance = geo.calcDistance(geo.makePoint(longitude, latitude),
				geo.makePoint(workshop.getLongitude(), workshop.getLatitude())) * DistanceUtils.DEG_TO_KM;
		return distance;
	}

	private Map<String, Double> getLocationMap(Double longitude, Double latitude, Double radius) {
		SpatialContext geo = SpatialContext.GEO;
		Rectangle rectangle = geo.getDistCalc().calcBoxByDistFromPt(geo.makePoint(longitude, latitude),
				radius * DistanceUtils.KM_TO_DEG, geo, null);
		Map<String, Double> location = new HashMap<String, Double>();
		location.put("minX", rectangle.getMinX());
		location.put("maxX", rectangle.getMaxX());
		location.put("minY", rectangle.getMinY());
		location.put("maxY", rectangle.getMaxY());
		return location;
	}

//	@Override
//	public void insertAndUpdateBatch(List<WorkshopMaster> wsvList) {
//		if(null!=wsvList && wsvList.size()>0){
//			List<WorkshopMaster> updateList = new ArrayList<WorkshopMaster>();
//			List<WorkshopMaster> insertList = new ArrayList<WorkshopMaster>();
//			for(int i =0;i<wsvList.size();i++){
//				WorkshopMaster wsv = wsvList.get(i);
//				String workShopCode = wsv.getWorkShopCode();
//				WorkshopMaster re =	workShopVoMapper.selectByWorkShopcode(workShopCode);
//				if(null!=re){
//					wsv.setId(re.getId());
//					updateList.add(wsv);
//				}else{
//					insertList.add(wsv);
//				}
//			}
//
//			if(insertList.size()>0){
//				workShopVoMapper.batchInsert(insertList);
//			}
//			if(updateList.size()>0){
//				workShopVoMapper.batchUpdate(updateList);
//			}
//		}
//	}
//	@Override
//	public int batchInsertWorkShop(List<WorkshopMaster> workShopList){
//		if(workShopList.size()>0){
//			return workShopVoMapper.batchInsert(workShopList);
//		}else{
//			return 0;
//		}
//	}
//	@Override
//	public int batchUpdateWorkShop(List<WorkshopMaster> workShopList){
//		if(workShopList.size()>0){
//			return workShopVoMapper.batchUpdate(workShopList);
//		}else{
//			return 0;
//		}
//	}

	//add by bo.liu start 0815
	@Override
	public Map<String, Object> getAllPartnerByRegionID(String regionId) {

		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		resultMap.put("code", "success");
		try
		{
			if(null==regionId || regionId.equals(""))
			{
				resultMap.put("code", "isnull");
				return resultMap;
			}
			reqMap.put("regionId", Long.parseLong(regionId));
			List<OrganizationVo> partnerLst = organizationServiceImpl.getAllPartnerByRegionId(reqMap);
			resultMap.put("partnerLst", partnerLst);

		}catch(Exception ex)
		{
			ex.printStackTrace();
			resultMap.put("code", "error");
		}
		return resultMap;
	}

//	@Override
//	public Map<String, Object> getWorkshopForUpdateByWorkshopId(
//			String workshopId) {
//		Map<String,Object> resultMap = new HashMap<String,Object>();
//		Map<String,Object> reqMap = new HashMap<String,Object>();
//		resultMap.put("code", "success");
//		try
//		{
//			//0.获取门店信息
//			if(null==workshopId || workshopId.equals("") ||workshopId.equals("null"))
//			{
//				resultMap.put("code", "isnull");
//				return resultMap;
//			}
//			Long workshopIdl = Long.parseLong(workshopId);
//			reqMap.put("workshopId", workshopIdl);
//			WorkshopMaster workShopVo  = workShopVoMapper.getWorkShopById(reqMap);
//			if(null==workShopVo){
//				resultMap.put("code", "isnull");
//				return resultMap;
//			}
//
//			//1.根据门店id获取已经选择的partner
//			List<WorkshopPartnerVo> workshopPartnerLst =  workshopPartnerServiceImpl.getSelectedPartnerByWorkshopId(workshopIdl);
//
//			String partnerIds="";
//			for(int i=0;i<workshopPartnerLst.size();i++)
//			{
//				partnerIds+=workshopPartnerLst.get(i).getPartnerId();
//				if(i!=workshopPartnerLst.size()-1)
//				{
//					partnerIds+=",";
//				}
//			}
//			workShopVo.setPartnerIds(partnerIds);
//			//获取对应门店的省市，区域
//			Long regionId = workShopVo.getRegionId();
//			if(null!=regionId && regionId > 0)
//			{
//				RegionVo  regionVo = regionServiceImpl.getRegionParentIdByRegionId(workShopVo.getRegionId());
//				workShopVo.setProvinceId(regionVo.getProvinceId());
//				workShopVo.setCityId(regionVo.getCityId());
//			}
//
//			resultMap.put("mWorkShopVo", workShopVo);
//
//			//2.获取技师信息
//			List<WorkshopEmployee> mechainVoLst = workshopEmployeeMapper.getMechanicByWorkshopId(reqMap);
//			resultMap.put("mechainVoLst", mechainVoLst);
//
//			//核销奖励分配规则
//			resultMap.put("distributionRule", workshopDistributionRuleBizServiceImpl.getWorkshopActiveRule(workshopIdl));
//			resultMap.put("skuDistributionRules", workshopDistributionRuleBizServiceImpl.queryWorkshopSkuDistributionRules(workshopIdl));
//
//		}catch(Exception ex){
//			ex.printStackTrace();
//			resultMap.put("code", "error");
//		}
//		return resultMap;
//	}
//
//	@Override
//	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
//	public Map<String, Object> insertOrUpdateWorkShop(WorkshopMaster mWorkshopVo, WxAttFile[] attFiles,
//			WxAttFile[] rewardCtracts, WorkshopDistributionRule distributionRule, Long subTaskId,
//			WorkshopDistributionRule[] skuDistributionRules, List<Long> deletedSkuDistributionRules) {
//		Map<String,Object> resultMap = new HashMap<String,Object>();
//		Map<String,Object> reqMap = new HashMap<String,Object>();
//		resultMap.put("code", "success");
//		try
//		{
//			Long mworkshopId = 0L;
//			WorkshopDistributionRule oldDistributionRule = null;
//			//0.保存门店信息
//			if(null==mWorkshopVo.getId()){
//				String workShopCode = mWorkshopVo.getWorkShopCode();
//				if(null== workShopCode || "".equals(workShopCode)){
//					mWorkshopVo.setWorkShopCode(CommonUtil.generateCode("WS"));
//				}
//				//更新店招店修改时间
//				if(!EmptyChecker.isEmpty(mWorkshopVo.getShopRecruitment())) {
//					mWorkshopVo.setShopRecruitmentUpdateTime(new Date());
//				}
//				workShopVoMapper.insertSelective(mWorkshopVo);
//				mworkshopId = mWorkshopVo.getId();
//				if(WorkshopMaster.WORKSHOP_STATUS2.equals(mWorkshopVo.getStatus())){
//					//新建的激活状态门店需要做录店统计
//					if(workshopStatusBizService.insertWhenNotExists(mworkshopId, WorkshopStatusVo.STATUS_ACTIVE, null)){
//						activeWorkshop(mworkshopId, mWorkshopVo.getPartnerIds());
//					}
//				}
//				//更新门店合同附件
//				if(attFiles != null){
//					for(WxAttFile file : attFiles){
//						file.setSourceId(mworkshopId);
//						wxAttFileMapper.updateByPrimaryKeySelective(file);
//					}
//				}
//				//更新门店激励合同附件
//				if(rewardCtracts != null){
//					for(WxAttFile file : rewardCtracts){
//						file.setSourceId(mworkshopId);
//						wxAttFileMapper.updateByPrimaryKeySelective(file);
//					}
//				}
//			}else{
//				mworkshopId = mWorkshopVo.getId();
//				mWorkshopVo.setUpdateTime(new Date());
//				//更新店招店修改时间
//				reqMap.put("workshopId", mWorkshopVo.getId());
//				WorkshopMaster oldData  = workShopVoMapper.getWorkShopById(reqMap);
//				if(!EmptyChecker.isEmpty(mWorkshopVo.getShopRecruitment())
//						&& !mWorkshopVo.getShopRecruitment().equals(oldData.getShopRecruitment())) {
//					mWorkshopVo.setShopRecruitmentUpdateTime(new Date());
//				}
//				workShopVoMapper.updateByPrimaryKeySelective(mWorkshopVo);
//
//				if(subTaskId != null){
//					//需要插入状态记录表,用于统计已激活的门店于
//					if(workshopStatusBizService.insertWhenNotExists(mworkshopId, WorkshopStatusVo.STATUS_ACTIVE, subTaskId)){//subTaskId需要从前台去获取，任务去录店的时候已经传递了
//						activeWorkshop(mworkshopId, mWorkshopVo.getPartnerIds());
//					}
//				}
//				//1.删除已有的partner id
////				wxWorkshopVoPartnerMapper.deleteWorkshopPartnerVoByWorkshopId(reqMap);
//				workshopPartnerServiceImpl.deleteWorkshopPartnerVoByWorkshopId(mWorkshopVo.getId());
//				if(distributionRule != null){
//					oldDistributionRule = workshopDistributionRuleBizServiceImpl.queryDistributionRuleByWorkshopId(mworkshopId);
//				}
//		        //删除优先核销分配规则
//		        if(deletedSkuDistributionRules != null && !deletedSkuDistributionRules.isEmpty()){
//		        	workshopDistributionRuleBizServiceImpl.deleteRule(deletedSkuDistributionRules);
//		        }
//			}
//			//更新核销分配规则
//			if(distributionRule != null && (oldDistributionRule == null ||
//					"Y".equals(oldDistributionRule.getAwardRealtime()) != "Y".equals(distributionRule.getAwardRealtime())
//					|| !oldDistributionRule.getPerLiterReward().equals(distributionRule.getPerLiterReward())
//					|| !oldDistributionRule.getOwnerReward().equals(distributionRule.getOwnerReward()))){
//				distributionRule.setWorkshopId(mworkshopId);
//				if(oldDistributionRule != null){
//					distributionRule.setId(oldDistributionRule.getId());
//				}
//				workshopDistributionRuleBizServiceImpl.saveDistributionRule(distributionRule);
//			}
//	        //更新优先核销分配规则
//	        if(skuDistributionRules != null){
//	        	for(WorkshopDistributionRule skuRule : skuDistributionRules){
//	        		skuRule.setWorkshopId(mworkshopId);
////	        		skuRule.setPartnerId(Long.parseLong(partnerIds[i]));
//	        		workshopDistributionRuleBizServiceImpl.saveDistributionRule(skuRule);
//	        	}
//	        }
//			String[] partnerIds = mWorkshopVo.getPartnerIds().split(",");
//			List<WorkshopPartnerVo> wpvList  = new ArrayList<WorkshopPartnerVo>();
//			for(int i=0;i<partnerIds.length;i++){
//				WorkshopPartnerVo wpv = new WorkshopPartnerVo();
//				wpv.setPartnerId(Long.parseLong(partnerIds[i]));
//				wpv.setWorkshopId(mworkshopId);
//				wpv.setWorkshopName(mWorkshopVo.getWorkShopName());
//				wpv.setRelationType(WorkshopPartnerVo.REGIONTYPE);
//				wpv.setCreateTime(new Date());
//				wpv.setCreator(ContextUtil.getCurUser().getChName());
//				wpvList.add(wpv);
//			}
//			workshopPartnerServiceImpl.batchInsertWorkShopPartner(wpvList,false);//modify by bo.liu 1123 true to false
//			resultMap.put("workshopId",mworkshopId);
//
//
//
//
//		}catch(Exception ex){
//			//手动触发回滚
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			ex.printStackTrace();
//			resultMap.put("code", "error");
//		}
//		return resultMap;
//	}

//	@Override
//	public Map<String, Object> getWorkshopByRegionId(String regionId,String mWworkshopStatus,String mWorkshopName,String mWorkshopFromSource) {
//
//		Map<String,Object> resultMap = new HashMap<String,Object>();
//		Map<String,Object> reqMap = new HashMap<String,Object>();
//		resultMap.put("code", "success");
//		WxTUser wxUser = ContextUtil.getCurUser();
//		try{
//			//获取是否雪弗龙或管理员用户
//			Long userType = wxUser.getmUserTypes();
//			Long userOrgId = wxUser.getOrgId();
//			reqMap.put("userType", userType);
//			reqMap.put("userOrgId", userOrgId);
//
//			if(mWworkshopStatus.equals("-1")){
//				mWworkshopStatus = null;
//			}
//
//			if(mWorkshopFromSource.equals("-1")){
//				mWorkshopFromSource = null;
//			}
//
//			reqMap.put("mWworkshopStatus", mWworkshopStatus);
//			reqMap.put("mWorkshopName", mWorkshopName);
//			reqMap.put("mWorkshopFromSource", mWorkshopFromSource);
//
//			//查询所有区域
//			if(null==regionId || regionId.equals("")){
//				reqMap.put("regionType", 1);//查询所有区域
//			}else{
//				reqMap.put("regionType", 0);//单个区域
//				reqMap.put("regionId", regionId);//区域id
//			}
//			reqMap.put("queryType","region");
//
//			List<WorkshopMaster> workshopLst =  workShopVoMapper.getWorkshopsByRegionId(reqMap);
//			resultMap.put("workshopLst", workshopLst);
//
//		}catch(Exception ex){
//			ex.printStackTrace();
//			resultMap.put("code", "error");
//		}
//		return resultMap;
//	}

//	@Override
//	public Map<String, Object> getExcuteUsersByPartners(String partnerIds) {
//		Map<String,Object> resultMap = new HashMap<String,Object>();
//		Map<String,Object> reqMap = new HashMap<String,Object>();
//		resultMap.put("code", "success");
//		try
//		{
//			//0.解析合伙人信息
//			if(null==partnerIds || partnerIds.equals(""))
//			{
//				resultMap.put("code", "isnull");
//				return resultMap;
//			}
//			//1.解析合伙人信息
//			String[] partners = partnerIds.split(",");
//			Long[] mPartnerIds = new Long[partners.length];
//			for(int i=0;i<partners.length;i++)
//			{
//				mPartnerIds[i] = Long.parseLong(partners[i]);
//			}
//			if(mPartnerIds.length==0)
//			{
//				mPartnerIds = null;
//			}
//			reqMap.put("mPartnerIds",mPartnerIds);
//			//2.获取合伙人内对应的所有执行人
//			List<WxTUser> lstExcuteUsers = wxUserService.getUsersByPartnerIdsForWorkshopUpdate(reqMap);
//			resultMap.put("lstExcuteUsers", lstExcuteUsers);
//
//
//		}catch(Exception ex)
//		{
//			ex.printStackTrace();
//			resultMap.put("code", "error");
//		}
//
//		return resultMap;
//	}
	//end by bo.liu 0815

//	@Override
//	public List<WorkshopMaster> getWorkShopByCode(String workShopCode) {
//		WorkShopVoExample example  = new  WorkShopVoExample();
//		Criteria criteria = example.createCriteria();
//		criteria.andWorkShopCodeEqualTo(workShopCode);
//		List<WorkshopMaster> list = workShopVoMapper.selectByExample(example);
//		return list;
//	}

	/**
	 * 创建店主接口入口
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String,Object> createShopManager(String phoneNo,String name,long workshopId){
		WxTUser wtu  = new WxTUser();
		Map<String,Object> map = validateInData(phoneNo,workshopId, wtu);
		if(map.get("result").equals("fail")){
			return map;
		}

		try{
			WxTRole role =  roleBizService.selectRoleByRoleName(Constants.WORKSHOP_MANAGER_CODE);
			WxTMenu wm  = wxMenuServiceImpl.selectMenuByMenuName(Constants.WORKSHOP_WRITEOFF_MENU_NAME);
			if(null==role){
				map.put("result", "fail");
				map.put("msg", "未找到对应的角色!"+Constants.WORKSHOP_MANAGER_CODE);
				return map;
			}
			if(null == wm){
				map.put("result", "fail");
				map.put("msg", "未找到对应的菜单!"+Constants.WORKSHOP_WRITEOFF_MENU_NAME);
				return map;
			}

			wtu.setLoginName(phoneNo);
			wtu.setUserNo(phoneNo);
			wtu.setChName(name);
			wtu.setIsValid("1");
			wtu.setStatus(1);
			wtu.setAllowLogin("T");
			wtu.setType("1");

			String salt = SaltUtil.getSalt();
			wtu.setSalt(salt);

			//String randomPassword = RandomUtil.generateString(6); //delete by bo.liu
			String randomPassword = phoneNo.substring(5);
			byte[] hashPassword = Digests.sha1(randomPassword.getBytes(),	Encodes.decodeHex(salt), Constants.HASH_INTERATIONS);
			String pwd = Encodes.encodeHex(hashPassword);
			wtu.setPassword(pwd);
			wtu.setPwdLasttime(new Date());
			wtu.setXgTime(new Date());
			wtu.setXgUser(ContextUtil.getCurUser().getLoginName());
			wtu.setXzTime(new Date());
			wtu.setXzUser(ContextUtil.getCurUser().getLoginName());

			map = wxUserService.createUser(wtu);
			Long userId  = (Long) map.get("userid");
			WxTUserrole userRole = new WxTUserrole();
			userRole.setRoleId(role.getRoleId());
			userRole.setUserId(userId);
			userRole.setGrantUserid(1L);
			userRole.setStatus(1);
			userRole.setXgSj(new Date());
			userRole.setXgUser(1L);
			userRole.setTenantId(1L);

			WxTUserbase  userbase = new WxTUserbase();
			userbase.setUserId(userId);
			userbase.setSourceId(wm.getMenuId());
			userbase.setRsType(2);
			userbase.setAddFlag("1");
			userbase.setXgUser(1L);
			userbase.setStatus(1);
			userbase.setTenantId(1L);

			WxTUserbase  userbase2 = new WxTUserbase();
			userbase2.setUserId(userId);
			userbase2.setSourceId(wm.getMenuPid());
			userbase2.setRsType(2);
			userbase2.setAddFlag("1");
			userbase2.setXgUser(1L);
			userbase2.setStatus(1);
			userbase2.setTenantId(1L);

			wxUserRoleServiceImpl.createUserRole(userRole);
			wxUserBaseServiceImpl.createUserBase(userbase);
			wxUserBaseServiceImpl.createUserBase(userbase2);

			map.put("result", "success");
			map.put("msg", "创建成功");
			map.put("password", randomPassword);
		}catch(Exception e){
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			map.put("result", "fail");
			map.put("msg", "出现异常!"+e.getMessage());
		}
		return map;
	}
	@Override
	public Map<String, Object> createShopMechanic(String phoneNo, String name,
			long workshopId) {
		WxTUser wtu  = new WxTUser();
		Map<String,Object> map = validateInData(phoneNo,workshopId, wtu);
		if(map.get("result").equals("fail")){
			return map;
		}

		try{
			WxTRole role =  roleBizService.selectRoleByRoleName(Constants.WORKSHOP_MECHANIC_CODE);
			WxTMenu wm  = wxMenuServiceImpl.selectMenuByMenuName(Constants.WORKSHOP_WRITEOFF_MENU_NAME);//后续可能考虑到新增一个技师核销菜单
			if(null==role){
				map.put("result", "fail");
				map.put("msg", "未找到对应的角色!"+Constants.WORKSHOP_MECHANIC_CODE);
				return map;
			}
			if(null == wm){
				map.put("result", "fail");
				map.put("msg", "未找到对应的菜单!"+Constants.WORKSHOP_WRITEOFF_MENU_NAME);
				return map;
			}


			wtu.setLoginName(phoneNo);
			wtu.setUserNo(phoneNo);
			wtu.setChName(name);
			wtu.setIsValid("1");
			wtu.setStatus(1);
			wtu.setAllowLogin("T");
			wtu.setType("1");

			String salt = SaltUtil.getSalt();
			wtu.setSalt(salt);

			//String randomPassword = RandomUtil.generateString(6);
			String randomPassword = phoneNo.substring(5);
			byte[] hashPassword = Digests.sha1(randomPassword.getBytes(),	Encodes.decodeHex(salt), Constants.HASH_INTERATIONS);
			String pwd = Encodes.encodeHex(hashPassword);
			wtu.setPassword(pwd);
			wtu.setPwdLasttime(new Date());
			wtu.setXgTime(new Date());
			wtu.setXgUser(ContextUtil.getCurUser().getLoginName());
			wtu.setXzTime(new Date());
			wtu.setXzUser(ContextUtil.getCurUser().getLoginName());

			map = wxUserService.createUser(wtu);
			Long userId  = (Long) map.get("userid");
			WxTUserrole userRole = new WxTUserrole();
			userRole.setRoleId(role.getRoleId());
			userRole.setUserId(userId);
			userRole.setGrantUserid(1L);
			userRole.setStatus(1);
			userRole.setXgSj(new Date());
			userRole.setXgUser(1L);
			userRole.setTenantId(1L);

			WxTUserbase  userbase = new WxTUserbase();
			userbase.setUserId(userId);
			userbase.setSourceId(wm.getMenuId());
			userbase.setRsType(2);
			userbase.setAddFlag("1");
			userbase.setXgUser(1L);
			userbase.setStatus(1);
			userbase.setTenantId(1L);

			WxTUserbase  userbase2 = new WxTUserbase();
			userbase2.setUserId(userId);
			userbase2.setSourceId(wm.getMenuPid());
			userbase2.setRsType(2);
			userbase2.setAddFlag("1");
			userbase2.setXgUser(1L);
			userbase2.setStatus(1);
			userbase2.setTenantId(1L);

			wxUserRoleServiceImpl.createUserRole(userRole);
			//菜单暂时不用创建 modify by bo.liu 20170216
			//wxUserBaseServiceImpl.createUserBase(userbase);
			//wxUserBaseServiceImpl.createUserBase(userbase2);

			map.put("result", "success");
			map.put("msg", "创建成功");
			map.put("password", randomPassword);
		}catch(Exception e){
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			map.put("result", "fail");
			map.put("msg", "出现异常!"+e.getMessage());
		}
		return map;
	}


/**
 * 校验输入的数据
 * @param phoneNo
 * @param workshopId
 * @param wtu
 * @return
 */
	private Map<String, Object> validateInData(String phoneNo, long workshopId, WxTUser wtu) {
		Map<String, Object> map = new HashMap<String,Object>();
		try {
			List<WxTUser>  userList = wxUserService.findUserByLoginName(phoneNo);
			if(null!= userList && userList.size()>0){
				map.put("result", "fail");
				map.put("msg", "该用户已存在!" + phoneNo);
				return map;
			}
			WorkshopMaster wsv = workshopMasterBizService.getBean(workshopId);
			if(null==wsv){
				map.put("result", "fail");
				map.put("msg", " 找不到对应的门店信息!" + workshopId);
				return map;
			}
			wtu.setOrgId(wsv.getId());
			map.put("result", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("result", "fail");
			map.put("msg", "查找组织信息出现异常!");
		}
		return map;
	}


	public Map<String, Object> queryMechanicListByWorkshopId(Long workshopId){
		Map<String, Object> resultMap = new HashMap<String, Object>();

		try {
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("workshopId", workshopId);

			List<WorkshopEmployee> mechainVoLst = workshopEmployeeMapper.getMechanicByWorkshopId(paramMap);
			resultMap.put("code", "success");
			resultMap.put("data", mechainVoLst);
		} catch (Exception e) {
			resultMap.put("code", "error");
			resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
		}
		return resultMap;
	}

	@Override
	public WorkshopMaster selectByPrimaryKey(Long id) {
		try {
			return workshopMasterBizService.getBean(id);
		} catch (WxPltException e) {
			throw new RuntimeException(e);
		}
	}

//	@Override
//	public Map<String, Object> getWorkshopWithCurrentUser() {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try {
//			WorkshopMaster workshop = workShopVoMapper.getWorkShopByUser(ContextUtil.getCurUserId());
//			if (workshop == null){
//				throw new WxPltException(MessageResourceUtil.getMessage("user.is_not_workshop_employee"));
//			}
//			resultMap.put("code", "success");
//			resultMap.put("workshop", workshop);
//		} catch (WxPltException e) {
//			resultMap.put("code", "error");
//			resultMap.put("codeMsg", e.getExpMsg());
//		} catch (Exception e) {
//			log.error("get current user's workshop info exception", e);
//			resultMap.put("code", "error");
//			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
//		}
//
//		return resultMap;
//	}

	@Override
	public Map<String, Object> queryWorkshopListWithEmployeeExist(Long partnerId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (partnerId == null){
				partnerId = ContextUtil.getCurUser().getOrgId();
			}
			Map<String, Object> params = new HashMap<String, Object>(5);
			params.put("partnerId", partnerId);
			params.put("existsEmployee", true);
			params.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
			List<WorkshopMaster> workshopVoList = workshopMasterBizService.querySimpleByParams(params);
			resultMap.put("code", "success");
			resultMap.put("data", workshopVoList);
		} catch (Exception e) {
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}

		return resultMap;
	}

//	@Override
//	public Map<String, Object> queryWorkshopListWithVerificationExist(Long partnerId) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try {
//			if (partnerId == null){
//				partnerId = ContextUtil.getCurUser().getOrgId();
//			}
//			List<WorkshopMaster> workshopVoList = workshopBizService.queryWorkshopListWithVerificationExist(partnerId);
//			resultMap.put("code", "success");
//			resultMap.put("data", workshopVoList);
//		} catch (Exception e) {
//			resultMap.put("code", "error");
//			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
//		}
//
//		return resultMap;
//	}
//	@SuppressWarnings("unchecked")
//	@Override
//	public Map<String, Object> importWorkshopBatchBat(Workbook wb) {
//		WorkshopMaster workShopVo = new WorkshopMaster();
//		List<WorkshopMaster> lstWorkShopVo = new ArrayList<WorkshopMaster>();
//		List<Map<String, Object>> errorList = new ArrayList<Map<String, Object>>();
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try { // 0.解析数据.
//			Map<String, Object> responseMap = ImportDataUtil
//					.getImportDataByReflect(wb, workShopVo,
//							WORKSHOP);
//			// 1. 判断结果是否正确.
//			String resultCode = (String) responseMap.get("result");
//			if (resultCode.equals("dataInvalid")) {
//				resultMap.put("code", "error");
//				return resultMap;
//			}
//			// 2.返回结果正确，继续执行.
//			lstWorkShopVo = (List<WorkshopMaster>) responseMap.get("datalst");
//			// 3.对返回结果进行分页处理
//			ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
//					lstWorkShopVo, ImportDataUtil.MAX_BATCH_ACCOUNT);
//			int toltalPage = newPage.getTotalPages();
//			//4.通过循环把数据插入数据库中
//			for (int i = 1; i <= toltalPage; i++) {
//				List<WorkshopMaster> volst = newPage.getObjects(i);
//				workShopVoMapper.insertWorkShopBatch(volst);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			Map<String, Object> error = new HashMap<String, Object>();
//			error.put("error", "未知错误");
//			errorList.add(error);
//		}
//		int uploadDataAccount = lstWorkShopVo.size();
//		resultMap.put("code", "success");
//
//		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
//		return resultMap;
//	}
//
//	@SuppressWarnings("unchecked")
//	@Override
//	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
//	public Map<String, Object> importDataForWorkShopBatchContainRelation(
//			Workbook wb,boolean isRelation){//,String status)//delete by bo.liu  Long PartnerId, String PartnerName) {
//		WorkshopMaster workShopVo = new WorkshopMaster();
//		List<WorkshopMaster> lstWorkShopVo = new ArrayList<WorkshopMaster>();
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		try { // 0.解析数据.
//			Map<String, Object> responseMap = ImportDataUtil
//					.getImportDataByReflect(wb, workShopVo,
//							WORKSHOP);
//			// 1. 判断结果是否正确.
//			String resultCode = (String) responseMap.get("result");
//			if (resultCode.equals("dataInvalid")) {
//				resultMap.put("code", "error");
//				return resultMap;
//			}
//			// 2.返回结果正确，继续执行.
//			lstWorkShopVo = (List<WorkshopMaster>) responseMap.get("datalst");
//			// 3.对返回结果进行分页处理
//			ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
//					lstWorkShopVo, ImportDataUtil.MAX_BATCH_ACCOUNT);
//			int toltalPage = newPage.getTotalPages();
//			//4.通过循环把数据插入数据库中
//			List<WorkshopMaster> tempLst = newPage.getObjects(1);
//			String status = tempLst.get(0).getStatus();
//			Long PartnerId = Long.parseLong(tempLst.get(0).getPartnerId());
//			String PartnerName = tempLst.get(0).getPartnerName();
//			for (int i = 1; i <= toltalPage; i++) {
//				List<WorkshopMaster> volst = newPage.getObjects(i);
//				workShopVoMapper.insertWorkShopBatchNew(volst);
//			}
//
//
//			//5.插入门店与partner的关联表  将门店插入到机构表中
//			if(isRelation)
//			{
//				Map<String,Object> reqMap  = new HashMap<String,Object>();
//				reqMap.put("status", status);//查找状态为导入前的状态（即刚刚导入门店的状态）
//				List<WorkshopMaster> lstWorkShop = workShopVoMapper.getWorkshopsByStatus(reqMap);
//				List<WorkshopPartnerVo> lstWorkshopPartner = new ArrayList<WorkshopPartnerVo>();
//				for(WorkshopMaster workshopVo: lstWorkShop)
//				{
//					WorkshopPartnerVo workshopPartnerVo = new WorkshopPartnerVo();
//					workshopPartnerVo.setWorkshopId(workshopVo.getId());
//					workshopPartnerVo.setWorkshopName(workshopVo.getWorkShopName());
//					workshopPartnerVo.setPartnerId(PartnerId);
//					workshopPartnerVo.setPartnerName(PartnerName);
//					workshopPartnerVo.setRelationType("trade");
//					workshopPartnerVo.setCreateTime(new Date());
//					lstWorkshopPartner.add(workshopPartnerVo);
//				}
//				ImportDataPageModelUtil newworkshopPartnerVoPage = new ImportDataPageModelUtil(
//						lstWorkshopPartner, ImportDataUtil.MAX_BATCH_ACCOUNT);
//				int workshopPartnerVotoltalPage = newPage.getTotalPages();
//				for (int i = 1; i <= workshopPartnerVotoltalPage; i++) {
//					List<WorkshopPartnerVo> workshopPartnerlst = newworkshopPartnerVoPage.getObjects(i);
//					workshopPartnerServiceImpl.batchInsertWorkShopPartner(workshopPartnerlst,false);//modify by bo.liu 1123 true to false
//				}
//				//重置门店状态为-1，待扫店状态
//				ImportDataPageModelUtil updateWorkShopPage = new ImportDataPageModelUtil(
//						lstWorkShop, ImportDataUtil.MAX_BATCH_ACCOUNT);
//				int updateWorkshopTotalPage = updateWorkShopPage.getTotalPages();
//				for (int i = 1; i <= updateWorkshopTotalPage; i++) {
//					List<WorkshopMaster> lstUpdateWorkShop = updateWorkShopPage.getObjects(i);
//					reqMap.put("lstUpdateWorkShop", lstUpdateWorkShop);
//					reqMap.put("status", WorkshopMaster.WORKSHOP_STATUS_1);//重置状态为-1
//					workShopVoMapper.updateWorkShopsByLst(reqMap);
//				}
//
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			//手动触发回滚
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//			//Map<String, Object> error = new HashMap<String, Object>();
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg","异常错误信息："+e.getMessage());
//			return resultMap;
//			//errorList.add(error);
//		}
//		int uploadDataAccount = lstWorkShopVo.size();
//		resultMap.put("code", "success");
//		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
//		return resultMap;
//	}

//	@Override
//	public List<WorkshopMaster> selectByWorkShopName(String workShopCode,String workShopName) {
//
//		WorkShopVoExample example = new WorkShopVoExample();
//		Criteria criteria = example.createCriteria();
//		criteria.andWorkShopCodeEqualTo(workShopCode);
//		criteria.andWorkShopNameEqualTo(workShopName);
//		return workShopVoMapper.selectByExample(example);
//
//	}
//
	@Override
	public Map<String, Object> getActiveWorkshopsByPartner(Long partnerId) {
		WxTUser user = ContextUtil.getCurUser();
		if(user.getmUserTypes() == 1 && partnerId == null){
			throw new RuntimeException("所属合伙人不能为空");
		}
		if(partnerId == null){
			partnerId = user.getOrgId();
		}
		Map<String, Object> map = new HashMap<String, Object>(2);
		Map<String, Object> reqMap = new HashMap<String, Object>();
		reqMap.put("partnerId", partnerId);
		reqMap.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
		reqMap.put("status", "3");
		try {
			map.put("data", workshopMasterBizService.querySimpleByParams(reqMap));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> getVerificationRule(String partnerIds) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		map.put("success", true);
		VerificationRule verificationRule = null;
		if(partnerIds != null && !"".equals(partnerIds)){
			String[] ids = partnerIds.split(",");
			int i = 0;
			Long partnerId = null;
			try {
				do{
					if(!"".equals(ids[i])){
						partnerId = Long.parseLong(ids[i]);
						verificationRule = verificationRuleBizServiceImpl.getVerificationRuleByPartnerId(partnerId);
					}
				}while((verificationRule == null || verificationRule.getPerLiterReward() == null) && ++i < ids.length);
				if(partnerId != null){
					map.put("skuDistributionRules", workshopDistributionRuleBizServiceImpl.querySkuDistributionRules(partnerId));
				}
			} catch (Exception e) {
				e.printStackTrace();
				map.put("success", false);
			}
		}
		map.put("data", (verificationRule != null && verificationRule.getPerLiterReward() == null) ? null : verificationRule);
		return map;
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> createMechanicUser() {
		Map<String,Object> resutlMap = new HashMap<String,Object>();
		try
		{

			WxTRole role =  roleBizService.selectRoleByRoleName(Constants.WORKSHOP_MECHANIC_CODE);
			WxTMenu wm  = wxMenuServiceImpl.selectMenuByMenuName(Constants.WORKSHOP_WRITEOFF_MENU_NAME);//后续可能考虑到新增一个技师核销菜单
			if(null==role){
				resutlMap.put("code", "error");
				resutlMap.put("msg", "未找到对应的角色!"+Constants.WORKSHOP_MECHANIC_CODE);
				return resutlMap;
			}
			if(null == wm){
				resutlMap.put("code", "error");
				resutlMap.put("msg", "未找到对应的菜单!"+Constants.WORKSHOP_WRITEOFF_MENU_NAME);
				return resutlMap;
			}
		//0.获取技师，且没有在用户表中的注册的
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("type", "Mechanic");
			List<WorkshopEmployee> lstWorkshopEmployee = workshopEmployeeMapper.selectByEmployeeType(reqMap);

			List<String> lstUserLoginName = new ArrayList<String>();

			List<WxTUser> lstUsers = new ArrayList<WxTUser>();
			for(WorkshopEmployee employee : lstWorkshopEmployee)
			{
				String phoneNo = employee.getMobile();
				Long orgId = employee.getWorkshopId();
				String name =  employee.getName();
				WxTUser wtu = new WxTUser();
				wtu.setLoginName(phoneNo);
				wtu.setOrgId(orgId);
				wtu.setUserNo(phoneNo);
				wtu.setChName(name);
				wtu.setIsValid("1");
				wtu.setStatus(1);
				wtu.setAllowLogin("T");
				wtu.setType("1");

				String salt = SaltUtil.getSalt();
				wtu.setSalt(salt);

				String randomPassword = phoneNo.substring(5);
				byte[] hashPassword = Digests.sha1(randomPassword.getBytes(),	Encodes.decodeHex(salt), Constants.HASH_INTERATIONS);
				String pwd = Encodes.encodeHex(hashPassword);
				wtu.setPassword(pwd);
				wtu.setPwdLasttime(new Date());
				wtu.setXgTime(new Date());
				wtu.setXgUser(ContextUtil.getCurUser().getLoginName());
				wtu.setXzTime(new Date());
				wtu.setXzUser(ContextUtil.getCurUser().getLoginName());
				lstUsers.add(wtu);
				lstUserLoginName.add(phoneNo);
				System.out.println("createMechanicUser--phoneNo-----------:"+phoneNo);
			}

		if(null==lstUsers ||lstUsers.isEmpty())
		{
			resutlMap.put("code", "error");
			resutlMap.put("msg", "未有技师用户需要录入");
			return resutlMap;
		}

		//1.批量录入到用户表
			ImportDataPageModelUtil allPage = new ImportDataPageModelUtil(
					lstUsers, 80);
			int userpageaccount = allPage.getTotalPages();
			for (int i = 1; i <= userpageaccount; i++) {
				List<WxTUser> lstuser = allPage
						.getObjects(i);
				wxUserService.batchInsertUser(lstuser);
			}
		//2.批量录入用户角色表
			//根据录入的用户，筛选，然后获取userid
			List<WxTUser> mechainUserLst = new ArrayList<WxTUser>();
			ImportDataPageModelUtil allPage1 = new ImportDataPageModelUtil(
					lstUserLoginName, 80);
			int mobileaccount = allPage1.getTotalPages();
			for (int i = 1; i <= mobileaccount; i++) {
				List<String> lstmobiles= allPage1
						.getObjects(i);
				List<WxTUser> lstTemp = wxUserService.getMerchainUserByMobiles(lstmobiles);
				mechainUserLst.addAll(lstTemp);
			}

			//要批量录入用户角色关联list
			List<WxTUserrole> lstUserroles = new ArrayList<WxTUserrole>();
			for(WxTUser wxUser: mechainUserLst)
			{
				WxTUserrole userRole = new WxTUserrole();
				userRole.setRoleId(role.getRoleId());
				userRole.setUserId(wxUser.getUserId());
				userRole.setGrantUserid(1L);
				userRole.setStatus(1);
				userRole.setXgSj(new Date());
				userRole.setXgUser(1L);
				userRole.setTenantId(1L);
				lstUserroles.add(userRole);
			}

			ImportDataPageModelUtil userrolePage = new ImportDataPageModelUtil(
					lstUserroles, 80);
			int userroleaccount = userrolePage.getTotalPages();
			for (int i = 1; i <= userroleaccount; i++) {
				List<WxTUserrole> lstuserroleTemps= userrolePage
						.getObjects(i);
				wxUserRoleServiceImpl.batchInsert(lstuserroleTemps);
			}
			resutlMap.put("code", "success");
			resutlMap.put("msg", "成功");

		}catch (Exception e) {
			//手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resutlMap.put("code", "error");
			resutlMap.put("msg", e.getMessage());
		}

		return resutlMap;
	}

	@Override
	public Map<String, Object> queryWorkshopForPage(WorkshopMasterParams params) {
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			// 组装登录用户信息参数
			WxTUser curUser = ContextUtil.getCurUser();
			if(!WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel())) {
				params.setPartnerId(curUser.getOrgId());
			}

			if(params.getExecuteUserId() == null && curUser.isPartnerDB()){
				String bdWorkshopPowerTag = partnerRolePowerService.getBdWorkshopPower(curUser.getOrgId());
				//巡店时根据BD开关来判断是否查看所有门店:0 表示关：需要过滤
				if( "0".equals(bdWorkshopPowerTag)){
					params.setExecuteUserId(curUser.getUserId());
				}
			}
			List<WorkshopMaster> rows = workshopMasterBizService.querySimpleForPage(params);
//			if(WxTUser.USER_MODEL_CHEVRON.equals(curUser.getUserModel()) && rows != null){
//				for(WorkshopMaster ws : rows){
//					List<OrganizationVo> ps = organizationServiceImpl.getAllPartnersByWorkshop(ws.getId());
//					if(ps != null){
//						String pNames = null;
//						for(OrganizationVo sp : ps){
//							if(pNames == null){
//								pNames = sp.getOrganizationName();
//							}else{
//								pNames += ("," + sp.getOrganizationName());
//							}
//						}
//						ws.setPartnerName(pNames);
//					}
//				}
//			}
			resultMap.put(Constants.RESULT_LST_KEY, rows);
			resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

	@Override
	public JsonResponse queryWorkshop(WorkshopMasterParams params) {
		JsonResponse map = new JsonResponse();
		log.info("queryWorkshop: " + params == null ? null : JsonUtil.writeValue(params));
		try {
			if(params.getStatus() == null && params.getAmStatus() == null) {
				params.setStatus("3");
			}
			List<WorkshopMaster> rows = workshopMasterBizService.querySimpleForPage(params);
			map.put(Constants.RESULT_LST_KEY, rows);
			map.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(),
					"com.chevron.pms.service.impl.WorkShopServiceImpl.queryWorkshop", params == null ? null : JsonUtil.writeValue(params));
		}
		return map;
	}
	
	@Override
	public JsonResponse queryWorkshopForHCB(WorkshopMasterParams params) {
		JsonResponse map = new JsonResponse();
		log.info("queryWorkshop: " + params == null ? null : JsonUtil.writeValue(params));
		try {
			if(params.getStatus() == null && params.getAmStatus() == null) {
				params.setStatus("3");
			}
			DicItemVo dicItem =  dicService.getDictItem("hcb_workshop","hcb_workshop_distancerange");
			if(dicItem != null ){
				Integer distanceRange = Integer.valueOf(dicItem.getDicItemName());
				params.setDistanceRange(distanceRange);
			}else{
				params.setDistanceRange(null);
			}
			DicItemVo dicItemPartner =  dicService.getDictItem("hcb_workshop","hcb_workshop_for_partner");
			if(dicItemPartner != null ){
				String partnerIdsString = dicItemPartner.getDicItemName();
				if(StringUtils.isNotEmpty(partnerIdsString)){
					List<Long> partnerIdList = new ArrayList<Long>();
					String[] partnerIds = partnerIdsString.split(",");
					for(int i = 0; i < partnerIds.length; i++){
						partnerIdList.add(Long.valueOf(partnerIds[i]));
					}				
					params.setPartnerIds(partnerIdList);
				}
			}else{
				params.setDistanceRange(null);
			}
			params.setPaging(true);
			List<WorkshopMaster> rows = workshopMasterBizService.querySimpleForPage(params);
			map.put(Constants.RESULT_LST_KEY, rows);
			map.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(),
					"com.chevron.pms.service.impl.WorkShopServiceImpl.queryWorkshop", params == null ? null : JsonUtil.writeValue(params));
		}
		return map;
	}

	@Override
	public JsonResponse getWorkshopDetail(Long workshopId) {
		JsonResponse map = new JsonResponse();
		log.info("getWorkshopDetail: " + workshopId);
		try {
			map.setDataResult(workshopMasterBizService.getBean(workshopId));
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(),
					"com.chevron.pms.service.impl.WorkShopServiceImpl.getWorkshopDetail", workshopId == null ? null : workshopId.toString());
		}
		return map;
	}

	@Override
	public Map<String, Object> getWorkshopInventoryDetail(Long workshopId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			InventoryQueryParams params = new InventoryQueryParams();
			params.setPaging(false);
			params.setWorkshopId(workshopId);
			map.put(Constants.RESULT_LST_KEY, inventoryVoMapper.selectInventory(params));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			map.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getWorkshopInfoForStoreLocator(Long workshopId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put("data", workshopMasterBizService.getBean(workshopId));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public void synEtoWorkShops(String isFall) throws WxPltException{
		WorkshopMasterExample example = new WorkshopMasterExample();
		//合格门店且未删除
		if("no".equals(isFall)){
			//未同步的
			example.createCriteria().andStatusEqualTo(3).andDeleteFlagEqualTo(0).andExtProperty23IsNull();
		}else {
			example.createCriteria().andStatusEqualTo(3).andDeleteFlagEqualTo(0);
		}
		List<WorkshopMaster> workshopMasters = workshopMasterBizService.queryByExample(example);
		etoBizService.synEtoWorkShops(workshopMasters);
	}

}
