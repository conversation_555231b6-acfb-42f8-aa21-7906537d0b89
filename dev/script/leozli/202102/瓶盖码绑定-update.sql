insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('BindCapQrcode.time', '绑定瓶盖码时间设置', '绑定瓶盖码时间设置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('BindCapQrcode.time', 'startDate', '2021-01-01', '开始日期', '1', 10);

CREATE INDEX ix_cap_code_detail_short_url ON wx_t_cap_code_detail (short_url);
ALTER TABLE [wx_t_cap_code_detail] ADD CONSTRAINT uk_wx_t_cap_code_detail UNIQUE (qr_code);

