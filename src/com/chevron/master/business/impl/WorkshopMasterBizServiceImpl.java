package com.chevron.master.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.dao.WorkshopMasterMapper;
import com.chevron.master.model.ReNameWorkShopVo;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.master.model.WorkshopMasterExample;
import com.chevron.master.model.WorkshopMasterParams;
import com.chevron.mdm.service.MdmService;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.business.WorkshopStatusBizService;
import com.chevron.pms.dao.WorkshopPartnerVoMapper;
import com.chevron.pms.model.WorkshopPartnerVo;
import com.chevron.pms.model.WorkshopStatusVo;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.*;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.EtoBizService;
import com.sys.utils.model.AsynProcessStatus;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import java.io.File;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 门店业务接口实现类
 *
 * <AUTHOR>
 * @version 1.0 2019-07-08 15:02
 */
@Service
public class WorkshopMasterBizServiceImpl implements WorkshopMasterBizService {
    @Autowired
    private WorkshopMasterMapper workshopMasterMapper;

    @Resource
    WorkshopStatusBizService workshopStatusBizService;

    @Resource
    WorkshopPartnerVoMapper workshopPartnerVoMapper;

    @Autowired
    private WorkshopEmployeeBizService workshopEmpBizService;

    @Autowired
    private DicService dicService;

    @Autowired
    private EtoBizService etoBizService;

    @Autowired
    private MdmService mdmService;

    private final static Logger log = Logger.getLogger(WorkshopMasterBizServiceImpl.class);

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insert(WorkshopMaster record) throws WxPltException {
        if (record.getPartnerId() == null) {
            throw new WxPltException("新增门店未关联经销商");
        }
        if (record.getExtFlag() == null) {
            record.setExtFlag(0);
        }
        if (record.getNewExtFlag() != null) {
            record.setExtFlag(record.getExtFlag() | record.getNewExtFlag());
        }
        if (record.getExtFlag2() == null) {
            record.setExtFlag2(0);
        }
        if (record.getNewExtFlag2() != null) {
            record.setExtFlag(record.getExtFlag2() | record.getNewExtFlag2());
        }
        if (record.getExtFlag3() == null) {
            record.setExtFlag3(0);
        }
        if (record.getNewExtFlag3() != null) {
            record.setExtFlag(record.getExtFlag3() | record.getNewExtFlag3());
        }
        Long user = ContextUtil.getCurUserId();
        Date now = DateUtil.getCurrentDate();
        record.setDeleteFlag(0);
        record.setCreateTime(now);
        record.setCreator(user.toString());

        int customerNum = (int) (Math.log(record.getCustomerType().doubleValue()) / Math.log(2)) + 1;
        String dateStr = customerNum + "" + DateUtils.getDateStr(new Date(), "yyyyMMddHHmm") + CommonUtil.generateSequenceCode("WorkshopMasterBizServiceImpl.insert", 6);
        record.setCustomerCode(dateStr);
        workshopMasterMapper.insertSelective(record);
        WorkshopPartnerVo workshopPartnerVo = new WorkshopPartnerVo();
        workshopPartnerVo.setPartnerId(record.getPartnerId());
        workshopPartnerVo.setWorkshopId(record.getId());
        workshopPartnerVo.setWorkshopName(record.getWorkshopName());
        workshopPartnerVo.setRelationType(WorkshopPartnerVo.REGIONTYPE);
        workshopPartnerVo.setCreateTime(new Date());
        workshopPartnerVo.setCreator(user.toString());
        workshopPartnerVoMapper.insertSelective(workshopPartnerVo);

        //门店信息同步到MDM
        try {
            //查出来再去同步
            WorkshopMaster workshopMaster = workshopMasterMapper.selectByPrimaryKey(record.getId());
            mdmService.syncWorkShopToMdm(workshopMaster);
        } catch(Exception e) {
            log.error("门店信息同步到MDM异常", e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateExtFlag(WorkshopMaster req, Date currentTime) {
        WorkshopMaster record = BeanUtil.toBean(req, WorkshopMaster.class);

        record.setUpdateTime(currentTime);
        record.setUpdateUserId(ContextUtil.getCurUserId());
        if (record.getExtFlag() != null) {
            if (record.getExtFlag() == 19) {
                record.setNewExtFlag(524288); // 中超店招店
                record.setExtFlag(null);
            } else if (record.getExtFlag() == 13) {
                record.setNewExtFlag(8192); // 蓝盾合作门店
                record.setExtFlag(null);
            } else if (record.getExtFlag() == 20) {
                record.setNewExtFlag(null); // 国六节油养护站（原CK合作门店）
                record.setExtFlag(null);
                record.setJoinCkPlan(1);
            } else if (record.getExtFlag() == 21) {
                record.setNewExtFlag(2097152); // 超低灰门店
                record.setExtFlag(null);
            }
        }
        workshopMasterMapper.updateByPrimaryKeySelective(record);

        //门店信息同步到MDM
        try {
            //查出来再去同步
            WorkshopMaster workshopMaster = workshopMasterMapper.selectByPrimaryKey(record.getId());
            mdmService.syncWorkShopToMdm(workshopMaster);
        } catch(Exception e) {
            log.error("门店信息同步到MDM异常", e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void update(WorkshopMaster record) throws WxPltException {
        record.setUpdateTime(DateUtil.getCurrentDate());
        if(null == record.getUpdateUserId()){
            record.setUpdateUserId(ContextUtil.getCurUserId());
        }
//		if(null != record.getBusinessWeight() && (record.getBusinessWeight()&16) > 0 && null != record.getProvinceId()) {
//			record.setRegionId(record.getProvinceId());
//		}
        if (record.getExtFlag() != null) {
            if (record.getNewExtFlag() != null) {
                record.setExtFlag(record.getExtFlag() | record.getNewExtFlag());
                record.setNewExtFlag(null);
            }
            if (record.getRemovedExtFlag() != null) {
                record.setExtFlag(record.getExtFlag() - (record.getExtFlag() & record.getRemovedExtFlag()));
                record.setRemovedExtFlag(null);
            }
        }
        workshopMasterMapper.updateByPrimaryKeySelective(record);
        if (record.getSubTaskId() != null) {
            //激活门店
            try {
                workshopStatusBizService.insertWhenNotExists(record.getId(), WorkshopStatusVo.STATUS_ACTIVE, record.getSubTaskId());
            } catch (WxPltException e) {
                throw e;
            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        //同步直播门店
        etoBizService.synWorkshop(record);

        //门店信息同步到MDM
        try {
            //查出来再去同步
            WorkshopMaster workshopMaster = workshopMasterMapper.selectByPrimaryKey(record.getId());
            mdmService.syncWorkShopToMdm(workshopMaster);
        } catch(Exception e) {
            log.error("门店信息同步到MDM异常", e);
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delete(List<Long> ids) throws WxPltException {
        WorkshopMasterExample example = new WorkshopMasterExample();
        example.createCriteria().andIdIn(ids);
        WorkshopMaster record = new WorkshopMaster();
        record.setDeleteFlag(1);
        record.setUpdateTime(DateUtil.getCurrentDate());
        workshopMasterMapper.updateByExampleSelective(record, example);

        for (Long id : ids) {
            //同步删除直播门店
            WorkshopMaster workshopMaster = new WorkshopMaster();
            workshopMaster.setId(id);
            workshopMaster.setDeleteFlag(1);
            etoBizService.synWorkshop(workshopMaster);

            //删除门店信息同步到MDM
            try {
                mdmService.deleteWorkShopToMdm(id);
            } catch(Exception e) {
                log.error("门店信息同步到MDM异常", e);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteByExample(WorkshopMasterExample example) throws WxPltException {
        workshopMasterMapper.deleteByExample(example);
    }

    @Override
    public List<WorkshopMaster> queryByExample(WorkshopMasterExample example) throws WxPltException {
        return workshopMasterMapper.selectByExample(example);
    }

    @Override
    public List<WorkshopMaster> queryByParams(Map<String, Object> params) throws WxPltException {
        return workshopMasterMapper.queryByParams(params);
    }

    @Override
    public WorkshopMaster getBean(Long id) throws WxPltException {
        WorkshopMaster workshopMaster = workshopMasterMapper.selectByPrimaryKey(id);
        if (workshopMaster == null || workshopMaster.getDeleteFlag().equals(1)
                || WorkshopMaster.WORKSHOP_STATUS_DELETED.equals(workshopMaster.getStatus())) {
            throw new WxPltException("门店已解除合作");
        }
        return workshopMaster;
    }

    @Override
    public void queryForPage(WorkshopMasterParams params, Map<String, Object> resultMap) throws WxPltException {
        resultMap.put(Constants.RESULT_LST_KEY, queryForPage(params));
        resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
    }

    @Override
    public List<WorkshopMaster> queryForPage(WorkshopMasterParams params) throws WxPltException {
        if (!params.isPaging()) {
            return workshopMasterMapper.queryForPage(params);
        }

        params.setQueryTotal(false);
        List<WorkshopMaster> workshopMasters = workshopMasterMapper.queryForPage(params);
        long totalCount = workshopMasterMapper.totalForPage(params);
        params.setQueryTotal(true);
        params.setTotalCount(totalCount);
        return workshopMasters;
    }

    @Override
    public WorkshopMaster getBean(String workshopId, String dmsKey) {
        WorkshopMaster bean = null;
        if (StringUtils.isBlank(workshopId)) {
            bean = workshopMasterMapper.selectDmsWorkshopByKey(dmsKey);
        } else {
            bean = workshopMasterMapper.selectByPrimaryKey(Long.parseLong(workshopId));
        }
        return bean;
    }


    @Override
    public List<WorkshopMaster> getMktCustomerList(WorkshopMasterParams params) throws WxPltException {
        return workshopMasterMapper.getMktCustomerList(params);
    }

    @Override
    public List<WorkshopMaster> getMktApplyCustomerList(WorkshopMasterParams params) throws WxPltException {
        return workshopMasterMapper.getMktApplyCustomerList(params);
    }

    @Override
    public List<ReNameWorkShopVo> getReNameWorkShopList() {
        return workshopMasterMapper.getReNameWorkShopList();
    }

    @SuppressWarnings("unchecked")
    @Override
    public void sendEmailForRenam() {
        try {
            //获取邮件发送配置
            String[] to = null;
            String[] cc = null;
            Map<String, Object> dicMap = dicService.getDicItemByDicTypeCode("workShop.reName.send.email");
            if ("success".equals(dicMap.get("result"))) {
                List<DicItemVo> list = (List<DicItemVo>) dicMap.get("data");
                if (list != null && !list.isEmpty()) {
                    for (DicItemVo vo : list) {
                        if ("cc".equals(vo.getDicItemCode())) {
                            cc = vo.getDicItemDesc().split(",");
                        }
                    }
                }
            } else {
                throw new WxPltException((String) dicMap.get("errorMsg"));
            }

            Map<String, Object> dicToMap = dicService.getDicItemByDicTypeCode("workShop.reName.send.email");
            if ("success".equals(dicToMap.get("result"))) {
                List<DicItemVo> list = (List<DicItemVo>) dicToMap.get("data");
                if (list != null && !list.isEmpty()) {
                    for (DicItemVo vo : list) {
                        if ("to".equals(vo.getDicItemCode())) {
                            to = vo.getDicItemDesc().split(",");
                        }
                    }
                }
            } else {
                throw new WxPltException((String) dicMap.get("errorMsg"));
            }

            WebApplicationContext webApplicationContext = ContextLoader
                    .getCurrentWebApplicationContext();
            ServletContext servletContext = webApplicationContext
                    .getServletContext();

            //处理数据
            Map<Long, ReNameWorkShopVo> reNameMap = new HashMap<Long, ReNameWorkShopVo>();
            Map<Long, Long> workShopIdMap = new HashMap<Long, Long>();
            List<ReNameWorkShopVo> reNameWorkShopList = workshopMasterMapper.getReNameWorkShopList();
            for (ReNameWorkShopVo reNameWorkShopVo : reNameWorkShopList) {

                if (null != workShopIdMap.get(reNameWorkShopVo.getId())) {
                    continue;
                } else {
                    workShopIdMap.put(reNameWorkShopVo.getId2(), reNameWorkShopVo.getId2());
                }

                ReNameWorkShopVo reNameWorkShop = reNameMap.get(reNameWorkShopVo.getDistributorId());
                if (null == reNameWorkShop) {
                    reNameWorkShop = new ReNameWorkShopVo();
                    reNameWorkShop.setCustomerNameCn(reNameWorkShopVo.getCustomerNameCn());
                    reNameWorkShop.setWorkshopName(reNameWorkShopVo.getWorkshopName());
                    reNameWorkShop.setWorkShopNames(reNameWorkShopVo.getWorkshopName2());
                    reNameWorkShop.setCreateTime(reNameWorkShopVo.getCreateTime());
                } else {
                    String workShopNames = reNameWorkShop.getWorkShopNames() + "," + reNameWorkShopVo.getWorkshopName2();
                    reNameWorkShop.setWorkShopNames(workShopNames);
                }
                reNameMap.put(reNameWorkShopVo.getDistributorId(), reNameWorkShop);
            }
            List<ReNameWorkShopVo> newDataList = new ArrayList<ReNameWorkShopVo>();
            newDataList.addAll(reNameMap.values());

            //发送周报邮件
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("honorific", "Dear：");
            dataMap.put("workShopList", newDataList);


            EmailSendUtils.sendEmailForListContent(servletContext, to, cc, "经销商门店名称相同报警", dataMap,
                    new File[]{}, "workShop/work_shop_rename.ftl");
            log.info(" sendEmailForRenameWorkShop end=============================");
            LogUtils.addInfoLog(1l, "WorkshopMasterBizServiceImpl.sendEmailForRenam", "经销商门店名称相同报警");


        } catch (Exception e) {
            log.error("WorkshopMasterBizServiceImpl error. " + e.getMessage(), e);
            LogUtils.addErrorLog(1l, "WorkshopMasterBizServiceImpl.sendEmailForRenam", "经销商门店名称相同报警提醒发送失败。" + e.getMessage(), null);
        }
    }

    @Override
    public void updateDeleteFlagByIds(List<Long> list) {
        workshopMasterMapper.updateDeleteFlagByIds(list);
    }

    @Override
    public List<WorkshopMaster> querySimpleByParams(Map<String, Object> params) throws WxPltException {
        return workshopMasterMapper.querySimpleByParams(params);
    }

    @Override
    public void querySimpleForPage(WorkshopMasterParams params, Map<String, Object> resultMap) throws WxPltException {
        resultMap.put(Constants.RESULT_LST_KEY, querySimpleForPage(params));
        resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
    }

    @Override
    public List<WorkshopMaster> querySimpleForPage(WorkshopMasterParams params) throws WxPltException {
        params.setQueryTotal(false);
        List<WorkshopMaster> workshopMasters = workshopMasterMapper.querySimpleForPage(params);
        long totalCount = workshopMasterMapper.totalSimpleForPage(params);
        params.setQueryTotal(true);
        params.setTotalCount(totalCount);
        return workshopMasters;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void partnerConfirm(String workshopIds, Integer extFlag) throws WxPltException {
        String[] idStrings = workshopIds.split(",");
//		List<WorkshopMaster> workShopList = new ArrayList<WorkshopMaster>(idStrings.length);
        final Long userId = ContextUtil.getCurUserId();
        for (String id : idStrings) {
            if (StringUtils.isNotBlank(id)) {
                final WorkshopMaster workshopMaster = new WorkshopMaster();
                workshopMaster.setId(Long.valueOf(id));
                workshopMaster.setNewExtFlag(extFlag);
//				workshopMaster.setExtProperty19("1");
                workshopMaster.setExtProperty22(DateUtils.getCurrentDate());
                if (extFlag.equals(1 << 8)) {
                    //经销商确认合作
                    workshopMaster.setActivationTime(workshopMaster.getExtProperty22());
                    workshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS3);
                    //激活门店
                    workshopStatusBizService.insertWhenNotExists(workshopMaster.getId(), WorkshopMaster.WORKSHOP_STATUS3.toString(),
                            null, workshopMaster.getActivationTime());
                }
                update(workshopMaster);
                if (!extFlag.equals(1 << 8)) {
                    //经销商确认不合作，不创建技师
                    continue;
                }
                //同步创建技师
                LogUtils.addLog(new LogTask() {

                    @Override
                    public void execute() throws Exception {
                        try {
                            workshopEmpBizService.createEmployeeByWorkshop(workshopMaster.getId());
                        } catch (Exception e) {
                            LogUtils.addErrorLog(userId, "com.chevron.master.business.impl.WorkshopMasterBizServiceImpl.partnerConfirm",
                                    "门店激活创建技师失败。" + e.getMessage(), workshopMaster.getId().toString());
                        }
                    }
                });
            }
        }
    }

    //AMS处理 潜在门店转为合作门店，ext_flag +256
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void partnerConfirmV2(String workshopIds, Integer extFlag) throws WxPltException {
        String[] idStrings = workshopIds.split(",");
        final Long userId = ContextUtil.getCurUserId();
        for (String id : idStrings) {
            if (StringUtils.isNotBlank(id)) {
                final WorkshopMaster workshopMaster = workshopMasterMapper.selectByPrimaryKey(Long.valueOf(id));
                //已经是经销商确认合作门店的，跳过
                if(WorkshopMaster.WORKSHOP_STATUS3==workshopMaster.getStatus() && (workshopMaster.getExtFlag()&256)>0 ){
                    continue;
                }
                workshopMaster.setNewExtFlag(extFlag);
				workshopMaster.setRemark("由AMS确认！潜在门店转为合作门店，extFlag+256");
                workshopMaster.setExtProperty22(DateUtils.getCurrentDate());
                if (extFlag.equals(1 << 8)) {
                    //经销商确认合作
                    workshopMaster.setActivationTime(workshopMaster.getExtProperty22());
                    workshopMaster.setStatus(WorkshopMaster.WORKSHOP_STATUS3);
                    //激活门店
                    workshopStatusBizService.insertWhenNotExists(workshopMaster.getId(), WorkshopMaster.WORKSHOP_STATUS3.toString(),
                            null, workshopMaster.getActivationTime());
                }
                update(workshopMaster);
                if (!extFlag.equals(1 << 8)) {
                    //经销商确认不合作，不创建技师
                    continue;
                }
                //同步创建技师
                LogUtils.addLog(new LogTask() {

                    @Override
                    public void execute() throws Exception {
                        try {
                            workshopEmpBizService.createEmployeeByWorkshop(workshopMaster.getId());
                        } catch (Exception e) {
                            LogUtils.addErrorLog(userId, "com.chevron.master.business.impl.WorkshopMasterBizServiceImpl.partnerConfirmV2",
                                    "门店激活创建技师失败。" + e.getMessage(), workshopMaster.getId().toString());
                        }
                    }
                });
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importWorkshopRoute(AsynProcessStatus processStatus, List<WorkshopMaster> importList, ValidatorHelper validatorHelper) throws WxPltException {
        if (CollectionUtils.isEmpty(importList)) {
            throw new WxPltException("导入数据不能为空！");
        }
        processStatus.saveMessage("数据检验，请等待......");
        checkData(importList, validatorHelper);
        processStatus.saveMessage("录入数据，请等待......");
        BatchOperationUtil<WorkshopMaster> batchOperationUtil = new BatchOperationUtil<WorkshopMaster>(importList, 80);
        while (batchOperationUtil.hasMoreElements()) {
            List<WorkshopMaster> workshopMasterList = batchOperationUtil.nextElement();
            workshopMasterMapper.batchUpdateRouteById(workshopMasterList);
        }
    }

    private void checkData(final List<WorkshopMaster> importList, final ValidatorHelper validatorHelper) throws WxPltException {
        final List<Integer> resultIdx = new ArrayList<Integer>();
        final List<Integer> repeatIdx = new ArrayList<Integer>();
        new BaseBatchSqlQuery() {
            //            private List<Integer> repeatIdx = new ArrayList<Integer>();
            {
                Set<String> checkSet = new HashSet<String>();
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < importList.size(); i++) {
                    WorkshopMaster workshopMaster = importList.get(i);
                    sb.append(workshopMaster.getPartnerName()).append(workshopMaster.getWorkshopName()).append(workshopMaster.getProvinceName())
                            .append(workshopMaster.getCityName()).append(workshopMaster.getDistName());
                    String checkKey = sb.toString();
                    sb.delete(0, sb.length());
                    boolean ownerIsBlank = StringUtils.isBlank(workshopMaster.getRouteOwnerAccount());
                    if (ownerIsBlank && StringUtils.isNotBlank(workshopMaster.getRoute())) {
                        validatorHelper.addEmptyProperty("第" + i + 1 + "行" + "专线所属人账号");
                    }
                    if (checkSet.contains(checkKey)) {
                        validatorHelper.addErrorProperty("以下客户重复：", workshopMaster.getWorkshopName(), "第" + (i + 1) + "行");
                        repeatIdx.add(i);
                    } else {
                        checkSet.add(checkKey);
                        setParamSize(ownerIsBlank?2:4);
                        beforeProcessRow();
                        StringBuilder sql = new StringBuilder("select ? idx,t1.id,t1.work_shop_name, r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, (case when o.type=3 then r1o1.organization_name + ' - ' else '' end) + o.organization_name partner_name ");
                        if (!ownerIsBlank) {
                            sql.append(",? owner_login_name,u.login_name ");
                        }
                        sql.append("from wx_t_work_shop t1 join wx_t_organization o on t1.partner_id=o.id left join wx_t_region r1 on t1.region_id=r1.id left join wx_t_region r2 on r1.parent_id=r2.id ")
                                .append("left join wx_t_region r3 on r2.parent_id=r3.id left join wx_t_partner_o2o_enterprise r1pe1 on r1pe1.partner_id=o.id left join wx_t_organization r1o1 on o.type=3 and r1pe1.ext_property1=r1o1.id ");
                        if (!ownerIsBlank) {
                            sql.append("left join wx_t_user u on t1.partner_id = u.org_id and u.status = 1 and u.login_name = ? ");
                        }
                        sql.append("where o.status='1' and t1.delete_flag=0 and t1.status>=0 and (o.organization_name+t1.work_shop_name+r3.region_name+r2.region_name+r1.region_name) = ? ");
                        addSql(sql.toString());
                        if (!ownerIsBlank){
                            addParams(i,workshopMaster.getRouteOwnerAccount(),workshopMaster.getRouteOwnerAccount(),checkKey);
                        }else {
                            addParams(i, checkKey);
                        }
                    }
                }
                executeQuery();
            }

            @Override
            protected void processResultByRow(ResultSet resultSet, Object... params) {
                try {
                    int idx = resultSet.getInt("idx");
                    resultIdx.add(idx);
                    importList.get(idx).setId(resultSet.getLong("id"));
                    String ownerLoginName = resultSet.getString("owner_login_name");
                    if (ownerLoginName!=null){
                        String loginName = resultSet.getString("login_name");
                        if (loginName==null){
                            validatorHelper.addErrorProperty("以下专线所属人账号未在该门店经销商下找到：",ownerLoginName,"第"+(idx+1)+"行");
                        }
                    }
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
        };

        for (int i = 0; i < importList.size(); i++) {
            if (!resultIdx.contains(i) && !repeatIdx.contains(i)) {
                WorkshopMaster workshopMaster = importList.get(i);
                validatorHelper.addErrorProperty("以下客户未找到：", workshopMaster.getWorkshopName(), "第" + (i + 1) + "行");
            }
        }
        String errorMsg = validatorHelper.getErrorMessage();
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new WxPltException(errorMsg);
        }
    }
}
