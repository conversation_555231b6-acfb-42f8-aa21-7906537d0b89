package com.chevron.b2b.model;

import java.util.Date;
import java.util.List;

import com.chevron.master.model.WorkshopMaster;
import com.chevron.pms.model.ProductVo;
import com.sys.organization.model.PartnerView;
import com.wordnik.swagger.annotations.ApiModel;
import com.wordnik.swagger.annotations.ApiModelProperty;

/**
 * B2B核销积分政策-wx_t_b2b_verify_point_policy
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
@ApiModel(value="B2B核销积分政策", description="B2B核销积分政策")
public class B2bVerifyPointPolicy {

	/**  ID  **/
    private Long id;

	/**  产品编码  **/
    private String sku;

	/**  奖励积分  **/
    private Double awardPoint;

	/**  启用  **/
    private Integer enableFlag;

	/**  删除标记  **/
    private Integer deleteFlag;

	/**  创建用户ID  **/
    private Long createUserId;

	/**  创建时间  **/
    private Date createTime;

	/**  最后修改用户ID  **/
    private Long updateUserId;

	/**  最后修改时间  **/
    private Date updateTime;
    
    private String productName;
    
    private String productNameEn;
   
    private double capacity;
    
    private Long iconId;
    
    private Date productCreationDate;
    
    private Date startDate;//开始时间
    
    private Date endDate;//结束时间
    
    private String distributorIds;//经销商id
    
    private List<PartnerView> distributorVo;
    
    private List<WorkshopMaster> workShopVo;
    
	/**  门店Id  **/
    private String workShopIds;
    
    /**  额外奖励积分  **/
    private Double orginalPoint;

	@ApiModelProperty(value="标题", name="policyName")
    private String policyName;
	
	private List<ProductVo> products;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Double getAwardPoint() {
        return awardPoint;
    }

    public void setAwardPoint(Double awardPoint) {
        this.awardPoint = awardPoint;
    }

    public Integer getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(Integer enableFlag) {
        this.enableFlag = enableFlag;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public double getCapacity() {
		return capacity;
	}

	public void setCapacity(double capacity) {
		this.capacity = capacity;
	}

	public Long getIconId() {
		return iconId;
	}

	public void setIconId(Long iconId) {
		this.iconId = iconId;
	}

	public Date getProductCreationDate() {
		return productCreationDate;
	}

	public void setProductCreationDate(Date productCreationDate) {
		this.productCreationDate = productCreationDate;
	}

	public String getProductNameEn() {
		return productNameEn;
	}

	public void setProductNameEn(String productNameEn) {
		this.productNameEn = productNameEn;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * @return the startDate
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the endDate
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the distributorIds
	 */
	public String getDistributorIds() {
		return distributorIds;
	}

	/**
	 * @param distributorIds the distributorIds to set
	 */
	public void setDistributorIds(String distributorIds) {
		this.distributorIds = distributorIds;
	}

	/**
	 * @return the distributorVo
	 */
	public List<PartnerView> getDistributorVo() {
		return distributorVo;
	}

	/**
	 * @param distributorVo the distributorVo to set
	 */
	public void setDistributorVo(List<PartnerView> distributorVo) {
		this.distributorVo = distributorVo;
	}

	public String getWorkShopIds() {
		return workShopIds;
	}

	public void setWorkShopIds(String workShopIds) {
		this.workShopIds = workShopIds;
	}

	public Double getOrginalPoint() {
		return orginalPoint;
	}

	public void setOrginalPoint(Double orginalPoint) {
		this.orginalPoint = orginalPoint;
	}

	public List<WorkshopMaster> getWorkShopVo() {
		return workShopVo;
	}

	public void setWorkShopVo(List<WorkshopMaster> workShopVo) {
		this.workShopVo = workShopVo;
	}

	public String getPolicyName() {
		return policyName;
	}

	public void setPolicyName(String policyName) {
		this.policyName = policyName;
	}

	public List<ProductVo> getProducts() {
		return products;
	}

	public void setProducts(List<ProductVo> products) {
		this.products = products;
	}
}
