package com.chevron.master.controller;

import com.chevron.master.model.BPMApproveCallbackParams;
import com.chevron.master.model.UpdateGiftPackageToReadReq;
import com.chevron.master.service.GiftPackageService;
import com.common.base.BaseResp;
import com.common.base.ResultCode;
import com.common.exception.WxPltException;
import com.common.util.JsonUtil;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * @Description 开客礼包
 * <AUTHOR> Ma
 * @Date 15:48 03/01/2024
 **/
@Controller
@RequestMapping(value="/giftPackage")
public class GiftPackageController {

    @Resource
    private GiftPackageService giftPackageService;

    private final Logger log = Logger.getLogger(GiftPackageController.class);

    /**
     * BPM 审批通过 回调接口
     * 逻辑  更新门店为合作门店且插入开客礼包表记录
     */
    @ResponseBody
    @RequestMapping(value="/updateData.do", method = {RequestMethod.POST})
    public BaseResp bpmCallback(@RequestBody BPMApproveCallbackParams params){
        log.info("GiftPackageController.bpmCallback requestBody: " + JsonUtil.writeValue(params));
        BaseResp baseResp = new BaseResp();
        try {
            if (null == params.getWorkShopId()) {
                baseResp.setCode(ResultCode.PARAM_ERROR.getCode());
                baseResp.setMessage(ResultCode.PARAM_ERROR.getMessage());
                return baseResp;
            }
            giftPackageService.bpmApproveCallback(params);
        } catch (Exception e) {
            log.error("GiftPackageController.bpmCallback error. ", e);
            if (e instanceof WxPltException) {
                baseResp.setMessage(((WxPltException) e).getExpMsg());
            } else {
                baseResp.setMessage(ResultCode.EXCETPION.getMessage());
            }
            baseResp.setCode(ResultCode.EXCETPION.getCode());
        }
        return baseResp;
    }

    /**
     * 手动触发合作门店开客礼包创建task
     */
    @ResponseBody
    @RequestMapping(value="/triggerCreateTask.do", method = {RequestMethod.POST, RequestMethod.GET})
    public BaseResp createWorkShopGiftPackage(){
        log.info("createWorkShopGiftPackage... ");
        BaseResp baseResp = new BaseResp();
        try {
            giftPackageService.createWorkShopGiftPackage();
        } catch (Exception e) {
            log.error("GiftPackageController.createWorkShopGiftPackage error. ", e);
            baseResp.setCode(ResultCode.EXCETPION.getCode());
            baseResp.setMessage(ResultCode.EXCETPION.getMessage());
        }
        return baseResp;
    }

    /**
     * 获取user未读消息列表
     */
    @ResponseBody
    @RequestMapping(value="/unReadMsg.do", method = {RequestMethod.POST, RequestMethod.GET})
    public BaseResp unReadMsg(){
        log.info("GiftPackageController.unReadMsg........");
        BaseResp baseResp = new BaseResp();
        try {
            baseResp.setData(giftPackageService.getUnReadMsg());
        } catch (Exception e) {
            log.error("GiftPackageController.unReadMsg error. ", e);
            baseResp.setCode(ResultCode.EXCETPION.getCode());
            baseResp.setMessage(ResultCode.EXCETPION.getMessage());
        }
        return baseResp;
    }

    /**
     * 更新消息为已读
     */
    @ResponseBody
    @RequestMapping(value="/updateToRead.do", method = {RequestMethod.POST})
    public BaseResp updateToRead(@RequestBody UpdateGiftPackageToReadReq req){
        log.info("GiftPackageController.updateToRead requestBody: " + JsonUtil.writeValue(req));
        BaseResp baseResp = new BaseResp();
        if (CollectionUtils.isEmpty(req.getGiftPackageIds())) {
            baseResp.setCode(ResultCode.PARAM_ERROR.getCode());
            baseResp.setMessage("giftPackageIds can not be null or empty");
            return baseResp;
        }
        try {
            giftPackageService.updateToRead(req.getGiftPackageIds());
        } catch (Exception e) {
            log.error("GiftPackageController.updateToRead error. ", e);
            if (e instanceof WxPltException) {
                baseResp.setMessage(((WxPltException) e).getExpMsg());
            } else {
                baseResp.setMessage(ResultCode.EXCETPION.getMessage());
            }
            baseResp.setCode(ResultCode.EXCETPION.getCode());
        }
        return baseResp;
    }
}
