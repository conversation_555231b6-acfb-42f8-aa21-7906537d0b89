insert into wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag,remark, create_user_id, create_time)
select distinct r1.role_id, p.module_code, p.permission_weight, 1, p.remark, 1, getdate() 
from wx_t_operation_permission p left join wx_t_role r on r.role_id=p.role_id
left join wx_t_role r1 on r1.ch_role_name='Distributor_houqin'
where r.ch_role_name='Service_Partner_Manager'
and p.module_code='Dsr.kpi'
and not exists (select 1 from wx_t_operation_permission p1 where p1.module_code=p.module_code and p1.role_id=r1.role_id
and p1.permission_weight=p.permission_weight)