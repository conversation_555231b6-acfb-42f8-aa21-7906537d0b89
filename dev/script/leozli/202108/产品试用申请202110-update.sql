ALTER TABLE wx_t_oil_sample_apply add abm_submit_time                  datetime                                       null

update el set el.source_key=(select eg1.id from wx_t_workflow_pre_exe_group eg1 where eg1.group_name='经销商负责FLSR(全量关系)')
from wx_t_workflow_exe_list el
left join wx_t_workflow_step ws on el.step_id=ws.step_id
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP'
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER'
where ws.workflow_code='OIL_SAMPLE_APPLY' and ws.step_code='TESTING_TRACKING_CONTENT'

update ws set ws.operation_permission_weight=5
from wx_t_workflow_exe_list el
left join wx_t_workflow_step ws on el.step_id=ws.step_id
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP'
LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER'
where ws.workflow_code='OIL_SAMPLE_APPLY' and ws.step_code='TESTING_TRACKING_CONTENT'

update sa set sa.abm_submit_time=wf_wsh1_1.execute_time
from wx_t_workflow_step_history wf_wsh1_1
left join wx_t_workflow_step_instance wf_wsi1_1 on wf_wsh1_1.step_instance_id=wf_wsi1_1.step_instance_id
left join wx_t_workflow_step ws on wf_wsi1_1.step_id=ws.step_id
left join wx_t_workflow_instance wi on wi.flow_instance_id=wf_wsi1_1.workflow_instance_id
left join wx_t_oil_sample_apply sa on sa.id=wi.form_key
where wi.workflow_code='OIL_SAMPLE_APPLY' and ws.step_code='ABM_APPROVE'

INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'oilSampleApplyJob', N'WorkGroup', N'1', N'0 0 9 * * ?', N'每天早上9点执行', NULL, N'1', N'oilSampleApplyJob', N'notifyFlsrUpdateTestStatus', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '每月提醒FLSR更新产品试用状态')
INSERT [dbo].[task_schedule_job] ([create_time], [update_time], [job_name], [job_group], [job_status], [cron_expression], [description], [bean_class], [is_concurrent], [spring_id], [method_name], [task_id], [start_time], [end_time], [repetition_type], [repetition_frequency], [create_user_id], [create_user_name], [source_task_name]) VALUES 
(getdate(), getdate(), N'oilSampleApplyJob', N'WorkGroup', N'1', N'0 0 8 * * ?', N'每天早上8点执行', NULL, N'1', N'oilSampleApplyJob', N'closeApplyByAuto', NULL, CAST(N'1980-08-22T17:01:00.000' AS DateTime), CAST(N'3033-08-22T17:01:00.000' AS DateTime), NULL, NULL, NULL, NULL, '自动关闭申请超过一年的试用申请')

/*select (select u.user_id from dw_customer_org_sales cos1 
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=cos1.distributor_id
left join wx_T_user u on u.cai=cos1.sales_cai and u.status=1 
where pe.partner_id=a.customerid and cos1.channel_weight&(case when a.brand=4 then 2 else a.brand end)>0),a.* */
--el.* into wx_t_wor_ins_exe_list20211115
update el set el.executor=(select u.user_id from dw_customer_org_sales cos1 
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=cos1.distributor_id
left join wx_T_user u on u.cai=cos1.sales_cai and u.status=1 
where pe.partner_id=a.customerid and cos1.channel_weight&(case when a.brand=4 then 2 else a.brand end)>0)
from wx_t_workflow_instance wi left join wx_t_workflow_step_instance si on wi.flow_instance_id=si.workflow_instance_id
left join wx_t_wor_ins_exe_list el on el.step_instance_id=si.step_instance_id
left join wx_t_workflow_step_instance wsi on wsi.step_instance_id=el.step_instance_id
left join wx_t_workflow_step ws on wsi.step_id=ws.step_id
left join wx_t_oil_sample_apply a on a.id=wi.form_key
where el.executor=(select user_id from wx_t_user u where u.login_name='cyana') and execute_status=10
and wi.next_step_no=si.step_no and ws.step_code='TESTING_TRACKING_CONTENT'