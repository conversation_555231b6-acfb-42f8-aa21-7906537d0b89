select * from (
select --* 
o.id "经销商ID", o.organization_name "经销商名称", w.id "门店ID(storeno)", w.work_shop_name "门店名称", 
we.name "技师姓名(name)", we.code "技师编码(cardnumber)", we.mobile "用户手机号(mobile)" 
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
left join wx_t_organization o on o.id=w.partner_id
where o.status=1 and we.enable_flag=1 and we.version_no=1
and o.id!=9 and employee_type in ('Owner','Boss') and w.status=3 and w.delete_flag=0 and w.business_weight=1
and len(we.mobile)=11
union all
select --* 
o.id "经销商ID", o.organization_name "经销商名称", w.id "门店ID(storeno)", w.work_shop_name "门店名称", 
isnull(w.ext_property4, w.contact_person) "技师姓名(name)", 'W' + convert(nvarchar(20),w.id) "技师编码(cardnumber)",
 isnull(w.ext_property5,w.contact_person_tel) "用户手机号(mobile)" 
from wx_t_work_shop w
left join wx_t_organization o on o.id=w.partner_id
where o.status=1 and w.status=3 and o.id!=9 and w.delete_flag=0 and w.business_weight in (2,4)
and isnull(w.ext_property4, w.contact_person) is not null and isnull(w.ext_property4, w.contact_person) !=''
and len(isnull(w.ext_property5,w.contact_person_tel))=11) a order by a.[经销商ID], a.[门店ID(storeno)]



select o.id "经销商ID", o.organization_name "经销商名称", 
w.id "门店ID(sid)", w.work_shop_name "门店名称(name)",
w.work_shop_address "门店地址(address)", w.latitude, w.longitude
from wx_t_work_shop w
left join wx_t_organization o on o.id=w.partner_id
where w.id in (select w.id
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
left join wx_t_organization o on o.id=w.partner_id
where o.status=1 and we.enable_flag=1 and we.version_no=1
and o.id!=9 and employee_type in ('Owner','Boss') and w.status=3 and w.delete_flag=0 and w.business_weight=1
and len(we.mobile)=11
union all
select w.id
from wx_t_work_shop w
left join wx_t_organization o on o.id=w.partner_id
where o.status=1 and w.status=3 and o.id!=9 and w.delete_flag=0 and w.business_weight in (2,4)
and isnull(w.ext_property4, w.contact_person) is not null and isnull(w.ext_property4, w.contact_person) !=''
and len(isnull(w.ext_property5,w.contact_person_tel))=11)
order by o.id

select o.id "经销商ID", o.organization_name "经销商名称(name)"
from wx_t_organization o 
where o.id in (select w.partner_id
from wx_t_workshop_employee we left join wx_t_work_shop w on w.id=we.workshop_id
left join wx_t_organization o on o.id=w.partner_id
where o.status=1 and we.enable_flag=1 and we.version_no=1
and o.id!=9 and employee_type in ('Owner','Boss') and w.status=3 and w.delete_flag=0 and w.business_weight=1
and len(we.mobile)=11
union all
select w.partner_id
from wx_t_work_shop w
left join wx_t_organization o on o.id=w.partner_id
where o.status=1 and w.status=3 and o.id!=9 and w.delete_flag=0 and w.business_weight in (2,4)
and isnull(w.ext_property4, w.contact_person) is not null and isnull(w.ext_property4, w.contact_person) !=''
and len(isnull(w.ext_property5,w.contact_person_tel))=11)
order by o.id

select dp.product_name "产品名称(product_name)", dp.sku "SKU(spu/skus[0].sku)",
'https://www.cvx-sh.com/anon/downloadsharedfile.do?attId='+convert(nvarchar(20), dp.product_hd_photo) "高清图(pics[0])",
'https://www.cvx-sh.com/anon/downloadsharedfile.do?attId='+convert(nvarchar(20), dp.product_photo) "缩略图(skus[0].attributes[0].thumbnail)",
case when convert(float, p.capacity) < 1-0.0000001 then convert(nvarchar(10), convert(int, convert(float, p.capacity)*1000)) + 'ML' else p.capacity+'L' end "容量(skus[0].attributes[0].value)",
dp.product_desc "详情(details)"
 from wx_t_db2b_product dp
left join wx_t_product p on dp.sku=p.sku
where dp.partner_id=-1 and dp.status=20