package com.chevron.b2b.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 计划单表查询条件
 * <AUTHOR>
 * @version 1.0 2018-12-11 11:33
 */
public class ScheduleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ScheduleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andScheduleNameIsNull() {
            addCriterion("schedule_name is null");
            return (Criteria) this;
        }

        public Criteria andScheduleNameIsNotNull() {
            addCriterion("schedule_name is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleNameEqualTo(String value) {
            addCriterion("schedule_name =", value, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameNotEqualTo(String value) {
            addCriterion("schedule_name <>", value, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameGreaterThan(String value) {
            addCriterion("schedule_name >", value, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameGreaterThanOrEqualTo(String value) {
            addCriterion("schedule_name >=", value, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameLessThan(String value) {
            addCriterion("schedule_name <", value, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameLessThanOrEqualTo(String value) {
            addCriterion("schedule_name <=", value, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameIn(List<String> values) {
            addCriterion("schedule_name in", values, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameNotIn(List<String> values) {
            addCriterion("schedule_name not in", values, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameBetween(String value1, String value2) {
            addCriterion("schedule_name between", value1, value2, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andScheduleNameNotBetween(String value1, String value2) {
            addCriterion("schedule_name not between", value1, value2, "scheduleName");
            return (Criteria) this;
        }

        public Criteria andIconIdIsNull() {
            addCriterion("icon_id is null");
            return (Criteria) this;
        }

        public Criteria andIconIdIsNotNull() {
            addCriterion("icon_id is not null");
            return (Criteria) this;
        }

        public Criteria andIconIdEqualTo(Long value) {
            addCriterion("icon_id =", value, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdNotEqualTo(Long value) {
            addCriterion("icon_id <>", value, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdGreaterThan(Long value) {
            addCriterion("icon_id >", value, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdGreaterThanOrEqualTo(Long value) {
            addCriterion("icon_id >=", value, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdLessThan(Long value) {
            addCriterion("icon_id <", value, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdLessThanOrEqualTo(Long value) {
            addCriterion("icon_id <=", value, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdIn(List<Long> values) {
            addCriterion("icon_id in", values, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdNotIn(List<Long> values) {
            addCriterion("icon_id not in", values, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdBetween(Long value1, Long value2) {
            addCriterion("icon_id between", value1, value2, "iconId");
            return (Criteria) this;
        }

        public Criteria andIconIdNotBetween(Long value1, Long value2) {
            addCriterion("icon_id not between", value1, value2, "iconId");
            return (Criteria) this;
        }

        public Criteria andBannerIdIsNull() {
            addCriterion("banner_id is null");
            return (Criteria) this;
        }

        public Criteria andBannerIdIsNotNull() {
            addCriterion("banner_id is not null");
            return (Criteria) this;
        }

        public Criteria andBannerIdEqualTo(Long value) {
            addCriterion("banner_id =", value, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdNotEqualTo(Long value) {
            addCriterion("banner_id <>", value, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdGreaterThan(Long value) {
            addCriterion("banner_id >", value, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("banner_id >=", value, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdLessThan(Long value) {
            addCriterion("banner_id <", value, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdLessThanOrEqualTo(Long value) {
            addCriterion("banner_id <=", value, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdIn(List<Long> values) {
            addCriterion("banner_id in", values, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdNotIn(List<Long> values) {
            addCriterion("banner_id not in", values, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdBetween(Long value1, Long value2) {
            addCriterion("banner_id between", value1, value2, "bannerId");
            return (Criteria) this;
        }

        public Criteria andBannerIdNotBetween(Long value1, Long value2) {
            addCriterion("banner_id not between", value1, value2, "bannerId");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIsNull() {
            addCriterion("schedule_type is null");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIsNotNull() {
            addCriterion("schedule_type is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeEqualTo(String value) {
            addCriterion("schedule_type =", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotEqualTo(String value) {
            addCriterion("schedule_type <>", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeGreaterThan(String value) {
            addCriterion("schedule_type >", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("schedule_type >=", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLessThan(String value) {
            addCriterion("schedule_type <", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLessThanOrEqualTo(String value) {
            addCriterion("schedule_type <=", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIn(List<String> values) {
            addCriterion("schedule_type in", values, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotIn(List<String> values) {
            addCriterion("schedule_type not in", values, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeBetween(String value1, String value2) {
            addCriterion("schedule_type between", value1, value2, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotBetween(String value1, String value2) {
            addCriterion("schedule_type not between", value1, value2, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeIsNull() {
            addCriterion("execute_user_type is null");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeIsNotNull() {
            addCriterion("execute_user_type is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeEqualTo(String value) {
            addCriterion("execute_user_type =", value, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeNotEqualTo(String value) {
            addCriterion("execute_user_type <>", value, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeGreaterThan(String value) {
            addCriterion("execute_user_type >", value, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeGreaterThanOrEqualTo(String value) {
            addCriterion("execute_user_type >=", value, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeLessThan(String value) {
            addCriterion("execute_user_type <", value, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeLessThanOrEqualTo(String value) {
            addCriterion("execute_user_type <=", value, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeIn(List<String> values) {
            addCriterion("execute_user_type in", values, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeNotIn(List<String> values) {
            addCriterion("execute_user_type not in", values, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeBetween(String value1, String value2) {
            addCriterion("execute_user_type between", value1, value2, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andExecuteUserTypeNotBetween(String value1, String value2) {
            addCriterion("execute_user_type not between", value1, value2, "executeUserType");
            return (Criteria) this;
        }

        public Criteria andAwardPointIsNull() {
            addCriterion("award_point is null");
            return (Criteria) this;
        }

        public Criteria andAwardPointIsNotNull() {
            addCriterion("award_point is not null");
            return (Criteria) this;
        }

        public Criteria andAwardPointEqualTo(Double value) {
            addCriterion("award_point =", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointNotEqualTo(Double value) {
            addCriterion("award_point <>", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointGreaterThan(Double value) {
            addCriterion("award_point >", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointGreaterThanOrEqualTo(Double value) {
            addCriterion("award_point >=", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointLessThan(Double value) {
            addCriterion("award_point <", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointLessThanOrEqualTo(Double value) {
            addCriterion("award_point <=", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointIn(List<Double> values) {
            addCriterion("award_point in", values, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointNotIn(List<Double> values) {
            addCriterion("award_point not in", values, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointBetween(Double value1, Double value2) {
            addCriterion("award_point between", value1, value2, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointNotBetween(Double value1, Double value2) {
            addCriterion("award_point not between", value1, value2, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andLeftStepsIsNull() {
            addCriterion("left_steps is null");
            return (Criteria) this;
        }

        public Criteria andLeftStepsIsNotNull() {
            addCriterion("left_steps is not null");
            return (Criteria) this;
        }

        public Criteria andLeftStepsEqualTo(Integer value) {
            addCriterion("left_steps =", value, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsNotEqualTo(Integer value) {
            addCriterion("left_steps <>", value, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsGreaterThan(Integer value) {
            addCriterion("left_steps >", value, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsGreaterThanOrEqualTo(Integer value) {
            addCriterion("left_steps >=", value, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsLessThan(Integer value) {
            addCriterion("left_steps <", value, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsLessThanOrEqualTo(Integer value) {
            addCriterion("left_steps <=", value, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsIn(List<Integer> values) {
            addCriterion("left_steps in", values, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsNotIn(List<Integer> values) {
            addCriterion("left_steps not in", values, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsBetween(Integer value1, Integer value2) {
            addCriterion("left_steps between", value1, value2, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andLeftStepsNotBetween(Integer value1, Integer value2) {
            addCriterion("left_steps not between", value1, value2, "leftSteps");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andScheduleDescIsNull() {
            addCriterion("schedule_desc is null");
            return (Criteria) this;
        }

        public Criteria andScheduleDescIsNotNull() {
            addCriterion("schedule_desc is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleDescEqualTo(String value) {
            addCriterion("schedule_desc =", value, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescNotEqualTo(String value) {
            addCriterion("schedule_desc <>", value, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescGreaterThan(String value) {
            addCriterion("schedule_desc >", value, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescGreaterThanOrEqualTo(String value) {
            addCriterion("schedule_desc >=", value, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescLessThan(String value) {
            addCriterion("schedule_desc <", value, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescLessThanOrEqualTo(String value) {
            addCriterion("schedule_desc <=", value, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescIn(List<String> values) {
            addCriterion("schedule_desc in", values, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescNotIn(List<String> values) {
            addCriterion("schedule_desc not in", values, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescBetween(String value1, String value2) {
            addCriterion("schedule_desc between", value1, value2, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andScheduleDescNotBetween(String value1, String value2) {
            addCriterion("schedule_desc not between", value1, value2, "scheduleDesc");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsIsNull() {
            addCriterion("assign_distributors is null");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsIsNotNull() {
            addCriterion("assign_distributors is not null");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsEqualTo(String value) {
            addCriterion("assign_distributors =", value, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsNotEqualTo(String value) {
            addCriterion("assign_distributors <>", value, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsGreaterThan(String value) {
            addCriterion("assign_distributors >", value, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsGreaterThanOrEqualTo(String value) {
            addCriterion("assign_distributors >=", value, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsLessThan(String value) {
            addCriterion("assign_distributors <", value, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsLessThanOrEqualTo(String value) {
            addCriterion("assign_distributors <=", value, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsIn(List<String> values) {
            addCriterion("assign_distributors in", values, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsNotIn(List<String> values) {
            addCriterion("assign_distributors not in", values, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsBetween(String value1, String value2) {
            addCriterion("assign_distributors between", value1, value2, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignDistributorsNotBetween(String value1, String value2) {
            addCriterion("assign_distributors not between", value1, value2, "assignDistributors");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsIsNull() {
            addCriterion("assign_workshops is null");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsIsNotNull() {
            addCriterion("assign_workshops is not null");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsEqualTo(String value) {
            addCriterion("assign_workshops =", value, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsNotEqualTo(String value) {
            addCriterion("assign_workshops <>", value, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsGreaterThan(String value) {
            addCriterion("assign_workshops >", value, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsGreaterThanOrEqualTo(String value) {
            addCriterion("assign_workshops >=", value, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsLessThan(String value) {
            addCriterion("assign_workshops <", value, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsLessThanOrEqualTo(String value) {
            addCriterion("assign_workshops <=", value, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsIn(List<String> values) {
            addCriterion("assign_workshops in", values, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsNotIn(List<String> values) {
            addCriterion("assign_workshops not in", values, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsBetween(String value1, String value2) {
            addCriterion("assign_workshops between", value1, value2, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andAssignWorkshopsNotBetween(String value1, String value2) {
            addCriterion("assign_workshops not between", value1, value2, "assignWorkshops");
            return (Criteria) this;
        }

        public Criteria andPublishTimeIsNull() {
            addCriterion("publish_time is null");
            return (Criteria) this;
        }

        public Criteria andPublishTimeIsNotNull() {
            addCriterion("publish_time is not null");
            return (Criteria) this;
        }

        public Criteria andPublishTimeEqualTo(Date value) {
            addCriterion("publish_time =", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeNotEqualTo(Date value) {
            addCriterion("publish_time <>", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeGreaterThan(Date value) {
            addCriterion("publish_time >", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("publish_time >=", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeLessThan(Date value) {
            addCriterion("publish_time <", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeLessThanOrEqualTo(Date value) {
            addCriterion("publish_time <=", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeIn(List<Date> values) {
            addCriterion("publish_time in", values, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeNotIn(List<Date> values) {
            addCriterion("publish_time not in", values, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeBetween(Date value1, Date value2) {
            addCriterion("publish_time between", value1, value2, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeNotBetween(Date value1, Date value2) {
            addCriterion("publish_time not between", value1, value2, "publishTime");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIsNull() {
            addCriterion("sales_channel is null");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIsNotNull() {
            addCriterion("sales_channel is not null");
            return (Criteria) this;
        }

        public Criteria andSalesChannelEqualTo(String value) {
            addCriterion("sales_channel =", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotEqualTo(String value) {
            addCriterion("sales_channel <>", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelGreaterThan(String value) {
            addCriterion("sales_channel >", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelGreaterThanOrEqualTo(String value) {
            addCriterion("sales_channel >=", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLessThan(String value) {
            addCriterion("sales_channel <", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLessThanOrEqualTo(String value) {
            addCriterion("sales_channel <=", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIn(List<String> values) {
            addCriterion("sales_channel in", values, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotIn(List<String> values) {
            addCriterion("sales_channel not in", values, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelBetween(String value1, String value2) {
            addCriterion("sales_channel between", value1, value2, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotBetween(String value1, String value2) {
            addCriterion("sales_channel not between", value1, value2, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andExtAttr1IsNull() {
            addCriterion("ext_attr1 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr1IsNotNull() {
            addCriterion("ext_attr1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr1EqualTo(String value) {
            addCriterion("ext_attr1 =", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotEqualTo(String value) {
            addCriterion("ext_attr1 <>", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1GreaterThan(String value) {
            addCriterion("ext_attr1 >", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr1 >=", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1LessThan(String value) {
            addCriterion("ext_attr1 <", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1LessThanOrEqualTo(String value) {
            addCriterion("ext_attr1 <=", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1In(List<String> values) {
            addCriterion("ext_attr1 in", values, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotIn(List<String> values) {
            addCriterion("ext_attr1 not in", values, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1Between(String value1, String value2) {
            addCriterion("ext_attr1 between", value1, value2, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotBetween(String value1, String value2) {
            addCriterion("ext_attr1 not between", value1, value2, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr2IsNull() {
            addCriterion("ext_attr2 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr2IsNotNull() {
            addCriterion("ext_attr2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr2EqualTo(String value) {
            addCriterion("ext_attr2 =", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotEqualTo(String value) {
            addCriterion("ext_attr2 <>", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2GreaterThan(String value) {
            addCriterion("ext_attr2 >", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr2 >=", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2LessThan(String value) {
            addCriterion("ext_attr2 <", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2LessThanOrEqualTo(String value) {
            addCriterion("ext_attr2 <=", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2In(List<String> values) {
            addCriterion("ext_attr2 in", values, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotIn(List<String> values) {
            addCriterion("ext_attr2 not in", values, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2Between(String value1, String value2) {
            addCriterion("ext_attr2 between", value1, value2, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotBetween(String value1, String value2) {
            addCriterion("ext_attr2 not between", value1, value2, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr3IsNull() {
            addCriterion("ext_attr3 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr3IsNotNull() {
            addCriterion("ext_attr3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr3EqualTo(String value) {
            addCriterion("ext_attr3 =", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotEqualTo(String value) {
            addCriterion("ext_attr3 <>", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3GreaterThan(String value) {
            addCriterion("ext_attr3 >", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr3 >=", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3LessThan(String value) {
            addCriterion("ext_attr3 <", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3LessThanOrEqualTo(String value) {
            addCriterion("ext_attr3 <=", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3In(List<String> values) {
            addCriterion("ext_attr3 in", values, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotIn(List<String> values) {
            addCriterion("ext_attr3 not in", values, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3Between(String value1, String value2) {
            addCriterion("ext_attr3 between", value1, value2, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotBetween(String value1, String value2) {
            addCriterion("ext_attr3 not between", value1, value2, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Integer value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Integer value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Integer value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Integer value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Integer value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Integer> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Integer> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
