package com.sys.permission.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.SpringUtils;
import com.sys.permission.business.PredefineDataPermissionCache;
import com.sys.permission.dao.WxSubjectResourcePermissionMapper;
import com.sys.permission.model.WxSubjectResourcePermission;
import com.sys.permission.model.WxSubjectResourcePermissionExample;
import com.sys.permission.service.DataPermissionService;
import com.sys.permission.service.PermissionAuthorizeService;

@Service
public class PermissionAuthorizeServiceImpl implements PermissionAuthorizeService {
	private Logger logger = LoggerFactory.getLogger(PermissionAuthorizeServiceImpl.class);
	
	@Resource
	private WxSubjectResourcePermissionMapper wxSubjectResourcePermissionMapper;

	@Override
	public Map<String, Object> getPermissions(String subjectType, Long subjectId, boolean withTreeData) {
		Map<String, Object> map = new HashMap<String, Object>(8);
		try {
			WxSubjectResourcePermissionExample example = new WxSubjectResourcePermissionExample();
			example.createCriteria().andSubjectIdEqualTo(subjectId).andSubjectTypeEqualTo(subjectType);
			map.put("data", wxSubjectResourcePermissionMapper.selectByExample(example));
			if(withTreeData){
				DataPermissionService dataPermissionServiceImpl = SpringUtils.getBean("dataPermissionServiceImpl");
				Map<String, Object> treeDataMap = dataPermissionServiceImpl.findInitialDatapPermissionTree();
				if("success".equals(treeDataMap.get("code"))){
					map.put("treeData", treeDataMap.get("resultData"));
					map.put("permissions", PredefineDataPermissionCache.getInstance().getPermissionTypeMap());
				}else{
					throw new RuntimeException("获取资源树失败");
				}
			}
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
			map.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
		}
		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> savePermission(WxSubjectResourcePermission[] records) {
		Map<String, Object> map = new HashMap<String, Object>();
		try{
			for(WxSubjectResourcePermission record : records){
				if(null == record.getPermissionId()){
					record.setCreationTime(DateUtil.getCurrentDate());
					record.setCreatedBy(ContextUtil.getCurUserId());
					wxSubjectResourcePermissionMapper.insertSelective(record);
					map.put("code", "success");
				} else {
					record.setCreationTime(null);
					record.setCreatedBy(null);
					wxSubjectResourcePermissionMapper.updateByPrimaryKeySelective(record);
					map.put("code", "success");
				}
			}
		} catch (Exception e) {
			map.put("code", "systemerror");
			e.printStackTrace();
			map.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(MessageResourceUtil.getMessage("system.unexpected_exception"), e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return map;
	}

}
