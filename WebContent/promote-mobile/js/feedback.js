; 'use strict';

$(function() {
	activity_feedback_widget.init();
});

var activity_feedback_widget = (function($) {
	
	var activityType = null, 
	form = null,
	activityData = null,
	util = null,
	loader = null,
	action = null;
	
	var req = {
			getData: function(callback) {
				LoadMask.show();
				var uuid = util.getURLParameter("id");
				$.ajax({
					dataType: 'json',
					type: 'get',
					contentType: 'application/json',
					url: '/promoteServCtrl/getActivityInfoMobile.do',
					data: {activityUUid: uuid},
					success : function (data) {
						LoadMask.hide();
						$.isFunction(callback) && callback(data);
	                },
				})
			},
			submit : function (data, callback) {
				LoadMask.show();
				$.ajax({
					dataType: 'json',
					type: 'post',
					contentType: 'application/json',
					url: '/promoteServCtrl/submitActivityFeedBack.do',
					data: JSON.stringify(data),
					success : function (data) {
						LoadMask.hide();
						$.isFunction(callback) && callback(data);
	                },
				})
			},
	},
	util = {
			getURLParameter: function(name) {
				return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.search)||[,""])[1].replace(/\+/g, '%20'))||null;
			},
			getActivityImg: function() {
				var activityImgIds = $("#fileList").val();
				if(null == activityImgIds || '' == activityImgIds) {
					return null;
				}
				activityImgIds = activityImgIds.substr(0, activityImgIds.length - 1);
				return activityImgIds;
			},
			a14ApplyForm: function() {
				var a14FileList = $("#a14FileList").val();
				if(null == a14FileList || '' == a14FileList) {
					return null;
				}
				a14FileList = a14FileList.substr(0, a14FileList.length - 1);
				return a14FileList;
			},
			complianceInvoice: function() {
				var invoiceFileList = $("#invoiceFileList").val();
				if(null == invoiceFileList || '' == invoiceFileList) {
					return null;
				}
				invoiceFileList = invoiceFileList.substr(0, invoiceFileList.length - 1);
				return invoiceFileList;
			},
			conferenceSchedule: function() {
				var shcheduleFileList = $("#shcheduleFileList").val();
				if(null == shcheduleFileList || '' == shcheduleFileList) {
					return null;
				}
				shcheduleFileList = shcheduleFileList.substr(0, shcheduleFileList.length - 1);
				return shcheduleFileList;
			},
			checkInSlip: function() {
				var checkInListFileList = $("#checkInListFileList").val();
				if(null == checkInListFileList || '' == checkInListFileList) {
					return null;
				}
				checkInListFileList = checkInListFileList.substr(0, checkInListFileList.length - 1);
				return checkInListFileList;
			},
			conferencePhoto: function() {
				var comferencePhotoFileList = $("#comferencePhotoFileList").val();
				if(null == comferencePhotoFileList || '' == comferencePhotoFileList) {
					return null;
				}
				comferencePhotoFileList = comferencePhotoFileList.substr(0, comferencePhotoFileList.length - 1);
				return comferencePhotoFileList;
			},
			hotelFeeStatement: function() {
				var hotelFeeFileList = $("#hotelFeeFileList").val();
				if(null == hotelFeeFileList || '' == hotelFeeFileList) {
					return null;
				}
				hotelFeeFileList = hotelFeeFileList.substr(0, hotelFeeFileList.length - 1);
				return hotelFeeFileList;
			},
			tripartiteAgreement: function() {
				var tripartileFileList = $("#tripartileFileList").val();
				if(null == tripartileFileList || '' == tripartileFileList) {
					return null;
				}
				tripartileFileList = tripartileFileList.substr(0, tripartileFileList.length - 1);
				return tripartileFileList;
			},
			proofPurchase: function() {
				var poorfFileList = $("#poorfFileList").val();
				if(null == poorfFileList || '' == poorfFileList) {
					return null;
				}
				poorfFileList = poorfFileList.substr(0, poorfFileList.length - 1);
				return poorfFileList;
			}
	},
	loader = {
			initActivityTypeText: function(callback) {
				req.getData(function(data) {
					if(true == data.success) {
						activityData = data.result;
						$("#activityTypeLbl").text(activityData.activityTypeName);
						if("YTH" == activityData.activityType) {
							$("#activityPicUpload").hide();
							$("#a14Upload").show();
							$("#invoiceUpload").show();
							$("#shcheduleUpload").show();
							$("#checkInListUpload").show();
							$("#comferencePhotoUpload").show();
							$("#hotelFeeUpload").show();
							$("#tripartileUpload").show();
							$("#poorfUpload").show();
						} else {
							$("#activityPicUpload").show();
							$("#a14Upload").hide();
							$("#invoiceUpload").hide();
							$("#shcheduleUpload").hide();
							$("#checkInListUpload").hide();
							$("#comferencePhotoUpload").hide();
							$("#hotelFeeUpload").hide();
							$("#tripartileUpload").hide();
							$("#poorfUpload").hide();
						}
						callback();
					} else {
						alert("获取数据失败，请重试！");
					}
				});
			},
			renderForm: function() {
				BUI && BUI.use('bui/form', function(Form) {
					form = new Form.Form({
						srcNode: '#activityFeedbackForm',
					}).render();
				});
			},
			uploaderFile: function() {
				BUI.use('bui/uploader',function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#uploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '17', sourceId: activityData.activityId},
				        success: function(result){
				        	var fileStr = $("#fileList").val();
				        	fileStr += result.attachmentFileList[0].attId + ",";
				        	$("#fileList").val(fileStr);
				        },
				        error: function(result) {
				        	$("#uploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			a14Uploder: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#a14Uploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var a14FileList = $("#a14FileList").val();
				        	a14FileList += result.attachmentFileList[0].attId + ",";
				        	$("#a14FileList").val(a14FileList);
				        },
				        error: function(result){
				      	  $("#a14Uploader").find("li").remove();
				      	  alert("附近上传失败");
				        }
				    });
					uploader.render();
				});
			},
			invoiceUploder: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#invoiceUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var invoiceFileList = $("#invoiceFileList").val();
				        	invoiceFileList += result.attachmentFileList[0].attId + ",";
				        	$("#invoiceFileList").val(invoiceFileList);
				        },
				        error: function(result) {
				        	$("#invoiceUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			shcheduleUploader: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#shcheduleUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var shcheduleFileList = $("#shcheduleFileList").val();
				        	shcheduleFileList += result.attachmentFileList[0].attId + ",";
				        	$("#shcheduleFileList").val(shcheduleFileList);
				        },
				        error: function(result) {
				        	$("#shcheduleUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			checkInListUploader: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#checkInListUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var checkInListFileList = $("#checkInListFileList").val();
				        	checkInListFileList += result.attachmentFileList[0].attId + ",";
				        	$("#checkInListFileList").val(checkInListFileList);
				        },
				        error: function(result) {
				        	$("#checkInListUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			comferencePhotoUploader: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#comferencePhotoUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var comferencePhotoFileList = $("#comferencePhotoFileList").val();
				        	comferencePhotoFileList += result.attachmentFileList[0].attId + ",";
				        	$("#comferencePhotoFileList").val(comferencePhotoFileList);
				        },
				        error: function(result) {
				        	$("#comferencePhotoUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			hotelFeeUploader: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#hotelFeeUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var hotelFeeFileList = $("#hotelFeeFileList").val();
				        	hotelFeeFileList += result.attachmentFileList[0].attId + ",";
				        	$("#hotelFeeFileList").val(hotelFeeFileList);
				        },
				        error: function(result) {
				        	$("#hotelFeeUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			tripartileUploader: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#tripartileUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var tripartileFileList = $("#tripartileFileList").val();
				        	tripartileFileList += result.attachmentFileList[0].attId + ",";
				        	$("#tripartileFileList").val(tripartileFileList);
				        },
				        error: function(result) {
				        	$("#tripartileUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			},
			poorfUploader: function() {
				BUI.use('bui/uploader', function (Uploader) {
					var uploader = new Uploader.Uploader({
				        render: '#poorfUploader',
				        url: '../../uploadForAppAttchmentFile.do',
				        name: 'myfiles',
				        width: '450px',
				        data: {sourceType: '18', sourceId: activityData.activityId},
				        success: function(result){
				        	var poorfFileList = $("#poorfFileList").val();
				        	poorfFileList += result.attachmentFileList[0].attId + ",";
				        	$("#poorfFileList").val(poorfFileList);
				        },
				        error: function(result) {
				        	$("#poorfUploader").find("li").remove();
				      	    alert("附近上传失败");
					    }
				    });
					uploader.render();
				});
			}
	},
	action = {
			submit: function() {
				form.valid();
				if(form.isValid()) {
					var formData = $.extend({}, form.getRecord(), {});
					delete formData["undefined"];
					if("YTH" == activityData.activityType) {
						
						var a14Str = util.a14ApplyForm();
						if(a14Str) {
							formData["a14ApplyForm"] = a14Str;
						} else {
							alert("请上传14A申请表");
							return false;
						}
						
						var invoiceStr = util.complianceInvoice();
						if(invoiceStr) {
							formData["complianceInvoice"] = invoiceStr;
						} else {
							alert("请上传合规发票");
							return;
						}
						
						var scheduleStr = util.conferenceSchedule();
						if(scheduleStr) {
							formData["conferenceSchedule"] = scheduleStr;
						}
						
						var checkInStr = util.checkInSlip();
						if(checkInStr) {
							formData["checkInSlip"] = checkInStr;
						}
						
						var conferencePhotoStr = util.conferencePhoto();
						if(conferencePhotoStr) {
							formData["conferencePhoto"] = conferencePhotoStr;
						}
						
						var hotelFeeStr = util.hotelFeeStatement();
						if(checkInStr) {
							formData["hotelFeeStatement"] = hotelFeeStr;
						}
						
						var tripartiteStr = util.tripartiteAgreement();
						if(checkInStr) {
							formData["tripartiteAgreement"] = tripartiteStr;
						}
						
						var proofStr = util.proofPurchase();
						if(checkInStr) {
							formData["proofPurchase"] = proofStr;
						}
					} else {
						var activityImgIds = util.getActivityImg();
						if(activityImgIds) {
							formData["activityImgIds"] = activityImgIds;
						} else {
							alert("请上传活动文件");
							return false;
						}
					}
					formData["activityId"] = activityData.activityId;
					formData["activityType"] = activityData.activityType;
					formData["applyBatchId"] = activityData.applyBatchId;
					req.submit(formData, function(data){
						if(data.success == true) {
							window.location.href = "/promote-mobile/success.html";
						} else {
							alert("提交失败，请重试！");
							return false;
						}
					});
				}
			}
	};
	
	return {
		init: function() {
			loader.initActivityTypeText(function () {
				if("YTH" == activityData.activityType) {
					loader.a14Uploder();
					loader.invoiceUploder();
					loader.shcheduleUploader();
					loader.checkInListUploader();
					loader.comferencePhotoUploader();
					loader.hotelFeeUploader();
					loader.tripartileUploader();
					loader.poorfUploader();
				} else {
					loader.uploaderFile();
				}
				loader.renderForm();
			});
		},
		submit: function() {
			action.submit();
		}
	}
	
}($));