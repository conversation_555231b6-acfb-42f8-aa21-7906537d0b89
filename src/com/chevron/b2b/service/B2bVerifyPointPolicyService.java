package com.chevron.b2b.service;

import com.chevron.b2b.model.B2bVerifyPointPolicy;

import java.util.List;
import java.util.Map;

/**
 * B2B核销积分政策操作RPC接口
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
public interface B2bVerifyPointPolicyService {

	/**
	 * 保存B2B核销积分政策信息
	 * @param record B2B核销积分政策对象
	 * @return 处理结果
	 */
	public Map<String, Object> save(B2bVerifyPointPolicy record);
	
	/**
	 * 删除B2B核销积分政策对象
	 * @param ids 被删除B2B核销积分政策对象ID集合
	 * @return 处理结果
	 */
	public Map<String, Object> delete(List<Long> ids);
}
