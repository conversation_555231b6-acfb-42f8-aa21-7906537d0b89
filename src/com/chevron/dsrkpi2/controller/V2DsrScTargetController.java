package com.chevron.dsrkpi2.controller;

import com.chevron.dsrkpi2.business.V2DsrScTargetBizService;
import com.chevron.dsrkpi2.model.V2DsrScTarget;
import com.chevron.dsrkpi2.model.V2DsrScTargetParams;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.chevron.importdata.ImportDataUtil;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.FileUtil;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.file.model.WxAttFile;
import com.sys.file.web.FileManager;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DSR服务费目标Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2021-03-31 10:10
 */
@Controller
@Api(value = "DSR服务费目标Controller", tags = "DSR服务费目标Controller")
@RequestMapping(value="/v2dsrsctarget")
public class V2DsrScTargetController {
	@Autowired
	private V2DsrScTargetBizService v2DsrScTargetBizService;
	
	private final static Logger log = Logger.getLogger(V2DsrScTargetController.class);

	/**
	 * 按月分组DSR服务费目标列表页面分页查询接口
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@ApiOperation(value="按月分组DSR服务费目标列表页面分页查询接口",  httpMethod="POST", notes="按月分组DSR服务费目标列表页面分页查询接口")
	@ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/queryForPageGroup.do")
	public ResponseMap queryForPageGroup(@ApiParam(name="params", value="查询对象", required=true) @RequestBody V2DsrScTargetParams params, HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("queryForPageGroup: " + JsonUtil.writeValue(params));

		try {
			v2DsrScTargetBizService.queryForPageGroup(params, resultMap);
			log.info("queryForPageGroup success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(),
					"com.chevron.dsrkpi2.controller.V2DsrScTargetController.queryForPageGroup", JsonUtil.writeValue(params));
		}
		return resultMap;
	}

	/**
	 * DSR服务费目标列表页面分页查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
    @ApiOperation(value="DSR服务费目标列表页面分页查询接口",  httpMethod="POST", notes="DSR服务费目标列表页面分页查询接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/data.do")
	public ResponseMap queryForPage(@ApiParam(name="params", value="查询对象", required=true) @RequestBody V2DsrScTargetParams params, HttpServletRequest request){
		ResponseMap resultMap = new ResponseMap();
		log.info("queryForPage: " + JsonUtil.writeValue(params));

		try {
			v2DsrScTargetBizService.queryForPage(params, resultMap);
			log.info("queryForPage success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi2.controller.V2DsrScTargetController.queryForPage", JsonUtil.writeValue(params));
		}
		return resultMap;
	}

	/**
	 * 保存DSR服务费目标信息
	 * @param record DSR服务费目标对象
	 * @return 处理结果
	 */
	@ResponseBody
    @ApiOperation(value="保存DSR服务费目标信息接口",  httpMethod="POST", notes="保存DSR服务费目标信息接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/save.do")
	public ResponseMap save(@ApiParam(name="record", value="DSR服务费目标对象", required=true) @RequestBody V2DsrScTarget record) {
		ResponseMap map = new ResponseMap();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				v2DsrScTargetBizService.insert(record);
			}else{
				v2DsrScTargetBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi2.controller.V2DsrScTargetController.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	/**
	 * 修改页面保存DSR服务费目标信息
	 * @param record DSR服务费目标对象
	 * @return 处理结果
	 */
	@ResponseBody
    @ApiOperation(value="修改页面保存DSR服务费目标信息接口",  httpMethod="POST", notes="修改页面保存DSR服务费目标信息接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/saveforedit.do")
	public ResponseMap saveForEditPage(@ApiParam(name="record", value="DSR服务费目标对象", required=true) @RequestBody V2DsrScTarget record) {
		ResponseMap map = new ResponseMap();
		log.info("saveForEditPage: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				v2DsrScTargetBizService.insert(record);
			}else{
				v2DsrScTargetBizService.updateForEditPage(record);
			}
			log.info("saveForEditPage success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi2.controller.V2DsrScTargetController.saveForEditPage", JsonUtil.writeValue(record));
		}
		return map;
	}

	/**
	 * 删除DSR服务费目标对象
	 * @param ids 被删除DSR服务费目标对象ID集合
	 * @return 处理结果
	 */
	@ResponseBody
    @ApiOperation(value="删除DSR服务费目标对象接口",  httpMethod="POST", notes="删除DSR服务费目标对象接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/delete.do")
	public ResponseMap delete(@ApiParam(name="ids", value="被删除DSR服务费目标对象ID集合", required=true) @RequestParam("ids") Long[] ids) {
		ResponseMap map = new ResponseMap();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			v2DsrScTargetBizService.delete(Arrays.asList(ids));
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi2.controller.V2DsrScTargetController.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}

	@ResponseBody
	@ApiOperation(value="导入奖励接口",  httpMethod="POST", notes="导入奖励接口")
	@ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value = "/importDsrScTarget.do", method = RequestMethod.POST)
	public Object importDsrScTarget(final @RequestParam("myfiles") MultipartFile[] myfiles, HttpServletRequest request,
									 HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(5);
		try {
			final Long userId = ContextUtil.getCurUser().getUserId();
			Map<String, Object> repsMap = ImportDataUtil.getImportDataByReflect1(FileManager.preProcessingWb(myfiles[0]),
					V2DsrScTarget.class, new String[]{
							"dsrAccount", "v1", "v2", "v3", "v4", "v5", "v6", "v7", "v8", "v9", "v10", "v11", "v12"},
					1, 0);
			if("dataInvalid".equals(repsMap.get("result"))){
				throw new WxPltException("Excel解析异常");
			}
			@SuppressWarnings("unchecked")
			final List<V2DsrScTarget> importList = (List<V2DsrScTarget>) repsMap.get("datalst");
			//保存Excel
			String fileName = myfiles[0].getOriginalFilename();
			String fileType = myfiles[0].getContentType();
			String storageName = StringUtils.getSeqNextval();
			String fileSuffix = StringUtils.getFileSuffixName(fileName);
			String storePath = FileUtil.getFolderName(GregorianCalendar.DATE);
			storageName = storageName + "." + fileSuffix;
			String realPath = FileManager.getFileRealPath(WxAttFile.SourceType_Excel, storePath);

			try {
				FileUtil.storeFile(realPath, storageName, myfiles[0].getInputStream(),
						WxAttFile.SourceType_Excel, fileType);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

			try {
				v2DsrScTargetBizService.importDsrScTarget(importList, userId);
				log.info("importDsrScTarget success. ");
				resultMap.put("code", "success");
			}catch (Exception e){
				log.error("importDsrScTarget fail: " + e.getMessage());
				resultMap.put("code", "error");
				resultMap.put("codeMsg", e.getMessage());
			}
		} catch (Exception e) {
			log.error("importDsrScTarget fail: " + e.getMessage());
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}


	@ApiOperation(value="下载奖励接口",  httpMethod="POST", notes="下载奖励接口")
	@ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value = "/exportDsrScTarget.do")
	public String exportDsrScTarget( @ApiParam(name="fileName", value="下载文件名称", required=true) @RequestParam("fileName")String fileName,
								 HttpServletRequest request, HttpServletResponse response){

		//初始化参数
		try {
			//组装表头
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("partnerName", "经销商名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);

			exportCol = new ExportCol("region", "区域");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("saleName", "销售");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("dsrName", "DSR名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("dsrAccount", "DSR账号");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v", "总奖励");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v1", "1月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v2", "2月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v3", "3月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v4", "4月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v5", "5月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v6", "6月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v7", "7月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v8", "8月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v9", "9月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v10", "10月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v11", "11月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("v12", "12月");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			//组装数据
			List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();

			V2DsrScTargetParams params = new V2DsrScTargetParams();
			params.setPaging(false);
			params.closeOrder();
			List<V2DsrScTarget> list = v2DsrScTargetBizService.queryForPageGroup(params);

			if(!CollectionUtils.isEmpty(list)){
				for (V2DsrScTarget target: list) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("partnerName",target.getPartnerName());
					map.put("region",target.getRegion());
					map.put("saleName",target.getSaleName());
					map.put("dsrName",target.getDsrName());
					map.put("dsrAccount",target.getDsrAccount());
					map.put("v",target.getV());
					map.put("v1",target.getV1());
					map.put("v2",target.getV2());
					map.put("v3",target.getV3());
					map.put("v4",target.getV4());
					map.put("v5",target.getV5());
					map.put("v6",target.getV6());
					map.put("v7",target.getV7());
					map.put("v8",target.getV8());
					map.put("v9",target.getV9());
					map.put("v10",target.getV10());
					map.put("v11",target.getV11());
					map.put("v12",target.getV12());
					dataList.add(map);
				}
			}

			CommonUtil.setExportResponseHeader(request, response, fileName, "xlsx");
			PoiWriteExcel.exportLargeData(dataList, exportCols, response.getOutputStream(), "KPI设置", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

	@ApiOperation(value="DSR服务费批量录入模板下载接口",  httpMethod="GET", notes="DSR服务费批量录入模板下载接口")
	@ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value = "/downExcelTemplate.do")
	public String downExcelTemplate(HttpServletRequest request, HttpServletResponse response, @ApiParam(name="fileName", value="下载文件名称", required=true) @RequestParam("fileName")String fileName){
		try {

			FileInputStream inStream = null;
			String basePath = request.getSession().getServletContext().getRealPath("/");//绝对路径
			File file = new File(basePath + "/app/sc_target_import.xlsx");
			try {
				inStream = new FileInputStream(file);
				byte[] buf = new byte[4096];
				int readLength;
				response.setContentType("application/vnd.ms-excel;charset=UTF-8");
				response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
				while (((readLength = inStream.read(buf)) != -1)) {
					response.getOutputStream().write(buf, 0, readLength);
				}
			}catch (Exception e){
				log.error(e.getMessage(), e);
				request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
				return "forward:/common/jsp/downloadError.jsp";
			}finally {
				try {
					inStream.close();
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
					return "forward:/common/jsp/downloadError.jsp";
				}
			}
			return null;
		}catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
	}
}
