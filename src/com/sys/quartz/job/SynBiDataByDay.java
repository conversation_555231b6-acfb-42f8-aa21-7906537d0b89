package com.sys.quartz.job;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.pms.dao.DmpMapper;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;
import com.common.util.WeixinMessageUtil;
import com.sys.log.business.LogBizService;
import com.sys.log.model.Log;

public class SynBiDataByDay {

	@Autowired
	private DmpMapper dmpMapper;
    
    @Autowired
    private LogBizService logBizService;
    
    private static Logger log = LoggerFactory.getLogger(SynBiDataByDay.class);
	
	public synchronized void execute(){
		final Log log = new Log();
		final Date now = DateUtil.getCurrentDate();
		try {
			dmpMapper.synBiData();
		} catch (Exception e) {
			log.setExtProperty3("ERROR");
			log.setExtProperty4(e.getMessage());
			SynBiDataByDay.log.error(e.getMessage(), e);
			WeixinMessageUtil.weixinAlarmMessagePush("PP-BI数据同步", "PP拉BI数据失败", "数据同步失败告警", "synBiData", e.getMessage());
		}
		log.setOperator(1l);
		log.setLogType("BI.sysLog");
		log.setExtProperty1("synData");
		log.setCreateTime(DateUtil.getCurrentDate());
		log.setExtProperty2((DateUtil.getCurrentDate().getTime() - now.getTime()) + "");
		try {
			logBizService.insert(log);
		} catch (WxPltException e) {
			e.printStackTrace();
		}
	}
}
