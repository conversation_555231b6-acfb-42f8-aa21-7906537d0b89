package com.sys.quartz.job;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.localmarketing.business.NonLocalMktEmaileInfoBizService;
import com.chevron.localmarketing.model.NonLocalMktEmaileInfo;
import com.chevron.localmarketing.model.NonLocalMktEmaileInfoVo;
import com.common.exception.WxPltException;
import com.common.util.DateUtils;
import com.common.util.ResponseMap;

import cn.hutool.core.collection.CollectionUtil;

public class SendNonLocMktWarnEmailTask {

	@Autowired
	private NonLocalMktEmaileInfoBizService nonLocalMktEmaileInfoBizService;
	
	private static Logger log = LoggerFactory.getLogger(SendNonLocMktWarnEmailTask.class);

	@SuppressWarnings("unchecked")
	public synchronized void execute(){
		log.info("SendNonLocMktWarnEmailTask job start....");
		try {
		//判断当天是否是当月第二周的第一天
		ResponseMap map = new ResponseMap();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("year", DateUtils.getYear(DateUtils.getCurrentDate()));
		params.put("month", DateUtils.getMonth(DateUtils.getCurrentDate()));
		params.put("weekName", "Monday");
		if(nonLocalMktEmaileInfoBizService.getTowWeekWorkDay(params)) {
			NonLocalMktEmaileInfoVo record = new NonLocalMktEmaileInfoVo();
			//如果是次年1月，需要发放前年12月数据
			String warnMonth = null;
			String warnYear = null;
			if("1".equals(DateUtils.getMonth(DateUtils.getCurrentDate()))) {
				warnMonth = "12";
				warnYear = (Integer.valueOf(DateUtils.getYear(DateUtils.getCurrentDate()))-1)+"";
				
			}else {
				warnMonth = (Integer.valueOf(DateUtils.getMonth(DateUtils.getCurrentDate()))-1)+"";
				warnYear = DateUtils.getYear(DateUtils.getCurrentDate());
			}
			record.setWarnYear(warnYear);
			record.setWarnMonth(warnMonth);
			record.setStart(0);
			record.setLimit(10000);
			record.setDirection("desc");
			record.setField("updateTime");
			record.setDeliverStatus(NonLocalMktEmaileInfo.LOCALMARKETING_DELIVER_STATUS_START1);
			record.setAttribute2(NonLocalMktEmaileInfo.LOCALMARKETING_FILE_STATUS_START1);
			record.setDeleteFlag(NonLocalMktEmaileInfo.LOCALMARKETING_DELETE_STATUS_START0);
			record.setOtherCustomer(1);
			nonLocalMktEmaileInfoBizService.getLocalMktEmailInfoByCondition(record,map);
			List<NonLocalMktEmaileInfo>  emaileInfos = (List<NonLocalMktEmaileInfo>) map.get("resultLst");
			Map<String, NonLocalMktEmaileInfo> sendMap = new HashMap<String, NonLocalMktEmaileInfo>();
			for (NonLocalMktEmaileInfo nonLocalMktEmaileInfo : emaileInfos) {
				StringBuffer stringBuffer = new StringBuffer();
				String infoKey = stringBuffer.append(nonLocalMktEmaileInfo.getFromDistributorId()).append("/").append(nonLocalMktEmaileInfo.getChannel()).toString();
				if(null == sendMap.get(infoKey)) {
					sendMap.put(infoKey, nonLocalMktEmaileInfo);
				}
			}
			List<NonLocalMktEmaileInfo> sendList= new ArrayList<NonLocalMktEmaileInfo>(sendMap.values());
			//发送邮件
			if(CollectionUtil.isNotEmpty(sendList)) {
				nonLocalMktEmaileInfoBizService.updateDeliverStatusAndSendEmaile(sendList);
			}
			log.info("SendNonLocMktWarnEmailTask success." );
		}
		} catch (WxPltException e) {
			e.printStackTrace();
			log.error("SendNonLocMktWarnEmailTask job break....", e);
		}
	}
}
