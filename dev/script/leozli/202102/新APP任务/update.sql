INSERT INTO [dbo].[wx_t_data]
           ([data_pid]
           ,[root_id]
           ,[data_name]
           ,[data_code]
           ,[data_descript]
           ,[data_order]
           ,[xg_sj]
           ,[xg_user]
           ,[status]
           ,[tenant_id])
     VALUES
           (37
           ,21
           ,'新店录入'
           ,'TT_2_7n3/'
           ,'合并品牌后的新店录入'
           ,9
           ,getdate()
           ,1
           ,1
           ,1);
INSERT INTO [dbo].[wx_t_data]
           ([data_pid]
           ,[root_id]
           ,[data_name]
           ,[data_code]
           ,[data_descript]
           ,[data_order]
           ,[xg_sj]
           ,[xg_user]
           ,[status]
           ,[tenant_id])
     VALUES
           (37
           ,21
           ,'车队及项目客户'
           ,'TT_2_7n7/'
           ,'车队及项目客户'
           ,9
           ,getdate()
           ,1
           ,1
           ,1);
INSERT INTO [dbo].[wx_t_data]
           ([data_pid]
           ,[root_id]
           ,[data_name]
           ,[data_code]
           ,[data_descript]
           ,[data_order]
           ,[xg_sj]
           ,[xg_user]
           ,[status]
           ,[tenant_id])
     VALUES
           (37
           ,21
           ,'门店走访'
           ,'TT_2_7ne/'
           ,'门店走访'
           ,9
           ,getdate()
           ,1
           ,1
           ,1);
INSERT INTO [dbo].[wx_t_data]
           ([data_pid]
           ,[root_id]
           ,[data_name]
           ,[data_code]
           ,[data_descript]
           ,[data_order]
           ,[xg_sj]
           ,[xg_user]
           ,[status]
           ,[tenant_id])
     VALUES
           (37
           ,21
           ,'车队及项目走访'
           ,'TT_2_7nf/'
           ,'车队及项目走访'
           ,9
           ,getdate()
           ,1
           ,1
           ,1);

INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'请拍摄完整门头照(包含门店完整名称)'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'photo'
           ,null);
update c set c.check_name='门店名称(门店全称，与门头照的名称显示一致)'
from wx_task_check c where check_id='23071848'

update c set c.check_name='陈列照：(要求陈列产品和陈列架周边环境清晰：1.包含雪佛龙产品。2.产品陈列在陈列架上。3.包含陈列架周围的环境。)'
from wx_task_check c where check_id='23071892' 

INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'客户润滑油每月总用量(升)'
           ,'客户润滑油每月总用量(升)'
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'textfield'
           ,'number');
INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'雪佛龙润滑油每月总销量(升)'
           ,'雪佛龙润滑油每月总销量(升)'
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'textfield'
           ,'number');    
INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'其它在用油竞品品牌'
           ,'其它在用油竞品品牌（非必填，可多选）'
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": true,     "datasourceType": "data",     "data": [         {             "text": "美孚",             "value": "美孚"         },         {             "text": "嘉实多",             "value": "嘉实多"         },         {             "text": "壳牌",             "value": "壳牌"         },         {             "text": "长城",             "value": "长城"         },         {             "text": "昆仑",             "value": "昆仑"         },         {             "text": "道达尔",             "value": "道达尔"         },         {             "text": "福斯",             "value": "福斯"         },         {             "text": "胜牌",             "value": "胜牌"         },         {             "text": "统一",             "value": "统一"         },         {             "text": "马石油",             "value": "马石油"         },         {             "text": "矫马",             "value": "矫马"         },         {             "text": "原厂油",             "value": "原厂油"         },         {             "text": "其他",             "value": "其他"         }     ],     "textField": "text",     "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队或公司照'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,'photoId'
           ,'photo'
           ,'');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队或项目名称'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'workShopName'
           ,'textfield'
           ,'');    
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队或项目地址(点击确认及定位地址)'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,'workshopAddress'
           ,'textfield'
           ,'');       
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队或项目联系人'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'contactPerson'
           ,'textfield'
           ,'');     
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队或项目联系人电话'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'contactPersonTel'
           ,'textfield'
           ,'phone');
INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'主要设备类型'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'options_with_other'
           ,'{     "otherText":"其它","showType": "checkbox",     "multiSelect": true,     "datasourceType": "data",     "data": [         {             "text": "重卡",             "value": "重卡"         },         {             "text": "轻卡",             "value": "轻卡"         },         {             "text": "巴士",             "value": "巴士"         },         {             "text": "其它",             "value": "其它"         }     ],     "textField": "text",     "valueField": "value" }'); 
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'门店类型'
           ,''
           ,990
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'type'
           ,'options'
           ,'{  "showType": "checkbox",  "multiSelect": false,  "datasourceType": "data",  "data": [{    "text": "4S店",    "value": "4S店"   },   {    "text": "快修保养店",    "value": "快修保养店"   },   {    "text": "综合修理厂",    "value": "综合修理厂"   },   {    "text": "汽车美容店",    "value": "汽车美容店"   },   {    "text": "汽配店",    "value": "汽配店"   },   {    "text": "二级分销商",    "value": "二级分销商"   },   {    "text": "其他",    "value": "其他"   }  ],  "textField": "text",  "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'门店类型'
           ,''
           ,991
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'type'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": false,     "datasourceType": "data",     "data": [         {             "text": "修理厂",             "value": "修理厂"         },         {             "text": "配件店",             "value": "配件店"         },         {             "text": "二级分销商",             "value": "二级分销商"         },         {             "text": "其他",             "value": "其他"         }     ],     "textField": "text",     "valueField": "value" }');           
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'门店类型'
           ,''
           ,992
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'type'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": false,     "datasourceType": "data",     "data": [         {             "text": "修理厂",             "value": "修理厂"         },         {             "text": "配件店",             "value": "配件店"         },         {             "text": "二级分销商",             "value": "二级分销商"         },         {             "text": "其他",             "value": "其他"         }     ],     "textField": "text",     "valueField": "value" }');           
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队及项目类型'
           ,''
           ,990
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,'type'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": false,     "datasourceType": "data",     "data": [         {             "text": "普通物流车队",             "value": "普通物流车队"         },         {             "text": "危险品运输车队",             "value": "危险品运输车队"         },         {             "text": "公交巴士车队",             "value": "公交巴士车队"         },         {             "text": "长途巴士车队",             "value": "长途巴士车队"         },         {             "text": "商旅租赁车队",             "value": "商旅租赁车队"         },         {             "text": "其他",             "value": "其他"         }     ],     "textField": "text",     "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队及项目类型'
           ,''
           ,991
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,'type'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": false,     "datasourceType": "data",     "data": [         {             "text": "私人车队",             "value": "私人车队"         },         {             "text": "混凝土搅拌站",             "value": "混凝土搅拌站"         },         {             "text": "工程公司",             "value": "工程公司"         },         {             "text": "矿山客户",             "value": "矿山客户"         },         {             "text": "整机维保商",             "value": "整机维保商"         },         {             "text": "其他",             "value": "其他"         }     ],     "textField": "text",     "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'主要销售/使用产品'
           ,''
           ,990
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'extProperty10'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": true,     "datasourceType": "data",     "data": [         {             "text": "金富力金盾",             "value": "金富力金盾"         },         {             "text": "金富力银盾",             "value": "金富力银盾"         },         {             "text": "金富力方程式",             "value": "金富力方程式"         },         {             "text": "金富力超级精选",             "value": "金富力超级精选"         },         {             "text": "金富力超级加强型",             "value": "金富力超级加强型"         },         {             "text": "金富力加强型",             "value": "金富力加强型"         },         {             "text": "金富力多用途全合成自动变速箱油",             "value": "金富力多用途全合成自动变速箱油"         },             {             "text": "金富力全合成无级变速箱油",             "value": "金富力全合成无级变速箱油"         },         {             "text": "金富力全合成手动变速箱油 75W-90",             "value": "金富力全合成手动变速箱油 75W-90"         },         {             "text": "金富力手动变速箱油 80W90",             "value": "金富力手动变速箱油 80W90"         },         {             "text": "金富力湿式双离合变速箱油",             "value": "金富力湿式双离合变速箱油"         },         {             "text": "自动变速箱油 1888",             "value": "自动变速箱油 1888"         },         {             "text": "金富力长效防冻防锈液",             "value": "金富力长效防冻防锈液"         },         {             "text": "刹车液",             "value": "刹车液"         }     ],     "textField": "text",     "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'主要销售/使用产品'
           ,''
           ,991
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'extProperty10'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": true,     "datasourceType": "data",     "data": [         {             "text": "德乐600超低灰合成机油",             "value": "德乐600超低灰合成机油"         },         {             "text": "德乐400超霸全合成",             "value": "德乐400超霸全合成"         },         {             "text": "德乐400超霸合成型",             "value": "德乐400超霸合成型"         },         {             "text": "德乐400超霸",             "value": "德乐400超霸"         },         {             "text": "德乐400 MGX",             "value": "德乐400 MGX"         },         {             "text": "超级金德乐",             "value": "超级金德乐"         },         {             "text": "金德乐",             "value": "金德乐"         },         {             "text": "银德乐",             "value": "银德乐"         },         {             "text": "德乐长效重载防冻防锈液",             "value": "德乐长效重载防冻防锈液"         },         {             "text": "德乐长效防冻防锈液",             "value": "德乐长效防冻防锈液"         },         {             "text": "德乐特级极压齿轮油",             "value": "德乐特级极压齿轮油"         },         {             "text": "德乐高级齿轮油",             "value": "德乐高级齿轮油"         },         {             "text": "雪佛龙特级极压齿轮油",             "value": "雪佛龙特级极压齿轮油"         },         {             "text": "工程机械专用特级液压油 AW",             "value": "工程机械专用特级液压油 AW"         },         {             "text": "雪佛龙抗磨液压油 AW",             "value": "雪佛龙抗磨液压油 AW"         },         {             "text": "雪佛龙特级抗磨液压油 HD",             "value": "雪佛龙特级抗磨液压油 HD"         }     ],     "textField": "text",     "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'主要销售/使用产品'
           ,''
           ,993
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,'workShop'
           ,'extProperty10'
           ,'options'
           ,'{     "showType": "checkbox",     "multiSelect": true,     "datasourceType": "data",     "data": [         {             "text": "德乐400超霸",             "value": "德乐400超霸"         },         {             "text": "德乐400 MGX",             "value": "德乐400 MGX"         },         {             "text": "超级金德乐",             "value": "超级金德乐"         },         {             "text": "金德乐",             "value": "金德乐"         },         {             "text": "银德乐",             "value": "银德乐"         },         {             "text": "工程机械专用特级柴油机油 CH-4",             "value": "工程机械专用特级柴油机油 CH-4"         },         {             "text": "工程机械专用超级柴油机油 CI-4",             "value": "工程机械专用超级柴油机油 CI-4"         },         {             "text": "工程机械专用特级液压油 AW",             "value": "工程机械专用特级液压油 AW"         },         {             "text": "雪佛龙特级抗磨液压油 HD",             "value": "雪佛龙特级抗磨液压油 HD"         },         {             "text": "特级宽温抗磨液压油 MV",             "value": "特级宽温抗磨液压油 MV"         },         {             "text": "超级宽温抗磨液压油 HDZ",             "value": "超级宽温抗磨液压油 HDZ"         },         {             "text": "德乐特级极压齿轮油",             "value": "德乐特级极压齿轮油"         },           {             "text": "德乐高级齿轮油",             "value": "德乐高级齿轮油"         },           {             "text": "雪佛龙特级极压齿轮油",             "value": "雪佛龙特级极压齿轮油"         },         {             "text": "雪佛龙工业齿轮油",             "value": "雪佛龙工业齿轮油"         },         {             "text": "德乐长效重载防冻防锈液",             "value": "德乐长效重载防冻防锈液"         },         {             "text": "德乐长效防冻防锈液",             "value": "德乐长效防冻防锈液"         }     ],     "textField": "text",     "valueField": "value" }');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'请拍摄完整门头照(包含门店完整名称)'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'photo'
           ,'');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'请拍摄公司名称或工地照片'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'photo'
           ,'');
 INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'请拍摄雪佛龙产品照片'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'photo'
           ,'');           
    INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'请完整说明设备德品牌,类型及数量'
           ,'请完整说明设备德品牌'
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'textfield'
           ,'');    
    INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'之前使用油品品牌,型号及换油周期(公里数或小时)'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'textfield'
           ,'');                  
    INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'使用雪佛龙油品品牌,型号及换油周期(公里数或小时数)'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'textfield'
           ,'');                  
    INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'车队或项目照片'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'photo'
           ,''); 
    INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'请拍摄车辆或设备照片'
           ,''
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'photo'
           ,''); 
    INSERT INTO [dbo].[wx_task_check]
           ([check_code]
           ,[level]
           ,[check_pid]
           ,[check_name]
           ,[check_descript]
           ,[check_order]
           ,[create_user]
           ,[create_time]
           ,[xg_user]
           ,[xg_sj]
           ,[status]
           ,[tenant_id]
           ,[vo_name]
           ,[property_name]
           ,[option_type]
           ,[option_value])
     VALUES
           ('-1'
           ,2
           ,99
           ,'设备品牌'
           ,'手动输入设备品牌'
           ,999
           ,1
           ,getdate()
           ,1
           ,getdate()
           ,1
           ,1
           ,null
           ,null
           ,'textfield'
           ,''); 

update mb set mb.brand=1
from wx_task_mb mb where mb_name like '%金富力%' and tmb_type_code!='TT_2_CDMCLZX';

update mb set mb.brand=2
from wx_task_mb mb where mb_name like '%德乐%' and tmb_type_code!='TT_2_CDMCLZX';

update mb set mb.brand=4
from wx_task_mb mb where mb_name like '%工程机械%' and tmb_type_code!='TT_2_GC_JX';

update s set s.submit_time=s.xg_sj
from wx_task_sub s where s.task_status=4;

insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('DsrKpiV2.kpiCode', 'DSR指标配置', 'DSR指标配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('DsrKpiV2.kpiCode', 'EFFECTIVE_VISIT_DAYS', '有效拜访天数', '天', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('DsrKpiV2.kpiCode', 'NEW_CUSTOMERS', '录入新客户数', '家', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('DsrKpiV2.kpiCode', 'CONFIRMED_SELL_THROUGH', '已确认销量', '升', '1');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status) values ('DsrKpiV2.kpiCode', 'CONFIRMED_SELL_THROUGH_HERO', '已确认英雄产品销量', '升', '1');

update p set p.ext_flag=0 from wx_t_product p where p.ext_flag is null
update wx_t_product set ext_flag=ext_flag|2 where sku='500175LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500175NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500175TAK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500185LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500185NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500185TAK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500186LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500186NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500186TAK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500269LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='500269NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503048LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503048NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503049LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503049NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503051LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503051NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503052LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503052NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='510725UVL'
update wx_t_product set ext_flag=ext_flag|2 where sku='510729PKL'
update wx_t_product set ext_flag=ext_flag|2 where sku='510729PLL'
update wx_t_product set ext_flag=ext_flag|2 where sku='513002LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513002NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513003HRK'
update wx_t_product set ext_flag=ext_flag|2 where sku='513003LMB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513003NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513005LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513005NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513006LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513006NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='513011NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503048LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500269LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503049LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500175LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503031LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500263LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500264LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500185LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500186LPK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503049NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503048NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503031NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500269NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500263NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500264NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500175NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500185NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='500186NJK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503336NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503336LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503337NJB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503337LPB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503301HRB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503301LMB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503302HRK'
update wx_t_product set ext_flag=ext_flag|2 where sku='503326DEE'
update wx_t_product set ext_flag=ext_flag|2 where sku='503326HRB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503326LMB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503329HRB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503329LMB'
update wx_t_product set ext_flag=ext_flag|2 where sku='503330HRB'
update wx_t_product set ext_flag=ext_flag|2 where sku='505501HRB'
update wx_t_product set ext_flag=ext_flag|2 where sku='505501LMB'

update wx_t_product set ext_flag=ext_flag|4 where sku='510570DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='510570LMB'
update wx_t_product set ext_flag=ext_flag|4 where sku='510570NJB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513004LPB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513004NJB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513501NMB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513502OIB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513504LMB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513504NJB'
update wx_t_product set ext_flag=ext_flag|4 where sku='510859JFK'
update wx_t_product set ext_flag=ext_flag|4 where sku='510859QLK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513202LPK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513203LPK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513204DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513204HRB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513204LPK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513205DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513205HRB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513205LPK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513212DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513212HRB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513212LPK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513213DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513213HRB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513213LPK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513506DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513506HRK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513507DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513512DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513512HRK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513512HXB'
update wx_t_product set ext_flag=ext_flag|4 where sku='513513DNK'
update wx_t_product set ext_flag=ext_flag|4 where sku='513513HRK'
update wx_t_product set ext_flag=ext_flag|4 where sku='540812SMK'


insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='chevron_Marketing'), 'Visit.check', 1, 1, '1-拜访打卡抽查', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'Visit.check', 1, 1, '1-拜访打卡抽查', 1, getdate());

update c set c.option_value='{  "showType": "checkbox",  "multiSelect": true,  "datasourceType": "data",  "data": [{    "text": "挖机",    "value": "挖机"   }, {    "text": "装载机",    "value": "装载机"   },   {    "text": "泵车",    "value": "泵车"   }, {    "text": "混凝土搅拌车",    "value": "混凝土搅拌车"   }, {    "text": "自卸车",    "value": "自卸车"   }, {    "text": "道路机械设备",    "value": "道路机械设备"   },    {    "text": "起重设备",    "value": "起重设备"   },   {    "text": "旋挖钻机",    "value": "旋挖钻机"   },   {    "text": "矿用自卸车",    "value": "矿用自卸车"   },   {    "text": "农机",    "value": "农机"   },   {    "text": "其他",    "value": "其他"   }  ],  "textField": "text",  "valueField": "value" }'
from wx_task_check c where check_id='23071933'
