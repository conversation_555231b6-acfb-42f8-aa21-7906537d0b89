package com.sys.push.model;

import java.util.Date;

import com.common.util.StringUtils;

public class WxPushMessage {
    public static final String API_KEY_ANDROID = "GNrvXKqDIYjRifXcdwx8LqF2";
    public static final String SECRET_KEY_ANDROID = "43gYq1ntvRSjEdWd1SaEQdrTbs0BUIWP";
    public static final String API_KEY_IOS = "PEIHIEcKOaeN4dBmeHi7u8YO";
    public static final String SECRET_KEY_IOS = "i8GaSeEG0Ptku6ysPMDFBGAAcp0exY2m";

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.message_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Long messageId;
    
    /** 引用消息ID */
    private Long messageMainId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.push_msg_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private String pushMsgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.user_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.device_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private String deviceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.device_type
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private String deviceType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.content
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.status
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Integer status;
    
    public final static int STATUS_UNREAD = 1;
    
    public final static int STATUS_READ = 3;
    
    public final static int STATUS_DELETED = -1;
    
    /** 消息状态(推送消息的推送状态及提示消息的提示状态) */
    private Integer pushStatus;
    
    public final static int PUSH_STATUS_UNPUSH = 0;
    
    public final static int PUSH_STATUS_SENT = 1;
    
    public final static int PUSH_STATUS_PUSHED = 2;
    
    public final static int PUSH_STATUS_FAIL = 10;
    
    /** 提示信息的提示关闭标记 */
    private Integer reminderCloseFlag;

    /** 消息类型 */
    private String messageType;
    
    public final static String MSG_TYPE_PUBLIC = "public";
    
    public final static String MSG_TYPE_PERSONAL = "personal";

    public final static String MSG_TYPE_TIPS = "tips";
    
    /** 未读信息数量 */
    private Integer unreadCount;
    
    /** 已读信息数量 */
    private Integer readCount;
    
    /** 消息标题 */
    private String messageTitle;

    /** 消息额外的数据 */
    private String extraInfo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.error_code
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private String errorCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.error_message
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private String errorMessage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.create_time
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.create_user
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Long createUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.xg_time
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Date xgTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.xg_user
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Long xgUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_push_message.tenant_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    private Long tenantId;
    
    private String userName;
    
    private String orgName;
    
    /** 内容摘要 */
    private String summary;

    /** 尝试次数 */
	private int tryTimes = 0;
	
	/** 上一次尝试时间 */
	private long pretryTime;


    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.message_id
     *
     * @return the value of wx_push_message.message_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Long getMessageId() {
        return messageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.message_id
     *
     * @param messageId the value for wx_push_message.message_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.push_msg_id
     *
     * @return the value of wx_push_message.push_msg_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public String getPushMsgId() {
        return pushMsgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.push_msg_id
     *
     * @param pushMsgId the value for wx_push_message.push_msg_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setPushMsgId(String pushMsgId) {
        this.pushMsgId = pushMsgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.user_id
     *
     * @return the value of wx_push_message.user_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.user_id
     *
     * @param userId the value for wx_push_message.user_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.device_id
     *
     * @return the value of wx_push_message.device_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public String getDeviceId() {
        return deviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.device_id
     *
     * @param deviceId the value for wx_push_message.device_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.device_type
     *
     * @return the value of wx_push_message.device_type
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public String getDeviceType() {
        return deviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.device_type
     *
     * @param deviceType the value for wx_push_message.device_type
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.content
     *
     * @return the value of wx_push_message.content
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.content
     *
     * @param content the value for wx_push_message.content
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.status
     *
     * @return the value of wx_push_message.status
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.status
     *
     * @param status the value for wx_push_message.status
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.error_code
     *
     * @return the value of wx_push_message.error_code
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.error_code
     *
     * @param errorCode the value for wx_push_message.error_code
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.error_message
     *
     * @return the value of wx_push_message.error_message
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.error_message
     *
     * @param errorMessage the value for wx_push_message.error_message
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setErrorMessage(String errorMessage) {
    	if(tryTimes > 0 && StringUtils.isNotBlank(errorMessage)){
    		if(StringUtils.isNotBlank(this.errorMessage)){
    			this.errorMessage = this.errorMessage + "；" + "第" + tryTimes + "次：" + errorMessage;
    		}else{
    			this.errorMessage = errorMessage;
    		}
    		if(this.errorMessage.length() > 50){
    			this.errorMessage = "..." + this.errorMessage.substring(this.errorMessage.length() - 44);
    		}
    	}else{
    		this.errorMessage = errorMessage;
    	}
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.create_time
     *
     * @return the value of wx_push_message.create_time
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.create_time
     *
     * @param createTime the value for wx_push_message.create_time
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.create_user
     *
     * @return the value of wx_push_message.create_user
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Long getCreateUser() {
        return createUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.create_user
     *
     * @param createUser the value for wx_push_message.create_user
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.xg_time
     *
     * @return the value of wx_push_message.xg_time
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Date getXgTime() {
        return xgTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.xg_time
     *
     * @param xgTime the value for wx_push_message.xg_time
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setXgTime(Date xgTime) {
        this.xgTime = xgTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.xg_user
     *
     * @return the value of wx_push_message.xg_user
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Long getXgUser() {
        return xgUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.xg_user
     *
     * @param xgUser the value for wx_push_message.xg_user
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setXgUser(Long xgUser) {
        this.xgUser = xgUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_push_message.tenant_id
     *
     * @return the value of wx_push_message.tenant_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_push_message.tenant_id
     *
     * @param tenantId the value for wx_push_message.tenant_id
     *
     * @mbggenerated Wed Aug 26 11:20:16 CST 2015
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public Integer getUnreadCount() {
		return unreadCount;
	}

	public Integer getReadCount() {
		return readCount;
	}

	public void setUnreadCount(Integer unreadCount) {
		this.unreadCount = unreadCount;
	}

	public void setReadCount(Integer readCount) {
		this.readCount = readCount;
	}

	public String getMessageTitle() {
		return messageTitle;
	}

	public void setMessageTitle(String messageTitle) {
		this.messageTitle = messageTitle;
	}

	public String getUserName() {
		return userName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public Long getMessageMainId() {
		return messageMainId;
	}

	public void setMessageMainId(Long messageMainId) {
		this.messageMainId = messageMainId;
	}

	public Integer getPushStatus() {
		return pushStatus;
	}

	public void setPushStatus(Integer pushStatus) {
		this.pushStatus = pushStatus;
	}

	public int getTryTimes() {
		return tryTimes;
	}

	public long getPretryTime() {
		return pretryTime;
	}

	public void setTryTimes(int tryTimes) {
		this.tryTimes = tryTimes;
	}

	public void setPretryTime(long pretryTime) {
		this.pretryTime = pretryTime;
	}

	public Integer getReminderCloseFlag() {
		return reminderCloseFlag;
	}

	public void setReminderCloseFlag(Integer reminderCloseFlag) {
		this.reminderCloseFlag = reminderCloseFlag;
	}

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }
}