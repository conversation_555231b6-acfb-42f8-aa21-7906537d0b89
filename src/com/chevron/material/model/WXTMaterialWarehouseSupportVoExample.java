package com.chevron.material.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WXTMaterialWarehouseSupportVoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WXTMaterialWarehouseSupportVoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andWarehouseIdIsNull() {
            addCriterion("WAREHOUSE_ID is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdIsNotNull() {
            addCriterion("WAREHOUSE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdEqualTo(Long value) {
            addCriterion("WAREHOUSE_ID =", value, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdNotEqualTo(Long value) {
            addCriterion("WAREHOUSE_ID <>", value, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdGreaterThan(Long value) {
            addCriterion("WAREHOUSE_ID >", value, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("WAREHOUSE_ID >=", value, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdLessThan(Long value) {
            addCriterion("WAREHOUSE_ID <", value, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdLessThanOrEqualTo(Long value) {
            addCriterion("WAREHOUSE_ID <=", value, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdIn(List<Long> values) {
            addCriterion("WAREHOUSE_ID in", values, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdNotIn(List<Long> values) {
            addCriterion("WAREHOUSE_ID not in", values, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdBetween(Long value1, Long value2) {
            addCriterion("WAREHOUSE_ID between", value1, value2, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andWarehouseIdNotBetween(Long value1, Long value2) {
            addCriterion("WAREHOUSE_ID not between", value1, value2, "warehouseId");
            return (Criteria) this;
        }

        public Criteria andSupportIdIsNull() {
            addCriterion("SUPPORT_ID is null");
            return (Criteria) this;
        }

        public Criteria andSupportIdIsNotNull() {
            addCriterion("SUPPORT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSupportIdEqualTo(Long value) {
            addCriterion("SUPPORT_ID =", value, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdNotEqualTo(Long value) {
            addCriterion("SUPPORT_ID <>", value, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdGreaterThan(Long value) {
            addCriterion("SUPPORT_ID >", value, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SUPPORT_ID >=", value, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdLessThan(Long value) {
            addCriterion("SUPPORT_ID <", value, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdLessThanOrEqualTo(Long value) {
            addCriterion("SUPPORT_ID <=", value, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdIn(List<Long> values) {
            addCriterion("SUPPORT_ID in", values, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdNotIn(List<Long> values) {
            addCriterion("SUPPORT_ID not in", values, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdBetween(Long value1, Long value2) {
            addCriterion("SUPPORT_ID between", value1, value2, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportIdNotBetween(Long value1, Long value2) {
            addCriterion("SUPPORT_ID not between", value1, value2, "supportId");
            return (Criteria) this;
        }

        public Criteria andSupportCodeIsNull() {
            addCriterion("SUPPORT_CODE is null");
            return (Criteria) this;
        }

        public Criteria andSupportCodeIsNotNull() {
            addCriterion("SUPPORT_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andSupportCodeEqualTo(String value) {
            addCriterion("SUPPORT_CODE =", value, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeNotEqualTo(String value) {
            addCriterion("SUPPORT_CODE <>", value, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeGreaterThan(String value) {
            addCriterion("SUPPORT_CODE >", value, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPORT_CODE >=", value, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeLessThan(String value) {
            addCriterion("SUPPORT_CODE <", value, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeLessThanOrEqualTo(String value) {
            addCriterion("SUPPORT_CODE <=", value, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeIn(List<String> values) {
            addCriterion("SUPPORT_CODE in", values, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeNotIn(List<String> values) {
            addCriterion("SUPPORT_CODE not in", values, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeBetween(String value1, String value2) {
            addCriterion("SUPPORT_CODE between", value1, value2, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportCodeNotBetween(String value1, String value2) {
            addCriterion("SUPPORT_CODE not between", value1, value2, "supportCode");
            return (Criteria) this;
        }

        public Criteria andSupportNameIsNull() {
            addCriterion("SUPPORT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSupportNameIsNotNull() {
            addCriterion("SUPPORT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSupportNameEqualTo(String value) {
            addCriterion("SUPPORT_NAME =", value, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameNotEqualTo(String value) {
            addCriterion("SUPPORT_NAME <>", value, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameGreaterThan(String value) {
            addCriterion("SUPPORT_NAME >", value, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPORT_NAME >=", value, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameLessThan(String value) {
            addCriterion("SUPPORT_NAME <", value, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameLessThanOrEqualTo(String value) {
            addCriterion("SUPPORT_NAME <=", value, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameIn(List<String> values) {
            addCriterion("SUPPORT_NAME in", values, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameNotIn(List<String> values) {
            addCriterion("SUPPORT_NAME not in", values, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameBetween(String value1, String value2) {
            addCriterion("SUPPORT_NAME between", value1, value2, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportNameNotBetween(String value1, String value2) {
            addCriterion("SUPPORT_NAME not between", value1, value2, "supportName");
            return (Criteria) this;
        }

        public Criteria andSupportTypeIsNull() {
            addCriterion("SUPPORT_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andSupportTypeIsNotNull() {
            addCriterion("SUPPORT_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andSupportTypeEqualTo(String value) {
            addCriterion("SUPPORT_TYPE =", value, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeNotEqualTo(String value) {
            addCriterion("SUPPORT_TYPE <>", value, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeGreaterThan(String value) {
            addCriterion("SUPPORT_TYPE >", value, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPORT_TYPE >=", value, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeLessThan(String value) {
            addCriterion("SUPPORT_TYPE <", value, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeLessThanOrEqualTo(String value) {
            addCriterion("SUPPORT_TYPE <=", value, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeIn(List<String> values) {
            addCriterion("SUPPORT_TYPE in", values, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeNotIn(List<String> values) {
            addCriterion("SUPPORT_TYPE not in", values, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeBetween(String value1, String value2) {
            addCriterion("SUPPORT_TYPE between", value1, value2, "supportType");
            return (Criteria) this;
        }

        public Criteria andSupportTypeNotBetween(String value1, String value2) {
            addCriterion("SUPPORT_TYPE not between", value1, value2, "supportType");
            return (Criteria) this;
        }

        public Criteria andComentsIsNull() {
            addCriterion("COMENTS is null");
            return (Criteria) this;
        }

        public Criteria andComentsIsNotNull() {
            addCriterion("COMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andComentsEqualTo(String value) {
            addCriterion("COMENTS =", value, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsNotEqualTo(String value) {
            addCriterion("COMENTS <>", value, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsGreaterThan(String value) {
            addCriterion("COMENTS >", value, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsGreaterThanOrEqualTo(String value) {
            addCriterion("COMENTS >=", value, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsLessThan(String value) {
            addCriterion("COMENTS <", value, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsLessThanOrEqualTo(String value) {
            addCriterion("COMENTS <=", value, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsIn(List<String> values) {
            addCriterion("COMENTS in", values, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsNotIn(List<String> values) {
            addCriterion("COMENTS not in", values, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsBetween(String value1, String value2) {
            addCriterion("COMENTS between", value1, value2, "coments");
            return (Criteria) this;
        }

        public Criteria andComentsNotBetween(String value1, String value2) {
            addCriterion("COMENTS not between", value1, value2, "coments");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("DELETE_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("DELETE_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Boolean value) {
            addCriterion("DELETE_FLAG =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Boolean value) {
            addCriterion("DELETE_FLAG <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Boolean value) {
            addCriterion("DELETE_FLAG >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("DELETE_FLAG >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Boolean value) {
            addCriterion("DELETE_FLAG <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("DELETE_FLAG <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Boolean> values) {
            addCriterion("DELETE_FLAG in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Boolean> values) {
            addCriterion("DELETE_FLAG not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("DELETE_FLAG between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("DELETE_FLAG not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("ATTRIBUTE1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("ATTRIBUTE1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("ATTRIBUTE1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("ATTRIBUTE1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("ATTRIBUTE1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("ATTRIBUTE1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("ATTRIBUTE1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("ATTRIBUTE1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("ATTRIBUTE1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("ATTRIBUTE1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("ATTRIBUTE1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("ATTRIBUTE1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("ATTRIBUTE2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("ATTRIBUTE2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("ATTRIBUTE2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("ATTRIBUTE2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("ATTRIBUTE2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("ATTRIBUTE2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("ATTRIBUTE2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("ATTRIBUTE2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("ATTRIBUTE2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("ATTRIBUTE2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("ATTRIBUTE2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("ATTRIBUTE2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("CREATION_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("CREATION_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(Date value) {
            addCriterion("CREATION_TIME =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(Date value) {
            addCriterion("CREATION_TIME <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(Date value) {
            addCriterion("CREATION_TIME >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATION_TIME >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(Date value) {
            addCriterion("CREATION_TIME <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATION_TIME <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<Date> values) {
            addCriterion("CREATION_TIME in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<Date> values) {
            addCriterion("CREATION_TIME not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(Date value1, Date value2) {
            addCriterion("CREATION_TIME between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATION_TIME not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("LAST_UPDATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("LAST_UPDATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(Date value) {
            addCriterion("LAST_UPDATE_TIME =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(Date value) {
            addCriterion("LAST_UPDATE_TIME <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(Date value) {
            addCriterion("LAST_UPDATE_TIME >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("LAST_UPDATE_TIME >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(Date value) {
            addCriterion("LAST_UPDATE_TIME <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("LAST_UPDATE_TIME <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<Date> values) {
            addCriterion("LAST_UPDATE_TIME in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<Date> values) {
            addCriterion("LAST_UPDATE_TIME not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("LAST_UPDATE_TIME between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("LAST_UPDATE_TIME not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("LAST_UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("LAST_UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(Long value) {
            addCriterion("LAST_UPDATED_BY =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(Long value) {
            addCriterion("LAST_UPDATED_BY <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(Long value) {
            addCriterion("LAST_UPDATED_BY >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("LAST_UPDATED_BY >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(Long value) {
            addCriterion("LAST_UPDATED_BY <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("LAST_UPDATED_BY <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<Long> values) {
            addCriterion("LAST_UPDATED_BY in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<Long> values) {
            addCriterion("LAST_UPDATED_BY not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(Long value1, Long value2) {
            addCriterion("LAST_UPDATED_BY between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("LAST_UPDATED_BY not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}