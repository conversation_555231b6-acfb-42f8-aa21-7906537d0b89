package com.zs.pms.model;

public class WxtZSAntifreezeFluidView {

	private String antifreezeFluidId;

	private String brandName;

	private String model;

	private String freezePoint;

	private String bolingPoint;

	private String color;

	private String capacity;

	public String getAntifreezeFluidId() {
		return antifreezeFluidId;
	}

	public void setAntifreezeFluidId(String antifreezeFluidId) {
		this.antifreezeFluidId = antifreezeFluidId;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getFreezePoint() {
		return freezePoint;
	}

	public void setFreezePoint(String freezePoint) {
		this.freezePoint = freezePoint;
	}

	public String getBolingPoint() {
		return bolingPoint;
	}

	public void setBolingPoint(String bolingPoint) {
		this.bolingPoint = bolingPoint;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public String getCapacity() {
		return capacity;
	}

	public void setCapacity(String capacity) {
		this.capacity = capacity;
	}
	
	
}
