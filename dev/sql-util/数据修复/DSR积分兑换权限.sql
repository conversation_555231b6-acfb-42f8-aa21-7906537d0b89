--INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select top 100 u.ch_name, u.login_name,h.point_paid_target_mtd, h.point_paid_actual_mtd,h.grant_month
--distinct u.user_id, 1 gu, (select r.role_id from wx_t_role r where r.ch_role_name='APP_Dsr_Point_Exchange') ri, 1 s
from wx_t_v2_dsr_sc_grant_history h
left join wx_t_user u on u.user_id=h.dsr_id
 where point_paid_actual_mtd>0 --and u.ch_name='常青'
 and not exists (select 1 from wx_t_role r left join wx_t_userrole ur on r.role_id=ur.role_id 
 where r.ch_role_name='APP_Dsr_Point_Exchange' and ur.user_id=h.dsr_id)