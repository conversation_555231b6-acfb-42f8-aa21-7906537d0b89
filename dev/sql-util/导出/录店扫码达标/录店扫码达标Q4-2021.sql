select t_org.id,t_org.organization_name "合伙人名称",t_org.create_time "创建时间",
sell4.workshop_count + sell5.workshop_count + sell6.workshop_count "录店数",
isnull(sell4.target,0) / 28 "10月目标",
sell4.sell_throuth "10月出库扫码", 
isnull(sell5.target,0) / 28 "11月目标",
sell5.sell_throuth "11月出库扫码",
isnull(sell6.target,0) / 28 "12月目标",
sell6.sell_throuth "12月出库扫码" from
wx_t_organization t_org
left join wx_t_partner_o2o_enterprise poe on poe.partner_id = t_org.id
left join 
(

SELECT DISTINCT 
t_partner_workshop_info.*, 
cast(t_partner_scan_info.sell_in as bigint)sell_in, 
t_target.target,
CAST(isnull(t_sell_throuth.sell_through,0) AS bigint) AS sell_throuth
		from (
		SELECT t_og.id partner_id ,t_og.organization_name partner_name, 
		isnull(ttt.workshop_count,0) workshop_count, 
		(CASE  WHEN isnull(ttt.workshop_count,0) <= '5'  
		THEN 'true'
		ELSE 'false'
		END) is_workshop_reach 
		FROM wx_t_organization t_og 
		LEFT JOIN 
		(SELECT * FROM 
		(
		select  o.id tenant_id, count(1) workshop_count from (select min(ws.create_time) lt,
		ws.workshop_id from wx_t_workshop_status ws where ws.workshop_with_status=3
		group by ws.workshop_id) a
		left join wx_t_workshop_partner wp on a.workshop_id=wp.workshop_id
		left join wx_t_organization o on o.id=wp.partner_id
		where a.lt >= '2020-10-01' and a.lt < '2020-11-01'
		group by o.id
		) tt) ttt
		ON ttt.tenant_id = t_og.id
		INNER JOIN wx_t_partner_o2o_enterprise tt_partner
		ON tt_partner.partner_id = t_og.id 
		WHERE t_og.type = '1' AND tt_partner.partner_property = 'NORMAL') t_partner_workshop_info
		INNER  JOIN 
		
		(SELECT DISTINCT t_org.id partner_id,t_org.organization_name, 
		isnull(tt.sell_in,0) sell_in, isnull(tt.sell_throuth,0)sell_throuth,isnull(tt.cdm_rebate,0)cdm_rebate  
		FROM wx_t_partner_o2o_enterprise t_p_o
		LEFT JOIN wx_t_organization t_org
		ON t_org.id = t_p_o.partner_id
		LEFT JOIN 
		(SELECT * FROM 
		(SELECT dw_sellin_throuth.customer_name_cn,
		isnull(sum(dw_sellin_throuth.sell_in),0) sell_in,
		isnull(sum(dw_sellin_throuth.sell_through),0)sell_throuth,
		isnull(sum(dw_sellin_throuth.cdm_rebate),0) * 1.16 cdm_rebate
		 FROM dw_cdm_rebate_sell_in_throuth dw_sellin_throuth
		WHERE dw_sellin_throuth.year_month_date = '2020-10-01'
		GROUP BY dw_sellin_throuth.customer_name_cn) t) tt
		ON tt.customer_name_cn = t_org.organization_name
		WHERE  t_org.type = '1' AND t_p_o.partner_property = 'NORMAL' and t_org.status=1) t_partner_scan_info
		ON  t_partner_scan_info.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN (
			SELECT partner_id, isNull(sum(sell_through),0) sell_through
	      		FROM
	      		(select t_workshop_partner.partner_id,
	      			(select sum(isnull( osp.actual_out_count,0) * convert(float, p1.capacity))
	      				from wx_t_out_stock_product osp
	      				LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
	     				left join wx_t_product p1 on p1.sku=osp.sku 
	     				WHERE t_outstock.order_no = o.order_no and p1.product_property = 'SN Above'
	     				AND  t_outstock.out_time >= '2020-10-01'
						AND  t_outstock.out_time < '2020-11-01'
	     			) sell_through
	     			from wx_t_order o
	      		left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
	      		where o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7', 10, 11)
	      	GROUP BY t_workshop_partner.partner_id,o.order_no
	      ) tt
	      GROUP BY partner_id
		)t_sell_throuth ON t_sell_throuth.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN wx_t_partner_rebate_info t_partner_rebate
		ON t_partner_rebate.partner_id = t_partner_scan_info.partner_id
		AND  (year_str+'-'+ left('00',2-len(month_str))+month_str) = '2020-10'	
		LEFT JOIN (
			select isnull(sum(xx01.target_volume), 0) AS target,poe.partner_id
			from [PP_MID].[DBO].syn_dw_to_pp_baseline_target xx01 
			left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = xx01.distributor_id 
			where xx01.target_type='SN & Above' and CONVERT(varchar(4), xx01.rebate_target_date, 23) = '2020'
			GROUP BY poe.partner_id
		)t_target
		ON t_partner_workshop_info.partner_id = t_target.partner_id
)sell4 on t_org.id = sell4.partner_id
left join (

SELECT DISTINCT 
t_partner_workshop_info.*, 
cast(t_partner_scan_info.sell_in as bigint)sell_in, 
t_target.target,
CAST(isnull(t_sell_throuth.sell_through,0) AS bigint) AS sell_throuth
		from (
		SELECT t_og.id partner_id ,t_og.organization_name partner_name, 
		isnull(ttt.workshop_count,0) workshop_count, 
		(CASE  WHEN isnull(ttt.workshop_count,0) <= '5'  
		THEN 'true'
		ELSE 'false'
		END) is_workshop_reach 
		FROM wx_t_organization t_og 
		LEFT JOIN 
		(SELECT * FROM 
		(
		select  o.id tenant_id, count(1) workshop_count from (select min(ws.create_time) lt,
		ws.workshop_id from wx_t_workshop_status ws where ws.workshop_with_status=3
		group by ws.workshop_id) a
		left join wx_t_workshop_partner wp on a.workshop_id=wp.workshop_id
		left join wx_t_organization o on o.id=wp.partner_id
		where a.lt >= '2020-11-01' and a.lt < '2020-12-01'
		group by o.id
		) tt) ttt
		ON ttt.tenant_id = t_og.id
		INNER JOIN wx_t_partner_o2o_enterprise tt_partner
		ON tt_partner.partner_id = t_og.id 
		WHERE t_og.type = '1' AND tt_partner.partner_property = 'NORMAL') t_partner_workshop_info
		INNER  JOIN 
		
		(SELECT DISTINCT t_org.id partner_id,t_org.organization_name, 
		isnull(tt.sell_in,0) sell_in, isnull(tt.sell_throuth,0)sell_throuth,isnull(tt.cdm_rebate,0)cdm_rebate  
		FROM wx_t_partner_o2o_enterprise t_p_o
		LEFT JOIN wx_t_organization t_org
		ON t_org.id = t_p_o.partner_id
		LEFT JOIN 
		(SELECT * FROM 
		(SELECT dw_sellin_throuth.customer_name_cn,
		isnull(sum(dw_sellin_throuth.sell_in),0) sell_in,
		isnull(sum(dw_sellin_throuth.sell_through),0)sell_throuth,
		isnull(sum(dw_sellin_throuth.cdm_rebate),0) * 1.16 cdm_rebate
		 FROM dw_cdm_rebate_sell_in_throuth dw_sellin_throuth
		WHERE dw_sellin_throuth.year_month_date = '2020-11-01'
		GROUP BY dw_sellin_throuth.customer_name_cn) t) tt
		ON tt.customer_name_cn = t_org.organization_name
		WHERE  t_org.type = '1' AND t_p_o.partner_property = 'NORMAL' and t_org.status=1) t_partner_scan_info
		ON  t_partner_scan_info.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN (
			SELECT partner_id, isNull(sum(sell_through),0) sell_through
	      		FROM
	      		(select t_workshop_partner.partner_id,
	      			(select sum(isnull( osp.actual_out_count,0) * convert(float, p1.capacity))
	      				from wx_t_out_stock_product osp
	      				LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
	     				left join wx_t_product p1 on p1.sku=osp.sku 
	     				WHERE t_outstock.order_no = o.order_no and p1.product_property = 'SN Above'
	     				AND  t_outstock.out_time >= '2020-11-01'
						AND  t_outstock.out_time < '2020-12-01'
	     			) sell_through
	     			from wx_t_order o
	      		left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
	      		where o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7', 10, 11)
	      	GROUP BY t_workshop_partner.partner_id,o.order_no
	      ) tt
	      GROUP BY partner_id
		)t_sell_throuth ON t_sell_throuth.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN wx_t_partner_rebate_info t_partner_rebate
		ON t_partner_rebate.partner_id = t_partner_scan_info.partner_id
		AND  (year_str+'-'+ left('00',2-len(month_str))+month_str) = '2020-11'	
		LEFT JOIN (
			select isnull(sum(xx01.target_volume), 0) AS target,poe.partner_id
			from [PP_MID].[DBO].syn_dw_to_pp_baseline_target xx01 
			left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = xx01.distributor_id 
			where xx01.target_type='SN & Above' and CONVERT(varchar(4), xx01.rebate_target_date, 23) = '2020'
			GROUP BY poe.partner_id
		)t_target
		ON t_partner_workshop_info.partner_id = t_target.partner_id
)sell5 on t_org.id = sell5.partner_id
left join(
	
SELECT DISTINCT 
t_partner_workshop_info.*, 
cast(t_partner_scan_info.sell_in as bigint)sell_in, 
t_target.target,
CAST(isnull(t_sell_throuth.sell_through,0) AS bigint) AS sell_throuth
		from (
		SELECT t_og.id partner_id ,t_og.organization_name partner_name, 
		isnull(ttt.workshop_count,0) workshop_count, 
		(CASE  WHEN isnull(ttt.workshop_count,0) <= '5'  
		THEN 'true'
		ELSE 'false'
		END) is_workshop_reach 
		FROM wx_t_organization t_og 
		LEFT JOIN 
		(SELECT * FROM 
		(
		select  o.id tenant_id, count(1) workshop_count from (select min(ws.create_time) lt,
		ws.workshop_id from wx_t_workshop_status ws where ws.workshop_with_status=3
		group by ws.workshop_id) a
		left join wx_t_workshop_partner wp on a.workshop_id=wp.workshop_id
		left join wx_t_organization o on o.id=wp.partner_id
		where a.lt >= '2020-12-01' and a.lt < '2021-01-01'
		group by o.id
		) tt) ttt
		ON ttt.tenant_id = t_og.id
		INNER JOIN wx_t_partner_o2o_enterprise tt_partner
		ON tt_partner.partner_id = t_og.id 
		WHERE t_og.type = '1' AND tt_partner.partner_property = 'NORMAL') t_partner_workshop_info
		INNER  JOIN 
		
		(SELECT DISTINCT t_org.id partner_id,t_org.organization_name, 
		isnull(tt.sell_in,0) sell_in, isnull(tt.sell_throuth,0)sell_throuth,isnull(tt.cdm_rebate,0)cdm_rebate  
		FROM wx_t_partner_o2o_enterprise t_p_o
		LEFT JOIN wx_t_organization t_org
		ON t_org.id = t_p_o.partner_id
		LEFT JOIN 
		(SELECT * FROM 
		(SELECT dw_sellin_throuth.customer_name_cn,
		isnull(sum(dw_sellin_throuth.sell_in),0) sell_in,
		isnull(sum(dw_sellin_throuth.sell_through),0)sell_throuth,
		isnull(sum(dw_sellin_throuth.cdm_rebate),0) * 1.16 cdm_rebate
		 FROM dw_cdm_rebate_sell_in_throuth dw_sellin_throuth
		WHERE dw_sellin_throuth.year_month_date = '2020-12-01'
		GROUP BY dw_sellin_throuth.customer_name_cn) t) tt
		ON tt.customer_name_cn = t_org.organization_name
		WHERE  t_org.type = '1' AND t_p_o.partner_property = 'NORMAL' and t_org.status=1) t_partner_scan_info
		ON  t_partner_scan_info.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN (
			SELECT partner_id, isNull(sum(sell_through),0) sell_through
	      		FROM
	      		(select t_workshop_partner.partner_id,
	      			(select sum(isnull( osp.actual_out_count,0) * convert(float, p1.capacity))
	      				from wx_t_out_stock_product osp
	      				LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
	     				left join wx_t_product p1 on p1.sku=osp.sku 
	     				WHERE t_outstock.order_no = o.order_no and p1.product_property = 'SN Above'
	     				AND  t_outstock.out_time >= '2020-12-01'
						AND  t_outstock.out_time < '2021-01-01'
	     			) sell_through
	     			from wx_t_order o
	      		left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
	      		where o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7', 10, 11)
	      	GROUP BY t_workshop_partner.partner_id,o.order_no
	      ) tt
	      GROUP BY partner_id
		)t_sell_throuth ON t_sell_throuth.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN wx_t_partner_rebate_info t_partner_rebate
		ON t_partner_rebate.partner_id = t_partner_scan_info.partner_id
		AND  (year_str+'-'+ left('00',2-len(month_str))+month_str) = '2020-12'	
		LEFT JOIN (
			select isnull(sum(xx01.target_volume), 0) AS target,poe.partner_id
			from [PP_MID].[DBO].syn_dw_to_pp_baseline_target xx01 
			left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = xx01.distributor_id 
			where xx01.target_type='SN & Above' and CONVERT(varchar(4), xx01.rebate_target_date, 23) = '2020'
			GROUP BY poe.partner_id
		)t_target
		ON t_partner_workshop_info.partner_id = t_target.partner_id
)sell6 on t_org.id = sell6.partner_id

WHERE poe.partner_property = 'NORMAL' and t_org.id<>9 and t_org.status=1 order by t_org.id asc