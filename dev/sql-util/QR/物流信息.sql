--根据瓶盖内码查询
SELECT r.track_code_id+2910000000,*
  FROM [qrcodedb].[dbo].[wx_t_cap_code_detail] cd
  left join wx_t_cap_track_no_rel r on r.cap_code_id=cd.code_id where qr_code='e5qpl5gtc7xj5p67';
--根据瓶盖外码查询
SELECT r.track_code_id+2910000000,*
FROM [qrcodedb].[dbo].[wx_t_cap_code_detail] cd
left join wx_t_cap_track_no_rel r on r.cap_code_id=cd.code_id where r.cap_code='898903181432';
  
select * from dw_customer_org_sales where distributor_id=619

select bir.customer_name_cn,* 
FROM wx_t_oem_product_packaging_code oppc
	LEFT JOIN wx_t_oem_product_packaging opp ON opp.id = oppc.packaging_id
	LEFT JOIN wx_t_oem_delivery_product dp
	ON (dp.codelevel = 1
			AND oppc.code1 = dp.code)
		OR (dp.codelevel = 2
			AND oppc.code2 = dp.code)
	LEFT JOIN wx_t_oem_delivery d ON d.id = dp.delivery_id
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir on bir.distributor_id=d.recv_distributor_id
	where oppc.code1='102956107683'
	
select d.recv_id-- bir.customer_name_cn,* 
FROM wx_t_oem_product_packaging_code oppc
	LEFT JOIN wx_t_oem_product_packaging opp ON opp.id = oppc.packaging_id
	LEFT JOIN wx_t_oem_delivery_product dp
	ON (dp.codelevel = 1
			AND oppc.code1 = dp.code)
		OR (dp.codelevel = 2
			AND oppc.code2 = dp.code)
	LEFT JOIN wx_t_oem_delivery d ON d.id = dp.delivery_id
	left join PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir on bir.distributor_id=d.recv_distributor_id
	where --oppc.code1='102943822887'
	--d.recv_id='2051'
	d.recv_name='SHANGHAI GAOQIAO CALTEX LUBRICATING'	