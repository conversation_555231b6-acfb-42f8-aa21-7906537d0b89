$(function() {
	newhomepage_init.init();
});

var groupByTypes = [{value:"byQuarter",text:"按季度"},{value:"byMonth",text:"按月份"}];
var DEFAULT_GROUP_BY_TYPE_INDEX = 0;
var sellinStatsByMonthAndTypeChart;
var totalSellinStatsByMonthChart;
var sellinStatsByMonthAndSkuChart;
var rpc = common.rpcClient;
function reloadPage(params){
	var p1 = $.extend({}, currentParams);
	if(params){
		p1 = $.extend(p1, params);
	}
	if(p1.partnerName){
		p1.partnerName = encodeURIComponent(encodeURIComponent(p1.partnerName));
	}
	var u = null;
	for(var k in p1){
		if(u == null){
			u = common.ctx + 'newhomepage/jsp/asm/SpSellInInfoAnalysis.jsp?';
		}else {
			u += '&';
		}
		u += k + '=' + p1[k];
	}
	if(u == null){
		u = common.ctx + 'newhomepage/jsp/asm/SpSellInInfoAnalysis.jsp';
	}
	window.location = u;
}
/**
 * 机构下拉框
 * @type {null}
 */
var organizationCtrl = null;
OrgAutoSelect = $.extend({}, Ctrls.AutoSelect, {
    init: function(valueField, opts){
        if(opts){
            opts = $.extend({placeholder: '支持名称或名称首字母搜索'}, opts);
        }else{
            opts = {placeholder: '支持名称或名称首字母搜索'};
        }
        opts.ctrlOpts = $.extend(opts.ctrlOpts, {
            processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
            	// debugger;
                if (!json || !json.resultLst) {
                    return false;
                }

                var len, data = {
                    value: []
                };
                len = json.resultLst.length;

                for (var i = 0; i < len; i++) {
                    data.value.push({
                        value: json.resultLst[i].id,
                        text: json.resultLst[i].organizationName
                    });
                }

                //字符串转化为 js 对象
                return data;
            }
        });
        return Ctrls.AutoSelect.init.call(this, 'myCdm/queryPartnersInfo.do', valueField, 'keyword', opts);
    }
});
var newhomepage_init = (function($) {

    var foo = new $.JsonRpcClient({
        ajaxUrl : '/wxPublicRpc.do'
    });

	var marketIviBar = null,
	
	iconNotUpClass = "fa fa-times-circle not-up-icon",
	iconRightClass = "fa fa-check-circle-o right-icon",
	iconWarmingClass= "fa fa-exclamation-circle warning-icon",
	
	rightTextClass = "up",
	notUpTextClass = "not-up",
	runningTextClass = "running",
	
	rightText = "已达标",
	notUpText = "未达标",
	runningText = "进行中",
	
	workshopTarget = 8,
		
	req = {}, 
	util = {},
	action = {},
	loader = {};
	
	req = {
		    getPartnerSellThroughInfoByDay: function(callback) {
                $.get(util.genPartnerQueryUrl("myCdm/queryPartnerSellThroughInfoByDay.do"), function(data){
                    if(data.code == 'success'){
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
			},
			getMonthlySellInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/monthlySellInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getProductMonthlySellInfo: function(callback) {
				$.get(common.ctx + "newhomepage/productMonthSellInfo.do?t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getQuarterlySellInfo: function(callback) {
				$.get(util.genPartnerQueryUrl("newhomepage/quarterlySellInfo.do"), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
           getSellinStatsByMonthAndType: function (callback,partnerId) {
               var param = {
                   "partnerId": partnerId?partnerId:$('#partnerId').val(),       //getPartnersInfo返回报文里面的id
                   "year": newhomepage_init.getSelectedYear(),
                   "salesChannel": "CDM",   // CDM或者CIO
                   "quarter": util.getNowQuarter(),
                   "month": currentMonth,
                   "withSalesTarget": true  // 需要带上sales target
               };

               if(param.partnerId === ''){
                   param.partnerId = null;
               }

               $.ajax({
                   type: "POST",
                   contentType: "application/json",
                   url: common.ctx + "myCdm/querySellinStatsByMonthAndType.do" + "?t=" + new Date().getTime(),
                   data: JSON.stringify(param),
                   dataType: 'json',
                   success: function (data) {
                       if (data.code == "success") {
                           $.isFunction(callback) && callback(data);
                       } else {
                           common.alertMes('拉取数据失败', 'error');
                       }
                   },
                   error: function (error) {
                       common.ajaxTimeout(error);
                       return;
                   }
               });

            // foo.call('sellinStatsService.querySellinStatsByMonthAndType', [param],
            //     function (result) {
            //         if (result.code == "success") {
            //             $.isFunction(callback) && callback(result.resultLst);
            //         } else {
            //             common.alertMes('拉取数据失败', 'error');
            //         }
            //     }, function (error) {
            //         common.ajaxTimeout(error);
            //     });
        },
        getSellinStatsByMonth: function (currentYear,customerCategory,roleType,loginCai,partnerId ,callback) {
        	partnerId?partnerId:$('#partnerId').val();
        	if(partnerId === ''){
        		partnerId = null;
        	}
        	rpc.call("biProcedureBizService.querySellInByMonth", [ currentYear,customerCategory,roleType,loginCai,partnerId ], function(result) {
				
    			callback(result);
    				
    		});
        	
        	
//            var param =   {
//                "partnerId": partnerId?partnerId:$('#partnerId').val(),       //getPartnersInfo返回报文里面的id
//                "year": newhomepage_init.getSelectedYear(),
//                "salesChannel": "CDM",   // CDM或者CIO
//                "quarter": util.getNowQuarter(),
//                "month": currentMonth,
//                "withSalesTarget": true  // 需要带上sales target
//            };
//            if(param.partnerId === ''){
//                param.partnerId = null;
//            }
//            $.ajax({
//                type: "POST",
//                contentType: "application/json",
//                url: common.ctx + "myCdm/querySellinStatsByMonth.do" + "?t=" + new Date().getTime(),
//                data: JSON.stringify(param),
//                dataType: 'json',
//                success: function (data) {
//                    if (data.code == "success") {
//                        $.isFunction(callback) && callback(data.resultLst);
//                    } else {
//                        common.alertMes('拉取数据失败', 'error');
//                    }
//                },
//                error: function (error) {
//                    common.ajaxTimeout(error);
//                    return;
//                }
//            });
            // foo.call('sellinStatsService.querySellinStatsByMonth', [param],
            //     function (result) {
            //         if (result.code == "success") {
            //             $.isFunction(callback) && callback(result.resultLst);
            //         } else {
            //             common.alertMes('拉取数据失败', 'error');
            //         }
            //     }, function (error) {
            //         common.ajaxTimeout(error);
            //     });
        },
        getSellinStatsByMonthAndSku: function (currentYear,customerCategory,roleType,loginCai,partnerId,callback) {
        	var quarterMode = util.isQuarterMode();
        	var startDate;
        	var endDate;
        	partnerId?partnerId:$('#partnerId').val();
        	if(partnerId === ''){
        		partnerId = null;
        	}
        	if(quarterMode){
        		switch($("#skuQuarter").val()){
        			case "1":
        				startDate = currentYear+'-'+'01-01'
        				endDate = currentYear+'-'+'04-01'
        				break;
        			case "2":
        				startDate = currentYear+'-'+'04-01'
        				endDate = currentYear+'-'+'07-01'
        				break;
        			case "3":
        				startDate = currentYear+'-'+'07-01'
        				endDate = currentYear+'-'+'10-01'
        				break;
        			case "4":
        				startDate = currentYear+'-'+'10-01'
        				endDate = currentYear+'-'+'12-31'
        				break;
        		}
        	} else {
        		switch($("#skuMonth").val()){
    			case "1":
    				startDate = currentYear+'-'+'01-01'
    				endDate = currentYear+'-'+'02-01'
    				break;
    			case "2":
    				startDate = currentYear+'-'+'02-01'
    				endDate = currentYear+'-'+'03-01'
    				break;
    			case "3":
    				startDate = currentYear+'-'+'03-01'
    				endDate = currentYear+'-'+'04-01'
    				break;
    			case "4":
    				startDate = currentYear+'-'+'04-01'
    				endDate = currentYear+'-'+'05-01'
    				break;
    			case "5":
    				startDate = currentYear+'-'+'05-01'
    				endDate = currentYear+'-'+'06-01'
    				break;
    			case "6":
    				startDate = currentYear+'-'+'06-01'
    				endDate = currentYear+'-'+'07-01'
    				break;
    			case "7":
    				startDate = currentYear+'-'+'07-01'
    				endDate = currentYear+'-'+'08-01'
    				break;
    			case "8":
    				startDate = currentYear+'-'+'08-01'
    				endDate = currentYear+'-'+'09-01'
    				break;
    			case "9":
    				startDate = currentYear+'-'+'09-01'
    				endDate = currentYear+'-'+'10-01'
    				break;
    			case "10":
    				startDate = currentYear+'-'+'10-01'
    				endDate = currentYear+'-'+'11-01'
    				break;
    			case "11":
    				startDate = currentYear+'-'+'11-01'
    				endDate = currentYear+'-'+'12-01'
    				break;
    			case "12":
    				startDate = currentYear+'-'+'12-01'
    				endDate = currentYear+'-'+'12-31'
    				break;
        		}
        	}
        	rpc.call("biProcedureBizService.queryTopNByProduct", [customerCategory,roleType,loginCai,partnerId,startDate,endDate,null], function(result) {
				
    			callback(result);
    				
    		});
//            var quarterMode = util.isQuarterMode();
//            var param =   {
//                "partnerId": partnerId?partnerId:$('#partnerId').val(),       //getPartnersInfo返回报文里面的id
//                "year": newhomepage_init.getSelectedYear(),
//                "salesChannel": "CDM",   // CDM或者CIO
//                "top":20
//            };
//            if(quarterMode){
//                param["quarter"] = $("#skuQuarter").val();
//            } else {
//                param["month"] = $("#skuMonth").val();
//            }
//            foo.call('sellinStatsService.querySellinStatsByMonthAndSku', [param],
//                function (result) {
//                    if (result.code == "success") {
//                        $.isFunction(callback) && callback(result.resultLst);
//                    } else {
//                        common.alertMes('拉取数据失败', 'error');
//                    }
//                }, function (error) {
//                    common.ajaxTimeout(error);
//                });
        },
        getSellinInfoYTD: function (currentYear,customerCategory,roleType,loginCai,distributorId ,callback) {
            // var partnerIdParam = partnerId ? partnerId : $('#partnerId').val();
            // var param = {};
            // if (partnerIdParam) {
            //     param["partnerId"] = partnerId;
            // }
//            $.get(util.genPartnerQueryUrl("myCdm/querySellinAndGrossInfoByPartnerId.do"), function (data) {
//                if (data.code == 'success') {
//                    $.isFunction(callback) && callback(data.data);
//                } else {
//                    common.alertMes(data.message, 'error');
//                    return;
//                }
//            });
        	rpc.call("biProcedureBizService.querySalesRank", [ currentYear,currentMonth,roleType,loginCai ], function(result) {
				
    			callback(result);
    				
    			});
        }
	},
	util = {
			openMenu: function(url) {
				if(top.openMenuTab){
					top.openMenuTab(url);
				}else{
					window.open(url);
				}
			},
			openUrl: function(url, menuId, menuTitle) {
				if(top.openMenu){
					top.openMenu(url, menuId, menuTitle);
				}else{
					window.open(url);
				}
			},
			openPage: function(url, type) {
				window.location.href = url + "?type=" + type;
			},
			toThousands: function(n) {
				return parseFloat(n).toLocaleString() 
			},
            divToThousands: function(num,fixedNum) {
                if(fixedNum == null || fixedNum === undefined){
                    fixedNum = 2;
                }
                var result = null;
                if(num) {
                    result = num.toFixed(fixedNum) / 1000;
                    result = result.toFixed(fixedNum);
                }
                return result;
            },
		    genPartnerQueryUrl: function (urlPath) {
                var partnerId = $('#partnerId').val();
                var timestamp = new Date().getTime();
                var year = util.getSelectedYear() || new Date().getFullYear();
                var month = currentMonth;
                // var
                return common.ctx + urlPath + "?t=" + timestamp + "&partnerId="+partnerId + "&year=" + year + "&month=" + month;
            },
		    doSearch: function (partnerId) {
                    // 获取pointType和orgId
//                    var orgId = partnerId==null?$('#partnerId').val():partnerId;
                    // if(!orgId){
                    //     common.alertMes("请选择一个机构再查询", 'error');
                    //     return;
                    // }
		    	if(!partnerId){
		    		partnerId = currentParams.partnerId = $('#partnerId').val();
		    		currentParams.partnerName = organizationCtrl.getText();
		    	}
                    var quarterMode = util.isQuarterMode();
                    if(quarterMode){
                        $("#skuQuarterDiv").show();
                        $("#skuMonthDiv").hide();
                    } else {
                        $("#skuQuarterDiv").hide();
                        $("#skuMonthDiv").show();
                    }
                	loader.initEcharts();
                	loader.loadSellinStatsByMonth(partnerId);
//					loader.loadSellinStatsByMonthAndType(partnerId);
					loader.loadSellinStatsByMonthAndSku(partnerId);
//					loader.loadSellinInfoYTD(partnerId);
            },
			getSelectedYear : function() {
				 if ($('#year-mode .years-wrapper ul li.active').size() > 0 ) {
				 	return $('#year-mode .years-wrapper ul li.active').text();
				 } else {
					return currentYear;
				 }
			},
			getSelectedMonth : function() {
				// if ($('#year-mode .monthes-wrapper ul li.active').size() > 0 ) {
				// 	return $('#year-mode .monthes-wrapper ul li.active').text();
				// } else {
					return $("#").val();
				// }
			},
            getSelectedGroupByType : function() {
                if ($('#group_by_type_c ul li.active').size() > 0 ) {
                    return $('#group_by_type_c ul li.active').data("group-type");
                } else {
                    return groupByTypes[0].value;
                }
            },
            getNowQuarter: function () {
                return currentQuarter;
            },
            isQuarterMode: function () {
                var quarterMode = util.getSelectedGroupByType() === groupByTypes[0].value;
                return quarterMode;
            }
	},
	loader = {
//		    loadSellinStatsByMonthAndType: function(partnerId){
//            	req.getSellinStatsByMonthAndType(function (data) {
//                    var result = data.resultLst;
//            		var monthArray = [1,2,3,4,5,6,7,8,9,10,11,12];
//                    var quarterArray = [1,2,3,4];
//                    var SnAboveArray = [];
//                    var SnBelowArray = [];
//                    var otherArray = [];
//                    var quarterMode = util.isQuarterMode();
//                    if(result && result.length >0){
//                        var SnAboveArrayQuarter = [null,null,null,null];
//                        var SnBelowArrayQuarter = [null,null,null,null];
//                        var otherArrayQuarter = [null,null,null,null];
//                        $.each(result,function (index,item) {
//                            // monthArray.push(new Date(item.transTime).getMonth() + 1);
//                            var statList = item.statList;
//                            if(quarterMode){
//                                var quarter = Math.floor(index/3);
//                                if(SnAboveArrayQuarter[quarter] == null){
//                                    SnAboveArrayQuarter[quarter] = 0;
//                                }
//                                if(SnBelowArrayQuarter[quarter] == null){
//                                    SnBelowArrayQuarter[quarter] = 0;
//                                }
//                                if(otherArrayQuarter[quarter] == null){
//                                    otherArrayQuarter[quarter] = 0;
//                                }
//                                SnAboveArrayQuarter[quarter] += statList[0].actualValue;
//                                SnBelowArrayQuarter[quarter] += statList[1].actualValue;
//                                otherArrayQuarter[quarter] += statList[2].actualValue;
//                                // SnAboveArrayQuarter[quarter] = parseFloat(SnAboveArrayQuarter[quarter]).toFixed(2);
//                                // SnBelowArrayQuarter[quarter] = parseFloat(SnBelowArrayQuarter[quarter]).toFixed(2);
//                                // otherArrayQuarter[quarter] = parseFloat(otherArrayQuarter[quarter]).toFixed(2);
//                            } else {
//                                SnAboveArray.push(statList[0].actualValue);
//                                SnBelowArray.push(statList[1].actualValue);
//                                otherArray.push(statList[2].actualValue);
//                            }
//
//                        });
//
//                        if(quarterMode){
//                            SnAboveArray = SnAboveArrayQuarter;
//                            SnBelowArray = SnBelowArrayQuarter;
//                            otherArray = otherArrayQuarter;
//                        }
//					}
//                    var chartOption = {
//                        // backgroundColor: 'rgba(129,129,129,50)',
//                        tooltip : {     //提示框，鼠标悬浮交互时的信息提示
//                            trigger: 'axis',
//                            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
//                                type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
//                            },
//                            formatter: quarterMode?'第{b}季度<br/>{a0}:{c0}<br/>{a2}:{c2}':'{b}月<br/>{a0}:{c0}<br/>{a2}:{c2}'
////                            {a1}:{c1}<br/>
////                            {a1}:{c1}<br/>
//                        },
//                        title: {
//                            text: '销量详情(L)',
//                            left: '2%',
//                            subtextStyle: {
//                                color: '#0066b2'
//                            }
//                        },
//                        legend: {
//                            x: 'center', // 'center'
//                            data:['SN及以上(L)', 'SM及以下(L)', '其他(L)'],
//                            show:true
//                        },
//                        detail: {
//                            fontSize:10,
//                            fontWeight: 'normal',
//                            color:"#333333",
//                            // formatter:'\n\n\n\n\n{value} {name}%'
//                            offsetCenter: [0, 80],
//                            formatter:function(v){
////                                return "已经完成{emphasize|" + v + unit + "},完成目标的{emphasize|" + sellinData[1].percent + "%}";
//                                return "已经完成{emphasize|" + v + unit + "}";
//                            },
//                            rich: {
//                                emphasize: {
//                                    color: 'red',
//                                    fontWeight: "normal",
//                                    fontSize: 10,
//                                    padding:0
//                                },
//                                normal: {
//                                    color: '#333333'
//                                }
//                            }
//                        },
//                        yAxis: [{
//                            type: 'value',
//                            axisLabel:{
//                                // formatter: function(value,index){
//                                //     return value.toFixed(2).toLocaleString() + 'L';
//                                // },
//                                formatter:'{value} L',
//                                textStyle:{
//                                    fontSize : 10
//                                }
//                            }
//                        }],
//                        xAxis: [{
//                            type: 'category',
//                            data: quarterMode?quarterArray:monthArray,
//                            axisLabel:{
//                                interval:0,   // x轴的数据是否一定要全部显示
//                                formatter:quarterMode?'第{value}季度':'{value}月'
//                            }
//                        }],
//                        series: [
//                            {
//                                name: 'SN及以上(L)',
//                                data: SnAboveArray,
//                                type: 'bar',
//                                stack: '总销量',
//                                itemStyle: {
//                                    normal: {
//                                        color: 'rgb(0,102,178)',  //柱状图的颜色
//                                        label:{
//                                            formatter: function(a,b,c){
//                                                return util.toThousands(a.data.toFixed(2));
//                                            }
//                                        }
//                                    }
//                                },
//                                label: {
//                                    normal: {
//                                        show: true,
//                                        position: 'inside',
//                                        textStyle: {
//                                            color: 'rgba(255, 255, 255, 1)',
//                                            fontSize:9
//                                        }
//                                    }
//                                }
//                            },
//                            {
//                                name: 'SM及以下(L)',
//                                data: SnBelowArray,
//                                type: 'bar',
//                                stack: '总销量',
//                                itemStyle: {
//                                    normal: {
//                                        color: 'rgb(0,112,140)',  //柱状图的颜色
//                                        label:{
//                                            formatter: function(a,b,c){
//                                                return util.toThousands(a.data.toFixed(2));
//                                            }
//                                        }
//                                    }
//                                },
//                                label: {
//                                    normal: {
//                                        show: true,
//                                        position: 'inside',
//                                        textStyle: {
//                                            color: 'rgba(255, 255, 255, 1)',
//                                            fontSize:9
//                                        }
//                                    }
//                                }
//                            },
//                            {
//                                name: '其他(L)',
//                                data: otherArray,
//                                type: 'bar',
//                                stack: '总销量',
//                                itemStyle: {
//                                    normal: {
//                                        color: 'rgb(118,146,49)',  //柱状图的颜色
//                                        label:{
//                                            formatter: function(a,b,c){
//                                                return util.toThousands(a.data.toFixed(2));
//                                            }
//                                        }
//                                    }
//                                },
//                                label: {
//                                    normal: {
//                                        show: true,
//                                        position: 'inside',
//                                        textStyle: {
//                                            color: 'rgba(255, 255, 255, 1)',
//                                            fontSize:9
//                                        }
//                                    }
//                                }
//                            }]
//                    };
//                    sellinStatsByMonthAndTypeChart.hideLoading();
//                    sellinStatsByMonthAndTypeChart.setOption(chartOption);
//                    sellinStatsByMonthAndTypeChart.off('click');
//                    // 然后开始显示quarterlyTargetResult
//                    if(data.quarterlyTargetResult){
//                        // 'Others','SM & Below','SN & Above'
//                        var SN_Above_target = data.quarterlyTargetResult["SN & Above"];
//                        var SM_Below_target = data.quarterlyTargetResult["SM & Below"];
//                        var Others_target = data.quarterlyTargetResult["Others"];
//                        if(SN_Above_target != null && SN_Above_target!=undefined){
//                            $("#SN_Above_target").text(SN_Above_target + "L");
//                        } else {
//                            $("#SN_Above_target").text("");
//                        }
//                        if(SM_Below_target != null && SM_Below_target!=undefined){
//                            $("#SM_Below_target").text(SM_Below_target + "L");
//                        } else {
//                            $("#SM_Below_target").text("");
//                        }
//                        if(Others_target != undefined && Others_target != null){
//                            $("#Others_target").text(Others_target + "L");
//                        } else {
//                            $("#Others_target").text("");
//                        }
//                    }
//                },partnerId);
//			},
            loadSellinStatsByMonthAndSku: function(partnerId){
                req.getSellinStatsByMonthAndSku(currentYear,customerCategory,roleType,loginCai,partnerId,function (result) {
                    var productArray = [];
                    var actualValueArray = [];
                    if(result && result.length >0){
                        $.each(result,function (index,item) {
                            // monthArray.push(new Date(item.transTime).getMonth() + 1);
                            // var statList = item.statList;
                            actualValueArray.push(parseFloat(item.act_liters));
                            productArray.push(item.product_name_cn);
                        });
                    }
                    var chartOption = {
                        tooltip : {     //提示框，鼠标悬浮交互时的信息提示
                            trigger: 'axis',
                            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                                type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                            }
                        },
                        grid: {
                            y2: 240
                        },
                        title: {
                            text: '分sku销量情况',
                            left: '2%',
                            subtextStyle: {
                                color: '#0066b2'
                            }
                        },
                        legend: {
                            data:['销量(L)'],
                            show:true
                        },
                        yAxis: [{
                            type: 'value',
                            axisLabel:{
                                // formatter: function(value,index){
                                //     return value.toFixed(2).toLocaleString() + 'L';
                                // },
                                formatter:'{value} L',
                                textStyle:{
                                    fontSize : 10
                                }}
                        }],
                        xAxis: [{
                            type: 'category',
                            data: productArray,
                            axisLabel:{
                                // interval:0,   // x轴的数据是否一定要全部显示
                                formatter:'{value}',
                                rotate:'45',
                                textStyle:{
                                    fontSize:9
                                }
                            }
                        }],
                        series: [
                            {
                                name: '销量(L)',
                                data: actualValueArray,
                                type: 'bar',
                                itemStyle: {
                                    normal: {
                                        color: 'rgb(0,102,178)',  //柱状图的颜色
                                        label:{
                                            formatter: function(a,b,c){
                                                return util.toThousands(a.data.toFixed(2));
                                            }
                                        }
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'inside',
                                        textStyle: {
                                            color: 'rgba(255, 255, 255, 1)',
                                            fontSize:9
                                        },
                                        // formatter: '{c}',

                                    }
                                }
                            }
                        ]
                    };
                    sellinStatsByMonthAndSkuChart.hideLoading();
                    sellinStatsByMonthAndSkuChart.setOption(chartOption);
                    sellinStatsByMonthAndSkuChart.off('click');
                },partnerId);
            },
        	loadSellinStatsByMonth: function(partnerId){
				req.getSellinStatsByMonth(currentYear,customerCategory,roleType,loginCai,partnerId,function (result) {
					var monthArray = [1,2,3,4,5,6,7,8,9,10,11,12];
					var quarterArray = [1,2,3,4];
					var actualValueArray = [];
					var targetValueArray = [];
					var quarterMode = util.isQuarterMode();
					var list = result.sellInByMonthList
					if(list && list.length >0){
                        var actualValueArrayQuarter = [null,null,null,null];
                        var targetValueArrayQuarter = [null,null,null,null];
						$.each(list,function (index,item) {
							// monthArray.push(new Date(item.transTime).getMonth() + 1);
							// var statList = item.statList;
                            if(quarterMode){
                                var quarter = Math.floor(index/3);
                                if(actualValueArrayQuarter[quarter] == null){
                                    actualValueArrayQuarter[quarter] = 0;
                                }
                                if(targetValueArrayQuarter[quarter] == null){
                                    targetValueArrayQuarter[quarter] = 0;
                                }
                                actualValueArrayQuarter[quarter] += item.actualValue;
                                targetValueArrayQuarter[quarter] += item.targetValue;
                            } else {
                                actualValueArray.push(item.actualValue);
                                targetValueArray.push(item.targetValue);
                            }
						});
                        if(quarterMode){
                            actualValueArray = actualValueArrayQuarter;
                            targetValueArray = targetValueArrayQuarter;
//                             for(var j=0;j<targetValueArray.length;j++){
//                                 if(targetValueArray[j] != null){
//                                     targetValueArray[j] = targetValueArray[j].toFixed(0);
//                                 }
//                             }
//                             for(var i=0;i<actualValueArray.length;i++){
//                                 if(actualValueArray[i] != null){
//                                     actualValueArray[i] = actualValueArray[i].toFixed(0);
//                                 }
//                             }
                        }
					}
                    for(var h=0;h<actualValueArray.length;h++){
                        if(actualValueArray[h] == null && targetValueArray[h]){
                            actualValueArray[h] = 0;
                        }
                    }
					var chartOption = {
						tooltip : {     //提示框，鼠标悬浮交互时的信息提示
							trigger: 'axis',
							axisPointer : {            // 坐标轴指示器，坐标轴触发有效
								type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
							},
                            formatter: quarterMode?'第{b}季度<br/>{a0}:{c0}<br/>{a1}:{c1}<br/>':'{b}月<br/>{a0}:{c0}<br/>{a1}:{c1}<br/>'
//                            {a1}:{c1}
                        },
                        title: {
                            text: '总销量',
                            left: '2%',
                            subtextStyle: {
                                color: '#0066b2'
                            }
                        },
                        legend: {
                        	data:['目标(L)','实际值(L)'],
//                            data:['实际值(L)'],
                            show:true
                        },
						yAxis: [{
							type: 'value',
							axisLabel:{
                                // formatter: function(value,index){
                                //     return value.toFixed(2).toLocaleString() + 'L';
                                // },
                                formatter:'{value} L',
                                textStyle:{
                                    fontSize : 10
                                }
                            }
						}],
						xAxis: [{
							type: 'category',
							data: quarterMode?quarterArray:monthArray,
							axisLabel:{
								interval:0,   // x轴的数据是否一定要全部显示
								formatter: quarterMode?'第{value}季度':'{value}月'
							}
						}],
						series: [
							{
                                name: '实际值(L)',
                                data: actualValueArray,
								type: 'bar',
								itemStyle: {
									normal: {
										color: 'rgb(0,102,178)',  //柱状图的颜色
                                        label:{
                                            formatter: function(a,b,c){
                                                return util.toThousands(a.data.toFixed(2));
                                            }
                                        }
									}
								},
								label: {
									normal: {
										show: true,
										position: 'inside',
										textStyle: {
											color: 'rgba(255, 255, 255, 1)',
                                            fontSize:9
										}
									}
								}
							}
                            ,{
                                name: '目标(L)',
                                data: targetValueArray,
                                type: 'line',
                                itemStyle: {
                                    normal: {
                                        color: 'rgb(226,24,54)'  //柱状图的颜色
                                    }
                                },
                                label: {
                                    normal: {
                                        show: false,
                                        position: 'inside',
                                        textStyle: {
                                            color: 'rgba(255, 255, 255, 1)',
                                            fontSize:9
                                        },
                                        formatter: '{c}'
                                    }
                                }
                            }
                        ]
					};
                    totalSellinStatsByMonthChart.hideLoading();
                    totalSellinStatsByMonthChart.setOption(chartOption);
                    totalSellinStatsByMonthChart.off('click');
                    
                    var typeList = result.sellInByMonthAndTypeList;
            		var monthArray = [1,2,3,4,5,6,7,8,9,10,11,12];
                    var quarterArray = [1,2,3,4];
                    var SnAboveArray = [];
                    var SnBelowArray = [];
                    var otherArray = [];
                    var quarterMode = util.isQuarterMode();
                    if(typeList && typeList.length >0){
                        var SnAboveArrayQuarter = [null,null,null,null];
                        var SnBelowArrayQuarter = [null,null,null,null];
                        var otherArrayQuarter = [null,null,null,null];
                        $.each(typeList,function (index,item) {
                            // monthArray.push(new Date(item.transTime).getMonth() + 1);
                            var statList = item.statList;
                            if(quarterMode){
                                var quarter = Math.floor(index/3);
                                if(SnAboveArrayQuarter[quarter] == null){
                                    SnAboveArrayQuarter[quarter] = 0;
                                }
                                if(SnBelowArrayQuarter[quarter] == null){
                                    SnBelowArrayQuarter[quarter] = 0;
                                }
                                if(otherArrayQuarter[quarter] == null){
                                    otherArrayQuarter[quarter] = 0;
                                }
                                SnAboveArrayQuarter[quarter] += statList[0].actualValue;
                                SnBelowArrayQuarter[quarter] += statList[1].actualValue;
                                otherArrayQuarter[quarter] += statList[2].actualValue;
                                // SnAboveArrayQuarter[quarter] = parseFloat(SnAboveArrayQuarter[quarter]).toFixed(2);
                                // SnBelowArrayQuarter[quarter] = parseFloat(SnBelowArrayQuarter[quarter]).toFixed(2);
                                // otherArrayQuarter[quarter] = parseFloat(otherArrayQuarter[quarter]).toFixed(2);
                            } else {
                                SnAboveArray.push(statList[0].actualValue);
                                SnBelowArray.push(statList[1].actualValue);
                                otherArray.push(statList[2].actualValue);
                            }

                        });

                        if(quarterMode){
                            SnAboveArray = SnAboveArrayQuarter;
                            SnBelowArray = SnBelowArrayQuarter;
                            otherArray = otherArrayQuarter;
                        }
					}
                    var chartOption = {
                        // backgroundColor: 'rgba(129,129,129,50)',
                        tooltip : {     //提示框，鼠标悬浮交互时的信息提示
                            trigger: 'axis',
                            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                                type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                            },
                            formatter: quarterMode?'第{b}季度<br/>{a0}:{c0}<br/>{a1}:{c1}<br/>{a2}:{c2}':'{b}月<br/>{a0}:{c0}<br/>{a1}:{c1}<br/>{a2}:{c2}'
//                            {a1}:{c1}<br/>
//                            {a1}:{c1}<br/>
                        },
                        title: {
                            text: '销量详情(L)',
                            left: '2%',
                            subtextStyle: {
                                color: '#0066b2'
                            }
                        },
                        legend: {
                            x: 'center', // 'center'
                            data:['SN及以上(L)', 'SM及以下(L)', '其他(L)'],
                            show:true
                        },
                        detail: {
                            fontSize:10,
                            fontWeight: 'normal',
                            color:"#333333",
                            // formatter:'\n\n\n\n\n{value} {name}%'
                            offsetCenter: [0, 80],
                            formatter:function(v){
//                                return "已经完成{emphasize|" + v + unit + "},完成目标的{emphasize|" + sellinData[1].percent + "%}";
                                return "已经完成{emphasize|" + v + unit + "}";
                            },
                            rich: {
                                emphasize: {
                                    color: 'red',
                                    fontWeight: "normal",
                                    fontSize: 10,
                                    padding:0
                                },
                                normal: {
                                    color: '#333333'
                                }
                            }
                        },
                        yAxis: [{
                            type: 'value',
                            axisLabel:{
                                // formatter: function(value,index){
                                //     return value.toFixed(2).toLocaleString() + 'L';
                                // },
                                formatter:'{value} L',
                                textStyle:{
                                    fontSize : 10
                                }
                            }
                        }],
                        xAxis: [{
                            type: 'category',
                            data: quarterMode?quarterArray:monthArray,
                            axisLabel:{
                                interval:0,   // x轴的数据是否一定要全部显示
                                formatter:quarterMode?'第{value}季度':'{value}月'
                            }
                        }],
                        series: [
                            {
                                name: 'SN及以上(L)',
                                data: SnAboveArray,
                                type: 'bar',
                                stack: '总销量',
                                itemStyle: {
                                    normal: {
                                        color: 'rgb(0,102,178)',  //柱状图的颜色
                                        label:{
                                            formatter: function(a,b,c){
                                                return util.toThousands(a.data.toFixed(2));
                                            }
                                        }
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'inside',
                                        textStyle: {
                                            color: 'rgba(255, 255, 255, 1)',
                                            fontSize:9
                                        }
                                    }
                                }
                            },
                            {
                                name: 'SM及以下(L)',
                                data: SnBelowArray,
                                type: 'bar',
                                stack: '总销量',
                                itemStyle: {
                                    normal: {
                                        color: 'rgb(0,112,140)',  //柱状图的颜色
                                        label:{
                                            formatter: function(a,b,c){
                                                return util.toThousands(a.data.toFixed(2));
                                            }
                                        }
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'inside',
                                        textStyle: {
                                            color: 'rgba(255, 255, 255, 1)',
                                            fontSize:9
                                        }
                                    }
                                }
                            },
                            {
                                name: '其他(L)',
                                data: otherArray,
                                type: 'bar',
                                stack: '总销量',
                                itemStyle: {
                                    normal: {
                                        color: 'rgb(118,146,49)',  //柱状图的颜色
                                        label:{
                                            formatter: function(a,b,c){
                                                return util.toThousands(a.data.toFixed(2));
                                            }
                                        }
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'inside',
                                        textStyle: {
                                            color: 'rgba(255, 255, 255, 1)',
                                            fontSize:9
                                        }
                                    }
                                }
                            }]
                    };
                    sellinStatsByMonthAndTypeChart.hideLoading();
                    sellinStatsByMonthAndTypeChart.setOption(chartOption);
                    sellinStatsByMonthAndTypeChart.off('click');
                    // 然后开始显示quarterlyTargetResult
//                    if(typeList.quarterlyTargetResult){
//                        // 'Others','SM & Below','SN & Above'
//                        var SN_Above_target = data.quarterlyTargetResult["SN & Above"];
//                        var SM_Below_target = data.quarterlyTargetResult["SM & Below"];
//                        var Others_target = data.quarterlyTargetResult["Others"];
//                        if(SN_Above_target != null && SN_Above_target!=undefined){
//                            $("#SN_Above_target").text(SN_Above_target + "L");
//                        } else {
//                            $("#SN_Above_target").text("");
//                        }
//                        if(SM_Below_target != null && SM_Below_target!=undefined){
//                            $("#SM_Below_target").text(SM_Below_target + "L");
//                        } else {
//                            $("#SM_Below_target").text("");
//                        }
//                        if(Others_target != undefined && Others_target != null){
//                            $("#Others_target").text(Others_target + "L");
//                        } else {
//                            $("#Others_target").text("");
//                        }
//                    }
                    var targetValue =  result.sellinInfo?result.sellinInfo.targetValue:null;
                    var actualValue =  result.sellinInfo?result.sellinInfo.actualValue:null;

                    if(targetValue != null && actualValue!=null){
                        var sellinPercent = targetValue == 0 ? 0 : (actualValue*100 / targetValue);
                        if(typeof sellinPercent.toFixed === "function" ){
                            sellinPercent = sellinPercent.toFixed(2);
                        }
                        $("#sell_in_value").text(util.toThousands(actualValue) + "L");
                        $("#sell_in_percent").text(sellinPercent + "%");
                    } else {
                        $("#sell_in_value").text(util.toThousands(actualValue) + "L");
                        $("#sell_in_percent").text("");
                    }

                    var grossTargetValue =  result.grossInfo?result.grossInfo.targetValue:null;
                    var grossActualValue =  result.grossInfo?result.grossInfo.actualValue:null;
                    if(grossTargetValue != null && grossActualValue!=null){
                    	var sellinPercent = grossTargetValue == 0 ? 0 : (grossActualValue*100 / grossTargetValue);
                        if(typeof sellinPercent.toFixed === "function" ){
                            sellinPercent = sellinPercent.toFixed(2);
                        }
                        $("#gross_info_value").text(util.toThousands(grossActualValue) + "USD");
                        $("#gross_info_percent").text(sellinPercent + "%");
                    } else {
                        $("#gross_info_value").text(util.toThousands(grossActualValue) + "USD");
                        $("#gross_info_percent").text("");
                    }
				},partnerId);
			},
            initEcharts: function() {
                if ($('#sellinStatsByMonthAndTypeChart').size()) {
                    if(!sellinStatsByMonthAndTypeChart){
                        sellinStatsByMonthAndTypeChart = echarts.init($('#sellinStatsByMonthAndTypeChart')[0]);
                    }
                    $(window).resize(sellinStatsByMonthAndTypeChart.resize);
                    sellinStatsByMonthAndTypeChart.resize();
                    sellinStatsByMonthAndTypeChart.showLoading({
                        color: '#c23531',
                        maskColor: 'rgba(200, 200, 200, 0.5)',
                        text : '正在努力的读取数据中...'
                    });
                }
                if ($('#totalSellinStatsByMonthChart').size()) {
                    if(!totalSellinStatsByMonthChart){
                        totalSellinStatsByMonthChart = echarts.init($('#totalSellinStatsByMonthChart')[0]);
                    }
                    $(window).resize(totalSellinStatsByMonthChart.resize);
                    totalSellinStatsByMonthChart.resize();
                    totalSellinStatsByMonthChart.showLoading({
                        color: '#c23531',
                        maskColor: 'rgba(200, 200, 200, 0.5)',
                        text : '正在努力的读取数据中...'
                    });
                }
                if ($('#sellinStatsByMonthAndSkuChart').size()) {
                    if(!sellinStatsByMonthAndSkuChart){
                        sellinStatsByMonthAndSkuChart = echarts.init($('#sellinStatsByMonthAndSkuChart')[0]);
                    }
                    $(window).resize(sellinStatsByMonthAndSkuChart.resize);
                    sellinStatsByMonthAndSkuChart.resize();
                    sellinStatsByMonthAndSkuChart.showLoading({
                        color: '#c23531',
                        maskColor: 'rgba(200, 200, 200, 0.5)',
                        text : '正在努力的读取数据中...'
                    });
                }
            },
		    // SNScanInfoGrid相关
			loadSellThrouth: function() {
				req.getSellThrouthInfo(function(data) {
					//季度数据初始化
					var sellThrouthQuarterList = [];
					for(var q = 1; q <= 4; q++) {
						sellThrouthQuarterList.push({
							quarter: q,
							sellIn: 0,
							sellThrouth: 0,
							workshopCount: 0,
							targetBaseLine: 0,
							sellThrouthFlag: true,
						});
					}
					
					var snMonthTarget = 0;
					for(var i in data) {
						var month = data[i].month;
						var sellThrouth = data[i].sellThrouth;
						var sellIn = data[i].sellIn;
						var workshopCount = data[i].workshopCount;
						var targetBaseLine = data[i].targetBaseLine;
						//每月目标
						if(month == currentMonth) {
							snMonthTarget = (targetBaseLine * 0.5).toFixed(0);
						}
						//生成季度数据
						var quarterIndex = parseInt((month + 2) / 3) - 1;
						sellThrouthQuarterList[quarterIndex].sellIn += sellIn;
						sellThrouthQuarterList[quarterIndex].sellThrouth += sellThrouth;
						sellThrouthQuarterList[quarterIndex].workshopCount += workshopCount;
						sellThrouthQuarterList[quarterIndex].targetBaseLine += targetBaseLine;
						//月目标是否达标
						var $productMonthSellInSpan = $('<span></span>');
						var $productMonthSellInIcon = $('<i aria-hidden="true"></i>');
						if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
							$productMonthSellInIcon.addClass(iconRightClass);
						} else if (sellThrouth / (targetBaseLine * 0.5) < 1 && sellThrouth / (targetBaseLine * 0.5) > 0.75) {
							$productMonthSellInIcon.addClass(iconWarmingClass);
							if(month < currentMonth) {
								sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
							}
						} else {
							$productMonthSellInIcon.addClass(iconNotUpClass);
							if(month < currentMonth) {
								sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
							}
						}
						
						$productMonthSellInSpan.append($productMonthSellInIcon);
						$productMonthSellInSpan.append(util.toThousands(sellThrouth));
						
						$("#p-actual-m" + month).empty();
						$("#p-actual-m" + month).append($productMonthSellInSpan);
					}
					$("#sn-month-target").html(snMonthTarget + "&nbsp;&nbsp;升");
					
					//季度数据加载
					for(var qi in sellThrouthQuarterList) {
						var quarter = sellThrouthQuarterList[qi].quarter;
						var sellThrouth = sellThrouthQuarterList[qi].sellThrouth;
						var sellIn = sellThrouthQuarterList[qi].sellIn;
						var workshopCount = sellThrouthQuarterList[qi].workshopCount;
						var targetBaseLine = sellThrouthQuarterList[qi].targetBaseLine;
						var sellThrouthFlag = sellThrouthQuarterList[qi].sellThrouthFlag;
						//扫码季度达标
						$pTarget = $("#p-target-q" + quarter);
						if(!sellThrouthFlag) {
							$pTarget.addClass(notUpTextClass);
							$pTarget.html(notUpText);
						} else {
							if(quarter >= currentQuarter) {
								$pTarget.addClass(runningTextClass);
								$pTarget.html(runningText);
							} else {
								if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
									$pTarget.addClass(rightTextClass);
									$pTarget.html(rightText);
								} else if(sellThrouth / (targetBaseLine * 0.5) < 1) {
									$pTarget.addClass(notUpTextClass);
									$pTarget.html(notUpText);
								}
							}
						}
						
						//新维修门店
						var $storeCountSpan = $('<span></span>');
						var $storeCountIcon = $('<i aria-hidden="true"></i>');
						if(workshopCount >= workshopTarget) {
							$storeCountIcon.addClass(iconRightClass);
						} else if(workshopCount < workshopTarget && workshopCount >= 6) {
							$storeCountIcon.addClass(iconWarmingClass);
						} else {
							$storeCountIcon.addClass(iconNotUpClass);
						}
						
						$storeCountSpan.append($storeCountIcon);
						$storeCountSpan.append(util.toThousands(workshopCount));
						
						$("#s-actual-q" + quarter).empty();
						$("#s-actual-q" + quarter).append($storeCountSpan);
						//门店达标
						$sTraget = $("#s-target-q" + quarter);
						if(quarter >= currentQuarter) {
							$sTraget.addClass(runningTextClass);
							$sTraget.html(runningText);
						} else {
							if(workshopCount >= workshopTarget) {
								$sTraget.addClass(rightTextClass);
								$sTraget.html(rightText);
							} else {
								$sTraget.addClass(notUpTextClass);
								$sTraget.html(notUpText);
							}
						}
						
					}
				});
			},
		    // total-sales-target,对应"总销量目标"和季度的那些值
			loadQuarterlySales: function() {
				req.getQuarterlySellInfo(function(data) {
					var totalSalesTraget = 0;
					for(var i in data) {
						var quarter = data[i].quarter;
						var targetBaseLine = data[i].targetBaseline;
						var sellIn = data[i].sellIn;
						var monthAvgTarget = (targetBaseLine / 3).toFixed(0);
						//总目标销量
						totalSalesTraget += targetBaseLine;
						//进货量基数标准
						$("#target-q" + quarter).html(util.toThousands(targetBaseLine));
						//月平均进货目标=季度进货量基数标准/3
						for(var j = 0; j < 3; j++) {
							month = 3 * quarter - j;
							$("#target-m" + month).html(util.toThousands(monthAvgTarget));
							$("#target-m" + month).attr("title",util.toThousands(monthAvgTarget));
						}
						//季度实际完成进货量
						var sellInDiv = $('<div></div>');
						sellInDiv.html(util.toThousands(sellIn));
						if(sellIn / targetBaseLine < 0.8) {
							sellInDiv.addClass("not-up");
						} else if (sellIn / targetBaseLine < 1 && sellIn / targetBaseLine > 0.8) {
							sellInDiv.addClass("warning");
						} else {
							sellInDiv.addClass("up");
						}
						$("#actual-q" + quarter).html(sellInDiv);
                        $("#actual-q" + quarter).attr("title",util.toThousands(sellIn));
						//季度实际完成比例
						var percent = "";
						if(targetBaseLine != 0) {
							percent = (sellIn / targetBaseLine * 100).toFixed(0) + "%";
						}
						$("#percent-q" + quarter).html(percent);
					}
					$("#total-sales-target").html(totalSalesTraget + "&nbsp;&nbsp;升");
				});
			},
        	// totalPerformanceInfoGrid 月平均进货量
			loadMontlySales: function() {
				req.getMonthlySellInfo(function(data){
					for(var i in data) {
						var month = data[i].month;
						var sellIn = data[i].sellIn;
						$("#actual-m" + month).html(util.toThousands(sellIn));
						$("#actual-m" + month).attr("title",(util.toThousands(sellIn)));
					}
				});
			},
		    // marketingInfoGrid,每季度成功开发的新维修门店目标
			loadLocalGoal: function() {
				for(var i = 1; i <= 12; i++) {
					$goalSpan = $("#gola-m" + i);
					$goalSpan.empty();
//					if(i <= currentMonth) {
//						$SpanContent = $('<i class="fa fa-check-circle-o right-icon" aria-hidden="true"></i>');
//						$goalSpan.append($SpanContent);
//						$goalSpan.append('完成');
//					}
				}
			},
        initOrganizationSelect: function () {
        // 初始化，机构下拉框
			if(initPartnerName && initPartnerId){
				$('#partnerId').val(initPartnerId).attr('text', initPartnerName)
            }
			organizationCtrl = OrgAutoSelect.init('#partnerId');
		},
        initSearch: function () {
			$("#search").click(function () {
				util.doSearch();
            });
        },
        initYear: function() {
            var thisYear = new Date().getFullYear();
            for (var j = 0; j < 2; j++) {//TODO
            	var i = thisYear - j;
                $('#year_c ul')
                    .append($('<li class="'+(i === currentYear?'active':'')+'">'+i+'</li>').click(function() {
                        $(this).siblings().removeClass('active');
                        $(this).addClass('active');
                        // var partnerId = $(".partner-list-wrapper ul li.active").attr("data-id");
                        // loader.initPerformanceTable(partnerId, $(this).html());
//                        loader.refreshMonthList($(this).text());
//                        util.doSearch()
                        reloadPage({
                        	year: $(this).text()
                        });
                    }));
            }
            util.doSearch(initPartnerId);
//            loader.refreshMonthList(thisYear);
        },
        initSkuQuarterAndYear: function(){
            BUI.use('bui/select', function(Select) {
                var nowMonth = currentMonth;
                var nowQuarter = currentQuarter;
                var monthItems = [];
                var quarterItems = [];
                for(var i = 1;i<=nowMonth;i++){
                    monthItems.push({
                        text : i + '月',
                        value : i
                    });
                }
                for(var j = 1;j<=nowQuarter;j++){
                    quarterItems.push({
                        text : "第" + j + '季度',
                        value : j
                    });
                }
                $('#skuQuarter').val(nowQuarter);
                var quarterCtrl = new Select.Select({
                    render : '#skuQuarterC',
                    valueField : '#skuQuarter',
                    width : '100%',
                    elStyle : {
                        'white-space' : 'nowrap'
                    },
                    items : quarterItems
                });
                quarterCtrl.render();
                quarterCtrl.on('change', function(ev) {
                    sellinStatsByMonthAndSkuChart.showLoading({
                        color: '#c23531',
                        maskColor: 'rgba(200, 200, 200, 0.5)',
                        text : '正在努力的读取数据中...'
                    });
                    loader.loadSellinStatsByMonthAndSku();
                });

                $('#skuMonth').val(nowMonth);
                var monthCtrl = new Select.Select({
                    render : '#skuMonthC',
                    valueField : '#skuMonth',
                    width : '100%',
                    elStyle : {
                        'white-space' : 'nowrap'
                    },
                    items : monthItems
                });
                monthCtrl.render();
                monthCtrl.on('change', function(ev) {
                    sellinStatsByMonthAndSkuChart.showLoading({
                        color: '#c23531',
                        maskColor: 'rgba(200, 200, 200, 0.5)',
                        text : '正在努力的读取数据中...'
                    });
                    loader.loadSellinStatsByMonthAndSku();
                });
            });
        },
        initGroupByTypes: function(){
            for (var i = 0; i < groupByTypes.length; i++) {
                var groupByType = groupByTypes[i], 
                	active = currentParams.groupByType ? (currentParams.groupByType == groupByType.value) : i === DEFAULT_GROUP_BY_TYPE_INDEX;
                $('#group_by_type_c ul')
                    .append($('<li data-group-type="'+ groupByType.value + '" class="'+(active?'active':'')+'">'+groupByType.text+'</li>').click(function() {
                        $(this).siblings().removeClass('active');
                        currentParams.groupByType = $(this).addClass('active').attr('data-group-type');
                        util.doSearch();
                    }));
            }
        },
        refreshMonthList: function(year) {
            $('#year-mode .monthes-wrapper ul').empty();
            var now = new Date();
            var thisYear = now.getFullYear();
            var maxMonth = 12, thisMonth;
            if (parseInt(year) === thisYear) {
                maxMonth = thisMonth = now.getMonth() + 1;
            }
            // $('#year-mode .monthes-wrapper ul').append($('<li class="" >全年</li>').click(function() {
            //     $(this).siblings().removeClass('active');
            //     $(this).addClass('active');
            //     util.doSearch();
            // }));
            for (var i = 1; i <= maxMonth; i++) {
                $('#year-mode .monthes-wrapper ul')
                    .append($('<li data-month="'+i+'" class="'+(i === thisMonth?'active':'')+'">'+i+'</li>').click(function() {
                        $(this).siblings().removeClass('active');
                        $(this).addClass('active');
                        util.doSearch();
                    }));
            }
        },
        loadSellinInfoYTD: function() {
            req.getSellinInfoYTD(currentYear,customerCategory,roleType,loginCai,distributorId,function (data){
                var targetValue =  data.sellinInfo?data.sellinInfo.targetValue:null;
                var actualValue =  data.sellinInfo?data.sellinInfo.actualValue:null;

                if(targetValue != null && actualValue!=null){
                    var sellinPercent = targetValue == 0 ? 0 : (actualValue*100 / targetValue);
                    if(typeof sellinPercent.toFixed === "function" ){
                        sellinPercent = sellinPercent.toFixed(2);
                    }
                    $("#sell_in_value").text(util.toThousands(actualValue) + "L");
                    $("#sell_in_percent").text(sellinPercent + "%");
                } else {
                    $("#sell_in_value").text(util.toThousands(actualValue) + "L");
                    $("#sell_in_percent").text("");
                }

                var grossTargetValue =  data.grossInfo?data.grossInfo.targetValue:null;
                var grossActualValue =  data.grossInfo?data.grossInfo.actualValue:null;
                if(grossTargetValue != null && grossActualValue!=null){
                    sellinPercent = (grossActualValue*100 / grossTargetValue);
                    if(typeof sellinPercent.toFixed === "function" ){
                        sellinPercent = sellinPercent.toFixed(2);
                    }
                    $("#gross_info_value").text(util.toThousands(grossActualValue) + "USD");
                    $("#gross_info_percent").text(sellinPercent + "%");
                } else {
                    $("#gross_info_value").text(util.toThousands(grossActualValue) + "USD");
                    $("#gross_info_percent").text("");
                }
            });
        }
	},
	action = {
	};
	
	return {
		init: function() {
		    loader.initSkuQuarterAndYear();
			loader.initOrganizationSelect();
            loader.initSearch();
            loader.initGroupByTypes();
             loader.initYear();
            // if(initPartnerId && initPartnerName) {
//                util.doSearch(initPartnerId);
            // }
			// loader.loadMarketIviInfo();
			// loader.loadMontlySales();
			// loader.loadSellThrouth();
			// loader.loadQuarterlySales();
            // loader.loadLocalGoal();
            // loader.loadSysMessage();
//			loader.loadRebateInfo();
//			loader.loadlastYearRebateInfo();
// 			loader.loadCdmPoints();
// 			loader.loadWorkshop();
// 			loader.loadSupportOrderPhone();
		},
		openUrl: function(url, menuId, menuTitle) {
			util.openUrl(url, menuId, menuTitle);
		},
		openMenu: function(url) {
			util.openMenu(url);
		},
		openPage: function(url, type) {
			util.openPage(url, type);
		},
        getSelectedYear: function() {
            var year = util.getSelectedYear() || new Date().getFullYear();
            return year;
        },
        getSelectedMonth: function () {
            var month = util.getSelectedMonth() || new Date().getMonth() + 1;
            return month;
        },
        divToThousands: function (num,fixedNum) {
            return util.divToThousands(num,fixedNum);
        }
	};
	
}($));