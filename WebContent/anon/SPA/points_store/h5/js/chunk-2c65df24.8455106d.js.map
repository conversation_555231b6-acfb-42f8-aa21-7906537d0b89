{"version": 3, "sources": ["webpack:///./src/views/order/Logistics.vue?7ae2", "webpack:///./node_modules/clipboard/dist/clipboard.js", "webpack:///./src/views/order/Logistics.vue?85a6", "webpack:///src/views/order/Logistics.vue", "webpack:///./src/views/order/Logistics.vue?a2f6", "webpack:///./src/views/order/Logistics.vue", "webpack:///./src/assets/images/noExpress.png"], "names": ["root", "factory", "module", "exports", "this", "modules", "installedModules", "__webpack_require__", "moduleId", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_typeof", "iterator", "obj", "constructor", "_createClass", "defineProperties", "target", "props", "length", "descriptor", "configurable", "writable", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_clipboardAction", "_clipboardAction2", "_interopRequireDefault", "_tinyEmitter", "_tinyEmitter2", "_goodListener", "_goodListener2", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "ReferenceError", "_inherits", "subClass", "superClass", "setPrototypeOf", "__proto__", "Clipboard", "_Emitter", "trigger", "options", "_this", "getPrototypeOf", "resolveOptions", "listenClick", "arguments", "undefined", "action", "defaultAction", "defaultTarget", "text", "defaultText", "container", "document", "body", "_this2", "listener", "e", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "currentTarget", "clipboardAction", "emitter", "getAttributeValue", "selector", "querySelector", "destroy", "actions", "support", "queryCommandSupported", "for<PERSON>ach", "suffix", "element", "attribute", "hasAttribute", "getAttribute", "_select", "_select2", "ClipboardAction", "initSelection", "selectedText", "selectFake", "selectTarget", "isRTL", "documentElement", "removeFake", "fakeHandlerCallback", "<PERSON><PERSON><PERSON>ler", "addEventListener", "fakeElem", "createElement", "style", "fontSize", "border", "padding", "margin", "position", "yPosition", "window", "pageYOffset", "scrollTop", "top", "setAttribute", "append<PERSON><PERSON><PERSON>", "copyText", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "succeeded", "execCommand", "err", "handleResult", "emit", "clearSelection", "focus", "getSelection", "removeAllRanges", "set", "_action", "Error", "nodeType", "_target", "select", "nodeName", "isReadOnly", "setSelectionRange", "removeAttribute", "selection", "range", "createRange", "selectNodeContents", "addRange", "toString", "E", "on", "callback", "ctx", "push", "fn", "once", "off", "apply", "_", "data", "slice", "evtArr", "len", "evts", "liveEvents", "is", "delegate", "listen", "type", "string", "node", "listenNode", "nodeList", "listenNodeList", "listenSelector", "Array", "HTMLElement", "String", "closest", "_delegate", "useCapture", "listenerFn", "elements", "querySelectorAll", "map", "DOCUMENT_NODE_TYPE", "Element", "matches", "proto", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "parentNode", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "_l", "cart", "id", "attrs", "productInfo", "image", "_v", "_s", "store_name", "true_price", "cart_num", "express", "index", "class", "status", "time", "context", "loaded", "expressList", "_e", "staticRenderFns", "NAME", "$route", "params", "cartInfo", "orderInfo", "watch", "getExpress", "mounted", "methods", "component"], "mappings": "yIAAA,yBAA0f,EAAG,G;;;;;;;CCM7f,SAA2CA,EAAMC,GAE/CC,EAAOC,QAAUF,KAFnB,CASGG,GAAM,WACT,OAAgB,SAAUC,GAEhB,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUL,QAGnC,IAAID,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHP,QAAS,IAUV,OANAE,EAAQG,GAAUG,KAAKT,EAAOC,QAASD,EAAQA,EAAOC,QAASI,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOC,QA0Df,OArDAI,EAAoBK,EAAIP,EAGxBE,EAAoBM,EAAIP,EAGxBC,EAAoBO,EAAI,SAASX,EAASY,EAAMC,GAC3CT,EAAoBU,EAAEd,EAASY,IAClCG,OAAOC,eAAehB,EAASY,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhET,EAAoBe,EAAI,SAASnB,GACX,qBAAXoB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAehB,EAASoB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAehB,EAAS,aAAc,CAAEsB,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBO,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAS/B,GAChC,IAAIc,EAASd,GAAUA,EAAO0B,WAC7B,WAAwB,OAAO1B,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAK,EAAoBO,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRT,EAAoBU,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG5B,EAAoB+B,EAAI,GAIjB/B,EAAoBA,EAAoBgC,EAAI,GAnF7C,CAsFN,CAEJ,SAAUrC,EAAQC,EAASI,GAEjC,aAGA,IAAIiC,EAA4B,oBAAXjB,QAAoD,kBAApBA,OAAOkB,SAAwB,SAAUC,GAAO,cAAcA,GAAS,SAAUA,GAAO,OAAOA,GAAyB,oBAAXnB,QAAyBmB,EAAIC,cAAgBpB,QAAUmB,IAAQnB,OAAOa,UAAY,gBAAkBM,GAElQE,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItC,EAAI,EAAGA,EAAIsC,EAAMC,OAAQvC,IAAK,CAAE,IAAIwC,EAAaF,EAAMtC,GAAIwC,EAAW7B,WAAa6B,EAAW7B,aAAc,EAAO6B,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAMjC,OAAOC,eAAe2B,EAAQG,EAAWlB,IAAKkB,IAAiB,OAAO,SAAUG,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYR,EAAiBO,EAAYhB,UAAWiB,GAAiBC,GAAaT,EAAiBO,EAAaE,GAAqBF,GAA7gB,GAEfG,EAAmBhD,EAAoB,GAEvCiD,EAAoBC,EAAuBF,GAE3CG,EAAenD,EAAoB,GAEnCoD,EAAgBF,EAAuBC,GAEvCE,EAAgBrD,EAAoB,GAEpCsD,EAAiBJ,EAAuBG,GAE5C,SAASH,EAAuBf,GAAO,OAAOA,GAAOA,EAAId,WAAac,EAAM,CAAEoB,QAASpB,GAEvF,SAASqB,EAAgBC,EAAUZ,GAAe,KAAMY,aAAoBZ,GAAgB,MAAM,IAAIa,UAAU,qCAEhH,SAASC,EAA2BC,EAAMxD,GAAQ,IAAKwD,EAAQ,MAAM,IAAIC,eAAe,6DAAgE,OAAOzD,GAAyB,kBAATA,GAAqC,oBAATA,EAA8BwD,EAAPxD,EAElO,SAAS0D,EAAUC,EAAUC,GAAc,GAA0B,oBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAIN,UAAU,kEAAoEM,GAAeD,EAASlC,UAAYlB,OAAOY,OAAOyC,GAAcA,EAAWnC,UAAW,CAAEO,YAAa,CAAElB,MAAO6C,EAAUlD,YAAY,EAAO+B,UAAU,EAAMD,cAAc,KAAeqB,IAAYrD,OAAOsD,eAAiBtD,OAAOsD,eAAeF,EAAUC,GAAcD,EAASG,UAAYF,GAMje,IAAIG,EAAY,SAAUC,GAOtB,SAASD,EAAUE,EAASC,GACxBd,EAAgB3D,KAAMsE,GAEtB,IAAII,EAAQZ,EAA2B9D,MAAOsE,EAAUD,WAAavD,OAAO6D,eAAeL,IAAY/D,KAAKP,OAI5G,OAFA0E,EAAME,eAAeH,GACrBC,EAAMG,YAAYL,GACXE,EAsIX,OAnJAT,EAAUK,EAAWC,GAuBrB/B,EAAa8B,EAAW,CAAC,CACrB3C,IAAK,iBACLN,MAAO,WACH,IAAIoD,EAAUK,UAAUlC,OAAS,QAAsBmC,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,GAElF9E,KAAKgF,OAAmC,oBAAnBP,EAAQO,OAAwBP,EAAQO,OAAShF,KAAKiF,cAC3EjF,KAAK0C,OAAmC,oBAAnB+B,EAAQ/B,OAAwB+B,EAAQ/B,OAAS1C,KAAKkF,cAC3ElF,KAAKmF,KAA+B,oBAAjBV,EAAQU,KAAsBV,EAAQU,KAAOnF,KAAKoF,YACrEpF,KAAKqF,UAA2C,WAA/BjD,EAAQqC,EAAQY,WAA0BZ,EAAQY,UAAYC,SAASC,OAQ7F,CACC5D,IAAK,cACLN,MAAO,SAAqBmD,GACxB,IAAIgB,EAASxF,KAEbA,KAAKyF,UAAW,EAAIhC,EAAeC,SAASc,EAAS,SAAS,SAAUkB,GACpE,OAAOF,EAAOG,QAAQD,QAS/B,CACC/D,IAAK,UACLN,MAAO,SAAiBqE,GACpB,IAAIlB,EAAUkB,EAAEE,gBAAkBF,EAAEG,cAEhC7F,KAAK8F,kBACL9F,KAAK8F,gBAAkB,MAG3B9F,KAAK8F,gBAAkB,IAAI1C,EAAkBM,QAAQ,CACjDsB,OAAQhF,KAAKgF,OAAOR,GACpB9B,OAAQ1C,KAAK0C,OAAO8B,GACpBW,KAAMnF,KAAKmF,KAAKX,GAChBa,UAAWrF,KAAKqF,UAChBb,QAASA,EACTuB,QAAS/F,SASlB,CACC2B,IAAK,gBACLN,MAAO,SAAuBmD,GAC1B,OAAOwB,EAAkB,SAAUxB,KAQxC,CACC7C,IAAK,gBACLN,MAAO,SAAuBmD,GAC1B,IAAIyB,EAAWD,EAAkB,SAAUxB,GAE3C,GAAIyB,EACA,OAAOX,SAASY,cAAcD,KAUvC,CACCtE,IAAK,cAOLN,MAAO,SAAqBmD,GACxB,OAAOwB,EAAkB,OAAQxB,KAOtC,CACC7C,IAAK,UACLN,MAAO,WACHrB,KAAKyF,SAASU,UAEVnG,KAAK8F,kBACL9F,KAAK8F,gBAAgBK,UACrBnG,KAAK8F,gBAAkB,SAG/B,CAAC,CACDnE,IAAK,cACLN,MAAO,WACH,IAAI2D,EAASF,UAAUlC,OAAS,QAAsBmC,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAC,OAAQ,OAEtFsB,EAA4B,kBAAXpB,EAAsB,CAACA,GAAUA,EAClDqB,IAAYf,SAASgB,sBAMzB,OAJAF,EAAQG,SAAQ,SAAUvB,GACtBqB,EAAUA,KAAaf,SAASgB,sBAAsBtB,MAGnDqB,MAIR/B,EApJK,CAqJdf,EAAcG,SAShB,SAASsC,EAAkBQ,EAAQC,GAC/B,IAAIC,EAAY,kBAAoBF,EAEpC,GAAKC,EAAQE,aAAaD,GAI1B,OAAOD,EAAQG,aAAaF,GAGhC5G,EAAOC,QAAUuE,GAIX,SAAUxE,EAAQC,EAASI,GAEjC,aAGA,IAAIiC,EAA4B,oBAAXjB,QAAoD,kBAApBA,OAAOkB,SAAwB,SAAUC,GAAO,cAAcA,GAAS,SAAUA,GAAO,OAAOA,GAAyB,oBAAXnB,QAAyBmB,EAAIC,cAAgBpB,QAAUmB,IAAQnB,OAAOa,UAAY,gBAAkBM,GAElQE,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItC,EAAI,EAAGA,EAAIsC,EAAMC,OAAQvC,IAAK,CAAE,IAAIwC,EAAaF,EAAMtC,GAAIwC,EAAW7B,WAAa6B,EAAW7B,aAAc,EAAO6B,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAMjC,OAAOC,eAAe2B,EAAQG,EAAWlB,IAAKkB,IAAiB,OAAO,SAAUG,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYR,EAAiBO,EAAYhB,UAAWiB,GAAiBC,GAAaT,EAAiBO,EAAaE,GAAqBF,GAA7gB,GAEf6D,EAAU1G,EAAoB,GAE9B2G,EAAWzD,EAAuBwD,GAEtC,SAASxD,EAAuBf,GAAO,OAAOA,GAAOA,EAAId,WAAac,EAAM,CAAEoB,QAASpB,GAEvF,SAASqB,EAAgBC,EAAUZ,GAAe,KAAMY,aAAoBZ,GAAgB,MAAM,IAAIa,UAAU,qCAMhH,IAAIkD,EAAkB,WAIlB,SAASA,EAAgBtC,GACrBd,EAAgB3D,KAAM+G,GAEtB/G,KAAK4E,eAAeH,GACpBzE,KAAKgH,gBAwOT,OA/NAxE,EAAauE,EAAiB,CAAC,CAC3BpF,IAAK,iBACLN,MAAO,WACH,IAAIoD,EAAUK,UAAUlC,OAAS,QAAsBmC,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,GAElF9E,KAAKgF,OAASP,EAAQO,OACtBhF,KAAKqF,UAAYZ,EAAQY,UACzBrF,KAAK+F,QAAUtB,EAAQsB,QACvB/F,KAAK0C,OAAS+B,EAAQ/B,OACtB1C,KAAKmF,KAAOV,EAAQU,KACpBnF,KAAKwE,QAAUC,EAAQD,QAEvBxE,KAAKiH,aAAe,KAQzB,CACCtF,IAAK,gBACLN,MAAO,WACCrB,KAAKmF,KACLnF,KAAKkH,aACElH,KAAK0C,QACZ1C,KAAKmH,iBASd,CACCxF,IAAK,aACLN,MAAO,WACH,IAAIqD,EAAQ1E,KAERoH,EAAwD,OAAhD9B,SAAS+B,gBAAgBT,aAAa,OAElD5G,KAAKsH,aAELtH,KAAKuH,oBAAsB,WACvB,OAAO7C,EAAM4C,cAEjBtH,KAAKwH,YAAcxH,KAAKqF,UAAUoC,iBAAiB,QAASzH,KAAKuH,uBAAwB,EAEzFvH,KAAK0H,SAAWpC,SAASqC,cAAc,YAEvC3H,KAAK0H,SAASE,MAAMC,SAAW,OAE/B7H,KAAK0H,SAASE,MAAME,OAAS,IAC7B9H,KAAK0H,SAASE,MAAMG,QAAU,IAC9B/H,KAAK0H,SAASE,MAAMI,OAAS,IAE7BhI,KAAK0H,SAASE,MAAMK,SAAW,WAC/BjI,KAAK0H,SAASE,MAAMR,EAAQ,QAAU,QAAU,UAEhD,IAAIc,EAAYC,OAAOC,aAAe9C,SAAS+B,gBAAgBgB,UAC/DrI,KAAK0H,SAASE,MAAMU,IAAMJ,EAAY,KAEtClI,KAAK0H,SAASa,aAAa,WAAY,IACvCvI,KAAK0H,SAASrG,MAAQrB,KAAKmF,KAE3BnF,KAAKqF,UAAUmD,YAAYxI,KAAK0H,UAEhC1H,KAAKiH,cAAe,EAAIH,EAASpD,SAAS1D,KAAK0H,UAC/C1H,KAAKyI,aAQV,CACC9G,IAAK,aACLN,MAAO,WACCrB,KAAKwH,cACLxH,KAAKqF,UAAUqD,oBAAoB,QAAS1I,KAAKuH,qBACjDvH,KAAKwH,YAAc,KACnBxH,KAAKuH,oBAAsB,MAG3BvH,KAAK0H,WACL1H,KAAKqF,UAAUsD,YAAY3I,KAAK0H,UAChC1H,KAAK0H,SAAW,QAQzB,CACC/F,IAAK,eACLN,MAAO,WACHrB,KAAKiH,cAAe,EAAIH,EAASpD,SAAS1D,KAAK0C,QAC/C1C,KAAKyI,aAOV,CACC9G,IAAK,WACLN,MAAO,WACH,IAAIuH,OAAY,EAEhB,IACIA,EAAYtD,SAASuD,YAAY7I,KAAKgF,QACxC,MAAO8D,GACLF,GAAY,EAGhB5I,KAAK+I,aAAaH,KAQvB,CACCjH,IAAK,eACLN,MAAO,SAAsBuH,GACzB5I,KAAK+F,QAAQiD,KAAKJ,EAAY,UAAY,QAAS,CAC/C5D,OAAQhF,KAAKgF,OACbG,KAAMnF,KAAKiH,aACXzC,QAASxE,KAAKwE,QACdyE,eAAgBjJ,KAAKiJ,eAAerH,KAAK5B,UAQlD,CACC2B,IAAK,iBACLN,MAAO,WACCrB,KAAKwE,SACLxE,KAAKwE,QAAQ0E,QAGjBf,OAAOgB,eAAeC,oBAQ3B,CACCzH,IAAK,UAMLN,MAAO,WACHrB,KAAKsH,eAEV,CACC3F,IAAK,SACL0H,IAAK,WACD,IAAIrE,EAASF,UAAUlC,OAAS,QAAsBmC,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,OAIjF,GAFA9E,KAAKsJ,QAAUtE,EAEM,SAAjBhF,KAAKsJ,SAAuC,QAAjBtJ,KAAKsJ,QAChC,MAAM,IAAIC,MAAM,uDASxBtI,IAAK,WACD,OAAOjB,KAAKsJ,UASjB,CACC3H,IAAK,SACL0H,IAAK,SAAa3G,GACd,QAAeqC,IAAXrC,EAAsB,CACtB,IAAIA,GAA8E,YAAjD,qBAAXA,EAAyB,YAAcN,EAAQM,KAA6C,IAApBA,EAAO8G,SAWjG,MAAM,IAAID,MAAM,+CAVhB,GAAoB,SAAhBvJ,KAAKgF,QAAqBtC,EAAOiE,aAAa,YAC9C,MAAM,IAAI4C,MAAM,qFAGpB,GAAoB,QAAhBvJ,KAAKgF,SAAqBtC,EAAOiE,aAAa,aAAejE,EAAOiE,aAAa,aACjF,MAAM,IAAI4C,MAAM,0GAGpBvJ,KAAKyJ,QAAU/G,IAY3BzB,IAAK,WACD,OAAOjB,KAAKyJ,YAIb1C,EAhPW,GAmPtBjH,EAAOC,QAAUgH,GAIX,SAAUjH,EAAQC,GAExB,SAAS2J,EAAOjD,GACZ,IAAIQ,EAEJ,GAAyB,WAArBR,EAAQkD,SACRlD,EAAQyC,QAERjC,EAAeR,EAAQpF,WAEtB,GAAyB,UAArBoF,EAAQkD,UAA6C,aAArBlD,EAAQkD,SAAyB,CACtE,IAAIC,EAAanD,EAAQE,aAAa,YAEjCiD,GACDnD,EAAQ8B,aAAa,WAAY,IAGrC9B,EAAQiD,SACRjD,EAAQoD,kBAAkB,EAAGpD,EAAQpF,MAAMuB,QAEtCgH,GACDnD,EAAQqD,gBAAgB,YAG5B7C,EAAeR,EAAQpF,UAEtB,CACGoF,EAAQE,aAAa,oBACrBF,EAAQyC,QAGZ,IAAIa,EAAY5B,OAAOgB,eACnBa,EAAQ1E,SAAS2E,cAErBD,EAAME,mBAAmBzD,GACzBsD,EAAUX,kBACVW,EAAUI,SAASH,GAEnB/C,EAAe8C,EAAUK,WAG7B,OAAOnD,EAGXnH,EAAOC,QAAU2J,GAKX,SAAU5J,EAAQC,GAExB,SAASsK,KAKTA,EAAErI,UAAY,CACZsI,GAAI,SAAU3J,EAAM4J,EAAUC,GAC5B,IAAI9E,EAAI1F,KAAK0F,IAAM1F,KAAK0F,EAAI,IAO5B,OALCA,EAAE/E,KAAU+E,EAAE/E,GAAQ,KAAK8J,KAAK,CAC/BC,GAAIH,EACJC,IAAKA,IAGAxK,MAGT2K,KAAM,SAAUhK,EAAM4J,EAAUC,GAC9B,IAAIzG,EAAO/D,KACX,SAASyF,IACP1B,EAAK6G,IAAIjK,EAAM8E,GACf8E,EAASM,MAAML,EAAK1F,WAItB,OADAW,EAASqF,EAAIP,EACNvK,KAAKsK,GAAG3J,EAAM8E,EAAU+E,IAGjCxB,KAAM,SAAUrI,GACd,IAAIoK,EAAO,GAAGC,MAAMzK,KAAKuE,UAAW,GAChCmG,IAAWjL,KAAK0F,IAAM1F,KAAK0F,EAAI,KAAK/E,IAAS,IAAIqK,QACjD3K,EAAI,EACJ6K,EAAMD,EAAOrI,OAEjB,IAAKvC,EAAGA,EAAI6K,EAAK7K,IACf4K,EAAO5K,GAAGqK,GAAGG,MAAMI,EAAO5K,GAAGmK,IAAKO,GAGpC,OAAO/K,MAGT4K,IAAK,SAAUjK,EAAM4J,GACnB,IAAI7E,EAAI1F,KAAK0F,IAAM1F,KAAK0F,EAAI,IACxByF,EAAOzF,EAAE/E,GACTyK,EAAa,GAEjB,GAAID,GAAQZ,EACV,IAAK,IAAIlK,EAAI,EAAG6K,EAAMC,EAAKvI,OAAQvC,EAAI6K,EAAK7K,IACtC8K,EAAK9K,GAAGqK,KAAOH,GAAYY,EAAK9K,GAAGqK,GAAGI,IAAMP,GAC9Ca,EAAWX,KAAKU,EAAK9K,IAY3B,OAJC+K,EAAiB,OACd1F,EAAE/E,GAAQyK,SACH1F,EAAE/E,GAENX,OAIXF,EAAOC,QAAUsK,GAKX,SAAUvK,EAAQC,EAASI,GAEjC,IAAIkL,EAAKlL,EAAoB,GACzBmL,EAAWnL,EAAoB,GAWnC,SAASoL,EAAO7I,EAAQ8I,EAAMjB,GAC1B,IAAK7H,IAAW8I,IAASjB,EACrB,MAAM,IAAIhB,MAAM,8BAGpB,IAAK8B,EAAGI,OAAOD,GACX,MAAM,IAAI3H,UAAU,oCAGxB,IAAKwH,EAAGX,GAAGH,GACP,MAAM,IAAI1G,UAAU,qCAGxB,GAAIwH,EAAGK,KAAKhJ,GACR,OAAOiJ,EAAWjJ,EAAQ8I,EAAMjB,GAE/B,GAAIc,EAAGO,SAASlJ,GACjB,OAAOmJ,EAAenJ,EAAQ8I,EAAMjB,GAEnC,GAAIc,EAAGI,OAAO/I,GACf,OAAOoJ,EAAepJ,EAAQ8I,EAAMjB,GAGpC,MAAM,IAAI1G,UAAU,6EAa5B,SAAS8H,EAAWD,EAAMF,EAAMjB,GAG5B,OAFAmB,EAAKjE,iBAAiB+D,EAAMjB,GAErB,CACHpE,QAAS,WACLuF,EAAKhD,oBAAoB8C,EAAMjB,KAc3C,SAASsB,EAAeD,EAAUJ,EAAMjB,GAKpC,OAJAwB,MAAM/J,UAAUuE,QAAQhG,KAAKqL,GAAU,SAASF,GAC5CA,EAAKjE,iBAAiB+D,EAAMjB,MAGzB,CACHpE,QAAS,WACL4F,MAAM/J,UAAUuE,QAAQhG,KAAKqL,GAAU,SAASF,GAC5CA,EAAKhD,oBAAoB8C,EAAMjB,QAe/C,SAASuB,EAAe7F,EAAUuF,EAAMjB,GACpC,OAAOe,EAAShG,SAASC,KAAMU,EAAUuF,EAAMjB,GAGnDzK,EAAOC,QAAUwL,GAKX,SAAUzL,EAAQC,GAQxBA,EAAQ2L,KAAO,SAASrK,GACpB,YAAiB0D,IAAV1D,GACAA,aAAiB2K,aACE,IAAnB3K,EAAMmI,UASjBzJ,EAAQ6L,SAAW,SAASvK,GACxB,IAAImK,EAAO1K,OAAOkB,UAAUoI,SAAS7J,KAAKc,GAE1C,YAAiB0D,IAAV1D,IACU,sBAATmK,GAAyC,4BAATA,IAChC,WAAYnK,IACK,IAAjBA,EAAMuB,QAAgB7C,EAAQ2L,KAAKrK,EAAM,MASrDtB,EAAQ0L,OAAS,SAASpK,GACtB,MAAwB,kBAAVA,GACPA,aAAiB4K,QAS5BlM,EAAQ2K,GAAK,SAASrJ,GAClB,IAAImK,EAAO1K,OAAOkB,UAAUoI,SAAS7J,KAAKc,GAE1C,MAAgB,sBAATmK,IAML,SAAU1L,EAAQC,EAASI,GAEjC,IAAI+L,EAAU/L,EAAoB,GAYlC,SAASgM,EAAU1F,EAASR,EAAUuF,EAAMjB,EAAU6B,GAClD,IAAIC,EAAa5G,EAASoF,MAAM7K,KAAM8E,WAItC,OAFA2B,EAAQgB,iBAAiB+D,EAAMa,EAAYD,GAEpC,CACHjG,QAAS,WACLM,EAAQiC,oBAAoB8C,EAAMa,EAAYD,KAe1D,SAASd,EAASgB,EAAUrG,EAAUuF,EAAMjB,EAAU6B,GAElD,MAAyC,oBAA9BE,EAAS7E,iBACT0E,EAAUtB,MAAM,KAAM/F,WAIb,oBAAT0G,EAGAW,EAAUvK,KAAK,KAAM0D,UAAUuF,MAAM,KAAM/F,YAI9B,kBAAbwH,IACPA,EAAWhH,SAASiH,iBAAiBD,IAIlCP,MAAM/J,UAAUwK,IAAIjM,KAAK+L,GAAU,SAAU7F,GAChD,OAAO0F,EAAU1F,EAASR,EAAUuF,EAAMjB,EAAU6B,OAa5D,SAAS3G,EAASgB,EAASR,EAAUuF,EAAMjB,GACvC,OAAO,SAAS7E,GACZA,EAAEE,eAAiBsG,EAAQxG,EAAEhD,OAAQuD,GAEjCP,EAAEE,gBACF2E,EAAShK,KAAKkG,EAASf,IAKnC5F,EAAOC,QAAUuL,GAKX,SAAUxL,EAAQC,GAExB,IAAI0M,EAAqB,EAKzB,GAAuB,qBAAZC,UAA4BA,QAAQ1K,UAAU2K,QAAS,CAC9D,IAAIC,EAAQF,QAAQ1K,UAEpB4K,EAAMD,QAAUC,EAAMC,iBACND,EAAME,oBACNF,EAAMG,mBACNH,EAAMI,kBACNJ,EAAMK,sBAU1B,SAASf,EAASzF,EAASR,GACvB,MAAOQ,GAAWA,EAAQ+C,WAAaiD,EAAoB,CACvD,GAA+B,oBAApBhG,EAAQkG,SACflG,EAAQkG,QAAQ1G,GAClB,OAAOQ,EAETA,EAAUA,EAAQyG,YAI1BpN,EAAOC,QAAUmM,S,yCC58BjB,IAAIiB,EAAS,WAAa,IAAIC,EAAIpN,KAASqN,EAAGD,EAAIE,eAAmBC,EAAGH,EAAII,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACL,EAAIM,GAAIN,EAAY,UAAE,SAASO,GAAM,OAAOJ,EAAG,MAAM,CAAC5L,IAAIgM,EAAKC,GAAGH,YAAY,uCAAuC,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMF,EAAKG,YAAYC,WAAWR,EAAG,MAAM,CAACE,YAAY,6BAA6B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACL,EAAIY,GAAG,IAAIZ,EAAIa,GAAGN,EAAKG,YAAYI,YAAY,OAAOX,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACH,EAAIY,GAAG,IAAIZ,EAAIa,GAAGN,EAAKQ,eAAeZ,EAAG,MAAM,CAACH,EAAIY,GAAG,IAAIZ,EAAIa,GAAGN,EAAKS,sBAAqBb,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,wCAAwC,CAACF,EAAG,MAAM,CAACE,YAAY,uCAAuC,CAACF,EAAG,MAAM,CAACE,YAAY,wBAAwBF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,QAAQ,CAACL,EAAIY,GAAG,WAAWZ,EAAIY,GAAG,IAAIZ,EAAIa,GAAGb,EAAIQ,IAAI,WAAWL,EAAG,MAAM,CAACE,YAAY,6CAA6CI,MAAM,CAAC,sBAAsBT,EAAIQ,KAAK,CAACR,EAAIY,GAAG,cAAcZ,EAAIM,GAAIN,EAAe,aAAE,SAASiB,EAAQC,GAAO,OAAOf,EAAG,MAAM,CAAC5L,IAAI2M,EAAMb,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,WAAWc,MAAgB,IAAVD,EAAc,KAAO,KAAKf,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACgB,MAAgB,IAAVD,EAAc,iBAAmB,IAAI,CAAClB,EAAIY,GAAG,IAAIZ,EAAIa,GAAGI,EAAQG,QAAQ,OAAOjB,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACL,EAAIY,GAAGZ,EAAIa,GAAGI,EAAQI,SAASlB,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACL,EAAIY,GAAGZ,EAAIa,GAAGI,EAAQK,oBAAmB,GAAItB,EAAIuB,SAAWvB,EAAIwB,YAAYhM,OAAQ2K,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACM,MAAM,CAAC,IAAM,EAAQ,aAAqCT,EAAIyB,MAAM,IACnpDC,EAAkB,G,+ECsDtB,cAEA,GACEnO,KAAMoO,EACNhE,KAAM,WACJ,MAAO,CACL6C,GAAI5N,KAAKgP,OAAOC,OAAOrB,GACvBsB,SAAU,GACVC,UAAW,GACXP,YAAa,GACbD,QAAQ,IAGZS,MAAO,CACLJ,OADJ,SACA,GACUnN,EAAElB,OAASoO,GAAQ/O,KAAKgP,OAAOC,OAAOrB,KAAO5N,KAAK4N,KACpD5N,KAAK4N,GAAK5N,KAAKgP,OAAOC,OAAOrB,GAC7B5N,KAAK4O,YAAc,GACnB5O,KAAKqP,gBAIXC,QAAS,WACPtP,KAAKqP,cAEPE,QAAS,CACP,WADJ,4GAEA,QAFA,yCAEA,6BAFA,cAGA,eACA,qCAJA,kCAKA,yBALA,sCAKA,EALA,KAKA,EALA,KAMA,6BACA,GACA,wCACA,gBAEA,kCAEA,sCACA,+CACA,aACA,2BACA,gCAjBA,mDChFkW,I,wBCQ9VC,EAAY,eACd,EACArC,EACA2B,GACA,EACA,KACA,WACA,MAIa,aAAAU,E,8BCnBf1P,EAAOC,QAAU,IAA0B", "file": "h5/js/chunk-2c65df24.8455106d.js", "sourcesContent": ["import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Logistics.vue?vue&type=style&index=0&id=033c6fbc&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Logistics.vue?vue&type=style&index=0&id=033c6fbc&scoped=true&lang=css&\"", "/*!\n * clipboard.js v2.0.4\n * https://zenorocha.github.io/clipboard.js\n * \n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ClipboardJS\"] = factory();\n\telse\n\t\troot[\"ClipboardJS\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _clipboardAction = __webpack_require__(1);\n\nvar _clipboardAction2 = _interopRequireDefault(_clipboardAction);\n\nvar _tinyEmitter = __webpack_require__(3);\n\nvar _tinyEmitter2 = _interopRequireDefault(_tinyEmitter);\n\nvar _goodListener = __webpack_require__(4);\n\nvar _goodListener2 = _interopRequireDefault(_goodListener);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n/**\n * Base class which takes one or more elements, adds event listeners to them,\n * and instantiates a new `ClipboardAction` on each click.\n */\nvar Clipboard = function (_Emitter) {\n    _inherits(Clipboard, _Emitter);\n\n    /**\n     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n     * @param {Object} options\n     */\n    function Clipboard(trigger, options) {\n        _classCallCheck(this, Clipboard);\n\n        var _this = _possibleConstructorReturn(this, (Clipboard.__proto__ || Object.getPrototypeOf(Clipboard)).call(this));\n\n        _this.resolveOptions(options);\n        _this.listenClick(trigger);\n        return _this;\n    }\n\n    /**\n     * Defines if attributes would be resolved using internal setter functions\n     * or custom functions that were passed in the constructor.\n     * @param {Object} options\n     */\n\n\n    _createClass(Clipboard, [{\n        key: 'resolveOptions',\n        value: function resolveOptions() {\n            var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n            this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n            this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n            this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n            this.container = _typeof(options.container) === 'object' ? options.container : document.body;\n        }\n\n        /**\n         * Adds a click event listener to the passed trigger.\n         * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n         */\n\n    }, {\n        key: 'listenClick',\n        value: function listenClick(trigger) {\n            var _this2 = this;\n\n            this.listener = (0, _goodListener2.default)(trigger, 'click', function (e) {\n                return _this2.onClick(e);\n            });\n        }\n\n        /**\n         * Defines a new `ClipboardAction` on each click event.\n         * @param {Event} e\n         */\n\n    }, {\n        key: 'onClick',\n        value: function onClick(e) {\n            var trigger = e.delegateTarget || e.currentTarget;\n\n            if (this.clipboardAction) {\n                this.clipboardAction = null;\n            }\n\n            this.clipboardAction = new _clipboardAction2.default({\n                action: this.action(trigger),\n                target: this.target(trigger),\n                text: this.text(trigger),\n                container: this.container,\n                trigger: trigger,\n                emitter: this\n            });\n        }\n\n        /**\n         * Default `action` lookup function.\n         * @param {Element} trigger\n         */\n\n    }, {\n        key: 'defaultAction',\n        value: function defaultAction(trigger) {\n            return getAttributeValue('action', trigger);\n        }\n\n        /**\n         * Default `target` lookup function.\n         * @param {Element} trigger\n         */\n\n    }, {\n        key: 'defaultTarget',\n        value: function defaultTarget(trigger) {\n            var selector = getAttributeValue('target', trigger);\n\n            if (selector) {\n                return document.querySelector(selector);\n            }\n        }\n\n        /**\n         * Returns the support of the given action, or all actions if no action is\n         * given.\n         * @param {String} [action]\n         */\n\n    }, {\n        key: 'defaultText',\n\n\n        /**\n         * Default `text` lookup function.\n         * @param {Element} trigger\n         */\n        value: function defaultText(trigger) {\n            return getAttributeValue('text', trigger);\n        }\n\n        /**\n         * Destroy lifecycle.\n         */\n\n    }, {\n        key: 'destroy',\n        value: function destroy() {\n            this.listener.destroy();\n\n            if (this.clipboardAction) {\n                this.clipboardAction.destroy();\n                this.clipboardAction = null;\n            }\n        }\n    }], [{\n        key: 'isSupported',\n        value: function isSupported() {\n            var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n\n            var actions = typeof action === 'string' ? [action] : action;\n            var support = !!document.queryCommandSupported;\n\n            actions.forEach(function (action) {\n                support = support && !!document.queryCommandSupported(action);\n            });\n\n            return support;\n        }\n    }]);\n\n    return Clipboard;\n}(_tinyEmitter2.default);\n\n/**\n * Helper function to retrieve attribute value.\n * @param {String} suffix\n * @param {Element} element\n */\n\n\nfunction getAttributeValue(suffix, element) {\n    var attribute = 'data-clipboard-' + suffix;\n\n    if (!element.hasAttribute(attribute)) {\n        return;\n    }\n\n    return element.getAttribute(attribute);\n}\n\nmodule.exports = Clipboard;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _select = __webpack_require__(2);\n\nvar _select2 = _interopRequireDefault(_select);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * Inner class which performs selection from either `text` or `target`\n * properties and then executes copy or cut operations.\n */\nvar ClipboardAction = function () {\n    /**\n     * @param {Object} options\n     */\n    function ClipboardAction(options) {\n        _classCallCheck(this, ClipboardAction);\n\n        this.resolveOptions(options);\n        this.initSelection();\n    }\n\n    /**\n     * Defines base properties passed from constructor.\n     * @param {Object} options\n     */\n\n\n    _createClass(ClipboardAction, [{\n        key: 'resolveOptions',\n        value: function resolveOptions() {\n            var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n            this.action = options.action;\n            this.container = options.container;\n            this.emitter = options.emitter;\n            this.target = options.target;\n            this.text = options.text;\n            this.trigger = options.trigger;\n\n            this.selectedText = '';\n        }\n\n        /**\n         * Decides which selection strategy is going to be applied based\n         * on the existence of `text` and `target` properties.\n         */\n\n    }, {\n        key: 'initSelection',\n        value: function initSelection() {\n            if (this.text) {\n                this.selectFake();\n            } else if (this.target) {\n                this.selectTarget();\n            }\n        }\n\n        /**\n         * Creates a fake textarea element, sets its value from `text` property,\n         * and makes a selection on it.\n         */\n\n    }, {\n        key: 'selectFake',\n        value: function selectFake() {\n            var _this = this;\n\n            var isRTL = document.documentElement.getAttribute('dir') == 'rtl';\n\n            this.removeFake();\n\n            this.fakeHandlerCallback = function () {\n                return _this.removeFake();\n            };\n            this.fakeHandler = this.container.addEventListener('click', this.fakeHandlerCallback) || true;\n\n            this.fakeElem = document.createElement('textarea');\n            // Prevent zooming on iOS\n            this.fakeElem.style.fontSize = '12pt';\n            // Reset box model\n            this.fakeElem.style.border = '0';\n            this.fakeElem.style.padding = '0';\n            this.fakeElem.style.margin = '0';\n            // Move element out of screen horizontally\n            this.fakeElem.style.position = 'absolute';\n            this.fakeElem.style[isRTL ? 'right' : 'left'] = '-9999px';\n            // Move element to the same position vertically\n            var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n            this.fakeElem.style.top = yPosition + 'px';\n\n            this.fakeElem.setAttribute('readonly', '');\n            this.fakeElem.value = this.text;\n\n            this.container.appendChild(this.fakeElem);\n\n            this.selectedText = (0, _select2.default)(this.fakeElem);\n            this.copyText();\n        }\n\n        /**\n         * Only removes the fake element after another click event, that way\n         * a user can hit `Ctrl+C` to copy because selection still exists.\n         */\n\n    }, {\n        key: 'removeFake',\n        value: function removeFake() {\n            if (this.fakeHandler) {\n                this.container.removeEventListener('click', this.fakeHandlerCallback);\n                this.fakeHandler = null;\n                this.fakeHandlerCallback = null;\n            }\n\n            if (this.fakeElem) {\n                this.container.removeChild(this.fakeElem);\n                this.fakeElem = null;\n            }\n        }\n\n        /**\n         * Selects the content from element passed on `target` property.\n         */\n\n    }, {\n        key: 'selectTarget',\n        value: function selectTarget() {\n            this.selectedText = (0, _select2.default)(this.target);\n            this.copyText();\n        }\n\n        /**\n         * Executes the copy operation based on the current selection.\n         */\n\n    }, {\n        key: 'copyText',\n        value: function copyText() {\n            var succeeded = void 0;\n\n            try {\n                succeeded = document.execCommand(this.action);\n            } catch (err) {\n                succeeded = false;\n            }\n\n            this.handleResult(succeeded);\n        }\n\n        /**\n         * Fires an event based on the copy operation result.\n         * @param {Boolean} succeeded\n         */\n\n    }, {\n        key: 'handleResult',\n        value: function handleResult(succeeded) {\n            this.emitter.emit(succeeded ? 'success' : 'error', {\n                action: this.action,\n                text: this.selectedText,\n                trigger: this.trigger,\n                clearSelection: this.clearSelection.bind(this)\n            });\n        }\n\n        /**\n         * Moves focus away from `target` and back to the trigger, removes current selection.\n         */\n\n    }, {\n        key: 'clearSelection',\n        value: function clearSelection() {\n            if (this.trigger) {\n                this.trigger.focus();\n            }\n\n            window.getSelection().removeAllRanges();\n        }\n\n        /**\n         * Sets the `action` to be performed which can be either 'copy' or 'cut'.\n         * @param {String} action\n         */\n\n    }, {\n        key: 'destroy',\n\n\n        /**\n         * Destroy lifecycle.\n         */\n        value: function destroy() {\n            this.removeFake();\n        }\n    }, {\n        key: 'action',\n        set: function set() {\n            var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'copy';\n\n            this._action = action;\n\n            if (this._action !== 'copy' && this._action !== 'cut') {\n                throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n            }\n        }\n\n        /**\n         * Gets the `action` property.\n         * @return {String}\n         */\n        ,\n        get: function get() {\n            return this._action;\n        }\n\n        /**\n         * Sets the `target` property using an element\n         * that will be have its content copied.\n         * @param {Element} target\n         */\n\n    }, {\n        key: 'target',\n        set: function set(target) {\n            if (target !== undefined) {\n                if (target && (typeof target === 'undefined' ? 'undefined' : _typeof(target)) === 'object' && target.nodeType === 1) {\n                    if (this.action === 'copy' && target.hasAttribute('disabled')) {\n                        throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n                    }\n\n                    if (this.action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n                        throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n                    }\n\n                    this._target = target;\n                } else {\n                    throw new Error('Invalid \"target\" value, use a valid Element');\n                }\n            }\n        }\n\n        /**\n         * Gets the `target` property.\n         * @return {String|HTMLElement}\n         */\n        ,\n        get: function get() {\n            return this._target;\n        }\n    }]);\n\n    return ClipboardAction;\n}();\n\nmodule.exports = ClipboardAction;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\nfunction select(element) {\n    var selectedText;\n\n    if (element.nodeName === 'SELECT') {\n        element.focus();\n\n        selectedText = element.value;\n    }\n    else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n        var isReadOnly = element.hasAttribute('readonly');\n\n        if (!isReadOnly) {\n            element.setAttribute('readonly', '');\n        }\n\n        element.select();\n        element.setSelectionRange(0, element.value.length);\n\n        if (!isReadOnly) {\n            element.removeAttribute('readonly');\n        }\n\n        selectedText = element.value;\n    }\n    else {\n        if (element.hasAttribute('contenteditable')) {\n            element.focus();\n        }\n\n        var selection = window.getSelection();\n        var range = document.createRange();\n\n        range.selectNodeContents(element);\n        selection.removeAllRanges();\n        selection.addRange(range);\n\n        selectedText = selection.toString();\n    }\n\n    return selectedText;\n}\n\nmodule.exports = select;\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar is = __webpack_require__(5);\nvar delegate = __webpack_require__(6);\n\n/**\n * Validates all params and calls the right\n * listener function based on its target type.\n *\n * @param {String|HTMLElement|HTMLCollection|NodeList} target\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listen(target, type, callback) {\n    if (!target && !type && !callback) {\n        throw new Error('Missing required arguments');\n    }\n\n    if (!is.string(type)) {\n        throw new TypeError('Second argument must be a String');\n    }\n\n    if (!is.fn(callback)) {\n        throw new TypeError('Third argument must be a Function');\n    }\n\n    if (is.node(target)) {\n        return listenNode(target, type, callback);\n    }\n    else if (is.nodeList(target)) {\n        return listenNodeList(target, type, callback);\n    }\n    else if (is.string(target)) {\n        return listenSelector(target, type, callback);\n    }\n    else {\n        throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n    }\n}\n\n/**\n * Adds an event listener to a HTML element\n * and returns a remove listener function.\n *\n * @param {HTMLElement} node\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNode(node, type, callback) {\n    node.addEventListener(type, callback);\n\n    return {\n        destroy: function() {\n            node.removeEventListener(type, callback);\n        }\n    }\n}\n\n/**\n * Add an event listener to a list of HTML elements\n * and returns a remove listener function.\n *\n * @param {NodeList|HTMLCollection} nodeList\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNodeList(nodeList, type, callback) {\n    Array.prototype.forEach.call(nodeList, function(node) {\n        node.addEventListener(type, callback);\n    });\n\n    return {\n        destroy: function() {\n            Array.prototype.forEach.call(nodeList, function(node) {\n                node.removeEventListener(type, callback);\n            });\n        }\n    }\n}\n\n/**\n * Add an event listener to a selector\n * and returns a remove listener function.\n *\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenSelector(selector, type, callback) {\n    return delegate(document.body, selector, type, callback);\n}\n\nmodule.exports = listen;\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports) {\n\n/**\n * Check if argument is a HTML element.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.node = function(value) {\n    return value !== undefined\n        && value instanceof HTMLElement\n        && value.nodeType === 1;\n};\n\n/**\n * Check if argument is a list of HTML elements.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.nodeList = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return value !== undefined\n        && (type === '[object NodeList]' || type === '[object HTMLCollection]')\n        && ('length' in value)\n        && (value.length === 0 || exports.node(value[0]));\n};\n\n/**\n * Check if argument is a string.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.string = function(value) {\n    return typeof value === 'string'\n        || value instanceof String;\n};\n\n/**\n * Check if argument is a function.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.fn = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return type === '[object Function]';\n};\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar closest = __webpack_require__(7);\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction _delegate(element, selector, type, callback, useCapture) {\n    var listenerFn = listener.apply(this, arguments);\n\n    element.addEventListener(type, listenerFn, useCapture);\n\n    return {\n        destroy: function() {\n            element.removeEventListener(type, listenerFn, useCapture);\n        }\n    }\n}\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element|String|Array} [elements]\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction delegate(elements, selector, type, callback, useCapture) {\n    // Handle the regular Element usage\n    if (typeof elements.addEventListener === 'function') {\n        return _delegate.apply(null, arguments);\n    }\n\n    // Handle Element-less usage, it defaults to global delegation\n    if (typeof type === 'function') {\n        // Use `document` as the first parameter, then apply arguments\n        // This is a short way to .unshift `arguments` without running into deoptimizations\n        return _delegate.bind(null, document).apply(null, arguments);\n    }\n\n    // Handle Selector-based usage\n    if (typeof elements === 'string') {\n        elements = document.querySelectorAll(elements);\n    }\n\n    // Handle Array-like based usage\n    return Array.prototype.map.call(elements, function (element) {\n        return _delegate(element, selector, type, callback, useCapture);\n    });\n}\n\n/**\n * Finds closest match and invokes callback.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Function}\n */\nfunction listener(element, selector, type, callback) {\n    return function(e) {\n        e.delegateTarget = closest(e.target, selector);\n\n        if (e.delegateTarget) {\n            callback.call(element, e);\n        }\n    }\n}\n\nmodule.exports = delegate;\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\nvar DOCUMENT_NODE_TYPE = 9;\n\n/**\n * A polyfill for Element.matches()\n */\nif (typeof Element !== 'undefined' && !Element.prototype.matches) {\n    var proto = Element.prototype;\n\n    proto.matches = proto.matchesSelector ||\n                    proto.mozMatchesSelector ||\n                    proto.msMatchesSelector ||\n                    proto.oMatchesSelector ||\n                    proto.webkitMatchesSelector;\n}\n\n/**\n * Finds the closest parent that matches a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @return {Function}\n */\nfunction closest (element, selector) {\n    while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n        if (typeof element.matches === 'function' &&\n            element.matches(selector)) {\n          return element;\n        }\n        element = element.parentNode;\n    }\n}\n\nmodule.exports = closest;\n\n\n/***/ })\n/******/ ]);\n});", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"logistics\"},[_vm._l((_vm.cartInfo),function(cart){return _c('div',{key:cart.id,staticClass:\"header acea-row row-between row-top\"},[_c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":cart.productInfo.image}})]),_c('div',{staticClass:\"text acea-row row-between\"},[_c('div',{staticClass:\"name line2\"},[_vm._v(\" \"+_vm._s(cart.productInfo.store_name)+\" \")]),_c('div',{staticClass:\"money\"},[_c('div',[_vm._v(\"￥\"+_vm._s(cart.true_price))]),_c('div',[_vm._v(\"x\"+_vm._s(cart.cart_num))])])])])}),_c('div',{staticClass:\"logisticsCon\"},[_c('div',{staticClass:\"company acea-row row-between-wrapper\"},[_c('div',{staticClass:\"picTxt acea-row row-between-wrapper\"},[_c('div',{staticClass:\"iconfont icon-wuliu\"}),_c('div',{staticClass:\"text\"},[_c('div',{staticClass:\"express line1\"},[_c('span',{staticClass:\"name\"},[_vm._v(\"快递单号：\")]),_vm._v(\" \"+_vm._s(_vm.id)+\" \")])])]),_c('div',{staticClass:\"copy acea-row row-center-wrapper copy-data\",attrs:{\"data-clipboard-text\":_vm.id}},[_vm._v(\" 复制单号 \")])]),_vm._l((_vm.expressList),function(express,index){return _c('div',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"circular\",class:index === 0 ? 'on' : ''}),_c('div',{staticClass:\"text\"},[_c('div',{class:index === 0 ? 'font-color-red' : ''},[_vm._v(\" \"+_vm._s(express.status)+\" \")]),_c('div',{staticClass:\"data\"},[_vm._v(_vm._s(express.time))]),_c('div',{staticClass:\"data\"},[_vm._v(_vm._s(express.context))])])])})],2),(_vm.loaded && !_vm.expressList.length)?_c('div',{staticClass:\"no-express\"},[_c('img',{attrs:{\"src\":require(\"@assets/images/noExpress.png\")}})]):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"logistics\">\n    <div\n      class=\"header acea-row row-between row-top\"\n      v-for=\"cart in cartInfo\"\n      :key=\"cart.id\"\n    >\n      <div class=\"pictrue\"><img :src=\"cart.productInfo.image\" /></div>\n      <div class=\"text acea-row row-between\">\n        <div class=\"name line2\">\n          {{ cart.productInfo.store_name }}\n        </div>\n        <div class=\"money\">\n          <div>￥{{ cart.true_price }}</div>\n          <div>x{{ cart.cart_num }}</div>\n        </div>\n      </div>\n    </div>\n    <div class=\"logisticsCon\">\n      <div class=\"company acea-row row-between-wrapper\">\n        <div class=\"picTxt acea-row row-between-wrapper\">\n          <div class=\"iconfont icon-wuliu\"></div>\n          <div class=\"text\">\n            <div class=\"express line1\">\n              <span class=\"name\">快递单号：</span> {{ id }}\n            </div>\n          </div>\n        </div>\n        <div\n          class=\"copy acea-row row-center-wrapper copy-data\"\n          :data-clipboard-text=\"id\"\n        >\n          复制单号\n        </div>\n      </div>\n      <div class=\"item\" v-for=\"(express, index) in expressList\" :key=\"index\">\n        <div class=\"circular\" :class=\"index === 0 ? 'on' : ''\"></div>\n        <div class=\"text\">\n          <div :class=\"index === 0 ? 'font-color-red' : ''\">\n            {{ express.status }}\n          </div>\n          <div class=\"data\">{{ express.time }}</div>\n          <div class=\"data\">{{ express.context }}</div>\n        </div>\n      </div>\n    </div>\n    <div class=\"no-express\" v-if=\"loaded && !expressList.length\">\n      <img src=\"@assets/images/noExpress.png\" />\n    </div>\n  </div>\n</template>\n<script>\nimport ClipboardJS from \"clipboard\";\nimport { express } from \"@api/order\";\n\nconst NAME = \"Logistics\";\n\nexport default {\n  name: NAME,\n  data: function() {\n    return {\n      id: this.$route.params.id,\n      cartInfo: [],\n      orderInfo: {},\n      expressList: [],\n      loaded: false\n    };\n  },\n  watch: {\n    $route(n) {\n      if (n.name === NAME && this.$route.params.id !== this.id) {\n        this.id = this.$route.params.id;\n        this.expressList = [];\n        this.getExpress();\n      }\n    }\n  },\n  mounted: function() {\n    this.getExpress();\n  },\n  methods: {\n     async getExpress() {\n      if (!this.id) return this.$dialog.error(\"订单不存在\");\n      this.loaded = false;\n      this.$dialog.loading.open('正在加载...')\n      const [status, res] = await express(this.id)\n      this.$dialog.loading.close()\n      if (status) {\n        this.expressList = res.result.resultLst || [];\n        this.loaded = true;\n      } else {\n          this.$dialog.error(res.msg || \"加载失败\");\n      }\n      this.$nextTick(function() {\n        var copybtn = document.getElementsByClassName(\"copy-data\");\n        const clipboard = new ClipboardJS(copybtn);\n        clipboard.on(\"success\", () => {\n          this.$dialog.success(\"复制成功\");\n        });\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.no-express {\n  margin: 1.5rem 0;\n}\n\n.no-express img {\n  width: 6rem;\n  margin: 0 auto;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Logistics.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Logistics.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Logistics.vue?vue&type=template&id=033c6fbc&scoped=true&\"\nimport script from \"./Logistics.vue?vue&type=script&lang=js&\"\nexport * from \"./Logistics.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Logistics.vue?vue&type=style&index=0&id=033c6fbc&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"033c6fbc\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"h5/img/noExpress.375a0428.png\";"], "sourceRoot": ""}