package com.web.controller;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.chevron.o2oorder.Constant.HttpClientUtils;
import com.chevron.o2oorder.Constant.WexConstants;
import com.common.util.ContextUtil;

@Component
public class WexinController {
	 private static final Log logger = LogFactory.getLog(WexinController.class);
	
	 @Scheduled(fixedRate= 1000*60*90, initialDelay = 2000)//项目启动2秒中之后执行一次，然后每90min执行一次，单位都为ms
	 public void getAccessToken() {
		 //获取AccessToken
		 String accessToken = HttpClientUtils.getToken();
		 //将accessToek放在redis中缓存
		 ContextUtil.setO2OAccessToken(WexConstants.ACCESS_TOKEN, accessToken);
		 //获取TicketToken
		 String ticketToken = null;
		 logger.info("accessToken:"+accessToken);
		try {
			ticketToken = HttpClientUtils.getJsApiTicket(accessToken);
			System.out.print(accessToken);
			logger.info("ticketTon:"+ticketToken);
			 //将ticketToken放在redis中缓存
			 ContextUtil.setO2OAccessToken(WexConstants.TICKET_TOKEN, ticketToken);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("ticketTon:"+ticketToken+"/"+"accessToken:"+accessToken);
		}
	 }

}
