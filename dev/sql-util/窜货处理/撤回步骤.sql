--清除已执行待办列表
select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --delete wiel1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='13' and wsi1.step_no>3
--重置撤回到步骤待办列表	
select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --update wiel1 set wiel1.execute_status=10
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id and wiel1.execute_status=30
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='13' and wsi1.step_no=3
--删除历史
select wsh1.*,wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --delete wsh1
		  from wx_t_workflow_step_instance wsi1
		  left join wx_t_workflow_step_history wsh1 on wsh1.step_instance_id=wsi1.step_instance_id
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='NON_LOCK_MKT' and wi1.form_key='13' and wsi1.step_no=3		 
--流程实例撤回到指定步骤
select * 
--update wi set next_step_no=3,status=10 --4, 20
from wx_t_workflow_instance wi where wi.workflow_code='NON_LOCK_MKT' and wi.form_key='13'		
--表单撤回到指定步骤
select top 1000 * 
--update f set f.form_status=12
from wx_t_non_local_mkt_flow_form f where id=13;
		 