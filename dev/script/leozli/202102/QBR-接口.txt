1. 统计费用支持
/budget2021/sumDistributorActualExpenseByQuarter.do?distributorIds=57&year=2021
参数： 
	year：年份【必填】
	distributorIds: 经销商ID【必填】 多个以逗号分隔
"data" : [ {
    "distributorId" : null,
    "regionName" : null,
    "salesCai" : null,
    "salesName" : null,
    "supervisorCai" : null,
    "supervisorName" : null,
    "customerType" : null,
    "year" : null,
    "quarter" : 1, //季度
    "month" : null,
    "productChannel" : null,
    "expenseCode" : "CDM_Dealer_Support",
    "onlineAmount" : 100.0,
    "sparkAmount" : null,
    "totalAmount" : 100.0, //费用值
    "sparkBudget" : null,
    "sparkActual" : null,
    "flsrBudget" : null,
    "flsrActual" : null,
    "regionBudget" : null,
    "regionActual" : null,
    "budgetNote" : null,
    "mktAmount" : 0.0,
    "projectAmount" : 0.0,
    "mktBudget" : null,
    "mktActual" : null,
    "projectBudget" : null,
    "projectActual" : null,
    "brand" : null,
    "customerName" : null,
    "budget2021ExpenseType" : {
      "id" : 10,
      "createUserId" : 1,
      "createTime" : null,
      "updateUserId" : null,
      "updateTime" : null,
      "brand" : 1,
      "expenseMainType" : "大区费用",
      "expenseMainCode" : 1, //1-大区费用，2-市场费用，4-项目费用
      "expenseType" : "金富力新经销商/分销商支持",
      "expenseCode" : "CDM_Dealer_Support",//费用标题
      "ifWorkflow" : 0,
      "ifImport" : 1,
      "deleteFlag" : 0,
      "groupName" : null,
      "actualAmount" : 0.0
    }
  }...]	