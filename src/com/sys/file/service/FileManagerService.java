package com.sys.file.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.common.util.ContextUtil;
import com.common.util.CustomUUIDGenerator;
import com.common.util.FileUtil;
import com.common.util.StringUtils;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.model.WxAttFile;
import com.sys.file.model.WxAttFileExample;
import com.sys.file.web.FileManager;

@Service
public class FileManagerService implements FileManagerServiceI {

	@Resource
	private WxAttFileMapper wxAttFileMapper;

	@Override
	public Map<String,Object> updateAttFileSourceID(String attId, String sourceId,
			String sourceType) throws Exception {
		Map<String,Object> resMap = new HashMap<String,Object>();
		Long curUserID = ContextUtil.getCurUserId();
		WxAttFile wxAttFile = wxAttFileMapper.selectByPrimaryKey(Long.valueOf(attId));
		if (wxAttFile == null) {
			resMap.put("code", "cannot find attachment file by id:" + attId);
		} else {
			wxAttFile.setSourceId(Long.valueOf(sourceId));
			wxAttFile.setSourceType(sourceType);
			wxAttFile.setAttStatus(1);
			wxAttFile.setXgSj(new Date());
			wxAttFile.setXgUser(curUserID);
			wxAttFileMapper.updateByPrimaryKey(wxAttFile);
			resMap.put("code", "success");
			resMap.put("attachmentFile", wxAttFile);
		}
		return resMap;
	}

	/**
	 * 供后台调用获取附件
	 *
	 * @param attId
	 * @param sourceType
	 * @throws IOException
	 */
	public InputStream downloadAttachmentFile(String attId, String sourceType)
			throws IOException {
		WxAttFile att = searchAttByKey(attId);
		String fileSep = System.getProperty("file.separator");
		String realPath = "";
		/*if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(sourceType))) {
			realPath = FileManager.fileUploadPath + att.getStorePath();
		} else {
			realPath = FileManager.remoteFileUploadPath + att.getStorePath();
		}*/
		//
		realPath = FileManager.getFileRealPath(sourceType, att.getStorePath());
		//end
		String storageName = "", filePath = "", fileName = "";
		storageName = att.getStorageName();
		fileName = att.getFileName();
		filePath = realPath + fileSep + storageName;
		return FileUtil.download(sourceType, filePath, fileName);
	}

/**
 * 新增附件
 * @param sourceId
 * @param fileName
 * @param storageName
 * @param fileType
 * @param sourceType
 * @param storePath
 * @param fileSize
 * @param uploadUser
 * @param tenantId
 * @return
 */
	public WxAttFile addAttFile(Long sourceId, String fileName,
			String storageName, String fileType, String sourceType,
			String storePath, Long fileSize, Long uploadUser, String tenantId) {

		// 新增处理信息
		Date nowDate = new Date();
		WxAttFile attFile = new WxAttFile();
		attFile.setSourceId(sourceId);
		attFile.setFileName(fileName);
		attFile.setStorageName(storageName);
		attFile.setFileType(fileType);
		attFile.setSourceType(sourceType);
		attFile.setStorePath(storePath);
		attFile.setCreateTime(nowDate);
		attFile.setFileSize(fileSize);
		attFile.setUploadUser(Long.valueOf(uploadUser));
		if (sourceId == null) {
			attFile.setAttStatus(0);
		} else {
			attFile.setAttStatus(1);
		}
		attFile.setZt("1");
		attFile.setXgSj(nowDate);
		attFile.setXgUser(Long.valueOf(uploadUser));
		attFile.setTenantId(Long.valueOf(tenantId));
		attFile.setUuid(CustomUUIDGenerator.generateUUID());
		wxAttFileMapper.insertSelective(attFile);
		return attFile;
	}

	public WxAttFile updateAttFileByattId(WxAttFile wxAttFile, String fileName,
			String storageName, String fileType, String storePath,
			Long fileSize, Long curUserID) throws Exception {
		if (wxAttFile != null) {
			wxAttFile.setFileName(fileName);
			wxAttFile.setStorageName(storageName);
			wxAttFile.setFileType(fileType);
			wxAttFile.setStorePath(storePath);
			wxAttFile.setFileSize(fileSize);
			wxAttFile.setXgSj(new Date());
			wxAttFile.setXgUser(curUserID);
			wxAttFileMapper.updateByPrimaryKey(wxAttFile);
		}
		return wxAttFile;
	}

	public WxAttFile searchAttByKey(String attId) {
		WxAttFile att = wxAttFileMapper.selectByPrimaryKey(Long.valueOf(attId));
		return att;
	}

	public WxAttFile searchAttByUuid(String uuid) {
		WxAttFileExample example = new WxAttFileExample();
		example.createCriteria().andUuidEqualTo(uuid);
		List<WxAttFile> attFileList = wxAttFileMapper.selectByExample(example);
		if (attFileList != null && attFileList.size() > 0) {
			return attFileList.get(0);
		}
		return null;
	}

	@Override
	public Map<String,String> deleteAttFile(String attId) throws Exception {
		Map<String,String> resMap = new HashMap<String,String>();
		WxAttFile wxAttFile = wxAttFileMapper.selectByPrimaryKey(Long.valueOf(attId));
		if (wxAttFile == null) {
			resMap.put("code", "cannot find attachment file by id:" + attId);
			resMap.put("codeMsg", "无此附件:" + attId);
		} else {
			FileManager.deleteAttFile(wxAttFile, wxAttFileMapper);
			resMap.put("code", "success");
			resMap.put("codeMsg", "删除附件成功");
		}
		return resMap;
	}

	public WxAttFile searchAttBySourceIdAndSourceType(String sourceId,
			String sourceType) {
		WxAttFileExample wxAttFileExample = new WxAttFileExample();
		WxAttFileExample.Criteria criteria = wxAttFileExample.createCriteria();
		criteria.andSourceIdEqualTo(Long.parseLong(sourceId));
		criteria.andSourceTypeEqualTo(sourceType);
		List<WxAttFile> attList = wxAttFileMapper
				.selectByExample(wxAttFileExample);
		if (attList != null && attList.size() == 1) {
			return attList.get(0);
		}
		return null;
	}

	public WxAttFile copyFile(WxAttFile oldFile, Long newSourceId,
			String newSourceType) throws Exception {
		WxAttFile newFile = null;
		String fileSep = System.getProperty("file.separator");
		String realPath = "";
		if (FileManager.LOCAL.equals(FileManager.sourceTypeMap.get(oldFile
				.getSourceType()))) {
			realPath = FileManager.fileUploadPath + oldFile.getStorePath();
		} else {
			realPath = FileManager.remoteFileUploadPath
					+ oldFile.getStorePath();
		}
		String storageName = "", filePath = "", fileName = "";
		storageName = oldFile.getStorageName();
		fileName = oldFile.getFileName();
		filePath = realPath + fileSep + storageName;
		InputStream oldFileIns = FileUtil.download(oldFile.getSourceType(),
				filePath, fileName);
		String newStorageName = StringUtils.getSeqNextval();
		String fileSuffix = StringUtils.getFileSuffixName(fileName);
		newStorageName = newStorageName + "." + fileSuffix;
		//存储新文件
		//add by bo.liu 1116 start
		String fileType = oldFile.getFileType();
		System.out.println("copyFile fileType:"+fileType);
		if(null==fileType||fileType.equals("null")||fileType.isEmpty())
		{
			fileType = FileManager.defaultfileType;
		}
		//add by bo.liu 1116 end
		FileUtil.storeFile(realPath, newStorageName, oldFileIns, newSourceType,fileType);
		//生成新的文件记录
		newFile = this.addAttFile(newSourceId, fileName, newStorageName,
				fileType, newSourceType, oldFile.getStorePath(),
				oldFile.getFileSize(), oldFile.getUploadUser(),
				String.valueOf(oldFile.getTenantId()));
		return newFile;
	}
	/**
	 * 复制文件列表，文件zt为0的不复制
	 * @throws Exception
	 */
	public List<WxAttFile> copyFileList(List<WxAttFile> oldFileList, Long newSourceId,
			String newSourceType) throws Exception {
		List<WxAttFile> newFileList = new ArrayList<WxAttFile>();
		if(oldFileList!=null && oldFileList.size()>0){
			for(WxAttFile oldFile:oldFileList ){
				if (!WxAttFile.Del_Zt.equals(oldFile.getZt())) {
					WxAttFile newFile=this.copyFile(oldFile, newSourceId, newSourceType);
					newFileList.add(newFile);
				}
			}
		}
		return  newFileList;

	}

	@Override
	public Map<String, Object> findAttsBySource(String sourceId,
			String sourceType) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try{
			Map<String, Object> params = new HashMap<String, Object>(5);
			params.put("sourceType", sourceType);
			params.put("sourceId", Long.parseLong(sourceId));
			map.put("data", wxAttFileMapper.queryByParams(params));
			map.put("success", true);
		}catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public String getStorageFilePath(String sourceId, String sourceType) {
		WxAttFile att = searchAttBySourceIdAndSourceType(sourceId, sourceType);
		String fileSep = System.getProperty("file.separator");
		String realPath = "";
		realPath = FileManager.fileUploadPath + att.getStorePath();
		String storageName = att.getStorageName();
		storageName = att.getStorageName();
		String storageFilePath = realPath + fileSep + storageName;
		return storageFilePath;
	}

	@Override
	public Boolean isSameName(Long sourceId, String sourceType, String fileName) {
		WxAttFileExample wxAttFileExample = new WxAttFileExample();
		wxAttFileExample.createCriteria().andSourceIdEqualTo(sourceId).andSourceTypeEqualTo(sourceType).andFileNameEqualTo(fileName);
		List<WxAttFile> list = wxAttFileMapper.selectByExample(wxAttFileExample);
		if (null != list && !list.isEmpty()) {
			return true;
		}
		return false;
	}

    @Override
    public Map<String, Object> getAttFileByAttrIds(List<Long> attIds) {
        Map<String, Object> map = new HashMap<String, Object>(16);
        try{
            WxAttFileExample wxAttFileExample = new WxAttFileExample();
            wxAttFileExample.createCriteria().andAttIdIn(attIds);
            List<WxAttFile> wxAttFiles = wxAttFileMapper.selectByExample(wxAttFileExample);
            map.put("data", wxAttFiles);
            map.put("success", true);
        }catch (Exception e) {
            e.printStackTrace();
            map.put("success", false);
        }
        return map;
    }
}
